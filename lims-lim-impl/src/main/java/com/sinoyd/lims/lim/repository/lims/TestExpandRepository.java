package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 修约规则
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
public interface TestExpandRepository extends IBaseJpaPhysicalDeleteRepository<DtoTestExpand, String>, LimsRepository<DtoTestExpand, String> {

    /**
     * 通过测试id,样品类型id查找
     *
     * @param testId       测试项目id
     * @param sampleTypeId 检测类型id
     * @return 返回测试扩展
     */
    DtoTestExpand findByTestIdAndSampleTypeId(String testId, String sampleTypeId);

    /**
     * 通过测试项目id,样品类型id删除
     *
     * @param testId       测试项目id
     * @param sampleTypeId 检测类型id
     * @return
     */
    @Transactional
    @Modifying
    @Query("delete from DtoTestExpand p where p.testId = :testId and p.sampleTypeId = :sampleTypeId")
    Integer delete(@Param("testId") String testId, @Param("sampleTypeId") String sampleTypeId);

    /**
     * @param testIds testIds       测试项目ids
     * @return 返回测试扩展
     */
    List<DtoTestExpand> findByTestIdIn(List<String> testIds);

    /**
     * @param testId 测试项目id
     * @return 返回相应的测试扩展
     */
    List<DtoTestExpand> findByTestId(String testId);

    /**
     * @param testId 测试项目id
     * @return 返回相应的测试扩展
     */
    @Query("select distinct p.sampleTypeId from DtoTestExpand p where p.testId = :testId")
    List<String> findSampleTypeIdByTestId(@Param("testId") String testId);

    /**
     * 通过测试项目id删除
     *
     * @param testIds       测试项目id集合
     * @return 删除的条数
     */
    @Transactional
    @Modifying
    Integer deleteByTestIdIn(Collection<String> testIds);

    /**
     * 根据样品类型Ids和测试项目Ids获取测试项目扩展
     * @param testIds 测试项目ids
     * @param sampleTypeId 样品类型id
     * @return 测试项目扩展集合
     */
    List<DtoTestExpand> findByTestIdInAndSampleTypeId(Collection<String> testIds,String sampleTypeId);
}