package com.sinoyd.lims.lim.service;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/05/29
 */
public interface DownLoadQcLimitTemplateService {

    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    void downLoadTemplate(HttpServletResponse response, Map<String, String> sheetNames, String fileName);
}
