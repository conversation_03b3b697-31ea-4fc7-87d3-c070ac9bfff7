package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoBusinessSerialNumber;
import com.sinoyd.lims.pro.enums.EnumPRO;

import java.util.Collection;

/**
 * 业务流水号业务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/2/29
 */
public interface BusinessSerialNumberService extends IBaseJpaService<DtoBusinessSerialNumber, String> {


    /**
     * 清除业务编号
     *
     * @param em          业务类型枚举
     * @param businessIds 业务id集合
     * @param redisKey    待清除的redis key
     */
    void clearBusinessSerialNumber(EnumPRO.EnumLogObjectType em, Collection<String> businessIds, String redisKey);


}
