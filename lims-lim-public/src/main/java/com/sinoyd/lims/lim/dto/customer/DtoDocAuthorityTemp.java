package com.sinoyd.lims.lim.dto.customer;


import com.sinoyd.frame.dto.DtoRole;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthority;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 文档权限实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@Data
public class DtoDocAuthorityTemp {


    /**
     * 角色的相关信息
     */
    private DtoRole role;


    /**
     * 角色相关的权限
     */
    private List<DtoDocAuthority> authList = new ArrayList<>();

}
