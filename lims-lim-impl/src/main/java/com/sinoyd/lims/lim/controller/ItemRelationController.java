package com.sinoyd.lims.lim.controller;

import com.sinoyd.lims.lim.dto.rcc.DtoItemRelation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.ItemRelationService;
import com.sinoyd.lims.lim.criteria.ItemRelationCriteria;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ItemRelation服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
 @Api(tags = "示例: ItemRelation服务")
 @RestController
 @RequestMapping("api/lim/itemRelation")
 @Validated
 public class ItemRelationController extends BaseJpaController<DtoItemRelation, String,ItemRelationService> {


    /**
     * 分页动态条件查询ItemRelation
     *
     * @param itemRelationCriteria 条件参数
     * @return RestResponse<List < ItemRelation>>
     */
    @ApiOperation(value = "分页动态条件查询ItemRelation", notes = "分页动态条件查询ItemRelation")
    @GetMapping
    public RestResponse<List<DtoItemRelation>> findByPage(ItemRelationCriteria itemRelationCriteria) {
        PageBean<DtoItemRelation> pageBean = super.getPageBean();
        RestResponse<List<DtoItemRelation>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, itemRelationCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ItemRelation
     *
     * @param id 主键id
     * @return RestResponse<DtoItemRelation>
     */
    @ApiOperation(value = "按主键查询ItemRelation", notes = "按主键查询ItemRelation")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoItemRelation> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoItemRelation> restResponse = new RestResponse<>();
        DtoItemRelation itemRelation = service.findOne(id);
        restResponse.setData(itemRelation);
        restResponse.setRestStatus(StringUtil.isNull(itemRelation) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增ItemRelation
     *
     * @param itemRelation 实体列表
     * @return RestResponse<DtoItemRelation>
     */
    @ApiOperation(value = "新增ItemRelation", notes = "新增ItemRelation")
    @PostMapping
    public RestResponse<DtoItemRelation> create(@Validated @RequestBody DtoItemRelation itemRelation) {
        RestResponse<DtoItemRelation> restResponse = new RestResponse<>();
        restResponse.setData(service.save(itemRelation));
        return restResponse;
    }

    /**
     * 新增ItemRelation
     *
     * @param itemRelation 实体列表
     * @return RestResponse<DtoItemRelation>
     */
    @ApiOperation(value = "修改ItemRelation", notes = "修改ItemRelation")
    @PutMapping
    public RestResponse<DtoItemRelation> update(@Validated @RequestBody DtoItemRelation itemRelation) {
        RestResponse<DtoItemRelation> restResponse = new RestResponse<>();
        restResponse.setData(service.update(itemRelation));
        return restResponse;
    }

    /**
     * "根据id批量删除ItemRelation
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ItemRelation", notes = "根据id批量删除ItemRelation")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}