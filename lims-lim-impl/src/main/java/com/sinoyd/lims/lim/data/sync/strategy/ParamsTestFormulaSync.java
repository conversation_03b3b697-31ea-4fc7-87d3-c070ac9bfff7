package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.ParamsTestFormulaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 测试项目公式参数同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/25
 */
@Component
@DependsOn({"springContextAware"})
@Order(13)
@Slf4j
public class ParamsTestFormulaSync extends AbsDataSync<DtoParamsTestFormula> {

    private ParamsTestFormulaService paramsTestFormulaService;

    private TestFormulaSync testFormulaSync;

    /**
     * 数据比较
     *
     * @param testIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoParamsTestFormula>> compareData(List<String> testIds) {
        //获取项目上全部公式参数
        List<DtoParamsTestFormula> projectDataList = paramsTestFormulaService.findAll();
        //公共库中的全部公式参数
        List<DtoParamsTestFormula> standardTestParamList = queryStandardData();
        //获取需要同步的测试项目相关公式
        List<DtoParamsFormula> standardTestFormulaList = testFormulaSync.queryStandardData();
        if (StringUtil.isNotEmpty(standardTestFormulaList) && StringUtil.isNotEmpty(testIds)) {
            standardTestFormulaList = standardTestFormulaList.parallelStream()
                    .filter(p -> EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue().equals(p.getObjectType()) &&
                            testIds.contains(p.getObjectId())).collect(Collectors.toList());
            List<String> formulaIds = standardTestFormulaList.parallelStream().map(DtoParamsFormula::getId).collect(Collectors.toList());
            standardTestParamList = standardTestParamList.parallelStream().filter(p -> formulaIds.contains(p.getObjId()))
                    .collect(Collectors.toList());
        }
        //比较数据
        return compareData(standardTestParamList, projectDataList);
    }

    /**
     * 同步数据
     *
     * @param testIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> testIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoParamsTestFormula>> compareResult = compareData(testIds);
        Optional<DtoDataCompareResult<DtoParamsTestFormula>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoParamsTestFormula errorDto = null;
            try {
                for (DtoParamsTestFormula dtoParamsTestFormula : r.getAddDataList()) {
                    errorDto = dtoParamsTestFormula;
                    paramsTestFormulaService.save(dtoParamsTestFormula);
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.测试项目公式参数.getValue();
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.测试项目公式参数.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.测试项目公式参数.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/paramsTestFormula";
    }

    @Autowired
    @Lazy
    public void setParamsTestFormulaService(ParamsTestFormulaService paramsTestFormulaService) {
        this.paramsTestFormulaService = paramsTestFormulaService;
    }

    @Autowired
    @Lazy
    public void setTestFormulaSync(TestFormulaSync testFormulaSync) {
        this.testFormulaSync = testFormulaSync;
    }
}