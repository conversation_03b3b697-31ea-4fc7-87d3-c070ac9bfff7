package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * ReceiveSampleRecord数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface ReceiveSampleRecordRepository extends IBaseJpaRepository<DtoReceiveSampleRecord, String> {

    /**
     * 查询对应项目id下的送样单
     *
     * @param projectId 项目id
     * @return 对应的送样单
     */
    List<DtoReceiveSampleRecord> findByProjectId(String projectId);

    /**
     * 排除信息状态根据项目id查询
     *
     * @param infoStatus 信息状态
     * @param projectId  项目id
     * @return 送样单列表
     */
    List<DtoReceiveSampleRecord> findByInfoStatusNotAndProjectId(Integer infoStatus, String projectId);

    /**
     * 查询对应项目id集合下的送样单
     *
     * @param projectIds 项目id集合
     * @return 对应的送样单
     */
    List<DtoReceiveSampleRecord> findByProjectIdIn(List<String> projectIds);

    /**
     * 根据状态查询
     *
     * @param statusList 项目类型
     * @return 送样单
     */
    List<DtoReceiveSampleRecord> findByStatusIn(List<String> statusList);

    /**
     * 批量办结送样单状态
     *
     * @param ids           送样单的ids
     * @param receiveStatus 送样单状态
     * @param status        送样单状态
     * @param infoStatus    信息登记状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSampleRecord r set r.receiveStatus = :receiveStatus,r.status = :status,r.infoStatus = :infoStatus," +
            "r.modifyDate = :modifyDate,r.modifier = :modifier  where r.id in :ids")
    Integer finishRecord(@Param("ids") List<String> ids,
                         @Param("receiveStatus") Integer receiveStatus,
                         @Param("status") String status,
                         @Param("infoStatus") Integer infoStatus,
                         @Param("modifier") String modifier,
                         @Param("modifyDate") Date modifyDate);

    /**
     * 数据录入提交送样单状态
     *
     * @param ids        送样单的ids
     * @param infoStatus 信息登记状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSampleRecord r set r.infoStatus = :infoStatus,r.uploadStatus = :uploadStatus,r.checkerId = :checkerId," +
            "r.modifyDate = :modifyDate,r.modifier = :modifier  where r.id in :ids")
    Integer submitRecordInfo(@Param("ids") List<String> ids,
                             @Param("infoStatus") Integer infoStatus,
                             @Param("uploadStatus") Integer uploadStatus,
                             @Param("checkerId") String checkerId,
                             @Param("modifier") String modifier,
                             @Param("modifyDate") Date modifyDate);

    /**
     * 提交的时候修改送样单状态
     *
     * @param id         主键id
     * @param infoStatus 信息登记状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回状态
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSampleRecord r set r.infoStatus = :infoStatus," +
            "r.modifyDate = :modifyDate,r.modifier = :modifier  where r.id = :id")
    Integer submitRecordInfo(@Param("id") String id,
                             @Param("infoStatus") Integer infoStatus,
                             @Param("modifier") String modifier,
                             @Param("modifyDate") Date modifyDate);

    /**
     * 复核通过送样单
     *
     * @param ids        送样单的ids
     * @param infoStatus 信息登记状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSampleRecord r set r.infoStatus = :infoStatus,r.uploadStatus = :uploadStatus," +
            "r.checkerId = :checkerId,r.checkTime = :checkTime,r.modifyDate = :modifyDate,r.modifier = :modifier  where r.id in :ids")
    Integer checkPassRecordInfo(@Param("ids") List<String> ids,
                                @Param("infoStatus") Integer infoStatus,
                                @Param("uploadStatus") Integer uploadStatus,
                                @Param("checkerId") String checkerId,
                                @Param("checkTime") Date checkTime,
                                @Param("modifier") String modifier,
                                @Param("modifyDate") Date modifyDate);

    /**
     * 审核通过送样单
     *
     * @param ids        送样单的ids
     * @param infoStatus 信息登记状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSampleRecord r set r.infoStatus = :infoStatus,r.uploadStatus = :uploadStatus,r.auditorId = :auditorId," +
            "r.auditTime = :auditTime,r.modifyDate = :modifyDate,r.modifier = :modifier  where r.id in :ids")
    Integer auditPassRecordInfo(@Param("ids") List<String> ids,
                                @Param("infoStatus") Integer infoStatus,
                                @Param("uploadStatus") Integer uploadStatus,
                                @Param("auditorId") String auditorId,
                                @Param("auditTime") Date auditTime,
                                @Param("modifier") String modifier,
                                @Param("modifyDate") Date modifyDate);

    /**
     * 现场退回送样单
     *
     * @param ids        送样单的ids
     * @param infoStatus 信息登记状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSampleRecord r set r.infoStatus = :infoStatus,r.uploadStatus = :uploadStatus," +
            "r.modifyDate = :modifyDate,r.modifier = :modifier where r.id in :ids")
    Integer refuseRecordInfo(@Param("ids") List<String> ids,
                             @Param("infoStatus") Integer infoStatus,
                             @Param("uploadStatus") Integer uploadStatus,
                             @Param("modifier") String modifier,
                             @Param("modifyDate") Date modifyDate);

    /**
     * 修改送样单的冗余json信息
     *
     * @param id   送样单的id
     * @param json json信息
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSampleRecord r set r.json = :json where r.id = :id")
    Integer updateJson(@Param("id") String id, @Param("json") String json);

    /**
     * 样品交接修改送样单信息
     *
     * @param id            送样单的id
     * @param receiveTime   接样时间
     * @param status        送样单状态
     * @param receiveStatus 送样单状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSampleRecord r set r.receiveTime = :receiveTime,r.status = :status,r.receiveStatus = :receiveStatus," +
            "r.modifyDate = :modifyDate,r.modifier = :modifier,r.receiveSampleDate =:receiveSampleDate where r.id = :id")
    Integer sampleReceive(@Param("id") String id, @Param("receiveTime") Date receiveTime,
                          @Param("status") String status,
                          @Param("receiveStatus") Integer receiveStatus,
                          @Param("modifier") String modifier,
                          @Param("modifyDate") Date modifyDate,
                          @Param("receiveSampleDate") Date receiveSampleDate);

    /**
     * 样品交接修改送样单信息
     *
     * @param id            送样单的id
     * @param receiveTime   接样时间
     * @param status        送样单状态
     * @param receiveStatus 送样单状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSampleRecord r set r.receiveTime = :receiveTime,r.status = :status,r.receiveStatus = :receiveStatus," +
            "r.modifyDate = :modifyDate,r.modifier = :modifier,r.receiveSampleDate =:receiveSampleDate,r.recipientId =:recipientId," +
            "r.remark =:remark where r.id = :id")
    Integer sampleReceive(@Param("id") String id, @Param("receiveTime") Date receiveTime,
                          @Param("status") String status,
                          @Param("receiveStatus") Integer receiveStatus,
                          @Param("modifier") String modifier,
                          @Param("modifyDate") Date modifyDate,
                          @Param("receiveSampleDate") Date receiveSampleDate,
                          @Param("recipientId") String recipientId,
                          @Param("remark") String remark);
}