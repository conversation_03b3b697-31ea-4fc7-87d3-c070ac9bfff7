package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.util.List;

/**
 * DtoSamplingCertHistory实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since  2024/11/25
 */
@Data
public class DtoSamplingCertHistory {
    /**
     * 样品类型
     */
    private String sampleType;

    /**
     * 采样人员
     */
    private String samplingPeople;

    /**
     * 项目
     */
    private String item;

    /**
     * 方法
     */
    private String method;

    /**
     * 标准编号
     */
    private String standardNo;

    /**
     * 上岗证编号
     */
    private String certCode;

    /**
     * 上岗证有效期
     */
    private String certEffectiveTime;

    /**
     * 持证状态
     */
    private String status;

    /**
     * 关联历史附件标识集合
     */
    private List<String> historyFileIds;
}
