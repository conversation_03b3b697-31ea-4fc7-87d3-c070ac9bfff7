package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 频次（携带指标）
 * <AUTHOR>
 * @version V1.0.0 2020/1/6
 * @since V100R001
 */
@Data
public class DtoSamplingFrequencyWithItem {
    public DtoSamplingFrequencyWithItem() {

    }

    public DtoSamplingFrequencyWithItem(DtoSamplingFrequency frequency) {
        this.samplingFrequencyId = frequency.getId();
        this.sampleFolderId = frequency.getSampleFolderId();
        this.periodCount = frequency.getPeriodCount();
        this.timePerPeriod = frequency.getTimePerPeriod();
        this.samplePerTime = frequency.getSamplePerTime();
        this.redFolderName = frequency.getRedFolderName();
    }

    public DtoSamplingFrequencyWithItem(DtoSamplingFrequency frequency, List<DtoSamplingFrequencyTest> sftList) {
        this.samplingFrequencyId = frequency.getId();
        this.sampleFolderId = frequency.getSampleFolderId();
        this.periodCount = frequency.getPeriodCount();
        this.timePerPeriod = frequency.getTimePerPeriod();
        this.samplePerTime = frequency.getSamplePerTime();
        this.redFolderName = frequency.getRedFolderName();
        this.analyzeItemIds = sftList.stream().map(DtoSamplingFrequencyTest::getAnalyseItemId).distinct().collect(Collectors.toList());
        this.testIds = sftList.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
    }

    public void setTests(List<DtoTest> testList) {
        this.analyzeItemIds = testList.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
        this.testIds = testList.stream().map(DtoTest::getId).distinct().collect(Collectors.toList());
    }


    /**
     * 频次id
     */
    private String samplingFrequencyId;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 周期
     */
    private Integer periodCount;

    /**
     * 次数
     */
    private Integer timePerPeriod;

    /**
     * 每次样品数
     */
    private Integer samplePerTime;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 分析项目id集合
     */
    private List<String> analyzeItemIds = new ArrayList<>();

    /**
     * 测试项目id集合
     */
    private List<String> testIds = new ArrayList<>();
}
