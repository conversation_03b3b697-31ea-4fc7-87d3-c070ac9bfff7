package com.sinoyd.lims.pro.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OrderStatisticsVo extends BaseOrderCustomerAnalyzeVO {

    /**
     * 销售人员图片路径
     */
    private String photoUrl;

    /**
     * 销售人员姓名
     */
    private String saleName;

    /**
     * 本年度签单数
     */
    private Integer orderNum;

    /**
     * 签单总额
     */
    private Integer signBillMoneyTotal;

    /**
     * 签单总额同比上年度
     */
    private Integer signBillMoneyCompare;

    /**
     * 当月新增签单额
     */
    private Integer monthSignBillMoney;

    /**
     * 当月新增签单个数
     */
    private Integer monthSignBillCount;

    /**
     * 当月同比上月增长签单额
     */
    private Integer monthSignBillMoneyCompare;

    /**
     * 当月新增报价额
     */
    private Integer monthOrderBillMoney;

    /**
     * 当月新增报价单个数
     */
    private Integer monthOrderBillCount;

    /**
     * 当月同比上月增长订单额
     */
    private Integer monthOrderBillMoneyCompare;
}
