package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.lim.dto.customer.DtoExportTest;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.transform.ImportCommonCheckImpl;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;

import java.util.Arrays;
import java.util.List;

/**
 * 测试项目导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TransformTestVerifyHandler extends ImportCommonCheckImpl implements IExcelVerifyHandler<DtoExportTest> {
    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    private List<DtoTest> repoDataList;

    private List<DtoExportTest> sheetExistDataList;

    @SneakyThrows
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExportTest exportTest) {
        //导入数据处理,跳过空行,数据去除前后空格
        try {
            if (importUtils.checkObjectIsNull(exportTest)) {
                return new ExcelVerifyHandlerResult(true);
            }
            importUtils.strToTrim(exportTest);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }

        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + (exportTest.getRowNum()+1) + "行数据校验有误");

        //本sheet内数据重复
        checkSeetDataRepeat(result,failStr,sheetExistDataList,exportTest, Arrays.asList("analyzeMethodId","analyzeItemId","sampleTypeId"));
        //与数据库数据重复
        checkRepoDataRepeat(result,failStr,repoDataList,exportTest, Arrays.asList("analyzeMethodId","analyzeItemId","sampleTypeId"));

        //更新重复校验容器
        sheetExistDataList.add(exportTest);

        //构建异常信息
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }
}
