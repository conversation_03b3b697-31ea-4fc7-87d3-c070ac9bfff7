package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.AnalyseBiologyDataService;
import com.sinoyd.lims.pro.criteria.AnalyseBiologyDataCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseBiologyData;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * AnalyseBiologyData服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: AnalyseBiologyData服务")
 @RestController
 @RequestMapping("api/pro/analyseBiologyData")
 public class AnalyseBiologyDataController extends BaseJpaController<DtoAnalyseBiologyData, String,AnalyseBiologyDataService> {


    /**
     * 分页动态条件查询AnalyseBiologyData
     * @param analyseBiologyDataCriteria 条件参数
     * @return RestResponse<List<AnalyseBiologyData>>
     */
     @ApiOperation(value = "分页动态条件查询AnalyseBiologyData", notes = "分页动态条件查询AnalyseBiologyData")
     @GetMapping
     public RestResponse<List<DtoAnalyseBiologyData>> findByPage(AnalyseBiologyDataCriteria analyseBiologyDataCriteria) {
         PageBean<DtoAnalyseBiologyData> pageBean = super.getPageBean();
         RestResponse<List<DtoAnalyseBiologyData>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, analyseBiologyDataCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询AnalyseBiologyData
     * @param id 主键id
     * @return RestResponse<DtoAnalyseBiologyData>
     */
     @ApiOperation(value = "按主键查询AnalyseBiologyData", notes = "按主键查询AnalyseBiologyData")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoAnalyseBiologyData> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoAnalyseBiologyData> restResponse = new RestResponse<>();
         DtoAnalyseBiologyData analyseBiologyData = service.findOne(id);
         restResponse.setData(analyseBiologyData);
         restResponse.setRestStatus(StringUtil.isNull(analyseBiologyData) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增AnalyseBiologyData
     * @param analyseBiologyData 实体列表
     * @return RestResponse<DtoAnalyseBiologyData>
     */
     @ApiOperation(value = "新增AnalyseBiologyData", notes = "新增AnalyseBiologyData")
     @PostMapping
     public RestResponse<DtoAnalyseBiologyData> create(@RequestBody @Validated DtoAnalyseBiologyData analyseBiologyData) {
         RestResponse<DtoAnalyseBiologyData> restResponse = new RestResponse<>();
         restResponse.setData(service.save(analyseBiologyData));
         return restResponse;
      }

     /**
     * 新增AnalyseBiologyData
     * @param analyseBiologyData 实体列表
     * @return RestResponse<DtoAnalyseBiologyData>
     */
     @ApiOperation(value = "修改AnalyseBiologyData", notes = "修改AnalyseBiologyData")
     @PutMapping
     public RestResponse<DtoAnalyseBiologyData> update(@RequestBody @Validated DtoAnalyseBiologyData analyseBiologyData) {
         RestResponse<DtoAnalyseBiologyData> restResponse = new RestResponse<>();
         restResponse.setData(service.update(analyseBiologyData));
         return restResponse;
      }

    /**
     * "根据id批量删除AnalyseBiologyData
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除AnalyseBiologyData", notes = "根据id批量删除AnalyseBiologyData")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }