-- 测试项目公式
DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_ParamsFormula' AND column_name = ('validate'))) THEN
        ALTER TABLE TB_LIM_ParamsFormula  ADD COLUMN validate INT ( 10 ) NULL DEFAULT '0' COMMENT '验证状态 0未验证 1已验证';
    END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_ParamsFormula' AND column_name = ('usageNum'))) THEN
        ALTER TABLE TB_LIM_ParamsFormula ADD COLUMN usageNum INT ( 10 ) NULL COMMENT '使用次数';
        END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

-- 质控限值
DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_BASE_QualityControlLimit' AND column_name = ('validate'))) THEN
        ALTER TABLE TB_BASE_QualityControlLimit  ADD COLUMN validate INT ( 10 ) NULL DEFAULT '0' COMMENT '验证状态 0未验证 1已验证';
        END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_BASE_QualityControlLimit' AND column_name = ('usageNum'))) THEN
        ALTER TABLE TB_BASE_QualityControlLimit ADD COLUMN usageNum INT ( 10 ) NULL COMMENT '使用次数';
        END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;