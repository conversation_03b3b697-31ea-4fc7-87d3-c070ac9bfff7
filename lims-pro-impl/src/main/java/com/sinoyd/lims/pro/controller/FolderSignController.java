package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.FolderSignService;
import com.sinoyd.lims.pro.criteria.FolderSignCriteria;
import com.sinoyd.lims.pro.dto.DtoFolderSign;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FolderSign服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/7/7
 * @since V100R001
 */
 @Api(tags = "示例: FolderSign服务")
 @RestController
 @RequestMapping("api/pro/folderSign")
 public class FolderSignController extends BaseJpaController<DtoFolderSign, String,FolderSignService> {


    /**
     * 分页动态条件查询FolderSign
     * @param folderSignCriteria 条件参数
     * @return RestResponse<List<FolderSign>>
     */
     @ApiOperation(value = "分页动态条件查询FolderSign", notes = "分页动态条件查询FolderSign")
     @GetMapping
     public RestResponse<List<DtoFolderSign>> findByPage(FolderSignCriteria folderSignCriteria) {
         PageBean<DtoFolderSign> pageBean = super.getPageBean();
         RestResponse<List<DtoFolderSign>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, folderSignCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FolderSign
     * @param id 主键id
     * @return RestResponse<DtoFolderSign>
     */
     @ApiOperation(value = "按主键查询FolderSign", notes = "按主键查询FolderSign")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFolderSign> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFolderSign> restResponse = new RestResponse<>();
         DtoFolderSign folderSign = service.findOne(id);
         restResponse.setData(folderSign);
         restResponse.setRestStatus(StringUtil.isNull(folderSign) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FolderSign
     * @param folderSign 实体列表
     * @return RestResponse<DtoFolderSign>
     */
     @ApiOperation(value = "新增FolderSign", notes = "新增FolderSign")
     @PostMapping
     public RestResponse<DtoFolderSign> create(@RequestBody @Validated DtoFolderSign folderSign) {
         RestResponse<DtoFolderSign> restResponse = new RestResponse<>();
         restResponse.setData(service.save(folderSign));
         return restResponse;
      }

     /**
     * 新增FolderSign
     * @param folderSign 实体列表
     * @return RestResponse<DtoFolderSign>
     */
     @ApiOperation(value = "修改FolderSign", notes = "修改FolderSign")
     @PutMapping
     public RestResponse<DtoFolderSign> update(@RequestBody @Validated DtoFolderSign folderSign) {
         RestResponse<DtoFolderSign> restResponse = new RestResponse<>();
         restResponse.setData(service.update(folderSign));
         return restResponse;
      }

    /**
     * "根据id批量删除FolderSign
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FolderSign", notes = "根据id批量删除FolderSign")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }