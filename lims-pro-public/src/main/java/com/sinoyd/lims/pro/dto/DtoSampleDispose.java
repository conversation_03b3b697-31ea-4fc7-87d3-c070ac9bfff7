package com.sinoyd.lims.pro.dto;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.pro.entity.SampleDispose;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * DtoSampleDispose实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SampleDispose")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoSampleDispose extends SampleDispose {
    private static final long serialVersionUID = 1L;

    /**
     * 样品id集合
     */
    @Transient
    private List<String> sampleIds;

    public DtoSampleDispose() {
    }

    /**
     * 获取列表信息构造器
     *
     * @param id              主键
     * @param sampleId        关联按样品id
     * @param sampleCode      关联样品编号
     * @param sampleSource    样品来源
     * @param sampleCount     样品数量
     * @param reserveDate     保留日期
     * @param reserveLocation 保留位置
     * @param redAnalyzeItems 分析项目
     * @param disposePersonId 处置人id
     * @param disposeDate     处置日起
     * @param disposeSolution 处置方式
     * @param disposeRemarks  处置备注
     * @param isDisposed      是否处置标识
     * @param samplingTimeEnd 采样时间
     * @param receiveId       样品送样单id
     */
    public DtoSampleDispose(String id, String sampleId, String sampleCode, String sampleSource,
                            Integer sampleCount, Date reserveDate, String reserveLocation,
                            String redAnalyzeItems, String disposePersonId, Date disposeDate,
                            String disposeSolution, String disposeRemarks, Boolean isDisposed,
                            Date samplingTimeEnd, String receiveId) {
        this.setId(id);
        this.setSampleId(sampleId);
        this.setSampleCode(sampleCode);
        this.setSampleSource(sampleSource);
        this.setSampleCount(sampleCount);
        this.setReserveDate(reserveDate);
        this.setReserveLocation(reserveLocation);
        this.setRedAnalyzeItems(redAnalyzeItems);
        this.setDisposePersonId(disposePersonId);
        this.setDisposeDate(disposeDate);
        this.setDisposeSolution(disposeSolution);
        this.setDisposeRemarks(disposeRemarks);
        this.setIsDisposed(isDisposed);
        this.setSamplingTime(DateUtil.dateToString(samplingTimeEnd, DateUtil.YEAR));
        this.setReceiveId(receiveId);
    }

    /**
     * 样品编号
     */
    @Transient
    private String sampleCode;

    /**
     * 样品保存时间前台保存格式化字符串 yyyy-MM-dd
     */
    @Transient
    private String reserveDateString;

    /**
     * 处置时间前台保存格式化字符串 yyyy-MM-dd
     */
    @Transient
    private String disposeDateString;

    /**
     * 采样时间
     */
    @Transient
    private String samplingTime;

    /**
     * 接样时间
     */
    @Transient
    private String receivingTime;

    /**
     * 采样单id
     */
    @Transient
    private String receiveId;

    /**
     * 留样处置绑定测试项目id
     */
    @Transient
    private List<String> testIds;

    /**
     * 留样处置关联的绑定信息数据，用来给前端编辑操作时获取绑定的测试项目id和名称
     */
    @Transient
    private List<DtoSampleDispose2Test> sampleDispose2Tests;

    /**
     * 留样处置样品关联的所有测试项目
     */
    @Transient
    private Map<String,String> tests;

    /**
     * 样品小类
     */
    @Transient
    private String sampleTypeId;

    /**
     * 样品大类
     */
    @Transient
    private String bigSampleTypeId;

}