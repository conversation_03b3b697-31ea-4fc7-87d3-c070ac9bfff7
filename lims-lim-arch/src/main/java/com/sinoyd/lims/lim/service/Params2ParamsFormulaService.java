package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoParams2ParamsFormula;
import com.sinoyd.lims.lim.dto.customer.DtoParams2ParamsFormulaTemp;
import com.sinoyd.lims.lim.dto.customer.DtoPersonalDataParams;
import com.sinoyd.lims.lim.dto.customer.DtoPersonalHeaderParams;

import java.util.List;
import java.util.Map;


/**
 * 记录单参数-参数配置公式操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
public interface Params2ParamsFormulaService extends IBaseJpaService<DtoParams2ParamsFormula, String> {

    /**
     * 保存个性化的数据参数
     *
     * @param personalDataParams 个性数据参数
     */
    void savePersonalDataParams(DtoPersonalDataParams personalDataParams);


    /**
     * 保存表头的个性化数据参数
     *
     * @param dtoPersonalHeaderParams 表头参数
     */
    void savePersonalHeaderParams(DtoPersonalHeaderParams dtoPersonalHeaderParams);

    /**
     * 获取个性化的数据
     *
     * @param recordConfigId   记录单id
     * @param objectId         对象id
     * @param paramsConfigId   参数id
     * @param paramsConfigType 类型
     * @return 返回数据
     */
    DtoParams2ParamsFormulaTemp findPersonalParams(String recordConfigId,
                                                   String objectId,
                                                   String paramsConfigId,
                                                   Integer paramsConfigType);

    /**
     * 复制数据参数
     *
     * @param dtoPersonalDataParams 复制数据参数
     * @param formulaIds            公式ids
     */
    void copyPersonalDataParams(DtoPersonalDataParams dtoPersonalDataParams, List<String> formulaIds);


    /**
     * 复制表头参数
     *
     * @param dtoPersonalHeaderParams 复制表头参数
     * @param formulaIds              公式ids
     */
    void copyPersonalHeaderParams(DtoPersonalHeaderParams dtoPersonalHeaderParams, List<String> formulaIds);

    /**
     * 数据参数关联公式参数调整,量纲,有效位数,小数位数调整后同步数据
     *
     * @param map 参数map
     */
    void updateParams2ParamsFormulaAndParamsConfig(Map<String, Object> map);

    /**
     * 批量设置公式参数配置
     *
     * @param map 参数map
     */
    void batchUpdateParams2ParamsFormula(Map<String, Object> map);
}