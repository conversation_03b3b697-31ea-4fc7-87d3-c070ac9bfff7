package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 环境管理-环境信息
 * <AUTHOR> 修改：xuxb
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnvironmentalRecordCriteria extends BaseCriteria {

    /**
     * 检索开始时间
     */
    private String dtBegin;

    /**
     * 检索结束时间
     */
    private String dtEnd;

    /**
     * 关联id
     */
    private String objectId;

    /**
     * 关联id
     */
    private List<String> ids = new ArrayList<>();

    /**
     * 关联类型
     */
    private Integer objectType = EnumLIM.EnumEnvRecObjType.所有.getValue();

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and startTime >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and endTime < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        if (StringUtil.isNotEmpty(this.objectId) && !UUIDHelper.GUID_EMPTY.equals(this.objectId)) {
            condition.append(" and objectId = :objectId");
            values.put("objectId", this.objectId);
        }

        if (!objectType.equals(EnumLIM.EnumEnvRecObjType.所有.getValue())) {
            condition.append(" and p.objectType = :objectType");
            values.put("objectType", this.objectType);
        }
        if (StringUtil.isNotNull(ids) && ids.size() > 0) {
            condition.append(" and id in :ids");
            values.put("ids", this.ids);
        }
        return condition.toString();
    }
}