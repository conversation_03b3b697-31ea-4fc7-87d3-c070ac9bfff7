package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.Examine;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 考核管理实体
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/09/14
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_Examine")
@Where(clause = "isDeleted = 0 ")
@Data
@DynamicInsert
public class DtoExamine extends Examine {

    private static final long serialVersionUID = 1L;

    @Transient
    private List<DtoExamineType> typeList;

    @Transient
    private String fullChargePeopleName;


}
