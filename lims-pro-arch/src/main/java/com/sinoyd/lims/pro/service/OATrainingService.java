package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;

import java.util.List;

/**
 * 培训审批业务操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
public interface OATrainingService {

    /**
     * 添加审批并启动流程
     *
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<List<DtoTraining>> taskDto);


    /**
     * 查询任务详细信息
     *
     * @param taskId 任务ID
     * @return
     */
    DtoOATaskDetail<List<DtoTraining>, String> findOATaskDetail(String taskId);

    /**
     * 草稿提交
     * @param taskDto  表单数据
     * @return         oa任务
     */
    String draftSubmit(DtoOATaskCreate<List<DtoTraining>> taskDto);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask saveAsDraft(DtoOATaskCreate<List<DtoTraining>> taskDto);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask draftSave(DtoOATaskCreate<List<DtoTraining>> taskDto);
}
