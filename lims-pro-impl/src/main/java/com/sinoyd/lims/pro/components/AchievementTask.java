package com.sinoyd.lims.pro.components;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.lims.pro.dto.DtoAnalyseAchievement2Person;
import com.sinoyd.lims.pro.dto.DtoOrderContractAchievement2Person;
import com.sinoyd.lims.pro.dto.DtoReportAchievement2Person;
import com.sinoyd.lims.pro.dto.DtoSamplingAchievement2Person;
import com.sinoyd.lims.pro.service.AnalyseAchievement2PersonService;
import com.sinoyd.lims.pro.service.OrderContractAchievement2PersonService;
import com.sinoyd.lims.pro.service.ReportAchievement2PersonService;
import com.sinoyd.lims.pro.service.SamplingAchievement2PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 绩效统计定时任务
 */
@Component
@EnableScheduling
public class AchievementTask {

    private OrderContractAchievement2PersonService orderContractAchievement2PersonService;

    private SamplingAchievement2PersonService samplingAchievement2PersonService;

    private AnalyseAchievement2PersonService analyseAchievement2PersonService;

    private ReportAchievement2PersonService reportAchievement2PersonService;

    /**
     * 每天23.55分执行合同绩效更新数据
     */
    @Scheduled(cron ="0 55 23 * * ?")
    public void doContractTask() {
        List<String> ids = orderContractAchievement2PersonService.findAll().stream().map(DtoOrderContractAchievement2Person::getId).collect(Collectors.toList());
        Integer year = CalendarUtil.getYear(new Date());
        orderContractAchievement2PersonService.updateData(year, ids);
        System.out.println("hello");
    }

    /**
     * 每天23.55分执行采样绩效更新数据
     */
    @Scheduled(cron ="0 55 23 * * ?")
    public void doSamplingTask() {
        List<String> ids = samplingAchievement2PersonService.findAll().stream().map(DtoSamplingAchievement2Person::getId).collect(Collectors.toList());
        Integer year = CalendarUtil.getYear(new Date());
        samplingAchievement2PersonService.updateData(year, ids);
    }

    /**
     * 每天23.55分执行分析绩效更新数据
     */
    @Scheduled(cron ="0 55 23 * * ?")
    public void doAnalyseTask() {
        List<String> ids = analyseAchievement2PersonService.findAll().stream().map(DtoAnalyseAchievement2Person::getId).collect(Collectors.toList());
        Integer year = CalendarUtil.getYear(new Date());
        analyseAchievement2PersonService.updateData(year, ids);
    }

    /**
     * 每天23.55分执行报告绩效更新数据
     */
    @Scheduled(cron ="0 55 23 * * ?")
    public void doReportTask() {
        List<String> ids = reportAchievement2PersonService.findAll().stream().map(DtoReportAchievement2Person::getId).collect(Collectors.toList());
        Integer year = CalendarUtil.getYear(new Date());
        reportAchievement2PersonService.updateData(year, ids);
    }

    @Autowired
    @Lazy
    public void setOrderContractAchievement2PersonService(OrderContractAchievement2PersonService orderContractAchievement2PersonService) {
        this.orderContractAchievement2PersonService = orderContractAchievement2PersonService;
    }

    @Autowired
    @Lazy
    public void setSamplingAchievement2PersonService(SamplingAchievement2PersonService samplingAchievement2PersonService) {
        this.samplingAchievement2PersonService = samplingAchievement2PersonService;
    }

    @Autowired
    @Lazy
    public void setAnalyseAchievement2PersonService(AnalyseAchievement2PersonService analyseAchievement2PersonService) {
        this.analyseAchievement2PersonService = analyseAchievement2PersonService;
    }

    @Autowired
    @Lazy
    public void setReportAchievement2PersonService(ReportAchievement2PersonService reportAchievement2PersonService) {
        this.reportAchievement2PersonService = reportAchievement2PersonService;
    }
}
