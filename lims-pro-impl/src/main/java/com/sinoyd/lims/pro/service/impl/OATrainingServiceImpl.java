package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.lim.dto.lims.DtoTraining2Participants;
import com.sinoyd.lims.lim.repository.lims.Training2ParticipantsRepository;
import com.sinoyd.lims.lim.repository.lims.TrainingRepository;
import com.sinoyd.lims.lim.service.TrainingService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;
import com.sinoyd.lims.pro.service.OATrainingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OATrainingServiceImpl implements OATrainingService {

    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    @Autowired
    @Lazy
    private TrainingService trainingService;

    @Autowired
    @Lazy
    private TrainingRepository trainingRepository;

    @Autowired
    @Lazy
    private Training2ParticipantsRepository training2ParticipantsRepository;


    private CodeService codeService;


    @Transactional
    @Override
    public String startProcess(DtoOATaskCreate<List<DtoTraining>> taskDto) {

        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumPRO.EnumOATaskType.培训审批, taskDto, vars);

        List<DtoTraining> data = taskDto.getData();
        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        for (DtoTraining training : data) {
            // 是否审批为是
            training.setAuditInd(true);
            DtoOATaskRelation taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(training.getId());
            taskRelations.add(taskRelation);
        }
        // 添加培训记录
        trainingService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<List<DtoTraining>, String> findOATaskDetail(String taskId) {
        // 查询当前审批流程
        DtoOATask task = oaTaskService.findByTaskId(taskId);
        DtoOATaskDetail<List<DtoTraining>, String> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));
        // 获取当前审批流下的关联的所有培训任务id
        List<DtoOATaskRelation> relations = oaTaskRelationService.findListByTaskId(taskId);
        List<String> trainingIds = relations.stream().map(DtoOATaskRelation::getObjectId).collect(Collectors.toList());
        // 根据id 查询所有培训
        List<DtoTraining> trainingList = trainingRepository.findByidIn(trainingIds);
        // 根据所有培训id 查询所有关联的参与人集合，根据培训id进行分组。
        Map<String, List<DtoTraining2Participants>> participantsMap = training2ParticipantsRepository.findByTrainingIdIn(trainingIds)
                .stream()
                .collect(Collectors.groupingBy(DtoTraining2Participants::getTrainingId));
        // 获取培训方式常量
        List<DtoCode> codeList = codeService.findCodes(LimCodeHelper.TRAINING);
        // 培训方式常量转换成map，方便根据code 插入name
        Map<String, String> map = codeList.stream().collect(Collectors.toMap(DtoCode::getDictCode, DtoCode::getDictName));

        // 循环培训集合 根据id添加对应的参与人。
        for (DtoTraining training : trainingList) {
            List<DtoTraining2Participants> participants = participantsMap.get(training.getId());
            training.setParticipantIds(StringUtil.isNotEmpty(participants) ? participants.stream().map(DtoTraining2Participants::getParticipantsId).collect(Collectors.toList()) : new ArrayList<String>());
            training.setWayName(map.get(training.getWay()));
        }
        // 培训信息
        taskDetail.setDetail(trainingList);
        return taskDetail;
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<List<DtoTraining>> taskDto) {
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }

    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<List<DtoTraining>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumPRO.EnumOATaskType.培训审批, taskDto, vars);
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<List<DtoTraining>> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<List<DtoTraining>> taskDto,DtoOATask oaTask){
        List<DtoTraining> data = taskDto.getData();
        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        for (DtoTraining training : data) {
            // 是否审批为是
            training.setAuditInd(true);
            DtoOATaskRelation taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(training.getId());
            taskRelations.add(taskRelation);
        }
        // 添加培训记录
        trainingService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }
}
