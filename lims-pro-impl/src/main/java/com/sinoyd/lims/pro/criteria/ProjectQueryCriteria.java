package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * 项目查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/08
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectQueryCriteria extends BaseCriteria implements Serializable {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目类型
     */
    private String projectTypeId;

    /**
     * 关键字
     */
    private String key;

    /**
     * 模块编码
     */
    private String module;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.id = pl.projectId");
//        condition.append(" and p.id = s.projectId");
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and p.inceptTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and p.inceptTime < :endTime");
            values.put("endTime", to);
        }
        if (StringUtil.isNotEmpty(this.projectTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.projectTypeId)) {
            condition.append(" and p.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (p.projectCode like :key or p.projectName like :key or p.customerName like :key) ");
            values.put("key", "%" + key + "%");
        }
        condition.append(" and exists (select projectId from DtoStatusForProject where module = 'report' or module = 'localTask')");
        return condition.toString();
    }
}
