package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * TestExpand实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "TestExpand")
@Data
public class TestExpand implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public TestExpand() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 测试Id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("测试Id（Guid）")
    private String testId;

    /**
     * 样品类型Id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("样品类型Id（Guid）")
    private String sampleTypeId;

    /**
     * 有效位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("有效位数")
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("小数位数")
    private Integer mostDecimal;

    /**
     * 检出限
     */
    @Column(length = 50)
    @ApiModelProperty("检出限")
    @Length(message = "检出限{validation.message.length}", max = 50)
    private String examLimitValue;

    /**
     * 单位（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("单位（Guid）")
    private String dimensionId;

    /**
     * 小于检出限（预留）
     */
    @Column(length = 50)
    @ApiModelProperty("小于检出限（预留）")
    @Length(message = "小于检出限{validation.message.length}", max = 50)
    private String examLimitValueLess;

    /**
     * 样品数量
     */
    @ColumnDefault("1")
    @ApiModelProperty("批次")
    private Integer timesOrder;

    /**
     * 样品数量
     */
    @ColumnDefault("1")
    @ApiModelProperty("样品数量")
    private Integer samplePeriod;

    /**
     * 测定下限
     */
    @Column(length = 50)
    @ApiModelProperty("测定下限")
    @Length(message = "测定下限{validation.message.length}", max = 50)
    private String lowerLimit;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 报告浓度计算方式(EnumComputeMode)
     */
    @ApiModelProperty("报告浓度计算方式(EnumComputeMode)")
    private Integer potencyComputeMode;

}