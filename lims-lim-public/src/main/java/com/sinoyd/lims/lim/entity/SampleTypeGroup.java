package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * SampleTypeGroup实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "SampleTypeGroup")
@Data
public class SampleTypeGroup extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public SampleTypeGroup() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 父节点（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("父节点（Guid）")
    private String parentId;

    /**
     * 分组类型（枚举EnumGroupType：1.分组规则，2.分组）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("分组类型（枚举EnumGroupType：1.分组规则，2.分组）")
    private Integer groupType;

    /**
     * 分组名称
     */
    @Column(length = 50)
    @ApiModelProperty("分组名称")
    @Length(message = "分组名称{validation.message.length}", max = 50)
    private String groupName;

    /**
     * 样品编号标识
     */
    @Column(length = 50)
    @ApiModelProperty("样品编号标识")
    private String sampleCodeTag;

    /**
     * 样品类型的id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("样品类型的id（Guid）")
    private String sampleTypeId;

    /**
     * 固定剂
     */
    @Column(length = 1000)
    @ApiModelProperty("固定剂")
    @Length(message = "固定剂{validation.message.length}", max = 1000)
    private String fixer;

    /**
     * 容器名称
     */
    @ApiModelProperty("容器名称")
    @Length(message = "容器名称{validation.message.length}", max = 255)
    private String containerName;

    /**
     * 保存条件
     */
    @Column(length = 1000)
    @ApiModelProperty("保存条件")
    @Length(message = "保存条件{validation.message.length}", max = 1000)
    private String saveCondition;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 体积类型
     */
    @Column
    @ApiModelProperty("体积类型")
    @Length(message = "体积类型{validation.message.length}", max = 100)
    private String volumeType;

    /**
     * 前处理方式
     */
    @Column
    @ApiModelProperty("前处理方式")
    @Length(message = "前处理方式{validation.message.length}", max = 200)
    private String pretreatmentMethod;

    /**
     * 采样体积
     */
    @Column
    @ApiModelProperty("采样体积")
    @Length(message = "采样体积{validation.message.length}", max = 200)
    private String sampleVolume;

    /**
     * 采样容器状态（枚举Enum：1.分组规则，2.分组）
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("采样容器状态 EnumContainerStatus 1.完好无损 2.破损")
    private Integer containerStatus;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}