package com.sinoyd.lims.probase.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoStatusForRecord;

import java.util.List;

public interface StatusForRecordBaseRepository extends IBaseJpaPhysicalDeleteRepository<DtoStatusForRecord, String> {
    /**
     * 查询指定送样单的状态信息
     *
     * @param receiveId 送样单id
     * @return 对应送样单下的状态信息
     */
    List<DtoStatusForRecord> findByReceiveId(String receiveId);

    /**
     * 根据送样单id删除状态信息
     * @param receiveId 送样单id
     * @return 删除的个数
     */
    Integer deleteByReceiveId(String receiveId);
}
