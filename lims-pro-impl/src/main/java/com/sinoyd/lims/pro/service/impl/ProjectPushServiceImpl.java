package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.SerialIdentifierConfigService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoProjectPut;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.FolderExtendService;
import com.sinoyd.lims.pro.service.ProjectPushService;
import com.sinoyd.lims.pro.service.ProjectService;
import com.sinoyd.lims.pro.util.WebServiceUtil;
import com.sinoyd.lims.pro.util.XmlToObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sun.misc.BASE64Encoder;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 项目推送模模块相关功能接口实现（由上海环境院项目迁移而来）
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/11
 * @since V100R001
 */
@Service
@Slf4j
public class ProjectPushServiceImpl implements ProjectPushService {

    @Value("${shanghaiAPI.url: unknown}")
    private String url;

    @Value("${shanghaiAPI.username: unknown}")
    private String username;

    @Value("${shanghaiAPI.password: unknown}")
    private String password;

    private WebServiceUtil webServiceUtil;

    private OrderContractRepository orderContractRepository;

    private ProjectService projectService;

    private DocumentRepository documentRepository;

    private ReportRepository reportRepository;

    private SerialIdentifierConfigService serialIdentifierConfigService;

    private ProjectContractRepository projectContractRepository;

    private CommonRepository commonRepository;

    private SampleFolderRepository sampleFolderRepository;

    private SamplingFrequencyRepository samplingFrequencyRepository;

    private PersonService personService;

    private PersonRepository personRepository;

    private FilePathConfig filePathConfig;

    private FolderExtendService folderExtendService;

    private SHSamplingPersonNewRepository shSamplingPersonRepository;

    private SHSamplingInstrumentNewRepository SHSamplingInstrumentNewRepository;

    private InstrumentService instrumentService;

    private AreaService areaService;

    private TestRepository testRepository;

    private InstrumentRepository instrumentRepository;

    @Override
    public List<Map<String, Object>> queryByMethodName(Map<String, String> req, RestResponse response) {
        String methodName = req.get("methodName");
        String key = req.get("key");
        String id = req.getOrDefault("ID","");
        long page = StringUtils.isNotNullAndEmpty(req.get("page")) ? Long.parseLong(req.get("page")) : 1L;
        long rows = StringUtils.isNotNullAndEmpty(req.get("rows")) ? Long.parseLong(req.get("rows")) : 15L;
        String result = webServiceUtil.doRequest(getParams(req), url);
        if (result.contains("<succes>")) {
            String code = result.split("<succes>")[1].split("</succes>")[0];
            if ("False".equals(code)) {
                return new ArrayList<>();
            }
        }
        result = "<Res><Items>" + result.split("<Items xmlns=\"\">")[1].split("</Items>")[0] + "</Items></Res>";
        List<Map<String, Object>> data = XmlToObjectUtil.multilayerXmlToMap(result);
        if (StringUtils.isNotNullAndEmpty(key)) {
            if ("M_MonitoringAnalysisMethod".equals(methodName)) {
                data = data.stream().filter(d -> StringUtil.isNotNull(d.get("METHODNAME")) && d.get("METHODNAME").toString().contains(key)).collect(Collectors.toList());
            } else if ("C_PartyABasicDataList".equals(methodName)) {
                data = data.stream().filter(d -> StringUtil.isNotNull(d.get("QYMC")) && d.get("QYMC").toString().contains(key)).collect(Collectors.toList());
            } else if ("M_MonitoringSamplingMethod".equals(methodName)) {
                data = data.stream().filter(d -> StringUtil.isNotNull(d.get("NAME")) && d.get("NAME").toString().contains(key)).collect(Collectors.toList());
            } else if ("B_GetALLWorkersList".equals(methodName)) {
                data = data.stream().filter(d -> StringUtil.isNotNull(d.get("XM")) && d.get("XM").toString().contains(key)).collect(Collectors.toList());
            } else if ("B_GetALLDevicesList".equals(methodName)) {
                data = data.stream().filter(d -> StringUtil.isNotNull(d.get("SBMC")) && d.get("SBMC").toString().contains(key)).collect(Collectors.toList());
            }
        }
        if(StringUtil.isNotEmpty(id)){
            if ("C_PartyABasicDataList".equals(methodName)) {
                data = data.stream().filter(d -> id.equals(d.get("ID"))).collect(Collectors.toList());
            }
        }
        response.setCount(data.size());
        data = data.stream().skip((page - 1) * rows).limit(rows).collect(Collectors.toList());
        return data;
    }

    @Override
    @Transactional
    public String pushContract(String id) {
        Map<String, String> req = new HashMap<>();
        DtoOrderContract contract = orderContractRepository.findOne(id);
        if (contract.getIsHavingPut()) {
            throw new BaseException("该合同已经推送过");
        }
        List<DtoDocument> documentList = documentRepository.findByFolderIdAndIsDeletedFalse(id);
        String fileName = "";
        if(StringUtil.isEmpty(documentList)){
            throw new BaseException("合同未上传附件");
        }else{
            fileName = pushRemoteFile(documentList.get(0),"1");
        }
        req.put("ID", "0");
        req.put("BT", contract.getContractName());
        req.put("XZ", contract.getContractNature());
        req.put("JFBZ", contract.getShanghaiEntId());
        req.put("JE", contract.getTotalAmount().toString());
        req.put("IS_SUBPACKAGE", contract.getIsHavingSub() ? "1" : "0");
        req.put("SUBPACKAGE_MONEY", StringUtil.isNotNull(contract.getSubAmount()) ? contract.getSubAmount().toString() : "0");
        req.put("SUBPACKAGE_ORG", StringUtil.isNotNull(contract.getSubOrgs()) ? contract.getSubOrgs() : "");
        req.put("ZQS", DateUtil.dateToString(contract.getExcuteStartTime(), DateUtil.YEAR));
        req.put("ZQZ", DateUtil.dateToString(contract.getExcuteEndTime(), DateUtil.YEAR));
        req.put("RWGS", contract.getSummary());
        req.put("FileName", documentList.get(0).getFilename());
        req.put("FilePath", fileName);
        req.put("IS_CHECK", "1");
        req.put("methodName", "C_InsertContractInfo");
        String result = webServiceUtil.doRequest(getParams(req), url);
        String code = result.split("<succes>")[1].split("</succes>")[0];
        String message = result.split("<message>")[1].split("</message>")[0];
        if ("False".equals(code)) {
            throw new BaseException(message);
        }
        contract.setIsHavingPut(true);
        String cId = result.split("<ID>")[1].split("</ID>")[0];
        contract.setCId(cId);
        orderContractRepository.save(contract);
        return cId;
    }



    /**
     * 远程推送文件
     * @param document 原文件
     * @return 远程文件名称
     */
    private String pushRemoteFile(DtoDocument document,String fileType) {
        Map<String, String> req = new HashMap<>();
        req.put("methodName", "CreateFile");
        req.put("fileName", document.getFilename());
        req.put("fileType", "8");
        String result = webServiceUtil.doRequest(getParams(req), url);
        String code = result.split("<succes>")[1].split("</succes>")[0];
        String message = result.split("<message>")[1].split("</message>")[0];
        String fileName = message;
        if ("False".equals(code)) {
            throw new BaseException(message);
        }
        req = new HashMap<>();
        req.put("methodName", "AppendFile");
        req.put("fileNameNew", fileName);
        req.put("fileType", fileType);
        String str = "";
        try {
            InputStream inputStream = new FileInputStream(filePathConfig.getFilePath() + document.getPath());
            byte[] data = new byte[inputStream.available()];
            inputStream.read(data);
            inputStream.close();
            // 加密
            BASE64Encoder encoder = new BASE64Encoder();
            str = encoder.encode(data);
        } catch (IOException e) {
            e.printStackTrace();
        }
        req.put("buffer", str);
        result = webServiceUtil.doRequest(getParams(req), url);
        code = result.split("<succes>")[1].split("</succes>")[0];
        message = result.split("<message>")[1].split("</message>")[0];
        if ("False".equals(code)) {
            throw new BaseException(message);
        }
        return fileName;
    }

    @Override
    @Transactional
    public String pushProject(DtoProjectPut projectPut) {
        DtoProject project = projectService.findOne(projectPut.getProjectId());
        List<DtoProjectContract> projectContractList = projectContractRepository.findByProjectId(project.getId());
        DtoProjectContract projectContract = StringUtil.isNotEmpty(projectContractList) ? projectContractList.get(0) : new DtoProjectContract();
        if (StringUtil.isEmpty(projectContract.getTestTarget())) {
            throw new BaseException("被测对象不能为空");
        }
        DtoOrderContract contractExpand = orderContractRepository.findOne(projectContract.getContractId());
        if (!contractExpand.getIsHavingPut()) {
            throw new BaseException("选择的合同未推送");
        }
        List<String> statusList = Stream.of(EnumPRO.EnumProjectStatus.项目下达中.name(), EnumPRO.EnumProjectStatus.方案审核中.name(),
                EnumPRO.EnumProjectStatus.方案未通过.name(), EnumPRO.EnumProjectStatus.方案确认中.name(), EnumPRO.EnumProjectStatus.方案编制中.name(),
                EnumPRO.EnumProjectStatus.开展中.name(), EnumPRO.EnumProjectStatus.数据汇总中.name(), EnumPRO.EnumProjectStatus.已办结.name()).collect(Collectors.toList());
        if (!statusList.contains(project.getStatus())) {
            throw new BaseException("项目审核之后才可以推送项目");
        }
        DtoArea area = areaService.findById(projectContract.getTaskLocation());
        Map<String, String> req = new HashMap<>();
        if (StringUtil.isNotNull(project) && StringUtils.isNotNullAndEmpty(project.getPollutionCode())) {
            req.put("BCDX_WRYBH", project.getPollutionCode());
        }
        req.put("ID", "0");
        req.put("CID", contractExpand.getCId());
        req.put("RWBH", project.getProjectCode());
        req.put("RWMC", project.getProjectName());
        req.put("RWLB", projectContract.getTaskType());
        req.put("CYLX", projectContract.getIsSample());
        req.put("RWLY", projectContract.getTaskSource());
        req.put("PROVINCE", "上海市");
        req.put("CITY", area.getAreaName());
        req.put("DISTRICT", area.getAreaName());
        req.put("RWDZ", projectContract.getTaskAddress());
        req.put("BCDX", projectContract.getTestTarget());
        req.put("XCLXR", projectContract.getSampleContact());
        req.put("XCLXDH", projectContract.getContactPhone());
        req.put("JE", projectContract.getTaskPrice().toString());
        req.put("IS_CHECK", "1");
        req.put("RWGS", projectContract.getTaskContent());
        req.put("FJMS", projectContract.getFileExplain());
        req.put("methodName", "M_InsertMonitoringTask");
        String result = webServiceUtil.doRequest(getParams(req), url);
        String code = result.split("<succes>")[1].split("</succes>")[0];
        String message = result.split("<message>")[1].split("</message>")[0];
        if ("False".equals(code)) {
            throw new BaseException(message);
        }
        String pid = result.split("<ID>")[1].split("</ID>")[0];
        projectContract.setPId(pid);
        projectContract.setHasPush(1);
        projectContractRepository.save(projectContract);
        return pid;
    }

    @Override
    public String pushFolder(DtoProjectPut projectPut) {
        DtoProject project = projectService.findOne(projectPut.getProjectId());
        List<DtoProjectContract> contractList = projectContractRepository.findByProjectId(projectPut.getProjectId());
        DtoProjectContract contract = StringUtil.isNotEmpty(contractList) ? contractList.get(0) : new DtoProjectContract();
        if (!StringUtils.isNotNullAndEmpty(contract) || null == contract.getHasPush() || 0 == contract.getHasPush()) {
            throw new BaseException("项目未推送");
        }
        List<String> statusList = Stream.of(EnumPRO.EnumProjectStatus.方案确认中.name(), EnumPRO.EnumProjectStatus.开展中.name(),
                EnumPRO.EnumProjectStatus.数据汇总中.name(), EnumPRO.EnumProjectStatus.已办结.name()).collect(Collectors.toList());
        if (!statusList.contains(project.getStatus())) {
            throw new BaseException("方案审核之后才可以推送方案");
        }
        List<DtoSampleFolder> folderList = sampleFolderRepository.findByProjectId(projectPut.getProjectId());
        List<String> folderIdList = folderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        Map<String, List<DtoTest>> fId2FolderExtend = new HashMap<>();
        TestCriteria criteria = new TestCriteria();
        folderIdList.forEach(f -> {
            criteria.setSampleFolderId(f);
            fId2FolderExtend.put(f, folderExtendService.findBySampleFolderId(criteria));
        });
        for (Map.Entry<String, List<DtoTest>> entry : fId2FolderExtend.entrySet()) {
            List<DtoTest> folderExtends = entry.getValue();
            folderExtends.forEach(t -> {
                if (!StringUtils.isNotNullAndEmpty(t.getShMethodId()) || !StringUtils.isNotNullAndEmpty(t.getShSamplingMethodId())) {
                    throw new BaseException("测试项目:" + t.getRedAnalyzeItemName() + "未配置监管平台分析方法或采样方法");
                }
            });
        }
        List<DtoSamplingFrequency> frequencyList = samplingFrequencyRepository.findBySampleFolderIdIn(folderIdList);
        Map<String, String> req = new HashMap<>();
        StringBuilder requset = new StringBuilder();
        requset.append("<tem:MTID>" + contract.getPId() + "</tem:MTID>");
        requset.append("<tem:strXmlPlan>");
        Map<String, String> folderId2NewId = new HashMap<>();
        //添加scheme
        requset.append("<![CDATA[");
        requset.append("<MonitorTaskPlan>");
        requset.append("<Scheme>");
        for (DtoSampleFolder folder : folderList) {
            String fId = UUIDHelper.NewID();
            folderId2NewId.put(folder.getId(), fId);
            requset.append("<Item>");
            requset.append("<ID>" + fId + "</ID>");
            requset.append("<PT>" + folder.getWatchSpot() + "</PT>");
            requset.append("<X>" + (StringUtils.isNotNullAndEmpty(folder.getLon()) ? folder.getLon() : "0") + "</X>");
            requset.append("<Y>" + (StringUtils.isNotNullAndEmpty(folder.getLat()) ? folder.getLat() : "0") + "</Y>");
            requset.append("</Item>");
        }
        requset.append("</Scheme>");
        Map<String, String> frequencyId2NewId = new HashMap<>();
        //添加task
        requset.append("<Task>");
        for (DtoSampleFolder folder : folderList) {
            List<DtoSamplingFrequency> frequency2Folder = frequencyList.stream().filter(f -> f.getSampleFolderId().equals(folder.getId())).collect(Collectors.toList());
            DtoSamplingFrequency frequency = frequency2Folder.stream().max(Comparator.comparing(DtoSamplingFrequency::getPeriodCount)).orElse(null);
            Integer periodCount = 1;
            if (StringUtil.isNotNull(frequency)) {
                periodCount = frequency.getPeriodCount();
            }
            Integer timePerPeriod = 1;
            frequency = frequency2Folder.stream().max(Comparator.comparing(DtoSamplingFrequency::getTimePerPeriod)).orElse(null);
            if (StringUtil.isNotNull(frequency)) {
                timePerPeriod = frequency.getTimePerPeriod();
            }
            String fId = UUIDHelper.NewID();
            frequencyId2NewId.put(folder.getId(), fId);
            requset.append("<Item>");
            requset.append("<ID>" + fId + "</ID>");
            requset.append("<MTSID>" + folderId2NewId.get(folder.getId()) + "</MTSID>");
            requset.append("<ZQ>" + periodCount + "</ZQ>");
            requset.append("<PC>" + timePerPeriod + "</PC>");
            requset.append("</Item>");
        }
        requset.append("</Task>");
        //添加project
        requset.append("<Project>");
        for (Map.Entry<String, List<DtoTest>> entry : fId2FolderExtend.entrySet()) {
            List<DtoTest> folderExtends = entry.getValue();
            for (DtoTest test : folderExtends) {
                requset.append("<Item>");
                requset.append("<MTSTID>" + frequencyId2NewId.get(entry.getKey()) + "</MTSTID>");
                requset.append("<MMID>" + test.getShMethodId() + "</MMID>");
                requset.append("<SAMPLETYPE>" + test.getShSamplingMethodId() + "</SAMPLETYPE>");
                requset.append("<SAMPLENUM>" + test.getSampleCount() + "</SAMPLENUM>");
                requset.append("<DEMO></DEMO>");
                requset.append("</Item>");
            }
        }
        requset.append("</Project>");
        requset.append("</MonitorTaskPlan>");
        requset.append("]]>");
        requset.append("</tem:strXmlPlan>");
        req.put("methodName", "M_InsertMonitorTaskPlan");
        req.put("request", requset.toString());
        String result = webServiceUtil.doRequest(getParams(req), url);
        String code = result.split("<succes>")[1].split("</succes>")[0];
        String message = result.split("<message>")[1].split("</message>")[0];
        if ("False".equals(code)) {
            throw new BaseException(message);
        }
        contract.setSchemeHasPush(1);
        projectContractRepository.save(contract);
        return null;
    }

    @Override
    public List<Map<String, String>> getSamplingPlan(String pId) {
        Map<String, String> req = new HashMap<>();
        req.put("methodName", "M_GetPlanList");
        req.put("MTID", pId);
        String result = webServiceUtil.doRequest(getParams(req), url);
        result = "<Res><Items>" + result.split("<Items xmlns=\"\">")[1].split("</Items>")[0] + "</Items></Res>";
        List<Map<String, Object>> resData = XmlToObjectUtil.multilayerXmlToMap(result);
        List<Map<String, String>> resultData = new ArrayList<>();
        for (Map data : resData) {
            Map<String, String> map = new HashMap<>();
            map.put("id", StringUtil.isNotNull(data.get("ID")) ? data.get("ID").toString() : "");
            map.put("samplingTaskId", StringUtil.isNotNull(data.get("MTSTID")) ? data.get("MTSTID").toString() : "");
            map.put("samplingCompleteTime", StringUtil.isNotNull(data.get("JHSJ")) ? data.get("JHSJ").toString() : "");
            map.put("periodCount", StringUtil.isNotNull(data.get("ZQ")) ? data.get("ZQ").toString() : "");
            map.put("timePerPeriod", StringUtil.isNotNull(data.get("PC")) ? data.get("PC").toString() : "");
            map.put("watchSpot", StringUtil.isNotNull(data.get("PT")) ? data.get("PT").toString() : "");
            map.put("testNames", StringUtil.isNotNull(data.get("ITEMS")) ? data.get("ITEMS").toString() : "");
            resultData.add(map);
        }
        return resultData;
    }

    @Override
    public String updateSamplingTime(DtoProjectPut project) {
        StringBuilder request = new StringBuilder();
        request.append("<tem:strXmlPlan>");
        request.append("<![CDATA[");
        request.append("<MonitorTaskPlan>");
        request.append("<Plan>");
        for (Map<String, String> map : project.getSamplingPlan()) {
            request.append("<Item>");
            request.append("<ID>" + map.get("samplingPlanId") + "</ID>");
            request.append("<DATE>" + map.get("samplingDate") + "</DATE>");
            request.append("</Item>");
        }
        request.append("</Plan>");
        request.append("</MonitorTaskPlan>");
        request.append("]]>");
        request.append("</tem:strXmlPlan>");
        Map<String, String> req = new HashMap<>();
        req.put("methodName", "M_UpdatePlanTime");
        req.put("request", request.toString());
        String result = webServiceUtil.doRequest(getParams(req), url);
        String code = result.split("<succes>")[1].split("</succes>")[0];
        String message = result.split("<message>")[1].split("</message>")[0];
        if ("False".equals(code)) {
            throw new BaseException(message);
        }
        List<DtoProjectContract> contractList = projectContractRepository.findByProjectId(project.getProjectId());
        if (StringUtil.isNotEmpty(contractList)) {
            DtoProjectContract contract = contractList.get(0);
            contract.setPlanHasPush(1);
            projectContractRepository.save(contract);
        }
        return null;
    }

    @Override
    @Transactional
    public void pushPlan(DtoProjectPut project) {
        List<DtoProjectContract> contractList = projectContractRepository.findByProjectId(project.getProjectId());
        DtoProjectContract contract = StringUtil.isNotEmpty(contractList) ? contractList.get(0) : null;
        if (StringUtil.isNotNull(contract)) {
            checkSamplingPersonAndInstrument(contract.getPId());
            updateSamplingPerson(contract.getPId());
            updateSamplingDevice(contract.getPId());
            contract.setPlanHasPush(1);
            projectContractRepository.save(contract);
            updateNewSamplingPerson(contract.getPId());
        }
    }

    @Override
    public String updateSamplingPerson(String taskId) {
        StringBuilder request = new StringBuilder();
        List<DtoSHSamplingPersonNew> personList = shSamplingPersonRepository.findByTaskId(taskId);
        List<DtoPerson> personExpands = personService.findAll();
        if (StringUtil.isNotEmpty(personList)) {
            request.append("<tem:WorkerDataList>");
            for (DtoSHSamplingPersonNew person : personList) {
                request.append("<tem:WorkerData>");
                request.append("<tem:MTID>" + taskId + "</tem:MTID>");
                Optional<DtoPerson> personExpand = personExpands.stream().filter(p -> p.getId().equals(person.getPersonId())).findFirst();
                personExpand.ifPresent(per -> {
                    request.append("<tem:OWID>" + per.getRegulateId() + "</tem:OWID>");
                });
                request.append("</tem:WorkerData>");
            }
            request.append("</tem:WorkerDataList>");
            Map<String, String> req = new HashMap<>();
            req.put("methodName", "M_AddWorkers");
            req.put("request", request.toString());
            String result = webServiceUtil.doRequest(getParams(req), url);
            String code = result.split("<succes>")[1].split("</succes>")[0];
            String message = result.split("<message>")[1].split("</message>")[0];
            if ("False".equals(code)) {
                throw new BaseException(message);
            }
        }
        return null;
    }

    @Override
    public String updateSamplingDevice(String taskId) {
        StringBuilder request = new StringBuilder();
        List<DtoSHSamplingInstrumentNew> instrumentList = SHSamplingInstrumentNewRepository.findByTaskId(taskId);
        List<DtoInstrument> allInstruments = instrumentService.findAll();
        if (StringUtil.isNotEmpty(instrumentList)) {
            request.append("<tem:DeivceDataList>");
            for (DtoSHSamplingInstrumentNew instrument : instrumentList) {
                request.append("<tem:DeivceData>");
                request.append("<tem:MTID>" + taskId + "</tem:MTID>");
                Optional<DtoInstrument> instrumentExpand = allInstruments.stream().filter(i -> i.getId().equals(instrument.getInstrumentId())).findFirst();
                instrumentExpand.ifPresent(ins -> {
                    request.append("<tem:ODID>" + ins.getRegulateId() + "</tem:ODID>");
                });
                request.append("</tem:DeivceData>");
            }
            request.append("</tem:DeivceDataList>");
            Map<String, String> req = new HashMap<>();
            req.put("methodName", "M_AddDeivces");
            req.put("request", request.toString());
            String result = webServiceUtil.doRequest(getParams(req), url);
            String code = result.split("<succes>")[1].split("</succes>")[0];
            String message = result.split("<message>")[1].split("</message>")[0];
            if ("False".equals(code)) {
                throw new BaseException(message);
            }
        }
        return null;
    }

    @Override
    @Transactional
    public void methodMatch(DtoProjectPut project) {
        Map<String, String> req = new HashMap<>();
        req.put("methodName", "M_MonitoringAnalysisMethod");
        req.put("page", "1");
        req.put("rows", "100000");
        List<Map<String, Object>> mapList = queryByMethodName(req, new RestResponse());
        List<DtoTest> testList = folderExtendService.findByProjectId(project.getProjectId());
        List<DtoTest> waitSaveList = new ArrayList<>();
        for (DtoTest test : testList) {
            if (!StringUtils.isNotNullAndEmpty(test.getShMethodId())) {
                Map<String, Object> map = mapList.stream().filter(m -> StringUtils.isNotNullAndEmpty(m.get("METHODNAME"))
                        && (m.get("METHODNAME").toString().replace(" ", "")).contains(test.getRedAnalyzeMethodName()
                        .replace(" ", ""))).findFirst().orElse(null);
                if (StringUtil.isNotNull(map)) {
                    test.setShMethodId(map.get("METHODID").toString());
                    test.setShMethodName(map.get("METHODNAME").toString());
                    waitSaveList.add(test);
                }
            }
        }
        testRepository.save(waitSaveList);
    }

    @Override
    public void findByCondition(PageBean<DtoProjectPut> page, BaseCriteria criteria) {
        page.setEntityName("DtoProject p,DtoProjectContract c");
        page.setSelect("select new com.sinoyd.lims.pro.dto.customer.DtoProjectPut(p.projectTypeId,p.projectCode,p.projectName,p.customerName,p.inceptTime,p.status,c.id,c.contractId," +
                "c.projectId,c.contractName,c.taskPrice,c.taskType,c.isSample,c.taskSource,c.taskLocation,c.sampleContact,c.contactPhone," +
                "c.taskAddress,c.fileExplain,c.taskContent,c.isPush,c.hasPush,c.isHandle,c.pId,c.schemeHasPush,c.planHasPush,c.reportHasPush)");
        commonRepository.findByPage(page, criteria);
    }

    @Override
    public List<DtoDocument> findReport(String projectId) {
        List<DtoReport> reports = reportRepository.findByProjectId(projectId);
        List<String> reportIds = reports.stream().map(DtoReport::getId).collect(Collectors.toList());
        List<DtoSerialIdentifierConfig> list = serialIdentifierConfigService.findListByConfigType(EnumLIM.EnumIdentifierConfig.报告编号.getValue());
        //将list转为map
        Map<String, String> reportTypeMap = list.stream().collect(Collectors.toMap(DtoSerialIdentifierConfig::getId, DtoSerialIdentifierConfig::getConfigName));
        List<DtoDocument> documents = new ArrayList<>();
        reportIds.forEach(r -> documents.addAll(documentRepository.findByFolderId(r)));
        List<DtoDocument> result = documents.stream().filter(d -> d.getFilename().endsWith("pdf")).collect(Collectors.toList());
        List<String> personIds = reports.stream().map(DtoReport::getCreatePersonId).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds)?personService.findAll(personIds):new ArrayList<>();
        result.forEach(r -> {
            Optional<DtoReport> report = reports.stream().filter(p -> p.getId().equals(r.getFolderId())).findFirst();
            report.ifPresent(p -> {
                r.setReportCode(p.getCode());
                r.setReportType(reportTypeMap.get(p.getReportTypeId()));
                r.setReportYear(p.getReportYear());
                Optional<DtoPerson> person = personList.stream().filter(per -> per.getId().equals(p.getCreatePersonId())).findFirst();
                person.ifPresent(per -> r.setSenderName(per.getCName()));
            });
        });
        return result;
    }

    @Override
    @Transactional
    public String pushReport(DtoProjectPut project) {
        DtoDocument document = documentRepository.findOne(project.getDocumentId());
        DtoReport report = reportRepository.findOne(document.getFolderId());
        List<DtoProjectContract> projectContractList = projectContractRepository.findByProjectId(report.getProjectId());
        DtoProjectContract projectContract = StringUtil.isNotEmpty(projectContractList) ? projectContractList.get(0) : new DtoProjectContract();
        DtoReport reportExpand = reportRepository.findOne(document.getFolderId());
        if (!StringUtils.isNotNullAndEmpty(projectContract.getPId())) {
            throw new BaseException("项目未推送");
        }
        if (StringUtil.isNull(reportExpand) || !StringUtils.isNotNullAndEmpty(reportExpand.getRegulateCode())) {
            throw new BaseException("未生成监管平台系统编号");
        }
        Map<String, String> req = new HashMap<>();
        req.put("methodName", "CreateFile");
        req.put("fileName", document.getFilename());
        req.put("fileType", "8");
        String result = webServiceUtil.doRequest(getParams(req), url);
        String code = result.split("<succes>")[1].split("</succes>")[0];
        String message = result.split("<message>")[1].split("</message>")[0];
        String fileName = message;
        if ("False".equals(code)) {
            throw new BaseException(message);
        }

        req = new HashMap<>();
        req.put("methodName", "AppendFile");
        req.put("fileNameNew", fileName);
        req.put("fileType", "8");
        String str = "";
        try {
            InputStream inputStream = new FileInputStream(filePathConfig.getFilePath() + document.getPath());
            byte[] data = new byte[inputStream.available()];
            inputStream.read(data);
            inputStream.close();
            // 加密
            BASE64Encoder encoder = new BASE64Encoder();
            str = encoder.encode(data);
        } catch (IOException e) {
            e.printStackTrace();
        }
        req.put("buffer", str);
        result = webServiceUtil.doRequest(getParams(req), url);
        code = result.split("<succes>")[1].split("</succes>")[0];
        message = result.split("<message>")[1].split("</message>")[0];
        if ("False".equals(code)) {
            throw new BaseException(message);
        }
        req = new HashMap<>();
        req.put("methodName", "M_AddUReport");
        req.put("MTID", projectContract.getPId());
        req.put("ID", "0");
        req.put("CODE", reportExpand.getRegulateCode());
        req.put("REPORT_DATE", DateUtil.dateToString(report.getCreateTime(), DateUtil.YEAR));
        req.put("FILENAME", document.getFilename());
        req.put("FILENAMEOLD", fileName);
        result = webServiceUtil.doRequest(getParams(req), url);
        code = result.split("<succes>")[1].split("</succes>")[0];
        message = result.split("<message>")[1].split("</message>")[0];
        if ("False".equals(code)) {
            throw new BaseException(message);
        }
        document.setRemark("已经推送");
        documentRepository.save(document);
        projectContract.setIsHandle(1);
        projectContract.setReportHasPush(1);
        projectContractRepository.save(projectContract);
        return null;
    }

    @Override
    public String getReportNum(String projectId) {
        List<DtoProjectContract> projectContractList = projectContractRepository.findByProjectId(projectId);
        DtoProjectContract projectContract = StringUtil.isNotEmpty(projectContractList) ? projectContractList.get(0) : null;
        if (StringUtil.isNull(projectContract) || !StringUtils.isNotNullAndEmpty(projectContract.getPId())) {
            throw new BaseException("该项目未推送");
        }
        Map<String, String> req = new HashMap<>();
        req.put("methodName", "M_GetReportNum");
        req.put("MTID", projectContract.getPId());
        String result = webServiceUtil.doRequest(getParams(req), url);
        String code = result.split("<succes>")[1].split("</succes>")[0];
        String message = result.split("<message>")[1].split("</message>")[0];
        if ("False".equals(code)) {
            throw new BaseException(message);
        }
        return message;
    }

    @Override
    public DtoPerson updatePersonExpand(DtoPerson dto) {
        DtoPerson person = personService.findOne(dto.getId());
        if(person!=null){
            person.setRegulateId(dto.getRegulateId());
            person.setRegulateName(dto.getRegulateName());
        }
        return personRepository.save(person);
    }

    @Override
    public DtoInstrument updateInstrumentExpand(DtoInstrument dto) {
        DtoInstrument instrument = instrumentRepository.findOne(dto.getId());
        if (instrument != null) {
            instrument.setRegulateId(dto.getRegulateId());
            instrument.setRegulateName(dto.getRegulateName());
            if(dto.getOrderNum()!=null){
                instrument.setOrderNum(dto.getOrderNum());
            }
        }
        return instrumentRepository.save(instrument);
    }

    private String getParams(Map<String, String> req) {
        StringBuilder res = new StringBuilder();
        res.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">")
                .append("<soapenv:Header>")
                .append("<tem:IsValidSoapHeader>")
                .append("<tem:UserName>")
                .append(username)
                .append("</tem:UserName>")
                .append("<tem:PassWord>")
                .append(password)
                .append("</tem:PassWord>")
                .append("</tem:IsValidSoapHeader>")
                .append("</soapenv:Header>")
                .append("<soapenv:Body>")
                .append("<tem:" + req.get("methodName") + ">");
        for (Map.Entry entry : req.entrySet()) {
            if (!entry.getKey().equals("methodName") && !entry.getKey().equals("key") && !entry.getKey().equals("request")
                    && !entry.getKey().equals("page") && !entry.getKey().equals("rows")) {
                res.append("<tem:" + entry.getKey() + ">" + entry.getValue() + "</tem:" + entry.getKey() + ">");
            } else if (entry.getKey().equals("request")) {
                res.append(entry.getValue());
            }
        }
        res.append("</tem:" + req.get("methodName") + ">")
                .append("</soapenv:Body>")
                .append("</soapenv:Envelope>");
        log.info(res.toString());
        return res.toString();
    }

    /**
     * 验证采样仪器和采样人员是否与监管平台绑定
     *
     * @param pid 监管平台项目id
     */
    private void checkSamplingPersonAndInstrument(String pid) {
        List<DtoSHSamplingPersonNew> personList = shSamplingPersonRepository.findByTaskId(pid);
        List<DtoPerson> personExpands = personService.findAll();
        personList.forEach(p -> {
            DtoPerson personExpand = personExpands.stream().filter(per -> per.getId().equals(p.getPersonId())).findFirst().orElse(null);
            if (StringUtil.isNull(personExpand) || !StringUtils.isNotNullAndEmpty(personExpand.getRegulateId())) {
                throw new BaseException("当前选择人员/仪器未与监管平台绑定，请检查后重试！");
            }
        });
        List<DtoSHSamplingInstrumentNew> instrumentList = SHSamplingInstrumentNewRepository.findByTaskId(pid);
        List<DtoInstrument> allInstruments = instrumentService.findAll();
        instrumentList.forEach(i -> {
            DtoInstrument instrument = allInstruments.stream().filter(ins -> ins.getId().equals(i.getInstrumentId())).findFirst().orElse(null);
            if (StringUtil.isNull(instrument) || !StringUtils.isNotNullAndEmpty(instrument.getRegulateId())) {
                throw new BaseException("当前选择人员/仪器未与监管平台绑定，请检查后重试！");
            }
        });
    }

    /**
     * 更新采样计划的采样人员为最新推送的采样人员
     *
     * @param taskId 任务id
     */
    private void updateNewSamplingPerson(String taskId) {
        List<DtoSHSamplingPersonNew> personList = shSamplingPersonRepository.findByTaskId(taskId);
        List<DtoProjectContract> projectContracts = projectContractRepository.findByPIdNotNullAndPlanHasPush(0);
        List<String> exsitsTaskIds = projectContracts.stream().map(DtoProjectContract::getPId).collect(Collectors.toList());
        shSamplingPersonRepository.deleteByTaskIdIn(exsitsTaskIds);
        List<DtoSHSamplingPersonNew> newPersonList = new ArrayList<>();
        exsitsTaskIds.forEach(t -> {
            personList.forEach(p -> {
                DtoSHSamplingPersonNew samplingPerson = new DtoSHSamplingPersonNew();
                samplingPerson.setTaskId(t);
                samplingPerson.setPersonId(p.getPersonId());
                newPersonList.add(samplingPerson);
            });
        });
        if (StringUtil.isNotEmpty(newPersonList)) {
            shSamplingPersonRepository.save(newPersonList);
        }
    }

    @Autowired
    public void setWebServiceUtil(WebServiceUtil webServiceUtil) {
        this.webServiceUtil = webServiceUtil;
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    public void setReportRepository(ReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }

    @Autowired
    @Lazy
    public void setSerialIdentifierConfigService(SerialIdentifierConfigService serialIdentifierConfigService) {
        this.serialIdentifierConfigService = serialIdentifierConfigService;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    public void setProjectContractRepository(ProjectContractRepository projectContractRepository) {
        this.projectContractRepository = projectContractRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setSamplingFrequencyRepository(SamplingFrequencyRepository samplingFrequencyRepository) {
        this.samplingFrequencyRepository = samplingFrequencyRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    @Lazy
    public void setFolderExtendService(FolderExtendService folderExtendService) {
        this.folderExtendService = folderExtendService;
    }

    @Autowired
    public void setShSamplingPersonRepository(SHSamplingPersonNewRepository shSamplingPersonRepository) {
        this.shSamplingPersonRepository = shSamplingPersonRepository;
    }

    @Autowired
    public void setSHSamplingInstrumentNewRepository(SHSamplingInstrumentNewRepository SHSamplingInstrumentNewRepository) {
        this.SHSamplingInstrumentNewRepository = SHSamplingInstrumentNewRepository;
    }

    @Autowired
    @Lazy
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }

    @Autowired
    @Lazy
    public void setInstrumentService(InstrumentService instrumentService) {
        this.instrumentService = instrumentService;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setOrderContractRepository(OrderContractRepository orderContractRepository) {
        this.orderContractRepository = orderContractRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setInstrumentRepository(InstrumentRepository instrumentRepository) {
        this.instrumentRepository = instrumentRepository;
    }
}
