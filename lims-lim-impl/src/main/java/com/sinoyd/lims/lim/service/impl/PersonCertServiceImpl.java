package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.PersonCertCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.PersonAbilityRepository;
import com.sinoyd.lims.lim.repository.lims.PersonCertRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.service.PersonAbilityService;
import com.sinoyd.lims.lim.service.PersonCertService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员证书
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/6
 * @since V100R001
 */
@Service
public class PersonCertServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPersonCert, String, PersonCertRepository> implements PersonCertService {

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private TestService testService;

    @Autowired
    private CodeService codeService;

    @Autowired
    private PersonAbilityRepository personAbilityRepository;

    // 分页查询
    @Override
    public void findByPage(PageBean<DtoPersonCert> page, BaseCriteria baseCriteria) {
        page.setEntityName("DtoPersonCert p");
        page.setSelect("select p");
        page.setSort("p.personId, p.certCode, p.certName ");
        PersonCertCriteria criteria = (PersonCertCriteria) baseCriteria;

        DtoCode code = codeService.findByCode("BASE_ExpirationAlertTime_PersonCert");
        // 状态不为空时，获取所有数据，并根据人员上岗证过期预警时间进行判断过滤
        String state = criteria.getState();
        if (StringUtil.isNotEmpty(state) && !"-1".equals(state)) {
            Date compareDate = DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR);
            if (!"2".equals(state)) {
                Calendar expiryDateCalendar = new GregorianCalendar();
                Date date = new Date();
                expiryDateCalendar.setTime(date);
                //当前日期加上预警天数
                expiryDateCalendar.add(Calendar.DATE, Integer.parseInt(code.getDictValue()));
                compareDate = expiryDateCalendar.getTime();
            }
            criteria.setCompareDate(compareDate);
        }
        super.findByPage(page, baseCriteria);
        List<DtoPersonCert> personCertList = page.getData();
        if (StringUtil.isNotEmpty(personCertList)) {
            List<DtoPerson> personList = personRepository.findAll();
            Map<String, DtoPerson> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, p -> p));
            for (DtoPersonCert personCert : personCertList) {
                personCert.setPersonName(personMap.getOrDefault(personCert.getPersonId(), new DtoPerson()).getCName());
            }
        }
        page.setData(personCertList);
    }


    // 删除人员证书
    @Transactional
    @Override
    public void delete(DtoPersonCert personCert) {
        super.delete(personCert);
    }

    /**
     * 新增人员证书
     */
    @Transactional
    @Override
    public DtoPersonCert save(DtoPersonCert entity) {
        if (StringUtil.isNotEmpty(entity.getCertCode()) && repository.getCountByCertCode(entity.getCertCode(), entity.getId()) > 1) {
            throw new BaseException("已存在相同的证书编号！");
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoPersonCert update(DtoPersonCert entity) {
        if (StringUtil.isNotEmpty(entity.getCertCode()) && repository.getCountByCertCode(entity.getCertCode(), entity.getId()) > 1) {
            throw new BaseException("已存在相同的证书编号！");
        }
        return super.update(entity);
    }

    @Override
    public DtoPersonCert findPersonAttachment(String certCode, String id) {
        DtoPersonCert personCert = super.findOne(id);
        if (StringUtil.isNull(personCert)) {
            personCert = new DtoPersonCert();
            personCert.setId(id);
            personCert.setCertCode(certCode);
        }
        return personCert;
    }

    @Override
    public List<DtoPersonAbility> getOverDueData() {
        //证书过期
//        StringBuilder detailBuilder = new StringBuilder("select x");
//        detailBuilder.append(" from DtoPersonCert as x")
//                .append(" where  DATE_FORMAT( certEffectiveTime, '%Y-%m-%d' ) != '1753-01-01' ")
//                .append(" and DATE_FORMAT(certEffectiveTime, '%Y-%m-%d') < DATE_FORMAT(now(), '%Y-%m-%d') ")
//                .append(" and EXISTS (select 1 from DtoPerson p where p.id = x.personId and p.isDeleted = 0)");
//        List<DtoPersonCert> list = comRepository.find(detailBuilder.toString());
//        List<String> certIdList = list.stream().map(DtoPersonCert::getId).collect(Collectors.toList());
        List<DtoPersonAbility> result = new ArrayList<>();
        //能力过期
        StringBuilder abilityBuilder = new StringBuilder("select a");
        abilityBuilder.append(" from DtoPersonAbility as a")
                .append(" where  DATE_FORMAT( certEffectiveTime, '%Y-%m-%d' ) != '1753-01-01' ")
                .append(" and DATE_FORMAT(certEffectiveTime, '%Y-%m-%d') < DATE_FORMAT(now(), '%Y-%m-%d') ")
                .append(" and EXISTS (select 1 from DtoPerson p where p.id = a.personId and p.isDeleted = 0)")
                .append(" and EXISTS (select 1 from DtoPersonCert pt where a.personCertId = pt.id)")
                .append(" and EXISTS (select 1 from DtoTest t where a.testId = t.id and t.isDeleted = 0)");
        List<DtoPersonAbility> abilitylist = comRepository.find(abilityBuilder.toString());
//        abilitylist = abilitylist.stream().filter(a -> !certIdList.contains(a.getPersonCertId())).collect(Collectors.toList());
        //证书调整为能力格式统一返回
//        for (DtoPersonCert personCert : list) {
//            DtoPersonAbility personAbility = new DtoPersonAbility();
//            personAbility.setPersonId(personCert.getPersonId());
//            personAbility.setPersonCertId(personCert.getId());
//            personAbility.setAchieveDate(personCert.getIssueCertTime());
//            personAbility.setCertEffectiveTime(personCert.getCertEffectiveTime());
//            abilitylist.add(personAbility);
//        }
        if (StringUtil.isNotEmpty(abilitylist)) {
            // 根据人员，证书，发证日期，有效期至  组合生成分组key
            List<String> groupKeys = abilitylist.stream().map(p -> String.format("%s%s%s%s", p.getPersonId(), p.getPersonCertId(), p.getAchieveDate(), p.getCertEffectiveTime())).distinct().collect(Collectors.toList());
            List<String> testIds = abilitylist.stream().map(DtoPersonAbility::getTestId).distinct().collect(Collectors.toList());
            List<String> personCertIds = abilitylist.stream().map(DtoPersonAbility::getPersonCertId).distinct().collect(Collectors.toList());
            List<String> personIds = abilitylist.stream().map(DtoPersonAbility::getPersonId).distinct().collect(Collectors.toList());
            //关联查询
            List<DtoPerson> dtoPersonList = StringUtil.isNotEmpty(personCertIds) ? personRepository.findAll(personIds) : new ArrayList<>();
            List<DtoPersonCert> dtoPersonCertList = StringUtil.isNotEmpty(personCertIds) ? repository.findAll(personCertIds) : new ArrayList<>();
            List<DtoTest> dtoTestList = StringUtil.isNotEmpty(testIds) ? testService.findRedisByIds(testIds) : new ArrayList<>();
            List<String> testParentIds = dtoTestList.stream().map(DtoTest::getParentId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
            List<DtoTest> parentTestList = StringUtil.isNotEmpty(testParentIds) ? testService.findRedisByIds(testParentIds) : new ArrayList<>();


            Date now = new Date();
            for (String key : groupKeys) {
                List<DtoPersonAbility> abilities = abilitylist.stream().filter(p -> String.format("%s%s%s%s", p.getPersonId(), p.getPersonCertId(), p.getAchieveDate(), p.getCertEffectiveTime()).equals(key)).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(abilities)) {
                    DtoPersonAbility ability = abilities.get(0);
                    DtoPersonAbility personAbility = new DtoPersonAbility();
                    personAbility.setExpirationDays(CalendarUtil.getDaysBetween(ability.getCertEffectiveTime(), now));
                    personAbility.setAchieveDate(ability.getAchieveDate());
                    personAbility.setCertEffectiveTime(ability.getCertEffectiveTime());
                    DtoPerson dtoPerson = dtoPersonList.stream().filter(p -> p.getId().equals(ability.getPersonId())).findFirst().orElse(null);
                    if (dtoPerson != null) {
                        personAbility.setPerson(dtoPerson.getCName());
                    }
                    //证书编号
                    DtoPersonCert dtoPersonCert = dtoPersonCertList.stream().filter(c -> c.getId().equals(ability.getPersonCertId())).findFirst().orElse(null);
                    if (dtoPersonCert != null) {
                        personAbility.setPersonCertCode(dtoPersonCert.getCertCode());
                    }
                    //分析项目 分析方法
                    List<String> testIdList = abilities.stream().map(DtoPersonAbility::getTestId).distinct().collect(Collectors.toList());
                    List<DtoTest> tests = dtoTestList.stream().filter(t -> testIdList.contains(t.getId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(tests)) {
                        List<DtoTest> finalTests = new ArrayList<>();
                        // 测试项目
                        Map<String, List<DtoTest>> parentId2TestListMap = tests.stream().collect(Collectors.groupingBy(DtoTest::getParentId));
                        for (Map.Entry<String, List<DtoTest>> entry : parentId2TestListMap.entrySet()) {
                            String parentId = entry.getKey();
                            List<DtoTest> loopTestList = entry.getValue();
                            //获取父测试项目
                            DtoTest parentTest = parentTestList.stream().filter(p -> parentId.equals(p.getId())).findFirst().orElse(null);
                            if (StringUtil.isNotNull(parentTest) && parentTest.getIsTotalTest() && loopTestList.size() > parentTest.getMergeBase()) {
                                finalTests.add(parentTest);
                            } else {
                                finalTests.addAll(loopTestList);
                            }
                        }
                        personAbility.setRedAnalyzeItemName(finalTests.stream().sorted(Comparator.comparing(DtoTest::getOrderNum)).map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining(",")));
                    }
                    result.add(personAbility);
                }
            }
            //人员顺序 过期天数倒序 分析方法顺序 分析项目顺序
            result.sort(Comparator.comparing(DtoPersonAbility::getPerson)
                    .thenComparing(DtoPersonAbility::getExpirationDays, Comparator.reverseOrder())
//                .thenComparing(DtoPersonAbility::getRedAnalyzeMethodName)
                    .thenComparing(DtoPersonAbility::getRedAnalyzeItemName));
        }
        return result;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        // 删除证书，级联删除检测能力详情
        personAbilityRepository.deleteByCertIds(idList);
        return super.logicDeleteById(ids);
    }
}