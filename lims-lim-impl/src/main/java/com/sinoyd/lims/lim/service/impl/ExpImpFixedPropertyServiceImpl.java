package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.FixedAssetsCriteria;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpFixedProperty;
import com.sinoyd.lims.lim.dto.lims.DtoFixedProperty;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.FixedAssetsRepository;
import com.sinoyd.lims.lim.service.ExpImpFixedPropertyService;
import com.sinoyd.lims.lim.service.FixedAssetsService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyFixedAssetsVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 固定资产导入导出接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@Service
public class ExpImpFixedPropertyServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFixedProperty, String, FixedAssetsRepository> implements ExpImpFixedPropertyService {

    private CodeService codeService;
    private ImportUtils importUtils;
    private DepartmentService departmentService;
    private EnterpriseRepository enterpriseRepository;
    private PersonService personService;
    private FixedAssetsService fixedAssetsService;
    private ImpModifyFixedAssetsVerify impModifyFixedAssetsVerify;
    private final static String[] status = new String[]{"使用中", "已报废"};

    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoFixedProperty> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        FixedAssetsCriteria criteria = (FixedAssetsCriteria) baseCriteria;
        fixedAssetsService.findByPage(page, criteria);
        List<DtoFixedProperty> DtoFixedProperty = page.getData();

        // 供应商
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findByIsDeletedFalseAndType(EnumBase.EnumEnterpriseType.供应商.getValue());
        // 所属科室
        List<DtoDepartment> deptList = departmentService.findAll();
        List<DtoPerson> personList = personService.findAll();
        List<DtoExpImpFixedProperty> importFixedAssets = new ArrayList<>();
        for (DtoFixedProperty dtoFixedAsset : DtoFixedProperty) {
            DtoExpImpFixedProperty dtoExpImpFixedProperty = new DtoExpImpFixedProperty();
            BeanUtils.copyProperties(dtoFixedAsset, dtoExpImpFixedProperty);
            String purchasePrice = StringUtil.isNotEmpty(dtoFixedAsset.getPurchasePrice().toString()) ? new BigDecimal(dtoFixedAsset.getPurchasePrice().toString()).toPlainString() : "";
            dtoExpImpFixedProperty.setPurchasePrice(purchasePrice);
            dtoExpImpFixedProperty.setPurchaseDate(StringUtil.isNotNull(dtoFixedAsset.getPurchaseDate()) ? DateUtil.dateToString(dtoFixedAsset.getPurchaseDate(), DateUtil.YEAR) : "");
            Optional<DtoEnterprise> dtoEnterprise = enterpriseList.stream().filter(p -> p.getId().equals(dtoFixedAsset.getSupplier())).findFirst();
            dtoEnterprise.ifPresent(p -> dtoExpImpFixedProperty.setSupplier(p.getName()));
            Optional<DtoDepartment> dtoDepartment = deptList.stream().filter(p -> p.getId().equals(dtoFixedAsset.getDeptId())).findFirst();
            dtoDepartment.ifPresent(p -> dtoExpImpFixedProperty.setDeptId(p.getDeptName()));
            Optional<DtoPerson> dtoPerson = personList.stream().filter(p -> p.getId().equals(dtoFixedAsset.getManager())).findFirst();
            dtoPerson.ifPresent(p -> dtoExpImpFixedProperty.setManager(p.getCName()));
            dtoExpImpFixedProperty.setStatus(EnumLIM.EnumAssetsStatus.getByValue(dtoFixedAsset.getStatus()));
            importFixedAssets.add(dtoExpImpFixedProperty);
        }
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpFixedProperty.class, importFixedAssets);
        // 设置下拉框
        this.processDropList(workBook);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    @Transactional
    public List<DtoFixedProperty> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);

        // 供应商
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findByIsDeletedFalseAndType(EnumBase.EnumEnterpriseType.供应商.getValue());
        // 获取所有资产类型
        List<DtoCode> codeList = codeService.findCodes("PRO_Assets_Type");
        // 所属科室
        List<DtoDepartment> deptList = departmentService.findAll();
        List<DtoPerson> personList = personService.findAll();
        // 临时数据
        List<DtoExpImpFixedProperty> fixedAssets = new ArrayList<>();
        // 系统中的数据
        List<DtoFixedProperty> fixedPropertyAll = repository.findAll();
        impModifyFixedAssetsVerify.getFixedAssetsTl().set(fixedAssets);
        impModifyFixedAssetsVerify.getEnterpriseTl().set(enterpriseList);
        impModifyFixedAssetsVerify.getCodeTl().set(codeList);
        impModifyFixedAssetsVerify.getDeptTl().set(deptList);
        impModifyFixedAssetsVerify.getPersonTl().set(personList);
        impModifyFixedAssetsVerify.getFixedAllTl().set(fixedPropertyAll);
        //获取导入结果
        ExcelImportResult<DtoExpImpFixedProperty> result = getExcelData(file, response);
        List<DtoExpImpFixedProperty> importFixedAssets = result.getList();
        //清除线程变量
        impModifyFixedAssetsVerify.getFixedAssetsTl().remove();
        impModifyFixedAssetsVerify.getEnterpriseTl().remove();
        impModifyFixedAssetsVerify.getCodeTl().remove();
        impModifyFixedAssetsVerify.getDeptTl().remove();
        impModifyFixedAssetsVerify.getPersonTl().remove();
        impModifyFixedAssetsVerify.getFixedAllTl().remove();
        //删除空行
        importFixedAssets.removeIf(p -> StringUtil.isEmpty(p.getAssetsName()));
        //判断文件中是否存在数据
        if (StringUtil.isEmpty(importFixedAssets)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        // 导入数据转实体
        List<DtoFixedProperty> assets = addDataHandle(importFixedAssets, enterpriseList, personList, deptList, fixedPropertyAll);
        addData(assets);

        return fixedAssetsService.findAll();
    }


    @Override
    @Transactional
    public void addData(List<DtoFixedProperty> data) {
        if (StringUtil.isNotEmpty(data)) {
            repository.save(data);
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpFixedProperty> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);

        params.setVerifyHandler(impModifyFixedAssetsVerify);
        ExcelImportResult<DtoExpImpFixedProperty> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoExpImpFixedProperty.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "固定资产导入错误信息");
            PoiExcelUtils.downLoadExcel("固定资产导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 导入数据转实体
     *
     * @param importFixedAssets 导入实体
     * @param enterpriseList    供应商
     * @param personList        用户
     * @param deptList          科室
     * @return 固定资产实体
     */
    private List<DtoFixedProperty> addDataHandle(List<DtoExpImpFixedProperty> importFixedAssets, List<DtoEnterprise> enterpriseList, List<DtoPerson> personList, List<DtoDepartment> deptList, List<DtoFixedProperty> fixedPropertyAll) {
        List<DtoFixedProperty> fixedAssetsList = new ArrayList<>();
        Map<String, DtoFixedProperty> fixedPropertyMap = fixedPropertyAll.stream().collect(Collectors.toMap(DtoFixedProperty::getId, p -> p));
        for (DtoExpImpFixedProperty importFixedAsset : importFixedAssets) {
            DtoFixedProperty fixedAssets = new DtoFixedProperty();
            if (fixedPropertyMap.containsKey(fixedAssets.getId())) {
                fixedAssets = fixedPropertyMap.get(fixedAssets.getId());
            } else {
                importFixedAsset.setId(UUIDHelper.NewID());
            }
            fixedAssets.setPurchasePrice(StringUtil.isNotEmpty(importFixedAsset.getPurchasePrice()) ? Double.parseDouble(importFixedAsset.getPurchasePrice()) : 0.0);
            BeanUtils.copyProperties(importFixedAsset, fixedAssets);
            fixedAssets.setStatus(StringUtil.isEmpty(importFixedAsset.getStatus()) ? 1 : Integer.parseInt(importFixedAsset.getStatus()));
            if (StringUtil.isNotEmpty(importFixedAsset.getSupplier())) {
                DtoEnterprise dtoEnterprise = enterpriseList.stream().filter(p -> importFixedAsset.getSupplier().equals(p.getName())).findFirst().orElse(null);
                fixedAssets.setSupplier(StringUtil.isNotNull(dtoEnterprise) ? dtoEnterprise.getId() : UUIDHelper.GUID_EMPTY);
            }
            DtoDepartment dtoDepartment = deptList.stream().filter(p -> p.getDeptName().equals(importFixedAsset.getDeptId())).findFirst().orElse(null);
            fixedAssets.setDeptId(StringUtil.isNotNull(dtoDepartment) ? dtoDepartment.getId() : UUIDHelper.GUID_EMPTY);
            Optional<DtoPerson> dtoPerson = personList.stream().filter(p -> p.getCName().equals(importFixedAsset.getManager())).findFirst();
            fixedAssets.setManager(dtoPerson.get().getId());
            fixedAssets.setPurchaseDate(StringUtil.isEmpty(importFixedAsset.getPurchaseDate()) ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importFixedAsset.getPurchaseDate()));
            fixedAssetsList.add(fixedAssets);
        }
        return fixedAssetsList;
    }

    /**
     * 处理下拉框信息
     *
     * @param workBook 工作簿
     */
    public void processDropList(Workbook workBook) {
        // 供应商
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findByIsDeletedFalseAndType(EnumBase.EnumEnterpriseType.供应商.getValue());
        List<String> enterpriseNames = enterpriseList.stream().map(DtoEnterprise::getName).collect(Collectors.toList());

        // 获取所有资产类型
        List<DtoCode> dbPost = codeService.findCodes("PRO_Assets_Type");
        List<String> dictName = dbPost.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        // 所属科室
        List<DtoDepartment> deptList = departmentService.findAll();
        List<String> deptNames = deptList.stream().map(DtoDepartment::getDeptName).collect(Collectors.toList());
        // 设置下拉框信息
        String[] enterpriseNamesDroplist = new String[enterpriseNames.size()];
        String[] dictNameDroplist = new String[dictName.size()];
        String[] deptNamesDroplist = new String[deptNames.size()];
        for (int i = 0; i < enterpriseNames.size(); i++) {
            enterpriseNamesDroplist[i] = enterpriseNames.get(i);
        }
        for (int i = 0; i < dictName.size(); i++) {
            dictNameDroplist[i] = dictName.get(i);
        }
        for (int i = 0; i < deptNames.size(); i++) {
            deptNamesDroplist[i] = deptNames.get(i);
        }
        importUtils.selectList(workBook, 6, 6, enterpriseNamesDroplist);
        importUtils.selectList(workBook, 7, 7, dictNameDroplist);
        importUtils.selectList(workBook, 8, 8, deptNamesDroplist);
        importUtils.selectList(workBook, 9, 9, status);
    }

    @Autowired
    @Lazy
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    @Lazy
    public void setFixedAssetsService(FixedAssetsService fixedAssetsService) {
        this.fixedAssetsService = fixedAssetsService;
    }

    @Autowired
    @Lazy
    public void setExpImpFixedAssetsVerify(ImpModifyFixedAssetsVerify impModifyFixedAssetsVerify) {
        this.impModifyFixedAssetsVerify = impModifyFixedAssetsVerify;
    }
}
