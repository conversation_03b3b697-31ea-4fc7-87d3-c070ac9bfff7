package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.List;

@Data
public class DtoDataDetail {

    /**
     * 项目ids
     */
    private List<String> projectIds;

    /***
     * 是否检出
     */
    private Boolean enableData;

    /**
     * 是否标红
     */
    private Boolean overRed;

    /**
     * 分析项目排序
     */
    private String itemSortId;

    /**
     * 点位排序
     */
    private String pointSortId;

    /**
     * 显示类型
     */
    private List<String> evaluationType;

    /**
     * 监测计划ids
     */
    private List<String> propertyIds;

    /**
     * 是否按照分析项目排序导出
     */
    private Boolean isSortExport;

    /**
     * 样品id列表
     */
    private List<String> sampleIds;

    /**
     * 是否分析项目合并
     */
    private Boolean isItemCombine = false;
}
