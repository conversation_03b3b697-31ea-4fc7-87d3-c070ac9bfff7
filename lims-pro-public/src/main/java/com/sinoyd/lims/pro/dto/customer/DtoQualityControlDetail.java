package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 质控信息
 * <AUTHOR>
 * @version V1.0.0 2020/1/14
 * @since V100R001
 */
@Data
public class DtoQualityControlDetail {
    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 检测类型id
     */
    public String sampleTypeId;

    /**
     * 检测类型名称
     */
    public String sampleTypeName;

    /**
     * 分析项目
     */
    public String redAnalyzeItemName;

    /**
     * 分析日期
     */
    private String analyseDate;
    /**
     * 分析样品数
     */
    public Integer allSample = 0;

    /**
     * 空白样检查数
     */
    public Integer kbCtrlNum = 0;

    /**
     * 空白样检查率
     */
    public String kbCtrlRate = "0.0%";

    /**
     * 空白样合格数
     */
    public Integer kbPassNum = 0;

    /**
     * 空白样合格率
     */
    public String kbPassRate = "0.0%";

    /**
     * 全程序空白检查数
     */
    public Integer qkbCtrlNum = 0;

    /**
     * 全程序空白检查率
     */
    public String qkbCtrlRate = "0.0%";

    /**
     * 全程序空白合格数
     */
    public Integer qkbPassNum = 0;

    /**
     * 全程序空白合格率
     */
    public String qkbPassRate = "0.0%";

    /**
     * 现场平行样检查数
     */
    public Integer pxCtrlNum = 0;

    /**
     * 现场平行样检查率
     */
    public String pxCtrlRate = "0.0%";

    /**
     * 现场平行样合格数
     */
    public Integer pxPassNum = 0;

    /**
     * 现场平行样合格率
     */
    public String pxPassRate = "0.0%";

    /**
     * 室内平行样检查数
     */
    public Integer ipxCtrlNum = 0;

    /**
     * 室内平行样检查率
     */
    public String ipxCtrlRate = "0.0%";

    /**
     * 室内平行样合格数
     */
    public Integer ipxPassNum = 0;

    /**
     * 室内平行样合格率
     */
    public String ipxPassRate = "0.0%";

    /**
     * 密码平行样检查数
     */
    public Integer mpxCtrlNum = 0;

    /**
     * 密码平行样检查率
     */
    public String mpxCtrlRate = "0.0%";

    /**
     * 密码平行样合格数
     */
    public Integer mpxPassNum = 0;

    /**
     * 密码平行样合格率
     */
    public String mpxPassRate = "0.0%";

    /**
     * 加标样检查数
     */
    public Integer jbCtrlNum = 0;

    /**
     * 加标样检查率
     */
    public String jbCtrlRate = "0.0%";

    /**
     * 加标样合格数
     */
    public Integer jbPassNum = 0;

    /**
     * 加标样合格率
     */
    public String jbPassRate = "0.0%";

    /**
     * 标样检查数
     */
    public Integer byCtrlNum = 0;

    /**
     * 标样检查率
     */
    public String byCtrlRate = "0.0%";

    /**
     * 标样合格数
     */
    public Integer byPassNum = 0;

    /**
     * 标样合格率
     */
    public String byPassRate = "0.0%";

    /**
     * 密码样检查数
     */
    public Integer mmCtrlNum = 0;

    /**
     * 密码样检查率
     */
    public String mmCtrlRate = "0.0%";

    /**
     * 密码样合格数
     */
    public Integer mmPassNum = 0;

    /**
     * 密码样合格率
     */
    public String mmPassRate = "0.0%";

    /**
     * 曲线校核样检查数
     */
    public Integer jhCtrlNum = 0;

    /**
     * 曲线校核样检查率
     */
    public String jhCtrlRate = "0.0%";

    /**
     * 曲线校核样合格数
     */
    public Integer jhPassNum = 0;

    /**
     * 曲线校核样合格率
     */
    public String jhPassRate = "0.0%";

    /**
     * 总检查数
     */
    public Integer allCtrlNum = 0;

    /**
     * 总合格数
     */
    public Integer allPassNum = 0;

    /**
     * 总检查率
     */
    public String allCtrlRate = "0.0%";

    /**
     * 总合格率
     */
    public String allPassRate = "0.0%";

    /**
     * 方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 标准编号
     */
    private String redCountryStandard;

    /**
     * 项目id
     */
    public String projectId;

    public void setKbCtrlNum(Integer kbCtrlNum) {
        this.kbCtrlNum = kbCtrlNum;
        if (this.allSample > 0) {
            this.kbCtrlRate = String.format("%.1f", (100.0 * this.kbCtrlNum) / this.allSample) + "%";
        }
    }

    public void setQkbCtrlNum(Integer qkbCtrlNum) {
        this.qkbCtrlNum = qkbCtrlNum;
        if (this.allSample > 0) {
            this.qkbCtrlRate = String.format("%.1f", (100.0 * this.qkbCtrlNum) / this.allSample) + "%";
        }
    }

    public void setPxCtrlNum(Integer pxCtrlNum) {
        this.pxCtrlNum = pxCtrlNum;
        if (this.allSample > 0) {
            this.pxCtrlRate = String.format("%.1f", (100.0 * this.pxCtrlNum) / this.allSample) + "%";
        }
        if (this.pxCtrlNum > 0 && this.pxPassNum > 0) {
            this.setPxPassNum(this.pxPassNum);
        }
    }

    public void setPxPassNum(Integer pxPassNum) {
        this.pxPassNum = pxPassNum;
        if (this.pxCtrlNum > 0) {
            this.pxPassRate = String.format("%.1f", (100.0 * this.pxPassNum) / this.pxCtrlNum) + "%";
        }
    }

    public void setMpxCtrlNum(Integer mpxCtrlNum) {
        this.mpxCtrlNum = mpxCtrlNum;
        if (this.allSample > 0) {
            this.mpxCtrlRate = String.format("%.1f", (100.0 * this.mpxCtrlNum) / this.allSample) + "%";
        }
        if (this.mpxCtrlNum > 0 && this.mpxPassNum > 0) {
            this.setMpxPassNum(this.mpxPassNum);
        }
    }

    public void setMpxPassNum(Integer mpxPassNum) {
        this.mpxPassNum = mpxPassNum;
        if (this.mpxCtrlNum > 0) {
            this.mpxPassRate = String.format("%.1f", (100.0 * this.mpxPassNum) / this.mpxCtrlNum) + "%";
        }
    }

    public void setIpxCtrlNum(Integer ipxCtrlNum) {
        this.ipxCtrlNum = ipxCtrlNum;
        if (this.allSample > 0) {
            this.ipxCtrlRate = String.format("%.1f", (100.0 * this.ipxCtrlNum) / this.allSample) + "%";
        }
        if (this.ipxCtrlNum > 0 && this.ipxPassNum > 0) {
            this.setIpxPassNum(this.ipxPassNum);
        }
    }

    public void setJhCtrlNum(Integer jhCtrlNum) {
        this.jhCtrlNum = jhCtrlNum;
        if (this.allSample > 0) {
            this.jhCtrlRate = String.format("%.1f", (100.0 * this.jhCtrlNum) / this.allSample) + "%";
        }
        if (this.jhCtrlNum > 0 && this.jhPassNum > 0) {
            this.setJhPassNum(this.jhPassNum);
        }
    }

    public void setIpxPassNum(Integer ipxPassNum) {
        this.ipxPassNum = ipxPassNum;
        if (this.ipxCtrlNum > 0) {
            this.ipxPassRate = String.format("%.1f", (100.0 * this.ipxPassNum) / this.ipxCtrlNum) + "%";
        }
    }

    public void setJhPassNum(Integer jhPassNum) {
        this.jhPassNum = jhPassNum;
        if (this.jhCtrlNum > 0) {
            this.jhPassRate = String.format("%.1f", (100.0 * this.jhPassNum) / this.jhCtrlNum) + "%";
        }
    }

    public void setJBCtrlNum(Integer jbCtrlNum) {
        this.jbCtrlNum = jbCtrlNum;
        if (this.allSample > 0) {
            this.jbCtrlRate = String.format("%.1f", (100.0 * this.jbCtrlNum) / this.allSample) + "%";
        }
        if (this.jbCtrlNum > 0 && this.jbPassNum > 0) {
            this.setJBPassNum(this.jbPassNum);
        }
    }

    public void setJBPassNum(Integer jbPassNum) {
        this.jbPassNum = jbPassNum;
        if (this.jbCtrlNum > 0) {
            this.jbPassRate = String.format("%.1f", (100.0 * this.jbPassNum) / this.jbCtrlNum) + "%";
        }
    }

    public void setKbPassNum(Integer kbPassNum) {
        this.kbPassNum = kbPassNum;
        if (this.kbCtrlNum > 0) {
            this.kbPassRate = String.format("%.1f", (100.0 * this.kbPassNum) / this.kbCtrlNum) + "%";
        }
    }

    public void setQkbPassNum(Integer qkbPassNum) {
        this.qkbPassNum = qkbPassNum;
        if (this.qkbCtrlNum > 0) {
            this.qkbPassRate = String.format("%.1f", (100.0 * this.qkbPassNum) / this.qkbCtrlNum) + "%";
        }
    }

    public void setByPassNum(Integer byPassNum) {
        this.byPassNum = byPassNum;
        if (this.byCtrlNum > 0) {
            this.byPassRate = String.format("%.1f", (100.0 * this.byPassNum) / this.byCtrlNum) + "%";
        }
    }

    public void setBYCtrlNum(Integer byCtrlNum) {
        this.byCtrlNum = byCtrlNum;
        if (this.allSample > 0) {
            this.byCtrlRate = String.format("%.1f", (100.0 * this.byCtrlNum) / this.allSample) + "%";
        }
    }

    public void setMmCtrlNum(Integer mmCtrlNum) {
        this.mmCtrlNum = mmCtrlNum;
        if (this.allSample > 0) {
            this.mmCtrlRate = String.format("%.1f", (100.0 * this.mmCtrlNum) / this.allSample) + "%";
        }
    }

    public void setMmPassNum(Integer mmPassNum) {
        this.mmPassNum = mmPassNum;
        if (this.mmCtrlNum > 0) {
            this.mmPassRate = String.format("%.1f", (100.0 * this.mmPassNum) / this.mmCtrlNum) + "%";
        }
    }

    public void setAllCtrlNum(Integer allCtrlNum) {
        this.allCtrlNum = allCtrlNum;
        if (this.allSample > 0) {
            this.allCtrlRate = String.format("%.1f", (100.0 * this.allCtrlNum) / this.allSample) + "%";
        }
        if (this.allCtrlNum > 0 && this.allPassNum > 0) {
            this.setAllPassNum(this.allPassNum);
        }
    }

    public void setAllPassNum(Integer allPassNum) {
        this.allPassNum = allPassNum;
        if (this.allCtrlNum > 0) {
            this.allPassRate = String.format("%.1f", (100.0 * this.allPassNum) / this.allCtrlNum) + "%";
        }
    }
}
