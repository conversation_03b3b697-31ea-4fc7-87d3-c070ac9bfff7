package com.sinoyd.lims.api.dto.customer;

import com.sinoyd.lims.pro.dto.customer.DtoDataSourcePhone;
import lombok.Data;

import java.util.List;

@Data
public class DtoParamsDataApiPhone {

    private String paramsConfigId;
    private String paramsName;
    private String sampleId;
    private String paramsValue;
    private Integer defaultControl;
    private List<DtoDataSourcePhone> dataSource;
    private String dimension;
    private String groupId;

    /**
     * 参数类型（枚举EnumParamsType：1.公共参数、2.样品参数、3.分析项目参数、4.点位参数）
     */
    private Integer paramsType;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 参考文本
     */
    private String referenceText;
}
