package com.sinoyd.lims.lim.ocr;

import com.sinoyd.lims.lim.service.ocr.RecognizeStrategy;

import java.util.List;

public class FullTextAfterParamInOneLine implements RecognizeStrategy {
    @Override
    public String recognize(List<String> data, String paramName) {
        String textWithParam = data.stream().filter(d->d.indexOf(paramName)!=-1).findFirst().orElse(null);
        if(textWithParam!=null){
            return textWithParam.substring(textWithParam.indexOf(paramName)).trim().replace(":","").replace("：","");
        }
        return "";
    }
}
