package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.QualityManageCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoQualityManage;
import com.sinoyd.lims.pro.dto.customer.DtoQMInstrumentChange;
import com.sinoyd.lims.pro.dto.customer.DtoQMProjectEvaluation;
import com.sinoyd.lims.pro.service.QualityManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * QualityManage服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: QualityManage服务")
 @RestController
 @RequestMapping("api/pro/qualityManage")
 public class QualityManageController extends BaseJpaController<DtoQualityManage, String,QualityManageService> {


    /**
     * 分页动态条件查询QualityManage
     *
     * @param qualityManageCriteria 条件参数
     * @return RestResponse<List < QualityManage>>
     */
    @ApiOperation(value = "分页动态条件查询QualityManage", notes = "分页动态条件查询QualityManage")
    @GetMapping
    public RestResponse<List<DtoQualityManage>> findByPage(QualityManageCriteria qualityManageCriteria) {
        PageBean<DtoQualityManage> pageBean = super.getPageBean();
        RestResponse<List<DtoQualityManage>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, qualityManageCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询QualityManage
     *
     * @param id 主键id
     * @return RestResponse<DtoQualityManage>
     */
    @ApiOperation(value = "按主键查询QualityManage", notes = "按主键查询QualityManage")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoQualityManage> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoQualityManage> restResponse = new RestResponse<>();
        DtoQualityManage qualityManage = service.findOne(id);
        restResponse.setData(qualityManage);
        restResponse.setRestStatus(StringUtil.isNull(qualityManage) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增QualityManage
     *
     * @param qualityManage 实体列表
     * @return RestResponse<DtoQualityManage>
     */
    @ApiOperation(value = "新增QualityManage", notes = "新增QualityManage")
    @PostMapping
    public RestResponse<DtoQualityManage> create(@RequestBody @Validated DtoQualityManage qualityManage) {
        RestResponse<DtoQualityManage> restResponse = new RestResponse<>();
        restResponse.setData(service.save(qualityManage));
        return restResponse;
    }

    /**
     * 新增QualityManage
     *
     * @param qualityManageList 实体列表
     * @return RestResponse<DtoQualityManage>
     */
    @ApiOperation(value = "新增QualityManage", notes = "新增QualityManage")
    @PostMapping("/batch")
    public RestResponse<List<DtoQualityManage>> batchCreate(@RequestBody List<DtoQualityManage> qualityManageList) {
        RestResponse<List<DtoQualityManage>> restResponse = new RestResponse<>();
        restResponse.setData(service.batchCreate(qualityManageList));
        return restResponse;
    }

    /**
     * 新增QualityManage
     *
     * @param qualityManageList 实体列表
     * @return RestResponse<DtoQualityManage>
     */
    @ApiOperation(value = "新增QualityManage", notes = "新增QualityManage")
    @PostMapping("/checkBatch")
    public RestResponse<List<DtoQualityManage>> checkBatchCreate(@RequestBody List<DtoQualityManage> qualityManageList) {
        RestResponse<List<DtoQualityManage>> restResponse = new RestResponse<>();
        restResponse.setData(service.checkBatchCreate(qualityManageList));
        return restResponse;
    }

    /**
     * 新增外部质控信息
     *
     * @param dto 实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "新增外部质控信息", notes = "新增外部质控信息")
    @PostMapping(path = "/outside")
    public RestResponse<Boolean> saveOutsideSample(@RequestBody DtoAnalyseData dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.saveOutside(dto.getSampleId(), dto.getTestIds());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 修改QualityManage
     *
     * @param qualityManage 实体列表
     * @return RestResponse<DtoQualityManage>
     */
    @ApiOperation(value = "修改QualityManage", notes = "修改QualityManage")
    @PutMapping
    public RestResponse<DtoQualityManage> update(@RequestBody @Validated DtoQualityManage qualityManage) {
        RestResponse<DtoQualityManage> restResponse = new RestResponse<>();
        restResponse.setData(service.update(qualityManage));
        return restResponse;
    }

    /**
     * 批量设置仪器
     *
     * @param dto 实体
     * @return RestResponse<DtoQualityManage>
     */
    @ApiOperation(value = "批量设置仪器", notes = "批量设置仪器")
    @PutMapping("/instrument")
    public RestResponse<Boolean> updateBatch(@RequestBody DtoQMInstrumentChange dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.updateInstrument(dto.getInstrumentId(), dto.getIds());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * "根据id批量删除QualityManage
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除QualityManage", notes = "根据id批量删除QualityManage")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 查询项目评价信息
     *
     * @param projectId 项目id
     * @return RestResponse<DtoQualityManage>
     */
    @ApiOperation(value = "按主键查询QualityManage", notes = "按主键查询QualityManage")
    @GetMapping(path = "/evaluation")
    public RestResponse<List<DtoQMProjectEvaluation>> findProjectEvaluation(@RequestParam(name = "projectId") String projectId) {
        RestResponse<List<DtoQMProjectEvaluation>> restResponse = new RestResponse<>();
        List<DtoQMProjectEvaluation> evaluationList = service.findProjectEvaluation(projectId);
        restResponse.setData(evaluationList);
        restResponse.setRestStatus(evaluationList.size() > 0 ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }
}