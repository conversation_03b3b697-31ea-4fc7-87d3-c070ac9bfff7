-- -------------------------------------------
-- 常量管理中添加 排污许可证检测类型名称匹配 常量
-- -------------------------------------------
DELETE
FROM t_sys_dict_type_project
WHERE id = '24b1f9dc30944465b98d8f390626e27e';
INSERT INTO t_sys_dict_type_project(id, dictName, dictType, enabled, sortNum, note,
                                    createDate, createUserGuid, createUserName, updateDate,
                                    updateUserGuid, updateUserName, orgGuid, deptGuid, deleted)
VALUES ('24b1f9dc30944465b98d8f390626e27e', '排污许可证检测类型名称匹配', 'LIM_PollutantDischargePermitTypeMatch', 1, 0,
        '用于匹配排污许可证开放平台与本系统的检测类型名称的对应关系\nps：解决各系统的检测类型名称不同的问题导致对应不上的问题\n例如：\nA系统有组织废气为【有组织废气】\nB系统有组织废气为【废气（有组织）】',
        '2025-04-24 14:44:18', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2025-04-24 14:48:13',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', NULL, 0);

-- -------------------------------------------
-- 排污许可证检测类型名称匹配 常量中添加废气、废水对应关系
-- -------------------------------------------
DELETE
FROM t_sys_dict_data_project
WHERE id = 'f1bb139607574281a1827af0517a7472';
INSERT INTO t_sys_dict_data_project(id, dictCode, dictName, dictValue, dictType, parentGuid,
                                    cssStyle, cssClass, enabled, sortNum, note, createDate,
                                    createUserGuid, createUserName, updateDate, updateUserGuid,
                                    updateUserName, orgGuid, deptGuid, deleted, extendS1,
                                    extendS2, extendS3, extendS4, extendI1, extendI2,
                                    extendI3, extendI4, extendF1, extendF2, extendF3,
                                    extendF4, extendD1, extendD2, extendD3, extendD4)
VALUES ('f1bb139607574281a1827af0517a7472', 'OrganizedWasteGas', '废气', '1', 'LIM_PollutantDischargePermitTypeMatch',
        '0', '', '', 1, 0, '字典名称为排污许可证开放平台检测类型名称\n扩展String1为本系统检测类型名称', '2025-04-24 14:50:23',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '2025-04-24 15:38:35', '59141356591b48e18e139aa54d9dd351', '超级管理员',
        '5f7bcf90feb545968424b0a872863876', NULL, 0, '废气（有组织）', '', '', '', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL);

DELETE
FROM t_sys_dict_data_project
WHERE id = '20ad1824c2d543a5a7425f2cf1591b2d';
INSERT INTO t_sys_dict_data_project(id, dictCode, dictName, dictValue, dictType, parentGuid,
                                    cssStyle, cssClass, enabled, sortNum, note, createDate,
                                    createUserGuid, createUserName, updateDate, updateUserGuid,
                                    updateUserName, orgGuid, deptGuid, deleted, extendS1,
                                    extendS2, extendS3, extendS4, extendI1, extendI2,
                                    extendI3, extendI4, extendF1, extendF2, extendF3,
                                    extendF4, extendD1, extendD2, extendD3, extendD4)
VALUES ('20ad1824c2d543a5a7425f2cf1591b2d', 'WasteWater', '废水', '0', 'LIM_PollutantDischargePermitTypeMatch', '0', '',
        '', 1, 0, '字典名称为排污许可证开放平台检测类型名称\n扩展String1为本系统检测类型名称', '2025-04-24 14:49:23',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '2025-04-24 14:50:47', '59141356591b48e18e139aa54d9dd351', '超级管理员',
        '5f7bcf90feb545968424b0a872863876', NULL, 0, '废水', '', '', '', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL);
