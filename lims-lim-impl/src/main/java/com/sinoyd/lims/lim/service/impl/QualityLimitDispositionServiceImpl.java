package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoQualityLimitDisposition;
import com.sinoyd.base.entity.QualityLimitDisposition;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityLimitDispositionRepository;
import com.sinoyd.lims.lim.service.QualityLimitDispositionService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * QualityLimitDisposition操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/5/27
 * @since V100R001
 */
 @Service
public class QualityLimitDispositionServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoQualityLimitDisposition,String, QualityLimitDispositionRepository> implements QualityLimitDispositionService {

    private QualityControlLimitRepository qualityControlLimitRepository;

    private TestService testService;

    @Override
    public void findByPage(PageBean<DtoQualityLimitDisposition> pb, BaseCriteria qualityLimitDispositionCriteria) {
        pb.setEntityName("DtoQualityLimitDisposition a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, qualityLimitDispositionCriteria);
        fullDispiositionVo(pb.getData());
    }

    @Override
    @Transactional
    public DtoQualityLimitDisposition save(DtoQualityLimitDisposition qualityLimitDisposition) {
        //进行唯一性判定
        DtoQualityLimitDisposition disposition = repository.findByQcGradeAndQcTypeAndJudgingMethodAndFormula(qualityLimitDisposition.getQcGrade(),
                qualityLimitDisposition.getQcType(), qualityLimitDisposition.getJudgingMethod(),
                qualityLimitDisposition.getFormula());
        if (StringUtil.isNotNull(disposition)) {
            throw new BaseException("所添加类型已存在，请确认后再操作！");
        }
        return super.save(qualityLimitDisposition);
    }

    /**
     * 配置设置是否默认
     *
     * @param disposition 配置信息
     * @return 是否
     */
    @Override
    public Boolean inspectDisposition(DtoQualityLimitDisposition disposition) {
        Boolean pass = Boolean.TRUE;
        List<DtoQualityLimitDisposition> dispositionList = repository.findByQcGradeAndQcTypeAndJudgingMethodAndIsAcquiesce(
                disposition.getQcGrade(), disposition.getQcType(), disposition.getJudgingMethod(), Boolean.TRUE);
        if (dispositionList.size() > 0) {
            pass = Boolean.FALSE;
        }
        return pass;
    }

    /**
     * 根据配置Id获取测试项目集合
     *
     * @param dispositionId 配置Id
     * @return 测试项目集合
     */
    @Override
    public List<DtoTest> findTestListByDispositionId(String dispositionId) {
        DtoQualityLimitDisposition disposition = repository.findOne(dispositionId);
        List<DtoTest> testList = new ArrayList<>();
        if (!disposition.getIsAcquiesce()) {
            Set<String> testIds = qualityControlLimitRepository.findByDispositionIdIn(Collections.singletonList(dispositionId))
                    .stream().map(DtoQualityControlLimit::getTestId).collect(Collectors.toSet());
            if (testIds.size() > 0) {
                testList = testService.findAll(testIds);
            }
        }
        return testList;
    }

    /**
     * 删除配置id
     *
     * @param ids 配置ids
     * @return 删除数
     */
    @Override
    @Transactional
    public Integer deleteByIds(Collection<String> ids) {
        List<DtoQualityLimitDisposition> dispositionList = repository.findAll(ids);
        //找到对应默认配置
        List<DtoQualityLimitDisposition> defaultList = dispositionList.stream()
                .filter(QualityLimitDisposition::getIsAcquiesce).collect(Collectors.toList());
        Set<Integer> qcGrades = defaultList.stream().map(DtoQualityLimitDisposition::getQcGrade).collect(Collectors.toSet());
        Set<Integer> qcTypes = defaultList.stream().map(DtoQualityLimitDisposition::getQcType).collect(Collectors.toSet());
        Set<Integer> methods = defaultList.stream().map(DtoQualityLimitDisposition::getJudgingMethod).collect(Collectors.toSet());
        List<DtoQualityControlLimit> controlLimitList = qualityControlLimitRepository
                .findByQcGradeInAndQcTypeInAndJudgingMethodIn(qcGrades, qcTypes, methods);
        List<DtoQualityLimitDisposition> qualityLimitDispositionList = repository
                .findByQcGradeInAndQcTypeInAndJudgingMethodInAndIsAcquiesce(qcGrades, qcTypes, methods, Boolean.TRUE);
        //排除需要删除的配置id
        List<DtoQualityLimitDisposition> finalQualityLimitDispositionList = qualityLimitDispositionList.stream()
                .filter(p -> !ids.contains(p.getId())).collect(Collectors.toList());
        List<DtoQualityControlLimit> limitList = qualityControlLimitRepository.findByDispositionIdIn(ids);
        //设置配置为空
        limitList.forEach(p -> {
            p.setDispositionId(UUIDHelper.GUID_EMPTY);
            String formulaString = finalQualityLimitDispositionList.stream().filter(d -> p.getQcGrade().equals(d.getQcGrade())
                    && p.getQcType().equals(d.getQcType()) && p.getJudgingMethod().equals(d.getJudgingMethod()))
                    .map(DtoQualityLimitDisposition::getFormula).findFirst().orElse("");
            p.setFormula(formulaString);
        });
        qualityControlLimitRepository.batchUpdate(limitList);
        controlLimitList.forEach(p -> {
            //删除的默认公式
            String defaultFormula = defaultList.stream().filter(d -> p.getQcGrade().equals(d.getQcGrade())
                    && p.getQcType().equals(d.getQcType()) && p.getJudgingMethod().equals(d.getJudgingMethod()))
                    .map(DtoQualityLimitDisposition::getFormula).findFirst().orElse("");
            //现在的默认公式
            String formulaString = finalQualityLimitDispositionList.stream().filter(d -> p.getQcGrade().equals(d.getQcGrade())
                    && p.getQcType().equals(d.getQcType()) && p.getJudgingMethod().equals(d.getJudgingMethod()))
                    .map(DtoQualityLimitDisposition::getFormula).findFirst().orElse("");
            if (StringUtil.isNotEmpty(defaultFormula)) {
                if (p.getFormula().equals(defaultFormula)) {
                    p.setDispositionId(UUIDHelper.GUID_EMPTY);
                    p.setFormula(formulaString);
                }
            }
        });
        //清空默认值
        qualityControlLimitRepository.batchUpdate(controlLimitList);
        return super.logicDeleteById(ids);
    }

    /**
     * 列表赋值
     *
     * @param dispositionList 配置集合
     */
    private void fullDispiositionVo(List<DtoQualityLimitDisposition> dispositionList) {
        List<String> dispositionIds = dispositionList.stream().map(DtoQualityLimitDisposition::getId).collect(Collectors.toList());
        List<DtoQualityControlLimit> limitList = qualityControlLimitRepository.findByDispositionIdIn(dispositionIds);
        dispositionList.forEach(p -> {
            if (!p.getIsAcquiesce()) {
                Set<String> testList = limitList.stream().filter(d -> d.getDispositionId().equals(p.getId()))
                        .map(DtoQualityControlLimit::getTestId).collect(Collectors.toSet());
                p.setTestCount(testList.size());
            }
        });
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }
}