package com.sinoyd.lims.lim.service;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 下载模板
 * <AUTHOR>
 * @version V1.0.0 2022/9/13
 * @since V100R001
 */
public interface DownLoadInsCheckRecordTemplateService {
    /**
     * 下载模板
     *
     * @param response      响应流
     * @param sheetNames    需要赋值的sheet名
     * @param fileName      文件名
     * @param instrumentId   仪器Id
     */
    void downLoadExcel(HttpServletResponse response, Map<String,String> sheetNames,String fileName,String instrumentId);
}
