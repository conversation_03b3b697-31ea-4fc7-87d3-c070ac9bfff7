package com.sinoyd.lims.lim.utils;

import com.sinoyd.SpringContextAware;
import com.sinoyd.lims.lim.service.ocr.RecognizeStrategy;
import dm.jdbc.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * ocr识别工具类
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/6
 * @since V100R001
 */
@Component
@Slf4j
public class OcrUtil {

    /**
     * 调用选定策略解析数据
     * @param strategyValue  策略类全路径
     * @param data      原始数据
     * @param paramName 参数名称
     * @return          解析结果
     */
    public static String recognize(String strategyValue,List<String> data,String paramName){
        RecognizeStrategy strategy = null;
        try{
            Class checkClass = Class.forName(strategyValue);//用描述作为包的命名
            strategy = SpringContextAware.getBean(checkClass);
        }
        catch (Exception ex){
            log.error(ex.getMessage(), ex);
        }
        return strategy==null?"":strategy.recognize(data,paramName);
    }

    /**
     * 图片转base64
     * @param imgPath 相对路径
     * @return        base64
     */
    public static String ImageToBase64(String imgPath) {
        byte[] data = null;
        InputStream in = null;
        try {
            in = new FileInputStream(imgPath);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }finally {
            FileUtil.close(in);
        }
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(Objects.requireNonNull(data));
    }
}
