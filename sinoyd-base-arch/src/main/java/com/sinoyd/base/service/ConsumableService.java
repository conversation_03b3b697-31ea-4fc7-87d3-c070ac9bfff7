package com.sinoyd.base.service;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.customer.DtoStandardTemp;
import com.sinoyd.frame.service.IBaseJpaService;


/**
 * 消耗品管理
 * <AUTHOR>
 * @version v1.0.0 2019/3/5
 * @since V100R001
 */
public interface ConsumableService extends IBaseJpaService<DtoConsumable, String> {

    /**
     * 根据rowGuid获取单个消耗品
     * <p>
     * 过滤假删
     * 
     * @param rowGuid
     * @return 标样实体（基本信息+详单）
     */
    DtoStandardTemp getStandardById(String rowGuid);

    /**
     * 新增标准样品
     * 
     * @param consumable 标准样品信息
     * @return 新增的带唯一标识<i>id</i>的标准样品实体（基本信息+详单）
     */
    DtoConsumable createStandard(DtoConsumable consumable);

    /**
     * 更新标准样品 TODO 标准样品的实体需要重新定义，然后获取的方法也要补充进去
     * 
     * @param consumable 消耗品信息
     * @return 新增的带唯一标识<i>id</i>的标准样品实体（基本信息+详单）
     */
    DtoConsumable updateStandard(DtoConsumable consumable);


    /**
     * 重新定义接口（为了返回调用该接口类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回消耗品对象
     */
    @Override
    DtoConsumable findOne(String id);

    /**
     * 复制消耗品标样
     *
     * @param id     id
     * @param times  复制次数
     */
    void copy(String id, Integer times);

    /**
     * 根据标样编号完全匹配获取标样
     *
     * @param dtoConsumable 标样编号
     * @return 标样信息
     */
    DtoConsumable byConsumableCode(DtoConsumable dtoConsumable);
}