package com.sinoyd.lims.probase.service;

import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;

public interface CheckRecRecordFactoryService {

    /**
     * 不同项目核对送样单状态
     *
     * @param type 包名
     * @param id   送样单id
     */
    void checkRecRecordByType(String type, String id);

    /**
     * 不同项目核对送样单状态
     *
     * @param type                包名
     * @param receiveSampleRecord 送样单信息
     */
    void checkRecRecordByType(String type, DtoReceiveSampleRecord receiveSampleRecord);
}
