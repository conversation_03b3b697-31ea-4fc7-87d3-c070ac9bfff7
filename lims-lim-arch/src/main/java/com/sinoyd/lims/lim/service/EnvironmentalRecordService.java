package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;
import com.sinoyd.lims.lim.dto.lims.DtoTest;

import java.util.List;

/**
 * 环境管理配置
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface EnvironmentalRecordService extends IBaseJpaService<DtoEnvironmentalRecord, String> {
    /**
     * 获取与指定环境记录相冲突的仪器使用记录
     *
     * @param record 环境记录
     */
    List<DtoInstrumentUseRecord> getConflictRecords(DtoEnvironmentalRecord record);

    /**
     * 获取环境记录
     *
     * @param id 环境记录id
     * @return 环境记录
     */
    DtoEnvironmentalRecord findRecord(String id);

    /**
     * 行编辑保存现场仪器使用记录/批量设置
     *
     * @param environmentalRecord 仪器使用记录
     * @return RestResponse<DtoEnvironmentalRecord> 响应
     */
    DtoEnvironmentalRecord newBatchSave(DtoEnvironmentalRecord environmentalRecord);

    /**
     * 新增采样仪器使用记录
     *
     * @param testList 测试项目列表
     * @return 记录列表
     */
    List<DtoEnvironmentalRecord> addByLocalTests(String subId, List<DtoTest> testList);

    /**
     * 现场仪器使用记录复制
     *
     * @param environmentalRecord 仪器使用记录
     * @return 仪器使用记录
     */
    DtoEnvironmentalRecord copy(DtoEnvironmentalRecord environmentalRecord);
}