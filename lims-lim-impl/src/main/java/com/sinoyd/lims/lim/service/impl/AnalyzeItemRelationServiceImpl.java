package com.sinoyd.lims.lim.service.impl;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelation;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelationParams;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeItemRelationParamsRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeItemRelationRepository;
import com.sinoyd.lims.lim.service.AnalyzeItemRelationParamsService;
import com.sinoyd.lims.lim.service.AnalyzeItemRelationService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 分析项目关系接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-08
 * @since V100R001
 */
@Service
public class AnalyzeItemRelationServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoAnalyzeItemRelation, String, AnalyzeItemRelationRepository>
        implements AnalyzeItemRelationService {

    @Autowired
    @Lazy
    private AnalyzeItemRelationParamsService analyzeItemRelationParamsService;

    @Autowired
    private AnalyzeItemRelationParamsRepository analyzeItemRelationParamsRepository;

    @Autowired
    @Lazy
    private AnalyzeItemService analyzeItemService;

    /**
     * 新增分析项目关系
     */
    @Override
    @Transactional
    public DtoAnalyzeItemRelation save(DtoAnalyzeItemRelation entity) {
        if (entity.getType().equals(EnumLIM.EnumAnalyzeItemRelationType.上报.getValue())){
            if (repository.getByAnalyzeItemId(entity.getAnalyzeItemId()) > 0) {
                throw new BaseException("该分析项目已经存在分析项目关系");
            }
        }
        return super.save(entity);
    }

    /**
     * 编辑分析项目关系
     */
    @Transactional
    @Override
    public DtoAnalyzeItemRelation update(DtoAnalyzeItemRelation entity) {
        if (entity.getType().equals(EnumLIM.EnumAnalyzeItemRelationType.上报.getValue())) {
            if (repository.getByAnalyzeItemIdAndNotId(entity.getAnalyzeItemId(), entity.getId()) > 0) {
                throw new BaseException("该分析项目已经存在分析项目关系");
            }
        }
        return super.update(entity);
    }

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoAnalyzeItemRelation> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoAnalyzeItemRelation p");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select p");

        super.findByPage(page, criteria);

//        List<DtoAnalyzeItemRelation> list = page.getData();
//
//        List<String> analyzeItemIds=list.stream().map(DtoAnalyzeItemRelation::getAnalyzeItemId).collect(Collectors.toList());
//
//        List<DtoAnalyzeItem> analyzeItemList = analyzeItemService.findAll();
//
//
//        for (DtoAnalyzeItemRelation item : list) {
//
//            List<DtoAnalyzeItemRelationParams> rList = analyzeItemRelationParamsService.getList(item.getId(), "");
//            if (rList.size() > 0) {
//                List<String> ids = rList.stream().map(DtoAnalyzeItemRelationParams::getAnalyzeItemId).collect(Collectors.toList());
//
//                List<DtoAnalyzeItem> analyzeItemListByRelation = analyzeItemList.stream().filter(p -> ids.contains(p.getId())).sorted(Comparator.comparing(DtoAnalyzeItem::getAnalyzeItemName).reversed()).collect(Collectors.toList());
//                item.setAnalyzeItemIds(ids);
//                item.setAnalyzeItemList(analyzeItemListByRelation);
//            }
//
//        }
//
//        page.setData(list);// 重新封装到pageBean返回到上层
    }

    @Override
    public DtoAnalyzeItemRelation findOne(String id) {
        DtoAnalyzeItemRelation item = super.findOne(id);
        List<DtoAnalyzeItemRelationParams> rList = analyzeItemRelationParamsService.getList(item.getId(), "");
        if (rList.size() > 0) {
            List<String> analyzeItemIds = rList.stream().map(DtoAnalyzeItemRelationParams::getAnalyzeItemId).collect(Collectors.toList());
            List<DtoAnalyzeItem> analyzeItemList = analyzeItemService.findRedisByIds(analyzeItemIds);
            List<String> ids = rList.stream().map(DtoAnalyzeItemRelationParams::getAnalyzeItemId).collect(Collectors.toList());

            List<DtoAnalyzeItem> analyzeItemListByRelation = analyzeItemList.stream().filter(p -> ids.contains(p.getId())).sorted(Comparator.comparing(DtoAnalyzeItem::getAnalyzeItemName).reversed()).collect(Collectors.toList());
            item.setAnalyzeItemIds(ids);
            item.setAnalyzeItemList(analyzeItemListByRelation);
        }
        return item;
    }

    /**
     * 单个删除(级联删除)
     */
    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        List<String> idList = new ArrayList<>();
        idList.add((String) id);
        return logicDeleteById(idList);
    }

    /**
     * 批量删除(级联删除？)
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        List<DtoAnalyzeItemRelationParams> params = analyzeItemRelationParamsRepository.getListByRelationIds(idList);

        if (StringUtil.isNotNull(params) && params.size() > 0) {
            List<String> pIds = new ArrayList<>();
            for (DtoAnalyzeItemRelationParams param : params) {
                pIds.add(param.getId());
            }
            // 删除关系参数
            analyzeItemRelationParamsService.logicDeleteById(ids);
        }
        return super.logicDeleteById(idList);
    }


    @Override
    public List<DtoAnalyzeItemRelation> findByAnalyzeItemIdAndType(String analyzeItemId, Integer type) {
        return repository.findByAnalyzeItemIdAndType(analyzeItemId, type);
    }
}