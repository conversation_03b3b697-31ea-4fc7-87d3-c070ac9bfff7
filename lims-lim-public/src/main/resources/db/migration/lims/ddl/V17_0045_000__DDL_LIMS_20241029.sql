-- 删除采样方法表
DROP TABLE IF EXISTS TB_LIM_SamplingMethod;

-- 人员能力新增字段
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN sampleTypeId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型id';
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN samplingMethodId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '采样方法id';
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN abilityType varchar(100) NOT NULL DEFAULT '' COMMENT '证书类型';
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN redAnalyzeItemName varchar(255) DEFAULT NULL COMMENT '项目名称';
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN redAnalyzeMethodName varchar(255) DEFAULT NULL COMMENT '方法标准名称';
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN redCountryStandard varchar(200) DEFAULT NULL COMMENT '标准编号';


