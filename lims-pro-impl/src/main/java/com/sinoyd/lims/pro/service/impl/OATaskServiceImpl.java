package com.sinoyd.lims.pro.service.impl;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoConsumableLog;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.ConsumableLogRepository;
import com.sinoyd.base.service.ConsumableLogService;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.workflow.activiti.service.IActProcessService;
import com.sinoyd.boot.workflow.activiti.service.IActTaskService;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.pro.criteria.OATaskCriteria;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskAttach;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskHandle;
import com.sinoyd.lims.pro.dto.DtoOATaskHandleLog;
import com.sinoyd.lims.pro.dto.DtoOATaskQuery;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskQueryType;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskStatus;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.repository.OATaskRelationRepository;
import com.sinoyd.lims.pro.repository.OATaskRepository;
import com.sinoyd.lims.pro.service.*;

import com.sinoyd.lims.pro.service.OAContractService;
import dm.jdbc.util.FileUtil;
import org.activiti.engine.TaskService;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Attachment;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

/**
 * 审批任务信息业务操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@Service
@Slf4j
public class OATaskServiceImpl extends BaseJpaServiceImpl<DtoOATask, String, OATaskRepository>
        implements OATaskService {
    /**
     * 审批日志服务
     */
    @Autowired
    @Lazy
    private OATaskHandleLogService oaTaskHandleLogService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    protected OATaskRelationService oaTaskRelationService;

    @Autowired
    private IActProcessService actProcessService;

    @Autowired
    private IActTaskService actTaskService;

    @Autowired
    private TaskService taskService;

    @Autowired
    @Lazy
    private FileControlApplyDetailService fileControlApplyDetailService;

    @Autowired
    @Lazy
    private HomeService homeService;

    @Autowired
    @Lazy
    private OAFileRevisionService oaFileRevisionService;

    @Autowired
    @Lazy
    private OAFileControlService oaFileControlService;

    @Autowired
    @Lazy
    private OAFileAbolishService oaFileAbolishService;

    @Autowired
    @Lazy
    private OAContractService oaContractService;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    @Lazy
    private DocumentService documentService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private DepartmentService departmentService;

    @Autowired
    @Lazy
    private OAConsumablePickListsDetailService oaConsumablePickListsDetailService;

    @Autowired
    private OATaskRelationRepository oaTaskRelationRepository;

    @Autowired
    private ConsumableLogRepository consumableLogRepository;

    @Autowired
    @Lazy
    private ConsumableLogService consumableLogService;

    @Transactional
    @Override
    public DtoOATask startProcess(EnumOATaskType taskType, DtoOATaskCreate<?> taskDto, Map<String, Object> vars) {
        //如果是文件受控，需要验证自己的手动输入的文件，受控编码是否重复
        if (taskType.getCode().equals(EnumOATaskType.文件受控.getCode())) {
            DtoOAFileControl dtoOAFileControl = (DtoOAFileControl) taskDto.getData();
            //如果文件id是null的就要做验证
            if (StringUtil.isNull(dtoOAFileControl.getFileId()) || dtoOAFileControl.getFileId().equals(UUIDHelper.GUID_EMPTY)) {
                if (fileControlApplyDetailService.isExistControlCode(dtoOAFileControl.getControlCode())) {
                    throw new BaseException("已存在相同的受控编号！");
                }
            }
        } else if (taskType.getCode().equals(EnumOATaskType.文件修订.getCode())) {
            DtoOAFileRevision dtoOAFileRevision = (DtoOAFileRevision) taskDto.getData();
            if (fileControlApplyDetailService.isExistControlCode(dtoOAFileRevision.getControlCode(), dtoOAFileRevision.getFileId())) {
                throw new BaseException("已存在相同的受控编号！");
            }
        }

        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        // 发起人账号标识
        String sponsor = user.getLoginId();

        DtoOATask oaTask = new DtoOATask();
        oaTask.setTitle(taskDto.getTitle());
        oaTask.setDescription(taskDto.getDescription());

        String oaTaskId = oaTask.getId();
        String procDefKey = taskType.getCode();
        oaTask.setProcTypeCode(procDefKey);
        oaTask.setProcTypeId(taskDto.getProcTypeId());
        oaTask.setProcTypeName(taskDto.getProcTypeName());
        oaTask.setSponsor(sponsor);
        oaTask.setSponsorId(user.getUserId());
        oaTask.setSponsorName(user.getUserName());
        oaTask.setDeptId(user.getDeptId());
        oaTask.setSubmitTime(new Date());

        // 设置流程发起人
        actProcessService.setStartProcessUserId(sponsor);
        // 启动流程
        ProcessInstance procInst = actProcessService.startProcess(procDefKey, oaTaskId, oaTask.getTitle(), vars);

        String procInstId = procInst.getId();
        // 关联流程实例ID
        oaTask.setProcInstId(procInstId);

        // 获取发起审批任务信息
        Task sponsorTask = actTaskService.getCurrentTask(procInstId);
        // 设置工作流任务的办理人
        actTaskService.setAssignee(sponsorTask.getId(), sponsor);

        DtoOATaskHandleLog handLog = new DtoOATaskHandleLog();
        handLog.setTaskId(oaTaskId);
        handLog.setIsAgree(true);
        //意见描述，如果工作流中配置了名称，那就默认空，否则默认是发起审批
        String comment = StringUtils.isNotNullAndEmpty(sponsorTask.getName()) ? "" : "发起审批";
        handLog.setComment(comment);
        handLog.setCompleteTime(new Date());
        handLog.setAssignee(sponsor);
        handLog.setAssigneeName(user.getUserName());
        handLog.setActTaskId(sponsorTask.getId());
        handLog.setActTaskDefKey(sponsorTask.getTaskDefinitionKey());
        handLog.setActTaskName(sponsorTask.getName());
        handLog.setIsFirstStep(true);

        // 完成发起审批
        actTaskService.completeCurrentTask(procInstId, comment, null, false);

        // 获取当前任务信息
        Task currTask = actTaskService.getCurrentTask(procInstId);
        // 设置工作流任务的办理人
        actTaskService.setAssignee(currTask.getId(), taskDto.getNextAssignee());

        oaTask.setCurrentTaskDefKey(currTask.getTaskDefinitionKey());
        oaTask.setCurrentTaskName(currTask.getName());
        oaTask.setCurrentAssignee(taskDto.getNextAssignee());
        oaTask.setCurrentAssigneeId(taskDto.getNextAssigneeId());
        oaTask.setCurrentAssigneeName(taskDto.getNextAssigneeName());
        oaTask.fillTaskStatus(EnumOATaskStatus.审批中);

        // 设置默认值
        oaTask.setCompleteTime(oaTask.getSubmitTime());

        DtoPerson dtoPerson = personService.findOne(PrincipalContextUser.getPrincipal().getUserId(), true);

        String deptId = UUIDHelper.GUID_EMPTY;
        String deptName = "";
        if (StringUtil.isNotNull(dtoPerson)) {
            deptId = dtoPerson.getDeptId();
            DtoDepartment dtoDepartment = departmentService.findOne(deptId);
            if (StringUtil.isNotNull(dtoDepartment)) {
                deptName = dtoDepartment.getDeptName();
            }
        }
        oaTask.setDeptId(deptId);
        oaTask.setDeptName(deptName);
        // 保存任务相关信息
        super.save(oaTask);
        oaTaskHandleLogService.save(handLog);

        //清除2个数据缓存
        homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId(),
                EnumLIM.EnumHomeTaskModule.我已发起.getValue());

        homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId(),
                EnumLIM.EnumHomeTaskModule.待我审批.getValue());
        return oaTask;
    }

    @Transactional
    @Override
    public DtoOATask saveAsDraft(EnumOATaskType taskType, DtoOATaskCreate<?> taskDto, Map<String, Object> vars) {
        //用户
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        //新增OA任务
        DtoOATask oaTask = new DtoOATask();
        //新增日志记录
        DtoOATaskHandleLog handLog = new DtoOATaskHandleLog();
        //任务及日志初始化
        initDraftTaskAndLog(user, taskType.getCode(), taskDto, oaTask, handLog);
        // 保存任务相关信息
        super.save(oaTask);
        oaTaskHandleLogService.save(handLog);
        //清除数据缓存
        homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId(),
                EnumLIM.EnumHomeTaskModule.我已发起.getValue());
        return oaTask;
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<?> taskDto, Map<String, Object> vars) {
        //oa任务
        DtoOATask oaTask = findByTaskId(taskDto.getOaTaskId());
        //发起人
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        // 设置流程发起人
        actProcessService.setStartProcessUserId(user.getLoginId());
        // 启动流程
        ProcessInstance procInst = actProcessService.startProcess(oaTask.getProcTypeCode(), oaTask.getId(), oaTask.getTitle(), vars);
        String procInstId = procInst.getId();
        // 关联流程实例ID
        oaTask.setProcInstId(procInstId);
        // 获取发起审批任务信息
        Task sponsorTask = actTaskService.getCurrentTask(procInstId);
        // 设置工作流任务的办理人
        actTaskService.setAssignee(sponsorTask.getId(), user.getLoginId());
        //意见描述，如果工作流中配置了名称，那就默认空，否则默认是发起审批
        String comment = StringUtils.isNotNullAndEmpty(sponsorTask.getName()) ? "" : "发起审批";
        // 完成发起审批
        actTaskService.completeCurrentTask(procInstId, comment, null, false);
        // 获取当前任务信息
        Task currTask = actTaskService.getCurrentTask(procInstId);
        // 设置工作流任务的办理人
        actTaskService.setAssignee(currTask.getId(), taskDto.getNextAssignee());
        //oatask更新
        oaTask.setCurrentTaskDefKey(currTask.getTaskDefinitionKey());
        oaTask.setCurrentTaskName(currTask.getName());
        oaTask.setCurrentAssigneeId(taskDto.getNextAssigneeId());
        oaTask.setCurrentAssigneeName(taskDto.getNextAssigneeName());
        oaTask.fillTaskStatus(EnumOATaskStatus.审批中);
        oaTask.setCompleteTime(new Date());
        super.save(oaTask);
        //插入提交日志
        DtoOATaskHandleLog handLog = new DtoOATaskHandleLog();
        handLog.setTaskId(oaTask.getId());
        handLog.setIsAgree(true);
        handLog.setComment(comment);
        handLog.setCompleteTime(new Date());
        handLog.setAssignee(user.getLoginId());
        handLog.setAssigneeName(user.getUserName());
        handLog.setActTaskId(sponsorTask.getId());
        handLog.setActTaskDefKey(sponsorTask.getTaskDefinitionKey());
        handLog.setActTaskName(sponsorTask.getName());
        handLog.setIsFirstStep(true);
        oaTaskHandleLogService.save(handLog);
        //清除数据缓存
        homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId(),
                EnumLIM.EnumHomeTaskModule.待我审批.getValue());
        return procInstId;
    }

    /**
     * 任务及日志初始化
     *
     * @param user       当前用户
     * @param procDefKey 类型编码
     * @param taskDto    表单数据
     * @param oaTask     oa任务
     * @param handLog    操作日志
     */
    private void initDraftTaskAndLog(CurrentPrincipalUser user, String procDefKey, DtoOATaskCreate<?> taskDto, DtoOATask oaTask, DtoOATaskHandleLog handLog) {
        //tas部分
        oaTask.setTitle(taskDto.getTitle());
        oaTask.setDescription(taskDto.getDescription());
        oaTask.setProcTypeCode(procDefKey);
        oaTask.setProcTypeId(taskDto.getProcTypeId());
        oaTask.setProcTypeName(taskDto.getProcTypeName());
        oaTask.setSponsor(user.getLoginId());
        oaTask.setSponsorId(user.getUserId());
        oaTask.setSponsorName(user.getUserName());
        oaTask.setDeptId(user.getDeptId());
        oaTask.setSubmitTime(new Date());
        oaTask.fillTaskStatus(EnumOATaskStatus.新建);
        oaTask.setCompleteTime(new Date());
        oaTask.setCurrentAssignee(user.getLoginId());
        oaTask.setCurrentAssigneeId(user.getUserId());
        oaTask.setCurrentAssigneeName(user.getUserName());
        DtoPerson dtoPerson = personService.findOne(user.getUserId(), true);
        String deptId = UUIDHelper.GUID_EMPTY;
        String deptName = "";
        if (StringUtil.isNotNull(dtoPerson)) {
            deptId = dtoPerson.getDeptId();
            DtoDepartment dtoDepartment = departmentService.findOne(deptId);
            if (StringUtil.isNotNull(dtoDepartment)) {
                deptName = dtoDepartment.getDeptName();
            }
        }
        oaTask.setDeptId(deptId);
        oaTask.setDeptName(deptName);
        //日志部分
        handLog.setActTaskName("保存草稿");
        handLog.setTaskId(oaTask.getId());
        handLog.setIsAgree(true);
        handLog.setCompleteTime(new Date());
        handLog.setAssignee(user.getLoginId());
        handLog.setAssigneeName(user.getUserName());
        handLog.setIsFirstStep(false);

    }

    @Override
    public void addOATaskRelation(String taskId, String objectId) {
        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(taskId);
        taskRelation.setObjectId(objectId);

        oaTaskRelationService.save(taskRelation);
    }

    @Override
    public DtoOATask findByTaskId(String taskId) {
        return super.findOne(taskId);
    }

    @Override
    public List<DtoOATaskAttach> getOATaskAttachDTO(String procInstId) {
        List<DtoOATaskAttach> oaTaskAttachs = new ArrayList<>();
        List<Attachment> srcAttachs = taskService.getProcessInstanceAttachments(procInstId);

        if (srcAttachs != null && !srcAttachs.isEmpty()) {
            DtoOATaskAttach attach = null;

            for (Attachment srcAttach : srcAttachs) {
                attach = new DtoOATaskAttach();
                attach.setId(srcAttach.getId());
                attach.setName(srcAttach.getName());
                attach.setType(srcAttach.getType());
                attach.setTime(srcAttach.getTime());
                attach.setUserId(srcAttach.getUserId());

                // 添加附件信息
                oaTaskAttachs.add(attach);
            }
        }

        return oaTaskAttachs;
    }

    @Override
    public void addAttachment(String procInstId, String taskId, List<MultipartFile> files) {
        if (!StringUtils.isNotNullAndEmpty(files)) {
            log.info("Add attachment file is empty");
            return;
        }

        if (!StringUtils.isNotNullAndEmpty(taskId)) {
            Task task = actTaskService.getCurrentTask(procInstId);
            taskId = task.getId();
        }

        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        // 设置附件上传人
        actTaskService.setAuthenticatedUserId(user.getUserName());

        try {
            for (MultipartFile file : files) {
                taskService.createAttachment(file.getContentType(), taskId, procInstId, file.getOriginalFilename(),
                        null, file.getInputStream());
            }
        } catch (IOException e) {
            log.error("Add attachment error ", e);
        }
    }

    @Override
    public InputStream getAttachmentContent(String attachmentId) {
        return taskService.getAttachmentContent(attachmentId);
    }

    @Override
    public Attachment getAttachment(String attachmentId) {
        return taskService.getAttachment(attachmentId);
    }

    @Override
    public void findTodo(PageBean<DtoOATask> page, DtoOATaskQuery param) {
        page.setEntityName("DtoOATask task");
        page.setSelect("select task");

        OATaskCriteria criteria = new OATaskCriteria();
        criteria.setParam(param);
        criteria.setQueryType(EnumOATaskQueryType.待我审批);
        criteria.setNotStatus(Collections.singletonList(EnumOATaskStatus.新建.getValue()));

        super.findByPage(page, criteria);
    }

    @Override
    public void findFinish(PageBean<DtoOATask> page, DtoOATaskQuery param) {
        page.setEntityName("DtoOATask task, DtoOATaskHandleLog log");
        page.setSelect("select task");

        OATaskCriteria criteria = new OATaskCriteria();
        criteria.setParam(param);
        criteria.setQueryType(EnumOATaskQueryType.我已审批);
        criteria.setNotStatus(Collections.singletonList(EnumOATaskStatus.新建.getValue()));

        super.findByPage(page, criteria);
    }

    @Override
    public void findSponsor(PageBean<DtoOATask> page, DtoOATaskQuery param) {
        page.setEntityName("DtoOATask task");
        page.setSelect("select task");

        OATaskCriteria criteria = new OATaskCriteria();
        criteria.setParam(param);
        criteria.setQueryType(EnumOATaskQueryType.我已发起);

        super.findByPage(page, criteria);
    }

    @Override
    public void findAll(PageBean<DtoOATask> page, DtoOATaskQuery param) {
        page.setEntityName("DtoOATask task");
        page.setSelect("select task");

        OATaskCriteria criteria = new OATaskCriteria();
        criteria.setParam(param);
        criteria.setQueryType(EnumOATaskQueryType.所有审批);
        criteria.setNotStatus(Collections.singletonList(EnumOATaskStatus.新建.getValue()));

        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public void completeTask(DtoOATaskHandle param) {
        // 查询审批任务
        DtoOATask oaTask = findByTaskId(param.getTaskId());

        DtoOATaskHandleLog handLog = new DtoOATaskHandleLog();
        handLog.setTaskId(param.getTaskId());
        handLog.setIsAgree(param.isPass());
        String comment = param.getComment();
        handLog.setComment(comment);
        handLog.setCompleteTime(new Date());

        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        handLog.setAssignee(user.getLoginId());
        handLog.setAssigneeName(user.getUserName());
        if (StringUtil.isNotNull(param.getJurorId())) {
            handLog.setJurorId(param.getJurorId());
        }
        // 获取当前任务信息
        String procInstId = oaTask.getProcInstId();
        Task currTask = actTaskService.getCurrentTask(procInstId);
        if (StringUtil.isNotNull(currTask)) {
            handLog.setActTaskId(currTask.getId());
            handLog.setActTaskDefKey(currTask.getTaskDefinitionKey());
            handLog.setActTaskName(currTask.getName());
            if (!param.isPass()) {
                setTaskEndValue(oaTask, EnumOATaskStatus.审批拒绝);
                // 添加工作流中的批注
                actTaskService.addTaskComment(currTask.getId(), procInstId, comment);
                // 终止流程
                actProcessService.deleteProcIns(procInstId, comment);

                //针对文件修订的数据，要改回到已受控状态，防止文件无法选择
                if (oaTask.getProcTypeCode().equals(EnumOATaskType.文件修订.getCode())||oaTask.getProcTypeCode().equals(EnumOATaskType.文件废止.getCode())) {
                    // 查找关系
                    DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(param.getTaskId());
                    String fileId = UUIDHelper.GUID_EMPTY;
                    if(oaTask.getProcTypeCode().equals(EnumOATaskType.文件修订.getCode())){
                        DtoOAFileRevision detail = oaFileRevisionService.findOne(relation.getObjectId());
                        if(detail!=null){
                            fileId = detail.getFileId();
                        }
                    }else if(oaTask.getProcTypeCode().equals(EnumOATaskType.文件废止.getCode())){
                        DtoOAFileAbolish detail = oaFileAbolishService.findOne(relation.getObjectId());
                        if(detail!=null){
                            fileId = detail.getFileId();
                        }
                    }
                    DtoFileControlApplyDetail dtoFileControlApplyDetail = fileControlApplyDetailService.findOne(fileId);

                    if (StringUtil.isNotNull(dtoFileControlApplyDetail)) {
                        //将数据还原到之前的
                        fileControlApplyDetailService.updateFileStatus(dtoFileControlApplyDetail.getId(), EnumLIM.EnumFileControlStatus.已受控);
                    }
                }
                //清除2个数据缓存
                homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                        PrincipalContextUser.getPrincipal().getOrgId(),
                        EnumLIM.EnumHomeTaskModule.我已发起.getValue());

                homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                        PrincipalContextUser.getPrincipal().getOrgId(),
                        EnumLIM.EnumHomeTaskModule.待我审批.getValue());

            } else {
                //处理通过的审批任务
                passTaskHandle(currTask, oaTask, param, procInstId);
            }
        } else { //说明流程最后结束了
            // normalCloseTaskNotify(oaTask);
            setTaskEndValue(oaTask, EnumOATaskStatus.审批通过);

            //清除2个数据缓存
            homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(),
                    EnumLIM.EnumHomeTaskModule.待我审批.getValue());

            homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(),
                    EnumLIM.EnumHomeTaskModule.我已审批.getValue());
        }
        // 更新审批任务中的信息
        super.update(oaTask);

        oaTaskHandleLogService.save(handLog);
    }

    /**
     * 流程审核通过的任务流程处理
     *
     * @param currTask   当前工作流
     * @param oaTask     当前的审批任务
     * @param param      提交参数
     * @param procInstId 工作流id
     */
    @Transactional
    protected void passTaskHandle(Task currTask, DtoOATask oaTask, DtoOATaskHandle param, String procInstId) {
        // 完成工作流中的当前任务步骤
        actTaskService.complete(currTask.getId(), currTask.getProcessInstanceId(), param.getComment());
        // 再次查询当前任务
        currTask = actTaskService.getCurrentTask(procInstId);

        if (StringUtil.isNull(currTask)) {
            // 表示整个流程结束了
            //normalCloseTaskNotify(oaTask);
            setTaskEndValue(oaTask, EnumOATaskStatus.审批通过);
            // 如果是合同审批的流程,合同审批结束需要OA合同信息覆盖原合同信息
            if (oaTask.getProcTypeCode().equals(EnumOATaskType.合同审批.getCode())) {
                oaContractService.oaContractToContract(oaTask);
            }
            dealTask(oaTask);
            //清除2个数据缓存
            homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(),
                    EnumLIM.EnumHomeTaskModule.待我审批.getValue());

            homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(),
                    EnumLIM.EnumHomeTaskModule.我已审批.getValue());

        } else {
            // 设置工作流中的当前任务的办理人
            actTaskService.setAssignee(currTask.getId(), param.getNextAssignee());
            oaTask.setCurrentTaskDefKey(currTask.getTaskDefinitionKey());
            oaTask.setCurrentTaskName(currTask.getName());
            oaTask.setCurrentAssignee(param.getNextAssignee());
            oaTask.setCurrentAssigneeId(param.getNextAssigneeId());
            oaTask.setCurrentAssigneeName(param.getNextAssigneeName());
        }
    }

    /**
     * 个性化方法
     *
     * @param oaTask 数据
     */
    @Transactional
    protected void dealTask(DtoOATask oaTask) {

    }

    /**
     * 审批任务结束设置相关属性值
     *
     * @param oaTask 审批任务
     * @param status 审批状态
     */
    protected void setTaskEndValue(DtoOATask oaTask, EnumOATaskStatus status) {
        oaTask.setCurrentTaskDefKey("");
        oaTask.setCurrentTaskName("");
        oaTask.setCurrentAssignee("");
        oaTask.setCurrentAssigneeId(UUIDHelper.GUID_EMPTY);
        oaTask.setCurrentAssigneeName("");
        oaTask.fillTaskStatus(status);
        oaTask.setCompleteTime(new Date());
    }

    @Override
    @Transactional
    public void cancelTask(String taskId, String reason) {
        // 查询审批任务
        DtoOATask oaTask = findByTaskId(taskId);

        DtoOATaskHandleLog handLog = new DtoOATaskHandleLog();
        handLog.setTaskId(taskId);
        handLog.setIsAgree(false);
        String comment = reason;
        handLog.setComment(comment);
        handLog.setCompleteTime(new Date());

        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        handLog.setAssignee(user.getLoginId());
        handLog.setAssigneeName(user.getUserName());
        // 获取当前任务信息
        String procInstId = oaTask.getProcInstId();
        Task currTask = actTaskService.getCurrentTask(procInstId);
        handLog.setActTaskName("任务撤销");
        if (StringUtil.isNotNull(currTask)) {
            handLog.setActTaskId(currTask.getId());
            handLog.setActTaskDefKey(currTask.getTaskDefinitionKey());

            // 添加工作流中的批注
            actTaskService.addTaskComment(currTask.getId(), procInstId, comment);
            // 终止流程
            actProcessService.deleteProcIns(procInstId, comment);
        }

        setTaskEndValue(oaTask, EnumOATaskStatus.已撤销);

        // 更新审批任务中的信息
        super.update(oaTask);

        oaTaskHandleLogService.save(handLog);

        //针对文件修订的数据，要改回到已受控状态，防止文件无法选择
        if (oaTask.getProcTypeCode().equals(EnumOATaskType.文件修订.getCode())) {
            // 查找关系
            DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(taskId);

            DtoOAFileRevision detail = oaFileRevisionService.findOne(relation.getObjectId());

            DtoFileControlApplyDetail dtoFileControlApplyDetail = fileControlApplyDetailService.findOne(detail.getFileId());

            if (StringUtil.isNotNull(dtoFileControlApplyDetail)) {
                //将数据还原到之前的
                fileControlApplyDetailService.updateFileStatus(dtoFileControlApplyDetail.getId(), EnumLIM.EnumFileControlStatus.已受控);
            }
        } else if (oaTask.getProcTypeCode().equals(EnumOATaskType.文件受控.getCode())) {
            // 查找关系
            DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(taskId);

            DtoOAFileControl detail = oaFileControlService.findOne(relation.getObjectId());

            DtoFileControlApplyDetail dtoFileControlApplyDetail = fileControlApplyDetailService.findOne(detail.getFileId());

            if (StringUtil.isNotNull(dtoFileControlApplyDetail)) {
                //将数据还原到之前的
                fileControlApplyDetailService.updateFileStatus(dtoFileControlApplyDetail.getId(), EnumLIM.EnumFileControlStatus.未受控);
            }
        }

        homeService.clearOATaskCache(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId(),
                EnumLIM.EnumHomeTaskModule.待我审批.getValue());
    }


    @Override
    public void syncAttachmentToDocument(
            String procInstId,
            String folderId,
            String path) {
        //创建文件目录
        String filePath = filePathConfig.getFilePath();
        String checkPath = filePath + "/" + path;
        File fileStream = new File(checkPath);
        if (!fileStream.exists()) {
            fileStream.mkdirs();
        }
        List<Attachment> attachments = taskService.getProcessInstanceAttachments(procInstId);
        for (Attachment attachment : attachments) {
            String fileName = attachment.getName();
            //物理名称
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String physicalName = dateFormat.format(new Date()) + "_" + fileName;
            //当中有"/" 所以不需要拼接了
            String pathName = checkPath + physicalName;
            int len = 0;
            //文件流
            FileOutputStream fos = null;
            InputStream inputStream = null;
            BufferedInputStream bi = null;
            try {
                //返回文件流
                inputStream = getAttachmentContent(attachment.getId());
                bi = new BufferedInputStream(inputStream);
                fos = new FileOutputStream(pathName);
                byte[] by = new byte[1024];
                while ((len = bi.read(by)) != -1) {
                    fos.write(by, 0, len);
                }
                fos.close();
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            } finally {
                FileUtil.close(fos);
                FileUtil.close(inputStream);
                FileUtil.close(bi);
            }

            String downPath = "/" + path + physicalName;

            String fileType = fileName.substring(fileName.lastIndexOf("."));
            DtoDocument dtoDocument = new DtoDocument();
            dtoDocument.setFolderId(folderId);
            dtoDocument.setFilename(fileName);
            dtoDocument.setPhysicalName(physicalName);
            dtoDocument.setPath(downPath);
            dtoDocument.setIsTranscript(false);
            dtoDocument.setDocSize(Integer.parseInt(String.valueOf(len)));
            dtoDocument.setDocSuffix(fileType);
            dtoDocument.setDownloadTimes(0);
            dtoDocument.setOrderNum(0);
            dtoDocument.setRemark("");
            dtoDocument.setUploadPerson(PrincipalContextUser.getPrincipal().getUserName());
            dtoDocument.setUploadPersonId(PrincipalContextUser.getPrincipal().getUserId());
            dtoDocument.setIsStick(false);
            dtoDocument.setOrgId(PrincipalContextUser.getPrincipal().getOrgId());

            documentService.save(dtoDocument);
        }
    }

    @Override
    public DtoOATask findAttachPath(String id) {
        return findOne(id);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<DtoOATaskRelation> oaTaskRelationList = oaTaskRelationRepository.findByTaskIdIn((List<String>) ids);
        //删除领料申请流程需还原库存
        List<DtoOATask> taskList = repository.findAll((List<String>) ids).stream()
                .filter(t -> t.getProcTypeCode().equals(EnumOATaskType.领料.getCode())).collect(Collectors.toList());
        if (!taskList.isEmpty()) {
            List<String> taskIds = taskList.stream().map(DtoOATask::getId).collect(Collectors.toList());
            //还原库存
            List<DtoConsumableLog> originConsumableLogList = consumableLogRepository.findAllByConsumablePickIdIn(taskIds);
            consumableLogService.delete(originConsumableLogList);
            //删除领用记录
            List<String> objectIds = oaTaskRelationList.stream().filter(r -> taskIds.contains(r.getTaskId())).map(DtoOATaskRelation::getObjectId)
                    .collect(Collectors.toList());
            List<DtoOAConsumablePickListsDetail> originConsumablePickListsDetailList = oaConsumablePickListsDetailService.findAll(objectIds);
            oaConsumablePickListsDetailService.delete(originConsumablePickListsDetailList);
        }
        oaTaskRelationRepository.delete(oaTaskRelationList);
        return super.logicDeleteById(ids);
    }

    @Override
    @Transactional
    public DtoOATask DraftSave(DtoOATaskCreate<?> taskDto) {
        DtoOATask oaTask = repository.findOne(taskDto.getOaTaskId());
        oaTask.setTitle(taskDto.getTitle());
        oaTask.setDescription(taskDto.getDescription());
        //新增日志记录
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        DtoOATaskHandleLog handLog = new DtoOATaskHandleLog();
        handLog.setActTaskName("保存草稿");
        handLog.setTaskId(oaTask.getId());
        handLog.setIsAgree(true);
        handLog.setCompleteTime(new Date());
        handLog.setAssignee(user.getLoginId());
        handLog.setAssigneeName(user.getUserName());
        handLog.setIsFirstStep(false);
        oaTaskHandleLogService.save(handLog);
        return save(oaTask);
    }
}
