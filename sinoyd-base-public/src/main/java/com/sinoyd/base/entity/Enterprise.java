package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Enterprise实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "Enterprise")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Enterprise extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Enterprise() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 企业名称（污染源名称）（lims）
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("企业名称（污染源名称）（lims）")
    @Length(message = "企业名称{validation.message.length}", max = 100)
    private String name;

    /**
     * 企业简称
     */
    @Column(length = 100)
    @ApiModelProperty("企业简称")
    private String shortName;

    /**
     * 企业编码
     */
    @Column(length = 20)
    @ApiModelProperty("企业编码")
    private String code;

    /**
     * 社会信用代码（lims）
     */
    @Column(length = 20)
    @ApiModelProperty("社会信用代码（lims）")
    @Length(message = "社会信用代码{validation.message.length}", max = 50)
    private String socialCreditCode;

    /**
     * 拼音缩写（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("拼音缩写（lims）")
    private String pinYin;

    /**
     * 全拼（lims）
     */
    @Column(length = 100)
    @ApiModelProperty("全拼（lims）")
    private String fullPinYin;

    /**
     * 建设状态（枚举EnumBuildStatus：0.建设中 1.已完成）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("建设状态（枚举EnumBuildStatus：0.建设中 1.已完成）")
    private Integer buildStatus;

    /**
     * 投产日期（lims）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("投产日期（lims）")
    private Date runDate;

    /**
     * 成立时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("成立时间")
    private Date passDate;

    /**
     * 行业类型Id（Guid）（常量：BASE_BussniessType）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("行业类型Id（Guid）（常量：BASE_BussniessType）")
    private String businessTypeId;

    /**
     * 注册类型Id（Guid）（常量：BASE_RegType）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("注册类型Id（Guid）（常量：BASE_RegType）")
    private String regTypeId;

    /**
     * 污染许可证编号
     */
    @ApiModelProperty("污染许可证编号")
    private String pollutionDischargeCode;
    /**
     * 企业经度
     */
    @Column(length = 20)
    @ApiModelProperty("企业经度")
    private String longitude;

    /**
     * 企业纬度
     */
    @Column(length = 20)
    @ApiModelProperty("企业纬度")
    private String latitude;

    /**
     * 经度（其他地图）
     */
    @Column(length = 20)
    @ApiModelProperty("经度（其他地图）")
    private String longitudeOther;

    /**
     * 纬度（其他地图）
     */
    @Column(length = 20)
    @ApiModelProperty("纬度（其他地图）")
    private String latitudeOther;

    /**
     * 联系人传真（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("联系人传真（lims）")
    @Length(message = "联系人传真{validation.message.length}", max = 50)
    private String contactFax;

    /**
     * 联系人（lims）
     */
    @Column(length = 20)
    @ApiModelProperty("联系人（lims）")
    @Length(message = "联系人{validation.message.length}", max = 100)
    private String contactMan;

    /**
     * 联系电话（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("联系电话（lims）")
    @Length(message = "联系电话{validation.message.length}", max = 50)
    private String contactPhoneNumber;

    /**
     * 联系手机（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("联系手机（lims）")
    @Length(message = "联系手机{validation.message.length}", max = 50)
    private String contactTelPhone;

    /**
     * 企业地址（lims）
     */
    @Column(length = 100)
    @ApiModelProperty("企业地址（lims）")
    @Length(message = "企业地址{validation.message.length}", max = 100)
    private String address;

    /**
     * 联系邮箱（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("联系邮箱（lims）")
    @Length(message = "联系邮箱{validation.message.length}", max = 50)
    private String email;

    /**
     * 邮政编码（lims）
     */
    @Column(length = 10)
    @ApiModelProperty("邮政编码（lims）")
    @Length(message = "邮政编码{validation.message.length}", max = 10)
    private String postalCode;

    /**
     * 法人代码（lims）
     */
    @Column(length = 20)
    @ApiModelProperty("法人代码（lims）")
    @Length(message = "法人代码{validation.message.length}", max = 20)
    private String corporationCode;

    /**
     * 法人代表（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("法人代表（lims）")
    @Length(message = "法人代表{validation.message.length}", max = 50)
    private String corporationName;

    /**
     * 注册资金（lims）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("注册资金（lims）")
    private BigDecimal registeredCapital;

    /**
     * 所在地区Id（Guid）（lims）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所在地区Id（Guid）（lims）")
    private String areaId;

    /**
     * 所属区域（lims）
     */
    @Column(length = 1000)
    @ApiModelProperty("所属区域（lims）")
    private String areaName;

    /**
     * 企业性质（lims）
     */
    @ApiModelProperty("企业性质（lims）")
    @Length(message = "企业性质{validation.message.length}", max = 255)
    private String nature;

    /**
     * 经营范围（lims）
     */
    @ApiModelProperty("经营范围（lims）")
    @Length(message = "经营范围{validation.message.length}", max = 255)
    private String businessScope;

    /**
     * 占地面积（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("占地面积（lims）")
    @Length(message = "占地面积{validation.message.length}", max = 50)
    private String acreage;

    /**
     * 厂房性质（lims）
     */
    @ApiModelProperty("厂房性质（lims）")
    @Length(message = "厂房性质{validation.message.length}", max = 255)
    private String houseNature;

    /**
     * 产权所有人（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("产权所有人（lims）")
    @Length(message = "产权所有人{validation.message.length}", max = 50)
    private String owner;

    /**
     * 所在工业园区（lims）
     */
    @ApiModelProperty("所在工业园区（lims）")
    @Length(message = "所在工业园区{validation.message.length}", max = 255)
    private String industrialPark;

    /**
     * 员工人数（lims）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("员工人数（lims）")
    private Integer employeeNumber;

    /**
     * 年正常生产天数（lims）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("年正常生产天数（lims）")
    private Integer runDaysPerYear;

    /**
     * 年产值（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("年产值（lims）")
    @Length(message = "年产值{validation.message.length}", max = 255)
    private String productValuePerYear;

    /**
     * 利税（lims）
     */
    @Column(length = 50)
    @ApiModelProperty("利税（lims）")
    @Length(message = "利税{validation.message.length}", max = 255)
    private String tax;

    /**
     * 企业种类(枚举EnumEntType：1.污染源 2.客户  4：供应商 8：分包商）使用按位与表示客户为污染源（lims）
     */
    @Column(nullable = false)
    @ColumnDefault("2")
    @ApiModelProperty("企业种类(枚举EnumEntType：1.污染源 2.客户  4：供应商 8：分包商）使用按位与表示客户为污染源（lims） ")
    private Integer type;

    /**
     * 质量认证（lims）
     */
    @Column(length = 1000)
    @ApiModelProperty("质量认证（lims）")
    @Length(message = "质量认证{validation.message.length}", max = 1000)
    private String qualityCertification;

    /**
     * 是否合格（lims）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否合格（lims）")
    private Boolean isEligibility;

    /**
     * 排序号（lims）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序号（lims）")
    private Integer orderNum;

    /**
     * 假删字段（lims）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段（lims）")
    private Boolean isDeleted = false;

    /**
     * 企业网址（lims）
     */
    @Column(length = 100)
    @ApiModelProperty("企业网址（lims）")
    @Length(message = "企业网址{validation.message.length}", max = 100)
    private String url;

    /**
     * 规模（lims）
     */
    @ApiModelProperty("规模（lims）")
    @Length(message = "规模{validation.message.length}", max = 255)
    private String scope;

    /**
     * 企业简介（lims）
     */
    @ApiModelProperty("企业简介（lims）")
    private String info;

    /**
     * 开业日期（lims）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("开业日期（lims）")
    private Date openDate;

    /**
     * 备注（lims）
     */
    @Column(length = 1000)
    @ApiModelProperty("备注（lims）")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 系统类型(枚举EnumSystemType：1.LIMS  2.环境质量 4.污染源)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("系统类型(枚举EnumSystemType：1.LIMS  2.环境质量 4.污染源)")
    private Integer systemType;

    /**
     * 关联系统客户编号
     */
    @Column(length = 50)
    @ApiModelProperty("关联系统客户编号")
    private String externalId;

    /**
     * cma证书编号
     */
    @Column(length = 50)
    @ApiModelProperty("cma证书编号")
    private String cmaCode;

    /**
     * cma证书有效期
     */
    @Column(nullable = false)
    @ApiModelProperty("cma证书有效期")
    private Date cmaExpiryDate;

    /**
     * CNAS证书编号
     */
    @Column(length = 50)
    @ApiModelProperty("CNAS证书编号")
    private String cnasCode;

    /**
     * CNAS证书有效期
     */
    @Column(nullable = false)
    @ApiModelProperty("CNAS证书有效期")
    private Date cnasExpiryDate;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     *
     */
    @Column(length = 50)
    @ApiModelProperty("")
    @Length(message = "委托费率{validation.message.length}", max = 50)
    private String entrustRate;

    /**
     * 所属行业
     */
    @Column(length = 100)
    @ApiModelProperty("所属行业")
    @Length(message = "所属行业{validation.message.length}", max = 100)
    private String industryKind;

    /**
     * 监管平台客户名称
     */
    @Column(length = 50)
    @ApiModelProperty("监管平台客户名称")
    private String regulateName;

    /**
     * 监管平台客户id
     */
    @Column(length = 50)
    @ApiModelProperty("监管平台客户id")
    private String regulateId;

    /**
     * 监管平台客户社会信用代码
     */
    @Column(length = 50)
    @ApiModelProperty("监管平台客户社会信用代码")
    private String regulateSocialCode;

    /**
     * 监管平台客户污染源编号
     */
    @Column(length = 50)
    @ApiModelProperty("监管平台客户污染源编号")
    private String regulatePollutionCode;

    /**
     * 排污许可证方法是否同步成功
     */
    @Column(nullable = false, columnDefinition = "b'0'")
    @ApiModelProperty("是否同步成功")
    private Boolean isSyncPollutionDischarge;


    /**
     * 资质情况
     */
    @Column(length = 255)
    @ApiModelProperty("资质情况")
    private String aptitude;

}