package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord2Sample;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * InstrumentUseRecord2Sample数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
public interface InstrumentUseRecord2SampleRepository extends IBaseJpaPhysicalDeleteRepository<DtoInstrumentUseRecord2Sample, String> {

    /**
     * 根据仪器使用记录获取样品关联
     *
     * @param instrumentUseRecordIds 仪器使用记录id集合
     * @return 仪器使用记录样品关联
     */
    List<DtoInstrumentUseRecord2Sample> findByInstrumentUseRecordIdIn(List<String> instrumentUseRecordIds);

    /**
     * 根据仪器使用记录删除样品关联
     *
     * @param instrumentUseRecordIds 仪器使用记录id集合
     */
    @Transactional
    Integer deleteByInstrumentUseRecordIdIn(List<String> instrumentUseRecordIds);

    /**
     * 根据仪器使用记录删除样品关联
     *
     * @param instrumentUseRecordId 仪器使用记录id
     */
    @Transactional
    Integer deleteByInstrumentUseRecordId(String instrumentUseRecordId);

    /**
     * 根据仪器使用记录id 获取相关的样品信息
     *
     * @param instrumentUseRecordId 仪器使用记录id
     * @return 返回相关的样品信息
     */
    List<DtoInstrumentUseRecord2Sample> findByInstrumentUseRecordId(String instrumentUseRecordId);

    /**
     * 根据样品标识获取样品关联
     *
     * @param sampleIds 样品标识
     * @return 仪器使用记录样品关联
     */
    List<DtoInstrumentUseRecord2Sample> findBySampleIdIn(List<String> sampleIds);
}