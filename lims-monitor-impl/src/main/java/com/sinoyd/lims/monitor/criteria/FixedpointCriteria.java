package com.sinoyd.lims.monitor.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.monitor.enums.EnumMonitor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Fixedpoint查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FixedpointCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 主键id集合
     */
    private List<String> ids;

    /**
     * 状态，1启用，0未启用，null所有
     */
    private Integer isEnabled;

    /**
     * 点位类型
     */
    private String folderType;

    /**
     * 点位名称和点位编码关键字
     */
    private String key;

    /**
     * 类型（枚举：环境质量 1 污染源 2）
     */
    private int pointType;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 污染源类型
     */
    private String pollutionType;

    /**
     * 企业id
     */
    private String entId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 样品类型id
     */
    private String sampleTypeId;
    /**
     * 需要排除的点位id列表
     */
    private List<String> excludePointIds;

    /**
     * 所在地区Id（Guid）（lims）
     */
    private String areaId;

    /**
     * 测站Id
     */
    private String stationId;

    /**
     * 测站Id
     */
    private List<String> stationIds;

    /**
     * 机构id集合
     */
    private List<String> orgIds;

    /**
     * 区域ids
     */
    private List<String> areaIds;

    /**
     * 是否过滤空测试项目（用作污染源选择企业点位，过滤掉测试项目未空的点位）
     */
    private Boolean isFilterEmptyTest = false;

    /**
     * 等级
     */
    private String level;

    @Override
    public String getCondition() {
        if (pointType == 1) {
            return getConditionForEnvQuality();
        } else if (pointType == 2) {
            return getConditionForPollutant();
        }
        return "";
    }

    /**
     * 获取环境质量点位列表查询条件
     *
     * @return 环境质量点位列表查询条件
     */
    private String getConditionForEnvQuality() {
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotEmpty(folderType)) {
            condition.append(" and folderType like :folderType ");
            values.put("folderType", "%" + this.folderType + "%");
        }
        if (EnumMonitor.EnumPointType.环境质量.getValue().equals(pointType)) {
            condition.append(" and pointType = :pointType ");
            values.put("pointType", EnumMonitor.EnumPointType.环境质量.getValue());
        }
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (pointCode like :key or  pointName like :key or examArea like :key) ");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(remark)) {
            condition.append(" and remark like :remark");
            values.put("remark", "%" + this.remark + "%");
        }
        if (StringUtil.isNotEmpty(excludePointIds)) {
            condition.append(" and id not in :excludePointIds ");
            values.put("excludePointIds", excludePointIds);
        }
        if (null != isEnabled) {
            if (0 == isEnabled) {
                condition.append(" and isEnabled = false ");
            } else {
                condition.append(" and isEnabled = true");
            }
        }
        if (StringUtil.isNotEmpty(areaIds) && !UUIDHelper.GUID_EMPTY.equals(areaId) && StringUtil.isEmpty(areaIds)) {
            condition.append(" and areaId = :areaId ");
            values.put("areaId", this.areaId);
        } else if (StringUtil.isNotEmpty(areaIds)) {
            condition.append(" and areaId in :areaIds ");
            values.put("areaIds", this.areaIds);
        }
        if (StringUtil.isNotEmpty(this.stationId) && !UUIDHelper.GUID_EMPTY.equals(this.stationId)) {
            condition.append(" and stationId = :stationId ");
            values.put("stationId", this.stationId);
        }
        if (StringUtil.isNotEmpty(this.orgIds)) {
            condition.append(" and exists(select 1 from DtoStation s where p.stationId = s.id and s.entId in :entIds)");
            values.put("entIds", this.orgIds);
        }
        if (StringUtil.isNotEmpty(ids)) {
            condition.append(" and p.id  in :ids ");
            values.put("ids", ids);
        }
        if (StringUtil.isNotEmpty(stationIds)) {
            condition.append(" and p.stationId  in :stationIds ");
            values.put("stationIds", stationIds);
        }
        if (StringUtil.isNotEmpty(level)) {
            condition.append(" and p.level  = :level ");
            values.put("level", level);
        }
        return condition.toString();
    }

    /**
     * 获取污染源点位列表查询条件
     *
     * @return 污染源点位列表查询条件
     */
    private String getConditionForPollutant() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        List<Integer> pointTypeList = new ArrayList();
        condition.append(" and a.enterpriseId = b.id");

        if (StringUtil.isNotEmpty(folderType)) {
            condition.append(" and a.folderType = :folderType ");
            values.put("folderType", this.folderType);
        }
        if (EnumMonitor.EnumPointType.污染源.getValue().equals(pointType)) {
            condition.append(" and pointType in :pointTypeList");
            pointTypeList.add(EnumMonitor.EnumPointType.污染源.getValue());
            values.put("pointTypeList", pointTypeList);
        }
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (a.pointName like :key or a.pointCode like :key) ");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(enterpriseName)) {
            condition.append(" and b.name like :enterpriseName ");
            values.put("enterpriseName", "%" + this.enterpriseName + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleTypeId)) {
            condition.append(" and a.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        if (null != isEnabled) {
            if (0 == isEnabled) {
                condition.append(" and a.isEnabled = false ");
            } else {
                condition.append(" and a.isEnabled = true");
            }
        }
        if (StringUtil.isNotEmpty(entId)) {
            List<String> entIdList = Arrays.asList(entId.split(","));
            condition.append(" and a.enterpriseId in :entIds ");
            values.put("entIds", entIdList);
        }
        if (StringUtil.isNotEmpty(pollutionType)) {
            condition.append(" and a.folderType = :pollutionType ");
            values.put("pollutionType", this.pollutionType);
        }
        if (StringUtil.isNotEmpty(this.stationId) && !UUIDHelper.GUID_EMPTY.equals(this.stationId)) {
            condition.append(" and stationId = :stationId ");
            values.put("stationId", this.stationId);
        }
        if (StringUtil.isNotEmpty(ids)) {
            condition.append(" and a.id  in :ids ");
            values.put("ids", ids);
        }
        if (StringUtil.isNotEmpty(stationIds)) {
            condition.append(" and a.stationId  in :stationIds ");
            values.put("stationIds", stationIds);
        }
        if (StringUtil.isNotNull(isFilterEmptyTest) && isFilterEmptyTest) {
            condition.append(" and exists (select 1 from DtoFixedPoint2Test p2t where p2t.fixedPointId = a.id) ");
        }
        return condition.toString();
    }
}