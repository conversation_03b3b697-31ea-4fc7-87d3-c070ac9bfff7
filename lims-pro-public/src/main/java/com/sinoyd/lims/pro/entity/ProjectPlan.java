package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ProjectPlan实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ProjectPlan")
 @Data
 public  class ProjectPlan implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  ProjectPlan() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目id")
	private String projectId;
    
    /**
    * 项目负责人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目负责人id")
	private String leaderId;
    
    /**
    * 编制报告人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("编制报告人id")
	private String reportMakerId;
    
    /**
    * 编制方案人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("编制方案人id")
	private String schemeMakerId;
    
    /**
    * 现场负责人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("现场负责人id")
	private String spotPersonId;
    
    /**
    * 监督人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("监督人id")
	private String supervisorId;
    
    /**
    * 要求完成时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("要求完成时间")
	private Date deadLine;
    
    /**
    * 预计出具报告时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("预计出具报告时间")
	private Date reportDate;
    
    /**
    * 预计完成分析日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("预计完成分析日期")
	private Date requireAnalyzeDate;
    
    /**
    * 预计完成采样日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("预计完成采样日期")
	private Date requireSamplingDate;
    
    /**
    * 责任人（质量控制计划）
    */
    @Column(length=50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("责任人（质量控制计划）")
    @Length(message = "责任人（质量控制计划）{validation.message.length}", max = 50)
	private String responsePerson;
    
    /**
    * 测试项目或具体要求的文字说明
    */
    @Column(length=1000)
    @ApiModelProperty("测试项目或具体要求的文字说明")
    @Length(message = "测试项目或具体要求的文字说明{validation.message.length}", max = 1000)
	private String requires;
    
    /**
    * 测试标准文字说明
    */
    @Column(length=1000)
    @ApiModelProperty("测试标准文字说明")
    @Length(message = "测试标准文字说明{validation.message.length}", max = 1000)
	private String testMethodRequires;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
	private String remark;
    
    /**
    * 是否同意使用非标准方法
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否同意使用非标准方法")
	private Boolean isUseMethod;
    
    /**
    * 是否评价
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否评价")
	private Boolean isEvaluate;
    
    /**
    * 是否警告
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否警告")
	private Boolean isWarning;
    
    /**
    * 提前警告天数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("提前警告天数")
	private Integer warningDay;
    
    /**
    * 是否反馈
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否反馈")
	private Boolean isFeedback;
    
    /**
    * 是否拟订合同
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否拟订合同")
	private Boolean isContract;
    
    /**
    * 分包单位
    */
    @ApiModelProperty("分包单位")
    @Length(message = "分包单位{validation.message.length}", max = 255)
	private String subName;
    
    /**
    * 分包项目
    */
    @Column(length=1000)
    @ApiModelProperty("分包项目")
    @Length(message = "分包项目{validation.message.length}", max = 1000)
	private String subItems;
    
    /**
    * 分包方式(枚举EnumSubMethod:0.无1.客户指定2.本站联系)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("分包方式(枚举EnumSubMethod:0.无1.客户指定2.本站联系)")
	private Integer subMethod;
    
    /**
    * 分包情况(枚举EnumOutSourcing: 0.不分包1.全部分包2.部分分包)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("分包情况(枚举EnumOutSourcing: 0.不分包1.全部分包2.部分分包)")
	private Integer isOutsourcing;
    
    /**
    * 是否编制方案
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("是否编制方案")
	private Boolean isMakePlan;
    
    /**
    * 辅助编制报告人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("辅助编制报告人id")
	private String reportMakerIIId;
    
    /**
    * 质量任务等级（枚举EnumPorjectQCGrade 1.内部质控 2.外部质控 3.分包质控）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("质量任务等级（枚举EnumPorjectQCGrade 1.内部质控 2.外部质控 3.分包质控）")
	private Integer qcGrade;
    
    /**
    * 质量任务类型(内部质控EnumInnerQCType：1.人员比对 2.方法比对 3.仪器比对 4.标样考核 5.盲样考核。外部质控EnumOuterQCType：1.实验室间比对 2.能力验证3.测量审核 4.其他 。分包质控EnumSubQCType：1.社会实验室比对 2.社会实验室标样考核)
   
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("质量任务类型(内部质控EnumInnerQCType：1.人员比对 2.方法比对 3.仪器比对 4.标样考核 5.盲样考核。外部质控EnumOuterQCType：1.实验室间比对 2.能力验证3.测量审核 4.其他 。分包质控EnumSubQCType：1.社会实验室比对 2.社会实验室标样考核) ")
	private Integer qcType;
    
    /**
    * 质量来源（质量控制——外部质控）
    */
    @ApiModelProperty("质量来源（质量控制——外部质控）")
    @Length(message = "质量来源（质量控制——外部质控）{validation.message.length}", max = 255)
	private String qcSource;
    
    /**
    * 判断依据（质量控制计划）
    */
    @ApiModelProperty("判断依据（质量控制计划）")
    @Length(message = "判断依据（质量控制计划）{validation.message.length}", max = 255)
	private String judgment;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }