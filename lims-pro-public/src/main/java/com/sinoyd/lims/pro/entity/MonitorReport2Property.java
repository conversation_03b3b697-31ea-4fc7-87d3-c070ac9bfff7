package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * 报告与监测计划关联实体
 * <AUTHOR>
 * @version V1.0.0 2025/3/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description="MonitorReport2Property")
@Data
public class MonitorReport2Property extends LimsBaseEntity {

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 报告标识
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告标识")
    private String reportId;

    /**
     * 监测计划标识
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("监测计划标识")
    private String propertyId;
}
