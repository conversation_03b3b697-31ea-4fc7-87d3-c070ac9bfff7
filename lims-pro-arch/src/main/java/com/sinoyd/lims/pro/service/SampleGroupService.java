package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoSampleItemGroupTag;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.customer.DtoSampleLabelData;

import java.util.List;


/**
 * SampleGroup操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
public interface SampleGroupService extends IBaseJpaService<DtoSampleGroup, String> {

    /**
     * 根据送样单id查询
     *
     * @param criteria 送样单id
     * @return 样品分组信息列表
     */
    List<DtoSampleGroup> findForReceiveSampleRecord(BaseCriteria criteria);

    /**
     * 修改送样单对应的样品分组信息
     *
     * @param sampleGroup 样品分组信息
     */
    void updateForRecord(DtoSampleGroup sampleGroup);

    /**
     * 更新送样单下样品的SampleGroup信息
     *
     * @param criteria 前端传参对象
     */
    void updateGroupInfo(BaseCriteria criteria);

    /**
     * 根据样品ids获取分组信息
     *
     * @param sampleIds 样品ids
     * @return 分组信息
     */
    List<DtoSampleGroup> findBySampleIds(List<String> sampleIds);

    /**
     * 获取样品分组信息
     *
     * @param sampleIds 前端传参对象
     * @param groupInfo 是否按分组获取
     * @param groupIds  分组id
     * @return 分组后的信息
     */
    List<DtoSampleLabelData> findSampleLabelData(List<String> sampleIds, int groupInfo, List<String> groupIds);

    /**
     * 取出分组信息
     *
     * @param isGroup  是否分组显示
     * @param groupIds 选择的分组id
     * @return 返回分组信息
     */
    List<DtoSampleTypeGroup> findSampleTypeGroup(Boolean isGroup, List<String> groupIds);

    /**
     * 设置新增分组数据
     *
     * @param dtoSample                 样品信息
     * @param oldSampleGroupList        已经存在的分组信息
     * @param sampleLabelData           样品标签数据
     * @param analyseItemNames          测试项目名称
     * @param marks                     标记
     * @param needDeleteSampleGroupList 需要删除的分组信息列表
     * @param newSampleGroupList        需要新增的分组信息列表
     */
    void setGroupMsg(DtoSample dtoSample, List<DtoSampleGroup> oldSampleGroupList, List<DtoSampleGroup> remainOldSampleGroupList,
                     DtoSampleLabelData sampleLabelData, String analyseItemNames,
                     List<String> marks, List<DtoSampleGroup> needDeleteSampleGroupList,
                     List<DtoSampleGroup> newSampleGroupList, Integer isGroup, List<DtoSampleLabelData> dataList);

    /**
     * 判断指定送样单下是否存在sampleGroup数据
     *
     * @param receiveId 送样单id
     * @return 是否存在sampleGroup数据
     */
    Boolean checkExist(String receiveId);

    /**
     * 根据样品id查询
     *
     * @param criteria 送样单id
     * @return 样品分组信息列表
     */
    List<DtoSampleGroup> findBySamples(BaseCriteria criteria);

    /**
     * 查询样品因子对应的分组标识
     *
     * @param criteria 查询条件
     * @return 结果
     */
    List<DtoSampleItemGroupTag> findSampleItemGroupTag(BaseCriteria criteria);

    /**
     * 批量填写接样人接样时间
     *
     * @param sampleGroup 实体列表
     */
    void batchUpdate(DtoSampleGroup sampleGroup);

    /**
     * 根据送样单信息更新样品分组数据
     *
     * @param receiveSampleRecord 送样单信息
     */
    void updateByReceiveInfo(DtoReceiveSampleRecord receiveSampleRecord);

    void generateAndSetTaggedSampleCode(
            java.util.function.Consumer<String> setSampleCodeWithTag,
            String currentSampleCode,
            String sampleCodeTag,
            Integer sampleCategory,
            String associateSampleId,
            java.util.function.Function<String, java.util.Optional<String>> associatedSampleCodeFinder
    );
}