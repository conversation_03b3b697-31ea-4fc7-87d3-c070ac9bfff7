package com.sinoyd.lims.lim.criteria;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仪器使用记录查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentUseRecordCriteria extends BaseCriteria {

    /**
     * 仪器id（默认当前仪器的id）
     */
    private String instrumentId;
    /**
     * 检索开始时间
     */
    private String dtBegin;
    /**
     * 检索结束时间
     */
    private String dtEnd;
    /**
     * 使用人id
     */
    private String userId;
    /**
     * 环境记录id结合
     */
    private List<String>environmentalManageIds;

    /**
     * 是否过滤共享仪器
     */
    private Boolean isFilter = false;

    /**
     * 对象类型
     */
    private Integer objectType;

    /**
     * 关键字
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and x.instrumentId = i.id");
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and x.startTime >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and x.endTime < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(instrumentId) && !UUIDHelper.GUID_EMPTY.equals(this.instrumentId)) {
            condition.append(" and (x.instrumentId = :instrumentId)");
            values.put("instrumentId", this.instrumentId);
        }
        if (StringUtils.isNotNullAndEmpty(userId) && !UUIDHelper.GUID_EMPTY.equals(this.userId)) {
            condition.append(" and (x.usePersonId = :usePersonId)");
            values.put("usePersonId", this.userId);
        }
        if (StringUtil.isNotNull(environmentalManageIds) && environmentalManageIds.size() > 0) {
            condition.append(" and x.environmentalManageId in :environmentalManageIds");
            values.put("environmentalManageIds", this.environmentalManageIds);
        }
        if (isFilter) {
            condition.append(" and i.shareFlag = 1");
        }
        if (StringUtil.isNotNull(this.objectType)) {
            condition.append(" and x.objectType = :objectType");
            values.put("objectType", this.objectType);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (i.instrumentName like :key or i.model like :key or i.instrumentsCode like :key or i.serialNo like :key)");
            values.put("key", "%" + this.key + "%");
        }
        return condition.toString();
    }
}