package com.sinoyd.lims.pro.dto.customer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 分析人员工作日历
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
 @Data
 public  class DtoAnalyseCalendar {
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date analyzeTime;

     // 分析样品数
     private Integer awaitAnalyseNum;

     // 创建检测单数
     private Integer createWorkSheetNum;

     // 提交检测单次数
     private Integer submitWorkSheetNum;

     // 审核检测单次数
     private Integer auditWorkSheetNum;

     // 检测详情
     private List<DtoAnalyseStatistics> analyseItems=new ArrayList<>();
 }