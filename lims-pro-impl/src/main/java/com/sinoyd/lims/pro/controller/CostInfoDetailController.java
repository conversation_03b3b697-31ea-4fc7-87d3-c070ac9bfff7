package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.CostInfoDetailService;
import com.sinoyd.lims.pro.criteria.CostInfoDetailCriteria;
import com.sinoyd.lims.pro.dto.DtoCostInfoDetail;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * CostInfoDetail服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: CostInfoDetail服务")
 @RestController
 @RequestMapping("api/pro/costInfoDetail")
 public class CostInfoDetailController extends BaseJpaController<DtoCostInfoDetail, String,CostInfoDetailService> {


    /**
     * 分页动态条件查询CostInfoDetail
     * @param costInfoDetailCriteria 条件参数
     * @return RestResponse<List<CostInfoDetail>>
     */
     @ApiOperation(value = "分页动态条件查询CostInfoDetail", notes = "分页动态条件查询CostInfoDetail")
     @GetMapping
     public RestResponse<List<DtoCostInfoDetail>> findByPage(CostInfoDetailCriteria costInfoDetailCriteria) {
         PageBean<DtoCostInfoDetail> pageBean = super.getPageBean();
         RestResponse<List<DtoCostInfoDetail>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, costInfoDetailCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询CostInfoDetail
     * @param id 主键id
     * @return RestResponse<DtoCostInfoDetail>
     */
     @ApiOperation(value = "按主键查询CostInfoDetail", notes = "按主键查询CostInfoDetail")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoCostInfoDetail> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoCostInfoDetail> restResponse = new RestResponse<>();
         DtoCostInfoDetail costInfoDetail = service.findOne(id);
         restResponse.setData(costInfoDetail);
         restResponse.setRestStatus(StringUtil.isNull(costInfoDetail) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增CostInfoDetail
     * @param costInfoDetail 实体列表
     * @return RestResponse<DtoCostInfoDetail>
     */
     @ApiOperation(value = "新增CostInfoDetail", notes = "新增CostInfoDetail")
     @PostMapping
     public RestResponse<DtoCostInfoDetail> create(@RequestBody @Validated DtoCostInfoDetail costInfoDetail) {
         RestResponse<DtoCostInfoDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.save(costInfoDetail));
         return restResponse;
      }

     /**
     * 新增CostInfoDetail
     * @param costInfoDetail 实体列表
     * @return RestResponse<DtoCostInfoDetail>
     */
     @ApiOperation(value = "修改CostInfoDetail", notes = "修改CostInfoDetail")
     @PutMapping
     public RestResponse<DtoCostInfoDetail> update(@RequestBody @Validated DtoCostInfoDetail costInfoDetail) {
         RestResponse<DtoCostInfoDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.update(costInfoDetail));
         return restResponse;
      }

    /**
     * "根据id批量删除CostInfoDetail
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除CostInfoDetail", notes = "根据id批量删除CostInfoDetail")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }