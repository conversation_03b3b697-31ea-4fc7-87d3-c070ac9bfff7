package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoWorkSheet;

import java.util.List;


/**
 * WorkSheet数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface WorkSheetRepository extends IBaseJpaRepository<DtoWorkSheet, String> {

    /**
     * 找到检测单下的子检测单
     *
     * @param parentId 父级id
     * @return 返回子检测单数据
     */
    List<DtoWorkSheet> findByParentId(String parentId);

    /**
     * 找到检测单下的子检测单
     *
     * @param parentIds 父级ids
     * @return 返回子检测单数据
     */
    List<DtoWorkSheet> findByParentIdIn(List<String> parentIds);

    /**
     * 根据工作单id找到子检测单id
     *
     * @param parentId 工作单id
     * @param testIds  测试项目id
     * @return
     */
    List<DtoWorkSheet> findByParentIdAndTestIdIn(String parentId, List<String> testIds);

    /**
     * 根据测试项目id查询工作单
     *
     * @param testIds List<String> testIds 测试项目id列表
     * @return List<DtoWorkSheet>查找到的工作单列表
     */
    List<DtoWorkSheet> findByTestIdIn(List<String> testIds);
}