package com.sinoyd.lims.pro.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.DictDataProjectModel;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.boot.frame.sys.service.IDictDataProjectService;
import com.sinoyd.boot.frame.sys.service.IOrgService;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoSampleItemGroupTag;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost2Person;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost2Test;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestPost2PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestPost2TestRepository;
import com.sinoyd.lims.lim.service.AnalyzeMethodService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.AnalyseAwaitCriteria;
import com.sinoyd.lims.pro.criteria.SampleGroupCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseAwait;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseProgress;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseWorkView;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分析数据缓存操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/18
 * @since V100R001
 */
@Service
public class AnalyseDataCacheServiceImpl implements AnalyseDataCacheService {
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private IOrgService orgService;

    @Autowired
    @Lazy
    private HomeService homeService;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    @Lazy
    private AnalyzeMethodService analyzeMethodService;

    @Autowired
    @Lazy
    private CodeService codeService;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    @Lazy
    private QualityControlService qualityControlService;

    @Autowired
    private TestPost2TestRepository testPost2TestRepository;

    @Autowired
    private TestPost2PersonRepository testPost2PersonRepository;

    @Autowired
    @Lazy
    private IDictDataProjectService dictDataProjectService;

    private ProjectPlanRepository projectPlanRepository;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private SampleGroupService sampleGroupService;

    /**
     * 根据人员id 获取实验室分析工作总览信息
     *
     * @param personId 人员id
     * @return 工作总览信息
     */
    @Override
    public DtoAnalyseWorkView findAnalyseStatistics(String personId) {
        //分析日历的key
        String analyseCalendarKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AnalyseCalendar.getValue());
        //分析计划的key
        String analysePlanKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AnalysePlan.getValue());
        //分析统计的key
        String analyzeStatisticsKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AnalyzeStatistics.getValue());
        //分析状态的key
        String analyzeStatusKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AnalyzeStatus.getValue());
        Object analyseCalendarJson = redisTemplate.opsForHash().get(analyseCalendarKey, personId);
        List analyseCalendarMap = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(analyseCalendarJson)) {
            analyseCalendarMap = JsonIterator.deserialize((String) analyseCalendarJson, List.class);
        }
        Object analysePlanJson = redisTemplate.opsForHash().get(analysePlanKey, personId);
        List analysePlanMap = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(analysePlanJson)) {
            analysePlanMap = JsonIterator.deserialize((String) analysePlanJson, List.class);
        }

        Object analyzeStatisticsJson = redisTemplate.opsForHash().get(analyzeStatisticsKey, personId);
        List analyzeStatisticsMap = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(analyzeStatisticsJson)) {
            analyzeStatisticsMap = JsonIterator.deserialize((String) analyzeStatisticsJson, List.class);
        }

        Object analyzeStatusJson = redisTemplate.opsForHash().get(analyzeStatusKey, personId);
        Map<String, Object> analyzeStatusMap = new HashMap<>();
        if (StringUtils.isNotNullAndEmpty(analyzeStatusJson)) {
            analyzeStatusMap = JsonIterator.deserialize((String) analyzeStatusJson, Map.class);
        }
        DtoAnalyseWorkView dtoAnalyseWorkView = new DtoAnalyseWorkView();
        dtoAnalyseWorkView.setAnalyzeStatus(analyzeStatusMap);
        dtoAnalyseWorkView.setAnalyzeStatistics(analyzeStatisticsMap);
        dtoAnalyseWorkView.setAnalysePlan(analysePlanMap);
        dtoAnalyseWorkView.setAnalyseCalendar(analyseCalendarMap);
        //todo 沈飞， 是否默认把今天的日志数据带出来？？？
        return dtoAnalyseWorkView;
    }

    /**
     * 根据人员id 获取实验室分析工作进度
     *
     * @param personId 人员id
     * @return 工作进度
     */
    @Override
    public DtoAnalyseProgress findAnalyseProgress(String personId) {

        //审核检测单的key
        String auditWorkSheetDetailKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AuditWorkSheetDetail.getValue());

        //待检样品的key
        String awaitSampleDetailKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AwaitSampleDetail.getValue());

        //待检检测单的key
        String awaitWorkSheetDetailKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AwaitWorkSheetDetail.getValue());

        //已确认的key
        String finishWorkSheetDetailKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_FinishWorkSheetDetail.getValue());

        //待检样品的json
        Object awaitSampleDetailJson = redisTemplate.opsForHash().get(awaitSampleDetailKey, personId);

        Map<String, Object> awaitSampleMap = new HashMap<>();
        if (StringUtils.isNotNullAndEmpty(awaitSampleDetailJson)) {
            awaitSampleMap = JsonIterator.deserialize((String) awaitSampleDetailJson, Map.class);
        }
        //待检检测单的json
        Object awaitWorkSheetDetailJson = redisTemplate.opsForHash().get(awaitWorkSheetDetailKey, personId);
        Map<String, Object> awaitWorkSheetDetailMap = new HashMap<>();
        if (StringUtils.isNotNullAndEmpty(awaitWorkSheetDetailJson)) {
            awaitWorkSheetDetailMap = JsonIterator.deserialize((String) awaitWorkSheetDetailJson, Map.class);
        }

        //待审核检测单的json
        Object auditWorkSheetDetailJson = redisTemplate.opsForHash().get(auditWorkSheetDetailKey, personId);
        Map<String, Object> auditWorkSheetDetailMap = new HashMap<>();
        if (StringUtils.isNotNullAndEmpty(auditWorkSheetDetailJson)) {
            auditWorkSheetDetailMap = JsonIterator.deserialize((String) auditWorkSheetDetailJson, Map.class);
        }
        //已确认的检测单的json
        Object finishWorkSheetDetailJson = redisTemplate.opsForHash().get(finishWorkSheetDetailKey, personId);
        Map<String, Object> finishWorkSheetDetailMap = new HashMap<>();
        if (StringUtils.isNotNullAndEmpty(finishWorkSheetDetailJson)) {
            finishWorkSheetDetailMap = JsonIterator.deserialize((String) finishWorkSheetDetailJson, Map.class);
        }
        DtoAnalyseProgress dtoAnalyseProgress = new DtoAnalyseProgress();
        dtoAnalyseProgress.setAwaitSample(awaitSampleMap);
        dtoAnalyseProgress.setAwaitWorkSheet(awaitWorkSheetDetailMap);
        dtoAnalyseProgress.setAuditWorkSheet(auditWorkSheetDetailMap);
        dtoAnalyseProgress.setFinishWorkSheet(finishWorkSheetDetailMap);
        return dtoAnalyseProgress;
    }

    /**
     * 获取待检样品的数据
     *
     * @param baseCriteria 查询条件
     * @return 返回待检分析样品数据
     */
    @Override
    public List<DtoAnalyseAwait> findWaitAnalyseDataByPersonIdAndTestId(BaseCriteria baseCriteria) {
        AnalyseAwaitCriteria criteria = (AnalyseAwaitCriteria) baseCriteria;
        AnalyseAwaitCriteria analyseAwaitCriteria = new AnalyseAwaitCriteria();
        //判断是否按岗位分配
        boolean allocateByPost = false;
        //判断是否隐藏受检单位信息
        boolean isHideInspected = false;
        DtoCode dtoCode = codeService.findByCode(ProCodeHelper.PRO_AnalyseAllocationRules_Post);
        DtoCode showInspectedCode = codeService.findByCode("PRO_IS_SHOW_FOLDER");
        if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
            allocateByPost = true;
        }
        if (StringUtil.isNotNull(showInspectedCode) && "1".equals(showInspectedCode.getDictValue())) {
            isHideInspected = true;
        }
        //按岗位分配时不添加人员查询条件
        if (!allocateByPost) {
            analyseAwaitCriteria.setPersonId(criteria.getPersonId());
        }
        analyseAwaitCriteria.setTestId(criteria.getTestId());
        analyseAwaitCriteria.setSampleTypeId(criteria.getSampleTypeId());
        analyseAwaitCriteria.setSampleKey(criteria.getSampleKey());
        analyseAwaitCriteria.setProjectKey(criteria.getProjectKey());
        analyseAwaitCriteria.setSamplingTimeBegin(criteria.getSamplingTimeBegin());
        analyseAwaitCriteria.setSamplingTimeEnd(criteria.getSamplingTimeEnd());

        List<DtoAnalyseAwait> analyseAwaits = this.findAnalyseAwaitList(analyseAwaitCriteria);

        List<String> sampleIds = analyseAwaits.stream().map(DtoAnalyseAwait::getSampleId).collect(Collectors.toList());
        List<String> projectIdList = analyseAwaits.stream().map(DtoAnalyseAwait::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProjectPlan> projectPlanList = StringUtil.isNotEmpty(projectIdList) ? projectPlanRepository.findByProjectIdIn(projectIdList) : new ArrayList<>();
        Map<String, List<DtoProjectPlan>> projectPlanMap = projectPlanList.stream().collect(Collectors.groupingBy(DtoProjectPlan::getProjectId));
        List<DtoSample> samples = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleIds)) {
            samples = sampleService.findAll(sampleIds);
        }

        List<DtoQualityControl> qcList = new ArrayList<>();
        List<String> qcIds = samples.stream().map(DtoSample::getQcId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(qcIds)) {
            qcList = qualityControlService.findAll(qcIds);
        }
        List<String> receiveIds = analyseAwaits.stream().map(DtoAnalyseAwait::getReceiveId).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordService.findAll(receiveIds);
        List<DtoSample> finalSamples = samples;
        List<DtoQualityControl> finalQcList = qcList;
        boolean finalIsHideInspected = isHideInspected;
        Set<String> sampleTypeIds = analyseAwaits.stream().map(DtoAnalyseAwait::getSampleTypeId).collect(Collectors.toSet());
        List<DtoSampleType> sampleTypeList = sampleTypeService.findAll(sampleTypeIds);
        SampleGroupCriteria sampleGroupCriteria = new SampleGroupCriteria();
        sampleGroupCriteria.setSampleIds(sampleIds);
        List<String> testIds = analyseAwaits.stream().map(DtoAnalyseAwait::getTestId).collect(Collectors.toList());
        sampleGroupCriteria.setTestIds(testIds);
        List<DtoSampleItemGroupTag> sampleItemGroupTagList = sampleGroupService.findSampleItemGroupTag(sampleGroupCriteria);
        List<DtoAnalyseAwait> finalAnalyseAwaits = analyseAwaits;
        analyseAwaits.forEach(p -> {
            Optional<DtoReceiveSampleRecord> receiveSampleRecord = receiveSampleRecords.stream().filter(r -> r.getId().equals(p.getReceiveId())).findFirst();
            receiveSampleRecord.ifPresent(r -> {
                p.setSenderId(r.getSenderId());
                p.setSenderName(r.getSenderName());
            });
            p.setIsFinishPrePare(EnumPreParedStatus.已制备.getValue().equals(p.getPreparedStatus()));
            Optional<DtoSampleType> sampleTypeOptional = sampleTypeList.stream().filter(type -> type.getId().equals(p.getSampleTypeId())).findFirst();
            sampleTypeOptional.ifPresent(type -> {
                p.setSampleTypeName(type.getTypeName());
            });
            if (finalIsHideInspected) {
                p.setInspectedEnt("/");
                p.setRedFolderName("/");
            }
            if (StringUtils.isNotNullAndEmpty(p.getSampleId())) {
                Optional<DtoSample> sampleOptional = finalSamples.stream().filter(s -> p.getSampleId().equals(s.getId())).findFirst();
                sampleOptional.ifPresent(sample -> {
                    if (StringUtil.isNotEmpty(sample.getQcId())) {
                        if (finalIsHideInspected) {
                            p.setRedFolderName("/");
                        }
                        if (!UUIDHelper.GUID_EMPTY.equals(sample.getQcId())) {
                            Optional<DtoQualityControl> qcOptional = finalQcList.stream().filter(q -> sample.getQcId().equals(q.getId())).findFirst();
                            qcOptional.ifPresent(qc -> {
                                Integer qcType = qc.getQcType();
                                Integer qcGrade = qc.getQcGrade();
                                p.setQcGrade(qcGrade);
                                p.setQcType(qcType);
                                if (finalIsHideInspected) {
                                    if ((EnumLIM.EnumQCGrade.外部质控.getValue().equals(qcGrade) && EnumLIM.EnumQCType.空白.getValue().equals(qcType))
                                            || (EnumLIM.EnumQCGrade.外部质控.getValue().equals(qcGrade) && EnumLIM.EnumQCType.运输空白.getValue().equals(qcType))) {
                                        p.setRedFolderName(sample.getRedFolderName());
                                    }
                                }
                            });
                        } else {
                            p.setQcGrade(sample.getQcGrade());
                            p.setQcType(sample.getQcType());
                        }
                    }
                });
            }
            if (projectPlanMap.containsKey(p.getProjectId())) {
                List<DtoProjectPlan> planList = projectPlanMap.get(p.getProjectId());
                if (StringUtil.isNotEmpty(planList)) {
                    p.setDeadLine(planList.get(0).getDeadLine());
                }
            }
            DtoSampleItemGroupTag sampleItemGroupTag = sampleItemGroupTagList.stream().filter(d -> p.getSampleId().equals(d.getSampleId())
                    && p.getTestId().equals(d.getTestId())).findFirst().orElse(null);
            p.setSampleCodeWithTag(p.getSampleCode());
            if (sampleItemGroupTag != null) {
                sampleGroupService.generateAndSetTaggedSampleCode(
                        p::setSampleCodeWithTag,
                        p.getSampleCode(),
                        sampleItemGroupTag.getSampleCodeTag(),
                        p.getSampleCategory(),
                        p.getAssociateSampleId(),
                        yySampleId -> finalAnalyseAwaits.stream()
                                .filter(await -> yySampleId.equals(await.getSampleId()))
                                .findFirst()
                                .map(DtoAnalyseAwait::getSampleCode)
                );
            }
        });
        analyseAwaits = analyseAwaits.stream().sorted(Comparator.comparing(DtoAnalyseAwait::getSampleTime, Comparator.reverseOrder())
                .thenComparing(DtoAnalyseAwait::getSampleTypeName).thenComparing(DtoAnalyseAwait::getSampleCode)
                .thenComparing(DtoAnalyseAwait::getGrade, Comparator.reverseOrder())).collect(Collectors.toList());
        return analyseAwaits;
    }

    /**
     * 查询待检测数据（构造方法）
     *
     * @param analyseAwaitCriteria 查询条件
     * @return 待检测数据
     */
    protected List<DtoAnalyseAwait> findAnalyseAwaitList(AnalyseAwaitCriteria analyseAwaitCriteria) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoAnalyseAwait(");
        stringBuilder.append("a.id,a.redAnalyzeItemName,a.analyseItemId,a.redCountryStandard,");
        stringBuilder.append("a.redAnalyzeMethodName,a.analyzeMethodId,a.sampleId,");
        stringBuilder.append("a.requireDeadLine,b.remark,b.code,b.redFolderName,b.inspectedEnt,");
        stringBuilder.append("b.samplingTimeBegin,a.testId,b.receiveId,c.recordCode,c.receiveSampleDate," +
                "b.sampleTypeId,'',d.grade,d.projectName,d.projectCode,b.preparedStatus, d.id, a.sampleReceiveDate," +
                "b.sampleCategory,b.associateSampleId)");
        stringBuilder.append(" from DtoAnalyseData a, DtoSample b,DtoReceiveSampleRecord c,DtoProject d");
        stringBuilder.append(" where 1=1 and a.isDeleted = 0 ");
        return commonRepository.find(stringBuilder.toString(), analyseAwaitCriteria);
    }

    /**
     * 刷新实验室分析缓存
     */
    @Override
    public void refreshAnalyseCache() {
        Wrapper<OrgModel> ew = new EntityWrapper<>();
        List<OrgModel> orgList = orgService.selectList(ew);
        for (OrgModel org : orgList) {
            String orgId = org.getId();
            //分析日历清除数据
            String analyseCalendarKey = EnumPRORedis.PRO_OrgId_AnalyseCalendar.getValue().replace("{0}", orgId);
            redisTemplate.delete(analyseCalendarKey);
            //已完成检测单清除数据
            String finishKey = EnumPRORedis.PRO_OrgId_FinishWorkSheetDetail.getValue().replace("{0}", orgId);
            redisTemplate.delete(finishKey);
            //分析状态将已完成清零
            String analyzeStatusKey = EnumPRORedis.PRO_OrgId_AnalyzeStatus.getValue().replace("{0}", orgId);
            Set<String> personIds = redisTemplate.opsForHash().keys(analyzeStatusKey);
            for (String personId : personIds) {
                Object analyzeStatusJson = redisTemplate.opsForHash().get(analyzeStatusKey, personId);
                Map<String, Object> analyzeStatusMap = new HashMap<>();
                if (StringUtils.isNotNullAndEmpty(analyzeStatusJson)) {
                    analyzeStatusMap = JsonIterator.deserialize((String) analyzeStatusJson, Map.class);
                } else {
                    analyzeStatusMap.put("awaitSample", 0);
                    analyzeStatusMap.put("awaitWorkSheet", 0);
                    analyzeStatusMap.put("auditWorkSheet", 0);
                }
                analyzeStatusMap.put("finishWorkSheet", 0);
                redisTemplate.opsForHash().put(analyzeStatusKey, personId, JsonStream.serialize(analyzeStatusMap));
            }
            //分析统计清除数据
            String analyzeStatisticsKey = EnumPRORedis.PRO_OrgId_AnalyzeStatistics.getValue().replace("{0}", orgId);
            redisTemplate.delete(analyzeStatisticsKey);
        }
    }


    /**
     * 保存分析人员的缓存信息
     *
     * @param analyseId 分析人员id
     * @param orgId     组织机构id
     */
    @Transactional
    @Override
    @Async
    public void saveAnalyseDataCache(String analyseId, String orgId) {
        saveAwaitInfo(analyseId, orgId);
        saveAnalyseInfo(analyseId, orgId);
    }


    /**
     * 保存缓存的待检、未分配的样品的数据
     *
     * @param analystId 分析人员id
     */
    @Transactional
    @Override
    @Async
    public void saveAwaitInfo(String analystId, String orgId) {
        //判断“按岗位分配”开关是否开启
        boolean allocateByPost = getAllocateByPost(orgId);
        List<DtoAnalyseData> datas = findAnalyseAwaitData(orgId, analystId);
        List<DtoAnalyseData> newDatas = getAnalyseNewData(datas);

        //按岗位分配开关开启时，需要需要根据人员id配置的所有岗位进行过滤
        newDatas = filterDataByPost(allocateByPost, analystId, newDatas);

        List<Map<String, Object>> awaitSampleDetailList = saveAwaitSampleRedis(newDatas, analystId, orgId);
        int totalSampleNum = 0;
        for (Map<String, Object> map : awaitSampleDetailList) {
            int sampleNum = (int) map.get("sampleNum");
            totalSampleNum += sampleNum;
        }
        //缓存首页实验室分析任务数量
        homeService.cacheAnalysTask(analystId, orgId, EnumLIM.EnumHomeTaskModule.实验室待检.getValue(), totalSampleNum);

        //分析状态
        Integer awaitSample = newDatas.stream().mapToInt(p -> (p.getInnerReceiveStatus().equals(EnumInnerReceiveStatus.已经领取.getValue()) && !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())) ? 1 : 0).sum();
        this.saveAnalyzeStatus(analystId, orgId, awaitSample);

        //分析计划
        this.saveAnalysePlan(analystId, orgId,
                newDatas.stream().filter(p -> (p.getInnerReceiveStatus().equals(EnumInnerReceiveStatus.可以领取.getValue())
                        || p.getInnerReceiveStatus().equals(EnumInnerReceiveStatus.不能领取.getValue())) && !p.getSampleCode().equals("")).collect(Collectors.toList()));
    }

    /**
     * 获取所有的待检测样品数据
     *
     * @param orgId 组织Id
     * @return 待检测数据
     */
    protected List<DtoAnalyseData> findAnalyseAwaitData(String orgId, String analystId) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select a.testId,a.redAnalyzeItemName,a.analyzeMethodId,a.redAnalyzeMethodName,");
        stringBuilder.append("a.redCountryStandard,a.grade,a.analyseItemId,");
        stringBuilder.append("s.code,s.receiveId,s.innerReceiveStatus,st.typeName,a.lowerLimit");
        stringBuilder.append(" from DtoAnalyseData a,DtoSample s,DtoSampleType st where 1=1");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and a.sampleId = s.id");
        stringBuilder.append(" and s.sampleTypeId = st.id");
        stringBuilder.append(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        stringBuilder.append(" and a.isCompleteField = 0");
        if (StringUtils.isNotNullAndEmpty(orgId)) {
            stringBuilder.append(" and a.orgId = :orgId");
            stringBuilder.append(" and s.orgId = :orgId");
            stringBuilder.append(" and st.orgId = :orgId");
            values.put("orgId", orgId);
        }
        stringBuilder.append(" and a.workSheetId = :workSheetId");
        values.put("workSheetId", UUIDHelper.GUID_EMPTY);
        stringBuilder.append(" and a.workSheetFolderId = :workSheetFolderId");
        values.put("workSheetFolderId", UUIDHelper.GUID_EMPTY);
        stringBuilder.append(" and a.dataStatus = :dataStatus");
        values.put("dataStatus", EnumAnalyseDataStatus.未测.getValue());
        stringBuilder.append(" and (s.samplingStatus = :samplingStatus1 or s.samplingStatus = :samplingStatus2)");
        values.put("samplingStatus1", EnumSamplingStatus.已经完成取样.getValue());
        values.put("samplingStatus2", EnumSamplingStatus.采样中.getValue());
        stringBuilder.append(" and s.status <> :status");
        values.put("status", EnumSampleStatus.样品作废.toString());
        stringBuilder.append(" and a.analystId = :analystId");
        values.put("analystId", analystId);
        return commonRepository.find(stringBuilder.toString(), values);
    }

    /**
     * 保存待检样品的Redis
     *
     * @param newDatas  需要保存的数据
     * @param analystId 监测人员
     * @param orgId     组织Id
     * @return 保存的Redis数据
     */
    protected List<Map<String, Object>> saveAwaitSampleRedis(List<DtoAnalyseData> newDatas, String analystId, String orgId) {
        List<Map<String, Object>> awaitSampleDetailList = new ArrayList<>();
        //根据测试项目分组数据，并计算样品数量
        Map<String, List<DtoAnalyseData>> anaMapByTestId = newDatas.stream().filter(p -> p.getInnerReceiveStatus().equals(EnumInnerReceiveStatus.已经领取.getValue())
                && !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())).
                collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.toList()));
        anaMapByTestId.forEach((testId, list) -> {
            Map<String, Object> awaitSampleDetail = new HashMap<>();
            awaitSampleDetail.put("testId", testId);
            awaitSampleDetail.put("redAnalyzeItemName", list.get(0).getRedAnalyzeItemName());
            awaitSampleDetail.put("analyzeMethodId", list.get(0).getAnalyzeMethodId());
            awaitSampleDetail.put("redAnalyzeMethodName", list.get(0).getRedAnalyzeMethodName());
            awaitSampleDetail.put("redCountryStandard", list.get(0).getRedCountryStandard());
            awaitSampleDetail.put("grade", list.get(0).getGrade());
            awaitSampleDetail.put("remark", "");
            awaitSampleDetail.put("sampleNum", list.size());
            awaitSampleDetailList.add(awaitSampleDetail);
        });

        //待检样品
        Collator comparator = Collator.getInstance(Locale.CHINA);
        awaitSampleDetailList.sort(Comparator.comparing(AnalyseDataCacheServiceImpl::comparingByRedAnalyzeItemName, comparator));
        this.saveCardInfo(analystId, EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AwaitSampleDetail.getValue(), orgId),
                "awaitSampleCount", "awaitSampleDetail", awaitSampleDetailList);

        return awaitSampleDetailList;
    }

    /**
     * 根据岗位过滤数据
     *
     * @param allocateByPost 是否按岗位分配
     * @param analystId      当前人员
     * @param newDatas       需要过滤的数据
     * @return 过滤后的数据
     */
    protected List<DtoAnalyseData> filterDataByPost(Boolean allocateByPost, String analystId, List<DtoAnalyseData> newDatas) {
        //按岗位分配开关开启时，需要需要根据人员id配置的所有岗位进行过滤
        if (allocateByPost) {
            List<String> testPostIdList = new ArrayList<>();
            //默认按照当前人员配置的所有岗位进行过滤
            if (StringUtil.isNotNull(analystId)) {
                List<DtoTestPost2Person> testPost2PersonList = testPost2PersonRepository.findByPersonId(analystId);
                testPostIdList = testPost2PersonList.stream().map(DtoTestPost2Person::getTestPostId).distinct().collect(Collectors.toList());
            }
            List<DtoTestPost2Test> testPost2TestList = StringUtil.isNotEmpty(testPostIdList) ? testPost2TestRepository.findByTestPostIdIn(testPostIdList) : new ArrayList<>();
            List<String> testIdList = testPost2TestList.stream().map(DtoTestPost2Test::getTestId).distinct().collect(Collectors.toList());
            newDatas = newDatas.stream().filter(p -> testIdList.contains(p.getTestId())).collect(Collectors.toList());
        }
        return newDatas;
    }

    /**
     * 获取测试项目处理后的数据
     *
     * @param datas 原数据
     * @return 处理后的测试项目数据
     */
    private List<DtoAnalyseData> getAnalyseNewData(List<DtoAnalyseData> datas) {
        List<DtoAnalyseData> newDatas = new ArrayList<>();

        Iterator<DtoAnalyseData> ite = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoAnalyseData analyseData = new DtoAnalyseData();
            String testId = (String) objs[0];
            String redAnalyzeItemName = (String) objs[1];
            String analyzeMethodId = (String) objs[2];
            String redAnalyzeMethodName = (String) objs[3];
            String redCountryStandard = (String) objs[4];
            Integer grade = (Integer) objs[5];
            String analyseItemId = (String) objs[6];
            String code = (String) objs[7];
            String receiveId = (String) objs[8];
            Integer innerReceiveStatus = (Integer) objs[9];
            String sampleTypeName = (String) objs[10];
            String lowerLimit = (String) objs[11];
            analyseData.setTestId(testId);
            analyseData.setRedAnalyzeItemName(redAnalyzeItemName);
            analyseData.setAnalyzeMethodId(analyzeMethodId);
            analyseData.setRedAnalyzeMethodName(redAnalyzeMethodName);
            analyseData.setRedCountryStandard(redCountryStandard);
            analyseData.setGrade(grade);
            analyseData.setAnalyseItemId(analyseItemId);
            analyseData.setSampleCode(code);
            analyseData.setReceiveId(receiveId);
            analyseData.setInnerReceiveStatus(innerReceiveStatus);
            analyseData.setSampleTypeName(sampleTypeName);
            analyseData.setLowerLimit(lowerLimit);
            newDatas.add(analyseData);
        }
        return newDatas;
    }

    /**
     * 写入检测中及之后的数据
     *
     * @param analystId 分析人员id
     */
    @Transactional
    @Override
    @Async
    public void saveAnalyseInfo(String analystId, String orgId) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select a.dataStatus,a.grade,a.redAnalyzeItemName,a.redAnalyzeMethodName,");
        stringBuilder.append("a.redCountryStandard,a.analyseItemId,a.analyzeTime,a.testId,w.analyzeTime,w.id,a.lowerLimit");
        stringBuilder.append(" from DtoAnalyseData a,DtoWorkSheetFolder w,DtoSample s where 1=1 ");
        stringBuilder.append(" and a.workSheetFolderId = w.id");
        stringBuilder.append(" and a.sampleId = s.id");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and w.isDeleted = 0");
        if (StringUtils.isNotNullAndEmpty(orgId)) {
            stringBuilder.append(" and a.orgId = :orgId");
            stringBuilder.append(" and w.orgId = :orgId");
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", orgId);
        }
        stringBuilder.append(" and a.analystId = :analystId");
        values.put("analystId", analystId);
        //当前时间
        Date nowDate = DateUtil.stringToDate(DateUtil.nowTime(DateUtil.YEAR), DateUtil.YEAR);

        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(nowDate);
        endCalendar.add(Calendar.DAY_OF_YEAR, 1);

        //当前时间往前推一个月
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(nowDate);
        startCalendar.set(Calendar.MONTH, -1);

        Date monthStart = startCalendar.getTime();
        Date monthEnd = endCalendar.getTime();

        //读取当前时间往前推一个月的数据或流转中的数据
        StringBuilder condition = new StringBuilder(" and ((w.analyzeTime >=:monthStart and w.analyzeTime < :monthEnd)");
        condition.append(" or w.workStatus < :workStatus)");
        values.put("monthStart", monthStart);
        values.put("monthEnd", monthEnd);
        values.put("workStatus", EnumWorkSheetStatus.审核通过.getValue());
        stringBuilder.append(condition);
        List<DtoAnalyseData> list = commonRepository.find(stringBuilder.toString(), values);
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        //工作单的数据要单独找，防止数据全部剔除，
        StringBuilder workSheetBuilder = new StringBuilder("select w from DtoWorkSheetFolder w where 1=1 and w.isDeleted = 0 ");
        workSheetBuilder.append(" and w.analystId = :analystId");
        workSheetBuilder.append(condition);
        values.clear();
        values.put("analystId", analystId);
        values.put("monthStart", monthStart);
        values.put("monthEnd", monthEnd);
        values.put("workStatus", EnumWorkSheetStatus.审核通过.getValue());
        List<DtoWorkSheetFolder> workSheetFolderSet = commonRepository.find(workSheetBuilder.toString(), values);

        Iterator<DtoAnalyseData> ite = list.iterator();
        // 循环迭代获取JPQL中查询返回的属性
        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoAnalyseData analyseData = new DtoAnalyseData();
            Integer dataStatus = (Integer) objs[0];
            Integer grade = (Integer) objs[1];
            String redAnalyzeItemName = (String) objs[2];
            String redAnalyzeMethodName = (String) objs[3];
            String redCountryStandard = (String) objs[4];
            String analyseItemId = (String) objs[5];
            Date analyzeTime = (Date) objs[6];
            String testId = (String) objs[7];
            Date workSheetFolderAnalyzeTime = (Date) objs[8];
            String workSheetFolderId = (String) objs[9];
            String lowerLimit = (String) objs[10];
            analyseData.setDataStatus(dataStatus);
            analyseData.setGrade(grade);
            analyseData.setRedAnalyzeItemName(redAnalyzeItemName);
            analyseData.setRedAnalyzeMethodName(redAnalyzeMethodName);
            analyseData.setRedCountryStandard(redCountryStandard);
            analyseData.setAnalyseItemId(analyseItemId);
            analyseData.setTestId(testId);
            analyseData.setAnalyzeTime(analyzeTime);
            analyseData.setLowerLimit(lowerLimit);
            //赋给analyzeTime的话框架会自动修改
            analyseData.setFolderAnalyzeTime(workSheetFolderAnalyzeTime);
            analyseData.setWorkSheetFolderId(workSheetFolderId);
            analyseDataList.add(analyseData);
        }
        //分析方法ids
        List<String> analyzeMethodIds = workSheetFolderSet.parallelStream().map(DtoWorkSheetFolder::getAnalyzeMethodId).distinct().collect(Collectors.toList());
        //相关的方法ids
        List<DtoAnalyzeMethod> methods = new ArrayList<>();
        if (analyzeMethodIds.size() > 0) {
            methods = analyzeMethodService.findAllDeleted(analyzeMethodIds);
        }

        Integer awaitWorkSheet = 0;//检测中的数量
        Integer finishWorkSheet = 0;//已完成的数量
        List<Map<String, Object>> awaitWorkSheetList = new ArrayList<>();
        List<Map<String, Object>> finishWorkSheetList = new ArrayList<>();
        for (DtoWorkSheetFolder workSheetFolder : workSheetFolderSet) {
            Map<String, Object> mapObject = this.getMapInfo(workSheetFolder);

            //计算检测中、审核中、已完成的数量
            List<DtoAnalyseData> dataList = new ArrayList<>();
            if ((workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.检测单拒绝.getValue()) ||
                    workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.新建.getValue()) ||
                    workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.已经保存.getValue()))) {
                dataList = analyseDataList.stream().filter(p -> p.getWorkSheetFolderId().equals(workSheetFolder.getId())).collect(Collectors.toList());
                awaitWorkSheet += dataList.size();
            } else if (!workSheetFolder.getAnalyzeTime().before(monthStart) && workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.审核通过.getValue())) {
                dataList = analyseDataList.stream().filter(p -> p.getWorkSheetFolderId().equals(workSheetFolder.getId())).collect(Collectors.toList());
                finishWorkSheet += dataList.stream().mapToInt(p -> !workSheetFolder.getAnalyzeTime().before(monthStart) ? 1 : 0).sum();
            }

            //写入样品数
            mapObject.put("sampleNum", dataList.size());
            if ((workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.检测单拒绝.getValue()) ||
                    workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.新建.getValue()) ||
                    workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.已经保存.getValue()))) {
                //待检检测单的样品数为数据状态处于待检中的数据的个数
                List<Integer> statusList = Arrays.asList(EnumAnalyseDataStatus.未测.getValue(), EnumAnalyseDataStatus.在测.getValue(), EnumAnalyseDataStatus.拒绝.getValue());
                mapObject.put("sampleNum", dataList.stream().mapToInt(p -> statusList.contains(p.getDataStatus()) ? 1 : 0).sum());
            }

            //写入指标相关信息
            if (dataList.size() > 0) {
                mapObject.put("grade", dataList.stream().sorted(Comparator.comparing(DtoAnalyseData::getGrade).reversed()).collect(Collectors.toList()).get(0).getGrade());
                mapObject.put("redAnalyzeItemName", String.join(",", dataList.stream().map(DtoAnalyseData::getRedAnalyzeItemName).collect(Collectors.toSet())));
                mapObject.put("redAnalyzeMethodName", dataList.get(0).getRedAnalyzeMethodName());
                mapObject.put("redCountryStandard", dataList.get(0).getRedCountryStandard());
            } else {
                mapObject.put("grade", EnumProjectGrade.一般.getValue());
                String redAnalyzeItemName = "";
                String redCountryStandard = "";
                DtoAnalyzeMethod method = methods.stream().filter(p -> p.getId().equals(workSheetFolder.getAnalyzeMethodId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(method)) {
                    redAnalyzeItemName = method.getMethodName();
                    redCountryStandard = method.getCountryStandard();
                }
                mapObject.put("redAnalyzeItemName", redAnalyzeItemName);
                mapObject.put("redCountryStandard", redCountryStandard);
            }
            if (workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.检测单拒绝.getValue()) ||
                    workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.新建.getValue()) ||
                    workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.已经保存.getValue())) {
                //纳入待检的检测单信息
                awaitWorkSheetList.add(mapObject);
            } else if (workSheetFolder.getAnalyzeTime().compareTo(monthStart) >= 0
                    && workSheetFolder.getAnalyzeTime().compareTo(monthEnd) < 0
                    && workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.审核通过.getValue())) {
                //仅分析时间为当前时间往前推一个月数据的才纳入已完成的检测单中
                finishWorkSheetList.add(mapObject);
            }
        }

        //待检检测单
        awaitWorkSheetList.sort(Comparator.comparing(AnalyseDataCacheServiceImpl::comparingByGrade).reversed().thenComparing(AnalyseDataCacheServiceImpl::comparingByAnalyzeTime)
                .thenComparing(AnalyseDataCacheServiceImpl::comparingByRedAnalyzeItemName));
        this.saveCardInfo(analystId, EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AwaitWorkSheetDetail.getValue(), orgId),
                "awaitWorkSheetCount", "awaitWorkSheetDetail", awaitWorkSheetList);

        //缓存首页实验室分析任务数量
        homeService.cacheAnalysTask(analystId, orgId, EnumLIM.EnumHomeTaskModule.实验室检测.getValue(), awaitWorkSheetList.size());

        //已确认检测单
        finishWorkSheetList.sort(Comparator.comparing(AnalyseDataCacheServiceImpl::comparingByAnalyzeTime)
                .thenComparing(AnalyseDataCacheServiceImpl::comparingByRedAnalyzeItemName));
        this.saveCardInfo(analystId, EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_FinishWorkSheetDetail.getValue(), orgId),
                "finishWorkSheetCount", "finishWorkSheetDetail", finishWorkSheetList);

        //分析状态
        this.saveAnalyzeStatus(analystId, orgId, null, awaitWorkSheet, null, finishWorkSheet);

        //分析日历
        this.saveAnalyseCalendar(analystId, orgId, new ArrayList<>(workSheetFolderSet), analyseDataList.stream().filter(p -> !p.getFolderAnalyzeTime().before(monthStart)).collect(Collectors.toList()));

        //分析统计
        this.saveAnalyzeStatistics(analystId, orgId,
                analyseDataList.stream().filter(p -> !p.getFolderAnalyzeTime().before(monthStart)).collect(Collectors.toList()));
    }

    /**
     * 写入检测单复核审核的数据
     *
     * @param personId 人员id
     */
    @Override
    @Async
    public void saveAuditInfo(String personId, String orgId) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select a,w");
        stringBuilder.append(" from DtoAnalyseData a,DtoWorkSheetFolder w,DtoSample s where 1=1");
        if (StringUtils.isNotNullAndEmpty(orgId)) {
            stringBuilder.append(" and a.orgId = :orgId");
            stringBuilder.append(" and w.orgId = :orgId");
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", orgId);
        }
        stringBuilder.append(" and a.workSheetFolderId = w.id");
        stringBuilder.append(" and a.sampleId = s.id");
        stringBuilder.append(" and s.isDeleted = 0 and a.isDeleted = 0 and w.isDeleted = 0 ");
        stringBuilder.append(" and (w.checkerId = :personId or w.auditorId = :personId)");
        values.put("personId", personId);
        stringBuilder.append(" and (w.workStatus = :workStatus1 or w.workStatus = :workStatus2)");
        values.put("workStatus1", EnumWorkSheetStatus.已经提交.getValue());
        values.put("workStatus2", EnumWorkSheetStatus.复核通过.getValue());
        List<DtoAnalyseData> list = commonRepository.find(stringBuilder.toString(), values);

        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        Set<DtoWorkSheetFolder> workSheetFolderSet = new HashSet<>();

        Iterator<DtoAnalyseData> ite = list.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoAnalyseData analyseData = (DtoAnalyseData) objs[0];
            DtoWorkSheetFolder workSheetFolder = (DtoWorkSheetFolder) objs[1];

            //状态为已提交且复核人为当前人员，或状态为复核通过且审核人为当前人员
            Boolean flag = (workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.已经提交.getValue()) && workSheetFolder.getCheckerId().equals(personId)) ||
                    (workSheetFolder.getWorkStatus().equals(EnumWorkSheetStatus.复核通过.getValue()) && workSheetFolder.getAuditorId().equals(personId));
            if (flag) {
                workSheetFolderSet.add(workSheetFolder);

                //数据状态为复审中才纳入
                if (analyseData.getDataStatus().equals(EnumAnalyseDataStatus.已测.getValue()) || analyseData.getDataStatus().equals(EnumAnalyseDataStatus.复核通过.getValue())) {
                    analyseDataList.add(analyseData);
                }
            }
        }

        List<Map<String, Object>> auditWorkSheetList = new ArrayList<>();
        for (DtoWorkSheetFolder workSheetFolder : workSheetFolderSet) {
            Map<String, Object> mapObject = this.getMapInfo(workSheetFolder);
            List<DtoAnalyseData> dataList = analyseDataList.stream().filter(p -> p.getWorkSheetFolderId().equals(workSheetFolder.getId())).collect(Collectors.toList());
            mapObject.put("sampleNum", dataList.size());
            if (dataList.size() > 0) {
                mapObject.put("grade", dataList.stream().sorted(Comparator.comparing(DtoAnalyseData::getGrade).reversed()).collect(Collectors.toList()).get(0).getGrade());
                mapObject.put("redAnalyzeItemName", String.join(",", dataList.stream().map(DtoAnalyseData::getRedAnalyzeItemName).collect(Collectors.toSet())));
                mapObject.put("redAnalyzeMethodName", dataList.get(0).getRedAnalyzeMethodName());
                mapObject.put("redCountryStandard", dataList.get(0).getRedCountryStandard());
            }
            auditWorkSheetList.add(mapObject);
        }

        auditWorkSheetList.sort(Comparator.comparing(AnalyseDataCacheServiceImpl::comparingByGrade).reversed().thenComparing(AnalyseDataCacheServiceImpl::comparingByAnalyzeTime)
                .thenComparing(AnalyseDataCacheServiceImpl::comparingByRedAnalyzeItemName));
        //审核中检测单
        this.saveCardInfo(personId, EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AuditWorkSheetDetail.getValue(), orgId),
                "auditWorkSheetCount", "auditWorkSheetDetail", auditWorkSheetList);

        //缓存首页实验室分析任务数量
        homeService.cacheAnalysTask(personId, orgId, EnumLIM.EnumHomeTaskModule.实验室审核.getValue(), auditWorkSheetList.size());

        //分析状态
        this.saveAnalyzeStatus(personId, orgId, null, null, analyseDataList.size());
    }

    /**
     * 提交
     *
     * @param analystId 分析人员id
     * @param times     次数
     */
    @Override
    public void saveSubmitTimes(String analystId, Integer times) {
        this.saveAnalyseCalendarTimes(analystId, "submitWorkSheetNum", times);
    }

    /**
     * 复审
     *
     * @param personId 人员id
     * @param times    次数
     */
    @Override
    public void saveAuditTimes(String personId, Integer times) {
        this.saveAnalyseCalendarTimes(personId, "auditWorkSheetNum", times);
    }

    /**
     * 保存卡片信息
     *
     * @param personId       人员id
     * @param hashKey        haskKey
     * @param countProperty  数据条数字段名
     * @param detailProperty 卡片详情字段名
     * @param list           卡片数据
     */
    private void saveCardInfo(String personId, String hashKey, String countProperty, String detailProperty, List<Map<String, Object>> list) {
        Map<String, Object> mapObj = new HashMap<>();
        if (countProperty.equals("awaitSampleCount")) {
            mapObj.put(countProperty, list.stream().map(p -> (Integer) p.get("sampleNum")).reduce(0, (sum, item) -> sum + item));
        } else {
            mapObj.put(countProperty, list.size());
        }
        mapObj.put(detailProperty, list);
        redisTemplate.opsForHash().put(hashKey, personId, JsonStream.serialize(mapObj));
    }

    /**
     * 保存分析状态
     *
     * @param analystId 分析人员id
     * @param nums      数据个数
     */
    private void saveAnalyzeStatus(String analystId, String orgId, Integer... nums) {
        //分析状态（显示该月至今已完成的样品个数，所有待检测的样品个数，审核中的样品个数，检测中的样品个数）
        Integer awaitSample = nums.length > 0 ? nums[0] : null;
        Integer awaitWorkSheet = nums.length > 1 ? nums[1] : null;
        Integer auditWorkSheet = nums.length > 2 ? nums[2] : null;
        Integer finishWorkSheet = nums.length > 3 ? nums[3] : null;

        String analyzeStatusKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AnalyzeStatus.getValue(), orgId);
        Object analyzeStatusJson = redisTemplate.opsForHash().get(analyzeStatusKey, analystId);
        Map<String, Object> analyzeStatusMap = new HashMap<>();
        if (StringUtils.isNotNullAndEmpty(analyzeStatusJson)) {
            analyzeStatusMap = JsonIterator.deserialize((String) analyzeStatusJson, Map.class);
        } else {
            analyzeStatusMap.put("awaitSample", 0);
            analyzeStatusMap.put("awaitWorkSheet", 0);
            analyzeStatusMap.put("auditWorkSheet", 0);
            analyzeStatusMap.put("finishWorkSheet", 0);
        }
        if (StringUtil.isNotNull(awaitSample)) {
            analyzeStatusMap.put("awaitSample", awaitSample);
        }
        if (StringUtil.isNotNull(awaitWorkSheet)) {
            analyzeStatusMap.put("awaitWorkSheet", awaitWorkSheet);
        }
        if (StringUtil.isNotNull(auditWorkSheet)) {
            analyzeStatusMap.put("auditWorkSheet", auditWorkSheet);
        }
        if (StringUtil.isNotNull(finishWorkSheet)) {
            analyzeStatusMap.put("finishWorkSheet", finishWorkSheet);
        }
        redisTemplate.opsForHash().put(analyzeStatusKey, analystId, JsonStream.serialize(analyzeStatusMap));
    }

    /**
     * 保存分析计划
     *
     * @param analystId 分析人员id
     * @param dataList  计划数据
     */
    private void saveAnalysePlan(String analystId, String orgId, List<DtoAnalyseData> dataList) {
        //分析计划
        String analysePlanKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AnalysePlan.getValue(), orgId);
        List<Map<String, Object>> analysePlanList = new ArrayList<>();
        dataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getAnalyseItemId, Collectors.toList())).forEach((analyseItemId, list) -> {
            Map<String, Object> analysePlanMap = new HashMap<>();
            analysePlanMap.put("analyzeItemId", analyseItemId);
            analysePlanMap.put("analyzeItemName", list.get(0).getRedAnalyzeItemName());
            analysePlanMap.put("sampleNum", list.size());
            List<Map<String, Object>> planDetailList = new ArrayList<>();
            list.stream().collect(Collectors.groupingBy(p -> p.getTestId() + p.getSampleTypeId(), Collectors.toList())).forEach((key, detailList) -> {
                Map<String, Object> planDetailMap = new HashMap<>();
                planDetailMap.put("id", detailList.get(0).getTestId());
                planDetailMap.put("analyzeItemId", analyseItemId);
                planDetailMap.put("redAnalyzeItemName", detailList.get(0).getRedAnalyzeItemName());
                planDetailMap.put("analyzeMethodId", detailList.get(0).getAnalyzeMethodId());
                planDetailMap.put("redAnalyzeMethodName", detailList.get(0).getRedAnalyzeMethodName());
                planDetailMap.put("redCountryStandard", detailList.get(0).getRedCountryStandard());
                planDetailMap.put("sampleTypeId", detailList.get(0).getSampleTypeId());
                planDetailMap.put("sampleTypeName", detailList.get(0).getSampleTypeName());
                planDetailMap.put("sampleNum", detailList.size());
                planDetailList.add(planDetailMap);
            });
            analysePlanMap.put("planDetail", planDetailList);
            analysePlanList.add(analysePlanMap);
        });
        redisTemplate.opsForHash().put(analysePlanKey, analystId, JsonStream.serialize(analysePlanList));
    }

    /**
     * 保存日历次数信息
     *
     * @param personId 人员id
     * @param key      对应map的键
     * @param times    次数
     */
    private void saveAnalyseCalendarTimes(String personId, String key, Integer times) {
        //分析日历的key
        String analyseCalendarKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AnalyseCalendar.getValue());
        Object analyseCalendarJson = redisTemplate.opsForHash().get(analyseCalendarKey, personId);
        List<Map<String, Object>> analyseCalendarMapList = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(analyseCalendarJson)) {
            analyseCalendarMapList = JsonIterator.deserialize((String) analyseCalendarJson, List.class);
        }
        String today = DateUtil.dateToString(new Date(), DateUtil.YEAR);
        if (analyseCalendarMapList.stream().anyMatch(p -> String.valueOf(p.getOrDefault("analyzeTime", "")).equals(today))) {
            for (Map<String, Object> analyseCalendarMap : analyseCalendarMapList) {
                if (analyseCalendarMap.getOrDefault("analyzeTime", "").equals(today)) {
                    Integer num = Integer.valueOf(String.valueOf(analyseCalendarMap.getOrDefault(key, 0)));
                    analyseCalendarMap.put(key, num + times);
                }
            }
        } else {
            Map<String, Object> analyseCalendar = new HashMap<>();
            analyseCalendar.put("analyzeTime", today);
            analyseCalendar.put("awaitAnalyseNum", 0);
            analyseCalendar.put("createWorkSheetNum", 0);
            analyseCalendar.put("submitWorkSheetNum", 0);
            analyseCalendar.put("auditWorkSheetNum", 0);
            analyseCalendar.put("analyseItems", new ArrayList<>());
            analyseCalendar.put(key, times);
            analyseCalendarMapList.add(analyseCalendar);
        }
        redisTemplate.opsForHash().put(analyseCalendarKey, personId, JsonStream.serialize(analyseCalendarMapList));
    }

    /**
     * 保存日历
     *
     * @param analystId           分析人员id
     * @param workSheetFolderList 当月检测单
     * @param analyseDataList     当月分析数据
     */
    private void saveAnalyseCalendar(String analystId,
                                     String orgId,
                                     List<DtoWorkSheetFolder> workSheetFolderList, List<DtoAnalyseData> analyseDataList) {
        //分析日历
        String analyseCalendarKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AnalyseCalendar.getValue(), orgId);

        Object analyseCalendarJson = redisTemplate.opsForHash().get(analyseCalendarKey, analystId);
        List<Map<String, Object>> analyseCalendarMapList = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(analyseCalendarJson)) {
            analyseCalendarMapList = JsonIterator.deserialize((String) analyseCalendarJson, List.class);
        }
        Map<String, Map<String, Object>> analyseCalendarMap = analyseCalendarMapList.stream().collect(Collectors.toMap(p -> String.valueOf(p.getOrDefault("analyzeTime", "")), analyseCalendar -> analyseCalendar));

        Map<String, Integer> workSheetFolderMap = workSheetFolderList.stream().
                collect(Collectors.groupingBy(p -> DateUtil.dateToString(p.getCreateTime(), DateUtil.YEAR), Collectors.collectingAndThen(Collectors.toList(), value -> value.size())));

        Map<String, Map<String, Object>> analyseCalendarNewMap = new HashMap<>();
        analyseDataList.stream().collect(Collectors.groupingBy(p -> DateUtil.dateToString(p.getFolderAnalyzeTime(), DateUtil.YEAR), Collectors.toList())).forEach((analyzeTime, list) -> {
            Map<String, Object> analyseCalendar = new HashMap<>();
            if (analyseCalendarMap.containsKey(analyzeTime)) {
                analyseCalendar = analyseCalendarMap.get(analyzeTime);
            }
            analyseCalendar.put("createWorkSheetNum", workSheetFolderMap.getOrDefault(analyzeTime, 0));
            analyseCalendar.put("analyzeTime", analyzeTime);
            analyseCalendar.put("awaitAnalyseNum", list.size());
            List<Map<String, Object>> analyseItemList = new ArrayList<>();
            list.stream().collect(Collectors.groupingBy(DtoAnalyseData::getAnalyseItemId, Collectors.toList())).forEach((analyseItemId, detailList) -> {
                Map<String, Object> analyseItem = new HashMap<>();
                analyseItem.put("analyzeItemId", analyseItemId);
                analyseItem.put("analyzeItemName", detailList.get(0).getRedAnalyzeItemName());
                analyseItem.put("sampleNum", detailList.size());
                analyseItemList.add(analyseItem);
            });
            analyseCalendar.put("analyseItems", analyseItemList);
            analyseCalendarNewMap.put(analyzeTime, analyseCalendar);
        });
        List<Map<String, Object>> analyseCalendarList = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);

        while (calendar.getTime().before(new Date())) {
            String date = DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
            if (analyseCalendarNewMap.containsKey(date)) {
                analyseCalendarList.add(analyseCalendarNewMap.get(date));
            } else if (analyseCalendarMap.containsKey(date)) {
                analyseCalendarMap.get(date).put("awaitAnalyseNum", 0);
                analyseCalendarMap.get(date).put("createWorkSheetNum", 0);
                analyseCalendarMap.get(date).put("analyseItems", new ArrayList<>());
                analyseCalendarList.add(analyseCalendarMap.get(date));
            } else {
                Map<String, Object> analyseCalendar = new HashMap<>();
                analyseCalendar.put("analyzeTime", date);
                analyseCalendar.put("awaitAnalyseNum", 0);
                analyseCalendar.put("createWorkSheetNum", 0);
                analyseCalendar.put("submitWorkSheetNum", 0);
                analyseCalendar.put("auditWorkSheetNum", 0);
                analyseCalendar.put("analyseItems", new ArrayList<>());
                analyseCalendarList.add(analyseCalendar);
            }
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        redisTemplate.opsForHash().put(analyseCalendarKey, analystId, JsonStream.serialize(analyseCalendarList));
    }

    /**
     * 保存分析统计
     *
     * @param analystId       分析人员id
     * @param analyseDataList 当月分析数据
     */
    private void saveAnalyzeStatistics(String analystId, String orgId, List<DtoAnalyseData> analyseDataList) {
        //分析统计（显示当月各个分析项目分析个数）
        String analyzeStatisticsKey = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_AnalyzeStatistics.getValue(), orgId);
        List<Map<String, Object>> analyzeStatisticsList = new ArrayList<>();

        analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getAnalyseItemId, Collectors.toList())).forEach((analyseItemId, list) -> {
            Map<String, Object> analyzeStatistics = new HashMap<>();
            analyzeStatistics.put("analyzeItemId", analyseItemId);
            analyzeStatistics.put("analyzeItemName", list.get(0).getRedAnalyzeItemName());
            analyzeStatistics.put("sampleNum", list.size());
            analyzeStatisticsList.add(analyzeStatistics);
        });

        redisTemplate.opsForHash().put(analyzeStatisticsKey, analystId, JsonStream.serialize(analyzeStatisticsList));
    }

    /**
     * 获取map信息
     *
     * @param workSheetFolder 检测单
     * @return map信息
     */
    private Map<String, Object> getMapInfo(DtoWorkSheetFolder workSheetFolder) {
        Map<String, Object> mapObject = new HashMap<>();
        mapObject.put("id", workSheetFolder.getId());
        mapObject.put("workSheetCode", workSheetFolder.getWorkSheetCode());
        mapObject.put("analyzeMethodId", workSheetFolder.getAnalyzeMethodId());
        mapObject.put("redAnalyzeItemName", "");
        mapObject.put("redAnalyzeMethodName", "");
        mapObject.put("redCountryStandard", "");
        mapObject.put("analystId", workSheetFolder.getAnalystId());
        mapObject.put("analystName", workSheetFolder.getAnalystName());
        mapObject.put("auditorId", workSheetFolder.getAuditorId());
        mapObject.put("auditorName", workSheetFolder.getAuditorName());
        mapObject.put("checkerId", workSheetFolder.getCheckerId());
        mapObject.put("checkerName", workSheetFolder.getCheckerName());
        mapObject.put("createTime", DateUtil.dateToString(workSheetFolder.getCreateTime(), DateUtil.YEAR));
        mapObject.put("analyzeTime", DateUtil.dateToString(workSheetFolder.getAnalyzeTime(), DateUtil.YEAR));
        mapObject.put("auditorTime", DateUtil.dateToString(workSheetFolder.getAuditDate(), DateUtil.YEAR));
        mapObject.put("checkedTime", DateUtil.dateToString(workSheetFolder.getCheckDate(), DateUtil.YEAR));
        mapObject.put("notPassRemark", workSheetFolder.getBackOpinion());
        mapObject.put("remark", workSheetFolder.getRemark());
        mapObject.put("status", workSheetFolder.getStatus());
        return mapObject;
    }

    /**
     * 判断“按岗位分配”开关是否开启
     *
     * @param orgId 组织id
     * @return 是否开启
     */
    private boolean getAllocateByPost(String orgId) {
        List<DictDataProjectModel> projectModelList = dictDataProjectService.findListByCode(orgId, "BASE_Switch",
                Collections.singletonList(ProCodeHelper.PRO_AnalyseAllocationRules_Post));
        if (StringUtil.isNotEmpty(projectModelList)) {
            DictDataProjectModel dictDataProjectModel = projectModelList.get(0);
//            DtoCode dtoCode = codeService.findByCode(ProCodeHelper.PRO_AnalyseAllocationRules_Post);
            if (StringUtil.isNotNull(dictDataProjectModel) && "1".equals(dictDataProjectModel.getDictValue())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 按项目等级排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    public static Integer comparingByGrade(Map<String, Object> map) {
        return (Integer) map.get("grade");
    }

    /**
     * 按检测单号排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    public static String comparingByWorksheetCode(Map<String, Object> map) {
        return (String) map.get("workSheetCode");
    }

    /**
     * 按分析时间排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    public static String comparingByAnalyzeTime(Map<String, Object> map) {
        return (String) map.get("analyzeTime");
    }

    /**
     * 按分析时间排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    public static String comparingByAuditorTime(Map<String, Object> map) {
        return (String) map.get("auditorTime");
    }

    /**
     * 按分析时间排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    public static String comparingBySortTime(Map<String, Object> map) {
        return (String) map.get("sortTime");
    }

    /**
     * 按测试项目排序
     *
     * @param map 集合对象
     * @return 返回排序值
     */
    public static String comparingByRedAnalyzeItemName(Map<String, Object> map) {
        return (String) map.get("redAnalyzeItemName");
    }

    @Autowired
    public void setProjectPlanRepository(ProjectPlanRepository projectPlanRepository) {
        this.projectPlanRepository = projectPlanRepository;
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }
}
