package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.monitor.repository.lims.FixedPointPropertyRepository;
import com.sinoyd.lims.pro.criteria.AutoTaskPlanCriteria;
import com.sinoyd.lims.pro.dto.DtoAutoTaskPlan;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoTask2FixedProperty;
import com.sinoyd.lims.pro.repository.AutoTaskPlanRepository;
import com.sinoyd.lims.pro.repository.Task2FixedPropertyRepository;
import com.sinoyd.lims.pro.service.AutoTaskPlanService;
import com.sinoyd.lims.pro.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动任务下达service
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/08
 * @since V100R001
 */
@Service
public class AutoTaskPlanServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoAutoTaskPlan, String, AutoTaskPlanRepository> implements AutoTaskPlanService {

    private Task2FixedPropertyRepository task2FixedPropertyRepository;

    private FixedPointPropertyRepository fixedPointPropertyRepository;

    private ProjectService projectService;

    private JdbcTemplate jdbcTemplate;

    @Override
    public void findByPage(PageBean<DtoAutoTaskPlan> page, BaseCriteria criteria) {
        Integer pageNo = page.getPageNo();
        Integer rowsPerPage = page.getRowsPerPage();
        page.setEntityName("DtoAutoTaskPlan a");
        page.setSelect("select a");
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        super.findByPage(page, criteria);
        AutoTaskPlanCriteria autoTaskPlanCriteria = (AutoTaskPlanCriteria) criteria;
        if (StringUtil.isNotEmpty(autoTaskPlanCriteria.getMonth())) {
            page.setData(page.getData().stream().filter(a ->{
                List<String> monthes = Arrays.asList(a.getMonth().split(","));
                return monthes.containsAll(autoTaskPlanCriteria.getMonth());
            }).collect(Collectors.toList()));
        }
        page.setRowsCount(page.getData().size());
        List<DtoAutoTaskPlan> results = page.getData();
        results = results.stream().skip((long) (pageNo - 1) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList());
        fillingTransientFields(results);
        page.setData(results);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        task2FixedPropertyRepository.deleteByTaskIdIn((List<String>) ids);
        return super.logicDeleteById(ids);
    }

    @Transactional
    @Override
    public Map<String, Object> savePlan(DtoAutoTaskPlan entity) {
        verify(entity);
        Map<String, Object> map = new HashMap();
        map.put("id", entity.getId());
        map.put("jobName", entity.getTaskCode());
        map.put("jobGroup", "DEFAULT");
        map.put("invokeTarget", "automaticRelease.createProjectTask('" + entity.getTaskCode() + "')");
        map.put("cronExpression", getCornFormula(entity.getDealDate()));
        map.put("misfirePolicy", 3);
        map.put("isConcurrent", false);
        map.put("status", 1);
        map.put("remark", "");
        saveTask2Property(entity);
        super.save(entity);
        return map;
    }

    @Override
    @Transactional
    public DtoAutoTaskPlan update(DtoAutoTaskPlan entity) {
        verify(entity);
        saveTask2Property(entity);
        return super.update(entity);
    }

    @Override
    public List<DtoFixedPointProperty> getPropertyByTaskId(String taskId) {
        List<DtoTask2FixedProperty> task2FixedProperties = task2FixedPropertyRepository.findByTaskIdIn(Arrays.asList(taskId));
        List<String> propertyIds = task2FixedProperties.stream().map(DtoTask2FixedProperty::getFixedPropertyId).distinct().collect(Collectors.toList());
        List<DtoFixedPointProperty> fixedPointPropertyList = StringUtil.isNotEmpty(propertyIds) ? fixedPointPropertyRepository.findByIdIn(propertyIds) : new ArrayList<>();
        List<String> parentIds = fixedPointPropertyList.stream().map(DtoFixedPointProperty::getParentId).distinct().collect(Collectors.toList());
        List<DtoFixedPointProperty> parentPropertyList = StringUtil.isNotEmpty(parentIds) ? fixedPointPropertyRepository.findByIdIn(parentIds) : new ArrayList<>();
        setPropertyName(parentPropertyList, fixedPointPropertyList);
        return parentPropertyList;
    }

    @Override
    @Transactional
    public void autoCreateProject(String taskId) {
        DtoAutoTaskPlan plan = repository.findOne(taskId);
        if (!checkNeedLaunch(plan)) {
            return;
        }
        List<String> propertyIds = task2FixedPropertyRepository.findByTaskIdIn(Arrays.asList(taskId)).stream().map(DtoTask2FixedProperty::getFixedPropertyId).collect(Collectors.toList());
        String month = getMonth() + "月";
        Date deadLine = getDate();
        Map<Integer, String> map = getProjectGenerateName(EnumLIM.EnumDealCycle.getByValue(plan.getDealCycle()), plan);
        for (Integer i = 1; i <+ plan.getDealNum(); i++) {
            DtoProject project = new DtoProject();
            project.setInceptTime(new Date());
            project.setIsStress(plan.getIsStress());
            project.setGrade(plan.getGrade());
            project.setLeaderId(plan.getLeaderId());
            project.setProjectTypeId(plan.getProjectTypeId());
            project.setProjectName(month + plan.getTaskName() + map.get(i));
            project.setProjectCode("");
            project.setIsWarning(false);
            project.setWarningDay(0);
            project.setDeadLine(deadLine);
            project.setInceptPersonId(plan.getInputPersonId());
            projectService.saveProject(project, propertyIds, false, true);
        }
    }

    /**
     * 判断当前月是否执行，如7月份就判断8月份在不在执行月份中
     *
     * @param plan 计划
     * @return
     */
    private Boolean checkNeedLaunch(DtoAutoTaskPlan plan) {
        String month = getMonth();
        List<String> monthes = Arrays.asList(plan.getMonth().split(","));
        if (monthes.contains(month)) {
            return true;
        }
        return false;
    }

    /**
     * 获取当前月份需要执行操作的月份数
     *
     * @return 月份
     */
    private String getMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.MONTH) + 2 + "";
    }

    private Date getDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, 2);
        return calendar.getTime();
    }

    /**
     * 根据执行周期获取项目名称
     *
     * @param dealCycle
     * @param plan
     * @return
     */
    private Map<Integer, String> getProjectGenerateName(EnumLIM.EnumDealCycle dealCycle, DtoAutoTaskPlan plan) {
        Map<Integer, String> map = new HashMap<>();
        switch (dealCycle) {
            case 每月:
                plan.setDealNum(1);
                map.put(1, "");
                break;
            case 每半月:
                plan.setDealNum(2);
                map.put(1, "（上旬）");
                map.put(2, "（下旬）");
                break;
            case 每周:
                plan.setDealNum(4);
                map.put(1, "（第一周）");
                map.put(2, "（第二周）");
                map.put(3, "（第三周）");
                map.put(4, "（第四周）");
                break;
            case 其他:
                for (Integer i = 1; i <= plan.getDealNum(); i++) {
                    map.put(i, "（第" + i + "次）");
                }
                break;
        }
        return map;
    }

    /**
     * 保存任务与监测计划关联数据
     *
     * @param plan 计划
     */
    private void saveTask2Property(DtoAutoTaskPlan plan) {
        task2FixedPropertyRepository.deleteByTaskIdIn(Arrays.asList(plan.getId()));
        if (StringUtil.isNotEmpty(plan.getPropertyIds())) {
            List<DtoFixedPointProperty> fixedPointPropertyList = fixedPointPropertyRepository.findByParentIdIn(plan.getPropertyIds());
            List<String> properTyIds = fixedPointPropertyList.stream().map(DtoFixedPointProperty::getId).collect(Collectors.toList());
            List<DtoTask2FixedProperty> properties = new ArrayList<>();
            properTyIds.forEach(p -> {
                DtoTask2FixedProperty property = new DtoTask2FixedProperty();
                property.setFixedPropertyId(p);
                property.setTaskId(plan.getId());
                properties.add(property);
            });
            if (StringUtil.isNotEmpty(properties)) {
                task2FixedPropertyRepository.save(properties);
            }
        }
    }

    /**
     * 校验编码是否唯一
     *
     * @param plan 数据
     */
    private void verify(DtoAutoTaskPlan plan) {
        if (repository.countByTaskCodeAndIdNot(plan.getTaskCode(), plan.getId()) > 0) {
            throw new BaseException("任务编码重复");
        }
        Map<String, DtoFixedPointProperty> parentPropertyMap = fixedPointPropertyRepository.findByIdIn(plan.getPropertyIds()).stream().collect(Collectors.toMap(DtoFixedPointProperty::getId, dto -> dto));
        List<DtoFixedPointProperty> fixedPointPropertyList = fixedPointPropertyRepository.findByParentIdIn(plan.getPropertyIds());
        Map<String, List<DtoFixedPointProperty>> parentId2FixedPointProperty = fixedPointPropertyList.stream().collect(Collectors.groupingBy(DtoFixedPointProperty::getParentId));
        List<String> monthes = Arrays.asList(plan.getMonth().split(","));
        parentId2FixedPointProperty.forEach((parentId, values) -> {
            Boolean pass = false;
            for (DtoFixedPointProperty property : values) {
                if (monthes.contains(String.valueOf(property.getMonth()))) {
                    pass = true;
                }
            }
            if (!pass) {
                throw new BaseException("[" + parentPropertyMap.get(parentId).getPropertyName() + "]应用月份与计划完全不匹配，请重新选择！");
            }
        });

    }

    /**
     * 填充冗余数据
     *
     * @param taskPlans 数据
     */
    private void fillingTransientFields(List<DtoAutoTaskPlan> taskPlans) {
        List<String> taskIds = taskPlans.stream().map(DtoAutoTaskPlan::getId).collect(Collectors.toList());
        List<DtoTask2FixedProperty> task2FixedProperties = StringUtil.isNotEmpty(taskIds) ? task2FixedPropertyRepository.findByTaskIdIn(taskIds) : new ArrayList<>();
        List<String> propertyIds = task2FixedProperties.stream().map(DtoTask2FixedProperty::getFixedPropertyId).distinct().collect(Collectors.toList());
        List<DtoFixedPointProperty> pointProperties = StringUtil.isNotEmpty(propertyIds) ? fixedPointPropertyRepository.findByIdIn(propertyIds) : new ArrayList<>();
        List<String> parentPropertyIds = pointProperties.stream().map(DtoFixedPointProperty::getParentId).distinct().collect(Collectors.toList());
        List<DtoFixedPointProperty> parentProperties = StringUtil.isNotEmpty(parentPropertyIds) ? fixedPointPropertyRepository.findByIdIn(parentPropertyIds) : new ArrayList<>();
        setPropertyName(parentProperties, pointProperties);
        taskPlans.forEach(plan -> {
            List<String> propertyIds2Plan = task2FixedProperties.stream().filter(t -> t.getTaskId().equals(plan.getId())).map(DtoTask2FixedProperty::getFixedPropertyId).distinct().collect(Collectors.toList());
            List<String> parentIds = pointProperties.stream().filter(p -> propertyIds2Plan.contains(p.getId())).map(DtoFixedPointProperty::getParentId).distinct().collect(Collectors.toList());
            plan.setPropertyNames(parentProperties.stream().filter(p -> parentIds.contains(p.getId())).map(DtoFixedPointProperty::getPropertyName).collect(Collectors.joining(",")));
        });
    }

    private void setPropertyName(List<DtoFixedPointProperty> parentProperties ,List<DtoFixedPointProperty> pointProperties) {
        for (DtoFixedPointProperty fixedPointProperty : parentProperties) {
            List<Integer> monthList = pointProperties.stream()
                    .filter(p -> p.getParentId().equals(fixedPointProperty.getId())).map(DtoFixedPointProperty::getMonth)
                    .distinct().sorted().collect(Collectors.toList());
            fixedPointProperty.setPropertyName(String.format("%s（%s）", fixedPointProperty.getPropertyName(),
                    org.apache.commons.lang.StringUtils.join(monthList, "、")));
        }
    }

    /**
     * 获取定时任务执行表达式
     *
     * @param dealDate 执行日期
     * @return String
     */
    private String getCornFormula(String dealDate) {
        String sql = "select * from TB_BASE_JOB where isDeleted = 0 and jobGroup = 'DEFAULT' and invokeTarget like '%automaticRelease.createProjectTask%'";
        List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql);
        List<String> cornExpressions = dataList.stream().map(map -> map.get("cronExpression").toString()).distinct().collect(Collectors.toList());
        Comparator<String> comparator1 = Comparator.comparingInt(corn -> Integer.valueOf(corn.split(" ")[2]));
        Comparator<String> comparator2 = Comparator.comparingInt(corn -> Integer.valueOf(corn.split(" ")[1]));
        Map<String, Optional<String>> cornExpressionMap = cornExpressions.stream().collect(Collectors.groupingBy(corn -> corn.split(" ")[3], Collectors.maxBy(comparator1.thenComparing(comparator2))));
        Optional<String> cornExpressionOp = cornExpressionMap.get(dealDate);
        if (StringUtil.isNotNull(cornExpressionOp) && cornExpressionOp.isPresent()) {
            String cornExpression = cornExpressionOp.get();
            String hour = cornExpression.split(" ")[2];
            String minute = cornExpression.split(" ")[1];
            if (Integer.valueOf(minute) + 5 < 60) {
                minute = String.valueOf(Integer.valueOf(minute) + 5);
            } else {
                hour = String.valueOf(Integer.valueOf(hour) + 1);
                minute = "0";
            }
            return "0 " + minute + " " + hour + " " + dealDate + " * ?";
        } else {
            return "0 15 1 " + dealDate + " * ?";
        }
    }

    @Autowired
    public void setTask2FixedPropertyRepository(Task2FixedPropertyRepository task2FixedPropertyRepository) {
        this.task2FixedPropertyRepository = task2FixedPropertyRepository;
    }

    @Autowired
    public void setFixedPointPropertyRepository(FixedPointPropertyRepository fixedPointPropertyRepository) {
        this.fixedPointPropertyRepository = fixedPointPropertyRepository;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}
