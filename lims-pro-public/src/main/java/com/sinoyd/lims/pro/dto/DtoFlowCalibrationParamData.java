package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.FlowCalibrationParamData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Map;

/**
 * DtoFlowCalibrationParamData实体
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/14
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_FlowCalibrationParamData")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoFlowCalibrationParamData extends FlowCalibrationParamData {

    /**
     * 相关配置
     */
    @Transient
    private Map<String,Object> config;
}
