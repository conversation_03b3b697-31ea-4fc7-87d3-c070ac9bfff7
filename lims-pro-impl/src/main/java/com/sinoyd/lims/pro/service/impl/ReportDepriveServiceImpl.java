package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoReportDeprive;
import com.sinoyd.lims.pro.repository.ReportDepriveRepository;
import com.sinoyd.lims.pro.repository.ReportRepository;
import com.sinoyd.lims.pro.service.ReportDepriveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告扣发接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
@Service
public class ReportDepriveServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportDeprive, String, ReportDepriveRepository> implements ReportDepriveService {

    private ReportRepository reportRepository;

    @Override
    @Transactional
    public List<DtoReportDeprive> save(Collection<DtoReportDeprive> entities) {
        // 汇总选中的所有报告id
        List<String> reportIds = entities.stream().flatMap(d -> Arrays.stream(d.getReportIds().split(","))).filter(d -> !UUIDHelper.GUID_EMPTY.equals(d)).collect(Collectors.toList());
        List<String> projectIds = entities.stream().map(DtoReportDeprive::getProjectId).distinct().collect(Collectors.toList());
        List<DtoReport> reportList = reportRepository.findAll(reportIds);
        if (reportList.stream().anyMatch(p -> p.getGrantStatus() == 2 || p.getGrantStatus() == 3)) {
            throw new BaseException("存在已发放或回收的报告，不可扣发");
        }

        // 根据项目id查询项目下已存在的报告扣发信息
        List<DtoReportDeprive> existsReportDepriveList = StringUtil.isNotEmpty(projectIds) ? repository.findByProjectIdIn(projectIds) : new ArrayList<>();
        // 根据本次保存数据id 与已存在报告扣发信息进行比较
        List<DtoReportDeprive> deleteList = existsReportDepriveList.stream().filter(e -> entities.stream().anyMatch(t -> e.getId().equals(t.getId()))).collect(Collectors.toList());
        List<String> deleteReportIds = deleteList.stream().flatMap(d -> Arrays.stream(d.getReportIds().split(","))).filter(d -> !UUIDHelper.GUID_EMPTY.equals(d)).distinct().collect(Collectors.toList());
        // 筛选已经移除的报告id
        deleteReportIds.removeIf(reportIds::contains);
//        if (StringUtil.isNotEmpty(deleteList)) {
//            repository.delete(deleteList);
//        }
        // 存在移除的报告id，将发放状态改为默认
        if (StringUtil.isNotEmpty(deleteReportIds)) {
            reportRepository.updateGrantStatus(deleteReportIds, 1, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        }
        // 发放状态改为已扣发
        if (StringUtil.isNotEmpty(reportIds)) {
            reportRepository.updateGrantStatus(reportIds, 4, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        }
        return super.save(entities);
    }

    @Override
    public List<DtoReportDeprive> findByProjectId(String projectId) {
        List<DtoReportDeprive> reportDeprives = repository.findByProjectIdIn(Collections.singletonList(projectId));
        reportDeprives.sort(Comparator.comparing(DtoReportDeprive::getCreateDate));
        return reportDeprives;
    }


    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        DtoReportDeprive reportDeprive = repository.findOne((String) id);
        // 根据报告id 更新报告状态
        List<String> reportIds = Arrays.stream(reportDeprive.getReportIds().split(",")).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(reportIds)) {
            reportRepository.updateGrantStatus(reportIds, 1, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        }
        return super.logicDeleteById(id);
    }

    @Autowired
    public void setReportRepository(ReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }
}
