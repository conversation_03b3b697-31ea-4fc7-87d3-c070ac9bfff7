package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.pro.criteria.ReportCriteria;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoReportNumberPool;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.dto.customer.DtoQcSampleEvaluateDetail;
import com.sinoyd.lims.pro.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 报告服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
@Api(tags = "示例: Report服务")
@RestController
@RequestMapping("api/pro/report")
public class ReportController extends BaseJpaController<DtoReport, String, ReportService> {


    /**
     * 分页动态条件查询报告
     *
     * @param reportCriteria 条件参数
     * @return RestResponse<List < Map < String, Object>>>
     */
    @ApiOperation(value = "分页动态条件查询报告", notes = "分页动态条件查询报告")
    @GetMapping
    public RestResponse<List<DtoReport>> findByPage(ReportCriteria reportCriteria) {
        PageBean<DtoReport> pageBean = super.getPageBean();
        RestResponse<List<DtoReport>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, reportCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询报告
     *
     * @param id 主键id
     * @return RestResponse<Map < String, Object>>
     */
    @ApiOperation(value = "按主键查询报告", notes = "按主键查询报告")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoReport> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoReport> restResponse = new RestResponse<>();
        DtoReport report = service.findOne(id);
        restResponse.setData(report);
        restResponse.setRestStatus(StringUtil.isNull(report) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 获取报告编号
     *
     * @param reportName 报告类型名称
     * @param reportYear 报告年份
     * @return RestResponse<String>
     */
    @ApiOperation(value = "获取报告编号", notes = "获取报告编号")
    @GetMapping(path = "/code")
    public RestResponse<String> createReportCode(@RequestParam(name = "reportName") String reportName,
                                                 @RequestParam(name = "reportYear") Integer reportYear,
                                                 @RequestParam(name = "projectCode") String projectCode,
                                                 @RequestParam(name = "reportId", required = false) String reportId) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.createReportCode(reportName, reportYear, projectCode, reportId, ""));
        return restResponse;
    }

    /**
     * 新增报告
     *
     * @param report 实体列表
     * @return RestResponse<DtoReport>
     */
    @ApiOperation(value = "新增报告", notes = "新增报告")
    @PostMapping
    public RestResponse<DtoReport> create(@RequestBody @Validated DtoReport report) {
        RestResponse<DtoReport> restResponse = new RestResponse<>();
        restResponse.setData(service.save(report));
        return restResponse;
    }

    /**
     * 修改报告
     *
     * @param report 实体列表
     * @return RestResponse<DtoReport>
     */
    @ApiOperation(value = "修改报告", notes = "修改报告")
    @PutMapping
    public RestResponse<DtoReport> update(@RequestBody @Validated DtoReport report) {
        RestResponse<DtoReport> restResponse = new RestResponse<>();
        restResponse.setData(service.update(report));
        return restResponse;
    }

    @ApiOperation(value = "保存排序好的点位", notes = "保存排序好的点位")
    @PostMapping("/saveSortFolder")
    public RestResponse<List<Map<String, String>>> saveSortFolder(@RequestBody DtoReport report) {
        RestResponse<List<Map<String, String>>> restResp = new RestResponse<>();
        service.saveSortFolder(report.getId(), report.getSortFolderIdList());
        restResp.setMsg("保存成功！");
        return restResp;
    }

    /**
     * "根据id删除报告
     *
     * @param id id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除报告", notes = "根据id删除报告")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * "根据id批量删除报告
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除报告", notes = "根据id批量删除报告")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * "信号操作
     *
     * @param dto 实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "信号操作", notes = "信号操作")
    @PostMapping("/signal")
    public RestResponse<Boolean> signal(@RequestBody DtoWorkflowSign dto) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.reportSignal(dto);
        restResp.setData(true);
        return restResp;
    }

    /**
     * 校验报告所选样品对应的质控样中是否有不合格的样品，有则给与提示
     *
     * @param reportIdList 报告id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "校验报告所选样品对应的质控样中是否有不合格的样品", notes = "校验报告所选样品对应的质控样中是否有不合格的样品")
    @PostMapping("/qcSamplePass")
    public RestResponse<String> signal(@RequestBody List<String> reportIdList) {
        RestResponse<String> restResp = new RestResponse<>();
        PageBean<DtoQcSampleEvaluateDetail> pageBean = super.getPageBean();
        restResp.setData(service.checkQcSamplePass(pageBean, reportIdList));
        return restResp;
    }

    /**
     * 修改报告
     *
     * @param report 实体列表
     * @return RestResponse<DtoReport>
     */
    @ApiOperation(value = "修改报告数", notes = "修改报告数")
    @PutMapping("/reportNum")
    public RestResponse<Boolean> updateReportNum(@RequestBody DtoReport report) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.updateReportNum(report.getId(), report.getReportNum());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 根据报告id查询送样单
     *
     * @param reportId 报告id
     * @return RestResponse<List < ReceiveSampleRecord>>
     */
    @ApiOperation(value = "根据报告id查询送样单", notes = "根据报告id查询送样单")
    @GetMapping("/receiveSampleRecord")
    public RestResponse<List<DtoReceiveSampleRecord>> findReceiveSampleRecord(@RequestParam(name = "reportId") String reportId) {
        RestResponse<List<DtoReceiveSampleRecord>> restResponse = new RestResponse<>();
        List<DtoReceiveSampleRecord> receiveSampleRecordList = service.findReceiveSampleRecord(reportId);
        restResponse.setRestStatus(StringUtil.isEmpty(receiveSampleRecordList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(receiveSampleRecordList);
        restResponse.setCount(receiveSampleRecordList.size());
        return restResponse;
    }

    /**
     * 根据报告id查询检测单
     *
     * @param reportId 报告id
     * @return RestResponse<List < ReceiveSampleRecord>>
     */
    @ApiOperation(value = "根据报告id查询检测单", notes = "根据报告id查询检测单")
    @GetMapping("/workSheetFolder")
    public RestResponse<List<DtoWorkSheetFolder>> findWorksheetFolder(@RequestParam(name = "reportId") String reportId) {
        RestResponse<List<DtoWorkSheetFolder>> restResponse = new RestResponse<>();
        List<DtoWorkSheetFolder> workSheetFolderList = service.findWorksheetFolder(reportId);
        restResponse.setRestStatus(StringUtil.isEmpty(workSheetFolderList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(workSheetFolderList);
        restResponse.setCount(workSheetFolderList.size());
        return restResponse;
    }

    /**
     * 获取报表名称
     *
     * @param map      参数信息
     * @param configId 配置id
     * @return 报表名称
     */
    @ApiOperation(value = "获取报表名称", notes = "获取报表名称")
    @PostMapping("/documentFileName/{configId}")
    public RestResponse<DtoReportConfig> documentFileName(@RequestBody Map<String, Object> map,
                                                          @PathVariable(name = "configId") String configId) {
        RestResponse<DtoReportConfig> restResp = new RestResponse<>();
        restResp.setData(service.documentFileName(map, configId));
        return restResp;
    }

    /**
     * 生成报告编号（新）
     *
     * @param reportNumberPool 报告编号池实体
     * @return RestResponse<String>
     */
    @ApiOperation(value = "生成报告编号（新）", notes = "生成报告编号（新）")
    @PostMapping("/createCode")
    public RestResponse<DtoReportNumberPool> createCode(@RequestBody DtoReportNumberPool reportNumberPool) {
        RestResponse<DtoReportNumberPool> restResp = new RestResponse<>();
        restResp.setData(service.createCode(reportNumberPool));
        return restResp;
    }

    /**
     * 手动报告签名
     *
     * @param map 参数
     * @return Boolean
     */
    @ApiOperation(value = "手动报告签名", notes = "手动报告签名")
    @PostMapping("/reportSign")
    public RestResponse<Boolean> reportSign(@RequestBody Map<String, Object> map) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.reportSign(map);
        restResp.setData(true);
        return restResp;
    }

    /**
     * 重置报告
     *
     * @param reportId 报告id
     * @return Boolean
     */
    @ApiOperation(value = "手动报告签名", notes = "手动报告签名")
    @PostMapping("/reloadReport/{reportId}")
    public RestResponse<Boolean> reloadReport(@PathVariable(name = "reportId") String reportId) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.reloadReport(reportId);
        restResp.setData(true);
        return restResp;
    }

    /**
     * 保存生成例行报告的配置
     *
     * @param reportId 报告id
     * @return Boolean
     */
    @ApiOperation(value = "手动报告签名", notes = "手动报告签名")
    @PostMapping("/monitorReport/{reportId}/{reportConfigId}")
    public RestResponse<Void> saveMonitorReportConfig(@PathVariable String reportId, @PathVariable String reportConfigId,
                                                      @RequestBody Map<String, Object> params) {
        RestResponse<Void> restResp = new RestResponse<>();
        service.saveMonitorReportConfig(reportId, reportConfigId, params);
        return restResp;
    }


    /**
     * 获取所有已记录的收件人信息
     *
     * @return 已记录的收件人信息
     */
    @ApiOperation(value = "获取所有已记录的收件人信息", notes = "获取所有已记录的收件人信息")
    @GetMapping("/getExpressAgeInfo")
    public RestResponse<List<Map<String, Object>>> getExpressAgeInfo() {
        RestResponse<List<Map<String, Object>>> restResp = new RestResponse<>();
        restResp.setData(service.getExpressAgeInfo());
        return restResp;
    }
}