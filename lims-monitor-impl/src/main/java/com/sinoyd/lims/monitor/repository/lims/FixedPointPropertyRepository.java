package com.sinoyd.lims.monitor.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;

import java.util.List;


/**
 * FixedPointProperty数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedPointPropertyRepository extends IBaseJpaRepository<DtoFixedPointProperty, String> {

    /**
     * 根据ids获取监测计划集合
     *
     * @param ids 监测计划ids
     * @return 监测计划集合
     */
    List<DtoFixedPointProperty> findByIdIn(List<String> ids);

    /**
     * 根据监测计划父id列表查询
     *
     * @param ids 监测计划父id列表
     * @return 监测计划集合
     */
    List<DtoFixedPointProperty> findByParentIdIn(List<String> ids);

    /**
     * 根据年份获取监测计划集合
     *
     * @param year 年份
     * @return 监测计划集合
     */
    List<DtoFixedPointProperty> findByYear(Integer year);

    /**
     * 根据年份和月份获取监测计划集合
     *
     * @param year  年份
     * @param month 月份
     * @return 监测计划集合
     */
    List<DtoFixedPointProperty> findByYearAndMonth(Integer year, Integer month);
}