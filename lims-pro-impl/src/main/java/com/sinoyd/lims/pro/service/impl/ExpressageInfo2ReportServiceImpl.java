package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoExpressageInfo2Report;
import com.sinoyd.lims.pro.repository.ExpressageInfo2ReportRepository;
import com.sinoyd.lims.pro.service.ExpressageInfo2ReportService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;


/**
 * 快递报告关联操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/5
 * @since V100R001
 */
 @Service
public class ExpressageInfo2ReportServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoExpressageInfo2Report,String,ExpressageInfo2ReportRepository> implements ExpressageInfo2ReportService {

    @Override
    public void findByPage(PageBean<DtoExpressageInfo2Report> pb, BaseCriteria expressageInfo2ReportCriteria) {
        // 多表关联查询返回自定义字段
        pb.setEntityName("DtoExpressageInfo2Report e2r, DtoReport r");
        pb.setSelect("select e2r, r.code");

        super.findByPage(pb, expressageInfo2ReportCriteria);

        List<DtoExpressageInfo2Report> datas = pb.getData();
        List<DtoExpressageInfo2Report> newDatas = new ArrayList<>();

        Iterator<DtoExpressageInfo2Report> e2rIte = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (e2rIte.hasNext()) {
            Object obj = e2rIte.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoExpressageInfo2Report e2r = (DtoExpressageInfo2Report) objs[0];
            e2r.setCode((String) objs[1]);
            newDatas.add(e2r);
        }

        pb.setData(newDatas);
    }
}