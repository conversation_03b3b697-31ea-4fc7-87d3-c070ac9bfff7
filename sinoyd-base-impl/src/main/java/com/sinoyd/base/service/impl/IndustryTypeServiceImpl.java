package com.sinoyd.base.service.impl;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.entity.SampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.IndustryTypeService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.base.repository.rcc.IndustryTypeRepository;

import com.sinoyd.frame.util.UUIDHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 行业类型接口实现
 * <AUTHOR>
 * @version V1.0.0 2019/1/7
 * @since V100R001
 */
@Service
public class IndustryTypeServiceImpl extends BaseJpaServiceImpl<DtoIndustryType, String, IndustryTypeRepository> implements IndustryTypeService {

    @Autowired
    @Lazy
    SampleTypeService sampleTypeService;

    @Autowired
    SampleTypeRepository sampleTypeRepository;
    /**
     * 根据parentId获取行业类型(备选) 留作备用,可能会需要根据parentId获取 若需要去接口添加方法,然后取消注释@Override即可(实现)
     *
     * @param parentId
     * @return 行业类型集合
     */
    //@Override
    public List<DtoIndustryType> getByParentId(String parentId) {
        return repository.findByParentId(parentId);
    }

    /**
     * 分页获取行业类型
     *
     * @param pageBean             封装分页类
     * @param industryTypeCriteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoIndustryType> pageBean, BaseCriteria industryTypeCriteria) {
        pageBean.setEntityName("DtoIndustryType p");
        pageBean.setSelect("select p");
        super.findByPage(pageBean, industryTypeCriteria);
        List<DtoIndustryType> list = pageBean.getData();

        pageBean.setData(list);// 重新封装到pageBean返回到上层
    }

    /**
     * 新增行业类型
     *
     * @param industryType
     * @return
     */
    @Override
    @Transactional
    public DtoIndustryType save(DtoIndustryType industryType) {
        if (StringUtil.isEmpty(industryType.getIndustryName())) {
            throw new BaseException("行业类型名称不能为空！");
        } else if (StringUtil.isNull(industryType.getOrderNum())) {
            throw new BaseException("行业类型排序值不能为空！");
        }
        // 判断行业类型名称是否重复
        else if (repository.getCountByName(UUIDHelper.NewID(), industryType.getIndustryName()) > 0) {
            throw new BaseException("已存在相同名称的行业类型！");
        }
//        //预留字段parentId默认值空Guid
//        industryType.setParentId(BaseCodeHelper.GUID_EMPTY);
        return super.save(industryType);
    }

    /**
     * 更新行业类型
     *
     * @param industryType
     * @return
     */
    @Transactional
    @Override
    public DtoIndustryType update(DtoIndustryType industryType) {
        if (StringUtil.isEmpty(industryType.getIndustryName())) {
            throw new BaseException("行业类型名称不能为空！");
        } else if (StringUtil.isNull(industryType.getOrderNum())) {
            throw new BaseException("行业类型排序值不能为空！");
        }
        // 判断行业类型名称是否重复
        else if (repository.getCountByName(industryType.getId(), industryType.getIndustryName()) > 0) {
            throw new BaseException("已存在相同名称的行业类型！");
        }
//        //预留字段parentId默认值空id
//        industryType.setParentId(BaseCodeHelper.GUID_EMPTY);
        return super.update(industryType);
    }

    /**
     * 检测类型树
     */
    @Override
    public List<TreeNode> tree() {
        List<DtoSampleType> list = sampleTypeService.findAll();    //找到所有的检测类型
        return tree(list);
    }


    /**
     * 检测类型大类数
     * @return
     */
    @Override
    public List<TreeNode> bigTree() {
        List<DtoSampleType> list = sampleTypeService.findAllBigSampleType();    //找到所有的大类检测类型
        return tree(list);
    }

    @Override
    public DtoIndustryType findAttachPath(String id) {
        return repository.findOne(id);
    }

    @Override
    public List<DtoIndustryType> findAllDeleted(List<String> ids) {
        return repository.findAllDeleted(ids);
    }

    @Override
    public List<DtoIndustryType> findAllDeleted() {
        return repository.findAllDeleted();
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> industryTypeIds = (List<String>) ids;
        if (industryTypeIds.size() > 0) {
            super.logicDeleteById(ids);
            List<DtoSampleType> sampleTypes = sampleTypeRepository.findByIndustryTypeIds(industryTypeIds);
            List<String> sampleTypeIds = sampleTypes.stream().map(DtoSampleType::getId).distinct().collect(Collectors.toList());
            if (sampleTypeIds.size() > 0) { //删除相应的检测类型数据
                sampleTypeService.logicDeleteById(sampleTypeIds);
            }
        }
        return 0;
    }

    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String industryTypeId = (String) id;
        List<String> industryTypeIds = new ArrayList<>();
        industryTypeIds.add(industryTypeId);
        return logicDeleteById(industryTypeIds);
    }

    private List<TreeNode> tree(List<DtoSampleType> list) {
        ArrayList<TreeNode> nodeLists = new ArrayList<>();//最终返回的行业类型-检测类型树
        Iterable<DtoIndustryType> industryLists = repository.findAllOrderByOrderNumDesc();//找到所有的行业类型
        //将行业类型转换成树类型
        for (DtoIndustryType var : industryLists) {
            TreeNode node = new TreeNode();
            node.setId(var.getId());
            node.setParentId(var.getParentId());
            node.setCategory(null);
            node.setLabel(var.getIndustryName());
            node.setOrderNum(var.getOrderNum());
            node.setType("industryType");
            node.setChildren(new ArrayList<>());
            nodeLists.add(node);
        }
        Map<String, String> sam2ind = new HashMap<>();//检测模板-行业类型Map,用来组装树
        //将检测类型转换成树类型
        ArrayList<TreeNode> sampleLists = new ArrayList<>();
        for (SampleType var : list) {
            if (var.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue())
                    || var.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型小类.getValue())) {
                TreeNode node = new TreeNode();
                node.setId(var.getId());
                node.setParentId(var.getParentId());
                node.setCategory(var.getCategory());
                node.setLabel(var.getTypeName());
                node.setOrderNum(var.getOrderNum());
                node.setExtent1(var.getIcon());
                String type = "sampleType" + "~" + var.getIndustryTypeId();
                node.setType(type);
                node.setChildren(new ArrayList<>());
                sampleLists.add(node);
                if (node.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue())) {
                    sam2ind.put(node.getId(), var.getIndustryTypeId());
                }
            }
        }

        List<TreeNode> sampleTypeTree = new ArrayList<>();//检测类型树的大类
        //遍历检测类型,将list类型转换成tree树
        //排序
        List<TreeNode> lists = sampleLists.stream().sorted(Comparator.comparing(TreeNode::getOrderNum).reversed()).collect(Collectors.toList());
        for (TreeNode var : lists) {
            for (TreeNode li : lists) {
                if (li.getParentId().equals(var.getId())) {
                    var.setIsLeaf(false);
                    var.getChildren().add(li);
                } else {
                    var.setIsLeaf(true);
                }
            }
            //检测类型大类
            if (var.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue()) &&
                    var.getParentId().equals(UUIDHelper.GUID_EMPTY)) {
                sampleTypeTree.add(var);
            }
        }
        //遍历行业类型,找到行业类型下的大类检测类型,组装整颗行业类型-检测类型树
        for (TreeNode node : nodeLists) {
            List<String> ids = new ArrayList<>();
            for (Map.Entry<String, String> entry : sam2ind.entrySet()) {
                if (entry.getValue().equals(node.getId())) {
                    ids.add(entry.getKey());
                }
            }
            if (ids.size() > 0) {
                //排序
                List<TreeNode> sampleTypeTreeLists = sampleTypeTree.stream().sorted(Comparator.comparing(TreeNode::getOrderNum).reversed()).collect(Collectors.toList());
                List<TreeNode> leafs = sampleTypeTreeLists.stream().filter(p -> ids.contains(p.getId())).collect(Collectors.toList());
                if (leafs.size() > 0) {
                    node.setIsLeaf(false);
                    for (TreeNode leaf : leafs) {
                        node.getChildren().add(leaf);
                    }
                } else {
                    node.setIsLeaf(true);
                }
            } else {
                node.setIsLeaf(true);
            }

        }
        return nodeLists;
    }
}