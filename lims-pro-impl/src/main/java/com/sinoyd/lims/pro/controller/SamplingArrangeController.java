package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.FolderPeriodCriteria;
import com.sinoyd.lims.pro.criteria.SamplingArrangeCriteria;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoSamplingArrange;
import com.sinoyd.lims.pro.service.SamplingArrangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 采样计划安排
 * <AUTHOR>
 * @version V1.0.0 2023/11/08
 * @since V100R001
 */
@Api(tags = "采样计划安排")
@RestController
@RequestMapping("/api/pro/samplingArrangePlan")
public class SamplingArrangeController extends BaseJpaController<DtoSamplingArrange,String, SamplingArrangeService> {


    /**
     * 树形动态分页查询
     * @param samplingArrangeCriteria 查询条件
     * @return 安排列表
     */
    @GetMapping("/tree")
    @ApiOperation(value = "树形动态分页查询", notes = "树形动态分页查询")
    public RestResponse<List<DtoProject>> showTree(SamplingArrangeCriteria samplingArrangeCriteria){
        RestResponse<List<DtoProject>> res = new RestResponse<>();
        PageBean<DtoSamplingArrange> pageBean = super.getPageBean();
        PageBean<DtoProject> projectPageBean = service.showTree(pageBean,samplingArrangeCriteria);
        res.setRestStatus(StringUtil.isEmpty(projectPageBean.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        res.setCount(projectPageBean.getRowsCount());
        res.setData(projectPageBean.getData());
        return res;
    }


    /**
     * 动态分页查询
     * @param samplingArrangeCriteria 查询条件
     * @return 安排列表
     */
    @GetMapping
    @ApiOperation(value = "分页动态条件查询SamplingArrangePlan", notes = "分页动态条件查询SamplingArrangePlan")
    public RestResponse<List<DtoSamplingArrange>> findByPage(SamplingArrangeCriteria samplingArrangeCriteria){
        RestResponse<List<DtoSamplingArrange>> res = new RestResponse<>();
        PageBean<DtoSamplingArrange> pageBean = super.getPageBean();
        service.findByPage(pageBean, samplingArrangeCriteria);
        res.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        res.setData(pageBean.getData());
        res.setCount(pageBean.getRowsCount());
        return res;
    }


    /**
     * 采样安排预览
     * @param samplingArrangeCriteria 查询条件
     * @return 安排列表
     */
    @GetMapping("/preview")
    @ApiOperation(value = "分页动态条件查询SamplingArrangePlan", notes = "分页动态条件查询SamplingArrangePlan")
    public RestResponse<List<Map<String,Object>>> collectPreviewData(SamplingArrangeCriteria samplingArrangeCriteria){
        RestResponse<List<Map<String,Object>>> res = new RestResponse<>();
        PageBean<DtoSamplingArrange> pageBean = super.getPageBean();
        service.findByPage(pageBean, samplingArrangeCriteria);
        List<Map<String,Object>> previewData = new ArrayList<>();
        service.collectPreviewData(pageBean.getData(),previewData);
        res.setRestStatus(StringUtil.isEmpty(previewData) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        res.setData(previewData);
        res.setCount(previewData.size());
        return res;
    }

    /**
     * 批量保存采样安排
     * @param dtoSamplingArrange  安排属性
     * @return 响应
     */
    @PostMapping
    @ApiOperation(value = "批量保存采样安排", notes = "批量保存采样安排")
    public RestResponse<DtoSamplingArrange> batchSaveArrange(@RequestBody DtoSamplingArrange dtoSamplingArrange){
        RestResponse<DtoSamplingArrange> res = new RestResponse<>();
        res.setData(service.batchSaveArrange(dtoSamplingArrange));
        res.setRestStatus(ERestStatus.SUCCESS);
        return res;
    }

    /**
     * 采样完成,将状态设置为是
     * @param ids   安排标识
     * @return 响应
     */
    @PostMapping("/submit")
    @ApiOperation(value = "批量重新安排", notes = "批量重新安排")
    public RestResponse<Void> submitArrange(@RequestBody List<String> ids){
        RestResponse<Void> res = new RestResponse<>();
        service.submitArrange(ids);
        res.setRestStatus(ERestStatus.SUCCESS);
        return res;
    }

    /**
     * 批量取消安排
     * @param ids 安排标识列表
     * @return 移除数量
     */
    @DeleteMapping
    @ApiOperation(value = "批量取消安排", notes = "批量取消安排")
    public RestResponse<Void> batchDelArrange(@RequestBody List<String> ids){
        RestResponse<Void> res = new RestResponse<>();
        service.batchDelArrange(ids);
        res.setRestStatus(ERestStatus.SUCCESS);
        return res;
    }

    @GetMapping("/findSamplingPlan/{samplingPlanId}")
    public RestResponse<DtoSamplingArrange> findSamplingPlan(@PathVariable(name = "samplingPlanId") String samplingPlanId) {
        RestResponse<DtoSamplingArrange> response = new RestResponse<>();
        response.setData(service.findBySamplingPlanId(samplingPlanId));
        response.setRestStatus(ERestStatus.SUCCESS);
        return response;
    }

    @PostMapping("/audit")
    public RestResponse<Void> audit(@RequestBody DtoSamplingArrange dtoSamplingArrange){
        RestResponse<Void> res = new RestResponse<>();
        service.submit(dtoSamplingArrange.getIdList(), dtoSamplingArrange.getStatus());
        res.setRestStatus(ERestStatus.SUCCESS);
        return res;
    }

    /**
     * 下载采样计划安排表
     * @param criteria 查询条件
     * @param response 响应流
     */
    @GetMapping("/download")
    @ApiOperation(value = "下载采样计划安排表", notes = "下载采样计划安排表")
    public void download(SamplingArrangeCriteria criteria, HttpServletResponse response){
        service.download(criteria,response);
    }

    /**
     * 点位周期动态分页查询
     * @param folderPeriodCriteria 查询条件
     * @return 安排列表
     */
    @GetMapping("/folderPeriodPage")
    @ApiOperation(value = "点位周期动态分页查询", notes = "点位周期动态分页查询")
    public RestResponse<List<DtoSamplingArrange>> folderPeriodPageQuery(FolderPeriodCriteria folderPeriodCriteria){
        RestResponse<List<DtoSamplingArrange>> res = new RestResponse<>();
        PageBean<DtoSamplingArrange> pageBean = super.getPageBean();
        service.folderPeriodPageQuery(pageBean, folderPeriodCriteria);
        res.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        res.setData(pageBean.getData());
        res.setCount(pageBean.getRowsCount());
        return res;
    }
}
