package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoReportDeprive;

import java.util.List;

/**
 * ReportDeprive操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
public interface ReportDepriveService extends IBaseJpaService<DtoReportDeprive, String> {

    /**
     * 根据项目id查询
     *
     * @param projectId 项目id
     * @return List<DtoReportDeprive>
     */
    List<DtoReportDeprive> findByProjectId(String projectId);

}
