package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityConfig;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityList;
import com.sinoyd.lims.lim.repository.lims.DocAuthorityConfigRepository;
import com.sinoyd.lims.lim.repository.lims.DocAuthorityListRepository;
import com.sinoyd.lims.lim.service.DocAuthorityListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文件夹权限接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/19
 * @since V100R001
 */
@Service
public class DocAuthorityListServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoDocAuthorityList, String, DocAuthorityListRepository> implements DocAuthorityListService {

    private DocAuthorityConfigRepository docAuthorityConfigRepository;

    private CodeService codeService;

    @Override
    @Transactional
    public void initAuthorityConfig(String objectId, String userId) {
        List<DtoCode> authList = codeService.findCodes(LimCodeHelper.LIM_DocumentAuthorityType);
        List<DtoDocAuthorityList> authorityLists = new ArrayList<>();
        List<DtoDocAuthorityConfig> authorityConfigs = new ArrayList<>();
        for (DtoCode dtoCode : authList) {
            DtoDocAuthorityList authorityList = new DtoDocAuthorityList();
            authorityList.setAuthCode(dtoCode.getDictCode());
            authorityList.setAuthName(dtoCode.getDictName());
            authorityList.setSortNum(dtoCode.getSortNum());
            authorityList.setObjectId(objectId);
            authorityList.setDefaultOpenInd(dtoCode.getExtendI1() == 1);
            authorityLists.add(authorityList);
            DtoDocAuthorityConfig authorityConfig = new DtoDocAuthorityConfig();
            authorityConfig.setUserId(userId);
            authorityConfig.setObjectId(objectId);
            authorityConfig.setAuthorityListId(authorityList.getId());
            authorityConfig.setAuthCode(dtoCode.getDictCode());
            authorityConfig.setAuthName(dtoCode.getDictName());
            authorityConfig.setSortNum(dtoCode.getSortNum());
            authorityConfig.setDefaultOpenInd(dtoCode.getExtendI1() == 1);
            authorityConfigs.add(authorityConfig);
        }
        if (StringUtil.isNotEmpty(authorityLists)) {
            repository.save(authorityLists);
        }
        if (StringUtil.isNotEmpty(authorityConfigs)) {
            docAuthorityConfigRepository.save(authorityConfigs);
        }
    }

    @Override
    @Transactional
    public void processOldAuthorityConfigData(List<String> objectIds) {
        List<DtoCode> authList = codeService.findCodes(LimCodeHelper.LIM_DocumentAuthorityType);
        List<DtoDocAuthorityList> authorityLists = repository.findByObjectIdIn(objectIds);
        List<DtoDocAuthorityList> saveAuthorityLists = new ArrayList<>();
        for (String objectId : objectIds) {
            List<DtoDocAuthorityList> authorityLists2Object = authorityLists.stream().filter(a -> a.getObjectId().equals(objectId)).collect(Collectors.toList());
            for (DtoCode dtoCode : authList) {
                if (authorityLists2Object.stream().noneMatch(a -> a.getAuthCode().equals(dtoCode.getDictCode()))) {
                    DtoDocAuthorityList authorityList = new DtoDocAuthorityList();
                    authorityList.setAuthCode(dtoCode.getDictCode());
                    authorityList.setAuthName(dtoCode.getDictName());
                    authorityList.setSortNum(dtoCode.getSortNum());
                    authorityList.setObjectId(objectId);
                    authorityList.setDefaultOpenInd(dtoCode.getExtendI1() == 1);
                    saveAuthorityLists.add(authorityList);
                }
            }
        }
        authorityLists.addAll(repository.save(saveAuthorityLists));
        //处理一些老数据，老数据authorityListId为‘00000000-0000-0000-0000-000000000000’，修改为对应权限id
        List<DtoDocAuthorityConfig> authorityConfigList = docAuthorityConfigRepository.findByObjectIdInAndAuthorityListId(objectIds, UUIDHelper.GUID_EMPTY);
        if (StringUtil.isNotEmpty(authorityConfigList)) {
            Map<String, String> authorityListMap = authorityLists.stream().collect(Collectors.toMap(d -> d.getObjectId() + "_" + d.getAuthCode(), dto -> dto.getId()));
            for (DtoDocAuthorityConfig authorityConfig : authorityConfigList) {
                authorityConfig.setAuthorityListId(authorityListMap.get(authorityConfig.getObjectId() + "_" + authorityConfig.getAuthCode()));
            }
            docAuthorityConfigRepository.save(authorityConfigList);
        }
    }

    @Autowired
    public void setDocAuthorityConfigRepository(DocAuthorityConfigRepository docAuthorityConfigRepository) {
        this.docAuthorityConfigRepository = docAuthorityConfigRepository;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

}
