package com.sinoyd.lims.api.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 移动端附件查询条件
 *
 * <AUTHOR>
 * @date V1.0.0 2024/02/26
 * @version: V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentPhoneCriteria extends BaseCriteria {

    /**
     * 文件类型id
     */
    private String docTypeId;

    /**
     * 关键字
     */
    private String key;

    
    private List<String> ids;



    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.folderId = r.id and r.projectId = t.id and t.isDeleted = 0 and r.isDeleted = 0");

        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (t.projectName like :key  or t.inspectedEnt like :key ");
            condition.append(" or r.recordCode like :key)");
            this.values.put("key", "%" + this.key + "%");
        }

        if (StringUtils.isNotNullAndEmpty(this.docTypeId)) {
            condition.append(" and p.docTypeId = :docTypeId ");
            this.values.put("docTypeId", this.docTypeId);
        }

        condition.append(" and p.isDeleted = 0");
        return condition.toString();
    }
}
