package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.base.dto.customer.DtoImportTestFormula;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaForUpdate;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.entity.ParamsPartFormula;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.*;

import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoParamsPartFormula实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_ParamsPartFormula")
 @Data
 @DynamicInsert
 public  class DtoParamsPartFormula extends ParamsPartFormula {

    @Transient
    private Set<DtoParamsConfig> paramsConfigList = new HashSet<>();

    /**
     * 获取参数公式全形式
     * @return 参数公式全形式
     */
    public String getFullFormula(){
        if(StringUtil.isEmpty(getFormula())){
            return "";
        }
        return getFormula() + " = " + getParamsName();
    }

    public DtoParamsPartFormula(){};

    /**
     * 测试项目公式导入数据转换
     * @param importTestFormula 导入实体
     */
    public DtoParamsPartFormula(DtoImportTestFormula importTestFormula){
        this.setFormula(importTestFormula.getPartFormula());
        this.setFormulaType(EnumLIM.EnumPartFormulaType.加标公式.getValue());
        if(StringUtil.isNotNull(importTestFormula.getMostSignificance())){
            this.setMostSignificance(importTestFormula.getMostSignificance());
        }
        if(StringUtil.isNotNull(importTestFormula.getMostDecimal())){
            this.setMostDecimal(importTestFormula.getMostDecimal());
        }
        //ps：公式导入（新增），针对填写了检出限的，测得量公式中“是否使用测试项目检出限”开关需要关闭
        if(StringUtil.isNotEmpty(importTestFormula.getDetectionLimit())){
            this.setDetectionLimit(importTestFormula.getDetectionLimit());
            this.setUseTestLimit(false);
        }
        else{
            this.setUseTestLimit(true);
        }
        if(StringUtil.isNotEmpty(importTestFormula.getCalculationMode())){
            this.setCalculationMode(EnumLIM.EnumCalculationMode.getValueByName(importTestFormula.getCalculationMode()));
        }
    }

    /**
     * 测试项目公式导入数据转换
     * @param dtoImportTestFormulaForUpdate 导入实体
     */
    public void importToEntity(DtoImportTestFormulaForUpdate dtoImportTestFormulaForUpdate){
        if(StringUtil.isNotEmpty(dtoImportTestFormulaForUpdate.getPartFormula())){
            this.setFormula(dtoImportTestFormulaForUpdate.getPartFormula());
        }
        if(StringUtil.isNotNull(dtoImportTestFormulaForUpdate.getMostSignificance())){
            this.setMostSignificance(dtoImportTestFormulaForUpdate.getMostSignificance());
        }
        if(StringUtil.isNotNull(dtoImportTestFormulaForUpdate.getMostDecimal())){
            this.setMostDecimal(dtoImportTestFormulaForUpdate.getMostDecimal());
        }
        if(StringUtil.isNotEmpty(dtoImportTestFormulaForUpdate.getDetectionLimit())){
            this.setDetectionLimit(dtoImportTestFormulaForUpdate.getDetectionLimit());
        }
        if(StringUtil.isNotEmpty(dtoImportTestFormulaForUpdate.getCalculationMode())){
            this.setCalculationMode(EnumLIM.EnumCalculationMode.getValueByName(dtoImportTestFormulaForUpdate.getCalculationMode()));
        }else{
            this.setCalculationMode(null);
        }
        this.setUseTestLimit(StringUtil.isEmpty(dtoImportTestFormulaForUpdate.getDetectionLimit()));
    }
}