package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReportFolderInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoReportFolderInfo实体
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ReportFolderInfo")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoReportFolderInfo extends ReportFolderInfo {

    public DtoReportFolderInfo(){

    }

    /**
     * 电子报告样品信息列表
     */
    @Transient
    private List<DtoReportSampleInfo> reportSampleInfoList;
}