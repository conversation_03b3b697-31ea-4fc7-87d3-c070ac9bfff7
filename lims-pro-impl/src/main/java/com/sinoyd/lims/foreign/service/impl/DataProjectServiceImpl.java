package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataProjectVO;
import com.sinoyd.lims.foreign.service.DataProjectService;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataProjectServiceImpl extends AbsQueryDataServiceImpl<DataProjectVO> implements DataProjectService {

    private PersonRepository personRepository;

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select t1.id,t1.projectCode,t2.NAME,t1.inceptPersonId,t3.leaderId,t3.reportMakerId,");
        sql.append("t1.inceptTime,t1.monitorPurp,t1.monitorMethods,t1.customerRequired,");
        sql.append("t1.saveCondition,t1.projectName,t1.customerName,t1.customerAddress,");
        sql.append("t1.linkMan,t1.linkPhone,t1.inspectedEnt,t1.inspectedLinkMan,t1.inspectedLinkPhone,");
        sql.append("t1.inspectedAddress,t1.orgId,t1.domainId ");
        sql.append(" from TB_PRO_Project t1");
        sql.append(" INNER JOIN TB_LIM_ProjectType t2 ON t1.projectTypeId = t2.id");
        sql.append(" INNER JOIN TB_PRO_ProjectPlan t3 ON t1.id = t3.projectId");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND t1.modifyDate > :syncTime ", "syncTime");
        super.appendCondition(sql, map, "AND t1.id in (:projectIds)", "projectIds");
    }

    @Override
    protected RowMapper<DataProjectVO> getEntityRowMapper() {

        return (rs, i) -> {
            DataProjectVO projectVO = new DataProjectVO();
            projectVO.setId(UUIDHelper.NewID());
            projectVO.setProjectId(rs.getString("id"));
            projectVO.setProjectCode(rs.getString("projectCode"));
            projectVO.setProjectTypeName(rs.getString("name"));
            projectVO.setInceptPersonName(rs.getString("inceptPersonId"));
            projectVO.setLeader(rs.getString("leaderId"));
            projectVO.setReportMakerName(rs.getString("reportMakerId"));
            projectVO.setInceptTime(rs.getDate("inceptTime"));
            projectVO.setMonitorPurp(rs.getString("monitorPurp"));
            projectVO.setMonitorMethods(rs.getString("monitorMethods"));
            projectVO.setCustomerRequired(rs.getString("customerRequired"));
            projectVO.setSaveCondition(rs.getString("saveCondition"));
            projectVO.setProjectName(rs.getString("projectName"));
            projectVO.setCustomerName(rs.getString("customerName"));
            projectVO.setCustomerAddress(rs.getString("customerAddress"));
            projectVO.setLinkMan(rs.getString("linkMan"));
            projectVO.setLinkPhone(rs.getString("linkPhone"));
            projectVO.setInspectedEnt(rs.getString("inspectedEnt"));
            projectVO.setInspectedLinkMan(rs.getString("inspectedLinkMan"));
            projectVO.setInspectedLinkPhone(rs.getString("inspectedLinkPhone"));
            projectVO.setInspectedAddress(rs.getString("inspectedAddress"));
            projectVO.setOrgId(rs.getString("orgId"));
            projectVO.setDomainId(rs.getString("domainId"));
            projectVO.setSyncTime(new Date());
            return projectVO;
        };
    }

    @Override
    protected void individuationData(Map<String, Object> map, List<DataProjectVO> list) {
        List<DtoPerson> personList = personRepository.findAllDeleted();
        Map<String, String> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
        list.forEach(p -> {
            p.setInceptPersonName(personMap.getOrDefault(p.getInceptPersonName(), ""));
            p.setLeader(personMap.getOrDefault(p.getLeader(), ""));
            p.setReportMakerName(personMap.getOrDefault(p.getReportMakerName(), ""));
        });

    }


    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }
}
