package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * ParamsPartFormula实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ParamsPartFormula")
 @Data
 public  class ParamsPartFormula implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  ParamsPartFormula() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 公式Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("公式Id")
	private String formulaId;
    
    /**
    * 部分公式
    */
    @Column(length=1000,nullable=false)
    @ApiModelProperty("部分公式")
    @Length(message = "部分公式{validation.message.length}", max = 1000)
	private String formula;


   /**
     * 参数名称
    */
    @Column(length=1000)
    @ApiModelProperty("参数名称")
    @Length(message = "参数名称{validation.message.length}", max = 100)
    private String paramsName;
    
    /**
    * 有效位数
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("有效位数")
    private Integer mostSignificance;
    
    /**
    * 小数位数
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("小数位数")
    private Integer mostDecimal;
    
    /**
    * 排序值
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;
    
    /**
    * 类型（EnumPartFormulaType：0.修约公式（用于测试公式及原始记录单参数公式中的修约） 1.检测类型参数公式 2.加标公式 3.BOD5判断公式 4.参数公式（如减空白后吸光度=吸光度-空白） 5.串联出证公式）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("类型（EnumPartFormulaType：0.修约公式（用于测试公式及原始记录单参数公式中的修约） 1.检测类型参数公式 2.加标公式 3.BOD5判断公式 4.参数公式（如减空白后吸光度=吸光度-空白） 5.串联出证公式）")
    private Integer formulaType;

    /**
     * 检出限
     */
    @Column
    @ApiModelProperty("检出限")
    private String detectionLimit;

    /**
     * 计算方式：枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零
     */
    @Column
    @ColumnDefault("-1")
    @ApiModelProperty("计算方式（枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零）")
    private Integer calculationMode;

    /**
     * 是否使用测试项目检出限
     */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("是否使用测试项目检出限")
    private Boolean useTestLimit;

    /**
     * 修约计算方式（参考枚举 EnumCalculateWay）（0： 先修约后计算，1：先计算后修约）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("修约计算方式（参考枚举 EnumCalculateWay）（0： 先修约后计算，1：先计算后修约）")
    private Integer reviseCalculationType;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }