package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.InstrumentUseRecord2Sample;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoInstrumentUseRecord2Sample实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_InstrumentUseRecord2Sample")
 @Data
 @DynamicInsert
 public  class DtoInstrumentUseRecord2Sample extends InstrumentUseRecord2Sample {
    private static final long serialVersionUID = 1L;


    /**
     * 样品编号
     */
    @Transient
    private String sampleCode;

    /**
     * 默认的构造函数
     */
    public DtoInstrumentUseRecord2Sample() {

    }

    /***
     * 该构造函数用到InstrumentUseRecordServiceImpl下的exportExcel方法
     * @param instrumentUseRecordId 仪器使用记录id
     * @param sampleId 样品id
     * @param sampleCode 样品编号
     */
    public DtoInstrumentUseRecord2Sample(String instrumentUseRecordId,
                                         String sampleId,
                                         String sampleCode){
        this.setInstrumentUseRecordId(instrumentUseRecordId);
        this.setSampleId(sampleId);
        this.setSampleCode(sampleCode);
    }
}