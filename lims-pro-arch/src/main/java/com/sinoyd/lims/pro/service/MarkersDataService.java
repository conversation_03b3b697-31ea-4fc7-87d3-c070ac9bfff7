package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.lim.dto.customer.DtoMarkersData;

import java.util.List;

/**
 * 数据标记操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/1
 * @since V100R001
 */
public interface MarkersDataService {
    /**
     * 修改验证状态和次数
     *
     * @param dtoMarkersData 数据标注实体
     */
    void updateValidate(DtoMarkersData dtoMarkersData);

//    /**
//     * 修改是用次数
//     * @param dtoMarkersData 数据标注实体
//     */
//    void updateUsageNum(DtoMarkersData dtoMarkersData);
}
