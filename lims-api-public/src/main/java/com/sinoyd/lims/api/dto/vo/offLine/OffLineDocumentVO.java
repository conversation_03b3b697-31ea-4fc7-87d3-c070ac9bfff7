package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：附件表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineDocumentVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 关联id
     */
    private String folderId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 物理文件名称
     */
    private String physicalName;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 文件类型
     */
    private String docTypeId;

    /**
     * 文件类型名称
     */
    private String docTypeName;

    /**
     * 文件大小
     */
    private Integer docSize;

    /**
     * 文件后缀
     */
    private String docSuffix;

    /**
     * 上传人Id
     */
    private String uploadPersonId;

    /**
     * 上传人名称
     */
    private String uploadPerson;

    /**
     * 所属机构
     */
    private String orgId;
}
