package com.sinoyd.lims.lim.data.qcconfig.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.configuration.QcDeviationFormulaConfig;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoQualityLimitDisposition;
import com.sinoyd.base.dto.rcc.DtoSubstitute;
import com.sinoyd.base.dto.vo.DeviationFormulaSrcVO;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.SubstituteService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.QualityControlLimitCriteria;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.data.qcconfig.dto.DtoQCTemp;
import com.sinoyd.lims.lim.data.qcconfig.dto.DtoQcType;
import com.sinoyd.lims.lim.data.qcconfig.service.QCConfigService;
import com.sinoyd.lims.lim.data.qcconfig.strategy.base.AbsQCConfig;
import com.sinoyd.lims.lim.dto.customer.DtoQualityControlDeviation;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.entity.Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityLimitDispositionRepository;
import com.sinoyd.lims.lim.service.TestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Transient;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sinoyd.base.constants.IBaseConstants.DEVIATION_FORMULA;

/**
 * 质控限值实现类
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/14
 */
@Service
@Slf4j
public class QCConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoQualityControlLimit, String, QualityControlLimitRepository> implements QCConfigService {

    private TestService testService;

    private List<AbsQCConfig> absQCConfigList;

    private SubstituteService substituteService;

    @Autowired
    private CodeService codeService;

    private RedisTemplate redisTemplate;

    private QualityLimitDispositionRepository qualityLimitDispositionRepository;

    /**
     * 分页查询测试项目
     *
     * @param pb       分页
     * @param criteria 条件
     */
    @Override
    public void findTestByPage(PageBean<DtoTest> pb, TestCriteria criteria) {
        if (!EnumLIM.EnumQCRangeStatus.所有.getValue().equals(criteria.getQcRangeStatus())) {
            List<DtoQualityControlLimit> qcLimitList = repository.findAll();
            List<String> testIds = qcLimitList.stream().map(DtoQualityControlLimit::getTestId).distinct()
                    .collect(Collectors.toList());
            if (EnumLIM.EnumQCRangeStatus.已配置.getValue().equals(criteria.getQcRangeStatus())) {
                criteria.setIncludeIds(testIds);
            } else if (EnumLIM.EnumQCRangeStatus.未配置.getValue().equals(criteria.getQcRangeStatus())) {
                criteria.setExcludeIds(testIds);
            }
        }
        testService.findByPage(pb, criteria);
        List<DtoTest> testList = pb.getData();
        for (DtoTest test : testList) {
            int count = repository.findByTestId(test.getId()).size();
            if (count > 0) {
                test.setQcRangeStatus(EnumLIM.EnumQCRangeStatus.已配置.toString());
            } else {
                test.setQcRangeStatus(EnumLIM.EnumQCRangeStatus.未配置.toString());
            }
            test.setQcRangeNum(count);
        }
        //按照分析方法正序排序
        testList = testList.stream().sorted(Comparator.comparing(Test::getRedAnalyzeMethodName).thenComparing(Test::getRedAnalyzeItemName)).collect(Collectors.toList());
        pb.setData(testList);
    }

    /**
     * 分页查询测试项目关联质控限值数据
     *
     * @param page     分页
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoQualityControlLimit> page, QualityControlLimitCriteria criteria) {
        page.setEntityName("DtoQualityControlLimit a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        List<DtoQualityControlLimit> data = page.getData();
        List<DtoQualityControlLimit> sortQCConfig = sortQCConfig(data);
        page.setData(sortQCConfig);
    }

    /**
     * 批量清除测试项目对应的质控限值列表
     *
     * @param testIdList 测试项目id列表
     */
    @Override
    @Transactional
    public void clearQcLimit(List<String> testIdList) {
        if (StringUtil.isNotEmpty(testIdList)) {
            List<DtoQualityControlLimit> limitList = repository.findByTestIdIn(testIdList);
            if (StringUtil.isNotEmpty(limitList)) {
                repository.delete(limitList);
            }
        }
    }

    @Override
    @Transactional
    public void uploadOldLimit() {
        List<DtoQualityControlLimit> limitList = repository.findByFormulaIsNotNull();
        List<DtoQualityLimitDisposition> dispositionList = qualityLimitDispositionRepository.findAll();
        limitList.forEach(p -> {
            if (UUIDHelper.GUID_EMPTY.equals(p.getDispositionId())) {
                //找到对应的配置
                Optional<DtoQualityLimitDisposition> dispositionOptional = dispositionList.stream().filter(d -> d.getQcGrade().equals(p.getQcGrade())
                        && d.getQcType().equals(p.getQcType()) && d.getJudgingMethod().equals(p.getJudgingMethod())
                        && d.getFormula().equals(p.getFormula())).findFirst();
                dispositionOptional.ifPresent(d -> {
                    p.setDispositionId(d.getId());
                });
            }
        });
        if (limitList.size() > 0) {
            repository.batchUpdate(limitList);
        }
    }

    /**
     * 分页查询测试项目关联质控限值数据
     *
     * @param qualityControlDeviation 实体对象
     * @return 偏差公式
     */
    @Override
    @Transactional
    public DtoQualityControlDeviation saveDeviation(DtoQualityControlDeviation qualityControlDeviation) {
        List<DtoQualityControlLimit> deviationLimitList = repository.findByTestId(DEVIATION_FORMULA);
        List<DeviationFormulaSrcVO> deviationFormulaSrcVOList = getDeviationFormulaQcTypeDataSource();
        DeviationFormulaSrcVO vo = deviationFormulaSrcVOList.stream().filter(p -> qualityControlDeviation.getQualityControlTypeCode().equals(p.getQualityControlTypeCode()))
                .findFirst().orElse(null);
        if (StringUtil.isNull(vo)) {
            throw new BaseException("偏差质控类型不存在!");
        }
        Integer qcGrade = Integer.valueOf(vo.getQcGrade());
        Integer qcType = Integer.valueOf(vo.getQcType());
        List<DtoQualityControlLimit> filterLimitList = deviationLimitList.stream().filter(p -> p.getQcGrade().equals(qcGrade)
                && p.getQcType().equals(qcType)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(filterLimitList)) {
            throw new BaseException("已存在" + vo.getQualityControlTypeName() + "类型的设置，请确认后再操作");
        }
        DtoQualityControlLimit limit = new DtoQualityControlLimit();
        limit.setFormula(EnumLIM.EnumDeviationFormula.getValueByCode(qualityControlDeviation.getDeviationFormula()));
        limit.setTestId(DEVIATION_FORMULA);
        limit.setQcGrade(qcGrade);
        limit.setQcType(qcType);
        if (!StringUtil.isNotEmpty(limit.getSubstituteId())) {
            limit.setSubstituteId(UUIDHelper.GUID_EMPTY);
        }
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_DeviationFormula.getValue());
        repository.save(limit);
        qualityControlDeviation.setId(limit.getId());
        redisTemplate.opsForHash().put(key, qcGrade + "_" + qcType, limit.getFormula());
        return qualityControlDeviation;
    }

    /**
     * 修改偏差公式
     *
     * @param qualityControlDeviation 实体对象
     * @return 偏差公式
     */
    @Override
    @Transactional
    public DtoQualityControlDeviation updateDeviation(DtoQualityControlDeviation qualityControlDeviation) {
        List<DeviationFormulaSrcVO> deviationFormulaSrcVOList = getDeviationFormulaQcTypeDataSource();
        DeviationFormulaSrcVO vo = deviationFormulaSrcVOList.stream().filter(p -> qualityControlDeviation.getQualityControlTypeCode().equals(p.getQualityControlTypeCode()))
                .findFirst().orElse(null);
        if (StringUtil.isNull(vo)) {
            throw new BaseException("偏差质控类型不存在!");
        }
        List<DtoQualityControlLimit> deviationLimitList = repository.findByTestId(DEVIATION_FORMULA);
        DtoQualityControlLimit oldLimit = deviationLimitList.stream().filter(p -> p.getId().equals(qualityControlDeviation.getId())).findFirst().orElse(null);
        if (StringUtil.isNull(oldLimit)) {
            throw new BaseException("偏差公式设置不存在!");
        }
        deviationLimitList.remove(oldLimit);
        Integer qcGrade = Integer.valueOf(vo.getQcGrade());
        Integer qcType = Integer.valueOf(vo.getQcType());
        List<DtoQualityControlLimit> duplicateLimitList = deviationLimitList.stream().filter(p -> p.getQcGrade().equals(qcGrade)
                && p.getQcType().equals(qcType)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(duplicateLimitList)) {
            throw new BaseException("同种质控类型的偏差公式已存在，不能重复设置！");
        }
        oldLimit.setFormula(EnumLIM.EnumDeviationFormula.getValueByCode(qualityControlDeviation.getDeviationFormula()));
        oldLimit.setQcGrade(qcGrade);
        oldLimit.setQcType(qcType);
        repository.save(oldLimit);
        //更新redis信息
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_DeviationFormula.getValue());
        redisTemplate.opsForHash().put(key, qcGrade + "_" + qcType, oldLimit.getFormula());
        return qualityControlDeviation;
    }

    @Override
    public Integer deleteDeviation(List<String> ids) {
        List<DtoQualityControlLimit> deviationLimitList = repository.findAll(ids);
        deviationLimitList = deviationLimitList.stream().filter(p -> DEVIATION_FORMULA.equals(p.getTestId())).collect(Collectors.toList());
        List<DtoQualityControlLimit> dftDeviationLimitList = deviationLimitList.stream().filter(p -> p.getQcGrade().equals(-1)
                && p.getQcType().equals(-1)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(dftDeviationLimitList)) {
            throw new BaseException("默认的偏差公式不能删除，请重新勾选!");
        }
        repository.delete(deviationLimitList);
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_DeviationFormula.getValue());
        for (DtoQualityControlLimit deviationLimit : deviationLimitList) {
            redisTemplate.opsForHash().delete(key, deviationLimit.getQcGrade() + "_" + deviationLimit.getQcType());
        }
        return deviationLimitList.size();
    }

    /**
     * 查询质控类型对应的偏差公式
     *
     * @return 偏差公式
     */
    @Override
    public List<DtoQualityControlDeviation> queryDeviation() {
        List<DtoQualityControlLimit> limitList = repository.findByTestId(DEVIATION_FORMULA);
        List<DtoQualityControlDeviation> qualityControlDeviationList = new ArrayList<>();
        List<DtoQualityControlDeviation> otherDeviationList = new ArrayList<>();
        DtoQualityControlDeviation defaultDeviation = null;
        if (StringUtil.isNotEmpty(limitList)) {
            List<DeviationFormulaSrcVO> deviationFormulaSrcVOList = getDeviationFormulaQcTypeDataSource();
            for (DtoQualityControlLimit limit : limitList) {
                DeviationFormulaSrcVO vo = deviationFormulaSrcVOList.stream().filter(p -> String.valueOf(limit.getQcGrade()).equals(p.getQcGrade())
                        && String.valueOf(limit.getQcType()).equals(p.getQcType())).findFirst().orElse(null);
                if (StringUtil.isNotNull(vo)) {
                    DtoQualityControlDeviation qualityControlDeviation = new DtoQualityControlDeviation();
                    qualityControlDeviation.setId(limit.getId());
                    qualityControlDeviation.setQualityControlTypeName(vo.getQualityControlTypeName());
                    qualityControlDeviation.setQualityControlTypeCode(vo.getQualityControlTypeCode());
                    qualityControlDeviation.setDeviationFormula(EnumLIM.EnumDeviationFormula.getCodeByValue(limit.getFormula()));
                    if ("默认".equals(vo.getQualityControlTypeName())) {
                        defaultDeviation = qualityControlDeviation;
                    } else {
                        otherDeviationList.add(qualityControlDeviation);
                    }
                }
            }
        }
        if (StringUtil.isNotNull(defaultDeviation)) {
            qualityControlDeviationList.add(defaultDeviation);
        }
        if (StringUtil.isNotEmpty(otherDeviationList)) {
            qualityControlDeviationList.addAll(otherDeviationList);
        }
        return qualityControlDeviationList;
    }

    /**
     * 按照质控类型排序
     *
     * @param data 需要排序的数据
     * @return 返回数据
     */
    private List<DtoQualityControlLimit> sortQCConfig(List<DtoQualityControlLimit> data) {
        List<DtoCode> lim_qcConfigSort = codeService.findCodes("LIM_QCConfigSort");
        for (DtoQualityControlLimit qcConfig : data) {
            Optional<DtoCode> sort = lim_qcConfigSort.stream().filter(p -> qcConfig.getQcTypeName().equals(p.getDictName())).findFirst();
            sort.ifPresent(p -> qcConfig.setOrderNumValue(Integer.valueOf(p.getDictValue())));
        }
        data.sort(Comparator.comparing(DtoQualityControlLimit::getOrderNumValue));
        return data;
    }

    /**
     * 保存数据
     *
     * @param qcRangeCop 需要保存的数据
     * @param qcType     质控类型
     * @return 已保存的数据
     */
    @Override
    @Transactional
    public DtoQualityControlLimit save(DtoQualityControlLimit qcRangeCop, Integer qcType) {
        DtoQualityControlLimit save;
        //校验数值范围和允许限值的格式
        if (!validateRangeLimit(qcRangeCop)) {
            throw new BaseException("数值范围或允许限值格式有误，请按照规定的格式进行配置！");
        }
        Optional<AbsQCConfig> optionalAbsDataSync = absQCConfigList.parallelStream()
                .filter(p -> p.getQcType().equals(qcRangeCop.getQcType()) && p.getQcGrade().equals(qcRangeCop.getQcGrade()))
                .findFirst();
        if (optionalAbsDataSync.isPresent()) {
            save = optionalAbsDataSync.get().save(qcRangeCop);
        } else {
            throw new BaseException("质控类型不存在，保存失败");
        }
        return save;
    }

    /**
     * 获取评判方式
     *
     * @return 所有评判方式
     */
    @Override
    public Map<Integer, String> findAllJudgingMethod() {
        Map<Integer, String> map = new HashMap<>();
        map.put(EnumBase.EnumJudgingMethod.限值判定.getValue(), EnumBase.EnumJudgingMethod.限值判定.name());
        map.put(EnumBase.EnumJudgingMethod.小于检出限.getValue(), EnumBase.EnumJudgingMethod.小于检出限.name());
        map.put(EnumBase.EnumJudgingMethod.回收率.getValue(), EnumBase.EnumJudgingMethod.回收率.name());
        map.put(EnumBase.EnumJudgingMethod.相对偏差.getValue(), EnumBase.EnumJudgingMethod.相对偏差.name());
        map.put(EnumBase.EnumJudgingMethod.相对误差.getValue(), EnumBase.EnumJudgingMethod.相对误差.name());
        map.put(EnumBase.EnumJudgingMethod.绝对偏差.getValue(), EnumBase.EnumJudgingMethod.绝对偏差.name());
        map.put(EnumBase.EnumJudgingMethod.穿透率.getValue(), EnumBase.EnumJudgingMethod.穿透率.name());
        return map;
    }

    /**
     * 查询所有替代物
     *
     * @return 替代物
     */
    @Override
    public List<DtoSubstitute> findSubstitute() {
        return substituteService.findAll();
    }

    /**
     * 查询选择的测试项目配置了那些质控限值类型
     *
     * @param testId 测试项目Id
     * @return 查询结果
     */
    @Override
    public DtoQCTemp findQCTemp(String testId) {
        DtoQCTemp temp = new DtoQCTemp();
        DtoTest test = testService.findOne(testId);
        if (StringUtil.isNotNull(temp)) {
            temp.setTestId(testId);
            temp.setAnalyzeItmeName(test.getRedAnalyzeItemName());
            temp.setAnalyzeMethodName(test.getRedAnalyzeMethodName());
            List<DtoQualityControlLimit> qcs = repository.findByTestId(testId);
            List<DtoQcType> qcTypes = new ArrayList<>();
            if (StringUtil.isNotEmpty(qcs)) {
                qcs.forEach(p -> {
                    DtoQcType qcType = new DtoQcType();
                    qcType.setQcType(p.getQcType());
                    qcType.setQcGrade(p.getQcGrade());
                    qcTypes.add(qcType);
                });
            }
            temp.setQcTypes(qcTypes);
        }
        return temp;
    }

    /**
     * 复制质控限值
     *
     * @param tempTestId 被复制的测试项目id
     * @param testIds    需要复制的测试项目id数组
     * @param qcList     需要复制的质控类型
     */
    @Override
    @Transactional
    public void copy(String tempTestId, List<String> testIds, List<DtoQualityControlLimit> qcList) {
        List<DtoQualityControlLimit> list = repository.findByTestId(tempTestId);
        //最后复制的质控限值集合
        List<DtoQualityControlLimit> copyList = new ArrayList<>();
        //判断需要复制的质控类型
        List<Integer> qcTypeValues = qcList.stream().map(DtoQualityControlLimit::getQcType).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(qcList)) {
            //获取到需要复制的类型集合
            list = list.stream().filter(p -> qcTypeValues.contains(p.getQcType())).collect(Collectors.toList());
            for (DtoQualityControlLimit dtoQc : list) {
                if (EnumLIM.EnumQCType.平行.getValue().equals(dtoQc.getQcType()) || EnumLIM.EnumQCType.空白.getValue().equals(dtoQc.getQcType())) {
                    List<Integer> qcGradeValues = qcList.stream().map(DtoQualityControlLimit::getQcGrade).collect(Collectors.toList());
                    List<Integer> isExist = qcGradeValues.stream().filter(p -> dtoQc.getQcGrade().equals(p)).collect(Collectors.toList());
                    if (StringUtil.isEmpty(isExist)) {
                        continue;
                    }
                }
                copyList.add(dtoQc);
            }
        }
        if (StringUtil.isNotEmpty(copyList) && StringUtil.isNotEmpty(testIds)) {
            List<DtoQualityControlLimit> newItems = new ArrayList<>();
            for (String testId : testIds) {
                DtoTest test = testService.findOne(testId);
                if (!testId.equals(tempTestId) && StringUtil.isNotNull(test)) {
                    for (DtoQualityControlLimit item : copyList) {
                        DtoQualityControlLimit newItem = new DtoQualityControlLimit();
                        BeanUtils.copyProperties(item, newItem, "id");
                        newItem.setSubstituteId(UUIDHelper.GUID_EMPTY);
                        if (item.getQcType().toString().equals(EnumLIM.EnumQCType.替代物.getValue().toString())) {
                            if (!test.getIsTotalTest()) {
                                throw new BaseException("被复制的测试项目是否总称为否，替代物质控无法复制，请去除替代物选项重试！");
                            }
                            newItem.setSubstituteId(item.getSubstituteId());
                            newItem.setSubstituteName(item.getSubstituteName());
                        }
                        newItem.setTestId(testId);
                        newItems.add(newItem);
                    }
                }
            }
            if (newItems.size() > 0) {
                repository.save(newItems);
            }
        }
    }

    /**
     * 获取偏差公式配置质控类型下拉框数据源
     *
     * @return 下拉框数据源信息
     */
    @Override
    public List<DeviationFormulaSrcVO> getDeviationFormulaQcTypeDataSource() {
        QcDeviationFormulaConfig qcDeviationFormulaConfig = SpringContextAware.getBean(QcDeviationFormulaConfig.class);
        List<DeviationFormulaSrcVO> deviationFormulaSrcVOList = qcDeviationFormulaConfig.getDeviationFormulaSrcVOList();
        //过滤掉不用配置偏差公式的质控类型
        deviationFormulaSrcVOList = deviationFormulaSrcVOList.stream().filter(p -> "1".equals(p.getIsDeviationFormula())).collect(Collectors.toList());
        return deviationFormulaSrcVOList;
    }

    /**
     * 配置设置是否默认
     *
     * @param id 配置Id
     */
    @Override
    @Transactional
    public void updateDefaultValue(String id) {
        DtoQualityLimitDisposition disposition = qualityLimitDispositionRepository.findOne(id);
        //找到当前存在的默认配置
        List<DtoQualityLimitDisposition> dispositionList = qualityLimitDispositionRepository.findByQcGradeAndQcTypeAndJudgingMethodAndIsAcquiesce
                (disposition.getQcGrade(), disposition.getQcType(), disposition.getJudgingMethod(), Boolean.TRUE);
        //排除当前
        dispositionList = dispositionList.stream().filter(p -> !p.getId().equals(id)).collect(Collectors.toList());
        //找到存在的默认配置，并且排除个性化配置
        List<String> dispositionIds = dispositionList.stream().map(DtoQualityLimitDisposition::getId).collect(Collectors.toList());
        Set<Integer> qcGradeList = dispositionList.stream().map(DtoQualityLimitDisposition::getQcGrade).collect(Collectors.toSet());
        Set<Integer> qcTypeList = dispositionList.stream().map(DtoQualityLimitDisposition::getQcType).collect(Collectors.toSet());
        Set<Integer> methodList = dispositionList.stream().map(DtoQualityLimitDisposition::getJudgingMethod).collect(Collectors.toSet());
        if (!qcGradeList.contains(disposition.getQcGrade())) {
            qcGradeList.add(disposition.getQcGrade());
        }
        if (!qcTypeList.contains(disposition.getQcType())) {
            qcTypeList.add(disposition.getQcType());
        }
        if (!methodList.contains(disposition.getJudgingMethod())) {
            methodList.add(disposition.getJudgingMethod());
        }
        List<DtoQualityControlLimit> individuationList = repository.findByDispositionIdIn(dispositionIds);
        List<DtoQualityControlLimit> limitList = repository.findByQcGradeInAndQcTypeInAndJudgingMethodIn(qcGradeList, qcTypeList, methodList);
        List<DtoQualityControlLimit> defaultList = new ArrayList<>();
        List<DtoQualityControlLimit> finalLimitList = limitList;
        dispositionList.forEach(p -> {
            Set<String> testIds = individuationList.stream().filter(d -> d.getDispositionId().equals(p.getId()))
                    .map(DtoQualityControlLimit::getTestId).collect(Collectors.toSet());
            List<DtoQualityControlLimit> controlLimitList = finalLimitList.stream().filter(d -> p.getQcGrade().equals(d.getQcGrade())
                    && p.getQcType().equals(d.getQcType()) && p.getJudgingMethod().equals(d.getJudgingMethod())
                    && p.getFormula().equals(d.getFormula()) && !testIds.contains(d.getTestId())).collect(Collectors.toList());
            //修改默认公式
            controlLimitList.forEach(c -> {
                c.setDispositionId(UUIDHelper.GUID_EMPTY);
                c.setFormula(disposition.getFormula());
            });
            defaultList.addAll(controlLimitList);
            p.setIsAcquiesce(Boolean.FALSE);
        });
        //修改默认值
        disposition.setIsAcquiesce(Boolean.TRUE);
        dispositionList.add(disposition);
        if (dispositionList.size() > 0) {
            qualityLimitDispositionRepository.save(dispositionList);
        }
        if (defaultList.size() > 0) {
            repository.save(defaultList);
            List<String> limitIds = defaultList.stream().map(DtoQualityControlLimit::getId).collect(Collectors.toList());
            limitList = limitList.stream().filter(p -> !limitIds.contains(p.getId())).collect(Collectors.toList());
        }
        //存在空公式的需要调整成默认公式
        limitList = limitList.stream().filter(p -> !StringUtil.isNotEmpty(p.getFormula())).collect(Collectors.toList());
        if (limitList.size() > 0) {
            limitList.forEach(p -> {
                p.setDispositionId(UUIDHelper.GUID_EMPTY);
                p.setFormula(disposition.getFormula());
            });
            repository.save(limitList);
        }
    }

    /**
     * 检验质控限值的数值范围和允许限值格式是否正确
     *
     * @param limit 质控限值配置对象
     * @return 校验是否通过
     */
    private boolean validateRangeLimit(DtoQualityControlLimit limit) {
        boolean validate = true;
        if (StringUtil.isNotNull(limit)) {
            if (StringUtil.isNotEmpty(limit.getRangeConfig()) && !validateFormat(limit.getRangeConfig())) {
                if(EnumLIM.EnumQCType.标准.getValue().equals(limit.getQcType())){
                    if(!(Pattern.compile("\\[\\w+\\]").matcher(limit.getRangeConfig()).find())){
                        validate = false;
                    }
                }else{
                    if(!validateFormat(limit.getRangeConfig())){
                        validate = false;
                    }
                }
            }
            if (StringUtil.isNotEmpty(limit.getAllowLimit()) && !validateFormat(limit.getAllowLimit())) {
                validate = false;
            }
        }
        return validate;
    }

    /**
     * 判断给定字符串是否满足允许限值的配置格式(正确的格式示例："[x] > 5" , "[x] >= 6 and [x] <= 10")
     *
     * @param s 给定字符串
     * @return 校验是否通过
     */
    @Override
    public boolean validateFormat(String s) {
        s = s.replace(" ", "");
        if (StringUtil.isNotEmpty(s)) {
            if (s.contains("and")) {
                String[] arr = s.split("and");
                return arr.length == 2 && validateFormat(arr[0]) && validateFormat(arr[1]);
            } else {
                if (s.startsWith("[x]") && DivationUtils.cntSubStr(s, "[x]") == 1) {
                    s = s.replace("[x]", "");
                    if (s.startsWith("<=") || s.startsWith(">=")) {
                        s = s.substring(2);
                    } else if (s.startsWith("<") || s.startsWith(">")) {
                        s = s.substring(1);
                    }
                    if (s.contains("c") || s.contains("d")) {
                        return true;
                    } else {
                        return DivationUtils.isNumber(s);
                    }
                }
            }
        }
        return false;
    }

    @Override
    @Transactional
    public Integer delete(List<String> ids) {
        return repository.logicDeleteById(ids);
    }

    @Autowired
    @Lazy
    public void setAbsQCConfigList(List<AbsQCConfig> absQCConfigList) {
        this.absQCConfigList = absQCConfigList;
    }

    @Autowired
    @Lazy
    public void setSubstituteService(SubstituteService substituteService) {
        this.substituteService = substituteService;
    }

    @Autowired
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    public void setQualityLimitDispositionRepository(QualityLimitDispositionRepository qualityLimitDispositionRepository) {
        this.qualityLimitDispositionRepository = qualityLimitDispositionRepository;
    }
}
