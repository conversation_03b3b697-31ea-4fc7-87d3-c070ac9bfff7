package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.RecordConfig2ParamsConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoRecordConfig2ParamsConfig实体
 * <AUTHOR>
 * @version V1.0.0 2022/6/27
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_RecordConfig2ParamsConfig")
@Data
@DynamicInsert
public class DtoRecordConfig2ParamsConfig extends RecordConfig2ParamsConfig {

    /**
     * 参数名称
     */
    @Transient
    private String paramsName;

    /**
     * 别名
     */
    @Transient
    private String alias;

    /**
     * 参数类型
     */
    @Transient
    private Integer paramsType;

    /**
     * 参数类型名称
     */
    @Transient
    private String paramsTypeName;

    /**
     * 默认值
     */
    @Transient
    private String defaultValue;

    /**
     * 是否必填
     */
    @Transient
    private Boolean isRequired;

    /**
     * 排序值
     */
    @Transient
    private Integer orderNum;
}
