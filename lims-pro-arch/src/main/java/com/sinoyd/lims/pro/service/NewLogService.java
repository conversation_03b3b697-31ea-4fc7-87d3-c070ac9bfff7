package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.probase.service.NewLogBaseService;

import java.util.List;

public interface NewLogService extends NewLogBaseService {

    /**
     * 新增项目信息更新日志
     *
     * @param ids
     * @param opinion
     * @param type
     */
    void createProjectInfoUpdateLog(List<String> ids, String opinion, String type);

    /**
     * 新增项目流程状态更新日志
     *
     * @param ids
     * @param opinion
     * @param nextOperatorId
     * @param nextOperatorName
     */
    void createProjectStatusUpdateLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName);

    /**
     * 新增项目流程状态退回日志
     *
     * @param ids
     * @param opinion
     * @param nextOperatorId
     * @param nextOperatorName
     */
    void createProjectStatusBackLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName);

    /**
     * 新增项目流程状态退回日志
     *
     * @param projectId 对象ids
     * @param opinion   意见
     */
    void createProjectSchemeModifyLog(String projectId, String opinion);

    /**
     * 新增项目流程状态审核日志
     *
     * @param ids
     * @param opinion
     * @param nextOperatorId
     * @param nextOperatorName
     * @param signal
     */
    void createProjectStatusAuditLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName, String signal);

    /**
     * 创建项目办结的日志
     *
     * @param ids              主键ids
     * @param opinion          意见
     * @param nextOperatorId   下一步操作人
     * @param nextOperatorName 下一步操作人姓名
     */
    void createFinishProjectLogs(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName);

    /**
     * 新增编制报告转交日志
     *
     * @param projectId
     * @param oldId
     * @param newId
     */
    void createUpdateReportMakerLog(String projectId, String oldId, String newId,String opinion);

    /**
     * 报告信息更新日志
     *
     * @param ids     报告ids
     * @param opinion 意见
     * @param type    报告日志类型
     */
    void createReportInfoUpdateLog(List<String> ids, String opinion, String type);

    /**
     * 报告发放流程创建日志
     * @param ids 报告编号
     * @param type 流程类型
     */
    void createReportGrantInfoLog(List<String> ids,String type);

    /**
     * 动态查询流程日志，整合web的场景
     *
     * @param criteria 动态参数
     * @return 流程日志
     */
    List<DtoLog> findNewLog(BaseCriteria criteria);

    /**
     * 查询评论需冗余的日志
     *
     * @param objectId   关联id
     * @param objectType 关联类型
     * @return 评论日志
     */
    List<DtoLog> findCommentLog(String objectId, Integer objectType);

    /**
     * 获取项目日志并根据日期排序（所有日志）
     *
     * @param projectId
     * @return
     */
    List<DtoLog> getLogByProjectId(String projectId);

    /**
     * 获取项目日志并根据日期排序（仅流程日志）
     *
     * @param projectId
     * @return
     */
    List<DtoLog> getProjectLogByProjId(String projectId);

    /**
     * 根据项目id获取项目报告日志并根据日期排序
     *
     * @param projectId
     * @return
     */
    List<DtoLog> getReportLogByProjId(String projectId);

    /**
     * 根据报告id获取项目报告日志并根据日期排序
     *
     * @param reportId
     * @return
     */
    List<DtoLog> getReportLogByReportId(String reportId);

    /**
     * 获取检测单相关日志并根据日期排序
     *
     * @param workSheetFolderIds
     * @return
     */
    List<DtoLog> getLogByWorkSheetIds(List<String> workSheetFolderIds);

    /**
     * 获取现场领样单相关日志并根据日期排序
     *
     * @param objectId
     * @return
     */
    List<DtoLog> getLogByLocalSubReceiveId(String objectId);

    /**
     * 获取送样单相关日志并根据日期排序
     *
     * @param receiveId
     * @return
     */
    List<DtoLog> getLogByReceiveId(String receiveId);

    /**
     * 项目相关送样单流程日志
     *
     * @param projectId
     * @return
     */
    List<DtoLog> getReceiveLogByProjId(String projectId);

    /**
     * 获取项目方案日志并根据日期排序（方案修改日志）
     *
     * @param projectId
     * @return
     */
    List<DtoLog> getSchemeModifyLogByProjId(String projectId);

    /**
     * 获取项目相关样品及数据日志
     *
     * @param projectId
     * @return
     */
    List<DtoLog> getSampleLogByProjId(String projectId);

    /**
     * 新增日志
     *
     * @param objectId
     * @param comment
     * @param opinion
     * @param logType
     * @param objectType
     * @param operateInfo
     * @param operatorId
     * @param operatorName
     * @param nextOperatorId
     * @param nextOperatorName
     */
    void createLog(String objectId, String comment, String opinion, int logType, int objectType, String operateInfo, String operatorId, String operatorName, String nextOperatorId, String nextOperatorName);

    /**
     * 新增日志
     *
     * @param dtoLog
     */
    void createLog(DtoLog dtoLog);

    /**
     * 费用管理流程日志
     *
     * @param ids              对象ids
     * @param opinion          意见
     * @param nextOperatorId   下一步操作人
     * @param nextOperatorName 下一步操作人姓名
     */
    void createCostStatusUpdateLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName);

    /**
     * 费用管理流程退回日志
     *
     * @param ids              对象ids
     * @param opinion          意见
     * @param nextOperatorId   下一步操作人
     * @param nextOperatorName 下一步操作人姓名
     */
    void createCostStatusBackLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName);

    /**
     * 处理日志信息
     *
     * @param logs     传入过来的日志
     * @param userList 用户信息
     * @return 返回新的日志
     */
    List<DtoLog> dealLogs(List<DtoLog> logs, List<DtoUser> userList);

    /**
     * 样品作废日志
     *
     * @param ids     作废的样品
     * @param opinion 作废原因
     * @param actionStr 作废动作（取消作废，作废样品）
     */
    void createSampleInvalidLog(List<String> ids, String opinion, String actionStr);

    void createLog(DtoLog dtoLog,Boolean insertLogFlag);

    void createLog(List<DtoLog> logs, Integer logType,Boolean insertLogFlag);

    /**
     * 通过订单ids获取订单操作日志
     * @param orderIds 订单id
     * @return 操作日志
     */
    List<DtoLog> getLogByOrderIds(List<String> orderIds);
}
