package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * OAProjectExpend查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OAProjectExpendCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 类别
     */
    private String typeId;

    /**
     * 说明
     */
    private String title;

    /**
     * 是否显示最终确认的数据
     */
    private Boolean isConfirm;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id = tr.objectId");
        condition.append(" and tr.taskId = t.id");

        if (StringUtils.isNotNullAndEmpty(this.projectId) && !UUIDHelper.GUID_EMPTY.equals(this.projectId)) {
            condition.append(" and a.projectId = :projectId");
            values.put("projectId", this.projectId);
        }
        if (StringUtils.isNotNullAndEmpty(this.typeId) && !UUIDHelper.GUID_EMPTY.equals(this.typeId)) {
            condition.append(" and a.typeId = :typeId");
            values.put("typeId", this.typeId);
        }
        if (StringUtils.isNotNullAndEmpty(this.title)) {
            condition.append(" and t.title like :title");
            values.put("title", "%" + this.title + "%");
        }

        if (StringUtil.isNotNull(isConfirm)) {
            condition.append(" and a.isConfirm = :isConfirm");
            values.put("isConfirm", this.isConfirm);
        }
        return condition.toString();
    }
}
