alter table TB_PRO_AnalyseData add column lowerLimit varchar(50);
comment on column TB_PRO_AnalyseData.lowerLimit is '测定下限';

-- 更新测定下限  先匹配个性化的，没有再匹配默认的
UPDATE TB_PRO_AnalyseData JOIN TB_LIM_TestExpand
ON TB_PRO_AnalyseData.testId = TB_LIM_TestExpand.testId and TB_PRO_AnalyseData.sampleTypeId = TB_LIM_TestExpand.sampleTypeId
    SET TB_PRO_AnalyseData.lowerLimit = TB_LIM_TestExpand.lowerLimit;

UPDATE TB_PRO_AnalyseData JOIN tb_lim_test ON TB_PRO_AnalyseData.testId = tb_lim_test.id
    SET TB_PRO_AnalyseData.lowerLimit = tb_lim_test.lowerLimit where TB_PRO_AnalyseData.lowerLimit is null;