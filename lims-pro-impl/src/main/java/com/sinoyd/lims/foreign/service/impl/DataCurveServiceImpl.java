package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataCurveVO;
import com.sinoyd.lims.foreign.service.DataCurveService;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 曲线信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataCurveServiceImpl extends AbsQueryDataServiceImpl<DataCurveVO> implements DataCurveService {


    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t1.worksheetId, t1.checkDate,t3.parentId, t2.testId,t2.coefficient,t2.configDate," +
                " t2.zeroPoint,t2.kValue,t2.bValue,t2.cValue,t2.isDouble,t2.curveType,t2.curveMode ,t2.curveInfo,t2.orgId,t2.domainId");
        sql.append(" from TB_PRO_WorkSheetCalibrationCurve t1 INNER JOIN TB_LIM_Curve t2 on t1.standardCurveId = t2.id ");
        sql.append(" INNER JOIN TB_PRO_WorkSheet t3 on t1.worksheetId = t3.id");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND (t1.modifyDate > :syncTime or t2.modifyDate > :syncTime) ", "syncTime");
        super.appendCondition(sql, map, "AND t1.workSheetId in (:workSheetIds)", "workSheetIds");
        super.appendCondition(sql, map, "AND t3.parentId in (:workSheetFolderIds)", "workSheetFolderIds");
    }

    @Override
    protected RowMapper<DataCurveVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataCurveVO dataCurveVO = new DataCurveVO();
            dataCurveVO.setId(UUIDHelper.NewID());
            dataCurveVO.setWorkSheetFolderId(rs.getString("parentId"));
            dataCurveVO.setTestId(rs.getString("testId"));
            dataCurveVO.setCheckDate(rs.getDate("checkDate"));
            dataCurveVO.setCoefficient(rs.getString("coefficient"));
            dataCurveVO.setConfigDate(rs.getDate("configDate"));
            dataCurveVO.setZeroPoint(rs.getString("zeroPoint"));
            dataCurveVO.setKValue(rs.getString("kValue"));
            dataCurveVO.setBValue(rs.getString("bValue"));
            dataCurveVO.setCValue(rs.getString("cValue"));
            dataCurveVO.setIsDouble(rs.getBoolean("isDouble"));
            dataCurveVO.setCurveType(rs.getString("curveType"));
            dataCurveVO.setCurveMode(rs.getString("curveMode"));
            dataCurveVO.setCurveInfo(rs.getString("curveInfo"));
            dataCurveVO.setOrgId(rs.getString("orgId"));
            dataCurveVO.setDomainId(rs.getString("domainId"));
            dataCurveVO.setSyncTime(new Date());
            return dataCurveVO;
        };
    }

}
