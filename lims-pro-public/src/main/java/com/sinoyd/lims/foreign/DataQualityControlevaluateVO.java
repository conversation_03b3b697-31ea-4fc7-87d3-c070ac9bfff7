package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
* 质控评价信息
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataQualityControlevaluateVO {

    /**
     * id
     */
    private String id;

    /**
     * 关联id
     */
    private String objectId;

    /**
     * 质控id
     */
    private String qcId;

    /**
     * 检查项
     */
    private String checkItem;

    /**
     * 评判方式
     */
    private Integer judgingMethod;

    /**
     * 是否合格
     */
    private Boolean isPass;

    /**
     * 检查项值
     */
    private String checkItemValue;

    /**
     * 允许限值
     */
    private String allowLimit;

    /**
     * 量纲
     */
    private String dimensionName;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;

}
