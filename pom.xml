<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
    <groupId>com.sinoyd.lims.probase</groupId>
	<artifactId>lims-probase</artifactId>
	<version>fix-5.4.13-SNAPSHOT</version>
	<name>lims-probase</name>
    <description>LIMS ProBase模块</description>
    <packaging>pom</packaging>

    <build>
        <finalName>lims-probase</finalName>
    </build>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.15.RELEASE</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>

        <probase.version>fix-5.4.13</probase.version>
        <monitor.version>fix-5.4.13</monitor.version>
    </properties>

    <modules>
        <module>lims-probase-arch</module>
        <module>lims-probase-impl</module>
        <module>lims-pro-public</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sinoyd.lims.probase</groupId>
                <artifactId>lims-pro-public</artifactId>
                <version>${probase.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.probase</groupId>
                <artifactId>lims-probase-arch</artifactId>
                <version>${probase.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.probase</groupId>
                <artifactId>lims-probase-impl</artifactId>
                <version>${probase.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.monitor</groupId>
                <artifactId>lims-monitor-impl</artifactId>
                <version>${monitor.version}-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id><!--这里需要和settings.xml中配置的私有库名称一致-->
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>
</project>
