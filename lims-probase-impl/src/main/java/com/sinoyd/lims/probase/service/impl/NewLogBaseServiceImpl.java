package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.probase.service.NewLogBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class NewLogBaseServiceImpl implements NewLogBaseService {

//    @Autowired
//    private LogForProjectBaseRepository logForProjectBaseRepository;

//    @Autowired
//    private LogForDataBaseRepository logForDataBaseRepository;
//
//    @Autowired
//    private LogForPlanBaseRepository logForPlanBaseRepository;
//
//    @Autowired
//    private LogForRecordBaseRepository logForRecordBaseRepository;
//
//    @Autowired
//    private LogForReportBaseRepository logForReportBaseRepository;
//
//    @Autowired
//    private LogForSampleBaseRepository logForSampleBaseRepository;
//
//    @Autowired
//    private LogForWorkSheetBaseRepository logForWorkSheetBaseRepository;
//
    @Autowired
    private CommonRepository commonRepository;

    @Override
    public void createLog(DtoLog dtoLog) {
        List<DtoLog> logs = new ArrayList<>();
        logs.add(dtoLog);
        createLog(logs, dtoLog.getLogType());
    }

    @Override
    public void  createLog(List<DtoLog> logs,Integer logType) {
        for (DtoLog log : logs) {
            log.setOperateTime(new Date());
        }
        EnumPRO.EnumLogType code = EnumPRO.EnumLogType.getByValue(logType);
        switch (code) {
            //#region 项目
            case 项目信息:
            case 项目分包:
            case 项目流程:
            case 项目方案:
            case 项目合同:
            case 项目报告转交:
            case 项目送样单:
            case 项目检测单:
                List<DtoLogForProject> logP = logs.stream().map(DtoLogForProject::new).collect(Collectors.toList());
                if (logP.size() > 0) {
                    commonRepository.insert(logP);
                }
//                logForProjectBaseRepository.save(logP);
                break;
            //#endregion

            //#region 方案
            case 新增方案:
            case 复制方案:
            case 清空方案:
            case 方案点位信息:
            case 方案点位数据信息:
            case 方案检测方法信息:
                List<DtoLogForPlan> dtoLogForPlans = logs.stream().map(DtoLogForPlan::new).collect(Collectors.toList());
                if (dtoLogForPlans.size() > 0) {
                    commonRepository.insert(dtoLogForPlans);
                }
//                logForPlanBaseRepository.save(dtoLogForPlans);
                break;
            //#endregion

            //#region 检测单
            case 检测单更新参数:
            case 检测单基本信息:
            case 检测单配置:
            case 检测单使用记录:
            case 检测单试剂配置:
            case 检测单数据保存:
            case 检测单同步仪器:
            case 检测单原始记录:
            case 检测单增删:
            case 检测单增删样品:
            case 检测单流程:
                List<DtoLogForWorkSheet> logW = logs.stream().map(DtoLogForWorkSheet::new).collect(Collectors.toList());
                if (logW.size() > 0) {
                    commonRepository.insert(logW);
                }
//                logForWorkSheetBaseRepository.save(logW);
                break;
            //#endregion

            //#region 送样单
            case 实验室领样单流程:
            case 实验室领样单信息:
            case 实验室领样单分配:
            case 送样单采样使用记录:
            case 送样单流程:
            case 送样单信息:
            case 送样单样品信息:
            case 现场领样单流程:
            case 现场领样单使用记录:
            case 现场领样单信息:
            case 现场领样单数据:
                List<DtoLogForRecord> logR = logs.stream().map(DtoLogForRecord::new).collect(Collectors.toList());
                if (logR.size() > 0) {
                    commonRepository.insert(logR);
                }
//                logForRecordBaseRepository.save(logR);
                break;
            //#endregion

            //#region 样品
            case 样品增删作废:
            case 样品信息:
            case 样品数据:
            case 样品流程:
            case 样品质控:
                List<DtoLogForSample> logS = logs.stream().map(DtoLogForSample::new).collect(Collectors.toList());
                if (logS.size() > 0) {
                    commonRepository.insert(logS);
                }
//                logForSampleBaseRepository.save(logS);
                break;
            //#endregion

            //#region 数据
            case 数据保存:
            case 数据分包:
            case 数据检测单:
            case 数据配置:
            case 数据退回:
            case 数据增删:
            case 数据审核:
                List<DtoLogForData> logD = logs.stream().map(DtoLogForData::new).collect(Collectors.toList());
                if (logD.size() > 0) {
                    commonRepository.insert(logD);
                }
//                logForDataBaseRepository.save(logD);
                break;
            //#endregion

            //#region 报告
            case 项目报告:
                List<DtoLogForReport> logRe = logs.stream().map(DtoLogForReport::new).collect(Collectors.toList());
                if (logRe.size() > 0) {
                    commonRepository.insert(logRe);
                }
//                logForReportBaseRepository.save(logRe);
                break;
            //#endregion

            //#region 费用流程
            case 费用流程:
            case 订单流程:
                List<DtoLogForCost> logCost = logs.stream().map(DtoLogForCost::new).collect(Collectors.toList());
                if (logCost.size() > 0) {
                    commonRepository.insert(logCost);
                }
                break;
            //#endregion
        }
    }

    /**
     * 新增日志
     *
     * @param objectId         对象id
     * @param comment          说明
     * @param opinion          意见（评审意见等）
     * @param logType          日志类型（如项目的方案、合同，样品的信息、检测项目）
     * @param objectType       对象类型（检测单、项目、数据等）
     * @param operateInfo      操作类型（新建、保存、修改等）
     * @param operatorId       操作人Id
     * @param operatorName     操作人
     * @param nextOperatorId   下一步操作人Id
     * @param nextOperatorName 下一步操作人
     */
    @Override
    public void createLog(String objectId, String comment, String opinion, int logType, int objectType, String operateInfo, String operatorId, String operatorName, String nextOperatorId, String nextOperatorName) {

        DtoLog log = new DtoLog();
        String operId = UUIDHelper.GUID_EMPTY;
        String operName = StringUtils.isNotNullAndEmpty(operatorName) ? operatorName : "";
        try {
            if (StringUtils.isNotNullAndEmpty(operatorId) && !operatorId.equals(UUIDHelper.GUID_EMPTY)) {
                operId = operatorId;
            } else {
                operId = PrincipalContextUser.getPrincipal().getUserId();
                operName = PrincipalContextUser.getPrincipal().getUserName();
            }
        } catch (Exception ex) {
        }
        log.setOperatorId(operId);
        log.setOperatorName(operName);
        log.setOperateTime(new Date());
        log.setOperateInfo(operateInfo);
        log.setNextOperatorId(StringUtils.isNotNullAndEmpty(nextOperatorId) ? nextOperatorId : UUIDHelper.GUID_EMPTY);
        log.setNextOperatorName(StringUtils.isNotNullAndEmpty(nextOperatorName) ? nextOperatorName : "");
        log.setLogType(logType);
        log.setObjectId(objectId);
        log.setObjectType(objectType);
        log.setComment(comment);
        log.setOpinion(opinion);
        log.setRemark("");
        createLog(log);
    }

}
