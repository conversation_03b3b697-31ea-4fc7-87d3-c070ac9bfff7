package com.sinoyd.lims.pro.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.dto.lims.DtoOAFileAbolish;
import com.sinoyd.lims.lim.enums.EnumLIM.EnumFileControlStatus;
import com.sinoyd.lims.lim.service.FileControlApplyDetailService;
import com.sinoyd.lims.lim.service.OAFileAbolishService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.service.OAProcFileAbolishService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 文件废止业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-08
 * @since V100R001
 */
@Service
public class OAProcFileAbolishServiceImpl implements OAProcFileAbolishService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    /**
     * 文件废止服务
     */
    @Autowired
    @Lazy
    private OAFileAbolishService oaFileAbolishService;

    /**
     * 文件控制管理明细服务
     */
    @Autowired
    @Lazy
    private FileControlApplyDetailService fileControlApplyDetailService;

    @Override
    public String startProcess(DtoOATaskCreate<DtoOAFileAbolish> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.文件废止, taskDto, vars);

        DtoOAFileAbolish data = taskDto.getData();

        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(data.getId());

        // 添加废止记录
        oaFileAbolishService.save(data);

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelation);
        
        // 更新文件状态
        fileControlApplyDetailService.updateFileStatus(data.getFileId(), EnumFileControlStatus.废止中);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<DtoOAFileAbolish, DtoFileControlApplyDetail> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<DtoOAFileAbolish, DtoFileControlApplyDetail> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        // 查找关系
        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(taskId);

        DtoOAFileAbolish detail = oaFileAbolishService.findOne(relation.getObjectId());
        // 设置详细信息
        taskDetail.setDetail(detail);

        // 设置扩展信息
        taskDetail.setExtend(fileControlApplyDetailService.findOne(detail.getFileId()));
        
        return taskDetail;
    }

    @Override
    @Transactional
    public void normalCloseTaskNotify(DtoOATask oaTask) {
        // 查找关系
        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(oaTask.getId());
        DtoOAFileAbolish detail = oaFileAbolishService.findOne(relation.getObjectId());
        String fileId = detail.getFileId();

        if (StringUtil.isNotEmpty(fileId) && !UUIDHelper.GUID_EMPTY.equals(fileId)) {
            // 更新文件状态
            fileControlApplyDetailService.updateAbolishFileStatus(fileId, EnumFileControlStatus.已废止,detail.getAbolishDate());
            // 需更新主表的废止信息
        }
    }

    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<DtoOAFileAbolish> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.文件废止, taskDto, vars);
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<DtoOAFileAbolish> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<DtoOAFileAbolish> taskDto,DtoOATask oaTask){
        DtoOAFileAbolish data = taskDto.getData();
        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(data.getId());
        // 添加废止记录
        oaFileAbolishService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelation);
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<DtoOAFileAbolish> taskDto) {
        // 更新文件状态
        DtoOAFileAbolish data = taskDto.getData();
        fileControlApplyDetailService.updateFileStatus(data.getFileId(), EnumFileControlStatus.废止中);
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }
}