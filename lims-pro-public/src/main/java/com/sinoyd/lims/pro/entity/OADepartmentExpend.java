package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import java.math.BigDecimal;


/**
 * OADepartmentExpend实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OADepartmentExpend")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class OADepartmentExpend implements BaseEntity,Serializable {
 
    public  OADepartmentExpend() {
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 部门ID
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("部门ID")
	private String deptId;
    
    /**
    * 种类(常量编码: OA_ExpenditureType )
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("种类(常量编码: OA_ExpenditureType )")
	private String typeId;
    
    /**
    * 金额
    */
    @Column(nullable=false)
    @ApiModelProperty("金额")
	private BigDecimal amount;
    
    /**
    * 支出日期
    */
    @Column(nullable=false)
    @ApiModelProperty("支出日期")
	private Date expendDate;
    
    /**
    * 说明
    */
    @ApiModelProperty("说明")
	private String description;

    /**
    * 是否最终确认
    */
    @Column(nullable=false)
    @ApiModelProperty("是否最终确认")
    private Boolean isConfirm=false;

    /**
    * 假删
    */
    @Column(nullable=false)
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }