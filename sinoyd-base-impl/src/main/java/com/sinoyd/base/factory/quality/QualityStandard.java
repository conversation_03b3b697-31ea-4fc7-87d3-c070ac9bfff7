package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;

import java.util.List;
import java.util.Map;

public class QualityStandard extends QualityControlKind {

    /**
     * 标样类别
     *
     * @return 标样类别
     */
    @Override
    public Integer qcTypeValue() {
        return 8;
    }

    /**
     * 返回对应的质控名称
     *
     * @return 质控名称
     */
    @Override
    public String qcTypeName() {
        return "标样";
    }

    /**
     * 样品类别
     *
     * @param qcGrade 质控类型
     * @return 质控类别
     */
    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "标样";
    }

    /**
     * 记录
     *
     * @param map 参数
     * @return 记录内容
     */
    @Override
    public String getFormt(Map<String, String> map) {
        String uncertainType = EnumBase.EnumUncertainType.getNameByValue(Integer.valueOf(map.getOrDefault("uncertainType","10")));
        return String.format("增加了标准样，标样编号为：%s，标值为：%s%s。", map.get("qcCode"), map.get("qcValue"),uncertainType);
    }

    /**
     * 样品名称
     *
     * @param folderName 原样名称
     * @param qcGrade    质控类型
     * @return 样品名称
     */
    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        return "质控样";
    }

    @Override
    public DtoQualityConfig getQualityConfig() {
        return getConfig("QualityStandard");
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return getConfig("QualityStandard", orderReviseVOList);
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}
