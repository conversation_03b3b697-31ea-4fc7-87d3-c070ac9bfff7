package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.od.dto.DtoOdSample;

import java.util.List;

/**
 * OdSampleRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface OdSampleRepository extends IBaseJpaRepository<DtoOdSample, String> {


    /**
     * 根据任务ids查询样品
     *
     * @param taskIds 任务ids
     * @return 样品集合
     */
    List<DtoOdSample> findByTaskIdIn(List<String> taskIds);

    /**
     * 根据任务id查询样品
     *
     * @param taskId 任务id
     * @return 样品集合
     */
    List<DtoOdSample> findByTaskId(String taskId);

    /**
     * 根据样品编号集合查询
     *
     * @param sampleCodes 样品编号集合
     * @return 嗅辨样品集合
     */
    List<DtoOdSample> findBySampleCodeIn(List<String> sampleCodes);
}
