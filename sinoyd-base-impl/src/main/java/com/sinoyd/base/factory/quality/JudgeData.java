package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.frame.service.CalculationService;

import java.util.List;
import java.util.Map;

/**
 * 比对数据
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/14
 */
public class JudgeData extends QualityControlKind {

    @Override
    public Integer qcTypeValue() {
        return null;
    }

    @Override
    public String qcTypeName() {
        return "比对";
    }

    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "比对";
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }

    @Override
    public String getFormt(Map<String, String> map) {
        return null;
    }

    @Override
    public DtoQualityConfig getQualityConfig() {
        return null;
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return null;
    }

    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        return null;
    }

    @Override
    protected void getCalculateData(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        if ("gasJudgeData".equals(controlLimit.getJudgeDataType())) {
            try {
                valueList.clear();
                //在线均值
                String zxAvg = MathUtil.calculateAvg(zxList);
                //实验室均值
                String syAvg = MathUtil.calculateAvg(syList);
                if (MathUtil.isNumber(zxAvg) && MathUtil.isNumber(syAvg)) {
                    CalculateService calculateService = SpringContextAware.getBean(CalculateService.class);
                    if (EnumBase.EnumReviseType.先比较再修约.getValue().equals(reviseType)) {
                        syAvg = halfLimit(syAvg, examLimitValue, configValue);
                        zxAvg = halfLimit(zxAvg, examLimitValue, configValue);
                    }
                    syAvg = calculateService.revise(sign, md, syAvg);
                    zxAvg = calculateService.revise(sign, md, zxAvg);
                    valueList.add(syAvg);
                    valueList.add(zxAvg);
                    valueList.add(syAvg);
                }
                if (EnumBase.EnumJudgingMethod.相对准确度.getValue().equals(controlLimit.getJudgingMethod())) {
                    //计算相对准确度，数据需要做一次处理
                    map.put(IBaseConstants.ONLINE_DATA_LIST, zxList);
                    map.put(IBaseConstants.LABORATORY_DATA_LIST, syList);
                }
            } catch (Exception ex) {

            }
        }
    }
}
