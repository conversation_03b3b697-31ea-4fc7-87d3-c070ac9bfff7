package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import com.sinoyd.lims.lim.repository.rcc.SerialNumberConfigRepository;
import com.sinoyd.lims.lim.service.SerialNumberConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

import static jdk.nashorn.internal.runtime.regexp.joni.Config.log;

/**
 * SerialNumberConfig操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@Service
@Slf4j
public class SerialNumberConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSerialNumberConfig, String, SerialNumberConfigRepository> implements SerialNumberConfigService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void findByPage(PageBean<DtoSerialNumberConfig> pb, BaseCriteria serialNumberConfigCriteria) {
        pb.setEntityName("DtoSerialNumberConfig a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, serialNumberConfigCriteria);
    }

    @Override
    public DtoSerialNumberConfig findBySerialNumberType(String serialNumberType) {
        Object object = redisTemplate.opsForValue().get(serialNumberType + PrincipalContextUser.getPrincipal().getOrgId());
        if (StringUtils.isNotNullAndEmpty(object)) {
//            TypeLiteral<DtoSerialNumberConfig> typeLiteral = new TypeLiteral<DtoSerialNumberConfig>() {
//            };
            log.error(object.toString());
            return JsonIterator.deserialize(object.toString(), DtoSerialNumberConfig.class);
        }
        return repository.findBySerialNumberType(serialNumberType);
    }

    @Override
    public List<DtoSerialNumberConfig> findBySerialNumberType(Collection<String> serialNumberTypeList) {
        return repository.findBySerialNumberTypeIn(serialNumberTypeList);
    }

    @Override
    public DtoSerialNumberConfig findBySerialNumberTypeAndPara2(String serialNumberType, String para2) {
        Object object = redisTemplate.opsForValue().get(serialNumberType + PrincipalContextUser.getPrincipal().getOrgId() + para2);
        if (StringUtils.isNotNullAndEmpty(object)) {
            TypeLiteral<DtoSerialNumberConfig> typeLiteral = new TypeLiteral<DtoSerialNumberConfig>() {
            };
            return JsonIterator.deserialize(object.toString(), typeLiteral);
        }
        return repository.findBySerialNumberTypeAndPara2(serialNumberType, para2);
    }

    @Transactional
    @Override
    public DtoSerialNumberConfig save(DtoSerialNumberConfig entity) {
        return super.save(entity);
    }
}