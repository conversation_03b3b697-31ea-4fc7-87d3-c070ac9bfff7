package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SamplingFrequencyTestTemp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoProjectApproval实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SamplingFrequencyTestTemp")
@Data
@DynamicInsert
public class DtoSamplingFrequencyTestTemp extends SamplingFrequencyTestTemp {

    @Transient
    private String samplingFrequencyId;
}
