package com.sinoyd.base.criteria;

import com.sinoyd.boot.workflow.activiti.dto.ActProcessDTO;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * WorkflowCriteria 工作流的查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/11/01
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowCriteria extends BaseCriteria {

    /**
     * 工作流名称
     */
    private String workflowName;

    /**
     * 分类
     */
    private String category;

    /**
     * 工作流编号
     */
    private String workflowCode;

    /**
     * 得到查询条件对象
     *
     * @return 返回想要的工作流数据
     */
    public ActProcessDTO getActProcessDTO() {
        ActProcessDTO actProcessDTO = new ActProcessDTO();
        if (StringUtils.isNotNullAndEmpty(this.workflowName)) {
            actProcessDTO.setDefName(workflowName);
        }
        if (StringUtils.isNotNullAndEmpty(this.workflowCode)) {
            actProcessDTO.setDefKey(workflowCode);
        }
        if (StringUtils.isNotNullAndEmpty(this.category)) {
            actProcessDTO.setDefCategory(category);
        }
        return actProcessDTO;
    }

    @Override
    public String getCondition() {
        return null;
    }
}
