package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class DtoImportConsumableDetail {

    /**
     * 单价
     */
    @Excel(name = "单价",orderNum = "90",width = 15)
    private String unitPrice;

    /**
     * 定制日期
     */
    @Excel(name = "定值日期",format = "yyyy-MM-dd",orderNum = "100",width = 23)
    private String orderTime;

    /**
     * 入库时间
     */
    @Excel(name = "入库日期(必填)",format = "yyyy-MM-dd",orderNum = "110",width = 23)
    private String storageDate;

    /**
     * 标洋有效日期
     */
    @Excel(name = "有效日期(必填)",format = "yyyy-MM-dd",orderNum = "120",width = 22)
    private String expiryDate;

    /**
     * 消耗品有效日期
     */
    @Excel(name = "有效日期",format = "yyyy-MM-dd",orderNum = "120",width = 22)
    private String expiryDate2;


    /**
     * 存放位置
     */
    @Excel(name = "存放位置",orderNum = "130",width = 22)
    private String keepPlace;

    /**
     * 备注
     */
    @Excel(name = "备注",orderNum = "140",width = 20)
    private String remark;

    /**
     * 生产批号
     */
    @Excel(name = "批号",orderNum = "150",width = 22)
    private String productionCode;

    /**
     * 生产厂商
     */
    @Excel(name = "生产厂商",orderNum = "160",width = 22)
    private String manufacturerName;

    @Excel(name = "供应厂商",orderNum = "170",width = 22)
    private String supplierName;
}
