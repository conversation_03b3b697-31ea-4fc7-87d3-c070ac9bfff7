package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoSamplingPersonConfig;
import com.sinoyd.lims.pro.repository.SamplingPersonConfigRepository;
import com.sinoyd.lims.pro.service.SamplingPersonConfigService;
import org.springframework.stereotype.Service;


/**
 * SamplingPersonConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class SamplingPersonConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSamplingPersonConfig,String,SamplingPersonConfigRepository> implements SamplingPersonConfigService {

    @Override
    public void findByPage(PageBean<DtoSamplingPersonConfig> pb, BaseCriteria samplingPersonConfigCriteria) {
        pb.setEntityName("DtoSamplingPersonConfig a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, samplingPersonConfigCriteria);
    }
}