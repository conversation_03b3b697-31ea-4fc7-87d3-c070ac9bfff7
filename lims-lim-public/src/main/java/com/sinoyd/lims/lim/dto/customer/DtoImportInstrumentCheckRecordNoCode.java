package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 仪器检定校准导入实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/13
 * @since V100R001
 */
@Data
public class DtoImportInstrumentCheckRecordNoCode implements IExcelModel, IExcelDataModel {

    /**
     * 行数
     */
    private Integer rowNum;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 仪器名称
     */
    private String instrumentCode;

    /**
     * 仪器id
     */
    private String instrumentId;

    /**
     * 溯源方式
     */
    @Excel(name = "溯源方式(检定、校准、自校)【必填】",orderNum = "200",width = 35)
    private String originTypeName;

    private Integer originType;


    /**
     * 溯源周期
     */
    @Excel(name = "溯源周期(月)【必填】",orderNum = "300",width = 23)
    private BigDecimal originCyc;

    /**
     * 检定/校准单位名称
     */
    @Excel(name = "检定/校准单位名称【必填】",orderNum = "400",width = 26)
    private String checkDeptName;

    /**
     * 检定/校准人
     */
    @Excel(name = "检定/校准人",orderNum = "500",width = 20)
    private String checkPerson;

    /**
     * 检定/校准人Id
     */
    private String checkPersonId;

    /**
     * 证书(记录)编号
     */
    @Excel(name = "证书(记录)编号",orderNum = "600",width = 22)
    private String certiCode;

    /**
     * 检定/校准日期
     */
    @Excel(name = "检定/校准日期【必填】",format = "yyyy-MM-dd",orderNum = "700",width = 22)
    private String checkTime;

    /**
     * 检定/校准有效期
     */
    @Excel(name = "检定/校准有效期【必填】",format = "yyyy-MM-dd",orderNum = "800",width = 25)
    private String checkEndDate;

    /**
     * 适用性（是，否）
     */
    @Excel(name = "确认结果(是，否)【必填】",orderNum = "900",width = 27)
    private String usabilityStr;

    private Boolean usability;



    /**
     * 检定结果（1.合格，0.不合格）
     */
    @Excel(name = "检定结果（合格，不合格）【必填】",orderNum = "1000",width = 33)
    private String checkResultStr;

    private Integer checkResult;

    /**
     * 备注
     */
    @Excel(name = "备注",orderNum = "1100",width = 22)
    private String remark;

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }
}
