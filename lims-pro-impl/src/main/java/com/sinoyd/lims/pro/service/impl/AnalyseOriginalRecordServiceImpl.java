package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoAnalyseOriginalRecord;
import com.sinoyd.lims.pro.repository.AnalyseOriginalRecordRepository;
import com.sinoyd.lims.pro.service.AnalyseOriginalRecordService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * AnalyseOriginalRecord操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class AnalyseOriginalRecordServiceImpl extends BaseJpaServiceImpl<DtoAnalyseOriginalRecord,String,AnalyseOriginalRecordRepository> implements AnalyseOriginalRecordService {

    @Override
    public void findByPage(PageBean<DtoAnalyseOriginalRecord> pb, BaseCriteria analyseOriginalRecordCriteria) {
        pb.setEntityName("DtoAnalyseOriginalRecord a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, analyseOriginalRecordCriteria);
    }

    @Override
    public List<DtoAnalyseOriginalRecord> findByDataIds(List<DtoAnalyseData> analyseDataList) {
        Set<String> anaIds = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toSet());
        List<DtoAnalyseOriginalRecord> recordList = repository.findByAnalyseDataIdIn(anaIds);
        recordList.forEach(r -> {
            Optional<DtoAnalyseData> dataOptional = analyseDataList.stream()
                    .filter(p -> r.getAnalyseDataId().equals(p.getId())).findFirst();
            dataOptional.ifPresent(p -> {
                r.setSampleId(p.getSampleId());
                r.setItemName(p.getRedAnalyzeItemName());
            });
        });
        return recordList;
    }
}