package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoReportNumberPool;

import java.util.List;

/**
 * ReportNumberPool数据访问接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/8/1
 * @since V100R001
 */
public interface ReportNumberPoolRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportNumberPool, String> {
    /**
     * 根据报告编码查询
     *
     * @param reportCode 报告编码
     * @return 数量
     */
    Integer countByCode(String reportCode);

    /**
     * 根据年份查询报告编号池
     *
     * @param year 年份
     * @return 报告编号
     */
    List<DtoReportNumberPool> findByYear(Integer year);

    /**
     * 根据报告编号年份报告类型查询报告编号池
     *
     * @param code         报告编号
     * @param year         年份
     * @param reportTypeId 报告类型
     * @return 报告编号
     */
    List<DtoReportNumberPool> findByCodeAndYearAndReportTypeId(String code, Integer year, String reportTypeId);

    /**
     * 根据报告编号查询报告编号池
     *
     * @param codeList 报告编号
     * @return 报告编号池
     */
    List<DtoReportNumberPool> findByCodeIn(List<String> codeList);

    /**
     * 根据报告类型年份查询
     *
     * @param reportTypeId 报告类型
     * @param year         年份
     * @param serialType   流水号类型
     * @return 报告编号池
     */
    List<DtoReportNumberPool> findByReportTypeIdAndYearAndSerialType(String reportTypeId, Integer year, String serialType);
}
