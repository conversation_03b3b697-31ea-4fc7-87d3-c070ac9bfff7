package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoAnalyseData;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 分析数据多线程操作接口
 *
 * @version V1.0.0 2024/4/28
 * @author: hukq
 * @since V100R001
 */
public interface AnalyseDataFutureService {


    /**
     * 多线程获取分析数据
     *
     * @param sampleIds 分析数据id
     * @return 日志集合
     */
    Future<List<DtoAnalyseData>> getListBySampleIdIn(List<String> sampleIds);

}
