package com.sinoyd.lims.pro.entity;


import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * ReportNumberPool数据实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/8/1
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ReportNumberPool")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ReportNumberPool implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ReportNumberPool() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 报告编号
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("报告编号")
    //@NotBlank(message = "报告编号{validation.message.blank}")
    @Length(message = "报告编号{validation.message.length}", max = 100)
    private String code;

    /**
     * 年份
     */
    @Column(length = 10)
    @ApiModelProperty("年份")
    private Integer year;

    /**
     * 报告类型id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("报告类型id")
    private String reportTypeId;

    /**
     * 编号状态 0:未使用 1:已使用 2: 已作废
     */
    @Column(length = 10)
    @ApiModelProperty("编号状态 0:未使用 1:已使用 2: 已作废")
    private Integer status;


    /**
     * 使用日期
     */
    @Column()
    @ApiModelProperty("使用日期")
    private Date usedDate;

    /**
     * 编号内号码
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("编号内号码")
    private Integer number;

    /**
     * 流水号类型
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("流水号类型")
    @Length(message = "流水号类型{validation.message.length}", max = 50)
    private String serialType;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
