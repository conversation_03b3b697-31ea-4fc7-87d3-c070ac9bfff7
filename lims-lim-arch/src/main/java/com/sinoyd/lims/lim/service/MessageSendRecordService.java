package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoMessageSendRecord;

import java.util.List;


/**
 * 消息业务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022-09-22
 */
public interface MessageSendRecordService extends IBaseJpaService<DtoMessageSendRecord, String> {

    /**
     * 关注消息数据
     *
     * @param ids       主键ids
     * @param isConcern 是否关注
     */
    boolean concernSendRecord(List<String> ids, Boolean isConcern);

    /**
     * 查询当前人消息的总数
     *
     * @return 返回总数
     */
    Integer queryLoginUserMsgCount();

    /**
     * 设置已读
     *
     * @param id 主键id
     * @return 返回状态
     */
    boolean setupRead(String id);

    /**
     * 设置已读
     *
     * @return 返回状态
     */
    boolean setupReadAll();

    /**
     * 根据消息类型查询当前人员下消息数量
     *
     * @param messageTypes 消息类型
     * @return 消息数量
     */
    Integer queryMessageCountByType(List<String> messageTypes);

    /**
     * 实时消息推送
     */
    void realTimeMessages();

    /**
     * 选中id设置已读
     *
     * @return 返回状态
     */
    boolean setupReadByIds(List<String> ids);

    /**
     * 创建提醒消息
     *
     * @param messageType
     * @param messageContent
     * @param receiverId
     * @return
     */
    DtoMessageSendRecord createMessage(String messageType, String messageContent, String receiverId);
}
