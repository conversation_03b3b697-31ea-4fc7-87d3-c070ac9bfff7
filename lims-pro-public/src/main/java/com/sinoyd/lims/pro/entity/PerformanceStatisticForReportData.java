package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;


/**
 * PerformanceStatisticForReportData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/24
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="PerformanceStatisticForReportData")
 @Data
 public  class PerformanceStatisticForReportData implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  PerformanceStatisticForReportData() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 项目id
    */
    @Column(length=100)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目id")
	private String projectId;
    
    /**
    * 报告时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("报告时间")
	private Date reportTime;
    
    /**
    * 编制报告人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("编制报告人id")
	private String reportMakerId;
    
    /**
    * 报告数
    */
    @Column(nullable=false)
    @ApiModelProperty("报告数")
	private Integer report;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }