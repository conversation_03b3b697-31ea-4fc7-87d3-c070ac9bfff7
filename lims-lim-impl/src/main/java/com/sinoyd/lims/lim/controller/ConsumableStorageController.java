package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ConsumableStorageCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoConsumableStorage;
import com.sinoyd.lims.lim.service.ConsumableStorageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 入库记录
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Api(tags = "消耗品入库: 消耗品入库服务")
@RestController
@RequestMapping("/api/lim/consumableStorage")
@Validated
public class ConsumableStorageController extends BaseJpaController<DtoConsumableStorage,String,ConsumableStorageService> {

    /**
     * 分页查询入库记录
     * @param criteria 入库记录查询条件
     * @return RestResponse<List<DtoConsumableStorage>>
     */
    @ApiOperation(value = "分页查询入库记录", notes = "分页查询入库记录")
    @GetMapping
    public RestResponse<List<DtoConsumableStorage>> findByPage(ConsumableStorageCriteria criteria){
        RestResponse<List<DtoConsumableStorage>> restResp = new RestResponse<>();
        
        PageBean<DtoConsumableStorage> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 新增入库记录
     * @param entity 入库实体
     * @return RestResponse<DtoConsumableStorage>
     */
    @ApiOperation(value = "新增入库记录", notes = "新增入库记录")
    @PostMapping
    public RestResponse<DtoConsumableStorage> create(@Validated @RequestBody DtoConsumableStorage entity)
    {
        RestResponse<DtoConsumableStorage> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoConsumableStorage data = service.save(entity);
        restResp.setData(data);

        return restResp;
    }
}