package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.TestService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 测试项目同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/21
 */
@Component
@DependsOn({"springContextAware"})
@Order(9)
@Slf4j
public class TestSync extends AbsDataSync<DtoTest> {

    private TestService testService;

    /**
     * 数据比较
     *
     * @param testIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoTest>> compareData(List<String> testIds) {
        //获取项目上全部测试项目
        List<DtoTest> projectDataList = testService.findAll();
        //公共库中的全部测试项目
        List<DtoTest> standardDataList = queryStandardData();
        //如果testIdList不是空，则表示选择部分同步
        if (StringUtil.isNotEmpty(testIds)) {
            standardDataList = standardDataList.parallelStream().filter(p -> testIds.contains(p.getId()))
                    .collect(Collectors.toList());
        }
        //比较数据
        return compareData(standardDataList, projectDataList);
    }

    /**
     * 同步数据
     *
     * @param testIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> testIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoTest>> compareResult = compareData(testIds);
        Optional<DtoDataCompareResult<DtoTest>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoTest errorDto = null;
            try {
                for (DtoTest dtoTest : r.getAddDataList()) {
                    errorDto = dtoTest;
                    if (testService.findOne(dtoTest.getId()) != null) {
                        testService.update(dtoTest);
                    } else {
                        testService.save(dtoTest);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.测试项目.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.测试项目.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/test";
    }

    @Override
    public ResponseEntity<JSONObject> queryStandardData(BaseCriteria baseCriteria, int page, int rows, String sort) {
        TestCriteria criteria = (TestCriteria) baseCriteria;
        String url = standardDataConfig.getHost() + getStandardDataQueryUrl() +
                "?page=" + page + "&rows=" + rows + "&sampleTypeId=" + criteria.getSampleTypeId() +
                "&key=" + criteria.getKey() + "&sort=" + sort + "&cert=" + ((StringUtil.isNotNull(criteria.getCert())) ? criteria.getCert() : "");
        return queryStandardData(url);
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.测试项目.getValue();
    }

    @Override
    public List<Integer> getDependDataType() {
        return Arrays.asList(EnumLIM.EnumDataSyncType.量纲.getValue(),
                EnumLIM.EnumDataSyncType.参数.getValue(),
                EnumLIM.EnumDataSyncType.行业类型.getValue(),
                EnumLIM.EnumDataSyncType.检测类型.getValue(),
                EnumLIM.EnumDataSyncType.检测类型参数.getValue(),
                EnumLIM.EnumDataSyncType.分析项目.getValue(),
                EnumLIM.EnumDataSyncType.参数配置.getValue(),
                EnumLIM.EnumDataSyncType.分析方法.getValue(),
                EnumLIM.EnumDataSyncType.测试项目.getValue(),
                EnumLIM.EnumDataSyncType.测试项目修约配置.getValue(),
                EnumLIM.EnumDataSyncType.测试项目公式.getValue(),
                EnumLIM.EnumDataSyncType.测试项目参数部分公式.getValue(),
                EnumLIM.EnumDataSyncType.测试项目公式参数.getValue());
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }
}