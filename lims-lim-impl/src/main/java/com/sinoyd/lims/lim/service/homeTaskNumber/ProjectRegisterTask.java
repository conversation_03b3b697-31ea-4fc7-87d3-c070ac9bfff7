package com.sinoyd.lims.lim.service.homeTaskNumber;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.lim.enums.EnumLIM;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 首页项目登记代办数字
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/10/26
 */
@Component
public class ProjectRegisterTask extends AbsTaskNumber {

    /**
     * 获取模块数量
     *
     * @param homeModule 模块编码
     * @param orgId      组织id
     * @param userId     人员id
     * @param outTypeIds 不包含id
     * @return 模块数量集合
     */
    @Override
    public List<DtoTaskNum> getTaskNum(HomeModule homeModule, String orgId, String userId, List<String> outTypeIds) {
        //创建查询sql语句
        StringBuilder stringBuilder = new StringBuilder("select b.inceptPersonId as userId,count(a.id) as count from TB_PRO_StatusForProject a,TB_PRO_Project b ");
        stringBuilder.append(" where 1=1")
                .append(" and a.orgId= ?")
                .append(" and a.status= ? and a.module = ?")
                .append(" and a.projectId=b.id");
        String[] arg = new String[]{orgId,"1",EnumLIM.EnumProjectModule.项目登记.getCode()};
        if (StringUtil.isNotEmpty(outTypeIds)) {
            StringBuilder outTypeIdBuilder = new StringBuilder();
            for (String outTypeId : outTypeIds) {
                outTypeIdBuilder.append("'").append(outTypeId).append("',");
            }
            outTypeIdBuilder.deleteCharAt(outTypeIdBuilder.lastIndexOf(","));
            stringBuilder.append(" and b.projectTypeId not in (").append(outTypeIdBuilder).append(")");
        }
        stringBuilder.append(" group by b.inceptPersonId");
        //执行sql语句
        return jdbcTemplate.query(stringBuilder.toString(),
                arg,
                (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"),resultSet.getLong("count")));
    }

    /**
     * 获取模块编码
     *
     * @return 模块编码
     */
    @Override
    public String getModuleCode() {
        return EnumLIM.EnumHomeTaskModule.项目登记.getValue();
    }
}
