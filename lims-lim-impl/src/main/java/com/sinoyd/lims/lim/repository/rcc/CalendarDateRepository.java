package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoCalendarDate;

import java.util.Date;
import java.util.List;

/**
 * 日历日期仓储
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/19
 */
public interface CalendarDateRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoCalendarDate, String>, LimsRepository<DtoCalendarDate, String> {

    /**
     * 根据时间区间来查询数据
     *
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @return 日历日期List
     */
    List<DtoCalendarDate> findByCalendarDateBetweenOrderByCalendarDate(Date beginDate, Date endDate);

}