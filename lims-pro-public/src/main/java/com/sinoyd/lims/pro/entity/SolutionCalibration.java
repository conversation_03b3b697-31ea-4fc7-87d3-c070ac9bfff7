package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * SolutionCalibration实体
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="SamplingPersonConfig")
@Data
public class SolutionCalibration implements BaseEntity, Serializable {
    private static final long serialVersionUID = 1L;

    public  SolutionCalibration() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 检测单标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测单标识")
    private String workSheetFolderId;

    /**
     * 标定溶液名称
     */
    @ApiModelProperty("标定溶液名称")
    @Column(length = 100)
    @Length(message = "标定溶液名称{validation.message.length}", max = 100)
    private String calibrationSolutionName;

    /**
     * 标定溶液名称
     */
    @ApiModelProperty("移取溶液名称")
    @Column(length = 100)
    @Length(message = "移取溶液名称{validation.message.length}", max = 100)
    private String transferSolutionName;

    /**
     * 移取溶液浓度
     */
    @ApiModelProperty("移取溶液浓度")
    @Column(length = 50)
    @Length(message = "移取溶液浓度{validation.message.length}", max = 50)
    private String transferSolutionConcentration;

    /**
     * 移取溶液浓度量纲标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("移取溶液浓度量纲标识")
    private String transferSolutionDimensionId;

    /**
     * 有效位数
     */
    @ApiModelProperty("有效位数")
    @Column
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    @ApiModelProperty("小数位数")
    @Column
    private Integer mostDecimal;

    /**
     * 标定浓度均值
     */
    @ApiModelProperty("标定浓度均值")
    @Column(length = 50)
    @Length(message = "标定浓度均值{validation.message.length}", max = 50)
    private String averageConcentration;

    /**
     * 标定浓度均值量纲标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("标定浓度均值量纲标识")
    private String averageConcentrationDimensionId;

    /**
     * 计算公式
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("计算公式")
    @Length(message = "计算公式{validation.message.length}", max = 100)
    private String formula;

    /**
     * 空白1
     */
    @Column(length = 50)
    @ApiModelProperty("空白1")
    @Length(message = "空白1{validation.message.length}", max = 50)
    private String blankOne;

    /**
     * 空白2
     */
    @Column(length = 50)
    @ApiModelProperty("空白2")
    @Length(message = "空白2{validation.message.length}", max = 50)
    private String blankTwo;

    /**
     * 空白均值
     */
    @Column(length = 50)
    @ApiModelProperty("空白均值")
    @Length(message = "空白均值{validation.message.length}", max = 50)
    private String blankAvg;

    /**
     * 标定日期
     */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("标定日期")
    private Date calibrationDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
