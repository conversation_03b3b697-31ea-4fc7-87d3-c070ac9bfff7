package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.SystemConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * 系统信息管理配置DTO
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/12/8
 */
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_SystemConfig")
@Data
public class DtoSystemConfig extends SystemConfig {

    @Transient
    String logoPic;

    @Transient
    private String editionCode;
}