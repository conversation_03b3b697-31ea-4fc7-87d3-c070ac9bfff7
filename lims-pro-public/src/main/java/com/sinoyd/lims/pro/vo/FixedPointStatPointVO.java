package com.sinoyd.lims.pro.vo;

import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 监测点位统计点位数据VO
 *
 * <AUTHOR>
 * @version V5.2.0 2025/4/14
 * @since V100R001
 */
@Data
public class FixedPointStatPointVO {

    /**
     * 点位id
     */
    private String pointId;

    /**
     * 点位名称
     */
    private String pointName;


    /**
     * 日期值数据（用于数据展示）
     */
    private List<FixedPointStatDateVO> dateDetail = new ArrayList<>();

    /**
     * 日期值数据(用于统计图表)
     */
    private List<FixedPointStatDateVO> statisticalChartsDetail = new ArrayList<>();

    /**
     * 构造方法（根据点位构造）
     *
     * @param point 监测点位数据
     */
    public FixedPointStatPointVO(DtoFixedpoint point) {
        this.pointId = point.getId();
        this.pointName = point.getPointName();
    }
}
