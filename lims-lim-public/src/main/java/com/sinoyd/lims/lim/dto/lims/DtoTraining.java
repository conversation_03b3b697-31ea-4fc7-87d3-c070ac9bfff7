package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.Training;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoTraining实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_Training")
@Data
@DynamicInsert
public class DtoTraining extends Training {

    /**
     * 参与人集合
     */
    @Transient
    private List<DtoTraining2Participants> participantsList;

    /**
     * 参与人id集合
     */
    @Transient
    private List<String> participantIds;

    /**
     * 培训方式名称
     */
    @Transient
    private String wayName;
}
