package com.sinoyd.lims.pro.util;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 计算操作工具
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public class MathUtil {
    private static char[] cnArr = new char[]{'一', '二', '三', '四', '五', '六', '七', '八', '九'};
    private static Pattern NUMBER_PATTERN = Pattern.compile("^[-\\+]?[\\d]*$");
    private static Pattern FLOAT_PATTERN = Pattern.compile("(\\d+\\.\\d+)");

    /**
     * 判断是否是数字
     *
     * @param value 值
     */
    public static Boolean isNumeral(Object value) {
        if (StringUtil.isNotNull(value)) {
            if (value instanceof Number) {
                return true;
            } else if (value instanceof String) {
                return stringIsNumeral((String) value);
            }
        }
        return false;
    }

    /**
     * 判断是否是整数
     *
     * @param value 值
     */
    public static boolean isInteger(Object value) {
        if (StringUtil.isNotNull(value) && StringUtil.isNotEmpty(String.valueOf(value))) {
            return NUMBER_PATTERN.matcher(String.valueOf(value)).matches();
        }
        return false;
    }

    /**
     * 判断字符串是否是数字
     *
     * @param value 字符串
     */
    private static Boolean stringIsNumeral(String value) {
        if (value.contains("e") || value.contains("E")) {
            try {
                new BigDecimal(value).toPlainString();
            } catch (Exception e) {
                //转换失败，说明是非数字
                return false;
            }
            return true;
        } else if (StringUtils.isNotNullAndEmpty(value) && (!value.trim().equals(""))) {
            if (value.startsWith("-") || value.startsWith("+")) {
                if (value.length() == 1) {
                    return false;
                }
                value = value.substring(1);
            }

            // hex
            if (value.length() > 2 && (value.startsWith("0x") || value.startsWith("0X"))) {
                char[] charArr = value.substring(2).toCharArray();
                for (char c : charArr) {
                    if (!((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'))) {
                        return false;
                    }
                }
                return true;
            }

            // 0-9,Point,Scientific
            Integer p = 0, s = 0, l = value.length();
            char[] charArr = value.toCharArray();
            for (Integer i = 0; i < l; i++) {
                if (charArr[i] == '.') {
                    // Point
                    if (p > 0 || s > 0 || i + 1 == l) {
                        return false;
                    }
                    p = i;
                } else if (charArr[i] == 'e' || charArr[i] == 'E') {
                    // Scientific
                    if (i == 0 || s > 0 || i + 1 == l) {
                        return false;
                    }
                    s = i;
                } else if (charArr[i] < '0' || charArr[i] > '9') {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 求和
     *
     * @param args 值
     */
    public static BigDecimal sumBigDecimal(Object... args) {
        BigDecimal sum = BigDecimal.valueOf(0);
        for (Object arg : args) {
            sum = sum.add(getBigDecimal(arg));
        }
        return sum;
    }

    /**
     * 求积
     *
     * @param args 值
     */
    public static BigDecimal multiplyBigDecimal(Object... args) {
        BigDecimal product = BigDecimal.valueOf(1);
        for (Object arg : args) {
            product = product.multiply(getBigDecimal(arg));
        }
        return product;
    }

    /**
     * Object转BigDecimal
     *
     * @param value 值
     */
    public static BigDecimal getBigDecimal(Object value) {
        BigDecimal ret = null;
        if (StringUtil.isNotNull(value)) {
            if (value instanceof BigDecimal) {
                ret = (BigDecimal) value;
            } else if (value instanceof String) {
                if (stringIsNumeral((String) value)) {
                    ret = new BigDecimal((String) value);
                } else {
                    throw new ClassCastException("[" + value + "] is not a correct BigDecimal value.");
                }
            } else if (value instanceof BigInteger) {
                ret = new BigDecimal((BigInteger) value);
            } else if (value instanceof Number) {
                ret = new BigDecimal(String.valueOf(((Number) value).doubleValue()));
            } else {
                throw new ClassCastException("Not possible to coerce [" + value + "] from class " + value.getClass() + " into a BigDecimal.");
            }
        }
        return ret;
    }

    /**
     * 阿拉伯数字转中文
     *
     * @param intInput 数值
     */
    public static String toChinese(Integer intInput) {
        String si = String.valueOf(intInput);
        String sd = "";
        if (si.length() == 1) {
            if (intInput == 0) {
                return sd;
            }
            sd += cnArr[intInput - 1];
            return sd;
        } else if (si.length() == 2) {
            if (si.substring(0, 1).equals("1")) {
                sd += "十";
                if (intInput % 10 == 0) {
                    return sd;
                }
            } else
                sd += (cnArr[intInput / 10 - 1] + "十");
            sd += toChinese(intInput % 10);
        } else if (si.length() == 3) {
            sd += (cnArr[intInput / 100 - 1] + "百");
            if (String.valueOf(intInput % 100).length() < 2) {
                if (intInput % 100 == 0) {
                    return sd;
                }
                sd += "零";
            }
            sd += toChinese(intInput % 100);
        } else if (si.length() == 4) {
            sd += (cnArr[intInput / 1000 - 1] + "千");
            if (String.valueOf(intInput % 1000).length() < 3) {
                if (intInput % 1000 == 0) {
                    return sd;
                }
                sd += "零";
            }
            sd += toChinese(intInput % 1000);
        } else if (si.length() == 5) {
            sd += (cnArr[intInput / 10000 - 1] + "万");
            if (String.valueOf(intInput % 10000).length() < 4) {
                if (intInput % 10000 == 0) {
                    return sd;
                }
                sd += "零";
            }
            sd += toChinese(intInput % 10000);
        }

        return sd;
    }

    /**
     * 获取字符串包含数值的小数位数
     *
     * @param str 字符串
     * @return 小数位数
     */
    public static Integer getMostDecimal(String str) {
        Matcher m = FLOAT_PATTERN.matcher(str);
        //m.find用来判断该字符串中是否含有与"(\\d+\\.\\d+)"相匹配的子串
        if (m.find()) {
            //如果有相匹配的,则判断是否为null操作
            //group()中的参数：0表示匹配整个正则，1表示匹配第一个括号的正则,2表示匹配第二个正则,在这只有一个括号,即1和0是一样的
            String value = StringUtils.isNull(m.group(1)) ? "" : m.group(1);
            if (StringUtils.isNotNull(value) && value.contains(".")) {
                return value.split("\\..")[1].length();
            }
        }
        return -1;
    }

    /**
     * BigDecimal转科学记数法字符串
     *
     * @param decimal 需要转换的值
     * @return 完成转换的字符串
     */
    private static String bigDecimalToScientificCounting(BigDecimal decimal){
        StringBuilder result = new StringBuilder();
        String sign = "";
        if (decimal.compareTo(new BigDecimal(0))==-1){
            decimal = decimal.abs();
            sign = "-";
        }
        String power = "";
        int timesOfTen = (int)Math.log10(decimal.doubleValue());
        if (timesOfTen>2){
            decimal = decimal.divide(new BigDecimal(10).pow(timesOfTen));
            power = "e"+timesOfTen;
        } else if (timesOfTen<-1){
            BigDecimal temp = new BigDecimal(timesOfTen).abs();
            decimal = decimal.multiply(new BigDecimal(10).pow(temp.intValue()+1)).stripTrailingZeros();
            power = "e"+(timesOfTen-1);
        }
        if ("-".equals(sign)){
            decimal = decimal.multiply(new BigDecimal(-1));
        }
        result.append(decimal);
        result.append(power);
        return result.toString();
    }
}