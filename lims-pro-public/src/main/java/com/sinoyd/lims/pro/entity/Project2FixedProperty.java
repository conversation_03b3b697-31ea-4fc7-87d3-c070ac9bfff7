package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;


/**
 * Project2FixedProperty实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Project2FixedProperty")
 @Data
 public  class Project2FixedProperty implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    /**
    * 项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目id")
	private String projectId;
    
    /**
    * 断面属性id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("断面属性id")
	private String fixedPropertyId;
    
 }