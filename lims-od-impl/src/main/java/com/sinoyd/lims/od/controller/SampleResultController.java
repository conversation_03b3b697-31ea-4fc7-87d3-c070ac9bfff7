package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.SampleResultCriteria;
import com.sinoyd.lims.od.dto.DtoLabGroup;
import com.sinoyd.lims.od.dto.DtoSampleResult;
import com.sinoyd.lims.od.dto.customer.DtoGroupSummaryTemp;
import com.sinoyd.lims.od.dto.customer.DtoSampleResultTemp;
import com.sinoyd.lims.od.service.SampleResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * sampleResult服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例: sampleResult服务")
@RestController
@RequestMapping("api/ods/sampleResult")
@Validated
public class SampleResultController extends BaseJpaController<DtoSampleResult, String, SampleResultService> {

    /**
     * 分页动态条件查询SampleResult
     *
     * @param sampleResultCriteria 条件参数
     * @return RestResponse<List < SampleResult>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoSampleResult>> findByPage(SampleResultCriteria sampleResultCriteria) {
        PageBean<DtoSampleResult> pageBean = super.getPageBean();
        RestResponse<List<DtoSampleResult>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, sampleResultCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询sampleResult
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询sampleResult", notes = "根据id查询sampleResult")
    @GetMapping("/{id}")
    public RestResponse<DtoSampleResult> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoSampleResult> restResp = new RestResponse<>();
        DtoSampleResult entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增sampleResult
     *
     * @param sampleResult sampleResult实体
     * @return 新增的sampleResult实体
     */
    @ApiOperation(value = "新增sampleResult", notes = "新增sampleResult")
    @PostMapping("")
    public RestResponse<DtoSampleResult> create(@Validated @RequestBody DtoSampleResult sampleResult) {
        RestResponse<DtoSampleResult> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoSampleResult data = service.save(sampleResult);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新sampleResult
     *
     * @param sampleResult sampleResult实体
     * @return 更新后的sampleResult实体
     */
    @ApiOperation(value = "更新sampleResult", notes = "更新sampleResult")
    @PutMapping("")
    public RestResponse<DtoSampleResult> update(@Validated @RequestBody DtoSampleResult sampleResult) {
        RestResponse<DtoSampleResult> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoSampleResult data = service.update(sampleResult);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }

    /**
     * 根据样品id查询sampleResult
     *
     * @param sampleId 样品id
     * @return
     */
    @ApiOperation(value = "根据id查询sampleResult", notes = "根据id查询sampleResult")
    @GetMapping("/sampleResult/{sampleId}")
    public RestResponse<DtoSampleResult> findBySampleId(@PathVariable(name = "sampleId") String sampleId) {
        RestResponse<DtoSampleResult> restResp = new RestResponse<>();
        DtoSampleResult entity = service.findBySampleId(sampleId);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 根据样品id查询默认或者最终实验组汇总信息，返回默认实验组id和最终实验次序
     *
     * @param sampleId 样品id
     * @return
     */
    @ApiOperation(value = "根据样品id查询默认实验组信息", notes = "根据样品id查询默认实验组信息")
    @GetMapping("/defaultGroup")
    public RestResponse<List<DtoGroupSummaryTemp>> findDefaultGroupBySampleId(@RequestParam("sampleId") String sampleId) {
        RestResponse<List<DtoGroupSummaryTemp>> restResp = new RestResponse<>();
        List<DtoGroupSummaryTemp> labGroups = service.findDefaultGroupBySampleId(sampleId);
        restResp.setData(labGroups);
        restResp.setRestStatus(StringUtil.isNull(labGroups) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 获取实验组汇总信息
     *
     * @param groupSummaryTemps 实验数据汇总临时传输实体
     * @return 实验组汇总结果
     */
    @ApiOperation(value = "获取实验组汇总信息", notes = "获取实验组汇总信息")
    @PostMapping("/groupSummary")
    public RestResponse<List<DtoLabGroup>> findGroupSummary(@RequestBody List<DtoGroupSummaryTemp> groupSummaryTemps) {
        RestResponse<List<DtoLabGroup>> restResp = new RestResponse<>();
        List<DtoLabGroup> entity = service.findGroupSummary(groupSummaryTemps);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }


    /**
     * 计算样品结果
     *
     * @param sampleResultTemp 样品结果汇总传输实体
     * @return
     */
    @ApiOperation(value = "计算样品结果", notes = "计算样品结果")
    @PostMapping("/calculate")
    public RestResponse<DtoSampleResult> calculateResult(@RequestBody DtoSampleResultTemp sampleResultTemp) {
        RestResponse<DtoSampleResult> restResp = new RestResponse<>();
        DtoSampleResult sampleResult = service.calculate(sampleResultTemp);
        restResp.setData(sampleResult);
        restResp.setRestStatus(StringUtil.isNull(sampleResult) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 完成实验
     *
     * @param sampleId 样品id
     * @return
     */
    @ApiOperation(value = "完成实验", notes = "完成实验")
    @PostMapping("/complete/{sampleId}")
    public RestResponse<DtoSampleResult> complete(@PathVariable("sampleId") String sampleId) {
        RestResponse<DtoSampleResult> restResp = new RestResponse<>();
        DtoSampleResult result = service.complete(sampleId);
        restResp.setData(result);
        restResp.setRestStatus(StringUtil.isNull(result) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


}
