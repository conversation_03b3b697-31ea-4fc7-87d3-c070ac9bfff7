package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.FastNavigationTemplateService;
import com.sinoyd.lims.lim.criteria.FastNavigationTemplateCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoFastNavigationTemplate;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.Collection;
import java.util.List;


/**
 * FastNavigationTemplate服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/3/27
 * @since V100R001
 */
 @Api(tags = "示例: FastNavigationTemplate服务")
 @RestController
 @RequestMapping("api/lim/fastNavigationTemplate")
 @Validated
 public class FastNavigationTemplateController extends BaseJpaController<DtoFastNavigationTemplate, String,FastNavigationTemplateService> {


    /**
     * 分页动态条件查询FastNavigationTemplate
     * @param fastNavigationTemplateCriteria 条件参数
     * @return RestResponse<List<FastNavigationTemplate>>
     */
     @ApiOperation(value = "分页动态条件查询FastNavigationTemplate", notes = "分页动态条件查询FastNavigationTemplate")
     @GetMapping
     public RestResponse<List<DtoFastNavigationTemplate>> findByPage(FastNavigationTemplateCriteria fastNavigationTemplateCriteria) {
         PageBean<DtoFastNavigationTemplate> pageBean = super.getPageBean();
         RestResponse<List<DtoFastNavigationTemplate>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fastNavigationTemplateCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }


    /**
     * 新增FastNavigationTemplate
     * @param fastNavigationTemplates 实体列表
     * @return RestResponse<DtoFastNavigationTemplate>
     */
     @ApiOperation(value = "新增个人快速导航", notes = "新增个人快速导航")
     @PostMapping
     public RestResponse<List<DtoFastNavigationTemplate>> create(@Validated @RequestBody Collection<DtoFastNavigationTemplate> fastNavigationTemplates) {
         RestResponse<List<DtoFastNavigationTemplate>> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fastNavigationTemplates));
         return restResponse;
      }
 }