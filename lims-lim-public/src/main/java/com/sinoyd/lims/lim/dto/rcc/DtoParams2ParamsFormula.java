package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.Params2ParamsFormula;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoParams2ParamsFormula实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_Params2ParamsFormula") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoParams2ParamsFormula extends Params2ParamsFormula {

 }