package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 测试项目仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/16
 * @since V100R001
 */
public interface TestRepository extends IBaseJpaRepository<DtoTest, String>, LimsRepository<DtoTest, String> {

    /**
     * 根据样品类型（大类）Id查询测试项目
     *
     * @param sampleTypeId 检测类型id
     * @return 返回相应的测试项目
     */
    @Query("select p from DtoTest p where p.isDeleted = 0 and p.sampleTypeId = :sampleTypeId")
    List<DtoTest> findBySampleTypeId(@Param("sampleTypeId") String sampleTypeId);

    /**
     * 根据父id查询测试项目
     *
     * @param parentIdList 父id列表
     * @return 返回相应的测试项目
     */
    @Query("select d from DtoTest d where d.isDeleted = 0 and d.parentId in :parentIdList")
    List<DtoTest> findByParentIdIn(@Param("parentIdList") List<String> parentIdList);

    /**
     * 根据分析项目id获取相应的测试项目数据
     *
     * @param analyzeItemId 分析项目id
     * @return 返回相应的测试项目数据
     */
    @Query("select p from DtoTest p where p.isDeleted = 0 and p.analyzeItemId = :analyzeItemId")
    List<DtoTest> findByAnalyzeItemId(@Param("analyzeItemId") String analyzeItemId);

    /**
     * 根据分析项目id列表获取相应的测试项目数据
     *
     * @param analyzeItemIds 分析项目id列表
     * @return 测试项目数据
     */
    List<DtoTest> findByAnalyzeItemIdInAndIsDeletedFalse(List<String> analyzeItemIds);

    /**
     * 根据分析项目id与分析方法id获取对于的测试项目数据
     *
     * @param analyzeItemId   分析项目id
     * @param analyzeMethodId 分析方法id
     * @return 测试项目数据
     */
    DtoTest findByAnalyzeItemIdAndAnalyzeMethodId(String analyzeItemId, String analyzeMethodId);

    /**
     * 根据分析项目id与分析方法id获取对于的测试项目数据
     *
     * @param analyzeItemId   分析项目id
     * @param analyzeMethodId 分析方法id
     * @return 测试项目数据
     */
    DtoTest findByAnalyzeItemIdAndAnalyzeMethodIdAndIsDeletedFalse(String analyzeItemId, String analyzeMethodId);

    /**
     * 新增子测试项目
     *
     * @param id     父级id
     * @param idList 测试项目id
     * @return 返回相应的测试项目
     */
    @Modifying
    @Query("update DtoTest d set d.parentId = :id where d.id in :idList")
    Integer createSonTests(@Param("id") String id, @Param("idList") Collection<String> idList);

    /**
     * 删除子测试项目
     *
     * @param id     父级id
     * @param idList 测试项目id
     * @return 删除相应的测试项目
     */
    @Modifying
    @Query("update DtoTest d set d.parentId = '00000000-0000-0000-0000-000000000000' where  d.isDeleted = 0 and d.id in :idList and d.parentId = :id")
    Integer delete(@Param("id") String id, @Param("idList") Collection<String> idList);

    /**
     * 获取子测试项目列表
     *
     * @param ids 父级id
     * @return 返回相应的测试项目
     */
    @Query("select d from DtoTest d where d.isDeleted = 0 and d.parentId in :ids order by orderNum desc")
    Set<DtoTest> getListByParentIds(@Param("ids") List<String> ids);


    /**
     * 批量更新parentId 为null
     *
     * @param ids 子项目ids
     * @return 更新子项目的信息
     */
    @Modifying
    @Query("update DtoTest d set d.parentId = '00000000-0000-0000-0000-000000000000' where d.id in :ids")
    Integer updateChildTestNull(@Param("ids") Collection<String> ids);

    /**
     * 根据分析方法id数组查询测试项目
     *
     * @param ids 分析方法ids
     * @return 测试项目集合
     */
    @Query("select p from DtoTest p where p.isDeleted = 0 and p.analyzeMethodId in :ids")
    List<DtoTest> getListByAnalyzeMethodId(@Param("ids") Collection<String> ids);


    /**
     * 新增时判断是否存在同名测试项目
     *
     * @param itemName   分析项目名称
     * @param methodName 分析方法名称
     * @return 相同名称的测试项目数量
     */
    @Query("select count(p.id) from DtoTest p where p.isDeleted = 0 and p.redAnalyzeItemName = :itemName and p.redAnalyzeMethodName = :methodName and p.sampleTypeId = :sampleTypeId")
    Integer getCountByName(@Param("itemName") String itemName, @Param("methodName") String methodName, @Param("sampleTypeId") String sampleTypeId);

    /**
     * 更新时判断是否存在同名测试项目
     *
     * @param itemName   分析项目名称
     * @param methodName 分析方法名称
     * @param id         要更新的测试项目的id
     * @return 相同名称的测试项目数量
     */
    @Query("select count(p.id) from DtoTest p where p.isDeleted = 0 and p.redAnalyzeItemName = :itemName and p.redAnalyzeMethodName = :methodName and p.id <> :id")
    Integer getCountByNameAndId(@Param("itemName") String itemName, @Param("methodName") String methodName, @Param("id") String id);

    /**
     * 更新时判断是否存在同名测试项目,区分样品类型
     *
     * @param redAnalyzeItemName   分析项目名称
     * @param redAnalyzeMethodName 分析方法名称
     * @param sampleTypeId         样品类型id
     * @param idList               要更新的测试项目的id
     * @return 相同名称的测试项目数量
     */
    List<DtoTest> findByRedAnalyzeItemNameAndRedAnalyzeMethodNameAndSampleTypeIdAndIdNotInAndIsDeletedFalse(String redAnalyzeItemName, String redAnalyzeMethodName,
                                                                                                            String sampleTypeId, List<String> idList);

    /**
     * 重写获取所有的方法
     *
     * @return 返回未删除测试项目信息
     */
    @Override
    @Query("select p from DtoTest p where p.isDeleted = 0")
    List<DtoTest> findAll();

    /**
     * 根据id数组获取测试项目信息
     *
     * @param ids 测试项目信息ids
     * @return 返回测试项目信息
     */
    @Query("select d from DtoTest d where d.isDeleted = 0 and d.id in :ids")
    @Override
    List<DtoTest> findAll(@Param("ids") Iterable<String> ids);


    /**
     * 返回所有的带假删的测试项目信息
     *
     * @return 返回带删除的测试项目信息
     */
    @Query("select p from DtoTest p")
    List<DtoTest> findAllDeleted();

    /**
     * 返回所有的带假删的测试项目信息
     *
     * @param ids 测试项目的ids
     * @return 返回带删除的测试项目信息
     */
    @Query("select p from DtoTest p where p.id in :ids")
    List<DtoTest> findAllDeleted(@Param("ids") Collection<String> ids);

    /**
     * 批量更新相关测试项目的分析项目信息
     *
     * @param ids 子项目ids
     * @return 更新测试项目的信息
     */
    @Transactional
    @Modifying
    @Query("update DtoTest d set d.redAnalyzeItemName = :analyzeItemName,d.fullPinYin = :fullPinYin,d.pinYin = :pinYin where d.id in :ids")
    Integer updateAnalyzeItemName(@Param("ids") List<String> ids,
                                  @Param("analyzeItemName") String analyzeItemName,
                                  @Param("fullPinYin") String fullPinYin,
                                  @Param("pinYin") String pinYin
    );

    /**
     * 根据分析项目id数组获取测试项目信息
     *
     * @param analyzeItemIds 分析项目ids
     * @return 返回测试项目信息
     */
    @Query("select d from DtoTest d where d.isDeleted = 0 and d.sampleTypeId = :sampleTypeId and d.analyzeItemId in :analyzeItemIds")
    List<DtoTest> findBySampleTypeIdAndAnalyzeItemIdIn(@Param("sampleTypeId") String sampleTypeId,
                                                       @Param("analyzeItemIds") Iterable<String> analyzeItemIds);

    /**
     * 排序自增
     *
     * @param idList 测试项目id
     * @return 返回相应的测试项目
     */
    @Modifying
    @Transactional
    @Query("update DtoTest d set d.orderNum = d.orderNum+1 where d.id in :idList")
    Integer incrementOrderNum(@Param("idList") Collection<String> idList);

    /**
     * 排序自减
     *
     * @param idList 测试项目id
     * @return 返回相应的测试项目
     */
    @Modifying
    @Transactional
    @Query("update DtoTest d set d.orderNum = d.orderNum-1 where d.id in :idList")
    Integer decrementOrderNum(@Param("idList") Collection<String> idList);

    /**
     * 更新相关测试项目的分析项目信息
     *
     * @param id 测试项目id
     * @return 更新测试项目的信息
     */
    @Transactional
    @Modifying
    @Query("update DtoTest d set d.testName = :testName,d.redAnalyzeItemName = :analyzeItemName,d.fullPinYin = :fullPinYin,d.pinYin = :pinYin where d.id = :id")
    Integer updateAnalyzeItemInfo(@Param("id") String id,
                                  @Param("testName") String testName,
                                  @Param("analyzeItemName") String analyzeItemName,
                                  @Param("fullPinYin") String fullPinYin,
                                  @Param("pinYin") String pinYin);

    /**
     * 更新相关测试项目的统计别名
     *
     * @param id 测试项目id
     * @return 更新测试项目的信息
     */
    @Transactional
    @Modifying
    @Query("update DtoTest d set d.itemStatisticalAlias = :itemStatisticalAlias where d.id = :id")
    Integer updateAnalyzeItemStatisticalAlias(@Param("id") String id,@Param("itemStatisticalAlias") String itemStatisticalAlias);

    /**
     * 根据id集合批量查询测试项目
     *
     * @param ids id List集合
     * @return 查询到的测试项目
     */
    List<DtoTest> findByIdIn(List<String> ids);

    /**
     * 根据分析方法获取测试项目
     *
     * @param methodId 分析方法id
     * @return
     */
    List<DtoTest> findByAnalyzeMethodIdAndIsDeletedFalse(String methodId);

    /**
     * 根据分析项目集合查询测试项目
     *
     * @param analyzeItemIds 分析项目id集合
     * @return 测试项目集合
     */
    List<DtoTest> findByAnalyzeItemIdIn(List<String> analyzeItemIds);

    /**
     * 根据资质查询测试项目
     *
     * @param certList 资质
     * @return 测试项目集合
     */
    List<DtoTest> findByCertInAndIsDeletedFalse(List<Integer> certList);

    /**
     * 根据关键字(分析项目名称、分析方法名称、标准编号)查询
     *
     * @param key 关键字
     * @return 测试项目集合
     */
    @Query("select t.id from DtoTest t where t.redAnalyzeItemName like :key  " +
            "and t.isDeleted = false ")
    List<String> findWithAnalyzeItemKey(@Param("key") String key);

    /**
     * 根据关键字(分析项目名称、分析方法名称、标准编号)查询
     *
     * @param key 关键字
     * @return 测试项目集合
     */
    @Query("select t.id from DtoTest t where (t.redAnalyzeMethodName like :key " +
            "or t.redCountryStandard like :key) " +
            "and t.isDeleted = false ")
    List<String> findWithAnalyzeMethodKey(@Param("key") String key);

    /**
     * 根据分析方法查询
     *
     * @param analyzeMethodIds 分析方法标识列表
     * @return 测试项目列表
     */
    List<DtoTest> findByAnalyzeMethodIdIn(List<String> analyzeMethodIds);

    /**
     * 根据分析方法查询被废止的测试项目
     *
     * @param analyzeMethodIds 分析方法标识列表
     * @return 测试项目列表
     */
    List<DtoTest> findByAnalyzeMethodIdInAndIsAbolishTrue(List<String> analyzeMethodIds);

    /**
     * 根据分析项目名称查询测试项目
     *
     * @param analyzeItemNames   分析项目名称集合
     * @return 测试项目集合
     */
    List<DtoTest> findByRedAnalyzeItemNameIn(Collection<String> analyzeItemNames);
}