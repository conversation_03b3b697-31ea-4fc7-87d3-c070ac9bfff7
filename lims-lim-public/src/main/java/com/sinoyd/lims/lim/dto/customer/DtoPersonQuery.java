package com.sinoyd.lims.lim.dto.customer;

import java.util.List;

import lombok.Data;

/**
 * 人员查询自定义dto
 * <AUTHOR>
 * @version V1.0.0 2019/11/19
 * @since V100R001
 */
@Data
public class DtoPersonQuery {

    /**
     * 权限
     */
    private List<String> permission;


    /**
     * 是否账户
     */
    private Boolean isUser;

    /**
     * 人员状态
     */
    private List<Integer> status;

    /**
     * 岗位id
     */
    private String postId;

    /**
     * 机构编码
     */
    private String orgCode = "";
}
