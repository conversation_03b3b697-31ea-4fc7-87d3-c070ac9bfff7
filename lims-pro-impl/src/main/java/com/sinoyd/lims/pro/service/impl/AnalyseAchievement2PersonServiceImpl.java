package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoCalendarDate;
import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.CostRepository;
import com.sinoyd.lims.lim.service.CalendarDateService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.criteria.AnalyseAchievement2PersonCriteria;
import com.sinoyd.lims.pro.criteria.AnalyseAchievementDetailsCriteria;
import com.sinoyd.lims.pro.criteria.AnalyzeTimelinessCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.repository.AnalyseAchievement2PersonRepository;
import com.sinoyd.lims.pro.repository.AnalyseAchievementDetailsRepository;
import com.sinoyd.lims.pro.repository.OrderQuotationRepository;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.service.AnalyseAchievement2PersonService;
import com.sinoyd.lims.pro.service.AnalyseAchievementDetailsService;
import com.sinoyd.lims.pro.service.AnalyzeTimelinessDataService;
import com.sinoyd.lims.pro.service.SampleService;
import com.sinoyd.lims.pro.vo.AnalyzeTimelinessVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AnalyseAchievement2Person操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Service
@Slf4j
public class AnalyseAchievement2PersonServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoAnalyseAchievement2Person, String, AnalyseAchievement2PersonRepository> implements AnalyseAchievement2PersonService {

    private AnalyseAchievementDetailsService analyseAchievementDetailsService;

    private AnalyseAchievementDetailsRepository analyseAchievementDetailsRepository;

    private DepartmentService departmentService;

    private PersonService personService;

    private CommonRepository commonRepository;

    private ProjectRepository projectRepository;

    private OrderQuotationRepository orderQuotationRepository;

    private TestRepository testRepository;

    private CostRepository costRepository;

    private AnalyzeTimelinessDataService analyzeTimelinessDataService;

    private CodeService codeService;

    private SampleService sampleService;

    private CalendarDateService calendarDateService;

    @Override
    public void findByPage(PageBean<DtoAnalyseAchievement2Person> page, BaseCriteria criteria) {
        AnalyseAchievement2PersonCriteria achievement2PersonCriteria = (AnalyseAchievement2PersonCriteria) criteria;
        page.setEntityName("DtoAnalyseAchievement2Person a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData(), achievement2PersonCriteria);
        page.getData().sort(Comparator.comparing(DtoAnalyseAchievement2Person::getPersonName,Comparator.nullsLast(Comparator.naturalOrder())));
    }

    @Override
    @Transactional
    public void selectPerson(List<String> personIds) {
        List<DtoAnalyseAchievement2Person> saveList = new ArrayList<>();
        personIds.forEach(p -> {
            DtoAnalyseAchievement2Person analyseAchievement2Person = new DtoAnalyseAchievement2Person();
            analyseAchievement2Person.setPersonId(p);
            saveList.add(analyseAchievement2Person);
        });
        if (StringUtil.isNotEmpty(saveList)) {
            super.save(saveList);
        }
    }

    @Override
    public void verifySelectPerson(List<String> personIds) {
        List<DtoAnalyseAchievement2Person> achievements = repository.findByPersonIdIn(personIds);
        List<DtoPerson> personList = personService.findAll(personIds);
        List<String> personNames = new ArrayList<>();
        personList.forEach(p -> {
            Optional<DtoAnalyseAchievement2Person> achievement = achievements.stream().filter(a -> a.getPersonId().equals(p.getId())).findFirst();
            achievement.ifPresent(a -> personNames.add(p.getCName()));
        });
        if (StringUtil.isNotEmpty(personNames)) {
            throw new BaseException(String.join(",", personNames) + "的绩效已存在");
        }
    }

    @Override
    @Transactional
    public void updateData(Integer year, List<String> ids) {
        List<DtoAnalyseAchievement2Person> achievements = repository.findAll(ids);
        List<String> personIds = achievements.stream().map(DtoAnalyseAchievement2Person::getPersonId).collect(Collectors.toList());
        Date startTime = DateUtil.stringToDate(CalendarUtil.getCurrentYearBegin(year), DateUtil.YEAR);
        Date endTime = DateUtil.stringToDate(CalendarUtil.getCurrentYearEnd(year), DateUtil.YEAR);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endTime);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        endTime = calendar.getTime();
        StringBuilder condition = new StringBuilder();
        condition.append("select s.projectId,s.id,s.code,s.sampleTypeId,a.testId,a.redAnalyzeItemName,a.redAnalyzeMethodName,a.redCountryStandard,a.analystId,a.analystName,a.analyzeTime,s.samplingTimeBegin ");
        condition.append("from DtoSample s, DtoAnalyseData a where s.id = a.sampleId and s.isDeleted = 0 and a.isDeleted = 0 ");
        condition.append("and s.sampleCategory = 0 and a.status = '已确认' and a.isOutsourcing = 0 and a.analystId in :personIds and a.analyzeTime >= :startTime and a.analyzeTime < :endTime");
        Map<String, Object> values = new HashMap<>();
        values.put("startTime", startTime);
        values.put("endTime", endTime);
        values.put("personIds", personIds);
        List<Object[]> datas = commonRepository.find(condition.toString(), values);
        List<DtoAnalyseAchievementDetails> details = new ArrayList<>();
        for (Object[] data : datas) {
            DtoAnalyseAchievementDetails analyseAchievementDetails = new DtoAnalyseAchievementDetails();
            analyseAchievementDetails.setProjectId(data[0].toString());
            analyseAchievementDetails.setSampleId(data[1].toString());
            analyseAchievementDetails.setSampleCode(data[2].toString());
            analyseAchievementDetails.setSampleTypeId(data[3].toString());
            analyseAchievementDetails.setTestId(data[4].toString());
            analyseAchievementDetails.setAnalyzeItemName(data[5].toString());
            analyseAchievementDetails.setAnalyzeMethodName(data[6].toString());
            analyseAchievementDetails.setCountryStandard(data[7].toString());
            analyseAchievementDetails.setAnalystId(data[8].toString());
            analyseAchievementDetails.setAnalystName(data[9].toString());
            analyseAchievementDetails.setAnalyzeTime((Date) data[10]);
            analyseAchievementDetails.setRequiredCompletedDate((Date) data[11]);
            details.add(analyseAchievementDetails);
        }
        List<String> projectIds = details.stream().map(DtoAnalyseAchievementDetails::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> projects = StringUtil.isNotEmpty(projectIds) ? projectRepository.findAll(projectIds) : new ArrayList<>();
        List<String> orderIds = projects.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getOrderId())).map(DtoProject::getOrderId).distinct().collect(Collectors.toList());
        List<DtoOrderQuotation> quotations = orderQuotationRepository.findByOrderIdIn(orderIds);
        Map<String, Object> proId2ZSXS = new HashMap<>();
        for (DtoProject project : projects) {
            DtoOrderQuotation quotation = quotations.stream().filter(q -> q.getOrderId().equals(project.getOrderId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(quotation)) {
                BigDecimal num = new BigDecimal(0);
                if (quotation.getFinalQuotation().compareTo(BigDecimal.ZERO) > 0 && (quotation.getTestPrice().add(quotation.getOtherPrice()).compareTo(BigDecimal.ZERO) > 0)){
                    num = quotation.getFinalQuotation().divide(quotation.getTestPrice().add(quotation.getOtherPrice()).add((quotation.getTestPrice().add(quotation.getOtherPrice())).multiply(quotation.getTaxRate()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP)), 2, BigDecimal.ROUND_HALF_UP);
                }
                else if(quotation.getFinalQuotation().compareTo(BigDecimal.ZERO) == 0
                        && quotation.getTotalPrice().compareTo(BigDecimal.ZERO) > 0
                        && (quotation.getTestPrice().add(quotation.getOtherPrice()).compareTo(BigDecimal.ZERO) > 0)){
                    //BUG2024052901649 【重要】【2024-5-31】【马川江】【绩效统计】采样绩效统计、分析绩效统计，计算产值的时候，如果“最终报价”为0的，需要带入“参考总价”进行计算。
                    num = quotation.getTotalPrice().divide(quotation.getTestPrice().add(quotation.getOtherPrice()).add((quotation.getTestPrice().add(quotation.getOtherPrice())).multiply(quotation.getTaxRate()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP)), 2, BigDecimal.ROUND_HALF_UP);
                }
                proId2ZSXS.put(project.getId(), num);
            }
        }
        List<DtoCalendarDate> calendarDates = calendarDateService.findByYear(CalendarUtil.getYear(new Date()));
        Map<String, DtoCalendarDate> date2CalendarDate = calendarDates.stream().collect(Collectors.toMap(dto -> DateUtil.dateToString(dto.getCalendarDate(), DateUtil.YEAR), d -> d));
        List<String> testIds = details.stream().map(DtoAnalyseAchievementDetails::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        Map<String, DtoTest> testMap = tests.stream().collect(Collectors.toMap(DtoTest::getId, dto -> dto));
        List<String> parentTestIds = tests.stream().filter(t -> !UUIDHelper.GUID_EMPTY.equals(t.getParentId())).map(DtoTest::getParentId).collect(Collectors.toList());
        List<DtoTest> parentTests = StringUtil.isNotEmpty(parentTestIds) ? testRepository.findAll(parentTestIds) : new ArrayList<>();
        testIds.addAll(parentTestIds);
        List<DtoCost> costs = StringUtil.isNotEmpty(testIds) ? costRepository.findByTestIdIn(testIds) : new ArrayList<>();
        analyseAchievementDetailsRepository.deleteByAchievementIdInAndAnalyzeTimeBetween(ids, startTime, endTime);
        List<DtoAnalyseAchievementDetails> saveList = new ArrayList<>();
        Calendar calendarAnalyze = Calendar.getInstance();
        achievements.forEach(a -> {
            List<DtoAnalyseAchievementDetails> details2Achievement = details.stream().filter(d -> d.getAnalystId().equals(a.getPersonId())).collect(Collectors.toList());
            for (DtoAnalyseAchievementDetails data : details2Achievement) {
                DtoAnalyseAchievementDetails save = new DtoAnalyseAchievementDetails();
                BeanUtils.copyProperties(data, save, "id");
                Date samplingTime = data.getRequiredCompletedDate();
                Integer analyseDayLen = 0;
                DtoTest anaTest = testMap.getOrDefault(data.getTestId(), null);
                if (StringUtil.isNotNull(anaTest)) {
                    analyseDayLen = anaTest.getAnalyseDayLen();
                }
                calendarAnalyze.clear();
                calendarAnalyze.setTime(data.getAnalyzeTime());
                Calendar calendarComplete = Calendar.getInstance();
                for (int i = 0; i < analyseDayLen; i++) {
                    Calendar cal = addCalendarDay(samplingTime, i + 1);
                    DtoCalendarDate calendarDate = date2CalendarDate.get(DateUtil.dateToString(cal.getTime(), DateUtil.YEAR));
                    calendarComplete = cal;
                    if (StringUtil.isNotNull(calendarDate) && calendarDate.getType().equals(1)) {
                        analyseDayLen++;
                    }
                }
                if (calendarComplete.compareTo(calendarAnalyze) < 0) {
                    save.setStatus("超期");
                } else {
                    save.setStatus("未超期");
                }
                save.setAchievementId(a.getId());
                Optional<DtoCost> cost = costs.stream().filter(c -> c.getTestId().equals(data.getTestId())).findFirst();
                Optional<DtoTest> test = tests.stream().filter(t -> t.getId().equals(data.getTestId())).findFirst();
                if (test.isPresent()) {
                    Optional<DtoTest> parentTest = parentTests.stream().filter(pt -> pt.getId().equals(test.get().getParentId())).findFirst();
                    if (parentTest.isPresent()) {
                        cost = costs.stream().filter(c -> c.getTestId().equals(parentTest.get().getId())).findFirst();
                    }
                }
                cost.ifPresent(c -> {
                    if (proId2ZSXS.containsKey(data.getProjectId())) {
                        save.setTotalAmount(c.getAnalyzeCost().multiply((BigDecimal) proId2ZSXS.get(data.getProjectId())).setScale(0, BigDecimal.ROUND_HALF_UP));
                    } else {
                        save.setTotalAmount(c.getAnalyzeCost().setScale(0, BigDecimal.ROUND_HALF_UP));
                    }
                });
                saveList.add(save);
            }
        });
        if (StringUtil.isNotEmpty(saveList)) {
            analyseAchievementDetailsRepository.save(saveList);
        }
    }

    @Override
    public Map<String, BigDecimal> chartForPerMonth() {
        DtoCode code = codeService.findByCode("AnalysisPerformance");
        final Map<String, BigDecimal> map = new HashMap<>();
        List<DtoAnalyseAchievementDetails> details = analyseAchievementDetailsRepository.findAll();
        if (code != null && "1".equals(code.getDictValue())) {
            List<String> testIds = details.stream().map(DtoAnalyseAchievementDetails::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> tests = testRepository.findAll(testIds);
            List<String> parentTestIds = tests.stream().filter(t -> !UUIDHelper.GUID_EMPTY.equals(t.getParentId())).map(DtoTest::getParentId).collect(Collectors.toList());
            List<DtoTest> parentTests = StringUtil.isNotEmpty(parentTestIds) ? testRepository.findAll(parentTestIds) : new ArrayList<>();
            List<DtoAnalyseAchievementDetails> allDetails = new ArrayList<>();
            Map<String, Map<String, List<DtoAnalyseAchievementDetails>>> allMap = details.stream().collect(Collectors.groupingBy(d -> DateUtil.dateToString(d.getAnalyzeTime(), "yyyy-MM"), Collectors.groupingBy(DtoAnalyseAchievementDetails::getSampleId)));
            for (Map.Entry<String, Map<String, List<DtoAnalyseAchievementDetails>>> entry : allMap.entrySet()) {
                for (Map.Entry<String, List<DtoAnalyseAchievementDetails>> sampleEntry : entry.getValue().entrySet()) {
                    List<String> testIds2Sample = sampleEntry.getValue().stream().map(DtoAnalyseAchievementDetails::getTestId).distinct().collect(Collectors.toList());
                    List<DtoTest> tests2Sample = tests.stream().filter(t -> testIds2Sample.contains(t.getId())).collect(Collectors.toList());
                    Map<String, List<DtoTest>> parentId2Test = tests2Sample.stream().collect(Collectors.groupingBy(DtoTest::getParentId));
                    for (Map.Entry<String, List<DtoTest>> testEntry : parentId2Test.entrySet()) {
                        Optional<DtoTest> parentTest = parentTests.stream().filter(pt -> pt.getId().equals(testEntry.getKey())).findFirst();
                        List<String> testIds2Parent = testEntry.getValue().stream().map(DtoTest::getId).collect(Collectors.toList());
                        if (parentTest.isPresent()) {
                            Optional<DtoAnalyseAchievementDetails> detailsPt = sampleEntry.getValue().stream().filter(d -> testIds2Parent.contains(d.getTestId())).findFirst();
                            if (detailsPt.isPresent()) {
                                allDetails.add(detailsPt.get());
                            }
                        } else {
                            List<DtoAnalyseAchievementDetails> detailsPt = sampleEntry.getValue().stream().filter(d -> testIds2Parent.contains(d.getTestId())).collect(Collectors.toList());
                            for (DtoAnalyseAchievementDetails detail : detailsPt) {
                                allDetails.add(detail);
                            }
                        }
                    }
                }
            }
            map.putAll(allDetails.stream().collect(Collectors.groupingBy(d -> DateUtil.dateToString(d.getAnalyzeTime(), "yyyy-MM"),
                    Collectors.mapping(DtoAnalyseAchievementDetails::getTotalAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)))));
        } else {
            Map<String, List<DtoAnalyseAchievementDetails>> anaDateMap = details.stream().collect(Collectors.groupingBy(d -> DateUtil.dateToString(d.getAnalyzeTime(), "yyyy-MM"), Collectors.toList()));
            anaDateMap.forEach((k,v) -> {
                // 调整为统计样品数
                List<String> sampleIds = v.stream().map(DtoAnalyseAchievementDetails::getSampleId).distinct().collect(Collectors.toList());
                BigDecimal num = new BigDecimal(String.valueOf(sampleIds.size()));
                map.put(k, num);
            });
        }
        return map;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        analyseAchievementDetailsRepository.deleteByAchievementIdIn((List<String>) ids);
        return super.logicDeleteById(ids);
    }

    /**
     * 填充冗余字段
     * @param dataList 数据
     * @param criteria 查询条件
     */
    private void fillingTransientFields(List<DtoAnalyseAchievement2Person> dataList, AnalyseAchievement2PersonCriteria criteria) {
        long t1 = System.currentTimeMillis();
        PageBean<DtoAnalyseAchievementDetails> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        AnalyseAchievementDetailsCriteria achievementDetailsCriteria = new AnalyseAchievementDetailsCriteria();
        achievementDetailsCriteria.setStartTime(criteria.getStartTime());
        achievementDetailsCriteria.setEndTime(criteria.getEndTime());
        analyseAchievementDetailsService.findByPage(pb, achievementDetailsCriteria);
        long t11 = System.currentTimeMillis();
        log.info("DtoAnalyseAchievementDetails："+(t11-t1));
        List<DtoDepartment> departments = departmentService.findAll();
        long t12 = System.currentTimeMillis();
        log.info("departments："+(t12-t11));
        List<String> personIds = dataList.stream().map(DtoAnalyseAchievement2Person::getPersonId).collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
        long t13 = System.currentTimeMillis();
        log.info("personList："+(t13-t12));
        List<String> testIds = pb.getData().stream().map(DtoAnalyseAchievementDetails::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        List<String> parentTestIds = tests.stream().filter(t -> !UUIDHelper.GUID_EMPTY.equals(t.getParentId())).map(DtoTest::getParentId).collect(Collectors.toList());
        List<DtoTest> parentTests = StringUtil.isNotEmpty(parentTestIds) ? testRepository.findAll(parentTestIds) : new ArrayList<>();
        long t14 = System.currentTimeMillis();
        log.info("parentTests："+(t14-t13));
        AnalyzeTimelinessCriteria analyzeTimelinessCriteria = new AnalyzeTimelinessCriteria();
        analyzeTimelinessCriteria.setStartSamplingDate(criteria.getStartTime());
        analyzeTimelinessCriteria.setEndSamplingDate(criteria.getEndTime());
        List<Object> timelinessDataObject = analyzeTimelinessDataService.queryTimeliness(analyzeTimelinessCriteria);
        long t2 = System.currentTimeMillis();
        log.info("数据查询耗时："+(t2-t1));
        List<AnalyzeTimelinessVO> timelinessData = new ArrayList<>();
        if (timelinessDataObject != null) {
            timelinessDataObject.forEach(t -> {
                try {
                    String json = JsonUtil.toJson(t);
                    timelinessData.add(JsonUtil.toObject(json, AnalyzeTimelinessVO.class));
                } catch (Exception e) {
                    throw new BaseException(e.getMessage());
                }
            });
        }
        dataList.forEach(data -> {
            List<DtoAnalyseAchievementDetails> achievementDetails = pb.getData().stream().filter(a -> a.getAchievementId().equals(data.getId())).collect(Collectors.toList());
            data.setSampleNum(achievementDetails.size());
            Map<String, List<DtoAnalyseAchievementDetails>> sampleId2AchievementDetails = achievementDetails.stream().collect(Collectors.groupingBy(DtoAnalyseAchievementDetails::getSampleId));
            BigDecimal total = new BigDecimal("0");
            for (Map.Entry<String, List<DtoAnalyseAchievementDetails>> entry : sampleId2AchievementDetails.entrySet()) {
                List<String> testIds2Sample = entry.getValue().stream().map(DtoAnalyseAchievementDetails::getTestId).distinct().collect(Collectors.toList());
                List<DtoTest> tests2Sample = tests.stream().filter(t -> testIds2Sample.contains(t.getId())).collect(Collectors.toList());
                Map<String, List<DtoTest>> parentId2Test = tests2Sample.stream().collect(Collectors.groupingBy(DtoTest::getParentId));
                for (Map.Entry<String, List<DtoTest>> testEntry : parentId2Test.entrySet()) {
                    Optional<DtoTest> parentTest = parentTests.stream().filter(pt -> pt.getId().equals(testEntry.getKey())).findFirst();
                    List<String> testIds2Parent = testEntry.getValue().stream().map(DtoTest::getId).collect(Collectors.toList());
                    if (parentTest.isPresent()) {
                        Optional<DtoAnalyseAchievementDetails> details = entry.getValue().stream().filter(d -> testIds2Parent.contains(d.getTestId())).findFirst();
                        if (details.isPresent()) {
                            total = total.add(details.get().getTotalAmount());
                        }
                    } else {
                        List<DtoAnalyseAchievementDetails> details = entry.getValue().stream().filter(d -> testIds2Parent.contains(d.getTestId())).collect(Collectors.toList());
                        for (DtoAnalyseAchievementDetails detail : details) {
                            total = total.add(detail.getTotalAmount());
                        }
                    }
                }
            }
            data.setTotal(total);
            Optional<DtoPerson> person = personList.stream().filter(p -> p.getId().equals(data.getPersonId())).findFirst();
            person.ifPresent(p -> {
                data.setPersonName(p.getCName());
                Optional<DtoDepartment> dtoDepartment = departments.stream().filter(d -> d.getId().equals(p.getDeptId())).findFirst();
                dtoDepartment.ifPresent(d -> data.setDeptName(d.getDeptName()));
            });
            Optional<AnalyzeTimelinessVO> vo = timelinessData.stream().filter(t -> t.getAnalystId().equals(data.getPersonId())).findFirst();
            vo.ifPresent(v -> data.setRateStr(v.getTimelinessRate()));
        });
        long t3 = System.currentTimeMillis();
        log.info("附加数据耗时："+(t3-t1));
    }

    /**
     * 根据日期以及天数获取日历时间
     *
     * @param date 日期
     * @param day  天数
     * @return 日历日期
     */
    private Calendar addCalendarDay(Date date, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, day);
        return calendar;
    }

    @Autowired
    @Lazy
    public void setAnalyseAchievementDetailsService(AnalyseAchievementDetailsService analyseAchievementDetailsService) {
        this.analyseAchievementDetailsService = analyseAchievementDetailsService;
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setAnalyseAchievementDetailsRepository(AnalyseAchievementDetailsRepository analyseAchievementDetailsRepository) {
        this.analyseAchievementDetailsRepository = analyseAchievementDetailsRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setOrderQuotationRepository(OrderQuotationRepository orderQuotationRepository) {
        this.orderQuotationRepository = orderQuotationRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setCostRepository(CostRepository costRepository) {
        this.costRepository = costRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyzeTimelinessDataService(AnalyzeTimelinessDataService analyzeTimelinessDataService) {
        this.analyzeTimelinessDataService = analyzeTimelinessDataService;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    @Lazy
    public void setCalendarDateService(CalendarDateService calendarDateService) {
        this.calendarDateService = calendarDateService;
    }
}
