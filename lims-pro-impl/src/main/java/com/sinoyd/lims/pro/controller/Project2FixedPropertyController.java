package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.Project2FixedPropertyService;
import com.sinoyd.lims.pro.criteria.Project2FixedPropertyCriteria;
import com.sinoyd.lims.pro.dto.DtoProject2FixedProperty;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * Project2FixedProperty服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: Project2FixedProperty服务")
 @RestController
 @RequestMapping("api/pro/project2FixedProperty")
 public class Project2FixedPropertyController extends BaseJpaController<DtoProject2FixedProperty, String,Project2FixedPropertyService> {


    /**
     * 分页动态条件查询Project2FixedProperty
     * @param project2FixedPropertyCriteria 条件参数
     * @return RestResponse<List<Project2FixedProperty>>
     */
     @ApiOperation(value = "分页动态条件查询Project2FixedProperty", notes = "分页动态条件查询Project2FixedProperty")
     @GetMapping
     public RestResponse<List<DtoProject2FixedProperty>> findByPage(Project2FixedPropertyCriteria project2FixedPropertyCriteria) {
         PageBean<DtoProject2FixedProperty> pageBean = super.getPageBean();
         RestResponse<List<DtoProject2FixedProperty>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, project2FixedPropertyCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询Project2FixedProperty
     * @param id 主键id
     * @return RestResponse<DtoProject2FixedProperty>
     */
     @ApiOperation(value = "按主键查询Project2FixedProperty", notes = "按主键查询Project2FixedProperty")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoProject2FixedProperty> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoProject2FixedProperty> restResponse = new RestResponse<>();
         DtoProject2FixedProperty project2FixedProperty = service.findOne(id);
         restResponse.setData(project2FixedProperty);
         restResponse.setRestStatus(StringUtil.isNull(project2FixedProperty) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增Project2FixedProperty
     * @param project2FixedProperty 实体列表
     * @return RestResponse<DtoProject2FixedProperty>
     */
     @ApiOperation(value = "新增Project2FixedProperty", notes = "新增Project2FixedProperty")
     @PostMapping
     public RestResponse<DtoProject2FixedProperty> create(@RequestBody DtoProject2FixedProperty project2FixedProperty) {
         RestResponse<DtoProject2FixedProperty> restResponse = new RestResponse<>();
         restResponse.setData(service.save(project2FixedProperty));
         return restResponse;
      }

     /**
     * 新增Project2FixedProperty
     * @param project2FixedProperty 实体列表
     * @return RestResponse<DtoProject2FixedProperty>
     */
     @ApiOperation(value = "修改Project2FixedProperty", notes = "修改Project2FixedProperty")
     @PutMapping
     public RestResponse<DtoProject2FixedProperty> update(@RequestBody DtoProject2FixedProperty project2FixedProperty) {
         RestResponse<DtoProject2FixedProperty> restResponse = new RestResponse<>();
         restResponse.setData(service.update(project2FixedProperty));
         return restResponse;
      }

    /**
     * "根据id批量删除Project2FixedProperty
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Project2FixedProperty", notes = "根据id批量删除Project2FixedProperty")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }