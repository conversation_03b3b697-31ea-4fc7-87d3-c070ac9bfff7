package com.sinoyd.lims.pro.strategy.strategy.judgeData;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoCompareJudge;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.CompareJudgeRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.service.QualityControlLimitService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sinoyd.base.constants.IBaseConstants.DEVIATION_FORMULA;

/**
 * 数据比对计算
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/28
 */
public abstract class AbsJudgeDataStrategy {

    protected CompareJudgeRepository compareJudgeRepository;

    protected QualityControlLimitRepository qualityControlLimitRepository;

    protected TestService testService;

    protected CalculateService calculateService;

    protected static List<DtoTest> testList = new ArrayList<>();

    protected static List<DtoQualityControlLimit> qualityControlLimitList = new ArrayList<>();

    protected static List<DtoCompareJudge> compareJudges = new ArrayList<>();

    protected QualityControlLimitService qualityControlLimitService;

    /**
     * 计算比对结果
     *
     * @param judgeDataList 比对数据
     */
    public abstract void calculateJudgeData(List<DtoSampleJudgeData> judgeDataList);

    protected void getData(List<DtoSampleJudgeData> judgeDataList) {
        List<String> testIds = judgeDataList.stream().map(DtoSampleJudgeData::getTestId).distinct().collect(Collectors.toList());
        testList = testService.findRedisByIds(testIds);
        List<String> anaItemIds = testList.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
        compareJudges = compareJudgeRepository.findByAnalyzeItemIdIn(anaItemIds);
        List<String> compareIds = compareJudges.stream().map(DtoCompareJudge::getId).collect(Collectors.toList());
        qualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(compareIds);
        List<Integer> qcTypes = qualityControlLimitList.stream().map(DtoQualityControlLimit::getQcType).distinct().collect(Collectors.toList());
        List<DtoQualityControlLimit> qcLimits = qualityControlLimitRepository.findByTestIdAndQcTypeIn(DEVIATION_FORMULA, qcTypes);
        qualityControlLimitList.forEach(p -> {
            Optional<DtoQualityControlLimit> controlLimit = qcLimits.stream().filter(q -> q.getQcType().equals(p.getQcType())).findFirst();
            controlLimit.ifPresent(c -> p.setQcRangeFormula(c.getFormula()));
        });
        qualityControlLimitService.fillControlMsg(qualityControlLimitList, "", "");
    }

    protected String halfLimit(String value, String examLimitValue, String examLimitValueLess) {
        if (MathUtil.isNumeral(value) && MathUtil.isNumeral(examLimitValue)) {
            //检出限大于检测结果
            if (MathUtil.getBigDecimal(examLimitValue).compareTo(MathUtil.getBigDecimal(value)) > 0) {
                value = "0";
            }
        } else {
            if (value.equals(checkValue(examLimitValue, examLimitValueLess))) {
                value = "0";
            }
        }
        return value;
    }

    protected void setJuageData(DtoSampleJudgeData sampleJudgeData) {
        if (StringUtil.isEmpty(sampleJudgeData.getExpectedValue()) || StringUtil.isEmpty(sampleJudgeData.getOnlineValue())) {
            sampleJudgeData.setPass("");
            //【重要】【2024-2-07】【徐逸舟】【现场任务】比对数据评价，“更新配置信息”和“评价”显示内容不一致 不需要更新
//            sampleJudgeData.setAllowLimit("");
            sampleJudgeData.setCheckItemValue("");
//            sampleJudgeData.setJudgingMethod(null);
            sampleJudgeData.setQcRateValue("");
        }
    }

    private String checkValue(String examLimitValue, String examLimitValueLess) {
        String result = "ND";
        EnumLIM.EnumExamLimitType examLimitType = EnumLIM.EnumExamLimitType.getByCode(examLimitValueLess);
        if (StringUtil.isNotNull(examLimitType)) {
            switch (examLimitType) {
                case 小于DL:
                    result = "＜DL";
                    break;
                case 检出限L:
                    result = examLimitValue + "L";
                    break;
                case 小于检出限:
                    result = "＜" + examLimitValue;
                    break;
                default:
                    result = "ND";
                    break;
            }
        }
        return result;
    }

    @Autowired
    public void setCompareJudgeRepository(CompareJudgeRepository compareJudgeRepository) {
        this.compareJudgeRepository = compareJudgeRepository;
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }

    @Autowired
    public void setCalculateService(CalculateService calculateService) {
        this.calculateService = calculateService;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    @Lazy
    public void setQualityControlLimitService(QualityControlLimitService qualityControlLimitService) {
        this.qualityControlLimitService = qualityControlLimitService;
    }
}
