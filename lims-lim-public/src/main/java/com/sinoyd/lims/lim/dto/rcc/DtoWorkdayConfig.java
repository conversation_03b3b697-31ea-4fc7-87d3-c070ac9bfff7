package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.WorkdayConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 工作休息日管理配置传输类
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/1/18
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_WorkdayConfig")
@Data
public class DtoWorkdayConfig extends WorkdayConfig {

}
