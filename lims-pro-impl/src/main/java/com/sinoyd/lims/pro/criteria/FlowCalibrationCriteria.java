package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * FlowCalibration查询条件
 * <AUTHOR>
 * @version V1.0.0
 * @since   2024/11/14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlowCalibrationCriteria extends BaseCriteria implements Serializable {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 校准类型
     */
    private Integer calibrationType;

    /**
     * 校准人标识
     */
    private String calibrationPersonId;

    /**
     * 关键字 仪器名称/规格型号/站内编号/出厂编号
     */
    private String key;

    /**
     * 仪器标识
     */
    private String instrumentId;

    /**
     * 送样单标识
     */
    private String receiveId;


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.instrumentId = i.id ");
        if(StringUtil.isNotEmpty(instrumentId)){
            condition.append(" and p.instrumentId = :instrumentId");
            values.put("instrumentId", instrumentId);
        }
        if(StringUtil.isNotEmpty(receiveId)){
            condition.append(" and p.receiveId like :receiveId");
            values.put("receiveId", "%"+receiveId+"%");
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and p.calibrationDate >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and p.calibrationDate < :endTime");
            values.put("endTime", to);
        }
        if(calibrationType!=null){
            condition.append(" and p.calibrationType = :calibrationType");
            values.put("calibrationType", calibrationType);
        }
        if(StringUtil.isNotEmpty(calibrationPersonId)){
            condition.append(" and p.calibrationPeople like :calibrationPersonId");
            values.put("calibrationPersonId", "%"+calibrationPersonId+"%");
        }
        if(StringUtil.isNotEmpty(key)){
            // 仪器名称/规格型号/站内编号/出厂编号
            condition.append(" and (i.instrumentName like :key or i.model like :key or i.instrumentsCode like :key or i.serialNo like :key)");
            values.put("key", "%"+key+"%");
        }
        return condition.toString();
    }
}
