package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.OrderContractCriteria;
import com.sinoyd.lims.pro.dto.DtoOrderContract;
import com.sinoyd.lims.pro.service.OrderContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * OrderContract服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
@Api(tags = "示例: orderContract服务")
@RestController
@RequestMapping("api/pro/orderContract")
public class OrderContractController extends BaseJpaController<DtoOrderContract,String,OrderContractService> {

    /**
     * 分页动态条件查询OrderContract
     *
     * @param contractCriteria 条件参数
     * @return RestResponse<List <DtoOrderContract>>
     */
    @ApiOperation(value = "分页动态条件查询OrderContract", notes = "分页动态条件查询OrderContract")
    @GetMapping
    public RestResponse<List<DtoOrderContract>> findByPage(OrderContractCriteria contractCriteria) {
        PageBean<DtoOrderContract> pageBean = super.getPageBean();
        RestResponse<List<DtoOrderContract>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, contractCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询OrderContract
     *
     * @param id 主键id
     * @return RestResponse<DtoOrderContract>
     */
    @ApiOperation(value = "按主键查询OrderContract", notes = "按主键查询OrderContract")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoOrderContract> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOrderContract> restResponse = new RestResponse<>();
        DtoOrderContract contract = service.findOne(id);
        restResponse.setData(contract);
        restResponse.setRestStatus(StringUtil.isNull(contract) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增contract
     *
     * @param contract 实体
     * @return RestResponse<DtoOrderContract>
     */
    @ApiOperation(value = "新增合同", notes = "新增合同")
    @PostMapping
    public RestResponse<DtoOrderContract> create(@RequestBody @Validated DtoOrderContract contract) {
        RestResponse<DtoOrderContract> restResponse = new RestResponse<>();
        restResponse.setData(service.save(contract));
        return restResponse;
    }

    /**
     * 修改contract
     *
     * @param contract 实体列表
     * @return RestResponse<DtoOrderContract>
     */
    @ApiOperation(value = "修改合同", notes = "修改合同")
    @PutMapping
    public RestResponse<DtoOrderContract> update(@RequestBody @Validated DtoOrderContract contract) {
        RestResponse<DtoOrderContract> restResponse = new RestResponse<>();
        restResponse.setData(service.update(contract));
        return restResponse;
    }

    /**
     * 根据id批量删除DtoOrderContract
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 合同编号自动生成
     *
     * @return RestResponse<String>
     */
    @ApiOperation(value = "合同编号自动生成", notes = "合同编号自动生成")
    @GetMapping("/code")
    public RestResponse<String> generateOrderContractCode() {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.generateOrderContractCode());
        restResp.setRestStatus( ERestStatus.SUCCESS);
        return restResp;
    }
}
