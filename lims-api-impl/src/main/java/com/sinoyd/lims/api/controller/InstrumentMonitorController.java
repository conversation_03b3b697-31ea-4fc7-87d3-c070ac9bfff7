package com.sinoyd.lims.api.controller;

import com.sinoyd.base.criteria.InstrumentCriteria;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.api.criteria.ProjectInstrumentDetailCriteria;
import com.sinoyd.lims.api.dto.*;
import com.sinoyd.lims.api.service.InstrumentMonitorService;
import com.sinoyd.lims.lim.criteria.EnvironmentalRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.lims.pro.criteria.ProjectCriteria;
import com.sinoyd.lims.pro.criteria.ProjectInstrumentQueryCriteria;
import com.sinoyd.lims.pro.dto.DtoProject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仪器出入库移动端内容
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Api(tags = "示例: instrumentMonitor")
@RestController
@RequestMapping("api/monitor/instrument")
public class InstrumentMonitorController extends BaseJpaController<DtoProjectInstrument, String, InstrumentMonitorService> {

    /**
     * 查询
     *
     * @param projectInstrumentQueryCriteria 查询条件
     * @return
     */
    @ApiOperation(value = "分页动态条件查询项目", notes = "分页动态条件查询项目")
    @GetMapping
    public RestResponse<List<DtoProjectInstrumentPhone>> getProjectInstrumentList(ProjectInstrumentQueryCriteria projectInstrumentQueryCriteria) {
        PageBean<DtoProjectInstrument> pageBean = super.getPageBean();
        RestResponse<List<DtoProjectInstrumentPhone>> restResponse = new RestResponse<>();
        List<DtoProjectInstrumentPhone> projectPhoneList = service.getProjectInstrumentList(pageBean, projectInstrumentQueryCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(projectPhoneList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(projectPhoneList);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询出入库记录
     *
     * @param id 主键id
     * @return RestResponse<DtoProjectInstrumentPhone>
     */
    @ApiOperation(value = "按主键查询出入库记录", notes = "按主键查询出入库记录")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoProjectInstrumentPhone> findProjectInstrumentPhone(@PathVariable(name = "id") String id) {
        RestResponse<DtoProjectInstrumentPhone> restResponse = new RestResponse<>();
        DtoProjectInstrumentPhone phone = service.findProjectInstrumentPhone(id);
        restResponse.setData(phone);
        restResponse.setRestStatus(StringUtil.isNull(phone) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 创建仪器入库记录
     *
     * @param instrumentPhone 仪器入库记录
     * @return RestResponse<DtoProjectInstrumentPhone>
     */
    @ApiOperation(value = "创建仪器入库记录", notes = "创建仪器入库记录")
    @PostMapping
    public RestResponse<DtoProjectInstrumentPhone> saveInstrumentPhone(@RequestBody DtoProjectInstrumentPhone instrumentPhone) {
        RestResponse<DtoProjectInstrumentPhone> restResp = new RestResponse<>();
        DtoProjectInstrumentPhone phone = service.saveInstrumentPhone(instrumentPhone);
        restResp.setData(phone);
        restResp.setRestStatus(StringUtil.isNull(phone) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 修改仪器入库记录
     *
     * @param instrumentPhone 仪器入库记录
     * @return RestResponse<DtoProjectInstrumentPhone>
     */
    @ApiOperation(value = "修改仪器入库记录", notes = "修改仪器入库记录")
    @PutMapping
    public RestResponse<DtoProjectInstrumentPhone> update(@RequestBody DtoProjectInstrumentPhone instrumentPhone) {
        RestResponse<DtoProjectInstrumentPhone> restResponse = new RestResponse<>();
        DtoProjectInstrumentPhone phone = service.updateInstrumentPhone(instrumentPhone);
        restResponse.setData(phone);
        restResponse.setRestStatus(StringUtil.isNull(phone) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 添加仪器记录明细
     *
     * @param detailPhone 参数
     */
    @ApiOperation(value = "添加仪器记录明细", notes = "添加仪器记录明细")
    @PostMapping("/accretionDetails")
    public RestResponse<Boolean> addInstrumentDetails(@RequestBody DtoProjectInstrumentDetailPhone detailPhone) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.addInstrumentDetails(detailPhone.getInstrumentId(), detailPhone.getProjectInstrumentId());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 批量添加仪器记录明细
     *
     * @param detailPhone 参数
     */
    @ApiOperation(value = "批量添加仪器记录明细", notes = "批量添加仪器记录明细")
    @PostMapping("/batchAccretionDetails")
    public RestResponse<Boolean> batchAddInstrumentDetails(@RequestBody DtoProjectInstrumentDetailPhone detailPhone) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.batchAddInstrumentDetails(detailPhone.getInstrumentIdList(), detailPhone.getProjectInstrumentId());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 添加仪器入库明细
     *
     * @param detailPhone 传入参数
     * @return 添加的条数
     */
    @ApiOperation(value = "添加仪器入库明细", notes = "添加仪器入库明细")
    @PostMapping("/details/in")
    public RestResponse<Boolean> addInstrumentDetailsIn(@RequestBody DtoProjectInstrumentDetailPhone detailPhone) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.addInstrumentDetailsIn(detailPhone.getInstrumentId(), detailPhone.getProjectInstrumentId());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 批量添加仪器入库明细，一次添加多个仪器
     *
     * @param detailPhone 传入参数
     * @return 添加的条数
     */
    @ApiOperation(value = "批量添加仪器入库明细", notes = "批量添加仪器入库明细")
    @PostMapping("/details/batchIn")
    public RestResponse<Boolean> addInstrumentDetailsBatchIn(@RequestBody DtoProjectInstrumentDetailPhone detailPhone) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.addInstrumentDetailsBatchIn(detailPhone.getInstrumentIdList(), detailPhone.getProjectInstrumentId());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 扫码出库弹出界面
     *
     * @param id 仪器id
     * @return RestResponse<DtoInstrumentOutPhone> 界面信息
     */
    @ApiOperation(value = "扫码出库弹出界面", notes = "扫码出库弹出界面")
    @GetMapping(path = "/out/{id}")
    public RestResponse<DtoInstrumentOutPhone> findInstrumentPhone(@PathVariable(name = "id") String id) {
        RestResponse<DtoInstrumentOutPhone> restResponse = new RestResponse<>();
        restResponse.setData(service.findInstrumentOutPhone(id));
        return restResponse;
    }

    /**
     * 扫码入库弹出界面
     *
     * @param instrumentId      仪器id
     * @return 入库弹出界面
     */
    @ApiOperation(value = "扫码入库弹出界面", notes = "扫码入库弹出界面")
    @GetMapping(path = "/in/{id}")
    public RestResponse<DtoInstrumentInPhone> findInstrumentInPhone(@PathVariable(name = "id") String instrumentId) {
        RestResponse<DtoInstrumentInPhone> restResponse = new RestResponse<>();
        restResponse.setData(service.findInstrumentInPhone(instrumentId));
        return restResponse;
    }

    /**
     * 仪器出入库记录批量删除
     *
     * @param detailPhone 仪器出入库信息ids
     * @return 删除条数
     */
    @ApiOperation(value = "仪器出入库记录批量删除", notes = "仪器出入库记录批量删除")
    @DeleteMapping
    public RestResponse<Boolean> deleteInstrumentPhone(@RequestBody DtoProjectInstrumentDetailPhone detailPhone) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.deleteInstrumentPhone(detailPhone.getIds());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 仪器出入库明细批量删除
     *
     * @param detailPhone 仪器出入库信息ids
     * @return 删除条数
     */
    @ApiOperation(value = "仪器出入库明细批量删除", notes = "仪器出入库明细批量删除")
    @DeleteMapping("/detail")
    public RestResponse<Boolean> deleteInstrumentDetailPhone(@RequestBody DtoProjectInstrumentDetailPhone detailPhone) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.deleteInstrumentDetailPhone(detailPhone.getIds());
        restResp.setData(true);
        return restResp;
    }

    /**
     * 仪器入库列表分页查询
     *
     * @param projectInstrumentDetailCriteria 查询条件
     * @return 分页数据
     */
    @ApiOperation(value = "仪器入库列表分页查询", notes = "仪器入库列表分页查询")
    @GetMapping("/in/page")
    public RestResponse<List<DtoProjectInstrumentDetailPhone>> findInstrumentInByPage(ProjectInstrumentDetailCriteria projectInstrumentDetailCriteria) {
        PageBean<DtoProjectInstrumentDetailPhone> pageBean = super.getPageBean();
        RestResponse<List<DtoProjectInstrumentDetailPhone>> restResponse = new RestResponse<>();
        service.findInstrumentInByPage(pageBean, projectInstrumentDetailCriteria);
        List<DtoProjectInstrumentDetailPhone> pageData = pageBean.getData();
        restResponse.setRestStatus(StringUtil.isEmpty(pageData) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageData);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 查询
     *
     * @param projectCriteria 查询条件
     * @return 查询项目
     */
    @ApiOperation(value = "分页动态条件查询项目", notes = "分页动态条件查询项目")
    @GetMapping("/project")
    public RestResponse<List<DtoProjectPhone>> getProjectList(ProjectCriteria projectCriteria) {
        PageBean<DtoProject> pageBean = super.getPageBean();
        RestResponse<List<DtoProjectPhone>> restResponse = new RestResponse<>();
        List<DtoProjectPhone> projectPhoneList = service.findProjectByPage(pageBean, projectCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(projectPhoneList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(projectPhoneList);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 查询仪器
     *
     * @param instrumentCriteria 查询条件
     * @return 仪器列表
     */
    @ApiOperation(value = "分页动态条件查询仪器", notes = "分页动态条件查询仪器")
    @GetMapping("/instrumentList")
    public RestResponse<List<DtoInstrument>> getInstrumentList(InstrumentCriteria instrumentCriteria) {
        PageBean<DtoInstrument> pageBean = super.getPageBean();
        RestResponse<List<DtoInstrument>> restResponse = new RestResponse<>();
        List<DtoInstrument> instrumentList = service.findInstrumentByPage(pageBean, instrumentCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(instrumentList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(instrumentList);
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 查询仪器使用记录
     *
     * @param recordCriteria 查询条件
     * @return 查询仪器使用记录
     */
    @ApiOperation(value = "查询仪器使用记录", notes = "查询仪器使用记录")
    @GetMapping("/instrumentRecord")
    public RestResponse<List<DtoEnvironmentalRecord>> getInstrumentRecordList(EnvironmentalRecordCriteria recordCriteria) {
        PageBean<DtoEnvironmentalRecord> pageBean = super.getPageBean();
        RestResponse<List<DtoEnvironmentalRecord>> restResponse = new RestResponse<>();
        service.findInstrumentRecordByPage(pageBean, recordCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据环境使用记录查询送样单状态
     *
     * @param environmentalRecord 环境实体
     */
    @ApiOperation(value = "根据环境使用记录查询送样单状态", notes = "根据环境使用记录查询送样单状态")
    @GetMapping(path = "/status")
    public RestResponse<Boolean> findReceiveStatus(DtoEnvironmentalRecord environmentalRecord) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        restResp.setData(service.findReceiveStatus(environmentalRecord));
        return restResp;
    }
}
