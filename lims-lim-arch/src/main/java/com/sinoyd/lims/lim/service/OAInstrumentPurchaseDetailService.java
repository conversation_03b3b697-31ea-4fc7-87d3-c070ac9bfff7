package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentPurchaseDetail;

/**
 * 仪器采购明细 业务操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
public interface OAInstrumentPurchaseDetailService extends IBaseJpaService<DtoOAInstrumentPurchaseDetail, String> {

    /**
     *  修改仪器剩余入库数量
     * @param oaInstrumentPurchaseDetail 仪器采购申请明细对象
     * @return DtoOAInstrumentPurchaseDetail
     */
    DtoOAInstrumentPurchaseDetail updateSurplusNum(DtoOAInstrumentPurchaseDetail oaInstrumentPurchaseDetail);
}
