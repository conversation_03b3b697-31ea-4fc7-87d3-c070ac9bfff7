package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.sampleFolder;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.DtoSampleFolderTemplate;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component(IFileNameConstant.SchemeSynchronizationKey.UPDATE_SAMPLEFOLDER)
public class UpdateSampleFolderStrategy extends AbsSampleFolderStrategy {

    /**
     * 调整点位方案
     *
     * @param sampleFolderTemplateList 修改点位内容
     */
    @Override
    public void synchronizationSampleFolder(String projectId, List<DtoSampleFolderTemplate> sampleFolderTemplateList) {
        //点位信息
        List<DtoSampleFolder> sampleFolderList = new ArrayList<>();
        sampleFolderTemplateList.forEach(s -> {
            DtoSampleFolder folder = new DtoSampleFolder();
            folder.setId(s.getSampleFolderId());
            folder.setWatchSpot(s.getWatchSpot());
            if (!StringUtil.isNotEmpty(s.getLon())) {
                s.setLon("");
            }
            folder.setLon(s.getLon());
            if (!StringUtil.isNotEmpty(s.getLat())) {
                s.setLat("");
            }
            folder.setLat(s.getLat());
            //先赋为空
            folder.setFolderCode("");
            sampleFolderList.add(folder);
        });
        sampleFolderList.forEach(p -> {
            sampleFolderService.update(p);
        });
    }
}
