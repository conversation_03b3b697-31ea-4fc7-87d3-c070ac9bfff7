package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoReportConfig2Module;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule2GroupType;
import com.sinoyd.lims.lim.repository.rcc.RecordConfigRepository;
import com.sinoyd.lims.lim.repository.rcc.ReportConfig2ModuleRepository;
import com.sinoyd.lims.lim.repository.rcc.ReportModule2GroupTypeRepository;
import com.sinoyd.lims.lim.repository.rcc.ReportModuleRepository;
import com.sinoyd.lims.lim.service.ReportConfig2ModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 报告组件配置关联关系操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Service
public class ReportConfig2ModuleServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportConfig2Module, String, ReportConfig2ModuleRepository> implements ReportConfig2ModuleService {

    private ReportModule2GroupTypeRepository reportModule2GroupTypeRepository;
    private RecordConfigRepository recordConfigRepository;
    private ReportModuleRepository reportModuleRepository;

    @Override
    public List<DtoReportConfig2Module> queryByRecordConfigId(String recordConfigId) {
        DtoRecordConfig recordConfig = recordConfigRepository.findOne(recordConfigId);
        if (StringUtil.isNotNull(recordConfig)) {
            List<DtoReportConfig2Module> reportConfig2ModuleList = repository.findByReportConfigId(recordConfig.getReportConfigId());
            if (StringUtil.isNotEmpty(reportConfig2ModuleList)) {
                List<String> moduleIdList = reportConfig2ModuleList.stream().map(DtoReportConfig2Module::getReportModuleId).distinct().collect(Collectors.toList());
                List<DtoReportModule> moduleList = reportModuleRepository.findAll(moduleIdList);
                Map<String, DtoReportModule> moduleMap = moduleList.stream().collect(Collectors.toMap(DtoReportModule::getId, dto -> dto));
                for (DtoReportConfig2Module config2Module : reportConfig2ModuleList) {
                    DtoReportModule module = moduleMap.get(config2Module.getReportModuleId());
                    if (StringUtil.isNotNull(module)) {
                        config2Module.setModuleCode(module.getModuleCode());
                        config2Module.setModuleName(module.getModuleName());
                        config2Module.setTableName(module.getTableName());
                        config2Module.setSourceTableName(module.getSourceTableName());
                    }
                }
                return reportConfig2ModuleList;
            }
        }
        return null;
    }

    @Override
    public DtoReportModule findModuleInfo(String id) {
        DtoReportConfig2Module reportConfig2Module = repository.findOne(id);
        if (StringUtil.isNotNull(reportConfig2Module)) {
            DtoReportModule module = reportModuleRepository.findOne(reportConfig2Module.getReportModuleId());
            if (StringUtil.isNotNull(module)) {
                //获取分页方式配置信息
                List<DtoReportModule2GroupType> reportModule2GroupTypeList = reportModule2GroupTypeRepository.findByReportConfigModuleId(id);
                module.setReportModule2GroupTypeList(reportModule2GroupTypeList);
                return module;
            }
        }
        return null;
    }

    /**
     * 新增报告和组件的关联关系
     *
     * @param entity 前端传递的实体
     * @return 新增的实体
     */
    @Override
    @Transactional
    public DtoReportConfig2Module save(DtoReportConfig2Module entity) {
        DtoRecordConfig recordConfig = recordConfigRepository.findOne(entity.getRecordConfigId());
        if (StringUtil.isNull(recordConfig)) {
            throw new BaseException("报告配置不存在！");
        }
        DtoReportConfig2Module config2Module = new DtoReportConfig2Module();
        config2Module.setReportConfigId(recordConfig.getReportConfigId());
        config2Module.setReportModuleId(entity.getReportModuleId());
        //新增报告组件关联关系
        DtoReportConfig2Module instConfig2Module = repository.save(config2Module);
        //新增分页方式配置
        List<DtoReportModule2GroupType> reportModule2GroupTypeList = entity.getReportModule2GroupTypeList();
        if (StringUtil.isNotEmpty(reportModule2GroupTypeList)) {
            for (DtoReportModule2GroupType reportModule2GroupType : reportModule2GroupTypeList) {
                reportModule2GroupType.setReportConfigModuleId(instConfig2Module.getId());
            }
            instConfig2Module.setReportModule2GroupTypeList(reportModule2GroupTypeList);
            reportModule2GroupTypeRepository.save(reportModule2GroupTypeList);
        }
        return instConfig2Module;
    }

    @Override
    @Transactional
    public DtoReportConfig2Module update(DtoReportConfig2Module entity) {
        DtoReportConfig2Module oldConfig2Module = repository.findOne(entity.getId());
        if (StringUtil.isNull(oldConfig2Module)) {
            throw new BaseException("报告组件关联关系不存在！");
        }
        oldConfig2Module.setReportModuleId(entity.getReportModuleId());
        repository.save(oldConfig2Module);
        //先删除旧的分页方式配置
        List<DtoReportModule2GroupType> oldGroupTypeList = reportModule2GroupTypeRepository.findByReportConfigModuleId(entity.getId());
        if (StringUtil.isNotEmpty(oldGroupTypeList)) {
            reportModule2GroupTypeRepository.delete(oldGroupTypeList);
        }
        //添加新的分页方式配置
        List<DtoReportModule2GroupType> newGroupTypeList = entity.getReportModule2GroupTypeList();
        if (StringUtil.isNotEmpty(newGroupTypeList)) {
            for (DtoReportModule2GroupType newGroupType : newGroupTypeList) {
                newGroupType.setReportConfigModuleId(entity.getId());
            }
            reportModule2GroupTypeRepository.save(newGroupTypeList);
            entity.setReportModule2GroupTypeList(newGroupTypeList);
        }
        return entity;
    }

    @Override
    @Transactional
    public int deleteConfig2Module(List<String> ids) {
        List<DtoReportConfig2Module> config2ModuleList = repository.findAll(ids);
        if (StringUtil.isNotEmpty(config2ModuleList)) {
            List<String> config2ModuleIdList = config2ModuleList.stream().map(DtoReportConfig2Module::getId).collect(Collectors.toList());
            //删除分页方式配置
            List<DtoReportModule2GroupType> module2GroupTypeList =  reportModule2GroupTypeRepository.findByReportConfigModuleIdIn(config2ModuleIdList);
            if (StringUtil.isNotEmpty(module2GroupTypeList)) {
                reportModule2GroupTypeRepository.delete(module2GroupTypeList);
            }
            //删除报告组件关联关系
            return repository.logicDeleteById(ids);
        }
        return 0;
    }

    @Override
    @Transactional
    public int deleteRecordConfigForReport(List<String> ids) {
        List<DtoRecordConfig> recordConfigList = recordConfigRepository.findAll(ids);
        if (StringUtil.isNotEmpty(recordConfigList)) {
            List<String> reportConfigIdList = recordConfigList.stream().map(DtoRecordConfig::getReportConfigId).distinct().collect(Collectors.toList());
            List<DtoReportConfig2Module> reportConfig2ModuleList = repository.findByReportConfigIdIn(reportConfigIdList);
            if (StringUtil.isNotEmpty(reportConfig2ModuleList)) {
                //删除报告和组件的关联关系
                repository.delete(reportConfig2ModuleList);
                List<String> reportConfig2ModuleIdList = reportConfig2ModuleList.stream().map(DtoReportConfig2Module::getId).collect(Collectors.toList());
                List<DtoReportModule2GroupType> reportModule2GroupTypeList = reportModule2GroupTypeRepository.findByReportConfigModuleIdIn(reportConfig2ModuleIdList);
                if (StringUtil.isNotEmpty(reportModule2GroupTypeList)) {
                    //删除组件分页方式配置信息
                    reportModule2GroupTypeRepository.delete(reportModule2GroupTypeList);
                }
            }
            //删除报告配置
            recordConfigRepository.delete(recordConfigList);
            return recordConfigList.size();
        }
        return 0;
    }

    @Autowired
    public void setReportModule2GroupTypeRepository(ReportModule2GroupTypeRepository reportModule2GroupTypeRepository) {
        this.reportModule2GroupTypeRepository = reportModule2GroupTypeRepository;
    }

    @Autowired
    public void setRecordConfigRepository(RecordConfigRepository recordConfigRepository) {
        this.recordConfigRepository = recordConfigRepository;
    }

    @Autowired
    public void setReportModuleRepository(ReportModuleRepository reportModuleRepository) {
        this.reportModuleRepository = reportModuleRepository;
    }
}