package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoFolderPeriodWWInfo;

import java.util.List;

/**
 * FolderPeriodWWInfo数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/7/7
 * @since V100R001
 */
public interface FolderPeriodWWInfoRepository extends IBaseJpaPhysicalDeleteRepository<DtoFolderPeriodWWInfo, String> {

    /**
     * 下发任务信息
     *
     * @param folderId    点位id
     * @param periodCount 周期
     * @return 任务信息
     */
    List<DtoFolderPeriodWWInfo> findByFolderIdAndPeriodCountOrderByIssueTimeDesc(String folderId, Integer periodCount);
}
