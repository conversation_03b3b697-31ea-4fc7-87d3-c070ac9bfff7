package com.sinoyd.lims.monitor.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.FixedPointExpendService;
import com.sinoyd.lims.monitor.criteria.FixedPointExpendCriteria;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPointExpend;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FixedPointExpend服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: FixedPointExpend服务")
 @RestController
 @RequestMapping("api/monitor/fixedPointExpend")
 public class FixedPointExpendController extends BaseJpaController<DtoFixedPointExpend, String,FixedPointExpendService> {


    /**
     * 分页动态条件查询FixedPointExpend
     * @param fixedPointExpendCriteria 条件参数
     * @return RestResponse<List<FixedPointExpend>>
     */
     @ApiOperation(value = "分页动态条件查询FixedPointExpend", notes = "分页动态条件查询FixedPointExpend")
     @GetMapping
     public RestResponse<List<DtoFixedPointExpend>> findByPage(FixedPointExpendCriteria fixedPointExpendCriteria) {
         PageBean<DtoFixedPointExpend> pageBean = super.getPageBean();
         RestResponse<List<DtoFixedPointExpend>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fixedPointExpendCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FixedPointExpend
     * @param id 主键id
     * @return RestResponse<DtoFixedPointExpend>
     */
     @ApiOperation(value = "按主键查询FixedPointExpend", notes = "按主键查询FixedPointExpend")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFixedPointExpend> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFixedPointExpend> restResponse = new RestResponse<>();
         DtoFixedPointExpend fixedPointExpend = service.findOne(id);
         restResponse.setData(fixedPointExpend);
         restResponse.setRestStatus(StringUtil.isNull(fixedPointExpend) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FixedPointExpend
     * @param fixedPointExpend 实体列表
     * @return RestResponse<DtoFixedPointExpend>
     */
     @ApiOperation(value = "新增FixedPointExpend", notes = "新增FixedPointExpend")
     @PostMapping
     public RestResponse<DtoFixedPointExpend> create(@RequestBody @Validated DtoFixedPointExpend fixedPointExpend) {
         RestResponse<DtoFixedPointExpend> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fixedPointExpend));
         return restResponse;
      }

     /**
     * 新增FixedPointExpend
     * @param fixedPointExpend 实体列表
     * @return RestResponse<DtoFixedPointExpend>
     */
     @ApiOperation(value = "修改FixedPointExpend", notes = "修改FixedPointExpend")
     @PutMapping
     public RestResponse<DtoFixedPointExpend> update(@RequestBody @Validated DtoFixedPointExpend fixedPointExpend) {
         RestResponse<DtoFixedPointExpend> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fixedPointExpend));
         return restResponse;
      }

    /**
     * "根据id批量删除FixedPointExpend
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FixedPointExpend", notes = "根据id批量删除FixedPointExpend")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }