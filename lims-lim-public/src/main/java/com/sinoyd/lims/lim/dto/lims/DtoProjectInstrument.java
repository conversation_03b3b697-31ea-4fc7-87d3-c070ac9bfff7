package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.lims.lim.entity.ProjectInstrument;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * DtoProjectInstrument实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ProjectInstrument")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoProjectInstrument extends ProjectInstrument {
    private static final long serialVersionUID = 1L;



    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 仪器设备名称及型号
     */
    @Transient
    private String instrumentNameCode;

    /**
     * 仪器id列表
     */
    @Transient
    private List<String> instrumentIds;

    /**
     * 仪器对象列表
     */
    @Transient
    private List<DtoInstrument> instrumentList;

    /**
     * 出入库明细对象列表
     */
    @Transient
    private List<DtoProjectInstrumentDetails> detailsList;

    /**
     * 已入库仪器数量
     */
    @Transient
    private Integer inCount;


    /**
     * 仪器出库数量
     */
    @Transient
    private Integer outCount;

    /**
     * 是否入库完成
     */
    @Transient
    private Boolean isInComplete;

    /**
     * 项目id列表
     */
    @Transient
    private List<String> projectIdList;

    /**
     * 项目名称列表
     */
    @Transient
    private List<String> projectNameList;

    /**
     * 项目信息列表
     */
    @Transient
    private List<Map<String, String>> projectList;

    public DtoProjectInstrument() {
    }

    public DtoProjectInstrument(String id, Date useDate, String projectId, String projectName, Integer outQualified, Integer intQualified,
                                String useNames, String administratorName,Date createDate) {
        setId(id);
        setUseDate(useDate);
        setProjectId(projectId);
        setProjectName(projectName);
        setOutQualified(outQualified);
        setIntQualified(intQualified);
        setUserNames(useNames);
        setAdministratorName(administratorName);
        setCreateDate(createDate);
    }
}