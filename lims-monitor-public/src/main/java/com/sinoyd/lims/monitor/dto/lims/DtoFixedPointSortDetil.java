package com.sinoyd.lims.monitor.dto.lims;

import com.sinoyd.lims.monitor.entity.FixedPointSortDetil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoFixedPointSortDetil实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_FixedPointSortDetil")
@Data
@DynamicInsert
public class DtoFixedPointSortDetil extends FixedPointSortDetil {
    private static final long serialVersionUID = 1L;

    /**
     * 接收的点位信息id集合
     */
    @Transient // 此注解标识为非数据库表字段
    private List<String> fixedPointIds;


}