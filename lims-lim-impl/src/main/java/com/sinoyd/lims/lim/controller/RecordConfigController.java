package com.sinoyd.lims.lim.controller;

import com.sinoyd.lims.lim.criteria.ParamsConfigCriteria;
import com.sinoyd.lims.lim.criteria.RecordConfigCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.service.RecordConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * RecordConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
 @Api(tags = "示例: RecordConfig服务")
 @RestController
 @RequestMapping("api/lim/recordConfig")
 @Validated
 public class RecordConfigController extends BaseJpaController<DtoRecordConfig, String,RecordConfigService> {


    /**
     * 分页动态条件查询RecordConfig
     *
     * @param recordConfigCriteria 条件参数
     * @return RestResponse<List < RecordConfig>>
     */
    @ApiOperation(value = "分页动态条件查询RecordConfig", notes = "分页动态条件查询RecordConfig")
    @GetMapping
    public RestResponse<List<DtoRecordConfig>> findByPage(RecordConfigCriteria recordConfigCriteria) {
        PageBean<DtoRecordConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoRecordConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, recordConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询RecordConfig
     *
     * @param id 主键id
     * @return RestResponse<DtoRecordConfig>
     */
    @ApiOperation(value = "按主键查询RecordConfig", notes = "按主键查询RecordConfig")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoRecordConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoRecordConfig> restResponse = new RestResponse<>();
        DtoRecordConfig recordConfig = service.findOne(id);
        restResponse.setData(recordConfig);
        restResponse.setRestStatus(StringUtil.isNull(recordConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 根据采样单配置id查询检测类型下参数信息
     *
     * @param paramsCriteria 主键id
     * @return RestResponse<DtoRecordConfig>
     */
    @ApiOperation(value = "根据采样单配置id查询检测类型下参数信息", notes = "按主键查询RecordConfig")
    @GetMapping(path = "/paramsConfig")
    public RestResponse<List<DtoParamsConfig>> findParamsConfig(ParamsConfigCriteria paramsCriteria) {
        RestResponse<List<DtoParamsConfig>> restResponse = new RestResponse<>();
        PageBean<DtoParamsConfig> pageBean = super.getPageBean();
        service.findParamsConfigByRecordId(pageBean, paramsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());

        return restResponse;
    }

    /**
     * 新增RecordConfig
     *
     * @param recordConfig 实体列表
     * @return RestResponse<DtoRecordConfig>
     */
    @ApiOperation(value = "新增RecordConfig", notes = "新增RecordConfig")
    @PostMapping
    public RestResponse<DtoRecordConfig> create(@Validated @RequestBody DtoRecordConfig recordConfig) {
        RestResponse<DtoRecordConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.save(recordConfig));
        return restResponse;
    }

    /**
     * 修改RecordConfig
     *
     * @param recordConfig 实体列表
     * @return RestResponse<DtoRecordConfig>
     */
    @ApiOperation(value = "修改RecordConfig", notes = "修改RecordConfig")
    @PutMapping
    public RestResponse<DtoRecordConfig> update(@Validated @RequestBody DtoRecordConfig recordConfig) {
        RestResponse<DtoRecordConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.update(recordConfig));
        return restResponse;
    }

    /**
     * "根据id批量删除RecordConfig
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除RecordConfig", notes = "根据id批量删除RecordConfig")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 复制记录单配置
     *
     * @param recordConfig 数据
     * @return RestResponse<Void>
     */
    @PostMapping("/copy")
    public RestResponse<Void> copy(@Validated @RequestBody DtoRecordConfig recordConfig) {
        RestResponse<Void> response = new RestResponse<>();
        service.copyRecordConfig(recordConfig);
        return response;
    }
}