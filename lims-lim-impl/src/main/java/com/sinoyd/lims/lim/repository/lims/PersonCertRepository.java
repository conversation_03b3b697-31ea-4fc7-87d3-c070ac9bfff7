package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 人员证书管理仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/6
 * @since V100R001
 */
public interface PersonCertRepository extends IBaseJpaPhysicalDeleteRepository<DtoPersonCert, String> {


    /**
     * 获取人员相关证书
     *
     * @param personIds 测试项目id数组
     * @return 返回集合
     */
    @Query("select p from DtoPersonCert p where p.personId in :personIds")
    List<DtoPersonCert> findByPersonIds(@Param("personIds") List<String> personIds);

    /**
     * 根据人员Id查询证书结果
     *
     * @param personId
     * @return 返回证书结果集
     */
    List<DtoPersonCert> findByPersonId(String personId);

    /**
     * 删除关联数据
     *
     * @param personId 人员id
     * @return 返回删除数量
     */
    @Transactional
    @Modifying
    @Query("delete from DtoPersonCert p where p.personId = :personId ")
    Integer deleteByPersonId(@Param("personId") String personId);

    /**
     * 根据编码和id查询是否存在证书
     *
     * @param certCode 证书编码
     * @param id       主键id
     * @return 数量
     */
    @Query("select count(p) from DtoPersonCert p where p.id <> :id and p.certCode = :certCode")
    Integer getCountByCertCode(@Param("certCode") String certCode, @Param("id") String id);

}