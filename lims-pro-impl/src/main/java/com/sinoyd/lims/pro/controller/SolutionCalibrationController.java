package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoSolutionCalibration;
import com.sinoyd.lims.pro.dto.DtoSolutionCalibrationRecord;
import com.sinoyd.lims.pro.service.SolutionCalibrationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * SamplingCarConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
@Api(tags = "SolutionCalibrationController")
@RestController
@RequestMapping("api/pro/solutionCalibration")
public class SolutionCalibrationController extends BaseJpaController<DtoSolutionCalibration, String, SolutionCalibrationService> {

    /**
     * 新增溶液标定
     * @param entity 溶液标定实体
     * @return RestResponse<DtoSolutionCalibration>
     */
    @ApiOperation(value = "新增溶液标定", notes = "新增溶液标定")
    @PostMapping
    public RestResponse<DtoSolutionCalibration> add(@RequestBody @Validated DtoSolutionCalibration entity) {
        RestResponse<DtoSolutionCalibration> restResponse = new RestResponse<>();
        restResponse.setData(service.save(entity));
        return restResponse;
    }

    /**
     * 自动计算
     * @param entity 溶液标定实体
     * @return RestResponse<DtoSolutionCalibration>
     */
    @ApiOperation(value = "自动计算", notes = "自动计算")
    @PostMapping("/automaticCalculate")
    public RestResponse<DtoSolutionCalibrationRecord> automaticCalculate(@RequestBody DtoSolutionCalibrationRecord entity) {
        RestResponse<DtoSolutionCalibrationRecord> restResponse = new RestResponse<>();
        restResponse.setData(service.automaticCalculate(entity));
        return restResponse;
    }

    /**
     * 计算
     * @param entity 溶液标定实体
     * @return RestResponse<DtoSolutionCalibration>
     */
    @ApiOperation(value = "计算", notes = "计算")
    @PostMapping("/calculate")
    public RestResponse<DtoSolutionCalibration> calculate(@RequestBody DtoSolutionCalibration entity) {
        RestResponse<DtoSolutionCalibration> restResponse = new RestResponse<>();
        restResponse.setData(service.calculate(entity));
        return restResponse;
    }

    /**
     * 数据计算
     * @param workSheetFolderId 检测单标识
     * @return RestResponse<DtoSolutionCalibration>
     */
    @ApiOperation(value = "数据查看", notes = "数据查看")
    @GetMapping("/{workSheetFolderId}")
    public RestResponse<DtoSolutionCalibration> find(@PathVariable String workSheetFolderId) {
        RestResponse<DtoSolutionCalibration> restResponse = new RestResponse<>();
        restResponse.setData(service.findByWorkSheetFolderId(workSheetFolderId));
        return restResponse;
    }

    /**
     * 修改保存
     * @param entity 溶液标定实体
     * @return RestResponse<DtoSolutionCalibration>
     */
    @ApiOperation(value = "修改保存", notes = "修改保存")
    @PutMapping
    public RestResponse<DtoSolutionCalibration> update(@RequestBody @Validated DtoSolutionCalibration entity) {
        RestResponse<DtoSolutionCalibration> restResponse = new RestResponse<>();
        restResponse.setData(service.save(entity));
        return restResponse;
    }
}
