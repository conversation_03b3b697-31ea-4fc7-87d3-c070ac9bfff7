package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * ProjectInstrument数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
public interface ProjectInstrumentRepository extends IBaseJpaRepository<DtoProjectInstrument, String> {

    /**
     * 根据项目id查询仪器使用记录
     *
     * @param projectIds 项目id
     * @return List<DtoProjectInstrument>
     */
    List<DtoProjectInstrument> findByProjectIdIn(List<String> projectIds);

}