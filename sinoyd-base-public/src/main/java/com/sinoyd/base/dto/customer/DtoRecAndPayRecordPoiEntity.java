package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 收款记录导入导出  poi实体
 * <AUTHOR>
 * @version V1.0.0 2023/11/30
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DtoRecAndPayRecordPoiEntity extends PoiBaseEntity{
    /**
     * 序号
     */
    @Excel(name = "序号",orderNum = "1",width = 20)
    private Integer index;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号",orderNum = "20",width = 40)
    private String contractCode;

    /**
     * 合同名称
     */
    @Excel(name = "合同名称",orderNum = "30",width = 40)
    private String contractName;

    /**
     * 甲方名称
     */
    @Excel(name = "甲方名称",orderNum = "40",width = 40)
    private String firstEntName;

    /**
     * 业务员
     */
    @Excel(name = "业务员",orderNum = "50",width = 40)
    private String salesPersonName;

    /**
     * 收款项
     */
    @Excel(name = "收款项",orderNum = "60",width = 40)
    private String collectItemStr;

    /**
     * 开票日期
     */
    @Excel(name = "开票日期",orderNum = "70",width = 40)
    private String invoiceDateStr;

    /**
     * 开票金额
     */
    @Excel(name = "开票金额",orderNum = "80",width = 40)
    private String invoiceAmountStr;

    /**
     * 到款日期
     */
    @Excel(name = "到款日期",orderNum = "90",width = 40)
    private String receiveDateStr;

    /**
     * 类型
     */
    @Excel(name = "类型",orderNum = "100",width = 40)
    private String moneyTypeStr;
}
