package com.sinoyd.lims.pro.strategy.strategy.dataValidator;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.sinoyd.frame.base.util.DateUtil.*;

/**
 * 日期时间校验
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/09/23
 */
@Component(IFileNameConstant.DataValidateStrategyKey.DATE_TIME_VALIDATE)
public class DateTimeValidator extends AbsDataValidator {

    private static final List<String> dateFormatList = Arrays.asList(FULL, YEAR, NO_SECOND_C, YEAR_ZH_CN, FULL_NO_SECOND,
            FULL_NO_MINUTE_ZH_CN2, FULL_SPRIT, FULL_ZH_CN2, FULL_ZH_CN, FULL_NO_MINUTE_SPRIT, TIME_ZH_CN,
            FULL_NO_SECOND_ZH_CN2, YEAR_NO_MONTH, FULL_NO_SECOND_SPRIT, FULL_NO_SECOND_ZH_CN, FULL_NO_MINUTE_ZH_CN, FULL_NO_MINUTE,
            TIME, YEAR_NO_SPRIT, YEAR_SPRIT, "yyyy.MM.dd");

    @Override
    public Boolean validate(Object value, Map<String, Object> map) {
        String valStr = StringUtil.isNotNull(value) ? value.toString() : "";
        if (StringUtil.isEmpty(valStr)) {
            return true;
        }
        return checkDateFormat(valStr);
    }

    /**
     * 校验是否日期格式
     *
     * @param dateStr 被校验的字符串
     * @return 校验结果
     */
    private boolean checkDateFormat(String dateStr) {
        for (String dateFormat : dateFormatList) {
            Date date = stringToDate(dateStr, dateFormat);
            if (StringUtil.isNotNull(date)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Integer getControlType() {
        return 7;
    }
}
