package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 指标删除数据结构
 * <AUTHOR>
 * @version V1.0.0 2020/6/13
 * @since V100R001
 */
@Data
public class DtoAnalyseDataDelete {

    public DtoAnalyseDataDelete(){

    }

    /**
     * 构造函数
     * @param projectId 项目id
     * @param sampleList 样品数据
     * @param isCheckSample 是否核对样品
     */
    public DtoAnalyseDataDelete(String projectId,List<DtoSample>sampleList,Boolean isCheckSample) {
        this.projectId = projectId;
        this.sample = sampleList.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())).collect(Collectors.toList());
        this.qcSample = sampleList.stream().filter(p -> !p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())).collect(Collectors.toList());
        this.isCheckSample = isCheckSample;
    }

    /**
     * 构造函数
     * @param projectId 项目id
     * @param sampleList 样品数据
     * @param testIds 测试项目id
     * @param analyseItemIds 分析项目id
     * @param isCheckSample 是否核对样品
     */
    public DtoAnalyseDataDelete(String projectId,List<DtoSample>sampleList,List<String>testIds,List<String>analyseItemIds,Boolean isCheckSample) {
        this.projectId = projectId;
        this.sample = sampleList.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())).collect(Collectors.toList());
        this.qcSample = sampleList.stream().filter(p -> !p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())).collect(Collectors.toList());
        this.testIds = testIds;
        this.analyseItemIds = analyseItemIds;
        this.isCheckSample = isCheckSample;
    }

    /**
     * 项目id
     */
    private String projectId = UUIDHelper.GUID_EMPTY;

    /**
     * 样品id--接收参数
     */
    private String sampleId;

    /**
     * 样品
     */
    private List<DtoSample> sample = new ArrayList<>();

    /**
     * 质控样品
     */
    private List<DtoSample> qcSample = new ArrayList<>();

    /**
     * 删除的测试项目id集合
     */
    List<String> testIds = new ArrayList<>();

    /**
     * 删除的分析项目id集合
     */
    List<String> analyseItemIds = new ArrayList<>();

    /**
     * 是否纠正状态
     */
    private Boolean isCheckSample = true;

    /**
     * 是否删除关联样测试项目
     */
    private Boolean isDeleteAssociateTest = true;

    /**
     * 是否删除样品（即所有指标均删除）
     */
    private Boolean isDeleteSample = false;

    /**
     * 获取所有样品id集合
     *
     * @return 样品id集合
     */
    public List<String> getAllSampleIds() {
        List<String> sampleIds = new ArrayList<>();
        sampleIds.addAll(this.sample.stream().map(DtoSample::getId).collect(Collectors.toList()));
        sampleIds.addAll(this.qcSample.stream().map(DtoSample::getId).collect(Collectors.toList()));
        return sampleIds;
    }

    /**
     * 获取所有样品集合
     *
     * @return 样品集合
     */
    public List<DtoSample> getAllSamples() {
        List<DtoSample> samples = new ArrayList<>();
        samples.addAll(this.sample);
        samples.addAll(this.qcSample);
        return samples;
    }

    /**
     * 纳入质控样
     *
     * @param qcSamples 质控样集合
     */
    public void addQCSamples(List<DtoSample> qcSamples) {
        for (DtoSample sample : qcSamples) {
            if (qcSample.stream().noneMatch(p -> p.getId().equals(sample.getId()))) {
                this.qcSample.add(sample);
            }
        }
    }

    /**
     * 移除室内质控样
     * @param sampleIds 需移除的质控样id
     */
    public void removeInnerSampleId(List<String> sampleIds) {
        this.qcSample = this.qcSample.stream().filter(p -> !sampleIds.contains(p.getId())).collect(Collectors.toList());
    }
}
