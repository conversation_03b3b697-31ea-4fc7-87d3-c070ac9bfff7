-- 质控分析记录表（项目进度）
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('fb7d5216-cc4a-4d41-aa92-8e754e118cb5', '8c57c06a-c1f2-41e5-a5f2-f5579b336fac', 'ProjectInquiry', '项目进度',
        'QualityAnalyzeStatistic', '质控分析记录表', 1, 0, 1, '', '项目进度:质控信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-08-13 14:01:47', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-08-13 14:01:47', NULL);

-- 修改旧消耗品领用记录下拉框生成
UPDATE TB_LIM_ReportApply SET name = '消耗品领用记录', type = 1 WHERE id = '14545753-fd21-4e2e-9c63-4f2393ba704a';


-- 消耗品领用记录总计
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('69a24bd5-90c2-41a6-8ab2-44fbe3c7918d', '0c658088-6230-44e6-91df-706bc0f31b23', 'ConsumableManage', '消耗品管理',
        'ConsumableLogTotal', '消耗品领用记录总计', 1, 0, 1, '', '消耗品管理:消耗品领用记录', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-08-13 16:51:28', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-08-13 16:51:28', NULL);