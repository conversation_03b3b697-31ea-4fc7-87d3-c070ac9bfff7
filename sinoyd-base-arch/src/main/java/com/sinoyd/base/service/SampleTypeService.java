package com.sinoyd.base.service;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.frame.service.IBaseJpaService;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 样品类型接口（样品类型模板接口）
 *
 * <AUTHOR>
 * @version V1.0.0 2018/12/14
 * @since V100R001
 */
public interface SampleTypeService extends IBaseJpaService<DtoSampleType, String> {

    /**
     * 根据检测类型小类获取检测类型大类
     *
     * @param id id
     * @return 检测类型大类实体
     */
    DtoSampleType getBigSampleType(String id);

    /**
     * 获取所有的大类信息
     *
     * @return 返回大类实体
     */
    List<DtoSampleType> findAllBigSampleType();

    /**
     * 根据行业类型id获取检测类型树
     *
     * @param industryId 行业类型id
     */
    List<TreeNode> getSampleTypeListByIndustryId(String industryId);


    /**
     * 获取所有redis下的检测类型
     *
     * @param ids 检测类型ids
     * @return 返回redis下面的检测类型数据
     */
    List<DtoSampleType> findRedisByIds(List<String> ids);

    /**
     * 返回父类下所有的子类检测类型
     *
     * @param parentId 父类id
     * @return 返回父类下所有的子类检测类型
     */
    List<DtoSampleType> findByParentId(String parentId);

    /**
     * 返回父类下所有的子类检测类型
     *
     * @param parentIds 父类ids
     * @return 返回父类下所有的子类检测类型
     */
    List<DtoSampleType> findByParentIds(Collection<String> parentIds);

    /**
     * 根据用户Id获取有假删的数据
     *
     * @param ids 主键ids
     * @return 返回带有假删的数据
     */
    List<DtoSampleType> findAllDeleted(List<String> ids);


    /**
     * 含假删的信息
     *
     * @return 返回假删信息
     */
    List<DtoSampleType> findAllDeleted();

    /**
     * 保存相应的redis数据
     *
     * @param item 检测类型的实体对象
     */
    void saveRedis(DtoSampleType item);

    /**
     * 根据行业类型id加载对应的检测类型
     *
     * @param industryId 行业类型id
     * @return 检测类型id
     */
    List<String> loadSampleTypeIds(String industryId);


    /**
     * 查询所有未删除的检测类型(排除检测模板)
     *
     * @return 所有未删除的检测类型
     */
    List<DtoSampleType> findSampleType();
}