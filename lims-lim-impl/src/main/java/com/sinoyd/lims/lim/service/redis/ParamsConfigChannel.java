package com.sinoyd.lims.lim.service.redis;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.repository.rcc.ParamsConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 参数配置接收通道
 * <AUTHOR>
 * @version V1.0.0 2019/11/28
 * @since V100R001
 */
@Component
@Service
public class ParamsConfigChannel {

    @Autowired
    private ParamsConfigRepository paramsConfigRepository;
    @Async
    @Transactional
    public void updateDimension(String dimensionJson) {
        TypeLiteral<DtoDimension> typeLiteral = new TypeLiteral<DtoDimension>() {
        };

        if (StringUtils.isNotNullAndEmpty(dimensionJson)) {

            DtoDimension dtoDimension = JsonIterator.deserialize(JsonIterator.deserialize(dimensionJson).toString(), typeLiteral);
            if (StringUtil.isNotNull(dtoDimension)) {
                List<DtoParamsConfig> dtoParamsConfigs = paramsConfigRepository.findByDimensionId(dtoDimension.getId());
                //需要修改的参数集合
                List<String> updateIds = new ArrayList<>();
                for (DtoParamsConfig dtoParamsConfig : dtoParamsConfigs) {
                    String dimension = dtoParamsConfig.getDimension();
                    //不相等说明需要修改
                    if (!dimension.equals(dtoDimension.getDimensionName())) {
                        updateIds.add(dtoParamsConfig.getId());
                    }
                }
                if (updateIds.size() > 0) {
                    String dimension = dtoDimension.getDimensionName();
                    //利用repository的方法进行批量修改数据
                    paramsConfigRepository.updateDimension(updateIds, dimension);
                }
            }
        }
    }
}
