package com.sinoyd.lims.lim.service;

import com.sinoyd.lims.lim.dto.lims.DtoTest;

/**
 * 数据修约工具
 * <AUTHOR>
 * @version ：v1.0.0
 * @date ：2021/10/9
 */
public interface ReviseService {

    /**
     * 数据修约
     *
     * @param mostSignificance 有效位数
     * @param mostDecimal      小数位数
     * @param value            值
     * @return 返回修约值
     */
    String getDecimal(Integer mostSignificance, Integer mostDecimal, String value);

    /**
     * 数据修约
     *
     * @param mostSignificance 有效位数
     * @param mostDecimal      小数位数
     * @param value            值
     * @param isSci            是否强制科学计数法
     * @return 返回修约值
     */
    String getDecimal(Integer mostSignificance, Integer mostDecimal, String value, Boolean isSci);


    /**
     * 根据测试项目，检测类型id，值进行修约
     *
     * @param testId       测试项目id
     * @param sampleTypeId 检测类型id
     * @param value        值
     * @return 返回修约值
     */
    String getDecimal(String testId, String sampleTypeId, String value);


    /**
     * 根据测试项目，检测类型id，值进行修约
     *
     * @param test         测试项目信息
     * @param sampleTypeId 检测类型id
     * @param value        值
     * @return 返回修约值
     */
    String getDecimal(DtoTest test, String sampleTypeId, String value);

    /**
     * 根据测试项目id进行数据修约
     *
     * @param testId 测试项目id
     * @param value  修约值
     * @return 返回修约值
     */
    String getDecimal(String testId, String value);

    /**
     * 根据测试项目进行数据修约
     *
     * @param test  测试项目信息
     * @param value 修约值
     * @return 返回修约值
     */
    String getDecimal(DtoTest test, String value);

    /**
     * 根据配置判断是否需要转换为科学计数法
     *
     * @param value 数值的Sting形式
     * @return 格式化后的值
     */
    String formatSci(String value);

    /**
     * 根据配置判断是否需要转换为科学计数法
     *
     * @param value        数值的Sting形式
     * @param digit        有效位
     * @param decimalDigit 小数位
     * @return 格式化后的值
     */
    String formatSci(String value, Integer digit, Integer decimalDigit);

    /**
     * 判断是否科学计数法
     *
     * @param valueStr 数字的字符形式
     * @return true: 是科学计数法，false: 非科学计数法
     */
    boolean isSci(String valueStr);

    /**
     * 科学计数法转普通数字
     *
     * @param sciStr 科学计数法字符串形式
     * @return 数字字符串形式
     */
    String sci2Number(String sciStr);
}
