package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.SamplePreparationCriteria;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSamplePreparation;
import com.sinoyd.lims.pro.dto.customer.DtoSampleOfPrepared;
import com.sinoyd.lims.pro.service.SamplePreparationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SamplePreparation服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2023/3/21
 * @since V100R001
 */
@Api(tags = "示例: SamplePreparation服务")
@RestController
@RequestMapping("api/pro/SamplePreparation")
public class SamplePreparationController extends BaseJpaController<DtoSamplePreparation,String, SamplePreparationService> {

    /**
     * 分页动态条件查询需要制备的样品
     *
     * @param samplePreparationCriteria 条件参数
     * @return 分页数据
     */
    @ApiOperation(value = "分页动态条件查询需要制备的样品", notes = "分页动态条件查询需要制备的样品")
    @GetMapping
    public RestResponse<List<DtoSample>> findSampleByPage(SamplePreparationCriteria samplePreparationCriteria){
        PageBean<DtoSample> pageBean = super.getPageBean();
        RestResponse<List<DtoSample>> restResponse = new RestResponse<>();
        service.findSamplesByPage(pageBean, samplePreparationCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 批量保存样品制备信息
     *
     * @param samplePreparations 样品制备信息
     * @return 保存后的样品制备数据
     */
    @ApiOperation(value = "批量保存样品制备信息",notes = "批量保存样品制备信息")
    @PostMapping("/batch")
    public RestResponse<List<DtoSamplePreparation>> batchSave(@RequestBody List<DtoSamplePreparation> samplePreparations){
        RestResponse<List<DtoSamplePreparation>> restResponse = new RestResponse<>();
        restResponse.setData(service.batchSave(samplePreparations));
        restResponse.setMsg("保存成功");
        return restResponse;
    }

    /**
     * 保存样品制备信息
     *
     * @param sampleOfPrepared 样品制备数据
     * @return 保存后的样品制备数据
     */
    @ApiOperation(value = "保存样品制备信息",notes = "保存样品制备信息")
    @PostMapping
    public RestResponse<List<DtoSamplePreparation>> save(@RequestBody DtoSampleOfPrepared sampleOfPrepared){
        RestResponse<List<DtoSamplePreparation>> restResponse = new RestResponse<>();
        restResponse.setData(service.save(sampleOfPrepared.getSampleId(), sampleOfPrepared.getSamplePreparations()));
        return restResponse;
    }

    /**
     * 样品完成制备
     *
     * @param sampleIds 需要制备完成的样品id集合
     * @return RestResponse<String>
     */
    @ApiOperation(value = "保存样品制备信息",notes = "保存样品制备信息")
    @PostMapping("/complete")
    public RestResponse<String> preparationSample(@RequestBody List<String> sampleIds){
        RestResponse<String> restResponse = new RestResponse<>();
        service.preparationSample(sampleIds);
        restResponse.setMsg("制备完成");
        return restResponse;
    }

    /**
     * 删除制备数据
     *
     * @param samplePreparedId 制备数据id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "删除制备数据", notes = "删除制备数据")
    @DeleteMapping("/{samplePreparedId}")
    public RestResponse<Integer> findSampleByPage(@PathVariable String samplePreparedId){
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(samplePreparedId));
        restResponse.setMsg("删除成功");
        return restResponse;
    }

    /**
     * 查询样品详细数据
     *
     * @param sampleId 样品id
     * @return 样品详细数据
     */
    @ApiOperation(value = "保存样品制备信息",notes = "保存样品制备信息")
    @GetMapping("/{sampleId}")
    public RestResponse<DtoSampleOfPrepared> findDetails(@PathVariable(name = "sampleId") String sampleId){
        RestResponse<DtoSampleOfPrepared> restResponse = new RestResponse<>();
        restResponse.setData(service.findDetails(sampleId));
        restResponse.setMsg("操作成功");
        return restResponse;
    }

    /**
     * 退回
     *
     * @param sampleIds 需要退回的样品id集合
     * @return RestResponse<String>
     */
    @ApiOperation(value = "退回",notes = "退回")
    @PostMapping("/back")
    public RestResponse<String> backPreparation(@RequestBody List<String> sampleIds){
        RestResponse<String> restResponse = new RestResponse<>();
        service.backPreparation(sampleIds);
        restResponse.setMsg("退回成功");
        return restResponse;
    }
}
