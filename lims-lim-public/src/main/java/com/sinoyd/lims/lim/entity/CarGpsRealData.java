package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * CarGpsRealData实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="CarGpsRealData")
 @Data
 public  class CarGpsRealData implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  CarGpsRealData() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 车辆id（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("车辆id（Guid）")
	private String carId;
    
    /**
    * 车辆行驶时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("车辆行驶时间")
    private Date carDate;
    
    /**
    * 纬度
    */
    @Column(length=50)
    @ApiModelProperty("纬度")
    @Length(message = "纬度{validation.message.length}", max = 50)
    private String lat;
    
    /**
    * 经度
    */
    @Column(length=50)
    @ApiModelProperty("经度")
    @Length(message = "经度{validation.message.length}", max = 50)
    private String lon;
    
    /**
    * 速度
    */
    @Column(length=50)
    @ApiModelProperty("速度")
    @Length(message = "速度{validation.message.length}", max = 50)
    private String speed;
    
    /**
    * GPS精度
    */
    @Column(length=50)
    @ApiModelProperty("GPS精度")
    @Length(message = "GPS精度{validation.message.length}", max = 50)
    private String precision;
    
    /**
    * 运行方向
    */
    @Column(length=50)
    @ApiModelProperty("运行方向")
    @Length(message = "运行方向{validation.message.length}", max = 50)
    private String direction;
    
    /**
    * 网络强度
    */
    @Column(length=50)
    @ApiModelProperty("网络强度")
    @Length(message = "网络强度{validation.message.length}", max = 50)
    private String sigWatch;
    
    /**
    * 运营商
    */
    @Column(length=50)
    @ApiModelProperty("运营商")
    @Length(message = "运营商{validation.message.length}", max = 50)
    private String operator;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }