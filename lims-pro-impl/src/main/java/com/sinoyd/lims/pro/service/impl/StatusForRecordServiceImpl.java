package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord;
import com.sinoyd.lims.pro.dto.DtoStatusForRecord;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumReceiveInfoStatus;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumStatus;
import com.sinoyd.lims.pro.repository.StatusForRecordRepository;
import com.sinoyd.lims.pro.service.ReceiveSubSampleRecordService;
import com.sinoyd.lims.pro.service.StatusForRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 送样单状态操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
@Service
public class StatusForRecordServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoStatusForRecord, String, StatusForRecordRepository> implements StatusForRecordService {

    @Autowired
    private StatusForRecordRepository statusForRecordRepository;

    @Autowired
    @Lazy
    private ReceiveSubSampleRecordService receiveSubSampleRecordService;

    /**
     * 创建状态
     *
     * @param receiveId 送样单id
     * @param module    模块编码
     */
    @Transactional
    @Override
    public void createStatus(String receiveId, String module) {
        DtoStatusForRecord status = new DtoStatusForRecord();
        status.setModule(module);
        status.setReceiveId(receiveId);
        status.setStatus(EnumStatus.待处理.getValue());
        repository.save(status);
    }

    /**
     * 状态置为已完成
     *
     * @param receiveId 送样单id
     * @param module    模块编码
     */
    @Transactional
    @Override
    public void completeStatus(String receiveId, String module) {
        DtoStatusForRecord status = statusForRecordRepository.findByReceiveIdAndModule(receiveId, module);
        if (StringUtil.isNotNull(status)) {
            status.setStatus(EnumStatus.已处理.getValue());
            comRepository.merge(status);
        }
    }

    /**
     * 修改状态数据
     *
     * @param record       处理后的送样单实体
     * @param nextPersonId 下一步操作人id
     * @param nextPerson   下一步操作人
     * @param opinion      意见
     */
    @Transactional
    @Override
    public void modifyStatus(DtoReceiveSampleRecord record, String nextPersonId, String nextPerson, String opinion) {
        /*因为这边不涉及工作流调用，故这边进行所有的逻辑判断
        目前涉及5个模块，委托现场送样、现场数据录入、现场数据复核、现场数据审核、样品交接
        委托现场送样->现场数据录入->现场数据复核->现场数据审核；复核、审核->录入
        现场数据录入->样品交接
        项目登记提交触发样品交接

        其中，项目登记提交触发样品交接调用createStatus，不在此范围内，故可见委托现场送样和样品交接不存在被回退和回退的可能性
        即这两个模块仅存在待处理，触发提交至已处理，不存在被回退到待处理或被删除的可能性
        涉及流转的仅为现场数据录入、现场数据复核、现场数据审核，用infoStatus来统一判断状态
        */

        List<DtoStatusForRecord> statusList = statusForRecordRepository.findByReceiveId(record.getId());
        //将list转为map
        Map<String, DtoStatusForRecord> statusMap = statusList.stream().collect(Collectors.toMap(DtoStatusForRecord::getModule, status -> status));

        if (record.getInfoStatus() > EnumReceiveInfoStatus.信息登记中.getValue()) {//状态为信息登记之后
            if (!statusMap.containsKey(EnumLIM.EnumReceiveRecordModule.样品交接.getValue())) {//有实验室领样单的才需要样品交接
                DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumPRO.EnumSubRecordType.分析.getValue());
                if (StringUtil.isNotNull(subRecord)) {
                    //添加样品交接的状态
                    DtoStatusForRecord status = new DtoStatusForRecord();
                    status.setStatus(record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue()) ? EnumStatus.待处理.getValue() : EnumStatus.已处理.getValue());
                    status.setReceiveId(record.getId());
                    status.setModule(EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                    status.setNextPersonId(UUIDHelper.GUID_EMPTY);
                    status.setNextPersonName("");
                    status.setCurrentPersonId(UUIDHelper.GUID_EMPTY);
                    status.setCurrentPersonName("");
                    status.setLastNewOpinion(opinion);
                    repository.save(status);
                }
            }
            this.submit(statusMap, record, nextPersonId, nextPerson, opinion);
        } else if (record.getInfoStatus().equals(EnumReceiveInfoStatus.信息登记中.getValue())) {
            Boolean flag = true;
            if (statusMap.containsKey(EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue())) {
                this.logicDeleteById(statusMap.get(EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue()).getId());
                flag = false;
            }
            if (statusMap.containsKey(EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue())) {
                this.logicDeleteById(statusMap.get(EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue()).getId());
                flag = false;
            }
            if (flag) {//认为是提交过来的
                this.submit(statusMap, record, nextPersonId, nextPerson, opinion);
            } else {//如果是复核或审核退回来的，把数据录入置为待处理
                DtoStatusForRecord status = statusMap.get(EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue());
                repository.updateStatusForRecord(status.getId(), EnumStatus.待处理.getValue(),
                        StringUtils.isNotNullAndEmpty(nextPersonId) && !UUIDHelper.GUID_EMPTY.equals(nextPersonId) ? nextPersonId : UUIDHelper.GUID_EMPTY,
                        StringUtils.isNotNullAndEmpty(nextPersonId) && !UUIDHelper.GUID_EMPTY.equals(nextPersonId) ? nextPerson : "",
                        opinion);
            }
        }
    }

    /**
     * 获取开始状态
     *
     * @param record 送样单实体
     * @return 开始状态
     */
    protected EnumReceiveInfoStatus getFromStatus(DtoReceiveSampleRecord record) {
        return EnumReceiveInfoStatus.getByValue(record.getInfoStatus() - 1);
    }

    private void submit(Map<String, DtoStatusForRecord> statusMap, DtoReceiveSampleRecord record, String nextPersonId, String nextPerson, String opinion) {
        EnumReceiveInfoStatus fromStatus = getFromStatus(record);
        EnumReceiveInfoStatus toStatus = EnumReceiveInfoStatus.getByValue(record.getInfoStatus());

        //注意，防止前端重复提交，不能默认后端调用该方法时进行了规避，故需判断map
        if (StringUtil.isNotNull(fromStatus)) {//来源的模块需置为已处理
            String fromModule = this.getModule(fromStatus);
            if (StringUtils.isNotNullAndEmpty(fromModule) && statusMap.containsKey(fromModule)) {
                DtoStatusForRecord status = statusMap.get(fromModule);
                if (status.getStatus().equals(EnumStatus.待处理.getValue())) {
                    repository.updateStatusForRecord(status.getId(), EnumStatus.已处理.getValue(),
                            StringUtils.isNotNullAndEmpty(nextPersonId) && !UUIDHelper.GUID_EMPTY.equals(nextPersonId) ? nextPersonId : UUIDHelper.GUID_EMPTY,
                            StringUtils.isNotNullAndEmpty(nextPersonId) && !UUIDHelper.GUID_EMPTY.equals(nextPersonId) ? nextPerson : "",
                            opinion);
                }
            }
        }
        if (StringUtil.isNotNull(toStatus)) {//到达的模块需置为待处理
            String toModule = this.getModule(toStatus);
            if (StringUtils.isNotNullAndEmpty(toModule) && !statusMap.containsKey(toModule)) {
                DtoStatusForRecord status = new DtoStatusForRecord();
                status.setStatus(EnumStatus.待处理.getValue());
                status.setReceiveId(record.getId());
                status.setModule(toModule);
                if (StringUtils.isNotNullAndEmpty(nextPersonId) && !UUIDHelper.GUID_EMPTY.equals(nextPersonId)) {
                    status.setCurrentPersonId(nextPersonId);
                    status.setCurrentPersonName(nextPerson);
                }
                status.setNextPersonId(UUIDHelper.GUID_EMPTY);
                status.setNextPersonName("");
                status.setLastNewOpinion(opinion);
                repository.save(status);
            }
        }
    }

    /**
     * 根据费用状态枚举获取对应所处模块
     *
     * @param status 费用状态枚举值
     */
    protected String getModule(EnumReceiveInfoStatus status) {
        switch (status) {
            case 新建:
                return EnumLIM.EnumReceiveRecordModule.委托现场送样.getValue();

            case 信息登记中:
                return EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue();

            case 信息复核中:
                return EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue();

            case 信息审核中:
                return EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue();

            default:
                return "";
        }
    }
}