
delete from TB_LIM_ReportConfig where id = '80850661-2665-4c44-9813-3475f58d19c6';

-- 添加仪器标签的报表模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType,
                                method, params, pageConfig, orderNum, bizType, remark, isDeleted,
                                orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod,
                                typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum)
VALUES ('80850661-2665-4c44-9813-3475f58d19c6', 1, 'InstrumentLabel', '仪器标签.xlsx', 'LIMReportForms/仪器标签.xlsx',
        'output/LIMReportForms/仪器标签.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.limReportForms.InstrumentLabelService', '', '', 0, 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-29 10:09:00',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-29 13:33:01', '',
        'LIMReportForms', 'InstrumentLabel', b'0', '', '', '', '');

delete from TB_LIM_ReportApply where id = 'b6e3833a-38b4-42b3-9806-42b9759e3040';

-- 添加仪器标签的报表模板应用场景
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact,
                               isShow, remark, location)
VALUES ('b6e3833a-38b4-42b3-9806-42b9759e3040', '80850661-2665-4c44-9813-3475f58d19c6', 'InstrumentManage', '仪器设备管理',
        'InstrumentLabel', '仪器标签生成', 0, 0, 1, '', '仪器设备管理');
