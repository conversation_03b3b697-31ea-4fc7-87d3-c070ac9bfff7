package com.sinoyd.lims.probase.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AnalyseDataBaseRepository extends IBaseJpaRepository<DtoAnalyseData, String> {
    @Query("select new com.sinoyd.lims.pro.dto.DtoAnalyseData (id,testId,sampleId,isOutsourcing,isSamplingOut,isCompleteField,dataStatus,status,isDataEnabled) from DtoAnalyseData  as a where a.isDeleted = 0 and a.sampleId =:sampleId and a.isOutsourcing=:isOutsourcing and a.isSamplingOut =:isSamplingOut")
    List<DtoAnalyseData> findBySampleIdAndIsOutsourcingAndIsSamplingOut(@Param("sampleId") String sampleId,@Param("isOutsourcing")  Boolean isOutsourcing,@Param("isSamplingOut") Boolean isSamplingOut);

    @Query("select new com.sinoyd.lims.pro.dto.DtoAnalyseData (id,testId,sampleId,isOutsourcing,isSamplingOut,isCompleteField,dataStatus,status,isDataEnabled) from DtoAnalyseData  as a where a.isDeleted = 0 and a.sampleId =:sampleId")
    List<DtoAnalyseData> findBySampleId(@Param("sampleId") String sampleId);

    @Query("select new com.sinoyd.lims.pro.dto.DtoAnalyseData (id,testId,sampleId,isOutsourcing,isSamplingOut,isCompleteField,dataStatus,status,isDataEnabled) from DtoAnalyseData  as a where a.isDeleted = 0 and a.sampleId in:sampleIds and a.isOutsourcing=:isOutsourcing and a.isSamplingOut =:isSamplingOut")
    List<DtoAnalyseData> findBySampleIdInAndIsOutsourcingAndIsSamplingOut(@Param("sampleIds") List<String> sampleIds, @Param("isOutsourcing") Boolean isOutsourcing,@Param("isSamplingOut") Boolean isSamplingOut);

}
