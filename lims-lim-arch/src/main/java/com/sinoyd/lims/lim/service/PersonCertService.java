package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;

import java.util.List;

/**
 * 人员上岗证管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface PersonCertService extends IBaseJpaService<DtoPersonCert, String> {
    /**
     * 上岗证过期详情
     * @return 上岗证过期列表
     */
    List<DtoPersonAbility> getOverDueData();

    /**
     * 获取附件归档路径
     * @param certCode 证书编号
     * @param id 证书id
     * @return 证书信息
     */
    DtoPersonCert findPersonAttachment(String certCode, String id);
}