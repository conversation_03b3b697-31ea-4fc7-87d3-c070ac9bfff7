package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.StationarySourceResultDetailCriteria;
import com.sinoyd.lims.od.dto.DtoStationarySourceResultDetail;
import com.sinoyd.lims.od.service.StationarySourceResultDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * stationarySourceResultDetail服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例: stationarySourceResultDetail服务")
@RestController
@RequestMapping("api/lim/stationarySourceResultDetail")
@Validated
public class StationarySourceResultDetailController extends BaseJpaController<DtoStationarySourceResultDetail, String, StationarySourceResultDetailService> {

    /**
     * 分页动态条件查询StationarySourceResultDetail
     *
     * @param stationarySourceResultDetailCriteria 条件参数
     * @return RestResponse<List < StationarySourceResultDetail>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoStationarySourceResultDetail>> findByPage(StationarySourceResultDetailCriteria stationarySourceResultDetailCriteria) {
        PageBean<DtoStationarySourceResultDetail> pageBean = super.getPageBean();
        RestResponse<List<DtoStationarySourceResultDetail>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, stationarySourceResultDetailCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询stationarySourceResultDetail
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询stationarySourceResultDetail", notes = "根据id查询stationarySourceResultDetail")
    @GetMapping("/{id}")
    public RestResponse<DtoStationarySourceResultDetail> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoStationarySourceResultDetail> restResp = new RestResponse<>();
        DtoStationarySourceResultDetail entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增stationarySourceResultDetail
     *
     * @param stationarySourceResultDetail stationarySourceResultDetail实体
     * @return 新增的stationarySourceResultDetail实体
     */
    @ApiOperation(value = "新增stationarySourceResultDetail", notes = "新增stationarySourceResultDetail")
    @PostMapping("")
    public RestResponse<DtoStationarySourceResultDetail> create(@Validated @RequestBody DtoStationarySourceResultDetail stationarySourceResultDetail) {
        RestResponse<DtoStationarySourceResultDetail> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoStationarySourceResultDetail data = service.save(stationarySourceResultDetail);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新stationarySourceResultDetail
     *
     * @param stationarySourceResultDetail stationarySourceResultDetail实体
     * @return 更新后的stationarySourceResultDetail实体
     */
    @ApiOperation(value = "更新stationarySourceResultDetail", notes = "更新stationarySourceResultDetail")
    @PutMapping("")
    public RestResponse<DtoStationarySourceResultDetail> update(@Validated @RequestBody DtoStationarySourceResultDetail stationarySourceResultDetail) {
        RestResponse<DtoStationarySourceResultDetail> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoStationarySourceResultDetail data = service.update(stationarySourceResultDetail);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
