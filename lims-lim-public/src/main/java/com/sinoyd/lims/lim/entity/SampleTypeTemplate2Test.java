package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * SampleTypeTemplate2Test实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SampleTypeTemplate2Test")
 @Data
 public  class SampleTypeTemplate2Test implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    /**
    * 模板id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("模板id")
	private String sampleTypeTemplateId;
    
    /**
    * 测试项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试项目id")
    private String testId;
    
    
 }