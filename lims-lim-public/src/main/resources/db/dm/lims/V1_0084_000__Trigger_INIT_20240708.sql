CREATE  TRIGGER "AFTER_UPDATE_PARAMS2PARAMSFORMULA"
    AFTER  UPDATE
    ON "TB_LIM_PARAMS2PARAMSFORMULA"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    IF new.isDeleted = 1 THEN
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
        new.domainId);
else
        if new.recordId != old.recordId or (new.recordId is null and old.recordId is not null) or
           (new.recordId is not null and old.recordId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'recordId',
        old.recordId, new.recordId, new.orgId, new.domainId);
END IF;
if new.paramsConfigId != old.paramsConfigId or (new.paramsConfigId is null and old.paramsConfigId is not null) or
           (new.paramsConfigId is not null and old.paramsConfigId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'paramsConfigId',
        old.paramsConfigId, new.paramsConfigId, new.orgId, new.domainId);
END IF;
if new.objectId != old.objectId or (new.objectId is null and old.objectId is not null) or
           (new.objectId is not null and old.objectId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'objectId',
        old.objectId, new.objectId, new.orgId, new.domainId);
END IF;
if new.formula != old.formula or (new.formula is null and old.formula is not null) or
           (new.formula is not null and old.formula is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'formula',
        old.formula, new.formula, new.orgId, new.domainId);
END IF;
if new.isEnabled != old.isEnabled or (new.isEnabled is null and old.isEnabled is not null) or
           (new.isEnabled is not null and old.isEnabled is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_params2paramsformula', new.id, new.modifier, new.modifyDate, 3, 'isEnabled',
        old.isEnabled, new.isEnabled, new.orgId, new.domainId);
END IF;
END IF;
end;
