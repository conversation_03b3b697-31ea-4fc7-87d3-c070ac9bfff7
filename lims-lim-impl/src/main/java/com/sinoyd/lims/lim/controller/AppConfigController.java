package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.AppConfigCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoAppConfig;
import com.sinoyd.lims.lim.service.AppConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * app应用配置
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023-01-12
 */
@RestController
@RequestMapping("/api/lim/appConfig")
@Validated
public class AppConfigController extends BaseJpaController<DtoAppConfig, String, AppConfigService> {

    /**
     * 根据id查询app应用配置
     *
     * @param id app应用配置id
     * @return 查询结果
     */
    @ApiOperation(value = "根据id查询app应用配置", notes = "根据id查询app应用配置")
    @GetMapping("/{id}")
    public RestResponse<DtoAppConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoAppConfig> restResp = new RestResponse<>();
        restResp.setData(service.findOne(id));
        return restResp;
    }

    /**
     * 分页动态条件查询app应用配置
     *
     * @param criteria 查询条件
     * @return 分页查询结果
     */
    @ApiOperation(value = "分页动态条件查询app应用配置", notes = "分页动态条件查询app应用配置")
    @GetMapping
    public RestResponse<List<DtoAppConfig>> findByPage(AppConfigCriteria criteria) {
        RestResponse<List<DtoAppConfig>> restResp = new RestResponse<>();
        PageBean<DtoAppConfig> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 新增app应用配置
     *
     * @param entity 传入的应用配置dto
     * @return 保存完以后的dto
     */
    @ApiOperation(value = "新增app应用配置", notes = "新增app应用配置")
    @PostMapping
    public RestResponse<DtoAppConfig> save(@Validated @RequestBody DtoAppConfig entity) {
        RestResponse<DtoAppConfig> restResp = new RestResponse<>();
        restResp.setData(service.save(entity));
        return restResp;
    }

    /**
     * 修改app应用配置
     *
     * @param entity 传入的应用配置dto
     * @return 修改完以后的dto
     */
    @ApiOperation(value = "修改app应用配置", notes = "修改app应用配置")
    @PutMapping
    public RestResponse<DtoAppConfig> update(@Validated @RequestBody DtoAppConfig entity) {
        RestResponse<DtoAppConfig> restResp = new RestResponse<>();
        restResp.setData(service.update(entity));
        return restResp;
    }


    /**
     * 批量删除app应用配置
     *
     * @param ids app应用配置ids
     * @return 响应响应
     */
    @ApiOperation(value = "批量删除app应用配置", notes = "批量删除app应用配置")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }
}



  