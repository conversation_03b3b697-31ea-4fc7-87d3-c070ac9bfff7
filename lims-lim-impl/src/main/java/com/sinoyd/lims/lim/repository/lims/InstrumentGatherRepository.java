package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGather;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 仪器接入操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
public interface InstrumentGatherRepository extends IBaseJpaRepository<DtoInstrumentGather, String> {

    /**
     * 根据仪器id 或者mn编号统计仪器接入数据
     *
     * @param instrumentId 仪器id
     * @param mnNumber     mn号码
     * @return 统计数量
     */
    @Query("select count(x.id) from DtoInstrumentGather x where x.isDeleted = 0 and x.id <> :id and (x.instrumentId = :instrumentId or x.mnNumber = :mnNumber)")
    Integer countByNotIdAndInstrumentIdOrMnNumber(@Param("id") String id,
                                                  @Param("instrumentId") String instrumentId,
                                                  @Param("mnNumber") String mnNumber);

}