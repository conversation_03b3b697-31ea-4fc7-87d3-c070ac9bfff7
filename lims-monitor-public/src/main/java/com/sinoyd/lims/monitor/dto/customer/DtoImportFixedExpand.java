package com.sinoyd.lims.monitor.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 环境质量点位导入模板下载拓展
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/08
 * @since V100R001
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoImportFixedExpand {
    /**
     * 点位类型
     */
    @Excel(name = "点位类型",orderNum = "10",width = 20)
    private String pointType;
}
