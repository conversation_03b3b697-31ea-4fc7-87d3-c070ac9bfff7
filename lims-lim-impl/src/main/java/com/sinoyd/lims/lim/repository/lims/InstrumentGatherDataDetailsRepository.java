package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherDataDetails;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherParams;

import java.util.Collection;
import java.util.List;

/**
 * 仪器接入数据操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
public interface InstrumentGatherDataDetailsRepository extends IBaseJpaPhysicalDeleteRepository<DtoInstrumentGatherDataDetails, String> {


    /**
     * 根据仪器接入数据ids查询数据详情
     *
     * @param instrumentGatherDataIds 仪器接入数据ids
     * @return 参数数据集合
     */
    List<DtoInstrumentGatherDataDetails> findByInstrumentGatherDataIdIn(Collection<String> instrumentGatherDataIds);


    /**
     * 根据仪器接入数据id查询数据详情
     *
     * @param instrumentGatherDataId 仪器接入数据id
     * @return 参数数据集合
     */
    List<DtoInstrumentGatherDataDetails> findByInstrumentGatherDataId(String instrumentGatherDataId);

}