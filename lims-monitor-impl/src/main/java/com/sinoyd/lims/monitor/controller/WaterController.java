package com.sinoyd.lims.monitor.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.criteria.WaterCriteria;
import com.sinoyd.lims.monitor.dto.rcc.DtoWater;
import com.sinoyd.lims.monitor.service.WaterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * Water服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: Water服务")
 @RestController
 @RequestMapping("api/monitor/water")
 public class WaterController extends BaseJpaController<DtoWater, String,WaterService> {

    /**
     * 分页动态条件查询Water
     * @param waterCriteria 条件参数
     * @return RestResponse<List<Water>>
     */
     @ApiOperation(value = "分页动态条件查询Water", notes = "分页动态条件查询Water")
     @GetMapping
     public RestResponse<List<DtoWater>> findByPage(WaterCriteria waterCriteria) {
         PageBean<DtoWater> pageBean = super.getPageBean();
         RestResponse<List<DtoWater>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, waterCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getData().size());
         return restResponse;
     }

     /**
     * 按主键查询Water
     * @param id 主键id
     * @return RestResponse<DtoWater>
     */
     @ApiOperation(value = "按主键查询Water", notes = "按主键查询Water")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoWater> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoWater> restResponse = new RestResponse<>();
         DtoWater water = service.findOne(id);
         restResponse.setData(water);
         restResponse.setRestStatus(StringUtil.isNull(water) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增Water
     * @param water 实体列表
     * @return RestResponse<DtoWater>
     */
     @ApiOperation(value = "新增Water", notes = "新增Water")
     @PostMapping
     public RestResponse<DtoWater> create(@RequestBody @Validated DtoWater water) {
         RestResponse<DtoWater> restResponse = new RestResponse<>();
         restResponse.setData(service.save(water));
         return restResponse;
      }

     /**
     * 新增Water
     * @param water 实体列表
     * @return RestResponse<DtoWater>
     */
     @ApiOperation(value = "修改Water", notes = "修改Water")
     @PutMapping
     public RestResponse<DtoWater> update(@RequestBody @Validated DtoWater water) {
         RestResponse<DtoWater> restResponse = new RestResponse<>();
         restResponse.setData(service.update(water));
         return restResponse;
      }

    /**
     * "根据id批量删除Water
     * @param ids id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Water", notes = "根据id批量删除Water")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }


    /**
     *水体管理树状结构转换
     * @return RestResponse<DtoWater>
     */
    @ApiOperation(value = "水体管理树状结构转换", notes = "水体管理树状结构转换")
    @GetMapping("/getTree")
    public RestResponse<List<DtoWater>> getTree(String key) {
        RestResponse<List<DtoWater>> restResponse = new RestResponse<>();
        if (StringUtil.isNotEmpty(key)){
            restResponse.setData(service.getTableTree("%"+key+"%"));
        }else {
            restResponse.setData(service.getTableTree(key));
        }
        return restResponse;
    }


    /**
     * 获取所有启用的水体
     *
     * @return RestResponse<List<DtoWater>>
     */
    @ApiOperation(value = "获取所有启用的水体", notes = "获取所有启用的水体")
    @GetMapping("/list")
    public RestResponse<List<DtoWater>> findWaterList() {
        RestResponse<List<DtoWater>> restResponse = new RestResponse<>();
        restResponse.setData(service.findWaterList());
        return restResponse;
    }
 }