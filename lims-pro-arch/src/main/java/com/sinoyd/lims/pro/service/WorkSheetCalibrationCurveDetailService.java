package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoWorkSheetCalibrationCurveDetail;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * WorkSheetCalibrationCurveDetail操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface WorkSheetCalibrationCurveDetailService extends IBaseJpaService<DtoWorkSheetCalibrationCurveDetail, String> {

    /**
     * 按校准曲线id查询明细
     *
     * @param workSheetCalibrationCurveId 校准曲线id
     * @return 返回校准曲线明细
     */
    List<DtoWorkSheetCalibrationCurveDetail> findByWorkSheetCalibrationCurveId(String workSheetCalibrationCurveId);
}