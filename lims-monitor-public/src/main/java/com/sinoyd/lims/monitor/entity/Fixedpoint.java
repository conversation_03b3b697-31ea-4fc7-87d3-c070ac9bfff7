package com.sinoyd.lims.monitor.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * Fixedpoint实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "Fixedpoint")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Fixedpoint extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Fixedpoint() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 测站id
     */
    @Column(length = 50)
    @ApiModelProperty("测站id")
    private String stationId;

    /**
     * 测站名称
     */
    @ApiModelProperty("测站名称")
    @Length(message = "测站名称{validation.message.length}", max = 255)
    private String stationName;

    /**
     * 点位名称
     */
    @ApiModelProperty("点位名称")
    @Length(message = "点位名称{validation.message.length}", max = 255)
    private String pointName;

    /**
     * 是否启用
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是否启用")
    private Boolean isEnabled;

    /**
     * 评价标准
     */
    @Column(length = 50)
    @ApiModelProperty("评价标准")
    private String evaluationId;

    /**
     * 考核区域
     */
    @Column(length = 50)
    @ApiModelProperty("考核区域")
    @Length(message = "考核区域{validation.message.length}", max = 50)
    private String examArea;


    /**
     * 评价等级
     */
    @Column(length = 50)
    @ApiModelProperty("评价等级")
    private String evaluationLevelId;

    /**
     * 备注
     */
    @Column(length = 2000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 2000)
    private String remark;

    /**
     * 周期
     */
    @Column(nullable = false)
    @ApiModelProperty("周期")
    private Integer cycleOrder;

    /**
     * 次数
     */
    @Column(nullable = false)
    @ApiModelProperty("次数")
    private Integer timesOrder;

    /**
     * 内部编码
     */
    @Column(length = 50)
    @ApiModelProperty("内部编码")
    @Length(message = "内部编码{validation.message.length}", max = 50)
    private String internalCode;

    /**
     * 点位编号
     */
    @Column(length = 50)
    @ApiModelProperty("点位编号")
    @Length(message = "点位编号{validation.message.length}", max = 50)
    private String pointCode;

    /**
     * 等级 ： 常量
     */
    @Column(length = 50)
    @ApiModelProperty("等级 ： 常量")
    @Length(message = "等级 ： 常量{validation.message.length}", max = 50)
    private String level;

    /**
     * 经度
     */
    @Column(length = 50)
    @ApiModelProperty("经度")
    @Length(message = "经度{validation.message.length}", max = 50)
    private String lon;
    /**
     * 纬度
     */
    @Column(length = 50)
    @ApiModelProperty("纬度")
    @Length(message = "纬度{validation.message.length}", max = 50)
    private String lat;
    /**
     * 类型（枚举：环境质量 1 污染源 2）
     */
    @Column(nullable = false)
    @ApiModelProperty("类型（枚举：环境质量 1 污染源 2）")
    private Integer pointType;

    /**
     * 点位类型（枚举： 0：其它（默认），1：河流，2：湖库，3：饮用水，4：功能区噪声，5：区域环境噪声，6：交通噪声，7：底泥，8：大气）
     */
    @Column(length = 50)
    @ApiModelProperty("点位类型（枚举： 0：其它（默认），1：河流，2：湖库，3：饮用水，4：功能区噪声，5：区域环境噪声，6：交通噪声，7：底泥，8：大气）")
    //@NotBlank(message = "点位类型（常量），多个类型以逗号隔开{validation.message.blank}")
    @Length(message = "点位类型（常量），多个类型以逗号隔开{validation.message.length}", max = 1000)
    private String folderType;

    /**
     * 样品类型
     */
    @Column(length = 50)
    @ApiModelProperty("样品类型")
    private String sampleTypeId;

    /**
     * 所属企业
     */
    @Column(length = 50)
    @ApiModelProperty("所属企业")
    private String enterpriseId;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    @Length(message = "地址{validation.message.length}", max = 255)
    private String villageCode;

    /**
     * 所在地区Id（Guid）（lims）
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所在地区Id（Guid）（lims）")
    private String areaId;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("排序值")
    private Integer orderNum;

}