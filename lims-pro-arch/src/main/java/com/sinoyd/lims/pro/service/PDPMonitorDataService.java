package com.sinoyd.lims.pro.service;


import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoPollutionDischargeSync;
import com.sinoyd.base.dto.vo.HtmlMonitorDataVO;

import java.util.Collection;
import java.util.List;

/**
 * （PDP:PollutantDischargePermit）排污许可证自行检测数据服务接口
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/23
 * @since V100R001
 */
public interface PDPMonitorDataService {


    /**
     * 根据企业id获取排污许可证自行检测数据
     *
     * @param enterpriseId 企业id
     * @return 自行监测点位数据
     */
    List<HtmlMonitorDataVO> findPointMonitorData(String enterpriseId);


    /**
     * 批量同步排污许可证自行监测点位数据
     *
     * @param enterpriseIds 企业id集合
     */
    void syncPointMonitorData(Collection<String> enterpriseIds);

    /**
     * 点位数据同步至本系统
     *
     * @param dataList     需要同步的自行监测数据
     * @param enterpriseId 企业id
     */
    void sync(Collection<HtmlMonitorDataVO> dataList, String enterpriseId);

    /**
     * 批量请求排污许可证自行监测数据并创建数据
     *
     * @param enterpriseList 企业数据
     */
    List<DtoPollutionDischargeSync> doBatchRequest(Collection<DtoEnterprise> enterpriseList);

    /**
     * 更新企业同步状态
     *
     * @param enterprises 企业集合
     * @param syncList    同步信息集合
     */
    void updateEntSyncStatus(Collection<DtoEnterprise> enterprises, List<DtoPollutionDischargeSync> syncList);
}
