package com.sinoyd.lims.pro.repository;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoQualityControlEvaluate;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * QualityControlEvaluate数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/9
 * @since V100R001
 */
public interface QualityControlEvaluateRepository extends IBaseJpaRepository<DtoQualityControlEvaluate, String>, LimsRepository<DtoQualityControlEvaluate, String> {

    /**
     * 根据关联id查找
     *
     * @param objectIds 关联样品id
     * @return 质控信息
     */
    List<DtoQualityControlEvaluate> findByObjectIdIn(Collection<String> objectIds);

    /**
     * 根据关联id列表删除
     *
     * @param objectIds 关联样品id
     * @return 删除的数量
     */
    @Modifying
    @Query("update DtoQualityControlEvaluate a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.objectId in :objectIds")
    int deleteByObjectIdIn(@Param("objectIds") List<String> objectIds,
                           @Param("modifier") String modifier,
                           @Param("modifyDate") Date modifyDate);

    /**
     * 根据id删除真删
     *
     * @param ids id集合
     * @return 删除条数
     */
    @Transactional
    Integer deleteByIdIn(List<String> ids);
}