package com.sinoyd.lims.pro.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.customer.DtoImportOutSourceData;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分包数据导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/2/9
 */
@Component
@Data
public class OutSourceDataVerifyHandle implements IExcelVerifyHandler<DtoImportOutSourceData> {


    /**
     * 关联数据
     */
    private ThreadLocal<List<DtoImportOutSourceData>> extendTl = new ThreadLocal<>();


    /**
     * 分包数据导入校验
     *
     * @param outSourceData 导入数据
     * @return 校验结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportOutSourceData outSourceData) {

        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        List<DtoImportOutSourceData> extendList = extendTl.get();
        Map<String, DtoImportOutSourceData> importOldMap = extendList.stream().collect(Collectors.toMap(DtoImportOutSourceData::getAnalyseDataId, item -> item));
        isChange(result, outSourceData, importOldMap);
        return result;
    }

    /**
     * 表格值修改校验
     *
     * @param result       校验结果
     * @param data         excel行数据
     * @param importOldMap 导入前原数据
     */
    private void isChange(ExcelVerifyHandlerResult result, DtoImportOutSourceData data, Map<String, DtoImportOutSourceData> importOldMap) {
        DtoImportOutSourceData oldData = null;
        if (StringUtil.isNotNull(importOldMap)) {
            oldData = importOldMap.get(data.getAnalyseDataId());
        }
        String errorMsg = "";
        if (StringUtil.isEmpty(data.getAnalyseDataId()) || StringUtil.isNull(oldData)) {
            errorMsg = "分析数据id不能修改；";
        } else {
            if (StringUtil.isEmpty(data.getState())|| !oldData.getState().equals(data.getState())) {
                errorMsg+="状态值不能修改；";
            }
            if (EnumPRO.EnumOutSourceDataStatus.getByValue(EnumPRO.EnumOutSourceDataStatus.已确认.getValue()).equals(oldData.getState())) {
                if (!oldData.getAnalyzeMethodName().equals(data.getAnalyzeMethodName())||!oldData.getTestValue().equals(data.getTestValue())
                ||!oldData.getDimensionName().equals(data.getDimensionName())) {
                    errorMsg+="状态值为已确认的，分析方法、出证结果、量纲不能修改";
                }
            }
        }

        if (StringUtil.isNotEmpty(errorMsg)) {
            result.setSuccess(false);
            result.setMsg("第" + (data.getRowNum() + 1) + "行数据校验错误，" + errorMsg);
        }
    }
}
