package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.OtherDetail;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoOtherDetail实体
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_OtherDetail") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoOtherDetail extends OtherDetail {
   private static final long serialVersionUID = 1L;

   @Transient
   private String typeName;

   @Transient
   private Boolean isChange = false;
 }