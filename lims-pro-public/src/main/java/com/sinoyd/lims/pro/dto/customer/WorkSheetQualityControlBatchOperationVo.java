package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.dto.DtoQualityControl;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 工作单批量操作质控样传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/6/21
 */
@Data
public class WorkSheetQualityControlBatchOperationVo {

    /**
     * 需要添加质控样的原样id列表
     */
    private List<String> sampleIds = new ArrayList<>();

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 质控数据传输对象
     */
    List<DtoQualityControl> qualityControls = new ArrayList<>();

}
