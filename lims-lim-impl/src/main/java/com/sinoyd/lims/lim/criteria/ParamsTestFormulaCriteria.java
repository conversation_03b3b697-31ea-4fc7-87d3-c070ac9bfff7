package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公式参数相关参数管理查询条件
 * <AUTHOR>
 * @version v1.0.0 2019/5/14
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParamsTestFormulaCriteria extends BaseCriteria {

    /**
     * 公式Id
     */
    private String objId;

    private String paramsId;

    /**
     * 关键字：参数名称，参数编号,变量名称
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtils.isNotNullAndEmpty(objId) && !UUIDHelper.GUID_EMPTY.equals(this.objId)) {
            condition.append(" and objId = :objId");
            values.put("objId", this.objId);
        }

        if (StringUtils.isNotNullAndEmpty(paramsId) && !UUIDHelper.GUID_EMPTY.equals(this.paramsId)) {
            condition.append(" and paramsId = :paramsId");
            values.put("paramsId", this.paramsId);
        }

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and (paramsName like :key or alias like :key)");
            values.put("key", "%" + this.key + "%");
        }

        return condition.toString();
    }
}