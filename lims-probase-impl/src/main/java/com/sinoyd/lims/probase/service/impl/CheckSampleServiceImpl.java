package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.entity.Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.probase.repository.AnalyseDataBaseRepository;
import com.sinoyd.lims.probase.repository.ReceiveSampleRecordBaseRepository;
import com.sinoyd.lims.probase.repository.SampleBaseRepository;
import com.sinoyd.lims.probase.repository.SampleFolderBaseRepository;
import com.sinoyd.lims.probase.service.CheckSampleService;
import com.sinoyd.lims.probase.service.NewLogBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CheckSampleServiceImpl implements CheckSampleService {

    @Autowired
    private SampleFolderBaseRepository sampleFolderBaseRepository;

    @Autowired
    private SampleBaseRepository sampleBaseRepository;

    @Autowired
    private AnalyseDataBaseRepository analyseDataBaseRepository;

    @Autowired
    private ReceiveSampleRecordBaseRepository receiveSampleRecordBaseRepository;

    @Autowired
    private TestService testService;

    @Autowired
    private NewLogBaseService newLogBaseService;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    private CodeService codeService;

    @Transactional
    @Override
    public void checkSample(String id) {
        DtoSample objSample = sampleBaseRepository.findOne(id);
        checkSample(objSample);
    }

    @Transactional
    @Override
    public void checkSample(DtoSample objSample) {
        try {
            Boolean isChange = false;
            String id = objSample.getId();
            //更新样品点位名称
            objSample.setRedFolderName(getFolderName(objSample));

            //TODO:开关考虑
            //是否有检测单审核步骤
            Boolean isWorkSheetAudit = true;
            //是否跳过样品分配
            DtoCode codeStr = codeService.findByCode(ProCodeHelper.SKIP_SAMPLE_RECEIVE);
            Boolean skipReceive = StringUtil.isNotNull(codeStr) && codeStr.getDictValue().equals("1");
            if (!objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品作废.toString())) {
                //更新分析项目名称
                List<DtoAnalyseData> anaList = analyseDataBaseRepository.findBySampleIdAndIsOutsourcingAndIsSamplingOut(id, false, false);
                List<DtoAnalyseData> allAnaList = analyseDataBaseRepository.findBySampleId(id);
                String oldRedAnalyzeItems = objSample.getRedAnalyzeItems();
                if (anaList.size() > 0) {
                    List<String> testIds = anaList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
                    List<DtoTest> testList = testService.findRedisByIds(testIds).stream().sorted(Comparator.comparing(DtoTest::getRedAnalyzeItemName)).collect(Collectors.toList());
                    String redAnalyzeItems = String.join(",", testList.stream().map(Test::getRedAnalyzeItemName).distinct().collect(Collectors.toList()));
                    if (!redAnalyzeItems.equals(oldRedAnalyzeItems)) {
                        objSample.setRedAnalyzeItems(redAnalyzeItems);
                        isChange = true;
                    }
                } else if (StringUtils.isNotNullAndEmpty(oldRedAnalyzeItems)) {
//                    if (allAnaList.size() == 0) {
                    //如果原先有值，但是数据删除之后要进行纠正修改
                    objSample.setRedAnalyzeItems("");
                    isChange = true;
//                    }
                }

                //无检测项目或者全部分包
                if (anaList.size() == 0) {
                    //存在分析分包的指标
                    if (allAnaList.stream().anyMatch(DtoAnalyseData::getIsSamplingOut)) {
                        //未采样
                        if (objSample.getSamplingStatus().equals(EnumPRO.EnumSamplingStatus.需要取样还未取样.getValue())) {
                            objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
                            objSample.setStatus(EnumPRO.EnumSampleStatus.样品未采样.toString());
                            isChange = true;
                        }
                        //已采样
                        else if (!(objSample.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.分析完成.getValue()) &&
                                objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品检毕.toString()))
                        ) {
                            objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.分析完成.getValue());
                            objSample.setStatus(EnumPRO.EnumSampleStatus.样品检毕.toString());
                            isChange = true;
                        }
                    } else {
                        //全部分包 == 采测分包 不需要分析及采样
                        if (!objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品检毕.toString())) {
                            objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.分析完成.getValue());
                            objSample.setStatus(EnumPRO.EnumSampleStatus.样品检毕.toString());
                            objSample.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                            isChange = true;
                        }
                    }
                }
                //没采样或采样中
                else if (!objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品未采样.toString())
                        && (objSample.getSamplingStatus().equals(EnumPRO.EnumSamplingStatus.需要取样还未取样.getValue())
                        || objSample.getSamplingStatus().equals(EnumPRO.EnumSamplingStatus.采样中.getValue())
                )) {
                    objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.不能分析.getValue());
                    objSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
                    objSample.setStatus(EnumPRO.EnumSampleStatus.样品未采样.toString());
                    isChange = true;
                }
                //送样单提交但未领样
                else if (!objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品未领样.toString())
                        && objSample.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不能分析.getValue())
                        && objSample.getSamplingStatus().equals(EnumPRO.EnumSamplingStatus.已经完成取样.getValue())) {
                    //判断样品领取是否跳过
                    if (skipReceive) {
                        objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                        objSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
                        objSample.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
                    } else {
                        objSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
                        objSample.setStatus(EnumPRO.EnumSampleStatus.样品未领样.toString());
                    }
                    isChange = true;
                }
                //样品待检
                else if (!objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品待检.toString())
                        && anaList.stream().filter(a -> !a.getStatus().equals(EnumPRO.EnumAnalyseDataStatus.未测.toString())).count() == 0) {
                    if (!objSample.getReceiveId().equals(UUIDHelper.GUID_EMPTY)) {
                        DtoReceiveSampleRecord objrsr = receiveSampleRecordBaseRepository.findOne(objSample.getReceiveId());
                        //判断送样单下样品是否存在已经领取
                        List<DtoSample> sampleList = sampleBaseRepository.findByReceiveId(objrsr.getId());
                        if (!objrsr.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue())) {
                            if (anaList.size() == anaList.stream().filter(a -> !a.getIsCompleteField()).count()) {
                                if ((objSample.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue())
                                        || objSample.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue())) && !skipReceive) {
                                    if (sampleList.stream().anyMatch(p -> EnumPRO.EnumInnerReceiveStatus.已经领取.getValue().equals(p.getInnerReceiveStatus())
                                            || EnumPRO.EnumInnerReceiveStatus.已确认领取.getValue().equals(p.getInnerReceiveStatus()))) {
                                        objSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
                                        objSample.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
                                    } else {
                                        objSample.setStatus(EnumPRO.EnumSampleStatus.样品未领样.toString());
                                    }
                                    isChange = true;
                                } else {
                                    //如果新的样品加入已有送样单，则需要修改样品领取状态
                                    if (!EnumPRO.EnumInnerReceiveStatus.已经领取.getValue().equals(objSample.getInnerReceiveStatus())) {
                                        objSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
                                    }
                                    objSample.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
                                    isChange = true;
                                }
                            } else if (!EnumPRO.EnumSampleStatus.样品待检.toString().equals(objSample.getStatus())) {
                                //如果不是样品待检的时候进行纠正，并记录变动
                                objSample.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
                                isChange = true;
                            }
                            if (!EnumPRO.EnumSamplingStatus.已经完成取样.getValue().equals(objSample.getSamplingStatus())) {
                                objSample.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                                isChange = true;
                            }
                            if (EnumPRO.EnumInnerReceiveStatus.不能领取.getValue().equals(objSample.getInnerReceiveStatus())) {
                                if (sampleList.stream().anyMatch(p -> EnumPRO.EnumInnerReceiveStatus.已经领取.getValue().equals(p.getInnerReceiveStatus())
                                        || EnumPRO.EnumInnerReceiveStatus.已确认领取.getValue().equals(p.getInnerReceiveStatus()))) {
                                    objSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
                                } else {
                                    objSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue());
                                }
                                isChange = true;
                            }
                            if (!EnumPRO.EnumAnalyzeStatus.可以分析.getValue().equals(objSample.getAnanlyzeStatus())) {
                                objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                                isChange = true;
                            }
                        } else//一个新增的送样单，添加一个样品，增加测试项目之后，再把测试项目删掉，再添加测试项目，样品状态没有做纠正，还是样品检毕
                        {
                            if (anaList.size() == anaList.stream().filter(a -> !a.getIsCompleteField()).count()) {
                                objSample.setStatus(EnumPRO.EnumSampleStatus.样品未领样.toString());
                                isChange = true;
                            } else if (!EnumPRO.EnumSampleStatus.样品待检.toString().equals(objSample.getStatus())) {
                                objSample.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
                                isChange = true;
                            }
                            if (!EnumPRO.EnumSamplingStatus.已经完成取样.getValue().equals(objSample.getSamplingStatus())) {
                                objSample.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                                isChange = true;
                            }
                            if (!EnumPRO.EnumInnerReceiveStatus.不能领取.getValue().equals(objSample.getInnerReceiveStatus())) {
                                objSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
                                isChange = true;
                            }
                            if (!EnumPRO.EnumAnalyzeStatus.可以分析.getValue().equals(objSample.getAnanlyzeStatus())) {
                                objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                                isChange = true;
                            }
                        }
                    }
                }
                //样品在测
                else if (!objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品在检.toString())
                        && anaList.stream().filter(a -> a.getStatus().equals(EnumPRO.EnumAnalyseDataStatus.拒绝.toString())
                        || a.getStatus().equals(EnumPRO.EnumAnalyseDataStatus.在测.toString())
                        || (a.getStatus().equals(EnumPRO.EnumAnalyseDataStatus.未测.toString())
                        && objSample.getSamplingStatus().equals(EnumPRO.EnumSamplingStatus.已经完成取样.getValue()))).count() > 0) {
                    objSample.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                    //当样品分配完之后，样品是待检状态，但是当再复制样品，样品会变成在检，所以这边需要排除待检的状态
                    if (!objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品待检.toString())) {
                        objSample.setStatus(EnumPRO.EnumSampleStatus.样品在检.toString());
                        objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.正在分析.getValue());
                    } else if (objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品待检.toString()) &&
                            !objSample.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.可以分析.getValue())) {
                        //样品待检状态要对应可以分析状态，不应该对应正在分析状态（也是复制样品的时候会触发）
                        objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                    }
                    if (objSample.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue())) {
                        objSample.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue());
                    }
                    isChange = true;
                }
                //样品检毕
                else if (!objSample.getStatus().equals(EnumPRO.EnumSampleStatus.样品检毕.toString())
                        && ((!isWorkSheetAudit && anaList.stream().filter(a -> !a.getStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.toString())).count() == 0)
                        || (isWorkSheetAudit && (anaList.stream().filter(a -> !a.getIsOutsourcing() && !a.getIsSamplingOut()
                        && !a.getIsCompleteField() && !a.getStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.toString())).count() == 0
                        && anaList.stream().filter(a -> (a.getIsOutsourcing() || a.getIsSamplingOut() || a.getIsCompleteField())
                        && !a.getIsDataEnabled()).count() == 0)))) {
                    objSample.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.分析完成.getValue());
                    objSample.setStatus(EnumPRO.EnumSampleStatus.样品检毕.toString());
                    isChange = true;
                }
            }
            if (isChange) {
                //#region Biz修改
                objSample.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                objSample.setModifyDate(new Date());
                //#endregion
                commonRepository.merge(objSample);
                String code = String.format(StringUtils.isNotNullAndEmpty(objSample.getCode()) ? ("(" + objSample.getCode() + ")") : "");

                String comment = String.format("更新样品%s%s状态为%s", objSample.getRedFolderName(), code, objSample.getStatus());

                newLogBaseService.createLog(objSample.getId(),
                        comment,
                        "",
                        EnumPRO.EnumLogType.样品流程.getValue(),
                        EnumPRO.EnumLogObjectType.样品.getValue(),
                        EnumPRO.EnumLogOperateType.样品状态更新.toString(),
                        PrincipalContextUser.getPrincipal().getUserId(),
                        PrincipalContextUser.getPrincipal().getUserName(),
                        "",
                        "");
            }
        } catch (Exception ex) {
            throw new BaseException(ex);
        }
    }

    /**
     * 样品重新获取点位名称
     *
     * @param dtoSample 样品信息
     * @return 返回相应的点位名称信息
     */
    @Override
    public String getFolderName(DtoSample dtoSample) {
        String folderName = StringUtils.isNotNullAndEmpty(dtoSample.getRedFolderName()) ? dtoSample.getRedFolderName() : "";

        if (StringUtils.isNotNullAndEmpty(dtoSample.getSampleFolderId())) {
            DtoSampleFolder objFolder = sampleFolderBaseRepository.findOne(dtoSample.getSampleFolderId());
            if (StringUtil.isNotNull(objFolder) && StringUtils.isNotNullAndEmpty(objFolder.getWatchSpot())) {
//                folderName = String.format("%s%s", objFolder.getWatchSpot(), StringUtils.isNotNullAndEmpty(objFolder.getFolderCode()) ? ("_" + objFolder.getFolderCode()) : "");
                folderName = objFolder.getWatchSpot();
                folderName = String.format("%s(%d-%d-%d)", folderName, dtoSample.getCycleOrder(), dtoSample.getTimesOrder(),
                        dtoSample.getSampleOrder());
            }
        }
        return folderName;
    }
}



