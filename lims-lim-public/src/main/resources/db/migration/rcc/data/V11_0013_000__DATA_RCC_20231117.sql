--刷新原始记录单数据参数与测试项目公式参数的关联
CREATE INDEX tb_lim_paramstestformula_objId_IDX USING BTREE ON tb_lim_paramstestformula (objId);
CREATE INDEX tb_lim_params2paramsformula_paramsTestFormulaId_IDX USING BTREE ON tb_lim_params2paramsformula (paramsTestFormulaId);
CREATE INDEX tb_lim_params2paramsformula_objectId_IDX USING BTREE ON tb_lim_params2paramsformula (objectId);
update tb_lim_params2paramsformula a
inner join tb_lim_paramstestformula b
on b.objId = a.objectId and a.formula = concat('[',b.paramsName ,']')
set a.paramsTestFormulaId  = b.id
where a.paramsTestFormulaId = '00000000-0000-0000-0000-000000000000';
