package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;

/**
 * 人员证书查询条件
 *
 * <AUTHOR>
 * @version v1.0.0 2019/5/6
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PersonCertCriteria extends BaseCriteria {

    /**
     * 有效期开始时间
     */
    private String startTime;

    /**
     * 有效期结束时间
     */
    private String endTime;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 状态 0：正常 1：即将过期 2：已过期
     */
    private String state;

    /**
     * 证书名称，证书编号
     */
    private String cert;

    /**
     * 分析项目（拼音）
     */
    private String analyzeItem;

    /**
     * 分析方法,编号
     */
    private String analyzeMethod;

    /**
     * 对比时间，用于时间状态查询使用
     */
    private Date compareDate;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(personId)
                && !UUIDHelper.GUID_EMPTY.equals(this.personId)) {
            condition.append(" and p.personId = :personId");
            values.put("personId", this.personId);
        }

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and p.issueCertTime >= :startTime ");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and p.issueCertTime < :endTime ");
            values.put("endTime", to);
        }

        if (StringUtil.isNotEmpty(state) && !"-1".equals(state)) {
            // 正常状态
            if ("0".equals(state)) {
                condition.append(" and exists (select 1 from DtoPersonAbility ab where p.id = ab.personCertId and ab.certEffectiveTime >= :compareDate )");
            } else {
                condition.append(" and exists (select 1 from DtoPersonAbility ab where p.id = ab.personCertId and ab.certEffectiveTime < :compareDate )");
                // 即将过期
                if ("1".equals(state)) {
                    condition.append(" and exists (select 1 from DtoPersonAbility ab where p.id = ab.personCertId and ab.certEffectiveTime >= :nowDate )");
                    values.put("nowDate", DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));
                }
            }
            values.put("compareDate", this.compareDate);
        }

        if (StringUtil.isNotEmpty(cert)) {
            condition.append(" and (p.certName like :cert or p.certCode like :cert)");
            values.put("cert", "%" + cert + "%");
        }

        if (StringUtil.isNotEmpty(analyzeItem)) {
            condition.append(" and exists (select 1 from  DtoPersonAbility ab where p.id = ab.personCertId and exists (select 1 from  DtoTest as t where t.id = ab.testId and (t.redAnalyzeItemName like :analyzeItem or t.fullPinYin like :analyzeItem)))");
            values.put("analyzeItem", "%" + analyzeItem + "%");
        }

        if (StringUtil.isNotEmpty(analyzeMethod)) {
            condition.append(" and exists (select 1 from  DtoPersonAbility ab where p.id = ab.personCertId and exists (select 1 from  DtoTest as t where t.id = ab.testId  and (t.redAnalyzeMethodName like :analyzeMethod or t.redCountryStandard like :analyzeMethod)))");
            values.put("analyzeMethod", "%" + analyzeMethod + "%");
        }

        return condition.toString();
    }
}