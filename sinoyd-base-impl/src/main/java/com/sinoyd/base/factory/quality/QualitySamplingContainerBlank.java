package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;

import java.util.List;
import java.util.Map;

public class QualitySamplingContainerBlank extends QualityControlKind {

    @Override
    public Integer qcTypeValue() {
        return 65536;
    }

    @Override
    public String qcTypeName() {
        return "采样介质空白";
    }

    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "采样介质空白";
    }

    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了采样介质空白样qcCode，原样为oldCode。";
    }

    @Override
    public DtoQualityConfig getQualityConfig() {
        return new DtoQualityConfig();
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return new DtoQualityConfig();
    }

    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        return "采样介质空白样";
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}
