package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForWorkSheetData;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;

import java.util.List;


/**
 * PerformanceStatisticForWorkSheetData操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/19
 * @since V100R001
 */
public interface PerformanceStatisticForWorkSheetDataService extends IBaseJpaService<DtoPerformanceStatisticForWorkSheetData, String> {

    /**
     * 生成检测单绩效
     *
     * @param workSheetFolderId 检测单ID
     */
    void createAnalyseStatistic(String workSheetFolderId);


    /**
     * 生成检测单绩效
     *
     * @param workSheetFolders 检测单信息
     * @param analyseDataList  检测单相关的数据
     */
    void createAnalyseStatistic(List<DtoWorkSheetFolder> workSheetFolders,
                                List<DtoAnalyseData> analyseDataList);


    /**
     * 查询分析数据相应的工作量
     *
     * @param baseCriteria 查询条件
     */
    void findByPage(PageBean<DtoPerformanceStatisticForWorkSheetData> pb, BaseCriteria baseCriteria);

    /**
     * 获取总计行
     *
     * @param baseCriteria
     * @return
     */
    DtoPerformanceStatisticForWorkSheetData findSumPerformanceStatistic(BaseCriteria baseCriteria);
}