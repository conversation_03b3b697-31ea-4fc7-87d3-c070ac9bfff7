package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.PerformanceStatisticForLocalDataCriteria;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForLocalData;
import com.sinoyd.lims.pro.service.PerformanceStatisticForLocalDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "示例: 现场数据绩效服务")
@RestController
@RequestMapping("api/pro/workloadLocal")
public class PerformanceStatisticForLocalDataController extends BaseJpaController<DtoPerformanceStatisticForLocalData, String,PerformanceStatisticForLocalDataService> {


    @ApiOperation(value = "查询现场数据绩效", notes = "查询现场数据绩效")
    @GetMapping
    public RestResponse<List<DtoPerformanceStatisticForLocalData>> findList(PerformanceStatisticForLocalDataCriteria baseCriteria) {
        PageBean<DtoPerformanceStatisticForLocalData> pageBean = super.getPageBean();
        RestResponse<List<DtoPerformanceStatisticForLocalData>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, baseCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    @ApiOperation(value = "查询现场数据绩效总计", notes = "查询现场数据绩效总计")
    @GetMapping("/sum")
    public RestResponse<DtoPerformanceStatisticForLocalData> findSumPerformanceStatistic(PerformanceStatisticForLocalDataCriteria baseCriteria) {
        RestResponse<DtoPerformanceStatisticForLocalData> restResponse = new RestResponse<>();
        service.findSumPerformanceStatistic(baseCriteria);
        restResponse.setData(service.findSumPerformanceStatistic(baseCriteria));
        return restResponse;
    }
}
