package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherParams;
import com.sinoyd.lims.lim.repository.lims.InstrumentGatherParamsRepository;
import com.sinoyd.lims.lim.service.InstrumentGatherParamsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 仪器接入参数操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Service
public class InstrumentGatherParamsServiceImpl extends BaseJpaServiceImpl<DtoInstrumentGatherParams, String, InstrumentGatherParamsRepository> implements InstrumentGatherParamsService {

    @Override
    public void findByPage(PageBean<DtoInstrumentGatherParams> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrumentGatherParams x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");
        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public DtoInstrumentGatherParams save(DtoInstrumentGatherParams entity) {
        if (repository.countByParamName(entity.getParamName(), entity.getDataType(), entity.getInstrumentGatherId()) > 0) {
            throw new BaseException("已存在相同的参数名称！");
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoInstrumentGatherParams update(DtoInstrumentGatherParams entity) {
        if (repository.countByParamNameAndId(entity.getId(), entity.getParamName(), entity.getDataType(), entity.getInstrumentGatherId()) > 0) {
            throw new BaseException("已存在相同的参数名称！");
        }
        return super.update(entity);
    }
}