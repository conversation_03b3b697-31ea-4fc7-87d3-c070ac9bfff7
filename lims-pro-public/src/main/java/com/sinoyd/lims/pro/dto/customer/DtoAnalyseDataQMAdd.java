package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 质控数据添加装载数据结构
 * <AUTHOR>
 * @version V1.0.0 2020/6/15
 * @since V100R001
 */
@Data
public class DtoAnalyseDataQMAdd {
    /**
     * 构造函数
     */
    public DtoAnalyseDataQMAdd() {

    }

    /**
     * 构造函数
     *
     * @param project             项目
     * @param sample              样品
     * @param receiveSampleRecord 送样单
     * @param testIds 测试项目id
     * @param isCheckSample       是否纠正状态
     */
    public DtoAnalyseDataQMAdd(DtoProject project, DtoSample sample, DtoReceiveSampleRecord receiveSampleRecord,List<String>testIds, Boolean isCheckSample) {
        this.project = project;
        this.sample = sample;
        this.receiveSampleRecord = receiveSampleRecord;
        this.testIds = testIds;
        this.isCheckSample = isCheckSample;
    }

    /**
     * 样品id--仅接收参数用
     */
    private String sampleId;

    /**
     * 测试项目id集合（存在重复）
     */
    private List<String> testIds = new ArrayList<>();

    /**
     * 项目
     */
    private DtoProject project;

    /**
     * 样品
     */
    private DtoSample sample;

    /**
     * 送样单
     */
    private DtoReceiveSampleRecord receiveSampleRecord;

    /**
     * 是否纠正状态
     */
    private Boolean isCheckSample = true;

    /**
     * 测试项目id与测试项目扩展的关联
     */
    private Map<String, DtoTestExpand> test2TestExpand = new HashMap<>();

    /**
     * 测试项目id与第一人员的关联
     */
    private Map<String, DtoPerson2Test> person2TestMap = new HashMap<>();

    /**
     * 测试项目id与分析数据id集合
     */
    private Map<String, List<String>> test2AnaId = new HashMap<>();


    /**
     * 设置测试扩展
     *
     * @param testExpandList 测试扩展列表
     */
    public void setTestExpandList(List<DtoTestExpand> testExpandList) {
        this.test2TestExpand = testExpandList.stream().collect(Collectors.toMap(DtoTestExpand::getTestId, expand -> expand));
    }

    /**
     * 设置测试人员配置
     *
     * @param p2tList 测试人员配置列表
     */
    public void setPerson2TestList(List<DtoPerson2Test> p2tList) {
        this.person2TestMap = p2tList.stream().collect(Collectors.toMap(DtoPerson2Test::getTestId, p2t -> p2t));
    }
}
