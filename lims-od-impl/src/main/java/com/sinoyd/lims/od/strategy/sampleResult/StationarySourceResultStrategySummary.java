package com.sinoyd.lims.od.strategy.sampleResult;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.common.utils.StationarySourceCalculationUtil;
import com.sinoyd.lims.od.constants.OdsConstants;
import com.sinoyd.lims.od.dto.*;
import com.sinoyd.lims.od.dto.customer.DtoGroupSummaryTemp;
import com.sinoyd.lims.od.enums.EnumOds;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 固定源结果汇总实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/11
 * @since V100R001
 */
@Component(OdsConstants.SampleResultName.STATIONARY_SOURCE_RESULT)
public class StationarySourceResultStrategySummary extends AbsSampleResultStrategy {


    /**
     * 固定源默认实验组
     */
    private static final List<Integer> defaultSn = Arrays.asList(1, 2);

    /**
     * T值校验数值
     */
    private static final BigDecimal T_CHECK_VALUE = new BigDecimal("3.182");

    @Override
    public List<DtoLabGroup> getDefaultGroup(List<DtoLabGroup> labGroupList) {
        List<String> groupIds = labGroupList.stream().map(DtoLabGroup::getId).collect(Collectors.toList());
        // 根据实验次序，筛选已完成三轮的实验组
        List<DtoLabSeq> labSeqList = StringUtil.isNotEmpty(groupIds) ? labSeqRepository.findByGroupIdIn(groupIds) : new ArrayList<>();
        long count = labSeqList.stream().filter(p -> p.getLabState().equals(EnumOds.EnumLabState.已完成.getValue())).map(DtoLabSeq::getGroupId).distinct().count();
        if (count < 2) {
            throw new BaseException("实验组小于两组，无法进行结果汇总");
        }
        return labGroupList.stream().filter(p -> defaultSn.contains(p.getSn())).peek(p -> p.setCalculateSn(p.getSn())).collect(Collectors.toList());
    }

    @Override
    public List<DtoLabGroup> findGroupSummary(List<DtoGroupSummaryTemp> groupSummaryTemps) {
        List<String> groupIds = groupSummaryTemps.stream().map(DtoGroupSummaryTemp::getGroupId).collect(Collectors.toList());
        List<DtoLabGroup> labGroups = StringUtil.isNotEmpty(groupIds) ? labGroupRepository.findAll(groupIds) : new ArrayList<>();
        List<DtoLabSeq> labSeqList = StringUtil.isNotEmpty(groupIds) ? labSeqRepository.findByGroupIdIn(groupIds) : new ArrayList<>();
        List<String> seqIds = labSeqList.stream().map(DtoLabSeq::getId).collect(Collectors.toList());
        List<DtoOdPersonAnswer> odPersonAnswers = StringUtil.isNotEmpty(seqIds) ? odPersonAnswerRepository.findByLabSeqIdIn(seqIds) : new ArrayList<>();
        List<String> personIds = odPersonAnswers.stream().map(DtoOdPersonAnswer::getPersonId).collect(Collectors.toList());
        List<DtoOdPerson> dtoOdPersonList = StringUtil.isNotEmpty(personIds) ? odPersonRepository.findAll(personIds) : new ArrayList<>();
        List<DtoStationarySourceResult> stationarySourceResults = stationarySourceResultRepository.findByGroupIdIn(groupIds);
        Map<String, DtoStationarySourceResult> sourceResultMap = stationarySourceResults.stream().collect(Collectors.toMap(DtoStationarySourceResult::getGroupId, p -> p));
        List<String> sourceResultIds = stationarySourceResults.stream().map(DtoStationarySourceResult::getId).collect(Collectors.toList());
        List<DtoStationarySourceResultDetail> stationarySourceResultDetails = StringUtil.isNotEmpty(sourceResultIds) ?
                stationarySourceResultDetailRepository.findByResultIdIn(sourceResultIds) : new ArrayList<>();

        for (DtoLabGroup labGroup : labGroups) {
            groupSummaryTemps.stream().filter(p -> p.getGroupId().equals(labGroup.getId())).findFirst().ifPresent(p -> {
                labGroup.setCalculateSn(p.getCalculateSn());
            });
            DtoStationarySourceResult stationarySourceResult = sourceResultMap.get(labGroup.getId());
            if (StringUtil.isNull(stationarySourceResult)) {
                List<DtoLabSeq> labSeqs = labSeqList.stream().filter(p -> p.getGroupId().equals(labGroup.getId())).collect(Collectors.toList());
                List<String> labSeqIds = labSeqs.stream().map(DtoLabSeq::getId).collect(Collectors.toList());
                List<DtoOdPersonAnswer> odPersonAnswerList = odPersonAnswers.stream().filter(p -> labSeqIds.contains(p.getLabSeqId())).collect(Collectors.toList());
                Map<String, DtoLabSeq> labSeqMap = labSeqs.stream().collect(Collectors.toMap(DtoLabSeq::getId, p -> p));
                odPersonAnswerList.forEach(p -> {
                    // 赋值稀释倍数
                    p.setDilutionRate(labSeqMap.containsKey(p.getLabSeqId()) ? labSeqMap.get(p.getLabSeqId()).getDilutionRate() : -1);
                });
                List<String> odPersonIds = odPersonAnswerList.stream().map(DtoOdPersonAnswer::getPersonId).distinct().collect(Collectors.toList());
                List<DtoOdPerson> odPersonList = dtoOdPersonList.stream().filter(p -> odPersonIds.contains(p.getId())).collect(Collectors.toList());
                stationarySourceResult = this.initStationarySourceInfo(labGroup, labSeqList, odPersonAnswerList, odPersonList);

            } else {
                DtoStationarySourceResult finalStationarySourceResult = stationarySourceResult;
                List<DtoStationarySourceResultDetail> sourceResultDetailList = stationarySourceResultDetails.stream().filter(p -> p.getResultId().equals(finalStationarySourceResult.getId())).collect(Collectors.toList());
                List<String> odPersonIds = sourceResultDetailList.stream().map(DtoStationarySourceResultDetail::getPersonId).distinct().collect(Collectors.toList());
                List<DtoOdPerson> odPersonList = dtoOdPersonList.stream().filter(p -> odPersonIds.contains(p.getId())).collect(Collectors.toList());
                // 嗅辨员次序赋值
                for (DtoStationarySourceResultDetail resultDetail : sourceResultDetailList) {
                    odPersonList.stream().filter(p -> p.getId().equals(resultDetail.getPersonId())).findFirst().ifPresent(per -> {
                        resultDetail.setPersonSn(per.getPersonSn());
                    });
                }
                // 嗅辨员次序排序
                sourceResultDetailList.sort(Comparator.comparing(DtoStationarySourceResultDetail::getPersonSn));
                stationarySourceResult.setSourceResultDetailList(sourceResultDetailList);
            }
            labGroup.setStationarySourceResult(stationarySourceResult);
        }
        // 按照最终分析结果次序排序
        labGroups.sort(Comparator.comparing(DtoLabGroup::getCalculateSn));
        return labGroups;
    }

    @Override
    @Transactional
    protected void calculateSampleResult(DtoSampleResult sampleResult, List<DtoLabGroup> labGroupList) {

        List<DtoStationarySourceResult> stationarySourceResults = labGroupList.stream().map(DtoLabGroup::getStationarySourceResult).collect(Collectors.toList());
        List<String[]> thresholdArrList = new ArrayList<>();
        // 获取需要计算的两组个人阈值集合
        List<DtoStationarySourceResultDetail> addStationarySourceResultDetails = new ArrayList<>();
        for (DtoStationarySourceResult stationarySourceResult : stationarySourceResults) {
            List<DtoStationarySourceResultDetail> sourceResultDetailList = stationarySourceResult.getSourceResultDetailList();
            thresholdArrList.add(sourceResultDetailList.stream().map(DtoStationarySourceResultDetail::getThreshold).toArray(String[]::new));
            addStationarySourceResultDetails.addAll(sourceResultDetailList);
        }
        // 此处汇总前做了限制，必然有两组
        if (thresholdArrList.size() == 2) {
            // 第一组阈值
            String[] thresholdArr1 = thresholdArrList.get(0);
            // 第二组阈值
            String[] thresholdArr2 = thresholdArrList.get(1);
            try {
                // X平均阈值
                String avgThreshold = StationarySourceCalculationUtil.calculateAvgThreshold(3, thresholdArr1, thresholdArr2);
                sampleResult.setXValue(avgThreshold);
                // R相关系数
                sampleResult.setRValue(StationarySourceCalculationUtil.calculateRelationParam(thresholdArr1, thresholdArr2, 4));
                //T检验结果
                String tValue = StationarySourceCalculationUtil.calculateInspectionStatistic(thresholdArr1, thresholdArr2, 9);
//                String tValue = "2.183";
                sampleResult.setTValue(tValue);
                boolean isPassed = false;
                // 根据t值校验，t<3.182则通过
                if (MathUtil.isNumber(tValue) && (new BigDecimal(tValue).compareTo(T_CHECK_VALUE) < 0)) {
                    isPassed = true;
                    // 臭气浓度
                    sampleResult.setOdourConsistence(StationarySourceCalculationUtil.calculateStenchDensity(0, avgThreshold));
                }
                sampleResult.setIsPassed(isPassed);
            } catch (Exception e) {
                throw new BaseException("污染源结果计算错误！" + e.getMessage());
            }
            // 更新汇总结果
            stationarySourceResultRepository.save(stationarySourceResults);
            stationarySourceResultDetailRepository.save(addStationarySourceResultDetails);
        }
    }

    /**
     * 初始化固定源结果信息
     *
     * @param labGroup           实验组对象
     * @param labSeqList         实验次序
     * @param odPersonAnswerList 嗅辨实验分析数据
     * @param odPersonList       嗅辨人员
     * @return
     */
    private DtoStationarySourceResult initStationarySourceInfo(DtoLabGroup labGroup,
                                                               List<DtoLabSeq> labSeqList,
                                                               List<DtoOdPersonAnswer> odPersonAnswerList,
                                                               List<DtoOdPerson> odPersonList) {
        DtoStationarySourceResult stationarySourceResult = new DtoStationarySourceResult();
        List<String> labSeqListOfGroup = labSeqList.stream().filter(p -> p.getGroupId().equals(labGroup.getId())).map(DtoLabSeq::getId).collect(Collectors.toList());
        // 实验组下所有嗅辨员分析数据，根据嗅辨员分组
        List<DtoOdPersonAnswer> personAnswerList = odPersonAnswerList.stream().filter(p -> labSeqListOfGroup.contains(p.getLabSeqId())).collect(Collectors.toList());
        Map<String, List<DtoOdPersonAnswer>> personAnswerGroupMap = personAnswerList.stream().collect(Collectors.groupingBy(DtoOdPersonAnswer::getPersonId));
        List<DtoStationarySourceResultDetail> stationarySourceResultDetails = new ArrayList<>();
        List<String> thresholds = new ArrayList<>();
        for (Map.Entry<String, List<DtoOdPersonAnswer>> entry : personAnswerGroupMap.entrySet()) {
            List<DtoOdPersonAnswer> personAnswers = entry.getValue();
            String personId = entry.getKey();
            DtoOdPerson odPerson = new DtoOdPerson();
            Optional<DtoOdPerson> personOptional = odPersonList.stream().filter(p -> p.getId().equals(personId)).findFirst();
            if (personOptional.isPresent()) {
                odPerson = personOptional.get();
            }
            // 检查嗅辨分析数据
            checkPersonAnswer(odPerson, personAnswers);
            // 获取个人阈值
            String threshold = getThreshold(personAnswers);

            // 固定源结果详情
            DtoStationarySourceResultDetail sourceResultDetail = new DtoStationarySourceResultDetail();
            sourceResultDetail.setResultId(stationarySourceResult.getId());
            sourceResultDetail.setPersonId(personId);
            sourceResultDetail.setThreshold(threshold);
            sourceResultDetail.setPersonSn(odPerson.getPersonSn());
            stationarySourceResultDetails.add(sourceResultDetail);
            thresholds.add(threshold);
        }
        stationarySourceResultDetails.sort(Comparator.comparing(DtoStationarySourceResultDetail::getPersonSn));
        stationarySourceResult.setGroupId(labGroup.getId());
        // 平均阈值
        String[] thresholdArr = thresholds.toArray(new String[0]);
        stationarySourceResult.setAvgThreshold(StationarySourceCalculationUtil.calculateAvgThreshold(3, thresholdArr));
        // 标准偏差
        stationarySourceResult.setRsd(StationarySourceCalculationUtil.calculateSampleStandardDeviation(4, thresholdArr));
        stationarySourceResult.setSourceResultDetailList(stationarySourceResultDetails);
        return stationarySourceResult;
    }

    /**
     * 检查嗅辨分析数据是否可以汇总
     *
     * @param odPerson      嗅辨员
     * @param personAnswers 嗅辨分析数据
     */
    private void checkPersonAnswer(DtoOdPerson odPerson, List<DtoOdPersonAnswer> personAnswers) {
        // 错误数据
        List<DtoOdPersonAnswer> error = personAnswers.stream().filter(p -> EnumOds.EnumEvaluation.错误.getValue().equals(p.getEvaluation())).collect(Collectors.toList());
        if (StringUtil.isEmpty(error)) {
            throw new BaseException("嗅辩员" + odPerson.getPersonSn() + "实验数据有误，无法汇总");
        }
    }

    /**
     * 获取固定源个人阈值
     *
     * @param personAnswers 嗅辨分析数据集合
     * @return 个人阈值
     */
    private String getThreshold(List<DtoOdPersonAnswer> personAnswers) {
        // 个人正解最大稀释倍数，当每组实验中没有正确答案，证明第一轮答错，稀释倍数默认成1
        Integer maxDilutionRate = personAnswers.stream().filter(p -> p.getPersonAnswer().equals(p.getStandardAnswer()))
                .map(DtoOdPersonAnswer::getDilutionRate)
                .max(Comparator.comparing(p -> p)).orElse(1);
        // 个人误解稀释倍数
        Integer minDilutionRate = personAnswers.stream().filter(p -> !p.getPersonAnswer().equals(p.getStandardAnswer()))
                .map(DtoOdPersonAnswer::getDilutionRate)
                .max(Comparator.comparing(p -> p)).orElse(0);
        // 计算个人阈值
        return StationarySourceCalculationUtil.calculateThreshold(String.valueOf(maxDilutionRate), String.valueOf(minDilutionRate), 2);
    }
}
