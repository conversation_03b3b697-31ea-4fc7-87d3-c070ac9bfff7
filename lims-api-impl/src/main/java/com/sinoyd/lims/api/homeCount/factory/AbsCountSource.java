package com.sinoyd.lims.api.homeCount.factory;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.api.service.FieldMonitoringService;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.pro.enums.EnumPRO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 移动端首页代办处理
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Component
public abstract class AbsCountSource {

    protected FieldMonitoringService fieldMonitoringService;

    protected JdbcTemplate jdbcTemplate;

    /**
     * 模块代办数字
     *
     * @return 代办数字
     */
    public abstract long homeCount();

    /**
     * 获取现场任务代办数字
     *
     * @param astrict        查询页面（1 ：数据查询 0：现场监测 2：应急监测）
     * @param projectTypeIds 项目类型
     * @return 项目个数
     */
    protected long getProjectList(int astrict, String projectTypeIds) {
        StringBuilder sam2projectIds = new StringBuilder("SELECT distinct t.id FROM TB_PRO_Project t, TB_PRO_Sample sam " +
                " where 1=1   AND sam.projectId = t.id  AND sam.receiveId = '00000000-0000-0000-0000-000000000000'  AND sam.isDeleted = 0");
        List<String> samProIds = jdbcTemplate.query(sam2projectIds.toString(), (resultSet, i) -> resultSet.getString("id"));
        StringBuilder rec2projectIds = new StringBuilder("  SELECT distinct t.id FROM TB_PRO_Project t, TB_PRO_ReceiveSampleRecord rec " +
                " WHERE 1 = 1  and rec.projectId = t.id AND Rec.uploadStatus = 0");
        List<String> recProIds = jdbcTemplate.query(rec2projectIds.toString(), (resultSet, i) -> resultSet.getString("id"));
        List<String> projectIds = new ArrayList<>(samProIds);
        projectIds.addAll(recProIds);
        StringBuilder stringBuilder = new StringBuilder("select p.id from TB_PRO_Project p");
        stringBuilder.append(" join TB_PRO_ProjectPlan pl on p.id = pl.projectId ");
        stringBuilder.append(" WHERE  p.orgId = ? AND pl.isMakePlan = 1  and p.isDeleted = 0");
        setProjectStatus(astrict, stringBuilder);
        setProjectTypeIds(projectTypeIds, stringBuilder);
        List<String> proIds = jdbcTemplate.query(stringBuilder.toString(),
                new String[]{PrincipalContextUser.getPrincipal().getOrgId()},
                (resultSet, i) -> resultSet.getString("id"));
        Collection collection = CollectionUtils.intersection(projectIds, proIds);
        return collection.size();
    }

    /**
     * 应急项目代办个数
     *
     * @param projectTypeIds 项目类型ids
     * @return 项目个数
     */
    protected long getRecordList(String projectTypeIds) {
        StringBuilder stringBuilder = new StringBuilder("select count(p.id) as count from");
        stringBuilder.append(" TB_PRO_ReceiveSampleRecord r,TB_PRO_Project p,TB_PRO_ProjectPlan pl");
        stringBuilder.append(" where 1=1 and p.orgId = ?");
        stringBuilder.append(" and p.id = pl.projectId and pl.isMakePlan = 0 ");
        stringBuilder.append(" and p.id = r.projectId ");
        setProjectStatus(0, stringBuilder);
        setProjectTypeIds(projectTypeIds, stringBuilder);
        stringBuilder.append(" and r.uploadStatus = 0");
        stringBuilder.append(" and (r.senderId = ? or exists (select 1 from TB_PRO_SamplingPersonConfig sfc");
        stringBuilder.append(" where r.id = sfc.objectId and sfc.objectType = 1 and sfc.samplingPersonId = ?))");
        List<DtoTaskNum> retList = jdbcTemplate.query(stringBuilder.toString(),
                new String[]{PrincipalContextUser.getPrincipal().getOrgId(),
                        PrincipalContextUser.getPrincipal().getUserId()
                        , PrincipalContextUser.getPrincipal().getUserId()},
                (resultSet, i) -> new DtoTaskNum(resultSet.getLong("count")));
        return retList.get(0).getCount();
    }

    /**
     * 设置项目状态
     *
     * @param astrict       查询页面（1 ：数据查询 0：现场监测 2：应急监测）
     * @param stringBuilder 查询
     */
    private void setProjectStatus(int astrict, StringBuilder stringBuilder) {
        List<String> statusList = new ArrayList<>();
        for (EnumPRO.EnumProjectStatus status : EnumPRO.EnumProjectStatus.values()) {
            if (status.getValue() >= EnumPRO.EnumProjectStatus.开展中.getValue()) {
                statusList.add(status.name());
            }
        }
        if (new Integer(2).equals(astrict)) {
            statusList.add(EnumPRO.EnumProjectStatus.项目登记中.name());
        }

        if (StringUtil.isNotEmpty(statusList)) {
            StringBuilder proStatusIdBuilder = new StringBuilder();
            for (String proStatus : statusList) {
                proStatusIdBuilder.append("'").append(proStatus).append("',");
            }
            proStatusIdBuilder.deleteCharAt(proStatusIdBuilder.lastIndexOf(","));
            stringBuilder.append(" and p.status in (").append(proStatusIdBuilder).append(")");
        }
    }

    /**
     * 设置项目类型ids
     *
     * @param projectTypeIds 项目类型ids
     * @param stringBuilder  查询
     */
    private void setProjectTypeIds(String projectTypeIds, StringBuilder stringBuilder) {
        if (StringUtil.isNotEmpty(projectTypeIds)) {
            List<String> typeIds = Arrays.asList(projectTypeIds.split(",").clone());
            StringBuilder typeIdBuilder = new StringBuilder();
            for (String tId : typeIds) {
                typeIdBuilder.append("'").append(tId).append("',");
            }
            typeIdBuilder.deleteCharAt(typeIdBuilder.lastIndexOf(","));
            stringBuilder.append(" and p.projectTypeId in (").append(typeIdBuilder).append(")");
        }
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Autowired
    @Lazy
    public void setFieldMonitoringService(FieldMonitoringService fieldMonitoringService) {
        this.fieldMonitoringService = fieldMonitoringService;
    }
}
