package com.sinoyd.lims.lim.service;


import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelation;

import java.util.List;

/**
 * 分析项目关系
 * <AUTHOR>
 * @version V1.0.0 2019/1/14
 * @since V100R001
 */
public interface AnalyzeItemRelationService extends IBaseJpaService<DtoAnalyzeItemRelation, String> {

    /**
     * 根据分析项目id及类型获取相关的数据
     *
     * @param analyzeItemId 分析项目id
     * @param type          类型
     * @return 返回相关的数据
     */
    List<DtoAnalyzeItemRelation> findByAnalyzeItemIdAndType(String analyzeItemId, Integer type);
}