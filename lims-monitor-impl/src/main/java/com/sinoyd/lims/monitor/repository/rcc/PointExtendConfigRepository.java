package com.sinoyd.lims.monitor.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.monitor.dto.rcc.DtoPointExtendConfig;

import java.util.Collection;
import java.util.List;

/**
 * 例行点位拓展字段配置访问操作接口
 *
 * <AUTHOR>
 * @version V5.2.0 2023/06/12
 * @since V100R001
 */
public interface PointExtendConfigRepository extends IBaseJpaRepository<DtoPointExtendConfig,String> {

    /**
     * 根据点位类型获取对应的点位拓展字段配置
     *
     * @param pointTypes 点位类型
     * @return 点位拓展字段配置
     */
    List<DtoPointExtendConfig> findByPointTypeIn(Collection<String> pointTypes);
}
