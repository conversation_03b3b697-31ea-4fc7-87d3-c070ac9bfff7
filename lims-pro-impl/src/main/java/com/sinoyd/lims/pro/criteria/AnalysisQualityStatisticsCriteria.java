package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 分析质量统计的查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2020/02/10
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalysisQualityStatisticsCriteria extends BaseCriteria implements Serializable {

    /**
     * 分析开始时间
     */
    private String startTime;

    /***
     * 分析结束时间
     */
    private String endTime;

    /**
     * 项目类型ids
     */
    private List<String> projectTypeIds;

    /**
     * 测试项目的ids
     */
    private List<String> testIds;

    /**
     * 检测类型的id
     */
    private String sampleTypeId;

    /**
     * 检测类型的id
     */
    private List<String> sampleTypeIds = new ArrayList<>();

    /**
     * 页数
     */
    private Integer page = 1;

    /**
     * 每页条数
     */
    private Integer rows = 10;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 是否按照项目分页
     */
    private Boolean isProjectGroup = false;

    /**
     * 是否按照分析项目合并统计
     */
    private Boolean isAnalyzeItemMerge = Boolean.FALSE;

    /**
     * 是否检毕
     */
    private Boolean isComplete = false;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.analyzeTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtils.isNotNullAndEmpty(this.endTime)) {
            Calendar c = Calendar.getInstance();
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and a.analyzeTime < :endTime");
            values.put("endTime", c.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(this.sampleTypeId) && !this.sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and a.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", sampleTypeId);
        }
        if (this.sampleTypeIds.size() > 0) {
            condition.append(" and a.sampleTypeId in :sampleTypeIds");
            values.put("sampleTypeIds", sampleTypeIds);
        }
        if (StringUtil.isNotNull(this.testIds) && testIds.size() > 0) {
            condition.append(" and a.testId in :testIds");
            values.put("testIds", testIds);
        }
        if (StringUtil.isNotNull(this.projectTypeIds) && projectTypeIds.size() > 0) {
            condition.append(" and p.projectTypeId in :projectTypeIds");
            values.put("projectTypeIds", projectTypeIds);
        }
        if (StringUtils.isNotNullAndEmpty(this.projectId) && !this.projectId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and p.id = :projectId");
            values.put("projectId", projectId);
        }
        if (StringUtil.isNotNull(this.isComplete) && this.isComplete) {
            condition.append(" and a.isDataEnabled = :isDataEnabled");
            values.put("isDataEnabled", this.isComplete);
        }
        return condition.toString();
    }
}
