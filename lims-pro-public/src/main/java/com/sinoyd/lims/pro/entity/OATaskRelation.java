package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;


/**
 * OATaskRelation实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OATaskRelation")
 @Data
 public  class OATaskRelation implements BaseEntity,Serializable {
 
 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    /**
    * 审批任务id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("审批任务id")
	private String taskId;
    
    /**
    * 关联id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("关联id")
	private String objectId;
    
 }