package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoExplore;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.repository.ExploreRepository;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.service.ExploreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;


/**
 * Explore操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ExploreServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoExplore, String, ExploreRepository> implements ExploreService {

    private final ProjectRepository projectRepository;

    @Transactional
    @Override
    public List<DtoExplore> save(Collection<DtoExplore> entities) {
        String projectId = new ArrayList<>(entities).get(0).getProjectId();
        List<DtoExplore> exploreList = repository.findByProjectIdOrderByCreateDate(projectId);
        if (StringUtil.isEmpty(exploreList)) {
            //当前项目没有踏勘记录
            super.save(entities);
        } else {
            //当前项目存在踏勘记录，则旧记录执行更新操作，新记录执行保存操作
            List<String> exploreIds = exploreList.parallelStream().map(DtoExplore::getId).collect(Collectors.toList());
            //更新列表
            List<DtoExplore> updateList = entities.parallelStream().filter(p -> exploreIds.contains(p.getId())).collect(Collectors.toList());
            //新增列表
            List<DtoExplore> createList = entities.parallelStream().filter(p -> !exploreIds.contains(p.getId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(updateList)) {
                super.update(updateList);
            }
            if (StringUtil.isNotEmpty(createList)) {
                super.save(createList);
            }
        }
        //冗余受检方名称
        return loadProjectExplore(projectId);
    }

    @Override
    public void findByPage(PageBean<DtoExplore> pb, BaseCriteria exploreCriteria) {
        pb.setEntityName("DtoExplore a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, exploreCriteria);
    }

    @Override
    public List<DtoExplore> loadProjectExplore(String projectId) {
        DtoProject dtoProject = projectRepository.findOne(projectId);
        List<DtoExplore> dataList = repository.findByProjectIdOrderByCreateDate(projectId);
        //冗余受检方名称
        dataList.forEach(dto -> dto.setInspectedEnt(dtoProject.getInspectedEnt()));
        return dataList;
    }

    @Override
    public DtoExplore findAttachPath(String id) {
        DtoExplore explore = findOne(id);
        DtoProject dtoProject = projectRepository.findOne(explore.getProjectId());
        explore.setProjectCode(dtoProject.getProjectCode());
        return explore;
    }
}