package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.sampleFolder;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component(IFileNameConstant.SchemeSynchronizationKey.DELETED_SAMPLEFOLDER)
public class DeletedSampleFolderStrategy extends AbsSampleFolderStrategy {

    /**
     * 调整点位方案
     *
     * @param sampleFolderTemplateList 修改点位内容
     */
    @Override
    public void synchronizationSampleFolder(String projectId, List<DtoSampleFolderTemplate> sampleFolderTemplateList) {
        List<String> folderIds = sampleFolderTemplateList.stream().map(DtoSampleFolderTemplate::getSampleFolderId)
                .filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
        List<String> tempIds = sampleFolderTemplateList.stream().map(DtoSampleFolderTemplate::getId).collect(Collectors.toList());
        List<String> frequencyIds = samplingFrequencyTempRepository.findBySampleFolderTempIdIn(tempIds).stream()
                .map(DtoSamplingFrequencyTemp::getSamplingFrequencyId).distinct().collect(Collectors.toList());
        folderIds.addAll(frequencyIds);
        List<String> workSheetFolderIds = sampleFolderService.getWorkSheetIdsBySamplingFrequencyId(folderIds);
        sampleFolderService.deleteTimes(folderIds);
        workSheetFolderService.checkWorkSheetFolder(workSheetFolderIds);
    }
}
