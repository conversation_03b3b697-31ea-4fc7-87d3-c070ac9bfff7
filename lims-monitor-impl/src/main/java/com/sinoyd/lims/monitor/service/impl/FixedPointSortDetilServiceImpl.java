package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSortDetil;
import com.sinoyd.lims.monitor.repository.lims.FixedPointSortDetilRepository;
import com.sinoyd.lims.monitor.service.FixedPointSortDetilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;


/**
 * FixedPointSortDetil操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class FixedPointSortDetilServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFixedPointSortDetil, String, FixedPointSortDetilRepository> implements FixedPointSortDetilService {

    @Autowired
    private FixedPointSortDetilRepository fixedPointSortDetilRepository;

    @Override
    public void findByPage(PageBean<DtoFixedPointSortDetil> pb, BaseCriteria fixedPointSortDetilCriteria) {
        pb.setEntityName("DtoFixedPointSortDetil a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fixedPointSortDetilCriteria);
    }

    @Transactional
    @Override
    public DtoFixedPointSortDetil save(DtoFixedPointSortDetil entity) {
        //新增之前清空该排序的关联点位
        if (StringUtil.isNotEmpty(entity.getSortId())){
            fixedPointSortDetilRepository.deleteBySortId(entity.getSortId());
        }
        ArrayList<DtoFixedPointSortDetil> sortList = new ArrayList<>();
        //将前端传的点位集合，进行遍历
        List<String> list =  entity.getFixedPointIds();
        int i = 1;
        for (String fixedPointIds: list) {
            DtoFixedPointSortDetil dtoFixedPointSortDetil = new DtoFixedPointSortDetil();
            dtoFixedPointSortDetil.setSortId(entity.getSortId());
            dtoFixedPointSortDetil.setFixedPointId(fixedPointIds);
            dtoFixedPointSortDetil.setOrderNum(i--);
            sortList.add(dtoFixedPointSortDetil);
        }
        fixedPointSortDetilRepository.save(sortList);
        return entity;
    }
}