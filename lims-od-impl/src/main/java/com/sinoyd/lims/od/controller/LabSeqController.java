package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.LabSeqCriteria;
import com.sinoyd.lims.od.dto.DtoLabSeq;
import com.sinoyd.lims.od.service.LabSeqService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * labSeq服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例: labSeq服务")
@RestController
@RequestMapping("api/ods/labSeq")
@Validated
public class LabSeqController extends BaseJpaController<DtoLabSeq, String, LabSeqService> {

    /**
     * 分页动态条件查询LabSeq
     *
     * @param labSeqCriteria 条件参数
     * @return RestResponse<List < LabSeq>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoLabSeq>> findByPage(LabSeqCriteria labSeqCriteria) {
        PageBean<DtoLabSeq> pageBean = super.getPageBean();
        RestResponse<List<DtoLabSeq>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, labSeqCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询labSeq
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询labSeq", notes = "根据id查询labSeq")
    @GetMapping("/{id}")
    public RestResponse<DtoLabSeq> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoLabSeq> restResp = new RestResponse<>();
        DtoLabSeq entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增labSeq
     *
     * @param labSeq labSeq实体
     * @return 新增的labSeq实体
     */
    @ApiOperation(value = "新增labSeq", notes = "新增labSeq")
    @PostMapping("")
    public RestResponse<DtoLabSeq> create(@Validated @RequestBody DtoLabSeq labSeq) {
        RestResponse<DtoLabSeq> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoLabSeq data = service.save(labSeq);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新labSeq
     *
     * @param labSeq labSeq实体
     * @return 更新后的labSeq实体
     */
    @ApiOperation(value = "更新labSeq", notes = "更新labSeq")
    @PutMapping("")
    public RestResponse<DtoLabSeq> update(@Validated @RequestBody DtoLabSeq labSeq) {
        RestResponse<DtoLabSeq> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoLabSeq data = service.update(labSeq);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }

    /**
     * 初始化实验组数据
     *
     * @param labSeq 实验组临时传输实体
     * @return 初始化实验组数据
     */
    @ApiOperation(value = "初始化实验组数据", notes = "初始化实验组数据")
    @PostMapping("/init")
    public RestResponse<DtoLabSeq> initLab(@RequestBody DtoLabSeq labSeq) {
        RestResponse<DtoLabSeq> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.initLab(labSeq));
        return restResponse;
    }

    /**
     * 根据样品id查询实验组和次序最大数
     *
     * @param sampleId 样品id
     * @return
     */
    @ApiOperation(value = "根据样品id查询实验组和次序最大数", notes = "根据样品id查询实验组和次序最大数")
    @GetMapping("/maxSn/{sampleId}")
    public RestResponse<Map<String, Object>> findMaxSn(@PathVariable(name = "sampleId") String sampleId) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        Map<String, Object> map = service.findMaxSn(sampleId);
        restResp.setData(map);
        restResp.setRestStatus(StringUtil.isNull(map) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 开始实验
     *
     * @param labSeq 实验次序实体
     * @return
     */
    @ApiOperation(value = "开始实验", notes = "开始实验")
    @PostMapping("/startLab")
    public RestResponse<DtoLabSeq> startLab(@Validated @RequestBody DtoLabSeq labSeq) {
        RestResponse<DtoLabSeq> restResp = new RestResponse<>();
        restResp.setData(service.startLab(labSeq));
        restResp.setRestStatus(ERestStatus.SUCCESS);
        return restResp;
    }


    /**
     * 刷新解答
     *
     * @param labSeqId 实验次序id
     * @return 实验次序
     */
    @ApiOperation(value = "刷新解答", notes = "刷新解答")
    @PostMapping("/refresh/{labSeqId}")
    public RestResponse<DtoLabSeq> refresh(@PathVariable(name = "labSeqId") String labSeqId) {
        RestResponse<DtoLabSeq> restResp = new RestResponse<>();
        restResp.setData(service.refreshAnswer(labSeqId));
        restResp.setRestStatus(ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 检查是否存在相同稀释倍数
     *
     * @param labSeq 检查是否存在相同稀释倍数
     * @return 相同稀释倍数的次序
     */
    @ApiOperation(value = "检查是否存在相同稀释倍数", notes = "检查是否存在相同稀释倍数")
    @PostMapping("/existsDilutionRat")
    public RestResponse<String> getExistsDilutionRat(@Validated @RequestBody DtoLabSeq labSeq) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.getExistsDilutionRat(labSeq));
        restResp.setRestStatus(ERestStatus.SUCCESS);
        return restResp;
    }

}
