package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ProjectApproval;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * DtoProjectApproval实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ProjectApproval")
@Data
@DynamicInsert
public class DtoProjectApproval extends ProjectApproval {

    /**
     * 方案id
     */
    @Transient
    private List<String> approveIds;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目登记时间
     */
    @Transient
    private Date inputTime;

    /**
     * 项目类型id
     */
    @Transient
    private String projectTypeId;

    /**
     * 委托方
     */
    @Transient
    private String customerName;

    /**
     * 受检方
     */
    @Transient
    private String inspectedEnt;

    /**
     * 项目负责人
     */
    @Transient
    private String leaderName;

    /**
     * 项目类型
     */
    @Transient
    private String projectTypeName;

    /**
     * 项目状态
     */
    @Transient
    private String status;

    /**
     * 审核人
     */
    @Transient
    private String approvePersonName;

    public DtoProjectApproval(){}

    public DtoProjectApproval(String id, String projectId, Date approveDate, String modifyStatus, String approvePersonId, String comment,
                              String projectName, String projectCode, Date inputTime, String projectTypeId, String customerName,
                              String inspectedEnt, String status) {
        setId(id);
        setProjectId(projectId);
        setApproveDate(approveDate);
        setModifyStatus(modifyStatus);
        setApprovePersonId(approvePersonId);
        setComment(comment);
        setProjectName(projectName);
        setProjectCode(projectCode);
        setInputTime(inputTime);
        setProjectTypeId(projectTypeId);
        setCustomerName(customerName);
        setInspectedEnt(inspectedEnt);
        setStatus(status);
    }
}
