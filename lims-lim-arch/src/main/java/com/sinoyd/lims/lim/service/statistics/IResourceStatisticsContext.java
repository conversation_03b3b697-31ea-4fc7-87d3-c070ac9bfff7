package com.sinoyd.lims.lim.service.statistics;


import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.lims.lim.dto.customer.DtoResourceStatistics;

import java.util.List;

/**
 * 资源统计上下文接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/22
 */
public interface IResourceStatisticsContext {

    /**
     * 资源统计
     *
     * @return 结果
     */
    List<DtoResourceStatistics> statistics();

    /**
     * 获取耗材过期详情
     * @return 消耗品列表
     */
    List<DtoConsumableDetail> getOverDueData();

    /**
     * 获取耗材低库存详情
     * @return 消耗品列表
     */
    List<DtoConsumable> getLowInventoryData();
}
