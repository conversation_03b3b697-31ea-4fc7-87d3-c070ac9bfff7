package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.customer.DtoSampleTemp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ReportDetailService;
import com.sinoyd.lims.pro.dto.DtoReportDetail;
import com.sinoyd.lims.pro.dto.customer.DtoReportDetailQuery;
import com.sinoyd.boot.common.dto.RestResponse;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;

import java.util.List;


/**
 * 报告明细服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/13
 * @since V100R001
 */
 @Api(tags = "示例: ReportDetail服务")
 @RestController
 @RequestMapping("api/pro/reportDetail")
 public class ReportDetailController extends BaseJpaController<DtoReportDetail, String,ReportDetailService> {

    /**
     * 查询样品
     *
     * @param queryDto 查询dto
     * @return RestResponse<List < DtoSample>>
     */
    @ApiOperation(value = "查询样品", notes = "查询样品")
    @PostMapping(path = "/samples")
    public RestResponse<List<DtoSampleTemp>> query(@RequestBody DtoReportDetailQuery queryDto) {
        RestResponse<List<DtoSampleTemp>> restResponse = new RestResponse<>();
        List<DtoSampleTemp> samples = service.queryMergeItemName(queryDto);
        restResponse.setRestStatus(samples.size() == 0 ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(samples);
        restResponse.setCount(samples.size());
        return restResponse;
    }

    /**
     * 查询样品
     *
     * @param queryDto 查询dto
     * @return RestResponse<List < DtoSample>>
     */
    @ApiOperation(value = "查询样品", notes = "查询样品")
    @PostMapping(path = "/reportSamples")
    public RestResponse<List<DtoSampleTemp>> queryReport(@RequestBody DtoReportDetailQuery queryDto) {
        RestResponse<List<DtoSampleTemp>> restResponse = new RestResponse<>();
        List<DtoSampleTemp> samples = service.queryReport(queryDto);
        restResponse.setRestStatus(samples.size() == 0 ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(samples);
        restResponse.setCount(samples.size());
        return restResponse;
    }

    /**
     * 批量修改报告明细
     *
     * @param report 实体列表
     * @return RestResponse<DtoReport>
     */
    @ApiOperation(value = "批量修改报告明细", notes = "批量修改报告明细")
    @PutMapping
    public RestResponse<List<DtoReportDetail>> update(@RequestBody DtoReport report) {
        RestResponse<List<DtoReportDetail>> restResponse = new RestResponse<>();
        restResponse.setData(service.batchUpdate(report));
        return restResponse;
    }

    /**
     * "批量删除报告明细
     *
     * @param report 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除报告明细", notes = "批量删除报告明细")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody DtoReport report) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.DeleteByRepIdTypeAndObjIds(report);
        restResp.setCount(count);
        return restResp;
    }
}