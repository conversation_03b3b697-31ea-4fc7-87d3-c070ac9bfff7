package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.service.AnalyseDataFutureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 分析数据多线程操作接口实现
 *
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/04/28
 */
@Service
@Slf4j
public class AnalyseDataFutureServiceImpl implements AnalyseDataFutureService {


    private AnalyseDataRepository analyseDataRepository;

    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<List<DtoAnalyseData>> getListBySampleIdIn(List<String> sampleIds) {
        log.info("===============子线程名称 {}======================", Thread.currentThread().getName());
        return new AsyncResult<>(analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds));
    }


    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }
}
