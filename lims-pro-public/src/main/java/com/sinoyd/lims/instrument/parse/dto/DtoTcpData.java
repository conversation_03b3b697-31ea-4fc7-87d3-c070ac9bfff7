package com.sinoyd.lims.instrument.parse.dto;

import com.sinoyd.lims.instrument.parse.entity.TcpData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 仪器解析数据传输实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/27
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Tcp_Datas")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoTcpData extends TcpData {
}