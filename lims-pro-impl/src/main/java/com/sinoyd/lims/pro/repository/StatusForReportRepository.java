package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoStatusForReport;

import java.util.Collection;
import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.transaction.annotation.Transactional;


/**
 * 报告状态数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/12
 * @since V100R001
 */
public interface StatusForReportRepository extends IBaseJpaPhysicalDeleteRepository<DtoStatusForReport, String> {

    /**
     * 查询指定报告的状态信息
     *
     * @param reportId 报告id
     * @return 对应报告下的状态信息
     */
    List<DtoStatusForReport> findByReportId(String reportId);

    /**
     * 删除指定报告的状态
     *
     * @param reportId 报告id
     */
    @Transactional
    void deleteByReportId(String reportId);

    /**
     * 删除指定报告的状态
     *
     * @param reportIds 报告id集合
     */
    @Transactional
    void deleteByReportIdIn(List<String> reportIds);

    /**
     * 查询指定报告的状态信息
     *
     * @param reportIds 报告ids
     * @return 对应报告下的状态信息
     */
    List<DtoStatusForReport> findByReportIdIn(Collection<String> reportIds);

    /**
     * 查询指定报告的状态信息
     *
     * @param reportIds 报告ids
     * @param module    模块
     * @return 对应报告下的状态信息
     */
    List<DtoStatusForReport> findByReportIdInAndModule(Collection<String> reportIds, String module);
}