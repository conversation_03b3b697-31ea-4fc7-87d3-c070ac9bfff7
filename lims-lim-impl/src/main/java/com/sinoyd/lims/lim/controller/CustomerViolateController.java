package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.CustomerViolateService;
import com.sinoyd.lims.lim.criteria.CustomerViolateCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoCustomerViolate;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * CustomerViolate服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
 @Api(tags = "示例: CustomerViolate服务")
 @RestController
 @RequestMapping("api/lim/customerViolate")
 @Validated
 public class CustomerViolateController extends BaseJpaController<DtoCustomerViolate, String,CustomerViolateService> {


    /**
     * 分页动态条件查询CustomerViolate
     * @param customerViolateCriteria 条件参数
     * @return RestResponse<List<CustomerViolate>>
     */
     @ApiOperation(value = "分页动态条件查询CustomerViolate", notes = "分页动态条件查询CustomerViolate")
     @GetMapping
     public RestResponse<List<DtoCustomerViolate>> findByPage(CustomerViolateCriteria customerViolateCriteria) {
         PageBean<DtoCustomerViolate> pageBean = super.getPageBean();
         RestResponse<List<DtoCustomerViolate>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, customerViolateCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询CustomerViolate
     * @param id 主键id
     * @return RestResponse<DtoCustomerViolate>
     */
     @ApiOperation(value = "按主键查询CustomerViolate", notes = "按主键查询CustomerViolate")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoCustomerViolate> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoCustomerViolate> restResponse = new RestResponse<>();
         DtoCustomerViolate customerViolate = service.findOne(id);
         restResponse.setData(customerViolate);
         restResponse.setRestStatus(StringUtil.isNull(customerViolate) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增CustomerViolate
     * @param customerViolate 实体列表
     * @return RestResponse<DtoCustomerViolate>
     */
     @ApiOperation(value = "新增CustomerViolate", notes = "新增CustomerViolate")
     @PostMapping
     public RestResponse<DtoCustomerViolate> create(@Validated @RequestBody DtoCustomerViolate customerViolate) {
         RestResponse<DtoCustomerViolate> restResponse = new RestResponse<>();
         restResponse.setData(service.save(customerViolate));
         return restResponse;
      }

     /**
     * 新增CustomerViolate
     * @param customerViolate 实体列表
     * @return RestResponse<DtoCustomerViolate>
     */
     @ApiOperation(value = "修改CustomerViolate", notes = "修改CustomerViolate")
     @PutMapping
     public RestResponse<DtoCustomerViolate> update( @Validated @RequestBody DtoCustomerViolate customerViolate) {
         RestResponse<DtoCustomerViolate> restResponse = new RestResponse<>();
         restResponse.setData(service.update(customerViolate));
         return restResponse;
      }

    /**
     * "根据id批量删除CustomerViolate
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除CustomerViolate", notes = "根据id批量删除CustomerViolate")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }