-- 新增无组织空白样组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('184270cb-046d-48e8-be2d-553eac915c38', 'dtUnOrgToKbStdTable', '标准版无组织空白样检测结果表组件（批次）', 'dtUnOrgToKbStdTable',
        'dtUnOrgToKbSource', 0, 0, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-10-11 13:20:29', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-10-11 13:20:29', '1', '0', 0, 0, 0, 1);

-- 无组织气报告模板配置调整
UPDATE TB_LIM_ReportModule
SET sonTableJson = '["dtUnOrgToHeadStdTable", "dtUnOrgToStdTable", "dtUnOrgToCpdStdTable", "dtUnOrgToKbStdTable"]'
WHERE moduleCode = 'unOrgToStdDataSource';