package com.sinoyd.lims.lim.data.qcconfig.strategy;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.lims.lim.data.qcconfig.strategy.base.AbsQCConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 采样器空白接口
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/14
 */
@Component
@DependsOn({"springContextAware"})
@Slf4j
public class CYQKBQCConfig extends AbsQCConfig {
    private QualityControlLimitRepository qualityControlLimitRepository;

    /**
     * 查询此质控类型的所有测试项目关联的数据
     *
     * @param testId 测试项目编号
     * @return 查询结果
     */
    @Override
    public List<DtoQualityControlLimit> queryData(String testId) {
        return qualityControlLimitRepository.findByTestIdAndQcType(testId,getQcType());
    }

    /**
     * 获取质控类型
     *
     * @return 质控类型
     */
    @Override
    public Integer getQcType() {
        return EnumLIM.EnumQCType.采样介质空白.getValue();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return 5;
    }

    /**
     * 获取质控类型名称
     *
     * @return 质控类型名称
     */
    @Override
    public String getQcTypeName() {
        return EnumLIM.EnumQCType.采样介质空白.name();
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }
}
