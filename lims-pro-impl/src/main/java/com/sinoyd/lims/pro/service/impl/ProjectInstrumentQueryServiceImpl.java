package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.InstrumentAccessRecordCriteria;
import com.sinoyd.lims.lim.criteria.ProjectInstrumentCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.lims.lim.dto.customer.DtoProjectInstrumentAccess;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails;
import com.sinoyd.lims.lim.entity.ProjectInstrumentDetails;
import com.sinoyd.lims.lim.repository.lims.ProjectInstrumentDetailsRepository;
import com.sinoyd.lims.lim.repository.lims.ProjectInstrumentRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.service.ProjectInstrumentQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * ProjectInstrument查询操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Service
public class ProjectInstrumentQueryServiceImpl extends BaseJpaServiceImpl<DtoProjectInstrument, String, ProjectInstrumentRepository> implements ProjectInstrumentQueryService {

    @Autowired
    private ProjectInstrumentDetailsRepository projectInstrumentDetailsRepository;

    @Autowired
    private InstrumentRepository instrumentRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CodeService codeService;

    @Autowired
    private PersonService personService;


    @Override
    public void findByPage(PageBean<DtoProjectInstrument> pb, BaseCriteria projectInstrumentCriteria) {
        ProjectInstrumentCriteria criteria = (ProjectInstrumentCriteria) projectInstrumentCriteria;
        String projectNameCode = criteria.getProjectNameCode();
        //项目名称编码关键字查询条件存在时，将其转换为 projectIds, projectName 查询条件
        if (StringUtil.isNotEmpty(projectNameCode)) {
            criteria.setProjectName(projectNameCode);
            Map<String, Object> queryValues = new HashMap<>();
            String queryStr = "select id from DtoProject a where (a.projectName like :projectNameCode or a.projectCode like :projectNameCode) and a.isDeleted = 0";
            queryValues.put("projectNameCode", "%" + projectNameCode + "%");
            List<String> projectIdList = comRepository.find(queryStr, queryValues);
            if (StringUtil.isNotEmpty(projectIdList)) {
                criteria.setProjectIds(projectIdList);
            }
        }
        pb.setEntityName("DtoProjectInstrument a");
        pb.setSelect(" select new com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument(a.id,a.useDate,a.projectId,a.projectName," +
                "a.outQualified,a.intQualified,a.userNames,a.administratorName,a.createDate) ");
        super.findByPage(pb, projectInstrumentCriteria);
        List<DtoProjectInstrument> projectInstrumentList = pb.getData();
        List<String> projectInstrumentIdList = new ArrayList<>();
        projectInstrumentList.forEach(p -> projectInstrumentIdList.add(p.getId()));
        List<String> projectIdList = new ArrayList<>();
        projectInstrumentList.forEach(p -> {
            if (StringUtils.isNotNullAndEmpty(p.getProjectId())) {
                projectIdList.add(p.getProjectId());
            }
        });
        //获取对应的仪器设备名称及型号
        List<DtoProjectInstrumentDetails> dtoProjectInstrumentDetailsList = new ArrayList<>();
        if (StringUtil.isNotEmpty(projectInstrumentIdList)) {
            Map<String, Object> values = new HashMap<>();
            values.put("projectInstrumentIdList", projectInstrumentIdList);
            dtoProjectInstrumentDetailsList = comRepository.find("select new com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails(a.projectInstrumentId,b.instrumentName," +
                    "b.instrumentsCode, a.isStorage) from DtoProjectInstrumentDetails a, DtoInstrument b where a.instrumentId = b.id and b.isDeleted = 0  " +
                    "and a.projectInstrumentId in :projectInstrumentIdList", values);
        }
        Map<String, List<DtoProjectInstrumentDetails>> projectInstrumentDetailsListMap = dtoProjectInstrumentDetailsList.stream()
                .collect(Collectors.groupingBy(DtoProjectInstrumentDetails::getProjectInstrumentId));
        //遍历仪器出入库记录列表，设置每个记录的已入库仪器数量和仪器出库数量
        for (DtoProjectInstrument projectInstrument : projectInstrumentList) {
            //每一个仪器出入库记录至少会选择一个仪器，因此不用考虑key不存在的情况
            List<DtoProjectInstrumentDetails> detailsList = projectInstrumentDetailsListMap.getOrDefault(projectInstrument.getId(), new ArrayList<>());
            //获取已入库的仪器数量
            List<DtoProjectInstrumentDetails> storageDetailsList = detailsList.stream().filter(ProjectInstrumentDetails::getIsStorage).collect(Collectors.toList());
            projectInstrument.setInCount(storageDetailsList.size());
            projectInstrument.setOutCount(detailsList.size());
            //判断是否入库完成
            projectInstrument.setIsInComplete(false);
            if (projectInstrument.getOutCount().equals(projectInstrument.getInCount())) {
                projectInstrument.setIsInComplete(true);
            }
        }
        if (StringUtil.isNotNull(criteria.getStorageStatus())) {
            //根据前端传递的出入库状态条件进行过滤
            int storageStatus = criteria.getStorageStatus();
            if (storageStatus != 0) {
                List<String> remainProInstIdList = new ArrayList<>();
                for (DtoProjectInstrument projectInstrument : projectInstrumentList) {
                    int inCount = projectInstrument.getInCount();
                    if ((storageStatus == 1 && inCount == 0) || (storageStatus == 2 && inCount > 0 && inCount < projectInstrument.getOutCount())
                            || (storageStatus == 3 && inCount == projectInstrument.getOutCount())) {
                        remainProInstIdList.add(projectInstrument.getId());
                    }
                }
                //过滤掉不满足入库状态条件的出入库记录
                projectInstrumentList = projectInstrumentList.stream().filter(p -> remainProInstIdList.contains(p.getId())).collect(Collectors.toList());
            }
        }

        //出入库记录id和其对应的仪器设备名称和型号列表的映射关系
        Map<String, String> proInsId2InsNameCodeMap = new HashMap<>();
        if (StringUtil.isNotEmpty(dtoProjectInstrumentDetailsList)) {
            dtoProjectInstrumentDetailsList.sort(Comparator.comparing(DtoProjectInstrumentDetails::getInstrumentName));
        }
        //拼接仪器设备名称及型号
        for (DtoProjectInstrumentDetails dto : dtoProjectInstrumentDetailsList) {
            String proInsId = dto.getProjectInstrumentId();
            String instName = StringUtil.isNotEmpty(dto.getInstrumentName()) ? dto.getInstrumentName() : "";
            String instCode = StringUtil.isNotEmpty(dto.getInstrumentsCode()) ? dto.getInstrumentsCode() : "";
            String curInsNameCode = instName + instCode;
            if (StringUtil.isNotEmpty(curInsNameCode)) {
                String newInsNameCode = proInsId2InsNameCodeMap.containsKey(proInsId) ? proInsId2InsNameCodeMap.get(proInsId) + "、" + curInsNameCode : curInsNameCode;
                proInsId2InsNameCodeMap.put(proInsId, newInsNameCode);
            }
        }
        projectInstrumentList.forEach(p -> p.setInstrumentNameCode(StringUtil.isNotEmpty(proInsId2InsNameCodeMap.get(p.getId()))
                ? proInsId2InsNameCodeMap.get(p.getId()) : ""));

        //设置projectInstrument对象的项目信息
        List<DtoProject> projectList = new ArrayList<>();
        if (StringUtil.isNotEmpty(projectIdList)) {
            StringBuilder sb = new StringBuilder();
            sb.append("select a from DtoProject a where 1=1");
            sb.append(" and a.isDeleted = 0");
            sb.append(" and a.id in :ids");
            Map<String, Object> projectValues = new HashMap<>();
            projectValues.put("ids", projectIdList);
            projectList = comRepository.find(sb.toString(), projectValues);
        }
        Map<String, DtoProject> proId2proMap = new HashMap<>();
        projectList.forEach(p -> proId2proMap.put(p.getId(), p));
        for (DtoProjectInstrument dto : projectInstrumentList) {
            if (StringUtil.isNull(dto.getProjectCode())) {
                dto.setProjectCode("");
            }
            if (StringUtil.isEmpty(dto.getProjectName())) {
                dto.setProjectName("");
                DtoProject loopProject = proId2proMap.get(dto.getProjectId());
                if (StringUtil.isNotNull(loopProject)) {
                    dto.setProjectName(loopProject.getProjectName());
                    dto.setProjectCode(loopProject.getProjectCode());
                }
            }
        }
        //按照日期和项目编号倒序排序
        projectInstrumentList.sort(Comparator.comparing(DtoProjectInstrument::getUseDate).thenComparing(DtoProjectInstrument::getCreateDate).thenComparing(DtoProjectInstrument::getProjectCode).reversed());
        pb.setData(projectInstrumentList);
    }

    /**
     * 仪器出入库查询
     *
     * @param pb                             分页记录
     * @param projectInstrumentQueryCriteria 查询条件
     */
    @Override
    public void findByPageQuery(PageBean<DtoProjectInstrument> pb, BaseCriteria projectInstrumentQueryCriteria) {
        pb.setEntityName("DtoProjectInstrument a");
        pb.setSelect("select a ");
        comRepository.findByPage(pb, projectInstrumentQueryCriteria);
        List<DtoProjectInstrument> datas = pb.getData();
        fillProjectInstrumentMessage(datas);
        pb.setData(datas);
    }

    @Override
    public void findAccessRecordByPage(PageBean<DtoProjectInstrumentAccess> pb, BaseCriteria instrumentAccessRecordCriteria) {
        InstrumentAccessRecordCriteria accessCriteria = (InstrumentAccessRecordCriteria) instrumentAccessRecordCriteria;
        ProjectInstrumentCriteria criteria = new ProjectInstrumentCriteria();
        criteria.setInstrumentId(accessCriteria.getInstrumentId());
        criteria.setProjectNameCode(accessCriteria.getProjectNameCode());
        criteria.setStartTime(accessCriteria.getDtBegin());
        criteria.setEndTime(accessCriteria.getDtEnd());
        PageBean<DtoProjectInstrument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(pb.getRowsPerPage());
        pageBean.setRowsCount(pb.getRowsCount());
//        pageBean.setSort("a.useDate-");
        this.findByPage(pageBean, criteria);
        List<DtoProjectInstrument> projectInstrumentList = pageBean.getData();
        List<DtoProjectInstrumentAccess> projectInstrumentAccessList = new ArrayList<>();
        if (StringUtil.isNotEmpty(projectInstrumentList)) {
            for (DtoProjectInstrument dto : projectInstrumentList) {
                DtoProjectInstrumentAccess access = new DtoProjectInstrumentAccess();
                access.setAccessDate(dto.getUseDate());
                access.setProjectCode(dto.getProjectCode());
                access.setProjectName(dto.getProjectName());
                access.setOutQualified(dto.getOutQualified());
                access.setIntQualified(dto.getIntQualified());
                access.setUserNames(dto.getUserNames());
                access.setAdministratorName(dto.getAdministratorName());
                projectInstrumentAccessList.add(access);
            }
        }
        pb.setData(projectInstrumentAccessList);
    }


    /**
     * 按照主键查询仪器出入库记录
     */
    @Override
    public DtoProjectInstrument findOne(String key) {
        DtoProjectInstrument dtoProjectInstrument = super.findOne(key);
        if (StringUtil.isNull(dtoProjectInstrument) || dtoProjectInstrument.getIsDeleted()) {
            throw new BaseException("仪器出入库记录不存在!");
        }
//        //获取项目编码，项目名称(projectId存在时根据项目id获取，不存在时直接取projectName属性)
//        List<String> projectCodeList = new ArrayList<>();
//        List<String> projectIdList = null;
//        if (StringUtil.isNotEmpty(dtoProjectInstrument.getProjectId())) {
//            projectIdList = Arrays.asList(dtoProjectInstrument.getProjectId().split(";"));
//            List<DtoProject> projectList = projectRepository.findAll(projectIdList);
//            for (DtoProject project : projectList) {
//                projectCodeList.add(project.getProjectCode());
//            }
//        }
        List<String> projectIdList = StringUtil.isNotEmpty(dtoProjectInstrument.getProjectId())
                ? Arrays.asList(dtoProjectInstrument.getProjectId().split(";")) : new ArrayList<>();
        List<String> projectNameList = StringUtil.isNotEmpty(dtoProjectInstrument.getProjectName())
                ? Arrays.asList(dtoProjectInstrument.getProjectName().split("、")) : new ArrayList<>();
        List<DtoProject> projectList = StringUtil.isNotEmpty(projectIdList) ? projectRepository.findAll(projectIdList) : new ArrayList<>();
        List<String> projectCodeList = projectList.stream().map(DtoProject::getProjectCode).collect(Collectors.toList());
        List<Map<String, String>> projectInfoList = new ArrayList<>();
        if (StringUtil.isNotEmpty(projectNameList)) {
            for (String projectName : projectNameList) {
                Map<String, String> projectMap = new HashMap<>();
                projectMap.put("projectName", projectName);
                DtoProject tmpProject = projectList.stream().filter(p -> projectName.equals(p.getProjectName())).findFirst().orElse(null);
                projectMap.put("id", StringUtil.isNotNull(tmpProject) ? tmpProject.getId() : "");
                projectInfoList.add(projectMap);
            }
        }
        dtoProjectInstrument.setProjectList(projectInfoList);
        dtoProjectInstrument.setProjectCode(StringUtil.isNotEmpty(projectCodeList) ? String.join("、", projectCodeList) : "");
        dtoProjectInstrument.setProjectIdList(projectIdList);
        dtoProjectInstrument.setProjectNameList(StringUtil.isNotEmpty(dtoProjectInstrument.getProjectName())
                ? Arrays.asList(dtoProjectInstrument.getProjectName().split("、")) : null);

        List<DtoProjectInstrumentDetails> projectInstrumentDetailsList = projectInstrumentDetailsRepository.findByProjectInstrumentId(key);
        //获取仪器列表
        List<DtoInstrument> instrumentList = new ArrayList<>();
        List<String> instIdList = new ArrayList<>();
        projectInstrumentDetailsList.forEach(p -> instIdList.add(p.getInstrumentId()));
        if (StringUtil.isNotEmpty(instIdList)) {
            instrumentList = instrumentRepository.findByIds(instIdList);
        }
        List<DtoCode> instrumentTypeList = codeService.findCodes("LIM_InstrumentType");
        for (DtoInstrument dtoInstrument : instrumentList) {
            DtoCode dtoCode = instrumentTypeList.stream().filter(item -> item.getDictCode().equals(dtoInstrument.getInstrumentTypeId())).findFirst().orElse(null);
            // 仪器类型名称
            String instrumentTypeName = "";
            if (StringUtil.isNotNull(dtoCode)) {
                assert dtoCode != null;
                instrumentTypeName = dtoCode.getDictName();
            }
            dtoInstrument.setInstrumentTypeName(instrumentTypeName);
        }
        Map<String, DtoInstrument> instrumentMap = instrumentList.stream().collect(Collectors.toMap(DtoInstrument::getId, dto -> dto));
        List<DtoPerson> personList = personService.findAll();
        for (DtoProjectInstrumentDetails details : projectInstrumentDetailsList) {
            DtoInstrument instrument = instrumentMap.getOrDefault(details.getInstrumentId(), new DtoInstrument());
            Optional<DtoPerson> inPerson = personList.stream().filter(p -> p.getId().equals(details.getInPerson())).findFirst();
            inPerson.ifPresent(p -> details.setInPersonName(p.getCName()));
            Optional<DtoPerson> outPerson = personList.stream().filter(p -> p.getId().equals(details.getOutPerson())).findFirst();
            outPerson.ifPresent(p -> details.setOutPersonName(p.getCName()));
            details.setInstrumentName(instrument.getInstrumentName());
            details.setInstrumentsCode(instrument.getInstrumentsCode());
            details.setModel(instrument.getModel());
            //如果入库情况为-1 则将其置为null,便于前端展示
            if (details.getInQualified() == -1) {
                details.setInQualified(null);
            }
            details.setFactoryName(instrument.getFactoryName());
        }
        dtoProjectInstrument.setDetailsList(projectInstrumentDetailsList);
//        //若存在入库备注，则将入库备注信息添加到出库备注信息后面一起返回
//        String inRemarks = dtoProjectInstrument.getInRemarks();
//        if (StringUtil.isNotEmpty(inRemarks)) {
//            String outInRemarks = dtoProjectInstrument.getOutRemarks() + System.getProperty("line.separator") + inRemarks;
//            dtoProjectInstrument.setOutRemarks(outInRemarks);
//        }
        return dtoProjectInstrument;
    }

    /**
     * 填充仪器出入库基本信息
     *
     * @param datas 仪器出入库记录
     */
    protected void fillProjectInstrumentMessage(List<DtoProjectInstrument> datas) {
        List<String> proInstIds = datas.stream().map(DtoProjectInstrument::getId).collect(Collectors.toList());
        List<String> projectIds = datas.stream().filter(p -> StringUtil.isNotEmpty(p.getProjectId())
                && !UUIDHelper.GUID_EMPTY.equals(p.getProjectId())).map(DtoProjectInstrument::getProjectId)
                .distinct().collect(Collectors.toList());
        Set<String> projectIdSet = new HashSet<>();
        for (String projectIdStr : projectIds) {
            projectIdSet.addAll(Arrays.asList(projectIdStr.split(";")));
        }
        //项目信息
        List<DtoProject> projectList = StringUtil.isNotEmpty(projectIdSet) ? projectRepository.findAll(projectIdSet) : new ArrayList<>();
        Map<String, DtoProject> projectMap = projectList.stream().collect(Collectors.toMap(DtoProject::getId, dto -> dto));
        //出入库仪器详情
        List<DtoProjectInstrumentDetails> detailsList = projectInstrumentDetailsRepository.findByProjectInstrumentIdIn(proInstIds);
        List<String> instrIds = detailsList.stream().map(DtoProjectInstrumentDetails::getInstrumentId).distinct().collect(Collectors.toList());
        //仪器信息
        List<DtoInstrument> instrumentList = new ArrayList<>();
        if (instrIds.size() > 0) {
            instrumentList = instrumentRepository.findByIds(instrIds);
        }
        List<DtoInstrument> finalInstrumentList = instrumentList;
        datas.forEach(p -> {
            //当前出入库记录对应的项目
            List<String> projectCodeList = new ArrayList<>();
            String projectIdStr = StringUtil.isNotEmpty(p.getProjectId()) ? p.getProjectId() : "";
            String[] projectIdsForData = projectIdStr.split(";");
            for (String projectId : projectIdsForData) {
                DtoProject project = projectMap.get(projectId);
                if (StringUtil.isNotNull(project)) {
                    projectCodeList.add(project.getProjectCode());
                }
            }
            p.setProjectCode(StringUtil.isNotEmpty(projectCodeList) ? String.join("、", projectCodeList) : "");
            p.setProjectIdList(projectIdsForData.length > 0 ? Arrays.asList(projectIdsForData) : null);
            p.setProjectNameList(StringUtil.isNotEmpty(p.getProjectName()) ? Arrays.asList(p.getProjectName().split("、")) : null);
            //当前出入库对应的仪器详情
            List<DtoProjectInstrumentDetails> proInstrDetailsList = detailsList.stream()
                    .filter(d -> p.getId().equals(d.getProjectInstrumentId())).collect(Collectors.toList());
            //详情的仪器ids
            List<String> instrumentIds = proInstrDetailsList.stream().map(DtoProjectInstrumentDetails::getInstrumentId)
                    .distinct().collect(Collectors.toList());
            //详情仪器的仪器名称+仪器编号
            List<String> instrNameCodeList = finalInstrumentList.stream().filter(instr -> instrumentIds.contains(instr.getId()))
                    .map(instr -> instr.getInstrumentName() +" "+ instr.getInstrumentsCode()).collect(Collectors.toList());
            if (instrNameCodeList.size() > 0) {
                //仪器名称信息
                p.setInstrumentNameCode(String.join(",", instrNameCodeList));
            }
            //是否入库
            p.setIsInComplete(Boolean.FALSE);
            if (proInstrDetailsList.size() > 0) {
                //出库总数
                p.setOutCount(proInstrDetailsList.size());
                List<DtoProjectInstrumentDetails> innerInstrumentList = proInstrDetailsList.stream()
                        .filter(ProjectInstrumentDetails::getIsStorage).collect(Collectors.toList());
                //入库数
                p.setInCount(innerInstrumentList.size());
                if (p.getOutCount().equals(p.getInCount())) {
                    //是否全部入库
                    p.setIsInComplete(Boolean.TRUE);
                }
            }
        });
    }
}