package com.sinoyd.lims.lim.dto.customer;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *  个性化的参数
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
@Data
public class DtoPersonalParams {

    /**
     * 参数数据
     */
    private DtoPersonalDataParams personalDataParams;

    /**
     * 表头数据
     */
    private DtoPersonalHeaderParams personalHeaderParams;


    /**
     * 对象ids
     */
    private List<String> objectIds = new ArrayList<>();
}
