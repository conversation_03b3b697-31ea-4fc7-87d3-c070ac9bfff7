package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ocr对象检索
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OcrConfigCriteria extends BaseCriteria{
    /**
     * 对象名称
     */
    private String configName;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if(StringUtils.isNotNullAndEmpty(configName))
        {
            condition.append(" and configName like :configName");
            values.put("configName", "%" + this.configName + "%");
        }
        return condition.toString();
    }
}