package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * RecordConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "RecordConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class RecordConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public RecordConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 名称
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("名称")
    @Length(message = "名称{validation.message.length}", max = 100)
    private String recordName;

    /**
     * 记录单类型(枚举EnumRecordType：1:采样记录单,2:原始记录单,3:报告)
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("记录单类型(枚举EnumRecordType：1:采样记录单,2:原始记录单,3:报告)")
    private Integer recordType = -1;

    /**
     * 报表模板Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("报表模板Id")
    private String reportConfigId;

    /**
     * 检测类型Id(小类)
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型Id(小类)")
    private String sampleTypeId;

    /**
     * 检测类型Id列表(小类)，多个id用逗号隔开
     */
    @Column
    @ApiModelProperty("检测类型Id列表(小类)，多个id用逗号隔开")
    @Length(message = "检测类型Id列表{validation.message.length}", max = 400)
    private String sampleTypeIds;

    /**
     * 备注
     */
    @Column(length = 4000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 255)
    private String remark;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 排序值
     */
    @ApiModelProperty("排序值")
    private Integer orderNum;

}