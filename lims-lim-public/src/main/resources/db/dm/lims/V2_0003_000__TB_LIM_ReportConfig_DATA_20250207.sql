-- 非道路移动柴油机械检测原始记录采样单配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('85e1e916-de91-486d-8ab5-c7ef09601522', 1, 'FdlSamplingRecord', 'LIMS-CY-FD-03-01 非道路移动柴油机械检测原始记录表.xlsx',
        'Sampling/LIMS-CY-FD-03-01 非道路移动柴油机械检测原始记录表.xlsx', 'output/Sampling/LIMS-CY-FD-03-01 非道路移动柴油机械检测原始记录表.xlsx',
        'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.FdlSamplingRecordService',
        '{\"sort\":\"orderNum-\"}', '', 0, 3, '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-05 11:29:40', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-05 11:29:40', 'sampleIds', 'Sampling', 'FdlSamplingRecord', 0,
        '', NULL, '', '', '', 0, NULL);

-- 柴油车污染物排放检测原始记录采样单配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('ddf823b0-a1b1-44e9-bb0e-94a34667879c', 1, 'CycSamplingRecord', 'LIMS-CY-FD-02-01 柴油车污染物排放检测原始记录表.xlsx',
        'Sampling/LIMS-CY-FD-02-01 柴油车污染物排放检测原始记录表.xlsx', 'output/Sampling/LIMS-CY-FD-02-01 柴油车污染物排放检测原始记录表.xlsx',
        'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.CycSamplingRecordService',
        '{\"sort\":\"orderNum-\"}', '', 0, 3, '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-05 11:34:06', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-05 11:34:06', 'sampleIds', 'Sampling', 'CycSamplingRecord', 0,
        '', NULL, '', '', '', 0, NULL);

-- 汽油车污染物排放检测原始记录采样单配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('017836f7-ce2f-4878-b7d0-2ee60b033a87', 1, 'QycSamplingRecord', 'LIMS-CY-FD-01-01 汽油车污染物排放检测原始记录表.xlsx',
        'Sampling/LIMS-CY-FD-01-01 汽油车污染物排放检测原始记录表.xlsx', 'output/Sampling/LIMS-CY-FD-01-01 汽油车污染物排放检测原始记录表.xlsx',
        'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.QycSamplingRecordService',
        '{\"sort\":\"orderNum-\"}', '', 0, 3, '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-05 13:11:40', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-02-05 13:11:40', 'sampleIds', 'Sampling', 'QycSamplingRecord', 0,
        '', NULL, '', '', '', 0, NULL);
