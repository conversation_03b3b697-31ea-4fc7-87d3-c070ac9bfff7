package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.dto.customer.DtoQCCompareCurve;

import java.util.List;

/**
 * 质控比例的统计
 *
 * <AUTHOR>
 * @version V1.0.0 2020/02/11
 * @since V100R001
 */
public interface QCCompareService {

    /**
     * 返回相应的质控比例数量曲线
     *
     * @param baseCriteria 查询条件
     * @return 返回质控比例数量
     */
    List<DtoQCCompareCurve> findQCCompareStatistics(BaseCriteria baseCriteria);
}
