-- tb_pro_costinfodetail
alter table tb_pro_costinfodetail add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_pro_costinfodetail add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_pro_costinfodetail add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_pro_costinfodetail add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_pro_costinfodetail add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_pro_costinfodetail add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_pro_costinfodetail set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_pro_costinfodetail set creator = '59141356591b48e18e139aa54d9dd351';
update tb_pro_costinfodetail set createDate = '2023-02-21 09:14:15';
update tb_pro_costinfodetail set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_pro_costinfodetail set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_pro_costinfodetail set modifyDate = '2023-02-21 09:14:15';

-- tb_pro_foldersign
alter table tb_pro_foldersign add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_pro_foldersign add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_pro_foldersign add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_pro_foldersign add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_pro_foldersign add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_pro_foldersign add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_pro_foldersign set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_pro_foldersign set creator = '59141356591b48e18e139aa54d9dd351';
update tb_pro_foldersign set createDate = '2023-02-21 09:14:15';
update tb_pro_foldersign set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_pro_foldersign set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_pro_foldersign set modifyDate = '2023-02-21 09:14:15';

-- tb_pro_qualitycontrolevaluate
alter table tb_pro_qualitycontrolevaluate add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_pro_qualitycontrolevaluate add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_pro_qualitycontrolevaluate add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_pro_qualitycontrolevaluate add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_pro_qualitycontrolevaluate add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_pro_qualitycontrolevaluate add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_pro_qualitycontrolevaluate set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_pro_qualitycontrolevaluate set creator = '59141356591b48e18e139aa54d9dd351';
update tb_pro_qualitycontrolevaluate set createDate = '2023-02-21 09:14:15';
update tb_pro_qualitycontrolevaluate set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_pro_qualitycontrolevaluate set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_pro_qualitycontrolevaluate set modifyDate = '2023-02-21 09:14:15';

-- tb_pro_reportrecover
alter table tb_pro_reportrecover add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_pro_reportrecover add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_pro_reportrecover add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_pro_reportrecover add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_pro_reportrecover add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_pro_reportrecover add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_pro_reportrecover set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_pro_reportrecover set creator = '59141356591b48e18e139aa54d9dd351';
update tb_pro_reportrecover set createDate = '2023-02-21 09:14:15';
update tb_pro_reportrecover set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_pro_reportrecover set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_pro_reportrecover set modifyDate = '2023-02-21 09:14:15';

