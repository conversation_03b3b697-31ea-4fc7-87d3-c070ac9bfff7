package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherDataDetails;
import com.sinoyd.lims.lim.repository.lims.InstrumentGatherDataDetailsRepository;
import com.sinoyd.lims.lim.service.InstrumentGatherDataDetailsService;
import org.springframework.stereotype.Service;

/**
 * 仪器接入数据操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Service
public class InstrumentGatherDataDetailsServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoInstrumentGatherDataDetails, String, InstrumentGatherDataDetailsRepository> implements InstrumentGatherDataDetailsService {


}