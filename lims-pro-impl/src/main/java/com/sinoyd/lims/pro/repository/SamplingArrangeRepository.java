package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSamplingArrange;

import java.util.List;


/**
 * DtoSamplingArrange数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/9/20
 * @since V100R001
 */
public interface SamplingArrangeRepository extends IBaseJpaPhysicalDeleteRepository<DtoSamplingArrange, String> {

    /**
     * 根据点位标识查找所有周期
     * @param ids  点位标识列表
     * @return     点位周期列表
     */
    List<DtoSamplingArrange> findAllBySampleFolderIdIn(List<String> ids);

    /**
     * 根据采样计划ID查找数据
     *
     * @param samplingPlanId 采样计划ID
     * @return  采样计划
     */
    List<DtoSamplingArrange> findBySamplingPlanId(String samplingPlanId);

    /**
     * 批量提交
     *
     * @param samplingPlanIds 采样计划ID列表
     * @return 采样计划
     */
    List<DtoSamplingArrange> findBySamplingPlanIdIn(List<String> samplingPlanIds);
}