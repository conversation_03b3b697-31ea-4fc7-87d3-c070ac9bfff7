package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.sinoyd.base.dto.customer.DtoImportTest;
import com.sinoyd.base.utils.poi.ExcelStyle;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.lims.lim.service.DownLoadTestTemplateService;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 模板下载
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
@Service
public class DownLoadTestTemplateServiceImpl implements DownLoadTestTemplateService {

    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    @Override
    public void downLoadTestTemplate(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        //设置导出空数据
        List<DtoImportTest> tests = getNullData();
        // 第一张Sheet参数
        ExportParams params1 = new ExportParams();
        // 设置第一张Sheet样式
        params1.setStyle(ExcelStyle.class);
        // 设置sheet名
        params1.setSheetName(sheetNames.get("firstName"));
        //导出模板
        PoiExcelUtils.exportExcel(tests,DtoImportTest.class,"测试项目导入模板",params1,response);
    }

    /**
     * 设置导出空值
     * @return 空list
     */
    private List<DtoImportTest> getNullData(){
        List<DtoImportTest> tests = new ArrayList<>();
        DtoImportTest test = new DtoImportTest();
        test.setIsCompleteField(null);
        test.setIsOutsourcing(null);
        test.setIsQCP(null);
        test.setIsQCB(null);
        test.setIsInsUseRecord(null);
        test.setIsSeries(null);
        test.setValidTime(null);
        test.setId(null);
        test.setMostDecimal(null);
        test.setMostSignificance(null);
        tests.add(test);
        return tests;
    }
}
