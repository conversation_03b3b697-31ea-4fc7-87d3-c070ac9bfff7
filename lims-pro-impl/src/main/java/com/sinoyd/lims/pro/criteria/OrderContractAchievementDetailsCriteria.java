package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * OrderContractAcihievement2Person查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderContractAchievementDetailsCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 绩效id
     */
    private String achievementId;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 签订日期开始时间
     */
    private String signStartTime;

    /**
     * 签订日期结束时间
     */
    private String signEndTime;

    /**
     * 合同编号，合同名称，甲方名称
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear();
        Calendar calendar = new GregorianCalendar();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.personId)) {
            condition.append(" and exists(select 1 from DtoOrderContractAchievement2Person p where p.id = a.achievementId and p.personId = :personId)");
            values.put("personId", this.personId);
        }
        if (StringUtil.isNotEmpty(this.signStartTime)) {
            Date date = DateUtil.stringToDate(this.signStartTime, DateUtil.YEAR);
            condition.append(" and a.signDate >= :signStartTime");
            values.put("signStartTime", date);
        }
        if (StringUtil.isNotEmpty(this.signEndTime)) {
            Date date = DateUtil.stringToDate(this.signEndTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.signDate < :signEndTime");
            values.put("signEndTime", date);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (a.contractCode like :key or a.contractName like :key or a.firstEntName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.achievementId)) {
            condition.append(" and a.achievementId = :achievementId");
            values.put("achievementId", this.achievementId);
        }
        return condition.toString();
    }
}
