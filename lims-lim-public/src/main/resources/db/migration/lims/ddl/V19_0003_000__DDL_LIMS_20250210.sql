ALTER TABLE tb_base_consumable ADD COLUMN rangeType int(10) NOT NULL DEFAULT 10 COMMENT '范围类型,关联枚举EnumConsumableRangeType';
ALTER TABLE tb_base_consumable ADD COLUMN rangeLow varchar(50) COMMENT '范围低点';
ALTER TABLE tb_base_consumable ADD COLUMN rangeHigh varchar(50) COMMENT '范围高点';
ALTER TABLE tb_base_consumableofmixed ADD COLUMN rangeLow varchar(50) COMMENT '范围低点';
ALTER TABLE tb_base_consumableofmixed ADD COLUMN rangeHigh varchar(50) COMMENT '范围高点';