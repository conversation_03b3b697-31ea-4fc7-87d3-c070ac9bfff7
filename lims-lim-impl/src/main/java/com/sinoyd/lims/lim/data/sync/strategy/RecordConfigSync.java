package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.criteria.RecordConfigCriteria;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.RecordConfigRepository;
import com.sinoyd.lims.lim.service.RecordConfigService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 采样单配置同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/24
 */
@Component
@DependsOn({"springContextAware"})
@Order(20)
@Slf4j
public class RecordConfigSync extends AbsDataSync<DtoRecordConfig> {

    @Autowired
    private RecordConfig2TestSync recordConfig2TestSync;

    @Autowired
    private SampleTypeSync sampleTypeSync;

    @Autowired
    private RecordConfigService service;

    @Autowired
    private RecordConfigRepository repository;


    /**
     * 数据比较
     *
     * @param recordIds 需要同步的采样单配置id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoRecordConfig>> compareData(List<String> recordIds) {
        List<DtoRecordConfig> projectData = service.findAll();
        List<DtoRecordConfig> standardData = queryStandardData();
        if (StringUtil.isNotEmpty(standardData) && StringUtil.isNotEmpty(recordIds)) {
            standardData = standardData.stream().filter(p -> recordIds.contains(p.getId())).collect(Collectors.toList());
        }
        standardData = standardData.stream().filter(p -> EnumLIM.EnumRecordType.采样记录单.getValue().equals(p.getRecordType())).collect(Collectors.toList());
        return compareData(standardData, projectData);
    }

    /**
     * 设置关联依赖表
     *
     * @param recordIds 需要同步的采样单配置id
     * @return 依赖结果
     */
    @Override
    public Map<Integer, List<String>> getChildItemMap(List<String> recordIds) {
        Map<Integer, List<String>> map = new HashMap<>();
        List<DtoRecordConfig> standardData = queryStandardData();
        if (StringUtil.isNotEmpty(standardData) && StringUtil.isNotEmpty(recordIds)) {
            standardData = standardData.stream().filter(p -> recordIds.contains(p.getId())).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(standardData)){
            //获取采样单配置关联的测试项目数据
            List<DtoRecordConfig2Test> standard2Test = recordConfig2TestSync.queryStandardData();
            if (StringUtil.isNotEmpty(standard2Test)&& StringUtil.isNotEmpty(recordIds)){
                standard2Test = standard2Test.stream().filter(p -> recordIds.contains(p.getRecordConfigId())).collect(Collectors.toList());
            }
            //获取关联的测试项目Id
            List<String> testIds = standard2Test.stream().map(DtoRecordConfig2Test::getTestId).collect(Collectors.toList());

            List<DtoSampleType> sampleTypeList = sampleTypeSync.queryStandardData();
            //所有检测类型
            List<String> sampleTypeIds = new ArrayList<>();

            if (StringUtil.isNotEmpty(sampleTypeList)){
                sampleTypeIds = sampleTypeList.stream().map(DtoSampleType::getId).collect(Collectors.toList());
            }
            //获取关联的报表配置Id
            List<String> reportConfigIds = standardData.stream().map(DtoRecordConfig::getReportConfigId).collect(Collectors.toList());

            map.put(EnumLIM.EnumDataSyncType.报表配置.getValue(), reportConfigIds);
            map.put(EnumLIM.EnumDataSyncType.报表配置应用.getValue(), reportConfigIds);
            map.put(EnumLIM.EnumDataSyncType.测试项目.getValue(), testIds);
            map.put(EnumLIM.EnumDataSyncType.参数配置.getValue(), testIds);
            map.put(EnumLIM.EnumDataSyncType.检测类型参数.getValue(), sampleTypeIds);
            map.put(EnumLIM.EnumDataSyncType.测试项目公式.getValue(), testIds);
            map.put(EnumLIM.EnumDataSyncType.测试项目公式参数.getValue(), testIds);
            map.put(EnumLIM.EnumDataSyncType.测试项目修约配置.getValue(), testIds);
        }
        return map;
    }

    /**
     * 同步数据
     *
     * @param recordIds       需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> recordIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoRecordConfig>> compareResult = compareData(recordIds);
        Optional<DtoDataCompareResult<DtoRecordConfig>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoRecordConfig errorDto = null;
            try {
                for (DtoRecordConfig dtoEvaluationAnalyzeItem : r.getAddDataList()) {
                    errorDto = dtoEvaluationAnalyzeItem;
                    if (repository.findOne(dtoEvaluationAnalyzeItem.getId()) != null) {
                        service.update(dtoEvaluationAnalyzeItem);
                    } else {
                        service.save(dtoEvaluationAnalyzeItem);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.采样单配置.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.采样单配置.getValue();
    }

    /**
     * 设置关联依赖表
     *
     * @return 依赖表类型
     */
    @Override
    public List<Integer> getDependDataType() {
        return Arrays.asList(
                EnumLIM.EnumDataSyncType.量纲.getValue(),
                EnumLIM.EnumDataSyncType.参数.getValue(),
                EnumLIM.EnumDataSyncType.行业类型.getValue(),
                EnumLIM.EnumDataSyncType.参数配置.getValue(),
                EnumLIM.EnumDataSyncType.检测类型.getValue(),
                EnumLIM.EnumDataSyncType.检测类型参数.getValue(),
                EnumLIM.EnumDataSyncType.分析项目.getValue(),
                EnumLIM.EnumDataSyncType.分析方法.getValue(),
                EnumLIM.EnumDataSyncType.测试项目.getValue(),
                EnumLIM.EnumDataSyncType.测试项目公式.getValue(),
                EnumLIM.EnumDataSyncType.测试项目公式参数.getValue(),
                EnumLIM.EnumDataSyncType.测试项目修约配置.getValue(),
                EnumLIM.EnumDataSyncType.报表配置.getValue(),
                EnumLIM.EnumDataSyncType.报表配置应用.getValue(),
                EnumLIM.EnumDataSyncType.采样单配置.getValue(),
                EnumLIM.EnumDataSyncType.记录单测试项目配置.getValue()
        );
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/recordConfig";
    }

    /**
     * 远程获取数据
     *
     * @param baseCriteria 查询条件
     * @param page         页码
     * @param rows         每页记录数
     * @param sort         排序名称
     * @return 查询数据
     */
    @Override
    public ResponseEntity<JSONObject> queryStandardData(BaseCriteria baseCriteria, int page, int rows, String sort) {
        RecordConfigCriteria criteria = (RecordConfigCriteria) baseCriteria;
        String recordName = StringUtil.isNotNull(criteria.getRecordName()) ? criteria.getRecordName() : "";
        String url = standardDataConfig.getHost() + getStandardDataQueryUrl() +
                "?page=" + page + "&rows=" + rows + "&sort=" + sort + "&recordType=" + EnumLIM.EnumRecordType.采样记录单.getValue().toString() + "&recordName=" + recordName;
        return queryStandardData(url);
    }

    /**
     * 获取类型
     *
     * @return 类型
     */
    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.采样单配置.getValue();
    }

}
