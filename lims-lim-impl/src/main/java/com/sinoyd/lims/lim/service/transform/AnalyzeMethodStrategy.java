package com.sinoyd.lims.lim.service.transform;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.AnalyzeMethodMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.verify.TransformAnalyzeMethodVerifyHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目数据迁移分析方法导入策略类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
@Component
@Order(50)
public class AnalyzeMethodStrategy implements TransformImportStrategy {
    /**
     * sheet页序号
     */
    private static final int sheetIndex = 1;

    private AnalyzeMethodRepository analyzeMethodRepository;

    private TransformAnalyzeMethodVerifyHandler transformAnalyzeMethodVerifyHandler;
    private AnalyzeMethodMapper analyzeMethodMapper;

    @Override
    @Transactional
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception {
        //参数初始化
        handleInit();
        params.setStartSheetIndex(sheetIndex);
        params.setVerifyHandler(transformAnalyzeMethodVerifyHandler);
        //获取校验结果集
        ExcelImportResult<DtoExportAnalyzeMethod> result = ExcelImportUtil.importExcelMore(inputStream, DtoExportAnalyzeMethod.class, params);
        // 检验失败抛出异常并返回错误文档
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(sheetIndex, "导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        //构建可保存数据
        List<DtoAnalyzeMethod> waitSaveList = new ArrayList<>();
        buildRightData(result, waitSaveList);
        //数据保存
        analyzeMethodRepository.save(waitSaveList);
    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {
        // 获取待导入分析方法
        List<DtoExportAnalyzeMethod> analyzeMethodList = testDependentData.getAnalyzeMethodList();
        List<DtoImportCheck> importChecks = dtoDataSyncParams.getImportChecks();
        // 筛选出检查类型为新增的分析方法数据
        Optional<DtoImportCheck> optional = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.分析方法表.getCheckItem()) &&
                BASE_DATA_TYPE[0].equals(p.getCheckType())).findFirst();
        List<DtoDataCheck> analyzeMethodCheck = optional.isPresent() ? optional.get().getDataChecks() : new ArrayList<>();
        List<String> addName = analyzeMethodCheck.stream().filter(p -> BASE_DATA_TYPE[0].equals(p.getType())).map(DtoDataCheck::getName).collect(Collectors.toList());
        // 根据名称筛选出待新增数据
        List<DtoAnalyzeMethod> dtoAnalyzeMethods = analyzeMethodList.stream()
                .filter(p -> addName.contains(p.getMethodName() + p.getCountryStandard()))
                .map(p -> analyzeMethodMapper.toDtoAnalyzeMethod(p)).collect(Collectors.toList());
        importTestTemp.setAnalyzeMethodTemps(dtoAnalyzeMethods);
        // 根据导出测试项目筛选分析方法
        List<DtoExportTest> testList = exportData.getTestList();
        if (StringUtil.isNotEmpty(testList)) {
            List<String> analyzeMethodIds = testList.stream().map(DtoExportTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
            List<DtoExportAnalyzeMethod> exportAnalyzeMethods = analyzeMethodList.stream().filter(p -> analyzeMethodIds.contains(p.getId())).collect(Collectors.toList());
            exportData.setAnalyzeMethodList(exportAnalyzeMethods);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoAnalyzeMethod> analyzeMethodTemps = importTestTemp.getAnalyzeMethodTemps();
        if (StringUtil.isNotEmpty(analyzeMethodTemps)) {
            //已同步记录数
            int i = 0;
            for (DtoAnalyzeMethod dtoAnalyzeMethod : analyzeMethodTemps) {
                analyzeMethodRepository.save(dtoAnalyzeMethod);
                webSocketServer.sendMessage(getMessage(analyzeMethodTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(analyzeMethodTemps.size(), 0));
        }
    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.分析方法表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.分析方法表.getSource();
    }

    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.分析方法表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        List<DtoExportAnalyzeMethod> analyzeMethodList = testDependentData.getAnalyzeMethodList();
        List<DtoImportCheck> importChecks = new ArrayList<>();
        List<DtoDataCheck> analyzeMethodCheckList = new ArrayList<>();
        if (StringUtil.isNotEmpty(analyzeMethodList)) {
            List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodRepository.findAll();
            analyzeMethodCheckList = analyzeMethodList.parallelStream()
                    .map(p -> {
                        DtoDataCheck dtoDataCheck = new DtoDataCheck();
                        dtoDataCheck.setName(p.getMethodName() + p.getCountryStandard());
                        Map<String, Object> otherField = new HashMap<>();
                        otherField.put("methodName", p.getMethodName() + p.getCountryStandard());
                        dtoDataCheck.setOtherField(otherField);
                        Optional<DtoAnalyzeMethod> analyzeMethodOptional = analyzeMethods.stream()
                                .filter(r -> (r.getMethodName() + r.getCountryStandard()).equals((p.getMethodName() + p.getCountryStandard()))).findFirst();
                        if (analyzeMethodOptional.isPresent()) {
                            dtoDataCheck.setType(BASE_DATA_TYPE[1]);
                            dtoDataCheck.setId(analyzeMethodOptional.get().getId());
                        } else {
                            dtoDataCheck.setType(BASE_DATA_TYPE[0]);
                            dtoDataCheck.setId(p.getId());
                        }
                        return dtoDataCheck;
                    }).collect(Collectors.toList());
        }
        List<DtoDataCheck> existsList = analyzeMethodCheckList.stream().filter(p -> BASE_DATA_TYPE[1].equals(p.getType())).collect(Collectors.toList());
        List<DtoDataCheck> noExistsList = analyzeMethodCheckList.stream().filter(p -> BASE_DATA_TYPE[0].equals(p.getType())).collect(Collectors.toList());
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.分析方法表.getCheckItem(),
                BASE_DATA_TYPE[1], existsList.size(), "所有外键ID关联改为系统内的ID，不进行导入。",
                existsList));
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.分析方法表.getCheckItem(),
                BASE_DATA_TYPE[0], noExistsList.size(), "新增分析方法，导入时将进行插入。",
                noExistsList));

        return importChecks;
    }

    /**
     * 校验容器初始化
     */
    private void handleInit() {
        transformAnalyzeMethodVerifyHandler = new TransformAnalyzeMethodVerifyHandler();
        transformAnalyzeMethodVerifyHandler.setRepoDataList(analyzeMethodRepository.findAll());
        transformAnalyzeMethodVerifyHandler.setSheetExistDataList(new ArrayList<>());
    }

    /**
     * 构建可保存数据
     *
     * @param result       导入结果集
     * @param waitSaveList 待保存结果容器
     */
    private void buildRightData(ExcelImportResult<DtoExportAnalyzeMethod> result, List<DtoAnalyzeMethod> waitSaveList) {
        List<DtoExportAnalyzeMethod> importList = result.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        for (DtoExportAnalyzeMethod exportAnalyzeMethod : importList) {
            DtoAnalyzeMethod dtoAnalyzeMethod = new DtoAnalyzeMethod();
            BeanUtils.copyProperties(exportAnalyzeMethod, dtoAnalyzeMethod);
            waitSaveList.add(dtoAnalyzeMethod);
        }
    }

    @Autowired
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyzeMethodMapper(AnalyzeMethodMapper analyzeMethodMapper) {
        this.analyzeMethodMapper = analyzeMethodMapper;
    }
}
