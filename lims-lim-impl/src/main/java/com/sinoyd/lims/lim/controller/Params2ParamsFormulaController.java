package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.rcc.DtoParams2ParamsFormula;
import com.sinoyd.lims.lim.dto.customer.DtoParams2ParamsFormulaTemp;
import com.sinoyd.lims.lim.dto.customer.DtoPersonalDataParams;
import com.sinoyd.lims.lim.dto.customer.DtoPersonalHeaderParams;
import com.sinoyd.lims.lim.dto.customer.DtoPersonalParams;
import com.sinoyd.lims.lim.service.Params2ParamsFormulaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.boot.common.dto.RestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * Params2ParamsFormula服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
 @Api(tags = "示例: Params2ParamsFormula服务")
 @RestController
 @RequestMapping("api/lim/params2ParamsFormula")
 @Validated
 public class Params2ParamsFormulaController extends BaseJpaController<DtoParams2ParamsFormula, String,Params2ParamsFormulaService> {

    /**
     * 获取个性化的数据
     *
     * @param recordConfigId   记录单id
     * @param objectId         对象id
     * @param paramsConfigId   参数id
     * @param paramsConfigType 类型
     * @return 返回数据
     */
    @GetMapping
    public RestResponse<DtoParams2ParamsFormulaTemp> findPersonalParams(@RequestParam("recordConfigId") String recordConfigId,
                                                                  @RequestParam("objectId") String objectId,
                                                                  @RequestParam("paramsConfigId") String paramsConfigId,
                                                                  @RequestParam("paramsConfigType") Integer paramsConfigType
    ) {
        RestResponse<DtoParams2ParamsFormulaTemp> restResponse = new RestResponse<>();
        DtoParams2ParamsFormulaTemp temp = service.findPersonalParams(recordConfigId, objectId, paramsConfigId, paramsConfigType);
        restResponse.setData(temp);
        return restResponse;
    }


    /**
     * 保存自定义参数
     *
     * @param params2ParamsFormula 实体列表
     * @return RestResponse<DtoParams2ParamsFormula>
     */
    @ApiOperation(value = "新增数据参数", notes = "新增数据参数")
    @PostMapping("/formula")
    public RestResponse<String> savePersonalDataParams(@Validated @RequestBody DtoPersonalDataParams params2ParamsFormula) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.savePersonalDataParams(params2ParamsFormula);
        return restResponse;
    }

    /**
     * 复制数据参数
     *
     * @param personalParams 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "复制数据参数", notes = "复制数据参数")
    @PostMapping("/formula/copy")
    public RestResponse<String> copyPersonalDataParams(@Validated @RequestBody DtoPersonalParams personalParams) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.copyPersonalDataParams(personalParams.getPersonalDataParams(), personalParams.getObjectIds());
        return restResponse;
    }

    /**
     * 保存自定义参数
     *
     * @param dtoPersonalHeaderParams 实体列表
     * @return RestResponse<DtoParams2ParamsFormula>
     */
    @ApiOperation(value = "保存自定义表头参数", notes = "保存自定义表头参数")
    @PostMapping("/test")
    public RestResponse<String> savePersonalHeaderParams(@RequestBody DtoPersonalHeaderParams dtoPersonalHeaderParams) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.savePersonalHeaderParams(dtoPersonalHeaderParams);
        return restResponse;
    }

    /**
     * 复制数据参数
     *
     * @param personalParams 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "复制表头参数", notes = "复制数据参数")
    @PostMapping("/test/copy")
    public RestResponse<String> copyPersonalHeaderParams(@RequestBody DtoPersonalParams personalParams) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.copyPersonalHeaderParams(personalParams.getPersonalHeaderParams(), personalParams.getObjectIds());
        return restResponse;
    }

    /**
     * 查询工作单参数
     *
     * @return 查询结果
     */
    @ApiOperation(value = "查询工作单参数", notes = "查询工作单参数")
    @GetMapping("/all")
    public RestResponse<List<DtoParams2ParamsFormula>> getAll(){
        RestResponse<List<DtoParams2ParamsFormula>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAll());
        return restResponse;
    }

    /**
     * 数据参数关联公式参数调整,量纲,有效位数,小数位数调整后同步数据
     *
     * @return 查询结果
     */
    @ApiOperation(value = "查询工作单参数", notes = "查询工作单参数")
    @PutMapping("/updateParams2ParamsFormulaAndParamsConfig")
    public RestResponse<Void> updateParams2ParamsFormulaAndParamsConfig(@RequestBody Map<String,Object> map){
        RestResponse<Void> res = new RestResponse<>();
        service.updateParams2ParamsFormulaAndParamsConfig(map);
        return res;
    }

    /**
     * 批量设置公式参数配置
     *
     * @return 查询结果
     */
    @ApiOperation(value = "批量设置公式参数配置", notes = "批量设置公式参数配置")
    @PutMapping("/batchUpdateParams2ParamsFormula")
    public RestResponse<Boolean> batchUpdateParams2ParamsFormula(@RequestBody Map<String,Object> map){
        RestResponse<Boolean> res = new RestResponse<>();
        service.batchUpdateParams2ParamsFormula(map);
        res.setData(Boolean.TRUE);
        return res;
    }
}