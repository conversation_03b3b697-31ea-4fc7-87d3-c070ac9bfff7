package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost2Person;

import java.util.List;

/**
 * 测试岗位人员配置仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
public interface TestPost2PersonRepository extends IBaseJpaPhysicalDeleteRepository<DtoTestPost2Person, String> {

    /**
     * 根据测试岗位id查找
     *
     * @param testPostId 测试岗位id
     * @return 测试岗位人员配置列表
     */
    List<DtoTestPost2Person> findByTestPostId(String testPostId);

    /**
     * 根据测试岗位id删除
     *
     * @param testPostIdList 测试岗位id列表
     * @return 删除的数量
     */
    int deleteByTestPostIdIn(List<String> testPostIdList);

    /**
     * 根据人员id查找
     *
     * @param personId 测试岗位id
     * @return 测试岗位人员配置列表
     */
    List<DtoTestPost2Person> findByPersonId(String personId);

    /**
     * 根据人员id集合查找
     *
     * @param personIds 人员id集合
     * @return 测试岗位人员配置列表
     */
    List<DtoTestPost2Person> findByPersonIdIn(List<String> personIds);

    /**
     * 根据测试岗位ids查找
     *
     * @param testPostIds 测试岗位ids
     * @return 测试岗位人员配置列表
     */
    List<DtoTestPost2Person> findByTestPostIdIn(List<String> testPostIds);

}