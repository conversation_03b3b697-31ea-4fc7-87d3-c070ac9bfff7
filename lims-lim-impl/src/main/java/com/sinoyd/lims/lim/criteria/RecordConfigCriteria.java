package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * RecordConfig查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordConfigCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 记录单名称
    */
    private String recordName;

    /**
     * 记录单类型
     */
    private Integer recordType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.recordName)) {
            condition.append(" and recordName like :recordName");
            values.put("recordName",  "%" + this.recordName + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.recordType)) {
            condition.append(" and recordType = :recordType");
            values.put("recordType",  this.recordType);
        }
        return condition.toString();
    }
}