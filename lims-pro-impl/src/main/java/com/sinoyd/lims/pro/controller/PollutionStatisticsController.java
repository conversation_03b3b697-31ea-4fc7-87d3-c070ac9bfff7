package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.PollutionStatisticsCriteria;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.service.PollutionStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 污染源数据统计接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/05/07
 * @since V100R001
 */
@Api(tags = "示例: 操作日志")
@RestController
@RequestMapping("api/pro/poll")
public class PollutionStatisticsController extends BaseJpaController<DtoProject, String,PollutionStatisticsService> {


    /**
     * 分页动态条件查询DtoProject
     *
     * @param pollutionStatisticsCriteria 条件参数
     * @return RestResponse<List<DtoProject>>
     */
    @ApiOperation(value = "分页动态条件查询DtoProject", notes = "分页动态条件查询DtoProject")
    @GetMapping
    public RestResponse<List<DtoProject>> findByPage(PollutionStatisticsCriteria pollutionStatisticsCriteria) {
        PageBean<DtoProject> pageBean = super.getPageBean();
        RestResponse<List<DtoProject>> restResponse = new RestResponse<>();
        List<DtoProject> list = service.findDtoProject(pageBean, pollutionStatisticsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(list);
        restResponse.setCount(list.size());
        return restResponse;
    }
}
