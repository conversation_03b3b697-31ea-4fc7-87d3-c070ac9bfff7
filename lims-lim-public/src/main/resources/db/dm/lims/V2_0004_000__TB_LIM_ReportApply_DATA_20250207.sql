-- 非道路移动柴油机械检测原始记录采样单配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('3e93abd6-fb83-4ff1-b191-9d2980406684', '85e1e916-de91-486d-8ab5-c7ef09601522', 'PrepareSample', '采样准备', '采样单',
        '非道路移动柴油机械检测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-02-05 11:31:12', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-02-05 11:31:12', 1);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('98754ac4-19c3-478c-a931-794937d2bd6e', '85e1e916-de91-486d-8ab5-c7ef09601522', 'LocalTask', '现场任务', '采样单',
        '非道路移动柴油机械检测原始记录', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-05 11:31:52', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-05 11:31:52', NULL);

-- 柴油车污染物排放检测原始记录采样单配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('67247903-30a0-4566-8593-7c687e4dc386', 'ddf823b0-a1b1-44e9-bb0e-94a34667879c', 'LocalTask', '现场任务', '采样单',
        '柴油车污染物排放检测原始记录', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-05 13:08:08', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-05 13:08:08', NULL);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('fbc465fc-c75b-4192-a05e-8dddd3962868', 'ddf823b0-a1b1-44e9-bb0e-94a34667879c', 'PrepareSample', '采样准备', '采样单',
        '柴油车污染物排放检测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-02-05 13:07:23', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-02-05 13:07:23', 1);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('8b9baba6-71ee-4f7f-af81-f33bea91d5f0', '017836f7-ce2f-4878-b7d0-2ee60b033a87', 'LocalTask', '现场任务', '采样单',
        '汽油车污染物排放检测原始记录', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-05 13:15:03', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-05 13:15:03', NULL);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('f72ab808-62e5-41fa-aa6b-b06d1f197791', '017836f7-ce2f-4878-b7d0-2ee60b033a87', 'PrepareSample', '采样准备', '采样单',
        '汽油车污染物排放检测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-02-05 13:14:24', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-02-05 13:14:24', 1);
