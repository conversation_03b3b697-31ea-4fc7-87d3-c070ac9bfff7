package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.AnalyzeMethodCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoLogForAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.service.AnalyzeMethodService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分析方法接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-13
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/analyzeMethod")
@Validated
public class AnalyzeMethodController extends BaseJpaController<DtoAnalyzeMethod, String, AnalyzeMethodService> {

    /**
     * 根据id查询分析方法
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询分析方法", notes = "根据id查询分析方法")
    @GetMapping("/{id}")
    public RestResponse<DtoAnalyzeMethod> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoAnalyzeMethod> restResp = new RestResponse<>();

        DtoAnalyzeMethod entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询分析方法
     *
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询分析方法", notes = "分页动态条件查询分析方法")
    @GetMapping
    public RestResponse<List<DtoAnalyzeMethod>> findByPage(AnalyzeMethodCriteria criteria) {

        RestResponse<List<DtoAnalyzeMethod>> restResp = new RestResponse<>();

        PageBean<DtoAnalyzeMethod> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增分析方法
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增分析方法", notes = "新增分析方法")
    @PostMapping
    public RestResponse<DtoAnalyzeMethod> save(@Validated @RequestBody DtoAnalyzeMethod entity) {

        RestResponse<DtoAnalyzeMethod> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeMethod data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改分析方法
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改分析方法", notes = "修改分析方法")
    @PutMapping
    public RestResponse<DtoAnalyzeMethod> update(@Validated @RequestBody DtoAnalyzeMethod entity) {

        RestResponse<DtoAnalyzeMethod> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeMethod data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id删除分析方法
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id删除分析方法", notes = "根据id删除分析方法")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除分析方法
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除分析方法", notes = "批量删除分析方法")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 替换分析方法
     *
     * @param entity 要替换的分析方法
     * @return 是否替换成功
     */
    @PostMapping("/replace")
    public RestResponse<Void> replace(@RequestBody DtoAnalyzeMethod entity) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.replace(entity);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 更新方法状态
     *
     * @param status 状态
     * @param ids    分析方法标识
     * @return RestResponse
     */
    @PostMapping("/updateMethodStatus/{status}")
    public RestResponse<Void> updateMethodStatus(@PathVariable Integer status, @RequestBody List<String> ids) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.updateMethodStatus(status, ids);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 相似度比较导出
     *
     * @param similarity 相似度%
     * @param response   响应体
     * @return
     */
    @ApiOperation(value = "相似度比较导出", notes = "相似度比较导出")
    @GetMapping("/compareSimilarity")
    public void compareSimilarity(Integer similarity, HttpServletResponse response) {
        service.compareExport(similarity, response);
    }

    /**
     * 获取分析方法状态日志
     *
     * @param id 方法标识
     * @return 日志列表
     */
    @ApiOperation(value = "获取分析方法状态日志", notes = "获取分析方法状态日志")
    @GetMapping("/log/{id}")
    public RestResponse<List<DtoLogForAnalyzeMethod>> getStatusLog(@PathVariable String id) {
        RestResponse<List<DtoLogForAnalyzeMethod>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.getStatusLog(id));
        return restResp;
    }

    /**
     * 根据检测类型id获取方法
     *
     * @param sampleTypeId 方法标识
     * @return 方法集合
     */
    @ApiOperation(value = "根据检测类型id获取方法", notes = "根据检测类型id获取方法")
    @GetMapping("/bySampleType")
    public RestResponse<List<DtoAnalyzeMethod>> getBySampleType(String sampleTypeId) {
        RestResponse<List<DtoAnalyzeMethod>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.bySampleType(sampleTypeId));
        return restResp;
    }

}