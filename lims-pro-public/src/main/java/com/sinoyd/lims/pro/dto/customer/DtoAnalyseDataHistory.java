package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 实验室分析，历史数据返回实体
 *
 * <AUTHOR>
 * @date V1.0.0 2023/12/28
 * @version: V100R001
 */
@Data
public class DtoAnalyseDataHistory {

    /**
     * 点位名称
     */
    private String watchSpot;

    /**
     * 分析项目
     */
    private String redAnalyzeItemName;

    /**
     * 历史最大值
     */
    private String maxTestVale;

    /**
     * 最大分析日期
     */
    private String maxAnalyzeTime;

    /**
     * 历史最小值
     */
    private String minTestValue;

    /**
     * 最小分析日期
     */
    private String minAnalyzeTime;

    /**
     * 均值
     */
    private String avgTestVal;

    /**
     * 样品警告集合
     */
    private List<Map<String,String>> sampleWarnList;

}
