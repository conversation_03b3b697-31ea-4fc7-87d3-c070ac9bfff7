package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoWorkSheetCalibrationCurveDetail;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * WorkSheetCalibrationCurveDetail数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface WorkSheetCalibrationCurveDetailRepository extends IBaseJpaRepository<DtoWorkSheetCalibrationCurveDetail, String> {

    /**
     * 按校准曲线id查询明细
     *
     * @param workSheetCalibrationCurveId 校准曲线id
     * @return 返回校准曲线明细
     */
    List<DtoWorkSheetCalibrationCurveDetail> findByWorkSheetCalibrationCurveId(String workSheetCalibrationCurveId);

    /**
     * 按校准曲线ids查询明细
     * @param workSheetCalibrationCurveIds 校准曲线ids
     * @return 返回校准曲线明细
     */
    List<DtoWorkSheetCalibrationCurveDetail> findByWorkSheetCalibrationCurveIdIn(List<String> workSheetCalibrationCurveIds);
    /**
     * 删除对应校准曲线下的明细
     *
     * @param workSheetCalibrationCurveId 校准曲线id
     * @return 删除的条数
     */
    @Transactional
    @Modifying
    @Query("delete from DtoWorkSheetCalibrationCurveDetail as a where a.workSheetCalibrationCurveId=:workSheetCalibrationCurveId ")
    Integer deleteByWorkSheetCalibrationCurveId(@Param("workSheetCalibrationCurveId") String workSheetCalibrationCurveId);
}