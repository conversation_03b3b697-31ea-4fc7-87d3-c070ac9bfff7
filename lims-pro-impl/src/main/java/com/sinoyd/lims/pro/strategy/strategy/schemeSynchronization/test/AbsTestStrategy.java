package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.test;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTestTemp;
import com.sinoyd.lims.pro.repository.SamplingFrequencyTestTempRepository;
import com.sinoyd.lims.pro.service.SampleFolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class AbsTestStrategy {

    protected SampleFolderService sampleFolderService;

    protected SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository;

    /**
     * 调整测试项目方案
     * @param samplingFrequencyTestTempList 修改项目内容
     */
    public abstract void synchronizationTest(List<DtoSamplingFrequencyTestTemp> samplingFrequencyTestTempList);

    @Autowired
    public void setSamplingFrequencyTestTempRepository(SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository) {
        this.samplingFrequencyTestTempRepository = samplingFrequencyTestTempRepository;
    }

    @Autowired
    @Lazy
    public void setSampleFolderService(SampleFolderService sampleFolderService) {
        this.sampleFolderService = sampleFolderService;
    }
}
