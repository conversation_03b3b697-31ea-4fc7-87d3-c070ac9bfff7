INSERT INTO t_sys_module_action(id, actionName, actionCode, moduleGuid, enabled, sortNum, note, createDate, createUserGuid, createUserName, updateDate, updateUserGuid, updateUserName, orgGuid, deptGuid, deleted) VALUES ('c4e17ca8b12f43e1acd2e03d7ff8d416', '检测费用明细费用权限', 'power_update_expense', '24085e21c8de4ba0b2c37598040d4b87', b'1', 1000, '采样费、分析费、报价”三列，是否允许在列上进行直接修改', '2023-12-15 09:43:17', '59141356591b48e18e139aa54d9dd351', '超级管理员', NULL, NULL, NULL, '5f7bcf90feb545968424b0a872863876', NULL, b'0');
INSERT INTO t_sys_module_action(id, actionName, actionCode, moduleGuid, enabled, sortNum, note, createDate, createUserGuid, createUserName, updateDate, updateUserGuid, updateUserName, orgGuid, deptGuid, deleted) VALUES ('7fd3bd089778476c83a5f1366c52833b', '检测费用明细费用权限', 'power_update_expense', '7b4f5982c40743dca152074b0066a736', b'1', 1000, '采样费、分析费、报价”三列，是否允许在列上进行直接修改', '2023-12-15 09:19:08', '59141356591b48e18e139aa54d9dd351', '超级管理员', NULL, NULL, NULL, '5f7bcf90feb545968424b0a872863876', NULL, b'0');
