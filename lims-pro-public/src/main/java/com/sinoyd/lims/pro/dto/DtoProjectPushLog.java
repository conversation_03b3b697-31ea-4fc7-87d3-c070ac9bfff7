package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ProjectPushLog;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoProjectPushLog实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ProjectPushLog")
 @Data
 @DynamicInsert
 public  class DtoProjectPushLog extends ProjectPushLog {
   private static final long serialVersionUID = 1L;
 }