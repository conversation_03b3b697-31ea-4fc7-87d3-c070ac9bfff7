package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.service.PDPTimerProcessService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * （PDP:PollutantDischargePermit）排污许可证自行检测数据定时服务Controller
 *
 * <AUTHOR>
 * @version V5.2.0 2025/05/06
 * @since V100R001
 */
@RestController
@RequestMapping("/api/pro/pdpTimerProcess")
public class PDPTimerProcessController extends ExceptionHandlerController<PDPTimerProcessService> {

    /**
     * 排污许可证自行检测数据定时服务数据同步
     *
     * @return 无返回值
     */
    @PostMapping("/monitorData")
    public RestResponse<Void> syncPointMonitorData() {
        service.syncPointMonitorData();
        return new RestResponse<>();
    }

    /**
     * 获取未同步成功的数据进行同步
     *
     * @return 无返回值
     */
    @PostMapping("/unSuccess")
    public RestResponse<Void> syncUnSuccessfulData() {
        service.syncUnSuccessfulData();
        return new RestResponse<>();
    }

}
