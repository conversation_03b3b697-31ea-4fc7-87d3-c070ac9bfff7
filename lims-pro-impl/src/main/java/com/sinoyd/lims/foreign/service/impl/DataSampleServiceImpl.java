package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataSampleVO;
import com.sinoyd.lims.foreign.service.DataSampleService;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 样品信息样品信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataSampleServiceImpl extends AbsQueryDataServiceImpl<DataSampleVO> implements DataSampleService {


    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select t1.id,t1.code,t1.projectId,t1.receiveId,t1.sampleFolderId,t4.fixedPointId,t1.cycleOrder,");
        sql.append("t1.timesOrder,t1.sampleOrder,t1.redFolderName,t2.typeName,t1.sampleCategory,");
        sql.append("t1.samplingTimeBegin,t1.samplingTimeEnd,t1.qcId,t1.associateSampleId,t1.pack,");
        sql.append("t1.sampleWeight,t1.weightOrQuantity,t1.samColor,t1.sampleExplain,t1.volume,");
        sql.append("t3.recordCode,t3.samplingTime,t3.sendTime,t3.senderName,t3.receiveSampleDate,");
        sql.append("t4.folderCode,t1.orgId,t1.domainId ");
        sql.append(" from TB_PRO_Sample t1 ");
        sql.append(" LEFT JOIN TB_BASE_SampleType t2 ON t1.sampleTypeId = t2.id ");
        sql.append(" LEFT JOIN TB_PRO_ReceiveSampleRecord t3 ON t1.receiveId = t3.id ");
        sql.append(" LEFT JOIN TB_PRO_SampleFolder t4 ON t1.sampleFolderId = t4.id");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }


    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "and t1.modifyDate > :syncTime", "syncTime");
        super.appendCondition(sql, map, "and t1.id in (:sampleIds) ", "sampleIds");
        super.appendCondition(sql, map, "and t1.projectId in (:projectIds)", "projectIds");
        super.appendCondition(sql, map, "and t1.receiveId in (:receiveIds)", "receiveIds");
    }

    @Override
    protected RowMapper<DataSampleVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataSampleVO sampleVO = new DataSampleVO();
            sampleVO.setId(UUIDHelper.NewID());
            sampleVO.setSampleCode(rs.getString("code"));
            sampleVO.setProjectId(rs.getString("projectId"));
            sampleVO.setSampleId(rs.getString("id"));
            sampleVO.setReceiveId(rs.getString("receiveId"));
            sampleVO.setSampleFolderId(rs.getString("sampleFolderId"));
            sampleVO.setFixedPointId(rs.getString("fixedPointId"));
            sampleVO.setCycleOrder(rs.getInt("cycleOrder"));
            sampleVO.setTimesOrder(rs.getInt("timesOrder"));
            sampleVO.setSampleOrder(rs.getInt("sampleOrder"));
            sampleVO.setRedFolderName(rs.getString("redFolderName"));
            sampleVO.setSampleTypeName(rs.getString("typeName"));
            sampleVO.setSampleCategory(rs.getInt("sampleCategory"));
            sampleVO.setSamplingTimeBegin(rs.getDate("samplingTimeBegin"));
            sampleVO.setSamplingTimeEnd(rs.getDate("samplingTimeEnd"));
            sampleVO.setQcId(rs.getString("qcId"));
            sampleVO.setAssociateSampleId(rs.getString("associateSampleId"));
            sampleVO.setPack(rs.getString("pack"));
            sampleVO.setSampleWeight(rs.getString("sampleWeight"));
            sampleVO.setWeightOrQuantity(rs.getString("weightOrQuantity"));
            sampleVO.setSamColor(rs.getString("samColor"));
            sampleVO.setSampleExplain(rs.getString("sampleExplain"));
            sampleVO.setVolume(rs.getString("volume"));
            sampleVO.setRecordCode(rs.getString("recordCode"));
            sampleVO.setSamplingTime(rs.getDate("samplingTime"));
            sampleVO.setSendTime(rs.getDate("sendTime"));
            sampleVO.setSenderName(rs.getString("senderName"));
            sampleVO.setReceiveSampleDate(rs.getDate("receiveSampleDate"));
            sampleVO.setFolderCode(rs.getString("folderCode"));
            sampleVO.setOrgId(rs.getString("orgId"));
            sampleVO.setDomainId(rs.getString("domainId"));
            sampleVO.setSyncTime(new Date());
            return sampleVO;
        };
    }

}
