package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.service.ConsumableService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.criteria.ConsumableStorageCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoConsumableStorage;
import com.sinoyd.lims.lim.repository.lims.ConsumableStorageRepository;
import com.sinoyd.lims.lim.service.ConsumableStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
public class ConsumableStorageServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoConsumableStorage, String, ConsumableStorageRepository> implements ConsumableStorageService {

    @Autowired
    @Lazy
    private ConsumableService consumableService;


    /**
     * 分页查询领用记录
     */
    @Override
    public void findByPage(PageBean<DtoConsumableStorage> page, BaseCriteria criteria) {
        ConsumableStorageCriteria consumableStorageCriteria = (ConsumableStorageCriteria) criteria;
        DtoConsumable consumable = consumableService.findOne(consumableStorageCriteria.getConsumableId());
        if (StringUtil.isNotNull(consumable)) {
            page.setEntityName("DtoConsumableStorage p");
            page.setSelect("select p");
            super.findByPage(page, criteria);
        }
    }

    /**
     * 领用记录新增
     */
    @Transactional
    @Override
    public DtoConsumableStorage save(DtoConsumableStorage entity) {

//        //找到详细信息
//        DtoConsumableDetail consumableDetail = consumableDetailService.findOne(entity.getConsumableDetailId());
//        //修改详细信息的库存
//        consumableDetail.setStorage(consumableDetail.getStorage().subtract(entity.getAmount()));
        //找到基本信息
        DtoConsumable consumable = consumableService.findOne(entity.getConsumableId());
        //修改基本信息的库存
        consumable.setInventory(consumable.getInventory().add(entity.getStorageNum()));
        consumableService.update(consumable);

        return super.save(entity);
    }

}