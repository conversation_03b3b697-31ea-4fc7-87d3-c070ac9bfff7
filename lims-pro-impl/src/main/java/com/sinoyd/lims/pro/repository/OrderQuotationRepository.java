package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoOrderQuotation;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * OrderQuotation数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
public interface OrderQuotationRepository extends IBaseJpaRepository<DtoOrderQuotation, String> {

    /**
     * 根据订单获取费用记录
     * @param orderId 订单id
     * @return 费用记录
     */
    DtoOrderQuotation findByOrderId(String orderId);

    /**
     * 订单ids获取费用记录
     * @param orderIds 订单ids
     * @return 费用记录
     */
    List<DtoOrderQuotation> findByOrderIdIn(List<String> orderIds);
}