package com.sinoyd.lims.monitor.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.FixedPoint2TestService;
import com.sinoyd.lims.monitor.criteria.FixedPoint2TestCriteria;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FixedPoint2Test服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: FixedPoint2Test服务")
 @RestController
 @RequestMapping("api/monitor/fixedPoint2Test")
 public class FixedPoint2TestController extends BaseJpaController<DtoFixedPoint2Test, String,FixedPoint2TestService> {


    /**
     * 分页动态条件查询FixedPoint2Test
     * @param fixedPoint2TestCriteria 条件参数
     * @return RestResponse<List<FixedPoint2Test>>
     */
     @ApiOperation(value = "分页动态条件查询FixedPoint2Test", notes = "分页动态条件查询FixedPoint2Test")
     @GetMapping
     public RestResponse<List<DtoFixedPoint2Test>> findByPage(FixedPoint2TestCriteria fixedPoint2TestCriteria) {
         PageBean<DtoFixedPoint2Test> pageBean = super.getPageBean();
         RestResponse<List<DtoFixedPoint2Test>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fixedPoint2TestCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FixedPoint2Test
     * @param id 主键id
     * @return RestResponse<DtoFixedPoint2Test>
     */
     @ApiOperation(value = "按主键查询FixedPoint2Test", notes = "按主键查询FixedPoint2Test")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFixedPoint2Test> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFixedPoint2Test> restResponse = new RestResponse<>();
         DtoFixedPoint2Test fixedPoint2Test = service.findOne(id);
         restResponse.setData(fixedPoint2Test);
         restResponse.setRestStatus(StringUtil.isNull(fixedPoint2Test) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FixedPoint2Test
     * @param fixedPoint2Test 实体列表
     * @return RestResponse<DtoFixedPoint2Test>
     */
     @ApiOperation(value = "新增FixedPoint2Test", notes = "新增FixedPoint2Test")
     @PostMapping
     public RestResponse<DtoFixedPoint2Test> create(@RequestBody DtoFixedPoint2Test fixedPoint2Test) {
         RestResponse<DtoFixedPoint2Test> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fixedPoint2Test));
         return restResponse;
      }

     /**
     * 新增FixedPoint2Test
     * @param fixedPoint2Test 实体列表
     * @return RestResponse<DtoFixedPoint2Test>
     */
     @ApiOperation(value = "修改FixedPoint2Test", notes = "修改FixedPoint2Test")
     @PutMapping
     public RestResponse<DtoFixedPoint2Test> update(@RequestBody DtoFixedPoint2Test fixedPoint2Test) {
         RestResponse<DtoFixedPoint2Test> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fixedPoint2Test));
         return restResponse;
      }

    /**
     * "根据id批量删除FixedPoint2Test
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FixedPoint2Test", notes = "根据id批量删除FixedPoint2Test")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }