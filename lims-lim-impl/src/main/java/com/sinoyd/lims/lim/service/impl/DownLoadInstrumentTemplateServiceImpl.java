package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.customer.DtoImportInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.lims.lim.dto.customer.DtoImportInstrumentExpend;
import com.sinoyd.lims.lim.service.DownLoadInstrumentTemplateService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模板下载
 * <AUTHOR>
 * @version V1.0.0 2022/3/24
 * @since V100R001
 */
@Service
public class DownLoadInstrumentTemplateServiceImpl implements DownLoadInstrumentTemplateService {

    /**
     * 仪器状态
     */
    private static final List<String> insStatus = new ArrayList<>();

    /**
     * 计量类型
     */
    private static final List<String> meteringType = new ArrayList<>();
    static {
        insStatus.add(EnumBase.EnumInstrumentStatus.正常.name());
        insStatus.add(EnumBase.EnumInstrumentStatus.报废.name());
        insStatus.add(EnumBase.EnumInstrumentStatus.停用.name());
        meteringType.add(EnumBase.EnumOriginType.校准.name());
        meteringType.add(EnumBase.EnumOriginType.检定.name());
        meteringType.add(EnumBase.EnumOriginType.自校.name());
    }

    //region 注入
    @Autowired
    private CodeService codeService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private ImportUtils importUtils;
    //endregion

    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    @Override
    public void downLoadExcel(HttpServletResponse response,Map<String,String> sheetNames,String fileName) {

        //region 获取关联参数
        // 获取所有的仪器类型
        List<String> insTypeNames = codeService.findCodes("LIM_InstrumentType").stream().map(DtoCode::getDictName).collect(Collectors.toList());
        // 获取所有的部门
        List<String> deptNames = departmentService.findAll().stream().map(DtoDepartment::getDeptName).collect(Collectors.toList());
        //endregion

        //region 获取关联数据集合
        // 获取关联数据
        List<DtoImportInstrumentExpend> insExtendList = getExtendData(insTypeNames, deptNames);
        // 获取仪器空数据
        List<DtoImportInstrument> instruments = getNullIns();
        //endregion

        //region 赋值工作单并下载
        // 获取需要下载的工作单
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportInstrument.class, DtoImportInstrumentExpend.class, instruments, insExtendList);
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName,response,workBook);
        //endregion
    }

    /**
     * 获取仪器导入关联数据
     *
     * @param insTypeNames 仪器类型数据
     * @param deptNames    部门数据
     * @return 关联集合
     */
    private List<DtoImportInstrumentExpend> getExtendData(List<String> insTypeNames,List<String> deptNames){
        //返回的数据集合
        List<DtoImportInstrumentExpend> insExtendList = new ArrayList<>();

        // 获取最长
        List<Integer> size = new ArrayList<>();
        size.add(insTypeNames.size());
        size.add(deptNames.size());
        size.add(insStatus.size());
        size.add(meteringType.size());
        Integer max = Collections.max(size);
        // 循环赋值第二种Sheet内容
        for (int i = 0; i < max; i++) {
            DtoImportInstrumentExpend insExtend = new DtoImportInstrumentExpend();
            insExtend.setDeptName(deptNames.size()<i+1 ? null : deptNames.get(i));
            insExtend.setInstrumentTypeName(insTypeNames.size()<i+1? null : insTypeNames.get(i));
            insExtend.setStatus(insStatus.size()<i+1 ? null : insStatus.get(i));
            insExtend.setMeteringType(meteringType.size()<i+1 ? null : meteringType.get(i));
            insExtendList.add(insExtend);
        }
        return insExtendList;
    }

    /**
     * 获取空仪器
     * 
     * @return 空仪器list
     */
    private List<DtoImportInstrument> getNullIns(){
        return new ArrayList<>();
    }
}
