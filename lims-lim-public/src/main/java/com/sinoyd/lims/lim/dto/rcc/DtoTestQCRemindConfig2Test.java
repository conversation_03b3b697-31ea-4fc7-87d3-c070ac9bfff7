package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.TestQCRemindConfig2Test;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoTestQCRemindConfig2Test实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_TestQCRemindConfig2Test")
@Data
@DynamicInsert
public class DtoTestQCRemindConfig2Test extends TestQCRemindConfig2Test {

    @Transient
    private String redAnalyzeMethodName;

    @Transient
    private String redCountryStandard;

    @Transient
    private String redAnalyzeItemName;

    @Transient
    private String sampleTypeName;

    @Transient
    private Boolean isOutsourcing;

    @Transient
    private Boolean isCompleteField;
}