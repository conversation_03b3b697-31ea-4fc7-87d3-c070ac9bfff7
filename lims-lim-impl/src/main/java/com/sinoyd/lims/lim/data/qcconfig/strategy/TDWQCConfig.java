package com.sinoyd.lims.lim.data.qcconfig.strategy;

import com.sinoyd.base.dto.rcc.DtoSubstitute;
import com.sinoyd.base.service.SubstituteService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.qcconfig.strategy.base.AbsQCConfig;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.service.TestService;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.persistence.Transient;
import java.util.List;

/**
 * 替代物质控限值接口
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/14
 */
@Component
@DependsOn({"springContextAware"})
@Slf4j
public class TDWQCConfig extends AbsQCConfig {

    private QualityControlLimitRepository qualityControlLimitRepository;

    private TestService testService;

    private SubstituteService substituteService;

    /**
     * 保存质控限值
     *
     * @param testQCRangeCopy 质控数据
     * @return 保存后的质控限值
     */
    @Override
    @Transient
    public DtoQualityControlLimit save(DtoQualityControlLimit testQCRangeCopy) {
        if (StringUtil.isNotNull(testQCRangeCopy)){
            testQCRangeCopy.setQcType(getQcType());
            //获取测试项目编号
            String testId = testQCRangeCopy.getTestId();
            //获取测试项目
            DtoTest test = testService.findOne(testId);
            //判断测试项目是否为总称
            if (StringUtil.isNotNull(test)){
                if (!test.getIsTotalTest()){
                    throw new BaseException("替代物只能配置在是否总称为是的测试项目上，请确认后再操作！");
                }
            }
            //设置替代物名称，编号
            String substituteId = testQCRangeCopy.getSubstituteId();
            DtoSubstitute substitute = substituteService.findOne(substituteId);
            if (StringUtil.isNotNull(substitute)){
                testQCRangeCopy.setSubstituteName(substitute.getCompoundName());
            }
        }
        return super.save(testQCRangeCopy);
    }

    /**
     * 查询此质控类型的所有测试项目关联的数据
     *
     * @param testId 测试项目编号
     * @return 查询结果
     */
    @Override
    public List<DtoQualityControlLimit> queryData(String testId) {
        return qualityControlLimitRepository.findByTestIdAndQcType(testId,getQcType());
    }

    /**
     * 获取质控类型
     *
     * @return 质控类型
     */
    @Override
    public Integer getQcType() {
        return EnumLIM.EnumQCType.替代物.getValue();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return 16;
    }

    /**
     * 获取质控类型名称
     *
     * @return 质控类型名称
     */
    @Override
    public String getQcTypeName() {
        return EnumLIM.EnumQCType.替代物.name();
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }

    @Autowired
    public void setSubstituteService(SubstituteService substituteService) {
        this.substituteService = substituteService;
    }

    @Autowired
    public void setTestService(TestService testService) {
        this.testService = testService;
    }
}
