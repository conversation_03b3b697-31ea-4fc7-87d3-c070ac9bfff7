package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评价信息查询条件
 * <AUTHOR>
 * @version 1.0.0 2019/5/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntEvaluationCriteria extends BaseCriteria {
    /**
     *  评价状态（-1代表所有）
     */
    private Integer status;
    /**
     *  默认当前企业的id
     */
    private String entId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuffer condition = new StringBuffer();

        if (status >= 0) {
            condition.append(" and (status = :status)");
            values.put("status", this.status);
        }

        if (StringUtils.isNotNullAndEmpty(entId) &&
                !UUIDHelper.GUID_EMPTY.equals(this.entId)) {
            condition.append(" and entId = :entId");
            values.put("entId", this.entId);
        }
        return condition.toString();
    }
}
