package com.sinoyd.lims.lim.dto.customer.expimp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;

/**
 * 固定资产导入导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@Data
public class DtoExpImpFixedProperty extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id（新增时不填）", orderNum = "90", width = 20)
    private String id;

    /**
     * 固定资产名称
     */
    @Excel(name = "固定资产名称(必填)", orderNum = "100", width = 20)
    private String assetsName;

    /**
     * 品牌型号
     */
    @Excel(name = "品牌型号", orderNum = "200", width = 20)
    private String brandModel;

    /**
     * 资产编号
     */
    @Excel(name = "资产编号(必填)", orderNum = "300", width = 20)
    private String assetsNo;

    /**
     * 采购时间
     */
    @Excel(name = "采购时间", orderNum = "400", width = 20)
    private String purchaseDate;

    /**
     * 采购价格
     */
    @Excel(name = "采购价格", orderNum = "500", width = 20)
    private String purchasePrice;

    /**
     * 供应商（Guid）
     */
    @Excel(name = "供应商", orderNum = "600", width = 20)
    private String supplier;

    /**
     * 资产类型
     */
    @Excel(name = "资产类型(必填)", orderNum = "700", width = 20)
    private String assetsType;

    /**
     * 所属科室（Guid）
     */
    @Excel(name = "所属科室(必填)", orderNum = "800", width = 20)
    private String deptId;

    /**
     * 资产状态 (1使用中，2已报废)
     */
    @Excel(name = "资产状态(必填)", replace = {"使用中_1", "已报废_2", "_null"}, orderNum = "900", width = 20)
    private String status;

    /**
     * 管理人员（Guid）
     */
    @Excel(name = "管理人员(必填)", orderNum = "1000", width = 20)
    private String manager;

}
