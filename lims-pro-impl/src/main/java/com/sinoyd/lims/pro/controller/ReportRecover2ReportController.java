package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ReportRecover2ReportService;
import com.sinoyd.lims.pro.criteria.ReportRecover2ReportCriteria;
import com.sinoyd.lims.pro.dto.DtoReportRecover2Report;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * reportrecover2report服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
 @Api(tags = "示例: reportrecover2report服务")
 @RestController
 @RequestMapping("api/pro/reportrecover2report")
 public class ReportRecover2ReportController extends BaseJpaController<DtoReportRecover2Report, String, ReportRecover2ReportService> {


    /**
     * 分页动态条件查询reportrecover2report
     * @param reportrecover2reportCriteria 条件参数
     * @return RestResponse<List<reportrecover2report>>
     */
     @ApiOperation(value = "分页动态条件查询reportrecover2report", notes = "分页动态条件查询reportrecover2report")
     @GetMapping
     public RestResponse<List<DtoReportRecover2Report>> findByPage(ReportRecover2ReportCriteria reportrecover2reportCriteria) {
         PageBean<DtoReportRecover2Report> pageBean = super.getPageBean();
         RestResponse<List<DtoReportRecover2Report>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, reportrecover2reportCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询reportrecover2report
     * @param id 主键id
     * @return RestResponse<Dtoreportrecover2report>
     */
     @ApiOperation(value = "按主键查询reportrecover2report", notes = "按主键查询reportrecover2report")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoReportRecover2Report> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoReportRecover2Report> restResponse = new RestResponse<>();
         DtoReportRecover2Report reportrecover2report = service.findOne(id);
         restResponse.setData(reportrecover2report);
         restResponse.setRestStatus(StringUtil.isNull(reportrecover2report) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增reportrecover2report
     * @param reportrecover2report 实体列表
     * @return RestResponse<Dtoreportrecover2report>
     */
     @ApiOperation(value = "新增reportrecover2report", notes = "新增reportrecover2report")
     @PostMapping
     public RestResponse<DtoReportRecover2Report> create(@RequestBody DtoReportRecover2Report reportrecover2report) {
         RestResponse<DtoReportRecover2Report> restResponse = new RestResponse<>();
         restResponse.setData(service.save(reportrecover2report));
         return restResponse;
      }

     /**
     * 新增reportrecover2report
     * @param reportrecover2report 实体列表
     * @return RestResponse<Dtoreportrecover2report>
     */
     @ApiOperation(value = "修改reportrecover2report", notes = "修改reportrecover2report")
     @PutMapping
     public RestResponse<DtoReportRecover2Report> update(@RequestBody DtoReportRecover2Report reportrecover2report) {
         RestResponse<DtoReportRecover2Report> restResponse = new RestResponse<>();
         restResponse.setData(service.update(reportrecover2report));
         return restResponse;
      }

    /**
     * "根据id批量删除reportrecover2report
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除reportrecover2report", notes = "根据id批量删除reportrecover2report")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }