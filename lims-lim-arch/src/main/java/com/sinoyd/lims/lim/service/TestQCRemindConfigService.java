package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig;

/**
 * 质控比例基本信息管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/18
 * @since V100R001
 */
public interface TestQCRemindConfigService extends IBaseJpaService<DtoTestQCRemindConfig, String> {

    /**
     * 获取相应的质控比例的配置数据
     *
     * @param testId  测试项目id
     * @param qcType  质控类型
     * @param qcGrade 质控等级
     * @return 返回相应的质控比例配置数据
     */
    DtoTestQCRemindConfig findByTestIdAndQcTypeAndQCGrade(String testId, Integer qcType, Integer qcGrade);
}