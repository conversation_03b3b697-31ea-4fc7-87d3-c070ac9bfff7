package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * AnalyseAwaitCriteria待检样品查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReceiveSampleRecordParamTemplateCriteria extends BaseCriteria implements Serializable {

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 样品类型id列表
     */
    private List<String> sampleTypeIdList;


    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
            condition.append(" and t.receiveId = :receiveId");
            values.put("receiveId", this.receiveId);
        }
        if (StringUtil.isNotEmpty(this.templateName)) {
            condition.append(" and t.templateName like :templateName");
            values.put("templateName", "%" + this.templateName + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleTypeIdList)) {
            condition.append(" and t.sampleTypeId in :sampleTypeIdList ");
            values.put("sampleTypeIdList", this.sampleTypeIdList);
        }
        return condition.toString();
    }
}
