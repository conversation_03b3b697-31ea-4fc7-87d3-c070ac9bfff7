package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.FlowCalibration2FrequencyCriteria;
import com.sinoyd.lims.pro.criteria.FolderPeriodCriteria;
import com.sinoyd.lims.pro.dto.DtoFlowCalibration2Frequency;
import com.sinoyd.lims.pro.dto.DtoSamplingArrange;
import com.sinoyd.lims.pro.service.FlowCalibration2FrequencyService;
import com.sinoyd.lims.pro.service.SamplingArrangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流量校准关联点位服务
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/19
 */
@Api(tags = "示例: 流量校准关联点位服务")
@RestController
@RequestMapping("api/mobile/flowCalibration2Frequency")
public class MobileFlowCalibration2FrequencyController
        extends BaseJpaController<DtoFlowCalibration2Frequency,String, FlowCalibration2FrequencyService> {

    @Autowired
    @Lazy
    private SamplingArrangeService samplingArrangeService;

    /**
     * 分页查询
     * @param criteria 查询条件
     * @return 数据
     */
    @GetMapping
    public RestResponse<List<DtoFlowCalibration2Frequency>> findByPage(FlowCalibration2FrequencyCriteria criteria){
        RestResponse<List<DtoFlowCalibration2Frequency>> response = new RestResponse<>();
        PageBean<DtoFlowCalibration2Frequency> page = super.getPageBean();
        service.findByPage(page,criteria);
        response.setData(page.getData());
        response.setCount(page.getRowsCount());
        return response;
    }

    /**
     * 批量新增
     * @param list 对象集合
     * @return 数据
     */
    @PostMapping("/batch")
    public RestResponse<List<DtoFlowCalibration2Frequency>> batchSave(@RequestBody List<DtoFlowCalibration2Frequency> list){
        RestResponse<List<DtoFlowCalibration2Frequency>> response = new RestResponse<>();
        response.setData(service.save(list));
        return response;
    }


    /**
     * 批量删除
     * @param ids 标识集合
     * @return 数据
     */
    @DeleteMapping
    public RestResponse<Integer> logicDeleteById(@RequestBody List<String> ids){
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.logicDeleteById(ids));
        return response;
    }

    /**
     * 点位周期动态分页查询
     * @param folderPeriodCriteria 查询条件
     * @return 安排列表
     */
    @GetMapping("/folderPeriodPage")
    @ApiOperation(value = "点位周期动态分页查询", notes = "点位周期动态分页查询")
    public RestResponse<List<DtoSamplingArrange>> folderPeriodPageQuery(FolderPeriodCriteria folderPeriodCriteria){
        RestResponse<List<DtoSamplingArrange>> res = new RestResponse<>();
        PageBean<DtoSamplingArrange> pageBean = super.getPageBean();
        samplingArrangeService.folderPeriodPageQuery(pageBean, folderPeriodCriteria);
        res.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        res.setData(pageBean.getData());
        res.setCount(pageBean.getRowsCount());
        return res;
    }
}
