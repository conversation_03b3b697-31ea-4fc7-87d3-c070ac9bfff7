package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.FileAuditCriteria;
import com.sinoyd.lims.pro.dto.DtoFileAudit;
import com.sinoyd.lims.pro.service.FileAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * FileAudit服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/19
 * @since V100R001
 */
 @Api(tags = "示例: FileAudit服务")
 @RestController
 @RequestMapping("api/pro/fileAudit")
 public class FileAuditController extends BaseJpaController<DtoFileAudit, String, FileAuditService> {
    /**
     * 分页动态条件查询
     *
     * @param criteria 条件参数
     * @return RestResponse<List < DtoFileAudit>>
     */
    @ApiOperation(value = "分页动态条件查询", notes = "分页动态条件查询")
    @GetMapping
    public RestResponse<List<DtoFileAudit>> findByPage(FileAuditCriteria criteria) {
        PageBean<DtoFileAudit> pageBean = super.getPageBean();
        RestResponse<List<DtoFileAudit>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增
     *
     * @param fileAudit 对象
     * @return RestResponse<DtoFileAudit>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping
    public RestResponse<DtoFileAudit> save(@RequestBody DtoFileAudit fileAudit) {
        RestResponse<DtoFileAudit> restResponse = new RestResponse<>() ;
        restResponse.setData(service.save(fileAudit));
        return restResponse;
    }

    /**
     * 修改
     *
     * @param fileAudit 对象
     * @return RestResponse<DtoFileAudit>
     */
    @ApiOperation(value = "修改", notes = "修改")
    @PutMapping
    public RestResponse<DtoFileAudit> update(@RequestBody DtoFileAudit fileAudit) {
        RestResponse<DtoFileAudit> restResponse = new RestResponse<>() ;
        restResponse.setData(service.update(fileAudit));
        return restResponse;
    }

    /**
     * 修改文件关联关系
     *
     * @param fileAudit 对象
     * @return RestResponse<DtoFileAudit>
     */
    @ApiOperation(value = "修改文件关联关系", notes = "修改文件关联关系")
    @PutMapping("/updateFileRelation")
    public RestResponse<Void> updateFileRelation(@RequestBody DtoFileAudit fileAudit) {
        RestResponse<Void> restResponse = new RestResponse<>() ;
        service.updateFileRelation(fileAudit);
        return restResponse;
    }

    /**
     * 批量删除校准记录
     *
     * @param ids 对象标识集合
     * @return RestResponse<Integer>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody List<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>() ;
        restResponse.setData(service.logicDeleteById(ids));
        return restResponse;
    }

    /**
     * 详情
     *
     * @param id 标识
     * @return RestResponse<DtoFileAudit>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/{id}")
    public RestResponse<DtoFileAudit> findOne(@PathVariable String id) {
        RestResponse<DtoFileAudit> restResponse = new RestResponse<>() ;
        restResponse.setData(service.findOne(id));
        return restResponse;
    }

    /**
     * 提交
     *
     * @param params 参数
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "提交", notes = "提交")
    @PostMapping("/submit")
    public RestResponse<Void> submit(@RequestBody Map<String,Object> params) {
        RestResponse<Void> restResponse = new RestResponse<>() ;
        service.submit(params);
        return restResponse;
    }
 }