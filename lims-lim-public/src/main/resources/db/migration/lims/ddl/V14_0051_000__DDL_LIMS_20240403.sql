DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_MONITOR_PropertyPoint2Test' AND column_name = 'samplePeriod')) THEN
ALTER TABLE TB_MONITOR_PropertyPoint2Test ADD COLUMN samplePeriod INT (11) NOT NULL DEFAULT 1 COMMENT '样次';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_MONITOR_PropertyPoint2Test' AND column_name = 'timesOrder')) THEN
ALTER TABLE TB_MONITOR_PropertyPoint2Test ADD COLUMN timesOrder INT (11) NOT NULL DEFAULT 1 COMMENT '批次';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_PRO_SamplingFrequencyTemp' AND column_name = 'samplePerTime')) THEN
ALTER TABLE TB_PRO_SamplingFrequencyTemp ADD COLUMN samplePerTime INT (11) NOT NULL DEFAULT 1 COMMENT '样品数';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_SampleType2Test' AND column_name = 'timesOrder')) THEN
ALTER TABLE TB_LIM_SampleType2Test ADD COLUMN timesOrder INT (11) NOT NULL DEFAULT 1 COMMENT '批次';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_SampleType2Test' AND column_name = 'samplePeriod')) THEN
ALTER TABLE TB_LIM_SampleType2Test ADD COLUMN samplePeriod INT (11) NOT NULL DEFAULT 1 COMMENT '样品数';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_Test' AND column_name = 'timesOrder')) THEN
ALTER TABLE TB_LIM_Test ADD COLUMN timesOrder INT (11) NOT NULL DEFAULT 1 COMMENT '批次';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_Test' AND column_name = 'sampleCount')) THEN
ALTER TABLE TB_LIM_Test CHANGE sampleCount samplePeriod INT (11);
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_TestExpand' AND column_name = 'timesOrder')) THEN
ALTER TABLE TB_LIM_TestExpand ADD COLUMN timesOrder INT (11) NOT NULL DEFAULT 1 COMMENT '批次';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;

DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_TestExpand' AND column_name = 'sampleCount')) THEN
ALTER TABLE TB_LIM_TestExpand CHANGE sampleCount samplePeriod INT (11);
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;
