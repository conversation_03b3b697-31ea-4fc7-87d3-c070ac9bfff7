package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.lims.lim.dto.lims.DtoPersonFaceMsg;
import com.sinoyd.lims.lim.repository.lims.PersonFaceMsgRepository;
import com.sinoyd.lims.lim.service.PersonFaceMsgService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.stereotype.Service;


/**
 * PersonFaceMsg操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/9/26
 * @since V100R001
 */
 @Service
public class PersonFaceMsgServiceImpl extends BaseJpaServiceImpl<DtoPersonFaceMsg,String,PersonFaceMsgRepository> implements PersonFaceMsgService {

    @Override
    public void findByPage(PageBean<DtoPersonFaceMsg> pb, BaseCriteria personFaceMsgCriteria) {
        pb.setEntityName("DtoPersonFaceMsg a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, personFaceMsgCriteria);
    }

    /**
     * 通过人员id获取face信息
     *
     * @param personId 人员id
     * @return face信息
     */
    @Override
    public DtoPersonFaceMsg getPersonFaceMsg(String personId) {
        return repository.findByPersonId(personId);
    }
}