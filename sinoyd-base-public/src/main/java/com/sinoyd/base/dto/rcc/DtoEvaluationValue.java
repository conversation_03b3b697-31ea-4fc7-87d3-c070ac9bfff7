package com.sinoyd.base.dto.rcc;

import com.sinoyd.base.entity.EvaluationValue;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoEvaluationValue实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_EvaluationValue")
 @Data
 @DynamicInsert
 public  class DtoEvaluationValue extends EvaluationValue {
    /**
     * 检测类型id
     */
    @Transient
    private String sampleTypeId;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 评价标准名称
     */
    @Transient
    private String evaluationName;

    /**
     * 评价标准等级名称
     */
    @Transient
    private String levelName;


    /**
     * 分析项目名称
     */
    @Transient
    private String analyzeItemName;

    /**
     * 量纲名称
     */
    @Transient
    private String dimensionName;
}