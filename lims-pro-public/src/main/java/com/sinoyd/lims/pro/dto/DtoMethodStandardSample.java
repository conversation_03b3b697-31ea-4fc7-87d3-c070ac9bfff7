package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 方法标准关联样品模型
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022/4/8
 */
@Data
public class DtoMethodStandardSample {

    /**
     * 样品Id
     */
    private String sampleId;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 点位名称
     */
    private String folderName;

    /**
     * 周期
     */
    private Integer cycleNum;

    /**
     * 频次
     */
    private Integer timesOrder;

    /**
     * 样品数
     */
    private Integer samplePeriod;
}