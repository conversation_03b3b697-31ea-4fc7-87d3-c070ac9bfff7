package com.sinoyd.lims.foreign.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataOutSorceDataVO;
import com.sinoyd.lims.foreign.DataSub2SampleVO;
import com.sinoyd.lims.foreign.service.DataOutSorceDataService;
import com.sinoyd.lims.foreign.service.DataSub2SampleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 领样单和样品关系服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataSub2Sample服务")
@RestController
@RequestMapping("api/pro/foreign/dataSub2Sample")
public class DataSub2SampleController extends ExceptionHandlerController<DataSub2SampleService> {


    /**
     * 数据同步
     *
     * @param map 同步参数
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataSub2SampleVO>> getSyncData(@RequestBody Map<String, Object> map) {
        List<DataSub2SampleVO> curveVOList = service.getSyncData(map);
        RestResponse<List<DataSub2SampleVO>> response = new RestResponse();
        response.setData(curveVOList);
        return response;
    }
}
