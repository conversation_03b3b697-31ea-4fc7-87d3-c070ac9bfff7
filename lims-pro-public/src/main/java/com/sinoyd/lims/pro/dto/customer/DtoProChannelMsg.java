package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import lombok.Data;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * 流程通知传输结构
 * <AUTHOR>
 * @version V1.0.0 2019/12/20
 * @since V100R001
 */
@Data
public class DtoProChannelMsg {
    /**
     * 获取消息
     */
    public String getMsg() {
        UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(this.user, null);
        SecurityContext ctx = SecurityContextHolder.getContext();
        ctx.setAuthentication(token);

        return this.msg;
    }

    /**
     * 当前人员
     */
    private CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();

    /**
     * 消息
     */
    private String msg = "";
}
