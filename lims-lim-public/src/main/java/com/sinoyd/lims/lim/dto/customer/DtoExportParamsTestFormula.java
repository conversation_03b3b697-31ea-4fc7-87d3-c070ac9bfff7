package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Table;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * 公式中的参数导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/6
 * @since V100R001
 */
@Table(name = "TB_LIM_ParamsTestFormula")
@Data
public class DtoExportParamsTestFormula extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id;

    /**
     * 对象id（如测试公式id）
     */
    @Excel(name = "对象id", orderNum = "20")
    private String objId;

    /**
     * 参数id
     */
    @Excel(name = "参数id", orderNum = "30")
    private String paramsId;

    /**
     * 参数名称
     */
    @Excel(name = "参数名称", orderNum = "40")
    private String paramsName;

    /**
     * 参数别名
     */
    @Excel(name = "参数别名", orderNum = "50")
    private String alias;

    /**
     * 默认值
     */
    @Excel(name = "默认值", orderNum = "60")
    private String defaultValue;

    /**
     * 排序值
     */
    @Excel(name = "排序值", orderNum = "70")
    @Pattern(regexp = "^-?\\d+$", message = "排序值必须为整数")
    private String orderNum;

    /**
     * 检测单模板中的变量名
     */
    @Excel(name = "检测单模板中的变量名", orderNum = "80")
    private String aliasInReport;

    /**
     * 计量单位id
     */
    @Excel(name = "计量单位id", orderNum = "90")
    private String dimensionId;

    /**
     * 计量单位
     */
    @Excel(name = "计量单位", orderNum = "100")
    private String dimension;

    /**
     * 类型（枚举EnumSourceType：0.无,1.样品，2.测试，3.企业，4.原始记录单）
     */
    @Excel(name = "类型", orderNum = "110")
    @Pattern(regexp = "^-?\\d+$", message = "类型必须为整数")
    private String sourceType;

    /**
     * 是否必填
     */
    @Excel(name = "是否必填", orderNum = "120")
    @Pattern(regexp = "^(true|false)$", message = "是否必填必须为布尔类型")
    private String isMust;

    /**
     * 是否允许修改
     */
    @Excel(name = "是否允许修改", orderNum = "130")
    @Pattern(regexp = "^(true|false)$", message = "是否允许修改必须为布尔类型")
    private String isEditable;

    /**
     * 检出限
     */
    @Excel(name = "检出限", orderNum = "140")
    @Pattern(regexp = "^[+-]?(\\d+\\.?\\d*|\\.\\d+)$", message = "检出限格式不符合要求")
    private String detectionLimit;

    /**
     * 计算方式：枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零
     */
    @Excel(name = "计算方式", orderNum = "150")
    @Pattern(regexp = "^-?\\d+$", message = "计算方式必须为整数")
    private String calculationMode;
}
