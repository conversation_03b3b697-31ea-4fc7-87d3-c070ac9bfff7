package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.monitor.criteria.StatisticCriteria;
import com.sinoyd.lims.pro.service.StatisticDataHandleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 统计表导出数据处理服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/08
 * @since V100R001
 */
@Api(tags = "示例: 统计表导出数据处理服务接口定义")
@RestController
@RequestMapping("api/pro/statisticDataHandle")
public class StatisticDataHandleController extends ExceptionHandlerController<StatisticDataHandleService> {

    /**
     * 获取统计表初始化数据
     *
     * @param criteria  数据查询条件
     * @param sort      排序
     * @param isOverRed 是否标红
     * @return 初始化数据
     */
    @ApiOperation(value = "获取统计表初始化数据", notes = "获取统计表初始化数据")
    @PostMapping("/exportData")
    public RestResponse<Map<String, Object>> getInitDataMap(@RequestBody StatisticCriteria criteria, String sort, Boolean isOverRed) {
        RestResponse<Map<String, Object>> response = new RestResponse<>();
        Map<String, Object> data = service.initDataMap(criteria, isOverRed, sort);
        response.setData(data);
        response.setCount(0);
        response.setMsg("操作成功");
        return response;
    }
}
