package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 最小二乘类
 * <AUTHOR>
 * @version V1.0.0 2019/12/27
 * @since V100R001
 */
@Data
public class LeastSquare {
    /**
     * 舍入精度
     */
    private final Integer ROUNDING_MODE = 20;

    /**
     * x值
     */
    private BigDecimal[] x;

    /**
     * x的总和
     */
    private BigDecimal xSum;


    /**
     * x均值
     */
    private BigDecimal xAvg;

    /**
     * y值
     */
    private BigDecimal[] y;

    /**
     * y的总和
     */
    private BigDecimal ySum;

    /**
     * y均值
     */
    private BigDecimal yAvg;

    /**
     * 权重
     */
    private BigDecimal[] weight;

    /**
     * 多项式个数
     */
    private Integer n;

    /**
     * 多项式系数
     */
    private BigDecimal[] coefficient;

    /**
     * 是否强制零点
     */
    private Boolean forcedZero;

    /**
     * Constructor method.
     *
     * @param x Array of x
     * @param y Array of y
     * @param n The order of polynomial
     */
    public LeastSquare(BigDecimal[] x, BigDecimal[] y, Integer n,Boolean forcedZero) {
        if (StringUtil.isNull(x) || StringUtil.isNull(y) || x.length < 2 || x.length != y.length
                || n < 2) {
            throw new BaseException(
                    "输入的组数或数据不满足计算条件！");
        }
        this.x = x;
        this.xSum = Arrays.stream(x).reduce(BigDecimal.ZERO, (sum, item) -> sum.add(item));
        this.xAvg = this.xSum.divide(BigDecimal.valueOf(this.x.length), ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
        this.y = y;
        this.ySum = Arrays.stream(y).reduce(BigDecimal.ZERO, (sum, item) -> sum.add(item));
        this.yAvg = this.ySum.divide(BigDecimal.valueOf(this.y.length), ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
        this.n = n;
        this.forcedZero = forcedZero;
        weight = new BigDecimal[x.length];
        for (int i = 0; i < x.length; i++) {
            weight[i] = BigDecimal.ONE;
        }
        if (forcedZero) {
            this.calculate();
        } else {
            this.compute();
        }
    }

    /**
     * Constructor method.
     *
     * @param x      Array of x
     * @param y      Array of y
     * @param weight Array of weight
     * @param n      The order of polynomial
     */
    public LeastSquare(BigDecimal[] x, BigDecimal[] y, BigDecimal[] weight, Integer n) {
        if (StringUtil.isNull(x) || StringUtil.isNull(y) || StringUtil.isNull(weight) || x.length < 2
                || x.length != y.length || x.length != weight.length || n.intValue() < 1) {
            throw new BaseException(
                    "输入的组数或数据不满足计算条件！");
        }
        this.x = x;
        this.xSum = Arrays.stream(x).reduce(BigDecimal.ZERO, (sum, item) -> sum.add(item));
        this.y = y;
        this.ySum = Arrays.stream(y).reduce(BigDecimal.ZERO, (sum, item) -> sum.add(item));
        this.n = n;
        this.weight = weight;
        this.compute();
    }

    /**
     * Get coefficient of polynomial.
     *
     * @return coefficient of polynomial
     */
    public BigDecimal[] getCoefficient() {
        return this.coefficient;
    }

    /**
     * Get rcoefficient
     *
     * @return rcoefficient
     */
    public BigDecimal computeRCoefficent() {
        BigDecimal cov = BigDecimal.ZERO;
        BigDecimal dx = BigDecimal.ZERO;
        BigDecimal dy = BigDecimal.ZERO;
        for (Integer i = 0; i < this.x.length; i++) {
            cov = cov.add(this.x[i].multiply(this.y[i]));
            dx = dx.add(this.x[i].subtract(xAvg).pow(2));
            dy = dy.add(this.y[i].subtract(yAvg).pow(2));
        }
        cov = cov.divide(BigDecimal.valueOf(this.x.length), ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).subtract(xAvg.multiply(yAvg));
        dx = dx.divide(BigDecimal.valueOf(this.x.length), ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
        dy = dy.divide(BigDecimal.valueOf(this.y.length), ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
        return cov.divide(this.sqrt(dx.multiply(dy)), ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
    }

    private void calculate() {
        if (StringUtil.isNull(x) || StringUtil.isNull(y) || x.length <= 1 || x.length != y.length
                || x.length < n || n < 2) {
            return;
        }
        //x*y的和
        BigDecimal sxy = BigDecimal.ZERO;
        for (Integer i = 0; i < x.length; i++) {
            sxy = sxy.add(x[i].multiply(y[i]));
        }
        //x的平方
        BigDecimal sxx = BigDecimal.ZERO;
        for (Integer i = 0; i < x.length; i++) {
            sxx = sxx.add(x[i].pow(2));
        }

        BigDecimal[] result = new BigDecimal[n];
        for (Integer i = 0; i < n; i++) {
            //0表示b 固定为0
            if (i.compareTo(0) == 0) {
                result[i] = BigDecimal.ZERO;
            }else {
                //1表示k 结果x*y/x*x
                result[i] = sxy.divide(sxx, ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
            }
        }
        this.coefficient = result;
    }

    /**
     * 求根
     *
     * @param value 值
     * @return 根
     */
    private BigDecimal sqrt(BigDecimal value) {
        final BigDecimal EPS = BigDecimal.valueOf(0.0000001d);
        BigDecimal num2 = BigDecimal.valueOf(2);
        BigDecimal x1 = BigDecimal.ZERO;
        BigDecimal x2 = value;
        do {
            x1 = x2;
            x2 = (x1.add(value.divide(x1, ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN))).divide(num2, ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);

        } while (x1.subtract(x2).abs().compareTo(EPS) > 0);
        return x2;
    }


    /**
     * Used to calculate value by given x.
     *
     * @param x x
     * @return y
     */
    private BigDecimal fit(BigDecimal x) {
        if ( StringUtil.isNull(coefficient)) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = BigDecimal.ZERO;
        for (int i = 0; i < coefficient.length; i++) {
            sum = sum.add(x.pow(i).multiply(coefficient[i]));
        }
        return sum;
    }

    /**
     * Use Newton's method to solve equation.
     *
     * @param y y
     * @return x
     */
    public BigDecimal solve(BigDecimal y) {
        return this.solve(y, BigDecimal.ONE);
    }

    /**
     * Use Newton's method to solve equation.
     *
     * @param y      y
     * @param startX The start point of x
     * @return x
     */
    private BigDecimal solve(BigDecimal y, BigDecimal startX) {
        final BigDecimal EPS = BigDecimal.valueOf(0.0000001d);
        if ( StringUtil.isNull(coefficient)) {
            return BigDecimal.ZERO;
        }
        BigDecimal x1 = BigDecimal.ZERO;
        BigDecimal x2 = startX;
        do {
            x1 = x2;
            x2 = x1.subtract((this.fit(x1).subtract(y)).divide(this.calcReciprocal(x1), ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN));
        } while (x1.subtract(x2).abs().compareTo(EPS) > 0);
        return x2;
    }

    /*
     * Calculate the reciprocal of x.
     * @param x x
     * @return the reciprocal of x
     */
    private BigDecimal calcReciprocal(BigDecimal x) {
        if ( StringUtil.isNull(coefficient)) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = BigDecimal.ZERO;
        for (Integer i = 1; i < coefficient.length; i++) {
            sum = sum.add(BigDecimal.valueOf(i).multiply(x.pow(i - 1).multiply(coefficient[i])));
        }
        return sum;
    }

    /*
     * This method is used to calculate each elements of augmented matrix.
     */
    private void compute() {
        if (StringUtil.isNull(x) || StringUtil.isNull(y) || x.length <= 1 || x.length != y.length
                || x.length < n || n < 2) {
            return;
        }
        BigDecimal[] s = new BigDecimal[(n - 1) * 2 + 1];
        for (int i = 0; i < s.length; i++) {
            for (Integer j = 0; j < x.length; j++) {
                if (StringUtil.isNull(s[i])) {
                    s[i] = BigDecimal.ZERO;
                }
                s[i] = s[i].add(x[j].pow(i).multiply(weight[j]));
            }
        }
        BigDecimal[] b = new BigDecimal[n];
        for (int i = 0; i < b.length; i++) {
            for (Integer j = 0; j < x.length; j++) {
                if (StringUtil.isNull(b[i])) {
                    b[i] = BigDecimal.ZERO;
                }
                b[i] = b[i].add(x[j].pow(i).multiply(y[j]).multiply(weight[j]));
            }
        }
        BigDecimal[][] a = new BigDecimal[n][n];
        for (Integer i = 0; i < n; i++) {
            for (Integer j = 0; j < n; j++) {
                a[i][j] = s[i + j];
            }
        }

        // Now we need to calculate each coefficients of augmented matrix
        coefficient = this.calcLinearEquation(a, b);
    }

    /*
     * Calculate linear equation.
     * The matrix equation is like this: Ax=B
     * @param a two-dimensional array
     * @param b one-dimensional array
     * @return x, one-dimensional array
     */
    private BigDecimal[] calcLinearEquation(BigDecimal[][] a, BigDecimal[] b) {
        if (StringUtil.isNull(a) || StringUtil.isNull(b) || a.length == 0 || a.length != b.length) {
            return null;
        }

        for (BigDecimal[] x : a) {
            if (StringUtil.isNull(x) || x.length != a.length) {
                return null;
            }
        }

        Integer len = a.length - 1;
        BigDecimal[] result = new BigDecimal[a.length];

        if (len.equals(0)) {
            result[0] = b[0].divide(a[0][0], ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
            return result;
        }

        BigDecimal[][] aa = new BigDecimal[len][len];
        BigDecimal[] bb = new BigDecimal[len];
        Integer posx = -1;
        Integer posy = -1;
        for (Integer i = 0; i <= len; i++) {
            for (Integer j = 0; j <= len; j++) {
                if (a[i][j].compareTo(BigDecimal.ZERO) != 0) {
                    posy = j;
                    break;
                }
            }
            if (!posy.equals(-1)) {
                posx = i;
                break;
            }
        }
        if (posx.equals(-1)) {
            return null;
        }

        Integer count = 0;
        for (Integer i = 0; i <= len; i++) {
            if (i.equals(posx)) {
                continue;
            }
            bb[count] = b[i].multiply(a[posx][posy]).subtract(b[posx].multiply(a[i][posy]));
            int count2 = 0;
            for (Integer j = 0; j <= len; j++) {
                if (j.equals(posy)) {
                    continue;
                }
                aa[count][count2] = a[i][j].multiply(a[posx][posy]).subtract(a[posx][j].multiply(a[i][posy]));
                count2++;
            }
            count++;
        }

        // Calculate sub linear equation
        BigDecimal[] result2 = this.calcLinearEquation(aa, bb);

        // After sub linear calculation, calculate the current coefficient
        BigDecimal sum = b[posx];
        count = 0;
        for (Integer i = 0; i <= len; i++) {
            if (i.equals(posy)) {
                continue;
            }
            sum = sum.subtract(a[posx][i].multiply(result2[count]));
            result[i] = result2[count];
            count++;
        }
        result[posy] = sum.divide(a[posx][posy], ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
        return result;
    }
}

