package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import java.math.BigDecimal;


/**
 * RecAndPayRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="RecAndPayRecord")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class RecAndPayRecord implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  RecAndPayRecord() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 合同id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("合同id")
	private String contractId;
    
    /**
    * 类型(枚举EnumMoneyType： 1.收款 2.付款 3.坏账)
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("类型(枚举EnumMoneyType： 1.收款 2.付款 3.坏账)")
	private Integer moneyType;
    
    /**
    * 金额
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("金额")
	private BigDecimal amount;
    
    /**
    * 操作人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("操作人id")
	private String operatorId;
    
    /**
    * 操作人姓名
    */
    @Column(length=50)
    @ApiModelProperty("操作人姓名")
    @Length(message = "操作人姓名{validation.message.length}", max = 50)
	private String operatorName;
    
    /**
    * 操作日期
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("操作日期")
	private Date operatorDate;
    
    /**
    * 发票号码
    */
    @Column(length=50)
    @ApiModelProperty("发票号码")
    @Length(message = "发票号码{validation.message.length}", max = 50)
	private String invoiceNum;
    
    /**
    * 发票代码
    */
    @Column(length=50)
    @ApiModelProperty("发票代码")
    @Length(message = "发票代码{validation.message.length}", max = 50)
	private String invoiceCode;
    
    /**
    * 开票日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("开票日期")
	private Date invoiceDate;

    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 收款项
     */
    @Column
    @ApiModelProperty("收款项")
    //@NotBlank(message = "收款项{validation.message.blank}")
    @Length(message = "收款项{validation.message.length}", max = 255)
    private String collectItem;

    /**
     * 到款日期
     */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("到款日期")
    private Date receiveDate;

    /**
     * 是否开票
     */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("是否开票")
    private Boolean hasInvoice=true;

    /**
     * 是否到款
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否到款")
    private Boolean isReceive=false;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
 }