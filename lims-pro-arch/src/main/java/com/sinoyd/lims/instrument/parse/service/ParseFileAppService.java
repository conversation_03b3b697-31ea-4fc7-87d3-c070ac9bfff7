package com.sinoyd.lims.instrument.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.instrument.parse.dto.DtoParseFileApp;

import javax.servlet.http.HttpServletRequest;

/**
 * 仪器解析应用业务访问接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022/11/22
 */
public interface ParseFileAppService extends IBaseJpaService<DtoParseFileApp, String> {

    /**
     * 上传文件
     * @param appId 应用id
     * @param request 携带文件的请求
     * @return 上传状态
     */
    Boolean upload(String appId, HttpServletRequest request);
}
