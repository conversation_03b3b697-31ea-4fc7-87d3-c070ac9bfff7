package com.sinoyd.lims.pro.entity;


import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * SamplePreparation样品制备实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/17
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "SamplePreparation")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SamplePreparation extends LimsBaseEntity {
    private static final long serialVersionUID = 1L;

    public SamplePreparation() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 样品id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("样品id")
    private String sampleId;

    /**
     * 制备的样品下的分析项目名称
     */
    @Column(length = 100)
    @ApiModelProperty("制备的样品下的分析项目名称")
    @Length(message = "制备的样品下的分析项目名称{validation.message.length}", max = 100)
    private String analyzeItemNames;

    /**
     * 制备开始时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("制备开始时间")
    private Date preparationBeginTime;

    /**
     * 制备结束时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("制备结束时间")
    private Date preparationEndTime;

    /**
     * 制备人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("制备人id")
    private String preparedPersonId;

    /**
     * 制备人名称
     */
    @Column(length = 50)
    @ApiModelProperty("制备人名称")
    @Length(message = "制备人名称{validation.message.length}", max = 50)
    private String preparedPersonName;

    /**
     * 制备方法
     */
    @Column(length = 200,nullable = false)
    @ColumnDefault("''")
    @ApiModelProperty("制备方法")
    @Length(message = "制备方法{validation.message.length}", max = 200)
    private String method;

    @Column(length = 1000)
    @ColumnDefault("")
    @ApiModelProperty("仪器id，多个id用;隔开")
    private String instrumentId;

    /**
     * 制备内容
     */
    @Column(length = 500)
    @ApiModelProperty("制备内容")
    @Length(message = "制备内容{validation.message.length}", max = 500)
    private String content;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;
}
