INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('764cd9eb-fdd6-4d80-8a3d-882f701204e2', 1, 'WorkSheetFGGDTR', 'GJW-04-2019-YS-TR-015 分光光度法测定土壤项目原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-015 分光光度法测定土壤项目原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-015 分光光度法测定土壤项目原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleTest\", \"workSheetDataSourceType\" : \"FGGDTWorkSheetDataSourceImpl\", \"qCGroupByTest\" : \"1\", \"qCAnaLmtCnt\" : \"3\"}',
        0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 14:09:08',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 21:36:17',
        'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFGGDTR', b'0', '', NULL, '', '', '', 1, 4);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('8f257e83-17af-4d7d-8dea-cbfbae38bc5e', '764cd9eb-fdd6-4d80-8a3d-882f701204e2', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:09:47', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:09:47', NULL);

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('1cd22988-107a-40fb-b254-7e7731d3dd8d', 1, 'WorkSheetPHT', 'GJW-04-2019-YS-TR-007 土壤pH测定原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-007 土壤pH测定原始记录表.xlsx', 'output/WorkSheet/GJW-04-2019-YS-TR-007 土壤pH测定原始记录表.xlsx',
        'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleOnlyVertical\", \"workSheetDataSourceType\" : \"PHTWorkSheetDataSourceImpl\"}',
        0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 14:16:52',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 22:09:00',
        'workSheetFolderId,type', 'WorkSheet', 'WorkSheetPHT', b'0', '', NULL, '', '', '', 1, 5);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('9f8b1be6-39b6-4142-b069-6b37f26a9219', '1cd22988-107a-40fb-b254-7e7731d3dd8d', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:17:39', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:17:39', NULL);

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('49a5148f-067c-4d96-a762-f94f66efee28', 1, 'WorkSheetTRSFGWZ', 'GJW-04-2019-YS-TR-008 土壤水分含量干物质的量测定原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-008 土壤水分含量干物质的量测定原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-008 土壤水分含量干物质的量测定原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleOnly\", \"workSheetDataSourceType\" : \"TRSFGWZWorkSheetDataSourceImpl\"}',
        0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 14:21:03',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 22:12:34',
        'workSheetFolderId,type', 'WorkSheet', 'WorkSheetTRSFGWZ', b'0', '', NULL, '', '', '', 1, 1);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('56114652-d98a-4d11-bd82-57f7c51ccc8c', '49a5148f-067c-4d96-a762-f94f66efee28', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:21:34', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:21:34', NULL);

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('9af5ea1a-7515-49e8-a4a6-4b97361edbe9', 1, 'WorkSheetTRYJZ', 'GJW-04-2019-YS-TR-010 土壤有机质测定原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-010 土壤有机质测定原始记录表.xlsx', 'output/WorkSheet/GJW-04-2019-YS-TR-010 土壤有机质测定原始记录表.xlsx',
        'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleOnly\",  \"workSheetDataSourceType\" : \"TRYJZWorkSheetDataSourceImpl\" }',
        0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 22:17:36',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 22:17:36',
        'workSheetFolderId,type', 'WorkSheet', 'WorkSheetTRYJZ', b'0', '', NULL, '', '', '', 0, NULL);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('cd240cbc-fbb6-4790-ac77-407200838dd3', '9af5ea1a-7515-49e8-a4a6-4b97361edbe9', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 22:29:27', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 22:29:27', NULL);

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('46b9f25b-af56-42f2-bada-cf0bb833de8e', 1, 'WorkSheetTRYLZ', 'GJW-04-2019-YS-TR-009 土壤阳离子交换量测定原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-009 土壤阳离子交换量测定原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-009 土壤阳离子交换量测定原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleOnly\", \"workSheetDataSourceType\" : \"TRYLZWorkSheetDataSourceImpl\"}',
        0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 14:24:41',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 14:24:41',
        'workSheetFolderId,type', 'WorkSheet', 'WorkSheetTRYLZ', b'0', '', NULL, '', '', '', 0, NULL);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('6aaa58a8-9cb0-48fe-9a45-4400320464a3', '46b9f25b-af56-42f2-bada-cf0bb833de8e', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:25:35', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:25:35', NULL);

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('36906665-5b7a-4123-97a9-a24da98324a7', 1, 'WorkSheetYZXSTR', 'GJW-04-2019-YS-TR-011 原子吸收法测定土壤元素原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-011 原子吸收法测定土壤元素原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-011 原子吸收法测定土壤元素原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleTest\", \"workSheetDataSourceType\" : \"YZXSTRWorkSheetDataSourceImpl\", \"qCGroupByTest\" : \"1\", \"qCAnaLmtCnt\" : \"3\"}',
        0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 14:30:56',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 14:40:11',
        'workSheetFolderId,type', 'WorkSheet', 'WorkSheetYZXSTR', b'0', '', NULL, '', '', '', 0, NULL);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('d3ee5128-2d86-45a6-bdb4-ea5a8c535813', '36906665-5b7a-4123-97a9-a24da98324a7', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:35:18', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:35:18', NULL);

INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('5384edf0-d550-4a66-b391-b9a2b9099752', 1, 'WorkSheetYZYGTR', 'GJW-04-2019-YS-TR-013 原子荧光法测定土壤元素原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-013 原子荧光法测定土壤元素原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-013 原子荧光法测定土壤元素原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleTest\", \"workSheetDataSourceType\" : \"YZYGTRWorkSheetDataSourceImpl\", \"qCGroupByTest\" : \"1\", \"qCAnaLmtCnt\" : \"3\"}',
        0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 14:38:45',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-23 14:38:45',
        'workSheetFolderId,type', 'WorkSheet', 'WorkSheetYZYGTR', b'0', '', NULL, '', '', '', 0, NULL);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('7ee2fed2-3cac-4ac8-8b71-9dcc7b30df1f', '5384edf0-d550-4a66-b391-b9a2b9099752', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:39:18', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-23 14:39:18', NULL);
