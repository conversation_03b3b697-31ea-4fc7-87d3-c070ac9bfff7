package com.sinoyd.lims.pro.service;

import java.util.List;

import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentPurchaseDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;

/**
 * 仪器采购业务操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
public interface OAProcInstrumentPurchaseDetailService {
    /**
     * 添加审批并启动流程
     * 
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<List<DtoOAInstrumentPurchaseDetail>> taskDto);

    /**
     * 查询任务详细信息
     * 
     * @param taskId 任务ID
     * @return
     */
    DtoOATaskDetail<List<DtoOAInstrumentPurchaseDetail>, String> findOATaskDetail(String taskId);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    String draftSubmit(DtoOATaskCreate<List<DtoOAInstrumentPurchaseDetail>> taskDto);

    /**
     * 草稿提交
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask saveAsDraft(DtoOATaskCreate<List<DtoOAInstrumentPurchaseDetail>> taskDto);

    /**
     * 草稿提交
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask draftSave(DtoOATaskCreate<List<DtoOAInstrumentPurchaseDetail>> taskDto);
}
