package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * FlowCalibration2Frequency实体
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/15
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description="FlowCalibration2Frequency")
@Data
public class FlowCalibration2Frequency extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 流量校准记录标识
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("流量校准记录标识")
    private String flowCalibrationId;

    /**
     * 点位标识
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("点位标识")
    private String sampleFolderId;

    /**
     * 周期
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("周期")
    private Integer periodCount;
}
