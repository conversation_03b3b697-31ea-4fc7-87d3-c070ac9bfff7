package com.sinoyd.lims.pro.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@Accessors(chain = true)
public class OrderCustomerNumAnlVo extends BaseOrderCustomerAnalyzeVO{

    /**
     * 客户总数
     */
    private Integer customerNumTotal;

    /**
     * 新增客户
     */
    private Integer customerNumNew;

    /**
     * 流失客户
     */
    private Integer customerNumLose;
}
