package com.sinoyd.lims.lim.service;


import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigRecord;

import java.util.List;
import java.util.Map;

/**
 * ocr对象参数数据服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
public interface OcrConfigRecordService extends IBaseJpaService<DtoOcrConfigRecord, String> {

    /**
     * 根据样品编号查询最新数据
     *
     * @return 最新识别数据
     */
    Map<String, DtoOcrConfigRecord> getLatestDataMapByCode(List<String> sampleCodes);

    /**
     * 根据历史记录查询数据并按照样品编号过滤
     *
     * @return 最新识别数据
     */
    Map<String, DtoOcrConfigRecord> getDataMapByRecordIds(List<String> ocrConfigRecordIds, List<String> sampleCodes);

    void saveConfigParamData(DtoOcrConfigRecord entity);
}
