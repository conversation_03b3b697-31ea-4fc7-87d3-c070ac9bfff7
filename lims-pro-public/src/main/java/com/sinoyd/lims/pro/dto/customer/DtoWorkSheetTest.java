package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import javax.persistence.Transient;
import java.util.List;

/**
 * 检测单相关的测试项目信息
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoWorkSheetTest {

    /**
     * 测试项目id
     */
    private String id;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;

    /**
     * 分析项目id
     */
    private String redAnalyzeItemId;

    /**
     * 公式信息
     */
    private String formula;

    /**
     * 公式id
     */
    private String formulaId;

    /**
     * 小类的检测类型id（小类）
     */
    private String sampleTypeId;


    /**
     * 大类的检测类型id
     */
    private String bigSampleTypeId;

    /**
     * 参数公式集合
     */
    private List<String> paramsFormulaList;

    /**
     * 工作单中分析项目的排序值
     */
    private int analyseItemOrderNum;

    /**
     * 工作单选项卡下的测试项目id集合
     */
    private List<String> workSheetTestIds;

    /**
     * 有效期提示说明
     */
    private String tips;
}
