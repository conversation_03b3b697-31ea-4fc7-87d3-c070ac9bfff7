package com.sinoyd.lims.api.service.impl;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.api.dto.*;
import com.sinoyd.lims.api.service.InstrumentMonitorService;
import com.sinoyd.lims.lim.criteria.EnvironmentalRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails;
import com.sinoyd.lims.lim.entity.ProjectInstrumentDetails;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.ProjectInstrumentDetailsRepository;
import com.sinoyd.lims.lim.repository.lims.ProjectInstrumentRepository;
import com.sinoyd.lims.lim.service.EnvironmentalRecordService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.ProjectInstrumentDetailsService;
import com.sinoyd.lims.lim.service.ProjectInstrumentService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord;
import com.sinoyd.lims.pro.entity.ReceiveSubSampleRecord;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.ReceiveSampleRecordRepository;
import com.sinoyd.lims.pro.repository.ReceiveSubSampleRecordRepository;
import com.sinoyd.lims.pro.service.ProjectInstrumentQueryService;
import com.sinoyd.lims.pro.service.ProjectService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪器出入库移动端内容实现
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Service
public class InstrumentMonitorServiceImpl extends BaseJpaServiceImpl<DtoProjectInstrument, String, ProjectInstrumentRepository>
        implements InstrumentMonitorService {

    private ProjectInstrumentDetailsRepository projectInstrumentDetailsRepository;

    private ProjectInstrumentDetailsService projectInstrumentDetailsService;

    private ProjectInstrumentQueryService projectInstrumentQueryService;

    private ProjectInstrumentService projectInstrumentService;

    private InstrumentService instrumentService;

    private PersonService personService;

    private ProjectService projectService;

    private EnvironmentalRecordService environmentalRecordService;

    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;


    /**
     * 检索仪器出入库列表信息
     *
     * @param pb                             pb
     * @param projectInstrumentPhoneCriteria 查询条件
     * @return
     */
    @Override
    public List<DtoProjectInstrumentPhone> getProjectInstrumentList(PageBean<DtoProjectInstrument> pb, BaseCriteria projectInstrumentPhoneCriteria) {
        pb.setEntityName("DtoProjectInstrument a");
        pb.setSelect("select a ");
        comRepository.findByPage(pb, projectInstrumentPhoneCriteria);
        List<DtoProjectInstrument> datas = pb.getData();
        return fillInstrumentPhoneMessage(datas);
    }

    /**
     * 按照出入库记录id查询出入库记录明细
     *
     * @param key 出入库id
     * @return 出入库明细
     */
    @Override
    public DtoProjectInstrumentPhone findProjectInstrumentPhone(String key) {
        DtoProjectInstrumentPhone instrumentPhone = new DtoProjectInstrumentPhone();
        DtoProjectInstrument projectInstrument = projectInstrumentQueryService.findOne(key);
        BeanUtils.copyProperties(projectInstrument, instrumentPhone);
        List<DtoProjectInstrumentDetailPhone> detailPhoneList = new ArrayList<>();
        projectInstrument.getDetailsList().forEach(p -> {
            DtoProjectInstrumentDetailPhone detailPhone = new DtoProjectInstrumentDetailPhone();
            BeanUtils.copyProperties(p, detailPhone);
            detailPhone.setInstrumentCode(p.getInstrumentsCode());
            detailPhoneList.add(detailPhone);
        });
        //排序
        List<DtoProjectInstrumentDetailPhone> sortDetailPhoneList = sortDetailPhone(detailPhoneList);
        instrumentPhone.setDetailPhoneList(sortDetailPhoneList);
        return instrumentPhone;
    }

    /**
     * 出入库明细排序 优先排待入库的，然后排已入库的，在这个基础上，按仪器名称顺序排列 BUG2023070596613
     *
     * @param detailPhoneList 待排序的出入库明细
     */
    private List<DtoProjectInstrumentDetailPhone> sortDetailPhone(List<DtoProjectInstrumentDetailPhone> detailPhoneList) {
        List<DtoProjectInstrumentDetailPhone> sortList = new ArrayList<>();
        List<DtoProjectInstrumentDetailPhone> storageList = new ArrayList<>();
        for (DtoProjectInstrumentDetailPhone detailPhone : detailPhoneList) {
            if (detailPhone.getIsStorage()) {
                storageList.add(detailPhone);
            } else {
                sortList.add(detailPhone);
            }
        }
        sortList.sort(Comparator.comparing(DtoProjectInstrumentDetailPhone::getInstrumentName));
        if (StringUtil.isNotEmpty(storageList)) {
            storageList.sort(Comparator.comparing(DtoProjectInstrumentDetailPhone::getInstrumentName));
            sortList.addAll(storageList);
        }
        return sortList;
    }

    /**
     * 保存仪器出入库记录
     *
     * @param instrumentPhone 仪器出入库记录信息
     * @return 出入库记录
     */
    @Transactional
    @Override
    public DtoProjectInstrumentPhone saveInstrumentPhone(DtoProjectInstrumentPhone instrumentPhone) {
        DtoProjectInstrument projectInstrument = new DtoProjectInstrument();
        BeanUtils.copyProperties(instrumentPhone, projectInstrument, "id");
        List<Map<String, String>> projectList = initProjectList(projectInstrument);
        projectInstrument.setProjectList(projectList);
        projectInstrument = projectInstrumentService.save(projectInstrument);
        instrumentPhone.setId(projectInstrument.getId());
        return instrumentPhone;
    }

    private List<Map<String, String>> initProjectList(DtoProjectInstrument projectInstrument) {
        if (StringUtil.isNotEmpty(projectInstrument.getProjectName())) {
            projectInstrument.setProjectName(projectInstrument.getProjectName().replace(";", ","));
        }
        List<Map<String, String>> projectList = new ArrayList<>();
        if (StringUtil.isEmpty(projectInstrument.getProjectId()) || UUIDHelper.GUID_EMPTY.equals(projectInstrument.getProjectId())) {
            //projectId为空，表示项目是手动输入的
            if (StringUtil.isNotEmpty(projectInstrument.getProjectName())) {
                String[] nameList = projectInstrument.getProjectName().split(",");
                for (String name : nameList) {
                    Map<String, String> map = new HashMap<>();
                    map.put("id", "");
                    map.put("projectName", name);
                    projectList.add(map);
                }
            }
        } else {
            //projectId不为空，表示项目是勾选的, 默认id，name一一对应
            List<String> projectIdList = Arrays.asList(projectInstrument.getProjectId().split(";"));
            List<String> nameList = Arrays.asList(projectInstrument.getProjectName().split(","));
            for (int i = 0; i < projectIdList.size(); i++) {
                Map<String, String> map = new HashMap<>();
                map.put("id", projectIdList.get(i));
                map.put("projectName", i < nameList.size() ? nameList.get(i) : "");
                projectList.add(map);
            }
        }
        return projectList;
    }

    /**
     * 修改仪器出入库记录
     *
     * @param instrumentPhone 仪器出入库记录信息
     * @return 出入库记录
     */
    @Transactional
    @Override
    public DtoProjectInstrumentPhone updateInstrumentPhone(DtoProjectInstrumentPhone instrumentPhone) {
        DtoProjectInstrument projectInstrument = new DtoProjectInstrument();
        BeanUtils.copyProperties(instrumentPhone, projectInstrument);
        List<Map<String, String>> projectList = initProjectList(projectInstrument);
        projectInstrument.setProjectList(projectList);
        projectInstrumentService.update(projectInstrument);
        return instrumentPhone;
    }

    /**
     * 添加仪器出库明细
     *
     * @param instrumentId      仪器id
     * @param instrumentPhoneId 仪器出入库记录id
     */
    @Transactional
    @Override
    public void addInstrumentDetails(String instrumentId, String instrumentPhoneId) {
        //优先判断仪器是否可以添加 改为扫码时提醒
//        Integer count = projectInstrumentDetailsRepository.findByInstrumentIdAndIsStorageFalse(instrumentId).size();
//        if (count > 0) {
//            throw new BaseException("当前仪器还未入库，请确认!");
//        }
        //添加仪器出入库明细记录
        DtoProjectInstrumentDetails details = new DtoProjectInstrumentDetails();
        details.setOutQualified(EnumLIM.EnumQualified.合格.getValue());
        details.setOutDate(new Date());
        details.setProjectInstrumentId(instrumentPhoneId);
        details.setOutPerson(PrincipalContextUser.getPrincipal().getUserId());
        details.setOutPersonName(PrincipalContextUser.getPrincipal().getUserName());
        details.setInstrumentIdList(Collections.singletonList(instrumentId));
        projectInstrumentDetailsService.addDetailsBatch(details);
    }

    /**
     * 批量添加仪器出库明细
     *
     * @param instrumentIdList  仪器id列表
     * @param instrumentPhoneId 仪器出入库记录id
     */
    @Transactional
    @Override
    public void batchAddInstrumentDetails(List<String> instrumentIdList, String instrumentPhoneId) {
        //优先判断仪器是否可以添加 改为扫码时提醒
//        Integer count = projectInstrumentDetailsRepository.findByInstrumentIdAndIsStorageFalse(instrumentId).size();
//        if (count > 0) {
//            throw new BaseException("当前仪器还未入库，请确认!");
//        }
        //添加仪器出入库明细记录
        DtoProjectInstrumentDetails details = new DtoProjectInstrumentDetails();
        details.setOutQualified(EnumLIM.EnumQualified.合格.getValue());
        details.setOutDate(new Date());
        details.setProjectInstrumentId(instrumentPhoneId);
        details.setOutPerson(PrincipalContextUser.getPrincipal().getUserId());
        details.setOutPersonName(PrincipalContextUser.getPrincipal().getUserName());
        details.setInstrumentIdList(instrumentIdList);
        projectInstrumentDetailsService.addDetailsBatch(details);
    }

    /**
     * 添加仪器入库明细
     *
     * @param instrumentId      仪器id
     * @param instrumentPhoneId 仪器出入库记录id
     */
    @Override
    @Transactional
    public void addInstrumentDetailsIn(String instrumentId, String instrumentPhoneId) {
        //判断仪器是否出库
        checkInstrumentDetailsOut(instrumentId, instrumentPhoneId);
        DtoProjectInstrumentDetails details = new DtoProjectInstrumentDetails();
        //入库日期
        details.setInDate(new Date());
        //入库人
        details.setInPerson(PrincipalContextUser.getPrincipal().getUserId());
        details.setInPersonName(PrincipalContextUser.getPrincipal().getUserName());
        //入库合格状态
        details.setInQualified(EnumLIM.EnumQualified.合格.getValue());
        //仪器id列表
        details.setInstrumentIdList(Collections.singletonList(instrumentId));
        //设置项目仪器表id
        details.setProjectInstrumentId(instrumentPhoneId);
        projectInstrumentService.instrumentIn(details);
    }

    /**
     * 批量添加仪器入库明细
     *
     * @param instrumentIdList  仪器id列表
     * @param instrumentPhoneId 仪器出入库记录id
     */
    @Override
    @Transactional
    public void addInstrumentDetailsBatchIn(List<String> instrumentIdList, String instrumentPhoneId) {
        //判断仪器是否出库
        batchCheckInstrumentDetailsOut(instrumentIdList, instrumentPhoneId);
        DtoProjectInstrumentDetails details = new DtoProjectInstrumentDetails();
        //入库日期
        details.setInDate(new Date());
        //入库人
        details.setInPerson(PrincipalContextUser.getPrincipal().getUserId());
        details.setInPersonName(PrincipalContextUser.getPrincipal().getUserName());
        //入库合格状态
        details.setInQualified(EnumLIM.EnumQualified.合格.getValue());
        //仪器id列表
        details.setInstrumentIdList(instrumentIdList);
        //设置项目仪器表id
        details.setProjectInstrumentId(instrumentPhoneId);
        projectInstrumentService.instrumentIn(details);
    }

    /**
     * 扫码出库弹窗
     *
     * @param id 仪器id
     * @return 弹窗信息
     */
    @Override
    public DtoInstrumentOutPhone findInstrumentOutPhone(String id) {
        DtoInstrument dtoInstrument = instrumentService.findOne(id);
        DtoInstrumentOutPhone dtoInstrumentPhone = new DtoInstrumentOutPhone();
        if (StringUtil.isNotNull(dtoInstrument)) {
            BeanUtils.copyProperties(dtoInstrument, dtoInstrumentPhone, "originEndDate");
            dtoInstrumentPhone.setOriginEndDate(DateUtil.dateToString(dtoInstrument.getOriginEndDate(), DateUtil.YEAR));
        } else {
            throw new BaseException("扫码仪器不存在，请确认!");
        }
        //优先判断仪器是否可以添加
        int count = projectInstrumentDetailsRepository.findByInstrumentIdAndIsStorageFalse(id).size();
        if (count > 0) {
            throw new BaseException("该仪器已出库，请确认后再操作!");
        }
        return dtoInstrumentPhone;
    }

    /**
     * 扫码入库弹出界面
     *
     * @param instrumentId 仪器id
     * @return 仪器入库扫码实体
     */
    @Override
    public DtoInstrumentInPhone findInstrumentInPhone(String instrumentId) {
        DtoInstrument dtoInstrument = instrumentService.findOne(instrumentId);
        DtoInstrumentInPhone inPhone = new DtoInstrumentInPhone();
        //判断仪器是否出库
        checkInstrumentDetailsOut(instrumentId, UUIDHelper.GUID_EMPTY);
        //设置详情信息
        Optional<DtoProjectInstrumentDetails> detailsOptional = projectInstrumentDetailsRepository.
                findByInstrumentIdAndIsStorageFalse(instrumentId).stream().findFirst();
        if (detailsOptional.isPresent()) {
            DtoPerson dtoPerson = personService.findOne(detailsOptional.get().getOutPerson());
            inPhone.setOutPersonId(detailsOptional.get().getOutPerson())
                    .setOutDate(detailsOptional.get().getOutDate());
            inPhone.setProjectInstrumentId(detailsOptional.get().getProjectInstrumentId());
            if (StringUtil.isNotNull(dtoPerson)) {
                inPhone.setOutPersonName(dtoPerson.getCName());
            }
        }

        //设置仪器信息
        BeanUtils.copyProperties(dtoInstrument, inPhone);
        //设置主表信息
        DtoProjectInstrument dtoProjectInstrument = projectInstrumentService.findOne(inPhone.getProjectInstrumentId());
        BeanUtils.copyProperties(dtoProjectInstrument, inPhone, "id");

        return inPhone;
    }

    /**
     * 仪器出入库记录批量删除
     *
     * @param instrumentPhoneIds 仪器出入库记录ids
     */
    @Override
    @Transactional
    public void deleteInstrumentPhone(List<String> instrumentPhoneIds) {
        Integer count = projectInstrumentDetailsRepository.countByProjectInstrumentIdInAndIsStorageTrue(instrumentPhoneIds);
        if (count > 0) {
            throw new BaseException("所删除的记录中包含已入库的仪器信息");
        }
        projectInstrumentService.deleteByIds(instrumentPhoneIds);
    }

    /**
     * 仪器出入库明细批量删除
     *
     * @param instrumentPhoneDetailIds 仪器出入库明细ids
     */
    @Override
    @Transactional
    public void deleteInstrumentDetailPhone(List<String> instrumentPhoneDetailIds) {
        Integer count = projectInstrumentDetailsRepository.countByIdInAndIsStorageTrue(instrumentPhoneDetailIds);
        if (count > 0) {
            throw new BaseException("所删除的仪器已入库");
        }
        projectInstrumentDetailsService.deleteDetailsBatch(instrumentPhoneDetailIds);
    }

    /**
     * 仪器入库列表分页查询
     *
     * @param pb                             分页条件
     * @param projectInstrumentPhoneCriteria 查询条件
     */
    @Override
    public void findInstrumentInByPage(PageBean<DtoProjectInstrumentDetailPhone> pb, BaseCriteria projectInstrumentPhoneCriteria) {
        pb.setEntityName("DtoProjectInstrument a,DtoProjectInstrumentDetails b");
        pb.setSelect("select new com.sinoyd.lims.api.dto.DtoProjectInstrumentDetailPhone(b.id,b.projectInstrumentId," +
                "b.instrumentId,b.outPerson,b.outDate,b.outQualified,b.inPerson,b.inDate,b.inQualified,a.projectName,a.userNames,b.isStorage) ");
        comRepository.findByPage(pb, projectInstrumentPhoneCriteria);
        List<DtoProjectInstrumentDetailPhone> pbData = pb.getData();
        fillInstrumentIn(pbData);
    }

    /**
     * 查询项目列表
     *
     * @param pb              分页查询
     * @param projectCriteria 查询条件
     * @return 项目列表
     */
    @Override
    public List<DtoProjectPhone> findProjectByPage(PageBean<DtoProject> pb, BaseCriteria projectCriteria) {
        projectService.findByPage(pb, projectCriteria);
        List<DtoProjectPhone> projectPhoneList = new ArrayList<>();
        pb.getData().forEach(p -> {
            DtoProjectPhone phone = new DtoProjectPhone();
            BeanUtils.copyProperties(p, phone);
            projectPhoneList.add(phone);
        });
        return projectPhoneList;
    }

    /**
     * 查询仪器列表
     *
     * @param pb                 分页查询
     * @param instrumentCriteria 查询条件
     * @return 仪器列表
     */
    @Override
    public List<DtoInstrument> findInstrumentByPage(PageBean<DtoInstrument> pb, BaseCriteria instrumentCriteria) {
        instrumentService.findByPage(pb, instrumentCriteria);
        return pb.getData();
    }

    /**
     * 查询仪器使用记录
     *
     * @param page     分页查询
     * @param criteria 查询条件
     */
    @Override
    public void findInstrumentRecordByPage(PageBean<DtoEnvironmentalRecord> page, BaseCriteria criteria) {
        EnvironmentalRecordCriteria recordCriteria = (EnvironmentalRecordCriteria) criteria;
        //当objectId是空的时候，返回值为空
        if (StringUtil.isNotEmpty(recordCriteria.getObjectId()) && !UUIDHelper.GUID_EMPTY.equals(recordCriteria.getObjectId())) {
            environmentalRecordService.findByPage(page, criteria);
        }
    }

    @Override
    public Boolean findReceiveStatus(DtoEnvironmentalRecord environmentalRecord) {
        boolean status = false;
        String objectId = environmentalRecord.getObjectId();
        if (StringUtil.isNotEmpty(objectId)){
            DtoReceiveSampleRecord receiveSampleRecord = null;
            DtoReceiveSubSampleRecord subSampleRecord;
            if (environmentalRecord.getObjectType().equals(EnumLIM.EnumEnvRecObjType.采样.getValue())) {
                receiveSampleRecord = receiveSampleRecordRepository.findOne(objectId);
            } else if (environmentalRecord.getObjectType().equals(EnumLIM.EnumEnvRecObjType.现场分析.getValue())) {
                subSampleRecord = receiveSubSampleRecordRepository.findOne(objectId);
                receiveSampleRecord = StringUtil.isNotNull(subSampleRecord) ? receiveSampleRecordRepository.findOne(subSampleRecord.getReceiveId()) : null;
            }
            if (receiveSampleRecord != null){
                status = receiveSampleRecord.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.信息登记中.getValue());
            }
        }
        return status;
    }

    /**
     * 填充仪器入库分页列表查询
     *
     * @param list 分页数据
     */
    private void fillInstrumentIn(List<DtoProjectInstrumentDetailPhone> list) {
        if (StringUtil.isNotEmpty(list)) {
            List<DtoPerson> dtoPersonList = personService.findAll();
            Map<String, DtoPerson> dtoPersonMap = dtoPersonList.stream().collect(Collectors.toMap(DtoPerson::getId, data -> data));
            List<String> instrumentIds = list.stream().map(DtoProjectInstrumentDetailPhone::getInstrumentId).collect(Collectors.toList());
            List<DtoInstrument> instrumentAll = instrumentService.findAll(instrumentIds);
            Map<String, DtoInstrument> instrumentMap = null;
            if (StringUtil.isNotEmpty(instrumentAll)) {
                instrumentMap = instrumentAll.stream().collect(Collectors.toMap(DtoInstrument::getId, data -> data));
            }
            for (DtoProjectInstrumentDetailPhone dto : list) {
                //填充入库人名称
                DtoPerson inPerson = dtoPersonMap.get(dto.getInPerson());
                if (StringUtil.isNotNull(inPerson)) {
                    dto.setInPersonName(inPerson.getCName());
                }
                //填充出库人名称
                DtoPerson outPerson = dtoPersonMap.get(dto.getOutPerson());
                if (StringUtil.isNotNull(outPerson)) {
                    dto.setOutPersonName(outPerson.getCName());
                }
                //填充仪器记录
                if (StringUtil.isNotNull(instrumentMap)) {
                    DtoInstrument dtoInstrument = instrumentMap.get(dto.getInstrumentId());
                    if (StringUtil.isNotNull(dtoInstrument)) {
                        dto.setInstrumentCode(dtoInstrument.getInstrumentsCode());
                        dto.setInstrumentName(dtoInstrument.getInstrumentName());
                        dto.setModel(dtoInstrument.getModel());
                    }
                }
            }
        }
    }

    /**
     * 填充出入库列表信息
     *
     * @param datas 出入库列表记录
     * @return 出入库列表信息
     */
    protected List<DtoProjectInstrumentPhone> fillInstrumentPhoneMessage(List<DtoProjectInstrument> datas) {
        List<DtoProjectInstrumentPhone> instrumentPhoneList = new ArrayList<>();
        List<String> proInstIds = datas.stream().map(DtoProjectInstrument::getId).collect(Collectors.toList());
        List<DtoProjectInstrumentDetails> detailsList = projectInstrumentDetailsRepository.findByProjectInstrumentIdIn(proInstIds);
        datas.forEach(p -> {
            //当前出入库对应的仪器详情
            List<DtoProjectInstrumentDetails> proInstrDetailsList = detailsList.stream()
                    .filter(d -> p.getId().equals(d.getProjectInstrumentId())).collect(Collectors.toList());
            if (proInstrDetailsList.size() > 0) {
                //出库总数
                p.setOutCount(proInstrDetailsList.size());
                List<DtoProjectInstrumentDetails> innerInstrumentList = proInstrDetailsList.stream()
                        .filter(ProjectInstrumentDetails::getIsStorage).collect(Collectors.toList());
                //入库数
                p.setInCount(innerInstrumentList.size());
            }
            DtoProjectInstrumentPhone instrumentPhone = new DtoProjectInstrumentPhone();
            BeanUtils.copyProperties(p, instrumentPhone);
            instrumentPhoneList.add(instrumentPhone);
        });
        return instrumentPhoneList;
    }

    /**
     * 判断仪器是否有出库记录
     *
     * @param instrumentId      仪器id
     * @param instrumentPhoneId 项项目仪器表id
     */
    private void checkInstrumentDetailsOut(String instrumentId, String instrumentPhoneId) {
        List<DtoProjectInstrumentDetails> dtoProjectInstrumentDetails = new ArrayList<>();
        if (!UUIDHelper.GUID_EMPTY.equals(instrumentPhoneId)) {
            dtoProjectInstrumentDetails = projectInstrumentDetailsRepository.
                    findByInstrumentIdAndProjectInstrumentIdAndIsStorageFalse(instrumentId, instrumentPhoneId);
        } else {
            dtoProjectInstrumentDetails = projectInstrumentDetailsRepository.findByInstrumentIdAndIsStorageFalse(instrumentId);
        }
        if (StringUtil.isEmpty(dtoProjectInstrumentDetails)) {
            throw new BaseException("所入库的仪器没有出库记录");
        }
    }

    /**
     * 批量判断仪器是否有出库记录
     *
     * @param instrumentIdList  仪器id
     * @param instrumentPhoneId 项项目仪器表id
     */
    private void batchCheckInstrumentDetailsOut(List<String> instrumentIdList, String instrumentPhoneId) {
        List<DtoProjectInstrumentDetails> detailsList;
        if (StringUtil.isNotEmpty(instrumentPhoneId) && !UUIDHelper.GUID_EMPTY.equals(instrumentPhoneId)) {
            detailsList = projectInstrumentDetailsRepository.findByProjectInstrumentId(instrumentPhoneId);
            detailsList = detailsList.stream().filter(p -> !p.getIsStorage() && instrumentIdList.contains(p.getInstrumentId())).collect(Collectors.toList());
        } else {
            detailsList = projectInstrumentDetailsRepository.findByInstrumentIdInAndIsStorageFalse(instrumentIdList);
        }
        List<String> instrumentIdWithDetails = detailsList.stream().map(DtoProjectInstrumentDetails::getInstrumentId).distinct().collect(Collectors.toList());
        if (instrumentIdWithDetails.size() < instrumentIdList.size()) {
            throw new BaseException("所入库的仪器没有出库记录");
        }
    }

    @Autowired
    public void setProjectInstrumentDetailsRepository(ProjectInstrumentDetailsRepository projectInstrumentDetailsRepository) {
        this.projectInstrumentDetailsRepository = projectInstrumentDetailsRepository;
    }

    @Autowired
    @Lazy
    public void setProjectInstrumentQueryService(ProjectInstrumentQueryService projectInstrumentQueryService) {
        this.projectInstrumentQueryService = projectInstrumentQueryService;
    }

    @Autowired
    @Lazy
    public void setProjectInstrumentService(ProjectInstrumentService projectInstrumentService) {
        this.projectInstrumentService = projectInstrumentService;
    }

    @Autowired
    @Lazy
    public void setProjectInstrumentDetailsService(ProjectInstrumentDetailsService projectInstrumentDetailsService) {
        this.projectInstrumentDetailsService = projectInstrumentDetailsService;
    }

    @Autowired
    @Lazy
    public void setInstrumentService(InstrumentService instrumentService) {
        this.instrumentService = instrumentService;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    @Lazy
    public void setEnvironmentalRecordService(EnvironmentalRecordService environmentalRecordService) {
        this.environmentalRecordService = environmentalRecordService;
    }

    @Autowired
    @Lazy
    public void setReceiveSampleRecordRepository(ReceiveSampleRecordRepository receiveSampleRecordRepository) {
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
    }

    @Autowired
    @Lazy
    public void setReceiveSubSampleRecordRepository(ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository) {
        this.receiveSubSampleRecordRepository = receiveSubSampleRecordRepository;
    }
}
