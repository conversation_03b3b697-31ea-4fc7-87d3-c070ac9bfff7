package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;

/**
 * 数据评价操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
public interface DataEvaluateService {
    /**
     * 获取数据上实际的等级
     *
     * @param evaluationId  评价标准id
     * @param parentId      条件项id
     * @param analyzeItemId 因子id
     * @param testValue     待评价值
     * @return 返回是否合格
     */
    DtoEvaluationLevel getRealLevel(String evaluationId, String parentId, String analyzeItemId, String testValue);

    /**
     * 判断是否合格
     *
     * @param evaValue  评价值
     * @param testValue 待评价值
     * @return 返回是否合格
     */
    Boolean getIsPass(DtoEvaluationValue evaValue, String testValue);

    /**
     * 获取超标倍数
     *
     * @param evaValue  评价值
     * @param testValue 待评价值
     * @return 返回超标倍数
     */
    String getOverProofTimes(DtoEvaluationValue evaValue, String testValue);
}