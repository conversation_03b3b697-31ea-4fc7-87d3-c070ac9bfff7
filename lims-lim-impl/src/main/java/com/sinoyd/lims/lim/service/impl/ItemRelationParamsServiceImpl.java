package com.sinoyd.lims.lim.service.impl;


import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelation;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;
import com.sinoyd.lims.lim.repository.rcc.ItemRelationParamsRepository;
import com.sinoyd.lims.lim.service.ItemRelationParamsService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * ItemRelationParams操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
 @Service
public class ItemRelationParamsServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoItemRelationParams,String, ItemRelationParamsRepository> implements ItemRelationParamsService {

     private AnalyzeItemRepository analyzeItemRepository;

    @Override
    public void findByPage(PageBean<DtoItemRelationParams> pb, BaseCriteria itemRelationParamsCriteria) {
        pb.setEntityName("DtoItemRelationParams a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, itemRelationParamsCriteria);
    }

    /**
     * 根据分析项目关系ids查询分析项目
     *
     * @param ids 分析项目关系ids
     * @return 分析项目关系详情
     */
    @Override
    public List<DtoItemRelationParams> findByRelationIds(Collection<?> ids) {
        return repository.findByRelationIdIn(ids);
    }

    /**
     * 根据分析项目获取关系详情
     * @param ids 分析项目ids
     * @return 分析项目详情
     */
    @Override
    public List<DtoItemRelationParams> findByItemIds(Collection<?> ids){
        return repository.findByAnalyzeItemIdIn(ids);
    }

    @Transactional
    @Override
    public void create(String relationId, List<String> itemIds) {
        List<DtoItemRelationParams> paramsList = repository.findByRelationIdIn(Collections.singleton(relationId));
        List<DtoItemRelationParams> newList = new ArrayList<>();
        List<DtoAnalyzeItem> analyzeItemList = analyzeItemRepository.findAll(itemIds);
        itemIds.forEach(itemId -> {
            Optional<DtoItemRelationParams> paramsOptional = paramsList.stream()
                    .filter(p -> itemId.equals(p.getAnalyzeItemId())).findFirst();
            if (!paramsOptional.isPresent()) {
                Optional<DtoAnalyzeItem> analyzeItem = analyzeItemList.stream().filter(p->itemId.equals(p.getId())).findFirst();
                if(analyzeItem.isPresent()) {
                    DtoItemRelationParams params = new DtoItemRelationParams();
                    params.setRelationId(relationId);
                    params.setAnalyzeItemId(itemId);
                    params.setAnalyzeItemName(analyzeItem.get().getAnalyzeItemName());
                    params.setOrderNum(0);
                    params.setPosition(-1);
                    newList.add(params);
                }
            }
        });
        repository.save(newList);
    }

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }
}