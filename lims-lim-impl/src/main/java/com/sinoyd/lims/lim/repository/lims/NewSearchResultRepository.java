package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchPlan;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;

import java.util.List;

/**
 * 查新计划仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
public interface NewSearchResultRepository extends IBaseJpaPhysicalDeleteRepository<DtoNewSearchResult, String> {

    /**
     * 根据任务id查询结果
     *
     * @param taskId 任务id
     * @return 查新结果
     */
    List<DtoNewSearchResult> findByTaskId(String taskId);
}
