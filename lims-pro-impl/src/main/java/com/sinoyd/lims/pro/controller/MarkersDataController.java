package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.customer.DtoMarkersData;
import com.sinoyd.lims.pro.service.MarkersDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据标记服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/1
 * @since V100R001
 */
@Api(tags = "示例: 数据标记")
@RestController
@RequestMapping("api/pro/marker")
public class MarkersDataController extends ExceptionHandlerController<MarkersDataService> {

    @ApiOperation(value = "修改验证状态和次数", notes = "修改验证状态和次数")
    @PutMapping("/validate")
    public RestResponse<String> updateValidate(@RequestBody DtoMarkersData dtoMarkersData) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.updateValidate(dtoMarkersData);
        return restResponse;
    }

}
