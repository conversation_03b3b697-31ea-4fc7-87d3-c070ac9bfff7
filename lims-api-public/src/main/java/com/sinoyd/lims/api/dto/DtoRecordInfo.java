package com.sinoyd.lims.api.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DtoRecordInfo {

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 送样单ids
     */
    private List<String> receiveIds;

    /**
     * 点位信息
     */
    private  List<String> folderInfo;

    /**
     * 采样人员ids
     */
    private  List<String> samplingPersonIds;

    /**
     * 采样负责人id
     */
    private  String samplingLeaderId;

    /**
     * 采样负责人名称
     */
    private String samplingLeaderName;

    /**
     * 采样时间
     */
    private Date samplingTime;
}
