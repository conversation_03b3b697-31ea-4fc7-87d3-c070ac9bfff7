package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoReportApply;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.repository.lims.ReportApplyRepository;
import com.sinoyd.lims.lim.repository.lims.ReportConfigRepository;
import com.sinoyd.lims.lim.service.ReportConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * ReportConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/10/13
 * @since V100R001
 */
 @Service
public class ReportConfigServiceImpl extends BaseJpaServiceImpl<DtoReportConfig,String, ReportConfigRepository> implements ReportConfigService {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    private ReportApplyRepository reportApplyRepository;

    /**
     * 查询报表模板配置
     *
     * @param pb                   分页条件
     * @param reportConfigCriteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoReportConfig> pb, BaseCriteria reportConfigCriteria) {
        pb.setEntityName("DtoReportConfig a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, reportConfigCriteria);
        List<DtoReportConfig> configList = pb.getData();
        if (StringUtil.isNotEmpty(configList)) {
            List<String> reportConfigIds = configList.stream().map(DtoReportConfig::getId).collect(Collectors.toList());
            Map<String, List<DtoDocument>> templateDocMap = documentService.findByObjectIds(reportConfigIds).stream().collect(Collectors.groupingBy(DtoDocument::getFolderId));
            for (DtoReportConfig config : configList) {
                config.setReportNameList(StringUtil.isNotEmpty(config.getReportName())
                        ? Arrays.asList(config.getReportName().split(";")) : new ArrayList<>());

                List<DtoDocument> templateList = templateDocMap.get(config.getId());
                if (StringUtil.isNotEmpty(templateList)) {
                    templateList.sort(Comparator.comparing(DtoDocument::getCreateDate, Comparator.reverseOrder()));
                    config.setTemplateDocId(templateList.get(0).getId());
                }
            }
        }
    }

    /**
     * 新增报表模板配置
     *
     * @param entity 报表配置实体
     * @return 返回保存的数据
     */
    @Override
    public DtoReportConfig save(DtoReportConfig entity) {
        // 报表编码唯一性校验
        List<DtoReportConfig> dtoReportConfigs = repository.findByReportCodeAndIdNot(entity.getReportCode(), entity.getId());
        if (dtoReportConfigs.size() > 0) {
            throw new BaseException("报表编码重复");
        }
        //报表名称数组格式化为以“;”分隔的字符串
        entity.setReportName(formatReportName(entity.getReportNameList()));
        return super.save(entity);
    }

    /**
     * 修改报表模板配置
     *
     * @param entity 报表配置实体
     * @return 返回数据
     */
    @Transactional
    @Override
    public DtoReportConfig update(DtoReportConfig entity) {
        // 报表编码唯一性校验
        List<DtoReportConfig> dtoReportConfigs = repository.findByReportCodeAndIdNot(entity.getReportCode(), entity.getId());
        if (dtoReportConfigs.size() > 0) {
            throw new BaseException("报表编码重复");
        }
        //报表名称数组格式化为以“;”分隔的字符串
        entity.setReportName(formatReportName(entity.getReportNameList()));
        return super.update(entity);
    }

    @Override
    public DtoReportConfig findByCode(String code) {
        List<DtoReportConfig> dtoReportConfigs = repository.findByReportCode(code);
        if (StringUtil.isNotNull(dtoReportConfigs) && dtoReportConfigs.size() > 0) {
            return dtoReportConfigs.get(0);
        }
        return null;
    }

    @Override
    public String download(String configId, HttpServletResponse response) throws IOException {
        Optional<DtoDocument> document = documentService.findByObjectId(configId).stream()
                .max(Comparator.comparing(DtoDocument::getCreateDate));
        String retStr = "未上传报表模板";
        if (document.isPresent()) {
            retStr = documentService.download(document.get().getId(), response);
        }
        return retStr;
    }

    @Override
    @Transactional
    public String downloadReport(String configId, HttpServletResponse response) throws IOException {
        DtoReportConfig config = repository.findOne(configId);
        if (StringUtil.isNotEmpty(config.getTemplate())) {
            return this.downLoadWithConfig(configId, response);
        } else {
            Optional<DtoDocument> document = documentService.findByObjectId(configId).stream()
                    .max(Comparator.comparing(DtoDocument::getCreateDate));
            if (document.isPresent()) {
                return documentService.downloadReport(document.get(), response);
            } else {
                return "请上传模板或者添加模板文件路径";
            }
        }
    }

    @Override
    public String getDocumentAttachPath(String configId) {
        String typeCode = "";
        DtoReportConfig reportConfig = super.findOne(configId);
        if (StringUtil.isNotNull(reportConfig)) {
            typeCode = reportConfig.getTypeCode();
        }
        return typeCode;
    }

    @Override
    public DtoReportConfig findOne(String key) {
        DtoReportConfig reportConfig = super.findOne(key);
        if (StringUtil.isNotNull(reportConfig)) {
            reportConfig.setReportNameList(StringUtil.isNotEmpty(reportConfig.getReportName())
                    ? Arrays.asList(reportConfig.getReportName().split(";")) : new ArrayList<>());
        }
        String fileName = "";
        if (StringUtil.isNotNull(reportConfig.getOutputName())) {
            fileName = reportConfig.getOutputName().substring(reportConfig.getOutputName().lastIndexOf("/") + 1);
        }
        reportConfig.setFileName(fileName);

        return reportConfig;
    }

    /**
     * 获取上传模板路径
     *
     * @param id   配置id
     * @param path 路径
     * @return 上传模板路径
     */
    @Override
    public String getTempPath(String id, String path) {
        Optional<DtoDocument> document = documentService.findByObjectId(id).stream().filter(p -> !p.getIsDeleted())
                .max(Comparator.comparing(DtoDocument::getCreateDate));
        String temStr = String.format("%s%s", "/", path);
        if (document.isPresent()) {
            temStr = document.get().getPath();
        }
        return temStr;
    }

    @Override
    @Transactional
    public DtoReportConfig copyReportConfig(String reportConfigId, String code) {
        DtoReportConfig source = repository.findOne(reportConfigId);
        DtoReportConfig target = new DtoReportConfig();
        if (source != null) {
            BeanUtils.copyProperties(source, target, "id");
            //其中报表编码和下载路径获取文本框填写内容
            target.setReportCode(code);
            target.setStrUrl(code);
            save(target);
            //复制报表应用
            List<DtoReportApply> saveList = new ArrayList<>();
            List<DtoReportApply> reportApplyList = reportApplyRepository.findByReportConfigIdIn(Collections.singleton(reportConfigId));
            for (DtoReportApply reportApply : reportApplyList) {
                DtoReportApply copyReportApply = new DtoReportApply();
                BeanUtils.copyProperties(reportApply, copyReportApply, "id");
                copyReportApply.setReportConfigId(target.getId());
                saveList.add(copyReportApply);
            }
            reportApplyRepository.save(saveList);
        }
        return target;
    }

    /**
     * 根据配置中的模板路径下载模板
     *
     * @param configId 配置id
     * @param response 响应体
     * @return 操作结果
     */
    private String downLoadWithConfig(String configId, HttpServletResponse response) throws IOException {
        String resultStr = "未找到报表模板配置信息";
        DtoReportConfig config = repository.findOne(configId);
        if (StringUtil.isNotNull(config)) {
            String path = filePathConfig.getTemplatePath() + "/" + config.getTemplate();
            resultStr = documentService.fileDownload(path, config.getTemplateName(), response);
        }
        return resultStr;
    }

    /**
     * 格式化报表名称
     *
     * @param reportNameList 报表名称列表
     * @return 返回数据
     */
    private String formatReportName(List<String> reportNameList) {
        StringBuilder reportName = new StringBuilder();
        if (StringUtil.isNotEmpty(reportNameList)) {
            for (String name : reportNameList) {
                reportName.append(name);
                reportName.append(";");
            }
            reportName = new StringBuilder(reportName.substring(0, reportName.length() - 1));
        }
        return reportName.toString();
    }
}