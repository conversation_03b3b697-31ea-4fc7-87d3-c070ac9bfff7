package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

import java.util.Date;

/**
 * 离线数据录入（离线数据表）：采样记录单实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineSampleRecordVO {

    /**
     * 离线数据主键ID
     */
    private String id;

    /**
     * 送样单编号
     */
    private String recordCode;

    /**
     * 采样人员姓名
     */
    private String samplingPersonNames;

    /**
     * 采样时间
     */
    private String samplingTime;

    /**
     * 数据上传时间
     */
    private Date pushTime;

    /**
     * 采样单号
     */
    private String receiveSampleRecordCode;

    /**
     * 采样负责人
     */
    private String leaderName;

    /**
     * 样品类型名称
     */
    private String sampleTypeNames;

    /**
     * 点位名称（多个用逗号分隔）
     */
    private String folderName;

    /**
     * 点位数量
     */
    private String folderCount;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 受检单位名称
     */
    private String inspectedEnt;

    /**
     * 受检单位地址
     */
    private String inspectedAddress;

    /**
     * 所属机构
     */
    private String orgId;
}
