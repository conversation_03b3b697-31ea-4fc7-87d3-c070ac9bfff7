package com.sinoyd.lims.pro.vo;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import lombok.Data;

import java.util.Date;

/**
 * 监测点位分析数据VO
 *
 * <AUTHOR>
 * @version V5.2.0 2025/4/14
 * @since V100R001
 */
@Data
public class FixedPointStatAnaDataVO {

    /**
     * id(分析数据id)
     */
    private String id;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;

    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 监测点位id
     */
    private String fixedPointId;

    /**
     * 分析时间
     */
    private Date analyzeTime;

    /**
     * 分析日期（天）
     */
    private String analyzeTimeDay;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 检测结果未修约值
     */
    private String testOrignValue;

    /**
     * 监测结果已修约值
     */
    private String testValueDstr;

    /**
     * 有效位数
     */
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    private Integer mostDecimal;

    /**
     * 检出限
     */
    private String examLimitValue;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 无参构造函数
     */
    public FixedPointStatAnaDataVO() {
    }

    /**
     * 构造方法
     *
     * @param analyseData 分析数据
     */
    public FixedPointStatAnaDataVO(DtoAnalyseData analyseData) {
        this.id = analyseData.getId();
        this.testId = analyseData.getTestId();
        this.redAnalyzeItemName = analyseData.getRedAnalyzeItemName();
        this.redAnalyzeMethodName = analyseData.getRedAnalyzeMethodName();
        this.sampleId = analyseData.getSampleId();
        this.sampleFolderId = analyseData.getSampleFolderId();
        this.analyzeTime = analyseData.getAnalyzeTime();
        this.testValue = analyseData.getTestValue();
        this.testOrignValue = analyseData.getTestOrignValue();
        this.testValueDstr = analyseData.getTestValueDstr();
        this.examLimitValue = analyseData.getExamLimitValue();
        this.mostSignificance = analyseData.getMostSignificance();
        this.mostDecimal = analyseData.getMostDecimal();
        this.dimensionId = analyseData.getDimensionId();
        this.dimensionId = analyseData.getDimension();
        this.analyzeTimeDay = analyseData.getAnalyzeTime() != null ? DateUtil.dateToString(analyseData.getAnalyzeTime(), DateUtil.YEAR) : "";
    }
}
