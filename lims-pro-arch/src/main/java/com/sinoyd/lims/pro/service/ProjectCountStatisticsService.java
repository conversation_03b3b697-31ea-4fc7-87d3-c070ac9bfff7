package com.sinoyd.lims.pro.service;


import com.sinoyd.lims.pro.dto.customer.DtoProjectSampleCount;

import java.util.List;
import java.util.Map;

public interface ProjectCountStatisticsService {

    /**
     * 查询相应的统计数据(项目数量)
     *
     * @param type      0 登记时间 1采样时间  EnumStatisticsTimeType
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    Map<String,Object> findProjectCountList(Integer type, String startTime, String endTime);


    /**
     * 查询相应的统计的样品数据数据数
     * @param type 0 登记时间 1采样时间  EnumStatisticsTimeType
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param projectTypeId 项目类型id
     * @return 返回相应的数据源
     */
    List<DtoProjectSampleCount> findProjectSampleCountList(Integer type, String startTime, String endTime, String projectTypeId);

    /**
     * 根据年份查询每月监测数量
     *
     * @param year 年份
     * @return 每月监测数量
     */
    Map<String, Object> findCountByYear(Integer year);
}
