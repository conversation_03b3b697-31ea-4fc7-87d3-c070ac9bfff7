package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.service.ProAsyncService;
import com.sinoyd.lims.pro.service.ProService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 检测业务操作接口(异步的调用)
 * <AUTHOR>
 * @version V1.0.0 2020/07/15
 * @since V100R001
 */
@Service
public class ProAsyncServiceImpl implements ProAsyncService {

    @Autowired
    @Lazy
    private ProService proService;

    @Async
    @Override
    public void checkSample(List<DtoSample> samples, DtoProject project,
                            CurrentPrincipalUser principalContextUser) {
        //重新赋值登录的账号信息
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(principalContextUser, null, authorities));
        proService.checkSample(samples, project);
    }
}
