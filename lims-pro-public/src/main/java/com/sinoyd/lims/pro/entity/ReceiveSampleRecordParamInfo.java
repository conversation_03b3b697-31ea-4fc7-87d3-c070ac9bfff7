package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ReceiveSampleRecordParam实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/19
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ReceiveSampleRecordParam")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ReceiveSampleRecordParamInfo implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ReceiveSampleRecordParamInfo() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 送样单模板id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("送样单模板id")
    private String templateId;

    /**
     * 参数配置id/测试项目id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("参数配置id/测试项目id")
    private String paramId;

    /**
     * 参数名称
     */
    @Column
    @ApiModelProperty("参数名称")
    private String paramName;

    /**
     * 排序值
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;

    /**
     * 对象类型（枚举EnumReceiveSampleRecordParamType：1.样品参数，2.点位参数，3.分析项目
     */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("对象类型（枚举EnumReceiveSampleRecordParamType：1.样品参数，2.点位参数，3.分析项目")
    private Integer type;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}