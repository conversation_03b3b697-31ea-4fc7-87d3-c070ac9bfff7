package com.sinoyd.base.dto.customer;

import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 批处理评价传输实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022年2月22日
 * @since V100R001
 */
@Data
public class DtoEvaluationBatchProcess implements Serializable {

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 点位id列表
     */
    private List<String> sampleFolderIdList;

    private Integer objectType;

    private Integer folderPlan;

    /**
     * 评价标准id
     */
    private String evaluationId;

    /**
     * 评价等级id
     */
    private String evaluationLevelId;

}
