package com.sinoyd.lims.lim.service.statistics;

import com.sinoyd.lims.lim.constants.LimConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 上岗证过期统计业务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/22
 */
@Service
@Slf4j
public class JobCertOverdueStatisticsServiceImpl extends AbsResourceStatisticsServiceImpl {

    @Override
    public String getStatisticsSql() {
        StringBuilder sql = new StringBuilder();
//        sql.append("select count(1) as 'num' from (")
//                .append("select id from tb_lim_personability where DATE_FORMAT( certEffectiveTime, '%Y-%m-%d' ) != '1753-01-01' and  DATE_FORMAT(certEffectiveTime, '%Y-%m-%d') < DATE_FORMAT(now(), '%Y-%m-%d') and EXISTS (select 1 from TB_LIM_Person p where p.id = personId and p.isDeleted = 0)")
//                .append("union ")
//                .append("select id as personCertId  from TB_LIM_PersonCert where DATE_FORMAT( certEffectiveTime, '%Y-%m-%d' ) != '1753-01-01' and  DATE_FORMAT(certEffectiveTime, '%Y-%m-%d') < DATE_FORMAT(now(), '%Y-%m-%d') and EXISTS (select 1 from TB_LIM_Person p where p.id = personId and p.isDeleted = 0)")
//                .append(") a");

        sql.append("SELECT count(1) as num from (SELECT 1 FROM tb_lim_personability")
                .append(" WHERE DATE_FORMAT( certEffectiveTime, '%Y-%m-%d' ) != '1753-01-01' ")
                .append(" AND DATE_FORMAT( certEffectiveTime, '%Y-%m-%d' ) < DATE_FORMAT( now(), '%Y-%m-%d' ) ")
                .append(" AND EXISTS ( SELECT 1 FROM TB_LIM_Person p WHERE p.id = personId AND p.isDeleted = 0 )")
                .append(" and EXISTS (select 1 from TB_LIM_PERSONCERT pt where personCertId = pt.id)")
                .append(" and EXISTS (select 1 from tb_lim_test t where testId = t.id and t.isDeleted = 0)")
                .append(" GROUP BY personId,personCertId ,certEffectiveTime ) as temp");
        return sql.toString();
    }

    @Override
    public String getStatisticsItemName() {
        return LimConstants.StatisticsItemName.JOB_CERT_OVERDUE;
    }

    @Override
    public String getUnit() {
        return "张";
    }
}