package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * CertHistoryFile实体
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/25
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description="CarManage")
@Data
@EntityListeners(AuditingEntityListener.class)
public class CertHistoryFile extends LimsBaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 原附件标识
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String referenceId;
}
