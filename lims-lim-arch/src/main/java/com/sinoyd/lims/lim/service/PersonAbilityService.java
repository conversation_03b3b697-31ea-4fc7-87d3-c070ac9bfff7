package com.sinoyd.lims.lim.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;

/**
 * 检测能力管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface PersonAbilityService extends IBaseJpaService<DtoPersonAbility, String> {
    /**
     * 查询对应测试项目的人员检测能力
     *
     * @param testId 测试项目id
     * @return 检测能力
     */
    List<DtoPersonAbility> findPersonCertByTestId(String testId);

    /**
     * 测试获取相关的人员检测能力
     *
     * @param testIds 测试项目ids
     * @return 返回相应的检测能力
     */
    List<DtoPersonAbility> findByTestIds(Collection<String> testIds);

    /**
     * 新增多条检测能力
     *
     * @param personAbilities 人员检测能力实体
     * @return 返回相应的检测能力
     */
    String saveList(List<DtoPersonAbility> personAbilities);

    /**
     * 批量更新日期
     *
     * @param map               修改参数
     */
    void bachUpdateDate(Map<String,Object> map);

    /**
     * 检测能力权限校验
     *
     * @param params 参数
     * @return 检测结果
     */
    String check(Map<String, Object> params);
}