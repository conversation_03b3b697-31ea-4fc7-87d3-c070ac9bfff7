package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.Training2Participants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoTraining2Participants实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_Training2Participants")
@Data
@DynamicInsert
public class DtoTraining2Participants extends Training2Participants {


}
