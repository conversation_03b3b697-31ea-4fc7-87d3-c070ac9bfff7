package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.Project2Customer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * Project2Customer实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/10/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_Project2Customer")
@Data
@DynamicInsert
public class DtoProject2Customer extends Project2Customer {

}