package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.lims.pro.service.SignatureService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;

public class BackReportRemoveDocListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {
        String businessKey = delegateExecution.getProcessBusinessKey();
        //签名
        SignatureService signatureService = SpringContextAware.getBean(SignatureService.class);
        try {
            signatureService.deleteDoc(businessKey);
        } catch (Exception ex) {
            throw new Exception(ex.getMessage());
        }
    }

}
