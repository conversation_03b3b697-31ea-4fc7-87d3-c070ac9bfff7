package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 查新计划查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NewSearchPlanCriteria extends BaseCriteria implements Serializable {

    /**
     * 关键字（任务名称）
     */
    private String key;

    /**
     * 任务状态
     */
    private Integer status = EnumLIM.EnumPlanStatus.所有.getValue();


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and p.planName like :key");
            values.put("key", "%" + key + "%");
        }

        if (!EnumLIM.EnumPlanStatus.所有.getValue().equals(status)) {
            condition.append(" and p.status = :status");
            values.put("status", status);
        } else {
            condition.append(" and p.status in :status");
            List<Integer> statues = new ArrayList<>();
            statues.add(EnumLIM.EnumPlanStatus.启用.getValue());
            statues.add(EnumLIM.EnumPlanStatus.新建.getValue());
            statues.add(EnumLIM.EnumPlanStatus.停用.getValue());
            values.put("status", statues);
        }
        return condition.toString();
    }

}
