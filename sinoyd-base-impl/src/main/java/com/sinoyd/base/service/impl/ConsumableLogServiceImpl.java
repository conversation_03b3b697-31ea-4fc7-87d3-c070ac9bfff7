package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.customer.DtoImportConsumableDetail;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.dto.lims.DtoConsumableLog;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.ConsumableLogRepository;
import com.sinoyd.base.service.ConsumableDetailService;
import com.sinoyd.base.service.ConsumableLogService;
import com.sinoyd.base.service.ConsumableService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 消耗领用记录
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
public class ConsumableLogServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoConsumableLog, String, ConsumableLogRepository> implements ConsumableLogService {

    @Autowired
    @Lazy
    private ConsumableService consumableService;

    @Autowired
    @Lazy
    private ConsumableDetailService consumableDetailService;

    @Autowired
    private CommonRepository commonRepository;

    /**
     * 分页查询领用记录
     */
    @Override
    public void findByPage(PageBean<DtoConsumableLog> page, BaseCriteria criteria) {
        page.setEntityName("DtoConsumableLog p");
        page.setSelect("select p");
        super.findByPage(page, criteria);
        loadFields(page.getData());
    }

    /**
     * 领用记录新增
     */
    @Transactional
    @Override
    public DtoConsumableLog save(DtoConsumableLog entity) {
        // TODO: 当消耗品有两个批次，每个批次10库存，领料时领用20个，会提示库存不足    暂时不做修改,继续使用以前的
        //找到详细信息
//        DtoConsumableDetail consumableDetail = consumableDetailService.findOne(entity.getConsumableDetailId());
        // 如果本批次数量大于领用数量,则领用本批次的,否则查询消耗品总数是否足够
//        if (consumableDetail.getStorage().compareTo(entity.getAmount()) > -1) {
        //修改详细信息的库存
//            consumableDetail.setStorage(consumableDetail.getStorage().subtract(entity.getAmount()));
//            consumableDetailService.update(consumableDetail);
//            DtoConsumable consumable = consumableService.findOne(consumableDetail.getParentId());
        //结存
//            entity.setBalance(consumable.getInventory());
        // 保存领用日志
//            return super.save(entity);
//        } else {
//            DtoConsumable consumable = consumableService.findOne(consumableDetail.getParentId());
        // 如果库存满足则进行领用,不满足则推出
//            if (consumable.getInventory().compareTo(entity.getAmount()) > -1){
//                List<DtoConsumableDetail> dtoConsumableDetails =  consumableDetailRepository.findByParentIdAndExpiryDateGreaterThanEqualOrderByExpiryDate(consumableDetail.getParentId(), new Date());
//                BigDecimal amount = entity.getAmount();
//                List<DtoConsumableDetail> updateConsumableDetails = new ArrayList<>();
//                for (DtoConsumableDetail dtoConsumableDetail:dtoConsumableDetails) {
        // 消耗品明细可以领取的消耗品数量,若本批次的领完之后会继续领取下一个批次的,直到领取到足够的消耗品
//                    if (amount.compareTo(dtoConsumableDetail.getStorage()) > -1){
//                        amount = amount.subtract(dtoConsumableDetail.getStorage());
//                        dtoConsumableDetail.setStorage(BigDecimal.ZERO);
//                        updateConsumableDetails.add(dtoConsumableDetail);
//                    } else {
//                        // 满足消耗品领取要求则跳出本次循环
//                        dtoConsumableDetail.setStorage(dtoConsumableDetail.getStorage().subtract(amount));
//                        updateConsumableDetails.add(dtoConsumableDetail);
//                        break;
//                    }
//                }
        // 对领取了消耗品的批次进行修改
//                if (StringUtil.isNotEmpty(updateConsumableDetails)){
//                    consumableDetailService.update(updateConsumableDetails);
//                }
        // 更新总数量
//                consumable.setInventory(consumable.getInventory().subtract(entity.getAmount()));
//                consumableService.update(consumable);
//                entity.setBalance(consumable.getInventory());
        // 保存领用日志
//                return super.save(entity);
//            } else {
//                throw new BaseException("库存不足,请核查领用数量");
//            }
//        }

        //找到详细信息
        DtoConsumableDetail consumableDetail = consumableDetailService.findOne(entity.getConsumableDetailId());
        Date nowDate = DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR);
        if (isJudgePassConsumable()){
            if (!"1753-01-01".equals(DateUtil.dateToString(consumableDetail.getExpiryDate(),DateUtil.YEAR))
                    && nowDate.compareTo(consumableDetail.getExpiryDate()) > 0){
                throw new BaseException("此消耗品已过期,无法领用");
            }
        }
        //修改详细信息的库存 如果本批次消耗品满足领用数量,则可以领取
        if (consumableDetail.getStorage().compareTo(entity.getAmount()) > -1) {
            consumableDetail.setStorage(consumableDetail.getStorage().subtract(entity.getAmount()));
            consumableDetailService.update(consumableDetail);
        } else {
            throw new BaseException("库存不足,请核查领用数量");
        }

        //找到基本信息
        DtoConsumable consumable = consumableService.findOne(consumableDetail.getParentId());

//        //修改基本信息的库存
//        consumable.setInventory(consumable.getInventory().subtract(entity.getAmount()));
//        consumableService.update(consumable);

        //结存
        entity.setBalance(consumable.getInventory());
        //默认领用状态为待确认
        entity.setStatus(EnumBase.EnumConsumableLogStatus.待确认.getValue());
        return super.save(entity);
    }

    @Override
    public List<DtoConsumableLog> save(Collection<DtoConsumableLog> entities) {
        List<DtoConsumableLog> list = new ArrayList<>();
        entities.forEach(v->list.add(save(v)));
        return list;
    }

    /**
     * 删除最新一条领用记录
     *
     * @param id 领用id
     * @return 删除的条数
     */
    @Override
    @Transactional
    public Integer logicDeleteById(String id) {
        //找到当前删除的领用记录
        DtoConsumableLog consumableLog = repository.findOne(id);
        //获取到领用记录对应的库存信息
        String consumableDetailId = consumableLog.getConsumableDetailId();
        if (StringUtil.isNotEmpty(consumableDetailId)){
            //删除后将库存退回
            DtoConsumableDetail detail = consumableDetailService.findOne(consumableDetailId);
            //领用数量加库存数量
            detail.setStorage(detail.getStorage().add(consumableLog.getAmount()));
            consumableDetailService.update(detail);
        }
        return super.logicDeleteById(id);
    }

    @Override
    public void delete(Collection<DtoConsumableLog> entities) {
        List<String> ids = entities.stream().map(DtoConsumableLog::getId).collect(Collectors.toList());
        ids.forEach(v->logicDeleteById(v));
    }

    /**
     * 领用确认
     *
     * @param id 领用id
     */
    @Override
    @Transactional
    public void confirm(String id) {
        DtoConsumableLog consumableLog = repository.findOne(id);
        if (StringUtil.isNull(consumableLog)) {
            throw new BaseException("领用记录不存在或已删除！");
        }
        if (!EnumBase.EnumConsumableLogStatus.待确认.getValue().equals(consumableLog.getStatus())) {
            throw new BaseException("只有待确认的领用记录才能确认！");
        }
        consumableLog.setStatus(EnumBase.EnumConsumableLogStatus.已确认.getValue());
        repository.save(consumableLog);
    }

    /**
     * 领用撤销
     *
     * @param consumableLog 领用记录
     */
    @Override
    @Transactional
    public void cancel(DtoConsumableLog consumableLog) {
        DtoConsumableLog oldLog = repository.findOne(consumableLog.getId());
        if (StringUtil.isNull(oldLog)) {
            throw new BaseException("领用记录不存在或已删除！");
        }
        if (EnumBase.EnumConsumableLogStatus.已撤销.getValue().equals(oldLog.getStatus())) {
            throw new BaseException("领用记录已经撤销不能再次撤销！");
        }
        //归还库存数量
        if (StringUtil.isNotEmpty(oldLog.getConsumableDetailId())){
            //删除后将库存退回
            DtoConsumableDetail detail = consumableDetailService.findOne(oldLog.getConsumableDetailId());
            if (StringUtil.isNotNull(detail)) {
                //领用数量加库存数量
                detail.setStorage(detail.getStorage().add(oldLog.getAmount()));
                consumableDetailService.save(detail);
            }
        }
        oldLog.setStatus(EnumBase.EnumConsumableLogStatus.已撤销.getValue());
        oldLog.setCancelReason(consumableLog.getCancelReason());
        repository.save(oldLog);
    }

    private void loadFields(List<DtoConsumableLog> consumableLogs) {
        List<String> consumableIds = consumableLogs.stream().map(DtoConsumableLog::getConsumableId).collect(Collectors.toList());
        List<DtoConsumable> consumables =StringUtil.isNotEmpty(consumableIds) ? consumableService.findAll(consumableIds) : new ArrayList<>();
        List<DtoConsumableDetail> consumableDetailList = StringUtil.isNotEmpty(consumableIds) ? consumableDetailService.findByParentIds(consumableIds) : new ArrayList<>();
        Map<String, List<DtoConsumableDetail>> consumableDetailMap = consumableDetailList.stream().collect(Collectors.groupingBy(DtoConsumableDetail::getParentId));
        Map<String, DtoConsumable> consumableMap = consumables.stream().collect(Collectors.toMap(DtoConsumable::getId, dto -> dto));
        Set<String> warnUserIdSet = new HashSet<>();
        consumableLogs.forEach(log -> {
            DtoConsumable consumable = consumableMap.get(log.getConsumableId());
            if (StringUtil.isNotNull(consumable)) {
                log.setUnit(consumable.getUnit());
                log.setSendWarnUserId(consumable.getSendWarnUserId());
                log.setConsumableName(consumable.getConsumableName());
                log.setConsumableCode(consumable.getConsumableCode());
                log.setCodeInStation(consumable.getCodeInStation());
                if (StringUtil.isNotEmpty(consumable.getSendWarnUserId())) {
                    warnUserIdSet.add(consumable.getSendWarnUserId());
                }
                if (consumableDetailMap.containsKey(consumable.getId())) {
                    log.setProductionCode(consumableDetailMap.get(consumable.getId()).get(0).getProductionCode());
                }
            }
        });
        Map<String, String> userIdNameMap = new HashMap<>();
        if (StringUtil.isNotEmpty(warnUserIdSet)) {
            List<Object[]> objects = commonRepository.find("select a.id, a.cName from DtoPerson a where a.isDeleted = 0 and a.id in :ids",
                    Collections.singletonMap("ids", warnUserIdSet));
            objects.forEach(p -> userIdNameMap.put(p[0].toString(), p[1].toString()));
        }
        consumableLogs.forEach(p -> p.setSendWarnUserName(userIdNameMap.getOrDefault(p.getSendWarnUserId(), "")));
    }

    /**
     * 是否判断消耗品过期领用开关
     *
     * @return 是否开关
     */
    protected Boolean isJudgePassConsumable(){
        return Boolean.FALSE;
    }
}