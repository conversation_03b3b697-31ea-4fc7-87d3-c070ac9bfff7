package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.criteria.InstrumentCriteria;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ProjectInstrumentCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails;
import com.sinoyd.lims.lim.service.ProjectInstrumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ProjectInstrument服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Api(tags = "示例: ProjectInstrument服务")
@RestController
@RequestMapping("api/lim/projectInstrument")
@Validated
public class ProjectInstrumentController extends BaseJpaController<DtoProjectInstrument, String, ProjectInstrumentService> {

    /**
     * 新增ProjectInstrument
     *
     * @param projectInstrument 实体列表
     * @return RestResponse<DtoProjectInstrument>
     */
    @ApiOperation(value = "新增ProjectInstrument", notes = "新增ProjectInstrument")
    @PostMapping
    public RestResponse<DtoProjectInstrument> create(@Validated @RequestBody DtoProjectInstrument projectInstrument) {
        RestResponse<DtoProjectInstrument> restResponse = new RestResponse<>();
        restResponse.setData(service.save(projectInstrument));
        return restResponse;
    }

    /**
     * 修改ProjectInstrument
     *
     * @param projectInstrument 实体列表
     * @return RestResponse<DtoProjectInstrument>
     */
    @ApiOperation(value = "修改ProjectInstrument", notes = "修改ProjectInstrument")
    @PutMapping
    public RestResponse<DtoProjectInstrument> update(@Validated @RequestBody DtoProjectInstrument projectInstrument) {
        RestResponse<DtoProjectInstrument> restResponse = new RestResponse<>();
        restResponse.setData(service.update(projectInstrument));
        return restResponse;
    }


    /**
     * "根据id批量删除ProjectInstrument
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ProjectInstrument", notes = "根据id批量删除ProjectInstrument")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deleteByIds(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 仪器入库操作
     *
     * @param projectInstrumentDetails 仪器入库明细实体
     * @return RestResponse<DtoProjectInstrument>
     */
    @ApiOperation(value = "仪器入库操作", notes = "仪器入库操作")
    @PostMapping("/in")
    public RestResponse<Integer> instrumentIn(@RequestBody DtoProjectInstrumentDetails projectInstrumentDetails) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.instrumentIn(projectInstrumentDetails));
        return restResponse;
    }

    /**
     * 仪器取消入库操作
     *
     * @param projectInstrumentDetails 仪器入库明细实体
     * @return RestResponse<DtoProjectInstrument>
     */
    @ApiOperation(value = "仪器取消入库操作", notes = "仪器取消入库操作")
    @PostMapping("/cancelIn")
    public RestResponse<Integer> instrumentCancelIn(@RequestBody DtoProjectInstrumentDetails projectInstrumentDetails) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.instrumentCancelIn(projectInstrumentDetails));
        return restResponse;
    }

    /**
     * 新增/修改仪器出入库记录时获取待选仪器列表
     *
     * @return RestResponse<DtoInstrument>
     */
    @ApiOperation(value = "新增/修改仪器出入库记录时获取待选仪器列表", notes = "新增/修改仪器出入库记录时获取待选仪器列表")
    @GetMapping("/instrument/out")
    public RestResponse<List<DtoInstrument>> getOutInstrument(InstrumentCriteria criteria) {
        RestResponse<List<DtoInstrument>> restResponse = new RestResponse<>();
        PageBean<DtoInstrument> page = super.getPageBean();
        service.getOutInstrument(page, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(page.getData());
        restResponse.setCount(page.getRowsCount());
        return restResponse;
    }

    /**
     * 仪器入库时获取待选仪器列表
     *
     * @return RestResponse<DtoInstrument>
     */
    @ApiOperation(value = "仪器入库时获取待选仪器列表", notes = "仪器入库时获取待选仪器列表")
    @GetMapping("/instrument/in")
    public RestResponse<List<DtoInstrument>> getInInstrument(ProjectInstrumentCriteria criteria) {
        RestResponse<List<DtoInstrument>> restResponse = new RestResponse<>();
        PageBean<DtoInstrument> page = super.getPageBean();
        service.getInInstrument(page, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(page.getData());
        restResponse.setCount(page.getRowsCount());
        return restResponse;
    }


}