package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoContract;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 合同管理仓储
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
public interface ContractRepository extends IBaseJpaRepository<DtoContract, String> {

    /**
     * 根据合同编号查找合同数量
     *
     * @param id           合同id
     * @param contractCode 合同编号
     * @return 合同个数
     */
    @Query("select count(p.id) from DtoContract p where p.contractCode = :contractCode and p.id != :id and p.isDeleted = 0")
    Integer getCountByCode(@Param("contractCode") String contractCode, @Param("id") String id);


    /**
     * 修改合同状态
     *
     * @param id         合同id
     * @param status     状态数据
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回修改的行数
     */
    @Transactional
    @Modifying
    @Query(" update DtoContract a set a.status = :status,a.modifier = :modifier,a.modifyDate = :modifyDate where  id = :id")
    Integer updateContractStatus(@Param("id") String id,
                                 @Param("status") Integer status,
                                 @Param("modifier") String modifier,
                                 @Param("modifyDate") Date modifyDate);

}