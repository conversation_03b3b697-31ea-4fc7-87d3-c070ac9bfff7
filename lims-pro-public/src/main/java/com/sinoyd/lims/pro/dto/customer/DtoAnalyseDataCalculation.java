package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据计算的Dto
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoAnalyseDataCalculation {

    /**
     * 相关的分析数据
     */
    private Map<String, Object> analyseData = new HashMap<>();


    /**
     * 关联数据
     */
    private List<Map<String, Object>> correlation = new ArrayList<>();

    /**
     * 公式相关参数
     */
    private List<DtoTestFormulaParamsConfig> paramsConfig = new ArrayList<>();

    /**
     * 核对null值
     */
    public void checkNull(String defaultValue,String... fields) {
        for (String field : fields) {
            if (analyseData.containsKey(field) && StringUtil.isNull(analyseData.get(field))) {
                analyseData.put(field, defaultValue);
            }
            for (Map<String, Object> corr : correlation) {
                if (corr.containsKey(field) && StringUtil.isNull(corr.get(field))) {
                    corr.put(field, defaultValue);
                }
            }
        }
    }
}
