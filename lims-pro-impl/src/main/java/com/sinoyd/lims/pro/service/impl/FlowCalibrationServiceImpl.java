package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.pro.dto.DtoFlowCalibration;
import com.sinoyd.lims.pro.dto.DtoFlowCalibration2Frequency;
import com.sinoyd.lims.pro.dto.DtoFlowCalibrationParamData;
import com.sinoyd.lims.pro.dto.DtoFlowCalibrationRow;
import com.sinoyd.lims.pro.repository.FlowCalibration2FrequencyRepository;
import com.sinoyd.lims.pro.repository.FlowCalibrationParamDataRepository;
import com.sinoyd.lims.pro.repository.FlowCalibrationRepository;
import com.sinoyd.lims.pro.repository.FlowCalibrationRowRepository;
import com.sinoyd.lims.pro.service.FlowCalibrationService;
import com.sinoyd.lims.pro.util.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * FlowCalibration操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/14
 */
@Service
@Slf4j
public class FlowCalibrationServiceImpl extends BaseJpaServiceImpl<DtoFlowCalibration,String, FlowCalibrationRepository>
        implements FlowCalibrationService {

    private CodeService codeService;

    private PersonRepository personRepository;

    private InstrumentRepository instrumentRepository;

    private FlowCalibrationRowRepository flowCalibrationRowRepository;

    private FlowCalibrationParamDataRepository flowCalibrationParamDataRepository;

    private FlowCalibration2FrequencyRepository flowCalibration2FrequencyRepository;

    private CalculationService calculationService;

    private CalculateService calculateService;

    final static String dictTypeName = "PRO_FlowCalibrationType";

    @Override
    public void findByPage(PageBean<DtoFlowCalibration> page, BaseCriteria criteria) {
        page.setEntityName("DtoFlowCalibration p,DtoInstrument i");
        page.setSelect("select p");
        super.findByPage(page, criteria);
        loadTransientFields(page.getData());
    }

    @Override
    @Transactional
    public DtoFlowCalibration update(DtoFlowCalibration entity) {
        DtoFlowCalibration flowCalibration = super.update(entity);
        //保存行数据 有公式就重新计算
        saveParamData(entity);
        //加载仪器部分数据
        loadTransientFields(Collections.singletonList(flowCalibration));
        //加载数据行和参数
        loadRowData(flowCalibration);
        return flowCalibration;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<DtoFlowCalibrationRow> rowList = flowCalibrationRowRepository.findByFlowCalibrationIdIn((Collection<String>) ids);
        if (StringUtil.isNotEmpty(rowList)) {
            deleteRow(rowList.stream().map(DtoFlowCalibrationRow::getId).collect(Collectors.toList()));
        }
        List<String> flowCalibration2FrequencyIdList = flowCalibration2FrequencyRepository.findByFlowCalibrationIdIn((List<String>) ids)
                .stream().map(DtoFlowCalibration2Frequency::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(flowCalibration2FrequencyIdList)) {
            flowCalibration2FrequencyRepository.logicDeleteById(flowCalibration2FrequencyIdList);
        }
        return super.logicDeleteById(ids);
    }

    @Override
    public DtoFlowCalibration findOne(String id) {
        DtoFlowCalibration flowCalibration = super.findOne(id);
        //加载仪器部分数据
        loadTransientFields(Collections.singletonList(flowCalibration));
        //加载数据行和参数
        loadRowData(flowCalibration);
        return flowCalibration;
    }

    @Override
    @Transactional
    public Integer deleteRow(List<String> ids) {
        //参数数据删除
        List<String> paramDataIdList = flowCalibrationParamDataRepository.findByFlowCalibrationRowIdIn(ids)
                .stream().map(DtoFlowCalibrationParamData::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(paramDataIdList)) {
            flowCalibrationParamDataRepository.logicDeleteById(paramDataIdList, new Date());
        }
        //参数行删除
        return flowCalibrationRowRepository.logicDeleteById(ids, new Date());
    }

    /**
     * 填充附件字段
     *
     * @param list 原始数据集
     */
    private void loadTransientFields(List<DtoFlowCalibration> list) {
        if (StringUtil.isNotEmpty(list)) {
            Set<String> instrumentIds = list.stream().map(DtoFlowCalibration::getInstrumentId).filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
            List<DtoInstrument> instrumentList = StringUtil.isNotEmpty(instrumentIds) ? instrumentRepository.findAll(instrumentIds) : new ArrayList<>();
            List<DtoPerson> personList = personRepository.findAll();
            list.forEach(flowCalibration -> {
                //仪器相关字段
                instrumentList.stream().filter(v -> v.getId().equals(flowCalibration.getInstrumentId())).findFirst()
                        .ifPresent(v -> {
                            flowCalibration.setInstrumentName(v.getInstrumentName());
                            flowCalibration.setModel(v.getModel());
                            flowCalibration.setInstrumentsCode(v.getInstrumentsCode());
                            flowCalibration.setSerialNo(v.getSerialNo());
                        });
                //校准人全拼
                if (StringUtil.isNotEmpty(flowCalibration.getCalibrationPeople())) {
                    List<String> personIds = Arrays.asList(flowCalibration.getCalibrationPeople().split(","));
                    List<String> names = personList.stream().filter(v -> personIds.contains(v.getId())).map(DtoPerson::getCName)
                            .distinct().sorted().collect(Collectors.toList());
                    flowCalibration.setCalibrationPeopleName(String.join("、", names));
                }
            });
        }
    }

    /**
     * 加载数据行数据
     *
     * @param flowCalibration 校准记录
     */
    private void loadRowData(DtoFlowCalibration flowCalibration) {
        //行参数配置
        List<DtoCode> codeList = codeService.findCodes(dictTypeName);
        List<Map<String, Object>> paramConfigList = new ArrayList<>();
        if (flowCalibration.getCalibrationType() != null) {
            DtoCode typeCode = codeList.stream().filter(v -> flowCalibration.getCalibrationType().toString().equals(v.getDictValue())).findFirst().orElse(null);
            if (typeCode != null) {
                List<DtoCode> paramCodeList = codeList.stream().filter(v -> typeCode.getId().equals(v.getParentId())).collect(Collectors.toList());
                for (DtoCode code : paramCodeList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("name", code.getDictName());
                    map.put("dimension", code.getExtendS1());
                    map.put("parentTitle", code.getExtendS2());
                    map.put("write", !StringUtil.isNotEmpty(code.getExtendS3()));
                    //声级计固定行数及参数名称
                    if ("声级计".equals(flowCalibration.getCalibrationTypeName()) && "参数名称".equals(code.getDictName())) {
                        map.put("write", false);
                    }
                    map.put("order", code.getSortNum());
                    paramConfigList.add(map);
                }
                flowCalibration.setParamConfigList(paramConfigList);
            }
        }
        //行参数数据
        List<DtoFlowCalibrationRow> rowList = flowCalibrationRowRepository.findByFlowCalibrationIdIn(Collections.singletonList(flowCalibration.getId()));
        if (StringUtil.isNotEmpty(rowList)) {
            List<String> rowIds = rowList.stream().map(DtoFlowCalibrationRow::getId).collect(Collectors.toList());
            List<DtoFlowCalibrationParamData> paramDataList = flowCalibrationParamDataRepository.findByFlowCalibrationRowIdIn(rowIds);
            paramDataList.forEach(v -> paramConfigList.stream().filter(p -> p.getOrDefault("name", "").equals(v.getParamName())).findFirst().ifPresent(v::setConfig));
            rowList.forEach(v -> {
                List<DtoFlowCalibrationParamData> rowParamList = paramDataList.stream().filter(p -> v.getId().equals(p.getFlowCalibrationRowId()))
                        .sorted((a, b) -> {
                            int av = a.getConfig() != null ? (int) a.getConfig().get("order") : 0;
                            int bv = b.getConfig() != null ? (int) b.getConfig().get("order") : 0;
                            return bv - av;
                        }).collect(Collectors.toList());
                v.setParamDataList(rowParamList);
            });
            flowCalibration.setRowList(rowList.stream().sorted(Comparator.comparing(DtoFlowCalibrationRow::getOrderNum)).collect(Collectors.toList()));
        }
    }

    /**
     * 保存行数据 有公式就重新计算
     *
     * @param flowCalibration 实体
     */
    private void saveParamData(DtoFlowCalibration flowCalibration) {
        if (StringUtil.isNotEmpty(flowCalibration.getDataList())) {
            List<DtoFlowCalibrationRow> rowList = flowCalibrationRowRepository.findByFlowCalibrationIdIn(Collections.singletonList(flowCalibration.getId()));
            List<String> rowIds = rowList.stream().map(DtoFlowCalibrationRow::getId).collect(Collectors.toList());
            final int[] orderNum = {rowList.stream().sorted(Comparator.comparing(DtoFlowCalibrationRow::getOrderNum).reversed()).map(DtoFlowCalibrationRow::getOrderNum).findFirst().orElse(0)};
            List<DtoFlowCalibrationParamData> paramDataList = StringUtil.isNotEmpty(rowIds) ?
                    flowCalibrationParamDataRepository.findByFlowCalibrationRowIdIn(rowIds) : new ArrayList<>();
            List<DtoCode> codeList = codeService.findCodes(dictTypeName);
            List<DtoCode> paramConfigList = new ArrayList<>();
            if (flowCalibration.getCalibrationType() != null) {
                DtoCode typeCode = codeList.stream().filter(v -> flowCalibration.getCalibrationType().toString().equals(v.getDictValue())).findFirst().orElse(null);
                if (typeCode != null) {
                    paramConfigList = codeList.stream().filter(v -> typeCode.getId().equals(v.getParentId()))
                            .sorted(Comparator.comparing(DtoCode::getSortNum, Comparator.reverseOrder())).collect(Collectors.toList());
                }
            }
            List<DtoFlowCalibrationRow> saveRowList = new ArrayList<>();
            List<DtoFlowCalibrationParamData> saveParamList = new ArrayList<>();
            List<DtoCode> finalParamConfigList = paramConfigList;
            flowCalibration.getDataList().forEach(map -> {
                DtoFlowCalibrationRow row = null;
                if (map.containsKey("id")) {
                    row = rowList.stream().filter(v -> v.getId().equals(map.get("id"))).findFirst().orElse(null);
                }
                if (row == null) {
                    orderNum[0]++;
                    row = new DtoFlowCalibrationRow();
                    row.setOrderNum(orderNum[0]);
                    row.setFlowCalibrationId(flowCalibration.getId());
                    saveRowList.add(row);
                }
                DtoFlowCalibrationRow finalRow = row;
                List<DtoFlowCalibrationParamData> rowParamDataList = paramDataList.stream().filter(v -> finalRow.getId().equals(v.getFlowCalibrationRowId()))
                        .collect(Collectors.toList());
                //处理需要计算的参数
                Map<String, Object> dataMap = new HashMap<>();
                map.forEach((k, v) -> {
                    if (!"id".equals(k)) {
                        if (v != null && MathUtil.isNumeral(v)) {
                            dataMap.put(k, new BigDecimal(v.toString()));
                        } else {
                            dataMap.put(k, v);
                        }
                    }
                });
                finalParamConfigList.forEach(paramConfig -> {
                    String formula = paramConfig.getExtendS3();
                    if (StringUtil.isNotEmpty(formula)) {
                        Map<String, Object> paramMap = new HashMap<>();
                        dataMap.forEach((k, v) -> {
                            if (formula.contains(k)) {
                                paramMap.put(k, v);
                            }
                        });
                        Object result = calculationService.calculationExpression(formula, paramMap);
                        if (result != null) {
                            if (MathUtil.isNumeral(result)) {
                                String reviseResult = calculateService.revise(-1, 2, result.toString());
                                dataMap.put(paramConfig.getDictName(), new BigDecimal(reviseResult));
                            } else {
                                dataMap.put(paramConfig.getDictName(), result);
                            }
                        }
                    }
                });
                //比较更新参数数据
                dataMap.forEach((k, v) -> {
                    if (!"id".equals(k)) {
                        DtoFlowCalibrationParamData paramData = rowParamDataList.stream().filter(existParamData -> k.equals(existParamData.getParamName()))
                                .findFirst().orElse(null);
                        if (paramData == null) {
                            paramData = new DtoFlowCalibrationParamData();
                            paramData.setFlowCalibrationRowId(finalRow.getId());
                            paramData.setParamName(k);
                            paramData.setParamValue(v != null ? v.toString() : "");
                            saveParamList.add(paramData);
                        } else {
                            if (!paramData.getParamValue().equals(v)) {
                                paramData.setParamValue(v.toString());
                                saveParamList.add(paramData);
                            }
                        }
                    }
                });
            });
            flowCalibrationRowRepository.save(saveRowList);
            flowCalibrationParamDataRepository.save(saveParamList);
        }
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setInstrumentRepository(InstrumentRepository instrumentRepository) {
        this.instrumentRepository = instrumentRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setFlowCalibrationRowRepository(FlowCalibrationRowRepository flowCalibrationRowRepository) {
        this.flowCalibrationRowRepository = flowCalibrationRowRepository;
    }

    @Autowired
    public void setFlowCalibrationParamDataRepository(FlowCalibrationParamDataRepository flowCalibrationParamDataRepository) {
        this.flowCalibrationParamDataRepository = flowCalibrationParamDataRepository;
    }

    @Autowired
    @Lazy
    public void setCalculationService(CalculationService calculationService) {
        this.calculationService = calculationService;
    }

    @Autowired
    @Lazy
    public void setCalculateService(CalculateService calculateService) {
        this.calculateService = calculateService;
    }

    @Autowired
    public void setFlowCalibration2FrequencyRepository(FlowCalibration2FrequencyRepository flowCalibration2FrequencyRepository) {
        this.flowCalibration2FrequencyRepository = flowCalibration2FrequencyRepository;
    }
}
