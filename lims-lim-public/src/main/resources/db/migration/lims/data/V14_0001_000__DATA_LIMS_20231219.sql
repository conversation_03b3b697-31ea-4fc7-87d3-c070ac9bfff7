--  收款合同导出
delete from tb_lim_reportconfig where id = '074ad891-b1ab-4ccd-8317-3ff114607e90';
INSERT INTO tb_lim_reportconfig
(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName, usageNum, validate)
VALUES('074ad891-b1ab-4ccd-8317-3ff114607e90', 1, 'OrderContract', '1.xls', '', '', '', 'com.sinoyd.lims.report.service.exports.OrderContractPoiExportServiceImpl', '', '', 0, 1, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-01 09:51:22', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-07 17:12:42', 'com.sinoyd.lims.pro.criteria.OrderContractCriteria', 'LIMReportForms', 'OrderContract', 0, '', '', '', '', '', NULL, 0);

--  收款计划导出
delete from tb_lim_reportconfig where id = 'd34a7e2e-a72f-43a8-b63e-59a4aa08eabb';
INSERT INTO tb_lim_reportconfig
(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName, usageNum, validate)
VALUES('d34a7e2e-a72f-43a8-b63e-59a4aa08eabb', 1, 'ContractCollectionPlan', '1.xls', '', '', '', 'com.sinoyd.lims.report.service.exports.ContractCollectionPlanPoiExportServiceImpl', '', '', 0, 1, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-04 10:46:41', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-04 10:48:06', 'com.sinoyd.lims.pro.criteria.ContractCollectionPlanCriteria', 'LIMReportForms', '/', 0, '', '', '', '', '', NULL, 0);

--  收款记录导出
delete from tb_lim_reportconfig where id = 'e7f44afe-45e1-4d50-bd5b-f21b6f24576f';
INSERT INTO tb_lim_reportconfig
(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName, usageNum, validate)
VALUES('e7f44afe-45e1-4d50-bd5b-f21b6f24576f', 1, 'RecAndPayRecord', '1.xls', '', '', '', 'com.sinoyd.lims.report.service.exports.RecAndPayRecordPoiExportServiceImpl', '', '', 0, 1, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-04 13:23:03', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-04 13:25:47', 'com.sinoyd.lims.pro.criteria.RecAndPayRecordCriteria', 'LIMReportForms', '/', 0, '', '', '', '', '', NULL, 0);