package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.OutSourceDataCriteria;
import com.sinoyd.lims.pro.dto.DtoOutSourceData;
import com.sinoyd.lims.pro.service.OutSourceDataService;
import com.sinoyd.lims.pro.vo.OutSourceDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 数据分包功能接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/02/08
 */
@Api(tags = "示例: 数据分包功能接口")
@RestController
@RequestMapping("api/pro/outSourceData")
public class OutSourceDataController extends BaseJpaController<DtoOutSourceData, String, OutSourceDataService> {


    /**
     * 分页动态条件查询
     *
     * @param criteria 条件参数
     * @return 分页数据
     */
    @ApiOperation(value = "分页动态条件查询", notes = "分页动态条件查询")
    @GetMapping
    public RestResponse<List<DtoOutSourceData>> findByPage(OutSourceDataCriteria criteria) {
        PageBean<DtoOutSourceData> pageBean = super.getPageBean();
        RestResponse<List<DtoOutSourceData>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }


    /**
     * 查询所有分析方法
     *
     * @return 分析方法
     */
    @ApiOperation(value = "查询所有分析方法", notes = "查询所有分析方法")
    @GetMapping("/method/all")
    public RestResponse<Map<String, String>> findAllAnalyzeMethod() {
        RestResponse<Map<String, String>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAllAnalyzeMethod());
        return restResponse;
    }

    /**
     * "批量操作中的批量修改
     *
     * @param vo 前端传输类
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量操作中的批量修改", notes = "批量操作中的批量修改")
    @PutMapping("/batch")
    public RestResponse<List<DtoOutSourceData>> updateBatch(@RequestBody OutSourceDataVO vo) {
        RestResponse<List<DtoOutSourceData>> restResp = new RestResponse<>();
        restResp.setData(service.updateBatch(vo));
        return restResp;
    }

    /**
     * 修改数据
     *
     * @param entitys 分包数据dtos
     * @return RestResponse<String>
     */
    @ApiOperation(value = "修改数据", notes = "修改数据")
    @PutMapping
    public RestResponse<List<DtoOutSourceData>> update(@RequestBody List<DtoOutSourceData> entitys) {
        RestResponse<List<DtoOutSourceData>> restResp = new RestResponse<>();
        restResp.setData(service.update(entitys));
        return restResp;
    }

    /**
     * 根据分析数据id查询详情
     *
     * @param id 分析数据id
     * @return 分包数据
     */
    @ApiOperation(value = "根据分析数据id查询详情", notes = "根据分析数据id查询详情")
    @GetMapping("{id}")
    public RestResponse<DtoOutSourceData> findOne(@PathVariable("id") String id) {
        RestResponse<DtoOutSourceData> restResponse = new RestResponse<>();
        restResponse.setData(service.findOne(id));
        return restResponse;
    }

    /**
     * 查询所有量纲名称
     *
     * @return 分包数据list
     */
    @ApiOperation(value = "查询所有量纲名称", notes = "查询所有量纲名称")
    @GetMapping("/dimension/all")
    public RestResponse<Map<String, String>> findAllDimensionName() {
        RestResponse<Map<String, String>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAllDimensionName());
        return restResponse;
    }

    /**
     * excel导出
     *
     * @param criteria 条件参数
     * @param response 响应数据
     */
    @ApiOperation(value = "excel导出", notes = "excel导出")
    @GetMapping("/export")
    public void exportExcel(OutSourceDataCriteria criteria, HttpServletResponse response) {
        service.exportExcel(criteria, response);
    }

    /**
     * 分包数据导入
     *
     * @param file 导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/import")
    public RestResponse<Boolean> importExcel(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.importExcel(file, objectMap, response);
        restResponse.setData(true);
        return restResponse;
    }

}