package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 评价标准导出
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/03
 * @since V100R001
 */
@Data
@AllArgsConstructor
public class DtoExportEvaluationCriteria {

    /**
     * 评价限值Id
     */
    private String Id;
    /**
     * 标准名称
     */
    @Excel(name = "标准名称(必填)",orderNum = "100",width = 20)
    private String name;

    /**
     * 标准代码
     */
    @Excel(name = "标准代码(必填)",orderNum = "200",width = 20)
    private String code;

    /**
     * 实施时间
     */
    @Excel(name = "实施日期",orderNum = "300",width = 20)
    private String startTime;

    /**
     * 样品类型
     */
    @Excel(name = "样品类型(必填)",orderNum = "400",width = 20)
    private String sampleType;

    /**
     * 评价条件
     */
    @Excel(name = "评价条件",orderNum = "500",width = 20)
    private String levelName;

    /**
     * 评价因子
     */
    @Excel(name = "评价因子",orderNum = "600",width = 20)
    private String analyseItemName;

    /**
     * 下限表达式
     */
    @Excel(name = "下限表达式",orderNum = "700",width = 20)
    private String lowerLimitSymbol;

    /**
     * 下限值
     */
    @Excel(name = "下限",orderNum = "800",width = 20)
    private String lowerLimitNum;

    /**
     * 上限表达式
     */
    @Excel(name = "上限表达式",orderNum = "900",width = 20)
    private String upperLimitSymbol;

    /**
     * 上限值
     */
    @Excel(name = "上限",orderNum = "1000",width = 20)
    private String upperLimitNum;

    public DtoExportEvaluationCriteria(String id,String lowerLimitSymbol, String lowerLimitNum, String upperLimitSymbol, String upperLimitNum) {
        this.Id = id;
        this.lowerLimitSymbol = lowerLimitSymbol;
        this.lowerLimitNum = lowerLimitNum;
        this.upperLimitSymbol = upperLimitSymbol;
        this.upperLimitNum = upperLimitNum;
    }
}
