package com.sinoyd.lims.pro.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentRepairApply;
import com.sinoyd.lims.lim.service.OAInstrumentRepairApplyService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.service.OAInstrumentRepairService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 仪器维修业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-02
 * @since V100R001
 */
@Service
public class OAInstrumentRepairServiceImpl implements OAInstrumentRepairService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    /**
     * 仪器维修服务
     */
    @Autowired
    @Lazy
    private OAInstrumentRepairApplyService oaInstrumentRepairApplyService;

    /**
     * 仪器服务
     */
    @Autowired
    @Lazy
    private InstrumentService instrumentService;

    @Override
    public String startProcess(DtoOATaskCreate<List<DtoOAInstrumentRepairApply>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.仪器维修, taskDto, vars);

        List<DtoOAInstrumentRepairApply> data = taskDto.getData();

        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        DtoOATaskRelation taskRelation = null;

        for (DtoOAInstrumentRepairApply repair : data) {
            taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(repair.getId());

            taskRelations.add(taskRelation);
        }

        // 添加仪器维修记录
        oaInstrumentRepairApplyService.save(data);

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<List<DtoOAInstrumentRepairApply>, List<DtoInstrument>> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<List<DtoOAInstrumentRepairApply>, List<DtoInstrument>> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        List<DtoOATaskRelation> relations = oaTaskRelationService.findListByTaskId(taskId);

        // 仪器维修记录信息
        List<DtoOAInstrumentRepairApply> repairRecords = new ArrayList<>();

        // 仪器信息
        List<DtoInstrument> instruments = new ArrayList<>();

        List<String> objectIds = relations.stream().map(DtoOATaskRelation::getObjectId).distinct().collect(Collectors.toList());
        if (objectIds.size() > 0) {
            repairRecords.addAll(oaInstrumentRepairApplyService.findAll(objectIds));
            List<String> instrumentIds = repairRecords.stream().map(DtoOAInstrumentRepairApply::getInstrumentId).distinct().collect(Collectors.toList());
            instruments.addAll(instrumentService.findAll(instrumentIds));
        }
        // 设置详细信息
        taskDetail.setDetail(repairRecords);
        // 设置扩展信息
        taskDetail.setExtend(instruments);

        return taskDetail;
    }

    @Override
    public void findCanSponsor(PageBean<?> page, String key, String typeId) {

    }

    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<List<DtoOAInstrumentRepairApply>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.仪器维修, taskDto, vars);
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<List<DtoOAInstrumentRepairApply>> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<List<DtoOAInstrumentRepairApply>> taskDto,DtoOATask oaTask){
        List<DtoOAInstrumentRepairApply> data = taskDto.getData();
        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        DtoOATaskRelation taskRelation = null;
        for (DtoOAInstrumentRepairApply repair : data) {
            taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(repair.getId());
            taskRelations.add(taskRelation);
        }
        // 添加仪器维修记录
        oaInstrumentRepairApplyService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<List<DtoOAInstrumentRepairApply>> taskDto) {
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }
}