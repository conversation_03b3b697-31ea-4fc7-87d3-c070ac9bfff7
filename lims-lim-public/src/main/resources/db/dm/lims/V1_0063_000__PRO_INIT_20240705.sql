CREATE TABLE "TB_PRO_PROJECT2FIXEDPROPERTY"
(
    "projectId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "fixedPropertyId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "id"              VARCHAR(50) NOT NULL
);
CREATE TABLE "TB_PRO_PROJECT2REPORT"
(
    "id"           VARCHAR(50) NOT NULL,
    "projectId"    VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "reportId"     VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "uploadStatus" INT         DEFAULT 0
                               NOT NULL
);
CREATE TABLE "TB_PRO_PROJECT2WORKSHEETFOLDER"
(
    "id"                VARCHAR(50) NOT NULL,
    "projectId"         VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "workSheetFolderId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL
);
CREATE TABLE "TB_PRO_PROJECTAPPROVAL"
(
    "id"              VARCHAR(50) NOT NULL,
    "projectId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "approveDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "modifyStatus"    VARCHAR(50)  DEFAULT ''
        NULL,
    "approvePersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "comment"         VARCHAR(255) DEFAULT ''
        NULL
);
CREATE TABLE "TB_PRO_PROJECTPLAN"
(
    "id"                  VARCHAR(50) NOT NULL,
    "projectId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "leaderId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "reportMakerId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "schemeMakerId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "spotPersonId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "supervisorId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "deadLine"            TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                      NOT NULL,
    "reportDate"          TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                      NOT NULL,
    "requireAnalyzeDate"  TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                      NOT NULL,
    "requireSamplingDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                      NOT NULL,
    "responsePerson"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "requires"            VARCHAR(1000) NULL,
    "testMethodRequires"  VARCHAR(1000) NULL,
    "remark"              VARCHAR(1000) NULL,
    "isUseMethod"         BIT          DEFAULT 0
                                      NOT NULL,
    "isEvaluate"          BIT          DEFAULT 0
                                      NOT NULL,
    "isWarning"           BIT          DEFAULT 0
                                      NOT NULL,
    "warningDay"          INT          DEFAULT 0
                                      NOT NULL,
    "isFeedback"          BIT          DEFAULT 0
                                      NOT NULL,
    "isContract"          BIT          DEFAULT 0
                                      NOT NULL,
    "subName"             VARCHAR(255) NULL,
    "subItems"            VARCHAR(1000) NULL,
    "subMethod"           INT          DEFAULT 0
                                      NOT NULL,
    "isOutsourcing"       INT          DEFAULT 0
                                      NOT NULL,
    "isMakePlan"          BIT          DEFAULT 0
                                      NOT NULL,
    "reportMakerIIId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "qcGrade"             INT          DEFAULT (-1)
                                      NOT NULL,
    "qcType"              INT          DEFAULT (-1)
                                      NOT NULL,
    "qcSource"            VARCHAR(255) NULL,
    "judgment"            VARCHAR(255) NULL,
    "orgId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL
);
CREATE TABLE "TB_PRO_PROJECTPLAN2PERSON"
(
    "id"             VARCHAR(50) NOT NULL,
    "projectPlanId"  VARCHAR(50) NOT NULL,
    "assessPersonId" VARCHAR(50) NOT NULL
);
CREATE TABLE "TB_PRO_PROJECTPUSHLOG"
(
    "id"           VARCHAR(50) NOT NULL,
    "title"        VARCHAR(255) NULL,
    "message"      CLOB NULL,
    "url"          VARCHAR(255) NULL,
    "personId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "pushTime"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "pushStatus"   BIT          DEFAULT 0
                               NOT NULL,
    "pushResponse" VARCHAR(1000) NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL
);
CREATE TABLE "TB_PRO_QCDATA"
(
    "id"          VARCHAR(50) NOT NULL,
    "userId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "testId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "paramsName"  VARCHAR(255) NULL,
    "lastTime"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL,
    "stdValue"    VARCHAR(100) NULL,
    "avgValue"    NUMBER(22,0) DEFAULT 0
        NOT NULL,
    "upAssist"    NUMBER(22,0) DEFAULT 0
        NOT NULL,
    "downAssist"  NUMBER(22,0) DEFAULT 0
        NOT NULL,
    "upWarning"   NUMBER(22,0) DEFAULT 0
        NOT NULL,
    "downWarning" NUMBER(22,0) DEFAULT 0
        NOT NULL,
    "upControl"   NUMBER(22,0) DEFAULT 0
        NOT NULL,
    "downControl" NUMBER(22,0) DEFAULT 0
        NOT NULL,
    "dataType"    INT          DEFAULT 0
                              NOT NULL,
    "dataStr"     CLOB NULL,
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL
);
CREATE TABLE "TB_PRO_QCRESULTEVALUATION"
(
    "id"               VARCHAR(50) NOT NULL,
    "projectId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "qcSituation"      VARCHAR(1000) NULL,
    "resultCompare"    VARCHAR(1000) NULL,
    "evaluationResult" VARCHAR(1000) NULL,
    "createPerson"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "createTime"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_QUALITYCONTROL"
(
    "id"                             VARCHAR(50) NOT NULL,
    "associateSampleId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "qcGrade"                        INT          DEFAULT (-1)
                                                 NOT NULL,
    "qcType"                         INT          DEFAULT (-1)
                                                 NOT NULL,
    "qcValue"                        VARCHAR(50) NULL,
    "qcVolume"                       VARCHAR(50) NULL,
    "qaId"                           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "qcTime"                         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                                 NOT NULL,
    "qcTestValue"                    VARCHAR(50) NULL,
    "realSampleTestValue"            VARCHAR(50) NULL,
    "qcCode"                         VARCHAR(50) NULL,
    "qcOriginValue"                  VARCHAR(50) NULL,
    "qcValidDate"                    TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                                 NOT NULL,
    "orgId"                          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "qcStandardDate"                 TIMESTAMP(0) NULL,
    "qcStandardId"                   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "qcConcentration"                VARCHAR(50) NULL,
    "qcVolumeDimensionId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "qcValueDimensionId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "qcTestValueDimensionId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "realSampleTestValueDimensionId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "qcConcentrationDimensionId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "isDeleted"                      BIT          DEFAULT 0
                                                 NOT NULL,
    "creator"                        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "createDate"                     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                                 NOT NULL,
    "domainId"                       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "modifier"                       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                 NOT NULL,
    "modifyDate"                     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                                 NOT NULL
);
CREATE TABLE "TB_PRO_QUALITYCONTROLEVALUATE"
(
    "id"             VARCHAR(50) NOT NULL,
    "objectId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "qcId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "checkItem"      VARCHAR(100) NULL,
    "judgingMethod"  INT NULL,
    "isPass"         BIT NULL,
    "checkItemValue" VARCHAR(100) NULL,
    "allowLimit"     VARCHAR(50) NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "dimensionName"  VARCHAR(50) NULL,
    "isDeleted"      BIT          DEFAULT 0
                                 NOT NULL,
    "limitId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL
);
CREATE TABLE "TB_PRO_QUALITYMANAGE"
(
    "id"                 VARCHAR(50) NOT NULL,
    "anaId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "qmType"             INT          DEFAULT (-1)
                                     NOT NULL,
    "qmValue"            VARCHAR(255) NULL,
    "qmRange"            VARCHAR(255) NULL,
    "qmVolume"           VARCHAR(50) NULL,
    "qmPersonId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "qmTime"             TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "qmTestValue"        VARCHAR(50) NULL,
    "stTestValue"        VARCHAR(50) NULL,
    "qmCode"             VARCHAR(255) NULL,
    "qmOriginValue"      VARCHAR(50) NULL,
    "instrumentId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "isMixedStandard"    BIT          DEFAULT 0
                                     NOT NULL,
    "redAnalyzeItemName" VARCHAR(100) NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "unitId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "unit"               VARCHAR(50)  DEFAULT ''
        NULL
);
CREATE TABLE "TB_PRO_QUOTATIONDETAIL"
(
    "id"                 VARCHAR(50)               NOT NULL,
    "orderId"            VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "quotationId"        VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "sampleTypeId"       VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "redAnalyseItemName" VARCHAR(255) NULL,
    "analyseItemId"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "redAnalyseMethod"   VARCHAR(255) NULL,
    "analyseMethodId"    VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "testId"             VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "folderName"         VARCHAR(2000) NULL,
    "projectCount"       INT            DEFAULT 1
                                                   NOT NULL,
    "cycleOrder"         INT            DEFAULT 1
                                                   NOT NULL,
    "timesOrder"         INT            DEFAULT 1
                                                   NOT NULL,
    "sampleOrder"        INT            DEFAULT 1
                                                   NOT NULL,
    "inspectedCount"     INT            DEFAULT 0
                                                   NOT NULL,
    "residueCount"       INT            DEFAULT 1
                                                   NOT NULL,
    "sampleCount"        INT            DEFAULT 1
                                                   NOT NULL,
    "samplingPrice"      DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "analysePrice"       DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "charge"             DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "quotationPrice"     DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "isDeleted"          BIT                       NOT NULL,
    "orgId"              VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "creator"            VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "createDate"         TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()
                                                   NOT NULL,
    "domainId"           VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "modifier"           VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                   NOT NULL,
    "modifyDate"         TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()
                                                   NOT NULL,
    "isTotal"            BIT            DEFAULT 0
                                                   NOT NULL
);
CREATE TABLE "TB_PRO_QUOTATIONDETAIL2TEST"
(
    "id"       VARCHAR(50) NOT NULL,
    "detailId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                           NOT NULL,
    "testId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                           NOT NULL
);
CREATE TABLE "TB_PRO_RECEIVESAMPLERECORD"
(
    "id"                VARCHAR(50) NOT NULL,
    "projectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "recordCode"        VARCHAR(20) NULL,
    "samplingTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "sendTime"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "senderId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "senderName"        VARCHAR(100) NULL,
    "receiveTime"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "recorderId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "receiveType"       INT          DEFAULT 0
                                    NOT NULL,
    "status"            VARCHAR(50) NULL,
    "receiveStatus"     INT          DEFAULT 1
                                    NOT NULL,
    "infoStatus"        INT          DEFAULT 1
                                    NOT NULL,
    "reportCode"        VARCHAR(20) NULL,
    "uploadStatus"      INT          DEFAULT 0
                                    NOT NULL,
    "uploadTime"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "validAnalyzeId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "checkerId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "checkTime"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "validCheckerId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "auditorId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "auditTime"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "validAuditerId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "backOpinion"       VARCHAR(1000) NULL,
    "remark"            VARCHAR(255) NULL,
    "json"              CLOB NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "sortId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "receiveSampleDate" TIMESTAMP(0) NULL,
    "recipientId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "isDeleted"         BIT          DEFAULT 0
                                    NOT NULL
);
CREATE TABLE "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"
(
    "id"         VARCHAR(50) NOT NULL,
    "templateId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "paramId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "paramName"  VARCHAR(500) NULL,
    "orderNum"   INT          DEFAULT (-1)
                             NOT NULL,
    "type"       INT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"
(
    "id"           VARCHAR(50) NOT NULL,
    "receiveId"    VARCHAR(50) NOT NULL,
    "templateName" VARCHAR(500) NULL,
    "sampleTypeId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL
);
CREATE TABLE "TB_PRO_RECEIVESUBSAMPLERECORD"
(
    "id"              VARCHAR(50) NOT NULL,
    "receiveId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "projectId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "code"            VARCHAR(20) NULL,
    "status"          VARCHAR(50)  DEFAULT '0'
        NULL,
    "subStatus"       INT          DEFAULT 0
                                  NOT NULL,
    "receivePersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "receiveName"     VARCHAR(50) NULL,
    "receiveTime"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                  NOT NULL,
    "checkerId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "checkerName"     VARCHAR(50) NULL,
    "checkTime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                  NOT NULL,
    "auditorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "auditorName"     VARCHAR(50) NULL,
    "auditTime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                  NOT NULL,
    "backOpinion"     VARCHAR(1000) NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "isDeleted"       BIT          DEFAULT 0
                                  NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL
);
CREATE TABLE "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"
(
    "receiveSubSampleRecordId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                           NOT NULL,
    "sampleId"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                           NOT NULL,
    "id"                       VARCHAR(50) NOT NULL,
    "isDeleted"                BIT          DEFAULT 0
                                           NOT NULL,
    "creator"                  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                           NOT NULL,
    "createDate"               TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                           NOT NULL,
    "domainId"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                           NOT NULL,
    "modifier"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                           NOT NULL,
    "modifyDate"               TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                           NOT NULL,
    "orgId"                    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                           NOT NULL
);
CREATE TABLE "TB_PRO_REPORT"
(
    "id"                VARCHAR(50) NOT NULL,
    "reportTypeId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "projectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "status"            VARCHAR(50) NOT NULL,
    "dataChangeStatus"  INT          DEFAULT 1
                                    NOT NULL,
    "code"              VARCHAR(100) NULL,
    "folderName"        VARCHAR(100) NULL,
    "sampleName"        VARCHAR(50) NULL,
    "testName"          VARCHAR(50) NULL,
    "createPersonId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createTime"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "reportYear"        VARCHAR(10) NULL,
    "securityCode"      VARCHAR(100) NULL,
    "certifiedPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "reportNum"         INT          DEFAULT 0
                                    NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "grantStatus"       INT          DEFAULT 1
                                    NOT NULL,
    "firstInstanceId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "secondInstanceId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "analyseItemSortId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "folderSortId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "isDeleted"         BIT          DEFAULT 0
                                    NOT NULL
);
CREATE TABLE "TB_PRO_REPORTACHIEVEMENT2PERSON"
(
    "id"         VARCHAR(50) NOT NULL,
    "personId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_PRO_REPORTACHIEVEMENTDETAILS"
(
    "id"             VARCHAR(50) NOT NULL,
    "achievementId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "reportCode"     VARCHAR(50) NULL,
    "reportId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "reportTypeId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "reportPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "reportTime"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                 NOT NULL,
    "projectCode"    VARCHAR(50) NULL,
    "projectName"    VARCHAR(100) NULL,
    "entId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "entName"        VARCHAR(255) NULL
);
CREATE TABLE "TB_PRO_REPORTBASEINFO"
(
    "id"               VARCHAR(50) NOT NULL,
    "reportId"         VARCHAR(50) NOT NULL,
    "projectName"      VARCHAR(200) NULL,
    "systemCode"       VARCHAR(100) NULL,
    "inspectedEnt"     VARCHAR(200) NULL,
    "inspectedAddress" VARCHAR(300) NULL,
    "customerName"     VARCHAR(200) NULL,
    "customerAddress"  VARCHAR(300) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "isDeleted"        BIT          DEFAULT 0
                                   NOT NULL,
    "reportDate"       VARCHAR(100) NULL,
    "technicalRemark"  VARCHAR(1000) NULL,
    "resultEvaluation" BIT          DEFAULT 0
                                   NOT NULL,
    "testPurpose"      VARCHAR(300) NULL
);
CREATE TABLE "TB_PRO_REPORTDETAIL"
(
    "id"         VARCHAR(50) NOT NULL,
    "reportId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "objectId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "objectType" INT          DEFAULT 1
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_PRO_REPORTFOLDERINFO"
(
    "id"           VARCHAR(50) NOT NULL,
    "reportId"     VARCHAR(50) NOT NULL,
    "folderName"   VARCHAR(200) NULL,
    "folderCode"   VARCHAR(100) NULL,
    "folderRemark" VARCHAR(800) NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "folderId"     VARCHAR(200) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "isDeleted"    BIT          DEFAULT 0
                               NOT NULL
);
CREATE TABLE "TB_PRO_REPORTFOLDERSORTINFO"
(
    "id"         VARCHAR(50) NOT NULL,
    "reportId"   VARCHAR(50) NOT NULL,
    "folderId"   VARCHAR(50) NULL,
    "orderNum"   INT          DEFAULT 0
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "isDeleted"  BIT          DEFAULT 0
                             NOT NULL
);
CREATE TABLE "TB_PRO_REPORTNUMBERPOOL"
(
    "id"           VARCHAR(50) NOT NULL,
    "year"         INT NULL,
    "code"         VARCHAR(100) DEFAULT ''
                               NOT NULL,
    "reportTypeId" VARCHAR(50)  DEFAULT ''
                               NOT NULL,
    "status"       INT          DEFAULT 0
        NULL,
    "usedDate"     TIMESTAMP(0) NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "number"       INT NULL,
    "serialType"   VARCHAR(50) NULL
);
CREATE TABLE "TB_PRO_REPORTRECOVER"
(
    "id"            VARCHAR(50) NOT NULL,
    "projectId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "recoverPerson" VARCHAR(50) NULL,
    "recoverTime"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL,
    "recoverReason" VARCHAR(100) NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL
);
CREATE TABLE "TB_PRO_REPORTRECOVER2REPORT"
(
    "id"        VARCHAR(50) NOT NULL,
    "recoverId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                            NOT NULL,
    "reportId"  VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                            NOT NULL
);
CREATE TABLE "TB_PRO_REPORTSAMPLEINFO"
(
    "id"                 VARCHAR(50) NOT NULL,
    "reportId"           VARCHAR(50) NOT NULL,
    "sampleCode"         VARCHAR(100) NULL,
    "sampleRemark"       VARCHAR(800) NULL,
    "reportFolderInfoId" VARCHAR(50) NOT NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "sampleId"           VARCHAR(200) DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "isDeleted"          BIT          DEFAULT 0
                                     NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLE"
(
    "id"                     VARCHAR(50) NOT NULL,
    "code"                   VARCHAR(50) NULL,
    "subProjectId"           VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "projectId"              VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "receiveId"              VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "sampleFolderId"         VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "samplingFrequencyId"    VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "cycleOrder"             INT           DEFAULT 0
                                         NOT NULL,
    "timesOrder"             INT           DEFAULT 0
                                         NOT NULL,
    "sampleOrder"            INT           DEFAULT 0
                                         NOT NULL,
    "redFolderName"          VARCHAR(100) NULL,
    "redAnalyzeItems"        VARCHAR(4000) DEFAULT ''
        NULL,
    "samplingPersonId"       VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "sampleTypeId"           VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "sampleCategory"         INT           DEFAULT 0
                                         NOT NULL,
    "inspectedEnt"           VARCHAR(100) NULL,
    "inspectedEntId"         VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "inceptTime"             TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                         NOT NULL,
    "samplingTimeBegin"      TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'
                                         NOT NULL,
    "samplingTimeEnd"        TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'
                                         NOT NULL,
    "status"                 VARCHAR(50) NOT NULL,
    "samplingConfig"         INT           DEFAULT 0
                                         NOT NULL,
    "samplingStatus"         INT           DEFAULT 0
                                         NOT NULL,
    "innerReceiveStatus"     INT           DEFAULT 0
                                         NOT NULL,
    "ananlyzeStatus"         INT           DEFAULT 0
                                         NOT NULL,
    "storeStatus"            INT           DEFAULT 0
                                         NOT NULL,
    "makeStatus"             INT           DEFAULT 0
                                         NOT NULL,
    "dataChangeStatus"       INT           DEFAULT 0
                                         NOT NULL,
    "customerCode"           VARCHAR(50) NULL,
    "qcId"                   VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "associateSampleId"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "isDeleted"              BIT           DEFAULT 0
                                         NOT NULL,
    "parentSampleId"         VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "makeSamPerId"           VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "isPrint"                INT           DEFAULT 0
                                         NOT NULL,
    "isKeep"                 BIT           DEFAULT 0
                                         NOT NULL,
    "keepLongTime"           INT           DEFAULT 0
                                         NOT NULL,
    "storageConditions"      VARCHAR(255) NULL,
    "loseEfficacyTime"       TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'
                                         NOT NULL,
    "pack"                   VARCHAR(50) NULL,
    "sampleWeight"           VARCHAR(50) NULL,
    "weightOrQuantity"       VARCHAR(50) NULL,
    "samColor"               VARCHAR(50) NULL,
    "sampleExplain"          VARCHAR(1000) NULL,
    "volume"                 VARCHAR(50) NULL,
    "samplingPlace"          VARCHAR(100) NULL,
    "remark"                 VARCHAR(1000) NULL,
    "samKind"                INT           DEFAULT 0
                                         NOT NULL,
    "isQualified"            BIT           DEFAULT 0
                                         NOT NULL,
    "sampleSource"           VARCHAR(100) NULL,
    "originalStatus"         VARCHAR(100) NULL,
    "isReturned"             BIT           DEFAULT 0
                                         NOT NULL,
    "preTreatmentCases"      VARCHAR(1000) NULL,
    "unqualifiedReason"      VARCHAR(1000) NULL,
    "disposeMeasure"         VARCHAR(1000) NULL,
    "consistencyValidStatus" INT           DEFAULT 0
                                         NOT NULL,
    "lon"                    VARCHAR(20) NULL,
    "lat"                    VARCHAR(20) NULL,
    "signerId"               VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "signTime"               TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'
                                         NOT NULL,
    "samplingRecordId"       VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "isOutsourcing"          INT           DEFAULT 0
                                         NOT NULL,
    "blindType"              INT           DEFAULT 0
                                         NOT NULL,
    "lastNewSubmitTime"      TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'
                                         NOT NULL,
    "orgId"                  VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "creator"                VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "createDate"             TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                         NOT NULL,
    "domainId"               VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "modifier"               VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                         NOT NULL,
    "modifyDate"             TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                         NOT NULL,
    "preparedStatus"         INT           DEFAULT 0
                                         NOT NULL,
    "sortNum"                VARCHAR(50)   DEFAULT ''
                                         NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLEDISPOSE"
(
    "id"              VARCHAR(50) NOT NULL,
    "sampleId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "sampleSource"    VARCHAR(100) NULL,
    "sampleCount"     INT          DEFAULT 1
                                  NOT NULL,
    "reserveDate"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                  NOT NULL,
    "reserveLocation" VARCHAR(100) NULL,
    "redAnalyzeItems" VARCHAR(1000) NULL,
    "disposePersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "disposeDate"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                  NOT NULL,
    "disposeSolution" VARCHAR(50) NOT NULL,
    "disposeRemarks"  VARCHAR(1000) NULL,
    "isDeleted"       BIT          DEFAULT 0
                                  NOT NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "isDisposed"      BIT          DEFAULT 0
                                  NOT NULL,
    "saveCondition"   VARCHAR(100) NULL
);
CREATE TABLE "TB_PRO_SAMPLEDISPOSE2TEST"
(
    "id"                 VARCHAR(50) NOT NULL,
    "sampleDisposeId"    VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "testId"             VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "redAnalyzeItemName" VARCHAR(100) NULL
);
CREATE TABLE "TB_PRO_SAMPLEFOLDER"
(
    "id"                        VARCHAR(50)               NOT NULL,
    "projectId"                 VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "subProjectId"              VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "watchSpot"                 VARCHAR(100) NULL,
    "fixedPointId"              VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "sampleTypeId"              VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "folderCode"                VARCHAR(50) NULL,
    "chargeRate"                DECIMAL(18, 2) DEFAULT 1. NOT NULL,
    "folderTypeId"              VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "folderTypeName"            VARCHAR(50) NULL,
    "redAnalyzeItems"           VARCHAR(1000) NULL,
    "lon"                       VARCHAR(20) NULL,
    "lat"                       VARCHAR(20) NULL,
    "grade"                     INT            DEFAULT 1
                                                          NOT NULL,
    "exhaustPipeHeight"         VARCHAR(20) NULL,
    "isOutsourcing"             INT            DEFAULT 0
                                                          NOT NULL,
    "craftFacilityName"         VARCHAR(100) NULL,
    "purificateFacilityName"    VARCHAR(100) NULL,
    "pollutionType"             VARCHAR(100) NULL,
    "boilerMakeUnit"            VARCHAR(100) NULL,
    "equipmentTypeName"         VARCHAR(100) NULL,
    "boilerUseDate"             TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'
                                                          NOT NULL,
    "chimneyHeight"             VARCHAR(20) NULL,
    "purificateFacilityUnit"    VARCHAR(100) NULL,
    "purificateFacilityType"    VARCHAR(100) NULL,
    "purificateFacilityUseDate" TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'
                                                          NOT NULL,
    "fuelType"                  VARCHAR(50) NULL,
    "stoveFacilityCode"         VARCHAR(50) NULL,
    "craftFacilityUseDate"      TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'
                                                          NOT NULL,
    "isTransition"              BIT            DEFAULT 0
                                                          NOT NULL,
    "inspectedEntId"            VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "inspectedEnt"              VARCHAR(100) NULL,
    "orgId"                     VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "isDeleted"                 BIT            DEFAULT 0
                                                          NOT NULL,
    "creator"                   VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "createDate"                TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()
                                                          NOT NULL,
    "domainId"                  VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "modifier"                  VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                          NOT NULL,
    "modifyDate"                TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()
                                                          NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLEFOLDEREVALUATE"
(
    "id"             VARCHAR(50) NOT NULL,
    "sampleFolderId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "testId"         VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "qcRateValue"    VARCHAR(255) NULL,
    "folderPass"     VARCHAR(50) DEFAULT ''
        NULL,
    "remark"         VARCHAR(255) NULL,
    "resultEvaluate" VARCHAR(50) DEFAULT ''
        NULL
);
CREATE TABLE "TB_PRO_SAMPLEFOLDERTEMPLATE"
(
    "id"              VARCHAR(50) NOT NULL,
    "approveId"       VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "sampleFolderId"  VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "operateType"     INT           DEFAULT 0
                                  NOT NULL,
    "watchSpot"       VARCHAR(100)  DEFAULT ''
        NULL,
    "sampleTypeId"    VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "redAnalyzeItems" VARCHAR(1000) DEFAULT ''
        NULL,
    "lon"             VARCHAR(20)   DEFAULT ''
        NULL,
    "lat"             VARCHAR(20)   DEFAULT ''
        NULL
);
CREATE TABLE "TB_PRO_SAMPLEGROUP"
(
    "id"                      VARCHAR(50) NOT NULL,
    "receiveId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "sampleId"                VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "sampleTypeGroupId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "sampleTypeGroupName"     VARCHAR(50) NULL,
    "analyseItemNames"        VARCHAR(2000) NULL,
    "hasScanned"              BIT          DEFAULT 0
                                          NOT NULL,
    "scannedTime"             TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                          NOT NULL,
    "scanner"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "orgId"                   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "creator"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "createDate"              TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                          NOT NULL,
    "domainId"                VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "modifier"                VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "modifyDate"              TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                          NOT NULL,
    "fixer"                   VARCHAR(1000) NULL,
    "containerName"           VARCHAR(255) NULL,
    "remark"                  VARCHAR(1000) NULL,
    "saveCondition"           VARCHAR(1000) NULL,
    "sampleStatus"            VARCHAR(1000) NULL,
    "riskDescription"         VARCHAR(1000) NULL,
    "transportationCondition" VARCHAR(1000) NULL,
    "samplingBegin"           VARCHAR(1000) NULL,
    "samplingEnd"             VARCHAR(1000) NULL,
    "isGroup"                 INT          DEFAULT 1
                                          NOT NULL,
    "reserveNums"             INT          DEFAULT 0
        NULL,
    "analyseNums"             INT          DEFAULT 0
        NULL,
    "pretreatmentMethod"      VARCHAR(200) NULL,
    "sampleVolume"            VARCHAR(200) NULL,
    "containerStatus"         INT          DEFAULT 0
                                          NOT NULL,
    "isDeleted"               BIT          DEFAULT 0
                                          NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLEGROUP2TEST"
(
    "id"                   VARCHAR(50) NOT NULL,
    "sampleGroupId"        VARCHAR(50) NULL,
    "testId"               VARCHAR(50) NULL,
    "analyzeItemId"        VARCHAR(50)  DEFAULT ''
                                       NOT NULL,
    "redAnalyzeMethodName" VARCHAR(255) DEFAULT ''
        NULL,
    "redCountryStandard"   VARCHAR(100) DEFAULT ''
        NULL
);
CREATE TABLE "TB_PRO_SAMPLEJUDGEDATA"
(
    "id"             VARCHAR(50) NOT NULL,
    "sampleId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "testId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "judgingMethod"  INT NULL,
    "compareType"    INT NULL,
    "checkType"      INT NULL,
    "onlineValue"    VARCHAR(50)  DEFAULT ''
                                 NOT NULL,
    "expectedValue"  VARCHAR(50)  DEFAULT ''
                                 NOT NULL,
    "qcRateValue"    VARCHAR(255) NULL,
    "pass"           VARCHAR(50) NULL,
    "dimensionId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "checkItemValue" VARCHAR(50) NULL,
    "allowLimit"     VARCHAR(50) NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "dataStatus"     VARCHAR(50)  DEFAULT ''
        NULL,
    "standardCode"   VARCHAR(50)  DEFAULT ''
        NULL,
    "testTimeStr"    VARCHAR(50)  DEFAULT ''
        NULL,
    "isNotEvaluate"  BIT          DEFAULT 0
                                 NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLEPREPARATION"
(
    "id"                   VARCHAR(50)  DEFAULT ''
        NOT NULL,
    "sampleId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "analyzeItemNames"     VARCHAR(100) NULL,
    "preparationBeginTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "preparationEndTime"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "preparedPersonId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "preparedPersonName"   VARCHAR(50) NULL,
    "method"               VARCHAR(200) DEFAULT ''
        NOT NULL,
    "content"              VARCHAR(500) NULL,
    "orgId"                VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "creator"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "createDate"           TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "modifier"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NOT NULL,
    "modifyDate"           TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "instrumentId"         VARCHAR(1000) NULL,
    "isDeleted"            BIT          DEFAULT 0
        NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLERESERVE"
(
    "id"              VARCHAR(50) NOT NULL,
    "sampleId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "sampleGroupId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "reserveDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "reservePersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "isDeleted"       BIT          DEFAULT 0
                                  NOT NULL,
    "reserveType"     INT          DEFAULT 0
                                  NOT NULL,
    "disposeMethod"   VARCHAR(100) NULL,
    "remark"          VARCHAR(100) NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLERESERVE2TEST"
(
    "id"                 VARCHAR(50) NOT NULL,
    "reserveId"          VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "analyzeItemId"      VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "redAnalyseItemName" VARCHAR(50) NULL
);
CREATE TABLE "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"
(
    "id"         VARCHAR(50) NOT NULL,
    "personId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"
(
    "id"                VARCHAR(50)               NOT NULL,
    "achievementId"     VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "sampleId"          VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "sampleCode"        VARCHAR(50)    DEFAULT ''
        NULL,
    "sampleFolderId"    VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "sampleFolderName"  VARCHAR(100)   DEFAULT ''
        NULL,
    "sampleTypeId"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "receiveId"         VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "recordCode"        VARCHAR(50)    DEFAULT ''
        NULL,
    "samplingPersonIds" TEXT NULL,
    "samplingTime"      TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'
                                                  NOT NULL,
    "totalAmount"       DECIMAL(18, 2) DEFAULT 0. NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLINGARRANGE"
(
    "id"                VARCHAR(50) NOT NULL,
    "planSamplingTime"  TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "team"              VARCHAR(50) NULL,
    "teamId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "chargePersonId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "chargePerson"      VARCHAR(50)  DEFAULT ''
                                    NOT NULL,
    "samplingPeople"    VARCHAR(255) NULL,
    "samplingPeopleIds" VARCHAR(1000) NULL,
    "car"               VARCHAR(50) NULL,
    "carId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "projectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "sampleFolderId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "periodCount"       INT          DEFAULT 0
                                    NOT NULL,
    "sampleCount"       INT          DEFAULT 0
                                    NOT NULL,
    "isArrange"         BIT          DEFAULT 0
                                    NOT NULL,
    "sampleTypeId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLINGCARCONFIG"
(
    "id"         VARCHAR(50) NOT NULL,
    "objectId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "objectType" INT         DEFAULT (-1)
                             NOT NULL,
    "carId"      VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "driverId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "orgId"      VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLINGFREQUENCY"
(
    "id"             VARCHAR(50) NOT NULL,
    "sampleFolderId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "periodCount"    INT         DEFAULT 0
                                 NOT NULL,
    "timePerPeriod"  INT         DEFAULT 0
                                 NOT NULL,
    "folderType"     INT         DEFAULT 0
                                 NOT NULL,
    "samplePerTime"  INT         DEFAULT 1
                                 NOT NULL,
    "orgId"          VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLINGFREQUENCYTEMP"
(
    "id"                  VARCHAR(50) NOT NULL,
    "sampleFolderTempId"  VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "samplingFrequencyId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "periodCount"         INT         DEFAULT 0
                                      NOT NULL,
    "timePerPeriod"       INT         DEFAULT 0
                                      NOT NULL,
    "operateType"         INT         DEFAULT 0
                                      NOT NULL,
    "samplePerTime"       INT         DEFAULT 1
                                      NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLINGFREQUENCYTEST"
(
    "id"                   VARCHAR(50) NOT NULL,
    "samplingFrequencyId"  VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "sampleFolderId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "testId"               VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "analyseItemId"        VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "analyzeMethodId"      VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "redAnalyzeItemName"   VARCHAR(100) NULL,
    "redAnalyzeMethodName" VARCHAR(255) NULL,
    "redCountryStandard"   VARCHAR(255) NULL,
    "isCompleteField"      BIT         DEFAULT 0
                                       NOT NULL,
    "isOutsourcing"        BIT         DEFAULT 0
                                       NOT NULL,
    "orgId"                VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "isSamplingOut"        BIT         DEFAULT 0
                                       NOT NULL
);
CREATE TABLE "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"
(
    "id"                      VARCHAR(50) NOT NULL,
    "samplingFrequencyTempId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "samplingFrequencyTestId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "operateType"             INT          DEFAULT 0
                                          NOT NULL,
    "testId"                  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "isSamplingOut"           BIT          DEFAULT 0
                                          NOT NULL,
    "analyseItemId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "analyzeMethodId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "redAnalyzeItemName"      VARCHAR(100) DEFAULT ''
        NULL,
    "redAnalyzeMethodName"    VARCHAR(255) DEFAULT ''
        NULL,
    "redCountryStandard"      VARCHAR(255) DEFAULT ''
        NULL,
    "isCompleteField"         BIT          DEFAULT 0
                                          NOT NULL,
    "isOutsourcing"           BIT          DEFAULT 0
                                          NOT NULL
);
ALTER TABLE "TB_PRO_PROJECT2FIXEDPROPERTY"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PROJECT2REPORT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PROJECT2WORKSHEETFOLDER"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PROJECTAPPROVAL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PROJECTPLAN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PROJECTPLAN2PERSON"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PROJECTPUSHLOG"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_QCDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_QCRESULTEVALUATION"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_QUALITYCONTROL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_QUALITYCONTROLEVALUATE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_QUALITYMANAGE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_QUOTATIONDETAIL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_QUOTATIONDETAIL2TEST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_RECEIVESAMPLERECORD"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_RECEIVESUBSAMPLERECORD"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTACHIEVEMENT2PERSON"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTACHIEVEMENTDETAILS"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTBASEINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTDETAIL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTFOLDERINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTFOLDERSORTINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTNUMBERPOOL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTRECOVER"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTRECOVER2REPORT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_REPORTSAMPLEINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLEDISPOSE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLEDISPOSE2TEST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLEFOLDER"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLEFOLDEREVALUATE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLEFOLDERTEMPLATE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLEGROUP"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLEGROUP2TEST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLEJUDGEDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLEPREPARATION"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLERESERVE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLERESERVE2TEST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLINGARRANGE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLINGCARCONFIG"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLINGFREQUENCY"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLINGFREQUENCYTEMP"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLINGFREQUENCYTEST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"
    ADD CONSTRAINT PRIMARY KEY ("id");

CREATE INDEX "IX_TB_PRO_QualityControl"
    ON "TB_PRO_QUALITYCONTROL" ("orgId", "associateSampleId", "qcGrade", "qcType", "isDeleted");

CREATE INDEX "IX_TB_PRO_QualityControlEvaluate"
    ON "TB_PRO_QUALITYCONTROLEVALUATE" ("objectId", "isDeleted");

CREATE INDEX "IX_TB_PRO_ReceiveSampleRecord"
    ON "TB_PRO_RECEIVESAMPLERECORD" ("orgId", "sendTime", "projectId");

CREATE INDEX "IX_TB_PRO_ReceiveSubSampleRecord"
    ON "TB_PRO_RECEIVESUBSAMPLERECORD" ("receiveId", "orgId");

CREATE INDEX "IX_TB_PRO_ReceiveSubSampleRecord2Sample"
    ON "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE" ("receiveSubSampleRecordId", "sampleId", "isDeleted");

CREATE INDEX "IX_TB_PRO_ReportDetail"
    ON "TB_PRO_REPORTDETAIL" ("objectId", "reportId", "orgId");

CREATE INDEX "INDEX_SamplePrepared"
    ON "TB_PRO_SAMPLE" ("code", "isDeleted", "orgId", "preparedStatus", "sampleTypeId", "redFolderName");

CREATE INDEX "IX_TB_PRO_Sample"
    ON "TB_PRO_SAMPLE" ("orgId", "samplingTimeBegin", "receiveId", "isDeleted", "code");

CREATE INDEX "IX_TB_PRO_Sample2"
    ON "TB_PRO_SAMPLE" ("orgId", "isDeleted", "projectId", "sampleTypeId", "code", "redFolderName", "inspectedEnt");

CREATE INDEX "IX_TB_PRO_Sample3"
    ON "TB_PRO_SAMPLE" ("orgId", "isDeleted", "receiveId", "sampleTypeId", "sampleFolderId");

CREATE INDEX "IX_TB_PRO_Sample4"
    ON "TB_PRO_SAMPLE" ("receiveId", "orgId", "isDeleted");

CREATE INDEX "IX_TB_PRO_Sample5"
    ON "TB_PRO_SAMPLE" ("associateSampleId", "orgId", "isDeleted");

CREATE INDEX "IX_TB_PRO_Sample6"
    ON "TB_PRO_SAMPLE" ("projectId", "orgId", "isDeleted");

CREATE INDEX "IX_TB_PRO_Sample7"
    ON "TB_PRO_SAMPLE" ("samplingFrequencyId", "orgId", "isDeleted");

CREATE INDEX "IX_TB_PRO_Sample8"
    ON "TB_PRO_SAMPLE" ("isDeleted", "code", "inspectedEnt");

CREATE INDEX "IX_TB_PRO_Sample9"
    ON "TB_PRO_SAMPLE" ("orgId", "isDeleted", "receiveId", "sampleTypeId", "samplingTimeBegin");

CREATE INDEX "IX_TB_PRO_SampleFolder"
    ON "TB_PRO_SAMPLEFOLDER" ("orgId", "projectId", "sampleTypeId");

CREATE INDEX "IX_TB_PRO_SampleGroup"
    ON "TB_PRO_SAMPLEGROUP" ("receiveId", "sampleId", "isDeleted");

CREATE INDEX "IX_TB_PRO_SamplingFrequency"
    ON "TB_PRO_SAMPLINGFREQUENCY" ("orgId", "sampleFolderId");

CREATE INDEX "IX_TB_PRO_SamplingFrequencyTest"
    ON "TB_PRO_SAMPLINGFREQUENCYTEST" ("orgId", "sampleFolderId", "samplingFrequencyId");

CREATE INDEX "IX_TB_PRO_SamplingFrequencyTest2"
    ON "TB_PRO_SAMPLINGFREQUENCYTEST" ("orgId", "samplingFrequencyId");

CREATE INDEX "IX_TB_PRO_SamplingFrequencyTest3"
    ON "TB_PRO_SAMPLINGFREQUENCYTEST" ("orgId", "sampleFolderId");

ALTER TABLE "TB_PRO_PROJECTPLAN"
    ADD CONSTRAINT "UIX_TB_PRO_ProjectPlan" UNIQUE ("orgId", "projectId");

COMMENT
ON TABLE "TB_PRO_PROJECT2FIXEDPROPERTY" IS '例行监测、污染源任务下达的断面属性';

COMMENT
ON COLUMN "TB_PRO_PROJECT2FIXEDPROPERTY"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2FIXEDPROPERTY"."fixedPropertyId" IS '断面属性id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2FIXEDPROPERTY"."id" IS '主键';

COMMENT
ON TABLE "TB_PRO_PROJECT2REPORT" IS '项目和报告关联表';

COMMENT
ON COLUMN "TB_PRO_PROJECT2REPORT"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2REPORT"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2REPORT"."reportId" IS '报告id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2REPORT"."uploadStatus" IS '上传状态 0 : 未上传 1：已上传';

COMMENT
ON TABLE "TB_PRO_PROJECT2WORKSHEETFOLDER" IS '项目与检测单的关联表';

COMMENT
ON COLUMN "TB_PRO_PROJECT2WORKSHEETFOLDER"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_PROJECT2WORKSHEETFOLDER"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2WORKSHEETFOLDER"."workSheetFolderId" IS '检测单id';

COMMENT
ON COLUMN "TB_PRO_PROJECTAPPROVAL"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PROJECTAPPROVAL"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_PROJECTAPPROVAL"."approveDate" IS '申请日期';

COMMENT
ON COLUMN "TB_PRO_PROJECTAPPROVAL"."modifyStatus" IS '修改状态';

COMMENT
ON COLUMN "TB_PRO_PROJECTAPPROVAL"."approvePersonId" IS '审核人员';

COMMENT
ON COLUMN "TB_PRO_PROJECTAPPROVAL"."comment" IS '意见';

COMMENT
ON TABLE "TB_PRO_PROJECTPLAN2PERSON" IS '项目计划与考核人员(现场质控）关联表';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN2PERSON"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN2PERSON"."projectPlanId" IS '项目计划id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN2PERSON"."assessPersonId" IS '考核人员id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPUSHLOG"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_PROJECTPUSHLOG"."title" IS '标题';

COMMENT
ON COLUMN "TB_PRO_PROJECTPUSHLOG"."message" IS '信息';

COMMENT
ON COLUMN "TB_PRO_PROJECTPUSHLOG"."url" IS '地址';

COMMENT
ON COLUMN "TB_PRO_PROJECTPUSHLOG"."personId" IS '人员id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPUSHLOG"."pushTime" IS '推送时间';

COMMENT
ON COLUMN "TB_PRO_PROJECTPUSHLOG"."pushStatus" IS '推送状态';

COMMENT
ON COLUMN "TB_PRO_PROJECTPUSHLOG"."pushResponse" IS '推送返回信息';

COMMENT
ON COLUMN "TB_PRO_PROJECTPUSHLOG"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."userId" IS '用户id';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."testId" IS '测试id';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."lastTime" IS '最新配置时间';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."stdValue" IS '方差值';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."avgValue" IS '平均值';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."upAssist" IS '上辅助线';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."downAssist" IS '下辅助线';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."upWarning" IS '上警告线';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."downWarning" IS '下警告线';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."upControl" IS '上控制线';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."downControl" IS '下控制线';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."dataType" IS '数据类型(枚举EnumDataType：1.空白 2.加标)';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."dataStr" IS '数据字符串';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_QCDATA"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_QCRESULTEVALUATION"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_QCRESULTEVALUATION"."projectId" IS '项目Id';

COMMENT
ON COLUMN "TB_PRO_QCRESULTEVALUATION"."qcSituation" IS '质量控制情况';

COMMENT
ON COLUMN "TB_PRO_QCRESULTEVALUATION"."resultCompare" IS '结果比较';

COMMENT
ON COLUMN "TB_PRO_QCRESULTEVALUATION"."evaluationResult" IS '评价结果';

COMMENT
ON COLUMN "TB_PRO_QCRESULTEVALUATION"."createPerson" IS '评价人员';

COMMENT
ON COLUMN "TB_PRO_QCRESULTEVALUATION"."createTime" IS '评价时间';

COMMENT
ON COLUMN "TB_PRO_QCRESULTEVALUATION"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_QCRESULTEVALUATION"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_QUALITYCONTROL" IS '质控信息';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."associateSampleId" IS '关联样品Id（平行样的关联样品）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcGrade" IS '质控等级（枚举EnumQCGrade：0.外部质控  1.内部质控）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcType" IS '质控类型（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcValue" IS '质控值（标准样的值/加标的值/平行样为空）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcVolume" IS '加标体积';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qaId" IS '添加质控人员id';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcTime" IS '添加质控时间';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcTestValue" IS '测定值';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."realSampleTestValue" IS '样值（可以是质量的也可以是浓度的）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcCode" IS '标样编号';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcOriginValue" IS '原样的检测结果（找限值范围）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcValidDate" IS '标样的有效期';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcStandardDate" IS '标样的配置日期';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcStandardId" IS '标样Id';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcConcentration" IS '加标液浓度';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcVolumeDimensionId" IS '加标体积/标准溶液加入量量纲id';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcValueDimensionId" IS '加入标准量/标准物质加入量/替代物加入量量纲id';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcTestValueDimensionId" IS '测定值量纲id';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."realSampleTestValueDimensionId" IS '样值量纲id';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."qcConcentrationDimensionId" IS '加标液浓度量纲';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROL"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_QUALITYCONTROLEVALUATE" IS '质控评价信息表';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."objectId" IS '对象id（分析数据Id）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."qcId" IS '质控信息id';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."checkItem" IS '检查项（对应质控限值配置中的检查项，针对质控样，检查项默认“出证结果”）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."judgingMethod" IS '评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."isPass" IS '是否合格（是否合格判定不满足判定条件时为空）';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."checkItemValue" IS '检查项值';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."allowLimit" IS '允许限值';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."dimensionName" IS '量纲名称';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_QUALITYCONTROLEVALUATE"."limitId" IS '质控限制id';

COMMENT
ON TABLE "TB_PRO_QUALITYMANAGE" IS '质量管理';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."anaId" IS '关联样品数据id';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."qmType" IS '质控类型（枚举EnumQMType：1.标样，2.加标样，3.其他）';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."qmValue" IS '质控值（标准样的值/加标的值/其他为空）';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."qmRange" IS '范围';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."qmVolume" IS '加标体积';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."qmPersonId" IS '添加质控人员id';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."qmTime" IS '添加质控时间';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."qmTestValue" IS '测定值';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."stTestValue" IS '样值（可以是质量的也可以是浓度的）';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."qmCode" IS '标准编号';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."qmOriginValue" IS '原样的检测结果（找限值范围）';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."instrumentId" IS '仪器id';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."isMixedStandard" IS '是否混标';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."redAnalyzeItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."unitId" IS '计量单位id';

COMMENT
ON COLUMN "TB_PRO_QUALITYMANAGE"."unit" IS '计量单位';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."orderId" IS '订单id';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."quotationId" IS '总计id';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."sampleTypeId" IS '样品类型id';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."redAnalyseItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."analyseItemId" IS '分析项目id';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."redAnalyseMethod" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."analyseMethodId" IS '分析方法id';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."folderName" IS '点位名称';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."projectCount" IS '任务数';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."cycleOrder" IS '周期数';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."timesOrder" IS '批次数';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."sampleOrder" IS '总检数';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."inspectedCount" IS '已检数';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."residueCount" IS '剩检数';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."sampleCount" IS '样品数量';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."samplingPrice" IS '采样费';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."analysePrice" IS '分析费';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."charge" IS '小计';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."quotationPrice" IS '总价';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL"."isTotal" IS '是否总称';

COMMENT
ON TABLE "TB_PRO_QUOTATIONDETAIL2TEST" IS '费用详情与测试项目关系';

COMMENT
ON COLUMN "TB_PRO_QUOTATIONDETAIL2TEST"."id" IS '主键';

COMMENT
ON TABLE "TB_PRO_RECEIVESAMPLERECORD" IS '送样单';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."projectId" IS '项目ID';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."recordCode" IS '送样单号';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."samplingTime" IS '采样时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."sendTime" IS '送样时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."senderId" IS '送样人（内部人员）ID';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."senderName" IS '送样人（采样负责人）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."receiveTime" IS '登记时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."recorderId" IS '登记人id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."receiveType" IS '接样类型（枚举EnumReceiveType：1.内部 2.外部 3.现场）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."status" IS '送样单状态（字符串，枚举EnumReceiveRecordStatus： 1.新建 2.已经送样 6.待数据确认 14.已数据确认）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."receiveStatus" IS '送样单状态（ 用于判断，枚举EnumReceiveRecordStatus： 1.新建 2.已经送样 6.待数据确认 14.已数据确认）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."infoStatus" IS '信息状态（枚举EnumReceiveInfoStatus：1.信息登记中，2.信息复核中 3.信息审核中 4.已确认）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."reportCode" IS '分析数据报告编号';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."uploadStatus" IS '移动端状态(0：未提交，1：数据录入中，2：已数据同步)';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."uploadTime" IS '上传时间（移动端）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."validAnalyzeId" IS '有证采样人员（用于图片签名）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."checkerId" IS '复核人Id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."checkTime" IS '复核时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."validCheckerId" IS '有证复核人员id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."auditorId" IS '审核人Id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."auditTime" IS '审核时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."validAuditerId" IS '有证采样审核人员';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."backOpinion" IS '退回意见（最新一个）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."json" IS '冗余json信息';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."sortId" IS '排序id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."receiveSampleDate" IS '接样日期';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."recipientId" IS '接样人id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORD"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."templateId" IS '送样单参数模板id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."paramId" IS '参数id/测试项目id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."paramName" IS '参数名称/分析项目名称';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."orderNum" IS '排序值';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."type" IS '对象类型（枚举EnumParamsType：2.样品参数，4.点位参数，3.分析项目';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMINFO"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."receiveId" IS '送样单id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."templateName" IS '模板名称';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESAMPLERECORDPARAMTEMPLATE"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_RECEIVESUBSAMPLERECORD" IS '领样单（一张送样单按科室分成1张或多张领样单）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."receiveId" IS '送样单ID';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."projectId" IS '登记项目ID';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."code" IS '领样单编号';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."status" IS '领样单状态（枚举EnumReceiveSubRecordStatus：0.无状态 1.有实验室数据 2.有现场数据 4.已领取 8.已确认领样 16.已提交 32.已现场复核 64.已实验室复核 128.可确认 256.已确认））';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."subStatus" IS '领样单状态（按位与，枚举EnumReceiveSubRecordStatus：0.无状态 1.有实验室数据 2.有现场数据 4.已领取 8.已确认领样 16.已提交 32.已现场复核 64.已实验室复核 128.可确认 256.已确认）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."receivePersonId" IS '领样人id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."receiveName" IS '领取人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."receiveTime" IS '领取时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."checkerId" IS '现场数据复核人Id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."checkerName" IS '现场数据复核人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."checkTime" IS '现场数据复核时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."auditorId" IS '现场数据审核人Id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."auditorName" IS '现场数据审核人名称';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."auditTime" IS '现场数据审核时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."backOpinion" IS '退回意见（最新一个）';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."domainId" IS '所属科室Id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE" IS '领样单和样品关联表';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."receiveSubSampleRecordId" IS '领样单id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."sampleId" IS '样品id';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_RECEIVESUBSAMPLERECORD2SAMPLE"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_REPORT" IS '项目报告';

COMMENT
ON COLUMN "TB_PRO_REPORT"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_REPORT"."reportTypeId" IS '报告类型id';

COMMENT
ON COLUMN "TB_PRO_REPORT"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_REPORT"."status" IS '报告状态';

COMMENT
ON COLUMN "TB_PRO_REPORT"."dataChangeStatus" IS '数据变更状态( 枚举EnumReportChangeStatus1.未变更 2.已变更)（针对已经编制报告的数据修改状态-样品数据增删改）';

COMMENT
ON COLUMN "TB_PRO_REPORT"."code" IS '报告编号';

COMMENT
ON COLUMN "TB_PRO_REPORT"."folderName" IS '点位名称（单位名称）';

COMMENT
ON COLUMN "TB_PRO_REPORT"."sampleName" IS '样品名称';

COMMENT
ON COLUMN "TB_PRO_REPORT"."testName" IS '检验类别';

COMMENT
ON COLUMN "TB_PRO_REPORT"."createPersonId" IS '添加人员';

COMMENT
ON COLUMN "TB_PRO_REPORT"."createTime" IS '添加时间';

COMMENT
ON COLUMN "TB_PRO_REPORT"."reportYear" IS '报告年份';

COMMENT
ON COLUMN "TB_PRO_REPORT"."securityCode" IS '防伪码';

COMMENT
ON COLUMN "TB_PRO_REPORT"."certifiedPersonId" IS '报告持证人员id';

COMMENT
ON COLUMN "TB_PRO_REPORT"."reportNum" IS '报告份数';

COMMENT
ON COLUMN "TB_PRO_REPORT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_REPORT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_REPORT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_REPORT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_REPORT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_REPORT"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_REPORT"."grantStatus" IS '发放状态（未发放,已发放,已回收）';

COMMENT
ON COLUMN "TB_PRO_REPORT"."firstInstanceId" IS '一审人id';

COMMENT
ON COLUMN "TB_PRO_REPORT"."secondInstanceId" IS '二审人id';

COMMENT
ON COLUMN "TB_PRO_REPORT"."analyseItemSortId" IS '分析项目排序id';

COMMENT
ON COLUMN "TB_PRO_REPORT"."folderSortId" IS '点位排序id';

COMMENT
ON COLUMN "TB_PRO_REPORT"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENT2PERSON"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENT2PERSON"."personId" IS '人员id';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENT2PERSON"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENT2PERSON"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENT2PERSON"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENT2PERSON"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENT2PERSON"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENT2PERSON"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."achievementId" IS '绩效id';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."reportCode" IS '报告编号';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."reportId" IS '报告id';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."reportTypeId" IS '报告类型id';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."reportPersonId" IS '报告编制人id';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."reportTime" IS '编制时间';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."projectCode" IS '项目编号';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."projectName" IS '项目名称';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."entId" IS '委托方id';

COMMENT
ON COLUMN "TB_PRO_REPORTACHIEVEMENTDETAILS"."entName" IS '委托方名称';

COMMENT
ON TABLE "TB_PRO_REPORTBASEINFO" IS '电子报告基础信息表';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."reportId" IS '报告id';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."projectName" IS '项目名称';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."systemCode" IS '系统编号';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."inspectedEnt" IS '受检单位';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."inspectedAddress" IS '受检单位地址';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."customerName" IS '委托单位';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."customerAddress" IS '委托单位地址';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."reportDate" IS '报告日期';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."technicalRemark" IS '报告技术说明备注';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."resultEvaluation" IS '是否评价';

COMMENT
ON COLUMN "TB_PRO_REPORTBASEINFO"."testPurpose" IS '检测目的';

COMMENT
ON TABLE "TB_PRO_REPORTDETAIL" IS '项目报告详情关联的样品或者断面属性';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."reportId" IS '报告ID';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."objectId" IS '关联ID';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."objectType" IS '关联类型(枚举EnumReportDetailType 1.样品 2.断面属性)';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_REPORTDETAIL"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_REPORTFOLDERINFO" IS '电子报告点位信息表';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."reportId" IS '报告id';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."folderName" IS '点位名称';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."folderCode" IS '点位编码';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."folderRemark" IS '点位备注，多个用;隔开';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."folderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERINFO"."isDeleted" IS '假删字段';

COMMENT
ON TABLE "TB_PRO_REPORTFOLDERSORTINFO" IS '报告点位排序信息表';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."reportId" IS '报告id';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."folderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."orderNum" IS '点位排序值';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_REPORTFOLDERSORTINFO"."isDeleted" IS '假删字段';

COMMENT
ON TABLE "TB_PRO_REPORTNUMBERPOOL" IS '报告编号池';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."year" IS '报告年份';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."code" IS '报告编号';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."reportTypeId" IS '报告类型id';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."status" IS '编号状态 0:未使用 1:已使用 2: 已作废';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."usedDate" IS '使用日期';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."number" IS '编号内号码';

COMMENT
ON COLUMN "TB_PRO_REPORTNUMBERPOOL"."serialType" IS '流水号类型';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."recoverPerson" IS '回收人';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."recoverTime" IS '回收日期';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."recoverReason" IS '回收理由';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_REPORTRECOVER2REPORT" IS '回收报告关联';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER2REPORT"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER2REPORT"."recoverId" IS '回收id';

COMMENT
ON COLUMN "TB_PRO_REPORTRECOVER2REPORT"."reportId" IS '报告id';

COMMENT
ON TABLE "TB_PRO_REPORTSAMPLEINFO" IS '电子报告样品信息表';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."reportId" IS '报告id';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."sampleCode" IS '样品编号';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."sampleRemark" IS '样品备注，多个用;隔开';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."reportFolderInfoId" IS '报告点位信息id';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."sampleId" IS '样品id';

COMMENT
ON COLUMN "TB_PRO_REPORTSAMPLEINFO"."isDeleted" IS '假删字段';

COMMENT
ON TABLE "TB_PRO_SAMPLE" IS '样品';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."code" IS '样品编号';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."subProjectId" IS '子项目的id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."receiveId" IS '送样记录id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."sampleFolderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samplingFrequencyId" IS '频次id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."cycleOrder" IS '采样周期序数';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."timesOrder" IS '每周期次数序数';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."sampleOrder" IS '每次样品序数';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."redFolderName" IS '冗余-点位';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."redAnalyzeItems" IS '冗余-分析项目';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samplingPersonId" IS '采样人id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."sampleCategory" IS '样品类别（EnumSampleCategory：0.原样 1.质控样 2.串联样 3.原样加原样 4.比对样）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."inspectedEnt" IS '受检单位';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."inspectedEntId" IS '受检单位ID';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."inceptTime" IS '样品登记时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samplingTimeBegin" IS '采样开始时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samplingTimeEnd" IS '采样结束时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."status" IS '样品状态（过程状态，枚举字符串EnumSampleStatus）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samplingConfig" IS '采样分配状态（枚举EnumSamplingConfig：0.未分配 1.已分配）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samplingStatus" IS '采样状态（枚举EnumSamplingStatus：1.不需要取样 2.需要取样还未取样 4.采样中 8.已经完成取样）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."innerReceiveStatus" IS '领样状态（枚举EnumInnerReceiveStatus：1.不能领取 2.可以领取 6.已经领取 12.已确认领取）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."ananlyzeStatus" IS '分析状态（枚举EnumAnalyzeStatus：1.不需要分析 2.不能分析 4.可以分析 8.正在分析 16.分析完成）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."storeStatus" IS '存储状态（枚举EnumStoreStatus：1.不能存储 2.可以存储 4.已经存储 8.可以销毁 16.已经销毁 32.已经被提取）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."makeStatus" IS '制样状态（枚举EnumMakeStatus：1.不需要制样 2.需要制样还未制样 6.已经完成制样）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."dataChangeStatus" IS '数据变更状态（ 枚举EnumSampleChangeStatus：0.未变更 1.已变更）（针对已经编制报告的数据修改状态-样品数据增删改）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."customerCode" IS '客户样品编号';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."qcId" IS '质控id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."associateSampleId" IS '质控样的原样Id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."parentSampleId" IS '父级样品（从父级样品制样而来）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."makeSamPerId" IS '制样人id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."isPrint" IS '是否已打印（枚举EnumPrintStatus：0.未打印1.已打印）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."isKeep" IS '是否留样';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."keepLongTime" IS '样品保留天数';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."storageConditions" IS '保存条件';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."loseEfficacyTime" IS '样品过期时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."pack" IS '包装/规格';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."sampleWeight" IS '样品重量';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."weightOrQuantity" IS '样品数量';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samColor" IS '颜色';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."sampleExplain" IS '样品特征';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."volume" IS '样品体积(string)';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samplingPlace" IS '采样点位置';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samKind" IS '样品的性质（枚举EnumSampleKind：0.分析样 1.备样 2.分析后备样）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."isQualified" IS '国检_是否合格(检测项目是否有不合格项)';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."sampleSource" IS '国检_样品来源';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."originalStatus" IS '国检_到达实验室状态';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."isReturned" IS '国检_是否需要退还';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."preTreatmentCases" IS '前处理情况';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."unqualifiedReason" IS '不合格原因';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."disposeMeasure" IS '处理措施';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."consistencyValidStatus" IS '验证状态';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."lon" IS '经度（实际）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."lat" IS '纬度（实际）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."signerId" IS '签到人id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."signTime" IS '签到时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."samplingRecordId" IS '采样单Id（预留）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."isOutsourcing" IS '预留分包状态（枚举EnumOutSourcing：0.不分包1.全部分包2.部分分包）';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."lastNewSubmitTime" IS '最新一次检测单数据提交时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."preparedStatus" IS '制备状态';

COMMENT
ON COLUMN "TB_PRO_SAMPLE"."sortNum" IS '排序值';

COMMENT
ON TABLE "TB_PRO_SAMPLEDISPOSE" IS '留样信息';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."sampleId" IS '样品编号';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."sampleSource" IS '样品来源';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."sampleCount" IS '样品数量';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."reserveDate" IS '保留日期';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."reserveLocation" IS '保存位置';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."redAnalyzeItems" IS '分析项目名称，多个英文逗号间隔';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."disposePersonId" IS '处置人员id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."disposeDate" IS '处置日期';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."disposeSolution" IS '处置方式';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."disposeRemarks" IS '处置备注';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."domainId" IS '所属实验室id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."modifier" IS '最近修改人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."modifyDate" IS '最新修改时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."isDisposed" IS '是否处置';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE"."saveCondition" IS '保存条件';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE2TEST"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE2TEST"."sampleDisposeId" IS '留样处置id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE2TEST"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEDISPOSE2TEST"."redAnalyzeItemName" IS '分析项目名称';

COMMENT
ON TABLE "TB_PRO_SAMPLEFOLDER" IS '点位信息表';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."subProjectId" IS '子项目的id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."watchSpot" IS '点位名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."fixedPointId" IS '断面id（断面扩展id）';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."folderCode" IS '点位号';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."chargeRate" IS '费用系数（默认1）';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."folderTypeId" IS '点位类型（常量 PRO_FolderType：进口、出口）';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."folderTypeName" IS '点位类型名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."redAnalyzeItems" IS '分析项目';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."lon" IS '经度（计划）';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."lat" IS '纬度（计划）';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."grade" IS '地图级别（默认1）';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."exhaustPipeHeight" IS '排气管高度';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."isOutsourcing" IS ' 预留分包状态（枚举EnumOutSourcing：0.不分包1.全部分包2.部分分包）';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."craftFacilityName" IS '工艺设备名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."purificateFacilityName" IS '净化设备名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."pollutionType" IS '污染源种类';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."boilerMakeUnit" IS '锅炉制造单位';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."equipmentTypeName" IS '名称(型号)';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."boilerUseDate" IS '锅炉投运日期';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."chimneyHeight" IS '烟囱高度';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."purificateFacilityUnit" IS '净化设备制造单位';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."purificateFacilityType" IS '净化设备型号';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."purificateFacilityUseDate" IS '净化设备投运日期';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."fuelType" IS '燃料类型';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."stoveFacilityCode" IS '炉窖设备编号';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."craftFacilityUseDate" IS '工艺设备/启用时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."isTransition" IS '是否进行折算';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."inspectedEntId" IS '受检单位ID';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."inspectedEnt" IS '受检单位';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDER"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDEREVALUATE"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDEREVALUATE"."sampleFolderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDEREVALUATE"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDEREVALUATE"."qcRateValue" IS '判定结果';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDEREVALUATE"."folderPass" IS '是否合格';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDEREVALUATE"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDEREVALUATE"."resultEvaluate" IS '结果评价';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDERTEMPLATE"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDERTEMPLATE"."approveId" IS '项目方案变更id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDERTEMPLATE"."sampleFolderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDERTEMPLATE"."operateType" IS '操作类型';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDERTEMPLATE"."watchSpot" IS '点位名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDERTEMPLATE"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDERTEMPLATE"."redAnalyzeItems" IS '分析项目';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDERTEMPLATE"."lon" IS '经度';

COMMENT
ON COLUMN "TB_PRO_SAMPLEFOLDERTEMPLATE"."lat" IS '纬度';

COMMENT
ON TABLE "TB_PRO_SAMPLEGROUP" IS '样品分组表';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."receiveId" IS '送样单id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."sampleId" IS '样品id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."sampleTypeGroupId" IS '样品分组id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."sampleTypeGroupName" IS '样品分组名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."analyseItemNames" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."hasScanned" IS '是否已扫码';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."scannedTime" IS '扫码时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."scanner" IS '扫码人id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."fixer" IS '固定剂';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."containerName" IS '容器名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."saveCondition" IS '保存条件';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."sampleStatus" IS '样品状态';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."riskDescription" IS '危险性描述';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."transportationCondition" IS '运输条件';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."samplingBegin" IS '采样开始时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."samplingEnd" IS '采样结束时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."isGroup" IS '分组标识 1:按分组  2:全因子  3:单因子';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."reserveNums" IS '领取数量';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."analyseNums" IS '分析项目数量';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."pretreatmentMethod" IS '前处理方式';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."sampleVolume" IS '采样体积';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."containerStatus" IS '采样容器状态 EnumContainerStatus 1.完好无损 2.破损';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP2TEST"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP2TEST"."sampleGroupId" IS '样品分组id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP2TEST"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP2TEST"."analyzeItemId" IS '分析项目Id（Guid）';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP2TEST"."redAnalyzeMethodName" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEGROUP2TEST"."redCountryStandard" IS '国家标准';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."sampleId" IS '样品id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."judgingMethod" IS '评判方式';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."compareType" IS '比对类型';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."checkType" IS '检测类型（0-废水比对，1-废气比对）';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."onlineValue" IS '在线值';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."expectedValue" IS '理论值';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."qcRateValue" IS '判定结果';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."pass" IS '是否合格';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."dimensionId" IS '量纲id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."checkItemValue" IS '检查项值';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."allowLimit" IS '允许限值';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."orgId" IS '机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."dataStatus" IS '数据状态';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."standardCode" IS '标样编号';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."testTimeStr" IS '监测时间/段';

COMMENT
ON COLUMN "TB_PRO_SAMPLEJUDGEDATA"."isNotEvaluate" IS '是否不参与评价';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."id" IS '标识';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."sampleId" IS '样品id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."analyzeItemNames" IS '制备的样品下的分析项目名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."preparationBeginTime" IS '制备开始时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."preparationEndTime" IS '制备结束时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."preparedPersonId" IS '制备人id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."preparedPersonName" IS '制备人名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."method" IS '制备方法';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."content" IS '制备内容';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."instrumentId" IS '仪器id，多个id用;隔开';

COMMENT
ON COLUMN "TB_PRO_SAMPLEPREPARATION"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."sampleId" IS '样品编号';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."sampleGroupId" IS '样品分组主键id';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."reserveDate" IS '操作日期';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."reservePersonId" IS '操作人员id';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."reserveType" IS '类型';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."disposeMethod" IS '处置方式（1.领取，2.处置）';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."domainId" IS '所属实验室id';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."modifier" IS '最近修改人';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE"."modifyDate" IS '最新修改时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE2TEST"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE2TEST"."reserveId" IS '领取数据标识';

COMMENT
ON COLUMN "TB_PRO_SAMPLERESERVE2TEST"."redAnalyseItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"."personId" IS '人员id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENT2PERSON"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."achievementId" IS '绩效id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."sampleId" IS '样品id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."sampleCode" IS '样品编号';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."sampleFolderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."sampleFolderName" IS '点位名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."receiveId" IS '送样单id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."recordCode" IS '送样单号';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."samplingPersonIds" IS '采样人id,逗号隔开';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."samplingTime" IS '采样时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGACHIEVEMENTDETAILS"."totalAmount" IS '产值';

COMMENT
ON TABLE "TB_PRO_SAMPLINGARRANGE" IS '采样安排计划表';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."planSamplingTime" IS '计划采样时间';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."team" IS '采样小组';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."teamId" IS '采样小组标识';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."chargePersonId" IS '采样负责人标识';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."chargePerson" IS '采样负责人';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."samplingPeople" IS '采样人员';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."samplingPeopleIds" IS '采样人员标识 ; 分割';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."car" IS '采样车辆';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."carId" IS '采样车辆标识';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."sampleFolderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."periodCount" IS '周期';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."sampleCount" IS '采样样品数';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."isArrange" IS '计划安排状态 0否 1是';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGARRANGE"."sampleTypeId" IS '检测类型id';

COMMENT
ON TABLE "TB_PRO_SAMPLINGCARCONFIG" IS '采样的车辆分配';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGCARCONFIG"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGCARCONFIG"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGCARCONFIG"."objectType" IS '对象类型(枚举EnumSamplingCarType：0.任务1.送样单)';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGCARCONFIG"."carId" IS '车辆的id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGCARCONFIG"."driverId" IS '驾驶员id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGCARCONFIG"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_SAMPLINGFREQUENCY" IS '点位频次';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCY"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCY"."sampleFolderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCY"."periodCount" IS '周期';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCY"."timePerPeriod" IS '次数';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCY"."folderType" IS '点位类型（枚举EnumFolderType：0.无类型 1.昼间 2.夜间）';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCY"."samplePerTime" IS '每次样品数';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCY"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEMP"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEMP"."sampleFolderTempId" IS '点位修改方案id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEMP"."samplingFrequencyId" IS '点位频次id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEMP"."periodCount" IS '周期';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEMP"."timePerPeriod" IS '次数';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEMP"."operateType" IS '操作类型';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEMP"."samplePerTime" IS '样品数';

COMMENT
ON TABLE "TB_PRO_SAMPLINGFREQUENCYTEST" IS '点位频次所做的测试';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."samplingFrequencyId" IS '点位频次id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."sampleFolderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."testId" IS '测试id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."analyseItemId" IS '分析项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."analyzeMethodId" IS '分析方法id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."redAnalyzeItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."redAnalyzeMethodName" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTEST"."isSamplingOut" IS '是否采样分包';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."samplingFrequencyTempId" IS '点位频次修改方案id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."samplingFrequencyTestId" IS '点位频次测试项目关联表id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."operateType" IS '操作类型';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."isSamplingOut" IS '是否采样分包';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."analyseItemId" IS '分析项目id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."analyzeMethodId" IS '分析方法id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."redAnalyzeItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."redAnalyzeMethodName" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."redCountryStandard" IS '国家标准';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."isCompleteField" IS '是否现场数据';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGFREQUENCYTESTTEMP"."isOutsourcing" IS '是否分包';

CREATE TABLE "TB_PRO_ANALYSEACHIEVEMENT2PERSON"
(
    "id"         VARCHAR(50) NOT NULL,
    "personId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_PRO_ANALYSEACHIEVEMENTDETAILS"
(
    "id"                VARCHAR(50)               NOT NULL,
    "achievementId"     VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "sampleId"          VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "sampleCode"        VARCHAR(50)    DEFAULT ''
        NULL,
    "testId"            VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "analyzeItemName"   VARCHAR(100) NULL,
    "analyzeMethodName" VARCHAR(255) NULL,
    "countryStandard"   VARCHAR(100)   DEFAULT ''
        NULL,
    "analystId"         VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "analystName"       VARCHAR(50)    DEFAULT ''
        NULL,
    "analyzeTime"       TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'
                                                  NOT NULL,
    "sampleTypeId"      VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "totalAmount"       DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "status"            VARCHAR(50)    DEFAULT ''
        NULL
);
CREATE TABLE "TB_PRO_ANALYSEBIOLOGYDATA"
(
    "id"            VARCHAR(50)     NOT NULL,
    "parentId"      VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "analyseDataId" VARCHAR(50)     NOT NULL,
    "taxonomyId"    VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "testValue"     VARCHAR(100) NULL,
    "testValueD"    DECIMAL(20, 10) NOT NULL,
    "dimension"     VARCHAR(50) NULL,
    "dimensionId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "volume"        VARCHAR(50) NULL,
    "countValue"    INT         DEFAULT 0
                                    NOT NULL,
    "times"         INT         DEFAULT 0
                                    NOT NULL,
    "orgId"         VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL
);



