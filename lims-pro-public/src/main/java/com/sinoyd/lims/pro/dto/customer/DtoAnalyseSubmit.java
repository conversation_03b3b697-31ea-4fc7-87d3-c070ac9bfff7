package com.sinoyd.lims.pro.dto.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 分析数据的提交
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoAnalyseSubmit {

    /**
     * 检测单数据
     */
    private DtoWorkSheetFolder workSheetFolder = new DtoWorkSheetFolder();

    /**
     * 核对是否填写了仪器记录
     */
    private Boolean isCheckInstrumentUseRecord = true;

    /**
     * 提交的数据信息
     */
    private List<DtoAnalyseDataProperty> workSheetProperties = new ArrayList<>();

    /**
     * 分析时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date analyzeTime;
}
