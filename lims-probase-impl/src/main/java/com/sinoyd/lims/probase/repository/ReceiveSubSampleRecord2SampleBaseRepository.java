package com.sinoyd.lims.probase.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord2Sample;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface ReceiveSubSampleRecord2SampleBaseRepository extends IBaseJpaPhysicalDeleteRepository<DtoReceiveSubSampleRecord2Sample, String> {

    @Transactional
    @Modifying
    @Query("update DtoReceiveSubSampleRecord2Sample as a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.receiveSubSampleRecordId=:receiveSubSampleRecordId ")
    Integer deleteByReceiveSubSampleRecordId(@Param("receiveSubSampleRecordId") String receiveSubSampleRecordId,
                                             @Param("modifier") String modifier,
                                             @Param("modifyDate") Date modifyDate);
    List<DtoReceiveSubSampleRecord2Sample> findByReceiveSubSampleRecordIdIn(List<String> subIds);

    @Transactional
    @Modifying
    @Query("update DtoReceiveSubSampleRecord2Sample as a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.receiveSubSampleRecordId=:receiveSubSampleRecordId and a.sampleId in :sampleIds ")
    Integer deleteByReceiveSubSampleRecordIdAndSampleIds(@Param("receiveSubSampleRecordId") String receiveSubSampleRecordId,
                                                         @Param("sampleIds") List<String> sampleIds,
                                                         @Param("modifier") String modifier,
                                                         @Param("modifyDate") Date modifyDate);
}
