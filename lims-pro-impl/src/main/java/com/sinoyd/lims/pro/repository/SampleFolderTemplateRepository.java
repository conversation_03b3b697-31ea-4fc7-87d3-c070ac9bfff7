package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSampleFolderTemplate;

import java.util.List;

/**
 *  SampleFolderTemplateRepository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/08/02
 */
public interface SampleFolderTemplateRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleFolderTemplate, String> {

    /**
     * 根据项目id查询数据
     *
     * @param approveId 项目id
     * @return List<DtoSampleFolderTemplate>
     */
    List<DtoSampleFolderTemplate> findByApproveId(String approveId);

    /**
     * 根据项目id查询数据
     *
     * @param approveIds 项目id
     * @return List<DtoSampleFolderTemplate>
     */
    List<DtoSampleFolderTemplate> findByApproveIdIn(List<String> approveIds);

}
