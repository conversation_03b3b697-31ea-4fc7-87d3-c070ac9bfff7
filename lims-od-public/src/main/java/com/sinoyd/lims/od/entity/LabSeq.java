package com.sinoyd.lims.od.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * LabSeq实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "LabSeq")
@Data
@EntityListeners(AuditingEntityListener.class)
public class LabSeq implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public LabSeq() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 实验组id
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("实验组id")
    private String groupId;

    /**
     * 序号(第几组实验)
     */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("序号(第几组实验)")
    private Integer sn;

    /**
     * 稀释倍数
     */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("稀释倍数")
    private Integer dilutionRate;

    /**
     * 实验状态，枚举管理
     */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("实验状态，枚举管理")
    private Integer labState;

    /**
     * 假删标识
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删标识")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}
