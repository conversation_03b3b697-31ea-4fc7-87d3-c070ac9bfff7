package com.sinoyd.lims.api.dto;

import lombok.Data;

import java.util.Date;

/**
 * 仪器出库扫码实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/2/2
 */
@Data
public class DtoInstrumentOutPhone {

    /**
     * 仪器id
     */
    private String id;

    /**
     * 仪器编号
     */
    private String instrumentsCode;


    /**
     * 规格型号
     */
    private String model;


    /**
     * 制造厂商
     */
    private String factoryName;


    /**
     * 溯源结果(枚举：EnumOriginResult：1合格、0不合格)
     */
    private Integer originResult;


    /**
     * 有效期
     */
    private String originEndDate;

    /**
     * 仪器状态
     */
    private Integer state;

    /**
     * 仪器名称
     */
    private String instrumentName;
}
