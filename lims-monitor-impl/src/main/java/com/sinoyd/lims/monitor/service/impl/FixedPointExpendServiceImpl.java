package com.sinoyd.lims.monitor.service.impl;


import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPointExpend;
import com.sinoyd.lims.monitor.repository.rcc.FixedPointExpendRepository;
import com.sinoyd.lims.monitor.service.FixedPointExpendService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * FixedPointExpend操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class FixedPointExpendServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFixedPointExpend, String, FixedPointExpendRepository> implements FixedPointExpendService {

    @Override
    public void findByPage(PageBean<DtoFixedPointExpend> pb, BaseCriteria fixedPointExpendCriteria) {
        pb.setEntityName("DtoFixedPointExpend a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fixedPointExpendCriteria);
    }

    @Override
    public List<DtoFixedPointExpend> findByFixedPointIds(List<String> fixedPointIds) {
        return repository.findByFixedPointIdIn(fixedPointIds);
    }
}