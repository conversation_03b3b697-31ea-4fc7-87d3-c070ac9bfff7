<configs>

    <config>
        <code>projectQRCode</code>
        <name>项目二维码附件</name>
        <className>com.sinoyd.lims.pro.service.ProjectService</className>
        <method>findAttachPath</method>
        <placeholder>projectCode</placeholder>
        <path>PRO/ProjectDoc/{projectCode}/Code</path>
    </config>
    <config>
        <code>projectRegister</code>
        <name>项目登记附件</name>
        <className>com.sinoyd.lims.pro.service.ProjectService</className>
        <method>findAttachPath</method>
        <placeholder>projectCode</placeholder>
        <path>PRO/ProjectDoc/{projectCode}/Registration</path>
    </config>
    <config>
        <code>projectContract</code>
        <name>项目合同相关文档</name>
        <className>com.sinoyd.lims.pro.service.ProjectService</className>
        <method>findAttachPath</method>
        <placeholder>projectCode</placeholder>
        <path>PRO/ProjectDoc/{projectCode}/Contract/Files</path>
    </config>
    <config>
        <code>projectCharge</code>
        <name>费用附件</name>
        <className>com.sinoyd.lims.pro.service.ProjectService</className>
        <method>findAttachPath</method>
        <placeholder>projectCode</placeholder>
        <path>PRO/ProjectDoc/{projectCode}/Charge</path>
    </config>
    <config>
        <code>receiveSampleRecord</code>
        <name>送样单采样单</name>
        <className>com.sinoyd.lims.pro.service.ReceiveSampleRecordService</className>
        <method>findAttachPath</method>
        <placeholder>recordCode</placeholder>
        <path>PRO/ReceiveSampleRecord/{recordCode}/Recdoc</path>
    </config>
    <config>
        <code>receiveSampleRecordPhotos</code>
        <name>送样单照片</name>
        <className>com.sinoyd.lims.pro.service.ReceiveSampleRecordService</className>
        <method>findAttachPath</method>
        <placeholder>recordCode</placeholder>
        <path>PRO/ReceiveSampleRecord/{recordCode}/RecPhotos</path>
    </config>
    <config>
        <code>smplingRecord</code>
        <name>采样单</name>
        <className>com.sinoyd.lims.pro.service.xxx类</className>
        <method>findAttachPath</method>
        <placeholder>samplingRecordTypeName,recordCode</placeholder>
        <path>PRO/SamplingRecord/Platform/{samplingRecordTypeName}/{recordCode}/Files</path>
    </config>
    <config>
        <code>smplingRecordAttachment</code>
        <name>采样单手机上传附件</name>
        <className>com.sinoyd.lims.pro.service.ReceiveSampleRecordService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/SamplingRecord/MobileRecord/{id}</path>
    </config>
    <config>
        <code>workSheetOriginRecord</code>
        <name>检测单原始记录单附件</name>
        <className>com.sinoyd.lims.pro.service.WorkSheetFolderService</className>
        <method>findAttachPath</method>
        <placeholder>redAnalyzeMethodName,analystName,workSheetCode</placeholder>
        <path>PRO/WorkSheet/{redAnalyzeMethodName}/{analystName}/{workSheetCode}/OriginRecord/Files</path>
    </config>
    <config>
        <code>workSheetFileGrabber</code>
        <name>检测单仪器解析文件</name>
        <className>com.sinoyd.lims.pro.service.WorkSheetFolderService</className>
        <method>findAttachPath</method>
        <placeholder>redAnalyzeMethodName,analystName,workSheetCode</placeholder>
        <path>PRO/WorkSheet/Mobile/{redAnalyzeMethodName}/{analystName}/{workSheetCode}/FileGrabber</path>
    </config>
    <config>
        <code>report</code>
        <name>报告附件</name>
        <className>com.sinoyd.lims.pro.service.ReportService</className>
        <method>findAttachPath</method>
        <placeholder>code</placeholder>
        <path>PRO/Report/{code}/Files</path>
    </config>
    <config>
        <code>electronicReport</code>
        <name>电子报告附件</name>
        <className>com.sinoyd.lims.pro.service.ReportService</className>
        <method>findAttachPath</method>
        <placeholder>code</placeholder>
        <path>PRO/ElectronicReport/{code}/Files</path>
    </config>
    <config>
        <code>reportFolderSketch</code>
        <name>报告点位示意图附件</name>
        <className>com.sinoyd.lims.pro.service.ReportService</className>
        <method>findAttachPath</method>
        <placeholder>code</placeholder>
        <path>PRO/Report/{code}/FolderSketch</path>
    </config>
    <config>
        <code>generateReport</code>
        <name>报告文件</name>
        <className>com.sinoyd.lims.pro.service.ReportService</className>
        <method>findAttachPath</method>
        <placeholder>code</placeholder>
        <path>PRO/Report/{code}/GenerateReport</path>
    </config>

    <config>
        <code>publishSystemVersion</code>
        <name>版本发布管理附件</name>
        <className>com.sinoyd.lims.lim.service.PublishSystemVersionService</className>
        <method>findAttachPath</method>
        <placeholder>versionNum,id</placeholder>
        <path>LIM/Resource/PublishSystemVersion/{versionNum}/{id}</path>
    </config>

    <config>
        <code>personPhotos</code>
        <name>人员照片</name>
        <className>com.sinoyd.lims.lim.service.PersonService</className>
        <method>findPersonAttachment</method>
        <placeholder>cName,id</placeholder>
        <path>LIM/Resource/Person/{cName}{id}/Photos</path>
    </config>
    <config>
        <code>userSign</code>
        <name>用户签名</name>
        <className>com.sinoyd.lims.lim.service.PersonService</className>
        <method>findPersonAttachment</method>
        <placeholder>cName,id</placeholder>
        <path>LIM/Resource/Person/{cName}{id}/Signature</path>
    </config>
    <config>
        <code>personSign</code>
        <name>人员签名</name>
        <className>com.sinoyd.lims.lim.service.PersonService</className>
        <method>findPersonAttachment</method>
        <placeholder>cName,id</placeholder>
        <path>LIM/Resource/Person/{cName}{id}/Signature</path>
    </config>
    <config>
        <code>personCertificate</code>
        <name>人员证书</name>
        <className>com.sinoyd.lims.lim.service.PersonService</className>
        <method>findPersonAttachment</method>
        <placeholder>cName,id</placeholder>
        <path>LIM/Resource/Person/{cName}{id}/Certificate</path>
    </config>
    <config>
        <code>person</code>
        <name>人员附件</name>
        <className>com.sinoyd.lims.lim.service.PersonService</className>
        <method>findPersonAttachment</method>
        <placeholder>cName,id</placeholder>
        <path>LIM/Resource/Person/{cName}{id}/Attachment</path>
    </config>

    <config>
        <code>training</code>
        <name>培训附件</name>
        <className>com.sinoyd.lims.lim.service.TrainingService</className>
        <method>findOne</method>
        <placeholder>trainingName</placeholder>
        <path>LIM/Training/{trainingName}</path>
    </config>

    <config>
        <code>personCert</code>
        <name>上岗证附件</name>
        <className>com.sinoyd.lims.lim.service.PersonCertService</className>
        <method>findPersonAttachment</method>
        <placeholder>certCode,id</placeholder>
        <path>LIM/Resource/PersonCert/{certCode}{id}</path>
    </config>

    <config>
        <code>systemConfigPhotos</code>
        <name>信息系统管理企业图片</name>
        <className>com.sinoyd.base.service.SystemConfigService</className>
        <method>findAttachment</method>
        <placeholder>shortName,id</placeholder>
        <path>LIM/Resource/systemConfig/{shortName}{id}/Photos</path>
    </config>

    <config>
        <code>systemConfigLOGO</code>
        <name>信息系统管理企业LOGO</name>
        <className>com.sinoyd.base.service.SystemConfigService</className>
        <method>findAttachment</method>
        <placeholder>shortName,id</placeholder>
        <path>LIM/Resource/systemConfig/{shortName}{id}/LOGO</path>
    </config>

    <config>
        <code>systemConfigCMA</code>
        <name>信息系统管理企业CMA章</name>
        <className>com.sinoyd.base.service.SystemConfigService</className>
        <method>findAttachment</method>
        <placeholder>shortName,id</placeholder>
        <path>LIM/Resource/systemConfig/{shortName}{id}/CMA</path>
    </config>

    <config>
        <code>systemConfigCNAS</code>
        <name>信息系统管理企业CNAS章</name>
        <className>com.sinoyd.base.service.SystemConfigService</className>
        <method>findAttachment</method>
        <placeholder>shortName,id</placeholder>
        <path>LIM/Resource/systemConfig/{shortName}{id}/CNAS</path>
    </config>

    <config>
        <code>systemConfigCheck</code>
        <name>信息系统管理企业检测专用章</name>
        <className>com.sinoyd.base.service.SystemConfigService</className>
        <method>findAttachment</method>
        <placeholder>shortName,id</placeholder>
        <path>LIM/Resource/systemConfig/{shortName}{id}/check</path>
    </config>



    <config>
        <code>instrumentPhotos</code>
        <name>仪器照片附件</name>
        <className>com.sinoyd.base.service.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/Photos</path>
    </config>
    <config>
        <code>instrument</code>
        <name>仪器附件</name>
        <className>com.sinoyd.base.service.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/Attachment</path>
    </config>
    <config>
        <code>instrumentUseRecord</code>
        <name>仪器使用附件</name>
        <className>com.sinoyd.base.service.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/UseRecord</path>
    </config>
    <config>
        <code>instrumentMaintenance</code>
        <name>仪器维修附件</name>
        <className>com.sinoyd.base.service.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/Maintenance</path>
    </config>
    <config>
        <code>instrumentScrapped</code>
        <name>仪器报废附件</name>
        <className>com.sinoyd.base.service.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>instrumentName,id</placeholder>
        <path>LIM/Resource/Instrument/{instrumentName}{id}/Scrapped</path>
    </config>
    <config>
        <code>consumable</code>
        <name>消耗品附件</name>
        <className>com.sinoyd.base.service.ConsumableService</className>
        <method>findOne</method>
        <placeholder>consumableName,id</placeholder>
        <path>LIM/Resource/Consumable/ConsumableDoc/{consumableName}{id}</path>
    </config>
    <config>
        <code>consumableDetail</code>
        <name>消耗品详单附件</name>
        <className>com.sinoyd.base.service.ConsumableDetailService</className>
        <method>findAttachment</method>
        <placeholder>id</placeholder>
        <path>LIM/Resource/Consumable/ConsumableDetailDoc/{id}</path>
    </config>
    <config>
        <code>analyzeMethod</code>
        <name>分析方法附件</name>
        <className>com.sinoyd.lims.lim.service.AnalyzeMethodService</className>
        <method>findOne</method>
        <placeholder>methodName</placeholder>
        <path>LIM/Resource/Test/AnalyzeMethod/{methodName}</path>
    </config>
    <config>
        <code>folder</code>
        <name>文档管理</name>
        <className>com.sinoyd.base.service.FolderService</className>
        <method>getDocumentAttachPath</method>
        <placeholder>path</placeholder>
        <path>LIM/Resource/Document/{path}</path>
    </config>
    <config>
        <code>environmental</code>
        <name>环境管理</name>
        <className>com.sinoyd.lims.lim.service.EnvironmentalService</className>
        <method>getDocumentAttachPath</method>
        <placeholder>path</placeholder>
        <path>LIM/Resource/{labCode}{id}</path>
    </config>
    <config>
        <code>reportConfig</code>
        <name>报表配置</name>
        <className>com.sinoyd.lims.lim.service.ReportConfigService</className>
        <method>getDocumentAttachPath</method>
        <placeholder>typeCode</placeholder>
        <path>{typeCode}</path>
    </config>
    <config>
        <code>carManage</code>
        <name>车辆管理附件</name>
        <className>com.sinoyd.lims.lim.service.CarManageService</className>
        <method>findOne</method>
        <placeholder>carCode</placeholder>
        <path>LIM/Resource/Car/{carCode}/Attachment</path>
    </config>
    <config>
        <code>carConsumerRecord</code>
        <name>车辆消费附件</name>
        <className>com.sinoyd.lims.lim.service.CarConsumerRecordService</className>
        <method>findOne</method>
        <placeholder>carCode,typeName,formatDate</placeholder>
        <path>LIM/Resource/Car/{carCode}/{typeName}{formatDate}</path>
    </config>
    <config>
        <code>contract</code>
        <name>合同附件</name>
        <className>com.sinoyd.lims.lim.service.ContractService</className>
        <method>findOne</method>
        <placeholder>contractCode,id</placeholder>
        <path>LIM/Resource/Contract/{contractCode}{id}</path>
    </config>
    <config>
        <code>contractPayRecord</code>
        <name>合同付款记录附件</name>
        <className>com.sinoyd.lims.lim.service.ContractService</className>
        <method>findOne</method>
        <placeholder>contractCode,id</placeholder>
        <path>LIM/Resource/RecAndPayRecord/{contractCode}{id}</path>
    </config>
    <config>
        <code>laboratoryInfo</code>
        <name>系统简介附件</name>
        <path>LIM/Configuration/Synopsis</path>
    </config>
    <config>
        <code>evaluation</code>
        <name>评价标准附件</name>
        <className>com.sinoyd.base.service.EvaluationCriteriaService</className>
        <method>findOne</method>
        <placeholder>name,ye</placeholder>
        <path>LIM/Evaluation/{name}{ye}</path>
    </config>
    <config>
        <code>fileControlApply</code>
        <name>文件受控申请文件附件</name>
        <className>com.sinoyd.lims.lim.service.FileControlApplyDetailService</className>
        <method>getFileControlPath</method>
        <placeholder>path,fileCode,controlCode</placeholder>
        <path>LIM/FileControl/{path}/{fileCode}/{controlCode}</path>
    </config>
    <config>
        <code>fileControlFinal</code>
        <name>文件受控正式文件附件</name>
        <className>com.sinoyd.lims.lim.service.FileControlApplyDetailService</className>
        <method>getFileControlPath</method>
        <placeholder>path,fileCode</placeholder>
        <path>LIM/FileControl/{path}/{fileCode}/最终版本</path>
    </config>
    <config>
        <code>fileNoControl</code>
        <name>文件受控未受控文件附件</name>
        <className>com.sinoyd.lims.lim.service.FileControlApplyDetailService</className>
        <method>getFileControlPath</method>
        <placeholder>path</placeholder>
        <path>LIM/FileControl/{path}/未受控文件</path>
    </config>
    <config>
        <code>blankCurve</code>
        <name>空白曲线</name>
        <className>com.sinoyd.lims.pro.service.BlankCurveStatisticsService</className>
        <method>getFilePathConfig</method>
        <placeholder>redAnalyzeItemName,cName</placeholder>
        <path>LIM/QC/BlankCurve/{redAnalyzeItemName}/{cName}</path>
    </config>
    <config>
        <code>standardCurve</code>
        <name>标准曲线</name>
        <className>com.sinoyd.lims.pro.service.BlankCurveStatisticsService</className>
        <method>getFilePathConfig</method>
        <placeholder>redAnalyzeItemName,cName</placeholder>
        <path>LIM/QC/StandardCurve/{redAnalyzeItemName}/{cName}</path>
    </config>
    <config>
        <code>appConfig</code>
        <name>app应用配置</name>
        <className>com.sinoyd.lims.lim.service.AppConfigService</className>
        <method>findAttachment</method>
        <placeholder>name,id</placeholder>
        <path>LIM/Resource/systemConfig/{name}/{id}</path>
    </config>
    <config>
        <code>shareFolder</code>
        <name>共享文档</name>
        <className>com.sinoyd.base.service.FolderService</className>
        <method>getDocumentAttachPath</method>
        <placeholder>path</placeholder>
        <path>DLY/ShareDocument/{path}</path>
    </config>
    <config>
        <code>notice</code>
        <name>公告附件</name>
        <className>com.sinoyd.lims.lim.service.NoticeService</className>
        <method>getNoticePath</method>
        <placeholder>categoryName,releaseDateStr,id</placeholder>
        <path>DLY/NoticeDoc/{categoryName}/{releaseDateStr}{id}</path>
    </config>
    <config>
        <code>annualPlan</code>
        <name>年度计划附件</name>
        <className>com.sinoyd.lims.qa.service.AnnualPlanService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/AnnualPlanDoc/{id}</path>
    </config>
    <config>
        <code>notConformItem</code>
        <name>不符合项管理附件</name>
        <className>com.sinoyd.lims.qa.service.NotConformItemService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/NotConformItemDoc/{id}</path>
    </config>
    <config>
        <code>internalAuditPlan</code>
        <name>内审管理附件</name>
        <className>com.sinoyd.lims.qa.service.InternalAuditPlanService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/NotInternalAuditPlanDoc/{id}</path>
    </config>
    <config>
        <code>customerComplaintRegist</code>
        <name>投诉管理附件</name>
        <className>com.sinoyd.lims.qa.service.CustomerComplaintRegistService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/CustomerComplaintDoc/{id}</path>
    </config>
    <config>
        <code>managementReviewPlan</code>
        <name>管理评审附件</name>
        <className>com.sinoyd.lims.qa.service.ManagementReviewPlanService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/ManagementReviewDoc/{id}</path>
    </config>
    <config>
        <code>monitoringPlan</code>
        <name>质量监督附件</name>
        <className>com.sinoyd.lims.qa.service.MonitoringPlanService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/MonitoringPlanDoc/{id}</path>
    </config>
    <config>
        <code>versionInfo</code>
        <name>移动端附件</name>
        <className>com.sinoyd.lims.lim.service.VersionInfoService</className>
        <method>findAttachPath</method>
        <placeholder>verType,version</placeholder>
        <path>LIM/versionInfo/{verType}/{version}/attachment</path>
    </config>
    <config>
        <code>versionQrCode</code>
        <name>移动端二维码</name>
        <className>com.sinoyd.lims.lim.service.VersionInfoService</className>
        <method>findAttachPath</method>
        <placeholder>verType,version</placeholder>
        <path>LIM/versionInfo/{verType}/{version}/qrCode</path>
    </config>
    <config>
        <code>riskAccident</code>
        <name>风险机遇管理附件</name>
        <className>com.sinoyd.lims.qa.service.RiskAndAccidentService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/RiskAccident/{id}</path>
    </config>

    <!--- 第二阶段 -->
    <config>
        <code>qcProject</code>
        <name>质控任务登记附件</name>
        <className>com.sinoyd.lims.pro.service.QCProjectService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/Project/{id}</path>
    </config>

    <config>
        <code>customerManagement</code>
        <name>客户管理附件</name>
        <className>com.sinoyd.base.service.EnterpriseService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>BASE/CustomerManagement/{id}</path>
    </config>
    <config>
        <code>subcontractorManagement</code>
        <name>分包商管理附件</name>
        <className>com.sinoyd.base.service.EnterpriseService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>BASE/SubcontractorManagement/{id}</path>
    </config>
    <config>
        <code>supplierManagement</code>
        <name>供应商管理附件</name>
        <className>com.sinoyd.base.service.EnterpriseService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>BASE/SupplierManagement/{id}</path>
    </config>

    <config>
        <code>projectExplore</code>
        <name>项目踏勘附件</name>
        <className>com.sinoyd.lims.pro.service.ExploreService</className>
        <method>findAttachPath</method>
        <placeholder>projectCode,id</placeholder>
        <path>PRO/ProjectDoc/{projectCode}/Explore/{id}</path>
    </config>

    <config>
        <code>examineFolder</code>
        <name>考核管理考核</name>
        <className>com.sinoyd.lims.lim.service.ExamineService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>LIM/Resource/examine/{id}</path>
    </config>

    <config>
        <code>examineRecord</code>
        <name>考核管理考核记录</name>
        <className>com.sinoyd.lims.lim.service.ExamineTypeRecordService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>LIM/Resource/examineRecord/{id}</path>
    </config>
    <config>
        <code>fixedAssets</code>
        <name>固定资产附件</name>
        <className>com.sinoyd.lims.lim.service.FixedAssetsService</className>
        <method>findAttachPath</method>
        <placeholder>assetsNo</placeholder>
        <path>LIM/FixedAssets/{assetsNo}</path>
    </config>

    <!--移动端附件-->
    <config>
        <code>folderPhoneDoc</code>
        <name>点位附件</name>
        <placeholder>id,cycleOrder</placeholder>
        <path>PRO/FolderDoc/{id}/{cycleOrder}</path>
    </config>

    <config>
        <code>recordSignature</code>
        <name>采样签名</name>
        <placeholder>id,typeName</placeholder>
        <path>PRO/ReceiveSampleRecordDoc/{id}/{typeName}</path>
    </config>

    <config>
        <code>orderContract</code>
        <name>合同附件</name>
        <className>com.sinoyd.lims.pro.service.OrderContractService</className>
        <method>findAttachPath</method>
        <placeholder>contractName,contractCode</placeholder>
        <path>PRO/OrderContract/{contractName}{contractCode}</path>
    </config>
    <config>
        <code>recAndPayRecord</code>
        <name>收款记录附件</name>
        <className>com.sinoyd.lims.pro.service.RecAndPayRecordService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/recAndPayRecord/{id}</path>
    </config>
    <config>
        <code>mobileRecord</code>
        <name>移动端送样单附件</name>
        <placeholder>id</placeholder>
        <path>PRO/ReceiveSampleRecord/MobileRecord/{id}</path>
    </config>
    <config>
        <code>instrumentInspect</code>
        <name>仪器核查附件</name>
        <className>com.sinoyd.base.service.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>id</placeholder>
        <path>LIM/InstrumentInspect/{id}</path>
    </config>
    <config>
        <code>instrumentCheck</code>
        <name>仪器校准附件</name>
        <className>com.sinoyd.base.service.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>id</placeholder>
        <path>LIM/InstrumentCheck/{id}</path>
    </config>
    <config>
        <code>instrumentRepair</code>
        <name>仪器维护附件</name>
        <className>com.sinoyd.base.service.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>id</placeholder>
        <path>LIM/InstrumentRepair/{id}</path>
    </config>
    <config>
        <code>instrumentMain</code>
        <name>仪器维修记录附件</name>
        <className>com.sinoyd.base.service.InstrumentService</className>
        <method>findInstrumentAttachment</method>
        <placeholder>id</placeholder>
        <path>LIM/InstrumentMain/{id}</path>
    </config>
    <config>
        <code>ocrImage</code>
        <name>OCR图片</name>
        <className>com.sinoyd.base.service.OcrConfigService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>LIM/OcrImage/{id}</path>
    </config>
    <config>
        <code>orderForm</code>
        <name>订单附件</name>
        <className>com.sinoyd.lims.pro.service.OrderFormService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/OrderForm/{id}</path>
    </config>
    <config>
        <code>OATask</code>
        <name>我的审批附件</name>
        <className>com.sinoyd.lims.pro.service.OATaskService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/OATask/{title}</path>
    </config>
    <config>
        <code>industryType</code>
        <name>行业类型附件</name>
        <className>com.sinoyd.base.service.IndustryTypeService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>BASE/IndustryType/{id}</path>
    </config>
    <config>
        <code>SamplingArrange</code>
        <name>采购计划安排附件</name>
        <className>com.sinoyd.lims.pro.service.SamplingArrangeService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/SamplingArrange/{samplingPlanId}</path>
    </config>


    <config>
        <code>instrumentGatherPhotos</code>
        <name>接入仪器照片附件</name>
        <className>com.sinoyd.lims.lim.service.InstrumentGatherService</className>
        <method>findInstrumentGatherAttachment</method>
        <placeholder>id</placeholder>
        <path>LIM/Resource/InstrumentGather/{id}/Photos</path>
    </config>

    <config>
        <code>projectSheet</code>
        <name>项目电子表单</name>
        <className>com.sinoyd.lims.pro.service.ProjectService</className>
        <method>findAttachPath</method>
        <placeholder>projectCode</placeholder>
        <path>PRO/ProjectDoc/{projectCode}/projectSheet</path>
    </config>

    <config>
        <code>deliverySpread</code>
        <name>电子交接单</name>
        <className>com.sinoyd.lims.pro.service.ReceiveSampleRecordService</className>
        <method>findAttachPath</method>
        <placeholder>recordCode</placeholder>
        <path>PRO/ReceiveSampleRecord/{recordCode}/RecDeliverySpread</path>
    </config>
    <config>
        <code>certHistory</code>
        <name>证书附件历史备份</name>
        <className>com.sinoyd.lims.lim.service.CertHistoryFileService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>LIM/CertHistory/{id}</path>
    </config>
    <config>
        <code>yearlyPlan</code>
        <name>年度计划电子表单</name>
        <className>com.sinoyd.lims.qa.service.YearlyPlanService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/YearlyPlanDoc/{id}</path>
    </config>
    <config>
        <code>yearlyQualitySupervisionPlan</code>
        <name>质量监督执行电子表单</name>
        <className>com.sinoyd.lims.qa.service.YearlyQualitySupervisionPlanService</className>
        <method>findAttachment</method>
        <placeholder>id</placeholder>
        <path>QA/YearlyQualitySupervisionPlanDoc/{id}</path>
    </config>
    <config>
        <code>yearlyQualityControlPlan</code>
        <name>质量控制执行电子表单</name>
        <className>com.sinoyd.lims.qa.service.YearlyQualityControlPlanService</className>
        <method>findAttachment</method>
        <placeholder>id</placeholder>
        <path>QA/YearlyQualityControlPlanDoc/{id}</path>
    </config>
    <config>
        <code>yearlyManagementReviewPlan</code>
        <name>管理评审执行电子表单</name>
        <className>com.sinoyd.lims.qa.service.YearlyManagementReviewPlanService</className>
        <method>findAttachment</method>
        <placeholder>id</placeholder>
        <path>QA/YearlyManagementReviewPlanDoc/{id}</path>
    </config>
    <config>
        <code>yearlyStaffTrainingPlan</code>
        <name>人员培训执行电子表单</name>
        <className>com.sinoyd.lims.qa.service.YearlyStaffTrainingPlanService</className>
        <method>findAttachment</method>
        <placeholder>id</placeholder>
        <path>QA/YearlyStaffTrainingPlanDoc/{id}</path>
    </config>
    <config>
        <code>yearlyInstrumentCheckPlan</code>
        <name>仪器期间核查执行电子表单</name>
        <className>com.sinoyd.lims.qa.service.YearlyInstrumentCheckPlanService</className>
        <method>findAttachment</method>
        <placeholder>id</placeholder>
        <path>QA/YearlyInstrumentCheckPlanDoc/{id}</path>
    </config>
    <config>
        <code>yearlyStandardCheckPlan</code>
        <name>标物期间核查执行电子表单</name>
        <className>com.sinoyd.lims.qa.service.YearlyStandardCheckPlanService</className>
        <method>findAttachment</method>
        <placeholder>id</placeholder>
        <path>QA/YearlyStandardCheckPlanDoc/{id}</path>
    </config>
    <config>
        <code>yearlyInstrumentConfirmPlan</code>
        <name>仪器检定校准执行电子表单</name>
        <className>com.sinoyd.lims.qa.service.YearlyInstrumentConfirmPlanService</className>
        <method>findAttachment</method>
        <placeholder>id</placeholder>
        <path>QA/YearlyInstrumentConfirmPlanDoc/{id}</path>
    </config>
    <config>
        <code>planInternalAudit</code>
        <name>内审计划附件</name>
        <className>com.sinoyd.lims.qa.service.PlanInternalAuditService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/PlanInternalAuditDoc/{id}</path>
    </config>
    <config>
        <code>internalAuditDivideTheWork</code>
        <name>内审分工附件</name>
        <className>com.sinoyd.lims.qa.service.InternalAuditDivideTheWorkService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/InternalAuditDivideTheWorkDoc/{id}</path>
    </config>
    <config>
        <code>internalAuditReport</code>
        <name>内审报告附件</name>
        <className>com.sinoyd.lims.qa.service.PlanInternalAuditService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>QA/PlanInternalAuditDoc/Report/{id}</path>
    </config>
    <config>
        <code>fileAuditProgress</code>
        <name>文件审批管理附件</name>
        <className>com.sinoyd.lims.pro.service.FileAuditService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/fileAuditProgress/{id}</path>
    </config>

    <config>
        <code>commissionSign</code>
        <name>委托方电子签名</name>
        <placeholder>id,typeName</placeholder>
        <path>PRO/projectSignDoc/{id}/{typeName}</path>
    </config>
    <config>
        <code>newSearchPlanConfirm</code>
        <name>标准查新标准确认附件</name>
        <className>com.sinoyd.lims.lim.service.NewSearchResultService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/newSearchPlanConfirm/{id}</path>
    </config>
    <config>
        <code>newSearchPlanPropagate</code>
        <name>标准查新标准宣贯附件</name>
        <className>com.sinoyd.lims.lim.service.NewSearchResultService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/newSearchPlanPropagate/{id}</path>
    </config>
    <config>
        <code>newSearchPlanDept</code>
        <name>标准查新标部门确认附件</name>
        <className>com.sinoyd.lims.lim.service.NewSearchResultService</className>
        <method>findAttachPath</method>
        <placeholder>id</placeholder>
        <path>PRO/newSearchPlanPropagate/{id}</path>
    </config>
</configs>