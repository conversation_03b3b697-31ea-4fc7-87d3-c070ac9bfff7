package com.sinoyd.lims.od.listener;

import com.sinoyd.base.listener.LIMSEvent;
import com.sinoyd.lims.od.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0.0 2025/3/18
 * @since V100R001
 */
@Component
public class TaskListener {

    private TaskService taskService;


    /**
     * 监听验证状态
     *
     * @param testEvent 测试项目事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.lims.lim.enums.EnumLIM.EnumOdsEvent).ADD.name()" +
                    " and #root.event.name eq T(com.sinoyd.lims.lim.enums.EnumLIM.EnumOdsEvent).CREATE_TASK.name()")
    @Transactional
    public void listenValidate(LIMSEvent<Map<String, Object>> testEvent) {
        // 测试项目依赖数据
        Map<String, Object> source = testEvent.getSource();
        taskService.autoCreateTask(source);
    }

    @Autowired
    @Lazy
    public void setTaskService(TaskService taskService) {
        this.taskService = taskService;
    }
}
