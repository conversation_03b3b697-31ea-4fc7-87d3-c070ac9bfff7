package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoWorkSheet;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * WorkSheet操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface WorkSheetService extends IBaseJpaService<DtoWorkSheet, String> {
    /**
     * 找到检测单下的子检测单
     *
     * @param parentId 父级id
     * @return 返回子检测单数据
     */
    List<DtoWorkSheet> findByParentId(String parentId);
}