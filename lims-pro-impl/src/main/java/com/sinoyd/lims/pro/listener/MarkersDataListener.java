package com.sinoyd.lims.pro.listener;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.listener.LIMSEvent;
import com.sinoyd.lims.lim.dto.customer.DtoMarkersData;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.impl.MarkersDataListenerImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.util.List;

/**
 * 数据标记监听
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/1
 * @since V100R001
 */
@Component
public class MarkersDataListener {

    @Autowired
    private MarkersDataListenerImpl markersDataListener;

    /**
     * 监听验证状态
     *
     * @param testEvent 测试项目事件
     */
    @EventListener(value = LIMSEvent.class,
            condition = "#root.event.action eq T(com.sinoyd.lims.lim.enums.EnumLIM.EnumMarkersData).VALIDATE.name()" +
                    " and #root.event.name eq T(com.sinoyd.lims.lim.enums.EnumLIM.EnumMarkersData).MARKERS_DATA.name()")
    @Transactional
    public void listenValidate(LIMSEvent<DtoMarkersData> testEvent) {
        DtoMarkersData dtoMarkersData = testEvent.getSource();
        // 测试项目依赖数据
        List<DtoTest> testList = dtoMarkersData.getTestList();
        List<DtoQualityControlLimit> qualityControlLimitList = dtoMarkersData.getQualityControlLimitList();
        markersDataListener.processMarkersData(testList, qualityControlLimitList);
    }

}
