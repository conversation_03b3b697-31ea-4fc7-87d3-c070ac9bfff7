package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig2Test;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp;
import com.sinoyd.lims.lim.repository.rcc.TestQCRemindConfig2TestRepository;
import com.sinoyd.lims.lim.repository.rcc.TestQCRemindConfigRepository;
import com.sinoyd.lims.lim.service.TestQCRemindConfig2TestService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * TestQCRemindConfig2Test操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
@Service
public class TestQCRemindConfig2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoTestQCRemindConfig2Test, String, TestQCRemindConfig2TestRepository> implements TestQCRemindConfig2TestService {

    @Autowired
    private TestService testService;

    @Autowired
    private SampleTypeService sampleTypeService;

    @Autowired
    private TestQCRemindConfigRepository testQCRemindConfigRepository;

    @Override
    public void findByPage(PageBean<DtoTestQCRemindConfig2Test> page, BaseCriteria criteria) {
        page.setEntityName("DtoTestQCRemindConfig2Test p");
        page.setSelect("select p");
        int originPageNo = page.getPageNo();
        int originPageCount = page.getRowsPerPage();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        super.findByPage(page, criteria);

        List<DtoTestQCRemindConfig2Test> list = page.getData();

        List<DtoTest> testList = testService.findAll();
        List<DtoSampleType> typeList = sampleTypeService.findAll();
        for (DtoTestQCRemindConfig2Test item : list) {

            Optional<DtoTest> ap = testList.stream().filter(p -> item.getTestId().contains(p.getId())).findFirst();
            if (ap.isPresent()) {
                DtoTest test = ap.get();
                item.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                item.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                item.setRedCountryStandard(test.getRedCountryStandard());
                item.setIsCompleteField(test.getIsCompleteField());
                item.setIsOutsourcing(test.getIsOutsourcing());

                Optional<DtoSampleType> apst = typeList.stream().filter(p -> test.getSampleTypeId().contains(p.getId())).findFirst();
                if (apst.isPresent()) {
                    DtoSampleType type = apst.get();
                    item.setSampleTypeName(type.getTypeName());
                }
            }

        }
        //BUG2024091900626 【重要】【rcc】【质控比例配置】关联测试项目列表排序：检测类型、标准编号、分析方法、分析项目顺序排列
        list.sort(Comparator.comparing(DtoTestQCRemindConfig2Test::getSampleTypeName,Comparator.nullsLast(Comparator.naturalOrder()))
        .thenComparing(DtoTestQCRemindConfig2Test::getRedCountryStandard,Comparator.nullsLast(Comparator.naturalOrder()))
        .thenComparing(DtoTestQCRemindConfig2Test::getRedAnalyzeMethodName,Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(DtoTestQCRemindConfig2Test::getRedAnalyzeItemName,Comparator.nullsLast(Comparator.naturalOrder())));

        page.setData(list.stream().skip((long) (originPageNo - 1) *originPageCount).limit(originPageCount).collect(Collectors.toList()));// 重新封装到pageBean返回到上层
    }

    @Override
    @Transactional
    public DtoTestQCRemindConfig2Test save(DtoTestQCRemindConfig2Test entity) {
        List<DtoTestQCRemindConfig2Test> save = save(Collections.singletonList(entity));
        return save.get(0);
    }

    @Override
    @Transactional
    public List<DtoTestQCRemindConfig2Test> save(Collection<DtoTestQCRemindConfig2Test> entities) {
        //获取到新增的测试项目id
        List<String> testIds = entities.stream().map(DtoTestQCRemindConfig2Test::getTestId).distinct().collect(Collectors.toList());
        List<String> insertConfigIds = entities.stream().map(DtoTestQCRemindConfig2Test::getConfigId).distinct().collect(Collectors.toList());
        //获取到测试项目对应的所有比例配置
        List<DtoTestQCRemindConfig2Test> testConfigs = repository.findByTestIdIn(testIds);
        Map<String, List<DtoTestQCRemindConfig2Test>> testConfigGroup = testConfigs.stream()
                .collect(Collectors.groupingBy(DtoTestQCRemindConfig2Test::getTestId));
        //获取质控比例配置
        List<String> qcRemindConfigIds = testConfigs.stream().map(DtoTestQCRemindConfig2Test::getConfigId).distinct().collect(Collectors.toList());
        qcRemindConfigIds.addAll(insertConfigIds);
        Map<String, DtoTestQCRemindConfig> QcRemindConfigMap = testQCRemindConfigRepository.findAll(qcRemindConfigIds)
                .stream().collect(Collectors.toMap(DtoTestQCRemindConfig::getId, p -> p));
        //需要删除的质控比例关联测试项目配置
        List<DtoTestQCRemindConfig2Test> deleteConfigs = new ArrayList<>();
        //过滤出新增的测试项目原有的同质控类型与同质控等级的比例配置
        entities.forEach(p->filterDeleteConfigs(p, QcRemindConfigMap, testConfigGroup, deleteConfigs));
        if (StringUtil.isNotEmpty(deleteConfigs)){
            delete(deleteConfigs);
        }
        return super.save(entities);
    }

    /**
     * 过滤需要删除的质控比例关联测试项目配置
     *
     * @param insertConfigTest  添加的关联测试项目配置
     * @param QcRemindConfigMap 所有的质控比例配置
     * @param testConfigGroup   新增的测试项目对应的所有质控比例关联配置
     * @param deleteConfigs     需要删除的质控比例关联测试项目配置
     */
    private void filterDeleteConfigs(DtoTestQCRemindConfig2Test insertConfigTest,
                                     Map<String, DtoTestQCRemindConfig> QcRemindConfigMap,
                                     Map<String, List<DtoTestQCRemindConfig2Test>> testConfigGroup,
                                     List<DtoTestQCRemindConfig2Test> deleteConfigs) {
        DtoTestQCRemindConfig insertConfig = QcRemindConfigMap.get(insertConfigTest.getConfigId());
        List<DtoTestQCRemindConfig2Test> testConfigsOfTest = testConfigGroup.get(insertConfigTest.getTestId());
        if (StringUtil.isNotEmpty(testConfigsOfTest) && insertConfig != null) {
            List<DtoTestQCRemindConfig2Test> deleteTestConfigs = testConfigsOfTest.stream().filter(p -> {
                DtoTestQCRemindConfig existsConfig = QcRemindConfigMap.get(p.getConfigId());
                if (existsConfig != null
                        && existsConfig.getQcType().equals(insertConfig.getQcType())
                        && existsConfig.getQcGrade().equals(insertConfig.getQcGrade())) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            deleteConfigs.addAll(deleteTestConfigs);
        }
    }

    /**
     * 获取对应测试项目下的比例配置
     *
     * @param testIds 测试项目id集合
     * @return 比例配置
     */
    @Override
    public List<DtoTestQCRemindTemp> findByTestIds(List<String> testIds) {
//        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp(");
//        stringBuilder.append("t.qcGrade,t.qcType,t.qcRemindPercent,t.modifyDate,t2t.testId)");
//        PageBean<DtoTestQCRemindTemp> pageBean = new PageBean<>();
//        pageBean.setRowsPerPage(Integer.MAX_VALUE);
//        pageBean.setEntityName("DtoTestQCRemindConfig2Test t2t,DtoTestQCRemindConfig t");
//        pageBean.setSelect(stringBuilder.toString());
//        pageBean.addCondition(" and t2t.configId = t.id");
//        pageBean.addCondition(String.format(" and t2t.testId in ('%s')", String.join("','", testIds)));
//        comRepository.findByPage(pageBean);
//        List<DtoTestQCRemindTemp> tempList = StringUtil.isNotNull(pageBean.getData()) ? pageBean.getData() : new ArrayList<>();
        StringBuilder sql = new StringBuilder("select new com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp(");
        sql.append(" t.qcGrade,t.qcType,t.qcRemindPercent,t.modifyDate,t2t.testId)");
        sql.append(" from DtoTestQCRemindConfig2Test t2t,DtoTestQCRemindConfig t");
        sql.append(" where t2t.configId = t.id");
        sql.append(" and t2t.testId in :testIds");
        Map<String, Object> map = new HashMap<>();
        map.put("testIds", testIds);
        List<DtoTestQCRemindTemp> tempList = comRepository.find(sql.toString(), map);
        List<DtoTestQCRemindConfig> cfgList = testQCRemindConfigRepository.findByIsDefaultTrue();
        for (String testId : testIds) {
            for (DtoTestQCRemindConfig cfg : cfgList) {
                tempList.add(new DtoTestQCRemindTemp(cfg, testId));
            }
        }

        //取最新配置的比例配置，若没有则取默认配置（默认配置取较严格的那个）
        return new ArrayList<>(tempList.stream().sorted(Comparator.comparing(DtoTestQCRemindTemp::getOrderNum).reversed()).
                collect(Collectors.groupingBy(p -> String.format("%d-%d-%s", p.getQcGrade(), p.getQcType(), p.getTestId()),
                        Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values());
    }
}