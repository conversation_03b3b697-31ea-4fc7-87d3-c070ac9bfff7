package com.sinoyd.lims.foreign.controller;


import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataOutSorceDataVO;
import com.sinoyd.lims.foreign.DataSampleGroupVO;
import com.sinoyd.lims.foreign.service.DataOutSorceDataService;
import com.sinoyd.lims.foreign.service.DataSampleGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 样品分组信息服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataSampleGroup服务")
@RestController
@RequestMapping("api/pro/foreign/dataSampleGroup")
public class DataSampleGroupController extends ExceptionHandlerController<DataSampleGroupService> {


    /**
     * 数据同步
     *
     * @param map 同步参数
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataSampleGroupVO>> getSyncData(@RequestBody Map<String, Object> map) {
        List<DataSampleGroupVO> curveVOList = service.getSyncData(map);
        RestResponse<List<DataSampleGroupVO>> response = new RestResponse();
        response.setData(curveVOList);
        return response;
    }
}
