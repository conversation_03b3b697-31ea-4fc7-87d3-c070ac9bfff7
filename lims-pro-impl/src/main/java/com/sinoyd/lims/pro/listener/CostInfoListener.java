package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.lims.pro.service.CostInfoService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目提交生成费用监听通知
 *
 * <AUTHOR>
 * @version V1.0.0 2019-11-29
 * @since V100R001
 */
@Component
@Slf4j
public class CostInfoListener implements ExecutionListener {

    @Transactional
    @Override
    public void notify(DelegateExecution execution) {

        CostInfoService costInfoService = SpringContextAware.getBean(CostInfoService.class);
        costInfoService.generate(execution.getProcessBusinessKey());
    }
}
