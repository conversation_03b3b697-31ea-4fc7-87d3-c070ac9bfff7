CREATE  TRIGGER "AFTER_INSERT_TEST"
    AFTER  INSERT
    ON "TB_LIM_TEST"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 1, null, null, null, new.orgId, new.domainId);
end;
