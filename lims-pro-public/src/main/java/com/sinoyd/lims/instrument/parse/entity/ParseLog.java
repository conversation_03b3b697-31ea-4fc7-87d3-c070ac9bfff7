package com.sinoyd.lims.instrument.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 仪器解析日志实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/8/7
 */
@MappedSuperclass
@ApiModel(description = "Instrument Parse Log")
@Data
public class ParseLog implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ParseLog() {
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 应用Id
     */
    @Column(length = 50)
    @ApiModelProperty("应用Id")
    private String appId;

    /**
     * 解析类型（1：文件解析 2：数据流解析 3:文件解析调试）
     */
    @Column(length = 50)
    @ApiModelProperty("解析类型（1：文件解析 2：数据流解析 3:文件解析调试）")
    private String parseType;

    /**
     * 解析文件名(文件解析时输入)
     */
    @Column(length = 500)
    @ApiModelProperty("解析文件名(文件解析时输入)")
    private String fileOrgName;

    /**
     * 解析文件源目录(文件解析时输入)
     */
    @Column(length = 500)
    @ApiModelProperty("解析文件源目录(文件解析时输入)")
    private String fileOrgFolderName;

    /**
     * 解析后文件名
     */
    @Column(length = 500)
    @ApiModelProperty("解析后文件名")
    private String fileName;

    /**
     * 解析成功存放目录
     */
    @Column(length = 500)
    @ApiModelProperty("解析成功存放目录")
    private String destFolderName;

    /**
     * 解析开始时间
     */
    @ApiModelProperty("解析开始时间")
    private Date beginTime;

    /**
     * 解析结束时间
     */
    @ApiModelProperty("解析结束时间")
    private Date endTime;

    /**
     * 解析状态(1:解析中 2:解析成功 3:解析失败 )
     */
    @Column(length = 50)
    @ApiModelProperty("解析状态(1:解析中 2:解析成功 3:解析失败 )")
    private String parseStatus;

    /**
     * 处理方式(解析,上传)
     */
    @Column(length = 50)
    @ApiModelProperty("处理方式(解析,上传)")
    private String handleType;

    /**
     * 日志内容
     */
    @ApiModelProperty("日志内容")
    private String logContent;

    /**
     * 假删字段
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}