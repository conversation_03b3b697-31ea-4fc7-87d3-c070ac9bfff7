package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.SignatureService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 删除签名监听
 * <AUTHOR>
 * @version V1.0.0 2022/3/10
 * @since V100R001
 */
@Slf4j
@Component
public class RemovePictureListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {

        String businessKey = delegateExecution.getProcessBusinessKey();

        //签名
        SignatureService signatureService = SpringContextAware.getBean(SignatureService.class);
        Map<String, Integer> mapType = new HashMap<>();
        //移除记录单签名的类型，去除签名上岗证人员等于分析者
        mapType.put(EnumPRO.EnumSigType.分析者.getName(), 2);
        //mapType.put(EnumPRO.EnumSigType.上岗证人员.getName(), 2);
        mapType.put(EnumPRO.EnumSigType.复核者.getName(), 1);
        mapType.put(EnumPRO.EnumSigType.审核者.getName(), 1);
        //有参数分析日期，不能去除分析日期
        //mapType.put(EnumPRO.EnumSigType.分析日期.getName(), 1);
        mapType.put(EnumPRO.EnumSigType.校核日期.getName(), 1);
        mapType.put(EnumPRO.EnumSigType.复核日期.getName(), 1);
        //原始记录单根据工作单状态签名
        signatureService.removeWorkSheetSig(businessKey, mapType);

    }
}
