package com.sinoyd.lims.order.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import com.sinoyd.lims.pro.vo.OrderQuotationAnlVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service(CustomerOrderAnaService.CUSTOMER_ORDER_QUOTATION_ANL)
public class CustomerOrderQuotationAnlImpl extends AbsCustomerOrder {

    /**
     * 合同额分析
     *
     * @param year 年份
     * @return OrderQuotationAnlVo
     */
    @Override
    public BaseOrderCustomerAnalyzeVO statistics(Integer year) {
        String currentYearBegin = CalendarUtil.getCurrentYearBegin(year);
        String currentYearEnd = CalendarUtil.getCurrentYearEnd(year);
        String lastYearBegin = CalendarUtil.getLastYearBegin(year);
        String lastYearEnd = CalendarUtil.getLastYearEnd(year);

        //本年签单额
        List<Map<String, Object>> currentSignList = super.getOrderQuotationAnlSql(false, currentYearBegin, currentYearEnd);
        Integer signTotal = getLastQuotationAnl(currentSignList);
        List<Map<String, Object>> signNumList = super.getOrderNum(false, currentYearBegin, currentYearEnd);
        int signNum = 0;
        if (StringUtil.isNotEmpty(signNumList)) {
            signNum = signNumList.size();
        }

        //上一年签单额
        List<Map<String, Object>> lastSignList = super.getOrderQuotationAnlSql(false, lastYearBegin, lastYearEnd);
        Integer lastSignTotal = getLastQuotationAnl(lastSignList);
        Integer signCompare = signTotal - lastSignTotal;

        //本年订单额
        List<Map<String, Object>> currentOrderList = super.getOrderQuotationAnlSql(true, currentYearBegin, currentYearEnd);
        Integer orderTotal = getLastQuotationAnl(currentOrderList);
        List<Map<String, Object>> orderNumList = super.getOrderNum(true, currentYearBegin, currentYearEnd);
        int orderNum = 0;
        if (StringUtil.isNotEmpty(orderNumList)) {
            orderNum = orderNumList.size();
        }

        //上一年订单额
        List<Map<String, Object>> lastOrderList = super.getOrderQuotationAnlSql(true, lastYearBegin, lastYearEnd);
        Integer lastOrderTotal = getLastQuotationAnl(lastOrderList);
        Integer orderCompare = orderTotal - lastOrderTotal;

        //返回vo
        return new OrderQuotationAnlVo(signTotal, signNum, signCompare, orderTotal, orderNum, orderCompare);
    }


    /**
     * 获取金额订单数据
     *
     * @param list 单子的list
     * @return 单子金额数
     */
    private Integer getLastQuotationAnl(List<Map<String, Object>> list) {
        int total = 0;
        if (list != null) {
            for (Map<String, Object> map : list) {
                int value = new BigDecimal(map.get("finalQuotation").toString()).intValue();
                total += value;
            }
        }
        return total;
    }


}
