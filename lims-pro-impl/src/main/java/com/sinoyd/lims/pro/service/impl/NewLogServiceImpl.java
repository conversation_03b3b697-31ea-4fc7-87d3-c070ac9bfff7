package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.criteria.LogForWorkSheetCriteria;
import com.sinoyd.lims.pro.criteria.NewLogCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.NewLogService;
import com.sinoyd.lims.probase.service.NewLogBaseService;
import com.sinoyd.lims.probase.service.impl.NewLogBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Primary
@Service
public class NewLogServiceImpl extends NewLogBaseServiceImpl implements NewLogService {

    @Autowired
    private NewLogBaseService newLogBaseService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private WorkSheetFolderRepository workSheetFolderRepository;

    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private ReportRepository reportRepository;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    @Autowired
    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;

    @Autowired
    private Project2WorkSheetFolderRepository project2WorkSheetFolderRepository;

    @Autowired
    private ReportDetailRepository reportDetailRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    private LogForProjectRepository logForProjectRepository;

    @Autowired
    private LogForDataRepository logForDataRepository;

    @Autowired
    private LogForPlanRepository logForPlanRepository;

    @Autowired
    private LogForRecordRepository logForRecordRepository;

    @Autowired
    private LogForReportRepository logForReportRepository;

    @Autowired
    private LogForSampleRepository logForSampleRepository;

    @Autowired
    private LogForWorkSheetRepository logForWorkSheetRepository;


    @Autowired
    private LogForCostRepository logForCostRepository;

    @Autowired
    private CostInfoRepository costInfoRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    /**
     * 新增项目信息更新日志
     *
     * @param ids     对象ids
     * @param opinion 意见
     * @param type    类型
     */
    @Transactional
    @Override
    public void createProjectInfoUpdateLog(List<String> ids, String opinion, String type) {
        for (String id : ids) {
            DtoProject pro = projectRepository.findOne(id);

            DtoLog log = new DtoLog();
            String operateInfo = "";
            String comment = "";
            String operatorId;
            String operatorName;
            if (type.equals(EnumPRO.EnumLogOperateType.删除项目.toString())) {
                operateInfo = "删除项目";
                comment = "删除项目。";
            } else if (type.equals(EnumPRO.EnumLogOperateType.增加项目.toString())) {
                operateInfo = "增加项目";
                comment = "增加项目。";
            } else if (type.equals(EnumPRO.EnumLogOperateType.修改项目.toString())) {
                operateInfo = "修改项目";
                comment = "修改项目。";
            }
            try {
                operatorId = PrincipalContextUser.getPrincipal().getUserId();
                operatorName = PrincipalContextUser.getPrincipal().getUserName();
            } catch (Exception ex) {
                operatorId = pro.getInceptPersonId();
                operatorName = pro.getInceptPersonName();
            }
            log.setOperatorId(operatorId);
            log.setOperatorName(operatorName);
            log.setOperateTime(new Date());
            log.setOperateInfo(operateInfo);
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName("");
            log.setLogType(EnumPRO.EnumLogType.项目信息.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.项目.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            newLogBaseService.createLog(log);
        }
    }

    /**
     * 新增项目流程状态更新日志
     *
     * @param ids              对象ids
     * @param opinion          意见
     * @param nextOperatorId   下一步操作人
     * @param nextOperatorName 下一步操作人姓名
     */
    @Transactional
    @Override
    public void createProjectStatusUpdateLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName) {
        List<DtoLog> logs = new ArrayList<>();
        for (String id : ids) {
            DtoProject pro = projectRepository.findOne(id);

            DtoLog log = new DtoLog();
            String comment;
            String operateInfo;
            if (!pro.getIsDeleted()) {
                operateInfo = "更新项目状态";
                comment = String.format("更新项目状态为%s", pro.getStatus());
                if (StringUtils.isNotNullAndEmpty(opinion)) {
                    comment += String.format(",意见:%s", opinion);
                }
                if (StringUtils.isNotNullAndEmpty(nextOperatorName)) {
                    comment += String.format(",下一步操作人:%s", nextOperatorName);
                }
                comment += "。";
            } else {
                operateInfo = "删除项目";
                comment = "删除项目。";
            }
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(operateInfo);
            log.setNextOperatorId(StringUtils.isNotNullAndEmpty(nextOperatorId) ? nextOperatorId : UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName(StringUtils.isNotNullAndEmpty(nextOperatorName) ? nextOperatorName : "");
            log.setLogType(EnumPRO.EnumLogType.项目流程.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.项目.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            logs.add(log);
        }
        newLogBaseService.createLog(logs, EnumPRO.EnumLogType.项目流程.getValue());
    }


    @Transactional
    @Override
    public void createFinishProjectLogs(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName) {
        List<DtoLog> logs = new ArrayList<>();
        for (String id : ids) {
            DtoLog log = new DtoLog();
            String comment;
            String operateInfo = "更新项目状态";
            comment = String.format("更新项目状态为%s", EnumPRO.EnumProjectStatus.已办结.toString());
            if (StringUtils.isNotNullAndEmpty(opinion)) {
                comment += String.format(",意见:%s", opinion);
            }
            if (StringUtils.isNotNullAndEmpty(nextOperatorName)) {
                comment += String.format(",下一步操作人:%s", nextOperatorName);
            }
            comment += "。";

            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(operateInfo);
            log.setNextOperatorId(StringUtils.isNotNullAndEmpty(nextOperatorId) ? nextOperatorId : UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName(StringUtils.isNotNullAndEmpty(nextOperatorName) ? nextOperatorName : "");
            log.setLogType(EnumPRO.EnumLogType.项目流程.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.项目.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            logs.add(log);
        }
        newLogBaseService.createLog(logs, EnumPRO.EnumLogType.项目流程.getValue());
    }

    @Override
    public void createLog(DtoLog dtoLog) {
        super.createLog(dtoLog);
    }

    @Override
    public void createLog(DtoLog dtoLog, Boolean insertLogFlag) {
        List<DtoLog> logs = new ArrayList<>();
        logs.add(dtoLog);
        createLog(logs, dtoLog.getLogType(), insertLogFlag);
    }

    @Override
    public void createLog(List<DtoLog> logs, Integer logType, Boolean insertLogFlag) {
        for (DtoLog log : logs) {
            log.setOperateTime(new Date());
        }
        EnumPRO.EnumLogType code = EnumPRO.EnumLogType.getByValue(logType);
        switch (code) {
            //#region 项目
            case 项目信息:
            case 项目分包:
            case 项目流程:
            case 项目方案:
            case 项目合同:
            case 项目报告转交:
            case 项目送样单:
            case 项目检测单:
                List<DtoLogForProject> logP = logs.stream().map(DtoLogForProject::new).collect(Collectors.toList());
                if (logP.size() > 0) {
                    commonRepository.insert(logP);
                }
//                logForProjectBaseRepository.save(logP);
                break;
            //#endregion

            //#region 方案
            case 新增方案:
            case 复制方案:
            case 清空方案:
            case 方案点位信息:
            case 方案点位数据信息:
            case 方案检测方法信息:
                List<DtoLogForPlan> dtoLogForPlans = logs.stream().map(DtoLogForPlan::new).collect(Collectors.toList());
                if (dtoLogForPlans.size() > 0) {
                    commonRepository.insert(dtoLogForPlans);
                }
//                logForPlanBaseRepository.save(dtoLogForPlans);
                break;
            //#endregion

            //#region 检测单
            case 检测单更新参数:
            case 检测单基本信息:
            case 检测单配置:
            case 检测单使用记录:
            case 检测单试剂配置:
            case 检测单数据保存:
            case 检测单同步仪器:
            case 检测单原始记录:
            case 检测单增删:
            case 检测单增删样品:
            case 检测单流程:
                this.worksheetLogInsert(logs, logType, insertLogFlag);
                break;
            //#endregion

            //#region 送样单
            case 实验室领样单流程:
            case 实验室领样单信息:
            case 实验室领样单分配:
            case 送样单采样使用记录:
            case 送样单流程:
            case 送样单信息:
            case 送样单样品信息:
            case 现场领样单流程:
            case 现场领样单使用记录:
            case 现场领样单信息:
            case 现场领样单数据:
                List<DtoLogForRecord> logR = logs.stream().map(DtoLogForRecord::new).collect(Collectors.toList());
                if (logR.size() > 0) {
                    commonRepository.insert(logR);
                }
//                logForRecordBaseRepository.save(logR);
                break;
            //#endregion

            //#region 样品
            case 样品增删作废:
            case 样品信息:
            case 样品数据:
            case 样品流程:
            case 样品质控:
                List<DtoLogForSample> logS = logs.stream().map(DtoLogForSample::new).collect(Collectors.toList());
                if (logS.size() > 0) {
                    commonRepository.insert(logS);
                }
//                logForSampleBaseRepository.save(logS);
                break;
            //#endregion

            //#region 数据
            case 数据保存:
            case 数据分包:
            case 数据检测单:
            case 数据配置:
            case 数据退回:
            case 数据增删:
            case 数据审核:
                List<DtoLogForData> logD = logs.stream().map(DtoLogForData::new).collect(Collectors.toList());
                if (logD.size() > 0) {
                    commonRepository.insert(logD);
                }
//                logForDataBaseRepository.save(logD);
                break;
            //#endregion

            //#region 报告
            case 项目报告:
                List<DtoLogForReport> logRe = logs.stream().map(DtoLogForReport::new).collect(Collectors.toList());
                if (logRe.size() > 0) {
                    commonRepository.insert(logRe);
                }
//                logForReportBaseRepository.save(logRe);
                break;
            //#endregion

            //#region 费用流程
            case 费用流程:
                List<DtoLogForCost> logCost = logs.stream().map(DtoLogForCost::new).collect(Collectors.toList());
                if (logCost.size() > 0) {
                    commonRepository.insert(logCost);
                }
                break;
            //#endregion
        }
    }

    /**
     * 根据开关和操作类型判断是否记录工作单流程日志
     *
     * @param logs          要保存的日志对象
     * @param logType       操作类型
     * @param insertLogFlag 是否进行工作单流程日志筛选开关
     */
    private void worksheetLogInsert(List<DtoLog> logs, Integer logType, Boolean insertLogFlag) {
        //如果开关打开，工作单是新建和已经保存状态的基本数据操作日志
        if (insertLogFlag) {
            //如果是检测单操作
            if (EnumPRO.EnumLogObjectType.检测单.getValue().equals(logs.get(0).getObjectType())) {
                //获取不需要保存操作日志的工作单状态
                List<String> worksheetStatusNeedSaveOperateLog = new ArrayList<>();
                worksheetStatusNeedSaveOperateLog.add(EnumPRO.EnumWorkSheetStatus.新建.name());
                worksheetStatusNeedSaveOperateLog.add(EnumPRO.EnumWorkSheetStatus.已经保存.name());
                //获取需要判断工作单状态的再进行保存的日志操作类型
                List<Integer> logTypeNeedSaveOperateLog = new ArrayList<>();
                logTypeNeedSaveOperateLog.add(EnumPRO.EnumLogType.检测单基本信息.getValue());
                logTypeNeedSaveOperateLog.add(EnumPRO.EnumLogType.检测单数据保存.getValue());
                DtoWorkSheetFolder workSheetFolder = workSheetFolderRepository.findOne(logs.get(0).getObjectId());
                //如果工作单状态不是新建、已经保存，或者操作类型不是检测单基本信息操作，检测单数据保存操作，就记录流程日志
                if (StringUtil.isNotNull(workSheetFolder)
                        && (!worksheetStatusNeedSaveOperateLog.contains(workSheetFolder.getStatus())
                        || !logTypeNeedSaveOperateLog.contains(logType))) {
                    List<DtoLogForWorkSheet> logW = logs.stream().map(DtoLogForWorkSheet::new).collect(Collectors.toList());
                    if (logW.size() > 0) {
                        commonRepository.insert(logW);
                    }
                }
            }
        }
        //如果关闭开关则正常保存流程日志
        else {
            List<DtoLogForWorkSheet> logW = logs.stream().map(DtoLogForWorkSheet::new).collect(Collectors.toList());
            if (logW.size() > 0) {
                commonRepository.insert(logW);
            }
        }
    }

    /**
     * 新增项目流程状态退回日志
     *
     * @param ids              对象ids
     * @param opinion          意见
     * @param nextOperatorId   下一步操作人
     * @param nextOperatorName 下一步操作人姓名
     */
    @Transactional
    @Override
    public void createProjectStatusBackLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName) {
        for (String id : ids) {
            DtoProject pro = projectRepository.findOne(id);

            String comment = String.format("退回项目状态为%s", pro.getStatus());
            if (StringUtils.isNotNullAndEmpty(opinion)) {
                comment += String.format(",意见:%s", opinion);
            }
            if (StringUtils.isNotNullAndEmpty(nextOperatorName)) {
                comment += String.format(",下一步操作人:%s", nextOperatorName);
            }
            comment += "。";

            DtoLog log = new DtoLog();
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo("退回项目");
            log.setNextOperatorId(StringUtils.isNotNullAndEmpty(nextOperatorId) ? nextOperatorId : UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName(StringUtils.isNotNullAndEmpty(nextOperatorName) ? nextOperatorName : "");
            log.setLogType(EnumPRO.EnumLogType.项目流程.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.项目.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            newLogBaseService.createLog(log);
        }
    }

    /**
     * 新增项目修改原因记录
     *
     * @param projectId 对象ids
     * @param opinion   意见
     */
    @Transactional
    @Override
    public void createProjectSchemeModifyLog(String projectId, String opinion) {
        String comment = String.format("%s修改了项目的方案", PrincipalContextUser.getPrincipal().getUserName());
        if (StringUtils.isNotNullAndEmpty(opinion)) {
            comment += String.format(",原因:%s", opinion);
        }
        comment += "。";

        DtoLog log = new DtoLog();
        log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
        log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
        log.setOperateTime(new Date());
        log.setOperateInfo("修改方案");
        log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
        log.setNextOperatorName("");
        log.setLogType(EnumPRO.EnumLogType.方案点位信息.getValue());
        log.setObjectId(projectId);
        log.setObjectType(EnumPRO.EnumLogObjectType.项目.getValue());
        log.setComment(comment);
        log.setOpinion(opinion);
        log.setRemark("");
        newLogBaseService.createLog(log);
    }


    /**
     * 新增项目流程状态审核日志
     *
     * @param ids              对象ids
     * @param opinion          意见
     * @param nextOperatorId   下一步操作人
     * @param nextOperatorName 下一步操作人姓名
     * @param signal           信号值
     */
    @Transactional
    @Override
    public void createProjectStatusAuditLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName, String signal) {
        //TODO:实际应用时写
    }

    /**
     * 新增编制报告转交日志
     *
     * @param projectId 项目id
     * @param oldId     老的id
     * @param newId     新的id
     */
    @Transactional
    @Override
    public void createUpdateReportMakerLog(String projectId, String oldId, String newId, String opinion) {
        String oldMan = "";
        if (StringUtils.isNotNullAndEmpty(oldId)) {
            oldMan = userService.findByUserId(oldId).getUserName();
        }
        String nextOperatorName = userService.findByUserId(newId).getUserName();

        String comment = String.format("将编制报告人%s转交为%s,原因是:%s,下一步操作人:%s。", oldMan, nextOperatorName, opinion, nextOperatorName);

        DtoLog log = new DtoLog();
        log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
        log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
        log.setOperateTime(new Date());
        log.setOperateInfo("转交报告");
        log.setNextOperatorId(newId);
        log.setNextOperatorName(nextOperatorName);
        log.setLogType(EnumPRO.EnumLogType.项目报告转交.getValue());
        log.setObjectId(projectId);
        log.setObjectType(EnumPRO.EnumLogObjectType.项目.getValue());
        log.setComment(comment);
        log.setOpinion("");
        log.setRemark("");
        newLogBaseService.createLog(log);

    }

    /**
     * 报告信息更新日志
     *
     * @param ids     对象ids
     * @param opinion 意见
     * @param type    类型
     */
    @Override
    public void createReportInfoUpdateLog(List<String> ids, String opinion, String type) {
        for (String id : ids) {
            DtoReport report = reportRepository.findOne(id);

            DtoLog log = new DtoLog();
            String operateInfo = "";
            String comment = "";
            String operatorId;
            String operatorName;
            if (type.equals(EnumPRO.EnumLogOperateType.删除报告.toString())) {
                operateInfo = "删除报告";
                comment = "删除报告。";
            } else if (type.equals(EnumPRO.EnumLogOperateType.增加报告.toString())) {
                operateInfo = "增加报告";
                comment = "增加报告。";
            } else if (type.equals(EnumPRO.EnumLogOperateType.修改报告.toString())) {
                operateInfo = "修改报告";
                comment = "修改报告。";
            }
            try {
                operatorId = PrincipalContextUser.getPrincipal().getUserId();
                operatorName = PrincipalContextUser.getPrincipal().getUserName();
            } catch (Exception ex) {
                operatorId = report.getReportMakerId();
                operatorName = report.getReportMakerName();
            }
            log.setOperatorId(operatorId);
            log.setOperatorName(operatorName);
            log.setOperateTime(new Date());
            log.setOperateInfo(operateInfo);
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName("");
            log.setLogType(EnumPRO.EnumLogType.项目报告.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.报告.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            newLogBaseService.createLog(log);
        }
    }

    /**
     * 报告发放流程更新日志
     *
     * @param ids  报告编号
     * @param type 流程类型
     */
    @Override
    public void createReportGrantInfoLog(List<String> ids, String type) {
        for (String id : ids) {
            DtoLog log = new DtoLog();
            String operateInfo = EnumPRO.EnumLogOperateType.更新报告状态.toString();
            DtoReport report = reportRepository.findOne(id);
            String comment = "";
            if ("新增发放".equals(type)) {
                if (report.getGrantStatus() == 2) {
                    continue;
                }
                comment = "新增了发放记录,发放状态更新为已发放";
            }
            if ("删除发放".equals(type)) {
                comment = "删除了发放记录,发放状态更新为未发放";
            }
            if ("新增回收".equals(type)) {
                if (report.getGrantStatus() == 3) {
                    continue;
                }
                comment = "新增了回收记录,发放状态更新为已回收";
            }
            if ("删除回收".equals(type)) {
                comment = "删除回收记录,发放状态更新为已发放";
            }
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(operateInfo);
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName("");
            log.setLogType(EnumPRO.EnumLogType.项目报告.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.报告.getValue());
            log.setComment(comment);
            log.setOpinion("");
            log.setRemark("");
            newLogBaseService.createLog(log);
        }
    }

    /**
     * 动态查询流程日志，整合web的场景
     *
     * @param criteria 动态参数
     * @return 流程日志
     */
    @Override
    public List<DtoLog> findNewLog(BaseCriteria criteria) {
        NewLogCriteria newLogCriteria = (NewLogCriteria) criteria;
        //项目进度传日志对象类型及项目id
        if (StringUtil.isNotNull(newLogCriteria.getLogObjectType()) && StringUtils.isNotNullAndEmpty(newLogCriteria.getProjectId())) {
            EnumPRO.EnumLogObjectType logObjectType = EnumPRO.EnumLogObjectType.getByValue(newLogCriteria.getLogObjectType());
            assert logObjectType != null;
            switch (logObjectType) {
                case 项目:
                    return this.getProjectLogByProjId(newLogCriteria.getProjectId());

                case 方案:
                    return this.getSchemeModifyLogByProjId(newLogCriteria.getProjectId());

                case 送样单:
                    return this.getReceiveLogByProjId(newLogCriteria.getProjectId());

                case 检测单:
                    List<DtoProject2WorkSheetFolder> p2wList = project2WorkSheetFolderRepository.findByProjectId(newLogCriteria.getProjectId());
                    return p2wList.size() > 0 ? this.getLogByWorkSheetIds(p2wList.stream().map(DtoProject2WorkSheetFolder::getWorkSheetFolderId).collect(Collectors.toList())) : new ArrayList<>();
                case 样品:
                    return this.getSampleLogByProjId(newLogCriteria.getProjectId());

                case 报告:
                    return this.getReportLogByProjId(newLogCriteria.getProjectId());

                case 费用管理:
                    DtoCostInfo costInfo = costInfoRepository.findByProjectId(newLogCriteria.getProjectId());
                    return this.getCostLogByCostId(costInfo.getId());
                default:
                    return new ArrayList<>();
            }
        }

        //费用的日志信息
        if (StringUtil.isNotNull(newLogCriteria.getLogObjectType()) && StringUtils.isNotNullAndEmpty(newLogCriteria.getCostId())) {

            EnumPRO.EnumLogObjectType logObjectType = EnumPRO.EnumLogObjectType.getByValue(newLogCriteria.getLogObjectType());
            assert logObjectType != null;
            switch (logObjectType) {
                case 费用管理:
                    return this.getCostLogByCostId(newLogCriteria.getCostId());
                default:
                    return new ArrayList<>();
            }
        }

        //一些场景传日志对象类型
        if (StringUtil.isNotNull(newLogCriteria.getLogObjectType())) {
            EnumPRO.EnumLogObjectType logObjectType = EnumPRO.EnumLogObjectType.getByValue(newLogCriteria.getLogObjectType());
            assert logObjectType != null;
            switch (logObjectType) {
                case 检测单:
                    LogForWorkSheetCriteria logForWorkSheetCriteria = new LogForWorkSheetCriteria();
                    logForWorkSheetCriteria.setStartTime(newLogCriteria.getStartTime());
                    logForWorkSheetCriteria.setEndTime(newLogCriteria.getEndTime());
                    logForWorkSheetCriteria.setOperatorId(newLogCriteria.getOperatorId());
                    PageBean<DtoLogForWorkSheet> pb = new PageBean<>();
                    pb.setEntityName("DtoLogForWorkSheet l");
                    pb.setSelect("select l");
                    pb.setRowsPerPage(Integer.MAX_VALUE);
                    commonRepository.findByPage(pb, logForWorkSheetCriteria);
                    List<DtoLog> logs = new ArrayList<>();
                    if (StringUtils.isNotNullAndEmpty(pb.getData()) && pb.getData().size() > 0) {
                        List<DtoLogForWorkSheet> workSheetLogs = pb.getData().stream().filter(p -> p.getLogType().equals(EnumPRO.EnumLogType.检测单增删.getValue()) ||
                                p.getLogType().equals(EnumPRO.EnumLogType.检测单流程.getValue())).collect(Collectors.toList());
                        List<DtoUser> uList = userService.findAll();
                        logs = dealLogs(workSheetLogs.parallelStream().map(DtoLog::new).collect(Collectors.toList()),
                                uList);
                    }
                    logs.sort(Comparator.comparing(DtoLog::getOperateTime).reversed());
                    return logs;

                default:
                    return new ArrayList<>();
            }
        }

        //现场任务传送样单id获取日志数据
        if (StringUtils.isNotNullAndEmpty(newLogCriteria.getReceiveId())) {
            return this.getLogByReceiveId(newLogCriteria.getReceiveId());
        }
        //实验室分析传检测单id获取日志
        if (StringUtils.isNotNullAndEmpty(newLogCriteria.getWorkSheetFolderId())) {
            return this.getLogByWorkSheetIds(Collections.singletonList(newLogCriteria.getWorkSheetFolderId()));
        }
        return new ArrayList<>();
    }

    /**
     * 查询评论需冗余的日志
     *
     * @param objectId   关联id
     * @param objectType 关联类型
     * @return 评论日志
     */
    @Override
    public List<DtoLog> findCommentLog(String objectId, Integer objectType) {
        EnumPRO.EnumCommentObjectType objType = EnumPRO.EnumCommentObjectType.getByValue(objectType);
        if (StringUtil.isNotNull(objType)) {
            assert objType != null;
            switch (objType) {
                case 项目:
                    return this.getProjectLogByProjId(objectId);

                case 报告:
                    List<DtoLog> logData = getReportLogByReportId(objectId);
                    logData = logData.stream().filter(p -> p.getOperateInfo().equals(EnumPRO.EnumLogOperateType.转交报告.toString()) ||
                            p.getOperateInfo().equals(EnumPRO.EnumLogOperateType.增加报告.toString()) ||
                            p.getOperateInfo().equals("更新报告状态") || p.getOperateInfo().equals("退回报告")
                            || p.getOperateInfo().equals(EnumPRO.EnumLogOperateType.修改报告.toString())).collect(Collectors.toList());
                    List<DtoReportDetail> reportDetails = reportDetailRepository.findByReportId(objectId);
                    List<String> samIds = reportDetails.stream().filter(p -> p.getObjectType().equals(EnumPRO.EnumReportDetailType.样品.getValue())).map(DtoReportDetail::getObjectId).collect(Collectors.toList());
                    if (samIds.size() > 0) {
                        //修改关联报告中样品的样品编号、点位名称、删除样品、增删测试项目、数据退回都会记录到“记录与评论”中
                        //目前相关日志均记录在样品的维度，不需要读取数据维度的日志
                        List<DtoLogForSample> sList = logForSampleRepository.findByObjectIdIn(samIds);
                        List<DtoLogForSample> logList = new ArrayList<>();
                        if (StringUtils.isNotNullAndEmpty(sList) && sList.size() > 0) {
                            logList.addAll(sList.stream().filter(p -> p.getOperateInfo().equals(EnumPRO.EnumLogOperateType.删除样品.toString()) ||
                                    p.getOperateInfo().equals(EnumPRO.EnumLogOperateType.增加检测项目.toString()) ||
                                    p.getOperateInfo().equals(EnumPRO.EnumLogOperateType.删除检测项目.toString()) ||
                                    p.getOperateInfo().equals(EnumPRO.EnumLogOperateType.退回数据.toString())).collect(Collectors.toList()));
                            logList.addAll(sList.stream().filter(p -> p.getOperateInfo().equals(EnumPRO.EnumLogOperateType.修改样品.toString()) && (
                                    p.getComment().contains("样品编号") || p.getComment().contains("点位名称")
                            )).collect(Collectors.toList()));
                            for (DtoLogForSample d : logList) {
                                DtoReportDetail reportDetail = reportDetails.stream().filter(p -> p.getObjectId().equals(d.getObjectId())).findFirst().orElse(null);
                                assert reportDetail != null;
                                if (StringUtil.isNotNull(reportDetail) && reportDetail.getCreateDate().before(d.getOperateTime())) {
                                    DtoLog log = new DtoLog(d);
                                    logData.add(log);
                                }
                            }
                        }
                    }
                    if (logData.size() > 0) {
                        List<DtoUser> uList = userService.findAll();
                        logData = dealLogs(logData, uList);
                    }
                    return logData;

                default:
                    return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取项目日志并根据日期排序（所有日志）
     *
     * @param projectId 项目id
     * @return 返回项目日志
     */
    @Override
    public List<DtoLog> getLogByProjectId(String projectId) {
        List<DtoUser> uList = userService.findAll();

        //项目日志
        List<DtoLogForProject> pList = logForProjectRepository.findByObjectId(projectId);
        List<DtoLog> logData = pList.parallelStream().map(DtoLog::new).collect(Collectors.toList());

        //项目方案日志
        List<DtoLogForPlan> plList = logForPlanRepository.findByObjectId(projectId);
        List<DtoLog> planLogDataList = plList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
        logData.addAll(planLogDataList);

        //项目报告日志
        List<DtoReport> reports = reportRepository.findByProjectId(projectId);
        if (StringUtils.isNotNullAndEmpty(reports) && reports.size() > 0) {
            List<String> reIds = reports.parallelStream().map(DtoReport::getId).collect(Collectors.toList());
            List<DtoLogForReport> reList = logForReportRepository.findByObjectIdIn(reIds);
            List<DtoLog> reLogDataList = reList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
            logData.addAll(reLogDataList);
        }

        //送样单、领样单日志
        List<String> pIds = new ArrayList<>();
        pIds.add(projectId);
        List<DtoReceiveSampleRecord> receiveSampleList = receiveSampleRecordRepository.findByProjectIdIn(pIds);

        if (StringUtils.isNotNullAndEmpty(receiveSampleList) && receiveSampleList.size() > 0) {
            List<String> rids = receiveSampleList.parallelStream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
            List<DtoReceiveSubSampleRecord> subList = receiveSubSampleRecordRepository.findByReceiveIdIn(rids);
            if (StringUtils.isNotNullAndEmpty(subList) && subList.size() > 0) {
                List<String> subIds = subList.parallelStream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
                rids.addAll(subIds);
            }
            List<DtoLogForRecord> rList = logForRecordRepository.findByObjectIdIn(rids);
            List<DtoLog> subLogDataList = rList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
            logData.addAll(subLogDataList);
        }

        //样品日志
        List<DtoSample> samList = sampleRepository.findByProjectIdContainsIsDeleted(projectId).stream().filter(s -> !s.getIsDeleted() || s.getStatus().equals(EnumPRO.EnumSampleStatus.样品作废.toString())).collect(Collectors.toList());
        if (StringUtils.isNotNullAndEmpty(samList) && samList.size() > 0) {
            List<String> sids = samList.parallelStream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoLogForSample> sList = logForSampleRepository.findByObjectIdIn(sids);
            List<DtoLog> sampleLogDataList = sList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
            if (sampleLogDataList.size() > 0) {
                logData.addAll(sampleLogDataList);
            }

            //数据日志
            List<DtoAnalyseData> anaList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sids);
            if (StringUtils.isNotNullAndEmpty(anaList) && anaList.size() > 0) {
                List<String> aids = anaList.parallelStream().map(DtoAnalyseData::getId).collect(Collectors.toList());

                List<DtoLogForData> dList = logForDataRepository.findByObjectIdIn(aids);
                List<DtoLog> anaLogDataList = dList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
                logData.addAll(anaLogDataList);

                //检测单日志
                List<String> workIds = anaList.stream().filter(a -> !a.getWorkSheetId().equals(UUIDHelper.GUID_EMPTY)).map(AnalyseData::getWorkSheetId).collect(Collectors.toList());
                List<DtoLogForWorkSheet> wList = logForWorkSheetRepository.findByObjectIdIn(workIds);
                List<DtoLog> workSheetLogDataList = wList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
                logData.addAll(workSheetLogDataList);
            }
        }
        logData = dealProjectLogs(logData, uList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取项目日志并根据日期排序（仅流程日志）
     *
     * @param projectId 项目id
     * @return 返回项目日志
     */
    @Override
    public List<DtoLog> getProjectLogByProjId(String projectId) {
        List<DtoUser> uList = userService.findAll();
        //项目日志
        List<DtoLogForProject> pList = logForProjectRepository.findByObjectId(projectId);
        List<DtoLog> logData = pList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
        logData = dealProjectLogs(logData, uList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取项目报告日志并根据日期排序
     *
     * @param projectId 项目id
     * @return 返回项目日志
     */
    @Override
    public List<DtoLog> getReportLogByProjId(String projectId) {
        List<DtoLog> logData = new ArrayList<>();
        List<DtoUser> uList = userService.findAll();

        List<DtoReport> reports = reportRepository.findByProjectId(projectId);
        if (StringUtils.isNotNullAndEmpty(reports) && reports.size() > 0) {
            List<String> reIds = reports.stream().map(DtoReport::getId).collect(Collectors.toList());
            List<DtoLogForReport> reList = logForReportRepository.findByObjectIdIn(reIds);
            List<DtoLog> reportLogList = reList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
            logData.addAll(reportLogList);
        }
        logData = dealProjectLogs(logData, uList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 根据报告id获取项目报告日志并根据日期排序
     *
     * @param reportId 报告id
     * @return 返回报告日志
     */
    @Override
    public List<DtoLog> getReportLogByReportId(String reportId) {
        List<DtoUser> uList = userService.findAll();
        List<DtoLogForReport> reList = logForReportRepository.findByObjectId(reportId);
        List<DtoLog> logData = reList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
        logData = dealProjectLogs(logData, uList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取检测单相关日志并根据日期排序
     *
     * @param workSheetFolderIds 工作单ids
     * @return 返回工作单日志
     */
    @Override
    public List<DtoLog> getLogByWorkSheetIds(List<String> workSheetFolderIds) {
        List<DtoLog> logData = new ArrayList<>();
        try {
            List<DtoUser> uList = userService.findAll();
            List<DtoWorkSheetFolder> workSheetFolders = workSheetFolderRepository.findByIdIn(workSheetFolderIds);
            List<String> wfList = workSheetFolders.stream().sorted(Comparator.comparing(DtoWorkSheetFolder::getWorkSheetCode))
                    .map(DtoWorkSheetFolder::getId).collect(Collectors.toList());
            List<String> filterStrs = new ArrayList<>();
            for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
                filterStrs.add("保存了检测单" + workSheetFolder.getWorkSheetCode() + "。");
            }

            List<DtoWorkSheet> works = workSheetRepository.findByParentIdIn(workSheetFolderIds);
            List<String> workIds = works.stream().map(DtoWorkSheet::getId).collect(Collectors.toList());
            workIds.addAll(wfList);

            List<DtoLogForWorkSheet> wList = logForWorkSheetRepository.findByObjectIdIn(workIds);
            List<DtoLog> logDataByWorks = wList.parallelStream().map(DtoLog::new).collect(Collectors.toList());

            Optional<Date> minOperateTimeOp = logDataByWorks.stream().filter(l -> EnumPRO.EnumLogOperateType.提交检测单.name().equals(l.getOperateInfo())).map(DtoLog::getOperateTime).min(Comparator.comparing(d -> d));
            Date minOperateTime = new Date();
            if (minOperateTimeOp.isPresent()) {
                minOperateTime = minOperateTimeOp.get();
            }
            final Date finalMinOperateTime = minOperateTime;

            List<Integer> logTypeNames = Arrays.asList(EnumPRO.EnumLogType.检测单增删.getValue());
            logDataByWorks = logDataByWorks.stream().filter(l -> logTypeNames.contains(l.getLogType()) || l.getOperateTime().compareTo(finalMinOperateTime) >= 0).collect(Collectors.toList());

            List<DtoAnalyseData> anaList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workIds);
            List<String> anaIds = anaList.stream().map(AnalyseData::getId).collect(Collectors.toList());

            List<DtoLogForData> aList = logForDataRepository.findByObjectIdIn(anaIds).stream().filter(l -> l.getOperateTime().compareTo(finalMinOperateTime) >= 0).collect(Collectors.toList());
            List<DtoLog> anaLogList = aList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
            logDataByWorks.addAll(anaLogList);
            for (String filter : filterStrs) {
                logDataByWorks.removeIf(l -> l.getComment().endsWith(filter));
            }

            for (String workSheetFolderId : wfList) {
                //检测单日志

                List<String> objectIds = works.stream().filter(w -> w.getParentId().equals(workSheetFolderId)).map(DtoWorkSheet::getId).distinct().collect(Collectors.toList());

                objectIds.add(workSheetFolderId);
                objectIds.addAll(anaList.stream().filter(a -> objectIds.contains(a.getWorkSheetId())).map(DtoAnalyseData::getId).distinct().collect(Collectors.toList()));

                List<DtoLog> logDataByWork = logDataByWorks.stream().filter(d -> objectIds.contains(d.getObjectId())).sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
                logDataByWork.forEach(l -> l.setTempId(workSheetFolderId));
                logData.addAll(logDataByWork);
            }
            logData = dealLogs(logData, uList);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取现场领样单相关日志并根据日期排序
     *
     * @param objectId 现场领样单id
     * @return 返回  现场领样单日志
     */
    @Override
    public List<DtoLog> getLogByLocalSubReceiveId(String objectId) {
        List<DtoUser> uList = userService.findAll();
        //领样单日志
        List<DtoLogForRecord> reList = logForRecordRepository.findByObjectId(objectId);
        List<DtoLog> logData = reList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
        List<DtoReceiveSubSampleRecord2Sample> list = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordId(objectId);
        if (StringUtils.isNotNullAndEmpty(list) && list.size() > 0) {
            List<String> samIds = list.stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).collect(Collectors.toList());
            List<DtoAnalyseData> anaList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(samIds);
            if (StringUtils.isNotNullAndEmpty(anaList) && anaList.size() > 0) {
                List<String> anaIds = anaList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                List<DtoLogForData> aList = logForDataRepository.findByObjectIdIn(anaIds);
                List<DtoLog> anaLogs = aList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
                logData.addAll(anaLogs);
            }
        }
        logData = dealLogs(logData, uList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取送样单相关日志并根据日期排序
     *
     * @param receiveId 送样单id
     * @return 返回送样单日志
     */
    @Override
    public List<DtoLog> getLogByReceiveId(String receiveId) {
        List<DtoLog> logData = new ArrayList<>();
        List<DtoUser> uList = userService.findAll();

        //送样单日志
        List<DtoLogForRecord> reList = logForRecordRepository.findByObjectId(receiveId);
        List<DtoLog> rLogDataList = reList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
        if (rLogDataList.size() > 0) {
            logData.addAll(rLogDataList);
        }

        //现场领样单日志
        List<DtoReceiveSubSampleRecord> subRecList = receiveSubSampleRecordRepository.findByReceiveId(receiveId);
        DtoReceiveSubSampleRecord locSub = subRecList.parallelStream().filter(p -> (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0).findFirst().orElse(null);
        if (StringUtil.isNotNull(locSub)) {
            //送样单日志
            List<DtoLogForRecord> sList = logForRecordRepository.findByObjectId(locSub.getId());
            List<DtoLog> subLogDataList = sList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
            if (subLogDataList.size() > 0) {
                logData.addAll(subLogDataList);
            }
        }

        //样品日志
        List<DtoSample> samList = sampleRepository.findByReceiveId(receiveId).stream().filter(s -> !s.getIsDeleted() || s.getStatus().equals(EnumPRO.EnumSampleStatus.样品作废.toString())).collect(Collectors.toList());
        if (StringUtils.isNotNullAndEmpty(samList) && samList.size() > 0) {
            List<String> sids = samList.parallelStream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoLogForSample> sList = logForSampleRepository.findByObjectIdIn(sids);
            List<DtoLog> sampleLogDataList = sList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
            if (sampleLogDataList.size() > 0) {
                logData.addAll(sampleLogDataList);
            }

            //数据日志
            //现场任务隐藏数据日志 （BUG2023053196281 11、现场任务，现场数据录入，保存按钮会有两种日志，操作为修改量纲后保存）
//            List<DtoAnalyseData> anaList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sids);
//            if (StringUtils.isNotNullAndEmpty(anaList) && anaList.size() > 0) {
//                List<String> aids = anaList.parallelStream().map(AnalyseData::getId).collect(Collectors.toList());
//                List<DtoLogForData> dList = logForDataRepository.findByObjectIdIn(aids);
//                List<DtoLog> anaLogDataList = dList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
//                if (anaLogDataList.size() > 0) {
//                    logData.addAll(anaLogDataList);
//                }
//            }
        }

        logData = dealLogs(logData, uList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 项目相关送样单流程日志
     *
     * @param projectId 项目id
     * @return 返回数据
     */
    @Override
    public List<DtoLog> getReceiveLogByProjId(String projectId) {
        List<DtoLog> logData = new ArrayList<>();
        List<DtoUser> uList = userService.findAll();

        //送样单日志
        List<String> ids = new ArrayList<>();
        ids.add(projectId);
        List<DtoReceiveSampleRecord> rsrList = receiveSampleRecordRepository.findByProjectIdIn(ids);
        if (StringUtils.isNotNullAndEmpty(rsrList) && rsrList.size() > 0) {
            List<String> rsrIds = rsrList.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
            List<DtoReceiveSubSampleRecord> subList = receiveSubSampleRecordRepository.findByReceiveIdIn(rsrIds);
            if (StringUtils.isNotNullAndEmpty(subList) && subList.size() > 0) {
                rsrIds.addAll(subList.stream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList()));
            }

            List<DtoLogForRecord> reList = logForRecordRepository.findByObjectIdIn(rsrIds);

            List<DtoLog> recordLogs = reList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
            logData.addAll(recordLogs);
        }
        logData = dealLogs(logData, uList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取项目方案日志并根据日期排序（方案修改日志）
     *
     * @param projectId 项目id
     * @return 返回项目日志
     */
    @Override
    public List<DtoLog> getSchemeModifyLogByProjId(String projectId) {

        List<DtoUser> uList = userService.findAll();

        //日志
        List<DtoLogForPlan> plList = logForPlanRepository.findByObjectId(projectId);
        List<DtoLog> logData = plList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
        logData = dealLogs(logData, uList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());

    }

    /**
     * 获取项目相关样品及数据日志
     *
     * @param projectId 项目id
     * @return 获取项目相关样品及数据日志
     */
    @Override
    public List<DtoLog> getSampleLogByProjId(String projectId) {
        List<DtoLog> logData = new ArrayList<>();
        List<DtoUser> uList = userService.findAll();

        //样品日志
        List<DtoSample> samList = sampleRepository.findByProjectIdOrderById(projectId).stream().filter(s -> !s.getIsDeleted() || s.getStatus().equals(EnumPRO.EnumSampleStatus.样品作废.toString())).collect(Collectors.toList());
        if (StringUtils.isNotNullAndEmpty(samList) && samList.size() > 0) {
            List<String> sids = samList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoSample> associateSamples = sampleRepository.findByAssociateSampleIdIn(sids);
            sids.addAll(associateSamples.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.串联样.getValue()) || p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue()))
                    .map(DtoSample::getId).collect(Collectors.toList()));
            List<DtoLogForSample> sList = logForSampleRepository.findByObjectIdIn(sids);
            List<DtoLog> sampleLogList = sList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
            logData.addAll(sampleLogList);


            //数据日志
            if (sids.size() > 0) {
                Map<String, Object> values = new HashMap<>();
                StringBuilder stringBuilder = new StringBuilder("select l");
                stringBuilder.append(" from DtoLogForData l,DtoAnalyseData a ");
                stringBuilder.append(" where l.objectId = a.id");
                stringBuilder.append(" and a.sampleId in :sampleIds");
                if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
                    stringBuilder.append(" and a.orgId = :orgId");
                    values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
                }
                stringBuilder.append(" and a.isDeleted = 0");
                values.put("sampleIds", sids);
                List<DtoLogForData> dList = commonRepository.find(stringBuilder.toString(), values);
                List<DtoLog> anaLogList = dList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
                logData.addAll(anaLogList);
            }
        }
        logData = dealLogs(logData, uList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * @param objectId         对象id
     * @param comment          描述
     * @param opinion          意见
     * @param logType          日志类型
     * @param objectType       对象类型
     * @param operateInfo      操作信息
     * @param operatorId       操作人
     * @param operatorName     操作人姓名
     * @param nextOperatorId   下一步操作人
     * @param nextOperatorName 下一步操作人姓名
     */
    @Override
    public void createLog(String objectId, String comment, String opinion, int logType, int objectType, String operateInfo, String operatorId, String operatorName, String nextOperatorId, String nextOperatorName) {
        newLogBaseService.createLog(
                objectId,
                comment,
                opinion,
                logType,
                objectType,
                operateInfo,
                operatorId,
                operatorName,
                nextOperatorId,
                nextOperatorName);
    }


    @Transactional
    @Override
    public void createCostStatusUpdateLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName) {
        for (String id : ids) {
            DtoCostInfo costInfo = costInfoRepository.findOne(id);

            DtoLog log = new DtoLog();
            String comment;
            String operateInfo;
            if (!costInfo.getIsDeleted()) {
                operateInfo = "更新费用状态";
                comment = String.format("更新费用状态为%s", costInfo.getStatus());
                if (StringUtils.isNotNullAndEmpty(opinion)) {
                    comment += String.format(",意见:%s", opinion);
                }
                if (StringUtils.isNotNullAndEmpty(nextOperatorName)) {
                    comment += String.format(",下一步操作人:%s", nextOperatorName);
                }
                comment += "。";
            } else {
                operateInfo = "删除费用";
                comment = "删除费用。";
            }
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(operateInfo);
            log.setNextOperatorId(StringUtils.isNotNullAndEmpty(nextOperatorId) ? nextOperatorId : UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName(StringUtils.isNotNullAndEmpty(nextOperatorName) ? nextOperatorName : "");
            log.setLogType(EnumPRO.EnumLogType.费用流程.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.费用管理.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            newLogBaseService.createLog(log);
        }
    }


    @Transactional
    @Override
    public void createCostStatusBackLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName) {
        for (String id : ids) {
            DtoCostInfo costInfo = costInfoRepository.findOne(id);

            String comment = String.format("退回费用状态为%s", costInfo.getStatus());
            if (StringUtils.isNotNullAndEmpty(opinion)) {
                comment += String.format(",意见:%s", opinion);
            }
            if (StringUtils.isNotNullAndEmpty(nextOperatorName)) {
                comment += String.format(",下一步操作人:%s", nextOperatorName);
            }
            comment += "。";

            DtoLog log = new DtoLog();
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo("退回费用");
            log.setNextOperatorId(StringUtils.isNotNullAndEmpty(nextOperatorId) ? nextOperatorId : UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName(StringUtils.isNotNullAndEmpty(nextOperatorName) ? nextOperatorName : "");
            log.setLogType(EnumPRO.EnumLogType.费用流程.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.费用管理.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            newLogBaseService.createLog(log);
        }
    }

    @Transactional
    @Override
    public void createSampleInvalidLog(List<String> ids, String opinion, String actionStr) {
        List<DtoSample> sampleList = StringUtil.isNotEmpty(ids) ? sampleRepository.findAll(ids) : new ArrayList<>();
        Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, p -> p));
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeRepository.findAll(sampleTypeIds) : new ArrayList<>();
        Map<String, DtoSampleType> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, p -> p));
        for (String id : ids) {
            DtoSample sample = sampleMap.getOrDefault(id, new DtoSample());
            DtoSampleType sampleType = sampleTypeMap.getOrDefault(sample.getSampleTypeId(), new DtoSampleType());
            String comment = String.format("%s作废了样品%s(%s)-%s", PrincipalContextUser.getPrincipal().getUserName(),
                    sample.getRedFolderName(), sampleType.getTypeName(), sample.getCode());
            if (actionStr.contains("取消作废")) {
                comment = String.format("%s取消作废了样品%s(%s)-%s", PrincipalContextUser.getPrincipal().getUserName(),
                        sample.getRedFolderName(), sampleType.getTypeName(), sample.getCode());
            }
            if (StringUtils.isNotNullAndEmpty(opinion)) {
                comment += String.format(",原因:%s", opinion);
            }
            comment += "。";

            DtoLog log = new DtoLog();
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(actionStr);
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName("");
            log.setLogType(EnumPRO.EnumLogType.样品增删作废.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.样品.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            newLogBaseService.createLog(log);
        }
    }

    /**
     * 订单操作日志
     *
     * @param orderIds 订单ids
     * @return 日志数据
     */
    @Override
    public List<DtoLog> getLogByOrderIds(List<String> orderIds) {
        //项目日志
        List<DtoLogForCost> pList = logForCostRepository.findByObjectIdIn(orderIds);
        List<String> userIds = pList.stream().map(DtoLogForCost::getOperatorId).distinct().collect(Collectors.toList());
        List<DtoUser> userList = userService.findByIds(userIds);
        List<DtoLog> logData = pList.stream().map(DtoLog::new).collect(Collectors.toList());
        logData = dealLogs(logData, userList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 费用的日志
     *
     * @param costId 费用id
     * @return 返回日志数据
     */
    private List<DtoLog> getCostLogByCostId(String costId) {
        //项目日志
        List<DtoLogForCost> pList = logForCostRepository.findByObjectId(costId);
        List<String> userIds = pList.stream().map(DtoLogForCost::getOperatorId).distinct().collect(Collectors.toList());

        List<DtoUser> userList = userService.findByIds(userIds);

        List<DtoLog> logData = pList.stream().map(DtoLog::new).collect(Collectors.toList());
        logData = dealLogs(logData, userList);
        return logData.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }


    /**
     * 处理日志信息
     *
     * @param logs     传入过来的日志
     * @param userList 用户信息
     * @return 返回新的日志
     */
    @Override
    public List<DtoLog> dealLogs(List<DtoLog> logs, List<DtoUser> userList) {
        for (DtoLog log : logs) {
            DtoUser dtoUser = userList.stream().filter(u -> u.getId().equals(log.getOperatorId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoUser)) {
                assert dtoUser != null;
                log.setOperatorName(dtoUser.getUserName());
            }
            log.setAllComment(String.format("%s于%s,%s", log.getOperatorName(), log.getOperateTime(), log.getComment()));
            log.setLogTypeName(EnumPRO.EnumLogType.getName(log.getLogType()));
            log.setObjectTypeName(EnumPRO.EnumLogObjectType.getName(log.getObjectType()));
        }
        return logs;
    }

    /**
     * 处理项目日志
     *
     * @param logs     传入过来的日志
     * @param userList 用户信息
     * @return 返回新的日志
     */
    private List<DtoLog> dealProjectLogs(List<DtoLog> logs, List<DtoUser> userList) {
        for (DtoLog log : logs) {
            DtoUser dtoUser = userList.stream().filter(u -> u.getId().equals(log.getOperatorId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoUser)) {
                assert dtoUser != null;
                log.setOperatorName(dtoUser.getUserName());
            }
            log.setAllComment(String.format("%s于%s,%s", log.getOperatorName(), log.getOperateTime(), log.getComment()));
            if (log.getLogType().equals(EnumPRO.EnumLogType.项目信息.getValue()) && log.getComment().contains("修改")) {
                log.setAllComment(log.getAllComment() + log.getOpinion());
            }
            log.setLogTypeName(EnumPRO.EnumLogType.getName(log.getLogType()));
            log.setObjectTypeName(EnumPRO.EnumLogObjectType.getName(log.getObjectType()));
        }
        return logs;
    }
}
