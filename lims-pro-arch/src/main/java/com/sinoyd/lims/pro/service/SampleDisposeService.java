package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoSampleDispose;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.customer.DtoSampleDisposeTemp;

import java.util.List;


/**
 * SampleDispose操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface SampleDisposeService extends IBaseJpaService<DtoSampleDispose, String> {

    /**
     * 留样处置
     *
     * @param disposeTemp 要处置的留样list
     * @return 处置完成的留样
     */
    List<DtoSampleDispose> disposal(DtoSampleDisposeTemp disposeTemp);

    /**
     * 查看样品是否已经留样
     *
     * @param sampleId 样品id
     * @return 是否已经留样true为已经留样，true为已经留样，false为未留样
     */
    Boolean checkSampleIsDisposed(String sampleId);

    /**
     * 查看多个样品是否已经留样
     *
     * @param sampleDisposes 留样管理实体
     * @return 是否已经留样true为已经留样，true为已经留样，false为未留样
     */
    Boolean checkSamplesIsDisposed(DtoSampleDispose sampleDisposes);

    /**
     * 样品批量留样
     *
     * @param sampleDispose 样品留样信息列表
     * @return 数据
     */
    DtoSampleDispose batchSave(DtoSampleDispose sampleDispose);

}