-- 社会生活噪声报告组件及报表配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('0938cece-bc0b-4376-b886-3157a5cb844e', '5549a3a3-4441-4deb-a2a0-e57956f89db0', 'ReportEditNew', '报告编制V2.0',
        'SocialNoiseStd', '社会生活噪声报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:26:37', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:26:37', NULL);

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, sampleTypeIds)
VALUES ('29eb0d71-b4df-4410-aa4d-1862894da8e4', '社会生活噪声报告', 3, '5549a3a3-4441-4deb-a2a0-e57956f89db0',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-07-04 15:37:47', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-07-04 15:37:47', '');


INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('4f5f20ad-e13c-4b31-aa04-bd89bc33aac7', 'dtSocialNoiseHeadStdTable', '标准版社会生活噪声表头组件',
        'dtSocialNoiseHeadStdTable', '', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:05:33', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:05:33', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('29b12294-f422-4b0a-8d8d-46c888532429', 'dtSocialNoiseTable', '标准版社会生活噪声检测结果表组件', 'dtSocialNoiseTable',
        'dtSocialNoiseSource', 10, 7, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:11:26', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:11:26', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('7c8e8608-d827-456c-9193-478028d4060d', 'socialNoiseDataSource', '标准版社会生活噪声报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtSocialNoiseHeadStdTable\", \"dtSocialNoiseTable\"]', b'1', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:56:09', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:56:09', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('3f63bfa2-7491-4b02-978e-fad4e0b2d472', '5549a3a3-4441-4deb-a2a0-e57956f89db0',
        '7c8e8608-d827-456c-9193-478028d4060d');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('564e2ef0-c6e4-42bf-9ab3-0ed82d791039', '5549a3a3-4441-4deb-a2a0-e57956f89db0',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('964492c4-8e0a-4300-8676-c6fdf1a29173', '5549a3a3-4441-4deb-a2a0-e57956f89db0',
        '44c0ef90-1cee-4379-bdec-e502a875c0a7');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('afc60d36-16b6-4212-88ad-2cf075be3f5d', '5549a3a3-4441-4deb-a2a0-e57956f89db0',
        '7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('bc0d0eb1-6dce-414b-83eb-70d442d74d98', '5549a3a3-4441-4deb-a2a0-e57956f89db0',
        'd75b5323-5558-4581-8340-29aa43c39e46');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('be124669-de72-4576-8c2f-6d3859f3c981', '5549a3a3-4441-4deb-a2a0-e57956f89db0',
        '3477d948-c1eb-418f-8fbd-81fc1295cee6');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('cb40fedd-a864-43ad-9c2c-0c07d236c8c5', '5549a3a3-4441-4deb-a2a0-e57956f89db0',
        '6130da13-013a-4a25-a856-bd1f16fc691b');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('d48b1f86-9019-40b2-b443-485accbae343', '5549a3a3-4441-4deb-a2a0-e57956f89db0',
        '844acab5-8883-44d1-b8fa-af039526e967');

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('30b95b8d-4fb1-49ab-8976-5f1236869b26', '3f63bfa2-7491-4b02-978e-fad4e0b2d472',
        'sampleData_samplingTimeBegin_dateGroupType', 9);

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('5aabda90-90ef-487f-8782-01e8da1008f4', '3f63bfa2-7491-4b02-978e-fad4e0b2d472',
        'sampleData_sampleFolderId_fieldGroupType', 10);


-- 废气比对报告模板及组件配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('d0381ba9-21e0-4d10-bae3-ee12c7650eb9', '01d3c202-a3f5-43dd-8813-07531d5d0544', 'ReportEditNew', '报告编制V2.0',
        'GasCompareStd', '废气比对报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 20:58:41', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 20:58:41', NULL);

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, sampleTypeIds)
VALUES ('13797f74-0b93-4622-a34a-4b12f2d9538a', '废气比对报告', 3, '01d3c202-a3f5-43dd-8813-07531d5d0544',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-07-09 21:01:49', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-07-09 21:01:49', '');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('f4383146-83e4-451a-94c8-df5babbdb25b', 'dtGasCompareTable', '标准版废气比对检测结果表组件', 'dtGasCompareTable',
        'dtGasCompareSource', 10000, 1, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:05:59', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:05:59', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('4dbc7c06-391d-4111-84d6-cfd058d72494', 'dtGasCompareDivTable', '标准版废气比对检测结果表组件（数据对差）', 'dtGasCompareDivTable',
        'dtGasCompareDivSource', 10000, 1, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:08:20', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:08:20', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('030ab6a7-b101-4b40-bfd8-cee47b6b1250', 'dtGasCompareInstTable', '标准版废气比对仪器信息表组件', 'dtGasCompareInstTable',
        'dtGasCompareInstSource', 10000, 1, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:09:38', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 21:09:38', '0', '0', 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode)
VALUES ('f94f8a55-1fb0-42c1-a87b-d4b078a2c697', 'gasCompareDataSource', '标准版废气比对报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtGasCompareTable\", \"dtGasCompareDivTable\", \"dtGasCompareInstTable\", \"outParallelStdDataSource\"]',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-07-09 21:12:13',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-07-09 21:12:13', '0', '0',
        0, 0, 0);

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('145fb920-ea93-4c86-a857-5eec70a6a0e2', '01d3c202-a3f5-43dd-8813-07531d5d0544',
        '6130da13-013a-4a25-a856-bd1f16fc691b');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('1d13f472-d2d1-45ac-bf97-63f70021d2b5', '01d3c202-a3f5-43dd-8813-07531d5d0544',
        '7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('25925ff3-a75f-4a09-8881-60eab0746a12', '01d3c202-a3f5-43dd-8813-07531d5d0544',
        'f94f8a55-1fb0-42c1-a87b-d4b078a2c697');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('53897e46-7153-42f5-9b86-af74d86bd4f4', '01d3c202-a3f5-43dd-8813-07531d5d0544',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('60cdfded-dead-4bca-929a-f916df781ff1', '01d3c202-a3f5-43dd-8813-07531d5d0544',
        '4327d2ae-f104-418e-be74-67f4072d06ec');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('7e7d3055-f7f4-4f3b-8b0b-126b48dd810b', '01d3c202-a3f5-43dd-8813-07531d5d0544',
        '3477d948-c1eb-418f-8fbd-81fc1295cee6');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('8063e2cb-c9ad-4b9d-ac01-0ef67b085542', '01d3c202-a3f5-43dd-8813-07531d5d0544',
        '44c0ef90-1cee-4379-bdec-e502a875c0a7');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('edc33266-095f-4865-a30c-6dbcd0238c13', '01d3c202-a3f5-43dd-8813-07531d5d0544',
        '844acab5-8883-44d1-b8fa-af039526e967');

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('70163774-258c-48c5-9a46-2e1f43a074ab', '25925ff3-a75f-4a09-8881-60eab0746a12',
        'sampleData_samplingTimeBegin_dateGroupType', 9);

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('d75da990-3cf2-486e-a368-4aee31e4b039', '25925ff3-a75f-4a09-8881-60eab0746a12',
        'sampleData_sampleFolderId_fieldGroupType', 10);
