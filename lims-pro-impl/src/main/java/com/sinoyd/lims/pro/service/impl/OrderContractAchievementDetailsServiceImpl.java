package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.dto.DtoOrderContractAchievementDetails;
import com.sinoyd.lims.pro.repository.OrderContractAchievementDetailsRepository;
import com.sinoyd.lims.pro.service.OrderContractAchievementDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OrderContractAcihievementDetails操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Service
public class OrderContractAchievementDetailsServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoOrderContractAchievementDetails, String, OrderContractAchievementDetailsRepository> implements OrderContractAchievementDetailsService {

    private PersonService personService;

    @Override
    public void findByPage(PageBean<DtoOrderContractAchievementDetails> page, BaseCriteria criteria) {
        page.setEntityName("DtoOrderContractAchievementDetails a");
        page.setSelect("select a");
        page.setSort("a.signDate-, a.contractCode-");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData());
    }

    @Override
    public void export(HttpServletResponse response, BaseCriteria criteria) {
        PageBean<DtoOrderContractAchievementDetails> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        page.setSort("a.signDate-, a.contractCode-");
        findByPage(page, criteria);
        PoiExcelUtils.exportExcel(page.getData(),null,"合同明细表格",DtoOrderContractAchievementDetails.class,"合同明细表格" + "_" + DateUtil.nowTime("yyyyMMddHHmmss"),response);
    }

    /**
     * 填充冗余字段
     * @param details 合同明细
     */
    private void fillingTransientFields(List<DtoOrderContractAchievementDetails> details) {
        Set<String> personIds = new HashSet<>();
        details.forEach(d->{
            personIds.addAll(Arrays.asList(d.getSignPersonId().split(",")));
        });
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
        details.forEach(d->{
            List<String> personIds2Detail = Arrays.asList(d.getSignPersonId().split(","));
            List<String> personNames = personList.stream().filter(p->personIds2Detail.contains(p.getId())).map(DtoPerson::getCName).collect(Collectors.toList());
            d.setSignPersonName(String.join(",", personNames));
        });
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }
}
