package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.CostInfoDetail;

import java.math.BigDecimal;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoCostInfoDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_CostInfoDetail")
 @Data
 @DynamicInsert
 public  class DtoCostInfoDetail extends CostInfoDetail {
    private static final long serialVersionUID = 1L;

    /**
     * 采样总费
     */
    @Transient
    private BigDecimal samplingTotalCost;

    /**
     * 分析总费
     */
    @Transient
    private BigDecimal analyzeTotalCost;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;
}