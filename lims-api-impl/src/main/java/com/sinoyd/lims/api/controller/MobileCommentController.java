package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoComment;
import com.sinoyd.lims.pro.service.CommentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Comment服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: Comment服务")
@RestController
@RequestMapping("api/mobile/comment")
public class MobileCommentController extends BaseJpaController<DtoComment, String, CommentService> {
    /**
     * 获取评论与日志
     * @param objectId 关联id
     * @param objectType 关联类型
     * @return RestResponse<String>
     */
    @ApiOperation(value = "获取评论与日志", notes = "获取评论与日志")
    @GetMapping(path = "/all")
    public RestResponse<DtoComment[]> createReportCode(@RequestParam(name = "objectId") String objectId, @RequestParam(name = "objectType") Integer objectType) {
        RestResponse<DtoComment[]> restResponse = new RestResponse<>();
        restResponse.setData(service.findAllComment(objectId, objectType));
        return restResponse;
    }
}
