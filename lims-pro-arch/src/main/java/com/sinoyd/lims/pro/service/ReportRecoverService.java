package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoReportRecover;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * reportrecover操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
public interface ReportRecoverService extends IBaseJpaService<DtoReportRecover, String> {
    /**
     * 获取项目关联的回收信息
     *
     * @param projectId 项目id
     * @param status 发放状态
     * @return 回收信息
     */
    List<DtoReportRecover> findRecover(String projectId, String status);

    /**
     * 查询项目已发放的关联报告
     *
     * @param projectId 项目id
     * @return 关联报告
     */
    List<DtoReport> findReport(String projectId);
}