package com.sinoyd.lims.pro.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import java.util.Date;

/**
 * 采样安排计划表Poi实体
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2024/11/14
 **/
@Data
public class DtoPoiSamplingArrange implements IExcelDataModel, IExcelModel {

    @Excel(name = "日期", orderNum = "100", width = 11,format = "yyyy-MM-dd")
    private Date planSamplingTime;

    @Excel(name = "采样负责人", orderNum = "110", width = 11)
    private String chargePerson;

    @Excel(name = "采样人员", orderNum = "120", width = 11)
    private String samplingPeople;

    @Excel(name = "采样车辆", orderNum = "130", width = 11)
    private String car;

    @Excel(name = "项目编号", orderNum = "140", width = 11)
    private String projectCode;

    @Excel(name = "项目名称", orderNum = "150", width = 11)
    private String projectName;

    @Excel(name = "点位名称", orderNum = "160", width = 11)
    private String watchSpot;

    @Excel(name = "点位编号", orderNum = "170", width = 11)
    private String folderCode;

    @Excel(name = "检测类型", orderNum = "180", width = 11)
    private  String sampleType;

    @Excel(name = "周期", orderNum = "190", width = 11)
    private Integer periodCount;

    @Excel(name = "采样样品数", orderNum = "200", width = 11)
    private Integer sampleCount;

    private Integer rowNum;

    private String errorMsg;

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }
}
