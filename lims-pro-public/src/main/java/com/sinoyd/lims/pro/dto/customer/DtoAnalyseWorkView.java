package com.sinoyd.lims.pro.dto.customer;


import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分析人员工作总览
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoAnalyseWorkView {


    /**
     * 分析日历的数据集
     */
    private List<Map<String, Object>> analyseCalendar = new ArrayList<>();

    /**
     * 分析计划的数据集合
     */
    private List<Map<String, Object>> analysePlan = new ArrayList<>();

    /**
     * 分析统计的数据集合
     */
    private List<Map<String, Object>> analyzeStatistics = new ArrayList<>();


    /**
     * 分析状态的数据集合
     */
    private Map<String, Object> analyzeStatus = new HashMap<>();
}
