package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoImportConsumableStandardExtend {
    @Excel(name = "等级",orderNum = "2",width = 16)
    private String grade;

    @Excel(name = "标样类型",orderNum = "1",width = 16)
    private String consumableCategory;
}
