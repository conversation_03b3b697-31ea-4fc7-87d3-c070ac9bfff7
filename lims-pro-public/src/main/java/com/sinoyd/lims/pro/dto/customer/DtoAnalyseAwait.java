package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.Date;

/**
 * 分析人员待检数据
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoAnalyseAwait {

    /**
     * 数据id
     */
    private String id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 要求完成日期
     */
    private Date deadLine;

    /**
     * 分析项目id
     */
    private String analyseItemId;
    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;


    /**
     * 标准编号
     */
    private String redCountryStandard;


    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;


    /**
     * 方法id
     */
    private String analyzeMethodId;


    /**
     * 样品id
     */
    private String sampleId;


    /**
     * 要求完成时间
     */
    private Date requireDeadLine;


    /**
     * 样品备注
     */
    private String sampleRemark;


    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 点位名称
     */
    private String redFolderName;


    /**
     * 受检单位
     */
    private String inspectedEnt;


    /**
     * 采样时间
     */
    private Date sampleTime;


    /**
     * 测试项目id
     */
    private String testId;


    /**
     * 送样单id
     */
    private String receiveId;


    /**
     * 送样单编号
     */
    private String recordCode;


    /**
     * 检测类型
     */
    private String sampleTypeId;


    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 等级
     */
    private Integer grade;


    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 采样开始时间
     */
    private String samplingBeginTime;

    /**
     * 采样结束时间
     */
    private String samplingEndTime;

    /**
     * 样品颜色
     */
    private String sampleColor;

    /**
     * 样品性状
     */
    private String sampleCharacter;

    /**
     * 样品气味
     */
    private String sampleSmell;

    /**
     * 接样时间
     */
    private Date receiveSampleDate;

    /**
     * 样品制备状态
     */
    private Integer preparedStatus;

    /**
     * 是否制备完成
     */
    private Boolean isFinishPrePare;

    /**
     * 送样人id
     */
    private String senderId;

    /**
     * 送样人姓名
     */
    private String senderName;

    /**
     * 领样日期
     */
    private Date sampleReceiveDate;

    /**
     * 带分组标记的样品编号
     */
    private String sampleCodeWithTag;

    /**
     * 样品类型
     */
    private Integer sampleCategory;

    /**
     * 关联样品id
     */
    private String associateSampleId;

    public DtoAnalyseAwait() {

    }


    /**
     * 主要用到待检样品的列表中 AnalyseDataServiceImpl下的findWaitAnalyseDataByPersonIdAndTestId，如果要调整，对应用的地方也要调整
     *
     * @param id                   主键id
     * @param redAnalyzeItemName   分析项目名称
     * @param analyseItemId        方法id
     * @param redCountryStandard   标准编号
     * @param redAnalyzeMethodName 方法名称
     * @param analyzeMethodId      方法id
     * @param sampleId             样品id
     * @param requireDeadLine      要求完成时间
     * @param remark               备注
     * @param code                 样品编号
     * @param redFolderName        点位名称
     * @param inspectedEnt         受检单位
     * @param samplingTimeBegin    采样时间
     * @param testId               测试项目id
     * @param receiveId            送样单ID
     * @param recordCode           送样单编号
     * @param sampleTypeId         样品类型id
     * @param sampleTypeName       样品类型名称
     * @param grade                等级
     * @param projectName          项目名称
     * @param projectCode          项目编号
     */
    public DtoAnalyseAwait(String id,
                           String redAnalyzeItemName,
                           String analyseItemId,
                           String redCountryStandard,
                           String redAnalyzeMethodName,
                           String analyzeMethodId,
                           String sampleId,
                           Date requireDeadLine,
                           String remark,
                           String code,
                           String redFolderName,
                           String inspectedEnt,
                           Date samplingTimeBegin,
                           String testId,
                           String receiveId,
                           String recordCode,
                           Date receiveSampleDate,
                           String sampleTypeId,
                           String sampleTypeName,
                           Integer grade,
                           String projectName,
                           String projectCode,
                           Integer preparedStatus,
                           String projectId,
                           Date sampleReceiveDate,
                           Integer sampleCategory,
                           String associateSampleId
    ) {

        this.id = id;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.analyseItemId = analyseItemId;
        this.redCountryStandard = redCountryStandard;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.analyzeMethodId = analyzeMethodId;
        this.sampleId = sampleId;
        this.requireDeadLine = requireDeadLine;
        this.sampleRemark = remark;
        this.sampleCode = code;
        this.redFolderName = redFolderName;
        this.inspectedEnt = inspectedEnt;
        this.sampleTime = samplingTimeBegin;
        this.testId = testId;
        this.receiveId = receiveId;
        this.recordCode = recordCode;
        this.receiveSampleDate = receiveSampleDate;
        this.sampleTypeId = sampleTypeId;
        this.sampleTypeName = sampleTypeName;
        this.grade = grade;
        this.projectName = projectName;
        this.projectCode = projectCode;
        this.preparedStatus = preparedStatus;
        this.projectId = projectId;
        this.sampleReceiveDate = sampleReceiveDate;
        this.sampleCategory = sampleCategory;
        this.associateSampleId = associateSampleId;
    }
}
