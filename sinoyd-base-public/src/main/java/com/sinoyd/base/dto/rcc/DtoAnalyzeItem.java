package com.sinoyd.base.dto.rcc;

import com.sinoyd.base.entity.AnalyzeItem;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoAnalyzeItem实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_AnalyzeItem")
 @Data
 @DynamicInsert
 public  class DtoAnalyzeItem extends AnalyzeItem {
      @Transient
      private String receivePerson;

      @Transient
      private String receiveDate;

      @Transient
      private Boolean isKeepSample;

      @Transient
      private Boolean isReceive;

 }