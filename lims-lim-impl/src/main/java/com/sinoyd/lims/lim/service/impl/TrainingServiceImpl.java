package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.lim.dto.lims.DtoTraining2Participants;
import com.sinoyd.lims.lim.repository.lims.Training2ParticipantsRepository;
import com.sinoyd.lims.lim.repository.lims.TrainingRepository;
import com.sinoyd.lims.lim.service.TrainingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 培训接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
@Service
@Slf4j
public class TrainingServiceImpl extends BaseJpaServiceImpl<DtoTraining, String, TrainingRepository> implements TrainingService {


    private Training2ParticipantsRepository participantsRepository;

    private CodeService codeService;

    private UserService userService;

    @Override
    public void findByPage(PageBean<DtoTraining> page, BaseCriteria criteria) {
        page.setEntityName("DtoTraining a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        List<DtoTraining> dataList = page.getData();
        // 获取培训方式常量
        List<DtoCode> codeList = codeService.findCodes(LimCodeHelper.TRAINING);
        // 培训方式常量转换成map，方便根据code 插入name
        Map<String, String> map = codeList.stream().collect(Collectors.toMap(DtoCode::getDictCode, DtoCode::getDictName));
        for (DtoTraining training : dataList) {
            training.setWayName(map.get(training.getWay()));
        }
        page.setData(dataList);
    }

    @Override
    public DtoTraining findOne(String id) {
        DtoTraining training = super.findOne(id);
        if (StringUtil.isNotNull(training)) {
            List<DtoTraining2Participants> participants = participantsRepository.findByTrainingId(id);
            training.setParticipantIds(StringUtil.isNotEmpty(participants) ? participants.stream().map(DtoTraining2Participants::getParticipantsId).collect(Collectors.toList()) : new ArrayList<String>());
        }
        return training;
    }

    @Transactional
    @Override
    public DtoTraining save(DtoTraining dtoTraining) {

        saveParticipants(dtoTraining);
        return super.save(dtoTraining);
    }

    @Transactional
    @Override
    public List<DtoTraining> save(Collection<DtoTraining> entities) {
        for (DtoTraining entity : entities) {
            saveParticipants(entity);
        }
        return super.save(entities);
    }

    @Transactional
    @Override
    public DtoTraining update(DtoTraining entity) {
        participantsRepository.deleteByTrainingId(entity.getId());
        // 保存参与人数据
        saveParticipants(entity);
        return super.update(entity);
    }

    /**
     * 保存参与人数据
     */
    private void saveParticipants(DtoTraining dtoTraining) {
        if (StringUtil.isNotNull(dtoTraining)) {
            List<DtoTraining2Participants> participants = dtoTraining.getParticipantsList();
            List<DtoTraining2Participants> participantsList = new ArrayList<>();
            if (StringUtil.isNotEmpty(participants)) {
                for (DtoTraining2Participants participant : participants) {
                    DtoTraining2Participants training2Participants = new DtoTraining2Participants();
                    training2Participants.setTrainingId(dtoTraining.getId());
                    training2Participants.setParticipantsId(participant.getParticipantsId());
                    training2Participants.setParticipantsName(participant.getParticipantsName());
                    participantsList.add(training2Participants);
                }
            }
            List<String> participantIds = dtoTraining.getParticipantIds();
            if (StringUtil.isNotEmpty(participantIds)) {
                List<DtoUser> allUser = userService.findAll();
                Map<String, DtoUser> dtoUserMap = allUser.stream().collect(Collectors.toMap(DtoUser::getId, dto -> dto));
                for (String participantId : participantIds) {
                    DtoUser dtoUser = dtoUserMap.get(participantId);
                    DtoTraining2Participants training2Participants = new DtoTraining2Participants();
                    training2Participants.setTrainingId(dtoTraining.getId());
                    training2Participants.setParticipantsId(dtoUser.getId());
                    training2Participants.setParticipantsName(dtoUser.getUserName());
                    participantsList.add(training2Participants);
                }
            }
            participantsRepository.save(participantsList);


        }
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setParticipantsRepository(Training2ParticipantsRepository participantsRepository) {
        this.participantsRepository = participantsRepository;
    }

    @Autowired
    @Lazy
    public void setUserService(UserService userService) {
        this.userService = userService;
    }
}
