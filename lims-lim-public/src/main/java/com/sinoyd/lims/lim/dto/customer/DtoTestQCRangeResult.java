package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 质控返回结果
 * <AUTHOR>
 * @version V1.0.0 2019/12/16
 * @since V100R001
 */
@Data
public class DtoTestQCRangeResult {

    /**
     * 是否通过
     */
    private Boolean isPass;

    /**
     * 质控范围
     */
    private String rangeConfig;


    /**
     * 范围类型
     */
    private Integer rangeType;

    /**
     * 偏差率
     */
    private BigDecimal rate;

    /**
     * 偏差范围要求
     */
    private String rangeLimit;
}