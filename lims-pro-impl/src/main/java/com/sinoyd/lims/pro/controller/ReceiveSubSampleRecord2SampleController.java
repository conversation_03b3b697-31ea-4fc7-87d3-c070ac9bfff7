package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ReceiveSubSampleRecord2SampleService;
import com.sinoyd.lims.pro.criteria.ReceiveSubSampleRecord2SampleCriteria;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord2Sample;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ReceiveSubSampleRecord2Sample服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: ReceiveSubSampleRecord2Sample服务")
 @RestController
 @RequestMapping("api/pro/receiveSubSampleRecord2Sample")
 public class ReceiveSubSampleRecord2SampleController extends BaseJpaController<DtoReceiveSubSampleRecord2Sample, String,ReceiveSubSampleRecord2SampleService> {


    /**
     * 分页动态条件查询ReceiveSubSampleRecord2Sample
     * @param receiveSubSampleRecord2SampleCriteria 条件参数
     * @return RestResponse<List<ReceiveSubSampleRecord2Sample>>
     */
     @ApiOperation(value = "分页动态条件查询ReceiveSubSampleRecord2Sample", notes = "分页动态条件查询ReceiveSubSampleRecord2Sample")
     @GetMapping
     public RestResponse<List<DtoReceiveSubSampleRecord2Sample>> findByPage(ReceiveSubSampleRecord2SampleCriteria receiveSubSampleRecord2SampleCriteria) {
         PageBean<DtoReceiveSubSampleRecord2Sample> pageBean = super.getPageBean();
         RestResponse<List<DtoReceiveSubSampleRecord2Sample>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, receiveSubSampleRecord2SampleCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询ReceiveSubSampleRecord2Sample
     * @param id 主键id
     * @return RestResponse<DtoReceiveSubSampleRecord2Sample>
     */
     @ApiOperation(value = "按主键查询ReceiveSubSampleRecord2Sample", notes = "按主键查询ReceiveSubSampleRecord2Sample")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoReceiveSubSampleRecord2Sample> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoReceiveSubSampleRecord2Sample> restResponse = new RestResponse<>();
         DtoReceiveSubSampleRecord2Sample receiveSubSampleRecord2Sample = service.findOne(id);
         restResponse.setData(receiveSubSampleRecord2Sample);
         restResponse.setRestStatus(StringUtil.isNull(receiveSubSampleRecord2Sample) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增ReceiveSubSampleRecord2Sample
     * @param receiveSubSampleRecord2Sample 实体列表
     * @return RestResponse<DtoReceiveSubSampleRecord2Sample>
     */
     @ApiOperation(value = "新增ReceiveSubSampleRecord2Sample", notes = "新增ReceiveSubSampleRecord2Sample")
     @PostMapping
     public RestResponse<DtoReceiveSubSampleRecord2Sample> create(@RequestBody DtoReceiveSubSampleRecord2Sample receiveSubSampleRecord2Sample) {
         RestResponse<DtoReceiveSubSampleRecord2Sample> restResponse = new RestResponse<>();
         restResponse.setData(service.save(receiveSubSampleRecord2Sample));
         return restResponse;
      }

     /**
     * 新增ReceiveSubSampleRecord2Sample
     * @param receiveSubSampleRecord2Sample 实体列表
     * @return RestResponse<DtoReceiveSubSampleRecord2Sample>
     */
     @ApiOperation(value = "修改ReceiveSubSampleRecord2Sample", notes = "修改ReceiveSubSampleRecord2Sample")
     @PutMapping
     public RestResponse<DtoReceiveSubSampleRecord2Sample> update(@RequestBody DtoReceiveSubSampleRecord2Sample receiveSubSampleRecord2Sample) {
         RestResponse<DtoReceiveSubSampleRecord2Sample> restResponse = new RestResponse<>();
         restResponse.setData(service.update(receiveSubSampleRecord2Sample));
         return restResponse;
      }

    /**
     * "根据id批量删除ReceiveSubSampleRecord2Sample
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ReceiveSubSampleRecord2Sample", notes = "根据id批量删除ReceiveSubSampleRecord2Sample")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }