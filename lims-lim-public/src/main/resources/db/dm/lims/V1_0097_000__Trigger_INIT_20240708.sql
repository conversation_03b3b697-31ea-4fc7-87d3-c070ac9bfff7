CREATE  TRIGGER "BEFORE_DELETE_PERSON2TEST"
    BEFORE  DELETE
    ON "TB_LIM_PERSON2TEST"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_person2test', old.id, '00000000-0000-0000-0000-000000000000', now(), 2, null, null, null,
        old.orgId, '00000000-0000-0000-0000-000000000000');
End;
