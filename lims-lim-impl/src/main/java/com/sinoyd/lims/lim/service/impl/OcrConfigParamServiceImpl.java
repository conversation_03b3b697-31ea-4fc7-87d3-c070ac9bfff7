package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParam;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.OcrConfigParamRepository;
import com.sinoyd.lims.lim.service.OcrConfigParamService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * ocr对象参数接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@Service
public class OcrConfigParamServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoOcrConfigParam, String, OcrConfigParamRepository> implements OcrConfigParamService {



    @Override
    public void findByPage(PageBean<DtoOcrConfigParam> page, BaseCriteria criteria) {
        page.setEntityName("DtoOcrConfigParam c");
        page.setSelect("select c");
        super.findByPage(page, criteria);
        List<DtoOcrConfigParam> list = page.getData();
        for (DtoOcrConfigParam dtoOcrConfigParam:list) {
            //参数类型文本
            dtoOcrConfigParam.setParamTypeName(EnumLIM.EnumOcrConfigParamType.getByValue(dtoOcrConfigParam.getParamType()));
        }
    }

    @Override
    public List<Map<String, Object>> getParamTypeSelectList() {
        List<Map<String, Object>> res = new ArrayList<>();
        for(EnumLIM.EnumOcrConfigParamType c : EnumLIM.EnumOcrConfigParamType.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("text",c.name());
            map.put("value",c.getValue());
            res.add(map);
        }
        return res;
    }

    @Override
    public List<DtoOcrConfigParam> findByConfigId(String configId) {
        return repository.findAllByConfigId(configId);
    }
}
