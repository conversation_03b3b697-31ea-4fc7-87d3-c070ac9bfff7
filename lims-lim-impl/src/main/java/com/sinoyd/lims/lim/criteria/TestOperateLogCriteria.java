package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;

/**
 * TestOperateLog查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/02/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestOperateLogCriteria extends BaseCriteria {

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endData;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 操作人
     */
    private String operatorId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 表主键
     */
    private String tableId;

    /**
     * 旧值
     */
    private String oldValue;

    /**
     * 新值
     */
    private String newValue;

    /**
     * 字段
     */
    private String operateField;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        //开始日期
        if (StringUtil.isNotEmpty(startDate)) {
            condition.append(" and p.operatorDate >= :startDate ");
            values.put("startDate", DateUtil.stringToDate(startDate, DateUtil.FULL));
        }
        //结束日期
        if (StringUtil.isNotEmpty(endData)) {
            condition.append(" and p.operatorDate <= :endData ");
            Date endDate = DateUtil.stringToDate(endData, DateUtil.FULL);
            Calendar c = Calendar.getInstance();
            c.setTime(endDate);
            c.add(Calendar.DATE, 1);
            endDate = c.getTime();
            values.put("endData", endDate);
        }
        //操作类型
        if (operateType != null) {
            condition.append(" and p.operateType = :operateType ");
            values.put("operateType", operateType);
        }
        //操作人
        if (StringUtil.isNotEmpty(operatorId)) {
            condition.append(" and p.operatorId = :operatorId ");
            values.put("operatorId", operatorId);
        }
        //表名
        if (StringUtil.isNotEmpty(tableName)) {
            condition.append(" and p.tableName like :tableName ");
            values.put("tableName", "%" + tableName + "%");
        }
        //表主键
        if (StringUtil.isNotEmpty(tableId)) {
            condition.append(" and p.tableId like :tableId ");
            values.put("tableId", "%" + tableId + "%");
        }
        //旧值
        if (StringUtil.isNotEmpty(oldValue)) {
            condition.append(" and p.oldValue like :oldValue ");
            values.put("oldValue", "%" + oldValue + "%");
        }
        //新值
        if (StringUtil.isNotEmpty(newValue)) {
            condition.append(" and p.newValue like :newValue ");
            values.put("newValue", "%" + newValue + "%");
        }
        //字段
        if (StringUtil.isNotEmpty(operateField)) {
            condition.append(" and p.operateField like :operateField ");
            values.put("operateField", "%" + operateField + "%");
        }
        return condition.toString();
    }
}