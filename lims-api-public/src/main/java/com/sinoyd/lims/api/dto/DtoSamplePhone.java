package com.sinoyd.lims.api.dto;

import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

@Data
public class DtoSamplePhone {

    /**
     * 样品id
     */
    private String samId;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 样品类型大类id
     */
    private String bigSampleTypeId;

    /**
     * 样品编号
     */
    @Length(message = "样品编号{validation.message.length}", max = 50)
    private String sampleCode;

    /**
     * 点位名称
     */
    private String folderName;

    /**
     * 周期
     */
    private Integer cycleOrder;

    /**
     * 次数
     */
    private Integer timesOrder;

    /**
     * 样次
     */
    private Integer sampleOrder;

    /**
     * 分析项目集合
     */
    private List<DtoAnalyseDataPhone> analyseDataList;

    /**
     * 采样日期
     */
    private Date samplingTimeBegin;
}
