DROP TABLE IF EXISTS TB_LIM_Examine;
CREATE TABLE TB_LIM_Examine (
                                id varchar(50) NOT NULL COMMENT '主键',
                                title varchar(255) NOT NULL  COMMENT '考核目标',
                                examineDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '考核日期',
                                deptId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '考核部门标识',
                                deptName varchar(50) NOT NULL  COMMENT '考核部门名称',
                                inspectedPersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '受考核人员标识',
                                inspectedPerson varchar(50) NOT NULL COMMENT '受考核人员名称',
                                registeDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '考核登记时间',
                                endDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '考核截止时间',
                                status varchar(50)   COMMENT '状态',
                                auditResult varchar(50)   COMMENT '评审结果',
                                auditOpinion varchar(1000)   COMMENT '审核意见',
                                isDeleted bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
                                creator varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
                                createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
                                modifier varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '最近修改人',
                                modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最新修改时间',
                                PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='考核管理表';

DROP TABLE IF EXISTS TB_LIM_ExamineType;
CREATE TABLE TB_LIM_ExamineType (
                                    id varchar(50) NOT NULL COMMENT '主键',
                                    parentId varchar(50)  DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '父项标识',
                                    examineId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '考核标识',
                                    title varchar(255) NOT NULL  COMMENT '考核目标',
                                    examineDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '考核日期',
                                    inspectedPersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '受考核人员标识',
                                    inspectedPerson varchar(50) NOT NULL COMMENT '受考核人员名称',
                                    content varchar(255)   COMMENT '考核内容',
                                    isDeleted bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                    orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
                                    creator varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
                                    createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
                                    modifier varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '最近修改人',
                                    modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最新修改时间',
                                    PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='考核类型表';

DROP TABLE IF EXISTS TB_LIM_ExamineTypeRecord;
CREATE TABLE TB_LIM_ExamineTypeRecord (
                                          id varchar(50) NOT NULL COMMENT '主键',
                                          examineTypeId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '考核类型标识',
                                          examineDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '登记日期',
                                          progress varchar(50) NOT NULL  COMMENT '考核进度',
                                          content varchar(1000)  COMMENT '详情内容',
                                          isDeleted bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                          orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
                                          creator varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
                                          createDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
                                          modifier varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '最近修改人',
                                          modifyDate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最新修改时间',
                                          PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='考核记录表';
ALTER TABLE tb_lim_examine ADD COLUMN fullChargePeopleName VARCHAR(500) NOT NULL COMMENT '所有受考核人员名称';
ALTER TABLE tb_lim_examine  modify COLUMN status int(10) NOT NULL DEFAULT 10 COMMENT '考核状态';
ALTER TABLE tb_lim_examinetype ADD COLUMN progress int(10) NOT NULL DEFAULT 0 COMMENT '考核项目进度';
ALTER TABLE tb_lim_examinetyperecord  modify COLUMN progress int(10) NOT NULL DEFAULT 0 COMMENT '进度';
ALTER TABLE tb_lim_examine ADD COLUMN addPersonId VARCHAR(50) NOT NULL COMMENT '登记人标识';
ALTER TABLE tb_lim_examine ADD COLUMN addPersonName VARCHAR(50) NOT NULL COMMENT '登记人名称';
ALTER TABLE tb_lim_examine ADD COLUMN addDate datetime NOT NULL COMMENT '登记日期';
ALTER TABLE tb_lim_examine DROP COLUMN examineDate;
ALTER TABLE tb_lim_examine DROP COLUMN auditResult;
ALTER TABLE tb_lim_examinetype DROP COLUMN examineDate;
ALTER TABLE tb_lim_examine DROP COLUMN fullChargePeopleName;