package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.lims.lim.entity.ConsumableStorage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoConsumableStorage
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ConsumableStorage")
@Data
@DynamicInsert
public class DtoConsumableStorage extends ConsumableStorage {


    public void importToEntity(DtoConsumableDetail consumableDetail) {
        setConsumableId(consumableDetail.getParentId());
        setPurchaseDetailId(consumableDetail.getId());
        setDimensionId(consumableDetail.getUnitId());
        setDimensionName(consumableDetail.getUnitName());
        setProductionCode(consumableDetail.getProductionCode());
        setCheckerId(consumableDetail.getCheckerId());
        setCheckerName(consumableDetail.getSendWarnUser());
        setCheckerDate(consumableDetail.getPurchasingDate());
        setCheckerResult(consumableDetail.getCheckerResult());
        setSupplyCompanyName(consumableDetail.getSupplierName());
        setExpiryDate(consumableDetail.getExpiryDate());
        setStorageNum(consumableDetail.getInventory());
        setStorageTime(consumableDetail.getStorageDate());
        setBalance(consumableDetail.getStorage());
        setUnitPrice(consumableDetail.getUnitPrice());
        setTotalPrice(consumableDetail.getUnitPrice().multiply(consumableDetail.getInventory()));
        setSupplyCompanyId(consumableDetail.getSupplierId());
        setSupplyCompanyName(consumableDetail.getSupplierName());
        setOperatorId(consumableDetail.getCreator());
        setOperatorName(consumableDetail.getCreatorName());
        setAppearance(consumableDetail.getAppearance());
        setCheckItem(consumableDetail.getCheckItem());
        setBuyReason(consumableDetail.getBuyReason());
        setKeepPlace(consumableDetail.getKeepPlace());
        setRemark(consumableDetail.getRemark());
        setOrgId(consumableDetail.getOrgId());
    }

}