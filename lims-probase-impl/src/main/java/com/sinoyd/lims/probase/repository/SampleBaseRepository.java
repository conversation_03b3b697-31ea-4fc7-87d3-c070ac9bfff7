package com.sinoyd.lims.probase.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSample;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface SampleBaseRepository extends IBaseJpaRepository<DtoSample, String> {

    @Query("select new com.sinoyd.lims.pro.dto.DtoSample(id,status,ananlyzeStatus,samplingStatus,innerReceiveStatus) from DtoSample s where s.isDeleted = 0 and s.receiveId = :receiveId ")
    List<DtoSample> findByReceiveId(@Param("receiveId") String receiveId);

    @Query("select s from DtoSample s where s.isDeleted = 0 and s.projectId = :projectId ")
    List<DtoSample> findByProjectId(@Param("projectId") String projectId);

    /**
     * 修改分析状态
     *
     * @param ids           样品ids
     * @param analyzeStatus 分析状态
     * @param modifier      修改人
     * @param modifyDate    修改时间
     * @return 返回修改行数
     */
    @Transactional
    @Query("update DtoSample s set s.ananlyzeStatus = :analyzeStatus,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.id in :ids")
    Integer updateAnalyzeStatus(@Param("ids") List<String> ids,
                                @Param("analyzeStatus") Integer analyzeStatus,
                                @Param("modifier") String modifier,
                                @Param("modifyDate") Date modifyDate);


    /**
     * 修改领样状态
     *
     * @param ids                样品ids
     * @param innerReceiveStatus 领样状态
     * @param modifier           修改人
     * @param modifyDate         修改时间
     * @return 返回修改行数
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("update DtoSample s set s.innerReceiveStatus = :innerReceiveStatus,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.id in :ids")
    Integer updateInnerReceiveStatus(@Param("ids") List<String> ids,
                                     @Param("innerReceiveStatus") Integer innerReceiveStatus,
                                     @Param("modifier") String modifier,
                                     @Param("modifyDate") Date modifyDate);


    /**
     * 修改领样状态及分析状态
     *
     * @param ids                样品ids
     * @param analyzeStatus      分析状态
     * @param innerReceiveStatus 领样状态
     * @param modifier           修改人
     * @param modifyDate         修改时间
     * @return 返回修改行数
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("update DtoSample s set s.innerReceiveStatus = :innerReceiveStatus,s.ananlyzeStatus = :analyzeStatus,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.id in :ids")
    Integer updateInnerReceiveStatusAndAnalyzeStatus(@Param("ids") List<String> ids,
                                                     @Param("analyzeStatus") Integer analyzeStatus,
                                                     @Param("innerReceiveStatus") Integer innerReceiveStatus,
                                                     @Param("modifier") String modifier,
                                                     @Param("modifyDate") Date modifyDate);

    /**
     * 修改领样状态及分析状态
     *
     * @param id                 样品id
     * @param samplingStatus     采样状态
     * @param status             状态
     * @param analyzeStatus      分析状态
     * @param innerReceiveStatus 领样状态
     * @param modifier           修改人
     * @param modifyDate         修改时间
     * @return 返回修改行数
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("update DtoSample s set s.samplingStatus = :samplingStatus,s.status =:status, s.innerReceiveStatus = :innerReceiveStatus,s.ananlyzeStatus = :analyzeStatus,s.modifyDate = :modifyDate,s.modifier = :modifier  where s.id = :id")
    Integer updateSampleStatus(@Param("id") String id,
                               @Param("samplingStatus") Integer samplingStatus,
                               @Param("status") String status,
                               @Param("innerReceiveStatus") Integer innerReceiveStatus,
                               @Param("analyzeStatus") Integer analyzeStatus,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);


    /**
     * 查询该项目没有送样单并且是不需要分析的样品
     *
     * @param projectId      项目id
     * @param receiveId      送样单id
     * @param analyzeStatus 状态
     * @return 返回条数
     */
    @Query("select count(a.id) from DtoSample a where  a.projectId=:projectId and a.isDeleted=0 and a.receiveId=:receiveId and a.ananlyzeStatus<>:analyzeStatus")
    Integer countSampleNoReceiveSample(@Param("projectId") String projectId,
                                       @Param("receiveId") String receiveId,
                                       @Param("analyzeStatus") Integer analyzeStatus);
}
