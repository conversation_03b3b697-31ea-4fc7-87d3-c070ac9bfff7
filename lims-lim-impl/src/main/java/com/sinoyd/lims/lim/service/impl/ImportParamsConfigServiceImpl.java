package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.customer.DtoImportParamsConfig;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.*;
import com.sinoyd.lims.lim.service.ImportParamsConfigService;
import com.sinoyd.lims.lim.verify.DataParamsConfigVerifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 原始记录单与测试项目关系导入接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2022/10/13
 * @since V100R001
 */
@Service
@Slf4j
public class ImportParamsConfigServiceImpl implements ImportParamsConfigService {

    private DataParamsConfigVerifyHandler dataParamsConfigVerifyHandler;

    private TestRepository testRepository;

    private List<DtoTest> allTestList;

    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    private List<DtoParamsTestFormula> allParamsTestFormulaList;

    private RecordConfig2TestRepository recordConfig2TestRepository;

    private List<DtoRecordConfig2Test> allRecordConfig2TestList;

    private SampleTypeRepository sampleTypeRepository;

    private List<DtoSampleType> allSampleTypeList;

    private ParamsFormulaRepository paramsFormulaRepository;

    private List<DtoParamsFormula> allParamsFormulaList;

    private String recordConfigId;

    private ParamsConfigRepository paramsConfigRepository;

    private ParamsRepository paramsRepository;

    private Params2ParamsFormulaRepository params2ParamsFormulaRepository;

    private DimensionRepository dimensionRepository;

    @Override
    @Transactional
    public void importExcel(MultipartFile file, Map<String, Object> objectMap, HttpServletResponse response) throws Exception {
        //文件格式校验
        PoiExcelUtils.verifyFileType(file);
        //通用数据初始化
        initDataContainer(objectMap);
        //更新校验容器并获取excel导入结果集
        initHandler();
        ExcelImportResult<DtoImportParamsConfig> importResult = getExcelData(file, response);
        //校验正确数据
        List<DtoImportParamsConfig> importList = importResult.getList();
        //跳过空行
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        //数据入库
        saveRelatedData(importList);
    }

    /**
     * 解析文件
     *
     * @param file     文件
     * @param response 响应
     * @return 结果集
     * @throws Exception 异常
     */
    private ExcelImportResult<DtoImportParamsConfig> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置是否校验及校验器
        params.setNeedVerify(true);
        params.setVerifyHandler(dataParamsConfigVerifyHandler);
        ExcelImportResult<DtoImportParamsConfig> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoImportParamsConfig.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "原始记录单数据参数导入错误信息");
            PoiExcelUtils.downLoadExcel("原始记录单数据参数导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 通用数据初始化
     *
     * @param objectMap 附加参数
     */
    private void initDataContainer(Map<String, Object> objectMap) {
        if(objectMap.get("recordConfigId")==null){
            throw new BaseException("传参异常，未传recordConfigId");
        }
        recordConfigId = (String) objectMap.get("recordConfigId");
        allTestList = testRepository.findAll();
        allParamsTestFormulaList = paramsTestFormulaRepository.findAll();
        allRecordConfig2TestList = recordConfig2TestRepository.findAll();
        allSampleTypeList = sampleTypeRepository.findAll();
        allParamsFormulaList = paramsFormulaRepository.findAll();
    }

    /**
     * 初始化校验器数据
     */
    private void initHandler() {
        dataParamsConfigVerifyHandler = new DataParamsConfigVerifyHandler();
        dataParamsConfigVerifyHandler.setSheetExistDataList(new ArrayList<>());
        dataParamsConfigVerifyHandler.setRecordConfigId(recordConfigId);
        dataParamsConfigVerifyHandler.setAllTestList(allTestList);
        dataParamsConfigVerifyHandler.setAllParamsTestFormulaList(allParamsTestFormulaList);
        dataParamsConfigVerifyHandler.setAllRecordConfig2TestList(allRecordConfig2TestList);
        dataParamsConfigVerifyHandler.setAllSampleTypeList(allSampleTypeList);
        dataParamsConfigVerifyHandler.setAllParamsFormulaList(allParamsFormulaList);
    }

    /**
     * 数据更新
     *
     * @param importList 正常导入数据列表
     */
    private void saveRelatedData(List<DtoImportParamsConfig> importList) {
        //所有待更新数据参数及个性化量纲等
        List<DtoParamsConfig> waitSaveParamsConfigList = new ArrayList<>();
        List<DtoParams2ParamsFormula> waitSaveParams2ParamsFormulaList = new ArrayList<>();
        //所有待移除个性化配置
        List<DtoParams2ParamsFormula> waitDeleteParams2ParamsFormulaList = new ArrayList<>();
        List<DtoParamsConfig> waitDeleteParamsConfigList = new ArrayList<>();
        //原始记录单所有数据参数
        List<DtoParamsConfig> allParamsConfigList = paramsConfigRepository.findAll();
        //所有参数列表
        List<DtoParams> allParamsList = paramsRepository.findAll();
        //所有个性化公式
        List<DtoParams2ParamsFormula> allParams2ParamsFormulaList = params2ParamsFormulaRepository.findAll();
        //所有量纲
        List<DtoDimension> allDimensionList = dimensionRepository.findAll();
        //所有测试项目公式参数
        List<DtoParamsTestFormula> allParamsTestFormulaList = paramsTestFormulaRepository.findAll();
        //临时新增容器，避免数据参数重复新增
        List<DtoParamsConfig> savedTempList = new ArrayList<>();
        for (DtoImportParamsConfig importParamsConfig : importList) {
            DtoParamsConfig dtoParamsConfig = allParamsConfigList.stream().filter(c -> importParamsConfig.getParamsConfigName().equals(c.getAlias())
                                                                                          &&recordConfigId.equals(c.getObjId())
                                                                                          &&EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue().equals(c.getType()))
                                                                             .findFirst().orElse(null);
            if (dtoParamsConfig == null) {
                //判断临时容器中是否已存在
                dtoParamsConfig = savedTempList.stream().filter(c -> importParamsConfig.getParamsConfigName().equals(c.getAlias())
                        &&recordConfigId.equals(c.getObjId())
                        &&EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue().equals(c.getType()))
                        .findFirst().orElse(null);
                if(dtoParamsConfig==null){
                    //判断数据库中是否存在
                    DtoParams dtoParam = allParamsList.stream().filter(p -> importParamsConfig.getParamsConfigName().equals(p.getParamName())).findFirst().orElse(null);
                    //数据参数不存在就新增
                    if (dtoParam != null) {
                        dtoParamsConfig = new DtoParamsConfig();
                        dtoParamsConfig.setObjId(recordConfigId);
                        dtoParamsConfig.setType(EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());
                        dtoParamsConfig.setParamsId(dtoParam.getId());
                        dtoParamsConfig.setAlias(dtoParam.getParamName());
                        waitSaveParamsConfigList.add(dtoParamsConfig);
                        savedTempList.add(dtoParamsConfig);
                    }
                }
            }
            if (dtoParamsConfig != null) {
                DtoSampleType dtoSampleType = allSampleTypeList.stream().filter(s -> s.getTypeName().equals(importParamsConfig.getSampleType())).findFirst().orElse(null);
                if (dtoSampleType != null) {
                    DtoTest dtoTest = allTestList.stream().filter(t -> t.getRedAnalyzeItemName().equals(importParamsConfig.getAnalyzeItem())
                            && t.getRedAnalyzeMethodName().equals(importParamsConfig.getAnalyzeMethod())
                            && dtoSampleType.getId().equals(t.getSampleTypeId()))
                            .findFirst().orElse(null);
                    if (dtoTest != null) {
                        DtoParamsFormula dtoParamsFormula = allParamsFormulaList.stream().filter(f -> dtoTest.getId().equals(f.getObjectId())
                                && dtoSampleType.getId().equals(f.getSampleTypeId())
                                && importParamsConfig.getFormula().equals(f.getFormula())
                                && EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue().equals(f.getObjectType()))
                                .findFirst().orElse(null);
                        if (dtoParamsFormula != null) {
                            if ("已配置".equals(importParamsConfig.getConfigStatus())) {
                                collectWaitSaveData(waitSaveParamsConfigList,waitSaveParams2ParamsFormulaList,importParamsConfig, dtoParamsConfig,
                                        dtoParamsFormula.getId(),allParams2ParamsFormulaList,allParamsConfigList,allDimensionList,allParamsTestFormulaList);
                            } else {
                                //未配置移除个性化配置
                                collectWaitDeleteData(waitDeleteParams2ParamsFormulaList, waitDeleteParamsConfigList,
                                        dtoParamsConfig.getId(),dtoParamsFormula.getId(),allParams2ParamsFormulaList,allParamsConfigList);
                            }
                        }
                    }
                }
            }
        }
        //数据更新
        paramsConfigRepository.save(waitSaveParamsConfigList);
        paramsConfigRepository.delete(waitDeleteParamsConfigList);
        params2ParamsFormulaRepository.save(waitSaveParams2ParamsFormulaList);
        params2ParamsFormulaRepository.delete(waitDeleteParams2ParamsFormulaList);
    }

    /**
     * 解析已配置部分数据参数
     * @param waitSaveParamsConfigList                 待更新容器
     * @param waitSaveParams2ParamsFormulaList         待更新容器
     * @param importParamsConfig                       导入实体
     * @param dtoParamsConfig                          数据参数
     * @param testFormulaId                            测试项目标识
     * @param allParams2ParamsFormulaList              个性化公式列表
     * @param allParamsConfigList                      参数配置列表
     * @param allDimensionList                         量纲列表
     */
    private void collectWaitSaveData(List<DtoParamsConfig> waitSaveParamsConfigList,List<DtoParams2ParamsFormula> waitSaveParams2ParamsFormulaList,
                                     DtoImportParamsConfig importParamsConfig,DtoParamsConfig dtoParamsConfig,String testFormulaId,List<DtoParams2ParamsFormula> allParams2ParamsFormulaList,
                                     List<DtoParamsConfig> allParamsConfigList,List<DtoDimension> allDimensionList,List<DtoParamsTestFormula> allParamsTestFormulaList) {
        String paramsConfigId = dtoParamsConfig.getId();
        DtoParams2ParamsFormula existParams2ParamsFormula = allParams2ParamsFormulaList.stream().filter(p->recordConfigId.equals(p.getRecordId())
                &&paramsConfigId.equals(p.getParamsConfigId())
                &&testFormulaId.equals(p.getObjectId())).findFirst().orElse(null);
        if(existParams2ParamsFormula==null){
            existParams2ParamsFormula = new DtoParams2ParamsFormula();
            existParams2ParamsFormula.setRecordId(recordConfigId);
            existParams2ParamsFormula.setParamsConfigId(paramsConfigId);
            existParams2ParamsFormula.setObjectId(testFormulaId);
        }
        //配置个性化公式
        existParams2ParamsFormula.setFormula(StringUtil.isNotEmpty(importParamsConfig.getRelatedParam())?importParamsConfig.getRelatedParam():"");
        //配置个性化量纲，小数位数，有效位数
        if(StringUtil.isNotEmpty(importParamsConfig.getDimension())||importParamsConfig.getMostDecimal()!=null||importParamsConfig.getMostSignificance()!=null){
            DtoParams2ParamsFormula finalExistParams2ParamsFormula = existParams2ParamsFormula;
            DtoParamsConfig personalParamsConfig = allParamsConfigList.stream().filter(p-> finalExistParams2ParamsFormula.getId().equals(p.getObjId())
                    &&recordConfigId.equals(p.getObjId())
                    &&EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue().equals(p.getType())).findFirst().orElse(null);
            if(personalParamsConfig==null){
                personalParamsConfig = new DtoParamsConfig();
                personalParamsConfig.setObjId(existParams2ParamsFormula.getId());
                personalParamsConfig.setType(EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue());
                personalParamsConfig.setParamsId(dtoParamsConfig.getParamsId());
                personalParamsConfig.setAlias(dtoParamsConfig.getAlias());
            }
            if(StringUtil.isNotEmpty(importParamsConfig.getDimension())){
                DtoDimension dimension = allDimensionList.stream().filter(d->importParamsConfig.getDimension().equals(d.getDimensionName())).findFirst().orElse(null);
                if(dimension!=null){
                    personalParamsConfig.setDimensionId(dimension.getId());
                    personalParamsConfig.setDimension(dimension.getDimensionName());
                }
            }
            personalParamsConfig.setMostDecimal(importParamsConfig.getMostDecimal());
            personalParamsConfig.setMostSignificance(importParamsConfig.getMostSignificance());
            waitSaveParamsConfigList.add(personalParamsConfig);
        }
        waitSaveParams2ParamsFormulaList.add(existParams2ParamsFormula);
    }

    /**
     * 解析未配置部分数据参数
     * @param waitDeleteParams2ParamsFormulaList  待移除数据容器
     * @param waitDeleteParamsConfigList          待移除数据容器
     * @param paramsConfigId                      数据参数标识
     * @param testFormulaId                       测试项目标识
     * @param allParams2ParamsFormulaList         个性化公式列表
     * @param recordParamsConfigList              参数列表
     */
    private void collectWaitDeleteData(List<DtoParams2ParamsFormula> waitDeleteParams2ParamsFormulaList, List<DtoParamsConfig> waitDeleteParamsConfigList,
                                       String paramsConfigId,String testFormulaId,List<DtoParams2ParamsFormula> allParams2ParamsFormulaList,List<DtoParamsConfig> recordParamsConfigList) {
        //待删除的个性化公式
        List<DtoParams2ParamsFormula> singeConfigWaitDelList = allParams2ParamsFormulaList.stream().filter(p->recordConfigId.equals(p.getRecordId())
                                                                                                              &&paramsConfigId.equals(p.getParamsConfigId())
                                                                                                              &&testFormulaId.equals(p.getObjectId()))
                                                                                                   .collect(Collectors.toList());
        waitDeleteParams2ParamsFormulaList.addAll(singeConfigWaitDelList);
        List<String> params2ParamsFormulaIdList = singeConfigWaitDelList.stream().map(DtoParams2ParamsFormula::getId).collect(Collectors.toList());
        //待删除的个性化量纲，有效位数，小数位数
        List<DtoParamsConfig> waitParamsConfigList = recordParamsConfigList.stream().filter(p->params2ParamsFormulaIdList.contains(p.getObjId())
                                                                                              &&recordConfigId.equals(p.getObjId())
                                                                                              &&EnumLIM.EnumParamsConfigType.原始记录单数据参数.getValue().equals(p.getType()))
                                                                                    .collect(Collectors.toList());
        waitDeleteParamsConfigList.addAll(waitParamsConfigList);
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setRecordConfig2TestRepository(RecordConfig2TestRepository recordConfig2TestRepository) {
        this.recordConfig2TestRepository = recordConfig2TestRepository;
    }

    @Autowired
    public void setParamsFormulaRepository(ParamsFormulaRepository paramsFormulaRepository) {
        this.paramsFormulaRepository = paramsFormulaRepository;
    }

    @Autowired
    public void setParamsTestFormulaRepository(ParamsTestFormulaRepository paramsTestFormulaRepository) {
        this.paramsTestFormulaRepository = paramsTestFormulaRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setParamsConfigRepository(ParamsConfigRepository paramsConfigRepository) {
        this.paramsConfigRepository = paramsConfigRepository;
    }

    @Autowired
    public void setParamsRepository(ParamsRepository paramsRepository) {
        this.paramsRepository = paramsRepository;
    }

    @Autowired
    public void setParams2ParamsFormulaRepository(Params2ParamsFormulaRepository params2ParamsFormulaRepository) {
        this.params2ParamsFormulaRepository = params2ParamsFormulaRepository;
    }


    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }
}
