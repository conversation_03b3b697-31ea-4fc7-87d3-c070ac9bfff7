package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SamplingFrequency;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoSamplingFrequency实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SamplingFrequency")
@Data
@DynamicInsert
public class DtoSamplingFrequency extends SamplingFrequency {
    private static final long serialVersionUID = 1L;

    /**
     * 样品点位名称
     */
    @Transient
    private String redFolderName;


    /**
     * 检测类型id
     */
    @Transient
    private String sampleTypeId;

    /**
     * 检测类型
     */
    @Transient
    private String sampleTypeName;

    /**
     * 样品大类id
     */
    @Transient
    private String bigSampleTypeId;

    /**
     * 样品大类名称
     */
    @Transient
    private String bigSampleName;

    /**
     * 例行任务点位排序值
     */
    @Transient
    private int fixedPointOrderNum;

    /**
     * 点位名称
     */
    @Transient
    private String watchSpot;

    /**
     * 样品点位名称
     */
    @Transient
    private String periodName;

    /**
     * 样品频次
     */
    @Transient
    private String timeName;

    @Transient
    private String sampleStatus;

    @Transient
    private String customerId;

    @Transient
    private String customerName;

    /**
     * 默认构造方法
     */
    public DtoSamplingFrequency(){}

    /**
     * 构造方法，参考 SamplingFrequencyServiceImpl中方法findPrepareSamplingFrequency
     *
     * @param id             主键
     * @param sampleFolderId 点位id
     * @param periodCount    periodCount
     * @param timePerPeriod  次数
     * @param folderType     点位类型
     * @param samplePerTime  每次样品数
     * @param orgId          组织机构id
     * @param watchSpot      点位名称
     * @param sampleTypeId   检测类型id
     */
    public DtoSamplingFrequency(String id,
                                String sampleFolderId,
                                Integer periodCount,
                                Integer timePerPeriod,
                                Integer folderType,
                                Integer samplePerTime,
                                String orgId,
                                String watchSpot,
                                String sampleTypeId) {
        this();
        setId(id);
        setSampleFolderId(sampleFolderId);
        setPeriodCount(periodCount);
        setTimePerPeriod(timePerPeriod);
        setFolderType(folderType);
        setSamplePerTime(samplePerTime);
        setOrgId(orgId);
        this.watchSpot = watchSpot;
        this.sampleTypeId = sampleTypeId;
    }

}