package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * DetailData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="DetailData")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class DetailData implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  DetailData() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 项目id
    */
    @Column(length=50)
    @ApiModelProperty("项目id")
	private String projectId;
    
    /**
    * 送样记录id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("送样记录id")
	private String receiveId;
    
    /**
    * 流水编号
    */
    @Column(length=50)
    @ApiModelProperty("流水编号")
	private String projectCode;
    
    /**
    * 项目类型id（外键）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目类型id（外键）")
	private String projectTypeId;
    
    /**
    * 项目名称
    */
    @Column(length=100)
    @ApiModelProperty("项目名称")
	private String projectName;
    
    /**
    * 项目登记时间(登记时间不能改)
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("项目登记时间(登记时间不能改)")
	private Date inputTime;
    
    /**
    * 委托时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("委托时间")
	private Date inceptTime;
    
    /**
    * 委托单位id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("委托单位id")
	private String customerId;
    
    /**
    * 委托单位
    */
    @Column(length=100)
    @ApiModelProperty("委托单位")
	private String customerName;
    
    /**
    * 样品编号
    */
    @Column(length=50)
    @ApiModelProperty("样品编号")
	private String sampleCode;
    
    /**
    * 点位id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("点位id")
	private String sampleFolderId;
    
    /**
    * 采样周期序数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("采样周期序数")
	private Integer cycleOrder= 0;
    
    /**
    * 每周期次数序数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("每周期次数序数")
	private Integer timesOrder= 0;
    
    /**
    * 每次样品序数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("每次样品序数")
	private Integer sampleOrder= 0;
    
    /**
    * 冗余-点位
    */
    @Column(length=100)
    @ApiModelProperty("冗余-点位")
	private String redFolderName;
    
    /**
    * 采样开始时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("采样开始时间")
	private Date samplingTimeBegin;
    
    /**
    * 采样结束时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("采样结束时间")
	private Date samplingTimeEnd;
    
    /**
    * 受检单位Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("受检单位Id")
	private String inspectedEntId;
    
    /**
    * 受检单位
    */
    @Column(length=100)
    @ApiModelProperty("受检单位")
	private String inspectedEnt;
    
    /**
    * 检测类型id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型id")
	private String sampleTypeId;
    
    /**
    * 点位名称
    */
    @Column(length=100)
    @ApiModelProperty("点位名称")
	private String watchSpot;
    
    /**
    * 断面id（断面扩展id）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("断面id（断面扩展id）")
	private String fixedPointId;
    
    /**
    * 点位号
    */
    @Column(length=50)
    @ApiModelProperty("点位号")
	private String folderCode;
    
    /**
    * 参数数据
    */
    @Column(length=4000)
    @ApiModelProperty("参数数据")
	private String paramsData;
    
    /**
    * 假删字段
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
	private Boolean isDeleted= false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }