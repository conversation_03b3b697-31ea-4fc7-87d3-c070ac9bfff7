package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.QualityControlEvaluate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

@Entity
@Table(name = "TB_PRO_QualityControlEvaluate")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
@EqualsAndHashCode(callSuper = true)
public class DtoQualityControlEvaluate extends QualityControlEvaluate {
    private static final long serialVersionUID = 1L;

    /**
     * 样品编号
     */
    @Transient
    private String sampleCode;

    /**
     * 分析项目名称
     */
    @Transient
    private String redAnalyzeItemName;

    /**
     * 质控类型
     */
    @Transient
    private Integer qcType;

    /**
     * 质控类型名称
     */
    @Transient
    private String qcTypeName;

    /**
     * 评判方式
     */
    @Transient
    private String judgingMethodName;

    /**
     * 分析项目id
     */
    @Transient
    private String analyseItemId;

    /**
     * 质控类型
     */
    @Transient
    private Integer qcGrade;

    /**
     * 是否合格
     */
    @Transient
    private String qcMessage;

    /**
     * 样品类型
     */
    @Transient
    private String sampleTypeId;

    /**
     * 检测单id
     */
    @Transient
    private String workSheetFolderId;

    /**
     * 评价类型名称
     */
    @Transient
    private String judgeTypeName;

    /**
     * 允许限值个性化文本
     */
    @Transient
    private String allowLimitText;

    /**
     * 带分组标记的样品编号
     */
    @Transient
    private String sampleCodeWithTag;
}
