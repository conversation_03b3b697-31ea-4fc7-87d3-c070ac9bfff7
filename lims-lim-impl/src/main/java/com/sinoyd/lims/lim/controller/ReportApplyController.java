package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.ReportApplyService;
import com.sinoyd.lims.lim.criteria.ReportApplyCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoReportApply;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ReportApply服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
 @Api(tags = "示例: ReportApply服务")
 @RestController
 @RequestMapping("api/lim/reportApply")
 @Validated
 public class ReportApplyController extends BaseJpaController<DtoReportApply, String,ReportApplyService> {


    /**
     * 分页动态条件查询ReportApply
     *
     * @param reportApplyCriteria 条件参数
     * @return RestResponse<List < ReportApply>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoReportApply>> findByPage(ReportApplyCriteria reportApplyCriteria) {
        PageBean<DtoReportApply> pageBean = super.getPageBean();
        RestResponse<List<DtoReportApply>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, reportApplyCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ReportApply
     *
     * @param id 主键id
     * @return RestResponse<DtoReportApply>
     */
    @ApiOperation(value = "按主键查询ReportApply", notes = "按主键查询ReportApply")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoReportApply> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoReportApply> restResponse = new RestResponse<>();
        DtoReportApply reportApply = service.findOne(id);
        restResponse.setData(reportApply);
        restResponse.setRestStatus(StringUtil.isNull(reportApply) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增ReportApply
     *
     * @param reportApply 实体列表
     * @return RestResponse<DtoReportApply>
     */
    @ApiOperation(value = "新增ReportApply", notes = "新增ReportApply")
    @PostMapping
    public RestResponse<DtoReportApply> create(@Validated @RequestBody DtoReportApply reportApply) {
        RestResponse<DtoReportApply> restResponse = new RestResponse<>();
        restResponse.setData(service.save(reportApply));
        return restResponse;
    }

    /**
     * 新增ReportApply
     *
     * @param reportApply 实体列表
     * @return RestResponse<DtoReportApply>
     */
    @ApiOperation(value = "修改ReportApply", notes = "修改ReportApply")
    @PutMapping
    public RestResponse<DtoReportApply> update(@Validated @RequestBody DtoReportApply reportApply) {
        RestResponse<DtoReportApply> restResponse = new RestResponse<>();
        restResponse.setData(service.update(reportApply));
        return restResponse;
    }

    /**
     * "根据id批量删除ReportApply
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ReportApply", notes = "根据id批量删除ReportApply")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}