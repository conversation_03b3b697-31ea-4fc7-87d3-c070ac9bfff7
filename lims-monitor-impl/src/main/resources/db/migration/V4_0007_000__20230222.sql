-- ==============================================
-- 添加订单明细表配置
-- =============================================
-- 报表模板配置
delete from TB_LIM_ReportConfig where id = 'e89254bc-3044-4ba8-bb41-02838f689ff9';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum)
VALUES ('e89254bc-3044-4ba8-bb41-02838f689ff9', 1, 'OrderForm', '订单明细_模板.xlsx', 'LIMReportForms/订单明细_模板.xlsx',
        'output/LIMReportForms/订单明细.xlsx', 'application', 'com.sinoyd.lims.pro.service.OrderFormService',
        '{\"sort\":\"orderDate-\"}', '', 0, 1, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-02-22 13:30:55', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-02-22 13:31:20', 'com.sinoyd.lims.pro.criteria.OrderFormCriteria',
        'LIMReportForms', 'export/OrderForm', b'0', '', '', '', '');

--场景配置
delete from TB_LIM_ReportApply where id = 'cd642acf-d699-42e1-87b7-5cf905d8bc17';
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                                               isRedact, isShow, remark, location)
VALUES ('cd642acf-d699-42e1-87b7-5cf905d8bc17', 'e89254bc-3044-4ba8-bb41-02838f689ff9', 'OrderManage', '订单管理',
        'OrderForm', '订单明细表', 0, 0, 1, '', '订单管理:订单登记');
