package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.Instrument2Detector;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoInstrument2Detector实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/4/28
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_Instrument2Detector")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoInstrument2Detector extends Instrument2Detector {

}