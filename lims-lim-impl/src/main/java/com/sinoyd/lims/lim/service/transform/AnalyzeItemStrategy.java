package com.sinoyd.lims.lim.service.transform;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.AnalyzeItemMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.verify.TransformAnalyzeItemVerifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目数据迁移分析项目导入策略类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
@Component
@Order(40)
@Slf4j
public class AnalyzeItemStrategy implements TransformImportStrategy {
    /**
     * sheet页序号
     */
    private static final int sheetIndex = 2;

    private AnalyzeItemRepository analyzeItemRepository;

    private TransformAnalyzeItemVerifyHandler transformAnalyzeItemVerifyHandler;

    private AnalyzeItemMapper analyzeItemMapper;
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    @Transactional
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception {
        //参数初始化
        handleInit();
        params.setStartSheetIndex(sheetIndex);
        params.setVerifyHandler(transformAnalyzeItemVerifyHandler);
        //获取校验结果集
        ExcelImportResult<DtoExportAnalyzeItem> result = ExcelImportUtil.importExcelMore(inputStream, DtoExportAnalyzeItem.class, params);
        // 检验失败抛出异常并返回错误文档
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(sheetIndex, "导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        //构建可保存数据
        List<DtoAnalyzeItem> waitSaveList = new ArrayList<>();
        buildRightData(result, waitSaveList);
        //数据保存
        analyzeItemRepository.save(waitSaveList);
    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {

        List<DtoExportAnalyzeItem> analyzeItemList = testDependentData.getAnalyzeItemList();

        List<DtoImportCheck> importChecks = dtoDataSyncParams.getImportChecks();
        // 筛选出检查类型为新增的分析项目数据
        Optional<DtoImportCheck> optional = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.分析项目表.getCheckItem()) &&
                BASE_DATA_TYPE[0].equals(p.getCheckType())).findFirst();
        List<DtoDataCheck> analyzeItemCheck = optional.isPresent() ? optional.get().getDataChecks() : new ArrayList<>();
        List<String> addAnalyzeItemName = analyzeItemCheck.stream().map(DtoDataCheck::getName).collect(Collectors.toList());
        List<DtoAnalyzeItem> analyzeItems = analyzeItemMapper.toDtoAnalyzeItemList(analyzeItemList.stream()
                .filter(p -> addAnalyzeItemName.contains(p.getAnalyzeItemName())).collect(Collectors.toList()));
        importTestTemp.setAnalyzeItemTemps(analyzeItems);
        // 根据导出测试项目筛选分析项目
        List<DtoExportTest> testList = exportData.getTestList();
        if (StringUtil.isNotEmpty(testList)) {
            List<String> analyzeItemIds = testList.stream().map(DtoExportTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
            List<DtoExportAnalyzeItem> exportAnalyzeItems = analyzeItemList.stream().filter(p -> analyzeItemIds.contains(p.getId())).collect(Collectors.toList());
            exportData.setAnalyzeItemList(exportAnalyzeItems);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoAnalyzeItem> analyzeItemTemps = importTestTemp.getAnalyzeItemTemps();

        if (StringUtil.isNotEmpty(analyzeItemTemps)) {
            int i = 0;
            //已同步记录数
            String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_AnalyzeItem.getValue());
            for (DtoAnalyzeItem dtoAnalyzeItem : analyzeItemTemps) {
                DtoAnalyzeItem item = analyzeItemRepository.save(dtoAnalyzeItem);
                // 更新redis 数据
                redisTemplate.opsForHash().put(key, item.getId(), JsonStream.serialize(item));
                webSocketServer.sendMessage(getMessage(analyzeItemTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(analyzeItemTemps.size(), 0));
        }
    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.分析项目表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.分析项目表.getSource();
    }

    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.分析项目表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        List<DtoExportAnalyzeItem> analyzeItemList = testDependentData.getAnalyzeItemList();
        List<DtoImportCheck> importChecks = new ArrayList<>();
        List<DtoDataCheck> analyzeItemCheckList = new ArrayList<>();
        if (StringUtil.isNotEmpty(analyzeItemList)) {
            List<DtoAnalyzeItem> allAnalyzeItems = analyzeItemRepository.findAll();
            analyzeItemCheckList = analyzeItemList.parallelStream()
                    .map(p -> {
                        DtoDataCheck dtoDataCheck = new DtoDataCheck();
                        dtoDataCheck.setName(p.getAnalyzeItemName());
                        Map<String, Object> otherField = new HashMap<>();
                        otherField.put("analyzeItemName", p.getAnalyzeItemName());
                        dtoDataCheck.setOtherField(otherField);
                        Optional<DtoAnalyzeItem> analyzeItemOptional = allAnalyzeItems.stream().filter(r -> r.getAnalyzeItemName().equals(p.getAnalyzeItemName())).findFirst();
                        if (analyzeItemOptional.isPresent()) {
                            dtoDataCheck.setType(BASE_DATA_TYPE[1]);
                            dtoDataCheck.setId(analyzeItemOptional.get().getId());
                        } else {
                            dtoDataCheck.setType(BASE_DATA_TYPE[0]);
                            dtoDataCheck.setId(p.getId());
                        }
                        return dtoDataCheck;
                    }).collect(Collectors.toList());
        }
        List<DtoDataCheck> existsList = analyzeItemCheckList.stream().filter(p -> BASE_DATA_TYPE[1].equals(p.getType())).collect(Collectors.toList());
        List<DtoDataCheck> noExistsList = analyzeItemCheckList.stream().filter(p -> BASE_DATA_TYPE[0].equals(p.getType())).collect(Collectors.toList());
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.分析项目表.getCheckItem(),
                BASE_DATA_TYPE[1], existsList.size(), "所有外键ID关联改为系统内的ID，不进行导入。",
                existsList));
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.分析项目表.getCheckItem(),
                BASE_DATA_TYPE[0], noExistsList.size(), "新增分析项目，导入时将进行插入。",
                noExistsList));

        return importChecks;
    }

    /**
     * 校验容器初始化
     */
    private void handleInit() {
        transformAnalyzeItemVerifyHandler = new TransformAnalyzeItemVerifyHandler();
        transformAnalyzeItemVerifyHandler.setRepoDataList(analyzeItemRepository.findAll());
        transformAnalyzeItemVerifyHandler.setSheetExistDataList(new ArrayList<>());
    }

    /**
     * 构建可保存数据
     *
     * @param result       导入结果集
     * @param waitSaveList 待保存结果容器
     */
    private void buildRightData(ExcelImportResult<DtoExportAnalyzeItem> result, List<DtoAnalyzeItem> waitSaveList) {
        List<DtoExportAnalyzeItem> importList = result.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        for (DtoExportAnalyzeItem exportAnalyzeItem : importList) {
            DtoAnalyzeItem dtoAnalyzeItem = new DtoAnalyzeItem();
            BeanUtils.copyProperties(exportAnalyzeItem, dtoAnalyzeItem);
            waitSaveList.add(dtoAnalyzeItem);
        }
    }

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    public void setAnalyzeItemMapper(AnalyzeItemMapper analyzeItemMapper) {
        this.analyzeItemMapper = analyzeItemMapper;
    }
}
