package com.sinoyd.lims.pro.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.pro.criteria.OutSourceDataCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoImportOutSourceData;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.OutSourceDataRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.verify.OutSourceDataVerifyHandle;
import com.sinoyd.lims.pro.vo.OutSourceDataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 分包数据实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/2/8
 */
@Service
@Slf4j
public class OutSourceDataServiceImpl extends BaseJpaServiceImpl<DtoOutSourceData, String, OutSourceDataRepository> implements OutSourceDataService {

    private AnalyseDataService analyseDataService;

    private SampleService sampleService;

    private SampleFolderService sampleFolderService;

    private SampleTypeService sampleTypeService;

    private ProjectService projectService;

    private AnalyzeMethodRepository analyzeMethodRepository;

    private OutSourceDataVerifyHandle outSourceDataVerifyHandle;

    private DimensionRepository dimensionRepository;
    private SampleRepository sampleRepository;


    @Override
    public void findByPage(PageBean<DtoOutSourceData> page, BaseCriteria criteria) {
        long t1 = System.currentTimeMillis();
        page.setEntityName("DtoSample s,DtoAnalyseData a,DtoSampleType t");
        page.setSelect("select new com.sinoyd.lims.pro.dto.DtoOutSourceData(t.typeName as sampleTypeName,s.samplingTimeBegin as samplingTime,s.redFolderName," +
                "s.code as sampleCode,a.redAnalyzeItemName as analyzeItemName," +
                "a.redAnalyzeMethodName,a.dimension,a.id,a.analyzeMethodId,a.dimensionId )");
        super.findByPage(page, criteria);
        long t2 = System.currentTimeMillis();
        log.info("分包数据查询耗时："+(t2-t1));
        fillingPageTransientFields(page.getData());
        long t3 = System.currentTimeMillis();
        log.info("分包附加数据查询耗时："+(t3-t2));
    }

    @Override
    @Transactional
    public List<DtoOutSourceData> update(List<DtoOutSourceData> entitys) {
        //判断是否确认数据
        Boolean isConfirm = true;
        for (DtoOutSourceData data : entitys) {
            if (data.getState().equals(0)) {
                isConfirm = false;
            }
        }
        if (isConfirm) {
            List<String> analyseDataIds = entitys.stream().map(DtoOutSourceData::getAnalyseDataId).collect(Collectors.toList());
            List<DtoAnalyseData> analyseDataList = analyseDataService.findAll(analyseDataIds);
            for (DtoAnalyseData analyseData : analyseDataList) {
                Optional<DtoOutSourceData> outSourceData = entitys.stream().filter(e -> e.getAnalyseDataId().equals(analyseData.getId())).findFirst();
                outSourceData.ifPresent(o -> {
                    analyseData.setStatus(EnumPRO.EnumAnalyseDataStatus.已确认.name());
                    analyseData.setDataStatus(EnumPRO.EnumAnalyseDataStatus.已确认.getValue());
                    analyseData.setTestValue(o.getTestValue());
                });
            }
            analyseDataService.save(analyseDataList);
        }
        return super.save(entitys);
    }

    @Override
    public Map<String, String> findAllAnalyzeMethod() {
        List<DtoOutSourceData> dtoOutSourceDataAll = repository.findByAnalyzeMethodNameIsNotNullAndAnalyzeMethodNameNot("");
        List<DtoAnalyzeMethod> dtoAnalyzeMethodAll = analyzeMethodRepository.findAll();
        Map<String, String> map = new LinkedHashMap<>();
        Map<String, String> analyzeMethodMap = new LinkedHashMap<>();
        for (DtoOutSourceData dtoOutSourceData : dtoOutSourceDataAll) {
            map.put(dtoOutSourceData.getAnalyzeMethodName(), dtoOutSourceData.getAnalyzeMethodId());
        }
        for (DtoAnalyzeMethod dtoAnalyzeMethod : dtoAnalyzeMethodAll) {
            map.put(dtoAnalyzeMethod.getMethodName(), dtoAnalyzeMethod.getId());
        }
        map.entrySet().stream().sorted(Collections.reverseOrder(Map.Entry.comparingByKey())).forEachOrdered(item -> analyzeMethodMap.put(item.getKey(), item.getValue()));
        return analyzeMethodMap;
    }

    @Override
    @Transactional
    public List<DtoOutSourceData> updateBatch(OutSourceDataVO vo) {
        List<DtoOutSourceData> dtoList = vo.getDtoList();
        //删除表中原有数据
        List<String> analyseDataIds = dtoList.stream().map(DtoOutSourceData::getAnalyseDataId).collect(Collectors.toList());
        repository.deleteByAnalyseDataIds(analyseDataIds, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        //批量新增数据
        for (DtoOutSourceData dtoOutSourceData : dtoList) {
            dtoOutSourceData.setId(UUIDHelper.NewID());
            //设置分析方法
            dtoOutSourceData.setAnalyzeMethodName(vo.getAnalyzeMethodName());
            if (StringUtil.isNotEmpty(vo.getAnalyzeMethodId())) {
                dtoOutSourceData.setAnalyzeMethodId(vo.getAnalyzeMethodId());
            } else {
                dtoOutSourceData.setAnalyseDataId(UUIDHelper.GUID_EMPTY);
            }
            //设置量纲
            dtoOutSourceData.setDimensionName(vo.getDimensionName());
            if (StringUtil.isNotEmpty(vo.getDimensionId())) {
                dtoOutSourceData.setDimensionId(vo.getDimensionId());
            } else {
                dtoOutSourceData.setDimensionId(UUIDHelper.GUID_EMPTY);
            }
        }
        return super.save(dtoList);
    }

    @Override
    public Map<String, String> findAllDimensionName() {
        List<DtoDimension> dimensionAll = dimensionRepository.findAll();
        List<DtoOutSourceData> dtoOutSourceDataAll = repository.findByDimensionNameIsNotNullAndDimensionNameNot("");
        Map<String, String> map = new LinkedHashMap<>();
        Map<String, String> dimensionMap = new LinkedHashMap<>();
        for (DtoDimension dtoDimension : dimensionAll) {
            map.put(dtoDimension.getDimensionName(), dtoDimension.getId());
        }
        for (DtoOutSourceData dtoOutSourceData : dtoOutSourceDataAll) {
            map.put(dtoOutSourceData.getDimensionName(), dtoOutSourceData.getDimensionId());
        }
        map.entrySet().stream().sorted(Collections.reverseOrder(Map.Entry.comparingByKey())).forEachOrdered(item -> dimensionMap.put(item.getKey(), item.getValue()));
        return dimensionMap;
    }


    @Override
    public DtoOutSourceData findOne(String key) {
        DtoOutSourceData dtoData = repository.findByAnalyseDataId(key);
        DtoOutSourceData dto = new DtoOutSourceData();
        DtoAnalyseData dtoAnalyseData = analyseDataService.findOne(key);
        if (StringUtil.isNotNull(dtoAnalyseData)) {
            dto.setRedAnalyzeItemName(dtoAnalyseData.getRedAnalyzeItemName());
            dto.setAnalyseDataId(dtoAnalyseData.getId());
            dto.setAnalyzeMethodName(dtoAnalyseData.getRedAnalyzeMethodName());
            dto.setAnalyzeMethodId(dtoAnalyseData.getAnalyzeMethodId());
            dto.setTestValue(dtoAnalyseData.getTestValue());
            dto.setDimensionName(dtoAnalyseData.getDimension());
            dto.setDimensionId(dtoAnalyseData.getDimensionId());
            dto.setState(EnumPRO.EnumOutSourceDataStatus.未完成.getValue());
            //当分包数据记录表中有数据时
            if (StringUtil.isNotNull(dtoData)) {
                dto.setId(dtoData.getId());
                dto.setState(dtoData.getState());
                dto.setAnalyzeMethodName(dtoData.getAnalyzeMethodName());
                dto.setAnalyzeMethodId(dtoData.getAnalyzeMethodId());
                dto.setDimensionId(dtoData.getDimensionId());
                dto.setDimensionName(dtoData.getDimensionName());
                dto.setDetectionLimit(dtoData.getDetectionLimit());
                dto.setTestValue(dtoData.getTestValue());
                dto.setSubcontractor(dtoData.getSubcontractor());
                dto.setAnalyzeTime(dtoData.getAnalyzeTime());
                dto.setAnalyzeEndTime(dtoData.getAnalyzeEndTime());
                dto.setCmaCode(dtoData.getCmaCode());
                dto.setOutSourceReportCode(dtoData.getOutSourceReportCode());
            }

            DtoSample dtoSample = sampleService.findOne(dtoAnalyseData.getSampleId());
            if (StringUtil.isNotNull(dtoSample)) {
                DtoSampleFolder dtoSampleFolder = sampleFolderService.findOne(dtoSample.getSampleFolderId());
                DtoSampleType dtoSampleType = sampleTypeService.findOne(dtoSample.getSampleTypeId());
                //设置样品信息
                dto.setSampleCode(dtoSample.getCode())
                        .setSampleFolderName(StringUtil.isNotNull(dtoSampleFolder) ? dtoSampleFolder.getWatchSpot() : null)
                        .setSampleStatus(dtoSample.getStatus())
                        .setSamplingTime(dtoSample.getSamplingTimeBegin())
                        .setSampleType(StringUtil.isNotNull(dtoSampleType) ? dtoSampleType.getTypeName() : null);
                String projectId = dtoSample.getProjectId();
                if (StringUtil.isEmpty(projectId) || UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    List<DtoSample> associateSampleList = sampleRepository.findByIds(Collections.singletonList(dtoSample.getAssociateSampleId()));
                    if (StringUtil.isNotEmpty(associateSampleList)) {
                        projectId = associateSampleList.get(0).getProjectId();
                    }
                }
                DtoProject dtoProject = (StringUtil.isNotEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId))
                        ? projectService.findOne(projectId) : null;
                //设置项目信息
                if (StringUtil.isNotNull(dtoProject)) {
                    dto.setProjectCode(dtoProject.getProjectCode())
                            .setProjectName(dtoProject.getProjectName())
                            .setCustomerName(dtoProject.getCustomerName())
                            .setInspectedEnt(dtoProject.getInspectedEnt());
                }
            }
        }
        return dto;
    }

    @Override
    @Transactional
    public void importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) {
        PoiExcelUtils.verifyFileType(file);
        OutSourceDataCriteria criteria = new OutSourceDataCriteria();
        List<DtoOutSourceData> dtoList = getAllPageData(criteria);
        outSourceDataVerifyHandle.getExtendTl().set(getOldImportData(dtoList));
        //获取excel数据
        ExcelImportResult<DtoImportOutSourceData> importResult = getExcelData(file, response);
        outSourceDataVerifyHandle.getExtendTl().remove();
        if (importResult.isVerfiyFail()) {
            Workbook failedWorkbook = new XSSFWorkbook();
            failedWorkbook.createSheet("分包数据导入错误信息");
            Workbook currentFailWorkbook = importResult.getFailWorkbook();
            PoiExcelUtils.copySheet(failedWorkbook.getSheet("分包数据导入错误信息"), currentFailWorkbook.getSheetAt(0), failedWorkbook);
            PoiExcelUtils.downLoadExcel("分包数据导入错误报告_" + DateUtil.nowTime("yyyyMMddHHmmss"),
                    response, failedWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        //获取校验成功得导入数据
        List<DtoImportOutSourceData> importList = importResult.getList();
        //获取导入数据相关分析数据
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        List<String> analyseDataIdList = importList.stream().map(DtoImportOutSourceData::getAnalyseDataId).collect(Collectors.toList());
        List<DtoOutSourceData> dataList = importToEntity(importList);
        repository.deleteByAnalyseDataIds(analyseDataIdList, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        super.save(dataList);
    }


    @Override
    public ExcelImportResult<DtoImportOutSourceData> getExcelData(MultipartFile file, HttpServletResponse response) {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(1);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(outSourceDataVerifyHandle);
        ExcelImportResult<DtoImportOutSourceData> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoImportOutSourceData.class, params);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException(e.getMessage());
        }

        return result;
    }

    @Override
    @Transactional
    public void exportExcel(BaseCriteria criteria, HttpServletResponse response) {
        List<DtoOutSourceData> pageData = getAllPageData(criteria);
        if (StringUtil.isNotEmpty(pageData)) {
            List<DtoImportOutSourceData> exportList = new ArrayList<>();
            for (DtoOutSourceData data : pageData) {
                DtoImportOutSourceData importOutSourceData = new DtoImportOutSourceData();
                importOutSourceData.setState(EnumPRO.EnumOutSourceDataStatus.getByValue(data.getState()));
                BeanUtils.copyProperties(data, importOutSourceData);
                exportList.add(importOutSourceData);
            }
            //调用导出api
            PoiExcelUtils.exportExcel(exportList, "分包数据一览表", "分包数据", DtoImportOutSourceData.class, "分包数据", true, response);
        }
    }

    /**
     * 获取所有分页数据
     *
     * @param criteria 查询条件
     * @return 分页数据
     */
    private List<DtoOutSourceData> getAllPageData(BaseCriteria criteria) {
        PageBean<DtoOutSourceData> pageBean = new PageBean<>();
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        pageBean.setSort("samplingTime-, sampleTypeName+, sampleCode+, analyzeItemName+");
        findByPage(pageBean, criteria);
        return pageBean.getData();
    }

    /**
     * excel导入类与数据库实体转换
     *
     * @param importList excel导入数据
     * @return 实体转换数据
     */
    private List<DtoOutSourceData> importToEntity(List<DtoImportOutSourceData> importList) {
        List<DtoOutSourceData> dtoList = new ArrayList<>();
        Map<String, String> allAnalyzeMethodMap = findAllAnalyzeMethod();
        Map<String, String> allDimensionNameMap = findAllDimensionName();
        for (DtoImportOutSourceData importData : importList) {
            DtoOutSourceData dto = new DtoOutSourceData();
            BeanUtils.copyProperties(importData, dto);
            dto.setState(EnumPRO.EnumOutSourceDataStatus.已确认.getValue());
            dto.setAnalyzeMethodId(UUIDHelper.GUID_EMPTY);
            dto.setDimensionId(UUIDHelper.GUID_EMPTY);
            if (EnumPRO.EnumOutSourceDataStatus.getByValue(EnumPRO.EnumOutSourceDataStatus.未完成.getValue()).equals(importData.getState())) {
                dto.setState(EnumPRO.EnumOutSourceDataStatus.未完成.getValue());
            }

            if (StringUtil.isNotNull(allAnalyzeMethodMap)) {
                if (StringUtil.isNotEmpty(allAnalyzeMethodMap.get(importData.getAnalyzeMethodName()))) {
                    dto.setAnalyzeMethodId(allAnalyzeMethodMap.get(importData.getAnalyzeMethodName()));
                }
            }

            if (StringUtil.isNotNull(allDimensionNameMap)) {
                if (StringUtil.isNotEmpty(allDimensionNameMap.get(importData.getDimensionName()))) {
                    dto.setDimensionId(allDimensionNameMap.get(importData.getDimensionName()));
                }
            }
            dtoList.add(dto);
        }
        return dtoList;
    }

    /**
     * 获取数据导入前原数据内容
     *
     * @param dtoList 分页数据
     * @return 报表数据
     */
    private List<DtoImportOutSourceData> getOldImportData(List<DtoOutSourceData> dtoList) {
        List<DtoImportOutSourceData> importDataList = new ArrayList<>();
        for (DtoOutSourceData dto : dtoList) {
            DtoImportOutSourceData dtoImportData = new DtoImportOutSourceData();
            BeanUtils.copyProperties(dto, dtoImportData);
            dtoImportData.setState(EnumPRO.EnumOutSourceDataStatus.getByValue(dto.getState()));
            importDataList.add(dtoImportData);
        }
        return importDataList;
    }

    /**
     * 分页数据填充
     *
     * @param dataList 分页数据
     */
    private void fillingPageTransientFields(List<DtoOutSourceData> dataList) {
        if (StringUtil.isNotEmpty(dataList)) {
            List<String> analyseDataIds = dataList.stream().map(DtoOutSourceData::getAnalyseDataId).distinct().collect(Collectors.toList());
            //查询出分包数据
            List<DtoOutSourceData> outSourceDataList = repository.findByAnalyseDataIdIn(analyseDataIds);
            //如果存在分包数据，需要用分包数据来覆盖分页数据的相关属性
            if (StringUtil.isNotEmpty(outSourceDataList)) {
                for (DtoOutSourceData data : dataList) {
                    Optional<DtoOutSourceData> outSourceDataOptional = outSourceDataList.stream()
                            .filter(p -> p.getAnalyseDataId().equals(data.getAnalyseDataId())).findFirst();
                    if (outSourceDataOptional.isPresent()) {
                        data.setId(outSourceDataOptional.get().getId());
                        data.setAnalyzeMethodName(outSourceDataOptional.get().getAnalyzeMethodName());
                        data.setAnalyzeMethodId(outSourceDataOptional.get().getAnalyzeMethodId());
                        data.setTestValue(outSourceDataOptional.get().getTestValue());
                        data.setDimensionName(outSourceDataOptional.get().getDimensionName());
                        data.setDimensionId(outSourceDataOptional.get().getDimensionId());
                        data.setDetectionLimit(outSourceDataOptional.get().getDetectionLimit());
                        data.setState(outSourceDataOptional.get().getState());
                        data.setAnalyzeTime(outSourceDataOptional.get().getAnalyzeTime());
                        // 新增分析结束日期
                        data.setAnalyzeEndTime(outSourceDataOptional.get().getAnalyzeEndTime());
                        data.setSubcontractor(outSourceDataOptional.get().getSubcontractor());
                        data.setCmaCode(outSourceDataOptional.get().getCmaCode());
                        data.setOutSourceReportCode(outSourceDataOptional.get().getOutSourceReportCode());
                    } else {
                        //如果不存在分包数据，数据是从数据表加载出来的，状态一律默认为未完成
                        data.setState(EnumPRO.EnumOutSourceDataStatus.未完成.getValue());
                    }
                }
            }
        }
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    @Lazy
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    @Lazy
    public void setSampleFolderService(SampleFolderService sampleFolderService) {
        this.sampleFolderService = sampleFolderService;
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    @Lazy
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }

    @Autowired
    @Lazy
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    @Lazy
    public void setOutSourceDataVerifyHandle(OutSourceDataVerifyHandle outSourceDataVerifyHandle) {
        this.outSourceDataVerifyHandle = outSourceDataVerifyHandle;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }
}