package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.customer.DtoProjectTest;

import java.util.List;

/**
 * 项目指标操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/27
 * @since V100R001
 */
public interface ProjectTestService {
    /**
     * 保存项目指标
     *
     * @param projectId       项目id
     * @param projectTestList 项目指标
     */
    void saveProjectTest(String projectId, List<DtoProjectTest> projectTestList);

    /**
     * 修改/添加项目指标
     *
     * @param projectId       项目id
     * @param projectTestList 项目指标
     */
     void modifyProjectTest(String projectId, List<DtoProjectTest> projectTestList);

    /**
     * 移除项目指标
     *
     * @param projectId       项目id
     */
    void removeProjectTest(String projectId);

    /**
     * 获取项目已有测试项目
     *
     * @param projectId      项目id
     * @param sampleTypeId   检测类型id
     * @param analyseItemIds 分析因子id集合
     * @return 已有测试项目
     */
    List<DtoTest> getProjectTest(String projectId, String sampleTypeId, List<String> analyseItemIds);
}
