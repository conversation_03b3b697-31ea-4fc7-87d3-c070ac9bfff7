package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoCustomerViolate;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.CustomerViolateRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.CustomerViolateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * CustomerViolate操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@Service
@RequiredArgsConstructor
public class CustomerViolateServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCustomerViolate, String, CustomerViolateRepository> implements CustomerViolateService {

    private final PersonRepository personRepository;

    private final EnterpriseRepository enterpriseRepository;

    @Override
    public void findByPage(PageBean<DtoCustomerViolate> pb, BaseCriteria customerViolateCriteria) {
        pb.setEntityName("DtoCustomerViolate a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, customerViolateCriteria);
        List<DtoCustomerViolate> pageData = pb.getData();
        List<DtoPerson> people = personRepository.findAll();
        List<String> enterpriseIds = pageData.stream().map(DtoCustomerViolate::getEnterpriseId).collect(Collectors.toList());
        List<DtoEnterprise> enterprises = StringUtil.isNotEmpty(enterpriseIds) ? enterpriseRepository.findAll(enterpriseIds) : new ArrayList<>();
        pageData.forEach(d -> {
            people.forEach(p -> {
                if (p.getId().equals(d.getCreator())) {
                    d.setCreator(p.getCName());
                }
                if (p.getId().equals(d.getRegisterPersonId())) {
                    d.setRegisterPersonName(p.getCName());
                }
                if (p.getId().equals(d.getHandlePersonId())) {
                    d.setHandlePersonName(p.getCName());
                }
            });
            enterprises.forEach(e -> {
                if (e.getId().equals(d.getEnterpriseId())) {
                    d.setEnterpriseName(e.getName());
                }
            });
        });
    }

    /**
     * 重写新增方法
     *
     * @param entity 要保存的实体
     * @return 保存后的实体
     */
    @Override
    @Transactional
    public DtoCustomerViolate save(DtoCustomerViolate entity) {
        CurrentPrincipalUser currentUser = PrincipalContextUser.getPrincipal();
        //如果没有传回登记时间则登记时间默认为当前时间
        if (StringUtils.isNull(entity.getRegisterTime())) {
            entity.setRegisterTime(new Date());
        }
        //如果没有传回登记人id，默认填充当前登录人id
        if (StringUtil.isEmpty(entity.getRegisterPersonId())) {
            entity.setRegisterPersonId(currentUser.getUserId());
        }
        super.save(entity);
        return entity;
    }
}