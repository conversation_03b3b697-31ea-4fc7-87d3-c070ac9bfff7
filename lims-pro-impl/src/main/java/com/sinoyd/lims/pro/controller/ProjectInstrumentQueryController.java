package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentAccessRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.lims.lim.dto.customer.DtoProjectInstrumentAccess;
import com.sinoyd.lims.pro.criteria.ProjectInstrumentQueryCriteria;
import com.sinoyd.lims.pro.service.ProjectInstrumentQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * ProjectInstrument查询服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
 @Api(tags = "示例: ProjectInstrument查询服务")
 @RestController
 @RequestMapping("api/lim/projectInstrument")
 public class ProjectInstrumentQueryController extends BaseJpaController<DtoProjectInstrument, String,ProjectInstrumentQueryService> {


    /**
     * 分页动态条件查询ProjectInstrument
     * @param projectInstrumentQueryCriteria 条件参数
     * @return RestResponse<List<ProjectInstrument>>
     */
     @ApiOperation(value = "分页动态条件查询ProjectInstrument", notes = "分页动态条件查询ProjectInstrument")
     @GetMapping
     public RestResponse<List<DtoProjectInstrument>> findByPage(ProjectInstrumentQueryCriteria projectInstrumentQueryCriteria) {
         PageBean<DtoProjectInstrument> pageBean = super.getPageBean();
         RestResponse<List<DtoProjectInstrument>> restResponse = new RestResponse<>();
         service.findByPageQuery(pageBean, projectInstrumentQueryCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

    /**
     * 分页动态条件查询单个仪器的出入库记录
     * @param instrumentAccessRecordCriteria 条件参数
     * @return RestResponse<List<ProjectInstrument>>
     */
    @ApiOperation(value = "分页动态条件查询ProjectInstrument", notes = "分页动态条件查询ProjectInstrument")
    @GetMapping("/instrumentAccessRecord")
    public RestResponse<List<DtoProjectInstrumentAccess>> findAccessRecordByPage(InstrumentAccessRecordCriteria instrumentAccessRecordCriteria) {
        PageBean<DtoProjectInstrumentAccess> pageBean = super.getPageBean();
        RestResponse<List<DtoProjectInstrumentAccess>> restResponse = new RestResponse<>();
        service.findAccessRecordByPage(pageBean, instrumentAccessRecordCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }



     /**
     * 按主键查询ProjectInstrument
     * @param id 主键id
     * @return RestResponse<DtoProjectInstrument>
     */
     @ApiOperation(value = "按主键查询ProjectInstrument", notes = "按主键查询ProjectInstrument")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoProjectInstrument> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoProjectInstrument> restResponse = new RestResponse<>();
         DtoProjectInstrument projectInstrument = service.findOne(id);
         restResponse.setData(projectInstrument);
         restResponse.setRestStatus(StringUtil.isNull(projectInstrument) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }
 }