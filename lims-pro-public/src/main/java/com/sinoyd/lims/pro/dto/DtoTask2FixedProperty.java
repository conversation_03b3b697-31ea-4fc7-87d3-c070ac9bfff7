package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.Task2FixedProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoTask2FixedProperty实体
 * <AUTHOR>
 * @version V1.0.0 2023/08/08
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_Task2FixedProperty")
@Data
@DynamicInsert
public class DtoTask2FixedProperty extends Task2FixedProperty {
}
