package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

/**
 * 项目进度采样情况
 * <AUTHOR>
 * @version V1.0.0 2020/2/7
 * @since V100R001
 */
@Data
public class DtoProjectInquirySamplingInfoDetail {
    /**
     * 大类id
     */
    private String bigSampleTypeId;

    /**
     * 小类id
     */
    private String sampleTypeId;

    /**
     * 小类名称
     */
    private String sampleTypeName;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 点位名称
     */
    private String watchSpot;

    /**
     * 采样状态
     */
    private Integer samplingStatus;

    /**
     * 未采个数
     */
    private Integer notSampled;

    /**
     * 已采个数（预留）
     */
    private Integer sampled;

    public DtoProjectInquirySamplingInfoDetail(){

    }

    public DtoProjectInquirySamplingInfoDetail(String bigSampleTypeId,String sampleTypeId,String sampleTypeName,String sampleFolderId,String watchSpot,Integer samplingStatus) {
        this.bigSampleTypeId = bigSampleTypeId;
        this.sampleTypeId = sampleTypeId;
        this.sampleTypeName = sampleTypeName;
        this.sampleFolderId = sampleFolderId;
        this.watchSpot = watchSpot;
        this.samplingStatus = samplingStatus;
        if (samplingStatus.equals(EnumPRO.EnumSamplingStatus.需要取样还未取样.getValue())) {
            this.notSampled = 1;
        } else {
            this.notSampled = 0;
        }
    }
}
