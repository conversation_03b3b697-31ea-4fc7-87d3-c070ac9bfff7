package com.sinoyd.base.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;

import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 混标信息查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsumableOfMixedCriteria extends BaseCriteria {

    // 标准样品id
    private String consumableId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(consumableId)) {
            condition.append(" and consumableId = :consumableId");
            values.put("consumableId", this.consumableId);
        }
        return condition.toString();
    }
}