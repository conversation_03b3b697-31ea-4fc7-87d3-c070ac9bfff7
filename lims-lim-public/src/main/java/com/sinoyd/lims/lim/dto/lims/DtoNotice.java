package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.entity.Notice;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.List;


/**
 * DtoNotice实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_Notice")
@Data
@DynamicInsert
public class DtoNotice extends Notice {
    
    @Transient
    private List<String> labelArray;

    @Transient
    private Integer msgCount;

   /**
     * 公告类型名称，用于文件附件机制
     */
    @Transient
    private String categoryName;


    /**
     * 发布日期的字符型，用于文件附件机制
     */
    @Transient
    private String releaseDateStr;
}