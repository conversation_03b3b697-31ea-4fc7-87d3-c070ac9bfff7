package com.sinoyd.lims.pro.strategy.strategy.dataValidator;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日期校验
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/09/23
 */
@Component(IFileNameConstant.DataValidateStrategyKey.DATE_VALIDATE)
public class DateValidator extends AbsDataValidator {

    private static final String dateRegular = "(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|" +
            "((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)";

    @Override
    public Boolean validate(Object value, Map<String, Object> map) {
        String valStr = StringUtil.isNotNull(value) ? value.toString() : "";
        if (StringUtil.isEmpty(valStr)) {
            return true;
        }
        Matcher matcher = Pattern.compile(dateRegular).matcher(valStr);
        return matcher.find();
    }

    @Override
    public Integer getControlType() {
        return 2;
    }
}
