package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SamplingPersonConfig;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoSamplingPersonConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_SamplingPersonConfig")
 @Data
 @DynamicInsert
 public  class DtoSamplingPersonConfig extends SamplingPersonConfig {
   private static final long serialVersionUID = 1L;
 }