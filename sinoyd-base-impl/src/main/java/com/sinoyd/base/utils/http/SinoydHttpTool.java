package com.sinoyd.base.utils.http;

import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 远大Http请求工具
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/11/10
 */
public class SinoydHttpTool {

    /**
     * post请求， 返回applicationo/json形式
     *
     * @param url     请求url
     * @param params  参数
     * @param headers 请求头部信息
     * @return 请求结果的String形式
     */
    public static String doPost(String url, Object params, Map<String, String> headers) {
        SinoydHttpClient client = new SinoydHttpClient();
        String response = client.doPost(url, params, headers);
        client.close();
        return response;
    }

    /**
     * get请求， 返回applicationo/json形式
     *
     * @param url     请求url
     * @param params  参数
     * @param headers 请求头部信息
     * @return 请求结果的String形式
     */
    public static String doGet(String url, Map<String, Object> params, Map<String, String> headers) {
        SinoydHttpClient client = new SinoydHttpClient();
        String response = client.doGet(url, params, headers);
        client.close();
        return response;
    }

    /**
     * 访问webService接口
     *
     * @param url       请求url
     * @param reqData   参数xml格式
     * @return  请求结果的String形式
     */
    public static String doRequest(String url, String reqData){
        SinoydHttpClient client = new SinoydHttpClient();
        String response = client.doRequest(reqData,url);
        client.close();
        return response;
    }

    /**
     * 获取头部信息
     *
     * @param token token
     * @return 头部信息
     */
    public static Map<String, String> getHttpHeaders(String token) {
        Map<String, String> map = new HashMap<>();
        map.put("Accept", "application/json");
        map.put("Content-Type", "application/json");
        if (!StringUtils.isEmpty(token)) {
            map.put("Authorization", token);
        }
        return map;
    }
}