package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoCertHistoryInfo;

import java.util.List;

/**
 * DtoCertHistoryFile
 * <AUTHOR>
 * @version V1.0.0
 * @since  2024/11/26
 */
public interface CertHistoryInfoRepository extends IBaseJpaPhysicalDeleteRepository<DtoCertHistoryInfo,String> {
    /**
     * 根据项目标识查询
     * @param projectId 项目标识
     * @return 结果
     */
    List<DtoCertHistoryInfo> findByProjectId(String projectId);
}
