package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * TestOperateLog实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/02/20
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "TestOperateLog")
@Data
public class TestOperateLog implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public TestOperateLog() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    @ApiModelProperty("主键id")
    private String id = UUIDHelper.NewID();

    /**
     * 表名
     */
    @Column(length = 50,nullable=false)
    @ApiModelProperty("表名")
    @Length(message = "表名{validation.message.length}", max = 50)
    private String tableName;

    /**
     * 表主键
     */
    @Column(length = 50,nullable=false)
    @ApiModelProperty("表主键")
    @Length(message = "表主键{validation.message.length}", max = 50)
    private String tableId;

    /**
     * 操作人标识
     */
    @Column(length = 50,nullable=false)
    @ApiModelProperty("操作人标识")
    private String operatorId;

    /**
     * 操作时间
     */
    @Column(nullable=false)
    @ApiModelProperty("操作时间")
    private Date operatorDate;

    /**
     * 操作类型
     */
    @Column(nullable=false)
    @ApiModelProperty("操作类型")
    private Integer operateType;

    /**
     * 操作字段
     */
    @Column(length = 50)
    @ApiModelProperty("操作字段")
    @Length(message = "操作字段{validation.message.length}", max = 50)
    private String operateField;

    /**
     * 旧值
     */
    @Column(length = 4000)
    @ApiModelProperty("旧值")
    @Length(message = "旧值{validation.message.length}", max = 4000)
    private String oldValue;

    /**
     * 新值
     */
    @Column(length = 4000)
    @ApiModelProperty("新值")
    @Length(message = "新值{validation.message.length}", max = 4000)
    private String newValue;

    /**
     * 组织机构id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;
}
