package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * SerialIdentifierConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SerialIdentifierConfig")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class SerialIdentifierConfig implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  SerialIdentifierConfig() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 配置编号(预留)
    */
    @Column(length=100)
    @ApiModelProperty("配置编号(预留)")
    @Length(message = "配置编号{validation.message.length}", max = 100)
    private String configCode;
    
    /**
    * 配置名称
    */
    @Column(length=100)
    @ApiModelProperty("配置名称")
    @Length(message = "配置名称{validation.message.length}", max = 100)
    private String configName;
    
    /**
    * 1:项目编号,2:样品编号,3:质控样编号,4:送样单编号,5:报告编号,6:检测单编号
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("1:项目编号,2:样品编号,3:质控样编号,4:送样单编号,5:报告编号,6:检测单编号,7:质控任务编号")
    private Integer configType;
    
    /**
    * 配置规则
    */
    @Column(length=1000)
    @ApiModelProperty("配置规则")
    @Length(message = "配置规则{validation.message.length}", max = 1000)
    private String configRule;

    /**
     * 项目类型--只有当选择项目编号的时候才启用
     */
    @Column(length=1000,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目类型id")
    @Length(message = "项目类型id{validation.message.length}", max = 1000)
    private String projectTypeId;

    /**
    * 质控类型--只有当选择质控样编号的时候才启用（枚举EnumQCGrade：0.外部质控  1.内部质控）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("质控类型--只有当选择质控样编号的时候才启用（枚举EnumQCGrade：0.外部质控  1.内部质控）")
    private Integer qcType;
    
    /**
    * 质控等级--只有当选择质控样编号的时候才启用（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("质控等级--只有当选择质控样编号的时候才启用（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）")
    private Integer qcGrade;
    
    /**
    * 排序号
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("排序号")
    private Integer orderNum;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }