package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.FileControlApplyDetail;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.Date;
import java.util.List;


/**
 * DtoFileControlApplyDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_FileControlApplyDetail")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoFileControlApplyDetail extends FileControlApplyDetail {

    /**
     * 文件类型名称
     */
    @Transient
    private String fileTypeName;
//
//    /**
//     * 编制人姓名
//     */
//    @Transient
//    private String makerName;
//
//    /**
//     * 上传人姓名
//     */
//    @Transient
//    private String uploaderName;



    /**
     * 主要用于文件归档路径处理
     */
    @Transient
    private String path;

    /**
     * 文件id集合
     */
    @Transient
    private List<String> fileIds;

    /**
     * 上传文件id
     */
    @Transient
    private String documentId;

}