package com.sinoyd.lims.lim.service;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface DownLoadConsumableTemplateService {
    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    void downLoadConsumable(HttpServletResponse response, Map<String,String> sheetNames, String fileName);

    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    void downLoadStandard(HttpServletResponse response, Map<String,String> sheetNames, String fileName);

    /**
     * 下载混表导入模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    void downLoadConsumableOfMixed(HttpServletResponse response, Map<String,String> sheetNames, String fileName);
}
