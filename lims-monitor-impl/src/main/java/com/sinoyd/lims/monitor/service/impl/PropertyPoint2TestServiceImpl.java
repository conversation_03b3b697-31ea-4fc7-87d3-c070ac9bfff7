package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.entity.AnalyzeMethodReagentConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.monitor.criteria.PropertyPoint2TestCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.lims.monitor.repository.lims.PropertyPoint2TestRepository;
import com.sinoyd.lims.monitor.service.PropertyPoint2TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * PropertyPoint2Test操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class PropertyPoint2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPropertyPoint2Test, String, PropertyPoint2TestRepository> implements PropertyPoint2TestService {

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    private PropertyPoint2TestRepository propertyPoint2TestRepository;

    private TestRepository testRepository;

    private AnalyzeMethodRepository analyzeMethodRepository;

    @Override
    public void findByPage(PageBean<DtoPropertyPoint2Test> pb, BaseCriteria propertyPoint2TestCriteria) {
        pb.setEntityName("DtoPropertyPoint2Test a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, propertyPoint2TestCriteria);
    }

    @Override
    public void findTest(PageBean<DtoTest> pb, BaseCriteria propertyPoint2TestCriteria) {
        PropertyPoint2TestCriteria testCriteria = (PropertyPoint2TestCriteria) propertyPoint2TestCriteria;
        List<DtoPropertyPoint2Test> point2TestList = propertyPoint2TestRepository.findByPropertyPointIdIn(testCriteria.getPropertyPointIds());
        Set<String> testIds = point2TestList.stream().map(DtoPropertyPoint2Test::getTestId).collect(Collectors.toSet());
        List<DtoTest> testList = testRepository.findAllDeleted(testIds);
        if (StringUtil.isNotEmpty(testList)) {
            List<String> sampleTypeIds = testList.parallelStream().map(DtoTest::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> dtoSampleTypeList = sampleTypeService.findRedisByIds(sampleTypeIds);
            List<String> analyzeMethodIds = testList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
            List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(analyzeMethodIds) ? analyzeMethodRepository.findAllDeleted(analyzeMethodIds) : new ArrayList<>();
            Map<String, DtoAnalyzeMethod> analyzeMethodMap = analyzeMethodList.stream().collect(Collectors.toMap(DtoAnalyzeMethod::getId, p -> p));
            for (DtoTest dtoTest : testList) {
                Optional<DtoSampleType> optional = dtoSampleTypeList.parallelStream().filter(p -> dtoTest.getSampleTypeId().equals(p.getId()))
                        .findFirst();
                optional.ifPresent(dto -> dtoTest.setSampleTypeName(dto.getTypeName()));
                Optional<DtoPropertyPoint2Test> point2Test = point2TestList.stream().filter(p -> dtoTest.getId().equals(p.getTestId())).findFirst();
                point2Test.ifPresent(p -> {
                    dtoTest.setTimesOrder(p.getTimesOrder());
                    dtoTest.setSamplePeriod(p.getSamplePeriod());
                });
                DtoAnalyzeMethod analyzeMethod = analyzeMethodMap.get(dtoTest.getAnalyzeMethodId());
                String testStatus = "";
                if ((!analyzeMethod.getIsDeleted() && !dtoTest.getIsDeleted())
                    || (EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(analyzeMethod.getStatus()) && dtoTest.getIsDeleted())){
                    testStatus = EnumLIM.EnumAnalyzeMethodStatus.getByValue(analyzeMethod.getStatus());
                }else {
                    testStatus = "删除";
                }
                dtoTest.setTestStatus(testStatus);
            }
        }
        pb.setData(testList);
    }

    @Autowired
    public void setPropertyPoint2TestRepository(PropertyPoint2TestRepository propertyPoint2TestRepository) {
        this.propertyPoint2TestRepository = propertyPoint2TestRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }
}