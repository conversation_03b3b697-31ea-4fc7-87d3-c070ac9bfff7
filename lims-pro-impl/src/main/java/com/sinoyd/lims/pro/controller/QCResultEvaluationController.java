package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.QCResultEvaluationService;
import com.sinoyd.lims.pro.criteria.QCResultEvaluationCriteria;
import com.sinoyd.lims.pro.dto.DtoQCResultEvaluation;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * QCResultEvaluation服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: QCResultEvaluation服务")
 @RestController
 @RequestMapping("api/pro/qcResultEvaluation")
 public class QCResultEvaluationController extends BaseJpaController<DtoQCResultEvaluation, String,QCResultEvaluationService> {


    /**
     * 分页动态条件查询QCResultEvaluation
     * @param qCResultEvaluationCriteria 条件参数
     * @return RestResponse<List<QCResultEvaluation>>
     */
     @ApiOperation(value = "分页动态条件查询QCResultEvaluation", notes = "分页动态条件查询QCResultEvaluation")
     @GetMapping
     public RestResponse<List<DtoQCResultEvaluation>> findByPage(QCResultEvaluationCriteria qCResultEvaluationCriteria) {
         PageBean<DtoQCResultEvaluation> pageBean = super.getPageBean();
         RestResponse<List<DtoQCResultEvaluation>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, qCResultEvaluationCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询QCResultEvaluation
     * @param id 主键id
     * @return RestResponse<DtoQCResultEvaluation>
     */
     @ApiOperation(value = "按主键查询QCResultEvaluation", notes = "按主键查询QCResultEvaluation")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoQCResultEvaluation> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoQCResultEvaluation> restResponse = new RestResponse<>();
         DtoQCResultEvaluation qCResultEvaluation = service.findOne(id);
         restResponse.setData(qCResultEvaluation);
         restResponse.setRestStatus(StringUtil.isNull(qCResultEvaluation) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增QCResultEvaluation
     * @param qCResultEvaluation 实体列表
     * @return RestResponse<DtoQCResultEvaluation>
     */
     @ApiOperation(value = "新增QCResultEvaluation", notes = "新增QCResultEvaluation")
     @PostMapping
     public RestResponse<DtoQCResultEvaluation> create(@RequestBody  @Validated DtoQCResultEvaluation qCResultEvaluation) {
         RestResponse<DtoQCResultEvaluation> restResponse = new RestResponse<>();
         restResponse.setData(service.save(qCResultEvaluation));
         return restResponse;
      }

     /**
     * 新增QCResultEvaluation
     * @param qCResultEvaluation 实体列表
     * @return RestResponse<DtoQCResultEvaluation>
     */
     @ApiOperation(value = "修改QCResultEvaluation", notes = "修改QCResultEvaluation")
     @PutMapping
     public RestResponse<DtoQCResultEvaluation> update(@RequestBody  @Validated DtoQCResultEvaluation qCResultEvaluation) {
         RestResponse<DtoQCResultEvaluation> restResponse = new RestResponse<>();
         restResponse.setData(service.update(qCResultEvaluation));
         return restResponse;
      }

    /**
     * "根据id批量删除QCResultEvaluation
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除QCResultEvaluation", notes = "根据id批量删除QCResultEvaluation")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }