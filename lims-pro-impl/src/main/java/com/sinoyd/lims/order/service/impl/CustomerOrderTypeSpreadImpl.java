package com.sinoyd.lims.order.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import com.sinoyd.lims.pro.vo.OrderTypeSpreadVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Service(CustomerOrderAnaService.CUSTOMER_ORDER_TYPE_SPREAD)
public class CustomerOrderTypeSpreadImpl extends AbsCustomerOrder {

    /**
     * 合同类型分布
     *
     * @param year 年份
     * @return List<OrderTypeSpreadVo>
     */
    @Override
    public List<BaseOrderCustomerAnalyzeVO> statisticsList(Integer year) {
        String currentYearBegin = CalendarUtil.getCurrentYearBegin(year);
        String currentYearEnd = CalendarUtil.getCurrentYearEnd(year);

        //合同类型sql
        StringBuilder orderTypeNameSb = new StringBuilder();
        orderTypeNameSb.append("SELECT id , name FROM TB_LIM_ProjectType WHERE isDeleted = 0 and id in ( SELECT projectTypeId FROM TB_PRO_OrderForm WHERE isDeleted = 0 and signDate BETWEEN ");
        orderTypeNameSb.append(" ? ");
        orderTypeNameSb.append(" AND ");
        orderTypeNameSb.append(" ? ");
        orderTypeNameSb.append(" AND grantStatus = ? GROUP BY projectTypeId)");
        Object[] orderTypeParams = new Object[] {currentYearBegin,currentYearEnd,EnumPRO.EnumOrderSignStatus.已签订.getValue()};
        List<Map<String, Object>> orderTypeNameList = super.getCustomerOrderData(orderTypeNameSb.toString(),orderTypeParams);
        //空表示找不到相关类型
        if (orderTypeNameList == null) {
            return null;
        }

        //合同金额sql
        StringBuilder orderFinalQuotationSb = new StringBuilder();
        orderFinalQuotationSb.append("SELECT t1.finalQuotation,t2.projectTypeId FROM TB_PRO_OrderQuotation t1 ,(SELECT id,projectTypeId  FROM TB_PRO_OrderForm WHERE isDeleted = 0 and signDate BETWEEN ");
        orderFinalQuotationSb.append(" ? ");
        orderFinalQuotationSb.append(" AND ");
        orderFinalQuotationSb.append(" ? ");
        orderFinalQuotationSb.append(" AND grantStatus = ? ) t2 WHERE t1.isDeleted = 0 and  t2.id = t1.orderId");
        Object[] orderFinalQuotationParams = new Object[] {currentYearBegin,currentYearEnd,EnumPRO.EnumOrderSignStatus.已签订.getValue()};
        List<Map<String, Object>> orderFinalQuotationList = super.getCustomerOrderData(orderFinalQuotationSb.toString(),orderFinalQuotationParams);
        //空表示没有订单数据
        if (orderFinalQuotationList == null) {
            return null;
        }
        //对数据进行组装
        List<BaseOrderCustomerAnalyzeVO> orderTypeSpreadVoList = new ArrayList<>();
        for (Map<String, Object> orderTypeMap : orderTypeNameList) {
            String id = orderTypeMap.get("id").toString();
            String name = orderTypeMap.get("name").toString();
            int finalQuotationTotal = 0;
            OrderTypeSpreadVo orderTypeSpreadVo = new OrderTypeSpreadVo();
            orderTypeSpreadVo.setName(name);
            for (Map<String, Object> orderQuotationMap : orderFinalQuotationList) {
                String projectTypeId = orderQuotationMap.get("projectTypeId").toString();
                if (id.equals(projectTypeId)) {
                    String finalQuotationString = orderQuotationMap.get("finalQuotation").toString();
                    BigDecimal bigDecimal = new BigDecimal(finalQuotationString);
                    int finalQuotation = bigDecimal.intValue();
                    finalQuotationTotal += finalQuotation;
                }
            }
            orderTypeSpreadVo.setFinalQuotation(finalQuotationTotal);
            orderTypeSpreadVoList.add(orderTypeSpreadVo);
        }
        return orderTypeSpreadVoList;
    }


}
