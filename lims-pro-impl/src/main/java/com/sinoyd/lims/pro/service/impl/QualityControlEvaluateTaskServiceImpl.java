package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.QualityTaskFactory;
import com.sinoyd.base.factory.quality.*;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.service.QualityControlLimitService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoAnalyseOriginalRecord;
import com.sinoyd.lims.pro.dto.DtoQualityControl;
import com.sinoyd.lims.pro.dto.DtoQualityControlEvaluate;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseQualityControlData;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.AnalyseOriginalRecordRepository;
import com.sinoyd.lims.pro.service.AnalyseDataService;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.service.QualityControlEvaluateTaskService;
import com.sinoyd.lims.pro.util.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 质控评价相关业务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/6
 */
@Service
@Slf4j
public class QualityControlEvaluateTaskServiceImpl implements QualityControlEvaluateTaskService {

    private static final Pattern PATTERN = Pattern.compile("\\[\\w+\\]");

    private AnalyseDataService analyseDataService;

    private ProService proService;

    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;

    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    private TestService testService;

    private QualityControlLimitService qualityControlLimitService;

    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<String> dealEvaluateData(List<DtoQualityControlEvaluate> evaluateList,
                                           List<DtoAnalyseQualityControlData> analyseQualityControlDataList,
                                           List<DtoQualityControlLimit> qualityControlLimitList,
                                           String workSheetFolderId, Map<String, String> anaId2QcInfoMap,
                                           List<DtoTest> testList, Map<String, DtoQualityControl> qcMap,
                                           Map<String, DtoAnalyseData> anaDataMap, UsernamePasswordAuthenticationToken token) {
        //为每个子线程设置token
        SecurityContextHolder.getContext().setAuthentication(token);
        CalculateService calculationService = SpringContextAware.getBean(CalculateService.class);
        try {
            for (DtoQualityControlEvaluate evaluate : evaluateList) {
                DtoAnalyseQualityControlData loopData = analyseQualityControlDataList.stream()
                        .filter(p -> p.getAnalyseId().equals(evaluate.getObjectId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(loopData)) {
                    List<DtoQualityControlLimit> controlLimitList = qualityControlLimitList.stream().filter(p -> p.getTestId().equals(loopData.getTestId())
                            && p.getQcType().equals(loopData.getQcType()) && p.getQcGrade().equals(loopData.getQcGrade())).collect(Collectors.toList());
                    String limitValue = loopData.getExamLimitValue();
                    String lowerLimit = loopData.getLowerLimit();
                    qualityControlLimitService.fillControlMsg(controlLimitList, limitValue, lowerLimit);
                    if (evaluate.getQcId().equals(UUIDHelper.GUID_EMPTY)) {
                        //表示为串联样
                        controlLimitList = qualityControlLimitList.stream().filter(p -> p.getTestId().equals(loopData.getTestId())
                                && p.getQcType().equals(new QualitySampleSeries().qcTypeValue())).collect(Collectors.toList());
                        List<String> parentIds = testList.stream().filter(p -> p.getId().equals(loopData.getTestId())
                                && !UUIDHelper.GUID_EMPTY.equals(p.getParentId())).map(DtoTest::getParentId).collect(Collectors.toList());
                        if (controlLimitList.size() == 0 && parentIds.size() > 0) {
                            controlLimitList = qualityControlLimitList.stream().filter(p -> parentIds.contains(p.getTestId())
                                    && p.getQcType().equals(new QualitySampleSeries().qcTypeValue())).collect(Collectors.toList());
                        }
                        updateEvaluateForSampleSeries(controlLimitList, loopData, evaluate, analyseQualityControlDataList, workSheetFolderId, anaId2QcInfoMap, calculationService);
                    } else {
                        //根据不同质控类型进行不同处理
                        if (new QualityBlank().qcTypeValue().equals(loopData.getQcType())
                                || new QualityReagentBlank().qcTypeValue().equals(loopData.getQcType())
                                || new QualityTransportBlank().qcTypeValue().equals(loopData.getQcType())
                                || new QualityInstrumentBlank().qcTypeValue().equals(loopData.getQcType())
                                || new QualityLocalBlank().qcTypeValue().equals(loopData.getQcType())
                                || new QualitySamplingContainerBlank().qcTypeValue().equals(loopData.getQcType())) {
//                            Optional<DtoTest> testOptional = testList.stream().filter(p -> loopData.getTestId().equals(p.getId())).findFirst();
//                            updateEvaluateForBlank(controlLimitList, loopData, evaluate, anaDataMap, testOptional);
                        } else if (new QualityParallel().qcTypeValue().equals(loopData.getQcType()) || new QualityCipherParallel().qcTypeValue().equals(loopData.getQcType())) {
                            updateEvaluateForParallel(controlLimitList, loopData, evaluate, analyseQualityControlDataList, workSheetFolderId, anaId2QcInfoMap, calculationService);
                        } else if (new QualityMark().qcTypeValue().equals(loopData.getQcType()) || new QualityBlankMark().qcTypeValue().equals(loopData.getQcType())) {
                            updateEvaluateForJb(controlLimitList, loopData, evaluate, calculationService, qcMap, analyseQualityControlDataList);
                        } else if (new QualityCorrectionFactor().qcTypeValue().equals(loopData.getQcType())) {
                            updateEvaluateForJz(controlLimitList, loopData, evaluate, workSheetFolderId, anaId2QcInfoMap, calculationService);
                        } else if (new CurveCheck().qcTypeValue().equals(loopData.getQcType())) {
//                            updateEvaluateForCurve(controlLimitList, loopData, evaluate, workSheetFolderId, anaId2QcInfoMap, calculationService);
                        } else if (new QualityStandard().qcTypeValue().equals(loopData.getQcType())) {

                        } else if (new QualitySampleSeries().qcTypeValue().equals(loopData.getQcType())) {
                            updateEvaluateForSampleSeries(controlLimitList, loopData, evaluate, analyseQualityControlDataList, workSheetFolderId, anaId2QcInfoMap, calculationService);
                        } else if (new QualityReplace().qcTypeValue().equals(loopData.getQcType())) {
                            //替代物的质控限值配置在父测试项目上，需进行个性化过滤
                            String anaItemName = anaDataMap.containsKey(evaluate.getObjectId()) ? anaDataMap.get(evaluate.getObjectId()).getRedAnalyzeItemName() : "";
                            updateEvaluateForReplace(qualityControlLimitList, loopData, evaluate, anaItemName, testList, calculationService);
                        } else if (new QualityPositiveControl().qcTypeValue().equals(loopData.getQcType()) || new QualityNegativeControl().qcTypeValue().equals(loopData.getQcType())) {
                            updateEvaluateForPosNeg(controlLimitList, loopData, evaluate);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException(e);
        }

        return new AsyncResult<>("");
    }

    /**
     * 更新串联样质控评价信息
     *
     * @param controlLimitList              质控限值配置列表
     * @param loopData                      分析质控信息对象
     * @param evaluate                      质控评价信息对象
     * @param analyseQualityControlDataList 分析数据及质控数据对象列表
     * @param workSheetFolderId             检测单id
     * @param anaId2QcInfoMap               分析数据id和qcInfo的映射关系
     * @param calculationService            计算服务对象
     */
    private void updateEvaluateForSampleSeries(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate,
                                               List<DtoAnalyseQualityControlData> analyseQualityControlDataList, String workSheetFolderId, Map<String, String> anaId2QcInfoMap,
                                               CalculationService calculationService) {
        String qcInfo = reCalculateQcInfoForSeries(controlLimitList, loopData, analyseQualityControlDataList, workSheetFolderId);
        if (!qcInfo.equals(loopData.getQcInfo())) {
            anaId2QcInfoMap.put(loopData.getAnalyseId(), qcInfo);
            loopData.setQcInfo(qcInfo);
        }
        String limitValue = loopData.getExamLimitValue();
        String lowerLimit = loopData.getLowerLimit();
        qualityControlLimitService.fillControlMsg(controlLimitList, limitValue, lowerLimit);
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/");
        //找到原样
        DtoAnalyseQualityControlData yyData = analyseQualityControlDataList.stream().filter(p -> p.getSampleId().equals(loopData.getAssociateSampleId())
                && p.getTestId().equals(loopData.getTestId())).findFirst().orElse(null);
        DtoQualityControlLimit limit = StringUtil.isNotNull(yyData) ? getControlLimit(controlLimitList, yyData.getTestValueDst(), calculationService) : null;
        if (limit == null || (limit != null && !evaluate.getLimitId().equals(limit.getId()))) {
            evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.系统验证.getValue());
            evaluate.setRemark("");
        }
        setLimitInfoForEvaluate(evaluate, limit, loopData.getQcInfo().replace("%", ""), new QualitySampleSeries());
    }

    /**
     * 更新空白样质控评价信息
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param evaluate         质控评价信息对象
     */
    private void updateEvaluateForBlank(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate,
                                        Map<String, DtoAnalyseData> anaDataMap, Optional<DtoTest> testOptional) {
        //空白样，需要判断检查项是出证结果还是公式参数(其他) 1：出证结果 2：公式参数
        String checkItemOther = getCheckItemOther(controlLimitList);
        String samVal = "/";
        if (StringUtil.isNotEmpty(controlLimitList)) {
            samVal = halfLimit(loopData.getTestValueDst(), loopData.getExamLimitValue(), "sample");
            if (testOptional.isPresent()) {
                if (EnumBase.EnumReviseType.先比较再修约.getValue().equals(testOptional.get().getReviseType())) {
                    samVal = halfLimit(loopData.getTestOriginValue(), loopData.getExamLimitValue(), "sample");
                    samVal = proService.getDecimal(loopData.getMostSignificance(), loopData.getMostDecimal(), samVal);
                }
            }
            samVal = StringUtil.isNotEmpty(checkItemOther) ? loopData.getParamMap().getOrDefault(checkItemOther, "/") : samVal;
        }
        Map<String, Object> qcMap = new QualityBlank().calculateDeviationValue(controlLimitList, Arrays.asList(samVal, loopData.getExamLimitValue()));
        DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
        //针对空白样，判断是否有配置小于检出限或者小于测定下限的限值配置，如有则默认获取其中一个（配置了小于检出限或者小于测定下限时，不需要根据samVal来判断使用的是哪个限值配置）
        if (StringUtil.isNull(limit)) {
            limit = checkDetectionTestLimit(controlLimitList);
        }
        String passStr = qcMap.getOrDefault("qcRate", "").toString();
        evaluate.setCheckItem(StringUtil.isNotEmpty(checkItemOther) ? checkItemOther : (StringUtil.isNotNull(limit) ? EnumLIM.EnumCheckItemType.出证结果.toString() : "/"));

        //检查项为其他时，量纲获取公式参数上的量纲
        String dim = anaDataMap.containsKey(loopData.getAnalyseId()) ? anaDataMap.get(loopData.getAnalyseId()).getDimension() : "";
        if (StringUtil.isNotEmpty(checkItemOther)) {
            List<DtoAnalyseOriginalRecord> originalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(Collections.singletonList(loopData.getAnalyseId()));
            if (StringUtil.isNotEmpty(originalRecordList)) {
                DtoParamsTestFormula paramsTestFormula = paramsTestFormulaRepository.findByObjId(originalRecordList.get(0).getTestFormulaId())
                        .stream().filter(p -> evaluate.getCheckItem().equals(p.getAlias())).findFirst().orElse(null);
                dim = StringUtil.isNotNull(paramsTestFormula) ? paramsTestFormula.getDimension() : "";
            }
        }
        evaluate.setDimensionName(dim);
        evaluate.setJudgingMethod(StringUtil.isNotNull(limit) ? limit.getJudgingMethod() : -1);
        evaluate.setAllowLimit(getYxPcForBlank(limit, loopData.getExamLimitValue(), loopData.getLowerLimit()));
        Boolean pass = StringUtil.isNotEmpty(passStr) ? ("是".equals(passStr) || "合格".equals(passStr)) : null;
        //配置的允许限值不符合规范时，直接不评价
        if (StringUtil.isNotNull(limit) && EnumBase.EnumJudgingMethod.限值判定.getValue().equals(limit.getJudgingMethod())
                && !PATTERN.matcher(limit.getAllowLimit()).find()) {
            evaluate.setIsPass(null);
        } else {
            evaluate.setIsPass(checkPassForBlank(limit, loopData.getExamLimitValue(), loopData.getLowerLimit(), samVal, pass));
            //检查项值不是数字类型时（小于检出限）,默认合格
            if (StringUtil.isNotNull(limit) && StringUtil.isNotEmpty(samVal) && !MathUtil.isNumeral(samVal)) {
                evaluate.setIsPass(true);
            }
        }
//        evaluate.setIsPass(checkPassForBlank(limit, loopData.getExamLimitValue(), loopData.getLowerLimit(), samVal, pass));
        if (StringUtil.isNotNull(limit) && EnumLIM.EnumCheckItemType.出证结果.getValue().equals(limit.getCheckItem())) {
            samVal = loopData.getTestValue();
        }
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(samVal) ? samVal : "/");
        // 质控限值id
        evaluate.setLimitId(StringUtil.isNotNull(limit) ? limit.getIsDefault() ? UUIDHelper.GUID_EMPTY : limit.getId() : UUIDHelper.GUID_EMPTY);
    }

    /**
     * 更新平行样质控评价信息
     *
     * @param controlLimitList              质控限值配置列表
     * @param loopData                      分析质控信息对象
     * @param evaluate                      质控评价信息对象
     * @param analyseQualityControlDataList 分析数据对象
     * @param workSheetFolderId             检测单id
     * @param anaId2QcInfoMap               分析数据id和qcInfo的映射关系
     * @param calculationService            计算服务对象
     */
    protected void updateEvaluateForParallel(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                             DtoQualityControlEvaluate evaluate, List<DtoAnalyseQualityControlData> analyseQualityControlDataList,
                                             String workSheetFolderId, Map<String, String> anaId2QcInfoMap, CalculationService calculationService) {
        DtoAnalyseQualityControlData yyAnaData = analyseQualityControlDataList.stream().filter(p -> p.getSampleId().equals(loopData.getAssociateSampleId())
                && p.getTestId().equals(loopData.getTestId())).findFirst().orElse(null);
        List<DtoAnalyseQualityControlData> yyJyyAnaDataList = analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(loopData.getAssociateSampleId())
                && p.getTestId().equals(loopData.getTestId()) && p.getQcType().equals(new QualitySampleCopy().qcTypeValue())).collect(Collectors.toList());
        yyJyyAnaDataList.add(yyAnaData);
        String samVal = "";
        OptionalDouble ave = yyJyyAnaDataList.stream().filter(a -> StringUtil.isNotNull(a) && MathUtil.isNumeral(a.getTestOriginValue())).mapToDouble(d -> Double.valueOf(d.getTestOriginValue())).average();
        if (ave.isPresent()) {
            samVal = String.valueOf(ave.getAsDouble());
        }
        samVal = StringUtil.isNotNull(yyAnaData) ? halfLimit(samVal, loopData.getExamLimitValue(), "qualityControl") : "";
        String sampleTypeId = evaluate.getSampleTypeId();
        DtoQualityControlLimit limit = getControlLimit(controlLimitList, samVal, calculationService);
        if (limit == null || (limit != null && !evaluate.getLimitId().equals(limit.getId()))) {
            evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.系统验证.getValue());
            evaluate.setRemark("");
        }
        String qcInfo = reCalculateQcInfoForParallel(controlLimitList, loopData, analyseQualityControlDataList, workSheetFolderId, yyAnaData, sampleTypeId);
        if (!qcInfo.equals(loopData.getQcInfo()) && !qcInfo.contains("不予评价")) {
            anaId2QcInfoMap.put(loopData.getAnalyseId(), qcInfo);
        }
        loopData.setQcInfo(qcInfo);
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/");
        setLimitInfoForEvaluate(evaluate, limit, loopData.getQcInfo().replace("%", ""), new QualityParallel());
    }

    /**
     * 更新加标样质控评价信息
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param evaluate         质控评价信息对象
     */
    private void updateEvaluateForJb(List<DtoQualityControlLimit> controlLimitList,
                                     DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate,
                                     CalculationService calculationService, Map<String, DtoQualityControl> qcMap,
                                     List<DtoAnalyseQualityControlData> analyseQualityControlDatas) {
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo().replace("%", "") : "/");
        DtoQualityControl qc = qcMap.get(evaluate.getQcId());
        // 原样数据
        DtoAnalyseQualityControlData yyData = analyseQualityControlDatas.stream().filter(p -> p.getSampleId().equals(loopData.getAssociateSampleId())).findFirst().orElse(null);
        DtoQualityControlLimit limit = StringUtil.isNotNull(qc) ? getControlLimitForJb(controlLimitList, qc, yyData, calculationService) : null;
        if (limit == null || (limit != null && !evaluate.getLimitId().equals(limit.getId()))) {
            evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.系统验证.getValue());
            evaluate.setRemark("");
        }
        QualityControlKind kind = QualityTaskFactory.getInstance().getQcSample(new QualityMark().qcTypeValue());
        if (new QualityBlankMark().qcTypeValue().equals(loopData.getQcType())) {
            kind = QualityTaskFactory.getInstance().getQcSample(new QualityBlankMark().qcTypeValue());
        }
        String qcInfo = StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "";
        setLimitInfoForEvaluate(evaluate, limit, qcInfo.replace("%", ""), kind);
        // 更新加标样检查项
        setJbCheckItem(limit, evaluate);
    }

    /**
     * 更新曲线校核样/校正系数检验样质控评价信息
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param evaluate         质控评价信息对象
     */
    private void updateEvaluateForJz(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate,
                                     String workSheetFolderId, Map<String, String> anaId2QcInfoMap, CalculationService calculationService) {
        String qcInfo = reCalculateQcInfoForJz(controlLimitList, loopData, workSheetFolderId);
        if (!qcInfo.equals(loopData.getQcInfo())) {
            anaId2QcInfoMap.put(loopData.getAnalyseId(), qcInfo);
            loopData.setQcInfo(qcInfo);
        }
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/");
        DtoQualityControlLimit limit = getControlLimit(controlLimitList, loopData.getQcInfo().replace("%", ""), calculationService);
        if (limit == null || (limit != null && !evaluate.getLimitId().equals(limit.getId()))) {
            evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.系统验证.getValue());
            evaluate.setRemark("");
        }
        setLimitInfoForEvaluate(evaluate, limit, loopData.getQcInfo().replace("%", ""), new QualityCorrectionFactor());
        evaluate.setCheckItem(StringUtil.isNotEmpty(limit.getCheckItemOther()) ? limit.getCheckItemOther() : "/");
    }

    /**
     * 更新曲线校核样/校正系数检验样质控评价信息
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param evaluate         质控评价信息对象
     */
    private void updateEvaluateForCurve(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate,
                                        String workSheetFolderId, Map<String, String> anaId2QcInfoMap, CalculationService calculationService) {
        if ("出证结果".equals(evaluate.getCheckItem())) {
            List<DtoQualityControlLimit> limitForTestVal = controlLimitList.stream().filter(p -> EnumLIM.EnumCheckItemType.出证结果.getValue().equals(p.getCheckItem())).collect(Collectors.toList());
            String qcInfo = reCalculateQcInfoForCurve(limitForTestVal, loopData, workSheetFolderId);
            if (!qcInfo.equals(loopData.getQcInfo())) {
//                anaId2QcInfoMap.put(loopData.getAnalyseId(), qcInfo);
                loopData.setQcInfo(qcInfo);
            }
            evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/");
            DtoQualityControlLimit limit = getControlLimit(limitForTestVal, loopData.getQcValue(), calculationService);
            setLimitInfoForCurveEvaluate(evaluate, limit, loopData, new CurveCheck(), anaId2QcInfoMap);
        } else {
            String checkItem = StringUtil.isNotEmpty(evaluate.getCheckItem()) ? evaluate.getCheckItem() : "";
            //检查项为公式参数的，需要按照对应参数名称过滤出相应的质控限值
            List<DtoQualityControlLimit> limitForItem = controlLimitList.stream().filter(p -> evaluate.getCheckItem().equals(p.getCheckItemOther())).collect(Collectors.toList());
            String qcInfo = reCalculateQcInfoForItemCurve(limitForItem, loopData, checkItem, workSheetFolderId);
            if (!qcInfo.equals(loopData.getQcInfo())) {
//                anaId2QcInfoMap.put(loopData.getAnalyseId(), qcInfo);
                loopData.setQcInfo(qcInfo);
            }
            evaluate.setCheckItemValue(StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/");
            DtoQualityControlLimit limit = getControlLimitForItemCurve(limitForItem, loopData.getQcValue(), calculationService);
            setLimitInfoForItemCurveEvaluate(evaluate, limit, loopData, new CurveCheck(), anaId2QcInfoMap);
        }
    }

    /**
     * 更新替代样质控评价信息
     *
     * @param controlLimitList   质控限值配置列表
     * @param loopData           分析质控信息对象
     * @param evaluate           质控评价信息对象
     * @param anaItemName        分析项目名称
     * @param calculationService 计算服务对象
     */
    private void updateEvaluateForReplace(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                          DtoQualityControlEvaluate evaluate, String anaItemName, List<DtoTest> testList, CalculationService calculationService) {
        DtoTest test = testList.stream().filter(p -> loopData.getTestId().equals(p.getId())).findFirst().orElse(null);
        String parentTestId = StringUtil.isNotNull(test) ? test.getParentId() : UUIDHelper.GUID_EMPTY;
        controlLimitList = controlLimitList.stream()
                .filter(p -> parentTestId.equals(p.getTestId()) && p.getQcType().equals(loopData.getQcType()) && StringUtil.isNotEmpty(p.getSubstituteName())
                        && p.getSubstituteName().equals(anaItemName)).collect(Collectors.toList());
        String limitValue = loopData.getExamLimitValue();
        String lowerLimit = loopData.getLowerLimit();
        qualityControlLimitService.fillControlMsg(controlLimitList, limitValue, lowerLimit);
        String qcInfo = StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "/";
        evaluate.setCheckItemValue(qcInfo);
        DtoQualityControlLimit limit = getControlLimit(controlLimitList, loopData.getTestValueDst(), calculationService);
        if (limit == null || (limit != null && !evaluate.getLimitId().equals(limit.getId()))) {
            evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.系统验证.getValue());
            evaluate.setRemark("");
        }
        setLimitInfoForEvaluate(evaluate, limit, qcInfo.replace("%", ""), new QualityReplace());
    }

    /**
     * 更新阴性/阳性对照试验质控评价信息
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param evaluate         质控评价信息对象
     */
    private void updateEvaluateForPosNeg(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, DtoQualityControlEvaluate evaluate) {
        String samVal = StringUtil.isNotEmpty(controlLimitList) ? loopData.getTestValueDst() : "/";
        evaluate.setCheckItemValue(StringUtil.isNotEmpty(samVal) ? samVal : "/");
        if (EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
            controlLimitList.stream().filter(v -> evaluate.getLimitId().equals(v.getId())).forEach(v -> {
                v.setAllowLimit(evaluate.getAllowLimit());
                v.setAllowLimitData(evaluate.getAllowLimit());
            });
        }
        Map<String, Object> qcMap = new QualityPositiveControl().calculateDeviationValue(controlLimitList, Arrays.asList(samVal, loopData.getExamLimitValue()));
        DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
        if (limit == null || (limit != null && !evaluate.getLimitId().equals(limit.getId()))) {
            evaluate.setJudgeType(EnumPRO.EnumQualityControlEvaluateType.系统验证.getValue());
            evaluate.setRemark("");
        }
        setLimitInfoForEvaluate(evaluate, limit, loopData.getQcInfo().replace("%", ""), new QualityParallel());
        String passStr = qcMap.getOrDefault("qcRate", "").toString();
        Boolean pass = StringUtil.isNotEmpty(passStr) ? ("是".equals(passStr) || "合格".equals(passStr)) : null;
        //配置的允许限值不符合规范时，直接不评价
        if (StringUtil.isNotNull(limit) && EnumBase.EnumJudgingMethod.限值判定.getValue().equals(limit.getJudgingMethod())
                && ((EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType()) && !PATTERN.matcher(evaluate.getAllowLimit()).find())
                || (EnumPRO.EnumQualityControlEvaluateType.系统验证.getValue().equals(evaluate.getJudgeType()) && !PATTERN.matcher(limit.getAllowLimit()).find()))) {
            evaluate.setIsPass(null);
        } else {
            evaluate.setIsPass(pass);
            //检查项值不是数字类型时（小于检出限）,默认合格
            if (StringUtil.isNotNull(limit) && StringUtil.isNotEmpty(samVal) && !MathUtil.isNumeral(samVal)) {
                evaluate.setIsPass(true);
            }
        }
    }

    /**
     * 重新计算串联样偏差
     *
     * @param controlLimitList              质控限值配置列表
     * @param loopData                      分析质控信息对象
     * @param analyseQualityControlDataList 分析数据及质控数据对象列表
     * @param workSheetFolderId             检测单id
     * @return 偏差
     */
    private String reCalculateQcInfoForSeries(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                              List<DtoAnalyseQualityControlData> analyseQualityControlDataList, String workSheetFolderId) {
        String qcInfo = loopData.getQcInfo();
        if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId)) {
            //找到串联样的原样
            DtoAnalyseQualityControlData yyAnaData = analyseQualityControlDataList.stream().filter(p -> p.getSampleId().equals(loopData.getAssociateSampleId())
                    && p.getTestId().equals(loopData.getTestId())).findFirst().orElse(null);
            List<DtoQualityControlLimit> limitList = controlLimitList.stream()
                    .filter(p -> p.getQcType().equals(new QualitySampleSeries().qcTypeValue())).collect(Collectors.toList());
            boolean itemOtherFlag = StringUtil.isNotEmpty(limitList) && EnumLIM.EnumCheckItemType.公式参数.getValue().equals(limitList.get(0).getCheckItem())
                    && StringUtil.isNotEmpty(limitList.get(0).getCheckItemOther());
            if (StringUtil.isNotNull(yyAnaData)) {
                //找到原样对应的所有串联样
                List<String> valueList = new ArrayList<>();
                String tsdYY = halfLimit(yyAnaData.getTestValueDst(), yyAnaData.getExamLimitValue(), "qualityControl");
                if (itemOtherFlag) {
                    tsdYY = yyAnaData.getParamMap().getOrDefault(limitList.get(0).getCheckItemOther(), "");
                }
                valueList.add(tsdYY);
                //表示串联样
                List<DtoAnalyseQualityControlData> clList = analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(yyAnaData.getSampleId())
                        && p.getTestId().equals(yyAnaData.getTestId()) && UUIDHelper.GUID_EMPTY.equals(p.getQcId())).collect(Collectors.toList());
                for (DtoAnalyseQualityControlData cl : clList) {
                    String tsd = halfLimit(cl.getTestValueDst(), cl.getExamLimitValue(), "qualityControl");
                    if (itemOtherFlag) {
                        tsd = cl.getParamMap().getOrDefault(limitList.get(0).getCheckItemOther(), "");
                    }
                    //排除当前样品
                    if (!yyAnaData.getSampleId().equals(cl.getSampleId())) {
                        valueList.add(tsd);
                    }
                }
                //判断是否为平行串联
                if (loopData.getQcType().equals(new QualityParallel().qcTypeValue())) {
                    //清除数据
                    valueList.clear();
                    //找到原样对应的平行样
                    List<DtoAnalyseQualityControlData> pxList = analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(yyAnaData.getAssociateSampleId())
                            && new QualityParallel().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList());
                    for (DtoAnalyseQualityControlData px : pxList) {
                        String tsd = halfLimit(px.getTestValueDst(), px.getExamLimitValue(), "qualityControl");
                        if (itemOtherFlag) {
                            tsd = px.getParamMap().getOrDefault(limitList.get(0).getCheckItemOther(), "");
                        }
                        //排除当前样品
                        if (!loopData.getSampleId().equals(px.getSampleId())) {
                            valueList.add(tsd);
                        }
                    }
                    String loopTsd = halfLimit(loopData.getTestValueDst(), loopData.getExamLimitValue(), "qualityControl");
                    if (itemOtherFlag) {
                        loopTsd = loopData.getParamMap().getOrDefault(limitList.get(0).getCheckItemOther(), "");
                    }
                    valueList.add(loopTsd);
                }

                String recoverRateStr = "";

                if (controlLimitList.size() > 0) {
                    Map<String, Object> qcMap = new QualitySampleSeries().calculateDeviationValue(limitList, valueList);
                    String recoverRate = qcMap.get("qcRate").toString();
                    DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                    if (StringUtil.isNotNull(limit)) {
                        Integer sign = new QualitySampleSeries().getQualityConfig().getMostSignificance();
                        Integer md = new QualitySampleSeries().getQualityConfig().getMostDecimal();
                        recoverRateStr = proService.getDecimal(sign, md, recoverRate);
                        if (StringUtil.isNotNull(recoverRateStr)) {
                            recoverRateStr = recoverRateStr.contains("无法计算") ? recoverRateStr : recoverRateStr + "%";
                        }
                    }
                } else {
                    //没有配置串联样质控限值时，穿透率'/'掉
                    recoverRateStr = "/";
                }
                qcInfo = recoverRateStr;
            }
        }
        return StringUtil.isNotNull(qcInfo) ? qcInfo : "";
    }

    /**
     * 更新质控评价对象的质控限值信息
     *
     * @param evaluate 质控评价对象
     * @param limit    质控限值对象
     * @param qcInfo   质控值
     */
    private void setLimitInfoForEvaluate(DtoQualityControlEvaluate evaluate, DtoQualityControlLimit limit, String qcInfo, QualityControlKind kind) {
        String checkItem = StringUtil.isNotNull(limit) ? EnumLIM.EnumCheckItemType.getName(limit.getCheckItem()) : "/";
        evaluate.setCheckItem(StringUtil.isNotEmpty(checkItem) ? checkItem : "/");
        evaluate.setJudgingMethod(StringUtil.isNotNull(limit) ? limit.getJudgingMethod() : -1);
        if (EnumPRO.EnumQualityControlEvaluateType.人工评价.getValue().equals(evaluate.getJudgeType())) {
            evaluate.setAllowLimit(StringUtil.isNotEmpty(evaluate.getAllowLimit()) ? evaluate.getAllowLimit() : "/");
            DtoQualityControlLimit tempLimit = new DtoQualityControlLimit();
            tempLimit.setAllowLimit(evaluate.getAllowLimit());
            tempLimit.setAllowLimitData(evaluate.getAllowLimit());
            evaluate.setIsPass((StringUtil.isNotNull(limit) && PATTERN.matcher(tempLimit.getAllowLimit()).find()) ? kind.deviationQualified(tempLimit, qcInfo) : null);
        } else {
            String allowLimit = StringUtil.isNotNull(limit) ? limit.getAllowLimitData() : "/";
            evaluate.setAllowLimit(StringUtil.isNotEmpty(allowLimit) ? allowLimit : "/");
            //配置的允许限值不符合规范时，直接不评价
            evaluate.setIsPass((StringUtil.isNotNull(limit) && PATTERN.matcher(limit.getAllowLimitData()).find()) ? kind.deviationQualified(limit, qcInfo) : null);
        }
        //如果评价的值为无法计算时，则评价为不合格
        if (qcInfo.contains("无法计算")) {
            evaluate.setIsPass(false);
        }
        // 质控限值id
        evaluate.setLimitId(StringUtil.isNotNull(limit) ? limit.getIsDefault() ? UUIDHelper.GUID_EMPTY : limit.getId() : UUIDHelper.GUID_EMPTY);
    }

    /**
     * 更新质控评价对象的质控限值信息
     *
     * @param evaluate 质控评价对象
     * @param limit    质控限值对象
     */
    private void setLimitInfoForCurveEvaluate(DtoQualityControlEvaluate evaluate, DtoQualityControlLimit limit, DtoAnalyseQualityControlData loopData,
                                              QualityControlKind kind, Map<String, String> anaId2QcInfoMap) {
        String qcInfo = loopData.getQcInfo().replace("%", "");
        evaluate.setJudgingMethod(StringUtil.isNotNull(limit) ? limit.getJudgingMethod() : -1);
        String allowLimit = (StringUtil.isNotNull(limit) && StringUtil.isNotEmpty(limit.getAllowLimit())) ? limit.getAllowLimit() : "/";
        evaluate.setAllowLimit(allowLimit);
        //配置的允许限值不符合规范时，直接不评价
        evaluate.setIsPass((StringUtil.isNotNull(limit) && PATTERN.matcher(limit.getAllowLimit()).find()) ? kind.deviationQualified(limit, qcInfo) : null);
        //如果评价的值为无法计算时，则评价为不合格
        if (qcInfo.contains("无法计算")) {
            evaluate.setIsPass(false);
        }
        String passStr = (StringUtil.isNotNull(evaluate.getIsPass()) && evaluate.getIsPass()) ? "合格" : "不合格";
        String existPassStr = anaId2QcInfoMap.getOrDefault(loopData.getAnalyseId(), "");
        anaId2QcInfoMap.put(loopData.getAnalyseId(), existPassStr + "," + passStr);
    }

    /**
     * 更新质控评价对象的质控限值信息 （曲线校核样检查项为公式参数时专用）
     *
     * @param evaluate 质控评价对象
     * @param limit    质控限值对象
     */
    private void setLimitInfoForItemCurveEvaluate(DtoQualityControlEvaluate evaluate, DtoQualityControlLimit limit, DtoAnalyseQualityControlData loopData, QualityControlKind kind,
                                                  Map<String, String> anaId2QcInfoMap) {
        String qcInfo = loopData.getQcInfo().replace("%", "");
        evaluate.setJudgingMethod(StringUtil.isNotNull(limit) ? limit.getJudgingMethod() : -1);
        String allowLimit = (StringUtil.isNotNull(limit) && StringUtil.isNotEmpty(limit.getAllowLimit())) ? limit.getAllowLimit() : "/";
        evaluate.setAllowLimit(allowLimit);
        //配置的允许限值不符合规范时，直接不评价
        evaluate.setIsPass((StringUtil.isNotNull(limit) && PATTERN.matcher(limit.getAllowLimit()).find()) ? kind.deviationQualified(limit, qcInfo) : null);
        //如果评价的值为无法计算时，则评价为不合格
        if (qcInfo.contains("无法计算")) {
            evaluate.setIsPass(false);
        }
        String passStr = (StringUtil.isNotNull(evaluate.getIsPass()) && evaluate.getIsPass()) ? "合格" : "不合格";
        String existPassStr = anaId2QcInfoMap.getOrDefault(loopData.getAnalyseId(), "");
        anaId2QcInfoMap.put(loopData.getAnalyseId(), existPassStr + "," + passStr);
    }

    /**
     * 平行计算需要检出限一半计算
     *
     * @return 检出限一半
     */
    protected String halfLimit(String value, String examLimitValue, String type) {
        //获取小于检出限计算方式参数配置
        String configValue = analyseDataService.getConfigValue(type);
        if (MathUtil.isNumeral(value) && MathUtil.isNumeral(examLimitValue)) {
            //根据参数值处理小于检出限的数值
            //检出限大于检测结果
            if (MathUtil.getBigDecimal(examLimitValue).compareTo(MathUtil.getBigDecimal(value)) > 0) {
                if (StringUtil.isNotEmpty(configValue)) {
                    if (EnumBase.EnumLessExamLimit.检出限一半.getValue().equals(configValue)) {
                        value = (MathUtil.getBigDecimal(examLimitValue).divide(new BigDecimal(2))).toString();
                    }
                    if (EnumBase.EnumLessExamLimit.检出限.getValue().equals(configValue)) {
                        value = examLimitValue;
                    }
                    if (EnumBase.EnumLessExamLimit.零.getValue().equals(configValue)) {
                        value = "0";
                    }
                    if (EnumBase.EnumLessExamLimit.检测结果.getValue().equals(configValue)) {
                        value = value;
                    }
                } else {
                    value = (MathUtil.getBigDecimal(examLimitValue).divide(new BigDecimal(2))).toString();
                }
            }
        }
        return value;
    }

    /**
     * 获取匹配的质控限值配置
     *
     * @param controlLimitList   质控限值配置列表
     * @param value              结果值
     * @param calculationService 计算服务对象
     */
    protected DtoQualityControlLimit getControlLimit(List<DtoQualityControlLimit> controlLimitList, String value, CalculationService calculationService) {
        if (MathUtil.isNumeral(value)) {
            for (DtoQualityControlLimit controlLimit : controlLimitList) {
                boolean isReplace = EnumLIM.EnumQCGrade.内部质控.getValue().equals(controlLimit.getQcGrade())
                        && EnumLIM.EnumQCType.替代物.getValue().equals(controlLimit.getQcType());
                String rangeConfig = isReplace ? "" : controlLimit.getRangeConfigData();
                Integer isCheckItem = controlLimit.getIsCheckItem();
                if (StringUtil.isNotNull(isCheckItem) && EnumLIM.EnumCheckItemType.出证结果.getValue().equals(isCheckItem)) {
                    if (StringUtil.isNotNull(rangeConfig)) {
                        if (DivationUtils.calculationResult(rangeConfig, new BigDecimal(value), calculationService)) {
                            return controlLimit;
                        }
                    } else {
                        return controlLimit;
                    }
                } else {
                    return controlLimit;
                }
            }
        }
        return null;
    }

    /**
     * 获取匹配的质控限值配置，加标样专用
     *
     * @param controlLimitList   质控限值配置列表
     * @param qc                 质控数据
     * @param calculationService 计算服务对象
     */
    protected DtoQualityControlLimit getControlLimitForJb(List<DtoQualityControlLimit> controlLimitList, DtoQualityControl qc,
                                                          DtoAnalyseQualityControlData yyData, CalculationService calculationService) {
        if (MathUtil.isNumeral(qc.getRealSampleTestValue())) {
            for (DtoQualityControlLimit controlLimit : controlLimitList) {
                String rangeConfig = controlLimit.getRangeConfigData();
                Integer isCheckItem = controlLimit.getIsCheckItem();
                if (StringUtil.isNotNull(isCheckItem) && isCheckItem == 1) {
                    String value = qc.getRealSampleTestValue();
                    if (controlLimit.getCheckItemOther().equals(EnumLIM.EnumCheckItemType.出证结果.name())
                            && MathUtil.isNumeral(yyData.getTestValueDst())) {
                        value = yyData.getTestValueDst();
                    }
                    if (StringUtil.isNotNull(rangeConfig)) {
                        if (DivationUtils.calculationResult(rangeConfig, new BigDecimal(value), calculationService)) {
                            return controlLimit;
                        }
                    } else {
                        return controlLimit;
                    }
                } else {
                    return controlLimit;
                }
            }
        }
        return null;
    }

    /**
     * 赋值加标样检查项
     *
     * @param limit    质控限值配置
     * @param evaluate 质控评价
     */
    protected void setJbCheckItem(DtoQualityControlLimit limit, DtoQualityControlEvaluate evaluate) {
        if (StringUtil.isNotNull(limit)) {
            // 更新加标检查项
            if (limit.getIsCheckItem() == 1) {
                evaluate.setCheckItem(limit.getCheckItemOther());
            }
        }
    }

    /**
     * 获取匹配的质控限值配置（曲线校核检查项为公式参数时专用）
     *
     * @param controlLimitList   质控限值配置列表
     * @param value              结果值
     * @param calculationService 计算服务对象
     */
    protected DtoQualityControlLimit getControlLimitForItemCurve(List<DtoQualityControlLimit> controlLimitList, String value, CalculationService calculationService) {
        if (MathUtil.isNumeral(value)) {
            for (DtoQualityControlLimit controlLimit : controlLimitList) {
                String rangeConfig = controlLimit.getRangeConfigData();
                if (StringUtil.isNotNull(rangeConfig)) {
                    if (DivationUtils.calculationResult(rangeConfig, new BigDecimal(value), calculationService)) {
                        return controlLimit;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取检查项内容
     *
     * @param controlLimitList 数据集合
     * @return 检查项内容
     */
    private static String getCheckItemOther(List<DtoQualityControlLimit> controlLimitList) {
        String checkItemOther = "";
        if (StringUtil.isNotEmpty(controlLimitList)) {
            DtoQualityControlLimit limit = controlLimitList.get(0);
            if (EnumLIM.EnumCheckItemType.公式参数.getValue().equals(limit.getCheckItem()) && StringUtil.isNotEmpty(limit.getCheckItemOther())) {
                checkItemOther = limit.getCheckItemOther();
            }
        }
        return checkItemOther;
    }

    /**
     * 获取检查项内容
     *
     * @param controlLimit 数据
     * @return 检查项内容
     */
    private static String getCheckItemOther(DtoQualityControlLimit controlLimit) {
        String checkItemOther = "";
        if (StringUtil.isNotNull(controlLimit)) {
            if (EnumLIM.EnumCheckItemType.公式参数.getValue().equals(controlLimit.getCheckItem()) && StringUtil.isNotEmpty(controlLimit.getCheckItemOther())) {
                checkItemOther = controlLimit.getCheckItemOther();
            }
        }
        return checkItemOther;
    }

    /**
     * 判断是否有配置小于检出限或者小于测定下限的限值配置，如有则默认获取其中一个
     *
     * @param controlLimitList 测试项目
     * @return 质控限值配置对象
     */
    private DtoQualityControlLimit checkDetectionTestLimit(List<DtoQualityControlLimit> controlLimitList) {
        for (DtoQualityControlLimit limit : controlLimitList) {
            if (EnumBase.EnumJudgingMethod.小于检出限.getValue().equals(limit.getJudgingMethod())
                    || EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(limit.getJudgingMethod())) {
                return limit;
            }
        }
        return null;
    }

    /**
     * 判断空白样允许限值
     *
     * @param limit          质控限值配置
     * @param examLimitValue 检出限
     * @param lowerLimit     测定下限
     * @return 允许限值
     */
    public static String getYxPcForBlank(DtoQualityControlLimit limit, String examLimitValue, String lowerLimit) {
        String yxPc = "/";
        if (StringUtil.isNotNull(limit)) {
            if (EnumBase.EnumJudgingMethod.小于检出限.getValue().equals(limit.getJudgingMethod())) {
                yxPc = examLimitValue;
            } else if (EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(limit.getJudgingMethod())) {
                yxPc = lowerLimit;
            } else {
                yxPc = StringUtil.isNotEmpty(limit.getAllowLimitData()) ? limit.getAllowLimitData() : "/";
            }
        }
        return yxPc;
    }

    /**
     * 判断空白样是否合格
     *
     * @param limit           质控限值配置
     * @param examLimitValue  检出限
     * @param lowerLimitValue 测定下限
     * @param samValue        检查项值
     * @param isPass          是否合格
     * @return 是否合格
     */
    public static Boolean checkPassForBlank(DtoQualityControlLimit limit, String examLimitValue, String lowerLimitValue, String samValue, Boolean isPass) {
        Boolean pass = null;
        if (StringUtil.isNotNull(limit)) {
            if (EnumBase.EnumJudgingMethod.小于检出限.getValue().equals(limit.getJudgingMethod())) {
                if (MathUtil.isNumeral(samValue) && MathUtil.isNumeral(examLimitValue)) {
                    pass = new BigDecimal(samValue).compareTo(new BigDecimal(examLimitValue)) < 0 ? true : false;
                }
            } else if (EnumBase.EnumJudgingMethod.小于测定下限.getValue().equals(limit.getJudgingMethod())) {
                if (MathUtil.isNumeral(samValue) && MathUtil.isNumeral(lowerLimitValue)) {
                    pass = new BigDecimal(samValue).compareTo(new BigDecimal(lowerLimitValue)) < 0 ? true : false;
                }
            } else {
                pass = isPass;
            }
        }
        return pass;
    }

    /**
     * 重新计算平行样的偏差
     *
     * @param controlLimitList              质控限值配置列表
     * @param loopData                      分析质控信息对象
     * @param analyseQualityControlDataList 分析数据对象
     * @param workSheetFolderId             检测单id
     * @return 偏差
     */
    protected String reCalculateQcInfoForParallel(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                                  List<DtoAnalyseQualityControlData> analyseQualityControlDataList,
                                                  String workSheetFolderId, DtoAnalyseQualityControlData yyAnaData, String sampleTypeId) {
        String qcInfo = StringUtil.isNotEmpty(loopData.getQcInfo()) ? loopData.getQcInfo() : "";
        if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId) && StringUtil.isNotNull(yyAnaData)) {
            List<String> dataValueList = new ArrayList<>();
            String yyTestValue = yyAnaData.getTestOriginValue();
            //判断是否有加原样 -- 调整除开原样，还要判断是否存在串联样
//            List<DtoAnalyseQualityControlData> dataList = analyseQualityControlDataList.stream()
//                    .filter(p -> p.getAssociateSampleId().equals(yyAnaData.getSampleId())
//                            && p.getQcType().equals(new QualitySampleCopy().qcTypeValue())).collect(Collectors.toList());
            //需要调整 判断是否存在加原样，有的话需要以中间值当做结果
            if (StringUtil.isNotEmpty(yyAnaData.getSeriesValue())) {
                yyTestValue = yyAnaData.getSeriesValue();
            }
            List<DtoTest> testList = testService.findAll(Collections.singletonList(loopData.getTestId()));
            DtoTest test = StringUtil.isNotEmpty(testList) ? testList.get(0) : new DtoTest();
            //测试项目测定下限
            Optional<DtoTestExpand> expandOptional = test.getTestExpandList().stream()
                    .filter(p -> sampleTypeId.equals(p.getSampleTypeId())).findFirst();
            String lowerLimit = test.getLowerLimit();
            if (expandOptional.isPresent()) {
                if (StringUtil.isNotEmpty(expandOptional.get().getLowerLimit())) {
                    lowerLimit = expandOptional.get().getLowerLimit();
                }
            }
            yyTestValue = analyseDataService.halfLimitTestOrignValue(yyTestValue, yyAnaData.getTestValueDst(), loopData.getExamLimitValue(), test);
            if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                yyTestValue = proService.getDecimal(yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal(), yyTestValue);
            }
            List<DtoQualityControlLimit> limitList = controlLimitList.stream().filter(p -> p.getQcType().equals(new QualityParallel().qcTypeValue())
                            && loopData.getQcGrade().equals(p.getQcGrade())).collect(Collectors.toList());
            DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
            qualityControlLimit.setTestId(loopData.getTestId());
            qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.相对偏差.getValue());
            qualityControlLimit.setQcType(new QualityParallel().qcTypeValue());
            qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
            //添加一个默认的类型
            if (StringUtil.isEmpty(limitList)) {
                limitList.add(qualityControlLimit);
            }
            //点击更新质控信息按钮时需要重新计算平行样的偏差
            if (StringUtil.isNotNull(yyAnaData)) {
                List<DtoAnalyseQualityControlData> pxDataForYy;
                List<String> inTestValue = new ArrayList<>();
                if (EnumLIM.EnumQCGrade.内部质控.getValue().equals(loopData.getQcGrade())) {
                    //找到原样的所有室内平行样
                    pxDataForYy = analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(yyAnaData.getSampleId())
                            && p.getTestId().equals(loopData.getTestId()) && EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade())
                            && new QualityParallel().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList());
                    for (DtoAnalyseQualityControlData data : pxDataForYy) {
                        if (EnumLIM.EnumAverageCompute.原样值.getValue().equals(test.getAverageCompute())) {
                            inTestValue.add(yyTestValue);
                        } else {
                            inTestValue.add(analyseDataService.halfLimitTestOrignValue(data.getTestOriginValue(),
                                    proService.getDecimal(loopData.getMostSignificance(), loopData.getMostDecimal(), data.getTestValueDst()),
                                    loopData.getExamLimitValue(), test));
                        }
                    }
                } else {
                    //加入当前的室外平行样
                    pxDataForYy = new ArrayList<>(Collections.singletonList(loopData));
                    //找到室外平行样对应的室外平行样
                    pxDataForYy.addAll(analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(loopData.getSampleId())
                            && p.getTestId().equals(loopData.getTestId()) && EnumLIM.EnumQCGrade.外部质控.getValue().equals(p.getQcGrade())
                            && new QualityParallel().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList()));
                    //找到对应的室内平行样
                    List<DtoAnalyseQualityControlData> pxDataList = analyseQualityControlDataList.stream()
                            .filter(p -> p.getAssociateSampleId().equals(yyAnaData.getSampleId())
                                    && EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade())
                                    && yyAnaData.getTestId().equals(p.getTestId())
                                    && new QualityParallel().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList());
                    if (pxDataList.size() > 0) {
                        List<String> pxValueList = new ArrayList<>();
                        for (DtoAnalyseQualityControlData data : pxDataList) {
                            String testOriginValue = data.getTestOriginValue();
                            if (EnumLIM.EnumAverageCompute.原样值.getValue().equals(test.getAverageCompute())) {
                                testOriginValue = yyTestValue;
                            } else {
                                if (StringUtil.isNotEmpty(data.getSeriesValue())) {
                                    testOriginValue = data.getSeriesValue();
                                }
                                testOriginValue = analyseDataService.halfLimitTestOrignValue(testOriginValue, data.getTestValueDst(), data.getExamLimitValue(), test);
                            }
                            pxValueList.add(testOriginValue);
                        }
                        if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                            pxValueList.clear();
                            for (DtoAnalyseQualityControlData data : pxDataList) {
                                String testOriginValue = data.getTestOriginValue();
                                if (EnumLIM.EnumAverageCompute.原样值.getValue().equals(test.getAverageCompute())) {
                                    testOriginValue = yyTestValue;
                                } else {
                                    if (StringUtil.isNotEmpty(data.getSeriesValue())) {
                                        testOriginValue = data.getSeriesValue();
                                    }
                                    testOriginValue = analyseDataService.halfLimitTestOrignValue(testOriginValue, data.getTestValueDst(), data.getExamLimitValue(), test);
                                    testOriginValue = proService.getDecimal(data.getMostSignificance(), data.getMostDecimal(), testOriginValue);
                                    testOriginValue = halfLimit(testOriginValue, data.getExamLimitValue(), "qualityControl");
                                }
                                pxValueList.add(testOriginValue);
                            }
                        }
                        pxValueList.add(yyTestValue);
                        BigDecimal sum = new BigDecimal(0);
                        for (String val : pxValueList) {
                            val = halfLimit(val, loopData.getExamLimitValue(), "qualityControl");
                            try {
                                BigDecimal value = new BigDecimal(val);
                                sum = sum.add(value);
                            } catch (Exception ex) {
                                System.out.println(ex.getMessage());
                            }
                        }
                        BigDecimal avg = sum.divide(new BigDecimal(pxValueList.size()), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).stripTrailingZeros();
                        if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                            avg = new BigDecimal(proService.getDecimal(yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal(), avg.toString()));
                        }
                        yyTestValue = avg.toString();
                    }
                }

                BigDecimal sum = new BigDecimal(0);
                if (StringUtil.isNotEmpty(yyTestValue) && MathUtil.isNumeral(yyTestValue)) {
                    sum = sum.add(new BigDecimal(halfLimit(yyTestValue, yyAnaData.getExamLimitValue(), "qualityControl")));
                }
                int count = 1;
                for (DtoAnalyseQualityControlData data : pxDataForYy) {
                    String val = data.getTestOriginValue();
                    if (StringUtil.isNotEmpty(data.getSeriesValue())) {
                        val = data.getSeriesValue();
                    }
                    if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                        val = proService.getDecimal(data.getMostSignificance(), data.getMostDecimal(), val);
                    }
                    String oriVal = halfLimit(val, data.getExamLimitValue(), "qualityControl");
                    if (MathUtil.isNumeral(oriVal)) {
                        sum = sum.add(new BigDecimal(oriVal));
                        count++;
                    }
                }
                List<String> valueList = new ArrayList<>();
                List<String> absValueList = new ArrayList<>();
                boolean isCompute = Boolean.FALSE;
                String yyVal = halfLimit(yyTestValue, yyAnaData.getExamLimitValue(), "qualityControl");
                boolean isComputeOne = isExamLimit(yyVal, loopData.getExamLimitValue());
                valueList.add(yyVal);
                absValueList.add(yyVal);
                dataValueList.add(yyVal);
                String inPxVal = "";
                if (EnumLIM.EnumQCGrade.内部质控.getValue().equals(loopData.getQcGrade())) {
                    String testOriginValue = loopData.getTestOriginValue();
                    if (StringUtil.isNotEmpty(loopData.getSeriesValue())) {
                        testOriginValue = loopData.getSeriesValue();
                    }
                    inPxVal = analyseDataService.halfLimitTestOrignValue(testOriginValue, loopData.getTestValueDst(), loopData.getExamLimitValue(), test);
                    if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                        inPxVal = proService.getDecimal(loopData.getMostSignificance(), loopData.getMostDecimal(), inPxVal);
                    }
                    inPxVal = halfLimit(inPxVal, loopData.getExamLimitValue(), "qualityControl");
                } else {
                    if (StringUtil.isNotEmpty(pxDataForYy)) {
                        DtoAnalyseQualityControlData xcData = pxDataForYy.get(0);
                        //找到现场平行样对应的室内平行样
                        List<DtoAnalyseQualityControlData> xpSNDataList = analyseQualityControlDataList.stream().filter(p -> p.getAssociateSampleId().equals(xcData.getSampleId())
                                && p.getTestId().equals(xcData.getTestId()) && EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade())
                                && new QualityParallel().qcTypeValue().equals(p.getQcType())).collect(Collectors.toList());
                        String testOriginValue = xcData.getTestOriginValue();
                        if (xpSNDataList.size() > 0) {
                            testOriginValue = xcData.getPxAverageValue();
                        }
                        if (StringUtil.isNotEmpty(xcData.getSeriesValue())) {
                            testOriginValue = xcData.getSeriesValue();
                        }
                        inPxVal = analyseDataService.halfLimitTestOrignValue(testOriginValue, xcData.getTestValueDst(), xcData.getExamLimitValue(), test);
                        if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                            inPxVal = proService.getDecimal(xcData.getMostSignificance(), xcData.getMostDecimal(), inPxVal);
                        }
                        inPxVal = halfLimit(inPxVal, xcData.getExamLimitValue(), "qualityControl");
                    }
                }
                boolean isComputeTwo = isExamLimit(inPxVal, loopData.getExamLimitValue());
                valueList.add(inPxVal);
                absValueList.add(inPxVal);
                String avg = sum.divide(new BigDecimal(count), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).toString();
                if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                    avg = proService.getDecimal(yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal(), avg);
                }
                if (!isComputeOne || !isComputeTwo) {
                    if (!(!isComputeOne && !isComputeTwo)) {
                        isCompute = Boolean.TRUE;
                    }
                }
                valueList.add(avg);
                absValueList.add(avg);
                dataValueList.addAll(inTestValue);
                if (inTestValue.size() > 1) {
                    isCompute = Boolean.FALSE;
                    //多个平行样时需要按照标准差的方式进行计算
                    List<String> yyPxValList = analyseDataService.collectYyPxVal(yyVal, inTestValue, yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal());
                    valueList.addAll(yyPxValList);
                }
                //计算偏差
                String rateStr = "";
                // 判断原样值，平行样值是否全部小于检测下限
                boolean outParallelQuality = analyseDataService.outParallelQuality(dataValueList, lowerLimit);
                if (!isCompute && !outParallelQuality) {
                    List<DtoQualityControlLimit> absLimitList = limitList.stream().filter(p -> EnumBase.EnumJudgingMethod.绝对偏差.getValue().equals(p.getJudgingMethod())).collect(Collectors.toList());
                    DtoQualityControlLimit limit = null;
                    //先判断绝对偏差
                    if (absLimitList.size() > 0) {
                        Map<String, Object> qcMap = new QualityParallel().calculateDeviationValue(absLimitList, absValueList);
                        limit = (DtoQualityControlLimit) qcMap.get("limit");
                        rateStr = qcMap.getOrDefault("qcRate", "").toString();
                    }
                    //排除绝对偏差的计算
                    if (!StringUtil.isNotNull(limit)) {
                        limitList = limitList.stream().filter(p -> !EnumBase.EnumJudgingMethod.绝对偏差.getValue().equals(p.getJudgingMethod())).collect(Collectors.toList());
                        Map<String, Object> qcMap = new QualityParallel().calculateDeviationValue(limitList, valueList);
                        limit = (DtoQualityControlLimit) qcMap.get("limit");
                        rateStr = qcMap.getOrDefault("qcRate", "").toString();
                    }
                    if (StringUtil.isNotNull(limit) && StringUtil.isNotEmpty(rateStr)) {
                        //平行样qcInfo按照两位有效数字，两位小数修约
                        rateStr = analyseDataService.checkAbsoluteDeviation(rateStr, limit.getJudgingMethod(),
                                yyAnaData.getMostSignificance(), yyAnaData.getMostDecimal(), new QualityParallel());
                        //相对偏差或者相对误差需要带%
                        if (EnumBase.EnumJudgingMethod.相对偏差.getValue().equals(limit.getJudgingMethod())
                                || EnumBase.EnumJudgingMethod.相对误差.getValue().equals(limit.getJudgingMethod())) {
                            if (!(valueList.size() > 3)) {
                                rateStr = rateStr.contains("无法计算") ? rateStr : rateStr + "%";
                            }
                        }
                    }
                } else {
                    rateStr = "无法计算";
                    if (outParallelQuality) {
                        rateStr = "无法计算，不予评价";
                    }
                }
                qcInfo = rateStr;
            }
        }
        return qcInfo;
    }

    private boolean isExamLimit(String value, String examLimitValue) {
        boolean isExamLimit = Boolean.FALSE;
        if (MathUtil.isNumeral(value) && MathUtil.isNumeral(examLimitValue)) {
            //检出限大于检测结果
            if (MathUtil.getBigDecimal(examLimitValue).compareTo(MathUtil.getBigDecimal(value)) > 0) {
                isExamLimit = Boolean.TRUE;
            }
        }
        return isExamLimit;
    }

    /**
     * 重新计算曲线校核样的偏差 (曲线校核样检查项为公式参数时专用)
     *
     * @param controlLimitList 质控限值配置列表
     * @param loopData         分析质控信息对象
     * @param checkItemOther   检测项公式参数名称
     * @return 偏差
     */
    private String reCalculateQcInfoForItemCurve(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData,
                                                 String checkItemOther, String workSheetFolderId) {
        String samVal = StringUtil.isNotEmpty(checkItemOther) ? loopData.getParamMap().getOrDefault(checkItemOther, "/") : "/";
        String qcInfo = loopData.getQcInfo();
        if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId)) {
            List<String> valueList = new ArrayList<>();
            valueList.add(loopData.getQcValue());
            valueList.add(samVal);
            valueList.add(samVal);
            List<DtoQualityControlLimit> limitList = controlLimitList.stream().filter(p -> p.getQcType().equals(loopData.getQcType())).collect(Collectors.toList());
            DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
            qualityControlLimit.setTestId(loopData.getTestId());
            qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.相对偏差.getValue());
            qualityControlLimit.setQcType(new CurveCheck().qcTypeValue());
            qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
            limitList.add(qualityControlLimit);
            if (limitList.size() > 0) {
                Map<String, Object> qcMap = new CurveCheck().calculateDeviationValue(limitList, valueList);
                //计算室内范围偏差
                DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                if (StringUtil.isNotNull(limit)) {
                    String deviation = qcMap.get("qcRate").toString();
                    Integer sign = new CurveCheck().getQualityConfig().getMostSignificance();
                    Integer md = new CurveCheck().getQualityConfig().getMostDecimal();
                    String recoverRateStr = "";
                    recoverRateStr = proService.getDecimal(sign, md, deviation);
                    Boolean isPass = new CurveCheck().deviationQualified(limit, recoverRateStr);
                    if (StringUtil.isNotEmpty(recoverRateStr)) {
                        recoverRateStr = recoverRateStr.contains("无法计算") ? recoverRateStr : recoverRateStr + "%";
                    }
                    qcInfo = recoverRateStr;
                }
            }
        }
        return StringUtil.isNotNull(qcInfo) ? qcInfo : "";
    }

    /**
     * 重新计算曲线校核样的偏差
     *
     * @param controlLimitList  质控限值配置列表
     * @param loopData          分析质控信息对象
     * @param workSheetFolderId 检测单id
     * @return 偏差
     */
    private String reCalculateQcInfoForCurve(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, String workSheetFolderId) {
        String qcInfo = loopData.getQcInfo();
        String testValueDst = halfLimit(loopData.getTestValueDst(), loopData.getExamLimitValue(), "qualityControl");
//        String testValueDst = loopData.getTestValueDst();
        if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId)) {
            List<String> valueList = new ArrayList<>();
            valueList.add(loopData.getQcValue());
            valueList.add(testValueDst);
            valueList.add(testValueDst);
            List<DtoQualityControlLimit> limitList = controlLimitList.stream().filter(p -> p.getQcType().equals(loopData.getQcType())).collect(Collectors.toList());
            DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
            qualityControlLimit.setTestId(loopData.getTestId());
            qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.相对偏差.getValue());
            qualityControlLimit.setQcType(new CurveCheck().qcTypeValue());
            qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
            limitList.add(qualityControlLimit);
            if (limitList.size() > 0) {
                Map<String, Object> qcMap = new CurveCheck().calculateDeviationValue(limitList, valueList);
                //计算室内范围偏差
                DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                if (StringUtil.isNotNull(limit)) {
                    String deviation = qcMap.get("qcRate").toString();
                    Integer sign = new CurveCheck().getQualityConfig().getMostSignificance();
                    Integer md = new CurveCheck().getQualityConfig().getMostDecimal();
                    String recoverRateStr = "";
                    recoverRateStr = proService.getDecimal(sign, md, deviation);
                    if (StringUtil.isNotEmpty(recoverRateStr)) {
                        recoverRateStr = recoverRateStr.contains("无法计算") ? recoverRateStr : recoverRateStr + "%";
                    }
                    qcInfo = recoverRateStr;
                }
            }
        }
        return StringUtil.isNotNull(qcInfo) ? qcInfo : "";
    }

    /**
     * 重新计算曲线校核样的偏差
     *
     * @param controlLimitList  质控限值配置列表
     * @param loopData          分析质控信息对象
     * @param workSheetFolderId 检测单id
     * @return 偏差
     */
    private String reCalculateQcInfoForJz(List<DtoQualityControlLimit> controlLimitList, DtoAnalyseQualityControlData loopData, String workSheetFolderId) {
        String qcInfo = loopData.getQcInfo();
        String testValueDst = halfLimit(loopData.getTestValueDst(), loopData.getExamLimitValue(), "qualityControl");
//        String testValueDst = loopData.getTestValueDst();
        if (!UUIDHelper.GUID_EMPTY.equals(workSheetFolderId)) {
            List<String> valueList = new ArrayList<>();
            valueList.add(loopData.getQcValue());
            valueList.add(testValueDst);
            valueList.add(testValueDst);
            List<DtoQualityControlLimit> limitList = controlLimitList.stream().filter(p -> p.getQcType().equals(loopData.getQcType())).collect(Collectors.toList());
            DtoQualityControlLimit qualityControlLimit = new DtoQualityControlLimit();
            qualityControlLimit.setTestId(loopData.getTestId());
            qualityControlLimit.setIsDefault(true);
            qualityControlLimit.setJudgingMethod(EnumBase.EnumJudgingMethod.相对偏差.getValue());
            qualityControlLimit.setQcType(new QualityCorrectionFactor().qcTypeValue());
            qualityControlLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
            limitList.add(qualityControlLimit);
            if (limitList.size() > 0) {
                Map<String, Object> qcMap = new QualityCorrectionFactor().calculateDeviationValue(limitList, valueList);
                //计算室内范围偏差
                DtoQualityControlLimit limit = (DtoQualityControlLimit) qcMap.get("limit");
                if (StringUtil.isNotNull(limit)) {
                    String deviation = qcMap.get("qcRate").toString();
                    Integer sign = new QualityCorrectionFactor().getQualityConfig().getMostSignificance();
                    Integer md = new QualityCorrectionFactor().getQualityConfig().getMostDecimal();
                    String recoverRateStr = "";
                    recoverRateStr = proService.getDecimal(sign, md, deviation);
                    if (StringUtil.isNotEmpty(recoverRateStr)) {
                        recoverRateStr = recoverRateStr.contains("无法计算") ? recoverRateStr : recoverRateStr + "%";
                    }
                    qcInfo = recoverRateStr;
                }
            }
        }
        return StringUtil.isNotNull(qcInfo) ? qcInfo : "";
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    @Lazy
    public void setProService(ProService proService) {
        this.proService = proService;
    }

    @Autowired
    public void setAnalyseOriginalRecordRepository(AnalyseOriginalRecordRepository analyseOriginalRecordRepository) {
        this.analyseOriginalRecordRepository = analyseOriginalRecordRepository;
    }

    @Autowired
    public void setParamsTestFormulaRepository(ParamsTestFormulaRepository paramsTestFormulaRepository) {
        this.paramsTestFormulaRepository = paramsTestFormulaRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    @Lazy
    public void setQualityControlLimitService(QualityControlLimitService qualityControlLimitService) {
        this.qualityControlLimitService = qualityControlLimitService;
    }
}
