package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.PersonAbilityCriteria;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.repository.lims.PersonAbilityRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.ExpImpPersonAbilityService;
import com.sinoyd.lims.lim.service.PersonAbilityService;
import com.sinoyd.lims.lim.service.PersonCertService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyPersonAbilityVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员检测能力导出导入接口实现
 *
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2023/12/21
 */
@Service
public class ExpImpPersonAbilityServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPersonAbility, String, PersonAbilityRepository> implements ExpImpPersonAbilityService {

    private PersonAbilityService personAbilityService;

    private ImportUtils importUtils;

    private TestService testService;

    private SampleTypeService sampleTypeService;

    private PersonCertService personCertService;

    private PersonRepository personRepository;

    private CodeService codeService;

    private AnalyzeMethodRepository analyzeMethodRepository;

    @Override
    @Transactional
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoPersonAbility> page = new PageBean<>();
        PersonAbilityCriteria criteria = (PersonAbilityCriteria) baseCriteria;
        page.setEntityName("DtoPersonAbility p, DtoPersonCert pt");
        page.setSelect("select p");
//        page.setSort("pt.certCode, st.typeName-, t.redAnalyzeItemName-, t.redAnalyzeMethodName-");
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        personAbilityService.findByPage(page, criteria);
        List<DtoPersonAbility> personAbilityList = page.getData();
        // 用户信息
        String personId = criteria.getPersonId();
        DtoPerson person = personRepository.findOne(personId);
        fileName = person.getCName() + "上岗证明细";
//        // 检测类型
        List<DtoSampleType> typeList = sampleTypeService.findAll();
        List<DtoSampleType> finalTypeList = typeList.stream()
                .filter(p -> p.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue()))
                .sorted(Comparator.comparing(DtoSampleType::getOrderNum).reversed())
                .collect(Collectors.toList());
        // 证书类别
        List<DtoCode> personCertCode = codeService.findCodes(BaseCodeHelper.PersonCertType);
//        // 证书信息
        List<String> certIds = personAbilityList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getPersonCertId())
                && !p.getPersonCertId().equals(UUIDHelper.GUID_EMPTY)).map(DtoPersonAbility::getPersonCertId).distinct().collect(Collectors.toList());
        List<DtoPersonCert> certList = StringUtil.isNotEmpty(certIds) ? personCertService.findAll(certIds) : new ArrayList<>();
        Date year1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        List<DtoExpImpPersonAbility> expImpPersonAbilities = new ArrayList<>();
        for (DtoPersonAbility personAbility : personAbilityList) {
            DtoExpImpPersonAbility expImpPersonAbility = new DtoExpImpPersonAbility();
            BeanUtils.copyProperties(personAbility, expImpPersonAbility);
            expImpPersonAbility.setName(person.getCName());

            // 日期处理
            String certEffectiveTime = personAbility.getCertEffectiveTime().compareTo(year1753) == 0 ? "" : DateUtil.dateToString(personAbility.getCertEffectiveTime(), DateUtil.YEAR);
            String achieveDate = personAbility.getAchieveDate().compareTo(year1753) == 0 ? "" : DateUtil.dateToString(personAbility.getAchieveDate(), DateUtil.YEAR);
            expImpPersonAbility.setAchieveDate(achieveDate);
            expImpPersonAbility.setCertEffectiveTime(certEffectiveTime);
            expImpPersonAbilities.add(expImpPersonAbility);
        }
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpPersonAbility.class, expImpPersonAbilities);
        // 设置下拉框、
        String[] sampleTypeArray = finalTypeList.stream().map(DtoSampleType::getTypeName).toArray(String[]::new);
        String[] certArray = certList.stream().map(DtoPersonCert::getCertCode).toArray(String[]::new);
        String[] certTypeArray = personCertCode.stream().map(DtoCode::getDictName).toArray(String[]::new);
        importUtils.selectList(workBook, 5, 5, sampleTypeArray);
        importUtils.selectList(workBook, 6, 6, certArray);
        importUtils.selectList(workBook, 9, 9, certTypeArray);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    public List<DtoPersonAbility> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {

        List<DtoPersonCert> personCerts = personCertService.findAll();
        List<DtoTest> testList = testService.findAll();
        List<DtoPersonAbility> personAbilityList = repository.findAll();
        // 检测类型
        List<DtoSampleType> typeList = sampleTypeService.findAll();
        typeList = typeList.stream()
                .filter(p -> p.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue()))
                .collect(Collectors.toList());
        List<DtoAnalyzeMethod> analyzeMethodList = analyzeMethodRepository.findAll();
        List<DtoPerson> personList = personRepository.findAll();
        // 证书类别
        List<DtoCode> personCertCode = codeService.findCodes(BaseCodeHelper.PersonCertType);
        // 初始化校验器
        ImpModifyPersonAbilityVerify verifyHandler = new ImpModifyPersonAbilityVerify(testList, personCerts, personAbilityList, typeList, personList, analyzeMethodList,personCertCode);
        ExcelImportResult<DtoExpImpPersonAbility> importResult = getExcelData(verifyHandler, file, response);

        //获取校验成功得导入数据
        List<DtoExpImpPersonAbility> importList = importResult.getList();
        //移除空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getName()));
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        List<DtoPersonAbility> personAbilities = importToEntity(importList, personCerts, personList, personAbilityList);
        addData(personAbilities);
        return repository.findAll();
    }

    /**
     * 导入数据转实体
     *
     * @param importList           导入数据源
     * @param personCerts          人员证书集合
     * @param personList           人员集合
     * @param personAbilityAllList 系统中的人员检测能力
     * @return
     */
    private List<DtoPersonAbility> importToEntity(List<DtoExpImpPersonAbility> importList, List<DtoPersonCert> personCerts, List<DtoPerson> personList, List<DtoPersonAbility> personAbilityAllList) {
        List<DtoPersonAbility> personAbilityList = new ArrayList<>();
        Map<String, DtoPersonAbility> abilityMap = personAbilityAllList.stream().collect(Collectors.toMap(DtoPersonAbility::getId, p -> p));

        for (DtoExpImpPersonAbility impPersonAbility : importList) {
            DtoPersonAbility dtoPersonAbility = new DtoPersonAbility();
            if (abilityMap.containsKey(impPersonAbility.getId())) {
                dtoPersonAbility = abilityMap.get(impPersonAbility.getId());
            } else {
                impPersonAbility.setId(UUIDHelper.NewID());
            }
            BeanUtils.copyProperties(impPersonAbility, dtoPersonAbility);
            DtoPerson dtoPerson = personList.stream().filter(p -> p.getCName().equals(impPersonAbility.getName())).findFirst().orElse(new DtoPerson());
            dtoPersonAbility.setPersonId(dtoPerson.getId());
            DtoPersonCert dtoPersonCert = personCerts.stream().filter(p -> impPersonAbility.getPersonCertCode().equals(p.getCertCode())).findFirst().orElse(null);
            if (StringUtil.isNotNull(dtoPersonCert)) {
                dtoPersonAbility.setPersonCertId(dtoPersonCert.getId());
            }
            dtoPersonAbility.setAchieveDate(impPersonAbility.getAchieveDate() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(impPersonAbility.getAchieveDate()));
            dtoPersonAbility.setCertEffectiveTime(impPersonAbility.getCertEffectiveTime() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(impPersonAbility.getCertEffectiveTime()));
            personAbilityList.add(dtoPersonAbility);
        }
        return personAbilityList;
    }

    @Override
    @Transactional
    public void addData(List<DtoPersonAbility> data) {
        if (StringUtil.isNotEmpty(data)) {
            repository.save(data);
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpPersonAbility> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    @Override
    public ExcelImportResult<DtoExpImpPersonAbility> getExcelData(IExcelVerifyHandler<DtoExpImpPersonAbility> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(verifyHandler);
        ExcelImportResult<DtoExpImpPersonAbility> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoExpImpPersonAbility.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "人员检测能力导入错误信息");
            PoiExcelUtils.downLoadExcel("人员检测能力导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }


    @Autowired
    public void setPersonAbilityService(PersonAbilityService personAbilityService) {
        this.personAbilityService = personAbilityService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    public void setPersonCertService(PersonCertService personCertService) {
        this.personCertService = personCertService;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }
}
