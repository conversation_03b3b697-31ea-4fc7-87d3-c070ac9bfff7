package com.sinoyd.lims.lim.service;

import java.util.Collection;
import java.util.List;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;

/**
 * 测试人员接口
 * <AUTHOR>
 * @version V1.0.0 2019/1/16
 * @since V100R001
 */
public interface Person2TestService extends IBaseJpaService<DtoPerson2Test, String> {

    /**
     * 获取可检测人员
     *
     * @param sampleTypeId 检测类型id(小类)
     * @param testId       测试项目id
     * @return 返回想要的检测人员
     */
    List<DtoPerson2Test> getTestPersons(String sampleTypeId, String testId);

    /**
     * 根据测试项目ids获取相关的检测人员
     *
     * @param testIds 测试项目ids
     * @return 返回想要的检测人员
     */
    List<DtoPerson2Test> findByTestIds(List<String> testIds);

    /**
     * 清除测试人员配置
     *
     * @param sampleTypeId 检测类型id(小类)
     * @param testId       测试项目id
     * @return 是否清除成功
     */
    Boolean delete(String sampleTypeId, String testId);

    /**
     * 保存测试人员配置
     *
     * @param entities 测试人员列表
     * @return
     */
    List<DtoPerson2Test> save(List<DtoPerson2Test> entities);


    /**
     * 根据测试项目id 获取相关的测试岗位
     *
     * @param testIds 测试项目ids
     * @return 返回相应的测试岗位信息
     */
    List<DtoPerson2Test> findRedisByTestIds(List<String> testIds);


    /**
     * 根据测试项目id 获取相关的测试岗位
     *
     * @param testId 测试项目id
     * @return 返回相应的测试岗位信息
     */
    List<DtoPerson2Test> findRedisByTestId(String testId);

    /**
     * 根据检测小类id及测试项目集合返回对应的第一负责人
     *
     * @param sampleTypeId 检测小类ID
     * @param testIds      测试项目ID集合
     * @return 返回第一负责人
     */
    List<DtoPerson2Test> findBySampleTypeAndTestIds(String sampleTypeId, Collection<String> testIds);

    /**
     * 根据测试项目id查询第一负责人员
     *
     * @param testId 测试项目id
     * @return 第一负责人
     */
    List<DtoPerson2Test> findByIsDefaultPersonTrueAndTestId(String testId);

    /**
     * 批量保存测试人员
     *
     * @param dtoPerson2Tests 模版
     * @param testIds         测试项目ids
     * @return 返回保存后的人员
     */
    List<DtoPerson2Test> save(List<DtoPerson2Test> dtoPerson2Tests, List<String> testIds);

    /**
     * 返回去重的检测类型id
     *
     * @param testId 测试项目id
     * @return 返回去重的检测类型数据
     */
    List<String> findSampleTypeIdByTestId(String testId);
}