CREATE TABLE "TB_QA_ANNUALPLAN"
(
    "id"             VARCHAR(50)   NOT NULL,
    "year"           INT          DEFAULT 0
                                   NOT NULL,
    "planType"       VARCHAR(50)   NOT NULL,
    "makePersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "makePersonName" VARCHAR(50) NULL,
    "makeTime"       TIMESTAMP(0)  NOT NULL,
    "auditContent"   VARCHAR(2000) NOT NULL,
    "auditPurpose"   VARCHAR(2000) NOT NULL,
    "remark"         VARCHAR(2000) NULL,
    "status"         VARCHAR(50) NULL,
    "isDeleted"      BIT          DEFAULT 0
                                   NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL
);
CREATE TABLE "TB_QA_CUSTOMERCOMPLAINTREGIST"
(
    "id"                 VARCHAR(50)   NOT NULL,
    "complaintName"      VARCHAR(250)  NOT NULL,
    "entId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "complaintPerson"    VARCHAR(50)   NOT NULL,
    "complaintPersonId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "problemDescription" VARCHAR(2000) NOT NULL,
    "phone"              VARCHAR(20) NULL,
    "email"              VARCHAR(100) NULL,
    "type"               VARCHAR(50) NULL,
    "complaintDate"      TIMESTAMP(0)  NOT NULL,
    "registPerson"       VARCHAR(50)   NOT NULL,
    "registPersonId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "registDate"         TIMESTAMP(0)  NOT NULL,
    "level"              VARCHAR(50) NULL,
    "finshDate"          TIMESTAMP(0) NULL,
    "opinion"            VARCHAR(1000) NULL,
    "status"             VARCHAR(50)   NOT NULL,
    "isDeleted"          BIT          DEFAULT 0
                                       NOT NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                       NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                       NOT NULL
);
CREATE TABLE "TB_QA_INTERNALAUDITIMPLEMENTPLAN"
(
    "id"             VARCHAR(50)  NOT NULL,
    "auditTime"      TIMESTAMP(0) NOT NULL,
    "auditedDept"    VARCHAR(100) NOT NULL,
    "personInCharge" VARCHAR(50)  NOT NULL,
    "auditor"        VARCHAR(50)  NOT NULL,
    "auditElement"   VARCHAR(2000) NULL,
    "isDeleted"      BIT          DEFAULT 0
                                  NOT NULL,
    "status"         VARCHAR(50) NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "auditPlanId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "auditedDeptId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL
);
CREATE TABLE "TB_QA_INTERNALAUDITPLAN"
(
    "id"           VARCHAR(50)   NOT NULL,
    "annualPlanId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "auditTime"    TIMESTAMP(0)  NOT NULL,
    "attendee"     VARCHAR(2000) NOT NULL,
    "auditPurp"    VARCHAR(2000) NOT NULL,
    "auditScope"   VARCHAR(2000) NOT NULL,
    "auditContent" VARCHAR(2000) NOT NULL,
    "auditGist"    VARCHAR(2000) NOT NULL,
    "status"       VARCHAR(50) NULL,
    "isDeleted"    BIT          DEFAULT 0
                                 NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL
);
CREATE TABLE "TB_QA_INTERNALAUDITPLANREPORT"
(
    "id"             VARCHAR(50)   NOT NULL,
    "internalPlanId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "makerId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "makerName"      VARCHAR(50) NULL,
    "makerTime"      TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                   NOT NULL,
    "auditReview"    VARCHAR(2000) NOT NULL,
    "auditResult"    VARCHAR(2000) NOT NULL,
    "isDeleted"      BIT          DEFAULT 0
                                   NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL
);
CREATE TABLE "TB_QA_INTERNALCHECKINFO"
(
    "id"              VARCHAR(50)   NOT NULL,
    "implementPlanId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "checkContent"    VARCHAR(2000) NOT NULL,
    "auditor"         VARCHAR(50)   NOT NULL,
    "checkResult"     INT          DEFAULT 0
                                    NOT NULL,
    "remark"          VARCHAR(2000) NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "isDeleted"       BIT          DEFAULT 0
                                    NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL
);
CREATE TABLE "TB_QA_LOG"
(
    "id"               VARCHAR(50)  NOT NULL,
    "operatorId"       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "operatorName"     VARCHAR(50) NULL,
    "operateTime"      TIMESTAMP(0) NOT NULL,
    "operateInfo"      VARCHAR(50)  NOT NULL,
    "nextOperatorId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "nextOperatorName" VARCHAR(50) NULL,
    "logType"          VARCHAR(50)  NOT NULL,
    "objectId"         VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "objectType"       VARCHAR(50)  NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL
);
CREATE TABLE "TB_QA_MANAGEMENTREVIEWPLAN"
(
    "id"                    VARCHAR(50)   NOT NULL,
    "annualPlanId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "reviewPurp"            VARCHAR(2000) NOT NULL,
    "attendee"              VARCHAR(2000) NOT NULL,
    "reviewContent"         VARCHAR(2000) NOT NULL,
    "reviewPrepareRequired" VARCHAR(2000) NULL,
    "reviewTime"            TIMESTAMP(0)  NOT NULL,
    "reviewAddr"            VARCHAR(255)  NOT NULL,
    "host"                  VARCHAR(50)   NOT NULL,
    "recorder"              VARCHAR(50) NULL,
    "status"                VARCHAR(50)   NOT NULL,
    "isDeleted"             BIT          DEFAULT 0
                                          NOT NULL,
    "orgId"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "creator"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "createDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                          NOT NULL,
    "domainId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "modifier"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "modifyDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                          NOT NULL
);
CREATE TABLE "TB_QA_MONITORINGPLAN"
(
    "id"         VARCHAR(50)  NOT NULL,
    "marker"     VARCHAR(50)  NOT NULL,
    "markDate"   TIMESTAMP(0) NOT NULL,
    "planName"   VARCHAR(255) NULL,
    "status"     VARCHAR(50) NULL,
    "isDeleted"  BIT          DEFAULT 0
                              NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL
);
CREATE TABLE "TB_QA_MONITORINGPLANCHECKINFO"
(
    "id"             VARCHAR(50)   NOT NULL,
    "planDetailId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "checkContent"   VARCHAR(2000) NULL,
    "checkResult"    VARCHAR(2000) NOT NULL,
    "checkDate"      TIMESTAMP(0)  NOT NULL,
    "checker"        VARCHAR(50)   NOT NULL,
    "checkName"      VARCHAR(255) NULL,
    "dutyPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "dutyPersonName" VARCHAR(50) NULL,
    "dutyDept"       VARCHAR(255) NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL
);
CREATE TABLE "TB_QA_MONITORINGPLANDETAIL"
(
    "id"           VARCHAR(50) NOT NULL,
    "planId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "dutyPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "dutyDept"     VARCHAR(255) NULL,
    "status"       VARCHAR(50) NOT NULL,
    "content"      VARCHAR(2000) NULL,
    "isDeleted"    BIT          DEFAULT 0
                               NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL
);
CREATE TABLE "TB_QA_NOTCONFORMITEM"
(
    "id"               VARCHAR(50)  NOT NULL,
    "sourceId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "ncProduceDept"    VARCHAR(50)  NOT NULL,
    "ncDeptPerson"     VARCHAR(50) NULL,
    "ncDeptPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "ncDescribe"       VARCHAR(2000) NULL,
    "ncSourceType"     VARCHAR(50) NULL,
    "ncMainPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "ncMainPerson"     VARCHAR(50) NULL,
    "ncFindPerson"     VARCHAR(50) NULL,
    "ncFindPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "ncFindDate"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "ncBasis"          VARCHAR(500) NOT NULL,
    "ncType"           VARCHAR(50) NULL,
    "ncCauseAnalysis"  VARCHAR(500) NULL,
    "ncElement"        VARCHAR(500) NULL,
    "ncNature"         VARCHAR(500) NULL,
    "ncItermNum"       VARCHAR(1000) NULL,
    "ncIterm"          VARCHAR(2000) NULL,
    "correctMeasures"  VARCHAR(2000) NULL,
    "expectFinishDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "finishDate"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "complete"         VARCHAR(2000) NULL,
    "effective"        INT          DEFAULT (-1)
                                    NOT NULL,
    "verifierEvaluate" VARCHAR(2000) NULL,
    "potential"        INT          DEFAULT 0
                                    NOT NULL,
    "restoreWork"      INT          DEFAULT 0
                                    NOT NULL,
    "notifyCustomer"   INT          DEFAULT 0
                                    NOT NULL,
    "correcteAction"   INT          DEFAULT 0
                                    NOT NULL,
    "status"           VARCHAR(50) NULL,
    "isDeleted"        BIT          DEFAULT 0
        NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "verifierDate"     TIMESTAMP(0) NULL
);
CREATE TABLE "TB_QA_REVIEWPLANREPORT"
(
    "id"             VARCHAR(50)   NOT NULL,
    "reviewPlanId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "makerId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "makerName"      VARCHAR(50) NULL,
    "makeDate"       TIMESTAMP(0)  NOT NULL,
    "reviewPurp"     VARCHAR(2000) NOT NULL,
    "reviewContent"  VARCHAR(2000) NOT NULL,
    "reviewGist"     VARCHAR(2000) NOT NULL,
    "reviewExpound"  VARCHAR(2000) NOT NULL,
    "reviewDecision" VARCHAR(2000) NOT NULL,
    "isDeleted"      BIT          DEFAULT 0
                                   NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL
);
CREATE TABLE "TB_QA_RISKANDACCIDENT"
(
    "id"               VARCHAR(50)  NOT NULL,
    "discoverDate"     TIMESTAMP(0) NOT NULL,
    "dutyDomainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "sourceType"       VARCHAR(50) NULL,
    "possibility"      INT          DEFAULT 0
                                    NOT NULL,
    "seriousness"      INT          DEFAULT 0
                                    NOT NULL,
    "directorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "dutyPersonId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "finderId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "measure"          VARCHAR(500) NULL,
    "studyOutPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "auditor"          VARCHAR(50) NULL,
    "affirmPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "achieveDate"      TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "performance"      VARCHAR(500) NULL,
    "affirmDate"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "affirmEvaluate"   VARCHAR(500) NULL,
    "findDate"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "coefficient"      VARCHAR(50) NULL,
    "description"      VARCHAR(500) NULL,
    "reason"           VARCHAR(500) NULL,
    "status"           VARCHAR(50)  NOT NULL,
    "isDeleted"        BIT          DEFAULT 0
                                    NOT NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL
);
CREATE TABLE "TB_QA_SUBMITRECORD"
(
    "id"               VARCHAR(50) NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "submitType"       INT          DEFAULT 0
                                   NOT NULL,
    "submitTime"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "submitPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "submitPersonName" VARCHAR(50) NULL,
    "nextPerson"       VARCHAR(200) NULL,
    "submitRemark"     VARCHAR(200) NULL,
    "stateFrom"        VARCHAR(50) NULL,
    "stateTo"          VARCHAR(50) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "isNewest"         BIT          DEFAULT 0
        NULL
);
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('08c23fb6-6a38-4f5d-80be-a46cc6bbe7f2', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 10:15:51',
        '审核质量监督记录', '00000000-0000-0000-0000-000000000000', '', '质量监督流程',
        '39c729c9-8b7a-4799-baab-1ec55b6fdc61', '质量监督', '超级管理员审核了质量监督记录，审核结果为审核通过。', null,
        '', '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('41d32851-851f-472b-b5cb-f11640eb085d', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 10:12:47',
        '新增质量监督计划', '00000000-0000-0000-0000-000000000000', '', '质量监督流程',
        '39c729c9-8b7a-4799-baab-1ec55b6fdc61', '质量监督', '超级管理员新增了质量监督', '', '',
        '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('42bd61c5-e212-4594-b0a9-e694bf590eff', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 10:14:30',
        '提交质量监督计划明细', '00000000-0000-0000-0000-000000000000', null, '质量监督流程',
        '39c729c9-8b7a-4799-baab-1ec55b6fdc61', '质量监督', '超级管理员提交质量监督执行，下一步操作人：超级管理员', '',
        '', '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('5bbec253-539e-46cf-9c69-4400388de25a', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 08:47:24',
        '编辑不符合项', '00000000-0000-0000-0000-000000000000', '', '不符合项登记流程',
        '81f6c92a-e3a1-4e2b-8e62-3e97f445e696', '不符合项', '超级管理员修改了不符合项', '', '',
        '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('5d1d4f02-c1b6-49c3-8a53-2fd55c3b0798', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 10:13:16',
        '提交质量监督计划', '00000000-0000-0000-0000-000000000000', '', '质量监督流程',
        '39c729c9-8b7a-4799-baab-1ec55b6fdc61', '质量监督', '超级管理员提交了质量监督计划。下一步操作人：超级管理员', '',
        '', '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('70828514-dd1e-45b9-8b3b-985845885d91', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 08:48:51',
        '新增风险机遇', '00000000-0000-0000-0000-000000000000', '', '风险机遇流程',
        '6fbe9c09-18c7-4392-92a2-31b1f4881eaf', '风险机遇', '超级管理员新增了风险机遇信息', '', '',
        '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('8df3963c-aae4-4f01-af90-11e4a7c80431', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 10:13:24',
        '审核质量监督计划', '00000000-0000-0000-0000-000000000000', '', '质量监督流程',
        '39c729c9-8b7a-4799-baab-1ec55b6fdc61', '质量监督',
        '超级管理员审核了质量监督计划，审核结果为：审核通过。下一步操作人：超级管理员', '', '',
        '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('a3664ec4-cbd2-433d-b5f6-2d877046439b', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 08:48:56',
        '提交风险机遇', '00000000-0000-0000-0000-000000000000', '59141356591b48e18e139aa54d9dd351', '风险机遇流程',
        '6fbe9c09-18c7-4392-92a2-31b1f4881eaf', '风险机遇', '超级管理员提交了风险机遇信息，下一步操作人：超级管理员', '',
        '', '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('a6cf2b45-cfb6-4e6d-95fc-3d7f0a1e5d92', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 08:47:27',
        '提交不符合项', '00000000-0000-0000-0000-000000000000', null, '不符合项登记流程',
        '81f6c92a-e3a1-4e2b-8e62-3e97f445e696', '不符合项管理', '超级管理员提交了不符合项，下一步操作人：超级管理员', '',
        '', '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('acf44a03-cf6d-4822-87fd-b4632335da81', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 08:47:22',
        '新增不符合项', '00000000-0000-0000-0000-000000000000', '', '不符合项登记流程',
        '81f6c92a-e3a1-4e2b-8e62-3e97f445e696', '不符合项', '超级管理员新增不符合项', '', '',
        '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('cabb7fab-1c7f-4fa9-bbf5-32fd7cb53034', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 10:13:10',
        '新增计划明细', '00000000-0000-0000-0000-000000000000', null, '质量监督流程',
        '39c729c9-8b7a-4799-baab-1ec55b6fdc61', '质量监督', '超级管理员新增计划明细信息，责任人：超级管理员', '', '',
        '5f7bcf90feb545968424b0a872863876');
INSERT INTO "TB_QA_LOG"("id", "operatorId", "operatorName", "operateTime", "operateInfo", "nextOperatorId",
                        "nextOperatorName", "logType", "objectId", "objectType", "comment", "opinion", "remark",
                        "orgId")
VALUES ('f7238e7b-f569-4c9e-9f93-4e2d666bb1e8', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-06-07 10:14:25',
        '新增检查项', '00000000-0000-0000-0000-000000000000', null, '质量监督流程',
        '39c729c9-8b7a-4799-baab-1ec55b6fdc61', '管理评审',
        '超级管理员新增检查项信息，检查结果：上半年随意挑选一天，对其工作进行监督', '', '',
        '5f7bcf90feb545968424b0a872863876');

INSERT INTO "TB_QA_MONITORINGPLAN"("id", "marker", "markDate", "planName", "status", "isDeleted", "orgId", "creator",
                                   "createDate", "domainId", "modifier", "modifyDate")
VALUES ('39c729c9-8b7a-4799-baab-1ec55b6fdc61', '59141356591b48e18e139aa54d9dd351', '2024-06-07 00:00:00',
        '质量监督计划', '质量监督完毕', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-06-07 10:12:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-06-07 10:15:51');

INSERT INTO "TB_QA_MONITORINGPLANCHECKINFO"("id", "planDetailId", "checkContent", "checkResult", "checkDate", "checker",
                                            "checkName", "dutyPersonId", "dutyPersonName", "dutyDept", "orgId",
                                            "creator", "createDate", "domainId", "modifier", "modifyDate")
VALUES ('3ebf356d-a88c-49ab-b612-a62f341d5079', 'f23b2c36-46f6-4cab-9b2c-ce5f9b1af78c',
        '上半年随意挑选一天，对其工作进行监督', '上半年随意挑选一天，对其工作进行监督', '2024-06-07 00:00:00',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '59141356591b48e18e139aa54d9dd351', '超级管理员',
        '20f8bafbcc1a49839e216bd2027dab35', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-06-07 10:14:25', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-06-07 10:14:25');

INSERT INTO "TB_QA_MONITORINGPLANDETAIL"("id", "planId", "dutyPersonId", "dutyDept", "status", "content", "isDeleted",
                                         "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate")
VALUES ('f23b2c36-46f6-4cab-9b2c-ce5f9b1af78c', '39c729c9-8b7a-4799-baab-1ec55b6fdc61',
        '59141356591b48e18e139aa54d9dd351', '20f8bafbcc1a49839e216bd2027dab35', '执行完毕', '安全防护及环境保护', 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:13:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:14:30');

INSERT INTO "TB_QA_NOTCONFORMITEM"("id", "sourceId", "ncProduceDept", "ncDeptPerson", "ncDeptPersonId", "ncDescribe",
                                   "ncSourceType", "ncMainPersonId", "ncMainPerson", "ncFindPerson", "ncFindPersonId",
                                   "ncFindDate", "ncBasis", "ncType", "ncCauseAnalysis", "ncElement", "ncNature",
                                   "ncItermNum", "ncIterm", "correctMeasures", "expectFinishDate", "finishDate",
                                   "complete", "effective", "verifierEvaluate", "potential", "restoreWork",
                                   "notifyCustomer", "correcteAction", "status", "isDeleted", "orgId", "creator",
                                   "createDate", "domainId", "modifier", "modifyDate", "verifierDate")
VALUES ('81f6c92a-e3a1-4e2b-8e62-3e97f445e696', 'LIM_NotConformItemSource_Impl', '48aa175e5bf14d78b86236661950ced0', '',
        '00000000-0000-0000-0000-000000000000', '不符合项', 'LIM_NotConformItemSourceType_Supervise',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '超级管理员', '59141356591b48e18e139aa54d9dd351',
        '2024-06-07 00:00:00', '依据', 'LIM_NotConformItemType_General', '', '', '', '', '', '', '1753-01-01 00:00:00',
        '1753-01-01 00:00:00', '', -1, '', 0, 0, 0, 0, '不符合项措施拟定中', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:47:21', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:47:27', '1753-01-01 00:00:00');

INSERT INTO "TB_QA_RISKANDACCIDENT"("id", "discoverDate", "dutyDomainId", "sourceType", "possibility", "seriousness",
                                    "directorId", "dutyPersonId", "finderId", "measure", "studyOutPersonId", "auditor",
                                    "affirmPersonId", "achieveDate", "performance", "affirmDate", "affirmEvaluate",
                                    "findDate", "coefficient", "description", "reason", "status", "isDeleted", "orgId",
                                    "creator", "createDate", "domainId", "modifier", "modifyDate")
VALUES ('6fbe9c09-18c7-4392-92a2-31b1f4881eaf', '2024-06-07 00:00:00', '20f8bafbcc1a49839e216bd2027dab35',
        'LIM_NotConformItemSourceType_Review', 3, 3, '59141356591b48e18e139aa54d9dd351',
        '46905a56-f770-42be-8d41-5b6c94f0ed52', '59141356591b48e18e139aa54d9dd351', '', '', '', '',
        '1753-01-01 00:00:00', '', '1753-01-01 00:00:00', '', '1753-01-01 00:00:00', '9', '123', '', '措施拟定中', 0,
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:48:51',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:48:56');

INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('2dcba9e9-e9f6-47a2-b9ab-06003597d208', '39c729c9-8b7a-4799-baab-1ec55b6fdc61', 4, 16, '2024-06-07 10:15:51',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '59141356591b48e18e139aa54d9dd351', null, '质量监督中',
        '质量监督完毕', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:15:51',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:15:51', 1);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('3edf3edd-c3c6-4535-a544-37600999f86f', '39c729c9-8b7a-4799-baab-1ec55b6fdc61', 4, 43, '2024-06-07 10:12:47',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, '计划编制中', '计划编制中',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:12:47',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:13:16', 0);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('3f85cdd8-0929-4d12-b162-9c495d76fe69', '39c729c9-8b7a-4799-baab-1ec55b6fdc61', 4, 13, '2024-06-07 10:13:16',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '59141356591b48e18e139aa54d9dd351', null, '计划评审中',
        '计划评审中', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:13:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:13:24', 0);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('4fcb88d6-4246-47ed-a5da-3c2cb5fc02b1', '6fbe9c09-18c7-4392-92a2-31b1f4881eaf', 7, 30, '2024-06-07 08:48:56',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '59141356591b48e18e139aa54d9dd351', null, '登记中',
        '措施拟定中', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:48:56',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:48:56', 1);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('53962716-3a4b-45fb-89b3-cc2de9a1ff22', '81f6c92a-e3a1-4e2b-8e62-3e97f445e696', 6, 37, '2024-06-07 08:47:22',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, '不符合项登记中', '不符合项登记中',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:47:22',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:47:27', 0);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('75f10378-af56-46fb-9538-8f40d9aa4dbf', 'f23b2c36-46f6-4cab-9b2c-ce5f9b1af78c', 4, 15, '2024-06-07 10:13:24',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '59141356591b48e18e139aa54d9dd351', '', '计划明细编制中',
        '执行中', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:13:24',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:14:30', 0);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('8b1ebad0-ea9a-4fb8-912e-e487a1b6dcc2', 'f23b2c36-46f6-4cab-9b2c-ce5f9b1af78c', 4, 43, '2024-06-07 10:13:10',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, '计划明细编制中', '计划明细编制中',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:13:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:13:24', 0);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('a941c007-f65d-49b4-8e2f-1905ea474b4a', '6fbe9c09-18c7-4392-92a2-31b1f4881eaf', 7, 29, '2024-06-07 08:48:51',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, '登记中', '登记中',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:48:51',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 08:48:56', 0);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('e1b3aec5-c491-405d-ae9a-2c4cb6206286', '39c729c9-8b7a-4799-baab-1ec55b6fdc61', 4, 14, '2024-06-07 10:14:30',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '59141356591b48e18e139aa54d9dd351', null, '计划评审中',
        '质量监督中', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:14:30',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:15:51', 0);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('e60a93f9-520b-46c8-9ad8-05557d4bb41f', '81f6c92a-e3a1-4e2b-8e62-3e97f445e696', 6, 23, '2024-06-07 08:47:27',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '59141356591b48e18e139aa54d9dd351', null, '不符合项登记中',
        '不符合项措施拟定中', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-06-07 08:47:27', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-06-07 08:47:27', 1);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('ef585e60-ef03-4d34-9cd4-5e7a48541ab2', 'f23b2c36-46f6-4cab-9b2c-ce5f9b1af78c', 4, 15, '2024-06-07 10:14:30',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, '执行完毕', '执行完毕',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:14:30',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:14:30', 1);
INSERT INTO "TB_QA_SUBMITRECORD"("id", "objectId", "objectType", "submitType", "submitTime", "submitPersonId",
                                 "submitPersonName", "nextPerson", "submitRemark", "stateFrom", "stateTo", "orgId",
                                 "creator", "createDate", "domainId", "modifier", "modifyDate", "isNewest")
VALUES ('fb0739c8-03e6-4504-9a94-fde49482bf24', '39c729c9-8b7a-4799-baab-1ec55b6fdc61', 4, 14, '2024-06-07 10:13:24',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '59141356591b48e18e139aa54d9dd351', '', '计划执行中',
        '计划执行中', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:13:24',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-06-07 10:14:30', 0);

ALTER TABLE "TB_QA_ANNUALPLAN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_CUSTOMERCOMPLAINTREGIST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_INTERNALAUDITIMPLEMENTPLAN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_INTERNALAUDITPLAN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_INTERNALAUDITPLANREPORT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_INTERNALCHECKINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_LOG"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_MANAGEMENTREVIEWPLAN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_MONITORINGPLAN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_MONITORINGPLANCHECKINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_MONITORINGPLANDETAIL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_NOTCONFORMITEM"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_REVIEWPLANREPORT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_RISKANDACCIDENT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QA_SUBMITRECORD"
    ADD CONSTRAINT PRIMARY KEY ("id");

COMMENT
ON TABLE "TB_QA_ANNUALPLAN" IS '年度计划表';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."year" IS '年度';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."planType" IS '年度计划类型';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."makePersonId" IS '制定人Id';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."makePersonName" IS '制定人名称';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."makeTime" IS '制定日期';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."auditContent" IS '审核内容';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."auditPurpose" IS '审核目的';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."remark" IS '备注';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."status" IS '状态：编制中、审核中、审核通过、审核不通过';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_ANNUALPLAN"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."complaintName" IS '投诉方名称';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."entId" IS '投诉企业';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."complaintPerson" IS '投诉人员';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."complaintPersonId" IS '投诉人员Id';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."problemDescription" IS '投诉问题描述';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."phone" IS '电话';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."email" IS '邮箱地址';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."type" IS '投诉方式（常量）';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."complaintDate" IS '投诉日期';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."registPerson" IS '登记人员';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."registPersonId" IS '登记人员Id';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."registDate" IS '登记日期';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."level" IS '投诉级别';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."finshDate" IS '要求完成日期';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."opinion" IS '成立意见';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."status" IS '状态';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_CUSTOMERCOMPLAINTREGIST"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QA_INTERNALAUDITIMPLEMENTPLAN" IS '内审实施计划';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."auditTime" IS '审核时间';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."auditedDept" IS '审核部门';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."personInCharge" IS '责任人';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."auditor" IS '审核人员';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."auditElement" IS '审核要素';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITIMPLEMENTPLAN"."auditedDeptId" IS '审核部门id';

COMMENT
ON TABLE "TB_QA_INTERNALAUDITPLAN" IS '内审计划信息';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."annualPlanId" IS '年度计划id';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."auditTime" IS '审核时间';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."attendee" IS '审核参与人员';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."auditPurp" IS '审核目的';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."auditScope" IS '审核范围';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."auditContent" IS '审核内容';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."auditGist" IS '审核依据';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."status" IS '状态';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLAN"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QA_INTERNALAUDITPLANREPORT" IS '内审报告信息';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."internalPlanId" IS '内审计划id';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."makerId" IS '编制人id';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."makerName" IS '编制人名字';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."makerTime" IS '编制日期';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."auditReview" IS '审核综述';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."auditResult" IS '审核结论';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_INTERNALAUDITPLANREPORT"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QA_INTERNALCHECKINFO" IS '检查项的信息';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."implementPlanId" IS '内审的实施计划id';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."checkContent" IS '检查项的内容';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."auditor" IS '审核人员';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."checkResult" IS '检查结果（1：符合、2：基本符合、3：不符合、4：不适用）';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."remark" IS '备注信息';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_INTERNALCHECKINFO"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QA_LOG" IS '年度计划日志表';

COMMENT
ON COLUMN "TB_QA_LOG"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_LOG"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_QA_LOG"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_QA_LOG"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_QA_LOG"."operateInfo" IS '操作类型(EnumOperationInfo, 1: 新增， 2: 审核)';

COMMENT
ON COLUMN "TB_QA_LOG"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_QA_LOG"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_QA_LOG"."logType" IS '日志类型';

COMMENT
ON COLUMN "TB_QA_LOG"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_QA_LOG"."objectType" IS '对象类型';

COMMENT
ON COLUMN "TB_QA_LOG"."comment" IS '说明';

COMMENT
ON COLUMN "TB_QA_LOG"."opinion" IS '意见';

COMMENT
ON COLUMN "TB_QA_LOG"."remark" IS '备注';

COMMENT
ON COLUMN "TB_QA_LOG"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_QA_MANAGEMENTREVIEWPLAN" IS '管理评审';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."annualPlanId" IS '年度计划id';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."reviewPurp" IS '评审目的';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."attendee" IS '参加人员';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."reviewContent" IS '评审内容';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."reviewPrepareRequired" IS '评审准备工作要求(评审主要议程)';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."reviewTime" IS '评审时间';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."reviewAddr" IS '评审地点';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."host" IS '主持人';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."recorder" IS '记录人';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."status" IS '评审状态';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_MANAGEMENTREVIEWPLAN"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QA_MONITORINGPLAN" IS '质量监督计划';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."id" IS '主键id';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."marker" IS '制定人';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."markDate" IS '制定日期';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."planName" IS '计划名称';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."status" IS '计划状态（计划编制中、审核不通过、计划审核中、质量监督中、已办结）';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLAN"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QA_MONITORINGPLANCHECKINFO" IS '质量计划的检查项';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."planDetailId" IS '质量监督明细';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."checkContent" IS '检查项的内容';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."checkResult" IS '检查结果';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."checkDate" IS '检查时间';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."checker" IS '检查人员Id';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."checkName" IS '检查人员Name';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."dutyPersonId" IS '责任人';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."dutyPersonName" IS '责任人名称';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."dutyDept" IS '责任部门';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."domainId" IS '实验室id';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANCHECKINFO"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QA_MONITORINGPLANDETAIL" IS '质量监督明细';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."id" IS '主键id';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."planId" IS '计划id';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."dutyPersonId" IS '责任人';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."dutyDept" IS '责任部门';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."status" IS '状态 （0：未提交，2：已提交）';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."content" IS '监督内容';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_MONITORINGPLANDETAIL"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."id" IS 'id';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."sourceId" IS '来源id（常量（GUID）：QA_Source）';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncProduceDept" IS '不符合项责任科室';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncDeptPerson" IS '不符合项科室主任';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncDeptPersonId" IS '不符合项科室主任Id';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncDescribe" IS '不符合项描述';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncSourceType" IS '来源类型（常量（GUID）：QA_SourceType）';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncMainPersonId" IS '不符合项主要负责人Id';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncMainPerson" IS '不符合项主要负责人';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncFindPerson" IS '不符合项发现人员';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncFindPersonId" IS '不符合项发现人员Id';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncFindDate" IS '不符合项发现日期';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncBasis" IS '不符合项发现依据';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncType" IS '不符合项类型';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncCauseAnalysis" IS '不符合项原因分析';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncElement" IS '不符合要素';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncNature" IS '不符合项性质';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncItermNum" IS '不符合项涉及条款号';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."ncIterm" IS '不符合项涉及条款内容';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."correctMeasures" IS '拟采取的纠正措施';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."expectFinishDate" IS '预计完成日期';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."finishDate" IS '纠正措施完成日期';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."complete" IS '确认纠正措施完成情况';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."effective" IS '验证纠正措施是否有效  0：无效，1：有效 ，2：未验证';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."verifierEvaluate" IS '验证及评价';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."potential" IS '是否是潜在问题（0：不符合项，1：潜在不符合项及预防措施）';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."restoreWork" IS '可否恢复工作';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."notifyCustomer" IS '是否通知客户';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."correcteAction" IS '是否需要采取纠正措施';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."status" IS '状态';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_QA_NOTCONFORMITEM"."verifierDate" IS '验证时间';

COMMENT
ON TABLE "TB_QA_REVIEWPLANREPORT" IS '管理评审报告';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."reviewPlanId" IS '管理评审id';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."makerId" IS '编制报告人id';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."makerName" IS '编制报告人名字';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."makeDate" IS '报告编制日期';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."reviewPurp" IS '评审目的';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."reviewContent" IS '评审内容';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."reviewGist" IS '评审依据';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."reviewExpound" IS '评审阐述';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."reviewDecision" IS '评审决议';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_REVIEWPLANREPORT"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QA_RISKANDACCIDENT" IS '风险与机遇';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."discoverDate" IS '发现日期';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."dutyDomainId" IS '责任科室';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."sourceType" IS '来源类型（常量：实验室内审、管理评审、质量监督、客户投诉、其他）';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."possibility" IS '风险机遇可能性（枚举）';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."seriousness" IS '风险机遇严重性（枚举）';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."directorId" IS '科室主任';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."dutyPersonId" IS '责任人';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."finderId" IS '发现人员';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."measure" IS '措施拟定';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."studyOutPersonId" IS '拟定人';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."auditor" IS '审核人';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."affirmPersonId" IS '确认人';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."achieveDate" IS '完成日期';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."performance" IS '完成情况';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."affirmDate" IS '确认日期';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."affirmEvaluate" IS '确认及评价';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."findDate" IS '发现日期';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."coefficient" IS '风险机遇系数';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."description" IS '风险机遇描述';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."reason" IS '原因分析';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."status" IS '评审状态';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_RISKANDACCIDENT"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."id" IS '主键';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."objectId" IS '关联Id';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."objectType" IS '关联对象类型（枚举EnumQAObjType：1. 年度计划....）';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."submitType" IS '操作类型（枚举EnumQASubmitType：0.无 1.年度计划提交 ....）';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."submitTime" IS '操作时间';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."submitPersonId" IS '操作人';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."submitPersonName" IS '操作人名称';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."nextPerson" IS '下一步操作人（名字）';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."submitRemark" IS '操作意见';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."stateFrom" IS '操作前状态';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."stateTo" IS '操作后状态';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QA_SUBMITRECORD"."modifyDate" IS '修改时间';

