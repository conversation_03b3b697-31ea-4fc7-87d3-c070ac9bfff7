package com.sinoyd.lims.foreign.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataReportBaseInfoVO;
import com.sinoyd.lims.foreign.service.DataReportBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 报告基本信息服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataReportBaseInfo服务")
@RestController
@RequestMapping("api/pro/foreign/dataReportBaseInfo")
public class DataReportBaseInfoController extends ExceptionHandlerController<DataReportBaseInfoService> {


    /**
     * 数据同步
     *
     * @param map 同步参数
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataReportBaseInfoVO>> getSyncData(@RequestBody Map<String, Object> map) {
        List<DataReportBaseInfoVO> curveVOList = service.getSyncData(map);
        RestResponse<List<DataReportBaseInfoVO>> response = new RestResponse();
        response.setData(curveVOList);
        return response;
    }
}
