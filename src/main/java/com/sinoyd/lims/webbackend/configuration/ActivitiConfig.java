//package com.sinoyd.lims.webbackend.configuration;
//
//import java.io.IOException;
//import java.util.List;
//
//import javax.sql.DataSource;
//
//import com.google.common.collect.Lists;
//import com.sinoyd.boot.workflow.activiti.extend.ActGroupManagerFactory;
//import com.sinoyd.boot.workflow.activiti.extend.ActUserManagerFactory;
//
//import org.activiti.engine.impl.interceptor.SessionFactory;
//import org.activiti.spring.SpringAsyncExecutor;
//import org.activiti.spring.SpringProcessEngineConfiguration;
//import org.activiti.spring.boot.AbstractProcessEngineAutoConfiguration;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//
///**
// * Activiti参数配置
// * <AUTHOR>
// * @version V1.0.0 2019年3月27日
// * @since   V100R001
// */
//@Configuration
//public class ActivitiConfig extends AbstractProcessEngineAutoConfiguration
//{
//    @Qualifier("primaryDataSource")
//    @Autowired
//    private DataSource dataSource;
//
//    @Autowired
//    private ActUserManagerFactory actUserManagerFactory;
//
//    @Autowired
//    private ActGroupManagerFactory actGroupManagerFactory;
//
//    @Bean
//    public SpringProcessEngineConfiguration springProcessEngineConfiguration(SpringAsyncExecutor springAsyncExecutor)
//        throws IOException
//    {
//        // 多数据源情况下指定数据源
//        SpringProcessEngineConfiguration config = super.baseSpringProcessEngineConfiguration(dataSource,
//        new DataSourceTransactionManager(this.dataSource), springAsyncExecutor);
//        String fontName = "宋体";
//        // 设置流程图字体
//        config.setActivityFontName(fontName);
//        config.setAnnotationFontName(fontName);
//        config.setLabelFontName(fontName);
//
//        List<SessionFactory> sessions = Lists.newArrayList();
//        sessions.add(actUserManagerFactory);
//        sessions.add(actGroupManagerFactory);
//
//        // 注入自定义用户权限 ，必须重新实现用户和组的相应方法
//        config.setCustomSessionFactories(sessions);
//
//        // 启用定时相关的功能
//        // config.setJobExecutorActivate(true);
//        return config;
//    }
//}
