package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoCurveTemp;
import com.sinoyd.lims.lim.dto.lims.DtoCurve;
import com.sinoyd.lims.lim.dto.lims.DtoCurveDetail;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.CurveDetailRepository;
import com.sinoyd.lims.lim.repository.lims.CurveRepository;
import com.sinoyd.lims.lim.service.CurveDetailService;
import com.sinoyd.lims.lim.service.CurveService;
import com.sinoyd.lims.lim.service.ReviseService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * Curve操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/5
 * @since V100R001
 */
@Service
public class CurveServiceImpl extends BaseJpaServiceImpl<DtoCurve, String, CurveRepository> implements CurveService {

    @Autowired
    @Lazy
    private CurveDetailService curveDetailService;

    @Autowired
    private CurveDetailRepository curveDetailRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    private RedisTemplate redisTemplate;

    private ReviseService reviseService;
    /**
     * 正则表达式,是否数值判断
     */
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");

    @Override
    public void findByPage(PageBean<DtoCurve> pb, BaseCriteria curveCriteria) {
        pb.setEntityName("DtoCurve a,DtoTest t,DtoSampleType st");
        pb.setSelect("select a,t.redAnalyzeItemName,t.redAnalyzeMethodName,t.redCountryStandard,st.typeName");
        comRepository.findByPage(pb, curveCriteria);

        List<DtoCurve> datas = pb.getData();
        List<DtoCurve> newDatas = new ArrayList<>();

        Iterator<DtoCurve> ite = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoCurve curve = (DtoCurve) objs[0];
            curve.setRedAnalyzeItemName((String) objs[1]);
            curve.setRedAnalyzeMethodName((String) objs[2]);
            curve.setRedCountryStandard((String) objs[3]);
            curve.setSampleTypeName((String) objs[4]);
            Calendar c = Calendar.getInstance();
            c.setTime(curve.getConfigDate());
            c.add(Calendar.DAY_OF_MONTH, curve.getPeriod());
            curve.setExpireDate(c.getTime());
            curve.setEquation(this.getEquation(curve));
            newDatas.add(curve);
        }

        pb.setData(newDatas);
    }

    @Override
    public List<DtoCurve> findByTestIds(List<String> testIds) {
        if (StringUtil.isNotNull(testIds) && testIds.size() > 0) {
            return repository.findByTestIdIn(testIds);
        }
        return new ArrayList<>();
    }

    /**
     * 获取标线信息
     *
     * @param id 标准曲线id
     * @return 标线信息
     */
    @Override
    public DtoCurveTemp findDetail(String id) {
        DtoCurveTemp temp = new DtoCurveTemp();
        DtoCurve curve = repository.findOne(id);
        BeanUtils.copyProperties(curve, temp);
        DtoTest test = testService.findOne(curve.getTestId());
        temp.setKValueFormat(test.getKValueFormat());
        temp.setKDecimalFormat(test.getKDecimalFormat());
        temp.setBValueFormat(test.getBValueFormat());
        temp.setBDecimalFormat(test.getBDecimalFormat());
        temp.setCValueFormat(test.getCValueFormat());
        temp.setCDecimalFormat(test.getCDecimalFormat());
        temp.setTestName(String.format("%s %s %s", test.getRedAnalyzeItemName(), test.getRedAnalyzeMethodName(), test.getRedCountryStandard()));
        temp.setEquation(this.getEquation(curve));
        List<DtoCurveDetail> detailList = curveDetailRepository.findByCurveIdOrderByOrderNumDesc(id);
        temp.setCurveDetail(detailList.stream().filter(v -> Boolean.FALSE.equals(v.getIsDimensionRow())).collect(Collectors.toList()));
        temp.setDimensionRow(detailList.stream().filter(v -> Boolean.TRUE.equals(v.getIsDimensionRow())).findFirst().orElse(null));
        return temp;
    }

    @Override
    public List<DtoCurveTemp> findList(String id) {
        List<DtoCurveTemp> tempList = new ArrayList<>();
        DtoCurveTemp temp = this.findDetail(id);
        tempList.add(temp);
        if (!UUIDHelper.GUID_EMPTY.equals(temp.getRelevanceId())) {
            DtoCurveTemp anceTemp = this.findDetail(temp.getRelevanceId());
            tempList.add(anceTemp);
        }
        return tempList;
    }

    /**
     * 删除标线，级联删除明细
     */
    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        curveDetailRepository.deleteByCurveId(idStr);
        return super.logicDeleteById(id);
    }

    /**
     * 批量删除标线，级联删除明细
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> curveIds = new ArrayList<>();
        for (Object id : ids) {
            curveIds.add(String.valueOf(id));
        }
        curveDetailRepository.deleteByCurveIdIn(curveIds);
        return super.logicDeleteById(ids);
    }

    /**
     * 新增标线
     *
     * @param temp        标准曲线信息
     * @param isCalculate 是否计算
     */
    @Transactional
    @Override
    public DtoCurveTemp save(DtoCurveTemp temp, Boolean isCalculate) {
        DtoCurve curve = new DtoCurve();
        curve.loadFromTemp(temp);
        if (isCalculate) {
            curveDetailService.calculation(temp.getCurveDetail(), curve);
        }
        BeanUtils.copyProperties(curve, temp);
        super.save(curve);
        curve.setEquation(this.getEquation(curve));
        this.checkTest(curve);
        curveDetailRepository.deleteByCurveId(temp.getId());
        List<DtoCurveDetail> details = temp.getCurveDetail();
        setCurveDetail(temp, details);
        temp.setCurveDetail(details);
        curveDetailService.save(details);
        return temp;
    }

    /**
     * 批量保存标线
     *
     * @param tempList 标准曲线信息
     * @return 曲线
     */
    @Transactional
    @Override
    public List<DtoCurveTemp> saveList(List<DtoCurveTemp> tempList) {
        if (tempList.size() > 1) {
            DtoCurveTemp tempOne = tempList.get(0);
            DtoCurveTemp tempTwo = tempList.get(1);
            tempOne.setRelevanceId(tempTwo.getId());
            tempTwo.setRelevanceId(tempOne.getId());
        }
        tempList.forEach(temp -> {
            this.save(temp, Boolean.FALSE);
        });
        return tempList;
    }

    /**
     * 复制曲线
     *
     * @param id 需要复制的曲线id
     */
    @Transactional
    @Override
    public DtoCurveTemp copy(String id) {
        //获取当前登录人
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        //获取被复制的曲线
        DtoCurve originalCurve = repository.findOne(id);
        //新曲线
        DtoCurve copyCurve = new DtoCurve();
        //复制参数(相关系数、零点、截距、斜率、实数不复制)
        copyCurve.setTestId(originalCurve.getTestId());
        copyCurve.setConfigPersonId(user.getUserId());
        copyCurve.setConfigName(user.getUserName());
        copyCurve.setKRange(originalCurve.getKRange());
        copyCurve.setBRange(originalCurve.getBRange());
        copyCurve.setCoefficientRange(originalCurve.getCoefficientRange());
        copyCurve.setCoefficient(originalCurve.getCoefficient());
        copyCurve.setKValue(originalCurve.getKValue());
        copyCurve.setBValue(originalCurve.getBValue());
        copyCurve.setConfigDate(originalCurve.getConfigDate());
        copyCurve.setPeriod(originalCurve.getPeriod());
        copyCurve.setIsDouble(originalCurve.getIsDouble());
        copyCurve.setCurveType(originalCurve.getCurveType());
        copyCurve.setCurveMode(originalCurve.getCurveMode());
        copyCurve.setRelevanceId(originalCurve.getRelevanceId());
        copyCurve.setBlankAvgDimensionId(originalCurve.getBlankAvgDimensionId());
        super.save(copyCurve);
        //获取被复制曲线的曲线明细
        List<DtoCurveDetail> curveDetailsOfOriginalCurve = curveDetailRepository.findByCurveIdOrderByOrderNumDesc(id);
        //新曲线明细容器
        List<DtoCurveDetail> curveDetailsOfCopyCurve = new ArrayList<>();
        DtoCurveTemp curveTemp = new DtoCurveTemp();
        //复制曲线明细(仅复制排序值，标准溶液体积，X值)
        for (DtoCurveDetail dtoCurveDetail : curveDetailsOfOriginalCurve) {
            DtoCurveDetail curveDetailOfCopyCurve = new DtoCurveDetail();
            if (dtoCurveDetail.getIsDimensionRow()) {
                //量纲行数据全部复制
                BeanUtils.copyProperties(dtoCurveDetail, curveDetailOfCopyCurve, "id", "creator", "createDate", "modifier", "modifyDate", "orgId", "domainId");
            } else {
                curveDetailOfCopyCurve.setCurveId(copyCurve.getId());
                curveDetailOfCopyCurve.setOrderNum(dtoCurveDetail.getOrderNum());
                curveDetailOfCopyCurve.setVValue(dtoCurveDetail.getVValue());
                curveDetailOfCopyCurve.setXValue(dtoCurveDetail.getXValue());
            }
            curveDetailsOfCopyCurve.add(curveDetailOfCopyCurve);
        }
        if (StringUtil.isNotEmpty(curveDetailsOfCopyCurve)) {
            curveDetailRepository.save(curveDetailsOfCopyCurve);
            curveTemp.setCurveDetail(curveDetailsOfCopyCurve);
        }
        BeanUtils.copyProperties(copyCurve, curveTemp);
        DtoTest test = testService.findOne(originalCurve.getTestId());
        curveTemp.setTestName(test.getTestName());
        curveTemp.setKDecimalFormat(test.getKDecimalFormat());
        curveTemp.setKValueFormat(test.getKValueFormat());
        curveTemp.setBDecimalFormat(test.getBDecimalFormat());
        curveTemp.setBValueFormat(test.getBValueFormat());
        curveTemp.setCDecimalFormat(test.getCDecimalFormat());
        curveTemp.setCValueFormat(test.getCValueFormat());
        return curveTemp;
    }

    /**
     * 复制曲线
     *
     * @param id 需要复制的曲线id
     * @return 曲线
     */
    @Transactional
    @Override
    public List<DtoCurveTemp> copyList(String id) {
        List<DtoCurveTemp> tempList = new ArrayList<>();
        //获取被复制的曲线
        DtoCurve curve = repository.findOne(id);
        tempList.add(this.copy(id));
        if (!UUIDHelper.GUID_EMPTY.equals(curve.getRelevanceId())) {
            tempList.add(this.copy(curve.getRelevanceId()));
        }
        return tempList;
    }

    /**
     * 修改标线
     *
     * @param temp        标准曲线信息
     * @param isCalculate 是否计算
     */
    @Transactional
    @Override
    public DtoCurveTemp update(DtoCurveTemp temp, Boolean isCalculate) {
        DtoCurve curve = super.findOne(temp.getId());
        curve.loadFromTemp(temp);
        if (isCalculate) {
            curveDetailService.calculation(temp.getCurveDetail(), curve);
            temp.setKValue(curve.getKValue());
            temp.setBValue(curve.getBValue());
            temp.setCValue(curve.getCValue());
            temp.setCoefficient(curve.getCoefficient());
        }
        super.update(curve);
        curve.setEquation(this.getEquation(curve));
        this.checkTest(curve);
        curveDetailRepository.deleteByCurveId(temp.getId());
        List<DtoCurveDetail> details = temp.getCurveDetail();
        setCurveDetail(temp, details);
        curveDetailService.save(temp.getCurveDetail());
        return temp;
    }

    /**
     * 修改标线
     *
     * @param tempList 标准曲线信息
     * @return 曲线
     */
    @Transactional
    @Override
    public List<DtoCurveTemp> updateList(List<DtoCurveTemp> tempList) {
        if (tempList.size() > 1) {
            DtoCurveTemp tempOne = tempList.get(0);
            DtoCurveTemp tempTwo = tempList.get(1);
            tempOne.setRelevanceId(tempTwo.getId());
            tempTwo.setRelevanceId(tempOne.getId());
        } else {
            DtoCurveTemp tempOne = tempList.get(0);
            tempOne.setRelevanceId(UUIDHelper.GUID_EMPTY);
        }
        tempList.forEach(temp -> {
            this.update(temp, Boolean.FALSE);
        });
        return tempList;
    }

    /**
     * 核对测试项目（斜率有效、截取小数、实数有效）
     *
     * @param curve 标准曲线
     */
    @Transactional
    public void checkTest(DtoCurve curve) {
        //不从缓存中读取，直接从数据库中读取，以免修改k，b，c的入口没有触发缓存导致k，b不一致
        DtoTest test = testService.findOne(curve.getTestId());
        if (!test.getKValueFormat().equals(curve.getKValueFormat())
                || !test.getKDecimalFormat().equals(curve.getKDecimalFormat())
                || !test.getBValueFormat().equals(curve.getBValueFormat())
                || !test.getBDecimalFormat().equals(curve.getBDecimalFormat())
                || !test.getCValueFormat().equals(curve.getCValueFormat())
                || !test.getCDecimalFormat().equals(curve.getCDecimalFormat())) {
            DtoTest dtoTest = new DtoTest();
            BeanUtils.copyProperties(test, dtoTest);
            dtoTest.setKValueFormat(curve.getKValueFormat());
            dtoTest.setKDecimalFormat(curve.getKDecimalFormat());
            dtoTest.setBValueFormat(curve.getBValueFormat());
            dtoTest.setBDecimalFormat(curve.getBDecimalFormat());
            dtoTest.setCValueFormat(curve.getCValueFormat());
            dtoTest.setCDecimalFormat(curve.getCDecimalFormat());
            comRepository.merge(dtoTest);
            String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Test.getValue());
            redisTemplate.opsForHash().delete(key, test.getId());
        }
    }

    /**
     * 计算标线
     *
     * @param temp 标准曲线信息
     */
    @Transactional
    @Override
    public DtoCurveTemp calculate(DtoCurveTemp temp) {
        DtoCurve curve = super.findOne(temp.getId());
        if (StringUtil.isNull(curve)) {
            return this.save(temp, Boolean.TRUE);
        }
        return this.update(temp, Boolean.TRUE);
    }

    @Override
    public DtoCurve getLastNewCurveInfo(String testId) {
        DtoCurve curve = repository.findByTestIdIn(Collections.singletonList(testId)).stream().max(Comparator.comparing(DtoCurve::getModifyDate)).orElse(null);
        if (StringUtil.isNotNull(curve)) {
            curveDetailRepository.findByCurveIdOrderByOrderNumDesc(curve.getId()).stream().filter(v -> Boolean.TRUE.equals(v.getIsDimensionRow()))
                    .findFirst().ifPresent(curve::setDimensionRow);
        }
        return curve;
    }


    @Override
    public DtoCurve getCurveMost(String testId) {
        List<DtoCurve> curveList = repository.findByTestIdIn(Collections.singletonList(testId));
        // 筛选有效位数或者小数位数不为空的最后一条曲线记录
        return curveList.stream().filter(p -> p.getMostDecimal() != -1 || p.getMostSignificance() != -1).max(Comparator.comparing(DtoCurve::getModifyDate)).orElse(null);
    }


    @Override
    public String getDecimal(Map<String, Object> map) {
        if (StringUtil.isNull(map.get("value")) || StringUtil.isEmpty(String.valueOf(map.get("value")))) {
            throw new BaseException("X值不能为空");
        }
        // x值
        String value = map.get("value").toString();
        // 有效位数
        Integer mostSignificance = StringUtil.isNotNull(map.get("mostSignificance")) && StringUtil.isNotEmpty(String.valueOf(map.get("mostSignificance")))
                ? Integer.parseInt(map.get("mostSignificance").toString()) : -1;
        // 小数位数
        Integer mostDecimal = StringUtil.isNotNull(map.get("mostDecimal")) && StringUtil.isNotEmpty(String.valueOf(map.get("mostDecimal"))) ?
                Integer.parseInt(map.get("mostDecimal").toString()) : -1;
        // 数据修约
        return reviseService.getDecimal(mostSignificance, mostDecimal, value);
    }

    /**
     * 获取方程
     *
     * @param curve 标线
     * @return 方程
     */
    private String getEquation(DtoCurve curve) {
        String equation = "";
        if (curve.getCurveType().equals(EnumLIM.EnumCurveType.二次型.getValue())) {
            equation = String.format("y=%s xʌ2 %s x %s", curve.getKValue(),
                    this.isNumber(curve.getBValue()) && new BigDecimal(curve.getBValue()).compareTo(BigDecimal.ZERO) >= 0 ? "+" + curve.getBValue() : curve.getBValue(),
                    this.isNumber(curve.getCValue()) && new BigDecimal(curve.getCValue()).compareTo(BigDecimal.ZERO) >= 0 ? "+" + curve.getCValue() : curve.getCValue());
        } else if (curve.getCurveType().equals(EnumLIM.EnumCurveType.Log型.getValue())) {
            equation = String.format("y=%s lgx %s", curve.getKValue(),
                    this.isNumber(curve.getBValue()) && new BigDecimal(curve.getBValue()).compareTo(BigDecimal.ZERO) >= 0 ? "+" + curve.getBValue() : curve.getBValue());
        } else {
            equation = String.format("y=%s x %s", curve.getKValue(),
                    this.isNumber(curve.getBValue()) && new BigDecimal(curve.getBValue()).compareTo(BigDecimal.ZERO) >= 0 ? "+" + curve.getBValue() : curve.getBValue());
        }
        equation = equation.replace(" ", "");
        return equation;
    }

    /**
     * 字符串是否是数字
     *
     * @param string 字符串
     * @return 是否数字
     */
    private boolean isNumber(String string) {
        if (StringUtil.isNull(string)) {
            return false;
        }
        return NUMBER_PATTERN.matcher(string).matches();
    }

    /**
     * 设置曲线名称
     *
     * @param temp 曲线信息
     */
    private void setCurveDetail(DtoCurveTemp temp, List<DtoCurveDetail> details) {
        Integer orderNum = 100;
        for (DtoCurveDetail detail : details) {
            detail.setOrderNum(orderNum);
            detail.setCurveId(temp.getId());
            orderNum--;
        }
        temp.setCurveDetail(details);
    }

    @Autowired
    @Lazy
    public void setReviseService(ReviseService reviseService) {
        this.reviseService = reviseService;
    }
}