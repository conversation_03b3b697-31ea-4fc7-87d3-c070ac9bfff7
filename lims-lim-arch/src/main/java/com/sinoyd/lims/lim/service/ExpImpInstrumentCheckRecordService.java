package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpInstrumentCheckRecord;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentCheckRecord;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 仪器检定校准导出接口实现
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
public interface ExpImpInstrumentCheckRecordService extends ImportExcelService<DtoExpImpInstrumentCheckRecord, DtoInstrumentCheckRecord> {

    /**
     * 导出
     *
     * @param criteria   请求参数
     * @param response   响应体
     * @param sheetNames sheet页名称
     * @param fileName   附件名称
     */
    void export(BaseCriteria criteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName);
}
