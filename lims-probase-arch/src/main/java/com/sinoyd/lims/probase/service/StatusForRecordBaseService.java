package com.sinoyd.lims.probase.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord;
import com.sinoyd.lims.pro.dto.DtoStatusForRecord;

public interface StatusForRecordBaseService extends IBaseJpaService<DtoStatusForRecord, String> {
    /**
     * 核对送样单状态
     *
     * @param record 送样单
     * @param anaSub 实验室领样单（没有则传null）
     */
    void checkInfoStatus(DtoReceiveSampleRecord record, DtoReceiveSubSampleRecord anaSub);
}
