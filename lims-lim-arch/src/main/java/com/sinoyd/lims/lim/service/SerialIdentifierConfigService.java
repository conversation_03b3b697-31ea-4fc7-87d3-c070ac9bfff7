package com.sinoyd.lims.lim.service;


import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;

import java.util.List;


/**
 * SerialIdentifierConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface SerialIdentifierConfigService extends IBaseJpaService<DtoSerialIdentifierConfig, String> {


    /**
     * 根据配置类型查询相应的信息
     *
     * @param configType 配置类型
     * @return 返回相应的配置信息
     */
    DtoSerialIdentifierConfig findByConfigType(Integer configType);


    /**
     * 根据配置类型查询相应的信息
     *
     * @param configType 配置类型
     * @return 返回相应的配置信息
     */
    DtoSerialIdentifierConfig findByConfigTypeAndProjectType(Integer configType,String projectTypeId);

    /**
     * 根据配置类型、质控类型、质控等级查询相应的配置信息
     *
     * @param configType 配置类型
     * @param qcGrade    质控类型
     * @param qcType     质控等级
     * @return 返回相应的配置信息
     */
    DtoSerialIdentifierConfig findByConfigType(Integer configType, Integer qcGrade, Integer qcType, String projectTypeId);

    /**
     * 根据配置类型、质控类型、质控等级查询相应的配置信息
     *
     * @param configType 配置类型
     * @param qcGrade    质控类型
     * @param qcType     质控等级
     * @return 返回相应的配置信息
     */
    DtoSerialIdentifierConfig findByConfigType(Integer configType, Integer qcGrade, Integer qcType);


    /**
     * 根据配置类型，类型名称查询相应的配置信息
     *
     * @param configType 配置类型
     * @param configName 类型名称
     * @return 返回相应的配置信息
     */
    DtoSerialIdentifierConfig findByConfigTypeAndConfigName(Integer configType, String configName);


    /**
     * 根据配置类型获取所有的信息
     *
     * @param configType 配置类型
     * @return 返回相应的配置信息集合
     */
    List<DtoSerialIdentifierConfig> findListByConfigType(Integer configType);


    /**
     * 验证编号的规则是否正确
     *
     * @param entity 实体
     * @return 规则是否正确
     */
    Boolean validRule(DtoSerialIdentifierConfig entity);

    /**
     * 保存相应的redis数据
     *
     * @param item 可配置编号的实体对象
     */
    void saveRedis(DtoSerialIdentifierConfig item);
}