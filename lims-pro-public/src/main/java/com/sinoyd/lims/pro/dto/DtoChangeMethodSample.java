package com.sinoyd.lims.pro.dto;

import lombok.Data;

import java.util.List;

/**
 * 根据样品更改方法的参数传值Dto
 */
@Data
public class DtoChangeMethodSample {
    /**
     * 测试项目Id
     */
    private String testId;
    /**
     * 选择的样品Id集合
     */
    private List<String> sampleIds;
    /**
     * 新的方法Id
     */
    private String newAnalyzeMethodId;
    /**
     * 是否统一更改方法
     */
    private Boolean isChangeAll;
}
