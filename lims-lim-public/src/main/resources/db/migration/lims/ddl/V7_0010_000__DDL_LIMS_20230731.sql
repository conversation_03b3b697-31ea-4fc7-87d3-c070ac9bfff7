-- ----------------------------
-- 修改检测数量统计查询视图
-- ----------------------------
ALTER
VIEW vi_pro_projectyysamplecountview AS
SELECT uuid()                          AS id,
       tb_pro_sample.projectId         AS projectId,
       tb_pro_project.inceptTime       AS inceptTime,
       tb_pro_analysedata.id           AS analyseDataId,
       tb_pro_analysedata.sampleId     AS sampleId,
       tb_pro_sample.sampleTypeId      AS sampleTypeId,
       tb_base_sampletype.typeName     AS sampleTypeName,
       tb_pro_project.projectTypeId    AS projectTypeId,
       tb_pro_analysedata.isDeleted    AS isDeleted,
       tb_pro_sample.samplingTimeBegin AS samplingTime,
       tb_pro_sample.orgId             AS orgId
FROM (
      (
          (tb_pro_sample JOIN tb_pro_project ON ((tb_pro_sample.projectId = tb_pro_project.id)))
              JOIN tb_pro_analysedata ON ((tb_pro_sample.id = tb_pro_analysedata.sampleId))
          )
         JOIN tb_base_sampletype ON ((tb_pro_sample.sampleTypeId = tb_base_sampletype.id))
    )
WHERE ((tb_pro_analysedata.isDeleted = 0) AND (tb_pro_sample.isDeleted = 0) AND
       (tb_pro_project.isDeleted = 0) AND (tb_pro_sample.sampleCategory = 0))
UNION ALL
SELECT uuid()                       AS id,
       b.projectId                  AS projectId,
       tb_pro_project.inceptTime    AS inceptTime,
       tb_pro_analysedata.id        AS analyseDataId,
       tb_pro_analysedata.sampleId  AS sampleId,
       b.sampleTypeId               AS sampleTypeId,
       tb_base_sampletype.typeName  AS sampleTypeName,
       tb_pro_project.projectTypeId AS projectTypeId,
       tb_pro_analysedata.isDeleted AS isDeleted,
       b.samplingTimeBegin          AS samplingTime,
       b.orgId                      AS orgId
FROM (
      (
          (
              ( tb_pro_sample a JOIN tb_pro_sample b ON ((a.associateSampleId = b.id)) )
                  JOIN tb_pro_project ON ((b.projectId = tb_pro_project.id))
              )
              JOIN tb_pro_analysedata ON ((a.id = tb_pro_analysedata.sampleId))
          )
         JOIN tb_base_sampletype ON ((b.sampleTypeId = tb_base_sampletype.id))
    )
WHERE (
              (a.isDeleted = 0)
              AND (b.isDeleted = 0)
              AND (tb_pro_project.isDeleted = 0)
              AND (a.sampleCategory = 2)
          );