package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRangeResult;
import com.sinoyd.lims.lim.service.TestQCRangeService;
import com.sinoyd.lims.pro.criteria.QualityControlCriteria;
import com.sinoyd.lims.pro.dto.DtoQualityControl;
import com.sinoyd.lims.pro.dto.customer.DtoQualityControlDetail;
import com.sinoyd.lims.pro.dto.customer.DtoQualityControlTemp;
import com.sinoyd.lims.pro.service.QualityControlService;
import com.sinoyd.lims.pro.service.SampleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * QualityControl服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: QualityControl服务")
@RestController
@RequestMapping("api/pro/qualityControl")
public class QualityControlController extends BaseJpaController<DtoQualityControl, String, QualityControlService> {

    @Autowired
    private SampleService sampleService;

    @Autowired
    private TestQCRangeService testQCRangeService;

    @ApiOperation(value = "分页查询数据", notes = "分页查询数据")
    @GetMapping
    public RestResponse<List<DtoQualityControl>> findByPage(QualityControlCriteria qualityControlCriteria) {
        PageBean<DtoQualityControl> pageBean = super.getPageBean();
        RestResponse<List<DtoQualityControl>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, qualityControlCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询QualityControl
     *
     * @param id 主键id
     * @return RestResponse<DtoQualityControl>
     */
    @ApiOperation(value = "按主键查询QualityControl", notes = "按主键查询QualityControl")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoQualityControl> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoQualityControl> restResponse = new RestResponse<>();
        DtoQualityControl qualityControl = service.findOne(id);
        restResponse.setData(qualityControl);
        restResponse.setRestStatus(StringUtil.isNull(qualityControl) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增QualityControl
     *
     * @param qualityControl 实体列表
     * @return RestResponse<DtoQualityControl>
     */
    @ApiOperation(value = "新增QualityControl", notes = "新增QualityControl")
    @PostMapping
    public RestResponse<List<Map<String, Object>>> addQCSample(@RequestBody @Validated DtoQualityControlTemp qualityControl) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        restResponse.setData(sampleService.addInnerSample(qualityControl, false));
        return restResponse;
    }

    /**
     * 新增现场QualityControl
     *
     * @param qualityControl 实体列表
     * @return RestResponse<DtoQualityControl>
     */
    @ApiOperation(value = "新增QualityControl", notes = "新增QualityControl")
    @PostMapping("/addXCSample")
    public RestResponse<List<Map<String, Object>>> addXcQCSample(@RequestBody @Validated DtoQualityControlTemp qualityControl) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        restResponse.setData(sampleService.addInnerSample(qualityControl, true));
        return restResponse;
    }

    /**
     * 新增QualityControl
     *
     * @param qualityControlList 实体列表
     * @return RestResponse<DtoQualityControl>
     */
    @ApiOperation(value = "新增QualityControl", notes = "新增QualityControl")
    @PostMapping("/addQcSampleList")
    public RestResponse<List<Map<String, Object>>> addQcSampleList(@RequestBody List<DtoQualityControlTemp> qualityControlList) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        restResponse.setData(sampleService.addInnerSample(qualityControlList));
        return restResponse;
    }

    /**
     * 新增QualityControl
     *
     * @param qualityControl 实体列表
     * @return RestResponse<DtoQualityControl>
     */
    @ApiOperation(value = "修改QualityControl", notes = "修改QualityControl")
    @PutMapping
    public RestResponse<DtoQualityControl> update(@RequestBody @Validated DtoQualityControl qualityControl) {
        RestResponse<DtoQualityControl> restResponse = new RestResponse<>();
        restResponse.setData(service.update(qualityControl));
        return restResponse;
    }

    /**
     * "根据id批量删除QualityControl
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除QualityControl", notes = "根据id批量删除QualityControl")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }


    /**
     * 获取加标数据
     *
     * @param qualityControl 实体列表
     * @return RestResponse<DtoQualityControl>
     */
    @ApiOperation(value = "获取加标数据", notes = "获取加标数据")
    @PostMapping("/jb")
    public RestResponse<DtoQualityControl> findJBValue(@RequestBody DtoQualityControlTemp qualityControl) {
        RestResponse<DtoQualityControl> restResponse = new RestResponse<>();
        DtoQualityControl dtoQualityControl = service.calculateJBValue(qualityControl.getQcId(), qualityControl.getAnaId(),
                qualityControl.getFormulaId(), qualityControl.getIsCalculate(), qualityControl.getAnalyseData(), qualityControl.getParamsConfig());
        restResponse.setData(dtoQualityControl);
        restResponse.setRestStatus(StringUtil.isNull(dtoQualityControl) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        if (StringUtil.isNotNull(dtoQualityControl.getIsPass()) && !dtoQualityControl.getIsPass()) {
            restResponse.setMsg("回收率不在" + dtoQualityControl.getRange() + "范围内");
        } else {
            restResponse.setMsg("");
        }
        return restResponse;
    }

    /**
     * 获取加标数据
     *
     * @param qualityControl 实体列表
     * @return RestResponse<DtoQualityControl>
     */
    @ApiOperation(value = "计算加标数据", notes = "计算加标数据")
    @PostMapping("/jbCalculate")
    public RestResponse<Map<String, Object>> calculateJBValue(@RequestBody DtoQualityControlTemp qualityControl) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        Map<String, Object> map = service.calculateJBValue(qualityControl.getRealSampleTestValue(), qualityControl.getQcTestValue(),
                qualityControl.getTestValue(), qualityControl.getQcValue(), qualityControl.getTestId(), qualityControl.getQcType());
        restResponse.setData(map);
        restResponse.setRestStatus(StringUtil.isNull(map) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        if (StringUtil.isNotNull(map.get("isPass")) && !(Boolean) map.get("isPass")) {
            restResponse.setMsg(map.get("range").toString());
        } else {
            restResponse.setMsg("");
        }
        return restResponse;
    }

    /**
     * 批量计算加标数据
     *
     * @param qualityControlTempList 实体列表
     * @return RestResponse<List < DtoQualityControl>>
     */
    @ApiOperation(value = "批量计算加标数据", notes = "批量计算加标数据")
    @PostMapping("/jbCalculate/batch")
    public RestResponse<List<DtoQualityControl>> calculateJBValueBatch(@RequestBody List<DtoQualityControlTemp> qualityControlTempList) {
        RestResponse<List<DtoQualityControl>> restResponse = new RestResponse<>();
        List<DtoQualityControl> resList = service.calculateJBValueBatch(qualityControlTempList);
        restResponse.setData(resList);
        restResponse.setRestStatus(StringUtil.isEmpty(resList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        List<String> msgList = new ArrayList<>();
        for (DtoQualityControl control : resList) {
            if (StringUtil.isNotNull(control.getIsPass()) && !control.getIsPass()) {
                String msg = "回收率不在" + control.getRange() + "范围内";
                if (!msgList.contains(msg)) {
                    msgList.add(msg);
                }
            }
        }
        restResponse.setMsg(StringUtil.isNotEmpty(msgList) ? String.join(" ", msgList) : "");
        return restResponse;
    }

    /**
     * 批量修改加标量纲
     *
     * @param qualityControlTempList         样值
     * @param qcVolumeDimensionId            加标体积量纲
     * @param qcValueDimensionId             加入标准量量纲
     * @param qcTestValueDimensionId         测定值量纲
     * @param realSampleTestValueDimensionId 样值量纲
     */
    @ApiOperation(value = "批量更新加标数据量纲", notes = "批量更新加标数据量纲")
    @PostMapping("/jbDimension/batch")
    public RestResponse<Void> updateJBDimensionBatch(@RequestBody List<DtoQualityControlTemp> qualityControlTempList, @RequestParam String qcVolumeDimensionId
            , @RequestParam String qcValueDimensionId, @RequestParam String qcTestValueDimensionId, @RequestParam String realSampleTestValueDimensionId,
                                                     @RequestParam String ssConcentrationDimensionId, @RequestParam String constantVolumeDimensionId) {
        RestResponse<Void> response = new RestResponse<>();
        service.updateJBDimensionBatch(qualityControlTempList, qcVolumeDimensionId, qcValueDimensionId, qcTestValueDimensionId, realSampleTestValueDimensionId, ssConcentrationDimensionId, constantVolumeDimensionId);
        return response;
    }

    @ApiOperation(value = "修改标样数据", notes = "修改标样数据")
    @PutMapping("/by")
    public RestResponse<Map<String, Object>> updateStandardSampleInfo(@RequestBody Map<String, Object> map) throws IllegalAccessException {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setData(service.updateStandardSampleInfoFromMap(map));
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "修改替代样数据并计算回收率", notes = "修改替代样数据并计算回收率")
    @PutMapping("/td")
    public RestResponse<DtoQualityControl> updateReplaceSampleInfo(@RequestBody DtoQualityControl dtoQualityControl) {
        RestResponse<DtoQualityControl> restResponse = new RestResponse<>();
        DtoQualityControl qualityControl = service.updateReplaceSampleInfo(dtoQualityControl);
        restResponse.setData(qualityControl);
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        if (StringUtil.isNotNull(qualityControl.getIsPass()) && !qualityControl.getIsPass()) {
            restResponse.setMsg(qualityControl.getRange());
        } else {
            restResponse.setMsg("");
        }
        return restResponse;
    }

    @ApiOperation(value = "修改校准系数检验数据并计算偏差", notes = "修改替代样数据并计算偏差")
    @PutMapping("/cf")
    public RestResponse<DtoQualityControl> updateCorrectionFactorSampleInfo(@RequestBody DtoQualityControl dtoQualityControl) {
        RestResponse<DtoQualityControl> restResponse = new RestResponse<>();
        DtoQualityControl qualityControl = service.updateCorrectionFactorSampleInfo(dtoQualityControl);
        restResponse.setData(qualityControl);
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        if (StringUtil.isNotNull(qualityControl.getIsPass()) && !qualityControl.getIsPass()) {
            restResponse.setMsg(qualityControl.getRange());
        } else {
            restResponse.setMsg("");
        }
        return restResponse;
    }


    /**
     * 获取替代样数据
     *
     * @param qualityControl 实体列表
     * @return RestResponse<DtoQualityControl>
     */
    @ApiOperation(value = "获取替代样数据", notes = "获取替代样数据")
    @PostMapping("/replace")
    public RestResponse<DtoQualityControlTemp> findReplaceValue(@RequestBody DtoQualityControlTemp qualityControl) {
        RestResponse<DtoQualityControlTemp> restResponse = new RestResponse<>();
        DtoQualityControlTemp dtoQualityControlTemp = service.getReplaceValue(qualityControl);
        restResponse.setData(dtoQualityControlTemp);
        restResponse.setRestStatus(StringUtil.isNull(dtoQualityControlTemp) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 计算替代样数据
     *
     * @param qualityControl 实体列表
     * @return RestResponse<DtoQualityControl>
     */
    @ApiOperation(value = "计算替代样数据", notes = "计算替代样数据")
    @PostMapping("/replaceCalculate")
    public RestResponse<Map<String, Object>> calculateReplaceValue(@RequestBody DtoQualityControlTemp qualityControl) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        Map<String, Object> map = service.calculateReplaceValue(qualityControl.getTestValue(), qualityControl.getAddition(), qualityControl.getFormulaId());
        restResponse.setData(map);
        restResponse.setRestStatus(StringUtil.isNull(map) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "加标提醒", notes = "加标提醒")
    @PostMapping("/jbRemind")
    public RestResponse<DtoTestQCRangeResult> calculateJBPass(@RequestBody DtoQualityControl qualityControl) {
        RestResponse<DtoTestQCRangeResult> restResponse = new RestResponse<>();
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        DtoTestQCRangeResult result = testQCRangeService.calculateJBPass(new BigDecimal(qualityControl.getQcOriginValue()), new BigDecimal(qualityControl.getQcRecoverRate()), qualityControl.getTestId());
        restResponse.setData(result);
        if (result.getIsPass()) {
            restResponse.setMsg("");
        } else {
            restResponse.setMsg("加标允差范围不在" + result.getRangeConfig() + "范围内，不合格!");
        }
        return restResponse;
    }

    @ApiOperation(value = "获取项目质控信息", notes = "获取项目质控信息")
    @GetMapping("/info")
    public RestResponse<List<DtoQualityControlDetail>> findQualityControlDetailByProjectId(@RequestParam(name = "projectId") String projectId) {
        RestResponse<List<DtoQualityControlDetail>> restResponse = new RestResponse<>();
        restResponse.setData(service.findQualityControlDetailByProjectId(projectId));
        restResponse.setCount(StringUtil.isNull(restResponse.getData()) ? 0 : restResponse.getData().size());
        restResponse.setRestStatus(StringUtil.isNull(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }
}