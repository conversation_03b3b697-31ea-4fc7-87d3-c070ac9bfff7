package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 检测单复核的操作
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoWorkSheetCheck {


    /**
     * 检测单ids
     */
    private List<String> ids = new ArrayList<>();


    /**
     * 是否通过
     */
    private Boolean status = false;

    /**
     * 意见
     */
    private String opinion;

    /**
     * 审核人
     */
    private String auditorId;

    /**
     * 审核人姓名
     */
    private String auditorName;

    /**
     * 是否编制报告
     */
    private Boolean isReport;
}