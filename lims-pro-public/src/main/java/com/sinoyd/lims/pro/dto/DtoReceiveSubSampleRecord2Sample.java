package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReceiveSubSampleRecord2Sample;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoReceiveSubSampleRecord2Sample实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ReceiveSubSampleRecord2Sample")
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoReceiveSubSampleRecord2Sample extends ReceiveSubSampleRecord2Sample {
   private static final long serialVersionUID = 1L;
 }