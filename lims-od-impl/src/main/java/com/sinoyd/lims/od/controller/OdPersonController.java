package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.OdPersonCriteria;
import com.sinoyd.lims.od.dto.DtoOdPerson;
import com.sinoyd.lims.od.service.OdPersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * odPerson服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例: odPerson服务")
@RestController
@RequestMapping("api/lim/odPerson")
@Validated
public class OdPersonController extends BaseJpaController<DtoOdPerson, String, OdPersonService> {

    /**
     * 分页动态条件查询OdPerson
     *
     * @param odPersonCriteria 条件参数
     * @return RestResponse<List < OdPerson>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoOdPerson>> findByPage(OdPersonCriteria odPersonCriteria) {
        PageBean<DtoOdPerson> pageBean = super.getPageBean();
        RestResponse<List<DtoOdPerson>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, odPersonCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询odPerson
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询odPerson", notes = "根据id查询odPerson")
    @GetMapping("/{id}")
    public RestResponse<DtoOdPerson> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOdPerson> restResp = new RestResponse<>();
        DtoOdPerson entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增odPerson
     *
     * @param odPerson odPerson实体
     * @return 新增的odPerson实体
     */
    @ApiOperation(value = "新增odPerson", notes = "新增odPerson")
    @PostMapping("")
    public RestResponse<DtoOdPerson> create(@Validated @RequestBody DtoOdPerson odPerson) {
        RestResponse<DtoOdPerson> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoOdPerson data = service.save(odPerson);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新odPerson
     *
     * @param odPerson odPerson实体
     * @return 更新后的odPerson实体
     */
    @ApiOperation(value = "更新odPerson", notes = "更新odPerson")
    @PutMapping("")
    public RestResponse<DtoOdPerson> update(@Validated @RequestBody DtoOdPerson odPerson) {
        RestResponse<DtoOdPerson> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoOdPerson data = service.update(odPerson);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
