package com.sinoyd.lims.pro.repository;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoProject2Customer;

import java.util.Collection;
import java.util.List;


/**
 * Project2Customer数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/10/12
 * @since V100R001
 */
public interface Project2CustomerRepository extends IBaseJpaPhysicalDeleteRepository<DtoProject2Customer, String>,
        LimsRepository<DtoProject2Customer, String> {

    /**
     * 根据项目id返回项目企业关联
     *
     * @param projectIdList 项目id列表
     * @return 项目企业关联对象列表
     */
    List<DtoProject2Customer> findByProjectIdIn(List<String> projectIdList);

    /**
     * 根据项目id返回项目企业关联
     *
     * @param projectId 项目id
     * @return 项目企业关联对象列表
     */
    List<DtoProject2Customer> findByProjectId(String projectId);

    /**
     * 根据项目id删除企业关联信息
     * @param projectIds 项目ids
     * @return 删除个数
     */
    Integer deleteByProjectIdIn(Collection<?> projectIds);

    /**
     * 根据项目id和企业id确认是否已经存在关联企业
     * @param projectId 项目id
     * @param customerId 企业id
     * @return 存在个数
     */
    Integer countByProjectIdAndCustomerId(String projectId,String customerId);
}