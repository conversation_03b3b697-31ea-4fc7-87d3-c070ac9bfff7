package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSampleReserve2Test;

import java.util.List;

/**
 * SampleReserve2TestRepository数据访问操作接口
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/20
 */
public interface SampleReserve2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleReserve2Test,String> {
    /**
     * 根据父级Id查询数据
     *
     * @param reserveIds 父级Id
     * @return 查询结果
     */
    List<DtoSampleReserve2Test> findByReserveIdIn(List<String> reserveIds);

    /**
     * 根据分析项目id查询数据
     *
     * @param analyzeItemId 分析项目id
     * @return 查询结果
     */
    List<DtoSampleReserve2Test> findByAnalyzeItemId(String analyzeItemId);

    /**
     * 根据分析项目id查询数据
     *
     * @param analyzeItemIds 分析项目id
     * @return 查询结果
     */
    List<DtoSampleReserve2Test> findByAnalyzeItemIdIn(List<String> analyzeItemIds);
}
