package com.sinoyd.base.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Map;

/**
 * LIMS实体基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/23
 */
@Data
public abstract class LimsBaseEntity implements BaseEntity, Serializable {

    /**
     * 数据库扩展信息
     */
    @Transient
    protected Map<String, Object> dbExtendMap;
}
