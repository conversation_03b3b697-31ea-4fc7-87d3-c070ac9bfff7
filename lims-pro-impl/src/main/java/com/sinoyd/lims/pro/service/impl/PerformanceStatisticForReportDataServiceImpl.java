package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.criteria.PerformanceStatisticForLocalDataCriteria;
import com.sinoyd.lims.pro.criteria.PerformanceStatisticForReportDataCriteria;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForLocalData;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForReportData;
import com.sinoyd.lims.pro.entity.PerformanceStatisticForReportData;
import com.sinoyd.lims.pro.repository.PerformanceStatisticForReportDataRepository;
import com.sinoyd.lims.pro.repository.ReportRepository;
import com.sinoyd.lims.pro.service.PerformanceStatisticForReportDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * PerformanceStatisticForReportData操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/24
 * @since V100R001
 */
 @Service
public class PerformanceStatisticForReportDataServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPerformanceStatisticForReportData,String,PerformanceStatisticForReportDataRepository> implements PerformanceStatisticForReportDataService {


    @Autowired
    private ReportRepository reportRepository;

    @Override
    public void createReportStatistic(String projectId, String reportMarker, Date reportTime) {
        List<String> projectIds = new ArrayList<>();
        projectIds.add(projectId);
        createReportStatistic(projectIds, reportMarker, reportTime, PrincipalContextUser.getPrincipal());
    }

    @Async
    @Override
    public void createReportStatistic(List<String> projectIds, String reportMarker, Date reportTime,CurrentPrincipalUser principalContextUser) {
        //重新赋值登录的账号信息
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(principalContextUser, null, authorities));
        List<Object[]> objects = reportRepository.countReportNumGroupByProjectIdsAndCreator(projectIds, reportMarker);
        List<DtoPerformanceStatisticForReportData> dtoPerformanceStatisticForReportDataList = new ArrayList<>();
        for (String projectId : projectIds) {
            Integer reportNum = 0;
            Object[] object = objects.stream().filter(p -> p[1].equals(projectId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(object)) {
                reportNum = ((Long) object[0]).intValue();
            }
            DtoPerformanceStatisticForReportData dtoPerformanceStatisticForReportData = new DtoPerformanceStatisticForReportData();
            dtoPerformanceStatisticForReportData.setProjectId(projectId);
            dtoPerformanceStatisticForReportData.setReportTime(reportTime);
            dtoPerformanceStatisticForReportData.setReport(reportNum);
            dtoPerformanceStatisticForReportData.setReportMakerId(reportMarker);
            dtoPerformanceStatisticForReportDataList.add(dtoPerformanceStatisticForReportData);
        }
        if (projectIds.size() > 0) {
            repository.deleteByProjectIdInAndReportMakerId(projectIds, reportMarker);
        }
        if (dtoPerformanceStatisticForReportDataList.size() > 0) {
            repository.save(dtoPerformanceStatisticForReportDataList);
        }
    }


    @Override
    public void findByPage(PageBean<DtoPerformanceStatisticForReportData> pb, BaseCriteria baseCriteria) {
        PerformanceStatisticForReportDataCriteria performanceStatisticForReportDataCriteria = (PerformanceStatisticForReportDataCriteria) baseCriteria;
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForReportData(");
        stringBuilder.append("a.id,a.projectId,a.reportTime,a.report,a.reportMakerId,a.orgId,b.projectCode,b.projectName,");
        stringBuilder.append("b.projectTypeId,");
        stringBuilder.append("c.name,b.customerId,b.customerName");
        stringBuilder.append(") ");
        pb.setSelect(stringBuilder.toString());
        pb.setEntityName("DtoPerformanceStatisticForReportData a,DtoProject b,DtoProjectType c");
        pb.setSort(performanceStatisticForReportDataCriteria.getSort());
        pb.setCondition(performanceStatisticForReportDataCriteria.getCondition());

        comRepository.findByPage(pb, performanceStatisticForReportDataCriteria.getValues());
    }

    /**
     * 获取总计行
     * @param baseCriteria
     * @return
     */
    @Override
    public  DtoPerformanceStatisticForReportData findSumPerformanceStatistic(BaseCriteria baseCriteria) {
        PerformanceStatisticForReportDataCriteria performanceStatisticForReportDataCriteria = (PerformanceStatisticForReportDataCriteria) baseCriteria;
        PageBean<Object[]> pb = new PageBean<>();
        pb.setSelect("select sum(p.report) ");
        pb.setEntityName("DtoPerformanceStatisticForReportData p");
        pb.setCondition(performanceStatisticForReportDataCriteria.getCondition());
        List<Object[]> list = comRepository.find(pb.getAutoQuery(), performanceStatisticForReportDataCriteria.getValues());
        if (list.size() > 0) {
            Object[] objArray = list.get(0);
            return new DtoPerformanceStatisticForReportData(Integer.valueOf(String.valueOf(objArray[0])));
        }
        return new DtoPerformanceStatisticForReportData();
    }
}