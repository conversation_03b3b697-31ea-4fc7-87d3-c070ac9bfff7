package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * ReceiveSampleRecord查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReceiveSampleRecordQueryCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 送样人员
     */
    private String senderId;

    /**
     * 项目类型
     */
    private String projectTypeId;

    /**
     * 排除的项目类型
     */
    private String exceptProjectTypeId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 关键字（样品编号、受检方、送样单号、项目编号、项目名称、委托方）
     */
    private String key;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 送样状态
     */
    private Integer receiveStatus = EnumPRO.EnumStatus.所有.getValue();


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and r.projectId = p.id");

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and r.sendTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and r.sendTime < :endTime");
            values.put("endTime", to);
        }
        if (StringUtil.isNotEmpty(this.projectTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.projectTypeId)) {
            condition.append(" and p.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        }
        if (StringUtil.isNotEmpty(this.exceptProjectTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.exceptProjectTypeId)) {
            condition.append(" and p.projectTypeId <> :exceptProjectTypeId");
            values.put("exceptProjectTypeId", this.exceptProjectTypeId);
        }
        if (StringUtil.isNotEmpty(this.senderId) && !UUIDHelper.GUID_EMPTY.equals(this.senderId)) {
            condition.append(" and r.senderId = :senderId");
            values.put("senderId", this.senderId);
        }
        if (StringUtil.isNotEmpty(this.projectId) && !UUIDHelper.GUID_EMPTY.equals(this.projectId)) {
            condition.append(" and r.projectId = :projectId");
            values.put("projectId", this.projectId);
        }
        if (!EnumPRO.EnumStatus.所有.getValue().equals(this.receiveStatus)) {
            condition.append(" and r.receiveStatus = :receiveStatus");
            values.put("receiveStatus", this.receiveStatus);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (r.recordCode like :key or p.projectCode like :key or " +
                    "p.projectName like :key or p.customerName like :key or p.inspectedEnt like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleCode)) {
            condition.append(" and exists (select 1 from DtoSample sam where sam.orgId = :orgId and r.id = sam.receiveId and sam.isDeleted = 0 and sam.code like :sampleCode)");
            values.put("sampleCode", "%" + this.sampleCode + "%");
            values.put("orgId", StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "");
        }
        return condition.toString();
    }
}
