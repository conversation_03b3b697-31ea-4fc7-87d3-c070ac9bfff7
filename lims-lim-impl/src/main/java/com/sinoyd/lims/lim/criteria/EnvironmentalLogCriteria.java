package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;

/**
 * 环境管理
 * <AUTHOR> 修改：xuxb
 * @version v1.0.0 2019/3/12
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnvironmentalLogCriteria extends BaseCriteria {


    /**
     * 实验室id
     */
    private String environmentalId;
    /**
     * 检索开始时间
     */
    private String dtBegin;
    /**
     * 检索结束时间
     */
    private String dtEnd;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(environmentalId) && !UUIDHelper.GUID_EMPTY.equals(this.environmentalId)) {
            condition.append(" and (environmentalId = :environmentalId)");
            values.put("environmentalId", this.environmentalId);
        }
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and updateTime >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and updateTime < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        return condition.toString();
    }
}