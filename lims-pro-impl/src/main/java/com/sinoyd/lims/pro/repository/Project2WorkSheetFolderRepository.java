package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoProject2WorkSheetFolder;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Project2WorkSheetFolder数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface Project2WorkSheetFolderRepository extends IBaseJpaPhysicalDeleteRepository<DtoProject2WorkSheetFolder, String> {

    /**
     * 根据项目id查询检测单关联
     *
     * @param projectId 项目id
     * @return 返回相应的检测单关联
     */
    List<DtoProject2WorkSheetFolder> findByProjectId(String projectId);

    /**
     * 根据项目id列表查询检测单关联
     *
     * @param projectIds 项目id列表
     * @return 返回相应的检测单关联
     */
    List<DtoProject2WorkSheetFolder> findByProjectIdIn(List<String> projectIds);

    /**
     * 删除检测单下的关联
     *
     * @param workSheetFolderId 检测单id
     * @return 返回删除的条数
     */
    @Transactional
    @Modifying
    @Query("delete from DtoProject2WorkSheetFolder where workSheetFolderId = :workSheetFolderId")
    Integer deleteByWorkSheetFolderId(@Param("workSheetFolderId") String workSheetFolderId);

    /**
     * 删除检测单下的关联
     *
     * @param projectId          项目id
     * @param workSheetFolderIds 检测单id集合
     * @return 返回删除的条数
     */
    @Transactional
    Integer deleteByProjectIdAndWorkSheetFolderIdIn(String projectId, List<String> workSheetFolderIds);


    /**
     * 根据检测单id查询检测单关联
     *
     * @param workSheetFolderId 检测单id
     * @return 返回相应的检测单关联
     */
    List<DtoProject2WorkSheetFolder> findByWorkSheetFolderId(String workSheetFolderId);

    /**
     * 根据检测单id集合查询检测单关联
     *
     * @param workSheetFolderIds 检测单id集合
     * @return 返回相应的检测单关联
     */
    List<DtoProject2WorkSheetFolder> findByWorkSheetFolderIdIn(List<String> workSheetFolderIds);

    /**
     * 删除检测单下的关联
     *
     * @param workSheetFolderIds 检测单id
     * @return 返回删除的条数
     */
    @Transactional
    @Modifying
    @Query("delete from DtoProject2WorkSheetFolder where workSheetFolderId in :workSheetFolderIds")
    Integer deleteByWorkSheetFolderIdIn(@Param("workSheetFolderIds") List<String> workSheetFolderIds);

    /**
     * 当前检测单排除指定不需要删除的项目id
     *
     * @param projectId         项目id
     * @param workSheetFolderId 检测单id
     * @return 返回删除行数
     */
    @Transactional
    Integer deleteByProjectIdNotInAndWorkSheetFolderId(List<String> projectId, String workSheetFolderId);
}