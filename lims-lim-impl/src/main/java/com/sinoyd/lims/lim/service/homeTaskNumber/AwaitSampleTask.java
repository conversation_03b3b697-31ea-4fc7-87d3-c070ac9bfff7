package com.sinoyd.lims.lim.service.homeTaskNumber;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost2Person;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost2Test;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestPost2PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestPost2TestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 首页实验室待检代办数字缓存刷新
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/10/26
 */
@Component
public class AwaitSampleTask extends AbsTaskNumber {

    private CodeService codeService;

    private TestPost2TestRepository testPost2TestRepository;

    private TestPost2PersonRepository testPost2PersonRepository;

    private UserService userService;


    /**
     * 刷新卡片的代办数据
     *
     * @param homeModule 模块数据
     */
    @Override
    public void refreshCardNum(HomeModule homeModule, String orgId, String userId) {
        //获取数据库中的数量
        List<DtoTaskNum> taskNumList = this.getTaskNum(homeModule, orgId, userId, new ArrayList<>());
        String redisKey = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_AwaitSampleDetail.getValue());
        for (DtoTaskNum taskNum : taskNumList) {
            saveCardInfo(taskNum.getUserId(), redisKey,"awaitSampleCount", "awaitSampleDetail", taskNum.getCount());
        }
    }

    /**
     * 获取缓存数据集合
     *
     * @param homeModule 模块编码
     * @param orgId      组织Id
     * @param userId     用户Id
     * @param outTypeIds 不包含的项目类型
     * @return 结果集
     */
    @Override
    public List<DtoTaskNum> getTaskNum(HomeModule homeModule, String orgId, String userId, List<String> outTypeIds) {
        List<DtoTaskNum> resultData = new ArrayList<>();
        //判断是否按岗位分配
        boolean allocateByPost = false;
        DtoCode dtoCode = codeService.findByCode(LimConstants.codeConstants.PRO_AnalyseAllocationRules_Post);
        if (StringUtil.isNotNull(dtoCode) && "1".equals(dtoCode.getDictValue())) {
            allocateByPost = true;
        }
        //创建查询sql语句
        StringBuilder stringBuilder = allocateByPost ? new StringBuilder("select a.testId,count(a.id) as count from")
                : new StringBuilder("select a.analystId as userId,count(a.id) as count from");
        stringBuilder.append(" TB_PRO_AnalyseData a,TB_PRO_Sample s");
        stringBuilder.append(" where 1=1 and a.orgId = ?");
        stringBuilder.append(" and  a.isDeleted = 0 and s.isDeleted = 0 and a.sampleId = s.id and a.isOutsourcing = 0 and a.isCompleteField = 0");
        stringBuilder.append(" and a.workSheetId = ? and a.workSheetFolderId = ?");
        stringBuilder.append(" and a.dataStatus = 1 and (s.samplingStatus = 4 or s.samplingStatus = 8)");
        stringBuilder.append(" and s.status <> '样品作废' and s.receiveId != ? and s.innerReceiveStatus = 6");
        stringBuilder.append(allocateByPost ? " group by a.testId" : " group by a.analystId");
        //当按照岗位显示时，显示的数量为人员下配置的所有岗位的待检样品数量
        if (allocateByPost) {
            //查询好分组的数量
            List<DtoTaskNum> groupByTest = jdbcTemplate.query(stringBuilder.toString(),
                    new String[]{orgId, UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY},
                    (resultSet, i) -> new DtoTaskNum("", resultSet.getString("testId"), resultSet.getLong("count")));
            //获取到所有的用户数据
            List<DtoUser> userList = userService.findAll();
            //获取用户id
            List<String> userIds = userList.stream().map(DtoUser::getId).collect(Collectors.toList());
            //根据人员id获取对应的岗位数据
            if (StringUtil.isNotEmpty(userIds)) {
                //获取人员对应岗位信息
                List<DtoTestPost2Person> toPersonList = testPost2PersonRepository.findByPersonIdIn(userIds);
                //获取所有的岗位对应测试项目数据
                List<String> testPostIds = toPersonList.stream().map(DtoTestPost2Person::getTestPostId).collect(Collectors.toList());
                List<DtoTestPost2Test> toTestList = StringUtil.isNotEmpty(testPostIds) ? testPost2TestRepository.findByTestPostIdIn(testPostIds) : new ArrayList<>();
                //按照人员分组岗位数据
                Map<String, List<DtoTestPost2Person>> postByPerson = toPersonList.stream().collect(Collectors.groupingBy(DtoTestPost2Person::getPersonId));
                for (String personId : userIds) {
                    DtoTaskNum taskNum = new DtoTaskNum();
                    taskNum.setUserId(personId);
                    //获取人员下的岗位数据
                    List<DtoTestPost2Person> dtoTestPost2People = postByPerson.get(personId);
                    //获取人员下的岗位id
                    List<String> testPostIdsOfPerson = StringUtil.isNotEmpty(dtoTestPost2People) ? dtoTestPost2People.stream().map(DtoTestPost2Person::getTestPostId).collect(Collectors.toList()) :new ArrayList<>();
                    //获取测试项目id
                    List<String> testIds = toTestList.stream()
                            .filter(p -> testPostIdsOfPerson.contains(p.getTestPostId()))
                            .map(DtoTestPost2Test::getTestId)
                            .collect(Collectors.toList());
                    List<DtoTaskNum> groupByTestOfUser = groupByTest.stream().filter(p -> testIds.contains(p.getTestId())).collect(Collectors.toList());

                    Optional<Long> num = groupByTestOfUser.stream().map(DtoTaskNum::getCount).reduce(Long::sum);
                    taskNum.setCount(num.orElse(BigDecimal.ZERO.longValue()));
                    resultData.add(taskNum);
                }
            }
        }else{
            //按照人员分组显示
            resultData = jdbcTemplate.query(stringBuilder.toString(),
                    new String[]{orgId, UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY},
                    (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"), resultSet.getLong("count")));
        }

        return resultData;
    }

    /**
     * 获取模块编码
     *
     * @return 模块编码
     */
    @Override
    public String getModuleCode() {
        return EnumLIM.EnumHomeTaskModule.实验室待检.getValue();
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setTestPost2TestRepository(TestPost2TestRepository testPost2TestRepository) {
        this.testPost2TestRepository = testPost2TestRepository;
    }

    @Autowired
    public void setTestPost2PersonRepository(TestPost2PersonRepository testPost2PersonRepository) {
        this.testPost2PersonRepository = testPost2PersonRepository;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }
}
