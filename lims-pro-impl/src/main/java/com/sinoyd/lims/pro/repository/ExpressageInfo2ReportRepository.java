package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoExpressageInfo2Report;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 快递报告数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/5
 * @since V100R001
 */
public interface ExpressageInfo2ReportRepository extends IBaseJpaPhysicalDeleteRepository<DtoExpressageInfo2Report, String> {

    /**
     * 删除对应快递下的快递报告关联
     *
     * @param expressageInfoIds 快递id集合
     * @return 
     */
    @Transactional
    Integer deleteByExpressageInfoIdIn(List<String> expressageInfoIds);

    /**
     * 根据快递id查询
     *
     * @param ids 快递id集合
     * @return 查询的集合
     */
    List<DtoExpressageInfo2Report> findByExpressageInfoIdIn(List<String> ids);

    /**
     * 根据报告id查询
     *
     * @param ids 快递id集合
     * @return 查询的集合
     */
    List<DtoExpressageInfo2Report> findByReportIdIn(List<String> ids);
}