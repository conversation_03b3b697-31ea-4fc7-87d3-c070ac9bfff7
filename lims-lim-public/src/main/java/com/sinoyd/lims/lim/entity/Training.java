package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 培训实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "Training")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Training extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Training() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 培训名称
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("培训名称")
    @Length(message = "培训名称{validation.message.length}", max = 50)
    private String trainingName;

    /**
     * 培训日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("培训日期")
    private Date trainingDate;

    /**
     * 培训方式（常量（Guid）：LIM_Training）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("培训方式（常量（Guid）：LIM_Training）")
    @Length(message = "培训方式{validation.message.length}", max = 50)
    private String way;

    /**
     * 培训时长
     */
    @ApiModelProperty("培训时长")
    private Double times;

    /**
     * 培训讲师
     */
    @Column(length = 50)
    @ApiModelProperty("培训讲师")
    @Length(message = "培训讲师{validation.message.length}", max = 50)
    private String lecturer;

    /**
     * 记录人
     */
    @Column(length = 50)
    @ApiModelProperty("记录人")
    @Length(message = "记录人{validation.message.length}", max = 50)
    private String recorder;

    /**
     * 培训状态  0: 未开展  1：已开展
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("培训状态  0: 未开展  1：已开展")
    private Integer status;

    /**
     * 是否审批 0: 否  1：是
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否审批 0: 否  1：是")
    private Boolean auditInd = false;

    /**
     * 培训内容
     */
    @ApiModelProperty("培训内容")
    @Length(message = "培训内容{validation.message.length}", max = 255)
    private String content;

    /**
     * 计划参加人数
     */
    @ApiModelProperty("计划参加人数")
    private Integer planPeopleNums;


    /**
     * 申请人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("申请人")
    private String applicant;

    /**
     * 申请日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("申请日期")
    private Date applicationDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;


    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}
