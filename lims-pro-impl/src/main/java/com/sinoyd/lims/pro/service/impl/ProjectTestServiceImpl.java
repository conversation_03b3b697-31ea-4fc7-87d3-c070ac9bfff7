package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.dto.customer.DtoProjectTest;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumPRORedis;
import com.sinoyd.lims.pro.service.ProjectTestService;
import com.sinoyd.lims.pro.service.SampleFolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 项目指标操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/27
 * @since V100R001
 */
@Service
public class ProjectTestServiceImpl implements ProjectTestService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private SampleFolderService sampleFolderService;

    /**
     * 保存项目指标
     *
     * @param projectId       项目id
     * @param projectTestList 项目指标
     */
    @Transactional
    @Override
    public void saveProjectTest(String projectId, List<DtoProjectTest> projectTestList) {
        String key = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_ProjectTest.getValue()) + projectId;
        Map<String, List<DtoProjectTest>> projectTestMap = projectTestList.stream().collect(Collectors.groupingBy(DtoProjectTest::getSampleTypeId));
        for (String sampleTypeId : projectTestMap.keySet()) {
            redisTemplate.opsForHash().put(key, sampleTypeId, JsonStream.serialize(projectTestMap.get(sampleTypeId)));
        }
        //若不存在key，即所传的条数为0，则随机加一个值，保证存在key，以免之后取指标时从redis找不到还去数据库找
        if (!redisTemplate.hasKey(key)) {
            redisTemplate.opsForHash().put(key, UUIDHelper.GUID_EMPTY, JsonStream.serialize(projectTestList));
        }
        redisTemplate.expire(key, ProCodeHelper.EXPIRE_HOUR, TimeUnit.HOURS);
    }

    /**
     * 修改/添加项目指标
     *
     * @param projectId       项目id
     * @param projectTestList 项目指标
     */
    @Transactional
    @Override
    public void modifyProjectTest(String projectId, List<DtoProjectTest> projectTestList) {
        String key = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_ProjectTest.getValue()) + projectId;
        if (!redisTemplate.hasKey(key)) {
            List<DtoProjectTest> list = sampleFolderService.findProjectTest(projectId);
            this.saveProjectTest(projectId, list);
        }
        Map<String, List<DtoProjectTest>> projectTestMap = projectTestList.stream().collect(Collectors.groupingBy(DtoProjectTest::getSampleTypeId));
        Map<String, List<DtoProjectTest>> originalProjectTestMap = new HashMap<>();
        List<Object> dataList = redisTemplate.opsForHash().multiGet(key, projectTestMap.keySet());
        TypeLiteral<List<DtoProjectTest>> typeLiteral = new TypeLiteral<List<DtoProjectTest>>() {
        };
        for (Object s : dataList) {
            if (StringUtil.isNotNull(s)) try {
                List<DtoProjectTest> projectTests = JsonIterator.deserialize(s.toString(), typeLiteral);
                if (StringUtil.isNotNull(projectTests) && projectTests.size() > 0) {
                    originalProjectTestMap.put(projectTests.get(0).getSampleTypeId(), projectTests);
                }
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
            }
        }
        for (String sampleTypeId : projectTestMap.keySet()) {
            if (originalProjectTestMap.containsKey(sampleTypeId)) {
                List<String> removeItemIds = projectTestMap.get(sampleTypeId).stream().map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList());
                List<DtoProjectTest> list = projectTestMap.get(sampleTypeId);
                list.addAll(originalProjectTestMap.get(sampleTypeId).stream().filter(p -> !removeItemIds.contains(p.getAnalyseItemId())).collect(Collectors.toList()));
                redisTemplate.opsForHash().put(key, sampleTypeId, JsonStream.serialize(list));
            } else {
                redisTemplate.opsForHash().put(key, sampleTypeId, JsonStream.serialize(projectTestMap.get(sampleTypeId)));
            }
        }
        //设置过期时间
        redisTemplate.expire(key, ProCodeHelper.EXPIRE_HOUR, TimeUnit.HOURS);
    }

    /**
     * 移除项目指标
     *
     * @param projectId       项目id
     */
    @Transactional
    @Override
    public void removeProjectTest(String projectId) {
        //所有涉及到删除修改的操作均调用该接口，直接清掉缓存，等下次需要添加指标时进行重新加载
        String key = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_ProjectTest.getValue()) + projectId;
        redisTemplate.delete(key);
    }

    /**
     * 获取项目已有测试项目
     *
     * @param projectId      项目id
     * @param sampleTypeId   检测类型id
     * @param analyseItemIds 分析因子id集合
     * @return 已有测试项目
     */
    @Transactional
    @Override
    public List<DtoTest> getProjectTest(String projectId, String sampleTypeId, List<String> analyseItemIds) {
        String key = EnumPRORedis.getRedisKey(EnumPRORedis.PRO_OrgId_ProjectTest.getValue()) + projectId;
        List<DtoProjectTest> projectTests = new ArrayList<>();
        Boolean flag = true;
        if (!redisTemplate.hasKey(key)) {
            flag = false;
            List<DtoProjectTest> list = sampleFolderService.findProjectTest(projectId);
            this.saveProjectTest(projectId, list);
            projectTests = list.stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId)).collect(Collectors.toList());
        }
        if (flag) {
            Object json = redisTemplate.opsForHash().get(key, sampleTypeId);
            if (StringUtils.isNotNullAndEmpty(json)) {
                //解析相应的配置数据
                TypeLiteral<List<DtoProjectTest>> typeLiteral = new TypeLiteral<List<DtoProjectTest>>() {
                };
                projectTests = JsonIterator.deserialize(json.toString(), typeLiteral);
            }
        }
        //设置过期时间
        redisTemplate.expire(key, ProCodeHelper.EXPIRE_HOUR, TimeUnit.HOURS);
        List<String> testIds = projectTests.stream().filter(p -> analyseItemIds.contains(p.getAnalyseItemId())).map(DtoProjectTest::getTestId).collect(Collectors.toList());
        if (testIds.size() > 0) {
            return testService.findRedisByIds(testIds);
        }
        return new ArrayList<>();
    }
}
