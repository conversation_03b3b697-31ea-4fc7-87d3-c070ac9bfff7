package com.sinoyd.base.dto.customer;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 评价标准限值判定实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2024/03/13
 */
@Data
@Accessors(chain = true)
public class EvaluationIntervalVO {

    /**
     * 评价等级名称
     */
    private String evaLevelName;

    /**
     * 评价等级id
     */
    private String evaLevelId;

    /**
     * 排序值，用于判断限值类别，排序值越大，类别越好
     */
    private Integer orderNum;

    /**
     * 当前限值对应的分析因子id
     */
    private String analyzeItemId;

    /**
     * 下限
     */
    private String lowerLimit;

    /**
     * 下限运算符
     */
    private String lowerLimitSymbol;

    /**
     * 上限
     */
    private String upperLimit;

    /**
     * 上限运算符
     */
    private String upperLimitSymbol;


}
