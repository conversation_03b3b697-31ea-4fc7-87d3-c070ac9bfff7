package com.sinoyd.lims.lim.vo;

import com.sinoyd.lims.lim.dto.rcc.DtoHolidayConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoWorkdayConfig;
import lombok.Data;

import java.util.List;

/**
 * 工作日vo
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/1/19
 **/
@Data
public class WorkHolidayConfigVO {

    /**
     * 年份
     */
    private Integer year;

    /**
     * 工作日配置dto
     */
    private DtoWorkdayConfig dtoWorkdayConfig;

    /**
     * 节假日配置dto
     */
    private List<DtoHolidayConfig> dtoHolidayConfigList;
}
