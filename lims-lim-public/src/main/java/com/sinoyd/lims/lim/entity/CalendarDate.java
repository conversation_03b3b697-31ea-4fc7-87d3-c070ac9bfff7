package com.sinoyd.lims.lim.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 日历日期实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/19
 */
@MappedSuperclass
@ApiModel(description = "CalendarDate")
@Data
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
public class CalendarDate implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public CalendarDate() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 节假日名称
     */
    @Column(length = 50)
    @ApiModelProperty("节假日名称")
    @Length(message = "节假日名称{validation.message.length}", max = 50)
    private String holidayName;

    /**
     * 日历日期
     */
    @Column(nullable = false)
    @ApiModelProperty("日历日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date calendarDate;

    /**
     * 星期数
     */
    @Column(nullable = false)
    @ApiModelProperty("星期数")
    private Integer weekday;

    /**
     * 类型（0：工作日，1：休息日）
     */
    @Column(nullable = false)
    @ApiModelProperty("类型（0：工作日，1：休息日）")
    private Integer type;


    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}