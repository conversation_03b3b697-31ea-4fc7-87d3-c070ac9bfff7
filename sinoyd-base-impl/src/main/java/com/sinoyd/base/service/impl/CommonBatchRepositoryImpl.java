package com.sinoyd.base.service.impl;

import com.sinoyd.base.service.CommonBatchRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 通用批处理repository
 *
 * <AUTHOR>
 * @version V1.0.0 2022-03-28
 * @since V1.0.0
 */
@Repository("commonBatchRepository")
@Slf4j
public class CommonBatchRepositoryImpl implements CommonBatchRepository {

    private NamedParameterJdbcTemplate jdbcTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<? extends BaseEntity> entities) {
        String sql = buildInsertSql(entities.get(0));
        jdbcTemplate.batchUpdate(sql, SqlParameterSourceUtils.createBatch(entities.toArray()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<? extends BaseEntity> entities) {
        String sql = buildUpdateSql(entities.get(0));
        jdbcTemplate.batchUpdate(sql, SqlParameterSourceUtils.createBatch(entities.toArray()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(Collection<?> ids, Class<?> entityClass) {
        String sql = buildDeleteSql(entityClass);
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("ids", new ArrayList<>(ids));
        jdbcTemplate.update(sql, params);
    }

    /**
     * 获取删除语句sql
     *
     * @param entityClass 实体类型
     * @return 删除sql语句
     */
    private String buildDeleteSql(Class<?> entityClass) {
        StringBuilder deleteSql = new StringBuilder();
        deleteSql.append("delete from ").append(getTableName(entityClass))
                .append(" where id in (:ids)");
        return deleteSql.toString();
    }

    /**
     * 获取插入语句sql
     *
     * @param entity 实体
     * @return sql语句
     */
    private String buildInsertSql(BaseEntity entity) {
        String tableName = getTableName(entity);
        StringBuilder insertSql = new StringBuilder();
        insertSql.append("INSERT INTO ").append(tableName)
                .append("(");
        Class<? extends BaseEntity> clazz = entity.getClass();
        Class<?> superClazz = clazz.getSuperclass();
        Field[] fields = superClazz.getDeclaredFields();
        StringBuilder fieldSql = new StringBuilder();
        StringBuilder valueSql = new StringBuilder();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            field.setAccessible(Boolean.TRUE);
            if (!"serialVersionUID".equals(field.getName())) {
                fieldSql.append(field.getName());
                valueSql.append(":").append(field.getName());
                if (i != fields.length - 1) {
                    fieldSql.append(", ");
                    valueSql.append(", ");
                } else {
                    fieldSql.append(")");
                    valueSql.append(")");
                }
            }
        }
        insertSql.append(fieldSql).append(" VALUES(").append(valueSql);
        return insertSql.toString();
    }

    /**
     * 获取更新语句sql
     *
     * @param entity 实体
     * @return sql语句
     */
    private String buildUpdateSql(BaseEntity entity) {
        String tableName = getTableName(entity);
        final BeanWrapper beanWrapper = new BeanWrapperImpl(entity);
        StringBuilder updateSql = new StringBuilder();
        updateSql.append("UPDATE ").append(tableName)
                .append(" SET ");
        Class<? extends BaseEntity> clazz = entity.getClass();
        Class<?> superClazz = clazz.getSuperclass();
        Field[] fields = superClazz.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            boolean fieldHasTransient = field.isAnnotationPresent(Transient.class);
            if (!"serialVersionUID".equals(field.getName()) && !fieldHasTransient && !field.getName().equals("id")) {
                Object v = beanWrapper.getPropertyValue(field.getName());
                //对象不是null值才做更新处理
                if(StringUtil.isNotNull(v)) {
                    updateSql.append(field.getName()).append(" = :").append(field.getName());
                    updateSql.append(",");
                }
            }
        }
        updateSql.replace(updateSql.lastIndexOf(","), updateSql.length(), " ");
        updateSql.append(" WHERE ID = :id ");
        return updateSql.toString();
    }


    /**
     * 获取表名
     *
     * @param entity 实体
     * @return 表名
     */
    private String getTableName(BaseEntity entity) {
        Class<? extends BaseEntity> clazz = entity.getClass();
        return getTableName(clazz);
    }

    /**
     * 获取表名
     *
     * @param entityClass 实体类型
     * @return 表名
     */
    private String getTableName(Class<?> entityClass) {
        Table tableAnnotation = entityClass.getAnnotation(Table.class);
        return tableAnnotation.name();
    }

    @Resource(name = "primaryJdbcTemplate")
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
    }
}