package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthority;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityTemp;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityValidate;

import java.util.List;
import java.util.Map;

/**
 * 文件夹权限接口   (建议：其实权限可以做即时修改)
 * <AUTHOR>
 * @version V1.0.0 2018/12/6
 * @since V100R001
 */
public interface DocAuthorityService extends IBaseJpaService<DtoDocAuthority, String> {
    /**
     * 获取常量中配置的权限，用于组成column
     * <p>
     * 根据常量的父Id或者常量的编码获取权限列表
     * <p>
     * 返回的列表是{@code pb.getData() }
     *
     * @param criteria 检索条件
     */
    Map<String, Object> getAuthorityList(BaseCriteria criteria);

    /**
     * 保存权限
     * <p>如果权限不存在则创建权限
     * <p>
     * 如果权限存在，则通过更新authState权限状态来确定是否有权限
     * <p>
     * 无效：如果定义了dto那就按照dto中的权限实体列表更新
     * <p>
     * 如果使用了List<Map>那就按照对应权限的Key的Id查找权限实体更新
     * 
     * @param dtoDocAuthorityTemps List<Map>，map内一个角色实体和一个权限实体的List
     */
    void saveAll(List<DtoDocAuthorityTemp> dtoDocAuthorityTemps);

    /**
     * 通过权限RowGuid权限验证是否拥有对某个文件夹/文件的操作权限
     * 
     * @param dtoDocAuthorityValidate       文件夹 文档权限
     * @return 是否拥有权限
     */
    Boolean validateAuth(DtoDocAuthorityValidate dtoDocAuthorityValidate);

    /**
     * 批量下载文件权限验证
     *
     * @return 能下载的文件标识
     */
    List<String> validateFileBatchDownload(List<String> ids);
}