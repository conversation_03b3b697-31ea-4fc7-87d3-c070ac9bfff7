package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2Test;

import java.util.List;


/**
 * 原始记录单相关配置数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
public interface RecordConfig2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoRecordConfig2Test, String> {

    /**
     * 根据记录id查询相应数据
     *
     * @param recordConfigId 记录配置id
     * @return 返回数据
     */
    List<DtoRecordConfig2Test> findByRecordConfigId(String recordConfigId);

    /**
     * 根据ids获取相应的数据
     * @param recordConfigIds 记录单配置ids
     * @return 返回数据
     */
    List<DtoRecordConfig2Test> findByRecordConfigIdIn(List<String> recordConfigIds);

    /**
     * 找到原始记录单的id
     *
     * @param testIds 根据测试项目找到唯一的原始记录单配置ids
     * @return 获取原始记录单id
     */
    List<DtoRecordConfig2Test> findByTestIdIn(List<String> testIds);
}