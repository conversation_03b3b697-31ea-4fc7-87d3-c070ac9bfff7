-- 供应商V2评价
CREATE TABLE "TB_BASE_ENTERPRISEEVALUATE"
(
    "ID"                VARCHAR(50)                                                 NOT NULL,
    "ENTID"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "YEARSN"            VARCHAR(10)  DEFAULT ''                                     NOT NULL,
    "CERTNAME"          VARCHAR(255),
    "CERTCODE"          VARCHAR(255),
    "CERTEFFECTIVETIME" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "ISQUALIFIED"       BIT          DEFAULT 0                                      NOT NULL,
    "ORGID"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATOR"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATEDATE"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "DOMAINID"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFIER"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFYDATE"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT                 CLUSTER PRIMARY KEY("ID")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_BASE_ENTERPRISEEVALUATE" IS '供应商评价信息';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."ID" IS 'id';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."ENTID" IS '供应商id';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."YEARSN" IS '年度';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."CERTNAME" IS '资质证书';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."CERTCODE" IS '证书编号';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."CERTEFFECTIVETIME" IS '有效期至';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."ISQUALIFIED" IS '是否合格供应商';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."ORGID" IS '组织机构id';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."CREATOR" IS '创建人';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."CREATEDATE" IS '创建时间';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."DOMAINID" IS '所属实验室';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."MODIFIER" IS '修改人';

COMMENT
ON COLUMN "TB_BASE_ENTERPRISEEVALUATE"."MODIFYDATE" IS '修改时间';





