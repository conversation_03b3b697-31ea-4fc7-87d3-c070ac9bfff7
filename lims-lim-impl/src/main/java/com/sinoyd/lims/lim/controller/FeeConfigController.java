package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.FeeConfigService;
import com.sinoyd.lims.lim.criteria.FeeConfigCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoFeeConfig;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FeeConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @Api(tags = "示例: FeeConfig服务")
 @RestController
 @RequestMapping("api/lim/feeConfig")
 @Validated
 public class FeeConfigController extends BaseJpaController<DtoFeeConfig, String,FeeConfigService> {


    /**
     * 分页动态条件查询FeeConfig
     * @param feeConfigCriteria 条件参数
     * @return RestResponse<List<FeeConfig>>
     */
     @ApiOperation(value = "分页动态条件查询FeeConfig", notes = "分页动态条件查询FeeConfig")
     @GetMapping
     public RestResponse<List<DtoFeeConfig>> findByPage(FeeConfigCriteria feeConfigCriteria) {
         PageBean<DtoFeeConfig> pageBean = super.getPageBean();
         RestResponse<List<DtoFeeConfig>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, feeConfigCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FeeConfig
     * @param id 主键id
     * @return RestResponse<DtoFeeConfig>
     */
     @ApiOperation(value = "按主键查询FeeConfig", notes = "按主键查询FeeConfig")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFeeConfig> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFeeConfig> restResponse = new RestResponse<>();
         DtoFeeConfig feeConfig = service.findOne(id);
         restResponse.setData(feeConfig);
         restResponse.setRestStatus(StringUtil.isNull(feeConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FeeConfig
     * @param feeConfig 实体列表
     * @return RestResponse<DtoFeeConfig>
     */
     @ApiOperation(value = "新增FeeConfig", notes = "新增FeeConfig")
     @PostMapping
     public RestResponse<DtoFeeConfig> create(@Validated @RequestBody DtoFeeConfig feeConfig) {
         RestResponse<DtoFeeConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.save(feeConfig));
         return restResponse;
      }

     /**
     * 新增FeeConfig
     * @param feeConfig 实体列表
     * @return RestResponse<DtoFeeConfig>
     */
     @ApiOperation(value = "修改FeeConfig", notes = "修改FeeConfig")
     @PutMapping
     public RestResponse<DtoFeeConfig> update(@Validated @RequestBody DtoFeeConfig feeConfig) {
         RestResponse<DtoFeeConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.update(feeConfig));
         return restResponse;
      }

    /**
     * "根据id批量删除FeeConfig
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FeeConfig", notes = "根据id批量删除FeeConfig")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }