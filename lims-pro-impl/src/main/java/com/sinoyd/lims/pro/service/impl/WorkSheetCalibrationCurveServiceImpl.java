package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoCurve;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.CurveService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseOriginalJson;
import com.sinoyd.lims.pro.dto.customer.DtoWorkSheetCalibrationCurveTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.AnalyseOriginalRecordRepository;
import com.sinoyd.lims.pro.repository.WorkSheetCalibrationCurveDetailRepository;
import com.sinoyd.lims.pro.repository.WorkSheetCalibrationCurveRepository;
import com.sinoyd.lims.pro.service.NewLogService;
import com.sinoyd.lims.pro.service.WorkSheetCalibrationCurveDetailService;
import com.sinoyd.lims.pro.service.WorkSheetCalibrationCurveService;
import com.sinoyd.lims.pro.service.WorkSheetService;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * WorkSheetCalibrationCurve操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Service
public class WorkSheetCalibrationCurveServiceImpl extends BaseJpaServiceImpl<DtoWorkSheetCalibrationCurve, String, WorkSheetCalibrationCurveRepository> implements WorkSheetCalibrationCurveService {
    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private CurveService curveService;

    @Autowired
    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private WorkSheetService workSheetService;

    @Autowired
    @Lazy
    private WorkSheetCalibrationCurveDetailService workSheetCalibrationCurveDetailService;

    @Autowired
    private WorkSheetCalibrationCurveDetailRepository workSheetCalibrationCurveDetailRepository;

    @Override
    public void findByPage(PageBean<DtoWorkSheetCalibrationCurve> pb, BaseCriteria workSheetCalibrationCurveCriteria) {
        pb.setEntityName("DtoWorkSheetCalibrationCurve a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, workSheetCalibrationCurveCriteria);
    }

    /**
     * 按照标线id查询分析的检测单数据
     */
    @Override
    public void findWorkSheetFolderByPage(PageBean<DtoWorkSheetFolder> pb, BaseCriteria workSheetCurveCriteria) {
        pb.setEntityName("DtoWorkSheetCalibrationCurve wsc,DtoWorkSheet w,DtoWorkSheetFolder wsf");
        pb.setSelect("select wsf,w.id as workSheetId");
        comRepository.findByPage(pb, workSheetCurveCriteria);

        List<DtoWorkSheetFolder> datas = pb.getData();
        List<DtoWorkSheetFolder> newDatas = new ArrayList<>();

        Iterator<DtoWorkSheetFolder> ite = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoWorkSheetFolder folder = (DtoWorkSheetFolder) objs[0];
            String workSheetId = (String) objs[1];
            folder.setWorkSheetId(workSheetId);
            newDatas.add(folder);
        }
        pb.setData(newDatas);
    }

    /**
     * 按子检测单id及测试项目id获取校准曲线信息
     *
     * @param workSheetId 子检测单id
     * @param testId      测试项目id
     * @return 返回校准曲线信息
     */
    @Override
    public DtoWorkSheetCalibrationCurveTemp findByTestIdAndWorkSheetId(String workSheetId, String testId) {
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoWorkSheetCalibrationCurve> pb = new PageBean<>();
        pb.setEntityName("DtoWorkSheetCalibrationCurve w,DtoCurve c");
        pb.setSelect("select w,c");
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.addCondition(" and w.standardCurveId = c.id");
        pb.addCondition(" and w.worksheetId = :workSheetId");
        values.put("workSheetId", workSheetId);
        pb.addCondition(" and c.testId = :testId");
        values.put("testId", testId);
        comRepository.findByPage(pb, values);
        if (StringUtil.isNotNull(pb.getData()) && pb.getData().size() > 0) {
            Object obj = pb.getData().get(0);
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoWorkSheetCalibrationCurve workSheetCalibrationCurve = (DtoWorkSheetCalibrationCurve) objs[0];
            DtoCurve curve = (DtoCurve) objs[1];
            DtoWorkSheetCalibrationCurveTemp temp = new DtoWorkSheetCalibrationCurveTemp();
            BeanUtils.copyProperties(curve, temp);
            temp.setId(workSheetCalibrationCurve.getId());
            temp.setWorksheetId(workSheetId);
            temp.setStandardCurveId(workSheetCalibrationCurve.getStandardCurveId());
            temp.setKValue(curve.getKValue());
            temp.setBValue(curve.getBValue());
            temp.setEquation(this.getEquation(curve));
            temp.setCurveDetail(workSheetCalibrationCurveDetailService.findByWorkSheetCalibrationCurveId(workSheetCalibrationCurve.getId()));
            return temp;
        }
        return null;
    }

    /**
     * 保存校准曲线信息
     *
     * @param temp 校准曲线信息
     * @return 明细
     */
    @Override
    @Transactional
    public DtoWorkSheetCalibrationCurveTemp saveWorkSheetCalibrationCurve(DtoWorkSheetCalibrationCurveTemp temp) {
        DtoWorkSheetCalibrationCurve dto = super.findOne(temp.getId());
        Boolean isCreate = false;
        if (StringUtil.isNull(dto)) {
            isCreate = true;
            dto = new DtoWorkSheetCalibrationCurve();
            dto.setCheckDate(new Date());
        }else{
            dto.setId(temp.getId());
        }
        DtoWorkSheet workSheet = workSheetService.findOne(temp.getWorksheetId());
        //保存公式参数数据
        List<DtoAnalyseData> anaData = analyseDataRepository.findByWorkSheetFolderIdIn(Collections.singletonList(workSheet.getParentId()));
        anaData = anaData.stream().filter(p->workSheet.getId().equals(p.getWorkSheetId())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(anaData)){
            List<String> analyseDataIds = anaData.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            List<DtoAnalyseOriginalRecord> originalRecords = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataIds);
            if (StringUtil.isNotEmpty(originalRecords)){
                for (DtoAnalyseOriginalRecord originalRecord : originalRecords) {
                    TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
                    };
                    List<DtoAnalyseOriginalJson> oldParamsTestFormula = JsonIterator.deserialize(originalRecord.getJson(), typeLiteral);
                    for (DtoAnalyseOriginalJson obj : oldParamsTestFormula) {
                        if ("k".equalsIgnoreCase(obj.getAlias())){
                            obj.setDefaultValue(temp.getKValue());
                        }
                        if ("b".equalsIgnoreCase(obj.getAlias())){
                            obj.setDefaultValue(temp.getBValue());
                        }
                    }
                    originalRecord.setJson(JsonStream.serialize(oldParamsTestFormula));
                }
                analyseOriginalRecordRepository.save(originalRecords);
            }
        }


        dto.setStandardCurveId(temp.getStandardCurveId());
        dto.setWorksheetId(temp.getWorksheetId());
        DtoCurve curve = curveService.findOne(temp.getStandardCurveId());
        if (isCreate) {
            repository.save(dto);
            newLogService.createLog(temp.getWorksheetId(), String.format("新增标准曲线,回归方程:%s", StringUtil.isNotNull(curve) ? this.getEquation(curve) : ""), "", EnumPRO.EnumLogType.检测单基本信息.getValue(), EnumPRO.EnumLogObjectType.检测单.getValue(), EnumPRO.EnumLogOperateType.更新检测单标准曲线.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        } else {
            comRepository.merge(dto);
            newLogService.createLog(temp.getWorksheetId(), String.format("更新标准曲线,回归方程:%s", StringUtil.isNotNull(curve) ? this.getEquation(curve) : ""), "", EnumPRO.EnumLogType.检测单基本信息.getValue(), EnumPRO.EnumLogObjectType.检测单.getValue(), EnumPRO.EnumLogOperateType.更新检测单标准曲线.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }

//        if (StringUtil.isEmpty(temp.getCurveDetail()) && isCreate) {
//            List<DtoWorkSheetCalibrationCurveDetail> workSheetCalibrationCurveDetails = this.getDefaultDetails(temp.getId());
//            temp.setCurveDetail(workSheetCalibrationCurveDetails);
//        } else {
//            workSheetCalibrationCurveDetailRepository.deleteByWorkSheetCalibrationCurveId(temp.getId());
//        }
//        workSheetCalibrationCurveDetailRepository.save(temp.getCurveDetail());
//        //如果在不是新增又没有curveDatail时候判定为清空了曲线
//        if (!isCreate && StringUtil.isEmpty(temp.getCurveDetail())) {
//            repository.delete(dto);
//            workSheetCalibrationCurveDetailRepository.deleteByWorkSheetCalibrationCurveId(temp.getId());
//        }
        return temp;
    }

    @Override
    public List<DtoWorkSheetCalibrationCurve> createWorkSheetCarveCheck(List<DtoWorkSheet> workSheets) {
        //测试项目ids
        List<String> testIds = workSheets.stream().map(DtoWorkSheet::getTestId).distinct().collect(Collectors.toList());
        List<DtoCurve> curveList = curveService.findByTestIds(testIds);
        List<DtoWorkSheetCalibrationCurve> workSheetCalibrationCurves = new ArrayList<>();
        List<DtoWorkSheetCalibrationCurveDetail> workSheetCalibrationCurveDetails = new ArrayList<>();
        for (DtoWorkSheet dtoWorkSheet : workSheets) {
            String testId = dtoWorkSheet.getTestId();
            Optional<DtoCurve> option = curveList.stream().filter(p -> p.getTestId().equals(testId)).sorted(Comparator.comparing(DtoCurve::getConfigDate).reversed()).findFirst();
            if (StringUtil.isNotNull(option) && option.isPresent()) {
                DtoCurve dtoCurve = option.get();
                DtoWorkSheetCalibrationCurve workSheetCalibrationCurve = new DtoWorkSheetCalibrationCurve();
                workSheetCalibrationCurve.setStandardCurveId(dtoCurve.getId());
                workSheetCalibrationCurve.setWorksheetId(dtoWorkSheet.getId());
                workSheetCalibrationCurve.setCheckDate(new Date());
                workSheetCalibrationCurve.setK(dtoCurve.getKValue());
                workSheetCalibrationCurve.setB(dtoCurve.getBValue());
                workSheetCalibrationCurve.setC(dtoCurve.getCValue());
                workSheetCalibrationCurves.add(workSheetCalibrationCurve);
                workSheetCalibrationCurveDetails.addAll(this.getDefaultDetails(workSheetCalibrationCurve.getId()));
            }
        }
        workSheetCalibrationCurveDetailRepository.save(workSheetCalibrationCurveDetails);
        repository.save(workSheetCalibrationCurves);
        return workSheetCalibrationCurves;
    }

    /**
     * 获取默认校准曲线明细
     *
     * @param workSheetCalibrationCurveId 校准曲线id
     * @return 明细
     */
    private List<DtoWorkSheetCalibrationCurveDetail> getDefaultDetails(String workSheetCalibrationCurveId) {
        List<DtoWorkSheetCalibrationCurveDetail> workSheetCalibrationCurveDetails = new ArrayList<>();
        for (Integer i = 0; i < 13; i++) {
            DtoWorkSheetCalibrationCurveDetail carveCheckDetail = new DtoWorkSheetCalibrationCurveDetail();
            carveCheckDetail.setAbsorbance("");
            if (i == 0 || i == 1) {
                carveCheckDetail.setAddAmount("0.00");
                carveCheckDetail.setAddVolume("0.00");
            } else {
                carveCheckDetail.setAddAmount("");
                carveCheckDetail.setAddVolume("");
            }
            carveCheckDetail.setAnalyseCode("");
            if (i == 11) {
                carveCheckDetail.setAnalyseCode("低浓度点");
            } else if (i == 12) {
                carveCheckDetail.setAnalyseCode("高浓度点");
            }
            carveCheckDetail.setAbsorbanceB("");
            carveCheckDetail.setLessBlankAbsorbance("");
            carveCheckDetail.setRelativeDeviation("");
            carveCheckDetail.setAValueTSF("");
            carveCheckDetail.setAValueTTZ("");
            carveCheckDetail.setWorkSheetCalibrationCurveId(workSheetCalibrationCurveId);
            workSheetCalibrationCurveDetails.add(carveCheckDetail);
        }
        return workSheetCalibrationCurveDetails;
    }

    /**
     * 获取方程
     *
     * @param curve 标线
     * @return 方程
     */
    private String getEquation(DtoCurve curve) {
        String equation = "";
        if (curve.getCurveType().equals(EnumLIM.EnumCurveType.二次型.getValue())) {
            equation = String.format("y=%s xʌ2 %s x %s", curve.getKValue(),
                    MathUtil.isNumeral(curve.getBValue()) && MathUtil.getBigDecimal(curve.getBValue()).compareTo(BigDecimal.ZERO) > 0 ? "+" + curve.getBValue() : curve.getBValue(),
                    MathUtil.isNumeral(curve.getCValue()) && MathUtil.getBigDecimal(curve.getCValue()).compareTo(BigDecimal.ZERO) > 0 ? "+" + curve.getCValue() : curve.getCValue());
        } else if (curve.getCurveType().equals(EnumLIM.EnumCurveType.Log型.getValue())) {
            equation = String.format("y=%s lgx %s", curve.getKValue(),
                    MathUtil.isNumeral(curve.getBValue()) && MathUtil.getBigDecimal(curve.getBValue()).compareTo(BigDecimal.ZERO) > 0 ? "+" + curve.getBValue() : curve.getBValue());
        } else {
            equation = String.format("y=%s x %s", curve.getKValue(),
                    MathUtil.isNumeral(curve.getBValue()) && MathUtil.getBigDecimal(curve.getBValue()).compareTo(BigDecimal.ZERO) > 0 ? "+" + curve.getBValue() : curve.getBValue());
        }
        equation = equation.replace(" ", "");
        return equation;
    }
}