package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.lims.pro.dto.customer.DtoProjectTest;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;
import com.sinoyd.lims.pro.service.SampleFolderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.SamplingFrequencyTestService;
import com.sinoyd.lims.pro.criteria.SamplingFrequencyTestCriteria;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * 频次指标服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: SamplingFrequencyTest服务")
 @RestController
 @RequestMapping("api/pro/samplingFrequencyTest")
 public class SamplingFrequencyTestController extends BaseJpaController<DtoSamplingFrequencyTest, String,SamplingFrequencyTestService> {

    @Autowired
    private SampleFolderService sampleFolderService;

    /**
     * 新增频次指标
     *
     * @param samplingFrequencyTest 实体列表
     * @return RestResponse<DtoSamplingFrequencyTest>
     */
    @ApiOperation(value = "新增频次指标", notes = "新增频次指标")
    @PostMapping
    public RestResponse<List<DtoProjectTest>> create(@RequestBody DtoSamplingFrequencyTest samplingFrequencyTest) {
        RestResponse<List<DtoProjectTest>> restResponse = new RestResponse<>();
        restResponse.setData(sampleFolderService.addFrequencyAnalyseItems(samplingFrequencyTest.getSamplingFrequencyIds(), samplingFrequencyTest.getAnalyseItemIds(), samplingFrequencyTest.getTestIds()));
        return restResponse;
    }

    /**
     * 修改频次指标
     *
     * @param samplingFrequencyTest 实体
     * @return RestResponse<DtoSamplingFrequencyTest>
     */
    @ApiOperation(value = "修改频次指标", notes = "修改频次指标")
    @PutMapping
    public RestResponse<DtoSampleFolderTemp> update(@RequestBody DtoSamplingFrequencyTest samplingFrequencyTest) {
        RestResponse<DtoSampleFolderTemp> restResponse = new RestResponse<>();
        restResponse.setData(sampleFolderService.modifySamplingFrequencyTest(samplingFrequencyTest.getSamplingFrequencyId(), samplingFrequencyTest.getAnalyseItemIds(), samplingFrequencyTest.getTestIds()));
        return restResponse;
    }

    /**
     * "批量删除频次指标
     *
     * @param samplingFrequencyTest 实体
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除频次指标", notes = "批量删除频次指标")
    @DeleteMapping
    public RestResponse<Boolean> delete(@RequestBody DtoSamplingFrequencyTest samplingFrequencyTest) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        sampleFolderService.deleteFrequencyAnalyseItems(samplingFrequencyTest.getSamplingFrequencyIds(), samplingFrequencyTest.getAnalyseItemIds());
        restResp.setData(true);
        return restResp;
    }

    /**
     * "批量删除频次指标
     *
     * @param samplingFrequencyTest 实体
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除频次指标", notes = "批量删除频次指标")
    @DeleteMapping("/v2")
    public RestResponse<Boolean> deleteByTestIds(@RequestBody DtoSamplingFrequencyTest samplingFrequencyTest) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        sampleFolderService.deleteSampleTests(samplingFrequencyTest.getSampleIds(), samplingFrequencyTest.getTestIds());
        restResp.setData(true);
        return restResp;
    }
}