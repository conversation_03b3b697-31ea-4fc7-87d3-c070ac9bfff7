package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;

import com.sinoyd.lims.lim.dto.lims.DtoExamineType;
import com.sinoyd.lims.lim.dto.lims.DtoExamineTypeRecord;
import com.sinoyd.lims.lim.repository.lims.ExamineTypeRecordRepository;
import com.sinoyd.lims.lim.repository.lims.ExamineTypeRepository;
import com.sinoyd.lims.lim.service.ExamineTypeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考核类型填写记录操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/14
 * @since V100R001
 */
@Service
public class ExamineTypeRecordServiceImpl extends BaseJpaServiceImpl<DtoExamineTypeRecord, String, ExamineTypeRecordRepository> implements ExamineTypeRecordService {

    @Autowired
    private ExamineTypeRepository examineTypeRepository;

    @Override
    public DtoExamineTypeRecord findAttachPath(String id) {
        return repository.findOne(id);
    }

    @Override
    @Transactional
    public List<DtoExamineTypeRecord> batchAddRecord(List<DtoExamineTypeRecord> list) {
        //更新考核项目进度
        DtoExamineType examineType =  examineTypeRepository.findOne(list.get(0).getExamineTypeId());
        Integer maxPorgress = list.stream().map(DtoExamineTypeRecord::getProgress).max(Integer::compare).get();
        examineType.setProgress(maxPorgress);
        examineTypeRepository.save(examineType);
        return repository.save(list);
    }

    @Override
    public List<DtoExamineTypeRecord> findAllByExamineTypeId(String typeId) {
        return repository.findAllByExamineTypeId(typeId);
    }

    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        DtoExamineTypeRecord record = repository.findOne((String) id);
        // 更新考核项目进度
        List<DtoExamineTypeRecord>  list = repository.findAllByExamineTypeId(record.getExamineTypeId());
        list = list.stream().filter(r->!id.equals(r.getId())).collect(Collectors.toList());
        Integer maxPorgress = 0;
        if(!list.isEmpty()){
            maxPorgress = list.stream().map(DtoExamineTypeRecord::getProgress).max(Integer::compare).get();
        }
        DtoExamineType examineType =  examineTypeRepository.findOne(record.getExamineTypeId());
        examineType.setProgress(maxPorgress);
        examineTypeRepository.save(examineType);
        return super.logicDeleteById(id);
    }
}
