package com.sinoyd.lims.lim.service.transform;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.ParamsPartFormulaMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.verify.TransformParamsPartFormulaVerifyHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 测试项目数据迁移测试项目部分公式导入策略类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
@Component
@Order(110)
public class ParamsPartFormulaStrategy implements TransformImportStrategy {
    /**
     * sheet页序号
     */
    private static final int sheetIndex = 7;

    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    private TransformParamsPartFormulaVerifyHandler transformParamsPartFormulaVerifyHandler;

    private ParamsPartFormulaMapper paramsPartFormulaMapper;

    @Override
    @Transactional
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception {
        //参数初始化
        handleInit();
        params.setStartSheetIndex(sheetIndex);
        params.setVerifyHandler(transformParamsPartFormulaVerifyHandler);
        //获取校验结果集
        ExcelImportResult<DtoExportParamsPartFormula> result = ExcelImportUtil.importExcelMore(inputStream, DtoExportParamsPartFormula.class, params);
        // 检验失败抛出异常并返回错误文档
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(sheetIndex, "导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        //构建可保存数据
        List<DtoParamsPartFormula> waitSaveList = new ArrayList<>();
        buildRightData(result, waitSaveList);
        //数据保存
        paramsPartFormulaRepository.save(waitSaveList);
    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {
        List<DtoExportParamsPartFormula> paramsPartFormulaList = testDependentData.getParamsPartFormulaList();
        if (StringUtil.isNotEmpty(paramsPartFormulaList)) {

            List<DtoExportParamsFormula> paramsFormulaList = exportData.getParamsFormulaList();
            List<String> formulaIds = paramsFormulaList.stream().map(DtoExportParamsFormula::getId).collect(Collectors.toList());
            // 导入数据
            List<DtoParamsPartFormula> paramsPartFormulas = paramsPartFormulaList.stream().filter(p -> !formulaIds.contains(p.getFormulaId()))
                    .map(p -> paramsPartFormulaMapper.toDtoParamsPartFormula(p)).collect(Collectors.toList());
            importTestTemp.setParamsPartFormulaTemps(paramsPartFormulas);

            // 导出数据
            if (StringUtil.isNotEmpty(formulaIds)) {
                List<DtoExportParamsPartFormula> exportParamsPartFormulas = paramsPartFormulaList.stream().filter(p -> formulaIds.contains(p.getFormulaId())).collect(Collectors.toList());
                exportData.setParamsPartFormulaList(exportParamsPartFormulas);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoParamsPartFormula> paramsPartFormulaTemps = importTestTemp.getParamsPartFormulaTemps();
        if (StringUtil.isNotEmpty(paramsPartFormulaTemps)) {
            //已同步记录数
            int i = 0;
            for (DtoParamsPartFormula dtoParamsPartFormula : paramsPartFormulaTemps) {
                paramsPartFormulaRepository.save(dtoParamsPartFormula);
                webSocketServer.sendMessage(getMessage(paramsPartFormulaTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(paramsPartFormulaTemps.size(),0));
        }
    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.拓展公式表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.拓展公式表.getSource();
    }

    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.拓展公式表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        return null;
    }

    /**
     * 校验容器初始化
     */
    private void handleInit() {
        transformParamsPartFormulaVerifyHandler = new TransformParamsPartFormulaVerifyHandler();
        transformParamsPartFormulaVerifyHandler.setRepoDataList(paramsPartFormulaRepository.findAll());
        transformParamsPartFormulaVerifyHandler.setSheetExistDataList(new ArrayList<>());
    }

    /**
     * 构建可保存数据
     *
     * @param result       导入结果集
     * @param waitSaveList 待保存结果容器
     */
    private void buildRightData(ExcelImportResult<DtoExportParamsPartFormula> result, List<DtoParamsPartFormula> waitSaveList) {
        List<DtoExportParamsPartFormula> importList = result.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        for (DtoExportParamsPartFormula exportParamsPartFormula : importList) {
            DtoParamsPartFormula dtoParamsPartFormula = new DtoParamsPartFormula();
            BeanUtils.copyProperties(exportParamsPartFormula, dtoParamsPartFormula);
            waitSaveList.add(dtoParamsPartFormula);
        }
    }

    @Autowired
    public void setParamsPartFormulaRepository(ParamsPartFormulaRepository paramsPartFormulaRepository) {
        this.paramsPartFormulaRepository = paramsPartFormulaRepository;
    }

    @Autowired
    public void setParamsPartFormulaMapper(ParamsPartFormulaMapper paramsPartFormulaMapper) {
        this.paramsPartFormulaMapper = paramsPartFormulaMapper;
    }
}
