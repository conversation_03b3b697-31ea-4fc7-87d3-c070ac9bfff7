package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * ItemRelation查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemRelationCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类型（1：自检，2：上报）
     */
    private Integer type;

    /**
     * 关键字（分析项目名称、公式）
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and formula like :key");
            values.put("key", "%" + this.key + "%");
        }
        if (type == -1) {
            condition.append(" and (type = 1 or type =2)");
        } else if (type > 0) {
            condition.append(" and (type = :type)");
            values.put("type", type);
        }
        return condition.toString();
    }
}