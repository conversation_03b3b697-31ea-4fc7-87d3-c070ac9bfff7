package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * SamplingCarConfig查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamplingCarConfigCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String carId;
    /**
     * 关键字
     */
    private String key;
    /**
     * 检索开始时间
     */
    private String dtBegin;
    /**
     * 检索结束时间
     */
    private String dtEnd;
    /**
     * 操作人
     */
    private String operatorId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and s.objectId = r.id");
        condition.append(" and r.projectId = p.id");
        // 车辆Id检索
        if (StringUtil.isNotEmpty(this.carId)) {
            condition.append(" and s.carId = :carId");
            values.put("carId", this.carId);
        }
        // 使用人员检索
        if (StringUtil.isNotEmpty(operatorId)) {
            condition.append(" and r.senderId = :operatorId");
            values.put("operatorId", this.operatorId);
        }
        // 关键词检索
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (p.projectCode like :key or p.projectName like :key or r.recordCode like :key or p.customerName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        // 开始时间
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and r.sendTime >= :dtBegin");
            values.put("dtBegin", from);
        }
        // 结束时间
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and r.sendTime < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        condition.append(" order by r.sendTime desc");
        return condition.toString();
    }
}