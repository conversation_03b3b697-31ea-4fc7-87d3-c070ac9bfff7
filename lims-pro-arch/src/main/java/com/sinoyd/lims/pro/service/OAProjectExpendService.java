package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoOAProjectExpend;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;

/**
 * 项目支出 业务操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
public interface OAProjectExpendService extends IBaseJpaService<DtoOAProjectExpend, String> {
    /**
     * 添加项目支出并启动流程
     *
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<DtoOAProjectExpend> taskDto);


    /**
     * 查询任务详细信息
     *
     * @param taskId 任务ID
     * @return  任务详情
     */
    DtoOATaskDetail<DtoOAProjectExpend, String> findOATaskDetail(String taskId);


    /**
     * 最后将项目支出进行确认操作
     *
     * @param id 主键id
     */
    void confirm(String id);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask saveAsDraft(DtoOATaskCreate<DtoOAProjectExpend> taskDto);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask draftSave(DtoOATaskCreate<DtoOAProjectExpend> taskDto);

    /**
     * 草稿提交
     * @param taskDto  表单数据
     * @return         oa任务
     */
    String draftSubmit(DtoOATaskCreate<DtoOAProjectExpend> taskDto);
}
