package com.sinoyd.lims.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>监督计划子系统枚举类</p>
 *
 * <AUTHOR>
 * @version V0.0.1 2021/03/23
 * @since V0.0.1
 */
public class EnumMonitor {


    /**
     * 点位排序 类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPointType {
        环境质量(1),

        污染源(2);

        private Integer value;
    }

    /**
     * 排污许可证数据类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPollutantDisChargePermitType {
        /**
         * 自行监测数据
         */
        自行监测数据("自行监测数据", "monitorData", "PDPMonitorDataRequest");

        /**
         * 枚举值
         */
        private final String value;

        /**
         * 请求类型（用于远程请求参数数据）
         */
        private final String requestType;

        /**
         * 枚举对应的策略名称
         */
        private final String beanName;

        /**
         * 根据枚举值获取枚举
         *
         * @param value 枚举值
         * @return 枚举
         */
        public static EnumPollutantDisChargePermitType getEnumByValue(String value) {
            for (EnumPollutantDisChargePermitType enumPollutantDisChargePermitType : EnumPollutantDisChargePermitType.values()) {
                if (enumPollutantDisChargePermitType.getValue().equals(value)) {
                    return enumPollutantDisChargePermitType;
                }
            }
            return null;
        }
    }
}
