package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoLogForAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分析方法管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/14
 * @since V100R001
 */
public interface AnalyzeMethodService extends IBaseJpaService<DtoAnalyzeMethod, String> {

    /**
     * 替换分析方法
     *
     * @param analyzeMethod 新方法的实体
     */
    void replace(DtoAnalyzeMethod analyzeMethod);

    /**
     * 通过分析方法名称和标准编号获取分析方法
     *
     * @param analyzeMethodName
     * @param countryStandard
     * @return 分析方法实体
     */
    DtoAnalyzeMethod getByAnalyzeMethodNameAndCountryStandard(String analyzeMethodName, String countryStandard);

    /**
     * 重新定义接口（为了返回调用该接口类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回方法对象
     */
    @Override
    DtoAnalyzeMethod findOne(String id);


    /**
     * 根据Id获取有假删的数据
     *
     * @param ids 方法ids
     * @return 返回带有假删的用户数据
     */
    List<DtoAnalyzeMethod> findAllDeleted(List<String> ids);


    /**
     * 含假删的方法信息
     *
     * @return 返回方法信息
     */
    List<DtoAnalyzeMethod> findAllDeleted();

    /**
     * 相似度比较导出
     *
     * @param similarity 相似度%
     * @param response   响应体
     */
    void compareExport(Integer similarity, HttpServletResponse response);

    /**
     * 更新方法状态
     *
     * @param status 状态
     * @param ids    分析方法标识
     */
    void updateMethodStatus(Integer status, List<String> ids);

    /**
     * 获取状态变更日志
     *
     * @param id 分析方法标识
     * @return 日志列表
     */
    List<DtoLogForAnalyzeMethod> getStatusLog(String id);

    /**
     * 根据检测类型小类id获取方法
     *
     * @param sampleTypeId 检测类型小类id 逗号分割
     * @return 方法集合
     */
    List<DtoAnalyzeMethod> bySampleType(String sampleTypeId);
}