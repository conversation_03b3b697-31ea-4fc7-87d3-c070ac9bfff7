package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 流程日志查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NewLogCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 日志对象类型
     */
    private Integer logObjectType;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 领样单id
     */
    private String subId;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 检测单id
     */
    private String workSheetFolderId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 操作人
     */
    private String operatorId;

    /**
     * 费用id
     */
    private String costId;

    @Override
    public String getCondition() {
        values.clear();
        return "";
    }
}
