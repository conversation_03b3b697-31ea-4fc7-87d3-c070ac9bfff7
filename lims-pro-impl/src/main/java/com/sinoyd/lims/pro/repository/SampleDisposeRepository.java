package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSampleDispose;

import java.util.List;


/**
 * SampleDispose数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface SampleDisposeRepository extends IBaseJpaRepository<DtoSampleDispose, String> {

    /**
     * 根据样品id查询留样处置数据
     *
     * @param sampleId 样品id
     * @return 留样处置列表
     */
    List<DtoSampleDispose> findBySampleId(String sampleId);

    /**
     * 根据样品ids查询留样处置数据
     *
     * @param sampleIds 样品id集合
     * @return 留样处置数据
     */
    List<DtoSampleDispose> findBySampleIdIn(List<String> sampleIds);

}