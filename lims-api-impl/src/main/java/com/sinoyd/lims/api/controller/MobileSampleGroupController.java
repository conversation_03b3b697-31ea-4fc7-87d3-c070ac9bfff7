package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.SampleGroupRecordCriteria;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import com.sinoyd.lims.pro.service.SampleGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SampleGroup服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@Api(tags = "示例: SampleGroup服务")
@RestController
@RequestMapping("api/mobile/sampleGroup")
public class MobileSampleGroupController extends BaseJpaController<DtoSampleGroup, String, SampleGroupService> {
    /**
     * 按条件查询对应送样单下的SampleGroup
     *
     * @param sampleGroupRecordCriteria 条件对象
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "按送样单id查询SampleGroup", notes = "按送样单id查询SampleGroup")
    @GetMapping(path = "/receiveSampleRecord")
    public RestResponse<List<DtoSampleGroup>> findForRecord(SampleGroupRecordCriteria sampleGroupRecordCriteria) {
        RestResponse<List<DtoSampleGroup>> restResponse = new RestResponse<>();
        List<DtoSampleGroup> sampleGroupList = service.findForReceiveSampleRecord(sampleGroupRecordCriteria);
        restResponse.setData(sampleGroupList);
        restResponse.setRestStatus(StringUtil.isNull(sampleGroupList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 更新送样单下样品的SampleGroup信息
     *
     * @param criteria 实体列表
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "更新送样单下样品的SampleGroup信息", notes = "更新送样单下样品的SampleGroup信息")
    @PostMapping("/receiveSampleRecord/updateGroupInfo")
    public RestResponse<String> updateGroupInfo(@RequestBody SampleGroupRecordCriteria criteria) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.updateGroupInfo(criteria);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }

    /**
     * 修改送样单下的SampleGroup信息
     *
     * @param sampleGroup 实体列表
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "修改送样单下的SampleGroup信息", notes = "修改送样单下的SampleGroup信息")
    @PutMapping("/receiveSampleRecord")
    public RestResponse<DtoSampleGroup> updateForRecord(@RequestBody DtoSampleGroup sampleGroup) {
        RestResponse<DtoSampleGroup> restResponse = new RestResponse<>();
        service.updateForRecord(sampleGroup);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }

    /**
     * "根据id批量删除SampleGroup
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除SampleGroup", notes = "根据id批量删除SampleGroup")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}
