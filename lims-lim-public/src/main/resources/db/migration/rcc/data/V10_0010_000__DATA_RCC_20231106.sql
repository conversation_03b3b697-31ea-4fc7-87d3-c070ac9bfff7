-- 无组织评价结果表组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('4327d2ae-f104-418e-be74-67f4072d06ec', 'dtUnOrgGasPjTable', '无组织评价结果表组件', 'dtUnOrgGasPjTable',
        'dtUnOrgGasPjSrc', 10000, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-06 13:17:16', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2023-11-06 13:17:16');

-- 评价结果表头组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('6130da13-013a-4a25-a856-bd1f16fc691b', 'dtTableHeadPj', '评价结果表头组件', 'dtTableHeadPj', '', 0, 0, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2023-11-06 13:14:53',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-11-06 13:14:53');

-- 无组织评价结果表组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('ac693c4a-7752-4473-a42d-3e38988c1f5d', 'dtOrgGasPjTable', '有组织评价结果表组件', 'dtOrgGasPjTable', 'dtOrgGasPjSrc',
        10000, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2023-11-06 13:16:21', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2023-11-06 13:16:21');

-- 噪声评价结果表组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('d75b5323-5558-4581-8340-29aa43c39e46', 'dtNoisePjTable', '噪声评价结果表组件', 'dtNoisePjTable', 'dtNoisePjSrc', 10000,
        0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2023-11-06 13:18:14',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-11-06 13:18:14');

-- 废水评价结果表组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('f33aa5b0-7ddd-4a62-8d7a-2b219f574bad', 'dtWasteWaterPjTable', '废水评价结果表组件', 'dtWasteWaterPjTable',
        'dtWasteWaterPjSrc', 10000, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-06 13:17:47', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2023-11-06 13:17:47');


-- 有组织报告添加评价结果组件
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('8dad744c-04c6-4fc4-8b98-bd1ed212874b', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        'ac693c4a-7752-4473-a42d-3e38988c1f5d');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('e7af0094-f8f4-44d2-803d-f48ceb98b6f2', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        '6130da13-013a-4a25-a856-bd1f16fc691b');

-- 无组织报告添加评价结果组件
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('139399bd-6d04-49e6-bfca-21f2716d3b9b', '99bce985-d395-4f6d-8cb2-13c38fe3dd01',
        '6130da13-013a-4a25-a856-bd1f16fc691b');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('fd67f351-0c71-46d3-a93f-f98d3f818333', '99bce985-d395-4f6d-8cb2-13c38fe3dd01',
        '4327d2ae-f104-418e-be74-67f4072d06ec');

-- 废水报告添加评价结果组件
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('71255bc4-8075-4185-8646-d12d327bccea', '334fee80-2765-4c9a-a6a7-e06067742a92',
        '6130da13-013a-4a25-a856-bd1f16fc691b');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('15f08125-29bf-4699-a8ec-78b9612e1a29', '334fee80-2765-4c9a-a6a7-e06067742a92',
        'f33aa5b0-7ddd-4a62-8d7a-2b219f574bad');

