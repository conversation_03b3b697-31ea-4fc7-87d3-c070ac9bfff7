package com.sinoyd.base.config;

import com.sinoyd.base.filter.ChannelFilter;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 过滤器配置类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/07
 **/
@Configuration
public class FilterConfig {

    @Bean
    public ChannelFilter channelFilter(){
        return new ChannelFilter();
    }

    @Bean
    public ApplicationListener<ApplicationReadyEvent> appListener() {
        return event -> {
            FilterRegistrationBean registration = new FilterRegistrationBean();
            registration.setFilter(channelFilter());
            registration.addUrlPatterns("/*");
            registration.setOrder(1);
        };
    }
}
