package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * InstrumentRepairRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="InstrumentRepairRecord")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class InstrumentRepairRecord implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  InstrumentRepairRecord() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 仪器Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("仪器Id")
	private String instrumentId;
    
    /**
    * 维修申请ID
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("维修申请ID")
    @Length(message = "维修申请ID{validation.message.length}", max = 50)
	private String purchaseApplyID;
    
    /**
    * 维修申请单号
    */
    @Column(length=20)
    @ApiModelProperty("维修申请单号")
    @Length(max = 20, message = "维修申请单号不能超过20")
	private String requestNoteCode;
    
    /**
    * 维修结果(枚举：EnumRepairRusult：1合格、0不合格)
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("维修结果(枚举：EnumRepairRusult：1合格、0不合格)")
    private Integer repairResult;
    
    /**
    * 设备故障描述
    */
    @Column(length=1000)
    @ApiModelProperty("设备故障描述")
    @Length(max = 1000, message = "设备故障描述不能超过1000")
	private String failureDesc;
    
    /**
    * 描述人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("描述人id")
    private String failureDescPersonId;
    
    /**
    * 描述人
    */
    @Column(length=50)
    @ApiModelProperty("描述人")
	private String failureDescPerson;
    
    /**
    * 描述日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("描述日期")
    private Date failureDescDate;
    
    /**
    * 验证人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("验证人id")
    private String repairResultCheckerId;
    
    /**
    * 验证人
    */
    @Column(length=50)
    @ApiModelProperty("验证人")
    @Length(max = 50, message = "验证人不能超过50")
	private String repairResultChecker;
    
    /**
    * 验证日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("验证日期")
    private Date repairResultCheckDate;
    
    /**
    * 维修记录人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("维修记录人id")
    private String repairContentRecorderId;
    
    /**
    * 维修记录人
    */
    @Column(length=50)
    @ApiModelProperty("维修记录人")
    @Length(max = 50, message = "维修记录人不能超过50")
	private String repairContentRecorder;
    
    /**
    * 维修记录日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("维修记录日期")
    private Date repairContentRecordDate;
    
    /**
    * 费用
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @Digits(integer=18,fraction=2,message="费用整数位精度18小数位经度2")
    @ApiModelProperty("费用")
    private BigDecimal cost;
    
    /**
    * 维修内容
    */
    @Column(length=1000)
    @ApiModelProperty("维修内容")
    @Length(max = 1000, message = "维修内容不能超过1000")
    private String repairContent;
    
    /**
    * 验收情况
    */
    @Column(length=1000)
    @ApiModelProperty("验收情况")
    @Length(max = 1000, message = "验收情况不能超过1000")
	private String checkContent;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(max = 1000, message = "备注不能超过1000")
	private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }