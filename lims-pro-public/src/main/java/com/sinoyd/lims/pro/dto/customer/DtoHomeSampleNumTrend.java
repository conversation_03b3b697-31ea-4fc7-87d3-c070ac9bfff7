package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 首页样品数趋势
 * <AUTHOR>
 * @version V1.0.0 2020/5/11
 * @since V100R001
 */
@Data
public class DtoHomeSampleNumTrend {
    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 样品个数
     */
    private Integer sampleCount;

    /**
     * 每月明细
     */
    private List<DtoHomeTrendDetail> detail = new ArrayList<>();

    /**
     * 填充明细
     * @param beginTime 开始时间
     * @param endTime 结束时间
     */
    public void fillDetail(Date beginTime, Date endTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(beginTime);

        while (!calendar.getTime().after(endTime)) {
            Date time = calendar.getTime();
            if (this.detail.stream().noneMatch(p -> p.getTime().equals(time))) {
                this.detail.add(new DtoHomeTrendDetail(time, 0));
            }
            calendar.add(Calendar.MONTH, 1);
        }
    }
}
