package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoOutSample;
import com.sinoyd.lims.pro.dto.customer.DtoOutSampleSave;
import com.sinoyd.lims.pro.dto.customer.DtoSampleCopyParam;
import com.sinoyd.lims.pro.service.QMSampleService;
import com.sinoyd.lims.pro.service.SchemeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 质控项目样品服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/2/5
 * @since V100R001
 */
@Api(tags = "示例: 质控项目样品服务")
@RestController
@RequestMapping("api/pro/qmSample")
public class QMSampleController extends BaseJpaController<DtoSample, String,QMSampleService> {

    @Autowired
    private SchemeService schemeService;

    /**
     * 新增质控项目样品
     *
     * @param dto 实体
     * @return RestResponse<DtoReceiveSampleRecordTemp>
     */
    @ApiOperation(value = "新增质控项目样品", notes = "新增质控项目样品")
    @PostMapping
    public RestResponse<DtoOutSample> saveQMSample(@RequestBody @Validated DtoOutSample dto) {
        RestResponse<DtoOutSample> restResponse = new RestResponse<>();
        restResponse.setData(service.saveQmSample(dto));
        return restResponse;
    }

    /**
     * 修改外部样品
     *
     * @param dto 实体列表
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "修改外部样品", notes = "修改外部样品")
    @PutMapping
    public RestResponse<Boolean> updateQMSample(@RequestBody @Validated DtoOutSampleSave dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.updateQmSample(dto);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 复制样品
     *
     * @param dto 实体
     * @return RestResponse<DtoReceiveSampleRecordTemp>
     */
    @ApiOperation(value = "复制样品", notes = "复制样品")
    @PostMapping(path = "/copy")
    public RestResponse<Boolean> copySamples(@RequestBody DtoSampleCopyParam dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        schemeService.copyOutsideSample(dto.getIds(), dto.getReceiveId(), dto.getTimes(),dto.getSamplingTimeBegin());
        restResponse.setData(true);
        return restResponse;
    }
}
