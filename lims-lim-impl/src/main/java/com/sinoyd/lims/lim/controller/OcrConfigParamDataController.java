package com.sinoyd.lims.lim.controller;

import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParamData;
import com.sinoyd.lims.lim.service.OcrConfigParamDataService;
import org.springframework.web.bind.annotation.*;


/**
 * ocr对象参数数据
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@RestController
@RequestMapping("api/lim/ocrConfigParamData")
public class OcrConfigParamDataController extends BaseJpaController<DtoOcrConfigParamData, String, OcrConfigParamDataService> {
}
