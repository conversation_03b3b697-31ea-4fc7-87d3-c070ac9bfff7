package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoOutSourceData;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * 分包数据访问数据库操作接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/2/8
 */
public interface OutSourceDataRepository extends IBaseJpaRepository<DtoOutSourceData, String> {

    /**
     * 根据分析数据id查询
     *
     * @param id 分析数据id
     * @return 分包数据
     */
    DtoOutSourceData findByAnalyseDataId(String id);

    /**
     * 根据分析数据id查询
     *
     * @param ids 分析数据id
     * @return 分包数据
     */
    List<DtoOutSourceData> findByAnalyseDataIdIn(Collection<String> ids);

    /**
     * 根据分析数据ids批量删除
     *
     * @param ids 分析数据ids
     */
    @Transactional
    @Modifying
    @Query("update DtoOutSourceData a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.analyseDataId in :ids")
    void deleteByAnalyseDataIds(@Param("ids") Collection<String> ids,
                                @Param("modifier") String modifier,
                                @Param("modifyDate") Date modifyDate);

    /**
     * 查询分析方法不为null和空字符串的数据
     *
     * @param name 分析方法名称
     * @return 分包数据
     */
    List<DtoOutSourceData> findByAnalyzeMethodNameIsNotNullAndAnalyzeMethodNameNot(String name);

    /**
     * 查询量纲名称不为null和空字符串的数据
     *
     * @param name 量纲名称
     * @return 分包数据
     */
    List<DtoOutSourceData> findByDimensionNameIsNotNullAndDimensionNameNot(String name);
}