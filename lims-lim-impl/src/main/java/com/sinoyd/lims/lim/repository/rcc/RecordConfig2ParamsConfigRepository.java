package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2ParamsConfig;

import java.util.List;

/**
 * 采样单配置关联检测类型参数配置数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/6/27
 * @since V100R001
 */
public interface RecordConfig2ParamsConfigRepository extends IBaseJpaPhysicalDeleteRepository<DtoRecordConfig2ParamsConfig, String> {
    /**
     * 根据记录id获取关联参数数据
     *
     * @param recordId 记录单id
     * @return 查询结果
     */
    List<DtoRecordConfig2ParamsConfig> findByRecordConfigId(String recordId);

    /**
     * 根据记录id获取关联参数数据
     *
     * @param recordIds 记录单id
     * @return 查询结果
     */
    List<DtoRecordConfig2ParamsConfig> findByRecordConfigIdIn(List<String> recordIds);
}
