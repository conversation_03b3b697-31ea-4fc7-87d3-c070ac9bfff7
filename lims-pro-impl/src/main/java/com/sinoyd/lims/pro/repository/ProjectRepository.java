package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoProject;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * Project数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface ProjectRepository extends IBaseJpaRepository<DtoProject, String> {
    /**
     * 返回相同编号的个数
     *
     * @param projectCode 项目编号
     * @return 相同编号的个数
     */
    Integer countByProjectCode(String projectCode);

    /**
     * 返回相同编号的个数
     *
     * @param projectCode 项目编号
     * @param id          项目id
     * @return 相同编号的个数
     */
    Integer countByProjectCodeAndIdNot(String projectCode, String id);

    /**
     * 批量办结项目状态
     *
     * @param ids            样品的ids
     * @param samplingStatus 采毕状态
     * @param status         项目状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoProject p set p.status = :status,p.samplingStatus = :samplingStatus,p.modifyDate = :modifyDate,p.modifier = :modifier where p.id in :ids")
    Integer finishProject(@Param("ids") List<String> ids,
                          @Param("samplingStatus") Integer samplingStatus,
                          @Param("status") String status,
                          @Param("modifier") String modifier,
                          @Param("modifyDate") Date modifyDate);

    /**
     * 修改项目的冗余json信息
     *
     * @param id   项目的id
     * @param json json信息
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoProject p set p.json = :json where p.id = :id")
    Integer updateJson(@Param("id") String id, @Param("json") String json);

    /**
     * 修改项目的采毕状态
     *
     * @param id             项目的id
     * @param samplingStatus 采毕状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoProject p set p.samplingStatus = :samplingStatus,p.modifyDate = :modifyDate,p.modifier = :modifier where p.id = :id")
    Integer updateSamplingStatus(@Param("id") String id, @Param("samplingStatus") Integer samplingStatus,
                                 @Param("modifier") String modifier,
                                 @Param("modifyDate") Date modifyDate);


    /**
     * @param ids        样品ids
     * @param status     状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoProject p set p.status = :status,p.modifyDate = :modifyDate,p.modifier = :modifier  where p.id in :ids")
    Integer updateProjectStatus(@Param("ids") List<String> ids, @Param("status") String status,
                                @Param("modifier") String modifier,
                                @Param("modifyDate") Date modifyDate);

    /**
     * 按登记日期获取项目个数分组
     *
     * @param inceptTime 登记日期起
     * @return 进行分组得到相应的项目数
     */
    @Query("select p.inceptTime,count(p.id)  from DtoProject p where p.isDeleted=0 and p.inceptTime >= :inceptTime group by p.inceptTime")
    List<Object[]> countProjectNumGroupByInceptTime(@Param("inceptTime") Date inceptTime);

    /**
     * 根据项目编号查询
     *
     * @param projectCode 项目编号
     * @return 实体
     */
    DtoProject findByProjectCode(String projectCode);

    /**
     * 根据项目编号列表查询
     *
     * @param projectCodeList 项目编号列表
     * @return 项目列表
     */
    List<DtoProject> findByProjectCodeIn(List<String> projectCodeList);

    /**
     * 通过订单id获取项目信息
     * @param orderId 订单id
     * @return 项目信息
     */
    List<DtoProject> findByOrderId(String orderId);

    /**
     * 根据主项目 查询子项目信息
     * @param ids 主项目id
     * @return 项目集合
     */
    List<DtoProject> findByParentIdIn(Collection<?> ids);

    /**
     * 根据项目类型获取项目信息
     * @param projectTypeIds 项目类型ids
     * @return 项目集合
     */
    List<DtoProject> findByProjectTypeIdIn(Collection<?> projectTypeIds);
}