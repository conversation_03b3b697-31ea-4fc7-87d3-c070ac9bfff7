package com.sinoyd.lims.lim.criteria;

import java.util.Calendar;
import java.util.Date;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仪器维护记录查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentMaintainRecordCriteria extends BaseCriteria {

    /**
     * 仪器id（默认当前仪器的id）
     */
    private String instrumentId;
    /**
     * 检索开始时间
     */
    private String dtBegin;
    /**
     * 检索结束时间
     */
    private String dtEnd;
    /**
     * 维护人员下拉框（空Guid代表所有）
     */
    private String maintainId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and startTime >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and endTime < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(instrumentId)
                && !UUIDHelper.GUID_EMPTY.equals(this.instrumentId)) {
            condition.append(" and (instrumentId = :instrumentId)");
            values.put("instrumentId", this.instrumentId);
        }
        if (StringUtils.isNotNullAndEmpty(maintainId)
                && !UUIDHelper.GUID_EMPTY.equals(this.maintainId)) {
            condition.append(" and (mainTainPersonId = :maintainId)");
            values.put("maintainId", this.maintainId);
        }
        return condition.toString();
    }
}