package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.dto.DtoCode;

import java.util.List;

/**
 * 刷新redsi缓存
 * <AUTHOR>
 * @version V1.0.0 2021/19/14
 * @since V100R001
 */
public interface RefreshRedisService {

    /**
     * 获取常量配置的redis模块名称及编码列表
     *
     * @return 常量配置的redis模块名称及编码列表
     */
    List<DtoCode> findRedisModel();


    /**
     * 根据常量配置的redis模块编码刷新redis缓存
     *
     * @param modelCodes   模块编码列表
     */
    void refreshRedisByCodes(List<DtoCode> modelCodes);

    /**
     * 刷新整个redis实例
     */
    void flushAll();

}
