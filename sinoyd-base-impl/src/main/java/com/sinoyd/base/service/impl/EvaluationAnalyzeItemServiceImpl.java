package com.sinoyd.base.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoEvaluationAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.EvaluationAnalyzeItemRepository;
import com.sinoyd.base.service.EvaluationAnalyzeItemService;
import com.sinoyd.base.service.EvaluationCriteriaService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * EvaluationAnalyzeItem操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
 @Service
public class EvaluationAnalyzeItemServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoEvaluationAnalyzeItem,String,EvaluationAnalyzeItemRepository> implements EvaluationAnalyzeItemService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private EvaluationCriteriaService evaluationCriteriaService;

    @Override
    public void findByPage(PageBean<DtoEvaluationAnalyzeItem> pb, BaseCriteria evaluationAnalyzeItemCriteria) {
        pb.setEntityName("DtoEvaluationAnalyzeItem a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, evaluationAnalyzeItemCriteria);
    }


    @Override
    public List<DtoEvaluationAnalyzeItem> findAnalyzeItemByEvaluationId(String evaluationId) {
        return repository.findByEvaluationId(evaluationId);
    }

    @Transactional
    @Override
    public List<DtoEvaluationAnalyzeItem> save(Collection<DtoEvaluationAnalyzeItem> dtoEvaluationAnalyzeItems) {

        List<String> evaluationIds = dtoEvaluationAnalyzeItems.stream().map(p -> p.getEvaluationId()).distinct().collect(Collectors.toList());
        List<DtoEvaluationAnalyzeItem> evaluationAnalyzeItems = repository.findByEvaluationIdIn(evaluationIds);
        //一般evaluationIds 只会有1个，不会多个评价标准一起处理的
        for (String evaluationId : evaluationIds) {
            //之前的分析项目信息
            List<DtoEvaluationAnalyzeItem> oldEvaluationAnalyzeItems = evaluationAnalyzeItems.stream().filter(p -> p.getEvaluationId().equals(evaluationId)).collect(Collectors.toList());
            //传过来的需要保存分析项目
            List<DtoEvaluationAnalyzeItem> evaluationAnalyzeItemList = dtoEvaluationAnalyzeItems.stream().filter(p -> p.getEvaluationId().equals(evaluationId)).collect(Collectors.toList());
            //当前评价标准已有的分析项目Ids
            List<String> oldAnalyzeItemIds = oldEvaluationAnalyzeItems.stream().map(DtoEvaluationAnalyzeItem::getAnalyzeItemId).collect(Collectors.toList());
            //新的分析项目ids
            List<String> newAnalyzeItemIds = evaluationAnalyzeItemList.stream().map(DtoEvaluationAnalyzeItem::getAnalyzeItemId).collect(Collectors.toList());

            List<DtoEvaluationAnalyzeItem> newItems = new ArrayList<>();//新保存的集合
            for (String newAnalyzeItemId : newAnalyzeItemIds) {
                if (!oldAnalyzeItemIds.contains(newAnalyzeItemId)) {//如果在已有项目里面不存在，需要加入到保存集合中
                    DtoEvaluationAnalyzeItem dtoEvaluationAnalyzeItem = new DtoEvaluationAnalyzeItem();
                    dtoEvaluationAnalyzeItem.setAnalyzeItemId(newAnalyzeItemId);
                    dtoEvaluationAnalyzeItem.setEvaluationId(evaluationId);
                    dtoEvaluationAnalyzeItem.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    dtoEvaluationAnalyzeItem.setCreator(PrincipalContextUser.getPrincipal().getUserId());
                    dtoEvaluationAnalyzeItem.setCreateDate(new Date());
                    dtoEvaluationAnalyzeItem.setModifyDate(new Date());
                    newItems.add(dtoEvaluationAnalyzeItem);
                }
            }
            if (newItems.size() > 0) {
                List<DtoEvaluationAnalyzeItem> list = super.save(newItems);
                oldEvaluationAnalyzeItems.addAll(list);//将新要保存的数据集放到老的数据集合中，一起重新保存redis
                saveRedis(evaluationId, oldEvaluationAnalyzeItems);
            }
        }
        return null;
    }

    @Transactional
    @Override
    public  Integer delete(String evaluationId,List<String> deleteAnalyzeItemIds) {
        //标准的相关分析项目
        if (deleteAnalyzeItemIds.size() > 0) { //说明要删除评价标准的相关分析项目
            Integer count = repository.delete(evaluationId, deleteAnalyzeItemIds);
            String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
            Object json = redisTemplate.opsForHash().get(key, evaluationId);
            if (StringUtils.isNotNullAndEmpty(json)) {
                TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
                };
                DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);
                List<DtoEvaluationAnalyzeItem> evaluationAnalyzeItems = item.getEvaluationAnalyzeItem();
                // 对评价标准中的分析因子进行非空判断
                if (StringUtil.isNotEmpty(evaluationAnalyzeItems)) {
                    evaluationAnalyzeItems = evaluationAnalyzeItems.stream().filter(p -> !deleteAnalyzeItemIds.contains(p.getAnalyzeItemId())).collect(Collectors.toList());
                }
                item.setEvaluationAnalyzeItem(evaluationAnalyzeItems);
                redisTemplate.opsForHash().put(key, evaluationId, JsonStream.serialize(item));
            } else {
                evaluationCriteriaService.initEvaluationCriteriaRedisById(evaluationId);
            }
            return count;
        }
        return 0;
    }

    private void saveRedis(String evaluationId, List<DtoEvaluationAnalyzeItem> newItems) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        Object json = redisTemplate.opsForHash().get(key, evaluationId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
            };
            DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);
            item.setEvaluationAnalyzeItem(newItems);
            redisTemplate.opsForHash().put(key, evaluationId, JsonStream.serialize(item));
        } else {
            evaluationCriteriaService.initEvaluationCriteriaRedisById(evaluationId);
        }
    }
}