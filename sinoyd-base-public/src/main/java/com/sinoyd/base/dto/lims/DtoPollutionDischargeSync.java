package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.PollutionDischargeSync;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 企业排污许可证同步数据传输实体
 *
 * <AUTHOR>
 * @version V5.2.0 2025/05/06
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_PollutionDischargeSync")
@Data
@DynamicInsert
public class DtoPollutionDischargeSync extends PollutionDischargeSync {

    /**
     * 无参构造函数
     */
    public DtoPollutionDischargeSync() {
        super();
    }

    /**
     * 构造函数（根据企业获取数据结果进行构造）
     *
     * @param isSuccess    同步是否成功
     * @param enterpriseId 企业id
     * @param dataContent  同步抓取内容
     */
    public DtoPollutionDischargeSync(Boolean isSuccess, String enterpriseId, String dataContent) {
        this();
        setEnterpriseId(enterpriseId);
        setRequestTime(new Date());
        setDataContent(dataContent);
        setIsSuccess(isSuccess);
    }
}
