package com.sinoyd.lims.api.service.impl;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoLogForLuckySheet;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.LogForLuckySheetService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.context.BaseContextHandler;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.api.criteria.DocumentPhoneCriteria;
import com.sinoyd.lims.api.criteria.ReceivePhoneCriteria;
import com.sinoyd.lims.api.dto.DtoPersonPhone;
import com.sinoyd.lims.api.dto.DtoReceiveSampleRecordPhone;
import com.sinoyd.lims.api.dto.customer.DtoMobilePointPic;
import com.sinoyd.lims.api.dto.customer.DtoMobileReceive;
import com.sinoyd.lims.api.service.ErgencyMonitoringService;
import com.sinoyd.lims.api.service.FieldMonitoringService;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPersonFaceMsg;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.VersionInfoRepository;
import com.sinoyd.lims.lim.service.LoginQRCodeService;
import com.sinoyd.lims.lim.service.PersonFaceMsgService;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataAdd;
import com.sinoyd.lims.pro.dto.customer.DtoLoadScheme;
import com.sinoyd.lims.pro.dto.customer.DtoReceiveSampleRecordTemp;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ErgencyMonitoringServiceImpl extends BaseJpaServiceImpl<DtoProject, String, ProjectRepository> implements ErgencyMonitoringService {


    @Autowired
    @Lazy
    private ProjectService projectService;

    @Autowired
    private VersionInfoRepository versionInfoRepository;

    @Autowired
    @Lazy
    private SampleFolderService sampleFolderService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private DocumentService documentService;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private ProjectTestService projectTestService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    @Lazy
    private SchemeService schemeService;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private PersonFaceMsgService personFaceMsgService;

    @Autowired
    private DocumentRepository documentRepository;

    private IConfigService configService;

    @Autowired
    @Lazy
    private FieldMonitoringService fieldMonitoringService;

    @Autowired
    @Lazy
    private LoginQRCodeService loginQRCodeService;

    private SampleJudgeDataService sampleJudgeDataService;

    @Autowired
    private StatusForRecordRepository statusForRecordRepository;

    private SamplingPersonConfigRepository samplingPersonConfigRepository;

    private ReportDetailRepository reportDetailRepository;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;

    @Autowired
    @Lazy
    private ReceiveSubSampleRecordService receiveSubSampleRecordService;

    @Autowired
    @Lazy
    private SignatureService signatureService;

    @Autowired
    @Lazy
    private LogForLuckySheetService logForLuckySheetService;

    /**
     * 创建应急项目
     *
     * @param receiveSampleRecordPhone 任务信息
     * @return 创建的应急项目
     */
    @Transactional
    @Override
    public DtoReceiveSampleRecordPhone saveOutsideProject(DtoReceiveSampleRecordPhone receiveSampleRecordPhone) {
        log.info("============================= 当前上下文机构id from PrincipalContextUser: " + PrincipalContextUser.getPrincipal().getOrgId() + "===========================");
        log.info("============================= 当前上下文机构id from BaseContextHandler " + BaseContextHandler.getOrgGuid() + "===========================");
        DtoReceiveSampleRecordTemp recordTemp = new DtoReceiveSampleRecordTemp();
        //region 赋值
        recordTemp.setProjectCode(receiveSampleRecordPhone.getProjectCode());
        recordTemp.setProjectName(receiveSampleRecordPhone.getProjectName());
        recordTemp.setProjectTypeId(receiveSampleRecordPhone.getProjectTypeId());
        recordTemp.setReportMakerId(receiveSampleRecordPhone.getReportorId());
        recordTemp.setDeadLine(receiveSampleRecordPhone.getRequireTime());
        recordTemp.setGrade(receiveSampleRecordPhone.getGrade());
        recordTemp.setInspectedEntId(receiveSampleRecordPhone.getInspectedEntId());
        recordTemp.setInspectedEnt(receiveSampleRecordPhone.getInspectedEnt());
        recordTemp.setInspectedLinkMan(receiveSampleRecordPhone.getInspectedLinkMan());
        recordTemp.setInspectedLinkPhone(receiveSampleRecordPhone.getInspectedLinkPhone());
        recordTemp.setInspectedAddress(receiveSampleRecordPhone.getInspectedAddress());
        recordTemp.setCustomerId(receiveSampleRecordPhone.getCustomerId());
        recordTemp.setCustomerName(receiveSampleRecordPhone.getCustomerName());
        recordTemp.setSenderId(receiveSampleRecordPhone.getSenderId());
        recordTemp.setSenderName(receiveSampleRecordPhone.getSenderName());
        recordTemp.setSendTime(receiveSampleRecordPhone.getSendTime());
        recordTemp.setMonitorPurp(receiveSampleRecordPhone.getMonitorPurp());
        recordTemp.setCustomerRequired(receiveSampleRecordPhone.getCustomerRequired());
        recordTemp.setRemark(receiveSampleRecordPhone.getRemark());
        recordTemp.setSamplingPersonIds(receiveSampleRecordPhone.getSamplingPersonIds());
        recordTemp.setSamplingTime(receiveSampleRecordPhone.getSamplingDate());
        recordTemp.setLeaderId(receiveSampleRecordPhone.getLeaderId());
        recordTemp.setReceiveRemark(receiveSampleRecordPhone.getReceiveRemark());
        recordTemp.setReceiveType(EnumPRO.EnumReceiveType.现场送样.getValue());
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            recordTemp.setInceptPersonId(PrincipalContextUser.getPrincipal().getUserId());
        }
        recordTemp.setInceptTime(new Date());
        recordTemp.setIsStress(false);
        recordTemp.setIsWarning(false);
        //endregion
        recordTemp = projectService.saveOutsideSendSample(recordTemp);
        receiveSampleRecordPhone.setProjectId(recordTemp.getProjectId());
        receiveSampleRecordPhone.setId(recordTemp.getId());
        receiveSampleRecordPhone.setProjectCode(recordTemp.getProjectCode());
        return receiveSampleRecordPhone;
    }

    @Transactional
    @Override
    public DtoReceiveSampleRecordPhone updateOutsideProject(DtoReceiveSampleRecordPhone receiveSampleRecordPhone) {
        DtoReceiveSampleRecordTemp recordTemp = new DtoReceiveSampleRecordTemp();
        //region 赋值
        recordTemp.setId(receiveSampleRecordPhone.getId());
        recordTemp.setProjectId(receiveSampleRecordPhone.getProjectId());
        recordTemp.setProjectCode(receiveSampleRecordPhone.getProjectCode());
        recordTemp.setProjectName(receiveSampleRecordPhone.getProjectName());
        recordTemp.setProjectTypeId(receiveSampleRecordPhone.getProjectTypeId());
        recordTemp.setReportMakerId(receiveSampleRecordPhone.getReportorId());
        recordTemp.setDeadLine(receiveSampleRecordPhone.getRequireTime());
        recordTemp.setGrade(receiveSampleRecordPhone.getGrade());
        recordTemp.setInspectedEntId(receiveSampleRecordPhone.getInspectedEntId());
        recordTemp.setInspectedEnt(receiveSampleRecordPhone.getInspectedEnt());
        recordTemp.setInspectedLinkMan(receiveSampleRecordPhone.getInspectedLinkMan());
        recordTemp.setInspectedLinkPhone(receiveSampleRecordPhone.getInspectedLinkPhone());
        recordTemp.setInspectedAddress(receiveSampleRecordPhone.getInspectedAddress());
        recordTemp.setCustomerId(receiveSampleRecordPhone.getCustomerId());
        recordTemp.setCustomerName(receiveSampleRecordPhone.getCustomerName());
        recordTemp.setSenderId(receiveSampleRecordPhone.getSenderId());
        recordTemp.setSenderName(receiveSampleRecordPhone.getSenderName());
        recordTemp.setSendTime(receiveSampleRecordPhone.getSendTime());
        recordTemp.setMonitorPurp(receiveSampleRecordPhone.getMonitorPurp());
        recordTemp.setMonitorMethods(receiveSampleRecordPhone.getMonitorMethods());
        recordTemp.setCustomerRequired(receiveSampleRecordPhone.getCustomerRequired());
        recordTemp.setRemark(receiveSampleRecordPhone.getRemark());
        recordTemp.setSamplingPersonIds(receiveSampleRecordPhone.getSamplingPersonIds());
        recordTemp.setSamplingTime(receiveSampleRecordPhone.getSamplingDate());
        recordTemp.setLeaderId(receiveSampleRecordPhone.getLeaderId());
        recordTemp.setReceiveRemark(receiveSampleRecordPhone.getReceiveRemark());
        //endregion
        recordTemp = projectService.updateOutsideSendSample(recordTemp);
        return receiveSampleRecordPhone;
    }

    /**
     * 复制送样类项目
     *
     * @param receiveSampleRecordPhone 复制项目信息
     * @return 复制的项目
     */
    @Transactional
    @Override
    public DtoReceiveSampleRecordPhone copyOutsideProject(DtoReceiveSampleRecordPhone receiveSampleRecordPhone) {
        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
        record.setProjectId(receiveSampleRecordPhone.getProjectId());
        record.setDeadLine(receiveSampleRecordPhone.getRequireTime());
        record.setInceptTime(DateUtil.stringToDate(DateUtil.nowTime(DateUtil.FULL), DateUtil.FULL));
        record.setReportDate(DateUtil.stringToDate(DateUtil.nowTime(DateUtil.FULL), DateUtil.FULL));
        record.setSamplingTime(receiveSampleRecordPhone.getSamplingDate());
        //复制项目
        DtoProject project = projectService.copyProject(record);
        DtoReceiveSampleRecord sampleRecord = receiveSampleRecordService.findOutsideSendSampleById(project.getId());
        receiveSampleRecordPhone.setId(sampleRecord.getId());
        receiveSampleRecordPhone.setProjectId(project.getId());
        receiveSampleRecordPhone.setProjectCode(project.getProjectCode());
        receiveSampleRecordPhone.setProjectName(project.getProjectName());
        receiveSampleRecordPhone.setProjectTypeId(project.getProjectTypeId());
        receiveSampleRecordPhone.setReportorId(project.getReportMakerId());
        receiveSampleRecordPhone.setRequireTime(project.getDeadLine());
        receiveSampleRecordPhone.setGrade(project.getGrade());
        receiveSampleRecordPhone.setInspectedEntId(project.getInspectedEntId());
        receiveSampleRecordPhone.setInspectedEnt(project.getInspectedEnt());
        receiveSampleRecordPhone.setInspectedLinkMan(project.getInspectedLinkMan());
        receiveSampleRecordPhone.setInspectedLinkPhone(project.getInspectedLinkPhone());
        receiveSampleRecordPhone.setInspectedAddress(project.getInspectedAddress());
        receiveSampleRecordPhone.setCustomerId(project.getCustomerId());
        receiveSampleRecordPhone.setCustomerName(project.getCustomerName());
        receiveSampleRecordPhone.setSenderId(sampleRecord.getSenderId());
        receiveSampleRecordPhone.setSenderName(sampleRecord.getSenderName());
        receiveSampleRecordPhone.setSendTime(sampleRecord.getSendTime());
        receiveSampleRecordPhone.setMonitorPurp(project.getMonitorPurp());
        receiveSampleRecordPhone.setCustomerRequired(project.getCustomerRequired());
        receiveSampleRecordPhone.setRemark(sampleRecord.getRemark());
        receiveSampleRecordPhone.setSamplingPersonIds(sampleRecord.getSamplingPersonIds());
        receiveSampleRecordPhone.setSamplingPersonNames(String.join(",", sampleRecord.getSamplingPersonNames()));
        receiveSampleRecordPhone.setSamplingTime(DateUtil.dateToString(sampleRecord.getSamplingTime(), DateUtil.FULL));
        receiveSampleRecordPhone.setLeaderId(project.getLeaderId());
        receiveSampleRecordPhone.setProjectTypeName(project.getProjectTypeName());
        receiveSampleRecordPhone.setReceiveIds(Collections.singletonList(sampleRecord.getId()));
        return receiveSampleRecordPhone;
    }

    @Override
    @Transactional
    public List<DtoSampleFolderTemp> copyFolders(List<String> ids, Integer times) {
        List<DtoSampleFolderTemp> temps = sampleFolderService.copyFolders(ids, times, true);
        List<String> sampleFolderIds = temps.stream().map(DtoSampleFolderTemp::getId).collect(Collectors.toList());
        List<DtoSample> samples = sampleRepository.findBySampleFolderIdIn(sampleFolderIds);
        List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        String projectId = samples.get(0).getProjectId();
        fieldMonitoringService.createSampleCode(sampleIds, projectId, new Date());
        return temps;
    }

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位信息
     */
    @Transactional
    @Override
    public String saveSampleList(DtoSampleFolder dtoSampleFolder) {
        List<DtoTest> testList = testRepository.findAll(dtoSampleFolder.getAnalyseItemIds());
        String receiveId = UUIDHelper.GUID_EMPTY;
        if (StringUtil.isNotEmpty(dtoSampleFolder.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(dtoSampleFolder.getReceiveId())) {
            receiveId = dtoSampleFolder.getReceiveId();
        } else {
            DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordRepository.findByProjectId(dtoSampleFolder.getProjectId()).stream().findFirst().orElse(null);
            if (receiveSampleRecord != null) {
                receiveId = receiveSampleRecord.getId();
            }
        }
        DtoSampleFolderTemp folderTemp = this.addFolder(dtoSampleFolder, testList, receiveId);
        return folderTemp.getSampleFolderId();
    }

    @Transactional
    @Override
    public String checkSample(String folderId, String projectId) {
        List<DtoSample> sampleList = sampleRepository.findBySampleFolderId(folderId);
        DtoProject project = projectRepository.findOne(projectId);
        //纠正样品状态
        proService.checkSample(sampleList, project);
        String xcSubId = "";
        DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordRepository.findByProjectId(projectId).stream().findFirst().orElse(null);
        if (StringUtil.isNotNull(receiveSampleRecord)) {
            List<DtoReceiveSubSampleRecord> subSampleRecordList = receiveSubSampleRecordRepository.findByReceiveId(receiveSampleRecord.getId());
            xcSubId = subSampleRecordList.stream().filter(p -> p.getCode().contains("XC")).map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(null);
        }
        return xcSubId;
    }

    /**
     * 移动端版本
     *
     * @param type 类型
     * @return 移动端版本
     */
    @Override
    public DtoVersionInfo getVersionInfo(String type) {
        Boolean isForceRenewal = Boolean.FALSE;
        ConfigModel configModel = configService.findConfig("phone.force.renewal");
        DtoVersionInfo versionInfo = versionInfoRepository.findByVerType(type).stream().max(Comparator.comparing(DtoVersionInfo::getVerTime)).orElse(null);
        if (StringUtil.isNotNull(versionInfo)) {
            // 处理移动端下载url
            loginQRCodeService.processUrl(versionInfo);
            if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
                isForceRenewal = Boolean.valueOf(configModel.getConfigValue());
                versionInfo.setIsForceRenewal(isForceRenewal);
            }
        }
        return versionInfo;
    }

    /**
     * 定位服务判定
     *
     * @return 返回结果
     */
    @Override
    public Map<String, String> isOrientation() {
        Map<String, String> mapValue = new HashMap<>();
        ConfigModel configModel = configService.findConfig("sys.orientation.disposition");
        if (StringUtil.isNotNull(configModel) && StringUtil.isNotEmpty(configModel.getConfigValue())) {
            try {
                mapValue = JsonUtil.toObject(configModel.getConfigValue(), Map.class);
            } catch (Exception ex) {
                throw new BaseException("定位服务判定配置错误");
            }
        }
        return mapValue;
    }

    /**
     * 获取当前人信息
     *
     * @param personId 人员id
     * @return 人员信息
     */
    @Override
    public DtoPersonPhone getPersonById(String personId) {
        DtoPerson person = personRepository.findOne(personId);
        DtoPersonPhone personPhone = new DtoPersonPhone();
        personPhone.setId(person.getId());
        personPhone.setCName(person.getCName());
        List<DtoDocument> photoList = documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc
                (Collections.singletonList(personId), "BASE_DocumentExtendType_PersonPhotos");
        if (StringUtil.isNotEmpty(photoList)) {
            List<DtoDocument> personPhotoList = photoList.parallelStream().filter(p -> person.getId().equals(p.getFolderId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(personPhotoList)) {
                personPhotoList.sort(Comparator.comparing(DtoDocument::getCreateDate, Comparator.reverseOrder()));
                person.setPhotoPath(personPhotoList.get(0).getPath());
            }
        }
        personPhone.setPicUrl(person.getPhotoPath());
        DtoDepartment department = departmentService.findOne(person.getDeptId());
        personPhone.setDomainName(department.getDeptName());
        return personPhone;
    }


    /**
     * 保存人员信息
     *
     * @param personId   人员id
     * @param faceBase64 脸部信息
     */
    @Override
    public void savePersonFace(String personId, String faceBase64) {
        DtoPersonFaceMsg faceMsg = personFaceMsgService.getPersonFaceMsg(personId);
        if (StringUtil.isNotNull(faceMsg)) {
            faceMsg.setFacePicture(faceBase64);
        } else {
            faceMsg = new DtoPersonFaceMsg();
            faceMsg.setPersonId(personId);
            faceMsg.setFacePicture(faceBase64);
        }
        personFaceMsgService.save(faceMsg);
    }

    /**
     * 比对人员信息
     *
     * @param personId 人员id
     * @return 是否正确
     * @throws Exception 报错信息
     */
    @Override
    public String personFaceCompare(String personId) throws Exception {
        DtoPersonFaceMsg faceMsg = personFaceMsgService.getPersonFaceMsg(personId);
        String faceBase = "";
        if (StringUtil.isNotNull(faceMsg)) {
            faceBase = faceMsg.getFacePicture();
        } else {
            throw new Exception("当前人员未维护脸部信息，请维护！");
        }
        return faceBase;
    }

    /**
     * 修改点位名称
     *
     * @param folderId   点位id
     * @param folderName 修改的点位名称
     */
    @Override
    public void updateSampleFolderName(String folderId, String folderName) {
        if (!StringUtil.isNotEmpty(folderName)) {
            throw new BaseException("点位名称不能未空，请确认！");
        }
        DtoSampleFolder folder = sampleFolderService.findOne(folderId);
        //点位名称不同时才进行修改
        if (!folder.getWatchSpot().equals(folderName)) {
            //点位下的样品 -- 原样
            List<DtoSample> sampleList = sampleRepository.findBySampleFolderId(folderId);
            if (sampleList.size() > 0) {
                //对应原样的质控样
                List<DtoSample> associateSamples = sampleRepository.findByAssociateSampleIdIn(sampleList.stream()
                        .map(DtoSample::getId).collect(Collectors.toList()));
                //修改样品名称
                sampleList.addAll(associateSamples);
                sampleList.forEach(p -> p.setRedFolderName(p.getRedFolderName().replace(folder.getWatchSpot(), folderName)));
                sampleRepository.save(sampleList);
            }
            //修改点位名称
            folder.setWatchSpot(folderName);
            sampleFolderRepository.save(folder);
        }
    }

    @Override
    @Transactional
    public void updateSampleCode(String sampleId, String sampleCode) {
        if (!StringUtils.isNotNullAndEmpty(sampleCode)) {
            throw new BaseException("样品编号不能未空，请确认！");
        }
        if (sampleRepository.countByCodeAndIdNot(sampleCode, sampleId) > 0) {
            throw new BaseException("样品编号已经存在");
        }
        DtoSample sample = sampleRepository.findOne(sampleId);
        if (StringUtils.isNotNullAndEmpty(sample.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(sample.getReceiveId())) {
            List<DtoStatusForRecord> statusList = statusForRecordRepository.findByReceiveId(sample.getReceiveId());
            List<String> modules = Arrays.asList(EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue(), EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue());
            if (statusList.stream().anyMatch(s -> modules.contains(s.getModule()))) {
                throw new BaseException("当前样品已检测，无法修改样品编号！");
            }
        }
        List<DtoAnalyseData> analyseData = analyseDataRepository.findBySampleIdAndIsDeletedFalse(sampleId);
        if (analyseData.stream().anyMatch(a -> StringUtils.isNotNullAndEmpty(a.getWorkSheetFolderId()) && !UUIDHelper.GUID_EMPTY.equals(a.getWorkSheetFolderId()))) {
            throw new BaseException("当前样品已检测，无法修改样品编号！");
        }
        sample.setCode(sampleCode);
        sampleRepository.save(sample);
    }

    @Override
    public List<DtoMobilePointPic> getMobilePointPic(PageBean<DtoMobilePointPic> pageBean, BaseCriteria baseCriteria) {
        DocumentPhoneCriteria criteria = (DocumentPhoneCriteria) baseCriteria;
        pageBean.setSelect("select new com.sinoyd.lims.api.dto.customer.DtoMobilePointPic(" +
                "p.id, p.path, t.projectName, t.projectTypeId, t.projectCode, r.id, r.recordCode, r.samplingTime, json_value(r.json,'$.sampleTypeIds') as sampleTypeIds)");
        pageBean.setEntityName("DtoDocument p, DtoReceiveSampleRecord r, DtoProject t");
        pageBean.setSort("r.samplingTime-p.modifyDate-");
        criteria.setDocTypeId(BaseCodeHelper.DOCUMENT_SAMPLE_POINTPIC);
        comRepository.findByPage(pageBean, criteria);
        List<DtoMobilePointPic> mobilePointPicList = pageBean.getData();
        // 采样人员
        List<String> receiveIds = mobilePointPicList.stream().map(DtoMobilePointPic::getReceiveId).distinct().collect(Collectors.toList());
        List<DtoSamplingPersonConfig> samplingPersonConfigs = samplingPersonConfigRepository.findByObjectIdIn(receiveIds);
        List<String> projectTypeIds = mobilePointPicList.stream().map(DtoMobilePointPic::getProjectTypeId).distinct().collect(Collectors.toList());
        List<DtoProjectType> projectTypeList = projectTypeService.findRedisByIds(projectTypeIds);
        Set<String> sampleTypeIds = new HashSet<>();
        mobilePointPicList.forEach(p -> {
            sampleTypeIds.addAll(Arrays.asList(p.getSampleTypeIds().split(",")));
        });
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findRedisByIds(new ArrayList<>(sampleTypeIds)) : new ArrayList<>();
        for (DtoMobilePointPic mobilePointPic : mobilePointPicList) {
            List<DtoSamplingPersonConfig> personConfigs = samplingPersonConfigs.stream().filter(p -> mobilePointPic.getReceiveId().equals(p.getObjectId())).collect(Collectors.toList());
            mobilePointPic.setSamplingPerson(personConfigs.stream().map(DtoSamplingPersonConfig::getSamplingPerson).collect(Collectors.joining(",")));
            Optional<DtoProjectType> projectTypeOptional = projectTypeList.stream().filter(p -> mobilePointPic.getProjectTypeId().equals(p.getId())).findFirst();
            projectTypeOptional.ifPresent(p -> {
                mobilePointPic.setProjectTypeName(p.getName());
            });
            List<String> sampleTypeIdList = Arrays.asList(mobilePointPic.getSampleTypeIds().split(","));
            mobilePointPic.setSampleTypeNames(sampleTypeList.stream().filter(p -> sampleTypeIdList.contains(p.getId())).map(DtoSampleType::getTypeName).collect(Collectors.joining(",")));
        }
        return mobilePointPicList;
    }

    @Override
    @Transactional
    public Integer deletePointPic(List<String> ids) {
        List<DtoDocument> dtoDocuments = documentService.findAll(ids);
        // 获取样品
        List<String> receiveIds = dtoDocuments.stream().map(DtoDocument::getFolderId).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordRepository.findAll(receiveIds);
        List<DtoSample> sampleList = sampleRepository.findByReceiveIdIn(receiveIds);
        // 报告绑定的样品
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoReportDetail> reportDetailList = reportDetailRepository.findByObjectIdInAndObjectType(sampleIds, EnumPRO.EnumReportDetailType.样品.getValue());
        List<String> recordCodes = new ArrayList<>();
        for (DtoDocument document : dtoDocuments) {
            List<String> sampleIdsByReceive = sampleList.stream().filter(p -> document.getFolderId().equals(p.getReceiveId())).map(DtoSample::getId).collect(Collectors.toList());
            List<DtoReportDetail> reportDetails = reportDetailList.stream().filter(p -> sampleIdsByReceive.contains(p.getObjectId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(reportDetails)) {
                receiveSampleRecords.stream().filter(p -> p.getId().equals(document.getFolderId())).findFirst().ifPresent(receive -> {
                    recordCodes.add(receive.getRecordCode());
                });
            }
        }
        if (StringUtil.isNotEmpty(recordCodes)) {
            throw new BaseException(String.join(",", recordCodes) + "中存在被报告编制选择的样品。不允许删除");
        }
        return documentService.logicDeleteById(ids);
    }

    @Override
    public List<DtoMobileReceive> getReceiveList(PageBean<DtoMobileReceive> pageBean, BaseCriteria baseCriteria) {
        ReceivePhoneCriteria criteria = (ReceivePhoneCriteria) baseCriteria;
        pageBean.setSelect("select new com.sinoyd.lims.api.dto.customer.DtoMobileReceive(" +
                "r.id, p.projectName, p.projectTypeId, p.projectCode, r.recordCode, r.samplingTime, json_value(r.json,'$.sampleTypeIds') as sampleTypeIds)");
        pageBean.setEntityName("DtoReceiveSampleRecord r, DtoProject p");
        pageBean.setSort("r.samplingTime-");
        criteria.setIsPointPic(Boolean.TRUE);
        comRepository.findByPage(pageBean, criteria);
        List<DtoMobileReceive> mobileReceives = pageBean.getData();
        // 采样人员
        List<String> receiveIds = mobileReceives.stream().map(DtoMobileReceive::getId).distinct().collect(Collectors.toList());
        List<DtoSamplingPersonConfig> samplingPersonConfigs = samplingPersonConfigRepository.findByObjectIdIn(receiveIds);
        List<String> projectTypeIds = mobileReceives.stream().map(DtoMobileReceive::getProjectTypeId).distinct().collect(Collectors.toList());
        List<DtoProjectType> projectTypeList = projectTypeService.findRedisByIds(projectTypeIds);
        Set<String> sampleTypeIds = new HashSet<>();
        mobileReceives.forEach(p -> {
            if (StringUtil.isNotEmpty(p.getSampleTypeIds())) {
                sampleTypeIds.addAll(Arrays.asList(p.getSampleTypeIds().split(",")));
            }
        });
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findRedisByIds(new ArrayList<>(sampleTypeIds)) : new ArrayList<>();
        for (DtoMobileReceive mobilePointPic : mobileReceives) {
            List<DtoSamplingPersonConfig> personConfigs = samplingPersonConfigs.stream().filter(p -> mobilePointPic.getId().equals(p.getObjectId())).collect(Collectors.toList());
            mobilePointPic.setSamplingPerson(personConfigs.stream().map(DtoSamplingPersonConfig::getSamplingPerson).collect(Collectors.joining(",")));
            Optional<DtoProjectType> projectTypeOptional = projectTypeList.stream().filter(p -> p.getId().equals(mobilePointPic.getProjectTypeId())).findFirst();
            projectTypeOptional.ifPresent(p -> {
                mobilePointPic.setProjectTypeName(p.getName());
            });
            if (StringUtil.isNotEmpty(mobilePointPic.getSampleTypeIds())) {
                List<String> sampleTypeIdList = Arrays.asList(mobilePointPic.getSampleTypeIds().split(","));
                mobilePointPic.setSampleTypeNames(sampleTypeList.stream().filter(p -> sampleTypeIdList.contains(p.getId())).map(DtoSampleType::getTypeName).collect(Collectors.joining(",")));
            }
        }
        return mobileReceives;
    }


    @Override
    @Transactional
    public String replacePointPic(String id, String receiveId) {

        DtoDocument document = documentService.findOne(id);
        if (StringUtil.isNotNull(document)) {
            String filePath = filePathConfig.getFilePath();
            // 获取替换的送样单文件路径
            Map<String, Object> map = new HashMap<>();
            map.put("id", receiveId);
            String newPath = "";
            try {
                newPath = "/" + documentService.getDocumentPath("receiveSampleRecord", map);
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new BaseException("更换送样单失败");
            }
            String checkPath = filePath + newPath;
            // 创建文件目录
            File fileStream = new File(checkPath);
            if (!fileStream.exists()) {
                fileStream.mkdirs();
            }
            // 源附件
            File source = new File(filePathConfig.getFilePath() + document.getPath());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String physicalName = dateFormat.format(new Date()) + "_" + document.getFilename();
            // 目标附件
            File target = new File(checkPath + "/" + physicalName);
            try {
                FileUtils.copyFile(source, target);
                FileUtils.deleteQuietly(source);
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new BaseException("更换送样单失败");
            }
            // 更新附件路径和
            document.setPath(newPath + "/" + physicalName);
            document.setPhysicalName(physicalName);
            document.setFolderId(receiveId);
            documentService.update(document);
        }
        return StringUtil.isNotNull(document) ? document.getPath() : "";
    }

    @Override
    public void findSignPage(PageBean<DtoDocument> pageBean, BaseCriteria baseCriteria) {
        int pageNo = pageBean.getPageNo();
        int rowsPerPage = pageBean.getRowsPerPage();
        pageBean.setPageNo(0);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = (DocumentCriteria) baseCriteria;
        documentService.findByPage(pageBean, baseCriteria);
        List<DtoDocument> data = pageBean.getData();
        List<String> folderIds = data.stream().map(DtoDocument::getFolderId).distinct().collect(Collectors.toList());
        List<DtoProject> projectList = StringUtil.isNotEmpty(folderIds) ? projectService.findAll(folderIds) : new ArrayList<>();
        for (DtoDocument document : data) {
            // 项目编号赋值
            projectList.stream().filter(p -> p.getId().equals(document.getFolderId())).findFirst().ifPresent(project -> {
                document.setProjectCode(project.getProjectCode());
            });
        }
        if (StringUtil.isNotEmpty(criteria.getMobileKey())) {
            String mobileKey = criteria.getMobileKey();
            data = data.stream().filter(p -> p.getFolderName().contains(mobileKey) || p.getProjectCode().contains(mobileKey)).collect(Collectors.toList());
        }
        pageBean.setRowsCount(data.size());
        data = data.stream().skip((long) (pageNo - 1) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList());
        pageBean.setData(data);
    }

    @Override
    @Transactional
    public DtoDocument sign(HttpServletRequest request) {
        // 附件id
        String documentId = request.getParameter("id");
        // 签名类型
        String personType = request.getParameter("personType");
        // 签名图片附件
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("files");
        DtoDocument dtoDocument = documentRepository.findOne(documentId);
        // 默认单个图片传输
        MultipartFile multipartFile = files.get(0);
        try {
            EnumLIM.MobileSignType mobileSignType = EnumLIM.MobileSignType.getByValue(personType);
            boolean isDownSig = mobileSignType != null ? mobileSignType.getIsDown() : false;
            // 签名
            signatureService.sigExcel(dtoDocument, personType, isDownSig, multipartFile);

        } catch (Exception e) {
            throw new BaseException("签名失败：" + e.getMessage());
        }
        // 更新签名状态
        dtoDocument.setSignStatus(EnumBase.EnumSignStatus.已签.getValue());
        DtoLogForLuckySheet log = new DtoLogForLuckySheet();
        log.setObjectId(documentId);
        CurrentPrincipalUser principalUser = PrincipalContextUser.getPrincipal();
        log.setOperatorId(principalUser.getUserId());
        log.setOperatorName(principalUser.getUserName());
        log.setOperateTime(new Date());
        log.setOperateInfo("上传电子签名");
        log.setRemark("上传了" + personType + "电子签名");
        logForLuckySheetService.save(log);
        return documentRepository.save(dtoDocument);
    }

    @Override
    public void signReceive(String docId) {
        // 根据附件id 查询
        DtoDocument document = documentRepository.findOne(docId);
        // 采样单签名
        signatureService.sigExcelByReceiveId(filePathConfig.getFilePath() + document.getPath(), document.getFolderId());
    }

    /**
     * 根据送样单id 查询最新的采样单
     *
     * @param folderId 送样单Id
     * @return
     */
    @Override
    public List<DtoDocument> getSamplingDocuments(String folderId) {
        // 根据folderId 查询所有采样单
        return documentRepository.findByFolderIdAndDocTypeIdOrderByCreateDateDesc(folderId, "sampleRecord")
                .stream().filter(p -> !p.getIsDeleted())
                .sorted(Comparator.comparing(DtoDocument::getCreateDate).reversed()).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void removeSignature(String docId) {
        DtoDocument document = documentRepository.findOne(docId);
        if (StringUtil.isNotNull(document)) {
            //签名
            Map<String, Integer> mapType = new HashMap<>();
            //移除记录单签名的类型，去除签名上岗证人员等于分析者
            mapType.put("企业当事人确认", 0);
            mapType.put("执法人员", 0);
            mapType.put("见证人员", 0);
            //有参数分析日期，不能去除分析日期
            mapType.put(EnumPRO.EnumSigType.采样人.getName(), 0);
            mapType.put("采样人员", 0);
            mapType.put(EnumPRO.EnumSigType.采样日期.getName(), 1);
            mapType.put(EnumPRO.EnumSigType.校核人.getName(), 0);
            mapType.put(EnumPRO.EnumSigType.校核日期.getName(), 1);
            mapType.put(EnumPRO.EnumSigType.监测人员.getName(), 0);
            mapType.put(EnumPRO.EnumSigType.监测日期.getName(), 1);
            mapType.put(EnumPRO.EnumSigType.审核人.getName(), 0);
            signatureService.removeSignatureByDoc(document, mapType);
        }
    }

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位
     * @param testList        测试项目
     * @return 返回该点位的方案
     */
    private DtoSampleFolderTemp addFolder(DtoSampleFolder dtoSampleFolder, List<DtoTest> testList, String receiveId) {
        //进行点位添加并返回
        DtoLoadScheme target = this.addSchemeFolder(dtoSampleFolder, testList, receiveId);

        DtoSampleType samType = sampleTypeService.findOne(dtoSampleFolder.getSampleTypeId());
        Map<String, DtoSampleType> samTypeMap = new HashMap<>();
        if (StringUtil.isNotNull(samType)) {
            samTypeMap.put(dtoSampleFolder.getSampleTypeId(), samType);
        }

        List<DtoSampleFolderTemp> temps = sampleFolderService.getFolderSchemes(dtoSampleFolder.getProjectId(), target.getSampleFolder(), target.getSamplingFrequency(), target.getSamplingFrequencyTest(), testList, samTypeMap);

        //更新项目缓存的指标及处理自增
        projectTestService.modifyProjectTest(dtoSampleFolder.getProjectId(), new ArrayList<>(temps.get(0).getItem().values()));

        return temps.get(0);
    }

    /**
     * 新增点位
     *
     * @param sampleFolder 点位
     * @param testList     点位测试项目
     * @return 点位
     */
    private DtoLoadScheme addSchemeFolder(DtoSampleFolder sampleFolder, List<DtoTest> testList, String receiveId) {
        //用于复制的模板数据载体
        DtoLoadScheme templateScheme = new DtoLoadScheme();
        //用于插入的数据载体
        DtoLoadScheme targetScheme = new DtoLoadScheme();

        DtoProject project = projectRepository.findOne(sampleFolder.getProjectId());
        DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());
        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(receiveId);
        List<DtoReceiveSubSampleRecord> receiveSubSampleRecords = receiveSubSampleRecordRepository.findByReceiveId(receiveId);
        List<String> subIds = receiveSubSampleRecords.stream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
        List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2Samples = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordIdIn(subIds);
        for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
            for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                for (Integer k = 1; k <= sampleFolder.getSampleOrder(); k++) {
                    DtoSamplingFrequency frequency = new DtoSamplingFrequency();
                    frequency.setPeriodCount(i);
                    frequency.setTimePerPeriod(j);
                    frequency.setSamplePerTime(k);
                    frequency.setSampleFolderId(sampleFolder.getId());

                    targetScheme.addSamplingFrequency(frequency);
                    templateScheme.putNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY, frequency.getId(), String.valueOf(i), String.valueOf(j), String.valueOf(k));
                    for (DtoTest test : testList) {
                        DtoSamplingFrequencyTest sft = new DtoSamplingFrequencyTest(test);
                        sft.setSampleFolderId(sampleFolder.getId());
                        sft.setSamplingFrequencyId(frequency.getId());
                        targetScheme.addSamplingFrequencyTest(sft);
                    }
                }
            }
        }
        //需要新增的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //需要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();

        DtoSample temp = new DtoSample(true);
        DtoGenerateSN targetGenerateSN = new DtoGenerateSN();
        targetGenerateSN = sampleService.createSampleCode(project, projectType, sampleFolder.getSampleTypeId(),
                sampleFolder.getId(), sampleFolder.getSamplingTimeBegin(), UUIDHelper.GUID_EMPTY, temp.getId(),
                true, StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : null, false);

        //需要新增的序列号
        if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigCreate())) {
            serialNumberConfigCreateList.add(targetGenerateSN.getSerialNumberConfigCreate());
        }
        //需要修改的序列号
        if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigUpdate())) {
            serialNumberConfigUpdateList.add(targetGenerateSN.getSerialNumberConfigUpdate());
        }
        temp.setCode(targetGenerateSN.getCode());
        temp.setSamplingFrequencyId(templateScheme.getNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY, "1", "1", "1"));
        temp.setSampleFolderId(sampleFolder.getId());
        temp.setReceiveId(receiveId);
        temp.setCycleOrder(1);
        temp.setTimesOrder(1);
        temp.setSampleOrder(1);
        temp.setProjectId(sampleFolder.getProjectId());
        temp.setRedFolderName(schemeService.getFolderName(sampleFolder, 1, 1, 1));
        temp.setSampleTypeId(sampleFolder.getSampleTypeId());
        temp.setInspectedEntId(project.getInspectedEntId());
        temp.setInspectedEnt(project.getInspectedEnt());
        temp.setInceptTime(new Date());
        temp.setSamplingTimeBegin(sampleFolder.getSamplingTimeBegin());
        temp.setSamplingTimeEnd(sampleFolder.getSamplingTimeBegin());
        temp.setStatus(EnumPRO.EnumSampleStatus.样品未领样.toString());
        temp.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
        temp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
        temp.setAnanlyzeStatus(testList.size() > 0 ? EnumPRO.EnumAnalyzeStatus.不能分析.getValue() : EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
        temp.setStoreStatus(EnumPRO.EnumStoreStatus.不能存储.getValue());
        temp.setMakeStatus(EnumPRO.EnumMakeStatus.不需要制样.getValue());
        temp.setSampleCategory(EnumPRO.EnumSampleCategory.原样.getValue());
        temp.setRedAnalyzeItems(proService.getAnalyzeItemsByTest(testList));

        templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, temp.getId(), "1", "1", "1");
        temp = sampleRepository.save(temp);

        for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
            for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                for (Integer k = 1; k <= sampleFolder.getSampleOrder(); k++) {
                    if (i.equals(1) && j.equals(1) && k.equals(1)) {
                        continue;
                    }
                    DtoSample targetSample = new DtoSample();
                    BeanUtils.copyProperties(temp, targetSample);
                    targetSample.setCycleOrder(i);
                    targetSample.setTimesOrder(j);
                    //todo 批次样品改造
                    targetSample.setSampleOrder(k);
                    targetSample.setSamplingFrequencyId(templateScheme.getNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY,
                            String.valueOf(i), String.valueOf(j), String.valueOf(k)));
                    targetSample.setId(UUIDHelper.NewID());
                    targetSample.setRedFolderName(schemeService.getFolderName(sampleFolder, i, j, k));

                    targetGenerateSN = new DtoGenerateSN();
                    targetGenerateSN = sampleService.createSampleCode(project, projectType, sampleFolder.getSampleTypeId(), sampleFolder.getId(), new Date(),
                            UUIDHelper.GUID_EMPTY, targetSample.getId(), true, StringUtil.isNotNull(PrincipalContextUser.getPrincipal())
                                    ? PrincipalContextUser.getPrincipal().getUserId() : null, false);

                    //需要新增的序列号
                    if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigCreate())) {
                        serialNumberConfigCreateList.add(targetGenerateSN.getSerialNumberConfigCreate());
                    }
                    //需要修改的序列号
                    if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigUpdate())) {
                        serialNumberConfigUpdateList.add(targetGenerateSN.getSerialNumberConfigUpdate());
                    }

                    targetSample.setCode(targetGenerateSN.getCode());

                    templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, targetSample.getId(), String.valueOf(i),
                            String.valueOf(j), String.valueOf(k));
                    targetScheme.addSample(targetSample);
                }
            }
        }
        if (testList.size() > 0) {
            DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, temp, record, testList, false);
            addDto.setIsAddAssociateTest(false);
            analyseDataService.addAnalyseData(addDto);

            List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findBySampleIdAndIsDeletedFalse(temp.getId());

            for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
                for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                    for (Integer k = 1; k <= sampleFolder.getSampleOrder(); k++) {
                        if (i.equals(1) && j.equals(1) && k.equals(1)) {
                            continue;
                        }
                        for (DtoAnalyseData analyseData : sourceAnalyseDatas) {
                            DtoAnalyseData targetAnalyseData = schemeService.getSchemeCloneAnalyseData(analyseData);
                            targetAnalyseData.setSampleId(templateScheme.getNewSampleId(UUIDHelper.GUID_EMPTY,
                                    String.valueOf(i), String.valueOf(j), String.valueOf(k)));

                            targetScheme.addAnalyseData(targetAnalyseData);
                        }
                    }
                }
            }
        }

        //添加比对信息
        List<DtoSample> sampleList = new ArrayList<>();
        sampleList.add(temp);
        sampleList.addAll(targetScheme.getSample());
        List<String> tIds = testList.stream().map(DtoTest::getId).collect(Collectors.toList());
        sampleJudgeDataService.createJudgeDataBySampleTest(sampleList, tIds, new ArrayList<>(), project);

        List<DtoReceiveSubSampleRecord2Sample> saveSub2SampleList = new ArrayList<>();
        if (testList.stream().anyMatch(a -> a.getIsCompleteField() && !a.getIsOutsourcing())) {
            DtoReceiveSubSampleRecord subSampleRecord = receiveSubSampleRecords.stream().filter(r -> r.getCode().contains("XC")).findFirst().orElse(null);
            if (StringUtil.isNotNull(subSampleRecord)) {
                for (DtoSample sample : sampleList) {
                    DtoReceiveSubSampleRecord2Sample record2Sample = new DtoReceiveSubSampleRecord2Sample();
                    record2Sample.setReceiveSubSampleRecordId(subSampleRecord.getId());
                    record2Sample.setSampleId(sample.getId());
                    saveSub2SampleList.add(record2Sample);
                }
            } else {
                receiveSubSampleRecordService.createSubRecord(record, sampleList, EnumPRO.EnumSubRecordType.现场.getValue());
            }
        }
        if (testList.stream().anyMatch(a -> !a.getIsCompleteField() && !a.getIsOutsourcing())) {
            DtoReceiveSubSampleRecord subSampleRecord = receiveSubSampleRecords.stream().filter(r -> r.getCode().contains("FX")).findFirst().orElse(null);
            if (StringUtil.isNotNull(subSampleRecord)) {
                for (DtoSample sample : sampleList) {
                    DtoReceiveSubSampleRecord2Sample record2Sample = new DtoReceiveSubSampleRecord2Sample();
                    record2Sample.setReceiveSubSampleRecordId(subSampleRecord.getId());
                    record2Sample.setSampleId(sample.getId());
                    saveSub2SampleList.add(record2Sample);
                }
            } else {
                receiveSubSampleRecordService.createSubRecord(record, sampleList, EnumPRO.EnumSubRecordType.分析.getValue());
            }
        }
        if (StringUtil.isNotEmpty(saveSub2SampleList)) {
            receiveSubSampleRecord2SampleRepository.save(saveSub2SampleList);
        }

        sampleFolderRepository.save(sampleFolder);
        schemeService.persistLoadScheme(targetScheme);

        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("新增点位%s(%s),%d周期,%d次,%d样次。", sampleFolder.getWatchSpot(), sampleType.getTypeName(),
                sampleFolder.getPeriodCount(), sampleFolder.getTimePerPeriod(), sampleFolder.getSampleOrder());
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(),
                EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加点位.toString(),
                StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId()
                        : null, StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserName()
                        : null, UUIDHelper.GUID_EMPTY, "");

        //纠正项目状态
        proService.checkProject(Collections.singletonList(sampleFolder.getProjectId()));

        if (serialNumberConfigCreateList.size() > 0) {
            comRepository.insert(serialNumberConfigCreateList);
        }
        if (serialNumberConfigUpdateList.size() > 0) {
            comRepository.updateBatch(serialNumberConfigUpdateList);
        }

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案新增点位, sampleFolder.getProjectId());
                    }
                }
        );

        targetScheme.setSampleFolder(Collections.singletonList(sampleFolder));
        return targetScheme;
    }


    @Autowired
    @Lazy
    public void setIConfigService(IConfigService configService) {
        this.configService = configService;
    }

    @Autowired
    @Lazy
    public void setSampleJudgeDataService(SampleJudgeDataService sampleJudgeDataService) {
        this.sampleJudgeDataService = sampleJudgeDataService;
    }

    @Autowired
    @Lazy
    public void setSamplingPersonConfigRepository(SamplingPersonConfigRepository samplingPersonConfigRepository) {
        this.samplingPersonConfigRepository = samplingPersonConfigRepository;
    }

    @Autowired
    @Lazy
    public void setReportDetailRepository(ReportDetailRepository reportDetailRepository) {
        this.reportDetailRepository = reportDetailRepository;
    }
}
