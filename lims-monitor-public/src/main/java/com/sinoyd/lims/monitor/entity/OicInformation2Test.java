package com.sinoyd.lims.monitor.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;


/**
 * OicInformation2Test实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OicInformation2Test")
 @Data
 public  class OicInformation2Test implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String oicId;
    
    /**
    * 
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String testId;
    
 }