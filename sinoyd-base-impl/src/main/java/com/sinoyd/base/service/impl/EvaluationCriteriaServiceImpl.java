package com.sinoyd.base.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.customer.DtoEvaluationValueTemp;
import com.sinoyd.base.dto.rcc.*;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.EvaluationAnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.EvaluationCriteriaRepository;
import com.sinoyd.base.repository.rcc.EvaluationValueRepository;
import com.sinoyd.base.service.*;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 评价配置接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
public class EvaluationCriteriaServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoEvaluationCriteria, String, EvaluationCriteriaRepository> implements EvaluationCriteriaService {

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    private CodeService codeService;


    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private EvaluationAnalyzeItemService evaluationAnalyzeItemService;

    @Autowired
    @Lazy
    private EvaluationLevelService evaluationLevelService;

    @Autowired
    @Lazy
    private EvaluationValueService evaluationValueService;

    private EvaluationValueRepository evaluationValueRepository;

    private EvaluationAnalyzeItemRepository evaluationAnalyzeItemRepository;

    @Override
    public void findByPage(PageBean pageBean, BaseCriteria evaluationCriteriaCriteria) {
        pageBean.setEntityName("DtoEvaluationCriteria e");
        pageBean.setSelect("select e");
        super.findByPage(pageBean, evaluationCriteriaCriteria);

        List<DtoEvaluationCriteria> dataList = pageBean.getData();

        //相关的检测类型
        List<DtoSampleType> dtoSampleTypes = new ArrayList<>();
        //取出检测类型
        List<String> sampleTypeIds = dataList.stream().map(DtoEvaluationCriteria::getSampleTypeId).distinct().collect(Collectors.toList());

        if (StringUtil.isNotNull(sampleTypeIds) && sampleTypeIds.size() > 0) {
            dtoSampleTypes = sampleTypeService.findAll(sampleTypeIds);
        }

        //常量的数据
        List<DtoCode> dtoCodes = new ArrayList<>();

        //取出常量类型
        List<String> categoryIds = dataList.stream().map(DtoEvaluationCriteria::getCategoryId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotNull(categoryIds) && categoryIds.size() > 0) {
            dtoCodes = codeService.findByCodes(categoryIds);
        }

        //新的集合
        List<DtoEvaluationCriteria> newDataList = new ArrayList<>();
        Iterator<DtoEvaluationCriteria> iterator = dataList.iterator();
        while (iterator.hasNext()) {
            String sampleTypeName = "";//检测类型的名称
            String categoryName = "";//标准类型名称
            DtoEvaluationCriteria dtoEvaluationCriteria = iterator.next();
            String sampleTypeId = dtoEvaluationCriteria.getSampleTypeId();
            String categoryId = dtoEvaluationCriteria.getCategoryId();
            Optional<DtoSampleType> optionalDtoSampleType = dtoSampleTypes.stream().filter(p -> p.getId().equals(sampleTypeId)).findFirst();
            if (StringUtil.isNotNull(optionalDtoSampleType) && optionalDtoSampleType.isPresent()) {
                DtoSampleType dtoSampleType = optionalDtoSampleType.get();
                sampleTypeName = dtoSampleType.getTypeName();
            }
            //取标准类型名称
            Optional<DtoCode> optionalCode = dtoCodes.stream().filter(p -> p.getDictCode().equals(categoryId)).findFirst();
            if (StringUtil.isNotNull(optionalCode) && optionalCode.isPresent()) {
                DtoCode dtoCode = optionalCode.get();
                categoryName = dtoCode.getDictName();
            }
            dtoEvaluationCriteria.setCategoryName(categoryName);
            dtoEvaluationCriteria.setSampleTypeName(sampleTypeName);
            newDataList.add(dtoEvaluationCriteria);
        }
        pageBean.setData(newDataList);
    }

    /**
     * 重新实现（为了返回调用该类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回评价标准对象
     */
    @Override
    public DtoEvaluationCriteria findOne(String id) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        Object json = redisTemplate.opsForHash().get(key, id);
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
            };
            DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);
            return item;
        }
        //否则从数据库中获取，并初始化到redis中
        DtoEvaluationCriteria item = initEvaluationCriteriaRedisById(id);
        return item;
    }

    @Transactional
    @Override
    public DtoEvaluationCriteria save(DtoEvaluationCriteria dtoEvaluationCriteria) {
        String code = dtoEvaluationCriteria.getCode();
        if (StringUtil.isNotEmpty(code)) {
            List<DtoEvaluationCriteria> dbEvaluationCriteria = repository.findAll();
            List<DtoEvaluationCriteria> isExist = dbEvaluationCriteria.stream().filter(p -> code.equals(p.getCode())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isExist)) {
                throw new BaseException("标准编码已存在！");
            }
        }
        DtoEvaluationCriteria item = super.save(dtoEvaluationCriteria);
        List<DtoEvaluationAnalyzeItem> analyzeItems = dtoEvaluationCriteria.getEvaluationAnalyzeItem();
        if (StringUtil.isNotNull(analyzeItems)) {
            for (DtoEvaluationAnalyzeItem analyzeItem : analyzeItems) {
                analyzeItem.setEvaluationId(item.getId());
            }
        }
        evaluationAnalyzeItemService.save(analyzeItems);
        saveEvaluationCriteriaRedis(item);
        return item;
    }

    @Transactional
    @Override
    public DtoEvaluationCriteria update(DtoEvaluationCriteria dtoEvaluationCriteria) {
        String code = dtoEvaluationCriteria.getCode();
        if (StringUtil.isNotEmpty(code)) {
            List<DtoEvaluationCriteria> dbEvaluationCriteria = repository.findAll();
            dbEvaluationCriteria.removeIf(p -> dtoEvaluationCriteria.getId().equals(p.getId()));
            List<DtoEvaluationCriteria> isExist = dbEvaluationCriteria.stream().filter(p -> code.equals(p.getCode())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isExist)) {
                throw new BaseException("标准编码已存在！");
            }
        }
        DtoEvaluationCriteria item = super.update(dtoEvaluationCriteria);
        List<DtoEvaluationAnalyzeItem> analyzeItems = dtoEvaluationCriteria.getEvaluationAnalyzeItem();
        List<DtoEvaluationAnalyzeItem> oldAnalyzeItems = evaluationAnalyzeItemService.findAnalyzeItemByEvaluationId(dtoEvaluationCriteria.getId());
        List<String> oldAnalyzeItemIds = oldAnalyzeItems.stream().map(DtoEvaluationAnalyzeItem::getAnalyzeItemId).distinct().collect(Collectors.toList());
        oldAnalyzeItemIds.removeAll(analyzeItems.stream().map(DtoEvaluationAnalyzeItem::getAnalyzeItemId).distinct().collect(Collectors.toList()));
        for (DtoEvaluationAnalyzeItem analyzeItem : analyzeItems) {
            analyzeItem.setEvaluationId(item.getId());
        }
        evaluationAnalyzeItemService.save(analyzeItems);
        updateEvaluationCriteriaRedis(item);
        if (oldAnalyzeItemIds.size() > 0) {
            evaluationAnalyzeItemService.delete(dtoEvaluationCriteria.getId(), oldAnalyzeItemIds);
        }
        return item;
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        //删除关联表数据
        idList.forEach(p -> {
            //删除评价标准等级
            List<DtoEvaluationLevel> levels = evaluationLevelService.findLevelByEvaluationId(p);
            String levelId = "";
            if (StringUtil.isNotEmpty(levels)) {
                DtoEvaluationLevel level = levels.get(0);
                levelId = level.getId();
                evaluationLevelService.logicDeleteById(level.getId());
            }
            //删除评价标准测试项目
            List<DtoEvaluationAnalyzeItem> anaByEva = evaluationAnalyzeItemService.findAnalyzeItemByEvaluationId(p);
            List<String> anaIds = new ArrayList<>();
            if (StringUtil.isNotEmpty(anaByEva)) {
                anaIds = anaByEva.stream().map(DtoEvaluationAnalyzeItem::getAnalyzeItemId).collect(Collectors.toList());
                evaluationAnalyzeItemService.delete(p, anaIds);
            }
            //删除评价标准限值
            List<DtoEvaluationValue> values = evaluationValueService.findEvaluationValueByLevelId(Arrays.asList(levelId));
            if (StringUtil.isNotEmpty(values)) {
                List<String> valueIds = values.stream().map(DtoEvaluationValue::getId).collect(Collectors.toList());
                DtoEvaluationValueTemp temp = new DtoEvaluationValueTemp();
                temp.setIds(valueIds);
                temp.setEvaluationId(p);
                temp.setLevelId(levelId);
                temp.setAnalyzeItemIds(anaIds);
                evaluationValueService.deleteEvaluationValue(temp);
            }
        });
        deleteRedis(idList);
        return super.logicDeleteById(ids);
    }


    /**
     * 初始化指定的redis数据
     *
     * @param id ids
     */
    @Override
    public DtoEvaluationCriteria initEvaluationCriteriaRedisById(String id) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        //否则从数据库中获取，并初始化到redis中
        DtoEvaluationCriteria item = super.findOne(id);
        if (StringUtil.isNotNull(item)) {
            List<DtoEvaluationAnalyzeItem> evaluationAnalyzeItems = evaluationAnalyzeItemService.findAnalyzeItemByEvaluationId(id);
            if (StringUtil.isNotNull(evaluationAnalyzeItems)) {
                item.setEvaluationAnalyzeItem(evaluationAnalyzeItems);
            }
            List<DtoEvaluationLevel> evaluationLevels = evaluationLevelService.findLevelByEvaluationId(id);
            if (StringUtil.isNotNull(evaluationLevels)) {
                item.setEvaluationLevel(evaluationLevels);
            }
            redisTemplate.opsForHash().put(key, id, JsonStream.serialize(item));
        }
        return item;
    }


    /**
     * 新增评价标准redis数据
     */
    @Override
    public void saveEvaluationCriteriaRedis(DtoEvaluationCriteria dtoEvaluationCriteria) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        redisTemplate.opsForHash().put(key, dtoEvaluationCriteria.getId(), JsonStream.serialize(dtoEvaluationCriteria));
    }


    @Override
    @Transactional
    public void refresh() {
        // 根据评价标准ids查询详细评价数据
        List<DtoEvaluationValue> evaluationValues = evaluationValueRepository.findAll();
        Map<String, List<DtoEvaluationValue>> groupEvaluationValue = evaluationValues.stream().collect(Collectors.groupingBy(DtoEvaluationValue::getEvaluationId));
        List<String> evaluationIds = new ArrayList<>(groupEvaluationValue.keySet());
        List<DtoEvaluationAnalyzeItem> evaluationAnalyzeItems = new ArrayList<>();
        for (Map.Entry<String, List<DtoEvaluationValue>> entry : groupEvaluationValue.entrySet()) {
            List<DtoEvaluationValue> evaluationValueList = entry.getValue();
            if (StringUtil.isNotEmpty(evaluationValueList)) {
                for (DtoEvaluationValue value : evaluationValueList) {
                    DtoEvaluationAnalyzeItem item = new DtoEvaluationAnalyzeItem();
                    item.setEvaluationId(entry.getKey());
                    item.setAnalyzeItemId(value.getAnalyzeItemId());
                    evaluationAnalyzeItems.add(item);
                }
            }
        }
        // 先删除原有的因子配置
        evaluationAnalyzeItemRepository.deleteByEvaluationIdIn(evaluationIds);
        // 新增配置
        evaluationAnalyzeItemRepository.save(evaluationAnalyzeItems);
    }

    /**
     * 修改评价标准redis数据
     */
    private void updateEvaluationCriteriaRedis(DtoEvaluationCriteria dtoEvaluationCriteria) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        Object json = redisTemplate.opsForHash().get(key, dtoEvaluationCriteria.getId());
        if (StringUtils.isNotNullAndEmpty(json)) {
            TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
            };
            DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);
            dtoEvaluationCriteria.setEvaluationAnalyzeItem(item.getEvaluationAnalyzeItem());
            dtoEvaluationCriteria.setEvaluationLevel(item.getEvaluationLevel());
            redisTemplate.opsForHash().put(key, dtoEvaluationCriteria.getId(), JsonStream.serialize(dtoEvaluationCriteria));
        } else {
            initEvaluationCriteriaRedisById(dtoEvaluationCriteria.getId());
        }
    }

    /**
     * 删除相应的redis数据
     *
     * @param ids 检测ids
     */
    private void deleteRedis(List<String> ids) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        redisTemplate.opsForHash().delete(key, ids.toArray());
    }

    @Autowired
    public void setEvaluationValueRepository(EvaluationValueRepository evaluationValueRepository) {
        this.evaluationValueRepository = evaluationValueRepository;
    }

    @Autowired
    public void setEvaluationAnalyzeItemRepository(EvaluationAnalyzeItemRepository evaluationAnalyzeItemRepository) {
        this.evaluationAnalyzeItemRepository = evaluationAnalyzeItemRepository;
    }
}
