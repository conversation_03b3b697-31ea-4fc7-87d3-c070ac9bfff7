package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 首页业务量趋势
 * <AUTHOR>
 * @version V1.0.0 2020/5/11
 * @since V100R001
 */
@Data
public class DtoHomeBusinessTrend {
    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 项目个数
     */
    private Integer projectCount;

    /**
     * 每月明细
     */
    private List<DtoHomeTrendDetail> detail = new ArrayList<>();

    /**
     * 填充明细
     * @param beginTime 开始时间
     * @param endTime 结束时间
     */
    public void fillDetail(Date beginTime, Date endTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(beginTime);

        while (!calendar.getTime().after(endTime)) {
            Date time = calendar.getTime();
            if (this.detail.stream().noneMatch(p -> p.getTime().equals(time))) {
                this.detail.add(new DtoHomeTrendDetail(time, 0));
            }
            calendar.add(Calendar.MONTH, 1);
        }
    }
}
