package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.FlowCalibration2FrequencyCriteria;
import com.sinoyd.lims.pro.dto.DtoFlowCalibration2Frequency;
import com.sinoyd.lims.pro.service.FlowCalibration2FrequencyService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流量校准关联点位服务
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/18
 */
@Api(tags = "示例: 流量校准关联点位服务")
@RestController
@RequestMapping("api/pro/flowCalibration2Frequency")
public class FlowCalibration2FrequencyController
        extends BaseJpaController<DtoFlowCalibration2Frequency,String, FlowCalibration2FrequencyService> {
    /**
     * 分页查询
     * @param criteria 查询条件
     * @return 数据
     */
    @GetMapping
    public RestResponse<List<DtoFlowCalibration2Frequency>> findByPage(FlowCalibration2FrequencyCriteria criteria){
        RestResponse<List<DtoFlowCalibration2Frequency>> response = new RestResponse<>();
        PageBean<DtoFlowCalibration2Frequency> page = super.getPageBean();
        service.findByPage(page,criteria);
        response.setCount(page.getRowsCount());
        response.setData(page.getData());
        return response;
    }

    /**
     * 批量新增
     * @param list 对象集合
     * @return 数据
     */
    @PostMapping("/batch")
    public RestResponse<List<DtoFlowCalibration2Frequency>> batchSave(@RequestBody List<DtoFlowCalibration2Frequency> list){
        RestResponse<List<DtoFlowCalibration2Frequency>> response = new RestResponse<>();
        response.setData(service.save(list));
        return response;
    }


    /**
     * 批量删除
     * @param ids 标识集合
     * @return 数据
     */
    @DeleteMapping
    public RestResponse<Integer> logicDeleteById(@RequestBody List<String> ids){
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.logicDeleteById(ids));
        return response;
    }

    /**
     * 查询送样单下流量校准记录关联的点位周期
     * @param receiveId 送样单标识
     * @param flowCalibrationId 流量校准标识
     * @return 数据
     */
    @GetMapping("/receive/{receiveId}/{flowCalibrationId}")
    public RestResponse<List<DtoFlowCalibration2Frequency>> findByReceive(@PathVariable String receiveId,@PathVariable String flowCalibrationId ){
        RestResponse<List<DtoFlowCalibration2Frequency>> response = new RestResponse<>();
        response.setData(service.findByReceive(receiveId,flowCalibrationId));
        return response;
    }

}
