package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.service.OAProjectExpendService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

/**
 * 项目支出的通知
 *
 * <AUTHOR>
 * @version V1.0.0 2020-03-30
 * @since V100R001
 */
@Component
@Slf4j
public class OAProjectExpendListener  implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {
        OATaskRelationService oaTaskRelationService = SpringContextAware.getBean(OATaskRelationService.class);
        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(delegateExecution.getProcessBusinessKey());
        OAProjectExpendService oaProjectExpendService = SpringContextAware.getBean(OAProjectExpendService.class);
        oaProjectExpendService.confirm(relation.getObjectId());
    }
}
