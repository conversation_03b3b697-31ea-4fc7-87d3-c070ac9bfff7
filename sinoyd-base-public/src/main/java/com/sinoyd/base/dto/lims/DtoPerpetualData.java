package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.PerpetualData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoPerpetualData实体
 * <AUTHOR>
 * @version V1.0.0 2023/12/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_PerpetualData")
@Data
@DynamicInsert
public class DtoPerpetualData extends PerpetualData {
}
