package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoExamineTypeRecord;
import com.sinoyd.lims.lim.service.ExamineTypeRecordService;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 考核类型填写记录controller
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/14
 * @since V100R001
 */
@Api(tags = "DtoExamineTypeRecord服务")
@RestController
@RequestMapping("api/lim/examineTypeRecord")
@Validated
public class ExamineTypeRecordController extends BaseJpaController<DtoExamineTypeRecord, String, ExamineTypeRecordService> {

    /**
     * 批量保存填写记录
     * @param list 记录列表
     * @return 记录列表
     */
    @PostMapping
    public RestResponse<List<DtoExamineTypeRecord>> batchAddRecord(@Validated @RequestBody List<DtoExamineTypeRecord> list){
        RestResponse<List<DtoExamineTypeRecord>> res = new RestResponse<>();
        res.setData(service.batchAddRecord(list));
        return res;
    }

    /**
     * 获取考核下所有填报记录
     * @param typeId 考核项目id
     * @return 填报记录列表
     */
    @GetMapping("/type/{id}")
    public RestResponse<List<DtoExamineTypeRecord>> findAllByExamineTypeId(@PathVariable("id")String typeId){
        RestResponse<List<DtoExamineTypeRecord>> res = new RestResponse<>();
        res.setData(service.findAllByExamineTypeId(typeId));
        return res;
    }

    /**
     * 删除单条填报记录填报记录
     * @param id 考核项目id
     * @return 填报记录实体
     */
    @DeleteMapping("/{id}")
    public RestResponse<Void> delRecord(@PathVariable("id")String id){
        RestResponse<Void> res = new RestResponse<>();
        service.logicDeleteById(id);
        return res;
    }
}
