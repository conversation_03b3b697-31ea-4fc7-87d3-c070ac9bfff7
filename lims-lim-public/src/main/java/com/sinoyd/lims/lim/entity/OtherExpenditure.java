package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * OtherExpenditure实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OtherExpenditure")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class OtherExpenditure implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  OtherExpenditure() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 操作人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("操作人id")
    private String operatorId;
    
    /**
    * 操作人
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("操作人")
	private String operator;
    
    /**
    * 操作 日期
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("操作 日期")
    private Date operateDate;
    
    /**
    * 所属部门（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属部门（Guid）")
    private String deptId;
    
    /**
    * 支出类型（常量编码：LIM_ExpenditureType）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("支出类型（常量编码：LIM_ExpenditureType）")
    @Length(message = "支出类型{validation.message.length}", max = 50)
    private String paytype;
    
    /**
    * 支出种类（枚举EnumOtherPayCategory：1支出，2收入）
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("支出种类（枚举EnumOtherPayCategory：1支出，2收入）")
    private Integer category;
    
    /**
    * 金额
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("金额")
    private BigDecimal amount;
    
    /**
    * 说明
    */
    @Column(length=1000)
    @ApiModelProperty("说明")
    @Length(message = "说明{validation.message.length}", max = 1000)
    private String explain;
    
    /**
    * 项目id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目id")
    private String projectId;
    
    /**
    * 项目编号
    */
    @Column(length=200)
    @ApiModelProperty("项目编号")
	private String projectCode;
    
    /**
    * 项目名称
    */
    @Column(length=1000)
    @ApiModelProperty("项目名称")
	private String projectName;
    
    /**
    * 企业id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("企业id")
    private String entId;
    
    /**
    * 企业名称
    */
    @Column(length=1000)
    @ApiModelProperty("企业名称")
	private String entName;
    
    /**
    * 状态
    */
    @Column(length=50)
    @ApiModelProperty("状态")
    @Length(message = "状态{validation.message.length}", max = 50)
    private String status;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }