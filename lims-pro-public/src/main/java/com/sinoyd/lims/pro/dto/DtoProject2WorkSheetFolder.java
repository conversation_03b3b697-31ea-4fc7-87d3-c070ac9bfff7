package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.Project2WorkSheetFolder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoProject2WorkSheetFolder实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_Project2WorkSheetFolder")
 @Data
 @DynamicInsert
 public  class DtoProject2WorkSheetFolder extends Project2WorkSheetFolder {
   private static final long serialVersionUID = 1L;
 }