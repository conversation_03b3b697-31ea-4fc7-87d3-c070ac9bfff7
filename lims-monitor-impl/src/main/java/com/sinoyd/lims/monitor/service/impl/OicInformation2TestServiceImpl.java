package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.monitor.dto.lims.DtoOicInformation2Test;
import com.sinoyd.lims.monitor.repository.lims.OicInformation2TestRepository;
import com.sinoyd.lims.monitor.service.OicInformation2TestService;
import org.springframework.stereotype.Service;


/**
 * OicInformation2Test操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Service
public class OicInformation2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoOicInformation2Test,String,OicInformation2TestRepository> implements OicInformation2TestService {

    @Override
    public void findByPage(PageBean<DtoOicInformation2Test> pb, BaseCriteria oicInformation2TestCriteria) {
        pb.setEntityName("DtoOicInformation2Test a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, oicInformation2TestCriteria);
    }
}