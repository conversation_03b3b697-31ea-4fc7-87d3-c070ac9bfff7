package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * SampleFolderTemplate实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "SampleFolderTemplate")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SampleFolderTemplate implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 项目方案变更id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目方案变更id")
    private String approveId;

    /**
     * 点位id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("点位id")
    private String sampleFolderId;

    /**
     * 操作类型
     */
    @Column(nullable=false)
    @ApiModelProperty("操作类型")
    private Integer operateType;

    /**
     * 点位名称
     */
    @Column(length = 50)
    @ApiModelProperty("点位名称")
    @Length(message = "点位名称{validation.message.length}", max = 100)
    private String watchSpot;

    /**
     * 检测类型id
     */
    @Column(length = 50)
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;

    /**
     * 分析项目
     */
    @Column(length = 50)
    @ApiModelProperty("分析项目")
    @Length(message = "分析项目{validation.message.length}", max = 1000)
    private String redAnalyzeItems;

    /**
     * 经度
     */
    @Column(length = 50)
    @ApiModelProperty("经度")
    @Length(message = "经度{validation.message.length}", max = 20)
    private String lon;

    /**
     * 纬度
     */
    @Column(length = 50)
    @ApiModelProperty("纬度")
    @Length(message = "纬度{validation.message.length}", max = 20)
    private String lat;

}
