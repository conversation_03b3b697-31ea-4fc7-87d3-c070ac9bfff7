-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关DDL脚本 -----------
-- ---------------- Query模块 --------------------
-- ----------------------------------------------

create table TB_QUERY_Item
(
    id         varchar(50)                                                not null comment 'id' primary key,
    itemName   varchar(100)                                               not null comment '查询名称',
    itemDesc   varchar(255)                                               null comment '查询描述',
    viewId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '查询所用视图id',
    usedTimes  int         default 0                                      not null comment '查询使用次数',
    isDeleted  bit         default b'0'                                   null comment '是否删除',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    checkOut   bit         default b'0'                                   not null
) comment '用户查询表，记录用户查询的信息' ENGINE = InnoDB
                                          CHARACTER SET = utf8
                                          COLLATE = utf8_general_ci
                                          ROW_FORMAT = Dynamic;

create table TB_QUERY_ItemColumn
(
    id             varchar(50)                                                not null comment '主键' primary key,
    itemId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '用户查询表id',
    viewFieldId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '视图字段表id',
    columnLength   int         default 5                                      not null comment '列宽',
    sortType       varchar(50)                                                not null comment '排序类型，升序、降序',
    sortSeq        int         default 0                                      not null comment '排序节点',
    isShow         bit         default b'0'                                   not null comment '是否显示',
    isScreen       bit         default b'0'                                   not null comment '是否过滤',
    isDeleted      bit         default b'0'                                   not null comment '是否删除',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    dataSource     varchar(2000)                                              null comment '数据源',
    defaultControl int         default 0                                      not null comment '默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）'
) comment '用户查询的列信息' ENGINE = InnoDB
                             CHARACTER SET = utf8
                             COLLATE = utf8_general_ci
                             ROW_FORMAT = Dynamic;

create table TB_QUERY_ItemCondition
(
    id            varchar(50)                                                not null comment 'id' primary key,
    itemId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '用户查询信息表id',
    dbCondition   varchar(2000)                                              null comment '查询条件DB形式',
    pageCondition varchar(2000)                                              null comment '查询条件页面形式',
    isDeleted     bit         default b'0'                                   null comment '是否删除',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '用户查询条件信息表' ENGINE = InnoDB
                               CHARACTER SET = utf8
                               COLLATE = utf8_general_ci
                               ROW_FORMAT = Dynamic;

create table TB_QUERY_View
(
    id         varchar(50)                                                not null comment '主键' primary key,
    viewName   varchar(100)                                               not null comment '视图名称',
    typeName   varchar(100)                                               not null comment '查询类别(页面显示)',
    typeDesc   varchar(255)                                               null comment '查询类别描述',
    orderNum   int         default 0                                      not null comment '排序值',
    isDeleted  bit         default b'0'                                   not null comment '是否删除',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '自定义查询视图表' ENGINE = InnoDB
                             CHARACTER SET = utf8
                             COLLATE = utf8_general_ci
                             ROW_FORMAT = Dynamic;

create table TB_QUERY_ViewField
(
    id         varchar(50)                                                not null comment '主键' primary key,
    viewId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '视图表主键',
    dbField    varchar(50)                                                not null comment 'DB字段名称',
    pageField  varchar(255)                                               null comment '页面字段名称',
    isShow     bit         default b'1'                                   not null comment '是否显示在客户端',
    isDeleted  bit         default b'0'                                   not null comment '是否删除',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '视图字段表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

