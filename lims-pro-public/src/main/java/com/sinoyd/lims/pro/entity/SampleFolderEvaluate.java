package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

@MappedSuperclass
@ApiModel(description="SampleFolderEvaluate")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SampleFolderEvaluate implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 点位id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("点位id")
    private String sampleFolderId;

    /**
     * 测试标识
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("测试项目标识")
    private String testId;

    /**
     * 判定结果
     */
    @Column(length = 255)
    @ApiModelProperty("判定结果")
    @Length(message = "判定结果{validation.message.length}", max = 255)
    private String qcRateValue;

    /**
     * 是否合格
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("是否合格")
    @Length(message = "是否合格{validation.message.length}", max = 50)
    private String folderPass;

    /**
     * 备注
     */
    @Column(length = 255, nullable = false)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 255)
    private String remark;

    /**
     * 结果评价
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("结果评价")
    @Length(message = "结果评价{validation.message.length}", max = 50)
    private String resultEvaluate;

}
