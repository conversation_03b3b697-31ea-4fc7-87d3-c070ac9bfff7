package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * SerialNumberConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SerialNumberConfig")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class SerialNumberConfig implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  SerialNumberConfig() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 序号生成类型
    */
    @Column(length=100)
    @ApiModelProperty("序号生成类型")
    @Length(message = "序号生成类型{validation.message.length}", max = 100)
    private String serialNumberType;
    
    /**
    * 参数1
    */
    @Column(length=100)
    @ApiModelProperty("参数1")
    @Length(message = "参数1{validation.message.length}", max = 100)
    private String para0;
    
    /**
    * 参数2
    */
    @Column(length=100)
    @ApiModelProperty("参数2")
    @Length(message = "参数2{validation.message.length}", max = 100)
    private String para1;
    
    /**
    * 参数3
    */
    @Column(length=100)
    @ApiModelProperty("参数3")
    @Length(message = "参数3{validation.message.length}", max = 100)
    private String para2;
    
    /**
    * 参数4
    */
    @Column(length=100)
    @ApiModelProperty("参数4")
    @Length(message = "参数4{validation.message.length}", max = 100)
    private String para3;
    
    /**
    * 组织机构id
    */
    @Column(length=50)
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 最新更新时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("最新更新时间")
    private Date lastUpdateTime;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }