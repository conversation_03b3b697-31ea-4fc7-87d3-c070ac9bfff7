package com.sinoyd.lims.lim.service;

import com.sinoyd.lims.lim.dto.lims.DtoPersonFaceMsg;
import com.sinoyd.frame.service.IBaseJpaService;


/**
 * PersonFaceMsg操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/9/26
 * @since V100R001
 */
public interface PersonFaceMsgService extends IBaseJpaService<DtoPersonFaceMsg, String> {

    /**
     * 通过人员id获取face信息
     *
     * @param personId 人员id
     * @return face信息
     */
    DtoPersonFaceMsg getPersonFaceMsg(String personId);
}