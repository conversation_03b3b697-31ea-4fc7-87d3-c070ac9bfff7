package com.sinoyd.lims.od.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.EnvGasCalculationUtil;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.od.dto.*;
import com.sinoyd.lims.od.enums.EnumOds;
import com.sinoyd.lims.od.repository.EnvGasResultRepository;
import com.sinoyd.lims.od.repository.LabGroupRepository;
import com.sinoyd.lims.od.repository.SampleResultRepository;
import com.sinoyd.lims.od.repository.TaskRepository;
import com.sinoyd.lims.od.service.EnvGasResultService;
import com.sinoyd.lims.od.service.OdPersonAnswerService;
import com.sinoyd.lims.od.service.OdSampleService;
import com.sinoyd.lims.pro.service.AnalyseDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 环境空气结果接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
public class EnvGasResultServiceImpl extends BaseJpaServiceImpl<DtoEnvGasResult, String, EnvGasResultRepository> implements EnvGasResultService {

    private LabGroupRepository labGroupRepository;
    private OdSampleService odSampleService;
    private TaskRepository taskRepository;
    private OdPersonAnswerService odPersonAnswerService;
    private SampleResultRepository sampleResultRepository;
    private AnalyseDataService analyseDataService;

    @Override
    public void findByPage(PageBean<DtoEnvGasResult> page, BaseCriteria criteria) {
        page.setEntityName("DtoEnvGasResult a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
    }


    @Override
    @Transactional
    public void calculateMValue(DtoLabSeq labSeq) {
        // 当环境空气类型中， 实验组中的三轮数据全部提交，自动计算M值
        if (labSeq.getSn() == 3) {
            // 刷新环境空气中的M值
            DtoLabGroup labGroup = labGroupRepository.findOne(labSeq.getGroupId());
            DtoOdSample sample = odSampleService.findOne(labGroup.getSampleId());
            DtoTask task = taskRepository.findOne(sample.getTaskId());
            if (task.getTaskType().equals(EnumOds.EnumTaskType.环境空气.getValue())) {
                List<DtoOdPersonAnswer> personAnswerList = odPersonAnswerService.findByGroupIdIn(Collections.singletonList(labGroup.getId()));
                // 初始化环境空气汇总结果
                DtoEnvGasResult result = repository.findByGroupId(labGroup.getId());
                DtoEnvGasResult envGasResult = StringUtil.isNotNull(result) ? result : new DtoEnvGasResult();
                List<DtoLabGroup> updateGroupList = new ArrayList<>();
                envGasResult.setDilutionRate(labSeq.getDilutionRate());
                int aValue = (int) personAnswerList.stream().filter(p -> p.getEvaluation().equals(EnumOds.EnumEvaluation.正确.getValue())).count();
                int bValue = (int) personAnswerList.stream().filter(p -> p.getEvaluation().equals(EnumOds.EnumEvaluation.不明.getValue())).count();
                int cValue = (int) personAnswerList.stream().filter(p -> p.getEvaluation().equals(EnumOds.EnumEvaluation.错误.getValue())).count();
                log.info("================= 开始计算M值 a = {}, b = {}, c = {} =================", aValue, bValue, cValue);
                String mValue = EnvGasCalculationUtil.calculateMValue(aValue, bValue, cValue, 2);
                log.info("================= 结束计算M值 M = {}  =================", mValue);
                envGasResult.setACount(aValue);
                envGasResult.setBCount(bValue);
                envGasResult.setCCount(cValue);
                envGasResult.setMValue(mValue);
                envGasResult.setGroupId(labGroup.getId());
                // 如果为第一组实验并且当M1<=0.58，显示通过，臭气浓度为<10
                if (MathUtil.isNumber(mValue) && (new BigDecimal(mValue).compareTo(new BigDecimal("0.58")) <= 0)) {
                    envGasResult.setIsPassed(true);
                    labGroup.setIsCalculate(true);
                    if (labGroup.getSn() == 1) {
                        // 实验组数据更新，作为最终分析结果
                        labGroup.setCalculateSn(1);
                        DtoSampleResult sampleResult = new DtoSampleResult();
                        sampleResult.setSampleId(sample.getId());
                        sampleResult.setOdourConsistence("<10");
                        sampleResult.setIsPassed(true);
                        sampleResultRepository.save(sampleResult);
                        // 完成样品
                        odSampleService.completeOdSample(sample.getId());
                        // 样品结果臭气浓度反推至LIMS
                        analyseDataService.updateByOdsSample(sample.getSampleCode(), sampleResult.getOdourConsistence());
                    } else {
                        labGroup.setCalculateSn(2);
                        // 选在当前组和上一组为最终结果，暂不计算臭气浓度，需要汇总页面重新计算
                        List<DtoLabGroup> labGroupList = labGroupRepository.findBySampleId(sample.getId());
                        labGroupList.stream().filter(p -> p.getSn().equals(labGroup.getSn() - 1)).findFirst().ifPresent(group -> {
                            group.setIsCalculate(true);
                            group.setCalculateSn(1);
                            updateGroupList.add(group);
                        });
                    }
                    updateGroupList.add(labGroup);
                }
                // 更新环境空气结果
                repository.save(envGasResult);
                // 更新实验组最终分析次序信息
                if (StringUtil.isNotEmpty(updateGroupList)) {
                    labGroupRepository.save(updateGroupList);
                }
            }
        }
    }


    @Autowired
    @Lazy
    public void setLabGroupRepository(LabGroupRepository labGroupRepository) {
        this.labGroupRepository = labGroupRepository;
    }

    @Autowired
    @Lazy
    public void setOdSampleService(OdSampleService odSampleService) {
        this.odSampleService = odSampleService;
    }

    @Autowired
    @Lazy
    public void setTaskRepository(TaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }

    @Autowired
    @Lazy
    public void setOdPersonAnswerService(OdPersonAnswerService odPersonAnswerService) {
        this.odPersonAnswerService = odPersonAnswerService;
    }

    @Autowired
    @Lazy
    public void setSampleResultRepository(SampleResultRepository sampleResultRepository) {
        this.sampleResultRepository = sampleResultRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }
}
