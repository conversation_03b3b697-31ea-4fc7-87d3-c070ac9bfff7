package com.sinoyd.base.entity;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 系统信息管理配置实体
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/12/8
 **/
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "SystemConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SystemConfig extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public SystemConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 机构编码
     */
    @Column(length = 50)
    @ApiModelProperty("机构编码")
    @Length(message = "机构编码{validation.message.length}", max = 50)
    private String orgCode;

    /**
     * 系统名称全写
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("系统名称全写")
    @Length(message = "系统名称全写{validation.message.length}", max = 50)
    private String fullName;

    /**
     * 系统名称简写
     */
    @Column(length = 50)
    @ApiModelProperty("系统名称简写")
    @Length(message = "系统名称简写{validation.message.length}", max = 50)
    private String shortName;

    /**
     * 欢迎登陆语
     */
    @Column
    @ApiModelProperty("欢迎登陆语")
    @Length(message = "欢迎登陆语{validation.message.length}", max = 255)
    private String welcomeWord;

    /**
     * 企业名称
     */
    @Column
    @ApiModelProperty("企业名称")
    @Length(message = "企业名称{validation.message.length}", max = 255)
    private String companyName;

    /**
     * 企业地址
     */
    @Column(length = 1000)
    @ApiModelProperty("企业地址")
    @Length(message = "企业地址{validation.message.length}", max = 1000)
    private String companyAddress;

    /**
     * 企业邮编
     */
    @Column(length = 20)
    @ApiModelProperty("企业邮编")
    @Length(message = "企业邮编{validation.message.length}", max = 20)
    private String companyPostCode;

    /**
     * 企业联系方式
     */
    @Column(length = 50)
    @ApiModelProperty("企业联系方式")
    @Length(message = "企业联系方式{validation.message.length}", max = 50)
    private String companyPhone;

    /**
     * 企业英文名称
     */
    @Column
    @ApiModelProperty("企业英文名称")
    @Length(message = "企业英文名称{validation.message.length}", max = 255)
    private String companyEnglishName;

    /**
     * 允许上传的文件类型
     */
    @Column
    @ApiModelProperty("允许上传的文件类型")
    private String allowFileSuffix;

    /**
     * 开户行
     */
    @Column
    @ApiModelProperty("开户行")
    @Length(message = "开户行{validation.message.length}", max = 50)
    private String bank;

    /**
     * 银行账户
     */
    @Column
    @ApiModelProperty("银行账户")
    @Length(message = "银行账户{validation.message.length}", max = 50)
    private String bankAccount;

    /**
     * 税号
     */
    @Column
    @ApiModelProperty("税号")
    @Length(message = "银行账户{validation.message.length}", max = 50)
    private String taxNumber;

    /**
     * 联系人
     */
    @Column(length = 50)
    @ApiModelProperty("联系人")
    @Length(message = "银行账户{validation.message.length}", max = 50)
    private String linkMan;


    /**
     * 法人代表
     */
    @Column(length = 50)
    @ApiModelProperty("法人代表")
    @Length(message = "银行账户{validation.message.length}", max = 50)
    private String legalRepresentative;


    /**
     * 成立时间
     */
    @ApiModelProperty("成立时间")
    private Date establishTime;


    /**
     * 实验室面积
     */
    @Column(length = 50)
    @ApiModelProperty("实验室面积")
    @Length(message = "实验室面积{validation.message.length}", max = 50)
    private String labArea;

    /**
     * 在职人数
     */
    @ApiModelProperty("在职人数")
    private Integer staffCount;

    /**
     * 注册资金
     */
    @Column(length = 50)
    @ApiModelProperty("注册资金")
    @Length(message = "注册资金{validation.message.length}", max = 50)
    private String registeredCapital;

    /**
     * 资质证书
     */
    @Column(length = 50)
    @ApiModelProperty("资质证书")
    @Length(message = "资质证书{validation.message.length}", max = 50)
    private String certificate;


    /**
     * CMA能力范围
     */
    @Column(length = 2000)
    @ApiModelProperty("CMA能力范围")
    private String cmaScope;

    /**
     * CNAS能力范围
     */
    @Column(length = 2000)
    @ApiModelProperty("CNAS能力范围")
    private String cnasScope;

    /**
     * 传真
     */
    @ApiModelProperty("传真")
    @Length(message = "传真{validation.message.length}", max = 255)
    private String fax;

    /**
     * 资质有效期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("资质有效期")
    private Date certificateExpiryDate;

    /**
     * 经度
     */
    @Column(length=50)
    @ApiModelProperty("经度")
    @Length(message = "传真{validation.message.length}", max = 50)
    private String longitude;

    /**
     * 纬度
     */
    @Column(length=50)
    @ApiModelProperty("纬度")
    @Length(message = "传真{validation.message.length}", max = 50)
    private String latitude;

    /**
     * 页面签章编码
     */
    @Column(length=100)
    @ApiModelProperty("页面签章编码")
    @Length(message = "页面签章编码{validation.message.length}", max = 100)
    private String sealCode;

    /**
     * 页面签章规则
     */
    @ApiModelProperty("页面签章规则")
    @Length(message = "页面签章规则{validation.message.length}", max = 255)
    private String sealRule;

    /**
     * 骑缝章签章编码
     */
    @Column(length=100)
    @ApiModelProperty("骑缝章签章编码")
    @Length(message = "骑缝章签章编码{validation.message.length}", max = 100)
    private String continuousSealCode;

    /**
     * 骑缝章签章规则
     */
    @ApiModelProperty("骑缝章签章规则")
    @Length(message = "骑缝章签章规则{validation.message.length}", max = 255)
    private String continuousSealRule;


    /**
     * cma签章高度
     */
    @Column(length=10)
    @ApiModelProperty("cma签章高度")
    @Length(message = "cma签章高度{validation.message.length}", max = 10)
    private String cmaHeight;

    /**
     * cma签章宽度度
     */
    @Column(length=10)
    @ApiModelProperty("cma签章宽度度")
    @Length(message = "cma签章宽度度{validation.message.length}", max = 10)
    private String cmaWidth;

    /**
     * cnas签章高度
     */
    @Column(length=10)
    @ApiModelProperty("cnas签章高度")
    @Length(message = "cnas签章高度{validation.message.length}", max = 10)
    private String cnasHeight;

    /**
     * cnas签章宽度度
     */
    @Column(length=10)
    @ApiModelProperty("骑缝章签章编码")
    @Length(message = "骑缝章签章编码{validation.message.length}", max = 10)
    private String cnasWidth;

    /**
     * 检测检疫签章高度
     */
    @Column(length=10)
    @ApiModelProperty("检测检疫签章高度")
    @Length(message = "检测检疫签章高度{validation.message.length}", max = 10)
    private String checkHeight;

    /**
     * 检测检疫签章宽度度
     */
    @Column(length=10)
    @ApiModelProperty("检测检疫签章宽度度")
    @Length(message = "检测检疫签章宽度度{validation.message.length}", max = 10)
    private String checkWidth;


    /**
     * 简介
     */
    @Column(length = 4000)
    @ApiModelProperty("简介")
    private String introduction;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("创建人")
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ApiModelProperty("创建时间")
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("修改人")
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ApiModelProperty("修改时间")
    @LastModifiedDate
    private Date modifyDate;
}
