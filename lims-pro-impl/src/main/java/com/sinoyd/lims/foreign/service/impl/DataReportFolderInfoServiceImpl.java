package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataReportBaseInfoVO;
import com.sinoyd.lims.foreign.DataReportFolderInfoVO;
import com.sinoyd.lims.foreign.DataReportFolderSortInfoVO;
import com.sinoyd.lims.foreign.service.DataReportFolderInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报告点位信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataReportFolderInfoServiceImpl extends AbsQueryDataServiceImpl<DataReportFolderInfoVO> implements DataReportFolderInfoService {


    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id,reportId,folderId,folderName,");
        sql.append("folderCode,folderRemark,orgId,domainId");
        sql.append(" from TB_PRO_ReportFolderInfo ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }
    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime", "syncTime");
        super.appendCondition(sql, map, "AND reportId in (:reportIds)", "reportIds");
        super.appendCondition(sql, map, "AND folderId in (:folderIds) ", "AND folderId in (:folderIds) ");
    }
    @Override
    protected RowMapper<DataReportFolderInfoVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataReportFolderInfoVO reportFolderInfoVO = new DataReportFolderInfoVO();
            reportFolderInfoVO.setId(UUIDHelper.NewID());
            reportFolderInfoVO.setReportId(rs.getString("reportId"));
            reportFolderInfoVO.setFolderId(rs.getString("folderId"));
            reportFolderInfoVO.setFolderName(rs.getString("folderName"));
            reportFolderInfoVO.setFolderCode(rs.getString("folderCode"));
            reportFolderInfoVO.setFolderRemark(rs.getString("folderRemark"));
            reportFolderInfoVO.setOrgId(rs.getString("orgId"));
            reportFolderInfoVO.setDomainId(rs.getString("domainId"));
            reportFolderInfoVO.setSyncTime(new Date());
            return reportFolderInfoVO;
        };
    }
}
