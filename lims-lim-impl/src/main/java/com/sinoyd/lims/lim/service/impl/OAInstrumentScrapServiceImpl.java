package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentScrap;
import com.sinoyd.lims.lim.repository.lims.OAInstrumentScrapRepository;
import com.sinoyd.lims.lim.service.OAInstrumentScrapService;

import org.springframework.stereotype.Service;

/**
 * 仪器报废 业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
@Service
public class OAInstrumentScrapServiceImpl
        extends BaseJpaServiceImpl<DtoOAInstrumentScrap, String, OAInstrumentScrapRepository>
        implements OAInstrumentScrapService {

}
