package com.sinoyd.lims.monitor.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.FixedPointSortDetilService;
import com.sinoyd.lims.monitor.criteria.FixedPointSortDetilCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSortDetil;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FixedPointSortDetil服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: FixedPointSortDetil服务")
 @RestController
 @RequestMapping("api/monitor/fixedPointSortDetil")
 public class FixedPointSortDetilController extends BaseJpaController<DtoFixedPointSortDetil, String,FixedPointSortDetilService> {


    /**
     * 分页动态条件查询FixedPointSortDetil
     * @param fixedPointSortDetilCriteria 条件参数
     * @return RestResponse<List<FixedPointSortDetil>>
     */
     @ApiOperation(value = "分页动态条件查询FixedPointSortDetil", notes = "分页动态条件查询FixedPointSortDetil")
     @GetMapping
     public RestResponse<List<DtoFixedPointSortDetil>> findByPage(FixedPointSortDetilCriteria fixedPointSortDetilCriteria) {
         PageBean<DtoFixedPointSortDetil> pageBean = super.getPageBean();
         RestResponse<List<DtoFixedPointSortDetil>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fixedPointSortDetilCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FixedPointSortDetil
     * @param id 主键id
     * @return RestResponse<DtoFixedPointSortDetil>
     */
     @ApiOperation(value = "按主键查询FixedPointSortDetil", notes = "按主键查询FixedPointSortDetil")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFixedPointSortDetil> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFixedPointSortDetil> restResponse = new RestResponse<>();
         DtoFixedPointSortDetil fixedPointSortDetil = service.findOne(id);
         restResponse.setData(fixedPointSortDetil);
         restResponse.setRestStatus(StringUtil.isNull(fixedPointSortDetil) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FixedPointSortDetil
     * @param fixedPointSortDetil 实体列表
     * @return RestResponse<DtoFixedPointSortDetil>
     */
     @ApiOperation(value = "新增FixedPointSortDetil", notes = "新增FixedPointSortDetil")
     @PostMapping
     public RestResponse<DtoFixedPointSortDetil> create(@RequestBody DtoFixedPointSortDetil fixedPointSortDetil) {
         RestResponse<DtoFixedPointSortDetil> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fixedPointSortDetil));
         return restResponse;
      }

     /**
     * 新增FixedPointSortDetil
     * @param fixedPointSortDetil 实体列表
     * @return RestResponse<DtoFixedPointSortDetil>
     */
     @ApiOperation(value = "修改FixedPointSortDetil", notes = "修改FixedPointSortDetil")
     @PutMapping
     public RestResponse<DtoFixedPointSortDetil> update(@RequestBody DtoFixedPointSortDetil fixedPointSortDetil) {
         RestResponse<DtoFixedPointSortDetil> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fixedPointSortDetil));
         return restResponse;
      }

    /**
     * "根据id批量删除FixedPointSortDetil
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FixedPointSortDetil", notes = "根据id批量删除FixedPointSortDetil")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }