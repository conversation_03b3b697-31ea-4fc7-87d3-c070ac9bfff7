package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * SamplingFrequencyTestTemp实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "SamplingFrequencyTestTemp")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SamplingFrequencyTestTemp implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 点位频次修改方案id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("点位频次修改方案id")
    private String samplingFrequencyTempId;

    /**
     * 点位频次测试项目关联表id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("点位频次测试项目关联表id")
    private String samplingFrequencyTestId;

    /**
     * 操作类型
     */
    @Column(nullable=false)
    @ApiModelProperty("操作类型")
    private Integer operateType;

    /**
     * 测试项目id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("测试项目id")
    private String testId;

    /**
     * 是否采样分包
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否采样分包")
    private Boolean isSamplingOut;

    /**
     * 分析项目id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析项目id")
    private String analyseItemId;

    /**
     * 分析方法id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析方法id")
    private String analyzeMethodId;

    /**
     * 分析项目名称
     */
    @Column(length = 50)
    @ApiModelProperty("分析项目名称")
    private String redAnalyzeItemName;

    /**
     * 分析方法名称
     */
    @Column(length = 255)
    @ApiModelProperty("分析方法名称")
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    @Column(length = 50)
    @ApiModelProperty("国家标准")
    private String redCountryStandard;

    /**
     * 是否现场数据
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否现场数据")
    private Boolean isCompleteField;

    /**
     * 是否分包
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否分包")
    private Boolean isOutsourcing;

}
