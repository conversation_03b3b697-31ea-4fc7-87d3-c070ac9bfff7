package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.customer.DtoReportInfo;
import com.sinoyd.lims.pro.service.ElectronicReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 电子报告服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
@Api(tags = "电子报告服务")
@RestController
@RequestMapping("/api/pro/electronicReport")
public class ElectronicReportController extends ExceptionHandlerController<ElectronicReportService> {

    @ApiOperation(value = "保存电子报告信息", notes = "保存电子报告信息")
    @PostMapping
    public RestResponse<String> saveElectronicReportInfo(@RequestBody DtoReportInfo reportInfo) {
        RestResponse<String> restResp = new RestResponse<>();
        service.saveReportInfo(reportInfo);
        restResp.setMsg("操作成功！");
        return restResp;
    }

    @ApiOperation(value = "自动获取电子报告信息", notes = "自动获取电子报告信息")
    @GetMapping("/auto")
    public RestResponse<DtoReportInfo> autoGenerateReportInfo(String reportId) {
        RestResponse<DtoReportInfo> restResp = new RestResponse<>();
        restResp.setData(service.autoGenerate(reportId));
        restResp.setMsg("操作成功！");
        return restResp;
    }

    @ApiOperation(value = "查询电子报告点位样品信息", notes = "查询电子报告点位样品信息")
    @GetMapping
    public RestResponse<DtoReportInfo> queryReportInfo(String reportId) {
        RestResponse<DtoReportInfo> restResp = new RestResponse<>();
        restResp.setData(service.queryReportInfo(reportId));
        restResp.setMsg("操作成功！");
        return restResp;
    }


    @ApiOperation(value = "获取报告所有点位并按照给定的点位排序规则进行排序", notes = "获取报告所有点位并按照给定的点位排序规则进行排序")
    @PostMapping("/getFolder")
    public RestResponse<List<Map<String, String>>> getFolderForReport(@RequestBody Map<String, String> map) {
        RestResponse<List<Map<String, String>>> restResp = new RestResponse<>();
        String reportId = map.getOrDefault("reportId", UUIDHelper.GUID_EMPTY);
        String folderSortId = map.getOrDefault("sampleFolderSortId", UUIDHelper.GUID_EMPTY);
        restResp.setData(service.getSortFolderForReport(reportId, folderSortId));
        restResp.setMsg("操作成功！");
        return restResp;
    }

    @ApiOperation(value = "清空电子报告信息", notes = "清空报告信息")
    @PostMapping("/clear")
    public RestResponse<String> clearReportInfo(@RequestBody Map<String, String> map) {
        RestResponse<String> restResp = new RestResponse<>();
        String reportId = map.getOrDefault("reportId", UUIDHelper.GUID_EMPTY);
        service.clearReportInfo(reportId);
        restResp.setMsg("操作成功！");
        return restResp;
    }

    /**
     * 上传点位示意图
     *
     * @param request 请求体
     */
    @ApiOperation(value = "上传点位示意图", notes = "上传点位示意图")
    @PostMapping("/uploadFolderSketch")
    public RestResponse<List<DtoDocument>> uploadFolderSketchDoc(HttpServletRequest request) {
        RestResponse<List<DtoDocument>> response = new RestResponse<>();
        List<DtoDocument> documentList = service.uploadFolderSketch(request, Arrays.asList("jpg", "、jpeg", "png"));
        response.setData(documentList);
        response.setRestStatus(StringUtil.isNull(documentList) ? ERestStatus.ERROR : ERestStatus.SUCCESS);
        return response;
    }

    /**
     * 报告上传
     *
     * @param request 请求体
     * @return 项目和报告的关系
     */
    @ApiOperation(value = "报告上传", notes = "报告上传")
    @PostMapping("/uploadReport")
    public RestResponse<DtoDocument> uploadReport(HttpServletRequest request) {
        RestResponse<DtoDocument> response = new RestResponse<>();
        request.setAttribute("folderName", "报告文件");
        response.setData(service.uploadReport(request));
        response.setMsg("上传成功！");
        response.setRestStatus(ERestStatus.SUCCESS);
        return response;
    }

    /**
     * 报告下载
     *
     * @param reportId 报告id
     * @param response 响应流
     * @return 返回数据
     */
    @ApiOperation(value = "报告下载", notes = "报告下载")
    @GetMapping("/downloadReport/{reportId}")
    public RestResponse<String> fileDownload(@PathVariable String reportId, Boolean isCopy, HttpServletResponse response) throws IOException {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.downloadReport(reportId,isCopy, response));
        return restResp;
    }

    /**
     * 获取下载的报告附件名称
     *
     * @param reportId 报告id
     * @param isCopy   是否副本
     * @return 返回数据
     */
    @ApiOperation(value = "获取下载的报告附件名称", notes = "获取下载的报告附件名称")
    @GetMapping("/docName/{reportId}")
    public RestResponse<String> fileDownload(@PathVariable String reportId, Boolean isCopy) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.getDocName(reportId, isCopy));
        return restResp;
    }

    /**
     * 查询报告技术备注说明
     *
     * @return RestResponse<ConfigModel>
     */
    @ApiOperation(value = "查询报告技术备注说明", notes = "查询报告技术备注说明")
    @GetMapping("/findRemark")
    public RestResponse<ConfigModel> findRemark() {
        RestResponse<ConfigModel> response = new RestResponse<>();
        response.setData(service.findTechnicalRemark());
        return response;
    }

    /**
     * 查询报告总称测试项目
     *
     * @return RestResponse<ConfigModel>
     */
    @ApiOperation(value = "查询报告总称测试项目", notes = "查询报告总称测试项目")
    @GetMapping("/getMergeTest")
    public RestResponse<List<DtoTest>> findMergeTest(String reportId) {
        RestResponse<List<DtoTest>> response = new RestResponse<>();
        response.setData(service.findReportMergeTest(reportId));
        return response;
    }

}

