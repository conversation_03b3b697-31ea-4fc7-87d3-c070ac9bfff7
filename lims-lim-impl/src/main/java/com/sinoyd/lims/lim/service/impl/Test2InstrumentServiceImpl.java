package com.sinoyd.lims.lim.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoTest2Instrument;
import com.sinoyd.lims.lim.repository.lims.Test2InstrumentRepository;
import com.sinoyd.lims.lim.service.Test2InstrumentService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 使用仪器
 * <AUTHOR>
 * @version V1.0.0 2019/1/17
 * @since V100R001
 */
@Service
public class Test2InstrumentServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoTest2Instrument, String, Test2InstrumentRepository> implements Test2InstrumentService {

    @Autowired
    private InstrumentRepository instrumentRepository;

    /**
     * 新增使用仪器(去重)
     */
    @Override
    @Transactional
    public Integer addTestInstrument(String testId, Collection<String> instrumentIds, Integer useType) {
        ArrayList<DtoTest2Instrument> t2iList = new ArrayList<DtoTest2Instrument>();
        for (String insId : instrumentIds) {
            Integer count = repository.getCountByNameAndId(testId, insId, useType);
            if (count == 0) {
                DtoTest2Instrument t2i = new DtoTest2Instrument();
                t2i.setTestId(testId);
                t2i.setInstrumentId(insId);
                t2i.setUseType(useType);
                t2i.setId(UUID.randomUUID().toString());
                t2iList.add(t2i);
            }
        }
        super.save(t2iList);
        return t2iList.size();
    }

    /**
     * 删除使用仪器
     */
    @Transactional
    @Override
    public Integer deleteTestInstrument(String testId, Collection<String> instrumentIds, Integer useType) {

        Integer count = repository.deleteByTestIdAndInsIds(testId, instrumentIds, useType);

        return count;
    }


    //TODO:此处涉及到前台展现仪器的数据

    /**
     * 获取使用仪器列表
     *
     * @param testId
     * @param useType
     * @return
     */
    @Override
    public List<DtoInstrument> getList(String testId, Integer useType) {
        List<DtoInstrument> list = new ArrayList<>();
        List<DtoTest2Instrument> t2iList = repository.findByTestIdAndUseType(testId, useType);
        if (t2iList.size() > 0) {
            Collection<String> ids = t2iList.stream().map(p -> p.getInstrumentId()).distinct().collect(Collectors.toList());
            list = instrumentRepository.findByIds(ids);
        }

        return list;


    }

}