package com.sinoyd.base.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;

import java.util.Collection;
import java.util.Map;

/**
 * LIMS基础事件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/12
 */
@Slf4j
public class LIMSEvent<T> extends ApplicationEvent {

    /**
     * 事件动作
     */
    public final String action;

    /**
     * 事件名称
     */
    public final String name;

    /**
     * 构造方法， 一般用于新增和更新动作
     *
     * @param t      实体类型
     * @param name   事件名
     * @param action 动作
     */
    public LIMSEvent(T t, String name, String action) {
        super(t);
        this.action = action;
        this.name = name;
        log.info("======= 发布了" + action + "事件: " + name + "=======");
    }

    /**
     * 构造方法， 一般用于删除动等动作
     *
     * @param ts     事件源集合
     * @param name   事件名
     * @param action 动作
     */
    public LIMSEvent(Collection<T> ts, String name, String action) {
        super(ts);
        this.action = action;
        this.name = name;
        log.info("======= 发布了" + action + "事件: " + name + "=======");
    }

    /**
     * 构造方法， 一般用于新增和更新动作
     *
     * @param map    事件源信息
     * @param name   事件名
     * @param action 动作
     */
    public LIMSEvent(Map<String, String> map, String name, String action) {
        super(map);
        this.action = action;
        this.name = name;
        log.info("======= 发布了" + action + "事件: " + name + "=======");
    }

    /**
     * 返回事件源实体
     *
     * @return 实体
     */
    @Override
    public T getSource() {
        log.info("======= 监听了" + name + action + "事件=======");
        return (T) super.getSource();
    }

    /**
     * 返回事件源id集合
     *
     * @return id集合
     */
    public Collection<T> getSourceList() {
        log.info("======= 监听了" + name + action + "事件=======");
        return (Collection<T>) super.getSource();
    }

    /**
     * 返回事件源Map
     *
     * @return 事件源map
     */
    public Map<String, String> getSourceMap() {
        log.info("======= 监听了" + name + action + "事件=======");
        return (Map<String, String>) super.getSource();
    }

}