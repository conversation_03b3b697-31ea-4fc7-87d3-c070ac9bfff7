CREATE TABLE "TB_PRO_ANALYSEDATA"
(
    "id"                   VARCHAR(50)                NOT NULL,
    "workSheetId"          VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "workSheetFolderId"    VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "subId"                VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "sampleId"             VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "testId"               VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "redAnalyzeItemName"   VARCHAR(100) NULL,
    "redAnalyzeMethodName" VARCHAR(255) NULL,
    "redCountryStandard"   VARCHAR(255) NULL,
    "analyseItemId"        VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "analyzeMethodId"      VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "qcId"                 VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "qcType"               INT             DEFAULT (-1)
                                                      NOT NULL,
    "qcGrade"              INT             DEFAULT (-1)
                                                      NOT NULL,
    "isQm"                 BIT             DEFAULT 0
                                                      NOT NULL,
    "receiveSubId"         VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "mostSignificance"     INT             DEFAULT (-1)
                                                      NOT NULL,
    "mostDecimal"          INT             DEFAULT (-1)
                                                      NOT NULL,
    "examLimitValue"       VARCHAR(50) NULL,
    "dimensionId"          VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "dimension"            VARCHAR(50) NULL,
    "testValue"            VARCHAR(100) NULL,
    "testOrignValue"       VARCHAR(100) NULL,
    "testValueD"           DECIMAL(20, 10) DEFAULT 0. NOT NULL,
    "testValueDstr"        VARCHAR(100) NULL,
    "status"               VARCHAR(50) NULL,
    "dataStatus"           INT             DEFAULT 1
                                                      NOT NULL,
    "dataChangeStatus"     INT             DEFAULT 0
                                                      NOT NULL,
    "analystId"            VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "analystName"          VARCHAR(50) NULL,
    "analyzeTime"          TIMESTAMP(0)    DEFAULT '1753-01-01 00:00:00'
                                                      NOT NULL,
    "dataInputTime"        TIMESTAMP(0)    DEFAULT '1753-01-01 00:00:00'
                                                      NOT NULL,
    "isDataEnabled"        BIT             DEFAULT 0
                                                      NOT NULL,
    "isCompleteField"      BIT             DEFAULT 0
                                                      NOT NULL,
    "isOutsourcing"        BIT             DEFAULT 0
                                                      NOT NULL,
    "isDeleted"            BIT             DEFAULT 0
                                                      NOT NULL,
    "requireDeadLine"      TIMESTAMP(0)    DEFAULT '1753-01-01 00:00:00'
                                                      NOT NULL,
    "grade"                INT             DEFAULT 0
                                                      NOT NULL,
    "deptId"               VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "isQualified"          BIT             DEFAULT 0
                                                      NOT NULL,
    "repeatTimes"          INT             DEFAULT 0
                                                      NOT NULL,
    "testTaskId"           VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "isPostCert"           BIT             DEFAULT 0
                                                      NOT NULL,
    "certEffectiveTime"    TIMESTAMP(0)    DEFAULT '1753-01-01 00:00:00'
                                                      NOT NULL,
    "orgId"                VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "creator"              VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "createDate"           TIMESTAMP(0)    DEFAULT CURRENT_TIMESTAMP()
                                                      NOT NULL,
    "domainId"             VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "modifier"             VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
                                                      NOT NULL,
    "modifyDate"           TIMESTAMP(0)    DEFAULT CURRENT_TIMESTAMP()
                                                      NOT NULL,
    "sampleTypeId"         VARCHAR(50)     DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "gatherCode"           VARCHAR(1000) NULL,
    "qcInfo"               VARCHAR(100) NULL,
    "seriesValue"          VARCHAR(100) NULL,
    "finishTime"           TIMESTAMP(0)    DEFAULT '1753-01-01 00:00:00'
                                                      NOT NULL,
    "isSamplingOut"        BIT             DEFAULT 0
                                                      NOT NULL,
    "sampleReceiveDate"    TIMESTAMP(0)    DEFAULT '1753-01-01 00:00:00'
                                                      NOT NULL,
    "isSci"                BIT             DEFAULT 0
                                                      NOT NULL
);
CREATE TABLE "TB_PRO_ANALYSEORIGINALRECORD"
(
    "id"            VARCHAR(50) NOT NULL,
    "analyseDataId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "json"          CLOB NULL,
    "testFormulaId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "testFormula"   VARCHAR(255) NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "isDeleted"     BIT          DEFAULT 0
                                NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL
);
CREATE TABLE "TB_PRO_AUTOTASKPLAN"
(
    "id"              VARCHAR(50) NOT NULL,
    "taskName"        VARCHAR(50)  DEFAULT ''
                                  NOT NULL,
    "taskCode"        VARCHAR(50)  DEFAULT ''
                                  NOT NULL,
    "dealCycle"       INT          DEFAULT 0
                                  NOT NULL,
    "dealNum"         INT          DEFAULT 0
                                  NOT NULL,
    "dealDate"        VARCHAR(50)  DEFAULT ''
        NULL,
    "projectCodeRule" INT          DEFAULT 0
                                  NOT NULL,
    "inputPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "leaderId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "isStress"        BIT          DEFAULT 0
                                  NOT NULL,
    "grade"           INT          DEFAULT 0
                                  NOT NULL,
    "month"           VARCHAR(100) DEFAULT ''
        NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "projectTypeId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL
);
CREATE TABLE "TB_PRO_BUSINESSSERIALNUMBER"
(
    "id"               VARCHAR(50) NOT NULL,
    "businessType"     VARCHAR(50) NOT NULL,
    "businessId"       VARCHAR(50) NOT NULL,
    "businessNumber"   VARCHAR(50) NOT NULL,
    "serialNumberType" VARCHAR(50) NOT NULL,
    "para0"            VARCHAR(50) NULL,
    "para1"            VARCHAR(50) NULL,
    "para2"            VARCHAR(50) NULL,
    "para3"            VARCHAR(50) NULL,
    "isDeleted"        BIT          DEFAULT 0
                                   NOT NULL,
    "orgId"            VARCHAR(50) NOT NULL,
    "domainId"         VARCHAR(50) NOT NULL,
    "creator"          VARCHAR(50) NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "modifier"         VARCHAR(50) NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL
);
CREATE TABLE "TB_PRO_COMMENT"
(
    "id"                VARCHAR(50) NOT NULL,
    "parentId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "objectId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "objectType"        INT          DEFAULT 0
                                    NOT NULL,
    "commentPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "commentPersonName" VARCHAR(50) NULL,
    "commentTime"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "comment"           CLOB NULL,
    "commentType"       INT          DEFAULT 1
                                    NOT NULL,
    "isDeleted"         BIT          DEFAULT 0
                                    NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL
);
CREATE TABLE "TB_PRO_COMMENTCOMPLIMENTDETAIL"
(
    "id"               VARCHAR(50) NOT NULL,
    "commentId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "complimentorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "complimentorName" VARCHAR(50) NULL,
    "complimentNumber" INT          DEFAULT 0
                                   NOT NULL,
    "complimentDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "complimentType"   INT          DEFAULT 0
                                   NOT NULL,
    "option"           CLOB NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_COSTINFO"
(
    "id"                 VARCHAR(50)    NOT NULL,
    "projectId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "samplingCost"       DECIMAL(18, 2) NOT NULL,
    "analyzeCost"        DECIMAL(18, 2) NOT NULL,
    "reportCost"         DECIMAL(18, 2) NOT NULL,
    "climbCost"          DECIMAL(18, 2) NOT NULL,
    "expertCost"         DECIMAL(18, 2) NOT NULL,
    "otherCost"          DECIMAL(18, 2) NOT NULL,
    "laborNum"           INT          DEFAULT 0
                                        NOT NULL,
    "laborDay"           INT          DEFAULT 0
                                        NOT NULL,
    "laborUnit"          DECIMAL(18, 2) NOT NULL,
    "laborCost"          DECIMAL(18, 2) NOT NULL,
    "carNum"             INT          DEFAULT 0
                                        NOT NULL,
    "carDay"             INT          DEFAULT 0
                                        NOT NULL,
    "carUnit"            DECIMAL(18, 2) NOT NULL,
    "carCost"            DECIMAL(18, 2) NOT NULL,
    "expectedTotalCost"  DECIMAL(18, 2) NOT NULL,
    "reportRate"         DECIMAL(18, 2) NOT NULL,
    "offerRate"          DECIMAL(18, 2) NOT NULL,
    "taxManageCost"      DECIMAL(18, 2) NOT NULL,
    "acturalTotalCost"   DECIMAL(18, 2) NOT NULL,
    "schemeChangeStatus" INT          DEFAULT 0
                                        NOT NULL,
    "status"             VARCHAR(20)    NOT NULL,
    "isDeleted"          BIT          DEFAULT 0
                                        NOT NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                        NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                        NOT NULL
);
CREATE TABLE "TB_PRO_COSTINFODETAIL"
(
    "id"                   VARCHAR(50)    NOT NULL,
    "costInfoId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "testId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "redAnalyzeItemName"   VARCHAR(50) NULL,
    "redAnalyzeMethodName" VARCHAR(255) NULL,
    "redCountryStandard"   VARCHAR(100) NULL,
    "sampleTypeId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "sampleNum"            INT          DEFAULT 0
                                          NOT NULL,
    "samplingConfigCost"   DECIMAL(18, 2) NULL,
    "samplingCost"         DECIMAL(18, 2) NOT NULL,
    "analyzeConfigCost"    DECIMAL(18, 2) NULL,
    "analyzeCost"          DECIMAL(18, 2) NOT NULL,
    "testRate"             DECIMAL(18, 2) NOT NULL,
    "totalCost"            DECIMAL(18, 2) NOT NULL,
    "orgId"                VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "creator"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "createDate"           TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                          NOT NULL,
    "domainId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "modifier"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                          NOT NULL,
    "modifyDate"           TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                          NOT NULL
);
CREATE TABLE "TB_PRO_DETAILANALYSEDATA"
(
    "id"             VARCHAR(50)     NOT NULL,
    "detailDataId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "testId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "analyseItemId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "examLimitValue" VARCHAR(50) NULL,
    "dimensionId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "dimension"      VARCHAR(50) NULL,
    "testValue"      VARCHAR(100) NULL,
    "testOrignValue" VARCHAR(100) NULL,
    "testValueD"     DECIMAL(20, 10) NOT NULL,
    "testValueDstr"  VARCHAR(100) NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "status"         VARCHAR(50) NULL,
    "dataStatus"     INT          DEFAULT 1
                                     NOT NULL,
    "isDataEnabled"  BIT          DEFAULT 0
                                     NOT NULL,
    "isDeleted"      BIT          DEFAULT 0
                                     NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL
);
CREATE TABLE "TB_PRO_DETAILDATA"
(
    "id"                VARCHAR(50) NOT NULL,
    "projectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "receiveId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "projectCode"       VARCHAR(50) NULL,
    "projectTypeId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "projectName"       VARCHAR(100) NULL,
    "inputTime"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "inceptTime"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "customerId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "customerName"      VARCHAR(100) NULL,
    "sampleCode"        VARCHAR(50) NULL,
    "sampleFolderId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "cycleOrder"        INT          DEFAULT 0
                                    NOT NULL,
    "timesOrder"        INT          DEFAULT 0
                                    NOT NULL,
    "sampleOrder"       INT          DEFAULT 0
                                    NOT NULL,
    "redFolderName"     VARCHAR(100) NULL,
    "samplingTimeBegin" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "samplingTimeEnd"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                    NOT NULL,
    "inspectedEntId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "inspectedEnt"      VARCHAR(100) NULL,
    "sampleTypeId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "watchSpot"         VARCHAR(100) NULL,
    "fixedPointId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "folderCode"        VARCHAR(50) NULL,
    "paramsData"        CLOB NULL,
    "isDeleted"         BIT          DEFAULT 0
                                    NOT NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL
);
CREATE TABLE "TB_PRO_DOCUMENT2LOG"
(
    "documentId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "logId"      VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "id"         VARCHAR(50) NOT NULL
);
CREATE TABLE "TB_PRO_EVALUATIONRECORD"
(
    "id"                VARCHAR(50) NOT NULL,
    "objectId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "objectType"        INT          DEFAULT 0
                                    NOT NULL,
    "folderPlan"        INT          DEFAULT (-1)
                                    NOT NULL,
    "evaluationId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "evaluationLevelId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "testId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "upperLimitValue"   VARCHAR(50) NULL,
    "lowerLimitValue"   VARCHAR(50) NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "upperLimitSymble"  VARCHAR(50) NULL,
    "lowerLimitSymble"  VARCHAR(50) NULL,
    "isDeleted"         BIT          DEFAULT 0
                                    NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "dimensionId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL
);
CREATE TABLE "TB_PRO_EXPLORE"
(
    "id"          VARCHAR(50) NOT NULL,
    "projectId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "principalId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "exploreDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL,
    "remarks"     VARCHAR(2000) NULL,
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL
);
CREATE TABLE "TB_PRO_EXPRESSAGEINFO"
(
    "id"               VARCHAR(50) NOT NULL,
    "projectId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "reportId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "addressee"        VARCHAR(100) NULL,
    "recipients"       VARCHAR(50) NULL,
    "recipientsPhone"  VARCHAR(50) NULL,
    "consigneeAddress" VARCHAR(255) NULL,
    "expressCompany"   VARCHAR(100) NULL,
    "expressNumber"    VARCHAR(50) NULL,
    "sender"           VARCHAR(50) NULL,
    "sendDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "remark"           VARCHAR(200) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_EXPRESSAGEINFO2REPORT"
(
    "id"               VARCHAR(50) NOT NULL,
    "expressageInfoId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "reportId"         VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_FOLDERSIGN"
(
    "id"             VARCHAR(50) NOT NULL,
    "sampleFolderId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "cycleOrder"     INT          DEFAULT (-1)
                                 NOT NULL,
    "signTime"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "signPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "signPersonName" VARCHAR(50) NULL,
    "signLon"        VARCHAR(50) NULL,
    "signLat"        VARCHAR(50) NULL,
    "signTip"        VARCHAR(200) NULL,
    "voiceTip"       VARCHAR(500) NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL
);
CREATE TABLE "TB_PRO_HOMEPENDINGNO"
(
    "id"         VARCHAR(50) NOT NULL,
    "moduleCode" VARCHAR(50) NOT NULL,
    "nums"       INT          DEFAULT 0
                             NOT NULL,
    "userId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORANALYZEMETHOD"
(
    "id"           VARCHAR(50) NOT NULL,
    "operatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "operatorName" VARCHAR(50) NULL,
    "operateTime"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "operateInfo"  VARCHAR(500) NULL,
    "objectId"     VARCHAR(50)  DEFAULT ''
                               NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORCOST"
(
    "id"               VARCHAR(50) NOT NULL,
    "operatorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "operatorName"     VARCHAR(50) NULL,
    "operateTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "operateInfo"      VARCHAR(500) NULL,
    "nextOperatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "nextOperatorName" VARCHAR(50) NULL,
    "logType"          INT          DEFAULT 0
                                   NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '000000000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORDATA"
(
    "id"               VARCHAR(50) NOT NULL,
    "operatorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "operatorName"     VARCHAR(50) NULL,
    "operateTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "operateInfo"      VARCHAR(500) NULL,
    "nextOperatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "nextOperatorName" VARCHAR(50) NULL,
    "logType"          INT          DEFAULT 0
                                   NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORORDERFORM"
(
    "id"               VARCHAR(50) NOT NULL,
    "operatorId"       VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "operatorName"     VARCHAR(50)   DEFAULT ''
        NULL,
    "operateTime"      TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "operateInfo"      VARCHAR(500)  DEFAULT ''
        NULL,
    "nextOperatorId"   VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "nextOperatorName" VARCHAR(50)   DEFAULT ''
        NULL,
    "logType"          INT           DEFAULT 0
                                   NOT NULL,
    "objectId"         VARCHAR(50)   DEFAULT ''
                                   NOT NULL,
    "objectType"       INT           DEFAULT 0
                                   NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) DEFAULT ''
        NULL,
    "remark"           VARCHAR(1000) DEFAULT ''
        NULL,
    "orgId"            VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORPLAN"
(
    "id"               VARCHAR(50) NOT NULL,
    "operatorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "operatorName"     VARCHAR(50) NULL,
    "operateTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "operateInfo"      VARCHAR(500) NULL,
    "nextOperatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "nextOperatorName" VARCHAR(50) NULL,
    "logType"          INT          DEFAULT 0
                                   NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORPROJECT"
(
    "id"               VARCHAR(50) NOT NULL,
    "operatorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "operatorName"     VARCHAR(50) NULL,
    "operateTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "operateInfo"      VARCHAR(500) NULL,
    "nextOperatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "nextOperatorName" VARCHAR(50) NULL,
    "logType"          INT          DEFAULT 0
                                   NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORRECORD"
(
    "id"               VARCHAR(50) NOT NULL,
    "operatorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "operatorName"     VARCHAR(50) NULL,
    "operateTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "operateInfo"      VARCHAR(500) NULL,
    "nextOperatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "nextOperatorName" VARCHAR(50) NULL,
    "logType"          INT          DEFAULT 0
                                   NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORREPORT"
(
    "id"               VARCHAR(50) NOT NULL,
    "operatorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "operatorName"     VARCHAR(50) NULL,
    "operateTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "operateInfo"      VARCHAR(500) NULL,
    "nextOperatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "nextOperatorName" VARCHAR(50) NULL,
    "logType"          INT          DEFAULT 0
                                   NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORSAMPLE"
(
    "id"               VARCHAR(50) NOT NULL,
    "operatorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "operatorName"     VARCHAR(50) NULL,
    "operateTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "operateInfo"      VARCHAR(500) NULL,
    "nextOperatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "nextOperatorName" VARCHAR(50) NULL,
    "logType"          INT          DEFAULT 0
                                   NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_LOGFORWORKSHEET"
(
    "id"               VARCHAR(50) NOT NULL,
    "operatorId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "operatorName"     VARCHAR(50) NULL,
    "operateTime"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "operateInfo"      VARCHAR(500) NULL,
    "nextOperatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "nextOperatorName" VARCHAR(50) NULL,
    "logType"          INT          DEFAULT 0
                                   NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "comment"          CLOB NULL,
    "opinion"          VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_OADEPARTMENTEXPEND"
(
    "id"          VARCHAR(50)    NOT NULL,
    "deptId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "typeId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "amount"      DECIMAL(18, 2) NOT NULL,
    "expendDate"  TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                 NOT NULL,
    "description" VARCHAR(255) NULL,
    "isDeleted"   BIT          DEFAULT 0
                                 NOT NULL,
    "isConfirm"   BIT          DEFAULT 0
                                 NOT NULL,
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL
);
CREATE TABLE "TB_PRO_OAPROJECTEXPEND"
(
    "id"          VARCHAR(50)    NOT NULL,
    "projectId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "typeId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "amount"      DECIMAL(18, 2) NOT NULL,
    "expendDate"  TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                 NOT NULL,
    "description" VARCHAR(255) NULL,
    "isDeleted"   BIT          DEFAULT 0
                                 NOT NULL,
    "orgId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "creator"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "createDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "domainId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifier"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifyDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "isConfirm"   BIT          DEFAULT 0
                                 NOT NULL
);
CREATE TABLE "TB_PRO_OATASK"
(
    "id"                  VARCHAR(50)  NOT NULL,
    "title"               VARCHAR(255) NOT NULL,
    "description"         VARCHAR(255) NULL,
    "sponsor"             VARCHAR(50)  NOT NULL,
    "sponsorId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "sponsorName"         VARCHAR(100) NULL,
    "submitTime"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                       NOT NULL,
    "completeTime"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                       NOT NULL,
    "currentAssignee"     VARCHAR(50)  NOT NULL,
    "currentAssigneeId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "currentAssigneeName" VARCHAR(50) NULL,
    "currentTaskDefKey"   VARCHAR(50) NULL,
    "currentTaskName"     VARCHAR(50) NULL,
    "procTypeCode"        VARCHAR(50)  NOT NULL,
    "procTypeId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "procTypeName"        VARCHAR(50) NULL,
    "procInstId"          VARCHAR(100) DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "status"              VARCHAR(50)  NOT NULL,
    "dataStatus"          INT          DEFAULT 0
                                       NOT NULL,
    "deptId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "deptName"            VARCHAR(100) NULL,
    "isDeleted"           BIT          DEFAULT 0
                                       NOT NULL,
    "orgId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "creator"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "createDate"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                       NOT NULL,
    "domainId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "modifier"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "modifyDate"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                       NOT NULL
);
CREATE TABLE "TB_PRO_OATASKHANDLELOG"
(
    "id"            VARCHAR(50) NOT NULL,
    "taskId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "isAgree"       BIT          DEFAULT 0
                                NOT NULL,
    "comment"       VARCHAR(255) NULL,
    "completeTime"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL,
    "assigneeId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "assignee"      VARCHAR(50) NULL,
    "assigneeName"  VARCHAR(100) NULL,
    "actTaskId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "actTaskDefKey" VARCHAR(50) NULL,
    "actTaskName"   VARCHAR(50) NULL,
    "isFirstStep"   BIT          DEFAULT 0
                                NOT NULL,
    "isDeleted"     BIT          DEFAULT 0
                                NOT NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL,
    "jurorId"       VARCHAR(500) NULL
);
CREATE TABLE "TB_PRO_OATASKRELATION"
(
    "taskId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                           NOT NULL,
    "objectId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                           NOT NULL,
    "id"       VARCHAR(50) NOT NULL
);
CREATE TABLE "TB_PRO_ORDERCONTRACT"
(
    "id"                  VARCHAR(50)  NOT NULL,
    "orderId"             VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "contractCode"        VARCHAR(50)  NOT NULL,
    "contractName"        VARCHAR(255) NOT NULL,
    "contractNature"      VARCHAR(50)  NOT NULL,
    "firstEntId"          VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "firstEntName"        VARCHAR(255) NOT NULL,
    "firstEntPersonName"  VARCHAR(50)  NOT NULL,
    "secondEntName"       VARCHAR(255) NOT NULL,
    "secondEntPersonName" VARCHAR(50)  NOT NULL,
    "secondEntType"       VARCHAR(50) NULL,
    "totalAmount"         DECIMAL(18, 2) NULL,
    "registrant"          VARCHAR(50) NULL,
    "signDate"            TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'
        NULL,
    "excuteStartTime"     TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'
        NULL,
    "excuteEndTime"       TIMESTAMP(0)  DEFAULT '1753-01-01 00:00:00'
        NULL,
    "signPersonId"        TEXT NULL,
    "isHavingSub"         BIT           DEFAULT 0
        NULL,
    "subAmount"           DECIMAL(18, 2) NULL,
    "subOrgs"             VARCHAR(255) NULL,
    "summary"             VARCHAR(1000) NULL,
    "assessRecord"        VARCHAR(1000) NULL,
    "isDeleted"           BIT           DEFAULT 0
                                       NOT NULL,
    "orgId"               VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "domainId"            VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "creator"             VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "createDate"          TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                       NOT NULL,
    "modifier"            VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "modifyDate"          TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                       NOT NULL,
    "contractStatus"      INT           DEFAULT 0
                                       NOT NULL,
    "firstEntPhone"       VARCHAR(50)   DEFAULT ''
                                       NOT NULL,
    "remark"              VARCHAR(1000) DEFAULT ''
        NULL
);
CREATE TABLE "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"
(
    "id"         VARCHAR(50) NOT NULL,
    "personId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
CREATE TABLE "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"
(
    "id"             VARCHAR(50)               NOT NULL,
    "achievementId"  VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                               NOT NULL,
    "contractCode"   VARCHAR(50)    DEFAULT ''
                                               NOT NULL,
    "contractName"   VARCHAR(255)   DEFAULT ''
                                               NOT NULL,
    "firstEntName"   VARCHAR(255)   DEFAULT ''
                                               NOT NULL,
    "totalAmount"    DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "signDate"       TIMESTAMP(0)   DEFAULT '1753-01-01 00:00:00'
        NULL,
    "signPersonId"   TEXT NULL,
    "contractNature" VARCHAR(50)    DEFAULT ''
                                               NOT NULL
);
CREATE TABLE "TB_PRO_ORDERFORM"
(
    "id"                 VARCHAR(50) NOT NULL,
    "orderCode"          VARCHAR(50) NOT NULL,
    "projectTypeId"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "orderName"          VARCHAR(50) NULL,
    "salesPersonId"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "salesPersonName"    VARCHAR(50) NULL,
    "orderDate"          TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "timeLimit"          INT           DEFAULT 30
                                     NOT NULL,
    "enterpriseId"       VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "enterpriseName"     VARCHAR(50) NULL,
    "linkPerson"         VARCHAR(50) NULL,
    "linkPhone"          VARCHAR(50) NULL,
    "address"            VARCHAR(50) NULL,
    "orderStatus"        INT           DEFAULT 0
                                     NOT NULL,
    "pushStatus"         INT           DEFAULT 0
                                     NOT NULL,
    "isDeleted"          BIT           DEFAULT 0
                                     NOT NULL,
    "orgId"              VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "creator"            VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "createDate"         TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "domainId"           VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "modifier"           VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "modifyDate"         TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "flowType"           INT           DEFAULT 0
                                     NOT NULL,
    "firstPersonId"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "secondPersonId"     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "threePersonId"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "registrantId"       VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "registrantName"     VARCHAR(50) NULL,
    "customerOrderNo"    VARCHAR(50) NULL,
    "grantStatus"        INT           DEFAULT (-1)
                                     NOT NULL,
    "signDate"           TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "inspectedEntId"     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "areaId"             VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "inspectedEnt"       VARCHAR(100)  DEFAULT ''
        NULL,
    "inspectedLinkMan"   VARCHAR(50)   DEFAULT ''
        NULL,
    "inspectedLinkPhone" VARCHAR(50)   DEFAULT ''
        NULL,
    "inspectedAddress"   VARCHAR(100)  DEFAULT ''
        NULL,
    "remark"             VARCHAR(1000) DEFAULT ''
        NULL
);
CREATE TABLE "TB_PRO_ORDERQUOTATION"
(
    "id"             VARCHAR(50)               NOT NULL,
    "orderId"        VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                               NOT NULL,
    "testPrice"      DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "testDiscount"   DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "discountPrice"  DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "otherPrice"     DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "preTax"         DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "taxRate"        DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "totalDiscount"  DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "totalPrice"     DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "finalQuotation" DECIMAL(18, 2) DEFAULT 0. NOT NULL,
    "isDeleted"      BIT            DEFAULT 0
                                               NOT NULL,
    "orgId"          VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                               NOT NULL,
    "creator"        VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                               NOT NULL,
    "createDate"     TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()
                                               NOT NULL,
    "domainId"       VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                               NOT NULL,
    "modifier"       VARCHAR(50)    DEFAULT '00000000-0000-0000-0000-000000000000'
                                               NOT NULL,
    "modifyDate"     TIMESTAMP(0)   DEFAULT CURRENT_TIMESTAMP()
                                               NOT NULL
);
CREATE TABLE "TB_PRO_OTHERDETAIL"
(
    "id"          VARCHAR(50)    NOT NULL,
    "orderId"     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "quotationId" VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "typeId"      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "standard"    DECIMAL(18, 2) NOT NULL,
    "unit"        VARCHAR(50) NULL,
    "days"        DECIMAL(18, 2) NOT NULL,
    "count"       DECIMAL(18, 2) NOT NULL,
    "price"       DECIMAL(18, 2) NOT NULL,
    "quotedPrice" DECIMAL(18, 2) NOT NULL,
    "remark"      VARCHAR(1000) NULL,
    "isDeleted"   BIT           DEFAULT 0
                                 NOT NULL,
    "orgId"       VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "creator"     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "createDate"  TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "domainId"    VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifier"    VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifyDate"  TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "formula"     VARCHAR(1000) DEFAULT ''
        NULL
);
CREATE TABLE "TB_PRO_OUTSOURCEDATA"
(
    "id"                  VARCHAR(50) NOT NULL,
    "analyseDataId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "analyzeMethodName"   VARCHAR(255) NULL,
    "analyzeMethodId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "testValue"           VARCHAR(50) NULL,
    "dimensionName"       VARCHAR(50) NULL,
    "dimensionId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "state"               INT          DEFAULT 0
                                      NOT NULL,
    "orgId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "domainId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "creator"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "createDate"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                      NOT NULL,
    "modifier"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                      NOT NULL,
    "modifyDate"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                      NOT NULL,
    "analyzeTime"         TIMESTAMP(0) NULL,
    "analyzeEndTime"      TIMESTAMP(0) NULL,
    "subcontractor"       VARCHAR(500) NULL,
    "cmaCode"             VARCHAR(200) NULL,
    "outSourceReportCode" VARCHAR(200) NULL,
    "isDeleted"           BIT          DEFAULT 0
                                      NOT NULL,
    "detectionLimit"      VARCHAR(50) NULL
);
CREATE TABLE "TB_PRO_PARAMSDATA"
(
    "id"             VARCHAR(50) NOT NULL,
    "objectId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "objectType"     INT          DEFAULT 0
                                 NOT NULL,
    "paramsConfigId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "paramsName"     VARCHAR(100) NULL,
    "paramsValue"    VARCHAR(2000) NULL,
    "dimension"      VARCHAR(50) NULL,
    "dimensionId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "orderNum"       INT          DEFAULT 0
                                 NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "groupId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "isDeleted"      BIT          DEFAULT 0
                                 NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL
);
CREATE TABLE "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"
(
    "id"                   VARCHAR(50) NOT NULL,
    "receiveId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "recordCode"           VARCHAR(20) NULL,
    "testId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "recorderId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "sendTime"             TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                       NOT NULL,
    "samplingTime"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                       NOT NULL,
    "sampleTypeId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "redAnalyzeItemName"   VARCHAR(100) DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "redAnalyzeMethodName" VARCHAR(255) DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "analyseItemId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "analyzeMethodId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "sample"               INT          DEFAULT 0
                                       NOT NULL,
    "localeGap"            INT          DEFAULT 0
                                       NOT NULL,
    "parallel"             INT          DEFAULT 0
                                       NOT NULL,
    "valid"                INT          DEFAULT 0
                                       NOT NULL,
    "orgId"                VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL
);
CREATE TABLE "TB_PRO_PERFORMANCESTATISTICFORREPORTDATA"
(
    "id"            VARCHAR(50) NOT NULL,
    "projectId"     VARCHAR(100) DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "reportTime"    TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                NOT NULL,
    "reportMakerId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "report"        INT          DEFAULT 0
                                NOT NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL
);
CREATE TABLE "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"
(
    "id"               VARCHAR(50) NOT NULL,
    "receiveId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "projectId"        VARCHAR(100) DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "recordCode"       VARCHAR(20) NULL,
    "samplingPersonId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "sendTime"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                   NOT NULL,
    "samplingTime"     TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                   NOT NULL,
    "senderName"       VARCHAR(100) NULL,
    "sampleTypeId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "sample"           INT          DEFAULT 0
                                   NOT NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"
(
    "id"                   VARCHAR(50) NOT NULL,
    "workSheetFolderId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "workSheetCode"        VARCHAR(20) NULL,
    "testId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "analystId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "analyzeTime"          TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                       NOT NULL,
    "sampleTypeId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "redAnalyzeItemName"   VARCHAR(100) NULL,
    "redAnalyzeMethodName" VARCHAR(255) NULL,
    "analyseItemId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "analyzeMethodId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL,
    "sample"               INT          DEFAULT 0
                                       NOT NULL,
    "localeGap"            INT          DEFAULT 0
                                       NOT NULL,
    "interiorGap"          INT          DEFAULT 0
                                       NOT NULL,
    "parallel"             INT          DEFAULT 0
                                       NOT NULL,
    "addition"             INT          DEFAULT 0
                                       NOT NULL,
    "curveItem"            INT          DEFAULT 0
                                       NOT NULL,
    "curveEntries"         INT          DEFAULT 0
                                       NOT NULL,
    "point"                INT          DEFAULT 0
                                       NOT NULL,
    "qcSample"             INT          DEFAULT 0
                                       NOT NULL,
    "valid"                INT          DEFAULT 0
                                       NOT NULL,
    "orgId"                VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                       NOT NULL
);
CREATE TABLE "TB_PRO_PROJECT"
(
    "id"                 VARCHAR(50) NOT NULL,
    "parentId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "projectCode"        VARCHAR(50) NULL,
    "projectTypeId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "projectName"        VARCHAR(100) NULL,
    "status"             VARCHAR(50) NOT NULL,
    "samplingStatus"     INT          DEFAULT 1
                                     NOT NULL,
    "reportStatus"       INT          DEFAULT 0
                                     NOT NULL,
    "inputTime"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "inceptPersonId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "sendSamplePerson"   VARCHAR(50) NULL,
    "inceptTime"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "lastModifyTime"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "expectedCharge"     DECIMAL(18, 2) NULL,
    "actualCharges"      DECIMAL(18, 2) NULL,
    "isStress"           BIT          DEFAULT 0
                                     NOT NULL,
    "grade"              INT          DEFAULT 0
                                     NOT NULL,
    "customerId"         VARCHAR(500) DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "customerName"       VARCHAR(1000) NULL,
    "inspectedEntId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "inspectedEnt"       VARCHAR(100) NULL,
    "inspectedLinkMan"   VARCHAR(50) NULL,
    "inspectedLinkPhone" VARCHAR(50) NULL,
    "inspectedAddress"   VARCHAR(100) NULL,
    "customerOwner"      VARCHAR(50) NULL,
    "customerAddress"    VARCHAR(100) NULL,
    "linkMan"            VARCHAR(50) NULL,
    "linkPhone"          VARCHAR(50) NULL,
    "linkEmail"          VARCHAR(50) NULL,
    "linkFax"            VARCHAR(50) NULL,
    "zipCode"            VARCHAR(10) NULL,
    "isDeleted"          BIT          DEFAULT 0
                                     NOT NULL,
    "monitorPurp"        VARCHAR(255) NULL,
    "monitorMethods"     VARCHAR(255) NULL,
    "customerRequired"   VARCHAR(1000) NULL,
    "sampleType"         VARCHAR(1000) NULL,
    "postMethod"         INT          DEFAULT 0
                                     NOT NULL,
    "remark"             VARCHAR(1000) NULL,
    "qcInfo"             VARCHAR(255) NULL,
    "controlInfo"        VARCHAR(255) NULL,
    "projectAddress"     VARCHAR(100) NULL,
    "docNumber"          VARCHAR(50) NULL,
    "compDate"           VARCHAR(10) NULL,
    "invAmount"          VARCHAR(20) NULL,
    "analyzeMethod"      INT          DEFAULT 0
                                     NOT NULL,
    "reportMethod"       VARCHAR(255) NULL,
    "saveCondition"      VARCHAR(255) NULL,
    "saveDate"           VARCHAR(255) NULL,
    "reportNum"          VARCHAR(255) NULL,
    "isOnline"           INT          DEFAULT 0
                                     NOT NULL,
    "pushStatus"         INT          DEFAULT 0
                                     NOT NULL,
    "qrCodeUrl"          VARCHAR(500) NULL,
    "sampleQuantity"     VARCHAR(10) NULL,
    "isQualified"        BIT          DEFAULT 0
                                     NOT NULL,
    "sampleDescription"  VARCHAR(255) NULL,
    "sampleNameCustomer" VARCHAR(100) NULL,
    "samKind"            VARCHAR(50) NULL,
    "tradeAreaCode"      VARCHAR(50) NULL,
    "batchCode"          VARCHAR(20) NULL,
    "testCode"           VARCHAR(20) NULL,
    "prodCompany"        VARCHAR(100) NULL,
    "compAddress"        VARCHAR(100) NULL,
    "extendInt1"         INT          DEFAULT (-1)
                                     NOT NULL,
    "extendInt2"         INT          DEFAULT (-1)
                                     NOT NULL,
    "extendInt3"         INT          DEFAULT (-1)
                                     NOT NULL,
    "extendStr1"         VARCHAR(255) NULL,
    "extendStr2"         VARCHAR(255) NULL,
    "extendStr3"         VARCHAR(255) NULL,
    "extendGuid1"        VARCHAR(50) NULL,
    "extendGuid2"        VARCHAR(50) NULL,
    "extendGuid3"        VARCHAR(50) NULL,
    "extendDate1"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "extendDate2"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "extendDate3"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "json"               CLOB NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "orderId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "isMultiEnterprise"  BIT          DEFAULT 0
                                     NOT NULL
);
CREATE TABLE "TB_PRO_PROJECT2CONTRACT"
(
    "id"         VARCHAR(50) NOT NULL,
    "projectId"  VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "contractId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL
);
CREATE TABLE "TB_PRO_PROJECT2CUSTOMER"
(
    "id"           VARCHAR(50) NOT NULL,
    "projectId"    VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "customerId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "customerName" VARCHAR(100) NULL
);
ALTER TABLE "TB_PRO_ANALYSEACHIEVEMENT2PERSON"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_ANALYSEACHIEVEMENTDETAILS"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_ANALYSEBIOLOGYDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_ANALYSEDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_ANALYSEORIGINALRECORD"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_AUTOTASKPLAN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_BUSINESSSERIALNUMBER"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_COMMENT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_COMMENTCOMPLIMENTDETAIL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_COSTINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_COSTINFODETAIL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_DETAILANALYSEDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_DETAILDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_DOCUMENT2LOG"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_EVALUATIONRECORD"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_EXPLORE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_EXPRESSAGEINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_EXPRESSAGEINFO2REPORT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_FOLDERSIGN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_HOMEPENDINGNO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORANALYZEMETHOD"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORCOST"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORORDERFORM"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORPLAN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORPROJECT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORRECORD"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORREPORT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORSAMPLE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_LOGFORWORKSHEET"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_OADEPARTMENTEXPEND"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_OAPROJECTEXPEND"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_OATASK"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_OATASKHANDLELOG"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_OATASKRELATION"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_ORDERCONTRACT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_ORDERFORM"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_ORDERQUOTATION"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_OTHERDETAIL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_OUTSOURCEDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PARAMSDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PERFORMANCESTATISTICFORREPORTDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PROJECT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PROJECT2CONTRACT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_PROJECT2CUSTOMER"
    ADD CONSTRAINT PRIMARY KEY ("id");

CREATE INDEX "IX_TB_PRO_AnalyseData"
    ON "TB_PRO_ANALYSEDATA" ("sampleId", "analyzeTime", "isDeleted");

CREATE INDEX "IX_TB_PRO_AnalyseData2"
    ON "TB_PRO_ANALYSEDATA" ("orgId", "qcId");

CREATE INDEX "IX_TB_PRO_AnalyseData3"
    ON "TB_PRO_ANALYSEDATA" ("orgId", "sampleId", "isDeleted", "isOutsourcing", "isCompleteField", "workSheetFolderId");

CREATE INDEX "IX_TB_PRO_AnalyseData4"
    ON "TB_PRO_ANALYSEDATA" ("sampleId", "orgId", "isDeleted");

CREATE INDEX "IX_TB_PRO_AnalyseData5"
    ON "TB_PRO_ANALYSEDATA" ("workSheetFolderId", "sampleId", "orgId", "analystId", "isDeleted");

CREATE INDEX "IX_TB_PRO_AnalyseData6"
    ON "TB_PRO_ANALYSEDATA" ("sampleId", "orgId", "testId", "analystId", "workSheetId", "isOutsourcing",
                             "isCompleteField", "dataStatus", "isDeleted");

CREATE INDEX "IX_TB_PRO_AnalyseData7"
    ON "TB_PRO_ANALYSEDATA" ("sampleId", "orgId", "isDeleted", "analyzeMethodId", "redAnalyzeItemName",
                             "redAnalyzeMethodName");

CREATE INDEX "IX_TB_PRO_AnalyseData8"
    ON "TB_PRO_ANALYSEDATA" ("sampleId", "orgId", "isDeleted", "isOutsourcing", "isSamplingOut");

CREATE INDEX "IX_TB_PRO_AnalyseData9"
    ON "TB_PRO_ANALYSEDATA" ("orgId", "isDeleted", "sampleId", "testId", "qcGrade", "isCompleteField", "analyzeTime");

CREATE INDEX "UIX_TB_PRO_AnalyseOriginalRecord"
    ON "TB_PRO_ANALYSEORIGINALRECORD" ("analyseDataId", "isDeleted");

CREATE INDEX "IX_TB_PRO_DetailAnalyseData"
    ON "TB_PRO_DETAILANALYSEDATA" ("detailDataId", "analyseItemId", "isDataEnabled", "isDeleted", "orgId");

CREATE INDEX "UIX_TB_PRO_EvaluationRecord"
    ON "TB_PRO_EVALUATIONRECORD" ("objectId", "isDeleted");

CREATE INDEX "IX_TB_PRO_LogForAnalyzeMethod"
    ON "TB_PRO_LOGFORANALYZEMETHOD" ("objectId", "orgId");

CREATE INDEX "IX_TB_PRO_LogForCost"
    ON "TB_PRO_LOGFORCOST" ("objectId", "orgId");

CREATE INDEX "IX_TB_PRO_LogForData"
    ON "TB_PRO_LOGFORDATA" ("objectId", "orgId");

CREATE INDEX "IX_TB_PRO_LogForOrderForm"
    ON "TB_PRO_LOGFORORDERFORM" ("objectId", "orgId");

CREATE INDEX "IX_TB_PRO_LogForPlan"
    ON "TB_PRO_LOGFORPLAN" ("objectId", "orgId");

CREATE INDEX "IX_TB_PRO_LogForProject"
    ON "TB_PRO_LOGFORPROJECT" ("objectId", "orgId");

CREATE INDEX "IX_TB_PRO_LogForRecord"
    ON "TB_PRO_LOGFORRECORD" ("objectId", "orgId");

CREATE INDEX "IX_TB_PRO_LogForReport"
    ON "TB_PRO_LOGFORREPORT" ("objectId", "orgId");

CREATE INDEX "IX_TB_PRO_LogForSample"
    ON "TB_PRO_LOGFORSAMPLE" ("objectId", "orgId");

CREATE INDEX "IX_TB_PRO_LogForWorkSheet"
    ON "TB_PRO_LOGFORWORKSHEET" ("objectId", "orgId");

CREATE INDEX "UIX_TB_PRO_ParamsData"
    ON "TB_PRO_PARAMSDATA" ("objectId", "paramsConfigId", "groupId", "objectType", "orgId", "isDeleted");

CREATE INDEX "IX_TB_PRO_PerformanceStatisticForWorkSheetData"
    ON "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA" ("orgId", "analyzeTime", "analystId");

CREATE INDEX "IX_TB_PRO_Project"
    ON "TB_PRO_PROJECT" ("orgId", "inceptTime", "projectTypeId", "inceptPersonId", "status", "inputTime");

CREATE INDEX "IX_TB_PRO_ProjectPlan"
    ON "TB_PRO_PROJECTPLAN" ("orgId", "projectId", "leaderId", "reportMakerId");

CREATE INDEX "tb_pro_projectplan_projectId_IDX"
    ON "TB_PRO_PROJECTPLAN" ("projectId");

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENT2PERSON"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENT2PERSON"."personId" IS '人员id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENT2PERSON"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENT2PERSON"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENT2PERSON"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENT2PERSON"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENT2PERSON"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENT2PERSON"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."achievementId" IS '绩效id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."sampleId" IS '样品id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."sampleCode" IS '样品编号';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."analyzeItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."analyzeMethodName" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."countryStandard" IS '标准编号';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."analystId" IS '分析人员id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."analystName" IS '分析人员名称';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."analyzeTime" IS '数据分析时间';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."totalAmount" IS '产值';

COMMENT
ON COLUMN "TB_PRO_ANALYSEACHIEVEMENTDETAILS"."status" IS '状态';

COMMENT
ON TABLE "TB_PRO_ANALYSEBIOLOGYDATA" IS '生物子数据';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."parentId" IS '父级Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."analyseDataId" IS '数据Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."taxonomyId" IS '生物Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."testValue" IS '数据结果';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."testValueD" IS '数据结果（用于计算）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."dimension" IS '计量单位';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."dimensionId" IS '计量单位Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."volume" IS '体积';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."countValue" IS '计数值（统计量）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."times" IS '次数';

COMMENT
ON COLUMN "TB_PRO_ANALYSEBIOLOGYDATA"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_ANALYSEDATA" IS '分析数据';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."workSheetId" IS '工作单子Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."workSheetFolderId" IS '工作单Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."subId" IS '分包单位id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."sampleId" IS '样品Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."testId" IS '测试id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."redAnalyzeItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."redAnalyzeMethodName" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."analyseItemId" IS '分析项目id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."analyzeMethodId" IS '分析方法id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."isQm" IS '是否是质控任务';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."receiveSubId" IS '领样单id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."mostSignificance" IS '有效位数';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."mostDecimal" IS '小数位数';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."examLimitValue" IS '检出限';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."dimensionId" IS '单位Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."dimension" IS '单位（字符串）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."testValue" IS '出证结果';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."testOrignValue" IS '检测结果（未修约）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."testValueD" IS '参与运算的值（检测结果的数值）（已修约）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."testValueDstr" IS '检测结果（已修约）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."status" IS '数据状态（字符串,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."dataStatus" IS '数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."dataChangeStatus" IS '数据变更状态（枚举EnumDataChangeStatus： 0.未变更 1.新增 2.修改 3.删除）（针对已经编制报告的数据修改状态-样品数据增删改）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."analystId" IS '分析人员Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."analystName" IS '分析人员';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."analyzeTime" IS '数据分析时间';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."dataInputTime" IS '数据录入时间';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."isDataEnabled" IS '有效性（数据确认，是否出具在报告上）';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."isCompleteField" IS '是否在现场完成(根据实际情况填写)';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."isOutsourcing" IS '是否分包';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."requireDeadLine" IS '要求完成时间';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."grade" IS '等级(EnumProjectGrade：0.一般 1.紧急 2.特急)';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."deptId" IS '所属科室ID';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."isQualified" IS '国检_是否合格(数据复验后是否仍超出限量值)';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."repeatTimes" IS '国检_复验次数';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."testTaskId" IS '水利_测试任务id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."isPostCert" IS '是有上岗证';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."certEffectiveTime" IS '上岗证有效期';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."sampleTypeId" IS '样品检测类型id-冗余';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."gatherCode" IS '采集编号';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."qcInfo" IS '质控信息';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."seriesValue" IS '串联中间结果';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."finishTime" IS '分析完成日期';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."isSamplingOut" IS '是否采样分包';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."sampleReceiveDate" IS '领样日期';

COMMENT
ON COLUMN "TB_PRO_ANALYSEDATA"."isSci" IS '是否科学计数法';

COMMENT
ON TABLE "TB_PRO_ANALYSEORIGINALRECORD" IS '分析数据公式数据';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."analyseDataId" IS '数据ID';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."json" IS '原始数据字符串';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."testFormulaId" IS '公式Id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."testFormula" IS '公式';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_ANALYSEORIGINALRECORD"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."taskName" IS '计划名称';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."taskCode" IS '计划编码';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."dealCycle" IS '执行周期(EnumDealCycle)';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."dealNum" IS '执行数量';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."dealDate" IS '每月执行日期';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."projectCodeRule" IS '项目编号 1-自动生成 2-手动生成(EnumCodeGenerateRule)';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."inputPersonId" IS '登记人';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."leaderId" IS '负责人';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."isStress" IS '是否着重关注';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."grade" IS '项目登记(EnumProjectGrade：0.一般 1.紧急 2.特急)';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."month" IS '应用月份(","隔开)';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_AUTOTASKPLAN"."projectTypeId" IS '项目类型id';

COMMENT
ON TABLE "TB_PRO_BUSINESSSERIALNUMBER" IS '业务流水号表';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."businessType" IS '业务类型，枚举管理，参考枚举 EnumLogObjectType';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."businessId" IS '业务id';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."businessNumber" IS '业务编号';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."serialNumberType" IS '流水号类型';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."para0" IS '流水号参数0';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."para1" IS '流水号参数1';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."para2" IS '流水号参数2';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."para3" IS '流水号参数3';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."isDeleted" IS '假删 标识';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."orgId" IS '所属机构ID';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."domainId" IS '所属实验室ID';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."modifier" IS '更新人';

COMMENT
ON COLUMN "TB_PRO_BUSINESSSERIALNUMBER"."modifyDate" IS '更新时间';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."parentId" IS '冗余用于回复';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."objectId" IS '评论关联id（项目id、我的审批、工作单、报告id）';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."objectType" IS '对象类型（枚举EnumCommentObjectType：1.项目 2.我的审批 3.工作单 4.报告）';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."commentPersonId" IS '评论人id（Guid）';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."commentPersonName" IS '评论人名称';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."commentTime" IS '评论时间';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."comment" IS '评论内容';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."commentType" IS '评论类型（枚举EnumCommentType：1、留言）';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_COMMENT"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."commentId" IS '评论Id';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."complimentorId" IS '操作人Id';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."complimentorName" IS '操作人的姓名';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."complimentNumber" IS '操作次数（考虑一个人多次点赞）';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."complimentDate" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."complimentType" IS '评论类型（0：赞 1：踩）';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."option" IS '内容';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_COMMENTCOMPLIMENTDETAIL"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_COSTINFO" IS '费用信息';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."samplingCost" IS '采样总费';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."analyzeCost" IS '分析总费';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."reportCost" IS '报告费';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."climbCost" IS '登高费';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."expertCost" IS '专家费';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."otherCost" IS '其它费';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."laborNum" IS '人数';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."laborDay" IS '人天';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."laborUnit" IS '元/人天';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."laborCost" IS '人工费';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."carNum" IS '车辆数';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."carDay" IS '车辆天';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."carUnit" IS '元/辆天';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."carCost" IS '车辆费';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."expectedTotalCost" IS '总计';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."reportRate" IS '报告折扣率';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."offerRate" IS '折扣率';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."taxManageCost" IS '税收管理费';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."acturalTotalCost" IS '报价';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."schemeChangeStatus" IS '方案变更状态（ 枚举EnumSchemeChangeStatus：0.未变更 1.已变更）';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."status" IS '状态（新建、审核中、审批中、审核不通过、审批不通过、已完成）';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_COSTINFO"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_COSTINFODETAIL" IS '费用信息明细';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."id" IS '主键Id';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."costInfoId" IS '费用id';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."redAnalyzeItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."redAnalyzeMethodName" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."redCountryStandard" IS '标准编号';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."sampleNum" IS '样品个数';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."samplingConfigCost" IS '配置上的采样费（当时的配置）';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."samplingCost" IS '采样费';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."analyzeConfigCost" IS '配置上的分析费（当时的配置）';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."analyzeCost" IS '分析费';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."testRate" IS '折扣率';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."totalCost" IS '合计';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_COSTINFODETAIL"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_DETAILANALYSEDATA" IS '详细的分析数据';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."detailDataId" IS '详细数据id';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."testId" IS '测试id';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."analyseItemId" IS '分析项目id';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."examLimitValue" IS '检出限';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."dimensionId" IS '单位Id';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."dimension" IS '单位（字符串）';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."testValue" IS '出证结果';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."testOrignValue" IS '检测结果（未修约）';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."testValueD" IS '参与运算的值（检测结果的数值）（已修约）';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."testValueDstr" IS '检测结果（已修约）';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."status" IS '数据状态（字符串,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."dataStatus" IS '数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."isDataEnabled" IS '有效性（数据确认，是否出具在报告上）';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_DETAILANALYSEDATA"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_DETAILDATA" IS '详细数据';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."id" IS '样品id';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."receiveId" IS '送样记录id';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."projectCode" IS '流水编号';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."projectTypeId" IS '项目类型id（外键）';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."projectName" IS '项目名称';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."inputTime" IS '项目登记时间(登记时间不能改)';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."inceptTime" IS '委托时间';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."customerId" IS '委托单位id';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."customerName" IS '委托单位';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."sampleCode" IS '样品编号';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."sampleFolderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."cycleOrder" IS '采样周期序数';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."timesOrder" IS '每周期次数序数';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."sampleOrder" IS '每次样品序数';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."redFolderName" IS '冗余-点位';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."samplingTimeBegin" IS '采样开始时间';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."samplingTimeEnd" IS '采样结束时间';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."inspectedEntId" IS '受检单位Id';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."inspectedEnt" IS '受检单位';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."watchSpot" IS '点位名称';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."fixedPointId" IS '断面id（断面扩展id）';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."folderCode" IS '点位号';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."paramsData" IS '参数数据';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_DETAILDATA"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_DOCUMENT2LOG" IS '文档相关日志';

COMMENT
ON COLUMN "TB_PRO_DOCUMENT2LOG"."documentId" IS '文档id';

COMMENT
ON COLUMN "TB_PRO_DOCUMENT2LOG"."logId" IS '日志id';

COMMENT
ON COLUMN "TB_PRO_DOCUMENT2LOG"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."objectType" IS '类型（枚举EnumEvaluationType：1、项目 2、点位 3、样品  4、数据）';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."folderPlan" IS '点位计划（枚举EnumEvaluationPlan：1、实际 2、计划）';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."evaluationId" IS '评价标准id';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."evaluationLevelId" IS '评价等级id';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."upperLimitValue" IS '上限';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."lowerLimitValue" IS '下限';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."upperLimitSymble" IS '上限运算符';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."lowerLimitSymble" IS '下限运算符';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_EVALUATIONRECORD"."dimensionId" IS '量纲id';

COMMENT
ON TABLE "TB_PRO_EXPLORE" IS '踏勘表';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."principalId" IS '踏勘负责人id';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."exploreDate" IS '踏勘日期';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."remarks" IS '踏勘说明';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_EXPLORE"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."reportId" IS '报告id（冗余，可能会精确到报告）';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."addressee" IS '收件单位';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."recipients" IS '收件人';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."recipientsPhone" IS '收件人电话';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."consigneeAddress" IS '收件地址';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."expressCompany" IS '快递公司';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."expressNumber" IS '快递单号';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."sender" IS '寄件人';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."sendDate" IS '寄件日期';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_EXPRESSAGEINFO2REPORT" IS '快递报告关联';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO2REPORT"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO2REPORT"."expressageInfoId" IS '快递id';

COMMENT
ON COLUMN "TB_PRO_EXPRESSAGEINFO2REPORT"."reportId" IS '报告id';

COMMENT
ON TABLE "TB_PRO_FOLDERSIGN" IS '点位签到表';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."sampleFolderId" IS '点位id';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."cycleOrder" IS '周期';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."signTime" IS '签到时间';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."signPersonId" IS '签到人id';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."signPersonName" IS '签到人名称';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."signLon" IS '签到经度';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."signLat" IS '签到纬度';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."signTip" IS '签到说明';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."voiceTip" IS '语音说明';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_FOLDERSIGN"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_HOMEPENDINGNO"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_HOMEPENDINGNO"."moduleCode" IS '模块编码';

COMMENT
ON COLUMN "TB_PRO_HOMEPENDINGNO"."nums" IS '待办数量';

COMMENT
ON COLUMN "TB_PRO_HOMEPENDINGNO"."userId" IS '用户id';

COMMENT
ON COLUMN "TB_PRO_HOMEPENDINGNO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_HOMEPENDINGNO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_HOMEPENDINGNO"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_HOMEPENDINGNO"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_LOGFORANALYZEMETHOD" IS '分析方法状态日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORANALYZEMETHOD"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORANALYZEMETHOD"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORANALYZEMETHOD"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORANALYZEMETHOD"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORANALYZEMETHOD"."operateInfo" IS '操作信息';

COMMENT
ON COLUMN "TB_PRO_LOGFORANALYZEMETHOD"."objectId" IS '分析方法id';

COMMENT
ON COLUMN "TB_PRO_LOGFORANALYZEMETHOD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_LOGFORANALYZEMETHOD"."domainId" IS '所属实验室id';

COMMENT
ON TABLE "TB_PRO_LOGFORCOST" IS '项目日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."logType" IS '日志类型（如项目的方案、合同，样品的信息、检测项目）';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."objectType" IS '对象类型（工作单、项目、数据等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."comment" IS '说明';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."opinion" IS '意见（评审意见等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORCOST"."remark" IS '备注';

COMMENT
ON TABLE "TB_PRO_LOGFORDATA" IS '数据日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."logType" IS '日志类型（如项目的方案、合同，样品的信息、检测项目）';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."objectType" IS '对象类型（工作单、项目、数据等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."comment" IS '说明';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."opinion" IS '意见（评审意见等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_LOGFORDATA"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_LOGFORORDERFORM" IS '订单日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."logType" IS '日志类型（如项目的方案、合同，样品的信息、检测项目）';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."objectType" IS '对象类型（工作单、项目、数据等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."comment" IS '说明';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."opinion" IS '意见（评审意见等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_LOGFORORDERFORM"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_LOGFORPLAN" IS '方案日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."logType" IS '日志类型（如项目的方案、合同，样品的信息、检测项目）';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."objectType" IS '对象类型（工作单、项目、数据等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."comment" IS '说明';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."opinion" IS '意见（评审意见等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_LOGFORPLAN"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_LOGFORPROJECT" IS '项目日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."logType" IS '日志类型（如项目的方案、合同，样品的信息、检测项目）';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."objectType" IS '对象类型（工作单、项目、数据等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."comment" IS '说明';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."opinion" IS '意见（评审意见等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_LOGFORPROJECT"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_LOGFORRECORD" IS '送样单、领样单日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."logType" IS '日志类型（如项目的方案、合同，样品的信息、检测项目）';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."objectType" IS '对象类型（工作单、项目、数据等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."comment" IS '说明';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."opinion" IS '意见（评审意见等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_LOGFORRECORD"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_LOGFORREPORT" IS '报告日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."logType" IS '日志类型（如项目的方案、合同，样品的信息、检测项目）';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."objectType" IS '对象类型（工作单、项目、数据等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."comment" IS '说明';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."opinion" IS '意见（评审意见等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_LOGFORREPORT"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_LOGFORSAMPLE" IS '样品日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."logType" IS '日志类型（如项目的方案、合同，样品的信息、检测项目）';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."objectType" IS '对象类型（工作单、项目、数据等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."comment" IS '说明';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."opinion" IS '意见（评审意见等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_LOGFORSAMPLE"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_LOGFORWORKSHEET" IS '工作单单日志';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."operatorId" IS '操作者Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."operatorName" IS '操作者名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."operateTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."nextOperatorId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."nextOperatorName" IS '下一步操作人名字';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."logType" IS '日志类型（如项目的方案、合同，样品的信息、检测项目）';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."objectId" IS '对象id';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."objectType" IS '对象类型（工作单、项目、数据等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."comment" IS '说明';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."opinion" IS '意见（评审意见等）';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_LOGFORWORKSHEET"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_OADEPARTMENTEXPEND" IS '部门支出';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."deptId" IS '部门ID';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."typeId" IS '种类(常量编码: OA_ExpenditureType )';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."amount" IS '金额';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."expendDate" IS '支出日期';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."description" IS '说明';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."isConfirm" IS '是否确认';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_OADEPARTMENTEXPEND"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_OAPROJECTEXPEND" IS '项目支出';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."projectId" IS '项目标识';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."typeId" IS '种类(常量编码:OA_ExpenditureType )';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."amount" IS '金额';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."expendDate" IS '支出日期';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."description" IS '申请说明';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_OAPROJECTEXPEND"."isConfirm" IS '是否确认';

COMMENT
ON TABLE "TB_PRO_OATASK" IS '审批任务信息';

COMMENT
ON COLUMN "TB_PRO_OATASK"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_OATASK"."title" IS '标题';

COMMENT
ON COLUMN "TB_PRO_OATASK"."description" IS '说明';

COMMENT
ON COLUMN "TB_PRO_OATASK"."sponsor" IS '发起人账号';

COMMENT
ON COLUMN "TB_PRO_OATASK"."sponsorId" IS '发起人标识';

COMMENT
ON COLUMN "TB_PRO_OATASK"."sponsorName" IS '发起人名称';

COMMENT
ON COLUMN "TB_PRO_OATASK"."submitTime" IS '提交时间';

COMMENT
ON COLUMN "TB_PRO_OATASK"."completeTime" IS '完成时间';

COMMENT
ON COLUMN "TB_PRO_OATASK"."currentAssignee" IS '当前办理人账号';

COMMENT
ON COLUMN "TB_PRO_OATASK"."currentAssigneeId" IS '当前办理人标识';

COMMENT
ON COLUMN "TB_PRO_OATASK"."currentAssigneeName" IS '当前办理人名称';

COMMENT
ON COLUMN "TB_PRO_OATASK"."currentTaskDefKey" IS '当前环节key';

COMMENT
ON COLUMN "TB_PRO_OATASK"."currentTaskName" IS '当前环节名称';

COMMENT
ON COLUMN "TB_PRO_OATASK"."procTypeCode" IS '工作流类型编码(枚举EnumProcTypeCode：contract: 合同审批、projectExpend: 项目支出、departmentExpend: 部门支出、instrumentPurchase: 仪器采购、instrumentRepair: 仪器维修、instrumentScrap: 仪器报废、consumable：领料、consumablePurchase: 消耗品采购、fileControl:文件受控、fileRevision: 文件修订、fileAbolish: 文件废止)';

COMMENT
ON COLUMN "TB_PRO_OATASK"."procTypeId" IS '工作流类型标识(常量编码: OA_Processtype)';

COMMENT
ON COLUMN "TB_PRO_OATASK"."procTypeName" IS '工作流类型名称';

COMMENT
ON COLUMN "TB_PRO_OATASK"."procInstId" IS '工作流实例Id';

COMMENT
ON COLUMN "TB_PRO_OATASK"."status" IS '状态名称(字符串，枚举EnumOATaskStatus：审批通过、审批中、审批拒绝)';

COMMENT
ON COLUMN "TB_PRO_OATASK"."dataStatus" IS '状态（数值，枚举EnumOATaskStatus：0.审批中 1.审批通过 2.审批拒绝)';

COMMENT
ON COLUMN "TB_PRO_OATASK"."deptId" IS '部门ID';

COMMENT
ON COLUMN "TB_PRO_OATASK"."deptName" IS '部门名称';

COMMENT
ON COLUMN "TB_PRO_OATASK"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_PRO_OATASK"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_OATASK"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_OATASK"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_OATASK"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_OATASK"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_OATASK"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_OATASKHANDLELOG" IS '审批任务流程日志';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."taskId" IS '任务标识';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."isAgree" IS '是否同意';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."comment" IS '批注';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."completeTime" IS '办理时间';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."assigneeId" IS '办理人id';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."assignee" IS '办理人账号';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."assigneeName" IS '办理人名称';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."actTaskId" IS '工作流任务id';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."actTaskDefKey" IS '工作流任务节点key';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."actTaskName" IS '工作流任务名称';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."isFirstStep" IS '是否为发起申请步骤';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_OATASKHANDLELOG"."jurorId" IS '陪审人id';

COMMENT
ON COLUMN "TB_PRO_OATASKRELATION"."taskId" IS '审批任务id';

COMMENT
ON COLUMN "TB_PRO_OATASKRELATION"."objectId" IS '关联id';

COMMENT
ON COLUMN "TB_PRO_OATASKRELATION"."id" IS '主键';

COMMENT
ON TABLE "TB_PRO_ORDERCONTRACT" IS '合同信息';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."orderId" IS '订单id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."contractCode" IS '合同编号';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."contractName" IS '合同名称';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."contractNature" IS '合同性质';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."firstEntId" IS '甲方id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."firstEntName" IS '甲方名称';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."firstEntPersonName" IS '甲方联系人';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."secondEntName" IS '乙方名称';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."secondEntPersonName" IS '乙方联系人';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."secondEntType" IS '乙方企业类型';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."totalAmount" IS '合同金额';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."registrant" IS '登记人';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."signDate" IS '签订日期';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."excuteStartTime" IS '合同履行开始时间';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."excuteEndTime" IS '合同履行结束时间';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."signPersonId" IS '签订人员id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."isHavingSub" IS '是否有分包项';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."subAmount" IS '分包金额';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."subOrgs" IS '分包机构';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."summary" IS '合同概述';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."assessRecord" IS '评审记录';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."isDeleted" IS '假删';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."domainId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."contractStatus" IS '合同状态（签订,未签订）';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."firstEntPhone" IS '甲方联系方式';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACT"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"."personId" IS '人员id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENT2PERSON"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"."achievementId" IS '人员绩效id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"."contractCode" IS '合同编号';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"."contractName" IS '合同名称';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"."firstEntName" IS '甲方名称';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"."totalAmount" IS '合同金额';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"."signDate" IS '签订日期';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"."signPersonId" IS '签订人员id';

COMMENT
ON COLUMN "TB_PRO_ORDERCONTRACTACHIEVEMENTDETAILS"."contractNature" IS '合同性质';

COMMENT
ON TABLE "TB_PRO_ORDERFORM" IS '订单信息';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."orderCode" IS '订单号';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."projectTypeId" IS '业务类型';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."orderName" IS '订单名称';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."salesPersonId" IS '业务员Id';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."salesPersonName" IS '业务员名称';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."orderDate" IS '订单日期';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."timeLimit" IS '报价期限';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."enterpriseId" IS '客户Id';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."enterpriseName" IS '客户名称';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."linkPerson" IS '联系人';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."linkPhone" IS '联系电话';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."address" IS '地址';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."orderStatus" IS '订单状态';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."pushStatus" IS '推送状态';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."flowType" IS '流程类型';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."firstPersonId" IS '一审人员';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."secondPersonId" IS '二审人员';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."threePersonId" IS '三审人员';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."registrantId" IS '登记人员Id';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."registrantName" IS '登记人员名称';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."customerOrderNo" IS '客户订单编号';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."grantStatus" IS '签单状态（签订,不签订）';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."signDate" IS '签单日期';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."inspectedEntId" IS '受检单位Id';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."areaId" IS '行政区域Id';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."inspectedEnt" IS '受检单位';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."inspectedLinkMan" IS '受检方联系人';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."inspectedLinkPhone" IS '受检方联系电话';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."inspectedAddress" IS '受检方地址';

COMMENT
ON COLUMN "TB_PRO_ORDERFORM"."remark" IS '备注';

COMMENT
ON TABLE "TB_PRO_ORDERQUOTATION" IS '订单总报价';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."orderId" IS '订单id';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."testPrice" IS '检测费小计';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."testDiscount" IS '检测折扣率';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."discountPrice" IS '折后检测费';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."otherPrice" IS '其他费用小计';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."preTax" IS '税前报价';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."taxRate" IS '税率';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."totalDiscount" IS '总价折扣率';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."totalPrice" IS '总价';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."finalQuotation" IS '最后报价';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_ORDERQUOTATION"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_OTHERDETAIL" IS '其他费用明细';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."orderId" IS '订单id';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."quotationId" IS '总计id';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."typeId" IS '费用项目id';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."standard" IS '收费标准';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."unit" IS '单位';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."days" IS '天数';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."count" IS '数量';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."price" IS '小计';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."quotedPrice" IS '报价';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_OTHERDETAIL"."formula" IS '计算公式';

COMMENT
ON TABLE "TB_PRO_OUTSOURCEDATA" IS '分包数据表';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."analyseDataId" IS '分析数据id';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."analyzeMethodName" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."analyzeMethodId" IS '分析方法id';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."testValue" IS '出证结果';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."dimensionName" IS '量纲名称';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."dimensionId" IS '量纲id';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."state" IS '状态（0未确认 1已确认）';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."domainId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."analyzeTime" IS '分析日期';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."analyzeEndTime" IS '分析结束日期';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."subcontractor" IS '分包商';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."cmaCode" IS '分包商CMA证书编号';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."outSourceReportCode" IS '分包报告编号';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_OUTSOURCEDATA"."detectionLimit" IS '检出限';

COMMENT
ON TABLE "TB_PRO_PARAMSDATA" IS '参数数据表';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."objectId" IS '对象Id';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."objectType" IS '对象类型（枚举EnumParamsDataType1:样品，2:工作单，3:企业，4.采样单）';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."paramsConfigId" IS '参数配置id';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."paramsName" IS '参数名称（使用别名）';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."dimension" IS '计量单位';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."dimensionId" IS '计量单位Id';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."orderNum" IS '排序';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."groupId" IS '分组Id';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_PARAMSDATA"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA" IS '工作量统计（现场数据）';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."receiveId" IS '送样单id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."recordCode" IS '送样单号';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."recorderId" IS '送样登记人';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."sendTime" IS '分析日期';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."redAnalyzeItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."redAnalyzeMethodName" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."analyseItemId" IS '分析项目id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."analyzeMethodId" IS '分析方法id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."sample" IS '样品数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."localeGap" IS '全程序空白样品数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."parallel" IS '室内平行样品数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."valid" IS '有效数据';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORLOCALDATA"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORREPORTDATA"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORREPORTDATA"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORREPORTDATA"."reportTime" IS '报告时间';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORREPORTDATA"."reportMakerId" IS '编制报告人id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORREPORTDATA"."report" IS '报告数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORREPORTDATA"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA" IS '采样项目的绩效';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."receiveId" IS '送样单id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."recordCode" IS '送样单号';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."samplingPersonId" IS '采样人员';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."sendTime" IS '送样日期';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."samplingTime" IS '采样时间';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."senderName" IS '送样人员';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."sample" IS '样品数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORSAMPLEDATA"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA" IS '工作量统计（工作单数据）';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."workSheetFolderId" IS '工作单id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."workSheetCode" IS '检测单编号';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."testId" IS '测试项目id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."analystId" IS '分析人Id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."analyzeTime" IS '分析日期';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."redAnalyzeItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."redAnalyzeMethodName" IS '分析方法名称';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."analyseItemId" IS '分析项目id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."analyzeMethodId" IS '分析方法id';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."sample" IS '样品数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."localeGap" IS '全程序空白样品数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."interiorGap" IS '室内空白样品数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."parallel" IS '平行样品数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."addition" IS '加标样品数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."curveItem" IS '曲线条数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."curveEntries" IS '曲线个数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."point" IS '带点个数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."qcSample" IS '带质控数';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."valid" IS '有效数据';

COMMENT
ON COLUMN "TB_PRO_PERFORMANCESTATISTICFORWORKSHEETDATA"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_PROJECT" IS '下达的项目、任务';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."parentId" IS '父级项目id';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."projectCode" IS '流水编号';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."projectTypeId" IS '项目类型id（外键）';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."projectName" IS '项目名称';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."status" IS '项目状态(存枚举字符串EnumProjectStatus)';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."samplingStatus" IS '委托现场送样装填（EnumSampledStatus 1.未采毕 2.已采毕）';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."reportStatus" IS '报告流程状态（EnumReportStatus： 0.未完成，1.已完成）';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."inputTime" IS '项目登记时间(登记时间不能改)';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."inceptPersonId" IS '登记人id';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."sendSamplePerson" IS '送样人';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."inceptTime" IS '委托时间';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."lastModifyTime" IS '项目最后修改时间';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."expectedCharge" IS '预计收费';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."actualCharges" IS '实际收费';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."isStress" IS '是否着重关注';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."grade" IS '项目等级(EnumProjectGrade：0.一般 1.紧急 2.特急)';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."inspectedEntId" IS '受检单位Id';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."inspectedEnt" IS '受检单位';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."inspectedLinkMan" IS '受检方联系人';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."inspectedLinkPhone" IS '受检方联系电话';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."inspectedAddress" IS '受检方地址';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."customerOwner" IS '法人代表';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."customerAddress" IS '地址';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."linkMan" IS '联系人';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."linkPhone" IS '电话';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."linkEmail" IS '电子邮件';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."linkFax" IS '传真';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."zipCode" IS '邮编';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."monitorPurp" IS '监测目的';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."monitorMethods" IS '监测方式';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."customerRequired" IS '客户委托内容';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."sampleType" IS '样品类型';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."postMethod" IS '发送方式(常量 PRO_PostMethod)';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."qcInfo" IS '质控信息';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."controlInfo" IS '监督信息';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."projectAddress" IS '项目地址';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."docNumber" IS '项目批准机关及文号';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."compDate" IS '建设竣工日期';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."invAmount" IS '投资总额';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."analyzeMethod" IS '监测方法(0:无,1.按现行国标行标执行2.客户指定方法)';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."reportMethod" IS '检验结果告知方式(1.出具正规报告2.电话告知)';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."saveCondition" IS '保存条件';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."saveDate" IS '保存期限';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."reportNum" IS '检测报告数';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."isOnline" IS '是否网上登记(0.否 1. 是)';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."pushStatus" IS '推送状态(0：不发布，1：已发布，2：已办结）';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."qrCodeUrl" IS '二维码图片存储地址';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."sampleQuantity" IS '国检_样品数量（出具在报告中）';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."isQualified" IS '国检_是否合格(样品是否含有不合格项)';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."sampleDescription" IS '国检_样品描述';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."sampleNameCustomer" IS '国检_委托品名';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."samKind" IS '国检_样品贸易类型（出口/进口/内贸）';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."tradeAreaCode" IS '国检_贸易区代码';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."batchCode" IS '国检_批号';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."testCode" IS '国检_客户编号（报验号）';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."prodCompany" IS '国检_生产厂家';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."compAddress" IS '国检_产地';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendInt1" IS '预留int类型1';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendInt2" IS '预留int类型2';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendInt3" IS '预留int类型3';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendStr1" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendStr2" IS '预留string类型2';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendStr3" IS '预留string类型3';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendGuid1" IS '预留Guid类型1';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendGuid2" IS '预留Guid类型2';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendGuid3" IS '预留Guid类型3';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendDate1" IS '预留Date类型1';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendDate2" IS '预留Date类型2';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."extendDate3" IS '预留Date类型3';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."json" IS '冗余json信息';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."orderId" IS '订单id';

COMMENT
ON COLUMN "TB_PRO_PROJECT"."isMultiEnterprise" IS '是否多企业';

COMMENT
ON TABLE "TB_PRO_PROJECT2CONTRACT" IS '项目与合同的关联表';

COMMENT
ON COLUMN "TB_PRO_PROJECT2CONTRACT"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_PROJECT2CONTRACT"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2CONTRACT"."contractId" IS '合同id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2CUSTOMER"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2CUSTOMER"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2CUSTOMER"."customerId" IS '企业id';

COMMENT
ON COLUMN "TB_PRO_PROJECT2CUSTOMER"."customerName" IS '企业名称';

COMMENT
ON TABLE "TB_PRO_PROJECTPLAN" IS '任务计划';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."leaderId" IS '项目负责人id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."reportMakerId" IS '编制报告人id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."schemeMakerId" IS '编制方案人id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."spotPersonId" IS '现场负责人id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."supervisorId" IS '监督人id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."deadLine" IS '要求完成时间';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."reportDate" IS '预计出具报告时间';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."requireAnalyzeDate" IS '预计完成分析日期';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."requireSamplingDate" IS '预计完成采样日期';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."responsePerson" IS '责任人（质量控制计划）';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."requires" IS '测试项目或具体要求的文字说明';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."testMethodRequires" IS '测试标准文字说明';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."isUseMethod" IS '是否同意使用非标准方法';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."isEvaluate" IS '是否评价';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."isWarning" IS '是否警告';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."warningDay" IS '提前警告天数';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."isFeedback" IS '是否反馈';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."isContract" IS '是否拟订合同';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."subName" IS '分包单位';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."subItems" IS '分包项目';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."subMethod" IS '分包方式(枚举EnumSubMethod:0.无1.客户指定2.本站联系)';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."isOutsourcing" IS '分包情况(枚举EnumOutSourcing: 0.不分包1.全部分包2.部分分包)';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."isMakePlan" IS '是否编制方案';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."reportMakerIIId" IS '辅助编制报告人id';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."qcGrade" IS '质量任务等级（枚举EnumPorjectQCGrade 1.内部质控 2.外部质控 3.分包质控）';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."qcType" IS '质量任务类型(内部质控EnumInnerQCType：1.人员比对 2.方法比对 3.仪器比对 4.标样考核 5.盲样考核。外部质控EnumOuterQCType：1.实验室间比对 2.能力验证3.测量审核 4.其他 。分包质控EnumSubQCType：1.社会实验室比对 2.社会实验室标样考核)
   ';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."qcSource" IS '质量来源（质量控制——外部质控）';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."judgment" IS '判断依据（质量控制计划）';

COMMENT
ON COLUMN "TB_PRO_PROJECTPLAN"."orgId" IS '组织机构id';

CREATE TABLE "TB_PRO_SAMPLINGPERSONCONFIG"
(
    "id"               VARCHAR(50) NOT NULL,
    "objectId"         VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT         DEFAULT 0
                                   NOT NULL,
    "samplingPersonId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "samplingPerson"   VARCHAR(50) NULL,
    "orgId"            VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_SOLUTIONCALIBRATION"
(
    "id"                              VARCHAR(50) NOT NULL,
    "workSheetFolderId"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "calibrationSolutionName"         VARCHAR(100) NULL,
    "transferSolutionName"            VARCHAR(100) NULL,
    "transferSolutionConcentration"   VARCHAR(50) NULL,
    "transferSolutionDimensionId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "mostSignificance"                INT          DEFAULT (-1)
                                                  NOT NULL,
    "mostDecimal"                     INT          DEFAULT (-1)
                                                  NOT NULL,
    "averageConcentration"            VARCHAR(50) NULL,
    "averageConcentrationDimensionId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "isDeleted"                       BIT          DEFAULT 0
                                                  NOT NULL,
    "orgId"                           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "creator"                         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "createDate"                      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                                  NOT NULL,
    "domainId"                        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "modifier"                        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                  NOT NULL,
    "modifyDate"                      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                                  NOT NULL
);
CREATE TABLE "TB_PRO_SOLUTIONCALIBRATIONRECORD"
(
    "id"                    VARCHAR(50) NOT NULL,
    "solutionCalibrationId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "transferVolume"        VARCHAR(50) NULL,
    "volumeStart"           VARCHAR(50) NULL,
    "volumeEnd"             VARCHAR(50) NULL,
    "concentration"         VARCHAR(50) NULL,
    "mostSignificance"      INT          DEFAULT (-1)
                                        NOT NULL,
    "mostDecimal"           INT          DEFAULT (-1)
                                        NOT NULL,
    "isDeleted"             BIT          DEFAULT 0
                                        NOT NULL,
    "orgId"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "creator"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "createDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                        NOT NULL,
    "domainId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "modifier"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "modifyDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                        NOT NULL
);
CREATE TABLE "TB_PRO_STATUSFORCOSTINFO"
(
    "id"                VARCHAR(50) NOT NULL,
    "costInfoId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "module"            VARCHAR(50) NOT NULL,
    "status"            INT          DEFAULT 1
                                    NOT NULL,
    "currentPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "currentPersonName" VARCHAR(50) NULL,
    "nextPersonId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "nextPersonName"    VARCHAR(50) NULL,
    "lastNewOpinion"    CLOB NULL,
    "extendStr1"        VARCHAR(255) NULL,
    "extendStr2"        VARCHAR(255) NULL,
    "extendStr3"        VARCHAR(255) NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL
);
CREATE TABLE "TB_PRO_STATUSFORPROJECT"
(
    "id"                VARCHAR(50) NOT NULL,
    "projectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "module"            VARCHAR(50) NOT NULL,
    "status"            INT          DEFAULT 1
                                    NOT NULL,
    "currentPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "currentPersonName" VARCHAR(50) NULL,
    "nextPersonId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "nextPersonName"    VARCHAR(50) NULL,
    "lastNewOpinion"    CLOB NULL,
    "extendStr1"        VARCHAR(255) NULL,
    "extendStr2"        VARCHAR(255) NULL,
    "extendStr3"        VARCHAR(255) NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL
);
CREATE TABLE "TB_PRO_STATUSFORRECORD"
(
    "id"                VARCHAR(50) NOT NULL,
    "receiveId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "module"            VARCHAR(50) NOT NULL,
    "status"            INT          DEFAULT 1
                                    NOT NULL,
    "currentPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "currentPersonName" VARCHAR(50) NULL,
    "nextPersonId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "nextPersonName"    VARCHAR(50) NULL,
    "lastNewOpinion"    CLOB NULL,
    "extendStr1"        VARCHAR(255) NULL,
    "extendStr2"        VARCHAR(255) NULL,
    "extendStr3"        VARCHAR(255) NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL
);
CREATE TABLE "TB_PRO_STATUSFORREPORT"
(
    "id"                VARCHAR(50) NOT NULL,
    "reportId"          VARCHAR(50) NOT NULL,
    "module"            VARCHAR(50) NOT NULL,
    "status"            INT          DEFAULT 1
                                    NOT NULL,
    "currentPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "currentPersonName" VARCHAR(50) NULL,
    "nextPersonId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "nextPersonName"    VARCHAR(50) NULL,
    "lastNewOpinion"    CLOB NULL,
    "extendStr1"        VARCHAR(255) NULL,
    "extendStr2"        VARCHAR(255) NULL,
    "extendStr3"        VARCHAR(255) NULL,
    "orgId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "creator"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "createDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL,
    "domainId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifier"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                    NOT NULL,
    "modifyDate"        TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                    NOT NULL
);
CREATE TABLE "TB_PRO_SUBMITRECORD"
(
    "id"               VARCHAR(50) NOT NULL,
    "objectId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "objectType"       INT          DEFAULT 0
                                   NOT NULL,
    "submitType"       INT          DEFAULT 0
                                   NOT NULL,
    "submitTime"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "submitPersonId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "submitPersonName" VARCHAR(50) NULL,
    "nextPerson"       VARCHAR(200) NULL,
    "submitRemark"     VARCHAR(200) NULL,
    "stateFrom"        VARCHAR(50) NULL,
    "stateTo"          VARCHAR(50) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL
);
CREATE TABLE "TB_PRO_SURVEY"
(
    "id"           VARCHAR(50) NOT NULL,
    "projectId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "surveyTime"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                               NOT NULL,
    "surveyUserId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "surveyNote"   VARCHAR(1000) NULL,
    "problem"      VARCHAR(1000) NULL,
    "entDelegate"  VARCHAR(50) NULL,
    "confirmTime"  TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                               NOT NULL,
    "remark"       VARCHAR(1000) NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL
);
CREATE TABLE "TB_PRO_TASK2FIXEDPROPERTY"
(
    "id"              VARCHAR(50) NOT NULL,
    "taskId"          VARCHAR(50) NOT NULL,
    "fixedPropertyId" VARCHAR(50) NOT NULL
);
CREATE TABLE "TB_PRO_WORKSHEET"
(
    "id"                 VARCHAR(50) NOT NULL,
    "parentId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "testId"             VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "recordId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "analyseItemId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "redAnalyzeItemName" VARCHAR(100) NULL,
    "remark"             VARCHAR(1000) NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "isDeleted"          BIT          DEFAULT 0
                                     NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                     NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                     NOT NULL
);
CREATE TABLE "TB_PRO_WORKSHEETCALIBRATIONCURVE"
(
    "id"              VARCHAR(50) NOT NULL,
    "worksheetId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "standardCurveId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "checkDate"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                  NOT NULL,
    "remark"          VARCHAR(1000) NULL,
    "orgId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "isDeleted"       BIT          DEFAULT 0
                                  NOT NULL,
    "creator"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL,
    "domainId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifier"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                  NOT NULL,
    "modifyDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                  NOT NULL
);
CREATE TABLE "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"
(
    "id"                          VARCHAR(50) NOT NULL,
    "workSheetCalibrationCurveId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                              NOT NULL,
    "analyseCode"                 VARCHAR(50) NULL,
    "addVolume"                   VARCHAR(50) NULL,
    "addAmount"                   VARCHAR(50) NULL,
    "absorbance"                  VARCHAR(50) NULL,
    "lessBlankAbsorbance"         VARCHAR(50) NULL,
    "absorbanceB"                 VARCHAR(50) NULL,
    "relativeDeviation"           VARCHAR(50) NULL,
    "aValueTTZ"                   VARCHAR(50) NULL,
    "aValueTSF"                   VARCHAR(50) NULL,
    "orgId"                       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                              NOT NULL,
    "isDeleted"                   BIT          DEFAULT 0
                                              NOT NULL,
    "creator"                     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                              NOT NULL,
    "createDate"                  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                              NOT NULL,
    "domainId"                    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                              NOT NULL,
    "modifier"                    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                              NOT NULL,
    "modifyDate"                  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                              NOT NULL
);
CREATE TABLE "TB_PRO_WORKSHEETFOLDER"
(
    "id"               VARCHAR(50) NOT NULL,
    "workSheetCode"    VARCHAR(20) NULL,
    "createTime"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "analystId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "analystName"      VARCHAR(50) NULL,
    "analyzeTime"      TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                   NOT NULL,
    "analyzeMethodId"  VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "checkerId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "checkerName"      VARCHAR(50) NULL,
    "checkDate"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                   NOT NULL,
    "auditorId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "auditorName"      VARCHAR(50) NULL,
    "auditDate"        TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                   NOT NULL,
    "status"           VARCHAR(50) NULL,
    "workStatus"       INT          DEFAULT 1
                                   NOT NULL,
    "backOpinion"      VARCHAR(1000) NULL,
    "remark"           VARCHAR(1000) NULL,
    "orgId"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "creator"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "createDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "domainId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifier"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                   NOT NULL,
    "modifyDate"       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                   NOT NULL,
    "certificatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "certificatorName" VARCHAR(50) NULL,
    "sortId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
        NULL,
    "finishTime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                   NOT NULL,
    "isDeleted"        BIT          DEFAULT 0
                                   NOT NULL,
    "backTimes"        INT          DEFAULT 0
                                   NOT NULL,
    "submitTime"       TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                   NOT NULL
);
CREATE TABLE "TB_PRO_WORKSHEETREAGENT"
(
    "id"                    VARCHAR(50) NOT NULL,
    "worksheetFolderId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "reagentConfigId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "reagent"               VARCHAR(1000) NULL,
    "context"               VARCHAR(1000) NULL,
    "reagentName"           VARCHAR(255) NULL,
    "reagentSpecification"  VARCHAR(255) NULL,
    "configurationSolution" VARCHAR(255) NULL,
    "configDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                        NOT NULL,
    "expiryDate"            TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
                                        NOT NULL,
    "course"                VARCHAR(1000) NULL,
    "opinion"               VARCHAR(1000) NULL,
    "orgId"                 VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "creator"               VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "diluent"               VARCHAR(200) DEFAULT ''
        NULL,
    "reagentType"           INT          DEFAULT 0
                                        NOT NULL,
    "suitItem"              VARCHAR(200) DEFAULT ''
        NULL,
    "concentration"         VARCHAR(200) NULL,
    "reagentVolumeQuality"  VARCHAR(200) NULL,
    "constantVolume"        VARCHAR(200) NULL,
    "isDeleted"             BIT          DEFAULT 0
                                        NOT NULL,
    "createDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                        NOT NULL,
    "domainId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "modifier"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                        NOT NULL,
    "modifyDate"            TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                        NOT NULL,
    "relateFlag"            BIT          DEFAULT 0
                                        NOT NULL
);
ALTER TABLE "TB_PRO_SAMPLINGPERSONCONFIG"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SOLUTIONCALIBRATION"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SOLUTIONCALIBRATIONRECORD"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_STATUSFORCOSTINFO"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_STATUSFORPROJECT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_STATUSFORRECORD"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_STATUSFORREPORT"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SUBMITRECORD"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_SURVEY"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_TASK2FIXEDPROPERTY"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_WORKSHEET"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_WORKSHEETCALIBRATIONCURVE"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_WORKSHEETFOLDER"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_PRO_WORKSHEETREAGENT"
    ADD CONSTRAINT PRIMARY KEY ("id");

CREATE INDEX "IX_TB_PRO_SamplingPersonCopnfig"
    ON "TB_PRO_SAMPLINGPERSONCONFIG" ("objectId", "objectType", "samplingPersonId");

CREATE INDEX "IX_TB_PRO_SolutionCalibration"
    ON "TB_PRO_SOLUTIONCALIBRATION" ("orgId", "workSheetFolderId", "isDeleted");

CREATE INDEX "IX_TB_PRO_SolutionCalibrationRecord"
    ON "TB_PRO_SOLUTIONCALIBRATIONRECORD" ("orgId", "solutionCalibrationId", "isDeleted");

CREATE INDEX "IX_TB_PRO_StatusForProject"
    ON "TB_PRO_STATUSFORPROJECT" ("orgId", "projectId", "module");

CREATE INDEX "IX_TB_PRO_StatusForRecord"
    ON "TB_PRO_STATUSFORRECORD" ("orgId", "receiveId", "module");

CREATE INDEX "IX_TB_PRO_StatusForRecord2"
    ON "TB_PRO_STATUSFORRECORD" ("receiveId", "module", "status", "orgId");

CREATE INDEX "IX_TB_PRO_WorkSheet"
    ON "TB_PRO_WORKSHEET" ("parentId", "testId", "isDeleted");

CREATE INDEX "IX_TB_PRO_WorkSheetCalibrationCurve"
    ON "TB_PRO_WORKSHEETCALIBRATIONCURVE" ("worksheetId", "standardCurveId", "isDeleted");

CREATE INDEX "IX_TB_PRO_WorkSheetCalibrationCurveDetail"
    ON "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL" ("workSheetCalibrationCurveId", "isDeleted");

CREATE INDEX "IX_TB_PRO_WorkSheetFolder"
    ON "TB_PRO_WORKSHEETFOLDER" ("orgId", "analyzeTime", "analystId", "workStatus", "workSheetCode");

CREATE INDEX "IX_TB_PRO_WorkSheetReagent"
    ON "TB_PRO_WORKSHEETREAGENT" ("worksheetFolderId", "reagentConfigId", "isDeleted");

COMMENT
ON COLUMN "TB_PRO_SAMPLINGPERSONCONFIG"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGPERSONCONFIG"."objectId" IS '对象Id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGPERSONCONFIG"."objectType" IS '对象类型(枚举EnumSamplingType：0.任务1.送样单)';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGPERSONCONFIG"."samplingPersonId" IS '采样人Id';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGPERSONCONFIG"."samplingPerson" IS '采样人';

COMMENT
ON COLUMN "TB_PRO_SAMPLINGPERSONCONFIG"."orgId" IS '组织机构id';

COMMENT
ON TABLE "TB_PRO_SOLUTIONCALIBRATION" IS '检测单溶液标定表';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."workSheetFolderId" IS '检测单标识';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."calibrationSolutionName" IS '标定溶液名称';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."transferSolutionName" IS '移取溶液名称';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."transferSolutionConcentration" IS '移取溶液浓度';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."transferSolutionDimensionId" IS '移取溶液浓度量纲标识';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."mostSignificance" IS '有效位数';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."mostDecimal" IS '小数位数';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."averageConcentration" IS '标定浓度均值';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."averageConcentrationDimensionId" IS '标定浓度均值量纲标识';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATION"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_SOLUTIONCALIBRATIONRECORD" IS '检测单溶液标定记录表';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."solutionCalibrationId" IS '溶液标定标识';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."transferVolume" IS '溶液移取量';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."volumeStart" IS 'V始';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."volumeEnd" IS 'V终';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."concentration" IS '标定液浓度';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."mostSignificance" IS '有效位数';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."mostDecimal" IS '小数位数';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_SOLUTIONCALIBRATIONRECORD"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_STATUSFORCOSTINFO" IS '费用状态表';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."costInfoId" IS '费用id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."module" IS '模块编码（枚举EnumCostInfoModule）';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."status" IS '状态（枚举 EnumStatus 1待处理 2已处理）';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."currentPersonId" IS '当前操作人Id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."currentPersonName" IS '当前操作人名称';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."nextPersonId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."nextPersonName" IS '下一步操作人名称';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."lastNewOpinion" IS '最新一条意见';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."extendStr1" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."extendStr2" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."extendStr3" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_STATUSFORCOSTINFO"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_STATUSFORPROJECT" IS '项目状态表';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."projectId" IS '项目id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."module" IS '模块编码（枚举EnumProjectModule）';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."status" IS '状态（枚举 EnumStatus 1待处理 2已处理）';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."currentPersonId" IS '当前操作人Id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."currentPersonName" IS '当前操作人名称';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."nextPersonId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."nextPersonName" IS '下一步操作人名称';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."lastNewOpinion" IS '最新一条意见';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."extendStr1" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."extendStr2" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."extendStr3" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_STATUSFORPROJECT"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_STATUSFORRECORD" IS '送样单状态表';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."receiveId" IS '送样单id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."module" IS '模块编码（枚举EnumRecordModule）';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."status" IS '状态（枚举 EnumStatus 1待处理 2已处理）';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."currentPersonId" IS '当前操作人Id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."currentPersonName" IS '当前操作人名称';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."nextPersonId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."nextPersonName" IS '下一步操作人名称';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."lastNewOpinion" IS '最新一条意见';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."extendStr1" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."extendStr2" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."extendStr3" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_STATUSFORRECORD"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_STATUSFORREPORT" IS '报告状态表';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."id" IS '主键id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."reportId" IS '报告id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."module" IS '模块编码（枚举EnumReportModule）';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."status" IS '状态（枚举 EnumStatus 1待处理 2已处理）';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."currentPersonId" IS '当前操作人Id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."currentPersonName" IS '当前操作人名称';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."nextPersonId" IS '下一步操作人Id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."nextPersonName" IS '下一步操作人名称';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."lastNewOpinion" IS '最新一条意见';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."extendStr1" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."extendStr2" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."extendStr3" IS '预留string类型1';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_STATUSFORREPORT"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."objectId" IS '关联Id';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."objectType" IS '关联对象类型（枚举EnumSubmitObjectType：1.项目 2.工作单 3.送样单 4.现场领样单 5.分析领样单）';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."submitType" IS '操作类型（枚举EnumSubmitType：0.无 1.项目登记 2.任务下达 ....）';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."submitTime" IS '操作时间';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."submitPersonId" IS '操作人';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."submitPersonName" IS '操作人名称';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."nextPerson" IS '下一步操作人（名字）';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."submitRemark" IS '操作意见';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."stateFrom" IS '操作前状态';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."stateTo" IS '操作后状态';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_SUBMITRECORD"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_SURVEY" IS '现场踏勘';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."projectId" IS '项目ID';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."surveyTime" IS '踏勘时间';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."surveyUserId" IS '踏勘人id';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."surveyNote" IS '现场踏勘情况（详细文本信息）';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."problem" IS '企业存在问题(原常量数值+是否合格 数值存放)';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."entDelegate" IS '企业签字人';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."confirmTime" IS '签字确认时间';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_SURVEY"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_TASK2FIXEDPROPERTY"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_TASK2FIXEDPROPERTY"."taskId" IS '任务id';

COMMENT
ON COLUMN "TB_PRO_TASK2FIXEDPROPERTY"."fixedPropertyId" IS '断面属性id';

COMMENT
ON TABLE "TB_PRO_WORKSHEET" IS '工作单子表';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."parentId" IS '主表Id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."testId" IS '测试Id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."recordId" IS '原始记录单id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."analyseItemId" IS '分析项目id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."redAnalyzeItemName" IS '分析项目名称';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_WORKSHEET"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_WORKSHEETCALIBRATIONCURVE" IS '校准曲线';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."worksheetId" IS '工作单id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."standardCurveId" IS '标准曲线id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."checkDate" IS '校准日期';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVE"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL" IS '校准曲线详情';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."id" IS '主键';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."workSheetCalibrationCurveId" IS '工作单校准曲线id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."analyseCode" IS '分析编号';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."addVolume" IS '标准溶液加入体积';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."addAmount" IS '标准物加入量';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."absorbance" IS '吸光度A';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."lessBlankAbsorbance" IS '减空白吸光度';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."absorbanceB" IS '吸光度B';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."relativeDeviation" IS '相对偏差';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."aValueTTZ" IS '220吸光度';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."aValueTSF" IS '275吸光度';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETCALIBRATIONCURVEDETAIL"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_PRO_WORKSHEETFOLDER" IS '工作单';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."workSheetCode" IS '工作单号';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."createTime" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."analystId" IS '分析人Id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."analystName" IS '分析人名称';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."analyzeTime" IS '分析日期';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."analyzeMethodId" IS '检测方法id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."checkerId" IS '复核人id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."checkerName" IS '复核人名称';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."checkDate" IS '复核日期';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."auditorId" IS '审核人id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."auditorName" IS '审核人姓名';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."auditDate" IS '审核日期';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."status" IS '工作单状态（字符串，枚举EnumWorkSheetStatus：1.新建 2.已经保存 6.工作单拒绝 8.已经提交 24.复核通过 32.审核通过）';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."workStatus" IS '工作单状态（int，枚举EnumWorkSheetStatus：1.新建 2.已经保存 6.工作单拒绝 8.已经提交 24.复核通过 32.审核通过）';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."backOpinion" IS '退回意见（最新一个）';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."remark" IS '备注';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."certificatorId" IS '上岗证人员id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."certificatorName" IS '上岗证人员姓名';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."sortId" IS '排序id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."finishTime" IS '分析完成日期';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."backTimes" IS '退回次数';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETFOLDER"."submitTime" IS '提交时间';

COMMENT
ON TABLE "TB_PRO_WORKSHEETREAGENT" IS '工作单配置试剂配置';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."id" IS 'id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."worksheetFolderId" IS '工作单id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."reagentConfigId" IS '试剂配置记录id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."reagent" IS '配置记录';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."context" IS '需求的配置过程 ';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."reagentName" IS '试剂名称';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."reagentSpecification" IS '试剂规格';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."configurationSolution" IS '配置溶液';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."configDate" IS '配置日期';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."expiryDate" IS '有效期';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."course" IS '稀释过程记录';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."opinion" IS '其他情况';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."creator" IS '配置人员id';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."diluent" IS '稀释液';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."reagentType" IS '试剂类型（1：一般试剂 2：标准溶液）';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."suitItem" IS '适用项目';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."concentration" IS '浓度';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."reagentVolumeQuality" IS '试剂体积或质量';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."constantVolume" IS '定容体积';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."isDeleted" IS '假删字段';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_PRO_WORKSHEETREAGENT"."relateFlag" IS '是否已有试剂选择';