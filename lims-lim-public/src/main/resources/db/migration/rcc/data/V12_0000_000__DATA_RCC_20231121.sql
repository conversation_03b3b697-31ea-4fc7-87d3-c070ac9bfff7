-- 样品流转单应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('1162c507-2bf4-4ef8-bfb4-515701eeb91c', '60918d00-7b37-493c-80a3-f905e168a1a1', 'ReportEdit', '报告编制',
        'SampleCirculation', '样品流转单', 1, 0, 1, '', '编制报告:关联样品', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-08 13:45:26', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-08 13:45:26', NULL);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('2c5f61c8-e8a8-4620-9900-13ea79fcf7ce', '60918d00-7b37-493c-80a3-f905e168a1a1', 'ReportEditNew', '报告编制（新）',
        'SampleCirculation', '样品流转单', 1, 0, 1, '', '编制报告:关联样品', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-08 13:28:00', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-11-08 13:28:00', NULL);

