package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;


/**
 * VersionInfo操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/9
 * @since V100R001
 */
public interface VersionInfoService extends IBaseJpaService<DtoVersionInfo, String> {

    /**
     * 获取附件路径
     *
     * @param id 版本信息id
     * @return 返回相应的路径信息
     */
    DtoVersionInfo findAttachPath(String id);

    /**
     * 生成二维码数据
     * @param versionInfo 生成二维码的实体
     * @return 二维码base64
     */
    DtoVersionInfo createQrCode(DtoVersionInfo versionInfo);
}