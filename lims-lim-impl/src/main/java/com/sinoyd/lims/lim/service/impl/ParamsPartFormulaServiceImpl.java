package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.service.ParamsPartFormulaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * ParamsPartFormula操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
 @Service
public class ParamsPartFormulaServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoParamsPartFormula,String,ParamsPartFormulaRepository> implements ParamsPartFormulaService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void findByPage(PageBean<DtoParamsPartFormula> pb, BaseCriteria paramsPartFormulaCriteria) {
        pb.setEntityName("DtoParamsPartFormula a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, paramsPartFormulaCriteria);
    }

    @Override
    public List<DtoParamsPartFormula> findRedisByFormulaId(String formulaId) {
        List<DtoParamsPartFormula> dtoParamsPartFormulas;
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_ParamsPartFormula.getValue());
        Object json = redisTemplate.opsForHash().get(key, formulaId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            TypeLiteral<List<DtoParamsPartFormula>> typeLiteral = new TypeLiteral<List<DtoParamsPartFormula>>() {
            };
            dtoParamsPartFormulas = JsonIterator.deserialize((String) json, typeLiteral);
        } else {
            dtoParamsPartFormulas = repository.findByFormulaId(formulaId);
            if (StringUtil.isNotNull(dtoParamsPartFormulas) && dtoParamsPartFormulas.size() > 0) {
                redisTemplate.opsForHash().put(key, formulaId, JsonStream.serialize(dtoParamsPartFormulas));
            }
        }
        return dtoParamsPartFormulas;
    }

    @Override
    public List<DtoParamsPartFormula> findByFormulaIds(List<String> formulaIds) {
        return repository.findByFormulaIdIn(formulaIds);
    }

    @Override
    public List<DtoParamsPartFormula> findByFormulaId(String id) {
        return repository.findByFormulaId(id);
    }
}