package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * ReportModule2GroupType实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ReportModule2GroupType")
@Data
public class ReportModule2GroupType implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 报告组件配置id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("报告组件配置id")
    private String reportConfigModuleId;

    /**
     * 分页类型名称（包含数据源，属性名称,分页方式）
     */
    @Column(nullable = false)
    @ApiModelProperty("groupTypeName")
    @Length(message = "分页类型名称{validation.message.length}", max = 255)
    private String groupTypeName;

    /**
     * 优先级（最外层分页的优先级最高）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("priority")
    private Integer priority;

}