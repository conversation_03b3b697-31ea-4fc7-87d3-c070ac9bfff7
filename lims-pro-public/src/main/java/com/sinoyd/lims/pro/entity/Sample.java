package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * Sample实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Sample")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Sample extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  Sample() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 样品编号
    */
    @Column(length=50)
    @ApiModelProperty("样品编号")
    @Length(message = "样品编号{validation.message.length}", max = 50)
	private String code;
    
    /**
    * 子项目的id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("子项目的id")
	private String subProjectId;
    
    /**
    * 项目id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目id")
	private String projectId;
    
    /**
    * 送样记录id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("送样记录id")
	private String receiveId;
    
    /**
    * 点位id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("点位id")
	private String sampleFolderId;

    /**
     * 频次id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("频次id")
    private String samplingFrequencyId;

    /**
    * 采样周期序数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("采样周期序数")
	private Integer cycleOrder;
    
    /**
    * 每周期次数序数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("每周期次数序数")
	private Integer timesOrder;
    
    /**
    * 每次样品序数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("每次样品序数")
	private Integer sampleOrder;
    
    /**
    * 冗余-点位
    */
    @Column(length=100)
    @ApiModelProperty("冗余-点位")
    @Length(message = "冗余-点位{validation.message.length}", max = 100)
	private String redFolderName;
    
    /**
    * 冗余-分析项目
    */
    @Column(length=1000)
    @ApiModelProperty("冗余-分析项目")
    @Length(message = "冗余-分析项目{validation.message.length}", max = 4000)
	private String redAnalyzeItems;
    
    /**
    * 采样人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样人id")
	private String samplingPersonId;
    
    /**
    * 检测类型id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型id")
	private String sampleTypeId;
    
    /**
    * 样品类别（EnumSampleCategory：0.原样 1.质控样 2.串联样 3.原样加原样 4.比对样）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("样品类别（EnumSampleCategory：0.原样 1.质控样 2.串联样 3.原样加原样 4.比对样）")
	private Integer sampleCategory;
    
    /**
    * 受检单位
    */
    @Column(length=100)
    @ApiModelProperty("受检单位")
    @Length(message = "受检单位{validation.message.length}", max = 100)
	private String inspectedEnt;
    
    /**
    * 受检单位ID
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("受检单位ID")
	private String inspectedEntId;
    
    /**
    * 样品登记时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("样品登记时间")
	private Date inceptTime;
    
    /**
    * 采样开始时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("采样开始时间")
	private Date samplingTimeBegin;
    
    /**
    * 采样结束时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("采样结束时间")
	private Date samplingTimeEnd;
    
    /**
    * 样品状态（过程状态，枚举字符串EnumSampleStatus）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("样品状态（过程状态，枚举字符串EnumSampleStatus）")
    //@NotBlank(message = "样品状态（过程状态，枚举字符串EnumSampleStatus）{validation.message.blank}")
    @Length(message = "样品状态（过程状态，枚举字符串EnumSampleStatus）{validation.message.length}", max = 50)
	private String status;
    
    /**
    * 采样分配状态（枚举EnumSamplingConfig：0.未分配 1.已分配）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("采样分配状态（枚举EnumSamplingConfig：0.未分配 1.已分配）")
	private Integer samplingConfig;
    
    /**
    * 采样状态（枚举EnumSamplingStatus：1.不需要取样 2.需要取样还未取样 4.采样中 8.已经完成取样）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("采样状态（枚举EnumSamplingStatus：1.不需要取样 2.需要取样还未取样 4.采样中 8.已经完成取样）")
	private Integer samplingStatus;
    
    /**
    * 领样状态（枚举EnumInnerReceiveStatus：1.不能领取 2.可以领取 6.已经领取 12.已确认领取）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("领样状态（枚举EnumInnerReceiveStatus：1.不能领取 2.可以领取 6.已经领取 12.已确认领取）")
	private Integer innerReceiveStatus;
    
    /**
    * 分析状态（枚举EnumAnalyzeStatus：1.不需要分析 2.不能分析 4.可以分析 8.正在分析 16.分析完成）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("分析状态（枚举EnumAnalyzeStatus：1.不需要分析 2.不能分析 4.可以分析 8.正在分析 16.分析完成）")
	private Integer ananlyzeStatus;
    
    /**
    * 存储状态（枚举EnumStoreStatus：1.不能存储 2.可以存储 4.已经存储 8.可以销毁 16.已经销毁 32.已经被提取）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("存储状态（枚举EnumStoreStatus：1.不能存储 2.可以存储 4.已经存储 8.可以销毁 16.已经销毁 32.已经被提取）")
	private Integer storeStatus;
    
    /**
    * 制样状态（枚举EnumMakeStatus：1.不需要制样 2.需要制样还未制样 6.已经完成制样）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("制样状态（枚举EnumMakeStatus：1.不需要制样 2.需要制样还未制样 6.已经完成制样）")
	private Integer makeStatus;
    
    /**
    * 数据变更状态（ 枚举EnumSampleChangeStatus：0.未变更 1.已变更）（针对已经编制报告的数据修改状态-样品数据增删改）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("数据变更状态（ 枚举EnumSampleChangeStatus：0.未变更 1.已变更）（针对已经编制报告的数据修改状态-样品数据增删改）")
	private Integer dataChangeStatus;

    /**
     * 样品制备状态（ 枚举EnumPreParedStatus：0.未制备 1.已制备）
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("数据变更状态（ 枚举EnumSampleChangeStatus：0.未变更 1.已变更）（针对已经编制报告的数据修改状态-样品数据增删改）")
    private Integer preparedStatus;
    
    /**
    * 客户样品编号
    */
    @Column(length=50)
    @ApiModelProperty("客户样品编号")
    @Length(message = "客户样品编号{validation.message.length}", max = 50)
	private String customerCode;
    
    /**
    * 质控id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("质控id")
	private String qcId;
    
    /**
    * 质控样的原样Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("质控样的原样Id")
	private String associateSampleId;
    
    /**
    * 假删字段
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
	private Boolean isDeleted=false;
    
    /**
    * 父级样品（从父级样品制样而来）
    */
    @Column(length=50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("父级样品（从父级样品制样而来）")
	private String parentSampleId;
    
    /**
    * 制样人id
    */
    @Column(length=50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("制样人id")
	private String makeSamPerId;
    
    /**
    * 是否已打印（枚举EnumPrintStatus：0.未打印1.已打印）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否已打印（枚举EnumPrintStatus：0.未打印1.已打印）")
	private Integer isPrint;
    
    /**
    * 是否留样
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否留样")
	private Boolean isKeep;
    
    /**
    * 样品保留天数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("样品保留天数")
	private Integer keepLongTime;
    
    /**
    * 保存条件
    */
    @ApiModelProperty("保存条件")
	private String storageConditions;
    
    /**
    * 样品过期时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("样品过期时间")
	private Date loseEfficacyTime;
    
    /**
    * 包装/规格
    */
    @Column(length=50)
    @ApiModelProperty("包装/规格")
    @Length(message = "包装/规格{validation.message.length}", max = 50)
	private String pack;
    
    /**
    * 样品重量
    */
    @Column(length=50)
    @ApiModelProperty("样品重量")
    @Length(message = "样品重量{validation.message.length}", max = 50)
	private String sampleWeight;
    
    /**
    * 样品数量
    */
    @Column(length=50)
    @ApiModelProperty("样品数量")
	private String weightOrQuantity;
    
    /**
    * 颜色
    */
    @Column(length=50)
    @ApiModelProperty("颜色")
    @Length(message = "颜色{validation.message.length}", max = 50)
	private String samColor;
    
    /**
    * 样品特征
    */
    @Column(length=1000)
    @ApiModelProperty("样品特征")
    @Length(message = "样品特征{validation.message.length}", max = 1000)
	private String sampleExplain;
    
    /**
    * 样品体积(string)
    */
    @Column(length=50)
    @ApiModelProperty("样品体积(string)")
	private String volume;
    
    /**
    * 采样点位置
    */
    @Column(length=100)
    @ApiModelProperty("采样点位置")
    @Length(message = "采样点位置{validation.message.length}", max = 100)
	private String samplingPlace;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
	private String remark;
    
    /**
    * 样品的性质（枚举EnumSampleKind：0.分析样 1.备样 2.分析后备样）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("样品的性质（枚举EnumSampleKind：0.分析样 1.备样 2.分析后备样）")
	private Integer samKind;
    
    /**
    * 国检_是否合格(检测项目是否有不合格项)
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("国检_是否合格(检测项目是否有不合格项)")
	private Boolean isQualified;
    
    /**
    * 国检_样品来源
    */
    @Column(length=100)
    @ApiModelProperty("国检_样品来源")
    @Length(message = "国检_样品来源{validation.message.length}", max = 100)
	private String sampleSource;
    
    /**
    * 国检_到达实验室状态
    */
    @Column(length=100)
    @ApiModelProperty("国检_到达实验室状态")
    @Length(message = "国检_到达实验室状态{validation.message.length}", max = 100)
	private String originalStatus;
    
    /**
    * 国检_是否需要退还
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("国检_是否需要退还")
	private Boolean isReturned;
    
    /**
    * 前处理情况
    */
    @Column(length=1000)
    @ApiModelProperty("前处理情况")
    @Length(message = "前处理情况{validation.message.length}", max = 1000)
	private String preTreatmentCases;
    
    /**
    * 不合格原因
    */
    @Column(length=1000)
    @ApiModelProperty("不合格原因")
	private String unqualifiedReason;
    
    /**
    * 处理措施
    */
    @Column(length=1000)
    @ApiModelProperty("处理措施")
    @Length(message = "处理措施{validation.message.length}", max = 1000)
	private String disposeMeasure;
    
    /**
    * 验证状态
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("验证状态")
	private Integer consistencyValidStatus;
    
    /**
    * 经度（实际）
    */
    @Column(length=20)
    @ApiModelProperty("经度（实际）")
    @Length(message = "经度（实际）{validation.message.length}", max = 20)
	private String lon;
    
    /**
    * 纬度（实际）
    */
    @Column(length=20)
    @ApiModelProperty("纬度（实际）")
    @Length(message = "纬度（实际）{validation.message.length}", max = 20)
	private String lat;
    
    /**
    * 签到人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("签到人id")
	private String signerId;
    
    /**
    * 签到时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("签到时间")
	private Date signTime;
    
    /**
    * 采样单Id（预留）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样单Id（预留）")
	private String samplingRecordId;
    
    /**
    * 预留分包状态（枚举EnumOutSourcing：0.不分包1.全部分包2.部分分包）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("预留分包状态（枚举EnumOutSourcing：0.不分包1.全部分包2.部分分包）")
	private Integer isOutsourcing;

    /**
     * 盲样类型 EnumSampleBlindType 1.实际水样 2.标样 3.留样复测 非盲样为0
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("盲样类型 EnumSampleBlindType 1.实际水样 2.标样 3.留样复测 非盲样为0")
    private Integer blindType;

    /**
     * 样品质控类型 {@link com.sinoyd.lims.pro.enums.EnumPRO.EnumQMType} 1.标样 2.加标样 3.其他 4.空白样
     * 用于质控任务的样品质控类型标记，没有业务逻辑作用
     */
    @ApiModelProperty("质控任务:样品质控类型 EnumQMType 1.标样 2.加标样 3.其他 4.空白样")
    private Integer sampleQmType;


    /**
     * 最新一次检测单分析数据提交时间
     */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("最新一次检测单分析数据提交时间")
    private Date lastNewSubmitTime;

    /**
     * 排序值
     */
    @ApiModelProperty("排序值")
    //@NotBlank(message = "排序值{validation.message.blank}")
    @Length(message = "排序值{validation.message.length}", max = 50)
    private String sortNum;

    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }