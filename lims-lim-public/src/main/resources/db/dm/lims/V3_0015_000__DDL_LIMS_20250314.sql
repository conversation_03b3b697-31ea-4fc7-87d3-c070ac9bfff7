ALTER TABLE tb_qa_yearlyinnerauditplan add column auditTime2 varchar(1000);
update tb_qa_yearlyinnerauditplan set auditTime2 = auditTime;
ALTER TABLE tb_qa_yearlyinnerauditplan drop column auditTime;
ALTER TABLE tb_qa_yearlyinnerauditplan RENAME COLUMN auditTime2 TO auditTime;
comment on column tb_qa_yearlyinnerauditplan.auditTime is '评审时间';
alter table TB_QA_YearlyManagementReviewPlan add column reviewTime varchar(500);
comment on column TB_QA_YearlyManagementReviewPlan.reviewTime is '评审时间';
alter table TB_QA_PlanInternalAudit add column auditEndTime datetime NOT NULL DEFAULT '1753-01-01 00:00:00';
comment on column TB_QA_PlanInternalAudit.auditEndTime is '审核结束时间';
alter table TB_QA_InternalAuditDivideTheWork add column auditEndTime datetime NOT NULL DEFAULT '1753-01-01 00:00:00';
comment on column TB_QA_InternalAuditDivideTheWork.auditEndTime is '审核结束时间';
