package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.base.entity.SampleType;
import lombok.Data;

import java.util.List;

/**
 * 送样单json字段结构
 * <AUTHOR>
 * @version V1.0.0 2019/11/19
 * @since V100R001
 */
@Data
public class DtoRecordJson {
    /**
     * 样品个数
     */
    private Integer sampleNum = 0;

    /**
     * 检测类型（逗号隔开）
     */
    private String sampleTypeIds;

    /**
     * 实验室检测类型（名称）
     */
    private String labSampleTypes;
}
