package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOtherExpenditure;
import com.sinoyd.lims.lim.dto.customer.DtoOtherExpenditureTotal;
import com.sinoyd.lims.lim.repository.lims.OtherExpenditureRepository;
import com.sinoyd.lims.lim.service.OtherExpenditureService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 非合同支出接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
@Service
public class OtherExpenditureServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoOtherExpenditure, String, OtherExpenditureRepository>
        implements OtherExpenditureService {

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoOtherExpenditure> page, BaseCriteria criteria) {

        // 设置查询的实体类名及别名
        page.setEntityName("DtoOtherExpenditure x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");

        super.findByPage(page, criteria);
    }

    @Override
    public DtoOtherExpenditureTotal getTotalNum(BaseCriteria criteria) {
        DtoOtherExpenditureTotal dto = new DtoOtherExpenditureTotal();
        BigDecimal totalAmount = BigDecimal.ZERO;

        PageBean<DtoOtherExpenditure> pageBean = new PageBean<DtoOtherExpenditure>();
        pageBean.setPageNo(0);
        pageBean.setRowsCount(Integer.MAX_VALUE);
        findByPage(pageBean, criteria);
        List<DtoOtherExpenditure> list = pageBean.getData();
        if (StringUtil.isNotEmpty(list)) {
            totalAmount = list.stream().map(DtoOtherExpenditure::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        dto.setTotalAmount(totalAmount);

        return dto;
    }

    // /**
    //  * 获取非合同支出数据
    //  */
    // @Override
    // public void findByPage(PageBean pb, BaseCriteria baseCriteria) {
    //     pb.setEntityName("OtherExpenditure p");
    //     pb.setSelect("select p");
    //     commonRepository.findByPage(pb, baseCriteria);
    //     List<OtherExpenditure> otherExpenditureList = pb.getData();
    //     BigDecimal totalAmount = BigDecimal.ZERO;
    //     for (OtherExpenditure other : otherExpenditureList) {
    //         totalAmount = totalAmount.add(other.getAmount());

    //     }
    //     for (OtherExpenditure other : otherExpenditureList) {
    //         other.setTotalAmount(totalAmount);
    //     }
    // }
}