package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoReportConfig2Module;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule;

import java.util.List;


/**
 * 报告组件配置
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
public interface ReportConfig2ModuleService extends IBaseJpaService<DtoReportConfig2Module, String> {

    /**
     * 根据报告配置id查询
     *
     * @param recordConfigId 配置id
     * @return 查询结果
     */
    List<DtoReportConfig2Module> queryByRecordConfigId(String recordConfigId);

    /**
     * 查询报告关联的组件的详细信息
     *
     * @param id 报告和组件关联关系id
     * @return 查询结果
     */
    DtoReportModule findModuleInfo(String id);

    /**
     * 批量删除报告组件关联信息
     *
     * @param ids 报告和组件关联关系id列表
     * @return 删除的数量
     */
    int deleteConfig2Module(List<String> ids);

    /**
     * 根据id批量删除报告配置
     *
     * @param ids 报告配置id列表
     * @return 删除的数量
     */
    int deleteRecordConfigForReport(List<String> ids);
}