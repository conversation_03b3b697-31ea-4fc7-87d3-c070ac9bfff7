package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReceiveSubSampleRecord;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.Date;


/**
 * DtoReceiveSubSampleRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ReceiveSubSampleRecord")
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoReceiveSubSampleRecord extends ReceiveSubSampleRecord {
    private static final long serialVersionUID = 1L;

    /**
     * 送样单号
     */
    @Transient
    private String recordCode;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目类型id
     */
    @Transient
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    @Transient
    private String projectTypeName;

    /**
     * 委托方id
     */
    @Transient
    private String customerId;

    /**
     * 委托方名称
     */
    @Transient
    private String customerName;

    /**
     * 送样人id
     */
    @Transient
    private String senderId;

    /**
     * 送样人名称
     */
    @Transient
    private String senderName;

    /**
     * 送样时间
     */
    @Transient
    private Date sendTime;

    /**
     * 采样时间
     */
    @Transient
    private Date samplingTime;

    /**
    * 分配状态
    */
   @Transient
   private String receiveSubstatus;

   /**
    * 登记日期
    */
   @Transient
   private Date inceptTime;

    /**
     * 紧急程度
     */
    @Transient
    private Integer grade;

    /**
     * 是否重点关注
     */
    @Transient
    private Boolean isStress;

    /**
     * 联系人
     */
    @Transient
    private String linkMan;

    /**
     * 联系方式
     */
    @Transient
    private String linkPhone;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeNames;
}