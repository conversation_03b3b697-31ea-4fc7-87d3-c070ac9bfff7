package com.sinoyd.lims.monitor.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.monitor.dto.rcc.DtoWater;

import java.util.Collection;
import java.util.List;


/**
 * Water操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface WaterService extends IBaseJpaService<DtoWater, String> {

     /**
      * 水体管理树状结构转换
      * @return
      */
     List<DtoWater> getTableTree(String key);

    /**
     * 查询所有水体
     *
     * @param waterType 水体类型编码
     * @return 水体列表
     */
    List<DtoWater> findWaterType(String waterType);

    /**
     *  通过ids进行级联删除
     * @param ids
     * @return
     */
    Integer logicDeleteById(Collection<?> ids);

    /**
     *
     * @return
     */
    List<DtoWater> findWaterList();

}