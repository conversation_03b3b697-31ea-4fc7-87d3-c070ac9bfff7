package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.StatusForRecordService;
import com.sinoyd.lims.pro.criteria.StatusForRecordCriteria;
import com.sinoyd.lims.pro.dto.DtoStatusForRecord;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * StatusForRecord服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
 @Api(tags = "示例: StatusForRecord服务")
 @RestController
 @RequestMapping("api/pro/statusForRecord")
 public class StatusForRecordController extends BaseJpaController<DtoStatusForRecord, String,StatusForRecordService> {


    /**
     * 分页动态条件查询StatusForRecord
     * @param statusForRecordCriteria 条件参数
     * @return RestResponse<List<StatusForRecord>>
     */
     @ApiOperation(value = "分页动态条件查询StatusForRecord", notes = "分页动态条件查询StatusForRecord")
     @GetMapping
     public RestResponse<List<DtoStatusForRecord>> findByPage(StatusForRecordCriteria statusForRecordCriteria) {
         PageBean<DtoStatusForRecord> pageBean = super.getPageBean();
         RestResponse<List<DtoStatusForRecord>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, statusForRecordCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询StatusForRecord
     * @param id 主键id
     * @return RestResponse<DtoStatusForRecord>
     */
     @ApiOperation(value = "按主键查询StatusForRecord", notes = "按主键查询StatusForRecord")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoStatusForRecord> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoStatusForRecord> restResponse = new RestResponse<>();
         DtoStatusForRecord statusForRecord = service.findOne(id);
         restResponse.setData(statusForRecord);
         restResponse.setRestStatus(StringUtil.isNull(statusForRecord) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增StatusForRecord
     * @param statusForRecord 实体列表
     * @return RestResponse<DtoStatusForRecord>
     */
     @ApiOperation(value = "新增StatusForRecord", notes = "新增StatusForRecord")
     @PostMapping
     public RestResponse<DtoStatusForRecord> create(@RequestBody DtoStatusForRecord statusForRecord) {
         RestResponse<DtoStatusForRecord> restResponse = new RestResponse<>();
         restResponse.setData(service.save(statusForRecord));
         return restResponse;
      }

     /**
     * 新增StatusForRecord
     * @param statusForRecord 实体列表
     * @return RestResponse<DtoStatusForRecord>
     */
     @ApiOperation(value = "修改StatusForRecord", notes = "修改StatusForRecord")
     @PutMapping
     public RestResponse<DtoStatusForRecord> update(@RequestBody DtoStatusForRecord statusForRecord) {
         RestResponse<DtoStatusForRecord> restResponse = new RestResponse<>();
         restResponse.setData(service.update(statusForRecord));
         return restResponse;
      }

    /**
     * "根据id批量删除StatusForRecord
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除StatusForRecord", notes = "根据id批量删除StatusForRecord")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }