package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoArrange2Method;
import com.sinoyd.lims.pro.repository.Arrange2MethodRepository;
import com.sinoyd.lims.pro.service.Arrange2MethodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采样方法接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/7/25
 * @since V100R001
 */
@Service
@Slf4j
public class Arrange2MethodServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoArrange2Method, String, Arrange2MethodRepository> implements Arrange2MethodService {

    private SampleTypeRepository sampleTypeRepository;

    @Override
    public List<DtoArrange2Method> findBySamplingPlanId(String samplingPlanId) {
        List<DtoArrange2Method> result = repository.findBySamplingPlanId(samplingPlanId);
        loadFields(result);
        return result;
    }

    private void loadFields(List<DtoArrange2Method> methods) {
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll();
        methods.forEach(method -> {
            method.setSampleTypeName(sampleTypes.stream().filter(st -> st.getId().equals(method.getSampleTypeId())).map(DtoSampleType::getTypeName).findFirst().orElse(""));
        });
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }
}
