package com.sinoyd.lims.pro.service.impl;

import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.repository.OATaskRelationRepository;
import com.sinoyd.lims.pro.service.OATaskRelationService;

import org.springframework.stereotype.Service;

/**
 * 审批任务关联业务业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@Service
public class OATaskRelationServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoOATaskRelation, String, OATaskRelationRepository>
        implements OATaskRelationService {
    @Override
    public DtoOATaskRelation findByTaskId(String taskId) {
        List<DtoOATaskRelation> relations = findListByTaskId(taskId);

        return StringUtil.isEmpty(relations) ? null : relations.get(0);
    }

    @Override
    public List<DtoOATaskRelation> findListByTaskId(String taskId) {
        return super.findByProperty(DtoOATaskRelation.class, "taskId", taskId);
    }
}
