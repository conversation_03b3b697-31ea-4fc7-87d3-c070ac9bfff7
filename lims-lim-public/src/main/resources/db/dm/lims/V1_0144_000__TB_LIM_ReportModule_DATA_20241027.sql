-- 废水比对报告组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('1471ad7d-70bf-45aa-8279-feb2ae6f62b7', 'dtBdWaterZkTable', '废水比对报告质控样数据表组件', 'dtBdWaterZkTable', 'dtBdZkSrc',
        0, 0, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-10-27 10:40:40', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-10-27 10:40:40', '0', '0', 0, 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('47e6134f-08be-4bd4-a60c-5554dfd1277f', 'dtBdWaterFixedTable', '废水比对报告固定标准依据表', 'dtBdWaterFixedTable', '', 0, 0,
        '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:44:47',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:44:47', '0', '0',
        0, 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('8b3fd405-8628-45cb-b2b4-b370d1f03080', 'dtBdWaterHeadTable', '废水比对报告表头组件', 'dtBdWaterHeadTable', '', 0, 0, '',
        0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:44:07',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:44:07', '0', '0',
        0, 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('a3146f56-677b-47d3-9418-a33c47d288c7', 'dtBdWaterTable', '废水比对报告实际水样测定检测数据表组件', 'dtBdWaterTable', 'dtBdSrc', 0,
        0, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:40:01',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:40:01', '0', '0',
        0, 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('b4159593-8252-4f2f-b45d-d7828815e1e5', 'waterBdDataSource', '废水比对报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtBdWaterHeadTable\", \"dtBdWaterTable\", \"dtBdWaterZkTable\", \"dtBdWaterCriterionTable\", \"dtBdWaterFixedTable\"]', 1,
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:50:23',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:50:23', '0', '0',
        0, 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('e6706ab0-56b8-48b5-badd-602bc4aaea0b', 'dtBdWaterCriterionTable', '废水比对报告技术说明数据表组件', 'dtBdWaterCriterionTable',
        'dtCriterionBdSrc', 0, 0, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-10-27 10:41:32', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-10-27 10:41:32', '0', '0', 0, 0, 0, 0);

update TB_LIM_ReportModule
set sonTableJson = '["normalWaterStdDataSource", "groundWaterStdDataSource", "groundWaterStdDataSource", "orgToStdDataSource", "unOrgWeaToStdDataSource", "solidStdDataSource", "noiseDayNightDataSource", "soilStdDataSource", "waterBdDataSource"]'
where moduleCode = 'comprehensiveStdDataSource';