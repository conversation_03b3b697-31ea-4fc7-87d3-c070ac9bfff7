package com.sinoyd.lims.lim.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 导入yml配置对应实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2024/11/01
 */
@Data
@Component
@ConfigurationProperties(prefix = "import")
public class ImportConfig {

    @Value("${import.allow-date-format:yyyy-MM-dd}")
    private String allowDateFormat;
}
