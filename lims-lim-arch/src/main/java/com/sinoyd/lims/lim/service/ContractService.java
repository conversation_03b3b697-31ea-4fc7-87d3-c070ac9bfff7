package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoContract;

/**
 * 合同管理
 * <AUTHOR>
 * @version V1.0.0 2019/2/25
 * @since V100R001
 */
public interface ContractService extends IBaseJpaService<DtoContract, String> {
    /**
     * 重新定义接口（为了返回调用该接口类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回合同对象
     */
    @Override
    DtoContract findOne(String id);


    /**
     * 修改合同的状态
     *
     * @param id     合同id
     * @param status 状态
     * @return 返回修改的行数
     */
    Integer updateContractStatus(String id, Integer status);
}