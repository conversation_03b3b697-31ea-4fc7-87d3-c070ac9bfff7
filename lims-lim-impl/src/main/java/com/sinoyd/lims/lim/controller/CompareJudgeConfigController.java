package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.CompareJudgeConfigCriteria;
import com.sinoyd.lims.lim.service.CompareJudgeConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CompareJudgeConfig服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/13
 * @since V100R001
 */
@Api(tags = "示例: CompareJudgeConfig服务")
@RestController
@RequestMapping("api/lim/compareJudgeConfig")
@Validated
public class CompareJudgeConfigController extends BaseJpaController<DtoQualityControlLimit, String, CompareJudgeConfigService> {

    /**
     * 分页动态条件查询DtoCompareJudge
     *
     * @param criteria 条件参数
     * @return RestResponse<List <DtoCompareJudge>>
     */
    @ApiOperation(value = "分页动态条件查询DtoCompareJudge", notes = "分页动态条件查询DtoCompareJudge")
    @GetMapping
    public RestResponse<List<DtoQualityControlLimit>> findByPage(CompareJudgeConfigCriteria criteria) {
        PageBean<DtoQualityControlLimit> pageBean = super.getPageBean();
        RestResponse<List<DtoQualityControlLimit>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 保存
     *
     * @param qualityControlLimit 实体
     * @return RestResponse<compareJudgeConfig>
     */
    @ApiOperation(value = "保存DtoCompareJudge", notes = "保存DtoCompareJudge")
    @PostMapping
    public RestResponse<DtoQualityControlLimit> save(@Validated @RequestBody DtoQualityControlLimit qualityControlLimit) {
        RestResponse<DtoQualityControlLimit> response = new RestResponse<>();
        response.setData(service.save(qualityControlLimit));
        return response;
    }

    /**
     * 更新
     *
     * @param compareJudgeConfig 实体
     * @return RestResponse<DtoCompareJudge>
     */
    @ApiOperation(value = "更新DtoCompareJudge", notes = "更新DtoCompareJudge")
    @PutMapping
    public RestResponse<DtoQualityControlLimit> update(@RequestBody DtoQualityControlLimit compareJudgeConfig) {
        RestResponse<DtoQualityControlLimit> response = new RestResponse<>();
        response.setData(service.update(compareJudgeConfig));
        return response;
    }

    /**
     * 根据id批量删除
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

}
