package com.sinoyd.lims.pro.dto;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.entity.LogForOrderForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * DtoLogForOrderForm实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_LogForOrderForm")
@Data
@DynamicInsert
public class DtoLogForOrderForm extends LogForOrderForm {

    private static final long serialVersionUID = 1L;

    /**
     * 日志类型名称
     */
    @Transient
    private String logTypeName;

    /**
     * 对象类型名称
     */
    @Transient
    private String objectTypeName;

    public DtoLogForOrderForm() {
    }

    public DtoLogForOrderForm(String operateInfo, String objectId, Integer logType, Integer objectType, String comment,
                              String opinion, String remark) {
        setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
        setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
        setOperateTime(new Date());
        setOperateInfo(operateInfo);
        setObjectId(objectId);
        setObjectType(objectType);
        setLogType(logType);
        setComment(comment);
        setOpinion(opinion);
        setRemark(remark);
        setNextOperatorId(UUIDHelper.GUID_EMPTY);
        setNextOperatorName("");
    }

}
