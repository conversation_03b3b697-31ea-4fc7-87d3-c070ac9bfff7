package com.sinoyd.lims.instrument.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.instrument.parse.dto.DtoParseFileApp;
import com.sinoyd.lims.instrument.parse.repository.ParseFileAppRepository;
import com.sinoyd.lims.instrument.parse.service.ParseFileAppService;
import dm.jdbc.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;


/**
 * FileApp操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/11/22
 * @since V100R001
 */
 @Service
 @Slf4j
public class ParseFileAppServiceImpl extends BaseJpaServiceImpl<DtoParseFileApp,String, ParseFileAppRepository> implements ParseFileAppService {

     @Value("${fileProps.instrumentParseFilePath}")
     private String filePath;

    @Override
    public Boolean upload(String appId, HttpServletRequest request) {
        if (StringUtil.isEmpty(appId)) {
            throw new BaseException("应用配置丢失,请刷新数据");
        }
        DtoParseFileApp app = repository.findOne(appId);
        if (StringUtil.isNull(app)) {
           throw new BaseException("应用配置丢失,请刷新数据");
        }
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("files");
        String dirPath = app.getFolderName();
        if (StringUtil.isEmpty(dirPath)) {
            throw new BaseException("文档路径配置错误");
        }
        // 去除前后/
        dirPath = this.suitFolder(dirPath);
        File fileDir = new File(filePath + "/" + dirPath);
        if (!fileDir.exists()) {
            fileDir.mkdirs();
        }
        for (MultipartFile file : files) {
            InputStream inputStream = null;
            Path path = Paths.get(filePath + "/" + dirPath + "/"
                    + file.getOriginalFilename());
            try {
                Files.deleteIfExists(path);
                inputStream = file.getInputStream();
                Files.copy(inputStream, path);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new BaseException("上传失败");
            }finally {
                FileUtil.close(inputStream);
            }
        }
        return true;
    }

    /**
     * 去除前后/
     * @param folder 路径
     * @return 路径
     */
    private String suitFolder(String folder) {
        if (StringUtil.isEmpty(folder)) {
            return null;
        }
        if (folder.startsWith("/")) {
            folder = folder.substring(folder.indexOf("/") + 1);
        }
        if (folder.endsWith("/")) {
            folder = folder.substring(0, folder.lastIndexOf("/"));
        }
        return folder;
    }
}