-- 新增无组织报告气象条件（带采样时间）组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('81f8f90b-21a8-415a-966a-c944c3cc5321', 'dtUnOrgWeaTimeStdTable', '标准版无组织气象条件表组件（批次，带采样时间）',
        'dtUnOrgWeaTimeStdTable', 'dtUnOrgWeaTimeSource', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-24 14:18:26', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-02-24 14:18:26', '1', '0', 0, 0, 0, 1);

-- 新增无组织报告检测结果表组件（批次，带最大值）
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('1031efec-51b1-4aec-9d57-5f5caf784b50', 'dtUnOrgToMaxStdTable', '标准版无组织检测结果表组件（批次，带最大值）',
        'dtUnOrgToMaxStdTable', 'dtUnOrgToMaxSource', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-24 14:25:59', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2025-02-24 14:25:59', '1', '0', 0, 0, 0, 1);

-- 新增无组织报告气象条件数据主表（批次，气象条件带采样时间）组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('d01ae140-94d8-4859-957f-c0a9128251a4', 'unOrgWeaTimeStdDataSource', '标准版无组织报告气象条件数据主表（批次，气象条件带采样时间）',
        'dtUnOrgWeaDataSource', '', 0, 0, '[\"dtUnOrgWeaHeadStdTable\", \"dtUnOrgWeaTimeStdTable\"]', b'1',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-02-24 14:47:03',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-02-24 14:47:03', '1', '0',
        0, 0, 0, 1);

-- 新增无组织报告样品数据主表（批次 ，样品表带最大值）组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('2c1ffbe8-5a27-4ba1-bc76-16974ccaa38d', 'unOrgToMaxStdDataSource', '标准版无组织报告样品数据主表（批次 ，样品表带最大值）',
        'dtUnOrgToDataSource', '', 0, 0,
        '[\"dtUnOrgToHeadStdTable\", \"dtUnOrgToMaxStdTable\", \"dtUnOrgToCpdStdTable\", \"dtUnOrgToKbStdTable\"]',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-02-24 14:54:27',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-02-24 14:54:27', '1', '0',
        0, 0, 0, 1);

-- 新增无组织报告检测数据主表（批次，气象条件带采样时间，样品表带最大值）组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode, compoundAvgCalculationMode,
                                               gasParamSplitMode)
VALUES ('67a0d903-165f-4fa4-ac79-236e14d2c7b5', 'unOrgWeaToTimeMaxStdDataSource',
        '标准版无组织报告检测数据主表（批次，气象条件带采样时间，样品表带最大值）', 'dtDataSource', '', 0, 0,
        '[\"unOrgWeaTimeStdDataSource\", \"unOrgToMaxStdDataSource\",  \"outParallelStdDataSource\"]', b'1',
        '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-02-24 14:56:43',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2025-02-24 14:56:43', '1', '0',
        0, 0, 0, 1);
