package com.sinoyd.base.service;

import com.sinoyd.base.dto.customer.DtoWorkflow;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;

import java.util.Map;

/**
 * WorkflowService 主要是提供公用的工作流服务对象
 * <AUTHOR>
 * @version V1.0.0 2019/10/30
 * @since V100R001
 */
public interface WorkflowService {

    /**
     * 分页查询相应的数据
     *
     * @param pageBean     分页查询
     * @param baseCriteria 查询条件
     */
    void findByPage(PageBean<DtoWorkflow> pageBean, BaseCriteria baseCriteria);

    /**
     * 提交工作流的信号信息
     *
     * @param dtoWorkflowSign 工作流信息
     */
    String submitSign(DtoWorkflowSign dtoWorkflowSign) throws Exception;


    /**
     * 创建工作流的实例
     *
     * @param workflowCode 工作流编号
     * @param objectId     对象id
     */
    void createInstance(String workflowCode, String objectId);

    /**
     * 终止实例
     *
     * @param objectId 对象id
     * @param option   原因
     */
    void endInstance(String objectId, String option);

    /**
     * 获取当前流程实例的任务
     *
     * @param procInstId 流程实例
     * @return 返回任务信息
     */
    Map<String, Object> getCurrentTask(String procInstId);


    /**
     * 获取当前使用流程最新的配置信息
     *
     * @param proDefKey 流程编码
     * @return 返回最新的配置信息
     */
    Map<String, Object> getProcessDefinition(String proDefKey);
}

