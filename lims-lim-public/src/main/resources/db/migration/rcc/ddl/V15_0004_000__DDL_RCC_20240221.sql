-- tb_base_analyzeitem
drop trigger if exists after_insert_analyzeitem;
DELIMITER $$
CREATE TRIGGER after_insert_analyzeitem
    AFTER INSERT
    ON tb_base_analyzeitem
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 1, null, null, null, new.orgId,
            new.domainId);
end $$
DELIMITER ;

drop trigger if exists after_update_analyzeitem;
DELIMITER $$
CREATE TRIGGER after_update_analyzeitem
    AFTER UPDATE
    ON tb_base_analyzeitem
    FOR EACH ROW
BEGIN
    IF new.isDeleted = 1 THEN
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
                new.domainId);
    else
        if new.analyzeItemName != old.analyzeItemName or
           (new.analyzeItemName is null and old.analyzeItemName is not null) or
           (new.analyzeItemName is not null and old.analyzeItemName is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'analyzeItemName',
                    old.analyzeItemName, new.analyzeItemName, new.orgId, new.domainId);
        END IF;
        if
                new.analyzeItemCode != old.analyzeItemCode or
                (new.analyzeItemCode is null and old.analyzeItemCode is not null) or
                (new.analyzeItemCode is not null and old.analyzeItemCode is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'analyzeItemCode',
                    old.analyzeItemCode, new.analyzeItemCode, new.orgId, new.domainId);
        END IF;
        if
                new.variableName != old.variableName or (new.variableName is null and old.variableName is not null) or
                (new.variableName is not null and old.variableName is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'variableName',
                    old.variableName, new.variableName, new.orgId, new.domainId);
        END IF;
        if
                new.pinYin != old.pinYin or (new.pinYin is null and old.pinYin is not null) or
                (new.pinYin is not null and old.pinYin is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'pinYin', old.pinYin,
                    new.pinYin, new.orgId, new.domainId);
        END IF;
        if
                new.fullPinYin != old.fullPinYin or (new.fullPinYin is null and old.fullPinYin is not null) or
                (new.fullPinYin is not null and old.fullPinYin is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'fullPinYin', old.fullPinYin,
                    new.fullPinYin, new.orgId, new.domainId);
        END IF;
        if
                new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
                (new.orderNum is not null and old.orderNum is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'orderNum', old.orderNum,
                    new.orderNum, new.orgId, new.domainId);
        END IF;
        if
                new.casNum != old.casNum or (new.casNum is null and old.casNum is not null) or
                (new.casNum is not null and old.casNum is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'casNum', old.casNum,
                    new.casNum, new.orgId, new.domainId);
        END IF;
    END IF;
end $$
DELIMITER ;

-- tb_lim_analyzemethod
drop trigger if exists after_insert_analyzemethod;
DELIMITER $$
CREATE TRIGGER after_insert_analyzemethod
    AFTER INSERT
    ON tb_lim_analyzemethod
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 1, null, null, null, new.orgId,
            new.domainId);
end $$
DELIMITER ;

drop trigger if exists after_update_analyzemethod;
DELIMITER $$
CREATE TRIGGER after_update_analyzemethod
    AFTER UPDATE
    ON tb_lim_analyzemethod
    FOR EACH ROW
BEGIN
    IF new.isDeleted = 1 THEN
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
                new.domainId);
    else
        if new.methodName != old.methodName or (new.methodName is null and old.methodName is not null) or
           (new.methodName is not null and old.methodName is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'methodName',
                    old.methodName, new.methodName, new.orgId, new.domainId);
        END IF;
        if
                new.countryStandard != old.countryStandard or
                (new.countryStandard is null and old.countryStandard is not null) or
                (new.countryStandard is not null and old.countryStandard is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'countryStandard',
                    old.countryStandard, new.countryStandard, new.orgId, new.domainId);
        END IF;
        if
                new.effectiveDate != old.effectiveDate or
                (new.effectiveDate is null and old.effectiveDate is not null) or
                (new.effectiveDate is not null and old.effectiveDate is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'effectiveDate',
                    old.effectiveDate, new.effectiveDate, new.orgId, new.domainId);
        END IF;
        if
                new.methodCode != old.methodCode or (new.methodCode is null and old.methodCode is not null) or
                (new.methodCode is not null and old.methodCode is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'methodCode',
                    old.methodCode, new.methodCode, new.orgId, new.domainId);
        END IF;
        if
                new.isCompleteTogether != old.isCompleteTogether or
                (new.isCompleteTogether is null and old.isCompleteTogether is not null) or
                (new.isCompleteTogether is not null and old.isCompleteTogether is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'isCompleteTogether',
                    old.isCompleteTogether, new.isCompleteTogether, new.orgId, new.domainId);
        END IF;
        if
                new.isControlled != old.isControlled or (new.isControlled is null and old.isControlled is not null) or
                (new.isControlled is not null and old.isControlled is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'isControlled',
                    old.isControlled, new.isControlled, new.orgId, new.domainId);
        END IF;
        if
                new.remark != old.remark or (new.remark is null and old.remark is not null) or
                (new.remark is not null and old.remark is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'remark', old.remark,
                    new.remark, new.orgId, new.domainId);
        END IF;
        if
                new.parentId != old.parentId or (new.parentId is null and old.parentId is not null) or
                (new.parentId is not null and old.parentId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'parentId', old.parentId,
                    new.parentId, new.orgId, new.domainId);
        END IF;
        if
                new.countryStandardName != old.countryStandardName or
                (new.countryStandardName is null and old.countryStandardName is not null) or
                (new.countryStandardName is not null and old.countryStandardName is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'countryStandardName',
                    old.countryStandardName, new.countryStandardName, new.orgId, new.domainId);
        END IF;
        if
                new.yearSn != old.yearSn or (new.yearSn is null and old.yearSn is not null) or
                (new.yearSn is not null and old.yearSn is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'yearSn', old.yearSn,
                    new.yearSn, new.orgId, new.domainId);
        END IF;
        if
                new.effectiveDays != old.effectiveDays or
                (new.effectiveDays is null and old.effectiveDays is not null) or
                (new.effectiveDays is not null and old.effectiveDays is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'effectiveDays',
                    old.effectiveDays, new.effectiveDays, new.orgId, new.domainId);
        END IF;
        if
                new.warningDays != old.warningDays or (new.warningDays is null and old.warningDays is not null) or
                (new.warningDays is not null and old.warningDays is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'warningDays',
                    old.warningDays, new.warningDays, new.orgId, new.domainId);
        END IF;
        if
                new.isInforce != old.isInforce or (new.isInforce is null and old.isInforce is not null) or
                (new.isInforce is not null and old.isInforce is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'isInforce', old.isInforce,
                    new.isInforce, new.orgId, new.domainId);
        END IF;
        if
                new.isInputBySample != old.isInputBySample or
                (new.isInputBySample is null and old.isInputBySample is not null) or
                (new.isInputBySample is not null and old.isInputBySample is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'isInputBySample',
                    old.isInputBySample, new.isInputBySample, new.orgId, new.domainId);
        END IF;
        if
                new.alias != old.alias or (new.alias is null and old.alias is not null) or
                (new.alias is not null and old.alias is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'alias', old.alias,
                    new.alias, new.orgId, new.domainId);
        END IF;
        if
                new.isCrossDay != old.isCrossDay or (new.isCrossDay is null and old.isCrossDay is not null) or
                (new.isCrossDay is not null and old.isCrossDay is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'isCrossDay',
                    old.isCrossDay, new.isCrossDay, new.orgId, new.domainId);
        END IF;
        if
                new.isPreparation != old.isPreparation or
                (new.isPreparation is null and old.isPreparation is not null) or
                (new.isPreparation is not null and old.isPreparation is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'isPreparation',
                    old.isPreparation, new.isPreparation, new.orgId, new.domainId);
        END IF;
        if
                new.preparedMethod != old.preparedMethod or
                (new.preparedMethod is null and old.preparedMethod is not null) or
                (new.preparedMethod is not null and old.preparedMethod is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'preparedMethod',
                    old.preparedMethod, new.preparedMethod, new.orgId, new.domainId);
        END IF;
        if
                new.status != old.status or (new.status is null and old.status is not null) or
                (new.status is not null and old.status is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_analyzemethod', new.id, new.modifier, new.modifyDate, 3, 'status', old.status,
                    new.status, new.orgId, new.domainId);
        END IF;
    END IF;
end $$
DELIMITER ;