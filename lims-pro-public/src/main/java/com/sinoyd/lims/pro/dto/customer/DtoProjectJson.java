package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目json字段结构
 * <AUTHOR>
 * @version V1.0.0 2019/11/16
 * @since V100R001
 */
@Data
public class DtoProjectJson {

    /**
     * 合同编号
     */
    private String contractCode = "";

    /**
     * 收款状态
     */
    private Integer collectionStatus;

    /**
     * 收款进度（已收款，坏账，总金额）
     */
    private BigDecimal[] collectionDetail;

    /**
     * 未采样品数
     */
    private Integer notSampled = 0;

    /**
     * 检测情况 检毕样品数/总样品数
     */
    private String analyzeSummary = "0/0";

    /**
     * 检测进度（已出证样品数，已检毕样品数，未检毕样品数）
     */
    private Integer[] analyzeDetail = new Integer[]{0, 0, 0};

    /**
     * 报告进度（已签发报告数、实际报告数）
     */
    private Integer[] reportDetail = new Integer[]{0, 0};

    /**
     * 项目报告变更状态
     */
    private Integer dataChangeStatus = EnumPRO.EnumProjectChangeStatus.未变更.getValue();
}
