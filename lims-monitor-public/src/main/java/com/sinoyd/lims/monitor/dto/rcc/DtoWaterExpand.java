package com.sinoyd.lims.monitor.dto.rcc;

import com.sinoyd.lims.monitor.entity.WaterExpand;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoWaterExpand实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_WaterExpand")
@Data
@DynamicInsert
public class DtoWaterExpand extends WaterExpand {
    private static final long serialVersionUID = 1L;

}