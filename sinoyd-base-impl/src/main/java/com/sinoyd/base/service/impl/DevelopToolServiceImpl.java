package com.sinoyd.base.service.impl;

import com.sinoyd.base.service.DevelopToolService;
import com.sinoyd.boot.common.exception.BaseException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 开发工具实现
 * 注意：该接口和业务无任何关系，只是用来提升开发效率
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/11
 */
@Service
@Slf4j
public class DevelopToolServiceImpl implements DevelopToolService {

    private JdbcTemplate jdbcTemplate;

    @Override
    public String generateApifoxObjectModel(Map<String, String> params) {
        try {
            String modelName = params.get("modelName");
            String tableName = params.get("tableName");
            DatabaseMetaData dbMetaData = Objects.requireNonNull(jdbcTemplate.getDataSource()).getConnection().getMetaData();
            String[] types = {"TABLE"};
            ResultSet tableRs = dbMetaData.getTables(null, null, tableName, types);
            List<FieldVO> voList = new ArrayList<>();
            while (tableRs.next()) {
                tableName = tableRs.getString("TABLE_NAME");
                String schemaName = tableRs.getString("TABLE_CAT");
                ResultSet columnRs = dbMetaData.getColumns(null, schemaName, tableName, null);
                while (columnRs.next()) {
                    String columnName = columnRs.getString("COLUMN_NAME");
                    String columnType = columnRs.getString("TYPE_NAME");
                    String columnRemarks = columnRs.getString("REMARKS");
                    if (!voList.parallelStream().map(FieldVO::getFieldName).collect(Collectors.toList()).contains(columnName)) {
                        voList.add(generateFieldObject(columnName, columnType, columnRemarks));
                    }
                }
            }
            return generateJson(modelName, voList);
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            throw new BaseException("生成Apifox数据模型出错");
        }
    }

    /**
     * 产生模型语句
     *
     * @param modelName 模型名称
     * @param voList    模型属性集合
     * @return 模型语句
     */
    private String generateJson(String modelName, List<FieldVO> voList) {
        StringBuilder sb = new StringBuilder();
        sb.append("{")
                .append("\"type\": \"object\",");

        sb.append("\"properties\": {");
        List<String> columnJsonList = voList.stream().map(FieldVO::toString).collect(Collectors.toList());
        sb.append(String.join(",", columnJsonList));
        sb.append("},");

        List<String> columnNameList = voList.stream().map(FieldVO::fieldWithDoubleQuote).collect(Collectors.toList());
        sb.append("\"x-apifox-orders\": [")
                .append(String.join(",", columnNameList))
                .append("],");

        sb.append("\"title\": \"").append(modelName).append("\",");

        sb.append(" \"required\": [")
                .append(String.join(",", columnNameList))
                .append("]");

        sb.append("}");
        return sb.toString();
    }

    /**
     * 将数据库字段类型转换成Apifox中的接口属性类型
     *
     * @param columnType 数据库字段类型
     * @return 接口属性类型
     */
    private FieldVO generateFieldObject(String columnName, String columnType, String columnRemarks) {
        FieldVO vo = new FieldVO();
        vo.setFieldName(columnName);
        vo.setFieldDesc(columnRemarks);
        if (columnType.startsWith("VARCHAR") || columnType.startsWith("NVARCHAR")) {
            vo.setFieldType("string");
            if(columnName.endsWith("id")){
                vo.setMockValue("@guid");
            }else {
                vo.setMockValue("@word");
            }
        } else if (columnType.startsWith("INT")) {
            vo.setFieldType("integer");
            vo.setMockValue("@integer(60, 100)");
        } else if (columnType.startsWith("BIT")) {
            vo.setFieldType("boolean");
            vo.setMockValue("@boolean");
        } else if (columnType.startsWith("DECIMAL")) {
            vo.setFieldType("number");
            vo.setMockValue("@float(60, 100, 3,2)");
        } else if ("DATE".equals(columnType)) {
            vo.setFieldType("string");
            vo.setMockValue("@date");
        } else if ("DATETIME".equals(columnType)) {
            vo.setFieldType("string");
            vo.setMockValue("@datetime");
        } else if (columnType.endsWith("TEXT")) {
            vo.setFieldType("string");
            vo.setMockValue("@csentence");
        } else {
            throw new BaseException("未知类型");
        }
        return vo;
    }

    @Data
    class FieldVO {
        /**
         * 模型属性类型
         */
        private String fieldType;

        /**
         * 属性名称
         */
        private String fieldName;

        /**
         * 属性模拟值
         */
        private String mockValue;

        /**
         * 属性描述
         */
        private String fieldDesc;

        public String toString() {
            return "\"" + this.fieldName + "\": {"
                    + "\"type\": \"" + this.fieldType + "\","
                    + "\"mock\": {\"mock\":\"" + this.mockValue + "\"},"
                    + "\"description\": \"" + this.fieldDesc + "\"}";
        }

        public String fieldWithDoubleQuote() {
            return "\"" + this.fieldName + "\"";
        }

    }


    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}