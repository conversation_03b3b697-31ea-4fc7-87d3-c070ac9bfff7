package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 分析人员工作分析状态
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
 @Data
 public  class DtoAnalyzeStatus {
    
    // 待检测样品数量
    private Integer awaitSample;

    // 检测中检测单数量
    private Integer awaitWorkSheet;
    
    // 待审核检测单数量
    private Integer auditWorkSheet;

    // 已完成检测单数量
    private Integer finishWorkSheet;
 }