-- 仪器接入表
CREATE TABLE tb_lim_InstrumentGather
(
    id           varchar(50) NOT NULL COMMENT 'id',
    instrumentId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '仪器id',
    mnNumber     varchar(50) NOT NULL DEFAULT '' COMMENT 'mn号',
    refresh      int(11) NOT NULL DEFAULT '5' COMMENT '刷新间隔(秒)',
    isDeleted    bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删字段',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='仪器接入表';


-- 仪器接入参数配置表
CREATE TABLE tb_lim_InstrumentGatherParams
(
    id                 varchar(50) NOT NULL COMMENT 'id',
    instrumentGatherId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '仪器接入表id',
    paramName          varchar(100)         DEFAULT NULL COMMENT '参数名称',
    paramLabel         varchar(100)         DEFAULT NULL COMMENT '参数标识',
    dataType           varchar(10) NOT NULL COMMENT '数据类型枚举EnumInstrumentGatherDataType G:工况参数、CN2083:结果参数、Channel: 通道',
    isEnum             bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否枚举',
    enumDataSource           varchar(255)         DEFAULT NULL COMMENT '枚举数据源',
    channelNum         int (10) DEFAULT NULL COMMENT '通道数量',
    dimension          varchar(20)          DEFAULT NULL COMMENT '量纲',
    orderNum           int(11) NOT NULL DEFAULT '0' COMMENT '排序值',
    remark             varchar(255)         DEFAULT NULL COMMENT '备注',
    isDeleted          bit(1)      NOT NULL DEFAULT b'0' COMMENT '假删字段',
    orgId              varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator            varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId           varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier           varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='仪器接入参数表';


-- 仪器接入数据表
CREATE TABLE tb_lim_InstrumentGatherData
(
    id                 varchar(50) NOT NULL COMMENT 'id',
    instrumentGatherId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '仪器接入表id',
    dataType           varchar(10) NOT NULL COMMENT '数据类型枚举EnumInstrumentGatherDataType G:工况参数、CN2083:结果参数',
    uploadTime         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='仪器接入数据表';


-- 仪器接入数据详情表
CREATE TABLE tb_lim_InstrumentGatherDataDetails
(
    id                       varchar(50) NOT NULL COMMENT 'id',
    instrumentGatherDataId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '参数收集id',
    InstrumentGatherParamsId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '仪器接入参数id',
    parmaName                varchar(100)         DEFAULT NULL COMMENT '参数名称',
    paramValue               varchar(255)         DEFAULT NULL COMMENT '参数值',
    dimension                varchar(20)          DEFAULT NULL COMMENT '量纲',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='仪器接入数据详情表';