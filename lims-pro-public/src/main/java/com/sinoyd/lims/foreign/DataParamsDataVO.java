package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * 参数信息
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Data
public class DataParamsDataVO {
    /**
     * id
     */
    private String id;

    /**
     * 关联id
     */
    private String objectId;

    /**
     * 参数名称
     */
    private String paramsConfigName;

    /**
     * 参数数据值
     */
    private String paramsValue;

    /**
     * 数据源
     */
    private String dataSource;

    /**
     * 单位名称
     */
    private String dimension;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
