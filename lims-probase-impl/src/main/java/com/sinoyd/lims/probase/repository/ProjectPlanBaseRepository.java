package com.sinoyd.lims.probase.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoProjectPlan;

public interface ProjectPlanBaseRepository extends IBaseJpaPhysicalDeleteRepository<DtoProjectPlan, String> {
    /**
     * 根据项目id查询项目计划
     *
     * @param projectId 项目id
     * @return 返回相应的项目计划
     */
    DtoProjectPlan findByProjectId(String projectId);
}
