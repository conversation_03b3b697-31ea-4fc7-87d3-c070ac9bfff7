package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportPerson;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.boot.frame.sys.service.IOrgService;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.*;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.RoleInfoServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoImportPersonExtend;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.ImportPersonService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.PersonVerifyHandle;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 人员导入实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2022/04/08
 * @since V100R001
 */
@Service
public class ImportPersonServiceImpl implements ImportPersonService {

    private PersonRepository personRepository;

    private PersonService personService;

    private DepartmentService departmentService;

    private CodeService codeService;

    private ImportUtils importUtils;

    private RoleInfoServiceImpl roleInfoService;

    private IOrgService orgService;

    private UserService userService;

    /**
     * 人员导入
     *
     * @param file      传入的文件
     * @param objectMap 业务数据Map
     * @return List<DtoPerson>
     * @throws Exception 异常抛出
     */
    @Override
    @Transactional
    public List<DtoPerson> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //region 业务参数
        Boolean isImportPost = (Boolean) objectMap.get(0);
        Boolean isImportTitle = (Boolean) objectMap.get(1);
        Boolean isImportEducation = (Boolean) objectMap.get(2);
        Boolean isCreateAccount = (Boolean) objectMap.get(3);
        PersonVerifyHandle verifyHandler = getVerifyHandler(isImportPost, isImportTitle, isImportEducation, isCreateAccount, file);
        ExcelImportResult<DtoImportPerson> importResult = getExcelData(verifyHandler, file, response);
        //获取校验成功得导入数据
        List<DtoImportPerson> importList = importResult.getList();
        //移除空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getChineseName()));
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        //获取所有的职务
        List<DtoCode> dbPostList = codeService.findCodes("LIM_Post");
        //获取所有的职务
        List<DtoCode> dbTitleList = codeService.findCodes("LIM_TechnicalTitle");
        //获取所有的职务
        List<DtoCode> dbEducationList = codeService.findCodes("LIM_Degree");
        //获取所有的部门
        List<DtoDepartment> dbDepartment = departmentService.findAll();
        //是否关联导入职务
        if (isImportPost) {
            importPost(dbPostList, file);
        }
        //是否关联导入职称
        if (isImportTitle) {
            importTitle(dbTitleList, file);
        }
        //是否关联导入学历
        if (isImportEducation) {
            importEducation(dbEducationList, file);
        }
        //添加数据
        List<DtoPerson> personList = importToEntity(dbDepartment, importList);
        //判断最大用户数
        judgeMaxUserNum(personList);
        //保存数据
        if (StringUtil.isNotEmpty(importList)) {
            addData(personList);
        }
        if (isCreateAccount) {
            //需要导入的常量
            importCreateAccount(personList);
        }
        return personService.findAll();
    }

    /**
     * 添加数据库数据
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoPerson> data) {
        personRepository.save(data);
    }


    /**
     * 获取导入数据
     *
     * @param file 传入的文件
     * @return ExcelImportResult
     * @throws Exception 异常信息
     */
    @Override
    public ExcelImportResult<DtoImportPerson> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param verifyHandler 校验器
     * @param file          传入的文件
     * @param response      响应体
     * @return 导入结果
     */
    @Override
    public ExcelImportResult<DtoImportPerson> getExcelData(IExcelVerifyHandler<DtoImportPerson> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(verifyHandler);
        ExcelImportResult<DtoImportPerson> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoImportPerson.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "人员导入导入错误信息");
            failWorkbook.removeSheetAt(1);
            PoiExcelUtils.downLoadExcel("人员导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 获取人员导入校验器
     *
     * @param isImportPost      是否导入职务
     * @param isImportTitle     是否导入职称
     * @param isImportEducation 是否导入学历
     * @param isCreateAccount   是否创建账号
     * @return 校验器
     */
    private PersonVerifyHandle getVerifyHandler(Boolean isImportPost, Boolean isImportTitle, Boolean isImportEducation,
                                                Boolean isCreateAccount, MultipartFile file) {
        Map<String, Boolean> relationMap = new HashMap<>();
        relationMap.put("isImportPost", isImportPost);
        relationMap.put("isImportTitle", isImportTitle);
        relationMap.put("isImportEducation", isImportEducation);
        relationMap.put("isCreateAccount", isCreateAccount);
        List<DtoImportPersonExtend> extendList = importUtils.getImportNames(file, DtoImportPersonExtend.class);
        return new PersonVerifyHandle(
                relationMap,
                codeService.findCodes("LIM_Post"),
                codeService.findCodes("LIM_TechnicalTitle"),
                codeService.findCodes("LIM_Degree"),
                departmentService.findAll(),
                roleInfoService.findAll(),
                personRepository.findAll(),extendList);
    }

    /**
     * 判断导入人员是否超过最大用户数
     *
     * @param personList 人员导入数据
     */
    private void judgeMaxUserNum(List<DtoPerson> personList) {
        //查询当前导入的机构
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        //查询机构数据，获取最大用户数
        OrgModel orgModel = orgService.selectById(orgId);
        //获取机构下用户
        List<DtoUser> orgUsers = userService.findAll().stream().filter(p -> orgId.equals(p.getOrgId())).collect(Collectors.toList());
        //判断导入人员数量与机构下用户数量之和是否大于最大用户数
        if ((personList.size() + orgUsers.size()) > orgModel.getMaxUserNum()) {
            throw new BaseException("导入的人员与系统中人员数量和超过最大用户数, 导入失败!");
        }
    }

    /**
     * 导入实体转换为人员实体
     *
     * @param ImportDeptList 导入的新部门
     * @param importPersons  导入数据
     * @return 人员实体
     */
    private List<DtoPerson> importToEntity(List<DtoDepartment> ImportDeptList, List<DtoImportPerson> importPersons) {
        List<DtoPerson> personList = new ArrayList<>();
        for (DtoImportPerson importPerson : importPersons) {
            List<String> deptIds = ImportDeptList.stream().filter(p -> importPerson.getDeptId().equals(p.getDeptName())).map(DtoDepartment::getId).collect(Collectors.toList());
            DtoPerson person = new DtoPerson();
            person.importToPersonEntity(importPerson);
            person.setTechnicalTitleDate(importPerson.getTechnicalTitleDate() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getTechnicalTitleDate()));
            person.setBirthDay(importPerson.getBirthDay() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getBirthDay()));
            person.setJoinPartyDate(importPerson.getJoinPartyDate() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getJoinPartyDate()));
            person.setJoinCompanyTime(importPerson.getJoinCompanyTime() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getJoinCompanyTime()));
            person.setLeaveCompanyTime(importPerson.getLeaveCompanyTime() == null ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importPerson.getLeaveCompanyTime()));
            person.setDeptId(StringUtil.isEmpty(deptIds) ? UUIDHelper.GUID_EMPTY : deptIds.get(0));
            personList.add(person);
        }
        return personList;
    }

    /**
     * 创建账号
     *
     * @param persons 人员
     */
    @Override
    public void importCreateAccount(List<DtoPerson> persons) {
        //获取导入时的角色
        for (DtoPerson person : persons) {
            List<String> roleNames = new ArrayList<>();
            splitRoleName(roleNames, person);
            //系统中存在的所有角色
            List<DtoRole> roles = roleInfoService.findAll();

            //获取需要导入的角色ID
            List<String> roleIds;
            if (StringUtil.isNotEmpty(roleNames)) {
                roleIds = roles.stream().filter(p -> roleNames.contains(p.getRoleName())).map(DtoRole::getRoleId).collect(Collectors.toList());
            } else {
                roleIds = new ArrayList<>();
            }
            List<DtoLoginExpand> loginExpands = new ArrayList<>();
            if (StringUtil.isNotEmpty(person.getUserName())) {
                writeExpandLogin(loginExpands, person.getUserName(), "userName");
            }
            if (StringUtil.isNotEmpty(person.getEmail())) {
                writeExpandLogin(loginExpands, person.getEmail(), "email");
            }

            //开通账号
            personService.openAccount(person.getId(), person.getMobile(), null, roleIds, loginExpands);

        }

    }

    /**
     * 获取拓展账号创建
     *
     * @param loginExpands 拓展账号集合
     * @param loginId      开通的Id
     * @param loginType    账号类型
     */
    private void writeExpandLogin(List<DtoLoginExpand> loginExpands, String loginId, String loginType) {
        DtoLoginExpand loginExpandName = new DtoLoginExpand();
        loginExpandName.setLoginId(loginId);
        loginExpandName.setLoginType(loginType);
        loginExpands.add(loginExpandName);
    }

    /**
     * 分割角色
     *
     * @param roleNames 角色集合
     * @param person    需要分割的实体
     */
    private void splitRoleName(List<String> roleNames, DtoPerson person) {
        if (StringUtil.isNotEmpty(person.getRoleNames())) {
            roleNames.addAll(importUtils.personStrToList(person.getRoleNames()));
        }
    }

    /**
     * 导入职务
     *
     * @param dbPost 数据库的常量类型
     * @param file   传入的文件
     */
    private void importPost(List<DtoCode> dbPost, MultipartFile file) {
        //需要导入的常量
        List<DtoImportPersonExtend> importPostNames = importUtils.getImportNames(file, DtoImportPersonExtend.class);
        //数据库中所有的职务Id
        List<String> postNames = dbPost.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //不包含的部门(需要导入的部门)
        List<String> isImportPost = importPostNames.stream().map(p -> p.getPost() == null ? null : p.getPost()).collect(Collectors.toList());
        importUtils.createCodes(isImportPost, "LIM_Post", postNames);
    }

    /**
     * 导入职称
     *
     * @param dbTitle 职称常量
     * @param file    文件流
     */
    private void importTitle(List<DtoCode> dbTitle, MultipartFile file) {
        //需要导入的常量
        List<DtoImportPersonExtend> importTitleNames = importUtils.getImportNames(file, DtoImportPersonExtend.class);
        //数据库中所有的职称Id
        List<String> titleNames = dbTitle.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //不包含的职称(需要导入的职称)
        List<String> isImportPost = importTitleNames.stream().map(p -> p.getTitle() == null ? null : p.getTitle()).collect(Collectors.toList());
        importUtils.createCodes(isImportPost, "LIM_TechnicalTitle", titleNames);
    }

    /**
     * 导入学历
     *
     * @param dbEducationList 学历常量
     * @param file            文件流
     */
    private void importEducation(List<DtoCode> dbEducationList, MultipartFile file) {
        //需要导入的学历
        List<DtoImportPersonExtend> importEducationNames = importUtils.getImportNames(file, DtoImportPersonExtend.class);
        //数据库中所有的学历Id
        List<String> educationNames = dbEducationList.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //不包含的学历(需要导入的学历)
        List<String> isImportPost = importEducationNames.stream().map(p -> p.getEducation() == null ? null : p.getEducation()).collect(Collectors.toList());
        importUtils.createCodes(isImportPost, "LIM_Degree", educationNames);
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setRoleInfoService(RoleInfoServiceImpl roleInfoService) {
        this.roleInfoService = roleInfoService;
    }

    @Autowired
    public void setOrgService(IOrgService orgService) {
        this.orgService = orgService;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }
}
