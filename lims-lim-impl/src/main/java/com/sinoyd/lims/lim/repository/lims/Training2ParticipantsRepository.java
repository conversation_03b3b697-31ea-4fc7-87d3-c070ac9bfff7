package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTraining2Participants;

import java.util.Arrays;
import java.util.List;

/**
 * 培训与参与人员关联仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
public interface Training2ParticipantsRepository extends IBaseJpaPhysicalDeleteRepository<DtoTraining2Participants, String> {
    /**
     * 根据培训id删除
     *
     * @param trainingId 培训id
     */
    void deleteByTrainingId(String trainingId);

    /**
     * 根据培训id 查询
     *
     * @param trainingId 培训id
     * @return 参与人集合
     */
    List<DtoTraining2Participants> findByTrainingId(String trainingId);

    /**
     * 很具培训id集合查询
     *
     * @param trainingIds 培训id集合
     * @return 参与人集合
     */
    List<DtoTraining2Participants> findByTrainingIdIn(List<String> trainingIds);
}
