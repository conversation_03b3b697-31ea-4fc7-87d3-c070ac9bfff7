package com.sinoyd.lims.lim.service.transform;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpFileManage;
import com.sinoyd.lims.lim.service.ImportExcelService;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 文件管理清单导出接口
 *
 * @version V1.0.0 2025/4/27
 * @author: xiexy
 * @since V100R001
 */
public interface ExpDocumentManageService extends ImportExcelService<DtoExpFileManage, DtoDocument> {
    /**
     * 文件管理清单
     */
    void export(BaseCriteria criteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName);
}
