package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.sampleFolder;

import com.sinoyd.lims.pro.dto.DtoSampleFolderTemplate;
import com.sinoyd.lims.pro.repository.SamplingFrequencyTempRepository;
import com.sinoyd.lims.pro.repository.SamplingFrequencyTestTempRepository;
import com.sinoyd.lims.pro.service.SampleFolderService;
import com.sinoyd.lims.pro.service.WorkSheetFolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class AbsSampleFolderStrategy {

    protected SampleFolderService sampleFolderService;

    protected WorkSheetFolderService workSheetFolderService;

    protected SamplingFrequencyTempRepository samplingFrequencyTempRepository;

    protected SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository;

    /**
     * 调整点位方案
     *
     * @param sampleFolderTemplateList 修改点位内容
     */
    public abstract void synchronizationSampleFolder(String projectId, List<DtoSampleFolderTemplate> sampleFolderTemplateList);

    @Autowired
    @Lazy
    public void setSampleFolderService(SampleFolderService sampleFolderService) {
        this.sampleFolderService = sampleFolderService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetFolderService(WorkSheetFolderService workSheetFolderService) {
        this.workSheetFolderService = workSheetFolderService;
    }

    @Autowired
    @Lazy
    public void setSamplingFrequencyTempRepository(SamplingFrequencyTempRepository samplingFrequencyTempRepository) {
        this.samplingFrequencyTempRepository = samplingFrequencyTempRepository;
    }

    @Autowired
    @Lazy
    public void setSamplingFrequencyTestTempRepository(SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository) {
        this.samplingFrequencyTestTempRepository = samplingFrequencyTestTempRepository;
    }
}
