package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.customer.DtoImportRecordConfig2Test;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParams2ParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.Params2ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsConfigRepository;
import com.sinoyd.lims.lim.repository.rcc.RecordConfig2TestRepository;
import com.sinoyd.lims.lim.repository.rcc.RecordConfigRepository;
import com.sinoyd.lims.lim.service.ImportRecordConfig2TestService;
import com.sinoyd.lims.lim.verify.RecordConfig2TestVerifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 原始记录单与测试项目关系导入接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/6
 * @since V100R001
 */
@Service
@Slf4j
public class ImportRecordConfig2TestServiceImpl implements ImportRecordConfig2TestService {

    private RecordConfig2TestRepository recordConfig2TestRepository;

    private TestRepository testRepository;

    private RecordConfigRepository recordConfigRepository;

    private SampleTypeRepository sampleTypeRepository;

    private Params2ParamsFormulaRepository params2ParamsFormulaRepository;

    private ParamsConfigRepository paramsConfigRepository;

    private RecordConfig2TestVerifyHandler recordConfig2TestVerifyHandler;

    private List<DtoTest> allTestList;

    private List<DtoRecordConfig> allRecordConfigList;

    private List<DtoSampleType> allSampleTypeList;

    @Override
    @Transactional
    public void importExcel(MultipartFile file,  HttpServletResponse response) throws Exception {
        //文件格式校验
        PoiExcelUtils.verifyFileType(file);
        //通用数据初始化
        initDataContainer();
        //更新校验容器并获取excel导入结果集
        initHandler();
        ExcelImportResult<DtoImportRecordConfig2Test> importResult = getExcelData(file, response);
        //校验正确数据
        List<DtoImportRecordConfig2Test> importList = importResult.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getRecordNames()));
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        //数据入库
        saveRelatedData(importList);
        //清理校验器数据
        clearHandler();
    }



    /**
     * 数据处理并入库
     * @param importList 导入数据集
     */
    private void saveRelatedData(List<DtoImportRecordConfig2Test> importList) {
        //待插入数据容器
        List<DtoRecordConfig2Test> waitSaveList = new ArrayList<>();
        //所有的原始记录单参数
        List<String>  waitUpdateParamsConfigIdList = new ArrayList<>();
        //所有原始记录单与测试项目关系
        List<DtoRecordConfig2Test> allRecordConfig2TestList = recordConfig2TestRepository.findAll();
        for (DtoImportRecordConfig2Test importRecordConfig2Test:importList) {
            collectWaitSaveList(waitSaveList,importRecordConfig2Test,allRecordConfig2TestList,allTestList,allRecordConfigList,allSampleTypeList);
        }
        //关联关系入库
        recordConfig2TestRepository.save(waitSaveList);
        //新增测试项目之后，所有的原始记录单参数都要改成未配置
        collectWaitUpdateParamsConfigIdList(waitUpdateParamsConfigIdList,waitSaveList);
        if(waitUpdateParamsConfigIdList.size()>0){
            paramsConfigRepository.updateParamsConfigIsAllConfig(waitUpdateParamsConfigIdList, false);
        }
    }


    /**
     * 解析导入实体并转换为可入库数据
     * @param waitSaveList             更新容器
     * @param importRecordConfig2Test  单条导入数据
     * @param allRecordConfig2TestList 所有已存在关联关系
     * @param allTestList              所有测试项目
     * @param allRecordConfigList      所有原始记录单
     * @param allSampleTypeList        所有检测类型
     */
    private void collectWaitSaveList(List<DtoRecordConfig2Test> waitSaveList, DtoImportRecordConfig2Test importRecordConfig2Test,List<DtoRecordConfig2Test> allRecordConfig2TestList,
                                     List<DtoTest> allTestList, List<DtoRecordConfig> allRecordConfigList,List<DtoSampleType> allSampleTypeList) {
        DtoSampleType dtoSampleType = allSampleTypeList.stream().filter(s->importRecordConfig2Test.getSampleType().equals(s.getTypeName())).findFirst().orElse(null);
        if(dtoSampleType !=null ){
            DtoTest dtoTest = allTestList.stream().filter(t->importRecordConfig2Test.getAnalyzeItem().equals(t.getRedAnalyzeItemName())
                                                         &&importRecordConfig2Test.getAnalyzeMethod().equals(t.getRedAnalyzeMethodName())
                                                         &&dtoSampleType.getId().equals(t.getSampleTypeId()))
                                                   .findFirst().orElse(null);
            if(dtoTest != null){
                List<String> singleReportNameList = new ArrayList<>(Arrays.asList(importRecordConfig2Test.getRecordNames().split("；")));
                for (String recordName:singleReportNameList) {
                    DtoRecordConfig dtoRecordConfig = allRecordConfigList.stream().filter(r->recordName.equals(r.getRecordName())).findFirst().orElse(null);
                    if(dtoRecordConfig !=null){
                        DtoRecordConfig2Test dtoRecordConfig2Test = allRecordConfig2TestList.stream().filter(r->dtoTest.getId().equals(r.getTestId())
                                                                                                            &&dtoRecordConfig.getId().equals(r.getRecordConfigId()))
                                                                                                      .findFirst().orElse(null);
                        if(dtoRecordConfig2Test == null){
                            dtoRecordConfig2Test = new DtoRecordConfig2Test();
                            dtoRecordConfig2Test.setTestId(dtoTest.getId());
                            dtoRecordConfig2Test.setRecordConfigId(dtoRecordConfig.getId());
                            waitSaveList.add(dtoRecordConfig2Test);
                        }
                    }
                }
            }
        }
    }

    /**
     * 新增测试项目之后，所有的原始记录单参数都要改成未配置
     * @param waitUpdateParamsConfigIdList 待更新标识列表
     * @param waitSaveList                 新增的关联关系
     */
    private void collectWaitUpdateParamsConfigIdList(List<String> waitUpdateParamsConfigIdList, List<DtoRecordConfig2Test> waitSaveList) {
        if(waitSaveList.size()>0){
            List<String> recordConfigIds = waitSaveList.stream().map(DtoRecordConfig2Test::getRecordConfigId).distinct().collect(Collectors.toList());
            List<DtoParams2ParamsFormula> params2ParamsFormulas = params2ParamsFormulaRepository.findByRecordIdIn(recordConfigIds);
            waitUpdateParamsConfigIdList.addAll(params2ParamsFormulas.stream().map(DtoParams2ParamsFormula::getParamsConfigId).distinct().collect(Collectors.toList()));
        }
    }

    /**
     * 通用数据初始化
     */
    private void initDataContainer() {
        //所有测试项目
        allTestList = testRepository.findAll();
        //所有原始记录单
        allRecordConfigList = recordConfigRepository.findAllByRecordType(EnumLIM.EnumRecordType.原始记录单.getValue());
        //所有检测类型
        allSampleTypeList = sampleTypeRepository.findAll();
    }


    /**
     * 初始化校验器
     */
    private void initHandler() {
        recordConfig2TestVerifyHandler =  new RecordConfig2TestVerifyHandler();
        recordConfig2TestVerifyHandler.setSheetExistDataList(new ArrayList<>());
        recordConfig2TestVerifyHandler.setAllSampleTypeList(allSampleTypeList);
        recordConfig2TestVerifyHandler.setAllTestList(allTestList);
        recordConfig2TestVerifyHandler.setAllRecordConfigList(allRecordConfigList);
    }

    /**
     * 移除下次调用校验器需重新赋值的容器，避免数据混淆
     */
    private void clearHandler() {
        recordConfig2TestVerifyHandler.getSheetExistDataList().clear();
        recordConfig2TestVerifyHandler.getAllSampleTypeList().clear();
        recordConfig2TestVerifyHandler.getAllTestList().clear();
        recordConfig2TestVerifyHandler.getAllRecordConfigList().clear();
    }


    /**
     * 解析文件
     * @param file      文件
     * @param response  响应
     * @return 结果集
     * @throws Exception 异常
     */
    private ExcelImportResult<DtoImportRecordConfig2Test> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置是否校验及校验器
        params.setNeedVerify(true);
        params.setVerifyHandler(recordConfig2TestVerifyHandler);
        ExcelImportResult<DtoImportRecordConfig2Test> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoImportRecordConfig2Test.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "原始记录单与测试项目关系导入错误信息");
            PoiExcelUtils.downLoadExcel("原始记录单与测试项目关系导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    @Autowired
    public void setRecordConfig2TestRepository(RecordConfig2TestRepository recordConfig2TestRepository) {
        this.recordConfig2TestRepository = recordConfig2TestRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setRecordConfigRepository(RecordConfigRepository recordConfigRepository) {
        this.recordConfigRepository = recordConfigRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setParams2ParamsFormulaRepository(Params2ParamsFormulaRepository params2ParamsFormulaRepository) {
        this.params2ParamsFormulaRepository = params2ParamsFormulaRepository;
    }

    @Autowired
    public void setParamsConfigRepository(ParamsConfigRepository paramsConfigRepository) {
        this.paramsConfigRepository = paramsConfigRepository;
    }
}
