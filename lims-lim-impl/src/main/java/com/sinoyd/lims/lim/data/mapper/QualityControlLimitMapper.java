package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.lims.lim.dto.customer.DtoExportQualityControlLimit;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * 质控限制实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface QualityControlLimitMapper {


    /**
     * DtoQualityControlLimit 实例转换成DtoExportQualityControlLimit实例
     *
     * @param qualityControlLimit 分析项目实体
     * @return DtoExportQualityControlLimit 实例
     */
    @Mapping(source = "judgingMethod", target = "judgingMethod")
    @Mapping(source = "qcGrade", target = "qcGrade")
    @Mapping(source = "qcType", target = "qcType")
    @Mapping(source = "checkItem", target = "checkItem")
    @Mapping(source = "isCheckItem", target = "isCheckItem")
    @Mapping(source = "validate", target = "validate")
    @Mapping(source = "usageNum", target = "usageNum")
    DtoExportQualityControlLimit toExportQualityControlLimit(DtoQualityControlLimit qualityControlLimit);

    /**
     * DtoQualityControlLimit 实例集合转换成DtoExportQualityControlLimit 实例集合
     *
     * @param qualityControlLimitList 分析项目实例集合
     * @return DtoExportQualityControlLimit 实例集合
     */
    @InheritConfiguration(name = "toExportQualityControlLimit")
    List<DtoExportQualityControlLimit> toExportQualityControlLimitList(List<DtoQualityControlLimit> qualityControlLimitList);


    /**
     * DtoExportQualityControlLimit 实例转换成DtoQualityControlLimit 实例
     *
     * @param exportQualityControlLimit 分析项目实体
     * @return DtoExportQualityControlLimit 实例
     */
    @Mapping(source = "judgingMethod", target = "judgingMethod")
    @Mapping(source = "qcGrade", target = "qcGrade")
    @Mapping(source = "qcType", target = "qcType")
    @Mapping(source = "checkItem", target = "checkItem")
    @Mapping(source = "isCheckItem", target = "isCheckItem")
    @Mapping(source = "validate", target = "validate")
    @Mapping(source = "usageNum", target = "usageNum")
    DtoQualityControlLimit toDtoQualityControlLimit(DtoExportQualityControlLimit exportQualityControlLimit);

    /**
     * DtoExportQualityControlLimit 实例集合转换成DtoQualityControlLimit 实例集合
     *
     * @param exportQualityControlLimitList 分析项目导入导出实例集合
     * @return DtoQualityControlLimit 实例集合
     */
    @InheritConfiguration(name = "toDtoQualityControlLimit")
    List<DtoQualityControlLimit> toDtoQualityControlLimitList(List<DtoExportQualityControlLimit> exportQualityControlLimitList);

}
