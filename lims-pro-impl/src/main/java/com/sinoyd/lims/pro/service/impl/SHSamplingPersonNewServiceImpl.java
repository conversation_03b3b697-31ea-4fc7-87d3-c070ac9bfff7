package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.dto.DtoSHSamplingPersonNew;
import com.sinoyd.lims.pro.repository.SHSamplingPersonNewRepository;
import com.sinoyd.lims.pro.service.SHSamplingPersonNewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * SHSamplingPerson操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/28
 * @since V100R001
 */
@Service
public class SHSamplingPersonNewServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSHSamplingPersonNew,String,SHSamplingPersonNewRepository> implements SHSamplingPersonNewService {

    private PersonService personService;

    private DepartmentService departmentService;

    @Override
    public void findByPage(PageBean<DtoSHSamplingPersonNew> page, BaseCriteria criteria) {
        page.setSelect("select a");
        page.setEntityName("DtoSHSamplingPersonNew a");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData());
    }

    @Override
    public List<DtoSHSamplingPersonNew> save(Collection<DtoSHSamplingPersonNew> entities) {
        List<DtoSHSamplingPersonNew> personList = (List)entities;
        List<String> existPersonIds = repository.findByTaskId(personList.get(0).getTaskId()).stream().map(DtoSHSamplingPersonNew::getPersonId).collect(Collectors.toList());
        List<DtoSHSamplingPersonNew> addList = new ArrayList<>();
        entities.forEach(e->{
            if(!existPersonIds.contains(e.getPersonId())) {
                addList.add(e);
            }
        });
        List<DtoSHSamplingPersonNew> result = new ArrayList<>();
        if(StringUtil.isNotEmpty(addList)) {
            result = super.save(addList);
        }
        return result;
    }

    /**
     * 填充冗余字段
     *
     * @param data 数据
     */
    private void fillingTransientFields(List<DtoSHSamplingPersonNew> data) {
        List<DtoPerson> personList = personService.findAll();
        List<DtoDepartment> dtoDepartments = departmentService.findAll();
        data.forEach(d->{
            Optional<DtoPerson> person = personList.stream().filter(p->p.getId().equals(d.getPersonId())).findFirst();
            person.ifPresent(per->{
                d.setPhone(per.getMobile());
                Optional<DtoDepartment> department = dtoDepartments.stream().filter(de->de.getId().equals(per.getDeptId())).findFirst();
                department.ifPresent(de->d.setDeptName(de.getDeptName()));
                d.setPersonName(per.getCName());
                d.setRegulateId(per.getRegulateId());
                d.setRegulateName(per.getRegulateName());
            });
        });
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }


    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }
}
