package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoProjectContract;

import java.util.Collection;
import java.util.List;

/**
 * 项目合同中间表Repository
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/11/21
 */
public interface ProjectContractRepository extends IBaseJpaPhysicalDeleteRepository<DtoProjectContract, String> {

    /**
     * 根据项目id删除数据
     *
     * @param ids 项目id
     */
    void deleteByProjectIdIn(Collection<?> ids);

    /**
     * 根据项目id查询
     *
     * @param id 项目id
     * @return DtoProjectContract
     */
    List<DtoProjectContract> findByProjectId(String id);

    /**
     * 根据项目ids查询
     *
     * @param ids 项目ids
     * @return List<DtoProjectContract>
     */
    List<DtoProjectContract> findByProjectIdIn(List<String> ids);

    /**
     * 根据合同id查询项目拓展信息
     *
     * @param contractId  合同id
     * @return            List<DtoProjectContract>
     */
    List<DtoProjectContract> findByContractIdAndHasPush(String contractId,Integer hasPush);

    /**
     * 查询监管平台任务id不为null的数据
     *
     * @param hasPush 计划推送状态
     * @return        List<DtoProjectContract>
     */
    List<DtoProjectContract> findByPIdNotNullAndPlanHasPush(Integer hasPush);
}
