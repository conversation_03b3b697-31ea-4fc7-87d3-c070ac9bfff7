package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.EnvironmentEnterpriseService;
import com.sinoyd.lims.pro.service.Project2ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;



/**
 * 项目和报告关联关系操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/04/25
 * @since V100R001
 */
@Service
public class Project2ReportServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoProject2Report, String, Project2ReportRepository> implements Project2ReportService {

    private ReportRepository reportRepository;
    private LogForReportRepository logForReportRepository;
    private DocumentService documentService;
    private DocumentRepository documentRepository;
    private FilePathConfig filePathConfig;
    private ExpressageInfo2ReportRepository expressageInfo2ReportRepository;
    private ExpressageInfoRepository expressageInfoRepository;
    private EnvironmentEnterpriseService environmentEnterpriseService;
    private ProjectRepository projectRepository;

    @Override
    public List<DtoProject2Report> findForProject(String projectId) {
        List<DtoProject2Report> project2ReportList = repository.findByProjectId(projectId);
        if (StringUtil.isNotEmpty(project2ReportList)) {
            List<String> reportIdList = project2ReportList.stream().map(DtoProject2Report::getReportId).collect(Collectors.toList());
            List<DtoLogForReport> logForReportList = logForReportRepository.findByObjectIdIn(reportIdList);
            logForReportList = logForReportList.stream().filter(p -> EnumPRO.EnumLogOperateType.增加报告.toString().equals(p.getOperateInfo())).collect(Collectors.toList());
            Map<String, List<DtoLogForReport>> logMap = logForReportList.stream().collect(Collectors.groupingBy(DtoLogForReport::getObjectId));
            List<DtoReport> reportList = reportRepository.findAll(reportIdList);
            //获取报告发放信息
            List<DtoExpressageInfo2Report> expressInfo2ReportList = expressageInfo2ReportRepository.findByReportIdIn(reportIdList);
            Map<String, List<DtoExpressageInfo2Report>> expressInfo2ReportMap = expressInfo2ReportList.stream().collect(Collectors.groupingBy(DtoExpressageInfo2Report::getReportId));
            List<String> expressInfoIdList = expressInfo2ReportList.stream().map(DtoExpressageInfo2Report::getExpressageInfoId).distinct().collect(Collectors.toList());
            List<DtoExpressageInfo> expressInfoList = StringUtil.isNotEmpty(expressInfoIdList) ? expressageInfoRepository.findAll(expressInfoIdList) : new ArrayList<>();
            //获取电子报告附件信息
            List<String> project2ReportIdList = project2ReportList.stream().map(DtoProject2Report::getId).collect(Collectors.toList());
            List<DtoDocument> documentList = documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(project2ReportIdList, "BASE_DocumentExtendType_DZBG");
            Map<String, List<DtoDocument>> documentMap = documentList.stream().collect(Collectors.groupingBy(DtoDocument::getFolderId));
            Map<String, DtoReport> reportMap = reportList.stream().collect(Collectors.toMap(DtoReport::getId, dto -> dto));
            for (DtoProject2Report project2Report : project2ReportList) {
                project2Report.setReportCode("");
                DtoReport report = reportMap.get(project2Report.getReportId());
                if (StringUtil.isNotNull(report)) {
                    project2Report.setReportCode(report.getCode());
                    project2Report.setReportStatus(report.getStatus());
                    project2Report.setGrantStatus(report.getGrantStatus());
                    List<DtoLogForReport> logList = logMap.get(project2Report.getReportId());
                    if (StringUtil.isNotEmpty(logList)) {
                        project2Report.setMakerName(logList.get(0).getOperatorName());
                        String makeDate = StringUtil.isNotNull(logList.get(0).getOperateTime()) ? DateUtil.dateToString(logList.get(0).getOperateTime(), DateUtil.YEAR) : "";
                        project2Report.setMakeDate(makeDate.contains("1753") ? "" : makeDate);
                    }
                    //获取报告发放信息
                    String sender = "";
                    String sendDate = "";
                    List<DtoExpressageInfo2Report> info2ReportList = expressInfo2ReportMap.get(project2Report.getReportId());
                    if (StringUtil.isNotEmpty(info2ReportList)) {
                        List<String> infoIdList = info2ReportList.stream().map(DtoExpressageInfo2Report::getExpressageInfoId).distinct().collect(Collectors.toList());
                        List<DtoExpressageInfo> infoList = expressInfoList.stream().filter(p -> infoIdList.contains(p.getId()))
                                .sorted(Comparator.comparing(DtoExpressageInfo::getSendDate).reversed()).collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(infoList)) {
                            sender = infoList.get(0).getSender();
                            sendDate = StringUtil.isNotNull(infoList.get(0).getSendDate()) ? DateUtil.dateToString(infoList.get(0).getSendDate(), DateUtil.YEAR) : "";
                            sendDate = sendDate.contains("1753") ? "" : sendDate;
                        }
                    }
                    project2Report.setSender(sender);
                    project2Report.setSendDate(sendDate);
                    //获取电子报告附件信息
                    String fileName = "", docSuffix = "", path = "";
                    List<DtoDocument> documentForReport = documentMap.get(project2Report.getId());
                    if (StringUtil.isNotEmpty(documentForReport)) {
                        documentForReport.sort(Comparator.comparing(DtoDocument::getCreateDate).reversed());
                        fileName = documentForReport.get(0).getFilename();
                        docSuffix = documentForReport.get(0).getDocSuffix();
                        path = documentForReport.get(0).getPath();
                    }
                    project2Report.setFileName(fileName);
                    project2Report.setDocSuffix(docSuffix);
                    project2Report.setPath(path);
                }
            }
        }
        project2ReportList.sort(Comparator.comparing(DtoProject2Report::getReportCode));
        return project2ReportList;
    }

    /**
     * 上传电子报告
     *
     * @param request 请求体
     */
    @Override
    @Transactional
    public DtoProject2Report uploadElectronicReport(HttpServletRequest request) {
        String project2ReportId = request.getParameter("folderId");
        DtoProject2Report project2Report = repository.findOne(project2ReportId);
        if (StringUtil.isNull(project2Report)) {
            throw new BaseException("该报告不存在！");
        }
        //上传电子报告
        documentService.upload(request, Collections.singletonList("pdf"));
        if (project2Report.getUploadStatus().equals(0)) {
            project2Report.setUploadStatus(1);
            repository.save(project2Report);
        }
        return project2Report;
    }


    @Override
    public String downloadElectronicReport(String project2ReportId, HttpServletResponse response) throws IOException {
        Optional<DtoDocument> document = documentService.findByObjectId(project2ReportId).stream().max(Comparator.comparing(DtoDocument::getCreateDate));
        if (document.isPresent()) {
            String path = filePathConfig.getFilePath() + document.get().getPath();
            String filename = document.get().getFilename();
            return documentService.fileDownload(path, filename, response);
        } else {
            return "文件不存在！";
        }
    }

    @Override
    @Transactional
    public void clearElectronicReport(String project2ReportId) {
        List<DtoDocument> documentList = documentService.findByObjectId(project2ReportId);
        if (StringUtil.isNotEmpty(documentList)) {
            List<String> docIdList = documentList.stream().map(DtoDocument::getId).collect(Collectors.toList());
            documentService.logicDeleteById(docIdList);
        }
        DtoProject2Report project2Report = repository.findOne(project2ReportId);
        if (StringUtil.isNotNull(project2Report)) {
            project2Report.setUploadStatus(0);
            repository.save(project2Report);
        }
    }

    @Override
    public void deleteByReportId(String reportId) {
        List<DtoProject2Report> project2ReportList = repository.findByReportId(reportId);
        if (StringUtil.isNotEmpty(project2ReportList)) {
            repository.delete(project2ReportList);

        }
    }

    @Autowired
    public void setReportRepository(ReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }

    @Autowired
    public void setLogForReportRepository(LogForReportRepository logForReportRepository) {
        this.logForReportRepository = logForReportRepository;
    }

    @Autowired
    @Lazy
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    public void setExpressageInfo2ReportRepository(ExpressageInfo2ReportRepository expressageInfo2ReportRepository) {
        this.expressageInfo2ReportRepository = expressageInfo2ReportRepository;
    }

    @Autowired
    public void setExpressageInfoRepository(ExpressageInfoRepository expressageInfoRepository) {
        this.expressageInfoRepository = expressageInfoRepository;
    }

    @Autowired
    @Lazy
    public void setEnvironmentEnterpriseService(EnvironmentEnterpriseService environmentEnterpriseService) {
        this.environmentEnterpriseService = environmentEnterpriseService;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }
}