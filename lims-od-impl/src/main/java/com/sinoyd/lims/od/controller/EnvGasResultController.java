package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.EnvGasResultCriteria;
import com.sinoyd.lims.od.dto.DtoEnvGasResult;
import com.sinoyd.lims.od.service.EnvGasResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * envGasResult服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例: envGasResult服务")
@RestController
@RequestMapping("api/lim/envGasResult")
@Validated
public class EnvGasResultController extends BaseJpaController<DtoEnvGasResult, String, EnvGasResultService> {

    /**
     * 分页动态条件查询EnvGasResult
     *
     * @param envGasResultCriteria 条件参数
     * @return RestResponse<List < EnvGasResult>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoEnvGasResult>> findByPage(EnvGasResultCriteria envGasResultCriteria) {
        PageBean<DtoEnvGasResult> pageBean = super.getPageBean();
        RestResponse<List<DtoEnvGasResult>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, envGasResultCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询envGasResult
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询envGasResult", notes = "根据id查询envGasResult")
    @GetMapping("/{id}")
    public RestResponse<DtoEnvGasResult> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoEnvGasResult> restResp = new RestResponse<>();
        DtoEnvGasResult entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增envGasResult
     *
     * @param envGasResult envGasResult实体
     * @return 新增的envGasResult实体
     */
    @ApiOperation(value = "新增envGasResult", notes = "新增envGasResult")
    @PostMapping("")
    public RestResponse<DtoEnvGasResult> create(@Validated @RequestBody DtoEnvGasResult envGasResult) {
        RestResponse<DtoEnvGasResult> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoEnvGasResult data = service.save(envGasResult);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新envGasResult
     *
     * @param envGasResult envGasResult实体
     * @return 更新后的envGasResult实体
     */
    @ApiOperation(value = "更新envGasResult", notes = "更新envGasResult")
    @PutMapping("")
    public RestResponse<DtoEnvGasResult> update(@Validated @RequestBody DtoEnvGasResult envGasResult) {
        RestResponse<DtoEnvGasResult> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoEnvGasResult data = service.update(envGasResult);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
