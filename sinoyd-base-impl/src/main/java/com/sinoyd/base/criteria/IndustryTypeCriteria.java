package com.sinoyd.base.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 行业类型查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/1/7
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndustryTypeCriteria extends BaseCriteria {

    /**
     * 关键字：行业类型名称,行业类型编号
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and (industryName like :key or industryCode like :key)");
            values.put("key", "%" + this.key + "%");
        }

        condition.append(" and isDeleted = 0 ");

        return condition.toString();
    }
}


