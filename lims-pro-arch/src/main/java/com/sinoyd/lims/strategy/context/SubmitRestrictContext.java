package com.sinoyd.lims.strategy.context;

import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/29
 */
public interface SubmitRestrictContext {

    /**
     * 提交判断
     *
     * @param beanName 方法名称
     * @param objMap   参数
     * @param status   状态
     * @return 提交判断
     */
    List<DtoSubmitRestrictVo> submitJudgment(String beanName, Object objMap, String status);
}
