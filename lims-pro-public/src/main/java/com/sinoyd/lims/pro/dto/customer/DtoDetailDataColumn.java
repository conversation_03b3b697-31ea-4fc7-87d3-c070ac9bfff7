package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 详细数据的test
 * <AUTHOR>
 * @version V1.0.0 2020/1/17
 * @since V100R001
 */
@Data
public class DtoDetailDataColumn {

    /**
     * 测试项目id
     */
    private String testId;


    /**
     * 测试项目名称
     */
    private String redAnalyzeItemName;


    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 排序号（默认为0）
     */
    private Integer orderNum = 0;

    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 分析方法编号
     */
    private String redCountryStandard;

    /**
     * 分析方法年份
     */
    private String redYearSn;
}
