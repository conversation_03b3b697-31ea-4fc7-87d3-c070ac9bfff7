package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.customer.DtoSampleTypeDefaultGroup;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.SampleTypeGroupCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.SampleTypeGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 样品分组配置
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "样品分组: 样品分组管理服务")
@RestController
@RequestMapping("/api/lim/sampleTypeGroup")
@Validated
public class SampleTypeGroupController extends BaseJpaController<DtoSampleTypeGroup, String, SampleTypeGroupService> {


    /**
     * 样品分组/分组规则获取
     *
     * @param id 样品分组/分组规则id
     * @return 样品分组/分组规则
     */
    @ApiOperation(value = "按主键获取样品分组", notes = "按主键获取样品分组")
    @GetMapping("/{id}")
    public Object getById(@PathVariable(name = "id") String id) {
        RestResponse<DtoSampleTypeGroup> restResponse = new RestResponse<>();
        DtoSampleTypeGroup sampleTypeGroup = service.findOne(id);
        restResponse.setData(sampleTypeGroup);
        restResponse.setRestStatus(StringUtil.isNull(sampleTypeGroup) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResponse;
    }

    /**
     * 样品分组列表
     *
     * @param sampleTypeGroupCriteria 样品分组查询条件
     * @return 样品分组列表
     */
    @ApiOperation(value = "分页动态条件查询样品分组", notes = "分页动态条件查询样品分组")
    @GetMapping("")
    public RestResponse<List<DtoSampleTypeGroup>> findByPage(SampleTypeGroupCriteria sampleTypeGroupCriteria) {
        RestResponse<List<DtoSampleTypeGroup>> restResponse = new RestResponse<>();
        PageBean<DtoSampleTypeGroup> page = super.getPageBean();
        service.findByPage(page, sampleTypeGroupCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(page.getData());
        restResponse.setCount(page.getRowsCount());

        return restResponse;
    }

    /**
     * 分组规则下获取分组列表
     *
     * @param parentId 分组规则Id
     * @return 样品分组列表
     */
    @ApiOperation(value = "分组规则下获取分组列表", notes = "分组规则下获取分组列表")
    @GetMapping("/getSampleTypeGroupList")
    public RestResponse<List<DtoSampleTypeGroup>> getSampleTypeGroupList(String parentId) {
        RestResponse<List<DtoSampleTypeGroup>> restResponse = new RestResponse<>();
        List<DtoSampleTypeGroup> sampleTypeGroupList = service.getSampleTypeGroupList(parentId);
        restResponse.setData(sampleTypeGroupList);
        restResponse.setMsg("操作成功");
        restResponse.setCount(sampleTypeGroupList.size());

        return restResponse;
    }

    /**
     * 样品分组/分组规则新增
     *
     * @param sampleTypeGroup 样品分组/分组规则
     * @return 返回新增的样品分组/分组规则
     */
    @ApiOperation(value = "新增样品分组", notes = "新增样品分组")
    @PostMapping("")
    public RestResponse<DtoSampleTypeGroup> create(@Validated @RequestBody DtoSampleTypeGroup sampleTypeGroup) {
        RestResponse<DtoSampleTypeGroup> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoSampleTypeGroup data = service.save(sampleTypeGroup);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 样品分组/分组规则更新
     *
     * @param sampleTypeGroup 样品分组/分组规则
     * @return 返回修改的样品分组/分组规则
     */
    @ApiOperation(value = "更新样品分组", notes = "更新样品分组")
    @PutMapping("")
    public RestResponse<DtoSampleTypeGroup> update(@Validated @RequestBody DtoSampleTypeGroup sampleTypeGroup) {
        RestResponse<DtoSampleTypeGroup> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoSampleTypeGroup data = service.update(sampleTypeGroup);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 样品分组/分组规则删除
     *
     * @param id 样品分组/分组规则id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除分组", notes = "根据id删除分组")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.deleteById(id);

        return restResponse;
    }

    /**
     * 取消分组
     *
     * @param sampleTypeGroup 样品分组信息
     * @return
     */
    @ApiOperation(value = "取消分组", notes = "取消分组")
    @PostMapping("/cancleSampleTypeGroup")
    public RestResponse<String> cancelSampleTypeGroup(@RequestBody DtoSampleTypeGroup sampleTypeGroup) {

        RestResponse<String> restResponse = new RestResponse<>();
        service.cancelSampleTypeGroup(sampleTypeGroup.getId(), sampleTypeGroup.getTestIds());
        restResponse.setMsg("操作成功！");

        return restResponse;
    }

    /**
     * 设置分组
     *
     * @param sampleTypeGroup 样品分组信息
     * @return
     */
    @ApiOperation(value = "设置分组", notes = "设置分组")
    @PostMapping("/setSampleTypeGroup")
    public RestResponse<String> setSampleTypeGroup(@RequestBody DtoSampleTypeGroup sampleTypeGroup) {

        RestResponse<String> restResponse = new RestResponse<>();
        service.setSampleTypeGroup(sampleTypeGroup.getId(), sampleTypeGroup.getTestIds());
        restResponse.setMsg("操作成功！");

        return restResponse;
    }

    /**
     * 分组规则树
     *
     * @return
     */
    @ApiOperation(value = "分组规则树", notes = "分组规则树")
    @GetMapping("/tree")
    public RestResponse<List<TreeNode>> getNode() {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setData(service.getNode());
        restResponse.setMsg("操作成功");

        return restResponse;
    }

    /**
     * 分组规则下获取测试项目
     *
     * @param sampleTypeId 分组规则id
     * @return 测试项目集合
     */
    @ApiOperation(value = "分组规则下获取测试项目", notes = "分组规则下获取测试项目")
    @GetMapping("/getTestList")
    public RestResponse<List<DtoTest>> getTestList(String sampleTypeId, String sampleTypeGroupId, Integer isGroup,
                                                   String analyzeItem, String analyzeMethodStd, Integer cert) {
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        List<DtoTest> testList = service.getTestList(sampleTypeId, sampleTypeGroupId, isGroup, analyzeItem,  analyzeMethodStd, cert);
        restResponse.setData(testList);
        restResponse.setMsg("操作成功");
        restResponse.setCount(testList.size());

        return restResponse;
    }

    /**
     * 分组下获取测试项目
     *
     * @param sampleTypeId 分组id
     * @return 测试项目集合
     */
    @ApiOperation(value = "分组下获取测试项目", notes = "分组下获取测试项目")
    @GetMapping("/testGroup")
    public RestResponse<List<DtoTest>> findTestByGroupId(String sampleTypeId, String sampleTypeGroupId, String anaNameMtdStd, Integer cert) {
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        List<DtoTest> testList = service.findTestByGroupId(sampleTypeId, sampleTypeGroupId, anaNameMtdStd, cert);
        restResponse.setData(testList);
        restResponse.setMsg("操作成功");
        restResponse.setCount(testList.size());

        return restResponse;
    }

    /**
     * 根据检测类型大类id列表获取检测类型默认的标签分组
     *
     * @param bigSampleTypeIdList 检测类型大类id列表
     * @return 返回检测类型
     */
    @ApiOperation(value = "根据检测类型大类id列表获取检测类型默认的标签分组", notes = "根据检测类型大类id列表获取检测类型默认的标签分组")
    @PostMapping("/getDefaultGroup")
    public RestResponse<List<DtoSampleTypeDefaultGroup>> findDefaultSampleGroup(@RequestBody List<String> bigSampleTypeIdList) {
        RestResponse<List<DtoSampleTypeDefaultGroup>> restResponse = new RestResponse<>();
        List<DtoSampleTypeDefaultGroup> resMap = service.findDefaultSampleGroup(bigSampleTypeIdList);
        restResponse.setData(resMap);
        restResponse.setRestStatus(StringUtil.isNull(resMap) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 复制样品分组
     *
     * @param sampleTypeGroup 数据
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "复制样品分组", notes = "复制样品分组")
    @PostMapping("/copySampleTypeGroup")
    public RestResponse<Void> copySampleTypeGroup(@Validated @RequestBody DtoSampleTypeGroup sampleTypeGroup) {
        RestResponse<Void> response = new RestResponse<>();
        service.copySampleTypeGroup(sampleTypeGroup.getId());
        return response;
    }
}