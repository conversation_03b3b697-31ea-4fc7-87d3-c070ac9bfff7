package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.ItemRelation;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.List;


/**
 * DtoItemRelation实体
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_ItemRelation")
 @Data
 @DynamicInsert
 public  class DtoItemRelation extends ItemRelation {
   private static final long serialVersionUID = 1L;

   @Transient
   private List<DtoItemRelationParams> itemRelationParamsList;
 }