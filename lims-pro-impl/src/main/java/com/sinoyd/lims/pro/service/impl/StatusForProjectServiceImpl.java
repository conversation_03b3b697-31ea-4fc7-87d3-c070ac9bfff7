package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectPlan;
import com.sinoyd.lims.pro.dto.DtoStatusForProject;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.ProjectPlanRepository;
import com.sinoyd.lims.pro.repository.StatusForProjectRepository;
import com.sinoyd.lims.pro.service.StatusForProjectService;
import com.sinoyd.lims.probase.service.impl.StatusForProjectBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 项目状态操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
 @Service
public class StatusForProjectServiceImpl extends StatusForProjectBaseServiceImpl implements StatusForProjectService {

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    private StatusForProjectRepository statusForProjectRepository;

    @Autowired
    private ProjectPlanRepository projectPlanRepository;

    /**
     * 修改状态数据
     *
     * @param from    状态起
     * @param to      状态止
     * @param sign    工作流信号对象
     * @param project 项目实体
     */
    @Transactional
    @Override
    public void modifyStatus(String from, String to, DtoWorkflowSign sign, DtoProject project) {
        //读取该项目的所有状态数据并转为map
        List<DtoStatusForProject> statusList = statusForProjectRepository.findByProjectId(project.getId());
        Map<String, DtoStatusForProject> statusMap = statusList.stream().collect(Collectors.toMap(DtoStatusForProject::getModule, status -> status));

        EnumProjectStatus fromStatus = EnumProjectStatus.getByName(from);
        EnumProjectStatus toStatus = EnumProjectStatus.getByName(to);

        if (StringUtil.isNotNull(toStatus)) {
            if (toStatus.equals(EnumProjectStatus.已办结)) {
                this.finish(statusList);
                //这边不定义常量数组进行处理了，把涉及的情形进行穷举
            } else if (toStatus.getValue() >= EnumProjectStatus.开展中.getValue()) {
                this.launch(statusMap, project, fromStatus, toStatus, sign);
            } else {//任务置为开展中之前的，由于状态无法从开展中退回到开展中之前，故可以认为是开展中之前的状态的起与始
                this.flow(statusMap, project, fromStatus, toStatus, sign);
            }
        }
    }

    /**
     * 置为办结
     *
     * @param statusList 项目状态信息列表
     */
    protected void finish(List<DtoStatusForProject> statusList) {
        //任务置为办结，则所有流程中待处理的状态置为已处理
        for (DtoStatusForProject status : statusList) {
            if (status.getStatus().equals(EnumStatus.待处理.getValue())) {
                status.setStatus(EnumStatus.已处理.getValue());
                comRepository.merge(status);
            }
        }
    }

    /**
     * 置为开展
     *
     * @param statusMap  项目状态信息Map
     * @param project    项目实体
     * @param fromStatus 状态起
     * @param toStatus   状态止
     * @param sign       工作流信号对象
     */
    protected void launch(Map<String, DtoStatusForProject> statusMap, DtoProject project, EnumProjectStatus fromStatus, EnumProjectStatus toStatus, DtoWorkflowSign sign) {
        //任务置为开展中，需判断编制报告、任务办结的状态
        DtoProjectPlan projectPlan = projectPlanRepository.findByProjectId(project.getId());
        if (statusMap.containsKey(EnumLIM.EnumProjectModule.报告管理.getCode())) {
            DtoStatusForProject sfp = statusMap.get(EnumLIM.EnumProjectModule.报告管理.getCode());
            Integer status = super.getReportStatus(project);
            if (StringUtil.isNotNull(status) && !sfp.getStatus().equals(status)) {
                sfp.setStatus(status);
                comRepository.merge(sfp);
            }
        } else {
            DtoStatusForProject sfp = new DtoStatusForProject();
            sfp.setStatus(super.getReportStatus(project));
            sfp.setProjectId(project.getId());
            sfp.setModule(EnumLIM.EnumProjectModule.报告管理.getCode());
            repository.save(sfp);
        }

        if (statusMap.containsKey(EnumLIM.EnumProjectModule.任务办结.getCode())) {
            DtoStatusForProject sfp = statusMap.get(EnumLIM.EnumProjectModule.任务办结.getCode());
            Integer status = super.getProjectEndStatus(project);
            if (StringUtil.isNotNull(status) && !sfp.getStatus().equals(status)) {
                sfp.setStatus(status);
                comRepository.merge(sfp);
            }
        } else {
            DtoStatusForProject sfp = new DtoStatusForProject();
            sfp.setStatus(super.getProjectEndStatus(project));
            sfp.setProjectId(project.getId());
            sfp.setModule(EnumLIM.EnumProjectModule.任务办结.getCode());
            repository.save(sfp);
        }

        Integer prepareStatus = super.getPrepareStatus(project, projectPlan);
        //如果不存在采样准备的状态，这里进行冗余删除
        if (StringUtil.isNull(prepareStatus)) {
            if (statusMap.containsKey(EnumLIM.EnumProjectModule.采样准备.getCode())) {
                repository.logicDeleteById(statusMap.get(EnumLIM.EnumProjectModule.采样准备.getCode()).getId());
            }
        } else {
            //若已存在采样准备的状态信息
            if (statusMap.containsKey(EnumLIM.EnumProjectModule.采样准备.getCode())) {
                DtoStatusForProject sfp = statusMap.get(EnumLIM.EnumProjectModule.采样准备.getCode());
                if (!sfp.getStatus().equals(prepareStatus)) {
                    sfp.setStatus(prepareStatus);
                    comRepository.merge(sfp);
                }
            } else {
                DtoStatusForProject sfp = new DtoStatusForProject();
                if (StringUtil.isEmpty(sfp.getNextPersonId())) {
                    sfp.setNextPersonId(UUIDHelper.GUID_EMPTY);
                }
                sfp.setStatus(prepareStatus);
                sfp.setProjectId(project.getId());
                sfp.setModule(EnumLIM.EnumProjectModule.采样准备.getCode());
                sfp.setCurrentPersonId(sign.getNextOperatorId());
                sfp.setCurrentPersonName(sign.getNextOperator());
                sfp.setLastNewOpinion(sign.getOption());
                repository.save(sfp);
            }
        }
        Integer localSendStatus = super.getLocalSendStatus(project, projectPlan);
        //如果不存在委托现场送样的状态，这里进行冗余删除
        if (StringUtil.isNull(localSendStatus)) {
            if (statusMap.containsKey(EnumLIM.EnumProjectModule.委托现场送样.getCode())) {
                this.logicDeleteById(statusMap.get(EnumLIM.EnumProjectModule.委托现场送样.getCode()).getId());
            }
        } else {
            //若已存在委托现场送样的状态信息
            if (statusMap.containsKey(EnumLIM.EnumProjectModule.委托现场送样.getCode())) {
                DtoStatusForProject sfp = statusMap.get(EnumLIM.EnumProjectModule.委托现场送样.getCode());
                if (!sfp.getStatus().equals(localSendStatus)) {
                    sfp.setStatus(localSendStatus);
                    comRepository.merge(sfp);
                }
            } else {
                DtoStatusForProject sfp = new DtoStatusForProject();
                sfp.setStatus(localSendStatus);
                sfp.setProjectId(project.getId());
                sfp.setModule(EnumLIM.EnumProjectModule.委托现场送样.getCode());
                sfp.setCurrentPersonId(sign.getNextOperatorId());
                sfp.setCurrentPersonName(sign.getNextOperator());
                sfp.setLastNewOpinion(sign.getOption());
                repository.save(sfp);
            }
        }

        if (fromStatus.getValue() < toStatus.getValue()) {
            //来源状态于开展中之前的，则需要置为已处理，同时判断采样准备、委托现场送样的状态
            String fromModule = this.getModule(fromStatus, project);
            DtoStatusForProject fsfp = statusMap.get(fromModule);
            //置为已处理
            fsfp.setStatus(EnumStatus.已处理.getValue());
            fsfp.setNextPersonId(sign.getNextOperatorId());
            fsfp.setNextPersonName(sign.getNextOperator());
            fsfp.setLastNewOpinion(sign.getOption());
            comRepository.merge(fsfp);
        }
        repository.flush();
    }

    /**
     * 开展前流转
     *
     * @param statusMap  项目状态信息Map
     * @param project    项目实体
     * @param fromStatus 状态起
     * @param toStatus   状态止
     * @param sign       工作流信号对象
     */
    protected void flow(Map<String, DtoStatusForProject> statusMap, DtoProject project, EnumProjectStatus fromStatus, EnumProjectStatus toStatus, DtoWorkflowSign sign) {
        String fromModule = StringUtil.isNull(fromStatus) ? "" : this.getModule(fromStatus, project);
        String toModule = StringUtil.isNull(toStatus) ? "" : this.getModule(toStatus, project);

        //若状态起在状态止之后，则不需要修改状态起的状态表数据，直接删除即可，否则需将状态起的状态表数据改为已处理
        if (StringUtils.isNotNullAndEmpty(fromModule) && fromStatus.getValue() > toStatus.getValue()) {
            fromModule = "";
        }

        if (StringUtil.isNotEmpty(fromModule)) {
            //默认存在当前状态对应的状态数据
            DtoStatusForProject status = statusMap.get(fromModule);
            //最新意见置空
            status.setLastNewOpinion("");
            status.setNextPersonId(sign.getNextOperatorId());
            if (!UUIDHelper.GUID_EMPTY.equals(sign.getNextOperatorId())) {
                status.setNextPersonName(sign.getNextOperator());
            }
            status.setStatus(EnumStatus.已处理.getValue());
            comRepository.merge(status);
        } else {//若来源模块为空，说明为退回，需移除状态
            this.removeStatus(toModule, statusMap);
        }

        //若存在状态止的待处理模块，需修改对应状态数据
        if (StringUtil.isNotEmpty(toModule)) {
            //若数据库中存在该条数据
            if (statusMap.containsKey(toModule)) {
                DtoStatusForProject status = statusMap.get(toModule);
                status.setStatus(EnumStatus.待处理.getValue());
                status.setNextPersonId(UUIDHelper.GUID_EMPTY);
                status.setNextPersonName("");
                status.setLastNewOpinion(sign.getOption());
                comRepository.merge(status);
            } else {
                DtoStatusForProject status = new DtoStatusForProject();
                status.setProjectId(project.getId());
                status.setModule(toModule);
                status.setStatus(EnumStatus.待处理.getValue());
                if (!UUIDHelper.GUID_EMPTY.equals(sign.getNextOperatorId())) {
                    status.setCurrentPersonId(sign.getNextOperatorId());
                    status.setCurrentPersonName(sign.getNextOperator());
                }
                status.setLastNewOpinion(sign.getOption());
                repository.save(status);
            }
        }
        //TODO: 临时加，后面要去掉, 鲍寅初
        comRepository.flush();
        repository.flush();
    }

    /**
     * 根据到达的模块状态删除该状态之后的数据
     *
     * @param toModule  到达的状态模块
     * @param statusMap 状态数据map
     */
    private void removeStatus(String toModule, Map<String, DtoStatusForProject> statusMap) {
        Map<String, Integer> enumMap = new HashMap<>();
        for (EnumLIM.EnumProjectModule m : EnumLIM.EnumProjectModule.values()) {
            enumMap.put(m.getCode(), m.getValue());
        }
        Integer toModuleVal = enumMap.getOrDefault(toModule, -1);
        for (String key : enumMap.keySet()) {
            if (statusMap.containsKey(key) && enumMap.get(key) > toModuleVal) {
                repository.delete(statusMap.get(key).getId());
            }
        }
    }

    /**
     * 根据项目状态枚举获取对应所处模块（仅获取开展中之前的）
     *
     * @param status  项目状态枚举值
     * @param project 项目实体
     */
    protected String getModule(EnumProjectStatus status, DtoProject project) {
        switch (status) {
            case 项目登记中:
            case 项目退回中:
            case 审核未通过:
                String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
                return projectTypeCode.equals(EnumProjectType.现场类.getValue()) ? EnumLIM.EnumProjectModule.现场任务.getCode() : EnumLIM.EnumProjectModule.项目登记.getCode();

            case 技术审核中:
                return EnumLIM.EnumProjectModule.技术审核.getCode();

            case 项目下达中:
                return EnumLIM.EnumProjectModule.项目下达.getCode();

            case 方案编制中:
            case 方案未通过:
                return EnumLIM.EnumProjectModule.方案编制.getCode();

            case 方案审核中:
                return EnumLIM.EnumProjectModule.方案审核.getCode();

            case 方案确认中:
                return EnumLIM.EnumProjectModule.方案确认.getCode();

            default:
                return "";
        }
    }
}