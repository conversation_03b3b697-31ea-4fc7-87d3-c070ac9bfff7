package com.sinoyd.lims.pro.service.impl;

import java.util.*;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoContract;
import com.sinoyd.lims.lim.dto.lims.DtoOAContract;
import com.sinoyd.lims.lim.entity.Contract;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.ContractService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.service.OAContractService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合同审批业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-02
 * @since V100R001
 */
@Service("OAContractServiceImpl")
public class OAContractServiceImpl implements OAContractService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    /**
     * 审批合同相关服务
     */
    @Autowired
    @Lazy
    private com.sinoyd.lims.lim.service.OAContractService oaContractService;
    /**
     * 合同服务
     */
    @Autowired
    @Lazy
    private ContractService contractService;

    @Autowired
    private CommonRepository comRepository;

    @Override
    @Transactional
    public String startProcess(DtoOATaskCreate<DtoOAContract> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.合同审批, taskDto, vars);

        DtoOAContract data = taskDto.getData();
        data.setContractId(data.getId());
        data.setId(UUIDHelper.NewID());
        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(data.getId());

        // 保存合同审批信息
        oaContractService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelation);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<DtoOAContract, String> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<DtoOAContract, String> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(taskId);
        // 查询合同申请相关明细
        DtoOAContract detail = oaContractService.findOne(relation.getObjectId());
        taskDetail.setDetail(detail);

        return taskDetail;
    }

    @Override
    public void findCanSponsor(PageBean<Contract> page, Date signBeginDate, Date signEndDate, String key,
            String salesManId, String typeId) {
        // 查询可发起的合同
        StringBuilder sqlBf = new StringBuilder();
        sqlBf.append("SELECT c ");
        sqlBf.append("  FROM Contract c ");
        sqlBf.append(" WHERE NOT EXISTS ( SELECT r.taskId ");
        sqlBf.append("                      FROM OATask t, OATaskRelation r ");
        sqlBf.append("                     WHERE t.rowGuid = r.taskId ");
        sqlBf.append("                       AND t.procTypeCode = 'contract' ");
        sqlBf.append("                       AND t.dataStatus in (1, 2) ");
        sqlBf.append("                       AND r.objectId = c.rowGuid) ");
        sqlBf.append("   AND isDeleted = 0");

        // SQL条件变量
        Map<String, Object> vars = new LinkedHashMap<>();

        if (StringUtil.isNotNull(signBeginDate)) {
            sqlBf.append(" AND signDate >= :signBeginDate ");
            vars.put("signBeginDate", signBeginDate);
        }

        if (StringUtil.isNotNull(signEndDate)) {
            sqlBf.append(" AND signDate <= :signEndDate ");
            vars.put("signEndDate", signEndDate);
        }

        if (StringUtil.isNotEmpty(key)) {
            sqlBf.append(" AND (contractCode LIKE :key OR contractName LIKE :key) ");
            vars.put("key", "%" + key+ "%");
        }

        if (StringUtil.isNotEmpty(salesManId)) {
            sqlBf.append(" AND salesManId = :salesManId");
            vars.put("salesManId", salesManId);
        }

        if (StringUtil.isNotEmpty(typeId)) {
            sqlBf.append(" AND type = :typeId");
            vars.put("typeId", typeId);
        }

        page.setQuery(sqlBf.toString());

        comRepository.findByPage(page, vars); 
    }

    /**
     * 将审批流程的合同信息覆盖掉财务下相关的合同信息
     * @param oaTask 合同审批流程
     */
    @Override
    public void oaContractToContract(DtoOATask oaTask) {
        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(oaTask.getId());
        DtoOAContract detail = oaContractService.findOne(relation.getObjectId());
        DtoContract contract = contractService.findOne(detail.getContractId());
        Boolean isCreate = false;
        if (StringUtil.isNull(contract)) {
            contract = new DtoContract();
            isCreate = true;
        }
        contract.setAddress(detail.getAddress());
        contract.setContractName(detail.getContractName());
        contract.setContractCode(detail.getContractCode());
        contract.setType(detail.getType());
        contract.setPeriod(detail.getPeriod());
        contract.setTotalAmount(detail.getTotalAmount());
        contract.setSalesManId(detail.getSalesManId());
        contract.setSalesManName(detail.getSalesManName());
        contract.setSignDate(detail.getSignDate());
        contract.setTimeBegin(detail.getTimeBegin());
        contract.setTimeEnd(detail.getTimeEnd());
        contract.setEntName(detail.getEntName());
        contract.setLinkMan(detail.getLinkMan());
        contract.setLinkPhone(detail.getLinkPhone());
        contract.setExplains(detail.getExplains());
        contract.setAttentions(detail.getAttentions());
        contract.setRemark(detail.getRemark());
        contract.setStatus(EnumLIM.EnumContractStatus.已签.getValue());
        if (isCreate) {
            contractService.save(contract);
        } else {
            contractService.update(contract);
        }
    }

    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<DtoOAContract> taskDto) {
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.合同审批, taskDto, new HashMap<>());
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<DtoOAContract> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<DtoOAContract> taskDto,DtoOATask oaTask){
        // 保存合同审批信息
        DtoOAContract data = taskDto.getData();
        data.setContractId(data.getId());
        data.setId(UUIDHelper.NewID());
        oaContractService.save(data);
        // 添加审批任务关联信息
        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(data.getId());
        oaTaskRelationService.save(taskRelation);
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<DtoOAContract> taskDto) {
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }
}
