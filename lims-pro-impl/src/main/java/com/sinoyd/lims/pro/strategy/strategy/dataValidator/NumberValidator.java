package com.sinoyd.lims.pro.strategy.strategy.dataValidator;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.util.MathUtil;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 数字校验
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/09/23
 */
@Component(IFileNameConstant.DataValidateStrategyKey.NUMBER_VALIDATE)
public class NumberValidator extends AbsDataValidator {

    @Override
    public Boolean validate(Object value, Map<String, Object> map) {
        String valStr = StringUtil.isNotNull(value) ? value.toString() : "";
        if (StringUtil.isEmpty(valStr)) {
            return true;
        }
        return MathUtil.isNumeral(value);
    }

    @Override
    public Integer getControlType() {
        return 3;
    }
}
