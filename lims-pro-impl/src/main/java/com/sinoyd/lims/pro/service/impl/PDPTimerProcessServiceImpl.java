package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoPollutionDischargeSync;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.base.service.PollutionDischargeSyncService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.service.PDPMonitorDataService;
import com.sinoyd.lims.pro.service.PDPTimerProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * （PDP:PollutantDischargePermit）排污许可证自行检测数据定时服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0 2025/05/06
 * @since V100R001
 */
@Service
public class PDPTimerProcessServiceImpl implements PDPTimerProcessService {

    private PDPMonitorDataService pdpMonitorDataService;

    private EnterpriseService enterpriseService;

    private PollutionDischargeSyncService pollutionDischargeSyncService;

    @Override
    @Transactional
    public void syncPointMonitorData() {
        //获取到所有的包含排污许可证编号的企业信息
        List<DtoEnterprise> enterpriseList = enterpriseService.findPollutionDischargeCodeList();
        //过滤掉同步成功的数据
        enterpriseList = enterpriseList.stream()
                .filter(e -> !e.getIsSyncPollutionDischarge())
                .collect(Collectors.toList());
        //查询相关同步信息
        List<String> enterpriseIds = enterpriseList.stream().map(DtoEnterprise::getId).collect(Collectors.toList());
        List<DtoPollutionDischargeSync> syncList = pollutionDischargeSyncService.findByEnterpriseIdIn(enterpriseIds);
        //需要做同步的数据
        List<DtoEnterprise> syncEntList = new ArrayList<>();
        for (DtoEnterprise enterprise : enterpriseList) {
            if (syncList.stream().noneMatch(s -> s.getEnterpriseId().equals(enterprise.getId()))) {
                syncEntList.add(enterprise);
            }
        }
        if (StringUtil.isNotEmpty(syncEntList)) {
            //选取前10条数据进行同步
            List<DtoEnterprise> syncEntListLimit = syncEntList.stream().limit(10).collect(Collectors.toList());
            List<DtoPollutionDischargeSync> saveSyncList = pdpMonitorDataService.doBatchRequest(syncEntListLimit);
            //保存同步相关数据
            pollutionDischargeSyncService.save(saveSyncList);
            //更新企业同步状态
            pdpMonitorDataService.updateEntSyncStatus(syncEntList, saveSyncList);
        }
    }

    @Override
    @Transactional
    public void syncUnSuccessfulData() {
        //查询已经有同步数据但未同步成功的数据
        List<DtoPollutionDischargeSync> syncList = pollutionDischargeSyncService.findUnSuccessList();
        if (StringUtil.isNotEmpty(syncList)) {
            //选取前10条数据进行同步
            List<DtoPollutionDischargeSync> syncListLimit = syncList.stream().limit(10).collect(Collectors.toList());
            //获取相关企业数据
            List<String> enterpriseIds = syncListLimit.stream().map(DtoPollutionDischargeSync::getEnterpriseId).collect(Collectors.toList());
            List<DtoEnterprise> enterpriseList = enterpriseService.findAll(enterpriseIds);
            //数据同步
            List<DtoPollutionDischargeSync> saveSyncList = pdpMonitorDataService.doBatchRequest(enterpriseList);
            pollutionDischargeSyncService.save(saveSyncList);
            //处理企业同步状态
            pdpMonitorDataService.updateEntSyncStatus(enterpriseList, saveSyncList);
        }
    }

    @Autowired
    public void setPdpMonitorDataService(PDPMonitorDataService pdpMonitorDataService) {
        this.pdpMonitorDataService = pdpMonitorDataService;
    }

    @Autowired
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }

    @Autowired
    public void setPollutionDischargeSyncService(PollutionDischargeSyncService pollutionDischargeSyncService) {
        this.pollutionDischargeSyncService = pollutionDischargeSyncService;
    }
}
