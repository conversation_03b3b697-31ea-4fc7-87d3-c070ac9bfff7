package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 送样单查询实体
 * <AUTHOR>
 * @version V1.0.0 2020/3/3
 * @since V100R001
 */
@Data
public class DtoReceiveSampleRecordQuery {
    /**
     * 送样单id
     */
    private String id;

    /**
     * 实验室领样单id
     */
    private String anaSubId = UUIDHelper.GUID_EMPTY;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 送样单号
     */
    private String recordCode;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 送样时间
     */
    private Date sendTime;

    /**
     * 送样人（内部人员）ID
     */
    private String senderId;

    /**
     * 送样人（采样负责人）
     */
    private String senderName;

    /**
     * 登记时间
     */
    private Date receiveTime;

    /**
     * 登记人id
     */
    private String recorderId;

    /**
     * 接样类型（枚举EnumReceiveType：1.内部 2.外部 3.现场 4.）
     */
    private Integer receiveType;

    /**
     * 送样单状态
     */
    private String status;

    /**
     * 送样单状态
     */
    private Integer receiveStatus;

    /**
     * 信息状态
     */
    private Integer infoStatus;

    /**
     * 复核人
     */
    private String checkerId;

    /**
     * 检测类型id
     */
    private String sampleTypeIds;

    /**
     * 检测类型名称
     */
    private String sampleTypeNames;

    /**
     * 流水编号
     */
    private String projectCode;

    /**
     * 项目类型id（外键）
     */
    private String projectTypeId;


    /**
     * 项目类型
     */
    private String projectTypeName;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 登记人id
     */
    private String inceptPersonId;

    /**
     * 委托时间
     */
    private Date inceptTime;

    /**
     * 是否着重关注
     */
    private Boolean isStress;

    /**
     * 项目等级(EnumProjectGrade：0.一般 1.紧急 2.特急)
     */
    private Integer grade;

    /**
     * 委托单位id
     */
    private String customerId;

    /**
     * 委托单位
     */
    private String customerName;

    /**
     * 受检单位Id
     */
    private String inspectedEntId;

    /**
     * 受检方联系人
     */
    private String inspectedLinkMan;

    /**
     * 受检方联系电话
     */
    private String inspectedLinkPhone;

    /**
     * 受检方地址
     */
    private String inspectedAddress;

    /**
     * 受检单位
     */
    private String inspectedEnt;

    /**
     * 法人代表
     */
    private String customerOwner;

    /**
     * 地址
     */
    private String customerAddress;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 电话
     */
    private String linkPhone;

    /**
     * 电子邮件
     */
    private String linkEmail;

    /**
     * 传真
     */
    private String linkFax;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 送样状态
     */
    private Integer sendStatus;

    /**
     * 送样状态提示
     */
    private String sendStatusTip = "";

    /**
     * 交接状态
     */
    private Integer innerStatus;

    /**
     * 交接状态提示
     */
    private String innerStatusTip = "";

    /**
     * 分配状态
     */
    private Integer assignStatus;

    /**
     * 分配状态提示
     */
    private String assignStatusTip = "";

    /**
     * 数据状态
     */
    private Integer dataStatus;

    /**
     * 数据状态提示
     */
    private String dataStatusTip = "";

    /**
     * 该构造函数主要用到 ReceiveSampleRecordServiceImpl 下面 queryByPage 方法，如果修改，对应的应用地方也要调整
     */
    public DtoReceiveSampleRecordQuery(String id, String projectId, String recordCode, Date samplingTime, Date sendTime, String senderId,
                                       String senderName, Date receiveTime, String recorderId, Integer receiveType, String status, Integer receiveStatus,
                                       Integer infoStatus,String checkerId, String sampleTypeIds, String projectCode, String projectTypeId, String projectName,
                                       String inceptPersonId, Date inceptTime, Boolean isStress, Integer grade, String customerId, String customerName,
                                       String inspectedEntId, String inspectedLinkMan, String inspectedLinkPhone, String inspectedAddress,
                                       String inspectedEnt, String customerOwner, String customerAddress, String linkMan, String linkPhone,
                                       String linkEmail, String linkFax, String zipCode) {
        this.id = id;
        this.projectId = projectId;
        this.recordCode = recordCode;
        this.samplingTime = samplingTime;
        this.sendTime = sendTime;
        this.senderId = senderId;
        this.senderName = senderName;
        this.receiveTime = receiveTime;
        this.recorderId = recorderId;
        this.receiveType = receiveType;
        this.status = status;
        this.receiveStatus = receiveStatus;
        this.infoStatus = infoStatus;
        this.checkerId = checkerId;
        this.sampleTypeIds = sampleTypeIds;
        this.projectCode = projectCode;
        this.projectTypeId = projectTypeId;
        this.projectName = projectName;
        this.inceptPersonId = inceptPersonId;
        this.inceptTime = inceptTime;
        this.isStress = isStress;
        this.grade = grade;
        this.customerId = customerId;
        this.customerName = customerName;
        this.inspectedEntId = inspectedEntId;
        this.inspectedLinkMan = inspectedLinkMan;
        this.inspectedLinkPhone = inspectedLinkPhone;
        this.inspectedAddress = inspectedAddress;
        this.inspectedEnt = inspectedEnt;
        this.customerOwner = customerOwner;
        this.customerAddress = customerAddress;
        this.linkMan = linkMan;
        this.linkPhone = linkPhone;
        this.linkEmail = linkEmail;
        this.linkFax = linkFax;
        this.zipCode = zipCode;
    }
}
