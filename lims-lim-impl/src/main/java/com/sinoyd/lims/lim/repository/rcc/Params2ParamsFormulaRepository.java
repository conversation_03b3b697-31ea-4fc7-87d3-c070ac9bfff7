package com.sinoyd.lims.lim.repository.rcc;


import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoParams2ParamsFormula;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Params2ParamsFormula数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
public interface Params2ParamsFormulaRepository extends IBaseJpaRepository<DtoParams2ParamsFormula, String> {

    /**
     * 根据查询查询相关的记录单
     *
     * @param paramsConfigIds 参数ids
     * @return 返回数据
     */
    List<DtoParams2ParamsFormula> findByParamsConfigIdIn(List<String> paramsConfigIds);


    /**
     * 查询记录单某个参数某个测试项目公式是否配置数据
     *
     * @param recordId       记录id
     * @param objectId       公式id
     * @param paramsConfigId 参数配置id
     * @return 返回数据
     */
    List<DtoParams2ParamsFormula> findByRecordIdAndObjectIdAndParamsConfigId(String recordId, String objectId, String paramsConfigId);


    /**
     * 查询记录单某个参数某个测试项目公式是否配置数据
     *
     * @param recordId       记录id
     * @param objectIdIn     公式ids
     * @param paramsConfigId 参数配置id
     * @return 返回数据
     */
    List<DtoParams2ParamsFormula> findByRecordIdAndObjectIdInAndParamsConfigId(String recordId, List<String> objectIdIn, String paramsConfigId);

    /**
     * 根据记录及对象id获取相关数据
     *
     * @param recordIds  记录单ids
     * @param objectIdIn 对象ids
     * @return 返回数据
     */
    List<DtoParams2ParamsFormula> findByRecordIdInAndObjectIdInAndIsDeletedFalse(List<String> recordIds, List<String> objectIdIn);

    /**
     * 根据记录及对象id获取相关数据
     *
     * @param recordIds  记录单ids
     * @param objectIdIn 对象ids
     * @return 返回数据
     */
    List<DtoParams2ParamsFormula> findByRecordIdInAndObjectIdIn(List<String> recordIds, List<String> objectIdIn);

    /**
     * 查询该记录单下这个参数数据是否配置全
     *
     * @param recordId       记录单id
     * @param paramsConfigId 参数id
     * @return 返回数据
     */
    List<DtoParams2ParamsFormula> findByRecordIdAndParamsConfigId(String recordId, String paramsConfigId);


    /**
     * 按记录单的id查询所有的参数配置
     *
     * @param recordId 记录单id
     * @return 返回所有的参数数据
     */
    List<DtoParams2ParamsFormula> findByRecordId(String recordId);


    /**
     * 按记录单的id查询所有的参数配置（批量）
     *
     * @param recordIds 对象ids
     * @return 返回数据
     */
    List<DtoParams2ParamsFormula> findByRecordIdIn(List<String> recordIds);


    /**
     * 复制数据的时候需要将原先的数据进行清除
     *
     * @param ids 主键ids
     */
    @Transactional
    @Modifying
    @Query("delete  from DtoParams2ParamsFormula as a where  a.id in :ids")
    Integer deleteByIds(@Param("ids") List<String> ids);


    /**
     * 删除某个测试项目关联之后，需要将数据删除
     *
     * @param recordId  记录单id
     * @param objectIds 对象ids
     */
    @Transactional
    @Modifying
    @Query("delete  from DtoParams2ParamsFormula as a where a.recordId=:recordId and a.objectId in :objectIds")
    Integer deleteByRecordIdAndObjectIdIn(@Param("recordId") String recordId, @Param("objectIds") List<String> objectIds);
}