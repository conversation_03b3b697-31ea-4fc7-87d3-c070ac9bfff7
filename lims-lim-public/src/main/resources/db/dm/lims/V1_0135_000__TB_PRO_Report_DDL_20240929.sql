-- --------------------------------------------------------
-- 报告表中添加扣发状态字段
-- --------------------------------------------------------
ALTER TABLE TB_PRO_Report
    ADD COLUMN depriveStatus INT NOT NULL DEFAULT 0;
COMMENT
ON COLUMN TB_PRO_Report.depriveStatus IS '扣发状态(0:未扣发 1:已扣发)';

-- --------------------------------------------------------
-- 创建报告扣发表
-- --------------------------------------------------------
CREATE TABLE TB_PRO_ReportDeprive
(
    id            varchar(50)   NOT NULL,
    projectId     varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    reportIds     varchar(1000) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    deprivePerson varchar(50) NULL DEFAULT NULL,
    depriveTime   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    depriveReason varchar(255) NULL DEFAULT NULL,
    orgId         varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator       varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate    datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    domainId      varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier      varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate    datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    PRIMARY KEY (id)
);

COMMENT
ON TABLE TB_PRO_ReportDeprive IS '报告扣发表';

COMMENT
ON COLUMN TB_PRO_ReportDeprive.id           IS '主键id';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.projectId    IS '项目id';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.reportIds    IS '报告id';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.deprivePerson IS '扣发人';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.depriveTime  IS '扣发日期';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.depriveReason IS '扣发理由';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.orgId        IS '组织机构id';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.creator      IS '创建人';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.createDate   IS '创建时间';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.domainId     IS '所属实验室';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.modifier     IS '修改人';
COMMENT
ON COLUMN TB_PRO_ReportDeprive.modifyDate   IS '修改时间';