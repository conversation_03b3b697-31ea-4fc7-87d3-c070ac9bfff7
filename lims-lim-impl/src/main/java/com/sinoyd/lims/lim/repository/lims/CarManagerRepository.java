package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoCarManage;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 车辆管理仓储
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
public interface CarManagerRepository extends IBaseJpaPhysicalDeleteRepository<DtoCarManage, String> {

    /**
     * 新增时判断是否存在同名车辆
     * 
     * @param carCode 车牌号
     * @param id 车辆id
     * @return 相同名称的参数数量
     */
    @Query("select count(p.id) from DtoCarManage p where p.carCode = :carCode and p.id != :id")
    Integer getCountByCode(@Param("carCode") String carCode,@Param("id") String id);

}