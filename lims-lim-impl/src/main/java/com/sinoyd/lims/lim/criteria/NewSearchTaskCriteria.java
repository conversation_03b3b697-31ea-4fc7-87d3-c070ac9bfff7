package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 查新任务查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NewSearchTaskCriteria extends BaseCriteria implements Serializable {


    /**
     * 关键字（任务名称）
     */
    private String key;


    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 任务状态
     */
    private Integer status = EnumLIM.EnumNewSearchStatus.所有.getValue();


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and t.taskName like :key");
            values.put("key", "%" + key + "%");
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and t.createDate >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and t.createDate < :endTime");
            values.put("endTime", to);
        }
        if (!EnumLIM.EnumNewSearchStatus.所有.getValue().equals(status)) {
            condition.append(" and t.status = :status");
            values.put("status", status);
        } else {
            condition.append(" and t.status in :status");
            List<Integer> statues = new ArrayList<>();
            statues.add(EnumLIM.EnumNewSearchStatus.待处理.getValue());
            statues.add(EnumLIM.EnumNewSearchStatus.已处理.getValue());
            values.put("status", statues);
        }

        return condition.toString();
    }


}
