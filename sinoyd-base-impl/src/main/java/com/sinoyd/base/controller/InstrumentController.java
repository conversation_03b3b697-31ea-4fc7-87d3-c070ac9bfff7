package com.sinoyd.base.controller;

import java.util.List;

import com.sinoyd.base.criteria.InstrumentCriteria;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 仪器管理-基本信息
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@Api(tags = "仪器基本信息管理")
@RestController
@RequestMapping("/api/base/instrument")
@Validated
public class InstrumentController extends BaseJpaController<DtoInstrument, String, InstrumentService> {

    /**
     * 根据id查询仪器
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询仪器", notes = "根据id查询仪器")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrument> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoInstrument> restResp = new RestResponse<>();

        DtoInstrument instrument = service.findOne(id);
        restResp.setData(instrument);

        restResp.setRestStatus(StringUtil.isNull(instrument) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询仪器
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询仪器", notes = "分页动态条件查询仪器")
    @GetMapping
    public RestResponse<List<DtoInstrument>> findByPage(InstrumentCriteria criteria) {

        RestResponse<List<DtoInstrument>> restResp = new RestResponse<>();

        PageBean<DtoInstrument> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增仪器
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增仪器", notes = "新增仪器")
    @PostMapping
    public RestResponse<DtoInstrument> save(@Validated @RequestBody DtoInstrument entity) {

        RestResponse<DtoInstrument> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrument data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改仪器
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改仪器", notes = "修改仪器")
    @PutMapping
    public RestResponse<DtoInstrument> update(@Validated @RequestBody DtoInstrument entity) {

        RestResponse<DtoInstrument> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrument data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id删除仪器(逻辑删除)
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id删除仪器(逻辑删除)", notes = "根据id删除仪器(逻辑删除)")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除仪器
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除仪器", notes = "批量删除仪器")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 复制仪器
     * @param instrumentId  原仪器rowGuid
     * @param instrumentsCode   新编号
     * @param serialNo  新出厂编号
     * @return
     */
    @ApiOperation(value = "复制仪器", notes = "复制仪器")
    @PostMapping("/copy")
    public RestResponse<DtoInstrument> getById(String instrumentId, String instrumentsCode, String serialNo) {

        RestResponse<DtoInstrument> restResp = new RestResponse<DtoInstrument>();

        DtoInstrument instrument = service.copy(instrumentId, instrumentsCode, serialNo);
        restResp.setData(instrument);

        restResp.setRestStatus(StringUtil.isNull(instrument) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 获取仪器存储路径
     * 
     * @param instrumentId   仪器id
     * @param instrumentName 仪器名称
     * @param type           存储类型
     * @return 保存路径
     */
    @ApiOperation(value = "获取仪器存储路径", notes = "获取仪器存储路径")
    @PostMapping("/getInstrumentPath")
    public RestResponse<String> getInstrumentDocumentPath(String instrumentId, String instrumentName, Integer type) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        String path = "";//service.getInstrumentDocumentPath(instrumentId, instrumentName, type);
        restResp.setData(path);

        return restResp;
    }


    /***
     * 获取制造厂商
     * @return
     */
    @ApiOperation(value = "获取制造厂商", notes = "获取制造厂商")
    @GetMapping("/getFactoryNameList")
    public RestResponse<List<DtoInstrument>> getFactoryNameList() {
        RestResponse<List<DtoInstrument>> restResp = new RestResponse<>();
        List<DtoInstrument> list = service.getFactoryNameList();

        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(list);
        restResp.setCount(list.size());

        return restResp;
    }

    /***
     * 获取溯源单位
     * @return
     */
    @ApiOperation(value = "获取溯源单位", notes = "获取溯源单位")
    @GetMapping("/getOriginUnitList")
    public RestResponse<List<DtoInstrument>> getOriginUnitList(Boolean isStandard) {
        RestResponse<List<DtoInstrument>> restResp = new RestResponse<>();
        List<DtoInstrument> list = service.getOriginUnitList();

        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(list);
        restResp.setCount(list.size());

        return restResp;
    }

    /***
     * 获取所有仪器
     *
     * @return 所有仪器
     */
    @ApiOperation(value = "获取所有仪器", notes = "获取所有仪器")
    @GetMapping("/getAll")
    public RestResponse<List<DtoInstrument>> findAllInstrument(){
        RestResponse<List<DtoInstrument>> response = new RestResponse<>();
        List<DtoInstrument> list = service.findAll();
        response.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        response.setData(list);
        response.setCount(list.size());
        return response;
    }

    /***
     *  仪器过期明细
     *
     * @return 仪器过期明细
     */
    @ApiOperation(value = "仪器过期明细", notes = "仪器过期明细")
    @GetMapping("/overDue")
    public RestResponse<List<DtoInstrument>> getOverDueData(){
        RestResponse<List<DtoInstrument>> response = new RestResponse<>();
        List<DtoInstrument> list = service.getOverDueData();
        response.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        response.setData(list);
        response.setCount(list.size());
        return response;
    }

    /***
     *  仪器即将过期明细
     *
     * @return 仪器过期明细
     */
    @ApiOperation(value = "仪器过期明细", notes = "仪器过期明细")
    @GetMapping("/willOverDue")
    public RestResponse<List<DtoInstrument>> getWillOverDueData(){
        RestResponse<List<DtoInstrument>> response = new RestResponse<>();
        List<DtoInstrument> list = service.getWillOverDueData();
        response.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        response.setData(list);
        response.setCount(list.size());
        return response;
    }

    @ApiOperation(value = "获取当前最大的仪器排序值", notes = "获取当前最大的仪器排序值")
    @GetMapping("/orderNum")
    public RestResponse<Integer> findOrderNum() {
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.getLastOrderNum());
        return response;
    }
}