package com.sinoyd.base.service.impl.divationMethod;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.frame.service.CalculationService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 相对准确度
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
public class RelativeAccuracyServiceImpl implements QualityDivationService {

    @Override
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        String retStr = "";
        if (valueList.size() > 0) {
            CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
            //实验室均值
            String syAvg = valueList.get(0);
            //在线集合
            List<String> zxList = (List<String>) map.get(IBaseConstants.ONLINE_DATA_LIST);
            //实验室集合
            List<String> syList = (List<String>) map.get(IBaseConstants.LABORATORY_DATA_LIST);
            //判断数据偏差是否使用绝对偏差 -- 检查项范围
            String qcRangeLimit = controlLimit.getRangeConfigData();//controlLimit.getRangeConfig();
            Boolean flag = true;
            if (EnumBase.EnumIsCheckItem.是.getValue().equals(controlLimit.getIsCheckItem())) {
                flag = DivationUtils.calculationResult(qcRangeLimit, new BigDecimal(syAvg), calculationService);
            }
            if (flag) {
                //实验室数据个数
                int count = syList.size();
                retStr = "无法计算";
                //实验数据必须大于0
                if (count > 0) {
                    //对差集合
                    List<String> diList = new ArrayList<>();
                    for (int i = 0; i < count; i++) {
                        //存在在线值
                        if (zxList.size() > i) {
                            diList.add(MathUtil.subtract(zxList.get(i), syList.get(i)));
                        }
                    }
                    //对差均值
                    String diAvg = MathUtil.calculateAvg(diList);
                    //标准偏差
                    BigDecimal deviation = DivationUtils.standardDeviationBigDecimal(diList, diAvg);
                    // 标准偏差保留两位小数
                    deviation = deviation.setScale(2, BigDecimal.ROUND_HALF_UP);
//                    String actionValue = ReviseDataFactory.revise(sign, md, deviation.toString(),Boolean.FALSE);
                    BigDecimal zxCoefficient = DivationUtils.confidenceCoefficient(count, deviation.toString());
                    // 置信系数保留两位小数
                    zxCoefficient = zxCoefficient.setScale(2, BigDecimal.ROUND_HALF_UP);
                    //相对准确度 = （|方差均值|+|置信系数|）/实验室均值
                    BigDecimal accuracyRelative = new BigDecimal(diAvg).abs().add(zxCoefficient.abs()).divide(new BigDecimal(syAvg), BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(100));
                    retStr = accuracyRelative.toString();
                }
            }
        }
        map.put("qcRate", retStr);
    }
}
