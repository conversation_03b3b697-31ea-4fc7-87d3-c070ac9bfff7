package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoConsumableOfMixed;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;

/**
 * 混标信息
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
public interface ConsumableOfMixedRepository extends IBaseJpaPhysicalDeleteRepository<DtoConsumableOfMixed, String> {
    /**
     * 根据标准样品id查询数据
     *
     * @param consumableId 标准样品id
     * @return 查询结果
     */
    List<DtoConsumableOfMixed> findByConsumableId(String consumableId);

    /**
     * 根据标准样品id和分析项目id查询数据条数
     *
     * @param consumableId   标准物质id
     * @param analyzeItemId  分析项目id
     * @return 条数
     */
    Integer countByConsumableIdAndAnalyzeItemIdAndIdNot(String consumableId, String analyzeItemId, String id);

    /**
     * 根据标准样品id查询数据
     *
     * @param consumableIds 标准样品id
     * @return 查询结果
     */
    List<DtoConsumableOfMixed> findByConsumableIdIn(List<String> consumableIds);
}