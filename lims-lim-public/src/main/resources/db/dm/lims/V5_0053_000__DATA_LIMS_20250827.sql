-- 仪器设备出入库记录表-按项目 报表模板配置
delete
from tb_lim_reportconfig
where id = '39c3b01d-3af8-4f48-9941-ac0fac262d1b';
INSERT INTO tb_lim_reportconfig (id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                 pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                 modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                 beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('39c3b01d-3af8-4f48-9941-ac0fac262d1b', 1, 'ProjectInstrumentByProRecord', '仪器设备出入库记录表-按项目.xlsx',
        'LIMReportForms/仪器设备出入库记录表-按项目.xlsx', 'output/LIMReportForms/仪器设备出入库记录表-按项目.xlsx',
        'application/excel', 'com.sinoyd.lims.report.service.exports.ProjectInstrumentByProRecordService', '', '', 0, 1,
        '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-08-27 14:50:26',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-08-27 14:53:28',
        'com.sinoyd.lims.lim.criteria.ProjectInstrumentCriteria', 'LIMReportForms', 'ProjectInstrumentByProRecord',
        0, '', NULL, '', '', '', 0, NULL);

delete
from tb_lim_reportapply
where id in ('01feb6db-c22d-406c-9256-cce8c78e712f', 'cfd945d4-21fe-49aa-8448-5ea13693669c');
INSERT INTO tb_lim_reportapply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                                location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('01feb6db-c22d-406c-9256-cce8c78e712f', '39c3b01d-3af8-4f48-9941-ac0fac262d1b', 'InstrumentAccessManage',
        '仪器出入库管理', 'ProjectInstrumentRecord', '导出仪器设备出入库记录表-按项目', 1, 0, 1, '', '',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-08-27 14:51:43',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-08-27 14:51:43', NULL);
INSERT INTO tb_lim_reportapply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                                location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('cfd945d4-21fe-49aa-8448-5ea13693669c', '39c3b01d-3af8-4f48-9941-ac0fac262d1b', 'InstrumentManage',
        '仪器设备管理', 'ProjectInstrumentRecord', '导出仪器设备出入库记录表-按项目', 1, 0, 1, '',
        '仪器设备管理:出入库记录', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-08-27 14:51:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-08-27 14:51:15', NULL);

