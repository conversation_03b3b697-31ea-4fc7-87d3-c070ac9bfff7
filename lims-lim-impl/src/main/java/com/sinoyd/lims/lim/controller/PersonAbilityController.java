package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.PersonAbilityCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;
import com.sinoyd.lims.lim.service.PersonAbilityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 人员检测能力
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Api(tags = "人员检测能力: 人员检测能力服务")
@RestController
@RequestMapping("/api/lim/personAbility")
@Validated
public class PersonAbilityController extends BaseJpaController<DtoPersonAbility, String, PersonAbilityService> {

    /**
     * 分页获取人员检测能力
     *
     * @param criteria 人员检测能力查询条件
     * @return RestResponse<List < DtoPersonAbility>>
     */
    @ApiOperation(value = "分页条件查询人员检测能力", notes = "分页条件查询人员检测能力")
    @GetMapping
    public RestResponse<List<DtoPersonAbility>> findByPage(PersonAbilityCriteria criteria) {
        RestResponse<List<DtoPersonAbility>> restResp = new RestResponse<>();
        PageBean<DtoPersonAbility> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 按主键查询人员检测能力
     *
     * @param id 人员检测能力id
     * @return RestResponse<DtoPersonAbility>
     */
    @ApiOperation(value = "按主键查询人员检测能力", notes = "按主键查询人员检测能力")
    @GetMapping("/{id}")
    public RestResponse<DtoPersonAbility> getById(@PathVariable(name = "id") String id) {
        RestResponse<DtoPersonAbility> restResp = new RestResponse<>();
        DtoPersonAbility personAbility = service.findOne(id);
        restResp.setData(personAbility);
        restResp.setRestStatus(StringUtil.isNull(personAbility) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增人员检测能力
     *
     * @param entitys 人员检测能力实体数组
     * @return RestResponse<DtoPersonAbility>
     */
    @ApiOperation(value = "新增人员检测能力", notes = "新增人员检测能力")
    @PostMapping
    public RestResponse<String> save(@Validated @RequestBody List<DtoPersonAbility> entitys) {
        RestResponse<String> restResp = new RestResponse<>();
        String msg = service.saveList(entitys);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(msg);
        return restResp;
    }

    /**
     * 更新人员检测能力
     *
     * @param entity 人员检测能力实体
     * @return RestResponse<DtoPersonAbility>
     */
    @ApiOperation(value = "更新人员检测能力", notes = "更新人员检测能力")
    @PutMapping
    public RestResponse<DtoPersonAbility> update(@Validated @RequestBody DtoPersonAbility entity) {
        RestResponse<DtoPersonAbility> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoPersonAbility data = service.update(entity);
        restResp.setData(data);

        return restResp;
    }

    /**
     * 根据id删除人员检测能力
     *
     * @param id 人员检测能力id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除人员检测能力", notes = "根据id删除人员检测能力")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        service.delete(id);

        return restResp;
    }

    /**
     * 根据id批量删除人员检测能力
     *
     * @param ids 人员检测能力id集合
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据ids批量删除人员检测能力", notes = "根据ids批量删除人员检测能力")
    @DeleteMapping
    public RestResponse<DtoPersonAbility> delete(@RequestBody List<String> ids) {
        RestResponse<DtoPersonAbility> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        service.logicDeleteById(ids);

        return restResp;
    }

    /**
     * 批量日期修改
     *
     * @param map 批量日期修改参数
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据ids批量删除人员检测能力", notes = "根据ids批量删除人员检测能力")
    @PostMapping("/bachUpdateDate")
    public RestResponse<DtoPersonAbility> bachUpdateDate(@RequestBody Map<String,Object> map) {
        RestResponse<DtoPersonAbility> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        service.bachUpdateDate(map);

        return restResp;
    }

}