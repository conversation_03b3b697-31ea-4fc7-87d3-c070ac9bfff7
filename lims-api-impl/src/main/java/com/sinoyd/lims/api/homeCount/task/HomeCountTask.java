package com.sinoyd.lims.api.homeCount.task;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.api.homeCount.factory.AbsCountSource;
import com.sinoyd.lims.api.service.IHomeCountTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 移动端首页代办处理
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Component
public class HomeCountTask implements IHomeCountTask {

    private final Map<String, AbsCountSource> dataMap = new ConcurrentHashMap<>();

    @Autowired
    public HomeCountTask(Map<String, AbsCountSource> strategyMap) {
        this.dataMap.putAll(strategyMap);
    }

    @Override
    public long homeCount(String beanName) {
        long count = 0;
        AbsCountSource dataSource = this.dataMap.get(beanName);
        if (StringUtil.isNotNull(dataSource)) {
            count = dataSource.homeCount();
        }
        return count;
    }
}
