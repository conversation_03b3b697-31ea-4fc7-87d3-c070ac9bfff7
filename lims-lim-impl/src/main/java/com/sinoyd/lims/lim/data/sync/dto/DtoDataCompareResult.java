package com.sinoyd.lims.lim.data.sync.dto;

import com.sinoyd.frame.base.entity.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 数据比较结果实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/20
 */
@Data
public class DtoDataCompareResult<T extends BaseEntity> {

    /**
     * 比较结果类型: 新增、差异、相同
     */
    private String category;

    /**
     * 数据名称：量纲、分析项目、分析方法等
     */
    private String itemName;

    /**
     * 比较结果记录数
     */
    private int count;

    /**
     * 数据集合(待新增)
     */
    private List<T> addDataList;

    /**
     * 是否必须同步
     */
    private boolean mustSyncFlag;

    /**
     * 排序值
     */
    private Integer orderNum;

    public DtoDataCompareResult() {
    }

    public DtoDataCompareResult(String category,
                                String itemName,
                                int count,
                                boolean mustSyncFlag,
                                Integer orderNum,
                                List<T> addDataList) {
        this.category = category;
        this.itemName = itemName;
        this.count = count;
        this.mustSyncFlag = mustSyncFlag;
        this.orderNum = orderNum;
        this.addDataList = addDataList;
    }
}