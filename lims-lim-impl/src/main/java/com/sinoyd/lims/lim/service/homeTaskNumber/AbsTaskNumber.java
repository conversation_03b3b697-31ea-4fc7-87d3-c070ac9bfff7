package com.sinoyd.lims.lim.service.homeTaskNumber;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 首页代办数字缓存刷新基类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/10/26
 */
@Component
public abstract class AbsTaskNumber {

    protected ProjectTypeService projectTypeService;

    protected StringRedisTemplate redisTemplate;

    protected JdbcTemplate jdbcTemplate;

    /**
     * 刷新缓存
     *
     * @param homeModule 需要刷新的模块
     */
    public void refreshRedis(HomeModule homeModule) {
        //获取到所有的项目类型数据
        List<DtoProjectType> projectTypeList = projectTypeService.findByTypeCode("HJ");
        projectTypeList.addAll(projectTypeService.findByTypeCode("WR"));
        //组织id
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        //当前操作人的人员id
        String userIdLogin = PrincipalContextUser.getPrincipal().getUserId();
        List<String> outTypeIds = new ArrayList<>();
        if (projectTypeList.size() > 0) {
            outTypeIds.addAll(projectTypeList.stream().map(DtoProjectType::getId).distinct().collect(Collectors.toList()));
        }

        //获取数据库中的数量
        List<DtoTaskNum> taskNumList = this.getTaskNum(homeModule, orgId, userIdLogin, outTypeIds);
        //将数量保存到redis中
        if (homeModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
            if (StringUtil.isNotEmpty(taskNumList)){
                for (DtoTaskNum taskNum : taskNumList) {
                    this.saveRedis(homeModule, orgId, taskNum.getUserId(), taskNum.getCount().intValue());
                }
            }else{
                this.removeRedis(homeModule,orgId,userIdLogin);
            }

        } else {
            if (StringUtil.isNotEmpty(taskNumList)){
                int countNum = 0;
                for (DtoTaskNum taskNum : taskNumList) {
                    countNum += taskNum.getCount().intValue();
                }
                this.saveRedis(homeModule, orgId, userIdLogin, countNum);
            }else{
                this.removeRedis(homeModule,orgId,userIdLogin);
            }
        }
    }

    /**
     * 刷新卡片代办数据
     *
     * @param homeModule 模块数据
     */
    public void refreshCardNum(HomeModule homeModule, String orgId, String userId) {

    }

    /**
     * 获取缓存数据集合
     *
     * @param homeModule 模块编码
     * @param orgId      组织Id
     * @param userId     用户Id
     * @param outTypeIds 不包含的项目类型
     * @return 结果集
     */
    public abstract List<DtoTaskNum> getTaskNum(HomeModule homeModule, String orgId, String userId, List<String> outTypeIds);

    /**
     * 保存到redis中
     */
    public void saveRedis(HomeModule homeModule, String orgId, String userId, Integer num) {
        //拼接RedisKey
        String redisKey = getRedisKey(userId, orgId, homeModule.getValue(), getModuleCode());
        //放置value
        Map<String, Integer> map = new HashMap<>();
        map.put("taskNum", num);
        //处理redis数据
        redisTemplate.opsForValue().set(redisKey, JsonStream.serialize(map));
    }

    /**
     * 保存到redis中
     */
    public void removeRedis(HomeModule homeModule, String orgId, String userId) {
        //拼接RedisKey
        String redisKey = getRedisKey(userId, orgId, homeModule.getValue(), getModuleCode());
        //处理redis数据
        redisTemplate.delete(redisKey);
    }

    /**
     * 获取模块名称
     *
     * @return 模块名称
     */
    public abstract String getModuleCode();

    /**
     * 获取指定的rediskey
     *
     * @param userId     用户id
     * @param orgId      组织机构id
     * @param value      value 是按人员还是按权限
     * @param moduleCode 模块编码
     * @return 返回指定key
     */
    private String getRedisKey(String userId, String orgId, Integer value, String moduleCode) {
        String redisKey;
        if (value.equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
            //说明按人员进行过滤
            redisKey = String.format("PRO:%s:%s:%s", orgId, userId, moduleCode);
        } else {
            redisKey = String.format("PRO:%s:%s", orgId, moduleCode);
        }
        return redisKey;
    }

    /**
     * 保存卡片信息
     *
     * @param personId      人员id
     * @param hashKey       haskKey
     * @param countProperty 数据条数字段名
     * @param count         数量
     */
    protected void saveCardInfo(String personId, String hashKey, String countProperty, String detailProperty, Long count) {
        Object json = redisTemplate.opsForHash().get(hashKey, personId);
        //获取
        Map<String, Object> awaitSampleMap = new HashMap<>();
        if (StringUtils.isNotNullAndEmpty(json)) {
            awaitSampleMap = JsonIterator.deserialize((String) json, Map.class);
        }

        List<Map<String, Object>> detailList = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(awaitSampleMap.get(detailProperty))){
            detailList = (List<Map<String, Object>>) awaitSampleMap.get(detailProperty);
        }
        awaitSampleMap.put(countProperty, count);
        awaitSampleMap.put(detailProperty,detailList);
        redisTemplate.opsForHash().put(hashKey, personId, JsonStream.serialize(awaitSampleMap));
    }

    @Autowired
    public void setProjectTypeService(ProjectTypeService projectTypeService) {
        this.projectTypeService = projectTypeService;
    }

    @Autowired
    public void setRedisTemplate(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}
