package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import com.sinoyd.frame.configuration.PrincipalContextUser;


/**
 * SamplingFrequency实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SamplingFrequency")
 @Data
 public  class SamplingFrequency implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  SamplingFrequency() {
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
    * 点位id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("点位id")
	private String sampleFolderId;

    /**
    * 周期
    */
    @Column(nullable=false)
    @ApiModelProperty("周期")
	private Integer periodCount;

    /**
    * 次数
    */
    @Column(nullable=false)
    @ApiModelProperty("次数")
	private Integer timePerPeriod;

    /**
    * 点位类型（枚举EnumFolderType：0.无类型 1.昼间 2.夜间）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("点位类型（枚举EnumFolderType：0.无类型 1.昼间 2.夜间）")
	private Integer folderType;

    /**
    * 每次样品数
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("每次样品数")
	private Integer samplePerTime;

    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;

 }