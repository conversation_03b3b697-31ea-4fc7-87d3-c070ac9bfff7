package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采样单配置关联参数配置管理查询条件
 * <AUTHOR>
 * @version v1.0.0 2022/6/28
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordConfig2ParamsConfigCriteria extends BaseCriteria {

    /**
     * 采样单配置id
     */
    private String recordId;

    /**
     * 查询条件（参数名称，别名）
     */
    private String condition;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and r.paramsConfigId = pc.id and pc.isDeleted = 0");

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.condition)) {
            condition.append(" and (pc.alias like :condition)");
            values.put("condition", "%" + this.condition + "%");
        }

        //对象id
        if (StringUtils.isNotNullAndEmpty(this.recordId) && !UUIDHelper.GUID_EMPTY.equals(this.recordId)) {
            condition.append(" and r.recordConfigId = :recordId ");
            values.put("recordId", this.recordId);
        }
        return condition.toString();
    }
}
