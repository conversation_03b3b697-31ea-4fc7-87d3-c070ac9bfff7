package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluateRow;
import com.sinoyd.lims.pro.dto.customer.DtoQCSample;
import com.sinoyd.lims.pro.dto.customer.DtoQCSampleCensus;
import com.sinoyd.lims.pro.dto.customer.DtoQCSampleDetail;

import java.util.List;

/**
 * 质控数据的统计
 *
 * <AUTHOR>
 * @version V1.0.0 2020/02/11
 * @since V100R001
 */
public interface QCSampleStatisticsService {

    /**
     * 分析查询质控数量
     *
     * @param pageBean 分析条件
     */
    void findByPage(PageBean<DtoQCSample> pageBean, BaseCriteria baseCriteria);

    /**
     * 查询所有质控样数据
     *
     * @param pageBean     分页
     * @param baseCriteria 查询条件
     */
    void findQcSamples(PageBean<DtoQCSampleCensus> pageBean, BaseCriteria baseCriteria);

    /**
     * 根据样品id查询质控评价详细
     *
     * @param sampleId 样品id
     * @return 质控评价详细
     */
    List<List<DtoEvaluateRow>> findDetails(String sampleId);

    /**
     * 获取相关的样品信息
     *
     * @param sampleIds 样品ids
     * @return 返回相关的样品信息
     */
    List<DtoSample> findSamples(List<String> sampleIds);


    /**
     * 找到质控样的详细数据
     *
     * @param sampleId          样品id
     * @param associateSampleId 原样id
     * @return 返回详细数据
     */
    List<DtoQCSampleDetail> findDetails(String sampleId, String associateSampleId);
}
