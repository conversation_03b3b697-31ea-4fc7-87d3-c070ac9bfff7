package com.sinoyd.lims.monitor.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;

import java.util.List;
import java.util.Map;


/**
 * FixedPointProperty操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedPointPropertyService extends IBaseJpaService<DtoFixedPointProperty, String> {

    /**
     * 根据年度查询
     *
     * @param year 年度
     * @return 结果集
     */
    List<DtoFixedPointProperty> loadTree(Integer year);

    /**
     * 选择关联点位
     *
     * @param dtoFixedPointProperty 实体
     */
    void selectRelationPoints(DtoFixedPointProperty dtoFixedPointProperty);

    /**
     * 根据点位id列表查询已选的测试项目信息
     *
     * @param pointIds 点位id列表
     * @return 点位已选的测试项目信息
     */
    List<DtoTest> loadPointTest(List<String> pointIds);

    /**
     * 复制监测计划
     *
     * @param parameter 复制参数
     */
    void copyProperty(Map<String, Object> parameter);

    /**
     * 按照月份批量新增
     * @param fixedPointProperty 监测计划实体
     * @return 完成新增的监测计划
     */
    List<DtoFixedPointProperty> batchSaveByMonths(DtoFixedPointProperty fixedPointProperty);
}