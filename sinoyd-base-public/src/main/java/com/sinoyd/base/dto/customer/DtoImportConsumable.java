package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelEntity;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DtoImportConsumable implements IExcelModel, IExcelDataModel {

    /**
     * 消耗品详细
     */
    @ExcelEntity
    private DtoImportConsumableDetail detail;

    /**
     * 编号
     */
    private String id = UUIDHelper.NewID();

    /**
     * 标样编号
     */
    @Excel(name = "唯一性编号(必填)",orderNum = "10",width = 10,isColumnHidden = true)
    private String consumableCode;

    @Excel(name = "证书编号",orderNum = "2",width = 17,isColumnHidden = true)
    private String standardCodeInStation;

    /**
     * 编号
     */
    @Excel(name = "编号",orderNum = "20",width = 24)
    private String codeInStation;

    /**
     * 消耗品名称
     */
    @Excel(name = "标准样品/消耗品名称(必填)",orderNum = "30",width = 17)
    private String consumableName;

    /**
     * 消耗品名称(消耗品导入用）
     */
    @Excel(name = "消耗品名称(必填)",orderNum = "30",width = 17)
    private String consumableName2;

    /**
     * 消耗品类型
     */
    @Excel(name = "标准样品/消耗品分类(必填)",orderNum = "40",width = 22)
    private String categoryId;

    /**
     * 消耗品类型（消耗品导入用）
     */
    @Excel(name = "消耗品分类(必填)",orderNum = "40",width = 22)
    private String categoryId2;

    /**
     * 规格
     */
    @Excel(name = "规格",orderNum = "50",width = 13)
    private String specification;

    /**
     * 等级
     */
    @Excel(name = "等级",orderNum = "60",width = 13)
    private String grade;

    /**
     * 库存数量
     */
    @Excel(name = "库存数量(必填)",orderNum = "70",width = 15)
    private String inventory;

    /**
     * 单位
     */
    @Excel(name = "单位",orderNum = "80",width = 12)
    private String unit;

    /**
     * 库存警告数量
     */
    @Excel(name = "库存警告数量",orderNum = "90",width = 14)
    private String warningNum;

    @Excel(name = "化合物名称",orderNum = "105",width = 12)
    private String compoundName;

    /**
     * 标值
     */
    @Excel(name = "浓度",width = 10,orderNum = "1000",isColumnHidden = true)
    private String standard;

    /**
     * 不确定度
     */
    @Excel(name = "不确定度",orderNum = "1000",isColumnHidden = true)
    private String uncertainty;

    /**
     * 范围低点
     */
    @Excel(name = "范围低点",orderNum = "1030",isColumnHidden = true)
    private String rangeLow;

    /**
     * 范围高点
     */
    @Excel(name = "范围高点",orderNum = "1060",isColumnHidden = true)
    private String rangeHigh;

    /**
     * 浓度量纲
     */
    @Excel(name = "浓度量纲",orderNum = "1100",isColumnHidden = true)
    private String dimension;

    /**
     * 不确定度类型
     */
    @Excel(name = "不确定度类型",orderNum = "1150",isColumnHidden = true)
    private String uncertainTypeName;

    /**
     * 稀释液
     */
    @Excel(name = "稀释液",orderNum = "1200",isColumnHidden = true)
    private String dilutedSolution;

    /**
     * 稀释方法
     */
    @Excel(name = "稀释方法",orderNum = "1300",isColumnHidden = true)
    private String dilutionMethod;

    /**
     * 提醒人id（Guid）
     */
//    @Excel(name = "管理员",orderNum = "12",width = 18)
    private String sendWarnUserId;

    /**
     * 是否实验室加密
     */
    @Excel(name = "是否实验室加密(必填)",orderNum = "175",isColumnHidden = true)
    private String isLabEncryption;

    /**
     * 提醒人名称
     */
    @Excel(name = "管理员(必填)",orderNum = "180",width = 18)
    private String sendWarnUserName;

    /**
     * 保存条件
     */
    @Excel(name = "保存条件",orderNum = "190",width = 25)
    private String keepCondition;

    /**
     * 安全须知
     */
    @Excel(name = "安全须知",orderNum = "200",width = 22)
    private String safetyInstruction;

    /**
     * 是否为标准样品
     */
    private Boolean isStandard = false;

    /**
     * 校验错误信息
     */
    private Map<String, List<String>> failStr;

    private Integer rowNum;

    private String errorMsg;

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }
}
