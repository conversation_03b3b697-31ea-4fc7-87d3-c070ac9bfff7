package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataProjectVO;
import com.sinoyd.lims.foreign.DataReportBaseInfoVO;
import com.sinoyd.lims.foreign.DataReportFolderSortInfoVO;
import com.sinoyd.lims.foreign.service.DataReportBaseInfoService;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报告基本信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataReportBaseInfoServiceImpl extends AbsQueryDataServiceImpl<DataReportBaseInfoVO> implements DataReportBaseInfoService {

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id,reportId,projectName,systemCode,inspectedEnt,");
        sql.append("inspectedAddress,customerName,customerAddress,orgId,domainId");
        sql.append(" from TB_PRO_ReportBaseInfo ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime", "syncTime");
        super.appendCondition(sql, map, "AND reportId in (:reportIds)", "reportIds");
    }

    @Override
    protected RowMapper<DataReportBaseInfoVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataReportBaseInfoVO reportBaseInfoVO = new DataReportBaseInfoVO();
            reportBaseInfoVO.setId(UUIDHelper.NewID());
            reportBaseInfoVO.setReportId(rs.getString("reportId"));
            reportBaseInfoVO.setProjectName(rs.getString("projectName"));
            reportBaseInfoVO.setSystemCode(rs.getString("systemCode"));
            reportBaseInfoVO.setInspectedEnt(rs.getString("inspectedEnt"));
            reportBaseInfoVO.setInspectedAddress(rs.getString("inspectedAddress"));
            reportBaseInfoVO.setCustomerName(rs.getString("customerName"));
            reportBaseInfoVO.setCustomerAddress(rs.getString("customerAddress"));
            reportBaseInfoVO.setOrgId(rs.getString("orgId"));
            reportBaseInfoVO.setDomainId(rs.getString("domainId"));
            reportBaseInfoVO.setSyncTime(new Date());
            return reportBaseInfoVO;
        };
    }
}
