package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.pro.dto.customer.DtoProjectCountStatistic;
import com.sinoyd.lims.pro.dto.customer.DtoProjectSampleCount;
import com.sinoyd.lims.pro.dto.customer.DtoSampleCountStatistic;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.ProjectCountStatisticsRepository;
import com.sinoyd.lims.pro.service.ProjectCountStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectCountStatisticsServiceImpl implements ProjectCountStatisticsService {

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    private ProjectCountStatisticsRepository projectCountStatisticsRepository;

    @Override
    public Map<String,Object> findProjectCountList(Integer type, String startTime, String endTime) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select ");
        stringBuilder.append("new com.sinoyd.lims.pro.dto.customer.DtoProjectCountStatistic(projectTypeId,projectTypeName,parentName,");
        stringBuilder.append("COUNT(distinct projectId) as projectCount)");
        stringBuilder.append(" from VProjectCountStatistic where 1=1 ");
        stringBuilder = getSelect(stringBuilder, type, startTime, endTime, values);
        stringBuilder.append(" group by projectTypeId,projectTypeName,parentName,orgId order by parentName,projectTypeName asc");
        List<DtoProjectCountStatistic> dataList = commonRepository.find(stringBuilder.toString(), values);
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> countInfo = new HashMap<>();
        Map<String, Integer> countMap = getYYSampleCount(type, startTime, endTime);
        Integer sampleCount = countMap.get("sampleCount");//原样样品数据
        Integer analyseDataCount = countMap.get("analyseDataCount");//原样的数据
        Long qcDataCount = getZKSampleCount(type, startTime, endTime);//质控数据数
        countInfo.put("sampleCount", sampleCount);
        countInfo.put("analyseDataCount", analyseDataCount);
        countInfo.put("qcDataCount", qcDataCount);
        resultMap.put("countInfo", countInfo);
        resultMap.put("detail", dataList);
        return resultMap;
    }


    @Override
    public  List<DtoProjectSampleCount> findProjectSampleCountList(Integer type, String startTime, String endTime, String projectTypeId) {
        Date from = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        Date to = DateUtil.stringToDate("2999-12-31", DateUtil.YEAR);
        if (StringUtils.isNotNullAndEmpty(startTime)) {
            from = DateUtil.stringToDate(startTime, DateUtil.YEAR);
        }
        if (StringUtils.isNotNullAndEmpty(endTime)) {
            to = DateUtil.stringToDate(endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
        }
        List<Object[]> objList = type.equals(EnumPRO.EnumStatisticsTimeType.采样时间.getValue()) ?
                projectCountStatisticsRepository.findProjectSampleCountListBySamplingTime(from, to, projectTypeId, PrincipalContextUser.getPrincipal().getOrgId()) :
                projectCountStatisticsRepository.findProjectSampleCountListByInceptTime(from, to, projectTypeId, PrincipalContextUser.getPrincipal().getOrgId());

        List<Object[]> zkList = type.equals(EnumPRO.EnumStatisticsTimeType.采样时间.getValue()) ?
                projectCountStatisticsRepository.findZKSampleCountBySamplingTime(from, to, PrincipalContextUser.getPrincipal().getOrgId(),projectTypeId) :
                projectCountStatisticsRepository.findZKSampleCountByInceptTime(from, to, PrincipalContextUser.getPrincipal().getOrgId(),projectTypeId);

        List<DtoProjectSampleCount> dataList = new ArrayList<>();
        for (Object[] obj : objList) {
            DtoProjectSampleCount psc = new DtoProjectSampleCount();
            psc.setSampleTypeName((String) obj[0]);
            psc.setAnalyseDataCount(Integer.valueOf(obj[1].toString()));
            psc.setSampleCount(Integer.valueOf(obj[2].toString()));
            psc.setQualityControlCount(0);
            for (Object[] zk : zkList){
                if(obj[3].equals(zk[0])){
                    psc.setQualityControlCount(Integer.valueOf(zk[1].toString()));
                }
            }
            dataList.add(psc);
        }
        return dataList;
    }

    /**
     * 根据年份查询每月监测数量
     *
     * @param year 年份
     * @return 每月监测数量
     */
    @Override
    public Map<String, Object> findCountByYear(Integer year) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Integer> months = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            months.add(i);
        }
        List<DtoSampleCountStatistic> listBySamplingTime = projectCountStatisticsRepository.findListBySamplingTime(getYearFirst(year), getYearLast(year));
        Map<Integer, List<DtoSampleCountStatistic>> dataGroupByMonth = listBySamplingTime.stream().collect(Collectors.groupingBy(p -> {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(p.getSamplingTime());
            return calendar.get(Calendar.MONTH) + 1;
        }));
        for (Integer month : months) {
            Map<String, Object> countMap = new HashMap<>();
            List<DtoSampleCountStatistic> dataOfMonth = dataGroupByMonth.getOrDefault(month, new ArrayList<>());
            //样品数量
            long sampleCount = dataOfMonth.stream().map(DtoSampleCountStatistic::getSampleId).distinct().count();
            //分析数据数量
            long analyseDataCount = dataOfMonth.stream().map(DtoSampleCountStatistic::getAnalyseDataId).distinct().count();
            countMap.put("sampleCount",sampleCount);
            countMap.put("analyseDataCount",analyseDataCount);
            resultMap.put(month.toString(), countMap);
        }
        return resultMap;
    }


    /**
     * 获取某年第一天日期
     *
     * @param year
     *            年份
     * @return Date
     */
    public static Date getYearFirst(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        return calendar.getTime();
    }

    /**
     * 获取某年最后一天日期
     *
     * @param year
     *            年份
     * @return Date
     */
    public static Date getYearLast(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return calendar.getTime();
    }


        /**
         * 获取原样的总数数据
         * @param type 类型
         * @param startTime 开始时间
         * @param endTime 结束时间
         * @return 返回相应的数据
         */
    private Map<String,Integer>  getYYSampleCount(Integer type, String startTime, String endTime) {
        Date from = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        Date to = DateUtil.stringToDate("2999-12-31", DateUtil.YEAR);
        if (StringUtils.isNotNullAndEmpty(startTime)) {
            from = DateUtil.stringToDate(startTime, DateUtil.YEAR);
        }
        if (StringUtils.isNotNullAndEmpty(endTime)) {
            to = DateUtil.stringToDate(endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
        }

        List<Object[]> dataList = type.equals(EnumPRO.EnumStatisticsTimeType.采样时间.getValue()) ? projectCountStatisticsRepository.findYYSampleCountBySamplingTime(from, to, PrincipalContextUser.getPrincipal().getOrgId()) :
                projectCountStatisticsRepository.findYYSampleCountByInceptTime(from, to, PrincipalContextUser.getPrincipal().getOrgId());
        Map<String, Integer> countMap = new HashMap<>();
        try {
            Integer analyseDataCount = dataList.stream().map(p -> Integer.valueOf(p[0].toString())).findFirst().orElse(null);

            if (StringUtil.isNull(analyseDataCount)) {
                analyseDataCount = 0;
            }
            Integer sampleCount = dataList.stream().map(p -> Integer.valueOf(p[1].toString())).findFirst().orElse(null);

            if (StringUtil.isNull(sampleCount)) {
                sampleCount = 0;
            }
            countMap.put("analyseDataCount", analyseDataCount);
            countMap.put("sampleCount", sampleCount);
        } catch (Exception e) {
            if (!countMap.containsKey("analyseDataCount")) {
                countMap.put("analyseDataCount", 0);
            }
            if (!countMap.containsKey("sampleCount")) {
                countMap.put("sampleCount", 0);
            }
        }

        return countMap;
    }

    /**
     * 质控的数据总数
     * @param type  类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 返回相应的数据
     */
    private Long getZKSampleCount(Integer type, String startTime, String endTime) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select ");
        stringBuilder.append("count(distinct analyseDataId) as analyseDataCount");
        stringBuilder.append(" from VProjectZKSampleCount where 1=1 ");
        stringBuilder = getSelect(stringBuilder, type, startTime, endTime, values);
        List<Long> dataList = commonRepository.find(stringBuilder.toString(), values);

        Long analyseDataCount = dataList.stream().findFirst().orElse(null);

        if (StringUtil.isNull(analyseDataCount)) {
            analyseDataCount = 0L;
        }
        return analyseDataCount;
    }
    /**
     * 统一的查询条件
     *
     * @param stringBuilder 条件
     * @param type          类型
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param values        值
     * @return 返回查询方式
     */
    private StringBuilder getSelect(StringBuilder stringBuilder,
                                    Integer type,
                                    String startTime,
                                    String endTime,
                                    Map<String, Object> values) {
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            String orgId = PrincipalContextUser.getPrincipal().getOrgId();
            stringBuilder.append(" and orgId = :orgId");
            values.put("orgId", orgId);
        }
        Date from = null;
        Date to = null;
        if (StringUtils.isNotNullAndEmpty(startTime)) {
            from = DateUtil.stringToDate(startTime, DateUtil.YEAR);
        }
        if (StringUtils.isNotNullAndEmpty(endTime)) {
            to = DateUtil.stringToDate(endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
        }
        if (StringUtil.isNotNull(type)) {
            if (type.equals(EnumPRO.EnumStatisticsTimeType.采样时间.getValue())) {
                if (StringUtil.isNotNull(from)) {
                    stringBuilder.append(" and samplingTime >= :from");
                    values.put("from", from);
                }
                if (StringUtil.isNotNull(to)) {
                    stringBuilder.append(" and samplingTime < :to");
                    values.put("to", to);
                }
            } else if (type.equals(EnumPRO.EnumStatisticsTimeType.登记时间.getValue())) {
                if (StringUtil.isNotNull(from)) {
                    stringBuilder.append(" and inceptTime >= :from");
                    values.put("from", from);
                }
                if (StringUtil.isNotNull(to)) {
                    stringBuilder.append(" and inceptTime < :to");
                    values.put("to", to);
                }
            }
        }
        return stringBuilder;
    }

}
