package com.sinoyd.lims.monitor.repository.lims;

import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSort;
import com.sinoyd.frame.repository.IBaseJpaRepository;


/**
 * FixedPointSort数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedPointSortRepository extends IBaseJpaRepository<DtoFixedPointSort, String> {

    /**
     * 根据排序名称查询实体数量
     *
     * @param sortName 排序名称
     * @return 数量
     */
    Integer countBySortName(String sortName);

    /**
     * 根据名称与id获取重复的条数
     *
     * @param id       id
     * @param sortName 排序名称
     * @return 数量
     */
    Integer countByIdNotAndSortName(String id, String sortName);
}