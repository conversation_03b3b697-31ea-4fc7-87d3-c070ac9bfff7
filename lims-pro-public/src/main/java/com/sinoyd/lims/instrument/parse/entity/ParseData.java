package com.sinoyd.lims.instrument.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 仪器解析数据实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/26
 */
@MappedSuperclass
@ApiModel(description = "Instrument Parse Data")
@Data
public class ParseData implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ParseData() {
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 方案Id
     */
    @Column(length = 50)
    @ApiModelProperty("方案Id")
    private String planId;

    /**
     * 应用Id
     */
    @Column(length = 50)
    @ApiModelProperty("应用Id")
    private String appId;

    /**
     * 解析日志主键
     */
    @Column(length = 50)
    @ApiModelProperty("解析日志主键")
    private String parselogId;

    /**
     * 仪器id
     */
    @Column(length = 250)
    @ApiModelProperty("仪器id")
    private String instrumentId;

    /**
     * 仪器名称
     */
    @Column(length = 250)
    @ApiModelProperty("仪器名称")
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Column(length = 250)
    @ApiModelProperty("仪器编号")
    private String instrumentCode;

    /**
     * 样品编号
     */
    @Column(length = 250)
    @ApiModelProperty("样品编号")
    private String sampleCode;

    /**
     * 分析项目名称
     */
    @Column(length = 250)
    @ApiModelProperty("分析项目名称")
    private String analyzeItemName;

    /**
     * 参数名称
     */
    @Column(length = 250)
    @ApiModelProperty("参数名称")
    private String paramName;

    /**
     * 属性别名
     */
    @Column(length = 250)
    @ApiModelProperty("属性别名")
    private String paramAlias;

    /**
     * 属性结果
     */
    @Column(length = 250)
    @ApiModelProperty("属性结果")
    private String value;

    /**
     * 数据解析时间
     */
    @Column(nullable = false)
    @ApiModelProperty("数据解析时间")
    private Date parseDataTime;

    /**
     * 假删字段
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 预留值1
     */
    @Column(length = 250)
    @ApiModelProperty("预留值1")
    private String extend1;

    /**
     * 预留值2
     */
    @Column(length = 250)
    @ApiModelProperty("预留值2")
    private String extend2;

    /**
     * 预留值3
     */
    @Column(length = 250)
    @ApiModelProperty("预留值3")
    private String extend3;

    /**
     * 预留值4
     */
    @Column(length = 250)
    @ApiModelProperty("预留值4")
    private String extend4;

    /**
     * 预留值5
     */
    @Column(length = 250)
    @ApiModelProperty("预留值5")
    private String extend5;
}