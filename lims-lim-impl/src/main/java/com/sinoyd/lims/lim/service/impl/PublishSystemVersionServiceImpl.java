package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoPublishSystemVersion;
import com.sinoyd.lims.lim.repository.rcc.PublishSystemVersionRepository;
import com.sinoyd.lims.lim.service.PublishSystemVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 版本发布管理实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/11/9
 */
@Service
public class PublishSystemVersionServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoPublishSystemVersion, String, PublishSystemVersionRepository>
        implements PublishSystemVersionService {

    JdbcTemplate jdbcTemplate;

    /**
     * 分页查询
     *
     * @param page     分页数据
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoPublishSystemVersion> page, BaseCriteria criteria) {
        page.setEntityName(" DtoPublishSystemVersion a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public DtoPublishSystemVersion save(DtoPublishSystemVersion entity) {
        checkVersionNumOnly(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoPublishSystemVersion update(DtoPublishSystemVersion entity) {
        checkVersionNumOnly(entity);
        return super.save(entity);
    }

    /**
     * 文件上传查询路径
     *
     * @param id 主键id
     * @return 版本发布管理dto
     */
    @Override
    public DtoPublishSystemVersion findAttachPath(String id) {
        return findOne(id);
    }

    @Override
    public String findFlyWayVersion() {
        String sql = "select script from schema_version WHERE installed_rank = (SELECT MAX(installed_rank) FROM schema_version) limit 1";
        Map<String, Object> map = jdbcTemplate.queryForMap(sql);
        String script = map.get("script").toString();
        script = script.substring(0, script.indexOf("."));
        return script;
    }

    /**
     * 校验版本号唯一性
     *
     * @param dto 版本发布管理dto
     */
    private void checkVersionNumOnly(DtoPublishSystemVersion dto) {
        Integer count = repository.countByVersionNumAndIdNot(dto.getVersionNum(), dto.getId());
        if (count.compareTo(0) > 0) {
            throw new BaseException("版本号重复");
        }
    }

    @Autowired
    @Lazy
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}