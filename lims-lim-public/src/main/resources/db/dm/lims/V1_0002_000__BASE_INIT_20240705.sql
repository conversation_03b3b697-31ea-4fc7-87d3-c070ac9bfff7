CREATE TABLE "TB_BASE_ANALYZEITEM"
(
    "id" VARCHAR(50) NOT NULL,
    "analyzeItemName" VARCHAR(100) NULL,
    "analyzeItemCode" VARCHAR(100) NULL,
    "variableName" VARCHAR(50) NULL,
    "pinYin" VARCHAR(50) NULL,
    "fullPinYin" VARCHAR(100) NULL,
    "isDeleted" BIT DEFAULT 0
                     NOT NULL,
    "orderNum" INT DEFAULT 0
                     NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "casNum" VARCHAR(50) DEFAULT ''
        NULL
);
CREATE TABLE "TB_BASE_CONSUMABLE"
(
    "id" VARCHAR(50) NOT NULL,
    "consumableName" VARCHAR(50) NOT NULL,
    "pinYin" VARCHAR(50) NULL,
    "fullPinYin" VARCHAR(100) NULL,
    "codeInStation" VARCHAR(50) NULL,
    "consumableCode" VARCHAR(20) NULL,
    "specification" VARCHAR(50) NULL,
    "unit" VARCHAR(50) NULL,
    "unitId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "grade" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "categoryId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "warningNum" DECIMAL(18,4) NOT NULL,
    "sendWarnUserId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "alias" VARCHAR(255) NULL,
    "isPoison" BIT DEFAULT 0
        NOT NULL,
    "keepCondition" VARCHAR(1000) NULL,
    "safetyInstruction" VARCHAR(1000) NULL,
    "remark" VARCHAR(1000) NULL,
    "isStandard" BIT DEFAULT 0
        NOT NULL,
    "dilutedSolution" VARCHAR(255) NULL,
    "dilutionMethod" VARCHAR(255) NULL,
    "concentration" VARCHAR(255) NULL,
    "uncertainty" VARCHAR(255) NULL,
    "isMixedStandard" BIT DEFAULT 0
        NOT NULL,
    "inventory" DECIMAL(18,4) NULL,
    "sendWarnTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "standard" VARCHAR(50) NULL,
    "nationalStandard" VARCHAR(100) NULL,
    "constantVolumeM" VARCHAR(100) NULL,
    "molecularWeight" VARCHAR(100) NULL,
    "molecularFormula" VARCHAR(100) NULL,
    "useWay" VARCHAR(255) NULL,
    "density" VARCHAR(100) NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "isLabEncryption" BIT DEFAULT 0
        NOT NULL,
    "dimensionId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "dimensionName" VARCHAR(50) DEFAULT ''
        NULL
);
CREATE TABLE "TB_BASE_CONSUMABLEDETAIL"
(
    "id" VARCHAR(50) NOT NULL,
    "parentId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "unitId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "unitName" VARCHAR(100) NULL,
    "inventory" DECIMAL(18,4) NOT NULL,
    "storage" DECIMAL(18,4) NOT NULL,
    "unitPrice" DECIMAL(18,2) NULL,
    "productionCode" VARCHAR(50) NULL,
    "storageDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "manufacturerName" VARCHAR(50) NULL,
    "expiryDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "supplierId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "supplierName" VARCHAR(100) NULL,
    "checkerId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "purchasingDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "checkerResult" INT DEFAULT 1
        NOT NULL,
    "appearance" VARCHAR(100) NULL,
    "checkItem" VARCHAR(100) NULL,
    "buyReason" VARCHAR(255) NULL,
    "keepPlace" VARCHAR(100) NULL,
    "remark" VARCHAR(1000) NULL,
    "isLocked" BIT DEFAULT 0
        NOT NULL,
    "productionDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "purchaser" VARCHAR(50) NULL,
    "codeInType" VARCHAR(50) NULL,
    "expiredStatus" INT DEFAULT 0
        NOT NULL,
    "purchaseNum" VARCHAR(255) NULL,
    "validTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "purchaseCount" DECIMAL(18,4) DEFAULT (-1.)
        NOT NULL,
    "storageNum" VARCHAR(255) NULL,
    "buyCount" DECIMAL(18,4) DEFAULT (-1.)
        NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "sendWarnUserId" VARCHAR(255) NULL,
    "orderTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NULL
);
CREATE TABLE "TB_BASE_CONSUMABLELOG"
(
    "id" VARCHAR(50) NOT NULL,
    "consumablePickId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "pickNum" VARCHAR(50) NULL,
    "userId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "amount" DECIMAL(18,4) NOT NULL,
    "remark" VARCHAR(1000) NULL,
    "balance" DECIMAL(18,4) NOT NULL,
    "occurrenceTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "consumableDetailId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "consumableId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "consumingPersonsName" VARCHAR(50) NULL,
    "pickTypeId" INT DEFAULT 0
        NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL
);
CREATE TABLE "TB_BASE_CONSUMABLEOFMIXED"
(
    "id" VARCHAR(50) NOT NULL,
    "consumableId" VARCHAR(50) NOT NULL,
    "analyzeItemId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "concentration" VARCHAR(255) NULL,
    "uncertainty" VARCHAR(255) NULL,
    "dimensionId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL
);
CREATE TABLE "TB_BASE_DIMENSION"
(
    "id" VARCHAR(50) NOT NULL,
    "dimensionName" VARCHAR(50) NOT NULL,
    "dimensionTypeId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "code" VARCHAR(50) NULL,
    "remark" VARCHAR(1000) NULL,
    "baseValue" DECIMAL(38,10) NOT NULL,
    "isDeleted" BIT DEFAULT 0
        NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "orderNum" INT DEFAULT 0
        NOT NULL
);
CREATE TABLE "TB_BASE_DOCUMENT"
(
    "id" VARCHAR(50) NOT NULL,
    "folderId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "folderName" VARCHAR(255) NULL,
    "filename" VARCHAR(255) NULL,
    "physicalName" VARCHAR(255) NULL,
    "path" VARCHAR(500) NULL,
    "isDeleted" BIT DEFAULT 0
                     NOT NULL,
    "isTranscript" BIT DEFAULT 0
                     NOT NULL,
    "docTypeId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "docTypeName" VARCHAR(255) NULL,
    "docSize" INT DEFAULT 0
                     NOT NULL,
    "docSuffix" VARCHAR(10) NULL,
    "downloadTimes" INT DEFAULT 0
                     NOT NULL,
    "orderNum" INT DEFAULT 0
                     NOT NULL,
    "remark" VARCHAR(1000) NULL,
    "uploadPersonId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "uploadPerson" VARCHAR(50) NULL,
    "isStick" BIT DEFAULT 0
                     NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "isSystemDraw" BIT DEFAULT 0
                     NOT NULL
);
CREATE TABLE "TB_BASE_ENTERPRISE"
(
    "id" VARCHAR(50) NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "shortName" VARCHAR(100) NULL,
    "code" VARCHAR(20) NULL,
    "socialCreditCode" VARCHAR(50) NULL,
    "pinYin" VARCHAR(50) NULL,
    "fullPinYin" VARCHAR(100) NULL,
    "buildStatus" BIGINT DEFAULT 1
        NOT NULL,
    "runDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "passDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "businessTypeId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "regTypeId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "longitude" VARCHAR(20) NULL,
    "latitude" VARCHAR(20) NULL,
    "longitudeOther" VARCHAR(20) NULL,
    "latitudeOther" VARCHAR(20) NULL,
    "contactFax" VARCHAR(50) NULL,
    "contactMan" VARCHAR(100) DEFAULT ''
        NULL,
    "contactPhoneNumber" VARCHAR(50) NULL,
    "contactTelPhone" VARCHAR(50) NULL,
    "address" VARCHAR(100) NULL,
    "email" VARCHAR(50) NULL,
    "postalCode" VARCHAR(10) NULL,
    "corporationCode" VARCHAR(20) NULL,
    "corporationName" VARCHAR(50) NULL,
    "registeredCapital" DECIMAL(18,0) DEFAULT (-1)
        NOT NULL,
    "areaId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "areaName" VARCHAR(1000) NULL,
    "nature" VARCHAR(255) NULL,
    "businessScope" VARCHAR(255) NULL,
    "acreage" VARCHAR(50) NULL,
    "houseNature" VARCHAR(255) NULL,
    "owner" VARCHAR(50) NULL,
    "industrialPark" VARCHAR(255) NULL,
    "employeeNumber" INT DEFAULT (-1)
        NOT NULL,
    "runDaysPerYear" INT DEFAULT (-1)
        NOT NULL,
    "productValuePerYear" VARCHAR(50) NULL,
    "tax" VARCHAR(50) NULL,
    "type" INT DEFAULT 2
        NOT NULL,
    "qualityCertification" VARCHAR(1000) NULL,
    "isEligibility" BIT DEFAULT 0
        NOT NULL,
    "orderNum" INT DEFAULT 0
        NOT NULL,
    "isDeleted" BIT DEFAULT 0
        NOT NULL,
    "url" VARCHAR(100) NULL,
    "scope" VARCHAR(255) NULL,
    "info" CLOB NULL,
    "openDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "remark" VARCHAR(1000) NULL,
    "systemType" INT DEFAULT 1
        NOT NULL,
    "externalId" VARCHAR(100) DEFAULT '********-0000-0000-0000-********0000'
        NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "entrustRate" VARCHAR(50) NULL,
    "industryKind" VARCHAR(100) NULL,
    "cmaCode" VARCHAR(50) DEFAULT ''
        NULL,
    "cmaExpiryDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL
);
CREATE TABLE "TB_BASE_ENTERPRISEEXTEND"
(
    "id" VARCHAR(50) NOT NULL,
    "entId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "pollutionSourceType" VARCHAR(300) NULL,
    "isUsed" BIT DEFAULT 0
                     NOT NULL,
    "attentionDegree" INT DEFAULT (-1)
                     NOT NULL,
    "isBreak" BIT DEFAULT 0
                     NOT NULL,
    "subRate" VARCHAR(50) NULL,
    "entrustRate" VARCHAR(50) NULL,
    "breakInfo" VARCHAR(1000) NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "pollutionCode" VARCHAR(50) NULL
);
CREATE TABLE "TB_BASE_EVALUATIONANALYZEITEM"
(
    "id" VARCHAR(50) NOT NULL,
    "evaluationId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "analyzeItemId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "symbol" VARCHAR(50) NULL,
    "unit" VARCHAR(50) NULL,
    "remark" VARCHAR(1000) NULL,
    "orderNum" INT DEFAULT 0
                     NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL
);
CREATE TABLE "TB_BASE_EVALUATIONCRITERIA"
(
    "id" VARCHAR(50) NOT NULL,
    "name" VARCHAR(100) NULL,
    "code" VARCHAR(20) NULL,
    "categoryId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "sampleTypeId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "startTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NULL,
    "endTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NULL,
    "status" INT DEFAULT 1
                     NOT NULL,
    "applyRange" VARCHAR(1000) NULL,
    "remark" VARCHAR(1000) NULL,
    "isDeleted" BIT DEFAULT 0
                     NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL
);
CREATE TABLE "TB_BASE_EVALUATIONLEVEL"
(
    "id" VARCHAR(50) NOT NULL,
    "evaluationId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "parentId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "name" VARCHAR(255) NULL,
    "describion" VARCHAR(1000) NULL,
    "orderNum" INT DEFAULT 0
                     NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL
);
CREATE TABLE "TB_BASE_EVALUATIONVALUE"
(
    "id" VARCHAR(50) NOT NULL,
    "evaluationId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "levelId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "analyzeItemId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "upperLimit" VARCHAR(50) NULL,
    "upperLimitSymble" VARCHAR(50) NULL,
    "lowerLimit" VARCHAR(50) NULL,
    "lowerLimitSymble" VARCHAR(50) NULL,
    "remark" VARCHAR(1000) NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "dimensionId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL
);
CREATE TABLE "TB_BASE_FOLDER"
(
    "id" VARCHAR(50) NOT NULL,
    "folderName" VARCHAR(50) NULL,
    "parentId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "orderNum" INT DEFAULT 0
                     NOT NULL,
    "remark" VARCHAR(1000) NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL
);
CREATE TABLE "TB_BASE_INDUSTRYTYPE"
(
    "id" VARCHAR(50) NOT NULL,
    "parentId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "industryName" VARCHAR(50) NULL,
    "industryCode" VARCHAR(20) NULL,
    "isDeleted" BIT DEFAULT 0
                     NOT NULL,
    "orderNum" INT DEFAULT 0
                     NOT NULL,
    "remark" VARCHAR(1000) NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL
);
CREATE TABLE "TB_BASE_INSTRUMENT"
(
    "id" VARCHAR(50) NOT NULL,
    "instrumentName" VARCHAR(50) NULL,
    "pinYin" VARCHAR(50) NULL,
    "fullPinYin" VARCHAR(100) NULL,
    "model" VARCHAR(200) NULL,
    "instrumentTypeId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "insRange" VARCHAR(200) NULL,
    "nicetyRate" VARCHAR(100) NULL,
    "price" DECIMAL(18,2) NOT NULL,
    "place" VARCHAR(100) NULL,
    "manager" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "managerName" VARCHAR(50) NULL,
    "isShowOnReport" BIT DEFAULT 0
        NOT NULL,
    "useConditions" VARCHAR(1000) NULL,
    "state" INT DEFAULT 1
        NOT NULL,
    "instrumentsCode" VARCHAR(50) NULL,
    "serialNo" VARCHAR(100) NULL,
    "factoryName" VARCHAR(100) NULL,
    "saleName" VARCHAR(100) NULL,
    "purchaseDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "belongDeptId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "deptName" VARCHAR(50) NULL,
    "fixedAssetsCode" VARCHAR(20) NULL,
    "controlMeasures" VARCHAR(1000) NULL,
    "useMethod" VARCHAR(1000) NULL,
    "maintenanceCyc" DECIMAL(18,1) DEFAULT (-1.)
        NOT NULL,
    "maintenanceContent" VARCHAR(1000) NULL,
    "maintenanceDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "inspectPeriod" DECIMAL(18,1) DEFAULT (-1.)
        NOT NULL,
    "inspectMethod" VARCHAR(1000) NULL,
    "inspectResult" INT DEFAULT 1
        NOT NULL,
    "inspectDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "originCyc" DECIMAL(18,1) DEFAULT 12.
        NOT NULL,
    "originType" INT DEFAULT (-1)
        NOT NULL,
    "originUnit" VARCHAR(100) NULL,
    "originDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "originEndDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "originResult" INT DEFAULT 1
        NOT NULL,
    "originRemark" VARCHAR(1000) NULL,
    "instrColor" VARCHAR(100) NULL,
    "remark" VARCHAR(1000) NULL,
    "recentOpenDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "alarmThreshold" NUMBER(22,0) DEFAULT 0
        NOT NULL,
    "isDeleted" BIT DEFAULT 0
        NOT NULL,
    "photoUrl" VARCHAR(500) NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "productDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "productCountry" VARCHAR(50) NULL,
    "constractNo" VARCHAR(50) NULL,
    "scrapTime" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NULL,
    "isInspected" BIT DEFAULT 0
        NOT NULL,
    "inspectMode" VARCHAR(50) NULL,
    "inspectReason" VARCHAR(200) NULL,
    "responsibleOffice" VARCHAR(50) NULL,
    "shareFlag" BIT DEFAULT 0
        NOT NULL,
    "isAssistInstrument" BIT DEFAULT 0
        NOT NULL
);
CREATE TABLE "TB_BASE_JOB"
(
    "id" VARCHAR(50) NOT NULL,
    "jobName" VARCHAR(50) NOT NULL,
    "jobGroup" VARCHAR(50) NOT NULL,
    "invokeTarget" VARCHAR(500) NOT NULL,
    "cronExpression" VARCHAR(255) NOT NULL,
    "misfirePolicy" INT DEFAULT 3
        NOT NULL,
    "isConcurrent" BIT DEFAULT 0
        NOT NULL,
    "status" INT DEFAULT 0
        NOT NULL,
    "remark" VARCHAR(500) NULL,
    "isDeleted" BIT DEFAULT 0
        NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL
);
CREATE TABLE "TB_BASE_JOBINFO"
(
    "id" VARCHAR(50) NOT NULL,
    "jobId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "beginTime" VARCHAR(10) NULL,
    "endTime" VARCHAR(10) NULL,
    "isIncludeDefault" BIT DEFAULT 0
                     NOT NULL,
    "remindDays" INT DEFAULT 0
                     NOT NULL,
    "sendType" INT DEFAULT 1
                     NOT NULL,
    "isDeleted" BIT DEFAULT 0
                     NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL
);
CREATE TABLE "TB_BASE_JOBINFORMPERSON"
(
    "id" VARCHAR(50) NOT NULL,
    "jobInfoId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "personId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL
);
CREATE TABLE "TB_BASE_LOGFORLUCKYSHEET"
(
    "id" VARCHAR(50) NOT NULL,
    "operatorId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "operatorName" VARCHAR(50) NULL,
    "operateTime" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "operateInfo" VARCHAR(500) NULL,
    "objectId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "remark" CLOB NULL,
    "comment" VARCHAR(1000) NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL
);
CREATE TABLE "TB_BASE_PERPETUALDATA"
(
    "id" VARCHAR(50) NOT NULL,
    "type" VARCHAR(255) NULL,
    "value" VARCHAR(255) NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL
);
CREATE TABLE "TB_BASE_QUALITYCONTROLLIMIT"
(
    "id" VARCHAR(50) NOT NULL,
    "testId" VARCHAR(50) DEFAULT ''
                     NOT NULL,
    "rangeConfig" VARCHAR(50) NULL,
    "judgingMethod" INT DEFAULT 1
                     NOT NULL,
    "allowLimit" VARCHAR(50) NULL,
    "qcGrade" INT DEFAULT (-1)
                     NOT NULL,
    "qcType" INT DEFAULT (-1)
                     NOT NULL,
    "qcTypeName" VARCHAR(50) NULL,
    "substituteId" VARCHAR(50) DEFAULT ''
                     NOT NULL,
    "substituteName" VARCHAR(50) NULL,
    "formula" VARCHAR(50) NULL,
    "checkItem" INT DEFAULT 1
                     NOT NULL,
    "checkItemOther" VARCHAR(50) NULL,
    "isCheckItem" INT DEFAULT 1
                     NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "description" VARCHAR(255) DEFAULT ''
        NULL,
    "validate" INT DEFAULT 0
        NULL,
    "usageNum" INT NULL,
    "dispositionId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL
);
CREATE TABLE "TB_BASE_QUALITYLIMITDISPOSITION"
(
    "id" VARCHAR(50) NOT NULL,
    "qcGrade" INT NOT NULL,
    "qcType" INT NOT NULL,
    "formula" VARCHAR(255) NULL,
    "judgingMethod" INT NOT NULL,
    "isAcquiesce" BIT NULL,
    "orgId" VARCHAR(50) NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL
);
CREATE TABLE "TB_BASE_SAMPLETYPE"
(
    "id" VARCHAR(50) NOT NULL,
    "industryTypeId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "parentId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "typeCode" VARCHAR(50) NULL,
    "typeName" VARCHAR(50) NULL,
    "remark" VARCHAR(1000) NULL,
    "laboratoryId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "shortName" VARCHAR(50) NULL,
    "isDeleted" BIT DEFAULT 0
                     NOT NULL,
    "keepLongTime" INT DEFAULT (-1)
                     NOT NULL,
    "reportingCycle" INT DEFAULT (-1)
                     NOT NULL,
    "category" INT DEFAULT (-1)
                     NOT NULL,
    "orderNum" INT DEFAULT 0
                     NOT NULL,
    "icon" VARCHAR(255) NULL,
    "systemType" INT DEFAULT 1
                     NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
                     NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                     NOT NULL,
    "defaultLabelGroupId" VARCHAR(50) NULL,
    "fieldTaskGroupId" VARCHAR(50) NULL,
    "checkType" INT NULL
);
CREATE TABLE "TB_BASE_SUBSTITUTE"
(
    "id" VARCHAR(50) NOT NULL,
    "casCode" VARCHAR(50) NOT NULL,
    "compoundName" VARCHAR(100) NOT NULL,
    "addition" VARCHAR(50) NOT NULL,
    "isDeleted" BIT DEFAULT 0
        NOT NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'
        NOT NULL,
    "dimensionId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "dimensionName" VARCHAR(100) NULL
);
CREATE TABLE "TB_BASE_SYSTEMCONFIG"
(
    "id" VARCHAR(50) NOT NULL,
    "fullName" VARCHAR(100) NOT NULL,
    "shortName" VARCHAR(50) NULL,
    "welcomeWord" VARCHAR(255) NULL,
    "companyName" VARCHAR(255) NULL,
    "companyAddress" VARCHAR(1000) NULL,
    "companyPostCode" VARCHAR(20) NULL,
    "companyPhone" VARCHAR(50) NULL,
    "companyEnglishName" VARCHAR(255) NULL,
    "orgId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "creator" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "domainId" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifier" VARCHAR(50) DEFAULT '********-0000-0000-0000-********0000'
        NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
        NOT NULL,
    "allowFileSuffix" VARCHAR(200) NULL,
    "bank" VARCHAR(50) NULL,
    "bankAccount" VARCHAR(50) NULL,
    "taxNumber" VARCHAR(50) NULL
);
ALTER TABLE "TB_BASE_ANALYZEITEM" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_CONSUMABLE" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_CONSUMABLEDETAIL" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_CONSUMABLELOG" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_CONSUMABLEOFMIXED" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_DIMENSION" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_DOCUMENT" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_ENTERPRISE" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_ENTERPRISEEXTEND" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_EVALUATIONANALYZEITEM" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_EVALUATIONCRITERIA" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_EVALUATIONLEVEL" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_EVALUATIONVALUE" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_FOLDER" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_INDUSTRYTYPE" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_INSTRUMENT" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_JOB" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_JOBINFO" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_JOBINFORMPERSON" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_LOGFORLUCKYSHEET" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_PERPETUALDATA" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_QUALITYCONTROLLIMIT" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_QUALITYLIMITDISPOSITION" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_SAMPLETYPE" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_SUBSTITUTE" ADD CONSTRAINT  PRIMARY KEY("id") ;

ALTER TABLE "TB_BASE_SYSTEMCONFIG" ADD CONSTRAINT  PRIMARY KEY("id") ;

CREATE INDEX "UIX_TB_BASE_Consumable"
ON "TB_BASE_CONSUMABLE"("isStandard");

CREATE INDEX "UIX_TB_BASE_ConsumableDetail"
ON "TB_BASE_CONSUMABLEDETAIL"("parentId","expiryDate");

ALTER TABLE "TB_BASE_ENTERPRISE" ADD CHECK("buildStatus" >= 0) ENABLE ;

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."analyzeItemName" IS '名称';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."analyzeItemCode" IS '分析因子编号';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."variableName" IS '变量名称（预留，前台改为别名）';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."pinYin" IS '拼音缩写';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."fullPinYin" IS '全拼';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."isDeleted" IS '是否删除';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."orderNum" IS '排序值';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_ANALYZEITEM"."casNum" IS 'CAS号';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."consumableName" IS '名称';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."pinYin" IS '拼音';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."fullPinYin" IS '全拼';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."codeInStation" IS '编号（本站编号）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."consumableCode" IS '标样编号';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."specification" IS '规格';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."unit" IS '单位名称';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."unitId" IS '单位Id';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."grade" IS '等级常量（Guid）（BASE_ConsumableGrade:进口、分析纯、FMP、高纯等）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."categoryId" IS '类别常量（Guid）（BASE_ConsumableCategory:高压气体、易制毒品、化学试剂等）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."warningNum" IS '警告数量';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."sendWarnUserId" IS '提醒人id（Guid）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."alias" IS '别名';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."isPoison" IS '是否易制毒';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."keepCondition" IS '保存条件';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."safetyInstruction" IS '安全须知';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."isStandard" IS '是否标样（0代表消耗品，1代表标样）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."dilutedSolution" IS '稀释液';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."dilutionMethod" IS '稀释方法';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."concentration" IS '浓度';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."uncertainty" IS '不确定度';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."isMixedStandard" IS '是否混标（0代表否  1代表是）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."inventory" IS '库存数量';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."sendWarnTime" IS '（预留3.2）提醒时间';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."standard" IS '（预留3.2）标值';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."nationalStandard" IS '（预留3.2）国家标准';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."constantVolumeM" IS '（预留3.2）定容介质';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."molecularWeight" IS '（预留3.2）分子量';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."molecularFormula" IS '（预留3.2）分子式';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."useWay" IS '（预留3.2）使用方法';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."density" IS '（预留3.2）密度';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."isLabEncryption" IS '是否实验室加密';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."dimensionId" IS '量纲id';

COMMENT ON COLUMN "TB_BASE_CONSUMABLE"."dimensionName" IS '量纲名称';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."parentId" IS '消耗品/标样id（Guid）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."unitId" IS '单位Id（冗余）（Guid）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."unitName" IS '单位名称（冗余）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."inventory" IS '入库数量';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."storage" IS '库存数量';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."unitPrice" IS '单价';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."productionCode" IS '生产批号';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."storageDate" IS '入库时间';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."manufacturerName" IS '生产厂商名字';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."expiryDate" IS '有效日期';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."supplierId" IS '供应厂商id（Guid）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."supplierName" IS '供应商名字';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."checkerId" IS '验收人id（Guid）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."purchasingDate" IS '验收日期';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."checkerResult" IS '验收结论：枚举（EnumCheckerResult：1合格、2不合格、3过期)';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."appearance" IS '外观';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."checkItem" IS '检验/验证项目';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."buyReason" IS '购买原因';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."keepPlace" IS '存放位置';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."isLocked" IS '是否锁定';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."productionDate" IS '（预留3.2）生产日期';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."purchaser" IS '（预留3.2）购买人';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."codeInType" IS '（预留3.2）类型内编号';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."expiredStatus" IS '（预留3.2）过期是否处理(枚举EnumExpiredStatus：0.未处理 1 提交处理 2  处理通过 3.处理未通过)';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."purchaseNum" IS '（预留3.2）购买证明号';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."validTime" IS '（预留3.2）证书有效期';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."purchaseCount" IS '（预留3.2）准购数量';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."storageNum" IS '（预留3.2）入库单号';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."buyCount" IS '（预留3.2）购买数量';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEDETAIL"."orderTime" IS '定值日期';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."consumablePickId" IS '领料单Id';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."pickNum" IS '领用单号';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."userId" IS '领用人';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."amount" IS '领用数量';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."balance" IS '领用结存';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."occurrenceTime" IS '领用时间';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."consumableDetailId" IS '领用批次Id';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."consumableId" IS '消耗品Id';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."consumingPersonsName" IS '领用人名字';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."pickTypeId" IS '（3.2预留）领用类型(枚举EnumPickType：0.领用1.盘库)';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_CONSUMABLELOG"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEOFMIXED"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEOFMIXED"."consumableId" IS '标样id（Guid）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEOFMIXED"."analyzeItemId" IS '分析项目id（Guid）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEOFMIXED"."concentration" IS '浓度';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEOFMIXED"."uncertainty" IS '不确定度';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEOFMIXED"."dimensionId" IS '计量单位id（Guid）';

COMMENT ON COLUMN "TB_BASE_CONSUMABLEOFMIXED"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."dimensionName" IS '量纲名称';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."dimensionTypeId" IS '量纲类型（使用常量Guid，常量名称BASE_DimensionType）';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."code" IS '编号';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."baseValue" IS '基准值';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."isDeleted" IS '假删';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_DIMENSION"."orderNum" IS '排序值';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."folderId" IS '文件夹Id（Guid）';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."folderName" IS '文件夹名称';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."filename" IS '文件名称';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."physicalName" IS '物理文件名';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."path" IS '文件路径';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."isDeleted" IS '假删';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."isTranscript" IS '是否副本';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."docTypeId" IS '文件类型（常量BASE_DocumentExtendType 用于pro项目附件）';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."docTypeName" IS '文件类型名称';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."docSize" IS '文件大小';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."docSuffix" IS '文件后缀';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."downloadTimes" IS '下载次数';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."orderNum" IS '排序值';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."uploadPersonId" IS '上传人Id';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."uploadPerson" IS '上传人姓名';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_DOCUMENT"."isSystemDraw" IS '是否系统绘制';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."id" IS 'id';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."name" IS '企业名称（污染源名称）（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."shortName" IS '企业简称';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."code" IS '企业编码';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."socialCreditCode" IS '社会信用代码';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."pinYin" IS '拼音缩写（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."fullPinYin" IS '全拼（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."buildStatus" IS '建设状态（枚举EnumBuildStatus：0.建设中 1.已完成）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."runDate" IS '投产日期（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."passDate" IS '成立时间';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."businessTypeId" IS '行业类型Id（Guid）（常量：BASE_BussniessType）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."regTypeId" IS '注册类型Id（Guid）（常量：BASE_RegType）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."longitude" IS '企业经度';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."latitude" IS '企业纬度';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."longitudeOther" IS '经度（其他地图）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."latitudeOther" IS '纬度（其他地图）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."contactFax" IS '联系人传真（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."contactMan" IS '联系人（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."contactPhoneNumber" IS '联系电话（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."contactTelPhone" IS '联系手机（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."address" IS '企业地址（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."email" IS '联系邮箱（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."postalCode" IS '邮政编码（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."corporationCode" IS '法人代码（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."corporationName" IS '法人代表（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."registeredCapital" IS '注册资金（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."areaId" IS '所在地区Id（Guid）（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."areaName" IS '所属区域（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."nature" IS '企业性质（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."businessScope" IS '经营范围（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."acreage" IS '占地面积（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."houseNature" IS '厂房性质（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."owner" IS '产权所有人（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."industrialPark" IS '所在工业园区（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."employeeNumber" IS '员工人数（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."runDaysPerYear" IS '年正常生产天数（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."productValuePerYear" IS '年产值（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."tax" IS '利税（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."type" IS '企业种类(枚举EnumEntType：1.污染源 2.客户  4：供应商 8：分包商）使用按位与表示客户为污染源（lims） ';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."qualityCertification" IS '质量认证（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."isEligibility" IS '是否合格（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."orderNum" IS '排序号（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."isDeleted" IS '假删字段（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."url" IS '企业网址（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."scope" IS '规模（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."info" IS '企业简介（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."openDate" IS '开业日期（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."remark" IS '备注（lims）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."systemType" IS '系统类型(枚举EnumSystemType：1.LIMS  2.环境质量 4.污染源)';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."externalId" IS '关联系统客户编号';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."industryKind" IS '所属行业';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."cmaCode" IS 'cma证书编号';

COMMENT ON COLUMN "TB_BASE_ENTERPRISE"."cmaExpiryDate" IS 'cma证书有效期';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."id" IS 'id';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."entId" IS '企业Id';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."pollutionSourceType" IS '污染源类型（常量 LIM_PollutionSourceType 多个类型用;隔开';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."isUsed" IS '是否使用（账号）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."attentionDegree" IS '关注程度（污染源）（EnumAttentionDegree：1：国控，2：省控，4：市控，8：区控）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."isBreak" IS '是否违约（客户）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."subRate" IS '分包费率（客户）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."entrustRate" IS '委托费率（客户）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."breakInfo" IS '违约信息（客户）';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_ENTERPRISEEXTEND"."pollutionCode" IS '污染源编号';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."evaluationId" IS '评价标准id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."analyzeItemId" IS '分析项目id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."symbol" IS '符号(3.2预留)';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."unit" IS '单位(3.2预留)';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."remark" IS '备注(3.2预留)';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."orderNum" IS '排序值(3.2预留';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_EVALUATIONANALYZEITEM"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."name" IS '评价标准名称';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."code" IS '标准代码';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."categoryId" IS '标准类型（常量BASE_EvaluateType：国标和地标）';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."sampleTypeId" IS '检测类型';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."startTime" IS '实施时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."endTime" IS '废止时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."status" IS '标准状态（枚举EnumEvaluateCriteriaStatus：1代表有效 2代表废止）';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."applyRange" IS '适用范围';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."isDeleted" IS '假删';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_EVALUATIONCRITERIA"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."evaluationId" IS '评价标准id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."parentId" IS '父级id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."name" IS '等级名称（条件项名称）';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."describion" IS '条件描述';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."orderNum" IS '排序值（条件编码）';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_EVALUATIONLEVEL"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."evaluationId" IS '评价标准id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."levelId" IS '评价等级id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."analyzeItemId" IS '分析项目id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."upperLimit" IS '上限';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."upperLimitSymble" IS '上限运算符';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."lowerLimit" IS '下限';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."lowerLimitSymble" IS '下限运算符';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_EVALUATIONVALUE"."dimensionId" IS '量纲id';

COMMENT ON COLUMN "TB_BASE_FOLDER"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_FOLDER"."folderName" IS '文件夹名称';

COMMENT ON COLUMN "TB_BASE_FOLDER"."parentId" IS '父级Id（Guid）';

COMMENT ON COLUMN "TB_BASE_FOLDER"."orderNum" IS '排序值';

COMMENT ON COLUMN "TB_BASE_FOLDER"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_FOLDER"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_FOLDER"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_FOLDER"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_FOLDER"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_FOLDER"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_FOLDER"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."parentId" IS '父级Id（Guid）（预留：多级行业类型使用）';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."industryName" IS '名称';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."industryCode" IS '编号';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."isDeleted" IS '假删';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."orderNum" IS '排序值';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_INDUSTRYTYPE"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."id" IS 'id';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."instrumentName" IS '设备名称';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."pinYin" IS '拼音缩写';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."fullPinYin" IS '全拼';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."model" IS '规格型号';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."instrumentTypeId" IS '仪器类型（常量：BASE_InstrumentType）';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."insRange" IS '量程';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."nicetyRate" IS '准确度等级';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."price" IS '仪器价格';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."place" IS '所在地';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."manager" IS '管理人员id（Guid）';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."managerName" IS '管理员名称（冗余）';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."isShowOnReport" IS '是否出证(是、否)';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."state" IS '状态(枚举：EnumInstrumentStatus：0报废、1正常、2停用、3过期)';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."instrumentsCode" IS '本站编号';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."serialNo" IS '出厂编号';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."factoryName" IS '制造厂商名称';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."saleName" IS '供应商';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."purchaseDate" IS '购置日期';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."belongDeptId" IS '所属科室id（Guid）';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."deptName" IS '所属科室名称（冗余）';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."fixedAssetsCode" IS '固定资产号';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."maintenanceCyc" IS '维护周期(周)';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."maintenanceContent" IS '维护内容';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."maintenanceDate" IS '最近日期（维护）';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."inspectPeriod" IS '核查周期(月)';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."inspectMethod" IS '核查方法';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."inspectResult" IS '核查结果(枚举：EnumInspectResult：1合格、0不合格)';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."inspectDate" IS '最近日期（核查）';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."originCyc" IS '溯源周期(月)';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."originType" IS '溯源方式(枚举：EnumOriginType：1检定、2校准、3自校)';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."originUnit" IS '溯源单位';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."originDate" IS '最近日期（溯源）';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."originEndDate" IS '过期日期（溯源）';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."originResult" IS '溯源结果(枚举：EnumOriginResult：1合格、0不合格)';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."originRemark" IS '溯源备注';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."instrColor" IS '颜色(玻璃仪器特有)';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."recentOpenDate" IS '最近开启日期，与最近使用日期、最近检定日期不同';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."alarmThreshold" IS '设备开启阀值，即设备在该时长内没有启动则不允许使用，单位是天';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."isDeleted" IS '是否删除';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."photoUrl" IS '图片路径';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."scrapTime" IS '报废时间';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."isInspected" IS '是否核查';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."inspectMode" IS '核查方式';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."inspectReason" IS '核查原因';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."responsibleOffice" IS '责任科室';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."shareFlag" IS '是否共用仪器';

COMMENT ON COLUMN "TB_BASE_INSTRUMENT"."isAssistInstrument" IS '是否辅助仪器';

COMMENT ON TABLE "TB_BASE_JOB" IS '定时任务调度表';

COMMENT ON COLUMN "TB_BASE_JOB"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_JOB"."jobName" IS '任务名称';

COMMENT ON COLUMN "TB_BASE_JOB"."jobGroup" IS '任务组名';

COMMENT ON COLUMN "TB_BASE_JOB"."invokeTarget" IS '调用目标字符串';

COMMENT ON COLUMN "TB_BASE_JOB"."cronExpression" IS 'cron执行表达式';

COMMENT ON COLUMN "TB_BASE_JOB"."misfirePolicy" IS '计划执行错误策略（1立即执行 2执行一次 3放弃执行）';

COMMENT ON COLUMN "TB_BASE_JOB"."isConcurrent" IS '是否并发执行（1允许 0禁止）';

COMMENT ON COLUMN "TB_BASE_JOB"."status" IS '状态（1正常 0暂停）';

COMMENT ON COLUMN "TB_BASE_JOB"."remark" IS '备注信息';

COMMENT ON COLUMN "TB_BASE_JOB"."isDeleted" IS '是否删除（0不删除 1删除）';

COMMENT ON COLUMN "TB_BASE_JOB"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_JOB"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_JOB"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_JOB"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_JOB"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_JOB"."modifyDate" IS '修改时间';

COMMENT ON TABLE "TB_BASE_JOBINFO" IS '任务详细信息';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."id" IS '主键id';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."jobId" IS '任务主键';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."beginTime" IS '每日发送的开始时间';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."endTime" IS '每日发送的结束时间';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."isIncludeDefault" IS '是否包含默认人员';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."remindDays" IS '提醒天数';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."sendType" IS '1：平台  2：短信  4：APP  8：微信  16：钉钉';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."isDeleted" IS '假删';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_JOBINFO"."modifyDate" IS '修改时间';

COMMENT ON TABLE "TB_BASE_JOBINFORMPERSON" IS '任务信息通知人';

COMMENT ON COLUMN "TB_BASE_JOBINFORMPERSON"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_JOBINFORMPERSON"."jobInfoId" IS '任务信息id';

COMMENT ON COLUMN "TB_BASE_JOBINFORMPERSON"."personId" IS '人员id';

COMMENT ON COLUMN "TB_BASE_JOBINFORMPERSON"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_JOBINFORMPERSON"."domainId" IS '所属实验室';

COMMENT ON TABLE "TB_BASE_LOGFORLUCKYSHEET" IS 'lucksheet操作记录表';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."id" IS 'id';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."operatorId" IS '操作者id';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."operatorName" IS '操作者名字';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."operateTime" IS '操作时间';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."operateInfo" IS '操作类型（新建、保存、修改等）';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."objectId" IS '对象id';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."comment" IS '说明';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_LOGFORLUCKYSHEET"."domainId" IS '所属实验室';

COMMENT ON TABLE "TB_BASE_PERPETUALDATA" IS '永久性数据存储表';

COMMENT ON COLUMN "TB_BASE_PERPETUALDATA"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_PERPETUALDATA"."type" IS '数据类型';

COMMENT ON COLUMN "TB_BASE_PERPETUALDATA"."value" IS '数据值';

COMMENT ON COLUMN "TB_BASE_PERPETUALDATA"."modifyDate" IS '修改日期';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."testId" IS '测试项目标识';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."rangeConfig" IS '检查项范围';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."judgingMethod" IS '评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."allowLimit" IS '允许限值';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."qcGrade" IS '质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."qcType" IS '质控类型（枚举EnumQCType：1.平行,2.空白,4.加标,8.标准,16.原样加原样,32.串联样,64.曲线校核,128.洗涤剂,256.运输空白,512.仪器空白,1024.试剂空白,2048.罐空白,4096.校正系数检验,8192.替代物,16384.阴性对照试验,32768.阳性对照试验,65536.采样介质空白,131072.空白加标;）';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."qcTypeName" IS '质控类型名称';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."substituteId" IS '替代物标识';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."substituteName" IS '代替物名称';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."formula" IS '穿透率公式';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."checkItem" IS '检查项（枚举EnumCheckItemType:1.出证结果，2.公式参数）';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."checkItemOther" IS '检查项内容';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."isCheckItem" IS '是否需要检查项';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."description" IS '技术说明';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."validate" IS '验证状态 0未验证 1已验证';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."usageNum" IS '使用次数';

COMMENT ON COLUMN "TB_BASE_QUALITYCONTROLLIMIT"."dispositionId" IS '配置id';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."id" IS 'id';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."qcGrade" IS '质控等级';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."qcType" IS '质控类型';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."formula" IS '公式';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."judgingMethod" IS '评判方式';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."isAcquiesce" IS '是否默认';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_QUALITYLIMITDISPOSITION"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."industryTypeId" IS '行业类型（Guid）';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."parentId" IS '父节点（Guid）';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."typeCode" IS '唯一编号（预留）';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."typeName" IS '类型名称';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."remark" IS '备注';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."laboratoryId" IS '所属实验室（Guid）（预留）';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."shortName" IS '简称';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."isDeleted" IS '是否删除';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."keepLongTime" IS '保留时长（预留）';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."reportingCycle" IS '报告周期（预留）';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."category" IS '样品分类(枚举EnumSampleCategory：1.大类2.样品类型3.模板,4.方案模板 )';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."orderNum" IS '排序值';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."icon" IS '图标';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."systemType" IS '系统类型(枚举EnumSystemType：1.LIMS  2.环境质量 4.污染源)';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."defaultLabelGroupId" IS '默认标签分组id';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."fieldTaskGroupId" IS '现场任务分组id';

COMMENT ON COLUMN "TB_BASE_SAMPLETYPE"."checkType" IS '检测类型（0-废水比对，1-废气比对）';

COMMENT ON TABLE "TB_BASE_SUBSTITUTE" IS '替代物信息';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."casCode" IS 'CAS号';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."compoundName" IS '化合物名称';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."addition" IS '加入量';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."isDeleted" IS '是否删除';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."domainId" IS '所属实验室id';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."modifier" IS '最近修改人';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."modifyDate" IS '最新修改时间';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."dimensionId" IS '量纲id';

COMMENT ON COLUMN "TB_BASE_SUBSTITUTE"."dimensionName" IS '量纲名称';

COMMENT ON TABLE "TB_BASE_SYSTEMCONFIG" IS '系统信息管理配置';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."id" IS '主键';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."fullName" IS '系统名称全写';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."shortName" IS '系统名称简写';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."welcomeWord" IS '欢迎登陆语';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."companyName" IS '企业名称';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."companyAddress" IS '企业地址';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."companyPostCode" IS '企业邮编';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."companyPhone" IS '企业联系方式';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."companyEnglishName" IS '企业英文名称';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."orgId" IS '组织机构id';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."creator" IS '创建人';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."createDate" IS '创建时间';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."domainId" IS '所属实验室';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."modifier" IS '修改人';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."modifyDate" IS '修改时间';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."allowFileSuffix" IS '允许上传的文件类型后缀';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."bank" IS '开户行';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."bankAccount" IS '银行账户';

COMMENT ON COLUMN "TB_BASE_SYSTEMCONFIG"."taxNumber" IS '税号';

