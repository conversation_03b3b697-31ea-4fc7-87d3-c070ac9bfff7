package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.dto.customer.*;

import java.util.List;
import java.util.Map;


/**
 * WorkSheetFolder操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface WorkSheetFolderService extends IBaseJpaService<DtoWorkSheetFolder, String> {

    /**
     * 创建检测单编号
     *
     * @return 返回检测单编号
     */
    String createWorkSheetCode();

    /**
     * 核对检测单
     *
     * @param workSheetFolderIds 检测单id集合
     */
    void checkWorkSheetFolder(List<String> workSheetFolderIds);

    /**
     * 根据检测单查询相应的分析数据
     *
     * @param workSheetFolderId 检测单id
     * @return 返回分析数据
     */
    List<DtoAnalyseDataTemp> findAnalyseDataByWorkSheetFolderId(String workSheetFolderId);

    void checkOriginalRecord();

    /**
     * 待检测工作单
     *
     * @param baseCriteria 条件
     * @return 返回数据
     */
    List<Map<String, Object>> findAwaitWorkSheetFolder(BaseCriteria baseCriteria);

    /**
     * 待复核工作单
     *
     * @param baseCriteria 条件
     * @return 返回数据
     */
    List<Map<String, Object>> findCheckWorkSheetFolder(BaseCriteria baseCriteria);


    /**
     * 已完成工作单
     *
     * @param baseCriteria 条件
     * @return 返回数据
     */
    List<Map<String, Object>> findCompleteWorkSheetFolder(BaseCriteria baseCriteria);


    /**
     * 批量按方法创建检测单
     *
     * @param dtoWorkSheetCreate 用里面的testIds
     * @return 返回检测单信息
     */
    List<DtoWorkSheetFolder> createWorkSheetFolderBatch(DtoWorkSheetCreate dtoWorkSheetCreate);


    /**
     * 创建检测单
     *
     * @param dtoWorkSheetCreate 用里面的analyseDataIds
     * @return 返回检测单信息
     */
    DtoWorkSheetFolder createWorkSheetFolder(DtoWorkSheetCreate dtoWorkSheetCreate);

    DtoWorkSheetFolder createWorkSheetFolderWithSameMethod(DtoWorkSheetCreate dtoWorkSheetCreate);

    /**
     * 保存排序值
     *
     * @param workSheetFolderId 工作单id
     * @param sortId            排序id
     */
    void saveSortId(String workSheetFolderId, String sortId);


    /**
     * 加入已有的检测单
     *
     * @param dtoWorkSheetJoin 加入检测单的实体
     */
    void joinOnceWorkSheetFolder(DtoWorkSheetJoin dtoWorkSheetJoin);


    /**
     * 根据检测单id打开检测单
     *
     * @param workSheetFolderId 检测单id
     * @return 返回检测单数据信息
     */
    List<DtoWorkSheetProperty> openWorkSheetAllAnalyseDataById(String workSheetFolderId);

    /**
     * 根据检测单id打开检测单
     *
     * @param workSheetFolderId 检测单id
     * @return 返回检测单数据信息
     */
    List<DtoWorkSheetProperty> openWorkSheetAnalyseDataById(String workSheetFolderId);

    /**
     * 根据检测单id打开检测单
     *
     * @param workSheetFolderId 检测单id
     * @param sampleId          样品id
     * @param isAll             是否
     * @return 返回检测单数据信息
     */
    List<DtoWorkSheetProperty> openWorkSheetAnalyseDataById(String workSheetFolderId, String sampleId, Boolean isAll);

    /**
     * 获取检测单样品信息及是否按样品录入
     *
     * @param workSheetFolderId 检测单id
     * @param sampleId          样品id
     * @return 返回检测单数据信息
     */
    List<DtoWorkSheetProperty> getIsSample(String workSheetFolderId, String sampleId, Boolean isAll);


    /**
     * 找到相应的分析数据
     *
     * @param pageBean     分页条件
     * @param baseCriteria 查询条件
     */
    void findAnalyseDataByPage(PageBean<DtoAnalyseDataTemp> pageBean, BaseCriteria baseCriteria);

    /**
     * 将分析数据转换成map 对象
     *
     * @param dtoAnalyseDataTemp 分析数据
     * @return 返回map对象
     */
    Map<String, Object> getAnalyseDataMap(DtoAnalyseDataTemp dtoAnalyseDataTemp, Map<String, String> codeMap);

    /**
     * 将分析数据转换成map 对象
     *
     * @param dtoAnalyseDataTemp 分析数据
     * @return 返回map对象
     */
    Map<String, Object> getAnalyseDataMap(List<DtoAnalyseDataTemp> groupAnalyseDataTemps, DtoAnalyseDataTemp dtoAnalyseDataTemp, Map<String, String> codeMap);

    /**
     * 设置分组样品id
     *
     * @param analyseDataTemps 分析数据
     */
    void setGroupSampleId(List<DtoAnalyseDataTemp> analyseDataTemps);

    /**
     * 更改分析数据公式
     *
     * @param dtoAnalyseDataChangeFormula 公式的Dto
     * @return 返回相应的更改后的集合
     */
    DtoWorkSheetProperty changeAnalyseDataFormula(DtoAnalyseDataChangeFormula dtoAnalyseDataChangeFormula);


    /**
     * 删除检测单
     *
     * @param ids 检测单id
     * @return 返回删除行数
     */
    Integer delete(List<String> ids);


    /**
     * 检测单数据复核操作
     *
     * @param ids         检测单ids
     * @param status      数据状态
     * @param opinion     备注
     * @param auditorId   审核人
     * @param auditorName 审核人姓名
     */
    List<String> checkPassWorkSheet(List<String> ids, Boolean status, String opinion, String auditorId, String auditorName);

    /**
     * 检测单数据确认操作
     *
     * @param ids       检测单ids
     * @param status    数据状态
     * @param opinion   备注
     * @param checkId   复核人
     * @param checkName 复核人姓名
     */
    List<String> confirmPassWorkSheet(List<String> ids, Boolean status, String opinion, String checkId, String checkName);

    /**
     * 退回检测单
     *
     * @param ids      检测单ids
     * @param opinion  意见
     * @param isReport 是否编制报告
     */
    void backWorkSheet(List<String> ids, String opinion, Boolean isReport);

    /**
     * 审核检测单
     *
     * @param ids     检测单ids
     * @param status  是否审核通过
     * @param opinion 意见
     */
    List<String> auditWorkSheet(List<String> ids, Boolean status, String opinion);


    /**
     * 附件路径
     *
     * @param id 项目id
     * @return 返回相应的路径信息
     */
    DtoWorkSheetFolder findAttachPath(String id);

    /**
     * 查询检测单下默认的仪器
     *
     * @param workSheetFolderId 检测单id
     * @return 质控仪器
     */
    List<DtoInstrument> findDefaultInstrument(String workSheetFolderId);

    /**
     * 切换原始记录单
     *
     * @param recordId          原始记录单Id
     * @param workSheetFolderId 工作单Id
     * @param testIds           测试项目Id
     * @return
     */
    List<DtoParamsData> changeWorkSheetParams(String recordId, String workSheetFolderId, List<String> testIds);

    /**
     * 计算参数数据
     *
     * @param dtoWorkSheetChangeRecord 参数对象
     * @return
     */
    List<DtoParamsData> calculateParamsData(DtoWorkSheetChangeRecord dtoWorkSheetChangeRecord);

    /**
     * 刷新工作单表头参数
     *
     * @param workSheetFolderId 工作单id
     * @return 刷新后的表头参数
     */
    List<DtoWorkSheetParamData> refreshWorksheetParamsData(String workSheetFolderId);

    /**
     * 查询报告分析数据的评价标准
     *
     * @param pageBean              分页信息
     * @param analyseDataEvaluation 评价
     * @return 返回封装对向
     */
    List<DtoAnalyseDataEvaluation> findAnalyseDataEvaluationReport(PageBean<DtoAnalyseData> pageBean, DtoAnalyseDataEvaluationVo analyseDataEvaluation);

    /**
     * 查询分析数据的评价标准
     *
     * @param pageBean              分页信息
     * @param analyseDataEvaluation 评价
     * @return 返回封装对向
     */
    List<DtoAnalyseDataEvaluation> findAnalyseDataEvaluation(PageBean<DtoAnalyseData> pageBean, DtoAnalyseDataEvaluationVo analyseDataEvaluation);


    /**
     * 检测单提交时的校验
     *
     * @param workSheetFolderId 检测单id
     */
    void validationForSubmit(String workSheetFolderId);

    /**
     * 获取工作单下的样品id
     *
     * @param workSheetFolderId 工作单id
     * @return 样品id列表
     */
    List<String> findWorkSheetFolderSampleIds(String workSheetFolderId);

    /**
     * 根据曲线Id获取工作单
     *
     * @param curveId 曲线Id
     */
    List<DtoWorkSheetFolder> findWorksheetByCurves(String curveId);

    /**
     * 批量新增内部质控样
     *
     * @param vo 数据传输对象
     */
    void batchAddQcSample(WorkSheetQualityControlBatchOperationVo vo);

    /**
     * 获取工作单下的默认复核人员
     *
     * @param auditPersonTests 接收实体
     * @return 默认复核人员
     */
    DtoKeyValue findDefaultAuditPerson(DtoAnalyseCheckPerson auditPersonTests);

    /**
     * 获取实验室分析代办任务
     *
     * @param personId 人员id
     * @return 任务数据
     */
    Map<String, Object> findTodoCount(String personId);


    /**
     * 删除同分析项目替代样
     *
     * @param analyseRemove 参数容器
     */
    List<String> removeReplaceSampleWithSameAnalyzeItem(DtoAnalyseRemove analyseRemove);

    /**
     * 重新设置样品排序值
     *
     * @param workSheetFolderId 检测单id
     */
    void resetSortNum(String workSheetFolderId);

    /**
     * 重置检测单签名
     *
     * @param worksheetFolderId 检测单id
     */
    void refreshSignature(String worksheetFolderId);

    /**
     * 判断原始记录单多份生成参数是否开启
     *
     * @return 多份生成参数值
     */
    Boolean workSheetMultiGenerate();

    /**
     * 配置实验室审核
     * 1：一审 2： 二审
     *
     * @return 返回判定
     */
    Boolean workSheetApprovalStep();

    /**
     * 工作单转交
     *
     * @param dtoWorkSheetCheck 传输参数
     */
    void transfer(DtoWorkSheetCheck dtoWorkSheetCheck);

    /**
     * 查MNP
     *
     * @param id 工作单id
     */
    void checkMnp(String id);


    /**
     * 获取检测单id下的实验室指标
     *
     * @param workSheetFolderId 检测单id
     * @return 返回获取检测单id下的实验室指标
     */
    List<DtoTest> findAnalyseTest(String workSheetFolderId);

    /**
     * 获取工作单下所有样品及因子
     *
     * @param workSheetFolderId 送样单id
     * @return RestResponse<Map<String,Object>>
     */
    Map<String, Object> findSampleAndTest(String workSheetFolderId);

    /**
     * 批量更新采集编号
     *
     * @param configMap 采集编号配置
     */
    void batchUpdateGatherCode(Map<String, List<String>> configMap);

    /**
     * 清空原始记录单附件
     *
     * @param workSheetFolderId 工作单id
     */
    void clearRecord(String workSheetFolderId);
}