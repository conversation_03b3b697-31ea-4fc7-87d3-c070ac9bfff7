package com.sinoyd.base.service;

import com.sinoyd.frame.service.CalculationService;

/**
 * 计算相关接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/6/30
 */
public interface CalculateService extends CalculationService {

    /**
     * 修约
     *
     * @param value             值
     * @param significantDigits 有效位数
     * @param decimalDigits     小数位数
     * @return 修约后的值
     */
    String revise(int significantDigits, int decimalDigits, String value);

    /**
     * 修约
     *
     * @param significantDigits 有效位数
     * @param decimalDigits     小数位数
     * @param value             值
     * @param isSci             是否强制修约
     * @return 修约后的值
     */
    String revise(int significantDigits, int decimalDigits, String value, Boolean isSci);
}
