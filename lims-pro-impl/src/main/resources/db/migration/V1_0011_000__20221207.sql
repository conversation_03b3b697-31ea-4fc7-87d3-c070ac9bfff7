-- 质控评价信息表
DROP TABLE IF EXISTS TB_PRO_QualityControlEvaluate;
CREATE TABLE TB_PRO_QualityControlEvaluate
(
    id             varchar(50) NOT NULL COMMENT '主键',
    objectId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '对象id（分析数据Id）',
    qcId           varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '质控信息id',
    checkItem      varchar(100) COMMENT '检查项（对应质控限值配置中的检查项，针对质控样，检查项默认“出证结果”）',
    judgingMethod  int(11) COMMENT '评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）',
    isPass         bit(1) COMMENT '是否合格（是否合格判定不满足判定条件时为空）',
    checkItemValue varchar(100) COMMENT '检查项值',
    allowLimit     varchar(50) COMMENT '允许限值',
    PRIMARY KEY (`id`)
) COMMENT '质控评价信息表';

-- 质控评价信息表中添加加标体积/标准溶液加入量量纲id字段
alter table TB_PRO_QualityControl
    add COLUMN qcVolumeDimensionId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '加标体积/标准溶液加入量量纲id';


-- 质控评价信息表中添加加入标准量/标准物质加入量/替代物加入量量纲id字段
alter table TB_PRO_QualityControl
    add COLUMN qcValueDimensionId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '加入标准量/标准物质加入量/替代物加入量量纲id';

-- 质控评价信息表中添加测定值量纲id字段
alter table TB_PRO_QualityControl
    add COLUMN qcTestValueDimensionId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '测定值量纲id';

-- 质控评价信息表中添加样值量纲id字段
alter table TB_PRO_QualityControl
    add COLUMN realSampleTestValueDimensionId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '样值量纲id';
