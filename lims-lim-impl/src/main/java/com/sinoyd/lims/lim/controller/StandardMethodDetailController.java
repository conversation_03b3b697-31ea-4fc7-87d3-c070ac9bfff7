package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.StandardMethodDetailCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethodDetail;
import com.sinoyd.lims.lim.entity.StandardMethodDetail;
import com.sinoyd.lims.lim.service.StandardMethodDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * StandardMethodDetail控制器
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Api(tags = "标准方法明细管理")
@RestController
@RequestMapping("/api/lim/standardMethodDetail")
@Validated
public class StandardMethodDetailController extends BaseJpaController<DtoStandardMethodDetail, String, StandardMethodDetailService> {

    /**
     * 分页动态条件查询StandardMethodDetail
     *
     * @param criteria 条件参数
     * @return RestResponse<List < StandardMethodDetail>>
     */
    @ApiOperation(value = "分页动态条件查询StandardMethodDetail", notes = "分页动态条件查询StandardMethodDetail")
    @GetMapping
    public RestResponse<List<DtoStandardMethodDetail>> findByPage(StandardMethodDetailCriteria criteria) {
        PageBean<DtoStandardMethodDetail> pageBean = super.getPageBean();
        RestResponse<List<DtoStandardMethodDetail>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询StandardMethodDetail
     *
     * @param id 主键id
     * @return RestResponse<StandardMethodDetail>
     */
    @ApiOperation(value = "按主键查询StandardMethodDetail", notes = "按主键查询StandardMethodDetail")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoStandardMethodDetail> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoStandardMethodDetail> restResponse = new RestResponse<>();
        DtoStandardMethodDetail standardMethodDetail = service.findOne(id);
        restResponse.setData(standardMethodDetail);
        restResponse.setRestStatus(StringUtil.isNull(standardMethodDetail) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增StandardMethodDetail实体
     *
     * @param standardMethodDetail 实体
     * @return RestResponse<StandardMethodDetail>
     */
    @ApiOperation(value = "新增StandardMethodDetail", notes = "新增StandardMethodDetail")
    @PostMapping
    public RestResponse<DtoStandardMethodDetail> create(@Validated @RequestBody DtoStandardMethodDetail standardMethodDetail) {
        RestResponse<DtoStandardMethodDetail> restResponse = new RestResponse<>();
        restResponse.setData(service.save(standardMethodDetail));
        return restResponse;
    }

    /**
     * 修改StandardMethodDetail实体
     *
     * @param standardMethodDetail 实体
     * @return RestResponse<StandardMethodDetail>
     */
    @ApiOperation(value = "修改StandardMethodDetail", notes = "修改StandardMethodDetail")
    @PutMapping
    public RestResponse<DtoStandardMethodDetail> update(@Validated @RequestBody DtoStandardMethodDetail standardMethodDetail) {
        RestResponse<DtoStandardMethodDetail> restResponse = new RestResponse<>();
        restResponse.setData(service.update(standardMethodDetail));
        return restResponse;
    }

    /**
     * 根据id批量删除StandardMethodDetail
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除StandardMethodDetail", notes = "根据id批量删除StandardMethodDetail")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
} 