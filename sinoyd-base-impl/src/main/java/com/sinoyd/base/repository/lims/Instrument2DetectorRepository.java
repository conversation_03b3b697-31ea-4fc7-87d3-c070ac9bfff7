package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoInstrument2Detector;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;

/**
 * 仪器关联多检测仪器操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/4/28
 * @since V100R001
 */
public interface Instrument2DetectorRepository extends IBaseJpaRepository<DtoInstrument2Detector, String> {

    /**
     * 根据仪器id查询多检测器溯源记录
     *
     * @param instrumentId 仪器id
     * @return 多检测器溯源记录
     */
    List<DtoInstrument2Detector> findByInstrumentId(String instrumentId);

    /**
     * 根据仪器ids查询多检测器溯源记录
     *
     * @param instrumentIds 仪器ids
     * @return 多检测器溯源记录
     */
    List<DtoInstrument2Detector> findByInstrumentIdIn(List<String> instrumentIds);
}