package com.sinoyd.lims.pro.vo;

import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Data
public class SampleJudgeDataVo {

    /**
     * 比对数据id集合
     */
    private List<String> ids;

    /**
     * 比对数据集合
     */
    private List<DtoSampleJudgeData> dataList;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;

    private Integer num;

    /**
     * 期待值
     */
    @Length(message = "理论值{validation.message.length}", max = 50)
    private String expectedValue;

    /**
     * 点位id
     */
    private String folderId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 标样编号
     */
    @Length(message = "标样编号{validation.message.length}", max = 50)
    private String standardCode;
}
