package com.sinoyd.lims.api.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;

@Data
@EqualsAndHashCode(callSuper = true)
public class ReceivePhoneCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目类型ids
     */
    private String projectTypeIds;

    /**
     * 关键字
     */
    private String key;

    /**
     * 项目等级
     */
    private Integer grade = EnumPRO.EnumProjectGrade.所有.getValue();

    /**
     * 状态
     */
    private Integer status = EnumPRO.EnumStatus.所有.getValue();

    /**
     * 采样人id
     */
    private String samplingPersonId;

    private Boolean isPointPic = false;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (!this.isPointPic){
            condition.append(" and p.id = pl.projectId and pl.isMakePlan = 0");
            condition.append(" and p.id = r.projectId");

            List<String> statusList = new ArrayList<>();
            for (EnumPRO.EnumProjectStatus status : EnumPRO.EnumProjectStatus.values()) {
                if (status.getValue() >= EnumPRO.EnumProjectStatus.开展中.getValue()) {
                    statusList.add(status.name());
                }
            }
            condition.append(" and p.status in :statusList");
            values.put("statusList", statusList);

            //单个项目类型
            if (StringUtil.isNotEmpty(this.projectTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.projectTypeId)) {
                condition.append(" and p.projectTypeId = :projectTypeId");
                values.put("projectTypeId", this.projectTypeId);
            }
            //多个项目类型
            if (StringUtil.isNotEmpty(this.projectTypeIds)) {
                List<String> typeIds = Arrays.asList(this.projectTypeIds.split(",").clone());
                condition.append(" and p.projectTypeId in :projectTypeIds");
                values.put("projectTypeIds", typeIds);
            }

            //开始时间
            if (StringUtil.isNotEmpty(this.startTime)) {
                Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
                condition.append(" and p.inceptTime >= :startTime");
                values.put("startTime", from);
            }
            //结束时间
            if (StringUtil.isNotEmpty(this.endTime)) {
                Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
                Calendar c = Calendar.getInstance();
                c.setTime(to);
                c.add(Calendar.DAY_OF_MONTH, 1);
                to = c.getTime();
                condition.append(" and p.inceptTime < :endTime");
                values.put("endTime", to);
            }
            //关键字（项目名称、项目编号、委托方）
            if (StringUtil.isNotEmpty(this.key)) {
                condition.append(" and (p.projectCode like :key or p.projectName like :key or p.customerName like :key)");
                values.put("key", "%" + this.key + "%");
            }
            //项目等级
            if (!EnumPRO.EnumProjectGrade.所有.getValue().equals(this.grade)) {
                condition.append(" and p.grade = :grade");
                values.put("grade", this.grade);
            }
            //状态(-1:所有 1:未完成 2:已完成)
            if (!EnumPRO.EnumStatus.所有.getValue().equals(this.status)) {
                if (EnumPRO.EnumStatus.待处理.getValue().equals(this.status)) {
                    condition.append(" and r.uploadStatus = 0");
                } else if (EnumPRO.EnumStatus.已处理.getValue().equals(this.status)) {
                    condition.append(" and r.uploadStatus != 0");
                }
            }
        }else {
            condition.append(" and p.id = r.projectId");
            // 筛选采样日期
            if (StringUtil.isNotEmpty(this.startTime)) {
                Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
                condition.append(" and r.samplingTime >= :startTime");
                values.put("startTime", from);
            }
            //结束时间
            if (StringUtil.isNotEmpty(this.endTime)) {
                Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
                Calendar c = Calendar.getInstance();
                c.setTime(to);
                c.add(Calendar.DAY_OF_MONTH, 1);
                to = c.getTime();
                condition.append(" and r.samplingTime < :endTime");
                values.put("endTime", to);
            }
            //关键字（项目名称、项目编号、受检方）
            if (StringUtil.isNotEmpty(this.key)) {
                condition.append(" and (p.projectCode like :key or p.projectName like :key or p.inspectedEnt like :key)");
                values.put("key", "%" + this.key + "%");
            }
        }

        if (StringUtil.isNotEmpty(this.samplingPersonId)){
            condition.append(" and (r.senderId = :samplingPersonId or exists (select 1 from DtoSamplingPersonConfig sfc" +
                    " where r.id = sfc.objectId and sfc.objectType = :objectType and sfc.samplingPersonId = :samplingPersonId))");
            values.put("objectType", EnumPRO.EnumSamplingType.送样单.getValue());
            values.put("samplingPersonId", samplingPersonId);
        }

        return condition.toString();
    }
}
