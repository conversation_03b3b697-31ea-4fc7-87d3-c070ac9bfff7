package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;

import java.util.Collection;
import java.util.List;


/**
 * ItemRelationParams数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
public interface ItemRelationParamsRepository extends IBaseJpaPhysicalDeleteRepository<DtoItemRelationParams, String> {

    /**
     * 根据分析项目关系ids查询分析项目
     * @param ids 分析项目关系ids
     * @return 分析项目关系详情
     */
    List<DtoItemRelationParams> findByRelationIdIn(Collection<?> ids);

    /**
     * 根据分析项目获取关系详情
     * @param ids 分析项目ids
     * @return 分析项目详情
     */
    List<DtoItemRelationParams> findByAnalyzeItemIdIn(Collection<?> ids);
}