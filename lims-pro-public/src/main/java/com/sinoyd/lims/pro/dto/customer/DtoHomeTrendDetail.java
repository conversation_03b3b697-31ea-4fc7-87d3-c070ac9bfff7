package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.Date;

/**
 * 首页趋势明细
 * <AUTHOR>
 * @version V1.0.0 2020/5/11
 * @since V100R001
 */
@Data
public class DtoHomeTrendDetail {
    /**
     * 时间戳
     */
    private Date time;

    /**
     * 数量
     */
    private Integer count;

    public DtoHomeTrendDetail() {

    }

    /**
     * 构造
     * @param time 时间
     * @param count 数量
     */
    public DtoHomeTrendDetail(Date time, Integer count) {
        this.time = time;
        this.count = count;
    }
}
