package com.sinoyd.lims.monitor.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.FixedPoint2PointService;
import com.sinoyd.lims.monitor.criteria.FixedPoint2PointCriteria;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Point;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FixedPoint2Point服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: FixedPoint2Point服务")
 @RestController
 @RequestMapping("api/monitor/fixedPoint2Point")
 public class FixedPoint2PointController extends BaseJpaController<DtoFixedPoint2Point, String,FixedPoint2PointService> {


    /**
     * 分页动态条件查询FixedPoint2Point
     * @param fixedPoint2PointCriteria 条件参数
     * @return RestResponse<List<FixedPoint2Point>>
     */
     @ApiOperation(value = "分页动态条件查询FixedPoint2Point", notes = "分页动态条件查询FixedPoint2Point")
     @GetMapping
     public RestResponse<List<DtoFixedPoint2Point>> findByPage(FixedPoint2PointCriteria fixedPoint2PointCriteria) {
         PageBean<DtoFixedPoint2Point> pageBean = super.getPageBean();
         RestResponse<List<DtoFixedPoint2Point>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fixedPoint2PointCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FixedPoint2Point
     * @param id 主键id
     * @return RestResponse<DtoFixedPoint2Point>
     */
     @ApiOperation(value = "按主键查询FixedPoint2Point", notes = "按主键查询FixedPoint2Point")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFixedPoint2Point> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFixedPoint2Point> restResponse = new RestResponse<>();
         DtoFixedPoint2Point fixedPoint2Point = service.findOne(id);
         restResponse.setData(fixedPoint2Point);
         restResponse.setRestStatus(StringUtil.isNull(fixedPoint2Point) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FixedPoint2Point
     * @param fixedPoint2Point 实体列表
     * @return RestResponse<DtoFixedPoint2Point>
     */
     @ApiOperation(value = "新增FixedPoint2Point", notes = "新增FixedPoint2Point")
     @PostMapping
     public RestResponse<DtoFixedPoint2Point> create(@RequestBody DtoFixedPoint2Point fixedPoint2Point) {
         RestResponse<DtoFixedPoint2Point> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fixedPoint2Point));
         return restResponse;
      }

     /**
     * 新增FixedPoint2Point
     * @param fixedPoint2Point 实体列表
     * @return RestResponse<DtoFixedPoint2Point>
     */
     @ApiOperation(value = "修改FixedPoint2Point", notes = "修改FixedPoint2Point")
     @PutMapping
     public RestResponse<DtoFixedPoint2Point> update(@RequestBody DtoFixedPoint2Point fixedPoint2Point) {
         RestResponse<DtoFixedPoint2Point> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fixedPoint2Point));
         return restResponse;
      }

    /**
     * "根据id批量删除FixedPoint2Point
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FixedPoint2Point", notes = "根据id批量删除FixedPoint2Point")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }