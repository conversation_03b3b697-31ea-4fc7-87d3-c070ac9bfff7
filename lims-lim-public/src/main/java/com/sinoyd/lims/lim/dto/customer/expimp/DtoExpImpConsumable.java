package com.sinoyd.lims.lim.dto.customer.expimp;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消耗品导入导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DtoExpImpConsumable extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id（新增时不填）", orderNum = "10", width = 20)
    private String id;

    /**
     * 编号
     */
    @Excel(name = "编号", orderNum = "20", width = 24)
    private String codeInStation;

    /**
     * 消耗品名称
     */
    @Excel(name = "消耗品名称(必填)", orderNum = "30", width = 17)
    private String consumableName;

    /**
     * 消耗品类型
     */
    @Excel(name = "消耗品分类(必填)", orderNum = "40", width = 22)
    private String categoryId;

    /**
     * 规格
     */
    @Excel(name = "规格", orderNum = "50", width = 13)
    private String specification;

    /**
     * 等级
     */
    @Excel(name = "等级", orderNum = "60", width = 13)
    private String grade;

    /**
     * 库存数量
     */
    @Excel(name = "库存数量(必填)", orderNum = "70", width = 15)
    private String inventory;

    /**
     * 单位
     */
    @Excel(name = "单位", orderNum = "80", width = 12)
    private String unit;

    /**
     * 提醒人名称
     */
    @Excel(name = "管理员(必填)", orderNum = "83", width = 18)
    private String sendWarnUserName;

    /**
     * 单价
     */
    @Excel(name = "单价", orderNum = "84", width = 15)
    private String unitPrice;


    private String unitId;

    /**
     * 库存警告数量
     */
    @Excel(name = "库存警告数量", orderNum = "85", width = 14)
    private String warningNum;

    /**
     * 详情id
     */
    @Excel(name = "详情id(新增时不填)", orderNum = "87", width = 15)
    private String detailId;

    /**
     * 入库时间
     */
    @Excel(name = "入库日期(必填)", orderNum = "110", width = 23)
    private String storageDate;

    /**
     * 有效日期
     */
    @Excel(name = "有效日期", orderNum = "120", width = 22)
    private String expiryDate;

    /**
     * 存放位置
     */
    @Excel(name = "存放位置", orderNum = "130", width = 22)
    private String keepPlace;

    /**
     * 生产批号
     */
    @Excel(name = "生产批号", orderNum = "150", width = 22)
    private String productionCode;

    /**
     * 生产厂商
     */
    @Excel(name = "生产厂商", orderNum = "160", width = 22)
    private String manufacturerName;


    @Excel(name = "供应厂商", orderNum = "170", width = 22)
    private String supplierName;

    /**
     * 保存条件
     */
    @Excel(name = "保存条件", orderNum = "180", width = 25)
    private String keepCondition;

    /**
     * 安全须知
     */
    @Excel(name = "安全须知", orderNum = "190", width = 22)
    private String safetyInstruction;

    /**
     * 备注
     */
    @Excel(name = "备注", orderNum = "200", width = 20)
    private String remark;


    /**
     * 提醒人id（Guid）
     */
    private String sendWarnUserId;

    /**
     * 是否为标准样品
     */
    private Boolean isStandard = false;

}
