package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;
import lombok.Data;

import java.util.List;

/**
 * 文档权限验证
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@Data
public class DtoDocAuthorityValidate {

    /***
     * 文件夹信息
     */
    private DtoFolder folder;


    /**
     * 文件信息
     */
    private List<DtoDocument> documents;


    /**
     * 权限编码
     */
    private String docAuthorityCode;
}
