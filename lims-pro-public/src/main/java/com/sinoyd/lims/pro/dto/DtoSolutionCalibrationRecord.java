package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SolutionCalibrationRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoSolutionCalibrationRecord实体
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SolutionCalibrationRecord")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoSolutionCalibrationRecord extends SolutionCalibrationRecord {
    /**
     * V始 - V终
     */
    @Transient
    private String diff;

    /**
     * 移取溶液浓度
     */
    @Transient
    private String transferSolutionConcentration;

    /**
     * 计算公式
     */
    @Transient
    private String formula;

    /**
     * 空白均值
     */
    @Transient
    private String blankAvg;
}
