package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.QuotationDetail2TestService;
import com.sinoyd.lims.pro.criteria.QuotationDetail2TestCriteria;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail2Test;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * QuotationDetail2Test服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/5/23
 * @since V100R001
 */
 @Api(tags = "示例: QuotationDetail2Test服务")
 @RestController
 @RequestMapping("api/pro/quotationDetail2Test")
 public class QuotationDetail2TestController extends BaseJpaController<DtoQuotationDetail2Test, String,QuotationDetail2TestService> {


    /**
     * 分页动态条件查询QuotationDetail2Test
     * @param quotationDetail2TestCriteria 条件参数
     * @return RestResponse<List<QuotationDetail2Test>>
     */
     @ApiOperation(value = "分页动态条件查询QuotationDetail2Test", notes = "分页动态条件查询QuotationDetail2Test")
     @GetMapping
     public RestResponse<List<DtoQuotationDetail2Test>> findByPage(QuotationDetail2TestCriteria quotationDetail2TestCriteria) {
         PageBean<DtoQuotationDetail2Test> pageBean = super.getPageBean();
         RestResponse<List<DtoQuotationDetail2Test>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, quotationDetail2TestCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询QuotationDetail2Test
     * @param id 主键id
     * @return RestResponse<DtoQuotationDetail2Test>
     */
     @ApiOperation(value = "按主键查询QuotationDetail2Test", notes = "按主键查询QuotationDetail2Test")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoQuotationDetail2Test> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoQuotationDetail2Test> restResponse = new RestResponse<>();
         DtoQuotationDetail2Test quotationDetail2Test = service.findOne(id);
         restResponse.setData(quotationDetail2Test);
         restResponse.setRestStatus(StringUtil.isNull(quotationDetail2Test) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增QuotationDetail2Test
     * @param quotationDetail2Test 实体列表
     * @return RestResponse<DtoQuotationDetail2Test>
     */
     @ApiOperation(value = "新增QuotationDetail2Test", notes = "新增QuotationDetail2Test")
     @PostMapping
     public RestResponse<DtoQuotationDetail2Test> create(@RequestBody DtoQuotationDetail2Test quotationDetail2Test) {
         RestResponse<DtoQuotationDetail2Test> restResponse = new RestResponse<>();
         restResponse.setData(service.save(quotationDetail2Test));
         return restResponse;
      }

     /**
     * 新增QuotationDetail2Test
     * @param quotationDetail2Test 实体列表
     * @return RestResponse<DtoQuotationDetail2Test>
     */
     @ApiOperation(value = "修改QuotationDetail2Test", notes = "修改QuotationDetail2Test")
     @PutMapping
     public RestResponse<DtoQuotationDetail2Test> update(@RequestBody DtoQuotationDetail2Test quotationDetail2Test) {
         RestResponse<DtoQuotationDetail2Test> restResponse = new RestResponse<>();
         restResponse.setData(service.update(quotationDetail2Test));
         return restResponse;
      }

    /**
     * "根据id批量删除QuotationDetail2Test
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除QuotationDetail2Test", notes = "根据id批量删除QuotationDetail2Test")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }