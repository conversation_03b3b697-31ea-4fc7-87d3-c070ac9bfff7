package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.StatusForProject;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoStatusForProject实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_StatusForProject")
 @Data
 @DynamicInsert
 public  class DtoStatusForProject extends StatusForProject {
   private static final long serialVersionUID = 1L;
 }