package com.sinoyd.lims.lim.controller;

import com.sinoyd.lims.lim.criteria.RecordConfig2TestCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2Test;
import com.sinoyd.lims.lim.service.RecordConfig2TestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;
import java.util.Map;


/**
 * RecordConfig2Test服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
 @Api(tags = "示例: RecordConfig2Test服务")
 @RestController
 @RequestMapping("api/lim/recordConfig2Test")
 public class RecordConfig2TestController extends BaseJpaController<DtoRecordConfig2Test, String,RecordConfig2TestService> {


    /**
     * 分页动态条件查询RecordConfig2Test
     *
     * @param recordConfig2TestCriteria 条件参数
     * @return RestResponse<List < RecordConfig2Test>>
     */
    @ApiOperation(value = "分页动态条件查询RecordConfig2Test", notes = "分页动态条件查询RecordConfig2Test")
    @GetMapping
    public RestResponse<List<DtoRecordConfig2Test>> findByPage(RecordConfig2TestCriteria recordConfig2TestCriteria) {
        PageBean<DtoRecordConfig2Test> pageBean = super.getPageBean();
        RestResponse<List<DtoRecordConfig2Test>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, recordConfig2TestCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }


    @ApiOperation(value = "新增RecordConfig2Test", notes = "新增RecordConfig2Test")
    @PostMapping("/{recordConfigId}")
    public RestResponse<List<DtoRecordConfig2Test>> create(@RequestBody List<String> testIds, @PathVariable String recordConfigId) {
        RestResponse<List<DtoRecordConfig2Test>> restResponse = new RestResponse<>();
        restResponse.setData(service.save(testIds, recordConfigId));
        return restResponse;
    }


    /**
     * "根据id批量删除RecordConfig2Test
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除RecordConfig2Test", notes = "根据id批量删除RecordConfig2Test")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    @ApiOperation(value = "记录参数相关的公式", notes = "记录参数相关的公式")
    @GetMapping("/formula")
    public RestResponse<List<Map<String, Object>>> findTestFormula(@RequestParam("recordConfigId") String recordConfigId,
                                                                   @RequestParam(value = "paramsConfigId", required = false) String paramsConfigId,
                                                                   @RequestParam("isAllConfig") Integer isAllConfig,
                                                                   @RequestParam(value = "analyzeItem",required = false) String analyzeItem,
                                                                   @RequestParam(value = "analyzeMethod",required = false) String analyzeMethod,
                                                                   @RequestParam(value = "formula",required = false) String formula) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> mapList = service.findTestFormula(recordConfigId, paramsConfigId, isAllConfig, analyzeItem,analyzeMethod,formula);
        restResponse.setRestStatus(StringUtil.isEmpty(mapList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(mapList);
        restResponse.setCount(mapList.size());
        return restResponse;
    }


    @ApiOperation(value = "表头参数相关的测试项目", notes = "表头参数相关的测试项目")
    @GetMapping("/test")
    public RestResponse<List<Map<String, Object>>> findTests(@RequestParam("recordConfigId") String recordConfigId,
                                                             @RequestParam("paramsConfigId") String paramsConfigId,
                                                             @RequestParam("isAllConfig") Integer isAllConfig,
                                                             @RequestParam("key") String key) {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        List<Map<String, Object>> mapList = service.findTests(recordConfigId, paramsConfigId, isAllConfig,key);
        restResponse.setRestStatus(StringUtil.isEmpty(mapList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(mapList);
        restResponse.setCount(mapList.size());
        return restResponse;
    }

    /**
     * 批量新增公式参数
     * @return 响应
     */
    @ApiOperation(value = "批量新增公式参数", notes = "批量新增公式参数")
    @PostMapping("/batchAddParams")
    public RestResponse<Void> batchAddParams(@RequestBody Map<String,Object> map){
        RestResponse<Void> res = new RestResponse<>();
        List<String> paramsIdList = (List<String>)map.get("paramsIdList");
        List<String> testFormulaIdList = (List<String>)map.get("testFormulaIdList");
        service.batchAddParams(paramsIdList,testFormulaIdList);
        res.setRestStatus(ERestStatus.SUCCESS);
        return  res;
    }
}