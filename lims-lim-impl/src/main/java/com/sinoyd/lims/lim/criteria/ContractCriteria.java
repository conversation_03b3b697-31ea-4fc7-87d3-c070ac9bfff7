package com.sinoyd.lims.lim.criteria;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;

/**
 * 合同管理查询条件
 * <AUTHOR> 修改：徐肖波
 * @version 1.0.0 2019/3/2
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContractCriteria extends BaseCriteria {

    /**
     * 签订开始时间
     */
    private String dtBegin;
    /**
     * 签订结束时间
     */
    private String dtEnd;
    /**
     * 关键字：合同编号、合同名称、单位
     */
    private String key;
    /**
     * 业务员
     */
    private String salesManId;
    /**
     * 合同类型
     */
    private String contractType;
    /**
     * 当前模块默认传-1，代表所有状态
     */
    private Integer collectionStatus;

    /**
     * 合同状态（-1或者不全默认所有），其余的数据需要进行过滤处理
     */
    private Integer status;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (contractName like :key or contractCode like :key or entName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtils.isNotNullAndEmpty(salesManId) && !UUIDHelper.GUID_EMPTY.equals(this.salesManId)) {
            condition.append(" and (salesManId = :salesManId)");
            values.put("salesManId", this.salesManId);
        }
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and signDate >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and signDate < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        if (collectionStatus.intValue() != -1) {
            condition.append(" and collectionStatus = :collectionStatus");
            values.put("collectionStatus", collectionStatus);
        }
        if (StringUtils.isNotNullAndEmpty(contractType) && !UUIDHelper.GUID_EMPTY.equals(this.contractType)) {
            condition.append(" and (type = :contractType)");
            values.put("contractType", this.contractType);
        }
        if (StringUtil.isNotNull(status) && status.intValue() != -1) {
            condition.append(" and status = :status)");
            values.put("status", this.status);
        }
        condition.append(" and isDeleted = 0");
        return condition.toString();
    }
}