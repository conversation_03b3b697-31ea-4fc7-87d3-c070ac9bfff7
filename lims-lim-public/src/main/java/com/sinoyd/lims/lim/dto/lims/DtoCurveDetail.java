package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.CurveDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoCurveDetial实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_CurveDetail")
 @Data
 @DynamicInsert
 public  class DtoCurveDetail extends CurveDetail {

 /**
  * 检测单id
  */
 @Transient
 private String workSheetId;

 /**
  * 曲线配置日期
  */
 @Transient
 private String configDate;

 /**
  * 曲线零点
  */
 @Transient
 private String curvePoint;

 /**
  * 相关系数
  */
 @Transient
 private String coefficient;

 /**
  * K值
  */
 @Transient
 private String kValue;

 /**
  * B值
  */
 @Transient
 private String bValue;

 /**
  * 曲线类型
  */
 @Transient
 private Integer curveType;

 /**
  * 曲线方程
  */
 @Transient
 private String equation;

 /**
  * 分析项目
  */
 @Transient
 private String redAnalyzeItemName;
}