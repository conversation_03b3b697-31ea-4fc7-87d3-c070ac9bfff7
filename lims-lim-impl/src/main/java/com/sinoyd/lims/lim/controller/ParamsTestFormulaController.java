package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ParamsTestFormulaCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.service.ParamsTestFormulaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数公式相关参数管理控制器
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "参数公式相关参数管理: 参数公式相关参数管理服务")
@RestController
@RequestMapping("/api/lim/paramsTestFormula")
@Validated
public class ParamsTestFormulaController extends BaseJpaController<DtoParamsTestFormula, String, ParamsTestFormulaService> {

    /**
     * 分页查询参数公式相关参数
     *
     * @param paramsTestFormulaCriteria
     * @return 参数List
     */
    @ApiOperation(value = "分页动态条件获取参数", notes = "分页动态条件获取参数")
    @GetMapping("")
    public RestResponse<List<DtoParamsTestFormula>> findByPage(ParamsTestFormulaCriteria paramsTestFormulaCriteria) {
        RestResponse<List<DtoParamsTestFormula>> restResponse = new RestResponse<>();
        PageBean<DtoParamsTestFormula> pageBean = super.getPageBean();
        service.findByPage(pageBean, paramsTestFormulaCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());

        return restResponse;
    }

    /**
     * 获取公式相关的参数数据
     * @param formulaId 公式id
     * @return 返回数据
     */
    @ApiOperation(value = "按主键获取参数公式相关参数", notes = "按主键获取参数公式相关参数")
    @GetMapping("/{formulaId}")
    public RestResponse<List<DtoParamsTestFormula>> findByObjectId(@PathVariable String formulaId) {
        RestResponse<List<DtoParamsTestFormula>> restResponse = new RestResponse<>();
        List<DtoParamsTestFormula> params = service.findByObjectId(formulaId);
        restResponse.setData(params);
        restResponse.setRestStatus(StringUtil.isNull(params) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResponse;
    }

    /**
     * 新增参数公式相关参数
     *
     * @param params 参数公式相关参数实体
     * @return 新增的参数公式相关参数实体
     */
    @ApiOperation(value = "新增参数公式相关参数", notes = "新增参数公式相关参数")
    @PostMapping("")
    public RestResponse<DtoParamsTestFormula> create(@Validated @RequestBody DtoParamsTestFormula params) {
        RestResponse<DtoParamsTestFormula> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParamsTestFormula data = service.save(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新参数公式相关参数
     *
     * @param params 参数公式相关参数实体
     * @return 更新后的参数公式相关参数实体
     */
    @ApiOperation(value = "更新参数公式相关参数", notes = "更新参数公式相关参数")
    @PutMapping("")
    public RestResponse<DtoParamsTestFormula> update(@Validated @RequestBody DtoParamsTestFormula params) {
        RestResponse<DtoParamsTestFormula> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParamsTestFormula data = service.update(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数公式相关参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数公式相关参数", notes = "根据id批量删除参数公式相关参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(service.logicDeleteById(id));
        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数公式相关参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数公式相关参数", notes = "根据id批量删除参数公式相关参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(service.logicDeleteById(ids));

        return restResponse;
    }
}