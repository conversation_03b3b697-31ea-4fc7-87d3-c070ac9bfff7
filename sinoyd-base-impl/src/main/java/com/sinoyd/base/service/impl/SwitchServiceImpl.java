package com.sinoyd.base.service.impl;

import com.sinoyd.base.service.ISwitchService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 相关开关服务实现类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/20
 **/
@Service
public class SwitchServiceImpl implements ISwitchService {

    @Value("${pollutant-discharge-permit.enabled:false}")
    private boolean enablePDP;

    @Override
    public boolean enablePollutantDischargePermit() {
        return this.enablePDP;
    }
}
