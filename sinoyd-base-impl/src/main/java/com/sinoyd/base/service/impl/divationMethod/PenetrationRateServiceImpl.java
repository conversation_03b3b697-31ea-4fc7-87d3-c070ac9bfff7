package com.sinoyd.base.service.impl.divationMethod;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.CalculationService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 穿透率
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
@Slf4j
public class PenetrationRateServiceImpl implements QualityDivationService {

    @Override
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        String retStr = "";
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        if (valueList.size() > 1) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcValue = valueList.get(1);
            if (DivationUtils.isNumber(samValue) && DivationUtils.isNumber(qcValue) && StringUtil.isNotEmpty(controlLimit.getFormula())) {
                //判断数据偏差是否使用穿透率 -- 检查项范围
                String qcRangeLimit = controlLimit.getRangeConfigData();//controlLimit.getRangeConfig();
                Boolean flag = true;
                if (StringUtil.isNotEmpty(qcRangeLimit)) {
                    flag = DivationUtils.calculationResult(qcRangeLimit, new BigDecimal(samValue), calculationService);
                }
                if(flag) {
                    Map<String, Object> limitMap = new HashMap<>();
                    limitMap.put("A", new BigDecimal(samValue));
                    limitMap.put("B", new BigDecimal(qcValue));
                    try {
                        retStr = calculationService.calculationExpression(controlLimit.getFormula(), limitMap).toString();
                        if (StringUtil.isEmpty(retStr)) {
                            retStr = "0";
                        }
                    } catch (Exception ex) {
                        log.error("计算穿透率错误");
                    }
                }
            }
        }
        map.put("qcRate", retStr);
    }
}
