package com.sinoyd.lims.pro.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 环保企业通样品信息推送VO
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2024/11/01
 **/
@Data
public class EnvironmentEnterpriseSampleVO {

    public EnvironmentEnterpriseSampleVO() {
    }

    public EnvironmentEnterpriseSampleVO(String id, String code, String sampleDate, String sampleLocation, String stationCode, List<EnvironmentEnterpriseDataVO> detects) {
        this.id = id;
        this.code = code;
        this.sampleDate = sampleDate;
        this.sampleLocation = sampleLocation;
        this.stationCode = stationCode;
        this.detects = detects;
    }

    /**
     * 样品主键id，要求项目id+样品id组合唯一
     */
    private String id;

    /**
     * 样品编号
     */
    private String code;

    /**
     * 采样时间
     */
    private String sampleDate;

    /**
     * 采样位置
     */
    private String sampleLocation;

    /**
     * 点位编号
     */
    private String stationCode;

    /**
     * 检测结果信息
     */
    private List<EnvironmentEnterpriseDataVO> detects;
}
