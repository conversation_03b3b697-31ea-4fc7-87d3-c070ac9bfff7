package com.sinoyd.base.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.EvaluationLevelRepository;
import com.sinoyd.base.service.EvaluationCriteriaService;
import com.sinoyd.base.service.EvaluationLevelService;
import com.sinoyd.base.service.EvaluationValueService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
 * EvaluationLevel操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
 @Service
public class EvaluationLevelServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoEvaluationLevel,String,EvaluationLevelRepository> implements EvaluationLevelService {

    @Autowired
    @Lazy
    private EvaluationValueService evaluationValueService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private EvaluationCriteriaService evaluationCriteriaService;

    @Autowired
    @Lazy
    private EvaluationLevelService evaluationLevelService;

    @Override
    public void findByPage(PageBean<DtoEvaluationLevel> pb, BaseCriteria evaluationLevelCriteria) {
        pb.setEntityName("DtoEvaluationLevel a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, evaluationLevelCriteria);
    }

    /**
     * 获取标准条件树
     * @param evaluationId 评价等级id
     * @return
     */
    @Override
    public List<TreeNode> findTreeNodeByEvaluationId(String evaluationId) {
        //获取所有的评价等级信息
        List<DtoEvaluationLevel> evaluationLevelList = repository.findByEvaluationId(evaluationId);

        //找到所有的父节点,并根据orderNum排序
        List<DtoEvaluationLevel> parentEvaluationLevels = evaluationLevelList.stream().filter(p -> !StringUtils.isNotNullAndEmpty(p.getParentId())
                || p.getParentId().equals(UUIDHelper.GUID_EMPTY)).sorted(Comparator.comparing(DtoEvaluationLevel::getOrderNum)).collect(Collectors.toList());
        Collections.reverse(parentEvaluationLevels);

        List<TreeNode> treeNodeList = new ArrayList<>();
        for (DtoEvaluationLevel evaluationLevel : parentEvaluationLevels) {
            TreeNode treeNode = new TreeNode();
            treeNode.setId(evaluationLevel.getId());
            treeNode.setParentId(evaluationLevel.getParentId());
            treeNode.setLabel(evaluationLevel.getName());
            treeNode.setIsLeaf(true);
            treeNode.setOrderNum(evaluationLevel.getOrderNum());
            treeNode.setExtent1(evaluationLevel.getDescribion());
            //根据父节点id查找父节点下的子节点
            treeNode.setChildren(getChildEvaluationLevel(evaluationLevelList, evaluationLevel.getId()));
            treeNodeList.add(treeNode);
        }
        return treeNodeList;
    }


    @Transactional
    @Override
    public DtoEvaluationLevel save(DtoEvaluationLevel entity) {
        Integer count = repository.countByEvaluationIdAndOrderNum(entity.getEvaluationId(), entity.getOrderNum());
        if (StringUtil.isNotNull(count) && count > 0) {
            throw new BaseException("已存在相同名称的条件编码！");
        }
        DtoEvaluationLevel item = super.save(entity);
        saveRedis(item);
        return item;
    }

    @Transactional
    @Override
    public DtoEvaluationLevel update(DtoEvaluationLevel entity) {
        Integer count = repository.countByEvaluationIdAndOrderNumAndIdNot(entity.getEvaluationId(),
                entity.getOrderNum(), entity.getId());
        if (StringUtil.isNotNull(count) && count > 0) {
            throw new BaseException("已存在相同名称的条件编码！");
        }
        DtoEvaluationLevel item = super.update(entity);
        saveRedis(item);
        return item;
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        DtoEvaluationLevel dtoEvaluationLevel = findOne((String) id);
        deleteRedis(dtoEvaluationLevel, dtoEvaluationLevel.getEvaluationId());
        Integer count =  super.logicDeleteById(id);
        deleteChildRecords(dtoEvaluationLevel.getEvaluationId(), dtoEvaluationLevel.getId());
        return count;
    }

    /**
     * 删除子记录
     *
     * @param evaluationId
     * @param parentId
     */
    private void deleteChildRecords(String evaluationId, String parentId){
        List<DtoEvaluationLevel> dtoEvaluationLevelList = repository.findByParentIdAndEvaluationId(parentId, evaluationId);
        if(StringUtil.isNotEmpty(dtoEvaluationLevelList)){
            for(DtoEvaluationLevel dtoEvaluationLevel : dtoEvaluationLevelList){
                super.delete(dtoEvaluationLevel);
                deleteRedis(dtoEvaluationLevel, evaluationId);
            }
        }
    }


    /**
     * 获取子节点的评价信息
     *
     * @return
     */
    private List<TreeNode> getChildEvaluationLevel(List<DtoEvaluationLevel> evaluationLevelList, String id) {
        List<TreeNode> treeNodeList = new ArrayList<>();
        //根据父节点id,获取子节点,并根据orderNum排序
        List<DtoEvaluationLevel> childEvaluationLevels = evaluationLevelList.stream().filter(p -> p.getParentId().equals(id)).sorted(Comparator.comparing(DtoEvaluationLevel::getOrderNum)).collect(Collectors.toList());
        Collections.reverse(childEvaluationLevels);

        for (DtoEvaluationLevel evaluationLevel : childEvaluationLevels) {
            TreeNode treeNode = new TreeNode();
            treeNode.setId(evaluationLevel.getId());
            treeNode.setParentId(evaluationLevel.getParentId());
            treeNode.setLabel(evaluationLevel.getName());
            treeNode.setOrderNum(evaluationLevel.getOrderNum());
            treeNode.setIsLeaf(true);
            treeNode.setExtent1(evaluationLevel.getDescribion());
            treeNode.setChildren(getChildEvaluationLevel(evaluationLevelList, evaluationLevel.getId()));
            treeNodeList.add(treeNode);
        }
        return treeNodeList;
    }


    @Override
    public List<DtoEvaluationLevel> findLevelByEvaluationId(String evaluationId) {
        List<DtoEvaluationLevel> evaluationLevels = repository.findByEvaluationId(evaluationId);
        if (StringUtil.isNotNull(evaluationLevels)) {
            List<String> levelIds = evaluationLevels.stream().map(DtoEvaluationLevel::getId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotNull(levelIds) && levelIds.size() > 0) {
                List<DtoEvaluationValue> evaluationValues = evaluationValueService.findEvaluationValueByLevelId(levelIds);
                if (StringUtil.isNotNull(evaluationValues)) {
                    for (DtoEvaluationLevel dtoEvaluationLevel : evaluationLevels) {
                        dtoEvaluationLevel.setEvaluationValue(evaluationValues.stream().filter(p -> p.getLevelId().equals(dtoEvaluationLevel.getId())).collect(Collectors.toList()));
                    }
                }
            }
        }
        return evaluationLevels;
    }


    /**
     * 保存Redis数据
     *
     * @param dtoEvaluationLevel 评价等级信息
     */
    private void saveRedis(DtoEvaluationLevel dtoEvaluationLevel) {
        String evaluationId = dtoEvaluationLevel.getEvaluationId();
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        Object json = redisTemplate.opsForHash().get(key, evaluationId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
            };
            DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);

            List<DtoEvaluationLevel> evaluationLevels = item.getEvaluationLevel();
            if (StringUtil.isNotNull(evaluationLevels)) {
                Optional<DtoEvaluationLevel> optional = evaluationLevels.stream().filter(p -> p.getId().equals(dtoEvaluationLevel.getId())).findFirst();

                //找到原先的评价等级信息，并且排除集合中，再把新的加入集合中
                if (StringUtil.isNotNull(optional) && optional.isPresent()) {
                    DtoEvaluationLevel old = optional.get();
                    evaluationLevels.remove(old);
                }
            } else { //防止之前未存储，重新初始化数据
                evaluationLevels = evaluationLevelService.findLevelByEvaluationId(evaluationId);
            }
            if (StringUtil.isNotNull(evaluationLevels)) {
                evaluationLevels.add(dtoEvaluationLevel);
            }
            item.setEvaluationLevel(evaluationLevels);
            redisTemplate.opsForHash().put(key, evaluationId, JsonStream.serialize(item));
        } else {
            evaluationCriteriaService.initEvaluationCriteriaRedisById(dtoEvaluationLevel.getEvaluationId());
        }
    }

    /**
     * 删除redis数据
     *
     * @param dtoEvaluationLevel 评价等级信息
     * @param evaluationId       评价标准id
     */
    private void deleteRedis(DtoEvaluationLevel dtoEvaluationLevel, String evaluationId) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.getValue());
        Object json = redisTemplate.opsForHash().get(key, evaluationId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<DtoEvaluationCriteria> typeLiteral = new TypeLiteral<DtoEvaluationCriteria>() {
            };
            DtoEvaluationCriteria item = JsonIterator.deserialize((String) json, typeLiteral);

            List<DtoEvaluationLevel> evaluationLevels = item.getEvaluationLevel();
            if (StringUtil.isNotNull(evaluationLevels)) {
                Optional<DtoEvaluationLevel> optional = evaluationLevels.stream().filter(p -> p.getId().equals(dtoEvaluationLevel.getId())).findFirst();
                //找到原先的评价等级信息，并且排除集合中，再把新的加入集合中
                if (StringUtil.isNotNull(optional) && optional.isPresent()) {
                    DtoEvaluationLevel old = optional.get();
                    evaluationLevels.remove(old);
                }
                item.setEvaluationLevel(evaluationLevels);
            } else { //防止之前未存储，重新初始化数据
                evaluationLevels = evaluationLevelService.findLevelByEvaluationId(evaluationId);
                if (StringUtil.isNotNull(evaluationLevels)) {
                    item.setEvaluationLevel(evaluationLevels);
                }
            }
            redisTemplate.opsForHash().put(key, evaluationId, JsonStream.serialize(item));
        } else {
            evaluationCriteriaService.initEvaluationCriteriaRedisById(dtoEvaluationLevel.getEvaluationId());
        }
    }
}