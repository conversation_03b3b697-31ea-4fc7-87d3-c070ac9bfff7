package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Instrument2Detector实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/4/28
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "Instrument2Detector")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Instrument2Detector extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Instrument2Detector() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 仪器Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("仪器Id")
    private String instrumentId;


    /**
     * 检测器名称
     */
    @Column(length = 255)
    @ApiModelProperty("检测器名称")
    @Length(message = "检测器名称{validation.message.length}", max = 255)
    private String detectorName;

    /**
     * 溯源周期(月)
     */
    @Column(nullable = false)
    @ColumnDefault("12")
    @ApiModelProperty("溯源周期(月)")
    private BigDecimal originCyc;

    /**
     * 溯源方式(枚举：EnumOriginType：1检定、2校准、3自校)
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("溯源方式(枚举：EnumOriginType：1检定、2校准、3自校)")
    private Integer originType;

    /**
     * 溯源单位
     */
    @Column(length = 100)
    @ApiModelProperty("溯源单位")
    @Length(message = "溯源单位{validation.message.length}", max = 100)
    private String originUnit;

    /**
     * 最近日期（溯源）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("最近日期（溯源）")
    private Date originDate;

    /**
     * 过期日期（溯源）
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("过期日期（溯源）")
    private Date originEndDate;

    /**
     * 溯源结果(枚举：EnumOriginResult：1合格、0不合格)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("溯源结果(枚举：EnumOriginResult：1合格、0不合格)")
    private Integer originResult;

    /**
     * 溯源备注
     */
    @Column(length = 1000)
    @ApiModelProperty("溯源备注")
    @Length(message = "溯源备注{validation.message.length}", max = 1000)
    private String originRemark;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;


    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;


}