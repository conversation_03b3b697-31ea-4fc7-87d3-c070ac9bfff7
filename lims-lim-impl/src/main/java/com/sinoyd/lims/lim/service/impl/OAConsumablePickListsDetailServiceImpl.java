package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePickListsDetail;
import com.sinoyd.lims.lim.repository.lims.OAConsumablePickListsDetailRepository;
import com.sinoyd.lims.lim.service.OAConsumablePickListsDetailService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * OAConsumablePickListsDetail操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/4/2
 * @since V100R001
 */
 @Service
public class OAConsumablePickListsDetailServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoOAConsumablePickListsDetail,String,OAConsumablePickListsDetailRepository> implements OAConsumablePickListsDetailService {

    @Override
    public void findByPage(PageBean<DtoOAConsumablePickListsDetail> pb, BaseCriteria oAConsumablePickListsDetailCriteria) {
        pb.setEntityName("DtoOAConsumablePickListsDetail a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, oAConsumablePickListsDetailCriteria);
    }

    @Override
    public List<DtoOAConsumablePickListsDetail> findByConsumableIds(Collection<String> consumableIds) {
        if (StringUtil.isNotEmpty(consumableIds)){
            return repository.findByConsumableIdIn(consumableIds);
        }
        return new ArrayList<>();
    }
}