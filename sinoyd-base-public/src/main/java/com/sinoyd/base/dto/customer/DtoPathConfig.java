package com.sinoyd.base.dto.customer;

import lombok.Data;

/**
 * 附件机制的自定义类
 * <AUTHOR>
 * @version V1.0.0 2019/10/19
 * @since V100R001
 */
@Data
public class DtoPathConfig {

    /**
     * 编号
     */
    private String code;

    /**
     * 类名
     */
    private String className;


    /**
     * 方法
     */
    private String method;

    /**
     * 占位符（多个按顺序隔开）
     */
    private String placeholder;

    /**
     * 路径
     */
    private String path;
}
