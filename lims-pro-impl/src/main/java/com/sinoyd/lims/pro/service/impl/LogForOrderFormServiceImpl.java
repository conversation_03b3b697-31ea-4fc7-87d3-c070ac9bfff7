package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoLogForOrderForm;
import com.sinoyd.lims.pro.repository.LogForOrderFormRepository;
import com.sinoyd.lims.pro.service.LogForOrderFormService;
import org.springframework.stereotype.Service;

/**
 * 订单日志服务
 *
 * <AUTHOR>
 * @version V1.0.0 2021/04/27
 * @since V100R001
 */
@Service
public class LogForOrderFormServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoLogForOrderForm, String, LogForOrderFormRepository> implements LogForOrderFormService {

    @Override
    public void findByPage(PageBean<DtoLogForOrderForm> page, BaseCriteria criteria) {
        page.setSelect("select a");
        page.setEntityName("DtoLogForOrderForm a");
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        page.setSort("operateTime-");
        super.findByPage(page, criteria);
        page.getData().forEach(log -> {
            if (log.getLogType() == 1) {
                log.setLogTypeName("订单基本信息");
            } else {
                log.setLogTypeName("订单费用");
            }
            if (log.getObjectType() == 1) {
                log.setObjectTypeName("订单");
            } else {
                log.setObjectTypeName("订单详细数据");
            }
        });
    }
}
