package com.sinoyd.base.dto.vo;

import javax.xml.bind.annotation.XmlElement;

/**
 * 质控规则配置
 *
 * <AUTHOR>
 * @version 5.2.0
 * @since 2023/05/12
 */
public class OrderReviseVO {
    /**
     * 常量编码
     */
    private String code;

    /**
     * 常量名称
     */
    private String name;

    /**
     * 排序值
     */
    private String order;

    /**
     * 是否跟随原样
     */
    private String followSample;

    /**
     * 是否关联拓展
     */
    private String isExtend;

    /**
     * 原样是否在质控样之上
     */
    private String sampleFirst;

    /**
     * 质控修约有效位
     */
    private Integer sign;

    /**
     * 质控修约小数位
     */
    private Integer scale;

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public void setFollowSample(String followSample) {
        this.followSample = followSample;
    }

    public void setIsExtend(String isExtend) {
        this.isExtend = isExtend;
    }

    public void setSampleFirst(String sampleFirst) {
        this.sampleFirst = sampleFirst;
    }

    public void setSign(Integer sign) {
        this.sign = sign;
    }

    public void setScale(Integer scale) {
        this.scale = scale;
    }

    @XmlElement(name = "code")
    public String getCode() {
        return code;
    }

    @XmlElement(name = "name")
    public String getName() {
        return name;
    }

    @XmlElement(name = "order")
    public String getOrder() {
        return order;
    }

    @XmlElement(name = "followSample")
    public String getFollowSample() {
        return followSample;
    }

    @XmlElement(name = "isExtend")
    public String getIsExtend() {
        return isExtend;
    }

    @XmlElement(name = "sampleFirst")
    public String getSampleFirst() {
        return sampleFirst;
    }

    @XmlElement(name = "sign")
    public Integer getSign() {
        return sign;
    }

    @XmlElement(name = "scale")
    public Integer getScale() {
        return scale;
    }
}
