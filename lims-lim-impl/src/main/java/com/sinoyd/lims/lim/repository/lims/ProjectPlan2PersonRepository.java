package com.sinoyd.lims.lim.repository.lims;


import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoProjectPlan2Person;

import java.util.List;


/**
 * EnvironmentalRecord2TestRepository数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/03/05
 * @since V100R001
 */
public interface ProjectPlan2PersonRepository extends IBaseJpaPhysicalDeleteRepository<DtoProjectPlan2Person, String> {

    /**
     * 根据ProjectPlanId查询
     * @param ProjectPlanId
     * @return
     */
    List<DtoProjectPlan2Person> findAllByProjectPlanId(String ProjectPlanId);
}