package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.DetailAnalyseData;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoDetailAnalyseData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_DetailAnalyseData") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoDetailAnalyseData extends DetailAnalyseData {
   private static final long serialVersionUID = 1L;
 }