-- 废水比对报告模板及组件配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('21b662e8-4ab9-446c-b8bf-d263d4ddcb16', '63393df4-fd9e-49db-a1da-428b03727e30', 'ReportEditNew', '报告编制V2.0',
        'WaterCompareStd', '废水比对报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-10-27 10:55:09', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-10-27 10:55:09', NULL);

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, sampleTypeIds, orderNum)
VALUES ('7864bfee-f557-4129-aa8e-df9012683d8c', '废水比对报告', 3, '63393df4-fd9e-49db-a1da-428b03727e30',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-10-27 10:55:57', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-10-27 10:55:57', '', 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('1471ad7d-70bf-45aa-8279-feb2ae6f62b7', 'dtBdWaterZkTable', '废水比对报告质控样数据表组件', 'dtBdWaterZkTable', 'dtBdZkSrc',
        0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-10-27 10:40:40', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-10-27 10:40:40', '0', '0', 0, 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('47e6134f-08be-4bd4-a60c-5554dfd1277f', 'dtBdWaterFixedTable', '废水比对报告固定标准依据表', 'dtBdWaterFixedTable', '', 0, 0,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:44:47',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:44:47', '0', '0',
        0, 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('8b3fd405-8628-45cb-b2b4-b370d1f03080', 'dtBdWaterHeadTable', '废水比对报告表头组件', 'dtBdWaterHeadTable', '', 0, 0, '',
        b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:44:07',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:44:07', '0', '0',
        0, 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('a3146f56-677b-47d3-9418-a33c47d288c7', 'dtBdWaterTable', '废水比对报告实际水样测定检测数据表组件', 'dtBdWaterTable', 'dtBdSrc', 0,
        0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:40:01',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:40:01', '0', '0',
        0, 0, 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('b4159593-8252-4f2f-b45d-d7828815e1e5', 'waterBdDataSource', '废水比对报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtBdWaterHeadTable\", \"dtBdWaterTable\", \"dtBdWaterZkTable\", \"dtBdWaterCriterionTable\", \"dtBdWaterFixedTable\"]',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:50:23',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-10-27 10:50:23', '0', '0',
        0, 0, 0, 0);


INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode,
                                speedCalculationMode, compoundAvgCalculationMode,
                                gasParamSplitMode)
VALUES ('e6706ab0-56b8-48b5-badd-602bc4aaea0b', 'dtBdWaterCriterionTable', '废水比对报告技术说明数据表组件', 'dtBdWaterCriterionTable',
        'dtCriterionBdSrc', 0, 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-10-27 10:41:32', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-10-27 10:41:32', '0', '0', 0, 0, 0, 0);

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('19d65e82-f02b-4f27-af7a-cad1a4b06f14', '63393df4-fd9e-49db-a1da-428b03727e30',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('5c363611-ea7f-4dc6-ae94-24722555e933', '63393df4-fd9e-49db-a1da-428b03727e30',
        'b4159593-8252-4f2f-b45d-d7828815e1e5');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('7df5fa2b-4193-4d66-b598-0a71387d5437', '63393df4-fd9e-49db-a1da-428b03727e30',
        '6130da13-013a-4a25-a856-bd1f16fc691b');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('a9596c4a-3119-4546-af8f-bd1754e80bdc', '63393df4-fd9e-49db-a1da-428b03727e30',
        'f33aa5b0-7ddd-4a62-8d7a-2b219f574bad');

INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('96856e93-d3ba-4388-ac0d-df8c28cd2f9a', '5c363611-ea7f-4dc6-ae94-24722555e933',
        'sampleData_sampleFolderId_fieldGroupType', 10);

update TB_LIM_ReportModule
set sonTableJson = '["normalWaterStdDataSource", "groundWaterStdDataSource", "groundWaterStdDataSource", "orgToStdDataSource", "unOrgWeaToStdDataSource", "solidStdDataSource", "noiseDayNightDataSource", "soilStdDataSource", "waterBdDataSource"]'
where moduleCode = 'comprehensiveStdDataSource';