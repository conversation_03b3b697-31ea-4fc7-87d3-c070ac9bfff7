package com.sinoyd.lims.pro.service.impl;

import java.util.*;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.repository.OAProjectExpendRepository;
import com.sinoyd.lims.pro.service.OAProjectExpendService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目支出 业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@Service
public class OAProjectExpendServiceImpl extends BaseJpaServiceImpl<DtoOAProjectExpend, String, OAProjectExpendRepository>
        implements OAProjectExpendService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    @Override
    @Transactional
    public String startProcess(DtoOATaskCreate<DtoOAProjectExpend> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.项目支出, taskDto, vars);

        DtoOAProjectExpend projectExpend = taskDto.getData();
        // 添加支出信息
        super.save(projectExpend);

        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(projectExpend.getId());

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelation);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<DtoOAProjectExpend, String> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<DtoOAProjectExpend, String> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(taskId);

        DtoOAProjectExpend projectExpend = super.findOne(relation.getObjectId());
        taskDetail.setDetail(projectExpend);

        // 设置扩展信息
        // taskDetail.setExtend(extend);

        return taskDetail;
    }

    @Override
    public void findByPage(PageBean<DtoOAProjectExpend> pb, BaseCriteria oaProjectExpendCriteria) {
        pb.setEntityName("DtoOAProjectExpend a,DtoOATaskRelation tr,DtoOATask t");
        pb.setSelect("select a,t.title,t.description");
        comRepository.findByPage(pb, oaProjectExpendCriteria);

        List<DtoOAProjectExpend> datas = pb.getData();
        List<DtoOAProjectExpend> newDatas = new ArrayList<>();

        Iterator<DtoOAProjectExpend> expendIte = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (expendIte.hasNext()) {
            Object obj = expendIte.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoOAProjectExpend expend = (DtoOAProjectExpend) objs[0];
            expend.setTitle((String) objs[1]);
            expend.setDescription((String) objs[2]);
            newDatas.add(expend);
        }
        pb.setData(newDatas);
    }

    @Override
    public void confirm(String id) {
        repository.confirm(id, true);
    }

    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<DtoOAProjectExpend> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.项目支出, taskDto, vars);
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<DtoOAProjectExpend> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<DtoOAProjectExpend> taskDto,DtoOATask oaTask){
        DtoOAProjectExpend projectExpend = taskDto.getData();
        // 添加支出信息
        super.save(projectExpend);
        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(projectExpend.getId());
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelation);
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<DtoOAProjectExpend> taskDto) {
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }
}
