package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoQualityManage;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoQMProjectEvaluation;

import java.util.List;


/**
 * QualityManage操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface QualityManageService extends IBaseJpaService<DtoQualityManage, String> {
    /**
     * 外部质控添加质控信息
     *
     * @param sampleId 样品id
     * @param testIds  测试项目id
     */
    void saveOutside(String sampleId, List<String> testIds);

    /**
     * 添加质控信息
     * @param sample 样品
     * @param record 送样单
     * @param qualityManages 质控信息
     */
    void addQualityManages(DtoSample sample, DtoReceiveSampleRecord record, List<DtoQualityManage> qualityManages);

    /**
     *
     * @param instrumentId 仪器id
     * @param ids 质控信息id集合
     */
    void updateInstrument(String instrumentId,List<String>ids);

    /**
     * 获取项目评价信息
     * @param projectId 项目id
     * @return 项目评价
     */
    List<DtoQMProjectEvaluation> findProjectEvaluation(String projectId);

    /**
     * 批量新增测试项目
     * @param qualityManageList 测试项目列表
     * @return 测试项目列表
     */
    List<DtoQualityManage> batchCreate(List<DtoQualityManage> qualityManageList);

    /**
     * 校验测试项目是否存在
     * @param qualityManageList 测试项目列表
     * @return 测试项目列表
     */
    List<DtoQualityManage> checkBatchCreate(List<DtoQualityManage> qualityManageList);
}