package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * SampleGroup查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleGroupCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 样品标识
     */
    private List<String> sampleIds;

    /**
     * 测试项目标识
     */
    private List<String> testIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and s.id = a.sampleId");
        condition.append(" and s.isDeleted = 0");
        if (StringUtil.isNotEmpty(this.receiveId)) {
            condition.append(" and s.receiveId = :receiveId");
            values.put("receiveId", this.receiveId);
        }
        if (StringUtil.isNotEmpty(this.sampleIds)) {
            condition.append(" and a.sampleId in :sampleIds");
            values.put("sampleIds", this.sampleIds);
        }
        return condition.toString();
    }
}