package com.sinoyd.lims.pro.vo;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRangeResult;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataCalculation;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 分析数据计算VO
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/31
 */
@Data
@Accessors(chain = true)
public class AnalyseDataCalculationVo {

    /**
     * 计算数据内容
     */
    private DtoAnalyseDataCalculation dtoAnalyseDataCalculation;

    /**
     * 质控范围
     */
    private DtoTestQCRangeResult rangeResult;

    /**
     * 测试项目
     */
    private DtoTest test;

    /**
     * 质控配置集合
     */
    private List<DtoQualityControlLimit> qLimitList;
}
