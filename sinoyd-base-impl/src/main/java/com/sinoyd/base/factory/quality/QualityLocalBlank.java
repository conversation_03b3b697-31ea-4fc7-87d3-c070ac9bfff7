package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;

import java.util.List;
import java.util.Map;

/**
 * 现场空白样
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2023/7/14
 */
public class QualityLocalBlank extends QualityControlKind {

    /**
     * 空白类别
     *
     * @return 空白类别
     */
    @Override
    public Integer qcTypeValue() {
        return 1048576;
    }

    /**
     * 返回对应的质控名称
     *
     * @return 质控名称
     */
    @Override
    public String qcTypeName() {
        return "现场空白";
    }

    /**
     * 样品类别
     *
     * @param qcGrade 质控类型
     * @return 质控类别
     */
    @Override
    public String getSampleProperty(Integer qcGrade) {
        String sampleProperty = "";
        if (qcGrade.equals(1)) {
            sampleProperty = "现场空白";
        } else {
            sampleProperty = "现场空白(实验室)";
        }
        return sampleProperty;
    }

    /**
     * 记录
     *
     * @param map 参数
     * @return 记录内容
     */
    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了现场空白样。";
    }

    /**
     * 样品名称
     * @param folderName 原样名称
     * @param qcGrade 质控类型
     * @return 样品名称
     */
    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        String redFolderName = "";
        if (qcGrade.equals(1)) {
            redFolderName = "现场空白";
        } else {
            redFolderName = "现场空白(室内)";
        }
        return redFolderName;
    }

    @Override
    public DtoQualityConfig getQualityConfig(){
        return getConfig("QualityLocalBlank");
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return getConfig("QualityLocalBlank", orderReviseVOList);
    }


    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }

}
