package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * LocalTaskPeopleCompare实体
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/27
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description="CostInfo")
@Data
public class LocalTaskPeopleCompare extends LimsBaseEntity {

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 考核负责人标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("考核负责人标识")
    private String leaderId;

    /**
     * 送样单标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("送样单标识")
    private String receiveId;

    /**
     * 项目标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目标识")
    private String projectId;

    /**
     * 考核人员标识,多个英文逗号拼接
     */
    @Column(length = 500, nullable = false)
    @ColumnDefault("''")
    @ApiModelProperty("考核人员标识,多个英文逗号拼接")
    private String checkPeopleId;

    /**
     * 考核日期
     */
    @ApiModelProperty("考核日期")
    private Date checkDate;
}
