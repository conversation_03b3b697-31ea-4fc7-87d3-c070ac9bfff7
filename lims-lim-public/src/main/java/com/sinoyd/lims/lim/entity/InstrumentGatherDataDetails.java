package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * 仪器接入实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "InstrumentGather")
@Data
@EntityListeners(AuditingEntityListener.class)
public class InstrumentGatherDataDetails implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();


    /**
     * 参数收集id
     */
    @ApiModelProperty("参数收集id")
    @Column(length = 50)
    private String instrumentGatherDataId;


    /**
     * 仪器接入参数id
     */
    @ApiModelProperty("仪器接入参数id")
    @Column(length = 50)
    private String InstrumentGatherParamsId;

    /**
     * 参数名称
     */
    @ApiModelProperty("参数名称")
    @Column(length = 100)
    @Length(message = "参数名称{validation.message.length}", max = 100)
    private String parmaName;

    /**
     * 参数值
     */
    @ApiModelProperty("参数值")
    @Column(length = 255)
    @Length(message = "参数值{validation.message.length}", max = 255)
    private String paramValue;

    /**
     * 量纲
     */
    @ApiModelProperty("量纲")
    @Column(length = 20)
    @Length(message = "量纲{validation.message.length}", max = 20)
    private String dimension;

}