package com.sinoyd.lims.webbackend.controller;

import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.controller.BaseController;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.EntityMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("api/entity/entityMapping")
public class EntityMappingController extends BaseController {

    @Autowired
    private EntityMappingService entityMappingService;

    @Autowired
    private CalculationService calculationService;

    @Autowired
    private CommonRepository commonRepository;


    @Autowired
    private AnalyzeItemRepository analyzeItemRepository;

    @RequestMapping("")
    public RestResponse<HashMap> getEntityMapping() {
        RestResponse<HashMap> restResponse = new RestResponse<>();
        Collection<String> packages = new ArrayList<>();
        packages.add("com.sinoyd.base.entity");
        packages.add("com.sinoyd.lims.lim.entity");
        restResponse.setData(entityMappingService.findEntityMapping(packages));
        return restResponse;
    }

    @RequestMapping("/calculation")
    public RestResponse<Object> calculationExpression() {
        RestResponse<Object> restResponse = new RestResponse<>();
        Map<String, Object> map = new HashMap<>();
        map.put("x", "5.995");
        Object data = calculationService.calculationExpression("[x] < 0.00 || [x] > 70.00", map);
        restResponse.setData(data);
        return restResponse;
    }


}
