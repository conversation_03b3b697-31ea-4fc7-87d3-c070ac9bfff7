package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.CostRuleForEntService;
import com.sinoyd.lims.lim.criteria.CostRuleForEntCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoCostRuleForEnt;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * 企业费用规则服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
 @Api(tags = "示例: CostRuleForEnt服务")
 @RestController
 @RequestMapping("api/lim/costRuleForEnt")
 @Validated
 public class CostRuleForEntController extends BaseJpaController<DtoCostRuleForEnt, String,CostRuleForEntService> {


    /**
     * 分页动态条件查询CostRuleForEnt
     *
     * @param costRuleForEntCriteria 条件参数
     * @return RestResponse<List < CostRuleForEnt>>
     */
    @ApiOperation(value = "分页动态条件查询CostRuleForEnt", notes = "分页动态条件查询CostRuleForEnt")
    @GetMapping
    public RestResponse<List<DtoCostRuleForEnt>> findByPage(CostRuleForEntCriteria costRuleForEntCriteria) {
        PageBean<DtoCostRuleForEnt> pageBean = super.getPageBean();
        RestResponse<List<DtoCostRuleForEnt>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, costRuleForEntCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 保存企业费用规则
     *
     * @param costRuleForEnt 实体列表
     * @return RestResponse<List <  D t oCostRuleForEnt>>
     */
    @ApiOperation(value = "保存企业费用规则", notes = "保存企业费用规则")
    @PostMapping
    public RestResponse<List<DtoCostRuleForEnt>> create(@Validated @RequestBody List<DtoCostRuleForEnt> costRuleForEnt) {
        RestResponse<List<DtoCostRuleForEnt>> restResponse = new RestResponse<>();
        restResponse.setData(service.save(costRuleForEnt));
        return restResponse;
    }

    /**
     * 刪除企业费用规则
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "刪除企业费用规则", notes = "刪除企业费用规则")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 刪除企业费用规则
     *
     * @param ids id集合
     * @return
     */
    @ApiOperation(value = "刪除企业费用规则", notes = "刪除企业费用规则")
    @DeleteMapping()
    public RestResponse<String> deleteAll(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setRestStatus(StringUtil.isNull(count) ? ERestStatus.UNMATCH_RECORD:ERestStatus.SUCCESS);
        restResp.setCount(count);

        return restResp;
    }
}