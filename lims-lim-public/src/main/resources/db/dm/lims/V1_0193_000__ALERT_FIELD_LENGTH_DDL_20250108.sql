alter table tb_base_analyzeitem modify "analyzeItemCode" varchar (100 CHAR);
alter table tb_base_analyzeitem modify "analyzeItemName" varchar (100 CHAR);
alter table tb_base_analyzeitem modify "casNum" varchar (100 CHAR);
alter table tb_base_analyzeitem modify "creator" varchar (100 CHAR);
alter table tb_base_analyzeitem modify "fullPinYin" varchar (200 CHAR);
alter table tb_base_analyzeitem modify "modifier" varchar (100 CHAR);
alter table tb_base_analyzeitem modify "pinYin" varchar (100 CHAR);
alter table tb_base_analyzeitem modify "variableName" varchar (100 CHAR);
alter table tb_base_consumable modify "alias" varchar (510 CHAR);
alter table tb_base_consumable modify "codeInStation" varchar (100 CHAR);
alter table tb_base_consumable modify "concentration" varchar (510 CHAR);
alter table tb_base_consumable modify "constantVolumeM" varchar (200 CHAR);
alter table tb_base_consumable modify "consumableCode" varchar (80 CHAR);
alter table tb_base_consumable modify "consumableName" varchar (100 CHAR);
alter table tb_base_consumable modify "creator" varchar (100 CHAR);
alter table tb_base_consumable modify "density" varchar (200 CHAR);
alter table tb_base_consumable modify "dilutedSolution" varchar (510 CHAR);
alter table tb_base_consumable modify "dilutionMethod" varchar (510 CHAR);
alter table tb_base_consumable modify "dimensionName" varchar (100 CHAR);
alter table tb_base_consumable modify "fullPinYin" varchar (200 CHAR);
alter table tb_base_consumable modify "grade" varchar (100 CHAR);
alter table tb_base_consumable modify "keepCondition" varchar (2000 CHAR);
alter table tb_base_consumable modify "modifier" varchar (100 CHAR);
alter table tb_base_consumable modify "molecularFormula" varchar (200 CHAR);
alter table tb_base_consumable modify "molecularWeight" varchar (200 CHAR);
alter table tb_base_consumable modify "nationalStandard" varchar (200 CHAR);
alter table tb_base_consumable modify "pinYin" varchar (100 CHAR);
alter table tb_base_consumable modify "remark" varchar (2000 CHAR);
alter table tb_base_consumable modify "safetyInstruction" varchar (2000 CHAR);
alter table tb_base_consumable modify "specification" varchar (100 CHAR);
alter table tb_base_consumable modify "standard" varchar (100 CHAR);
alter table tb_base_consumable modify "uncertainty" varchar (510 CHAR);
alter table tb_base_consumable modify "unit" varchar (100 CHAR);
alter table tb_base_consumable modify "useWay" varchar (510 CHAR);
alter table tb_base_consumabledetail modify "appearance" varchar (200 CHAR);
alter table tb_base_consumabledetail modify "buyReason" varchar (510 CHAR);
alter table tb_base_consumabledetail modify "checkItem" varchar (200 CHAR);
alter table tb_base_consumabledetail modify "codeInType" varchar (100 CHAR);
alter table tb_base_consumabledetail modify "keepPlace" varchar (200 CHAR);
alter table tb_base_consumabledetail modify "manufacturerName" varchar (100 CHAR);
alter table tb_base_consumabledetail modify "productionCode" varchar (100 CHAR);
alter table tb_base_consumabledetail modify "purchaseNum" varchar (510 CHAR);
alter table tb_base_consumabledetail modify "purchaser" varchar (100 CHAR);
alter table tb_base_consumabledetail modify "remark" varchar (2000 CHAR);
alter table tb_base_consumabledetail modify "storageNum" varchar (510 CHAR);
alter table tb_base_consumabledetail modify "supplierName" varchar (200 CHAR);
alter table tb_base_consumabledetail modify "unitName" varchar (200 CHAR);
alter table tb_base_consumablelog modify "consumingPersonsName" varchar (100 CHAR);
alter table tb_base_consumablelog modify "creator" varchar (100 CHAR);
alter table tb_base_consumablelog modify "modifier" varchar (100 CHAR);
alter table tb_base_consumablelog modify "pickNum" varchar (100 CHAR);
alter table tb_base_consumablelog modify "remark" varchar (2000 CHAR);
alter table tb_base_consumableofmixed modify "concentration" varchar (510 CHAR);
alter table tb_base_consumableofmixed modify "uncertainty" varchar (510 CHAR);
alter table tb_base_dimension modify "code" varchar (100 CHAR);
alter table tb_base_dimension modify "creator" varchar (100 CHAR);
alter table tb_base_dimension modify "dimensionName" varchar (100 CHAR);
alter table tb_base_dimension modify "modifier" varchar (100 CHAR);
alter table tb_base_dimension modify "remark" varchar (2000 CHAR);
alter table tb_base_document modify "creator" varchar (100 CHAR);
alter table tb_base_document modify "docSuffix" varchar (20 CHAR);
alter table tb_base_document modify "docTypeName" varchar (510 CHAR);
alter table tb_base_document modify "filename" varchar (510 CHAR);
alter table tb_base_document modify "folderName" varchar (510 CHAR);
alter table tb_base_document modify "modifier" varchar (100 CHAR);
alter table tb_base_document modify "path" varchar (1000 CHAR);
alter table tb_base_document modify "physicalName" varchar (510 CHAR);
alter table tb_base_document modify "remark" varchar (2000 CHAR);
alter table tb_base_document modify "uploadPerson" varchar (100 CHAR);
alter table tb_base_enterprise modify "acreage" varchar (100 CHAR);
alter table tb_base_enterprise modify "address" varchar (200 CHAR);
alter table tb_base_enterprise modify "aptitude" varchar (510 CHAR);
alter table tb_base_enterprise modify "areaName" varchar (2000 CHAR);
alter table tb_base_enterprise modify "businessScope" varchar (510 CHAR);
alter table tb_base_enterprise modify "cmaCode" varchar (100 CHAR);
alter table tb_base_enterprise modify "code" varchar (40 CHAR);
alter table tb_base_enterprise modify "contactFax" varchar (100 CHAR);
alter table tb_base_enterprise modify "contactMan" varchar (200 CHAR);
alter table tb_base_enterprise modify "contactPhoneNumber" varchar (100 CHAR);
alter table tb_base_enterprise modify "contactTelPhone" varchar (100 CHAR);
alter table tb_base_enterprise modify "corporationCode" varchar (40 CHAR);
alter table tb_base_enterprise modify "corporationName" varchar (100 CHAR);
alter table tb_base_enterprise modify "creator" varchar (100 CHAR);
alter table tb_base_enterprise modify "email" varchar (100 CHAR);
alter table tb_base_enterprise modify "entrustRate" varchar (100 CHAR);
alter table tb_base_enterprise modify "fullPinYin" varchar (200 CHAR);
alter table tb_base_enterprise modify "houseNature" varchar (510 CHAR);
alter table tb_base_enterprise modify "industrialPark" varchar (510 CHAR);
alter table tb_base_enterprise modify "industryKind" varchar (200 CHAR);
alter table tb_base_enterprise modify "latitude" varchar (40 CHAR);
alter table tb_base_enterprise modify "latitudeOther" varchar (40 CHAR);
alter table tb_base_enterprise modify "longitude" varchar (40 CHAR);
alter table tb_base_enterprise modify "longitudeOther" varchar (40 CHAR);
alter table tb_base_enterprise modify "modifier" varchar (100 CHAR);
alter table tb_base_enterprise modify "name" varchar (200 CHAR);
alter table tb_base_enterprise modify "nature" varchar (510 CHAR);
alter table tb_base_enterprise modify "owner" varchar (100 CHAR);
alter table tb_base_enterprise modify "pinYin" varchar (100 CHAR);
alter table tb_base_enterprise modify "regulatePollutionCode" varchar (100 CHAR);
alter table tb_base_enterprise modify "postalCode" varchar (20 CHAR);
alter table tb_base_enterprise modify "productValuePerYear" varchar (100 CHAR);
alter table tb_base_enterprise modify "qualityCertification" varchar (2000 CHAR);
alter table tb_base_enterprise modify "regulateName" varchar (100 CHAR);
alter table tb_base_enterprise modify "remark" varchar (2000 CHAR);
alter table tb_base_enterprise modify "scope" varchar (510 CHAR);
alter table tb_base_enterprise modify "shortName" varchar (200 CHAR);
alter table tb_base_enterprise modify "regulateSocialCode" varchar (100 CHAR);
alter table tb_base_enterprise modify "socialCreditCode" varchar (100 CHAR);
alter table tb_base_enterprise modify "tax" varchar (100 CHAR);
alter table tb_base_enterprise modify "url" varchar (200 CHAR);
alter table tb_base_enterpriseextend modify "breakInfo" varchar (2000 CHAR);
alter table tb_base_enterpriseextend modify "entrustRate" varchar (100 CHAR);
alter table tb_base_enterpriseextend modify "pollutionCode" varchar (100 CHAR);
alter table tb_base_enterpriseextend modify "pollutionSourceType" varchar (600 CHAR);
alter table tb_base_enterpriseextend modify "subRate" varchar (100 CHAR);
alter table tb_base_evaluationanalyzeitem modify "creator" varchar (100 CHAR);
alter table tb_base_evaluationanalyzeitem modify "modifier" varchar (100 CHAR);
alter table tb_base_evaluationanalyzeitem modify "remark" varchar (2000 CHAR);
alter table tb_base_evaluationanalyzeitem modify "symbol" varchar (100 CHAR);
alter table tb_base_evaluationanalyzeitem modify "unit" varchar (100 CHAR);
alter table tb_base_evaluationcriteria modify "applyRange" varchar (2000 CHAR);
alter table tb_base_evaluationcriteria modify "code" varchar (40 CHAR);
alter table tb_base_evaluationcriteria modify "creator" varchar (100 CHAR);
alter table tb_base_evaluationcriteria modify "modifier" varchar (100 CHAR);
alter table tb_base_evaluationcriteria modify "name" varchar (200 CHAR);
alter table tb_base_evaluationcriteria modify "remark" varchar (2000 CHAR);
alter table tb_base_evaluationlevel modify "creator" varchar (100 CHAR);
alter table tb_base_evaluationlevel modify "describion" varchar (2000 CHAR);
alter table tb_base_evaluationlevel modify "modifier" varchar (100 CHAR);
alter table tb_base_evaluationlevel modify "name" varchar (510 CHAR);
alter table tb_base_evaluationvalue modify "creator" varchar (100 CHAR);
alter table tb_base_evaluationvalue modify "lowerLimit" varchar (100 CHAR);
alter table tb_base_evaluationvalue modify "lowerLimitSymble" varchar (100 CHAR);
alter table tb_base_evaluationvalue modify "modifier" varchar (100 CHAR);
alter table tb_base_evaluationvalue modify "remark" varchar (2000 CHAR);
alter table tb_base_evaluationvalue modify "upperLimit" varchar (100 CHAR);
alter table tb_base_evaluationvalue modify "upperLimitSymble" varchar (100 CHAR);
alter table tb_base_folder modify "creator" varchar (100 CHAR);
alter table tb_base_folder modify "folderName" varchar (100 CHAR);
alter table tb_base_folder modify "modifier" varchar (100 CHAR);
alter table tb_base_folder modify "remark" varchar (2000 CHAR);
alter table tb_base_industrytype modify "creator" varchar (100 CHAR);
alter table tb_base_industrytype modify "industryCode" varchar (40 CHAR);
alter table tb_base_industrytype modify "industryName" varchar (100 CHAR);
alter table tb_base_industrytype modify "modifier" varchar (100 CHAR);
alter table tb_base_industrytype modify "remark" varchar (2000 CHAR);
alter table tb_base_instrument modify "authorizedPersons" varchar (2000 CHAR);
alter table tb_base_instrument modify "constractNo" varchar (100 CHAR);
alter table tb_base_instrument modify "controlMeasures" varchar (2000 CHAR);
alter table tb_base_instrument modify "creator" varchar (100 CHAR);
alter table tb_base_instrument modify "deptName" varchar (100 CHAR);
alter table tb_base_instrument modify "factoryName" varchar (200 CHAR);
alter table tb_base_instrument modify "fixedAssetsCode" varchar (40 CHAR);
alter table tb_base_instrument modify "fullPinYin" varchar (200 CHAR);
alter table tb_base_instrument modify "inspectMethod" varchar (2000 CHAR);
alter table tb_base_instrument modify "inspectMode" varchar (100 CHAR);
alter table tb_base_instrument modify "inspectReason" varchar (400 CHAR);
alter table tb_base_instrument modify "insRange" varchar (400 CHAR);
alter table tb_base_instrument modify "instrColor" varchar (200 CHAR);
alter table tb_base_instrument modify "instrumentName" varchar (100 CHAR);
alter table tb_base_instrument modify "instrumentsCode" varchar (100 CHAR);
alter table tb_base_instrument modify "maintenanceContent" varchar (2000 CHAR);
alter table tb_base_instrument modify "manager" varchar (100 CHAR);
alter table tb_base_instrument modify "managerName" varchar (100 CHAR);
alter table tb_base_instrument modify "model" varchar (400 CHAR);
alter table tb_base_instrument modify "modifier" varchar (100 CHAR);
alter table tb_base_instrument modify "nicetyRate" varchar (200 CHAR);
alter table tb_base_instrument modify "originRemark" varchar (2000 CHAR);
alter table tb_base_instrument modify "originUnit" varchar (200 CHAR);
alter table tb_base_instrument modify "photoUrl" varchar (1000 CHAR);
alter table tb_base_instrument modify "pinYin" varchar (100 CHAR);
alter table tb_base_instrument modify "place" varchar (200 CHAR);
alter table tb_base_instrument modify "productCountry" varchar (100 CHAR);
alter table tb_base_instrument modify "regulateName" varchar (100 CHAR);
alter table tb_base_instrument modify "remark" varchar (2000 CHAR);
alter table tb_base_instrument modify "responsibleOffice" varchar (100 CHAR);
alter table tb_base_instrument modify "saleName" varchar (200 CHAR);
alter table tb_base_instrument modify "serialNo" varchar (200 CHAR);
alter table tb_base_instrument modify "useConditions" varchar (2000 CHAR);
alter table tb_base_instrument modify "useMethod" varchar (2000 CHAR);
alter table tb_base_job modify "creator" varchar (100 CHAR);
alter table tb_base_job modify "cronExpression" varchar (510 CHAR);
alter table tb_base_job modify "invokeTarget" varchar (1000 CHAR);
alter table tb_base_job modify "jobGroup" varchar (100 CHAR);
alter table tb_base_job modify "jobName" varchar (100 CHAR);
alter table tb_base_job modify "modifier" varchar (100 CHAR);
alter table tb_base_job modify "remark" varchar (1000 CHAR);
alter table tb_base_jobinfo modify "beginTime" varchar (20 CHAR);
alter table tb_base_jobinfo modify "creator" varchar (100 CHAR);
alter table tb_base_jobinfo modify "endTime" varchar (20 CHAR);
alter table tb_base_jobinfo modify "modifier" varchar (100 CHAR);
alter table tb_base_logforluckysheet modify "comment" varchar (2000 CHAR);
alter table tb_base_logforluckysheet modify "operateInfo" varchar (1000 CHAR);
alter table tb_base_logforluckysheet modify "operatorName" varchar (100 CHAR);
alter table tb_base_perpetualdata modify "type" varchar (510 CHAR);
alter table tb_base_perpetualdata modify "value" varchar (510 CHAR);
alter table tb_base_qualitycontrollimit modify "allowLimit" varchar (100 CHAR);
alter table tb_base_qualitycontrollimit modify "checkItemOther" varchar (100 CHAR);
alter table tb_base_qualitycontrollimit modify "creator" varchar (100 CHAR);
alter table tb_base_qualitycontrollimit modify "description" varchar (510 CHAR);
alter table tb_base_qualitycontrollimit modify "formula" varchar (100 CHAR);
alter table tb_base_qualitycontrollimit modify "modifier" varchar (100 CHAR);
alter table tb_base_qualitycontrollimit modify "qcTypeName" varchar (100 CHAR);
alter table tb_base_qualitycontrollimit modify "rangeConfig" varchar (100 CHAR);
alter table tb_base_qualitycontrollimit modify "substituteName" varchar (100 CHAR);
alter table tb_base_qualitylimitdisposition modify "creator" varchar (100 CHAR);
alter table tb_base_qualitylimitdisposition modify "formula" varchar (510 CHAR);
alter table tb_base_qualitylimitdisposition modify "modifier" varchar (100 CHAR);
alter table tb_base_sampletype modify "creator" varchar (100 CHAR);
alter table tb_base_sampletype modify "icon" varchar (510 CHAR);
alter table tb_base_sampletype modify "modifier" varchar (100 CHAR);
alter table tb_base_sampletype modify "remark" varchar (2000 CHAR);
alter table tb_base_sampletype modify "shortName" varchar (100 CHAR);
alter table tb_base_sampletype modify "typeCode" varchar (100 CHAR);
alter table tb_base_sampletype modify "typeColor" varchar (100 CHAR);
alter table tb_base_sampletype modify "typeName" varchar (100 CHAR);
alter table tb_base_substitute modify "addition" varchar (100 CHAR);
alter table tb_base_substitute modify "casCode" varchar (100 CHAR);
alter table tb_base_substitute modify "compoundName" varchar (200 CHAR);
alter table tb_base_substitute modify "creator" varchar (100 CHAR);
alter table tb_base_substitute modify "dimensionName" varchar (200 CHAR);
alter table tb_base_substitute modify "modifier" varchar (100 CHAR);
alter table tb_base_systemconfig modify "allowFileSuffix" varchar (400 CHAR);
alter table tb_base_systemconfig modify "bank" varchar (100 CHAR);
alter table tb_base_systemconfig modify "bankAccount" varchar (100 CHAR);
alter table tb_base_systemconfig modify "certificate" varchar (100 CHAR);
alter table tb_base_systemconfig modify "companyAddress" varchar (2000 CHAR);
alter table tb_base_systemconfig modify "companyEnglishName" varchar (510 CHAR);
alter table tb_base_systemconfig modify "companyName" varchar (510 CHAR);
alter table tb_base_systemconfig modify "companyPhone" varchar (100 CHAR);
alter table tb_base_systemconfig modify "companyPostCode" varchar (40 CHAR);
alter table tb_base_systemconfig modify "creator" varchar (100 CHAR);
alter table tb_base_systemconfig modify "fax" varchar (510 CHAR);
alter table tb_base_systemconfig modify "fullName" varchar (200 CHAR);
alter table tb_base_systemconfig modify "labArea" varchar (100 CHAR);
alter table tb_base_systemconfig modify "latitude" varchar (100 CHAR);
alter table tb_base_systemconfig modify "legalRepresentative" varchar (100 CHAR);
alter table tb_base_systemconfig modify "linkMan" varchar (100 CHAR);
alter table tb_base_systemconfig modify "longitude" varchar (100 CHAR);
alter table tb_base_systemconfig modify "modifier" varchar (100 CHAR);
alter table tb_base_systemconfig modify "registeredCapital" varchar (100 CHAR);
alter table tb_base_systemconfig modify "shortName" varchar (100 CHAR);
alter table tb_base_systemconfig modify "taxNumber" varchar (100 CHAR);
alter table tb_base_systemconfig modify "welcomeWord" varchar (510 CHAR);
alter table tb_lim_analyzeitemrelation modify "analyzeItemName" varchar (100 CHAR);
alter table tb_lim_analyzeitemrelation modify "creator" varchar (100 CHAR);
alter table tb_lim_analyzeitemrelation modify "formula" varchar (1000 CHAR);
alter table tb_lim_analyzeitemrelation modify "modifier" varchar (100 CHAR);
alter table tb_lim_analyzeitemrelationparams modify "analyzeItemName" varchar (100 CHAR);
alter table tb_lim_analyzeitemrelationparams modify "creator" varchar (100 CHAR);
alter table tb_lim_analyzeitemrelationparams modify "modifier" varchar (100 CHAR);
alter table tb_lim_analyzeitemsort modify "creator" varchar (100 CHAR);
alter table tb_lim_analyzeitemsort modify "modifier" varchar (100 CHAR);
alter table tb_lim_analyzeitemsort modify "remark" varchar (510 CHAR);
alter table tb_lim_analyzeitemsort modify "sortCode" varchar (500 CHAR);
alter table tb_lim_analyzeitemsort modify "sortName" varchar (500 CHAR);
alter table tb_lim_analyzeitemsortdetail modify "analyzeItemName" varchar (100 CHAR);
alter table tb_lim_analyzemethod modify "alias" varchar (100 CHAR);
alter table tb_lim_analyzemethod modify "countryStandard" varchar (400 CHAR);
alter table tb_lim_analyzemethod modify "countryStandardName" varchar (200 CHAR);
alter table tb_lim_analyzemethod modify "creator" varchar (100 CHAR);
alter table tb_lim_analyzemethod modify "methodCode" varchar (100 CHAR);
alter table tb_lim_analyzemethod modify "methodName" varchar (510 CHAR);
alter table tb_lim_analyzemethod modify "modifier" varchar (100 CHAR);
alter table tb_lim_analyzemethod modify "preparedMethod" varchar (400 CHAR);
alter table tb_lim_analyzemethod modify "remark" varchar (2000 CHAR);
alter table tb_lim_analyzemethod modify "yearSn" varchar (100 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "concentration" varchar (400 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "configurationSolution" varchar (1000 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "constantVolume" varchar (400 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "context" varchar (2000 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "course" varchar (2000 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "diluent" varchar (400 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "opinion" varchar (2000 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "reagentName" varchar (200 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "reagentSpecification" varchar (1000 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "reagentVolumeQuality" varchar (400 CHAR);
alter table tb_lim_analyzemethodreagentconfig modify "suitItem" varchar (400 CHAR);
alter table tb_lim_appconfig modify "code" varchar (100 CHAR);
alter table tb_lim_appconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_appconfig modify "linkAddress" varchar (2000 CHAR);
alter table tb_lim_appconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_appconfig modify "name" varchar (200 CHAR);
alter table tb_lim_appconfig modify "remark" varchar (2000 CHAR);
alter table tb_lim_appconfig modify "type" varchar (100 CHAR);
alter table tb_lim_calendardate modify "creator" varchar (100 CHAR);
alter table tb_lim_calendardate modify "holidayName" varchar (100 CHAR);
alter table tb_lim_calendardate modify "modifier" varchar (100 CHAR);
alter table tb_lim_carconsumerrecord modify "content" varchar (2000 CHAR);
alter table tb_lim_carconsumerrecord modify "creator" varchar (100 CHAR);
alter table tb_lim_carconsumerrecord modify "modifier" varchar (100 CHAR);
alter table tb_lim_carconsumerrecord modify "type" varchar (100 CHAR);
alter table tb_lim_cargpsconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_cargpsconfig modify "gpsCode" varchar (100 CHAR);
alter table tb_lim_cargpsconfig modify "gpsFrequency" varchar (40 CHAR);
alter table tb_lim_cargpsconfig modify "gpsModel" varchar (100 CHAR);
alter table tb_lim_cargpsconfig modify "gpsPwd" varchar (100 CHAR);
alter table tb_lim_cargpsconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_cargpsconfig modify "rateLimited" varchar (100 CHAR);
alter table tb_lim_cargpsconfig modify "simNumber" varchar (100 CHAR);
alter table tb_lim_cargpsdata modify "carSn" varchar (100 CHAR);
alter table tb_lim_cargpsdata modify "userName" varchar (100 CHAR);
alter table tb_lim_cargpsrealdata modify "direction" varchar (100 CHAR);
alter table tb_lim_cargpsrealdata modify "lat" varchar (100 CHAR);
alter table tb_lim_cargpsrealdata modify "lon" varchar (100 CHAR);
alter table tb_lim_cargpsrealdata modify "operator" varchar (100 CHAR);
alter table tb_lim_cargpsrealdata modify "precision" varchar (100 CHAR);
alter table tb_lim_cargpsrealdata modify "sigWatch" varchar (100 CHAR);
alter table tb_lim_cargpsrealdata modify "speed" varchar (100 CHAR);
alter table tb_lim_carmanage modify "carBrand" varchar (100 CHAR);
alter table tb_lim_carmanage modify "carCode" varchar (100 CHAR);
alter table tb_lim_carmanage modify "carColor" varchar (100 CHAR);
alter table tb_lim_carmanage modify "carModel" varchar (100 CHAR);
alter table tb_lim_carmanage modify "carType" varchar (100 CHAR);
alter table tb_lim_carmanage modify "creator" varchar (100 CHAR);
alter table tb_lim_carmanage modify "engineCode" varchar (100 CHAR);
alter table tb_lim_carmanage modify "modifier" varchar (100 CHAR);
alter table tb_lim_carmanage modify "oilConsumption" varchar (100 CHAR);
alter table tb_lim_carmanage modify "remark" varchar (2000 CHAR);
alter table tb_lim_certhistoryinfo modify "creator" varchar (100 CHAR);
alter table tb_lim_certhistoryinfo modify "modifier" varchar (100 CHAR);
alter table tb_lim_comparejudge modify "creator" varchar (100 CHAR);
alter table tb_lim_comparejudge modify "modifier" varchar (100 CHAR);
alter table tb_lim_consumablestorage modify "appearance" varchar (200 CHAR);
alter table tb_lim_consumablestorage modify "buyReason" varchar (2000 CHAR);
alter table tb_lim_consumablestorage modify "checkerName" varchar (100 CHAR);
alter table tb_lim_consumablestorage modify "checkItem" varchar (200 CHAR);
alter table tb_lim_consumablestorage modify "creator" varchar (100 CHAR);
alter table tb_lim_consumablestorage modify "dimensionName" varchar (200 CHAR);
alter table tb_lim_consumablestorage modify "keepPlace" varchar (200 CHAR);
alter table tb_lim_consumablestorage modify "manufacturerName" varchar (200 CHAR);
alter table tb_lim_consumablestorage modify "modifier" varchar (100 CHAR);
alter table tb_lim_consumablestorage modify "operatorName" varchar (100 CHAR);
alter table tb_lim_consumablestorage modify "productionCode" varchar (40 CHAR);
alter table tb_lim_consumablestorage modify "remark" varchar (2000 CHAR);
alter table tb_lim_consumablestorage modify "supplyCompanyName" varchar (200 CHAR);
alter table tb_lim_contract modify "address" varchar (200 CHAR);
alter table tb_lim_contract modify "attentions" varchar (510 CHAR);
alter table tb_lim_contract modify "contractCode" varchar (100 CHAR);
alter table tb_lim_contract modify "contractName" varchar (510 CHAR);
alter table tb_lim_contract modify "creator" varchar (100 CHAR);
alter table tb_lim_contract modify "entName" varchar (100 CHAR);
alter table tb_lim_contract modify "explains" varchar (510 CHAR);
alter table tb_lim_contract modify "linkMan" varchar (100 CHAR);
alter table tb_lim_contract modify "linkPhone" varchar (100 CHAR);
alter table tb_lim_contract modify "modifier" varchar (100 CHAR);
alter table tb_lim_contract modify "period" varchar (100 CHAR);
alter table tb_lim_contract modify "remark" varchar (2000 CHAR);
alter table tb_lim_contract modify "salesManName" varchar (100 CHAR);
alter table tb_lim_contract modify "type" varchar (100 CHAR);
alter table tb_lim_contractcollectionplan modify "collectItem" varchar (510 CHAR);
alter table tb_lim_contractcollectionplan modify "creator" varchar (100 CHAR);
alter table tb_lim_contractcollectionplan modify "modifier" varchar (100 CHAR);
alter table tb_lim_contractcollectionplan modify "remark" varchar (2000 CHAR);
alter table tb_lim_cost modify "creator" varchar (100 CHAR);
alter table tb_lim_cost modify "modifier" varchar (100 CHAR);
alter table tb_lim_cost modify "redAnalyzeItemName" varchar (100 CHAR);
alter table tb_lim_cost modify "redAnalyzeMethodName" varchar (510 CHAR);
alter table tb_lim_cost modify "redCountryStandard" varchar (100 CHAR);
alter table tb_lim_costrule modify "creator" varchar (100 CHAR);
alter table tb_lim_costrule modify "modifier" varchar (100 CHAR);
alter table tb_lim_costruleforent modify "creator" varchar (100 CHAR);
alter table tb_lim_costruleforent modify "modifier" varchar (100 CHAR);
alter table tb_lim_curve modify "blankOne" varchar (100 CHAR);
alter table tb_lim_curve modify "blankTwo" varchar (100 CHAR);
alter table tb_lim_curve modify "bRange" varchar (100 CHAR);
alter table tb_lim_curve modify "bValue" varchar (100 CHAR);
alter table tb_lim_curve modify "coefficient" varchar (100 CHAR);
alter table tb_lim_curve modify "coefficientRange" varchar (100 CHAR);
alter table tb_lim_curve modify "configName" varchar (100 CHAR);
alter table tb_lim_curve modify "creator" varchar (100 CHAR);
alter table tb_lim_curve modify "curveInfo" varchar (200 CHAR);
alter table tb_lim_curve modify "cValue" varchar (100 CHAR);
alter table tb_lim_curve modify "doubleName" varchar (100 CHAR);
alter table tb_lim_curve modify "kRange" varchar (100 CHAR);
alter table tb_lim_curve modify "kValue" varchar (100 CHAR);
alter table tb_lim_curve modify "modifier" varchar (100 CHAR);
alter table tb_lim_curve modify "zeroPoint" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "aValueBG" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "blank220" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "blank275" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "blankAbsorbance" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "code" varchar (300 CHAR);
alter table tb_lim_curvedetail modify "colorAbsorbance" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "hValue" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "interfereAbsorbance" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "lessBlank" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "remark" varchar (2000 CHAR);
alter table tb_lim_curvedetail modify "vValue" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "xValue" varchar (100 CHAR);
alter table tb_lim_curvedetail modify "yValue" varchar (100 CHAR);
alter table tb_lim_customerviolate modify "creator" varchar (100 CHAR);
alter table tb_lim_customerviolate modify "handleWay" varchar (4000 CHAR);
alter table tb_lim_customerviolate modify "modifier" varchar (100 CHAR);
alter table tb_lim_customerviolate modify "violateContent" varchar (4000 CHAR);
alter table tb_lim_docauthority modify "authName" varchar (500 CHAR);
alter table tb_lim_docauthority modify "roleName" varchar (500 CHAR);
alter table tb_lim_docauthorityconfig modify "authCode" varchar (100 CHAR);
alter table tb_lim_docauthorityconfig modify "authName" varchar (500 CHAR);
alter table tb_lim_docauthoritylist modify "authCode" varchar (100 CHAR);
alter table tb_lim_docauthoritylist modify "authName" varchar (500 CHAR);
alter table tb_lim_entevaluation modify "auditOpinion" varchar (2000 CHAR);
alter table tb_lim_entevaluation modify "content" varchar (2000 CHAR);
alter table tb_lim_entevaluation modify "creator" varchar (100 CHAR);
alter table tb_lim_entevaluation modify "modifier" varchar (100 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "auditChecker" varchar (100 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "comment" varchar (2000 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "creator" varchar (100 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "info" varchar (2000 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "modifier" varchar (100 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "remark" varchar (2000 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "serviceContent" varchar (2000 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "shortestDeliveryTime" varchar (20 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "trialComment" varchar (2000 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "trialPerson" varchar (100 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "trialResult" varchar (2000 CHAR);
alter table tb_lim_entsuppliergoodsevaluation modify "trialTimeLen" varchar (510 CHAR);
alter table tb_lim_entsupplierservice modify "creator" varchar (100 CHAR);
alter table tb_lim_entsupplierservice modify "goodsCode" varchar (40 CHAR);
alter table tb_lim_entsupplierservice modify "goodsModel" varchar (100 CHAR);
alter table tb_lim_entsupplierservice modify "goodsName" varchar (200 CHAR);
alter table tb_lim_entsupplierservice modify "goodsType" varchar (200 CHAR);
alter table tb_lim_entsupplierservice modify "modifier" varchar (100 CHAR);
alter table tb_lim_entsupplierservice modify "remark" varchar (2000 CHAR);
alter table tb_lim_environmental modify "creator" varchar (100 CHAR);
alter table tb_lim_environmental modify "highestHumidity" varchar (100 CHAR);
alter table tb_lim_environmental modify "highestPressure" varchar (100 CHAR);
alter table tb_lim_environmental modify "highestTemperature" varchar (100 CHAR);
alter table tb_lim_environmental modify "labCode" varchar (100 CHAR);
alter table tb_lim_environmental modify "labName" varchar (100 CHAR);
alter table tb_lim_environmental modify "lowestHumidity" varchar (100 CHAR);
alter table tb_lim_environmental modify "lowestPressure" varchar (100 CHAR);
alter table tb_lim_environmental modify "lowestTemperature" varchar (100 CHAR);
alter table tb_lim_environmental modify "modifier" varchar (100 CHAR);
alter table tb_lim_environmental modify "personInCharge" varchar (100 CHAR);
alter table tb_lim_environmentallog modify "creator" varchar (100 CHAR);
alter table tb_lim_environmentallog modify "humidity" varchar (100 CHAR);
alter table tb_lim_environmentallog modify "modifier" varchar (100 CHAR);
alter table tb_lim_environmentallog modify "pressure" varchar (100 CHAR);
alter table tb_lim_environmentallog modify "temperature" varchar (100 CHAR);
alter table tb_lim_environmentalrecord modify "creator" varchar (100 CHAR);
alter table tb_lim_environmentalrecord modify "humidity" varchar (100 CHAR);
alter table tb_lim_environmentalrecord modify "modifier" varchar (100 CHAR);
alter table tb_lim_environmentalrecord modify "pressure" varchar (100 CHAR);
alter table tb_lim_environmentalrecord modify "remark" varchar (2000 CHAR);
alter table tb_lim_environmentalrecord modify "temperature" varchar (100 CHAR);
alter table tb_lim_examine modify "addPersonName" varchar (100 CHAR);
alter table tb_lim_examine modify "auditOpinion" varchar (2000 CHAR);
alter table tb_lim_examine modify "creator" varchar (100 CHAR);
alter table tb_lim_examine modify "deptName" varchar (100 CHAR);
alter table tb_lim_examine modify "inspectedPerson" varchar (100 CHAR);
alter table tb_lim_examine modify "modifier" varchar (100 CHAR);
alter table tb_lim_examine modify "title" varchar (510 CHAR);
alter table tb_lim_examinetype modify "content" varchar (510 CHAR);
alter table tb_lim_examinetype modify "creator" varchar (100 CHAR);
alter table tb_lim_examinetype modify "inspectedPerson" varchar (100 CHAR);
alter table tb_lim_examinetype modify "modifier" varchar (100 CHAR);
alter table tb_lim_examinetype modify "title" varchar (510 CHAR);
alter table tb_lim_examinetyperecord modify "content" varchar (2000 CHAR);
alter table tb_lim_examinetyperecord modify "creator" varchar (100 CHAR);
alter table tb_lim_examinetyperecord modify "modifier" varchar (100 CHAR);
alter table tb_lim_feeconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_feeconfig modify "formula" varchar (2000 CHAR);
alter table tb_lim_feeconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_feeconfig modify "remark" varchar (510 CHAR);
alter table tb_lim_feeconfig modify "typeName" varchar (100 CHAR);
alter table tb_lim_feeconfig modify "unit" varchar (100 CHAR);
alter table tb_lim_filecontrolapply modify "abolishReason" varchar (2000 CHAR);
alter table tb_lim_filecontrolapply modify "applyDesc" varchar (2000 CHAR);
alter table tb_lim_filecontrolapply modify "controlCode" varchar (200 CHAR);
alter table tb_lim_filecontrolapply modify "creator" varchar (100 CHAR);
alter table tb_lim_filecontrolapply modify "modifier" varchar (100 CHAR);
alter table tb_lim_filecontrolapply modify "reviseContent" varchar (2000 CHAR);
alter table tb_lim_filecontrolapply modify "status" varchar (100 CHAR);
alter table tb_lim_filecontrolapplydetail modify "controlCode" varchar (200 CHAR);
alter table tb_lim_filecontrolapplydetail modify "creator" varchar (100 CHAR);
alter table tb_lim_filecontrolapplydetail modify "fileCode" varchar (200 CHAR);
alter table tb_lim_filecontrolapplydetail modify "fileName" varchar (200 CHAR);
alter table tb_lim_filecontrolapplydetail modify "fileType" varchar (100 CHAR);
alter table tb_lim_filecontrolapplydetail modify "maker" varchar (100 CHAR);
alter table tb_lim_filecontrolapplydetail modify "modifier" varchar (100 CHAR);
alter table tb_lim_filecontrolapplydetail modify "version" varchar (100 CHAR);
alter table tb_lim_filegrantrecovery modify "code" varchar (510 CHAR);
alter table tb_lim_filegrantrecovery modify "creator" varchar (100 CHAR);
alter table tb_lim_filegrantrecovery modify "modifier" varchar (100 CHAR);
alter table tb_lim_filegrantrecovery modify "remark" varchar (2000 CHAR);
alter table tb_lim_fixedproperty modify "assetsName" varchar (100 CHAR);
alter table tb_lim_fixedproperty modify "assetsNo" varchar (22 CHAR);
alter table tb_lim_fixedproperty modify "assetsType" varchar (100 CHAR);
alter table tb_lim_fixedproperty modify "brandModel" varchar (100 CHAR);
alter table tb_lim_fixedproperty modify "creator" varchar (100 CHAR);
alter table tb_lim_fixedproperty modify "manager" varchar (100 CHAR);
alter table tb_lim_fixedproperty modify "modifier" varchar (100 CHAR);
alter table tb_lim_fixedproperty modify "supplier" varchar (100 CHAR);
alter table tb_lim_holidayconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_holidayconfig modify "holidayName" varchar (100 CHAR);
alter table tb_lim_holidayconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_instrumentcheckrecord modify "calibration" varchar (2000 CHAR);
alter table tb_lim_instrumentcheckrecord modify "certiCode" varchar (200 CHAR);
alter table tb_lim_instrumentcheckrecord modify "checkContent" varchar (2000 CHAR);
alter table tb_lim_instrumentcheckrecord modify "checkDeptName" varchar (200 CHAR);
alter table tb_lim_instrumentcheckrecord modify "checkPerson" varchar (100 CHAR);
alter table tb_lim_instrumentcheckrecord modify "checkWay" varchar (100 CHAR);
alter table tb_lim_instrumentcheckrecord modify "creator" varchar (100 CHAR);
alter table tb_lim_instrumentcheckrecord modify "deviation" varchar (2000 CHAR);
alter table tb_lim_instrumentcheckrecord modify "indicate" varchar (2000 CHAR);
alter table tb_lim_instrumentcheckrecord modify "modifier" varchar (100 CHAR);
alter table tb_lim_instrumentgather modify "creator" varchar (100 CHAR);
alter table tb_lim_instrumentgather modify "mnNumber" varchar (100 CHAR);
alter table tb_lim_instrumentgather modify "modifier" varchar (100 CHAR);
alter table tb_lim_instrumentgatherdata modify "dataType" varchar (20 CHAR);
alter table tb_lim_instrumentgatherdatadetails modify "dimension" varchar (40 CHAR);
alter table tb_lim_instrumentgatherdatadetails modify "paramValue" varchar (510 CHAR);
alter table tb_lim_instrumentgatherdatadetails modify "parmaName" varchar (200 CHAR);
alter table tb_lim_instrumentgatherparams modify "creator" varchar (100 CHAR);
alter table tb_lim_instrumentgatherparams modify "dataType" varchar (20 CHAR);
alter table tb_lim_instrumentgatherparams modify "dimension" varchar (40 CHAR);
alter table tb_lim_instrumentgatherparams modify "enumDataSource" varchar (510 CHAR);
alter table tb_lim_instrumentgatherparams modify "modifier" varchar (100 CHAR);
alter table tb_lim_instrumentgatherparams modify "paramLabel" varchar (200 CHAR);
alter table tb_lim_instrumentgatherparams modify "paramName" varchar (200 CHAR);
alter table tb_lim_instrumentgatherparams modify "remark" varchar (510 CHAR);
alter table tb_lim_instrumentinspect modify "creator" varchar (100 CHAR);
alter table tb_lim_instrumentinspect modify "inspectContent" varchar (2000 CHAR);
alter table tb_lim_instrumentinspect modify "inspectPerson" varchar (100 CHAR);
alter table tb_lim_instrumentinspect modify "modifier" varchar (100 CHAR);
alter table tb_lim_instrumentinspect modify "remark" varchar (2000 CHAR);
alter table tb_lim_instrumentmaintainrecord modify "creator" varchar (100 CHAR);
alter table tb_lim_instrumentmaintainrecord modify "humidity" varchar (100 CHAR);
alter table tb_lim_instrumentmaintainrecord modify "maintainContent" varchar (2000 CHAR);
alter table tb_lim_instrumentmaintainrecord modify "maintainDeptName" varchar (200 CHAR);
alter table tb_lim_instrumentmaintainrecord modify "maintainPerson" varchar (100 CHAR);
alter table tb_lim_instrumentmaintainrecord modify "maintainremark" varchar (2000 CHAR);
alter table tb_lim_instrumentmaintainrecord modify "maintainRule" varchar (2000 CHAR);
alter table tb_lim_instrumentmaintainrecord modify "modifier" varchar (100 CHAR);
alter table tb_lim_instrumentmaintainrecord modify "temperature" varchar (100 CHAR);
alter table tb_lim_instrumentrepairrecord modify "checkContent" varchar (2000 CHAR);
alter table tb_lim_instrumentrepairrecord modify "creator" varchar (100 CHAR);
alter table tb_lim_instrumentrepairrecord modify "failureDesc" varchar (2000 CHAR);
alter table tb_lim_instrumentrepairrecord modify "failureDescPerson" varchar (100 CHAR);
alter table tb_lim_instrumentrepairrecord modify "modifier" varchar (100 CHAR);
alter table tb_lim_instrumentrepairrecord modify "remark" varchar (2000 CHAR);
alter table tb_lim_instrumentrepairrecord modify "repairContent" varchar (2000 CHAR);
alter table tb_lim_instrumentrepairrecord modify "repairContentRecorder" varchar (100 CHAR);
alter table tb_lim_instrumentrepairrecord modify "repairResultChecker" varchar (100 CHAR);
alter table tb_lim_instrumentrepairrecord modify "requestNoteCode" varchar (40 CHAR);
alter table tb_lim_instrumentstorage modify "creator" varchar (100 CHAR);
alter table tb_lim_instrumentstorage modify "factoryName" varchar (200 CHAR);
alter table tb_lim_instrumentstorage modify "instrumentName" varchar (510 CHAR);
alter table tb_lim_instrumentstorage modify "instrumentsCode" varchar (40 CHAR);
alter table tb_lim_instrumentstorage modify "model" varchar (510 CHAR);
alter table tb_lim_instrumentstorage modify "modifier" varchar (100 CHAR);
alter table tb_lim_instrumentstorage modify "operator" varchar (100 CHAR);
alter table tb_lim_instrumentstorage modify "operatorName" varchar (100 CHAR);
alter table tb_lim_instrumentstorage modify "serialNo" varchar (40 CHAR);
alter table tb_lim_instrumentstorage modify "supplyCompanyName" varchar (200 CHAR);
alter table tb_lim_instrumentuserecord modify "beforeAfterSituation" varchar (510 CHAR);
alter table tb_lim_instrumentuserecord modify "beforeUseSituation" varchar (510 CHAR);
alter table tb_lim_instrumentuserecord modify "creator" varchar (100 CHAR);
alter table tb_lim_instrumentuserecord modify "humidity" varchar (100 CHAR);
alter table tb_lim_instrumentuserecord modify "modifier" varchar (100 CHAR);
alter table tb_lim_instrumentuserecord modify "pressure" varchar (100 CHAR);
alter table tb_lim_instrumentuserecord modify "remark" varchar (2000 CHAR);
alter table tb_lim_instrumentuserecord modify "temperature" varchar (100 CHAR);
alter table tb_lim_instrumentuserecord modify "testIds" varchar (2000 CHAR);
alter table tb_lim_itemrelation modify "creator" varchar (100 CHAR);
alter table tb_lim_itemrelation modify "formula" varchar (1000 CHAR);
alter table tb_lim_itemrelation modify "leftFormula" varchar (1000 CHAR);
alter table tb_lim_itemrelation modify "modifier" varchar (100 CHAR);
alter table tb_lim_itemrelation modify "rightFormula" varchar (1000 CHAR);
alter table tb_lim_itemrelationparams modify "analyzeItemName" varchar (100 CHAR);
alter table tb_lim_itemrelationparams modify "creator" varchar (100 CHAR);
alter table tb_lim_itemrelationparams modify "modifier" varchar (100 CHAR);
alter table tb_lim_messagesendrecord modify "messageType" varchar (200 CHAR);
alter table tb_lim_messagesendrecord modify "receiver" varchar (100 CHAR);
alter table tb_lim_newsearchplan modify "creator" varchar (100 CHAR);
alter table tb_lim_newsearchplan modify "executor" varchar (100 CHAR);
alter table tb_lim_newsearchplan modify "modifier" varchar (100 CHAR);
alter table tb_lim_newsearchplan modify "planName" varchar (200 CHAR);
alter table tb_lim_newsearchplan modify "remark" varchar (510 CHAR);
alter table tb_lim_newsearchresult modify "creator" varchar (100 CHAR);
alter table tb_lim_newsearchresult modify "modifier" varchar (100 CHAR);
alter table tb_lim_newsearchresult modify "replaceNum" varchar (100 CHAR);
alter table tb_lim_newsearchresult modify "standardName" varchar (200 CHAR);
alter table tb_lim_newsearchresult modify "standardNum" varchar (100 CHAR);
alter table tb_lim_newsearchresult modify "year" varchar (100 CHAR);
alter table tb_lim_newsearchtask modify "creator" varchar (100 CHAR);
alter table tb_lim_newsearchtask modify "executor" varchar (100 CHAR);
alter table tb_lim_newsearchtask modify "modifier" varchar (100 CHAR);
alter table tb_lim_newsearchtask modify "taskName" varchar (200 CHAR);
alter table tb_lim_notice modify "category" varchar (100 CHAR);
alter table tb_lim_notice modify "creator" varchar (100 CHAR);
alter table tb_lim_notice modify "label" varchar (2000 CHAR);
alter table tb_lim_notice modify "modifier" varchar (100 CHAR);
alter table tb_lim_notice modify "releaseMan" varchar (510 CHAR);
alter table tb_lim_notice modify "status" varchar (100 CHAR);
alter table tb_lim_notice modify "title" varchar (510 CHAR);
alter table tb_lim_noticemsg modify "creator" varchar (100 CHAR);
alter table tb_lim_noticemsg modify "messagePerson" varchar (100 CHAR);
alter table tb_lim_noticemsg modify "modifier" varchar (100 CHAR);
alter table tb_lim_oaconsumablepicklistsdetail modify "codeInStation" varchar (200 CHAR);
alter table tb_lim_oaconsumablepicklistsdetail modify "consumableName" varchar (510 CHAR);
alter table tb_lim_oaconsumablepicklistsdetail modify "creator" varchar (100 CHAR);
alter table tb_lim_oaconsumablepicklistsdetail modify "gradeName" varchar (200 CHAR);
alter table tb_lim_oaconsumablepicklistsdetail modify "materialUnit" varchar (200 CHAR);
alter table tb_lim_oaconsumablepicklistsdetail modify "materialUse" varchar (200 CHAR);
alter table tb_lim_oaconsumablepicklistsdetail modify "modifier" varchar (100 CHAR);
alter table tb_lim_oaconsumablepicklistsdetail modify "remark" varchar (1000 CHAR);
alter table tb_lim_oaconsumablepicklistsdetail modify "specification" varchar (510 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "articleNo" varchar (510 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "brand" varchar (510 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "codeInStation" varchar (100 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "concentration" varchar (510 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "consumableCode" varchar (40 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "consumableName" varchar (100 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "creator" varchar (100 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "dilutedSolution" varchar (510 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "dilutionMethod" varchar (510 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "dimensionName" varchar (100 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "keepCondition" varchar (2000 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "materialModel" varchar (100 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "modifier" varchar (100 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "purpose" varchar (200 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "remark" varchar (2000 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "safetyInstruction" varchar (2000 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "skillRequire" varchar (510 CHAR);
alter table tb_lim_oaconsumablepurchasedetail modify "uncertainty" varchar (510 CHAR);
alter table tb_lim_oacontract modify "address" varchar (200 CHAR);
alter table tb_lim_oacontract modify "attentions" varchar (510 CHAR);
alter table tb_lim_oacontract modify "contractCode" varchar (100 CHAR);
alter table tb_lim_oacontract modify "contractName" varchar (510 CHAR);
alter table tb_lim_oacontract modify "creator" varchar (100 CHAR);
alter table tb_lim_oacontract modify "entName" varchar (100 CHAR);
alter table tb_lim_oacontract modify "explains" varchar (510 CHAR);
alter table tb_lim_oacontract modify "linkMan" varchar (100 CHAR);
alter table tb_lim_oacontract modify "linkPhone" varchar (100 CHAR);
alter table tb_lim_oacontract modify "modifier" varchar (100 CHAR);
alter table tb_lim_oacontract modify "period" varchar (100 CHAR);
alter table tb_lim_oacontract modify "remark" varchar (2000 CHAR);
alter table tb_lim_oacontract modify "salesManName" varchar (100 CHAR);
alter table tb_lim_oacontract modify "type" varchar (100 CHAR);
alter table tb_lim_oafileabolish modify "abolishReason" varchar (2000 CHAR);
alter table tb_lim_oafileabolish modify "creator" varchar (100 CHAR);
alter table tb_lim_oafileabolish modify "fileName" varchar (200 CHAR);
alter table tb_lim_oafileabolish modify "modifier" varchar (100 CHAR);
alter table tb_lim_oafilecontrol modify "controlCode" varchar (200 CHAR);
alter table tb_lim_oafilecontrol modify "creator" varchar (100 CHAR);
alter table tb_lim_oafilecontrol modify "fileCode" varchar (200 CHAR);
alter table tb_lim_oafilecontrol modify "fileName" varchar (200 CHAR);
alter table tb_lim_oafilecontrol modify "maker" varchar (100 CHAR);
alter table tb_lim_oafilecontrol modify "makerName" varchar (100 CHAR);
alter table tb_lim_oafilecontrol modify "modifier" varchar (100 CHAR);
alter table tb_lim_oafilecontrol modify "version" varchar (100 CHAR);
alter table tb_lim_oafilerevision modify "controlCode" varchar (200 CHAR);
alter table tb_lim_oafilerevision modify "creator" varchar (100 CHAR);
alter table tb_lim_oafilerevision modify "fileCode" varchar (200 CHAR);
alter table tb_lim_oafilerevision modify "fileName" varchar (200 CHAR);
alter table tb_lim_oafilerevision modify "makerName" varchar (100 CHAR);
alter table tb_lim_oafilerevision modify "modifier" varchar (100 CHAR);
alter table tb_lim_oafilerevision modify "reviseContent" varchar (2000 CHAR);
alter table tb_lim_oafilerevision modify "sourceControlCode" varchar (200 CHAR);
alter table tb_lim_oafilerevision modify "sourceVersion" varchar (100 CHAR);
alter table tb_lim_oafilerevision modify "version" varchar (100 CHAR);
alter table tb_lim_oainstrumentpurchasedetail modify "creator" varchar (100 CHAR);
alter table tb_lim_oainstrumentpurchasedetail modify "instrumentName" varchar (100 CHAR);
alter table tb_lim_oainstrumentpurchasedetail modify "materialModel" varchar (100 CHAR);
alter table tb_lim_oainstrumentpurchasedetail modify "modifier" varchar (100 CHAR);
alter table tb_lim_oainstrumentpurchasedetail modify "purpose" varchar (200 CHAR);
alter table tb_lim_oainstrumentpurchasedetail modify "remark" varchar (2000 CHAR);
alter table tb_lim_oainstrumentpurchasedetail modify "skillRequire" varchar (510 CHAR);
alter table tb_lim_oainstrumentrepairapply modify "creator" varchar (100 CHAR);
alter table tb_lim_oainstrumentrepairapply modify "failureDesc" varchar (2000 CHAR);
alter table tb_lim_oainstrumentrepairapply modify "modifier" varchar (100 CHAR);
alter table tb_lim_oainstrumentrepairapply modify "remark" varchar (2000 CHAR);
alter table tb_lim_oainstrumentscrap modify "creator" varchar (100 CHAR);
alter table tb_lim_oainstrumentscrap modify "modifier" varchar (100 CHAR);
alter table tb_lim_oainstrumentscrap modify "remark" varchar (2000 CHAR);
alter table tb_lim_oainstrumentscrap modify "scrapDesc" varchar (2000 CHAR);
alter table tb_lim_ocrconfig modify "configName" varchar (510 CHAR);
alter table tb_lim_ocrconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_ocrconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_ocrconfigparam modify "dimension" varchar (100 CHAR);
alter table tb_lim_ocrconfigparam modify "paramName" varchar (510 CHAR);
alter table tb_lim_ocrconfigparam modify "paramNameAlias" varchar (510 CHAR);
alter table tb_lim_ocrconfigparam modify "regexBegin" varchar (510 CHAR);
alter table tb_lim_ocrconfigparam modify "regexEnd" varchar (510 CHAR);
alter table tb_lim_ocrconfigparamdata modify "creator" varchar (100 CHAR);
alter table tb_lim_ocrconfigparamdata modify "modifier" varchar (100 CHAR);
alter table tb_lim_ocrconfigparamdata modify "saveValue" varchar (510 CHAR);
alter table tb_lim_ocrconfigrecord modify "creator" varchar (100 CHAR);
alter table tb_lim_ocrconfigrecord modify "filePath" varchar (1000 CHAR);
alter table tb_lim_ocrconfigrecord modify "groupName" varchar (510 CHAR);
alter table tb_lim_ocrconfigrecord modify "modifier" varchar (100 CHAR);
alter table tb_lim_ocrconfigrecord modify "sampleCode" varchar (100 CHAR);
alter table tb_lim_otherexpenditure modify "creator" varchar (100 CHAR);
alter table tb_lim_otherexpenditure modify "entName" varchar (2000 CHAR);
alter table tb_lim_otherexpenditure modify "explain" varchar (2000 CHAR);
alter table tb_lim_otherexpenditure modify "modifier" varchar (100 CHAR);
alter table tb_lim_otherexpenditure modify "operator" varchar (100 CHAR);
alter table tb_lim_otherexpenditure modify "paytype" varchar (100 CHAR);
alter table tb_lim_otherexpenditure modify "projectCode" varchar (400 CHAR);
alter table tb_lim_otherexpenditure modify "projectName" varchar (2000 CHAR);
alter table tb_lim_otherexpenditure modify "status" varchar (100 CHAR);
alter table tb_lim_params modify "creator" varchar (100 CHAR);
alter table tb_lim_params modify "dimension" varchar (100 CHAR);
alter table tb_lim_params modify "modifier" varchar (100 CHAR);
alter table tb_lim_params modify "paramCode" varchar (100 CHAR);
alter table tb_lim_params modify "paramName" varchar (100 CHAR);
alter table tb_lim_params modify "regex" varchar (200 CHAR);
alter table tb_lim_params modify "remark" varchar (510 CHAR);
alter table tb_lim_params modify "variableName" varchar (100 CHAR);
alter table tb_lim_params2paramsformula modify "creator" varchar (100 CHAR);
alter table tb_lim_params2paramsformula modify "formula" varchar (2000 CHAR);
alter table tb_lim_params2paramsformula modify "modifier" varchar (100 CHAR);
alter table tb_lim_paramsconfig modify "alias" varchar (100 CHAR);
alter table tb_lim_paramsconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_paramsconfig modify "dataSource" varchar (8000 CHAR);
alter table tb_lim_paramsconfig modify "defaultValue" varchar (100 CHAR);
alter table tb_lim_paramsconfig modify "dimension" varchar (100 CHAR);
alter table tb_lim_paramsconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_paramsconfig modify "referenceText" varchar (2000 CHAR);
alter table tb_lim_paramsformula modify "creator" varchar (100 CHAR);
alter table tb_lim_paramsformula modify "formula" varchar (2000 CHAR);
alter table tb_lim_paramsformula modify "modifier" varchar (100 CHAR);
alter table tb_lim_paramspartformula modify "detectionLimit" varchar (100 CHAR);
alter table tb_lim_paramspartformula modify "formula" varchar (2000 CHAR);
alter table tb_lim_paramspartformula modify "paramsName" varchar (200 CHAR);
alter table tb_lim_paramstestformula modify "alias" varchar (100 CHAR);
alter table tb_lim_paramstestformula modify "aliasInReport" varchar (100 CHAR);
alter table tb_lim_paramstestformula modify "defaultValue" varchar (100 CHAR);
alter table tb_lim_paramstestformula modify "detectionLimit" varchar (100 CHAR);
alter table tb_lim_paramstestformula modify "dimension" varchar (100 CHAR);
alter table tb_lim_paramstestformula modify "paramsName" varchar (100 CHAR);
alter table tb_lim_person modify "archivesPlace" varchar (200 CHAR);
alter table tb_lim_person modify "birthPlace" varchar (200 CHAR);
alter table tb_lim_person modify "certificateNO" varchar (200 CHAR);
alter table tb_lim_person modify "cName" varchar (100 CHAR);
alter table tb_lim_person modify "codeSigning" varchar (40 CHAR);
alter table tb_lim_person modify "contactMethod" varchar (100 CHAR);
alter table tb_lim_person modify "creator" varchar (100 CHAR);
alter table tb_lim_person modify "degree" varchar (100 CHAR);
alter table tb_lim_person modify "developMethodRes" varchar (510 CHAR);
alter table tb_lim_person modify "email" varchar (200 CHAR);
alter table tb_lim_person modify "emergentLinkMan" varchar (100 CHAR);
alter table tb_lim_person modify "eName" varchar (100 CHAR);
alter table tb_lim_person modify "experienceRequired" varchar (510 CHAR);
alter table tb_lim_person modify "fullPinYin" varchar (200 CHAR);
alter table tb_lim_person modify "homeAddress" varchar (200 CHAR);
alter table tb_lim_person modify "homeTel" varchar (100 CHAR);
alter table tb_lim_person modify "homeTown" varchar (200 CHAR);
alter table tb_lim_person modify "idCard" varchar (40 CHAR);
alter table tb_lim_person modify "manageRes" varchar (510 CHAR);
alter table tb_lim_person modify "mobile" varchar (100 CHAR);
alter table tb_lim_person modify "modifier" varchar (100 CHAR);
alter table tb_lim_person modify "nation" varchar (40 CHAR);
alter table tb_lim_person modify "nativePlace" varchar (200 CHAR);
alter table tb_lim_person modify "photoUrl" varchar (1000 CHAR);
alter table tb_lim_person modify "pinYin" varchar (200 CHAR);
alter table tb_lim_person modify "politicalFace" varchar (40 CHAR);
alter table tb_lim_person modify "regulateName" varchar (100 CHAR);
alter table tb_lim_person modify "remark" varchar (510 CHAR);
alter table tb_lim_person modify "school" varchar (100 CHAR);
alter table tb_lim_person modify "signature" varchar (400 CHAR);
alter table tb_lim_person modify "specialty" varchar (100 CHAR);
alter table tb_lim_person modify "submissionDuty" varchar (510 CHAR);
alter table tb_lim_person modify "tecCompetence" varchar (510 CHAR);
alter table tb_lim_person modify "testResEvaluation" varchar (510 CHAR);
alter table tb_lim_person modify "testResWork" varchar (510 CHAR);
alter table tb_lim_person modify "trainingPrograms" varchar (510 CHAR);
alter table tb_lim_person modify "userNo" varchar (80 CHAR);
alter table tb_lim_person modify "volk" varchar (40 CHAR);
alter table tb_lim_personability modify "abilityType" varchar (200 CHAR);
alter table tb_lim_personability modify "redAnalyzeItemName" varchar (510 CHAR);
alter table tb_lim_personability modify "redAnalyzeMethodName" varchar (510 CHAR);
alter table tb_lim_personability modify "redCountryStandard" varchar (400 CHAR);
alter table tb_lim_personcert modify "certCode" varchar (100 CHAR);
alter table tb_lim_personcert modify "certName" varchar (200 CHAR);
alter table tb_lim_personcert modify "certType" varchar (200 CHAR);
alter table tb_lim_personcert modify "issuingAuthority" varchar (200 CHAR);
alter table tb_lim_personcert modify "personName" varchar (510 CHAR);
alter table tb_lim_personcert modify "phoneUrl" varchar (1000 CHAR);
alter table tb_lim_personcert modify "remark" varchar (2000 CHAR);
alter table tb_lim_personfacemsg modify "creator" varchar (100 CHAR);
alter table tb_lim_personfacemsg modify "modifier" varchar (100 CHAR);
alter table tb_lim_projectinstrument modify "administratorName" varchar (100 CHAR);
alter table tb_lim_projectinstrument modify "creator" varchar (100 CHAR);
alter table tb_lim_projectinstrument modify "inRemarks" varchar (2000 CHAR);
alter table tb_lim_projectinstrument modify "modifier" varchar (100 CHAR);
alter table tb_lim_projectinstrument modify "outRemarks" varchar (2000 CHAR);
alter table tb_lim_projectinstrument modify "projectName" varchar (2000 CHAR);
alter table tb_lim_projectinstrument modify "userIds" varchar (4000 CHAR);
alter table tb_lim_projectinstrument modify "userNames" varchar (4000 CHAR);
alter table tb_lim_projectinstrumentdetails modify "creator" varchar (100 CHAR);
alter table tb_lim_projectinstrumentdetails modify "inPerson" varchar (100 CHAR);
alter table tb_lim_projectinstrumentdetails modify "modifier" varchar (100 CHAR);
alter table tb_lim_projectinstrumentdetails modify "outPerson" varchar (100 CHAR);
alter table tb_lim_projecttype modify "alias" varchar (510 CHAR);
alter table tb_lim_projecttype modify "creator" varchar (100 CHAR);
alter table tb_lim_projecttype modify "mark" varchar (510 CHAR);
alter table tb_lim_projecttype modify "modifier" varchar (100 CHAR);
alter table tb_lim_projecttype modify "name" varchar (510 CHAR);
alter table tb_lim_publishsystemversion modify "creator" varchar (100 CHAR);
alter table tb_lim_publishsystemversion modify "flaywayVersion" varchar (100 CHAR);
alter table tb_lim_publishsystemversion modify "modifier" varchar (100 CHAR);
alter table tb_lim_publishsystemversion modify "publishPerson" varchar (100 CHAR);
alter table tb_lim_publishsystemversion modify "title" varchar (400 CHAR);
alter table tb_lim_publishsystemversion modify "versionNum" varchar (200 CHAR);
alter table tb_lim_recandpayrecord modify "collectItem" varchar (510 CHAR);
alter table tb_lim_recandpayrecord modify "creator" varchar (100 CHAR);
alter table tb_lim_recandpayrecord modify "invoiceCode" varchar (100 CHAR);
alter table tb_lim_recandpayrecord modify "invoiceNum" varchar (100 CHAR);
alter table tb_lim_recandpayrecord modify "modifier" varchar (100 CHAR);
alter table tb_lim_recandpayrecord modify "operatorName" varchar (100 CHAR);
alter table tb_lim_recandpayrecord modify "remark" varchar (2000 CHAR);
alter table tb_lim_recordconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_recordconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_recordconfig modify "recordName" varchar (200 CHAR);
alter table tb_lim_recordconfig modify "remark" varchar (510 CHAR);
alter table tb_lim_recordconfig modify "sampleTypeIds" varchar (800 CHAR);
alter table tb_lim_reportapply modify "code" varchar (100 CHAR);
alter table tb_lim_reportapply modify "creator" varchar (100 CHAR);
alter table tb_lim_reportapply modify "location" varchar (100 CHAR);
alter table tb_lim_reportapply modify "modifier" varchar (100 CHAR);
alter table tb_lim_reportapply modify "module" varchar (100 CHAR);
alter table tb_lim_reportapply modify "moduleName" varchar (100 CHAR);
alter table tb_lim_reportapply modify "name" varchar (100 CHAR);
alter table tb_lim_reportapply modify "remark" varchar (1000 CHAR);
alter table tb_lim_reportconfig modify "beanName" varchar (1000 CHAR);
alter table tb_lim_reportconfig modify "controlNum" varchar (200 CHAR);
alter table tb_lim_reportconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_reportconfig modify "dataMethod" varchar (510 CHAR);
alter table tb_lim_reportconfig modify "defineFileName" varchar (1000 CHAR);
alter table tb_lim_reportconfig modify "method" varchar (510 CHAR);
alter table tb_lim_reportconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_reportconfig modify "outputName" varchar (200 CHAR);
alter table tb_lim_reportconfig modify "params" varchar (1000 CHAR);
alter table tb_lim_reportconfig modify "remark" varchar (1000 CHAR);
alter table tb_lim_reportconfig modify "reportCode" varchar (200 CHAR);
alter table tb_lim_reportconfig modify "reportName" varchar (2000 CHAR);
alter table tb_lim_reportconfig modify "returnType" varchar (40 CHAR);
alter table tb_lim_reportconfig modify "strUrl" varchar (100 CHAR);
alter table tb_lim_reportconfig modify "template" varchar (200 CHAR);
alter table tb_lim_reportconfig modify "templateName" varchar (200 CHAR);
alter table tb_lim_reportconfig modify "typeCode" varchar (100 CHAR);
alter table tb_lim_reportconfig modify "versionNum" varchar (200 CHAR);
alter table tb_lim_reportmodule modify "auxiliaryInstrument" varchar (510 CHAR);
alter table tb_lim_reportmodule modify "creator" varchar (100 CHAR);
alter table tb_lim_reportmodule modify "modifier" varchar (100 CHAR);
alter table tb_lim_reportmodule modify "moduleCode" varchar (200 CHAR);
alter table tb_lim_reportmodule modify "moduleName" varchar (200 CHAR);
alter table tb_lim_reportmodule modify "sonTableJson" varchar (1000 CHAR);
alter table tb_lim_reportmodule modify "sourceTableName" varchar (200 CHAR);
alter table tb_lim_reportmodule modify "tableName" varchar (200 CHAR);
alter table tb_lim_reportmodule modify "totalTest" varchar (510 CHAR);
alter table tb_lim_reportmodule2grouptype modify "groupTypeName" varchar (510 CHAR);
alter table tb_lim_sampletypegroup modify "containerName" varchar (510 CHAR);
alter table tb_lim_sampletypegroup modify "creator" varchar (100 CHAR);
alter table tb_lim_sampletypegroup modify "fixer" varchar (2000 CHAR);
alter table tb_lim_sampletypegroup modify "groupName" varchar (100 CHAR);
alter table tb_lim_sampletypegroup modify "modifier" varchar (100 CHAR);
alter table tb_lim_sampletypegroup modify "pretreatmentMethod" varchar (400 CHAR);
alter table tb_lim_sampletypegroup modify "remark" varchar (2000 CHAR);
alter table tb_lim_sampletypegroup modify "sampleVolume" varchar (400 CHAR);
alter table tb_lim_sampletypegroup modify "saveCondition" varchar (2000 CHAR);
alter table tb_lim_sampletypegroup modify "volumeType" varchar (200 CHAR);
alter table tb_lim_serialidentifierconfig modify "configCode" varchar (200 CHAR);
alter table tb_lim_serialidentifierconfig modify "configName" varchar (200 CHAR);
alter table tb_lim_serialidentifierconfig modify "configRule" varchar (2000 CHAR);
alter table tb_lim_serialidentifierconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_serialidentifierconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_serialidentifierconfig modify "remark" varchar (2000 CHAR);
alter table tb_lim_serialnumberconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_serialnumberconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_serialnumberconfig modify "para0" varchar (200 CHAR);
alter table tb_lim_serialnumberconfig modify "para1" varchar (200 CHAR);
alter table tb_lim_serialnumberconfig modify "para2" varchar (200 CHAR);
alter table tb_lim_serialnumberconfig modify "para3" varchar (200 CHAR);
alter table tb_lim_serialnumberconfig modify "serialNumberType" varchar (200 CHAR);
alter table tb_lim_test modify "airPollution" varchar (200 CHAR);
alter table tb_lim_test modify "creator" varchar (100 CHAR);
alter table tb_lim_test modify "domainCode" varchar (100 CHAR);
alter table tb_lim_test modify "examLimitValue" varchar (100 CHAR);
alter table tb_lim_test modify "examLimitValueLess" varchar (100 CHAR);
alter table tb_lim_test modify "fullPinYin" varchar (510 CHAR);
alter table tb_lim_test modify "lowerLimit" varchar (100 CHAR);
alter table tb_lim_test modify "modifier" varchar (100 CHAR);
alter table tb_lim_test modify "pinYin" varchar (200 CHAR);
alter table tb_lim_test modify "redAnalyzeItemName" varchar (200 CHAR);
alter table tb_lim_test modify "redAnalyzeMethodName" varchar (510 CHAR);
alter table tb_lim_test modify "redCountryStandard" varchar (200 CHAR);
alter table tb_lim_test modify "redYearSn" varchar (200 CHAR);
alter table tb_lim_test modify "remark" varchar (2000 CHAR);
alter table tb_lim_test modify "shMethodName" varchar (510 CHAR);
alter table tb_lim_test modify "shSamplingMethodName" varchar (510 CHAR);
alter table tb_lim_test modify "testCode" varchar (100 CHAR);
alter table tb_lim_test modify "testName" varchar (2000 CHAR);
alter table tb_lim_test modify "tips" varchar (2000 CHAR);
alter table tb_lim_test modify "totalTestName" varchar (2000 CHAR);
alter table tb_lim_testexpand modify "examLimitValue" varchar (100 CHAR);
alter table tb_lim_testexpand modify "examLimitValueLess" varchar (100 CHAR);
alter table tb_lim_testexpand modify "lowerLimit" varchar (100 CHAR);
alter table tb_lim_testoperatelog modify "newValue" varchar (8000 CHAR);
alter table tb_lim_testoperatelog modify "oldValue" varchar (8000 CHAR);
alter table tb_lim_testoperatelog modify "operateField" varchar (100 CHAR);
alter table tb_lim_testoperatelog modify "tableName" varchar (100 CHAR);
alter table tb_lim_testpost modify "car" varchar (100 CHAR);
alter table tb_lim_testpost modify "chargePerson" varchar (100 CHAR);
alter table tb_lim_testpost modify "creator" varchar (100 CHAR);
alter table tb_lim_testpost modify "description" varchar (2000 CHAR);
alter table tb_lim_testpost modify "modifier" varchar (100 CHAR);
alter table tb_lim_testpost modify "postCode" varchar (200 CHAR);
alter table tb_lim_testpost modify "postName" varchar (200 CHAR);
alter table tb_lim_testqcrange modify "absLimit" varchar (100 CHAR);
alter table tb_lim_testqcrange modify "creator" varchar (100 CHAR);
alter table tb_lim_testqcrange modify "modifier" varchar (100 CHAR);
alter table tb_lim_testqcrange modify "rangeConfig" varchar (100 CHAR);
alter table tb_lim_testqcrange modify "relLimit" varchar (100 CHAR);
alter table tb_lim_testqcremindconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_testqcremindconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_training modify "applicant" varchar (100 CHAR);
alter table tb_lim_training modify "content" varchar (510 CHAR);
alter table tb_lim_training modify "creator" varchar (100 CHAR);
alter table tb_lim_training modify "lecturer" varchar (100 CHAR);
alter table tb_lim_training modify "modifier" varchar (100 CHAR);
alter table tb_lim_training modify "recorder" varchar (100 CHAR);
alter table tb_lim_training modify "trainingName" varchar (100 CHAR);
alter table tb_lim_training modify "way" varchar (100 CHAR);
alter table tb_lim_training2participants modify "participantsName" varchar (100 CHAR);
alter table tb_lim_versioninfo modify "codeUrl" varchar (6000 CHAR);
alter table tb_lim_versioninfo modify "creator" varchar (100 CHAR);
alter table tb_lim_versioninfo modify "modifier" varchar (100 CHAR);
alter table tb_lim_versioninfo modify "verCode" varchar (600 CHAR);
alter table tb_lim_versioninfo modify "version" varchar (100 CHAR);
alter table tb_lim_versioninfo modify "verType" varchar (100 CHAR);
alter table tb_lim_versioninfo modify "verUrl" varchar (510 CHAR);
alter table tb_lim_versioninfo modify "verValue" varchar (4000 CHAR);
alter table tb_lim_workdayconfig modify "creator" varchar (100 CHAR);
alter table tb_lim_workdayconfig modify "modifier" varchar (100 CHAR);
alter table tb_lim_workdayconfig modify "weekendDay" varchar (40 CHAR);
alter table tb_lim_workdayconfig modify "workday" varchar (40 CHAR);
alter table tb_monitor_fixedpoint modify "creator" varchar (100 CHAR);
alter table tb_monitor_fixedpoint modify "examArea" varchar (100 CHAR);
alter table tb_monitor_fixedpoint modify "folderType" varchar (2000 CHAR);
alter table tb_monitor_fixedpoint modify "internalCode" varchar (100 CHAR);
alter table tb_monitor_fixedpoint modify "lat" varchar (100 CHAR);
alter table tb_monitor_fixedpoint modify "level" varchar (100 CHAR);
alter table tb_monitor_fixedpoint modify "lon" varchar (100 CHAR);
alter table tb_monitor_fixedpoint modify "modifier" varchar (100 CHAR);
alter table tb_monitor_fixedpoint modify "pointCode" varchar (100 CHAR);
alter table tb_monitor_fixedpoint modify "pointName" varchar (510 CHAR);
alter table tb_monitor_fixedpoint modify "remark" varchar (4000 CHAR);
alter table tb_monitor_fixedpoint modify "stationName" varchar (510 CHAR);
alter table tb_monitor_fixedpoint modify "villageCode" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "acidp" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "acidpl" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "airpl" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "areasl" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "boilerMakeUnit" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "chimneyHeight" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "craftFacilityName" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "creator" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "emissionFate" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "equipmentTypeName" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "exhaustPipeHeight" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "fuelType" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "functionZoneCode" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "gridCoverPeoples" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "gridLength" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "gridWidth" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "importAndExport" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "modifier" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "noiseFunZoneCode" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "noiseSourceCode" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "pollutionType" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "purificateFacilityName" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "purificateFacilityType" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "purificateFacilityUnit" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "railwayLength" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "railwayWidth" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "rchal" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "rdLevel" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "rdsecfromto" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "rdsecName" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "refer" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "so2pl" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "stoveFacilityCode" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "stoveFacilityType" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "underWaterType" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "underWaterTypeCode" varchar (510 CHAR);
alter table tb_monitor_fixedpointexpend modify "watersl" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "waterworks" varchar (400 CHAR);
alter table tb_monitor_fixedpointexpend modify "weekCalcu" varchar (100 CHAR);
alter table tb_monitor_fixedpointexpend modify "ygwq" varchar (510 CHAR);
alter table tb_monitor_fixedpointproperty modify "creator" varchar (100 CHAR);
alter table tb_monitor_fixedpointproperty modify "modifier" varchar (100 CHAR);
alter table tb_monitor_fixedpointproperty modify "pointType" varchar (100 CHAR);
alter table tb_monitor_fixedpointproperty modify "propertyName" varchar (510 CHAR);
alter table tb_monitor_fixedpointproperty modify "remark" varchar (4000 CHAR);
alter table tb_monitor_fixedpointsort modify "creator" varchar (100 CHAR);
alter table tb_monitor_fixedpointsort modify "modifier" varchar (100 CHAR);
alter table tb_monitor_fixedpointsort modify "sortName" varchar (510 CHAR);
alter table tb_monitor_fixedpointsortdetil modify "creator" varchar (100 CHAR);
alter table tb_monitor_fixedpointsortdetil modify "modifier" varchar (100 CHAR);
alter table tb_monitor_oicinformation modify "creator" varchar (100 CHAR);
alter table tb_monitor_oicinformation modify "examLimitValue" varchar (100 CHAR);
alter table tb_monitor_oicinformation modify "instrumentCode" varchar (100 CHAR);
alter table tb_monitor_oicinformation modify "instrumentModel" varchar (100 CHAR);
alter table tb_monitor_oicinformation modify "instrumentName" varchar (100 CHAR);
alter table tb_monitor_oicinformation modify "methodName" varchar (510 CHAR);
alter table tb_monitor_oicinformation modify "modifier" varchar (100 CHAR);
alter table tb_monitor_oicinformation modify "range" varchar (100 CHAR);
alter table tb_monitor_pointextendconfig modify "codeDataSource" varchar (200 CHAR);
alter table tb_monitor_pointextendconfig modify "creator" varchar (100 CHAR);
alter table tb_monitor_pointextendconfig modify "dataSource" varchar (4000 CHAR);
alter table tb_monitor_pointextendconfig modify "dataSourceUrl" varchar (200 CHAR);
alter table tb_monitor_pointextendconfig modify "defaultValue" varchar (200 CHAR);
alter table tb_monitor_pointextendconfig modify "filedAlias" varchar (100 CHAR);
alter table tb_monitor_pointextendconfig modify "filedName" varchar (100 CHAR);
alter table tb_monitor_pointextendconfig modify "modifier" varchar (100 CHAR);
alter table tb_monitor_pointextendconfig modify "pointType" varchar (100 CHAR);
alter table tb_monitor_pointextendconfig modify "treeChildFiled" varchar (200 CHAR);
alter table tb_monitor_pointextendconfig modify "urlReturnKey" varchar (200 CHAR);
alter table tb_monitor_pointextendconfig modify "urlReturnValue" varchar (200 CHAR);
alter table tb_monitor_pointextenddata modify "creator" varchar (100 CHAR);
alter table tb_monitor_pointextenddata modify "filedAlias" varchar (100 CHAR);
alter table tb_monitor_pointextenddata modify "filedName" varchar (100 CHAR);
alter table tb_monitor_pointextenddata modify "filedValue" varchar (200 CHAR);
alter table tb_monitor_pointextenddata modify "modifier" varchar (100 CHAR);
alter table tb_monitor_pointextenddata modify "pointType" varchar (100 CHAR);
alter table tb_monitor_property2point modify "pointCode" varchar (200 CHAR);
alter table tb_monitor_station modify "creator" varchar (100 CHAR);
alter table tb_monitor_station modify "modifier" varchar (100 CHAR);
alter table tb_monitor_station modify "remark" varchar (4000 CHAR);
alter table tb_monitor_station modify "staddress" varchar (510 CHAR);
alter table tb_monitor_station modify "stcode" varchar (100 CHAR);
alter table tb_monitor_station modify "stname" varchar (100 CHAR);
alter table tb_monitor_water modify "creator" varchar (100 CHAR);
alter table tb_monitor_water modify "modifier" varchar (100 CHAR);
alter table tb_monitor_water modify "remark" varchar (4000 CHAR);
alter table tb_monitor_water modify "waterCode" varchar (100 CHAR);
alter table tb_monitor_water modify "waterName" varchar (100 CHAR);
alter table tb_monitor_water modify "waterType" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "areaWaterLevel" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "awaterl" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "creator" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "endPlaceName" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "lakesTypeCode" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "locationName" varchar (500 CHAR);
alter table tb_monitor_waterexpand modify "modifier" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "netWaterLevel" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "remark" varchar (510 CHAR);
alter table tb_monitor_waterexpand modify "startPlaceName" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "waterFunctionZoneLen" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "waterFunctionZoneType" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "waterl" varchar (100 CHAR);
alter table tb_monitor_waterexpand modify "yswq" varchar (100 CHAR);
alter table tb_pro_analyseachievement2person modify "creator" varchar (100 CHAR);
alter table tb_pro_analyseachievement2person modify "modifier" varchar (100 CHAR);
alter table tb_pro_analyseachievementdetails modify "analystName" varchar (100 CHAR);
alter table tb_pro_analyseachievementdetails modify "analyzeItemName" varchar (200 CHAR);
alter table tb_pro_analyseachievementdetails modify "analyzeMethodName" varchar (510 CHAR);
alter table tb_pro_analyseachievementdetails modify "countryStandard" varchar (200 CHAR);
alter table tb_pro_analyseachievementdetails modify "sampleCode" varchar (100 CHAR);
alter table tb_pro_analyseachievementdetails modify "status" varchar (100 CHAR);
alter table tb_pro_analysebiologydata modify "dimension" varchar (100 CHAR);
alter table tb_pro_analysebiologydata modify "testValue" varchar (200 CHAR);
alter table tb_pro_analysebiologydata modify "volume" varchar (100 CHAR);
alter table tb_pro_analysedata modify "analystName" varchar (100 CHAR);
alter table tb_pro_analysedata modify "creator" varchar (100 CHAR);
alter table tb_pro_analysedata modify "dimension" varchar (100 CHAR);
alter table tb_pro_analysedata modify "examLimitValue" varchar (100 CHAR);
alter table tb_pro_analysedata modify "gatherCode" varchar (2000 CHAR);
alter table tb_pro_analysedata modify "lowerLimit" varchar (100 CHAR);
alter table tb_pro_analysedata modify "modifier" varchar (100 CHAR);
alter table tb_pro_analysedata modify "pxAverageValue" varchar (200 CHAR);
alter table tb_pro_analysedata modify "qcInfo" varchar (200 CHAR);
alter table tb_pro_analysedata modify "redAnalyzeItemName" varchar (200 CHAR);
alter table tb_pro_analysedata modify "redAnalyzeMethodName" varchar (510 CHAR);
alter table tb_pro_analysedata modify "redCountryStandard" varchar (510 CHAR);
alter table tb_pro_analysedata modify "seriesValue" varchar (200 CHAR);
alter table tb_pro_analysedata modify "status" varchar (100 CHAR);
alter table tb_pro_analysedata modify "testOrignValue" varchar (200 CHAR);
alter table tb_pro_analysedata modify "testValue" varchar (200 CHAR);
alter table tb_pro_analysedata modify "testValueDstr" varchar (200 CHAR);
alter table tb_pro_analyseoriginalrecord modify "creator" varchar (100 CHAR);
alter table tb_pro_analyseoriginalrecord modify "modifier" varchar (100 CHAR);
alter table tb_pro_analyseoriginalrecord modify "testFormula" varchar (510 CHAR);
alter table tb_pro_arrange2method modify "alias" varchar (200 CHAR);
alter table tb_pro_arrange2method modify "creator" varchar (100 CHAR);
alter table tb_pro_arrange2method modify "methodName" varchar (510 CHAR);
alter table tb_pro_arrange2method modify "modifier" varchar (100 CHAR);
alter table tb_pro_arrange2method modify "standardCode" varchar (200 CHAR);
alter table tb_pro_autotaskplan modify "creator" varchar (100 CHAR);
alter table tb_pro_autotaskplan modify "dealDate" varchar (100 CHAR);
alter table tb_pro_autotaskplan modify "modifier" varchar (100 CHAR);
alter table tb_pro_autotaskplan modify "month" varchar (200 CHAR);
alter table tb_pro_autotaskplan modify "taskCode" varchar (100 CHAR);
alter table tb_pro_autotaskplan modify "taskName" varchar (100 CHAR);
alter table tb_pro_businessserialnumber modify "businessNumber" varchar (100 CHAR);
alter table tb_pro_businessserialnumber modify "businessType" varchar (100 CHAR);
alter table tb_pro_businessserialnumber modify "creator" varchar (100 CHAR);
alter table tb_pro_businessserialnumber modify "modifier" varchar (100 CHAR);
alter table tb_pro_businessserialnumber modify "para0" varchar (100 CHAR);
alter table tb_pro_businessserialnumber modify "para1" varchar (100 CHAR);
alter table tb_pro_businessserialnumber modify "para2" varchar (100 CHAR);
alter table tb_pro_businessserialnumber modify "para3" varchar (100 CHAR);
alter table tb_pro_businessserialnumber modify "serialNumberType" varchar (100 CHAR);
alter table tb_pro_comment modify "commentPersonName" varchar (100 CHAR);
alter table tb_pro_comment modify "creator" varchar (100 CHAR);
alter table tb_pro_comment modify "modifier" varchar (100 CHAR);
alter table tb_pro_commentcomplimentdetail modify "complimentorName" varchar (100 CHAR);
alter table tb_pro_commentcomplimentdetail modify "remark" varchar (2000 CHAR);
alter table tb_pro_costinfo modify "creator" varchar (100 CHAR);
alter table tb_pro_costinfo modify "modifier" varchar (100 CHAR);
alter table tb_pro_costinfo modify "status" varchar (40 CHAR);
alter table tb_pro_costinfodetail modify "creator" varchar (100 CHAR);
alter table tb_pro_costinfodetail modify "modifier" varchar (100 CHAR);
alter table tb_pro_costinfodetail modify "redAnalyzeItemName" varchar (100 CHAR);
alter table tb_pro_costinfodetail modify "redAnalyzeMethodName" varchar (510 CHAR);
alter table tb_pro_costinfodetail modify "redCountryStandard" varchar (200 CHAR);
alter table tb_pro_detailanalysedata modify "dimension" varchar (100 CHAR);
alter table tb_pro_detailanalysedata modify "examLimitValue" varchar (100 CHAR);
alter table tb_pro_detailanalysedata modify "pxAverageValue" varchar (200 CHAR);
alter table tb_pro_detailanalysedata modify "status" varchar (100 CHAR);
alter table tb_pro_detailanalysedata modify "testOrignValue" varchar (200 CHAR);
alter table tb_pro_detailanalysedata modify "testValue" varchar (200 CHAR);
alter table tb_pro_detailanalysedata modify "testValueDstr" varchar (200 CHAR);
alter table tb_pro_detaildata modify "customerName" varchar (200 CHAR);
alter table tb_pro_detaildata modify "folderCode" varchar (100 CHAR);
alter table tb_pro_detaildata modify "inspectedEnt" varchar (200 CHAR);
alter table tb_pro_detaildata modify "projectCode" varchar (100 CHAR);
alter table tb_pro_detaildata modify "projectName" varchar (200 CHAR);
alter table tb_pro_detaildata modify "redFolderName" varchar (200 CHAR);
alter table tb_pro_detaildata modify "sampleCode" varchar (100 CHAR);
alter table tb_pro_detaildata modify "watchSpot" varchar (200 CHAR);
alter table tb_pro_evaluationrecord modify "creator" varchar (100 CHAR);
alter table tb_pro_evaluationrecord modify "lowerLimitSymble" varchar (100 CHAR);
alter table tb_pro_evaluationrecord modify "lowerLimitValue" varchar (100 CHAR);
alter table tb_pro_evaluationrecord modify "modifier" varchar (100 CHAR);
alter table tb_pro_evaluationrecord modify "upperLimitSymble" varchar (100 CHAR);
alter table tb_pro_evaluationrecord modify "upperLimitValue" varchar (100 CHAR);
alter table tb_pro_explore modify "creator" varchar (100 CHAR);
alter table tb_pro_explore modify "modifier" varchar (100 CHAR);
alter table tb_pro_explore modify "remarks" varchar (4000 CHAR);
alter table tb_pro_expressageinfo modify "addressee" varchar (200 CHAR);
alter table tb_pro_expressageinfo modify "consigneeAddress" varchar (510 CHAR);
alter table tb_pro_expressageinfo modify "expressCompany" varchar (200 CHAR);
alter table tb_pro_expressageinfo modify "expressNumber" varchar (100 CHAR);
alter table tb_pro_expressageinfo modify "recipients" varchar (100 CHAR);
alter table tb_pro_expressageinfo modify "recipientsPhone" varchar (100 CHAR);
alter table tb_pro_expressageinfo modify "remark" varchar (400 CHAR);
alter table tb_pro_expressageinfo modify "sender" varchar (100 CHAR);
alter table tb_pro_flowcalibration modify "calibrationPeople" varchar (2000 CHAR);
alter table tb_pro_flowcalibration modify "calibrationTypeName" varchar (100 CHAR);
alter table tb_pro_flowcalibration modify "creator" varchar (100 CHAR);
alter table tb_pro_flowcalibration modify "modifier" varchar (100 CHAR);
alter table tb_pro_flowcalibrationparamdata modify "creator" varchar (100 CHAR);
alter table tb_pro_flowcalibrationparamdata modify "modifier" varchar (100 CHAR);
alter table tb_pro_flowcalibrationparamdata modify "paramName" varchar (100 CHAR);
alter table tb_pro_flowcalibrationparamdata modify "paramValue" varchar (100 CHAR);
alter table tb_pro_flowcalibrationrow modify "creator" varchar (100 CHAR);
alter table tb_pro_flowcalibrationrow modify "modifier" varchar (100 CHAR);
alter table tb_pro_foldersign modify "creator" varchar (100 CHAR);
alter table tb_pro_foldersign modify "modifier" varchar (100 CHAR);
alter table tb_pro_foldersign modify "signLat" varchar (100 CHAR);
alter table tb_pro_foldersign modify "signLon" varchar (100 CHAR);
alter table tb_pro_foldersign modify "signPersonName" varchar (100 CHAR);
alter table tb_pro_foldersign modify "signTip" varchar (400 CHAR);
alter table tb_pro_foldersign modify "voiceTip" varchar (1000 CHAR);
alter table tb_pro_homependingno modify "moduleCode" varchar (100 CHAR);
alter table tb_pro_logforanalyzemethod modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logforanalyzemethod modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logforcost modify "nextOperatorName" varchar (100 CHAR);
alter table tb_pro_logforcost modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logforcost modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logforcost modify "opinion" varchar (2000 CHAR);
alter table tb_pro_logforcost modify "remark" varchar (2000 CHAR);
alter table tb_pro_logfordata modify "nextOperatorName" varchar (100 CHAR);
alter table tb_pro_logfordata modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logfordata modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logfordata modify "opinion" varchar (2000 CHAR);
alter table tb_pro_logfordata modify "remark" varchar (2000 CHAR);
alter table tb_pro_logfororderform modify "nextOperatorName" varchar (100 CHAR);
alter table tb_pro_logfororderform modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logfororderform modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logfororderform modify "opinion" varchar (2000 CHAR);
alter table tb_pro_logfororderform modify "remark" varchar (2000 CHAR);
alter table tb_pro_logforplan modify "nextOperatorName" varchar (100 CHAR);
alter table tb_pro_logforplan modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logforplan modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logforplan modify "opinion" varchar (2000 CHAR);
alter table tb_pro_logforplan modify "remark" varchar (2000 CHAR);
alter table tb_pro_logforproject modify "nextOperatorName" varchar (100 CHAR);
alter table tb_pro_logforproject modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logforproject modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logforproject modify "opinion" varchar (2000 CHAR);
alter table tb_pro_logforproject modify "remark" varchar (2000 CHAR);
alter table tb_pro_logforrecord modify "nextOperatorName" varchar (100 CHAR);
alter table tb_pro_logforrecord modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logforrecord modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logforrecord modify "opinion" varchar (2000 CHAR);
alter table tb_pro_logforrecord modify "remark" varchar (2000 CHAR);
alter table tb_pro_logforreport modify "nextOperatorName" varchar (100 CHAR);
alter table tb_pro_logforreport modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logforreport modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logforreport modify "opinion" varchar (2000 CHAR);
alter table tb_pro_logforreport modify "remark" varchar (2000 CHAR);
alter table tb_pro_logforsample modify "nextOperatorName" varchar (100 CHAR);
alter table tb_pro_logforsample modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logforsample modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logforsample modify "opinion" varchar (2000 CHAR);
alter table tb_pro_logforsample modify "remark" varchar (2000 CHAR);
alter table tb_pro_logforworksheet modify "nextOperatorName" varchar (100 CHAR);
alter table tb_pro_logforworksheet modify "operateInfo" varchar (1000 CHAR);
alter table tb_pro_logforworksheet modify "operatorName" varchar (100 CHAR);
alter table tb_pro_logforworksheet modify "opinion" varchar (2000 CHAR);
alter table tb_pro_logforworksheet modify "remark" varchar (2000 CHAR);
alter table tb_pro_oadepartmentexpend modify "creator" varchar (100 CHAR);
alter table tb_pro_oadepartmentexpend modify "description" varchar (510 CHAR);
alter table tb_pro_oadepartmentexpend modify "modifier" varchar (100 CHAR);
alter table tb_pro_oaprojectexpend modify "creator" varchar (100 CHAR);
alter table tb_pro_oaprojectexpend modify "description" varchar (510 CHAR);
alter table tb_pro_oaprojectexpend modify "modifier" varchar (100 CHAR);
alter table tb_pro_oatask modify "creator" varchar (100 CHAR);
alter table tb_pro_oatask modify "currentAssignee" varchar (100 CHAR);
alter table tb_pro_oatask modify "currentAssigneeName" varchar (100 CHAR);
alter table tb_pro_oatask modify "currentTaskDefKey" varchar (100 CHAR);
alter table tb_pro_oatask modify "currentTaskName" varchar (100 CHAR);
alter table tb_pro_oatask modify "deptName" varchar (200 CHAR);
alter table tb_pro_oatask modify "description" varchar (510 CHAR);
alter table tb_pro_oatask modify "modifier" varchar (100 CHAR);
alter table tb_pro_oatask modify "procTypeCode" varchar (100 CHAR);
alter table tb_pro_oatask modify "procTypeName" varchar (100 CHAR);
alter table tb_pro_oatask modify "sponsor" varchar (100 CHAR);
alter table tb_pro_oatask modify "sponsorName" varchar (200 CHAR);
alter table tb_pro_oatask modify "status" varchar (100 CHAR);
alter table tb_pro_oatask modify "title" varchar (510 CHAR);
alter table tb_pro_oataskhandlelog modify "actTaskDefKey" varchar (100 CHAR);
alter table tb_pro_oataskhandlelog modify "actTaskName" varchar (100 CHAR);
alter table tb_pro_oataskhandlelog modify "assignee" varchar (100 CHAR);
alter table tb_pro_oataskhandlelog modify "assigneeName" varchar (200 CHAR);
alter table tb_pro_oataskhandlelog modify "comment" varchar (510 CHAR);
alter table tb_pro_oataskhandlelog modify "creator" varchar (100 CHAR);
alter table tb_pro_oataskhandlelog modify "modifier" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "assessRecord" varchar (2000 CHAR);
alter table tb_pro_ordercontract modify "contractCode" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "contractName" varchar (510 CHAR);
alter table tb_pro_ordercontract modify "contractNature" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "creator" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "firstEntName" varchar (510 CHAR);
alter table tb_pro_ordercontract modify "firstEntPersonName" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "firstEntPhone" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "modifier" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "registrant" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "remark" varchar (2000 CHAR);
alter table tb_pro_ordercontract modify "secondEntName" varchar (510 CHAR);
alter table tb_pro_ordercontract modify "secondEntPersonName" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "secondEntType" varchar (100 CHAR);
alter table tb_pro_ordercontract modify "shanghaiEntName" varchar (510 CHAR);
alter table tb_pro_ordercontract modify "subOrgs" varchar (510 CHAR);
alter table tb_pro_ordercontract modify "summary" varchar (2000 CHAR);
alter table tb_pro_ordercontractachievement2person modify "creator" varchar (100 CHAR);
alter table tb_pro_ordercontractachievement2person modify "modifier" varchar (100 CHAR);
alter table tb_pro_ordercontractachievementdetails modify "contractCode" varchar (100 CHAR);
alter table tb_pro_ordercontractachievementdetails modify "contractName" varchar (510 CHAR);
alter table tb_pro_ordercontractachievementdetails modify "contractNature" varchar (100 CHAR);
alter table tb_pro_ordercontractachievementdetails modify "firstEntName" varchar (510 CHAR);
alter table tb_pro_orderform modify "address" varchar (100 CHAR);
alter table tb_pro_orderform modify "creator" varchar (100 CHAR);
alter table tb_pro_orderform modify "customerOrderNo" varchar (100 CHAR);
alter table tb_pro_orderform modify "enterpriseName" varchar (100 CHAR);
alter table tb_pro_orderform modify "inspectedAddress" varchar (200 CHAR);
alter table tb_pro_orderform modify "inspectedEnt" varchar (200 CHAR);
alter table tb_pro_orderform modify "inspectedLinkMan" varchar (100 CHAR);
alter table tb_pro_orderform modify "inspectedLinkPhone" varchar (100 CHAR);
alter table tb_pro_orderform modify "linkPerson" varchar (100 CHAR);
alter table tb_pro_orderform modify "linkPhone" varchar (100 CHAR);
alter table tb_pro_orderform modify "modifier" varchar (100 CHAR);
alter table tb_pro_orderform modify "orderCode" varchar (100 CHAR);
alter table tb_pro_orderform modify "orderName" varchar (100 CHAR);
alter table tb_pro_orderform modify "registrantName" varchar (100 CHAR);
alter table tb_pro_orderform modify "remark" varchar (2000 CHAR);
alter table tb_pro_orderform modify "salesPersonName" varchar (100 CHAR);
alter table tb_pro_orderquotation modify "creator" varchar (100 CHAR);
alter table tb_pro_orderquotation modify "modifier" varchar (100 CHAR);
alter table tb_pro_otherdetail modify "creator" varchar (100 CHAR);
alter table tb_pro_otherdetail modify "formula" varchar (2000 CHAR);
alter table tb_pro_otherdetail modify "modifier" varchar (100 CHAR);
alter table tb_pro_otherdetail modify "remark" varchar (2000 CHAR);
alter table tb_pro_otherdetail modify "unit" varchar (100 CHAR);
alter table tb_pro_outsourcedata modify "analyzeMethodName" varchar (510 CHAR);
alter table tb_pro_outsourcedata modify "cmaCode" varchar (400 CHAR);
alter table tb_pro_outsourcedata modify "creator" varchar (100 CHAR);
alter table tb_pro_outsourcedata modify "detectionLimit" varchar (100 CHAR);
alter table tb_pro_outsourcedata modify "dimensionName" varchar (100 CHAR);
alter table tb_pro_outsourcedata modify "modifier" varchar (100 CHAR);
alter table tb_pro_outsourcedata modify "outSourceReportCode" varchar (400 CHAR);
alter table tb_pro_outsourcedata modify "subcontractor" varchar (1000 CHAR);
alter table tb_pro_outsourcedata modify "testValue" varchar (100 CHAR);
alter table tb_pro_paramsdata modify "creator" varchar (100 CHAR);
alter table tb_pro_paramsdata modify "dimension" varchar (100 CHAR);
alter table tb_pro_paramsdata modify "modifier" varchar (100 CHAR);
alter table tb_pro_paramsdata modify "paramsName" varchar (200 CHAR);
alter table tb_pro_paramsdata modify "paramsValue" varchar (4000 CHAR);
alter table tb_pro_performancestatisticforlocaldata modify "recordCode" varchar (40 CHAR);
alter table tb_pro_performancestatisticforlocaldata modify "redAnalyzeItemName" varchar (200 CHAR);
alter table tb_pro_performancestatisticforlocaldata modify "redAnalyzeMethodName" varchar (510 CHAR);
alter table tb_pro_performancestatisticforsampledata modify "recordCode" varchar (40 CHAR);
alter table tb_pro_performancestatisticforsampledata modify "senderName" varchar (200 CHAR);
alter table tb_pro_performancestatisticforworksheetdata modify "redAnalyzeItemName" varchar (200 CHAR);
alter table tb_pro_performancestatisticforworksheetdata modify "redAnalyzeMethodName" varchar (510 CHAR);
alter table tb_pro_performancestatisticforworksheetdata modify "workSheetCode" varchar (40 CHAR);
alter table tb_pro_project modify "addressName" varchar (600 CHAR);
alter table tb_pro_project modify "amount" varchar (100 CHAR);
alter table tb_pro_project modify "batchCode" varchar (40 CHAR);
alter table tb_pro_project modify "compAddress" varchar (200 CHAR);
alter table tb_pro_project modify "compDate" varchar (20 CHAR);
alter table tb_pro_project modify "controlInfo" varchar (510 CHAR);
alter table tb_pro_project modify "creator" varchar (100 CHAR);
alter table tb_pro_project modify "customerAddress" varchar (200 CHAR);
alter table tb_pro_project modify "customerName" varchar (2000 CHAR);
alter table tb_pro_project modify "customerOwner" varchar (100 CHAR);
alter table tb_pro_project modify "customerRequired" varchar (2000 CHAR);
alter table tb_pro_project modify "docNumber" varchar (100 CHAR);
alter table tb_pro_project modify "environmentCode" varchar (100 CHAR);
alter table tb_pro_project modify "extendGuid1" varchar (100 CHAR);
alter table tb_pro_project modify "extendGuid2" varchar (100 CHAR);
alter table tb_pro_project modify "extendGuid3" varchar (100 CHAR);
alter table tb_pro_project modify "extendStr1" varchar (510 CHAR);
alter table tb_pro_project modify "extendStr2" varchar (510 CHAR);
alter table tb_pro_project modify "extendStr3" varchar (510 CHAR);
alter table tb_pro_project modify "inspectedAddress" varchar (200 CHAR);
alter table tb_pro_project modify "inspectedEnt" varchar (200 CHAR);
alter table tb_pro_project modify "inspectedLinkMan" varchar (100 CHAR);
alter table tb_pro_project modify "inspectedLinkPhone" varchar (100 CHAR);
alter table tb_pro_project modify "invAmount" varchar (40 CHAR);
alter table tb_pro_project modify "isAccreditedRemark" varchar (1000 CHAR);
alter table tb_pro_project modify "linkEmail" varchar (100 CHAR);
alter table tb_pro_project modify "linkFax" varchar (100 CHAR);
alter table tb_pro_project modify "linkMan" varchar (100 CHAR);
alter table tb_pro_project modify "linkPhone" varchar (100 CHAR);
alter table tb_pro_project modify "modifier" varchar (100 CHAR);
alter table tb_pro_project modify "monitorMethodRemark" varchar (1000 CHAR);
alter table tb_pro_project modify "monitorMethods" varchar (510 CHAR);
alter table tb_pro_project modify "monitorPurp" varchar (510 CHAR);
alter table tb_pro_project modify "pollutionCode" varchar (100 CHAR);
alter table tb_pro_project modify "prodCompany" varchar (200 CHAR);
alter table tb_pro_project modify "projectAddress" varchar (200 CHAR);
alter table tb_pro_project modify "projectCode" varchar (100 CHAR);
alter table tb_pro_project modify "projectName" varchar (200 CHAR);
alter table tb_pro_project modify "qcInfo" varchar (510 CHAR);
alter table tb_pro_project modify "qrCodeUrl" varchar (1000 CHAR);
alter table tb_pro_project modify "regulateReportType" varchar (100 CHAR);
alter table tb_pro_project modify "remark" varchar (2000 CHAR);
alter table tb_pro_project modify "reportMethod" varchar (510 CHAR);
alter table tb_pro_project modify "reportNum" varchar (510 CHAR);
alter table tb_pro_project modify "reportStamp" varchar (100 CHAR);
alter table tb_pro_project modify "samKind" varchar (100 CHAR);
alter table tb_pro_project modify "sampleDescription" varchar (510 CHAR);
alter table tb_pro_project modify "sampleNameCustomer" varchar (200 CHAR);
alter table tb_pro_project modify "sampleQuantity" varchar (20 CHAR);
alter table tb_pro_project modify "sampleType" varchar (2000 CHAR);
alter table tb_pro_project modify "saveCondition" varchar (510 CHAR);
alter table tb_pro_project modify "saveDate" varchar (510 CHAR);
alter table tb_pro_project modify "sendSamplePerson" varchar (100 CHAR);
alter table tb_pro_project modify "status" varchar (100 CHAR);
alter table tb_pro_project modify "testCode" varchar (40 CHAR);
alter table tb_pro_project modify "tradeAreaCode" varchar (100 CHAR);
alter table tb_pro_project modify "zipCode" varchar (20 CHAR);
alter table tb_pro_project2customer modify "customerName" varchar (200 CHAR);
alter table tb_pro_projectapproval modify "comment" varchar (510 CHAR);
alter table tb_pro_projectapproval modify "modifyStatus" varchar (100 CHAR);
alter table tb_pro_projectcontract modify "contactPhone" varchar (40 CHAR);
alter table tb_pro_projectcontract modify "contractName" varchar (100 CHAR);
alter table tb_pro_projectcontract modify "fileExplain" varchar (510 CHAR);
alter table tb_pro_projectcontract modify "isSample" varchar (40 CHAR);
alter table tb_pro_projectcontract modify "sampleContact" varchar (100 CHAR);
alter table tb_pro_projectcontract modify "taskAddress" varchar (100 CHAR);
alter table tb_pro_projectcontract modify "taskLocation" varchar (2000 CHAR);
alter table tb_pro_projectcontract modify "taskSource" varchar (40 CHAR);
alter table tb_pro_projectcontract modify "taskType" varchar (100 CHAR);
alter table tb_pro_projectplan modify "judgment" varchar (510 CHAR);
alter table tb_pro_projectplan modify "qcSource" varchar (510 CHAR);
alter table tb_pro_projectplan modify "remark" varchar (2000 CHAR);
alter table tb_pro_projectplan modify "requires" varchar (2000 CHAR);
alter table tb_pro_projectplan modify "responsePerson" varchar (100 CHAR);
alter table tb_pro_projectplan modify "subItems" varchar (2000 CHAR);
alter table tb_pro_projectplan modify "subName" varchar (510 CHAR);
alter table tb_pro_projectplan modify "testMethodRequires" varchar (2000 CHAR);
alter table tb_pro_projectpushlog modify "pushResponse" varchar (2000 CHAR);
alter table tb_pro_projectpushlog modify "title" varchar (510 CHAR);
alter table tb_pro_projectpushlog modify "url" varchar (510 CHAR);
alter table tb_pro_qcdata modify "creator" varchar (100 CHAR);
alter table tb_pro_qcdata modify "modifier" varchar (100 CHAR);
alter table tb_pro_qcdata modify "paramsName" varchar (510 CHAR);
alter table tb_pro_qcdata modify "stdValue" varchar (200 CHAR);
alter table tb_pro_qcresultevaluation modify "createPerson" varchar (100 CHAR);
alter table tb_pro_qcresultevaluation modify "evaluationResult" varchar (2000 CHAR);
alter table tb_pro_qcresultevaluation modify "qcSituation" varchar (2000 CHAR);
alter table tb_pro_qcresultevaluation modify "remark" varchar (2000 CHAR);
alter table tb_pro_qcresultevaluation modify "resultCompare" varchar (2000 CHAR);
alter table tb_pro_qualitycontrol modify "constantVolume" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "creator" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "modifier" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "qcCode" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "qcConcentration" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "qcOriginValue" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "qcTestValue" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "qcValue" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "qcVolume" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "realSampleTestValue" varchar (100 CHAR);
alter table tb_pro_qualitycontrol modify "remark" varchar (1000 CHAR);
alter table tb_pro_qualitycontrol modify "ssConcentration" varchar (100 CHAR);
alter table tb_pro_qualitycontrolevaluate modify "allowLimit" varchar (100 CHAR);
alter table tb_pro_qualitycontrolevaluate modify "checkItem" varchar (200 CHAR);
alter table tb_pro_qualitycontrolevaluate modify "checkItemValue" varchar (200 CHAR);
alter table tb_pro_qualitycontrolevaluate modify "creator" varchar (100 CHAR);
alter table tb_pro_qualitycontrolevaluate modify "dimensionName" varchar (100 CHAR);
alter table tb_pro_qualitycontrolevaluate modify "modifier" varchar (100 CHAR);
alter table tb_pro_qualitycontrolevaluate modify "remark" varchar (510 CHAR);
alter table tb_pro_qualitymanage modify "qmCode" varchar (510 CHAR);
alter table tb_pro_qualitymanage modify "qmOriginValue" varchar (100 CHAR);
alter table tb_pro_qualitymanage modify "qmRange" varchar (510 CHAR);
alter table tb_pro_qualitymanage modify "qmTestValue" varchar (100 CHAR);
alter table tb_pro_qualitymanage modify "qmValue" varchar (510 CHAR);
alter table tb_pro_qualitymanage modify "qmVolume" varchar (100 CHAR);
alter table tb_pro_qualitymanage modify "redAnalyzeItemName" varchar (200 CHAR);
alter table tb_pro_qualitymanage modify "stTestValue" varchar (100 CHAR);
alter table tb_pro_qualitymanage modify "unit" varchar (100 CHAR);
alter table tb_pro_quotationdetail modify "creator" varchar (100 CHAR);
alter table tb_pro_quotationdetail modify "folderName" varchar (4000 CHAR);
alter table tb_pro_quotationdetail modify "modifier" varchar (100 CHAR);
alter table tb_pro_quotationdetail modify "redAnalyseItemName" varchar (510 CHAR);
alter table tb_pro_quotationdetail modify "redAnalyseMethod" varchar (510 CHAR);
alter table tb_pro_receivesamplerecord modify "backOpinion" varchar (2000 CHAR);
alter table tb_pro_receivesamplerecord modify "creator" varchar (100 CHAR);
alter table tb_pro_receivesamplerecord modify "modifier" varchar (100 CHAR);
alter table tb_pro_receivesamplerecord modify "recordCode" varchar (40 CHAR);
alter table tb_pro_receivesamplerecord modify "remark" varchar (510 CHAR);
alter table tb_pro_receivesamplerecord modify "reportCode" varchar (40 CHAR);
alter table tb_pro_receivesamplerecord modify "senderName" varchar (200 CHAR);
alter table tb_pro_receivesamplerecord modify "status" varchar (100 CHAR);
alter table tb_pro_receivesamplerecordparaminfo modify "creator" varchar (100 CHAR);
alter table tb_pro_receivesamplerecordparaminfo modify "modifier" varchar (100 CHAR);
alter table tb_pro_receivesamplerecordparaminfo modify "paramName" varchar (1000 CHAR);
alter table tb_pro_receivesamplerecordparamtemplate modify "creator" varchar (100 CHAR);
alter table tb_pro_receivesamplerecordparamtemplate modify "modifier" varchar (100 CHAR);
alter table tb_pro_receivesamplerecordparamtemplate modify "templateName" varchar (1000 CHAR);
alter table tb_pro_receivesubsamplerecord modify "auditorName" varchar (100 CHAR);
alter table tb_pro_receivesubsamplerecord modify "backOpinion" varchar (2000 CHAR);
alter table tb_pro_receivesubsamplerecord modify "checkerName" varchar (100 CHAR);
alter table tb_pro_receivesubsamplerecord modify "code" varchar (40 CHAR);
alter table tb_pro_receivesubsamplerecord modify "creator" varchar (100 CHAR);
alter table tb_pro_receivesubsamplerecord modify "modifier" varchar (100 CHAR);
alter table tb_pro_receivesubsamplerecord modify "receiveName" varchar (100 CHAR);
alter table tb_pro_receivesubsamplerecord modify "status" varchar (100 CHAR);
alter table tb_pro_receivesubsamplerecord2sample modify "creator" varchar (100 CHAR);
alter table tb_pro_receivesubsamplerecord2sample modify "modifier" varchar (100 CHAR);
alter table tb_pro_report modify "code" varchar (200 CHAR);
alter table tb_pro_report modify "creator" varchar (100 CHAR);
alter table tb_pro_report modify "folderName" varchar (200 CHAR);
alter table tb_pro_report modify "modifier" varchar (100 CHAR);
alter table tb_pro_report modify "regulateCode" varchar (100 CHAR);
alter table tb_pro_report modify "regulateReportType" varchar (100 CHAR);
alter table tb_pro_report modify "reportYear" varchar (20 CHAR);
alter table tb_pro_report modify "sampleName" varchar (100 CHAR);
alter table tb_pro_report modify "securityCode" varchar (200 CHAR);
alter table tb_pro_report modify "status" varchar (100 CHAR);
alter table tb_pro_report modify "testName" varchar (100 CHAR);
alter table tb_pro_reportachievement2person modify "creator" varchar (100 CHAR);
alter table tb_pro_reportachievement2person modify "modifier" varchar (100 CHAR);
alter table tb_pro_reportachievementdetails modify "entName" varchar (510 CHAR);
alter table tb_pro_reportachievementdetails modify "projectCode" varchar (100 CHAR);
alter table tb_pro_reportachievementdetails modify "projectName" varchar (200 CHAR);
alter table tb_pro_reportachievementdetails modify "reportCode" varchar (100 CHAR);
alter table tb_pro_reportbaseinfo modify "creator" varchar (100 CHAR);
alter table tb_pro_reportbaseinfo modify "customerAddress" varchar (600 CHAR);
alter table tb_pro_reportbaseinfo modify "customerName" varchar (400 CHAR);
alter table tb_pro_reportbaseinfo modify "inspectedAddress" varchar (600 CHAR);
alter table tb_pro_reportbaseinfo modify "inspectedEnt" varchar (400 CHAR);
alter table tb_pro_reportbaseinfo modify "modifier" varchar (100 CHAR);
alter table tb_pro_reportbaseinfo modify "projectName" varchar (400 CHAR);
alter table tb_pro_reportbaseinfo modify "reportDate" varchar (200 CHAR);
alter table tb_pro_reportbaseinfo modify "systemCode" varchar (200 CHAR);
alter table tb_pro_reportbaseinfo modify "technicalRemark" varchar (2000 CHAR);
alter table tb_pro_reportbaseinfo modify "testPurpose" varchar (600 CHAR);
alter table tb_pro_reportdeprive modify "creator" varchar (100 CHAR);
alter table tb_pro_reportdeprive modify "deprivePerson" varchar (100 CHAR);
alter table tb_pro_reportdeprive modify "depriveReason" varchar (510 CHAR);
alter table tb_pro_reportdeprive modify "modifier" varchar (100 CHAR);
alter table tb_pro_reportdeprive modify "reportIds" varchar (2000 CHAR);
alter table tb_pro_reportdetail modify "creator" varchar (100 CHAR);
alter table tb_pro_reportdetail modify "modifier" varchar (100 CHAR);
alter table tb_pro_reportfolderinfo modify "creator" varchar (100 CHAR);
alter table tb_pro_reportfolderinfo modify "folderCode" varchar (200 CHAR);
alter table tb_pro_reportfolderinfo modify "folderName" varchar (400 CHAR);
alter table tb_pro_reportfolderinfo modify "folderRemark" varchar (1600 CHAR);
alter table tb_pro_reportfolderinfo modify "modifier" varchar (100 CHAR);
alter table tb_pro_reportfoldersortinfo modify "creator" varchar (100 CHAR);
alter table tb_pro_reportfoldersortinfo modify "modifier" varchar (100 CHAR);
alter table tb_pro_reportnumberpool modify "code" varchar (200 CHAR);
alter table tb_pro_reportnumberpool modify "creator" varchar (100 CHAR);
alter table tb_pro_reportnumberpool modify "modifier" varchar (100 CHAR);
alter table tb_pro_reportnumberpool modify "serialType" varchar (100 CHAR);
alter table tb_pro_reportrecover modify "creator" varchar (100 CHAR);
alter table tb_pro_reportrecover modify "modifier" varchar (100 CHAR);
alter table tb_pro_reportrecover modify "recoverPerson" varchar (100 CHAR);
alter table tb_pro_reportrecover modify "recoverReason" varchar (200 CHAR);
alter table tb_pro_reportsampleinfo modify "creator" varchar (100 CHAR);
alter table tb_pro_reportsampleinfo modify "modifier" varchar (100 CHAR);
alter table tb_pro_reportsampleinfo modify "sampleCode" varchar (200 CHAR);
alter table tb_pro_reportsampleinfo modify "sampleRemark" varchar (1600 CHAR);
alter table tb_pro_sample modify "code" varchar (100 CHAR);
alter table tb_pro_sample modify "creator" varchar (100 CHAR);
alter table tb_pro_sample modify "customerCode" varchar (100 CHAR);
alter table tb_pro_sample modify "disposeMeasure" varchar (2000 CHAR);
alter table tb_pro_sample modify "inspectedEnt" varchar (200 CHAR);
alter table tb_pro_sample modify "lat" varchar (40 CHAR);
alter table tb_pro_sample modify "lon" varchar (40 CHAR);
alter table tb_pro_sample modify "modifier" varchar (100 CHAR);
alter table tb_pro_sample modify "originalStatus" varchar (200 CHAR);
alter table tb_pro_sample modify "pack" varchar (100 CHAR);
alter table tb_pro_sample modify "preTreatmentCases" varchar (2000 CHAR);
alter table tb_pro_sample modify "redAnalyzeItems" varchar (8000 CHAR);
alter table tb_pro_sample modify "redFolderName" varchar (200 CHAR);
alter table tb_pro_sample modify "remark" varchar (2000 CHAR);
alter table tb_pro_sample modify "samColor" varchar (100 CHAR);
alter table tb_pro_sample modify "sampleExplain" varchar (2000 CHAR);
alter table tb_pro_sample modify "sampleSource" varchar (200 CHAR);
alter table tb_pro_sample modify "sampleWeight" varchar (100 CHAR);
alter table tb_pro_sample modify "samplingPlace" varchar (200 CHAR);
alter table tb_pro_sample modify "sortNum" varchar (100 CHAR);
alter table tb_pro_sample modify "status" varchar (100 CHAR);
alter table tb_pro_sample modify "storageConditions" varchar (510 CHAR);
alter table tb_pro_sample modify "unqualifiedReason" varchar (2000 CHAR);
alter table tb_pro_sample modify "volume" varchar (100 CHAR);
alter table tb_pro_sample modify "weightOrQuantity" varchar (100 CHAR);
alter table tb_pro_sampledispose modify "creator" varchar (100 CHAR);
alter table tb_pro_sampledispose modify "disposeRemarks" varchar (2000 CHAR);
alter table tb_pro_sampledispose modify "disposeSolution" varchar (100 CHAR);
alter table tb_pro_sampledispose modify "modifier" varchar (100 CHAR);
alter table tb_pro_sampledispose modify "redAnalyzeItems" varchar (2000 CHAR);
alter table tb_pro_sampledispose modify "reserveLocation" varchar (200 CHAR);
alter table tb_pro_sampledispose modify "sampleSource" varchar (200 CHAR);
alter table tb_pro_sampledispose modify "saveCondition" varchar (200 CHAR);
alter table tb_pro_sampledispose2test modify "redAnalyzeItemName" varchar (200 CHAR);
alter table tb_pro_samplefolder modify "boilerMakeUnit" varchar (200 CHAR);
alter table tb_pro_samplefolder modify "chimneyHeight" varchar (40 CHAR);
alter table tb_pro_samplefolder modify "craftFacilityName" varchar (200 CHAR);
alter table tb_pro_samplefolder modify "creator" varchar (100 CHAR);
alter table tb_pro_samplefolder modify "equipmentTypeName" varchar (200 CHAR);
alter table tb_pro_samplefolder modify "exhaustPipeHeight" varchar (40 CHAR);
alter table tb_pro_samplefolder modify "folderCode" varchar (100 CHAR);
alter table tb_pro_samplefolder modify "folderTypeName" varchar (100 CHAR);
alter table tb_pro_samplefolder modify "fuelType" varchar (100 CHAR);
alter table tb_pro_samplefolder modify "inspectedEnt" varchar (200 CHAR);
alter table tb_pro_samplefolder modify "lat" varchar (40 CHAR);
alter table tb_pro_samplefolder modify "lon" varchar (40 CHAR);
alter table tb_pro_samplefolder modify "modifier" varchar (100 CHAR);
alter table tb_pro_samplefolder modify "pollutionType" varchar (200 CHAR);
alter table tb_pro_samplefolder modify "purificateFacilityName" varchar (200 CHAR);
alter table tb_pro_samplefolder modify "purificateFacilityType" varchar (200 CHAR);
alter table tb_pro_samplefolder modify "purificateFacilityUnit" varchar (200 CHAR);
alter table tb_pro_samplefolder modify "redAnalyzeItems" varchar (2000 CHAR);
alter table tb_pro_samplefolder modify "stoveFacilityCode" varchar (100 CHAR);
alter table tb_pro_samplefolder modify "watchSpot" varchar (200 CHAR);
alter table tb_pro_samplefolderevaluate modify "folderPass" varchar (100 CHAR);
alter table tb_pro_samplefolderevaluate modify "qcRateValue" varchar (510 CHAR);
alter table tb_pro_samplefolderevaluate modify "remark" varchar (510 CHAR);
alter table tb_pro_samplefolderevaluate modify "resultEvaluate" varchar (100 CHAR);
alter table tb_pro_samplefoldertemplate modify "lat" varchar (40 CHAR);
alter table tb_pro_samplefoldertemplate modify "lon" varchar (40 CHAR);
alter table tb_pro_samplefoldertemplate modify "redAnalyzeItems" varchar (2000 CHAR);
alter table tb_pro_samplefoldertemplate modify "watchSpot" varchar (200 CHAR);
alter table tb_pro_samplegroup modify "analyseItemNames" varchar (4000 CHAR);
alter table tb_pro_samplegroup modify "containerName" varchar (510 CHAR);
alter table tb_pro_samplegroup modify "creator" varchar (100 CHAR);
alter table tb_pro_samplegroup modify "fixer" varchar (2000 CHAR);
alter table tb_pro_samplegroup modify "modifier" varchar (100 CHAR);
alter table tb_pro_samplegroup modify "pretreatmentMethod" varchar (400 CHAR);
alter table tb_pro_samplegroup modify "remark" varchar (2000 CHAR);
alter table tb_pro_samplegroup modify "riskDescription" varchar (2000 CHAR);
alter table tb_pro_samplegroup modify "sampleStatus" varchar (2000 CHAR);
alter table tb_pro_samplegroup modify "sampleTypeGroupName" varchar (510 CHAR);
alter table tb_pro_samplegroup modify "sampleVolume" varchar (400 CHAR);
alter table tb_pro_samplegroup modify "samplingBegin" varchar (2000 CHAR);
alter table tb_pro_samplegroup modify "samplingEnd" varchar (2000 CHAR);
alter table tb_pro_samplegroup modify "saveCondition" varchar (2000 CHAR);
alter table tb_pro_samplegroup modify "scanner" varchar (100 CHAR);
alter table tb_pro_samplegroup modify "transportationCondition" varchar (2000 CHAR);
alter table tb_pro_samplegroup2test modify "redAnalyzeMethodName" varchar (510 CHAR);
alter table tb_pro_samplegroup2test modify "redCountryStandard" varchar (200 CHAR);
alter table tb_pro_samplejudgedata modify "allowLimit" varchar (100 CHAR);
alter table tb_pro_samplejudgedata modify "checkItemValue" varchar (100 CHAR);
alter table tb_pro_samplejudgedata modify "creator" varchar (100 CHAR);
alter table tb_pro_samplejudgedata modify "dataStatus" varchar (100 CHAR);
alter table tb_pro_samplejudgedata modify "expectedValue" varchar (100 CHAR);
alter table tb_pro_samplejudgedata modify "modifier" varchar (100 CHAR);
alter table tb_pro_samplejudgedata modify "onlineValue" varchar (100 CHAR);
alter table tb_pro_samplejudgedata modify "pass" varchar (100 CHAR);
alter table tb_pro_samplejudgedata modify "qcRateValue" varchar (510 CHAR);
alter table tb_pro_samplejudgedata modify "standardCode" varchar (100 CHAR);
alter table tb_pro_samplejudgedata modify "testTimeStr" varchar (100 CHAR);
alter table tb_pro_samplepreparation modify "analyzeItemNames" varchar (200 CHAR);
alter table tb_pro_samplepreparation modify "content" varchar (1000 CHAR);
alter table tb_pro_samplepreparation modify "creator" varchar (100 CHAR);
alter table tb_pro_samplepreparation modify "method" varchar (400 CHAR);
alter table tb_pro_samplepreparation modify "modifier" varchar (100 CHAR);
alter table tb_pro_samplepreparation modify "preparedPersonName" varchar (100 CHAR);
alter table tb_pro_samplereserve modify "creator" varchar (100 CHAR);
alter table tb_pro_samplereserve modify "disposeMethod" varchar (200 CHAR);
alter table tb_pro_samplereserve modify "modifier" varchar (100 CHAR);
alter table tb_pro_samplereserve modify "remark" varchar (200 CHAR);
alter table tb_pro_samplereserve2test modify "redAnalyseItemName" varchar (100 CHAR);
alter table tb_pro_samplingachievement2person modify "creator" varchar (100 CHAR);
alter table tb_pro_samplingachievement2person modify "modifier" varchar (100 CHAR);
alter table tb_pro_samplingachievementdetails modify "recordCode" varchar (100 CHAR);
alter table tb_pro_samplingachievementdetails modify "sampleCode" varchar (100 CHAR);
alter table tb_pro_samplingachievementdetails modify "sampleFolderName" varchar (200 CHAR);
alter table tb_pro_samplingarrange modify "car" varchar (100 CHAR);
alter table tb_pro_samplingarrange modify "chargePerson" varchar (100 CHAR);
alter table tb_pro_samplingarrange modify "samplingPeople" varchar (510 CHAR);
alter table tb_pro_samplingarrange modify "samplingPeopleIds" varchar (2000 CHAR);
alter table tb_pro_samplingarrange modify "status" varchar (100 CHAR);
alter table tb_pro_samplingarrange modify "team" varchar (100 CHAR);
alter table tb_pro_samplingfrequencytest modify "redAnalyzeItemName" varchar (200 CHAR);
alter table tb_pro_samplingfrequencytest modify "redAnalyzeMethodName" varchar (200 CHAR);
alter table tb_pro_samplingfrequencytest modify "redCountryStandard" varchar (510 CHAR);
alter table tb_pro_samplingfrequencytesttemp modify "redAnalyzeItemName" varchar (200 CHAR);
alter table tb_pro_samplingfrequencytesttemp modify "redAnalyzeMethodName" varchar (200 CHAR);
alter table tb_pro_samplingfrequencytesttemp modify "redCountryStandard" varchar (510 CHAR);
alter table tb_pro_samplingpersonconfig modify "samplingPerson" varchar (100 CHAR);
alter table tb_pro_solutioncalibration modify "averageConcentration" varchar (100 CHAR);
alter table tb_pro_solutioncalibration modify "calibrationSolutionName" varchar (200 CHAR);
alter table tb_pro_solutioncalibration modify "creator" varchar (100 CHAR);
alter table tb_pro_solutioncalibration modify "formula" varchar (200 CHAR);
alter table tb_pro_solutioncalibration modify "modifier" varchar (100 CHAR);
alter table tb_pro_solutioncalibration modify "transferSolutionConcentration" varchar (100 CHAR);
alter table tb_pro_solutioncalibration modify "transferSolutionName" varchar (200 CHAR);
alter table tb_pro_solutioncalibrationrecord modify "concentration" varchar (100 CHAR);
alter table tb_pro_solutioncalibrationrecord modify "creator" varchar (100 CHAR);
alter table tb_pro_solutioncalibrationrecord modify "modifier" varchar (100 CHAR);
alter table tb_pro_solutioncalibrationrecord modify "transferVolume" varchar (100 CHAR);
alter table tb_pro_solutioncalibrationrecord modify "volumeEnd" varchar (100 CHAR);
alter table tb_pro_solutioncalibrationrecord modify "volumeStart" varchar (100 CHAR);
alter table tb_pro_statusforcostinfo modify "creator" varchar (100 CHAR);
alter table tb_pro_statusforcostinfo modify "currentPersonName" varchar (100 CHAR);
alter table tb_pro_statusforcostinfo modify "extendStr1" varchar (510 CHAR);
alter table tb_pro_statusforcostinfo modify "extendStr2" varchar (510 CHAR);
alter table tb_pro_statusforcostinfo modify "extendStr3" varchar (510 CHAR);
alter table tb_pro_statusforcostinfo modify "modifier" varchar (100 CHAR);
alter table tb_pro_statusforcostinfo modify "module" varchar (100 CHAR);
alter table tb_pro_statusforcostinfo modify "nextPersonName" varchar (100 CHAR);
alter table tb_pro_statusforproject modify "creator" varchar (100 CHAR);
alter table tb_pro_statusforproject modify "currentPersonName" varchar (100 CHAR);
alter table tb_pro_statusforproject modify "extendStr1" varchar (510 CHAR);
alter table tb_pro_statusforproject modify "extendStr2" varchar (510 CHAR);
alter table tb_pro_statusforproject modify "extendStr3" varchar (510 CHAR);
alter table tb_pro_statusforproject modify "modifier" varchar (100 CHAR);
alter table tb_pro_statusforproject modify "module" varchar (100 CHAR);
alter table tb_pro_statusforproject modify "nextPersonName" varchar (100 CHAR);
alter table tb_pro_statusforrecord modify "creator" varchar (100 CHAR);
alter table tb_pro_statusforrecord modify "currentPersonName" varchar (100 CHAR);
alter table tb_pro_statusforrecord modify "extendStr1" varchar (510 CHAR);
alter table tb_pro_statusforrecord modify "extendStr2" varchar (510 CHAR);
alter table tb_pro_statusforrecord modify "extendStr3" varchar (510 CHAR);
alter table tb_pro_statusforrecord modify "modifier" varchar (100 CHAR);
alter table tb_pro_statusforrecord modify "module" varchar (100 CHAR);
alter table tb_pro_statusforrecord modify "nextPersonName" varchar (100 CHAR);
alter table tb_pro_statusforreport modify "creator" varchar (100 CHAR);
alter table tb_pro_statusforreport modify "currentPersonName" varchar (100 CHAR);
alter table tb_pro_statusforreport modify "extendStr1" varchar (510 CHAR);
alter table tb_pro_statusforreport modify "extendStr2" varchar (510 CHAR);
alter table tb_pro_statusforreport modify "extendStr3" varchar (510 CHAR);
alter table tb_pro_statusforreport modify "modifier" varchar (100 CHAR);
alter table tb_pro_statusforreport modify "module" varchar (100 CHAR);
alter table tb_pro_statusforreport modify "nextPersonName" varchar (100 CHAR);
alter table tb_pro_submitrecord modify "creator" varchar (100 CHAR);
alter table tb_pro_submitrecord modify "modifier" varchar (100 CHAR);
alter table tb_pro_submitrecord modify "nextPerson" varchar (400 CHAR);
alter table tb_pro_submitrecord modify "stateFrom" varchar (100 CHAR);
alter table tb_pro_submitrecord modify "stateTo" varchar (100 CHAR);
alter table tb_pro_submitrecord modify "submitPersonName" varchar (100 CHAR);
alter table tb_pro_submitrecord modify "submitRemark" varchar (400 CHAR);
alter table tb_pro_survey modify "entDelegate" varchar (100 CHAR);
alter table tb_pro_survey modify "problem" varchar (2000 CHAR);
alter table tb_pro_survey modify "remark" varchar (2000 CHAR);
alter table tb_pro_survey modify "surveyNote" varchar (2000 CHAR);
alter table tb_pro_worksheet modify "creator" varchar (100 CHAR);
alter table tb_pro_worksheet modify "modifier" varchar (100 CHAR);
alter table tb_pro_worksheet modify "redAnalyzeItemName" varchar (200 CHAR);
alter table tb_pro_worksheet modify "remark" varchar (2000 CHAR);
alter table tb_pro_worksheetcalibrationcurve modify "creator" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurve modify "modifier" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurve modify "remark" varchar (2000 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "absorbance" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "absorbanceB" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "addAmount" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "addVolume" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "analyseCode" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "aValueTSF" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "aValueTTZ" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "creator" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "lessBlankAbsorbance" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "modifier" varchar (100 CHAR);
alter table tb_pro_worksheetcalibrationcurvedetail modify "relativeDeviation" varchar (100 CHAR);
alter table tb_pro_worksheetfolder modify "analystName" varchar (100 CHAR);
alter table tb_pro_worksheetfolder modify "auditorName" varchar (100 CHAR);
alter table tb_pro_worksheetfolder modify "backOpinion" varchar (2000 CHAR);
alter table tb_pro_worksheetfolder modify "certificatorName" varchar (100 CHAR);
alter table tb_pro_worksheetfolder modify "checkerName" varchar (100 CHAR);
alter table tb_pro_worksheetfolder modify "creator" varchar (100 CHAR);
alter table tb_pro_worksheetfolder modify "modifier" varchar (100 CHAR);
alter table tb_pro_worksheetfolder modify "remark" varchar (2000 CHAR);
alter table tb_pro_worksheetfolder modify "status" varchar (100 CHAR);
alter table tb_pro_worksheetfolder modify "workSheetCode" varchar (40 CHAR);
alter table tb_pro_worksheetreagent modify "concentration" varchar (400 CHAR);
alter table tb_pro_worksheetreagent modify "configurationSolution" varchar (510 CHAR);
alter table tb_pro_worksheetreagent modify "constantVolume" varchar (400 CHAR);
alter table tb_pro_worksheetreagent modify "context" varchar (2000 CHAR);
alter table tb_pro_worksheetreagent modify "course" varchar (2000 CHAR);
alter table tb_pro_worksheetreagent modify "creator" varchar (100 CHAR);
alter table tb_pro_worksheetreagent modify "diluent" varchar (400 CHAR);
alter table tb_pro_worksheetreagent modify "modifier" varchar (100 CHAR);
alter table tb_pro_worksheetreagent modify "opinion" varchar (2000 CHAR);
alter table tb_pro_worksheetreagent modify "reagent" varchar (2000 CHAR);
alter table tb_pro_worksheetreagent modify "reagentName" varchar (510 CHAR);
alter table tb_pro_worksheetreagent modify "reagentSpecification" varchar (510 CHAR);
alter table tb_pro_worksheetreagent modify "reagentVolumeQuality" varchar (400 CHAR);
alter table tb_pro_worksheetreagent modify "suitItem" varchar (400 CHAR);
alter table tb_qa_annualplan modify "auditContent" varchar (4000 CHAR);
alter table tb_qa_annualplan modify "auditPurpose" varchar (4000 CHAR);
alter table tb_qa_annualplan modify "creator" varchar (100 CHAR);
alter table tb_qa_annualplan modify "makePersonName" varchar (100 CHAR);
alter table tb_qa_annualplan modify "modifier" varchar (100 CHAR);
alter table tb_qa_annualplan modify "planType" varchar (100 CHAR);
alter table tb_qa_annualplan modify "remark" varchar (4000 CHAR);
alter table tb_qa_annualplan modify "status" varchar (100 CHAR);
alter table tb_qa_customercomplaintregist modify "complaintName" varchar (500 CHAR);
alter table tb_qa_customercomplaintregist modify "complaintPerson" varchar (100 CHAR);
alter table tb_qa_customercomplaintregist modify "creator" varchar (100 CHAR);
alter table tb_qa_customercomplaintregist modify "email" varchar (200 CHAR);
alter table tb_qa_customercomplaintregist modify "level" varchar (100 CHAR);
alter table tb_qa_customercomplaintregist modify "modifier" varchar (100 CHAR);
alter table tb_qa_customercomplaintregist modify "opinion" varchar (2000 CHAR);
alter table tb_qa_customercomplaintregist modify "phone" varchar (40 CHAR);
alter table tb_qa_customercomplaintregist modify "problemDescription" varchar (4000 CHAR);
alter table tb_qa_customercomplaintregist modify "registPerson" varchar (100 CHAR);
alter table tb_qa_customercomplaintregist modify "status" varchar (100 CHAR);
alter table tb_qa_customercomplaintregist modify "type" varchar (100 CHAR);
alter table tb_qa_internalauditimplementplan modify "auditedDept" varchar (200 CHAR);
alter table tb_qa_internalauditimplementplan modify "auditElement" varchar (4000 CHAR);
alter table tb_qa_internalauditimplementplan modify "auditor" varchar (100 CHAR);
alter table tb_qa_internalauditimplementplan modify "creator" varchar (100 CHAR);
alter table tb_qa_internalauditimplementplan modify "modifier" varchar (100 CHAR);
alter table tb_qa_internalauditimplementplan modify "personInCharge" varchar (100 CHAR);
alter table tb_qa_internalauditimplementplan modify "status" varchar (100 CHAR);
alter table tb_qa_internalauditplan modify "attendee" varchar (4000 CHAR);
alter table tb_qa_internalauditplan modify "auditContent" varchar (4000 CHAR);
alter table tb_qa_internalauditplan modify "auditGist" varchar (4000 CHAR);
alter table tb_qa_internalauditplan modify "auditPurp" varchar (4000 CHAR);
alter table tb_qa_internalauditplan modify "auditScope" varchar (4000 CHAR);
alter table tb_qa_internalauditplan modify "creator" varchar (100 CHAR);
alter table tb_qa_internalauditplan modify "modifier" varchar (100 CHAR);
alter table tb_qa_internalauditplan modify "status" varchar (100 CHAR);
alter table tb_qa_internalauditplanreport modify "auditResult" varchar (4000 CHAR);
alter table tb_qa_internalauditplanreport modify "auditReview" varchar (4000 CHAR);
alter table tb_qa_internalauditplanreport modify "creator" varchar (100 CHAR);
alter table tb_qa_internalauditplanreport modify "makerName" varchar (100 CHAR);
alter table tb_qa_internalauditplanreport modify "modifier" varchar (100 CHAR);
alter table tb_qa_internalcheckinfo modify "auditor" varchar (100 CHAR);
alter table tb_qa_internalcheckinfo modify "checkContent" varchar (4000 CHAR);
alter table tb_qa_internalcheckinfo modify "creator" varchar (100 CHAR);
alter table tb_qa_internalcheckinfo modify "modifier" varchar (100 CHAR);
alter table tb_qa_internalcheckinfo modify "remark" varchar (4000 CHAR);
alter table tb_qa_log modify "logType" varchar (100 CHAR);
alter table tb_qa_log modify "nextOperatorName" varchar (100 CHAR);
alter table tb_qa_log modify "objectType" varchar (100 CHAR);
alter table tb_qa_log modify "operateInfo" varchar (100 CHAR);
alter table tb_qa_log modify "operatorName" varchar (100 CHAR);
alter table tb_qa_log modify "opinion" varchar (2000 CHAR);
alter table tb_qa_log modify "remark" varchar (2000 CHAR);
alter table tb_qa_managementreviewplan modify "attendee" varchar (4000 CHAR);
alter table tb_qa_managementreviewplan modify "creator" varchar (100 CHAR);
alter table tb_qa_managementreviewplan modify "host" varchar (100 CHAR);
alter table tb_qa_managementreviewplan modify "modifier" varchar (100 CHAR);
alter table tb_qa_managementreviewplan modify "recorder" varchar (100 CHAR);
alter table tb_qa_managementreviewplan modify "reviewAddr" varchar (510 CHAR);
alter table tb_qa_managementreviewplan modify "reviewContent" varchar (4000 CHAR);
alter table tb_qa_managementreviewplan modify "reviewPrepareRequired" varchar (4000 CHAR);
alter table tb_qa_managementreviewplan modify "reviewPurp" varchar (4000 CHAR);
alter table tb_qa_managementreviewplan modify "status" varchar (100 CHAR);
alter table tb_qa_monitoringplan modify "creator" varchar (100 CHAR);
alter table tb_qa_monitoringplan modify "marker" varchar (100 CHAR);
alter table tb_qa_monitoringplan modify "modifier" varchar (100 CHAR);
alter table tb_qa_monitoringplan modify "planName" varchar (510 CHAR);
alter table tb_qa_monitoringplan modify "status" varchar (100 CHAR);
alter table tb_qa_monitoringplancheckinfo modify "checkContent" varchar (4000 CHAR);
alter table tb_qa_monitoringplancheckinfo modify "checker" varchar (100 CHAR);
alter table tb_qa_monitoringplancheckinfo modify "checkName" varchar (510 CHAR);
alter table tb_qa_monitoringplancheckinfo modify "checkResult" varchar (4000 CHAR);
alter table tb_qa_monitoringplancheckinfo modify "creator" varchar (100 CHAR);
alter table tb_qa_monitoringplancheckinfo modify "dutyDept" varchar (510 CHAR);
alter table tb_qa_monitoringplancheckinfo modify "dutyPersonName" varchar (100 CHAR);
alter table tb_qa_monitoringplancheckinfo modify "modifier" varchar (100 CHAR);
alter table tb_qa_monitoringplandetail modify "content" varchar (4000 CHAR);
alter table tb_qa_monitoringplandetail modify "creator" varchar (100 CHAR);
alter table tb_qa_monitoringplandetail modify "dutyDept" varchar (510 CHAR);
alter table tb_qa_monitoringplandetail modify "modifier" varchar (100 CHAR);
alter table tb_qa_monitoringplandetail modify "status" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "complete" varchar (4000 CHAR);
alter table tb_qa_notconformitem modify "correctMeasures" varchar (4000 CHAR);
alter table tb_qa_notconformitem modify "creator" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "modifier" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "ncBasis" varchar (1000 CHAR);
alter table tb_qa_notconformitem modify "ncCauseAnalysis" varchar (1000 CHAR);
alter table tb_qa_notconformitem modify "ncDeptPerson" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "ncDescribe" varchar (4000 CHAR);
alter table tb_qa_notconformitem modify "ncElement" varchar (1000 CHAR);
alter table tb_qa_notconformitem modify "ncFindPerson" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "ncIterm" varchar (4000 CHAR);
alter table tb_qa_notconformitem modify "ncItermNum" varchar (2000 CHAR);
alter table tb_qa_notconformitem modify "ncMainPerson" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "ncNature" varchar (1000 CHAR);
alter table tb_qa_notconformitem modify "ncProduceDept" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "ncSourceType" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "ncType" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "status" varchar (100 CHAR);
alter table tb_qa_notconformitem modify "verifierEvaluate" varchar (4000 CHAR);
alter table tb_qa_reviewplanreport modify "creator" varchar (100 CHAR);
alter table tb_qa_reviewplanreport modify "makerName" varchar (100 CHAR);
alter table tb_qa_reviewplanreport modify "modifier" varchar (100 CHAR);
alter table tb_qa_reviewplanreport modify "reviewContent" varchar (4000 CHAR);
alter table tb_qa_reviewplanreport modify "reviewDecision" varchar (4000 CHAR);
alter table tb_qa_reviewplanreport modify "reviewExpound" varchar (4000 CHAR);
alter table tb_qa_reviewplanreport modify "reviewGist" varchar (4000 CHAR);
alter table tb_qa_reviewplanreport modify "reviewPurp" varchar (4000 CHAR);
alter table tb_qa_riskandaccident modify "affirmEvaluate" varchar (1000 CHAR);
alter table tb_qa_riskandaccident modify "auditor" varchar (100 CHAR);
alter table tb_qa_riskandaccident modify "coefficient" varchar (100 CHAR);
alter table tb_qa_riskandaccident modify "creator" varchar (100 CHAR);
alter table tb_qa_riskandaccident modify "description" varchar (1000 CHAR);
alter table tb_qa_riskandaccident modify "measure" varchar (1000 CHAR);
alter table tb_qa_riskandaccident modify "modifier" varchar (100 CHAR);
alter table tb_qa_riskandaccident modify "performance" varchar (1000 CHAR);
alter table tb_qa_riskandaccident modify "reason" varchar (1000 CHAR);
alter table tb_qa_riskandaccident modify "sourceType" varchar (100 CHAR);
alter table tb_qa_riskandaccident modify "status" varchar (100 CHAR);
alter table tb_qa_submitrecord modify "creator" varchar (100 CHAR);
alter table tb_qa_submitrecord modify "modifier" varchar (100 CHAR);
alter table tb_qa_submitrecord modify "nextPerson" varchar (400 CHAR);
alter table tb_qa_submitrecord modify "stateFrom" varchar (100 CHAR);
alter table tb_qa_submitrecord modify "stateTo" varchar (100 CHAR);
alter table tb_qa_submitrecord modify "submitPersonName" varchar (100 CHAR);
alter table tb_qa_submitrecord modify "submitRemark" varchar (400 CHAR);
alter table tb_query_item modify "creator" varchar (100 CHAR);
alter table tb_query_item modify "itemDesc" varchar (510 CHAR);
alter table tb_query_item modify "itemName" varchar (200 CHAR);
alter table tb_query_item modify "modifier" varchar (100 CHAR);
alter table tb_query_itemcolumn modify "creator" varchar (100 CHAR);
alter table tb_query_itemcolumn modify "dataSource" varchar (4000 CHAR);
alter table tb_query_itemcolumn modify "modifier" varchar (100 CHAR);
alter table tb_query_itemcolumn modify "sortType" varchar (100 CHAR);
alter table tb_query_itemcondition modify "creator" varchar (100 CHAR);
alter table tb_query_itemcondition modify "dbCondition" varchar (4000 CHAR);
alter table tb_query_itemcondition modify "modifier" varchar (100 CHAR);
alter table tb_query_itemcondition modify "pageCondition" varchar (4000 CHAR);
alter table tb_query_view modify "creator" varchar (100 CHAR);
alter table tb_query_view modify "modifier" varchar (100 CHAR);
alter table tb_query_view modify "typeDesc" varchar (510 CHAR);
alter table tb_query_view modify "typeName" varchar (200 CHAR);
alter table tb_query_view modify "viewName" varchar (200 CHAR);
alter table tb_query_viewfield modify "creator" varchar (100 CHAR);
alter table tb_query_viewfield modify "dbField" varchar (100 CHAR);
alter table tb_query_viewfield modify "modifier" varchar (100 CHAR);
alter table tb_query_viewfield modify "pageField" varchar (510 CHAR);
