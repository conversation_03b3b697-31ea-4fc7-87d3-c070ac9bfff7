package com.sinoyd.lims.monitor.controller;

import com.sinoyd.lims.monitor.dto.customer.DtoTestPointVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.Property2PointService;
import com.sinoyd.lims.monitor.criteria.Property2PointCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoProperty2Point;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;
import java.util.Map;


/**
 * Property2Point服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Api(tags = "示例: Property2Point服务")
@RestController
@RequestMapping("api/monitor/property2Point")
public class Property2PointController extends BaseJpaController<DtoProperty2Point, String, Property2PointService> {


    /**
     * 分页动态条件查询Property2Point
     *
     * @param property2PointCriteria 条件参数
     * @return RestResponse<List < Property2Point>>
     */
    @ApiOperation(value = "分页动态条件查询Property2Point", notes = "分页动态条件查询Property2Point")
    @GetMapping
    public RestResponse<List<DtoProperty2Point>> findByPage(Property2PointCriteria property2PointCriteria) {
        PageBean<DtoProperty2Point> pageBean = super.getPageBean();
        RestResponse<List<DtoProperty2Point>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, property2PointCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询Property2Point
     *
     * @param id 主键id
     * @return RestResponse<DtoProperty2Point>
     */
    @ApiOperation(value = "按主键查询Property2Point", notes = "按主键查询Property2Point")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoProperty2Point> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoProperty2Point> restResponse = new RestResponse<>();
        DtoProperty2Point property2Point = service.findOne(id);
        restResponse.setData(property2Point);
        restResponse.setRestStatus(StringUtil.isNull(property2Point) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增Property2Point
     *
     * @param property2Point 实体列表
     * @return RestResponse<DtoProperty2Point>
     */
    @ApiOperation(value = "新增Property2Point", notes = "新增Property2Point")
    @PostMapping
    public RestResponse<DtoProperty2Point> create(@RequestBody @Validated DtoProperty2Point property2Point) {
        RestResponse<DtoProperty2Point> restResponse = new RestResponse<>();
        restResponse.setData(service.save(property2Point));
        return restResponse;
    }

    /**
     * 新增Property2Point
     *
     * @param property2Point 实体列表
     * @return RestResponse<DtoProperty2Point>
     */
    @ApiOperation(value = "修改Property2Point", notes = "修改Property2Point")
    @PutMapping
    public RestResponse<DtoProperty2Point> update(@RequestBody @Validated DtoProperty2Point property2Point) {
        RestResponse<DtoProperty2Point> restResponse = new RestResponse<>();
        restResponse.setData(service.update(property2Point));
        return restResponse;
    }

    /**
     * "根据id批量删除Property2Point
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Property2Point", notes = "根据id批量删除Property2Point")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 根据监测计划id查询关联点位信息
     *
     * @param propertyId 监测计划id
     * @return RestResponse<List < Property2Point>>
     */
    @ApiOperation(value = "根据监测计划id查询关联点位信息", notes = "根据监测计划id查询关联点位信息")
    @GetMapping("points/{propertyId}")
    public RestResponse<List<DtoProperty2Point>> findByPage(@PathVariable("propertyId") String propertyId) {
        RestResponse<List<DtoProperty2Point>> restResponse = new RestResponse<>();
        List<DtoProperty2Point> dataList = service.loadProperty2PointList(propertyId);
        restResponse.setData(dataList);
        restResponse.setCount(StringUtil.isEmpty(dataList) ? 0 : dataList.size());
        return restResponse;
    }

    /**
     * 批量新增测试项目
     *
     * @param parameter 参数
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "批量新增测试项目", notes = "批量新增测试项目")
    @PostMapping("/batch/test")
    public RestResponse<Boolean> batchAddTest(@RequestBody Map<String, Object> parameter) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.batchAddTest(parameter);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 批量删除测试项目
     *
     * @param parameter 参数
     * @return RestResponse<Boolean> 响应内容
     */
    @ApiOperation(value = "批量删除测试项目", notes = "批量删除测试项目")
    @DeleteMapping("/batch/test")
    public RestResponse<Boolean> batchDeleteTest(@RequestBody Map<String, List<String>> parameter) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.batchDeleteTest(parameter);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }

    /**
     * 批量修改批次及样品数
     * @param pointVo 集合
     * @return 响应内容
     */
    @ApiOperation(value = "批量修改批次样次", notes = "批量修改批次样次")
    @PostMapping("/batch/timeOrSample")
    public RestResponse<Boolean> batchTimesOrSampleOrder(@RequestBody DtoTestPointVo pointVo) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.batchTimesOrSampleOrder(pointVo);
        restResponse.setData(Boolean.TRUE);
        return restResponse;
    }
}