package com.sinoyd.lims.lim.service;

import com.sinoyd.lims.lim.dto.rcc.DtoCostRuleForEnt;
import com.sinoyd.frame.service.IBaseJpaService;


/**
 * CostRuleForEnt操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
public interface CostRuleForEntService extends IBaseJpaService<DtoCostRuleForEnt, String> {

    /**
     * 获取客户的费用规则
     *
     * @param entId 客户id
     * @return 对应客户的费用规则
     */
    DtoCostRuleForEnt getByEntId(String entId);
}