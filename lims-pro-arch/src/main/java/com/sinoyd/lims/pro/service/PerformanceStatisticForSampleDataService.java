package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForSampleData;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;

import java.util.List;


/**
 * PerformanceStatisticForSampleData操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
public interface PerformanceStatisticForSampleDataService extends IBaseJpaService<DtoPerformanceStatisticForSampleData, String> {

    /**
     * 创建采样项目的绩效
     *
     * @param record 送样单
     * @param principalContextUser  当前账号信息
     */
    void createSampleStatistic(DtoReceiveSampleRecord record,CurrentPrincipalUser principalContextUser);

    /**
     * 创建采样项目的绩效
     *
     * @param records 送样单ids
     * @param principalContextUser  当前账号信息
     */
    void createSampleStatistic(List<DtoReceiveSampleRecord> records,CurrentPrincipalUser principalContextUser);


    /**
     * 查询采样项目的数据
     *
     * @param baseCriteria 查询条件
     */
    void findByPage(PageBean<DtoPerformanceStatisticForSampleData> pb, BaseCriteria baseCriteria);

    /**
     * 获取总计行
     *
     * @param baseCriteria
     * @return
     */
    DtoPerformanceStatisticForSampleData findSumPerformanceStatistic(BaseCriteria baseCriteria);
}