package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DtoImportTest implements IExcelDataModel, IExcelModel {
    /**
     * 主键id
     */
    @Excel(name = "Id(必填)GUID", orderNum = "10", width = 50)
    private String id;

    /**
     * 分析方法名称
     */
    @Excel(name = "方法名称(必填)", orderNum = "20", width = 35)
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    @Excel(name = "标准号", orderNum = "30", width = 22)
    private String redCountryStandard;

    /**
     * 年份（预留）
     */
    @Excel(name = "标准年份", orderNum = "40", width = 17)
    private String redYearSn;

    /**
     * 分析项目名称
     */
    @Excel(name = "分析因子(必填)", orderNum = "50", width = 25)
    private String redAnalyzeItemName;

    /**
     * 样品类型（Guid）
     */
    @Excel(name = "检测类型(必填)", orderNum = "60", width = 20)
    private String sampleTypeId;

    /**
     * 计量单位（Guid）
     */
    @Excel(name = "量纲", orderNum = "70", width = 10)
    private String dimensionId;

    /**
     * 检出限
     */
    @Excel(name = "检出限", orderNum = "80", width = 10)
    private String examLimitValue;

    /**
     * 测定下限
     */
    @Excel(name = "测定下限", orderNum = "85", width = 10)
    private String lowerLimit;

    /**
     * 有效位数
     */
    @Excel(name = "最大有效", orderNum = "90", width = 10)
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    @Excel(name = "最大小数", orderNum = "100", width = 10)
    private Integer mostDecimal;

    /**
     * 样品有效期（h）
     */
    @Excel(name = "样品有效期（h）", orderNum = "110", width = 22)
    private BigDecimal validTime;

    /**
     * 有效期提示说明
     */
    @Excel(name = "有效期提示说明", orderNum = "115", width = 22)
    private String tips;

    /**
     * 是否现场数据
     */
    @Excel(name = "是否现场(必填)", orderNum = "120", width = 22)
    private String isCompleteField;

    /**
     * 是否采测分包
     */
    @Excel(name = "是否采测分包", orderNum = "130", width = 10)
    private String isOutsourcing;

    /**
     * 是否分析分包
     */
    @Excel(name = "是否分析分包", orderNum = "133", width = 10)
    private String isSamplingOut;

    /**
     * 是否做质控平行
     */
    @Excel(name = "是否平行", orderNum = "140", width = 10)
    private String isQCP;

    /**
     * 是否做质控空白
     */
    @Excel(name = "是否空白", orderNum = "150", width = 10)
    private String isQCB;

    /**
     * 是否做串联样
     */
    @Excel(name = "是否串联", orderNum = "160", width = 10)
    private String isSeries;

    /**
     * 是否填写仪器使用记录（预留）
     */
    @Excel(name = "是否填写仪器", orderNum = "170", width = 22)
    private String isInsUseRecord;

    /**
     * 默认人员
     */
    @Excel(name = "检测人员", orderNum = "180", width = 15)
    private String defaultPerson;

    /**
     * 检测能力
     */
    @Excel(name = "检测人员2", orderNum = "190", width = 22)
    private String abilityPerson;

    /**
     * 浓度计算公式
     */
    @Excel(name = "浓度计算公式", orderNum = "200", width = 30)
    private String testFormula;

    /**
     * 测得量计算公式
     */
    @Excel(name = "测得量计算公式", orderNum = "210", width = 30)
    private String measureFormula;


    /**
     * 关联原始记录单
     */
    @Excel(name = "关联原始记录单", orderNum = "250", width = 30)
    private String relatedReport;

    /**
     * 测试项目名称
     */
    private String testName;

    /**
     * 行号
     */
    private Integer rowNum;

    /**
     * 错误信息
     */
    private String errorMsg;

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }
}
