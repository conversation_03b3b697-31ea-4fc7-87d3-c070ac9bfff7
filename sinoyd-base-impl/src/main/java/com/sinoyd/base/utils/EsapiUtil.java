package com.sinoyd.base.utils;

import com.sinoyd.base.configuration.ESAPIConfiguration;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.owasp.esapi.ESAPI;

/**
 * 路径判断
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/07
 */
@Slf4j
public class EsapiUtil {

    static {
        // 通过静态方法注入,重写读取配置文件的类
        ESAPI.override(new ESAPIConfiguration());
    }

    /**
     * 判断是否正确路径
     *
     * @param inputPath 判断路径
     * @return 返回判断结果
     */
    public static Boolean isValidFilePath(String inputPath) {
        boolean isValid;
        //判断路径是否正确
        try {
            String canonical = ESAPI.validator().getValidInput("输入路径", inputPath, "DirectoryName", Integer.MAX_VALUE, false);
            if (!canonical.equals(inputPath)) {
                isValid = false;
            } else {
                isValid = true;
            }
        } catch (Exception var) {
            log.error("路径：" + inputPath);
            log.error(var.getMessage(), var);
            isValid = false;
        }
        return isValid;
    }

    /**
     * 判断是否正确路径
     *
     * @param inputPath 判断路径
     * @param fileName  文件名称
     * @return 返回判断结果
     */
    public static Boolean isValidFilePath(String inputPath, String fileName) {
        //去掉文件名
        if (StringUtils.isNotNull(fileName)) {
            String[] pathList = inputPath.split("/");
            int count = pathList.length;
            fileName = pathList[count - 1];
            if (!"/".equals(fileName.substring(0, 1))) {
                fileName = String.format("/%s", fileName);
            }
            inputPath = inputPath.replace(fileName, "");
        }
        return isValidFilePath(inputPath);
    }
}
