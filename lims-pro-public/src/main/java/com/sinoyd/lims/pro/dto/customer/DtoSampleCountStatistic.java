package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.Date;

/**
 * 项目数量统计的样品数据
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@Data
public class DtoSampleCountStatistic {

    /**
     * id
     */
    private String  id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 分析数据id
     */
    private String analyseDataId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 采样日期
     */
    private Date samplingTime;

    public DtoSampleCountStatistic(String id, String projectId, String analyseDataId, String sampleId, Date samplingTime) {
        this.id = id;
        this.projectId = projectId;
        this.analyseDataId = analyseDataId;
        this.sampleId = sampleId;
        this.samplingTime = samplingTime;
    }
}
