package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * VersionInfo查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VersionInfoCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 版本类别
     */
    private String verType;

    /**
     * 更新内容关键字
     */
    private String verValue;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        if (StringUtil.isNotEmpty(this.verType)) {
            condition.append(" and verType = :verType");
            values.put("verType", this.verType);
        }
        if (StringUtil.isNotEmpty(verValue)) {
            condition.append(" and verValue like :verValue ");
            values.put("verValue", "%" + this.verValue + "%");
        }
        return condition.toString();
    }
}