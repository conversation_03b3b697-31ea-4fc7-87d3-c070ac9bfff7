package com.sinoyd.lims.pro.controller;

import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ELogType;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.lims.DtoOAContract;
import com.sinoyd.lims.lim.entity.Contract;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.service.OAContractService;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 合同审批服务接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@RestController
@RequestMapping("/api/pro/oaContracts")
@Api(tags = "工作流: 合同审批服务")
public class OAContractController extends ExceptionHandlerController<OAContractService> {
    /**
     * 查询可发起审批合同
     * @param signBeginDate 签订开始日期
     * @param signEndDate 签订结束日期
     * @param key 关键字
     * @param salesManId 业务员Id
     * @param typeId 合同类型Id
     * @param pageNo 当前页
     * @param pageSize 每页大小
     * @return
     */
    @ApiOperation(value = "查询可发起审批合同", notes = "查询可发起审批合同")
    @GetMapping(path = "/canSponsor")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<List<Contract>> findCanSponsor(@RequestParam(name = "signBeginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date signBeginDate,
                                                           @RequestParam(name = "signEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date signEndDate, 
                                                           @RequestParam(name = "key", required = false) String key, 
                                                           @RequestParam(name = "salesManId", required = false) String salesManId, 
                                                           @RequestParam(name = "typeId", required = false) String typeId, 
                                                           @RequestParam(name = "pageNo") Integer pageNo, 
                                                           @RequestParam(name = "pageSize") Integer pageSize) {
        RestResponse<List<Contract>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        PageBean<Contract> page = new PageBean<>();
        page.setPageNo(pageNo);
        page.setRowsPerPage(pageSize);

        service.findCanSponsor(page, signBeginDate, signEndDate, key, salesManId, typeId);

        restResp.setData(page.getData());
        restResp.setCount(page.getPageTotal());

        return restResp;
    }

    /**
     * 添加审批
     * 
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加审批", notes = "添加审批启动流程")
    @PostMapping
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> startProcess(@RequestBody DtoOATaskCreate<DtoOAContract> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        String procInstId = service.startProcess(taskDto);
        restResp.setData(procInstId);
        return restResp;
    }

    /**
     * 保存为草稿
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "保存为草稿", notes = "保存为草稿")
    @PostMapping("/saveAsDraft")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> saveAsDraft(@RequestBody DtoOATaskCreate<DtoOAContract> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.saveAsDraft(taskDto));
        return restResp;
    }

    /**
     * 草稿保存
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "草稿保存", notes = "草稿保存")
    @PostMapping("/draftSave")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> draftSave(@RequestBody DtoOATaskCreate<DtoOAContract> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSave(taskDto));
        return restResp;
    }

    /**
     * 草稿提交
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加审批", notes = "添加审批启动流程")
    @PostMapping("/draftSubmit")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> draftSubmit(@RequestBody DtoOATaskCreate<DtoOAContract> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSubmit(taskDto));
        return restResp;
    }

    /**
     * 查询合同信息
     * 
     * @param taskId 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "查询合同信息", notes = "查询合同信息")
    @GetMapping(path = "/task/{taskId}")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<DtoOATaskDetail<DtoOAContract, String>> findDetailByTaskId(@PathVariable(name = "taskId") String taskId) {
        RestResponse<DtoOATaskDetail<DtoOAContract, String>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoOATaskDetail<DtoOAContract, String> detail = service.findOATaskDetail(taskId);
        restResp.setData(detail);

        return restResp;
    }
}
