package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * ReceiveSubSampleRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ReceiveSubSampleRecord")
 @Data
 public  class ReceiveSubSampleRecord implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  ReceiveSubSampleRecord() { 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 送样单ID
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("送样单ID")
	private String receiveId;
    
    /**
    * 登记项目ID
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("登记项目ID")
	private String projectId;
    
    /**
    * 领样单编号
    */
    @Column(length=20)
    @ApiModelProperty("领样单编号")
	private String code;
    
    /**
    * 领样单状态（枚举EnumReceiveSubRecordStatus：0.无状态 1.有实验室数据 2.有现场数据 4.已领取 8.已确认领样 16.已提交 32.已现场复核 64.已实验室复核 128.可确认 256.已确认）
   ）
    */
    @Column(length=50)
    @ColumnDefault("0")
    @ApiModelProperty("领样单状态（枚举EnumReceiveSubRecordStatus：0.无状态 1.有实验室数据 2.有现场数据 4.已领取 8.已确认领样 16.已提交 32.已现场复核 64.已实验室复核 128.可确认 256.已确认））")
	private String status;
    
    /**
    * 领样单状态（按位与，枚举EnumReceiveSubRecordStatus：0.无状态 1.有实验室数据 2.有现场数据 4.已领取 8.已确认领样 16.已提交 32.已现场复核 64.已实验室复核 128.可确认 256.已确认）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("领样单状态（按位与，枚举EnumReceiveSubRecordStatus：0.无状态 1.有实验室数据 2.有现场数据 4.已领取 8.已确认领样 16.已提交 32.已现场复核 64.已实验室复核 128.可确认 256.已确认）")
	private Integer subStatus;
    
    /**
    * 领样人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("领样人id")
	private String receivePersonId;
    
    /**
    * 领取人
    */
    @Column(length=50)
    @ApiModelProperty("领取人")
	private String receiveName;
    
    /**
    * 领取时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("领取时间")
	private Date receiveTime;
    
    /**
    * 现场数据复核人Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("现场数据复核人Id")
	private String checkerId;
    
    /**
    * 现场数据复核人
    */
    @Column(length=50)
    @ApiModelProperty("现场数据复核人")
	private String checkerName;
    
    /**
    * 现场数据复核时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("现场数据复核时间")
	private Date checkTime;
    
    /**
    * 现场数据审核人Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("现场数据审核人Id")
	private String auditorId;
    
    /**
    * 现场数据审核人名称
    */
    @Column(length=50)
    @ApiModelProperty("现场数据审核人名称")
	private String auditorName;
    
    /**
    * 现场数据审核时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("现场数据审核时间")
	private Date auditTime;
    
    /**
    * 退回意见（最新一个）
    */
    @Column(length=1000)
    @ApiModelProperty("退回意见（最新一个）")
	private String backOpinion;
    
    /**
    * 所属科室Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属科室Id")
	private String domainId;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;


    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;
 }