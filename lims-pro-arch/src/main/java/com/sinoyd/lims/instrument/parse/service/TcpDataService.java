package com.sinoyd.lims.instrument.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.instrument.parse.dto.DtoTcpData;

import java.util.List;

public interface TcpDataService extends IBaseJpaService<DtoTcpData, String> {

    /**
     * 根据任务编号，采样单号获取tcp数据
     *
     * @param taskId   任务编号
     * @param sampleId 采样单号
     * @return tcp数据
     */
    List<DtoTcpData> findByTaskIdAndSampleId(String taskId, String sampleId);
}
