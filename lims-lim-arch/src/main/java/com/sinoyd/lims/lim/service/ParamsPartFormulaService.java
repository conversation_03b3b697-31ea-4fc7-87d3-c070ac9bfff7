package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;

import java.util.List;


/**
 * ParamsPartFormula操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
public interface ParamsPartFormulaService extends IBaseJpaService<DtoParamsPartFormula, String> {


    /**
     * 根据公式id 获取相关的部分修约公式
     *
     * @param formulaId 公式id
     * @return 返回部分公式数据
     */
    List<DtoParamsPartFormula> findRedisByFormulaId(String formulaId);

    /**
     * 根据公式id获取相关修约公式
     * @param formulaIds 公式ids
     * @return 返回部分公式数据
     */
    List<DtoParamsPartFormula> findByFormulaIds(List<String> formulaIds);

    /**
     * 根据公式id获取相关修约公式
     * @param id 公式id
     * @return 返回部分公式数据
     */
    List<DtoParamsPartFormula> findByFormulaId(String id);
}