ALTER TABLE tb_pro_qualitycontrol ADD COLUMN rangeType int NOT NULL DEFAULT 10;
comment on COLUMN tb_pro_qualitycontrol.rangeType is '范围类型,关联枚举EnumConsumableRangeType';
ALTER TABLE tb_pro_qualitycontrol ADD COLUMN rangeLow varchar(50);
comment on COLUMN tb_pro_qualitycontrol.rangeLow is '范围低点';
ALTER TABLE tb_pro_qualitycontrol ADD COLUMN rangeHigh varchar(50);
comment on COLUMN tb_pro_qualitycontrol.rangeHigh is '范围高点';
ALTER TABLE tb_pro_qualitymanage ADD COLUMN rangeType int NOT NULL DEFAULT 10;
comment on COLUMN tb_pro_qualitymanage.rangeType is '范围类型,关联枚举EnumConsumableRangeType';
ALTER TABLE tb_pro_qualitymanage ADD COLUMN rangeLow varchar(50);
comment on COLUMN tb_pro_qualitymanage.rangeLow is '范围低点';
ALTER TABLE tb_pro_qualitymanage ADD COLUMN rangeHigh varchar(50);
comment on COLUMN tb_pro_qualitymanage.rangeHigh is '范围高点';