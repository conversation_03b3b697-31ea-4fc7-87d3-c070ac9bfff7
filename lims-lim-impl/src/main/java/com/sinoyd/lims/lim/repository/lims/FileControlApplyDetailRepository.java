package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 文件管理数据访问接口
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
public interface FileControlApplyDetailRepository extends IBaseJpaRepository<DtoFileControlApplyDetail, String> {
    /**
     * ControlCode
     *
     * @param controlCode 文件受控编号
     * @param id          文件id
     * @return 返回查询到的数量
     */
    @Query("select count(p) from DtoFileControlApplyDetail p where p.id <> :id and p.controlCode = :controlCode and p.isDeleted = 0")
    Integer getCountByControlCode(@Param("controlCode") String controlCode, @Param("id") String id);

    @Query("select p from DtoFileControlApplyDetail p where p.grantId = :grantId and p.isDeleted = 0")
    List<DtoFileControlApplyDetail> getListByGrantId(@Param("grantId") String grantId);

    /**
     * @param controlCode 文件受控编号
     * @return 返回查询到的数量
     */
    @Query("select count(p) from DtoFileControlApplyDetail p where  p.controlCode = :controlCode and p.isDeleted = 0")
    Integer getCountByControlCode(@Param("controlCode") String controlCode);


    /**
     * 修改文件状态
     *
     * @param id     主键id
     * @param status 状态数据
     * @return 返回修改行数
     */
    @Transactional
    @Modifying
    @Query("update DtoFileControlApplyDetail set  status=:status where  id=:id")
    Integer updateFileStatus(@Param("id") String id, @Param("status") Integer status);


    /**
     * 修改受控时间
     *
     * @param id          主键id
     * @param status      状态数据
     * @param controlDate 受控日期
     * @return 返回修改行数
     */
    @Transactional
    @Modifying
    @Query("update DtoFileControlApplyDetail set  status=:status,controlDate=:controlDate where  id=:id")
    Integer updateControlFileStatus(@Param("id") String id, @Param("status") Integer status,
                                    @Param("controlDate") Date controlDate);

    @Transactional
    @Modifying
    @Query("update DtoFileControlApplyDetail set status=:status,controlDate=:controlDate where  id in :ids")
    Integer updateControlFileStatusByIds(@Param("ids") List<String> ids, @Param("status") Integer status,
                                    @Param("controlDate") Date controlDate);


    /**
     * 修改废止时间
     *
     * @param id          主键id
     * @param status      状态数据
     * @param abolishDate 废止日期
     * @return 返回修改行数
     */
    @Transactional
    @Modifying
    @Query("update DtoFileControlApplyDetail set  status=:status,abolishDate=:abolishDate where  id=:id")
    Integer updateAbolishFileStatus(@Param("id") String id, @Param("status") Integer status,
                                    @Param("abolishDate") Date abolishDate);

    @Transactional
    @Modifying
    @Query("update DtoFileControlApplyDetail set  status=:status,abolishDate=:abolishDate where  id in :ids")
    Integer updateAbolishFileStatusByIds(@Param("ids") List<String> ids, @Param("status") Integer status,
                                    @Param("abolishDate") Date abolishDate);

    /**
     * 根据文件类型编码查询
     * @param fileTypes 文件类型编码集合
     * @return
     */
    List<DtoFileControlApplyDetail> findByFileTypeIn(List<String> fileTypes);
}