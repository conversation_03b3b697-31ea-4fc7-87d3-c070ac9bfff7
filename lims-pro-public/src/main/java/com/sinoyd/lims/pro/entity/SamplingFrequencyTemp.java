package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * SamplingFrequencyTemp实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "SamplingFrequencyTemp")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SamplingFrequencyTemp implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 点位方案变更id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("点位方案变更id")
    private String sampleFolderTempId;

    /**
     * 点位频次id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("点位频次id")
    private String samplingFrequencyId;

    /**
     * 周期
     */
    @Column(nullable=false)
    @ApiModelProperty("周期")
    private Integer periodCount;

    /**
     * 次数
     */
    @Column(nullable=false)
    @ApiModelProperty("次数")
    private Integer timePerPeriod;

    /**
     * 样品数
     */
    @Column(nullable=false)
    @ApiModelProperty("样品数")
    private Integer samplePerTime;

    /**
     * 操作类型
     */
    @Column(nullable=false)
    @ApiModelProperty("操作类型")
    private Integer operateType;

}
