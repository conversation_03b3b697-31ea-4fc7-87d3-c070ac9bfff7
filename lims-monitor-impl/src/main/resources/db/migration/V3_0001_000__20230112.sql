
DROP TABLE IF EXISTS TB_LIM_AppConfig;
CREATE TABLE TB_LIM_AppConfig
(
    id          varchar(50)  NOT NULL COMMENT '主键',
    name        varchar(100) NOT NULL COMMENT '应用名称',
    code        varchar(50)  NOT NULL COMMENT '应用编码',
    linkAddress varchar(1000) NULL COMMENT '链接地址',
    roleId      varchar(1000) NULL COMMENT '角色',
    status      bit(1) NULL DEFAULT 1 COMMENT '启用状态',
    orderNum    int NULL DEFAULT 0 COMMENT '排序值',
    remark      varchar(1000) NULL COMMENT '备注',
    orgId       varchar(50)  NOT NULL COMMENT '组织机构id',
    creator     varchar(50)  NOT NULL COMMENT '创建人',
    createDate  datetime     NOT NULL COMMENT '创建时间',
    domainId    varchar(50)  NOT NULL COMMENT '所属实验室',
    modifier    varchar(50)  NOT NULL COMMENT '修改人',
    modifyDate  datetime     NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT = 'app应用配置表'