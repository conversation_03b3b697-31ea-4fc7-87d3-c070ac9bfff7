package com.sinoyd.lims.lim.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.service.InstrumentUseRecord2SampleService;
import com.sinoyd.lims.lim.criteria.InstrumentUseRecord2SampleCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord2Sample;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * InstrumentUseRecord2Sample服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
 @Api(tags = "示例: InstrumentUseRecord2Sample服务")
 @RestController
 @RequestMapping("api/lim/instrumentUseRecord2Sample")
 public class InstrumentUseRecord2SampleController extends BaseJpaController<DtoInstrumentUseRecord2Sample, String,InstrumentUseRecord2SampleService> {


    /**
     * 分页动态条件查询InstrumentUseRecord2Sample
     * @param instrumentUseRecord2SampleCriteria 条件参数
     * @return RestResponse<List<InstrumentUseRecord2Sample>>
     */
     @ApiOperation(value = "分页动态条件查询InstrumentUseRecord2Sample", notes = "分页动态条件查询InstrumentUseRecord2Sample")
     @GetMapping
     public RestResponse<List<DtoInstrumentUseRecord2Sample>> findByPage(InstrumentUseRecord2SampleCriteria instrumentUseRecord2SampleCriteria) {
         PageBean<DtoInstrumentUseRecord2Sample> pageBean = super.getPageBean();
         RestResponse<List<DtoInstrumentUseRecord2Sample>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, instrumentUseRecord2SampleCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询InstrumentUseRecord2Sample
     * @param id 主键id
     * @return RestResponse<DtoInstrumentUseRecord2Sample>
     */
     @ApiOperation(value = "按主键查询InstrumentUseRecord2Sample", notes = "按主键查询InstrumentUseRecord2Sample")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoInstrumentUseRecord2Sample> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoInstrumentUseRecord2Sample> restResponse = new RestResponse<>();
         DtoInstrumentUseRecord2Sample instrumentUseRecord2Sample = service.findOne(id);
         restResponse.setData(instrumentUseRecord2Sample);
         restResponse.setRestStatus(StringUtil.isNull(instrumentUseRecord2Sample) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增InstrumentUseRecord2Sample
     * @param instrumentUseRecord2Sample 实体列表
     * @return RestResponse<DtoInstrumentUseRecord2Sample>
     */
     @ApiOperation(value = "新增InstrumentUseRecord2Sample", notes = "新增InstrumentUseRecord2Sample")
     @PostMapping
     public RestResponse<DtoInstrumentUseRecord2Sample> create(@RequestBody DtoInstrumentUseRecord2Sample instrumentUseRecord2Sample) {
         RestResponse<DtoInstrumentUseRecord2Sample> restResponse = new RestResponse<>();
         restResponse.setData(service.save(instrumentUseRecord2Sample));
         return restResponse;
      }

     /**
     * 新增InstrumentUseRecord2Sample
     * @param instrumentUseRecord2Sample 实体列表
     * @return RestResponse<DtoInstrumentUseRecord2Sample>
     */
     @ApiOperation(value = "修改InstrumentUseRecord2Sample", notes = "修改InstrumentUseRecord2Sample")
     @PutMapping
     public RestResponse<DtoInstrumentUseRecord2Sample> update(@RequestBody DtoInstrumentUseRecord2Sample instrumentUseRecord2Sample) {
         RestResponse<DtoInstrumentUseRecord2Sample> restResponse = new RestResponse<>();
         restResponse.setData(service.update(instrumentUseRecord2Sample));
         return restResponse;
      }

    /**
     * "根据id批量删除InstrumentUseRecord2Sample
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除InstrumentUseRecord2Sample", notes = "根据id批量删除InstrumentUseRecord2Sample")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }