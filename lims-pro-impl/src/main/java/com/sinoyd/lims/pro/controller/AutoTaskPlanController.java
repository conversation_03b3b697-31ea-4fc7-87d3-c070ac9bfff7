package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.pro.criteria.AutoTaskPlanCriteria;
import com.sinoyd.lims.pro.dto.DtoAutoTaskPlan;
import com.sinoyd.lims.pro.service.AutoTaskPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * AutoTaskPlan服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/04
 * @since V100R001
 */
@Api(tags = "示例: AutoTaskPlan服务")
@RestController
@RequestMapping("api/pro/autoTaskPlan")
public class AutoTaskPlanController extends BaseJpaController<DtoAutoTaskPlan, String, AutoTaskPlanService> {

    @ApiOperation(value = "自动任务下达列表", notes = "自动任务下达列表")
    @GetMapping
    public RestResponse<List<DtoAutoTaskPlan>> findByPage(AutoTaskPlanCriteria criteria) {
        RestResponse<List<DtoAutoTaskPlan>> response = new RestResponse<>();
        PageBean<DtoAutoTaskPlan> pb = super.getPageBean();
        service.findByPage(pb, criteria);
        response.setRestStatus(StringUtil.isEmpty(pb.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        response.setData(pb.getData());
        response.setCount(pb.getRowsCount());
        return response;
    }

    @ApiOperation(value = "新增自动任务下达", notes = "新增自动任务下达")
    @PostMapping
    public RestResponse<Map<String, Object>> save(@RequestBody @Validated DtoAutoTaskPlan plan) {
        RestResponse<Map<String, Object>> response = new RestResponse<>();
        response.setData(service.savePlan(plan));
        return response;
    }

    @ApiOperation(value = "更新自动任务下达", notes = "更新自动任务下达")
    @PutMapping
    public RestResponse<DtoAutoTaskPlan> update(@RequestBody @Validated DtoAutoTaskPlan plan) {
        RestResponse<DtoAutoTaskPlan> response = new RestResponse<>();
        response.setData(service.update(plan));
        return response;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("")
    public RestResponse<Integer> delete(@RequestBody List<String> ids) {
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.logicDeleteById(ids));
        return response;
    }

    @ApiOperation(value = "获取绑定监测计划信息", notes = "获取绑定监测计划信息")
    @GetMapping("/getPropertyByTaskId")
    public RestResponse<List<DtoFixedPointProperty>> getPropertyByTaskId(@RequestParam(name = "taskId") String taskId) {
        RestResponse<List<DtoFixedPointProperty>> response = new RestResponse<>();
        response.setData(service.getPropertyByTaskId(taskId));
        return response;
    }

    @ApiOperation(value = "任务自动生成接口", notes = "任务自动生成接口")
    @PostMapping("/autoCreateProject")
    public RestResponse<Void> autoCreateProject(@RequestBody DtoAutoTaskPlan autoTaskPlan) {
        RestResponse<Void> response = new RestResponse<>();
        service.autoCreateProject(autoTaskPlan.getId());
        return response;
    }

}
