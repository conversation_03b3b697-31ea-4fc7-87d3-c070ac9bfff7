package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * ExpressageInfo查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExpressageInfoCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 项目id
    */
    private String projectId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.projectId)) {
            condition.append(" and projectId = :projectId");
            values.put("projectId", this.projectId);
        }
        return condition.toString();
    }
}