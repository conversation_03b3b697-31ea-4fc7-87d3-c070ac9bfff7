package com.sinoyd.base.service;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;

import java.util.List;
import java.util.Map;

public interface QualityDivationService {
    /**
     * 质控限值计算
     *
     * @param controlLimit 质控限值配置
     * @param valueList    数据结果
     * @return 计算结果
     */
    void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map);
}
