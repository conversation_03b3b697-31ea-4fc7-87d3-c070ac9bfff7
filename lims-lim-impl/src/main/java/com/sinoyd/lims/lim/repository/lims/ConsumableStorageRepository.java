package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoConsumableStorage;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 消耗品入库仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
public interface ConsumableStorageRepository extends IBaseJpaPhysicalDeleteRepository<DtoConsumableStorage, String> {
    /**
     * 根据消耗品标识查询
     *
     * @param consumableIds 消耗品标识集合
     * @return 入库列表
     */
    List<DtoConsumableStorage> findByConsumableIdIn(List<String> consumableIds);

    /**
     * 根据采购明细id查询
     *
     * @param purchaseDetailId 消耗品采购明细标识Id
     * @return 入库列表
     */
    DtoConsumableStorage findByPurchaseDetailId(String purchaseDetailId);

    /**
     * 修改入库日期
     *
     * @param storageTime 入库日期
     * @param id          主键id
     * @return 入库列表
     */
    @Transactional
    @Modifying
    @Query("update DtoConsumableStorage p set p.storageTime = :storageTime where p.id = :id")
    Integer updateStorageTimeById(@Param("storageTime") Date storageTime,
                                  @Param("id") String id);
}