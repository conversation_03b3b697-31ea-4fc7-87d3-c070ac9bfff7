package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 标准方法查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StandardMethodCriteria extends BaseCriteria implements Serializable {

    /**
     * 配对状态
     */
    private Integer status = EnumLIM.MethodMatchState.所有.getValue();

    /**
     * 标准编号、标准名称
     */
    private String key;

    /**
     * 项目名称
     */
    private String itemName;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (countryStandard like :key or methodName like :key) ");
            values.put("key", "%" + this.key + "%");
        }

        if (StringUtil.isNotEmpty(this.itemName)) {
            condition.append(" and itemName like :itemName ");
            values.put("itemName", "%" + this.itemName + "%");
        }
        return condition.toString();
    }
}