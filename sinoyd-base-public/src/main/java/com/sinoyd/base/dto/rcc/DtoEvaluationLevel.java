package com.sinoyd.base.dto.rcc;

import com.sinoyd.base.entity.EvaluationLevel;

import java.util.List;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoEvaluationLevel实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_EvaluationLevel")
 @Data
 @DynamicInsert
 public  class DtoEvaluationLevel extends EvaluationLevel {
   private static final long serialVersionUID = 1L;
   /**
    * 评价值
    */
    @Transient
    private List<DtoEvaluationValue> evaluationValue;

    /**
     * 评价等级全称
     */
    @Transient
    private String levelFullName;
 }