package com.sinoyd.lims.lim.service;

import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 公式导入模板接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/7
 * @since V100R001
 */
public interface DownLoadFormulaTemplateService {
    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    void downLoadTemplate(HttpServletResponse response, Map<String, String> sheetNames, String fileName);

    /**
     * 下载模板
     *
     * @param response   响应流
     */
    void downLoadTemplateUpdate(HttpServletResponse response);

    /**
     * 公式数据下拉框处理
     *
     * @param workBook   工作单
     * @param col1       列1
     * @param col2       列2
     * @param col3       列3
     * @param sheetIndex sheet页索引
     */
    void processDropList(Workbook workBook, int col1,int col2,int col3, int sheetIndex);
}