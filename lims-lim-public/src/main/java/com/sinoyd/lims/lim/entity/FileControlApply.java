package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * FileControlApply实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "FileControlApply")
@Data
@EntityListeners(AuditingEntityListener.class)
public class FileControlApply implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public FileControlApply() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 受控申请编号，修订申请编号，废止申请编号
     */
    @Column(length = 100)
    @ApiModelProperty("受控申请编号，修订申请编号，废止申请编号")
    @Length(message = "受控申请编号{validation.message.length}", max = 100)
    private String controlCode;

    /**
     * 申请人id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("申请人id（Guid）")
    private String applyPersonId;

    /**
     * 申请描述
     */
    @Column(length = 1000)
    @ApiModelProperty("申请描述")
    @Length(message = "申请描述{validation.message.length}", max = 1000)
    private String applyDesc;

    /**
     * 申请日期
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("申请日期")
    private Date applyDate;

    /**
     * 状态
     */
    @Column(length = 50)
    @ApiModelProperty("状态")
    @Length(message = "状态{validation.message.length}", max = 50)
    private String status;

    /**
     * 受控日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("受控日期")
    private Date controlDate;

    /**
     * 修订日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("修订日期")
    private Date reviseDate;

    /**
     * 修订内容
     */
    @Column(length = 1000)
    @ApiModelProperty("修订内容")
    @Length(message = "修订内容{validation.message.length}", max = 1000)
    private String reviseContent;

    /**
     * 废止的原因
     */
    @Column(length = 1000)
    @ApiModelProperty("废止的原因")
    @Length(message = "废止的原因{validation.message.length}", max = 1000)
    private String abolishReason;

    /**
     * 废止的日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("废止的日期")
    private Date abolishDate;

    /**
     * 申请类型（枚举EnumFileControlApplyType：1：受控申请，2：修订申请，3：废止申请）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("申请类型（枚举EnumFileControlApplyType：1：受控申请，2：修订申请，3：废止申请）")
    @NotBlank(message = "申请类型{validation.message.blank}")
    private Integer applyType;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}