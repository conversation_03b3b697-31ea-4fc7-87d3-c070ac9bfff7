package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.lim.criteria.AnalyzeMethodReagentConfigCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeMethodReagentConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.WorkSheetReagentService;
import com.sinoyd.lims.pro.criteria.WorkSheetReagentCriteria;
import com.sinoyd.lims.pro.dto.DtoWorkSheetReagent;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * WorkSheetReagent服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: WorkSheetReagent服务")
 @RestController
 @RequestMapping("api/pro/workSheetReagent")
 public class WorkSheetReagentController extends BaseJpaController<DtoWorkSheetReagent, String,WorkSheetReagentService> {


    /**
     * 分页动态条件查询WorkSheetReagent
     *
     * @param workSheetReagentCriteria 条件参数
     * @return RestResponse<List < WorkSheetReagent>>
     */
    @ApiOperation(value = "分页动态条件查询WorkSheetReagent", notes = "分页动态条件查询WorkSheetReagent")
    @GetMapping
    public RestResponse<List<DtoWorkSheetReagent>> findByPage(WorkSheetReagentCriteria workSheetReagentCriteria) {
        PageBean<DtoWorkSheetReagent> pageBean = super.getPageBean();
        RestResponse<List<DtoWorkSheetReagent>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, workSheetReagentCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 分页动态条件查询试剂配置
     *
     * @param analyzeMethodReagentConfigCriteria 条件参数
     * @return RestResponse<List < WorkSheetReagent>>
     */
    @ApiOperation(value = "分页动态条件查询试剂配置", notes = "分页动态条件查询试剂配置")
    @GetMapping("/config")
    public RestResponse<List<DtoAnalyzeMethodReagentConfig>> findConfigByPage(AnalyzeMethodReagentConfigCriteria analyzeMethodReagentConfigCriteria) {
        PageBean<DtoWorkSheetReagent> pageBean = super.getPageBean();
        RestResponse<List<DtoAnalyzeMethodReagentConfig>> restResponse = new RestResponse<>();
        PageBean<DtoAnalyzeMethodReagentConfig> pb = service.findConfigByPage(pageBean, analyzeMethodReagentConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pb.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pb.getData());
        restResponse.setCount(pb.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询WorkSheetReagent
     *
     * @param id 主键id
     * @return RestResponse<DtoWorkSheetReagent>
     */
    @ApiOperation(value = "按主键查询WorkSheetReagent", notes = "按主键查询WorkSheetReagent")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoWorkSheetReagent> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoWorkSheetReagent> restResponse = new RestResponse<>();
        DtoWorkSheetReagent workSheetReagent = service.findOne(id);
        restResponse.setData(workSheetReagent);
        restResponse.setRestStatus(StringUtil.isNull(workSheetReagent) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增WorkSheetReagent
     *
     * @param workSheetReagent 实体列表
     * @return RestResponse<DtoWorkSheetReagent>
     */
    @ApiOperation(value = "新增WorkSheetReagent", notes = "新增WorkSheetReagent")
    @PostMapping
    public RestResponse<DtoWorkSheetReagent> create(@RequestBody @Validated DtoWorkSheetReagent workSheetReagent) {
        RestResponse<DtoWorkSheetReagent> restResponse = new RestResponse<>();
        restResponse.setData(service.save(workSheetReagent));
        return restResponse;
    }

    /**
     * 新增WorkSheetReagent
     *
     * @param workSheetReagent 实体列表
     * @return RestResponse<DtoWorkSheetReagent>
     */
    @ApiOperation(value = "修改WorkSheetReagent", notes = "修改WorkSheetReagent")
    @PutMapping
    public RestResponse<DtoWorkSheetReagent> update(@RequestBody @Validated DtoWorkSheetReagent workSheetReagent) {
        RestResponse<DtoWorkSheetReagent> restResponse = new RestResponse<>();
        restResponse.setData(service.update(workSheetReagent));
        return restResponse;
    }

    /**
     * 挑选试剂配置记录
     *
     * @param dto 实体
     * @return RestResponse<DtoWorkSheetReagent>
     */
    @ApiOperation(value = "挑选试剂配置记录", notes = "挑选试剂配置记录")
    @PostMapping("/relate")
    public RestResponse<DtoWorkSheetReagent> relate(@RequestBody DtoWorkSheetReagent dto) {
        RestResponse<DtoWorkSheetReagent> restResponse = new RestResponse<>();
        restResponse.setData(service.relate(dto.getReagentConfigId(), dto.getWorksheetFolderId()));
        return restResponse;
    }

    /**
     * 批量挑选试剂配置记录
     *
     * @param dto  数据载体
     * @return  RestResponse<List<DtoWorkSheetReagent>>
     */
    @ApiOperation(value = "批量挑选试剂配置记录", notes = "批量挑选试剂配置记录")
    @PostMapping("/batchRelate")
    public RestResponse<List<DtoWorkSheetReagent>> batchRelate(@RequestBody DtoWorkSheetReagent dto) {
        RestResponse<List<DtoWorkSheetReagent>> restResponse = new RestResponse<>();
        restResponse.setData(service.batchRelate(dto.getReagentConfigIds(), dto.getWorksheetFolderId()));
        return restResponse;
    }

    /**
     * "根据id批量删除WorkSheetReagent
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除WorkSheetReagent", notes = "根据id批量删除WorkSheetReagent")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids, @RequestParam(name = "userId", required = false) String userId) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids, userId);
        restResp.setCount(count);
        return restResp;
    }
}