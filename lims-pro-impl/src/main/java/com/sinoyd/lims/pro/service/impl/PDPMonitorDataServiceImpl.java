package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoPollutionDischargeSync;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.vo.HtmlMonitorDataVO;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.lims.PollutionDischargeSyncRepository;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.base.service.PollutionDischargeSyncService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.strategy.context.HtmlParseContext;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import com.sinoyd.lims.monitor.enums.EnumMonitor;
import com.sinoyd.lims.monitor.service.FixedPoint2TestService;
import com.sinoyd.lims.monitor.service.FixedpointService;
import com.sinoyd.lims.monitor.service.StationService;
import com.sinoyd.lims.monitor.strategy.context.PDPRequestContext;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.PDPMonitorDataService;
import io.swagger.models.HttpMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * （PDP:PollutantDischargePermit）排污许可证自行检测数据服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/23
 * @since V100R001
 */
@Service
public class PDPMonitorDataServiceImpl implements PDPMonitorDataService {

    private HtmlParseContext htmlParseContext;

    private PDPRequestContext pdpRequestContext;

    private EnterpriseService enterpriseService;

    private EnterpriseRepository enterpriseRepository;

    private TestService testService;

    private SampleTypeService sampleTypeService;

    private FixedpointService fixedpointService;

    private FixedPoint2TestService fixedPoint2TestService;

    private CodeService codeService;

    private StationService stationService;

    private PollutionDischargeSyncService pollutionDischargeSyncService;

    private PollutionDischargeSyncRepository pollutionDischargeSyncRepository;

    private final String EMPTY_STR = "";

    @Override
    public List<HtmlMonitorDataVO> findPointMonitorData(String enterpriseId) {
        List<HtmlMonitorDataVO> monitorDataList = new ArrayList<>();
        //根据企业id查询到排污许可证信息
        DtoEnterprise enterprise = enterpriseService.findOne(enterpriseId);
        if (StringUtil.isNotEmpty(enterprise.getPollutionDischargeCode()) && enterprise.getIsSyncPollutionDischarge()) {
            //获取排污许可证自行监测点位数据
            DtoPollutionDischargeSync syncData = pollutionDischargeSyncRepository.findByEnterpriseId(enterpriseId);
            //获取自行监测点位数据
            String html = StringUtil.isNotNull(syncData) ? syncData.getDataContent() : EMPTY_STR;
            //解析html数据
            monitorDataList = htmlParseContext
                    .parse(html, EnumBase.EnumHtmlParseType.TABLE.getValue(), HtmlMonitorDataVO.class);
        }
        //匹配本系统测试项目
        matchTestProject(monitorDataList);
        //返回匹配后的数据
        return monitorDataList;
    }

    @Override
    @Transactional
    public void syncPointMonitorData(Collection<String> enterpriseIds) {
        //查询到所选的污染源数据
        List<DtoEnterprise> enterpriseList = enterpriseService.findAll(enterpriseIds);
        //过滤掉没有排污许可证编号的污染源数据并请求数据
        List<DtoPollutionDischargeSync> syncList = doBatchRequest(enterpriseList.stream()
                .filter(e -> StringUtil.isNotEmpty(e.getPollutionDischargeCode()))
                .collect(Collectors.toList()));
        //保存同步相关数据
        pollutionDischargeSyncRepository.save(syncList);
        //更新企业同步状态
        updateEntSyncStatus(enterpriseList, syncList);
    }

    @Override
    @Transactional
    public void sync(Collection<HtmlMonitorDataVO> dataList, String enterpriseId) {
        List<DtoFixedpoint> fixedPointList = new ArrayList<>();
        List<DtoFixedPoint2Test> point2TestList = new ArrayList<>();
        //获取所有的污染源点位类型
        List<DtoCode> pollutionPointType = codeService.findCodes("LIM_PollutionPointType");
        //获取到所有测站数据
        List<DtoStation> stationList = stationService.findAll();
        //企业信息
        DtoEnterprise enterprise = enterpriseService.findOne(enterpriseId);
        //无法获取企业信息时不进行点位同步
        if (enterprise != null) {
            //获取当前企业下的所有点位
            List<DtoFixedpoint> existsPointList = fixedpointService.findByEnterpriseId(enterpriseId);
            //查询已经存在点位下的关联测试项目数据
            List<String> existsPointIds = existsPointList.stream().map(DtoFixedpoint::getId).collect(Collectors.toList());
            Map<String, List<DtoFixedPoint2Test>> existsPointTestGroup = fixedPoint2TestService.findByFixedPointIdIn(existsPointIds).stream().collect(Collectors.groupingBy(DtoFixedPoint2Test::getFixedPointId));
            //获取[检测类型id:点位编号:点位名称]数据，进行点位数据添加
            Map<String, List<HtmlMonitorDataVO>> pointGroup = dataList.stream()
                    .collect(Collectors.groupingBy(p -> p.getSampleTypeId() + ":" + p.getPointCode() + ":" + p.getPointName()));
            for (Map.Entry<String, List<HtmlMonitorDataVO>> entry : pointGroup.entrySet()) {
                //获取到当前点位下的测试项目
                if (StringUtil.isNotEmpty(entry.getValue())) {
                    //获取到点位下已匹配上本系统测试项目的数据
                    List<HtmlMonitorDataVO> matchHtmlData = entry.getValue().stream().filter(p -> StringUtil.isNotEmpty(p.getTestId())).collect(Collectors.toList());
                    //只添加匹配上测试项目数据的点位数据
                    if (StringUtil.isNotEmpty(matchHtmlData)) {
                        //匹配已有点位
                        DtoFixedpoint fixedPoint = existsPointList
                                .stream().filter(p -> entry.getKey().equals(p.getSampleTypeId() + ":" + p.getPointCode() + ":" + p.getPointName())).findFirst()
                                .orElse(createMatchPoint(matchHtmlData.get(0), pollutionPointType, enterprise, stationList));
                        //获取点位下已存在的关联测试项目数据
                        List<DtoFixedPoint2Test> existsTests = existsPointTestGroup.getOrDefault(fixedPoint.getId(), new ArrayList<>());
                        //循环处理测试项目数据
                        point2TestList.addAll(createMatchPoint2Test(fixedPoint, matchHtmlData, existsTests));
                        //添加点位数据
                        fixedPointList.add(fixedPoint);
                    }
                }
            }
            if (StringUtil.isNotEmpty(fixedPointList)) {
                //处理排序
                fixedPointList.sort(Comparator.comparing(DtoFixedpoint::getPointCode, Comparator.reverseOrder())
                        .thenComparing(DtoFixedpoint::getPointName, Comparator.reverseOrder()));
                fixedpointService.save(fixedPointList);
            }
            if (StringUtil.isNotEmpty(point2TestList)) {
                fixedPoint2TestService.save(point2TestList);
            }
        }
    }


    /**
     * 批量请求排污许可证自行监测数据并创建数据
     *
     * @param enterpriseList 企业数据
     */
    @Override
    public List<DtoPollutionDischargeSync> doBatchRequest(Collection<DtoEnterprise> enterpriseList) {
        //根据当前企业信息查询相关同步数据
        List<DtoPollutionDischargeSync> syncList = pollutionDischargeSyncService
                .findByEnterpriseIdIn(enterpriseList.stream().map(DtoEnterprise::getId).collect(Collectors.toList()));
        //需要更新保存的同步数据
        List<DtoPollutionDischargeSync> updateSyncList = new ArrayList<>();
        Map<String, String> enterpriseSyncMap = new HashMap<>();
        //批量请求爬取数据
        enterpriseList.forEach(e -> enterpriseSyncMap.put(e.getId(), pdpRequestContext.getHtml(HttpMethod.GET.name(), new CopyOnWriteArrayList<NameValuePair>() {{
            add(new NameValuePair("code", e.getPollutionDischargeCode()));
        }}, EnumMonitor.EnumPollutantDisChargePermitType.自行监测数据.getValue())));
        //判断是否有同步成功的数据
        for (DtoEnterprise enterprise : enterpriseList) {
            String html = enterpriseSyncMap.get(enterprise.getId());
            Boolean isSuccess = StringUtil.isNotEmpty(html);
            Optional<DtoPollutionDischargeSync> existsSyncDataOp = syncList.stream().filter(s -> s.getEnterpriseId().equals(enterprise.getId()))
                    .findFirst();
            if (existsSyncDataOp.isPresent()) {
                DtoPollutionDischargeSync existsSyncData = existsSyncDataOp.get();
                existsSyncData.setIsSuccess(isSuccess);
                existsSyncData.setRequestTime(new Date());
                existsSyncData.setDataContent(html);
                updateSyncList.add(existsSyncData);
            } else {
                updateSyncList.add(new DtoPollutionDischargeSync(isSuccess, enterprise.getId(), html));
            }
        }
        return updateSyncList;
    }

    /**
     * 更新企业同步状态
     *
     * @param enterprises 企业集合
     * @param syncList    同步信息集合
     */
    @Override
    @Transactional
    public void updateEntSyncStatus(Collection<DtoEnterprise> enterprises, List<DtoPollutionDischargeSync> syncList) {
        if (StringUtil.isNotEmpty(enterprises)) {
            enterprises.forEach(e ->
                    e.setIsSyncPollutionDischarge(
                            syncList.stream().anyMatch(p -> p.getEnterpriseId().equals(e.getId())
                                    && p.getIsSuccess())
                    )
            );
            enterpriseRepository.save(enterprises);
        }
    }

    /**
     * 创建匹配点位关联测试项目数据
     *
     * @param fixedPoint      已匹配创建的点位数据
     * @param monitorDataList 自行监测数据
     * @return 关联测试项目数据
     */
    private List<DtoFixedPoint2Test> createMatchPoint2Test(DtoFixedpoint fixedPoint, List<HtmlMonitorDataVO> monitorDataList, List<DtoFixedPoint2Test> existsTests) {
        List<DtoFixedPoint2Test> point2TestList = new ArrayList<>();
        for (HtmlMonitorDataVO monitorData : monitorDataList) {
            if (StringUtil.isNotEmpty(monitorData.getTestId())) {
                DtoFixedPoint2Test point2Test = existsTests.stream()
                        .filter(p -> monitorData.getTestId().equals(p.getTestId())).findFirst().orElse(new DtoFixedPoint2Test());
                point2Test.setFixedPointId(fixedPoint.getId());
                point2Test.setTestId(monitorData.getTestId());
                point2Test.setTimesOrder(monitorData.getBatchCount());
                point2Test.setSamplePeriod(monitorData.getSampleBatchCount());
                point2Test.setProjectInterval(monitorData.getProjectInterval());
                point2TestList.add(point2Test);
            }
        }
        return point2TestList;
    }

    /**
     * 创建匹配点位数据
     *
     * @param monitorPointData   自行监测数据
     * @param pollutionPointType 点位类型常量数据
     * @param enterprise         当前处理的企业数据
     * @param stationList        系统测站数据
     * @return 匹配点位数据
     */
    private DtoFixedpoint createMatchPoint(HtmlMonitorDataVO monitorPointData, List<DtoCode> pollutionPointType,
                                           DtoEnterprise enterprise, List<DtoStation> stationList) {
        DtoFixedpoint fixedPoint = new DtoFixedpoint();
        if (StringUtil.isNotEmpty(monitorPointData.getSampleTypeId())) {
            fixedPoint.setPointType(EnumMonitor.EnumPointType.污染源.getValue());
            fixedPoint.setSampleTypeId(monitorPointData.getSampleTypeId());
            fixedPoint.setPointCode(monitorPointData.getPointCode());
            fixedPoint.setPointName(monitorPointData.getPointName());
            //获取点位类型
            String folderType = pollutionPointType.stream()
                    .filter(p -> p.getDictName().equals(monitorPointData.getSampleType()))
                    .findFirst().map(DtoCode::getDictCode).orElse(EMPTY_STR);
            fixedPoint.setFolderType(folderType);
            //获取企业上地区与测站地区相同的测站，如果有则获取地区相同的测站，没有则获取第一个测站
            DtoStation station = stationList.stream().filter(s -> s.getStaddress().equals(enterprise.getAreaId()))
                    .findFirst().orElse(stationList.get(0));
            fixedPoint.setStationId(station == null ? EMPTY_STR : station.getId());
            fixedPoint.setStationName(station == null ? EMPTY_STR : station.getStname());
            fixedPoint.setCycleOrder(1);
            fixedPoint.setTimesOrder(1);
            fixedPoint.setIsEnabled(true);
            fixedPoint.setEnterpriseId(enterprise.getId());
            fixedPoint.setEvaluationId(EMPTY_STR);
            fixedPoint.setEvaluationLevelId(EMPTY_STR);
        }
        return fixedPoint;
    }

    /**
     * 匹配本系统测试项目
     *
     * @param monitorDataList 匹配前数据
     */
    private void matchTestProject(List<HtmlMonitorDataVO> monitorDataList) {
        if (StringUtil.isNotEmpty(monitorDataList)) {
            //查询检测类型匹配关系
            List<DtoCode> typeMatchCodes = codeService.findCodes("LIM_PollutantDischargePermitTypeMatch");
            if (StringUtil.isEmpty(typeMatchCodes)) {
                throw new BaseException("请在常量中配置排污许可证平台检测类型匹配关系！");
            }
            //获取到所有的分析项目集合以及分析方法集合
            //分析项目
            Set<String> analyzeItemNames = monitorDataList.stream().map(HtmlMonitorDataVO::getAnalyzeItemName)
                    .filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
            //获取所有检测类型小类
            List<DtoSampleType> sampleTypes = sampleTypeService.findSampleType();
            //根据分析项目、分析方法查询到可匹配的测试项目数据
            List<DtoTest> testList = testService.findByAnalyzeItemNames(analyzeItemNames);
            //完全匹配测试项目
            for (HtmlMonitorDataVO monitorPointData : monitorDataList) {
                //匹配本系统检测类型名称
                String sampleTypeName = typeMatchCodes.stream().filter(p -> p.getDictName().equals(monitorPointData.getSampleType()))
                        .findFirst().map(DtoCode::getExtendS1).orElse(EMPTY_STR);
                Optional<DtoSampleType> sampleType = sampleTypes.stream().filter(t -> sampleTypeName.equals(t.getTypeName())).findFirst();
                if (sampleType.isPresent()) {
                    monitorPointData.setSampleTypeId(sampleType.get().getId())
                            .setBigSampleTypeId(sampleType.get().getParentId())
                            .setSampleTypeName(sampleType.get().getTypeName());
                    Optional<DtoTest> testOp = testList.stream().filter(test -> matchAnalyzeMethod(monitorPointData, test)
                            && sampleType.get().getParentId().equals(test.getSampleTypeId())).findFirst();
                    testOp.ifPresent(test -> monitorPointData.setTestId(test.getId())
                            .setSampleBatchCount(test.getSamplePeriod())
                            .setBatchCount(test.getTimesOrder())
                            .setProjectInterval(matchProjectInterval(monitorPointData.getMonitoringFrequency())));
                }
            }
        }
    }

    /**
     * 匹配分析方法
     *
     * @param monitorPointData 自行监测数据
     * @param test             本站测试项目
     * @return 是否匹配成功
     */
    private boolean matchAnalyzeMethod(HtmlMonitorDataVO monitorPointData, DtoTest test) {
        String monitorMethod = monitorPointData.getAnalyzeMethodName();
        //排污许可证自行监测数据可能的分析方法格式
        //示例：	固定污染源排放烟气黑度的测定 林格曼烟气黑度图法HJ/T 398-2007
        String methodFormat1 = test.getRedAnalyzeMethodName() + test.getRedCountryStandard();
        //示例：《固定污染源废气 苯系物的测定 气袋采样/直接进样-气相色谱法》（HJ 1261-2022）
        String methodFormat2 = "《" + test.getRedAnalyzeMethodName() + "》" + "（" + test.getRedCountryStandard() + "）";
        //示例：《固定污染源废气 苯系物的测定 气袋采样/直接进样-气相色谱法》HJ 1261-2022
        String methodFormat3 = "《" + test.getRedAnalyzeMethodName() + "》" + test.getRedCountryStandard();
        //示例：固定污染源废气油烟和油雾的测定 红外分光光度法（HJ1077-2019）
        String methodFormat4 = test.getRedAnalyzeMethodName() + "（" + test.getRedCountryStandard() + "）";
        //示例：	固定污染源排放烟气黑度的测定 林格曼烟气黑度图法 HJ/T 398-2007
        String methodFormat5 = test.getRedAnalyzeMethodName() + " " + test.getRedCountryStandard();
        return monitorPointData.getAnalyzeItemName().equals(test.getRedAnalyzeItemName())
                && (monitorMethod.equals(methodFormat1)
                || monitorMethod.equals(methodFormat2)
                || monitorMethod.equals(methodFormat3)
                || monitorMethod.equals(methodFormat4)
                || monitorMethod.equals(methodFormat5));

    }

    /**
     * 获取监测间隔
     *
     * @param pdpInterval 排污许可证自行监测间隔
     * @return 匹配后的监测间隔
     */
    private String matchProjectInterval(String pdpInterval) {
        EnumPRO.EnumDisassembleType disassemble = EnumPRO.EnumDisassembleType.getByPollutantDischargePermitType(pdpInterval);
        if (disassemble != null) {
            return disassemble.name();
        }
        return EMPTY_STR;
    }


    @Autowired
    public void setHtmlParseContext(HtmlParseContext htmlParseContext) {
        this.htmlParseContext = htmlParseContext;
    }

    @Autowired
    public void setPdpRequestContext(PDPRequestContext pdpRequestContext) {
        this.pdpRequestContext = pdpRequestContext;
    }

    @Autowired
    @Lazy
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    @Lazy
    public void setFixedpointService(FixedpointService fixedpointService) {
        this.fixedpointService = fixedpointService;
    }

    @Autowired
    @Lazy
    public void setFixedPoint2TestService(FixedPoint2TestService fixedPoint2TestService) {
        this.fixedPoint2TestService = fixedPoint2TestService;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setStationService(StationService stationService) {
        this.stationService = stationService;
    }

    @Autowired
    public void setPollutionDischargeSyncService(PollutionDischargeSyncService pollutionDischargeSyncService) {
        this.pollutionDischargeSyncService = pollutionDischargeSyncService;
    }

    @Autowired
    public void setPollutionDischargeSyncRepository(PollutionDischargeSyncRepository pollutionDischargeSyncRepository) {
        this.pollutionDischargeSyncRepository = pollutionDischargeSyncRepository;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }
}
