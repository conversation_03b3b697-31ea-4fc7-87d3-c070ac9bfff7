-- ----------------------------------------------
-- -------- 归属于RCC部分的表相关初始化数据脚本 -------
-- --------- TB_LIM_ReportApply ----------------
-- ----------------------------------------------

INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('00a120da-67eb-4204-955b-2523ac628adf', '711be950-f96f-463a-aa59-1f66355ba1f2', 'StandardMaterialManage', '标准物质管理', 'StandardMaterial', '标准物质领用记录', 0, 0, 1, '', '标准物质管理:标准物质管理', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('044747f2-62ac-4bf7-82ed-fd91eee18b0f', '9edbedfd-e8fb-427b-9fbd-97c082282d07', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('0552b4ec-c3bc-4ab0-9d58-dc05ab188e4c', 'b73a4a93-400c-4401-a046-0df2dc61cb70', 'ConsumableManage', '消耗品管理', 'ConsumableCRK', '消耗性材料出入库登记表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('06a1cf51-49e9-4bbb-b280-6d05b9c2d0e0', '1b87da15-8ae4-4e64-9f29-62a5d313fdcb', 'ReportEdit', '报告编制', 'ZFZDWasteWater', '执法监测（重点源废水）监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('0b0d1b86-cb14-4f36-a84d-1d70259f5077', 'c3498b50-f2cf-4d7f-b001-0b862d2c3311', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('0bdcde1a-fae2-45e7-9ffe-8f9b23bb8277', '0df57004-fbec-44c6-966e-da81d184bc42', 'PrepareSample', '采样准备', '采样单', '大气环境采样和交接记录单', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('1058845f-17c8-4511-93d1-8e5ce098a182', '486f9342-7c83-4afe-a98c-f7d36ecdefcf', 'ProjectRegister', '项目登记', 'DetectionTask', '生成任务单', 0, 0, 1, '', '项目登记:项目登记', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('1277cfdd-8b42-44ad-8481-69f5c1040a15', 'ad110547-6935-440d-b1f9-be0066e03fd8', 'LocalTask', '现场任务', '采样单', '功能区环境噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('133c8558-b013-491a-8713-27f8ac2c2de8', '34afebc4-6f06-4976-88f2-68fdaaff009e', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('152ab63a-936d-4fb4-bb42-726035e79a1f', '3d58569c-5314-4d09-af71-c81b762d232d', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('166fce5c-0cf1-46ea-a674-068af9af1276', '3e842677-05fb-4af0-ad53-8fcc69fe017c', 'ReportEdit', '报告编制', 'Precipitation', '降雨常规监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('1c4f8246-d33f-4e7d-afe9-798536e9da90', 'ac44d3ee-03dc-48b5-9f82-071a29ec8d81', 'LocalTask', '现场任务', '采样单', '底质（底泥、沉积物）采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('1cd70ef1-5965-4d8b-836b-bde5e0832941', '8259d792-6390-40e9-b31c-90e8b30ad138', 'PrepareSample', '采样准备', '采样单', '铁路边界噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('1da31f00-f7c7-47ce-8f54-b25a4ac96475', '29837dae-c309-4614-9d9b-ed923d89a420', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('1f2f518a-6cd4-411b-8cf8-5aeac602f69d', 'cfd5467a-d55b-4323-af33-086c6630f375', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('1fef623e-4e1b-471c-906a-d5870d3d778c', '7146c6a6-6e6c-4494-b16c-e587dba931a4', 'TestManage', '测试项目管理', 'TestExport', '导出', 0, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('22af3b3f-e623-4df1-8a34-95b04281a8e8', '40e81b2c-e0b7-4a13-8c00-6dcac5fdb006', 'PrepareSample', '采样准备', '采样单', '区域环境噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('232a9b4f-622f-4be7-8154-ac94cc98f97e', 'd724cc9a-0f65-4652-aac5-5b837125f875', 'LocalTask', '现场任务', '采样单', '社会生活源边界环境噪声测量记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('29855259-e869-4cfa-b192-afe649ecbacf', 'a818dd51-085a-4897-ad22-7f9066d185a0', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('2aa4a25e-2008-4e86-97eb-7db74b034ae8', '4da29a8a-a8e4-49d8-962e-e4b13ce71041', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('2c081dfb-fb10-418e-a99b-75d5cbce9d45', '1b665218-f21b-4935-9c60-e0811c1a4e91', 'ReportEdit', '报告编制', 'AreaNoise', '区域噪声监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('2ecdb3c1-f72b-43b0-94d6-e7fe2afcad56', 'd5842be5-3a2a-4af9-9be0-40befb046af7', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('2ed23f4c-90ab-4b8b-ba6b-4cc1d6c96a32', '9c2a604b-1be9-40d6-b747-662690e98952', 'PrepareSample', '采样准备', '采样单', '地表水采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('316fc576-732f-494c-9fb7-00ecde6348bd', 'bfe0fe80-6cb7-420d-9bb6-045e7bfd252f', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('31dcc094-06b9-4fb9-a9ba-412cc5904170', 'b4f459ee-f234-4fd5-82a3-fade13de06aa', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('33b67550-8ac8-4f6e-8d6b-b322f45c3539', '5b54620b-0197-43c9-acf5-ffb431812c78', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('356bf732-0bbc-4d54-9384-a3484ad6dbc6', 'e5c1ef02-9170-408c-a17a-5aea859735ea', 'ReportEdit', '报告编制', 'WasteGas', '废气监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('3583bbe7-f25a-4871-a9a6-d3e63b59d367', '1fd82f64-5e64-43ce-ac20-d29d1c609c77', 'LocalTask', '现场任务', '采样单', '自动烟尘(气、油烟)采样记录记录表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('35fefbc2-e213-45b0-912e-0cce86e622c3', 'd724cc9a-0f65-4652-aac5-5b837125f875', 'PrepareSample', '采样准备', '采样单', '社会生活源边界环境噪声测量记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('37440930-d7e8-484e-aaf2-a6d5139444c4', '9ad6013c-2e67-44c8-8547-824d0c412131', 'PrepareSample', '采样准备', '采样单', '工业企业厂界环境噪声测量记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('388ccb51-97b3-4be2-996a-d082762cf717', '43bc4a72-5fbe-4ede-bc85-24caab90cc29', 'LocalTask', '现场任务', '采样单', '污染源废水采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('398d28f3-70de-4707-a063-c57feb42b5c0', '40e81b2c-e0b7-4a13-8c00-6dcac5fdb006', 'LocalTask', '现场任务', '采样单', '区域环境噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('3b06101d-d4b0-4d7c-8bf6-8fa79884d894', '8592c3d4-6656-4223-8282-00791fd324b0', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('3d8feb5c-85aa-4ec3-be3a-034475a7b618', '11e986ca-8eca-45e9-b173-692069145836', 'SampleReceive', '样品交接', 'SampleReceive', '生成', 0, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('46731ad2-bc21-418d-82db-76f750f8316a', '36242497-8d85-443c-b9c1-f8ddf68b61eb', 'AnalyseDataManage', '实验室分析', '分析记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('4751ace4-e3eb-4eee-a153-66c5d610fb03', 'b5ac1c6d-c841-436d-b38f-00e4cb28323f', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('497ce4df-ec1d-4c35-b5c3-1f282213f65b', 'aa774fca-48a4-4240-be99-779dc1d59067', 'LocalTask', '现场任务', '采样单', '降尘采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('49ac62d5-fba8-4d0b-9cd9-13bd028b9163', '7157741c-7783-42e3-8dff-6e2f19d21e18', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('49e5d2e8-2cfa-42fc-9b87-9f79638030fa', 'e964d612-50c3-4854-bec7-46c7f729ca54', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('4c25d06a-62d8-428d-96dd-95dd5947146e', 'B233BB98-412E-46D5-9A3A-ADF7EB264F23', 'ProjectInquiry', '项目进度', '详细数据', '导出', 0, 0, 1, '', '项目详情:详细数据:数据信息', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('512c4cae-80f7-4756-b2e4-e19650061d33', 'd5fdc9a7-e980-419e-b0e1-cbbb9ceea6a9', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('5140d89d-0a8e-4277-a541-aeace3745a11', 'ac44d3ee-03dc-48b5-9f82-071a29ec8d81', 'PrepareSample', '采样准备', '采样单', '底质（底泥、沉积物）采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('51cf6f10-b45c-4aaf-8ee5-2e50a475b08c', 'c3129316-2a7b-4870-ac1f-2604c4dff612', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('52b64af2-6917-4a97-865d-71956163925f', '43bc4a72-5fbe-4ede-bc85-24caab90cc29', 'PrepareSample', '采样准备', '采样单', '污染源废水采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('53af3bf9-826f-4fee-9f6b-3c60d0beffd6', 'b27e8645-6319-42e0-ab5b-07bb2135689e', 'LocalTask', '现场任务', '采样单', '交通噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('5441b997-e633-4af2-a061-c19d46fb3471', '13ca8480-6871-436e-88c1-002b8cdc0390', 'PrepareSample', '采样准备', '采样单', 'pH、电导率、溶解氧、水温、浊度、透明度测试原始记录（8月）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('55c220f0-862f-4cec-aee5-a4df1715d151', 'eab54930-afa0-4dc5-9b40-41fde23dfba9', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('5ddfa1f3-730c-48fb-ae78-fc2e3e8dcc8e', 'edac2fd2-200e-4b4c-9158-20aab569c91c', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('5f869288-9fe7-455d-92a4-6b70be87101f', '5df5a558-4add-4a4c-a94b-a60e1d62b359', 'ConsumableManage', '消耗品管理', 'Consumable', '消耗品清单', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('6091f1e3-6ff3-4924-a7da-ecaff0e2ff39', '383cf141-7199-4c2d-a576-7e1992763a08', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('67581edd-969c-42a9-a017-8d879ecec193', '02198c5c-b8c9-4e78-9950-4088f2fa388e', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('68916f4d-8377-4df9-bf81-c2d8b85ef37c', '1b88786d-e7bc-494f-9680-0e4cf24ec358', 'LocalTask', '现场任务', '采样单', '地表水采样和交接记录（8月）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('68e62e3a-2df6-4dd7-be54-2ec6b3054f8e', '6b985723-75cb-4e65-95fc-e9daee6275d1', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-03-20 10:01:30', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-03-20 10:01:30');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('6a98140f-64f4-4442-ac12-75bba9c223d3', '3847f64f-e4a5-4da5-a3e9-63566e17482f', 'ConsumableManage', '消耗品管理', 'ConsumableYZD', '易制毒材料出入库登记表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('6f9f0d99-9d4e-4358-9ce0-b790c709dde0', '76d8d28c-f387-405f-8020-a2bbc48e86f3', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('6fba362f-2bcf-4191-a23d-179063e3d6a1', '83af429e-3ebe-4e37-b1d4-3ba6f140b81f', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('73384dbd-da21-40d1-9c0f-00b9eb7c4730', '9c2a604b-1be9-40d6-b747-662690e98952', 'LocalTask', '现场任务', '采样单', '地表水采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('73d8da03-bb56-4343-9c91-bd98af3d4b0f', 'e656a41e-734a-4171-b45f-5678e95b42c6', 'ReportEdit', '报告编制', 'Radiation', '辐射报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('74b41bf7-f12d-46d1-8ffa-a97a3e272f21', '76ee46dc-6f2c-4e4b-9e03-3a74a6746303', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('761ef126-e1b6-4180-bead-c93c5d1ada16', '4f29dab4-f73a-4d71-88d0-e6b693c0ae4c', 'LocalTask', '现场任务', '采样单', '敏感点（区域）环境噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('76ccb191-e62f-4b32-a133-eb04c1fd4381', '76fceb26-1a14-44fc-9f2f-54328d8639fc', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('7e3e7e42-e7c1-4c54-8e9b-77035defcce7', 'b208f3a9-ce4f-4291-91db-604a873b72b1', 'ReportEdit', '报告编制', 'TrafficNoise', '交通噪声监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('7eeaa644-b27e-45f0-9466-121766bb36a7', '115b7033-8fd6-4d62-89ef-d3a899ee99bc', 'LocalTask', '现场任务', '采样单', '烟气分析仪测量记录表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('80f75b5d-3699-46b5-81a1-cf48b88390d9', 'd95a33e3-80cd-45ce-82d0-a4b26b275f1a', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('82259d9a-8232-4717-b6b5-d8ceffa4dbc6', '23928ece-1569-4447-a92b-66a4b00958ce', 'PrepareSample', '采样准备', '采样单', '土壤采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('82c320b1-1af9-467e-967f-aba457fdec83', 'f780d70b-05aa-4f4d-a302-d56fcaeb8bc2', 'ReportEdit', '报告编制', 'NormalWaterTest', '常规水检测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('84a11eb6-1a00-4c1b-a3e1-7d38379c4e2e', '13ca8480-6871-436e-88c1-002b8cdc0390', 'LocalTask', '现场任务', '采样单', 'pH、电导率、溶解氧、水温、浊度、透明度测试原始记录（8月）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('84cfe6ee-e29b-4350-be39-b19e428fb0ec', '1c9ce2dc-6851-45e8-8223-7353e1574344', 'EnvironmentStatistics', '环境质量数据统计', '统计', '导出', 0, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('84d2c3eb-82fd-474d-8f1f-10f40d96c143', '4be62f6f-49cb-4a61-8a73-b0e50cb58632', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('8559fa05-53ef-4dba-bc80-380a595dfb10', '15fab25f-60a3-4f83-b698-abb3e3ab419a', 'LocalTask', '现场任务', '采样单', '大气降水采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('880b946b-670b-45b6-947a-a99a126b87ab', 'ad110547-6935-440d-b1f9-be0066e03fd8', 'PrepareSample', '采样准备', '采样单', '功能区环境噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('8927ba03-b918-430e-935c-b41d6cd65a39', '1325a1d0-9df3-49a7-b1a2-fdda3981f344', 'ReportEdit', '报告编制', 'WTWasteWater', '委托监测（废水）监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('89b1e892-6e8d-439c-b303-c4fa9b17807a', '15fab25f-60a3-4f83-b698-abb3e3ab419a', 'PrepareSample', '采样准备', '采样单', '大气降水采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('8a0a9116-f758-473a-bd34-c4d021a771c0', 'abee292e-6b62-40ae-ac21-4f78462a6d8c', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('8a6b6ba7-9267-4945-abbb-f4efaf8239e9', '50d92e04-c761-4d93-b99d-152a190dd9c4', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('8b20cc7c-55f6-4f7d-8584-0194929a7be6', '9ad6013c-2e67-44c8-8547-824d0c412131', 'LocalTask', '现场任务', '采样单', '工业企业厂界环境噪声测量记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('8c0c909b-cf31-4b5c-96b9-842d90382e99', '7a8d0a5d-1ff7-4236-b28d-2d24afe568a3', 'LocalTask', '现场任务', '采样单', '地下水采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('8d6b40d0-b27b-45b1-a365-961d9f16fe2d', '23928ece-1569-4447-a92b-66a4b00958ce', 'LocalTask', '现场任务', '采样单', '土壤采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('8efda18e-0a68-43c1-bb40-bc914ba9cd83', 'fffaa09b-3e4d-484c-9341-739b13c27c1d', 'ReportEdit', '报告编制', 'FallenDustTest', '降尘检测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('907ae9be-89ee-4cd2-b9f8-7465b49ef28a', '1cb6a072-03f2-4c93-ab0a-5c19fc278fce', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('917da390-0cc7-4e5b-8093-f5b2d1c121db', '2c1b684f-15fe-49d5-ba8e-71f93abc43b9', 'InstrumentManage', '仪器设备管理', 'InstrumentUseRecord', '生成', 0, 0, 1, '', '仪器设备管理:使用记录', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('97c51a73-a84f-4ec0-83c9-ded6b2dc5e89', '9d16e7c2-341d-4fb5-a116-3be3564003f2', 'ConsumableManage', '消耗品管理', 'ConsumableYZB', '易制爆材料出入库登记表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('986b75e0-c68e-44bf-bcfa-986917e01a6d', 'beeffdf7-6ca1-4b26-980e-91060191ef4b', 'ReportEdit', '报告编制', 'FallenDust', '降尘监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('98cc3c60-9513-4b1a-898a-54ba8ced1398', 'd1db1218-0695-4d52-a85c-7495e624afdf', 'SampleDisposalManage', '留样处置管理', 'SampleDispose', '导出', 0, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('9a5f2454-a987-4c31-bb71-bcb9c34e50f0', '444e0e74-53c8-4679-90fc-8adb774efe77', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('9af071d9-6fb0-4e15-aafe-e8422f8c4001', '8259d792-6390-40e9-b31c-90e8b30ad138', 'LocalTask', '现场任务', '采样单', '铁路边界噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('9ca17864-88cb-48a1-b8fe-ef986387ad16', '33c70d5a-feb6-4faa-8468-f56cf870f94e', 'LocalTask', '现场任务', '采样单', '煤样采集和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('a09a9ab6-dde1-4c66-aacb-bfd09cdc28d2', 'c14e2f38-e18b-4151-9654-621feeee9e04', 'StandardMaterialManage', '标准物质管理', 'StandardConsumable', '生成标样清单', 0, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('a0b5261d-bcee-44b6-aaa8-f3fe5b5fd895', '1fd82f64-5e64-43ce-ac20-d29d1c609c77', 'PrepareSample', '采样准备', '采样单', '自动烟尘(气、油烟)采样记录记录表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('a32f4040-1bfd-4881-b87a-0e57d0813600', '33c70d5a-feb6-4faa-8468-f56cf870f94e', 'PrepareSample', '采样准备', '采样单', '煤样采集和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('a37ae8fa-1244-464c-9fb3-e171fa3db8cd', 'a80e9479-5173-4862-bac9-10bf6baf3276', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('a576369e-e927-459d-99a4-65bbb3a6e232', '2d971659-fd73-4998-b4a1-d3ca495fa4b5', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('a61a0d11-c5d8-4257-895d-9a3f84d7c25e', '87a9ace0-d4b9-4e97-86ac-c07239e8f9f6', 'ReportEdit', '报告编制', 'FuncNoise', '功能区噪声监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('a6df4196-be5a-44a9-9173-409d0faf502f', 'a67f7243-6754-4a40-9e82-216241d1c9c8', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('a9a9a578-72ca-4440-b757-63253d61672f', '519e62ea-a900-4635-b6fc-fe106fca73ac', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('ae7f56ca-e9ba-48fb-997a-5ea0f3afd795', '5d5c9d4e-20bf-4478-8dd4-84eb4ee92620', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('af06a500-4723-4d84-94fd-270e557f5c95', 'fc2386c6-d75f-434c-a787-1ce184089d8a', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('afdb5b72-03d2-416d-9c1f-1bc1e970ce0e', 'e54f395b-eeca-460b-b730-a912c86b7950', 'ReportEdit', '报告编制', 'NormalWater', '常规水监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b206369d-7029-4b60-8a1c-b6482d132375', '67963bc5-eac5-47ff-a926-857b7d1b7181', 'InstrumentAccessManage', '仪器出入库管理', 'ProjectInstrument', '导出', 0, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b25f5cce-c024-47ec-b6b8-df327f83e174', '9dc1061d-ab81-4abe-bec2-a6d02d09a39b', 'ReportEdit', '报告编制', 'NormalWaterTestHorizontal', '常规水检测报告（横版）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b41498ce-935f-49f3-a9af-57a379b3cdcb', '1e42ed56-8181-4284-89eb-f60e25606a3b', 'analysisQualityStatistics', '分析质控统计', 'QualityControl', '生成报表', 0, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b5efb15d-1061-4dad-964d-0159ee9f8b11', '6a7692b8-fcd6-4741-bf9a-8ab20e57cbe5', 'ReportEdit', '报告编制', 'SmokeBlack', '烟气黑度监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b68430ad-36bd-461d-95f3-86e5e8c34f44', 'd5b83be1-be36-41a2-a654-3e6114643d6a', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b6e3833a-38b4-42b3-9806-42b9759e3040', '80850661-2665-4c44-9813-3475f58d19c6', 'InstrumentManage', '仪器设备管理', 'InstrumentLabel', '仪器标签生成', 0, 0, 1, '', '仪器设备管理', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b7153ab9-6d42-45fc-8d0b-8e74b648ba87', '2cd060b9-bdc5-4264-91b9-89f4a4d04485', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b72d91a9-d9e7-48f5-bc55-fb9a3f79b3f4', '04fcd27b-4c96-42e6-8cf9-4ff611468a55', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('b98d8204-7cbc-48ce-a1bf-74463a728576', '8ac8f239-048b-4e7e-b26a-b8a6df89179b', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('bd7954c7-ef84-45e1-8f69-bc8d263ba7c2', '4f29dab4-f73a-4d71-88d0-e6b693c0ae4c', 'PrepareSample', '采样准备', '采样单', '敏感点（区域）环境噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('c041a5fa-97dd-4c51-bf34-ed5eeeed699b', 'caee46e5-ef7b-45f0-bcfc-06d7a3ef764c', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('c0e49e80-f7e0-4f01-9c44-6e22aa2771b4', '7c9bcdc5-efa2-4e3e-832c-1ab19aa56487', 'ReportEdit', '报告编制', 'ZFWasteWater', '执法监测（废水）监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('c10e8c98-848d-4c2a-a609-2cae74491dcc', 'cb62e1f3-4ff5-4caa-b483-81f9c5d01d8b', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('c15c24b6-3b66-42ed-ab56-b772772958fd', '8068f37d-accb-40cb-9e49-9c4892f97d48', 'ReportEdit', '报告编制', 'JcNoise', '监测报告格式使用版（噪声）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('c6a4813d-a6a0-414a-b7cf-d5105c3d79cd', 'c077c43e-6316-4753-9955-e74412159c2f', 'ReportEdit', '报告编制', 'SurfaceWater', '常规地表水', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('c790d1d5-1003-4743-bd59-b3c52bf5dab3', '9e4e25f7-aae8-475d-a6ab-2b2325a6da70', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('c86307f2-919a-4660-8348-0b6c932b92df', 'aa774fca-48a4-4240-be99-779dc1d59067', 'PrepareSample', '采样准备', '采样单', '降尘采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('c9c497f9-085a-4272-809b-06ae77d531e6', 'd7d94fa2-263b-4607-8daf-08885cd0e641', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('ca32e398-22ad-418d-a274-02f3ffde4baa', '7a8d0a5d-1ff7-4236-b28d-2d24afe568a3', 'PrepareSample', '采样准备', '采样单', '地下水采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('cc9c6d84-bde6-4cac-a523-d434e41a40c7', '80298b64-3579-48fe-aba4-54f966924ed6', 'EvaluationCriteria', '评价标准', 'EvaluationCriteria', '导出', 0, 0, 1, '', '', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-02-27 18:54:29', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-02-27 18:54:29');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('cd642acf-d699-42e1-87b7-5cf905d8bc17', 'e89254bc-3044-4ba8-bb41-02838f689ff9', 'OrderManage', '订单管理', 'OrderForm', '订单明细表', 0, 0, 1, '', '订单管理:订单登记', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('ce7d358b-6898-4bb6-87ef-9721c2ccc544', '115b7033-8fd6-4d62-89ef-d3a899ee99bc', 'PrepareSample', '采样准备', '采样单', '烟气分析仪测量记录表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('d0262c79-0815-420f-a233-b1e37d06dd6a', 'e71eb218-886f-4d46-83fe-fa2560affadb', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('d0f54e81-5794-4263-9027-79a88ef6083e', 'fb681ec3-5f64-49bf-a9c3-6bfb242c1b2c', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('d2271219-c2a2-4590-883a-e73780586bee', '17B39793-F9B8-4EC1-AB71-D116D302359C', 'ProjectInquiry', '项目进度', '详细数据', '导出', 0, 0, 1, '', '项目详情:详细数据:样品信息', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('d338ba85-f242-4c02-bc38-4e0faf830db9', '476240ad-6f2b-42f2-a4ec-4b0a83a329b7', 'InstrumentManage', '仪器设备管理', 'Instrument', '仪器清单', 1, 0, 1, '', '仪器设备管理', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('d5d13cf1-53aa-4637-850d-f82658c391ce', '5f3df1da-8425-43a5-99d6-3ff37a19ac14', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('d6084467-52d3-4c4a-b2ca-98f4023184a3', '22b0aca6-df06-45a0-b456-41864979f2c5', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('d8699a68-d3ef-4de6-9b37-fea03561eb61', 'ce909369-2370-4f7b-a8b7-f49e93f348ef', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('d9b9fce3-ab34-4974-bc26-6175668a01f1', '944766ed-960b-4c13-a667-e58feb129eca', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('e2405f5f-0d39-4cc9-974e-780db8534cdc', 'bee92f18-7ac8-41bf-8905-1a296b979f3d', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('e294e85a-6997-4100-89e2-9b2211fe2c22', '0df57004-fbec-44c6-966e-da81d184bc42', 'LocalTask', '现场任务', '采样单', '大气环境采样和交接记录单', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('e507b69d-4533-4167-9919-b255c360052d', 'd4524da2-3414-4a4c-99a4-816965107af2', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('e64c22b3-a213-43a5-88cc-16cb51a05b5d', 'c074b990-316a-4ec0-802c-002edee5bf61', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('e84b59d2-93f4-450f-bd64-f937ac58c4f0', '5dba6b7f-fcdb-492d-84eb-7e93f3e87894', 'LocalTask', '现场任务', '采样单', '有组织气态污染物采样和交接记录表(不等速采样)', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('e93f0800-ba9c-4164-b6a9-d59aba5a9072', 'c7e4a25a-2683-4934-90b0-c52e7c9c60a1', 'ReportEdit', '报告编制', 'GasSampleAnalysisResults', '气体样品及分析结果计算表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('ebebd259-efa3-4691-aca1-5c01d6e0379e', 'cc6a7036-1320-4ea0-97a5-2820633ad0a8', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('ec82f76a-1628-40af-b8fa-8160d331c3e5', '17cafc42-dd44-4f57-b189-4c4eff5b2acd', 'ReportEdit', '报告编制', 'Smoke', '烟气监测报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('ef1457ee-369f-4838-a19d-952cffe7e524', '1b88786d-e7bc-494f-9680-0e4cf24ec358', 'PrepareSample', '采样准备', '采样单', '地表水采样和交接记录（8月）', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('efd3d015-0681-48a2-bd10-e2fa8b0e89d7', '3daed502-3166-4cbc-9cf8-cbd23caf1d36', 'LocalTask', '现场任务', '采样单', '工业固体废物采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('f26ddc10-4cad-4749-baf9-8699044525be', 'cfe61a30-992f-4a31-915d-68051dc8dcfc', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('f4500514-1728-4349-a3fa-9a94bc133fba', '5dba6b7f-fcdb-492d-84eb-7e93f3e87894', 'PrepareSample', '采样准备', '采样单', '有组织气态污染物采样和交接记录表(不等速采样)', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('f50d1f92-7609-4c4f-9c67-10e2837df553', '3daed502-3166-4cbc-9cf8-cbd23caf1d36', 'PrepareSample', '采样准备', '采样单', '工业固体废物采样和交接记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('f6aa608b-0f2c-4310-afdf-0b71fdf516e9', '104401a3-c446-4bf8-9e2f-127558479b9b', 'PeopleManage', '人员管理', 'Person', '生成人员一览表', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('f99f9ad9-bb79-4422-9f3a-ae97f182d18e', 'c891cb86-3891-428a-aaab-d4fa88c244e2', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('fc424547-8ae9-498d-87e7-06bbd33ee584', '2a3106df-09b2-4503-b5cc-6858a892c625', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('fcad14e9-dd78-4918-a1c8-7f403973feea', 'dc87dc82-c2e5-4163-bc20-fa4f1f033186', 'AnalyseDataManage', '实验室分析', '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('feb93a21-20fd-434d-858a-d69eaa1534c9', '4e416b15-e6b9-4bda-b3d7-6a7e87182c38', 'ReportEdit', '报告编制', 'ZFUnOrgEnv', '执法监测（无组织、环境空气）报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
INSERT INTO TB_LIM_ReportApply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate) VALUES ('ff3c0021-2781-47a6-8f9a-e6e8229abe58', 'b27e8645-6319-42e0-ab5b-07bb2135689e', 'PrepareSample', '采样准备', '采样单', '交通噪声监测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 09:14:15');
