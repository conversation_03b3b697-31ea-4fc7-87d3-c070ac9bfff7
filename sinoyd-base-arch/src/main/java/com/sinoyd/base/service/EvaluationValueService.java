package com.sinoyd.base.service;

import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.dto.customer.DtoEvaluationValueCopy;
import com.sinoyd.base.dto.customer.DtoEvaluationValueTemp;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * EvaluationValue操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
public interface EvaluationValueService extends IBaseJpaService<DtoEvaluationValue, String> {

    /**
     * 根据评价等级id获取评价数据
     *
     * @param levelIds 评价ids
     * @return 返回评价数据
     */
    List<DtoEvaluationValue> findEvaluationValueByLevelId(List<String> levelIds);


    /**
     * 保存条件下的分项目信息
     *
     * @param dtoEvaluationValueTemp 评价条件
     * @return 返回相应的评价值信息
     */
    List<DtoEvaluationValue> addAnalyzeItem(DtoEvaluationValueTemp dtoEvaluationValueTemp);


    /**
     * 复制条件下的分析项目信息
     *
     * @param dtoEvaluationValueCopy 复制的评价信息
     * @return 返回相应的评价值信息
     */
    List<DtoEvaluationValue> copyAnalyzeItem(DtoEvaluationValueCopy dtoEvaluationValueCopy);


    /**
     * 修改运算符
     *
     * @param dtoEvaluationValueTemp 评价条件
     * @return 返回修改后的值
     */
    Integer updateSymbol(DtoEvaluationValueTemp dtoEvaluationValueTemp);

    /***
     * 删除评价信息值
     * @param dtoEvaluationValueTemp 评价信息值
     * @return 返回删除行
     */
    Integer deleteEvaluationValue(DtoEvaluationValueTemp dtoEvaluationValueTemp);

    /**
     * 批量修改量纲
     *
     * @param dtoEvaluationValueTemp 评价条件
     * @return 返回修改后的值
     */
    Integer batchUpdateDimension(DtoEvaluationValueTemp dtoEvaluationValueTemp);
}