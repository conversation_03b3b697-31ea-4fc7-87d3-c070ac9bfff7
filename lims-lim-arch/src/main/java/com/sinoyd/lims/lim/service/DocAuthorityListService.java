package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityList;

import java.util.List;

/**
 * 文件夹权限接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/19
 * @since V100R001
 */
public interface DocAuthorityListService extends IBaseJpaService<DtoDocAuthorityList, String> {

    /**
     * 根据人员与用户id初始化权限
     *
     * @param objectId 文件id
     * @param userId   用户id
     */
    void initAuthorityConfig(String objectId, String userId);

    /**
     * 处理一些旧数据
     *
     * @param objectIds 文件夹id
     */
    void processOldAuthorityConfigData(List<String> objectIds);

}
