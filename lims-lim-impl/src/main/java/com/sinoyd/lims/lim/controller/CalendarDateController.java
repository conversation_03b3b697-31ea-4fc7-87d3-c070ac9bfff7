package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.rcc.DtoCalendarDate;
import com.sinoyd.lims.lim.service.CalendarDateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 日历日期接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023-01-19
 */
@Api(tags = "日历日期接口服务")
@RestController
@RequestMapping("/api/lim/calendarDate")
@Validated
public class CalendarDateController extends BaseJpaController<DtoCalendarDate, String, CalendarDateService> {

    /**
     * 查询日历日期数据
     *
     * @param year 年份
     * @return 日历日期List
     */
    @ApiOperation(value = "查询日历日期数据", notes = "查询日历日期数据")
    @GetMapping("/year/{year}")
    public RestResponse<List<DtoCalendarDate>> find(@PathVariable("year") Integer year) {
        RestResponse<List<DtoCalendarDate>> restResp = new RestResponse<>();
        restResp.setData(service.findByYear(year));
        return restResp;
    }


    /**
     * 初始化日历日期数据
     *
     * @param year 传入年份
     * @return 初始化完以后的dto
     */
    @ApiOperation(value = "初始化日历日期数据", notes = "初始化日历日期数据")
    @PostMapping("{year}")
    public RestResponse<List<DtoCalendarDate>> init(@PathVariable("year") Integer year) {
        RestResponse<List<DtoCalendarDate>> restResp = new RestResponse<>();
        restResp.setData(service.initCalendarDate(year));
        return restResp;
    }

    /**
     * 批量修改日历日期数据
     *
     * @param dtoList 传入日历日期数据list
     * @return 修改完以后的dto
     */
    @ApiOperation(value = "批量修改日历日期数据", notes = "批量修改日历日期数据")
    @PutMapping
    public RestResponse<List<DtoCalendarDate>> update(@Validated @RequestBody List<DtoCalendarDate> dtoList) {
        RestResponse<List<DtoCalendarDate>> restResp = new RestResponse<>();
        restResp.setData(service.save(dtoList));
        return restResp;
    }


}



  