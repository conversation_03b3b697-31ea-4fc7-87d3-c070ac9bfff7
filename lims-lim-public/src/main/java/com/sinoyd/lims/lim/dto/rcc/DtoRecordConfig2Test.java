package com.sinoyd.lims.lim.dto.rcc;

import javax.persistence.*;

import com.sinoyd.lims.lim.entity.RecordConfig2Test;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoRecordConfig2Test实体
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_RecordConfig2Test")
 @Data
 @DynamicInsert
 public  class DtoRecordConfig2Test extends RecordConfig2Test {
    private static final long serialVersionUID = 1L;

    /**
     * 分析方法名称
     */
    @Transient
    private String redAnalyzeMethodName;

    /**
     * 标准编号
     */
    @Transient
    private String redCountryStandard;


    /**
     * 分析项目名称
     */
    @Transient
    private String redAnalyzeItemName;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 分析项目排序值
     */
    @Transient
    private Integer orderNum;

    /**
     * 构造函数 RecordConfig2TestServiceImpl 下的findByPage 对应
     * @param id 主键id
     * @param testId 测试项目id
     * @param recordConfigId 配置id
     * @param redAnalyzeItemName 分析项目
     * @param redAnalyzeMethodName 分析方法
     * @param sampleTypeName 监测类型
     * @param redCountryStandard 标准方法
     */
    public DtoRecordConfig2Test(String id,
                                String testId,
                                String recordConfigId,
                                String redAnalyzeItemName,
                                String redAnalyzeMethodName,
                                String redCountryStandard,
                                String sampleTypeName,
                                Integer orderNum) {
        this.setId(id);
        this.setTestId(testId);
        this.setRecordConfigId(recordConfigId);
        this.setRedAnalyzeItemName(redAnalyzeItemName);
        this.setRedAnalyzeMethodName(redAnalyzeMethodName);
        this.setRedCountryStandard(redCountryStandard);
        this.setSampleTypeName(sampleTypeName);
        this.setOrderNum(orderNum);
    }


    public DtoRecordConfig2Test(){}
}