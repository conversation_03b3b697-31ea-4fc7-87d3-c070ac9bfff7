package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.dto.DtoReportBaseInfo;
import com.sinoyd.lims.pro.dto.DtoReportFolderInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 电子报告信息实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
@Data
@AllArgsConstructor
public class DtoReportInfo {

    public DtoReportInfo() {
    }

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 电子报告基础信息
     */
    private DtoReportBaseInfo reportBaseInfo;

    /**
     * 电子报告点位信息列表
     */
    private List<DtoReportFolderInfo> reportFolderInfoList;

    /**
     * 是否生成
     * 生成报告前判定是否需要先生成电子报告
     */
    private Boolean isCreate = false;

}
