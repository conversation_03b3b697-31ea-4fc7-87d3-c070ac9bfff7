package com.sinoyd.lims.api.dto;

import lombok.Data;

import javax.persistence.Transient;
import java.util.Date;
import java.util.List;


@Data
public class DtoProjectDetailPhone {

    /**
     * 项目id
     */
    private String id;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 受检单位id
     */
    private String inspectedEntId;

    /**
     * 受检单位名称
     */
    private String inspectedEntName;

    /**
     * 登记时间
     */
    private Date inputTime;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 状态
     */
    private String status;

    /**
     * 等级
     */
    private Integer grade;

    /**
     * 项目负责人Id
     */
    private String leaderId;

    /**
     * 项目负责人名称
     */
    private String leaderName;

    /**
     * 监测要求及说明
     */
    private String customerRequired;

    /**
     * 其他说明
     */
    private String remark;

    /**
     * 地址
     */
    private String address;

    /**
     * 应急送样单id
     */
    private String receiveId;

    /**
     * 编制报告人
     */
    private String reportMakerId;

    /**
     * 编制报告人
     */
    private String reportMakerName;

    /**
     * 要求时间
     */
    private Date deadLine;

    /**
     * 联系人
     */
    private String inspectedLinkMan;

    /**
     * 联系电话
     */
    private String inspectedLinkPhone;

    /**
     * 委托单位id
     */
    private String customerId;

    /**
     * 委托单位名称
     */
    private String customerName;

    /**
     * 送养人Id
     */
    private String senderId;

    /**
     * 送养人名称
     */
    private String senderName;

    /**
     * 送样时间
     */
    private Date sendTime;

    /**
     * 监测目标
     */
    private String monitorPurp;

    /**
     * 监测方式
     */
    private String monitorMethods;


    /**
     * 监测目的
     */
    private String samplingPersonIds;

    /**
     * 采样人员
     */
    private List<String> samPersonIds;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 送样类型
     */
    private Integer receiveType;

    /**
     * 登记人id
     */
    private String inceptPersonId;

    /**
     * 登记时间
     */
    private Date inceptTime;

    /**
     * 分析领样单
     */
    private String subXCId;

    /**
     * 现场领样单
     */
    private String subFXId;

    /**
     * 采样人名称
     */
    private String samplingPersonNames;

    /**
     * 送样单备注
     */
    private String receiveRemark;
}
