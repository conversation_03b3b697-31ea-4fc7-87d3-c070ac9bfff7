package com.sinoyd.lims.monitor.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * 例行点位拓展字段配置实体
 * <AUTHOR>
 * @version V5.2.0 2023/06/12
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="PointExtendConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class PointExtendConfig implements BaseEntity, Serializable {
    private static final long serialVersionUID = 1L;

    public PointExtendConfig() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 点位类型(常量控制)
     */
    @Column(length = 50)
    @ApiModelProperty("点位类型")
    @Length(message = "点位类型(常量控制){validation.message.length}", max = 50)
    private String pointType;

    /**
     * 参数id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("参数id")
    private String paramsId;

    /**
     * 拓展字段名称
     */
    @Column(length = 50)
    @ApiModelProperty("拓展字段名称")
    @Length(message = "拓展字段名称{validation.message.length}", max = 50)
    private String filedName;

    /**
     * 拓展字段别名
     */
    @Column(length = 50)
    @ApiModelProperty("拓展字段别名")
    @Length(message = "拓展字段别名{validation.message.length}", max = 50)
    private String filedAlias;

    /**
     * 默认值
     */
    @Column(length = 50)
    @ApiModelProperty("默认值")
    @Length(message = "默认值{validation.message.length}", max = 100)
    private String defaultValue;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;

    /**
     * 默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件
     * ）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）")
    private Integer defaultControl;

    /**
     * 数据源
     */
    @Column(length = 2000)
    @ApiModelProperty("数据源")
    @Length(message = "数据源{validation.message.length}", max = 2000)
    private String dataSource;

    /**
     * 数据源类型
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("下拉框数据源类型（枚举EnumControlDataSourceType:1.接口请求 2.常量数据源 3.自定义数据源）")
    private Integer dataSourceType;

    /**
     * 常量数据源
     */
    @Column(length = 100)
    @ApiModelProperty("常量数据源")
    @Length(message = "常量数据源(常量类型){validation.message.length}", max = 100)
    private String codeDataSource;

    /**
     * 数据源请求接口
     */
    @Column(length = 100)
    @ApiModelProperty("数据源请求接口")
    @Length(message = "数据源请求接口{validation.message.length}", max = 100)
    private String dataSourceUrl;

    /**
     * 数据源请求接口返回Key值对应字段
     */
    @Column(length = 100)
    @ApiModelProperty("数据源请求接口返回Key值对应字段")
    @Length(message = "Key对应字段{validation.message.length}", max = 100)
    private String urlReturnKey;

    /**
     * 数据源请求接口返回Value值对应字段
     */
    @Column(length = 100)
    @ApiModelProperty("数据源请求接口返回Value值对应字段")
    @Length(message = "Value对应字段{validation.message.length}", max = 100)
    private String urlReturnValue;

    /**
     * 下拉树对应子数据字段
     */
    @Column(length = 100)
    @ApiModelProperty("下拉树对应子数据字段")
    @Length(message = "下拉树对应子数据字段{validation.message.length}", max = 100)
    private String treeChildFiled;

    /**
     * isDeleted
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("isDeleted")
    private Boolean isDeleted = false;

    /**
     * 是否必填（用于测试公式，检测类型参数）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否必填（用于测试公式，检测类型参数）")
    private Boolean requiredInd;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
