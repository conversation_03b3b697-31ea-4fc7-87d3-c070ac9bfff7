package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSampleDispose2Test;

import java.util.Collection;
import java.util.List;


/**
 * SampleDispose2Test数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/11/8
 * @since V100R001
 */
public interface SampleDispose2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleDispose2Test, String> {

    /**
     * 根据留样处置id批量删除关联表
     *
     * @param sampleDisposeIds 留样处置id集合
     */
    void deleteAllBySampleDisposeIdIn(Collection<?> sampleDisposeIds);

    /**
     * 根据留样处置id删除关联数据
     *
     * @param sampleDisposeId 留样处置id
     */
    void deleteAllBySampleDisposeId(String sampleDisposeId);

    /**
     * 根据留样处置id查询关联数据
     *
     * @param sampleDisposeId 留样处置id
     * @return 查询到的关联数据
     */
    List<DtoSampleDispose2Test> findBySampleDisposeId(String sampleDisposeId);

    /**
     * 根据留样id集合查询关联数据
     * @param disposeIds 留样处置id集合
     * @return 查询到的关联数据
     */
    List<DtoSampleDispose2Test> findBySampleDisposeIdIn(List<String> disposeIds);

}