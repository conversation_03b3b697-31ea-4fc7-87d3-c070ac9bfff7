package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.AnalyseAchievement2Person;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * DtoAnalyseAchievement2Person实体
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_AnalyseAchievement2Person")
@Data
@DynamicInsert
public class DtoAnalyseAchievement2Person extends AnalyseAchievement2Person {

    private static final long serialVersionUID = 1L;

    @Transient
    private String deptName;

    @Transient
    private String personName;

    @Transient
    private Integer sampleNum;

    @Transient
    private String rateStr;

    @Transient
    private BigDecimal total;


}
