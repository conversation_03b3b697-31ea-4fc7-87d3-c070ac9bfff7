package com.sinoyd.lims.monitor.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * Station查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StationCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 测站名称和测站代码关键字
     */
    private String nameOrCode;

    /**
     * 状态：1.启用 0.未启用 -1.所有
     */
    private Integer isEndable;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(nameOrCode)) {
            condition.append(" and (stname like :nameOrCode or stcode like :nameOrCode)");
            values.put("nameOrCode", "%" + this.nameOrCode + "%");
        }

        if (null != isEndable) {
            if (0 == isEndable) {
                condition.append(" and isEndable = false ");
            } else {
                condition.append(" and isEndable = true");
            }
        }
        return condition.toString();
    }
}