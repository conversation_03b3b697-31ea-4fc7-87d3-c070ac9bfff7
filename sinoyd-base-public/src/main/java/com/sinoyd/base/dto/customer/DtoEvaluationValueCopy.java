package com.sinoyd.base.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 评价等级值复制的实体（多条件）
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Data
public class DtoEvaluationValueCopy {

    /**
     * 评价标准id
     */
    private String evaluationId;

    /**
     * 源等级id
     */
    private String sourceLevelId;


    /**
     * 待复制的等级id
     */
    private List<String> destinationLevelIds;


    /**
     * 分析项目ids
     */
    private List<String> analyzeItemIds = new ArrayList<>();
}
