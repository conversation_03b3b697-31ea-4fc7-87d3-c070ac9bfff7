package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import org.joda.time.DateTime;


/**
 * OrderForm查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderFormCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 订单开始时间
     */
    private String startTime;

    /**
     * 订单结束时间
     */
    private String endTime;

    /**
     * 业务员id
     */
    private String saleId;

    /**
     * 登记人id
     */
    private String registrantId;

    /**
     * 业务类型
     */
    private String projectTypeId;

    /**
     * 报价单状态
     */
    private Integer orderStatus = EnumPRO.EnumStatus.所有.getValue();

    /**
     * 推送状态
     */
    private Integer pushStatus = EnumPRO.EnumStatus.所有.getValue();

    /**
     * 签订状态
     */
    private Integer grantStatus = EnumPRO.EnumStatus.所有.getValue();

    /**
     * 报价单号
     */
    private String key;

    /**
     * 模块编码
     */
    private String module;

    /**
     * 处理状态
     */
    private Integer status = EnumPRO.EnumStatus.所有.getValue();

    /**
     * 审核人id
     */
    private String auditPersonId;

    /**
     * 选择的订单id集合
     */
    List<String> orderIds;

    /**
     * 待办状态，如果为true，表示请求为获取待办标签数量
     */
    private Boolean todoStatus = false;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and a.id = :id");
            values.put("id", this.id);
        }

        if (StringUtil.isNotEmpty(this.projectTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.projectTypeId)) {
            condition.append(" and a.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.orderDate >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and a.orderDate < :endTime");
            values.put("endTime", to);
        }
        if (StringUtil.isNotEmpty(this.saleId) && !UUIDHelper.GUID_EMPTY.equals(this.saleId)) {
            condition.append(" and a.salesPersonId = :saleId");
            values.put("saleId", this.saleId);
        }
        if (StringUtil.isNotEmpty(this.registrantId) && !UUIDHelper.GUID_EMPTY.equals(this.registrantId)) {
            condition.append(" and a.registrantId = :registrantId");
            values.put("registrantId", this.registrantId);
        }


//        if (StringUtil.isNotEmpty(this.module)) {
//            condition.append(" and s.module = :module");
//            if (this.module.equals(EnumPRO.EnumOrderModule.订单一审.getCode())) {
//                values.put("module", EnumPRO.EnumOrderModule.订单一审.getCode());
//            } else if (this.module.equals(EnumPRO.EnumOrderModule.订单二审.getCode())) {
//                values.put("module", EnumPRO.EnumOrderModule.订单二审.getCode());
//            } else {
//                values.put("module", this.module);
//            }
//        }

        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (a.orderCode like :key or a.orderName like :key or a.enterpriseName like :key or a.customerOrderNo like :key)");
            values.put("key", "%" + this.key + "%");
        }

        if (StringUtil.isNotNull(this.todoStatus) && this.todoStatus) {
            condition.append(" and a.orderStatus in :orderStatus");
            List<Integer> statusList = new ArrayList<>();
            statusList.add(EnumPRO.EnumOrderStatus.登记中.getValue());
            statusList.add(EnumPRO.EnumOrderStatus.审核不通过.getValue());
            condition.append(" and a.orderStatus in :orderStatus");
            values.put("orderStatus", statusList);
        } else {
            if (!EnumPRO.EnumStatus.所有.getValue().equals(this.orderStatus)) {
                if (!EnumPRO.EnumOrderStatus.一审中.getValue().equals(this.orderStatus)) {
                    condition.append(" and a.orderStatus = :orderStatus");
                    values.put("orderStatus", this.orderStatus);
                } else {
                    List<Integer> statusList = new ArrayList<>();
                    statusList.add(EnumPRO.EnumOrderStatus.一审中.getValue());
                    statusList.add(EnumPRO.EnumOrderStatus.二审中.getValue());
                    condition.append(" and a.orderStatus in :orderStatus");
                    values.put("orderStatus", statusList);
                }
            } else {
                if (!EnumPRO.EnumOrderModule.订单录入.getCode().equals(this.module)) {
                    List<Integer> statusList = new ArrayList<>();
                    statusList.add(EnumPRO.EnumOrderStatus.一审中.getValue());
                    statusList.add(EnumPRO.EnumOrderStatus.二审中.getValue());
                    statusList.add(EnumPRO.EnumOrderStatus.审核通过.getValue());
                    condition.append(" and a.orderStatus in :orderStatus");
                    values.put("orderStatus", statusList);
                }
            }
        }

        if (StringUtil.isNotEmpty(this.auditPersonId) && !UUIDHelper.GUID_EMPTY.equals(this.auditPersonId)) {
            condition.append(" and ( a.firstPersonId = :auditPersonId or a.secondPersonId = :auditPersonId or a.threePersonId = :auditPersonId )");
            values.put("auditPersonId", this.auditPersonId);
        }

        if (!EnumPRO.EnumStatus.所有.getValue().equals(this.pushStatus)) {
            condition.append(" and a.pushStatus = :pushStatus");
            values.put("pushStatus", this.pushStatus);
        }

        if (!EnumPRO.EnumStatus.所有.getValue().equals(this.grantStatus)) {
            if (this.grantStatus == 0 || this.grantStatus == 1) {
                condition.append(" and exists (select 1 from DtoOrderContract c where a.id = c.orderId and c.contractStatus = :grantStatus )");
                values.put("grantStatus", this.grantStatus);
            } else {
                condition.append(" and not exists (select 1 from DtoOrderContract c where a.id = c.orderId)");
            }
        }

        if (StringUtil.isNotNull(this.status) && !EnumPRO.EnumStatus.所有.getValue().equals(this.status)) {
            condition.append("  and a.orderStatus in :orderStatusList");
            List<Integer> statusList = new ArrayList<>();
            if (EnumPRO.EnumStatus.待处理.getValue().equals(this.status)) {
                if (EnumPRO.EnumOrderModule.订单录入.getCode().equals(this.module)) {
                    statusList.add(EnumPRO.EnumOrderStatus.登记中.getValue());
                    statusList.add(EnumPRO.EnumOrderStatus.审核不通过.getValue());
                } else {
                    statusList.add(EnumPRO.EnumOrderStatus.一审中.getValue());
                    statusList.add(EnumPRO.EnumOrderStatus.二审中.getValue());
                }
            } else {
                if (EnumPRO.EnumOrderModule.订单录入.getCode().equals(this.module)) {
                    statusList.add(EnumPRO.EnumOrderStatus.一审中.getValue());
                    statusList.add(EnumPRO.EnumOrderStatus.二审中.getValue());
                    statusList.add(EnumPRO.EnumOrderStatus.审核通过.getValue());
                } else {
                    statusList.add(EnumPRO.EnumOrderStatus.审核不通过.getValue());
                    statusList.add(EnumPRO.EnumOrderStatus.审核通过.getValue());
                }
            }
            values.put("orderStatusList", statusList);
        }

        return condition.toString();
    }
}