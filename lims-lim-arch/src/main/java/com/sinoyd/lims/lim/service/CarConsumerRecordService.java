package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoCarConsumerRecord;

/**
 * 车辆消费记录配置
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface CarConsumerRecordService extends IBaseJpaService<DtoCarConsumerRecord, String> {

    /**
     * 重新定义接口（为了返回调用该接口类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回车辆消费记录对象
     */
    @Override
    DtoCarConsumerRecord findOne(String id);
}