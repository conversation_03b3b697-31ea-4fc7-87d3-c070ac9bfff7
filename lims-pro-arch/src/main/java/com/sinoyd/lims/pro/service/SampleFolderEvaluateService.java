package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSampleFolderEvaluate;

import java.util.List;

/**
 *  点位评价数据管理service
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/06/13
 */
public interface SampleFolderEvaluateService extends IBaseJpaService<DtoSampleFolderEvaluate, String> {

    /**
     * 批量保存
     * @param folderEvaluateList evaluateList
     * @return 数据集合
     */
    List<DtoSampleFolderEvaluate> saveList(List<DtoSampleFolderEvaluate> folderEvaluateList);
}
