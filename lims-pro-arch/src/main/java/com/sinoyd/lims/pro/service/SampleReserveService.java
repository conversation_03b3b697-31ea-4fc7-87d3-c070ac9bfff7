package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleReserve;

import java.util.List;
import java.util.Map;

/**
 * 样品处理操作接口
 * <AUTHOR>
 * @version V5.2.0 2022/6/21
 */
public interface SampleReserveService extends IBaseJpaService<DtoSampleReserve,String> {

    /**
     * 分页查询数据
     *
     * @param pb 分页数据
     * @param criteria 查询条件
     */
    void findSamplesByPage(PageBean<DtoSample> pb, BaseCriteria criteria);

    /**
     * 保存领取信息
     *
     * @param sampleReserve 保存的数据
     * @return 保存的数据
     */
    List<DtoSampleReserve> saveReserve(DtoSampleReserve sampleReserve);


    /**
     * 判断样品是否已经领取
     *
     * @param sampleReserve 保存的数据
     * @return 已存在的数据
     */
    List<DtoSampleReserve> judgeSaveData(DtoSampleReserve sampleReserve);

    /**
     * 根据样品id查询样品详细信息
     *
     * @param sampleId 样品id
     * @return 查询结果
     */
    DtoSample findSampleDetail(String sampleId);

    /**
     * 根据样品Id获取测试项目
     *
     * @param sampleIds 样品ID
     * @return 测试项目集合
     */
    List<DtoAnalyzeItem> findAnalyzeItemBySampleIds(List<String> sampleIds);


    /**
     * 取消处置
     *
     * @param ids 需要取消处置的Id
     * @return 取消的样品个数
     */
    Integer cancelDispose(List<String> ids);

    /**
     * 根据样品id列表查询样品详细信息
     *
     * @param sampleIds 样品id
     * @return 查询结果
     */
    Map<String, Object> findSampleDetails(List<String> sampleIds);

}
