package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoImportStandardMethod;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 标准方法导入校验处理类
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Data
public class StandardMethodVerifyHandle implements IExcelVerifyHandler<DtoImportStandardMethod> {

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportStandardMethod dto) {
        // 导入数据处理
        try {
            // 跳过空行
            if (importUtils.checkObjectIsNull(dto)) {
                return new ExcelVerifyHandlerResult(true);
            }
            // 前后去空格
            importUtils.strToTrim(dto);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }

        // 校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        // 校验错误信息
        StringBuilder failStr = new StringBuilder("第" + dto.getRowNum() + "行数据校验错误");

        // 必填项校验
        importUtils.checkIsNull(result, dto.getMethodId(), "方法id", failStr);
        importUtils.checkIsNull(result, dto.getItemName(), "项目名称", failStr);
        importUtils.checkIsNull(result, dto.getSampleType(), "监测类别", failStr);
        importUtils.checkIsNull(result, dto.getMethodName(), "标准名称", failStr);

        // 处理校验字符串
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }
} 