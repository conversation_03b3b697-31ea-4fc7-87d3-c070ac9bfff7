package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.criteria.EvaluationCriteriaCriteria;
import com.sinoyd.base.dto.rcc.*;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.EvaluationCriteriaRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.customer.DtoExportNewSearchResult;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpEvaluationCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;
import com.sinoyd.lims.lim.entity.NewSearchResult;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.NewSearchResultRepository;
import com.sinoyd.lims.lim.service.ExpImpEvaluationCriteriaService;
import com.sinoyd.lims.lim.service.ExportNewSearchResultService;
import com.sinoyd.lims.lim.service.ImportExcelService;
import com.sinoyd.lims.lim.service.NewSearchResultService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 查新结果导出接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/5
 * @since V100R001
 */
@Service
public class ExpImpNewSearchResultServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoNewSearchResult, String, NewSearchResultRepository> implements ExportNewSearchResultService {

    private NewSearchResultService newSearchResultService;

    private ImportUtils importUtils;

    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoNewSearchResult> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        newSearchResultService.findByPage(page, baseCriteria);
        List<DtoNewSearchResult> newSearchResultList = page.getData();
        List<DtoExportNewSearchResult> exportNewSearchResults = new ArrayList<>();
        Date date1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        for (DtoNewSearchResult newSearchResult : newSearchResultList) {
            DtoExportNewSearchResult export = new DtoExportNewSearchResult();
            BeanUtils.copyProperties(newSearchResult, export);
            export.setIsNewStandard(newSearchResult.getIsNewStandard() ? "是" : "否");
            export.setIsConfirm(newSearchResult.getIsConfirm() ? "是" : "否");
            export.setIsPropagate(newSearchResult.getIsPropagate() ? "是" : "否");
            export.setConfirmation(newSearchResult.getConfirmation() ? "是" : "否");
            export.setPropagate(newSearchResult.getPropagate() ? "是" : "否");
            export.setNewSearchDate(dateToStr(newSearchResult.getNewSearchDate(),date1753));
            export.setEffectiveDate(dateToStr(newSearchResult.getEffectiveDate(),date1753));
            export.setReleaseDate(dateToStr(newSearchResult.getReleaseDate(),date1753));
            export.setStatus(EnumLIM.EnumNewSearchStatus.getByValue(newSearchResult.getStatus()));
            exportNewSearchResults.add(export);
        }
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExportNewSearchResult.class, exportNewSearchResults);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    private String dateToStr(Date date, Date date1753) {
        String resultDate = "";
        if (StringUtil.isNotNull(date) && date.compareTo(date1753) != 0) {
            resultDate = DateUtil.dateToString(date, DateUtil.YEAR);
        }
        return resultDate;
    }

    /**
     * 处理导入表格
     *
     * @param file      传入的文件
     * @param objectMap
     * @param response
     * @return List<T>
     */
    @Override
    public List<DtoNewSearchResult> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        return null;
    }

    /**
     * 导入到数据库
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoNewSearchResult> data) {

    }

    /**
     * 获取文件需要导入的数据
     *
     * @param file     传入的文件
     * @param response
     * @return List
     */
    @Override
    public ExcelImportResult<DtoExportNewSearchResult> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    @Autowired
    public void setNewSearchResultService(NewSearchResultService newSearchResultService) {
        this.newSearchResultService = newSearchResultService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }
}
