package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTest2Instrument;
import com.sinoyd.lims.lim.service.Test2InstrumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 使用仪器
 * <p>
 * <AUTHOR> 修改：guqx
 * @version V1.0.0 2019/1/17
 * @since V100R001
 */
@Api(tags = "使用仪器: 使用仪器服务")
@RestController
@RequestMapping("/api/lim/test2Instrument")
@Validated
public class Test2InstrumentController extends BaseJpaController<DtoTest2Instrument, String, Test2InstrumentService>
{
    /**
     * 新增使用仪器(去重)
     * @param test
     */
    @ApiOperation(value = "新增使用仪器", notes = "新增使用仪器")
    @PostMapping("/addTestInstrument")
    public RestResponse<Integer> save(@Validated @RequestBody DtoTest test)
    {
//        String [] insIds = instrumentIds.replace("[", "").replace("]", "").split(",");
//        ArrayList<String> ids = new ArrayList<String>();
//        for (String id : insIds) {
//            ids.add(id);
//        }
        RestResponse<Integer> response = new RestResponse<Integer>();
        response.setRestStatus(ERestStatus.SUCCESS);
            response.setData(service.addTestInstrument(test.getId(), test.getInstrumentIds(), test.getUseType()));
        response.setCount(1);
        
        return response;
    }

    /**
     * 真删
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id删除使用仪器", notes = "根据id删除使用仪器")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id)
    {
        RestResponse<String> response = new RestResponse<>();
        response.setRestStatus(ERestStatus.SUCCESS);
        response.setCount(service.logicDeleteById(id));
        
        return response;
    }

    /**
     * 真删 批量
     * @param test
     * @return
     */
    @ApiOperation(value = "根据id批量删除使用仪器", notes = "根据id批量删除使用仪器")
    @DeleteMapping("/deleteTestInstrument")
    public RestResponse<String> deleteTestInstrument(@RequestBody DtoTest test)
    {
        RestResponse<String> response = new RestResponse<>();
        response.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.deleteTestInstrument(test.getId(), test.getInstrumentIds(), test.getUseType());
        response.setCount(count);
        
        return response;
    }

    /**
     * 获取使用仪器列表
     * @param test
     * @return
     */
    @ApiOperation(value = "获取使用仪器列表", notes = "获取使用仪器列表")
    @PostMapping("/getList")
    public RestResponse<List<DtoInstrument>> getList(@Validated @RequestBody DtoTest test)
    {
        RestResponse<List<DtoInstrument>> response =  new RestResponse<>();
        List<DtoInstrument> list=service.getList(test.getId(), test.getUseType());
        response.setData(list);
        response.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        response.setCount(list.size());
       
        return response;
    }

}