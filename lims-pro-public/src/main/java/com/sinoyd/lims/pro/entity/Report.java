package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;


/**
 * Report实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Report")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Report extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  Report() {
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
    * 报告类型id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告类型id")
	private String reportTypeId;

    /**
    * 项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目id")
	private String projectId;

    /**
    * 报告状态
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告状态")
    //@NotBlank(message = "报告状态{validation.message.blank}")
    @Length(message = "报告状态{validation.message.length}", max = 50)
	private String status;

    /**
     * 扣发状态(0:未扣发 1:已扣发)
     */
    @ApiModelProperty("扣发状态")
    private Integer depriveStatus;

    /**
    * 数据变更状态( 枚举EnumReportChangeStatus1.未变更 2.已变更)（针对已经编制报告的数据修改状态-样品数据增删改）
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("数据变更状态( 枚举EnumReportChangeStatus1.未变更 2.已变更)（针对已经编制报告的数据修改状态-样品数据增删改）")
	private Integer dataChangeStatus;

    /**
     * 报告发放回收状态
     */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("报告发放回收状态")
    private Integer grantStatus;

    /**
    * 报告编号
    */
    @Column(length=100)
    @ApiModelProperty("报告编号")
    @Length(message = "报告编号{validation.message.length}", max = 100)
	private String code;

    /**
    * 点位名称（单位名称）
    */
    @Column(length=100)
    @ApiModelProperty("点位名称（单位名称）")
    @Length(message = "点位名称（单位名称）{validation.message.length}", max = 100)
	private String folderName;

    /**
    * 样品名称
    */
    @Column(length=50)
    @ApiModelProperty("样品名称")
    @Length(message = "样品名称{validation.message.length}", max = 50)
	private String sampleName;

    /**
    * 检验类别
    */
    @Column(length=50)
    @ApiModelProperty("检验类别")
    @Length(message = "检验类别{validation.message.length}", max = 50)
	private String testName;

    /**
    * 添加人员
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("添加人员")
	private String createPersonId;

    /**
    * 添加时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("添加时间")
	private Date createTime;

    /**
    * 报告年份
    */
    @Column(length=10)
    @ApiModelProperty("报告年份")
    @Length(message = "报告年份{validation.message.length}", max = 10)
	private String reportYear;

    /**
    * 防伪码
    */
    @Column(length=100)
    @ApiModelProperty("防伪码")
    @Length(message = "防伪码{validation.message.length}", max = 100)
	private String securityCode;

    /**
    * 报告持证人员id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("报告持证人员id")
	private String certifiedPersonId;

    /**
     * 是否加盖CMA章
     */
    @Column
    @ApiModelProperty("是否加盖CMA章")
    private Integer isCMA;

    /**
     * 监管平台报告类型
     */
    @Column(length=50)
    @ApiModelProperty("监管平台报告类型")
    @Length(message = "监管平台报告类型，多个id用英文逗号分隔{validation.message.length}", max = 50)
    private String regulateReportType;

    /**
     * 监管平台系统编号
     */
    @Column(length=50)
    @ApiModelProperty("监管平台系统编号")
    @Length(message = "监管平台系统编号{validation.message.length}", max = 50)
    private String regulateCode;

    /**
     * * 报告份数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("报告份数")
    private Integer reportNum;

    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("一审人id")
    private String firstInstanceId;

    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("二审人id")
    private String secondInstanceId;

    /**
     * 分析项目排序id
     */
    @Column
    @ApiModelProperty("分析项目排序id")
    private String analyseItemSortId;

    /**
     * 点位排序id
     */
    @Column
    @ApiModelProperty("点位排序id")
    private String folderSortId;

    /**
     * 监测报表标识
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("监测报表标识")
    private String monitorReportConfigId;

    /**
     * 例行报告名称
     */
    @Column
    @ApiModelProperty("上报报告名称")
    private String monitorReportName;

    /**
     * 报告盖章  监测专用章、CMA章、CNAS章（多选）
     */
    @Column(length = 50)
    @Length(message = "报告盖章{validation.message.length}", max = 50)
    private String reportStamp;

    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;

    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;

    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;

    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;

    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

 }