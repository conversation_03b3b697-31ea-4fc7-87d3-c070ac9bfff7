package com.sinoyd.lims.lim.criteria;

import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;

/**
 * ReportConfig查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportConfigCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    private String id;
    /**
     * 报表类型（枚举：EnumReportConfigType:1文件，2统计面板，3json数据源)
     */
    private Integer type;

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 是否禁用
     */
    private Integer isForbidden = EnumLIM.EnumQCRangeStatus.所有.getValue();

    /**
     * 业务分类（枚举：EnumRecordType（1.报表 2.原始记录单）
     */
    private Integer bizType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        if (StringUtil.isNotEmpty(this.reportCode)) {
            condition.append(" and reportCode like :reportCode");
            values.put("reportCode", "%" + this.reportCode + "%");
        }
        if (StringUtil.isNotEmpty(this.templateName)) {
            condition.append(" and templateName like :templateName");
            values.put("templateName", "%" + this.templateName + "%");
        }
        if (StringUtil.isNotNull(this.type) && this.type != -1) {
            condition.append(" and type = :type");
            values.put("type", this.type);
        }
        if (StringUtil.isNotNull(this.bizType) && this.bizType != -1) {
            condition.append(" and bizType = :bizType");
            values.put("bizType", this.bizType);
        }

        if(!isForbidden.equals(EnumLIM.EnumQCRangeStatus.所有.getValue())){
            condition.append(" and exists (select 1 from DtoReportApply b where b.reportConfigId = a.id and b.isShow = :isShow)");
            values.put("isShow", this.isForbidden);
        }
        return condition.toString();
    }
}