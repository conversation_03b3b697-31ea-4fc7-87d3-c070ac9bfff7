package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.SerialIdentifierConfig;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoSerialIdentifierConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_SerialIdentifierConfig")
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoSerialIdentifierConfig extends SerialIdentifierConfig {
   private static final long serialVersionUID = 1L;
 }