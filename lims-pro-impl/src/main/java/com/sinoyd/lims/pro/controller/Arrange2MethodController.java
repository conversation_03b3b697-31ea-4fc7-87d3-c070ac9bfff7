package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoArrange2Method;
import com.sinoyd.lims.pro.service.Arrange2MethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采样方法服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2024/7/25
 * @since V100R001
 */
@Api(tags = "示例: 采样方法服务")
@RestController
@RequestMapping("api/pro/arrange2Method")
public class Arrange2MethodController extends BaseJpaController<DtoArrange2Method, String, Arrange2MethodService> {

    /**
     * 批量保存
     *
     * @param list 参数
     * @return RestResponse<List<DtoArrange2Method>>
     */
    @ApiOperation(value = "批量保存", notes = "批量保存")
    @PostMapping
    public RestResponse<List<DtoArrange2Method>> batchSave(@RequestBody List<DtoArrange2Method> list) {
        RestResponse<List<DtoArrange2Method>> response = new RestResponse<>();
        response.setData(service.save(list));
        return response;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping
    public RestResponse<Void> delete(@RequestBody List<String> ids) {
        RestResponse<Void> response = new RestResponse<>();
        service.logicDeleteById(ids);
        return response;
    }

}
