package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.pro.dto.DtoReport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ReportRecoverService;
import com.sinoyd.lims.pro.criteria.ReportRecoverCriteria;
import com.sinoyd.lims.pro.dto.DtoReportRecover;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * reportrecover服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
 @Api(tags = "示例: reportrecover服务")
 @RestController
 @RequestMapping("api/pro/reportRecover")
 public class ReportRecoverController extends BaseJpaController<DtoReportRecover, String, ReportRecoverService> {


    /**
     * 分页动态条件查询reportrecover
     * @param reportRecoverCriteria 条件参数
     * @return RestResponse<List<reportRecover>>
     */
     @ApiOperation(value = "分页动态条件查询reportRecover", notes = "分页动态条件查询reportRecover")
     @GetMapping
     public RestResponse<List<DtoReportRecover>> findByPage(ReportRecoverCriteria reportRecoverCriteria) {
         PageBean<DtoReportRecover> pageBean = super.getPageBean();
         RestResponse<List<DtoReportRecover>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, reportRecoverCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

    /**
          * 项目Id查询回收信息
          *
          * @param projectId 项目Id
          * @param grantStatus 发放状态
          * @return RestResponse<List<DtoReportRecover>>
          */
    @ApiOperation(value = "项目Id查询回收信息", notes = "项目Id查询回收信息")
    @GetMapping("/findRecover")
    public RestResponse<List<DtoReportRecover>> findRecover(String projectId,String grantStatus){
        RestResponse<List<DtoReportRecover>> response = new RestResponse<>();
        response.setData(service.findRecover(projectId, grantStatus));
        response.setMsg("查询成功");
        return response;
    }

    /**
          * 项目Id查询发放状态为已发放的报告
          *
          * @param projectId 项目Id
          * @return RestResponse<List<DtoReport>>
          */
    @ApiOperation(value = "项目Id查询回收信息", notes = "项目Id查询回收信息")
    @GetMapping("/findReport/{projectId}")
    public RestResponse<List<DtoReport>> findReport(@PathVariable("projectId") String projectId){
        RestResponse<List<DtoReport>> response = new RestResponse<>();
        response.setData(service.findReport(projectId));
        response.setMsg("查询成功");
        return response;
    }

    /**
     * 保存回收信息
     *
     * @param reportRecover 需要添加的数据List
     * @return RestResponse<List<DtoReportRecover>>
     */
    @ApiOperation(value = "保存回收信息", notes = "保存回收信息")
    @PostMapping("/saveReportRecover")
    public RestResponse<List<DtoReportRecover>> save(@RequestBody List<DtoReportRecover> reportRecover){
        RestResponse<List<DtoReportRecover>> response = new RestResponse<>();
        response.setData(service.save(reportRecover));
        response.setMsg("保存成功");
        return response;
    }

    /**
     * 根据Id删除回收信息
     *
     * @param id 回收信息Id
     * @return RestResponse<Integer>
     */
    @ApiOperation(value = "根据Id删除回收信息", notes = "根据Id删除回收信息")
    @DeleteMapping("/{id}")
    public RestResponse<Integer> logicDelete(@PathVariable String id){
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.logicDeleteById(id));
        response.setMsg("删除成功");
        return response;
    }

    /**
     * 根据Id删除回收信息
     *
     * @param ids 回收信息Id
     * @return RestResponse<Integer>
     */
    @ApiOperation(value = "删除回收信息", notes = "删除回收信息")
    @DeleteMapping
    public RestResponse<Integer> logicDelete(@RequestBody List<String> ids){
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.logicDeleteById(ids));
        response.setMsg("删除成功");
        return response;
    }
 }