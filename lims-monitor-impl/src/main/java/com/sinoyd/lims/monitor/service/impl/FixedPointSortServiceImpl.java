package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSort;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSortDetil;
import com.sinoyd.lims.monitor.dto.rcc.*;
import com.sinoyd.lims.monitor.repository.lims.FixedPointSortDetilRepository;
import com.sinoyd.lims.monitor.repository.lims.FixedPointSortRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.monitor.service.FixedPointSortService;
import com.sinoyd.lims.monitor.service.FixedpointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * FixedPointSort操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class FixedPointSortServiceImpl extends BaseJpaServiceImpl<DtoFixedPointSort, String, FixedPointSortRepository> implements FixedPointSortService {

    @Autowired
    private FixedPointSortDetilRepository fixedPointSortDetilRepository;

    @Autowired
    private FixedpointRepository fixedpointRepository;

    @Autowired
    private CodeService codeService;

    @Autowired
    @Lazy
    private FixedpointService fixedpointService;


    @Override
    public void findByPage(PageBean<DtoFixedPointSort> pb, BaseCriteria fixedPointSortCriteria) {
        pb.setEntityName("DtoFixedPointSort a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fixedPointSortCriteria);
    }

    @Transactional
    @Override
    public DtoFixedPointSort save(DtoFixedPointSort entity) {
        //根据排序名称查找是否存在
        if (StringUtil.isNotEmpty(entity.getSortName())) {
            Integer num = repository.countBySortName(entity.getSortName());
            if (num != 0) {
                throw new BaseException("已存在类型名称为" + entity.getSortName());
            }
        }
        return super.save(entity);
    }

    @Transactional
    @Override
    public DtoFixedPointSort update(DtoFixedPointSort entity) {
        if (StringUtil.isNotEmpty(entity.getSortName())) {
            Integer num = repository.countByIdNotAndSortName(entity.getId(), entity.getSortName());
            if (num != 0) {
                throw new BaseException("已存在类型名称为" + entity.getSortName());
            }
        }
        return super.save(entity);
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        //级联删除点位排序详情
        fixedPointSortDetilRepository.deleteBySortIdIn(idList);
        return super.logicDeleteById(idList);
    }

    @Override
    public DtoFixedPointSort findOne(String id) {
        DtoFixedPointSort dtoFixedPointSort = super.findOne(id);
        //根据sortId查询点位排序表的集合
        List<DtoFixedPointSortDetil> dtoList = fixedPointSortDetilRepository.findBySortId(id);
        //获取排序表的id集合
        List<String> fixedList = dtoList.parallelStream().map(DtoFixedPointSortDetil::getFixedPointId).collect(Collectors.toList());
        //根据排序表的id集和查询点位表的集合对象
        List<DtoFixedpoint> dtoFixedpoints = fixedpointRepository.findByIdIn(fixedList);
        //点位类型字典列表
        List<DtoCode> folderTypeCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.ENV_QUALITY_FOLDER_TYPE);
        //评价标准列表
        List<DtoEvaluationCriteria> evaluationCriteriaList = fixedpointService.loadEvaluationCriteria(dtoFixedpoints);
        //评价等级id列表
        List<DtoEvaluationLevel> evaluationLevelList = fixedpointService.loadEvaluationLevel(dtoFixedpoints);
        //水体列表
        List<DtoWater> waterList = fixedpointService.loadWaterByFixedPoint(dtoFixedpoints);
        //等级字典列表
        List<DtoCode> levelCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.FOLDER_LEVEL);
        //将点位类型查到
        if (dtoFixedpoints != null ){
            for (DtoFixedpoint dto: dtoFixedpoints) {
                //冗余点位类型名称
                Optional<DtoCode> folderTypeOptional = folderTypeCodeList.parallelStream().filter(p -> p.getDictCode().equals(dto.getFolderType()))
                        .findFirst();
                if (folderTypeOptional.isPresent()) {
                    dto.setFolderTypeName(folderTypeOptional.get().getDictName());
                }
                //冗余评价标准名称
                Optional<DtoEvaluationCriteria> evaluationCriteriaOptional = evaluationCriteriaList.parallelStream().filter(p -> p.getId().equals(dto.getEvaluationId()))
                        .findFirst();
                if (evaluationCriteriaOptional.isPresent()) {
                    dto.setEvaluationName(evaluationCriteriaOptional.get().getName());
                }
                //冗余评价等级名称
                Optional<DtoEvaluationLevel> evaluationLevelOptional = evaluationLevelList.parallelStream().filter(p -> p.getId().equals(dto.getEvaluationLevelId()))
                        .findFirst();
                if (evaluationLevelOptional.isPresent()) {
                    dto.setEvaluationLvlName(evaluationLevelOptional.get().getName());
                }
                //冗余所属水体名称
                Optional<DtoWater> waterOptional = waterList.parallelStream().filter(p -> p.getId().equals(dto.getWaterId()))
                        .findFirst();
                if (waterOptional.isPresent()) {
                    dto.setWaterName(waterOptional.get().getWaterName());
                }
                //冗余点位等级名称
                Optional<DtoCode> levelOptional = levelCodeList.parallelStream().filter(p -> p.getDictCode().equals(dto.getLevel()))
                        .findFirst();
                if (levelOptional.isPresent()) {
                    dto.setLevelName(levelOptional.get().getDictName());
                }
            }
        }
        //遍历点位表
        for (DtoFixedpoint dtoFixedpoint: dtoFixedpoints) {
            //遍历点位排序表
            for (DtoFixedPointSortDetil dtoFixedPointSortDetil: dtoList) {
                //找到排序表和关联表的数据，将排序表的orderNum字段放入点位表的orderNum
                if (dtoFixedpoint.getId().equals(dtoFixedPointSortDetil.getFixedPointId())){
                    dtoFixedpoint.setOrderNumForSort(dtoFixedPointSortDetil.getOrderNum());
                }
            }
        }
        //通过点位表冗余的orderNum字段进行排序
        dtoFixedpoints.sort(Comparator.comparing(DtoFixedpoint::getOrderNumForSort, Comparator.reverseOrder()));
        dtoFixedPointSort.setDtoFixedpoint(dtoFixedpoints);
        return dtoFixedPointSort;
    }
}