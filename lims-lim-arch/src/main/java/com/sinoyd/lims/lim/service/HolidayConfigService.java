package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoHolidayConfig;
import com.sinoyd.lims.lim.vo.WorkHolidayConfigVO;

import java.util.List;

/**
 * 节假日管理配置接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/18
 */
public interface HolidayConfigService extends IBaseJpaService<DtoHolidayConfig, String> {

    /**
     * 根据年份查询
     *
     * @param year 年份
     * @return 节假日管理配置List
     */
    List<DtoHolidayConfig> findByYear(Integer year);

    /**
     * 保存工作日以及节假日配置，并生成日期日历
     *
     * @param vo 工作日以及节假日配置
     * @return List<DtoHolidayConfig>
     */
    List<DtoHolidayConfig> save(WorkHolidayConfigVO vo);

}