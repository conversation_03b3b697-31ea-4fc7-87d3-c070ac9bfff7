package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.test;

import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTestTemp;
import com.sinoyd.lims.pro.dto.customer.DtoProjectTest;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component(IFileNameConstant.SchemeSynchronizationKey.UPDATE_TEST)
public class UpdateTestStrategy extends AbsTestStrategy {

    @Override
    public void synchronizationTest(List<DtoSamplingFrequencyTestTemp> samplingFrequencyTestTempList) {
        Map<String, List<DtoSamplingFrequencyTestTemp>> frequencyMap = samplingFrequencyTestTempList.stream()
                .collect(Collectors.groupingBy(DtoSamplingFrequencyTestTemp::getSamplingFrequencyId));
        Set<String> tempIds = samplingFrequencyTestTempList.stream()
                .map(DtoSamplingFrequencyTestTemp::getSamplingFrequencyTempId).collect(Collectors.toSet());
        //找到其他对应的无修改的指标
        List<DtoSamplingFrequencyTestTemp> otherTestTempList = samplingFrequencyTestTempRepository
                .findBySamplingFrequencyTempIdIn(tempIds).stream()
                .filter(p -> p.getOperateType().equals(EnumLIM.EnumOperateType.无修改.getValue()))
                .collect(Collectors.toList());
        for (String frequencyKey : frequencyMap.keySet()) {
            List<DtoProjectTest> testList = new ArrayList<>();
            String tempId = frequencyMap.get(frequencyKey).get(0).getSamplingFrequencyTempId();
            List<DtoSamplingFrequencyTestTemp> otherTest = otherTestTempList.stream()
                    .filter(p -> p.getSamplingFrequencyTempId().equals(tempId))
                    .collect(Collectors.toList());
            otherTest.addAll(frequencyMap.get(frequencyKey));
            otherTest.forEach(p -> {
                DtoProjectTest test = new DtoProjectTest();
                test.setAnalyseItemId(p.getAnalyseItemId());
                test.setIsOutsourcing(p.getIsOutsourcing());
                test.setIsSamplingOut(p.getIsSamplingOut());
                testList.add(test);
            });
            sampleFolderService.sub(Collections.singletonList(frequencyKey), testList);
        }
    }
}
