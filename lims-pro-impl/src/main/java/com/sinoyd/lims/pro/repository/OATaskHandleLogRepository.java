package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoOATaskHandleLog;

import java.util.Collection;
import java.util.List;

/**
 * 审批任务流程日志数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
public interface OATaskHandleLogRepository extends IBaseJpaRepository<DtoOATaskHandleLog, String> {
    /**
     * 流程日志
     * @param taskId 流程id
     * @return 流程日志
     */
    List<DtoOATaskHandleLog> findByTaskId(String taskId);

    /**
     * 获取审批任务的流程日志
     *
     * @param taskIds 审批任务id集合
     * @return 审批任务的流程日志
     */
    List<DtoOATaskHandleLog> findByTaskIdIn(Collection<String> taskIds);
}
