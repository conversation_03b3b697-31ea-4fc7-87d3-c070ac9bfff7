package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 样品分组仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
public interface SampleTypeGroupRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleTypeGroup, String> {

    /**
     * 样品分组/分组规则删除
     *
     * @param idList 样品分组/分组规则id集合
     * @return 返回样品分组/分组规则实体删除数量
     */
    @Modifying
    @Query("delete DtoSampleTypeGroup p where p.id in :idList or p.parentId in :idList")
    Integer delete(@Param("idList") Collection<String> idList);


    /**
     * 根据分组类型获取样品分组/分组规则
     *
     * @return 返回样品分组/分组规则实体集合
     */
    @Query("select p from DtoSampleTypeGroup p where p.groupType = :groupType")
    List<DtoSampleTypeGroup> getListByGroupType(@Param("groupType") Integer groupType);

    /**
     * 判断是否存在重复样品编号标识
     *
     * @return 相同名称的数量
     */
    @Query("select count(p.id) from DtoSampleTypeGroup p where p.id <> :id and  p.sampleCodeTag = :sampleCodeTag and p.groupType = :groupType and  p.sampleTypeId = :sampleTypeId and p.parentId = :parentId")
    Integer getSampleCodeTagCount(@Param("id") String id, @Param("sampleCodeTag") String sampleCodeTag, @Param("groupType") Integer groupType, @Param("sampleTypeId") String sampleTypeId, @Param("parentId") String parentId);

    /**
     * 根据样品编号标识分组类型检测类型查询集合
     *
     * @return 结果集合
     */
    @Query("select p from DtoSampleTypeGroup p where p.id <> :id and  p.sampleCodeTag = :sampleCodeTag and p.groupType = :groupType and  p.sampleTypeId = :sampleTypeId and p.parentId = :parentId")
    List<DtoSampleTypeGroup> getSampleCodeTagList(@Param("id") String id, @Param("sampleCodeTag") String sampleCodeTag, @Param("groupType") Integer groupType, @Param("sampleTypeId") String sampleTypeId, @Param("parentId") String parentId);


    /**
     * 根据分组规则获取样品分组
     *
     * @return 返回样品分组实体集合
     */
    @Query("select p from DtoSampleTypeGroup p where p.groupType = 2 and p.parentId = :parentId order by orderNum desc")
    List<DtoSampleTypeGroup> getListByParentId(@Param("parentId") String parentId);


    /**
     * 判断是否存在同名
     *
     * @return 相同名称的数量
     */
    @Query("select count(p.id) from DtoSampleTypeGroup p where p.id <> :id and  p.groupName = :groupName and p.groupType = :groupType and  p.sampleTypeId = :sampleTypeId and p.parentId = :parentId")
    Integer getCount(@Param("id") String id, @Param("groupName") String groupName, @Param("groupType") Integer groupType, @Param("sampleTypeId") String sampleTypeId, @Param("parentId") String parentId);

    /**
     * 根据id集合查询样品分组
     *
     * @param ids id list集合
     * @return 查询结果
     */
    List<DtoSampleTypeGroup> findByIdIn(List<String> ids);

    /**
     * 根据样品类型id集合查询样品分组
     *
     * @param sampleTypeIds 样品类型id集合
     * @return 查询结果
     */
    List<DtoSampleTypeGroup> findBySampleTypeIdIn(List<String> sampleTypeIds);

    /**
     * 根据分组id集合查询样品分组详情
     *
     * @param parentIds 分组ids
     * @param groupType 分组类型
     * @return 查询结果
     */
    List<DtoSampleTypeGroup> findByParentIdInAndGroupType(List<String> parentIds, Integer groupType);
}