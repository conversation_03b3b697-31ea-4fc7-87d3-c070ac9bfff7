package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.QrCodeUtil;
import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;
import com.sinoyd.lims.lim.repository.rcc.VersionInfoRepository;
import com.sinoyd.lims.lim.service.VersionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.image.BufferedImage;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * VersionInfo操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/9
 * @since V100R001
 */
@Service
public class VersionInfoServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoVersionInfo, String, VersionInfoRepository> implements VersionInfoService {

    private DocumentService documentService;

    @Value("${mobile.client.download.path:unknownPath}")
    private String downloadPath;

    private FilePathConfig filePathConfig;

    private CodeService codeService;

    @Override
    public void findByPage(PageBean<DtoVersionInfo> pb, BaseCriteria versionInfoCriteria) {
        pb.setEntityName("DtoVersionInfo a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, versionInfoCriteria);
        List<DtoVersionInfo> dataList = pb.getData();
        if (StringUtil.isNotEmpty(dataList)) {
            List<DtoCode> list = codeService.findCodes("LIM_MobileTerminalType");
            for (DtoVersionInfo dto : dataList) {
                for (DtoCode dtoCode : list) {
                    if (dto.getVerType().equals(dtoCode.getDictCode())) {
                        dto.setVerTypeName(dtoCode.getDictName());
                    }
                }
            }
        }
    }


    @Transactional
    @Override
    public DtoVersionInfo save(DtoVersionInfo entity) {
        //根据类别和版本号查找是否存在
        Integer num = repository.countByVerTypeAndVersion(entity.getVerType(), entity.getVersion());
        if (num != 0) {
            throw new BaseException("对应的类别" + entity.getVerType() + "和版本号" + entity.getVersion() + "已存在");
        }
        super.save(entity);
        return entity;
    }

    @Transactional
    @Override
    public DtoVersionInfo update(DtoVersionInfo entity) {
        //如果前端传的类别和版本号不为空
        if (entity.getVerType() != null && entity.getVersion() != null) {
            //根据类别和版本号查找是否存在
            Integer num = repository.countByVerTypeAndVersion(entity.getVerType(), entity.getVersion());
            DtoVersionInfo dtoVersionInfo = repository.findByVerTypeAndVersion(entity.getVerType(), entity.getVersion());
            if (num >= 1) {
                if (!entity.getId().equals(dtoVersionInfo.getId())) {
                    throw new BaseException("对应的类别" + entity.getVerType() + "和版本号" + entity.getVersion() + "已存在");
                }
            }
        }
        return super.update(entity);
    }

    @Override
    public DtoVersionInfo createQrCode(DtoVersionInfo versionInfo) {
        DtoVersionInfo entity = repository.findOne(versionInfo.getId());
        //Map参数(用户获取二维码和附件路径)
        Map<String, Object> map = new HashMap<>();
        map.put("verType", entity.getVerType());
        map.put("version", entity.getVersion());
        //设置附件路径
        String attachmentPath = "/" + documentService.getDocumentPathByPlaceholder("versionInfo", map);
        entity.setVerUrl(attachmentPath + "/" + versionInfo.getDocumentName());
        if (StringUtil.isNotEmpty(versionInfo.getDocumentId())) {
            //拼接下载路径放到二维码中
            String appDownloadUrl = downloadPath + "/" + versionInfo.getDocumentId();
            BufferedImage image = QrCodeUtil.createImageCode(appDownloadUrl, "UTF-8", 240);
            try {
                String path = "/" + documentService.getDocumentPathByPlaceholder("versionQrCode", map);
                String fileName = String.format("%s二维码.png", entity.getVerType() + "_" + entity.getVersion() + "_");
                QrCodeUtil.saveImage(filePathConfig.getFilePath() + path, image, fileName);
                //设置二维码路径
                entity.setVerCode(path + "/" + fileName);
                entity.setBase64Content(documentService.convertBase64Content(entity.getVerCode()));
                entity.setCodeUrl(documentService.convertBase64Content(entity.getVerCode()));
            } catch (Exception ex) {
                throw new BaseException("生成二维码失败");
            }
            repository.save(entity);
            return entity;
        }
        return null;
    }

    @Override
    public DtoVersionInfo findAttachPath(String id) {
        return findOne(id);
    }

    @Autowired
    @Lazy
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }
}