DROP TABLE IF EXISTS TB_PRO_Project2Report;
CREATE TABLE TB_PRO_Project2Report  (
    id varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
    projectId varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '项目id',
    reportId varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报告id',
    uploadStatus int(11) NOT NULL DEFAULT 0 COMMENT '上传状态 0 : 未上传 1：已上传',
    PRIMARY KEY (`id`)
) COMMENT = '项目和报告关联表'
