package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForSampleData;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * PerformanceStatisticForSampleData数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
public interface PerformanceStatisticForSampleDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoPerformanceStatisticForSampleData, String> {

    /**
     * 删除送样单相关的采样项目数据
     *
     * @param recordIds 送样单
     * @return 返回删除的行数
     */
    @Transactional
    @Modifying
    @Query("delete  from  DtoPerformanceStatisticForSampleData where receiveId in :recordIds")
    Integer deleteByReceiveIdIn(@Param("recordIds") List<String> recordIds);
}