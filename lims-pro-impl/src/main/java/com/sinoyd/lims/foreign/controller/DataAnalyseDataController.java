package com.sinoyd.lims.foreign.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataAnalyseDataVO;
import com.sinoyd.lims.foreign.service.DataAnalyseDataService;
import com.sinoyd.lims.foreign.service.impl.DataAnalyseDataServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 数据信息服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataAnalyseData服务")
@RestController
@RequestMapping("api/pro/foreign/analyseData")
public class DataAnalyseDataController extends ExceptionHandlerController<DataAnalyseDataService> {


    /**
     * 数据同步
     *
     * @param map 参数
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataAnalyseDataVO>> getSyncData(@RequestBody Map<String, Object> map) {
        List<DataAnalyseDataVO> dataAnalyseDatas = service.getSyncData(map);
        RestResponse<List<DataAnalyseDataVO>> response = new RestResponse();
        response.setData(dataAnalyseDatas);
        return response;
    }

}
