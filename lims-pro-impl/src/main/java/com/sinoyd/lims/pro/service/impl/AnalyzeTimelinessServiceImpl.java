package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.lims.lim.dto.customer.DtoExportEvaluationCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoPersonQuery;
import com.sinoyd.lims.lim.dto.rcc.DtoCalendarDate;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.service.CalendarDateService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.criteria.AnalyzeTimelinessCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.rowMapper.AnalyzeTimelinessRowMapper;
import com.sinoyd.lims.pro.service.AnalyseTimelinessFutureService;
import com.sinoyd.lims.pro.service.AnalyzeTimelinessChartService;
import com.sinoyd.lims.pro.service.AnalyzeTimelinessDataService;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordService;
import com.sinoyd.lims.pro.vo.AnalyseDataCalculationVo;
import com.sinoyd.lims.pro.vo.AnalyzeDetailDataVO;
import com.sinoyd.lims.pro.vo.AnalyzeTimelinessMapVO;
import com.sinoyd.lims.pro.vo.AnalyzeTimelinessVO;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 及时率统计业务实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/31
 */
@Service
@Slf4j
public class AnalyzeTimelinessServiceImpl implements AnalyzeTimelinessDataService, AnalyzeTimelinessChartService {

    private RedisTemplate redisTemplate;

    private CommonRepository commonRepository;

    private CodeService codeService;

    private NamedParameterJdbcTemplate jdbcTemplate;

    private ReceiveSampleRecordService sampleRecordService;

    private CalendarDateService calendarDateService;

    private final String code = "BASE_ExpirationAlertTime_AnalyseData";

    private PersonService personService;

    private AnalyseTimelinessFutureService analyseTimelinessFutureService;


    @Override
    public List<Object> queryTimeliness(BaseCriteria baseCriteria) {
        DtoPersonQuery personQuery = new DtoPersonQuery();
        personQuery.setIsUser(true);
        personQuery.setStatus(Arrays.asList(1, 3));
        personQuery.setPermission(Collections.singletonList("LIMS_PRO_AnalyseData_Show"));
        List<DtoKeyValue> keyValues = personService.query(personQuery);
        AnalyzeTimelinessCriteria criteria = (AnalyzeTimelinessCriteria) baseCriteria;
        List<Object> result = null;
//        Object object = redisTemplate.opsForValue().get(criteria.getQueryTimelinessKey());
//        if (object != null && !"".equals(object)) {
//            result = JsonIterator.deserialize(object.toString(), List.class);
//            result.removeIf(d -> keyValues.stream().noneMatch(k -> k.getValue().equals(((Map) d).get("analystId"))));
//        }
        if (StringUtil.isEmpty(result)) {
            DtoCode dtoCode = codeService.findByCode(code);
            criteria.setWarnDay(dtoCode != null ? Long.parseLong(dtoCode.getDictValue()) : 1);
            List<AnalyzeTimelinessVO> dataList = queryDBTimeliness(criteria);
            // 过滤掉分析人员名称为空的，并按照姓名正序排序
            if (StringUtil.isNotEmpty(dataList)) {
                Comparator<AnalyzeTimelinessVO> comparator = Comparator.comparingDouble(a -> Double.parseDouble(a.getTimelinessRate().split("%")[0]));
                result = dataList.stream().sorted(comparator.thenComparing(AnalyzeTimelinessVO::getAnalystName, Comparator.reverseOrder()).reversed()).collect(Collectors.toList());
                result.removeIf(d -> keyValues.stream().noneMatch(k -> k.getValue().equals(((AnalyzeTimelinessVO) d).getAnalystId())));
//                redisTemplate.opsForValue().set(criteria.getQueryTimelinessKey(), JsonStream.serialize(result), 300, TimeUnit.SECONDS);
            }
        }

        return result;
    }

    @Override
    public void queryDetailData(PageBean<AnalyzeDetailDataVO> pb, BaseCriteria baseCriteria, RestResponse response) {
        Integer page = pb.getPageNo();
        Integer rows = pb.getRowsPerPage();
        AnalyzeTimelinessCriteria criteria = (AnalyzeTimelinessCriteria) baseCriteria;
        criteria.setIsSql(false);
        DtoCode dtoCode = codeService.findByCode(code);
        criteria.setWarnDay(dtoCode != null ? Long.parseLong(dtoCode.getDictValue()) : 1).setIsDataList(true);
//        pb.setEntityName("DtoSample s,DtoAnalyseData a,DtoTest t,DtoSampleType type");
//        pb.setSelect("select new com.sinoyd.lims.pro.vo.AnalyzeDetailDataVO(s.sampleTypeId,type.typeName as sampleTypeName,s.code as sampleCode,a.redAnalyzeItemName as analyzeItemName," +
//                "a.status,s.samplingTimeBegin as samplingDate ,a.analyzeTime,a.analystName,t.analyseDayLen,s.receiveId)");
//        pb.setSort("samplingDate-, sampleTypeName+, analyzeItemName+, sampleCode+");
        pb.setEntityName("DtoAnalyseStatisticsData a");
        pb.setSelect("select new com.sinoyd.lims.pro.vo.AnalyzeDetailDataVO(a.sampleTypeId,a.sampleTypeName as sampleTypeName,a.sampleCode as sampleCode,a.redAnalyzeItemName as analyzeItemName," +
                "a.status,a.samplingTimeBegin as samplingDate ,a.analyzeTime,a.analystName,a.analyseDayLen,a.receiveId,a.receiveSampleDate as receiveDate)");
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        Date d1 = new Date();
        commonRepository.findByPage(pb, criteria);
        Date d2 = new Date();
        log.info("查询数据详情耗时 ： " + (d2.getTime() - d1.getTime()));
        fillingPageTransientFields(pb.getData());
        filterData(pb, criteria);
        List<AnalyzeDetailDataVO> dataVOList = pb.getData();
        dataVOList.sort(getComparator());
        Date d3 = new Date();
        log.info("处理数据耗时 ： " + (d3.getTime() - d2.getTime()));
        response.setCount(pb.getData().size());
        pb.setData(pb.getData().stream().skip((page - 1) * rows).limit(rows).collect(Collectors.toList()));
    }

    @Override
    public Map<String, Object> totalTimelinessChart(BaseCriteria baseCriteria) {
        AnalyzeTimelinessCriteria criteria = (AnalyzeTimelinessCriteria) baseCriteria;
        Map<String, Object> map = null;
//        Object object = redisTemplate.opsForValue().get(criteria.getTimelinessChartKey());
//        if (object != null && !"".equals(object)) {
//            map = JsonIterator.deserialize(object.toString(), Map.class);
//        }
        if (StringUtil.isEmpty(map)) {
//            List<AnalyzeTimelinessVO> dataList = queryDBTimeliness(criteria);
            List<AnalyzeTimelinessVO> dataList = queryDBTimeliness(criteria);
            String rateStr = "";
            String total = "0";
            String promptness = "0";
            if (StringUtil.isNotEmpty(dataList)) {
                //分析项目总数
                int totalCount = dataList.stream().mapToInt(AnalyzeTimelinessVO::getTotalCount).sum();
                if (totalCount != 0) {
                    total = String.valueOf(totalCount);
                    //已超期
                    int overdueCount = dataList.stream().mapToInt(AnalyzeTimelinessVO::getOverdueCount).sum();
                    //及时率
                    BigDecimal rate = new BigDecimal(Math.subtractExact(totalCount, overdueCount)).multiply(new BigDecimal(100))
                            .divide(new BigDecimal(totalCount), 1, BigDecimal.ROUND_HALF_UP);
                    promptness = new BigDecimal(Math.subtractExact(totalCount, overdueCount)) + "";
                    rateStr = rate + "%";
                }
            }
            map = new ConcurrentHashMap<>();
            map.put("rate", rateStr);
            map.put("total", total);
            map.put("promptness", promptness);
//            redisTemplate.opsForValue().set(criteria.getTimelinessChartKey(), JsonStream.serialize(map), 300, TimeUnit.SECONDS);
        }
        return map;
    }


    @Override
    public Map<String, List<Object>> histogramChart(BaseCriteria baseCriteria) {
        AnalyzeTimelinessCriteria criteria = (AnalyzeTimelinessCriteria) baseCriteria;
        Map<String, List<Object>> result = null;
//        Object object = redisTemplate.opsForValue().get(criteria.getHistogramChartKey());
//        if (object != null && !"".equals(object)) {
//            result = JsonIterator.deserialize(object.toString(), Map.class);
//        }
        if (StringUtil.isEmpty(result)) {
            List<AnalyzeTimelinessVO> dataList = queryDBTimeliness(criteria);
            if (StringUtil.isNotEmpty(dataList)) {
                List<Object> analystNames = dataList.stream().map(AnalyzeTimelinessVO::getAnalystName).collect(Collectors.toList());
                List<Object> count = dataList.stream().map(AnalyzeTimelinessVO::getTotalCount).collect(Collectors.toList());
                List<Object> rate = dataList.stream().map(AnalyzeTimelinessVO::getTimelinessRate).collect(Collectors.toList());
                result = new ConcurrentHashMap<>();
                result.put("analystNames", analystNames);
                result.put("count", count);
                result.put("rate", rate);
//                redisTemplate.opsForValue().set(criteria.getHistogramChartKey(), JsonStream.serialize(result), 300, TimeUnit.SECONDS);
            }
        }
        return result;
    }

    /**
     * 从数据库查询统计率:
     * 只根据采样时间、检测类型、分析人员查询
     *
     * @param baseCriteria 查询条件
     * @return 数据
     */
    private List<AnalyzeTimelinessVO> queryDBTimeliness(BaseCriteria baseCriteria) {
        AnalyzeTimelinessCriteria criteria = (AnalyzeTimelinessCriteria) baseCriteria;
        criteria.setIsSql(true);
        String condition = criteria.getCondition();
        Map<String, Object> values = criteria.getValues();
        String sql = "select a.analystId,a.analystName,a.samplingTimeBegin,a.analyseDayLen,a.analyzeTime,a.dataStatus  " +
                "from TB_PRO_AnalyseStatisticsData a where 1=1 " + condition;
        long t1 = System.currentTimeMillis();
        List<AnalyzeTimelinessMapVO> voList = jdbcTemplate.query(sql, values, new AnalyzeTimelinessRowMapper());
        log.info("原始数据查询：" + (System.currentTimeMillis() - t1));
        return fillingTransientFields(voList);
    }

    /**
     * 填充临时字段用于前端展示
     *
     * @param dtoList sql查询的结果
     * @return 前端展示内容
     */
    private List<AnalyzeTimelinessVO> fillingTransientFields(List<AnalyzeTimelinessMapVO> dtoList) {
        long t1 = System.currentTimeMillis();
        DtoCode dtoCode = codeService.findByCode(code);
        Integer warnDay = dtoCode != null ? Integer.parseInt(dtoCode.getDictValue()) : 1;
        Map<String, List<AnalyzeTimelinessMapVO>> mapGroup = dtoList.stream().filter(item -> StringUtil.isNotEmpty(item.getAnalystId()) && StringUtil.isNotEmpty(item.getAnalystName())).collect(Collectors.groupingBy(AnalyzeTimelinessMapVO::getAnalystId));
        List<AnalyzeTimelinessVO> voList = new ArrayList<>();
        List<DtoCalendarDate> calendarDates = calendarDateService.findByYear(CalendarUtil.getYear(new Date()));
        Map<String, DtoCalendarDate> date2CalendarDate = calendarDates.stream().collect(Collectors.toMap(dto -> DateUtil.dateToString(dto.getCalendarDate(), DateUtil.YEAR), d -> d));
        //分析时间日历
        Calendar calendarAnalyze = Calendar.getInstance();
        for (String key : mapGroup.keySet()) {
            AnalyzeTimelinessVO vo = new AnalyzeTimelinessVO();
            List<AnalyzeTimelinessMapVO> mapVos = mapGroup.get(key);
            vo.setAnalystId(key)
                    .setAnalystName(mapVos.get(0).getAnalystName())
                    .setTotalCount(mapVos.size());
            List<Map<String, Integer>> mapList = new ArrayList<>();
            // 多线程处理预警日期数据
            futureFullAnalyzeTimeliness(mapVos, mapList, date2CalendarDate, warnDay);
            int willOverdueCount = StringUtil.isNotEmpty(mapList) ? mapList.stream().map(p -> p.get("willOverdueCount")).reduce(0, Integer::sum) : 0;
            int overdueCount = StringUtil.isNotEmpty(mapList) ? mapList.stream().map(p -> p.get("overdueCount")).reduce(0, Integer::sum) : 0;
            vo.setOverdueCount(overdueCount)
                    .setWillOverdueCount(willOverdueCount);
            //及时率
            BigDecimal rate = new BigDecimal(Math.subtractExact(vo.getTotalCount(), overdueCount)).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(vo.getTotalCount()), 1, BigDecimal.ROUND_HALF_UP);
            String rateStr = rate + "%";
            vo.setTimelinessRate(rateStr);
            voList.add(vo);
        }
        log.info("附件数据查询：" + (System.currentTimeMillis() - t1));
        return voList;
    }

    /**
     * 多线程处理数据
     *
     * @param voList            统计数据源
     * @param overdueMapList    超期Map
     * @param date2CalendarDate 工作日Map
     * @param warnDay           预警时间
     */
    private void futureFullAnalyzeTimeliness(List<AnalyzeTimelinessMapVO> voList, List<Map<String, Integer>> overdueMapList,
                                             Map<String, DtoCalendarDate> date2CalendarDate, Integer warnDay) {
        List<Future<List<AnalyzeTimelinessMapVO>>> futureList = new ArrayList<>();
        List<AnalyzeTimelinessMapVO> resultList = new ArrayList<>();
        List<AnalyzeTimelinessMapVO> list = null;
        final int batchSize = 1000;
        for (AnalyzeTimelinessMapVO vo : voList) {
            if (list == null) {
                list = new ArrayList<>();
            }
            if (list.size() < batchSize) {
                list.add(vo);
            } else if (list.size() == batchSize) {
                //多线程处理排序
                futureList.add(analyseTimelinessFutureService.handleDate(list, overdueMapList, date2CalendarDate, warnDay));
                list = new ArrayList<>();
                list.add(vo);
            }
        }
        //如果存在最后一批样，需要单独去排序处理
        if (StringUtil.isNotEmpty(list)) {
            futureList.add(analyseTimelinessFutureService.handleDate(list, overdueMapList, date2CalendarDate, warnDay));
        }
        //处理多线程处理的结果
        try {
            for (Future<List<AnalyzeTimelinessMapVO>> analyseDataResult : futureList) {
                while (true) {
                    if (analyseDataResult.isDone() && !analyseDataResult.isCancelled()) {
                        resultList.addAll(analyseDataResult.get());
                        break;
                    } else {
                        //防止CPU高速轮询被耗空
                        Thread.sleep(1);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("......多线程处理分析数据出错......");
        }
    }


    /**
     * 多线程处理数据
     *
     * @param voList            统计数据源
     * @param date2CalendarDate 工作日Map
     */
    private void futureFullAnalyzeDetail(List<AnalyzeDetailDataVO> voList, Map<String, DtoCalendarDate> date2CalendarDate) {
        List<Future<List<AnalyzeDetailDataVO>>> futureList = new ArrayList<>();
        List<AnalyzeDetailDataVO> resultList = new ArrayList<>();
        List<AnalyzeDetailDataVO> list = null;
        final int batchSize = 1000;
        for (AnalyzeDetailDataVO vo : voList) {
            if (list == null) {
                list = new ArrayList<>();
            }
            if (list.size() < batchSize) {
                list.add(vo);
            } else if (list.size() == batchSize) {
                //多线程处理排序
                futureList.add(analyseTimelinessFutureService.handleDetailDate(list, date2CalendarDate));
                list = new ArrayList<>();
                list.add(vo);
            }
        }
        //如果存在最后一批样，需要单独去排序处理
        if (StringUtil.isNotEmpty(list)) {
            futureList.add(analyseTimelinessFutureService.handleDetailDate(list, date2CalendarDate));
        }
        //处理多线程处理的结果
        try {
            for (Future<List<AnalyzeDetailDataVO>> analyseDataResult : futureList) {
                while (true) {
                    if (analyseDataResult.isDone() && !analyseDataResult.isCancelled()) {
                        resultList.addAll(analyseDataResult.get());
                        break;
                    } else {
                        //防止CPU高速轮询被耗空
                        Thread.sleep(1);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("......多线程处理分析数据出错......");
        }
    }


    /**
     * 填充分页返回数据
     *
     * @param voList 分页数据
     */
    private void fillingPageTransientFields(List<AnalyzeDetailDataVO> voList) {
        List<DtoCalendarDate> calendarDates = calendarDateService.findByYear(CalendarUtil.getYear(new Date()));
        Map<String, DtoCalendarDate> date2CalendarDate = calendarDates.stream().collect(Collectors.toMap(dto -> DateUtil.dateToString(dto.getCalendarDate(), DateUtil.YEAR), d -> d));
        futureFullAnalyzeDetail(voList, date2CalendarDate);
    }


    /**
     * 定义排序比较器
     *
     * @return
     */
    private Comparator<AnalyzeDetailDataVO> getComparator() {
        Comparator<AnalyzeDetailDataVO> comparator = (o1, o2) -> {
            // 1: 按照 samplingDate 倒序排序
            int dateComparison = o2.getSamplingDate().compareTo(o1.getSamplingDate());
            if (dateComparison != 0) {
                return dateComparison;
            }

            // 2: 按照 sampleTypeName 正序排序
            int sampleTypeComparison = o1.getSampleTypeName().compareTo(o2.getSampleTypeName());
            if (sampleTypeComparison != 0) {
                return sampleTypeComparison;
            }

            // 3: 按照 analyzeItemName 正序排序
            int analyzeItemComparison = o1.getAnalyzeItemName().compareTo(o2.getAnalyzeItemName());
            if (analyzeItemComparison != 0) {
                return analyzeItemComparison;
            }

            // 4: 按照 sampleCode 正序排序
            return o1.getSampleCode().compareTo(o2.getSampleCode());
        };
        return comparator;
    }

    /**
     * 根据数据状态过滤数据
     *
     * @param pb       数据集
     * @param criteria 条件
     */
    private void filterData(PageBean<AnalyzeDetailDataVO> pb, AnalyzeTimelinessCriteria criteria) {
        List<AnalyzeDetailDataVO> voList = pb.getData();
        int warnDay = criteria.getWarnDay().intValue();
        if (criteria.getDataStatus() != null && criteria.getIsDataList()) {
            if (criteria.getDataStatus() == 0) {
                //未超期
                voList = voList.stream().filter(vo -> vo.getAnalyzeDate().compareTo(vo.getRequiredCompleteDate()) <= 0)
                        .collect(Collectors.toList());
            } else if (criteria.getDataStatus() == 2) {
                //超期
                voList = voList.stream().filter(vo -> vo.getAnalyzeDate().compareTo(vo.getRequiredCompleteDate()) > 0)
                        .collect(Collectors.toList());
            } else {
                //即将超期
                voList = voList.stream().filter(vo -> (vo.getAnalyzeDate().compareTo(vo.getRequiredCompleteDate()) <= 0)
                        && vo.getAnalyzeDate().compareTo(addCalendarDay(vo.getRequiredCompleteDate(), -warnDay).getTime()) > 0)
                        .collect(Collectors.toList());
            }
            pb.setData(voList);
        }
    }

    /**
     * 根据日期以及天数获取日历时间
     *
     * @param date 日期
     * @param day  天数
     * @return 日历日期
     */
    private Calendar addCalendarDay(Date date, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, day);
        return calendar;
    }

    /**
     * 根据日期以及天数获取日历时间
     *
     * @param date 日期
     * @param day  天数
     * @return 日历日期
     */
    private Calendar addCalendarDay(Date date, int day, Map<String, DtoCalendarDate> date2CalendarDate) {
        Calendar calendarComplete = Calendar.getInstance();
        for (int i = 0; i < day; i++) {
            Calendar calendar = addCalendarDay(date, i + 1);
            DtoCalendarDate calendarDate = date2CalendarDate.get(DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR));
            calendarComplete = calendar;
            if (StringUtil.isNotNull(calendarDate) && calendarDate.getType().equals(1)) {
                day++;
            }
        }
        return calendarComplete;
    }


    @Autowired
    @Lazy
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    @Lazy
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setJdbcTemplate(NamedParameterJdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Autowired
    @Lazy
    public void setSampleRecordService(ReceiveSampleRecordService sampleRecordService) {
        this.sampleRecordService = sampleRecordService;
    }

    @Autowired
    @Lazy
    public void setCalendarDateService(CalendarDateService calendarDateService) {
        this.calendarDateService = calendarDateService;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    @Lazy
    public void setAnalyseTimelinessFutureService(AnalyseTimelinessFutureService analyseTimelinessFutureService) {
        this.analyseTimelinessFutureService = analyseTimelinessFutureService;
    }
}