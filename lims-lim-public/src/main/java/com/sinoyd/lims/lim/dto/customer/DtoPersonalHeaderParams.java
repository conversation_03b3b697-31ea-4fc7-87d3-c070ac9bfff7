package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.lims.lim.dto.rcc.DtoParams2ParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *  个性化的表头参数
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
@Data
public class DtoPersonalHeaderParams {

    /**
     * 记录单Id
     */
    private String recordId;


    /**
     * 参数配置id
     */
    private String paramsConfigId;

    /**
     * 冗余的是测试项目上的id
     */
    private String objectId;

    /**
     * 公式
     */
    private String formula;

    /**
     * 别名
     */
    private String alias;

    /**
     * 是否使用
     */
    private Boolean isUse = false;

    /**
     * 是否启用个性化配置
     */
    private Boolean isShow = false;

    /**
     * 是否使用默认值
     */
    private Boolean isUseDefault = false;

    /**
     * 量纲
     */
    private String dimension;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 参数id
     */
    private String paramsId;

    /**
     * 有效位
     */
    private Integer mostSignificance;


    /**
     * 小数位
     */
    private Integer mostDecimal;


    /**
     *
     */
    private String defaultValue;

    /**
     * 部分公式
     */
    private List<DtoParamsPartFormula> paramsPartFormulas=new ArrayList<>();

    /**
     * 设置参数配置
     * @param dtoPersonalHeaderParams 个性化配置
     * @param paramsConfig          参数配置
     */
    public void setDtoParamsConfig(DtoPersonalHeaderParams dtoPersonalHeaderParams,DtoParamsConfig paramsConfig) {
        paramsConfig.setMostDecimal(dtoPersonalHeaderParams.getMostDecimal());
        paramsConfig.setMostSignificance(dtoPersonalHeaderParams.getMostSignificance());
        paramsConfig.setDimension(dtoPersonalHeaderParams.getDimension());
        paramsConfig.setDimensionId(dtoPersonalHeaderParams.getDimensionId());
        paramsConfig.setDefaultValue(dtoPersonalHeaderParams.getDefaultValue());
    }
    /**
     * 设置参数配置
     *
     * @param dtoParams2ParamsFormula          记录单参数
     * @param dtoPersonalHeaderParams 个性化配置
     */
    public void setDtoParams2ParamsFormula(DtoParams2ParamsFormula dtoParams2ParamsFormula, DtoPersonalHeaderParams dtoPersonalHeaderParams) {
        dtoParams2ParamsFormula.setRecordId(dtoPersonalHeaderParams.getRecordId());
        dtoParams2ParamsFormula.setParamsConfigId(dtoPersonalHeaderParams.getParamsConfigId());
        dtoParams2ParamsFormula.setFormula(dtoPersonalHeaderParams.getFormula());
        dtoParams2ParamsFormula.setObjectId(dtoPersonalHeaderParams.getObjectId());
    }

}
