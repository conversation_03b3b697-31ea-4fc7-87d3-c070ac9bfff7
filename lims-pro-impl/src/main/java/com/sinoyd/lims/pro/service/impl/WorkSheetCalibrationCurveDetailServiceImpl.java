package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.DtoWorkSheetCalibrationCurveDetail;
import com.sinoyd.lims.pro.repository.WorkSheetCalibrationCurveDetailRepository;
import com.sinoyd.lims.pro.service.WorkSheetCalibrationCurveDetailService;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * WorkSheetCalibrationCurveDetail操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class WorkSheetCalibrationCurveDetailServiceImpl extends BaseJpaServiceImpl<DtoWorkSheetCalibrationCurveDetail,String,WorkSheetCalibrationCurveDetailRepository> implements WorkSheetCalibrationCurveDetailService {

    @Override
    public void findByPage(PageBean<DtoWorkSheetCalibrationCurveDetail> pb, BaseCriteria workSheetCalibrationCurveDetailCriteria) {
        pb.setEntityName("DtoWorkSheetCalibrationCurveDetail a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, workSheetCalibrationCurveDetailCriteria);
    }

    /**
     * 按校准曲线id查询明细
     *
     * @param workSheetCalibrationCurveId 校准曲线id
     * @return 返回校准曲线明细
     */
    @Override
    public List<DtoWorkSheetCalibrationCurveDetail> findByWorkSheetCalibrationCurveId(String workSheetCalibrationCurveId) {
        List<DtoWorkSheetCalibrationCurveDetail> details = repository.findByWorkSheetCalibrationCurveId(workSheetCalibrationCurveId);

        BigDecimal maxVolume = BigDecimal.ONE;
        List<BigDecimal> volumes = details.stream().filter(p -> MathUtil.isNumeral(p.getAddVolume())).map(p -> MathUtil.getBigDecimal(p.getAddVolume())).collect(Collectors.toList());
        if (volumes.size() > 0) {
            maxVolume = MathUtil.sumBigDecimal(maxVolume, Collections.max(volumes));
        }
        BigDecimal max = maxVolume;
        details.sort(Comparator.comparing(DtoWorkSheetCalibrationCurveDetail::getAnalyseCode).thenComparing((DtoWorkSheetCalibrationCurveDetail w) ->
                MathUtil.isNumeral(w.getAddVolume()) ? MathUtil.getBigDecimal(w.getAddVolume()) : max));
        return details;
    }
}