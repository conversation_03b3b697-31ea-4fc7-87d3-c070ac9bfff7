package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoCarManage;
import com.sinoyd.lims.lim.repository.lims.CarManagerRepository;
import com.sinoyd.lims.lim.service.CarManageService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 车辆管理接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
@Service
public class CarManageServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCarManage, String, CarManagerRepository>
        implements CarManageService {

    /**
     * 新增车辆
     */
    @Transactional
    @Override
    public DtoCarManage save(DtoCarManage entity) {

        Integer count = repository.getCountByCode(entity.getCarCode(), entity.getId());

        if (count > 0) {
            throw new BaseException("已存在相同名称的车牌号！");
        }

        return super.save(entity);
    }

    /**
     * 更新车辆
     */
    @Transactional
    @Override
    public DtoCarManage update(DtoCarManage entity) {

        Integer count = repository.getCountByCode(entity.getCarCode(), entity.getId());

        if (count > 0) {
            throw new BaseException("已存在相同名称的车牌号！");
        }

        return super.update(entity);
    }

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean pageBean, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        pageBean.setEntityName("DtoCarManage p");
        // 设置查询返回的字段、实体别名表示所有字段
        pageBean.setSelect("select p");

        super.findByPage(pageBean, criteria);
    }

    /**
     * 重新实现（为了返回调用该类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回车辆管理对象
     */
    @Override
    public DtoCarManage findOne(String id) {
        return super.findOne(id);
    }
}