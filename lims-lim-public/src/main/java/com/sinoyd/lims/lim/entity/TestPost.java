package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * TestPost实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "TestPost")
@Data
@EntityListeners(AuditingEntityListener.class)
public class TestPost extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public TestPost() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 岗位名称
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("岗位名称")
    @Length(message = "岗位名称{validation.message.length}", max = 100)
    private String postName;

    /**
     * 岗位类型(1.现场 2.分析)
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("岗位类型")
    private Integer postType;

    /**
     * 岗位编码
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("岗位编码")
    @Length(message = "岗位编码{validation.message.length}", max = 100)
    private String postCode;

    /**
     * 排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum = 0;

    /**
     * 岗位描述
     */
    @Column(length=1000)
    @ApiModelProperty("岗位描述")
    @Length(message = "岗位描述{validation.message.length}", max = 1000)
    private String description;

    /**
     * 采样负责人
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("采样负责人")
    private String chargePerson;

    /**
     * 采样负责人标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样负责人标识")
    private String chargePersonId;

    /**
     * 采样车辆
     */
    @Column(length = 50)
    @ApiModelProperty("采样车辆")
    private String car;

    /**
     * 采样车辆标识
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样车辆标识")
    private String carId;

    /**
     * 假删
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted=false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}