package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.util.List;

/**
 * DtoInstrumentHistory实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since  2024/11/25
 */
@Data
public class DtoInstrumentHistory {

    /**
     * 仪器编号
     */
    private String instrumentsCode;

    /**
      仪器名称
     */
    private String instrumentName;

    /**
     * 出厂编号
     */
    private String serialNo;

    /**
     * 规格喜好
     */
    private String model;

    /**
     * 管理人员
     */
    private String managePeople;

    /**
     * 仪器有效期
     */
    private String effectiveTime;

    /**
     * 仪器状态
     */
    private String status;

    /**
     * 关联历史附件标识集合
     */
    private List<String> historyFileIds;
}
