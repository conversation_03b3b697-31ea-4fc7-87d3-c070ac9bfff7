package com.sinoyd.lims.monitor.dto.rcc;

import com.sinoyd.lims.monitor.entity.Water;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoWater实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_Water")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoWater extends Water {
    private static final long serialVersionUID = 1L;

    /**
     * 所属水体
     */
    @Transient // 此注解标识为非数据库表字段
    private String belongingWater;

    /**
     * 水体详细信息扩展表
     */
    @Transient // 此注解标识为非数据库表字段
    private DtoWaterExpand dtoWaterExpand;

    /**
     *树状子节点集合
     */
    @Transient // 此注解标识为非数据库表字段
    private List<DtoWater> chirdList;

}