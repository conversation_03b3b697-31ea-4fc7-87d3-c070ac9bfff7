package com.sinoyd.lims.api.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

@Data
public class DtoFolderInfo {

    /**
     * 点位id
     */
    private String folderId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 点位名称
     */
    private String folderName;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 检测类型ids
     */
    private List<String> sampleTypeIds;

    /**
     * 周期
     */
    private Integer cycleOrder;

    /**
     * 次数
     */
    private Integer timesOrder;

    /**
     * 经度
     */
    @Length(message = "签到经度{validation.message.length}", max = 50)
    private String lon;

    /**
     * 纬度
     */
    @Length(message = "签到纬度{validation.message.length}", max = 50)
    private String lat;

    /**
     * 签到时间
     */
    private Date signTime;

    /**
     * 样品ids
     */
    private List<String> sampleIds;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 移动端版本
     */
    private String type;

    /**
     * 签到说明
     */
    @Length(message = "签到说明{validation.message.length}", max = 200)
    private String signTip;

    /**
     * 是否校验当前人员
     */
    private Boolean isVerify = Boolean.FALSE;

    /**
     * 语音说明
     */
    @Length(message = "语音说明{validation.message.length}", max = 500)
    private String voiceTip;

    /**
     * 签到信息id
     */
    private String folderSignId;
}
