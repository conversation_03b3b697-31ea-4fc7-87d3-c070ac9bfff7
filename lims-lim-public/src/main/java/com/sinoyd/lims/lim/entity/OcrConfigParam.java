package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * OcrConfigParam实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "OcrConfigParam")
@Data
@EntityListeners(AuditingEntityListener.class)
public class OcrConfigParam implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     *OCR对象标识
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String configId;

    /**
     *参数名称
     */
    @Column(nullable = false)
    @Length(message = "参数名称{validation.message.length}", max = 255)
    private String paramName;

    /**
     *参数别名
     */
    @Column(nullable = false)
    @Length(message = "参数别名{validation.message.length}", max = 255)
    private String paramNameAlias;

    /**
     *类型（枚举EnumOcrConfigParamType：样品参数1、现场数据2、现场公式3）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    private Integer paramType;

    /**
     *计量单位id
     */
    @Column(length = 50)
    @Length(message = "计量单位{validation.message.length}", max = 50)
    private String dimension;

    /**
     *  正则开始
     */
    @Column
    @ColumnDefault("''")
    @Length(message = "正则开始{validation.message.length}", max = 255)
    private String regexBegin;

    /**
     *  正则结束
     */
    @Column
    @ColumnDefault("''")
    @Length(message = "正则结束{validation.message.length}", max = 255)
    private String regexEnd;

    /**
     *   分析项目标识,分割
     */
    @Column(length = 4000)
    @Length(message = "分析项目标识{validation.message.length}", max = 4000)
    private String analyzeItemId;

    /**
     *最大截取长度
     */
    @Column
    private Integer maxLength;

    /**
     *排序值
     */
    @Column
    private Integer orderNum;
}
