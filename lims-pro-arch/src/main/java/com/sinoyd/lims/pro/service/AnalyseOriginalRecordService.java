package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoAnalyseOriginalRecord;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Collection;
import java.util.List;


/**
 * AnalyseOriginalRecord操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface AnalyseOriginalRecordService extends IBaseJpaService<DtoAnalyseOriginalRecord, String> {

    /**
     * 根据分析数据ids获取参数集合
     *
     * @param analyseDataList 数据集合
     * @return 返回参数集合
     */
    List<DtoAnalyseOriginalRecord> findByDataIds(List<DtoAnalyseData> analyseDataList);
}