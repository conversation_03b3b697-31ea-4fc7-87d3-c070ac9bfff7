package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.customer.DtoRecordConfigParams;

import java.util.List;

/**
 * 记录单相关的参数（表头参数或者记录单参数）
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
public interface RecordConfigParamsConfigService {

    /**
     * 查询记录单参数数据
     *
     * @param criteria 查询条件
     * @return 返回数据
     */
    List<DtoParamsConfig> findRecordConfigParamsConfigList(BaseCriteria criteria);


    /**
     * 保存记录单数据
     *
     * @param paramsConfig     参数配置
     * @param paramsConfigType //EnumParamsConfigType：6.原始记录单-数据参数 7.报告（预留）9.原始记录单-表头参数
     * @return 返回数据
     */
    DtoParamsConfig saveRecordConfigParams(DtoParamsConfig paramsConfig, Integer paramsConfigType);


    /**
     * 修改记录单数据
     *
     * @param paramsConfig     参数配置
     * @param paramsConfigType //EnumParamsConfigType：6.原始记录单-数据参数 7.报告（预留）9.原始记录单-表头参数
     * @return 返回数据
     */
    DtoParamsConfig updateRecordConfigParams(DtoParamsConfig paramsConfig, Integer paramsConfigType);


    /**
     * 删除记录数据
     *
     * @param ids 主键ids
     * @return 返回删除行数据
     */
    Integer deleteRecordConfigParams(List<String> ids);


    /**
     * 根据测试项目获取记录单的参数
     *
     * @param testIds 测试项目ids
     * @return 返回数据
     */
    List<DtoRecordConfigParams> findTestParamsByTestIds(List<String> testIds);


    /**
     * 找到测试项目指定记录单的参数
     * @param testIds 测试项目ids
     * @param recordId 记录单id
     * @return 返回数据
     */
    List<DtoRecordConfigParams>  findTestParamsByRecordId(List<String> testIds,String recordId);
}
