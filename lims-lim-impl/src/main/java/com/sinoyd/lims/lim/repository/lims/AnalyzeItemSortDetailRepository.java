package com.sinoyd.lims.lim.repository.lims;

import java.util.List;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * 分析项目排序详情仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/18
 * @since V100R001
 */
public interface AnalyzeItemSortDetailRepository extends IBaseJpaPhysicalDeleteRepository<DtoAnalyzeItemSortDetail, String>, LimsRepository<DtoAnalyzeItemSortDetail, String> {

    /**
     * 通过排序id获取排序详情中最小的排序值
     *
     * @param sortId 排序id
     * @return 返回排序值
     */
    @Query("select min(p.orderNum) from DtoAnalyzeItemSortDetail p where p.sortId = :sortId")
    Integer getMinNum(@Param("sortId") String sortId);

    /**
     * 获取分析项目排序详情列表
     *
     * @param sortId 排序id
     * @return 返回分析项目排序详情集合
     */
    @Query("select p from DtoAnalyzeItemSortDetail p where p.sortId = :sortId")
    List<DtoAnalyzeItemSortDetail> getList(@Param("sortId") String sortId);

    /**
     * 删除该排序下所有的分析项目
     *
     * @param sortId 排序id
     * @return 返回分析项目id
     */
    @Transactional
    @Modifying
    @Query("delete from DtoAnalyzeItemSortDetail p where p.sortId = :sortId")
    Integer deleteBySortId(@Param("sortId") String sortId);

    /**
     * 根据排序id查询
     *
     * @param sortId 排序id
     * @return List<DtoAnalyzeItemSortDetail>排序明细实体
     */
    List<DtoAnalyzeItemSortDetail> findBySortId(String sortId);
}