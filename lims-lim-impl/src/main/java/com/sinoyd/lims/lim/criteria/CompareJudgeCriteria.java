package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * CompareJudge查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CompareJudgeCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    private String key;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and exists(select 1 from DtoAnalyzeItem i where a.analyzeItemId = i.id and i.isDeleted = 0)");
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and exists(select 1 from DtoAnalyzeItem i where a.analyzeItemId = i.id and i.analyzeItemName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        return condition.toString();
    }
}
