package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoParamsData;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

public interface ParamsDataFutureService {

    /**
     * 多线程获取参数
     *
     * @param objectIds 数据id
     * @return 日志集合
     */
    Future<List<DtoParamsData>> getListByObjectIdIn(List<String> objectIds);

    /**
     * 多线程获取删除数据
     * @param mapList
     * @param paramsDataList
     * @return
     */
    Future<List<DtoParamsData>> getDelParamsDataList(List<Map<String, Object>> mapList, List<DtoParamsData> paramsDataList);

    /**
     * 删除数据
     * @param paramsDataList 数据集合
     */
    Future<Void> delete(List<DtoParamsData> paramsDataList);

}
