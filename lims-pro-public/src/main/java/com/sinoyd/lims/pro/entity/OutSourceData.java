package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 分包数据实体
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/2/8
 **/
@MappedSuperclass
@ApiModel(description = "OutSourceData")
@Data
@EntityListeners(AuditingEntityListener.class)
public class OutSourceData {

    private static final long serialVersionUID = 1L;

    public OutSourceData() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    @Column(length = 50)
    private String id= UUIDHelper.NewID();

    /**
     * 分析数据id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("分析数据id")
    private String analyseDataId;

    /**
     * 分析方法名称
     */
    @Column
    @ApiModelProperty("分析方法名称")
    private String analyzeMethodName;

    /**
     * 分析方法id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("分析方法id")
    @ColumnDefault(value = "00000000-0000-0000-0000-000000000000")
    private String analyzeMethodId = "00000000-0000-0000-0000-000000000000";

    /**
     * 出证结果
     */
    @Column(length = 50)
    @ApiModelProperty("出证结果")
    private String testValue;

    /**
     * 量纲名称
     */
    @Column(length = 50)
    @ApiModelProperty("量纲名称")
    private String dimensionName;

    /**
     * 检出限
     */
    @Column(length = 50)
    @ApiModelProperty("检出限")
    private String detectionLimit;

    /**
     * 量纲id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("量纲id")
    @ColumnDefault(value = "00000000-0000-0000-0000-000000000000")
    private String dimensionId = "00000000-0000-0000-0000-000000000000";

    /**
     * 状态（0未确认 1已确认）
     */
    @Column(nullable = false)
    @ApiModelProperty("状态")
    @ColumnDefault(value = "0")
    private Integer state = 0;

    @Column
    @ApiModelProperty("分析开始日期")
    private Date analyzeTime;

    @Column
    @ApiModelProperty("分析结束日期")
    private Date analyzeEndTime;

    @Column
    @ApiModelProperty("分包商")
    private String subcontractor;

    @Column
    @ApiModelProperty("分包商CMA证书编号")
    private String cmaCode;

    @Column
    @ApiModelProperty("分包报告编号")
    private String outSourceReportCode;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("创建人")
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ApiModelProperty("创建时间")
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("修改人")
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ApiModelProperty("修改时间")
    @LastModifiedDate
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;
}
