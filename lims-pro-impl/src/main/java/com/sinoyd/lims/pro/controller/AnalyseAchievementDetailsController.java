package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.AnalyseAchievementDetailsCriteria;
import com.sinoyd.lims.pro.criteria.SamplingAchievementDetailsCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseAchievementDetails;
import com.sinoyd.lims.pro.service.AnalyseAchievementDetailsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * analyseAchievementDetails服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/13
 * @since V100R001
 */
@Api(tags = "示例: OrderContractAchievementDetails服务")
@RestController
@RequestMapping("api/pro/analyseAchievementDetails")
public class AnalyseAchievementDetailsController extends BaseJpaController<DtoAnalyseAchievementDetails, String, AnalyseAchievementDetailsService> {

    /**
     * 分页动态条件查询DtoSamplingAchievementDetails
     *
     * @param criteria 条件参数
     * @return RestResponse<List <DtoSamplingAchievementDetails>>
     */
    @ApiOperation(value = "分页动态条件查询DtoSamplingAchievementDetails", notes = "分页动态条件查询DtoSamplingAchievementDetails")
    @GetMapping
    public RestResponse<List<DtoAnalyseAchievementDetails>> findByPage(AnalyseAchievementDetailsCriteria criteria) {
        PageBean<DtoAnalyseAchievementDetails> pageBean = super.getPageBean();
        RestResponse<List<DtoAnalyseAchievementDetails>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 导出合同明细
     * @param criteria 查询条件
     * @param response 响应
     * @return
     */
    @ApiOperation(value = "导出合同明细", notes = "导出合同明细")
    @GetMapping("/export")
    public RestResponse<Void> exportDetails(AnalyseAchievementDetailsCriteria criteria, HttpServletResponse response) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.export(response, criteria);
        return restResponse;
    }

}
