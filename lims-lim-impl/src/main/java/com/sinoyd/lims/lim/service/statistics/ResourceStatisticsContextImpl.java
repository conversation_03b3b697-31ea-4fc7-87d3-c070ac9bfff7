package com.sinoyd.lims.lim.service.statistics;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.repository.lims.ConsumableRepository;
import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.customer.DtoResourceStatistics;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 资源统计上下文实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/22
 */
@Service
public class ResourceStatisticsContextImpl implements IResourceStatisticsContext {

    private Map<String, ResourceStatisticsService> resourceStatisticsServiceMap;

    private CommonRepository commonRepository;

    private ConsumableRepository consumableRepository;

    private JdbcTemplate jdbcTemplate;

    private PersonRepository personRepository;

    @Override
    public List<DtoResourceStatistics> statistics() {
        List<DtoResourceStatistics> voList = new ArrayList<>();

        for (Map.Entry<String, ResourceStatisticsService> map : resourceStatisticsServiceMap.entrySet()) {
            voList.add(map.getValue().statistics());
        }
        return voList;
    }

    @Override
    public List<DtoConsumableDetail> getOverDueData() {
        PageBean<DtoConsumableDetail> pb = new PageBean<>();
        pb.setEntityName("DtoConsumableDetail d,DtoConsumable c");
        pb.setSelect("select d ");
        pb.setCondition(" and d.parentId = c.id and storage > 0 and d.expiryDate <> '1753-01-01 00:00:00' and DATE_FORMAT(d.expiryDate, '%Y-%m-%d') < DATE_FORMAT(now(), '%Y-%m-%d')");
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        commonRepository.findByPage(pb);
        List<DtoConsumableDetail> list = pb.getData();
        //所有消耗品详情
        List<DtoConsumable> dtoConsumableList = consumableRepository.findAll();
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        Date now = new Date();
        //计算过期天数 批次为主体
        for (DtoConsumableDetail consumableDetail:list) {
            DtoConsumable dtoConsumable = dtoConsumableList.stream().filter(d->d.getId().equals(consumableDetail.getParentId())).findFirst().orElse(null);
            if(dtoConsumable!=null){
                consumableDetail.setExpirationDays(CalendarUtil.getDaysBetween(consumableDetail.getExpiryDate(),now));
                consumableDetail.setConsumableName(dtoConsumable.getConsumableName());
                //规格
                consumableDetail.setSpecification(dtoConsumable.getSpecification());
                //管理人-> 提醒人
                DtoPerson dtoPerson = dtoPersonList.stream().filter(p-> p.getId().equals(dtoConsumable.getSendWarnUserId())).findFirst().orElse(null);
                if(dtoPerson!=null){
                    consumableDetail.setSendWarnUser(dtoPerson.getCName());
                }
            }
        }
        //排序 过期天数倒序，消耗品名称顺序
        list.sort(Comparator.comparing(DtoConsumableDetail::getExpirationDays,Comparator.reverseOrder()).thenComparing(DtoConsumableDetail::getConsumableName));
        return list;
    }

    @Override
    public List<DtoConsumable> getLowInventoryData() {
        StringBuilder sql = new StringBuilder();
        sql.append("select c.* ")
                .append("from TB_BASE_Consumable c, ")
                .append("(select sum(d.storage) as totalStorage, d.parentId from TB_BASE_ConsumableDetail d group by d.parentId) temp ")
                .append("where c.id = temp.parentId ")
                .append("and c.warningNum >= temp.totalStorage ")
                .append("and temp.totalStorage > 0 order by c.consumableName");
        List<DtoConsumable> list = jdbcTemplate.query(sql.toString(),new BeanPropertyRowMapper(DtoConsumable.class));
        List<DtoPerson> dtoPersonList = personRepository.findAll();
        for (DtoConsumable dtoConsumable:list ) {
            //管理人-》提醒人
            DtoPerson dtoPerson = dtoPersonList.stream().filter(p-> p.getId().equals(dtoConsumable.getSendWarnUserId())).findFirst().orElse(null);
            if(dtoPerson!=null){
                dtoConsumable.setSendWarnUser(dtoPerson.getCName());
            }
        }
        return list;
    }

    @Autowired
    @Lazy
    public void setResourceStatisticsServiceMap(Map<String, ResourceStatisticsService> resourceStatisticsServiceMap) {
        this.resourceStatisticsServiceMap = resourceStatisticsServiceMap;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setConsumableRepository(ConsumableRepository consumableRepository) {
        this.consumableRepository = consumableRepository;
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }
}