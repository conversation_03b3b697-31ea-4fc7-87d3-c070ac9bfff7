-- 新增现场空白质控样报告组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode)
VALUES ('862381fb-faa5-4650-bb3b-c91e082a5c9e', 'OuterBlankStdData', '标准版全程序空白样检测结果表组件', 'OuterBlankStdData',
        'dtOuterBlankSource', 1000, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-05-10 15:53:06', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-05-10 15:53:06', '0', '0', 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode)
VALUES ('4f6f0596-8de1-4887-95b3-2d335a72a4a8', 'TransportBlankStdData', '标准版运输空白样检测结果表组件', 'TransportBlankStdData',
        'dtTransportBlankSource', 1000, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-05-10 15:54:19', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-05-10 15:54:19', '0', '0', 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode)
VALUES ('ff682c7e-8ef2-4a87-a1d1-da970636d0d1', 'SiteBlankStdData', '标准版现场空白样检测结果表组件', 'SiteBlankStdData',
        'dtSiteBlankSource', 1000, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-05-10 15:55:46', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-05-10 15:55:46', '0', '0', 0, 0);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                               sampleCount, testCount, sonTableJson, isCompound, orgId,
                                               creator, createDate, domainId, modifier, modifyDate,
                                               totalTest, auxiliaryInstrument, conversionCalculationMode,
                                               speedCalculationMode)
VALUES ('56ecb137-470e-4e8d-8498-ca709dd20cb3', 'EquipBlankStdData', '标准版仪器空白样检测结果表组件', 'EquipBlankStdData',
        'dtEquipBlankSource', 1000, 0, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-05-10 15:58:34', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-05-10 15:58:34', '0', '0', 0, 0);

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'SoilStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'SoilStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'SoilStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'SoilStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'NormalWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'NormalWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'NormalWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'NormalWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'SolidStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'SolidStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'SolidStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'SolidStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'FlyAshStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'FlyAshStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'FlyAshStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'FlyAshStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'SludgeStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'SludgeStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'SludgeStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'SludgeStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'ComprehensiveStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'ComprehensiveStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'ComprehensiveStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'ComprehensiveStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'SurfaceWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'SurfaceWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'SurfaceWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'SurfaceWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'GroundWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'GroundWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'GroundWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'GroundWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'WasteWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'WasteWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'WasteWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'WasteWaterStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'NoiseStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'NoiseStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'NoiseStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'NoiseStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'SyStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'SyStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'SyStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'SyStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'OrgGasStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'OrgGasStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'OrgGasStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'OrgGasStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '4f6f0596-8de1-4887-95b3-2d335a72a4a8'
from tb_lim_reportconfig where reportCode = 'UnOrgGasStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '56ecb137-470e-4e8d-8498-ca709dd20cb3'
from tb_lim_reportconfig where reportCode = 'UnOrgGasStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, '862381fb-faa5-4650-bb3b-c91e082a5c9e'
from tb_lim_reportconfig where reportCode = 'UnOrgGasStd';

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
select uuid(), id, 'ff682c7e-8ef2-4a87-a1d1-da970636d0d1'
from tb_lim_reportconfig where reportCode = 'UnOrgGasStd';