package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;



/**
 * NoticeMsg查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticeMsgCriteria extends BaseCriteria implements Serializable {

    /**
     * 公告id
     */
    private String noticeId;

    /**
     * 留言人id
     */
    private String messagePersonId;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(noticeId)
                && !UUIDHelper.GUID_EMPTY.equals(this.noticeId)) {
            condition.append(" and noticeId = :noticeId");
            values.put("noticeId", this.noticeId);
        }
        if (StringUtils.isNotNullAndEmpty(messagePersonId)
                && !UUIDHelper.GUID_EMPTY.equals(this.messagePersonId)) {
            condition.append(" and messagePersonId = :messagePersonId");
            values.put("messagePersonId", this.messagePersonId);
        }
        return condition.toString();
    }
}