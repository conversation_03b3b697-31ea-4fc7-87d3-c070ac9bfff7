-- 文件审批备案步骤生成文件审批单
delete from tb_lim_reportconfig where id = '964d4dab-2d9e-48c5-8f6b-b8bccab716f1';
INSERT INTO tb_lim_reportconfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName, validate, usageNum, controlNumLocation, versionNumLocation)
VALUES ('964d4dab-2d9e-48c5-8f6b-b8bccab716f1', 1, 'FileAuditRegister', '质量管理体系文件审批单.xlsx', 'LIMReportForms/质量管理体系文件审批单.xlsx', 'output/LIMReportForms/质量管理体系文件审批单.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.QualityControlFileAuditService', '{ \"isPageSig\": true}', '', 9999, 1, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-06-03 10:46:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-06-03 15:44:03', '', 'LIMReportForms', 'FileAuditRegister', 0, '', NULL, '', '', '', 0, NULL, '左上', '中上');

delete from tb_lim_reportapply where id = 'f4f45383-9316-44eb-9bf2-9e879223aa8a';
INSERT INTO tb_lim_reportapply (id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark, location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('f4f45383-9316-44eb-9bf2-9e879223aa8a', '964d4dab-2d9e-48c5-8f6b-b8bccab716f1', 'FileRegister', '备案人员备案', 'FileRegister', '生成', 0, 0, 1, '', '文件审批', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-06-03 13:46:36', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2025-06-03 13:46:36', NULL);
