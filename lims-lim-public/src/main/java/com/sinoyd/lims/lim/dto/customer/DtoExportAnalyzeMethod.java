package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Table;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * 分析方法导出
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/5
 * @since V100R001
 */
@Table(name = "TB_LIM_AnalyzeMethod")
@Data
public class DtoExportAnalyzeMethod extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id;

    /**
     * 方法名称
     */
    @Excel(name = "方法名称", orderNum = "20")
    private String methodName;

    /**
     * 标准编号
     */
    @Excel(name = "标准编号", orderNum = "30")
    private String countryStandard;

    /**
     * 是否删除
     */
    @Excel(name = "是否删除", orderNum = "40")
    @Pattern(regexp = "^(true|false)$", message = "是否删除必须为布尔类型")
    private String isDeleted;

    /**
     * 标准实施日期
     */
    @Excel(name = "标准实施日期", orderNum = "50")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}(\\.\\d+)?$", message = "标准实施日期不符合日期格式要求")
    private String effectiveDate;

    /**
     * 受控编号
     */
    @Excel(name = "受控编号", orderNum = "60")
    private String methodCode;

    /**
     * 是否可以同时完成
     */
    @Excel(name = "是否可以同时完成", orderNum = "70")
    @Pattern(regexp = "^(true|false)$", message = "是否可以同时完成必须为布尔类型")
    private String isCompleteTogether;

    /**
     * 是否受控
     */
    @Excel(name = "是否受控", orderNum = "80")
    @Pattern(regexp = "^(true|false)$", message = "是否受控必须为布尔类型")
    private String isControlled;

    /**
     * 是否制备
     */
    @Excel(name = "是否制备", orderNum = "90")
    @Pattern(regexp = "^(true|false)$", message = "是否制备必须为布尔类型")
    private String isPreparation;

    /**
     * 制备方法
     */
    @Excel(name = "制备方法", orderNum = "100")
    private String preparedMethod;

    /**
     * 备注
     */
    @Excel(name = "备注", orderNum = "110")
    private String remark;

    /**
     * 父级Id（Guid）（预留，例：XXX方法，拉伸测试）
     */
    @Excel(name = "父级Id（Guid）", orderNum = "120")
    private String parentId;

    /**
     * 国家标准名称（预留）
     */
    @Excel(name = "国家标准名称", orderNum = "130")
    private String countryStandardName;

    /**
     * 年度（预留）
     */
    @Excel(name = "年度", orderNum = "140")
    private String yearSn;

    /**
     * 有效天数（预留）
     */
    @Excel(name = "有效天数", orderNum = "150")
    @Pattern(regexp = "^-?\\d+$", message = "有效天数必须为整数")
    private String effectiveDays;

    /**
     * 警告天数（预留）
     */
    @Excel(name = "警告天数", orderNum = "160")
    @Pattern(regexp = "^-?\\d+$", message = "警告天数必须为整数")
    private String warningDays;

    /**
     * 是否现行有效（预留）
     */
    @Excel(name = "是否现行有效", orderNum = "170")
    @Pattern(regexp = "^(true|false)$", message = "是否现行有效必须为布尔类型")
    private String isInforce;

    /**
     * 状态（预留）
     */
    @Excel(name = "状态", orderNum = "180")
    private String status;

    /**
     * 是否按样品录入 1：是 0：否
     */
    @Excel(name = "是否按样品录入 1：是 0：否", orderNum = "250")
    @Pattern(regexp = "^(true|false)$", message = "是否按样品录入必须为布尔类型")
    private String isInputBySample;

    /**
     * 别名
     */
    @Excel(name = "别名", orderNum = "260")
    private String alias;

    /**
     * 是否可跨天完成
     */
    @Excel(name = "是否可跨天完成", orderNum = "270")
    @Pattern(regexp = "^(true|false)$", message = "是否可跨天完成必须为布尔类型")
    private String isCrossDay;
}
