package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.lims.lim.dto.customer.DtoExportAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * 分析方法实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface AnalyzeMethodMapper {


    /**
     * DtoAnalyzeMethod 实例转换成DtoExportAnalyzeMethod实例
     *
     * @param AnalyzeMethod 分析项目实体
     * @return DtoExportAnalyzeMethod 实例
     */
    @Mapping(source = "isDeleted", target = "isDeleted")
    @Mapping(source = "effectiveDate", target = "effectiveDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "isCompleteTogether", target = "isCompleteTogether")
    @Mapping(source = "isControlled", target = "isControlled")
    @Mapping(source = "isPreparation", target = "isPreparation")
    @Mapping(source = "effectiveDays", target = "effectiveDays")
    @Mapping(source = "warningDays", target = "warningDays")
    @Mapping(source = "isInforce", target = "isInforce")
    @Mapping(source = "isInputBySample", target = "isInputBySample")
    @Mapping(source = "isCrossDay", target = "isCrossDay")
    DtoExportAnalyzeMethod toExportAnalyzeMethod(DtoAnalyzeMethod AnalyzeMethod);

    /**
     * DtoAnalyzeMethod 实例集合转换成DtoExportAnalyzeMethod 实例集合
     *
     * @param AnalyzeMethodList 分析项目实例集合
     * @return DtoExportAnalyzeMethod 实例集合
     */
    @InheritConfiguration(name = "toExportAnalyzeMethod")
    List<DtoExportAnalyzeMethod> toExportAnalyzeMethodList(List<DtoAnalyzeMethod> AnalyzeMethodList);


    /**
     * DtoExportAnalyzeMethod 实例转换成DtoAnalyzeMethod 实例
     *
     * @param exportAnalyzeMethod 分析项目实体
     * @return DtoExportAnalyzeMethod 实例
     */
    @Mapping(source = "isDeleted", target = "isDeleted")
    @Mapping(source = "effectiveDate", target = "effectiveDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "isCompleteTogether", target = "isCompleteTogether")
    @Mapping(source = "isControlled", target = "isControlled")
    @Mapping(source = "isPreparation", target = "isPreparation")
    @Mapping(source = "effectiveDays", target = "effectiveDays")
    @Mapping(source = "warningDays", target = "warningDays")
    @Mapping(source = "isInforce", target = "isInforce")
    @Mapping(source = "isInputBySample", target = "isInputBySample")
    @Mapping(source = "isCrossDay", target = "isCrossDay")
    DtoAnalyzeMethod toDtoAnalyzeMethod(DtoExportAnalyzeMethod exportAnalyzeMethod);

    /**
     * DtoExportAnalyzeMethod 实例集合转换成DtoAnalyzeMethod 实例集合
     *
     * @param exportAnalyzeMethodList 分析项目导入导出实例集合
     * @return DtoAnalyzeMethod 实例集合
     */
    @InheritConfiguration(name = "toDtoAnalyzeMethod")
    List<DtoAnalyzeMethod> toDtoAnalyzeMethodList(List<DtoExportAnalyzeMethod> exportAnalyzeMethodList);
}
