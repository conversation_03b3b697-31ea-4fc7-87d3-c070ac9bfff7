package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.FolderSign;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoFolderSign实体
 * <AUTHOR>
 * @version V1.0.0 2021/7/7
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_FolderSign")
 @Data
 @DynamicInsert
 public  class DtoFolderSign extends FolderSign {
   private static final long serialVersionUID = 1L;
 }