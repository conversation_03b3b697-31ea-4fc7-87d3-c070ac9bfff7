package com.sinoyd.lims.lim.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoMessageSendRecord;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.MessageSendRecordRepository;
import com.sinoyd.lims.lim.service.MessageSendRecordService;
import com.sinoyd.lims.lim.vo.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.websocket.Session;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * 消息业务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022-09-22
 */
@Service
@Slf4j
public class MessageSendRecordServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoMessageSendRecord, String, MessageSendRecordRepository>
        implements MessageSendRecordService {

    private WebSocketServer webSocketServer;

    private RedisTemplate redisTemplate;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void findByPage(PageBean<DtoMessageSendRecord> pb, BaseCriteria messageSendRecordCriteria) {
        pb.setEntityName("DtoMessageSendRecord a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, messageSendRecordCriteria);
    }

    @Transactional
    @Override
    public boolean concernSendRecord(List<String> ids, Boolean isConcern) {
        List<DtoMessageSendRecord> list = super.findAll(ids);
        if (isConcern) {
            //如果数据改成关注，需要将数据改成已读模式
            list.forEach(p -> {
                p.setIsConcern(true);
                p.setStatus(EnumLIM.EnumMessageSendRecordStatus.已读.getValue());
            });
        } else {
            list.forEach(p -> p.setIsConcern(false));
        }
        super.save(list);
        return true;
    }

    @Override
    @Transactional
    public boolean setupRead(String id) {
        DtoMessageSendRecord dtoMessageSendRecord = super.findOne(id);
        dtoMessageSendRecord.setStatus(EnumLIM.EnumMessageSendRecordStatus.已读.getValue());
        super.save(dtoMessageSendRecord);
        return true;
    }

    @Override
    @Transactional
    public boolean setupReadByIds(List<String> ids) {
        List<DtoMessageSendRecord> messageSendRecords = super.findAll(ids);
        messageSendRecords.forEach(p -> p.setStatus(EnumLIM.EnumMessageSendRecordStatus.已读.getValue()));
        super.save(messageSendRecords);
        return true;
    }

    @Override
    @Transactional
    public boolean setupReadAll() {
        List<DtoMessageSendRecord> messageSendRecordList = super.findAll();
        messageSendRecordList.forEach(p -> p.setStatus(EnumLIM.EnumMessageSendRecordStatus.已读.getValue()));
        super.save(messageSendRecordList);
        return true;
    }

    @Override
    public Integer queryLoginUserMsgCount() {
        return repository.countByReceiverAndStatus(PrincipalContextUser.getPrincipal().getUserId(),
                EnumLIM.EnumMessageSendRecordStatus.未读.getValue());
    }

    @Override
    public Integer queryMessageCountByType(List<String> messageTypes) {
        return repository.countByReceiverAndStatusAndMessageTypeIn(PrincipalContextUser.getPrincipal().getUserId(),
                EnumLIM.EnumMessageSendRecordStatus.未读.getValue(), messageTypes);
    }

    @Override
    @Transactional
    public DtoMessageSendRecord createMessage(String messageType, String messageContent, String receiverId) {
        DtoMessageSendRecord messageSendRecord = new DtoMessageSendRecord();
        messageSendRecord.setId(UUIDHelper.NewID());
        messageSendRecord.setJobId(UUIDHelper.GUID_EMPTY);
        messageSendRecord.setMessageType(messageType);
        messageSendRecord.setMessageContent(messageContent);
        messageSendRecord.setReceiver(receiverId);
        messageSendRecord.setSendTime(new Date());
        messageSendRecord.setStatus(EnumLIM.EnumMessageSendRecordStatus.未读.getValue());
        messageSendRecord.setIsConcern(false);
        messageSendRecord.setSendType(1);
        messageSendRecord.setOrgId(PrincipalContextUser.getPrincipal().getOrgId());
        super.save(messageSendRecord);
        // 创建提醒缓存
        List<DtoMessageSendRecord> messageSendRecordList = new ArrayList<>();
        messageSendRecordList.add(messageSendRecord);
        String redisKey = EnumLIM.EnumLIMRedis.MESSAGE_SEND_RECORD.getValue();
        Object obj = redisTemplate.opsForHash().get(redisKey, receiverId);
        TypeLiteral<List<DtoMessageSendRecord>> typeLiteral = new TypeLiteral<List<DtoMessageSendRecord>>() {
        };
        if (null != obj) {
            messageSendRecordList.addAll(JsonIterator.deserialize(obj.toString(), typeLiteral));
        }
        String json;
        try {
            json = JsonUtil.toJson(messageSendRecordList);
            redisTemplate.opsForHash().put(redisKey, receiverId, json);
        } catch (JsonProcessingException e) {
           log.error("缓存消息失败" + e.getMessage());
        }
        return messageSendRecord;
    }

    /**
     * 消息推送
     * 根据定时任务，主动推送消息
     */
    @Scheduled(fixedRate = 3000)
    @Transactional
    @Override
    public void realTimeMessages() {
        ConcurrentHashMap<String, Session> sessions = WebSocketServer.getSessions();
        if (StringUtil.isEmpty(sessions)) {
            return;
        }
        // 查询提醒数据
        String redisKey = EnumLIM.EnumLIMRedis.MESSAGE_SEND_RECORD.getValue();
        // 根据会话中保存的用户ID集合查询缓存
        Map<String, Object> map = redisTemplate.opsForHash().entries(redisKey);
        if (StringUtil.isNotEmpty(map)) {
            TypeLiteral<List<DtoMessageSendRecord>> typeLiteral = new TypeLiteral<List<DtoMessageSendRecord>>() {
            };
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                Object value = entry.getValue();
                // 反序列化消息实体，并过滤掉已提醒的消息
                String userId = entry.getKey();
                List<DtoMessageSendRecord> deserialize = JsonIterator.deserialize(value.toString(), typeLiteral);
                // 未提醒
                List<DtoMessageSendRecord> notRemindList = deserialize.stream().filter(p -> !p.getIsRemind()).collect(Collectors.toList());
                // 已提醒
                List<DtoMessageSendRecord> remindList = deserialize.stream().filter(DtoMessageSendRecord::getIsRemind).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(notRemindList)) {

                    List<MessageVO> messageVOS = notRemindList.stream().map(p -> {
                        MessageVO messageVO = new MessageVO();
                        BeanUtils.copyProperties(p, messageVO);
                        return messageVO;
                    }).collect(Collectors.toList());
                    try {
                        // 发送消息，并返回状态
                        boolean sendStatus = webSocketServer.sendMessage(userId, JsonUtil.toJson(messageVOS));
                        // 发送成功更新缓存状态
                        if (sendStatus) {
                            System.out.println("消息发送成功");
                            List<DtoMessageSendRecord> messageSendRecords = notRemindList.stream().peek(p -> p.setIsRemind(Boolean.TRUE)).collect(Collectors.toList());
                            messageSendRecords.addAll(remindList.stream().filter(p -> p.getReceiver().equals(userId)).collect(Collectors.toList()));
                            String json = null;
                            try {
                                json = JsonUtil.toJson(messageSendRecords);
                            } catch (JsonProcessingException e) {
                                e.printStackTrace();
                            }
                            redisTemplate.opsForHash().put(redisKey, userId, json);
                        }
                    }catch  (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    @Autowired
    public void setWebSocketServer(WebSocketServer webSocketServer) {
        this.webSocketServer = webSocketServer;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
