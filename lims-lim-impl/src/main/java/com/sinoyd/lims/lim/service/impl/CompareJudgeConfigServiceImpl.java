package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.service.CompareJudgeConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * CompareJudgeConfig操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/14
 * @since V100R001
 */
@Service
public class CompareJudgeConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoQualityControlLimit, String, QualityControlLimitRepository> implements CompareJudgeConfigService {

    @Override
    public void findByPage(PageBean<DtoQualityControlLimit> page, BaseCriteria criteria) {
        page.setEntityName("DtoQualityControlLimit a");
        page.setSelect("select a");
        page.setSort("a.qcType-, a.judgingMethod");
        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public DtoQualityControlLimit save(DtoQualityControlLimit entity) {
        if (!validateRangeLimit(entity)) {
            throw new BaseException("数值范围或允许限值格式有误，请按照规定的格式进行配置！");
        }
        if(!StringUtil.isNotEmpty(entity.getSubstituteId())){
            entity.setSubstituteId(UUIDHelper.GUID_EMPTY);
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoQualityControlLimit update(DtoQualityControlLimit entity) {
        if (!validateRangeLimit(entity)) {
            throw new BaseException("数值范围或允许限值格式有误，请按照规定的格式进行配置！");
        }
        return super.update(entity);
    }

    /**
     * 检验质控限值的数值范围和允许限值格式是否正确
     *
     * @param limit 质控限值配置对象
     * @return 校验是否通过
     */
    private boolean validateRangeLimit(DtoQualityControlLimit limit) {
        boolean validate = true;
        if (StringUtil.isNotNull(limit)) {
            if (StringUtil.isNotEmpty(limit.getRangeConfig()) && !validateFormat(limit.getRangeConfig())) {
                validate = false;
            }
            if (StringUtil.isNotEmpty(limit.getAllowLimit()) && !validateFormat(limit.getAllowLimit())) {
                validate = false;
            }
        }
        return validate;
    }

    /**
     * 判断给定字符串是否满足允许限值的配置格式(正确的格式示例："[x] > 5" , "[x] >= 6 and [x] <= 10")
     *
     * @param s 给定字符串
     * @return 校验是否通过
     */
    private boolean validateFormat(String s) {
        s = s.replace(" ", "");
        if (StringUtil.isNotEmpty(s)) {
            if (s.contains("and")) {
                String[] arr = s.split("and");
                return arr.length == 2 && validateFormat(arr[0]) && validateFormat(arr[1]);
            } else {
                if (s.startsWith("[x]") && DivationUtils.cntSubStr(s, "[x]") == 1) {
                    s = s.replace("[x]", "");
                    if (s.startsWith("<=") || s.startsWith(">=")) {
                        s = s.substring(2);
                    } else if (s.startsWith("<") || s.startsWith(">")) {
                        s = s.substring(1);
                    }
                    if (s.contains("c") || s.contains("d")) {
                        return true;
                    } else {
                        return DivationUtils.isNumber(s);
                    }
                }
            }
        }
        return false;
    }
}
