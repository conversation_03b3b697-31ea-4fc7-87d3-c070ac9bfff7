package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.criteria.InstrumentCriteria;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.ProjectInstrumentCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails;
import com.sinoyd.lims.lim.entity.ProjectInstrumentDetails;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.ProjectInstrumentDetailsRepository;
import com.sinoyd.lims.lim.repository.lims.ProjectInstrumentRepository;
import com.sinoyd.lims.lim.service.ProjectInstrumentService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ProjectInstrument操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Service
public class ProjectInstrumentServiceImpl extends BaseJpaServiceImpl<DtoProjectInstrument, String, ProjectInstrumentRepository> implements ProjectInstrumentService {

    @Autowired
    private ProjectInstrumentDetailsRepository projectInstrumentDetailsRepository;


    @Autowired
    private PersonRepository personRepository;

    @Autowired
    @Lazy
    private InstrumentService instrumentService;

    private InstrumentRepository instrumentRepository;


    /**
     * 新增仪器出入库记录
     */
    @Transactional
    @Override
    public DtoProjectInstrument save(DtoProjectInstrument entity) {
        //先插入仪器出入库主表记录
        entity.setIsDeleted(false);
        //出库情况默认为合格
        entity.setOutQualified(1);
        //根据人员id获取人员名称
        setPersonNameByIds(entity);
//        if (StringUtil.isNotEmpty(entity.getProjectIdList())) {
//            List<String> notNullProjectIdList = new ArrayList<>();
//            for (String id : entity.getProjectIdList()) {
//                if (StringUtil.isNull(id)) {
//                    id = "";
//                }
//                notNullProjectIdList.add(id);
//            }
//            //前端可以选择多个项目id
//            entity.setProjectId(String.join(";", notNullProjectIdList));
//        }
//        if (StringUtil.isNull(entity.getProjectId())) {
//            //若projectId为空，则将projectId置为空字符串，避免数据库字段默认值"0000-0000-0000..."影响前端修改接口传参时判断是否将projectName属性清空
//            // (前端通过判断projectId是否为空，来决定是否清空projectName属性)
//            entity.setProjectId("");
//        }
        List<String> projectNameList = new ArrayList<>();
        List<String> projectIdList = new ArrayList<>();
        for (Map<String, String> projectMap : entity.getProjectList()) {
            projectNameList.add(projectMap.get("projectName"));
            if (StringUtil.isNotEmpty(projectMap.get("id")) && !UUIDHelper.GUID_EMPTY.equals(projectMap.get("id"))) {
                projectIdList.add(projectMap.get("id"));
            }
        }
        if (StringUtil.isNotEmpty(projectIdList)) {
            entity.setProjectId(String.join(";", projectIdList));
        }
        if (StringUtil.isNotEmpty(projectNameList)) {
            entity.setProjectName(String.join("、", projectNameList));
        }
        DtoProjectInstrument dtoProjectInstrument = super.save(entity);
        if (StringUtil.isNull(dtoProjectInstrument)) {
            throw new BaseException("新增仪器出入库失败！");
        }
        return dtoProjectInstrument;
    }

    /**
     * 修改仪器出入库记录
     */
    @Transactional
    @Override
    public DtoProjectInstrument update(DtoProjectInstrument entity) {
        DtoProjectInstrument projectInstrument = repository.findOne(entity.getId());
        if (StringUtil.isNull(projectInstrument) || projectInstrument.getIsDeleted()) {
            throw new BaseException("仪器出入库信息不存在或已被删除！");
        }
        //修改出入库主表
        setPersonNameByIds(entity);

        List<Map<String, String>> projectMapList = entity.getProjectList();
        List<String> projectNameList = new ArrayList<>();
        List<String> projectIdList = new ArrayList<>();
        for (Map<String, String> projectMap : projectMapList) {
            if (StringUtil.isNotEmpty(projectMap.get("id")) && !UUIDHelper.GUID_EMPTY.equals(projectMap.get("id"))) {
                projectIdList.add(projectMap.get("id"));
            }
            projectNameList.add(projectMap.get("projectName"));
        }
        if (StringUtil.isNotEmpty(projectNameList)) {
            entity.setProjectName(String.join("、", projectNameList));
        }
        if (StringUtil.isNotEmpty(projectIdList)) {
            //前端可以选择多个项目id
            entity.setProjectId(String.join(";", projectIdList));
        }
//        if (StringUtil.isNull(entity.getProjectId())) {
//            //表示项目名称是用户手动输入，需要清空原来的 projectId
//            entity.setProjectId("");
//        }
        //只修改出入库基本信息,不改动出入库合格情况
        entity.setIntQualified(projectInstrument.getIntQualified());
        entity.setOutQualified(projectInstrument.getOutQualified());
        return super.update(entity);
    }


    /**
     * 按照主键列表删除仪器出入库记录
     */
    @Transactional
    @Override
    public int deleteByIds(List<String> ids) {
        if (StringUtil.isEmpty(ids)) {
            return 0;
        }
        //先刪除仪器出入库主表记录
        int cnt = super.logicDeleteById(ids);
        //再删除出入库明细
        projectInstrumentDetailsRepository.deleteByProjectInstrumentIdIn(ids);
        return cnt;
    }

    /**
     * 仪器入库
     */
    @Transactional
    @Override
    public int instrumentIn(DtoProjectInstrumentDetails projectInstrumentDetails) {
        String projectInstrumentId = projectInstrumentDetails.getProjectInstrumentId();
        DtoProjectInstrument dtoProjectInstrument = checkExist(projectInstrumentId);
        //入库合格情况
        int inQualified = projectInstrumentDetails.getInQualified();
        //入库日期
        Date inDate = projectInstrumentDetails.getInDate();
        //入库人
        String inPerson = projectInstrumentDetails.getInPerson();
        //找到原有的出入库记录明细
        List<DtoProjectInstrumentDetails> projectInstrumentDetailsList = projectInstrumentDetailsRepository.findByProjectInstrumentId(projectInstrumentId);
        //过滤出未入库的明细记录
        List<DtoProjectInstrumentDetails> notInDetailsList = projectInstrumentDetailsList.stream().filter(p -> !p.getIsStorage()).collect(Collectors.toList());
        //前端传递的需要入库的仪器id列表
        List<String> instrumentIdList = projectInstrumentDetails.getInstrumentIdList();
        //需要做入库操作的明细列表
        List<DtoProjectInstrumentDetails> inDetailsList = new ArrayList<>();
        for (DtoProjectInstrumentDetails notInDetails : notInDetailsList) {
            if (instrumentIdList.contains(notInDetails.getInstrumentId())) {
                notInDetails.setIsStorage(true);
                notInDetails.setInQualified(inQualified);
                notInDetails.setInDate(inDate);
                notInDetails.setInPerson(inPerson);
                inDetailsList.add(notInDetails);
            }
        }
        int inCnt = 0;
        if (StringUtil.isNotEmpty(inDetailsList)) {
            projectInstrumentDetailsRepository.save(inDetailsList);
            inCnt = inDetailsList.size();
        }

        if (inCnt > 0) {
            if (dtoProjectInstrument.getIntQualified() == 1) {
                if (inQualified == 0) {
                    //如果仪器出入库主表的入库状态为合格，且本次入库的仪器入库情况为不合格，则需要将入库记录主表的入库情况更改为不合格
                    dtoProjectInstrument.setIntQualified(0);
                    super.update(dtoProjectInstrument);
                }
            } else if (dtoProjectInstrument.getIntQualified() == -1) {
                //如果仪器出入库主表的入库状态为空，则需要根据本次入库的合格情况，对应的更新入库记录主表的入库情况
                dtoProjectInstrument.setIntQualified(inQualified);
                super.update(dtoProjectInstrument);
            }
        }
        return inCnt;
    }

    /**
     * 仪器取消入库
     */
    @Transactional
    @Override
    public int instrumentCancelIn(DtoProjectInstrumentDetails projectInstrumentDetails) {
        String projectInstrumentId = projectInstrumentDetails.getProjectInstrumentId();
        DtoProjectInstrument projectInstrument = checkExist(projectInstrumentId);
        //找到所有出入库记录明细
        List<DtoProjectInstrumentDetails> projectInstrumentDetailsList = projectInstrumentDetailsRepository.findByProjectInstrumentId(projectInstrumentId);
        //过滤出已入库的明细记录
        List<DtoProjectInstrumentDetails> inDetailsList = projectInstrumentDetailsList.stream().filter(ProjectInstrumentDetails::getIsStorage).collect(Collectors.toList());
        //存在已入库的仪器明细时才进行取消入库操作
        int cancelCnt = 0;
        if (StringUtil.isNotEmpty(inDetailsList)) {
            //前端传递的需要取消入库的仪器id列表
            List<String> instrumentIdList = projectInstrumentDetails.getInstrumentIdList();
            if (StringUtil.isNotEmpty(instrumentIdList)) {
                //需要做取消入库操作的明细列表
                List<DtoProjectInstrumentDetails> cancelInDetailsList = new ArrayList<>();
                //剩余已入库的仪器明细列表
                List<DtoProjectInstrumentDetails> remainInDetailsList = new ArrayList<>();
                for (DtoProjectInstrumentDetails inDetails : inDetailsList) {
                    if (instrumentIdList.contains(inDetails.getInstrumentId())) {
                        DtoProjectInstrumentDetails cancelInDetail = new DtoProjectInstrumentDetails();
                        BeanUtils.copyProperties(inDetails, cancelInDetail, "inDate");
                        cancelInDetail.setIsStorage(false);
                        cancelInDetail.setInQualified(-1);
                        cancelInDetail.setInPerson(UUIDHelper.GUID_EMPTY);
                        //入库日期改为默认值 1753-01-01 00:00:00
                        cancelInDetail.setInDate(DateUtil.stringToDate("1753-01-01 00:00:00", DateUtil.FULL));
                        cancelInDetailsList.add(cancelInDetail);
                    } else {
                        remainInDetailsList.add(inDetails);
                    }
                }

                if (StringUtil.isNotEmpty(cancelInDetailsList)) {
                    List<String> instrumentIdForCancel = cancelInDetailsList.stream().map(DtoProjectInstrumentDetails::getInstrumentId).distinct().collect(Collectors.toList());
                    //校验是否能进行取消入库操作(如果仪器已在其他出入库记录中出库则不能进行取消入库操作)
                    checkCancelIn(instrumentIdForCancel, projectInstrumentId);
                    List<DtoProjectInstrumentDetails> actCancelInDetailsList = new ArrayList<>();
                    List<String> remainInDetailsIdList = new ArrayList<>();
                    for (DtoProjectInstrumentDetails details : cancelInDetailsList) {
                        if (instrumentIdForCancel.contains(details.getInstrumentId())) {
                            actCancelInDetailsList.add(details);
                        } else {
                            remainInDetailsIdList.add(details.getId());
                        }
                    }
                    if (StringUtil.isNotEmpty(actCancelInDetailsList)) {
                        projectInstrumentDetailsRepository.save(actCancelInDetailsList);
                    }
                    if (StringUtil.isNotEmpty(remainInDetailsIdList)) {
                        List<DtoProjectInstrumentDetails> extraRemainDetailList = inDetailsList.stream().filter(p -> remainInDetailsIdList.contains(p.getId()))
                                .collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(extraRemainDetailList)) {
                            remainInDetailsList.addAll(extraRemainDetailList);
                        }
                    }
                    cancelCnt = actCancelInDetailsList.size();
                }
                if (cancelCnt > 0) {
                    //原本的入库合格情况
                    int inQualified = projectInstrument.getIntQualified();
                    if (StringUtil.isEmpty(remainInDetailsList)) {
                        //全部取消入库的情况
                        if (inQualified != -1) {
                            projectInstrument.setIntQualified(-1);
                            repository.save(projectInstrument);
                        }
                    } else {
                        //未全部取消入库的情况，统计剩余已入库的明细记录的入库情况
                        int[] arr = calcInQualifiedCnt(remainInDetailsList);
                        if (arr[0] > 0) {
                            //入库不合格数量大于0
                            if (inQualified != 0) {
                                projectInstrument.setIntQualified(0);
                                repository.save(projectInstrument);
                            }
                        } else if (arr[1] > 0) {
                            //入库不合格数量等于0, 入库合格数量大于0
                            if (inQualified != 1) {
                                projectInstrument.setIntQualified(1);
                                repository.save(projectInstrument);
                            }
                        } else {
                            //入库不合格数量等于0, 入库合格数量等于0
                            if (inQualified != -1) {
                                projectInstrument.setIntQualified(-1);
                                repository.save(projectInstrument);
                            }
                        }
                    }
                }
            }
        }
        return cancelCnt;
    }

    /**
     * 校验是否能进行取消入库操作(如果仪器已在其他出入库记录中出库则不能进行取消入库操作)
     *
     * @param instrumentIdForCancel 需要取消入库的仪器id列表
     * @return projectInstrumentId  仪器出入库记录id
     */
    private void checkCancelIn(List<String> instrumentIdForCancel, String projectInstrumentId) {
        List<DtoProjectInstrumentDetails> detailsList = projectInstrumentDetailsRepository.findByInstrumentIdInAndIsStorageFalse(instrumentIdForCancel);
        //过滤出已存在其他出入库记录并且未入库的明细
        detailsList = detailsList.stream().filter(p -> !projectInstrumentId.equals(p.getProjectInstrumentId())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(detailsList)) {
            List<String> notPassInstrumentIdList = detailsList.stream().map(DtoProjectInstrumentDetails::getInstrumentId).distinct().collect(Collectors.toList());
            instrumentIdForCancel.removeAll(notPassInstrumentIdList);
            List<DtoInstrument> notPassInstrumentList = instrumentRepository.findAll(notPassInstrumentIdList);
            List<String> notPassInstrumentNameList = notPassInstrumentList.stream().map(DtoInstrument::getInstrumentName).distinct().collect(Collectors.toList());
            throw new BaseException(String.join("、", notPassInstrumentNameList) + " 已再另一单出库，不能取消入库！");
        }
    }

    /**
     * 统计入库情况的不合格数量
     *
     * @param detailsList 入库明细列表
     * @return 第一个元素为入库不合格数量, 第二个元素为入库合格数量
     */
    private int[] calcInQualifiedCnt(List<DtoProjectInstrumentDetails> detailsList) {
        int inNotQualify = 0;
        int inQualify = 0;
        for (DtoProjectInstrumentDetails details : detailsList) {
            if (details.getInQualified() == 0) {
                inNotQualify++;
            } else if (details.getInQualified() == 1) {
                inQualify++;
            }
        }
        int[] resArr = new int[4];
        resArr[0] = inNotQualify;
        resArr[1] = inQualify;
        return resArr;
    }

    /**
     * 根据人员ids获取并设置人员名称
     *
     * @param entity DtoProjectInstrument对象
     */
    private void setPersonNameByIds(DtoProjectInstrument entity) {
        String userIds = entity.getUserIds();
        if (StringUtil.isNotEmpty(userIds)) {
            List<String> userIdList = Arrays.asList(userIds.split(","));
            if (StringUtil.isNotEmpty(userIdList)) {
                List<DtoPerson> personList = personRepository.findByIdIn(userIdList);
                StringBuilder sb = new StringBuilder();
                for (DtoPerson person : personList) {
                    sb.append(person.getCName());
                    sb.append(",");
                }
                String userNames = sb.toString();
                if (userNames.length() > 0) {
                    userNames = userNames.substring(0, userNames.length() - 1);
                }
                entity.setUserNames(userNames);
            }
        }
        String adminId = entity.getAdministratorId();
        if (StringUtil.isNotEmpty(adminId)) {
            DtoPerson person = personRepository.findOne(adminId);
            if (StringUtil.isNotNull(person)) {
                entity.setAdministratorName(person.getCName());
            }
        }
    }

    @Override
    public void getOutInstrument(PageBean<DtoInstrument> page, BaseCriteria criteria) {
        InstrumentCriteria instrumentCriteria = (InstrumentCriteria) criteria;
        //过滤掉已出库的仪器
        instrumentCriteria.setFilterOutInstrument(true);
        instrumentService.findByPage(page, instrumentCriteria);
    }

    @Override
    public void getInInstrument(PageBean<DtoInstrument> page, BaseCriteria criteria) {
        ProjectInstrumentCriteria projectInstrumentCriteria = (ProjectInstrumentCriteria) criteria;
        String projectInstrumentId = projectInstrumentCriteria.getProjectInstrumentId();
        List<DtoProjectInstrumentDetails> projectInstrumentDetailsList = projectInstrumentDetailsRepository.findByProjectInstrumentId(projectInstrumentId);
        //排除掉已入库的仪器明细
        List<DtoProjectInstrumentDetails> filterDetailList = projectInstrumentDetailsList.stream().filter(p -> !p.getIsStorage()).collect(Collectors.toList());
        List<String> instrumentIdList = filterDetailList.stream().map(DtoProjectInstrumentDetails::getInstrumentId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(instrumentIdList)) {
            page.setEntityName("DtoInstrument a");
            page.setSelect("select a");
            page.addCondition(String.format(" and a.id in ('%s')", String.join("','", instrumentIdList)));
            page.addCondition(" and a.isDeleted = 0");
            comRepository.findByPage(page);
        } else {
            page.setData(null);
            page.setRowsCount(0);
        }
    }

    /**
     * 检查出入库记录是否存在
     *
     * @param projectInstrumentId 仪器出入库对象id
     * @return DtoProjectInstrument
     */
    private DtoProjectInstrument checkExist(String projectInstrumentId) {
        DtoProjectInstrument dtoProjectInstrument = super.findOne(projectInstrumentId);
        if (StringUtil.isNull(dtoProjectInstrument) || dtoProjectInstrument.getIsDeleted()) {
            throw new BaseException("仪器出入库记录不存在!");
        }
        return dtoProjectInstrument;
    }

    @Autowired
    public void setInstrumentRepository(InstrumentRepository instrumentRepository) {
        this.instrumentRepository = instrumentRepository;
    }
}