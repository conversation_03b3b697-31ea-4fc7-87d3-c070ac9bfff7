package com.sinoyd.base.repository.rcc;

import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * EvaluationLevel数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
public interface EvaluationLevelRepository extends IBaseJpaPhysicalDeleteRepository<DtoEvaluationLevel, String>, LimsRepository<DtoEvaluationLevel, String> {

    /**
     * 根据评价id获取所有的评价等级信息
     *
     * @param evaluationId 评价id
     * @return 返回所有的评价等级信息
     */
    List<DtoEvaluationLevel> findByEvaluationId(String evaluationId);

    /**
     * 根据评价id获取所有的评价等级信息
     *
     * @param evaluationId 评价id
     * @return 返回所有的评价等级信息
     */
    List<DtoEvaluationLevel> findByEvaluationIdIn(List<String> evaluationId);

    /**
     * 判断同一个评价标准编码是否重复
     *
     * @param evaluationId
     * @param orderNum     编码
     * @return 返回个数
     */
    Integer countByEvaluationIdAndOrderNum(String evaluationId, Integer orderNum);


    /**
     * 判断同一个评价标准编码是否重复
     *
     * @param evaluationId
     * @param orderNum     编码
     * @param id           主键id
     * @return 返回个数
     */
    Integer countByEvaluationIdAndOrderNumAndIdNot(String evaluationId, Integer orderNum, String id);

    /**
     * 查询标准条件子记录
     *
     * @param parentId
     * @param evaluationId
     * @return
     */
    List<DtoEvaluationLevel> findByParentIdAndEvaluationId(String parentId, String evaluationId);
}