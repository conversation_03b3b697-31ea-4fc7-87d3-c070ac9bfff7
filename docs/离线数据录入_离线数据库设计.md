# 一、业务数据库表结构文档

## 1. 采样记录表 (Sample Record Table)

### 表名: `receive_sample_record` 或 `sampling_record`

### 字段说明

| 字段名                | 数据类型     | 是否必填 | 默认值 | 说明                   |
| --------------------- | ------------ | -------- | ------ | ---------------------- |
| **主键字段**          |              |          |        |                        |
| `id`                  | VARCHAR(50) | 是       | -      | 主键ID，UUID格式       |
| `recordCode`          | VARCHAR(50)  | 是       | -      | 送样单编号             |
| **采样信息**          |              |          |        |                        |
| `samplingPersonNames` | VARCHAR(200) | 否       | -      | 采样人员姓名           |
| `samplingTime`        | VARCHAR(50)  | 否       | -      | 采样时间（格式化显示） |
| `pushTime`            | DATE         | 否       | NULL   | 数据上传时间           |
| `receiveSampleRecordCode` | VARCHAR(200) | 否 | - | 采样单号 |
| `leaderName` | VARCHAR(200) | 否 | - | 采样负责人 |
| `sampleTypeNames` | VARCHAR(200) | 否 | - | 样品类型名称 |
| **点位信息** |||||
| `folderName` | VARCHAR(500) | 否 | - | 点位名称（多个用逗号分隔） |
| `folderCount` | INT | 否 | 0 | 点位数量 |
| **项目信息** |||||
| `projectCode` | VARCHAR(50) | 否 | NULL | 项目编码 |
| `projectName` | VARCHAR(200) | 否 | NULL | 项目名称 |
| `projectTypeName` | VARCHAR(100) | 否 | NULL | 项目类型名称 |
| **受检单位信息** |||||
| `inspectedEnt` | VARCHAR(200) | 否 | NULL | 受检单位名称 |
| `inspectedAddress` | VARCHAR(500) | 否 | NULL | 受检单位地址 |
| `orgId` | VARCHAR(50) | 是 |  | 所属机构 |


## 2. 点位表

### 表名: `sample_folder`

### 字段说明

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| **主键字段** |||||
| `id` | VARCHAR(50) | 是 | - | 主键ID，UUID格式 |
| `receiveId ` | VARCHAR(50) | 是 | - | 采样单id |
| **点位信息** |||||
| `folderId` | VARCHAR(50) | 否 |  | 点位id |
| `watchSpot ` | VARCHAR(500) | 否 | - | 点位名称 |
| `periodCount ` | INT | 否 | - | 周期 |
|  `timePerPeriod ` | INT | 否 | - | 批次 |
|  `samplePerTime ` | INT | 否 | - | 样次 |
|  `sampleCodes ` | VARCHAR(500) | 否 | - | 样品编号 |
|  `sampleCount ` | VARCHAR(500) | 否 | - | 样品数量 |
|  `analyzeItems ` | VARCHAR(500) | 否 | - | 测试项目 |
|  `analyzeCount ` | VARCHAR(500) | 否 | - | 测试数量 |
|  `sampleTypeName ` | VARCHAR(500) | 否 | - | 检测类型 |
|  `lon ` | VARCHAR(500) | 否 | - | 计划经度 |
|  `lat ` | VARCHAR(500) | 否 | - | 计划纬度 |
| `orgId` | VARCHAR(50) | 是 |  | 所属机构 |

### 点位签到表

### 表名：`folder_sign`

### 字段说明

| 字段名             | 数据类型     | 是否必填 | 默认值 | 说明             |
| ------------------ | ------------ | -------- | ------ | ---------------- |
| **主键字段**       |              |          |        |                  |
| `id`               | VARCHAR(50)  | 是       | -      | 主键ID，UUID格式 |
| `pointId`          | VARCHAR(50)  | 否       | NULL   | 点位id           |
| `folderId`         |              |          |        |                  |
| `periodCount `     |              |          |        |                  |
| `signLon `         | VARCHAR(500) | 否       | -      | 签到经度         |
| `signLat `         | VARCHAR(500) | 否       | -      | 签到纬度         |
| `signTip  `        | VARCHAR(500) | 否       | -      | 签到说明         |
| `signTime   `      | VARCHAR(500) | 否       | -      | 签到时间         |
| `signPersonId  `   | VARCHAR(500) | 否       | -      | 签到人Id         |
| `signPersonName  ` | VARCHAR(500) | 否       | -      | 签到人名称       |
| `orgId`            | VARCHAR(50)  | 是       |        | 所属机构         |

## 3. 样品表

## 表名: `sample_item_list`

### 表名: `sample_item_list`

### 字段说明

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| **主键字段** |||||
| `id` | VARCHAR(50) | 是 | - | 主键ID，UUID格式 |
| **关联字段** |||||
| `receiveId` | VARCHAR(50) | 是 | - | 采样单ID，关联采样记录表 |
| `folderId ` | VARCHAR(50) | 否 | NULL | 点位ID，关联点位表 |
| `pointId ` |  |  |  |  |
| **样品信息** |||||
| `code` | VARCHAR(100) | 否 | - | 样品编码 |
| `redFolderName` | VARCHAR(200) | 否 | - | 点位名称 |
|  `sampleTypeId` | VARCHAR(50) | 否 | - | 检测类型 |
| `periodCount ` | INT | 否 | - | 周期 |
| `timePerPeriod ` | INT | 否 | - | 批次 |
| `samplePerTime ` | INT | 否 | - | 样次 |
| `orgId` | VARCHAR(50) | 是 |  | 所属机构 |

## 4. 数据表

### 表名: `analyse_data`

### 字段说明

| 字段名       | 数据类型    | 是否必填 | 默认值 | 说明             |
| ------------ | ----------- | -------- | ------ | ---------------- |
| **主键字段** |             |          |        |                  |
| `id`         | VARCHAR(50) | 是       | -      | 主键ID，UUID格式 |
| **关联字段** |             |          |        |                  |
| `sampleId`   | VARCHAR(50) | 是       | -      | 样品id           |
| `testId `    | VARCHAR(50) | 是       |        | 测试项目id       |
| `orgId`      | VARCHAR(50) | 是       |        | 所属机构         |

## 5. 分组表

### 表名: `sample_group`

### 字段说明

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| **主键字段** |||||
| `id` | VARCHAR(50) | 是 | - | 主键ID，UUID格式 |
| **关联字段** |||||
| `sampleId` | VARCHAR(50) | 是 | - | 样品id |
| `sampleTypeGroupId`   | VARCHAR(50) | 否 | NULL | 分组id           |
| `sampleTypeGroupName` | VARCHAR(255) | 否 | NULL | 分组名称 |
| `analyseItemNames` | VARCHAR(2000) | 否 | NULL | 分析项目名称 |
| `fixer` | VARCHAR(1000) | 否 | NULL | 固定剂 |
| `containerName` | VARCHAR(255) | 否 | NULL | 容器名称 |
| `orgId` | VARCHAR(50) | 是 |  | 所属机构 |

### 分组关联项目表

### 表名：`sample_group_test`

### 字段说明

| 字段名               | 数据类型     | 是否必填 | 默认值 | 说明             |
| -------------------- | ------------ | -------- | ------ | ---------------- |
| **主键字段**         |              |          |        |                  |
| id                   | VARCHAR(50)  | 是       | -      | 主键ID，UUID格式 |
| **关键字段**         |              |          |        |                  |
| sampleGroupId        | VARCHAR(50)  | 否       | -      | 分组id           |
| testId               | VARCHAR(50)  | 否       | -      | 测试项目id       |
| analyzeItemId        | VARCHAR(50)  | 否       | -      | 分析项目id       |
| redAnalyzeMethodName | VARCHAR(255) | 否       | NULL   | 分析方法名称     |
| redCountryStandard   | VARCHAR(100) | 否       | NULL   | 国家标准         |

## 6. 参数表（公共、点位）

- 公共参数表receiveId （送样单id关联）
- 点位参数表folderId （点位id关联）

### 6.1 表名: `public_params_data` 公共参数表

### 字段说明

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| **主键字段** |||||
| `id` | VARCHAR(50) | 是 | - | 主键ID，UUID格式 |
| **关联字段** |||||
| `receiveId` | VARCHAR(50) | 是 | - | 采样id |
| `paramBucket` | TEXT | 否 | NULL | 参数数据JSON字符串 |
| `orgId` | VARCHAR(50) | 是 |  | 所属机构 |

### paramBucket 数据结构详解

`paramBucket` 字段存储的是JSON字符串，解析后的结构如下：

```json
  {
    "paramsType": "string",           // 参数类型
    "paramsDataPhoneList": [          // 参数数据列表
      {
        "paramName": "string",        // 参数名称
        "paramValue": "string",       // 参数值
        "paramUnit": "string",        // 参数单位
        "paramsType": "string",         //公共参数、样品参数、分析项目参数、点位参数
        "defaultControl": "string",   // 类型
        "referenceText": "string",     // 默认值
        "paramsConfigId": "string",     // ???用到了
        "dataSource":[
            {
                "key":"string",
                "value":"string"
            }
        ]
      }
    ]
  }
```

### 6.2 表名: `folder_params_data` 点位参数表

### 字段说明

| 字段名           | 数据类型    | 是否必填 | 默认值 | 说明                           |
| ---------------- | ----------- | -------- | ------ | ------------------------------ |
| **主键字段**     |             |          |        |                                |
| `id`             | VARCHAR(50) | 是       | -      | 主键ID，UUID格式               |
| **关联字段**     |             |          |        |                                |
| `sampleFolderId` | VARCHAR(50) | 是       | -      | 点位id（不按周期分组的点位id） |
| `paramBucket`    | TEXT        | 否       | NULL   | 参数数据JSON字符串             |
| `orgId`          | VARCHAR(50) | 是       |        | 所属机构                       |

### paramBucket 数据结构详解

`paramBucket` 字段存储的是JSON字符串，解析后的结构如下：

``````json
  {
    "paramsType": "string",           // 参数类型
    "paramsDataPhoneList": [          // 参数数据列表
      {
        "paramName": "string",        // 参数名称
        "paramValue": "string",       // 参数值
        "paramUnit": "string",        // 参数单位
        "paramsType": "string",         //公共参数、样品参数、分析项目参数、点位参数
        "defaultControl": "string",   // 类型
        "referenceText": "string",     // 默认值
        "paramsConfigId": "string",     // ???用到了
        "dataSource":[
            {
                "key":"string",
                "value":"string"
            }
        ]
      }
    ]
  }
``````



## 7. 样品数据表

### 表名: `sample_params_data`

### 字段说明

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| **主键字段** |||||
| `id` | VARCHAR(36) | 是 | - | 主键ID，UUID格式 |
| **关联字段** |||||
| `receiveId` | VARCHAR(36) | 是 | - | 采样id |
| `pointId ` | VARCHAR(36) | 否 | NULL | 点位ID，关联点位表（按周期拆分） |
| `code ` | VARCHAR(36) | 否 | NULL | 样品编号 |
| `sampleTypeId ` | VARCHAR(36) | 否 | NULL | 样品类型id |
| `redFolderName ` | VARCHAR(36) | 否 | NULL | 点位名称 |
| `qcType ` | VARCHAR(36) | 否 | NULL | ???用到了 |
| `paramBucket` | TEXT | 否 | NULL | 参数数据JSON字符串 |
| `orgId` | VARCHAR(50) | 是 |  | 所属机构 |

### paramBucket 数据结构详解

`paramBucket` 字段存储的是JSON字符串，解析后的结构如下：

```json
  {
    "sampleCode": "string",           // 参数类型样品编号samId
    "samId": "string",                // 样品id
    "qcType": "string",               //?
    "analyseDataPhoneList": [          // 分析数据列表
      {
        "itemName": "string",        // 
        "isPass": "string",       // 
        "testId": "string",        // 
        "anaId": "string",        // 
        "sampleId": "string",        //       
        "anaValue": "string",        // 
        "formula": "string",        // 
        "formulaId": "string",        // 
        "analyseOriginalJsonList":[ //分析数据公式参数列表
            {
                "defaultValue":"string",
                "alias":"string",
                "isMust":"string",
                "isEditable":"string",
                "orderNum":"string",
            }
        ]
      }
    ],
    "paramsDataPhoneList": [          // 参数数据列表
      {
        "paramName": "string",        // 参数名称
        "paramValue": "string",       // 参数值
        "paramsType": "string",       // 参数类型
        "dimension": "string",        //  量纲
        "groupId": "string",         // 分组id
        "defaultControl": "string",   //  
        "referenceText": "string",     // 
        "sampleId": "string",     // 
        "orderNum": "string",     // 
        "paramsConfigId": "string",     // 
        "dataSource":[
            {
                "key":"string",
                "value":"string"
            }
        ]
      }
    ]
  }
```

## 8. 附件表

### 表名: `document`

### 字段说明

| 字段名           | 数据类型     | 是否必填 | 默认值 | 说明             |
| ---------------- | ------------ | -------- | ------ | ---------------- |
| **主键字段**     |              |          |        |                  |
| `id`             | VARCHAR(36)  | 是       | -      | 主键ID，UUID格式 |
| **关联字段**     |              |          |        |                  |
| `folderId`       | VARCHAR(50)  | 是       | -      | 关联id           |
| `fileName`       | VARCHAR(255) | 否       | NULL   | 文件名称         |
| `physicalName `  | VARCHAR(255) | 否       | NULL   | 物理文件名称     |
| `path`           | VARCHAR(500) | 否       | NULL   | 文件路径         |
| `docTypeId `     | VARCHAR(50)  | 否       | NULL   | 文件类型         |
| `docTypeName `   | VARCHAR(255) | 否       | NULL   | 文件类型名称     |
| `docSize `       | INT          | 否       | NULL   | 文件大小         |
| `docSuffix`      | VARCHAR(10)  | 否       | NULL   | 文件后缀         |
| `uploadPersonId` | VARCHAR(50)  | 否       | NULL   | 上传人Id         |
| `uploadPerson`   | VARCHAR(50)  | 否       | NULL   | 上传人名称       |
| `orgId`          | VARCHAR(50)  | 是       |        | 所属机构         |

# 二、配置数据表结构文档

## 1. 分组配置表

### 表名: `sample_group_type`

### 字段说明

| 字段名               | 数据类型      | 是否必填 | 默认值 | 说明                           |
| -------------------- | ------------- | -------- | ------ | ------------------------------ |
| **主键字段**         |               |          |        |                                |
| `id`                 | VARCHAR(36)   | 是       | -      | 主键ID，UUID格式               |
| `parentId`           | VARCHAR(50)   | 是       | -      | 采样id                         |
| `groupType`          | INT           | 否       | NULL   | 分组类型（1.分组规则，2.分组） |
| `groupName `         | VARCHAR(50)   | 否       | NULL   | 分组名称                       |
| `sampleTypeId `      | VARCHAR(50)   | 否       | NULL   | 样品类型id                     |
| `fixer `             | VARCHAR(1000) | 否       | NULL   | 固定剂                         |
| `containerName `     | VARCHAR(255)  | 否       | NULL   | 容器名称                       |
| `saveCondition`      | VARCHAR(1000) | 否       | NULL   | 保存条件                       |
| `volumeType`         | VARCHAR(100)  | 否       | NULL   | 体积类型                       |
| `pretreatmentMethod` | VARCHAR(200)  | 否       | NULL   | 前处理方式                     |
| `orderNum`           | INT           | 否       | NULL   | 排序值                         |
| `orgId`              | VARCHAR(50)   | 是       |        | 所属机构                       |

## 2. 分组测试项目关系表

### 表名: `sample_group_type_test`

### 字段说明

| 字段名              | 数据类型    | 是否必填 | 默认值 | 说明             |
| ------------------- | ----------- | -------- | ------ | ---------------- |
| **主键字段**        |             |          |        |                  |
| `id`                | VARCHAR(36) | 是       | -      | 主键ID，UUID格式 |
| `sampleTypeGroupId` | VARCHAR(50) | 是       | -      | 样品分组id       |
| `testId`            | VARCHAR(50) | 是       | -      | 测试项目id       |

## 3. 检测类型表

### 表名: `sample_type`

### 字段说明

| 字段名       | 数据类型    | 是否必填 | 默认值 | 说明             |
| ------------ | ----------- | -------- | ------ | ---------------- |
| **主键字段** |             |          |        |                  |
| `id`         | VARCHAR(36) | 是       | -      | 主键ID，UUID格式 |
| `parentId`   | VARCHAR(50) | 是       | -      | 父id             |
| `typeCode `  | VARCHAR(50) | 否       | NULL   | 类型编号         |
| `typeName `  | VARCHAR(50) | 否       | NULL   | 类型名称         |
| `shortName`  | VARCHAR(50) | 否       | NULL   | 简称             |
| `orderNum`   | INT         | 否       | NULL   | 排序值           |
| `category`   | INT         | 否       | NULL   | 样品分类         |
| `orgId`      | VARCHAR(50) | 是       |        | 所属机构         |

## 4. 人员表

### 表名: `person`

### 字段说明

| 字段名       | 数据类型    | 是否必填 | 默认值 | 说明             |
| ------------ | ----------- | -------- | ------ | ---------------- |
| **主键字段** |             |          |        |                  |
| `id`         | VARCHAR(36) | 是       | -      | 主键ID，UUID格式 |
| `cName`      | VARCHAR(36) | 是       | -      | 人员名称         |
| `orgId`      | VARCHAR(50) | 是       |        | 所属机构         |