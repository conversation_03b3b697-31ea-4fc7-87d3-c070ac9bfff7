package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * SHSamplingInstrument查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/28
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SHSamplingInstrumentNewCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 点位id
     */
    private String taskId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if(StringUtil.isNotEmpty(this.taskId)) {
            condition.append(" and a.taskId = :taskId");
            values.put("taskId",this.taskId);
        }
        return condition.toString();
    }

}
