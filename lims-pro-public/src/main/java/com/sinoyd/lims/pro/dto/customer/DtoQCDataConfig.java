package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 质控数据的配置
 * <AUTHOR>
 * @version V1.0.0 2020/02/10
 * @since V100R001
 */
@Data
public class DtoQCDataConfig {

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 数据类型(枚举EnumDataType：1.空白 2.加标)
     */
    private Integer type;

    /**
     * 参数名称
     */
    private String paramsName="";

    /**
     * 质控数据
     */
    private List<DtoQCDataDetail> qcDataDetails = new ArrayList<>();
}
