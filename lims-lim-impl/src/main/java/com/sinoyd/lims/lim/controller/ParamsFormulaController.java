package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ParamsFormulaCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.service.ParamsFormulaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数公式管理控制器
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "参数公式管理: 参数公式管理服务")
@RestController
@RequestMapping("/api/lim/paramsFormula")
@Validated
public class ParamsFormulaController extends BaseJpaController<DtoParamsFormula, String, ParamsFormulaService> {

    /**
     * 根据id获取参数公式
     *
     * @param id 参数公式id
     * @return 参数公式实体
     */
    @ApiOperation(value = "按主键获取参数公式", notes = "按主键获取参数公式")
    @GetMapping("/{id}")
    public RestResponse<DtoParamsFormula> getById(@PathVariable String id) {
        RestResponse<DtoParamsFormula> restResponse = new RestResponse<>();
        DtoParamsFormula params = service.findOne(id);
        restResponse.setData(params);
        restResponse.setRestStatus(StringUtil.isNull(params) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResponse;
    }

    /**
     * 分页查询参数
     *
     * @param criteria 查询条件
     * @return 实体集合
     */
    @ApiOperation(value = "分页查询参数", notes = "分页查询参数")
    @GetMapping
    public RestResponse<List<DtoParamsFormula>> findByPage(ParamsFormulaCriteria criteria) {
        RestResponse<List<DtoParamsFormula>> restResponse = new RestResponse<>();
        PageBean<DtoParamsFormula> pageBean = super.getPageBean();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增参数公式
     *
     * @param params 参数公式实体
     * @return 新增的参数公式实体
     */
    @ApiOperation(value = "新增参数公式", notes = "新增参数公式")
    @PostMapping("")
    public RestResponse<DtoParamsFormula> create(@Validated @RequestBody DtoParamsFormula params) {
        RestResponse<DtoParamsFormula> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParamsFormula data = service.save(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新参数公式
     *
     * @param params 参数公式实体
     * @return 更新后的参数公式实体
     */
    @ApiOperation(value = "更新参数公式", notes = "更新参数公式")
    @PutMapping("")
    public RestResponse<DtoParamsFormula> update(@Validated @RequestBody DtoParamsFormula params) {
        RestResponse<DtoParamsFormula> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParamsFormula data = service.update(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数公式id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数公式", notes = "根据id批量删除参数公式")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数公式ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数公式", notes = "根据id批量删除参数公式")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }
}