package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.AnalyzeMethodReagentConfigCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeMethodReagentConfig;
import com.sinoyd.lims.lim.service.AnalyzeMethodReagentConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 试剂配制记录接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019/05/14
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/analyzeMethodReagentConfig")
@Validated
public class AnalyzeMethodReagentConfigController
        extends BaseJpaController<DtoAnalyzeMethodReagentConfig, String, AnalyzeMethodReagentConfigService> {

    /**
     * 根据id获取试剂配置记录
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取试剂配置记录", notes = "根据id获取试剂配置记录")
    @GetMapping("/{id}")
    public RestResponse<DtoAnalyzeMethodReagentConfig> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoAnalyzeMethodReagentConfig> restResp = new RestResponse<>();

        DtoAnalyzeMethodReagentConfig entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 根据analyzeMethodId获取试剂配置记录(备用,接口未定义,待沟通)
     * 
     * @param analyzeMethodId 试剂配置记录analyzeMethodId
     * @return 试剂配置记录集合
     */
    @GetMapping("/analyzeMethodId/{analyzeMethodId}")
    public RestResponse<List<DtoAnalyzeMethodReagentConfig>> getByAnalyzeMethodId(
            @PathVariable String analyzeMethodId) {

        RestResponse<List<DtoAnalyzeMethodReagentConfig>> restResp = new RestResponse<>();

        List<DtoAnalyzeMethodReagentConfig> entities = null;
        // entities = service.getByAnalyzeMethodId(analyzeMethodId);

        restResp.setRestStatus(StringUtil.isEmpty(entities) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(entities);
        restResp.setCount(entities.size());

        return restResp;
    }

    /**
     * 分页动态条件查询试剂配置记录
     * 
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询试剂配置记录", notes = "分页动态条件查询试剂配置记录")
    @GetMapping
    public RestResponse<List<DtoAnalyzeMethodReagentConfig>> findByPage(AnalyzeMethodReagentConfigCriteria criteria) {

        RestResponse<List<DtoAnalyzeMethodReagentConfig>> restResp = new RestResponse<>();

        PageBean<DtoAnalyzeMethodReagentConfig> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增试剂配置记录
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增试剂配置记录", notes = "新增试剂配置记录")
    @PostMapping
    public RestResponse<DtoAnalyzeMethodReagentConfig> save(@Validated @RequestBody DtoAnalyzeMethodReagentConfig entity) {

        RestResponse<DtoAnalyzeMethodReagentConfig> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeMethodReagentConfig data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 更新试剂配置记录
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "更新试剂配置记录", notes = "更新试剂配置记录")
    @PutMapping
    public RestResponse<DtoAnalyzeMethodReagentConfig> update(@Validated @RequestBody DtoAnalyzeMethodReagentConfig entity) {

        RestResponse<DtoAnalyzeMethodReagentConfig> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeMethodReagentConfig data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id删除试剂配置记录
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id删除试剂配置记录", notes = "根据id删除试剂配置记录")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除试剂配置记录
     * 
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除试剂配置记录", notes = "批量删除试剂配置记录")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}