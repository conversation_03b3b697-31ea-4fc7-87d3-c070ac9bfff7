-- 社会生活噪声报告模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('5549a3a3-4441-4deb-a2a0-e57956f89db0', 1, 'SocialNoiseStd', '标准版社会生活噪声报告.doc', 'Report/标准版报告.doc',
        'output/Report/社会生活噪声报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '社会生活噪声报告', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-07-04 15:20:51',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-07-04 15:20:51',
        'reportId,sortId', 'Report', 'SocialNoiseStd', b'0', '', NULL, '', '', '', 0, NULL);

-- 废气比对报告模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                               returnType, method, params, pageConfig, orderNum, bizType,
                                               remark, isDeleted, orgId, creator, createDate, domainId,
                                               modifier, modifyDate, dataMethod, typeCode, strUrl,
                                               isDefineFileName, defineFileName, beanName, versionNum,
                                               controlNum, reportName, validate, usageNum)
VALUES ('01d3c202-a3f5-43dd-8813-07531d5d0544', 1, 'GasCompareStd', '标准版废气比对报告.doc', 'Report/标准版报告.doc',
        'output/Report/废气比对报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '废气比对报告', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-07-09 20:57:49',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2024-07-09 20:57:49',
        'reportId,sortId', 'Report', 'GasCompareStd', b'0', '', NULL, '', '', '', 0, NULL);
