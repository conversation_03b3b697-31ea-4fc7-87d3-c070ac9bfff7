package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoTask2FixedProperty;

import java.util.List;

/**
 * 自动任务下达与断面属性关联表数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/15
 * @since V100R001
 */
public interface Task2FixedPropertyRepository extends IBaseJpaPhysicalDeleteRepository<DtoTask2FixedProperty, String> {

    /**
     * 根据任务id查询数据
     *
     * @param taskIds 任务id
     * @return List<DtoTask2FixedProperty>
     */
    List<DtoTask2FixedProperty> findByTaskIdIn(List<String> taskIds);

    /**
     * 根据任务id删除数据
     *
     * @param taskIds 任务id
     */
    void deleteByTaskIdIn(List<String> taskIds);

}
