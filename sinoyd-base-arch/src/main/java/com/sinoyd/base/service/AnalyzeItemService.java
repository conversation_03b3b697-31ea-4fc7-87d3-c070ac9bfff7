package com.sinoyd.base.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;

import java.util.Collection;
import java.util.List;

/**
 * 分析项目管理
 * <AUTHOR>
 * @version v1.0.0 2019/1/14
 * @since V100R001
 */
public interface AnalyzeItemService extends IBaseJpaService<DtoAnalyzeItem, String> {

    /**
     * 通过分析项目名称获取分析项目
     *
     * @param analyzeItemName 分析项目名称
     * @return 分析项目
     */
    DtoAnalyzeItem getByAnalyzeItemName(String analyzeItemName);

    /***
     * 根据id数组获取分析项目
     * @param ids 分析项目id数据
     * @return 返回分析项目数据
     */
    List<DtoAnalyzeItem> findByIds(Collection<String> ids);


    /***
     *  根据id从redis下获取相应的分析项目数据
     * @param ids 分析项目id数据
     * @return 返回分析项目数据
     */
    List<DtoAnalyzeItem> findRedisByIds(List<String> ids);


    /**
     * 根据用户Id获取有假删的数据
     *
     * @param ids 主键ids
     * @return 返回带有假删的数据
     */
    List<DtoAnalyzeItem> findAllDeleted(List<String> ids);


    /**
     * 含假删的信息
     *
     * @return 返回假删信息
     */
    List<DtoAnalyzeItem> findAllDeleted();

    /**
     * 生成拼音全拼
     * @return
     */
    List<DtoAnalyzeItem> changePinYinFull();

}