package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 国家标准方法导入实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Data
public class DtoImportStandardMethod extends PoiBaseEntity {



    @Excel(name = "项目名称", orderNum = "20", width = 17)
    private String itemName;

    @Excel(name = "监测类别", orderNum = "30", width = 17)
    private String sampleType;

    @Excel(name = "标准编号", orderNum = "40", width = 17)
    private String countryStandard;

    @Excel(name = "标准名称", orderNum = "50", width = 17)
    private String methodName;

    @Excel(name = "方法id", orderNum = "60", width = 17)
    private String methodId;

}
