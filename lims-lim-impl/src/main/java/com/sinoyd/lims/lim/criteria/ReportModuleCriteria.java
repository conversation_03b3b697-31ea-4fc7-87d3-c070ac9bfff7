package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * ReportModule查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportModuleCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 组件名称编码
    */
    private String moduleCodeName;

    /**
     * 主表名称，数据行表名
     */
    private String tableName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.moduleCodeName)) {
            condition.append(" and (moduleCode like :moduleCodeName or moduleName like :moduleCodeName) ");
            values.put("moduleCodeName",  "%" + this.moduleCodeName + "%");
        }
        if (StringUtils.isNotNullAndEmpty(this.tableName)) {
            condition.append(" and (tableName like :tableName or sourceTableName like :tableName) ");
            values.put("tableName",  "%" + this.tableName + "%");
        }
        return condition.toString();
    }
}