package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.CalendarDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 日历日期实体传输类
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/1/19
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_CalendarDate")
@Data
public class DtoCalendarDate extends CalendarDate {

    /**
     * 返回农历日期
     */
    @Transient
    private String nlDate;

}
