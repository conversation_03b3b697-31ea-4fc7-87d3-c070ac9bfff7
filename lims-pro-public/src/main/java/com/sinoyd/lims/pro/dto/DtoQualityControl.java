package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.QualityControl;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoQualityControl实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_QualityControl")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoQualityControl extends QualityControl {
    private static final long serialVersionUID = 1L;

    /**
     * 加标回收率
     */
    @Transient
    private String qcRecoverRate;

    /**
     * 增值
     */
    @Transient
    private String qcAddedValue;

    @Transient
    private String testId;

    /**
     * 替代物cas号
     */
    @Transient
    private String casCode;

    /**
     * 替代物化合物名称
     */
    @Transient
    private String compoundName;

    /**
     * 替代物加入量
     */
    @Transient
    private String addition;

    /**
     * 替代样出证结果
     */
    @Transient
    private String testValue;

    /**
     * 有效位数
     */
    @Transient
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    @Transient
    private Integer mostDecimal;

    /**
     * 数据id
     */
    @Transient
    private String analyseDataId;

    /**
     * 是否合格
     */
    @Transient
    private Boolean isPass;

    /**
     * 允许范围
     */
    @Transient
    private String range;
}
