package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;


/**
 * SampleDispose查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleDisposeCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 保存日期查询开始时间
     */
    private String reserveStartDate;

    /**
     * 保存日期查询结束时间
     */
    private String reserveEndDate;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 保存位置
     */
    private String location;

    /**
     * 处置状态
     */
    private int disposeStatus;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        Calendar calendar = new GregorianCalendar();
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.isDeleted = 0 ");
        condition.append(" and b.id = a.sampleId");
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and a.id = :id ");
            values.put("id", this.id);
        }
        //样品编号查询
        if (StringUtil.isNotEmpty(this.sampleCode)) {
            condition.append(" and b.code like :sampleCode ");
            values.put("sampleCode", "%" + this.sampleCode + "%");
        }
        //保存时间开始时间查询
        if (StringUtil.isNotEmpty(this.reserveStartDate)) {
            Date date = DateUtil.stringToDate(this.reserveStartDate, DateUtil.YEAR);
            condition.append(" and a.reserveDate >= :startTime");
            values.put("startTime", date);
        }
        //保存时间结束时间查询
        if (StringUtil.isNotEmpty(this.reserveEndDate)) {
            Date date = DateUtil.stringToDate(this.reserveEndDate, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.reserveDate < :endTime");
            values.put("endTime", date);
        }
        if (this.disposeStatus != 0) {
            if (this.disposeStatus == 1) {
                condition.append(" and a.isDisposed = false");
            } else {
                condition.append(" and a.isDisposed = true");
            }
        }
        //保存位置查询
        if (StringUtil.isNotEmpty(this.location)) {
            condition.append(" and a.reserveLocation like :location");
            values.put("location", "%" + this.location + "%");
        }
        return condition.toString();
    }
}