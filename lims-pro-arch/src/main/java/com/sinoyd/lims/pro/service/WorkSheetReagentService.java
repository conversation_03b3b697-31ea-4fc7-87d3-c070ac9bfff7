package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeMethodReagentConfig;
import com.sinoyd.lims.pro.dto.DtoWorkSheetReagent;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * WorkSheetReagent操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface WorkSheetReagentService extends IBaseJpaService<DtoWorkSheetReagent, String> {
    /**
     * 选择试剂配置
     *
     * @param reagentConfigId   试剂配置id
     * @param workSheetFolderId 检测单id
     * @return 试剂配置记录
     */
    DtoWorkSheetReagent relate(String reagentConfigId, String workSheetFolderId);

    /**
     * 批量选择试剂配置
     *
     * @param reagentConfigIds   试剂配制记录ids
     * @param workSheetFolderId  检测单id
     * @return List<DtoWorkSheetReagent>
     */
    List<DtoWorkSheetReagent> batchRelate(List<String> reagentConfigIds, String workSheetFolderId);

    /**
     * 分页查询试剂配置记录
     */
    PageBean<DtoAnalyzeMethodReagentConfig> findConfigByPage(PageBean<DtoWorkSheetReagent> pb, BaseCriteria analyzeMethodReagentConfigCriteria);


    /**
     * 人员需要是分析数据录入切换的人员传入进来
     *
     * @param ids    主键ids
     * @param selectUserId 人员
     * @return 返回删除行数
     */
    Integer logicDeleteById(List<String> ids, String selectUserId);
}