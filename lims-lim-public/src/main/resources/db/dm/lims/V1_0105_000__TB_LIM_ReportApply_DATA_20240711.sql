-- 社会生活噪声报告组件及报表配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('0938cece-bc0b-4376-b886-3157a5cb844e', '5549a3a3-4441-4deb-a2a0-e57956f89db0', 'ReportEditNew', '报告编制V2.0',
        'SocialNoiseStd', '社会生活噪声报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:26:37', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-04 15:26:37', NULL);

-- 废气比对报告模板及组件配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('d0381ba9-21e0-4d10-bae3-ee12c7650eb9', '01d3c202-a3f5-43dd-8813-07531d5d0544', 'ReportEditNew', '报告编制V2.0',
        'GasCompareStd', '废气比对报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 20:58:41', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-07-09 20:58:41', NULL);