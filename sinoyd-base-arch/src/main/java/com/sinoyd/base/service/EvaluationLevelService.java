package com.sinoyd.base.service;

import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * EvaluationLevel操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
public interface EvaluationLevelService extends IBaseJpaService<DtoEvaluationLevel, String> {


    /**
     * 根据评价标准id获取相应的评价等级信息
     *
     * @param evaluationId 评价等级id
     * @return 返回相应的评价等级信息
     */
    List<TreeNode> findTreeNodeByEvaluationId(String evaluationId);


    /**
     * 根据评价标准id获取相应的评价等级信息
     * @param evaluationId 评价等级id
     * @return 返回相应的评价等级信息
     */
    List<DtoEvaluationLevel> findLevelByEvaluationId(String evaluationId);
}