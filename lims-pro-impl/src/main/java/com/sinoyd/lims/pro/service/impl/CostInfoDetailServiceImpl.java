package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.lims.pro.dto.DtoCostInfoDetail;
import com.sinoyd.lims.pro.repository.CostInfoDetailRepository;
import com.sinoyd.lims.pro.service.CostInfoDetailService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.stereotype.Service;


/**
 * 费用明细操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class CostInfoDetailServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCostInfoDetail,String,CostInfoDetailRepository> implements CostInfoDetailService {

    @Override
    public void findByPage(PageBean<DtoCostInfoDetail> pb, BaseCriteria costInfoDetailCriteria) {
        pb.setEntityName("DtoCostInfoDetail a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, costInfoDetailCriteria);
    }
}