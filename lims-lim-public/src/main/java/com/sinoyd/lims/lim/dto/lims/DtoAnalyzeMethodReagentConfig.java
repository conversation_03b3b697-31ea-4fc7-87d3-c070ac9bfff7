package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.AnalyzeMethodReagentConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoAnalyzeMethodReagentConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_AnalyzeMethodReagentConfig")
@Data
@DynamicInsert
public class DtoAnalyzeMethodReagentConfig extends AnalyzeMethodReagentConfig {

    /*
     * 方法名称
     */
    @Transient
    private String redAnalyzeMethodName;

    /*
     * 国家标准
     */
    @Transient
    private String redCountryStandard;

    /*
     * 检测单号
     */
    @Transient
    private String workSheetCodes;

    /**
     * 过期状态
     */
    @Transient
    private String status;

}