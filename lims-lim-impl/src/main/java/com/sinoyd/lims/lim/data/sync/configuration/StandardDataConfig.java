package com.sinoyd.lims.lim.data.sync.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 公共库配置类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/20
 */
@Component
@ConfigurationProperties(prefix="standardData")
@Data
public class StandardDataConfig {

    /**
     * 公共库host
     */
    private String host;

    /**
     * 共同库登录id
     */
    private String loginId;

    /**
     * 共同库登录密码
     */
    private String password;
}