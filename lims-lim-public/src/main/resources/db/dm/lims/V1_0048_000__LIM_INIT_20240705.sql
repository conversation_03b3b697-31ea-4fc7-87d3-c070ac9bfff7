INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('02198c5c-b8c9-4e78-9950-4088f2fa388e',1,'WorkSheetZLFQ','SINOYD-LIMS-FX-16-01 重量法分析原始记录（气）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-16-01 重量法分析原始记录（气）_模板.xlsx','output/WorkSheet/重量法分析原始记录（气）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFQWorkSheetDataSourceImpl" }',3700,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-09 22:48:22','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 13:55:06','workSheetFolderId,type','WorkSheet','WorkSheetZLFQ',0,'','',null,null,'',1,43);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('0468a4f6-f11a-4e2b-836f-7bfd161a1b21',1,'QCProjectExport','NTEM-26-TF001-2023 质控任务单.xlsx','LIMReportForms/NTEM-26-TF001-2023 质控任务单.xlsx','output/LIMReportForms/NTEM-26-TF001-2023 质控任务单.xlsx','application/excel','com.sinoyd.lims.report.service.limReportForms.QCProjectExportService','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-05 17:08:23','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-05 17:19:36','','LIMReportForms','QCProjectExport',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('04fcd27b-4c96-42e6-8cf9-4ff611468a55',1,'WorkSheetZDXSYD','SINOYD-LIMS-FX-08-01 总氮、硝酸盐氮分析原始记录单_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-08-01 总氮、硝酸盐氮分析原始记录单_模板.xlsx','output/WorkSheet/总氮、硝酸盐氮分析原始记录单_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZDXSYDWorkSheetDataSourceImpl" }',4530,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-08 22:04:45','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-23 16:08:58','workSheetFolderId,type','WorkSheet','WorkSheetZDXSYD',0,'','',null,null,'',1,25);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('050a49f8-bcbc-49f6-87e1-e6ce114aea8d',1,'WorkSheetDZFDTDW','SINOYD-LIMS-FX-74-01有机单组分（带替代物）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-74-01有机单组分（带替代物）_模板.xlsx','output/WorkSheet/SINOYD-LIMS-FX-74-01有机单组分（带替代物）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly", "workSheetDataSourceType" : "YJDZFWorkSheetDataSourceImpl"}',0,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-18 15:43:48','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-20 08:41:34','workSheetFolderId,type','WorkSheet','WorkSheetDZFDTDW',0,'',null,'','','',1,1);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('074ad891-b1ab-4ccd-8317-3ff114607e90',1,'OrderContract','1.xls','','','','com.sinoyd.lims.report.service.exports.OrderContractPoiExportServiceImpl','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-01 09:51:22','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-07 17:12:42','com.sinoyd.lims.pro.criteria.OrderContractCriteria','LIMReportForms','OrderContract',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('0a5048ae-b005-43ca-bd83-c97e0374013d',1,'WorkSheetSPSZLJDZFTR','SINOYD-LIMS-FX-29-01 色谱、色质联机分析原始记录（多组分土壤）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-29-01 色谱、色质联机分析原始记录（多组分土壤）_模板.xlsx','output/WorkSheet/色谱、色质联机分析原始记录（多组分土壤）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySingleSampleTestSPSZDZF", "workSheetDataSourceType" : "SPSZLJDZFWorkSheetDataSourceImpl"}',2830,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 11:11:03','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:11:35','workSheetFolderId,type','WorkSheet','WorkSheetSPSZLJDZFTR',0,'',null,'','','',1,36);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('0a80a85f-c9b1-40b7-966c-58c8721150fc',1,'QcProjectBlindExam','质控任务统计（盲样考核）.xlsx','LIMReportForms/质控任务统计（盲样考核）.xlsx','output/LIMReportForms/质控任务统计（盲样考核）.xlsx','application/excel','com.sinoyd.lims.report.service.exports.QcProjectBlindExamExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-03 09:44:56','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:34:26','com.sinoyd.lims.pro.criteria.QCProjectCriteria','LIMReportForms','QcProjectBlindExam',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('0b6dd6f9-6faa-4246-a2d6-8c5890fb62c3',1,'QualityControlRecord','质量控制记录单.xlsx','LIMReportForms/质量控制记录单.xlsx','output/LIMReportForms/质量控制记录单.xlsx','application/excel','com.sinoyd.lims.report.service.limReportForms.QualityControlRecordService','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-11 18:46:46','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-11 18:55:38','com.sinoyd.lims.pro.criteria.AnalysisQualityStatisticsCriteria','LIMReportForms','QualityControlRecord',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('0C4E3A66-60E2-4AC5-89F1-8ACC8B12BF2C',1,'SampleLabel','样品标签(含二维码).xlsx','Sample/样品标签(标准版).xls','output/Sample/样品标签.xlsx','application/excel','com.sinoyd.lims.sampling.service.samplingReport.SampleLabelServiceImpl','isBlank,isGroup,groupIds,isSample,sampleIds,receiveId',null,0,5,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-14 15:36:30',null,'Sample','-',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('0de3b7c0-3989-4e93-ac27-98a862c94e49',1,'SamplingContainer','采样容器准备清单.xlsx','Sampling/采样容器准备清单.xlsx','output/LIMReportForms/采样容器准备清单.xlsx','application/excel','com.sinoyd.lims.report.service.exports.SamplingContainerExportService','{"sort":"sampleId-"}','',0,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-11-27 15:43:05','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:51:01','com.sinoyd.lims.pro.criteria.SampleGroupRecordCriteria','Sampling','export/SamplingContainer',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('0df57004-fbec-44c6-966e-da81d184bc42',1,'AtmosphereSamplingRecord','SINOYD-LIMS-CY-01-01 大气环境采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-01-01 大气环境采样记录单.xlsx','output/Sampling/SINOYD-LIMS-CY-01-01 大气环境采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.AtmosphereSamplingRecordService','{"sort":"orderNum-"}',null,9999,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-27 16:08:30','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-12 14:13:44','sampleIds','Sampling','AtmosphereSamplingRecord',1,'[projectCode]','samplingFileNameStrategy',null,null,'',1,41);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('104401a3-c446-4bf8-9e2f-127558479b9b',1,'Person','人员一览表.xlsx','LIMReportForms/人员一览表.xls','output/LIMReportForms/人员一览表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.PersonExportService','{"sort":"orderNum-"}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:34:15','com.sinoyd.lims.lim.criteria.PersonCriteria','LIMReportForms','export/Person',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('11220910-2650-48bf-9c28-bde79efe4266',1,'QcProjectInstrumentCompare','质控任务统计（仪器比对）.xlsx','LIMReportForms/质控任务统计（仪器比对）.xlsx','output/LIMReportForms/质控任务统计（仪器比对）.xlsx','application/excel','com.sinoyd.lims.report.service.exports.QcProjectInstrumentCompareExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-03 09:52:28','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:34:32','com.sinoyd.lims.pro.criteria.QCProjectCriteria','LIMReportForms','QcProjectInstrumentCompare',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('115b7033-8fd6-4d62-89ef-d3a899ee99bc',1,'FlueGasSamplingRecord','SINOYD-LIMS-CY-18-01 烟气分析仪测量记录表.xlsx','Sampling/SINOYD-LIMS-CY-18-01 烟气分析仪测量记录表.xlsx','output/Sampling/烟气分析仪测量记录表.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.FlueGasSamplingRecordService','{"sort":"orderNum-"}',null,7777,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-05 14:54:16','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-27 20:58:30','sampleIds','Sampling','FlueGasSamplingRecord',0,'','',null,null,'',1,57);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('11e986ca-8eca-45e9-b173-692069145836',1,'SampleReceive','SINOYD-LIMS-CY-24-01 样品交接单.xlsx','Sampling/SINOYD-LIMS-CY-24-01 样品交接单.xlsx','output/LIMReportForms/样品交接单.xlsx','application/excel','com.sinoyd.lims.report.service.proReport.DeliveryReceiptReportService','{"isSignature": true}',null,6660,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-29 10:22:45','com.sinoyd.lims.lim.criteria.InstrumentUseRecordCriteria','LIMReportForms','SampleReceive',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1325a1d0-9df3-49a7-b1a2-fdda3981f344',1,'WTWasteWater','委托监测（废水）监测报告_模板.doc','Report/委托监测（废水）监测报告_模板.doc','output/Report/委托监测（废水）监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.WTWasteWaterReportService','{"sort":"orderNum-"}',null,1950,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-09 16:34:48','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','WTWasteWater',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('13ca8480-6871-436e-88c1-002b8cdc0390',1,'SurfaceWaterAugustSecondSamplingRecord','SINOYD-LIMS-CY-22-01 pH、电导率、溶解氧、水温、浊度、透明度测试原始记录.xlsx','Sampling/SINOYD-LIMS-CY-22-01 pH、电导率、溶解氧、水温、浊度、透明度测试原始记录.xlsx','output/Sampling/pH、电导率、溶解氧、水温、浊度、透明度测试原始记录.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.SurfaceWaterAugustSecondSamplingRecordService','{"sort":"orderNum-"}','',6666,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-06-10 11:42:00','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-07-03 16:52:41','sampleIds','Sampling','SurfaceWaterAugustSecondSamplingRecord',0,'','',null,null,'',1,4);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('140f68e4-fcfa-46cd-9faa-4d119866a42b',1,'ContainerList','容器清单.xls','','output/LIMReportForms/容器清单.xls','application/excel','','{"sort":"orderNum-"}','',0,1,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-08 22:39:46','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:58:03','','LIMReportForms','ContainerList',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('15fab25f-60a3-4f83-b698-abb3e3ab419a',1,'AtmosphericPrecipitationWaterSamplingRecord','SINOYD-LIMS-CY-02-01 大气降水采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-02-01 大气降水采样记录单.xlsx','output/Sampling/大气降水采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.AtmosphericPrecipitationWaterSamplingRecordService','{"sort":"orderNum-"}',null,9988,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-14 14:16:18','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-23 15:56:11','sampleIds','Sampling','AtmosphericPrecipitationWaterSamplingRecord',0,'','',null,null,'',1,8);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1748ee6d-0c27-429a-a9a6-30f8d78dd8fb',1,'ComprehensiveStd','标准综合报告.doc','Report/标准版报告.doc','output/Report/综合报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',0,4,'综合报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-05 16:42:16','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-05 16:42:52','reportId,sortId','Report','ComprehensiveStd',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('17B39793-F9B8-4EC1-AB71-D116D302359C',1,'DetailDataProject','项目分析数据.xlsx','LIMReportForms/项目分析数据.xls','output/Statistic/项目分析数据.xlsx','application/excel','com.sinoyd.lims.report.service.exports.DetailDataExportService','{"sort":"orderNum-"}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:47:58','com.sinoyd.lims.pro.criteria.DetailDataProjectCriteria','Statistic','export/DetailDataProject',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('17cafc42-dd44-4f57-b189-4c4eff5b2acd',1,'Smoke','烟气监测报告_模板.doc','Report/烟气监测报告_模板.doc','output/Report/烟气监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.SmokeReportService','{"sort":"orderNum-"}',null,1870,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-21 13:44:44','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','Smoke',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1b665218-f21b-4935-9c60-e0811c1a4e91',1,'AreaNoise','区域噪声监测报告_模板.doc','Report/区域噪声监测报告_模板.doc','output/Report/区域噪声监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.AreaNoiseReportService','{"sort":"orderNum-"}',null,1770,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-24 09:50:13','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:47','reportId,sortId','Report','AreaNoise',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1b87da15-8ae4-4e64-9f29-62a5d313fdcb',1,'ZFZDWasteWater','执法监测（重点源废水）监测报告_模板.doc','Report/执法监测（重点源废水）监测报告_模板.doc','output/Report/执法监测（重点源废水）监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.ZFZDWasteWaterReportService','{"sort":"orderNum-"}',null,1980,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-12 21:31:56','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','ZFZDWasteWater',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1b88786d-e7bc-494f-9680-0e4cf24ec358',1,'SurfaceWaterAugustSamplingRecord','地表水采样和交接记录（8月）.xlsx','SamplingRecords/地表水采样和交接记录（8月）.xls','output/SamplingRecords/地表水采样和交接记录（8月）.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.SurfaceWaterAugustSamplingRecordService','{"sort":"orderNum-"}','',890,3,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-06-10 11:24:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-01 13:53:33','sampleIds','Sampling','SurfaceWaterAugustSamplingRecord',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1c9ce2dc-6851-45e8-8223-7353e1574344',1,'EnvironmentStatistics','环境质量数据统计.xlsx','LIMReportForms/环境质量数据统计.xls','output/Statistic/环境质量数据统计.xlsx','application/excel','com.sinoyd.lims.report.service.exports.EnvironmentStatisticsExportService','{"sort":"orderNum-"}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-08-18 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 18:04:03','com.sinoyd.lims.pro.criteria.EnvironmentStatisticsExportCriteria','Statistic','export/EnvironmentStatistics',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1cb6a072-03f2-4c93-ab0a-5c19fc278fce',1,'WorkSheetPHDDL','SINOYD-LIMS-FX-19-01 pH、电导率测试原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-19-01 pH、电导率测试原始记录_模板.xlsx','output/WorkSheet/pH、电导率测试原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "PHDDLWorkSheetDataSourceImpl" }',3500,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-10 14:02:37','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-09 13:09:00','workSheetFolderId,type','WorkSheet','WorkSheetPHDDL',0,'','',null,null,'',1,17);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1db9c01d-48e0-4158-8ff4-5e2d415e4be7',1,'RongJieYangFXYSJLD','SINOYD-LIMS-FX-73-01溶解氧分析原始记录单.xlsx','WorkSheet/SINOYD-LIMS-FX-73-01溶解氧分析原始记录单.xlsx','output/WorkSheet/SINOYD-LIMS-FX-73-01溶解氧分析原始记录单.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',0,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-07 10:51:57','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-07 12:53:43','workSheetFolderId,type','WorkSheet','RongJieYangFXYSJLD',0,'',null,'','','',1,4);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1e42ed56-8181-4284-89eb-f60e25606a3b',1,'QualityControl','质控分析表.xlsx','LIMReportForms/质控分析表.xls','output/LIMReportForms/质控分析表.xlsx','application/excel','com.sinoyd.lims.report.service.statisticReport.QualityAnalyzeReportService','{"sort":"orderNum-"}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-05-07 16:18:07','com.sinoyd.lims.pro.criteria.AnalysisQualityStatisticsCriteria','LIMReportForms','export/QualityControl',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('1fd82f64-5e64-43ce-ac20-d29d1c609c77',1,'SmokeSampelRecord','SINOYD-LIMS-CY-21-03 自动烟尘（气、油烟）采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-21-03 自动烟尘（气、油烟）采样记录单.xlsx','output/Sampling/自动烟尘（气、油烟）采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.SmokeSamplingRecordService','{"sort":"orderNum-"}',null,6677,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-29 11:15:57','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-28 17:47:17','sampleIds','Sampling','SmokeSampelRecord',0,'','',null,null,'',1,33);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('214781d6-d44e-4fb1-a5d5-94612dfe8371',1,'SampleLabel2','样品标签(含固定剂、承载方式).xls','Sample/样品标签(含固定剂、承载方式).xls','output/Sample/样品标签.xlsx','application/excel','com.sinoyd.lims.sampling.service.samplingReport.SampleLabelServiceImpl','isBlank,isGroup,groupIds,isSample,sampleIds,receiveId','',0,5,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-14 15:23:47','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-01 14:37:12','','Sample','-',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('22b0aca6-df06-45a0-b456-41864979f2c5',1,'WorkSheetRLFGMS','SINOYD-LIMS-FX-10-01 容量法分析原始记录（高锰酸盐指数）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-10-01 容量法分析原始记录（高锰酸盐指数）_模板.xlsx','output/WorkSheet/容量法分析原始记录（高锰酸盐指数）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "RLFWorkSheetDataSourceImpl" }',4300,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-08 23:17:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-14 13:09:26','workSheetFolderId,type','WorkSheet','WorkSheetRLFGMS',0,'','',null,null,'',1,38);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('23928ece-1569-4447-a92b-66a4b00958ce',1,'SoilSamplingRecord','SINOYD-LIMS-CY-16-02 土壤采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-16-02 土壤采样记录单.xlsx','output/Sampling/土壤采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.SoilSamplingRecordService','{"sort":"orderNum-"}',null,8800,3,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-18 15:55:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:32:26','sampleIds','Sampling','SoilSamplingRecord',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('29489e4c-34da-464e-8398-ed50d911cd11',1,'ConsumablePutIn','消耗品入库清单_模板.xlsx','/LIMReportForms/消耗品入库清单_模板.xlsx','output/LIMReportForms/消耗品入库清单.xlsx','application/excel','com.sinoyd.lims.report.service.exports.ConsumablePutInExportService','{"sort":"consumableName+specification"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-15 18:50:35','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-16 09:38:53','com.sinoyd.base.criteria.ConsumableCriteria','LIMReportForms','export/ConsumablePutIn',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('29837dae-c309-4614-9d9b-ed923d89a420',1,'WorkSheetSDXSBSF','SINOYD-LIMS-FX-22-01 色度（稀释倍数法） 分析原始记录单_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-22-01 色度（稀释倍数法） 分析原始记录单_模板.xlsx','output/WorkSheet/色度（稀释倍数法） 分析原始记录单_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "SDXSBSFWorkSheetDataSourceImpl" }',3300,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-10 15:24:41','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-02 17:10:17','workSheetFolderId,type','WorkSheet','WorkSheetSDXSBSF',0,'','',null,null,'',1,30);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('2a3106df-09b2-4503-b5cc-6858a892c625',1,'WorkSheetZYJT','SINOYD-LIMS-FX-59-01 总有机碳(TOC)分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-59-01 总有机碳(TOC)分析原始记录_模板.xlsx','output/WorkSheet/总有机碳(TOC)分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZYJTWorkSheetDataSourceImpl" }',950,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-20 13:46:42','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-23 16:11:09','workSheetFolderId,type','WorkSheet','WorkSheetZYJT',0,'','',null,null,'',1,18);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('2c1b684f-15fe-49d5-ba8e-71f93abc43b9',1,'InstrumentUseRecord','仪器设备使用记录表.xlsx','LIMReportForms/仪器设备使用记录表.xls','output/LIMReportForms/仪器设备使用记录表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.InstrumentUseRecordPageReportService','{"sort":"startTime-"}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-28 10:12:57','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-10-23 14:25:07','com.sinoyd.lims.lim.criteria.InstrumentUseRecordCriteria','LIMReportForms','InstrumentUseRecord',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('2cd060b9-bdc5-4264-91b9-89f4a4d04485',1,'WorkSheetLYZXSST','SINOYD-LIMS-FX-48-01 冷原子吸收法分析原始记录（水、土）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-48-01 冷原子吸收法分析原始记录（水、土）_模板.xlsx','output/WorkSheet/冷原子吸收法分析原始记录（水、土）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZYGFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1700,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-18 11:18:17','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:54:30','workSheetFolderId,type','WorkSheet','WorkSheetLYZXSST',0,'','',null,null,'',1,26);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('2d971659-fd73-4998-b4a1-d3ca495fa4b5',1,'WorkSheetICPMSKLW','SINOYD-LIMS-FX-50-01 ICP-MS分析原始记录（颗粒物）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-50-01 ICP-MS分析原始记录（颗粒物）_模板.xlsx','output/WorkSheet/ICP-MS分析原始记录（颗粒物）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "ICPMSKLWWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1500,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-19 15:34:08','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-14 16:08:29','workSheetFolderId,type','WorkSheet','WorkSheetICPMSKLW',0,'','',null,null,'',1,24);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('308B0522-DDB1-428B-9B6E-A30CEDF60FF8',1,'ConsumableLog','消耗品领用记录.xlsx','LIMReportForms/消耗品领用记录.xlsx','output/LIMReportForms/消耗品领用记录.xlsx','application/excel','com.sinoyd.lims.report.service.exports.ConsumableLogExportService','{"sort":"consumableName+specification"}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-06-21 09:22:06','com.sinoyd.base.criteria.ConsumableCriteria','LIMReportForms','export/ConsumableLog',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('30e099ad-a9dd-4166-82b0-09cfb93c5cd2',1,'DBSSamplingRecord','SINOYD-LIMS-CY-04-02 地表水采样和分析记录单.xls','Sampling/SINOYD-LIMS-CY-04-02 地表水采样和分析记录单.xls','output/SamplingRecords/SINOYD-LIMS-CY-04-02 地表水采样和分析记录单.xls','application/excel','com.sinoyd.lims.sampling.service.samplingReport.STRSamplingRecordService','','',0,3,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-11-06 09:04:38','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:21:23','','Sampling','DBSSamplingRecord',0,'','','','','',1,3);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('30f535c5-28d8-4fd2-9fb3-f5fc5320c102',1,'WorkSheetLZXZDJS','SINOYD-LIMS-FX-69-01 离子选择电极法原始记录单（水）.xlsx','WorkSheet/SINOYD-LIMS-FX-69-01 离子选择电极法原始记录单（水）.xlsx','output/WorkSheet/离子选择电极法原始记录单（水）.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',890,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-08 11:26:05','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 15:46:19','workSheetFolderId,type','WorkSheet','WorkSheetLZXZDJS',0,'',null,'','','',1,27);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('328424ca-fd81-420d-9134-a578e57d0352',1,'SampleJudgeDataRecord','废水比对监测结果统计表.xlsx','LIMReportForms/废水比对监测结果统计表.xlsx','output/LIMReportForms/废水比对监测结果统计表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.SampleJudgeDataExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-21 08:51:00','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-24 13:47:28','com.sinoyd.lims.pro.criteria.SampleJudgeDataCriteria','LIMReportForms','export/SampleJudgeDataRecord',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('334fee80-2765-4c9a-a6a7-e06067742a92',1,'WasteWaterStd','标准废水报告.doc','Report/标准版报告.doc','output/Report/废水报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',1000,4,'废水报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-05-30 13:46:11','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-05 11:10:40','reportId,sortId','Report','WasteWaterStd',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('335a4134-96c6-4746-8ff8-afc97ddd92b1',1,'QuotationSheet','检测报价单.xls','LIMReportForms/检测报价单.xls','output/LIMReportForms/检测报价单.xlsx','application/excel','com.sinoyd.lims.report.service.limReportForms.QuotationSheetService','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-19 14:40:36','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-01 14:37:28','','LIMReportForms','QuotationSheet',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('33c70d5a-feb6-4faa-8468-f56cf870f94e',1,'CoalSamplingRecord','SINOYD-LIMS-CY-11-01 煤样采集记录单.xlsx','Sampling/SINOYD-LIMS-CY-11-01 煤样采集记录单.xlsx','output/Sampling/煤样采集记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.CoalSamplingRecordService','{"sort":"orderNum-"}',null,8877,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-17 15:03:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-13 10:09:57','sampleIds','Sampling','CoalSamplingRecord',0,'','',null,null,'',1,5);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('34983ed3-2cb1-4fc9-a4b2-1fe19b7be730',1,'InstrumentVerificationCalibration','计量仪器检定校准情况记录.xlsx','LIMReportForms/计量仪器检定校准情况记录.xls','output/LIMReportForms/计量仪器检定校准情况记录.xlsx','application/excel','com.sinoyd.lims.report.service.instrument.InstrumentVerificationCalibrationService',null,null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-06-09 14:47:26','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-06-11 09:02:02',null,'LIMReportForms',null,0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('34afebc4-6f06-4976-88f2-68fdaaff009e',1,'WorkSheetJJGWJG','SINOYD-LIMS-FX-56-01 甲基汞、烷基汞分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-56-01 甲基汞、烷基汞分析原始记录_模板.xlsx','output/WorkSheet/甲基汞、烷基汞分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "JJGWJGWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',980,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-20 08:21:27','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-26 15:19:00','workSheetFolderId,type','WorkSheet','WorkSheetJJGWJG',0,'','',null,null,'',1,29);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('3562ec04-07d8-425f-8827-52dc8dc063ee',1,'WorkSheetYZXSFGGDFQ','SINOYD-LIMS-FX-44-01 原子吸收分光光度法分析原始记录（气）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-44-01 原子吸收分光光度法分析原始记录（气）_模板.xlsx','output/WorkSheet/原子吸收分光光度法分析原始记录（气）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "YZXSFGGDSWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1950,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 11:26:14','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-14 16:26:21','workSheetFolderId,type','WorkSheet','WorkSheetYZXSFGGDFQ',0,'',null,'','','',1,13);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('36242497-8d85-443c-b9c1-f8ddf68b61eb',1,'WorkSheetSPSZLJS','SINOYD-LIMS-FX-26-01 色谱、色质联机分析原始记录(水)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-26-01 色谱、色质联机分析原始记录(水)_模板.xlsx','output/WorkSheet/色谱、色质联机分析原始记录(水)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJSWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',3100,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-14 10:23:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-08 10:25:36','workSheetFolderId,type','WorkSheet','WorkSheetSPSZLJS',0,'','',null,null,'',1,21);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('366c1331-f7e2-452c-a633-ee28e9aff959',1,'FoodBill','夜餐费补助发放表.xlsx','LIMReportForms/夜餐费补助发放表.xlsx','output/LIMReportForms/夜餐费补助发放表.xlsx','application/excel','com.sinoyd.lims.oa.service.FoodBillReportService','','',0,1,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-05 19:25:45','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:58:51','','LIMReportForms','-',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('383cf141-7199-4c2d-a576-7e1992763a08',1,'WorkSheetLYZXSFQ','SINOYD-LIMS-FX-46-01 冷原子吸收法分析原始记录（气）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-46-01 冷原子吸收法分析原始记录（气）_模板.xlsx','output/WorkSheet/冷原子吸收法分析原始记录（气）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZXSFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1800,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-18 10:56:58','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:46:09','workSheetFolderId,type','WorkSheet','WorkSheetLYZXSFQ',0,'','',null,null,'',1,24);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('3847f64f-e4a5-4da5-a3e9-63566e17482f',1,'ConsumableYZD','易制毒材料出入库登记表.xlsx','LIMReportForms/易制毒材料出入库登记表.xlsx','output/LIMReportForms/易制毒材料出入库登记表.xlsx','application/excel','com.sinoyd.lims.report.service.limReportForms.jx.ConsumableYZDService','','',6,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-05 19:24:27','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-19 14:42:19','','LIMReportForms','ConsumableYZD',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('3b6975bc-79d2-46c3-8f5c-b0c8a6808d6a',1,'AttendanceTotal','考勤统计表.xlsx','LIMReportForms/考勤统计表.xlsx','output/LIMReportForms/考勤统计表.xlsx','application/excel','com.sinoyd.lims.oa.service.AttendanceTotalReportService','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-05 19:25:01','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-05 19:25:01','','LIMReportForms','-',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('3d58569c-5314-4d09-af71-c81b762d232d',1,'WorkSheetFGGDQ','SINOYD-LIMS-FX-02-01 分光光度法原始记录（气）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-02-01 分光光度法原始记录（气）_模板.xlsx','output/WorkSheet/分光光度法原始记录（气）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "FGGDQWorkSheetDataSourceImpl" }',5000,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-05 17:34:59','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-23 10:00:43','workSheetFolderId,type','WorkSheet','WorkSheetFGGDQ',0,'','',null,null,'',1,37);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('3daed502-3166-4cbc-9cf8-cbd23caf1d36',1,'SolidWasteSamplingRecord','SINOYD-LIMS-CY-06-02 工业固体废物采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-06-02 工业固体废物采样记录单.xlsx','output/Sampling/工业固体废物采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.SolidWasteSamplingRecordService','{"sort":"orderNum-"}',null,9933,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-18 15:34:11','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-05 08:14:19','sampleIds','Sampling','SolidWasteSamplingRecord',0,'','',null,null,'',1,34);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('3e842677-05fb-4af0-ad53-8fcc69fe017c',1,'Precipitation','降雨常规监测报告_模板.doc','Report/降雨常规监测报告_模板.doc','output/Report/降雨常规监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.PrecipitationReportService','',null,1920,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-08 15:37:11','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','Precipitation',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('40e81b2c-e0b7-4a13-8c00-6dcac5fdb006',1,'RegionalEnvironmentNoiseSamplingRecord','SINOYD-LIMS-CY-13-01 区域环境噪声监测原始记录.xlsx','Sampling/SINOYD-LIMS-CY-13-01 区域环境噪声监测原始记录.xlsx','output/Sampling/区域环境噪声监测原始记录.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.RegionalEnvironmentalNoiseSamplingRecordService','{"sort":"orderNum-"}',null,8855,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-22 14:17:58','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-07-04 17:09:24','sampleIds','Sampling','RegionalEnvironmentNoiseSamplingRecord',0,'','',null,null,'',1,14);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('43bc4a72-5fbe-4ede-bc85-24caab90cc29',1,'PollutionWasteWaterSamplingRecord','SINOYD-LIMS-CY-17-01 污染源废水采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-17-01 污染源废水采样记录单.xlsx','output/Sampling/SINOYD-LIMS-CY-17-01 污染源废水采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.PollutionWasteWaterSamplingRecordService','{"sort":"orderNum-"}',null,7799,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-13 13:57:11','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-01 09:22:31','sampleIds','Sampling','PollutionWasteWaterSamplingRecord',0,'','',null,null,'',1,19);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('444e0e74-53c8-4679-90fc-8adb774efe77',1,'WorkSheetGTFWFSX','SINOYD-LIMS-FX-20-01 固体废物腐蚀性原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-20-01 固体废物腐蚀性原始记录_模板.xlsx','output/WorkSheet/固体废物腐蚀性原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "GTFWFSXWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "1","mainDataExcludeTypeList" : [{"sampleCategory" : "1", "qcGrade" : "2", "qcType" : "1"}], "relDataTypeList" : [{"sampleCategory" : "1", "qcGrade" : "2", "qcType" : "1"}]}}',3450,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-14 10:32:29','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-13 08:35:33','workSheetFolderId,type','WorkSheet','WorkSheetGTFWFSX',0,'','',null,null,'',1,38);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('45b37cb1-916e-46ab-a401-2e1cefb47c79',1,'DQHJCYSamplingRecord','SINOYD-LIMS-CY-01-02 大气环境采样记录单（多样品）.xlsx','Sampling/SINOYD-LIMS-CY-01-02 大气环境采样记录单（多样品）.xlsx','output/SamplingRecords/SINOYD-LIMS-CY-01-02 大气环境采样记录单（多样品）.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.DQHJCYSamplingRecordService','','',9999,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-11-13 17:07:57','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-31 11:02:30','','Sampling','DQHJCYSamplingRecord',0,'','','','','',1,12);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('46cd731f-e490-4cad-8a14-cf23c08e8543',1,'WorkSheetKXFYJLS2','SINOYD-LIMS-FX-57-01 可吸附有机卤素分析原始记录_模板2.xlsx','WorkSheet/SINOYD-LIMS-FX-57-01 可吸附有机卤素分析原始记录_模板2.xlsx','output/WorkSheet/SINOYD-LIMS-FX-57-01 可吸附有机卤素分析原始记录_模板2.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "YZXSFGGDSWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',971,2,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-15 14:17:22','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-18 18:45:28','workSheetFolderId,type','WorkSheet','WorkSheetKXFYJLS2',0,'','',null,null,'',1,11);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('476240ad-6f2b-42f2-a4ec-4b0a83a329b7',1,'Instrument','仪器一览表.xlsx','LIMReportForms/仪器一览表.xls','output/LIMReportForms/仪器一览表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.InstrumentExportService','{"sort":"instrumentsCode"}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-05-07 16:01:44','com.sinoyd.base.criteria.InstrumentCriteria','LIMReportForms','export/Instrument',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('486f9342-7c83-4afe-a98c-f7d36ecdefcf',1,'DetectionTask','任务单.xlsx','LIMReportForms/任务单.xlsx','output/LIMReportForms/任务单.xlsx','application/excel','com.sinoyd.lims.report.service.proReport.DetectionTaskReportService','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-03-01 17:04:35','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-28 09:27:40','projectId','LIMReportForms','DetectionTask',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('48c167b4-c125-4674-bcbc-e56de0355d29',1,'AttendanceDetail','考勤汇总表_模板.xlsx','LIMReportForms/考勤汇总表_模板.xls','output/LIMReportForms/考勤汇总表_模板.xlsx','application/excel','com.sinoyd.lims.oa.service.AttendanceReportService','',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-27 15:06:49','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-05 20:29:48','','LIMReportForms',' ',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('490a1d94-2ee0-45b2-8371-440cc8a1f082',1,'FlueGasBlackSamplingRecord','SINOYD-LIMS-CY-19-01 烟气黑度原始记录表.xlsx','Sampling/SINOYD-LIMS-CY-19-01 烟气黑度原始记录表.xlsx','output/Sampling/烟气黑度原始记录表.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.FlueGasBlackSamplingRecordService','{"sort":"orderNum-"}','',7770,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 11:07:58','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-03 13:21:56','sampleIds','Sampling','FlueGasBlackSamplingRecord',0,'',null,'','','',1,9);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('4b512e79-64f5-4033-a45a-22044a953983',1,'SampleReceiveXC','交接单（现场任务）.xls','LIMReportForms/交接单（现场任务）.xls','output/LIMReportForms/交接单.xlsx','application/excel','','{"sort":"orderNum-"}','',0,1,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-08 22:35:11','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-25 11:18:00','','LIMReportForms','SampleReceiveXC',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('4be62f6f-49cb-4a61-8a73-b0e50cb58632',1,'WorkSheetRLFS','SINOYD-LIMS-FX-12-01 容量法分析原始记录（水）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-12-01 容量法分析原始记录（水）_模板.xlsx','output/WorkSheet/容量法分析原始记录（水）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "RLFWorkSheetDataSourceImpl" }',4100,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-09 15:16:20','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-14 14:23:45','workSheetFolderId,type','WorkSheet','WorkSheetRLFS',0,'','',null,null,'',1,23);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('4da29a8a-a8e4-49d8-962e-e4b13ce71041',1,'WorkSheetLZXZDJF','离子选择电极法分析原始记录_模板.xls','WorkSheet/离子选择电极法分析原始记录_模板.xls','output/WorkSheet/离子选择电极法分析原始记录.xls','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "LZXZDJFWorkSheetDataSourceImpl"}',325,2,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-02-21 10:05:47','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 13:28:06','workSheetFolderId,type','WorkSheet','WorkSheetLZXZDJF',0,'','','','',null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('4e416b15-e6b9-4bda-b3d7-6a7e87182c38',1,'ZFUnOrgEnv','执法监测（无组织、环境空气）报告_模板.doc','Report/执法监测（无组织、环境空气）报告_模板.doc','output/Report/执法监测（无组织、环境空气）报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.ZFUnOrgEnvReportService','{"sort":"orderNum-"}',null,1890,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-21 13:46:44','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','ZFUnOrgEnv',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('4e422830-3817-4aa0-9f01-cf3f41566f22',1,'WorkSheetFGGDT','SINOYD-LIMS-FX-67-01 分光光度法原始记录（土）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-67-01 分光光度法原始记录（土）_模板.xlsx','output/WorkSheet/分光光度法原始记录（土）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',905,2,'',0,'5f7bcf90feb545968424b0a872863876','95a69fe5-1d28-4125-b435-ff2e09a75378','2024-01-16 09:28:10','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-23 10:09:07','workSheetFolderId,type','WorkSheet','WorkSheetFGGDT',0,'',null,'','','',1,16);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('4f29dab4-f73a-4d71-88d0-e6b693c0ae4c',1,'SensitiveEnvironmentNoiseSamplingRecord','SINOYD-LIMS-CY-12-02 敏感点（区域）环境噪声监测原始记录.xlsx','Sampling/SINOYD-LIMS-CY-12-02 敏感点（区域）环境噪声监测原始记录.xlsx','output/Sampling/敏感点（区域）环境噪声监测原始记录.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.SensitiveEnvironmentNoiseSamplingRecordService','{"sort":"orderNum-"}',null,8866,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-21 15:51:33','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-07-04 16:34:41','sampleIds','Sampling','SensitiveEnvironmentNoiseSamplingRecord',0,'','',null,null,'',1,39);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('50d92e04-c761-4d93-b99d-152a190dd9c4',1,'WorkSheetFGGDHYS','SINOYD-LIMS-FX-06-01 分光光度法原始记录（丁基黄原酸）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-06-01 分光光度法原始记录（丁基黄原酸）_模板.xlsx','output/WorkSheet/分光光度法原始记录（丁基黄原酸）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',4700,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-08 11:29:00','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-23 16:08:27','workSheetFolderId,type','WorkSheet','WorkSheetFGGDHYS',0,'','',null,null,'',1,19);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('519e62ea-a900-4635-b6fc-fe106fca73ac',1,'WorkSheetYLSA','SINOYD-LIMS-FX-09-01 叶绿素a测得原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-09-01 叶绿素a测得原始记录_模板.xlsx','output/WorkSheet/叶绿素a测得原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',4500,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-08 21:52:20','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-23 16:09:45','workSheetFolderId,type','WorkSheet','WorkSheetYLSA',0,'','',null,null,'',1,19);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('524f4eee-5748-4da8-b8d9-1075ec08f6cf',1,'QCConfigExport','质控限值配置导出.xlsx','','质控限值配置导出.xlsx','application/excel','com.sinoyd.lims.report.service.statisticReport.QCConfigExportService','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-04 13:24:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-04 13:24:50','com.sinoyd.lims.lim.criteria.TestCriteria','LIMReportForms','easyPoi/QCConfigExport',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('534d4437-e6f6-4434-b2b0-6786ffa275b4',1,'WorkSheetLZSPFQDLM','SINOYD-LIMS-FX-70-01 离子色谱法分析原始记录（气带滤膜）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-70-01 离子色谱法分析原始记录（气带滤膜）_模板.xlsx','output/WorkSheet/离子色谱法分析原始记录（气带滤膜）.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTestGroup", "workSheetDataSourceType" : "LZSPFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}',880,2,'',0,'5f7bcf90feb545968424b0a872863876','46905a56-f770-42be-8d41-5b6c94f0ed52','2024-01-29 08:25:25','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 15:48:18','workSheetFolderId,type','WorkSheet','WorkSheetLZSPFQDLM',0,'',null,'','','',1,40);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('55fc5d14-cc9a-4396-be34-8b2e4fceb009',1,'Evection','出差申请单_模板.xlsx','LIMReportForms/出差申请单_模板.xls','output/LIMReportForms/出差申请单.xlsx','application/excel','com.sinoyd.lims.oa.service.EvectionReportService','ids',null,0,1,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-28 08:57:18','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:59:09',null,'LIMReportForms',null,0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('56b3f7b9-bee9-4419-a986-c4cf5e2812b7',1,'OrgGasStd','标准版有组织气报告.doc','/Report/标准版报告.doc','output/Report/有组织气报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',900,4,'有组织废气',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-10-19 08:40:10','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-05 10:35:14','reportId,sortId','Report','OrgGasStd',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('5b54620b-0197-43c9-acf5-ffb431812c78',1,'WorkSheetFLZXZDJFWZZ','SINOYD-LIMS-FX-24-01 氟离子选择电极法分析原始记录（气-无组织）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-24-01 氟离子选择电极法分析原始记录（气-无组织）_模板.xlsx','output/WorkSheet/氟离子选择电极法分析原始记录（气-无组织）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "FLZXZDJFWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}',3180,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-12-30 14:07:07','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-23 16:10:33','workSheetFolderId,type','WorkSheet','WorkSheetFLZXZDJFWZZ',0,'','',null,null,'',1,33);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('5d5c9d4e-20bf-4478-8dd4-84eb4ee92620',1,'WorkSheetZLFJC','SINOYD-LIMS-FX-18-01 重量法分析原始记录（降尘）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-18-01 重量法分析原始记录（降尘）_模板.xlsx','output/WorkSheet/重量法分析原始记录（降尘）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFWorkSheetDataSourceImpl" }',3600,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-10 09:05:51','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 09:35:12','workSheetFolderId,type','WorkSheet','WorkSheetZLFJC',0,'','',null,null,'',1,23);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('5dba6b7f-fcdb-492d-84eb-7e93f3e87894',1,'GaseousPollutantsSamplingRecord','SINOYD-LIMS-CY-20-03 有组织气态污染物采样记录单(不等速采样).xlsx','Sampling/SINOYD-LIMS-CY-20-03 有组织气态污染物采样记录单(不等速采样).xlsx','output/Sampling/有组织气态污染物采样记录单(不等速采样).xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.GaseousPollutantsSamplingRecordService','{"sort":"orderNum-"}',null,7766,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-31 15:31:24','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-28 13:49:39','sampleIds','Sampling','GaseousPollutantsSamplingRecord',0,'','',null,null,'',1,38);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('5df5a558-4add-4a4c-a94b-a60e1d62b359',1,'Consumable','消耗品清单.xlsx','LIMReportForms/消耗品清单.xlsx','output/LIMReportForms/消耗品清单.xlsx','application/excel','com.sinoyd.lims.report.service.exports.ConsumableExportService','{"sort":"consumableName+specification"}',null,10,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-06 09:30:59','com.sinoyd.base.criteria.ConsumableCriteria','LIMReportForms','export/Consumable',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('5f3df1da-8425-43a5-99d6-3ff37a19ac14',1,'WorkSheetKXFYJLS','SINOYD-LIMS-FX-57-01 可吸附有机卤素分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-57-01 可吸附有机卤素分析原始记录_模板.xlsx','output/WorkSheet/SINOYD-LIMS-FX-57-01 可吸附有机卤素分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "YZXSFGGDSWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',970,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-20 10:31:01','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 15:40:07','workSheetFolderId,type','WorkSheet','WorkSheetKXFYJLS',0,'','',null,null,'',1,4);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('60918d00-7b37-493c-80a3-f905e168a1a1',1,'SampleCirculation','样品流转单.xls','LIMReportForms/样品流转单.xls','output/LIMReportForms/样品流转单.xls','application/excel','com.sinoyd.lims.report.service.limReportForms.SampleCirculationService','{ "isPageSig": true}','',0,1,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-21 13:17:26','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:59:03','','LIMReportForms','SampleCirculation',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('631983e4-ec42-41f5-b356-d63c6ea4a6ee',1,'QualityControlMerge','质控分析表.xlsx','LIMReportForms/质控分析表.xlsx','output/LIMReportForms/质控分析表(合并).xlsx','application/excel','com.sinoyd.lims.report.service.statisticReport.QualityAnalyzeReportByMergeService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-12 14:00:27','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-12 14:32:46','com.sinoyd.lims.pro.criteria.AnalysisQualityStatisticsCriteria','LIMReportForms','export/QualityControlMerge',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('63796f83-8e75-49d1-97b8-9ed27bd6bcc3',1,'CookingFumeSamplingRecord','SINOYD-LIMS-CY-26-02 饮食业油烟采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-26-02 饮食业油烟采样记录单.xlsx','output/SamplingRecords/SINOYD-LIMS-CY-26-02 饮食业油烟采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.CookingFumeSamplingRecordService','{"sort":"orderNum-"}','',6633,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-06 10:20:51','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-02 08:28:21','sampleIds','Sampling','CookingFumeSamplingRecord',0,'',null,'','','',1,24);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('6758fbb9-5e51-4657-a019-2b0581116db5',1,'LieuHoliday','补休审批表_模板.xlsx','LIMReportForms/补休审批表_模板.xls','output/LIMReportForms/补休审批表.xlsx','application/excel','com.sinoyd.lims.oa.service.LieuHolidayReportService','id',null,0,1,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-28 16:21:28','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:59:09','','LIMReportForms',' ',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('67963bc5-eac5-47ff-a926-857b7d1b7181',1,'ProjectInstrument','仪器设备出入库情况记录_模板.xlsx','LIMReportForms/仪器设备出入库情况记录_模板.xls','output/LIMReportForms/仪器设备出入库情况记录.xlsx','application/excel','com.sinoyd.lims.report.service.exports.ProjectInstrumentQueryReportService','',null,0,1,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-28 13:10:11','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-05-07 16:06:42','com.sinoyd.lims.lim.criteria.ProjectInstrumentCriteria','LIMReportForms','ProjectInstrument',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('67ea8582-1a15-4f8f-b166-e0eddd5479f6',1,'FlyAshStd','标准版飞灰报告.doc','Report/标准版报告.doc','output/Report/飞灰报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',700,4,'飞灰报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-05-30 13:53:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-07 15:38:08','reportId,sortId','Report','FlyAshStd',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('6a7692b8-fcd6-4741-bf9a-8ab20e57cbe5',1,'SmokeBlack','烟气黑度监测报告_模板.doc','Report/烟气黑度监测报告_模板.doc','output/Report/烟气黑度监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.SmokeBlackReportService','{"sort":"orderNum-"}',null,1880,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-21 13:42:31','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','SmokeBlack',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('6b985723-75cb-4e65-95fc-e9daee6275d1',1,'WorkSheetLZSPFQX','（351）离子色谱法分析原始记录（带曲线）_模板.xls','WorkSheet/（351）离子色谱法分析原始记录（带曲线）_模板.xls','output/WorkSheet/（351）离子色谱法分析原始记录（带曲线）.xls','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "LZSPFQXWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',111,2,'',1,'5f7bcf90feb545968424b0a872863876','a70e76eb-0638-48d5-9660-6090170bc163','2023-02-26 21:31:44','5f7bcf90feb545968424b0a872863876','a70e76eb-0638-48d5-9660-6090170bc163','2023-09-19 13:28:51','workSheetFolderId,type','WorkSheet','WorkSheetLZSPFQX',0,'','','','',null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('6d2b8c93-c4f3-42da-bfad-200338064849',1,'TRSamplingRecord','SINOYD-LIMS-CY-16-02 土壤采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-16-02 土壤采样记录单.xlsx','output/SamplingRecords/SINOYD-LIMS-CY-16-02 土壤采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.samplingReport.STRSamplingRecordService','','',8800,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-11-06 09:09:45','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-23 10:38:43','','Sampling','TRSamplingRecord',0,'','','','','',1,8);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('6eb0d739-0fcb-492e-8e6c-500f73aeaa5a',1,'WorkSheetLHWDLF','硫化物碘量法分析记录表_模板.xlsx','WorkSheet/硫化物碘量法分析记录表_模板.xlsx','output/WorkSheet/硫化物碘量法分析记录表.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',0,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-04-23 11:31:14','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-04-23 11:31:14','workSheetFolderId,type','WorkSheet','WorkSheetLHWDLF',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('711be950-f96f-463a-aa59-1f66355ba1f2',1,'StandardMaterial','标准物质领用记录.xlsx','LIMReportForms/标准物质领用记录.xlsx','output/LIMReportForms/标准物质领用记录.xlsx','application/excel','com.sinoyd.lims.report.service.limReportForms.StandardMaterialService','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-19 14:20:27','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-19 14:20:27','com.sinoyd.base.criteria.ConsumableCriteria','LIMReportForms','StandardMaterial',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('7146c6a6-6e6c-4494-b16c-e587dba931a4',1,'TestExport','测试项目导出_模板.xls','LIMReportForms/测试项目导出_模板.xls','output/LIMReportForms/测试项目导出.xlsx','application/excel','com.sinoyd.lims.report.service.exports.TestExportService','{"sort":"sampleTypeId+,orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-12-05 10:17:38','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-01 14:38:09','com.sinoyd.lims.lim.criteria.TestCriteria','LIMReportForms','export/TestExport',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('7157741c-7783-42e3-8dff-6e2f19d21e18',1,'WorkSheetZLFQWZZ','SINOYD-LIMS-FX-17-01 重量法分析原始记录（气-无组织）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-17-01 重量法分析原始记录（气-无组织）_模板.xlsx','output/WorkSheet/SINOYD-LIMS-FX-17-01 重量法分析原始记录（气-无组织）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFQWorkSheetDataSourceImpl" }',3650,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-12-30 14:03:27','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-20 09:39:12','workSheetFolderId,type','WorkSheet','WorkSheetZLFQWZZ',0,'','',null,null,'',1,16);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('7265cd9c-dbca-4ab3-94b2-3039cdddfb27',1,'ProjectInstrumentRecord','仪器设备出入库记录表_模板.xlsx','LIMReportForms/仪器设备出入库记录表_模板.xlsx','output/LIMReportForms/仪器设备出入库记录表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.ProjectInstrumentRecordService','','',888,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-03 10:04:14','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-23 16:43:42','com.sinoyd.lims.lim.criteria.ProjectInstrumentCriteria','LIMReportForms','ProjectInstrumentRecord',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('76707b2e-4981-4fce-896a-18ced00ad760',1,'NoiseStd','标准版噪声报告.doc','Report/标准版报告.doc','output/Report/噪声报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',800,4,'噪声报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-05-30 13:55:44','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-05 10:37:11','reportId,sortId','Report','NoiseStd',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('76d8d28c-f387-405f-8020-a2bbc48e86f3',1,'WorkSheetZLFS','SINOYD-LIMS-FX-14-01 重量法分析原始记录（水）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-14-01 重量法分析原始记录（水）_模板.xlsx','output/WorkSheet/重量法分析原始记录（水）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFWorkSheetDataSourceImpl" }',3900,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-09 16:06:23','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-03 15:07:39','workSheetFolderId,type','WorkSheet','WorkSheetZLFS',0,'','',null,null,'',1,24);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('76ee46dc-6f2c-4e4b-9e03-3a74a6746303',1,'WorkSheetLZSPF','SINOYD-LIMS-FX-53-01 离子色谱法分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-53-01 离子色谱法分析原始记录_模板.xlsx','output/WorkSheet/离子色谱法分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1100,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-19 16:43:20','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 15:31:57','workSheetFolderId,type','WorkSheet','WorkSheetLZSPF',0,'','',null,null,'',1,19);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('76fceb26-1a14-44fc-9f2f-54328d8639fc',1,'WorkSheetSPSZLJT','SINOYD-LIMS-FX-27-01 色谱、色质联机分析原始记录(土)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-27-01 色谱、色质联机分析原始记录(土)_模板.xlsx','output/WorkSheet/色谱、色质联机分析原始记录(土)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',2900,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-14 11:20:43','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:01:46','workSheetFolderId,type','WorkSheet','WorkSheetSPSZLJT',0,'','',null,null,'',1,24);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('79265c6b-2b79-492e-aa9d-ffa690d5e98d',1,'LocalFXJLRecord','SINOYD-LIMS-CY-28-01现场监测分析记录表.xlsx','Sampling/SINOYD-LIMS-CY-28-01现场监测分析记录表.xlsx','output/SamplingRecords/SINOYD-LIMS-CY-28-01现场监测分析记录表.xlsx','application/excel','com.sinoyd.lims.sampling.service.tz.samplingReport.TzCommonRecordService','','pageByTest',6611,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-08 10:57:59','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-21 16:20:39','','Sampling','LocalFXJLRecord',0,'',null,'','','',1,11);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('79914b30-6bdf-499e-b81f-1e85f6ce7195',1,'SurfaceWaterStd','标准版地表水报告.doc','Report/标准版报告.doc','output/Report/地表水报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',0,4,'地表水报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-20 10:07:10','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-20 10:07:10','reportId,sortId','Report','SurfaceWaterStd',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('7a8d0a5d-1ff7-4236-b28d-2d24afe568a3',1,'UnderGroundWaterSamplingRecord','SINOYD-LIMS-CY-05-04 地下水采样和分析记录单.xlsx','Sampling/SINOYD-LIMS-CY-05-04 地下水采样和分析记录单.xlsx','output/Sampling/SINOYD-LIMS-CY-05-04 地下水采样和分析记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.samplingReport.STRSamplingRecordService','{"sort":"orderNum-"}',null,9955,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-08 17:03:55','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-05 08:14:51','sampleIds','Sampling','UnderGroundWaterSamplingRecord',0,'','',null,null,'',1,25);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('7c9bcdc5-efa2-4e3e-832c-1ab19aa56487',1,'ZFWasteWater','执法监测（废水）监测报告_模板.doc','Report/执法监测（废水）监测报告_模板.doc','output/Report/执法监测（废水）监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.ZFWasteWaterReportService','{"sort":"orderNum-"}',null,1970,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-12 21:30:00','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','ZFWasteWater',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('7cb6e9e9-3438-40e3-b886-71e7479c963e',1,'WorkSheetFDCDGFJ','SINOYD-LIMS-FX-64-01 粪大肠菌群（多管发酵法）分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-64-01 粪大肠菌群（多管发酵法）分析原始记录_模板.xlsx','output/WorkSheet/粪大肠菌群（多管发酵法）分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "FDCDGFJWorkSheetDataSourceImpl" }',915,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 13:31:46','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-20 11:10:07','workSheetFolderId,type','WorkSheet','WorkSheetFDCDGFJ',0,'',null,'','','',1,12);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('7d121986-3317-44cd-bb0b-f9a81261694b',1,'PhFieldAnalyzeSamplingRecord','pH现场分析原始记录单.xlsx','Sampling/pH现场分析原始记录单.xlsx','output/Sampling/pH现场分析原始记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.PhFieldAnalyzeSamplingRecordService','{"sort":"orderNum-"}','',0,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-15 15:06:26','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-15 15:06:26','sampleIds','Sampling','PhFieldAnalyzeSamplingRecord',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('7d4df1a8-eea4-415f-be9a-8d2b325698fb',1,'InstrumentMaintainRecord','仪器维护记录.xlsx','LIMReportForms/仪器维护记录.xlsx','output/LIMReportForms/仪器维护记录.xlsx','application/excel','com.sinoyd.lims.report.service.exports.InstrumentMaintainRecordPageService','{"sort":"startTime-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-28 16:05:41','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-28 16:05:41','com.sinoyd.lims.lim.criteria.InstrumentMaintainRecordCriteria','LIMReportForms','InstrumentMaintainRecord',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('80298b64-3579-48fe-aba4-54f966924ed6',1,'EvaluationCriteria','评价标准信息_模板.xlsx','LIMReportForms/评价标准信息_模板.xlsx','output/LIMReportForms/评价标准信息.xlsx','application/excel','com.sinoyd.lims.report.service.exports.ExportEvaluationCriteriaService','{"sort":"name"}','',0,1,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-02-27 18:38:43','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-03 10:04:39','com.sinoyd.lims.lim.criteria.EnvironmentalCriteria','LIMReportForms','export/EvaluationCriteria',0,'','','','',null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('8035b129-e0ff-4f9c-9471-4d4ac065f83a',1,'DYDZTCSamplingRecord','SINOYD-LIMS-CY-25-02 打印单粘贴处.xlsx','Sampling/SINOYD-LIMS-CY-25-02 打印单粘贴处.xlsx','output/SamplingRecords/SINOYD-LIMS-CY-25-02 打印单粘贴处.xlsx','application/excel','com.sinoyd.lims.sampling.service.tz.samplingReport.TzCommonRecordService','','pageByProject',6655,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-01 11:09:33','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:23:50','','Sampling','DYDZTCSamplingRecord',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('8068f37d-accb-40cb-9e49-9c4892f97d48',1,'JcNoise','监测报告格式使用版（噪声）_模板.doc','Report/监测报告格式使用版（噪声）_模板.doc','output/Report/监测报告格式使用版（噪声）报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.JcNoiseReportService','{"sort":"orderNum-"}',null,1790,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-24 09:47:26','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','JcNoise',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('80850661-2665-4c44-9813-3475f58d19c6',1,'InstrumentLabel','仪器标签.xlsx','Sample/仪器标签.xlsx','output/LIMReportForms/仪器标签.xlsx','application/excel','com.sinoyd.lims.report.service.limReportForms.InstrumentLabelService','','',0,5,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-29 10:09:00','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:41:02','','Sample','InstrumentLabel',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('8259d792-6390-40e9-b31c-90e8b30ad138',1,'RailwayNoiseSamplingRecord','SINOYD-LIMS-CY-15-01 铁路边界噪声监测原始记录.xlsx','Sampling/SINOYD-LIMS-CY-15-01 铁路边界噪声监测原始记录.xlsx','output/Sampling/铁路边界噪声监测原始记录.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.RailwayNoiseSamplingRecordService','{"sort":"orderNum-"}',null,8811,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-23 11:27:01','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-05 09:39:19','sampleIds','Sampling','RailwayNoiseSamplingRecord',0,'','',null,null,'',1,22);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('83af429e-3ebe-4e37-b1d4-3ba6f140b81f',1,'WorkSheetLYZYGS','SINOYD-LIMS-FX-38-01 （冷）原子荧光法分析原始记录(水)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-38-01 （冷）原子荧光法分析原始记录(水)_模板.xlsx','output/WorkSheet/（冷）原子荧光法分析原始记录(水)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',2500,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-16 22:01:15','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:29:11','workSheetFolderId,type','WorkSheet','WorkSheetLYZYGS',0,'','',null,null,'',1,13);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('847c4f73-e898-45e7-8e83-2f4e001016da',1,'OtSummary','加班汇总审批单.xlsx','LIMReportForms/加班汇总审批单.xlsx','output/LIMReportForms/加班汇总审批单.xlsx','application/excel','com.sinoyd.lims.oa.service.WorkOverTimeTotalReportService','','',0,1,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-13 08:27:56','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:59:31','','LIMReportForms','OtSummary',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('856db631-d6e3-4cd7-9269-91905856a734',1,'SocialLifeNoiseSamplingRecord','社会生活环境噪声检测原始记录.xlsx','Sampling/社会生活环境噪声检测原始记录.xlsx','output/Sampling/社会生活环境噪声检测原始记录.xlsx','application/excel','com.sinoyd.lims.sampling.service.samplingReport.SocialLifeNoiseSamplingRecordService','{"sort":"orderNum-"}','',0,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-18 17:17:43','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-18 17:17:43','sampleIds','Sampling','SocialLifeNoiseSamplingRecord',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('8592c3d4-6656-4223-8282-00791fd324b0',1,'WorkSheetJYL','SINOYD-LIMS-FX-61-01 降雨量分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-61-01 降雨量分析原始记录_模板.xlsx','output/WorkSheet/降雨量分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',930,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-20 14:14:34','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-20 15:08:38','workSheetFolderId,type','WorkSheet','WorkSheetJYL',0,'','',null,null,'',1,11);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('87a9ace0-d4b9-4e97-86ac-c07239e8f9f6',1,'FuncNoise','功能区噪声监测报告.doc','Report/功能区噪声监测报告_模板.doc','output/Report/功能区噪声监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.FuncNoiseReportService','{"sort":"orderNum-"}',null,0,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-24 09:45:09','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:51','reportId,sortId','Report','FuncNoise',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('8ac8f239-048b-4e7e-b26a-b8a6df89179b',1,'WorkSheetYW','SINOYD-LIMS-FX-41-01 油雾分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-41-01 油雾分析原始记录_模板.xlsx','output/WorkSheet/油雾分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "YWWorkSheetDataSourceImpl" }',2200,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-17 10:14:00','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-28 13:07:17','workSheetFolderId,type','WorkSheet','WorkSheetYW',0,'','',null,null,'',1,13);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('8b1c3b43-6ecf-470e-b968-0d4c4b798ef1',1,'WaterReport','水监测报告_模板.doc','Report/水监测报告_模板.doc','output/Report/水监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.WaterReportService','{"sort":"orderNum-"}',null,1940,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-16 16:17:37','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','WaterReport',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('94218570-513e-4347-9af7-1534eaf36b78',1,'SyStd','标准版送样类报告.doc','Report/标准版报告.doc','output/Report/送样类报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',2000,4,'送样报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-05-30 13:53:45','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-19 13:18:28','reportId,sortId','Report','SyStd',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('944766ed-960b-4c13-a667-e58feb129eca',1,'WorkSheetRLFHXXYL','SINOYD-LIMS-FX-11-02 容量法分析原始记录（化学需氧量）_模板 .xlsx','WorkSheet/SINOYD-LIMS-FX-11-02 容量法分析原始记录（化学需氧量）_模板 .xlsx','output/WorkSheet/容量法分析原始记录（化学需氧量）_模板 .xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "RLFWorkSheetDataSourceImpl" }',4200,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-09 14:39:37','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-12 13:00:12','workSheetFolderId,type','WorkSheet','WorkSheetRLFHXXYL',0,'','',null,null,'',1,20);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('94a483db-4841-4c22-95e5-2b892eb3ca0b',1,'BaseQuery','自定义查询结果_模板.xlsx','LIMReportForms/自定义查询结果_模板.xls','output/LIMReportForms/自定义查询结果.xlsx','application/excel','com.sinoyd.lims.query.service.ItemExportService','com.sinoyd.lims.query.criteria.SelectBaseItemCriteria',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-08-10 17:02:00','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-08-13 08:49:00',null,'LIMReportForms',null,0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('96e515b0-6196-47af-a8eb-78bd018d44de',1,'SoilStd','标准版土壤报告.doc','Report/标准版报告.doc','output/Report/土壤报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',0,4,'土壤报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-13 14:49:03','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-13 14:49:03','reportId,sortId','Report','SoilStd',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('975F6767-EBD6-4AF7-B0C6-8A9D9730C822',1,'InstrumentUseRecord','仪器设备使用记录表.xlsxx','LIMReportForms/仪器设备使用记录表.xls','output/LIMReportForms/仪器设备使用记录表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.InstrumentUseRecordPageReportService','com.sinoyd.lims.lim.criteria.InstrumentUseRecordCriteria',null,0,1,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-10-23 14:24:49',null,'LIMReportForms','InstrumentUseRecord',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('99b18b13-e5bd-4b78-af42-2ca8948cbbd8',1,'WorkSheetpH','SINOYD-LIMS-FX-68-01 pH测试原始记录（土）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-68-01 pH测试原始记录（土）_模板.xlsx','output/WorkSheet/pH测试原始记录（土）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "PHDDLWorkSheetDataSourceImpl" }',900,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-22 16:57:52','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-09 13:12:56','workSheetFolderId,type','WorkSheet','WorkSheetpH',0,'',null,'','','',1,16);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('99bce985-d395-4f6d-8cb2-13c38fe3dd01',1,'UnOrgGasStd','标准版无组织报告.doc','Report/标准版报告.doc','output/Report/无组织报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',850,4,'无组织废气报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-05-30 14:02:29','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-07 15:37:02','reportId,sortId','Report','UnOrgGasStd',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('99f70329-6c41-4916-a47b-fdb58a6676a7',1,'SludgeStd','标准版污泥报告.doc','Report/标准版报告.doc','output/Report/污泥报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',750,4,'污泥报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-05-30 13:59:24','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-07 15:37:57','reportId,sortId','Report','SludgeStd',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('9ad6013c-2e67-44c8-8547-824d0c412131',1,'FactoryEnvironmentNoiseSamplingRecord','SINOYD-LIMS-CY-07-02 工业企业厂界环境噪声测量记录.xlsx','Sampling/SINOYD-LIMS-CY-07-02 工业企业厂界环境噪声测量记录.xlsx','output/Sampling/SINOYD-LIMS-CY-07-02 工业企业厂界环境噪声测量记录.xlsx','application/excel','com.sinoyd.lims.sampling.service.samplingReport.FactoryEntNoiseRecordService','{"sort":"orderNum-"}',null,9922,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-15 17:11:41','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-12 09:18:36','sampleIds','Sampling','FactoryEnvironmentNoiseSamplingRecord',0,'','',null,null,'',1,39);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('9b1fb895-1fcf-11ee-9975-424e45cd431a',1,'PersonCertByAnaItem','上岗证(分析项目).xlsx','LIMReportForms/上岗证(分析项目).xlsx','output/LIMReportForms/上岗证(分析项目).xlsx','application/excel','com.sinoyd.lims.report.service.exports.CertByAnaItemExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-11 16:00:06','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-11 16:01:20','com.sinoyd.lims.lim.criteria.PersonCriteria','LIMReportForms','export/PersonCertByAnaItem',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('9c2a604b-1be9-40d6-b747-662690e98952',1,'SurfaceWaterSamplingRecord','SINOYD-LIMS-CY-04-04 地表水采样和分析记录单.xlsx','Sampling/SINOYD-LIMS-CY-04-04 地表水采样和分析记录单.xlsx','output/Sampling/SINOYD-LIMS-CY-04-04 地表水采样和分析记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.samplingReport.STRSamplingRecordService','{"sort":"orderNum-"}',null,9966,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-06 10:57:53','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-13 14:57:22','sampleIds','Sampling','SurfaceWaterSamplingRecord',0,'','',null,null,'',1,45);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('9d16e7c2-341d-4fb5-a116-3be3564003f2',1,'ConsumableYZB','易制爆材料出入库登记表.xlsx','LIMReportForms/易制爆材料出入库登记表.xlsx','output/LIMReportForms/易制爆材料出入库登记表.xlsx','application/excel','com.sinoyd.lims.report.service.limReportForms.jx.ConsumableYZBService','','',4,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-05 19:23:49','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-19 14:42:25','','LIMReportForms','ConsumableYZB',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('9dc1061d-ab81-4abe-bec2-a6d02d09a39b',1,'NormalWaterTestHorizontal','常规水检测报告（横版）_模板.doc','Report/常规水检测报告（横版）_模板.doc','output/Report/常规水检测报告（横版）.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.NormalWaterTestReportHorizontalService','{"sort":"orderNum-"}','',1985,4,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-08-24 17:27:22','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','NormalWaterTestHorizontal',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('9e4e25f7-aae8-475d-a6ab-2b2325a6da70',1,'WorkSheetFDCJQ','SINOYD-LIMS-FX-63-01 粪大肠菌群（酶底物法）分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-63-01 粪大肠菌群（酶底物法）分析原始记录_模板.xlsx','output/WorkSheet/粪大肠菌群（酶底物法）分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "FDCJQWorkSheetDataSourceImpl" }',920,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-20 14:59:34','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-20 11:15:22','workSheetFolderId,type','WorkSheet','WorkSheetFDCJQ',0,'','',null,null,'',1,18);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('9edbedfd-e8fb-427b-9fbd-97c082282d07',1,'WorkSheetFLZXZDJF','SINOYD-LIMS-FX-23-01 氟离子选择电极法分析原始记录（气）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-23-01 氟离子选择电极法分析原始记录（气）_模板.xlsx','output/WorkSheet/氟离子选择电极法分析原始记录（气）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "FLZXZDJFWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}',3200,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-11 23:00:44','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-16 13:23:23','workSheetFolderId,type','WorkSheet','WorkSheetFLZXZDJF',0,'','',null,null,'',1,17);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('9ef2c783-c970-4667-9369-785df7725d4f',1,'WorkSheetFGGDSCS','SINOYD-LIMS-FX-01-01 分光光度法原始记录（水）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-01-01 分光光度法原始记录（水）_模板.xlsx','output/WorkSheet/分光光度法原始记录（水）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',5100,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-06 08:28:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-06 10:00:17','workSheetFolderId,type','WorkSheet','WorkSheetFGGDSCS',0,'','','','','',1,69);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('9f456064-ba3a-4a2a-8165-ef4bebd022e2',1,'StandardPutIn','标准物质入库清单_模板.xlsx','/LIMReportForms/标准物质入库清单_模板.xlsx','output/LIMReportForms/标准物质入库清单.xlsx','application/excel','com.sinoyd.lims.report.service.exports.StandardPutInExportService','{"sort":"consumableName+specification"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-16 09:21:07','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-16 09:39:05','com.sinoyd.base.criteria.ConsumableCriteria','LIMReportForms','export/StandardPutIn',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('a67f7243-6754-4a40-9e82-216241d1c9c8',1,'WorkSheetICPMSGTFW','SINOYD-LIMS-FX-49-01 ICP-MS分析原始记录（固体废物浸出液）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-49-01 ICP-MS分析原始记录（固体废物浸出液）_模板.xlsx','output/WorkSheet/ICP-MS分析原始记录（固体废物浸出液）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "ICPMSGTFWWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1600,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-18 13:12:29','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 15:21:40','workSheetFolderId,type','WorkSheet','WorkSheetICPMSGTFW',0,'','',null,null,'',1,16);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('a72e8361-b566-4fa6-a3ff-5fa84ee75ab2',1,'SwimingBathPoolRecord','SINOYD-LIMS-CY-27-01游泳池、公共浴池采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-27-01游泳池、公共浴池采样记录单.xlsx','output/SamplingRecords/SINOYD-LIMS-CY-27-01游泳池、公共浴池采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.samplingReport.SwimingBathPoolRecordService','','',6622,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-08 14:12:49','5f7bcf90feb545968424b0a872863876','46905a56-f770-42be-8d41-5b6c94f0ed52','2024-02-19 13:40:08','','Sampling','SwimingBathPoolRecord',0,'',null,'','','',1,20);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('a7bfa9ec-e38d-4c57-8285-43bd55659c5b',1,'OT','加班审批表_模板.xlsx','LIMReportForms/加班审批表_模板.xls','output/LIMReportForms/加班审批表.xlsx','application/excel','com.sinoyd.lims.oa.service.OtReportService','id',null,0,1,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-28 15:12:25','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:59:43','','LIMReportForms',' ',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('a80e9479-5173-4862-bac9-10bf6baf3276',1,'WorkSheetJWZT','SINOYD-LIMS-FX-35-01 总烃、甲烷、非甲烷总烃分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-35-01 总烃、甲烷、非甲烷总烃分析原始记录_模板.xlsx','output/WorkSheet/总烃、甲烷、非甲烷总烃分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "JWZTWorkSheetDataSourceImpl" }',2620,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-25 14:37:36','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-25 16:51:06','workSheetFolderId,type','WorkSheet','WorkSheetJWZT',0,'','',null,null,'',1,41);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('a818dd51-085a-4897-ad22-7f9066d185a0',1,'WorkSheetLYZYGQ','SINOYD-LIMS-FX-36-01 （冷）原子荧光法分析原始记录(气)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-36-01 （冷）原子荧光法分析原始记录(气)_模板.xlsx','output/WorkSheet/（冷）原子荧光法分析原始记录(气)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZYGFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',2600,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-16 21:37:36','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-20 09:38:13','workSheetFolderId,type','WorkSheet','WorkSheetLYZYGQ',0,'','',null,null,'',1,11);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('a87a7836-40cd-4608-95a5-67ea3c24ccdb',1,'DXSSamplingRecord','SINOYD-LIMS-CY-05-02 地下水采样和分析记录单.xls','Sampling/SINOYD-LIMS-CY-05-02 地下水采样和分析记录单.xls','output/SamplingRecords/SINOYD-LIMS-CY-05-02 地下水采样和分析记录单.xls','application/excel','com.sinoyd.lims.sampling.service.samplingReport.STRSamplingRecordService','','',0,3,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-11-06 09:07:45','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:20:47','','Sampling','DXSSamplingRecord',0,'','','','','',1,1);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('aa774fca-48a4-4240-be99-779dc1d59067',1,'DustSamplingRecord','SINOYD-LIMS-CY-09-01 降尘采样及分析记录单.xlsx','Sampling/SINOYD-LIMS-CY-09-01 降尘采样及分析记录单.xlsx','output/Sampling/降尘采样及分析记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.DustSamplingRecordService','{"sort":"orderNum-"}',null,8899,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-24 16:28:24','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-13 15:38:09','sampleIds','Sampling','DustSamplingRecord',0,'','',null,null,'',1,2);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('ab0ee6c8-597c-4dc1-b2f1-28819d0d29f3',1,'WorkSheetICPMSGTFW1','SINOYD-LIMS-FX-71-01 ICP-MS分析原始记录（土）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-71-01 ICP-MS分析原始记录（土）_模板.xlsx','output/WorkSheet/SINOYD-LIMS-FX-71-01 ICP-MS分析原始记录（土）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "ICPMSGTFWWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',870,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-26 21:00:47','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-16 08:38:41','workSheetFolderId,type','WorkSheet','WorkSheetICPMSGTFW1',0,'','',null,null,'',1,25);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('abee292e-6b62-40ae-ac21-4f78462a6d8c',1,'WorkSheetFGGDS','SINOYD-LIMS-FX-01-01 分光光度法原始记录（水）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-01-01 分光光度法原始记录（水）_模板.xlsx','output/WorkSheet/分光光度法原始记录（水）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',5100,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-05 15:21:36','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-30 13:57:12','workSheetFolderId,type','WorkSheet','WorkSheetFGGDS',0,'','',null,null,'',1,77);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('ac44d3ee-03dc-48b5-9f82-071a29ec8d81',1,'SedimentSamplingRecord','SINOYD-LIMS-CY-03-02 底质（底泥、沉积物）采样记录单.xlsx','Sampling/SINOYD-LIMS-CY-03-02 底质（底泥、沉积物）采样记录单.xlsx','output/Sampling/底质（底泥、沉积物）采样记录单.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.SedimentSamplingRecordService','{"sort":"orderNum-"}',null,9977,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-17 17:50:21','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-30 09:32:24','sampleIds','Sampling','SedimentSamplingRecord',0,'','',null,null,'',1,12);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('ad110547-6935-440d-b1f9-be0066e03fd8',1,'FunctionalAreasNoiseSamplingRecord','SINOYD-LIMS-CY-08-01 功能区环境噪声监测原始记录.xlsx','Sampling/SINOYD-LIMS-CY-08-01 功能区环境噪声监测原始记录.xlsx','output/Sampling/功能区环境噪声监测原始记录.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.FunctionalAreasNoiseSamplingRecordService','{"sort":"orderNum-"}',null,9911,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-16 16:05:12','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-13 09:14:38','sampleIds','Sampling','FunctionalAreasNoiseSamplingRecord',0,'','',null,null,'',1,44);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('b208f3a9-ce4f-4291-91db-604a873b72b1',1,'TrafficNoise','交通噪声监测报告_模板.doc','Report/交通噪声监测报告_模板.doc','output/Report/交通噪声监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.TrafficNoiseReportService','{"sort":"orderNum-"}',null,1780,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-24 09:48:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:47','reportId,sortId','Report','TrafficNoise',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('B233BB98-412E-46D5-9A3A-ADF7EB264F23',1,'DetailParamsDataProject','项目参数数据.xlsx','LIMReportForms/项目参数数据.xls','output/Statistic/项目参数数据.xlsx','application/excel','com.sinoyd.lims.report.service.exports.DetailDataExportService','{"sort":"orderNum-"}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:48:19','com.sinoyd.lims.pro.criteria.DetailDataProjectCriteria','Statistic','export/DetailParamsDataProject',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('b27e8645-6319-42e0-ab5b-07bb2135689e',1,'TrafficNoiseSamplingRecord','SINOYD-LIMS-CY-10-02 交通噪声监测原始记录.xlsx','Sampling/SINOYD-LIMS-CY-10-02 交通噪声监测原始记录.xlsx','output/Sampling/交通噪声监测原始记录.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.TrafficNoiseSamplingRecordService','{"sort":"orderNum-"}',null,8888,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-22 16:18:35','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-13 08:54:28','sampleIds','Sampling','TrafficNoiseSamplingRecord',0,'','',null,null,'',1,14);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('b4138920-8f84-4f77-96ba-a46d27e3d5e3',1,'WorkSheetHWFGGDFNew','SINOYD-LIMS-FX-40-02 红外(非分散)分光光度法分析原始记录（水）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-40-02 红外(非分散)分光光度法分析原始记录（水）_模板.xlsx','output/WorkSheet/红外(非分散)分光光度法分析原始记录（水）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest",  "workSheetDataSourceType" : "HWFGGDNewWorkSheetDataSourceImpl",  "qCGroupByTest" : "1", "qCAnaLmtCnt" : "1" }',2300,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-04 10:53:41','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-13 15:15:17','workSheetFolderId,type','WorkSheet','WorkSheetHWFGGDFNew',0,'',null,'','','',1,29);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('b4862f99-ab84-49f6-aad3-804b45cbd7f1',1,'SampleLabelGC','样品标签（国产化）.xls','Sample/样品标签（国产化）.xls','output/Sample/样品标签（国产化）.xlsx','application/excel','com.sinoyd.lims.sampling.service.samplingReport.SampleLabelServiceImpl','isBlank,isGroup,groupIds,isSample,sampleIds,receiveId','',0,5,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-06-29 13:47:07','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:37:55','','Sample','-',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('b4f459ee-f234-4fd5-82a3-fade13de06aa',1,'WorkSheetLDZSF','SINOYD-LIMS-FX-62-01 流动注射法分析记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-62-01 流动注射法分析记录_模板.xlsx','output/WorkSheet/流动注射法分析记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',925,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-20 14:25:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-20 14:38:55','workSheetFolderId,type','WorkSheet','WorkSheetLDZSF',0,'','',null,null,'',1,22);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('b5ac1c6d-c841-436d-b38f-00e4cb28323f',1,'WorkSheetFGGDZZS','SINOYD-LIMS-FX-07-01 分光光度法原始记录（浊度-色度补偿）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-07-01 分光光度法原始记录（浊度-色度补偿）_模板.xlsx','output/WorkSheet/分光光度法原始记录（浊度-色度补偿）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',4600,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-08 15:16:54','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-23 10:05:03','workSheetFolderId,type','WorkSheet','WorkSheetFGGDZZS',0,'','',null,null,'',1,27);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('b73a4a93-400c-4401-a046-0df2dc61cb70',1,'ConsumableCRK','消耗性材料出入库登记表.xlsx','LIMReportForms/消耗性材料出入库登记表.xlsx','output/LIMReportForms/消耗性材料出入库登记表.xlsx','application/excel','com.sinoyd.lims.report.service.limReportForms.jx.ConsumableCRKService','','',8,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-05 19:23:07','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-19 14:16:47','','LIMReportForms','ConsumableCRK',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('ba8e7106-c54e-413f-be35-6e69569277b5',1,'ReagentConfig','一般试剂配制记录.xlsx','LIMReportForms/一般试剂配制记录.xlsx','output/LIMReportForms/一般试剂配制记录.xlsx','application/excel','com.sinoyd.lims.report.service.exports.ReagentConfigExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-08-01 16:30:46','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:34:05','com.sinoyd.lims.lim.criteria.AnalyzeMethodExportCriteria','LIMReportForms','export/ReagentConfig',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('ba8f43c9-6034-490b-a8a2-433e74b8664e',1,'ProjectStatistics','SINOYD-LIMS-ZY-23-01项目统计报表.xlsx','LIMReportForms/SINOYD-LIMS-ZY-23-01项目统计报表.xlsx','output/LIMReportForms/SINOYD-LIMS-ZY-23-01项目统计报表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.ProjectStatisticsExportService','{"sort":""}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-10-31 09:37:43','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-10-31 17:13:15','com.sinoyd.lims.pro.criteria.ProjectCriteria','LIMReportForms','export/ProjectStatistics',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('baba271d-afde-48da-a285-2e22e9fbd808',1,'WasteWater','废水采样单.xlsx','Sample/废水采样单.xls','output/Sample/废水采样单.xlsx','application/excel','com.sinoyd.lims.report.service.samplingReport.FSSamplingRecordService','sampleIds',null,0,1,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:37:12',null,'SamplingRecords',null,0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('bee92f18-7ac8-41bf-8905-1a296b979f3d',1,'WorkSheetRLFGLCOD','SINOYD-LIMS-FX-13-01 容量法分析原始记录（高氯COD）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-13-01 容量法分析原始记录（高氯COD）_模板.xlsx','output/WorkSheet/容量法分析原始记录（高氯COD）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "RLFWorkSheetDataSourceImpl" }',4000,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-09 15:42:49','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-30 14:25:37','workSheetFolderId,type','WorkSheet','WorkSheetRLFGLCOD',0,'','',null,null,'',1,11);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('beeffdf7-6ca1-4b26-980e-91060191ef4b',1,'FallenDust','降尘监测报告_模板.doc','Report/降尘监测报告_模板.doc','output/Report/降尘监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.FallenDustReportService','{"sort":"orderNum-"}',null,1760,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-21 13:36:36','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:47','reportId,sortId','Report','FallenDust',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('bfe0fe80-6cb7-420d-9bb6-045e7bfd252f',1,'WorkSheetSPSZLJ','SINOYD-LIMS-FX-25-01 色谱、色质联机分析原始记录（曲线计算）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-25-01 色谱、色质联机分析原始记录（曲线计算）_模板.xlsx','output/WorkSheet/SINOYD-LIMS-FX-25-01 色谱、色质联机分析原始记录（曲线计算）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',3150,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-05 11:00:15','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-02 08:36:02','workSheetFolderId,type','WorkSheet','WorkSheetSPSZLJ',0,'','',null,null,'',1,26);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c031154d-ba9e-4043-8c4c-5852fb938890',1,'PersonCertByPerson','上岗证(人员证书).xlsx','LIMReportForms/上岗证(人员证书).xlsx','output/LIMReportForms/上岗证(人员证书).xlsx','application/excel','com.sinoyd.lims.report.service.exports.CertByPersonExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-11 16:00:06','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-11 16:01:20','com.sinoyd.lims.lim.criteria.PersonCriteria','LIMReportForms','export/PersonCertByPerson',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('C065A75C-68D0-4DB8-A358-4996F54884AB',1,'DetailData','数据查询.xlsx','LIMReportForms/数据查询.xls','output/Statistic/数据查询.xlsx','application/excel','com.sinoyd.lims.report.service.statisticReport.DetailDataReportService','{"sort":""}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:47:24','com.sinoyd.lims.pro.criteria.DetailDataCriteria','LIMReportForms','export/DetailDataProject',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c074b990-316a-4ec0-802c-002edee5bf61',1,'WorkSheetICPAESS','SINOYD-LIMS-FX-52-01 ICP-AES分析原始记录（水）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-52-01 ICP-AES分析原始记录（水）_模板.xlsx','output/WorkSheet/ICP-AES分析原始记录（水）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1200,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-19 16:18:25','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 15:29:52','workSheetFolderId,type','WorkSheet','WorkSheetICPAESS',0,'','',null,null,'',1,9);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c077c43e-6316-4753-9955-e74412159c2f',1,'SurfaceWater','地表水监测报告_模板.doc','/Report/地表水监测报告_模板.doc','output/Report/地表水监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.SurfaceWaterReportService','{"sort":"orderNum-"}',null,99999,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-07 13:48:58','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','SurfaceWater',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c09b715e-1248-49e1-9edf-2aee03bc8009',1,'EvaluationCriteriaExport','评价标准.xls','LIMReportForms/评价标准.xls','output/LIMReportForms/评价标准.xls','application/excel','com.sinoyd.lims.report.service.exports.EvaluationCriteriaExportService','{"sort":"name"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-11-23 11:19:51','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-11-23 13:03:27','com.sinoyd.base.criteria.EvaluationCriteriaCriteria','LIMReportForms','export/EvaluationCriteriaExport',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c14e2f38-e18b-4151-9654-621feeee9e04',1,'StandardConsumable','SINOYD-LIMS-ZY-07-02 标样清单.xlsx','LIMReportForms/SINOYD-LIMS-ZY-07-02 标样清单.xlsx','output/LIMReportForms/SINOYD-LIMS-ZY-07-02 标样清单.xlsx','application/excel','com.sinoyd.lims.report.service.baseReport.StandardConsumableReportService','{"sort":"consumableName"}',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2020-12-05 13:19:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-21 09:44:56','com.sinoyd.base.criteria.ConsumableCriteria','LIMReportForms','export/StandardConsumable',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c3129316-2a7b-4870-ac1f-2604c4dff612',1,'WorkSheetSDBGBSF','SINOYD-LIMS-FX-21-01 色度(铂钴比色法)原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-21-01 色度(铂钴比色法)原始记录_模板.xlsx','output/WorkSheet/ 色度(铂钴比色法)原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',3400,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-10 14:18:31','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-02 17:32:55','workSheetFolderId,type','WorkSheet','WorkSheetSDBGBSF',0,'','',null,null,'',1,12);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c3498b50-f2cf-4d7f-b001-0b862d2c3311',1,'WorkSheetQXFZXS','SINOYD-LIMS-FX-60-01 气相分子吸收光谱法原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-60-01 气相分子吸收光谱法原始记录_模板.xlsx','output/WorkSheet/气相分子吸收光谱法原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',940,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-20 13:59:14','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-23 16:11:43','workSheetFolderId,type','WorkSheet','WorkSheetQXFZXS',0,'','',null,null,'',1,19);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c67ca493-a08a-413e-95cb-21db2f1a6e19',1,'SampleJudgeDataRecord','NTEM-28-TF029-2023 废水比对监测结果统计表.xlsx','LIMReportForms/NTEM-28-TF029-2023 废水比对监测结果统计表.xlsx','output/LIMReportForms/NTEM-28-TF029-2023 废水比对监测结果统计表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.SampleJudgeDataExportService','{"sort":"orderNum-"}','',0,1,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-11 15:31:20','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-17 15:20:29','com.sinoyd.lims.pro.criteria.SampleJudgeDataCriteria','LIMReportForms','export/SampleJudgeDataRecord',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c6b960a3-b0db-4beb-ba55-75ee67e95b7c',1,'QcProjectMarkExam','质控任务统计（加标样考核）.xlsx','LIMReportForms/质控任务统计（加标样考核）.xlsx','output/LIMReportForms/质控任务统计（加标样考核）.xlsx','application/excel','com.sinoyd.lims.report.service.exports.QcProjectMarkExamExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-03 09:24:06','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:34:35','com.sinoyd.lims.pro.criteria.QCProjectCriteria','LIMReportForms','QcProjectMarkExam',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c760a3de-f4dc-4e35-9c4d-175f2e7884c4',1,'QcProjectPersonCompare','质控任务统计（人员比对）.xlsx','LIMReportForms/质控任务统计（人员比对）.xlsx','output/LIMReportForms/质控任务统计（人员比对）.xlsx','application/excel','com.sinoyd.lims.report.service.exports.QcProjectPersonCompareExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-03 09:48:32','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:34:39','com.sinoyd.lims.pro.criteria.QCProjectCriteria','LIMReportForms','QcProjectPersonCompare',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c7e4a25a-2683-4934-90b0-c52e7c9c60a1',1,'GasSampleAnalysisResults','气体样品分析结果计算表_模板.xlsx','Sampling/气体样品分析结果计算表_模板.xlsx','output/Sampling/气体样品分析结果计算表.xlsx','application/excel','com.sinoyd.lims.wordreport.service.jx.wordReport.GasSampleAnalysisResultsService','','',0,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-02-08 09:20:14','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-14 13:48:46','','Sampling','GasSampleAnalysisResults',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('c891cb86-3891-428a-aaab-d4fa88c244e2',1,' WorkSheetLZSPFQWZZ','SINOYD-LIMS-FX-55-01 离子色谱法分析原始记录（气-无组织）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-55-01 离子色谱法分析原始记录（气-无组织）_模板.xlsx','output/WorkSheet/离子色谱法分析原始记录（气-无组织）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTestGroup", "workSheetDataSourceType" : "LZSPFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}',990,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-12-30 14:05:05','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 15:36:01','workSheetFolderId,type','WorkSheet',' WorkSheetLZSPFQWZZ',0,'','',null,null,'',1,43);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('caee46e5-ef7b-45f0-bcfc-06d7a3ef764c',1,'WorkSheetSPSZLJQ','SINOYD-LIMS-FX-30-01 色谱、色质联机分析原始记录(气)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-30-01 色谱、色质联机分析原始记录(气)_模板.xlsx','output/WorkSheet/色谱、色质联机分析原始记录(气)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',2800,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-14 14:07:33','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-06-17 13:59:46','workSheetFolderId,type','WorkSheet','WorkSheetSPSZLJQ',0,'','',null,null,'',1,24);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('cb62e1f3-4ff5-4caa-b483-81f9c5d01d8b',1,'WorkSheetWRSHXYL','SINOYD-LIMS-FX-58-01 五日生化需氧量分析记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-58-01 五日生化需氧量分析记录_模板.xlsx','output/WorkSheet/五日生化需氧量分析记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "WRSHXYLNewWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "1","mainDataExcludeTypeList" : [{"sampleCategory" : "3", "qcGrade" : "2", "qcType" : "16"}], "relDataTypeList" : [{"sampleCategory" : "3", "qcGrade" : "2", "qcType" : "16"}]}}',960,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-20 11:12:59','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-20 17:08:13','workSheetFolderId,type','WorkSheet','WorkSheetWRSHXYL',0,'','',null,null,'',1,26);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('cc6a7036-1320-4ea0-97a5-2820633ad0a8',1,'WorkSheetHWFGGDF','SINOYD-LIMS-FX-40-01 红外(非分散)分光光度法分析原始记录（水）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-40-01 红外(非分散)分光光度法分析原始记录（水）_模板.xlsx','output/WorkSheet/红外(非分散)分光光度法分析原始记录（水）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySample",  "workSheetDataSourceType" : "HWFGGDWorkSheetDataSourceImpl",  "qCGroupByTest" : "1", "qCAnaLmtCnt" : "1" }',2300,2,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-17 09:21:39','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-28 13:14:40','workSheetFolderId,type','WorkSheet','WorkSheetHWFGGDF',0,'','',null,null,'',1,1);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('ccd25c0c-a8cd-479e-80f0-cde305fee372',1,'NormalWaterStd','标准常规水报告.doc','Report/标准版报告.doc','output/Report/常规水报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',950,4,'常规水报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-05-30 13:53:45','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-05 11:10:29','reportId,sortId','Report','NormalWaterStd',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('cdff15b0-a443-492f-842d-86a347f38e8f',1,'ceshiSamplingRecord','有组织气态污染物采样和交接记录表(不等速采样)_测试.xlsx','SamplingRecords/有组织气态污染物采样和交接记录表(不等速采样)_测试.xls','output/SamplingRecords/有组织气态污染物采样和交接记录表(不等速采样)_测试.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.GaseousPollutantsSamplingRecordServiceTwo','{"sort":"orderNum-"}',null,690,3,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-14 09:38:25','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-15 19:48:11','sampleIds','Sampling','ceshiSamplingRecord',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('ce909369-2370-4f7b-a8b7-f49e93f348ef',1,'WorkSheetFGGDSY','SINOYD-LIMS-FX-05-01 分光光度法原始记录（石油类）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-05-01 分光光度法原始记录（石油类）_模板.xlsx','output/WorkSheet/分光光度法原始记录（石油类）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',4800,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-08 10:57:58','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-28 10:08:04','workSheetFolderId,type','WorkSheet','WorkSheetFGGDSY',0,'','',null,null,'',1,25);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('cfd5467a-d55b-4323-af33-086c6630f375',1,'WorkSheetYZXSFGGDFS','SINOYD-LIMS-FX-43-01 原子吸收分光光度法分析原始记录（水）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-43-01 原子吸收分光光度法分析原始记录（水）_模板.xlsx','output/WorkSheet/原子吸收分光光度法分析原始记录（水）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "YZXSFGGDSWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',2000,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-18 10:24:42','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-28 09:49:23','workSheetFolderId,type','WorkSheet','WorkSheetYZXSFGGDFS',0,'','',null,null,'',1,8);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('cfe61a30-992f-4a31-915d-68051dc8dcfc',1,'WorkSheetFGGDQHW','SINOYD-LIMS-FX-04-01 分光光度法原始记录（氰化物）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-04-01 分光光度法原始记录（氰化物）_模板.xlsx','output/WorkSheet/分光光度法原始记录（氰化物）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',4900,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-07 13:14:05','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-23 16:07:56','workSheetFolderId,type','WorkSheet','WorkSheetFGGDQHW',0,'','',null,null,'',1,19);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d1db1218-0695-4d52-a85c-7495e624afdf',1,'SampleDispose','样品留样及处理登记表_模板.xlsx','LIMReportForms/样品留样及处理登记表_模板.xls','output/LIMReportForms/样品留样及处理登记表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.SampleDisposeReportService','',null,0,1,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-28 22:38:08','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-05-07 16:13:48','com.sinoyd.lims.pro.criteria.SampleDisposeCriteria','LIMReportForms','SampleDispose',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d27c1262-3adc-4085-adfa-ff97dd95cf29',1,'WorkSheetLZXZDJFT','SINOYD-LIMS-FX-72-01 离子选择电极法原始记录单（土）.xlsx','WorkSheet/SINOYD-LIMS-FX-72-01 离子选择电极法原始记录单（土）.xlsx','output/WorkSheet/离子选择电极法原始记录单（土）.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }',865,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-04 11:02:29','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 15:56:23','workSheetFolderId,type','WorkSheet','WorkSheetLZXZDJFT',0,'','',null,null,'',1,24);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d2e016dd-5676-46c5-b326-f867d9a2a573',1,'GroundWaterStd','标准版地下水报告.doc','Report/标准版报告.doc','output/Report/地下水报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',0,4,'地下水报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-20 09:42:35','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-20 09:42:35','reportId,sortId','Report','GroundWaterStd',0,'',null,'','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d34a7e2e-a72f-43a8-b63e-59a4aa08eabb',1,'ContractCollectionPlan','1.xls','','','','com.sinoyd.lims.report.service.exports.ContractCollectionPlanPoiExportServiceImpl','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-04 10:46:41','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-04 10:48:06','com.sinoyd.lims.pro.criteria.ContractCollectionPlanCriteria','LIMReportForms','/',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d4524da2-3414-4a4c-99a4-816965107af2',1,'WorkSheetZLFGHS','SINOYD-LIMS-FX-15-01 重量法分析原始记录（干物质、含水率、水分）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-15-01 重量法分析原始记录（干物质、含水率、水分）_模板.xlsx','output/WorkSheet/重量法分析原始记录（干物质、含水率、水分）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFGHSWorkSheetDataSourceImpl" }',3800,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-09 22:03:55','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-03 14:05:16','workSheetFolderId,type','WorkSheet','WorkSheetZLFGHS',0,'','',null,null,'',1,16);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d5842be5-3a2a-4af9-9be0-40befb046af7',1,'WorkSheetLYZYGQWZZ','SINOYD-LIMS-FX-37-01 （冷）原子荧光法分析原始记录(气-无组织)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-37-01 （冷）原子荧光法分析原始记录(气-无组织)_模板.xlsx','output/WorkSheet/（冷）原子荧光法分析原始记录(气-无组织)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZYGFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',2550,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-12-30 13:56:37','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:27:02','workSheetFolderId,type','WorkSheet','WorkSheetLYZYGQWZZ',0,'','',null,null,'',1,15);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d5b83be1-be36-41a2-a654-3e6114643d6a',1,'WorkSheetICPMSS','SINOYD-LIMS-FX-51-01 ICP-MS分析原始记录（水）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-51-01 ICP-MS分析原始记录（水）_模板.xlsx','output/WorkSheet/ICP-MS分析原始记录（水）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1300,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-19 15:53:29','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-10 15:32:18','workSheetFolderId,type','WorkSheet','WorkSheetICPMSS',0,'','',null,null,'',1,23);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d5fdc9a7-e980-419e-b0e1-cbbb9ceea6a9',1,'WorkSheetLZSPFQ','SINOYD-LIMS-FX-54-01 离子色谱法分析原始记录（气）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-54-01 离子色谱法分析原始记录（气）_模板.xlsx','output/WorkSheet/离子色谱法分析原始记录（气）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTestGroup", "workSheetDataSourceType" : "LZSPFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}',1000,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-19 17:29:47','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 15:33:51','workSheetFolderId,type','WorkSheet','WorkSheetLZSPFQ',0,'','',null,null,'',1,43);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d724cc9a-0f65-4652-aac5-5b837125f875',1,'SocialEnvironmentNoiseSamplingRecord','SINOYD-LIMS-CY-14-02 社会生活源边界环境噪声测量记录.xlsx','Sampling/SINOYD-LIMS-CY-14-02 社会生活源边界环境噪声测量记录.xlsx','output/Sampling/社会生活源边界环境噪声测量记录.xlsx','application/excel','com.sinoyd.lims.sampling.service.jx.samplingReport.SocialEnvironmentNoiseSamplingRecordService','{"sort":"orderNum-"}',null,8833,3,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-22 10:26:30','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-07-04 17:20:08','sampleIds','Sampling','SocialEnvironmentNoiseSamplingRecord',0,'','',null,null,'',1,23);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d7d94fa2-263b-4607-8daf-08885cd0e641',1,'WorkSheetLYZXSFQWZZ','SINOYD-LIMS-FX-47-01 冷原子吸收法分析原始记录（气-无组织）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-47-01 冷原子吸收法分析原始记录（气-无组织）_模板.xlsx','output/WorkSheet/冷原子吸收法分析原始记录（气-无组织）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZXSFQWZZWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1750,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-12-30 13:58:47','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:52:03','workSheetFolderId,type','WorkSheet','WorkSheetLYZXSFQWZZ',0,'','',null,null,'',1,23);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('d95a33e3-80cd-45ce-82d0-a4b26b275f1a',1,'WorkSheetLYZYGT','SINOYD-LIMS-FX-39-01 （冷）原子荧光法分析原始记录(土)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-39-01 （冷）原子荧光法分析原始记录(土)_模板.xlsx','output/WorkSheet/（冷）原子荧光法分析原始记录(土)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',2400,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-16 22:22:35','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-16 08:25:32','workSheetFolderId,type','WorkSheet','WorkSheetLYZYGT',0,'','',null,null,'',1,11);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('dc87dc82-c2e5-4163-bc20-fa4f1f033186',1,'WorkSheetSPSZLJQCLWZZ','SINOYD-LIMS-FX-33-01 色谱、色质联机分析原始记录（气-串联-无组织)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-33-01 色谱、色质联机分析原始记录（气-串联-无组织)_模板.xlsx','output/WorkSheet/色谱、色质联机分析原始记录（气-串联-无组织)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTestGroup", "workSheetDataSourceType" : "SPSZLJQCLWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}',2650,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-12-30 14:02:12','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:21:03','workSheetFolderId,type','WorkSheet','WorkSheetSPSZLJQCLWZZ',0,'','',null,null,'',1,24);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('dfe43963-0ec5-4e21-a611-6e16aedd8f36',1,'QYHJJCCDFBRecord','SINOYD-LIMS-ZY-37-01 企业环境检测测定点分布示意图.xlsx','Sampling/SINOYD-LIMS-ZY-37-01 企业环境检测测定点分布示意图.xlsx','output/SamplingRecords/SINOYD-LIMS-ZY-37-01 企业环境检测测定点分布示意图.xlsx','application/excel','com.sinoyd.lims.sampling.service.tz.samplingReport.TzCommonRecordService','','pageByProject',6600,3,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-08 13:14:25','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-22 09:02:30','','Sampling','QYHJJCCDFBRecord',0,'',null,'','','',1,7);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('e111ad52-4a79-4637-ad84-27f4761e2b7e',1,'StandardLabel','标样标签.xlsx','Sample/标样标签.xlsx','output/LIMReportForms/标样标签.xlsx','application/excel','com.sinoyd.lims.report.service.limReportForms.StandardLabelService','','',0,5,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-05 14:56:43','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:41:14','','Sample','StandardLabel',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('e54f395b-eeca-460b-b730-a912c86b7950',1,'NormalWater','常规水监测报告_模板.doc','Report/常规水监测报告_模板.doc','output/Report/常规水监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.NormalWaterReportService','{"sort":"orderNum-"}',null,99988,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-07 15:53:37','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','NormalWater',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('e5c1ef02-9170-408c-a17a-5aea859735ea',1,'WasteGas','废气监测报告_模板.doc','Report/废气监测报告_模板.doc','output/Report/废气监测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.WasteGasReportService','{"sort":"orderNum-"}',null,1900,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-21 13:36:07','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','WasteGas',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('e656a41e-734a-4171-b45f-5678e95b42c6',1,'Radiation','辐射报告_模板.doc','Report/辐射报告_模板.doc','output/Report/辐射报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.RadiationReportService','{"sort":"orderNum-"}',null,1930,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-24 09:51:38','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','Radiation',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('e71ae6aa-1182-4088-a9ec-66225d0a7948',1,'ConsumablesPurchase','消耗性材料采购申请表.xls','LIMReportForms/消耗性材料采购申请表.xls','output/LIMReportForms/消耗性材料采购申请表.xlsx','application/excel','com.sinoyd.lims.oa.service.ConsumablesPurchaseRequisitionReportService','id','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-07-10 16:56:09','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-01 14:39:24','','LIMReportForms','ConsumablesPurchase',0,'','',null,null,'',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('e71eb218-886f-4d46-83fe-fa2560affadb',1,'WorkSheetSPSZLJQWZZ','SINOYD-LIMS-FX-31-01 色谱、色质联机分析原始记录(气-无组织)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-31-01 色谱、色质联机分析原始记录(气-无组织)_模板.xlsx','output/WorkSheet/色谱、色质联机分析原始记录(气-无组织)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',2750,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-12-30 14:00:24','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:16:01','workSheetFolderId,type','WorkSheet','WorkSheetSPSZLJQWZZ',0,'','',null,null,'',1,25);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('e7f44afe-45e1-4d50-bd5b-f21b6f24576f',1,'RecAndPayRecord','1.xls','','','','com.sinoyd.lims.report.service.exports.RecAndPayRecordPoiExportServiceImpl','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-04 13:23:03','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-04 13:25:47','com.sinoyd.lims.pro.criteria.RecAndPayRecordCriteria','LIMReportForms','/',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('e89254bc-3044-4ba8-bb41-02838f689ff9',1,'OrderForm','订单明细_模板.xlsx','LIMReportForms/订单明细_模板.xlsx','output/LIMReportForms/订单明细.xlsx','application','com.sinoyd.lims.report.service.exports.OrderFormExportService','{"sort":"orderDate-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-02-22 13:30:55','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-02-22 13:31:20','com.sinoyd.lims.pro.criteria.OrderFormCriteria','LIMReportForms','export/OrderForm',0,'','','','',null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('e964d612-50c3-4854-bec7-46c7f729ca54',1,'WorkSheetYY','SINOYD-LIMS-FX-42-01 油烟分析原始记录_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-42-01 油烟分析原始记录_模板.xlsx','output/WorkSheet/油烟分析原始记录_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "YYWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "2", "mainDataExcludeTypeList" : [], "relDataTypeList" : []}}',2100,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-18 10:01:05','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-20 21:15:57','workSheetFolderId,type','WorkSheet','WorkSheetYY',0,'','',null,null,'',1,34);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('eab54930-afa0-4dc5-9b40-41fde23dfba9',1,'WorkSheetYZXSFGGDFT','SINOYD-LIMS-FX-45-01 原子吸收分光光度法分析原始记录（土）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-45-01 原子吸收分光光度法分析原始记录（土）_模板.xlsx','output/WorkSheet/原子吸收分光光度法分析原始记录（土）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "YZXSFGGDSWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',1900,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-18 10:39:27','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-03-28 08:49:27','workSheetFolderId,type','WorkSheet','WorkSheetYZXSFGGDFT',0,'','',null,null,'',1,33);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('eb924106-a28f-422b-ba10-466704b88545',1,'QcProjectStandardExam','质控任务统计（标样考核）.xlsx','LIMReportForms/质控任务统计（标样考核）.xlsx','output/LIMReportForms/质控任务统计（标样考核）.xlsx','application/excel','com.sinoyd.lims.report.service.exports.QcProjectStandardExamExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-03 08:42:16','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:34:42','com.sinoyd.lims.pro.criteria.QCProjectCriteria','LIMReportForms','QcProjectStandardExam',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('edac2fd2-200e-4b4c-9158-20aab569c91c',1,'WorkSheetSPSZLJDZF','SINOYD-LIMS-FX-28-01 色谱、色质联机分析原始记录（多组分）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-28-01 色谱、色质联机分析原始记录（多组分）_模板.xlsx','output/WorkSheet/色谱、色质联机分析原始记录（多组分）.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySingleSampleTestSPSZDZF", "workSheetDataSourceType" : "SPSZLJDZFWorkSheetDataSourceImpl"}',2850,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-19 15:40:36','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-07 14:07:22','workSheetFolderId,type','WorkSheet','WorkSheetSPSZLJDZF',0,'','',null,null,'',1,41);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('ee666c5a-4a50-4a20-b351-2972f40d69e5',1,'QcProjectAnnualStatistical','年度质控考核统计表.xlsx','LIMReportForms/年度质控考核统计表.xlsx','output/LIMReportForms/年度质控考核统计表.xlsx','application/excel','com.sinoyd.lims.report.service.exports.QcProjectAnnualStatisticalExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-03 09:59:05','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:34:59','com.sinoyd.lims.pro.criteria.QCProjectCriteria','LIMReportForms','export/QcProjectAnnualStatistical',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('ef5aa838-fa33-4b1e-bee2-23f5a7026fd3',1,'SolidStd','标准版炉渣报告.doc','Report/标准版报告.doc','output/Report/炉渣报告.doc','application/word','com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService','{"sort":"orderNum-"}','',600,4,'炉渣报告',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-05-30 14:03:38','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-02-07 15:38:34','reportId,sortId','Report','SolidStd',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('f378aa4b-0d48-4f94-8670-4b22ce9f0c05',1,'QcProjectHandPersonCompare','质控任务统计（手工、仪器比对）.xlsx','LIMReportForms/质控任务统计（手工、仪器比对）.xlsx','output/LIMReportForms/质控任务统计（手工、仪器比对）.xlsx','application/excel','com.sinoyd.lims.report.service.exports.QcProjectPersonCompareExportService','{"sort":"orderNum-"}','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-03 09:56:34','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:34:47','com.sinoyd.lims.pro.criteria.QCProjectCriteria','LIMReportForms','QcProjectHandPersonCompare',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('f6d77ffe-76fb-4c70-86bb-3acaa1574443',1,'DataParamsConfig','1.xls','','','','com.sinoyd.lims.report.service.exports.DataParamsConfigPoiExportServiceImpl','','',0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-10-16 13:50:47','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-10-17 11:14:34','com.sinoyd.lims.lim.criteria.DataParamsConfigExportCriteria','LIMReportForms','/',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('f780d70b-05aa-4f4d-a302-d56fcaeb8bc2',1,'NormalWaterTest','常规水检测报告_模板.doc','Report/常规水检测报告_模板.doc','output/Report/常规水检测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.NormalWaterTestReportService','{"sort":"orderNum-"}',null,1990,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-07 17:12:39','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:39','reportId,sortId','Report','NormalWaterTest',0,'','',null,null,null,0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('fb681ec3-5f64-49bf-a9c3-6bfb242c1b2c',1,'WorkSheetFGGDQWZZ','SINOYD-LIMS-FX-03-01 分光光度法原始记录（气-无组织）_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-03-01 分光光度法原始记录（气-无组织）_模板.xlsx','output/WorkSheet/分光光度法原始记录（气-无组织）_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "FGGDQWorkSheetDataSourceImpl" }',4950,2,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-12-30 14:06:08','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-23 16:07:19','workSheetFolderId,type','WorkSheet','WorkSheetFGGDQWZZ',0,'','',null,null,'',1,20);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('fc2386c6-d75f-434c-a787-1ce184089d8a',1,'WorkSheetSPSZLJQCL','SINOYD-LIMS-FX-32-01 色谱、色质联机分析原始记录（气-串联)_模板.xlsx','WorkSheet/SINOYD-LIMS-FX-32-01 色谱、色质联机分析原始记录（气-串联)_模板.xlsx','output/WorkSheet/色谱、色质联机分析原始记录（气-串联)_模板.xlsx','application/excel','com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService','','{"originalRecordType" : "lineExtendBySampleTestGroup", "workSheetDataSourceType" : "SPSZLJQCLWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}',2700,2,null,0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2022-01-15 15:44:23','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-15 15:16:28','workSheetFolderId,type','WorkSheet','WorkSheetSPSZLJQCL',0,'','',null,null,'',1,18);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('fc32463a-58a2-4d67-a040-a3bc8c07cea0',1,'ContainerList','容器清单.xlsx','','','application/excel','','','',0,1,'',1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-06-18 22:30:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-01-12 17:58:03','','LIMReportForms','ContainerList',0,'','','','','',0,null);
INSERT INTO "TB_LIM_REPORTCONFIG"("id","type","reportCode","templateName","template","outputName","returnType","method","params","pageConfig","orderNum","bizType","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate","dataMethod","typeCode","strUrl","isDefineFileName","defineFileName","beanName","versionNum","controlNum","reportName","validate","usageNum") VALUES('fffaa09b-3e4d-484c-9341-739b13c27c1d',1,'FallenDustTest','降尘检测报告_模板.doc','Report/降尘检测报告_模板.doc','output/Report/降尘检测报告.doc','application/word','com.sinoyd.lims.wordreport.service.jx.wordReport.FallenDustReportService','{"sort":"orderNum-"}',null,1750,4,null,1,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2021-12-21 13:36:59','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-09-19 14:08:47','reportId,sortId','Report','FallenDustTest',0,'','',null,null,null,0,null);

