package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.lims.lim.criteria.PersonCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoPersonCertQuery;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.customer.DtoPersonQuery;
import com.sinoyd.lims.lim.service.PersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 人员管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Api(tags = "人员管理: 人员管理服务")
@RestController
@RequestMapping("/api/lim/person")
@Validated
public class PersonController extends BaseJpaController<DtoPerson, String, PersonService> {


    /**
     * 分页动态条件查询人员
     *
     * @param personCriteria 条件
     * @return RestResponse<List < D t oPerson>>
     */
    @ApiOperation(value = "分页动态条件查询人员", notes = "分页动态条件查询人员")
    @GetMapping
    public RestResponse<List<DtoPerson>> findByPage(PersonCriteria personCriteria) {
        RestResponse<List<DtoPerson>> restResp = new RestResponse<>();
        PageBean<DtoPerson> page = super.getPageBean();
        service.findByPage(page, personCriteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 按主键查询人员信息
     *
     * @param id 主键id
     * @return RestResponse<DtoPerson>
     */
    @ApiOperation(value = "按主键id查询人员", notes = "按主键id查询人员")
    @GetMapping("/{id}")
    public RestResponse<DtoPerson> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoPerson> restResp = new RestResponse<>();
        DtoPerson person = service.findOne(id);
        restResp.setData(person);
        restResp.setRestStatus(StringUtil.isNull(person) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 新增人员
     *
     * @param entity 人员实体
     * @return RestResponse<DtoPerson>
     */
    @ApiOperation(value = "新增人员", notes = "新增人员")
    @PostMapping
    public RestResponse<DtoPerson> save(@Validated @RequestBody DtoPerson entity) {
        RestResponse<DtoPerson> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoPerson data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 更新人员
     *
     * @param entity 人员实体
     * @return RestResponse<DtoPerson>
     */
    @ApiOperation(value = "更新人员", notes = "更新人员")
    @PutMapping
    public RestResponse<DtoPerson> update(@Validated @RequestBody DtoPerson entity) {
        RestResponse<DtoPerson> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoPerson data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id删除人员
     *
     * @param id 删除Id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除人员", notes = "根据id删除人员")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@RequestBody String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(id));

        return restResp;
    }

    /**
     * 根据id批量删除人员
     *
     * @param ids 删除ids
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除人员", notes = "根据id批量删除人员")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(ids));

        return restResp;

    }

    /**
     * 新增账号
     *
     * @param entity 人员实体
     * @return RestResponse<DtoPerson>
     */
    @ApiOperation(value = "新增人员", notes = "新增人员")
    @PostMapping("/openAccount")
    public RestResponse<DtoPerson> openAccount(@RequestBody DtoUser entity) {
        RestResponse<DtoPerson> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.openAccount(entity.getId(), entity.getLoginId(), entity.getPassword(), entity.getRoleIds(), entity.getLoginExpand());

        restResp.setMsg("新增成功！");

        return restResp;
    }

    /**
     * 人员查询
     *
     * @param queryDto 查询dto
     * @return RestResponse<List < D t o KeyValue>>
     */
    @ApiOperation(value = "人员查询", notes = "人员查询")
    @PostMapping("/query")
    public RestResponse<List<DtoKeyValue>> query(@RequestBody DtoPersonQuery queryDto) {
        RestResponse<List<DtoKeyValue>> restResp = new RestResponse<>();
        List<DtoKeyValue> datas = service.query(queryDto);
        restResp.setRestStatus(StringUtil.isEmpty(datas) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(datas);
        restResp.setCount(StringUtil.isEmpty(datas) ? 0 : datas.size());

        return restResp;
    }

    /**
     * 人员查询
     *
     * @param queryDto 查询dto
     * @return RestResponse<List < D t o KeyValue>>
     */
    @ApiOperation(value = "人员查询", notes = "人员查询")
    @PostMapping("/queryMap")
    public RestResponse<List<Map<String, Object>>> queryOnlyName(@RequestBody DtoPersonQuery queryDto) {
        RestResponse<List<Map<String, Object>>> restResp = new RestResponse<>();
        List<Map<String, Object>> datas = service.queryOnlyName(queryDto);
        restResp.setRestStatus(StringUtil.isEmpty(datas) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(datas);
        restResp.setCount(StringUtil.isEmpty(datas) ? 0 : datas.size());

        return restResp;
    }

    /**
     * 根据测试项目查询人员及有证信息
     *
     * @param personCertQuery 查询条件
     * @return RestResponse<List < M a p < S t r i ng, O b j e ct>>>
     */
    @ApiOperation(value = "按主键查询项目类型", notes = "按主键查询项目类型")
    @PostMapping(path = "/cert")
    public RestResponse<List<Map<String, Object>>> queryWithCert(@RequestBody DtoPersonCertQuery personCertQuery) {
        RestResponse<List<Map<String, Object>>> restResp = new RestResponse<>();
        List<Map<String, Object>> datas = service.queryWithCert(personCertQuery);
        restResp.setRestStatus(StringUtil.isEmpty(datas) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(datas);
        restResp.setCount(StringUtil.isEmpty(datas) ? 0 : datas.size());

        return restResp;
    }


    @ApiOperation(value = "查询所有人的头像", notes = "查询所有人的头像")
    @GetMapping(path = "/photo")
    public RestResponse<List<Map<String, Object>>> findAllPhotoUrl(HttpServletRequest request) {
        RestResponse<List<Map<String, Object>>> restResp = new RestResponse<>();
        List<Map<String, Object>> datas = service.findAllPhotoUrl(request);
        restResp.setRestStatus(StringUtil.isEmpty(datas) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(datas);
        restResp.setCount(StringUtil.isEmpty(datas) ? 0 : datas.size());
        return restResp;
    }

    /**
     * 拼音转换
     *
     * @return
     */
    @ApiOperation(value = "拼音转换", notes = "拼音转换")
    @PostMapping("/changePinYin")
    public RestResponse<List<DtoPerson>> changePinYin() {
        RestResponse<List<DtoPerson>> rest = new RestResponse<>();
        rest.setData(service.changePinYinFull());
        return rest;
    }

    /**
     * 上传人员签名
     *
     * @param request 请求体
     * @return 保存签名后的人员数据
     */
    @ApiOperation(value = "上传人员签名", notes = "上传人员签名")
    @PostMapping("/uploadSignature")
    public RestResponse<DtoPerson> uploadSignature(HttpServletRequest request) {
        RestResponse<DtoPerson> response = new RestResponse<>();
        DtoPerson data = service.uploadSignature(request);
        response.setData(data);
        response.setRestStatus(StringUtil.isNull(data) ? ERestStatus.ERROR : ERestStatus.SUCCESS);
        response.setCount(StringUtil.isNull(data) ? 0 : 1);
        return response;
    }

    /**
     * 上传人员签名
     *
     * @param ids 人员签名id
     * @return 保存签名后的人员数据
     */
    @ApiOperation(value = "删除人员签名", notes = "删除人员签名")
    @DeleteMapping("/signature")
    public RestResponse<DtoPerson> uploadSignature(@RequestBody List<String> ids) {
        RestResponse<DtoPerson> response = new RestResponse<>();
        DtoPerson data = service.deleteSignature(ids);
        response.setData(data);
        response.setRestStatus(StringUtil.isNull(data) ? ERestStatus.ERROR : ERestStatus.SUCCESS);
        response.setCount(StringUtil.isNull(data) ? 0 : 1);
        return response;
    }

    /**
     * 判断当前登录人有无签名文件
     *
     * @return 返回值
     */
    @ApiOperation(value = "判断当前登录人有无签名文件", notes = "判断当前登录人有无签名文件")
    @GetMapping("/haveSignature")
    public RestResponse<Boolean> haveSignature() {
        RestResponse<Boolean> response = new RestResponse<>();
        response.setData(service.haveSignature());
        return response;
    }

    /**
     * 上岗证总览
     *
     * @param personCriteria 条件
     * @return RestResponse<List<DtoPerson>>
     */
    @ApiOperation(value = "上岗证总览", notes = "上岗证总览")
    @GetMapping("/certPreview")
    public RestResponse<Map<String,Object>> certPreview(PersonCriteria personCriteria) {
        RestResponse<Map<String,Object>> restResp = new RestResponse<>();
        PageBean<DtoPerson> page = super.getPageBean();
        restResp.setData(service.certPreview(page, personCriteria));
        return restResp;
    }
}