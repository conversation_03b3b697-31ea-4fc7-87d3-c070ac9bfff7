package com.sinoyd.lims.monitor.dto.lims;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.entity.FixedPointProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoFixedPointProperty实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_FixedPointProperty")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoFixedPointProperty extends FixedPointProperty {
    private static final long serialVersionUID = 1L;

    /**
     * 是否与项目关联
     */
    @Transient
    private String projectRelevance;

    /**
     * 树结构中显示的监测计划名称
     */
    @Transient
    private String displayName;

    /**
     * 子监测计划
     */
    @Transient
    private List<DtoFixedPointProperty> childList;

    /**
     * 关联点位id列表
     */
    @Transient
    private List<String> relationPointIds;

    /**
     * 关联点位列表
     */
    @Transient
    private List<DtoFixedpoint> relationPointList;

    /**
     * 父级监测计划名称
     */
    @Transient
    private String parentName;

    /**
     * 样品数
     */
    @Transient
    private Integer samplePeriod;

    /**
     * 源对象的主键，用于复制
     */
    @Transient
    private String sourceId;

    /**
     * 月份list用于多月份新增
     */
    @Transient
    private List<Integer> months;

    /**
     * 测试项目集合
     */
    @Transient
    private List<DtoTest> testList;

    /**
     * 是否测试项目提示（用作例行点位，存在”停用”、“作废”、“删除”的测试项目时，进行提示）
     */
    @Transient
    private Boolean isTestTip = false;
}