package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *  测试人员的变动
 * <AUTHOR>
 * @version V1.0.0 2019/12/21
 * @since V100R001
 */
@Data
public class DtoPerson2TestChange {

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 检测类型ids
     */
    private List<String> sampleTypeIds = new ArrayList<>();


    /**
     * 组织结构id
     */
    private String orgId;

    /**
     * 变动的人员id
     */
    private String personId;

    /**
     * 创建人
     */
    private String creator;
}
