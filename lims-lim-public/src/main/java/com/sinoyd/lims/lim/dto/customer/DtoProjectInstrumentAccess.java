package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.util.Date;


/**
 * DtoProjectInstrumentAccess 传输类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/11/13
 * @since V100R001
 */

@Data
public class DtoProjectInstrumentAccess {


    /**
     * 使用日期
     */
    private Date accessDate;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;



    /**
     * 出库合格情况，0 - 不合格，1 - 合格
     */
    private Integer outQualified;

    /**
     * 入库合格情况，0 - 不合格，1 - 合格
     */
    private Integer intQualified;

    /**
     * 使用人员名称，多个用英文逗号间隔
     */
    private String userNames;

    /**
     * 管理人员名称
     */
    private String administratorName;

}