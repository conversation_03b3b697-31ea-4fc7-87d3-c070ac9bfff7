package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.rcc.DtoQualityLimitDisposition;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;

import java.util.Collection;
import java.util.List;


/**
 * QualityLimitDisposition操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/5/27
 * @since V100R001
 */
public interface QualityLimitDispositionService extends IBaseJpaService<DtoQualityLimitDisposition, String> {

    /**
     * 删除配置id
     *
     * @param ids 配置ids
     * @return 删除数
     */
    Integer deleteByIds(Collection<String> ids);

    /**
     * 配置设置是否默认
     *
     * @param disposition 配置信息
     * @return 是否
     */
    Boolean inspectDisposition(DtoQualityLimitDisposition disposition);

    /**
     * 根据配置Id获取测试项目集合
     *
     * @param dispositionId 配置Id
     * @return 测试项目集合
     */
    List<DtoTest> findTestListByDispositionId(String dispositionId);
}