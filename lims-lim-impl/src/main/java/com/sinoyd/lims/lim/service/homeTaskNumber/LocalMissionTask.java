package com.sinoyd.lims.lim.service.homeTaskNumber;

import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.lim.enums.EnumLIM;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 首页现场任务代办数字缓存刷新
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/10/26
 */
@Component
public class LocalMissionTask extends AbsTaskNumber {

    /**
     * 获取缓存数据集合
     *
     * @param homeModule 模块编码
     * @param orgId      组织Id
     * @param userId     用户Id
     * @param outTypeIds 不包含的项目类型
     * @return 结果集
     */
    @Override
    public List<DtoTaskNum> getTaskNum(HomeModule homeModule, String orgId, String userId, List<String> outTypeIds) {
        //定义sql
        StringBuilder stringBuilder = new StringBuilder("select c.person_Id as userId,count(c.id) as count from");
        stringBuilder.append("(SELECT a.id, b.senderId as person_Id from TB_PRO_StatusForRecord a,TB_PRO_ReceiveSampleRecord b,TB_PRO_Project c")
                .append(" where 1=1 and a.orgId = ?")
                .append(" and a.status = ? and a.module = ?")
                .append(" and a.receiveId = b.id and b.projectId = c.id and c.isDeleted = 0")
                .append(" union")
                .append(" SELECT a.id, d.samplingPersonId as person_Id from")
                .append(" TB_PRO_StatusForRecord a,TB_PRO_ReceiveSampleRecord b,TB_PRO_Project c,TB_PRO_SamplingPersonConfig d")
                .append(" where 1=1 and a.orgId = ?")
                .append(" and a.status = ? and a.module = ?")
                .append(" and a.receiveId = b.id and b.projectId = c.id and c.isDeleted = 0 and a.receiveId = d.objectId and d.objectType = 1")
                .append(" ) c group by c.person_Id");
        //执行sql语句
        return jdbcTemplate.query(stringBuilder.toString(),
                new String[]{orgId,"1",EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue(),orgId,"1",EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue()},
                (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"),resultSet.getLong("count")));
    }

    /**
     * 获取模块编码
     *
     * @return 模块编码
     */
    @Override
    public String getModuleCode() {
        return EnumLIM.EnumHomeTaskModule.现场任务.getValue();
    }
}
