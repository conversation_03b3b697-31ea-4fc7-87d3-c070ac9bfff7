package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityConfigTemp;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityValidate;
import com.sinoyd.lims.lim.dto.customer.DtoDocUserConfigTemp;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityConfig;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityList;

import java.util.List;

/**
 * 文件夹权限接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/3
 * @since V100R001
 */
public interface DocAuthorityConfigService extends IBaseJpaService<DtoDocAuthorityConfig, String> {

    /**
     * 根据objectId 查询权限
     *
     * @param objectId 文件夹id
     * @return
     */
    List<DtoDocAuthorityList> findAuthorityList(String objectId, String orgId);

    /**
     * 根据objectId,authId 查询人员
     *
     * @param objectId 文件夹id
     * @param authCode 权限编码
     * @return
     */
    List<DtoDocUserConfigTemp> findUserList(String objectId, String authCode);

    /**
     * 查询所有人员
     * @return List<DtoDocUserConfigTemp>
     */
    List<DtoDocUserConfigTemp> findAllUserList();

    /**
     * 保存配置人员权限
     *
     * @param temp 人员信息
     */
    void saveAll(DtoDocAuthorityConfigTemp temp);

    /**
     * 批量保存配置人员权限
     *
     * @param temp 人员信息
     */
    void batchSaveAll(DtoDocAuthorityConfigTemp temp);

    /**
     * 权限验证
     *
     * @param dtoDocAuthorityValidate 文档权限验证实体
     * @return 是否具有权限
     */
    Boolean validateAuth(DtoDocAuthorityValidate dtoDocAuthorityValidate);


    /**
     * 初始化权限信息
     *
     * @param objectId 文件夹id
     * @param userId   用户id
     * @return 权限数据集合
     */
    List<DtoDocAuthorityConfig> initAuthorityConfig(String objectId, String userId);

    /**
     * 批量设置
     *
     * @param ids  id集合
     * @param defaultOpenInd  是否开启
     */
    void batchSet(List<String> ids, Boolean defaultOpenInd);

    /**
     * 文件夹批量设置
     *
     * @param ids               文件夹id
     * @param authCodes         权限编码
     * @param defaultOpenInd    是否开启
     */
    void batchSet(List<String> ids, List<String> authCodes, Boolean defaultOpenInd);

    /**
     * 文件夹批量设置
     *
     * @param ids               文件夹id
     * @param authCodes         权限id集合
     * @param userIds           用户id
     */
    void batchSet(List<String> ids, List<String> authCodes, List<String> userIds);

}
