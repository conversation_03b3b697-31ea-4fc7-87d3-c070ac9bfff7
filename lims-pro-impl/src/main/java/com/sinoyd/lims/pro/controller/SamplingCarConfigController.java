package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.SamplingCarConfigService;
import com.sinoyd.lims.pro.criteria.SamplingCarConfigCriteria;
import com.sinoyd.lims.pro.dto.DtoSamplingCarConfig;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * SamplingCarConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: SamplingCarConfig服务")
 @RestController
 @RequestMapping("api/pro/samplingCarConfig")
 public class SamplingCarConfigController extends BaseJpaController<DtoSamplingCarConfig, String,SamplingCarConfigService> {


    /**
     * 分页动态条件查询SamplingCarConfig
     * @param samplingCarConfigCriteria 条件参数
     * @return RestResponse<List<SamplingCarConfig>>
     */
     @ApiOperation(value = "分页动态条件查询SamplingCarConfig", notes = "分页动态条件查询SamplingCarConfig")
     @GetMapping
     public RestResponse<List<DtoSamplingCarConfig>> findByPage(SamplingCarConfigCriteria samplingCarConfigCriteria) {
         PageBean<DtoSamplingCarConfig> pageBean = super.getPageBean();
         RestResponse<List<DtoSamplingCarConfig>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, samplingCarConfigCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询SamplingCarConfig
     * @param id 主键id
     * @return RestResponse<DtoSamplingCarConfig>
     */
     @ApiOperation(value = "按主键查询SamplingCarConfig", notes = "按主键查询SamplingCarConfig")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoSamplingCarConfig> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoSamplingCarConfig> restResponse = new RestResponse<>();
         DtoSamplingCarConfig samplingCarConfig = service.findOne(id);
         restResponse.setData(samplingCarConfig);
         restResponse.setRestStatus(StringUtil.isNull(samplingCarConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增SamplingCarConfig
     * @param samplingCarConfig 实体列表
     * @return RestResponse<DtoSamplingCarConfig>
     */
     @ApiOperation(value = "新增SamplingCarConfig", notes = "新增SamplingCarConfig")
     @PostMapping
     public RestResponse<DtoSamplingCarConfig> create(@RequestBody DtoSamplingCarConfig samplingCarConfig) {
         RestResponse<DtoSamplingCarConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.save(samplingCarConfig));
         return restResponse;
      }

     /**
     * 新增SamplingCarConfig
     * @param samplingCarConfig 实体列表
     * @return RestResponse<DtoSamplingCarConfig>
     */
     @ApiOperation(value = "修改SamplingCarConfig", notes = "修改SamplingCarConfig")
     @PutMapping
     public RestResponse<DtoSamplingCarConfig> update(@RequestBody DtoSamplingCarConfig samplingCarConfig) {
         RestResponse<DtoSamplingCarConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.update(samplingCarConfig));
         return restResponse;
      }

    /**
     * "根据id批量删除SamplingCarConfig
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除SamplingCarConfig", notes = "根据id批量删除SamplingCarConfig")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }