package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * AnalyseData实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "AnalyseData")
@Data
@EntityListeners(AuditingEntityListener.class)
public class AnalyseData implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public AnalyseData() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 检测单子Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测单子Id")
    private String workSheetId;

    /**
     * 检测单Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测单Id")
    private String workSheetFolderId;

    /**
     * 分包单位id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分包单位id")
    private String subId;

    /**
     * 样品Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("样品Id")
    private String sampleId;

    /**
     * 样品检测类型id-冗余
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("样品检测类型id-冗余")
    private String sampleTypeId;

    /**
     * 测试id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("测试id")
    private String testId;

    /**
     * 分析项目名称
     */
    @Column(length = 100)
    @ApiModelProperty("分析项目名称")
    private String redAnalyzeItemName;

    /**
     * 分析方法名称
     */
    @ApiModelProperty("分析方法名称")
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    @Column(length = 255)
    @ApiModelProperty("国家标准")
    private String redCountryStandard;

    /**
     * 分析项目id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("分析项目id")
    private String analyseItemId;

    /**
     * 分析方法id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("分析方法id")
    private String analyzeMethodId;

    /**
     * 质控id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("质控id")
    private String qcId;

    /**
     * 质控等级（枚举EnumQCGrade：0.外部质控  1.内部质控）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("质控等级（枚举EnumQCGrade：0.外部质控  1.内部质控）")
    private Integer qcGrade;

    /**
     * 质控类型（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("质控类型（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）")
    private Integer qcType;


    /**
     * 质控类型（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否是质控任务")
    private Boolean isQm;

    /**
     * 领样单id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("领样单id")
    private String receiveSubId;

    /**
     * 有效位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("有效位数")
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("小数位数")
    private Integer mostDecimal;

    /**
     * 测定下限
     */
    @Column(length = 50)
    @ApiModelProperty("测定下限")
    private String lowerLimit;

    /**
     * 检出限
     */
    @Column(length = 50)
    @ApiModelProperty("检出限")
    private String examLimitValue;

    /**
     * 单位Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("单位Id")
    private String dimensionId;

    /**
     * 单位（字符串）
     */
    @Column(length = 50)
    @ApiModelProperty("单位（字符串）")
    private String dimension;

    /**
     * 出证结果
     */
    @Column(length = 100)
    @ApiModelProperty("出证结果")
    private String testValue;

    /**
     * 检测结果（未修约）
     */
    @Column(length = 100)
    @ApiModelProperty("检测结果（未修约）")
    private String testOrignValue;

    /**
     * 参与运算的值（检测结果的数值）（已修约）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("参与运算的值（检测结果的数值）（已修约）")
    private BigDecimal testValueD;

    /**
     * 检测结果（已修约）
     */
    @Column(length = 100)
    @ApiModelProperty("检测结果（已修约）")
    private String testValueDstr;

    /**
     * 数据状态（字符串,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）
     */
    @Column(length = 50)
    @ApiModelProperty("数据状态（字符串,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）")
    private String status;

    /**
     * 数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）")
    private Integer dataStatus;

    /**
     * 数据变更状态（枚举EnumDataChangeStatus： 0.未变更 1.新增 2.修改 3.删除）（针对已经编制报告的数据修改状态-样品数据增删改）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("数据变更状态（枚举EnumDataChangeStatus： 0.未变更 1.新增 2.修改 3.删除）（针对已经编制报告的数据修改状态-样品数据增删改）")
    private Integer dataChangeStatus;

    /**
     * 分析人员Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析人员Id")
    private String analystId;

    /**
     * 分析人员
     */
    @Column(length = 50)
    @ApiModelProperty("分析人员")
    private String analystName;

    /**
     * 数据分析时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("数据分析时间")
    private Date analyzeTime;

    /**
     * 数据录入时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("数据录入时间")
    private Date dataInputTime;

    /**
     * 平行样和原样的均值
     */
    @Column
    @ApiModelProperty("平行样和原样的均值")
    private String pxAverageValue;

    /**
     * 有效性（数据确认，是否出具在报告上）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("有效性（数据确认，是否出具在报告上）")
    private Boolean isDataEnabled;

    /**
     * 是否在现场完成(根据实际情况填写)
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否在现场完成(根据实际情况填写)")
    private Boolean isCompleteField;

    /**
     * 是否分包
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否分包")
    private Boolean isOutsourcing;

    /**
     * 是否采测分包
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否采测分包")
    private Boolean isSamplingOut;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 要求完成时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("要求完成时间")
    private Date requireDeadLine;

    /**
     * 等级(EnumProjectGrade：0.一般 1.紧急 2.特急)
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("等级(EnumProjectGrade：0.一般 1.紧急 2.特急)")
    private Integer grade;

    /**
     * 所属科室ID
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属科室ID")
    private String deptId;

    /**
     * 国检_是否合格(数据复验后是否仍超出限量值)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("国检_是否合格(数据复验后是否仍超出限量值)")
    private Boolean isQualified;

    /**
     * 国检_复验次数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("国检_复验次数")
    private Integer repeatTimes;

    /**
     * 水利_测试任务id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("水利_测试任务id")
    private String testTaskId;

    /**
     * 是有上岗证
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("是有上岗证")
    private Boolean isPostCert;

    /**
     * 上岗证有效期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("上岗证有效期")
    private Date certEffectiveTime;

    /**
     * 领样日期
     */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("领样日期")
    private Date sampleReceiveDate;

    /**
     * 是否科学计数法
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否科学计数法")
    private Boolean isSci;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 采集编号
     */
    @Column(length = 50)
    @ApiModelProperty("采集编号")
    private String gatherCode;

    /**
     * 质控信息
     */
    @Column(length = 100)
    @ApiModelProperty("质控信息")
    private String qcInfo;

    /**
     * 串联中间结果
     */
    @Column(length = 100)
    @ApiModelProperty("串联中间结果")
    private String seriesValue;

    /**
     * 分析完成日期
     */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("分析完成日期")
    private Date finishTime;

}