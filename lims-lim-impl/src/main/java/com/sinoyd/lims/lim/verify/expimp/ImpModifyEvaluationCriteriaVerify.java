package com.sinoyd.lims.lim.verify.expimp;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.rcc.*;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpEvaluationCriteria;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 评价标准导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/20
 * @since V100R001
 */

@Data
public class ImpModifyEvaluationCriteriaVerify implements IExcelVerifyHandler<DtoExpImpEvaluationCriteria> {

    //region 线程变量
    /**
     * 临时数据
     */
    private final List<DtoAnalyzeItem> analyzeItemList;

    /**
     * 所有的检测类型数据
     */
    private final List<DtoSampleType> sampleTypeList;

    /**
     * 所有的评价标准数据
     */
    private final List<DtoEvaluationCriteria> evaCriteriaList;

    /**
     * 所有的评价等级数据
     */
    private final List<DtoEvaluationLevel> evaLevelList;

    /**
     * 所有的评价因子数据
     */
    private final List<DtoEvaluationValue> evaValuesList;

    /**
     * 所有的评价因子数据
     */
    private final List<DtoDimension> dimensionList;

    /**
     * 临时导入数据
     */
    private List<DtoExpImpEvaluationCriteria> evaluationTempList;
    //endregion

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();


    public ImpModifyEvaluationCriteriaVerify(List<DtoAnalyzeItem> analyzeItemList,
                                             List<DtoSampleType> sampleTypeList,
                                             List<DtoEvaluationCriteria> evaCriteriaList,
                                             List<DtoEvaluationLevel> evaLevelList,
                                             List<DtoEvaluationValue> evaValuesList,
                                             List<DtoDimension> dimensionList) {
        this.analyzeItemList = analyzeItemList;
        this.sampleTypeList = sampleTypeList;
        this.evaCriteriaList = evaCriteriaList;
        this.evaLevelList = evaLevelList;
        this.evaValuesList = evaValuesList;
        this.dimensionList = dimensionList;
    }

    /**
     * 人员导入数据校验
     *
     * @param dto 导入数据
     * @return 校验结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExpImpEvaluationCriteria dto) {
        //导入参数处理
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(dto)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //前后去空格
            importUtils.strToTrim(dto);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        StringBuilder failStr = new StringBuilder("第" + (dto.getRowNum() + 1) + "行数据校验错误");
        //必填校验
        importUtils.checkIsNull(result, dto.getName(), "标准名称", failStr);
        importUtils.checkIsNull(result, dto.getCode(), "标准代码", failStr);
        importUtils.checkIsNull(result, dto.getSampleTypeName(), "样品类型", failStr);
        if (StringUtil.isEmpty(evaluationTempList)) {
            evaluationTempList = new ArrayList<>();
        }
        //判断检测类型是否存在
        isExistSampleType(result, dto, sampleTypeList, failStr);
        //判断评价因子是否存在
        isExistAnaItem(result, dto, analyzeItemList, failStr);
        //判断评价因子是否存在
        isExistDimension(result, dto, dimensionList, failStr);
        //判断数据是否重复
        isRepeatData(result, dto, evaluationTempList, failStr);
        //处理数据Id
        handleData(dto, evaCriteriaList, evaLevelList, analyzeItemList, sampleTypeList);
        //判断数据库中是否存在重复评价因子
        isRepeatAnaItem(result, dto, evaValuesList, evaCriteriaList, failStr);
        //数据格式校验
        importUtils.checkDateTwo(result, dto.getStartTime(), "实施时间", failStr);
        //处理校验字符串
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        evaluationTempList.add(dto);
        //返回数据
        return result;
    }


    /**
     * 量纲检验
     *
     * @param result  校验结果
     * @param dto     实体
     * @param dto     所有量纲
     * @param failStr 检验错误信息
     */
    private void isExistDimension(ExcelVerifyHandlerResult result, DtoExpImpEvaluationCriteria dto, List<DtoDimension> dimensionList, StringBuilder failStr) {
        List<DtoDimension> isExistDimension;
        if (StringUtil.isNotEmpty(dto.getDimension())) {
            isExistDimension = dimensionList.stream().filter(p -> dto.getDimension().equals(p.getDimensionName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistDimension)) {
                result.setSuccess(false);
                failStr.append("；量纲在系统重不存在");
            } else {
                dto.setDimensionId(isExistDimension.get(0).getId());
            }
        }else {
            dto.setDimensionId(UUIDHelper.GUID_EMPTY);
        }
    }

    /**
     * 判断数据库中是否存在重复评价因子
     *
     * @param result           校验结果
     * @param dto              导入的数据
     * @param evaluationValues 数据库中的限值
     * @param failStr          校验错误字符
     */
    private void isRepeatAnaItem(ExcelVerifyHandlerResult result, DtoExpImpEvaluationCriteria dto, List<DtoEvaluationValue> evaluationValues, List<DtoEvaluationCriteria> evaCriteriaList, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(dto.getAnalyzeItemId()) && StringUtil.isNotEmpty(dto.getEvaluationCriteriaId()) && StringUtil.isNotEmpty(dto.getEvaluationCriteriaId())) {
            // 数据库筛选是否存在评价标准，存在则不检查
            Optional<DtoEvaluationCriteria> criteriaOptional = evaCriteriaList.stream().filter(p -> dto.getEvaluationCriteriaId().equals(p.getId())).findFirst();
            if (!criteriaOptional.isPresent()) {
                List<DtoEvaluationValue> repeatList = evaluationValues.stream()
                        .filter(p -> dto.getAnalyzeItemId().equals(p.getAnalyzeItemId())
                                && dto.getEvaluationCriteriaId().equals(p.getEvaluationId())
                                && dto.getEvaluationLevelId().equals(p.getLevelId()))
                        .collect(Collectors.toList());
                if (StringUtil.isNotEmpty(repeatList)) {
                    result.setSuccess(false);
                    failStr.append("；此评价标准下评价限值在系统中已存在");
                }
            }
        }

    }

    /**
     * 导入数据重复校验
     *
     * @param result         校验结果
     * @param dto            导入的数据
     * @param evaluationTemp 临时数据
     * @param failStr        校验错误字符
     */
    private void isRepeatData(ExcelVerifyHandlerResult result, DtoExpImpEvaluationCriteria dto,
                              List<DtoExpImpEvaluationCriteria> evaluationTemp, StringBuilder failStr) {
        if (StringUtil.isNotEmpty(evaluationTemp)) {
            //校验评价标准编码重复问题
            if (StringUtil.isNotEmpty(dto.getCode()) && StringUtil.isNotEmpty(dto.getName())) {
                List<DtoExpImpEvaluationCriteria> sameCodeList = evaluationTemp.stream().filter(p -> dto.getCode().equals(p.getCode())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(sameCodeList)) {
                    //查找相同标准编码下的不同名称的数据
                    List<DtoExpImpEvaluationCriteria> notSameNameList = sameCodeList.stream().filter(p -> !dto.getName().equals(p.getName())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(notSameNameList)) {
                        result.setSuccess(false);
                        failStr.append("；已存在相同的标准评价编码");
                    }
                }
                //校验评价因子重复
                if (StringUtil.isNotEmpty(dto.getEvaluationLevel()) && StringUtil.isNotEmpty(dto.getAnalyzeItemNames())) {
                    List<DtoExpImpEvaluationCriteria> sameNameAndCodeList = sameCodeList.stream()
                            .filter(p -> dto.getName().equals(p.getName())
                                    && dto.getCode().equals(p.getCode())
                                    && dto.getEvaluationLevel().equals(p.getEvaluationLevel())
                                    && dto.getAnalyzeItemNames().equals(p.getAnalyzeItemNames()))
                            .collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(sameNameAndCodeList)) {
                        result.setSuccess(false);
                        failStr.append("；此评价等级下已存在相同的评价因子");
                    }
                }
            }
        }
    }

    /**
     * 检测类型检验
     *
     * @param result             校验结果
     * @param evaluationCriteria 实体
     * @param dbSampleType       所有检测类型
     * @param failStr            检验错误信息
     */
    private void isExistSampleType(ExcelVerifyHandlerResult result, DtoExpImpEvaluationCriteria evaluationCriteria, List<DtoSampleType> dbSampleType, StringBuilder failStr) {
        List<DtoSampleType> isExistEnterprise;
        if (StringUtil.isNotEmpty(evaluationCriteria.getSampleTypeName())) {
            isExistEnterprise = dbSampleType.stream().filter(p -> evaluationCriteria.getSampleTypeName().equals(p.getTypeName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistEnterprise)) {
                result.setSuccess(false);
                failStr.append("；检测类型在系统中不存在");
            }
        }
    }

    /**
     * 判断是否存在评价因子
     *
     * @param result           校验结果
     * @param importEnterprise 导入的企业数据
     * @param analyzeItems     所有区域信息
     * @param failStr          错误信息
     */
    private void isExistAnaItem(ExcelVerifyHandlerResult result, DtoExpImpEvaluationCriteria importEnterprise, List<DtoAnalyzeItem> analyzeItems, StringBuilder failStr) {
        List<DtoAnalyzeItem> isExistAnaItem;
        if (StringUtil.isNotEmpty(importEnterprise.getAnalyzeItemNames())) {
            isExistAnaItem = analyzeItems.stream().filter(p -> importEnterprise.getAnalyzeItemNames().equals(p.getAnalyzeItemName())).collect(Collectors.toList());
            if (StringUtil.isEmpty(isExistAnaItem)) {
                result.setSuccess(false);
                failStr.append("；评价因子在系统中不存在");
            }
        }
    }

    /**
     * 处理已经存在的评价标准和分析因子，绑定id
     *
     * @param importDto          导入的数据
     * @param evaluationCriteria 评价标准数据
     * @param evaluationLevels   评价登记数据
     * @param analyzeItems       分析因子数据
     * @param sampleTypes        检测类型数据
     */
    private void handleData(DtoExpImpEvaluationCriteria importDto, List<DtoEvaluationCriteria> evaluationCriteria,
                            List<DtoEvaluationLevel> evaluationLevels, List<DtoAnalyzeItem> analyzeItems, List<DtoSampleType> sampleTypes) {
        //处理评价标准id
        Map<String, List<DtoEvaluationLevel>> levelMap = evaluationLevels.stream().collect(Collectors.groupingBy(DtoEvaluationLevel::getEvaluationId));
        Optional<DtoEvaluationCriteria> criteriaOp = evaluationCriteria.stream()
                .filter(p -> importDto.getName().equals(p.getName()) && importDto.getCode().equals(p.getCode()))
                .findFirst();
        // 已存在的评价标准赋值id
        criteriaOp.ifPresent(p -> {
            importDto.setEvaluationCriteriaId(p.getId());
            if (StringUtil.isEmpty(importDto.getId())){
                importDto.setId(p.getId());
            }
        });
        //处理评价等级id
        if (StringUtil.isNotEmpty(importDto.getEvaluationCriteriaId())) {
            List<DtoEvaluationLevel> levelOfCriteria = levelMap.get(importDto.getEvaluationCriteriaId());
            if (StringUtil.isNotEmpty(levelOfCriteria)) {
                // ## 替换分隔符
                String evaluationLevel = importDto.getEvaluationLevel().replace(LimConstants.ImportConstants.CHINESE_SPLIT_CHAR, LimConstants.ImportConstants.ENGLISH_SPLIT_CHAR);
                Optional<DtoEvaluationLevel> levelOfCriteriaOp = levelOfCriteria.stream().filter(p -> evaluationLevel.equals(p.getLevelFullName())).findFirst();
                levelOfCriteriaOp.ifPresent(p -> importDto.setEvaluationLevelId(p.getId()));
            }
        }
        //处理评价因子id
        if (StringUtil.isNotEmpty(importDto.getAnalyzeItemNames()) && StringUtil.isNotEmpty(analyzeItems)) {
            Optional<DtoAnalyzeItem> anaItemOp = analyzeItems.stream().filter(p -> importDto.getAnalyzeItemNames().equals(p.getAnalyzeItemName())).findFirst();
            anaItemOp.ifPresent(p -> importDto.setAnalyzeItemId(p.getId()));
        }
        //处理检测类型id
        if (StringUtil.isNotEmpty(importDto.getSampleTypeName()) && StringUtil.isNotEmpty(sampleTypes)) {
            Optional<DtoSampleType> sampleTypeOp = sampleTypes.stream().filter(p -> importDto.getSampleTypeName().equals(p.getTypeName())).findFirst();
            sampleTypeOp.ifPresent(p -> importDto.setSampleTypeId(p.getId()));
        }
    }
}
