package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.Date;

/**
 * 样品公共实体对象
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoSampleTemp {
    /**
     * 样品id
     */
    private String id;

    /**
     * 样品编号
     */
    private String code;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 采样时间
     */
    private Date samplingTimeBegin;

    /**
     * 分析项目
     */
    private String redAnalyzeItems;

    /**
     * 样品状态
     */
    private Integer samplingStatus;

    /**
     * 样品状态
     */
    private String status;

    /**
     * 出证状态
     */
    private String certificateStatus;

    /**
     * 是否选中（报告选择样品用）
     */
    private Boolean isChecked;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 送样单信息状态（枚举EnumReceiveInfoStatus：1.信息登记中，2.信息复核中 3.信息审核中 4.已确认）
     */
    private Integer infoStatus;

    /**
     * 送样单复核人id（用作前端判断送样单状态）
     */
    private String checkerId;


    /**
     * 构造函数
     */
    public DtoSampleTemp() {

    }

    /**
     * 构造函数
     *
     * @param id                样品id
     * @param code              样品编号
     * @param sampleTypeId      检测类型id
     * @param sampleTypeName    检测类型名称
     * @param redFolderName     点位名称
     * @param samplingTimeBegin 采样时间
     * @param redAnalyzeItems   分析项目
     * @param samplingStatus    采样状态
     * @param status            样品状态
     */
    public DtoSampleTemp(String id, String code, String sampleTypeId, String sampleTypeName, String redFolderName, Date samplingTimeBegin, String redAnalyzeItems, Integer samplingStatus, String status, String projectId) {
        this.id = id;
        this.code = code;
        this.sampleTypeId = sampleTypeId;
        this.sampleTypeName = sampleTypeName;
        this.redFolderName = redFolderName;
        this.samplingTimeBegin = samplingTimeBegin;
        this.redAnalyzeItems = redAnalyzeItems;
        this.samplingStatus = samplingStatus;
        this.status = status;
        this.projectId = projectId;
    }

    /**
     * 构造函数
     *
     * @param id                样品id
     * @param code              样品编号
     * @param sampleTypeId      检测类型id
     * @param sampleTypeName    检测类型名称
     * @param redFolderName     点位名称
     * @param samplingTimeBegin 采样时间
     * @param redAnalyzeItems   分析项目
     * @param samplingStatus    采样状态
     * @param status            样品状态
     * @param projectId         项目id
     * @param receiveId         送样单id
     */
    public DtoSampleTemp(String id, String code, String sampleTypeId, String sampleTypeName, String redFolderName, Date samplingTimeBegin,
                         String redAnalyzeItems, Integer samplingStatus, String status, String projectId, String receiveId) {
        this(id, code, sampleTypeId, sampleTypeName, redFolderName, samplingTimeBegin, redAnalyzeItems, samplingStatus, status, projectId);
        this.receiveId = receiveId;
    }
}
