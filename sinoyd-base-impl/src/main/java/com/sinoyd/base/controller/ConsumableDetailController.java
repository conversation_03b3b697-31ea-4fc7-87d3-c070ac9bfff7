package com.sinoyd.base.controller;

import java.util.List;

import com.sinoyd.base.criteria.ConsumableDetailCriteria;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.service.ConsumableDetailService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 消耗品详单管理接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
@Api(tags = "消耗品详单管理")
@RestController
@RequestMapping("/api/base/consumableDetail")
@Validated
public class ConsumableDetailController
        extends BaseJpaController<DtoConsumableDetail, String, ConsumableDetailService> {

    /**
     * 获取消耗品详单信息
     *
     * @param id 消耗品详单id
     * @return 返回消耗品详单信息
     */
    @ApiOperation(value = "根据详单id查询详单信息", notes = "根据详单id查询详单信息")
    @GetMapping("/{id}")
    public RestResponse<DtoConsumableDetail> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoConsumableDetail> restResp = new RestResponse<>();

        DtoConsumableDetail instrument = service.findOne(id);
        restResp.setData(instrument);

        restResp.setRestStatus(StringUtil.isNull(instrument) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页获取消耗品详单信息
     *
     * @param criteria 消耗品详单查询条件
     * @return 返回消耗品详单列表
     */
    @ApiOperation(value = "分页获取消耗品详单信息", notes = "分页获取消耗品详单信息")
    @GetMapping
    public RestResponse<List<DtoConsumableDetail>> findByPage(ConsumableDetailCriteria criteria) {

        RestResponse<List<DtoConsumableDetail>> restResp = new RestResponse<>();

        PageBean<DtoConsumableDetail> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增的消耗品详单信息
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增的消耗品详单信息", notes = "新增的消耗品详单信息")
    @PostMapping
    public RestResponse<DtoConsumableDetail> save(@Validated @RequestBody DtoConsumableDetail entity) {

        RestResponse<DtoConsumableDetail> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoConsumableDetail data = service.saveAndChangeConsumable(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 批量修改消耗品信息详单
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改消耗品信息详单", notes = "修改消耗品信息详单")
    @PutMapping
    public RestResponse<DtoConsumableDetail> update(@Validated @RequestBody DtoConsumableDetail entity) {

        RestResponse<DtoConsumableDetail> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoConsumableDetail data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id批量删除消耗品详单
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量删除消耗品详单", notes = "根据id批量删除消耗品详单")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }


    /***
     * 获取生产厂商
     * @param isStandard 是否标准物质
     * @return
     */
    @ApiOperation(value = "分页动态条件查询消耗品/标样", notes = "分页动态条件查询消耗品/标样")
    @GetMapping("/getManufacturerList")
    public RestResponse<List<DtoConsumableDetail>> getManufacturerList(Boolean isStandard) {
        RestResponse<List<DtoConsumableDetail>> restResp = new RestResponse<>();
        List<DtoConsumableDetail> list = service.getManufacturerList(isStandard);

        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(list);
        restResp.setCount(list.size());

        return restResp;
    }
}