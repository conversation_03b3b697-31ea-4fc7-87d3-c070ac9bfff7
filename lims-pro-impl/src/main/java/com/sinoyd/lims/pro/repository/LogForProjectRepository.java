package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoLogForProject;

import java.util.List;


/**
 * LogForProject数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
public interface LogForProjectRepository extends IBaseJpaPhysicalDeleteRepository<DtoLogForProject, String> {

    List<DtoLogForProject> findByObjectId(String objectId);

}