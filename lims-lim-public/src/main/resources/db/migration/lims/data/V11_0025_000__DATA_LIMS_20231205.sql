-- 首页报告编制(新)待办统计
delete from tb_base_job where id = '0aee0edd-82e1-4657-8523-1434e9645245';
INSERT INTO `tb_base_job` (`id`, `jobName`, `jobGroup`, `invokeTarget`, `cronExpression`, `misfirePolicy`,
                           `isConcurrent`, `status`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`,
                           `domainId`, `modifier`, `modifyDate`)
VALUES ('0aee0edd-82e1-4657-8523-1434e9645245', '首页报告编制(新)待办统计', 'HOME',
        'reportEditNewStatisticsTask.statisticsProjectNums', '0 0/5 * * * ?', 3, b'0', 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-27 14:01:50');

-- 硫化物碘量法分析记录表模板配置
delete from TB_LIM_ReportConfig where id = '6eb0d739-0fcb-492e-8e6c-500f73aeaa5a';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('6eb0d739-0fcb-492e-8e6c-500f73aeaa5a', 1, 'WorkSheetLHWDLF', '硫化物碘量法分析记录表_模板.xlsx', 'WorkSheet/硫化物碘量法分析记录表_模板.xlsx', 'output/WorkSheet/硫化物碘量法分析记录表.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{\"originalRecordType\" : \"lineExtendBySampleOnly\",  \"workSheetDataSourceType\" : \"commonWorkSheetDataSourceImpl\" }', 0, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-04-23 11:31:14', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-04-23 11:31:14', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLHWDLF', b'0', '', '', '', '', '');

-- 质控任务统计（盲样考核）模板配置
delete from TB_LIM_ReportConfig where id = '0a80a85f-c9b1-40b7-966c-58c8721150fc';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('0a80a85f-c9b1-40b7-966c-58c8721150fc', 1, 'QcProjectBlindExam', '质控任务统计（盲样考核）.xlsx',
        '/LIMReportForms/质控任务统计（盲样考核）.xlsx', 'output/LIMReportForms/质控任务统计（盲样考核）.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.QcProjectBlindExamExportService', '{\"sort\":\"orderNum-\"}', '', 0, 1,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 09:44:56',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 09:44:56',
        'com.sinoyd.lims.pro.criteria.QCProjectCriteria', 'LIMReportForms', 'export/QcProjectBlindExam', b'0', '', '',
        '', '', '');

-- 质控任务统计（仪器比对）模板配置
delete from TB_LIM_ReportConfig where id = '11220910-2650-48bf-9c28-bde79efe4266';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('11220910-2650-48bf-9c28-bde79efe4266', 1, 'QcProjectInstrumentCompare', '质控任务统计（仪器比对）.xlsx',
        '/LIMReportForms/质控任务统计（仪器比对）.xlsx', 'output/LIMReportForms/质控任务统计（仪器比对）.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.QcProjectInstrumentCompareExportService', '{\"sort\":\"orderNum-\"}',
        '', 0, 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-07-03 09:52:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-07-03 14:19:52', 'com.sinoyd.lims.pro.criteria.QCProjectCriteria', 'LIMReportForms',
        'QcProjectInstrumentCompare', b'0', '', '', '', '', '');

-- 质控任务统计（加标样考核）模板配置
delete from TB_LIM_ReportConfig where id = 'c6b960a3-b0db-4beb-ba55-75ee67e95b7c';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('c6b960a3-b0db-4beb-ba55-75ee67e95b7c', 1, 'QcProjectMarkExam', '质控任务统计（加标样考核）.xlsx',
        '/LIMReportForms/质控任务统计（加标样考核）.xlsx', 'output/LIMReportForms/质控任务统计（加标样考核）.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.QcProjectMarkExamExportService', '{\"sort\":\"orderNum-\"}', '', 0, 1,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 09:24:06',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 09:24:06',
        'com.sinoyd.lims.pro.criteria.QCProjectCriteria', 'LIMReportForms', 'export/QcProjectMarkExam', b'0', '', '',
        '', '', '');

-- 质控任务统计（人员比对）模板配置
delete from TB_LIM_ReportConfig where id = 'c760a3de-f4dc-4e35-9c4d-175f2e7884c4';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('c760a3de-f4dc-4e35-9c4d-175f2e7884c4', 1, 'QcProjectPersonCompare', '质控任务统计（人员比对）.xlsx',
        '/LIMReportForms/质控任务统计（人员比对）.xlsx', 'output/LIMReportForms/质控任务统计（人员比对）.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.QcProjectPersonCompareExportService', '{\"sort\":\"orderNum-\"}', '', 0,
        1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 09:48:32',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 14:10:09',
        'com.sinoyd.lims.pro.criteria.QCProjectCriteria', 'LIMReportForms', 'QcProjectPersonCompare', b'0', '', '', '',
        '', '');

-- 质控任务统计（标样考核）模板配置
delete from TB_LIM_ReportConfig where id = 'eb924106-a28f-422b-ba10-466704b88545';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('eb924106-a28f-422b-ba10-466704b88545', 1, 'QcProjectStandardExam', '质控任务统计（标样考核）.xlsx',
        '/LIMReportForms/质控任务统计（标样考核）.xlsx', 'output/LIMReportForms/质控任务统计（标样考核）.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.QcProjectStandardExamExportService', '{\"sort\":\"orderNum-\"}', '', 0,
        1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 08:42:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 08:42:16',
        'com.sinoyd.lims.pro.criteria.QCProjectCriteria', 'LIMReportForms', 'export/QcProjectStandardExam', b'0', '',
        '', '', '', '');

-- 年度质控考核统计表模板配置
delete from TB_LIM_ReportConfig where id = 'ee666c5a-4a50-4a20-b351-2972f40d69e5';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('ee666c5a-4a50-4a20-b351-2972f40d69e5', 1, 'QcProjectAnnualStatistical', '年度质控考核统计表.xlsx',
        '/LIMReportForms/年度质控考核统计表.xlsx', 'output/LIMReportForms/年度质控考核统计表.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.QcProjectAnnualStatisticalExportService', '{\"sort\":\"orderNum-\"}',
        '', 0, 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-07-03 09:59:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-07-03 09:59:05', 'com.sinoyd.lims.pro.criteria.QCProjectCriteria', 'LIMReportForms',
        'export/QcProjectAnnualStatistical', b'0', '', '', '', '', '');

-- 质控任务统计（手工、仪器比对）模板配置
delete from TB_LIM_ReportConfig where id = 'f378aa4b-0d48-4f94-8670-4b22ce9f0c05';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('f378aa4b-0d48-4f94-8670-4b22ce9f0c05', 1, 'QcProjectHandPersonCompare', '质控任务统计（手工、仪器比对）.xlsx',
        '/LIMReportForms/质控任务统计（手工、仪器比对）.xlsx', 'output/LIMReportForms/质控任务统计（手工、仪器比对）.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.QcProjectPersonCompareExportService', '{\"sort\":\"orderNum-\"}', '', 0,
        1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 09:56:34',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-03 14:22:39',
        'com.sinoyd.lims.pro.criteria.QCProjectCriteria', 'LIMReportForms', 'QcProjectHandPersonCompare', b'0', '', '',
        '', '', '');


-- 添加上岗证(人员)报表导出配置
delete from TB_LIM_ReportConfig where id = 'c031154d-ba9e-4043-8c4c-5852fb938890';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('c031154d-ba9e-4043-8c4c-5852fb938890', 1, 'PersonCertByPerson', '上岗证(人员证书).xlsx',
        'LIMReportForms/上岗证(人员证书).xlsx', 'output/LIMReportForms/上岗证(人员证书).xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.CertByPersonExportService', '{\"sort\":\"orderNum-\"}', '', 0, 1, '',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-11 16:00:06',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-11 16:01:20',
        'com.sinoyd.lims.lim.criteria.PersonCriteria', 'LIMReportForms', 'export/PersonCertByPerson', b'0', '', '', '',
        '', '');

-- 添加上岗证(分析项目)报表导出配置
delete from TB_LIM_ReportConfig where id = '9b1fb895-1fcf-11ee-9975-424e45cd431a';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('9b1fb895-1fcf-11ee-9975-424e45cd431a', 1, 'PersonCertByAnaItem', '上岗证(分析项目).xlsx',
        'LIMReportForms/上岗证(分析项目).xlsx', 'output/LIMReportForms/上岗证(分析项目).xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.CertByAnaItemExportService', '{\"sort\":\"orderNum-\"}', '', 0, 1, '',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-11 16:00:06',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-11 16:01:20',
        'com.sinoyd.lims.lim.criteria.PersonCriteria', 'LIMReportForms', 'export/PersonCertByAnaItem', b'0', '', '', '',
        '', '');


-- 容器清单报表模板配置
delete from TB_LIM_ReportConfig where id = '140f68e4-fcfa-46cd-9faa-4d119866a42b';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('140f68e4-fcfa-46cd-9faa-4d119866a42b', 1, 'ContainerList', '容器清单.xls', '', 'output/LIMReportForms/容器清单.xls',
        'application/excel', '', '{\"sort\":\"orderNum-\"}', '', 0, 1, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-08 22:39:46', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-08 22:39:46', '', 'LIMReportForms', 'ContainerList', b'0', '', '',
        '', '', '');

-- 现场任务交接单报表模板配置
delete from TB_LIM_ReportConfig where id = '4b512e79-64f5-4033-a45a-22044a953983';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('4b512e79-64f5-4033-a45a-22044a953983', 1, 'SampleReceiveXC', '交接单（现场任务）.xls', 'LIMReportForms/交接单（现场任务）.xls',
        'output/LIMReportForms/交接单.xlsx', 'application/excel', '', '{\"sort\":\"orderNum-\"}', '', 0, 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-08 22:35:11',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-08 22:36:11', '',
        'LIMReportForms', 'SampleReceiveXC', b'0', '', '', '', '', '');


-- 试剂配置导出报表模板配置
delete from TB_LIM_ReportConfig where id = 'ba8e7106-c54e-413f-be35-6e69569277b5';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('ba8e7106-c54e-413f-be35-6e69569277b5', 1, 'ReagentConfig', '一般试剂配制记录.xlsx', '/LIMReportForms/一般试剂配制记录.xlsx',
        'output/LIMReportForms/一般试剂配制记录.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.ReagentConfigExportService', '{\"sort\":\"orderNum-\"}', '', 0, 1, '',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-01 16:30:46',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-08-01 16:30:46',
        'com.sinoyd.lims.lim.criteria.AnalyzeMethodReagentConfigCriteria', 'LIMReportForms', 'export/ReagentConfig',
        b'0', '', '', '', '', '');

-- 添加标样标签报表模板配置
delete from TB_LIM_ReportConfig where id = 'e111ad52-4a79-4637-ad84-27f4761e2b7e';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName)
VALUES ('e111ad52-4a79-4637-ad84-27f4761e2b7e', 1, 'StandardLabel', '标样标签.xlsx', 'LIMReportForms/标样标签.xlsx',
        'output/LIMReportForms/标样标签.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.limReportForms.StandardLabelService', '', '', 0, 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-05 14:56:43',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-05 14:56:43', '',
        'LIMReportForms', 'StandardLabel', b'0', '', '', '', '', '');


-- 修改质控任务统计加标考核报表模板配置
update TB_LIM_ReportConfig set strUrl = 'QcProjectMarkExam' where reportCode = 'QcProjectMarkExam';

-- 修改质控任务统计标样考核报表模板配置
update TB_LIM_ReportConfig set strUrl = 'QcProjectStandardExam' where reportCode = 'QcProjectStandardExam';

-- 修改质控任务统计盲样考核报表模板配置
update TB_LIM_ReportConfig set strUrl = 'QcProjectBlindExam' where reportCode = 'QcProjectBlindExam';

-- 修改一般试剂配置记录表报表模板配置
update TB_LIM_ReportConfig set dataMethod = 'com.sinoyd.lims.lim.criteria.AnalyzeMethodExportCriteria' where reportCode = 'ReagentConfig';

-- 送样类报告模板配置
delete from TB_LIM_ReportConfig where id = '94218570-513e-4347-9af7-1534eaf36b78';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('94218570-513e-4347-9af7-1534eaf36b78', 1, 'SyStd', '标准版送样类报告.doc', 'Report/标准版报告.doc',
        'output/Report/送样类报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-05-30 13:53:45',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-05-30 14:04:16',
        'reportId,sortId', 'Report', 'SyStd', b'0', '', '', '', '', '');

delete from TB_LIM_ReportConfig where id = '328424ca-fd81-420d-9134-a578e57d0352';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName)
VALUES ('328424ca-fd81-420d-9134-a578e57d0352', 1, 'ComparisonStatistic', '废水比对结果统计表.xlsx', '', '废水比对结果统计表.xlsx',
        'application/excel', 'com.sinoyd.lims.report.service.statisticReport.ComparisonStatisticService', '', '', 0, 1,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-21 08:51:00',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-21 11:14:09',
        'com.sinoyd.lims.monitor.criteria.StatisticCriteria', 'LIMReportForms', 'easyPoi/ComparisonStatistic', b'0', '',
        '', '', '', '');

-- 仪器维护报表模板配置
delete from TB_LIM_ReportConfig where id = '7d4df1a8-eea4-415f-be9a-8d2b325698fb';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType,
                                method, params, pageConfig, orderNum, bizType, remark, isDeleted,
                                orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod,
                                typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName)
VALUES ('7d4df1a8-eea4-415f-be9a-8d2b325698fb', 1, 'InstrumentMaintainRecord', '仪器维护记录.xlsx',
        'LIMReportForms/仪器维护记录.xlsx', 'output/LIMReportForms/仪器维护记录.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.exports.InstrumentMaintainRecordPageService', '{\"sort\":\"startTime-\"}', '',
        0, 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-28 16:05:41',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-28 16:05:41',
        'com.sinoyd.lims.lim.criteria.InstrumentMaintainRecordCriteria', 'LIMReportForms',
        'export/InstrumentMaintainRecord', b'0', '', '', '', '', '');

update TB_LIM_ReportConfig set strUrl = 'InstrumentMaintainRecord' where id = '7d4df1a8-eea4-415f-be9a-8d2b325698fb';

-- 仪器设备使用记录表模板配置调整
update TB_LIM_ReportConfig
set method = 'com.sinoyd.lims.report.service.exports.InstrumentUseRecordPageReportService',
    strUrl = 'InstrumentUseRecord'
where reportCode = 'InstrumentUseRecord'
  and isDeleted = 0;

-- 有组织报告模板配置
delete from TB_LIM_ReportConfig where id = '56b3f7b9-bee9-4419-a986-c4cf5e2812b7';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName)
VALUES ('56b3f7b9-bee9-4419-a986-c4cf5e2812b7', 1, 'OrgGasStd', '标准版有组织气报告.doc', '/Report/标准版报告.doc',
        'output/Report/有组织气报告.doc', 'application/word',
        'com.sinoyd.lims.wordreport.service.wordReport.GenerateWordReportService', '{\"sort\":\"orderNum-\"}', '', 0, 4,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-10-19 08:40:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-10-19 08:40:10',
        'reportId,sortId', 'Report', 'OrgGasStd', b'0', '', '', '', '', '');

delete from TB_LIM_RecordConfig where id = 'b091fffb-1fd4-4d94-b57e-c446aa8e8bfe';
INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId, remark, isDeleted, orgId,
                                creator, createDate, domainId, modifier, modifyDate)
VALUES ('b091fffb-1fd4-4d94-b57e-c446aa8e8bfe', '有组织报告', 3, '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-10-19 08:44:28', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-10-19 08:44:28');

delete from TB_LIM_ReportConfig where id = 'ba8f43c9-6034-490b-a8a2-433e74b8664e';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('ba8f43c9-6034-490b-a8a2-433e74b8664e', 1, 'ProjectStatistics', 'SINOYD-LIMS-ZY-23-01项目统计报表.xlsx',
        'LIMReportForms/SINOYD-LIMS-ZY-23-01项目统计报表.xlsx', 'output/LIMReportForms/SINOYD-LIMS-ZY-23-01项目统计报表.xlsx',
        'application/excel', 'com.sinoyd.lims.report.service.exports.ProjectStatisticsExportService', '{\"sort\":\"\"}',
        '', 0, 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-10-31 09:37:43', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-10-31 17:13:15', 'com.sinoyd.lims.pro.criteria.ProjectCriteria', 'LIMReportForms',
        'export/ProjectStatistics', b'0', '', '', '', '', '');

delete from TB_LIM_ReportConfig where id = '6d2b8c93-c4f3-42da-bfad-200338064849';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('6d2b8c93-c4f3-42da-bfad-200338064849', 1, 'TRSamplingRecord', 'SINOYD-LIMS-CY-16-02 土壤采样记录单.xls', 'SamplingRecords/SINOYD-LIMS-CY-16-02 土壤采样记录单.xls', 'output/SamplingRecords/SINOYD-LIMS-CY-16-02 土壤采样记录单.xls', 'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.STRSamplingRecordService', '', '', 0, 3, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-06 09:09:45', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-06 09:09:45', '', 'Sampling', 'TRSamplingRecord', b'0', '', '', '', '', '');
delete from TB_LIM_ReportConfig where id = 'a87a7836-40cd-4608-95a5-67ea3c24ccdb';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('a87a7836-40cd-4608-95a5-67ea3c24ccdb', 1, 'DXSSamplingRecord', 'SINOYD-LIMS-CY-05-02 地下水采样和分析记录单.xls', 'SamplingRecords/SINOYD-LIMS-CY-05-02 地下水采样和分析记录单.xls', 'output/SamplingRecords/SINOYD-LIMS-CY-05-02 地下水采样和分析记录单.xls', 'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.STRSamplingRecordService', '', '', 0, 3, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-06 09:07:45', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-06 09:07:45', '', 'Sampling', 'DXSSamplingRecord', b'0', '', '', '', '', '');
delete from TB_LIM_ReportConfig where id = '30e099ad-a9dd-4166-82b0-09cfb93c5cd2';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('30e099ad-a9dd-4166-82b0-09cfb93c5cd2', 1, 'DBSSamplingRecord', 'SINOYD-LIMS-CY-04-02 地表水采样和分析记录单.xls', 'SamplingRecords/SINOYD-LIMS-CY-04-02 地表水采样和分析记录单.xls', 'output/SamplingRecords/SINOYD-LIMS-CY-04-02 地表水采样和分析记录单.xls', 'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.STRSamplingRecordService', '', '', 0, 3, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-06 09:04:38', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-06 09:04:38', '', 'Sampling', 'DBSSamplingRecord', b'0', '', '', '', '', '');

delete from TB_LIM_ReportConfig where id = '45b37cb1-916e-46ab-a401-2e1cefb47c79';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('45b37cb1-916e-46ab-a401-2e1cefb47c79', 1, 'DQHJCYSamplingRecord', 'SINOYD-LIMS-CY-01-02 大气环境采样记录单（多样品）.xls', 'SamplingRecords/SINOYD-LIMS-CY-01-02 大气环境采样记录单（多样品）.xls', 'output/SamplingRecords/SINOYD-LIMS-CY-01-02 大气环境采样记录单（多样品）.xls', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.DQHJCYSamplingRecordService', '', '', 0, 3, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-13 17:07:57', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-13 17:07:57', '', 'Sampling', 'DQHJCYSamplingRecord', b'0', '', '', '', '', '');

-- 样品流转单模板配置
delete from TB_LIM_ReportConfig where id = '60918d00-7b37-493c-80a3-f905e168a1a1';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType,
                                method, params, pageConfig, orderNum, bizType, remark, isDeleted,
                                orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod,
                                typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName)
VALUES ('60918d00-7b37-493c-80a3-f905e168a1a1', 1, 'SampleCirculation', '样品流转单.xls', 'LIMReportForms/样品流转单.xls',
        'output/LIMReportForms/样品流转单.xls', 'application/excel',
        'com.sinoyd.lims.report.service.limReportForms.SampleCirculationService', '{ \"isPageSig\": true}', '', 0, 1,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-21 13:17:26',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-09-28 14:46:29', '',
        'LIMReportForms', 'SampleCirculation', b'0', '', '', '', '', '');

-- 原始记录单数据参数导出
delete from TB_LIM_ReportConfig where id = 'f6d77ffe-76fb-4c70-86bb-3acaa1574443';
INSERT INTO TB_LIM_ReportConfig
(id, `type`, reportCode, templateName, template, outputName, returnType, `method`, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName)
VALUES('f6d77ffe-76fb-4c70-86bb-3acaa1574443', 1, 'DataParamsConfig', '1.xls', '', '', '', 'com.sinoyd.lims.report.service.exports.DataParamsConfigPoiExportServiceImpl', '', '', 0, 1, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-10-16 13:50:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-10-17 11:14:34', 'com.sinoyd.lims.lim.criteria.DataParamsConfigExportCriteria', 'LIMReportForms', '/', 0, '', '', '', '', '');

-- 评价标准模板配置
delete from TB_LIM_ReportConfig where id = 'c09b715e-1248-49e1-9edf-2aee03bc8009';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName)
VALUES ('c09b715e-1248-49e1-9edf-2aee03bc8009', 1, 'EvaluationCriteriaExport', '评价标准.xls', 'LIMReportForms/评价标准.xls',
        'output/LIMReportForms/评价标准.xls', 'application/excel',
        'com.sinoyd.lims.report.service.exports.EvaluationCriteriaExportService', '{\"sort\":\"name\"}', '', 0, 1, '',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-23 11:19:51',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-23 13:03:27',
        'com.sinoyd.base.criteria.EvaluationCriteriaCriteria', 'LIMReportForms', 'export/EvaluationCriteriaExport',
        b'0', '', '', '', '', '');

delete from TB_LIM_ReportConfig where id = '0de3b7c0-3989-4e93-ac27-98a862c94e49';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('0de3b7c0-3989-4e93-ac27-98a862c94e49', 1, 'SamplingContainer', '采样容器准备清单.xlsx', 'LIMReportForms/采样容器准备清单.xlsx', 'output/LIMReportForms/采样容器准备清单.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.SamplingContainerExportService', '{\"sort\":\"sampleId-\"}', '', 0, 1, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-27 15:43:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-11-27 15:46:41', 'com.sinoyd.lims.pro.criteria.SampleGroupRecordCriteria', 'LIMReportForms', 'export/SamplingContainer', b'0', '', '', '', '', '');

delete from TB_LIM_ReportConfig where id = '8035b129-e0ff-4f9c-9471-4d4ac065f83a';
INSERT INTO TB_LIM_ReportConfig(`id`, `type`, `reportCode`, `templateName`, `template`, `outputName`, `returnType`, `method`, `params`, `pageConfig`, `orderNum`, `bizType`, `remark`, `isDeleted`, `orgId`, `creator`, `createDate`, `domainId`, `modifier`, `modifyDate`, `dataMethod`, `typeCode`, `strUrl`, `isDefineFileName`, `defineFileName`, `beanName`, `versionNum`, `controlNum`, `reportName`, `validate`, `usageNum`) VALUES ('8035b129-e0ff-4f9c-9471-4d4ac065f83a', 1, 'DYDZTCSamplingRecord', 'SINOYD-LIMS-CY-24-02 打印单粘贴处.xlsx', '/Sampling/SINOYD-LIMS-CY-24-02 打印单粘贴处.xlsx', 'output/SamplingRecords/SINOYD-LIMS-CY-24-02 打印单粘贴处.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.tz.samplingReport.TzCommonRecordService', '', 'pageByProject', 0, 3, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-01 11:09:33', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-12-01 11:09:33', '', 'Sampling', 'DYDZTCSamplingRecord', b'0', '', '', '', '', '', 0, NULL);
UPDATE TB_LIM_ReportConfig set method = 'com.sinoyd.lims.report.service.exports.ConsumableStorageExportService' where reportCode = 'ConsumableInventory';

