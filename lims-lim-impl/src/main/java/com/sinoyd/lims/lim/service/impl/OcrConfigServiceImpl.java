package com.sinoyd.lims.lim.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.customer.DtoOcrDataContainer;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfig;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParam;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParamData;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigRecord;
import com.sinoyd.lims.lim.repository.lims.OcrConfigParamDataRepository;
import com.sinoyd.lims.lim.repository.lims.OcrConfigParamRepository;
import com.sinoyd.lims.lim.repository.lims.OcrConfigRecordRepository;
import com.sinoyd.lims.lim.repository.lims.OcrConfigRepository;
import com.sinoyd.lims.lim.service.OcrConfigService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;


/**
 * ocr对象接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@Service
@Slf4j
public class OcrConfigServiceImpl extends BaseJpaServiceImpl<DtoOcrConfig, String, OcrConfigRepository> implements OcrConfigService {

    private OcrConfigParamRepository ocrConfigParamRepository;

    private OcrConfigParamDataRepository ocrConfigParamDataRepository;

    private OcrConfigRecordRepository ocrConfigRecordRepository;

    private FilePathConfig filePathConfig;

    @Value("${ocr.url:http://192.168.30.21:19990/api/call-agent/2?stream=true}")
    private String ocrUrl;

    @Value("${ocr.prompt:角色：你是位资深的光学数据识别专家,任务：你需要识别图片内容并将内容按照指定的json格式输出,要求,1.禁止将中文转成英文,禁止将单位转成中文,2.数据中的*需要去掉,3.数据不需要单位,4.采样日期和采样时间不需要分开。指定的json格式示例如下{参数名称1:参数值1,参数名称2:参数值2},去掉不必要的换行符,生成的json文本要能支持直接转换为java中的JSONObject,回答不需要其余说明,只需要输出json文本。}")
    private String ocrPrompt;


    @Override
    public void findByPage(PageBean<DtoOcrConfig> page, BaseCriteria criteria) {
        page.setSelect("select c");
        page.setEntityName("DtoOcrConfig c ");
        super.findByPage(page, criteria);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        //级联删除对象参数
        List<DtoOcrConfigParam> dtoOcrConfigParamList = ocrConfigParamRepository.findAllByConfigIdIn((List<String>) ids);
        ocrConfigParamRepository.delete(dtoOcrConfigParamList);
        return super.logicDeleteById(ids);
    }

    @Override
    public DtoOcrConfig findAttachPath(String id) {
        return repository.findOne(id);
    }

    @Override
    public List<DtoOcrConfig> findByCode(String instrumentCode) {
        return repository.findByWwInstrumentCode(instrumentCode);
    }

    @Override
    @Transactional
    public DtoOcrDataContainer ocrRecognize(DtoOcrConfigRecord ocrConfigRecord) {
        //存储容器
        DtoOcrDataContainer container = new DtoOcrDataContainer();
//        List<DtoOcrConfigParamData> initDatalist = new ArrayList<>();
//        //待检测参数
//        List<DtoOcrConfigParam> paramList = ocrConfigParamRepository.findAllByConfigId(ocrConfigRecord.getConfigId())
//                .stream().sorted(Comparator.comparing(DtoOcrConfigParam::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
//        //提示词
//        DtoOcrConfig ocrConfig = repository.findOne(ocrConfigRecord.getConfigId());
//        //ocr server 解析
//        //postOcrServer(ocrConfigRecord.getFilePath(), container, ocrConfig.getPrompt());
//        //数据转换
//        parseOriginData(ocrConfigRecord, initDatalist, paramList, container);
//        //初始数据保存
//        ocrConfigRecordRepository.save(ocrConfigRecord);
//        ocrConfigParamDataRepository.save(initDatalist);
//        container.setDataList(initDatalist);
        return container;
    }

    /**
     * 数据转换
     *
     * @param ocrConfigRecord 参数容器
     * @param initDatalist    存储容器
     * @param paramList       待检测参数
     * @param container       解析对象
     */
    private void parseOriginData(DtoOcrConfigRecord ocrConfigRecord, List<DtoOcrConfigParamData> initDatalist, List<DtoOcrConfigParam> paramList, DtoOcrDataContainer container) {
        ocrConfigRecord.setOriginData(container.getOcrParse());
        ocrConfigRecord.setAiAnswer(container.getAiAnswer());
        JSONObject object = JSONObject.parseObject(container.getAiAnswer());
        for (DtoOcrConfigParam dtoOcrConfigParam : paramList) {
            DtoOcrConfigParamData configParamData = new DtoOcrConfigParamData();
            configParamData.setRecordId(ocrConfigRecord.getId());
            configParamData.setConfigParamId(dtoOcrConfigParam.getId());
            configParamData.setDimension(dtoOcrConfigParam.getDimension());
            configParamData.setParamName(dtoOcrConfigParam.getParamName());
            String saveValue = "";
            if (object.containsKey(dtoOcrConfigParam.getParamName()) && object.get(dtoOcrConfigParam.getParamName()) != null) {
                saveValue = object.get(dtoOcrConfigParam.getParamName()).toString();
            }
            configParamData.setOrgId(ocrConfigRecord.getOrgId());
            configParamData.setCreator(ocrConfigRecord.getCreator());
            configParamData.setSaveValue(saveValue);
            initDatalist.add(configParamData);
        }
    }

    /**
     * 调用ocr服务接口
     */
//    private void postOcrServer(String filePath, DtoOcrDataContainer container, String prompt) {
//        //调用ocr接口
//        HttpHeaders headers = new HttpHeaders();
////        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
//        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
//        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
//        map.add("prompt", StringUtil.isNotEmpty(prompt) ? prompt : ocrPrompt);
//        File file = new File(filePathConfig.getFilePath() + filePath);
//        FileSystemResource fileResource = new FileSystemResource(file);
//        map.add("image_file", fileResource);
//        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(map, headers);
//        String json = createCustomerRestTemplate().postForEntity(ocrUrl, requestEntity, String.class).getBody();
//        log.info("ai识别结果：" + json);
//        try {
//            JSONObject originObject = JSONObject.parseObject(json).getJSONObject("step_results");
//            container.setOcrParse(originObject.getJSONObject("ocr_parse").toString());
//            container.setAiAnswer(originObject.getJSONObject("refine_ocr").toJSONString());
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            throw new BaseException("AI识别异常");
//        }
//    }
    @Override
    @Transactional
    public void callStreamApi(DtoOcrConfigRecord ocrConfigRecord, Consumer<String> onData) {
        //存储容器
        DtoOcrDataContainer container = new DtoOcrDataContainer();
        List<DtoOcrConfigParamData> initDatalist = new ArrayList<>();
        //待检测参数
        List<DtoOcrConfigParam> paramList = ocrConfigParamRepository.findAllByConfigId(ocrConfigRecord.getConfigId())
                .stream().sorted(Comparator.comparing(DtoOcrConfigParam::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
        //提示词
        DtoOcrConfig ocrConfig = repository.findOne(ocrConfigRecord.getConfigId());

        File file = new File(filePathConfig.getFilePath() + ocrConfigRecord.getFilePath());
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("prompt", StringUtil.isNotEmpty(ocrConfig.getPrompt()) ? ocrConfig.getPrompt() : ocrPrompt)
                .addFormDataPart("image_file", file.getName(),
                        RequestBody.create(MediaType.parse("application/octet-stream"), file))
                .build();
        Request request = new Request.Builder()
                .url(ocrUrl)
                .post(body)
                .build();
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new RuntimeException("API call failed: " + response.code());
            }
            try (ResponseBody responseBody = response.body();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(responseBody.byteStream(),
                         StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().isEmpty() && !line.contains("id:")) {
                        String json = line.replace("data: ", "");
                        try {
                            Boolean done = JSONObject.parseObject(json).getBoolean("done");
                            if (done) {
                                JSONObject originObject = JSONObject.parseObject(json).getJSONObject("workflow_result");
                                container.setAiAnswer(originObject.toJSONString());
                                container.setRecordId(ocrConfigRecord.getId());
                                //数据转换
                                parseOriginData(ocrConfigRecord, initDatalist, paramList, container);
                                //初始数据保存
                                ocrConfigRecordRepository.save(ocrConfigRecord);
                                ocrConfigParamDataRepository.save(initDatalist);
                                //存储容器
                                container.setDataList(initDatalist);
                                String endStr = JsonUtil.toJson(container);
                                // 立即处理每一行数据，不等待所有数据读取完成
                                onData.accept(endStr);
                            } else {
                                // 立即处理每一行数据，不等待所有数据读取完成
                                onData.accept(json);
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                            throw new BaseException("AI识别异常");
                        }
                        // 添加小延迟，确保数据能够及时发送
                        try {
                            Thread.sleep(10);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Stream API call failed", e);
            throw new RuntimeException("Stream API call failed", e);
        }
    }

    /**
     * 个性化RestTemplate，设置超时时间
     *
     * @return RestTemplate
     */
    public RestTemplate createCustomerRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        // 创建 HttpClient
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(new PoolingHttpClientConnectionManager())
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setConnectTimeout(180000) // 设置连接超时时间（单位：毫秒）
                        .setSocketTimeout(180000)  // 设置读取超时时间（单位：毫秒）
                        .build())
                .build();
        // 设置请求工厂
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        restTemplate.setRequestFactory(requestFactory);
        restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }

    @Autowired
    public void setOcrConfigParamRepository(OcrConfigParamRepository ocrConfigParamRepository) {
        this.ocrConfigParamRepository = ocrConfigParamRepository;
    }


    @Autowired
    public void setOcrConfigParamDataRepository(OcrConfigParamDataRepository ocrConfigParamDataRepository) {
        this.ocrConfigParamDataRepository = ocrConfigParamDataRepository;
    }

    @Autowired
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    public void setOcrConfigRecordRepository(OcrConfigRecordRepository ocrConfigRecordRepository) {
        this.ocrConfigRecordRepository = ocrConfigRecordRepository;
    }
}
