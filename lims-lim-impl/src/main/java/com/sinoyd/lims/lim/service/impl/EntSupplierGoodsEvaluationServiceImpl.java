package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoEntSupplierGoodsEvaluation;
import com.sinoyd.lims.lim.dto.lims.DtoEntSupplierService;
import com.sinoyd.lims.lim.repository.lims.EntSupplierGoodsEvaluationRepository;
import com.sinoyd.lims.lim.repository.lims.EntSupplierServiceRepository;
import com.sinoyd.lims.lim.service.EntSupplierGoodsEvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 评价信息-商品评价
 * <AUTHOR>
 * @version V1.0.0 2019/3/8
 * @since V100R001
 */
@Service
public class EntSupplierGoodsEvaluationServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoEntSupplierGoodsEvaluation, String, EntSupplierGoodsEvaluationRepository>
        implements EntSupplierGoodsEvaluationService {

    @Autowired
    private EntSupplierServiceRepository entSupplierServiceRepository;

    /***
     * 分页查询商品评价
     *
     * @param page
     * @param criteria
     */
    @Override
    public void findByPage(PageBean<DtoEntSupplierGoodsEvaluation> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoEntSupplierGoodsEvaluation p");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select p");

        super.findByPage(page, criteria);

        List<DtoEntSupplierGoodsEvaluation> list = page.getData();

        if (StringUtil.isNotEmpty(list)) {
            DtoEntSupplierGoodsEvaluation cc = list.get(0);
            String entId = cc.getEntId();
            List<DtoEntSupplierService> goodsList = entSupplierServiceRepository.findByEntId(entId);
            for (DtoEntSupplierGoodsEvaluation item : list) {
                if (StringUtils.isNotNullAndEmpty(item.getGoodsId()) && !UUIDHelper.GUID_EMPTY.equals(item.getGoodsId())) {
                    Optional<DtoEntSupplierService> ap = goodsList.stream().filter(p -> p.getId().equals(item.getGoodsId())).findFirst();
                    if (ap.isPresent()) {
                        DtoEntSupplierService goods = ap.get();
                        item.setGoodsName(goods.getGoodsName());
                    }
                }
            }
        }

        page.setData(list);// 重新封装到pageBean返回到上层

    }
}