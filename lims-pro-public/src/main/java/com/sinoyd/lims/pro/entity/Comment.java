package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * Comment实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Comment")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Comment implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Comment() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 冗余用于回复
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("冗余用于回复")
	private String parentId;
    
    /**
    * 评论关联id（项目id、我的审批、检测单、报告id）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("评论关联id（项目id、我的审批、检测单、报告id）")
	private String objectId;
    
    /**
    * 对象类型（枚举EnumCommentObjectType：1.项目 2.我的审批 3.检测单 4.报告）
    */
    @Column(nullable=false)
    @ApiModelProperty("对象类型（枚举EnumCommentObjectType：1.项目 2.我的审批 3.检测单 4.报告）")
	private Integer objectType;
    
    /**
    * 评论人id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("评论人id（Guid）")
	private String commentPersonId;
    
    /**
    * 评论人名称
    */
    @Column(length=50)
    @ApiModelProperty("评论人名称")
    @Length(message = "评论人名称{validation.message.length}", max = 50)
	private String commentPersonName;
    
    /**
    * 评论时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("评论时间")
	private Date commentTime;
    
    /**
    * 评论内容
    */
    @ApiModelProperty("评论内容")
	private String comment;
    
    /**
    * 评论类型（枚举EnumCommentType：1、留言）
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("评论类型（枚举EnumCommentType：1、留言）")
	private Integer commentType;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }