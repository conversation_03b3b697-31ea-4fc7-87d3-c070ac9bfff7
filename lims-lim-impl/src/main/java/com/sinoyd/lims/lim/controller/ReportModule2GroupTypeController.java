package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.customer.DtoModuleGroupTypeTemp;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule2GroupType;
import com.sinoyd.lims.lim.service.ReportModule2GroupTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ReportModule2GroupType 服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Api(tags = "示例: ReportModule2GroupType 服务")
@RestController
@RequestMapping("api/lim/reportModule2GroupType")
@Validated
public class ReportModule2GroupTypeController extends BaseJpaController<DtoReportModule2GroupType, String, ReportModule2GroupTypeService> {

    /**
     * 新增 ReportModule2GroupType
     *
     * @param moduleGroupTypeTemp 请求实体
     * @return RestResponse<ReportModule2GroupType>
     */
    @ApiOperation(value = "新增ReportModule2GroupType", notes = "新增ReportModule2GroupType")
    @PostMapping
    public RestResponse<List<DtoReportModule2GroupType>> create(@Validated @RequestBody DtoModuleGroupTypeTemp moduleGroupTypeTemp) {
        RestResponse<List<DtoReportModule2GroupType>> restResponse = new RestResponse<>();
        restResponse.setData(service.saveGroupType(moduleGroupTypeTemp));
        return restResponse;
    }

    /**
     * 修改 ReportModule2GroupType
     *
     * @param moduleGroupTypeTemp 实体列表
     * @return RestResponse<DtoReportConfig2Module>
     */
    @ApiOperation(value = "修改ReportModule2GroupType", notes = "修改ReportModule2GroupType")
    @PutMapping
    public RestResponse<List<DtoReportModule2GroupType>> update(@Validated @RequestBody DtoModuleGroupTypeTemp moduleGroupTypeTemp) {
        RestResponse<List<DtoReportModule2GroupType>> restResponse = new RestResponse<>();
        restResponse.setData(service.updateGroupType(moduleGroupTypeTemp));
        return restResponse;
    }
}