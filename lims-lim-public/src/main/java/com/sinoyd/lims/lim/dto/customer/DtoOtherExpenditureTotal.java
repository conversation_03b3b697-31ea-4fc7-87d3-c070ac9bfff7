package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.lims.lim.dto.lims.DtoOtherExpenditure;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @Data
 @DynamicInsert
 public  class DtoOtherExpenditureTotal {

	@Transient 
	private BigDecimal totalAmount;

    @Transient
    private List<DtoOtherExpenditure> otherExpenditureList;

 }