package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.test;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTestTemp;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**g
 * <AUTHOR>
 */
@Component(IFileNameConstant.SchemeSynchronizationKey.DELETED_TEST)
public class DeletedTestStrategy extends AbsTestStrategy {

    /**
     * 调整测试项目方案
     *
     * @param samplingFrequencyTestTempList 修改项目内容
     */
    @Override
    public void synchronizationTest(List<DtoSamplingFrequencyTestTemp> samplingFrequencyTestTempList) {
        Map<String, List<DtoSamplingFrequencyTestTemp>> frequencyMap = samplingFrequencyTestTempList.stream()
                .collect(Collectors.groupingBy(DtoSamplingFrequencyTestTemp::getSamplingFrequencyId));
        for (String frequencyKey : frequencyMap.keySet()) {
            List<String> anaItemIds = frequencyMap.get(frequencyKey).stream()
                    .map(DtoSamplingFrequencyTestTemp::getAnalyseItemId).distinct().collect(Collectors.toList());
            sampleFolderService.deleteFrequencyAnalyseItems(Collections.singletonList(frequencyKey), anaItemIds);
        }
    }
}
