package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRange;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRangeResult;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.TestQCRangeRepository;
import com.sinoyd.lims.lim.service.TestQCRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * TestQCRange操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
@Service
public class TestQCRangeServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoTestQCRange, String, TestQCRangeRepository> implements TestQCRangeService {


    @Autowired
    private TestQCRangeRepository testQCRangeRepository;

    @Autowired
    private CalculationService calculationService;

    private static final Pattern PATTERN = Pattern.compile("\\[\\w+\\]");

    @Override
    public void findByPage(PageBean<DtoTestQCRange> pb, BaseCriteria testQCRangeCriteria) {
        pb.setEntityName("DtoTestQCRange a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, testQCRangeCriteria);
    }

    /**
     * 根据测试项目id获取质控限值
     *
     * @param testIds 测试项目id
     * @return 返回相应的质控限值
     */
    @Override
    public List<DtoTestQCRange> findByTestIdIn(List<String> testIds) {
        return repository.findByTestIdIn(testIds);
    }

    @Transactional
    @Override
    public void copy(String tempTestId, List<String> testIds) {
        List<DtoTestQCRange> list = testQCRangeRepository.getByTestId(tempTestId);
        if (StringUtil.isNotEmpty(list) && StringUtil.isNotEmpty(testIds)) {
            List<String> deleteTestIds = new ArrayList<>();
            List<DtoTestQCRange> newItems = new ArrayList<>();
            for (String testId : testIds) {
                if (!testId.equals(tempTestId)) {
                    deleteTestIds.add(testId);
                    for (DtoTestQCRange item : list) {
                        DtoTestQCRange newItem = new DtoTestQCRange();
                        newItem.setTestId(testId);
                        newItem.setAbsLimit(item.getAbsLimit());
                        newItem.setQcGrade(item.getQcGrade());
                        newItem.setQcType(item.getQcType());
                        newItem.setRelLimit(item.getRelLimit());
                        newItem.setRangeConfig(item.getRangeConfig());
                        newItems.add(newItem);

                    }
                }
            }
            if (deleteTestIds.size() > 0) {
                testQCRangeRepository.clearAll(deleteTestIds);
            }
            if (newItems.size() > 0) {
                super.save(newItems);
            }
        }
    }

    @Override
    public DtoTestQCRangeResult qcRangeIsPass(String testId, Integer qcType, BigDecimal data1, BigDecimal data2, BigDecimal data3) {
        List<DtoTestQCRange> testQCRanges = repository.findByTestIdAndQcType(testId, qcType);
        return this.qcRangeIsPass(testQCRanges, qcType, data1, data2, data3);
    }

    @Override
    public DtoTestQCRangeResult qcRangeIsPass(List<DtoTestQCRange> testQCRanges, Integer qcType, BigDecimal data1, BigDecimal data2, BigDecimal data3) {
        DtoTestQCRangeResult rangeResult = new DtoTestQCRangeResult();
        Boolean isPass = true;
        String range = "";
        Boolean flag;
        Integer type = -1;
        String qcRangeLimit = "";
        if (qcType.equals(EnumLIM.EnumQCType.平行.getValue())) {
            String qcRange = "";
            for (DtoTestQCRange testQCRange : testQCRanges) {
                qcRangeLimit = testQCRange.getRangeConfig();
                flag = calculationResult(qcRangeLimit, data1);
                if (flag) {
                    if (StringUtils.isNotNullAndEmpty(testQCRange.getAbsLimit())) {
                        qcRange = testQCRange.getAbsLimit();
                        range = qcRange;
                        type = EnumLIM.EnumQCRangeType.绝对偏差.getValue();
                    } else {
                        qcRange = testQCRange.getRelLimit();
                        range = qcRange;
                        type = EnumLIM.EnumQCRangeType.相对偏差.getValue();
                    }
                }
            }
            BigDecimal max, min;
            if (data2.compareTo(data3) > -1) {
                max = data2;
                min = data3;
            } else {
                max = data3;
                min = data2;
            }
            BigDecimal sumData = data2.add(data3);
            if (sumData.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal rate = BigDecimal.ZERO;
                if (max.compareTo(min) != 0) {
                    rate = type.equals(EnumLIM.EnumQCRangeType.绝对偏差.getValue()) ? max.subtract(min).abs() : (max.subtract(min)).divide(sumData, 20, RoundingMode.DOWN).multiply(new BigDecimal(100));
                }
                flag = calculationResult(qcRange, rate);
                if (!flag) {
                    isPass = false;
                }
                rangeResult.setRate(rate);
            } else {
                BigDecimal rate = type.equals(EnumLIM.EnumQCRangeType.绝对偏差.getValue()) ? max.subtract(min).abs() : BigDecimal.ZERO;
                flag = calculationResult(qcRange, rate);
                if (!flag) {
                    isPass = false;
                }
                rangeResult.setRate(rate);
            }
        } else if (qcType.equals(EnumLIM.EnumQCType.加标.getValue())) {
            String jbRange = "";
            for (DtoTestQCRange testQCRange : testQCRanges) {
                qcRangeLimit = testQCRange.getRangeConfig();
                flag = calculationResult(qcRangeLimit, data2);
                if (flag) {
                    if (StringUtils.isNotNullAndEmpty(testQCRange.getAbsLimit())) {
                        jbRange = testQCRange.getRelLimit();
                        range = jbRange;
                    } else {
                        jbRange = testQCRange.getRelLimit();
                        range = jbRange;
                    }
                }
            }
            flag = calculationResult(jbRange, data1);
            if (!flag) {
                isPass = false;
            }
        }
        rangeResult.setIsPass(isPass);
        rangeResult.setRangeConfig(range);
        rangeResult.setRangeType(type);
        rangeResult.setRangeLimit(qcRangeLimit);
        return rangeResult;
    }

    @Override
    public DtoTestQCRangeResult calculateJBPass(BigDecimal value, BigDecimal rate, String testId) {
        return qcRangeIsPass(testId, EnumLIM.EnumQCType.加标.getValue(), rate, value, new BigDecimal(0));
    }

    @Override
    public DtoTestQCRangeResult calculatePXPass(BigDecimal avg, BigDecimal max, BigDecimal min, String testId) {
        return qcRangeIsPass(testId, EnumLIM.EnumQCType.平行.getValue(), avg, max, min);
    }

    /**
     * 质控限值范围通用计算方法
     *
     * @param range 范围
     * @return 是否通过
     */
    private Boolean calculationResult(String range, BigDecimal data) {
        Boolean flag = true;
        Matcher m = PATTERN.matcher(range);
        while (m.find()) {
            String matchWord = m.group(0);
            Map<String, Object> map = new HashMap<>();
            map.put(matchWord.replace("[", "").replace("]", ""), data);
            // 修正之后的范围
            String rangeCorrect = UpdateOperators(range);
            Object result = calculationService.calculationExpression(rangeCorrect, map);
            if (result instanceof Boolean) {
                flag = (Boolean) result;
            } else if (result instanceof String) {
                String flagResult = ((String) result);
                if (flagResult.equals("false")) {
                    flag = false;
                }
            }
        }
        return flag;
    }

    /**
     * 进行操作运算符是修饰
     *
     * @param range 需要被修改的运算符
     * @return 修改之后的运算符
     */
    private String UpdateOperators(String range) {
        String rangeCorrect = "";
        // 如果存在and这将and装换为" && ",并替换其他的操作字符
        if (range.indexOf(EnumLIM.EnumOperators.并且.getValue()) != -1) {
            String[] rangeArr = range.split(EnumLIM.EnumOperators.并且.getValue());
            return UpdateOperators(rangeArr[0]) + " && " + UpdateOperators(rangeArr[1]);
        }
        // 如果存在>=.将>=装换为" >= ";否则如果存在>,将>转为" > "
        if (range.indexOf(EnumLIM.EnumOperators.大于等于.getValue()) != -1) {
            rangeCorrect = range.replace(EnumLIM.EnumOperators.大于等于.getValue(), " >= ");
        } else if (range.indexOf(EnumLIM.EnumOperators.大于.getValue()) != -1) {
            rangeCorrect = range.replace(EnumLIM.EnumOperators.大于.getValue(), " > ");
        }

        // 如果存在<=.将<=装换为" <= ";否则如果存在<,将<转为" < "
        if (range.indexOf(EnumLIM.EnumOperators.小于等于.getValue()) != -1) {
            rangeCorrect = range.replace(EnumLIM.EnumOperators.小于等于.getValue(), " <= ");
        } else if (range.indexOf(EnumLIM.EnumOperators.小于.getValue()) != -1) {
            rangeCorrect = range.replace(EnumLIM.EnumOperators.小于.getValue(), " < ");
        }
        return rangeCorrect;
    }
}