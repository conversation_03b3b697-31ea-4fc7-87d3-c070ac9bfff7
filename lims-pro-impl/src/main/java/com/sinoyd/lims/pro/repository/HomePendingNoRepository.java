package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoHomePendingNo;

import java.util.List;

/**
 * 首页待办数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/17
 * @since V100R001
 */
public interface HomePendingNoRepository extends IBaseJpaPhysicalDeleteRepository<DtoHomePendingNo, String> {
    /**
     * 根据用户id，组织id查询
     *
     * @param orgId  组织机构id
     * @param userId 用户id
     * @return 首页代办数量
     */
    List<DtoHomePendingNo> findByOrgIdAndUserId(String orgId, String userId);
}
