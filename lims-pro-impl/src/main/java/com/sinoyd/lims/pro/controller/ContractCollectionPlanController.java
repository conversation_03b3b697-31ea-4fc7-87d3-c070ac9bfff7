package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoContractCollectionPlan;
import com.sinoyd.lims.pro.criteria.ContractCollectionPlanCriteria;
import com.sinoyd.lims.pro.service.ContractCollectionPlanService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 收款计划接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-09
 * @since V100R001
 */
@RestController
@RequestMapping("/api/pro/contractCollectionPlan")
public class ContractCollectionPlanController
        extends BaseJpaController<DtoContractCollectionPlan, String, ContractCollectionPlanService> {

    /**
     * 按主键查询收款计划
     * 
     * @param id         标识
     * @return           DtoContractCollectionPlan
     */
    @ApiOperation(value = "按主键查询收款计划", notes = "按主键查询收款计划")
    @GetMapping("/{id}")
    public RestResponse<DtoContractCollectionPlan> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoContractCollectionPlan> restResp = new RestResponse<>();
        DtoContractCollectionPlan entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 分页动态条件查询收款计划
     * 
     * @param criteria 检索条件
     * @return List<DtoContractCollectionPlan>
     */
    @ApiOperation(value = "分页动态条件查询收款计划", notes = "分页动态条件查询收款计划")
    @GetMapping
    public RestResponse<List<DtoContractCollectionPlan>> findByPage(ContractCollectionPlanCriteria criteria) {

        RestResponse<List<DtoContractCollectionPlan>> restResp = new RestResponse<>();

        PageBean<DtoContractCollectionPlan> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增收款计划
     * 
     * @param entity DtoContractCollectionPlan
     * @return       DtoContractCollectionPlan
     */
    @ApiOperation(value = "新增收款计划", notes = "新增收款计划")
    @PostMapping
    public RestResponse<DtoContractCollectionPlan> save(@RequestBody @Validated DtoContractCollectionPlan entity) {
        RestResponse<DtoContractCollectionPlan> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.save(entity));
        return restResp;
    }

    /**
     * 修改收款计划
     * 
     * @param entity DtoContractCollectionPlan
     * @return       DtoContractCollectionPlan
     */
    @ApiOperation(value = "修改收款计划", notes = "修改收款计划")
    @PutMapping
    public RestResponse<DtoContractCollectionPlan> update(@RequestBody @Validated DtoContractCollectionPlan entity) {
        RestResponse<DtoContractCollectionPlan> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.update(entity));
        return restResp;
    }



    /**
     * 批量删除收款计划
     * 
     * @param ids 标识数组
     * @return   String
     */
    @ApiOperation(value = "批量删除收款计划", notes = "批量删除收款计划")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }

    /**
     * 计算分析图表及汇总数据
     *
     * @param criteria 检索参数
     * @return     Map<String,Object>
     */
    @ApiOperation(value = "计算分析图表及汇总数据", notes = "计算分析图表及汇总数据")
    @GetMapping("/analyzeProfileData")
    public RestResponse<Map<String,Object>> analyzeProfileData(ContractCollectionPlanCriteria criteria) {
        RestResponse<Map<String,Object>> restResp = new RestResponse<>();
        PageBean<DtoContractCollectionPlan> page = super.getPageBean();
        restResp.setData(service.analyzeProfileData(page,criteria));
        return restResp;
    }


    /**
     * 收款项下拉
     *
     * @return           DtoContractCollectionPlan
     */
    @ApiOperation(value = "收款项下拉", notes = "收款项下拉")
    @GetMapping("/collectItem/{orderContractId}")
    public RestResponse<List<String>> getCollectItemSelectList(@PathVariable("orderContractId")String orderContractId) {
        RestResponse<List<String>> restResp = new RestResponse<>();
        restResp.setData(service.getCollectItemSelectList(orderContractId));
        restResp.setRestStatus(ERestStatus.SUCCESS);
        return restResp;
    }
}