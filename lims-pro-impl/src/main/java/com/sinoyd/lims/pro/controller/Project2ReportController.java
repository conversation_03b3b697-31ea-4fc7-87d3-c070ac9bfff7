package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoProject2Report;
import com.sinoyd.lims.pro.service.Project2ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * 项目和报告关联关系服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/04/25
 * @since V100R001
 */
@Api(tags = "示例: 项目和报告关联关系服务")
@RestController
@RequestMapping("api/pro/project2Report")
public class Project2ReportController extends BaseJpaController<DtoProject2Report, String, Project2ReportService> {

    /**
     * 根据项目id查询project2Report
     *
     * @param projectId 项目id
     * @return RestResponse<List < CostInfo>>
     */
    @ApiOperation(value = "根据项目id查询project2Report", notes = "根据项目id查询project2Report")
    @GetMapping
    public RestResponse<List<DtoProject2Report>> find(String projectId) {
        RestResponse<List<DtoProject2Report>> restResponse = new RestResponse<>();
        List<DtoProject2Report> project2ReportList = service.findForProject(projectId);
        restResponse.setRestStatus(StringUtil.isEmpty(project2ReportList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(project2ReportList);
        restResponse.setCount(project2ReportList.size());
        return restResponse;
    }

    /**
     * 上传电子报告
     *
     * @param request 请求体
     * @return 项目和报告的关系
     */
    @ApiOperation(value = "上传电子报告", notes = "上传电子报告")
    @PostMapping("/uploadElectronicReport")
    public RestResponse<DtoProject2Report> uploadSignature(HttpServletRequest request) {
        RestResponse<DtoProject2Report> response = new RestResponse<>();
        DtoProject2Report data = service.uploadElectronicReport(request);
        response.setData(data);
        response.setRestStatus(StringUtil.isNull(data) ? ERestStatus.ERROR : ERestStatus.SUCCESS);
        response.setCount(StringUtil.isNull(data) ? 0 : 1);
        return response;
    }

    /**
     * 电子报告文件下载
     *
     * @param project2ReportId 项目和报告的关联关系id
     * @param response         响应流
     * @return 返回数据
     */
    @ApiOperation(value = "电子报告文件下载", notes = "电子报告文件下载")
    @GetMapping("/downloadElectronicReport/{project2ReportId}")
    public RestResponse<String> fileDownload(@PathVariable String project2ReportId, HttpServletResponse response) throws IOException {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.downloadElectronicReport(project2ReportId, response));
        return restResp;
    }

    /**
     * 清空电子报告文件
     *
     * @param map 参数映射
     * @return 是否清除成功
     */
    @ApiOperation(value = "清空电子报告文件", notes = "清空电子报告文件")
    @PostMapping("/clearElectronicReport")
    public RestResponse<String> fileClear(@RequestBody Map<String, String> map) {
        RestResponse<String> restResp = new RestResponse<>();
        service.clearElectronicReport(map.getOrDefault("project2ReportId", UUIDHelper.GUID_EMPTY));
        restResp.setMsg("操作成功！");
        return restResp;
    }
}