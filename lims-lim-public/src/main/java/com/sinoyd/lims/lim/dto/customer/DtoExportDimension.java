package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;

import javax.persistence.Table;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * 量纲导出
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/5
 * @since V100R001
 */
@Table(name = "TB_BASE_Dimension")
@Data
public class DtoExportDimension extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id;

    /**
     * 量纲名称
     */
    @Excel(name = "量纲名称", orderNum = "20")
    private String dimensionName;

    /**
     * 量纲类型（使用常量Guid，常量名称BASE_DimensionType）
     */
    @Excel(name = "量纲类型", orderNum = "30")
    private String dimensionTypeId;

    /**
     * 编号
     */
    @Excel(name = "编号", orderNum = "40")
    private String code;

    /**
     * 排序值
     */
    @Excel(name = "排序值", orderNum = "50")
    @Pattern(regexp = "^-?\\d+$", message = "排序值必须为整数")
    private String orderNum;

    /**
     * 备注
     */
    @Excel(name = "备注", orderNum = "60")
    private String remark;

    /**
     * 基准值
     */
    @Excel(name = "基准值", orderNum = "70")
    @Pattern(regexp = "^[+-]?(\\d+\\.?\\d*|\\.\\d+)$", message = "基准值格式不符合要求")
    private String baseValue;

}
