package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroup2TestRepository;
import com.sinoyd.lims.pro.criteria.SampleReserveGroupCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataUpdate;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.entity.SampleDispose;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.SampleGroupService;
import com.sinoyd.lims.pro.service.SampleReserveGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 样品处理操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/20
 * @since V100R001
 */
@Service
@Slf4j
public class SampleReserveGroupServiceImpl extends BaseJpaServiceImpl<DtoSampleReserve, String, SampleReserveGroupRepository> implements SampleReserveGroupService {


    //region 依赖注入
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    protected AnalyseDataRepository analyseDataRepository;

    private SampleReserve2TestRepository sampleReserve2TestRepository;

    protected SampleRepository sampleRepository;

    private SampleTypeRepository sampleTypeRepository;

    private AnalyzeItemRepository analyzeItemRepository;

    private SampleDispose2TestRepository sampleDispose2TestRepository;

    private SampleDisposeRepository sampleDisposeRepository;

    protected SampleGroupRepository sampleGroupRepository;

    private SampleTypeGroup2TestRepository sampleTypeGroup2TestRepository;

    private TestRepository testRepository;

    private QualityControlRepository qualityControlRepository;

    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    private SampleGroupService sampleGroupService;
    //endregion


    /**
     * 分页查询数据
     *
     * @param pb       分页数据
     * @param criteria 查询条件
     */
    @Override
    public void findSamplesGroupByPage(PageBean<DtoSampleGroup> pb, BaseCriteria criteria) {
        pb.setEntityName("DtoSampleGroup t1,DtoSample t2");
        pb.setSelect("select new com.sinoyd.lims.pro.dto.DtoSampleGroup(" +
                "t1.id as sampleGroupId, t2.code as sampleCode, t1.sampleId, t2.redFolderName," +
                "t2.sampleTypeId, t2.samplingTimeBegin,  t2.projectId, t2.status," +
                "t2.receiveId, t1.analyseItemNames, t1.reserveNums, t1.analyseNums," +
                " t1.sampleTypeGroupId, t1.isGroup, t2.qcId,t1.receiveSampleDate)");
        SampleReserveGroupCriteria sampleReserveGroupCriteria = (SampleReserveGroupCriteria) criteria;
        comRepository.findByPage(pb, sampleReserveGroupCriteria);
        List<DtoSampleGroup> sampleGroupList = pb.getData();
        if (StringUtil.isNotEmpty(sampleGroupList)) {
            // 获取质控信息
            List<String> sampleIds = sampleGroupList.stream().map(DtoSampleGroup::getSampleId).distinct().collect(Collectors.toList());
            List<DtoSample> sampleAll = sampleRepository.findAll(sampleIds);
            List<String> qcIds = sampleAll.stream().map(DtoSample::getQcId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
            List<DtoQualityControl> qualityControlList = qualityControlRepository.findAll(qcIds);
            Map<String, List<DtoQualityControl>> qualityControlMap = qualityControlList.stream().filter(p -> p.getQcGrade().equals(1)).collect(Collectors.groupingBy(DtoQualityControl::getId));

            // 获取送样单数据,获取接样日期
//            List<String> receiveIds = sampleGroupList.stream().map(DtoSampleGroup::getReceiveId).distinct().collect(Collectors.toList());
//            List<DtoReceiveSampleRecord> receiveSampleRecordList = receiveSampleRecordRepository.findAll(receiveIds);
//            Map<String, Date> sampleRecordIdToMap = receiveSampleRecordList.stream().collect(Collectors.toMap(DtoReceiveSampleRecord::getId, DtoReceiveSampleRecord::getReceiveSampleDate));

            //获取所有的检测类型
            List<String> sampleTypeIds = sampleGroupList.stream().map(DtoSampleGroup::getSampleTypeId).collect(Collectors.toList());
            List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeRepository.findAll(sampleTypeIds) : new ArrayList<>();

            // 样品领取处置数据
            List<String> samplelGroupIds = sampleGroupList.stream().map(DtoSampleGroup::getSampleGroupId).collect(Collectors.toList());
            List<DtoSampleReserve> sampleReserveList = repository.findBySampleGroupIdIn(samplelGroupIds);
            Map<String, List<DtoSampleReserve>> sampleReserveToMap = sampleReserveList.stream().filter(p -> EnumLIM.EnumReserveType.处置.getValue().equals(p.getReserveType())).collect(Collectors.groupingBy(DtoSampleReserve::getSampleGroupId));

            //获取留样处置数据
            List<DtoSampleDispose> disposeList = sampleDisposeRepository.findBySampleIdIn(sampleIds);
            List<String> disposeIds = disposeList.stream().map(DtoSampleDispose::getId).collect(Collectors.toList());
            List<DtoSampleDispose2Test> sampleDispose2Tests = sampleDispose2TestRepository.findBySampleDisposeIdIn(disposeIds);
            Map<String, List<DtoSampleDispose2Test>> dispose2TestToMap = sampleDispose2Tests.stream().collect(Collectors.groupingBy(DtoSampleDispose2Test::getSampleDisposeId));

            List<DtoAnalyzeItem> analyzeItemAll = analyzeItemRepository.findAll();
            for (DtoSampleGroup sampleGroup : sampleGroupList) {
                // 设置质控数据类型，质控等级
                if (StringUtil.isNotEmpty(sampleGroup.getQcId()) && !UUIDHelper.GUID_EMPTY.equals(sampleGroup.getQcId())) {
                    List<DtoQualityControl> qualityControls = qualityControlMap.get(sampleGroup.getQcId());
                    if (StringUtil.isNotEmpty(qualityControls)) {
                        DtoQualityControl dtoQualityControl = qualityControls.stream().filter(p -> sampleGroup.getQcId().equals(p.getId())).findFirst().orElse(null);
                        sampleGroup.setQcGrade(dtoQualityControl.getQcGrade());
                        sampleGroup.setQcType(dtoQualityControl.getQcType());
                    }
                }
                // 接样日期
//                sampleGroup.setSendTime(sampleRecordIdToMap.get(sampleGroup.getReceiveId()));
                // 检测类型
                if (StringUtil.isNotEmpty(sampleTypes)) {
                    DtoSampleType sampleType = sampleTypes.stream().filter(p -> p.getId().equals(sampleGroup.getSampleTypeId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(sampleType)) {
                        sampleGroup.setSampleTypeName(sampleType.getTypeName());
                    }
                }
                // 处置状态
                String keepStatus = "未留样";
                String reserveStatus = "未处置";
                sampleGroup.setReserveStatus(reserveStatus);
                if (StringUtil.isNotEmpty(sampleReserveToMap)) {
                    List<DtoSampleReserve> dtoSampleReserves = sampleReserveToMap.get(sampleGroup.getSampleGroupId());
                    if (StringUtil.isNotEmpty(dtoSampleReserves)) {
                        dtoSampleReserves.removeIf(DtoSampleReserve::getIsDeleted);
                        if (StringUtil.isNotEmpty(dtoSampleReserves)) {
                            sampleGroup.setReserveStatus("已处置");
                        }
                    }
                }
                // 根据样品ID和分组id筛选留样信息
                List<DtoSampleDispose> collect = new ArrayList<>();
                if (StringUtil.isNotEmpty(disposeList)) {
                    List<DtoSampleDispose> keepDispose = disposeList.stream().filter(p -> sampleGroup.getSampleId().equals(p.getSampleId())).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(keepDispose)) {
                        // 获取当前数据下所有的分析项目->总称后的
                        List<String> itemNames = Arrays.asList(sampleGroup.getAnalyseItemNames().split("，"));
                        // 根据分析项目名称获取对应的分析项目对象，然后筛选出分析项目id，用作和留样记录绑定的数据比较
                        List<String> ids = analyzeItemAll.stream().filter(p -> itemNames.contains(p.getAnalyzeItemName())).map(DtoAnalyzeItem::getId).collect(Collectors.toList());
                        for (DtoSampleDispose dtoSampleDispose : keepDispose) {
                            List<DtoSampleDispose2Test> dtoSampleDispose2Tests = dispose2TestToMap.get(dtoSampleDispose.getId());
                            List<String> testIds = dtoSampleDispose2Tests.stream().map(DtoSampleDispose2Test::getTestId).collect(Collectors.toList());
                            if (StringUtil.isNotEmpty(testIds.stream().filter(ids::contains).collect(Collectors.toList()))) {
                                collect.add(dtoSampleDispose);
                            }
                        }
                    }
                }

                //设置留样状态
                if (StringUtil.isNotEmpty(collect)) {
                    // 筛选已处置的留样，用作比较 是否全部处置
                    List<DtoSampleDispose> disposes = collect.stream().filter(SampleDispose::getIsDisposed).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(disposes)) {
                        keepStatus = "留样中";
                        if (disposes.size() == collect.size()) {
                            keepStatus = "已处置";
                        }
                    } else {
                        keepStatus = "留样中";
                    }
                }
                sampleGroup.setKeepStatus(keepStatus);

            }
        }
        pb.setData(sampleGroupList);
    }

    /**
     * 根据样品Id查询样品详细数据
     *
     * @param sampleGroupId 样品分组主键id
     * @return 查询结果
     */
    @Override
    public DtoSample findSampleDetail(String sampleGroupId) {
        List<DtoAnalyzeItem> analyzeItems = new ArrayList<>();
        DtoSampleGroup sampleGroup = sampleGroupRepository.findOne(sampleGroupId);
        String sampleId = sampleGroup.getSampleId();

        List<String> analyzeNames = Arrays.asList(sampleGroup.getAnalyseItemNames().split("，"));
        List<DtoSampleDispose> disposesList = sampleDisposeRepository.findBySampleIdIn(Collections.singletonList(sampleId));
        List<DtoSampleDispose> disposes = new ArrayList<>();
        if (StringUtil.isNotEmpty(disposesList)) {
            List<DtoAnalyzeItem> analyzeItemAll = analyzeItemRepository.findAll();
            List<String> disposeIds = disposesList.stream().map(DtoSampleDispose::getId).collect(Collectors.toList());
            List<DtoSampleDispose2Test> sampleDispose2Tests = sampleDispose2TestRepository.findBySampleDisposeIdIn(disposeIds);
            Map<String, List<DtoSampleDispose2Test>> dispose2TestToMap = sampleDispose2Tests.stream().collect(Collectors.groupingBy(DtoSampleDispose2Test::getSampleDisposeId));
            // 根据分析项目名称获取对应的分析项目对象，然后筛选出分析项目id，用作和留样记录绑定的数据比较
            List<String> ids = analyzeItemAll.stream().filter(p -> analyzeNames.contains(p.getAnalyzeItemName())).map(DtoAnalyzeItem::getId).collect(Collectors.toList());
            for (DtoSampleDispose dtoSampleDispose : disposesList) {
                List<DtoSampleDispose2Test> dtoSampleDispose2Tests = dispose2TestToMap.get(dtoSampleDispose.getId());
                List<String> testIds = dtoSampleDispose2Tests.stream().map(DtoSampleDispose2Test::getTestId).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(testIds.stream().filter(ids::contains).collect(Collectors.toList()))) {
                    disposes.add(dtoSampleDispose);
                }
            }
        }
        String status = "未留样";
        String reserveStatus = "未处置";
        String reservePersonName = "";
        AtomicReference<Integer> testNum = new AtomicReference<>(0);
        int totalTestNum = 0;
        DtoSampleReserve disposeData = new DtoSampleReserve();
        analyzeItems = analyzeItemRepository.findByAnalyzeItemNameIn(analyzeNames);
        totalTestNum = analyzeItems.size();
        // 设置样品的处置状态与关联测试项目领取数据
        DtoSample sample = sampleRepository.findOne(sampleId);
        // 送样单id
        String receiveId = sample.getReceiveId();
        if (StringUtil.isNotEmpty(sample.getAssociateSampleId()) && !UUIDHelper.GUID_EMPTY.equals(sample.getAssociateSampleId())) {
            DtoSample yySample = sampleRepository.findOne(sample.getAssociateSampleId());
            receiveId = yySample.getReceiveId();
        }
        //获取所有领样单数据
        List<DtoReceiveSubSampleRecord> subSampleRecords = receiveSubSampleRecordRepository.findByReceiveId(receiveId);
        Date samplingTime = StringUtil.isNotNull(sampleGroup.getReceiveSampleDate()) ? sampleGroup.getReceiveSampleDate() : getReceiveTime(receiveId, subSampleRecords);
        if (StringUtil.isNotNull(sample)) {
            //获取检测类型
            String sampleTypeId = sample.getSampleTypeId();
            DtoSampleType sampleType = sampleTypeRepository.findOne(sampleTypeId);
            if (StringUtil.isNotNull(sampleType)) {
                sample.setSampleTypeName(sampleType.getTypeName());
                sample.setBigSampleTypeId(sampleType.getParentId());
            }
            List<DtoSampleReserve> bySampleId = repository.findBySampleGroupId(sampleGroupId);
            bySampleId.removeIf(DtoSampleReserve::getIsDeleted);
            if (StringUtil.isNotEmpty(bySampleId)) {
                //获取处置的数据
                Optional<DtoSampleReserve> sampleReserveOptional = bySampleId.stream()
                        .filter(p -> EnumLIM.EnumReserveType.处置.getValue().toString().equals(p.getReserveType().toString())).max(Comparator.comparing(DtoSampleReserve::getCreateDate));
                if (sampleReserveOptional.isPresent()) {
                    reserveStatus = "已处置";
                    DtoSampleReserve reserve = sampleReserveOptional.get();
                    DtoPerson person = personRepository.findOne(reserve.getReservePersonId());
                    reservePersonName = StringUtil.isNotNull(person) ? person.getCName() : "";
                    disposeData = sampleReserveOptional.get();
                }
                disposeData.setReservePersonName(reservePersonName);
                //获取领取的数据
                List<DtoSampleReserve> reserveDataList = bySampleId.stream()
                        .filter(p -> EnumLIM.EnumReserveType.领取.getValue().toString().equals(p.getReserveType().toString()))
                        .collect(Collectors.toList());
                if (StringUtil.isNotEmpty(reserveDataList)) {
                    for (DtoSampleReserve reserve : reserveDataList) {
                        setReserveDetail(analyzeItems, disposes, testNum, reserve);
                    }
                }
            }
            //设置留样状态信息
            analyzeItems.forEach(p -> setIsKeepSample(p, disposes));

            sample.setReceiveTest(analyzeItems);
            //设置样品留样状态
            if (StringUtil.isNotEmpty(disposes)) {
                status = "留样中";
                if (disposes.get(0).getIsDisposed()) {
                    status = "已处置";
                }
            }
            sample.setReceiveTestNum(testNum.get());
            sample.setReceiveTotalTestNum(totalTestNum);
            sample.setReserveStatus(reserveStatus);
            sample.setKeepStatus(status);
            sample.setSampleReserve(disposeData);
            sample.setSendTime(samplingTime);
        }
        return sample;
    }

    @Override
    public Map<String, Object> findSampleDetails(List<String> sampleGroupIds) {
        Map<String, Object> map = new HashMap<>();

        List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findAll(sampleGroupIds);
        List<String> sampleIds = sampleGroupList.stream().map(DtoSampleGroup::getSampleId).distinct().collect(Collectors.toList());
        List<String> analyzeNames = new ArrayList<>();
        sampleGroupList.forEach(p -> analyzeNames.addAll(Arrays.asList(p.getAnalyseItemNames().split("，"))));
        List<DtoAnalyzeItem> analyzeItemList = analyzeItemRepository.findByAnalyzeItemNameIn(analyzeNames);

        List<DtoSample> sampleList = sampleRepository.findAll(sampleIds);
        // 获取留样记录
        List<DtoSampleDispose> disposeList = sampleDisposeRepository.findBySampleIdIn(sampleIds);
        Map<String, List<DtoSampleDispose>> sampleDisposeMap = disposeList.stream().collect(Collectors.groupingBy(DtoSampleDispose::getSampleId));
        List<DtoSampleDispose2Test> dispose2TestList = new ArrayList<>();
        if (StringUtil.isNotEmpty(disposeList)) {
            List<String> disposeIds = disposeList.stream().map(DtoSampleDispose::getId).collect(Collectors.toList());
            dispose2TestList = sampleDispose2TestRepository.findBySampleDisposeIdIn(disposeIds);
        }
        List<DtoSampleDispose2Test> finalDispose2TestList = dispose2TestList;
        List<DtoAnalyzeItem> finalAnalyzeItems = new ArrayList<>();
        for (DtoSampleGroup sampleGroup : sampleGroupList) {
            List<String> itemNames = Arrays.asList(sampleGroup.getAnalyseItemNames().split("，"));
            List<DtoAnalyzeItem> analyzeItems = analyzeItemList.stream().filter(p -> itemNames.contains(p.getAnalyzeItemName())).collect(Collectors.toList());
            List<DtoSampleDispose> dtoSampleDisposes = sampleDisposeMap.get(sampleGroup.getSampleId());
            List<String> disposeIds = StringUtil.isNotEmpty(dtoSampleDisposes) ? dtoSampleDisposes.stream().map(DtoSampleDispose::getId).collect(Collectors.toList()) : new ArrayList<>();
            for (DtoAnalyzeItem itemName : analyzeItems) {
                List<DtoSampleDispose2Test> collect = finalDispose2TestList.stream().filter(x -> itemName.getId().equals(x.getTestId()) && disposeIds.contains(x.getSampleDisposeId())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(collect)) {
                    itemName.setIsReceive(true);
                } else {
                    itemName.setIsReceive(false);
                }
                finalAnalyzeItems.add(itemName);
            }
        }
        finalAnalyzeItems = finalAnalyzeItems.stream().distinct().collect(Collectors.toList());
        map.put("receiveTest", finalAnalyzeItems);
        List<String> sampleCodeList = sampleList.stream().map(DtoSample::getCode).distinct().collect(Collectors.toList());
        map.put("code", StringUtil.isNotEmpty(sampleCodeList) ? String.join("、", sampleCodeList) : "");
        return map;
    }

    @Override
    public List<DtoAnalyzeItem> findAnalyzeItems(List<String> sampleGroupIds) {
        List<DtoAnalyzeItem> analyzeItems = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleGroupIds)) {
            // 根据样品id和分组id获取分组后的数据
            List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findAll(sampleGroupIds);
            List<String> sampleIds = sampleGroupList.stream().map(DtoSampleGroup::getSampleId).distinct().collect(Collectors.toList());
            List<String> analyseItemNames = sampleGroupList.stream().map(DtoSampleGroup::getAnalyseItemNames).collect(Collectors.toList());
            // 根据分组后的分析项目名称，处理汇总后查询分析项目对象
            List<String> anNames = new ArrayList<>();
            analyseItemNames.forEach(p -> anNames.addAll(Arrays.asList(p.split("，"))));
            List<String> finalAnNames = anNames.stream().distinct().collect(Collectors.toList());
            analyzeItems = analyzeItemRepository.findByAnalyzeItemNameIn(finalAnNames);

            // 获取已经领取的记录
            List<DtoSampleReserve2Test> receiveAnaItem = new ArrayList<>();
            List<DtoSampleReserve> reserves = repository.findBySampleGroupIdIn(sampleGroupList.stream().map(DtoSampleGroup::getId).collect(Collectors.toList()));
            if (StringUtil.isNotEmpty(reserves)) {
                List<String> reserveIds = reserves.stream().map(DtoSampleReserve::getId).collect(Collectors.toList());
                // 获取领取记录关联的分析项目数据
                if (StringUtil.isNotEmpty(reserveIds)) {
                    receiveAnaItem = sampleReserve2TestRepository.findByReserveIdIn(reserveIds);
                }

            }
            List<DtoSampleReserve2Test> finalReceiveAnaItem = receiveAnaItem;
            analyzeItems.forEach(p -> {
                List<DtoSampleReserve2Test> reserve2Tests = finalReceiveAnaItem.stream().filter(x -> p.getId().equals(x.getAnalyzeItemId())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(reserve2Tests)) {
                    p.setIsReceive(true);
                } else {
                    p.setIsReceive(false);
                }
            });
        }
        return analyzeItems;
    }


    @Transactional
    @Override
    public List<DtoSampleReserve> saveReserve(DtoSampleReserve sampleReserve) {

        // 根据样品id和分组id获取样品分组数据
        List<String> sampleGroupIds = sampleReserve.getSampleGroupIds();
        List<DtoSampleGroup> sampleGroups = sampleGroupRepository.findAll(sampleGroupIds);
        List<String> sampleIds = sampleGroups.stream().map(DtoSampleGroup::getSampleId).distinct().collect(Collectors.toList());
        // 获取分组下所包含的多有分析项目
        List<String> analyzeItemNames = new ArrayList<>();
        sampleGroups.stream().map(DtoSampleGroup::getAnalyseItemNames).collect(Collectors.toList()).forEach(r -> {
            analyzeItemNames.addAll(Arrays.asList(r.split("，")));
        });
        List<DtoAnalyzeItem> analyzeItems = analyzeItemRepository.findByAnalyzeItemNameIn(analyzeItemNames);

        //获取所有的样品对应的测试项目数据
        List<DtoAnalyseData> analyseData = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleIds)) {
            analyseData = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
            analyseData.removeIf(AnalyseData::getIsCompleteField);
        }

        // 获取当前选中样品下的所有数据，然后再按照样品id进行分组
        List<DtoSampleGroup> sampleIdIn = sampleGroupRepository.findBySampleIdIn(sampleIds);
        Map<String, List<DtoSampleGroup>> sampleGroupsToMap = sampleIdIn.stream().collect(Collectors.groupingBy(DtoSampleGroup::getSampleId));
        List<String> groupTypeIds = sampleIdIn.stream().map(DtoSampleGroup::getSampleTypeGroupId).distinct().collect(Collectors.toList());

        // 根据样品id 查询是否存在总称
        List<String> testIdList = analyseData.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        List<DtoTest> dtoTests = testRepository.findAll(testIdList);
        List<String> parentIds = dtoTests.stream().map(DtoTest::getParentId).filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).distinct().collect(Collectors.toList());
        List<DtoTest> parentTests = StringUtil.isNotEmpty(parentIds) ? testRepository.findAll(parentIds) : new ArrayList<>();
        Map<String, List<DtoTest>> testToMap = new HashMap<>();
        if (parentTests.size() > 0) {
            List<DtoTest> allSunDtoTests = testRepository.findByParentIdIn(parentTests.stream().map(DtoTest::getId).collect(Collectors.toList()));
            testToMap = allSunDtoTests.stream().collect(Collectors.groupingBy(DtoTest::getParentId));
        }
        // 分组信息与测试项目关联
        List<DtoSampleTypeGroup2Test> sampleTypeGroupTestLst = sampleTypeGroup2TestRepository.findBySampleTypeGroupIds(groupTypeIds);
        Map<String, List<DtoSampleTypeGroup2Test>> sampleTypeGroupTestMap = sampleTypeGroupTestLst.stream().collect(Collectors.groupingBy(DtoSampleTypeGroup2Test::getSampleTypeGroupId));

        // 获取已领取的测试项目
        List<DtoSampleReserve> sampleReserveList = repository.findBySampleGroupIdIn(sampleGroupIds).stream().filter(p -> EnumLIM.EnumReserveType.领取.getValue().equals(p.getReserveType())).collect(Collectors.toList());
        Map<String, List<DtoSampleReserve>> sampleReserveToMap = sampleReserveList.stream().collect(Collectors.groupingBy(DtoSampleReserve::getSampleGroupId));
        List<String> sampleReserveIds = sampleReserveList.stream().map(DtoSampleReserve::getId).collect(Collectors.toList());
        List<DtoSampleReserve2Test> reserve2TestList = sampleReserve2TestRepository.findByReserveIdIn(sampleReserveIds);
        Map<String, List<DtoSampleReserve2Test>> reserve2TestToMap = reserve2TestList.stream().collect(Collectors.groupingBy(DtoSampleReserve2Test::getReserveId));

        //批量保存的数据
        List<DtoSampleReserve> batchInsert = new ArrayList<>();
        //批量保存的子表数据
        List<DtoSampleReserve2Test> batchToTest = new ArrayList<>();
        //修改样品分组表数据
        List<DtoSampleGroup> updateSampleGroups = new ArrayList<>();
        // 更细分析数据
        List<DtoAnalyseData> updateAnalyseDataList = new ArrayList<>();
        //保存数据
        for (DtoSampleGroup sampleGroup : sampleGroups) {
            // 获取分组数据中存在的分析项目
            String itemNames = sampleGroup.getAnalyseItemNames();
            List<String> itemNameList = Arrays.asList(itemNames.split("，"));
            List<String> analyzeItemIds = analyzeItems.stream().filter(p -> itemNameList.contains(p.getAnalyzeItemName())).map(DtoAnalyzeItem::getId).collect(Collectors.toList());
            List<String> containsItemIds = StringUtil.isNotEmpty(sampleReserve.getAnalyzeItemIds()) ?
                    sampleReserve.getAnalyzeItemIds().stream().filter(analyzeItemIds::contains).collect(Collectors.toList()) : analyzeItemIds;

            // 不选择同方法时，
            // 实验室分析待检测页面设置时进入，检查样品下的总称内的测试项目是否全部都设置领样日期
            if (!sampleReserve.getIsSameMethod() && !sampleReserve.getIsUpdateAnalyseData()) {
                checkSampleReceiveDate(testToMap, parentTests, sampleGroup, analyseData, containsItemIds);
            }

            List<String> testIds = new ArrayList<>();
            //设置样品关联测试项目id
            List<DtoAnalyseData> finalAnalyseData = new ArrayList<>();
            if (!UUIDHelper.GUID_EMPTY.equals(sampleGroup.getSampleTypeGroupId())) {
                List<String> collect = sampleTypeGroupTestMap.get(sampleGroup.getSampleTypeGroupId()).stream().map(DtoSampleTypeGroup2Test::getTestId).collect(Collectors.toList());
                finalAnalyseData = analyseData.stream().filter(p -> collect.contains(p.getTestId()) && p.getSampleId().equals(sampleGroup.getSampleId())).collect(Collectors.toList());
            } else {
                Integer isGroup = sampleGroup.getIsGroup();
                // 同一样品中有部分分组，部分不分组的情况，全因子和单因子的情况
                if (isGroup == 1) {
                    // 当前样品分组数据
                    List<DtoSampleGroup> sampleGroupsToSample = sampleGroupsToMap.get(sampleGroup.getSampleId());
                    // 筛选有分组id的数据
                    List<DtoSampleGroup> dtoSampleGroups = sampleGroupsToSample.stream().filter(p -> p.getSampleId().equals(sampleGroup.getSampleId()) && !p.getSampleTypeGroupId().equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList());
                    List<String> collect = new ArrayList<>();
                    // 根据分组id 获取所有关联的测试项目
                    dtoSampleGroups.forEach(p -> collect.addAll(sampleTypeGroupTestMap.get(p.getSampleTypeGroupId()).stream().map(DtoSampleTypeGroup2Test::getTestId).collect(Collectors.toList())));
                    // 筛选分组测试项目中不包含的分析数据
                    finalAnalyseData = analyseData.stream().filter(p -> !collect.contains(p.getTestId()) && p.getSampleId().equals(sampleGroup.getSampleId())).collect(Collectors.toList());
                } else if (isGroup == 3) {
                    finalAnalyseData = analyseData.stream().filter(p -> sampleGroup.getAnalyseItemNames().equals(p.getRedAnalyzeItemName()) && p.getSampleId().equals(sampleGroup.getSampleId())).collect(Collectors.toList());
                } else if (isGroup == 2) {
                    finalAnalyseData = analyseData.stream().filter(p -> p.getSampleId().equals(sampleGroup.getSampleId())).collect(Collectors.toList());
                }
            }
            // 获取已领取的分析项目id
            List<String> reserveAnalyseItemIds = new ArrayList<>();
            if (StringUtil.isNotEmpty(sampleReserveToMap.get(sampleGroup.getId()))) {
                List<DtoSampleReserve> dtoSampleReserves = sampleReserveToMap.get(sampleGroup.getId());
                for (DtoSampleReserve dtoSampleReserve : dtoSampleReserves) {
                    reserveAnalyseItemIds.addAll(reserve2TestToMap.get(dtoSampleReserve.getId()).stream().map(DtoSampleReserve2Test::getAnalyzeItemId).collect(Collectors.toList()));
                }
            }

            getTest(testIds, sampleGroup.getSampleId(), finalAnalyseData, sampleReserve, parentTests, updateAnalyseDataList, containsItemIds);
            if (StringUtil.isNotEmpty(testIds)) {
                //添加数据
                addBatchInsert(batchInsert, batchToTest, sampleReserve, testIds, sampleGroup.getSampleId(), sampleGroup.getId());
            }
            if (EnumLIM.EnumReserveType.处置.getValue().toString().equals(sampleReserve.getReserveType().toString())) {
                //添加数据
                addBatchInsert(batchInsert, batchToTest, sampleReserve, testIds, sampleGroup.getSampleId(), sampleGroup.getId());
            }
            // 初始化领取数量
            int reserveNums = 0;
            if (EnumLIM.EnumReserveType.领取.getValue().equals(sampleReserve.getReserveType())) {

                // 领取数量保存到样品分组表
                List<String> analyzeMethodIds = finalAnalyseData.stream().map(DtoAnalyseData::getAnalyzeMethodId).distinct().collect(Collectors.toList());
                // 筛选出存在总称的情况
                List<DtoTest> generalTermList = parentTests.stream().filter(p -> containsItemIds.contains(p.getAnalyzeItemId())
                        && analyzeMethodIds.contains(p.getAnalyzeMethodId())).collect(Collectors.toList());
                // 获取总称下的所有分析项目个数,去除已领取的项目
                if (StringUtil.isNotEmpty(generalTermList)) {
                    generalTermList = generalTermList.stream().filter(p -> !reserveAnalyseItemIds.contains(p.getAnalyzeItemId())).collect(Collectors.toList());
                    for (DtoTest dtoTest : generalTermList) {
                        List<String> ids = testToMap.get(dtoTest.getId()).stream().map(DtoTest::getId).collect(Collectors.toList());
                        List<DtoAnalyseData> dtoAnalyseData = finalAnalyseData.stream().filter(p -> ids.contains(p.getTestId())).collect(Collectors.toList());
                        reserveNums += dtoAnalyseData.size();
                    }
                }

                List<String> resevreTestIds = testIds.stream().distinct().collect(Collectors.toList());
                // 筛选出选中的数据中为总称
                List<String> generalTermIds = generalTermList.stream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());

                List<String> notContains = resevreTestIds.stream().filter(p -> analyzeItemIds.contains(p) && !reserveAnalyseItemIds.contains(p) && !generalTermIds.contains(p)).collect(Collectors.toList());
                reserveNums += notContains.size();

                if (reserveNums > 0) {
                    sampleGroup.setReserveNums(StringUtil.isNotNull(sampleGroup.getReserveNums()) ? sampleGroup.getReserveNums() + reserveNums : reserveNums);
                    updateSampleGroups.add(sampleGroup);
                }
            }
        }
        if (StringUtil.isNotEmpty(updateSampleGroups)) {
            sampleGroupService.save(updateSampleGroups);
        }
        List<DtoSampleReserve> save = new ArrayList<>();
        //保存领取数据
        if (StringUtil.isNotEmpty(batchInsert)) {
            save = repository.save(batchInsert);
            if (StringUtil.isNotEmpty(batchToTest)) {
                //保存领取关联测试项目
                sampleReserve2TestRepository.save(batchToTest);
            }
        }
        // 更新分析数据中的领取时间
        if (StringUtil.isNotEmpty(updateAnalyseDataList)) {
            if (sampleReserve.getIsUpdateAnalyseData()) {
                updateAnalyseDataList.forEach(p -> {
                    p.setSampleReceiveDate(sampleReserve.getReserveDate());
                });
                analyseDataRepository.save(updateAnalyseDataList);
            }

        }

        return save;
    }

    @Override
    public Integer cancelDispose(List<String> sampleGroupIds) {
        List<DtoSampleGroup> sampleGroups = sampleGroupRepository.findAll(sampleGroupIds);
        List<String> sampleIds = sampleGroups.stream().map(DtoSampleGroup::getSampleId).collect(Collectors.toList());
        List<DtoSampleReserve> update = new ArrayList<>();
        List<DtoSampleReserve> reserves = repository.findBySampleGroupIdIn(sampleGroupIds);
        if (StringUtil.isNotEmpty(reserves)) {
            List<DtoSampleReserve> dispose = reserves.stream()
                    .filter(p -> EnumLIM.EnumReserveType.处置.getValue().toString().equals(p.getReserveType().toString()))
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(dispose)) {
                dispose.forEach(p -> p.setIsDeleted(true));
            }
            update = repository.save(reserves);
        }
        return update.size();
    }

    /**
     * 更新样品领取数据
     *
     * @param analyseDataList      分析数据集合
     * @param dtoAnalyseDataUpdate 分析数据更新实体
     */
    @Transactional
    @Override
    public void updateReserve(List<DtoAnalyseData> analyseDataList, DtoAnalyseDataUpdate dtoAnalyseDataUpdate) {
        if (StringUtil.isNotEmpty(analyseDataList)) {
            // 获取样品ids
            List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            // 获取测试项目ids
            List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            // 样品分组数据
            List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findBySampleIdIn(sampleIds);

            // 测试项目集合
            List<DtoTest> testList = testRepository.findAll(testIds);

            List<String> parentTestIds = testList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getParentId())).map(DtoTest::getParentId).collect(Collectors.toList());
            // 同方法批量设置
            if (dtoAnalyseDataUpdate.getIsSameMethod()) {
                List<DtoTest> parentTestList = StringUtil.isNotEmpty(parentTestIds)?testRepository.findAll(parentTestIds):new ArrayList<>();
                testList.addAll(parentTestList);
            }

            // 只有样品分组数据不为null时调用
            if (StringUtil.isNotEmpty(sampleGroupList)) {
                List<String> analyzeItemIds = testList.stream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());
                // 填充待保存数据，调用保存方法
                DtoSampleReserve sampleReserve = new DtoSampleReserve();
                sampleReserve.setSampleGroupIds(sampleGroupList.stream().map(DtoSampleGroup::getId).collect(Collectors.toList()));
                sampleReserve.setAnalyzeItemIds(analyzeItemIds);
                sampleReserve.setReserveDate(dtoAnalyseDataUpdate.getSampleReceiveDate());
                sampleReserve.setReserveType(EnumLIM.EnumReserveType.领取.getValue());
                sampleReserve.setIsUpdateAnalyseData(false);
                sampleReserve.setIsSameMethod(dtoAnalyseDataUpdate.getIsSameMethod());
                sampleReserve.setReservePersonId(dtoAnalyseDataUpdate.getReservePersonId());
                this.saveReserve(sampleReserve);
            }
        }
    }

    @Override
    public List<DtoSampleReserve> judgeSaveData(DtoSampleReserve sampleReserve) {
        //已存在的数据集合
        List<DtoSampleReserve> resultData = new ArrayList<>();
        //已存在的数据集合
        List<DtoSampleReserve> result = new ArrayList<>();

        //获取需要添加的样品Id
        List<String> sampleGroupIds = sampleReserve.getSampleGroupIds();
        List<DtoSampleGroup> sampleGroupList = sampleGroupRepository.findAll(sampleGroupIds);
        List<String> sampleIds = sampleGroupList.stream().map(DtoSampleGroup::getSampleId).distinct().collect(Collectors.toList());

        //查询所有关联领取测试项目的信息
        List<DtoSampleReserve2Test> toTests = sampleReserve2TestRepository.findAll();
        //获取所有的测试项目数据
        List<DtoAnalyseData> anaData = analyseDataRepository.findBySampleIdIn(sampleIds);

        Map<String, DtoSample> dtoSampleMap = sampleRepository.findByIds(sampleIds).stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
        //获取所有的领取操作数据
        List<DtoSampleReserve> sampleReserves = repository.findAll();
        // 根据样品id 查询是否存在总称
        List<String> testIdList = anaData.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        List<DtoTest> dtoTests = testRepository.findAll(testIdList);
        List<String> parentIds = dtoTests.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getParentId())).map(DtoTest::getParentId).distinct().collect(Collectors.toList());
        List<DtoTest> parentList = StringUtil.isNotEmpty(parentIds) ? testRepository.findAll(parentIds) : new ArrayList<>();
        List<String> parentAnalyzeItAemIds = parentList.stream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());

        //判断数据是否存在
        if (StringUtil.isNotEmpty(sampleReserves)) {
            for (DtoSampleGroup sampleGroup : sampleGroupList) {
                //查询操作
                List<DtoSampleReserve> reserveList = repository.findBySampleGroupId(sampleGroup.getId());
                //移除已删除的数据
                reserveList.removeIf(p -> p.getIsDeleted() && EnumLIM.EnumReserveType.领取.getValue().toString().equals(p.getReserveType().toString()));

                if (StringUtil.isNotEmpty(reserveList)) {
                    //获取到类型为领取的数据
                    List<DtoSampleReserve> reservesData = reserveList.stream()
                            .filter(p -> sampleReserve.getReserveType().toString().equals(p.getReserveType().toString())).collect(Collectors.toList());

                    //获取领取数据
                    for (DtoSampleReserve reserve : reservesData) {
                        if (StringUtil.isNotNull(reserve) && EnumLIM.EnumReserveType.领取.getValue().toString().equals(reserve.getReserveType().toString())) {
                            if (reserve.getReserveType().equals(sampleReserve.getReserveType())) {
                                //获取样品下的所有分析项目ID
                                List<String> anaItemIds = anaData.stream()
                                        .filter(p -> sampleGroup.getSampleId().equals(p.getSampleId())).map(DtoAnalyseData::getAnalyseItemId)
                                        .collect(Collectors.toList());
                                if (StringUtil.isNotEmpty(parentAnalyzeItAemIds)) {
                                    anaItemIds.addAll(parentAnalyzeItAemIds);
                                }
                                //获取保存的分析项目Id
                                List<String> saveIds = sampleReserve.getAnalyzeItemIds().stream().filter(anaItemIds::contains).collect(Collectors.toList());
                                //获取到领取的测试项目数据
                                List<DtoSampleReserve2Test> toTestsOfSample = toTests.stream()
                                        .filter(p -> reserve.getId().equals(p.getReserveId()) && saveIds.contains(p.getAnalyzeItemId()))
                                        .collect(Collectors.toList());
                                if (StringUtil.isNotEmpty(toTestsOfSample)) {
                                    reserve.setSampleCode(dtoSampleMap.get(sampleGroup.getSampleId()).getCode());
                                    reserve.setSampleReserve2Tests(toTestsOfSample);
                                    resultData.add(reserve);
                                }
                            }
                        }
                    }
                }
            }
        }
        for (DtoSampleGroup sampleGroup : sampleGroupList) {
            List<DtoSampleReserve> reserveList = resultData.stream().filter(p -> sampleGroup.getId().equals(p.getSampleGroupId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(reserveList)) {
                DtoSampleReserve temp = new DtoSampleReserve();
                for (DtoSampleReserve reserve : reserveList) {
                    if (StringUtil.isNotEmpty(temp.getSampleReserve2Tests())) {
                        if (reserve.getSampleReserve2Tests().size() > temp.getSampleReserve2Tests().size()) {
                            BeanUtils.copyProperties(reserve, temp);
                        }
                    } else {
                        BeanUtils.copyProperties(reserve, temp);
                    }
                }
                result.add(temp);
            }
        }
        return result;
    }


    /**
     * 获取接样时间
     *
     * @param receiveId        送样单
     * @param subSampleRecords 领养单数据
     * @return 接样时间
     */
    private Date getReceiveTime(String receiveId, List<DtoReceiveSubSampleRecord> subSampleRecords) {
        Date samplingTime = null;
        if (StringUtil.isNotEmpty(subSampleRecords)) {
            DtoReceiveSubSampleRecord dtoReceiveSubSampleRecord = subSampleRecords.stream().filter(p -> receiveId.equals(p.getReceiveId()) && p.getCode().contains(EnumPRO.EnumSubRecordType.分析.getValue())).findFirst().orElse(null);
            if (dtoReceiveSubSampleRecord != null) {
                samplingTime = dtoReceiveSubSampleRecord.getReceiveTime();
            }
        }
        return samplingTime;
    }

    /**
     * 设置项目类型
     *
     * @param projectList  项目数据
     * @param projectTypes 项目类型数据
     * @param prjectId     项目id
     * @param sample       需要设置id样品
     */
    private void setProjectType(List<DtoProject> projectList, List<DtoProjectType> projectTypes, String prjectId, DtoSample sample) {
        Optional<DtoProject> projectOptional = projectList.stream().filter(p -> prjectId.equals(p.getId())).findFirst();
        projectOptional.ifPresent(p -> {
            Optional<DtoProjectType> projectType = projectTypes.stream().filter(t -> p.getProjectTypeId().equals(t.getId())).findFirst();
            String projectTypeId = "";
            String projectTypeName = "";
            if (projectType.isPresent()) {
                projectTypeId = projectType.get().getId();
                projectTypeName = projectType.get().getName();
            }
            sample.setProjectName(p.getProjectName());
            sample.setProjectCode(p.getProjectCode());
            sample.setProjectTypeName(projectTypeName);
            sample.setProjectTypeId(projectTypeId);
        });
    }

    /**
     * 获取已选择的测试项目中此样品下的关联测试项目
     *
     * @param anaItemIds    测试项目Id集合
     * @param sampleId      样品Id
     * @param analyseData   测试项目数据
     * @param sampleReserve 领取操作数据
     */
    private void getTest(List<String> anaItemIds, String sampleId, List<DtoAnalyseData> analyseData,
                         DtoSampleReserve sampleReserve, List<DtoTest> parentTests,
                         List<DtoAnalyseData> updateAnalyseDataList, List<String> containsItemIds) {
        //获取已选择的测试项目中此样品下的关联测试项目
        if (StringUtil.isNotEmpty(analyseData)
                && EnumLIM.EnumReserveType.领取.getValue().toString().equals(sampleReserve.getReserveType().toString())) {
            List<DtoAnalyseData> analyseDataOfSample = analyseData
                    .stream().filter(p -> sampleId.equals(p.getSampleId()))
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(analyseDataOfSample)) {
                // 添加待修改的分析数据
                if (EnumLIM.EnumReserveType.领取.getValue().equals(sampleReserve.getReserveType())) {
                    // 添加待更新的测试项目
                    updateAnalyseDataList.addAll(analyseDataOfSample);
                }
                List<DtoAnalyseData> anaDataOfAnaItem = analyseDataOfSample
                        .stream().filter(p -> containsItemIds.contains(p.getAnalyseItemId()))
                        .collect(Collectors.toList());

                //获取分析项目Id
                List<String> tempList = anaDataOfAnaItem
                        .stream().map(DtoAnalyseData::getAnalyseItemId)
                        .collect(Collectors.toList());
                List<String> analyzeMethodIds = analyseData.stream().map(DtoAnalyseData::getAnalyzeMethodId).distinct().collect(Collectors.toList());
                //  总称数据有值时
                if (StringUtil.isNotEmpty(parentTests)) {
                    tempList.addAll(parentTests.stream().filter(p -> containsItemIds.contains(p.getAnalyzeItemId())
                            && analyzeMethodIds.contains(p.getAnalyzeMethodId()))
                            .map(DtoTest::getAnalyzeItemId)
                            .collect(Collectors.toList()));
                }

                anaItemIds.addAll(tempList);
            }
        }
    }


    /**
     * 添加批量数据
     *
     * @param batchInsert   批量添加集合
     * @param batchToTest   批量添加集合
     * @param sampleReserve 添加的数据
     * @param anaItemIds    测试项目Id
     * @param sampleId      样品Id
     */
    private void addBatchInsert(List<DtoSampleReserve> batchInsert, List<DtoSampleReserve2Test> batchToTest,
                                DtoSampleReserve sampleReserve, List<String> anaItemIds, String sampleId, String sampleGroupId) {
        DtoSampleReserve newDto = new DtoSampleReserve();
        //添加需要添加的子表数据
        BeanUtils.copyProperties(sampleReserve, newDto);
        newDto.setId(UUIDHelper.NewID());
        List<DtoSampleReserve2Test> toTest = getToTest(anaItemIds, newDto);
        batchToTest.addAll(toTest);
        newDto.setSampleReserve2Tests(toTest);
        newDto.setSampleId(sampleId);
        newDto.setAnalyzeItemIds(anaItemIds);
        newDto.setSampleGroupId(sampleGroupId);
        newDto.setCreateDate(new Date());
        newDto.setCreator(PrincipalContextUser.getPrincipal().getUserId());
        newDto.setModifier(UUIDHelper.GUID_EMPTY);
        newDto.setModifyDate(new Date());
        batchInsert.add(newDto);
    }

    /**
     * 添加子表关联数据
     *
     * @param anaItemIds 测试项目Id
     * @param reserve    对应的父数据
     * @return 子表关联数据
     */
    private List<DtoSampleReserve2Test> getToTest(List<String> anaItemIds, DtoSampleReserve reserve) {
        List<DtoSampleReserve2Test> result = new ArrayList<>();
        List<DtoAnalyzeItem> analyzeItems = new ArrayList<>();
        if (StringUtil.isNotEmpty(anaItemIds)) {
            analyzeItems = analyzeItemRepository.findAll(anaItemIds);
        }
        if (StringUtil.isNotEmpty(analyzeItems)) {
            analyzeItems.forEach(p -> {
                DtoSampleReserve2Test toTest = new DtoSampleReserve2Test();
                toTest.setReserveId(reserve.getId());
                toTest.setAnalyzeItemId(p.getId());
                toTest.setRedAnalyseItemName(p.getAnalyzeItemName());
                result.add(toTest);
            });
        }
        return result;
    }

    /**
     * 设置领取详细信息
     *
     * @param analyzeItems 样品对应的分析项目集合
     * @param disposes     留样数据
     * @param testNum      领取个数
     */
    private void setReserveDetail(List<DtoAnalyzeItem> analyzeItems,
                                  List<DtoSampleDispose> disposes, AtomicReference<Integer> testNum, DtoSampleReserve reserveSelect) {
        for (DtoAnalyzeItem p : analyzeItems) {
            List<DtoSampleReserve2Test> isExistData = sampleReserve2TestRepository.findByReserveIdIn(Collections.singletonList(reserveSelect.getId()));
            isExistData = isExistData.stream().filter(n -> p.getId().equals(n.getAnalyzeItemId())).collect(Collectors.toList());
            //设置是否留样
            setIsKeepSample(p, disposes);
            if (StringUtil.isEmpty(isExistData)) {
                continue;
            }
            String analyzeItemId = p.getId();
            List<DtoSampleReserve2Test> toTests = sampleReserve2TestRepository.findByAnalyzeItemId(analyzeItemId);
            List<String> reserveIds = toTests.stream().map(DtoSampleReserve2Test::getReserveId).collect(Collectors.toList());
            List<DtoSampleReserve> reserves = repository.findAll(reserveIds);
            DtoSampleReserve reserve = selectLastOne(reserves, reserveSelect.getSampleId());
            //获取到此测试项目是否领取
            List<DtoSampleReserve2Test> existData = toTests.stream()
                    .filter(toTest -> analyzeItemId.equals(toTest.getAnalyzeItemId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(existData)) {
                //领取数量+1
                testNum.getAndSet(testNum.get() + 1);
                //设置领取人
                DtoPerson person = personRepository.findOne(reserve.getReservePersonId());
                p.setReceivePerson(StringUtil.isNotNull(person) ? person.getCName() : "");
                //设置领取日期
                p.setReceiveDate(DateUtil.dateToString(reserve.getReserveDate(), "yyyy-MM-dd HH:mm:ss"));

            }
        }
    }

    /**
     * 设置留样状态
     *
     * @param analyzeItem 需要设置的分析项目
     * @param disposes    留样数据集合
     */
    private void setIsKeepSample(DtoAnalyzeItem analyzeItem, List<DtoSampleDispose> disposes) {
        if (StringUtil.isNotEmpty(disposes)) {
            //获取所有的样品留样信息ID
            List<String> disposeIds = disposes.stream().map(DtoSampleDispose::getId).collect(Collectors.toList());
            //根据留样Id获取留样的测试项目的Id
            List<DtoSampleDispose2Test> testOfDispose = sampleDispose2TestRepository.findBySampleDisposeIdIn(disposeIds);
            List<String> analyzeItemIds = testOfDispose.stream().map(DtoSampleDispose2Test::getTestId).collect(Collectors.toList());
            List<String> existDispose = analyzeItemIds.stream()
                    .filter(toTest -> analyzeItem.getId().equals(toTest))
                    .collect(Collectors.toList());
            analyzeItem.setIsKeepSample(StringUtil.isNotEmpty(existDispose));
        } else {
            analyzeItem.setIsKeepSample(false);
        }
    }

    /**
     * 获取时间最新的数据
     *
     * @param list 领取数据
     * @return 结果
     */
    public DtoSampleReserve selectLastOne(List<DtoSampleReserve> list, String sampleId) {
        List<DtoSampleReserve> reserveOfNewDate = list.stream().filter(p -> sampleId.equals(p.getSampleId())).sorted(Comparator.comparing(DtoSampleReserve::getCreateDate).reversed()).collect(Collectors.toList());
        DtoSampleReserve reserve = null;
        if (StringUtil.isNotEmpty(reserveOfNewDate)) {
            reserve = reserveOfNewDate.get(0);
        }
        return reserve;
    }

    /**
     * 检查分析数据是否全部设置领样
     *
     * @param testToMap       总称id下对应子测试项目map
     * @param parentTests     总称测试项目
     * @param sampleGroup     样品分组数据
     * @param analyseDataList 分析数据
     * @param containsItemIds 测试项目ids
     */
    private void checkSampleReceiveDate(Map<String, List<DtoTest>> testToMap,
                                        List<DtoTest> parentTests,
                                        DtoSampleGroup sampleGroup,
                                        List<DtoAnalyseData> analyseDataList,
                                        List<String> containsItemIds) {

        for (DtoTest test : parentTests) {
            assert test != null;
            List<DtoTest> dtoTests = testToMap.get(test.getId());
            List<String> sunTestIds = dtoTests.stream().map(DtoTest::getId).collect(Collectors.toList());
            // 待总称的样品id
            String sampleId = sampleGroup.getSampleId();
            // 获取当前样品下的总称下的分析数据
            List<DtoAnalyseData> collect = analyseDataList.stream().filter(p -> sampleId.equals(p.getSampleId())
                    && sunTestIds.contains(p.getTestId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(collect)) {
                // 判断当前总称下的所有分析项目是都全部设置领样日期
                Date targetDate = DateUtil.stringToDate("1753-01-01 00:00:00", DateUtil.FULL);
                List<DtoAnalyseData> collect1 = collect.stream().filter(p -> p.getSampleReceiveDate().compareTo(targetDate) != 0).collect(Collectors.toList());
                if (collect1.size() == collect.size()) {
                    containsItemIds.add(test.getAnalyzeItemId());
                }
            }
        }
    }


    @Autowired
    public void setReceiveSubSampleRecordRepository(ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository) {
        this.receiveSubSampleRecordRepository = receiveSubSampleRecordRepository;
    }

    @Autowired
    public void setSampleReserve2TestRepository(SampleReserve2TestRepository sampleReserve2TestRepository) {
        this.sampleReserve2TestRepository = sampleReserve2TestRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setSampleDispose2TestRepository(SampleDispose2TestRepository sampleDispose2TestRepository) {
        this.sampleDispose2TestRepository = sampleDispose2TestRepository;
    }

    @Autowired
    public void setSampleDisposeRepository(SampleDisposeRepository sampleDisposeRepository) {
        this.sampleDisposeRepository = sampleDisposeRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    private PersonRepository personRepository;

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    public void setSampleGroupRepository(SampleGroupRepository sampleGroupRepository) {
        this.sampleGroupRepository = sampleGroupRepository;
    }

    @Autowired
    public void setSampleTypeGroup2TestRepository(SampleTypeGroup2TestRepository sampleTypeGroup2TestRepository) {
        this.sampleTypeGroup2TestRepository = sampleTypeGroup2TestRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setQualityControlRepository(QualityControlRepository qualityControlRepository) {
        this.qualityControlRepository = qualityControlRepository;
    }

    @Autowired
    public void setReceiveSampleRecordRepository(ReceiveSampleRecordRepository receiveSampleRecordRepository) {
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
    }

    @Autowired
    public void setSampleGroupService(SampleGroupService sampleGroupService) {
        this.sampleGroupService = sampleGroupService;
    }
}
