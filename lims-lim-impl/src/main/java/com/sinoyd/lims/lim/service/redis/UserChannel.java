package com.sinoyd.lims.lim.service.redis;

import com.jsoniter.JsonIterator;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;

/**
 * 用户通道
 * <AUTHOR>
 * @version V1.0.0 2019/11/28
 * @since V100R001
 */
@Component
@Service
public class UserChannel {

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private CommonRepository commonRepository;

    @Async
    @Transactional
    public void createUser(String userInfo) {
        if (StringUtils.isNotNullAndEmpty(userInfo)) {
            Map<String, Object> userModel = JsonIterator.deserialize(JsonIterator.deserialize(userInfo).toString(), Map.class);
            String name = (String) userModel.get("displayName");
            String id = (String) userModel.get("id");

            DtoPerson dtoPerson = personRepository.findOne(id);

            if (StringUtil.isNull(dtoPerson)) {
                dtoPerson = new DtoPerson();
                dtoPerson.setId(id);
                dtoPerson.setCName(name);
                dtoPerson.setDeptId((String) userModel.get("deptGuid"));
                if (StringUtils.isNotNullAndEmpty(name)) {
                    dtoPerson.setFullPinYin(PinYinUtil.getFullSpell(name));
                    dtoPerson.setPinYin(PinYinUtil.getFirstSpell(name));
                }
                if (StringUtil.isNotNull(userModel.get("sexCode"))) {
                    dtoPerson.setSex(Integer.parseInt((String) userModel.get("sexCode")));
                }else {
                    dtoPerson.setSex(EnumLIM.EnumSex.男.getValue());
                }
                dtoPerson.setOrderNum((Integer) userModel.get("sortNum"));
                dtoPerson.setBirthDay((Date) userModel.get("birthDay"));
                dtoPerson.setEmail((String) userModel.get("email"));
                dtoPerson.setStatus(EnumLIM.EnumPersonStatus.在职.getValue());
                dtoPerson.setMobile((String) userModel.get("telephone"));
                dtoPerson.setUserNo((String) userModel.get("empNum"));
                dtoPerson.setOrgId((String) userModel.get("orgGuid"));
                personRepository.save(dtoPerson);
            } else if (dtoPerson.getIsDeleted()) { //假删的还原
                dtoPerson.setIsDeleted(false);
                commonRepository.merge(dtoPerson);
            }
        }
    }
}
