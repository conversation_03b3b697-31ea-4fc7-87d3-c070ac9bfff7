package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseAwait;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseProgress;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseWorkView;

import java.util.*;

/**
 * 分析数据缓存操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
public interface AnalyseDataCacheService {
    /**
     * 根据人员id 获取实验室分析工作总览信息
     *
     * @param personId 人员id
     * @return 工作总览信息
     */
    DtoAnalyseWorkView findAnalyseStatistics(String personId);


    /**
     * 根据人员id 获取实验室分析工作进度
     *
     * @param personId 人员id
     * @return 工作进度
     */
    DtoAnalyseProgress findAnalyseProgress(String personId);


    /**
     * 获取待检样品的数据
     *
     * @param baseCriteria 分析人员id
     * @return 返回待检分析样品数据
     */
    List<DtoAnalyseAwait> findWaitAnalyseDataByPersonIdAndTestId(BaseCriteria baseCriteria);

    /**
     * 刷新实验室分析缓存
     */
    void refreshAnalyseCache();

    /**
     * 保存分析人员相关的缓存信息
     * @param analyseId 分析人员id
     * @param orgId 组织机构id
     */
    void saveAnalyseDataCache(String analyseId,String orgId);

    /**
     * 保存缓存的待检、未分配的样品的数据
     *
     * @param analystId 分析人员id
     * @param orgId 组织机构id
     */
    void saveAwaitInfo(String analystId,String orgId);

    /**
     * 写入检测中及之后的数据
     *
     * @param analystId 分析人员id
     * @param orgId 组织机构id
     */
    void saveAnalyseInfo(String analystId,String orgId);

    /**
     * 写入检测单复核审核的数据
     *
     * @param personId 人员id
     */
    void saveAuditInfo(String personId,String orgId);

    /**
     * 提交
     *
     * @param analystId 分析人员id
     * @param times     次数
     */
    void saveSubmitTimes(String analystId, Integer times);

    /**
     * 复审
     *
     * @param personId 人员id
     * @param times    次数
     */
    void saveAuditTimes(String personId, Integer times);
}
