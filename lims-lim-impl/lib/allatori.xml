<config>
	<input>
		<jar in="../target/lims-lim-impl.jar" out="../target/lims-lim-impl.jar" />
	</input>

	<keep-names>
		<class access="protected+">
			<field access="protected+" />
			<method access="protected+" parameters="keep" />
		</class>
	</keep-names>

	<ignore-classes>
		<class template="class *springframework*" />
		<class template="class *shardingjdbc*" />
		<class template="class *jni*" />
		<class template="class *persistence*" />
		<class template="class *activiti.engine*" />
		<class template="class com.sinoyd.lims.lim.criteria*" />
	</ignore-classes>
</config>
