package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.customer.DtoErrorInfo;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 报告提交
 * <AUTHOR>
 */
@Component(IFileNameConstant.SubmitStrategyKey.FINISH_REPORT)
public class FinishReportStrategy extends AbsSubmitRestrictStrategy {
    @Override
    public List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        List<String> projectIds = (List<String>) objMap;
        List<DtoProject> projectList = projectService.findAll(projectIds);
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.报告完成.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.报告完成.getModuleName());
        List<String> msgList = new ArrayList<>();
        projectList.forEach(p -> {
            DtoErrorInfo errorInfo = new DtoErrorInfo(Boolean.TRUE);
            projectService.checkReportCondition(p.getId(), errorInfo);
            String msg = errorInfo.getMsg();
            if (StringUtil.isNotEmpty(msg)) {
                msgList.add(String.format("%s%s", p.getProjectCode(), msg));
            }
        });
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        restrictVoList.add(restrictVo);
        return restrictVoList;
    }
}
