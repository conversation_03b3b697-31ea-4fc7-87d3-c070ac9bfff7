package com.sinoyd.lims.monitor.dto.lims;

import com.sinoyd.lims.monitor.entity.OicInformation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoOicInformation实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_MONITOR_OicInformation")
 @Data
 @DynamicInsert
 public  class DtoOicInformation extends OicInformation {
   private static final long serialVersionUID = 1L;

 /**
  * 分析项目集合
  */
 @Transient
 private List<String> analysis;

 /**
  * 分析项目名称
  */
 @Transient
 private String analyzeItemName;



}