package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.repository.rcc.IndustryTypeRepository;
import com.sinoyd.base.service.IndustryTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 行业类型同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/21
 */
@Component
@DependsOn({"springContextAware"})
@Order(3)
@Slf4j
public class IndustryTypeSync extends AbsDataSync<DtoIndustryType> {

    private IndustryTypeService industryTypeService;

    private IndustryTypeRepository industryTypeRepository;

    /**
     * 数据比较
     *
     * @param testIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoIndustryType>> compareData(List<String> testIds) {
        //获取项目上全部行业类型
        List<DtoIndustryType> projectDataList = industryTypeService.findAll();
        //公共库中的行业类型
        List<DtoIndustryType> standardIndustryTypeList = queryStandardData();
        //比较数据
        return compareData(standardIndustryTypeList, projectDataList);
    }

    /**
     * 同步数据
     *
     * @param testIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> testIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoIndustryType>> compareResult = compareData(testIds);
        Optional<DtoDataCompareResult<DtoIndustryType>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoIndustryType errorDto = null;
            try {
                for (DtoIndustryType dtoIndustryType : r.getAddDataList()) {
                    errorDto = dtoIndustryType;
                    if (industryTypeRepository.findOne(dtoIndustryType.getId()) != null) {
                        industryTypeService.update(dtoIndustryType);
                    }else{
                        industryTypeService.save(dtoIndustryType);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.行业类型.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.行业类型.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/base/industryType";
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.行业类型.getValue();
    }

    @Autowired
    @Lazy
    public void setIndustryTypeService(IndustryTypeService industryTypeService) {
        this.industryTypeService = industryTypeService;
    }

    @Autowired
    public void setIndustryTypeRepository(IndustryTypeRepository industryTypeRepository) {
        this.industryTypeRepository = industryTypeRepository;
    }
}