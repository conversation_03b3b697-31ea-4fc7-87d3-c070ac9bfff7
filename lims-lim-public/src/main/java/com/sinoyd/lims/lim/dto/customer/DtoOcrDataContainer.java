package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParamData;
import lombok.Data;

import java.util.List;

/**
 * DtoOcrDataContainer 识别结果容器
 * <AUTHOR>
 * @version V1.0.0 2024/02/22
 * @since V100R001
 */
@Data
public class DtoOcrDataContainer {

    /**
     * ocr匹配结果数据
     */
    private List<DtoOcrConfigParamData> dataList;

    /**
     * ocr步骤
     */
    private String ocrParse;

    /**
     * ai回答结果
     */
    private String aiAnswer;

    /**
     * 识别记录id
     */
    private String recordId;
}
