package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.criteria.TestFormulaCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormula;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaTheSame;
import com.sinoyd.lims.lim.service.TestFormulaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.http.HttpResponse;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 测试项目公式
 * <AUTHOR>
 * @version V1.0.0 2019/12/17
 * @since V100R001
 */
@Api(tags = "测试项目公式")
@RestController
@RequestMapping("/api/lim/testFormula")
public class TestFormulaController extends ExceptionHandlerController<TestFormulaService> {

    @ApiOperation(value = "分页动态条件查询人员", notes = "分页动态条件查询人员")
    @GetMapping
    public RestResponse<List<DtoTestFormula>> findByPage(TestFormulaCriteria testFormulaCriteria) {
        RestResponse<List<DtoTestFormula>> restResp = new RestResponse<>();
        PageBean<DtoTestFormula> page = super.getPageBean();
        service.findByPage(page, testFormulaCriteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    @ApiOperation(value = "分页动态条件查询人员", notes = "分页动态条件查询人员")
    @PostMapping("/query")
    public RestResponse<List<DtoTestFormula>> findPostByPage(@RequestBody TestFormulaCriteria testFormulaCriteria) {
        return findByPage(testFormulaCriteria);
    }

    /**
     * 新增人员
     *
     * @param entity 人员实体
     * @return RestResponse<DtoPerson>
     */
    @ApiOperation(value = "新增公式", notes = "新增公式")
    @PostMapping
    public RestResponse<DtoTestFormula> save(@RequestBody DtoTestFormula entity) {
        RestResponse<DtoTestFormula> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoTestFormula data = service.saveTestFormula(entity);
        restResp.setData(data);
        restResp.setCount(1);
        return restResp;
    }

    /**
     * 更新人员
     *
     * @param entity 人员实体
     * @return RestResponse<DtoPerson>
     */
    @ApiOperation(value = "更新公式", notes = "更新公式")
    @PutMapping
    public RestResponse<DtoTestFormula> update(@RequestBody DtoTestFormula entity) {
        RestResponse<DtoTestFormula> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoTestFormula data = service.updateTestFormula(entity);
        restResp.setData(data);
        restResp.setCount(1);
        return restResp;
    }

    /**
     * 修改部分公式
     *
     * @param entity 公式
     * @return RestResponse<DtoPerson>
     */
    @ApiOperation(value = "修改部分公式", notes = "修改部分公式")
    @PutMapping("/savePartFormula")
    public RestResponse<DtoTestFormula> updatePartFormula(@RequestBody DtoTestFormula entity) {
        RestResponse<DtoTestFormula> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.saveParamsPartFormula(entity, entity.getId());
        return restResp;
    }


    @ApiOperation(value = "按主键id查询公式", notes = "按主键id查询公式")
    @GetMapping("/{id}")
    public RestResponse<DtoTestFormula> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoTestFormula> restResp = new RestResponse<>();
        DtoTestFormula dtoTestFormula = service.findOne(id);
        restResp.setData(dtoTestFormula);
        restResp.setRestStatus(StringUtil.isNull(dtoTestFormula) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    @ApiOperation(value = "根据id批量删除公式", notes = "根据id批量删除公式")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.delete(ids));
        return restResp;
    }


    @ApiOperation(value = "判断公式是否相同", notes = "判断公式是否相同")
    @PostMapping("/same")
    public RestResponse<Boolean> isSameTestFormula(@RequestBody DtoTestFormulaTheSame dtoTestFormulaTheSame) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        restResp.setData(service.isSameTestFormula(dtoTestFormulaTheSame));
        return restResp;
    }

    @ApiOperation(value = "保存部分参数公式", notes = "保存部分参数公式")
    @PostMapping("/part")
    public RestResponse<DtoParamsPartFormula> isSameTestFormula(@RequestBody DtoParamsPartFormula paramsPartFormula) {
        RestResponse<DtoParamsPartFormula> restResp = new RestResponse<>();
        restResp.setData(service.saveParamsPartFormula(paramsPartFormula));
        return restResp;
    }

    @ApiOperation(value = "复制公式", notes = "复制公式")
    @PostMapping("/copy/{id}/{sampleTypeId}")
    public RestResponse<String> copyTestFormula(@PathVariable(name = "id") String id, @PathVariable(name = "sampleTypeId") String sampleTypeId, @RequestBody List<String> testIds) {
        RestResponse<String> restResp = new RestResponse<>();
        service.copyTestFormula(id, sampleTypeId, testIds);
        return restResp;
    }

    @ApiOperation(value = "导出公式", notes = "导出公式")
    @GetMapping("/export")
    public RestResponse<String> export( TestFormulaCriteria testFormulaCriteria,HttpServletResponse response) {
        RestResponse<String> restResp = new RestResponse<>();
        PageBean<DtoTestFormula> page = super.getPageBean();
        service.export(page,testFormulaCriteria,response);
        return restResp;
    }

    @ApiOperation(value = "清空公式", notes = "清空公式")
    @DeleteMapping("/remove/{id}/{formulaType}")
    public RestResponse<String> removeFormula(@PathVariable(name = "id") String id, @PathVariable(name = "formulaType") Integer formulaType) {
        RestResponse<String> restResp = new RestResponse<>();
        service.removeFormulaByType(id, formulaType);
        return restResp;
    }
}
