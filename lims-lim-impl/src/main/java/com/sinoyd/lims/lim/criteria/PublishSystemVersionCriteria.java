package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;

/**
 * 版本发布管理查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022-11-9
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PublishSystemVersionCriteria extends BaseCriteria {

    /**
     * 开始日期
     */
    private String startTime;

    /**
     * 结束日期
     */
    private String endTime;

    /**
     * 是否发布 false发布 true不发布
     */
    private Boolean isPublish;

    /**
     * 是否产品 false是产品 true不是产品
     */
    private Boolean isProduct;

    /**
     * 发布人
     */
    private String publishPerson;

    /**
     * 版本号
     */
    private String versionNum;

    /**
     * 更新内容
     */
    private String updateContent;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.publishDate >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and a.publishDate < :endTime");
            values.put("endTime", c.getTime());
        }

        if (isPublish != null) {
            condition.append(" and a.isPublish = :isPublish");
            values.put("isPublish", isPublish);
        }

        if (isProduct != null) {
            condition.append(" and a.isProduct = :isProduct");
            values.put("isProduct", isProduct);
        }

        if (StringUtil.isNotEmpty(publishPerson)) {
            condition.append(" and a.publishPerson like :publishPerson");
            values.put("publishPerson", "%" + publishPerson + "%");
        }

        if (StringUtil.isNotEmpty(versionNum)) {
            condition.append(" and a.versionNum like :versionNum");
            values.put("versionNum", "%" + versionNum + "%");
        }

        if (StringUtil.isNotEmpty(updateContent)) {
            condition.append(" and a.updateContent like :updateContent");
            values.put("updateContent", "%" + updateContent + "%");
        }

        return condition.toString();
    }
}