package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestExpandRepository;
import com.sinoyd.lims.lim.service.TestExpandService;
import com.sinoyd.lims.lim.service.TestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 修约规则
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Service
@Slf4j
public class TestExpandServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoTestExpand, String, TestExpandRepository> implements TestExpandService {


    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private DimensionService dimensionService;


    @Override
    public void findByPage(PageBean<DtoTestExpand> page, BaseCriteria criteria) {
        page.setEntityName("DtoTestExpand a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
    }

    /**
     * 通过testID SampleTypeId删除(真删)
     */
    @Transactional
    @Override
    public Boolean delete(String testId, String sampleTypeId) {
        Boolean flag = false;
        if (repository.delete(testId, sampleTypeId) > 0) {
            List<DtoTestExpand> testExpands = deleteRedis(testId, sampleTypeId);
            saveRedis(testExpands, testId);
            flag = true;
        }
        return flag;
    }

    @Override
    public List<DtoTestExpand> findRedisByTestIds(List<String> testIds) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestExpand.getValue());
        List<Object> dataList = redisTemplate.opsForHash().multiGet(key, testIds);
        return setMaps(dataList);
    }

    @Override
    public List<DtoTestExpand> findRedisByTestId(String testId) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestExpand.getValue());
        Object json = redisTemplate.opsForHash().get(key, testId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            return turnRedisTestExpand(json);
        }
        List<DtoTestExpand> testExpands = repository.findByTestId(testId);
        saveRedis(testExpands, testId);
        return testExpands;
    }

    /**
     * 通过testID SampleTypeId获取
     */
    @Override
    public DtoTestExpand get(String testId, String sampleTypeId) {
        return repository.findByTestIdAndSampleTypeId(testId, sampleTypeId);
    }

    /**
     * 保存修约规则
     */
    @Transactional
    @Override
    public DtoTestExpand save(DtoTestExpand entity) {
        DtoTestExpand testExpand = repository.findByTestIdAndSampleTypeId(entity.getTestId(), entity.getSampleTypeId());
        if ((StringUtil.isNotNull(testExpand) && !testExpand.getDimensionId().equals(entity.getDimensionId())) || StringUtil.isNull(testExpand)) {
            dimensionService.incrementOrderNum(Collections.singletonList(entity.getDimensionId()));
        }
        repository.delete(entity.getTestId(), entity.getSampleTypeId());
        entity.setId(UUIDHelper.NewID());
        DtoTestExpand item = super.save(entity);
        List<DtoTestExpand> testExpands = deleteRedis(entity.getTestId(), entity.getSampleTypeId());
        testExpands.add(item);
        saveRedis(testExpands, entity.getTestId());
        DtoTest test = testService.findOne(entity.getTestId());
        //将主表里面的有效位数及小数位数修改（主要是大类）
        if (StringUtil.isNotNull(test) && test.getSampleTypeId().equals(entity.getSampleTypeId())) {
            test.setMostSignificance(entity.getMostSignificance());
            test.setMostDecimal(entity.getMostDecimal());
            testService.update(test);
        }
        return item;
    }

    @Override
    public  List<String> findSampleTypeIdByTestId(String testId) {
        return repository.findSampleTypeIdByTestId(testId);
    }

    @Override
    public List<DtoTestExpand> findByTestIds(List<String> testIds) {
        return repository.findByTestIdIn(testIds);
    }

    /**
     * 从redis中获取相应的数据
     *
     * @param dataList 数据集合
     * @return 返回排口相应的数据
     */
    private List<DtoTestExpand> setMaps(List<Object> dataList) {
        List<DtoTestExpand> itemList = new ArrayList<>();
        TypeLiteral<List<DtoTestExpand>> typeLiteral = new TypeLiteral<List<DtoTestExpand>>() {
        };
        for (Object s : dataList) {
            if (StringUtil.isNotNull(s)) {
                try {
                    List<DtoTestExpand> item = JsonIterator.deserialize(s.toString(), typeLiteral);
                    if (StringUtil.isNotNull(item)) {
                        itemList.addAll(item);
                    }
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
        }
        return itemList;
    }

    /**
     * 保存相应的redis数据
     *
     * @param testExpands 测试项目扩展ids
     */
    private void saveRedis(List<DtoTestExpand> testExpands,String testId) {
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestExpand.getValue());
        redisTemplate.opsForHash().put(key, testId, JsonStream.serialize(testExpands));
    }

    /**
     * 先清除测试扩展信息
     * @param testId 测试项目ID
     * @param sampleTypeId 检测类型ID
     * @return 返回测试扩展信息
     */
    private   List<DtoTestExpand>  deleteRedis(String testId,String sampleTypeId) {
        List<DtoTestExpand> testExpands = new ArrayList<>();
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestExpand.getValue());
        Object json = redisTemplate.opsForHash().get(key, testId);
        if (StringUtils.isNotNullAndEmpty(json)) {
            testExpands = turnRedisTestExpand(json);
            Optional<DtoTestExpand> optionalTestExpand = testExpands.stream().filter(p -> p.getTestId().equals(testId) && p.getSampleTypeId().equals(sampleTypeId)).findFirst();
            if (optionalTestExpand.isPresent()) {
                testExpands.remove(optionalTestExpand.get());
            }
        }
        return testExpands;
    }


    /**
     * 转换Redis数据为实体数据
     *
     * @param json Redis数据
     * @return 实体数据集合
     */
    private List<DtoTestExpand> turnRedisTestExpand(Object json){
        List<DtoTestExpand> testExpands = new ArrayList<>();
        try{
            //解析相应的配置数据
            TypeLiteral<List<DtoTestExpand>> typeLiteral = new TypeLiteral<List<DtoTestExpand>>() {
            };
            testExpands = JsonIterator.deserialize(json.toString(), typeLiteral);
        }catch (Exception e){
            try{
                //解析相应的配置数据
                TypeLiteral<DtoTestExpand> typeLiteral = new TypeLiteral<DtoTestExpand>() {
                };
                testExpands.add(JsonIterator.deserialize(json.toString(), typeLiteral));
            }catch (Exception ex){
                log.error(e.getMessage(), e);
                throw new BaseException("转换测试项目拓展缓存数据出错，请刷新缓存!");
            }
        }
        return testExpands;
    }
}