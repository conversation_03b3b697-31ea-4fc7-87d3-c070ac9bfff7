package com.sinoyd.lims.pro.service.impl;


import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.criteria.PollutionStatisticsCriteria;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.service.PollutionStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 污染源统计操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/05/07
 * @since V100R001
 */
@Service
public class PollutionStatisticsServiceImpl extends BaseJpaServiceImpl<DtoProject, String, ProjectRepository> implements PollutionStatisticsService {

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Override
    public List<DtoProject> findDtoProject(PageBean<DtoProject> pb, BaseCriteria pollutionStatisticsCriteria) {
        PollutionStatisticsCriteria criteria = (PollutionStatisticsCriteria) pollutionStatisticsCriteria;
        //获取前端传的检测id
        String sampleTypeId = criteria.getSampleTypeId();
        //如果前端传了检测id，就两表关联查询
        if (StringUtil.isNotEmpty(sampleTypeId)) {
            pb.setEntityName("DtoProject a,DtoSampleFolder b");
        } else {
            pb.setEntityName("DtoProject a ");
        }
        pb.setSelect("select a ");
        comRepository.findByPage(pb, pollutionStatisticsCriteria);
        //将项目去重
        List<DtoProject> dtoProjects = new ArrayList<>(new HashSet<>(pb.getData()));
        dtoProjects.sort(Comparator.comparing(DtoProject::getInputTime, Comparator.reverseOrder()));
        if (StringUtil.isNotNull(dtoProjects)) {
            //只显示污染源的项目，通过唯一标识code
            List<DtoProjectType> projectType = projectTypeService.findByTypeCode("WR");
            if (projectType.size() > 0) {
                List<String> typeIds = projectType.stream().map(DtoProjectType::getId).collect(Collectors.toList());
                dtoProjects = dtoProjects.parallelStream().filter(p -> typeIds.contains(p.getProjectTypeId())).collect(Collectors.toList());
            }
        }
        return dtoProjects;
    }
}