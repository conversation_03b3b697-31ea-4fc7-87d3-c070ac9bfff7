package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoLogForRecord;

import java.util.List;


/**
 * LogForRecord数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
public interface LogForRecordRepository extends IBaseJpaPhysicalDeleteRepository<DtoLogForRecord, String> {

    List<DtoLogForRecord> findByObjectId(String objectId);
    List<DtoLogForRecord> findByObjectIdIn(List<String> objectIds);
}