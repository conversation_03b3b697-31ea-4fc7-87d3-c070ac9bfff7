package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;

/**
 * 分析方法导出查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2023-09-07
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeMethodExportCriteria extends BaseCriteria {

    /**
     * 每页记录数
     */
    private Integer rows;

    /**
     * 页数
     */
    private Integer page;

    /**
     * 排序
     */
    private String sort;

    /**
     * 分析方法名称
     */
    private String key;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 开始时间
     */
    private String dtBegin;

    /**
     * 结束时间
     */
    private String dtEnd;

    /**
     * 试剂类型（1：一般试剂 2：标准溶液）
     */
    private String reagentType;

    /**
     * 分析方法key
     */
    private String  methodKey;

    private String workSheetFolderId;

    @Override
    public String getCondition() {
        StringBuilder condition  = new StringBuilder();
        if (StringUtil.isNotEmpty(this.dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and a.configDate >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtil.isNotEmpty(this.dtEnd)) {
            Calendar calendar = Calendar.getInstance();
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            calendar.setTime(to);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            to = calendar.getTime();
            condition.append(" and a.configDate < :dtEnd");
            values.put("dtEnd", to);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (a.reagentName like :key or a.reagentSpecification like :key or a.configurationSolution like :key or a.course like :key or a.context like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.reagentType) && !this.reagentType.equals("0")) {
            condition.append(" and a.reagentType = :reagentType ");
            values.put("reagentType", Integer.valueOf(this.reagentType));
        }
        return condition.toString();
    }
}