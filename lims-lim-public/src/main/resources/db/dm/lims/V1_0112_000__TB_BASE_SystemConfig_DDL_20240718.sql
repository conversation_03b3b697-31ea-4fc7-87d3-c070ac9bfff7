ALTER TABLE TB_BASE_SystemConfig ADD COLUMN linkMan VARCHAR(100);
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN legalRepresentative VARCHAR(100);
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN establishTime DATETIME;
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN labArea VARCHAR(100);
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN staffCount INT;
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN registeredCapital VARCHAR(100);
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN certificate VARCHAR(100);
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN cmaScope TEXT;
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN cnasScope TEXT;
COMMENT ON COLUMN TB_BASE_SystemConfig.linkMan IS '联系人';
COMMENT ON COLUMN TB_BASE_SystemConfig.legalRepresentative IS '法人代表';
COMMENT ON COLUMN TB_BASE_SystemConfig.establishTime IS '成立时间';
COMMENT ON COLUMN TB_BASE_SystemConfig.labArea IS '实验室面积';
COMMENT ON COLUMN TB_BASE_SystemConfig.staffCount IS '在职人数';
COMMENT ON COLUMN TB_BASE_SystemConfig.registeredCapital IS '注册资金';
COMMENT ON COLUMN TB_BASE_SystemConfig.certificate IS '资质证书';
COMMENT ON COLUMN TB_BASE_SystemConfig.cmaScope IS 'CMA能力范围';
COMMENT ON COLUMN TB_BASE_SystemConfig.cnasScope IS 'CNAS能力范围';