package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.TestPostCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;
import com.sinoyd.lims.lim.service.TestPostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 测试岗位管理
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
@Api(tags = "测试岗位管理: 测试岗位管理服务")
@RestController
@RequestMapping("/api/lim/testPost")
@Validated
public class TestPostController extends BaseJpaController<DtoTestPost, String, TestPostService> {


    /**
     * 分页动态条件查询测试岗位
     *
     * @param testPostCriteria 条件
     * @return RestResponse<List < DtoTestPost>>
     */
    @ApiOperation(value = "分页动态条件查询测试岗位", notes = "分页动态条件查询测试岗位")
    @GetMapping
    public RestResponse<List<DtoTestPost>> findByPage(TestPostCriteria testPostCriteria) {
        RestResponse<List<DtoTestPost>> restResp = new RestResponse<>();
        PageBean<DtoTestPost> page = super.getPageBean();
        service.findByPage(page, testPostCriteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 获取岗位树
     *
     * @return RestResponse<List < DtoTestPost>>
     */
    @ApiOperation(value = "获取岗位树", notes = "获取岗位树")
    @GetMapping("/tree")
    public RestResponse<List<DtoTestPost>> getTestPostTree() {
        RestResponse<List<DtoTestPost>> restResp = new RestResponse<>();
        List<DtoTestPost> testPostList = service.getTestPostTree();
        restResp.setRestStatus(StringUtil.isEmpty(testPostList) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(testPostList);
        restResp.setCount(testPostList.size());
        return restResp;
    }

    /**
     * 新增测试岗位
     *
     * @param entity 测试岗位实体
     * @return RestResponse<DtoTestPost>
     */
    @ApiOperation(value = "新增测试岗位", notes = "新增测试岗位")
    @PostMapping
    public RestResponse<DtoTestPost> save(@Validated @RequestBody DtoTestPost entity) {
        RestResponse<DtoTestPost> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoTestPost testPost = service.save(entity);
        restResp.setData(testPost);
        restResp.setCount(1);
        return restResp;
    }

    /**
     * 更新测试岗位
     *
     * @param entity 测试岗位实体
     * @return RestResponse<DtoTestPost>
     */
    @ApiOperation(value = "更新测试岗位", notes = "更新测试岗位")
    @PutMapping
    public RestResponse<DtoTestPost> update(@Validated @RequestBody DtoTestPost entity) {
        RestResponse<DtoTestPost> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoTestPost testPost = service.update(entity);
        restResp.setData(testPost);
        restResp.setCount(1);
        return restResp;
    }

    /**
     * 根据id删除测试岗位
     *
     * @param id 删除Id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除测试岗位", notes = "根据id删除测试岗位")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@RequestBody String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.deleteTestPost(id);
        return restResp;
    }

    /**
     * 根据id批量删除测试岗位
     *
     * @param ids 删除ids
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除测试岗位", notes = "根据id批量删除测试岗位")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.deleteTestPost(ids);
        return restResp;
    }

    /**
     * 按主键查询测试岗位信息(包括人员及测试项目配置)
     *
     * @param id 主键id
     * @return RestResponse<DtoTestPost>
     */
    @ApiOperation(value = "按主键id查询测试岗位", notes = "按主键id查询测试岗位")
    @GetMapping("/{id}")
    public RestResponse<DtoTestPost> find(@PathVariable(name = "id") String id, TestPostCriteria testPostCriteria) {
        RestResponse<DtoTestPost> restResp = new RestResponse<>();
        DtoTestPost testPost = service.findTestPost(id, testPostCriteria);
        restResp.setData(testPost);
        restResp.setRestStatus(StringUtil.isNull(testPost) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 修改测试岗位配置（人员配置，测试项目配置）
     *
     * @param entity 测试岗位实体
     * @return RestResponse<DtoTestPost>
     */
    @ApiOperation(value = "新增/修改测试岗位配置", notes = "新增/修改测试岗位配置")
    @PostMapping("/config")
    public RestResponse<DtoTestPost> updateConfig(@Validated @RequestBody DtoTestPost entity) {
        RestResponse<DtoTestPost> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoTestPost testPost = service.updateConfig(entity);
        restResp.setData(testPost);
        restResp.setCount(1);
        return restResp;
    }

    /**
     * 按照人员id查找该人员配置的所有岗位
     *
     * @param personId 人员id
     * @return RestResponse<DtoTestPost>
     */
    @ApiOperation(value = "按照人员id查找该人员配置的所有岗位", notes = "按照人员id查找该人员配置的所有岗位")
    @GetMapping("/person/{personId}")
    public RestResponse<List<DtoTestPost>> findByPersonId(@PathVariable(name = "personId") String personId) {
        RestResponse<List<DtoTestPost>> restResp = new RestResponse<>();
        List<DtoTestPost> testPostList = service.findByPerson(personId);
        restResp.setData(testPostList);
        restResp.setRestStatus(StringUtil.isNull(testPostList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 根据采样小组id和测试项目id删除数据
     *
     * @param testPost 数据载体
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "根据采样小组id和测试项目id删除数据", notes = "根据采样小组id和测试项目id删除数据")
    @DeleteMapping("/deleteTest")
    public RestResponse<Void> deleteTest(@RequestBody DtoTestPost testPost) {
        RestResponse<Void> response = new RestResponse<>();
        service.deleteTest(testPost.getId(), testPost.getTestIdList());
        return response;
    }

    /**
     * 根据采样小组id和测试项目id删除数据
     *
     * @param testPost 数据载体
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "根据采样小组id和测试项目id新增数据", notes = "根据采样小组id和测试项目id新增数据")
    @PostMapping("/addTest")
    public RestResponse<Void> addTest(@RequestBody DtoTestPost testPost) {
        RestResponse<Void> response = new RestResponse<>();
        service.addTest(testPost.getId(), testPost.getTestIdList());
        return response;
    }

}