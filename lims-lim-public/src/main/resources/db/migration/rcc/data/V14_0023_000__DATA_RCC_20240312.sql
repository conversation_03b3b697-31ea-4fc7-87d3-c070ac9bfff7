-- 炉渣报告检测结果表测试项目数量调整
update TB_LIM_ReportModule
set sampleCount = 4
where moduleCode = 'dtSolidStdTable';

-- 新增标准版报告化合物数据新组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('dabc62c4-138b-4d38-a25e-41681713e347', 'dtCompoundStdNewTable', '标准版化合物检测结果表新组件', 'dtCompoundStdNewTable',
        'dtCompoundSource', 60, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2024-03-12 14:02:12', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2024-03-12 14:02:12');


-- 标准版报告数据主表，化合物组件配置调整
update TB_LIM_ReportModule
set sonTableJson = '{"headModule":"dtWaterHeadStdTable", "bodyModule":"dtWasteWaterStdTable", "secondBodyModule":"dtCompoundStdNewTable", "thirdBodyModule":""}'
where moduleCode = 'wasteWaterStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '{"headModule":"dtSyHeadStdTable", "bodyModule":"dtSyStdTable", "secondBodyModule":"dtCompoundStdNewTable", "thirdBodyModule":""}'
where moduleCode = 'sYStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '{"headModule":"dtWaterHeadStdTable", "bodyModule":"dtUnOrgGasStdTable", "secondBodyModule":"dtCompoundStdNewTable", "thirdBodyModule":""}'
where moduleCode = 'unOrgGasStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '{"headModule":"dtWaterHeadStdTable", "bodyModule":"dtSolidStdTable", "secondBodyModule":"dtCompoundStdNewTable", "thirdBodyModule":""}'
where moduleCode = 'solidStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '{"headModule":"dtWaterHeadStdTable", "bodyModule":"dtNormalWaterStdTable", "secondBodyModule":"dtCompoundStdNewTable", "thirdBodyModule":""}'
where moduleCode = 'normalWaterStdDataSource';

