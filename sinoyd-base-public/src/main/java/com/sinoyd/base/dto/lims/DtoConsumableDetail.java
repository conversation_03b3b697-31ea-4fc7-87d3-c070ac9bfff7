package com.sinoyd.base.dto.lims;

import com.sinoyd.base.dto.customer.DtoImportConsumable;
import com.sinoyd.base.entity.ConsumableDetail;

import javax.persistence.*;
import javax.transaction.Transactional;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.math.BigDecimal;


/**
 * DtoConsumableDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_ConsumableDetail")
@Data
@DynamicInsert
public class DtoConsumableDetail extends ConsumableDetail {

    @Transient
    private String consumableName;

    @Transient
    private String specification;

    @Transient
    private String unit;

    @Transient
    private String codeInStation;

    @Transient
    private String purchaseDetailId;

    @Transient
    private String consumableCode;

    /**
     *  过期天数
     */
    @Transient
    private Integer expirationDays;


    /**
     * 创建人
     */
    @Transient
    private String creator;

    /**
     * 创建人名称
     */
    @Transient
    private String creatorName;


    /**
     * 入库存储新的id
     */
    @Transient
    private String newId;

    /**
     * 提醒人
     */
    @Transient
    private String sendWarnUser;

    /**
     * 设置导入实体值
     * @param importEntity 导入实体
     */
    public void importToEntity(DtoImportConsumable importEntity){
        setId(importEntity.getIsStandard()?importEntity.getId(): UUIDHelper.NewID());
        setParentId(importEntity.getId());
        setStorage(new BigDecimal(importEntity.getInventory()));
        setInventory(new BigDecimal(importEntity.getInventory()));
        setUnitPrice(StringUtil.isEmpty(importEntity.getDetail().getUnitPrice()) ? BigDecimal.ZERO : new BigDecimal(importEntity.getDetail().getUnitPrice()));
        setKeepPlace(importEntity.getDetail().getKeepPlace());
        setCheckerId(importEntity.getSendWarnUserId());
        setRemark(importEntity.getDetail().getRemark());
        setProductionCode(importEntity.getDetail().getProductionCode());
        setManufacturerName(importEntity.getDetail().getManufacturerName());
    }
}