INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('075c534a-e189-4e16-850c-77c62b610820','dtNoiseDayNightTable','标准版噪声昼夜检测结果表组件','dtNoiseDayNightTable','dtNoiseSource',10,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-11 15:35:25','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-11 15:35:25',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('1078837a-9b0c-4b6a-aa42-22768a65643e','sYStdDataSource','标准版送样类报告检测数据主表','dtDataSource','',0,0,'["dtSyHeadStdTable", "dtSyStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-09-17 19:37:21','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-09-17 19:37:21',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('1b246cac-93d0-4c83-a56a-02958f5f912c','dtSolidStdTable','标准版炉渣检测结果表组件','dtSolidStdTable','dtSolidSource',4,10,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:35:55','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:35:55',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('1b539b2f-5460-484a-a243-3580a5ceeb42','orgGasStdDataSource','标准版有组织报告检测数据主表','dtDataSource','',0,0,'["dtOrgHeadStdTable", "dtOrgStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-10-19 08:41:35','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-10-19 08:41:35',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('21714ba6-903c-4268-ab0f-8ed284e30eff','wasteWaterStdDataSource','标准版废水报告检测数据主表','dtDataSource','',0,0,'["dtWaterHeadStdTable", "dtWasteWaterStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:40:00','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:40:00',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('222bbd76-aac1-495d-9556-6be7292ba0f7','dtNormalWaterStdTable','标准版常规水检测结果表组件','dtNormalWaterStdTable','dtNormalWaterSource',4,10,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:40:41','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:40:41',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('2823a872-d972-49b5-9cb4-769c4028af32','dtOrgSmkZsSpdDataSource','标准版有组织表头烟气参数样品检测数据主表（批次）','dtOrgSmkZsSpdDataSource','',0,0,'["dtOrgToHeadStdTable", "dtOrgSmkStdTable", "dtOrgToZsStdTable", "dtOrgToSpdStdTable", "dtOrgToCpdStdTable"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-26 17:22:44','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-26 17:22:44','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('28f33429-0140-489e-837d-e7e535887907','noiseDayNightDataSource','标准版噪声昼夜报告检测数据主表','dtDataSource','',0,0,'["dtTableHeadDay", "dtTableHeadDayNight", "dtNoiseDayNightTable"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-11 15:07:01','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-11 15:07:01',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('2a89e1a2-201d-47cf-8b0c-f435f58ed218','dtUnOrgWeaHeadStdTable','标准版无组织气象条件表头组件 （批次）','dtUnOrgWeaHeadStdTable','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-19 10:23:24','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-19 10:23:24','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('3477d948-c1eb-418f-8fbd-81fc1295cee6','dtTechnicalRemarkStdTable','标准版技术备注信息检测结果表组件','dtTechnicalRemarkStdTable','dtTechnicalRemarkSource',10,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-07-27 14:53:06','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-07-27 14:53:06',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('37b51443-895e-460e-96d6-692c3ec610b4','groundWaterStdDataSource','标准版地表水报告检测数据主表','dtDataSource','',0,0,'["dtWaterHeadStdTable", "dtGroundWaterStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-20 09:00:08','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-20 09:00:08','1',null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('3988a0aa-0df5-4a7e-91e2-cd5cb7c23c13','dtSyStdTable','标准版送样检测结果表组件','dtSyStdTable','dtSySource',5,10,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-09-17 19:31:30','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-09-17 19:31:30',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('39a21865-22cb-4632-bfe2-806a09ef253f','outParallelStdDataSource','标准版现场平行检测数据主表','dtDataSource','',0,0,'["dtOutParallelStdTable", "dtPxCompoundStdTable"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-15 16:49:36','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-15 16:49:36',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('4167cdb1-2b7f-44b8-ac9c-bc4c5906f58d','dtNoiseTable','标准版噪声检测结果表组件','dtNoiseTable','dtNoiseSource',10,1,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:38:02','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:38:02',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('4327d2ae-f104-418e-be74-67f4072d06ec','dtUnOrgGasPjTable','无组织评价结果表组件','dtUnOrgGasPjTable','dtUnOrgGasPjSrc',10000,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-11-06 13:17:16','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-11-06 13:17:16',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('44c0ef90-1cee-4379-bdec-e502a875c0a7','dtCriterionStdTable','标准版技术依据信息检测结果表组件','dtCriterionStdTable','dtCriterionSource',10,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:36:16','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:36:16',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('4ccd38c3-00fd-45b4-b5aa-d2b26d249d6e','dtPxCompoundStdTable','标准版现场平行样化合物检测结果表组件','dtPxCompoundStdTable','dtPxCompoundSource',1,60,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-15 16:46:59','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-15 16:46:59',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('4f6f0596-8de1-4887-95b3-2d335a72a4a8','TransportBlankStdData','标准版运输空白样检测结果表组件','TransportBlankStdData','dtTransportBlankSource',1000,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-05-10 15:54:19','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-05-10 15:54:19','0','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('521ca0a9-ecd9-427a-9dd2-56834c0c2578','dtUnOrgToHeadStdTable','标准版无组织检测结果表头组件 （批次）','dtUnOrgToHeadStdTable','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-19 10:13:31','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-19 10:13:31','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('52d85129-2878-46fe-bdc0-6162f6140248','unOrgToStdDataSource','标准版无组织报告样品数据主表（批次）','dtUnOrgToDataSource','',0,0,'["dtUnOrgToHeadStdTable", "dtUnOrgToStdTable", "dtUnOrgToCpdStdTable"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-19 10:32:26','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-19 10:32:26','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('53a5e187-4d15-4ef8-8eb0-13895a8b41ff','dtOutParallelStdTable','标准版现场平行样检测结果表组件','dtOutParallelStdTable','dtOutParallelSource',1,13,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-15 16:45:19','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-15 16:45:19',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('5593eed0-245d-43a3-8ade-6c8e5a5e0e6c','soilStdDataSource','标准版土壤报告检测数据主表','dtDataSource','',0,0,'["dtWaterHeadStdTable", "dtSoilStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-13 15:15:37','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-13 15:15:37',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('55fe89e1-dc8c-4b86-b03b-e6dbf3143340','innerParallelStdData','标准版室内平行样检测结果表组件','InnerParallelStdData','dtParallelSource',10,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:39:02','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:39:02',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('56ecb137-470e-4e8d-8498-ca709dd20cb3','EquipBlankStdData','标准版仪器空白样检测结果表组件','EquipBlankStdData','dtEquipBlankSource',1000,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-05-10 15:58:34','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-05-10 15:58:34','0','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('5af3f142-1873-4e0b-80ef-867f1cddcc52','dtOrgHeadStdTable','标准版有组织报告表头组件','dtOrgHeadStdTable','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-10-19 09:04:55','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-10-19 09:04:55',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('5be0d7de-998b-4398-83a6-78a5b8721afd','dtCompoundStdTable','标准版化合物检测结果表组件','dtCompoundStdTable','dtCompoundSource',60,2,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:38:48','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:38:48',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('5bfeb24e-2589-4c9f-af29-2beab9cd1a2f','unOrgWeaToStdDataSource','标准版无组织报告检测数据主表（批次）','dtDataSource','',0,0,'["unOrgWeaStdDataSource", "unOrgToStdDataSource",  "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-19 10:52:51','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-19 10:52:51','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('6130da13-013a-4a25-a856-bd1f16fc691b','dtTableHeadPj','评价结果表头组件','dtTableHeadPj','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-11-06 13:14:53','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-11-06 13:14:53',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('6197edbb-43de-4c89-aa55-7f687d021455','dtUnOrgToStdTable','标准版无组织检测结果表组件（批次）','dtUnOrgToStdTable','dtUnOrgToSource',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-19 10:11:40','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-19 10:11:40','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('7626547e-d18c-485b-97ac-783095ce7c4b','dtTableHeadDay','标准版噪声昼间表头组件','dtTableHeadDay','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-11 15:15:25','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-11 15:15:25',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('76d9545f-8eb1-4ab2-8aa4-f9decfe2a54e','innerBlankStdData','标准版室内空白样检测结果表组件','InnerBlankStdData','dtInnerBlankSource',20,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:37:42','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:37:42',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf','dtPointPicStdTable','点位示意图组件','dtPointPicStdTable','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-02-23 17:09:40','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-02-23 17:09:40',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('7fa8f96d-7d80-4430-9b83-efbe13dde5fc','dtOrgCompoundStdTable','标准版有组织化合物检测结果表组件','dtOrgCompoundStdTable','dtOrgCompoundSource',50,1,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-10-19 08:43:57','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-10-19 08:43:57',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('844acab5-8883-44d1-b8fa-af039526e967','qcStdDataSource','标准版质控说明数据主表','dtQcDataSource','',0,0,'["standardStdData", "innerBlankStdData", "OuterBlankStdData", "TransportBlankStdData", "SiteBlankStdData", "EquipBlankStdData", "innerParallelStdData", "markStdData", "replaceStdData"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-05-27 15:24:50','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-05-27 15:24:50','0','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('862381fb-faa5-4650-bb3b-c91e082a5c9e','OuterBlankStdData','标准版全程序空白样检测结果表组件','OuterBlankStdData','dtOuterBlankSource',1000,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-05-10 15:53:06','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-05-10 15:53:06','0','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('92d5fafa-47b7-4eae-9492-f36be43b381a','dtSyHeadStdTable','标准版送样类报告表头组件','dtSyHeadStdTable','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-09-17 19:16:25','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-09-17 19:16:25',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('97dc3085-3707-49f3-b61f-289a719033a3','unOrgGasStdDataSource','标准版无组织报告检测数据主表','dtDataSource','',0,0,'["dtWaterHeadStdTable", "dtUnOrgGasStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:39:47','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:39:47',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('9b557e2c-bd2c-4c73-8a6c-4a55e38f52ba','standardStdData','标准版标样检测结果表组件','StandardStdData','dtStandardSource',10,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:38:32','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:38:32',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('9d60c3e0-dbd7-469a-bf0b-42ae1951863c','solidStdDataSource','标准版炉渣报告检测数据主表','dtDataSource','',0,0,'["dtWaterHeadStdTable", "dtSolidStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:34:25','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:34:25',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('9ec36cc5-f36a-46b5-9407-b8f0b9765be5','dtTableHeadTwo','标准版噪声表头组件','dtTableHeadTwo','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:38:15','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:38:15',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('a33e5ca9-11d5-426e-91a1-f54140a5c2d3','dtOrgToSpdStdTable','标准版有组织排放速率检测结果表组件（批次）','dtOrgToSpdStdTable','dtOrgToSpdSource',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-26 17:07:02','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-26 17:07:02','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('a49934db-d45a-4176-b02c-185d858b1cc2','dtOrgToZsStdTable','标准版有组织折算浓度检测结果表组件（批次）','dtOrgToZsStdTable','dtOrgToZsSource',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-26 17:06:19','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-26 17:06:19','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('ac346b38-6ca8-4dce-b9ed-c44940c7b137','dtWasteWaterStdTable','标准版废水检测结果表组件','dtWasteWaterStdTable','dtWasteWaterSource',4,10,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:37:24','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:37:24',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('ac693c4a-7752-4473-a42d-3e38988c1f5d','dtOrgGasPjTable','有组织评价结果表组件','dtOrgGasPjTable','dtOrgGasPjSrc',10000,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-11-06 13:16:21','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-11-06 13:16:21',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('b01a7055-a235-4548-a9c1-18ab4ae8439c','dtGroundWaterStdTable','标准版地表水检测结果表组件','dtGroundWaterStdTable','dtGroundWaterSource',5,13,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-20 08:56:10','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-20 08:56:10','1',null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('b1c8a053-0fd0-4869-a3c0-7172eae2346d','dtUnOrgToCpdStdTable','标准版无组织化合物检测结果表组件（批次）','dtUnOrgToCpdStdTable','dtUnOrgToCpdSource',6,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-19 10:15:33','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-19 10:15:33','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('b6529d14-4f7f-42ba-82b7-278f822304c8','dtOrgToCpdStdTable','标准版有组织化合物检测结果表组件（批次）','dtOrgToCpdStdTable','dtOrgToCpdSource',6,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-26 17:08:24','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-26 17:08:24','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('b79900f3-aa9d-4d4f-8985-a89afc03b96a','comprehensiveStdDataSource','标准版综合报告内容主表','dtDataSource','',0,0,'["normalWaterStdDataSource", "groundWaterStdDataSource", "groundWaterStdDataSource", "orgToStdDataSource", "unOrgWeaToStdDataSource", "solidStdDataSource", "noiseDayNightDataSource", "soilStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-05 16:37:01','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-05 16:37:01',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('b8844044-ae34-43f2-b70d-868a79a27115','dtOrgSmkStdTable','标准版有组织烟气参数表组件（批次）','dtOrgSmkStdTable','dtOrgSmkSource',0,4,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-26 17:04:58','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-26 17:04:58','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('b9c0336e-0497-4108-b811-7b49848a588f','dtUnOrgWeaStdTable','标准版无组织气象条件表组件（批次）','dtUnOrgWeaStdTable','dtUnOrgWeaSource',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-19 10:24:13','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-19 10:24:13','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('ba52a4c7-960f-4ede-ad34-6a4ea0335ba4','basic','报告基础信息表','basic','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:40:24','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:40:24',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('bc73b465-89c2-47d0-ad3e-1f6bd6be99da','dtOrgStdTable','标准版有组织检测结果表组件','dtOrgStdTable','dtOrgSource',4,20,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-10-19 08:42:06','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-10-19 08:42:06',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('bd644ab7-aed3-4e86-a5f3-1efdf70dbf2c','normalWaterStdDataSource','标准版常规水报告检测数据主表','dtDataSource','',0,0,'["dtWaterHeadStdTable", "dtNormalWaterStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:34:50','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:34:50',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('c2654afe-624b-4152-9055-784447c89a4a','orgToStdDataSource','标准版有组织报告检测数据主表（批次）','dtDataSource','',0,0,'["dtOrgSmkZsSpdDataSource",  "outParallelStdDataSource"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-27 11:18:39','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-27 11:18:39','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('c7786377-4793-4161-894a-e6fee1279c64','noiseDataSource','标准版噪声报告检测数据主表','dtDataSource','',0,0,'{"headModule":"dtTableHeadTwo", "bodyModule":"dtNoiseTable", "secondBodyModule":"", "thirdBodyModule":""}',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:39:32','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:39:32',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('c821e7ca-418f-48a3-a5da-83062d6ee94c','dtWaterHeadStdTable','标准版水报告表头组件','dtWaterHeadStdTable','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:40:11','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:40:11',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('cd7ffcdb-af1a-4500-8313-5f03fdfd4990','dtUnOrgGasStdTable','标准版无组织检测结果表组件','dtUnOrgGasStdTable','dtUnOrgGasSource',4,8,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:35:27','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:35:27',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('d48a6879-fa80-4e61-99a8-d98d53d09ec1','dtOrgToHeadStdTable','标准版有组织检测结果表头（批次）','dtOrgToHeadStdTable','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-26 16:54:43','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-26 16:54:43','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('d5166dca-0b26-451f-85d7-17f027cb44c5','dtSoilStdTable','标准版土壤检测结果表组件','dtSoilStdTable','dtSoilSource',4,14,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-13 15:12:04','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-13 15:12:04',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('d75b5323-5558-4581-8340-29aa43c39e46','dtNoisePjTable','噪声评价结果表组件','dtNoisePjTable','dtNoisePjSrc',10000,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-11-06 13:18:14','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-11-06 13:18:14',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('dabc62c4-138b-4d38-a25e-41681713e347','dtCompoundStdNewTable','标准版化合物检测结果表新组件','dtCompoundStdNewTable','dtCompoundSource',4,30,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-12 14:02:12','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-12 14:02:12',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('e58dc0e5-06f2-498e-ad02-596ba4dafc8e','unOrgWeaStdDataSource','标准版无组织报告气象条件数据主表（批次）','dtUnOrgWeaDataSource','',0,0,'["dtUnOrgWeaHeadStdTable", "dtUnOrgWeaStdTable"]',1,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-04-19 10:36:09','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-04-19 10:36:09','1','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('ed98fe49-0f55-458c-9bb1-10f07878d1ed','dtTableHeadDayNight','标准版噪声昼夜表头组件','dtTableHeadDayNight','',0,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-03-11 15:16:05','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-03-11 15:16:05',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('f33aa5b0-7ddd-4a62-8d7a-2b219f574bad','dtWasteWaterPjTable','废水评价结果表组件','dtWasteWaterPjTable','dtWasteWaterPjSrc',10000,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-11-06 13:17:47','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-11-06 13:17:47',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('fe05546e-47f5-436a-a99f-970a96bcd590','markStdData','标准版加标样检测结果表组件','MarkStdData','dtMarkSource',10,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-04-09 16:03:17','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-04-09 16:03:17',null,null,0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('ff682c7e-8ef2-4a87-a1d1-da970636d0d1','SiteBlankStdData','标准版现场空白样检测结果表组件','SiteBlankStdData','dtSiteBlankSource',1000,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2024-05-10 15:55:46','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2024-05-10 15:55:46','0','0',0,0,0);
INSERT INTO "TB_LIM_REPORTMODULE"("id","moduleCode","moduleName","tableName","sourceTableName","sampleCount","testCount","sonTableJson","isCompound","orgId","creator","createDate","domainId","modifier","modifyDate","totalTest","auxiliaryInstrument","conversionCalculationMode","speedCalculationMode","compoundAvgCalculationMode") VALUES('fff21a0d-5e6f-4608-b966-d09bf86cc5f9','replaceStdData','标准版替代样检测结果表组件','ReplaceStdData','dtReplaceSource',10,0,'',0,'5f7bcf90feb545968424b0a872863876','00000000-0000-0000-0000-000000000000','2023-05-30 13:39:16','00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000','2023-05-30 13:39:16',null,null,0,0,0);

