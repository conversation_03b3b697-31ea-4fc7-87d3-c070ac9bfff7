package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.Digits;
import java.math.BigDecimal;
import java.util.Date;


/**
 * ConsumableDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ConsumableDetail")
 @Data
 public  class ConsumableDetail extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  ConsumableDetail() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 消耗品/标样id（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("消耗品/标样id（Guid）")
	private String parentId;
    
    /**
    * 单位Id（冗余）（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("单位Id（冗余）（Guid）")
    @Length(message = "单位Id{validation.message.length}", max = 50)
	private String unitId;
    
    /**
    * 单位名称（冗余）
    */
    @Column(length=100)
    @ApiModelProperty("单位名称（冗余）")
    @Length(message = "单位名称{validation.message.length}", max = 100)
	private String unitName;
    
    /**
    * 入库数量
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("入库数量")
    @Digits(integer = 18, fraction = 4, message = "入库数量整数位精度18小数位精度4")
	private BigDecimal inventory;
    
    /**
    * 库存数量
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("库存数量")
    @Digits(integer = 18, fraction = 4, message = "库存数量整数位精度18小数位精度4")
    private BigDecimal storage;
    
    /**
    * 单价
    */
    @ColumnDefault("0")
    @ApiModelProperty("单价")
    @Digits(integer = 18, fraction = 2, message = "单价整数位精度18小数位精度2")
    private BigDecimal unitPrice;
    
    /**
    * 生产批号
    */
    @Column(length=50)
    @ApiModelProperty("生产批号")
    @Length(message = "生产批号{validation.message.length}", max = 50)
	private String productionCode;
    
    /**
    * 入库时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("入库时间")
	private Date storageDate;
    
    /**
    * 生产厂商名字
    */
    @Column(length=50)
    @ApiModelProperty("生产厂商名字")
    @Length(message = "生产厂商{validation.message.length}", max = 50)
	private String manufacturerName;
    
    /**
    * 有效日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("有效日期")
	private Date expiryDate;
    
    /**
    * 供应厂商id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("供应厂商id（Guid）")
    @Length(message = "供应厂商Id{validation.message.length}", max = 50)
	private String supplierId;
    
    /**
    * 供应商名字
    */
    @Column(length=100)
    @ApiModelProperty("供应商名字")
    @Length(message = "供应厂商{validation.message.length}", max = 100)
	private String supplierName;
    
    /**
    * 验收人id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("验收人id（Guid）")
    @Length(message = "验收人{validation.message.length}", max = 50)
	private String checkerId;
    
    /**
    * 验收日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("验收日期")
	private Date purchasingDate;
    
    /**
    * 验收结论：枚举（EnumCheckerResult：1合格、2不合格、3过期)
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("验收结论：枚举（EnumCheckerResult：1合格、2不合格、3过期)")
	private Integer checkerResult;
    
    /**
    * 外观
    */
    @Column(length=100)
    @ApiModelProperty("外观")
    @Length(message = "外观{validation.message.length}", max = 100)
	private String appearance;
    
    /**
    * 检验/验证项目
    */
    @Column(length=100)
    @ApiModelProperty("检验/验证项目")
    @Length(message = "检验/验证项目{validation.message.length}", max = 100)
	private String checkItem;
    
    /**
    * 购买原因
    */
    @ApiModelProperty("购买原因")
    @Length(message = "购买原因{validation.message.length}", max = 255)
	private String buyReason;
    
    /**
    * 存放位置
    */
    @Column(length=100)
    @ApiModelProperty("存放位置")
    @Length(message = "存放位置{validation.message.length}", max = 100)
	private String keepPlace;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
	private String remark;
    
    /**
    * 是否锁定
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否锁定")
	private Boolean isLocked;
    
    /**
    * （预留3.2）生产日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("（预留3.2）生产日期")
	private Date productionDate;
    
    /**
    * （预留3.2）购买人
    */
    @Column(length=50)
    @ApiModelProperty("（预留3.2）购买人")
	private String purchaser;
    
    /**
    * （预留3.2）类型内编号
    */
    @Column(length=50)
    @ApiModelProperty("（预留3.2）类型内编号")
	private String codeInType;
    
    /**
    * （预留3.2）过期是否处理(枚举EnumExpiredStatus：0.未处理 1 提交处理 2  处理通过 3.处理未通过)
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("（预留3.2）过期是否处理(枚举EnumExpiredStatus：0.未处理 1 提交处理 2  处理通过 3.处理未通过)")
	private Integer expiredStatus;
    
    /**
    * （预留3.2）购买证明号
    */
    @ApiModelProperty("（预留3.2）购买证明号")
	private String purchaseNum;
    
    /**
    * （预留3.2）证书有效期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("（预留3.2）证书有效期")
    private Date validTime;
    
    /**
    * （预留3.2）准购数量
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("（预留3.2）准购数量")
    private BigDecimal purchaseCount;
    
    /**
    * （预留3.2）入库单号
    */
    @ApiModelProperty("（预留3.2）入库单号")
	private String storageNum;
    
    /**
    * （预留3.2）购买数量
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("（预留3.2）购买数量")
	private BigDecimal buyCount;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 定值日期
     */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("定值日期")
    private Date orderTime;
    
 }