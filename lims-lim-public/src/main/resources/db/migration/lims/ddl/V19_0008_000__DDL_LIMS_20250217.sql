alter table tb_qa_yearlyinstrumentcheckplan add column instrumentInspectId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '仪器期间核查标识';
alter table tb_qa_yearlyinstrumentconfirmplan add column instrumentCheckRecordId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '仪器检定校准标识';
alter table tb_lim_instrumentinspect add column deptId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '责任部门标识';
alter table tb_lim_instrumentinspect add column inspectMethod varchar(255) comment '核查方法';
alter table tb_lim_instrumentcheckrecord add column personId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '责任人标识';
alter table tb_lim_instrumentcheckrecord add column deptId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '责任部门标识';
alter table tb_lim_instrumentcheckrecord add column indicator varchar(255) comment '技术指标';
alter table tb_lim_instrumentcheckrecord add column basis varchar(255) comment '确认依据';

alter table tb_qa_yearlystandardcheckplan add column executeYear int(10)  comment '执行年份';
alter table tb_qa_yearlymanagementreviewplan add column executeYear int(10)  comment '执行年份';
alter table tb_qa_yearlyinnerauditplan add column executeYear int(10)  comment '执行年份';
alter table tb_qa_yearlystafftrainingplan add column executeYear int(10)  comment '执行年份';
alter table tb_qa_yearlyinstrumentconfirmplan add column executeYear int(10)  comment '执行年份';
alter table tb_qa_yearlyinstrumentcheckplan add column executeYear int(10)  comment '执行年份';
alter table tb_qa_yearlyqualitysupervisionplan add column executeYear int(10)  comment '执行年份';
alter table tb_qa_yearlyqualitycontrolplan add column executeYear int(10)  comment '执行年份';




