package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.lim.dto.rcc.DtoCalendarDate;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.vo.AnalyzeDetailDataVO;
import com.sinoyd.lims.pro.vo.AnalyzeTimelinessMapVO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * 分析数据多线程操作接口
 *
 * @version V1.0.0 2024/4/28
 * @author: hukq
 * @since V100R001
 */
public interface AnalyseTimelinessFutureService {


    Future<List<AnalyzeTimelinessMapVO>> handleDate(List<AnalyzeTimelinessMapVO> voList, List<Map<String, Integer>> overdueMapList,
                                                    Map<String, DtoCalendarDate> date2CalendarDate,
                                                    Integer warnDay);

    Future<List<AnalyzeDetailDataVO>> handleDetailDate(List<AnalyzeDetailDataVO> voList, Map<String, DtoCalendarDate> date2CalendarDate);
}
