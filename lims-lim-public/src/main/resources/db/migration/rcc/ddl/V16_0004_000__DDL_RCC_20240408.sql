-- tb_lim_recordconfig
drop trigger if exists after_insert_recordconfig;
DELIMITER $$
CREATE TRIGGER after_insert_recordconfig
    AFTER INSERT
    ON tb_lim_recordconfig
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 1, null, null, null, new.orgId,
            new.domainId);
end $$
DELIMITER ;

drop trigger if exists after_update_recordconfig;
DELIMITER $$
CREATE TRIGGER after_update_recordconfig
    AFTER UPDATE
    ON tb_lim_recordconfig
    FOR EACH ROW
BEGIN
    IF new.isDeleted = 1 THEN
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
                new.domainId);
    else
       if new.recordName != old.recordName or (new.recordName is null and old.recordName is not null) or
           (new.recordName is not null and old.recordName is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'recordName',
                    old.recordName, new.recordName, new.orgId, new.domainId);
       END IF;
       if new.recordType != old.recordType or (new.recordType is null and old.recordType is not null) or
           (new.recordType is not null and old.recordType is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'recordType',
                    old.recordType, new.recordType, new.orgId, new.domainId);
       END IF;
       if new.reportConfigId != old.reportConfigId or (new.reportConfigId is null and old.reportConfigId is not null) or
           (new.reportConfigId is not null and old.reportConfigId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'reportConfigId',
                    old.reportConfigId, new.reportConfigId, new.orgId, new.domainId);
       END IF;
       if new.sampleTypeId != old.sampleTypeId or (new.sampleTypeId is null and old.sampleTypeId is not null) or
           (new.sampleTypeId is not null and old.sampleTypeId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'sampleTypeId',
                    old.sampleTypeId, new.sampleTypeId, new.orgId, new.domainId);
       END IF;
       if new.remark != old.remark or (new.remark is null and old.remark is not null) or
           (new.remark is not null and old.remark is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'remark',
                    old.remark, new.remark, new.orgId, new.domainId);
       END IF;
    END IF;
end $$
DELIMITER ;

-- tb_lim_recordconfig2paramsconfig
drop trigger if exists after_insert_recordconfig2paramsconfig;
DELIMITER $$
CREATE TRIGGER after_insert_recordconfig2paramsconfig
    AFTER INSERT
    ON tb_lim_recordconfig2paramsconfig
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_recordconfig2paramsconfig', new.id, '00000000-0000-0000-0000-000000000000', now(), 1, null, null, null,
            '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000');
end $$
DELIMITER ;

drop trigger if exists after_update_recordconfig2paramsconfig;
DELIMITER $$
CREATE TRIGGER after_update_recordconfig2paramsconfig
    AFTER UPDATE
    ON tb_lim_recordconfig2paramsconfig
    FOR EACH ROW
BEGIN
    if new.recordConfigId != old.recordConfigId or (new.recordConfigId is null and old.recordConfigId is not null) or
       (new.recordConfigId is not null and old.recordConfigId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_recordconfig2paramsconfig', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'recordConfigId',
                old.recordConfigId, new.recordConfigId, '00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000');
    END IF;
    if new.paramsConfigId != old.paramsConfigId or (new.paramsConfigId is null and old.paramsConfigId is not null) or
       (new.paramsConfigId is not null and old.paramsConfigId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_recordconfig2paramsconfig', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'paramsConfigId',
                old.paramsConfigId, new.paramsConfigId, '00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000');
    END IF;
end $$
DELIMITER ;

drop trigger if exists before_delete_recordconfig2paramsconfig;
DELIMITER $$
CREATE TRIGGER before_delete_recordconfig2paramsconfig
    BEFORE DELETE
    ON tb_lim_recordconfig2paramsconfig
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_recordconfig2paramsconfig', old.id, '00000000-0000-0000-0000-000000000000', now(), 2, null, null, null,
            '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000');
End $$
DELIMITER ;

-- tb_lim_recordconfig2test
drop trigger if exists after_insert_recordconfig2test;
DELIMITER $$
CREATE TRIGGER after_insert_recordconfig2test
    AFTER INSERT
    ON tb_lim_recordconfig2test
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_recordconfig2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 1, null, null, null,
            '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000');
end $$
DELIMITER ;

drop trigger if exists after_update_recordconfig2test;
DELIMITER $$
CREATE TRIGGER after_update_recordconfig2test
    AFTER UPDATE
    ON tb_lim_recordconfig2test
    FOR EACH ROW
BEGIN
    if new.recordConfigId != old.recordConfigId or (new.recordConfigId is null and old.recordConfigId is not null) or
       (new.recordConfigId is not null and old.recordConfigId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_recordconfig2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'recordConfigId',
                old.recordConfigId, new.recordConfigId, '00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000');
    END IF;
    if new.testId != old.testId or (new.testId is null and old.testId is not null) or
       (new.testId is not null and old.testId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_recordconfig2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'testId',
                old.testId, new.testId, '00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000');
    END IF;
end $$
DELIMITER ;

drop trigger if exists before_delete_recordconfig2test;
DELIMITER $$
CREATE TRIGGER before_delete_recordconfig2test
    BEFORE DELETE
    ON tb_lim_recordconfig2test
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_recordconfig2test', old.id, '00000000-0000-0000-0000-000000000000', now(), 2, null, null, null,
            '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000');
End $$
DELIMITER ;









