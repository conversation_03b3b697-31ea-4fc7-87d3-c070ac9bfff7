package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

/**
 * 企业导入字段
 * <AUTHOR>
 * @version V1.0.0 2022/09/20
 * @since V100R001
 */
@Data
public class DtoImportEnterprise implements IExcelDataModel, IExcelModel {

    /**
     * 企业名称（污染源名称）
     */
    @Excel(name = "企业名称(必填)",orderNum = "100",width = 46)
    private String name;

    /**
     * 是否污染源
     */
    @Excel(name = "是否污染源",orderNum = "110",width = 18)
    private String isPollutionStr;

    /**
     * 污染源类型
     */
    @Excel(name = "污染源类型(是污染源时必填)",orderNum = "120",width = 30)
    private String pollutionType;

    /**
     * 污染源编号
     */
    @Excel(name = "污染源编号(是污染源时必填)",orderNum = "130",width = 30)
    private String pollutionCode;
    /**
     * 所属区域(省)
     */
    @Excel(name = "所属区域(省)",orderNum = "210",width = 17)
    private String provinceAreaName;

    /**
     * 省区域id
     */
    private String provinceId;

    /**
     * 所属区域(市)
     */
    @Excel(name = "所属区域(市)",orderNum = "220",width = 17)
    private String cityAreaName;

    /**
     * 市区域id
     */
    private String cityId;

    /**
     * 所属区域(县、区)
     */
    @Excel(name = "所属区域(县、区)",orderNum = "230",width = 17)
    private String areaName;

    /**
     * 所属行业
     */
    @Excel(name = "所属行业",orderNum = "240",width = 17)
    private String industryKind;

    /**
     * 行业类型
     */
    @Excel(name = "行业类型",orderNum = "250",width = 40)
    private String businessTypeId;

    /**
     * 县,区区域id
     */
    private String areaId;

    /**
     * 企业地址
     */
    @Excel(name = "地址",orderNum = "300",width = 35)
    private String address;

    /**
     * 法人代表 
     */
    @Excel(name = "企业法人",orderNum = "400",width = 13)
    private String corporationName;

    /**
     * 社会信用代码
     */
    @Excel(name = "社会信用代码",orderNum = "500",width = 31)
    private String socialCreditCode;

    /**
     * 联系人 
     */
    @Excel(name = "联系人",orderNum = "600",width = 15)
    private String contactMan;

    /**
     * 联系手机
     */
    @Excel(name = "联系方式",orderNum = "700",width = 15)
    private String contactTelPhone;

    /**
     * 联系人传真
     */
    @Excel(name = "传真",orderNum = "800",width = 21)
    private String contactFax;

    /**
     * 联系邮箱
     */
    @Excel(name = "邮箱",orderNum = "900",width = 22)
    private String email;

    /**
     * 企业简介 
     */
    @Excel(name = "企业简介",orderNum = "1000",width = 40)
    private String info;

    /**
     * 行号
     */
    private Integer rowNum;

    /**
     * 错误信息
     */
    private String errorMsg;

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }
}
