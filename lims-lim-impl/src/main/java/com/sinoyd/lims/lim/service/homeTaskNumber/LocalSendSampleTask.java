package com.sinoyd.lims.lim.service.homeTaskNumber;

import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.lim.enums.EnumLIM;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 首页现场委托送样代办数字缓存刷新
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/10/26
 */
@Component
public class LocalSendSampleTask extends AbsTaskNumber{

    /**
     * 获取模块数量
     *
     * @param homeModule 模块编码
     * @param orgId      组织id
     * @param userId     人员id
     * @param outTypeIds 不包含id
     * @return 模块数量集合
     */
    @Override
    public List<DtoTaskNum> getTaskNum(HomeModule homeModule, String orgId, String userId, List<String> outTypeIds) {
        //创建查询sql语句
        StringBuilder stringBuilder = new StringBuilder("select a.currentPersonId as userId,count(a.id) as count from TB_PRO_StatusForProject a")
                .append(" where 1=1 and a.orgId = ?")
                .append(" and a.status = ? and a.module = ?")
                .append(" group by a.currentPersonId");
        //执行sql语句
        return jdbcTemplate.query(stringBuilder.toString(),
                new String[]{orgId,"1",EnumLIM.EnumProjectModule.委托现场送样.getCode()},
                (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"),resultSet.getLong("count")));
    }

    /**
     * 获取模块编码
     *
     * @return 模块编码
     */
    @Override
    public String getModuleCode() {
        return EnumLIM.EnumHomeTaskModule.现场委托送样.getValue();
    }
}
