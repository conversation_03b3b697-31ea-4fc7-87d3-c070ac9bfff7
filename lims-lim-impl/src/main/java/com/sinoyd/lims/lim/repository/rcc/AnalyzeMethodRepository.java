package com.sinoyd.lims.lim.repository.rcc;

import java.util.Collection;
import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 分析方法仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-13
 * @since V100R001
 */
public interface AnalyzeMethodRepository extends IBaseJpaRepository<DtoAnalyzeMethod, String> {

    /**
     * 根据分析方法名称和标准编号进行查询
     *
     * @param methodName
     * @param countryStandard
     * @return 分析方法实体
     */
    @Query("select p from DtoAnalyzeMethod p where p.isDeleted = 0 and p.methodName = :methodName and p.countryStandard = :countryStandard and p.isSamplingMethod = 0")
    DtoAnalyzeMethod getByNameAndCountryStandard(@Param("methodName") String methodName, @Param("countryStandard") String countryStandard);

    /**
     * 根据分析方法名称进行查询
     *
     * @param methodNames 分析方法名称
     * @return 分析方法实体
     */
    @Query("select p from DtoAnalyzeMethod p where p.isDeleted = 0 and p.methodName in :methodName")
    List<DtoAnalyzeMethod> getByMethodNameIn(@Param("methodName") List<String> methodNames);

    /**
     * 根据parentId获取分析方法
     *
     * @param parentId 分析方法parentId
     * @return 分析方法集合
     */
    @Query("select p from DtoAnalyzeMethod p where p.isDeleted = 0 and p.parentId = :parentId")
    List<DtoAnalyzeMethod> getListByParentId(@Param("parentId") String parentId);

    /**
     * 新增时判断是否存在同名分析方法
     *
     * @param methodName 分析方法名称
     * @return 相同名称的分析方法数量
     */
    @Query("select count(p.id) from DtoAnalyzeMethod p where p.isDeleted = 0 and p.methodName = :methodName")
    Integer getCountByName(@Param("methodName") String methodName);

    /**
     * 更新时判断是否存在同名分析方法
     *
     * @param methodName 分析方法名称
     * @param id         要更新的分析方法的id
     * @return 相同名称的分析方法数量
     */
    @Query("select count(p.id) from DtoAnalyzeMethod p where p.isDeleted = 0 and p.methodName = :methodName and p.id <> :id")
    Integer getCountByNameAndId(@Param("methodName") String methodName, @Param("id") String id);

    /**
     * 重写获取所有的方法
     *
     * @return 返回未删除的方法信息
     */
    @Override
    @Query("select p from DtoAnalyzeMethod p where p.isDeleted = 0")
    List<DtoAnalyzeMethod> findAll();

    /**
     * 重写获取所有的方法
     *
     * @param ids 方法ids
     * @return 返回未删除的方法信息
     */
    @Override
    @Query("select p from DtoAnalyzeMethod p where p.isDeleted = 0 and p.id in :ids")
    List<DtoAnalyzeMethod> findAll(@Param("ids") Iterable<String> ids);

    /**
     * 返回所有的带假删的方法信息
     *
     * @return 返回带删除的方法信息
     */
    @Query("select p from DtoAnalyzeMethod p")
    List<DtoAnalyzeMethod> findAllDeleted();

    /**
     * 返回所以的带删除的方法信息
     *
     * @param ids 方法的ids
     * @return 返回带删除的方法信息
     */
    @Query("select p from DtoAnalyzeMethod p where p.id in :ids")
    List<DtoAnalyzeMethod> findAllDeleted(@Param("ids") List<String> ids);

    /**
     * 根据分析方法名称进行查询
     *
     * @param methodName
     * @return 分析方法实体
     */
    @Query("select count(p) from DtoAnalyzeMethod p where p.isDeleted = 0 and p.methodName = :methodName and p.id <> :id and p.isSamplingMethod = 1")
    Integer countByMethodName(@Param("methodName") String methodName, @Param("id") String id);

    /**
     * 根据检测类型ids获取方法
     *
     * @param sampleTypeIds 检测类型id
     * @return 方法集合
     */
    List<DtoAnalyzeMethod> findBySampleTypeIdIn(Collection<String> sampleTypeIds);

}