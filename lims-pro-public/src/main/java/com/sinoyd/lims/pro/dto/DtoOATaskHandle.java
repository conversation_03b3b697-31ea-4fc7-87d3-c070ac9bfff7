package com.sinoyd.lims.pro.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * 审批任务办理传输实体
 * <AUTHOR>
 * @version V1.0.0 2019-03-28
 * @since V100R001
 */
@Data
public class DtoOATaskHandle implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审批任务ID
     */
    private String taskId;

    /**
     * 是否通过
     */
    private boolean pass = true;

    /**
     * 审核人员
     */
    private String jurorId;

    /**
     * 批注
     */
    private String comment;

    /**
     * 下一步办理人账号标识
     */
    private String nextAssignee = "";

    /**
     * 下一步办理人Guid标识
     */
    private String nextAssigneeId = "";

    /**
     * 下一步办理人名称
     */
    private String nextAssigneeName = "";
}