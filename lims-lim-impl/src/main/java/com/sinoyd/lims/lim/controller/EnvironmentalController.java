package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.EnvironmentalCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmental;
import com.sinoyd.lims.lim.service.EnvironmentalService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 环境管理
 * <AUTHOR> 修改：xuxb
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/environmental")
@Validated
public class EnvironmentalController extends BaseJpaController<DtoEnvironmental, String, EnvironmentalService>{

    /**
     * 根据id获取环境信息
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取环境信息", notes = "根据id获取环境信息")
    @GetMapping("/{id}")
    public RestResponse<DtoEnvironmental> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoEnvironmental> restResp = new RestResponse<>();
        DtoEnvironmental entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询环境信息
     * 
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询环境信息", notes = "分页动态条件查询环境信息")
    @GetMapping
    public RestResponse<List<DtoEnvironmental>> findByPage(EnvironmentalCriteria criteria) {

        RestResponse<List<DtoEnvironmental>> restResp = new RestResponse<>();

        PageBean<DtoEnvironmental> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增环境信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增环境信息", notes = "新增环境信息")
    @PostMapping
    public RestResponse<DtoEnvironmental> save(@Validated @RequestBody DtoEnvironmental entity) {

        RestResponse<DtoEnvironmental> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoEnvironmental data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改环境信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改环境信息", notes = "修改环境信息")
    @PutMapping
    public RestResponse<DtoEnvironmental> update(@Validated @RequestBody DtoEnvironmental entity) {

        RestResponse<DtoEnvironmental> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoEnvironmental data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 刪除环境信息
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "删除环境信息", notes = "刪除环境信息")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除环境信息
     * 
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除环境信息", notes = "批量删除环境信息")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}