package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 现场数据录入保存的dto
 * <AUTHOR>
 * @version V1.0.0 2020/2/25
 * @since V100R001
 */
@Data
public class DtoLocalDataSave {
    /**
     * 相关的分析数据
     */
    private List<DtoAnalyseDataProperty> analyseData = new ArrayList<>();

    /**
     * 领样单id
     */
    private String subId;

    /**
     * 排序Id
     */
    private String sortId;
}
