package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;


/**
 * QuotationDetail查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QuotationDetailCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 订单Id
     */
    private String orderId;

    /**
     * 任务数
     */
    private Integer projectCount = -1;

    /**
     * 检测类型
     */
    private String sampleTypeId;

    /**
     * 分析项目
     */
    private String redAnalyseItemName;

    /**
     * 点位名称
     */
    private String folderName;

    /**
     * 分析方法
     */
    private String redAnalyseMethod;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        if (StringUtil.isNotEmpty(this.orderId)) {
            condition.append(" and orderId = :orderId");
            values.put("orderId", this.orderId);
        }
        // 检测类型筛选
        if (StringUtil.isNotEmpty(this.sampleTypeId)) {
            condition.append(" and sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        // 分析项目筛选
        if (StringUtil.isNotEmpty(this.redAnalyseItemName)) {
            condition.append(" and redAnalyseItemName like :redAnalyseItemName ");
            values.put("redAnalyseItemName", "%" + this.redAnalyseItemName + "%");
        }
        if (StringUtil.isNotEmpty(this.folderName)) {
            condition.append(" and folderName like :folderName");
            values.put("folderName", "%" + this.folderName + "%");
        }
        if (StringUtil.isNotEmpty(this.redAnalyseMethod)) {
            condition.append(" and redAnalyseMethod like :redAnalyseMethod");
            values.put("redAnalyseMethod", "%" + this.redAnalyseMethod + "%");
        }
        condition.append(" and projectCount >= :projectCount");
        values.put("projectCount", this.projectCount);
        return condition.toString();
    }
}