package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.StationarySourceResultCriteria;
import com.sinoyd.lims.od.dto.DtoStationarySourceResult;
import com.sinoyd.lims.od.service.StationarySourceResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * stationarySourceResult服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例: stationarySourceResult服务")
@RestController
@RequestMapping("api/ods/stationarySourceResult")
@Validated
public class StationarySourceResultController extends BaseJpaController<DtoStationarySourceResult, String, StationarySourceResultService> {

    /**
     * 分页动态条件查询StationarySourceResult
     *
     * @param stationarySourceResultCriteria 条件参数
     * @return RestResponse<List < StationarySourceResult>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoStationarySourceResult>> findByPage(StationarySourceResultCriteria stationarySourceResultCriteria) {
        PageBean<DtoStationarySourceResult> pageBean = super.getPageBean();
        RestResponse<List<DtoStationarySourceResult>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, stationarySourceResultCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询stationarySourceResult
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询stationarySourceResult", notes = "根据id查询stationarySourceResult")
    @GetMapping("/{id}")
    public RestResponse<DtoStationarySourceResult> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoStationarySourceResult> restResp = new RestResponse<>();
        DtoStationarySourceResult entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增stationarySourceResult
     *
     * @param stationarySourceResult stationarySourceResult实体
     * @return 新增的stationarySourceResult实体
     */
    @ApiOperation(value = "新增stationarySourceResult", notes = "新增stationarySourceResult")
    @PostMapping("")
    public RestResponse<DtoStationarySourceResult> create(@Validated @RequestBody DtoStationarySourceResult stationarySourceResult) {
        RestResponse<DtoStationarySourceResult> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoStationarySourceResult data = service.save(stationarySourceResult);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新stationarySourceResult
     *
     * @param stationarySourceResult stationarySourceResult实体
     * @return 更新后的stationarySourceResult实体
     */
    @ApiOperation(value = "更新stationarySourceResult", notes = "更新stationarySourceResult")
    @PutMapping("")
    public RestResponse<DtoStationarySourceResult> update(@Validated @RequestBody DtoStationarySourceResult stationarySourceResult) {
        RestResponse<DtoStationarySourceResult> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoStationarySourceResult data = service.update(stationarySourceResult);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }



}
