package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SolutionCalibration;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoSolutionCalibration实体
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SolutionCalibration")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoSolutionCalibration extends SolutionCalibration {

    @Transient
    private List<DtoSolutionCalibrationRecord> recordList;
}
