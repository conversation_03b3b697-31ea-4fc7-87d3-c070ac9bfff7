package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * OtherDetail查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtherDetailCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    private String id;

    /**
     * 订单Id
     */
    private String orderId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.typeId = f.id");
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and a.id = :id");
            values.put("id", this.id);
        }
        if (StringUtil.isNotEmpty(this.orderId)) {
            condition.append(" and a.orderId = :orderId");
            values.put("orderId", this.orderId);
        }
        return condition.toString();
    }
}