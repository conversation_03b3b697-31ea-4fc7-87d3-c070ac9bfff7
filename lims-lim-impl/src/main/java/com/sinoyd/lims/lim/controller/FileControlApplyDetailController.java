package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.FileControlApplyDetailCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.service.FileControlApplyDetailService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 文件信息（右侧Grid）
 * <AUTHOR>
 * @version V1.0.0 2019/3/2
 * @since  V100R001
 */
@RestController
@RequestMapping("/api/lim/fileControlApplyDetail")
@Validated
public class FileControlApplyDetailController extends BaseJpaController<DtoFileControlApplyDetail, String, FileControlApplyDetailService> {
    /**
     * 获取文件信息
     * 
     * @param id 文件信息id
     * @return 返回文件信息
     */
    @GetMapping("/{id}")
    public RestResponse<DtoFileControlApplyDetail> getById(@PathVariable String id) {
        RestResponse<DtoFileControlApplyDetail> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        restResp.setData(service.findOne(id));

        return restResp;
    }

    /**
     * 分页获取文件信息
     * @param criteria 文件信息查询条件
     * @return 返回文件信息列表
     */
    @GetMapping()
    public RestResponse<List<DtoFileControlApplyDetail>> findByPage(FileControlApplyDetailCriteria criteria) {
        RestResponse<List<DtoFileControlApplyDetail>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        PageBean<DtoFileControlApplyDetail> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

//        PageDto pageDto = new PageDto(page.getPageNo(), page.getRowsPerPage(), page.getRowsCount(), page.getPageTotal());
//        restResp.setPage(pageDto);
//        restResp.setCount(pageDto.getTotal());
        
        return restResp;
    }

    /**
     * 新增文件
     * @param fileControlApplyDetail 文件信息
     * @return 返回新增的文件信息
     */
    @PostMapping()
    public RestResponse<DtoFileControlApplyDetail> create(@Validated @RequestBody DtoFileControlApplyDetail fileControlApplyDetail) {
        RestResponse<DtoFileControlApplyDetail> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.save(fileControlApplyDetail));

        return restResp;
    }

    /**
     * 修改文件信息
     * @param fileControlApplyDetail 文件信息
     * @return 返回修改的文件信息
     */
    @PutMapping()
    public RestResponse<DtoFileControlApplyDetail> update(@Validated @RequestBody DtoFileControlApplyDetail fileControlApplyDetail) {
        RestResponse<DtoFileControlApplyDetail> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.update(fileControlApplyDetail));

        return restResp;
    }

    /**
     * 删除文件
     * @param id 文件信息id
     * @return 返回是否删除
     */
    @DeleteMapping("/{id}")
    public RestResponse<Boolean> delete(@PathVariable String id) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        restResp.setData(service.logicDeleteById(id) > 0);

        return restResp;
    }

    /**
     * 批量删除文件
     * 
     * @param ids 文件信息id集合
     * @return 返回删除的记录数
     */
    @DeleteMapping()
    public RestResponse<Integer> delete(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        restResp.setData(service.logicDeleteById(ids));

        return restResp;
    }

    /**
     * 根据文件类型id批量删除文档明细
     * @param folderIds 文件类型id集合
     * @return
     */
    @ApiOperation(value = "根据文件类型id批量删除文档明细", notes = "根据文件类型id批量删除文档明细")
    @DeleteMapping("/folderId")
    public RestResponse<String> deleteByFolderIds(@RequestBody List<String> folderIds) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setCount(service.deleteByDictCode(folderIds));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 文件树结构
     * @return RestResponse<List<TreeNode>>
     */
    @ApiOperation(value = "文件树结构", notes = "文件树结构")
    @GetMapping("/fileTree")
    public RestResponse<List<TreeNode>> bigTree() {
        RestResponse<List<TreeNode>> response = new RestResponse<>();
        response.setData(service.getAllFileTypes());
        return response;
    }

    /**
     * 更新文件状态
     *
     * @param detail 数据载体
     * @return RestResponse<Void>
     */
    @PostMapping("/updateStatus")
    public RestResponse<Void> updateFileStatus(@RequestBody DtoFileControlApplyDetail detail) {
        RestResponse<Void> response = new RestResponse<>();
        service.updateFileStatus(detail);
        return response;
    }

    @PostMapping("/downLoad")
    public RestResponse<Void> downLoadFile(@RequestBody DtoFileControlApplyDetail detail, HttpServletResponse response) throws Exception {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.downLoadFile(detail.getId(), detail.getDocumentId(), response);
        return restResponse;
    }

    @PostMapping("/batchDownLoad")
    public RestResponse<Void> batchDownLoad(@RequestBody DtoFileControlApplyDetail detail, HttpServletResponse response) throws Exception {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.batchDownLoad(detail.getFileIds(), response);
        return restResponse;
    }
}