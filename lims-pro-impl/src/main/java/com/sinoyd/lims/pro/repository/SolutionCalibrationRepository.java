package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSolutionCalibration;


/**
 * DtoSolutionCalibration数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
public interface SolutionCalibrationRepository extends IBaseJpaRepository<DtoSolutionCalibration, String> {

    /**
     * 根据workSheetFolderId查询
     * @param workSheetFolderId 检测单标识
     * @return 记录集合
     */
    DtoSolutionCalibration findByWorkSheetFolderId(String  workSheetFolderId);
}
