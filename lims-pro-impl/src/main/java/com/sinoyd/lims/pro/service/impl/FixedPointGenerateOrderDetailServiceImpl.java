package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.lims.lim.service.CostService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.entity.Fixedpoint;
import com.sinoyd.lims.monitor.service.FixedPoint2TestService;
import com.sinoyd.lims.monitor.service.FixedpointService;
import com.sinoyd.lims.pro.dto.DtoOrderQuotation;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail2Test;
import com.sinoyd.lims.pro.repository.QuotationDetail2TestRepository;
import com.sinoyd.lims.pro.repository.QuotationDetailRepository;
import com.sinoyd.lims.pro.service.IFixedPointGenerateOrderDetailService;
import com.sinoyd.lims.pro.service.OrderQuotationService;
import com.sinoyd.lims.pro.service.QuotationDetailService;
import com.sinoyd.lims.pro.vo.OrderPollutionPointGenerateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 企业点位生成订单详情服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/24
 * @since V100R001
 */
@Service
public class FixedPointGenerateOrderDetailServiceImpl implements IFixedPointGenerateOrderDetailService {

    private QuotationDetailService quotationDetailService;

    private QuotationDetailRepository quotationDetailRepository;

    private OrderQuotationService orderQuotationService;

    private QuotationDetail2TestRepository quotationDetail2TestRepository;

    private FixedpointService fixedpointService;

    private FixedPoint2TestService fixedPoint2TestService;

    private TestService testService;

    private CostService costService;

    @Override
    public void generateDetail(OrderPollutionPointGenerateVO generateVO) {
        //查询到订单的金额数据
        DtoOrderQuotation orderQuotation = orderQuotationService.findByOrderId(generateVO.getOrderFormId());
        //获取选择的点位数据
        List<DtoFixedpoint> pointList = fixedpointService.findAll(generateVO.getFixedPointIds());
        Map<String, DtoFixedpoint> fixedPointMap = pointList.stream().collect(Collectors.toMap(DtoFixedpoint::getId, point -> point));
        //获取所有的关联测试项目数据
        List<DtoFixedPoint2Test> point2TestList = fixedPoint2TestService.findByFixedPointIdIn(generateVO.getFixedPointIds());
        //设置周期
        for (DtoFixedPoint2Test point2Test : point2TestList) {
            DtoFixedpoint point = fixedPointMap.get(point2Test.getFixedPointId());
            point2Test.setCycleOrder(point.getCycleOrder());
            point2Test.setSampleTypeId(point.getSampleTypeId());
        }
        //获取关联测试项目数据中的所有的测试项目id集合
        List<String> testIds = point2TestList.stream().map(DtoFixedPoint2Test::getTestId).distinct().collect(Collectors.toList());
        //费用数据
        List<DtoCost> costList = costService.findByTestIdIn(testIds);
        //查询到测试项目数据
        List<DtoTest> testList = testService.findAll(testIds);

        //需要保存的订单详情数据
        List<DtoQuotationDetail> detailList = new ArrayList<>();
        //需要保存的订单详情测试项目关联数据
        List<DtoQuotationDetail2Test> quotationDetail2TestList = new ArrayList<>();

        //按照检测类型分组
        Map<String, List<DtoFixedPoint2Test>> pointTypeGroup = point2TestList.stream().collect(Collectors.groupingBy(DtoFixedPoint2Test::getSampleTypeId));
        for (Map.Entry<String, List<DtoFixedPoint2Test>> typeEntry : pointTypeGroup.entrySet()) {
            String sampleTypeId = typeEntry.getKey();
            //按照周期-批次-样次对关联测试项目进行分组
            Map<String, List<DtoFixedPoint2Test>> point2TestMap = typeEntry.getValue().stream()
                    .collect(Collectors.groupingBy(point2Test ->
                            point2Test.getCycleOrder() + "-" + point2Test.getTimesOrder() + "-" + point2Test.getSamplePeriod()));
            for (Map.Entry<String, List<DtoFixedPoint2Test>> cycleEntry : point2TestMap.entrySet()) {
                //获取当前周期批次样次下的测试项目数据
                List<String> testIdsOfCycle = cycleEntry.getValue().stream().map(DtoFixedPoint2Test::getTestId).distinct().collect(Collectors.toList());
                //分割周期-批次-样次
                String[] split = cycleEntry.getKey().split("-");
                //循环测试项目进行订单详情转换
                List<DtoTest> testOfCycle = testList.stream().filter(p -> testIdsOfCycle.contains(p.getId())).collect(Collectors.toList());
                for (DtoTest test : testOfCycle) {
                    DtoQuotationDetail detail = createDetail(split, generateVO.getOrderFormId(),
                            orderQuotation.getId(), test, pointList, cycleEntry.getValue());
                    //处理总称测试项目价格
                    quotationDetailService.handleTotalTestPrice(quotationDetail2TestList, detail, test, costList, sampleTypeId);
                    detailList.add(detail);
                }
            }
        }
        //保存
        if (StringUtil.isNotEmpty(detailList)) {
            quotationDetailRepository.save(detailList);
        }
        if (StringUtil.isNotEmpty(quotationDetail2TestList)) {
            quotationDetail2TestRepository.save(quotationDetail2TestList);
        }
    }

    /**
     * 创建订单详情数据
     *
     * @param split            周期-批次-样次分割数组
     * @param orderFormId      订单id
     * @param orderQuotationId 订单报价id
     * @param test             测试项目
     * @param pointList        点位数据
     * @param cycleTestList    周期分组下的测试项目关联点位数据
     * @return 订单详情数据
     */
    private DtoQuotationDetail createDetail(String[] split,
                                            String orderFormId, String orderQuotationId,
                                            DtoTest test,
                                            List<DtoFixedpoint> pointList,
                                            List<DtoFixedPoint2Test> cycleTestList) {
        DtoQuotationDetail detail = new DtoQuotationDetail(orderFormId, orderQuotationId, test);
        detail.setCycleOrder(Integer.parseInt(split[0]));
        detail.setTimesOrder(Integer.parseInt(split[1]));
        detail.setSampleCount(Integer.parseInt(split[2]));
        //设置点位名称
        Set<String> testFolderIds = new HashSet<>();
        Set<String> projectIntervals = new HashSet<>();
        cycleTestList.stream().filter(p -> p.getTestId().equals(test.getId())).collect(Collectors.toList()).forEach(p -> {
            testFolderIds.add(p.getFixedPointId());
            projectIntervals.add(p.getProjectInterval());
        });
        Set<String> folderName = pointList.stream().filter(p -> testFolderIds.contains(p.getId())).map(Fixedpoint::getPointName).collect(Collectors.toSet());
        detail.setFolderName(String.join(",", folderName));
        Integer folderCount = folderName.size();
        //监测间隔
        detail.setProjectInterval(String.join(",", projectIntervals));
        //总检数
        int totalCount = detail.getCycleOrder() * folderCount * detail.getTimesOrder() * detail.getProjectCount () * detail.getSampleCount();
        detail.setSampleOrder(totalCount);
        //剩检数
        Integer count = detail.getSampleOrder() - detail.getInspectedCount();
        detail.setResidueCount(count);
        return detail;
    }

    @Autowired
    public void setQuotationDetailService(QuotationDetailService quotationDetailService) {
        this.quotationDetailService = quotationDetailService;
    }

    @Autowired
    public void setQuotationDetailRepository(QuotationDetailRepository quotationDetailRepository) {
        this.quotationDetailRepository = quotationDetailRepository;
    }

    @Autowired
    public void setOrderQuotationService(OrderQuotationService orderQuotationService) {
        this.orderQuotationService = orderQuotationService;
    }

    @Autowired
    public void setQuotationDetail2TestRepository(QuotationDetail2TestRepository quotationDetail2TestRepository) {
        this.quotationDetail2TestRepository = quotationDetail2TestRepository;
    }

    @Autowired
    public void setFixedpointService(FixedpointService fixedpointService) {
        this.fixedpointService = fixedpointService;
    }

    @Autowired
    public void setFixedPoint2TestService(FixedPoint2TestService fixedPoint2TestService) {
        this.fixedPoint2TestService = fixedPoint2TestService;
    }

    @Autowired
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setCostService(CostService costService) {
        this.costService = costService;
    }
}
