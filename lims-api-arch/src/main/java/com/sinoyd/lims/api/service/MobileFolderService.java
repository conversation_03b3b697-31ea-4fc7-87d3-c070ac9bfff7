package com.sinoyd.lims.api.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityValidate;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;

import java.util.List;
import java.util.Map;

public interface MobileFolderService extends IBaseJpaService<DtoFolder, String> {

    /**
     * 根据文件夹id查询下级文件夹和文件夹中文件
     * @param folderId 文件夹id
     * @return List<Object>
     */
    List<Object> findFileAndFolder(String folderId, String key);

    /**
     * 权限验证
     *
     * @param dtoDocAuthorityValidate 文档权限验证实体
     * @return 是否具有权限
     */
    Boolean validateAuth(DtoDocAuthorityValidate dtoDocAuthorityValidate);

    /**
     * 删除文件
     *
     * @param ids 文件id集合
     */
    void delete(List<String> ids);

    /**
     * 更新文件备注
     * @param map 参数
     * @return RestResponse<Void>
     */
    void updateRemark(Map<String, Object> map);
}
