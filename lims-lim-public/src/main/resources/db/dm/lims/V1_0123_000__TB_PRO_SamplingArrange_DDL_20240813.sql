ALTER TABLE TB_PRO_SAMPLINGARRANGE ADD COLUMN kbFlag BIT DEFAULT NULL NULL;
ALTER TABLE TB_PRO_SAMPLINGARRANGE ADD COLUMN pxFlag BIT DEFAULT NULL NULL;
ALTER TABLE TB_PRO_SAMPLINGARRANGE ADD COLUMN status VARCHAR(50) DEFAULT '' NULL;
ALTER TABLE TB_PRO_SAMPLINGARRANGE ADD COLUMN samplingPlanId VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL;
COMMENT ON COLUMN TB_PRO_SAMPLINGARRANGE.kbFlag IS '空白样';
COMMENT ON COLUMN TB_PRO_SAMPLINGARRANGE.pxFlag IS '平行样';
COMMENT ON COLUMN TB_PRO_SAMPLINGARRANGE.status IS '状态';
COMMENT ON COLUMN TB_PRO_SAMPLINGARRANGE.samplingPlanId IS '采样计划id';