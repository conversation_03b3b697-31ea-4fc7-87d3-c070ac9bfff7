package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.pro.criteria.SamplePreparationCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSamplePreparation;
import com.sinoyd.lims.pro.dto.customer.DtoSampleOfPrepared;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.ReceiveSampleRecordRepository;
import com.sinoyd.lims.pro.repository.SamplePreparationRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.SamplePreparationService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 样品制备操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@Service
public class SamplePreparationServiceImpl extends BaseJpaServiceImpl<DtoSamplePreparation, String, SamplePreparationRepository> implements SamplePreparationService {

    private AnalyzeMethodRepository analyzeMethodRepository;

    private SampleTypeRepository sampleTypeRepository;

    private AnalyseDataRepository analyseDataRepository;

    private SampleRepository sampleRepository;

    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    private InstrumentRepository instrumentRepository;

    /**
     * 分页查询样品
     *
     * @param pageBean 分页数据
     * @param criteria 查询条件
     */
    @Override
    public void findSamplesByPage(PageBean<DtoSample> pageBean, BaseCriteria criteria) {
        SamplePreparationCriteria samplePreparationCriteria = (SamplePreparationCriteria) criteria;
        pageBean.setEntityName("DtoSample s,DtoReceiveSampleRecord r");
        pageBean.setSelect("select new com.sinoyd.lims.pro.dto.DtoSample(s.id,s.code,s.sampleTypeId,s.sampleFolderId,s.status,s.receiveId,s.samplingTimeBegin,s.preparedStatus,s.redFolderName,r.receiveSampleDate) ");
        //设置是否制备为是的分析方法id
        List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodRepository.findAll();
        List<String> analyzeMethodIds = analyzeMethods.stream().filter(DtoAnalyzeMethod::getIsPreparation).map(DtoAnalyzeMethod::getId).distinct().collect(Collectors.toList());
        samplePreparationCriteria.setAnalyzeMethodIds(analyzeMethodIds);
        //分页查询数据
        comRepository.findByPage(pageBean, samplePreparationCriteria);
        //获取已经查询到的数据
        List<DtoSample> sampleList = pageBean.getData();
        //获取所有的检测类型
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeRepository.findAll(sampleTypeIds) : new ArrayList<>();
        Map<String, String> sampleTypeNameMap = sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));
        //获取所有的测试项目数据
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdIn(sampleIds) : new ArrayList<>();
        Map<String, List<DtoAnalyseData>> anaDataGroup = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        //获取啊素有制备数据
        List<DtoSamplePreparation> samplePreparations = StringUtil.isNotEmpty(sampleIds) ? repository.findBySampleIdIn(sampleIds) : new ArrayList<>();
        Map<String, List<DtoSamplePreparation>> preparedGroupBySample = samplePreparations.stream().collect(Collectors.groupingBy(DtoSamplePreparation::getSampleId));
        for (DtoSample sample : sampleList) {
            List<DtoSamplePreparation> samplePreparationsOfSample = new ArrayList<>();
            if (StringUtil.isNotEmpty(preparedGroupBySample)) {
                samplePreparationsOfSample = preparedGroupBySample.getOrDefault(sample.getId(), new ArrayList<>());
            }
            //检测类型
            sample.setSampleTypeName(StringUtil.isNotEmpty(sampleTypeNameMap) ? sampleTypeNameMap.getOrDefault(sample.getSampleTypeId(), "") : "");
            //处理分析项目名称
            List<DtoAnalyseData> anaDataOfSample = StringUtil.isNotEmpty(anaDataGroup) ? anaDataGroup.getOrDefault(sample.getId(), new ArrayList<>()) : new ArrayList<>();
            String analyseItems = "";
            //过滤掉需要制备的测试项目
            if (StringUtil.isNotEmpty(anaDataOfSample)) {
                anaDataOfSample = anaDataOfSample.stream().filter(p -> analyzeMethodIds.contains(p.getAnalyzeMethodId())).collect(Collectors.toList());
                List<String> analyzeItemNames = anaDataOfSample.stream().sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName)).map(DtoAnalyseData::getRedAnalyzeItemName).collect(Collectors.toList());
                analyseItems = String.join("、", analyzeItemNames);
            }
            sample.setAnalyseItems(analyseItems);
            if (StringUtil.isNotEmpty(samplePreparationsOfSample)) {
                //获取所有的制备人名称
                List<String> preparedPersonNames = samplePreparationsOfSample.stream().map(DtoSamplePreparation::getPreparedPersonName).distinct().collect(Collectors.toList());
                //制备人名称
                sample.setPreparedPersonName(String.join(",", preparedPersonNames));
                //制备日期（所有制备开始日期中最早的日期）
                List<Date> prepareBeginTimes = samplePreparationsOfSample.stream().map(DtoSamplePreparation::getPreparationBeginTime).distinct().collect(Collectors.toList());
                Date beginTime = Collections.min(prepareBeginTimes);
                sample.setPreparedDate(beginTime);
            }


        }
        pageBean.setData(sampleList);
    }

    /**
     * 批量样品制备
     *
     * @param samplePreparations 样品制备数据
     * @return 已添加的样品制备数据
     */
    @Transactional
    @Override
    public List<DtoSamplePreparation> batchSave(List<DtoSamplePreparation> samplePreparations) {
        List<DtoSamplePreparation> batchSaveList = new ArrayList<>();
        for (DtoSamplePreparation samplePreparation : samplePreparations) {
            //获取到批量制备的关联样品
            List<String> sampleIdsOfPrepared = samplePreparation.getSampleIds();
            //循环添加制备信息
            for (String sampleId : sampleIdsOfPrepared) {
                DtoSamplePreparation samplePreparationTemp = new DtoSamplePreparation();
                BeanUtils.copyProperties(samplePreparation, samplePreparationTemp,"id");
                samplePreparationTemp.setSampleId(sampleId);
                batchSaveList.add(samplePreparationTemp);
            }
        }
        if (StringUtil.isNotEmpty(batchSaveList)) {
            setInstrumentForPreparation(batchSaveList);
            repository.batchInsert(batchSaveList);
        }
        return samplePreparations;
    }

    /**
     * 样品制备
     *
     * @param sampleId           需要制备的样品id
     * @param samplePreparations 样品制备数据
     * @return 已制备的样品数据
     */
    @Transactional
    @Override
    public List<DtoSamplePreparation> save(String sampleId, List<DtoSamplePreparation> samplePreparations) {
        //获取旧的样品制备数据
        List<DtoSamplePreparation> deletedList = repository.findBySampleId(sampleId);
        //判断制备时间
        for (DtoSamplePreparation samplePreparation : samplePreparations) {
            samplePreparation.setId(UUIDHelper.NewID());
            samplePreparation.setSampleId(sampleId);
            if (samplePreparation.getPreparationBeginTime().compareTo(samplePreparation.getPreparationEndTime()) > 0) {
                throw new BaseException("制备记录中存在开始时间大于结束时间的记录，请检查后保存!");
            }
        }
        //删除旧的样品制备信息
        if (StringUtil.isNotEmpty(deletedList)) {
            repository.logicDeleteById(deletedList.stream().map(DtoSamplePreparation::getId).collect(Collectors.toList()), new Date());
        }
        if (StringUtil.isNotEmpty(samplePreparations)) {
            //设置制备记录中的仪器id
            setInstrumentForPreparation(samplePreparations);
            repository.batchInsert(samplePreparations);
        }
        return samplePreparations;
    }

    /**
     * 设置制备记录中的仪器id
     *
     * @param samplePreparationList 制备记录列表
     */
    private void setInstrumentForPreparation(List<DtoSamplePreparation> samplePreparationList) {
        for (DtoSamplePreparation preparation : samplePreparationList) {
            String instrumentId = "";
            List<Map<String, String>> instrumentData = preparation.getInstrumentData();
            if (StringUtil.isNotEmpty(instrumentData)) {
                List<String> instrumentIdList = new ArrayList<>();
                for (Map<String, String> map : instrumentData) {
                    String instId = map.get("instrumentId");
                    if (StringUtil.isNotEmpty(instId)) {
                        instrumentIdList.add(instId);
                    }
                }
                if (StringUtil.isNotEmpty(instrumentIdList)) {
                    instrumentId = String.join(";", instrumentIdList);
                }
            }
            preparation.setInstrumentId(instrumentId);
        }
    }

    /**
     * 样品制备完成
     *
     * @param sampleIds 需要制备完成的样品id
     */
    @Transactional
    @Override
    public void preparationSample(List<String> sampleIds) {
        //存放未制备的样品数据
        List<String> nonPreparedCode = new ArrayList<>();
        //获取所有样品数据
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(sampleIds) : new ArrayList<>();
        //获取所有的样品制备信息
        List<DtoSamplePreparation> samplePreparations = StringUtil.isNotEmpty(sampleIds) ? repository.findBySampleIdIn(sampleIds) : new ArrayList<>();
        //按照样品id分组样品制备信息
        Map<String, List<DtoSamplePreparation>> preparationGroup = samplePreparations.stream().collect(Collectors.groupingBy(DtoSamplePreparation::getSampleId));
        for (DtoSample sample : sampleList) {
            List<DtoSamplePreparation> preparationsOfSample = StringUtil.isNotEmpty(preparationGroup) ? preparationGroup.getOrDefault(sample.getId(), new ArrayList<>()) : new ArrayList<>();
            //判断样品是否存在制备信息
            if (StringUtil.isEmpty(preparationsOfSample)) {
                nonPreparedCode.add(sample.getCode());
                continue;
            }
            sample.setPreparedStatus(EnumPRO.EnumPreParedStatus.已制备.getValue());
        }
        //判断是否存在未制备的样品
        if (StringUtil.isNotEmpty(nonPreparedCode)) {
            throw new BaseException(String.join(";", nonPreparedCode) + "样品未有制备记录,请检查");
        }
        if (StringUtil.isNotEmpty(sampleList)) {
            sampleRepository.batchUpdate(sampleList);
        }
    }

    /**
     * 查询样品的详细信息以及样品制备信息
     *
     * @param sampleId 样品id
     * @return 样品详细信息
     */
    @Override
    public DtoSampleOfPrepared findDetails(String sampleId) {
        DtoSample sample = sampleRepository.findOne(sampleId);
        DtoSampleOfPrepared sampleOfPrepared = new DtoSampleOfPrepared();
        sampleOfPrepared.setSampleId(sample.getId());
        sampleOfPrepared.setCode(sample.getCode());
        DtoSampleType sampleType = StringUtil.isNotEmpty(sample.getSampleTypeId()) ? sampleTypeRepository.findOne(sample.getSampleTypeId()) : null;
        sampleOfPrepared.setSampleTypeName(sampleType != null ? sampleType.getTypeName() : "");
        sampleOfPrepared.setRedFolderName(sample.getRedFolderName());
        sampleOfPrepared.setSamplingTimeBegin(DateUtil.dateToString(sample.getSamplingTimeBegin(), DateUtil.YEAR));
        DtoReceiveSampleRecord receiveSampleRecord = StringUtil.isNotEmpty(sample.getReceiveId()) ? receiveSampleRecordRepository.findOne(sample.getReceiveId()) : null;
        String receiveTime = "";
        if (receiveSampleRecord != null) {
            receiveTime = StringUtil.isNotNull(receiveSampleRecord.getReceiveSampleDate()) ? DateUtil.dateToString(receiveSampleRecord.getReceiveSampleDate(), DateUtil.YEAR) : "";
        }
        sampleOfPrepared.setReceiveTime(receiveTime);
        //获取所有需要制备的方法
        List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodRepository.findAll();
        List<String> analyzeMethodIds = analyzeMethods.stream().filter(DtoAnalyzeMethod::getIsPreparation).map(DtoAnalyzeMethod::getId).distinct().collect(Collectors.toList());
        //获取样品下所有的测试项目数据
        List<DtoAnalyseData> analyseData = analyseDataRepository.findBySampleId(sampleId);
        //过滤掉不需要制备的测试项目
        analyseData = analyseData.stream().filter(p -> analyzeMethodIds.contains(p.getAnalyzeMethodId())).collect(Collectors.toList());
        List<String> analyzeItemNames = analyseData.stream().sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName)).map(DtoAnalyseData::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
        sampleOfPrepared.setAnalyzeItemNames(String.join("、", analyzeItemNames));
        //获取样品制备数据
        List<DtoSamplePreparation> samplePreparations = repository.findBySampleId(sampleId);
        if (StringUtil.isNotEmpty(samplePreparations)) {
            //设置制备记录的仪器信息
            setInstrumentDataForPreparation(samplePreparations);
        }
        sampleOfPrepared.setSamplePreparations(samplePreparations);
        return sampleOfPrepared;
    }

    /**
     * 退回
     *
     * @param sampleIds 需退回的样品id
     */
    @Override
    public void backPreparation(List<String> sampleIds) {
        //存放未制备的样品数据
        List<String> nonPreparedCode = new ArrayList<>();
        //获取所有样品数据
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(sampleIds) : new ArrayList<>();
        for (DtoSample sample : sampleList) {
            // 判断是否存在未制备完成的样品
            if (EnumPRO.EnumPreParedStatus.未制备.getValue().equals(sample.getPreparedStatus())) {
                nonPreparedCode.add(sample.getCode());
                continue;
            }
            sample.setPreparedStatus(EnumPRO.EnumPreParedStatus.未制备.getValue());
        }
        //判断是否存在未制备的样品
        if (StringUtil.isNotEmpty(nonPreparedCode)) {
            throw new BaseException(String.join(";", nonPreparedCode) + "未制备完成,请检查");
        }
        if (StringUtil.isNotEmpty(sampleList)) {
            sampleRepository.save(sampleList);
        }
    }

    /**
     * 设置制备记录的仪器信息
     *
     * @param samplePreparations 制备记录
     */
    private void setInstrumentDataForPreparation(List<DtoSamplePreparation> samplePreparations) {
        List<String> instrumentIdList = new ArrayList<>();
        for (DtoSamplePreparation preparation : samplePreparations) {
            if (StringUtil.isNotEmpty(preparation.getInstrumentId())) {
                instrumentIdList.addAll(new ArrayList<>(Arrays.asList(preparation.getInstrumentId().split(";"))));
            }
        }
        List<DtoInstrument> instrumentList = StringUtil.isNotEmpty(instrumentIdList) ? instrumentRepository.findAll(instrumentIdList) : new ArrayList<>();
        Map<String, DtoInstrument> instrumentMap = instrumentList.stream().collect(Collectors.toMap(DtoInstrument::getId, dto -> dto));
        //再次遍历制备记录，设置每一条记录对应的仪器id及名称
        for (DtoSamplePreparation preparation : samplePreparations) {
            List<Map<String, String>> instrumentData = new ArrayList<>();
            if (StringUtil.isNotEmpty(preparation.getInstrumentId())) {
                List<String> loopInstIdList = new ArrayList<>(Arrays.asList(preparation.getInstrumentId().split(";")));
                for (String instId : loopInstIdList) {
                    if (instrumentMap.containsKey(instId)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("instrumentId", instId);
                        map.put("instrumentName", instrumentMap.get(instId).getInstrumentName());
                        instrumentData.add(map);
                    }
                }
            }
            preparation.setInstrumentData(instrumentData);
        }
    }

    /**
     * 删除样品制备技术局
     *
     * @param id 样品制备id
     * @return 删除的条数
     */
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        return repository.logicDeleteById(id,new Date());
    }

    @Autowired
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setReceiveSampleRecordRepository(ReceiveSampleRecordRepository receiveSampleRecordRepository) {
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
    }

    @Autowired
    public void setInstrumentRepository(InstrumentRepository instrumentRepository) {
        this.instrumentRepository = instrumentRepository;
    }
}
