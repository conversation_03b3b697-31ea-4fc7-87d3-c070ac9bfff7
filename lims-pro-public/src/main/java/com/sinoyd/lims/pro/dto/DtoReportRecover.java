package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReportRecover;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.ArrayList;
import java.util.List;


/**
 * Dtoreportrecover实体
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ReportRecover")
 @Data
 @DynamicInsert
 public  class DtoReportRecover extends ReportRecover {
    private static final long serialVersionUID = 1L;

    @Transient
    private List<DtoReportRecover2Report> reportRecover2ReportList = new ArrayList<>();

    /**
     * 报告编号
     */
    @Transient
    private String code;

    /**
     * 报告类型
     */
    @Transient
    private String reportTypeName;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目类型
     */
    @Transient
    private String projectTypeName;

    /**
     * 受检方
     */
    @Transient
    private String inspectedEnt;

    /**
     * 报告状态
     */
    @Transient
    private String status;

    /***
     * 委托方的名称
     */
    @Transient
    private String customerName;

    /***
     * 报告标识
     */
    @Transient
    private String reportId;

    /***
     * 报告年份
     */
    @Transient
    private String reportYear;
}