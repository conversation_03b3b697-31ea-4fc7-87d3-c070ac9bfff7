package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.base.entity.BaseEntity;

import com.sinoyd.lims.pro.dto.DtoEvaluationRecord;
import lombok.Data;

import java.util.*;

/**
 * 报告评价结果
 * <AUTHOR>
 * @version V1.0.0 2019/11/12
 * @since V100R001
 */
@Data
public class DtoEvaluationResult implements BaseEntity {

    /**
     * 主键id
     */
    private String id;

    /**
     * 关联id集合
     */
    private List<String> objectIds = new ArrayList<>();

    /**
     * 类型
     */
    private Integer objectType;

    /**
     * 点位计划
     */
    private Integer folderPlan;

    /**
     * 点位名称
     */
    private String watchSpot;

    /**
     * 平均值结论
     */
    private String avgResult;

    /**
     * 最小值结论
     */
    private String minResult;

    /**
     * 最大值结论
     */
    private String maxResult;

    /**
     * 点位详情
     */
    private List<DtoEvaluationRecord> evaluationRecord = new ArrayList<>();
}