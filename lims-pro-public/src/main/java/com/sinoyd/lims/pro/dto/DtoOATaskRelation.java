package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.OATaskRelation;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoOATaskRelation实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_OATaskRelation")
 @Data
 @DynamicInsert
 public  class DtoOATaskRelation extends OATaskRelation {

 }