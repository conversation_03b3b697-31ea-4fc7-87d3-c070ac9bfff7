package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentCheckOut;

import java.util.List;


/**
 * ProjectInstrumentDetails操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
public interface ProjectInstrumentDetailsService extends IBaseJpaService<DtoProjectInstrumentDetails, String> {

    /**
     * 批量新增出入库记录明细
     *
     * @param projectInstrumentDetails 出入库记录明细实体
     * @return 出入库记录明细列表
     */
    int addDetailsBatch(DtoProjectInstrumentDetails projectInstrumentDetails);

    /**
     * 修改出入库记录明细
     *
     * @param projectInstrumentDetails 出入库记录明细实体
     * @return 出入库记录明细列表
     */
    DtoProjectInstrumentDetails updateDetail(DtoProjectInstrumentDetails projectInstrumentDetails);

    /**
     * 批量删除出入库记录明细
     *
     * @param ids 出入库记录明细id列表
     * @return 删除的数量
     */
    int deleteDetailsBatch(List<String> ids);

    /**
     * 查询所有的仪器出库信息
     *
     * @param pageBean 分页数据
     * @param criteria 分页查询条件
     */
    void findInstrumentStorage(PageBean<DtoInstrumentCheckOut> pageBean, BaseCriteria criteria);

    /**
     * 批量确认入库信息
     *
     * @param isConfirm 是否为确认操作
     * @param ids 出入库明细id
     */
    void batchOperationConfirm(Boolean isConfirm,List<String> ids);
}