package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoImportStandardMethod;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethod;
import com.sinoyd.lims.lim.repository.lims.StandardMethodRepository;
import com.sinoyd.lims.lim.service.ImportStandardMethodService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.StandardMethodVerifyHandle;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 标准方法导入实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Service
public class ImportStandardMethodServiceImpl implements ImportStandardMethodService {

    @Autowired
    private StandardMethodRepository standardMethodRepository;

    @Autowired
    private ImportUtils importUtils;

    /**
     * 标准方法导入
     *
     * @param file      传入的文件
     * @param objectMap 业务数据Map
     * @return List<DtoStandardMethod>
     * @throws Exception 异常抛出
     */
    @Override
    @Transactional
    public List<DtoStandardMethod> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        StandardMethodVerifyHandle verifyHandler = new StandardMethodVerifyHandle();
        ExcelImportResult<DtoImportStandardMethod> importResult = getExcelData(verifyHandler, file, response);

        // 获取校验成功的导入数据
        List<DtoImportStandardMethod> importList = importResult.getList();
        // 移除空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getMethodId()));
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }

        // 转换为实体并保存
        List<DtoStandardMethod> standardMethods = importToEntity(importList);
        if (StringUtil.isNotEmpty(standardMethods)) {
            addData(standardMethods);
        }

        return standardMethodRepository.findAll();
    }

    /**
     * 导入数据保存到数据库
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoStandardMethod> data) {
        standardMethodRepository.save(data);
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param file     传入的文件
     * @param response
     * @return List
     */
    @Override
    public ExcelImportResult<DtoImportStandardMethod> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param verifyHandler 校验器
     * @param file          传入的文件
     * @param response      响应体
     * @return 导入结果
     */
    @Override
    public ExcelImportResult<DtoImportStandardMethod> getExcelData(IExcelVerifyHandler<DtoImportStandardMethod> verifyHandler,
                                                                   MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        // 设置表头区域
        params.setTitleRows(0);
        // 设置表头开始行
        params.setHeadRows(1);
        // 设置开始工作簿
        params.setStartSheetIndex(0);
        // 设置是否校验
        params.setNeedVerify(true);
        // 设置校验handle
        params.setVerifyHandler(verifyHandler);

        ExcelImportResult<DtoImportStandardMethod> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(), DtoImportStandardMethod.class, params);

        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "标准方法导入错误信息");
            PoiExcelUtils.downLoadExcel("标准方法导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 导入数据转换为实体
     *
     * @param importList 导入数据
     * @return 标准方法实体列表
     */
    private List<DtoStandardMethod> importToEntity(List<DtoImportStandardMethod> importList) {
        List<DtoStandardMethod> standardMethods = new ArrayList<>();
        // 获取所有已存在的标准方法
        List<DtoStandardMethod> existingMethods = standardMethodRepository.findAll();
        // 创建methodId到实体的映射
        Map<String, DtoStandardMethod> existingMethodMap = existingMethods.stream()
                .collect(Collectors.toMap(DtoStandardMethod::getMethodId, method -> method));
        // 导入时，同一个方法id，只导入一条，覆盖最新的信息
        Map<String, List<DtoImportStandardMethod>> importStandardMethodMap = importList.stream().collect(Collectors.groupingBy(DtoImportStandardMethod::getMethodId));
        for (Map.Entry<String, List<DtoImportStandardMethod>> entry : importStandardMethodMap.entrySet()) {
            // 获取导入数据中相同方法id中的最后一条
            entry.getValue().stream().max(Comparator.comparing(DtoImportStandardMethod::getRowNum)).ifPresent(importStandardMethod -> {
                DtoStandardMethod standardMethod;
                // 检查是否存在相同methodId的记录
                if (existingMethodMap.containsKey(importStandardMethod.getMethodId())) {
                    // 如果存在，获取已存在的实体并更新其他字段
                    standardMethod = existingMethodMap.get(importStandardMethod.getMethodId());
                    // 更新其他字段
                    standardMethod.setItemName(importStandardMethod.getItemName());
                    standardMethod.setSampleType(importStandardMethod.getSampleType());
                    standardMethod.setCountryStandard(importStandardMethod.getCountryStandard());
                    standardMethod.setMethodName(importStandardMethod.getMethodName());
                } else {
                    // 如果不存在，创建新实体
                    standardMethod = new DtoStandardMethod();
                    BeanUtils.copyProperties(importStandardMethod, standardMethod);
                }
                standardMethods.add(standardMethod);
            });

        }
        return standardMethods;
    }

    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    @Override
    public void downLoadExcel(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        // 获取仪器空数据
        List<DtoImportStandardMethod> importStandardMethods = new ArrayList<>();
        //endregion

        //region 赋值工作单并下载
        // 获取需要下载的工作单
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportStandardMethod.class, importStandardMethods);
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
        //endregion
    }
}
