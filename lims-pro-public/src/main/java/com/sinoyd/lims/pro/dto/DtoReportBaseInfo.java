package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReportBaseInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoReportBaseInfo实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ReportBaseInfo")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoReportBaseInfo extends ReportBaseInfo {

}