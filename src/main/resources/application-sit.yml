server:
  # 后端端口
  port: ${PORT:8961}
  tomcat:
    # tomcat的URI编码，保证get请求采用指定的编码，这里是utf-8编码
    uri-encoding: utf-8

frame-boot:
  # swagger相关配置
  swagger:
    # 是否启用swagger，true: 启用， false: 不启用
    enabled: true
    # swagger扫描包
    basePackage: com.sinoyd
    # swagger在线项目名称
    title: LIMS5.1
    # swagger在线项目描述
    description: 系统Rest接口API

  # 框架注册码服务校验地址
  regVerifyUrl: ${REG_VERIFY_URL:http://*************:8080/api/register/auth/verify}
  # 权限同步网关服务地址前缀
  gateUrlPrefix: ${GATE_URL_PREFIX:http://localhost:9099/api/proxy}
  # rest请求超时设置(单位：毫秒)
  restRequestTimeout: 3000

  # 互联网用户中心相关配置
  user-center:
    # 是否为本地离线模式，true: 离线模式，false：云模式
    localMode: ${USER_CENTER_LOCALMODE:true}
    # 用户中心接口服务地址
    serverUrl: ${USER_CENTER_SERVERURL:http://*************:8857/api/auth/users}
    # 客户端ID(云版模式需要配置)
    clientId: ${USER_CENTER_CLIENTID:8}
    # 客户端秘钥(云版模式需要配置)
    clientSecret: ${USER_CENTER_CLIENTSECRET:jIkjGEIfn7lYem52gcnfqHiLGkAEav98DfI5BRt4}
    # 短信内容中系统名称(云版模式需要配置)
    sendSmsProductName: ${USER_CENTER_SMSNAME:云框架}
    # 密码模式 1：数字，2：大写字母，4：小写字母，8：特殊字符，-1 不进行验证，按位与进行叠加配置
    passwordMode: 15
    # 密码长度
    passwordLength: 8
    # 单位有效期过期提醒时间，以天为单位
    remindCycle: 30
    # 登录类型，离线模式才起效果
    loginTypes:
      - code: useName # 用户名
        name: 用户名 # 描述
      - code: email # 邮箱
        name: 邮箱 # 描述

  user-lock:
    # 用户登录失败是否锁定账号，true: 锁定，false: 不锁定
    enabled: false

# spring相关配置
spring:
  application:
    #  应用名称
    name: sinoyd-lims
  # Redis 数据源
  redis:
    # Redis主机
    host: ${REDIS_HOST:*************}
    # Redis 密码，如果没有，直接空
    password: sinoyd
    # 连接超时时间（毫秒）
    timeout: 100000
    # Redis 库
    database: 0

  # 数据源
  datasource:
    # 业务数据源
    primary:
      # DB驱动名称
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      # DB连接url
      url: *******************************************************
      # DB用户名
      username: sa
      # DB密码
      password: 11111
      # 指定启动连接池时，初始建立的连接数量
      initialSize: 10
      # 指定必须保持连接的最小值(For DBCP and Tomcat connection pools)
      minIdle: 5
      # 指定连接池中最大的活跃连接数
      maxActive: 60
      # 指定连接池等待连接返回的最大等待时间，单位毫秒
      maxWait: 60000
      # 当连接空闲时，是否执行连接测试
      testWhileIdle: true
      # 指定是否池化statements
      poolPreparedStatements: false

    # 框架数据源
    frame:
      # DB驱动名称
      driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      # DB连接url
      url: jdbc:sqlserver://${SQLSER_HOST:*************};DatabaseName=Frame5.1
      # DB用户名
      username: sa
      # DB密码
      password: 11111
      # 指定启动连接池时，初始建立的连接数量
      initialSize: 10
      # 指定必须保持连接的最小值(For DBCP and Tomcat connection pools)
      minIdle: 5
      # 指定连接池中最大的活跃连接数
      maxActive: 60
      # 指定连接池等待连接返回的最大等待时间，单位毫秒
      maxWait: 60000
      # 当连接空闲时，是否执行连接测试
      testWhileIdle: true
      # 指定是否池化statements
      poolPreparedStatements: false

    # 工作流数据源
    activiti:
      # DB驱动名称
      driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      # DB连接url
      url: *******************************************************
      # DB用户名
      username: sa
      # DB密码
      password: 11111
      type: com.alibaba.druid.pool.DruidDataSource
      # 指定启动连接池时，初始建立的连接数量
      initialSize: 5
      # 指定必须保持连接的最小值(For DBCP and Tomcat connection pools)
      minIdle: 5
      # 指定连接池中最大的活跃连接数
      maxActive: 20
      # 指定连接池等待连接返回的最大等待时间，单位毫秒
      maxWait: 60000
      # 指定空闲连接检查、废弃连接清理、空闲连接池大小调整之间的操作时间间隔，单位毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 指定一个空闲连接最少空闲多久后可被清除，单位毫秒
      minEvictableIdleTimeMillis: 300000
      # 指定获取连接时连接校验的sql查询语句
      validationQuery: SELECT 1
      # 当连接空闲时，是否执行连接测试
      testWhileIdle: true
      # 当从连接池借用连接时，是否测试该连接，true：测试，false：不测试
      testOnBorrow: false
      # 在连接归还到连接池时是否测试该连接，true：测试，false：不测试
      testOnReturn: false
      # 指定是否池化statements
      poolPreparedStatements: false
      # 指定PSCache的大小
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的filters， wall指防火墙
      filters: stat,wall,log4j
      # 通过connectProperties属性来打开mergeSql功能
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 合并多个DruidDataSource的监控数据
      useGlobalDataSourceStat: true

    # 仪器解析数据源
    instrumentParse:
      # DB驱动名称
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      # DB连接url
      url: ***********************************************************
      # DB用户名
      username: sa
      # DB密码
      password: 1qaz
      # 指定启动连接池时，初始建立的连接数量
      initialSize: 10
      # 指定必须保持连接的最小值(For DBCP and Tomcat connection pools)
      minIdle: 5
      # 指定连接池中最大的活跃连接数
      maxActive: 50
      # 指定连接池等待连接返回的最大等待时间，单位毫秒
      maxWait: 60000
      # 当连接空闲时，是否执行连接测试
      testWhileIdle: true

  # JPA相关配置
  jpa:
    # DB类型
    database: sql_server
    # 方言配置
    database-platform: com.sinoyd.frame.configuration.CustomSQLServer2008JSONDialect
    hibernate:
      # 自动创建|更新|验证数据库表结构，一般开发时用
      # ddl-auto: update
      naming:
        # 设置使用的命名策略，负责模型对象层次的处理，将对象模型处理为逻辑名称，
        # 当没有使用@Table和@Column注解时，implicit-strategy配置项才会被使用，当对象模型中已经指定时，implicit-strategy并不会起作用
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
        # 负责映射成真实的数据名称的处理，将逻辑名称处理为物理名称，一定会被应用，与对象模型中是否显式地指定列名或者已经被隐式决定无关
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    # 在日志中显示sql， true：显示，false：不显示。一般开发是配置true
    show-sql: true
    properties:
      hibernate:
        session_factory:
          # JPA拦截器
          statement_inspector: com.sinoyd.frame.inspector.JpaInterceptor
        # JPA打印sql的时候，true：格式化，false：不格式化
        format_sql: true

# 文件相关配置
fileProps:
  # 文件上传路径
  filePath: E:/LIMS5.1/source/lims-proxy/files
  # 临时目录（生成的报表会先放临时目录）
  outputPath: E:/LIMS5.1/source/lims-proxy/outputs
  # 仪器解析文件路径（从仪器解析项目获取，LIMS需要读取相关文件）
  instrumentParseFilePath: E:/LIMS5.1/source/lims-proxy/files/instruments
  # 报表、采样单、原始记录单等模板
  templatePath: E:/LIMS5.1/source/lims-proxy/files/report_templates
  # 允许上传的文件类型
  fileSuffix: jpg,doc,docx,xls,xlsx,jpeg,png,txt,mp3,flac,avi,mp4

# 日志相关配置
logging:
  # 日志等级(debug、info、warn、error)
  level: info
  # 日志存放路径
  file: d:/SpringBoot/SinoydLIMS51.log

# 是否暴露端点，true：暴露，false：不暴露
management:
  security:
    enabled: false

#jwt token相关设置
jwt:
  user:
    # token存放请求头部key名
    token-header: Authorization
    # token有效期（单位：分钟），超过该时长token将会过期
    expire: 120