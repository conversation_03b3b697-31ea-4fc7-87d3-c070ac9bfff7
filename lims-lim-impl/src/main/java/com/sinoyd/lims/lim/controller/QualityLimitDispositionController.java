package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.rcc.DtoQualityLimitDisposition;
import com.sinoyd.lims.lim.criteria.QualityLimitDispositionCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.QualityLimitDispositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * QualityLimitDisposition服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2024/5/27
 * @since V100R001
 */
 @Api(tags = "示例: QualityLimitDisposition服务")
 @RestController
 @RequestMapping("api/lim/qualityLimitDisposition")
 @Validated
 public class QualityLimitDispositionController extends BaseJpaController<DtoQualityLimitDisposition, String, QualityLimitDispositionService> {


    /**
     * 分页动态条件查询QualityLimitDisposition
     *
     * @param qualityLimitDispositionCriteria 条件参数
     * @return RestResponse<List < QualityLimitDisposition>>
     */
    @ApiOperation(value = "分页动态条件查询QualityLimitDisposition", notes = "分页动态条件查询QualityLimitDisposition")
    @GetMapping
    public RestResponse<List<DtoQualityLimitDisposition>> findByPage(QualityLimitDispositionCriteria qualityLimitDispositionCriteria) {
        PageBean<DtoQualityLimitDisposition> pageBean = super.getPageBean();
        RestResponse<List<DtoQualityLimitDisposition>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, qualityLimitDispositionCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询QualityLimitDisposition
     *
     * @param id 主键id
     * @return RestResponse<DtoQualityLimitDisposition>
     */
    @ApiOperation(value = "按主键查询QualityLimitDisposition", notes = "按主键查询QualityLimitDisposition")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoQualityLimitDisposition> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoQualityLimitDisposition> restResponse = new RestResponse<>();
        DtoQualityLimitDisposition qualityLimitDisposition = service.findOne(id);
        restResponse.setData(qualityLimitDisposition);
        restResponse.setRestStatus(StringUtil.isNull(qualityLimitDisposition) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增QualityLimitDisposition
     *
     * @param qualityLimitDisposition 实体列表
     * @return RestResponse<DtoQualityLimitDisposition>
     */
    @ApiOperation(value = "新增QualityLimitDisposition", notes = "新增QualityLimitDisposition")
    @PostMapping
    public RestResponse<DtoQualityLimitDisposition> create(@Validated @RequestBody DtoQualityLimitDisposition qualityLimitDisposition) {
        RestResponse<DtoQualityLimitDisposition> restResponse = new RestResponse<>();
        restResponse.setData(service.save(qualityLimitDisposition));
        return restResponse;
    }

    /**
     * 新增QualityLimitDisposition
     *
     * @param qualityLimitDisposition 实体列表
     * @return RestResponse<DtoQualityLimitDisposition>
     */
    @ApiOperation(value = "修改QualityLimitDisposition", notes = "修改QualityLimitDisposition")
    @PutMapping
    public RestResponse<DtoQualityLimitDisposition> update(@Validated @RequestBody DtoQualityLimitDisposition qualityLimitDisposition) {
        RestResponse<DtoQualityLimitDisposition> restResponse = new RestResponse<>();
        restResponse.setData(service.update(qualityLimitDisposition));
        return restResponse;
    }

    /**
     * "根据id批量删除QualityLimitDisposition
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除QualityLimitDisposition", notes = "根据id批量删除QualityLimitDisposition")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deleteByIds(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 判断当前类型是否存在默认
     *
     * @param qualityLimitDisposition 实体列表
     * @return Boolean
     */
    @ApiOperation(value = "判断当前类型是否存在默认", notes = "判断当前类型是否存在默认")
    @PostMapping("/judgmentInspect")
    public RestResponse<Boolean> inspectDisposition(@RequestBody DtoQualityLimitDisposition qualityLimitDisposition) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.inspectDisposition(qualityLimitDisposition));
        return restResponse;
    }

    /**
     * 根据配置Id获取测试项目集合
     *
     * @param dispositionId 配置Id
     * @return List<DtoTest>
     */
    @ApiOperation(value = "根据配置Id获取测试项目集合", notes = "根据配置Id获取测试项目集合")
    @GetMapping("/test/{dispositionId}")
    public RestResponse<List<DtoTest>> findTestListByDispositionId(@PathVariable(name = "dispositionId") String dispositionId) {
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        restResponse.setData(service.findTestListByDispositionId(dispositionId));
        return restResponse;
    }
}