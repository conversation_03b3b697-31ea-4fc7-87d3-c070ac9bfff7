package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * QualityControlEvaluate实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/9
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "QualityControlEvaluate")
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
public class QualityControlEvaluate extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public  QualityControlEvaluate() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }
    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 关联对象id（分析数据id）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("关联对象id")
    private String objectId;

    /**
     * 质控信息id
     */
    @Column(length = 50)
    @ApiModelProperty("质控信息id")
    private String qcId;

    /**
     * 质控配置Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("质控限值id")
    private String limitId;

    /**
     * 检查项（对应质控限值配置中的检查项，针对质控样，检查项默认“出证结果”）
     */
    @Column(length = 100)
    @ApiModelProperty("检查项（对应质控限值配置中的检查项，针对质控样，检查项默认“出证结果”）")
    private String checkItem;

    /**
     * 评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）
     */
    @Column
    @ApiModelProperty("评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）")
    private Integer judgingMethod;

    /**
     * 是否合格（是否合格，判定不满足判定条件时为空）
     */
    @Column
    @ApiModelProperty("是否合格（是否合格判定不满足判定条件时为空）")
    private Boolean isPass;

    /**
     * 检查项值
     */
    @Column(length = 100)
    @ApiModelProperty("检查项值")
    private String checkItemValue;

    /**
     * 允许限值
     */
    @Column(length = 50)
    @ApiModelProperty("允许限值")
    private String allowLimit;

    /**
     * 不确定度类型
     */
    @ApiModelProperty("不确定度类型")
    private Integer uncertainType;

    /**
     * 量纲名称
     */
    @Column(length=50)
    @ApiModelProperty("量纲名称")
    private String dimensionName;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 评判类型
     */
    @Column
    @ApiModelProperty("评价类型,系统验证1/人工评价2")
    private Integer judgeType;

    /**
     * 说明
     */
    @ApiModelProperty("说明")
    private String remark;
}
