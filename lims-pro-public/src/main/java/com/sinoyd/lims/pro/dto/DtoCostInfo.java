package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.CostInfo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoCostInfo实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_CostInfo") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoCostInfo extends CostInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目类型id
     */
    @Transient
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    @Transient
    private String projectTypeName;

    /**
     * 项目等级
     */
    @Transient
    private Integer grade;

    /**
     * 委托方id
     */
    @Transient
    private String customerId;

    /**
     * 委托方名称
     */
    @Transient
    private String customerName;

    /**
     * 项目登记人Id
     */
    @Transient
    private String inceptPersonId;

    /**
     * 登记时间
     */
    @Transient
    private Date inceptTime;

    /**
     * 项目登记人名称
     */
    @Transient
    private String inceptPersonName;

    /**
     * 费用明细
     */
    @Transient
    private List<DtoCostInfoDetail> costInfoDetail = new ArrayList<>();

   /**
    * 意见
    */
   @Transient
   private String comment;


}