package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoLogForPlan;

import java.util.List;


/**
 * LogForPlan数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
public interface LogForPlanRepository extends IBaseJpaPhysicalDeleteRepository<DtoLogForPlan, String> {

    List<DtoLogForPlan> findByObjectId(String objectId);
}