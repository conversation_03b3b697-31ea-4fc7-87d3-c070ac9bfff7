package com.sinoyd.lims.pro.dto.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 检测单数据保存的dto
 * <AUTHOR>
 * @version V1.0.0 2019/12/11
 * @since V100R001
 */
@Data
public class DtoWorkSheetSave {

    /**
     * 检测单属性
     */
    List<DtoAnalyseDataProperty> workSheetProperty = new ArrayList<>();


    /**
     * 分析日期（需要能保存修改）
     */

    /**
     * 分析时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date analyzeTime;


    /**
     * 检测单id
     */
    private String workSheetFolderId;


    /**
     * 分析完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date finishTime;

}
