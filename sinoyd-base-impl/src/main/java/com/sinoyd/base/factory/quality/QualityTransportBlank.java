package com.sinoyd.base.factory.quality;


import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;

import java.util.List;
import java.util.Map;

public class QualityTransportBlank extends QualityControlKind {

    /**
     * 空白类别
     *
     * @return 空白类别
     */
    @Override
    public Integer qcTypeValue() {
        return 256;
    }

    /**
     * 返回对应的质控名称
     *
     * @return 质控名称
     */
    @Override
    public String qcTypeName() {
        return "运输空白";
    }

    /**
     * 样品类别
     *
     * @param qcGrade 质控类型
     * @return 质控类别
     */
    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "运输空白";
    }

    /**
     * 记录
     *
     * @param map 参数
     * @return 记录内容
     */
    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了运输空白样。";
    }

    @Override
    public DtoQualityConfig getQualityConfig() {
        return new DtoQualityConfig();
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return new DtoQualityConfig();
    }

    /**
     * 样品名称
     *
     * @param folderName 原样名称
     * @param qcGrade    质控类型
     * @return 样品名称
     */
    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        return "运输空白样";
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}
