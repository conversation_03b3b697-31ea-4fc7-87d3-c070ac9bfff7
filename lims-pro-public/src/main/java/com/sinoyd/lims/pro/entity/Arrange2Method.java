package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 采样方法实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/7/25
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "Arrange2Method")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Arrange2Method extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Arrange2Method() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 采样计划id
     */
    @ApiModelProperty("采样计划id")
    private String samplingPlanId;

    /**
     * 采样方法id
     */
    @ApiModelProperty("采样方法id")
    private String samplingMethodId;

    /**
     * 检测类型id
     */
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;

    /**
     * 采样方法名称
     */
    @ApiModelProperty("采样方法名称")
    private String methodName;

    /**
     * 采样方法别名
     */
    @ApiModelProperty("采样方法别名")
    private String alias;

    /**
     * 标准编号
     */
    @ApiModelProperty("标准编号")
    private String standardCode;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
