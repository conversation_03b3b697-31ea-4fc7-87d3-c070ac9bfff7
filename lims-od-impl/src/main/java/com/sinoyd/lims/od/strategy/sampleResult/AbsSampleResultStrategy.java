package com.sinoyd.lims.od.strategy.sampleResult;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.od.dto.DtoLabGroup;
import com.sinoyd.lims.od.dto.DtoLabSeq;
import com.sinoyd.lims.od.dto.DtoOdSample;
import com.sinoyd.lims.od.dto.DtoSampleResult;
import com.sinoyd.lims.od.dto.customer.DtoGroupSummaryTemp;
import com.sinoyd.lims.od.dto.customer.DtoSampleResultTemp;
import com.sinoyd.lims.od.enums.EnumOds;
import com.sinoyd.lims.od.repository.*;
import com.sinoyd.lims.od.service.OdPersonAnswerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 样品结果汇总抽象类
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/11
 * @since V100R001
 */
public abstract class AbsSampleResultStrategy {

    protected SampleResultRepository sampleResultRepository;

    protected TaskRepository taskRepository;

    protected OdSampleRepository odSampleRepository;

    protected LabGroupRepository labGroupRepository;

    protected LabSeqRepository labSeqRepository;

    protected OdPersonAnswerRepository odPersonAnswerRepository;

    protected OdPersonAnswerService odPersonAnswerService;

    protected StationarySourceResultRepository stationarySourceResultRepository;

    protected StationarySourceResultDetailRepository stationarySourceResultDetailRepository;

    protected OdPersonRepository odPersonRepository;

    protected EnvGasResultRepository envGasResultRepository;

    /**
     * 根据样品id查询所选实验组
     *
     * @param taskType 任务类型
     * @param sampleId 样品id
     * @return 实验组集合
     */
    public List<DtoGroupSummaryTemp> findDefaultGroupBySampleId(Integer taskType, String sampleId) {
        List<DtoLabGroup> labGroupList = labGroupRepository.findBySampleId(sampleId);
        // 筛选已经被
        List<DtoLabGroup> calculateaLabGroup = labGroupList.stream().filter(DtoLabGroup::getIsCalculate).collect(Collectors.toList());
        if (StringUtil.isEmpty(calculateaLabGroup)) {
            // 默认实验组
            calculateaLabGroup = getDefaultGroup(labGroupList);
        }
        // 转化为临时传输
        List<DtoGroupSummaryTemp> groupSummaryTemps = new ArrayList<>();
        for (DtoLabGroup labGroup : calculateaLabGroup) {
            DtoGroupSummaryTemp groupSummaryTemp = new DtoGroupSummaryTemp();
            groupSummaryTemp.setGroupId(labGroup.getId());
            groupSummaryTemp.setCalculateSn(labGroup.getCalculateSn());
            groupSummaryTemp.setSampleId(labGroup.getSampleId());
            groupSummaryTemp.setTaskType(taskType);
            groupSummaryTemps.add(groupSummaryTemp);
        }
        // 按最终分析顺序排序
        groupSummaryTemps.sort(Comparator.comparing(DtoGroupSummaryTemp::getCalculateSn));

        return groupSummaryTemps;
    }


    /**
     * 计算样品结果
     *
     * @param sampleResultTemp 样品结果汇总传输实体
     * @return 样品结果
     */
    @Transactional
    public DtoSampleResult calculate(DtoSampleResultTemp sampleResultTemp) {
        List<DtoLabGroup> labGroupList = sampleResultTemp.getLabGroupList();
        DtoSampleResult sampleResult = sampleResultTemp.getSampleResult();
        DtoOdSample odSample = odSampleRepository.findOne(sampleResultTemp.getSampleId());
        if (StringUtil.isNull(sampleResult)) {
            // 初始化样品结果容器
            sampleResult = new DtoSampleResult();
        }
        sampleResult.setSampleState(odSample.getSampleState());
        if (StringUtil.isNotEmpty(labGroupList)) {
            // 样品未完成时，实时计算结果
            if (!odSample.getSampleState().equals(EnumOds.EnumOdsampleState.已完成.getValue())) {
                // 根据所选实验组计算结果
                calculateSampleResult(sampleResult, labGroupList);
                labGroupList.forEach(p -> p.setIsCalculate(true));
                // 更新汇总结果，旧数据取消作为最终分析结果
                List<String> groupIds = labGroupList.stream().map(DtoLabGroup::getId).collect(Collectors.toList());
                List<DtoLabGroup> oldGroupList = labGroupRepository.findBySampleId(sampleResult.getSampleId());
                List<DtoLabGroup> updateGroupList = oldGroupList.stream().filter(p -> !groupIds.contains(p.getId())).peek(p -> {
                    p.setIsCalculate(false);
                    p.setCalculateSn(-1);
                }).collect(Collectors.toList());
                labGroupList.addAll(updateGroupList);
                labGroupRepository.save(labGroupList);
            }
        }
        return sampleResult;
    }


    /**
     * 获取实验组汇总信息
     *
     * @param groupSummaryTemps 实验组汇总临时传输实体
     * @return 汇总信息集合
     */
    public abstract List<DtoLabGroup> findGroupSummary(List<DtoGroupSummaryTemp> groupSummaryTemps);

    /**
     * 过滤默认实验组
     *
     * @return 默认实验组
     */
    protected abstract List<DtoLabGroup> getDefaultGroup(List<DtoLabGroup> labGroupList);

    /**
     * 计算样品实验结果
     *
     * @param sampleResult 样品结果实体
     * @param labGroupList 汇总实验组集合
     */
    @Transactional
    protected abstract void calculateSampleResult(DtoSampleResult sampleResult, List<DtoLabGroup> labGroupList);


    @Autowired
    @Lazy
    public void setSampleResultRepository(SampleResultRepository sampleResultRepository) {
        this.sampleResultRepository = sampleResultRepository;
    }

    @Autowired
    @Lazy
    public void setTaskRepository(TaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }

    @Autowired
    @Lazy
    public void setOdSampleRepository(OdSampleRepository odSampleRepository) {
        this.odSampleRepository = odSampleRepository;
    }

    @Autowired
    @Lazy
    public void setLabGroupRepository(LabGroupRepository labGroupRepository) {
        this.labGroupRepository = labGroupRepository;
    }

    @Autowired
    @Lazy
    public void setLabSeqRepository(LabSeqRepository labSeqRepository) {
        this.labSeqRepository = labSeqRepository;
    }

    @Autowired
    @Lazy
    public void setOdPersonAnswerRepository(OdPersonAnswerRepository odPersonAnswerRepository) {
        this.odPersonAnswerRepository = odPersonAnswerRepository;
    }

    @Autowired
    @Lazy
    public void setStationarySourceResultRepository(StationarySourceResultRepository stationarySourceResultRepository) {
        this.stationarySourceResultRepository = stationarySourceResultRepository;
    }

    @Autowired
    @Lazy
    public void setStationarySourceResultDetailRepository(StationarySourceResultDetailRepository stationarySourceResultDetailRepository) {
        this.stationarySourceResultDetailRepository = stationarySourceResultDetailRepository;
    }

    @Autowired
    @Lazy
    public void setOdPersonRepository(OdPersonRepository odPersonRepository) {
        this.odPersonRepository = odPersonRepository;
    }

    @Autowired
    @Lazy
    public void setEnvGasResultRepository(EnvGasResultRepository envGasResultRepository) {
        this.envGasResultRepository = envGasResultRepository;
    }

    @Autowired
    @Lazy
    public void setOdPersonAnswerService(OdPersonAnswerService odPersonAnswerService) {
        this.odPersonAnswerService = odPersonAnswerService;
    }
}
