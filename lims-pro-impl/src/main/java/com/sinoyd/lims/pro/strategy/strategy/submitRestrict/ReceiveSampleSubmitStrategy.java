package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
/**
 * 现场送样提交
 * <AUTHOR>
 */
@Component(IFileNameConstant.SubmitStrategyKey.SUBMIT_RECEIVE_SAMPLE)
public class ReceiveSampleSubmitStrategy extends AbsSubmitRestrictStrategy {
    @Override
    public List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        String receiveId = objMap.toString();
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.样品登记.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.样品登记.getModuleName());
        if (findByReceiveId(receiveId).size() == 0) {
            restrictVo.setExceptionOption("未添加方案");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        restrictVoList.add(restrictVo);
        return restrictVoList;
    }
}
