package com.sinoyd.lims.pro.controller;

import java.util.List;

import com.sinoyd.base.criteria.ConsumableCriteria;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ELogType;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePickListsDetail;
import com.sinoyd.lims.lim.dto.lims.DtoOAContract;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.service.OAConsumablePickListsService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 领料申请服务接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
@Api(tags = "工作流: 领料申请服务")
@RestController
@RequestMapping("/api/pro/oaConsumableLogs")
public class OAConsumablePickListsController extends ExceptionHandlerController<OAConsumablePickListsService> {
    /**
     * 添加领料申请
     * 
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加领料申请", notes = "添加领料申请启动流程")
    @PostMapping
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> startProcess(@RequestBody DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        String procInstId = service.startProcess(taskDto);
        restResp.setData(procInstId);
        return restResp;
    }

    /**
     * 保存为草稿
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "保存为草稿", notes = "保存为草稿")
    @PostMapping("/saveAsDraft")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> saveAsDraft(@RequestBody DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.saveAsDraft(taskDto));
        return restResp;
    }

    /**
     * 草稿保存
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "草稿保存", notes = "草稿保存")
    @PostMapping("/draftSave")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> draftSave(@RequestBody DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSave(taskDto));
        return restResp;
    }

    /**
     * 草稿提交
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加审批", notes = "添加审批启动流程")
    @PostMapping("/draftSubmit")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> draftSubmit(@RequestBody DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSubmit(taskDto));
        return restResp;
    }

    /**
     * 查询领料信息
     * 
     * @param taskId 任务id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "查询领料信息", notes = "查询领料信息")
    @GetMapping(path = "/task/{taskId}")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<DtoOATaskDetail<List<DtoOAConsumablePickListsDetail>, List<DtoConsumable>>> findDetailByTaskId(@PathVariable(name = "taskId") String taskId) {
        RestResponse<DtoOATaskDetail<List<DtoOAConsumablePickListsDetail>, List<DtoConsumable>>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoOATaskDetail<List<DtoOAConsumablePickListsDetail>, List<DtoConsumable>> detail = service.findOATaskDetail(taskId);
        restResp.setData(detail);
        return restResp;
    }

    /**
     * 分页获取消耗品信息
     *
     * @param criteria 查询条件
     * @return 消耗品分页数据
     */
    @ApiOperation(value = "分页动态条件查询消耗品/标样", notes = "分页动态条件查询消耗品/标样")
    @GetMapping("/consumables")
    public RestResponse<List<DtoConsumable>> findByPage(ConsumableCriteria criteria) {
        RestResponse<List<DtoConsumable>> restResp = new RestResponse<>();
        PageBean<DtoConsumable> page = super.getPageBean();
        service.findConsumableByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }
}
