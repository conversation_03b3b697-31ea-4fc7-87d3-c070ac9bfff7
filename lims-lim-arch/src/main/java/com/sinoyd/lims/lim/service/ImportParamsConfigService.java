package com.sinoyd.lims.lim.service;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 原始记录单数据参数导入接口
 * <AUTHOR>
 * @version V1.0.0 2023/10/13
 * @since V100R001
 */
public interface ImportParamsConfigService {
    /**
     * 数据导入
     * @param file      文件
     * @param objectMap  附加参数容器
     * @param response   响应
     * @throws Exception 导入异常
     */
    void importExcel(MultipartFile file, Map<String, Object> objectMap,  HttpServletResponse response) throws Exception;
}
