package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.pro.dto.customer.DtoQCDataConfig;
import com.sinoyd.lims.pro.dto.customer.DtoQCDataDetail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.QCDataService;
import com.sinoyd.lims.pro.criteria.QCDataCriteria;
import com.sinoyd.lims.pro.dto.DtoQCData;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * QCData服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: QCData服务")
 @RestController
 @RequestMapping("api/pro/qcData")
 public class QCDataController extends BaseJpaController<DtoQCData, String,QCDataService> {

    @ApiOperation(value = "查询配置数据", notes = "查询配置数据")
    @GetMapping
    public RestResponse<DtoQCData> find(@RequestParam("testId") String testId,
                                        @RequestParam("userId") String userId,
                                        @RequestParam("type") Integer type,
                                        @RequestParam("paramsName") String paramsName) {
        RestResponse<DtoQCData> restResponse = new RestResponse<>();
        DtoQCData qCData = service.findConfigQCData(testId, userId, type, paramsName);
        restResponse.setData(qCData);
        restResponse.setRestStatus(StringUtil.isNull(qCData) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增QCData
     *
     * @param qcDataConfig 实体列表
     * @return RestResponse<DtoQCData>
     */
    @ApiOperation(value = "新增质控配置数据", notes = "新增质控配置数据")
    @PostMapping
    public RestResponse<String> create(@RequestBody DtoQCDataConfig qcDataConfig) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.saveConfigQCData(qcDataConfig);
        return restResponse;
    }
}