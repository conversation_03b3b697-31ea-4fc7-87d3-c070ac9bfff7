package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * SamplingArrange实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/20
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "SamplingArrange")
@Data
public class SamplingArrange implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public SamplingArrange() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();


    /**
     * 项目id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("项目id")
    private String projectId;

    /**
     * 点位id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("点位id")
    private String sampleFolderId;


    /**
     * 周期
     */
    @Column(nullable = false)
    @ApiModelProperty("周期")
    private Integer periodCount;

    /**
     * 周期
     */
    @Column(nullable = false)
    @ApiModelProperty("采样样品数")
    private Integer sampleCount;

    /**
     * 检测类型id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;


    /**
     * 计划采样时间
     */
    @Column(nullable = false)
    @ApiModelProperty("计划采样时间")
    private Date planSamplingTime;


    /**
     * 采样小组
     */
    @Column(length = 50)
    @ApiModelProperty("采样小组")
    @Length(message = "采样小组{validation.message.length}", max = 50)
    private String team;

    /**
     * 采样小组标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样小组标识")
    private String teamId;

    /**
     * 采样负责人
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("采样负责人")
    @Length(message = "采样负责人{validation.message.length}", max = 50)
    private String chargePerson;

    /**
     * 采样负责人标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样负责人标识")
    private String chargePersonId;

    /**
     * 采样人员
     */
    @Column()
    @ApiModelProperty("采样人员")
    @Length(message = "采样人员{validation.message.length}", max = 255)
    private String samplingPeople;

    /**
     * 采样人员标识 ; 分割
     */
    @Column(length = 1000)
    @ApiModelProperty("采样人员标识 ; 分割")
    @Length(message = "采样人员标识 ; 分割{validation.message.length}", max = 1000)
    private String samplingPeopleIds;

    /**
     * 采样车辆
     */
    @Column(length = 50)
    @ApiModelProperty("采样车辆")
    @Length(message = "采样车辆{validation.message.length}", max = 50)
    private String car;

    /**
     * 采样车辆标识
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样车辆标识")
    private String carId;

    /**
     * 空白样
     */
    @ApiModelProperty("空白样")
    private Boolean kbFlag;

    /**
     * 平行样
     */
    @ApiModelProperty("平行样")
    private Boolean pxFlag;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @Length(message = "状态{validation.message.length}", max = 50)
    private String status;

    /**
     * 采样计划id
     */
    @ApiModelProperty("采样计划id")
    private String samplingPlanId;

    /**
     * 计划安排状态
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("计划安排状态 是否安排")
    private Boolean isArrange;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;
}