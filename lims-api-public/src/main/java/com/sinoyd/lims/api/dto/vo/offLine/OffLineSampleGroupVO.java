package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：分组表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineSampleGroupVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 分组id
     */
    private String sampleTypeGroupId;

    /**
     * 分组名称
     */
    private String sampleTypeGroupName;

    /**
     * 分析项目名称
     */
    private String analyseItemNames;

    /**
     * 固定剂
     */
    private String fixer;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 所属机构
     */
    private String orgId;
}
