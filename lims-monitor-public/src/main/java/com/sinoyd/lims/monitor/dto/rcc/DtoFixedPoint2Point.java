package com.sinoyd.lims.monitor.dto.rcc;

import com.sinoyd.lims.monitor.entity.FixedPoint2Point;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


/**
 * DtoFixedPoint2Point实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_FixedPoint2Point")
@Data
@DynamicInsert
public class DtoFixedPoint2Point extends FixedPoint2Point {
    private static final long serialVersionUID = 1L;

    /**
     * 点位编号
     */
    @Transient
    private String pointCode;

    /**
     * 点位名称
     */
    @Transient
    private String pointName;

    /**
     * 所属测站
     */
    @Transient
    private String stationName;

    /**
     * 是否启用
     */
    @Transient
    private Boolean isEnabled;

    /**
     * 点位创建时间
     */
    @Transient
    private Date pointCreateDate;

    /**
     * 点位类型
     */
    @Transient
    private String folderType;

    /**
     * 点位类型名称
     */
    @Transient
    private String folderTypeName;

    /**
     * 水体名称
     */
    @Transient
    private String waterName;
}