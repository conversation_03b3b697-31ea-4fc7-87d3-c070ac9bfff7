package com.sinoyd.base.controller;

import com.sinoyd.base.dto.customer.DtoEvaluationValueCopy;
import com.sinoyd.base.dto.customer.DtoEvaluationValueTemp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.base.service.EvaluationValueService;
import com.sinoyd.base.criteria.EvaluationValueCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * EvaluationValue服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
 @Api(tags = "示例: EvaluationValue服务")
 @RestController
 @RequestMapping("api/base/evaluationValue")
 @Validated
 public class EvaluationValueController extends BaseJpaController<DtoEvaluationValue, String,EvaluationValueService> {


    /**
     * 分页动态条件查询EvaluationValue
     *
     * @param evaluationValueCriteria 条件参数
     * @return RestResponse<List < EvaluationValue>>
     */
    @ApiOperation(value = "分页动态条件查询EvaluationValue", notes = "分页动态条件查询EvaluationValue")
    @GetMapping
    public RestResponse<List<DtoEvaluationValue>> findByPage(EvaluationValueCriteria evaluationValueCriteria) {
        PageBean<DtoEvaluationValue> pageBean = super.getPageBean();
        RestResponse<List<DtoEvaluationValue>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, evaluationValueCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询EvaluationValue
     *
     * @param id 主键id
     * @return RestResponse<DtoEvaluationValue>
     */
    @ApiOperation(value = "按主键查询EvaluationValue", notes = "按主键查询EvaluationValue")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoEvaluationValue> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoEvaluationValue> restResponse = new RestResponse<>();
        DtoEvaluationValue evaluationValue = service.findOne(id);
        restResponse.setData(evaluationValue);
        restResponse.setRestStatus(StringUtil.isNull(evaluationValue) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增EvaluationValue
     *
     * @param dtoEvaluationValueTemp 实体列表
     * @return RestResponse<DtoEvaluationValue>
     */
    @ApiOperation(value = "新增EvaluationValue", notes = "新增EvaluationValue")
    @PostMapping
    public RestResponse<List<DtoEvaluationValue>> addAnalyzeItem(@Validated @RequestBody DtoEvaluationValueTemp dtoEvaluationValueTemp) {
        RestResponse<List<DtoEvaluationValue>> restResponse = new RestResponse<>();
        restResponse.setData(service.addAnalyzeItem(dtoEvaluationValueTemp));
        return restResponse;
    }

    /**
     * 新增EvaluationValue
     *
     * @param dtoEvaluationValueCopy 实体列表
     * @return RestResponse<DtoEvaluationValue>
     */
    @ApiOperation(value = "复制EvaluationValue", notes = "复制EvaluationValue")
    @PostMapping("/copy")
    public RestResponse<List<DtoEvaluationValue>> copyAnalyzeItem(@Validated @RequestBody DtoEvaluationValueCopy dtoEvaluationValueCopy) {
        RestResponse<List<DtoEvaluationValue>> restResponse = new RestResponse<>();
        restResponse.setData(service.copyAnalyzeItem(dtoEvaluationValueCopy));
        return restResponse;
    }

    /**
     * 修改EvaluationValue
     *
     * @param dtoEvaluationValueTemp 实体列表
     * @return RestResponse<DtoEvaluationValue>
     */
    @ApiOperation(value = "修改EvaluationValue", notes = "修改EvaluationValue")
    @PutMapping("/symbol")
    public RestResponse<Integer> updateSymbol(@Validated @RequestBody DtoEvaluationValueTemp dtoEvaluationValueTemp) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.updateSymbol(dtoEvaluationValueTemp));
        return restResponse;
    }

    /**
     * 修改EvaluationValue
     *
     * @param dtoEvaluationValue 实体列表
     * @return RestResponse<DtoEvaluationValue>
     */
    @ApiOperation(value = "修改EvaluationValue", notes = "修改EvaluationValue")
    @PutMapping
    public RestResponse<DtoEvaluationValue> update(@RequestBody DtoEvaluationValue dtoEvaluationValue) {
        RestResponse<DtoEvaluationValue> restResponse = new RestResponse<>();
        restResponse.setData(service.update(dtoEvaluationValue));
        return restResponse;
    }

    /**
     * "根据id批量删除EvaluationValue
     *
     * @param dtoEvaluationValueTemp 删除对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据相关的数据批量删除EvaluationValue", notes = "根据相关数据批量删除EvaluationValue")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody DtoEvaluationValueTemp dtoEvaluationValueTemp) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deleteEvaluationValue(dtoEvaluationValueTemp);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 批量修改评价标准量纲
     *
     * @param dtoEvaluationValueTemp 实体列表
     * @return RestResponse<DtoEvaluationValue>
     */
    @ApiOperation(value = "批量修改评价标准量纲", notes = "批量修改评价标准量纲")
    @PostMapping("/dimension")
    public RestResponse<Integer> batchUpdateDimension(@RequestBody DtoEvaluationValueTemp dtoEvaluationValueTemp) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.batchUpdateDimension(dtoEvaluationValueTemp));
        return restResponse;
    }
}