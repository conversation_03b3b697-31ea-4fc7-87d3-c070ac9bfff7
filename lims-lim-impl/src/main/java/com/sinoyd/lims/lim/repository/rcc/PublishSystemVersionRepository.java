package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoPublishSystemVersion;

/**
 * 分析项目关系参数仓储
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/11/9
 */
public interface PublishSystemVersionRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoPublishSystemVersion, String> {

    /**
     * 根据版本号以及主键查询
     *
     * @param versionNum 版本号
     * @param id         主键
     * @return 查询条数
     */
    Integer countByVersionNumAndIdNot(String versionNum, String id);

}