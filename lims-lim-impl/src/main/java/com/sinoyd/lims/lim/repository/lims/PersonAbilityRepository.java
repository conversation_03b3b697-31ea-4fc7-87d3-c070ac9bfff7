package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 人员检查能力仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
public interface PersonAbilityRepository extends IBaseJpaPhysicalDeleteRepository<DtoPersonAbility, String> {
    /**
     * 根据personId查询
     *
     * @param personId 人员personId
     * @param testId   测试testId
     * @return DtoPersonAbility
     */
    DtoPersonAbility findByPersonIdAndTestId(String personId, String testId);

    /**
     * 删除
     *
     * @param testId   测试testId
     * @param personId 人员personId
     * @return DtoPersonAbility
     */
    @Transactional
    @Modifying
    @Query("delete from DtoPersonAbility p where p.testId = :testId and  p.personId = :personId ")
    Integer deleteByTestIdAndPersonId(@Param("testId") String testId, @Param("personId") String personId);

    /**
     * 删除关联数据
     *
     * @param certIds 证书id集合
     * @return 返回删除数量
     */
    @Transactional
    @Modifying
    @Query("delete from DtoPersonAbility p where p.personCertId in :certIds ")
    Integer deleteByCertIds(@Param("certIds") List<String> certIds);

    /**
     * 根据测试项目ids 获取相关的检测能力
     *
     * @param testIds 测试项目ids
     * @return 返回相关的检测能力
     */
    List<DtoPersonAbility> findByTestIdIn(Collection<String> testIds);

    /**
     * 查询某个人员的所有检测能力
     *
     * @param personId 人员id
     * @return 返回相关检测能力集合
     */
    List<DtoPersonAbility> findByPersonId(String personId);

    /**
     * 根据人员id查询检测能力
     *
     * @param personIds 人员id
     * @return 检测能力
     */
    List<DtoPersonAbility> findByPersonIdIn(List<String> personIds);

    /**
     * 根据人员与对应证书查询所有检测能力
     *
     * @param personId 检测人员id
     * @param certId   证书id
     * @return 检测能力
     */
    List<DtoPersonAbility> findByPersonIdAndPersonCertId(String personId, String certId);

    /**
     * 根据检测能力ids 查询
     *
     * @param bigSampleTypeIds 检测能力ids
     * @return 能力集合
     */
    List<DtoPersonAbility> findBySampleTypeIdIn(List<String> bigSampleTypeIds);

    /**
     * 根据证书查询所有检测能力
     *
     * @param certIds   证书id
     * @return 检测能力
     */
    List<DtoPersonAbility> findByPersonCertIdIn(List<String> certIds);

}