package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.entity.QuotationDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;


/**
 * DtoQuotationDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_QuotationDetail") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoQuotationDetail extends QuotationDetail {
    private static final long serialVersionUID = 1L;

    @Transient
    private String[] folderList = new String[]{};

    @Transient
    private List<DtoTest> testList;

    @Transient
    private Integer folderCount;

    @Transient
    private String sampleTypeName;

    @Transient
    private List<String> detailIds = new ArrayList<>();

    @Transient
    private String projectId;

    @Transient
    private Boolean isChange = false;

    @Transient
    private String sampleTypeId;

    /**
     * 检测模板id
     */
    @Transient
    private String testTemplateId;

    /**
     * 导入订单明细-按点位导入时过滤用
     */
    @Transient
    private List<String> filterList = new ArrayList<>();

    /**
     * 标准编号
     */
    @Transient
    private String redCountryStandard;

    /**
     * 无参构造函数
     */
    public DtoQuotationDetail() {
        super();
    }

    /**
     * 用于企业点位导入订单详情时的构造函数
     *
     * @param orderId     订单id
     * @param quotationId 报价id
     * @param test        测试项目
     */
    public DtoQuotationDetail(String orderId, String quotationId, DtoTest test) {
        this();
        setAnalyseItemId(test.getAnalyzeItemId());
        setAnalyseMethodId(test.getAnalyzeMethodId());
        setRedAnalyseItemName(test.getRedAnalyzeItemName());
        setRedAnalyseMethod(test.getRedAnalyzeMethodName());
        setTestId(test.getId());
        setSampleTypeId(test.getSampleTypeId());
        setSamplingPrice(test.getSamplingCharge());
        setAnalysePrice(test.getTestingCharge());
        setIsTotal(test.getIsTotalTest());
        setOrderId(orderId);
        setQuotationId(quotationId);
        setResidueCount(0);
        setProjectCount(1);
        setInspectedCount(0);
    }
}