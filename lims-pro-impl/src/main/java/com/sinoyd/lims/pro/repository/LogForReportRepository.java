package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoLogForReport;

import java.util.List;


/**
 * LogForReport数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/26
 * @since V100R001
 */
public interface LogForReportRepository extends IBaseJpaPhysicalDeleteRepository<DtoLogForReport, String> {

    List<DtoLogForReport> findByObjectId(String objectId);
    List<DtoLogForReport> findByObjectIdIn(List<String> objectIds);
}