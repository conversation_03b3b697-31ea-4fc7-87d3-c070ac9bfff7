package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.dto.DtoWorkSheet;
import com.sinoyd.lims.pro.dto.DtoWorkSheetCalibrationCurve;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.dto.customer.DtoWorkSheetCalibrationCurveTemp;

import java.util.List;


/**
 * WorkSheetCalibrationCurve操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface WorkSheetCalibrationCurveService extends IBaseJpaService<DtoWorkSheetCalibrationCurve, String> {

    /**
     * 存储校准曲线数据
     *
     * @param workSheets 相关的testId 及 worksheetId
     * @return 返回创建的相应曲线数据
     */
    List<DtoWorkSheetCalibrationCurve> createWorkSheetCarveCheck(List<DtoWorkSheet> workSheets);

    /**
     * 按子检测单id及测试项目id获取校准曲线信息
     *
     * @param workSheetId 子检测单id
     * @param testId      测试项目id
     * @return 返回校准曲线信息
     */
    DtoWorkSheetCalibrationCurveTemp findByTestIdAndWorkSheetId(String workSheetId, String testId);

    /**
     * 保存校准曲线信息
     *
     * @param temp 校准曲线信息
     * @return 明细
     */
    DtoWorkSheetCalibrationCurveTemp saveWorkSheetCalibrationCurve(DtoWorkSheetCalibrationCurveTemp temp);

    /**
     * 按照标线id查询分析的检测单数据
     */
    void findWorkSheetFolderByPage(PageBean<DtoWorkSheetFolder> pb, BaseCriteria workSheetCurveCriteria);
}