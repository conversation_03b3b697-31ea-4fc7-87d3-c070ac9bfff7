package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoQCResultEvaluation;
import com.sinoyd.lims.pro.repository.QCResultEvaluationRepository;
import com.sinoyd.lims.pro.service.QCResultEvaluationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * QCResultEvaluation操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Service
public class QCResultEvaluationServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoQCResultEvaluation,String,QCResultEvaluationRepository> implements QCResultEvaluationService {

    @Override
    public void findByPage(PageBean<DtoQCResultEvaluation> pb, BaseCriteria qCResultEvaluationCriteria) {
        pb.setEntityName("DtoQCResultEvaluation a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, qCResultEvaluationCriteria);
    }

    @Transactional
    @Override
    public DtoQCResultEvaluation save(DtoQCResultEvaluation entity) {
        entity.setCreatePerson(PrincipalContextUser.getPrincipal().getUserId());
        entity.setCreateTime(new Date());
        return this.repository.save(entity);
    }

    @Transactional
    @Override
    public DtoQCResultEvaluation update(DtoQCResultEvaluation entity) {
        return comRepository.merge(entity);
    }

    @Override
    public List<DtoQCResultEvaluation> findByProjectIds(Collection<String> projectIds) {
        if (StringUtil.isNotEmpty(projectIds)){
            return repository.findByProjectIdIn(projectIds);
        }
        return new ArrayList<>();
    }
}