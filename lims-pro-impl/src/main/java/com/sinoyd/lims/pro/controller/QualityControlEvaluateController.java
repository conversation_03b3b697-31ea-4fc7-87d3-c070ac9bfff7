package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.criteria.QualityControlEvaluateCriteria;
import com.sinoyd.lims.pro.dto.DtoQualityControlEvaluate;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluateDetail;
import com.sinoyd.lims.pro.dto.customer.DtoQcSampleEvaluateDetail;
import com.sinoyd.lims.pro.service.QualityControlEvaluateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * QualityControlEvaluate服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/11/10
 * @since V100R001
 */
 @Api(tags = "示例: QualityControlEvaluate服务")
 @RestController
 @RequestMapping("api/pro/qualityControlEvaluate")
 public class QualityControlEvaluateController extends BaseJpaController<DtoQualityControlEvaluate, String, QualityControlEvaluateService> {

    @ApiOperation(value = "QualityControlEvaluate", notes = "分页动态条件查询AnalyseData")
    @GetMapping
    public RestResponse<List<DtoQualityControlEvaluate>> findByPage(QualityControlEvaluateCriteria qualityControlEvaluateCriteria) {
        PageBean<DtoQualityControlEvaluate> pageBean = super.getPageBean();
        RestResponse<List<DtoQualityControlEvaluate>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, qualityControlEvaluateCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 更新质控评价
     *ReportTestCertMark
     * @param map 检测单id
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "更新质控评价", notes = "更新质控评价")
    @PutMapping(path = "/update")
    public RestResponse<Boolean> find(@RequestBody Map<String, String> map) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        String workSheetFolderId = map.getOrDefault("workSheetFolderId", UUIDHelper.GUID_EMPTY);
        service.updateEvaluate(workSheetFolderId);
        restResponse.setData(true);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "查询质控评价详情", notes = "查询质控评价详情")
    @GetMapping(path = "/detail")
    public RestResponse<DtoEvaluateDetail> findByPage(String evaluateId) {
        RestResponse<DtoEvaluateDetail> restResponse = new RestResponse<>();
        DtoEvaluateDetail evaluateDetail = service.findEvaluateDetail(evaluateId);
        restResponse.setData(evaluateDetail);
        restResponse.setRestStatus(StringUtil.isNull(evaluateDetail) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "查询质控样评价明细", notes = "查询质控样评价明细")
    @GetMapping(path = "/qcSampleDetail")
    public RestResponse<List<DtoQcSampleEvaluateDetail>> qcSampleDetail(DtoQcSampleEvaluateDetail detail) {
        RestResponse<List<DtoQcSampleEvaluateDetail>> restResponse = new RestResponse<>();
        PageBean<DtoQcSampleEvaluateDetail> pageBean = super.getPageBean();
        List<DtoQcSampleEvaluateDetail> details = service.findEvaluateDetailByPage(pageBean, detail);
        restResponse.setData(details);
        restResponse.setCount(pageBean.getRowsCount());
        restResponse.setRestStatus(StringUtil.isNull(details) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 手动修正允许限值并重新评价
     *
     * @param qualityControlEvaluate 质控评价
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "手动修正允许限值并重新评价", notes = "手动修正允许限值并重新评价")
    @PutMapping(path = "/handleEvaluate")
    public RestResponse<Void> find(@RequestBody DtoQualityControlEvaluate qualityControlEvaluate) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.handleEvaluate(qualityControlEvaluate);
        return restResponse;
    }

    /**
     * 重置为系统评价
     *
     * @param worksheetFolderId 检测单标识
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "重置为系统评价", notes = "重置为系统评价")
    @PostMapping(path = "/systemEvaluate/{worksheetFolderId}")
    public RestResponse<Void> systemEvaluate(@PathVariable String worksheetFolderId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.systemEvaluate(worksheetFolderId);
        return restResponse;
    }
}

