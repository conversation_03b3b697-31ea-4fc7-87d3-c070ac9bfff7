DROP TABLE IF EXISTS TB_PRO_LocalTaskPeopleCompare;
CREATE TABLE TB_PRO_LocalTaskPeopleCompare
(
    id             varchar(50) NOT NULL,
    leaderId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    checkPeopleId  varchar(500)  NOT NULL DEFAULT '',
    checkDate      datetime DEFAULT '1753-01-01 00:00:00' NOT NULL,
    receiveId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    projectId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
);
ALTER TABLE TB_PRO_LocalTaskPeopleCompare ADD CONSTRAINT PRIMARY KEY (id);
COMMENT ON TABLE TB_PRO_LocalTaskPeopleCompare IS '现场质控任务人员比对配置表';
COMMENT ON COLUMN TB_PRO_LocalTaskPeopleCompare.id IS 'id';
COMMENT ON COLUMN TB_PRO_LocalTaskPeopleCompare.leaderId IS '考核负责人标识';
COMMENT ON COLUMN TB_PRO_LocalTaskPeopleCompare.checkPeopleId IS '考核人员标识,多个英文逗号拼接';
COMMENT ON COLUMN TB_PRO_LocalTaskPeopleCompare.checkDate IS '考核日期';
COMMENT ON COLUMN TB_PRO_LocalTaskPeopleCompare.receiveId IS '送样单标识';
COMMENT ON COLUMN TB_PRO_LocalTaskPeopleCompare.projectId IS '项目标识';