package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.dto.lims.DtoOAFileRevision;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件修订业务操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-08
 * @since V100R001
 */
public interface OAProcFileRevisionService {
    /**
     * 添加审批并启动流程
     * 
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<DtoOAFileRevision> taskDto);

    /**
     * 查询任务详细信息
     * 
     * @param taskId 任务ID
     * @return
     */
    DtoOATaskDetail<DtoOAFileRevision, DtoFileControlApplyDetail> findOATaskDetail(String taskId);
    
    /**
     * 正常结束任务通知
     * @param oaTask 任务对象
     */
    void normalCloseTaskNotify(DtoOATask oaTask);


    /**
     * 文件上传
     * @param procInstId 工作流id
     * @param files 文件集合
     * @param ids 受控文件明细id
     * @return
     */
    void uploadFilesControl(String procInstId, List<String> ids , List<MultipartFile> files);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask saveAsDraft(DtoOATaskCreate<DtoOAFileRevision> taskDto);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask draftSave(DtoOATaskCreate<DtoOAFileRevision> taskDto);

    /**
     * 草稿提交
     * @param taskDto  表单数据
     * @return         oa任务
     */
    String draftSubmit(DtoOATaskCreate<DtoOAFileRevision> taskDto);
}
