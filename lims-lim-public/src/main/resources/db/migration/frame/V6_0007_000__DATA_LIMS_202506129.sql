-- 小于检出限计算参数配置调整
update t_sys_config set configValue = '{"qualityControl":"0","sample":"0", "report":"0"}', note = '小于检出限计算，一般应用于两类：带入偏差计算（qualityControl）、带入浓度均值计算（sample）。这两个需要分别配置。
①带入偏差计算（qualityControl）：一般应用于“曲线校核样、校正系数检验”偏差计算，小于检出限带入的结果。一般默认取“0”;
②带入浓度均值计算（sample）：一般用于平行样的出证结果、报告中点位均值、批次均值的时候，小于检出限带入的结果。一般默认取“检出限一半”。
③带入浓度求和计算（report）：用于电子报告中总称测试项目及补充总量浓度求和的时候，小于检出限带入的结果。一般默认取“检出限一半”。' where configKey = 'sys_pro_unlessexamlimit_type';