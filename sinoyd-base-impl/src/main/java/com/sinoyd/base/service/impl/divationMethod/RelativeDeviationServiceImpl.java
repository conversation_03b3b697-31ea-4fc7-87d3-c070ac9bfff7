package com.sinoyd.base.service.impl.divationMethod;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.configuration.QcRulesConfig;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.quality.CurveCheck;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.CalculationService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sinoyd.base.utils.base.DivationUtils.standardDeviationBigDecimal;

/**
 * 相对偏差
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
@Slf4j
public class RelativeDeviationServiceImpl implements QualityDivationService {


    @Override
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        String retStr = "";
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        if (valueList.size() > 2) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcValue = valueList.get(1);
            //均值结果
            String avgValue = valueList.get(2);
            if (DivationUtils.isNumber(samValue) && DivationUtils.isNumber(qcValue) && DivationUtils.isNumber(avgValue)) {
                //判断数据偏差是否使用绝对偏差 -- 检查项范围
                String qcRangeLimit = controlLimit.getRangeConfigData();//controlLimit.getRangeConfig();
                Boolean flag = true;
                if (EnumBase.EnumIsCheckItem.是.getValue().equals(controlLimit.getIsCheckItem())) {
                    if (StringUtil.isNotEmpty(qcRangeLimit) && !new CurveCheck().qcTypeValue().equals(controlLimit.getQcType())) {
                        flag = DivationUtils.calculationResult(qcRangeLimit, new BigDecimal(avgValue), calculationService);
                    } else if (StringUtil.isNotEmpty(qcRangeLimit) && new CurveCheck().qcTypeValue().equals(controlLimit.getQcType())) {
                        //曲线校核用加入量做判断条件
                        flag = DivationUtils.calculationResult(qcRangeLimit, new BigDecimal(qcValue), calculationService);
                    }
                }
                if (flag) {
                    if (valueList.size() > 3) {
                        List<String> yyPxValList = valueList.stream().skip(3).limit(valueList.size()).collect(Collectors.toList());
                        //多个平行样的情况，相对偏差按照标准差的方式进行计算
                        //获取标准差修约规则
                        int[] rules = DivationUtils.getStdDevRoundingRuleByXml(SpringContextAware.getBean(QcRulesConfig.class));
                        retStr = multiRelativeDeviation(yyPxValList, avgValue, rules[0], rules[1]);
                    } else {
                        //根据质控类型，质控等级获取配置的偏差公式
                        String deviationFormula = map.getOrDefault("deviationFormula", "").toString();
                        if (StringUtil.isNotEmpty(deviationFormula)) {
                            Map<String, Object> paramMap = new HashMap<>();
                            BigDecimal a = new BigDecimal(samValue);
                            paramMap.put("a", a);
                            paramMap.put("b", new BigDecimal(qcValue));
                            BigDecimal c = new BigDecimal(samValue).subtract(new BigDecimal(qcValue)).abs();
                            //判断分母是否为0，如果分母为0则无法计算
                            if (c.compareTo(BigDecimal.ZERO) != 0) {
                                if (deviationFormula.contains("a+b")) {
                                    BigDecimal sumValue = new BigDecimal(samValue).add(new BigDecimal(qcValue));
                                    if (sumValue.compareTo(BigDecimal.ZERO) == 0) {
                                        retStr = "无法计算，除数为0";
                                    }
                                } else {
                                    if (a.compareTo(BigDecimal.ZERO) == 0) {
                                        retStr = "无法计算，除数为0";
                                    }
                                }
                                if (!retStr.contains("无法计算")) {
                                    log.info("...... 开始计算: 公式 - {}, 参数 - {} ......", deviationFormula, paramMap);
                                    retStr = calculationService.calculationExpression(deviationFormula, paramMap).toString();
                                }
                            } else {
                                retStr = BigDecimal.ZERO.toString();
                            }

                            if (DivationUtils.isNumber(retStr)) {
                                retStr = new BigDecimal(retStr).multiply(new BigDecimal("100")).round(new MathContext(10, RoundingMode.HALF_EVEN)).toString();
                            }
                        } else {
                            BigDecimal subtract = new BigDecimal(samValue).subtract(new BigDecimal(qcValue)).abs();
                            //公式为 (|x-y|)/(x+y)*100
                            BigDecimal sumValue = new BigDecimal(samValue).add(new BigDecimal(qcValue));
                            //两数和与两数差 都不等于 0 不然结果都是0
                            if (new BigDecimal(samValue).compareTo(BigDecimal.ZERO) != 0 && subtract.compareTo(BigDecimal.ZERO) != 0) {
                                //判断分母是否为0，为0则无法计算
                                if (sumValue.compareTo(BigDecimal.ZERO) != 0) {
                                    retStr = (subtract.divide(sumValue, 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100))).toString();
//                                    //两数和与两数差 都不等于 0 不然结果都是0
//                                    if (new BigDecimal(samValue).compareTo(BigDecimal.ZERO) != 0 && subtract.compareTo(BigDecimal.ZERO) != 0) {
//                                        retStr = (subtract.divide(sumValue, 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100))).toString();
//                                    }else{
//                                        retStr = BigDecimal.ZERO.toString();
//                                    }
                                } else {
                                    retStr = "无法计算，除数为0";
                                }
                            } else {
                                retStr = BigDecimal.ZERO.toString();
                            }
                        }
                    }
                }
            }
        }
        map.put("qcRate", retStr);
    }

    /**
     * 多个平行样时，计算相对偏差(标准差S/平均值*100%)
     *
     * @param strValues    数据列表
     * @param avg          平均值
     * @param deviationSig 标准差有效位
     * @param deviationDec 标准差小数位
     * @return 相对偏差
     */
    private String multiRelativeDeviation(List<String> strValues, String avg, int deviationSig, int deviationDec) {
        CalculateService calculateService = SpringContextAware.getBean(CalculateService.class);
        BigDecimal avgDec = new BigDecimal(avg);
        //计算标准差
        BigDecimal s = standardDeviationBigDecimal(strValues, avg);
        //标准差s需要修约，修约方式为：保留三位有效，两位小数。（修约规则常量维护）
        return calculateService.revise(deviationSig, deviationDec, s.toPlainString());
        //标准差 / 平均值 * 100% BUG2024081299657 到标准差结束
//        return avgDec.compareTo(BigDecimal.ZERO) != 0 ? s.divide(avgDec, 10, BigDecimal.ROUND_HALF_EVEN).multiply(new BigDecimal(100)).toPlainString()
//                : BigDecimal.ZERO.toString();
    }
}
