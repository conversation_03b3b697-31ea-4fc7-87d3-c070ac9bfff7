package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.EntSupplierGoodsEvaluationCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoEntSupplierGoodsEvaluation;
import com.sinoyd.lims.lim.service.EntSupplierGoodsEvaluationService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商管理-评价信息-商品评价 
 * <AUTHOR> 修改：xuxb
 * @version V1.0.0 2019/3/8
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/entSupplierGoodsEvaluation")
@Validated
public class EntSupplierGoodsEvaluationController extends BaseJpaController<DtoEntSupplierGoodsEvaluation,String, EntSupplierGoodsEvaluationService> {

    /**
     * 根据id获取商品评价
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取商品评价", notes = "根据id获取商品评价")
    @GetMapping("/{id}")
    public RestResponse<DtoEntSupplierGoodsEvaluation> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoEntSupplierGoodsEvaluation> restResp = new RestResponse<>();
        DtoEntSupplierGoodsEvaluation entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询商品评价信息
     * 
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询商品评价信息", notes = "分页动态条件查询商品评价信息")
    @GetMapping
    public RestResponse<List<DtoEntSupplierGoodsEvaluation>> findByPage(EntSupplierGoodsEvaluationCriteria criteria) {

        RestResponse<List<DtoEntSupplierGoodsEvaluation>> restResp = new RestResponse<>();

        PageBean<DtoEntSupplierGoodsEvaluation> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增商品评价信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增商品评价信息", notes = "新增商品评价信息")
    @PostMapping
    public RestResponse<DtoEntSupplierGoodsEvaluation> save(@Validated @RequestBody DtoEntSupplierGoodsEvaluation entity) {

        RestResponse<DtoEntSupplierGoodsEvaluation> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoEntSupplierGoodsEvaluation data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改商品评价信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改商品评价信息", notes = "修改商品评价信息")
    @PutMapping
    public RestResponse<DtoEntSupplierGoodsEvaluation> update(@Validated @RequestBody DtoEntSupplierGoodsEvaluation entity) {

        RestResponse<DtoEntSupplierGoodsEvaluation> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoEntSupplierGoodsEvaluation data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 刪除商品评价信息
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "刪除商品评价信息", notes = "刪除商品评价信息")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除商品评价信息
     * 
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除商品评价信息", notes = "批量删除商品评价信息")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}