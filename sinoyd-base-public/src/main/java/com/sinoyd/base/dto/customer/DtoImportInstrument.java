package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class DtoImportInstrument implements IExcelDataModel, IExcelModel {
    /**
     * 行号
     */
    private Integer rowNum;

    private String errorMsg;

    /**
     * 本站编号
     */
    @Excel(name = "本站编号(必填)", orderNum = "20", width = 25)
    private String instrumentsCode;

    /**
     * 出厂编号
     */
    @Excel(name = "出厂编号(必填)", orderNum = "30", width = 25)
    private String serialNo;

    /**
     * 固定资产号
     */
    @Excel(name = "固定资产号", orderNum = "40", width = 17)
    private String fixedAssetsCode;

    /**
     * 设备名称
     */
    @Excel(name = "仪器名称(必填)", orderNum = "50", width = 25)
    private String instrumentName;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号(必填)", orderNum = "60", width = 22)
    private String model;

    /**
     * 仪器类型（常量：BASE_InstrumentType）
     */
    @Excel(name = "仪器类型(必填)", orderNum = "70", width = 22)
    private String instrumentTypeId;

    /**
     * 量程
     */
    @Excel(name = "量程", orderNum = "80", width = 15)
    private String insRange;

    /**
     * 准确度等级
     */
    @Excel(name = "准确度等级", orderNum = "90", width = 15)
    private String nicetyRate;

    /**
     * 购置日期
     */
    @Excel(name = "购置日期", format = "yyyy-MM-dd", orderNum = "100", width = 25)
    private String purchaseDate;

    /**
     * 仪器价格
     */
    @Excel(name = "仪器价格", orderNum = "110", width = 17)
    private String price;

    /**
     * 制造厂商名称
     */
    @Excel(name = "生产商", orderNum = "120", width = 13)
    private String factoryName;

    /**
     * 供应商
     */
    @Excel(name = "供应商", orderNum = "130", width = 13)
    private String saleName;

    /**
     * 状态(枚举：EnumInstrumentStatus：0报废、1正常、2停用、3过期)
     */
    @Excel(name = "状态(必填)", replace = {"报废_0", "正常_1", "停用_2", "过期_3", "_null"}, orderNum = "140", width = 17)
    private String state;

    /**
     * 所属科室id（Guid）
     */
    private String belongDeptId;

    /**
     * 所属科室名称（冗余）
     */
    @Excel(name = "所属部门(必填)", orderNum = "150", width = 25)
    private String deptName;

    /**
     * 管理员名称（冗余）
     */
    @Excel(name = "管理人员(必填)", orderNum = "160", width = 20)
    private String managerName;

    /**
     * 所在地
     */
    @Excel(name = "所在地", orderNum = "170", width = 13)
    private String place;

    /**
     * 检定技术指标
     */
    @Excel(name = "检定技术指标", orderNum = "173", width = 20)
    private String technicalSpecification;

    /**
     * 确认依据
     */
    @Excel(name = "确认依据", orderNum = "176", width = 20)
    private String confirmationBasis;

    /**
     * 最近日期（溯源）
     */
    @Excel(name = "溯源日期", format = "yyyy-MM-dd", orderNum = "180", width = 22)
    private String originDate;

    /**
     * 溯源周期(月)
     */
    @Excel(name = "溯源周期(月)", orderNum = "190", width = 20)
    private String originCyc;

    /**
     * 溯源方式(枚举：EnumOriginType：1检定、2校准、3自校)
     */
    @Excel(name = "计量类型", replace = {"检定_1", "校准_2", "自校_3", "_null"}, orderNum = "200", width = 15)
    private String originType;

    /**
     * 溯源单位
     */
    @Excel(name = "溯源单位", orderNum = "210", width = 12)
    private String originUnit;

    /**
     * 溯源单位
     */
    @Excel(name = "溯源结果", replace = {"合格_1", "不合格_0"}, orderNum = "220", width = 12)
    private String originResult;

    /**
     * 溯源备注
     */
    @Excel(name = "溯源备注", orderNum = "230", width = 12)
    private String originRemark;

    /**
     * 最近日期（核查）
     */
    @Excel(name = "期间检查日期", format = "yyyy-MM-dd", orderNum = "240", width = 17)
    private String inspectDate;

    /**
     * 核查周期(月)
     */
    @Excel(name = "核查周期(月)", orderNum = "250", width = 20)
    private String inspectPeriod;

    /**
     * 核查方法
     */
    @Excel(name = "核查方法", orderNum = "260", width = 17)
    private String inspectMethod;


    /**
     * 核查结果
     */
    @Excel(name = "核查结果", replace = {"合格_1", "不合格_0"}, orderNum = "270", width = 12)
    private String inspectResult;

    /**
     * 最近日期（维护）
     */
    @Excel(name = "维护日期", format = "yyyy-MM-dd", orderNum = "280", width = 20)
    private String maintenanceDate;

    /**
     * 维护周期(周)
     */
    @Excel(name = "维护周期(周)", orderNum = "290", width = 17)
    private String maintenanceCyc;

    /**
     * 维护内容
     */
    @Excel(name = "维护内容", orderNum = "300", width = 22)
    private String maintenanceContent;

    /**
     * 控制措施
     */
    @Excel(name = "控制措施", orderNum = "310", width = 22)
    private String controlMeasures;

    /**
     * 使用条件
     */
    @Excel(name = "使用条件", orderNum = "320", width = 22)
    private String useConditions;

    /**
     * 使用措施
     */
    @Excel(name = "使用措施", orderNum = "330", width = 22)
    private String useMethod;

    /**
     * 最近开启日期
     */
    @Excel(name = "启用日期", format = "yyyy-MM-dd", orderNum = "340", width = 20)
    private String recentOpenDate;

    /**
     * 备注
     */
    @Excel(name = "备注", orderNum = "350", width = 22)
    private String remark;


    /**
     * 校验错误信息
     */
    private Map<String, List<String>> failStr;

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }
}
