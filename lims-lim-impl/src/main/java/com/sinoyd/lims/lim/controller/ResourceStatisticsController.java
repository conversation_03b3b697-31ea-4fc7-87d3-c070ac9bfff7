package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.customer.DtoResourceStatistics;
import com.sinoyd.lims.lim.service.statistics.IResourceStatisticsContext;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资源统计用户访问接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/22
 */
@RestController
@RequestMapping("/api/lim/statistics")
public class ResourceStatisticsController extends ExceptionHandlerController<IResourceStatisticsContext> {

    /**
     * 首页资源提醒数字统计
     *
     * @return 结果
     */
    @ApiOperation(value = "首页资源提醒数字统计", notes = "首页资源提醒数字统计")
    @GetMapping("/homepage/resource")
    public RestResponse<List<DtoResourceStatistics>> homepageResourceStatistics() {
        RestResponse<List<DtoResourceStatistics>> restResp = new RestResponse<>();
        restResp.setData(service.statistics());
        return restResp;
    }


    /**
     * 获取耗材过期详情
     * @return 消耗品列表
     */
    @ApiOperation(value = "耗材过期详情", notes = "耗材过期详情")
    @GetMapping("/consumable/overDue")
    public RestResponse<List<DtoConsumableDetail>> getOverDueData() {
        RestResponse<List<DtoConsumableDetail>> restResp = new RestResponse<>();
        List<DtoConsumableDetail> list = service.getOverDueData();
        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(list);
        restResp.setCount(list.size());
        return restResp;
    }

    /**
     * 获取耗材低库存详情
     * @return 消耗品列表
     */
    @ApiOperation(value = "获取耗材低库存详情", notes = "获取耗材低库存详情")
    @GetMapping("/consumable/lowInventory")
    public RestResponse<List<DtoConsumable>> getLowInventoryData() {
        RestResponse<List<DtoConsumable>> restResp = new RestResponse<>();
        List<DtoConsumable> list = service.getLowInventoryData();
        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(list);
        restResp.setCount(list.size());
        return restResp;
    }
}