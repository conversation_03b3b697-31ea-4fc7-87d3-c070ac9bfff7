package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.List;

/**
 * 留样处置传输类
 *
 * <AUTHOR>
 * @version ：v1.0.0
 * @date ：2021/10/26
 */
@Data
public class DtoSampleDisposeTemp {

    /**
     * 留样处置操作人id
     */
    private String disposePersonId;

    /**
     * 留样处置备注
     */
    private String disposeRemark;

    /**
     * 处置方式
     */
    private String disposeType;

    /**
     * 处置日期
     */
    private String disposeDate;

    /**
     * 需要进行处置的留样id
     */
    private List<String> disposeIds;

}
