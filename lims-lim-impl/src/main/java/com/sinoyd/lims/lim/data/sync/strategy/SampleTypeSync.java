package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 检测类型同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/21
 */
@Component
@DependsOn({"springContextAware"})
@Order(4)
@Slf4j
public class SampleTypeSync extends AbsDataSync<DtoSampleType> {

    private SampleTypeService sampleTypeService;

    private SampleTypeRepository sampleTypeRepository;

    /**
     * 数据比较
     *
     * @param testIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoSampleType>> compareData(List<String> testIds) {
        //获取项目上全部数据
        List<DtoSampleType> projectDataList = sampleTypeService.findAll();
        //公共库中的数据
        List<DtoSampleType> standardDataList = queryStandardData();
        //比较数据
        return compareData(standardDataList, projectDataList);
    }

    /**
     * 同步数据
     *
     * @param testIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> testIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoSampleType>> compareResult = compareData(testIds);
        Optional<DtoDataCompareResult<DtoSampleType>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoSampleType errorDto = null;
            try {
                for (DtoSampleType dtoSampleType : r.getAddDataList()) {
                    errorDto = dtoSampleType;
                    if (sampleTypeRepository.findOne(dtoSampleType.getId()) != null) {
                        sampleTypeService.update(dtoSampleType);
                    } else {
                        sampleTypeService.save(dtoSampleType);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.检测类型.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.检测类型.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/base/sampleType";
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.检测类型.getValue();
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }
}