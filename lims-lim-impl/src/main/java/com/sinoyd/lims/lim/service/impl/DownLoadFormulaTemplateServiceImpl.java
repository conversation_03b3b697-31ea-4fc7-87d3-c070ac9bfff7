package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.customer.DtoImportTestFormula;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaForUpdate;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaParams;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaRevise;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.DownLoadFormulaTemplateService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.vo.CascadingDropdownVO;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公式导出接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/7
 * @since V100R001
 */
@Service
public class DownLoadFormulaTemplateServiceImpl implements DownLoadFormulaTemplateService {

    private ImportUtils importUtils;
    private SampleTypeRepository sampleTypeRepository;
    private DimensionRepository dimensionRepository;


    @Override
    public void downLoadTemplate(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        List<DtoImportTestFormula> formulaList = getNullIns(new DtoImportTestFormula());

        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportTestFormula.class, formulaList);
        // 设置下拉框
        this.processDropList(workBook, 0, 3, 9, 0);
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    public void downLoadTemplateUpdate(HttpServletResponse response) {
        List<DtoImportTestFormulaForUpdate> importTestFormulaForUpdates = getNullIns(new DtoImportTestFormulaForUpdate());
        List<DtoImportTestFormulaParams> importTestFormulaParams = getNullIns(new DtoImportTestFormulaParams());
        List<DtoImportTestFormulaRevise> importTestFormulaRevises = getNullIns(new DtoImportTestFormulaRevise());
        Map<String, List<?>> sheetDataMap = new LinkedHashMap<>();
        Map<String, Class<?>> sheetClassMap = new HashMap<>();
        sheetDataMap.put("公式配置", importTestFormulaForUpdates);
        sheetDataMap.put("参数扩展配置", importTestFormulaParams);
        sheetDataMap.put("修约公式", importTestFormulaRevises);
        sheetClassMap.put("公式配置", DtoImportTestFormulaForUpdate.class);
        sheetClassMap.put("参数扩展配置", DtoImportTestFormulaParams.class);
        sheetClassMap.put("修约公式", DtoImportTestFormulaRevise.class);
        Workbook workBook = importUtils.getWorkBook(sheetDataMap, sheetClassMap);
        this.processDropList(workBook, 1, 4, 11, 0);
        this.processDropList(workBook, 1, 4, 0, 1);
        this.processDropList(workBook, 1, 4, 0, 2);
        // 量纲
        List<DtoDimension> dimensionList = dimensionRepository.findAll();
        String[] dimensionModes = dimensionList.stream().map(DtoDimension::getDimensionName).toArray(String[]::new);
        importUtils.selectListMoreThan255(workBook, "量纲", 4, 1, 9, 9, dimensionModes);
        PoiExcelUtils.downLoadExcel("【导入模板】公式修改", response, workBook);
    }

    @Override
    public void processDropList(Workbook workBook, int col1, int col2, int col3, int sheetIndex) {
        // 获取所有检测类型
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll();
        sampleTypes = sampleTypes.stream().filter(p -> EnumBase.EnumSampleTypeCategory.检测类型大类.getValue().equals(p.getCategory()) ||
                EnumBase.EnumSampleTypeCategory.检测类型小类.getValue().equals(p.getCategory())).collect(Collectors.toList());
        List<DtoSampleType> bigSampleType = sampleTypes.stream().filter(p -> EnumBase.EnumSampleTypeCategory.检测类型大类.getValue().equals(p.getCategory()))
                .sorted(Comparator.comparing(DtoSampleType::getOrderNum).reversed()).collect(Collectors.toList());


        // 设置下拉框信息
        String[] calculationModes = Arrays.stream(EnumLIM.EnumCalculationMode.values()).map(Enum::name).toArray(String[]::new);
        if (col3 != 0) {
            importUtils.selectList(workBook, col3, col3, calculationModes, sheetIndex);
        }
        CascadingDropdownVO orgRoleCascadingDropdownVO = new CascadingDropdownVO();
        List<Integer> sampleTypeDropdownColumnIdxList = new ArrayList<Integer>() {{
            add(col1);
            add(col2);
        }};
        LinkedHashMap<String, List<String>> sampleTypeDropdownDataMap = new LinkedHashMap<>();
        Map<String, String> bigMap = bigSampleType.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));
        sampleTypeDropdownDataMap.put("检测类型", bigSampleType.stream().map(DtoSampleType::getTypeName).collect(Collectors.toList()));
        for (DtoSampleType dtoSampleType : bigSampleType) {
            List<String> sonList = sampleTypes.stream().filter(p -> dtoSampleType.getId().equals(p.getParentId()))
                    .sorted(Comparator.comparing(DtoSampleType::getOrderNum).reversed())
                    .map(DtoSampleType::getTypeName).collect(Collectors.toList());
            sonList.add(0,bigMap.get(dtoSampleType.getId()));
            sampleTypeDropdownDataMap.put(bigMap.get(dtoSampleType.getId()), sonList);
        }
        orgRoleCascadingDropdownVO.setDropdownDataList(sampleTypeDropdownDataMap)
                .setColumnIdxList(sampleTypeDropdownColumnIdxList)
                .setStartRowIdx(2)
                .setDropdownDataSheetName("检测类型（公式）数据源");
        importUtils.createCascadeDropDownBox(workBook.getSheetAt(sheetIndex), orgRoleCascadingDropdownVO);

    }

    /**
     * 获取按照大类小类排序
     *
     * @param sampleTypes   类型数据
     * @param bigSampleType 大类数据
     * @return 排序后的数据
     */
    private List<DtoSampleType> getShortSampleType(List<DtoSampleType> sampleTypes, List<DtoSampleType> bigSampleType) {
        List<DtoSampleType> shortSampleType = new ArrayList<>();
        for (DtoSampleType dtoSampleType : bigSampleType) {
            shortSampleType.add(dtoSampleType);
            // 递归获取所有子类，并按照排序值进行排序
            processSortChild(sampleTypes, dtoSampleType.getId(), shortSampleType);
        }
        return shortSampleType;
    }

    /**
     * 处理子类型排序
     *
     * @param sampleTypes 检测类型数据
     * @param parentId    父级id
     * @param parentId    排序的值
     */
    private void processSortChild(List<DtoSampleType> sampleTypes, String parentId, List<DtoSampleType> shortSampleType) {
        List<DtoSampleType> childSamples = sampleTypes.stream()
                .filter(p -> p.getParentId().equals(parentId))
                .sorted(Comparator.comparingInt(DtoSampleType::getOrderNum))
                .collect(Collectors.toList());
        for (DtoSampleType childSample : childSamples) {
            shortSampleType.add(childSample); // 添加子类
        }
    }

    /**
     * 获取空仪器检定数据
     *
     * @return 空list
     */
    private <T> List<T> getNullIns(T t) {
        // 获取仪器空数据
        List<T> formulas = new ArrayList<>();
        formulas.add(t);
        return formulas;
    }


    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }
}
