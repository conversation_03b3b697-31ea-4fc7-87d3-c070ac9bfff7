package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.QCResultEvaluation;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoQCResultEvaluation实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_QCResultEvaluation")
 @Data
 @DynamicInsert
 public  class DtoQCResultEvaluation extends QCResultEvaluation {
   private static final long serialVersionUID = 1L;
 }