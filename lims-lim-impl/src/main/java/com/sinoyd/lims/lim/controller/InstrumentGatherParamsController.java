package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentGatherParamsCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherParams;
import com.sinoyd.lims.lim.service.InstrumentGatherParamsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 仪器接入参数接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Api(tags = "示例: 仪器接入参数接口定义")
@RestController
@RequestMapping("api/lim/instrumentGatherParams")
@Validated
public class InstrumentGatherParamsController extends BaseJpaController<DtoInstrumentGatherParams, String, InstrumentGatherParamsService> {

    /**
     * 分页动态条件查询InstrumentGatherParams
     *
     * @param instrumentGatherParamsCriteria 条件
     * @return RestResponse<List < InstrumentGatherParams>>
     */
    @ApiOperation(value = "分页动态条件查询InstrumentGatherParams", notes = "分页动态条件查询InstrumentGatherParams")
    @GetMapping
    public RestResponse<List<DtoInstrumentGatherParams>> findByPage(InstrumentGatherParamsCriteria instrumentGatherParamsCriteria) {
        PageBean<DtoInstrumentGatherParams> pageBean = super.getPageBean();
        RestResponse<List<DtoInstrumentGatherParams>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, instrumentGatherParamsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询", notes = "根据id查询")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrumentGatherParams> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoInstrumentGatherParams> restResp = new RestResponse<>();
        DtoInstrumentGatherParams entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增
     *
     * @param InstrumentGatherParams 实体
     * @return 新增的实体
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("")
    public RestResponse<DtoInstrumentGatherParams> create(@Validated @RequestBody DtoInstrumentGatherParams InstrumentGatherParams) {
        RestResponse<DtoInstrumentGatherParams> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoInstrumentGatherParams data = service.save(InstrumentGatherParams);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新
     *
     * @param InstrumentGatherParams 实体
     * @return 更新后的实体
     */
    @ApiOperation(value = "更新", notes = "更新")
    @PutMapping("")
    public RestResponse<DtoInstrumentGatherParams> update(@Validated @RequestBody DtoInstrumentGatherParams InstrumentGatherParams) {
        RestResponse<DtoInstrumentGatherParams> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoInstrumentGatherParams data = service.update(InstrumentGatherParams);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除", notes = "根据id批量删除")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
