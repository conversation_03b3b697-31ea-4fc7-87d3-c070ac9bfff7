package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 参数管理仓储
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
public interface ParamsRepository extends IBaseJpaRepository<DtoParams, String> {

    /**
     * 根据名称与id获取重复的条数
     *
     * @param id
     * @param paramName
     * @return
     */
    @Query("select count(p) from DtoParams p where p.id <> :id and p.paramName = :paramName and p.isDeleted = 0")
    Integer getCountByName(@Param("id") String id, @Param("paramName") String paramName);

    /**
     * 根据名称获取实体
     *
     * @param paramName
     * @return
     */
    @Query("select p from DtoParams p where p.paramName = :paramName and p.isDeleted = 0")
    DtoParams getByName(@Param("paramName") String paramName);

    /**
     * 根据参数idList查询参数
     *
     * @param idList
     * @return
     */
    @Query("select p from DtoParams p where p.isDeleted = 0 and p.id in :idList")
    List<DtoParams> getListByIds(@Param("idList") Collection<String> idList);

    /**
     * @param dimensionId 量纲id
     * @return 根据量纲id获取相关的参数信息
     */
    @Query("select p from DtoParams p where p.isDeleted = 0 and p.dimensionId = :dimensionId")
    List<DtoParams> findByDimensionId(@Param("dimensionId") String dimensionId);


    /**
     * 批量修改参数的量纲信息
     *
     * @param ids           参数的ids
     * @param dimensionName 量纲名称
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoParams d set d.dimension = :dimensionName where d.id in :ids")
    Integer updateDimension(@Param("ids") List<String> ids, @Param("dimensionName") String dimensionName);

    /**
     * 所有未删除参数
     * @return 所有未删除参数
     */
    List<DtoParams> findAllByIsDeletedFalse();
}