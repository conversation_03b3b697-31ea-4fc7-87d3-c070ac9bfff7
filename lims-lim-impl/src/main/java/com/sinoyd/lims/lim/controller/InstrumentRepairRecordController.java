package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentRepairRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentRepairRecord;
import com.sinoyd.lims.lim.service.InstrumentRepairRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仪器维修记录接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
@Api(tags = "仪器维修记录")
@Validated
@RestController
@RequestMapping("/api/lim/instrumentRepairRecord")
public class InstrumentRepairRecordController extends BaseJpaController<DtoInstrumentRepairRecord, String, InstrumentRepairRecordService>{

    /**
     * 根据id获取仪器维修记录
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取仪器维修记录", notes = "根据id获取仪器维修记录")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrumentRepairRecord> find(@PathVariable(name = "id") String id){

        RestResponse<DtoInstrumentRepairRecord> restResp = new RestResponse<>();
        DtoInstrumentRepairRecord record = service.findOne(id);
        restResp.setData(record);

        restResp.setRestStatus(StringUtil.isNull(record) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 根据申请单id获取仪器维修记录
     * @param purchaseApplyID 申请单id
     * @return
     */
    @ApiOperation(value = "根据申请单id获取仪器维修记录", notes = "根据申请单id获取仪器维修记录")
    @GetMapping("/purchaseApplyID/{purchaseApplyID}")
    public RestResponse<List<DtoInstrumentRepairRecord>> findByPurchaseApplyID(@PathVariable(name = "purchaseApplyID") String purchaseApplyID){
        RestResponse<List<DtoInstrumentRepairRecord>> rest = new RestResponse<>();
        List<DtoInstrumentRepairRecord> instrumentRepairRecords = service.findByPurchaseApplyID(purchaseApplyID);
        rest.setRestStatus(StringUtil.isEmpty(instrumentRepairRecords) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        rest.setData(instrumentRepairRecords);
        return rest;
    }
    
     /**
      * 分页动态条件查询维修记录
      * @param criteria
      * @return
      */
    @ApiOperation(value = "分页动态条件查询维修记录", notes = "分页动态条件查询维修记录")
    @GetMapping
    public RestResponse<List<DtoInstrumentRepairRecord>> findByPage(InstrumentRepairRecordCriteria criteria){

        RestResponse<List<DtoInstrumentRepairRecord>> restResp = new RestResponse<>();

        PageBean<DtoInstrumentRepairRecord> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增仪器维修记录
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增仪器维修记录", notes = "新增仪器维修记录")
    @PostMapping
    public RestResponse<DtoInstrumentRepairRecord> save(@Validated @RequestBody DtoInstrumentRepairRecord entity) {

        RestResponse<DtoInstrumentRepairRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentRepairRecord data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改仪器维修记录
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改仪器维修记录", notes = "修改仪器维修记录")
    @PutMapping
    public RestResponse<DtoInstrumentRepairRecord> update(@Validated @RequestBody DtoInstrumentRepairRecord entity) {

        RestResponse<DtoInstrumentRepairRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentRepairRecord data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id批量删除仪器维护记录
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量删除仪器维修记录", notes = "根据id批量删除仪器维修记录")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}