package com.sinoyd.lims.od.dto.customer;

import com.sinoyd.lims.od.dto.DtoLabGroup;
import com.sinoyd.lims.od.dto.DtoSampleResult;
import lombok.Data;

import java.util.List;

/**
 * 样品结果汇总传输实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/11
 * @since V100R001
 */
@Data
public class DtoSampleResultTemp {

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 样品结果
     */
    private DtoSampleResult sampleResult;

    /**
     * 实验组集合
     */
    private List<DtoLabGroup> labGroupList;

}
