package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoProject2FixedProperty;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * Project2FixedProperty数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface Project2FixedPropertyRepository extends IBaseJpaPhysicalDeleteRepository<DtoProject2FixedProperty, String> {

    /**
     * 通过任务id获取 例行任务关联点位集合
     *
     * @param projectId 任务id
     * @return 例行任务关联点位集合
     */
    List<DtoProject2FixedProperty> findByProjectId(String projectId);

    /**
     * 通过任务id集合获取 例行任务关联点位集合
     *
     * @param projectId 任务id
     * @return 例行任务关联点位集合
     */
    List<DtoProject2FixedProperty> findByProjectIdIn(List<String> projectId);

    /**
     * 通过方案id集合获取 例行任务关联点位集合
     *
     * @param propertyIds 方案id集合
     * @return 例行任务关联点位集合
     */
    List<DtoProject2FixedProperty> findByFixedPropertyIdIn(List<String> propertyIds);

    /**
     * 通过方案id获取 例行任务关联点位
     *
     * @param propertyId 方案id
     * @return 例行任务关联点位集合
     */
    List<DtoProject2FixedProperty> findByFixedPropertyId(String propertyId);


}