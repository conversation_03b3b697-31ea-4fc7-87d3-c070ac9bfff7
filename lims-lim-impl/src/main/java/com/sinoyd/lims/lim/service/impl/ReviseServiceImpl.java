package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.service.CalculateService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.service.ReviseService;
import com.sinoyd.lims.lim.service.TestExpandService;
import com.sinoyd.lims.lim.service.TestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Optional;

/**
 * 数据修约工具实现
 *
 * <AUTHOR>
 * @version ：v1.0.0
 * @date ：2021/10/9
 */
@Service
@Slf4j
public class ReviseServiceImpl implements ReviseService {

    private TestService testService;

    private TestExpandService testExpandService;

    private CodeService codeService;

    private CalculateService calculateService;


    @Override
    public String getDecimal(Integer mostSignificance, Integer mostDecimal, String value) {
        return calculateService.revise(mostSignificance, mostDecimal, value);
    }

    @Override
    public String getDecimal(Integer mostSignificance, Integer mostDecimal, String value, Boolean isSci) {
        return calculateService.revise(mostSignificance, mostDecimal, value, isSci);
    }

    @Override
    public String getDecimal(String testId, String sampleTypeId, String value) {
        DtoTest dtoTest = testService.findOne(testId);
        return getDecimal(dtoTest, sampleTypeId, value);
    }

    @Override
    public String getDecimal(DtoTest dtoTest, String sampleTypeId, String value) {
        String testId = dtoTest.getId();
        Integer mostSignificance = dtoTest.getMostSignificance();
        Integer mostDecimal = dtoTest.getMostDecimal();
        List<DtoTestExpand> expands = testExpandService.findRedisByTestId(testId);
        if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
            Optional<DtoTestExpand> optionalTestExpand = expands.stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId)).findFirst();
            if (!optionalTestExpand.isPresent()) {
                optionalTestExpand = expands.stream().filter(p -> p.getSampleTypeId().equals(dtoTest.getSampleTypeId())).findFirst();
            }
            if (optionalTestExpand.isPresent()) {
                DtoTestExpand dtoTestExpand = optionalTestExpand.get();
                mostSignificance = dtoTestExpand.getMostSignificance();
                mostDecimal = dtoTestExpand.getMostDecimal();
            }
        }
        String result = getDecimal(mostSignificance, mostDecimal, value);
        return mostSignificance.toString() + ";" + mostDecimal.toString() + ";" + result;
    }

    @Override
    public String getDecimal(String testId, String value) {
        DtoTest dtoTest = testService.findOne(testId);
        return getDecimal(dtoTest, value);
    }

    @Override
    public String getDecimal(DtoTest dtoTest, String value) {
        Integer mostSignificance = dtoTest.getMostSignificance();
        Integer mostDecimal = dtoTest.getMostDecimal();
        String result = getDecimal(mostSignificance, mostDecimal, value);
        return mostSignificance.toString() + ";" + mostDecimal.toString() + ";" + result;
    }

    @Override
    public String formatSci(String value) {
        if (!value.contains("E")) {
            value = new BigDecimal(value).toEngineeringString();
        }
        DtoCode dtoCode = codeService.findByCode("LIM_ChgSciSwitch_Show");
        if (dtoCode != null && "1".equals(dtoCode.getDictValue())) {
            value = changeSciValue(value);
        }
        if (value.contains("10^")) {
            return setTopDopt(value);
        }
        return value;
    }

    @Override
    public String formatSci(String value, Integer digit, Integer decimalDigit) {
        DtoCode dtoCode = codeService.findByCode("LIM_ChgSciSwitch_Show");
        if (dtoCode == null || (dtoCode != null && !"1".equals(dtoCode.getDictValue()))) {
            return getDecimal(digit, decimalDigit, value);
        }
        String format = "0.";
        if (digit == 1) {
            format = "0";
        }
        int i = 1;
        while (i < digit) {
            format += "0";
            i++;
        }
        format += "E0";
        DecimalFormat decimalFormat = new DecimalFormat(format);
        decimalFormat.setRoundingMode(RoundingMode.HALF_EVEN);
        MathContext ctx = new MathContext(digit, RoundingMode.HALF_EVEN);
        BigDecimal bigDecimal = new BigDecimal(value, ctx);
        String formatValue = decimalFormat.format(bigDecimal.doubleValue());
        return formatValue;
    }

    @Override
    public boolean isSci(String valueStr) {
        String reg = "^((-?\\d+.?\\d*)[Ee]{1}(-?\\d+))$";
        return valueStr.matches(reg);
    }

    @Override
    public String sci2Number(String sciStr) {
        return new BigDecimal(sciStr).toPlainString();
    }

    /**
     * 转换为科学计数法，不考虑次方数的上标格式
     *
     * @param value 单元格值
     * @return 返回相应的值
     */
    private String changeSciValue(String value) {
        try {
            StringBuilder forReturn = new StringBuilder(value);
            if ((value.contains("E") && !value.contains("N")) ||
                    (value.contains("E") && value.contains("ND"))) {
                String[] strings = value.split("E");
                String zNum = "";
                if (strings[0] == null || "".equals(strings[0])) {
                    return value;
                }
                if (strings.length != 3) {
                    if (strings[1].contains("x")) {
                        String[] valStrings = strings[1].split("x");
                        zNum = valStrings[0].replace("+", "");
                        return strings[0] + "×10^" + zNum + "x" + valStrings[1];
                    } else {
                        zNum = strings[1].replace("+", "");
                        return strings[0] + "×10^" + zNum;
                    }
                } else {
                    String[] valStrings = strings[1].split("x");
                    zNum = valStrings[0].replace("+", "");
                    String bNum = strings[2].replace("+", "");
                    return strings[0] + "×10^" + zNum + "x" + valStrings[1] + "×10^" + bNum;
                }
            } else if (value.contains("↑")) {
                String[] strings = value.split("↑");
                if (strings.length == 2) {
                    return strings[0] + "<sup>" + strings[1] + "</sup>";
                }
                if (strings.length > 2) {
                    for (int i = 1; i < strings.length; i += 2) {
                        strings[i] = "<sup>" + strings[i] + "</sup>";
                    }
                    for (String s : strings) {
                        forReturn.append(s);
                    }
                }
            } else if (value.contains("↓")) {
                String[] strings = value.split("↓");
                if (strings.length > 2) {
                    for (int i = 1; i < strings.length; i += 2) {
                        strings[i] = "<sub>" + strings[i] + "</sub>";
                    }
                    for (String s : strings) {
                        forReturn.append(s);
                    }
                }
            }
            return forReturn.toString();
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BaseException("数据转换科学计数法失败");
        }
    }

    /**
     * 设置科学计数法次方数的上标格式
     *
     * @param value 单元格值
     * @return 格式化完成后的值
     */
    private String setTopDopt(String value) {
        String[] strArr = value.split("\\^");
        if (strArr.length <= 1) {
            return value;
        }
        //判断当前上标部分是否存在其他数据
        if (strArr[1].contains(" ")) {
            String[] arr = strArr[1].split(" ");
            if (arr.length != 2) {
                return value;
            }
            String upValue = strArr[0] + "^<sup>" + arr[0] + "</sup> " + arr[1];
            return upValue.replace("^", "");
        }
        if (strArr.length != 3) {
            if (strArr[1].contains("x")) {
                String upValue = strArr[0] + "^<sup>" + strArr[1].split("x")[0] + "</sup>x" + strArr[1].split("x")[1];
                return upValue.replace("^", "");
            } else {
                String upValue = strArr[0] + "^<sup>" + strArr[1] + "</sup>";
                return upValue.replace("^", "");
            }
        }
        String upValue = strArr[0] + "^<sup>" + strArr[1].split("x")[0] + "</sup>x" + strArr[1].split("x")[1] + "^<sup>" + strArr[2] + "</sup>";
        return upValue.replace("^", "");
    }

    /**
     * 根据有效位数修约
     *
     * @param value            值
     * @param mostSignificance 有效位数
     * @return 返回修约值
     */
    private String revisionByMostSignificance(BigDecimal value, Integer mostSignificance) {
        String result = value.toPlainString();
        boolean isNeedRevision = false;// 整数部分和小数部分位数之和大于有效位数的
        boolean isNeedScienceRevision = false;// 是否需要修约成科学计数法
        if (value.abs().compareTo(new BigDecimal(1)) >= 0) {// 绝对值区间在[1,+∞)情况
            String valueStr = result;
            if (valueStr.contains(".")) {
                // 有小数部分
                if (valueStr.split("\\.")[0].replace("-", "").length() > mostSignificance) {
                    // 整数部分的位数比有效位数大
                    isNeedScienceRevision = true;
                } else if (valueStr.replace(".", "").replace("-", "").length() > mostSignificance) {
                    isNeedRevision = true;
                }
            } else {
                if (valueStr.length() > mostSignificance) {
                    isNeedScienceRevision = true;
                }
            }
        } else {// 值在区间(0,1)情况
            String valueStr = result.replace("0.", "").replace("-", "");
            if (valueStr.length() > mostSignificance) {
                DecimalFormat df = new DecimalFormat();
                StringBuffer style = new StringBuffer("0");
                for (int i = 0; i < mostSignificance; i++) {
                    if (i == 0) {
                        style.append(".");
                    }
                    style.append("0");
                }
                df.applyPattern(style.toString());
                result = df.format(value);
            }
        }
        if (isNeedRevision) {// 按有效位数修约
            DecimalFormat df = new DecimalFormat();
            StringBuffer style = new StringBuffer("0");
            for (int i = 0; i < mostSignificance - (result.split("\\.")[0].replace("-", "")).length(); i++) {
                if (i == 0) {
                    style.append(".");
                }
                style.append("0");
            }
            df.applyPattern(style.toString());
            result = df.format(value);
        }
        if (isNeedScienceRevision) {// 修成科学计数法
            DecimalFormat df = new DecimalFormat();
            StringBuffer style = new StringBuffer("0");
            for (int i = 0; i < mostSignificance - 1; i++) {
                if (i == 0) {
                    style.append(".");
                }
                style.append("0");
            }
            style.append("E0");
            df.applyPattern(style.toString());
            result = df.format(value);
        }
        return result;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    @Lazy
    public void setTestExpandService(TestExpandService testExpandService) {
        this.testExpandService = testExpandService;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    @Lazy
    public void setCalculateService(CalculateService calculateService) {
        this.calculateService = calculateService;
    }
}
