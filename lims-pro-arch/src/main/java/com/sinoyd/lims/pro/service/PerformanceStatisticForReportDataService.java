package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForReportData;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Date;
import java.util.List;


/**
 * PerformanceStatisticForReportData操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/24
 * @since V100R001
 */
public interface PerformanceStatisticForReportDataService extends IBaseJpaService<DtoPerformanceStatisticForReportData, String> {


    /**
     * 创建报告工作量
     *
     * @param projectId    项目id
     * @param reportMarker 编制报告人
     * @param reportTime   报告时间
     */
    void createReportStatistic(String projectId, String reportMarker, Date reportTime);

    /**
     * 创建报告工作量
     *
     * @param projectIds   项目id
     * @param reportMarker 编制报告人
     * @param reportTime   报告时间
     * @param principalContextUser 当前账号信息
     */
    void createReportStatistic(List<String> projectIds, String reportMarker, Date reportTime,CurrentPrincipalUser principalContextUser);

    /**
     * 查询报告相关的绩效
     *
     * @param baseCriteria 查询条件
     */
    void findByPage(PageBean<DtoPerformanceStatisticForReportData> pb, BaseCriteria baseCriteria);

    /**
     * 获取总计行
     *
     * @param baseCriteria
     * @return
     */
    DtoPerformanceStatisticForReportData findSumPerformanceStatistic(BaseCriteria baseCriteria);
}