package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.lims.DtoEnterpriseExtend;
import com.sinoyd.base.repository.lims.EnterpriseExtendRepository;
import com.sinoyd.base.service.EnterpriseExtendService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.stereotype.Service;


/**
 * EnterpriseExtend操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/9/18
 * @since V100R001
 */
 @Service
public class EnterpriseExtendServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoEnterpriseExtend,String,EnterpriseExtendRepository> implements EnterpriseExtendService {

    @Override
    public void findByPage(PageBean<DtoEnterpriseExtend> pb, BaseCriteria enterpriseExtendCriteria) {
        pb.setEntityName("DtoEnterpriseExtend a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, enterpriseExtendCriteria);
    }
}