-- ----------------------------------------------
-- -------- 归属于RCC部分的表相关初始化数据脚本 -------
-- -------- TB_LIM_AnalyzeMethod ------------------
-- ----------------------------------------------

INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('0281ad54-9b29-4d40-ac33-ee8e7ce563da', '水质 32种元素的测定 电感耦合等离子体发射光谱法（水平法）', 'HJ 776-2015', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-23 16:59:42', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-23 16:59:48', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('116f085d-1b1d-42c4-a967-6e4a5fb657ad', ' 水质 pH值的测定 电极法', 'HJ 1147-2020', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-08 14:16:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-10 15:58:04', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('135e0736-a38a-454f-b030-e0a2022959fd', '水污染物排放总量监测技术规范', 'HJ/T 92-2002', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:15:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:15:05', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('178b08ec-08e3-46dd-9260-89547d6ca0e1', '环境空气 降尘的测定 重量法', 'HJ 1221-2021', false, '1753-01-01 00:00:00', '', false, true, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:09:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:09:09', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('2d18a6df-f1e7-4311-8a06-35801add0974', '亚甲基蓝分光光度法《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:57:56', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:57:56', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('2e9a9d7c-44c3-4f8b-89df-9d7dc9e7899e', '水质 挥发酚的测定 4-氨基安替比林分光光度法(直接法)', 'HJ 503-2009', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:24:19', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:24:19', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('3a80f232-e440-4c0a-8e07-15d81b3e2113', '降雨量的测定', '', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-28 17:47:20', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-28 17:47:20', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1051', '城市区域环境振动测量方法', 'GB/T 10071-1988', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1988', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:08:40', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1154', '大气固定污染源 氟化物的测定 离子选择电极法', 'HJ/T 67-2001', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1155', '大气固定污染源 镉的测定 火焰原子吸收分光光度法', 'HJ/T 64.1-2001', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1156', '大气固定污染源 镉的测定 石墨炉原子吸收分光光度法', 'HJ/T 64.2-2001', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1158', '大气固定污染源 镍的测定 火焰原子吸收分光光度法', 'HJ/T 63.1-2001', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1159', '大气固定污染源 镍的测定 石墨炉原子吸收分光光度法', 'HJ/T 63.2-2001', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2002', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:09:36', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1168', '大气降水pH值的测定 电极法', 'GB/T 13580.4-1992', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1992', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:09:54', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1170', '大气降水电导率的测定方法', 'GB/T 13580.3-1992', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1992', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:10:53', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1171', '大气降水中铵盐的测定', 'GB/T 13580.11-1992', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1992', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:10:31', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1172', '大气降水中氟、氯、亚硝酸盐、硝酸盐、硫酸盐的测定 离子色谱法', 'GB/T 13580.5-1992', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '1992', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B025', '2022-06-08 14:44:36', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1224', '辐射环境保护管理导则 电磁辐射监测仪器和方法', 'HJ/T 10.2-1996', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1996', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1228', '高氯废水 化学需氧量的测定 氯气校正法', 'HJ/T 70-2001', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:23:42', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1233', '工业企业厂界环境噪声排放标准', 'GB 12348-2008', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2008', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1347', '工作场所空气有毒物质测定 酰胺类化合物', 'GBZ/T 160.62-2004', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2004', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:11:20', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1408', '固定污染源废气 氮氧化物的测定 定电位电解法', 'HJ 693-2014', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1412', '固定污染源废气 低浓度颗粒物的测定 重量法', 'HJ 836-2017', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1414', '固定污染源废气 二氧化硫的测定 定电位电解法', 'HJ 57-2017', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1415', '固定污染源废气 二氧化硫的测定 非分散红外吸收法', 'HJ 629-2011', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2011', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:00:27', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1424', '固定污染源废气 硫酸雾的测定 离子色谱法', 'HJ 544-2016', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1432', '固定污染源废气 铅的测定 火焰原子吸收分光光度法', 'HJ 685-2014', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:01:25', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1439', '固定污染源废气 一氧化碳的测定 定电位电解法', 'HJ 973-2018', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 16:59:53', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1441', '固定污染源废气 总烃、甲烷和非甲烷总烃的测定 气相色谱法', 'HJ 38-2017', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1452', '固定污染源排气中丙烯腈的测定 气相色谱法', 'HJ/T 37-1999', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1456', '固定污染源排气中二氧化硫的测定 碘量法', 'HJ/T 56-2000', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2000', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1459', '固定污染源排气中酚类化合物的测定 4-氨基安替比林分光光度法（萃取法）', 'HJ/T 32-1999', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-23 17:06:30', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1465', '固定污染源排气中甲醇的测定 气相色谱法', 'HJ/T 33-1999', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1466', '固定污染源排气中颗粒物测定与气态污染物采样方法', 'GB/T 16157-1996', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1996', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:05:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1472', '固定污染源排气中氯气的测定 甲基橙分光光度法', 'HJ/T 30-1999', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1474', '固定污染源排气中氰化氢的测定 异烟酸-吡唑啉酮分光光度法', 'HJ/T 28-1999', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1489', '固体废物 腐蚀性测定 玻璃电极法', 'GB/T 15555.12-1995', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:25:07', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1492', '固体废物 汞、砷、硒、铋、锑的测定 微波消解/原子荧光法', 'HJ 702-2014', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1493', '固体废物 金属元素的测定 电感耦合等离子体质谱法', 'HJ 766-2015', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B011', '2022-03-29 15:19:38', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1494', '固体废物 六价铬的测定 二苯碳酰二肼分光光度法', 'GB/T 15555.4-1995', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:22:24', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1506', '固体废物 总铬的测定 二苯碳酰二肼分光光度法', 'GB/T 15555.5-1995', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:24:20', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1508', '固体废物 总铬的测定 火焰原子吸收分光光度法', 'HJ 749-2015', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 16:58:39', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1527', '环境空气 氨的测定 次氯酸钠-水杨酸分光光度法', 'HJ 534-2009', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:45:53', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1529', '环境空气 苯系物的测定 活性炭吸附/二硫化碳解吸-气相色谱法', 'HJ 584-2010', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2010', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-31 13:56:13', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1532', '环境空气 臭氧的测定 紫外光度法', 'HJ 590-2010', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2010', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1534', '环境空气 氮氧化物(一氧化氮和二氧化氮)的测定 盐酸萘乙二胺分光光度法', 'HJ 479-2009', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1538', '环境空气 二氧化硫的测定 甲醛吸收-副玫瑰苯胺分光光度法', 'HJ 482-2009', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1542', '环境空气 氟化物的测定 滤膜采样/氟离子选择电极法', 'HJ 955-2018', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:19:37', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1544', '环境空气 氟化物的测定 石灰滤纸采样氟离子选择电极法', 'HJ 481-2009', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1547', '环境空气 挥发性有机物的测定 罐采样/气相色谱-质谱法', 'HJ 759-2015', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:16:10', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1553', '环境空气 铅的测定 火焰原子吸收分光光度法', 'GB/T 15264-1994', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1994', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:23:21', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1555', '环境空气 铅的测定 石墨炉原子吸收分光光度法', 'HJ 539-2015', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1561', '环境空气 总悬浮颗粒物的测定 重量法', 'GB/T 15432-1995', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:06:27', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1569', '环境空气和废气 氨的测定 纳氏试剂分光光度法', 'HJ 533-2009', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1572', '环境空气和废气 氯化氢的测定 离子色谱法', 'HJ 549-2016', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1599', '建筑施工场界环境噪声排放标准', 'GB 12523-2011', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2011', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1601', '交流输变电工程电磁环境监测方法（试行）', 'HJ 681-2013', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2013', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1605', '空气和废气 颗粒物中金属元素的测定 电感耦合等离子体发射光谱法', 'HJ 777-2015', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1606', '空气和废气 颗粒物中铅等金属元素的测定 电感耦合等离子体质谱法', 'HJ 657-2013', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2013', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B015', '2022-05-18 16:43:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1610', '空气质量 苯胺类的测定 盐酸萘乙二胺分光光度法', 'GB/T 15502-1995', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1995', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:33:35', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1611', '空气质量 恶臭的测定 三点比较式臭袋法', 'GB/T 14675-1993', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1993', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:33:05', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1617', '空气质量 一氧化碳的测定 非分散红外法', 'GB/T 9801-1988', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1988', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1630', '气相色谱法测定多氯联苯 美国环保局', 'EPA 8082A-2007', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-09-08 14:41:31', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1674', '社会生活环境噪声排放标准', 'GB 22337-2008', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2008', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1678', '生活垃圾化学特性通用检测方法', 'CJ/T 96-2013', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2013', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1827', '水、土中有机磷农药测定的气相色谱法', 'GB/T 14552-2003', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2003', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:19:38', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1835', '水质 65种元素的测定 电感耦合等离子体质谱法', 'HJ 700-2014', false, '2021-06-08 00:00:00', '1', true, false, '空白', '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B025', '2022-06-15 14:55:28', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1837', '水质 pH值的测定 电极法', 'HJ 1147-2020', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2020', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-10 15:57:58', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1838', '水质 阿特拉津的测定 高效液相色谱法', 'HJ 587-2010', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2010', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1842', '水质 氨氮的测定 纳氏试剂分光光度法', 'HJ 535-2009', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1849', '水质 苯胺类化合物的测定 气相色谱-质谱法', 'HJ 822-2017', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1853', '水质 吡啶的测定 顶空/气相色谱法', 'HJ 1072-2019', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2019', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:14:31', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1855', '水质 丙烯腈的测定 气相色谱法', 'HJ/T 73-2001', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2001', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1864', '水质 多环芳烃的测定 液液萃取和固相萃取高效液相色谱法', 'HJ 478-2009', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-10-21 13:15:05', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1869', '水质 二氧化氯和亚氯酸盐的测定 连续滴定碘量法', 'HJ 551-2016', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1875', '水质 酚类化合物的测定 液液萃取/气相色谱法', 'HJ 676-2013', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2013', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:18:36', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1876', '水质 粪大肠菌群的测定 多管发酵法', 'HJ 347.2-2018', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1881', '水质 钙和镁总量的测定 EDTA滴定法', 'GB/T 7477-1987', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1882', '水质 高锰酸盐指数的测定', 'GB/T 11892-1989', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B017', '2022-06-22 09:45:19', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1885', '水质 铬的测定 火焰原子吸收分光光度法', 'HJ 757-2015', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1886', '水质 汞、砷、硒、铋和锑的测定 原子荧光法', 'HJ 694-2014', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1889', '水质 化学需氧量的测定 重铬酸盐法', 'HJ 828-2017', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:22:04', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1896', '水质 挥发性有机物的测定 吹扫捕集/气相色谱-质谱法', 'HJ 639-2012', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2012', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-10-08 11:36:38', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1899', '水质 挥发性有机物的测定 顶空/气相色谱-质谱法', 'HJ 810-2016', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:15:21', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1916', '水质 硫化物的测定 碘量法', 'HJ/T 60-2000', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2000', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1917', '水质 硫化物的测定 气相分子吸收光谱法', 'HJ/T 200-2005', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2005', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1921', '水质 六价铬的测定 二苯碳酰二肼分光光度法', 'GB/T 7467-1987', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:05:22', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1922', '水质 六六六、滴滴涕的测定 气相色谱法', 'GB/T 7492-1987', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B024', '2022-10-18 14:41:47', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1924', '水质 氯苯类化合物的测定 气相色谱法', 'HJ 621-2011', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2011', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:17:19', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1925', '水质 氯化物的测定 硝酸银滴定法', 'GB/T 11896-1989', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:41:42', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1928', '水质 镍的测定 火焰原子吸收分光光度法', 'GB/T 11912-1989', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:48:59', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1931', '水质 氰化物的测定 容量法和分光光度法（异烟酸-巴比妥酸分光光度法）', 'HJ 484-2009', false, '2021-06-08 00:00:00', '1', false, false, '', '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B012', '2022-06-23 11:53:59', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1935', '水质 全盐量的测定 重量法', 'HJ/T 51-1999', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1937', '水质 溶解氧的测定 电化学探头法', 'HJ 506-2009', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1948', '水质 石油类和动植物油类的测定 红外分光光度法', 'HJ 637-2018', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2012', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:29:03', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1949', '水质 水温的测定 温度计或颠倒温度计测定法', 'GB/T 13195-1991', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1991', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:24:42', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1952', '水质 铁、锰的测定 火焰原子吸收分光光度法', 'GB/T 11911-1989', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:47:15', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1955', '水质 铜、锌、铅、镉的测定 原子吸收分光光度法', 'GB/T 7475-87', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:47:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1958', '水质 无机阴离子（F⁻、Cl⁻、NO₂⁻、Br⁻、NO₃⁻、PO₄³⁻、SO₃²⁻、SO₄²⁻）的测定 离子色谱法', 'HJ 84-2016', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B025', '2022-10-20 15:07:59', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1976', '水质 悬浮物的测定 重量法', 'GB/T 11901-1989', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:17:06', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1985', '水质 银的测定 火焰原子吸收分光光度法', 'GB/T 11907-89', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:44:35', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1989', '水质 有机磷农药的测定 气相色谱法', 'GB/T 13192-1991', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '1991', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-08-23 10:46:56', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1991', '水质 有机氯农药和氯苯类化合物的测定 气相色谱-质谱法', 'HJ 699-2014', false, '2021-06-08 00:00:00', '1', true, false, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-24 21:04:14', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1993', '水质 浊度的测定 浊度计法', 'HJ 1075-2019', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2019', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1995', '水质 总氮的测定 碱性过硫酸钾消解紫外分光光度法', 'HJ 636-2012', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2012', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:13:47', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c1997', '水质 总铬的测定', 'GB/T 7466-1987', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2000', '水质 总磷的测定 钼酸铵分光光度法', 'GB/T 11893-1989', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B022', '2022-06-09 14:48:26', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2002', '水质 总有机碳的测定 燃烧氧化-非分散红外吸收法', 'HJ 501-2009', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:14:57', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2019', '铁路边界噪声限值及其测量方法', 'GB/T 12525-1990', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1990', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:47:46', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2029', '土壤 干物质和水分的测定 重量法', 'HJ 613-2011', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2011', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2032', '土壤 氰化物和总氰化物的测定 分光光度法', 'HJ 745-2015', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2058', '土壤和沉积物 硫化物的测定 亚甲基蓝分光光度法', 'HJ 833-2017', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2078', '土壤水分测定法', 'NY/T 52-1987', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1987', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2086', '土壤质量 铅、镉的测定 石墨炉原子吸收分光光度法', 'GB/T 17141-1997', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '1997', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:08:23', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2091', '土壤质量 总汞、总砷、总铅的测定 原子荧光法 第1部分：土壤中总汞的测定', 'GB/T 22105.1-2008', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2008', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:07:52', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2093', '土壤质量 总汞、总砷、总铅的测定 原子荧光法 第2部分：土壤中总砷的测定', 'GB/T 22105.2-2008', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2008', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:08:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('49d37696-e5b5-412a-b3fd-06d4141c2153', '移动通信基站电磁辐射环境监测方法', 'HJ 972-2018', false, '2021-06-08 00:00:00', '1', false, false, null, '00000000-0000-0000-0000-000000000000', null, '2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:55', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('4d9526b6-9ee7-4ad2-8e94-4e23b57464b1', '水质 硫化物的测定 亚甲基蓝分光光度法', 'HJ 1226-2021', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:10:08', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:10:08', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('52b94251-d37c-4bac-9af6-089219be7107', '原子吸收分光光度法《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:59:04', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:59:04', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('5429f7b0-abb6-46fd-a7c4-28e1af4f5e95', '水质 叶绿素a 的测定 分光光度法', 'HJ 897-2017', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-16 09:11:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-16 09:11:05', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('59a1ed45-a12a-4b40-bd47-eba018bff41e', '毛细柱气相色谱法 《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:26:40', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:26:40', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('61581e41-ed57-444c-a333-639e5764b306', 'ICP-AES法 《土壤元素的近代分析方法》中国环境监测总站(1992年)', '《土壤元素的近代分析方法》', false, '1992-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:55:19', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:55:19', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('762ae61d-3206-4ba4-92a8-623d543c3f96', '二苯碳酰二肼分光光度法《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '（第四版增补版）国家环保总局（2007年）', false, '1753-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:57:48', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:57:48', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('76c4e88e-f338-47f8-9785-ff8d12d9dd95', '固定污染源废气 油烟和油雾的测定 红外分光光度法', 'HJ 1077-2019', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-10 14:29:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-10 14:29:09', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('7eff469a-e11a-40ff-b34b-cd8c2da752be', '水质 挥发酚的测定 4-氨基安替比林分光光度法', 'HJ 503-2009', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:24:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-10 13:48:39', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('83b52dba-479e-4d2a-8d0f-2e072eea7943', '水质 浊度的测定', 'GB/T 13200-1991', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:22:31', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:22:31', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('86ED7B1F-BA6E-11E9-9C35-7922EB66D383', '固定污染源排放烟气黑度的测定 林格曼烟气黑度图法', 'HJ/T 398-2007', false, '2001-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, '2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:03:42', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('86EE657B-BA6E-11E9-9C35-7922EB66D383', '水质 32种元素的测定 电感耦合等离子体发射光谱法', 'HJ 776-2015', false, '2001-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, '2015', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-10 13:45:35', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('86F14B39-BA6E-11E9-9C35-7922EB66D383', '水质 硝基苯类化合物的测定 气相色谱-质谱法', 'HJ 716-2014', false, '2001-01-01 00:00:00', null, true, false, null, '00000000-0000-0000-0000-000000000000', null, '2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:17:36', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('86F1727E-BA6E-11E9-9C35-7922EB66D383', '环境空气 颗粒物中水溶性阴离子（F-、Cl-、Br-、NO2-、NO3-、PO43-、SO32-、SO42-）的测定 离子色谱法', 'HJ 799-2016', false, '2001-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, '2016', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-08 18:10:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:46:37', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('8e11644b-479b-4b39-b169-7b89e7f58fe6', '塞氏盘法《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:19:19', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:19:19', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('a0040df2-e9ed-40ad-bb73-42b796781ed9', '固定污染源排气中酚类化合物的测定 4-氨基安替比林分光光度法', 'HJ/T 32-1999', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-23 17:07:13', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-10 13:44:53', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('a65dde30-2c2f-44be-b718-64c0cb9267d8', '便携式、实验室电导率仪法', '《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', false, '1753-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:58:48', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:58:48', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('a6a6ca0d-b31a-474a-982a-040bb720ccb8', '水中丙烯酰胺的测定 高效液相色谱法', '', false, '1753-01-01 00:00:00', '', false, true, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B012', '2022-08-16 16:24:39', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B012', '2022-08-16 16:24:39', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('b80ece84-9292-4512-94a5-4fc510cc3fe8', '水质 浮游植物的测定 0.1 ml 计数框-显微镜计数法', 'HJ 1216-2021', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:12:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:12:47', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('beb61622-10c2-4a1d-8d72-ec8d10284465', '水质 氨氮的测定 气相分子吸收光谱法', 'HJ/T 195-2005', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:23:39', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:23:39', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('c036917a-3297-429e-a3a1-a1451739d8fe', '水质 丁基黄原酸的测定 紫外分光光度法', 'HJ 756-2015', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-16 12:57:06', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-16 12:57:06', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('d5143d26-e130-42ea-92cc-97b2b02b78df', '原子荧光分光光度法 《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》(第四版增补版)国家环保总局', false, '1753-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:59:40', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:59:40', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('dce2fa8f-60f6-42fe-8816-78243eba1d9b', '水质 氟化物的测定 离子选择电极法 ', 'GB/T 7484-1987', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:21:34', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:21:34', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('dd4537f0-4e46-45b0-8ad6-af8e2600d0c4', '一硝基和二硝基化合物 还原-偶氮光度法《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '《水和废水监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:57:12', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:57:12', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('e5edaa20-a883-4891-96db-a30595ee89cd', '煤中全硫的测定方法', 'GB/T 214-2007', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-19 15:39:10', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-19 15:39:10', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed16fc20-74e2-11ec-bdd3-43f738053e8f', '固体废物  浸出毒性浸出方法  硫酸硝酸法', 'HJ/T 299-2007', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed172331-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 二氧化硫的测定 便携式紫外吸收法', 'HJ 1131-2020', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2020', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed174a42-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 挥发性有机物的测定 固相吸附/气相色谱-质谱法', 'HJ 734-2014', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-16 17:22:00', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed177153-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 氮氧化物的测定 便携式紫外吸收法', 'HJ 1132-2020', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2020', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed179864-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 氮氧化物的测定 非分散红外吸收法 ', 'HJ 692-2014', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:51:01', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed17bf75-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 汞的测定 冷原子吸收分光光度法(暂行)', ' HJ 543-2009', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:51:11', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1834a8-74e2-11ec-bdd3-43f738053e8f', '固定污染源废气 气态汞的测定 活性炭吸附/热裂解原子吸收分光光度法', 'HJ 917-2017', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:09:15', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1882ca-74e2-11ec-bdd3-43f738053e8f', '固定污染源排气中一氧化碳的测定 非色散红外吸收法', 'HJ/T 44-1999', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:51:28', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed18a9db-74e2-11ec-bdd3-43f738053e8f', '固定污染源排气中氮氧化物的测定 盐酸萘乙二胺分光光度法 ', 'HJ/T 43-1999', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:51:35', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed18d0ec-74e2-11ec-bdd3-43f738053e8f', '固定污染源排气中氯乙烯
的测定 气相色谱法', 'HJ/T34-1999', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:51:43', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed191f0e-74e2-11ec-bdd3-43f738053e8f', '固定污染源排气中铬酸雾的测定 二苯基碳酰二肼分光光度法 ', 'HJ/T 29- 1999', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1999', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:52:50', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed19461f-74e2-11ec-bdd3-43f738053e8f', '土壤 pH值的测定 电位法', 'HJ 962-2018', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:23:40', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed196d30-74e2-11ec-bdd3-43f738053e8f', '土壤 石油类的测定 红外分光光度法', 'HJ 1051-2019', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2019', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:42:14', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed199441-74e2-11ec-bdd3-43f738053e8f', '土壤和沉积物 有机氯农药的测定 气相色谱法', 'HJ 921-2017', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-27 09:06:56', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed19bb52-74e2-11ec-bdd3-43f738053e8f', '土壤和沉积物 总汞的测定 催化热解-冷原子吸收分光光度法', 'HJ 923-2017', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:42:23', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed19e263-74e2-11ec-bdd3-43f738053e8f', '土壤和沉积物铜、锌、铅、镍、铬的测定火焰原子吸收分光光度法', ' HJ 491-2019', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2019', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:54:15', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1a3085-74e2-11ec-bdd3-43f738053e8f', '大气降水中钙、镁的测定 原子吸收分光光度法 ', 'GB/T 13580.13-1992', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1992', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:58:01', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1a5796-74e2-11ec-bdd3-43f738053e8f', '大气降水中钠、钾的测定 原子吸收分光光度法', 'GB/T 13580.12-1992', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1992', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:58:01', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1accc9-74e2-11ec-bdd3-43f738053e8f', '异烟酸-吡唑啉酮分光光度法《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 15:59:16', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1b41fc-74e2-11ec-bdd3-43f738053e8f', '气相色谱-质谱法《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '《水和废水监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:18:19', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1b690d-74e2-11ec-bdd3-43f738053e8f', '气相色谱/质谱法（GC/MS）测定半挥发性有机物 美国环保局', 'EPA 8270D-2014', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-09-09 13:59:23', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1bb72f-74e2-11ec-bdd3-43f738053e8f', '气相色谱法 《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）国家环保总局', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-08-04 15:36:43', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1c0551-74e2-11ec-bdd3-43f738053e8f', '水中丙烯酰胺的测定 高效液相色谱法', null, false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 16:00:22', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1c2c62-74e2-11ec-bdd3-43f738053e8f', '水中微囊藻毒素-LR的测定 高效液相色谱串联质谱法', null, false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 16:00:22', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1c5373-74e2-11ec-bdd3-43f738053e8f', '水中微囊藻毒素的测定', 'GB/T 20466-2006', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 16:00:22', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1cefb7-74e2-11ec-bdd3-43f738053e8f', '水质 五日生化需氧量（BOD5）的测定 稀释与接种', 'HJ 505-2009', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 16:00:43', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1e4f50-74e2-11ec-bdd3-43f738053e8f', '水质 氯苯的测定 气相色谱法 ', 'HJ/T 74-2001', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2001', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:17:05', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1e9d72-74e2-11ec-bdd3-43f738053e8f', '水质 水样对费氏弧菌发光强度的抑制作用的测定（发光细菌试验）第三部分：冻干细菌法 ISO 11348-3-2007', ' ISO 11348-3-2007', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 16:02:08', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1f12a5-74e2-11ec-bdd3-43f738053e8f', '水质 游离氯和总氯的测定 N,N-二乙基-1,4-苯二胺滴定法', 'HJ 585-2010', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2010', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1f39b6-74e2-11ec-bdd3-43f738053e8f', '水质 溶解氧的测定 碘量法 GB/T 7489-1987', 'GB/T 7489-1987', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1987', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1f60c7-74e2-11ec-bdd3-43f738053e8f', '水质 烷基汞的测定 吹扫捕集/气相色谱—冷原子荧光光谱法 ', 'HJ 977-2018', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1f87d8-74e2-11ec-bdd3-43f738053e8f', '水质 甲醛的测定 乙酰丙酮分光光度法 ', 'HJ 601-2011', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2011', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1faee9-74e2-11ec-bdd3-43f738053e8f', '水质 百菌清和溴氰菊酯的测定 气相色谱法', 'HJ 698-2014', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-08-25 16:31:25', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed1ffd0b-74e2-11ec-bdd3-43f738053e8f', '水质 石油类的测定 紫外分光光度法（试行）', ' HJ 970-2018', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed20723e-74e2-11ec-bdd3-43f738053e8f', '水质 联苯胺的测定 高效液相色谱法', 'HJ 1017-2019', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2019', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed20994f-74e2-11ec-bdd3-43f738053e8f', '水质 色度的测定 稀释倍数法', 'HJ 1182-2021', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2021', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed20bf60-74e2-11ec-bdd3-43f738053e8f', '水质 色度的测定（铂钴比色法）', 'GB/T 11903-1989', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 19:01:12', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed20e671-74e2-11ec-bdd3-43f738053e8f', '水质 苯胺类化合物的测定 N-(1-萘基)乙二胺偶氮分光光度法', 'GB 11889-1989', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-23 09:11:59', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed210d82-74e2-11ec-bdd3-43f738053e8f', '水质 钙和镁的测定 原子吸收分光光度法 ', ' GB/T 11905-1989', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed213493-74e2-11ec-bdd3-43f738053e8f', '水质 钾和钠的测定 火焰原子吸收分光光度法 ', 'GB/T 11904-1989', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1989', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed215ba4-74e2-11ec-bdd3-43f738053e8f', '水质 阴离子表面活性剂的测定 亚甲蓝分光光度法 ', 'GB/T 7494-1987', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1987', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:50:44', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed2182b5-74e2-11ec-bdd3-43f738053e8f', '水质 黄磷的测定 气相色谱法 ', 'HJ 701-2014', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed21a9c6-74e2-11ec-bdd3-43f738053e8f', '水质  游离氯和总氯的测定 N,N-二乙基-1,4-苯二胺分光光度法', 'HJ 586-2010', false, '2021-06-08 00:00:00', '1', false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2010', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:16:56', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed21d0d7-74e2-11ec-bdd3-43f738053e8f', '水质 四乙基铅的测定 顶空/气相色谱-质谱法 ', 'HJ 959-2018', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed21f7e8-74e2-11ec-bdd3-43f738053e8f', '水质 总大肠菌群、粪大肠菌群和大肠埃希氏菌的测定 酶底物法', 'HJ 1001-2018', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed221ef9-74e2-11ec-bdd3-43f738053e8f', '水质 总汞的测定 冷原子吸收分光光度法', 'HJ 597-2011', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2011', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:36:31', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed22460a-74e2-11ec-bdd3-43f738053e8f', '水质 挥发酚的测定 流动注射-4-氨基安替比林分光光度法', 'HJ 825-2017', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed226d1b-74e2-11ec-bdd3-43f738053e8f', '水质苯系物的测定顶空/气相色谱法', 'HJ 1067-2019', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2019', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B024', '2022-10-28 09:27:45', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed22942c-74e2-11ec-bdd3-43f738053e8f', '河流流量测验规范', ' GB 50179-2015 附录B', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2015', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed22bb3d-74e2-11ec-bdd3-43f738053e8f', '测烟望远镜法《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）国家环保总局', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed22e24e-74e2-11ec-bdd3-43f738053e8f', '海洋监测规范 第4部分：海水分析', 'GB 17378.4-2007', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B014', '2022-05-17 16:18:32', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed233070-74e2-11ec-bdd3-43f738053e8f', '海洋监测规范 第5部分：沉积物分析', 'GB 17378.5-2007 ', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed237e92-74e2-11ec-bdd3-43f738053e8f', '火焰原子吸收分光光度法《土壤元素的近代分析方法》中国环境监测总站（1992年）', '《土壤元素的近代分析方法》', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1992', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed23a5a3-74e2-11ec-bdd3-43f738053e8f', '环境γ辐射剂量率测量技术规范', 'HJ 11517-2021', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2021', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed23ccb4-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 噪声测量修正值', 'HJ 706-2014', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed23f3c5-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 城市声环境常规监测（功能区噪声）', 'HJ 640-2012', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2012', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed241ad6-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 城市声环境常规监测（区域环境噪声）', 'HJ 640-2012', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2012', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed2441e7-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 城市声环境常规监测（道路交通噪声）', 'HJ 640-2012', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2012', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed2468f8-74e2-11ec-bdd3-43f738053e8f', '环境噪声监测技术规范 结构传播固定设备室内噪声', 'HJ 707-2014', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed249009-74e2-11ec-bdd3-43f738053e8f', '环境影响评价技术导则 输变电', 'HJ 24-2020', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2020', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed252c4b-74e2-11ec-bdd3-43f738053e8f', '环境空气 汞的测定 巯基棉富集-冷原子荧光分光光度法(暂行)', 'HJ 542-2009', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2009', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:45:58', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed2ddedd-74e2-11ec-bdd3-43f738053e8f', '环境空气 臭氧前体有机物的测定 罐采样/气相色谱-氢离子火焰检测器/质谱检测器联用法 《环境空气臭氧前体有机物手工监测技术要求（试行）》（环办监测函（2018）240号）附录B  ', '《环境空气臭氧前体有机物手工监测技术要求（试行）》（环办监测函（2018）240号）附录B  ', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B023', '2022-05-17 09:14:34', true, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed2ea22e-74e2-11ec-bdd3-43f738053e8f', '环境空气 苯系物的测定 固体吸附/热脱附-气相色谱法', 'HJ 583-2010', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2010', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed2f6581-74e2-11ec-bdd3-43f738053e8f', '环境空气 降水中阳离子（Na⁺、NH₄⁺、K⁺、Mg²⁺、Ca²⁺）的测定 离子色谱法', 'HJ 1005-2018', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2018', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B025', '2022-10-20 15:05:53', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3028d3-74e2-11ec-bdd3-43f738053e8f', '环境空气PM10和PM2.5的测定 重量法', 'HJ 618-2011', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2011', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed304fe4-74e2-11ec-bdd3-43f738053e8f', '环境空气 总烃、甲烷和非甲烷总烃的测定 直接进样-气相色谱法', 'HJ 604-2017', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2017', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed324bb5-74e2-11ec-bdd3-43f738053e8f', '环境空气 醛、酮类化合物的测定 高效液相色谱法', 'HJ 683- 2014', false, '1753-01-01 00:00:00', null, true, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2014', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B012', '2022-04-08 16:51:31', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed330f06-74e2-11ec-bdd3-43f738053e8f', '环境空气 颗粒物中水溶性阳离子(Li+、Na+、NH4+、K+、Ca2+、Mg2+)的测定 离子色谱法', 'HJ 800-2016', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2016', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed333617-74e2-11ec-bdd3-43f738053e8f', '环境空气中氡的标准测量方法 连续氡监测仪法', 'GB/T 14582-1993 附录C', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1993', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed33d258-74e2-11ec-bdd3-43f738053e8f', '环境空气和废气 酰胺类化合物的测定 液相色谱法 ', 'HJ 801-2016', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2016', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '1aca5f0b-06ff-48eb-AAAA-d6032652B012', '2022-04-26 15:51:43', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3495a9-74e2-11ec-bdd3-43f738053e8f', '环境空气和废气 颗粒物中砷、硒、铋、锑的测定 原子荧光法', 'HJ 1133-2020', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2020', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 18:46:52', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3558fc-74e2-11ec-bdd3-43f738053e8f', '环境空气气态污染物（SO2、NO2、O3、CO）连续自动监测系统技术要求及检测方法', 'HJ 654-2013', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2013', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed35a71d-74e2-11ec-bdd3-43f738053e8f', '环境空气颗粒物（ PM10和PM2.5）连续自动监测系统技术要求及检测方法', 'HJ 653-2013', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2013', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed35f53e-74e2-11ec-bdd3-43f738053e8f', '生活饮用水标准检验方法 农药指标 ', 'GB/T 5750.9-2006', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed36917f-74e2-11ec-bdd3-43f738053e8f', '生活饮用水标准检验方法 有机物指标 ', 'GB/T 5750.8-2006', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed36dfa0-74e2-11ec-bdd3-43f738053e8f', '生活饮用水标准检验方法 消毒副产物指标 ', 'GB/T 5750.10-2006', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3706b1-74e2-11ec-bdd3-43f738053e8f', '甲基橙指示剂滴定法《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '《水和废水监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3754d2-74e2-11ec-bdd3-43f738053e8f', '石墨炉原子吸收分光光度法《空气和废气监测分析方法》（第四版增补版）国家环保总局(2007年)', '《空气和废气监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed383f33-74e2-11ec-bdd3-43f738053e8f', '石墨炉原子吸收法《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '《水和废水监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, '2006年', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed386644-74e2-11ec-bdd3-43f738053e8f', '碘量法 《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed388d55-74e2-11ec-bdd3-43f738053e8f', '碱片-离子色谱法《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed38b466-74e2-11ec-bdd3-43f738053e8f', '碱片-重量法 《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed38db77-74e2-11ec-bdd3-43f738053e8f', '碱片-铬酸钡分光光度法《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2007', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed392999-74e2-11ec-bdd3-43f738053e8f', '空气质量 甲醛的测定 乙酰丙酮分光光度法 GB/T 15516-1995', 'GB/T 15516-1995', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 1995', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3950aa-74e2-11ec-bdd3-43f738053e8f', '蚕桑区桑叶氟化物含量控制标准', 'DB 33/392-2003', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2003', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3977bb-74e2-11ec-bdd3-43f738053e8f', '表面污染测定 第1部分：β发射体（Eβmax＞0.15MeV）和 α发射体', 'GB/T 14056.1-2008', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2008', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed399ecc-74e2-11ec-bdd3-43f738053e8f', '酚酞指示剂滴定法《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '《水和废水监测分析方法》（第四版增补版）', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed39eced-74e2-11ec-bdd3-43f738053e8f', '酸碱指示剂滴定法《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-10-26 16:55:20', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3a621e-74e2-11ec-bdd3-43f738053e8f', '重量法 《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-10-26 16:55:16', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3a892f-74e2-11ec-bdd3-43f738053e8f', '重铬酸钾容量法《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-10-26 16:55:11', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3ab040-74e2-11ec-bdd3-43f738053e8f', '钼锑抗分光光度法《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-10-26 16:55:06', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed3afe61-74e2-11ec-bdd3-43f738053e8f', '镜检法 《水和废水监测分析方法》（第四版增补版）国家环保总局（2006年）', '', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2006', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-10-26 16:55:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('ed467012-74e2-11ec-bdd3-43f738053e8f', '高压架空输电线、变电站无线电干扰测量方法', 'GB/T 7349-2002', false, '1753-01-01 00:00:00', null, false, true, null, '00000000-0000-0000-0000-000000000000', null, ' 2002', -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-02-18 00:00:00', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('eecbad44-70e0-4d29-80e5-8bb4671149ab', '4-氨基安替比林分光光度法《空气和废气监测分析方法》（第四版增补版）国家环保总局（2007年）', '《空气和废气监测分析方法》（第四版增补版）', false, '2007-01-01 00:00:00', null, false, false, null, '00000000-0000-0000-0000-000000000000', null, null, -1, -1, true, null, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:56:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:56:09', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('fd75b1a3-1ba9-4fca-8ab2-ddb50d465557', '水质 可吸附有机卤素（AOX）的测定 微库仑法', 'HJ 1214-2021', false, '1753-01-01 00:00:00', '', false, false, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:11:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-04-29 13:11:50', false, null, false);
INSERT INTO TB_LIM_AnalyzeMethod (id, methodName, countryStandard, isDeleted, effectiveDate, methodCode, isCompleteTogether, isControlled, remark, parentId, countryStandardName, yearSn, effectiveDays, warningDays, isInforce, status, orgId, creator, createDate, domainId, modifier, modifyDate, isInputBySample, alias, isCrossDay) VALUES ('fe0b32f6-f63a-4190-b58f-0ac95cc3237d', '水质 松节油的测定 气相色谱法', 'HJ 696-2014', false, '1753-01-01 00:00:00', '', false, true, '', '00000000-0000-0000-0000-000000000000', '', '', -1, -1, true, '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 18:03:48', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-02-09 18:03:48', false, null, false);
