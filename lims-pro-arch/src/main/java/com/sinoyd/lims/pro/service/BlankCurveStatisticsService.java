package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;

import java.util.Map;

/**
 * 空白曲线的统计
 *
 * <AUTHOR>
 * @version V1.0.0 2020/02/10
 * @since V100R001
 */
public interface BlankCurveStatisticsService {

    /**
     * 获取空白曲线数据
     *
     * @param baseCriteria 查询条件
     */
    Map<String, Object> findBlankQCData(BaseCriteria baseCriteria);


    /**
     * 存储曲线的陆路
     *
     * @param testId   测试项目id
     * @param personId 人员id
     * @return 返回数据结构
     */
    Map<String, Object> getFilePathConfig(String testId, String personId);
}
