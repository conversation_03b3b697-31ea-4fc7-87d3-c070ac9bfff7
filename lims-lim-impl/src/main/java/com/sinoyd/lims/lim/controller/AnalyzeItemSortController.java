package com.sinoyd.lims.lim.controller;

import java.util.List;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.AnalyzeItemSortCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSort;
import com.sinoyd.lims.lim.service.AnalyzeItemSortService;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.ApiOperation;

/**
 * 分析项目排序接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-14
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/analyzeItemSort")
@Validated
public class AnalyzeItemSortController extends BaseJpaController<DtoAnalyzeItemSort, String, AnalyzeItemSortService> {

    /**
     * 根据id查询分析项目排序
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询分析项目排序", notes = "根据id查询分析项目排序")
    @GetMapping("/{id}")
    public RestResponse<DtoAnalyzeItemSort> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoAnalyzeItemSort> restResp = new RestResponse<>();

        DtoAnalyzeItemSort entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询分析项目排序
     * 
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询分析项目排序", notes = "分页动态条件查询分析项目排序")
    @GetMapping
    public RestResponse<List<DtoAnalyzeItemSort>> findByPage(AnalyzeItemSortCriteria criteria) {

        RestResponse<List<DtoAnalyzeItemSort>> restResp = new RestResponse<>();

        PageBean<DtoAnalyzeItemSort> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增分析项目排序
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增分析项目排序", notes = "新增分析项目排序")
    @PostMapping
    public RestResponse<DtoAnalyzeItemSort> save(@Validated @RequestBody DtoAnalyzeItemSort entity) {

        RestResponse<DtoAnalyzeItemSort> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeItemSort data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 更新分析项目排序
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "更新分析项目排序", notes = "更新分析项目排序")
    @PutMapping
    public RestResponse<DtoAnalyzeItemSort> update(@Validated @RequestBody DtoAnalyzeItemSort entity) {

        RestResponse<DtoAnalyzeItemSort> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeItemSort data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }
    
    /**
     * 根据id删除分析项目排序
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id删除分析项目排序", notes = "根据id删除分析项目排序")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除分析项目排序
     * 
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除分析项目排序", notes = "批量删除分析项目排序")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}



  