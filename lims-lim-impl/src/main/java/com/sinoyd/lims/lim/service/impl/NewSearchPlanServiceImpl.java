package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchPlan;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchResult;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchTask;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.NewSearchPlanRepository;
import com.sinoyd.lims.lim.service.NewSearchPlanService;
import com.sinoyd.lims.lim.service.PersonService;
import org.apache.avalon.framework.Enum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 查新计划接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Service
public class NewSearchPlanServiceImpl extends BaseJpaServiceImpl<DtoNewSearchPlan, String, NewSearchPlanRepository> implements NewSearchPlanService {

    private PersonService personService;

    @Override
    public void findByPage(PageBean<DtoNewSearchPlan> page, BaseCriteria criteria) {
        page.setEntityName("DtoNewSearchPlan p");
        page.setSelect("select p");
        super.findByPage(page, criteria);
        List<DtoNewSearchPlan> resultList = page.getData();
        List<String> executorIds = resultList.stream().map(DtoNewSearchPlan::getExecutor).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(executorIds) ? personService.findAll(executorIds) : new ArrayList<>();
        for (DtoNewSearchPlan newSearchPlan : resultList) {
            // 执行人
                personList.stream().filter(p -> newSearchPlan.getExecutor().equals(p.getId())).findFirst().ifPresent(person -> {
                    newSearchPlan.setExecutorName(person.getCName());
                });

        }
    }


    @Override
    @Transactional
    public DtoNewSearchPlan save(DtoNewSearchPlan entity) {
        if (repository.countByPlanName(entity.getPlanName(), entity.getId()) > 0) {
            throw new BaseException("已存在相同名称的计划！");
        }
        entity.setStatus(EnumLIM.EnumPlanStatus.新建.getValue());
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoNewSearchPlan update(DtoNewSearchPlan entity) {
        if (repository.countByPlanName(entity.getPlanName(), entity.getId()) > 0) {
            throw new BaseException("已存在相同名称的计划！");
        }
        return super.update(entity);
    }

    /**
     * 提交查新计划
     *
     * @param id 查新计划主键id
     * @return job实体
     */
    @Override
    @Transactional
    public Map<String, Object> submit(String id) {
        DtoNewSearchPlan newSearchPlan = repository.findOne(id);
        if (EnumLIM.EnumPlanStatus.启用.getValue().equals(newSearchPlan.getStatus())) {
            throw new BaseException("不能重复提交");
        }
        Map<String, Object> jobMap = new HashMap<>();
        jobMap.put("id", id);
        jobMap.put("jobName", newSearchPlan.getPlanName());
        jobMap.put("jobGroup", "DEFAULT");
        jobMap.put("invokeTarget", "newSearchPlanProcess.createTask('" + newSearchPlan.getPlanName() + "')");
        jobMap.put("cronExpression", generateCron(newSearchPlan.getDealCycle(), newSearchPlan.getDealDate()));
        jobMap.put("misfirePolicy", 3);
        jobMap.put("isConcurrent", false);
        jobMap.put("status", 1);
        jobMap.put("remark", "");
        // 如果是从停用状态重新启用。给前端一个是否更新的状态，服务代码中需要调用恢复任务的接口，而不是新增任务
        jobMap.put("isUpdate", newSearchPlan.getStatus().equals(EnumLIM.EnumPlanStatus.停用.getValue()));
        newSearchPlan.setStatus(EnumLIM.EnumPlanStatus.启用.getValue());
        super.save(newSearchPlan);
        return jobMap;
    }

    @Override
    @Transactional
    public String deactivate(String id) {
        DtoNewSearchPlan newSearchPlan = repository.findOne(id);
        if (EnumLIM.EnumPlanStatus.停用.getValue().equals(newSearchPlan.getStatus())) {
            throw new BaseException("当前计划已停用");
        }
        newSearchPlan.setStatus(EnumLIM.EnumPlanStatus.停用.getValue());
        super.save(newSearchPlan);
        return id;
    }

    /**
     * 根据计划配置生成Corn表达式
     *
     * @param dealCycle 执行周期
     * @param dealDate  月度执行日期
     * @return Corn表达式
     */
    private String generateCron(int dealCycle, int dealDate) {
        String cron = "";
        switch (EnumLIM.EnumNewSearchPlanDealCycle.getByValue(dealCycle)) {
            case "每月":
                cron = "0 0 0 " + dealDate + " * ?";
                break;
            case "每2月":
                cron = "0 0 0 " + dealDate + " 1/2 ?";
                break;
            case "每6月":
                cron = "0 0 0 " + dealDate + " 1/6 ?";
                break;
            case "每年":
                // 选择每年时，开始执行日期为当前提交日期所在月份，可能存在2月份的月度执行日期超出28号的情况，所以先定义为1号
                LocalDate currentDate = LocalDate.now();
                cron = "0 0 0 1 " + currentDate.getMonth() + " ?";
                break;
            default:
                throw new IllegalArgumentException("Unsupported dealCycle: " + dealCycle);
        }
        return cron;
    }

    @Autowired
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }
}
