default:
  tags:
    - dind

variables:
  MAVEN_REPO_USERNAME: $MVN_USER  # 在GitLab设置Variables
  MAVEN_REPO_PASSWORD: $MVN_PWD

stages:
  - xpre      # 代码检查（看sonar设置也可能会有dingding推送），单元检查等
  - compile

sonarqube:
  stage: xpre
  image:
    name: registry.dev.yd/sinoyd/registry/sonar-scanner
  variables:
    PLUGIN_TOKEN: $DINGTALK_TOKEN
    SONAR_PROJECT_KEY: $SONAR_PROJECT_KEY
    SONAR_TOKEN: $SONAR_TOKEN
  allow_failure: true
  script:
    - /app/runcheck.sh
  only:
    refs:
      - urgent

compile:jdk8:
  image: registry.dev.yd/sinoyd/registry/maven:3.8.4-openjdk-8-dn
  stage: compile
  script:
    #- ls -l
    - cp m2-settings.xml /root/.m2/settings.xml
    - mvn clean deploy -Dmonitor.version=$CI_COMMIT_TAG
  rules:
    - if: $CI_COMMIT_TAG