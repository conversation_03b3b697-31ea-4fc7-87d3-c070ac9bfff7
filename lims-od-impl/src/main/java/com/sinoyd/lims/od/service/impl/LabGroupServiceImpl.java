package com.sinoyd.lims.od.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.od.dto.*;
import com.sinoyd.lims.od.repository.*;
import com.sinoyd.lims.od.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 实验组接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
public class LabGroupServiceImpl extends BaseJpaServiceImpl<DtoLabGroup, String, LabGroupRepository> implements LabGroupService {


    private LabSeqService labSeqService;


    @Override
    public void findByPage(PageBean<DtoLabGroup> page, BaseCriteria criteria) {
        page.setEntityName("DtoLabGroup a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
    }

    @Override
    public List<DtoLabGroup> findBySampleIdIn(List<String> sampleIds) {
        return repository.findBySampleIdIn(sampleIds);
    }


    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        List<DtoLabSeq> labSeqList = labSeqService.findByGroupIdIn(idList);
        if (StringUtil.isNotEmpty(labSeqList)) {
            labSeqService.logicDeleteById(labSeqList.stream().map(DtoLabSeq::getId).collect(Collectors.toList()));
        }
        return super.logicDeleteById(ids);
    }


    @Autowired
    @Lazy
    public void setLabSeqService(LabSeqService labSeqService) {
        this.labSeqService = labSeqService;
    }


}
