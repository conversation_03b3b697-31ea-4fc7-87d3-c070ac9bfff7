package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.TestExpandCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.service.TestExpandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 修约规则
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "修约规则: 修约规则服务")
@RestController
@RequestMapping("/api/lim/testExpand")
@Validated
public class TestExpandController extends BaseJpaController<DtoTestExpand, String, TestExpandService> {

    /**
     * 分页动态条件查询测试项目扩展信息
     *
     * @param criteria 查询条件
     * @return 结果集合
     */
    @ApiOperation(value = "分页动态条件查询测试项目扩展信息", notes = "分页动态条件查询测试项目扩展信息")
    @GetMapping
    public RestResponse<List<DtoTestExpand>> findByPage(TestExpandCriteria criteria) {
        RestResponse<List<DtoTestExpand>> restResp = new RestResponse<>();
        PageBean<DtoTestExpand> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }


    /**
     * 通过测试项目id,检测类型id获取
     *
     * @param entity 根据testId 测试项目id和sampleTypeId 检测类型id
     */
    @ApiOperation(value = "按测试项目id,检测类型id获取查询修约规则", notes = "按测试项目id,检测类型id获取查询修约规则")
    @PostMapping("")
    public RestResponse<DtoTestExpand> get(@RequestBody DtoTestExpand entity) {
        RestResponse<DtoTestExpand> response = new RestResponse<>();
        DtoTestExpand testexpend = service.get(entity.getTestId(), entity.getSampleTypeId());
        response.setData(testexpend);
        response.setRestStatus(StringUtil.isNull(testexpend) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return response;
    }

    /**
     * 真删
     *
     * @param testExpand 根据testId 测试项目id和sampleTypeId 检测类型id
     * @return
     */
    @ApiOperation(value = "按测试项目id,检测类型id删除查询修约规则", notes = "按测试项目id,检测类型id删除查询修约规则")
    @PostMapping("/delete")
    public RestResponse<Boolean> delete(@RequestBody DtoTestExpand testExpand) {
        RestResponse<Boolean> response = new RestResponse<Boolean>();
        response.setRestStatus(ERestStatus.SUCCESS);
        service.delete(testExpand.getTestId(), testExpand.getSampleTypeId());
        response.setCount(1);

        return response;
    }

    /**
     * 新增
     *
     * @param testExpand
     * @return
     */
    @ApiOperation(value = "新增修约规则", notes = "新增修约规则")
    @PostMapping("/save")
    public RestResponse<DtoTestExpand> save(@Validated @RequestBody DtoTestExpand testExpand) {
        RestResponse<DtoTestExpand> response = new RestResponse<>();
        response.setRestStatus(ERestStatus.SUCCESS);
        DtoTestExpand data = service.save(testExpand);
        response.setData(data);
        response.setCount(1);

        return response;
    }

    @GetMapping("/sampleType")
    public RestResponse<List<String>> findSampleTypeIdByTestId(@RequestParam(value = "testId") String testId) {
        RestResponse<List<String>> restResp = new RestResponse<>();
        List<String> sampleTypeIds = service.findSampleTypeIdByTestId(testId);
        restResp.setData(sampleTypeIds);
        return restResp;
    }

    /**
     * 根据测试项目id查询扩展信息
     *
     * @param testIds 测试项目id集合
     * @return 测试项目扩展信息
     */
    @ApiOperation(value = "根据测试项目id查询扩展信息", notes = "根据测试项目id查询扩展信息")
    @PostMapping("/test")
    public RestResponse<List<DtoTestExpand>> findByIds(@RequestBody List<String> testIds) {
        RestResponse<List<DtoTestExpand>> restResp = new RestResponse<>();
        restResp.setData(service.findByTestIds(testIds));
        return restResp;
    }
}