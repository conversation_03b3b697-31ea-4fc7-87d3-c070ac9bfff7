-- 标准版报告添加平行样数据相关组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate)
VALUES ('39a21865-22cb-4632-bfe2-806a09ef253f', 'outParallelStdDataSource', '标准版现场平行检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtOutParallelStdTable\", \"dtPxCompoundStdTable\"]', b'1', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-03-15 16:49:36', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-03-15 16:49:36');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate)
VALUES ('4ccd38c3-00fd-45b4-b5aa-d2b26d249d6e', 'dtPxCompoundStdTable', '标准版现场平行样化合物检测结果表组件', 'dtPxCompoundStdTable',
        'dtPxCompoundSource', 1, 60, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-03-15 16:46:59', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-03-15 16:46:59');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate)
VALUES ('53a5e187-4d15-4ef8-8eb0-13895a8b41ff', 'dtOutParallelStdTable', '标准版现场平行样检测结果表组件', 'dtOutParallelStdTable',
        'dtOutParallelSource', 1, 13, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-03-15 16:45:19', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-03-15 16:45:19');

update TB_LIM_ReportModule
set sonTableJson = '["dtWaterHeadStdTable", "dtWasteWaterStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]'
where moduleCode = 'wasteWaterStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '["dtSyHeadStdTable", "dtSyStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]'
where moduleCode = 'sYStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '["dtWaterHeadStdTable", "dtSoilStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]'
where moduleCode = 'soilStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '["dtWaterHeadStdTable", "dtNormalWaterStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]'
where moduleCode = 'normalWaterStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '["dtWaterHeadStdTable", "dtSolidStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]'
where moduleCode = 'solidStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '["dtOrgHeadStdTable", "dtOrgStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]'
where moduleCode = 'orgGasStdDataSource';

update TB_LIM_ReportModule
set sonTableJson = '["dtWaterHeadStdTable", "dtUnOrgGasStdTable", "dtCompoundStdNewTable", "outParallelStdDataSource"]'
where moduleCode = 'unOrgGasStdDataSource';