package com.sinoyd.lims.monitor.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;


/**
 * FixedPoint2Point实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="FixedPoint2Point")
 @Data
 public  class FixedPoint2Point implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 点位id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String fixedPointId;
    
    /**
    * 关联点位id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String pointId;
    
 }