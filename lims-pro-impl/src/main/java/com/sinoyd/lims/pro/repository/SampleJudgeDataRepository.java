package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;

import java.util.Collection;
import java.util.List;


/**
 *  分析绩效repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/03/12
 */
public interface SampleJudgeDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleJudgeData, String> {

    /**
     * 通过样品ids获取比对数据
     *
     * @param sampleIds 样品ids
     * @return 比对数据
     */
    List<DtoSampleJudgeData> findBySampleIdIn(List<String> sampleIds);

    /**
     * 根据样品id和测试项目id查询数据
     *
     * @param sampleIds 样品id
     * @param testId    测试项目id
     * @return List<DtoSampleJudgeData>
     */
    List<DtoSampleJudgeData> findBySampleIdInAndTestId(List<String> sampleIds, String testId);

    /**
     * 根据样品id删除数据
     *
     * @param sampleIds 样品id集合
     */
    Integer deleteBySampleIdIn(List<String> sampleIds);

    /**
     * 根据样品id和测试项目id查询数据
     *
     * @param sampleIds 样品id
     * @param testIds   测试项目id
     * @return List<DtoSampleJudgeData>
     */
    List<DtoSampleJudgeData> findBySampleIdInAndTestIdIn(Collection<String> sampleIds, Collection<String> testIds);
}
