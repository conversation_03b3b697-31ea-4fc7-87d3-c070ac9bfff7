package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 分析方法查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-13
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeMethodCriteria extends BaseCriteria {

    /**
     * 关键字：分析方法名称、受控编号、标准编号
     */
    private String key;

    /**
     * 状态
     */
    private List<Integer> statusList;

    /**
     * 是否采样方法
     */
    private Boolean isSamplingMethod = false;

    /**
     * 检测类型（采样方法使用）
     */
    private String sampleTypeId;

    /**
     * 状态单个
     */
    private Integer status;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();

        /**
         * 关键字模糊查找
         */
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and (methodName like :key or countryStandard like :key or methodCode like :key)");
            values.put("key", "%" + this.key + "%");
        }
        /**
         * 状态查找
         */
        if (statusList != null && !statusList.isEmpty()) {
            condition.append(" and status in :statusList ");
            values.put("statusList", statusList);
        }
        if (StringUtil.isNotNull(isSamplingMethod)) {
            condition.append(" and isSamplingMethod = :isSamplingMethod ");
            values.put("isSamplingMethod", isSamplingMethod);
        }

        if (StringUtil.isNotEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and sampleTypeId = :sampleTypeId ");
            values.put("sampleTypeId", sampleTypeId);
        }

        if (StringUtil.isNotNull(status)) {
            condition.append(" and status = :status ");
            values.put("status", status);
        }

        condition.append(" and isDeleted = 0 ");

        return condition.toString();
    }
}