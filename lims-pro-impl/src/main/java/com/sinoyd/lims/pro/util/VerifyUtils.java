package com.sinoyd.lims.pro.util;

import com.sinoyd.boot.common.util.StringUtil;

import java.util.Map;

/**
 * 导入导出工具类
 *
 * <AUTHOR>
 * @version V1.0.0 2024/02/21
 * @since V100R001
 */
public class VerifyUtils {

    /**
     * 检查是否为空行
     *
     * @param map 行数据映射
     * @return 是否为空行
     */
    public static boolean checkEmptyRow(Map<String, Object> map) {
        boolean isEmpty = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (StringUtil.isNotNull(entry.getValue())) {
                isEmpty = false;
                break;
            }
        }
        return isEmpty;
    }
}
