package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品评价信息查询条件
 * <AUTHOR>
 * @version v1.0.0 2019/5/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class    EntSupplierGoodsEvaluationCriteria extends BaseCriteria {

    // 默认当前评价的idevaluationId
    private String evaluationId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuffer conditon = new StringBuffer();
        if(StringUtil.isNotNull(evaluationId) &&
                !UUIDHelper.GUID_EMPTY.equals(this.evaluationId)){
            conditon.append(" and evaluationId = :evaluationId");
            values.put("evaluationId",this.evaluationId);
        }
        return conditon.toString();
    }
}