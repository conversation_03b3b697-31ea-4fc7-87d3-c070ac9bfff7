package com.sinoyd.lims.pro.strategy.context;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.strategy.strategy.documentFileName.AbsDocumentFileNameStrategy;
import com.sinoyd.lims.strategy.context.DocumentFileNameContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 附件名称策略上下文
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/29
 */
@Component
public class DocumentFileNameContextImpl implements DocumentFileNameContext {

    /**
     * 所有具体生成策略字典
     */
    private final Map<String, AbsDocumentFileNameStrategy> documentFileNameStrategyMap = new ConcurrentHashMap<>();

    @Autowired
    public DocumentFileNameContextImpl(Map<String, AbsDocumentFileNameStrategy> documentFileNameStrategyMap) {
        this.documentFileNameStrategyMap.putAll(documentFileNameStrategyMap);
    }

    /**
     * 生成附件名称
     *
     * @param map 报表id
     * @return 附件名称
     */
    @Override
    public Map<String, String> generateDocumentName(String beanName, Map<String, Object> map) {
        if (!StringUtil.isNotNull(this.documentFileNameStrategyMap.get(beanName))) {
            throw new BaseException("调用方法不合法");
        }
        return this.documentFileNameStrategyMap.get(beanName).generateDocumentName(map);
    }
}
