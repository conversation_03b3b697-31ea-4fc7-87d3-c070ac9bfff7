package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.od.dto.DtoEnvGasResult;

import java.util.List;

/**
 * EnvGasResultRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface EnvGasResultRepository extends IBaseJpaRepository<DtoEnvGasResult, String> {

    /**
     * 根据实验组ids查询环境空气汇总结果
     *
     * @param groupIds 实验组ids
     * @return 环境空气汇总结果
     */
    List<DtoEnvGasResult> findByGroupIdIn(List<String> groupIds);

    /**
     * 根据实验组id查询环境空气汇总结果
     *
     * @param groupId 实验组id
     * @return 环境空气汇总结果
     */
    DtoEnvGasResult findByGroupId(String groupId);
}
