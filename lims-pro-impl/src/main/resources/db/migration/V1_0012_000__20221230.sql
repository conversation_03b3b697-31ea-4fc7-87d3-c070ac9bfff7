DROP TABLE IF EXISTS TB_PRO_OrderContract;
-- 添加合同信息表
CREATE TABLE TB_PRO_OrderContract
(
    id                  varchar(50)  NOT NULL COMMENT 'id',
    orderId             varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '订单id',
    contractCode        varchar(50)  NOT NULL DEFAULT '' COMMENT '合同编号',
    contractName        varchar(255) NOT NULL DEFAULT '' COMMENT '合同名称',
    contractNature      varchar(50)  NOT NULL DEFAULT '' COMMENT '合同性质',
    firstEntId          varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '甲方id',
    firstEntName        varchar(255) NOT NULL DEFAULT '' COMMENT '甲方名称',
    firstEntPersonName  varchar(50)  NOT NULL DEFAULT '' COMMENT '甲方联系人',
    secondEntName       varchar(255) NOT NULL DEFAULT '' COMMENT '乙方名称',
    secondEntPersonName varchar(50)  NOT NULL DEFAULT '' COMMENT '乙方联系人',
    secondEntType       varchar(50) NULL DEFAULT '' COMMENT '乙方企业类型',
    totalAmount         decimal(18, 2) NULL DEFAULT 0 COMMENT '合同金额',
    registrant          varchar(50) NULL DEFAULT '' COMMENT '登记人',
    signDate            datetime(0) NULL DEFAULT '1753-01-01 00:00:00' COMMENT '签订日期',
    excuteStartTime     datetime(0) NULL DEFAULT '1753-01-01 00:00:00' COMMENT '合同履行开始时间',
    excuteEndTime       datetime(0) NULL DEFAULT '1753-01-01 00:00:00' COMMENT '合同履行结束时间',
    signPersonId        text NULL COMMENT '签订人员id',
    isHavingSub         bit(1) NULL DEFAULT b'0' COMMENT '是否有分包项',
    subAmount           decimal(18, 2) NULL DEFAULT 0 COMMENT '分包金额',
    subOrgs             varchar(255) NULL DEFAULT '' COMMENT '分包机构',
    summary             varchar(1000) NULL DEFAULT '' COMMENT '合同概述',
    assessRecord        varchar(1000) NULL DEFAULT '' COMMENT '评审记录',
    isDeleted           bit(1)       NOT NULL DEFAULT b'0' COMMENT '假删',
    orgId               varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    domainId            varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator             varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate          datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    modifier            varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate          datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT = '合同信息';