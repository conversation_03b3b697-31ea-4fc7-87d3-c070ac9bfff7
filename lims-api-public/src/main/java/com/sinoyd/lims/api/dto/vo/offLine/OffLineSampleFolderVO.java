package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：点位表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineSampleFolderVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 采样单id
     */
    private String receiveId;

    /**
     * 点位id
     */
    private String folderId;

    /**
     * 点位名称
     */
    private String watchSpot;

    /**
     * 周期
     */
    private Integer periodCount;

    /**
     * 批次
     */
    private Integer timePerPeriod;

    /**
     * 样次
     */
    private Integer samplePerTime;

    /**
     * 样品编号
     */
    private String sampleCodes;

    /**
     * 样品数量
     */
    private String sampleCount;

    /**
     * 测试项目
     */
    private String analyzeItems;

    /**
     * 测试数量
     */
    private String analyzeCount;

    /**
     * 检测类型
     */
    private String sampleTypeName;

    /**
     * 计划经度
     */
    private String lon;

    /**
     * 计划纬度
     */
    private String lat;

    /**
     * 所属机构
     */
    private String orgId;
}
