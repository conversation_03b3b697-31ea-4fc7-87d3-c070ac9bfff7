package com.sinoyd.lims.od.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * LabGroup查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LabGroupCriteria extends BaseCriteria {


    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 是否完成
     */
    private Boolean isComplete = false;

    /**
     * 任务类型
     */
    private Integer taskType;

    @Override
    public String getCondition() {

        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotNull(sampleId)) {
            condition.append(" and a.sampleId = :sampleId");
            values.put("sampleId", sampleId);
        }
        if (StringUtil.isNotNull(isComplete) && isComplete) {
            condition.append(" and exists (select 1 from DtoLabSeq s where a.id = s.groupId and s.labState = 1)");
        }

        return condition.toString();

    }
}
