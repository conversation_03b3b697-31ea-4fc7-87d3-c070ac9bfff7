package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoCertHistoryInfo;

import java.util.List;
import java.util.Map;

/**
 * CertHistoryInfo操作接口
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/28
 */
public interface CertHistoryInfoService extends IBaseJpaService<DtoCertHistoryInfo, String> {
    /**
     * 查询项目历史证书信息
     * @param projectId 样品标识
     */
    Map<String,Object> queryHistoryInfo(String projectId);

    /**
     * 获取历史附件
     * @param certHistoryFileIds 历史附件信息
     * @return 获取历史附件
     */
    List<DtoDocument> queryHistoryFile(List<String> certHistoryFileIds);
}
