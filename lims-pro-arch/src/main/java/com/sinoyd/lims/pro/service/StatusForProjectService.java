package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.probase.service.StatusForProjectBaseService;


/**
 * 项目状态操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
public interface StatusForProjectService extends StatusForProjectBaseService {
    /**
     * 修改状态数据
     *
     * @param from    状态起
     * @param to      状态止
     * @param sign    工作流信号对象
     * @param project 项目实体
     */
    void modifyStatus(String from, String to, DtoWorkflowSign sign, DtoProject project);
}