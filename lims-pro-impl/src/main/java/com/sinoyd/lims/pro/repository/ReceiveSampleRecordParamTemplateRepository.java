package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecordParamTemplate;
import com.sinoyd.lims.pro.dto.DtoReportFolderInfo;

import java.util.List;


/**
 * 送样单参数模板访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/19
 * @since V100R001
 */
public interface ReceiveSampleRecordParamTemplateRepository extends IBaseJpaPhysicalDeleteRepository<DtoReceiveSampleRecordParamTemplate, String> {

    /**
     * 根据模板名称查询
     *
     * @param templateName 模板名称
     * @return 返回相应的模板
     */
    List<DtoReceiveSampleRecordParamTemplate> findByTemplateName(String templateName);

    /**
     * 根据样品类型id查询
     *
     * @param sampleTypeId 样品类型id
     * @return 返回相应的模板
     */
    List<DtoReceiveSampleRecordParamTemplate> findBySampleTypeId(String sampleTypeId);
}