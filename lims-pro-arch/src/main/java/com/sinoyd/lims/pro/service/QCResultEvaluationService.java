package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoQCResultEvaluation;

import java.util.Collection;
import java.util.List;


/**
 * QCResultEvaluation操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface QCResultEvaluationService extends IBaseJpaService<DtoQCResultEvaluation, String> {

    /**
     * 根据项目id查询对应的质控任务评价
     *
     * @param projectIds 项目id集合
     * @return 质控任务评价
     */
    List<DtoQCResultEvaluation> findByProjectIds(Collection<String> projectIds);
}