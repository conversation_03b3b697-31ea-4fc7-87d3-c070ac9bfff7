-- 异常脚本调整Lims -> Rcc
DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
begin
	declare table_count int;
    select count(*) from information_schema.tables where table_schema = database() and table_name = 'TB_LIM_CompareJudge';
    if table_count = 0 THEN
          CREATE TABLE TB_LIM_CompareJudge  (
                                      id varchar(50) NOT NULL COMMENT 'id',
                                      analyzeItemId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析项目id',
                                      checkType int(11) NULL DEFAULT NULL COMMENT '检测类型（0-废水比对，1-废气比对）',
                                      defaultStandardNum int(11) NOT NULL DEFAULT 0 COMMENT '默认标样数',
                                      orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
                                      creator varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
                                      createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                      domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
                                      modifier varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
                                      modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                      PRIMARY KEY (id)
          );
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;