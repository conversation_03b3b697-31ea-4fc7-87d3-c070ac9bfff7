INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c78','首页方案审核待办统计','HOME','auditSolutionStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7a','首页实验室审核待办统计','HOME','laboratoryAuditStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7b','首页实验室检测中代办统计','HOME','laboratoryCheckingStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7c','首页实验室待待检测代办统计','HOME','laboratoryWaitingCheckStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7d','首页现场任务数据审核待办统计','HOME','localDataAuditStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:23:42');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7e','首页现场任务数据复核待办统计','HOME','localDataCheckStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:24:12');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7f','首页现场任务待办统计','HOME','localMissionStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:23:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7g','首页委托现场送样待办统计','HOME','localSendSampleStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:33:47');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7h','首页方案编制待办统计','HOME','makeSolutionStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7i','首页重点污染源多企业待办统计','HOME','pollutionEnterprisesStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7j','首页采样准备待办统计','HOME','prepareSampleStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7k','首页项目审核待办统计','HOME','projectAuditStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7l','首页任务办结待办统计','HOME','projectEndStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7m','首页项目下达待办统计','HOME','projectIssueStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7n','首页项目登记待办统计','HOME','projectRegisterStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7o','首页数据汇总待办统计','HOME','qcCollectStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7p','首页评价结果待办统计','HOME','qcEvaluateStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7r','首页质控任务登记待办统计','HOME','qcRegisterStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7s','首页报告审核待办统计','HOME','reportAuditStatisticTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7t','首页报告校核待办统计','HOME','reportCheckStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7u','首页报告编制待办统计','HOME','reportEditStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7v','首页报告复核待办统计','HOME','reportReviewStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7w','首页报告签发待办统计','HOME','reportSignStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7x','首页例行项目登记待办统计','HOME','rontineRegisterStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7y','首页样品分配待办统计','HOME','sampleAssignStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9634c7z','首页样品交接待办统计','HOME','sampleReceiveStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('0aee0edd-82e1-4657-8523-1434e9645245','首页报告编制(新)待办统计','HOME','reportEditNewStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-07-27 14:01:50');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('112abe4f-4679-4148-995a-1b424c5f6d3f','消耗品标准样品过期提醒','MESSAGE','consumableOverdue.sendMsg(''消耗品标准样品过期提醒'')','0 0/15 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:19:43','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:19:43');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('53a470c6-7134-4cc4-97d2-8c4d691a19c2','领样单状态更新','DEFAULT','updateStatusTask.process(''ReceiveSubSampleRecordStatus'')','0 0 */1 * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-16 16:36:20','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-16 17:32:51');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('7881d8e5-3980-4abd-8465-a5f50cd0945c','首页订单登记待办统计','HOME','orderRegisterStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-29 15:46:06','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-29 15:48:16');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('8c11a244-0598-4422-a4fa-9cb3b22c6182','数据查询服务','DEFAULT','dataProcessTask.process()','0 0/15 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:12:43','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:12:43');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('9b3c8d9a-5ffd-43d5-ae2c-5b5b6c2928c2','消耗品标准样品库存提醒','MESSAGE','consumableStorage.sendMsg(''消耗品标准样品库存提醒'')','0 0/15 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:18:47','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:18:47');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('ad77f432-97f5-4ed8-bb79-79cf91f8fffc','分析数据状态更新','DEFAULT','updateStatusTask.process(''AnalyseDataStatus'')','0 0 */1 * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-16 16:35:51','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-16 17:32:48');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('b7f8a0e3-8a55-4357-ac03-85abcb8c2b48','首页订单审核待办统计','HOME','orderAuditStatisticsTask.statisticsProjectNums','0 0/5 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-29 15:46:28','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-12-29 15:47:13');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('b9158aeb-d742-48d2-a07b-4dec718f0794','仪器维护超期提醒','MESSAGE','instrumentMaintain.sendMsg(''仪器维护超期提醒'')','0 0/15 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:15:14','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:15:14');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('bc0c3a04-9c32-4183-b18e-6fbdbd5a3377','样品状态更新','DEFAULT','updateStatusTask.process(''SampleStatus'')','0 0 */1 * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-04-16 13:06:45','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2024-05-16 17:32:38');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('d9b9f187-427d-4278-90e9-9577e107c2da','消耗品标准样品即将过期提醒','MESSAGE','consumableWillOverdue.sendMsg(''消耗品标准样品即将过期提醒'')','0 0/15 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:16:02','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:16:02');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('de1be52d-3a79-4a52-a71f-f189af098dc4','人员上岗证过期提醒','MESSAGE','jobCertificate.sendMsg(''人员上岗证过期提醒'')','0 0/15 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:13:33','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:13:33');
INSERT INTO "TB_BASE_JOB"("id","jobName","jobGroup","invokeTarget","cronExpression","misfirePolicy","isConcurrent","status","remark","isDeleted","orgId","creator","createDate","domainId","modifier","modifyDate") VALUES('fa862b06-17eb-41bd-94e9-cbaa22f2acf1','仪器检定校准超期提醒','MESSAGE','instrumentCheck.sendMsg(''仪器检定校准超期提醒'')','0 0/15 * * * ?',3,0,1,'',0,'5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:14:23','5f7bcf90feb545968424b0a872863876','59141356591b48e18e139aa54d9dd351','2023-01-13 16:14:23');

