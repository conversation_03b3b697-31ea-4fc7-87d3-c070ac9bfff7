package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule;

import java.util.List;


/**
 * 报告组件配置
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
public interface ReportModuleService extends IBaseJpaService<DtoReportModule, String> {

    /**
     * 批量删除组件
     *
     * @param ids 组件id列表
     * @return 删除个数
     */
    int deleteModule(List<String> ids);
}