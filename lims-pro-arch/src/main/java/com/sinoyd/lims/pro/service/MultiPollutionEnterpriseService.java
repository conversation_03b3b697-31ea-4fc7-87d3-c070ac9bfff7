package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.dto.DtoProject2Customer;

import java.util.Collection;
import java.util.List;

/**
 * 重点污染源多企业
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
public interface MultiPollutionEnterpriseService {

    /**
     * 判断是否已经选择企业
     * @param projectIds 项目id
     * @throws Exception 错误内容
     */
    void judgmentEnterprise(List<String> projectIds) throws Exception;

    /**
     * 删除多企业污染源项目
     * @param ids 项目ids
     */
    void logicDeleteByIds(Collection<?> ids);

    /**
     * 添加企业
     *
     * @param project2CustomerList 企业信息
     */
    void addEnterprise(List<DtoProject2Customer> project2CustomerList);

    /**
     * 根据id删除企业
     * @param proEntIds ids
     */
    void deleteEnterprise(List<String> proEntIds);

    /**
     * 根据主项目拆分子项目
     * @param projectId 主项目id
     */
    void splitSubProject(String projectId);

    /**
     * 多企业污染源查询关联企业信息
     * @param pageBean 返回信息
     * @param criteria 查询条件
     */
    void findByPage(PageBean<DtoEnterprise> pageBean, BaseCriteria criteria);
}
