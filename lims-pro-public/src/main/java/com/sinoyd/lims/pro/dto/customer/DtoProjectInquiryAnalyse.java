package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.Date;

/**
 * 项目进度检测情况
 * <AUTHOR>
 * @version V1.0.0 2020/2/7
 * @since V100R001
 */
@Data
public class DtoProjectInquiryAnalyse {

    /**
     * 分析数据id
     */
    private String id;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 测试项目
     */
    private String redAnalyzeItemName;

    /**
     * 分析方法
     */
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    private String redCountryStandard;

    /**
     * 检测单id
     */
    private String workSheetFolderId;

    /**
     * 检测单编号
     */
    private String workSheetCode;

    /**
     * 分析人id
     */
    private String analystId;

    /**
     * 分析人
     */
    private String analystName;

    /**
     * 分析时间
     */
    private Date analyzeTime;

    /**
     * 检测单状态
     */
    private Integer workStatus;

    /**
     * 检测单状态
     */
    private String status;

    /**
     * 样品个数
     */
    private Integer sampleNum;

    public DtoProjectInquiryAnalyse() {

    }

    public DtoProjectInquiryAnalyse(String id, String testId, String redAnalyzeItemName, String redAnalyzeMethodName, String redCountryStandard,
                                    String analystId, String analystName) {
        this.id = id;
        this.testId = testId;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.analystId = analystId;
        this.analystName = analystName;
    }

    public DtoProjectInquiryAnalyse(String id, String testId, String redAnalyzeItemName, String redAnalyzeMethodName, String redCountryStandard,
                                    String workSheetFolderId, String workSheetCode,String analystId, String analystName,Date analyzeTime,Integer workStatus,String status) {
        this.id = id;
        this.testId = testId;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.workSheetFolderId = workSheetFolderId;
        this.workSheetCode = workSheetCode;
        this.analystId = analystId;
        this.analystName = analystName;
        this.analyzeTime = analyzeTime;
        this.workStatus = workStatus;
        this.status = status;
    }
}
