package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * WorkSheetFolder数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface WorkSheetFolderRepository extends IBaseJpaRepository<DtoWorkSheetFolder, String> {

    /**
     * 获取检测单
     *
     * @param ids
     * @return
     */
    List<DtoWorkSheetFolder> findByIdIn(List<String> ids);

    /**
     * 通过人员和方法获取检测单
     * @param analystId 人员id
     * @param methodId 方法id
     * @return 检测单
     */
    List<DtoWorkSheetFolder> findByAnalystIdAndAnalyzeMethodIdOrderByAnalyzeTimeDesc(String analystId,String methodId);

    /**
     * 根据检测单相关的状态数据
     *
     * @param ids         检测单ids
     * @param status      数据状态
     * @param workStatus  数据状态
     * @param auditorId   审核人员
     * @param auditorName 审核人员姓名
     * @param modifier 修改人
     * @param modifyDate 修改日期
     * @return 返回更新数据行
     */
    @Transactional
    @Modifying
    @Query("update DtoWorkSheetFolder a set a.status= :status,a.workStatus= :workStatus,a.auditorId= :auditorId,a.auditorName= :auditorName,a.modifier=:modifier,a.modifyDate=:modifyDate,a.backOpinion=:opinion where a.id in :ids")
    Integer updateStatus(@Param("ids") List<String> ids, @Param("status") String status, @Param("workStatus") Integer workStatus,
                         @Param("auditorId") String auditorId, @Param("auditorName") String auditorName, @Param("modifier") String modifier,
                         @Param("modifyDate") Date modifyDate, @Param("opinion") String opinion);

    /**
     * 根据检测单相关的状态数据
     *
     * @param ids         检测单ids
     * @param status      数据状态
     * @param workStatus  数据状态
     * @param auditorId   审核人员
     * @param auditorName 审核人员姓名
     * @param checkDate 复核日期
     * @param modifier 修改人
     * @param modifyDate 修改日期
     * @return 返回更新数据行
     */
    @Transactional
    @Modifying
    @Query("update DtoWorkSheetFolder a set a.status= :status,a.checkDate=:checkDate,a.workStatus= :workStatus,a.auditorId= :auditorId,a.auditorName= :auditorName,a.modifier=:modifier,a.modifyDate=:modifyDate where a.id in :ids")
    Integer updateStatus(@Param("ids") List<String> ids, @Param("status") String status, @Param("workStatus") Integer workStatus,
                         @Param("auditorId") String auditorId, @Param("auditorName") String auditorName,@Param("checkDate") Date checkDate,
                         @Param("modifier") String modifier,
                         @Param("modifyDate") Date modifyDate);


    /**
     * 根据检测单相关的状态数据
     *
     * @param ids        检测单ids
     * @param status     数据状态
     * @param workStatus 数据状态
     * @param opinion 退回意见
     * @return 返回更新数据行
     */
    @Transactional
    @Modifying
    @Query("update DtoWorkSheetFolder a set a.status= :status,a.workStatus= :workStatus,a.modifier=:modifier,a.modifyDate=:modifyDate,a.backOpinion=:opinion where a.id in :ids")
    Integer updateStatus(@Param("ids") List<String> ids, @Param("status") String status,
                         @Param("workStatus") Integer workStatus,
                         @Param("modifier") String modifier,
                         @Param("modifyDate") Date modifyDate,
                         @Param("opinion") String opinion);

    /**
     * 根据检测单相关的状态数据
     *
     * @param ids        检测单ids
     * @param status     数据状态
     * @param workStatus 数据状态
     * @param opinion 退回意见
     * @return 返回更新数据行
     */
    @Transactional
    @Modifying
    @Query("update DtoWorkSheetFolder a set a.status= :status,a.workStatus= :workStatus,a.modifier=:modifier,a.modifyDate=:modifyDate,a.backOpinion=:opinion, a.analyzeTime = :analyzeTime where a.id in :ids")
    Integer updateStatus(@Param("ids") List<String> ids, @Param("status") String status,
                         @Param("workStatus") Integer workStatus,
                         @Param("modifier") String modifier,
                         @Param("modifyDate") Date modifyDate,
                         @Param("opinion") String opinion, @Param("analyzeTime") Date analyzeTime);

    /**
     * 根据检测单相关的状态数据
     *
     * @param ids        检测单ids
     * @param status     数据状态
     * @param workStatus 数据状态
     * @param auditDate 审核日期
     * @return 返回更新数据行
     */
    @Transactional
    @Modifying
    @Query("update DtoWorkSheetFolder a set a.status= :status,a.workStatus= :workStatus,a.auditDate=:auditDate,a.modifier=:modifier,a.modifyDate=:modifyDate where a.id in :ids")
    Integer updateStatus(@Param("ids") List<String> ids, @Param("status") String status,
                         @Param("workStatus") Integer workStatus,
                         @Param("auditDate") Date auditDate,
                         @Param("modifier") String modifier,
                         @Param("modifyDate") Date modifyDate);
}