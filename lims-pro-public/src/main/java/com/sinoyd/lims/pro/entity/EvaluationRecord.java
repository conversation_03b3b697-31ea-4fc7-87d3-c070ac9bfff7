package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.util.Date;

/**
 * EvaluationRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="EvaluationRecord")
 @Data
 public  class EvaluationRecord implements BaseEntity,Serializable {

   public EvaluationRecord() {
      this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
      this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

   /**
    * 主键id
    */
   @Id
   @Column(length = 50)
   private String id = UUIDHelper.NewID();

   /**
    *
    */
   @Column(length = 50, nullable = false)
   @ApiModelProperty("")
   private String objectId;

   /**
    * 类型（枚举EnumEvaluationType：1、项目 2、点位 3、样品）
    */
   @Column(nullable = false)
   @ApiModelProperty("类型（枚举EnumEvaluationType：1、项目 2、点位 3、样品）")
   private Integer objectType;

   /**
    * 点位计划（枚举EnumEvaluationPlan：1、实际 2、计划）
    */
   @Column(nullable = false)
   @ColumnDefault("-1")
   @ApiModelProperty("点位计划（枚举EnumEvaluationPlan：1、实际 2、计划）")
   private Integer folderPlan;

   /**
    * 评价标准id
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("评价标准id")
   private String evaluationId;

   /**
    * 评价等级id
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("评价等级id")
   private String evaluationLevelId;

   /**
    * 测试项目id
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("测试项目id")
   private String testId;

   /**
    * 上限运算符
    */
   @Column(length = 50)
   @ApiModelProperty("上限运算符")
   private String upperLimitSymble;

   /**
    * 下限运算符
    */
   @Column(length = 50)
   @ApiModelProperty("下限运算符")
   private String lowerLimitSymble;

   /**
    * 上限
    */
   @Column(length = 50)
   @ApiModelProperty("上限")
   private String upperLimitValue;

   /**
    * 下限
    */
   @Column(length = 50)
   @ApiModelProperty("下限")
   private String lowerLimitValue;

   /**
    * 量纲id
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("量纲id")
   private String dimensionId;

   /**
    * 组织机构id
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("组织机构id")
   private String orgId;

   /**
    * 创建人
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @CreatedBy
   @ApiModelProperty("创建人")
   private String creator;

   /**
    * 创建时间
    */
   @Column(nullable = false)
   @ColumnDefault("getdate")
   @CreatedDate
   @ApiModelProperty("创建时间")
   private Date createDate;

   /**
    * 所属实验室
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("所属实验室")
   private String domainId;

   /**
    * 修改人
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @LastModifiedBy
   @ApiModelProperty("修改人")
   private String modifier;

   /**
    * 修改时间
    */
   @Column(nullable = false)
   @ColumnDefault("getdate")
   @LastModifiedDate
   @ApiModelProperty("修改时间")
   private Date modifyDate;

   /**
    * 假删
    */
   @Column(nullable = false)
   @ColumnDefault("0")
   @ApiModelProperty("假删")
   private Boolean isDeleted = false;

  /**
   * 排放速率
   */
  @Column(length = 10)
  @ApiModelProperty("排放速率")
  private String emissionRate;

  /**
   * 排放速率结果
   */
  @Column(length = 10)
  @ApiModelProperty("排放速率结果")
  private String emissionRateValue;
}