-- 开关
UPDATE t_sys_dict_data_project
set note = '一般结合“方案变更”审核流程一起使用，如果不允许随意修改方案，且修改方案必须走“方案变更”审核流程，那么原先方案上及样品登记页面上"修改方案"按钮就必须隐藏掉，用于权限控制。项目上除非特殊要求，一般默认开启，允许在项目登记修改方案。'
where id = '226e3b9e799d450b85cfac34d85cb1d8';

UPDATE t_sys_dict_data_project
set dictName = '实验室分析是否隐藏客户数据',
    note     = '主要针对实验室相关模块，是否在样品分配，实验室分析的过程中，允许分析人员看到”受检方、点位名称“等与客户相关的敏感信息。项目上从规范的角度，一般默认隐藏，客户有特殊要求的时候，再开启。'
where id = '30d1e050dff84dcb8a3cc24da1926ae5';

UPDATE t_sys_dict_data_project
set dictName = '是否人脸识别（移动端）',
    note     = '应用于移动端，在登陆系统、点位签到/修改等功能的时候，需要进行人脸识别；项目上一般默认关闭即可。'
where id = '31e5d3cdd66843f58408f49f5fefec9a';


UPDATE t_sys_dict_data_project
set dictName = '送样单是否自动流转到样品交接',
    note     = '用于控制采样准备以后，现场任务和样品交接并行，还是只能送样单提交后，才能流转至样品交接，用于流程控制；项目上一般默认开启，支持流程并行，不然容易卡流程。'
where id = '8101197875e847d69fe04c50db8c71ca';

UPDATE t_sys_dict_data_project
set dictName = '检测单中是否显示历史数据',
    note     = '用于控制“实验室分析”检测单中，“历史数据”选项卡是否显示，政府项目，一般默认开启，第三方项目，一般默认关闭。
功能说明：一般用于统计环境质量、污染源点位管理中生成方案的点位，用于统计历史最大值、最小值、均值，并判断是否有异常数据，用于提醒；'
where id = '819b6429242640c29a4a2d07eb4ce644';

UPDATE t_sys_dict_data_project
set dictName = '表单科学计数法显示方式',
    note     = '应用于表单（暂时仅支持excel）导出数据的时候，显示形式是否将“E”的数据转换为科学技术法显示（例如：1.2E+3, 1.2E3 转为 1.2 x 10³）,项目一般默认关闭。'
where id = '8361c18432734f44998b7fa1efb99edd';

UPDATE t_sys_dict_data_project
set dictName = '检测单提交是否强制验证上岗证',
    note     = '用于验证，实验室分析提交的时候，分析人员是否有持有上岗证，如果没有上岗证，是否强制提交至持证人员那里进行校核；项目上一般默认关闭。'
where id = 'a34ae5e5d31d49f0ba4e5b80b4042e7b';

UPDATE t_sys_dict_data_project
set note = '用于定义样品交接后，样品领取的方式。项目上一般默认关闭。
开启：样品交接以后，采用”按岗位领取“的方式，一般应用关于第三方项目；
关闭：样品交接以后，采用”样品分配“的方式，一般应用于政府项目；'
where id = 'b3273990039646bd88c91d42ca179953';

UPDATE t_sys_dict_data_project
set dictName = '检测单-质控评价是否显示现场平行样',
    note     = '用于控制实验室分析-质控评价中，是否显示“现场平行样”的数据，项目上一般默认关闭。'
where id = 'b5f347774aed4acb99bf91fc5a800418';

UPDATE t_sys_dict_data_project
set dictName = '是否跳过样品分配流程',
    note     = '用于样品交接提交后，是否自动跳过“样品分配”模块，如果自动跳过，能够减少样品分配人的工作。项目上一般默认关闭。'
where id = 'b85c35a938544177aacdb54068f5b8d1';

-- 参数表增加备注字段长度
ALTER TABLE t_sys_config MODIFY note VARCHAR (500);
-- 参数设置
update t_sys_config
set configName = '退出登录跳转地址',
    note       = '用户退出登录的时候，默认跳转的页面，一般应用于有“综合门户”的项目。'
where id = 'fb0f578747e94cf8a644c9b657fa1328';

update t_sys_config
set note = '目前仅应用于常州项目，用于不同机构登陆后调用不同的首页。项目初始化默认为空。'
where id = 'b5d2358c608a4de3a63d76e0e9bb0049';

update t_sys_config
set note = '用于初始化密码用户密码，初始密码不会进行复杂度验证；'
where id = '1f5d596bb31a4750a0d5b3b8d91ba863';

update t_sys_config
set note = '小于检出限计算，一般应用于两类：带入偏差计算（qualityControl）、带入浓度均值计算（sample）。这两个需要分别配置。
①带入偏差计算（qualityControl）：一般应用于“曲线校核样、校正系数检验”偏差计算，小于检出限带入的结果。一般默认取“0”;
②带入浓度均值计算（sample）：一般用于平行样的出证结果、报告中点位均值、批次均值的时候，小于检出限带入的结果。一般默认取“检出限一半”。'
where id = '0c9b50fc348c4beaa0ab24161a80cc6c';

update t_sys_config
set configName = '标签二维码大小配置',
    note       = '应用于样品标签，左上角二维码大小的设置；
+ picWidth：宽度
+ picHeight：高度
+ picSize：二维码存储内容大小，一般值越大越好，用于控制二维码本身四周的白边大小
+ picLeft：左边距
+ picTop：上边距'
where id = '0d107c18921849ca8a543c83745e6bfe';

update t_sys_config
set configName = '加标样样值计算是否考虑实验室平行',
    note       = '实验室分析，原样同时添加了“室内平行样”和“加标样”，用于控制“加标样样值”计算是否与平行样求均值，项目一般默认开启；'
where id = '117ca63d2068405f89da5bfdf4ffc0bb';

update t_sys_config
set configName = '检测单提交判断是否存在记录单',
    note       = '实验室分析提交，是否强制判定原始记录单是否存在。项目上一般默认false，正常只有半绑的项目，然后强制要求将扫描件上传至系统的情况，会配置成true。'
where id = '3605215632934c3b8842a681ba9afee0';

update t_sys_config
set note = '设置移动端是否强制更新至最新版本。项目上一般默认关闭；'
where id = '419270a31faa4b36ba26791bdc708ced';

update t_sys_config
set note = '用于计算样品是否过期，计算的时候，优先获取测试项目上配置的“样品有效期（h）”字段中配置的内容，如果未配置，再调用这里配置的时长；'
where id = '6143410359eb440e83ec4cd280f89e9f';

update t_sys_config
set note = '系统中，除了编制报告模块，其余（例如：现场任务-采样单、实验室分析-原始记录单）默认在线编辑打开方式。考虑iWeboffice系统兼容性，以及国产化要求，项目上一般**默认使用luckysheet。'
where id = '7d9d47c5a63f46d791e71cdfcccd08e2';

update t_sys_config
set configName = '现场平行是否参与实验室出证计算',
    note       = '用于控制“实验室分析”检测单中，计算出证结果的时候，现场平行样是否带入最终出证结果的计算，项目上一般默认关闭。'
where id = 'c50d718b664a4feca4d1e828b9ea51e9';

update t_sys_config
set note = '用于设置移动端网关访问地址，用于移动端与平台的数据通信；项目上需要按实际路径进行初始化设置；'
where id = 'c7fe0b1742364ba1a9cb1387a685a861';

update t_sys_config
set note = '用于控制登陆界面，是否显示下载移动端的二维码，项目一般默认关闭，有移动端的项目可以开启。'
where id = 'ee5f4f424ff44bdebcd9bc6bb29dbae0';

update t_sys_config
set note = '报告中，有关“流量信息”的固定描述，在“编制报告-修改报告信息”中通过“自动生成”功能进行获取，并同步生成到报告对应位置。项目上一般默认为空。'
where id = '1960acd5-176c-4e4c-9a74-451716a4424c';

update t_sys_config
set note = '报告中，有关“工况信息”的固定描述，在“编制报告-修改报告信息”中通过“自动生成”功能进行获取，并同步生成到报告对应位置。项目上一般默认为空。'
where id = 'fbc701e7-a124-45b9-a903-33db4d6db719';

update t_sys_config
set note = '应用于编制报告-电子报告-报告基本信息中，能够配置的内容初始化到“技术说明备注”中。'
where id = '554103f7-cb14-43a9-bc03-9e8931f6972d';

-- 删除作废开关
DELETE
from t_sys_dict_data_project
where id in ('8905ba5257794791a3cf732f0d6e97a5', '05196ae1cf304e05806f429832475126');
DELETE
from t_sys_config
where id in (
             '57ec10cae07741cba922e0cbc260b4a9',
             'fb197777b71f4911b7ad1e89f71d2fee'
    );
