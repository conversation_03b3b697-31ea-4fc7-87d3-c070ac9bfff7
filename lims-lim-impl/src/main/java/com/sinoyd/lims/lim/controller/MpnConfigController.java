package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.MpnConfigCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoMpnConfig;
import com.sinoyd.lims.lim.service.MpnConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Mnp配置接口服务
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Api(tags = "Mnp配置接口服务")
@RestController
@RequestMapping("api/lim/mpnConfig")
@Validated
public class MpnConfigController extends BaseJpaController<DtoMpnConfig, String, MpnConfigService> {
    /**
     * 分页动态条件查询mpnConfig
     *
     * @param mpnConfigCriteria 条件参数
     * @return RestResponse<List < mpnConfig>>
     */
    @ApiOperation(value = "分页动态条件查询", notes = "分页动态条件查询")
    @GetMapping
    public RestResponse<List<DtoMpnConfig>> findByPage(MpnConfigCriteria mpnConfigCriteria) {
        PageBean<DtoMpnConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoMpnConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, mpnConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询Mnp配置
     *
     * @param id
     * @return 计划实体
     */
    @ApiOperation(value = "根据id查询Mnp配置", notes = "根据id查询Mnp配置")
    @GetMapping("/{id}")
    public RestResponse<DtoMpnConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoMpnConfig> restResp = new RestResponse<>();
        DtoMpnConfig entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增Mnp配置
     *
     * @param mpnConfig Mnp配置实体
     * @return 新增的Mnp配置实体
     */
    @ApiOperation(value = "新增Mnp配置", notes = "新增Mnp配置")
    @PostMapping("")
    public RestResponse<DtoMpnConfig> create(@Validated @RequestBody DtoMpnConfig mpnConfig) {
        RestResponse<DtoMpnConfig> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoMpnConfig data = service.save(mpnConfig);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }


    /**
     * 更新Mnp配置
     *
     * @param mpnConfig Mnp配置实体
     * @return 更新后的Mnp配置实体
     */
    @ApiOperation(value = "更新Mnp配置", notes = "更新Mnp配置")
    @PutMapping("")
    public RestResponse<DtoMpnConfig> update(@Validated @RequestBody DtoMpnConfig mpnConfig) {
        RestResponse<DtoMpnConfig> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoMpnConfig data = service.update(mpnConfig);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
