package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.lims.lim.enums.EnumLIM;
import groovy.transform.EqualsAndHashCode;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 检测统计查询条件
 * <AUTHOR>
 * @version V1.0.0 2022年7月8日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestStatisticsCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 分析项目
     */
    private String analyzeItem;

    /**
     * 分析人员
     */
    private String analyzePerson;

    /**
     * 项目类型
     */
    private String projectTypeId;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 数据状态
     */
    private String status;

    /**
     * 岗位id
     */
    private String testPostId;

    /**
     * 分析人员s
     */
    private String analyzePersons;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
//        condition.append(" and a.isDeleted = 0");
//        condition.append(" and a.sampleId = s.id and s.receiveId = rs.id and s.receiveId = rss.receiveId");
//        condition.append(" and rs.status <> :status and rss.code like '%FX%' and a.isCompleteField = 0");
//        values.put("status", EnumLIM.EnumReceiveRecordStatus.新建.name());
        if (StringUtil.isNotEmpty(this.sampleCode)) {
            condition.append(" and a.sampleCode like :sampleCode");
            values.put("sampleCode", "%" + this.sampleCode + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeItem)) {
            condition.append(" and a.redAnalyzeItemName like :analyzeItem");
            values.put("analyzeItem", "%" + this.analyzeItem + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleTypeId)) {
            condition.append(" and a.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        if (StringUtil.isNotEmpty(this.analyzePerson)) {
            condition.append(" and a.analystId = :analyzePerson");
            values.put("analyzePerson", this.analyzePerson);
        }
        if (StringUtil.isNotEmpty(this.beginTime)) {
            Date from = DateUtil.stringToDate(this.beginTime, DateUtil.YEAR);
            condition.append(" and a.receiveSampleDate >= :beginTime");
            values.put("beginTime", from);
        }
        //人员
        if (StringUtils.isNotNullAndEmpty(this.analyzePersons)) {
            String replace = analyzePersons.replace("，", ",");
            List<String> analyzePersonsList = Arrays.asList(replace.split(","));
            condition.append(" and a.analystId in :analyzePersons");
            values.put("analyzePersons", analyzePersonsList);
        } else {
            //岗位
            if (StringUtils.isNotNullAndEmpty(this.testPostId)) {
                //当选择岗位而不选择人员时查询此岗位下的所有人员
                condition.append(" and a.analystId in (select pp.personId from DtoTestPost2Person pp where pp.testPostId = :testPostId)");
                values.put("testPostId", this.testPostId);
            }
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            to = c.getTime();
            condition.append(" and a.receiveSampleDate < :endTime");
            values.put("endTime", to);
        }
        if (StringUtil.isNotEmpty(this.status)) {
            condition.append(" and a.analyseDataStatus = :status");
            values.put("status", this.status);
        }
        if (StringUtil.isNotEmpty(this.projectTypeId)) {
            condition.append(" and a.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        }
        return condition.toString();
    }
}
