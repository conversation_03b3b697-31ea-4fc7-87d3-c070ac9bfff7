CREATE  TRIGGER "AFTER_UPDATE_ANALYZEITEM"
    AFTER  UPDATE
    ON "TB_BASE_ANALYZEITEM"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    IF new.isDeleted = 1 THEN
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
        new.domainId);
else
        if new.analyzeItemName != old.analyzeItemName or
           (new.analyzeItemName is null and old.analyzeItemName is not null) or
           (new.analyzeItemName is not null and old.analyzeItemName is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'analyzeItemName',
        old.analyzeItemName, new.analyzeItemName, new.orgId, new.domainId);
END IF;
if
                new.analyzeItemCode != old.analyzeItemCode or
                (new.analyzeItemCode is null and old.analyzeItemCode is not null) or
                (new.analyzeItemCode is not null and old.analyzeItemCode is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'analyzeItemCode',
        old.analyzeItemCode, new.analyzeItemCode, new.orgId, new.domainId);
END IF;
if
                new.variableName != old.variableName or (new.variableName is null and old.variableName is not null) or
                (new.variableName is not null and old.variableName is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'variableName',
        old.variableName, new.variableName, new.orgId, new.domainId);
END IF;
if
                new.pinYin != old.pinYin or (new.pinYin is null and old.pinYin is not null) or
                (new.pinYin is not null and old.pinYin is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'pinYin', old.pinYin,
        new.pinYin, new.orgId, new.domainId);
END IF;
if
                new.fullPinYin != old.fullPinYin or (new.fullPinYin is null and old.fullPinYin is not null) or
                (new.fullPinYin is not null and old.fullPinYin is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'fullPinYin', old.fullPinYin,
        new.fullPinYin, new.orgId, new.domainId);
END IF;
if
                new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
                (new.orderNum is not null and old.orderNum is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'orderNum', old.orderNum,
        new.orderNum, new.orgId, new.domainId);
END IF;
if
                new.casNum != old.casNum or (new.casNum is null and old.casNum is not null) or
                (new.casNum is not null and old.casNum is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_base_analyzeitem', new.id, new.modifier, new.modifyDate, 3, 'casNum', old.casNum,
        new.casNum, new.orgId, new.domainId);
END IF;
END IF;
end;
