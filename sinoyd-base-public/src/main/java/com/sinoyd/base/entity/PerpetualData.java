package com.sinoyd.base.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * 永久性数据存储实体
 * <AUTHOR>
 * @version V5.2.0
 * @since 2023/12/25
 */
@MappedSuperclass
@ApiModel(description="PerpetualData")
@Data
@EntityListeners(AuditingEntityListener.class)
public class PerpetualData implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 数据类型
     */
    @Column
    @ApiModelProperty("数据类型")
    @Length(message = "数据类型{validation.message.length}", max = 255)
    private String type;

    /**
     * 数据值
     */
    @Column
    @ApiModelProperty("数据值")
    @Length(message = "数据值{validation.message.length}", max = 255)
    private String value;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
