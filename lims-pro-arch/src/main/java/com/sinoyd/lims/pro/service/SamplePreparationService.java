package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSamplePreparation;
import com.sinoyd.lims.pro.dto.customer.DtoSampleOfPrepared;

import java.util.List;

/**
 * 样品制备操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/17
 * @since V100R001
 */
public interface SamplePreparationService extends IBaseJpaService<DtoSamplePreparation, String> {

    /**
     * 分页查询样品
     *
     * @param pageBean 分页数据
     * @param criteria 查询条件
     */
    void findSamplesByPage(PageBean<DtoSample> pageBean, BaseCriteria criteria);

    /**
     * 批量样品制备
     *
     * @param samplePreparations 样品制备数据
     * @return 已添加的样品制备数据
     */
    List<DtoSamplePreparation> batchSave(List<DtoSamplePreparation> samplePreparations);

    /**
     * 样品制备
     *
     * @param sampleId           需要制备的样品id
     * @param samplePreparations 样品制备数据
     * @return 已制备的样品数据
     */
    List<DtoSamplePreparation> save(String sampleId, List<DtoSamplePreparation> samplePreparations);

    /**
     * 样品制备完成
     *
     * @param sampleIds 需要制备完成的样品id
     */
    void preparationSample(List<String> sampleIds);

    /**
     * 查询样品的详细信息以及样品制备信息
     *
     * @param sampleId 样品id
     * @return 样品详细信息
     */
    DtoSampleOfPrepared findDetails(String sampleId);

    /**
     * 退回
     *
     * @param sampleIds 需要退回的样品id
     */
    void backPreparation(List<String> sampleIds);
}
