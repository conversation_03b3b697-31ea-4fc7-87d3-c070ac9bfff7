DROP TABLE IF EXISTS tb_pro_fileAudit;
CREATE TABLE tb_pro_fileAudit (
                                  id varchar(50) NOT NULL COMMENT '主键',
                                  title varchar(500) NOT NULL DEFAULT '' COMMENT '名称',
                                  type int(10) NOT NULL DEFAULT '-1' COMMENT '申请类型，关联枚举EnumFileAuditApplyType',
                                  folderId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '文件目录',
                                  description varchar(2000) DEFAULT NULL COMMENT '内容描述',
                                  documentIds varchar(2000) DEFAULT NULL COMMENT '文件标识',
                                  fileName varchar(4000) DEFAULT NULL COMMENT '文件名称',
                                  isSetValidDate bit(1) NOT NULL DEFAULT b'0' COMMENT '是否设定启用日期',
                                  validDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '启用日期',
                                  issueRange varchar(500) DEFAULT NULL COMMENT '发放范围',
                                  reviseDeptId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修编部门标识',
                                  revisePersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修编人标识',
                                  auditPeople varchar(2000) DEFAULT NULL COMMENT '参与评审人员，多个guid英文逗号拼接',
                                  techLeaderId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '技术或质量负责人',
                                  approvePersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '批准人员',
                                  receivePeople varchar(2000) DEFAULT NULL COMMENT '接收人员，多个guid英文逗号拼接',
                                  registerPersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '备案人员',
                                  stepCode varchar(50) NOT NULL COMMENT '模块编码（枚举EnumFileAuditStep）',
                                  stepStatus int(10) NOT NULL DEFAULT '-1' COMMENT '状态，关联枚举EnumFileAuditStatus',
                                  creator varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '发起人',
                                  createDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '创建时间',
                                  startDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '发起时间',
                                  passDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '通过时间',
                                  PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='文件审核记录';

DROP TABLE IF EXISTS tb_pro_fileAuditStatus;
CREATE TABLE tb_pro_fileAuditStatus (
                                        id varchar(50) NOT NULL COMMENT '主键',
                                        fileAuditId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '文件审核标识',
                                        stepCode varchar(50) NOT NULL COMMENT '模块编码（枚举EnumFileAuditStep）',
                                        isHandle bit(1) NOT NULL DEFAULT b'0' COMMENT '是否处理',
                                        isPass bit(1) NOT NULL DEFAULT b'0' COMMENT '是否通过',
                                        currentPersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '当前操作人Id',
                                        lastNewOpinion mediumtext COMMENT '最新一条意见',
                                        lastOperateDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '最新一次操作时间',
                                        PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='文件审核处理状态';