package com.sinoyd.lims.lim.service.homeTaskNumber;

import com.jsoniter.output.JsonStream;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.lim.enums.EnumLIM;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 首页办公管理代办数字缓存刷新
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/10/26
 */
@Component
public class OfficeManageTask extends AbsTaskNumber {

    private CommonRepository commonRepository;

    private RedisTemplate redisTemplate;

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    private UserService userService;

    /**
     * 刷新缓存
     *
     * @param homeModule 需要刷新的模块
     */
    @Override
    public void refreshRedis(HomeModule homeModule) {
        List<DtoUser> userList = userService.findAll();
        Map<String, List<DtoUser>> userListGroup = userList.stream().collect(Collectors.groupingBy(DtoUser::getLoginId));
        //组织id
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        //获取所有需要刷新缓存的模块
        List<String> officeModules = Arrays.asList(EnumLIM.EnumHomeTaskModule.我已发起.getValue(),
                EnumLIM.EnumHomeTaskModule.待我审批.getValue(),
                EnumLIM.EnumHomeTaskModule.我已审批.getValue());
        //循环刷新
        for (String officeModule : officeModules) {
            List<DtoTaskNum> oaTaskPersonList = getOATaskPersonList(officeModule);
            if (StringUtil.isNotEmpty(oaTaskPersonList)) {
                for (DtoTaskNum taskNum : oaTaskPersonList) {
                    if (officeModule.equals(EnumLIM.EnumHomeTaskModule.我已审批.getValue())) {
                        String userId = "";
                        List<DtoUser> dtoUsers = userListGroup.get(taskNum.getUserId());
                        if (StringUtil.isNotEmpty(dtoUsers)){
                            Optional<DtoUser> user = dtoUsers.stream().findFirst();
                            if (user.isPresent()) {
                                userId = user.get().getId();
                            }
                        }
                        List<Map<String, Object>> oaTaskList = getOATaskList(officeModule, taskNum.getUserId(), taskNum.getUserId());
                        this.saveRedis(oaTaskList, userId, orgId, officeModule);
                    } else {
                        List<Map<String, Object>> oaTaskList = getOATaskList(officeModule, taskNum.getUserId(), taskNum.getUserId());
                        this.saveRedis(oaTaskList, taskNum.getUserId(), orgId, officeModule);
                    }
                }
            }else{
                List<String> userIds = StringUtil.isNotEmpty(userList) ? userList.stream().map(DtoUser::getId).collect(Collectors.toList()) : new ArrayList<>();
                for (String userId : userIds) {
                    this.saveRedis(null, userId, orgId, officeModule);
                }
            }
        }
    }

    @Override
    public List<DtoTaskNum> getTaskNum(HomeModule homeModule, String orgId, String userId, List<String> outTypeIds) {
        return null;
    }

    /**
     * 保存redis数据
     *
     * @param oaTaskList 需要爆粗你的数据
     * @param userId     当前用户id
     * @param orgId      组织id
     * @param moduleCode 模块编码
     */
    private void saveRedis(List<Map<String, Object>> oaTaskList, String userId, String orgId, String moduleCode) {

        Map<String, Object> map = new HashMap<>();
        if (StringUtil.isNotEmpty(oaTaskList)) {
            map.put("totalCount", oaTaskList.size());
            map.put("data", oaTaskList);
        }else{
            map.put("totalCount", BigDecimal.ZERO);
            map.put("data", null);
        }
        //获取redisKey
        String redisKey = String.format("PRO:%s:%s:%s", orgId, userId, moduleCode);
        //保存redis数据
        redisTemplate.opsForValue().set(redisKey, JsonStream.serialize(map));

    }

    /**
     * 获取每个审批模块的数据
     *
     * @param officeModule 模块编码
     * @return 查询结果
     */
    private List<DtoTaskNum> getOATaskPersonList(String officeModule) {
        if (officeModule.equals(EnumLIM.EnumHomeTaskModule.我已发起.getValue())) {
            String sql = "select a.sponsorId as userId,count(a.id) as count from TB_PRO_OATask a where 1=1 and a.dataStatus not in (1,4) group by a.sponsorId";
            return jdbcTemplate.query(sql,
                    new String[]{},
                    (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"), resultSet.getLong("count")));

        } else if (officeModule.equals(EnumLIM.EnumHomeTaskModule.待我审批.getValue())) {
            String sql = "select a.currentAssigneeId as userId,count(a.id) as count from TB_PRO_OATask a where 1=1 group by a.currentAssigneeId";
            return jdbcTemplate.query(sql,
                    new String[]{},
                    (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"), resultSet.getLong("count")));
        }
        if (officeModule.equals(EnumLIM.EnumHomeTaskModule.我已审批.getValue())) {
            String sql = "select b.assignee as userId,count(a.id) as count from TB_PRO_OATask a,TB_PRO_OATaskHandleLog b where 1=1 and a.id = b.taskId" +
                    " and b.isFirstStep = 0 group by b.assignee";
            return jdbcTemplate.query(sql,
                    new String[]{},
                    (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"), resultSet.getLong("count")));
        }
        return null;
    }

    /**
     * 获取每个审批模块的数据
     *
     * @param officeModule 模块编码
     * @param userId       当前用户id
     * @param loginId      当前登录id
     * @return 查询结果
     */
    private List<Map<String, Object>> getOATaskList(String officeModule, String userId, String loginId) {
        Map<String, Object> value = new HashMap<>();
        value.put("userId", userId);
        if (officeModule.equals(EnumLIM.EnumHomeTaskModule.我已发起.getValue())) {
            String sql = "select a from DtoOATask a where 1=1 and a.dataStatus not in (1,4) and sponsorId = :userId";
            List<Map<String, Object>> list = commonRepository.find(sql, value);
            return list;

        } else if (officeModule.equals(EnumLIM.EnumHomeTaskModule.待我审批.getValue())) {
            String sql = "select a from DtoOATask a where 1=1 and currentAssigneeId = :userId";
            List<Map<String, Object>> list = commonRepository.find(sql, value);
            return list;
        }
        if (officeModule.equals(EnumLIM.EnumHomeTaskModule.我已审批.getValue())) {
            value.clear();
            String sql = "select a from DtoOATask a,DtoOATaskHandleLog b where 1=1 and a.id = b.taskId" +
                    " and b.isFirstStep = 0 and b.assignee = :assignee";
            value.put("assignee", loginId);
            List<Map<String, Object>> list = commonRepository.find(sql, value);
            return list;
        }
        return null;
    }

    /**
     * 获取模块编码
     *
     * @return 模块编码
     */
    @Override
    public String getModuleCode() {
        return EnumLIM.EnumHomeTaskModule.我的审批.getValue();
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
