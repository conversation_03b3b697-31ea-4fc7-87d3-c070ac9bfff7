package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 环境管理
 * <AUTHOR> 修改：xuxb
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnvironmentalCriteria extends BaseCriteria {


    /**
     *  关键字（实验室名称、实验室编号）
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (labName like :key or labCode like :key)");
            values.put("key", "%" + this.key + "%");
        }
        condition.append(" and isDeleted != 1");
        return condition.toString();
    }
}