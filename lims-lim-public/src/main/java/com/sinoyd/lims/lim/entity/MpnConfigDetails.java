package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * MpnConfigDetails实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/2/12
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "MpnConfigDetails")
@Data
@EntityListeners(AuditingEntityListener.class)
public class MpnConfigDetails implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * MNP配置ID
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("MNP配置ID")
    @Length(message = "MNP配置ID{validation.message.length}", max = 50)
    private String mpnConfigId;



    /**
     * 参数1值
     */
    @Column(length = 50)
    @ApiModelProperty("参数1值")
    @Length(message = "参数1值{validation.message.length}", max = 50)
    private String param1Value;



    /**
     * 参数2值
     */
    @Column(length = 50)
    @ApiModelProperty("参数2值")
    @Length(message = "参数2值{validation.message.length}", max = 50)
    private String param2Value;


    /**
     * 参数3值
     */
    @Column(length = 50)
    @ApiModelProperty("参数3值")
    @Length(message = "参数3值{validation.message.length}", max = 50)
    private String param3Value;



    /**
     * 结果值
     */
    @Column(length = 50)
    @ApiModelProperty("结果值")
    @Length(message = "结果值{validation.message.length}", max = 50)
    private String resultParamValue;

    /**
     * 95%置信下限
     */
    @Column(length = 50)
    @ApiModelProperty("95%置信下限")
    @Length(message = "95%置信下限{validation.message.length}", max = 50)
    private String confidenceLow;

    /**
     * 95%置信上限
     */
    @Column(length = 50)
    @ApiModelProperty("95%置信上限")
    @Length(message = "95%置信上限{validation.message.length}", max = 50)
    private String confidenceUp;

    /**
     *排序值
     */
    @Column
    @ApiModelProperty("排序值")
    private Integer orderNum;
}
