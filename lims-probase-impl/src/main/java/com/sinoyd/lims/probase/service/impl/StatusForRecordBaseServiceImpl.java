package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.probase.repository.ProjectBaseRepository;
import com.sinoyd.lims.probase.repository.ProjectPlanBaseRepository;
import com.sinoyd.lims.probase.repository.StatusForRecordBaseRepository;
import com.sinoyd.lims.probase.service.StatusForRecordBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StatusForRecordBaseServiceImpl  extends BaseJpaPhysicalDeleteServiceImpl<DtoStatusForRecord,String,StatusForRecordBaseRepository> implements StatusForRecordBaseService {

    @Autowired
    private ProjectTypeService projectTypeService;

    @Autowired
    private ProjectBaseRepository projectBaseRepository;

    @Autowired
    private ProjectPlanBaseRepository projectPlanBaseRepository;

    /**
     * 核对送样单状态
     *
     * @param record 送样单
     */
    @Override
    @Transactional
    public void checkInfoStatus(DtoReceiveSampleRecord record, DtoReceiveSubSampleRecord anaSub) {
        //读取该项目的所有状态数据并转为map
        List<DtoStatusForRecord> statusList = repository.findByReceiveId(record.getId());
        Map<String, DtoStatusForRecord> statusMap = statusList.stream().collect(Collectors.toMap(DtoStatusForRecord::getModule, status -> status));

        DtoProject project = projectBaseRepository.findOne(record.getProjectId());
        if (StringUtil.isNotNull(project)) {
            EnumPRO.EnumProjectStatus projectStatus = EnumPRO.EnumProjectStatus.getByName(project.getStatus());
            String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
            if (projectTypeCode.equals(EnumPRO.EnumProjectType.委托类.getValue()) ||
                    projectTypeCode.equals(EnumPRO.EnumProjectType.现场类.getValue()) ||
                    EnumPRO.EnumProjectType.全流程.getValue().equals(projectTypeCode) ||
                    EnumPRO.EnumProjectType.例行类.getValue().equals(projectTypeCode)) {
                //需根据信息状态进行核对
                if (record.getInfoStatus() < EnumPRO.EnumReceiveInfoStatus.信息登记中.getValue()) {
                    //需删除
                    this.delete(statusMap, EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue(),
                            EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue(),
                            EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue());
                } else if (record.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.信息登记中.getValue())) {
                    //需删除
                    this.delete(statusMap, EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue(),
                            EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue());

                    //需核对
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue(), EnumPRO.EnumStatus.待处理.getValue());
                } else if (record.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.信息复核中.getValue())) {
                    //需删除
                    this.delete(statusMap, EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue());

                    //需核对
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue(), EnumPRO.EnumStatus.已处理.getValue());
                    String personId = UUIDHelper.GUID_EMPTY;
                    String personName = "";
                    if (statusMap.containsKey(EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue())) {
                        DtoStatusForRecord sfr = statusMap.get(EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue());
                        personId = sfr.getNextPersonId();
                        personName = sfr.getNextPersonName();
                    }
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue(), EnumPRO.EnumStatus.待处理.getValue(), personId, personName);
                } else if (record.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.信息审核中.getValue())) {
                    //需核对
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue(), EnumPRO.EnumStatus.已处理.getValue());
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue(), EnumPRO.EnumStatus.已处理.getValue());
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue(), EnumPRO.EnumStatus.待处理.getValue());
                } else if (record.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.已确认.getValue())) {
                    //需核对
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue(), EnumPRO.EnumStatus.已处理.getValue());
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue(), EnumPRO.EnumStatus.已处理.getValue());
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue(), EnumPRO.EnumStatus.已处理.getValue());
                }

                if (StringUtil.isNotNull(anaSub) && StringUtil.isNotNull(projectStatus) &&
                        projectStatus.getValue() >= EnumPRO.EnumProjectStatus.开展中.getValue() && record.getInfoStatus() > EnumPRO.EnumReceiveInfoStatus.信息登记中.getValue()) {
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue(),
                            record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue()) ? EnumPRO.EnumStatus.待处理.getValue() : EnumPRO.EnumStatus.已处理.getValue());
                } else if (StringUtil.isNull(anaSub)) {
                    this.delete(statusMap, EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                }
            } else if (projectTypeCode.equals(EnumPRO.EnumProjectType.送样类.getValue())) {
                //外部送样类不存在现场状态，需全部移除
                this.delete(statusMap, EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue(),
                        EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue(),
                        EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue());

                if (StringUtil.isNotNull(anaSub) && StringUtil.isNotNull(projectStatus) &&
                        projectStatus.getValue() >= EnumPRO.EnumProjectStatus.开展中.getValue()) {
                    this.check(statusMap, record.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue(),
                            record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue()) ? EnumPRO.EnumStatus.待处理.getValue() : EnumPRO.EnumStatus.已处理.getValue());
                } else {
                    this.delete(statusMap, EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                }
            } else if (projectTypeCode.equals(EnumPRO.EnumProjectType.质控类.getValue())) {
                //质控项目删除所有的送样单状态
                DtoProjectPlan projectPlan = projectPlanBaseRepository.findByProjectId(project.getId());
                if (!(EnumPRO.EnumPorjectQCGrade.现场质控.getValue().equals(project.getQcGrade())
                        || (projectPlan != null && EnumPRO.EnumPorjectQCGrade.现场质控.getValue().equals(projectPlan.getQcGrade()))
                )) {
                    repository.deleteByReceiveId(record.getId());
                }
            }
        } else {
            //项目已被删除则删除所有的送样单状态
            repository.deleteByReceiveId(record.getId());
        }
    }

    private void check(Map<String, DtoStatusForRecord> statusMap, String receiveId, String module, Integer status) {
        if (statusMap.containsKey(module)) {
            DtoStatusForRecord sfr = statusMap.get(module);
            if (!sfr.getStatus().equals(status)) {
                sfr.setStatus(status);
                comRepository.merge(sfr);
            }
        } else {
            DtoStatusForRecord sfr = new DtoStatusForRecord();
            sfr.setReceiveId(receiveId);
            sfr.setModule(module);
            sfr.setStatus(status);
            sfr.setNextPersonId(UUIDHelper.GUID_EMPTY);
            sfr.setCurrentPersonId(UUIDHelper.GUID_EMPTY);
            repository.save(sfr);
        }
    }

    private void check(Map<String, DtoStatusForRecord> statusMap, String receiveId, String module, Integer status, String personId, String personName) {
        if (statusMap.containsKey(module)) {
            DtoStatusForRecord sfr = statusMap.get(module);
            if (!sfr.getStatus().equals(status)) {
                sfr.setStatus(status);
                comRepository.merge(sfr);
            }
        } else {
            DtoStatusForRecord sfr = new DtoStatusForRecord();
            sfr.setReceiveId(receiveId);
            sfr.setModule(module);
            sfr.setStatus(status);
            sfr.setNextPersonId(UUIDHelper.GUID_EMPTY);
            sfr.setCurrentPersonId(personId);
            sfr.setCurrentPersonName(personName);
            repository.save(sfr);
        }
    }

    private void delete(Map<String, DtoStatusForRecord> statusMap, String... modules) {
        for (String module : modules) {
            if (statusMap.containsKey(module)) {
                repository.delete(statusMap.get(module));
            }
        }
    }
}
