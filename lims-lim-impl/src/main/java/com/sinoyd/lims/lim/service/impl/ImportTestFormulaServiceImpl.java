package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.customer.DtoImportTestFormula;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.service.ImportTestFormulaService;
import com.sinoyd.lims.lim.verify.TestFormulaVerifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 测试项目公式导入
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/6
 * @since V100R001
 */
@Service
@Slf4j
public class ImportTestFormulaServiceImpl implements ImportTestFormulaService {
    /**
     *   正则表达式，用于从公式中提取参数
     */
    private static final Pattern MAP_PATTERN = Pattern.compile("(?<=\\[).*?(?=\\])");

    private TestFormulaVerifyHandler testFormulaVerifyHandler;

    private ParamsPartFormulaRepository paramsPartFormulaRepository;
    private ParamsTestFormulaRepository paramsTestFormulaRepository;
    private TestRepository testRepository;
    private SampleTypeRepository sampleTypeRepository;
    private ParamsRepository paramsRepository;
    private ParamsFormulaRepository paramsFormulaRepository;

    @Override
    @Transactional
    public void importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        //文件格式校验
        PoiExcelUtils.verifyFileType(file);

        //更新校验容器并获取excel导入结果集
        testFormulaVerifyHandler = new TestFormulaVerifyHandler();
        initHandleContainer(testFormulaVerifyHandler);
        ExcelImportResult<DtoImportTestFormula> importResult = getExcelData(file, response);
        clearPartContainer(testFormulaVerifyHandler);

        //校验正确数据
        List<DtoImportTestFormula> importList = importResult.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getSampleType()));
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        //多表导入 个性化
        saveRelatedData(importList);
    }

    /**
     * 数据拆解并保存
     * @param importList 导入数据列表
     */
    private void saveRelatedData(List<DtoImportTestFormula> importList){
        //所有待插入列表
        List<DtoParamsFormula> formulaList = new ArrayList<>();
        List<DtoParamsPartFormula> partFormulaList = new ArrayList<>();
        List<DtoParamsTestFormula> testFormulaList = new ArrayList<>();
        //所有测试项目
        List<DtoTest> testList = testRepository.findAll();
        //所有检测类型
        List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll();
        //所有参数
        List<DtoParams> paramsList = paramsRepository.findAllByIsDeletedFalse();
        for (DtoImportTestFormula importTestFormula:importList) {
            String paramsFormulaId = UUIDHelper.NewID();
            //测试项目公式
            collectParamsFormulaList(importTestFormula,formulaList,paramsFormulaId,testList,sampleTypeList);
            //测得量公式
            collectPartFormulaList(importTestFormula,partFormulaList,paramsFormulaId);
            //相关参数
            collectTestFormulaList(importTestFormula,testFormulaList,paramsFormulaId,paramsList);
        }
        //数据更新
        saveParamsFormulaList(formulaList);
        saveParamsPartFormulaList(partFormulaList);
        saveParamsTestFormulaList(testFormulaList);
    }

    /**
     * 归集待插入测试项目公式数据
     * @param importTestFormula 导入实体
     * @param formulaList       待插入数据容器
     * @param paramsFormulaId   关联标识
     * @param testList          所有测试项目
     * @param sampleTypeList    所有检测类型
     */
    private void collectParamsFormulaList(DtoImportTestFormula importTestFormula,List<DtoParamsFormula> formulaList,String paramsFormulaId,List<DtoTest> testList,List<DtoSampleType> sampleTypeList){
        DtoParamsFormula paramsFormula = new DtoParamsFormula(importTestFormula,testList,sampleTypeList);
        paramsFormula.setId(paramsFormulaId);
        formulaList.add(paramsFormula);
    }

    /**
     * 归集待插入测得量公式数据
     * @param importTestFormula  导入实体
     * @param partFormulaList    待插入数据容器
     * @param paramsFormulaId    关联标识
     */
    private void collectPartFormulaList(DtoImportTestFormula importTestFormula,List<DtoParamsPartFormula> partFormulaList,String paramsFormulaId){
        if(StringUtil.isNotEmpty(importTestFormula.getPartFormula())){
            DtoParamsPartFormula paramsPartFormula = new DtoParamsPartFormula(importTestFormula);
            paramsPartFormula.setFormulaId(paramsFormulaId);
            partFormulaList.add(paramsPartFormula);
        }
    }

    /**
     * 归集待插入公式参数数据
     * @param importTestFormula  导入实体
     * @param testFormulaList    待插入数据容器
     * @param paramsFormulaId    关联标识
     * @param paramsList         所有参数
     */
    private void collectTestFormulaList(DtoImportTestFormula importTestFormula,List<DtoParamsTestFormula> testFormulaList,String paramsFormulaId,List<DtoParams> paramsList){
        Matcher m = MAP_PATTERN.matcher(importTestFormula.getFormula());
        List<String> paramsNameList = new ArrayList<>();
        while (m.find()) {
            String key = m.group(0);
            paramsNameList.add(key);
        }
        //重复参数去重
        paramsNameList = paramsNameList.stream().distinct().collect(Collectors.toList());
        //按照公式参数的顺序进行排列，排序值由990开始往下降，每个参数减掉10
        int sortNum = 990;
        for (String paramName:paramsNameList) {
            DtoParams dtoParams = paramsList.stream().filter(p->p.getParamName().equals(paramName)).findFirst().orElse(null);
            if(StringUtil.isNotNull(dtoParams)){
                DtoParamsTestFormula paramsTestFormula = new DtoParamsTestFormula();
                paramsTestFormula.setObjId(paramsFormulaId);
                paramsTestFormula.setParamsId(dtoParams.getId());
                paramsTestFormula.setParamsName(dtoParams.getParamName());
                paramsTestFormula.setAlias(dtoParams.getParamName());
                paramsTestFormula.setOrderNum(sortNum>10?sortNum:0);
                sortNum -= 10;
                testFormulaList.add(paramsTestFormula);
            }
        }
    }


    /**
     * 数据更新
     * @param formulaList 数据列表
     */
    @Transactional
    public void saveParamsFormulaList(List<DtoParamsFormula> formulaList) {
        paramsFormulaRepository.save(formulaList);
    }

    /**
     * 数据更新
     * @param partFormulaList 数据列表
     */
    @Transactional
    public void saveParamsPartFormulaList(List<DtoParamsPartFormula> partFormulaList) {
        paramsPartFormulaRepository.save(partFormulaList);
    }

    /**
     * 数据更新
     * @param testFormulaList 数据列表
     */
    @Transactional
    public void saveParamsTestFormulaList(List<DtoParamsTestFormula> testFormulaList) {
        paramsTestFormulaRepository.save(testFormulaList);
    }

    /**
     *
     * @param file      文件
     * @param response  响应
     * @return 结果集
     * @throws Exception 异常
     */
    private ExcelImportResult<DtoImportTestFormula> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头开始行
        params.setHeadRows(2);
        //设置是否校验及校验器
        params.setNeedVerify(true);
        params.setVerifyHandler(testFormulaVerifyHandler);
        ExcelImportResult<DtoImportTestFormula> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoImportTestFormula.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "测试项目公式导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目公式导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 初始化校验所需数据，避免循环查询导致性能问题
     * @param testFormulaVerifyHandler 校验器
     */
    private void initHandleContainer(TestFormulaVerifyHandler testFormulaVerifyHandler){
        testFormulaVerifyHandler.setAllTestList(testRepository.findAll());
        testFormulaVerifyHandler.setAllSampleTypeList(sampleTypeRepository.findAll());
        testFormulaVerifyHandler.setAllParamsList(paramsRepository.findAllByIsDeletedFalse());
        testFormulaVerifyHandler.setAllParamsFormulaList(paramsFormulaRepository.findAll());
        testFormulaVerifyHandler.setDuplicationCheckList(new ArrayList<>());
    }

    /**
     * 移除下次调用校验器需重新赋值的容器，避免数据混淆
     * @param testFormulaVerifyHandler 校验器
     */
    private void clearPartContainer(TestFormulaVerifyHandler testFormulaVerifyHandler){
        testFormulaVerifyHandler.getDuplicationCheckList().clear();
    }


    @Autowired
    @Lazy
    public void setParamsPartFormulaRepository(ParamsPartFormulaRepository paramsPartFormulaRepository) {
        this.paramsPartFormulaRepository = paramsPartFormulaRepository;
    }

    @Autowired
    @Lazy
    public void setParamsTestFormulaRepository(ParamsTestFormulaRepository paramsTestFormulaRepository) {
        this.paramsTestFormulaRepository = paramsTestFormulaRepository;
    }

    @Autowired
    @Lazy
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    @Lazy
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    @Lazy
    public void setParamsRepository(ParamsRepository paramsRepository) {
        this.paramsRepository = paramsRepository;
    }

    @Autowired
    @Lazy
    public void setParamsFormulaRepository(ParamsFormulaRepository paramsFormulaRepository) {
        this.paramsFormulaRepository = paramsFormulaRepository;
    }
}
