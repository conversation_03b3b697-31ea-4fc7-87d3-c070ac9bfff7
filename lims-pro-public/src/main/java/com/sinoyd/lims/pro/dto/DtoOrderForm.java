package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.OrderForm;
import javax.persistence.*;

import com.sinoyd.lims.pro.entity.OtherDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.List;


/**
 * DtoOrderForm实体
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_OrderForm") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoOrderForm extends OrderForm {
    private static final long serialVersionUID = 1L;

    /**
     * 业务类型名称
     */
    @Transient
    private String projectTypeName;

    @Transient
    private String orderStatusName;

    @Transient
    private String pushStatusName;

    @Transient
    private String opinion;

    @Transient
    private DtoOrderQuotation orderQuotation;

    @Transient
    private List<DtoQuotationDetail> quotationDetailList;

    @Transient
    private List<DtoOtherDetail> otherDetailList;

    @Transient
    private List<DtoProject> projectList;

    @Transient
    private String areaName;

    @Transient
    private String grantStatusStr;
}