package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
* 报告点位信息
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataReportFolderInfoVO {
    /**
     * id
     */
    private String id;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 点位id
     */
    private String folderId;

    /**
     * 点位名称
     */
    private String folderName;

    /**
     * 点位编码
     */
    private String folderCode;

    /**
     * 点位备注
     */
    private String folderRemark;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
