package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.lims.lim.dto.customer.DtoExportParamsTestFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;


/**
 * 测试项目参数公式实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ParamsTestFormulaMapper {


    /**
     * DtoParamsTestFormula 实例转换成DtoExportParamsTestFormula实例
     *
     * @param paramsTestFormula 分析项目实体
     * @return DtoExportParamsTestFormula 实例
     */
    @Mapping(source = "orderNum", target = "orderNum")
    @Mapping(source = "sourceType", target = "sourceType")
    @Mapping(source = "isMust", target = "isMust")
    @Mapping(source = "isEditable", target = "isEditable")
    @Mapping(source = "detectionLimit", target = "detectionLimit")
    @Mapping(source = "calculationMode", target = "calculationMode")
    DtoExportParamsTestFormula toExportParamsTestFormula(DtoParamsTestFormula paramsTestFormula);

    /**
     * DtoParamsTestFormula 实例集合转换成DtoExportParamsTestFormula 实例集合
     *
     * @param paramsTestFormulaList 分析项目实例集合
     * @return DtoExportParamsTestFormula 实例集合
     */
    @InheritConfiguration(name = "toExportParamsTestFormula")
    List<DtoExportParamsTestFormula> toExportParamsTestFormulaList(List<DtoParamsTestFormula> paramsTestFormulaList);


    /**
     * DtoExportParamsTestFormula 实例转换成DtoParamsTestFormula 实例
     *
     * @param exportParamsTestFormula 分析项目实体
     * @return DtoExportParamsTestFormula 实例
     */
    @Mapping(source = "orderNum", target = "orderNum")
    @Mapping(source = "sourceType", target = "sourceType")
    @Mapping(source = "isMust", target = "isMust")
    @Mapping(source = "isEditable", target = "isEditable")
    @Mapping(source = "detectionLimit", target = "detectionLimit")
    @Mapping(source = "calculationMode", target = "calculationMode")
    DtoParamsTestFormula toDtoParamsTestFormula(DtoExportParamsTestFormula exportParamsTestFormula);

    /**
     * DtoExportParamsTestFormula 实例集合转换成DtoParamsTestFormula 实例集合
     *
     * @param exportParamsTestFormulaList 分析项目导入导出实例集合
     * @return DtoParamsTestFormula 实例集合
     */
    @InheritConfiguration(name = "toDtoParamsTestFormula")
    List<DtoParamsTestFormula> toDtoParamsTestFormulaList(List<DtoExportParamsTestFormula> exportParamsTestFormulaList);

}
