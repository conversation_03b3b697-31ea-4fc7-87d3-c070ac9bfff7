package com.sinoyd.base.dto.customer;

import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * DtoWorkflowSign 工作流信号对象
 * <AUTHOR>
 * @version V1.0.0 2019/10/30
 * @since V100R001
 */
@Data
public class DtoWorkflowSign {

    /**
     * 信号值
     */
    private String signal;

    /**
     * 对象ids
     */
    private List<String> objectIds = new ArrayList<>();

    /**
     * 对象id
     */
    private String objectId;

    /**
     * 意见
     */
    private String option;


    /**
     * 下一步操作人 todo 是不是在业务流程中也增加下一步操作人，方便数据过滤
     */
    private String nextOperatorId;

    /**
     * 编制报告，二审人员
     */
    private String secondInstanceId;

    /**
     * 下一步操作人名称
     */
    private String nextOperator;

    /**
     * 是否自动激活流程
     */
    private Boolean isActivate = false;

    /**
     * 是否自动更新业务表中的状态
     */
    private Boolean isAutoStatus = true;

    /**
     * 编制报告人id
     */
    private String reportMakerId;

    /**
     * 将单个的数据对象放入到集合里面
     *
     * @param objectIds 数据对象ids
     */
    public void setObjectIds(List<String> objectIds) {
        if (StringUtils.isNotNullAndEmpty(this.getObjectId())) {
            if (!objectIds.contains(this.getObjectId())) {
                objectIds.add(this.getObjectId());
            }
        }
        this.objectIds = objectIds;
    }


    /**
     * 将单个的数据对象放入到集合里面
     */
    public List<String> getObjectIds() {
        if (StringUtils.isNotNullAndEmpty(this.getObjectId())) {
            if (!this.objectIds.contains(this.getObjectId())) {
                objectIds.add(this.getObjectId());
            }
        }
        return objectIds;
    }
}