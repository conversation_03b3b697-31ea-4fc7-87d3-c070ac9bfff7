package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ExpressageInfoService;
import com.sinoyd.lims.pro.criteria.ExpressageInfoCriteria;
import com.sinoyd.lims.pro.dto.DtoExpressageInfo;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.Collection;
import java.util.List;


/**
 * 快递服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
 @Api(tags = "示例: 快递服务")
 @RestController
 @RequestMapping("api/pro/expressageInfo")
 public class ExpressageInfoController extends BaseJpaController<DtoExpressageInfo, String,ExpressageInfoService> {


    /**
     * 分页动态条件查询ExpressageInfo
     * @param expressageInfoCriteria 条件参数
     * @return RestResponse<List<ExpressageInfo>>
     */
     @ApiOperation(value = "分页动态条件查询快递", notes = "分页动态条件查询快递")
     @GetMapping
     public RestResponse<List<DtoExpressageInfo>> findByPage(ExpressageInfoCriteria expressageInfoCriteria) {
         PageBean<DtoExpressageInfo> pageBean = super.getPageBean();
         RestResponse<List<DtoExpressageInfo>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, expressageInfoCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询快递
     * @param id 主键id
     * @return RestResponse<DtoExpressageInfo>
     */
     @ApiOperation(value = "按主键查询快递", notes = "按主键查询快递")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoExpressageInfo> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoExpressageInfo> restResponse = new RestResponse<>();
         DtoExpressageInfo expressageInfo = service.findOne(id);
         restResponse.setData(expressageInfo);
         restResponse.setRestStatus(StringUtil.isNull(expressageInfo) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增快递
     * @param expressageInfos 项目快递信息
     * @return 
     */
     @ApiOperation(value = "新增快递", notes = "新增快递")
     @PostMapping
     public RestResponse<List<DtoExpressageInfo>> create(@RequestBody List<DtoExpressageInfo> expressageInfos) {
         RestResponse<List<DtoExpressageInfo>> restResponse = new RestResponse<>();
         restResponse.setData(service.save(expressageInfos));
         return restResponse;
      }

    /**
     * 新增快递
     * @param entity 项目快递信息
     * @return RestResponse<DtoExpressageInfo>
     */
    @ApiOperation(value = "新增报告发放", notes = "新增报告发放")
    @PostMapping("/saveOne")
    public RestResponse<DtoExpressageInfo> saveOne(@RequestBody DtoExpressageInfo entity) {
        RestResponse<DtoExpressageInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.save(entity));
        return restResponse;
    }

    /**
     * 新增快递
     * @param entity 项目快递信息
     * @return RestResponse<DtoExpressageInfo>
     */
    @ApiOperation(value = "修改报告发放", notes = "修改报告发放")
    @PutMapping("/saveOne")
    public RestResponse<DtoExpressageInfo> updateOne(@RequestBody DtoExpressageInfo entity) {
        RestResponse<DtoExpressageInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.update(entity));
        return restResponse;
    }

        /**
     * 根据id删除项目类型
     * @param  id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除快递", notes = "根据id删除快递")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id)
    {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }
 }