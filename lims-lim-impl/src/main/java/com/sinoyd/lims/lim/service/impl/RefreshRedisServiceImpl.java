package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.rcc.*;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.base.service.EvaluationCriteriaService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.model.ConfigModel;
import com.sinoyd.boot.frame.sys.service.IConfigService;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.configuration.HomeTaskConfig;
import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.repository.lims.Person2TestRepository;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.lim.service.homeTaskNumber.AbsTaskNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.base.constants.IBaseConstants.DEVIATION_FORMULA;

/**
 * 刷新redis缓存
 *
 * <AUTHOR>
 * @version V1.0.0 2021/09/14
 * @since V100R001
 */
@Service
@Slf4j
public class RefreshRedisServiceImpl implements RefreshRedisService {

    @Autowired
    @Lazy
    private CodeService codeService;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private Person2TestRepository person2TestRepository;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    @Autowired
    private ProjectTypeService projectTypeService;

    @Autowired
    private AnalyzeItemRepository analyzeItemRepository;

    @Autowired
    @Lazy
    private DimensionService dimensionService;

    @Autowired
    @Lazy
    private SerialIdentifierConfigService serialIdentifierConfigService;

    @Autowired
    @Lazy
    private EvaluationCriteriaService evaluationCriteriaService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    private HomeTaskConfig homeTaskConfig;

    @Autowired
    private List<AbsTaskNumber> taskList;

    private TestExpandService testExpandService;

    private QualityControlLimitRepository qualityControlLimitRepository;

    @Autowired
    private IConfigService configService;


    private static Map<String, String> code2MethodNameMap = new HashMap<>();

    static {
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_OrgId_Test.toString(), "reloadTest");
        code2MethodNameMap.put(EnumBase.EnumBASRedis.BAS_OrgId_AnalyzeItem.toString(), "reloadAnalyzeItem");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_OrgId_TestExpand.toString(), "reloadTestExpand");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_OrgId_Person2Test.toString(), "reloadPerson2Test");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_OrgId_ParamsPartFormula.toString(), "reloadParamsPartFormula");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_OrgId_ProjectType.toString(), "reloadProjectType");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_OrgId_MessageSendConfig.toString(), "reloadMessageSendConfig");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_OrgId_IdentifierConfig.toString(), "reloadIdentifierConfig");
        code2MethodNameMap.put(EnumBase.EnumBASRedis.BAS_OrgId_Dimension.toString(), "reloadDimension");
        code2MethodNameMap.put(EnumBase.EnumBASRedis.BAS_OrgId_EvaluationCriteria.toString(), "reloadEvaluationCriteria");
        code2MethodNameMap.put(EnumBase.EnumBASRedis.BAS_OrgId_SampleType.toString(), "reloadSampleType");
        code2MethodNameMap.put(EnumBase.EnumBASRedis.BAS_OrgId_DeviationFormula.toString(), "reloadDeviationFormula");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_Home_TaskNumber.toString(), "reloadHomeTaskNum");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_Home_OfficeManage.toString(), "reloadOfficeManage");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_Card_AnalyseDataCardNum.toString(), "reloadAnalyseDataCardNum");
        code2MethodNameMap.put(EnumLIM.EnumLIMRedis.LIM_Limit_Type.toString(), "reloadLimitConfigType");
    }

    @Override
    public List<DtoCode> findRedisModel() {
        List<DtoCode> codeList = codeService.findCodes("LIM_RedisModel");
        codeList.sort(Comparator.comparing(DtoCode::getSortNum, Comparator.reverseOrder()).thenComparing(DtoCode::getDictCode));
        return codeList;
    }

    @Override
    public void refreshRedisByCodes(List<DtoCode> modelCodes) {
        if (StringUtil.isEmpty(modelCodes)) {
            return;
        }
        Class clazz = this.getClass();
        try {
            for (DtoCode modelCode : modelCodes) {
                String key = modelCode.getDictValue();
                String orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
                key = key.replace("{0}", orgId);
                Set<String> keySets = redisTemplate.keys(key);
                redisTemplate.delete(keySets);
                if (!"*".equals(key)) {
                    Method method = clazz.getMethod(code2MethodNameMap.get(modelCode.getDictCode()));
                    method.invoke(this);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("刷新Redis缓存发生错误");
        }
    }

    /**
     * 刷新整个redis实例
     */
    @Override
    public void flushAll() {
        // 使用 RedisCallback 获取底层连接
        redisTemplate.execute((RedisConnection connection) -> {
            // 执行 flushAll 命令
            connection.flushAll();
            return null;
        });
    }


    public void reloadAnalyseDataCardNum() {
        //组织id
        String orgId = PrincipalContextUser.getPrincipal().getOrgId();
        //当前操作人的人员id
        String userIdLogin = PrincipalContextUser.getPrincipal().getUserId();
        //添加实验室分析的代办数量
        List<String> modules = Arrays.asList(EnumLIM.EnumHomeTaskModule.实验室待检.getValue(),
                EnumLIM.EnumHomeTaskModule.实验室检测.getValue(),
                EnumLIM.EnumHomeTaskModule.实验室审核.getValue());
        for (String module : modules) {
            HomeModule homeModule = new HomeModule();
            homeModule.setModuleCode(module);
            homeModule.setValue(EnumLIM.EnumProjectTaskCache.人员.getValue());
            Optional<AbsTaskNumber> taskNum = taskList.stream().filter(p -> module.equals(p.getModuleCode())).findFirst();
            taskNum.ifPresent(p -> p.refreshCardNum(homeModule, orgId, userIdLogin));
        }

    }

    /**
     * 刷新首页代办数字缓存
     */
    public void reloadHomeTaskNum() {
        //获取到配置的主页模块
        List<HomeModule> projectModules = homeTaskConfig.getProjectModules();
        //循环判断需要进入哪个策略
        for (HomeModule projectModule : projectModules) {
            Optional<AbsTaskNumber> taskNum = taskList.stream().filter(p -> projectModule.getModuleCode().equals(p.getModuleCode())).findFirst();
            taskNum.ifPresent(p -> p.refreshRedis(projectModule));
        }
        reloadAnalyseDataManage();
    }

    /**
     * 刷新首页办公管理缓存数据
     */
    public void reloadOfficeManage() {
        HomeModule officeModule = new HomeModule();
        officeModule.setModuleCode(EnumLIM.EnumHomeTaskModule.我的审批.getValue());
        officeModule.setValue(EnumLIM.EnumProjectTaskCache.人员.getValue());
        Optional<AbsTaskNumber> taskNum = taskList.stream().filter(p -> officeModule.getModuleCode().equals(p.getModuleCode())).findFirst();
        taskNum.ifPresent(p -> p.refreshRedis(officeModule));
    }

    /**
     * 刷新首页实验室分析代办数字缓存
     */
    public void reloadAnalyseDataManage() {
        //添加实验室分析的代办数量
        List<String> modules = Arrays.asList(EnumLIM.EnumHomeTaskModule.实验室待检.getValue(),
                EnumLIM.EnumHomeTaskModule.实验室检测.getValue(),
                EnumLIM.EnumHomeTaskModule.实验室审核.getValue());
        for (String module : modules) {
            HomeModule homeModule = new HomeModule();
            homeModule.setModuleCode(module);
            homeModule.setValue(EnumLIM.EnumProjectTaskCache.人员.getValue());
            Optional<AbsTaskNumber> taskNum = taskList.stream().filter(p -> module.equals(p.getModuleCode())).findFirst();
            taskNum.ifPresent(p -> p.refreshRedis(homeModule));
        }

    }

    /**
     * 刷新测试项目的redis缓存
     */
    public void reloadTest() {
        testService.initRedis();
    }

    /**
     * 刷新分析项目的redis缓存
     */
    public void reloadAnalyzeItem() {
        List<DtoAnalyzeItem> analyzeItemList = analyzeItemRepository.findAll();
        if (StringUtil.isEmpty(analyzeItemList)) {
            return;
        }
        for (DtoAnalyzeItem dtoAnalyzeItem : analyzeItemList) {
            String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_AnalyzeItem.getValue());
            redisTemplate.opsForHash().put(key, dtoAnalyzeItem.getId(), JsonStream.serialize(dtoAnalyzeItem));
        }
    }

    /**
     * 从数据库中获取偏差公式对应的质控限值对象
     */
    public void reloadDeviationFormula() {
        List<DtoQualityControlLimit> controlLimitList = qualityControlLimitRepository.findByTestId(DEVIATION_FORMULA);
        if (StringUtil.isEmpty(controlLimitList)) {
            return;
        }
        for (DtoQualityControlLimit limit : controlLimitList) {
            String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_DeviationFormula.getValue());
            redisTemplate.opsForHash().put(key, limit.getQcGrade() + "_" + limit.getQcType(), limit.getFormula());
        }
    }

    /**
     * 刷新测试项目扩展信息的redis缓存
     */
    public void reloadTestExpand() {
        List<DtoTestExpand> testExpandList = testExpandService.findAll();
        if (StringUtil.isEmpty(testExpandList)) {
            return;
        }
        Map<String, List<DtoTestExpand>> testId2ExpandListMap = testExpandList.stream().
                collect(Collectors.groupingBy(testExpand -> testExpand.getTestId()));
        for (Map.Entry<String, List<DtoTestExpand>> entry : testId2ExpandListMap.entrySet()) {
            String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_TestExpand.getValue());
            redisTemplate.opsForHash().put(key, entry.getKey(), JsonStream.serialize(entry.getValue()));
        }

    }

    /**
     * 刷新人员相关的岗位信息的redis缓存
     */
    public void reloadPerson2Test() {
        List<DtoPerson2Test> person2TestList = person2TestRepository.findAll();
        if (StringUtil.isEmpty(person2TestList)) {
            return;
        }
        Map<String, List<DtoPerson2Test>> testId2PersonTestListMap = person2TestList.stream().
                collect(Collectors.groupingBy(person2Test -> person2Test.getTestId()));
        for (Map.Entry<String, List<DtoPerson2Test>> entry : testId2PersonTestListMap.entrySet()) {
            String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Person2Test.getValue());
            redisTemplate.opsForHash().put(key, entry.getKey(), JsonStream.serialize(entry.getValue()));
        }
    }

    /**
     * 刷新测试项目公式的部分公式的redis缓存
     */
    public void reloadParamsPartFormula() {
        List<DtoParamsPartFormula> paramsPartFormulaList = paramsPartFormulaRepository.findAll();
        if (StringUtil.isEmpty(paramsPartFormulaList)) {
            return;
        }
        Map<String, List<DtoParamsPartFormula>> formulaId2ParamsPartFormulaListMap = paramsPartFormulaList.stream().
                collect(Collectors.groupingBy(paramsPartFormula -> paramsPartFormula.getFormulaId()));
        for (Map.Entry<String, List<DtoParamsPartFormula>> entry : formulaId2ParamsPartFormulaListMap.entrySet()) {
            String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_ParamsPartFormula.getValue());
            redisTemplate.opsForHash().put(key, entry.getKey(), JsonStream.serialize(entry.getValue()));
        }
    }


    /**
     * 刷新项目类型的redis缓存
     */
    public void reloadProjectType() {
        List<DtoProjectType> projectTypeList = projectTypeService.findAll();
        for (DtoProjectType dtoProjectType : projectTypeList) {
            if (StringUtil.isNotEmpty(dtoProjectType.getConfig())) {
                Map configMap = JsonIterator.deserialize(dtoProjectType.getConfig(), Map.class);
                dtoProjectType.setConfigObject(configMap);
            }
        }
    }

    /**
     * 重新加载编号配置到缓存
     */
    public void reloadIdentifierConfig() {
        List<DtoSerialIdentifierConfig> configList = serialIdentifierConfigService.findAll();
        for (DtoSerialIdentifierConfig config : configList) {
            serialIdentifierConfigService.saveRedis(config);
        }
    }

    /**
     * 重新加载量纲到缓存
     */
    public void reloadDimension() {
        List<DtoDimension> dtoDimensionList = dimensionService.findAll();
        if (StringUtil.isNotEmpty(dtoDimensionList)) {
            dtoDimensionList = dtoDimensionList.parallelStream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
            for (DtoDimension dtoDimension : dtoDimensionList) {
                dimensionService.saveRedis(dtoDimension);
            }
        }
    }

    /**
     * 重新加载评价标准到缓存
     */
    public void reloadEvaluationCriteria() {
        List<DtoEvaluationCriteria> evaluationCriteriaList = evaluationCriteriaService.findAll();
        for (DtoEvaluationCriteria dtoEvaluationCriteria : evaluationCriteriaList) {
            evaluationCriteriaService.initEvaluationCriteriaRedisById(dtoEvaluationCriteria.getId());
        }
    }

    /**
     * 重新加载检测类型到缓存
     */
    public void reloadSampleType() {
        List<DtoSampleType> sampleTypeList = sampleTypeService.findAll();
        if (StringUtil.isNotEmpty(sampleTypeList)) {
            sampleTypeList = sampleTypeList.parallelStream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
            for (DtoSampleType dtoSampleType : sampleTypeList) {
                sampleTypeService.save(dtoSampleType);
            }
        }
    }

    /**
     * 重新加载检出限计算
     */
    public void reloadLimitConfigType(){
        String configValue = "";
        ConfigModel configModel = configService.findConfig("sys_pro_unlessexamlimit_type");
        if(StringUtil.isNotNull(configModel)) {
            configValue = configModel.getConfigValue();
        }
        redisTemplate.opsForValue().set(EnumLIM.EnumLIMRedis.LIM_Limit_Type.getValue(), configValue);
    }

    @Autowired
    @Lazy
    public void setTestExpandService(TestExpandService testExpandService) {
        this.testExpandService = testExpandService;
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }
}
