package com.sinoyd.lims.pro.criteria;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.pro.dto.DtoOATaskQuery;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskQueryType;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审批任务查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-17
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OATaskCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询参数
     */
    private DtoOATaskQuery param;

    /**
     * 查询类型
     */
    private EnumOATaskQueryType queryType;

    /**
     * 排除的状态
     */
    private List<Integer> notStatus;

    @Override
    public String getCondition() {
        String condition = "";
        switch (this.queryType) {
            case 待我审批:
                condition = buildTodo();
                break;
            case 我已审批:
                condition = buildFinish();
                break;
            case 我已发起:
                condition = buildSponsor();
                break;
            case 所有审批:
                condition = buildAll();
                break;
            default:
                break;
        }

        return condition;
    }

    /**
     * 构建待我审批查询条件
     * @return
     */
    private String buildTodo() {
        StringBuilder conditionSb = new StringBuilder();

        if (StringUtil.isNotEmpty(param.getProcTypeId())) {
            conditionSb.append(" and procTypeId = :procTypeId");
            values.put("procTypeId", param.getProcTypeId());
        }

        // 当前处理人标识为自己的
        conditionSb.append(" and currentAssigneeId = :currentAssigneeId");
        values.put("currentAssigneeId", PrincipalContextUser.getPrincipal().getUserId());

        if (StringUtil.isNotEmpty(param.getDeptId())) {
            conditionSb.append(" and deptId = :deptId");
            values.put("deptId", param.getDeptId());
        }

        if (StringUtil.isNotEmpty(param.getSponsorId())) {
            conditionSb.append(" and sponsorId = :sponsorId");
            values.put("sponsorId", param.getSponsorId());
        }

        if (StringUtil.isNotEmpty(param.getTitle())) {
            conditionSb.append(" and title like :title");
            values.put("title", "%" + param.getTitle() + "%");
        }

        if (StringUtil.isNotNull(notStatus) && notStatus.size() > 0) {
            conditionSb.append(" and dataStatus not in :notStatus");
            values.put("notStatus", notStatus);
        }

        return conditionSb.toString();
    }

    /**
     * 构建我已审批查询条件
     * @return
     */
    private String buildFinish() {
        StringBuilder conditionSb = new StringBuilder();
        conditionSb.append(" and task.id = log.taskId");
        conditionSb.append(" and log.isFirstStep = 0");
        conditionSb.append(" and log.assignee = :assignee");
        values.put("assignee", PrincipalContextUser.getPrincipal().getLoginId());

        if (StringUtil.isNotEmpty(param.getProcTypeId())) {
            conditionSb.append(" and task.procTypeId = :procTypeId");
            values.put("procTypeId", param.getProcTypeId());
        }

        if (StringUtil.isNotEmpty(param.getSubmitBeginTime())
                && StringUtil.isNotEmpty(param.getSubmitEndTime())) {
            Date from = DateUtil.stringToDate(param.getSubmitBeginTime(), DateUtil.YEAR);
            Date to = DateUtil.stringToDate(param.getSubmitEndTime(), DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            conditionSb.append(" and task.submitTime >= :submitBeginTime");
            values.put("submitBeginTime", from);
            conditionSb.append(" and task.submitTime < :submitEndTime");
            values.put("submitEndTime", c.getTime());
        }
        if (StringUtil.isNotEmpty(param.getDeptId())) {
            conditionSb.append(" and task.deptId = :deptId");
            values.put("deptId", param.getDeptId());
        }

        if (StringUtil.isNotEmpty(param.getSponsorId())) {
            conditionSb.append(" and task.sponsorId = :sponsorId");
            values.put("sponsorId", param.getSponsorId());
        }

        if (StringUtil.isNotNull(param.getDataStatus())) {
            conditionSb.append(" and task.dataStatus = :dataStatus");
            values.put("dataStatus", param.getDataStatus());
        }

        if (StringUtil.isNotEmpty(param.getTitle())) {
            conditionSb.append(" and task.title like :title");
            values.put("title", "%" + param.getTitle() + "%");
        }

        if (StringUtil.isNotNull(notStatus) && notStatus.size() > 0) {
            conditionSb.append(" and dataStatus not in :notStatus");
            values.put("notStatus", notStatus);
        }
        return conditionSb.toString();
    }

    /**
     * 构建我已发起查询条件
     * @return
     */
    private String buildSponsor() {
        StringBuilder conditionSb = new StringBuilder();
        
        if (StringUtil.isNotEmpty(param.getProcTypeId())) {
            conditionSb.append(" and procTypeId = :procTypeId");
            values.put("procTypeId", param.getProcTypeId());
        }

        if (StringUtil.isNotEmpty(param.getSubmitBeginTime())
                && StringUtil.isNotEmpty(param.getSubmitEndTime())) {
            Date from = DateUtil.stringToDate(param.getSubmitBeginTime(), DateUtil.YEAR);
            conditionSb.append(" and task.submitTime >= :submitBeginTime");
            values.put("submitBeginTime", from);
            Date to = DateUtil.stringToDate(param.getSubmitEndTime(), DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            conditionSb.append(" and task.submitTime < :submitEndTime");
            values.put("submitEndTime", c.getTime());
        }

        // 发起人标识为自己的
        conditionSb.append(" and sponsorId = :sponsorId");
        values.put("sponsorId", PrincipalContextUser.getPrincipal().getUserId());

        if (StringUtil.isNotNull(param.getDataStatus())) {
            conditionSb.append(" and dataStatus = :dataStatus");
            values.put("dataStatus", param.getDataStatus());
        }

        if (StringUtil.isNotEmpty(param.getTitle())) {
            conditionSb.append(" and title like :title");
            values.put("title", "%" + param.getTitle() + "%");
        }

        if (StringUtil.isNotNull(notStatus) && notStatus.size() > 0) {
            conditionSb.append(" and dataStatus not in :notStatus");
            values.put("notStatus", notStatus);
        }
        return conditionSb.toString();
    }
    
    /**
     * 构建所有审批查询条件
     * @return
     */
    private String buildAll() {
        StringBuilder conditionSb = new StringBuilder();
        
        if (StringUtil.isNotEmpty(param.getProcTypeId())) {
            conditionSb.append(" and procTypeId = :procTypeId");
            values.put("procTypeId", param.getProcTypeId());
        }

        if (StringUtil.isNotEmpty(param.getSubmitBeginTime())
                && StringUtil.isNotEmpty(param.getSubmitEndTime())) {
            Date from = DateUtil.stringToDate(param.getSubmitBeginTime(), DateUtil.YEAR);
            Date to = DateUtil.stringToDate(param.getSubmitEndTime(), DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            conditionSb.append(" and task.submitTime >= :submitBeginTime");
            values.put("submitBeginTime", from);
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            conditionSb.append(" and task.submitTime < :submitEndTime");
            values.put("submitEndTime", c.getTime());
        }
        
        if (StringUtil.isNotEmpty(param.getDeptId())) {
            conditionSb.append(" and deptId = :deptId");
            values.put("deptId", param.getDeptId());
        }

        if (StringUtil.isNotEmpty(param.getSponsorId())) {
            conditionSb.append(" and sponsorId = :sponsorId");
            values.put("sponsorId", param.getSponsorId());
        }

        if (StringUtil.isNotNull(param.getDataStatus())) {
            conditionSb.append(" and dataStatus = :dataStatus");
            values.put("dataStatus", param.getDataStatus());
        }

        if (StringUtil.isNotEmpty(param.getTitle())) {
            conditionSb.append(" and title like :title");
            values.put("title", "%" + param.getTitle() + "%");
        }

        if (StringUtil.isNotNull(notStatus) && notStatus.size() > 0) {
            conditionSb.append(" and dataStatus not in :notStatus");
            values.put("notStatus", notStatus);
        }
        return conditionSb.toString();
    }
}