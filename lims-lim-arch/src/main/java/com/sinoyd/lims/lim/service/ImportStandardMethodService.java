package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.customer.DtoImportTest;
import com.sinoyd.lims.lim.dto.customer.DtoImportStandardMethod;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethod;
import com.sinoyd.lims.lim.dto.lims.DtoTest;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 国家标准方法导入接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/14
 * @since V100R001
 */
public interface ImportStandardMethodService extends ImportExcelService<DtoImportStandardMethod, DtoStandardMethod> {
    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    void downLoadExcel(HttpServletResponse response, Map<String, String> sheetNames, String fileName);

}
