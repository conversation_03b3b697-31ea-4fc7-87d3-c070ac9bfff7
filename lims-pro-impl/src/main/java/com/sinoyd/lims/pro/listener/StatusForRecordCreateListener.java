package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 外部送样送样单状态生成监听通知
 *
 * <AUTHOR>
 * @version V1.0.0 2019-12-07
 * @since V100R001
 */
@Component
@Slf4j
public class StatusForRecordCreateListener implements ExecutionListener {

    @Transactional
    @Override
    public void notify(DelegateExecution execution) {
        ReceiveSampleRecordService service = SpringContextAware.getBean(ReceiveSampleRecordService.class);
        service.createOutsideStatus(execution.getProcessBusinessKey());
    }
}
