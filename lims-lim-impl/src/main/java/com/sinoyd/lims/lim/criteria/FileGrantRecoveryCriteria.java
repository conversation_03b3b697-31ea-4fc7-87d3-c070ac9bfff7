package com.sinoyd.lims.lim.criteria;

import java.util.Calendar;
import java.util.Date;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件发放与回收
 * <AUTHOR> 修改：xuxb
 * @version v1.0.0 2019/3/6
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileGrantRecoveryCriteria extends BaseCriteria {


    /**
     * 关键字：发放编号、受控编号、文件名称、文件编号、版本号
     */
    private String key;
    /**
     * 检索开始时间（发放日期）
     */
    private String dtBegin;
    /**
     * 检索结束时间（发放日期）
     */
    private String dtEnd;
    /**
     *  文件类型（空Guid代表所有）
     */
    private String fileType;
    /**
     * 文件状态（参见前台枚举,其中-1代表所有）
     */
    private Integer fileStatus;
    /**
     * 领用人id（传空默认所有）
     */
    private String materialPersonId;
    /**
     *  回收人id（传空默认所有）
     */
    private String recoveryPersonId;

    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and fileGrantRecovery.id = fileControlApplyDetail.grantId");

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and (fileGrantRecovery.code like :key or fileControlApplyDetail.controlCode like :key or fileControlApplyDetail.fileCode like :key or fileControlApplyDetail.fileName like :key or fileControlApplyDetail.version like :key) ");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtils.isNotNullAndEmpty(materialPersonId) && !UUIDHelper.GUID_EMPTY.equals(materialPersonId)) {
            condition.append(" and fileGrantRecovery.materialPersonId = :materialPersonId");
            values.put("materialPersonId", this.materialPersonId);
        }
        if (StringUtils.isNotNullAndEmpty(recoveryPersonId) && !UUIDHelper.GUID_EMPTY.equals(recoveryPersonId)) {
            condition.append(" and fileGrantRecovery.recoveryPersonId = :recoveryPersonId");
            values.put("recoveryPersonId", this.recoveryPersonId);
        }
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and fileGrantRecovery.grantTime >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and fileGrantRecovery.grantTime < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(fileType) && !UUIDHelper.GUID_EMPTY.equals(fileType)) {
            condition.append(" and fileControlApplyDetail.fileType = :fileType");
            values.put("fileType", this.fileType);
        }
        if (fileStatus != -1) {
            condition.append(" and fileControlApplyDetail.status = :status");
            values.put("status", this.fileStatus);
        }

        return condition.toString();
    }
}