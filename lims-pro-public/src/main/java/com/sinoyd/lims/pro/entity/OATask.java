package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * OATask实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OATask")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class OATask implements BaseEntity,Serializable {
 
    public  OATask() {
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 标题
    */
    @Column(nullable=false)
    @ApiModelProperty("标题")
	private String title;
    
    /**
    * 说明
    */
    @ApiModelProperty("说明")
	private String description;
    
    /**
    * 发起人账号
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("发起人账号")
	private String sponsor;
    
    /**
    * 发起人标识
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("发起人标识")
	private String sponsorId;
    
    /**
    * 发起人名称
    */
    @Column(length=100)
    @ApiModelProperty("发起人名称")
	private String sponsorName;
    
    /**
    * 提交时间
    */
    @Column(nullable=false)
    @ApiModelProperty("提交时间")
	private Date submitTime;
    
    /**
    * 完成时间
    */
    @Column(nullable=false)
    @ApiModelProperty("完成时间")
	private Date completeTime;
    
    /**
    * 当前办理人账号
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("当前办理人账号")
	private String currentAssignee;
    
    /**
    * 当前办理人标识
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("当前办理人标识")
	private String currentAssigneeId;
    
    /**
    * 当前办理人名称
    */
    @Column(length=50)
    @ApiModelProperty("当前办理人名称")
	private String currentAssigneeName;
    
    /**
    * 当前环节key
    */
    @Column(length=50)
    @ApiModelProperty("当前环节key")
	private String currentTaskDefKey;
    
    /**
    * 当前环节名称
    */
    @Column(length=50)
    @ApiModelProperty("当前环节名称")
	private String currentTaskName;
    
    /**
    * 工作流类型编码(枚举EnumProcTypeCode：contract: 合同审批、projectExpend: 项目支出、departmentExpend: 部门支出、instrumentPurchase: 仪器采购、instrumentRepair: 仪器维修、instrumentScrap: 仪器报废、consumable：领料、consumablePurchase: 消耗品采购、fileControl:文件受控、fileRevision: 文件修订、fileAbolish: 文件废止)
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("工作流类型编码(枚举EnumProcTypeCode：contract: 合同审批、projectExpend: 项目支出、departmentExpend: 部门支出、instrumentPurchase: 仪器采购、instrumentRepair: 仪器维修、instrumentScrap: 仪器报废、consumable：领料、consumablePurchase: 消耗品采购、fileControl:文件受控、fileRevision: 文件修订、fileAbolish: 文件废止)")
	private String procTypeCode;
    
    /**
    * 工作流类型标识(常量编码: OA_Processtype)
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("工作流类型标识(常量编码: OA_Processtype)")
	private String procTypeId;
    
    /**
    * 工作流类型名称
    */
    @Column(length=50)
    @ApiModelProperty("工作流类型名称")
	private String procTypeName;
    
    /**
    * 工作流实例Id
    */
    @Column(length=100,nullable=false)
    @ApiModelProperty("工作流实例Id")
	private String procInstId;
    
    /**
    * 状态名称(字符串，枚举EnumOATaskStatus：审批通过、审批中、审批拒绝)
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("状态名称(字符串，枚举EnumOATaskStatus：审批通过、审批中、审批拒绝)")
	private String status;
    
    /**
    * 状态（数值，枚举EnumOATaskStatus：0.审批中 1.审批通过 2.审批拒绝)
    */
    @Column(nullable=false)
    @ApiModelProperty("状态（数值，枚举EnumOATaskStatus：0.审批中 1.审批通过 2.审批拒绝)")
	private Integer dataStatus;
    
    /**
    * 部门ID
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("部门ID")
	private String deptId;
    
    /**
    * 部门名称
    */
    @Column(length=100)
    @ApiModelProperty("部门名称")
	private String deptName;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }