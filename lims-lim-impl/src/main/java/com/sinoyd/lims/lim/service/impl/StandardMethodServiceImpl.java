package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.criteria.StandardMethodCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethod;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethodDetail;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.StandardMethodDetailRepository;
import com.sinoyd.lims.lim.repository.lims.StandardMethodRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.StandardMethodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * StandardMethod服务实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Service
public class StandardMethodServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoStandardMethod, String, StandardMethodRepository> implements StandardMethodService {

    private StandardMethodDetailRepository standardMethodDetailRepository;

    private TestRepository testRepository;

    private AnalyzeMethodRepository analyzeMethodRepository;

    @Override
    public void findByPage(PageBean<DtoStandardMethod> page, BaseCriteria criteria) {
        page.setEntityName("DtoStandardMethod a");
        page.setSelect("select a");
        StandardMethodCriteria standardMethodCriteria = (StandardMethodCriteria) criteria;
        int pageNo = page.getPageNo();
        int rowsPerPage = page.getRowsPerPage();
        if (StringUtil.isNotNull(standardMethodCriteria.getStatus()) && !standardMethodCriteria.getStatus().equals(EnumLIM.MethodMatchState.所有.getValue())) {
            page.setPageNo(0);
            page.setRowsPerPage(Integer.MAX_VALUE);
        }
        super.findByPage(page, criteria);
        List<DtoStandardMethod> pageData = page.getData();
        if (StringUtil.isNotEmpty(pageData)) {
            List<String> methodIds = pageData.stream().map(DtoStandardMethod::getId).collect(Collectors.toList());
            List<DtoStandardMethodDetail> standardMethodDetails = StringUtil.isNotEmpty(methodIds) ? standardMethodDetailRepository.findByMethodIdIn(methodIds) : new ArrayList<>();
            Map<String, List<DtoStandardMethodDetail>> groupMethodMap = standardMethodDetails.stream().collect(Collectors.groupingBy(DtoStandardMethodDetail::getMethodId));
            for (DtoStandardMethod standardMethod : pageData) {
                // 筛选配对信息
                List<DtoStandardMethodDetail> methodDetails = groupMethodMap.getOrDefault(standardMethod.getId(), new ArrayList<>());
                if (StringUtil.isNotEmpty(methodDetails)) {
                    standardMethod.setMatchNum(methodDetails.size());
                }
            }
            if (StringUtil.isNotNull(standardMethodCriteria.getStatus()) && !standardMethodCriteria.getStatus().equals(EnumLIM.MethodMatchState.所有.getValue())) {
                if (standardMethodCriteria.getStatus().equals(EnumLIM.MethodMatchState.已配对.getValue())) {
                    pageData = pageData.stream().filter(p -> p.getMatchNum() > 0).collect(Collectors.toList());
                } else {
                    pageData = pageData.stream().filter(p -> p.getMatchNum() == 0).collect(Collectors.toList());
                }
                // 处理分页
                page.setRowsCount(pageData.size());
                pageData = pageData.stream().skip((long) (pageNo -1 ) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList());
            }
            page.setData(pageData);
        }

    }

    /**
     * 自动匹配
     */
    @Override
    @Transactional
    public void autoMatch() {
        List<DtoStandardMethod> standardMethods = repository.findAll();
        // 已经匹配的数据
        List<DtoStandardMethodDetail> standardMethodDetails = standardMethodDetailRepository.findAll();
        Map<String, List<String>> detailMap = standardMethodDetails.stream()
                .collect(Collectors.groupingBy(DtoStandardMethodDetail::getMethodId, Collectors.mapping(DtoStandardMethodDetail::getObjectId, Collectors.toList())));

        List<DtoTest> testList = testRepository.findAll();
        Map<String, List<DtoTest>> testGroupMap = testList.stream().collect(Collectors.groupingBy(p -> p.getRedAnalyzeItemName() + "_" + p.getRedCountryStandard()));
        // 采样方法
        List<DtoAnalyzeMethod> analyzeMethodList = analyzeMethodRepository.findAll().stream().filter(p->Boolean.TRUE.equals(p.getIsSamplingMethod())).collect(Collectors.toList());
        Map<String, List<DtoAnalyzeMethod>> analyzeMethodMap = analyzeMethodList.stream().filter(p-> StringUtil.isNotEmpty(p.getCountryStandard()))
                .collect(Collectors.groupingBy(DtoAnalyzeMethod::getCountryStandard));
        List<DtoStandardMethodDetail> addMethodDetail = new ArrayList<>();
        for (DtoStandardMethod standardMethod : standardMethods) {
            String testKey = standardMethod.getItemName() + "_" + standardMethod.getCountryStandard();
            List<DtoTest> tests = testGroupMap.getOrDefault(testKey, new ArrayList<>());
            List<String> testIds = tests.stream().map(DtoTest::getId).collect(Collectors.toList());
            List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodMap.getOrDefault(standardMethod.getCountryStandard(), new ArrayList<>());
            testIds.addAll(analyzeMethods.stream().map(DtoAnalyzeMethod::getId).collect(Collectors.toList()));
            // 去掉已经匹配的项目
            List<String> objectIds = detailMap.getOrDefault(standardMethod.getId(), new ArrayList<>());
            testIds.removeIf(objectIds::contains);
            for (String testId : testIds) {
                DtoStandardMethodDetail standardMethodDetail = new DtoStandardMethodDetail();
                standardMethodDetail.setMethodId(standardMethod.getId());
                standardMethodDetail.setObjectId(testId);
                addMethodDetail.add(standardMethodDetail);
            }
        }
        if (StringUtil.isNotEmpty(addMethodDetail)) {
            standardMethodDetailRepository.save(addMethodDetail);
        }

    }


    @Autowired
    @Lazy
    public void setStandardMethodDetailRepository(StandardMethodDetailRepository standardMethodDetailRepository) {
        this.standardMethodDetailRepository = standardMethodDetailRepository;
    }

    @Autowired
    @Lazy
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }
}