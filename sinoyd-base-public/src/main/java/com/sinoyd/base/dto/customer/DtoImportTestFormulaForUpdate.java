package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

/**
 *   测试项目公式导入（新增）easypoi实体
 *   对应模板sheet1
 */
@Data
public class DtoImportTestFormulaForUpdate extends PoiBaseEntity {
    /**
     * id
     */
    @Excel(name = "id", orderNum = "100", width = 35)
    private String id;

    /**
     * 检测类型
     */
    @Excel(name = "检测类型", orderNum = "200", width = 15)
    private String sampleType;

    /**
     * 分析项目
     */
    @Excel(name = "分析项目", orderNum = "300", width = 15)
    private String analyzeItem;

    /**
     * 分析方法
     */
    @Excel(name = "分析方法", orderNum = "400", width = 50)
    private String analyzeMethod;

    /**
     * 检测类型（公式）
     */
    @Excel(name = "检测类型（公式）", orderNum = "500", width = 20)
    private String sampleTypeForFormula;

    /**
     * 公式
     */
    @Excel(name = "公式", orderNum = "600", width = 45)
    private String formula;

    /**
     * 公式参数
     */
    @Excel(name = "公式参数", orderNum = "700", width = 50)
    private String params;

    /**
     * 测得量公式
     */
    @Excel(name = "公式", groupName = "测得量公式", orderNum = "800", fixedIndex = 7, width = 35)
    private String partFormula;

    /**
     * 有效位数
     */
    @Excel(name = "有效位数", groupName = "测得量公式", orderNum = "900", fixedIndex = 8, width = 10)
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    @Excel(name = "小数位数", groupName = "测得量公式", orderNum = "1000", fixedIndex = 9, width = 10)
    private Integer mostDecimal;

    /**
     * 检出限
     */
    @Excel(name = "检出限", groupName = "测得量公式", orderNum = "1100", fixedIndex = 10, width = 10)
    private String detectionLimit;

    /**
     * 计算方式
     */
    @Excel(name = "计算方式", groupName = "测得量公式", orderNum = "1200", fixedIndex = 11, width = 10)
    private String calculationMode;


    /**
     * 串联出证公式
     */
    @Excel(name = "串联出证公式", orderNum = "1600", width = 30)
    private String seriesFormula;

}
