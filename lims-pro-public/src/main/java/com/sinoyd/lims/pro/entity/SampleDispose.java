package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * SampleDispose实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "SampleDispose")
@Data
public class SampleDispose implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public SampleDispose() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 样品标识
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("样品标识")
    private String sampleId;

    /**
     * 样品来源
     */
    @Column(length = 100)
    @ApiModelProperty("样品来源")
    @Length(message = "样品来源{validation.message.length}", max = 100)
    private String sampleSource;

    /**
     * 样品数量
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("样品数量")
    private Integer sampleCount;

    /**
     * 保留日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("保留日期")
    private Date reserveDate;

    /**
     * 保存位置
     */
    @Column(length = 100)
    @ApiModelProperty("保存位置")
    @Length(message = "保存位置{validation.message.length}", max = 100)
    private String reserveLocation;

    /**
     * 分析项目名称，多个英文逗号间隔
     */
    @Column(length = 1000)
    @ApiModelProperty("分析项目名称，多个英文逗号间隔")
    @Length(message = "分析项目名称，多个英文逗号间隔{validation.message.length}", max = 1000)
    private String redAnalyzeItems;

    /**
     * 处置人Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("处置人Id")
    private String disposePersonId;

    /**
     * 处置日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("处置日期")
    private Date disposeDate;

    /**
     * 处置方式
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("处置方式")
    @Length(message = "处置方式{validation.message.length}", max = 50)
    //@NotBlank(message = "处置方式{validation.message.blank}")
    private String disposeSolution;

    /**
     * 处置备注
     */
    @Column(length = 1000)
    @ApiModelProperty("处置备注")
    @Length(message = "处置备注{validation.message.length}", max = 1000)
    private String disposeRemarks;

    /**
     * 保存条件
     */
    @Column(length = 100)
    @ApiModelProperty("保存条件")
    @Length(message = "保存条件{validation.message.length}", max = 100)
    private String saveCondition;

    /**
     * 假删字段
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;

    /**
     * 是否处置字段，0(false)为未处置，1(true)为处置
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否处置字段")
    private Boolean isDisposed = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}