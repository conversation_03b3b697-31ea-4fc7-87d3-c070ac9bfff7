package com.sinoyd.lims.monitor.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;


/**
 * PropertyPoint2Test实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="PropertyPoint2Test")
 @Data
 public  class PropertyPoint2Test implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String propertyPointId;
    
    /**
    * 
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String testId;

    /**
     * 次数
     */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("次数")
    private Integer timesOrder;

    /**
     * 样品数
     */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("样品数")
    private Integer samplePeriod;
 }