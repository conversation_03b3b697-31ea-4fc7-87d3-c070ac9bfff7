package com.sinoyd.lims.od.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.od.dto.DtoOdPersonAnswer;

import java.util.List;

/**
 * 嗅辨员答案操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
public interface OdPersonAnswerService extends IBaseJpaService<DtoOdPersonAnswer, String> {

    /**
     * 提交实验数据
     *
     * @param odPersonAnswer 实验数据
     * @return 实验数据
     */
    DtoOdPersonAnswer submit(DtoOdPersonAnswer odPersonAnswer);

    /**
     * 根据实验次序id查询
     *
     * @param labSeqId 实验次序id
     * @return 实验数据
     */
    List<DtoOdPersonAnswer> findByLabSeqId(String labSeqId);

    /**
     * 根据实验组ids查询嗅辨分析数据
     * @param groupIds 实验组ids
     * @return 嗅辨分析数据
     */
    List<DtoOdPersonAnswer> findByGroupIdIn(List<String> groupIds);
}
