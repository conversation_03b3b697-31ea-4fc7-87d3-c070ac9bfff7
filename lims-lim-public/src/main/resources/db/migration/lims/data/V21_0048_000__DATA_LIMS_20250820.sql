DELETE
from tb_lim_reportconfig
where id in ('2a4e7a96-8720-405a-8459-6a217c3b6d0f',
             '21db6652-3b91-432f-a5b1-d564fc2d7ce6');
INSERT INTO tb_lim_reportconfig (id, type, reportCode, templateName, template, outputName,
                                 returnType, method, params, pageConfig, orderNum, bizType,
                                 remark, isDeleted, orgId, creator, createDate, domainId,
                                 modifier, modifyDate, dataMethod, typeCode, strUrl,
                                 isDefineFileName, defineFileName, beanName, versionNum,
                                 controlNum, reportName, validate, usageNum)
VALUES ('2a4e7a96-8720-405a-8459-6a217c3b6d0f', 1, 'MBXYZSamplingRecord', 'LIMS-CY-Q-16-01 油气回收系统密闭性及液阻检测原始记录表.xlsx',
        'Sampling/LIMS-CY-Q-16-01 油气回收系统密闭性及液阻检测原始记录表.xlsx',
        'output/SamplingRecords/LIMS-CY-Q-16-01 油气回收系统密闭性及液阻检测原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.sampling.service.samplingReport.MBXYZSamplingRecordService', '', '', 0, 3, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-08-20 17:17:07',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-08-20 17:58:16', '', 'Sampling',
        'MBXYZSamplingRecord', b'0', '', NULL, '', '', '', 1, 2);
INSERT INTO tb_lim_reportconfig (id, type, reportCode, templateName, template, outputName,
                                 returnType, method, params, pageConfig, orderNum, bizType,
                                 remark, isDeleted, orgId, creator, createDate, domainId,
                                 modifier, modifyDate, dataMethod, typeCode, strUrl,
                                 isDefineFileName, defineFileName, beanName, versionNum,
                                 controlNum, reportName, validate, usageNum)
VALUES ('21db6652-3b91-432f-a5b1-d564fc2d7ce6', 1, 'QYBZSamplingRecord', 'LIMS-CY-Q-15-01 油气回收气液比原始记录表(新).xlsx',
        'Sampling/LIMS-CY-Q-15-01 油气回收气液比原始记录表(新).xlsx', 'output/SamplingRecords/LIMS-CY-Q-15-01 油气回收气液比原始记录表(新).xlsx',
        'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.QYBSamplingRecordService', '', '', 0, 3,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-08-20 17:09:16',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-08-20 17:46:08', '', 'Sampling',
        'QYBZSamplingRecord', b'0', '', NULL, '', '', '', 1, 5);


DELETE
from tb_lim_reportapply
where id in (
             '01602545-d59b-4738-879d-097a1c61d46d',
             '1d41d074-4ba5-41de-9e99-83997fcdc706',
             'cae462a0-6dec-4c2f-a09f-4e333abb2dd6',
             'e25afbe0-46ab-4ca6-a7e4-85cd14709e5f'
    );
INSERT INTO tb_lim_reportapply (id, reportConfigId, module, moduleName, code, name, type,
                                isRedact, isShow, remark, location, orgId, creator,
                                createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('01602545-d59b-4738-879d-097a1c61d46d', '21db6652-3b91-432f-a5b1-d564fc2d7ce6', 'PrepareSample', '采样准备', '采样单',
        '油气回收气液比原始记录表(新)', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-08-20 17:10:33', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-08-20 17:10:33', NULL);
INSERT INTO tb_lim_reportapply (id, reportConfigId, module, moduleName, code, name, type,
                                isRedact, isShow, remark, location, orgId, creator,
                                createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('1d41d074-4ba5-41de-9e99-83997fcdc706', '21db6652-3b91-432f-a5b1-d564fc2d7ce6', 'LocalTask', '现场任务', '采样单',
        '油气回收气液比原始记录表(新)', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-08-20 17:11:08', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-08-20 17:11:08', NULL);
INSERT INTO tb_lim_reportapply (id, reportConfigId, module, moduleName, code, name, type,
                                isRedact, isShow, remark, location, orgId, creator,
                                createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('cae462a0-6dec-4c2f-a09f-4e333abb2dd6', '2a4e7a96-8720-405a-8459-6a217c3b6d0f', 'PrepareSample', '采样准备', '采样单',
        '系统密闭性及液阻检测原始记录', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-08-20 17:17:33', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-08-20 17:17:33', NULL);
INSERT INTO tb_lim_reportapply (id, reportConfigId, module, moduleName, code, name, type,
                                isRedact, isShow, remark, location, orgId, creator,
                                createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('e25afbe0-46ab-4ca6-a7e4-85cd14709e5f', '2a4e7a96-8720-405a-8459-6a217c3b6d0f', 'LocalTask', '现场任务', '采样单',
        '系统密闭性及液阻检测原始记录', 1, 0, 1, '', '现场任务:数据:样品信息', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-08-20 17:17:46', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-08-20 17:17:46', NULL);
