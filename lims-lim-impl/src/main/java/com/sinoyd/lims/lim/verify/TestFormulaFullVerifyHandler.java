package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaForUpdate;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 测试项目公式导入(修改）公式配置sheet数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/8
 * @since V100R001
 */
@Data
public class TestFormulaFullVerifyHandler implements IExcelVerifyHandler<DtoImportTestFormulaForUpdate> {
    /**
     *   正则表达式，用于从公式中提取参数
     */
    private static final Pattern MAP_PATTERN = Pattern.compile("(?<=\\[).*?(?=\\])");

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    /**
     *  重复数据校验容器
     */
    private List<DtoImportTestFormulaForUpdate> duplicationCheckList;

    /**
     *  测试项目校验容器，存放所有测试项目
     */
    private List<DtoTest> allTestList;

    /**
     *  检测类型校验容器，存放所有检测类型
     */
    private List<DtoSampleType> allSampleTypeList;

    /**
     *  参数校验容器，存放所有参数
     */
    private List<DtoParams> allParamsList;

    /**
     * 数据校验
     *
     * @param importTestFormulaForUpdate 导入的实体
     * @return 导入结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportTestFormulaForUpdate importTestFormulaForUpdate) {
        //导入数据处理,跳过空行,数据去除前后空格
        try {
            if (importUtils.checkObjectIsNull(importTestFormulaForUpdate)) {
                return new ExcelVerifyHandlerResult(true);
            }
            importUtils.strToTrim(importTestFormulaForUpdate);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }

        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + (importTestFormulaForUpdate.getRowNum()-1) + "行数据校验有误");

        //必填校验
        importUtils.checkIsNull(result, importTestFormulaForUpdate.getId(), "id", failStr);
        importUtils.checkIsNull(result, importTestFormulaForUpdate.getSampleType(), "检测类型", failStr);
        importUtils.checkIsNull(result, importTestFormulaForUpdate.getAnalyzeItem(), "分析项目", failStr);
        importUtils.checkIsNull(result, importTestFormulaForUpdate.getAnalyzeMethod(), "分析方法", failStr);
        importUtils.checkIsNull(result, importTestFormulaForUpdate.getSampleTypeForFormula(), "检测类型（公式）", failStr);
        importUtils.checkIsNull(result, importTestFormulaForUpdate.getFormula(), "公式", failStr);

        // 数据格式校验
        importUtils.checkNumTwo(result, importTestFormulaForUpdate.getMostSignificance(), "测得量公式有效位数", failStr);
        importUtils.checkNumTwo(result, importTestFormulaForUpdate.getMostDecimal(), "测得量公式小数位数", failStr);

        // 业务数据校验
        //检测类型（公式）：类型是否属于对应测试项目大类及其小类
        isExistTest(result, failStr, importTestFormulaForUpdate);

        //更新重复校验容器
        duplicationCheckList.add(importTestFormulaForUpdate);

        //构建异常信息
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }


    /**
     * 需要根据“检测类型”、“分析项目”、“分析方法”判定系统中是否存在该测试项目  需要验证“检测类型（公式）”是否属于“测试项目检测类型大类及其小类”的要求
     * @param result  校验结果
     * @param failStr 校验错误数据
     * @param importTestFormulaForUpdate 导入数据
     */
    private void isExistTest(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTestFormulaForUpdate importTestFormulaForUpdate) {
        DtoTest existTest = allTestList.stream().filter(test->test.getRedAnalyzeItemName().equals(importTestFormulaForUpdate.getAnalyzeItem())&&test.getRedAnalyzeMethodName().equals(importTestFormulaForUpdate.getAnalyzeMethod()))
                .findFirst().orElse(null);
        if(StringUtil.isNull(existTest)){
            result.setSuccess(false);
            failStr.append("；").append("测试项目不存在");
        }
        else{
            DtoSampleType existTestSampleType = allSampleTypeList.stream().filter(s->s.getId().equals(existTest.getSampleTypeId())).findFirst().orElse(null);
            if(!existTestSampleType.getTypeName().equals(importTestFormulaForUpdate.getSampleType())){
                result.setSuccess(false);
                failStr.append("；").append("测试项目与检测类型不匹配");
            }
            else{
                //检测类型（公式）与 检测类型都填写但不相同的情况 校验 检测类型（公式） 是否为检测类型的小类
                if(StringUtil.isNotEmpty(importTestFormulaForUpdate.getSampleType())&&StringUtil.isNotEmpty(importTestFormulaForUpdate.getSampleTypeForFormula())
                        &&!importTestFormulaForUpdate.getSampleType().equals(importTestFormulaForUpdate.getSampleTypeForFormula())){
                    DtoSampleType sampleTypeP = allSampleTypeList.stream().filter(s->s.getTypeName().equals(importTestFormulaForUpdate.getSampleType())).findFirst().orElse(null);
                    if(StringUtil.isNull(sampleTypeP)){
                        result.setSuccess(false);
                        failStr.append("；").append("检测类型不存在;");
                    }
                    DtoSampleType sampleTypeC = allSampleTypeList.stream().filter(s->s.getTypeName().equals(importTestFormulaForUpdate.getSampleTypeForFormula())).findFirst().orElse(null);
                    if(StringUtil.isNull(sampleTypeC)){
                        result.setSuccess(false);
                        failStr.append("；").append("检测类型（公式）不存在;");
                    }
                    if(StringUtil.isNotNull(sampleTypeP)&&StringUtil.isNotNull(sampleTypeC)&&!sampleTypeC.getParentId().equals(sampleTypeP.getId())){
                        result.setSuccess(false);
                        failStr.append("；").append("检测类型（公式）与检测类型不匹配;");
                    }
                }
            }
        }

    }
}
