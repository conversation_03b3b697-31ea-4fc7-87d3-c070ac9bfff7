package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoProject;

import java.util.List;
import java.util.Map;

/**
 * 环保企业通项目数据推送接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/31
 * @since V100R001
 */
public interface EnvironmentEnterpriseService {

    /**
     * 项目信息推送
     *
     * @param projectList   项目列表
     * @param projectStatus 项目推送状态
     */
    void pushProjectData(List<DtoProject> projectList, String projectStatus);

    /**
     * 下载report文件流
     *
     * @param id    报告文档id
     * @param token token字符串
     * @return report文件流
     */
    byte[] downReport(String id, String token);

    /**
     * 推送项目
     *
     * @param projectId 项目id
     */
    void push(String projectId);
}
