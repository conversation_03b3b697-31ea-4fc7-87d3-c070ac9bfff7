-- （351）离子色谱法分析原始记录（带曲线） 记录单模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig,
                                orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier,
                                modifyDate,
                                dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum)
VALUES ('6b985723-75cb-4e65-95fc-e9daee6275d1', 1, 'WorkSheetLZSPFQX', '（351）离子色谱法分析原始记录（带曲线）_模板.xls',
        'WorkSheet/（351）离子色谱法分析原始记录（带曲线）_模板.xls', 'output/WorkSheet/（351）离子色谱法分析原始记录（带曲线）.xls',
        'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{\"originalRecordType\" : \"lineExtendBySampleTest\", \"workSheetDataSourceType\" : \"LZSPFQXWorkSheetDataSourceImpl\", \"qCGroupByTest\" : \"1\", \"qCAnaLmtCnt\" : \"4\"}',
        111, 2, '', b'0', '5f7bcf90feb545968424b0a872863876', 'a70e76eb-0638-48d5-9660-6090170bc163',
        '2023-02-26 21:31:44', '5f7bcf90feb545968424b0a872863876',
        'a70e76eb-0638-48d5-9660-6090170bc163', '2023-02-26 21:33:52', 'workSheetFolderId,type', 'WorkSheet',
        'WorkSheetLZSPFQX', b'0', '', '', '', '');

-- （351）离子色谱法分析原始记录（带曲线） 记录单应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,location)
VALUES ('68e62e3a-2df6-4dd7-be54-2ec6b3054f8e', '6b985723-75cb-4e65-95fc-e9daee6275d1', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入');