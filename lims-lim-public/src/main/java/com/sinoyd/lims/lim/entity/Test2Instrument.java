package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * Test2Instrument实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Test2Instrument")
 @Data
 public  class Test2Instrument implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Test2Instrument() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 测试标识（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试标识（Guid）")
	private String testId;
    
    /**
    * 仪器标识（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("仪器标识（Guid）")
    private String instrumentId;
    
    /**
    * 使用类型（枚举EnumInsUseObjType：1采样，2：实验室分析，4：现场分析）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("使用类型（枚举EnumInsUseObjType：1采样，2：实验室分析，4：现场分析）")
    private Integer useType;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }