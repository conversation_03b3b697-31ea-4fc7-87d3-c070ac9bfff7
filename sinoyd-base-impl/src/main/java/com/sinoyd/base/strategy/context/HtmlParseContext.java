package com.sinoyd.base.strategy.context;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.strategy.htmlParse.AbstractHtmlParse;
import com.sinoyd.boot.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Html数据解析上下文
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/21
 * @since V100R001
 */
@Component
public class HtmlParseContext {

    private Map<String, AbstractHtmlParse> htmlParseMap;

    /**
     * Html数据解析
     *
     * @param html 需要解析的Html数据
     * @param type 解析类型
     * @return 解析结果
     */
    public <T> List<T> parse(String html, Integer type, Class<T> c) {
        if (StringUtil.isEmpty(html)) {
            return new ArrayList<>();
        }
        EnumBase.EnumHtmlParseType parseType = EnumBase.EnumHtmlParseType.getEnumItem(type);
        if (parseType == null) {
            throw new RuntimeException("Html解析类型不存在！");
        }
        AbstractHtmlParse htmlParse = htmlParseMap.get(parseType.getBeanName());
        if (htmlParse == null) {
            throw new RuntimeException("Html解析类型未实现！");
        }
        return htmlParse.parse(html, c);
    }

    @Autowired
    private void setHtmlParseMap(Map<String, AbstractHtmlParse> htmlParseMap) {
        this.htmlParseMap = htmlParseMap;
    }
}
