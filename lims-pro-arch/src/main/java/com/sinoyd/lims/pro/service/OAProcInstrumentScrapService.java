package com.sinoyd.lims.pro.service;

import java.util.List;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentScrap;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;

/**
 * 仪器报废业务操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
public interface OAProcInstrumentScrapService {
    /**
     * 添加审批并启动流程
     * 
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<List<DtoOAInstrumentScrap>> taskDto);

    /**
     * 查询任务详细信息
     * 
     * @param taskId 任务ID
     * @return
     */
    DtoOATaskDetail<List<DtoOAInstrumentScrap>, List<DtoInstrument>> findOATaskDetail(String taskId);

    /**
     * 查询可发起审批列表
     * @param page 分页对象
     * @param key 关键字
     * @param typeId 类型Id
     */
    void findCanSponsor(PageBean<?> page, String key, String typeId);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask saveAsDraft(DtoOATaskCreate<List<DtoOAInstrumentScrap>> taskDto);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask draftSave(DtoOATaskCreate<List<DtoOAInstrumentScrap>> taskDto);

    /**
     * 草稿提交
     * @param taskDto  表单数据
     * @return         oa任务
     */
    String draftSubmit(DtoOATaskCreate<List<DtoOAInstrumentScrap>> taskDto);
}
