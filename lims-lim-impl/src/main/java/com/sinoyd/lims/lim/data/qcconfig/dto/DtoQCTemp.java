package com.sinoyd.lims.lim.data.qcconfig.dto;

import lombok.Data;

import java.util.List;

/**
 * 质控限值已配置类型返回实体
 * <AUTHOR>
 * @version V1.0.0 2022/6/14
 * @since V100R001
 */
@Data
public class DtoQCTemp {

    /**
     * 选择的测试项目Id
     */
    private String testId;

    /**
     * 选择的分析项目名称
     */
    private String  analyzeItmeName;

    /**
     * 选择的分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 已配置的质控类型
     */
    private List<DtoQcType> qcTypes;
}
