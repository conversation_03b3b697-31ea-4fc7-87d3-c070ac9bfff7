package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoProject2WorkSheetFolder;
import com.sinoyd.lims.pro.repository.Project2WorkSheetFolderRepository;
import com.sinoyd.lims.pro.service.Project2WorkSheetFolderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Project2WorkSheetFolder操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Service
public class Project2WorkSheetFolderServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoProject2WorkSheetFolder,String,Project2WorkSheetFolderRepository> implements Project2WorkSheetFolderService {

    /**
     * 核查后删除项目及检测单id
     *
     * @param projectId            项目id
     * @param workSheetFolderIds   检测单id集合
     * @param exceptSampleIds      排除的样品id集合
     * @param exceptAnalyseDataIds 排除的数据id集合
     */
    @Transactional
    @Override
    public void removeByProjectIdAndWorkSheetFolderId(String projectId,
                                                      List<String> workSheetFolderIds,
                                                      List<String> exceptSampleIds,
                                                      List<String> exceptAnalyseDataIds) {
        workSheetFolderIds = workSheetFolderIds.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
        if (workSheetFolderIds.size() == 0) {
            return;
        }
        Map<String, Object> values = new HashMap<>();
        PageBean<String> pb = new PageBean<>();
        pb.setEntityName("DtoAnalyseData a,DtoSample s");
        pb.setSelect("select distinct a.workSheetFolderId");
        pb.addCondition(" and s.isDeleted = 0 and a.isDeleted = 0 ");
        pb.addCondition(" and a.sampleId = s.id");
        pb.addCondition(" and a.workSheetFolderId <> :workSheetFolderId");
        pb.addCondition(" and s.projectId = :projectId");
        values.put("workSheetFolderId", UUIDHelper.GUID_EMPTY);
        values.put("projectId", projectId);
        if (StringUtil.isNotNull(exceptSampleIds) && exceptSampleIds.size() > 0) {
            pb.addCondition(" and s.id not in :exceptSampleIds");
            values.put("exceptSampleIds", exceptSampleIds);
        }
        if (StringUtil.isNotNull(exceptAnalyseDataIds) && exceptAnalyseDataIds.size() > 0) {
            pb.addCondition(" and a.id not in :exceptAnalyseDataIds");
            values.put("exceptAnalyseDataIds", exceptAnalyseDataIds);
        }

        List<String> nowFolderIds = comRepository.find(pb.getAutoQuery(), values);
        workSheetFolderIds.removeAll(nowFolderIds);
        if (workSheetFolderIds.size() > 0) {
            repository.deleteByProjectIdAndWorkSheetFolderIdIn(projectId, workSheetFolderIds);
        }
    }
}