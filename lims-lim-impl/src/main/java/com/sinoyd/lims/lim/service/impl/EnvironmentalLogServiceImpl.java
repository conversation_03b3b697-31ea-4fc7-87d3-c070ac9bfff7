package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalLog;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import com.sinoyd.lims.lim.repository.lims.EnvironmentalLogRepository;
import com.sinoyd.lims.lim.service.EnvironmentalLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 环境日志管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/8
 * @since V100R001
 */
@Service
public class EnvironmentalLogServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoEnvironmentalLog, String, EnvironmentalLogRepository> implements EnvironmentalLogService {

    /**
     * 新增环境日志
     */
    @Transactional
    @Override
    public DtoEnvironmentalLog save(DtoEnvironmentalLog entity) {
        return super.save(entity);
    }

    @Transactional
    @Override
    public DtoEnvironmentalLog update(DtoEnvironmentalLog entity) {
        return super.update(entity);
    }

    /**
     * 分页查询环境日志
     */
    @Override
    public void findByPage(PageBean<DtoEnvironmentalLog> page, BaseCriteria criteria) {

        // 设置查询的实体类名及别名
        page.setEntityName("DtoEnvironmentalLog p");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select p");

        super.findByPage(page, criteria);
    }

    /**
     * 保存实验室环境使用记录
     *
     * @param dto 环境记录
     * @return 实验室环境使用记录集合
     */
    @Override
    @Transactional
    public DtoEnvironmentalLog saveEnvironmentalLog(DtoEnvironmentalRecord dto) {
        DtoEnvironmentalLog log = new DtoEnvironmentalLog();
        convertField(dto,log);
        return super.save(log);
    }

    @Override
    @Transactional
    public DtoEnvironmentalLog saveOrUpdateEnvironmentalLog(DtoEnvironmentalRecord dto, String objectId) {
        DtoEnvironmentalLog environmentalLog;
        if(StringUtil.isNotEmpty(objectId)&&!UUIDHelper.GUID_EMPTY.equals(objectId)){
            environmentalLog = repository.findByEnvironmentalIdAndObjectId(dto.getEnvironmentalId(),objectId);
            if(environmentalLog==null){
                environmentalLog = new DtoEnvironmentalLog();
                environmentalLog.setObjectId(objectId);
            }
        }else{
            environmentalLog = new DtoEnvironmentalLog();
        }
        convertField(dto,environmentalLog);
        return  super.save(environmentalLog);
    }

    /**
     * 属性复制
     * @param dto 仪器使用记录
     * @param environmentalLog 环境日志
     */
    private void convertField(DtoEnvironmentalRecord dto,DtoEnvironmentalLog environmentalLog){
        environmentalLog.setEnvironmentalId(dto.getEnvironmentalId());
        environmentalLog.setHumidity(StringUtil.isNotEmpty(dto.getHumidity()) ? dto.getHumidity() : "");
        environmentalLog.setPressure(dto.getPressure());
        environmentalLog.setTemperature(StringUtil.isNotEmpty(dto.getTemperature()) ? dto.getTemperature() : "");
        environmentalLog.setUpdateTime(dto.getStartTime());
    }
}