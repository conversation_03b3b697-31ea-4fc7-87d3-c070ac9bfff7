package com.sinoyd.lims.lim.service.homeTaskNumber;

import com.sinoyd.lims.lim.configuration.HomeModule;
import com.sinoyd.lims.lim.dto.customer.DtoTaskNum;
import com.sinoyd.lims.lim.enums.EnumLIM;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 首页现场数据复核代办数字缓存刷新
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/10/26
 */
@Component
public class LocalDataCheckTask extends AbsTaskNumber{

    /**
     * 获取缓存数据集合
     *
     * @param homeModule 模块编码
     * @param orgId      组织Id
     * @param userId     用户Id
     * @param outTypeIds 不包含的项目类型
     * @return 结果集
     */
    @Override
    public List<DtoTaskNum> getTaskNum(HomeModule homeModule, String orgId, String userId, List<String> outTypeIds) {
        //创建查询sql语句
        StringBuilder stringBuilder = new StringBuilder("select a.currentPersonId as userId,count(a.id) as count from");
        stringBuilder.append(" TB_PRO_StatusForRecord a,TB_PRO_ReceiveSampleRecord b,TB_PRO_Project c");
        stringBuilder.append(" where 1=1 and a.orgId = ?");
        stringBuilder.append(" and a.status = ? and a.module = ?");
        stringBuilder.append(" and a.receiveId=b.id and b.projectId=c.id and c.isDeleted=0 ");
        stringBuilder.append(" group by a.currentPersonId");
        //执行sql语句
        return jdbcTemplate.query(stringBuilder.toString(),
                new String[]{orgId,"1",EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue()},
                (resultSet, i) -> new DtoTaskNum(resultSet.getString("userId"),resultSet.getLong("count")));
    }

    /**
     * 获取模块编码
     *
     * @return 模块编码
     */
    @Override
    public String getModuleCode() {
        return EnumLIM.EnumHomeTaskModule.现场数据复核.getValue();
    }
}
