package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoFolderPeriodWWInfo;
import com.sinoyd.lims.pro.repository.FolderPeriodWWInfoRepository;
import com.sinoyd.lims.pro.service.FolderPeriodWWInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * FolderPeriodWWInfo操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/7/7
 * @since V100R001
 */
@Service
public class FolderPeriodWWInfoServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFolderPeriodWWInfo,String, FolderPeriodWWInfoRepository> implements FolderPeriodWWInfoService {

    /**
     * 下发任务信息
     *
     * @param folderId    点位id
     * @param periodCount 周期
     * @return 任务信息
     */
    @Override
    public List<DtoFolderPeriodWWInfo> findByFolderIdAndPeriodCount(String folderId, Integer periodCount) {
        return repository.findByFolderIdAndPeriodCountOrderByIssueTimeDesc(folderId, periodCount);
    }
}
