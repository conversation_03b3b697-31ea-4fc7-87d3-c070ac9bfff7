package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * SubmitRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SubmitRecord")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class SubmitRecord implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  SubmitRecord() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 关联Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("关联Id")
	private String objectId;
    
    /**
    * 关联对象类型（枚举EnumSubmitObjectType：1.项目 2.检测单 3.送样单 4.现场领样单 5.分析领样单）
    */
    @Column(nullable=false)
    @ApiModelProperty("关联对象类型（枚举EnumSubmitObjectType：1.项目 2.检测单 3.送样单 4.现场领样单 5.分析领样单）")
	private Integer objectType;
    
    /**
    * 操作类型（枚举EnumSubmitType：0.无 1.项目登记 2.任务下达 ....）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("操作类型（枚举EnumSubmitType：0.无 1.项目登记 2.任务下达 ....）")
	private Integer submitType;
    
    /**
    * 操作时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("操作时间")
	private Date submitTime;
    
    /**
    * 操作人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("操作人")
	private String submitPersonId;
    
    /**
    * 操作人名称
    */
    @Column(length=50)
    @ApiModelProperty("操作人名称")
    @Length(message = "操作人名称{validation.message.length}", max = 50)
	private String submitPersonName;
    
    /**
    * 下一步操作人（名字）
    */
    @Column(length=200)
    @ApiModelProperty("下一步操作人（名字）")
    @Length(message = "下一步操作人（名字）{validation.message.length}", max = 200)
	private String nextPerson;
    
    /**
    * 操作意见
    */
    @Column(length=200)
    @ApiModelProperty("操作意见")
    @Length(message = "操作意见{validation.message.length}", max = 200)
	private String submitRemark;
    
    /**
    * 操作前状态
    */
    @Column(length=50)
    @ApiModelProperty("操作前状态")
    @Length(message = "操作前状态{validation.message.length}", max = 50)
	private String stateFrom;
    
    /**
    * 操作后状态
    */
    @Column(length=50)
    @ApiModelProperty("操作后状态")
    @Length(message = "操作后状态{validation.message.length}", max = 50)
	private String stateTo;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }