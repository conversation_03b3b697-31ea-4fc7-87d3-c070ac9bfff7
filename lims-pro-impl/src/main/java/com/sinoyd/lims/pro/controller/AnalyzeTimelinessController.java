package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.AnalyzeTimelinessCriteria;
import com.sinoyd.lims.pro.service.AnalyzeTimelinessChartService;
import com.sinoyd.lims.pro.service.AnalyzeTimelinessDataService;
import com.sinoyd.lims.pro.vo.AnalyzeDetailDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 分析及时率统计接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/01/31
 */
@Api(tags = "分析及时率统计接口")
@RestController
@RequestMapping("api/pro/analyzeTimeliness")
public class AnalyzeTimelinessController extends ExceptionHandlerController<AnalyzeTimelinessDataService> {

    private AnalyzeTimelinessChartService chartService;

    /**
     * 数据详情列表分页查询
     *
     * @param criteria 查询条件
     * @return 分页结果
     */
    @ApiOperation(value = "数据详情列表分页查询", notes = "数据详情列表分页查询")
    @GetMapping("/data/details")
    public RestResponse<List<AnalyzeDetailDataVO>> findDetail(AnalyzeTimelinessCriteria criteria) {
        PageBean<AnalyzeDetailDataVO> pageBean = super.getPageBean();
        RestResponse<List<AnalyzeDetailDataVO>> restResponse = new RestResponse<>();
        service.queryDetailData(pageBean, criteria,restResponse);
        restResponse.setData(pageBean.getData());
        return restResponse;
    }

    /**
     * 及时率信息统计列表
     *
     * @param criteria 查询条件
     * @return 及时率信息统计列表数据
     */
    @ApiOperation(value = "及时率信息统计", notes = "及时率信息统计")
    @GetMapping("/data/timeliness")
    public RestResponse<List<Object>> queryTimeliness(AnalyzeTimelinessCriteria criteria) {
        RestResponse<List<Object>> restResponse = new RestResponse<>();
        restResponse.setData(service.queryTimeliness(criteria));
        return restResponse;
    }

    /**
     * 总及时率图表查询
     *
     * @param criteria 查询条件
     * @return 总及时率图表查询结果
     */
    @ApiOperation(value = "总及时率图表查询", notes = "总及时率图表查询")
    @GetMapping("/chart/timeliness")
    public RestResponse<Map<String, Object>> totalTimelinessChart(AnalyzeTimelinessCriteria criteria) {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setData(chartService.totalTimelinessChart(criteria));
        return restResponse;
    }

    /**
     * 及时率柱状图查询
     *
     * @param criteria 查询条件
     * @return 及时率柱状图查询结果
     */
    @ApiOperation(value = "及时率柱状图查询", notes = "及时率柱状图查询")
    @GetMapping("/chart/histogram")
    public RestResponse<Map<String, List<Object>>> histogramChart(AnalyzeTimelinessCriteria criteria) {
        RestResponse<Map<String, List<Object>>> restResponse = new RestResponse<>();
        restResponse.setData(chartService.histogramChart(criteria));
        return restResponse;
    }

    @Autowired
    @Lazy
    public void setChartService(AnalyzeTimelinessChartService chartService) {
        this.chartService = chartService;
    }
}
