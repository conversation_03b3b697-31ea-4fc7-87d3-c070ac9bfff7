package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分析项目关系参数查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeItemRelationParamsCriteria extends BaseCriteria {

    /**
     * 分析项目关系
     */
    private String relationId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (!UUIDHelper.GUID_EMPTY.equals(this.relationId)) {
            condition.append(" and (relationId = :relationId)");
            values.put("relationId", this.relationId);
        }
        return condition.toString();
    }
}