package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
* 样品制备信息
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataSamplePreparationVO {

    /**
     * id
     */
    private String id;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 制备样品的分析项目名称
     */
    private String analyzeItemNames;

    /**
     * 制备开始时间
     */
    private Date preparationBeginTime;

    /**
     * 制备结束时间
     */
    private Date preparationEndTime;

    /**
     * 制备人名称
     */
    private String preparedPersonName;

    /**
     * 制备方法
     */
    private String method;

    /**
     * 制备内容
     */
    private String content;

    /**
     * 制备仪器ids
     */
    private String instrumentId;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
