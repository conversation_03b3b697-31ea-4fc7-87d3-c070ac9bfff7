package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.entity.ConsumableDetail;
import com.sinoyd.base.service.ConsumableDetailService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePickListsDetail;
import com.sinoyd.lims.lim.service.OAConsumablePickListsDetailService;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.service.OAConsumablePickListCheckService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 领料流程审批提交检查服务接口实现
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2024/10/08
 */
@Service
public class OAConsumablePickListCheckServiceImpl extends OATaskCheckServiceImpl implements OAConsumablePickListCheckService {

    private OATaskRelationService oaTaskRelationService;

    private OAConsumablePickListsDetailService oaConsumablePickListsDetailService;

    private ConsumableDetailService consumableDetailService;

    @Override
    public List<DtoSubmitRestrictVo> submitCheck(String taskId) {
        List<DtoSubmitRestrictVo> result = new ArrayList<>();
        //获取相关任务的关系数据
        List<DtoOATaskRelation> relations = oaTaskRelationService.findListByTaskId(taskId);
        List<String> objectIds = relations.stream().map(DtoOATaskRelation::getObjectId).distinct().collect(Collectors.toList());
        if (objectIds.size() > 0) {
            //获取领料的明细数据
            List<DtoOAConsumablePickListsDetail> pickListsDetails = oaConsumablePickListsDetailService.findAll(objectIds);
            //得到消耗品的ids
            List<String> consumableIds = pickListsDetails.stream().map(DtoOAConsumablePickListsDetail::getConsumableId).collect(Collectors.toList());
            Calendar expiryDateCalendar = Calendar.getInstance();
            expiryDateCalendar.setTime(new Date());
            // 设置时间为今天的零点零分零秒
            expiryDateCalendar.set(Calendar.HOUR_OF_DAY, 0);
            expiryDateCalendar.set(Calendar.MINUTE, 0);
            expiryDateCalendar.set(Calendar.SECOND, 0);
            expiryDateCalendar.set(Calendar.MILLISECOND, 0);
            //通过消耗品id 找到消耗品的明细数据，暂时按有效时间升序排吧，并且扣除是有效的库存
            List<DtoConsumableDetail> details = consumableDetailService.findByParentIds(consumableIds);
            for (DtoOAConsumablePickListsDetail pickListsDetail : pickListsDetails) {
                List<DtoConsumableDetail> detailList = details.stream().filter(p -> p.getParentId().equals(pickListsDetail.getConsumableId())).collect(Collectors.toList());
                for (DtoConsumableDetail detail : detailList) {
                    Date expireDate = detail.getExpiryDate();
                    if (StringUtil.isNotNull(expireDate)) {
                        String expireDateStr = DateUtil.dateToString(expireDate, DateUtil.YEAR);
                        if (!(StringUtil.isEmpty(expireDateStr) || expireDateStr.contains("1753") || expireDate.compareTo(expiryDateCalendar.getTime()) > -1)) {
                            DtoSubmitRestrictVo submitRestrictVo = new DtoSubmitRestrictVo();
                            submitRestrictVo.setCheckItem("物资有效库存校验");
                            submitRestrictVo.setIsUnusual(false);
                            submitRestrictVo.setModuleName("领料审批");
                            submitRestrictVo.setExceptionOption("领用物资：" + pickListsDetail.getConsumableName() + " 有效期内库存不足，领用的物资可能为过期物资。");
                            result.add(submitRestrictVo);
                        }
                    }
                }
                // 计算总库存
                BigDecimal inventory = detailList.stream().map(ConsumableDetail::getStorage).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 说明库存充裕，否则提示库存不足
                if (inventory.compareTo(pickListsDetail.getMaterialNum()) < 0) {
                    DtoSubmitRestrictVo submitRestrictVo = new DtoSubmitRestrictVo();
                    submitRestrictVo.setCheckItem("物资库存校验");
                    submitRestrictVo.setIsPass(false);
                    submitRestrictVo.setModuleName("领料审批");
                    submitRestrictVo.setExceptionOption("领用物资：" + pickListsDetail.getConsumableName() + " 总库存不足");
                    result.add(submitRestrictVo);
                }
            }
        }
        return result;
    }

    @Autowired
    @Lazy
    public void setOaTaskRelationService(OATaskRelationService oaTaskRelationService) {
        this.oaTaskRelationService = oaTaskRelationService;
    }

    @Autowired
    @Lazy
    public void setOaConsumablePickListsDetailService(OAConsumablePickListsDetailService oaConsumablePickListsDetailService) {
        this.oaConsumablePickListsDetailService = oaConsumablePickListsDetailService;
    }

    @Autowired
    @Lazy
    public void setConsumableDetailService(ConsumableDetailService consumableDetailService) {
        this.consumableDetailService = consumableDetailService;
    }
}
