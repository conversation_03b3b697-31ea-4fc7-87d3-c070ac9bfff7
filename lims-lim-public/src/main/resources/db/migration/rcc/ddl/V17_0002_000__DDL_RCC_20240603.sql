DROP TABLE IF EXISTS `tb_base_qualitylimitdisposition`;
CREATE TABLE `tb_base_qualitylimitdisposition`  (
    `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
    `qcGrade` int(11) NOT NULL COMMENT '质控等级',
    `qcType` int(11) NOT NULL COMMENT '质控类型',
    `formula` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公式',
    `judgingMethod` int(11) NOT NULL COMMENT '评判方式',
    `isAcquiesce` bit(1) NULL DEFAULT NULL COMMENT '是否默认',
    `orgId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组织机构id',
    `creator` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `domainId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    `modifier` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    `modifyDate` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

ALTER TABLE tb_base_qualitycontrollimit
    ADD COLUMN dispositionId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '配置id';