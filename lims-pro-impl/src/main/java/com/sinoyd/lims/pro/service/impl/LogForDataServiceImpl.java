package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.lims.pro.dto.DtoLogForData;
import com.sinoyd.lims.pro.repository.LogForDataRepository;
import com.sinoyd.lims.pro.service.LogForDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 分析数据日志业务接口实现
 *
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/04/22
 */
@Service
@Slf4j
public class LogForDataServiceImpl implements LogForDataService {

    @Autowired
    private LogForDataRepository logForDataRepository;

    @Override
    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<List<DtoLogForData>> getLogForData(List<String> anaDataIds) {
        List<DtoLogForData> logForDataList = logForDataRepository.findByObjectIdIn(anaDataIds);
        return new AsyncResult<>(logForDataList);
    }
}
