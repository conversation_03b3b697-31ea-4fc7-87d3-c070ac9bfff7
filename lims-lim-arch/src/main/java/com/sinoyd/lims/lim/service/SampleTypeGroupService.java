package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.customer.DtoSampleTypeDefaultGroup;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.base.dto.customer.TreeNode;

import java.util.*;

/**
 * 样品分组接口
 *
 * <AUTHOR>
 * @version V1.0.0 2018/12/14
 * @since V100R001
 */
public interface SampleTypeGroupService extends IBaseJpaService<DtoSampleTypeGroup, String> {

    /**
     * 分组规则树
     *
     * @return 样品分组树列表
     */
    List<TreeNode> getNode();

    /**
     * 分组规则下获取分组列表
     *
     * @param parentId 分组规则Id
     * @return 分组列表
     */
    List<DtoSampleTypeGroup> getSampleTypeGroupList(String parentId);

    /**
     * 分组规则下获取测试项目
     *
     * @param sampleTypeId      样品类型id
     * @param sampleTypeGroupId 分组规则id
     * @param isGroup           是否分组 1：是 2：否 -1：所有 (为空默认所有)
     * @param analyzeItem       分析项目名称
     * @param analyzeMethodStd  分析方法名称，标准编号
     * @return 测试项目列表
     */
    List<DtoTest> getTestList(String sampleTypeId, String sampleTypeGroupId, Integer isGroup, String analyzeItem, String analyzeMethodStd, Integer cert);

    /**
     * 设置分组
     *
     * @param sampleTypeGroupId 分组id
     * @param testIds           被分组的测试项目id
     */
    void setSampleTypeGroup(String sampleTypeGroupId, Collection<String> testIds);

    /**
     * 取消分组
     *
     * @param sampleTypeGroupId 分组id
     * @param testIds           被分组的测试项目id
     */
    void cancelSampleTypeGroup(String sampleTypeGroupId, Collection<String> testIds);

    /**
     * 删除分组规则/分组
     *
     * @param id 分组规则/分组id
     */
    void deleteById(String id);

    /**
     * @param sampleTypeId      样品类型
     * @param sampleTypeGroupId 分组id
     * @param anaNameMtdStd     分析项目名称，分析方法名称，标准编号关键字
     * @return 返回相应的测试项目
     */
    List<DtoTest> findTestByGroupId(String sampleTypeId, String sampleTypeGroupId, String anaNameMtdStd, Integer cert);

    /**
     * 根据检测类型大类id列表获取检测类型默认的标签分组
     *
     * @param bigSampleTypeIdList 检测类型的实体对象
     */
    List<DtoSampleTypeDefaultGroup> findDefaultSampleGroup(List<String> bigSampleTypeIdList);

    /**
     * @param id
     */
    void copySampleTypeGroup(String id);

    ;
}