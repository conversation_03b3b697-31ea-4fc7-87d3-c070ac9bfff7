package com.sinoyd.lims.pro.entity;


import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * SampleGroup2Test实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "SampleGroup2Test")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SampleGroup2Test implements BaseEntity, Serializable {


    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 样品分组id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("样品分组id")
    private String sampleGroupId;

    /**
     * 测试项目id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("测试项目id")
    private String testId;

    /**
     * 分析项目Id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析项目Id（Guid）")
    private String analyzeItemId;

    /**
     * 分析方法名称
     */
    @Column(length = 50)
    @ApiModelProperty("分析方法名称")
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    @Column(length = 100)
    @ApiModelProperty("国家标准")
    private String redCountryStandard;

}
