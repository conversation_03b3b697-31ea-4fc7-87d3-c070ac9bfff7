package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.customer.DtoSampleCountStatistic;
import com.sinoyd.lims.pro.view.VProjectCountStatistic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

/**
 * 检测数量统计访问接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface ProjectCountStatisticsRepository extends JpaRepository<VProjectCountStatistic, String> {
    /**
     * 根据登记时间范围返回统计个数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计个数
     */
    @Query(value = "select sum(analyseDataCount) analyseDataCount,count(1) sampleCount from " +
            "(select count(v.analyseDataId) analyseDataCount,v.sampleId from VI_PRO_ProjectYYSampleCountView v " +
            "where v.orgId = :orgId  and v.inceptTime>= :startTime and v.inceptTime< :endTime  group by v.sampleId) a", nativeQuery = true)
    List<Object[]> findYYSampleCountByInceptTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("orgId") String orgId);

    /**
     * 根据采样时间范围返回统计个数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计个数
     */
    @Query(value = "select sum(analyseDataCount) analyseDataCount,count(1) sampleCount from " +
            "(select count(v.analyseDataId) analyseDataCount,v.sampleId from VI_PRO_ProjectYYSampleCountView v " +
            "where v.orgId = :orgId  and v.samplingTime>= :startTime and v.samplingTime< :endTime  group by v.sampleId) a", nativeQuery = true)
    List<Object[]> findYYSampleCountBySamplingTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("orgId") String orgId);

    /**
     * 根据登记时间范围返回统计质控样个数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计个数
     */
    @Query(value = "select sampleTypeId, count(1) as qualityControlCount from VI_PRO_ProjectZKSampleCountView v " +
            "where v.orgId = :orgId  and v.projectTypeId = :projectTypeId  " +
            "and v.inceptTime>= :startTime and v.inceptTime< :endTime  " +
            "group by v.sampleTypeId ", nativeQuery = true)
    List<Object[]> findZKSampleCountByInceptTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                                 @Param("orgId") String orgId,@Param("projectTypeId") String projectTypeId);

    /**
     * 根据采样时间范围返回统计质控样个数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计个数
     */
    @Query(value = "select sampleTypeId, count(1) as qualityControlCount from VI_PRO_ProjectZKSampleCountView v " +
            "where v.orgId = :orgId  and v.projectTypeId = :projectTypeId  " +
            "and v.samplingTime>= :startTime and v.samplingTime< :endTime  " +
            "group by v.sampleTypeId ", nativeQuery = true)
    List<Object[]> findZKSampleCountBySamplingTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                                   @Param("orgId") String orgId,@Param("projectTypeId") String projectTypeId);

    /**
     * 根据登记时间范围返回对应项目类型的统计个数
     *
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param projectTypeId 项目类型id
     * @return 统计个数
     */
    @Query(value = "select a.sampleTypeName,sum(analyseDataCount) analyseDataCount,count(1) sampleCount,a.sampleTypeId from " +
            "(select v.sampleTypeId,v.sampleTypeName,count(v.analyseDataId) analyseDataCount,v.sampleId from VI_PRO_ProjectYYSampleCountView v " +
            "where v.orgId=:orgId and v.inceptTime>=:startTime and v.inceptTime< :endTime and v.projectTypeId= :projectTypeId " +
            "group by v.sampleTypeName,v.sampleTypeId,v.sampleId)  a " +
            "group by a.sampleTypeName,a.sampleTypeId" +
            "order by a.sampleTypeName asc", nativeQuery = true)
    List<Object[]> findProjectSampleCountListByInceptTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("projectTypeId") String projectTypeId, @Param("orgId") String orgId);

    /**
     * 根据采样时间范围返回对应项目类型的统计个数
     *
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param projectTypeId 项目类型id
     * @return 统计个数
     */
    @Query(value = "select a.sampleTypeName,sum(analyseDataCount) analyseDataCount,count(1) sampleCount,a.sampleTypeId from " +
            "(select v.sampleTypeId,v.sampleTypeName,count(v.analyseDataId) analyseDataCount,v.sampleId from VI_PRO_ProjectYYSampleCountView v " +
            "where v.orgId=:orgId and v.samplingTime>=:startTime and v.samplingTime< :endTime and v.projectTypeId= :projectTypeId " +
            "group by v.sampleTypeName,v.sampleTypeId,v.sampleId)  a " +
            "group by a.sampleTypeName,a.sampleTypeId" +
            "order by a.sampleTypeName asc", nativeQuery = true)
    List<Object[]> findProjectSampleCountListBySamplingTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("projectTypeId") String projectTypeId, @Param("orgId") String orgId);

    /**
     * 根据采样时间范围返回列表集合
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计个数
     */
    @Query(value = "select new com.sinoyd.lims.pro.dto.customer.DtoSampleCountStatistic(v.id,v.projectId,v.analyseDataId,v.sampleId,v.samplingTime) from VProjectYYSampleCount v " +
            "where v.samplingTime>= :startTime and v.samplingTime< :endTime")
    List<DtoSampleCountStatistic> findListBySamplingTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
