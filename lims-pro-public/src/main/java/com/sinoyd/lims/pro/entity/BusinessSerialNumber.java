package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * 业务流水号实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/2/29
 */
@MappedSuperclass
@ApiModel(description = "业务流水号实体")
@Data
@EntityListeners(AuditingEntityListener.class)
public class BusinessSerialNumber implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public BusinessSerialNumber() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    @ApiModelProperty("id")
    private String id = UUIDHelper.NewID();

    /**
     * 业务类型，枚举管理，参考枚举 EnumLogObjectType
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 业务id
     */
    @ApiModelProperty("业务id")
    private String businessId;

    /**
     * 业务编号
     */
    @ApiModelProperty("业务编号")
    private String businessNumber;

    /**
     * 流水号类型
     */
    @ApiModelProperty("流水号类型")
    private String serialNumberType;

    /**
     * 流水号参数0
     */
    @ApiModelProperty("流水号参数0")
    private String para0;

    /**
     * 流水号参数1
     */
    @ApiModelProperty("流水号参数1")
    private String para1;

    /**
     * 流水号参数2
     */
    @ApiModelProperty("流水号参数2")
    private String para2;

    /**
     * 流水号参数3
     */
    @ApiModelProperty("流水号参数3")
    private String para3;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 所属机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属机构id")
    private String orgId;

    /**
     * 所属实验室id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室id")
    private String domainId;

    /**
     * 创建人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;


    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}