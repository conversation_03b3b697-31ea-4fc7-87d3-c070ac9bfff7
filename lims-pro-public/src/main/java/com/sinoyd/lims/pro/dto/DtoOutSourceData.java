package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.OutSourceData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 分包数据传输类
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/2/8
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_OutSourceData")
@Where(clause = "isDeleted = 0")
@Data
@Accessors(chain = true)
public class DtoOutSourceData extends OutSourceData {

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 委托单位
     */
    @Transient
    private String customerName;

    /**
     * 受检单位
     */
    @Transient
    private String inspectedEnt;

    /**
     * 点位名称
     */
    @Transient
    private String sampleFolderName;

    /**
     * 样品编号
     */
    @Transient
    private String sampleCode;

    /**
     * 采样日期
     */
    @Transient
    private Date samplingTime;

    /**
     * 检测类型
     */
    @Transient
    private String sampleType;

    /**
     * 分析项目名称
     */
    @Transient
    private String redAnalyzeItemName;

    /**
     * 样品状态
     */
    @Transient
    private String sampleStatus;


    public DtoOutSourceData() {
    }


    public DtoOutSourceData(String sampleType, Date samplingTime, String sampleFolderName, String sampleCode,
                            String redAnalyzeItemName, String analyzeMethodName, String dimensionName, String analyseDataId,
                            String analyzeMethodId, String dimensionId) {
        this.sampleType = sampleType;
        this.samplingTime = samplingTime;
        this.sampleFolderName = sampleFolderName;
        this.sampleCode = sampleCode;
        this.redAnalyzeItemName = redAnalyzeItemName;
        super.setAnalyzeMethodName(analyzeMethodName);
        super.setDimensionName(dimensionName);
        super.setAnalyseDataId(analyseDataId);
        super.setAnalyzeMethodId(analyzeMethodId);
        super.setDimensionId(dimensionId);
    }
}
