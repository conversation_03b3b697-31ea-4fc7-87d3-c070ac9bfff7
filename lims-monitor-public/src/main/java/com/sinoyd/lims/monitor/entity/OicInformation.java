package com.sinoyd.lims.monitor.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;


/**
 * OicInformation实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OicInformation")
 @Data
 public  class OicInformation implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

   public  OicInformation() {
      this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
      this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 所属点位
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("所属点位")
	private String fixedPointId;
    
    /**
    * 仪器名称
    */
    @Column(length=50)
    @ApiModelProperty("仪器名称")
    @Length(message = "仪器名称{validation.message.length}", max = 50)
	private String instrumentName;
    
    /**
    * 仪器型号
    */
    @Column(length=50)
    @ApiModelProperty("仪器型号")
    @Length(message = "仪器型号{validation.message.length}", max = 50)
	private String instrumentModel;
    
    /**
    * 出厂编号
    */
    @Column(length=50)
    @ApiModelProperty("出厂编号")
    @Length(message = "出厂编号{validation.message.length}", max = 50)
	private String instrumentCode;

    /**
     * 出厂编号
     */
    @ApiModelProperty("方法名称")
    @Length(message = "方法名称{validation.message.length}", max = 255)
    private String methodName;

    /**
     * 检出限
     */
    @Column(length=50)
    @ApiModelProperty("检出限")
    @Length(message = "检出限{validation.message.length}", max = 50)
    private String examLimitValue;
    
    /**
    * 量程
    */
    @Column(length=50,name = "\"range\"")
    @ApiModelProperty("量程")
    @Length(message = "量程{validation.message.length}", max = 50)
	private String range;

    /**
     * 制造商
     */
    @ApiModelProperty("制造商")
    @Length(message = "制造商{validation.message.length}", max = 300)
    private String manufacturer;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
 }