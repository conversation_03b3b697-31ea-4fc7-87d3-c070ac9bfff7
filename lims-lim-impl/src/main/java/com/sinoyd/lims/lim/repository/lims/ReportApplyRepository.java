package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoReportApply;

import java.util.Collection;
import java.util.List;


/**
 * ReportApply数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
public interface ReportApplyRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportApply, String> {

    /**
     * 根据报表配置id查询
     *
     * @param reportConfigIds 报表配置id集合
     * @return 报表预埋实体集合
     */
    List<DtoReportApply> findByReportConfigIdIn(Collection<String> reportConfigIds);
}