package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * ConsumableOfMixed实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ConsumableOfMixed")
@Data
public class ConsumableOfMixed implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ConsumableOfMixed() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 标样id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("标样id（Guid）")
    @Length(message = "标样id{validation.message.length}", max = 50)
    private String consumableId;

    /**
     * 分析项目id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析项目id（Guid）")
    @Length(message = "分析项目id{validation.message.length}", max = 50)
    private String analyzeItemId;

    /**
     * 浓度
     */
    @ApiModelProperty("浓度")
    @Length(message = "浓度{validation.message.length}", max = 255)
    private String concentration;

    /**
     * 不确定度
     */
    @ApiModelProperty("不确定度")
    @Length(message = "不确定度{validation.message.length}", max = 255)
    private String uncertainty;

    /**
     * 计量单位id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("计量单位id（Guid）")
    private String dimensionId;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 范围低点
     */
    @Column(length = 50)
    @ApiModelProperty("范围低点")
    private String rangeLow;

    /**
     * 范围高点
     */
    @Column(length = 50)
    @ApiModelProperty("范围高点")
    private String rangeHigh;
}