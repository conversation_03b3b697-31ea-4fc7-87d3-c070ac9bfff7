package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentUseRecordBath;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;

import java.util.Date;
import java.util.List;

/**
 * 仪器维修配置
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface InstrumentUseRecordService extends IBaseJpaService<DtoInstrumentUseRecord, String> {
    /**
     * 获取仪器使用记录
     *
     * @param environmentalManageIds 环境记录id集合
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByEnvironmentalManageIds(List<String> environmentalManageIds);

    /**
     * 根据关联id及类型获取仪器使用记录
     *
     * @param objectId   关联id
     * @param objectType 关联类型
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByObjectIdAndObjectType(String objectId, Integer objectType);

    /**
     * 根据关联id及类型获取仪器使用记录
     *
     * @param objectIds  关联id集合
     * @param objectType 关联类型
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByObjectIdInAndObjectType(List<String> objectIds, Integer objectType);


    /**
     * 保存仪器使用记录
     *
     * @param dtoInstrumentUseRecord 待保存的使用记录
     * @return 返回保存后的以前使用记录
     */
    DtoInstrumentUseRecord saveInstrumentUseRecord(DtoInstrumentUseRecord dtoInstrumentUseRecord);


    /**
     * 保存修改仪器使用记录
     *
     * @param dtoInstrumentUseRecord 待修改的使用记录
     * @return 返回保存后的以前使用记录
     */
    DtoInstrumentUseRecord updateInstrumentUseRecord(DtoInstrumentUseRecord dtoInstrumentUseRecord);

    /**
     * 查询该时间段范围的数据
     *
     * @param instrumentIds 仪器ids
     * @param start         开始时间
     * @param end           结束时间
     * @return 返回数据
     */
    List<DtoInstrumentUseRecord> findByInstrumentIdInAndObjectTypeAndStartTimeBeforeAndEndTimeAfter(
            List<String> instrumentIds,
            Integer objectType
            , Date start, Date end);

    /**
     * 新增仪器使用
     *
     * @param environmentalRecordId 现场仪器记录
     * @param entity                仪器使用
     * @return 仪器使用
     */
    List<DtoInstrumentUseRecord> newSave(String environmentalRecordId, List<DtoInstrumentUseRecord> entityList);

    /**
     * 批量新增仪器使用记录
     *
     * @param useRecordBathList 仪器使用记录及环境记录信息列表
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> newSaveBath(List<DtoInstrumentUseRecordBath> useRecordBathList);

    /**
     * 普通修改
     *
     * @param entity 对象
     * @return 对象
     */
    List<DtoInstrumentUseRecord> newUpdate(DtoInstrumentUseRecord entity);

    Integer newDelete(String id);
}