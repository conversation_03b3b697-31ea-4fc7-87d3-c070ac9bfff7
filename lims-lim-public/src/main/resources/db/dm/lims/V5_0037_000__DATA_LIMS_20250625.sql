-- 报告质控说明表添加现场平行样组件
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate,
                                totalTest, auxiliaryInstrument, conversionCalculationMode, speedCalculationMode,
                                compoundAvgCalculationMode, gasParamSplitMode)
VALUES ('6cadd6ff-c5ca-4cc3-904a-7a121cbe84c3', 'siteParallelStdData', '标准版现场平行样质控说明检测结果表组件', 'siteParallelStdData',
        'dtSiteParallelSource', 10, 0, '', 0, '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000',
        '2025-06-24 23:07:30', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '2025-06-24 23:07:30', '0', '0', 0, 0, 0, 1);

update TB_LIM_ReportModule
set sonTableJson = '["standardStdData", "innerBlankStdData", "OuterBlankStdData", "TransportBlankStdData", "SiteBlankStdData", "EquipBlankStdData", "innerParallelStdData", "markStdData", "replaceStdData", "jhCurveStdData", "siteParallelStdData"]'
where moduleCode = 'qcStdDataSource';