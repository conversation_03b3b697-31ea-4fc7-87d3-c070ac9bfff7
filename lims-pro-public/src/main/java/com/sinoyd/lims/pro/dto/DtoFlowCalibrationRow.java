package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.FlowCalibrationRow;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoFlowCalibrationRow实体
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/14
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_FlowCalibrationRow")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoFlowCalibrationRow extends FlowCalibrationRow {
    /**
     * 参数集合
     */
    @Transient
    private List<DtoFlowCalibrationParamData> paramDataList;
}
