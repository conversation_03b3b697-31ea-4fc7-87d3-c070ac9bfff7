package com.sinoyd.lims.strategy.context;

import com.sinoyd.lims.lim.dto.rcc.DtoItemRelation;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoSample;

import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/29
 */
public interface CheckRelationItem {


    /**
     * 分析项目关系验证
     *
     * @param analyseDataList        分析数据集合
     * @param sampleList             样品集合
     * @param analyseDatas           全分析数据
     * @param paramsList             参数集合
     * @param relationList           分析关系
     * @param itemRelationParamsList 分析关系参数集合
     * @return 分析项目关系验证
     */
    Future<List<String>> initCheckRelationItem(List<DtoAnalyseData> analyseDataList, List<DtoSample> sampleList, List<DtoAnalyseData> analyseDatas,
                                               List<DtoItemRelationParams> paramsList, List<DtoItemRelation> relationList, List<DtoItemRelationParams> itemRelationParamsList);
}
