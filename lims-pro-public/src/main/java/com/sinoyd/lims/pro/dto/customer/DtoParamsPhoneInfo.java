package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.List;

@Data
public class DtoParamsPhoneInfo {
    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 周期
     */
    private Integer cycleOrder;

    /**
     * 是否录入数据
     */
    private Integer isEnterValue;

    /**
     * 公共参数
     */
    private List<DtoParamsDataPhone> paramsConfigList;

    /**
     * 样品参数
     */
    private DtoSampleParamsPhone sampleParamsPhone;
}
