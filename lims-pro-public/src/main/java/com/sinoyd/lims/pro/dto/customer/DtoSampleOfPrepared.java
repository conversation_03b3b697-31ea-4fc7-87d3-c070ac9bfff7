package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.dto.DtoSamplePreparation;
import lombok.Data;

import java.util.List;

/**
 * 样品制备列表实体
 * <AUTHOR>
 * @version V1.0.0 2023/03/21
 * @since V100R001
 */
@Data
public class DtoSampleOfPrepared {

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 样品编号
     */
    private String code;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 检测类型
     */
    private String sampleTypeName;

    /**
     * 采样日期
     */
    private String samplingTimeBegin;

    /**
     * 接样日期
     */
    private String receiveTime;

    /**
     * 分析项目名称
     */
    private String analyzeItemNames;

    /**
     * 样品制备信息
     */
    private List<DtoSamplePreparation> samplePreparations;

}
