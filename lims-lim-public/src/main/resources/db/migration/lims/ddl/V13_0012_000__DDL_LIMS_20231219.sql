CREATE TABLE TB_LIM_DocAuthorityList
(
    id             varchar(50) NOT NULL COMMENT 'id',
    objectId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '文件夹id',
    authCode       varchar(50) NOT NULL DEFAULT '' COMMENT '权限编码（常量Guid，常量名称 LIM_AuthType）',
    authName       varchar(250) NULL DEFAULT '' COMMENT '权限名称',
    defaultOpenInd bit(1) NULL DEFAULT NULL COMMENT '是否默认开启 1：是  0：否',
    sortNum        int(11) NULL DEFAULT NULL COMMENT '排序号',
    orgId          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    PRIMARY KEY (id)
);

ALTER TABLE TB_LIM_DocAuthorityConfig
    ADD COLUMN authorityListId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '文件夹对应权限id';