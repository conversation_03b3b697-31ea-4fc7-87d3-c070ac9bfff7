package com.sinoyd.base.dto.vo;

import javax.xml.bind.annotation.XmlElement;

/**
 * 质控样偏差公式数据源规则配置
 *
 * <AUTHOR>
 * @version 5.2.0
 * @since 2023/05/24
 */
public class DeviationFormulaSrcVO {

    /**
     * 质控类型名称
     */
    private String qualityControlTypeName;

    /**
     * 质控类型编码
     */
    private Integer qualityControlTypeCode;

    /**
     * 质控等级 1：室外 2：室内
     */
    private String qcGrade;

    /**
     * 质控类型
     */
    private String qcType;

    /**
     * 是否可配置偏差公式 1：是 0：否
     */
    private String isDeviationFormula;

    public void setQualityControlTypeName(String qualityControlTypeName) {
        this.qualityControlTypeName = qualityControlTypeName;
    }

    public void setQualityControlTypeCode(Integer qualityControlTypeCode) {
        this.qualityControlTypeCode = qualityControlTypeCode;
    }

    public void setQcGrade(String qcGrade) {
        this.qcGrade = qcGrade;
    }

    public void setQcType(String qcType) {
        this.qcType = qcType;
    }

    public void setIsDeviationFormula(String isDeviationFormula) {
        this.isDeviationFormula = isDeviationFormula;
    }

    @XmlElement(name = "qualityControlTypeName")
    public String getQualityControlTypeName() {
        return qualityControlTypeName;
    }

    @XmlElement(name = "qualityControlTypeCode")
    public Integer getQualityControlTypeCode() {
        return qualityControlTypeCode;
    }

    @XmlElement(name = "qcGrade")
    public String getQcGrade() {
        return qcGrade;
    }

    @XmlElement(name = "qcType")
    public String getQcType() {
        return qcType;
    }

    @XmlElement(name = "isDeviationFormula")
    public String getIsDeviationFormula() {
        return isDeviationFormula;
    }
}
