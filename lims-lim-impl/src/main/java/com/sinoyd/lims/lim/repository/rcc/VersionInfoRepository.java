package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * VersionInfo数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/9
 * @since V100R001
 */
public interface VersionInfoRepository extends IBaseJpaPhysicalDeleteRepository<DtoVersionInfo, String> {

    /**
     * 根据类别和版本号查询数量
     * @param verType
     * @param version
     * @return  查到的数量
     */
    Integer countByVerTypeAndVersion(String verType,String version);

    /**
     * 根据类别和版本号查询实体
     * @param verType
     * @param version
     * @return  实体
     */
    DtoVersionInfo findByVerTypeAndVersion(String verType,String version);

    /**
     * 根据类别查询实体
     * @param verType
     * @return  实体
     */
    List<DtoVersionInfo> findByVerType(String verType);
}