ALTER TABLE tb_base_consumable ADD COLUMN rangeType int NOT NULL DEFAULT 10;
comment on COLUMN tb_base_consumable.rangeType is '范围类型,关联枚举EnumConsumableRangeType';
ALTER TABLE tb_base_consumable ADD COLUMN rangeLow varchar(50);
comment on COLUMN tb_base_consumable.rangeLow is '范围低点';
ALTER TABLE tb_base_consumable ADD COLUMN rangeHigh varchar(50);
comment on COLUMN tb_base_consumable.rangeHigh is '范围高点';
ALTER TABLE tb_base_consumableofmixed ADD COLUMN rangeLow varchar(50);
comment on COLUMN tb_base_consumableofmixed.rangeLow is '范围低点';
ALTER TABLE tb_base_consumableofmixed ADD COLUMN rangeHigh varchar(50);
comment on COLUMN tb_base_consumableofmixed.rangeHigh is '范围高点';