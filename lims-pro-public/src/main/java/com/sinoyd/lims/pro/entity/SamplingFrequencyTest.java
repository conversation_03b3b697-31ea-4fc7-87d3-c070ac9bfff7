package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * SamplingFrequencyTest实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SamplingFrequencyTest")
 @Data
 public  class SamplingFrequencyTest implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

   public SamplingFrequencyTest() {
      this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

   /**
    * 主键id
    */
   @Id
   @Column(length = 50)
   private String id = UUIDHelper.NewID();

   /**
    * 点位频次id
    */
   @Column(length = 50, nullable = false)
   @ApiModelProperty("点位频次id")
   private String samplingFrequencyId;

   /**
    * 点位id
    */
   @Column(length = 50, nullable = false)
   @ApiModelProperty("点位id")
   private String sampleFolderId;

   /**
    * 测试id
    */
   @Column(length = 50, nullable = false)
   @ApiModelProperty("测试id")
   private String testId;

   /**
    * 分析项目id
    */
   @Column(length = 50, nullable = false)
   @ApiModelProperty("分析项目id")
   private String analyseItemId;

   /**
    * 分析方法id
    */
   @Column(length = 50, nullable = false)
   @ApiModelProperty("分析方法id")
   private String analyzeMethodId;

   /**
    * 分析项目名称
    */
   @Column(length = 100)
   @ApiModelProperty("分析项目名称")
   @Length(message = "分析项目名称{validation.message.length}", max = 100)
   private String redAnalyzeItemName;

   /**
    * 分析方法名称
    */
   @Column(length = 255)
   @ApiModelProperty("分析方法名称")
   @Length(message = "分析方法名称{validation.message.length}", max = 255)
   private String redAnalyzeMethodName;

   /**
    * 国家标准
    */
   @Column(length = 255)
   @ApiModelProperty("国家标准")
   @Length(message = "{validation.message.length}", max = 255)
   private String redCountryStandard;

   /**
    * 是否在现场完成(根据实际情况填写)
    */
   @Column(nullable = false)
   @ColumnDefault("0")
   @ApiModelProperty("是否在现场完成(根据实际情况填写)")
   private Boolean isCompleteField;

   /**
    * 是否分包
    */
   @Column(nullable = false)
   @ColumnDefault("0")
   @ApiModelProperty("是否分包")
   private Boolean isOutsourcing;


   /**
    * 是否采测分包
    */
   @Column(nullable = false)
   @ColumnDefault("0")
   @ApiModelProperty("是否采测分包")
   private Boolean isSamplingOut;

   /**
    * 采样方法方法Id（Guid）
   */
   @Column(length = 50, nullable = false)
   @ApiModelProperty("分析方法Id（Guid）")
   private String samplingMethodId;

   /**
    * 组织机构id
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("组织机构id")
   private String orgId;

}