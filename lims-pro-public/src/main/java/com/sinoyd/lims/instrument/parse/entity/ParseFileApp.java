package com.sinoyd.lims.instrument.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 仪器解析应用实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/9/7
 */
@MappedSuperclass
@ApiModel(description = "Instrument Parse App")
@Data
public class ParseFileApp implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ParseFileApp() {
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 应用名称
     */
    @Column(length = 50)
    @ApiModelProperty("应用名称")
    private String appName;

    /**
     * 仪器id
     */
    @Column(length = 50)
    @ApiModelProperty("仪器id")
    private String instrumentId;

    /**
     * 仪器名称
     */
    @Column(length = 250)
    @ApiModelProperty("仪器名称")
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Column(length = 250)
    @ApiModelProperty("仪器编号")
    private String instrumentCode;

    /**
     * 解析类型（1：文件解析 2：数据流解析 3:文件解析调试）
     */
    @Column(length = 50)
    @ApiModelProperty("解析类型（1：文件解析 2：数据流解析 3:文件解析调试）")
    private String parseType;

    /**
     * 方案id
     */
    @Column(length = 50)
    @ApiModelProperty("方案id")
    private String planId;

    /**
     * 监听文件夹
     */
    @Column(length = 250)
    @ApiModelProperty("监听文件夹")
    private String folderName;

    /**
     * 解析成功存放目录
     */
    @Column(length = 250)
    @ApiModelProperty("解析成功存放目录")
    private String sucFolderName;

    /**
     * 解析失败存放目录
     */
    @Column(length = 250)
    @ApiModelProperty("解析失败存放目录")
    private String failFolderName;

    /**
     * 假删字段
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}