package com.sinoyd.lims.api.dto.vo;

import com.sinoyd.lims.instrument.parse.dto.DtoTcpData;
import lombok.Data;

import java.util.List;

@Data
public class ProjectVO {

    /**
     * 项目Id
     */
    private String projectId;

    /**
     * 点位Id
     */
    private String folderId;

    /**
     * 周期
     */
    private Integer periodCount;

    /**
     * ww任务id
     */
    private String taskId;

    /**
     * ww采集单号
     */
    private String collectNo;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 分组id
     */
    private String groupId;

    /**
     * 解析数据
     */
    private WWFolderVO wwFolderVO;
}
