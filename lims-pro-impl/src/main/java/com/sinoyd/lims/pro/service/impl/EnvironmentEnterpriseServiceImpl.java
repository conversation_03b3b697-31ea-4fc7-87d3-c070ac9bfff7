package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.http.HTTPCaller;
import com.sinoyd.common.http.HttpClientUtil;
import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoRecordJson;
import com.sinoyd.lims.pro.entity.ReceiveSampleRecord;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.EnvironmentEnterpriseService;
import com.sinoyd.lims.pro.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 环保企业通项目数据推送接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/31
 * @since V100R001
 */
@Service
@Slf4j
public class EnvironmentEnterpriseServiceImpl implements EnvironmentEnterpriseService {

    /**
     * 应用id
     */
    @Value("${environment-enterprise.appID:0}")
    private String APPID;

    /**
     * 加密参数
     */
    @Value("${environment-enterprise.appSecret:84761749b186d1828ce64c38273ac65b}")
    private String APP_SECRET;

    /**
     * 推送请求地址 https://dnqyt.envchina.com
     */
    @Value("${environment-enterprise.push_gate_url:http://192.168.6.10:9819}")
    private String PUSH_GATE_URL;

    /**
     * 推送请求路径
     */
    @Value("${environment-enterprise.push_url:/api/open/sample-detect/push}")
    private String PUSH_URL;

    /**
     * token验证接口请求路径
     */
    @Value("${environment-enterprise.login_url:/api/open/sample-detect/login}")
    private String LOGIN_URL;

    private static final Integer PROJECT_STATUS_REGISTER = 1;
    private static final Integer PROJECT_STATUS_DISTRIBUTE = 2;
    private static final Integer PROJECT_STATUS_LAUNCH = 3;
    private static final Integer PROJECT_STATUS_REPORT = 6;
    private static final Integer PROJECT_STATUS_FINISH = 7;

    private static final Integer REPORT_STATUS_EDIT = 1;
    private static final Integer REPORT_STATUS_APPROVE = 2;
    private static final Integer REPORT_STATUS_SIGN = 3;
    private static final Integer REPORT_STATUS_FINISH = 4;

    private ReportRepository reportRepository;
    private SampleRepository sampleRepository;
    private DocumentRepository documentRepository;
    private AnalyseDataRepository analyseDataRepository;
    private EnterpriseRepository enterpriseRepository;
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;
    private SampleTypeRepository sampleTypeRepository;
    private SampleFolderRepository sampleFolderRepository;
    private EvaluationRecordRepository evaluationRecordRepository;
    private AnalyzeItemRepository analyzeItemRepository;
    private ProjectPushInfoRepository projectPushInfoRepository;
    private ProjectRepository projectRepository;
    private Project2ReportRepository project2ReportRepository;
    private CodeService codeService;

    @Autowired
    private FilePathConfig filePathConfig;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void pushProjectData(List<DtoProject> projectList, String projectStatus) {
        if (pushSwitch()) {
            List<String> projectCodeList = projectList.stream().map(DtoProject::getProjectCode).collect(Collectors.toList());
            String projectCodeStr = String.join("，", projectCodeList);
            log.info("开始环保企业通项目信息推送,项目编号： " + projectCodeStr);
            if (StringUtil.isNotEmpty(projectList)) {
                try {
                    List<EnvironmentEnterpriseVO> voList = getProjectData(projectList, projectStatus);
                    log.info("项目信息json字符串组装完成：");
                    log.info(JsonUtil.toJson(voList));
                    if (StringUtil.isNotEmpty(voList)) {
                        String secretKey = getSecretKey();
                        log.info("秘钥：" + secretKey);
                        List<NameValuePair> paramList = new ArrayList<>();
                        paramList.add(new NameValuePair("", voList));
                        List<NameValuePair> headerList = new ArrayList<>();
                        headerList.add(new NameValuePair("dn-qyt-token", secretKey));
                        log.info("开始调用项目信息推送接口");
                        String rstStr = HTTPCaller.getInstance(false).postAsString(PUSH_GATE_URL, PUSH_URL, headerList, paramList);
//                    String rstStr = SinoydHttpTool.doPost(PUSH_GATE_URL + PUSH_URL, voList, getHttpHeaders(secretKey));
                        log.info("项目信息推送成功, rstStr = " + rstStr);
                        log.info("开始更新项目推送记录");
                        updatePushRecord(projectList);
                        log.info("更新项目推送记录成功");
                    }
                } catch (Exception e) {
                    log.error("项目信息推送失败 ", e);
                }
            }
            log.info("结束环保企业通项目信息推送,项目编号： " + projectCodeStr);
        }
    }

    /**
     * 校验环保企业通数据推送开关是否开启
     *
     * @return 是否开启
     */
    private boolean pushSwitch() {
        DtoCode dtoCode = codeService.findByCode("PRO_ENVIRONMENT_PROJECT_PUSH");
        return dtoCode != null && "1".equals(dtoCode.getDictValue());
    }

    /**
     * 推送项目
     *
     * @param projectId 项目id
     */
    @Override
    public void push(String projectId) {
        DtoProject project = projectRepository.findOne(projectId);
        if (StringUtil.isNotNull(project)) {
            pushProjectData(Collections.singletonList(project), null);
        }
    }

    /**
     * 下载report文件流
     *
     * @param id    报告文档id
     * @param token token字符串
     * @return report文件流
     */
    @Override
    public byte[] downReport(String id, String token) {
        id = StringUtil.isNotEmpty(id) ? id : "";
        token = StringUtil.isNotEmpty(token) ? token : "";
        log.info("开始环保企业通下载报告，id= " + id + "token= " + token);
        if (StringUtil.isNotEmpty(id) && !UUIDHelper.GUID_EMPTY.equals(id)) {
            try {
                log.info("开始调用token验证接口，token :" + token);
                List<NameValuePair> paramList = new ArrayList<>();
                paramList.add(new NameValuePair("token", token));
                String loginUrl = HttpClientUtil.addParam2Url(LOGIN_URL, paramList);
                String rstStr = HTTPCaller.getInstance(false).postAsString(PUSH_GATE_URL, loginUrl, new ArrayList<>(), new ArrayList<>());
//                List<NameValuePair> paramList = new ArrayList<>();
//                paramList.add(new NameValuePair("token", token));
//                String loginUrl = HttpClientUtil.addParam2Url(LOGIN_URL, paramList);
//                String rstStr = SinoydHttpTool.doPost(PUSH_GATE_URL + loginUrl, new HashMap<>(), getHttpHeaders(""));
                log.info("token验证接口调用成功, rstStr = " + rstStr);
                Map<String, Object> rstMap = StringUtil.isNotNull(rstStr) ? JsonUtil.toObject(rstStr, Map.class) : new HashMap<>();
                Map<String, Object> dataMap = (Map<String, Object>) rstMap.getOrDefault("data", new HashMap<>());
                String corpUsc = dataMap.getOrDefault("corpUscc", "").toString();
                //校验信用代码是否存在
                boolean flag = checkCreditCode(corpUsc);
                if (!flag) {
                    log.info("社会信用代码不存在，corpUscc= " + corpUsc);
                    throw new BaseException("社会信用代码不存在，无法下载");
                }
                byte[] bytes = downReportStream(id);
                log.info("bytes = " + Arrays.toString(bytes));
                log.info("环保企业通下载报告结束，id= " + id + " token= " + token);
                return bytes;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                String msg = (e instanceof BaseException) ? e.getMessage() : "环保企业通下载报告失败";
                throw new BaseException(msg);
            }
        }
        log.info("环保企业通下载报告结束，id= " + id + "token= " + token);
        return new byte[0];
    }

    /**
     * 以文件流形式下载报告
     *
     * @param docId 报告文档id
     * @return 文件内容
     */
    private byte[] downReportStream(String docId) {
        log.info("开始下载报告 id= " + docId);
        DtoDocument document = documentRepository.findOne(docId);
        byte[] bytes = new byte[0];
        if (StringUtil.isNull(document) || document.getIsDeleted()) {
            log.info("报告不存在或已被删除 id= " + docId);
            return bytes;
        }
        InputStream is = null;
        String absPath = filePathConfig.getFilePath() + document.getPath();
        try {
            is = new BufferedInputStream(Files.newInputStream(Paths.get(absPath)));
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            bytes = buffer;
        } catch (IOException e) {
            log.error("报告文件读取失败，id= " + docId, e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("文件流关闭失败", e);
                }
            }
        }
        log.info("下载报告结束 docId= " + docId);
        return bytes;
    }

    /**
     * 校验系统中是否存在给定的社会信用代码
     *
     * @param corpUsc 社会信用代码
     * @return 是否存在
     */
    private boolean checkCreditCode(String corpUsc) {
        log.info("开始校验社会信用代码，corpUscc= " + corpUsc);
        if (StringUtil.isNotNull(corpUsc)) {
            List<DtoEnterprise> enterpriseList = enterpriseRepository.findBySocialCreditCodeAndIsDeletedFalse(corpUsc);
            return StringUtil.isNotEmpty(enterpriseList);
        } else {
            return false;
        }
    }

    /**
     * 更新项目推送记录
     *
     * @param projectList 项目列表
     */
    private void updatePushRecord(List<DtoProject> projectList) {
        List<String> projectIdList = projectList.stream().map(DtoProject::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(projectIdList)) {
            Date pushTime = new Date();
            List<String> existProjectIdList = new ArrayList<>();
            List<DtoProjectPushInfo> existPushInfoList = projectPushInfoRepository.findByProjectIdIn(projectIdList);
            if (StringUtil.isNotEmpty(existPushInfoList)) {
                existPushInfoList.forEach(p -> p.setPushTime(pushTime));
                projectPushInfoRepository.save(existPushInfoList);
                existProjectIdList = existPushInfoList.stream().map(DtoProjectPushInfo::getProjectId).distinct().collect(Collectors.toList());
            }
            projectIdList.removeAll(existProjectIdList);
            if (StringUtil.isNotEmpty(projectIdList)) {
                List<DtoProjectPushInfo> instInfoList = new ArrayList<>();
                projectIdList.forEach(p -> instInfoList.add(new DtoProjectPushInfo(UUIDHelper.NewID(), p, pushTime)));
                projectPushInfoRepository.save(instInfoList);
            }
        }
    }

    /**
     * 获取推送的项目数据json字符串
     *
     * @param projectList 项目列表
     */
    private List<EnvironmentEnterpriseVO> getProjectData(List<DtoProject> projectList, String projectStatus) {
        List<EnvironmentEnterpriseVO> voList = new ArrayList<>();
        List<String> inspectEntIdList = projectList.stream().map(DtoProject::getInspectedEntId).distinct().collect(Collectors.toList());
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findAll(inspectEntIdList);
        Map<String, DtoEnterprise> enterpriseMap = enterpriseList.stream().collect(Collectors.toMap(DtoEnterprise::getId, dto -> dto));
        projectList = projectList.stream().filter(p -> enterpriseMap.containsKey(p.getInspectedEntId())
                && StringUtil.isNotEmpty(enterpriseMap.get(p.getInspectedEntId()).getSocialCreditCode())).collect(Collectors.toList());
        if (StringUtil.isEmpty(projectList)) {
            log.info("项目受检单位社会信用代码为空，无需推送");
            return voList;
        }
        List<String> projectIdList = projectList.stream().map(DtoProject::getId).collect(Collectors.toList());
        List<DtoReport> reportList = reportRepository.findByProjectIdIn(projectIdList);
        List<DtoProject2Report> project2ReportList = getProject2ReportList(projectIdList, reportList);
        Map<String, List<DtoProject2Report>> project2ReportMap = project2ReportList.stream().collect(Collectors.groupingBy(DtoProject2Report::getProjectId));
        List<String> project2ReportIdList = project2ReportList.stream().map(DtoProject2Report::getId).collect(Collectors.toList());
        List<DtoDocument> docList = StringUtil.isNotEmpty(project2ReportIdList) ? documentRepository.findByFolderIdIn(project2ReportIdList) : new ArrayList<>();
        Map<String, List<DtoDocument>> docMap = docList.stream().collect(Collectors.groupingBy(DtoDocument::getFolderId));
        List<DtoSample> sampleList = sampleRepository.findByProjectIdIn(projectIdList);
        List<String> sampleIdList = new ArrayList<>();
        Set<String> receiveIdSet = new HashSet<>(), typeIdSet = new HashSet<>(), folderIdSet = new HashSet<>();
        for (DtoSample sample : sampleList) {
            receiveIdSet.add(sample.getReceiveId());
            sampleIdList.add(sample.getId());
            typeIdSet.add(sample.getSampleTypeId());
            folderIdSet.add(sample.getSampleFolderId());
        }
        List<DtoReceiveSampleRecord> receiveSampleRecordList = StringUtil.isNotEmpty(receiveIdSet) ? receiveSampleRecordRepository.findAll(receiveIdSet) : new ArrayList<>();
        Map<String, DtoSampleFolder> folderMap = StringUtil.isNotEmpty(folderIdSet) ? sampleFolderRepository.findAll(folderIdSet).stream()
                .collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto)) : new HashMap<>();
        Map<String, List<DtoSample>> sampleMap = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getProjectId));
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIdList) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList) : new ArrayList<>();
        Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
        List<String> analyseDataIdList = new ArrayList<>();
        Set<String> itemIdSet = new HashSet<>();
        for (DtoAnalyseData analyseData : analyseDataList) {
            analyseDataIdList.add(analyseData.getId());
            itemIdSet.add(analyseData.getAnalyseItemId());
        }
        Map<String, DtoAnalyzeItem> analyzeItemMap = StringUtil.isNotEmpty(itemIdSet) ? analyzeItemRepository.findAll(itemIdSet).stream()
                .collect(Collectors.toMap(DtoAnalyzeItem::getId, dto -> dto)) : new HashMap<>();
        Map<String, List<DtoEvaluationRecord>> evaluationRecordMap = StringUtil.isNotEmpty(analyseDataIdList)
                ? evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(analyseDataIdList, EnumPRO.EnumEvaluationType.分析数据.getValue(),
                EnumPRO.EnumEvaluationPlan.分析数据.getValue()).stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getObjectId)) : new HashMap<>();
        Map<String, List<DtoReport>> reportMap = reportList.stream().collect(Collectors.groupingBy(DtoReport::getProjectId));
        List<DtoSampleType> typeList = StringUtil.isNotEmpty(typeIdSet) ? sampleTypeRepository.findAll(typeIdSet) : new ArrayList<>();
        for (DtoProject project : projectList) {
            String creditCode = enterpriseMap.get(project.getInspectedEntId()).getSocialCreditCode();
            Integer reportStatus = getStatusForReport(project, reportMap);
            String actProjectStatus = StringUtil.isNotEmpty(projectStatus) ? projectStatus : project.getStatus();
            Integer status = getStatusForProject(actProjectStatus, reportStatus);
            List<DtoSample> sampleForProject = sampleMap.getOrDefault(project.getId(), new ArrayList<>());
            long yetCnt = sampleForProject.stream().filter(p -> StringUtil.isNotEmpty(p.getCode())).count();
            List<String> sampleIdForProject = sampleForProject.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoAnalyseData> dataForProject = analyseDataList.stream().filter(p -> sampleIdForProject.contains(p.getSampleId())).collect(Collectors.toList());
            Map<String, List<DtoAnalyseData>> dataMapForProject = dataForProject.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
            int yetDetectNum = 0;
            for (Map.Entry<String, List<DtoAnalyseData>> entry : dataMapForProject.entrySet()) {
                boolean confirmFlag = entry.getValue().stream().anyMatch(p -> (!p.getIsCompleteField()
                        && !EnumPRO.EnumAnalyseDataStatus.已确认.getValue().equals(p.getDataStatus())) || (p.getIsCompleteField()
                        && !EnumPRO.EnumAnalyseDataStatus.复核通过.getValue().equals(p.getDataStatus())));
                if (!confirmFlag) {
                    yetDetectNum++;
                }
            }
            //获取项目报告信息
            List<EnvironmentEnterpriseReportVO> reportVOList = getReportFilesForProject(project, docMap, project2ReportMap);
            //获取项目采样信息
            List<EnvironmentEnterpriseSamplingVO> samplingVOList = getItemsForProject(project, sampleMap, receiveSampleRecordList, typeList, folderMap,
                    analyseDataMap, evaluationRecordMap, analyzeItemMap);
            String createDateStr = StringUtil.isNotNull(project.getCreateDate()) ? DateUtil.dateToString(project.getCreateDate(), DateUtil.FULL) : "";
            voList.add(new EnvironmentEnterpriseVO(project.getId(), creditCode, project.getProjectCode(), status, reportStatus, createDateStr, project.getIsDeleted(),
                    sampleForProject.size(), (int) yetCnt, sampleForProject.size(), yetDetectNum, reportVOList, samplingVOList));
        }
        return voList;
    }

    /**
     * 获取项目和报告的关联关系
     *
     * @param projectIdList 项目id列表
     * @param reportList    报告列表
     * @return 项目和报告的关联关系
     */
    private List<DtoProject2Report> getProject2ReportList(List<String> projectIdList, List<DtoReport> reportList) {
        List<String> reportIdForProjects = reportList.stream().filter(p -> projectIdList.contains(p.getProjectId())).map(DtoReport::getId).collect(Collectors.toList());
        List<DtoProject2Report> project2ReportList = project2ReportRepository.findByProjectIdIn(projectIdList);
        project2ReportList = project2ReportList.stream().filter(p -> reportIdForProjects.contains(p.getReportId())).collect(Collectors.toList());
        return project2ReportList;
    }

    /**
     * 获取项目采样信息
     *
     * @param project        项目
     * @param sampleMap      样品映射
     * @param recordList     送样单列表
     * @param typeList       样品类型列表
     * @param folderMap      点位映射
     * @param analyseDataMap 分析数据映射
     * @return 项目报告信息
     */
    private List<EnvironmentEnterpriseSamplingVO> getItemsForProject(DtoProject project, Map<String, List<DtoSample>> sampleMap, List<DtoReceiveSampleRecord> recordList,
                                                                     List<DtoSampleType> typeList, Map<String, DtoSampleFolder> folderMap, Map<String, List<DtoAnalyseData>> analyseDataMap,
                                                                     Map<String, List<DtoEvaluationRecord>> evaluationRecordMap, Map<String, DtoAnalyzeItem> analyzeItemMap) {
        List<EnvironmentEnterpriseSamplingVO> voList = new ArrayList<>();
        List<DtoSample> sampleForProject = sampleMap.getOrDefault(project.getId(), new ArrayList<>());
        sampleForProject = sampleForProject.stream().filter(p -> StringUtil.isNotEmpty(p.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())).collect(Collectors.toList());
        List<String> receiveIdForProject = sampleForProject.stream().map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> recordForProject = recordList.stream().filter(p -> receiveIdForProject.contains(p.getId()))
                .sorted(Comparator.comparing(ReceiveSampleRecord::getRecordCode)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(recordForProject)) {
            Map<String, List<DtoSample>> sampleMapForRecord = sampleForProject.stream().collect(Collectors.groupingBy(DtoSample::getReceiveId));
            for (DtoReceiveSampleRecord record : recordForProject) {
                DtoRecordJson json = record.getJson() != null ? JsonIterator.deserialize(record.getJson(), DtoRecordJson.class) : new DtoRecordJson();
                List<String> typeIdList = StringUtil.isNotEmpty(json.getSampleTypeIds()) ? Arrays.asList(json.getSampleTypeIds().split(",")) : new ArrayList<>();
                List<String> typeNameList = typeList.stream().filter(p -> typeIdList.contains(p.getId())).map(DtoSampleType::getTypeName).distinct().collect(Collectors.toList());
                String typeStr = String.join("，", typeNameList);
                //获取送样单样品数据
                List<EnvironmentEnterpriseSampleVO> sampleVOList = getSamplesForRecord(record, sampleMapForRecord, project, folderMap, analyseDataMap,
                        evaluationRecordMap, analyzeItemMap);
                voList.add(new EnvironmentEnterpriseSamplingVO(record.getId(), typeStr, sampleVOList));
            }
        }
        return voList;
    }

    /**
     * 获取送样单样品数据
     *
     * @param record             送样单
     * @param sampleMapForRecord 送样单样品映射
     * @param folderMap          点位映射
     * @return 送样单样品数据
     */
    private List<EnvironmentEnterpriseSampleVO> getSamplesForRecord(DtoReceiveSampleRecord record, Map<String, List<DtoSample>> sampleMapForRecord, DtoProject project,
                                                                    Map<String, DtoSampleFolder> folderMap, Map<String, List<DtoAnalyseData>> analyseDataMap,
                                                                    Map<String, List<DtoEvaluationRecord>> evaluationRecordMap, Map<String, DtoAnalyzeItem> analyzeItemMap) {
        List<EnvironmentEnterpriseSampleVO> sampleVOList = new ArrayList<>();
        List<DtoSample> sampleForRecord = sampleMapForRecord.get(record.getId());
        sampleForRecord.sort(Comparator.comparing(DtoSample::getCode));
        for (DtoSample sample : sampleForRecord) {
            DtoSampleFolder folder = folderMap.getOrDefault(sample.getSampleFolderId(), null);
            String sampleLocation = StringUtil.isNotNull(folder) ? folder.getWatchSpot() : "",
                    stationCode = (StringUtil.isNotNull(folder) && StringUtil.isNotEmpty(folder.getFolderCode())) ? folder.getFolderCode() : "/";
            //获取样品检测数据
            Date samplingDate = sample.getSamplingTimeBegin();
            String samplingDateStr = StringUtil.isNotNull(samplingDate) ? DateUtil.dateToString(samplingDate, DateUtil.FULL) : "";
            List<EnvironmentEnterpriseDataVO> dataVOList = getDetectsForSample(sample, project, analyseDataMap, evaluationRecordMap, analyzeItemMap);
            sampleVOList.add(new EnvironmentEnterpriseSampleVO(sample.getId(), sample.getCode(), samplingDateStr, sampleLocation, sampleLocation, dataVOList));
        }
        return sampleVOList;
    }

    /**
     * 获取样品检测数据
     *
     * @param sample              样品
     * @param project             项目
     * @param analyseDataMap      分析数据映射
     * @param evaluationRecordMap 评价记录映射
     * @return 样品检测数据
     */
    private List<EnvironmentEnterpriseDataVO> getDetectsForSample(DtoSample sample, DtoProject project, Map<String, List<DtoAnalyseData>> analyseDataMap,
                                                                  Map<String, List<DtoEvaluationRecord>> evaluationRecordMap, Map<String, DtoAnalyzeItem> analyzeItemMap) {
        List<EnvironmentEnterpriseDataVO> dataVOList = new ArrayList<>();
        if (analyseDataMap.containsKey(sample.getId())) {
            List<DtoAnalyseData> dataForSample = analyseDataMap.get(sample.getId());
            //仅推送已确认的检测数据
            dataForSample = dataForSample.stream().filter(p -> EnumPRO.EnumAnalyseDataStatus.已确认.getValue().equals(p.getDataStatus())
                    || (EnumPRO.EnumAnalyseDataStatus.复核通过.getValue().equals(p.getDataStatus()) && p.getIsCompleteField())).collect(Collectors.toList());
            dataForSample.sort(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName));
            for (DtoAnalyseData analyseData : dataForSample) {
                String itemId = analyseData.getAnalyseItemId();
                String itemCode = analyzeItemMap.containsKey(itemId) ? analyzeItemMap.get(itemId).getAnalyzeItemCode() : "";
                itemCode = StringUtil.isNotEmpty(itemCode) ? itemCode : "/";
                DtoEvaluationRecord evaluationRecord = evaluationRecordMap.containsKey(analyseData.getId()) ? evaluationRecordMap.get(analyseData.getId()).get(0) : null;
                String minLmt = StringUtil.isNotNull(evaluationRecord) ? evaluationRecord.getLowerLimitValue() : "",
                        maxLmt = StringUtil.isNotNull(evaluationRecord) ? evaluationRecord.getUpperLimitValue() : "";
                String testVal = StringUtil.isNotEmpty(analyseData.getTestValue()) ? (analyseData.getTestValue().replace("<", "").replace("＜", "").replace("L", "")) : "";
                dataVOList.add(new EnvironmentEnterpriseDataVO(analyseData.getId(), itemCode,
                        analyseData.getRedAnalyzeItemName(), testVal, analyseData.getDimension(), "", minLmt, maxLmt));
            }
        }
        return dataVOList;
    }

    /**
     * 获取项目报告信息
     *
     * @param project           项目
     * @param project2ReportMap 项目报告关系映射
     * @param docMap            报告文档映射
     * @return 项目报告信息
     */
    private List<EnvironmentEnterpriseReportVO> getReportFilesForProject(DtoProject project, Map<String, List<DtoDocument>> docMap,
                                                                         Map<String, List<DtoProject2Report>> project2ReportMap) {
        List<EnvironmentEnterpriseReportVO> reportVOList = new ArrayList<>();
        if (project2ReportMap.containsKey(project.getId())) {
            List<DtoProject2Report> project2ReportList = project2ReportMap.get(project.getId());
            for (DtoProject2Report project2Report : project2ReportList) {
                if (docMap.containsKey(project2Report.getId())) {
                    List<DtoDocument> documentList = docMap.get(project2Report.getId());
                    documentList = documentList.stream().sorted(Comparator.comparing(DtoDocument::getCreateDate).reversed()).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(documentList)) {
                        reportVOList.add(new EnvironmentEnterpriseReportVO(documentList.get(0).getFilename(), documentList.get(0).getId()));
                    }
                }
            }
        }
        return reportVOList;
    }

    /**
     * 获取项目状态
     *
     * @param projectStatus 项目状态
     * @param reportStatus  报告映射
     * @return 项目状态 1登记 2派发 3开展中 6报告 7完成
     */
    private Integer getStatusForProject(String projectStatus, Integer reportStatus) {
        if (reportStatus == null) {
            return EnumPRO.EnumProjectStatus.开展中.name().equals(projectStatus) ? PROJECT_STATUS_LAUNCH :
                    (EnumPRO.EnumProjectStatus.项目登记中.name().equals(projectStatus) ? PROJECT_STATUS_REGISTER : PROJECT_STATUS_DISTRIBUTE);
        } else {
            return (REPORT_STATUS_FINISH.equals(reportStatus)) ? PROJECT_STATUS_FINISH : PROJECT_STATUS_REPORT;
        }
    }

    /**
     * 获取报告状态
     *
     * @param project   项目
     * @param reportMap 报告映射
     * @return 报告状态 1报告编制 2报告审核 3报告签发 4报告完成
     */
    private Integer getStatusForReport(DtoProject project, Map<String, List<DtoReport>> reportMap) {
        if (reportMap.containsKey(project.getId())) {
            boolean signFlag = false, approveFlag = false;
            List<DtoReport> reportForProject = reportMap.get(project.getId());
            for (DtoReport report : reportForProject) {
                if (EnumPRO.EnumReportState.编制报告中.name().equals(report.getStatus())
                        || EnumPRO.EnumReportState.报告未通过.name().equals(report.getStatus())) {
                    return REPORT_STATUS_EDIT;
                }
                approveFlag = EnumPRO.EnumReportState.报告审核中.name().equals(report.getStatus())
                        || EnumPRO.EnumReportState.报告复核中.name().equals(report.getStatus()) || approveFlag;
                signFlag = EnumPRO.EnumReportState.报告签发中.name().equals(report.getStatus()) || signFlag;
            }
            return approveFlag ? REPORT_STATUS_APPROVE : (signFlag ? REPORT_STATUS_SIGN : REPORT_STATUS_FINISH);
        }
        return null;
    }

    /**
     * 获取秘钥
     */
    private String getSecretKey() {
        log.info("开始获取秘钥 a：" + APPID);
        String time = DateUtil.nowTime("yyyyMMddHHmmss");
        log.info("当前时间 b：" + time);
        log.info("appSecret：" + APP_SECRET);
        String c = APPID + time + APP_SECRET;
        log.info("c= " + c);
        c = getMD5(c);
        log.info("md5加密后 c= " + c);
        return APPID + "." + time + "." + c;
    }

    /**
     * md5加密算法
     *
     * @param input 原字符串
     * @return 返回32位加密字符串
     */
    private static String getMD5(String input) {
        byte[] source = input.getBytes(StandardCharsets.UTF_8);
        return getMD5(source);
    }

    /**
     * md5加密算法
     *
     * @param source 原字符字节
     * @return 返回32位加密字符串
     */
    public static String getMD5(byte[] source) {
        String s = null;
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            //获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            //使用指定的字节更新摘要
            mdInst.update(source);
            //获得密文
            byte[] digest = mdInst.digest();
            char[] str = new char[digest.length * 2];
            int k = 0;
            for (byte byte0 : digest) {
                //定义转换成16进制的算法，0xf是低四位为1其余为0的数，与运算为取出低四位
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            s = new String(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return s;
    }

    @Autowired
    public void setReportRepository(ReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    public void setReceiveSampleRecordRepository(ReceiveSampleRecordRepository receiveSampleRecordRepository) {
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setEvaluationRecordRepository(EvaluationRecordRepository evaluationRecordRepository) {
        this.evaluationRecordRepository = evaluationRecordRepository;
    }

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    public void setProjectPushInfoRepository(ProjectPushInfoRepository projectPushInfoRepository) {
        this.projectPushInfoRepository = projectPushInfoRepository;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setProject2ReportRepository(Project2ReportRepository project2ReportRepository) {
        this.project2ReportRepository = project2ReportRepository;
    }

    @Autowired
    @Lazy
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }
}
