package com.sinoyd.lims.lim.service.impl;

import java.util.Collection;
import java.util.List;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup2Test;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroup2TestRepository;
import com.sinoyd.lims.lim.service.SampleTypeGroup2TestService;

import org.springframework.stereotype.Service;

/**
 * 样品模板分组
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@Service
public class SampleTypeGroup2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleTypeGroup2Test, String, SampleTypeGroup2TestRepository> implements SampleTypeGroup2TestService {

    @Override
    public List<DtoSampleTypeGroup2Test> getList(String sampleTypeGroupId, Collection<String> testIds) {
        List<DtoSampleTypeGroup2Test> list = repository.getList(sampleTypeGroupId, testIds);
        return list;
    }

    @Override
    public List<DtoSampleTypeGroup2Test> findBySampleTypeGroupId(String sampleTypeGroupId) {
        List<DtoSampleTypeGroup2Test> list = repository.findBySampleTypeGroupId(sampleTypeGroupId);
        return list;
    }

    @Override
    public List<DtoSampleTypeGroup2Test> findBySampleTypeGroupIds(List<String> sampleTypeGroupIds) {
        List<DtoSampleTypeGroup2Test> list = repository.findBySampleTypeGroupIds(sampleTypeGroupIds);
        return list;
    }
}