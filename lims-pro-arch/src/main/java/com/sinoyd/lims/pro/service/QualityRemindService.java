package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.customer.DtoQualityRemind;

import java.util.List;

/**
 * 质控提醒操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface QualityRemindService {
    /**
     * 查询检测单的质控提醒
     *
     * @param workSheetFolderId 检测单id
     */
    List<DtoQualityRemind> findByWorkSheetFolderId(String workSheetFolderId);

    /**
     * 查询送样单的质控提醒
     *
     * @param receiveIds 送样单id集合
     */
    List<DtoQualityRemind> findByReceiveIds(List<String> receiveIds);
}
