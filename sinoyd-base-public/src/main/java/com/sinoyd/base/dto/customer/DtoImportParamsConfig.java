package com.sinoyd.base.dto.customer;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 原始记录单数据参数导入导出  poi实体
 * <AUTHOR>
 * @version V1.0.0 2023/10/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DtoImportParamsConfig extends PoiBaseEntity{


    /**
     * 序号
     */
    @Excel(name = "序号",orderNum = "1",width = 20)
    private Integer index;

    /**
     *  原始记录单参数名称
     */
    @Excel(name = "原始记录单参数名称",orderNum = "10",width = 20)
    private String paramsConfigName;

    /**
     *  分析项目
     */
    @Excel(name = "分析项目",orderNum = "20",width = 20)
    private String analyzeItem;

    /**
     *  分析方法
     */
    @Excel(name = "分析方法",orderNum = "30",width = 20)
    private String analyzeMethod;

    /**
     *  检测类型
     */
    @Excel(name = "检测类型",orderNum = "31",width = 20)
    private String sampleType;

    /**
     *  公式
     */
    @Excel(name = "公式",orderNum = "40",width = 20)
    private String formula;

    /**
     *  列参数
     */
    @Excel(name = "列参数",orderNum = "50",width = 20)
    private String columnParams;

    /**
     *  关联公式参数
     */
    @Excel(name = "关联公式参数",orderNum = "60",width = 20)
    private String relatedParam;

    /**
     *  量纲
     */
    @Excel(name = "量纲",orderNum = "70",width = 20)
    private String dimension;


    /**
     *  有效位数
     */
    @Excel(name = "有效位数",orderNum = "80",width = 20)
    private Integer mostSignificance;

    /**
     *  小数位数
     */
    @Excel(name = "小数位数",orderNum = "90",width = 20)
    private Integer mostDecimal;

    /**
     *  配置状态
     */
    @Excel(name = "配置状态",orderNum = "100",width = 20)
    private String configStatus;
}
