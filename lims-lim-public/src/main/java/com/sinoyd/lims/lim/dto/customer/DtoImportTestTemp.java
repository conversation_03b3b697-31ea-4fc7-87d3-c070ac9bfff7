package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.*;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 导入测试项目临时存存储实体
 */
@Data
public class DtoImportTestTemp {

    /**
     * 测试项目
     */
    private List<DtoTest> testTemps = new ArrayList<>();

    /**
     * 分析方法
     */
    private List<DtoAnalyzeMethod> analyzeMethodTemps = new ArrayList<>();
    /**
     * 分析项目
     */
    private List<DtoAnalyzeItem> analyzeItemTemps = new ArrayList<>();

    /**
     * 量纲
     */
    private List<DtoDimension> dimensionTemps = new ArrayList<>();

    /**
     * 参数
     */
    private List<DtoParams> paramsTemps = new ArrayList<>();

    /**
     * 测试项目拓展
     */
    private List<DtoTestExpand> testExpandTemps = new ArrayList<>();

    /**
     * 质控限值
     */
    private List<DtoQualityControlLimit> qualityControlLimitTemps = new ArrayList<>();

    /**
     * 公式配置
     */
    private List<DtoParamsFormula> paramsFormulaTemps = new ArrayList<>();

    /**
     * 公式参数数据
     */
    private List<DtoParamsTestFormula> paramsTestFormulaTemps = new ArrayList<>();

    /**
     * 测得量公式
     */
    private List<DtoParamsPartFormula> paramsPartFormulaTemps = new ArrayList<>();
}
