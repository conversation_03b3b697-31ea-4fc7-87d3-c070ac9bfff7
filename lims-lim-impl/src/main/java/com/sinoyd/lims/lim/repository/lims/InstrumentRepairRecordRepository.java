package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentRepairRecord;

import java.util.List;

/**
 * 仪器维修记录操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
public interface InstrumentRepairRecordRepository extends IBaseJpaPhysicalDeleteRepository<DtoInstrumentRepairRecord, String> {
    /**
     *  根据申请单id查询维修记录
     * @param purchaseApplyID 维修申请单id
     * @return 维修记录集合
     */
    List<DtoInstrumentRepairRecord> findByPurchaseApplyID(String purchaseApplyID);

    /**
     *  根据仪器id查询维修记录
     * @param instrumentIds 仪器id集合
     * @return 维修记录集合
     */
    List<DtoInstrumentRepairRecord> findByInstrumentIdIn(List<String> instrumentIds);
}