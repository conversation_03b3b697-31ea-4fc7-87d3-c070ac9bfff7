package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.lims.pro.dto.DtoSurvey;
import com.sinoyd.lims.pro.repository.SurveyRepository;
import com.sinoyd.lims.pro.service.SurveyService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.stereotype.Service;


/**
 * Survey操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class SurveyServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSurvey,String,SurveyRepository> implements SurveyService {

    @Override
    public void findByPage(PageBean<DtoSurvey> pb, BaseCriteria surveyCriteria) {
        pb.setEntityName("DtoSurvey a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, surveyCriteria);
    }
}