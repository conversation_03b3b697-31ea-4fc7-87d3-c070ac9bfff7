package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSampleReserve;

import java.util.List;

/**
 * SampleReserveRepository数据访问操作接口
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/20
 */
public interface SampleReserveRepository extends IBaseJpaRepository<DtoSampleReserve,String> {
    /**
     * 根据样品Id查询数据
     *
     * @param sampleIds 样品Id
     * @return 查询结果
     */
    List<DtoSampleReserve> findBySampleIdIn(List<String> sampleIds);

    /**
     * 根据样品Id查询数据
     *
     * @param sampleId 样品Id
     * @return 查询结果
     */
    List<DtoSampleReserve> findBySampleId(String sampleId);

}
