package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig;
import lombok.Data;

import java.util.Date;

/**
 * 质控提醒结果
 * <AUTHOR>
 * @version V1.0.0 2020/1/14
 * @since V100R001
 */
@Data
public class DtoTestQCRemindTemp {
    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 质控比例
     */
    private Integer qcRemindPercent;

    /**
     * 排序值
     */
    private Long orderNum;

    /**
     * 测试项目id
     */
    private String testId;

    public DtoTestQCRemindTemp(){

    }

    /**
     * 获取测试项目对应质控提醒用
     * @param qcGrade 质控等级
     * @param qcType 质控类型
     * @param qcRemindPercent 质控比例
     * @param modifyDate 修改时间
     * @param testId 测试项目id
     */
    public DtoTestQCRemindTemp(Integer qcGrade,Integer qcType,Integer qcRemindPercent,Date modifyDate,String testId) {
        this.qcGrade = qcGrade;
        this.qcType = qcType;
        this.qcRemindPercent = qcRemindPercent;
        this.orderNum = modifyDate.getTime();
        this.testId = testId;
    }

    /**
     * 获取测试项目对应质控提醒用
     * @param config 配置
     * @param testId 测试项目id
     */
    public DtoTestQCRemindTemp(DtoTestQCRemindConfig config,String testId) {
        this.qcGrade = config.getQcGrade();
        this.qcType = config.getQcType();
        this.qcRemindPercent = config.getQcRemindPercent();
        this.orderNum = DateUtil.stringToDate("1753-01-01 00:00:00", DateUtil.FULL).getTime() - config.getQcRemindPercent();
        this.testId = testId;
    }
}
