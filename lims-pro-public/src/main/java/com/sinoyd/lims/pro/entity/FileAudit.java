package com.sinoyd.lims.pro.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * FileAudit实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "FileAudit")
@Data
public class FileAudit extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 名称
     */
    @Column(length = 500,nullable = false)
    private String title;

    /**
     * 申请类型，关联枚举EnumFileAuditApplyType
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    private Integer type;

    /**
     * 文件目录
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String folderId;

    /**
     * 内容描述
     */
    @Column(length = 2000)
    private String description;

    /**
     * 文件标识
     */
    @Column(length = 2000)
    private String documentIds;

    /**
     * 文件名称,多个英文逗号拼接
     */
    @Column(length = 4000)
    private String fileName;

    /**
     * 是否设定启用日期
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean isSetValidDate;

    /**
     * 启用日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @JsonFormat(pattern = "yyyy-mm-dd")
    private Date validDate;

    /**
     * 发放范围
     */
    @Column(length = 500)
    private String issueRange;

    /**
     * 修编部门标识
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String reviseDeptId;

    /**
     * 修编人标识
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String revisePersonId;

    /**
     * 参与评审人员，多个guid英文逗号拼接
     */
    @Column(length = 2000)
    private String auditPeople;

    /**
     * 技术或质量负责人
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String techLeaderId;

    /**
     * 批准人员
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String approvePersonId;

    /**
     * 接收人员，多个guid英文逗号拼接
     */
    @Column(length = 2000)
    private String receivePeople;

    /**
     * 备案人员
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String registerPersonId;

    /**
     * 模块编码（枚举EnumFileAuditStep）
     */
    @Column(length = 50,nullable = false)
    private String stepCode;

    /**
     * 状态，关联枚举EnumFileAuditStatus
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    private Integer stepStatus;

    /**
     * 发起人
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    private Date createDate;

    /**
     * 发起时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    private Date startDate;

    /**
     * 通过时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    private Date passDate;
}