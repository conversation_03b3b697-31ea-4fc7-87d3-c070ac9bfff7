package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Table;
import javax.validation.constraints.Pattern;

/**
 * 质控限值导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/6
 * @since V100R001
 */
@Table(name = "TB_BASE_QualityControlLimit")
@Data
public class DtoExportQualityControlLimit extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id;

    /**
     * 测试标识
     */
    @Excel(name = "测试标识", orderNum = "20")
    private String testId;

    /**
     * 数值范围
     */
    @Excel(name = "数值范围", orderNum = "30")
    private String rangeConfig;

    /**
     * 评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）
     */
    @Excel(name = "评判方式", orderNum = "40")
    @Pattern(regexp = "^-?\\d+$", message = "评判方式必须为整数")
    private String judgingMethod;

    /**
     * 绝对偏差
     */
    @Excel(name = "绝对偏差", orderNum = "50")
    private String allowLimit;

    /**
     * 质控等级（枚举EnumQCGrade：1.外部质控  2.内部质控）
     */
    @Excel(name = "质控等级", orderNum = "60")
    @Pattern(regexp = "^-?\\d+$", message = "质控等级必须为整数")
    private String qcGrade;

    /**
     * 质控类型（枚举EnumQCType：1.平行 2.空白 4.加标 8.标样）
     */
    @Excel(name = "质控类型", orderNum = "70")
    @Pattern(regexp = "^-?\\d+$", message = "质控类型必须为整数")
    private String qcType;

    /**
     * 质控类型名称
     */
    @Excel(name = "质控类型名称", orderNum = "80")
    private String qcTypeName;

    /**
     * 代替物id
     */
    @Excel(name = "代替物id", orderNum = "90")
    private String substituteId;

    /**
     * 代替物名称
     */
    @Excel(name = "代替物名称", orderNum = "100")
    private String substituteName;

    /**
     * 穿透率公式
     */
    @Excel(name = "穿透率公式", orderNum = "110")
    private String formula;

    /**
     * 检查项（枚举EnumCheckItemType:1.出证结果，2.公式参数）
     */
    @Excel(name = "检查项", orderNum = "180")
    @Pattern(regexp = "^-?\\d+$", message = "检查项必须为整数")
    private String checkItem;

    /**
     * 检查项内容
     */
    @Excel(name = "检查项内容", orderNum = "190")
    private String checkItemOther;

    /**
     * 是否需要检查项（1.是，2.否）
     */
    @Excel(name = "是否需要检查项1.是，2.否）", orderNum = "200")
    @Pattern(regexp = "^-?\\d+$", message = "是否需要检查项必须为整数")
    private String isCheckItem;


    /**
     * 验证状态 0未验证 1已验证
     */
    @Excel(name = "验证状态", orderNum = "210")
    @Pattern(regexp = "^-?\\d+$", message = "验证状态必须为整数")
    private String validate;

    /**
     * 使用次数
     */
    @Excel(name = "使用次数", orderNum = "220")
    @Pattern(regexp = "^-?\\d+$", message = "使用次数必须为整数")
    private String usageNum;
}
