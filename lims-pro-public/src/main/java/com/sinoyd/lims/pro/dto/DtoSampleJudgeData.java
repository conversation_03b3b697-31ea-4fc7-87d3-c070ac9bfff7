package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SampleJudgeData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoSampleJudgeData实体
 * <AUTHOR>
 * @version V1.0.0 2023/06/13
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SampleJudgeData")
@Data
@DynamicInsert
public class DtoSampleJudgeData extends SampleJudgeData {

    /**
     * 样品编号
     */
    @Transient
    private String sampleCode;

    /**
     * 量纲名称
     */
    @Transient
    private String dimensionName;

    @Transient
    private String analyzeItemName;

    @Transient
    private String folderName;

    @Transient
    private String sampleName;

    @Transient
    private Integer sampleCategory;

    @Transient
    private String folderId;

    @Transient
    private String folderPass;

    @Transient
    private String remark;

    @Transient
    private String resultEvaluate;

    @Transient
    private String yySampleCode;

    @Transient
    private Integer orderNum;

    /**
     * 是否关联报告
     */
    @Transient
    private Boolean isConnectReport;

    @Transient
    private Boolean isSave;
}
