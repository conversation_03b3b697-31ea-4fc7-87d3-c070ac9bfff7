package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.CodeServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * 待检工作单，已完成工作单，待确认工作单的查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年12月27日
 * @since   V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkSheetFolderAwaitCriteria  extends BaseCriteria implements Serializable {

    /**
     * 分析人员id 复核人 确认人
     */
    private String personId;

    /**
     * 项目关键字
     */
    private String projectKey;

    /**
     * 样品关键字
     */
    private String sampleKey;


    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 开始时间
     */
    private String endTime;

    /**
     * 检测类型的id
     */
    private String sampleTypeId;

    /**
     * 是否显示客户信息
     */
    private Boolean isShowCustomer = true;

    /**
     * 采样开始时间
     */
    private String samplingTimeBegin;

    /**
     * 采样结束时间
     */
    private String samplingTimeEnd;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and ").append("analyzeTime").append(" >= :from");
            values.put("from", from);
        }
        if (StringUtils.isNotNullAndEmpty(endTime)) {
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(DateUtil.stringToDate(this.endTime, DateUtil.YEAR));
            endCalendar.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and ").append("analyzeTime").append(" < :to");
            this.values.put("to", endCalendar.getTime());
        }

        if (StringUtils.isNotNullAndEmpty(projectKey)) {
            StringBuilder stringBuilder = new StringBuilder(" and exists(select 1 from DtoAnalyseData a,DtoSample b,");
            stringBuilder.append("DtoReceiveSampleRecord r,DtoProject p");
            stringBuilder.append(" where w.id=a.workSheetFolderId and a.sampleId=b.id and b.receiveId=r.id and r.projectId=p.id");
            stringBuilder.append(" and a.isDeleted=0 and b.isDeleted=0 and p.isDeleted=0");
            if (StringUtils.isNotNullAndEmpty(sampleKey)) {
                stringBuilder.append(" and (b.code like :sampleKey or b.inspectedEnt like :sampleKey)");
                values.put("sampleKey", "%" + this.sampleKey + "%");
            }
            if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
                stringBuilder.append(" and b.sampleTypeId =:sampleTypeId");
                values.put("sampleTypeId", this.sampleTypeId);
            }
            if (StringUtil.isNotEmpty(this.samplingTimeBegin)) {
                Date from = DateUtil.stringToDate(this.samplingTimeBegin, DateUtil.YEAR);
                stringBuilder.append(" and b.samplingTimeBegin >= :samplingTimeBegin");
                values.put("samplingTimeBegin", from);
            }
            if (StringUtil.isNotEmpty(this.samplingTimeEnd)) {
                Date to = DateUtil.stringToDate(this.samplingTimeEnd, DateUtil.YEAR);
                Calendar c = Calendar.getInstance();
                c.setTime(to);
                c.add(Calendar.DAY_OF_MONTH, 1);
                stringBuilder.append(" and b.samplingTimeBegin < :samplingTimeEnd");
                values.put("samplingTimeEnd", c.getTime());
            }
            stringBuilder.append(" and (p.projectCode like :projectKey or p.projectName like :projectKey))");
            condition.append(stringBuilder);
            values.put("projectKey", "%" + this.projectKey + "%");
        } else if (StringUtils.isNotNullAndEmpty(sampleKey)) {
            StringBuilder stringBuilder = new StringBuilder(" and exists(select 1 from DtoAnalyseData a,DtoSample b");
            stringBuilder.append(" where w.id=a.workSheetFolderId and a.sampleId=b.id and a.isDeleted=0 and b.isDeleted=0 ");
            if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
                stringBuilder.append(" and b.sampleTypeId =:sampleTypeId");
                values.put("sampleTypeId", this.sampleTypeId);
            }
            if (StringUtil.isNotEmpty(this.samplingTimeBegin)) {
                Date from = DateUtil.stringToDate(this.samplingTimeBegin, DateUtil.YEAR);
                stringBuilder.append(" and b.samplingTimeBegin >= :samplingTimeBegin");
                values.put("samplingTimeBegin", from);
            }
            if (StringUtil.isNotEmpty(this.samplingTimeEnd)) {
                Date to = DateUtil.stringToDate(this.samplingTimeEnd, DateUtil.YEAR);
                Calendar c = Calendar.getInstance();
                c.setTime(to);
                c.add(Calendar.DAY_OF_MONTH, 1);
                stringBuilder.append(" and b.samplingTimeBegin < :samplingTimeEnd");
                values.put("samplingTimeEnd", c.getTime());
            }
            if (isShowCustomer){
                stringBuilder.append(" and (b.code like :sampleKey or b.inspectedEnt like :sampleKey))");
            }else{
                stringBuilder.append(" and b.code like :sampleKey)");
            }

            condition.append(stringBuilder);
            values.put("sampleKey", "%" + this.sampleKey + "%");
        } else if ((StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY))
                || StringUtil.isNotEmpty(this.samplingTimeBegin) || StringUtil.isNotEmpty(this.samplingTimeEnd)) {
            StringBuilder stringBuilder = new StringBuilder(" and exists(select 1 from DtoAnalyseData a,DtoSample b");
            stringBuilder.append(" where w.id=a.workSheetFolderId and a.sampleId=b.id and a.isDeleted=0 and b.isDeleted=0 ");
            if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
                stringBuilder.append(" and b.sampleTypeId =:sampleTypeId");
                values.put("sampleTypeId", this.sampleTypeId);
            }
            if (StringUtil.isNotEmpty(this.samplingTimeBegin)) {
                Date from = DateUtil.stringToDate(this.samplingTimeBegin, DateUtil.YEAR);
                stringBuilder.append(" and b.samplingTimeBegin >= :samplingTimeBegin");
                values.put("samplingTimeBegin", from);
            }
            if (StringUtil.isNotEmpty(this.samplingTimeEnd)) {
                Date to = DateUtil.stringToDate(this.samplingTimeEnd, DateUtil.YEAR);
                Calendar c = Calendar.getInstance();
                c.setTime(to);
                c.add(Calendar.DAY_OF_MONTH, 1);
                stringBuilder.append(" and b.samplingTimeBegin < :samplingTimeEnd");
                values.put("samplingTimeEnd", c.getTime());
            }
            stringBuilder.append(")");
            condition.append(stringBuilder);
        }
        return condition.toString();
    }
}
