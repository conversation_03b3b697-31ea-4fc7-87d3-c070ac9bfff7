package com.sinoyd.lims.pro.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * OrderContractAchievement2Person实体
 * <AUTHOR>
 * @version V1.0.0 2023/3/13
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="OrderContractAchievementDetails")
@Data
public class OrderContractAchievementDetails implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 人员绩效id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("人员绩效id")
    private String achievementId;

    /**
     * 合同编号
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("合同编号")
    @Excel(name = "合同编号",needMerge = true,orderNum = "20",width = 11)
    private String contractCode;

    /**
     * 合同名称
     */
    @Column(nullable=false)
    @ApiModelProperty("合同名称")
    @Excel(name = "合同名称",needMerge = true,orderNum = "30",width = 11)
    private String contractName;

    /**
     * 合同性质
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("合同性质")
    @Excel(name = "合同性质",needMerge = true,orderNum = "80",width = 11)
    private String contractNature;

    /**
     * 甲方名称
     */
    @Column(nullable=false)
    @ApiModelProperty("甲方名称")
    @Excel(name = "甲方名称",needMerge = true,orderNum = "40",width = 11)
    private String firstEntName;

    /**
     * 合同金额
     */
    @ApiModelProperty("合同金额")
    @Excel(name = "合同金额",needMerge = true,orderNum = "50",width = 11)
    private BigDecimal totalAmount;

    /**
     * 签订日期
     */
    @ApiModelProperty("签订日期")
    @Excel(name = "签订日期",exportFormat = "yyyy-MM-dd",needMerge = true,orderNum = "60",width = 11)
    private Date signDate;

    /**
     * 签订人员
     */
    @Column(length=4000)
    @ApiModelProperty("签订人员")
    private String signPersonId;

}
