package com.sinoyd.lims.pro.view;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 验证方案的视图
 */
@Entity
@Table(name = "VI_PRO_CheckProjectScheme")
@Data
public class VCheckProjectScheme {

    /**
     * 主键id
     */
    @Id
    private String id;

    /**
     * 数量
     */
    private Integer cou;


    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 组织机构id
     */
    private String orgId;
}
