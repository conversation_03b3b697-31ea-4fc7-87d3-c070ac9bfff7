package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * EnterpriseExtend实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="EnterpriseExtend")
 @Data
 public  class EnterpriseExtend implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  EnterpriseExtend() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 企业Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("企业Id")
	private String entId;
    
    /**
    * 污染源类型（常量 LIM_PollutionSourceType 多个类型用;隔开）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("污染源类型（常量 LIM_PollutionSourceType 多个类型用;隔开")
    @Length(message = "污染源类型{validation.message.length}", max = 300)
    private String pollutionSourceType;
    
    /**
    * 是否使用（账号）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否使用（账号）")
    private Boolean isUsed;
    
    /**
    * 关注程度（污染源）（EnumAttentionDegree：1：国控，2：省控，4：市控，8：区控）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("关注程度（污染源）（EnumAttentionDegree：1：国控，2：省控，4：市控，8：区控）")
    private Integer attentionDegree;
    
    /**
    * 是否违约（客户）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否违约（客户）")
    private Boolean isBreak;
    
    /**
    * 分包费率（客户）
    */
    @Column(length=50)
    @ApiModelProperty("分包费率（客户）")
    @Length(message = "分包费率（客户）{validation.message.length}", max = 50)
    private String subRate;
    
    /**
    * 委托费率（客户）
    */
    @Column(length=50)
    @ApiModelProperty("委托费率（客户）")
    @Length(message = "委托费率（客户）{validation.message.length}", max = 50)
    private String entrustRate;
    
    /**
    * 违约信息（客户）
    */
    @Column(length=1000)
    @ApiModelProperty("违约信息（客户）")
    @Length(message = "违约信息（客户）{validation.message.length}", max = 1000)
    private String breakInfo;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;

    /**
     * 污染源编号
     */
    @Column(length=50)
    @ApiModelProperty("污染源编号")
    @Length(message = "污染源编号{validation.message.length}", max = 50)
    private String pollutionCode;
    
 }