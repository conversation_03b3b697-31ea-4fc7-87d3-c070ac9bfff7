package com.sinoyd.lims.pro.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * ReportAchievementDetails实体
 * <AUTHOR>
 * @version V1.0.0 2023/3/13
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="ReportAchievementDetails")
@Data
public class ReportAchievementDetails implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 人员绩效id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("人员绩效id")
    private String achievementId;

    /**
     * 报告id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告id")
    private String reportId;

    /**
     * 报告编号
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告编号")
    @Excel(name = "报告编号",needMerge = true,orderNum = "10",width = 11)
    private String reportCode;

    /**
     * 报告类型id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告类型id")
    private String reportTypeId;

    /**
     * 报告编制人id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告编制人id")
    private String reportPersonId;

    /**
     * 编制日期
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("编制日期")
    @Excel(name = "编制日期",exportFormat = "yyyy-MM-dd",needMerge = true,orderNum = "40",width = 11)
    private Date reportTime;

    /**
     * 项目编号
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目编号")
    @Excel(name = "项目编号",needMerge = true,orderNum = "50",width = 11)
    private String projectCode;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    @Excel(name = "项目名称",needMerge = true,orderNum = "60",width = 11)
    private String projectName;

    /**
     * 委托方id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("委托方id")
    private String entId;

    /**
     * 委托方名称
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("委托方名称")
    @Excel(name = "委托方",needMerge = true,orderNum = "70",width = 11)
    private String entName;


}
