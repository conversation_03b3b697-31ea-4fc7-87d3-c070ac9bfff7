package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;
/**
 *   测试项目公式导入（新增）easypoi实体
 *   对应模板sheet3
 */
@Data
public class DtoImportTestFormulaRevise extends PoiBaseEntity {
    /**
     * id
     */
    @Excel(name = "id", orderNum = "100", width = 35)
    private String id;

    /**
     * 检测类型
     */
    @Excel(name = "检测类型", orderNum = "200", width = 15)
    private String sampleType;

    /**
     * 分析项目
     */
    @Excel(name = "分析项目", orderNum = "300", width = 15)
    private String analyzeItem;

    /**
     * 分析方法
     */
    @Excel(name = "分析方法", orderNum = "400", width = 50)
    private String analyzeMethod;

    /**
     * 检测类型（公式）
     */
    @Excel(name = "检测类型（公式）", orderNum = "500", width = 20)
    private String sampleTypeForFormula;

    /**
     * 公式
     */
    @Excel(name = "公式", orderNum = "600", width = 45)
    private String formula;


    /**
     * 修约公式
     */
    @Excel(name = "公式", groupName = "修约公式", orderNum = "1300", fixedIndex = 6, width = 30)
    private String reviseFormula;

    /**
     * 有效位数
     */
    @Excel(name = "有效位数", groupName = "修约公式", orderNum = "1400", fixedIndex = 7, width = 10)
    private Integer reviseMostSignificance;

    /**
     * 小数位数
     */
    @Excel(name = "小数位数", groupName = "修约公式", orderNum = "1500", fixedIndex = 8, width = 10)
    private Integer reviseMostDecimal;

}
