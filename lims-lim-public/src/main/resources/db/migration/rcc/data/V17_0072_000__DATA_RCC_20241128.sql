-- 批量更新质控限值公式配置 a-b 改成b-a
UPDATE tb_base_qualitylimitdisposition set formula = '(b-a)/((a+b)/2)*100%' where formula = '(a-b)/((a+b)/2)*100%';
UPDATE tb_base_qualitylimitdisposition set formula = '(b-a)/(a+b)*100%' where formula = '(a-b)/(a+b)*100%';
UPDATE tb_base_qualitylimitdisposition set formula = '(b-a)/a*100%' where formula = '(a-b)/a*100%';
UPDATE tb_base_qualitylimitdisposition set formula = '|(b-a)/((a+b)/2)|*100%' where formula = '|(a-b)/((a+b)/2)|*100%';
UPDATE tb_base_qualitylimitdisposition set formula = '|(b-a)/(a+b)|*100%' where formula = '|(a-b)/(a+b)|*100%';
UPDATE tb_base_qualitylimitdisposition set formula = '|(b-a)/a|*100%' where formula = '|(a-b)/a|*100%';
UPDATE tb_base_qualitylimitdisposition set formula = 'b-a' where formula = 'a-b';
UPDATE tb_base_qualitylimitdisposition set formula = '|b-a|' where formula = '|a-b|';
UPDATE tb_base_qualitycontrollimit set formula = '(b-a)/((a+b)/2)*100%' where formula = '(a-b)/((a+b)/2)*100%';
UPDATE tb_base_qualitycontrollimit set formula = '(b-a)/(a+b)*100%' where formula = '(a-b)/(a+b)*100%';
UPDATE tb_base_qualitycontrollimit set formula = '(b-a)/a*100%' where formula = '(a-b)/a*100%';
UPDATE tb_base_qualitycontrollimit set formula = '|(b-a)/((a+b)/2)|*100%' where formula = '|(a-b)/((a+b)/2)|*100%';
UPDATE tb_base_qualitycontrollimit set formula = '|(b-a)/(a+b)|*100%' where formula = '|(a-b)/(a+b)|*100%';
UPDATE tb_base_qualitycontrollimit set formula = '|(b-a)/a|*100%' where formula = '|(a-b)/a|*100%';
UPDATE tb_base_qualitycontrollimit set formula = 'b-a' where formula = 'a-b';
UPDATE tb_base_qualitycontrollimit set formula = '|b-a|' where formula = '|a-b|';