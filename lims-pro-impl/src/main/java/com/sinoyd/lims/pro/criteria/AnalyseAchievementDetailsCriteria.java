package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * analyseAchievementDetails查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyseAchievementDetailsCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 绩效id
     */
    private String achievementId;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 样品编号，点位名称
     */
    private String sampleCode;


    private String itemKey;

    private String methodKey;

    @Override
    public String getCondition() {
        values.clear();
        Calendar calendar = new GregorianCalendar();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.personId)) {
            condition.append(" and a.analystId = :personId");
            values.put("personId", this.personId);
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.analyzeTime >= :startTime");
            values.put("startTime", date);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.analyzeTime < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(this.sampleCode)) {
            condition.append(" and a.sampleCode like :sampleCode");
            values.put("sampleCode", "%" + this.sampleCode + "%");
        }
        if (StringUtil.isNotEmpty(this.achievementId)) {
            condition.append(" and a.achievementId = :achievementId");
            values.put("achievementId", this.achievementId);
        }
        if (StringUtil.isNotEmpty(this.itemKey)) {
            condition.append(" and a.analyzeItemName like :itemKey");
            values.put("itemKey", "%" + this.itemKey + "%");
        }
        if (StringUtil.isNotEmpty(this.methodKey)) {
            condition.append(" and (a.analyzeMethodName = :methodKey or a.countryStandard like :methodKey)");
            values.put("methodKey", "%" + this.methodKey + "%");
        }
        return condition.toString();
    }
}
