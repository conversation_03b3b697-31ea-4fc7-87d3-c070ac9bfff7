package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 监测数据返回实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
@Data
public class DtoMonitorData  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 测试项目列表
     */
    private List<Map<String, Object>> test = new ArrayList<>();

    /**
     * 数据
     */
    private List<Map<String, Object>> analyseData = new ArrayList<>();
}
