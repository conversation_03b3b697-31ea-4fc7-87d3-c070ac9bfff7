package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;


/**
 * SamplingPersonConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SamplingPersonConfig")
 @Data
 public  class SamplingPersonConfig implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  SamplingPersonConfig() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 对象Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("对象Id")
	private String objectId;
    
    /**
    * 对象类型(枚举EnumSamplingType：0.任务1.送样单)
    */
    @Column(nullable=false)
    @ApiModelProperty("对象类型(枚举EnumSamplingType：0.任务1.送样单)")
	private Integer objectType;
    
    /**
    * 采样人Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("采样人Id")
	private String samplingPersonId;
    
    /**
    * 采样人
    */
    @Column(length=50)
    @ApiModelProperty("采样人")
    @Length(message = "采样人{validation.message.length}", max = 50)
	private String samplingPerson;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }