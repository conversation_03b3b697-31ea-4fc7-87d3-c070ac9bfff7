package com.sinoyd.base.dto.customer;

import com.sinoyd.frame.base.entity.BaseEntity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.*;

/**
 * 标准样品信息传输实体
 * <AUTHOR>
 * @version V1.0.0 2019/4/19
 * @since V100R001
 */
@Data
public  class DtoStandardTemp implements BaseEntity
{
    private  Integer id;

    private String consumableName;

    private String codeInStation;

    private String consumableCode;

    private String unit;

    private String unitId;

    private String grade;

    private String gradeName;

    private String categoryId;

    private String category;

    private BigDecimal warningNum;

    private String sendWarnUserId;

    private String sendWarnUser;

    private Boolean isPoison;

    private String dilutedSolution;

    private String dilutionMethod;

    private String concentration;

    private String uncertainty;

    private String keepCondition;

    private Boolean isMixedStandard;

    private String safetyInstruction;

    private String remark;

    private BigDecimal inventory;

    private BigDecimal unitPrice;

    private String productionCode;

    private String manufacturerName;

    private Date storageDate;

    private Date expiryDate;

    private String checkerId;

    private String checker;

    private String supplierId;

    private String supplierName;

    private Integer checkerResult;

    private Date purchasingDate;

    private String appearance;

    private String checkItem;

    private String buyReason;

    private String keepPlace;

    private Map<String, Object> dbExtendMap;
}