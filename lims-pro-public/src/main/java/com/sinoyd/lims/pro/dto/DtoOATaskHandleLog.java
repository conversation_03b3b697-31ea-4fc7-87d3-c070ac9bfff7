package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.OATaskHandleLog;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoOATaskHandleLog实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_OATaskHandleLog")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoOATaskHandleLog extends OATaskHandleLog {

    /**
     * 组装说明
     */
    @Transient
    private String allComment;

}