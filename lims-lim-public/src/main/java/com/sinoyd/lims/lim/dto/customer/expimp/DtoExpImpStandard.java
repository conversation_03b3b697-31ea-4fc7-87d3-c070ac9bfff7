package com.sinoyd.lims.lim.dto.customer.expimp;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 标准物质导入导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DtoExpImpStandard extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id（新增时不填）", orderNum = "5", width = 20)
    private String id;

    @Excel(name = "标样编号(批号)(必填)",orderNum = "10",width = 15)
    private String consumableCode;

    @Excel(name = "证书编号",orderNum = "20",width = 17)
    private String codeInStation;

    @Excel(name = "标准样品/消耗品名称(必填)",orderNum = "30",width = 22)
    private String consumableName;

    @Excel(name = "标准样品/消耗品分类(必填)",orderNum = "40",width = 22)
    private String categoryId;

    @Excel(name = "规格",orderNum = "50",width = 13)
    private String specification;

    @Excel(name = "等级",orderNum = "60",width = 13)
    private String grade;

    @Excel(name = "库存数量(必填)",orderNum = "70",width = 17)
    private String inventory;

    @Excel(name = "单位",orderNum = "80",width = 12)
    private String unit;

    private String unitId;

    @Excel(name = "库存警告数量",orderNum = "90",width = 14)
    private String warningNum;

    @Excel(name = "单价",orderNum = "100",width = 12)
    private String unitPrice;

    @Excel(name = "化合物名称",orderNum = "105",width = 12)
    private String compoundName;

    @Excel(name = "浓度",orderNum = "110",width = 10)
    private String standard;

    @Excel(name = "不确定度",orderNum = "120",width = 10)
    private String uncertainty;

    @Excel(name = "范围低点",orderNum = "124",width = 10)
    private String rangeLow;

    @Excel(name = "范围高点",orderNum = "128",width = 10)
    private String rangeHigh;

    @Excel(name = "浓度量纲",orderNum = "130",width = 10)
    private String dimensionName;

    @Excel(name = "不确定度类型",orderNum = "140",width = 10)
    private String uncertainTypeName;

    private String dimensionId;

    @Excel(name = "定值日期",orderNum = "150",width = 20)
    private String orderTime;

    @Excel(name = "入库日期(必填)",orderNum = "200",width = 20)
    private String storageDate;

    @Excel(name = "有效日期(必填)",orderNum = "300",width = 20)
    private String expiryDate;

    @Excel(name = "是否实验室加密(必填)",orderNum = "350",width = 20)
    private String isLabEncryption;

    @Excel(name = "管理员(必填)",orderNum = "400",width = 20)
    private String sendWarnUserName;

    @Excel(name = "存放位置",orderNum = "500",width = 11)
    private String keepPlace;

    @Excel(name = "备注",orderNum = "600",width = 11)
    private String reMark;

    @Excel(name = "稀释液",orderNum = "700",width = 17)
    private String dilutedSolution;

    @Excel(name = "稀释方法",orderNum = "800",width = 17)
    private String dilutionMethod;

    @Excel(name = "保存条件",orderNum = "900",width = 22)
    private String keepCondition;

    @Excel(name = "安全须知",orderNum = "1000",width = 22)
    private String safetyInstruction;

    @Excel(name = "生产批号",orderNum = "1100",width = 22)
    private String productionCode;

    @Excel(name = "生产厂商",orderNum = "1200",width = 22)
    private String manufacturerName;

    @Excel(name = "供应厂商",orderNum = "1250",width = 22)
    private String supplierName;

    private Boolean isStandard = true;
}
