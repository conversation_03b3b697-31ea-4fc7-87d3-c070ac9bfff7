package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.SerialIdentifierConfigCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.service.SerialIdentifierConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * SerialIdentifierConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @Api(tags = "可配置编号: 可配置编号服务")
 @RestController
 @RequestMapping("api/mobile/serialIdentifierConfig")
 @Validated
 public class MobileSerialIdentifierConfigController extends BaseJpaController<DtoSerialIdentifierConfig, String,SerialIdentifierConfigService> {


    /**
     * 分页动态条件查询可配置编号
     *
     * @param serialIdentifierConfigCriteria 条件参数
     * @return 可配置编号的相关信息
     */
    @ApiOperation(value = "分页动态条件查询可配置编号", notes = "分页动态条件查询可配置编号")
    @GetMapping
    public RestResponse<List<DtoSerialIdentifierConfig>> findByPage(SerialIdentifierConfigCriteria serialIdentifierConfigCriteria) {
        PageBean<DtoSerialIdentifierConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoSerialIdentifierConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, serialIdentifierConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }
}