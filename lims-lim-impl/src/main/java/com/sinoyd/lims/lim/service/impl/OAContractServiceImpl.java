package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOAContract;
import com.sinoyd.lims.lim.repository.lims.OAContractRepository;
import com.sinoyd.lims.lim.service.OAContractService;
import org.springframework.stereotype.Service;


/**
 * OAContract操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/9/28
 * @since V100R001
 */
 @Service("OAContractApplyServiceImpl")
public class OAContractServiceImpl extends BaseJpaServiceImpl<DtoOAContract,String, OAContractRepository> implements OAContractService {

    @Override
    public void findByPage(PageBean<DtoOAContract> pb, BaseCriteria oAContractCriteria) {
        pb.setEntityName("DtoOAContract a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, oAContractCriteria);
    }
}