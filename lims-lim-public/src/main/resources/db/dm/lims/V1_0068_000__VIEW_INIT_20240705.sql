CREATE VIEW "TB_VIEW_DATA"
            ("id", "redanalyzeitemname", "redanalyzemethodname", "redcountrystandard", "status", "dimension",
             "analystname", "analyzetime", "code", "redfoldername", "redanalyzeitems", "inspectedent",
             "samplingtimebegin", "samplestatus", "typename")
AS
select "a"."id"                   AS "id",
       "a"."redAnalyzeItemName"   AS "redanalyzeitemname",
       "a"."redAnalyzeMethodName" AS "redanalyzemethodname",
       "a"."redCountryStandard"   AS "redcountrystandard",
       "a"."status"               AS "status",
       "a"."dimension"            AS "dimension",
       "a"."analystName"          AS "analystname",
       "a"."analyzeTime"          AS "analyzetime",
       "s"."code"                 AS "code",
       "s"."redFolderName"        AS "redfoldername",
       "s"."redAnalyzeItems"      AS "redanalyzeitems",
       "s"."inspectedEnt"         AS "inspectedent",
       "s"."samplingTimeBegin"    AS "samplingtimebegin",
       "s"."status"               AS "samplestatus",
       "st"."typeName"            AS "typename"
from ((("tb_pro_analysedata" "a" join "tb_pro_sample" "s"
        on ((("a"."sampleId" = "s"."id") and ("a"."isDeleted" = 0)))) join "tb_lim_test" "t"
       on (("a"."testId" = "t"."id"))) join "tb_base_sampletype" "st" on (("st"."id" = "t"."sampleTypeId")));
CREATE VIEW "VI_PRO_ANALYSEDATAOFPROJECT" ("id")
AS
select distinct "a"."workSheetFolderId" AS "id"
from ("tb_pro_sample" "s" join "tb_pro_analysedata" "a"
      on ((("a"."sampleId" = "s"."id") and ("s"."status" <> '样品检毕'))))
where exists(select 1 from "tb_pro_project" "p" where (("s"."projectId" = "p"."id") and ("p"."status" = '已办结')));
CREATE VIEW "VI_PRO_CHECKPROJECTSCHEME" ("cou", "id", "redFolderName", "projectId", "orgId")
AS
select count("b"."id")     AS "cou",
       "a"."id"            AS "id",
       "a"."redFolderName" AS "redFolderName",
       "a"."projectId"     AS "projectId",
       "a"."orgId"         AS "orgId"
from ("tb_pro_sample" "a" left join "tb_pro_analysedata" "b"
      on ((("a"."isDeleted" = 0) and ("a"."id" = "b"."sampleId") and ("b"."isDeleted" = 0))))
group by "a"."id", "a"."redFolderName", "a"."redAnalyzeItems", "a"."projectId", "a"."orgId";
CREATE VIEW "VI_PRO_PROJECTCOUNTSTATISTICVIEW"
            ("id", "projectId", "projectTypeId", "projectTypeName", "parentName", "inceptTime", "samplingTime", "orgId")
AS
select NEWID()                 AS "id",
       "p"."id"                AS "projectId",
       "pt"."id"               AS "projectTypeId",
       "pt"."name"             AS "projectTypeName",
       "ptp"."name"            AS "parentName",
       "p"."inceptTime"        AS "inceptTime",
       "s"."samplingTimeBegin" AS "samplingTime",
       "p"."orgId"             AS "orgId"
from ((("tb_pro_project" "p" join "tb_lim_projecttype" "pt"
        on ((("p"."isDeleted" = 0) and ("p"."projectTypeId" = "pt"."id") and
             ("p"."parentId" = '00000000-0000-0000-0000-000000000000')))) left join "tb_lim_projecttype" "ptp"
       on (("ptp"."id" = "pt"."parentId"))) left join "tb_pro_sample" "s" on (("s"."projectId" = "p"."id")));
CREATE VIEW "VI_PRO_PROJECTYYSAMPLECOUNTVIEW"
            ("id", "projectId", "inceptTime", "analyseDataId", "sampleId", "sampleTypeId", "sampleTypeName",
             "projectTypeId", "isDeleted", "samplingTime", "orgId")
AS
select NEWID()                             AS "id",
       "tb_pro_sample"."projectId"         AS "projectId",
       "tb_pro_project"."inceptTime"       AS "inceptTime",
       "tb_pro_analysedata"."id"           AS "analyseDataId",
       "tb_pro_analysedata"."sampleId"     AS "sampleId",
       "tb_pro_sample"."sampleTypeId"      AS "sampleTypeId",
       "tb_base_sampletype"."typeName"     AS "sampleTypeName",
       "tb_pro_project"."projectTypeId"    AS "projectTypeId",
       "tb_pro_analysedata"."isDeleted"    AS "isDeleted",
       "tb_pro_sample"."samplingTimeBegin" AS "samplingTime",
       "tb_pro_sample"."orgId"             AS "orgId"
from ((("tb_pro_sample" join "tb_pro_project" on (("tb_pro_sample"."projectId" = "tb_pro_project"."id"))) join "tb_pro_analysedata"
       on (("tb_pro_sample"."id" = "tb_pro_analysedata"."sampleId"))) join "tb_base_sampletype"
      on (("tb_pro_sample"."sampleTypeId" = "tb_base_sampletype"."id")))
where (("tb_pro_analysedata"."isDeleted" = 0) and ("tb_pro_sample"."isDeleted" = 0) and
       ("tb_pro_project"."isDeleted" = 0) and ("tb_pro_sample"."sampleCategory" = 0))
union all
select NEWID()                          AS "id",
       "b"."projectId"                  AS "projectId",
       "tb_pro_project"."inceptTime"    AS "inceptTime",
       "tb_pro_analysedata"."id"        AS "analyseDataId",
       "tb_pro_analysedata"."sampleId"  AS "sampleId",
       "b"."sampleTypeId"               AS "sampleTypeId",
       "tb_base_sampletype"."typeName"  AS "sampleTypeName",
       "tb_pro_project"."projectTypeId" AS "projectTypeId",
       "tb_pro_analysedata"."isDeleted" AS "isDeleted",
       "b"."samplingTimeBegin"          AS "samplingTime",
       "b"."orgId"                      AS "orgId"
from (((("tb_pro_sample" "a" join "tb_pro_sample" "b" on (("a"."associateSampleId" = "b"."id"))) join "tb_pro_project"
        on (("b"."projectId" = "tb_pro_project"."id"))) join "tb_pro_analysedata"
       on (("a"."id" = "tb_pro_analysedata"."sampleId"))) join "tb_base_sampletype"
      on (("b"."sampleTypeId" = "tb_base_sampletype"."id")))
where (("a"."isDeleted" = 0) and ("b"."isDeleted" = 0) and ("tb_pro_project"."isDeleted" = 0) and
       ("a"."sampleCategory" = 2));
CREATE VIEW "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"
            ("id", "projectId", "inceptTime", "analyseDataId", "sampleId", "sampleTypeId", "sampleTypeName",
             "projectTypeId", "isDeleted", "samplingTime", "orgId")
AS
select NEWID()                 AS "id",
       "s"."projectId"         AS "projectId",
       "p"."inceptTime"        AS "inceptTime",
       "a"."id"                AS "analyseDataId",
       "a"."sampleId"          AS "sampleId",
       "s"."sampleTypeId"      AS "sampleTypeId",
       "st"."typeName"         AS "sampleTypeName",
       "p"."projectTypeId"     AS "projectTypeId",
       "a"."isDeleted"         AS "isDeleted",
       "s"."samplingTimeBegin" AS "samplingTime",
       "s"."orgId"             AS "orgId"
from (((("tb_pro_sample" "qs" join "tb_pro_sample" "s"
         on ((("qs"."associateSampleId" = "s"."id") and ("qs"."isDeleted" = 0) and ("s"."isDeleted" = 0) and
              ("qs"."sampleCategory" = 1)))) join "tb_pro_project" "p"
        on ((("s"."projectId" = "p"."id") and ("p"."isDeleted" = 0)))) join "tb_pro_analysedata" "a"
       on ((("qs"."id" = "a"."sampleId") and ("a"."qcType" not in (16, 32))))) join "tb_base_sampletype" "st"
      on (("s"."sampleTypeId" = "st"."id")));
CREATE VIEW "VI_PRO_QCSAMPLEVIEW"
            ("id", "code", "redFolderName", "redAnalyzeItems", "samplingPersonId", "sampleTypeId", "inceptTime",
             "status", "associateSampleId", "qcGrade", "qcType", "associateSampleCode", "samplingPersonName",
             "sampleTypeName", "bigSampleTypeId", "orgId")
AS
select "s"."id"                AS "id",
       "s"."code"              AS "code",
       "s"."redFolderName"     AS "redFolderName",
       "s"."redAnalyzeItems"   AS "redAnalyzeItems",
       "s"."samplingPersonId"  AS "samplingPersonId",
       "s"."sampleTypeId"      AS "sampleTypeId",
       "s"."inceptTime"        AS "inceptTime",
       "s"."status"            AS "status",
       "s"."associateSampleId" AS "associateSampleId",
       "qc"."qcGrade"          AS "qcGrade",
       "qc"."qcType"           AS "qcType",
       "qs"."code"             AS "associateSampleCode",
       "p"."cName"             AS "samplingPersonName",
       "st"."typeName"         AS "sampleTypeName",
       "st"."parentId"         AS "bigSampleTypeId",
       "s"."orgId"             AS "orgId"
from (((("tb_pro_sample" "s" join "tb_lim_person" "p"
         on ((("s"."samplingPersonId" = "p"."id") and ("s"."isDeleted" = 0) and
              ("s"."sampleCategory" = 1)))) left join "tb_pro_sample" "qs"
        on (("s"."associateSampleId" = "qs"."id"))) join "tb_base_sampletype" "st"
       on (("st"."id" = "s"."sampleTypeId"))) left join "tb_pro_qualitycontrol" "qc" on (("s"."qcId" = "qc"."id")));
COMMENT
ON VIEW "TB_VIEW_DATA" IS 'VIEW';

COMMENT
ON COLUMN "TB_VIEW_DATA"."analystname" IS '分析人员';

COMMENT
ON COLUMN "TB_VIEW_DATA"."analyzetime" IS '数据分析时间';

COMMENT
ON COLUMN "TB_VIEW_DATA"."code" IS '样品编号';

COMMENT
ON COLUMN "TB_VIEW_DATA"."dimension" IS '单位（字符串）';

COMMENT
ON COLUMN "TB_VIEW_DATA"."id" IS 'id';

COMMENT
ON COLUMN "TB_VIEW_DATA"."inspectedent" IS '受检单位';

COMMENT
ON COLUMN "TB_VIEW_DATA"."redanalyzeitemname" IS '分析项目名称';

COMMENT
ON COLUMN "TB_VIEW_DATA"."redanalyzeitems" IS '冗余-分析项目';

COMMENT
ON COLUMN "TB_VIEW_DATA"."redanalyzemethodname" IS '分析方法名称';

COMMENT
ON COLUMN "TB_VIEW_DATA"."redfoldername" IS '冗余-点位';

COMMENT
ON COLUMN "TB_VIEW_DATA"."samplestatus" IS '样品状态（过程状态，枚举字符串EnumSampleStatus）';

COMMENT
ON COLUMN "TB_VIEW_DATA"."samplingtimebegin" IS '采样开始时间';

COMMENT
ON COLUMN "TB_VIEW_DATA"."status" IS '数据状态（字符串,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）';

COMMENT
ON COLUMN "TB_VIEW_DATA"."typename" IS '类型名称';

COMMENT
ON VIEW "VI_PRO_ANALYSEDATAOFPROJECT" IS 'VIEW';

COMMENT
ON COLUMN "VI_PRO_ANALYSEDATAOFPROJECT"."id" IS '工作单Id';

COMMENT
ON VIEW "VI_PRO_CHECKPROJECTSCHEME" IS 'VIEW';

COMMENT
ON COLUMN "VI_PRO_CHECKPROJECTSCHEME"."id" IS 'id';

COMMENT
ON COLUMN "VI_PRO_CHECKPROJECTSCHEME"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "VI_PRO_CHECKPROJECTSCHEME"."projectId" IS '项目id';

COMMENT
ON COLUMN "VI_PRO_CHECKPROJECTSCHEME"."redFolderName" IS '冗余-点位';

COMMENT
ON VIEW "VI_PRO_PROJECTCOUNTSTATISTICVIEW" IS 'VIEW';

COMMENT
ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."inceptTime" IS '委托时间';

COMMENT
ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."parentName" IS '项目类型名称';

COMMENT
ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."projectId" IS 'id';

COMMENT
ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."projectTypeId" IS 'id';

COMMENT
ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."projectTypeName" IS '项目类型名称';

COMMENT
ON COLUMN "VI_PRO_PROJECTCOUNTSTATISTICVIEW"."samplingTime" IS '采样开始时间';

COMMENT
ON VIEW "VI_PRO_PROJECTYYSAMPLECOUNTVIEW" IS 'VIEW';

COMMENT
ON VIEW "VI_PRO_PROJECTZKSAMPLECOUNTVIEW" IS 'VIEW';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."analyseDataId" IS 'id';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."inceptTime" IS '委托时间';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."projectId" IS '项目id';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."projectTypeId" IS '项目类型id（外键）';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."sampleId" IS '样品Id';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."sampleTypeName" IS '类型名称';

COMMENT
ON COLUMN "VI_PRO_PROJECTZKSAMPLECOUNTVIEW"."samplingTime" IS '采样开始时间';

COMMENT
ON VIEW "VI_PRO_QCSAMPLEVIEW" IS 'VIEW';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."associateSampleCode" IS '样品编号';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."associateSampleId" IS '质控样的原样Id';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."bigSampleTypeId" IS '父节点（Guid）';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."code" IS '样品编号';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."id" IS 'id';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."inceptTime" IS '样品登记时间';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."qcGrade" IS '质控等级（枚举EnumQCGrade：0.外部质控  1.内部质控）';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."qcType" IS '质控类型（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."redAnalyzeItems" IS '冗余-分析项目';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."redFolderName" IS '冗余-点位';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."sampleTypeId" IS '检测类型id';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."sampleTypeName" IS '类型名称';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."samplingPersonId" IS '采样人id';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."samplingPersonName" IS '姓名';

COMMENT
ON COLUMN "VI_PRO_QCSAMPLEVIEW"."status" IS '样品状态（过程状态，枚举字符串EnumSampleStatus）';

