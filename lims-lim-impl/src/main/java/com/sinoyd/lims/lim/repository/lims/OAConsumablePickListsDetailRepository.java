package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePickListsDetail;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;


/**
 * OAConsumablePickListsDetail数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/4/2
 * @since V100R001
 */
public interface OAConsumablePickListsDetailRepository extends IBaseJpaPhysicalDeleteRepository<DtoOAConsumablePickListsDetail, String> {

    /**
     * 根据消耗品ID查询消耗品领料清单明细
     *
     * @param consumableId 消耗品ID
     * @return 消耗品领料清单明细列表
     */
    List<DtoOAConsumablePickListsDetail> findByConsumableIdIn(Collection<String> consumableId);

}