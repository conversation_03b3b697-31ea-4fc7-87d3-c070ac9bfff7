package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoEnterpriseEvaluate;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 供应商评价信息Repository
 *
 * <AUTHOR>
 * @version V1.0.0 2024/3/14
 * @since V100R001
 */
public interface EnterpriseEvaluateRepository extends IBaseJpaPhysicalDeleteRepository<DtoEnterpriseEvaluate, String> {
    /**
     * 根据供应商id查询评价信息
     *
     * @param id 供应商id
     * @return 评价信息
     */
    DtoEnterpriseEvaluate findByEntId(String id);

    /**
     * 根据供应商ids查询评价信息
     *
     * @param entIds 供应商ids
     * @return 评价信息
     */
    List<DtoEnterpriseEvaluate> findByEntIdIn(List<String> entIds);
}