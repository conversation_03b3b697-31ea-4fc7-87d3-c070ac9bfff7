package com.sinoyd.base.controller;

import com.sinoyd.base.service.ISwitchService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * LIMS开关
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/05/20
 */
@RestController
@RequestMapping("/api/base/switch")
public class LimsSwitchController extends ExceptionHandlerController<ISwitchService> {

    /**
     * 启用RCC
     *
     * @return 包含布尔值的RestResponse对象，表示启用RCC的结果
     */
    @GetMapping("/pdp/enable")
    public RestResponse<Boolean> enablePollutantDischargePermit() {
        RestResponse<Boolean> response = new RestResponse<>();
        response.setData(service.enablePollutantDischargePermit());
        return response;
    }
}
