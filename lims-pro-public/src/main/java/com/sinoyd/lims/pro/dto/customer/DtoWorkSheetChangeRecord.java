package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.dto.DtoParamsData;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DtoWorkSheetChange 切换工作单
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/2020/10/20
 * @since V100R001
 */
@Data
public class DtoWorkSheetChangeRecord {

    /**
     * 原始记录单id
     */
    private String recordId;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 测试项目id集合
     */
    private List<String> testIds = new ArrayList<>();

    /**
     * 公式
     */
    private String formula;

    /**
     * 公式Id
     */
    private String formulaId;

    /**
     * 相关的参数集合
     */
    private List<DtoParamsData> paramsDataList = new ArrayList<>();
}
