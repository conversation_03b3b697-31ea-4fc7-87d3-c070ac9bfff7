ALTER TABLE ACT_RU_JOB ENABLE CONSTRAINT ACT_FK_JOB_EXCEPTION;
ALTER TABLE ACT_RU_EXECUTION ENABLE CONSTRAINT ACT_FK_EXE_SUPER;
ALTER TABLE ACT_RU_EXECUTION ENABLE CONSTRAINT ACT_FK_EXE_PROCDEF;
ALTER TABLE ACT_RU_EXECUTION ENABLE CONSTRAINT ACT_FK_EXE_PARENT;
ALTER TABLE ACT_RU_EXECUTION ENABLE CONSTRAINT ACT_FK_EXE_PROCINST;
ALTER TABLE ACT_RE_MODEL ENABLE CONSTRAINT ACT_FK_MODEL_SOURCE_EXTRA;
ALTER TABLE ACT_RE_MODEL ENABLE CONSTRAINT ACT_FK_MODEL_SOURCE;
ALTER TABLE ACT_RE_MODEL ENABLE CONSTRAINT ACT_FK_MODEL_DEPLOYMENT;
ALTER TABLE ACT_GE_BYTEARRAY ENABLE CONSTRAINT ACT_FK_BYTEARR_DEPL;
ALTER TABLE ACT_PROCDEF_INFO ENABLE CONSTRAINT ACT_FK_INFO_JSON_BA;
ALTER TABLE ACT_PROCDEF_INFO ENABLE CONSTRAINT ACT_FK_INFO_PROCDEF;
ALTER TABLE ACT_RU_EVENT_SUBSCR ENABLE CONSTRAINT ACT_FK_EVENT_EXEC;
ALTER TABLE ACT_RU_VARIABLE ENABLE CONSTRAINT ACT_FK_VAR_BYTEARRAY;
ALTER TABLE ACT_RU_VARIABLE ENABLE CONSTRAINT ACT_FK_VAR_PROCINST;
ALTER TABLE ACT_RU_VARIABLE ENABLE CONSTRAINT ACT_FK_VAR_EXE;
ALTER TABLE ACT_RU_IDENTITYLINK ENABLE CONSTRAINT ACT_FK_ATHRZ_PROCEDEF;
ALTER TABLE ACT_RU_IDENTITYLINK ENABLE CONSTRAINT ACT_FK_IDL_PROCINST;
ALTER TABLE ACT_RU_IDENTITYLINK ENABLE CONSTRAINT ACT_FK_TSKASS_TASK;
ALTER TABLE ACT_RU_TASK ENABLE CONSTRAINT ACT_FK_TASK_PROCDEF;
ALTER TABLE ACT_RU_TASK ENABLE CONSTRAINT ACT_FK_TASK_PROCINST;
ALTER TABLE ACT_RU_TASK ENABLE CONSTRAINT ACT_FK_TASK_EXE;
ALTER TABLE ACT_ID_MEMBERSHIP ENABLE CONSTRAINT ACT_FK_MEMB_GROUP;
ALTER TABLE ACT_ID_MEMBERSHIP ENABLE CONSTRAINT ACT_FK_MEMB_USER;
