package com.sinoyd.lims.lim.controller;

import java.util.List;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.AnalyzeItemRelationCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelation;
import com.sinoyd.lims.lim.service.AnalyzeItemRelationService;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.ApiOperation;

/**
 * 分析项目关系接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-13
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/analyzeItemRelation")
@Validated
public class AnalyzeItemRelationController extends BaseJpaController<DtoAnalyzeItemRelation, String, AnalyzeItemRelationService> {

    /**
     * 根据id查询分析项目关系
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询分析项目关系", notes = "根据id查询分析项目关系")
    @GetMapping("/{id}")
    public RestResponse<DtoAnalyzeItemRelation> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoAnalyzeItemRelation> restResp = new RestResponse<>();

        DtoAnalyzeItemRelation entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询分析项目关系
     *
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询分析项目关系", notes = "分页动态条件查询分析项目关系")
    @GetMapping
    public RestResponse<List<DtoAnalyzeItemRelation>> findByPage(AnalyzeItemRelationCriteria criteria) {

        RestResponse<List<DtoAnalyzeItemRelation>> restResp = new RestResponse<>();

        PageBean<DtoAnalyzeItemRelation> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增分析项目关系
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增分析项目关系", notes = "新增分析项目关系")
    @PostMapping
    public RestResponse<DtoAnalyzeItemRelation> save(@Validated @RequestBody DtoAnalyzeItemRelation entity) {

        RestResponse<DtoAnalyzeItemRelation> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeItemRelation data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改分析项目关系
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改分析项目关系", notes = "修改分析项目关系")
    @PutMapping
    public RestResponse<DtoAnalyzeItemRelation> update(@Validated @RequestBody DtoAnalyzeItemRelation entity) {

        RestResponse<DtoAnalyzeItemRelation> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoAnalyzeItemRelation data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id删除分析项目关系
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id删除分析项目关系", notes = "根据id删除分析项目关系")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        restResp.setCount(service.logicDeleteById(id));

        return restResp;
    }

    /**
     * 批量删除分析项目关系
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除分析项目关系", notes = "批量删除分析项目关系")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(ids));

        return restResp;
    }
}