package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * StatusForReport实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="StatusForReport")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class StatusForReport implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  StatusForReport() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 报告id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告id")
	private String reportId;
    
    /**
    * 模块编码（枚举EnumReportModule）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("模块编码（枚举EnumReportModule）")
	private String module;
    
    /**
    * 状态（枚举 EnumStatus 1待处理 2已处理）
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("状态（枚举 EnumStatus 1待处理 2已处理）")
	private Integer status;
    
    /**
    * 当前操作人Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("当前操作人Id")
	private String currentPersonId;
    
    /**
    * 当前操作人名称
    */
    @Column(length=50)
    @ApiModelProperty("当前操作人名称")
	private String currentPersonName;
    
    /**
    * 下一步操作人Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("下一步操作人Id")
	private String nextPersonId;
    
    /**
    * 下一步操作人名称
    */
    @Column(length=50)
    @ApiModelProperty("下一步操作人名称")
	private String nextPersonName;
    
    /**
    * 最新一条意见
    */
    @Column(length=4000)
    @ApiModelProperty("最新一条意见")
	private String lastNewOpinion;
    
    /**
    * 预留string类型1
    */
    @ApiModelProperty("预留string类型1")
	private String extendStr1;
    
    /**
    * 预留string类型1
    */
    @ApiModelProperty("预留string类型1")
	private String extendStr2;
    
    /**
    * 预留string类型1
    */
    @ApiModelProperty("预留string类型1")
	private String extendStr3;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }