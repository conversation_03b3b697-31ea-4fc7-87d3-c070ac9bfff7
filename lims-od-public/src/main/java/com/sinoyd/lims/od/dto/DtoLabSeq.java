package com.sinoyd.lims.od.dto;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.od.entity.LabSeq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * LabSeq实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Ods_LabSeq")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoLabSeq extends LabSeq {


    /**
     * 样品id
     */
    @Transient
    private String sampleId;


    /**
     * 实验组（第几组）
     */
    @Transient
    private Integer groupSn;

    /**
     * 实验组（第几组）字符串
     */
    @Transient
    private String groupSnStr;

    /**
     * 实验组次序（第几轮）字符串
     */
    @Transient
    private String seqSnStr;

    /**
     * 嗅辨人员答题集合
     */
    @Transient
    private List<DtoOdPersonAnswer> personAnswerList;

    /**
     * 实验组、序号拼接字符串
     *
     * @param labSeq 实验次序实体
     */
    public void buildSnStr(DtoLabSeq labSeq) {
        labSeq.setGroupSnStr(String.format("实验%s", labSeq.getGroupSn()));
        labSeq.setSeqSnStr(String.format("第%s轮", labSeq.getSn()));
    }
}
