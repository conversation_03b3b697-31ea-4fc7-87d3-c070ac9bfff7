package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 样品制备列表查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023年3月20日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamplePreparationCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接样开始日期
     */
    private String startTime;

    /**
     * 接样结束日期
     */
    private String endTime;

    /**
     * 检测类型
     */
    private String sampleTypeId;

    /**
     * 样品查询条件（样品编号，点位名称）
     */
    private String sampleKey;

    /**
     * 项目相关查询条件（项目编号，项目名称）
     */
    private String projectKey;

    /**
     * 分析项目
     */
    private String analyzeItemName;

    /**
     * 分析方法查询条件（方法名称，方法编号）
     */
    private String methodKey;

    /**
     * 制备状态（0:未制备，1:已制备）
     */
    private Integer preparedStatus;

    /**
     * 制备人
     */
    private String preparedPersonId;

    /**
     * 分析方法id集合
     */
    private List<String> analyzeMethodIds;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder(" and s.sampleCategory = :sampleCategory and s.isDeleted = 0");
        values.put("sampleCategory", EnumPRO.EnumSampleCategory.原样.getValue());
        // 送样单上的查询条件
        appendReceiveCondition(condition);
        // 测试项目的查询条件
        appendAnalyseDataCondition(condition);
        // 项目查询条件
        if (StringUtils.isNotNullAndEmpty(this.projectKey)) {
            condition.append(" and exists(select 1 from DtoProject p where s.projectId = p.id and (p.projectCode like :projectKey or p.projectName like :projectKey))");
            values.put("projectKey", this.projectKey);
        }
        // 样品查询条件
        // 检测类型
        if (StringUtils.isNotNullAndEmpty(this.sampleTypeId)) {
            condition.append(" and s.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        //样品相关条件
        if (StringUtils.isNotNullAndEmpty(this.sampleKey)) {
            condition.append(" and (s.code like :sampleKey or s.redFolderName like :sampleKey)");
            values.put("sampleKey", "%" + this.sampleKey + "%");
        }
        //制备状态
        if (StringUtils.isNotNullAndEmpty(this.preparedStatus) && this.preparedStatus != -1) {
            condition.append(" and s.preparedStatus = :preparedStatus");
            values.put("preparedStatus", this.preparedStatus);
        }
        //制备人
        if (StringUtils.isNotNullAndEmpty(this.preparedPersonId) && !UUIDHelper.GUID_EMPTY.equals(this.preparedPersonId)) {
            condition.append(" and exists(select 1 from DtoSamplePreparation sp where sp.sampleId = s.id and sp.preparedPersonId = :preparedPersonId)");
            values.put("preparedPersonId", this.preparedPersonId);
        }
        return condition.toString();
    }

    /**
     * 获取交接单相关的查询条件
     *
     * @param condition 查询条件
     */
    private void appendReceiveCondition(StringBuilder condition) {
        //获取样品交接后的样品
//        condition.append(" and exists (SELECT 1 FROM DtoReceiveSampleRecord r WHERE s.receiveId = r.id and r.receiveStatus > 1");
        condition.append(" and s.receiveId = r.id and r.receiveStatus > 1");
        //接样开始时间
        if (StringUtils.isNotNullAndEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and r.receiveSampleDate > :startTime");
            values.put("startTime", from);
        }
        //接样结束时间
        if (StringUtils.isNotNullAndEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            to = c.getTime();
            condition.append(" and r.receiveSampleDate < :endTime");
            values.put("endTime", to);
        }
//        condition.append(")");
    }

    /**
     * 获取测试项目相关的查询条件
     *
     * @param condition 查询条件
     */
    private void appendAnalyseDataCondition(StringBuilder condition) {
        condition.append(" and exists(select 1 from DtoAnalyseData a where a.sampleId = s.id and a.isDeleted = 0");
        //分析方法id
        if (StringUtil.isNotEmpty(this.analyzeMethodIds)) {
            condition.append(" and a.analyzeMethodId in :analyzeMethodIds");
            values.put("analyzeMethodIds", this.analyzeMethodIds);
        } else {
            condition.append(" and 1=2");
        }
        //分析项目
        if (StringUtils.isNotNullAndEmpty(this.analyzeItemName)) {
            condition.append(" and a.redAnalyzeItemName like :analyzeItemName");
            values.put("analyzeItemName", "%" + this.analyzeItemName + "%");
        }
        //分析方法
        if (StringUtils.isNotNullAndEmpty(this.methodKey)) {
            condition.append(" and (a.redAnalyzeMethodName like :methodKey or a.redCountryStandard like :methodKey)");
            values.put("methodKey", "%" + this.methodKey + "%");
        }
        condition.append(")");
    }
}
