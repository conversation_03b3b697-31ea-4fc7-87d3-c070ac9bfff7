package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoNotice;

/**
 * 公告管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/18
 * @since V100R001
 */
public interface NoticeService extends IBaseJpaService<DtoNotice, String> {
   
    /**
     * 公告置顶
     * @param noticeId 公告id
     * @return 公告实体
     */
    DtoNotice makeTop(String noticeId);

    /**
     * 公告取消置顶
     * @param noticeId 公告id
     * @return 公告实体
     */
    DtoNotice cancelTop(String noticeId);

    /**
     * 获取公告的附件
     * @param id 公告id
     * @return 公告实体
     */
    DtoNotice getNoticePath(String id);
}