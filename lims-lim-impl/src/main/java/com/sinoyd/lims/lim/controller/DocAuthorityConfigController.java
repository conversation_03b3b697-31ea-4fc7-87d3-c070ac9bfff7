package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityConfigTemp;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityValidate;
import com.sinoyd.lims.lim.dto.customer.DtoDocUserConfigTemp;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityConfig;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityList;
import com.sinoyd.lims.lim.service.DocAuthorityConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件夹权限配置
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/3
 * @since V100R001
 */
@Api(tags = "文件夹权限配置: 文件夹权限配置服务")
@RestController
@RequestMapping("/api/lim/docAuthorityConfig")
@Validated
public class DocAuthorityConfigController extends BaseJpaController<DtoDocAuthorityConfig, String, DocAuthorityConfigService> {

    /**
     * 文件权限配置列表
     *
     * @param objectId 文件夹di
     * @return 返回权限配置列表
     */
    @ApiOperation(value = "查询文件权限配置列表", notes = "查询文件权限配置列表")
    @GetMapping("/authority")
    public RestResponse<List<DtoDocAuthorityList>> findAuthorityList(@RequestParam(name = "objectId") String objectId,
                                                                     @RequestParam(name = "orgId")String orgId) {

        RestResponse<List<DtoDocAuthorityList>> restResponse = new RestResponse<>();
        List<DtoDocAuthorityList> docAuthorityList = service.findAuthorityList(objectId,orgId);
        restResponse.setData(docAuthorityList);
        restResponse.setCount(docAuthorityList.size());
        return restResponse;
    }


    /**
     * 文件权限对应操作人员配置
     *
     * @param objectId 文件夹id
     * @param authCode 权限id
     * @return 返回人员配置列表
     */
    @ApiOperation(value = "查询文件权限对应操作人配置", notes = "查询文件权限对应操作人员配置")
    @GetMapping("/user")
    public RestResponse<List<DtoDocUserConfigTemp>> findUserList(@RequestParam(name = "objectId") String objectId, @RequestParam(name = "authCode") String authCode) {

        RestResponse<List<DtoDocUserConfigTemp>> restResponse = new RestResponse<>();
        List<DtoDocUserConfigTemp> docAuthorityConfigList = service.findUserList(objectId, authCode);
        restResponse.setData(docAuthorityConfigList);
        restResponse.setCount(docAuthorityConfigList.size());

        return restResponse;
    }

    /**
     * 获取所有人员
     * @return RestResponse<List<DtoDocUserConfigTemp>>
     */
    @ApiOperation(value = "获取所有人员", notes = "获取所有人员")
    @GetMapping("/findAllUser")
    public RestResponse<List<DtoDocUserConfigTemp>> findAllUerList() {
        RestResponse<List<DtoDocUserConfigTemp>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAllUserList());
        return restResponse;
    }

    /**
     * 文件权限配置人员保存
     *
     * @param temp 权限配置人员
     * @return 新增的权限集合
     */
    @ApiOperation(value = "文件权限配置人员保存", notes = "文件权限配置人员保存")
    @PutMapping("")
    public RestResponse<String> create(@Validated @RequestBody DtoDocAuthorityConfigTemp temp) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.saveAll(temp);
        restResponse.setCount(1);
        return restResponse;
    }

    /**
     * 批量文件权限配置人员保存
     *
     * @param temp 权限配置人员
     * @return 新增的权限集合
     */
    @ApiOperation(value = "批量文件权限配置人员保存", notes = "批量文件权限配置人员保存")
    @PutMapping("/batchCreate")
    public RestResponse<String> batchCreate(@Validated @RequestBody DtoDocAuthorityConfigTemp temp) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.batchSaveAll(temp);
        restResponse.setCount(1);
        return restResponse;
    }


    /**
     * 权限验证
     *
     * @return 返回是否具有权限
     */
    @ApiOperation(value = "权限验证", notes = "权限验证")
    @PostMapping("/validateAuth")
    public RestResponse<Boolean> validateAuth(@RequestBody DtoDocAuthorityValidate dtoDocAuthorityValidate) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.validateAuth(dtoDocAuthorityValidate));
        return restResponse;
    }

    /**
     * 批量设置
     *
     * @param docAuthorityList 参数
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "批量设置", notes = "批量设置")
    @PostMapping("/batchSet/{defaultOpenInd}")
    public RestResponse<Void> batchSet(@RequestBody DtoDocAuthorityList docAuthorityList, @PathVariable(name = "defaultOpenInd") Boolean defaultOpenInd) {
        RestResponse<Void> response = new RestResponse<>();
        service.batchSet(docAuthorityList.getIds(), defaultOpenInd);
        return response;
    }

    /**
     * 文件夹批量设置
     *
     * @param docAuthorityList 参数
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "文件夹批量设置", notes = "文件夹批量设置")
    @PostMapping("/folderBatchSet")
    public RestResponse<Void> folderBatchSet(@RequestBody DtoDocAuthorityList docAuthorityList) {
        RestResponse<Void> response = new RestResponse<>();
        service.batchSet(docAuthorityList.getIds(), docAuthorityList.getAuthCodes(), docAuthorityList.getUserIds());
        return response;
    }

    /**
     * 文件夹批量设置
     *
     * @param docAuthorityList 参数
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "文件夹批量设置", notes = "文件夹批量设置")
    @PostMapping("/folderBatchSet/{defaultOpenInd}")
    public RestResponse<Void> folderBatchSet(@RequestBody DtoDocAuthorityList docAuthorityList, @PathVariable(name = "defaultOpenInd") Boolean defaultOpenInd) {
        RestResponse<Void> response = new RestResponse<>();
        service.batchSet(docAuthorityList.getIds(), docAuthorityList.getAuthCodes(), defaultOpenInd);
        return response;
    }



}
