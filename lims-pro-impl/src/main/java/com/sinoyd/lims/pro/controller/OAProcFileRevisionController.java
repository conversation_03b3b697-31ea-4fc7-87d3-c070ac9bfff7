package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ELogType;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.dto.lims.DtoOAContract;
import com.sinoyd.lims.lim.dto.lims.DtoOAFileRevision;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.service.OAProcFileRevisionService;

import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件修订服务接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-08
 * @since V100R001
 */
@Api(tags = "工作流: 文件修订服务")
@RestController
@RequestMapping("/api/pro/oaFileRevisions")
public class OAProcFileRevisionController extends ExceptionHandlerController<OAProcFileRevisionService> {
    /**
     * 添加文件修订申请
     * 
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加文件修订申请", notes = "添加文件修订申请启动流程")
    @PostMapping
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> startProcess(@RequestBody DtoOATaskCreate<DtoOAFileRevision> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        String procInstId = service.startProcess(taskDto);
        restResp.setData(procInstId);

        return restResp;
    }

    /**
     * 保存为草稿
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "保存为草稿", notes = "保存为草稿")
    @PostMapping("/saveAsDraft")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> saveAsDraft(@RequestBody DtoOATaskCreate<DtoOAFileRevision> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.saveAsDraft(taskDto));
        return restResp;
    }

    /**
     * 草稿保存
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "草稿保存", notes = "草稿保存")
    @PostMapping("/draftSave")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> draftSave(@RequestBody DtoOATaskCreate<DtoOAFileRevision> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSave(taskDto));
        return restResp;
    }

    /**
     * 草稿提交
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加审批", notes = "添加审批启动流程")
    @PostMapping("/draftSubmit")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> draftSubmit(@RequestBody DtoOATaskCreate<DtoOAFileRevision> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSubmit(taskDto));
        return restResp;
    }

    /**
     * 查询文件修订信息
     * 
     * @param taskId 任务id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "查询文件修订信息", notes = "查询文件修订信息")
    @GetMapping(path = "/task/{taskId}")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<DtoOATaskDetail<DtoOAFileRevision, DtoFileControlApplyDetail>> findDetailByTaskId(@PathVariable(name = "taskId") String taskId) {
        RestResponse<DtoOATaskDetail<DtoOAFileRevision, DtoFileControlApplyDetail>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoOATaskDetail<DtoOAFileRevision, DtoFileControlApplyDetail> detail = service.findOATaskDetail(taskId);
        restResp.setData(detail);

        return restResp;
    }

    /**
     * 文件上传
     * @param procInstId 工作流id
     * @param files 文件集合
     * @param ids 受控申请文件明细id集合
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加附件", notes = "添加附件")
    @PostMapping(path = "/uploadFileRevision")
    public RestResponse<String> uploadFilesControl(@RequestParam(name = "procInstId") String procInstId,
                                                   @RequestParam(name = "ids") List<String> ids,
                                                   List<MultipartFile> files){
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.uploadFilesControl(procInstId, ids, files);
        return restResp;
    }
}
