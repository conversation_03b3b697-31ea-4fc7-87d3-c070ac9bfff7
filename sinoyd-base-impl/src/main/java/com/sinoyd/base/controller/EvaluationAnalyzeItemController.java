package com.sinoyd.base.controller;

import com.sinoyd.base.dto.rcc.DtoEvaluationAnalyzeItem;
import com.sinoyd.base.service.EvaluationAnalyzeItemService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 评价标准分析项目服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/16
 * @since V100R001
 */
@Api(tags = "示例: 评价标准分析项目服务")
@RestController
@RequestMapping("api/base/evaluationAnalyzeItem")
public class EvaluationAnalyzeItemController extends BaseJpaController<DtoEvaluationAnalyzeItem, String,EvaluationAnalyzeItemService> {

    @ApiOperation(value = "按评价标准id查询相应的评价分析项目", notes = "按评价标准id查询相应的评价分析项目")
    @GetMapping("/all")
    public RestResponse<List<DtoEvaluationAnalyzeItem>> findAll(){
        RestResponse<List<DtoEvaluationAnalyzeItem>> restResponse = new RestResponse<>();
        List<DtoEvaluationAnalyzeItem> analyzeItems = service.findAll();
        restResponse.setRestStatus(StringUtil.isEmpty(analyzeItems) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(analyzeItems);
        restResponse.setCount(analyzeItems.size());
        return restResponse;
    }

    @ApiOperation(value = "按评价标准id查询相应的评价分析项目", notes = "按评价标准id查询相应的评价分析项目")
    @GetMapping
    public RestResponse<List<DtoEvaluationAnalyzeItem>> findAnalyzeItemByEvaluationId(@RequestParam("evaluationId") String evaluationId) {
        RestResponse<List<DtoEvaluationAnalyzeItem>> restResponse = new RestResponse<>();
        List<DtoEvaluationAnalyzeItem> analyzeItems = service.findAnalyzeItemByEvaluationId(evaluationId);
        restResponse.setRestStatus(StringUtil.isEmpty(analyzeItems) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(analyzeItems);
        restResponse.setCount(analyzeItems.size());
        return restResponse;
    }
}
