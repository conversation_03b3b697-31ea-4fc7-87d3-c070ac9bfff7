-- 新增 tb_lim_paramstestformula tb_lim_paramspartformula 删除日志记录触发器
drop trigger if exists before_delete_paramstestformula;
DELIMITER $$
CREATE TRIGGER before_delete_paramstestformula
    BEFORE DELETE
    ON tb_lim_paramstestformula
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_paramstestformula', old.id, '00000000-0000-0000-0000-000000000000', now(), 2, null, null,
            null, old.orgId, '00000000-0000-0000-0000-000000000000');
End $$
DELIMITER ;


drop trigger if exists before_delete_paramspartformula;
DELIMITER $$
CREATE TRIGGER before_delete_paramspartformula
    BEFORE DELETE
    ON tb_lim_paramspartformula
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_paramspartformula', old.id, '00000000-0000-0000-0000-000000000000', now(), 2, null, null,
            null, old.orgId, '00000000-0000-0000-0000-000000000000');
End $$
DELIMITER ;