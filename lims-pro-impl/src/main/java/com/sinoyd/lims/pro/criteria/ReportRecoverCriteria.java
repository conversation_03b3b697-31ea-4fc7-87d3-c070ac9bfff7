package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * reportrecover查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportRecoverCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告年份
     */
    private String reportYear;

    /**
     * 报告类型
     */
    private String reportTypeId;

    /**
     * 项目类型
     */
    private String projectTypeId;

    /**
     * 回收人
     */
    private String recoverPerson;

    /**
     * 关键字 项目名称/项目编号/委托方/受检方
     */
    private String expressKey;

    /**
     * 报告编号
     */
    private String code;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and rc.id = r2e.recoverId");
        condition.append(" and rp.id = r2e.reportId");
        condition.append(" and rp.projectId = p.id");
        if (StringUtil.isNotEmpty(this.reportYear)) {
            condition.append(" and rp.reportYear = :reportYear");
            values.put("reportYear", this.reportYear);
        }
        if (StringUtil.isNotEmpty(this.reportTypeId)) {
            condition.append(" and rp.reportTypeId = :reportTypeId");
            values.put("reportTypeId", this.reportTypeId);
        }
        if (StringUtil.isNotEmpty(this.projectTypeId)) {
            condition.append(" and p.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        }
        if (StringUtil.isNotEmpty(this.code)) {
            condition.append(" and rp.code like :code");
            values.put("code", "%" + this.code + "%");
        }
        if (StringUtil.isNotEmpty(this.expressKey)) {
            condition.append(" and (p.projectCode like :expressKey or p.projectName like :expressKey or p.customerName like :expressKey or p.inspectedEnt like :expressKey)");
            values.put("expressKey", "%" + this.expressKey + "%");
        }
        if (StringUtil.isNotEmpty(this.recoverPerson)) {
            condition.append(" and rc.recoverPerson like :recoverPerson");
            values.put("recoverPerson", "%" + this.recoverPerson + "%");
        }

        return condition.toString();
    }
}