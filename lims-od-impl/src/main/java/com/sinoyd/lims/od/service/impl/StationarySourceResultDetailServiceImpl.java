package com.sinoyd.lims.od.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.od.dto.DtoStationarySourceResultDetail;
import com.sinoyd.lims.od.repository.StationarySourceResultDetailRepository;
import com.sinoyd.lims.od.service.StationarySourceResultDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
public class StationarySourceResultDetailServiceImpl extends BaseJpaServiceImpl<DtoStationarySourceResultDetail, String, StationarySourceResultDetailRepository>
        implements StationarySourceResultDetailService {


}
