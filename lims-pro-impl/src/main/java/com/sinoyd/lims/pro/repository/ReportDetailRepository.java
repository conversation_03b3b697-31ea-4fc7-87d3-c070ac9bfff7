package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoReportDetail;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;


/**
 * 报告详情数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/12
 * @since V100R001
 */
public interface ReportDetailRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportDetail, String> {

    /**
     * 按报告id查询关联信息
     *
     * @param reportId 报告id
     * @return 返回相应的报告关联信息
     */
    List<DtoReportDetail> findByReportId(String reportId);

    /**
     * 按报告id集合查询关联信息
     *
     * @param reportIds 报告id集合
     * @return 返回相应的报告关联信息
     */
    List<DtoReportDetail> findByReportIdIn(Collection reportIds);

    /**
     * 按报告id删除明细
     *
     * @param reportId 报告id
     * @return 返回删除的个数
     */
    @Transactional
    Integer deleteByReportId(String reportId);

    /**
     * 按报告id删除明细
     *
     * @param reportIds 报告id集合
     * @return 返回删除的个数
     */
    @Transactional
    Integer deleteByReportIdIn(List<String> reportIds);

    /**
     * 按报告id,报告类型，objectId删除明细
     *
     * @param objectIds 报告id集合
     * @param objectType 报告类型
     * @param objectType 报告类型
     * @return 返回删除的个数
     */
    @Transactional
    Integer deleteByReportIdAndObjectTypeAndObjectIdIn(@Param("reportId")String reportId, @Param("objectType")int objectType, @Param("objectIds")List<String> objectIds);

    /**
     * 根据样品id获取数据
     * @param objectIds 样品ids
     * @return 个数
     */
    @Transactional
    Integer countByObjectIdIn(Collection<String> objectIds);

    /**
     * 根据样品id和对象类型获取数据
     *
     * @param objectIds  样品id
     * @param objectType 对象类型
     * @return List<DtoReportDetail>
     */
    List<DtoReportDetail> findByObjectIdInAndObjectType(Collection<String> objectIds, Integer objectType);
}