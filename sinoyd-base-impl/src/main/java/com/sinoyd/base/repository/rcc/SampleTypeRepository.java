package com.sinoyd.base.repository.rcc;

import java.util.Collection;
import java.util.List;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 样品类型数据接口
 * <AUTHOR>
 * @version V1.0.0 2019/1/9
 * @since V100R001
 */
public interface SampleTypeRepository extends IBaseJpaRepository<DtoSampleType,String> {

    /**
     * 根据小类id获取大类
     *
     * @param id 检测类型小类的id
     * @return 返回检测类型大类
     */
    @Query("select p from DtoSampleType p where p.id = (select q.parentId from DtoSampleType q where q.id = :id and q.isDeleted = 0) and p.isDeleted = 0")
    DtoSampleType getBigSampleType(@Param("id") String id);

    /**
     * 获取检测类型的类别
     */
    Integer findCategoryById(String id);

    /**
     * 查找名称重复的检测类型
     *
     * @param typeName 检测类型名称
     * @param id       检测类型id
     * @return 查找到检测类型的个数
     */
    @Query("select count(p.id) from DtoSampleType p where p.typeName = :typeName and p.id <> :id and p.parentId = :parentId and p.isDeleted = 0")
    Integer countByNameAndParentId(@Param("typeName") String typeName, @Param("id") String id, @Param("parentId") String parentId);

    /**
     * 通过id判断当前类型有没有子类型
     *
     * @param id 检测类型id
     * @return 查找到检测类型的个数
     */
    @Query("select count(p.id) from DtoSampleType p where p.isDeleted = 0 and p.parentId = :id")
    Integer haveChildren(String id);

    /**
     * 查找所有的检测类型(不含模板)
     *
     * @return 返回所有的检测类型(不含模板)
     */
    @Query("select p from DtoSampleType p where p.isDeleted =0  and p.category <> 3 and p.industryTypeId in (select q.id from DtoIndustryType q where q.isDeleted =0 )")
    List<DtoSampleType> getList();

    /**
     * 查找所有的检测类型(不含模板)包含假删
     *
     * @return 返回所有的检测类型(不含模板)包含假删
     */
    @Query("select p from DtoSampleType p where p.category <> 3 and p.industryTypeId in (select q.id from DtoIndustryType q where q.isDeleted =0 )")
    List<DtoSampleType> getListWithDeleted();

    /**
     * 根据大类Id获取小类
     *
     * @return
     */
    @Query("select p from DtoSampleType p where p.isDeleted = 0 and p.category = 2 and p.parentId = :parentId")
    List<DtoSampleType> getListByBigSampleType(@Param("parentId") String parentId);

    /**
     * 通过行业类型查找所有的检测类型(不含模板)
     *
     * @param industryTypeId 行业类型id
     * @return 返回检测类型集合
     */
    @Query("select p from DtoSampleType p where p.industryTypeId = :industryTypeId and p.isDeleted = 0 and (p.category = 1 or p.category = 2)")
    List<DtoSampleType> getListByIndustryId(@Param("industryTypeId") String industryTypeId);

    /**
     * 通过行业类型查找所有的检测类型(不含模板)
     *
     * @return 所有的检测类型(不含模板)
     */
    @Query("select p from DtoSampleType p where p.isDeleted = 0 and (p.category = 1 or p.category = 2) and p.industryTypeId in (select q.id from DtoIndustryType q where q.isDeleted =0)")
    List<DtoSampleType> getListByNull();

    /**
     * 根据父类的id获取子类的id
     */
    @Query("select p.id from DtoSampleType p where p.parentId = :id and p.isDeleted = 0")
    List<String> getChildrenIds(@Param("id") String id);

    /**
     * 查找同一个行业类型下的检测类型大类重名的个数
     *
     * @param id             检测类型id
     * @param industryTypeId 行业类型id
     * @param typeName       类型名称
     * @return 返回重名个数
     */
    @Query("select count(p.id) from DtoSampleType p where p.isDeleted =0 and p.id <> :id and p.industryTypeId = :industryTypeId and p.typeName = :typeName and p.category = 1")
    Integer countByIndustryTypeId(@Param("id") String id, @Param("industryTypeId") String industryTypeId, @Param("typeName") String typeName);


    /**
     * 根据类型查询
     *
     * @param category 类型
     * @return 返回大类的检测类型
     */
    @Query("select p from DtoSampleType p where p.isDeleted = 0 and p.category = :category")
    List<DtoSampleType> findByCategory(@Param("category") Integer category);


    /**
     * 重写获取所有的方法
     *
     * @return 返回未删除检测类型信息
     */
    @Override
    @Query("select p from DtoSampleType p where p.isDeleted = 0")
    List<DtoSampleType> findAll();

    /**
     * 根据id数组获取检测类型信息
     *
     * @param ids 检测类型信息ids
     * @return 返回检测类型信息
     */
    @Query("select d from DtoSampleType d where d.isDeleted = 0 and d.id in :ids")
    @Override
    List<DtoSampleType> findAll(@Param("ids") Iterable<String> ids);


    /**
     * 返回所有的带假删的检测类型信息
     *
     * @return 返回带删除的检测类型信息
     */
    @Query("select p from DtoSampleType p")
    List<DtoSampleType> findAllDeleted();

    /**
     * 返回所有的带假删的检测类型信息
     *
     * @param ids 人员的ids
     * @return 返回带删除的检测类型信息
     */
    @Query("select p from DtoSampleType p where p.id in :ids")
    List<DtoSampleType> findAllDeleted(@Param("ids") List<String> ids);

    /**
     * 返回父类下所有的子类检测类型
     *
     * @param parentId 父类id
     * @return 返回父类下所有的子类检测类型
     */
    @Query("select p from DtoSampleType p where p.parentId = :parentId and p.isDeleted=0")
    List<DtoSampleType> findByParentId(@Param("parentId") String parentId);


    /**
     * 返回行业类型下所有的子类检测类型
     *
     * @param industryTypeIds  行业类型id
     * @return 返回父类下所有的子类检测类型
     */
    @Query("select p from DtoSampleType p where p.industryTypeId in :industryTypeIds and p.isDeleted=0")
    List<DtoSampleType> findByIndustryTypeIds(@Param("industryTypeIds") List<String> industryTypeIds);

    /**
     * 返回行业类型下所有的子类检测类型大
     *
     * @param industryTypeId  行业类型id
     * @return 返回父类下所有的子类检测类型大类
     */
    @Query("select p from DtoSampleType p where p.industryTypeId = :industryTypeId and p.category = :category and p.isDeleted = 0 ")
    List<DtoSampleType> findByIndustryTypeIdAndCategory(@Param("industryTypeId") String industryTypeId, @Param("category") Integer category);

    /**
     * 返回行业类型大类
     *
     * @param typeCode 样品类型code
     * @return 返回行业类型大类
     */
    @Query("select p from DtoSampleType p where p.typeCode = :typeCode and p.isDeleted=0")
    DtoSampleType findByTypeCode(@Param("typeCode") String typeCode);

    /**
     * 返回行业类型大类
     *
     * @param typeCode 样品类型code
     * @return 返回行业类型大类
     */
    @Query("select p from DtoSampleType p where p.typeName = :typeName and p.isDeleted=0")
    DtoSampleType findByTypeName(@Param("typeName") String typeCode);

    /**
     * 返回父类下所有的子类检测类型
     *
     * @param parentIds 父类ids
     * @return 返回父类下所有的子类检测类型
     */
    List<DtoSampleType> findByParentIdIn(Collection<String> parentIds);
}
