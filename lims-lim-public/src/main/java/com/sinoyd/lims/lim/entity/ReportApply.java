package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ReportApply实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ReportApply")
@Data
public class ReportApply implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ReportApply() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 配置id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("配置id")
    private String reportConfigId;

    /**
     * 所属模块
     */
    @Column(length = 50)
    @ApiModelProperty("所属模块")
    @Length(message = "所属模块{validation.message.length}", max = 50)
    private String module;

    /**
     * 所属模块名称
     */
    @Column(length = 50)
    @ApiModelProperty("所属模块名称")
    @Length(message = "所属模块名称{validation.message.length}", max = 50)
    private String moduleName;

    /**
     * 控件编码
     */
    @Column(length = 50)
    @ApiModelProperty("控件编码")
    @Length(message = "控件编码{validation.message.length}", max = 50)
    private String code;

    /**
     * 显示名称
     */
    @Column(length = 50)
    @ApiModelProperty("显示名称")
    @Length(message = "显示名称{validation.message.length}", max = 50)
    private String name;

    /**
     * 地址
     */
    @Column(length = 50)
    @ApiModelProperty("地址")
    @Length(message = "地址{validation.message.length}", max = 50)
    private String location;

    /**
     * 控件类型
     */
    @Column(nullable = false)
    @ApiModelProperty("控件类型")
    private Integer type;

    /**
     * 是否可编辑
     */
    @Column(nullable = false)
    @ApiModelProperty("是否可编辑")
    private Integer isRedact;

    /**
     * 是否显示名称
     */
    @Column(nullable = false)
    @ApiModelProperty("是否显示名称")
    private Integer isShow;
    /**
     * 是否空白填充 1:填充 0:不填充
     */
    @Column
    @ApiModelProperty("是否空白填充")
    private Integer blankFill;

    /**
     * 备注
     */
    @Column(length = 500)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 500)
    private String remark;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}