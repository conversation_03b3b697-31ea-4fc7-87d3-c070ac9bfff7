package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 公告管理
 * <AUTHOR> 修改： guqx
 * @version V1.0.0 2019/3/19
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticeCriteria extends BaseCriteria {

    /**
     * 公告类型（空Guid代表全部）
     */
    private String noticeType;
    /**
     * 公告标签（空Guid代表全部）
     */
    private String noticeLabel;

    /**
     * 公告标签数组
     */
    private List<String> noticeLabels;

    /**
     * 检索开始时间
     */
    private String timeStart;
    /**
     *  检索结束时间
     */
    private String timeEnd;
    /**
     * 关键字（公告标题、公告内容）
     */
    private String key;

    /**
     * 是否发布
     */
    private Boolean isRelease;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(timeStart)) {
            Date from = DateUtil.stringToDate(this.timeStart, DateUtil.YEAR);
            condition.append(" and releaseTime >= :timeStart");
            values.put("timeStart", from);
        }
        if (StringUtils.isNotNullAndEmpty(timeEnd)) {
            Date to = DateUtil.stringToDate(this.timeEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and releaseTime < :timeEnd");
            values.put("timeEnd", c.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(noticeType) && !UUIDHelper.GUID_EMPTY.equals(this.noticeType)) {
            condition.append(" and (category = :noticeType)");
            values.put("noticeType", this.noticeType);
        }
        boolean isNoticeLabelEmpty = true;
        if (StringUtils.isNotNullAndEmpty(noticeLabel) && !UUIDHelper.GUID_EMPTY.equals(this.noticeLabel)) {
            condition.append(" and (label like :noticeLabel)");
            values.put("noticeLabel", "%" + this.noticeLabel + "%");
            isNoticeLabelEmpty = false;
        }
        if (StringUtils.isNotNullAndEmpty(key)) {
            if(isNoticeLabelEmpty){
                condition.append(" and (title like :key or content like :key or releaseMan like :key or label like :key)");
            }else{
                condition.append(" and (title like :key or content like :key or releaseMan like :key)");
            }
            values.put("key", "%" + this.key + "%");
        }

        if (StringUtil.isNotNull(this.isRelease)) {
            condition.append(" and isRelease = :isRelease)");
            values.put("isRelease", this.isRelease);
        }
        return condition.toString();
    }
}