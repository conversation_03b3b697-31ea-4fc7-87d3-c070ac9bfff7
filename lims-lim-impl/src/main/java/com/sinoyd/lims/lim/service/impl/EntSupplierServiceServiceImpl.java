package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoEntSupplierService;
import com.sinoyd.lims.lim.repository.lims.EntSupplierServiceRepository;
import com.sinoyd.lims.lim.service.EntSupplierServiceService;

import org.springframework.stereotype.Service;

/**
 * 供应商管理-商品管理 
 * <AUTHOR>
 * @version V1.0.0 2019/3/8
 * @since V100R001
 */
@Service
public class EntSupplierServiceServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoEntSupplierService, String, EntSupplierServiceRepository>
        implements EntSupplierServiceService {

    /***
     * 分页查询商品评价
     * 
     * @param page
     * @param criteria
     */
    @Override
    public void findByPage(PageBean<DtoEntSupplierService> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoEntSupplierService p");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select p");

        super.findByPage(page, criteria);
    }
}