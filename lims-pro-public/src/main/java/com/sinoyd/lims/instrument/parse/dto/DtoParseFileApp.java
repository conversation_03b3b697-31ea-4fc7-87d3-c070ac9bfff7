package com.sinoyd.lims.instrument.parse.dto;

import com.sinoyd.lims.instrument.parse.entity.ParseFileApp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 仪器解析应用传输实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/0/7
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_FileApp")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoParseFileApp extends ParseFileApp {

}