package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 评价结果查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2022年2月22日
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EvaluationAnalyzeResultCriteria extends BaseCriteria implements Serializable {

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 是否过滤已检毕样品 true：是  false：否
     */
    private Boolean detectionComplete;

    /**
     * 是否超标标红 true：是  false：否
     */
    private Boolean overRed;

    /**
     * 分析项目排序id
     */
    private String itemSortId;

    /**
     * 点位排序id
     */
    private String pointSortId;

    /**
     * 评价类型列表(原始值，最大值，最小值，点位均值)
     */
    private List<String> evaluationType;

    @Override
    public String getCondition() {
        return null;
    }
}
