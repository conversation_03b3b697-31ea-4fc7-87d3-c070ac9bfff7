package com.sinoyd.lims.monitor.service;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 环境质量/污染源导入模板下载接口
 * <AUTHOR>
 * @version V1.0.0 2022/12/08
 * @since V100R001
 */
public interface DownLoadFixedPointTemplateService {
    /**
     * 下载模板
     *
     * @param response      响应流
     * @param fileName      文件名
     */
    void downLoadEQ(HttpServletResponse response, String fileName);

    /**
     * 下载模板
     *
     * @param response      响应流
     * @param fileName      文件名
     */
    void downLoadRS(HttpServletResponse response, String fileName);
}
