package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ProjectPushLogService;
import com.sinoyd.lims.pro.criteria.ProjectPushLogCriteria;
import com.sinoyd.lims.pro.dto.DtoProjectPushLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ProjectPushLog服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: ProjectPushLog服务")
 @RestController
 @RequestMapping("api/pro/projectPushLog")
 public class ProjectPushLogController extends BaseJpaController<DtoProjectPushLog, String,ProjectPushLogService> {


    /**
     * 分页动态条件查询ProjectPushLog
     * @param projectPushLogCriteria 条件参数
     * @return RestResponse<List<ProjectPushLog>>
     */
     @ApiOperation(value = "分页动态条件查询ProjectPushLog", notes = "分页动态条件查询ProjectPushLog")
     @GetMapping
     public RestResponse<List<DtoProjectPushLog>> findByPage(ProjectPushLogCriteria projectPushLogCriteria) {
         PageBean<DtoProjectPushLog> pageBean = super.getPageBean();
         RestResponse<List<DtoProjectPushLog>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, projectPushLogCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询ProjectPushLog
     * @param id 主键id
     * @return RestResponse<DtoProjectPushLog>
     */
     @ApiOperation(value = "按主键查询ProjectPushLog", notes = "按主键查询ProjectPushLog")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoProjectPushLog> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoProjectPushLog> restResponse = new RestResponse<>();
         DtoProjectPushLog projectPushLog = service.findOne(id);
         restResponse.setData(projectPushLog);
         restResponse.setRestStatus(StringUtil.isNull(projectPushLog) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增ProjectPushLog
     * @param projectPushLog 实体列表
     * @return RestResponse<DtoProjectPushLog>
     */
     @ApiOperation(value = "新增ProjectPushLog", notes = "新增ProjectPushLog")
     @PostMapping
     public RestResponse<DtoProjectPushLog> create(@RequestBody @Validated DtoProjectPushLog projectPushLog) {
         RestResponse<DtoProjectPushLog> restResponse = new RestResponse<>();
         restResponse.setData(service.save(projectPushLog));
         return restResponse;
      }

     /**
     * 新增ProjectPushLog
     * @param projectPushLog 实体列表
     * @return RestResponse<DtoProjectPushLog>
     */
     @ApiOperation(value = "修改ProjectPushLog", notes = "修改ProjectPushLog")
     @PutMapping
     public RestResponse<DtoProjectPushLog> update(@RequestBody @Validated DtoProjectPushLog projectPushLog) {
         RestResponse<DtoProjectPushLog> restResponse = new RestResponse<>();
         restResponse.setData(service.update(projectPushLog));
         return restResponse;
      }

    /**
     * "根据id批量删除ProjectPushLog
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ProjectPushLog", notes = "根据id批量删除ProjectPushLog")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }