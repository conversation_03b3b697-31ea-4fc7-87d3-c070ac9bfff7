package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoPollutionDischargeSync;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;

/**
 * 企业排污许可证数据服务接口
 *
 * <AUTHOR>
 * @version V5.2.0 2025/05/06
 * @since V100R001
 */
public interface PollutionDischargeSyncRepository extends IBaseJpaPhysicalDeleteRepository<DtoPollutionDischargeSync, String> {

    /**
     * 根据企业id集合查询相关数据
     *
     * @param enterpriseIds 企业id集合
     * @return 企业排污许可证同步数据集合
     */
    List<DtoPollutionDischargeSync> findByEnterpriseIdIn(Collection<String> enterpriseIds);

    /**
     * 获取未同步成功的数据
     *
     * @return 企业排污许可证同步数据集合
     */
    List<DtoPollutionDischargeSync> findByIsSuccessFalseOrderByRequestTime();

    /**
     * 根据企业id查询同步数据
     *
     * @param enterpriseId 企业id
     * @return 排污许可证同步数据
     */
    DtoPollutionDischargeSync findByEnterpriseId(String enterpriseId);
}
