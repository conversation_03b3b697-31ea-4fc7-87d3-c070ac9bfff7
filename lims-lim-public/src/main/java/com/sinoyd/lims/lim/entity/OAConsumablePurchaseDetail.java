package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * OAConsumablePurchaseDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "OAConsumablePurchaseDetail")
@Data
@EntityListeners(AuditingEntityListener.class)
public class OAConsumablePurchaseDetail extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public OAConsumablePurchaseDetail() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 消耗品标识（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("消耗品标识（Guid）")
    private String consumableId;

    /**
     * 消耗品名称
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("消耗品名称")
    @Length(message = "消耗品名称{validation.message.length}", max = 50)
    private String consumableName;

    /**
     * 编号（本站编号）
     */
    @Column(length = 50)
    @ApiModelProperty("编号（本站编号）")
    @Length(message = "编号（本站编号）{validation.message.length}", max = 50)
    private String codeInStation;

    /**
     * 标样编号
     */
    @Column(length = 20)
    @ApiModelProperty("标样编号")
    @Length(message = "标样编号{validation.message.length}", max = 20)
    private String consumableCode;

    /**
     * 物资种类（枚举EnumMaterialType：1.消耗品 2.标样）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("物资种类（枚举EnumMaterialType：1.消耗品 2.标样）")
    private Integer materialType;

    /**
     * 规格型号
     */
    @Column(length = 50)
    @ApiModelProperty("规格型号")
    @Length(message = "规格型号{validation.message.length}", max = 50)
    private String materialModel;

    /**
     * 等级常量（Guid）（LIM_ConsumableGrade:进口、分析纯、FMP、高纯等）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("等级常量（Guid）（LIM_ConsumableGrade:进口、分析纯、FMP、高纯等）")
    private String gradeId;

    /**
     * 计量单位Id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("计量单位Id（Guid）")
    private String dimensionId;

    /**
     * 计量单位名称
     */
    @Column(length = 50)
    @ApiModelProperty("计量单位名称")
    private String dimensionName;

    /**
     * 类别常量（Guid）（LIM_ConsumableCategory:高压气体、易制毒品、化学试剂等）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("类别常量（Guid）（LIM_ConsumableCategory:高压气体、易制毒品、化学试剂等）")
    private String categoryId;

    /**
     * 申请计划数量
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("申请计划数量")
    private Integer planNum;

    /**
     * 剩余未入库数量
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("剩余未入库数量")
    private Integer surplusNum;

    /**
     * 实际采购数量
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("实际采购数量")
    private Integer purchaseNum;

    /**
     * 供应时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("供应时间")
    private Date supplyTime;

    /**
     * 用途
     */
    @Column(length = 100)
    @ApiModelProperty("用途")
    @Length(message = "用途{validation.message.length}", max = 100)
    private String purpose;

    /**
     * 技术要求
     */
    @ApiModelProperty("技术要求")
    @Length(message = "技术要求{validation.message.length}", max = 255)
    private String skillRequire;

    /**
     * 是否易制毒
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否易制毒")
    private Boolean isPoison;

    /**
     * 保存条件
     */
    @Column(length = 1000)
    @ApiModelProperty("保存条件")
    @Length(message = "保存条件{validation.message.length}", max = 1000)
    private String keepCondition;

    /**
     * 安全须知
     */
    @Column(length = 1000)
    @ApiModelProperty("安全须知")
    @Length(message = "安全须知{validation.message.length}", max = 1000)
    private String safetyInstruction;

    /**
     * 稀释液
     */
    @ApiModelProperty("稀释液")
    @Length(message = "稀释液{validation.message.length}", max = 255)
    private String dilutedSolution;

    /**
     * 稀释方法
     */
    @ApiModelProperty("稀释方法")
    @Length(message = "稀释方法{validation.message.length}", max = 255)
    private String dilutionMethod;

    /**
     * 浓度
     */
    @ApiModelProperty("浓度")
    @Length(message = "浓度{validation.message.length}", max = 255)
    private String concentration;

    /**
     * 不确定度
     */
    @ApiModelProperty("不确定度")
    @Length(message = "不确定度{validation.message.length}", max = 255)
    private String uncertainty;

    /**
     * 是否混标（0代表否  1代表是）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否混标（0代表否  1代表是）")
    private Boolean isMixedStandard;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 品牌
     */
    @Column(length = 200)
    @ApiModelProperty("品牌")
    @Length(message = "品牌{validation.message.length}", max = 255)
    private String brand;

    /**
     * 单价
     */
    @Column
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 预算总额
     */
    @Column
    @ApiModelProperty("预算总额")
    private BigDecimal budgetAmount;

    /**
     * 货号
     */
    @Column(length = 200)
    @ApiModelProperty("货号")
    @Length(message = "货号{validation.message.length}", max = 255)
    private String articleNo;

    /**
     * 使用人id（多个使用人用逗号隔开）
     */
    @Column(length = 50)
    @ApiModelProperty("使用人id")
    private String userId;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}