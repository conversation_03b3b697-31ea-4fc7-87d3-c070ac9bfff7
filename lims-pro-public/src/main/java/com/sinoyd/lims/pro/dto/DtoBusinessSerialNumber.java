package com.sinoyd.lims.pro.dto;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.entity.BusinessSerialNumber;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 业务流水号实体传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/2/29
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_BusinessSerialNumber")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoBusinessSerialNumber extends BusinessSerialNumber {

    /**
     * 流水号整数形式
     */
    @Transient
    private Integer serialNumber;

    public Integer getSerialNumber() {
        if (StringUtil.isNotEmpty(getPara1())) {
            return Integer.parseInt(getPara1());
        }
        return 0;
    }
}