package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.NewSearchResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 查新结果
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_NewSearchResult")
@Data
@DynamicInsert
public class DtoNewSearchResult extends NewSearchResult {

    /**
     * 执行人
     */
    @Transient
    private String executor;

    /**
     * 确认人名称
     */
    @Transient
    private String confirmName;

    /**
     * 确认任务确认人名称
     */
    @Transient
    private String executeConfirmName;

    /**
     * 宣贯任务宣贯人名称
     */
    @Transient
    private String executePropagateName;

    /**
     * 填充字段斜杠
     *
     * @param newSearchResult 结果实体
     */
    public void fillField(DtoNewSearchResult newSearchResult) {
        newSearchResult.setStandardName("/");
        newSearchResult.setStandardNum("/");
        newSearchResult.setYear("/");
        newSearchResult.setReplaceNum("/");
        newSearchResult.setReplaceNum("/");
    }
}
