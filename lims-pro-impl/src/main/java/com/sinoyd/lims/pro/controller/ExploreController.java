package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.ExploreCriteria;
import com.sinoyd.lims.pro.dto.DtoExplore;
import com.sinoyd.lims.pro.service.ExploreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * Explore服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@Api(tags = "示例: Explore服务")
@RestController
@RequestMapping("api/pro/explore")
public class ExploreController extends BaseJpaController<DtoExplore, String, ExploreService> {


    /**
     * 分页动态条件查询Explore
     *
     * @param exploreCriteria 条件参数
     * @return RestResponse<List < Explore>>
     */
    @ApiOperation(value = "分页动态条件查询Explore", notes = "分页动态条件查询Explore")
    @GetMapping
    public RestResponse<List<DtoExplore>> findByPage(ExploreCriteria exploreCriteria) {
        PageBean<DtoExplore> pageBean = super.getPageBean();
        RestResponse<List<DtoExplore>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, exploreCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 加载项目的踏勘信息
     *
     * @param projectId 项目id
     * @return RestResponse<List < Explore>>
     */
    @ApiOperation(value = "加载项目的踏勘信息", notes = "加载项目的踏勘信息")
    @GetMapping(path = "/list/{projectId}")
    public RestResponse<List<DtoExplore>> loadProjectExplore(@PathVariable("projectId") String projectId) {
        RestResponse<List<DtoExplore>> restResponse = new RestResponse<>();
        List<DtoExplore> dataList = service.loadProjectExplore(projectId);
        restResponse.setRestStatus(StringUtil.isEmpty(dataList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(dataList);
        restResponse.setCount(StringUtil.isEmpty(dataList) ? 0 : dataList.size());
        return restResponse;
    }

    /**
     * 按主键查询Explore
     *
     * @param id 主键id
     * @return RestResponse<DtoExplore>
     */
    @ApiOperation(value = "按主键查询Explore", notes = "按主键查询Explore")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoExplore> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoExplore> restResponse = new RestResponse<>();
        DtoExplore explore = service.findOne(id);
        restResponse.setData(explore);
        restResponse.setRestStatus(StringUtil.isNull(explore) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增Explore
     *
     * @param explore 实体
     * @return RestResponse<DtoExplore>
     */
    @ApiOperation(value = "新增Explore", notes = "新增Explore")
    @PostMapping
    public RestResponse<DtoExplore> create(@RequestBody @Validated DtoExplore explore) {
        RestResponse<DtoExplore> restResponse = new RestResponse<>();
        restResponse.setData(service.save(explore));
        return restResponse;
    }

    /**
     * 批量保存(包含更新)Explore
     *
     * @param exploreList 实体列表
     * @return RestResponse<DtoExplore>
     */
    @ApiOperation(value = "批量保存(包含更新)Explore", notes = "批量保存(包含更新)Explore")
    @PostMapping(path = "/batch")
    public RestResponse<List<DtoExplore>> batchSave(@RequestBody List<DtoExplore> exploreList) {
        RestResponse<List<DtoExplore>> restResponse = new RestResponse<>();
        restResponse.setData(service.save(exploreList));
        return restResponse;
    }

    /**
     * 新增Explore
     *
     * @param explore 实体列表
     * @return RestResponse<DtoExplore>
     */
    @ApiOperation(value = "修改Explore", notes = "修改Explore")
    @PutMapping
    public RestResponse<DtoExplore> update(@RequestBody @Validated DtoExplore explore) {
        RestResponse<DtoExplore> restResponse = new RestResponse<>();
        restResponse.setData(service.update(explore));
        return restResponse;
    }

    /**
     * "根据id批量删除Explore
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Explore", notes = "根据id批量删除Explore")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}