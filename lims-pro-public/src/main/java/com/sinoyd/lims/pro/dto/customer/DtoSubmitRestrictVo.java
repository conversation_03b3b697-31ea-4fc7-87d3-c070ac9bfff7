package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0.0 2022/09/30
 * @since V100R001
 */
@Data
public class DtoSubmitRestrictVo {

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 检查内容
     */
    private String checkItem;

    /**
     * 异常说明
     */
    private String exceptionOption = "验证通过";

    /**
     * 是否允许
     */
    private Boolean isPass = Boolean.TRUE;

    /**
     * 是否异常
     */
    private Boolean isUnusual = Boolean.TRUE;
}
