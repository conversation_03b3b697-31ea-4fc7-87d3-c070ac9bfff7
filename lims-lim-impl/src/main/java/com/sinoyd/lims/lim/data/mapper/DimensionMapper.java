package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.lims.lim.dto.customer.DtoExportDimension;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 量纲实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface DimensionMapper {


    /**
     * DtoDimension 实例转换成DtoExportDimension实例
     *
     * @param dimension 分析项目实体
     * @return DtoExportDimension 实例
     */
    @Mapping(source = "orderNum", target = "orderNum")
    @Mapping(source = "baseValue", target = "baseValue", qualifiedByName = "bigDecimalToStr")
    DtoExportDimension toExportDimension(DtoDimension dimension);

    /**
     * DtoDimension 实例集合转换成DtoExportDimension 实例集合
     *
     * @param dimensionList 分析项目实例集合
     * @return DtoExportDimension 实例集合
     */
    @InheritConfiguration(name = "toExportDimension")
    List<DtoExportDimension> toExportDimensionList(List<DtoDimension> dimensionList);


    /**
     * DtoExportDimension 实例转换成DtoDimension 实例
     *
     * @param exportDimension 分析项目实体
     * @return DtoExportDimension 实例
     */
    @Mapping(source = "orderNum", target = "orderNum")
    @Mapping(source = "baseValue", target = "baseValue")
    DtoDimension toDtoDimension(DtoExportDimension exportDimension);

    /**
     * DtoExportDimension 实例集合转换成DtoDimension 实例集合
     *
     * @param exportDimensionList 分析项目导入导出实例集合
     * @return DtoDimension 实例集合
     */
    @InheritConfiguration(name = "toDtoDimension")
    List<DtoDimension> toDtoDimensionList(List<DtoExportDimension> exportDimensionList);

    @Named("bigDecimalToStr")
    default String bigDecimalToStr(BigDecimal bigDecimal) {
        return bigDecimal.toPlainString();
    }
}
