package com.sinoyd.base.dto.customer;

import lombok.Data;

import java.util.List;

/**
 * 评价信息复制传输VO
 *
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/04/19
 */
@Data
public class DtoEvaluationRecordCopyVO {

    /**
     * 复制源点位id
     */
    private String sampleFolderId;

    /**
     * 复制目标点位id集合
     */
    private List<String> targetSampleFolderIds;

    /**
     * 类型
     */
    private Integer objectType;

    /**
     * 点位计划
     */
    private Integer folderPlan;

}
