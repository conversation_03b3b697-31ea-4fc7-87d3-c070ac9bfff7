package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;


/**
 * 项目查询条件污染源
 * <AUTHOR>
 * @version V1.0.0 2021年05月07日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PollutionStatisticsCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目名称和项目编号关键字和受检单位
     */
    private String key;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 是否多企业
     */
    private Boolean isMultiEnterprise;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        //如果前端传了检测类型id就两表联查
        if (StringUtil.isNotEmpty(sampleTypeId)){
            condition.append(" and a.id = b.projectId ");
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.inputTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and a.inputTime < :endTime");
            values.put("endTime", to);
        }
        if(StringUtil.isNotEmpty(this.key)){
            condition.append(" and (a.projectCode like :key or  a.projectName like :key or exists (select 1 from DtoSampleFolder folder where a.id = folder.projectId and folder.inspectedEnt like :key)) ");
            values.put("key", "%" + this.key + "%");
        }
        if(StringUtil.isNotEmpty(this.sampleTypeId)){
            condition.append(" and b.sampleTypeId = :sampleTypeId ");
            values.put("sampleTypeId",  this.sampleTypeId );
        }
        if (StringUtil.isNotNull(this.isMultiEnterprise)){
            condition.append(" and a.isMultiEnterprise = :isMultiEnterprise");
            values.put("isMultiEnterprise",this.isMultiEnterprise);
        }
        return condition.toString();
    }
}