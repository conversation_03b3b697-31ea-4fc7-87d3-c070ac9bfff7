package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * FileControlApplyDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="FileControlApplyDetail")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class FileControlApplyDetail implements BaseEntity,Serializable {

    private static final long serialVersionUID = 1L;

    public FileControlApplyDetail() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 父级id(最新的处理文件信息的父级id为Int，之前的文件信息的父级id是最新的文件信息的id)
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("父级id(最新的处理文件信息的父级id为Int，之前的文件信息的父级id是最新的文件信息的id)")
    private String parentId;

    /**
     * 申请单id（最新的文件记录，申请单id为Id）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("申请单id（最新的文件记录，申请单id为Id）")
    private String fileApplyId;

    /**
     * 发放回收id（只有发放回收的时候id才有数据，如果发放回收记录删除，id要清空）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("发放回收id（只有发放回收的时候id才有数据，如果发放回收记录删除，id要清空）")
    private String grantId;

    /**
     * 受控编号
     */
    @Column(length = 100)
    @ApiModelProperty("受控编号")
    @Length(message = "受控编号{validation.message.length}", max = 100)
    private String controlCode;

    /**
     * 文件名称
     */
    @Column(length = 100)
    @ApiModelProperty("文件名称")
    @Length(message = "文件名称{validation.message.length}", max = 100)
    private String fileName;

    /**
     * 文件编号
     */
    @Column(length = 100)
    @ApiModelProperty("文件编号")
    @Length(message = "文件编号{validation.message.length}", max = 100)
    private String fileCode;

    /**
     * 文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）")
    @Length(message = "文件类型{validation.message.length}", max = 50)
    private String fileType;

    /**
     * 编制人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("编制人id")
    private String maker;

    /**
     * 编制时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("编制时间")
    private Date compileTime;

    /**
     * 状态（枚举EnumFileControlApplyDetailStatus：1：未受控、2：受控申请中、3：修订中、4：废止中、5：已受控、6：已废止）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("状态（枚举EnumFileControlApplyDetailStatus：1：未受控、2：受控申请中、3：修订中、4：废止中、5：已受控、6：已废止）")
    private Integer status;

    /**
     * 版本号
     */
    @Column(length = 50)
    @ApiModelProperty("版本号")
    @Length(message = "版本号{validation.message.length}", max = 50)
    private String version;

    /**
     * 受控日期
     */
    @ApiModelProperty("受控日期")
    private Date controlDate;

    /**
     * 废止日期
     */
    @ApiModelProperty("废止日期")
    private Date abolishDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}