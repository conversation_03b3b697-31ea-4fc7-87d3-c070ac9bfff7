package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 车辆管理-消费记录
 * <AUTHOR> 修改：徐肖波
 * @version v1.0.0 2019/3/12
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CarConsumerRecordCriteria extends BaseCriteria {

    /**
     * 消费人员id（空Guid代表所有）
     */
    private String salesManId;

    /**
     * 所属科室id（空Guid代表所有）
     */
    private String deptId;

    private List<String> salesManIds;

    /**
     * 检索开始时间
     */
    private String dtBegin;

    /**
     * 检索结束时间
     */
    private String dtEnd;

    /**
     * 车辆id
     */
    private String carId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and salseDate >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtils.isNotNullAndEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and salseDate < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        if (StringUtils.isNotNullAndEmpty(salesManId) && !UUIDHelper.GUID_EMPTY.equals(this.salesManId)) {
            condition.append(" and (salesManId = :salesManId)");
            values.put("salesManId", this.salesManId);
        }
        if (StringUtil.isNotEmpty(salesManIds)) {
            condition.append(" and (salesManId in :salesManIds)");
            values.put("salesManIds", this.salesManIds);
        }
        if (StringUtils.isNotNullAndEmpty(carId) && !UUIDHelper.GUID_EMPTY.equals(this.carId)) {
            condition.append(" and (carId = :carId)");
            values.put("carId", this.carId);
        }
        return condition.toString();
    }
}