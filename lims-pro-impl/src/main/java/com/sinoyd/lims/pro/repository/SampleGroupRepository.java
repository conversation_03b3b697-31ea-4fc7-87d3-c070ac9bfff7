package com.sinoyd.lims.pro.repository;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;

import java.util.List;


/**
 * SampleGroup数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
public interface SampleGroupRepository extends IBaseJpaRepository<DtoSampleGroup, String>, LimsRepository<DtoSampleGroup, String> {

    /**
     * 根据送样单id查询样品分组
     *
     * @param receiveId 送样单id
     * @return 查询结果
     */
    List<DtoSampleGroup> findByReceiveId(String receiveId);

    /**
     * 根据送样单id查询样品分组
     *
     * @param receiveIds 送样单ids
     * @return 查询结果
     */
    List<DtoSampleGroup> findByReceiveIdIn(List<String> receiveIds);

    /**
     * 根据样品id集合查询样品分组
     *
     * @param sampleIds 样品id集合
     * @return 查询结果
     */
    List<DtoSampleGroup> findBySampleIdIn(List<String> sampleIds);

    /**
     * 根据样品id和分组id获取样品分组
     * @param sampleId 样品id
     * @param groupId 分组id
     * @return 样品分组
     */
    DtoSampleGroup findBySampleIdAndSampleTypeGroupId(String sampleId,String groupId);
}