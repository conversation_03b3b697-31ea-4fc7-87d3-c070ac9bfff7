package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.od.dto.DtoLabGroup;

import java.util.List;

/**
 * LabGroupRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface LabGroupRepository extends IBaseJpaRepository<DtoLabGroup, String> {

    /**
     * 根据样品id查询
     *
     * @param sampleId 样品id
     * @return 实验组集合
     */
    List<DtoLabGroup> findBySampleId(String sampleId);

    /**
     * 根据样品ids 查询实验组集合
     *
     * @param sampleIds 样品ids
     * @return 实验组集合
     */
    List<DtoLabGroup> findBySampleIdIn(List<String> sampleIds);
}
