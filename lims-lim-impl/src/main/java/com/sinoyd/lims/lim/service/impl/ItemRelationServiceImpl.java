package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelation;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ItemRelationParamsRepository;
import com.sinoyd.lims.lim.repository.rcc.ItemRelationRepository;
import com.sinoyd.lims.lim.service.ItemRelationParamsService;
import com.sinoyd.lims.lim.service.ItemRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * ItemRelation操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
 @Service
public class ItemRelationServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoItemRelation,String, ItemRelationRepository> implements ItemRelationService {

    private ItemRelationParamsService itemRelationParamsService;

    private ItemRelationParamsRepository itemRelationParamsRepository;

    @Override
    public void findByPage(PageBean<DtoItemRelation> pb, BaseCriteria itemRelationCriteria) {
        pb.setEntityName("DtoItemRelation a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, itemRelationCriteria);
    }

    @Transactional
    @Override
    public DtoItemRelation save(DtoItemRelation itemRelation) {
        DtoItemRelation finalItemRelation = super.save(itemRelation);
        List<DtoItemRelationParams> itemRelationParamsList = itemRelation.getItemRelationParamsList();
        itemRelationParamsList.forEach(p -> {
            p.setRelationId(finalItemRelation.getId());
        });
        itemRelationParamsService.save(itemRelationParamsList);
        return finalItemRelation;
    }

    @Override
    public DtoItemRelation findOne(String id){
        DtoItemRelation itemRelation = super.findOne(id);
        List<DtoItemRelationParams> itemRelationParamsList = itemRelationParamsService.findByRelationIds(Collections.singleton(id));
        itemRelation.setItemRelationParamsList(itemRelationParamsList);
        return itemRelation;
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> itemRelationParamsIds = itemRelationParamsService.findByRelationIds(ids)
                .stream().map(DtoItemRelationParams::getId).collect(Collectors.toList());
        if (itemRelationParamsIds.size() > 0) {
            itemRelationParamsService.logicDeleteById(itemRelationParamsIds);
        }
        return super.logicDeleteById(ids);
    }

    @Override
    public Map<String, List<String>> findCombineConfig() {
        Map<String,List<String>> calculateMap = new HashMap<>();
        //挑选符合条件的公式并处理参数
        List<DtoItemRelation> itemRelationList = repository.findByType(EnumLIM.EnumAnalyzeItemRelationType.上报.getValue()).stream()
                .filter(v->v.getSymbolType()==1&& StringUtil.isNotEmpty(v.getLeftFormula())&&StringUtil.isNotEmpty(v.getRightFormula()))
                .collect(Collectors.toList());
        List<String> relationIds = itemRelationList.stream().map(DtoItemRelation::getId).collect(Collectors.toList());
        List<DtoItemRelationParams> itemRelationParamsList = StringUtil.isNotEmpty(relationIds)?itemRelationParamsRepository.findByRelationIdIn(relationIds):new ArrayList<>();
        //key 是合并后分析项目标识  value是需要累加的分析项目标识集合
        for (DtoItemRelation itemRelation:itemRelationList) {
            List<DtoItemRelationParams> paramsList = itemRelationParamsList.stream().filter(v->itemRelation.getId().equals(v.getRelationId())).collect(Collectors.toList());
            String keyItemId = paramsList.stream().filter(v->itemRelation.getLeftFormula().contains("["+v.getAnalyzeItemName()+"]"))
                    .map(DtoItemRelationParams::getAnalyzeItemId).findFirst().orElse("");
            List<String> partItemIdList = paramsList.stream().filter(v->itemRelation.getRightFormula().contains("["+v.getAnalyzeItemName()+"]"))
                    .map(DtoItemRelationParams::getAnalyzeItemId).collect(Collectors.toList());
            if(StringUtil.isNotEmpty(keyItemId)&&StringUtil.isNotEmpty(partItemIdList)){
                calculateMap.put(keyItemId,partItemIdList);
            }
        }
        return calculateMap;
    }

    @Autowired
    @Lazy
    public void setItemRelationParamsService(ItemRelationParamsService itemRelationParamsService) {
        this.itemRelationParamsService = itemRelationParamsService;
    }

    @Autowired
    public void setItemRelationParamsRepository(ItemRelationParamsRepository itemRelationParamsRepository) {
        this.itemRelationParamsRepository = itemRelationParamsRepository;
    }
}