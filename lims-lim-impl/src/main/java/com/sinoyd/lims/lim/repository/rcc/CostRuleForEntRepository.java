package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.lims.lim.dto.rcc.DtoCostRuleForEnt;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * CostRuleForEnt数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/9
 * @since V100R001
 */
public interface CostRuleForEntRepository extends IBaseJpaPhysicalDeleteRepository<DtoCostRuleForEnt, String> {

     /**
     * 获取客户的费用规则
     *
     * @param entId 客户id
     * @return 对应客户的费用规则
     */
    DtoCostRuleForEnt getByEntId (String entId);

    /**
     * 根据客户id集合查询
     * @param ids id集合
     * @return
     */
    List<DtoCostRuleForEnt> findByEntIdIn(List<String> ids);
}