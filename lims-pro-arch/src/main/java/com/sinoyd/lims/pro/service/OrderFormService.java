package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoOrderForm;
import com.sinoyd.lims.pro.dto.DtoOrderQuotation;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import com.sinoyd.lims.pro.dto.customer.DtoOrderSubmit;
import com.sinoyd.lims.pro.dto.customer.DtoSignOrder;

import java.util.Date;
import java.util.List;


/**
 * OrderForm操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
public interface OrderFormService extends IBaseJpaService<DtoOrderForm,String> {

    /**
     * 创建订单编号
     * @param projectTypeId 业务类型id
     * @param inputTime 订单时间
     * @return 订单编号
     */
    String createOrderCode(String projectTypeId, Date inputTime);

    /**
     * 修改费用详情
     * @param orderId 订单Id
     * @param isOther 是否其他费用
     * @param isQuotation 是否费用明细
     * @return 费用详情
     */
    DtoOrderQuotation updateQuotation(String orderId, Boolean isOther, Boolean isQuotation);

    /**
     * 更新已检+未检数
     * @param orderId 订单id
     */
    List<DtoQuotationDetail> updateCount(String orderId);

    /**
     * 提交订单
     * @param orderSubmit 提交信息
     */
    void submit(DtoOrderSubmit orderSubmit);

    /**
     * 审核订单
     * @param orderSubmit 订单信息
     */
    void auditOrder(DtoOrderSubmit orderSubmit);

    /**
     * 复制订单
     *
     * @param orderIds 订单数据
     */
    void copyOrderForm(List<String> orderIds);


    /**
     * 订单签订
     *
     * @param signOrder 订单信息
     */
    void signingOrder(DtoSignOrder signOrder);

    /**
     * 订单附件
     * @param id 主键
     * @return DtoOrderForm
     */
    DtoOrderForm findAttachPath(String id);
}