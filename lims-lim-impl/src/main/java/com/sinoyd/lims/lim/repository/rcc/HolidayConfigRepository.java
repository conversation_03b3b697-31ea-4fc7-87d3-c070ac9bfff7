package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoHolidayConfig;

import java.util.List;

/**
 * 节假日管理配置仓储
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/18
 */
public interface HolidayConfigRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoHolidayConfig, String> {


    /**
     * 根据年份查询
     *
     * @param year 年份
     * @return 节假日管理配置List
     */
    List<DtoHolidayConfig> findByYear(Integer year);

    /**
     * 根据年份删除
     *
     * @param year 年份
     */
    void deleteByYear(Integer year);

}