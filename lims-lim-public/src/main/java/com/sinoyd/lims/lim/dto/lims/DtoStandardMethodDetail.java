package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.PersonAbility;
import com.sinoyd.lims.lim.entity.StandardMethodDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * DtoStandardMethodDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_StandardMethodDetail")
@Data
@DynamicInsert
public class DtoStandardMethodDetail extends StandardMethodDetail {


    /**
     * 选中测试项目或者采样方法ids
     */
    @Transient
    private List<String> objectIds = new ArrayList<>();

    /**
     * 项目名称
     */
    @Transient
    private String redAnalyzeItemName;

    /**
     * 方法名称
     */
    @Transient
    private String redAnalyzeMethodName;

    /**
     * 标准编号
     */
    @Transient
    private String redCountryStandard;


}