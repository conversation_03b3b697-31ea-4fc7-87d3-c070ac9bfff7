package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.lims.DtoReportApply;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.ReportApplyRepository;
import com.sinoyd.lims.lim.service.ReportApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 报表模板配置应用同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/25
 */
@Component
@DependsOn({"springContextAware"})
@Order(15)
@Slf4j
public class ReportApplySync extends AbsDataSync<DtoReportApply> {

    private ReportApplyService reportApplyService;

    private ReportApplyRepository reportApplyRepository;

    /**
     * 数据比较
     * @param reportConfigIds 需要同步的数据id
     * @return List<DtoDataCompareResult<DtoReportApply>>
     */
    @Override
    public List<DtoDataCompareResult<DtoReportApply>> compareData(List<String> reportConfigIds) {
        //获取项目上所有报表模板应用配置
        List<DtoReportApply> projectDataList=reportApplyService.findAll();
        //公共库中的报表模板应用配置
        List<DtoReportApply> standardDataList=queryStandardData();
        //比较数据
        if (StringUtil.isNotEmpty(reportConfigIds)) {
            standardDataList = standardDataList.parallelStream().filter(p -> reportConfigIds.contains(p.getReportConfigId()))
                    .collect(Collectors.toList());
        }
        return compareData(standardDataList,projectDataList);
    }

    /**
     * 同步数据
     *
     * @param dataIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> dataIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoReportApply>> compareResult = compareData(dataIds);
        Optional<DtoDataCompareResult<DtoReportApply>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoReportApply errorDto = null;
            try {
                for (DtoReportApply dtoReportApply : r.getAddDataList()) {
                    errorDto = dtoReportApply;
                    //防止公共库未被假删，但项目库被假删，此种情况直接更新
                    if (reportApplyRepository.findOne(dtoReportApply.getId()) != null) {
                        reportApplyService.update(dtoReportApply);
                    } else {
                        reportApplyService.save(dtoReportApply);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.报表配置应用.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.报表配置应用.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/reportApply";
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.报表配置应用.getValue();
    }

    @Autowired
    @Lazy
    public void setReportApplyService(ReportApplyService reportApplyService) {
        this.reportApplyService = reportApplyService;
    }

    @Autowired
    public void setReportApplyRepository(ReportApplyRepository reportApplyRepository) {
        this.reportApplyRepository = reportApplyRepository;
    }
}
