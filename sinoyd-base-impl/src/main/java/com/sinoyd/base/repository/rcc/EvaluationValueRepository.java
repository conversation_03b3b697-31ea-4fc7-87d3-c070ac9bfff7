package com.sinoyd.base.repository.rcc;

import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * EvaluationValue数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
public interface EvaluationValueRepository extends IBaseJpaPhysicalDeleteRepository<DtoEvaluationValue, String>, LimsRepository<DtoEvaluationValue, String> {

    /**
     * 根据所有的评价等级获取相应的评价配置
     *
     * @param levelIds 评价等级
     * @return 返回评价配置信息
     */
    List<DtoEvaluationValue> findByLevelIdIn(List<String> levelIds);


    /**
     * 根据等级id 获取相应的评价信息
     *
     * @param levelId 等级id
     * @return 返回相应的评价信息
     */
    List<DtoEvaluationValue> findByLevelId(String levelId);


    /**
     * 根据评价标准ID获取相关的评价信息
     *
     * @param evaluationId 评价等级ID
     * @param levelId      排除当前等级id
     * @return 返回想要的评价信息
     */
    List<DtoEvaluationValue> findByEvaluationIdAndLevelIdNot(String evaluationId, String levelId);

    /**
     * 批量修改上限及下限符号
     * @param lowerLimitSymbol 下限符号
     * @param upperLimitSymbol 上限符号
     * @param ids 主键ids
     * @return 返回修改行数
     */
    @Transactional
    @Modifying
    @Query("update DtoEvaluationValue  set lowerLimitSymble = :lowerLimitSymbol,upperLimitSymble = :upperLimitSymbol where id in :ids")
    Integer updateSymbol(@Param("lowerLimitSymbol") String lowerLimitSymbol,
                         @Param("upperLimitSymbol") String upperLimitSymbol, @Param("ids") List<String> ids);

    /**
     * 批量修改量纲
     * @param dimensionId 量纲Id
     * @param ids 主键ids
     * @return 返回修改行数
     */
    @Transactional
    @Modifying
    @Query("update DtoEvaluationValue  set dimensionId = :dimensionId where id in :ids")
    Integer batchUpdateDimension(@Param("dimensionId") String dimensionId, @Param("ids") List<String> ids);


    /**
     * 根据所有的评价标准获取相应的评价配置
     *
     * @param evaluationIds 评价标准id
     * @return 返回评价配置信息
     */
    List<DtoEvaluationValue> findByEvaluationIdIn(List<String> evaluationIds);
}