package com.sinoyd.lims.api.service;

import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.api.dto.*;
import com.sinoyd.lims.api.dto.customer.DtoParamsDataApiPhone;
import com.sinoyd.lims.api.dto.customer.DtoSampleCopy;
import com.sinoyd.lims.api.dto.customer.DtoSampleParamsApiPhone;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoSampleDataPhone;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FieldMonitoringService extends IBaseJpaService<DtoProject, String> {

    /**
     * 项目查询
     *
     * @param pb                   pagebean
     * @param projectPhoneCriteria 查询条件
     */
    List<DtoProjectPhone> getProjectList(PageBean<DtoProject> pb, BaseCriteria projectPhoneCriteria);

    List<DtoProjectPhone> getReceiveProjectList(PageBean<DtoReceiveSampleRecord> pb, BaseCriteria receivePhoneCriteria);

    /**
     * 通过编码查询项目类型
     *
     * @param code   编码
     * @param values 类型
     * @return 项目类型集合
     */
    List<Map<String, Object>> getProjectTypeList(String code, String[] values);

    /**
     * 获取样品类型小类
     *
     * @return 样品类型小类
     */
    List<TreeNode> getSampleTypeList();

    /**
     * 地图底图是否使用天地图
     * true 是，false 否
     *
     * @return 返回判断
     */
    boolean isWorldEarth();

    /**
     * @param map 传参
     * @return 样品标签数据
     */
    Object createSampleLabelData(Map<String, Object> map);

    /**
     * 获取项目详情
     *
     * @param projectId 项目id
     * @return 项目详情
     */
    DtoProjectDetailPhone getProjectDetailById(String projectId);

    /**
     * 获取点位信息
     *
     * @param projectId     项目id
     * @param folderName    点位名称
     * @param sampleTypeIds 样品类型id
     * @param cycleOrder    周期
     * @param timesOrder    次数
     * @return 点位列表
     */
    List<DtoSampleFolderPhone> getFolderList(String projectId, String folderName, List<String> sampleTypeIds, Integer cycleOrder, Integer timesOrder);

    /**
     * 获取最大周期和最大次数
     *
     * @param projectId 项目id
     * @return 最大周期和最大次数
     */
    Map<String, Object> getMaxCount(String projectId);

    /**
     * 创建送样单
     *
     * @param projectId          项目id
     * @param folderInfo         点位信息
     * @param samplingPersonIds  采样人员
     * @param samplingLeaderId   采样负责人id
     * @param samplingLeaderName 采样负责人名称
     * @param samplingTime       采样时间
     */
    void createReceiveSampleRecord(String projectId, List<String> folderInfo, List<String> samplingPersonIds, String samplingLeaderId, String samplingLeaderName, Date samplingTime);

    /**
     * 创建送样单（委托现场监测）
     *
     * @param receiveSampleRecord 送样单信息
     * @return 送样单信息
     */
    DtoReceiveSampleRecordPhone createReceiveSampleRecord(DtoReceiveSampleRecord receiveSampleRecord);

    /**
     * 获取详情
     *
     * @param projectId 项目id
     * @param receiveId 送样单id
     * @return 详情
     */
    DtoReceiveSampleRecordPhone getRecord(String projectId, String receiveId);

    /**
     * 加入已有采样单
     *
     * @param projectId  项目id
     * @param receiveId  送样单id
     * @param folderInfo 点位信息
     */
    void joinToReceiveSampleRecord(String projectId, String receiveId, List<String> folderInfo);

    /**
     * 通过项目id获取送样单集合
     *
     * @param projectId 项目id
     * @return 送样单集合
     */
    List<DtoReceiveSampleRecordPhone> getReceiveSampleRecordByProjectId(String projectId, List<String> sampleTypeIds, Boolean isFilterSamPerson);

    /**
     * 通过点位获取点位信息
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @return 点位信息
     */
    DtoSampleFolderPhone getFolderById(String folderId, Integer cycleOrder,String receiveId);

    /**
     * 保存签到信息
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @param lon        经度
     * @param lat        纬度
     * @param signTime   签到时间
     */
    void saveSignInfo(String folderId, Integer cycleOrder, String lon, String lat,
                      Date signTime, String signTip, Boolean isVerify, String voiceTip);

    /**
     * 保存语音说明
     *
     * @param folderSignId 签到信息id
     * @param voiceTip     语音说明
     */
    void saveVoiceTip(String folderSignId, String voiceTip);

    /**
     * 获取样品列表
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @return 样品列表
     */
    List<DtoSample> getSampleList(String folderId, String projectId, String receiveId, Integer cycleOrder);

    /**
     * 获取样品列表(包含作废样品）
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @return 样品列表
     */
    List<DtoSample> getSampleWithInvalidList(String folderId, String projectId, String receiveId, Integer cycleOrder);

    /**
     * 获取样品列表(包含作废样品和标样）
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @return 样品列表
     */
    List<DtoSample> getSampleWithInvalidAndStandardList(String folderId, String projectId, String receiveId, Integer cycleOrder);

    /**
     * 获取样品列表
     *
     * @param folderId   点位id
     * @param cycleOrder 周期
     * @return 样品列表
     */
    List<DtoSample> getSampleList(String folderId, String projectId, List<String> receiveIdList, Integer cycleOrder);

    /**
     * 剔除样品
     *
     * @param qcSampleIds 质控样ids
     */
    void removeQcSample(String projectId, List<String> qcSampleIds);

    /**
     * 创建样品编号
     *
     * @param sampleIds    样品ids
     * @param projectId    项目id
     * @param samplingTime 采样时间
     */
    void createSampleCode(List<String> sampleIds, String projectId, Date samplingTime);

    /**
     * 创建样品编号
     *
     * @param sampleIds    样品ids
     * @param projectId    项目id
     * @param samplingTime 采样时间
     */
    void createSampleCode(List<String> sampleIds, String projectId, Date samplingTime, String receiveId);

    /**
     * 获取样品信息
     *
     * @param samId 样品id
     * @return 样品信息
     */
    DtoSamplePhone getSampleDetail(String samId);

    /**
     * 测试项目列表
     *
     * @param pb           分页
     * @param testCriteria 检索条件
     * @return 测试项目列表
     */
    List<DtoAnalyseDataPhone> getTestList(PageBean<DtoTest> pb, BaseCriteria testCriteria);

    /**
     * 提交送样单
     *
     * @param recIds 送样单ids
     */
    void submitReceive(List<String> recIds);

    /**
     * 采样单详情
     *
     * @param recId 送样单id
     * @return 送样单详情
     */
    DtoReceiveSampleRecordPhone getRecordById(String recId);

    /**
     * 保存送样单信息
     *
     * @param receiveSampleRecordPhone 送样单信息
     */
    void saveRecordInfo(DtoReceiveSampleRecordPhone receiveSampleRecordPhone);

    /**
     * 通过送样单id获取点位信息
     *
     * @param recId 送样单id
     * @return 点位列表
     */
    List<DtoSampleFolderPhone> getFolderInfoByReceiveId(String recId);

    /**
     * 剔除送样单样品
     *
     * @param recId      送样单id
     * @param folderInfo 点位信息
     */
    void removeSample(String recId, List<String> folderInfo);

    /**
     * 剔除送样单样品
     *
     * @param recId      送样单id
     * @param folderInfo 点位信息
     */
    void removeOutSample(String recId, List<String> folderInfo);

    /**
     * 获取点位、公共参数
     *
     * @param sampleFolderId 点位id
     * @param sampleTypeId   样品类型id
     * @param cycleOrder     周期
     * @return 点位、公共参数
     */
    List<Map<String, Object>> getFolderParams(String sampleFolderId, String projectId, String receiveId, String sampleTypeId, Integer cycleOrder);

    /**
     * 获取样品参数
     *
     * @param sampleId     样品id
     * @param sampleTypeId 样品类型id
     * @return 样品参数
     */
    DtoSampleParamsApiPhone getSampleParams(String sampleId, String sampleTypeId, Integer isEnterValue);

    /**
     * 获取样品参数
     *
     * @param sampleId     样品id
     * @param sampleTypeId 样品类型id
     * @return 样品参数
     */
    List<DtoSampleParamsApiPhone> getSampleParamsList(String sampleId, String sampleTypeId, Integer isEnterValue);

    /**
     * 保存点位参数
     *
     * @param sampleFolderId      点位id
     * @param projectId           项目id
     * @param receiveId           送样单id
     * @param cycleOrder          周期
     * @param paramsDataPhoneList 参数数据
     */
    void saveFolderParamsValue(String sampleFolderId, String projectId, String receiveId, Integer cycleOrder, List<DtoParamsDataApiPhone> paramsDataPhoneList);

    /**
     * 保存样品参数
     *
     * @param sampleParamsPhone 样品数据
     */
    void saveSampleParamsValue(DtoSampleParamsApiPhone sampleParamsPhone);

    /**
     * 判断是否人脸采集
     *
     * @return 判断结果
     */
    DtoJudgmentPhone switchIsOpen();

    /**
     * 验证权限
     *
     * @param userId   用户id
     * @param authCode 权限编码
     * @return 时候具有权限
     */
    Boolean validateAuth(String userId, String authCode);

    /**
     * 样品复制
     *
     * @param sampleCopy 传输实体
     */
    void copySample(DtoSampleCopy sampleCopy);

    /**
     * 监测数据详情保存
     *
     * @param sampleDataPhone 监测数据详情
     */
    void saveSampleDataPhone(DtoSampleDataPhone sampleDataPhone);

    /**
     * 采样点位附件上传
     *
     * @param request 请求体
     * @return 附件集合
     */
    List<DtoDocument> uploadFolderFile(HttpServletRequest request);

    /**
     * 查询采样附件，根据点位类型分组
     *
     * @param documentCriteria 查询条件
     * @return 分组后的附件Map
     */
    Map<String, List<DtoDocument>> getFolderFile(PageBean<DtoDocument> pageBean, DocumentCriteria documentCriteria);


    /**
     * 移动端添加现场质控样
     *
     * @param maps 质控样参数
     */
    void addXCSamplePhone(List<Map<String, Object>> maps);

    /**
     * 分页获取消耗品（标样）信息
     *
     * @param criteria 查询条件
     */
    void findStandardByPage(PageBean<DtoConsumable> page, BaseCriteria criteria);

    /**
     * 分页获取量纲信息
     *
     * @param criteria 查询条件
     */
    void findDimensionByPage(PageBean<DtoDimension> page, BaseCriteria criteria);

    /**
     * 移动端剔除质控样
     *
     * @param anaId 分析数据id
     */
    void removeQCDataById(String anaId);

    /**
     * 移动端签到范围
     * @return 签到范围
     */
    String getSignRange();

    /**
     * 保存送样单交接信息
     *
     * @param receiveSampleRecordPhone 送样单信息
     * @return true
     */
    void saveRecordReceiveConfig(DtoReceiveSampleRecordPhone receiveSampleRecordPhone);

    /**
     * 一键接样
     *
     * @param receiveSampleRecordPhone 送样单信息
     * @return true
     */
    void saveSelectReceiveConfig(DtoReceiveSampleRecordPhone receiveSampleRecordPhone);
}
