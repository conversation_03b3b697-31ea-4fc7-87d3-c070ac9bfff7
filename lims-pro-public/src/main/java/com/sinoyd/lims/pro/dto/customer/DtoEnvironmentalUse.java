package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import lombok.Data;
import org.aspectj.lang.annotation.DeclareAnnotation;

/**
 * 仪器记录信息的dto
 * <AUTHOR>
 * @version V1.0.0 2020/2/23
 * @since V100R001
 */
@Data
public class DtoEnvironmentalUse {
    /**
     * 开始时间
     */
    @DeclareAnnotation(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @DeclareAnnotation(value = "结束时间")
    private String endTime;

    /**
     * 温度
     */
    @DeclareAnnotation(value = "温度")
    private String temperature;

    /**
     * 湿度
     */
    @DeclareAnnotation(value = "湿度")
    private String humidity;

    /**
     * 大气压
     */
    @DeclareAnnotation(value = "大气压")
    private String pressure;

    /**
     * 备注
     */
    @DeclareAnnotation(value = "备注")
    private String remark;

    /**
     * 分析指标
     */
    @DeclareAnnotation(value = "分析项目")
    private String testNames;

    /**
     * 样品编号
     */
    @DeclareAnnotation(value = "关联样品")
    private String sampleCodes;

    /**
     * 仪器名称
     */
    @DeclareAnnotation(value = "使用仪器")
    private String instrumentNames;

    public DtoEnvironmentalUse() {

    }

    public DtoEnvironmentalUse(DtoEnvironmentalRecord record) {
        this.startTime = DateUtil.dateToString(record.getStartTime(), DateUtil.FULL_NO_SECOND);
        this.endTime = DateUtil.dateToString(record.getEndTime(), DateUtil.FULL_NO_SECOND);
        this.temperature = record.getTemperature();
        this.humidity = record.getHumidity();
        this.pressure = record.getPressure();
        this.remark =  record.getRemark();
    }
}
