package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoCostRule;
import com.sinoyd.lims.lim.repository.rcc.CostRuleRepository;
import com.sinoyd.lims.lim.service.CostRuleService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.stereotype.Service;


/**
 * 费用规则操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
 @Service
public class CostRuleServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCostRule,String,CostRuleRepository> implements CostRuleService {

    @Override
    public void findByPage(PageBean<DtoCostRule> pb, BaseCriteria costRuleCriteria) {
        pb.setEntityName("DtoCostRule a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, costRuleCriteria);
    }
}