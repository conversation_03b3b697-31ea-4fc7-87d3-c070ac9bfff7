package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.lims.lim.dto.customer.DtoImportQualityControlLimit;
import com.sinoyd.lims.lim.service.DownLoadQcLimitTemplateService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/05/29
 */
@Service
public class DownLoadQcLimitTemplateServiceImpl implements DownLoadQcLimitTemplateService {

    private ImportUtils importUtils;
    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    @Override
    public void downLoadTemplate(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        List<DtoImportQualityControlLimit> qualityControlLimits = getNullIns(new DtoImportQualityControlLimit());

        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportQualityControlLimit.class, qualityControlLimits);

        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    /**
     * 获取空仪器检定数据
     *
     * @return 空list
     */
    private <T> List<T> getNullIns(T t) {
        // 获取仪器空数据
        List<T> formulas = new ArrayList<>();
        formulas.add(t);
        return formulas;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }
}
