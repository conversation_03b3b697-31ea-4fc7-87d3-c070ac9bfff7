package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.criteria.EnterpriseCriteria;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProject2Customer;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.Project2CustomerRepository;
import com.sinoyd.lims.pro.service.MultiPollutionEnterpriseService;
import com.sinoyd.lims.pro.service.ProjectService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 重点污染源多企业
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
@Service
public class MultiPollutionEnterpriseServiceImpl implements MultiPollutionEnterpriseService {

    private Project2CustomerRepository project2CustomerRepository;

    private ProjectService projectService;

    private EnterpriseService enterpriseService;

    /**
     * 判断是否已经选择企业
     *
     * @param projectIds 项目id
     */
    @Override
    public void judgmentEnterprise(List<String> projectIds) throws Exception {
        StringBuilder msg = new StringBuilder();
        List<DtoProject> projects = projectService.findAll(projectIds);
        for (DtoProject project : projects) {
            int count = project2CustomerRepository.findByProjectId(project.getId()).size();
            //当前项目下没有选择企业
            if (count == 0) {
                if (StringUtil.isNotEmpty(msg.toString())) {
                    msg.append(",");
                }
                msg.append(project.getProjectName());
            }
        }
        if (StringUtil.isNotEmpty(msg.toString())) {
            throw new Exception("[" + msg.toString() + "]项目还未选择污染源企业，请确认！");
        }
    }

    /**
     * 根据主项目拆分子项目
     *
     * @param projectId 主项目id
     */
    @Override
    @Transactional
    public void splitSubProject(String projectId) {
        //主项目信息
        DtoProject project = projectService.findOne(projectId);
        //主项目关联企业信息
        List<DtoProject2Customer> customerList = project2CustomerRepository.findByProjectId(projectId);
        //拆分子项目
        createProjectByEnterprise(project, customerList);
    }

    /**
     * 删除多企业污染源项目
     *
     * @param ids 项目ids
     */
    @Override
    @Transactional
    public void logicDeleteByIds(Collection<?> ids) {
        List<DtoProject> projectList = projectService.findByParentIds(ids);
        //删除主项目不需要控制子项目是否已经开展
        //deleteProjectCondition(projectList);
        //找到子项目的ids
        List<String> proIds = projectList.stream().map(DtoProject::getId).collect(Collectors.toList());
        //删除子项目
        if(proIds.size()>0) {
            projectService.logicDeleteById(proIds);
        }
        //删除关联的企业信息
        project2CustomerRepository.deleteByProjectIdIn(ids);
        //再删除主项目
        projectService.logicDeleteById(ids);
    }

    /**
     * 添加企业
     *
     * @param project2CustomerList 企业信息
     */
    @Override
    @Transactional
    public void addEnterprise(List<DtoProject2Customer> project2CustomerList) {
        for (DtoProject2Customer project2Customer : project2CustomerList) {
            //判断企业是否已经存在
            if (project2CustomerRepository.countByProjectIdAndCustomerId(project2Customer.getProjectId(),
                    project2Customer.getCustomerId()) > 0) {
                throw new BaseException("已存在相同企业，请确认！");
            }
        }
        String projectId = project2CustomerList.stream().map(DtoProject2Customer::getProjectId)
                .findFirst().orElse(UUIDHelper.GUID_EMPTY);
        if (!UUIDHelper.GUID_EMPTY.equals(projectId)) {
            //找到添加企业的项目
            DtoProject project = projectService.findOne(projectId);
            //判断项目当前状态，是否为已任务下达，如果已经任务下达，则需要新增项目，如果未下达，则只新增企业
            if (EnumPRO.EnumProjectStatus.已下达.name().equals(project.getStatus())) {
                //根据企业新增项目
                createProjectByEnterprise(project, project2CustomerList);
            }
        }
        //保存企业信息
        project2CustomerRepository.save(project2CustomerList);
    }

    /**
     * 根据id删除企业
     *
     * @param proEntIds ids
     */
    @Override
    @Transactional
    public void deleteEnterprise(List<String> proEntIds) {
        List<DtoProject2Customer> project2CustomerList = project2CustomerRepository.findAll(proEntIds);
        List<String> entIds = project2CustomerList.stream().map(DtoProject2Customer::getCustomerId).distinct().collect(Collectors.toList());
        List<String> projectIds = project2CustomerList.stream().map(DtoProject2Customer::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> projectList = projectService.findByParentIds(projectIds).stream().
                filter(p -> entIds.contains(p.getCustomerId())).collect(Collectors.toList());
        //判断能否删除子项目
        deleteProjectCondition(projectList);
        //删除子项目
        if (projectList.size() > 0) {
            projectService.logicDeleteById(projectList.stream().map(DtoProject::getId).collect(Collectors.toList()));
        }
        //删除企业
        project2CustomerRepository.delete(project2CustomerList);
    }

    //企业查询

    /**
     * 多企业污染源查询关联企业信息
     * @param pageBean 返回信息
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoEnterprise> pageBean, BaseCriteria criteria) {
        EnterpriseCriteria entCriteria = (EnterpriseCriteria) criteria;
        //通过id过滤出关联企业的信息
        List<DtoProject2Customer> customerList = project2CustomerRepository.findByProjectId(entCriteria.getProjectId());
        List<String> entIds = customerList.stream().map(DtoProject2Customer::getCustomerId).distinct().collect(Collectors.toList());
        //判断是否企业查询还是项目企业查询
        if (StringUtil.isNotNull(entCriteria.getIsQuery()) && entCriteria.getIsQuery()) {
            entCriteria.setExistsEntIds(entIds);
        } else {
            entCriteria.setEntIds(entIds);
        }
        enterpriseService.findByPage(pageBean, criteria);
        pageBean.getData().forEach(p -> {
            Optional<DtoProject2Customer> customer = customerList.stream().filter(c -> p.getId().equals(c.getCustomerId())).findFirst();
            customer.ifPresent(c -> p.setProCustomerId(c.getId()));
        });
    }

    /**
     * 自动下发子项目
     *
     * @param project      主项目信息
     * @param customerList 企业集合
     */
    private void createProjectByEnterprise(DtoProject project, List<DtoProject2Customer> customerList) {
        List<String> customerIds = customerList.stream().map(DtoProject2Customer::getCustomerId).distinct().collect(Collectors.toList());
        List<DtoEnterprise> enterpriseList = enterpriseService.findAll(customerIds);
        Integer count = 0;
        Optional<DtoProject> pro = projectService.findByParentIds(Collections.singletonList(project.getId()))
                .stream().max(Comparator.comparing(DtoProject::getProjectCode));
        if (pro.isPresent()) {
            //找到最大的流水号加一
            count = Integer.valueOf(pro.get().getProjectCode().replace(project.getProjectCode() + "-", ""));
        }
        List<DtoProject> projectList = new ArrayList<>();
        //按照企业名称排序
        customerList.sort(Comparator.comparing(DtoProject2Customer::getCustomerName));
        for (DtoProject2Customer p : customerList) {
            DecimalFormat df = new DecimalFormat("000");
            count++;
            DtoProject newProject = new DtoProject();
            BeanUtils.copyProperties(project, newProject, "id");
            newProject.setParentId(project.getId());
            Optional<DtoEnterprise> enterprise = enterpriseList.stream().filter(ent -> ent.getId().equals(p.getCustomerId())).findFirst();
            newProject.setIsMultiEnterprise(Boolean.FALSE);
            //委托单位、受检单位 都要修改
            //污染源多企业子项目没有委托方，所以委托方置空（2023-07-06【长城提】）
            newProject.setCustomerId(UUIDHelper.GUID_EMPTY);
            newProject.setCustomerName("");
            newProject.setInspectedEntId(p.getCustomerId());
            newProject.setInspectedEnt(p.getCustomerName());
            newProject.setProjectName(p.getCustomerName());
            if (enterprise.isPresent()) {
                newProject.setCustomerAddress("");
                newProject.setCustomerOwner("");
                newProject.setLinkMan("");
                newProject.setLinkPhone("");
                newProject.setLinkEmail("");
                newProject.setLinkFax("");
                newProject.setInspectedAddress(enterprise.get().getAddress());
                newProject.setInspectedLinkMan(enterprise.get().getContactMan());
                newProject.setInspectedLinkPhone(enterprise.get().getContactPhoneNumber());
            }
            //项目编号
            String projectCode = String.format("%s-%s", project.getProjectCode(), df.format(count));
            newProject.setProjectCode(projectCode);
            projectList.add(newProject);
        }
        //新增项目
        projectService.saveBatchProject(projectList);
        //纠正项目的状态
        checkProjectSignal(projectList, project.getLeaderId(), project.getIsMakePlan());
    }

    /**
     * 多企业自动创建项目，子项目自动在方案编制中
     *
     * @param projectList 子项目集合
     * @param leaderId    项目负责人id
     */
    private void checkProjectSignal(List<DtoProject> projectList, String leaderId, Boolean isMakePlan) {
        List<String> projectIds = projectList.stream().map(DtoProject::getId).collect(Collectors.toList());
        DtoWorkflowSign workflowSign = new DtoWorkflowSign();
        workflowSign.setObjectIds(projectIds);
        if (isMakePlan) {
            workflowSign.setSignal("makeSolution");
        } else {
            workflowSign.setSignal("projectLaunch");
        }
        workflowSign.setNextOperatorId(leaderId);
        projectService.projectSignal(workflowSign);
    }

    private void deleteProjectCondition(List<DtoProject> projectList) {
        //判断子项目状态是否存在开展中及之后
        if (projectList.stream().anyMatch(p -> EnumPRO.EnumProjectStatus.getByName(p.getStatus()).getValue() >=
                EnumPRO.EnumProjectStatus.开展中.getValue())) {
            throw new BaseException("存在已经开展的子项目，请确认！");
        }
    }

    @Autowired
    public void setProject2CustomerRepository(Project2CustomerRepository project2CustomerRepository) {
        this.project2CustomerRepository = project2CustomerRepository;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    @Lazy
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }
}
