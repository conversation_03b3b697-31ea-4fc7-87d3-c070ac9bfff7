package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.monitor.dto.rcc.DtoWaterExpand;
import com.sinoyd.lims.monitor.repository.rcc.WaterExpandRepository;
import com.sinoyd.lims.monitor.service.WaterExpandService;
import org.springframework.stereotype.Service;


/**
 * WaterExpand操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Service
public class WaterExpandServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoWaterExpand,String,WaterExpandRepository> implements WaterExpandService {

    @Override
    public void findByPage(PageBean<DtoWaterExpand> pb, BaseCriteria waterExpandCriteria) {
        pb.setEntityName("DtoWaterExpand a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, waterExpandCriteria);
    }
}