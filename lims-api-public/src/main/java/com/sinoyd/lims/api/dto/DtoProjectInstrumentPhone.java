package com.sinoyd.lims.api.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 仪器出入库移动端实体
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Data
public class DtoProjectInstrumentPhone {

    /**
     * 仪器出入库记录id
     */
    private String id;

    /**
     * 仪器使用时间
     */
    private Date useDate;

    /**
     * 仪器入库数量
     */
    private Integer inCount;

    /**
     * 仪器出库数量
     */
    private Integer outCount;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 管理员id
     */
    private String administratorId;

    /**
     * 管理员名称
     */
    private String administratorName;

    /**
     * 使用人ids
     */
    private String userIds;

    /**
     * 使用人员名称
     */
    private String userNames;

    /**
     * 仪器出入库记录详情
     */
    private List<DtoProjectInstrumentDetailPhone> detailPhoneList;
}
