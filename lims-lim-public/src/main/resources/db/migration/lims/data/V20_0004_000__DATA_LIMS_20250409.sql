-- 订单管理模块中配置项目电子表单生成按钮
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('09ebaac0-44ed-4a3d-b075-0b9c43b09cb4', 'cb178e6c-345a-43cc-a923-57d0da5f04ce', 'OrderManageNew', '订单管理V2.0',
        'DetectionTask', '生成任务单（横版）', 1, 0, 1, '', '项目登记:电子表单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-09 16:03:49', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-09 16:03:49', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('a7cfe31f-97df-4af1-b632-8ee9365bf64f', '486f9342-7c83-4afe-a98c-f7d36ecdefcf', 'OrderManageNew', '订单管理V2.0',
        'DetectionTask', '生成任务单（竖版）', 1, 0, 1, '', '项目登记:电子表单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-09 16:03:14', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-09 16:03:14', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('a088b5e5-936e-45be-ae66-94bdc19588b4', 'f3bfd2b1-6357-4aab-8b99-9e6893f343ac', 'OrderManageNew', '订单管理V2.0',
        'CommissionSheetSampling', '委托单（采样）', 1, 0, 1, '', '项目登记:电子表单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-09 15:55:40', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-09 15:55:40', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('3f32e02f-b358-42ff-8f8b-5d37339bbe0f', '65e28d8e-f693-4f4a-820a-224d7fe-3431', 'OrderManageNew', '订单管理V2.0',
        'CommissionSheetSendSample', '委托单（送样）', 1, 0, 1, '', '项目登记:电子表单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-09 15:51:47', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-09 15:51:47', NULL);
