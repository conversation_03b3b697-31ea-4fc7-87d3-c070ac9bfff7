package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.persistence.Column;

/**
 * 查新结果导入导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Data
@AllArgsConstructor
public class DtoExportNewSearchResult {

    /**
     * 执行人
     */
    @Excel(name = "执行人", orderNum = "100", width = 20)
    private String executor;
    /**
     * 是否新标准
     */
    @Excel(name = "是否新标准", orderNum = "200", width = 20)
    private String isNewStandard;

    /**
     * 标准名称
     */
    @Excel(name = "标准名称", orderNum = "300", width = 20)
    private String standardName;

    /**
     * 标准编号
     */
    @Excel(name = "标准编号", orderNum = "400", width = 20)
    private String standardNum;

    /**
     * 年份
     */
    @Excel(name = "年份", orderNum = "500", width = 20)
    private String year;

    /**
     * 发布日期
     */
    @Excel(name = "发布日期", orderNum = "600", width = 20)
    private String releaseDate;

    /**
     * 实施日期
     */
    @Excel(name = "实施日期", orderNum = "700", width = 20)
    private String effectiveDate;

    /**
     * 查新日期
     */
    @Excel(name = "查新日期", orderNum = "800", width = 20)
    private String newSearchDate;

    /**
     * 替代标准号
     */
    @Excel(name = "替代标准号", orderNum = "900", width = 20)
    private String replaceNum;

    /**
     * 任务状态1:新建 2已提交 3已审核
     */
    @Excel(name = "任务状态",  orderNum = "1000", width = 20)
    private String status;

    /**
     * 是否确认
     */
    @Excel(name = "是否确认", orderNum = "1100", width = 20)
    private String isConfirm;

    /**
     * 确认状况
     */
    @Excel(name = "确认状况", orderNum = "1200", width = 20)
    private String confirmation;

    /**
     * 是否宣贯
     */
    @Excel(name = "要否宣贯",  orderNum = "1300", width = 20)
    private String isPropagate;


    /**
     * 宣贯情况
     */
    @Excel(name = "宣贯情况",  orderNum = "1400", width = 20)
    private String propagate;

    /**
     * 确认人
     */
    @Excel(name = "确认人",  orderNum = "1500", width = 20)
    private String confirmName;

    public DtoExportNewSearchResult() {

    }
}
