package com.sinoyd.base.strategy.htmlParse;

import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.List;

/**
 * Html解析基类
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/21
 * @since V100R001
 */
@Component
public abstract class AbstractHtmlParse {

    /**
     * html解析
     *
     * @param html html数据
     * @param c    需要解析的结果类
     * @return java对象数据集合
     */
    public abstract <T> List<T> parse(String html, Class<T> c);

    /**
     * 实例化对象
     *
     * @param c 目标类
     * @return 实例化对象
     */
    protected <T> T getInstance(Class<T> c) {
        T entity = null;

        try {
            entity = c.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException("无法实例化目标类: " + c.getName(), e);
        }
        return entity;
    }


    public static String getHtml() {
        try (BufferedReader reader =
                     new BufferedReader(
                             new FileReader("E:/javaproject/SpringCacheDemo/TestHtmlParse/src/main/resources/htmlParse/htmlDataSource2.html"))) {
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
            }
            return StringEscapeUtils.unescapeJava(builder.toString());
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("获取字符串报错");
        }
    }


}
