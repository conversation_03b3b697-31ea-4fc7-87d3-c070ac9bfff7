package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord2Sample;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord2Sample;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * EnvironmentalRecord2SampleRepository数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 20240305
 * @since V100R001
 */
public interface EnvironmentalRecord2SampleRepository extends IBaseJpaPhysicalDeleteRepository<DtoEnvironmentalRecord2Sample, String> {

    /**
     * 根据EnvironmentalRecordId 查询
     * @param EnvironmentalRecordIds 仪器使用记录标识
     * @return 集合
     */
    List<DtoEnvironmentalRecord2Sample> findAllByEnvironmentalRecordIdIn(List<String> EnvironmentalRecordIds);
}