package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.base.factory.QualityTaskFactory;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

/**
 * 质控样品数据结构
 * <AUTHOR>
 * @version V1.0.0 2020/1/15
 * @since V100R001
 */
@Data
public class DtoQCSample {
    /**
     * 样品id
     */
    private String id;

    /**
     * 样品编号
     */
    private String sampleCode = "";

    /**
     * 点位名称
     */
    private String redFolderName = "";

    /**
     * 送样单编号
     */
    private String recordCode = "";

    /**
     * 检测单编号
     */
    private String workSheetCode = "";

    /**
     * 样品性质
     */
    private String sampleProperty = "";

    /**
     * 样品类别
     */
    private Integer sampleCategory;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     *
     * @param id 样品id
     * @param sampleCode 样品编号
     * @param redFolderName 点位名称
     * @param recordCode 送样单编号
     * @param workSheetCode 检测单编号
     * @param sampleCategory 样品类别
     * @param qcGrade 质控等级
     * @param qcType 质控类型
     */
    public DtoQCSample(String id, String sampleCode, String redFolderName, String recordCode, String workSheetCode,Integer sampleCategory,Integer qcGrade,Integer qcType) {
        this.id = id;
        this.sampleCode = sampleCode;
        this.redFolderName = redFolderName;
        this.recordCode = recordCode;
        this.workSheetCode = workSheetCode;
        this.sampleCategory = sampleCategory;
        this.qcGrade = qcGrade;
        this.qcType = qcType;
        this.setSampleProperty(sampleCategory, qcGrade, qcType);
    }

    /**
     *
     * @param id 样品id
     * @param sampleCode 样品编号
     * @param redFolderName 点位名称
     * @param workSheetCode 检测单编号
     * @param sampleCategory 样品类别
     * @param qcGrade 质控等级
     * @param qcType 质控类型
     */
    public DtoQCSample(String id, String sampleCode, String redFolderName, String workSheetCode,Integer sampleCategory,Integer qcGrade,Integer qcType) {
        this.id = id;
        this.sampleCode = sampleCode;
        this.redFolderName = redFolderName;
        this.workSheetCode = workSheetCode;
        this.sampleCategory = sampleCategory;
        this.qcGrade = qcGrade;
        this.qcType = qcType;
        this.setSampleProperty(sampleCategory, qcGrade, qcType);
    }

    /**
     *
     * @param sampleCategory 样品类别
     * @param qcGrade 质控等级
     * @param qcType 质控类型
     */
    private void setSampleProperty(Integer sampleCategory,Integer qcGrade,Integer qcType){
        if (sampleCategory.equals(EnumPRO.EnumSampleCategory.原样.getValue())) {
            this.sampleProperty = EnumPRO.EnumSampleCategory.原样.toString();
        } else if (sampleCategory.equals(EnumPRO.EnumSampleCategory.质控样.getValue())) {
            this.sampleProperty = QualityTaskFactory.getInstance().getQcSample(qcType).getSampleProperty(qcGrade);
        }
    }
}
