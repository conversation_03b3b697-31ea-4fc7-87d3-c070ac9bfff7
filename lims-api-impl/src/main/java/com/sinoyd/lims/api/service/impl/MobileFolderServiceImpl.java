package com.sinoyd.lims.api.service.impl;

import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.api.service.MobileFolderService;
import com.sinoyd.lims.lim.criteria.FolderCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoDocAuthorityValidate;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;
import com.sinoyd.lims.lim.repository.lims.FolderRepository;
import com.sinoyd.lims.lim.service.DocAuthorityConfigService;
import com.sinoyd.lims.lim.service.FolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 移动端文件管理服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/10/30
 * @since V100R001
 */
@Service
public class MobileFolderServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFolder, String, FolderRepository> implements MobileFolderService {

    private DocumentService documentService;

    private FolderService folderService;

    private DocAuthorityConfigService docAuthorityConfigService;

    @Override
    public List<Object> findFileAndFolder(String folderId, String key) {
        List<Object> result = new ArrayList<>();
        //该文件夹下所有文件夹数据
        result.addAll(getFolderData(folderId, key));
        //该文件夹下所有文件数据
        result.addAll(getDocumentData(folderId, key));
        return result;
    }

    @Override
    @Transactional
    public void delete(List<String> ids) {
        folderService.logicDeleteById(ids);
        documentService.logicDeleteById(ids);
    }

    @Override
    public Boolean validateAuth(DtoDocAuthorityValidate dtoDocAuthorityValidate) {
        return docAuthorityConfigService.validateAuth(dtoDocAuthorityValidate);
    }

    /**
     * 更新文件备注
     *
     * @param map 参数
     * @return RestResponse<Void>
     */
    @Override
    @Transactional
    public void updateRemark(Map<String, Object> map) {
        List<String> ids = (List<String>) map.get("ids");
        String remark = (String) map.get("remark");
        List<DtoDocument> documentList = documentService.findAll(ids);
        for (DtoDocument document : documentList) {
            document.setRemark(remark);
        }
        documentService.update(documentList);
    }

    /**
     * 获取文件夹数据
     *
     * @param folderId 文件夹id
     * @param key      关键字
     * @return List<DtoFolder>
     */
    private List<DtoFolder> getFolderData(String folderId, String key) {
        PageBean<DtoFolder> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.setSort("orderNum-");
        FolderCriteria folderCriteria = new FolderCriteria();
        folderCriteria.setParentId(folderId);
        folderCriteria.setKey(key);
        folderService.findByPage(pb, folderCriteria);
        return pb.getData();
    }

    /**
     * 获取文件数据
     *
     * @param folderId 文件夹id
     * @param key      关键字
     * @return List<DtoDocument>
     */
    private List<DtoDocument> getDocumentData(String folderId, String key) {
        if (UUIDHelper.GUID_EMPTY.equals(folderId)) {
            return new ArrayList<>();
        }
        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        pageBean.setSort("fileName");
        DocumentCriteria documentCriteria = new DocumentCriteria();
        documentCriteria.setFolderId(folderId);
        documentCriteria.setIsFolder(true);
        documentCriteria.setFileName(key);
        documentService.findByPage(pageBean, documentCriteria);
        return pageBean.getData();
    }

    @Autowired
    @Lazy
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    @Lazy
    public void setFolderService(FolderService folderService) {
        this.folderService = folderService;
    }

    @Autowired
    @Lazy
    public void setDocAuthorityConfigService(DocAuthorityConfigService docAuthorityConfigService) {
        this.docAuthorityConfigService = docAuthorityConfigService;
    }
}
