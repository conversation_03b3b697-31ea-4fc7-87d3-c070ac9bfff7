package com.sinoyd.lims.lim.data.sync.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 数据同步传参实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/24
 */
@Data
public class DtoDataSyncParam {

    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 需要同步的数据id(来自公共库)
     */
    private List<String> dataIds;

    /**
     * 记录数
     */
    private Integer count;
}