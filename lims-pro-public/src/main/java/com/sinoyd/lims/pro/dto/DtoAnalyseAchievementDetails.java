package com.sinoyd.lims.pro.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.lims.pro.entity.AnalyseAchievementDetails;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * DtoAnalyseAchievementDetails实体
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_AnalyseAchievementDetails")
@Data
@DynamicInsert
public class DtoAnalyseAchievementDetails extends AnalyseAchievementDetails {

    private static final long serialVersionUID = 1L;

    @Transient
    @Excel(name = "检测类型",needMerge = true,orderNum = "80",width = 11)
    private String sampleTypeName;

    @Transient
    private String projectId;

    @Transient
    private Date requiredCompletedDate;

}
