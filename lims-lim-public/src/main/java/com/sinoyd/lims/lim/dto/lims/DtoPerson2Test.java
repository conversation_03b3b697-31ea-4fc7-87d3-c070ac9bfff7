package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.Person2Test;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoPerson2Test实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_Person2Test")
 @Data
 @DynamicInsert
 public  class DtoPerson2Test extends Person2Test {
    /*
     * 姓名
     */
    @Transient
    private String cName;

    /*
     * 人员科室id
     */
    @Transient
    private String domainId;

    public DtoPerson2Test(String personId,String testId,String typeId,String chineseName,Boolean isDefaultPerson){
        this.setPersonId(personId);
        this.setTestId(testId);
        this.setCName(chineseName);
        this.setIsDefaultPerson(isDefaultPerson);
        this.setSampleTypeId(typeId);
    }

    public DtoPerson2Test() {

    }
}