package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * ReceiveSampleRecordParamInfoCriteria待检样品查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023年9月20日
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReceiveSampleRecordParamInfoCriteria extends BaseCriteria implements Serializable {

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 样品类型id
     */
    private String sampleTypeId;


    @Override
    public String getCondition() {
        return null;
    }
}
