package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGather;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;

import java.util.Map;

/**
 * 仪器接入操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
public interface InstrumentGatherService extends IBaseJpaService<DtoInstrumentGather, String> {

    /**
     * 找到仪器接入的归档路径
     *
     * @param id 仪器接入id
     * @return 返回相应的归档路径
     */
    DtoInstrumentGather findInstrumentGatherAttachment(String id);

    /**
     * 仪器在线状态
     *
     * @return 在线状态
     */
    Map<String, Object> onlineStatus();
}