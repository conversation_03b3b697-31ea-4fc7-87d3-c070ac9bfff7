package com.sinoyd.base.service;


import com.sinoyd.base.dto.lims.DtoSystemConfig;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Map;

/**
 * 系统信息管理配置操作接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/12/8
 */
public interface SystemConfigService extends IBaseJpaService<DtoSystemConfig, String> {
    /**
     * 上传下载使用方法
     * @param id id 主键
     * @return DtoSystemConfig 系统信息管理配置DTO
     */
    DtoSystemConfig findAttachment(String id);

    /**
     * 查询表中的记录（有且只有一条）
     * @return DtoSystemConfig
     */
    DtoSystemConfig findSystemConfigOne();

    /**
     * 在线编辑方式
     * @return 配置内容
     */
    Map<String,Boolean> configMode();

    /**
     *  水印全局配置
     * @return 配置内容
     */
    String getWatermarkConfig();
}
