<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
    <groupId>com.sinoyd.lims.lim</groupId>
	<artifactId>lims-lim</artifactId>
	<version>fix-5.4.13-SNAPSHOT</version>
	<name>lims-lim</name>
    <description>LIMS LIM 模块</description>
    <packaging>pom</packaging>

    <build>
        <finalName>lims-lim</finalName>
    </build>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.15.RELEASE</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>

        <lim.version>fix-5.4.13</lim.version>
        <base.version>fix-5.4.13</base.version>
    </properties>

    <modules>
        <module>lims-lim-public</module>
        <module>lims-lim-arch</module>
        <module>lims-lim-impl</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sinoyd.lims.lim</groupId>
                <artifactId>lims-lim-public</artifactId>
                <version>${lim.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.lim</groupId>
                <artifactId>lims-lim-arch</artifactId>
                <version>${lim.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.lim</groupId>
                <artifactId>lims-lim-impl</artifactId>
                <version>${lim.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.base</groupId>
                <artifactId>sinoyd-base-impl</artifactId>
                <version>${base.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.9.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id><!--这里需要和settings.xml中配置的私有库名称一致-->
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>
</project>
