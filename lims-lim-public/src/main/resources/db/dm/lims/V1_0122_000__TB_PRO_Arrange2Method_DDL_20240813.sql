CREATE TABLE TB_PRO_ARRANGE2METHOD
(
    id               VARCHAR(50)                                                 NOT NULL,
    samplingPlanId   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    samplingMethodId VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    sampleTypeId     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    methodName       VARCHAR(255) DEFAULT '' NULL,
    alias            VARCHAR(100) DEFAULT '' NULL,
    standardCode     VARCHAR(100) DEFAULT '' NULL,
    orgId            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                                                 NOT NULL,
    creator          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                                                 NOT NULL,
    createDate       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                                                                 NOT NULL,
    domainId         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                                                 NOT NULL,
    modifier         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                                                                 NOT NULL,
    modifyDate       TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                                                                 NOT NULL
);
ALTER TABLE TB_PRO_ARRANGE2METHOD ADD CONSTRAINT  PRIMARY KEY(id) ;
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.id IS 'id';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.samplingPlanId IS '采样计划id';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.samplingMethodId IS '采样方法id';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.sampleTypeId IS '检测类型id';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.methodName IS '方法名称';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.alias IS '别名';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.standardCode IS '标准编号';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.orgId IS '组织机构id';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.creator IS '创建人';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.createDate IS '创建时间';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.domainId IS '所属实验室';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.modifier IS '修改人';
COMMENT ON COLUMN TB_PRO_ARRANGE2METHOD.modifyDate IS '修改时间';