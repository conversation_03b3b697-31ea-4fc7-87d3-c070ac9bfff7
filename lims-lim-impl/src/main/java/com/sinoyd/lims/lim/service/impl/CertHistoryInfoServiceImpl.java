package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoCertHistoryFile;
import com.sinoyd.lims.lim.dto.lims.DtoCertHistoryInfo;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.CertHistoryFileRepository;
import com.sinoyd.lims.lim.repository.lims.CertHistoryInfoRepository;
import com.sinoyd.lims.lim.service.CertHistoryInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CertHistoryInfoServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCertHistoryInfo,String, CertHistoryInfoRepository>
        implements CertHistoryInfoService {

    private CertHistoryFileRepository certHistoryFileRepository;

    private DocumentRepository documentRepository;

    @Override
    public Map<String, Object> queryHistoryInfo(String projectId) {
        Map<String, Object> rtMap = new HashMap<>();
        List<DtoCertHistoryInfo> list = repository.findByProjectId(projectId);
        Arrays.asList(EnumLIM.EnumCertHistoryInfoType.values()).forEach(infoType->{
            List<DtoCertHistoryInfo> infoList = list.stream().filter(t->infoType.getValue().equals(t.getInfoType()))
                    .sorted(Comparator.comparing(DtoCertHistoryInfo::getCreateDate)).collect(Collectors.toList());
            List<Object> detailList = new ArrayList<>();
            infoList.forEach(f->{
                if(StringUtil.isNotEmpty(f.getDetail())){
                    Object detail = JsonIterator.deserialize(f.getDetail(), infoType.getClazz());
                    detailList.add(detail);
                }
            });
            rtMap.put(infoType.name(),detailList);
        });
        return rtMap;
    }

    @Override
    public List<DtoDocument> queryHistoryFile(List<String> certHistoryFileIds) {
        List<DtoDocument> result = new ArrayList<>();
        if(StringUtil.isNotEmpty(certHistoryFileIds)){
            List<String> documentIds = certHistoryFileRepository.findAll(certHistoryFileIds).stream().map(DtoCertHistoryFile::getReferenceId)
                    .collect(Collectors.toList());
            result = documentRepository.findAll(documentIds);
        }
        return result;
    }

    @Autowired
    public void setCertHistoryFileRepository(CertHistoryFileRepository certHistoryFileRepository) {
        this.certHistoryFileRepository = certHistoryFileRepository;
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }
}
