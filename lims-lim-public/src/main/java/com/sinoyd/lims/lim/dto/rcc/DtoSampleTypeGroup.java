package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.SampleTypeGroup;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.List;


/**
 * DtoSampleTypeGroup实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_SampleTypeGroup")
@Data
@DynamicInsert
public class DtoSampleTypeGroup extends SampleTypeGroup {

    @Transient
    private List<String> testIds;

    @Transient
    private Boolean defaultLabelGroup;

    @Transient
    private String sampleItemNames;
}