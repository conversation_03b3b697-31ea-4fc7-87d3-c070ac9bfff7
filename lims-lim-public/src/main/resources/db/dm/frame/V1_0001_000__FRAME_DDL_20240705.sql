CREATE TABLE "casbin_rule"
(
    "p_type" VARCHAR(100),
    "v0"     VARCHAR(100),
    "v1"     VARCHAR(100),
    "v2"     VARCHAR(100),
    "v3"     VARCHAR(100),
    "v4"     VARCHAR(100),
    "v5"     VARCHAR(100)
) STORAGE(ON "MAIN", CLUSTERBTR);


CREATE TABLE "t_auth_user"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "loginID"        VARCHAR(50)                              NOT NULL,
    "password"       VARCHAR(50),
    "displayName"    VARCHAR(100),
    "empNum"         VARCHAR(50),
    "sexCode"        VARCHAR(2),
    "birthday"       TIMESTAMP(0),
    "email"          VARCHAR(100),
    "telephone"      VARCHAR(50),
    "picture"        VARCHAR(200),
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    "pwdUpdateDate"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "t_auth_user"."pwdUpdateDate" IS '密码修改时间';


CREATE TABLE "t_auth_user_expand"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "authId"         VARCHAR(50)                              NOT NULL,
    "loginType"      VARCHAR(50),
    "loginId"        VARCHAR(50),
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON COLUMN "t_auth_user_expand"."createDate" IS '创建时间';
COMMENT
ON COLUMN "t_auth_user_expand"."createUserGuid" IS '创建人Guid';
COMMENT
ON COLUMN "t_auth_user_expand"."createUserName" IS '创建人';
COMMENT
ON COLUMN "t_auth_user_expand"."deleted" IS '逻辑删除';
COMMENT
ON COLUMN "t_auth_user_expand"."deptGuid" IS '所属部门Guid';
COMMENT
ON COLUMN "t_auth_user_expand"."enabled" IS '启用';
COMMENT
ON COLUMN "t_auth_user_expand"."note" IS '备注';
COMMENT
ON COLUMN "t_auth_user_expand"."orgGuid" IS '所属机构Guid';
COMMENT
ON COLUMN "t_auth_user_expand"."sortNum" IS '排序（值越大显示靠前）';
COMMENT
ON COLUMN "t_auth_user_expand"."updateDate" IS '更新时间';
COMMENT
ON COLUMN "t_auth_user_expand"."updateUserGuid" IS '更新人Guid';
COMMENT
ON COLUMN "t_auth_user_expand"."updateUserName" IS '更新人';


CREATE TABLE "t_sys_area"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "areaCode"       VARCHAR(50),
    "areaName"       VARCHAR(100)                             NOT NULL,
    "parentGuid"     VARCHAR(50)  DEFAULT '0'                 NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_config"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "configName"     VARCHAR(100)                             NOT NULL,
    "configKey"      VARCHAR(100)                             NOT NULL,
    "configValue"    VARCHAR(2000),
    "enabled"        BIT                                      NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(1000),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_department"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "deptCode"       VARCHAR(50),
    "deptName"       VARCHAR(255)                             NOT NULL,
    "parentGuid"     VARCHAR(50)  DEFAULT '0'                 NOT NULL,
    "deptTypeCode"   VARCHAR(50),
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_dict_data_project"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "dictCode"       VARCHAR(100),
    "dictName"       VARCHAR(255)                             NOT NULL,
    "dictValue"      VARCHAR(100)                             NOT NULL,
    "dictType"       VARCHAR(100)                             NOT NULL,
    "parentGuid"     VARCHAR(50),
    "cssStyle"       VARCHAR(100),
    "cssClass"       VARCHAR(100),
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(1000),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    "extendS1"       VARCHAR(200),
    "extendS2"       VARCHAR(200),
    "extendS3"       VARCHAR(200),
    "extendS4"       VARCHAR(200),
    "extendI1"       INT,
    "extendI2"       INT,
    "extendI3"       INT,
    "extendI4"       INT,
    "extendF1"       NUMBER(22,0),
    "extendF2"       NUMBER(22,0),
    "extendF3"       NUMBER(22,0),
    "extendF4"       NUMBER(22,0),
    "extendD1"       TIMESTAMP(0),
    "extendD2"       TIMESTAMP(0),
    "extendD3"       TIMESTAMP(0),
    "extendD4"       TIMESTAMP(0),
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_dict_data_standard"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "dictCode"       VARCHAR(100),
    "dictName"       VARCHAR(255)                             NOT NULL,
    "dictValue"      VARCHAR(100)                             NOT NULL,
    "dictType"       VARCHAR(100)                             NOT NULL,
    "parentGuid"     VARCHAR(50)  DEFAULT '0'                 NOT NULL,
    "cssStyle"       VARCHAR(100),
    "cssClass"       VARCHAR(100),
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    "extendS1"       VARCHAR(200),
    "extendS2"       VARCHAR(200),
    "extendS3"       VARCHAR(200),
    "extendS4"       VARCHAR(200),
    "extendI1"       INT,
    "extendI2"       INT,
    "extendI3"       INT,
    "extendI4"       INT,
    "extendF1"       NUMBER(22,0),
    "extendF2"       NUMBER(22,0),
    "extendF3"       NUMBER(22,0),
    "extendF4"       NUMBER(22,0),
    "extendD1"       TIMESTAMP(0),
    "extendD2"       TIMESTAMP(0),
    "extendD3"       TIMESTAMP(0),
    "extendD4"       TIMESTAMP(0),
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_dict_type_project"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "dictName"       VARCHAR(255)                             NOT NULL,
    "dictType"       VARCHAR(100)                             NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_dict_type_standard"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "dictName"       VARCHAR(255)                             NOT NULL,
    "dictType"       VARCHAR(100)                             NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_invitecode"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "tstamp"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "orgName"        VARCHAR(255)                             NOT NULL,
    "expirationDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "maxUserNum"     INT          DEFAULT 0                   NOT NULL,
    "maxStorage"     INT          DEFAULT 0                   NOT NULL,
    "navGuid"        VARCHAR(50)                              NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_log"
(
    "id"              VARCHAR(50)                              NOT NULL,
    "tstamp"          TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "loginID"         VARCHAR(50),
    "ip"              VARCHAR(100),
    "mac"             VARCHAR(100),
    "operateType"     INT,
    "success"         BIT,
    "sysCode"         VARCHAR(100),
    "moduleName"      VARCHAR(255),
    "actionName"      VARCHAR(255),
    "uri"             VARCHAR(1000),
    "requestMethod"   VARCHAR(255),
    "requestContent"  CLOB,
    "responseContent" CLOB,
    "classMethod"     VARCHAR(1000),
    "costTimeMillis"  INT,
    "description"     VARCHAR(500),
    "enabled"         BIT          DEFAULT 0                   NOT NULL,
    "sortNum"         INT,
    "note"            VARCHAR(255),
    "createDate"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid"  VARCHAR(50),
    "createUserName"  VARCHAR(50),
    "updateDate"      TIMESTAMP(0),
    "updateUserGuid"  VARCHAR(50),
    "updateUserName"  VARCHAR(50),
    "orgGuid"         VARCHAR(50),
    "deptGuid"        VARCHAR(50),
    "deleted"         BIT          DEFAULT 0                   NOT NULL,
    NOT               CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_module"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "moduleCode"     VARCHAR(50),
    "moduleName"     VARCHAR(255)                             NOT NULL,
    "moduleUrl"      VARCHAR(500),
    "icon"           VARCHAR(500),
    "blank"          BIT          DEFAULT 0                   NOT NULL,
    "expanded"       BIT          DEFAULT 0                   NOT NULL,
    "parentGuid"     VARCHAR(50)  DEFAULT '0'                 NOT NULL,
    "webAppGuid"     VARCHAR(50),
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_module_action"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "actionName"     VARCHAR(255)                             NOT NULL,
    "actionCode"     VARCHAR(100)                             NOT NULL,
    "moduleGuid"     VARCHAR(50)                              NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_navigation"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "navName"        VARCHAR(255)                             NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_navigation_menu"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "navGuid"        VARCHAR(50)                              NOT NULL,
    "moduleGuid"     VARCHAR(50)                              NOT NULL,
    "parentGuid"     VARCHAR(50)  DEFAULT '0'                 NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_org"
(
    "id"             VARCHAR(50)             NOT NULL,
    "orgCode"        VARCHAR(50),
    "orgName"        VARCHAR(255)            NOT NULL,
    "orgTypeCode"    VARCHAR(50),
    "foundDate"      TIMESTAMP(0),
    "expirationDate" TIMESTAMP(0),
    "maxUserNum"     INT,
    "maxStorage"     INT,
    "usedStorage"    INT,
    "navGuid"        VARCHAR(50)             NOT NULL,
    "inviteCode"     VARCHAR(50),
    "creditCode"     VARCHAR(100),
    "artificialUser" VARCHAR(50),
    "telephone"      VARCHAR(50),
    "email"          VARCHAR(100),
    "fax"            VARCHAR(100),
    "businessScope"  VARCHAR(100),
    "webSite"        VARCHAR(200),
    "parentGuid"     VARCHAR(50) DEFAULT '0' NOT NULL,
    "areaCode"       VARCHAR(50),
    "address"        VARCHAR(1000),
    "picture"        VARCHAR(200),
    "logo"           VARCHAR(200),
    "enabled"        BIT         DEFAULT 0   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0),
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "deleted"        BIT         DEFAULT 0   NOT NULL,
    "extendS1"       VARCHAR(200),
    "extendS2"       VARCHAR(200),
    "extendS3"       VARCHAR(200),
    "extendS4"       VARCHAR(200),
    "extendI1"       INT,
    "extendI2"       INT,
    "extendI3"       INT,
    "extendI4"       INT,
    "extendF1"       NUMBER(22,0),
    "extendF2"       NUMBER(22,0),
    "extendF3"       NUMBER(22,0),
    "extendF4"       NUMBER(22,0),
    "extendD1"       TIMESTAMP(0),
    "extendD2"       TIMESTAMP(0),
    "extendD3"       TIMESTAMP(0),
    "extendD4"       TIMESTAMP(0),
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_resource"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "name"           VARCHAR(255)                             NOT NULL,
    "permission"     VARCHAR(200),
    "url"            VARCHAR(300),
    "requestMethod"  VARCHAR(200),
    "parentGuid"     VARCHAR(50)  DEFAULT '0'                 NOT NULL,
    "webAppGuid"     VARCHAR(50),
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_role"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "roleCode"       VARCHAR(50),
    "roleName"       VARCHAR(255)                             NOT NULL,
    "admin"          BIT          DEFAULT 0                   NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_role_permission"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "roleGuid"       VARCHAR(50)                              NOT NULL,
    "permissionType" VARCHAR(50)                              NOT NULL,
    "permissionGuid" VARCHAR(50)                              NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_user"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "loginID"        VARCHAR(50)                              NOT NULL,
    "password"       VARCHAR(50),
    "displayName"    VARCHAR(255),
    "empNum"         VARCHAR(50),
    "sexCode"        VARCHAR(2),
    "birthday"       TIMESTAMP(0),
    "email"          VARCHAR(100),
    "telephone"      VARCHAR(50),
    "userTypeCode"   VARCHAR(50),
    "homeUrl"        VARCHAR(200),
    "themeCode"      VARCHAR(50),
    "centerId"       VARCHAR(50),
    "picture"        VARCHAR(200),
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    "extendS1"       VARCHAR(200),
    "extendS2"       VARCHAR(200),
    "extendS3"       VARCHAR(200),
    "extendS4"       VARCHAR(200),
    "extendI1"       INT,
    "extendI2"       INT,
    "extendI3"       INT,
    "extendI4"       INT,
    "extendF1"       NUMBER(22,0),
    "extendF2"       NUMBER(22,0),
    "extendF3"       NUMBER(22,0),
    "extendF4"       NUMBER(22,0),
    "extendD1"       TIMESTAMP(0),
    "extendD2"       TIMESTAMP(0),
    "extendD3"       TIMESTAMP(0),
    "extendD4"       TIMESTAMP(0),
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_user_department"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "deptGuid"       VARCHAR(50),
    "userGuid"       VARCHAR(50)                              NOT NULL,
    "leader"         BIT          DEFAULT 0                   NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_user_role"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "roleGuid"       VARCHAR(50)                              NOT NULL,
    "userGuid"       VARCHAR(50)                              NOT NULL,
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "t_sys_webapp"
(
    "id"             VARCHAR(50)                              NOT NULL,
    "appCode"        VARCHAR(50)                              NOT NULL,
    "appName"        VARCHAR(255)                             NOT NULL,
    "secret"         VARCHAR(200)                             NOT NULL,
    "icon"           VARCHAR(200)                             NOT NULL,
    "homeSwitch"     BIT          DEFAULT 0                   NOT NULL,
    "basePath"       VARCHAR(300),
    "enabled"        BIT          DEFAULT 0                   NOT NULL,
    "sortNum"        INT,
    "note"           VARCHAR(255),
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "createUserGuid" VARCHAR(50),
    "createUserName" VARCHAR(50),
    "updateDate"     TIMESTAMP(0),
    "updateUserGuid" VARCHAR(50),
    "updateUserName" VARCHAR(50),
    "orgGuid"        VARCHAR(50),
    "deptGuid"       VARCHAR(50),
    "deleted"        BIT          DEFAULT 0                   NOT NULL,
    NOT              CLUSTER PRIMARY KEY("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

