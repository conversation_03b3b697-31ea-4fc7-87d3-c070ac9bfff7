package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.SpringContextAware;
import com.sinoyd.base.service.CalculateService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.MathUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseOriginalJson;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.AnalyseOriginalRecordRepository;
import com.sinoyd.lims.pro.repository.SolutionCalibrationRepository;
import com.sinoyd.lims.pro.repository.WorkSheetRepository;
import com.sinoyd.lims.pro.service.SolutionCalibrationRecordService;
import com.sinoyd.lims.pro.service.SolutionCalibrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 溶液标定
 *
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
@Service
@Slf4j
public class SolutionCalibrationServiceImpl extends BaseJpaServiceImpl<DtoSolutionCalibration, String, SolutionCalibrationRepository>
        implements SolutionCalibrationService {

    private SolutionCalibrationRecordService solutionCalibrationRecordService;

    private CalculateService calculateService;

    private WorkSheetRepository workSheetRepository;

    private AnalyseDataRepository analyseDataRepository;

    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;

    @Override
    @Transactional
    public DtoSolutionCalibration save(DtoSolutionCalibration entity) {
        if (!entity.getRecordList().isEmpty()) {
            List<DtoSolutionCalibrationRecord> recordList = entity.getRecordList();
            recordList.forEach(r -> r.setSolutionCalibrationId(entity.getId()));
            solutionCalibrationRecordService.save(recordList);
        }
        //保存公式参数数据
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderId(entity.getWorkSheetFolderId());
        if (StringUtil.isNotEmpty(analyseDataList)){
            List<String> analyseDataIds = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            List<DtoAnalyseOriginalRecord> originalRecords = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataIds);
            if (StringUtil.isNotEmpty(originalRecords)){
                for (DtoAnalyseOriginalRecord originalRecord : originalRecords) {
                    TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
                    };
                    List<DtoAnalyseOriginalJson> oldParamsTestFormula = JsonIterator.deserialize(originalRecord.getJson(), typeLiteral);
                    for (DtoAnalyseOriginalJson obj : oldParamsTestFormula) {
                        if (StringUtil.isNotEmpty(entity.getAverageConcentration())
                                &&("k".equalsIgnoreCase(obj.getAlias())||"标定浓度均值".equalsIgnoreCase(obj.getAlias()))){
                            obj.setDefaultValue(entity.getAverageConcentration());
                        }
                        if (StringUtil.isNotEmpty(entity.getBlankAvg())&&("空白".equalsIgnoreCase(obj.getAlias())||"空白平均滴定".equalsIgnoreCase(obj.getAlias()))){
                            obj.setDefaultValue(entity.getBlankAvg());
                        }
                        if (StringUtil.isNotEmpty(entity.getBlankOne())&&"空白滴定1".equalsIgnoreCase(obj.getAlias())){
                            obj.setDefaultValue(entity.getBlankOne());
                        }
                        if (StringUtil.isNotEmpty(entity.getBlankTwo())&&"空白滴定2".equalsIgnoreCase(obj.getAlias())){
                            obj.setDefaultValue(entity.getBlankTwo());
                        }
                    }
                    originalRecord.setJson(JsonStream.serialize(oldParamsTestFormula));
                }
                analyseOriginalRecordRepository.save(originalRecords);
            }
        }
        return super.save(entity);
    }

    @Override
    public DtoSolutionCalibration calculate(DtoSolutionCalibration entity) {
        validateEntity(entity.getTransferSolutionConcentration(), entity.getRecordList());
        List<DtoSolutionCalibrationRecord> recordList = entity.getRecordList();
        List<String> recordValueList = recordList.stream().map(DtoSolutionCalibrationRecord::getConcentration).collect(Collectors.toList());
        //标定液浓度 = 移取溶液浓度*溶液移取量/(V终-V始)
        String averageConcentration = com.sinoyd.common.utils.MathUtil.calculateAvg(recordValueList);
        int mostSignificance = StringUtil.isNotNull(entity.getMostSignificance()) ? entity.getMostSignificance() : -1;
        int mostDecimal = StringUtil.isNotNull(entity.getMostDecimal()) ? entity.getMostDecimal() : -1;
        averageConcentration = calculateService.revise(mostSignificance, mostDecimal, averageConcentration);
        entity.setAverageConcentration(averageConcentration);
        return entity;
    }

    @Override
    public DtoSolutionCalibrationRecord automaticCalculate(DtoSolutionCalibrationRecord record) {
        validateEntity(record.getTransferSolutionConcentration(), Collections.singletonList(record));
        BigDecimal transferSolutionConcentration = new BigDecimal(record.getTransferSolutionConcentration());
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        if (StringUtil.isNotEmpty(record.getTransferVolume()) && StringUtil.isNotEmpty(record.getVolumeStart()) && StringUtil.isNotEmpty(record.getVolumeEnd())) {
            BigDecimal transferVolume = new BigDecimal(record.getTransferVolume());
            BigDecimal volumeStart = new BigDecimal(record.getVolumeStart());
            BigDecimal volumeEnd = new BigDecimal(record.getVolumeEnd());
            BigDecimal blankAvg = StringUtil.isNotEmpty(record.getBlankAvg()) ? new BigDecimal(record.getBlankAvg()) : BigDecimal.ZERO;
            BigDecimal subValue = volumeEnd.subtract(volumeStart);
            record.setDiff(subValue.toPlainString());
            // 定义公式中的参数容器
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("移取溶液浓度", transferSolutionConcentration);
            paramMap.put("溶液移取量", transferVolume);
            paramMap.put("V终", volumeEnd);
            paramMap.put("V始", volumeStart);
            paramMap.put("V空白", blankAvg);
            String concentration = "s";
            // 根据公式计算
            concentration = calculationService.calculationExpression(record.getFormula(), paramMap).toString();
            //修约
            concentration = calculateService.revise(record.getMostSignificance(), record.getMostDecimal(), concentration);
            record.setConcentration(concentration);
        }
        return record;
    }

    @Override
    @Transactional
    public DtoSolutionCalibration findByWorkSheetFolderId(String workSheetFolderId) {
        DtoSolutionCalibration entity = repository.findByWorkSheetFolderId(workSheetFolderId);
        if (entity == null) {
            //相关信息支持默认上一检测单中保存的数据，避免重复填写 除了 标定浓度均值 V终 其余默认上一检测单数据
            String lastWorkSheetFolderId = queryLastWorkSheetFolderId(workSheetFolderId);
            if (StringUtil.isNotEmpty(lastWorkSheetFolderId)) {
                entity = repository.findByWorkSheetFolderId(lastWorkSheetFolderId);
                if (entity != null) {
                    //有老单子且有标定记录
                    DtoSolutionCalibration newEntity = new DtoSolutionCalibration();
                    BeanUtils.copyProperties(entity, newEntity, "id", "creator", "createDate", "modifier", "modifyDate", "orgId", "domainId");
                    newEntity.setWorkSheetFolderId(workSheetFolderId);
                    newEntity.setAverageConcentration(null);
                    List<DtoSolutionCalibrationRecord> recordList = solutionCalibrationRecordService.findBySolutionCalibrationId(entity.getId());
                    List<DtoSolutionCalibrationRecord> saveRecordList = new ArrayList<>();
                    for (DtoSolutionCalibrationRecord oldRecord : recordList) {
                        DtoSolutionCalibrationRecord newRecord = new DtoSolutionCalibrationRecord();
                        BeanUtils.copyProperties(oldRecord, newRecord, "id", "creator", "createDate", "modifier", "modifyDate", "orgId", "domainId");
                        newRecord.setSolutionCalibrationId(newEntity.getId());
                        newRecord.setVolumeEnd(null);
                        newRecord.setConcentration(null);
                        saveRecordList.add(newRecord);
                    }
                    solutionCalibrationRecordService.save(saveRecordList);
                    repository.save(newEntity);
                    newEntity.setRecordList(saveRecordList);
                    return newEntity;
                } else {
                    //有老单子但无标定记录
                    entity = new DtoSolutionCalibration();
                    entity.setMostDecimal(-1);
                    entity.setMostSignificance(-1);
                    entity.setCalibrationDate(new Date());
                }
            } else {
                //第一张单子
                entity = new DtoSolutionCalibration();
                entity.setMostDecimal(-1);
                entity.setMostSignificance(-1);
                entity.setCalibrationDate(new Date());
            }
        } else {
            List<DtoSolutionCalibrationRecord> recordList = solutionCalibrationRecordService.findBySolutionCalibrationId(entity.getId());
            for (DtoSolutionCalibrationRecord record : recordList) {
                if (StringUtil.isNotEmpty(record.getVolumeStart()) && StringUtil.isNotEmpty(record.getVolumeEnd())) {
                    BigDecimal volumeStart = new BigDecimal(record.getVolumeStart());
                    BigDecimal volumeEnd = new BigDecimal(record.getVolumeEnd());
                    BigDecimal subValue = volumeEnd.subtract(volumeStart);
                    record.setDiff(subValue.toPlainString());
                }
            }
            entity.setRecordList(recordList);
        }
        return entity;
    }

    /**
     * 查询最后一张工作单
     *
     * @return 工作单标识
     */
    private String queryLastWorkSheetFolderId(String workSheetFolderId) {
        // 查询当前工作单所做的测试项目
        List<DtoWorkSheet> workSheets = workSheetRepository.findByParentId(workSheetFolderId);
        List<String> testIds = workSheets.stream().map(DtoWorkSheet::getTestId).distinct().collect(Collectors.toList());
        // 查询所有已保存工作单，并且存在已保存标定溶液记录的，并且包含当前工作所做测试项目的最晚一条工作单数据
        PageBean<String> pb = new PageBean<>();
        pb.setRowsPerPage(1);
        pb.setEntityName("DtoWorkSheetFolder a");
        pb.setSelect("select a.id");
        StringBuilder condition = new StringBuilder(" and a.workStatus >= :workStatus and exists (select 1 from DtoSolutionCalibration s where a.id = s.workSheetFolderId)");
        condition.append(" and exists(select 1 from DtoWorkSheet w where w.parentId = a.id and w.testId in :testIds)");
        condition.append(" and a.isDeleted = 0 ");
        Map<String, Object> values = new HashMap<>();
        values.put("workStatus", EnumPRO.EnumWorkSheetStatus.已经保存.getValue());
        values.put("testIds", testIds);
        pb.setCondition(condition.toString());
        pb.setSort("createTime-");
        comRepository.findByPage(pb, values);
        List<String> workSheetFolderIdList = pb.getData();
        return workSheetFolderIdList.isEmpty() ? "" : workSheetFolderIdList.get(0);
    }

    /**
     * 校验数据
     *
     * @param transferSolutionConcentration 溶液标定
     */
    private void validateEntity(String transferSolutionConcentration, List<DtoSolutionCalibrationRecord> recordList) {
        if (StringUtil.isEmpty(transferSolutionConcentration)) {
            throw new BaseException("移取溶液浓度未填写！");
        } else {
            if (!MathUtil.isNumber(transferSolutionConcentration)) {
                throw new BaseException("移取溶液浓度不是数字！");
            }
            if (recordList.isEmpty()) {
                throw new BaseException("未填写标定记录！");
            } else {
                recordList.forEach(r -> {
                    if (StringUtil.isNotEmpty(r.getTransferVolume()) && !MathUtil.isNumber(r.getTransferVolume())) {
                        throw new BaseException(String.format("溶液移取量%s不是数字！", r.getTransferVolume()));
                    }
                    if (StringUtil.isNotEmpty(r.getVolumeStart()) && !MathUtil.isNumber(r.getVolumeStart())) {
                        throw new BaseException(String.format("V始%s不是数字！", r.getVolumeStart()));
                    }
                    if (StringUtil.isNotEmpty(r.getVolumeEnd()) && !MathUtil.isNumber(r.getVolumeEnd())) {
                        throw new BaseException(String.format("V终%s不是数字！", r.getVolumeEnd()));
                    }
                });
            }
        }
    }


    @Autowired
    @Lazy
    public void setSolutionCalibrationRecordService(SolutionCalibrationRecordService solutionCalibrationRecordService) {
        this.solutionCalibrationRecordService = solutionCalibrationRecordService;
    }

    @Autowired
    @Lazy
    public void setCalculateService(CalculateService calculateService) {
        this.calculateService = calculateService;
    }

    @Autowired
    public void setWorkSheetRepository(WorkSheetRepository workSheetRepository) {
        this.workSheetRepository = workSheetRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setAnalyseOriginalRecordRepository(AnalyseOriginalRecordRepository analyseOriginalRecordRepository) {
        this.analyseOriginalRecordRepository = analyseOriginalRecordRepository;
    }
}
