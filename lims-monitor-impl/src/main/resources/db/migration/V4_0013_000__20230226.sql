-- tb_lim_oaconsumablepicklistsdetail
alter table tb_lim_oaconsumablepicklistsdetail add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_lim_oaconsumablepicklistsdetail add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_lim_oaconsumablepicklistsdetail add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_lim_oaconsumablepicklistsdetail add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_lim_oaconsumablepicklistsdetail add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_lim_oaconsumablepicklistsdetail add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_lim_oaconsumablepicklistsdetail set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_oaconsumablepicklistsdetail set creator = '59141356591b48e18e139aa54d9dd351';
update tb_lim_oaconsumablepicklistsdetail set createDate = '2023-02-21 09:14:15';
update tb_lim_oaconsumablepicklistsdetail set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_oaconsumablepicklistsdetail set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_lim_oaconsumablepicklistsdetail set modifyDate = '2023-02-21 09:14:15';

-- tb_lim_projectinstrumentdetails
alter table tb_lim_projectinstrumentdetails add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_lim_projectinstrumentdetails add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_lim_projectinstrumentdetails add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_lim_projectinstrumentdetails add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_lim_projectinstrumentdetails add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_lim_projectinstrumentdetails add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_lim_projectinstrumentdetails set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_projectinstrumentdetails set creator = '59141356591b48e18e139aa54d9dd351';
update tb_lim_projectinstrumentdetails set createDate = '2023-02-21 09:14:15';
update tb_lim_projectinstrumentdetails set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_projectinstrumentdetails set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_lim_projectinstrumentdetails set modifyDate = '2023-02-21 09:14:15';


-- tb_lim_publishsystemversion
alter table tb_lim_publishsystemversion add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_lim_publishsystemversion add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_lim_publishsystemversion add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_lim_publishsystemversion add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_lim_publishsystemversion add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_lim_publishsystemversion add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_lim_publishsystemversion set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_publishsystemversion set creator = '59141356591b48e18e139aa54d9dd351';
update tb_lim_publishsystemversion set createDate = '2023-02-21 09:14:15';
update tb_lim_publishsystemversion set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_publishsystemversion set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_lim_publishsystemversion set modifyDate = '2023-02-21 09:14:15';

-- tb_lim_reportapply
alter table tb_lim_reportapply add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_lim_reportapply add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_lim_reportapply add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_lim_reportapply add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_lim_reportapply add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_lim_reportapply add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_lim_reportapply set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_reportapply set creator = '59141356591b48e18e139aa54d9dd351';
update tb_lim_reportapply set createDate = '2023-02-21 09:14:15';
update tb_lim_reportapply set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_reportapply set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_lim_reportapply set modifyDate = '2023-02-21 09:14:15';

-- tb_lim_sampletypegroup
alter table tb_lim_sampletypegroup add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_lim_sampletypegroup add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_lim_sampletypegroup add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_lim_sampletypegroup add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_lim_sampletypegroup add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_lim_sampletypegroup add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_lim_sampletypegroup set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_sampletypegroup set creator = '59141356591b48e18e139aa54d9dd351';
update tb_lim_sampletypegroup set createDate = '2023-02-21 09:14:15';
update tb_lim_sampletypegroup set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_lim_sampletypegroup set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_lim_sampletypegroup set modifyDate = '2023-02-21 09:14:15';

-- tb_monitor_fixedpointexpend
alter table tb_monitor_fixedpointexpend add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_monitor_fixedpointexpend add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_monitor_fixedpointexpend add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_monitor_fixedpointexpend add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_monitor_fixedpointexpend add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_monitor_fixedpointexpend add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_monitor_fixedpointexpend set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_monitor_fixedpointexpend set creator = '59141356591b48e18e139aa54d9dd351';
update tb_monitor_fixedpointexpend set createDate = '2023-02-21 09:14:15';
update tb_monitor_fixedpointexpend set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_monitor_fixedpointexpend set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_monitor_fixedpointexpend set modifyDate = '2023-02-21 09:14:15';

-- tb_monitor_fixedpointsortdetil
alter table tb_monitor_fixedpointsortdetil add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_monitor_fixedpointsortdetil add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_monitor_fixedpointsortdetil add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_monitor_fixedpointsortdetil add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_monitor_fixedpointsortdetil add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_monitor_fixedpointsortdetil add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_monitor_fixedpointsortdetil set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_monitor_fixedpointsortdetil set creator = '59141356591b48e18e139aa54d9dd351';
update tb_monitor_fixedpointsortdetil set createDate = '2023-02-21 09:14:15';
update tb_monitor_fixedpointsortdetil set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_monitor_fixedpointsortdetil set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_monitor_fixedpointsortdetil set modifyDate = '2023-02-21 09:14:15';

-- tb_monitor_oicinformation
alter table tb_monitor_oicinformation add column orgId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '组织机构id';
alter table tb_monitor_oicinformation add column creator VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '创建人';
alter table tb_monitor_oicinformation add column createDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '创建时间';
alter table tb_monitor_oicinformation add column domainId VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '所属实验室';
alter table tb_monitor_oicinformation add column modifier VARCHAR(50) NOT NULL default '00000000-0000-0000-0000-000000000000' comment '修改人';
alter table tb_monitor_oicinformation add column modifyDate datetime not NULL DEFAULT CURRENT_TIMESTAMP(0) comment '修改时间';

update tb_monitor_oicinformation set orgId = '5f7bcf90feb545968424b0a872863876';
update tb_monitor_oicinformation set creator = '59141356591b48e18e139aa54d9dd351';
update tb_monitor_oicinformation set createDate = '2023-02-21 09:14:15';
update tb_monitor_oicinformation set domainId = '5f7bcf90feb545968424b0a872863876';
update tb_monitor_oicinformation set modifier = '59141356591b48e18e139aa54d9dd351';
update tb_monitor_oicinformation set modifyDate = '2023-02-21 09:14:15';


