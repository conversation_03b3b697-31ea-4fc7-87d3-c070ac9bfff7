package com.sinoyd.lims.monitor.repository.lims;

import com.sinoyd.lims.monitor.dto.lims.DtoOicInformation2Test;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * OicInformation2Test数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface OicInformation2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoOicInformation2Test, String> {


    /**
     * 通过仪器表id查询集合
     * @param oicIds
     * @return 实体
     */
    List<DtoOicInformation2Test> findByOicIdIn(List<String> oicIds);

    /**
     * 通过仪器表id查询集合
     * @param oicIds
     * @return 实体
     */
    List<DtoOicInformation2Test> findByOicId(String oicIds);

}