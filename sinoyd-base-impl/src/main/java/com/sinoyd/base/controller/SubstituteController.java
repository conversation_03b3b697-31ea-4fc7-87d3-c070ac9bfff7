package com.sinoyd.base.controller;

import com.sinoyd.base.criteria.SubstituteCriteria;
import com.sinoyd.base.dto.rcc.DtoSubstitute;
import com.sinoyd.base.service.SubstituteService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * Substitute服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Api(tags = "示例: Substitute服务")
@RestController
@RequestMapping("api/base/substitute")
@Validated
public class SubstituteController extends BaseJpaController<DtoSubstitute, String, SubstituteService> {


    /**
     * 分页动态条件查询Substitute
     *
     * @param substituteCriteria 条件参数
     * @return RestResponse<List < Substitute>>
     */
    @ApiOperation(value = "分页动态条件查询Substitute", notes = "分页动态条件查询Substitute")
    @GetMapping
    public RestResponse<List<DtoSubstitute>> findByPage(SubstituteCriteria substituteCriteria) {
        PageBean<DtoSubstitute> pageBean = super.getPageBean();
        RestResponse<List<DtoSubstitute>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, substituteCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询Substitute
     *
     * @param id 主键id
     * @return RestResponse<DtoSubstitute>
     */
    @ApiOperation(value = "按主键查询Substitute", notes = "按主键查询Substitute")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoSubstitute> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoSubstitute> restResponse = new RestResponse<>();
        DtoSubstitute substitute = service.findOne(id);
        restResponse.setData(substitute);
        restResponse.setRestStatus(StringUtil.isNull(substitute) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增Substitute
     *
     * @param substitute 实体列表
     * @return RestResponse<DtoSubstitute>
     */
    @ApiOperation(value = "新增Substitute", notes = "新增Substitute")
    @PostMapping
    public RestResponse<DtoSubstitute> create(@Validated @RequestBody DtoSubstitute substitute) {
        RestResponse<DtoSubstitute> restResponse = new RestResponse<>();
        restResponse.setData(service.save(substitute));
        return restResponse;
    }

    /**
     * 新增Substitute
     *
     * @param substitute 实体列表
     * @return RestResponse<DtoSubstitute>
     */
    @ApiOperation(value = "修改Substitute", notes = "修改Substitute")
    @PutMapping
    public RestResponse<DtoSubstitute> update(@Validated @RequestBody DtoSubstitute substitute) {
        RestResponse<DtoSubstitute> restResponse = new RestResponse<>();
        restResponse.setData(service.update(substitute));
        return restResponse;
    }

    /**
     * "根据id批量删除Substitute
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Substitute", notes = "根据id批量删除Substitute")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}