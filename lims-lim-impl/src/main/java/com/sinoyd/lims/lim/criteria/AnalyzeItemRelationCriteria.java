package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分析项目关系查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-13
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeItemRelationCriteria extends BaseCriteria{

    /**
     * 类型（1：自检，2：上报）
     */
    private Integer type;

    /**
     * 关键字（分析项目名称、公式）
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if(StringUtils.isNotNullAndEmpty(key))
        {
            condition.append(" and (analyzeItemName like :key or formula like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if(type==-1)
        {
            condition.append(" and (type = 1 or type =2)");
        }
        else if(type > 0)
        {
            condition.append(" and (type = :type)");
            values.put("type", type);
        }
        return condition.toString();
    }
}