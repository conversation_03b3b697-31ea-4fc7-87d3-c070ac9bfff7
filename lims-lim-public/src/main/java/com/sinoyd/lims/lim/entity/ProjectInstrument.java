package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ProjectInstrument实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ProjectInstrument")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ProjectInstrument implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ProjectInstrument() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 项目id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目id")
    private String projectId;

    /**
     * 项目名称
     */
    @Column(length = 50)
    @ApiModelProperty("项目名称")
    private String projectName;


    /**
     * 使用日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("使用日期")
    private Date useDate;

    /**
     * 管理人员Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("管理人员Id")
    private String administratorId;

    /**
     * 管理人员名称
     */
    @Column(length = 50)
    @ApiModelProperty("管理人员名称")
    private String administratorName;

    /**
     * 使用人员id，多个用英文逗号间隔
     */
    @Column(length = 2000, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("使用人员id，多个用英文逗号间隔")
    @Length(message = "使用人员id{validation.message.length}", max = 2000)
    private String userIds;

    /**
     * 使用人员名称，多个用英文逗号间隔
     */
    @Column(length = 2000)
    @ApiModelProperty("使用人员名称，多个用英文逗号间隔")
    @Length(message = "使用人员名称{validation.message.length}", max = 2000)
    private String userNames;

    /**
     * 出库合格情况，0 - 不合格，1 - 合格
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("出库合格情况，0 - 不合格，1 - 合格")
    private Integer outQualified;

    /**
     * 入库合格情况，0 - 不合格，1 - 合格
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("入库合格情况，0 - 不合格，1 - 合格")
    private Integer intQualified;

    /**
     * 出库备注
     */
    @Column(length = 1000)
    @ApiModelProperty("出库备注")
    @Length(message = "出库备注{validation.message.length}", max = 1000)
    private String outRemarks;

    /**
     * 入库备注
     */
    @Column(length = 1000)
    @ApiModelProperty("入库备注")
    @Length(message = "入库备注{validation.message.length}", max = 1000)
    private String inRemarks;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室id")
    private String domainId;

    /**
     * 最近修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("最近修改人")
    private String modifier;

    /**
     * 最新修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @LastModifiedDate
    @ApiModelProperty("最新修改时间")
    private Date modifyDate;

}