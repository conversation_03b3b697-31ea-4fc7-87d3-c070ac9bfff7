package com.sinoyd.lims.lim.entity;


import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * 培训与参与人关联实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "Training2Participants")
@Data
@EntityListeners(AuditingEntityListener.class)
public class Training2Participants implements BaseEntity, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 培训id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("培训id")
    private String trainingId;

    /**
     * 参与人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("参与人id")
    private String participantsId;

    /**
     * 参与人姓名
     */
    @Column(length = 50)
    @ApiModelProperty("参与人姓名")
    @Length(message = "参与人姓名{validation.message.length}", max = 50)
    private String participantsName;
}
