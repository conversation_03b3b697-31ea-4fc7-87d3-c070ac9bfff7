package com.sinoyd.lims.pro.strategy.strategy.dataValidator;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 时间校验
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/09/23
 */
@Component(IFileNameConstant.DataValidateStrategyKey.TIME_VALIDATE)
public class TimeValidator extends AbsDataValidator {

    private static final String dateRegular = "([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])";

    @Override
    public Boolean validate(Object value, Map<String, Object> map) {
        String valStr = StringUtil.isNotNull(value) ? value.toString() : "";
        if (StringUtil.isEmpty(valStr)) {
            return true;
        }
        Matcher matcher = Pattern.compile(dateRegular).matcher(valStr);
        return matcher.find();
    }

    @Override
    public Integer getControlType() {
        return 9;
    }
}
