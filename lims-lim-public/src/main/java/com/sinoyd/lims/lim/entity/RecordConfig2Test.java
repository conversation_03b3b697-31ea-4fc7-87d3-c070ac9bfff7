package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * RecordConfig2Test实体
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="RecordConfig2Test")
 @Data
 public  class RecordConfig2Test implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 记录单配置id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("记录单配置id")
	private String recordConfigId;
    
    /**
    * 测试项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试项目id")
    private String testId;
    
 }