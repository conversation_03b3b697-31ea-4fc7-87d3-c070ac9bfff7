package com.sinoyd.lims.monitor.controller;

import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.monitor.criteria.FixedpointCriteria;
import com.sinoyd.lims.monitor.service.ExportFixedPointHJService;
import com.sinoyd.lims.monitor.service.ExportFixedPointWRService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 监测计划导出服务接口
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@RestController
@RequestMapping("/api/monitor/export")
public class ExportFixedPointController extends ExceptionHandlerController<ExportFixedPointWRService> {

    @Autowired
    private ExportFixedPointHJService exportFixedPointHJService;

    /**
     * 人员检测能力导出
     */
    @GetMapping("/fixedPointWR")
    public void exportFixedPointWR(FixedpointCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put(LimConstants.ImportConstants.FIRST_SHEET_NAME, "污染源点位数据");
        sheetNames.put(LimConstants.ImportConstants.SECOND_SHEET_NAME, "点位类型");
        service.export(criteria, response, sheetNames, "污染源点位");
    }

    /**
     * 人员检测能力导出
     */
    @GetMapping("/fixedPointHJ")
    public void exportFixedPointHJ(FixedpointCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put(LimConstants.ImportConstants.FIRST_SHEET_NAME, "环境质量点位数据");
        sheetNames.put(LimConstants.ImportConstants.SECOND_SHEET_NAME, "点位类型");
        exportFixedPointHJService.export(criteria, response, sheetNames, "环境质量点位");
    }

}
