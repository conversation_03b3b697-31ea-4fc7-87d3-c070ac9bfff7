package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 仪器检定校准导入实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/13
 * @since V100R001
 */
@Data
public class DtoImportInstrumentCheckRecord extends DtoImportInstrumentCheckRecordNoCode implements IExcelModel, IExcelDataModel {

    /**
     * 仪器名称
     */
    @Excel(name = "仪器本站编号(多个编号以逗号隔开)【必填】",orderNum = "100",width = 38)
    private String instrumentCode;
}
