package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * AnalyzeItemSortDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="AnalyzeItemSortDetail")
 @Data
 public  class AnalyzeItemSortDetail implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  AnalyzeItemSortDetail() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 分析项目排序Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("分析项目排序Id")
	private String sortId;
    
    /**
    * 分析项目Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("分析项目Id")
	private String analyzeItemId;
    
    /**
    * 分析项目名称
    */
    @Column(length=50)
    @ApiModelProperty("分析项目名称")
	private String analyzeItemName;
    
    /**
    * 排序值
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }