package com.sinoyd.lims.monitor.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.OicInformationService;
import com.sinoyd.lims.monitor.criteria.OicInformationCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoOicInformation;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * OicInformation服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: OicInformation服务")
 @RestController
 @RequestMapping("api/monitor/oicInformation")
 public class OicInformationController extends BaseJpaController<DtoOicInformation, String,OicInformationService> {


    /**
     * 分页动态条件查询OicInformation
     *
     * @param oicInformationCriteria 条件参数
     * @return RestResponse<List < OicInformation>>
     */
    @ApiOperation(value = "分页动态条件查询OicInformation", notes = "分页动态条件查询OicInformation")
    @GetMapping
    public RestResponse<List<DtoOicInformation>> findByPage(OicInformationCriteria oicInformationCriteria) {
        PageBean<DtoOicInformation> pageBean = super.getPageBean();
        RestResponse<List<DtoOicInformation>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, oicInformationCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询OicInformation
     *
     * @param id 主键id
     * @return RestResponse<DtoOicInformation>
     */
    @ApiOperation(value = "按主键查询OicInformation", notes = "按主键查询OicInformation")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoOicInformation> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOicInformation> restResponse = new RestResponse<>();
        DtoOicInformation oicInformation = service.findOne(id);
        restResponse.setData(oicInformation);
        restResponse.setRestStatus(StringUtil.isNull(oicInformation) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增OicInformation
     *
     * @param oicInformation 实体列表
     * @return RestResponse<DtoOicInformation>
     */
    @ApiOperation(value = "新增OicInformation", notes = "新增OicInformation")
    @PostMapping
    public RestResponse<DtoOicInformation> create(@RequestBody @Validated DtoOicInformation oicInformation) {
        RestResponse<DtoOicInformation> restResponse = new RestResponse<>();
        restResponse.setData(service.save(oicInformation));
        return restResponse;
    }

    /**
     * 新增OicInformation
     *
     * @param oicInformation 实体列表
     * @return RestResponse<DtoOicInformation>
     */
    @ApiOperation(value = "修改OicInformation", notes = "修改OicInformation")
    @PutMapping
    public RestResponse<DtoOicInformation> update(@RequestBody @Validated DtoOicInformation oicInformation) {
        RestResponse<DtoOicInformation> restResponse = new RestResponse<>();
        restResponse.setData(service.update(oicInformation));
        return restResponse;
    }

    /**
     * "根据id批量删除OicInformation
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除OicInformation", notes = "根据id批量删除OicInformation")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}