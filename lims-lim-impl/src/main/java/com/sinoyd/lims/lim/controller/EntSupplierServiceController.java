package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.EntSupplierServiceCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoEntSupplierService;
import com.sinoyd.lims.lim.service.EntSupplierServiceService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商关于-商品管理 
 * <AUTHOR> 修改：xuxb
 * @version V1.0.0 2019/3/8
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/entSupplierService")
@Validated
public class EntSupplierServiceController
        extends BaseJpaController<DtoEntSupplierService, String, EntSupplierServiceService> {

    /**
     * 根据id获取商品
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取商品", notes = "根据id获取商品")
    @GetMapping("/{id}")
    public RestResponse<DtoEntSupplierService> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoEntSupplierService> restResp = new RestResponse<>();
        DtoEntSupplierService entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询商品
     * 
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询商品", notes = "分页动态条件查询商品")
    @GetMapping
    public RestResponse<List<DtoEntSupplierService>> findByPage(EntSupplierServiceCriteria criteria) {

        RestResponse<List<DtoEntSupplierService>> restResp = new RestResponse<>();

        PageBean<DtoEntSupplierService> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增商品
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增商品", notes = "新增商品")
    @PostMapping
    public RestResponse<DtoEntSupplierService> save(@Validated @RequestBody DtoEntSupplierService entity) {

        RestResponse<DtoEntSupplierService> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoEntSupplierService data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改商品
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改商品", notes = "修改商品")
    @PutMapping
    public RestResponse<DtoEntSupplierService> update(@Validated @RequestBody DtoEntSupplierService entity) {

        RestResponse<DtoEntSupplierService> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoEntSupplierService data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 刪除商品
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "刪除商品", notes = "刪除商品")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除商品
     * 
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除商品", notes = "批量删除商品")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}