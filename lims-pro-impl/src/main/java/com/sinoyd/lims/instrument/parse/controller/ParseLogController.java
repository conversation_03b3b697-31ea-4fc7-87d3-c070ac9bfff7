package com.sinoyd.lims.instrument.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.instrument.parse.criteria.ParseLogCriteria;
import com.sinoyd.lims.instrument.parse.dto.DtoParseLog;
import com.sinoyd.lims.instrument.parse.service.ParseLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 仪器解析日志用户接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/9/6
 */
@Api(tags = "仪器解析日志用户接口")
@RestController
@RequestMapping("api/pro/parseLog")
public class ParseLogController extends BaseJpaController<DtoParseLog, String, ParseLogService> {
    /**
     * 分页动态条件查询仪器解析日志
     *
     * @param criteria 条件参数
     * @return RestResponse<List < DtoParseLog>>
     */
    @ApiOperation(value = "分页动态条件查询仪器解析日志", notes = "分页动态条件查询仪器解析日志")
    @GetMapping
    public RestResponse<List<DtoParseLog>> findByPage(ParseLogCriteria criteria) {
        PageBean<DtoParseLog> pageBean = super.getPageBean();
        RestResponse<List<DtoParseLog>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 下载方案附件
     *
     * @param logId    日志id
     * @param response 响应流
     * @return 返回数据
     */
    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/download/{logId}")
    public RestResponse<String> fileDownload(@PathVariable String logId, HttpServletResponse response) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.download(logId, response));
        return restResp;
    }


}