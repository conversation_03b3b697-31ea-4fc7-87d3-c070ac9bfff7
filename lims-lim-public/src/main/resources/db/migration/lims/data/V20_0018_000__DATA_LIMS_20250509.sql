-- 内审实施计划表单配置
delete  from tb_lim_reportconfig where id = '1a5c8076-8d3b-477f-ab23-abaa86d1c50c';
INSERT INTO tb_lim_reportconfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum, versionNumLocation,
                                controlNumLocation)
VALUES ('1a5c8076-8d3b-477f-ab23-abaa86d1c50c', 1, 'InternalAuditPlanSheet', '内审实施计划.xlsx',
        'LIMReportForms/内审实施计划.xlsx', 'output/LIMReportForms/内审实施计划.xlsx', 'application/excel',
        'com.sinoyd.lims.report.service.limReportForms.InternalAuditPlanSheetService', '', '', 0, 1, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-04-29 09:35:30',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-04-29 09:35:30', '',
        'LIMReportForms', 'InternalAuditPlanSheet', b'0', '', NULL, '', '', '', 0, NULL, '中上', '左上');

delete  from tb_lim_reportapply where id = '98f46b3b-bc45-433d-b212-b2f101595d3d';
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('98f46b3b-bc45-433d-b212-b2f101595d3d', '1a5c8076-8d3b-477f-ab23-abaa86d1c50c', 'PlanInternalWrite', '编制内审计划V2',
        'InternalAuditPlanSheet', '生成', 0, 0, 1, '', '电子表单', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-29 09:39:34', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-04-29 09:39:34', NULL);
