package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * FolderPeriodWWInfo实体
 * <AUTHOR>
 * @version V1.0.0 2021/7/7
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="FolderPeriodWWInfo")
@Data
public class FolderPeriodWWInfo implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 点位
     */
    @Column(length = 50)
    @ApiModelProperty("点位")
    private String folderId;

    /**
     * 周期
     */
    @Column(nullable = false)
    @ApiModelProperty("周期")
    private Integer periodCount;

    /**
     * 下发时间
     */
    @Column(nullable = false)
    @ApiModelProperty("下发时间")
    private Date issueTime;

    /**
     * ww任务id
     */
    @Column(length = 50)
    @ApiModelProperty("ww任务id")
    private String taskId;

    /**
     * ww采集单号
     */
    @Column(length = 50)
    @ApiModelProperty("ww采集单号")
    private String collectNo;
}
