package com.sinoyd.lims.api.service;

import com.sinoyd.lims.api.dto.vo.ProjectVO;
import com.sinoyd.lims.api.dto.vo.WWFolderVO;
import com.sinoyd.lims.api.dto.vo.WWProjectVO;
import com.sinoyd.lims.instrument.parse.dto.DtoTcpData;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;

import java.util.List;

public interface MobileWWService {

    /**
     * 返回任务下发信息
     *
     * @param projectVO 条件
     * @return 下发信息
     */
    WWProjectVO findByProAndFolder(ProjectVO projectVO);

    /**
     * 保存任务下发和点位关系
     *
     * @param projectVO 条件
     */
    void saveFolderPeriodWWInfo(ProjectVO projectVO);

    /**
     * 根据下发任务查询仪器解析内容
     *
     * @param projectVO 条件
     * @return 查询内容
     */
    List<WWFolderVO> findWWInfo(ProjectVO projectVO);

    /**
     * 根据样品id获取样品分组信息
     *
     * @param sampleId 样品id
     * @return 分组信息
     */
    List<DtoSampleGroup> findBySampleId(String sampleId);

    /**
     * 同步解析内容
     *
     * @param projectVO 条件
     */
    void syncParamsInfo(ProjectVO projectVO);
}
