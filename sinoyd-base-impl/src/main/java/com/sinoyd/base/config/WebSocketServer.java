package com.sinoyd.base.config;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.security.Principal;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * websockets服务端
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/23
 */
@ServerEndpoint(value = "/api/websockets")
@Component
@Slf4j
public class WebSocketServer {

    private static final CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<>();

    private Session session;

    private String sessionId;

    private static final ConcurrentHashMap<String, Session> sessions = new ConcurrentHashMap<>();

    /**
     * 连接建立成功回调方法
     *
     * @param session websockets会话
     */
    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        webSocketSet.add(this);
        this.sessionId = session.getId();
        sessions.put(sessionId, this.session);
        this.sendMessage("WS连接成功...");
    }

    /**
     * 连接关闭回调方法
     */
    @OnClose
    public void onClose() {
        webSocketSet.remove(this);
        if (StringUtil.isNotEmpty(this.sessionId)) {
            sessions.remove(this.sessionId);
        }
    }

    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String queryType) {
        this.sendMessage("accept: " + queryType);
    }

    /**
     * @param session websockets会话
     * @param error   错误对象
     */
    @OnError
    public void onError(Session session, Throwable error) {
        this.sendMessage("发生错误，请联系技术支持方...");
        log.error(error.getMessage(), error);
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) {
        try {
            //遍历客户端
            for (WebSocketServer webSocket : webSocketSet) {
                webSocket.session.getBasicRemote().sendText(message);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 实现服务器根据用户ID推送
     *
     * @param userId  用户ID
     * @param message 信息内容
     * @return 是否推送成功
     */
    public boolean sendMessage(String userId, String message) {
        boolean flag = false;
        try {
            if (StringUtil.isNotEmpty(userId)) {
                for (Session session : sessions.values()) {
                    String sessionUserId = getUserId(session);
                    if (userId.equals(sessionUserId)) {
                        session.getBasicRemote().sendText(message);
                        flag = true;
                    }
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return flag;
    }

    /**
     * 取出所有会话
     *
     * @return 会话Map
     */
    public static ConcurrentHashMap<String, Session> getSessions() {
        return sessions;
    }

    /**
     * 根据session获取用户id
     *
     * @param session session
     * @return 用户id
     */
    private static String getUserId(Session session) {
        String userId = "";
        if (StringUtil.isNotNull(session.getUserPrincipal())) {
            // 根据会话中的用户信息匹配待发送的userId
            Principal principal = session.getUserPrincipal();
            UsernamePasswordAuthenticationToken authenticationToken = (UsernamePasswordAuthenticationToken) principal;
            CurrentPrincipalUser currentPrincipalUser = (CurrentPrincipalUser) authenticationToken.getPrincipal();
            userId = currentPrincipalUser.getUserId();
        }
        return userId;
    }

}