package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.rcc.DtoWorkdayConfig;
import com.sinoyd.lims.lim.service.WorkdayConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作日管理配置接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023-01-18
 */
@Api(tags = "工作日管理配置接口服务")
@RestController
@RequestMapping("/api/lim/workdayConfig")
public class WorkdayConfigController extends BaseJpaController<DtoWorkdayConfig, String, WorkdayConfigService> {

    /**
     * 查询工作日管理配置
     *
     * @param year 年份
     * @return 工作日管理配置List
     */
    @ApiOperation(value = "查询工作日管理配置", notes = "查询工作日管理配置")
    @GetMapping("/year/{year}")
    public RestResponse<DtoWorkdayConfig> find(@PathVariable("year") Integer year) {
        RestResponse<DtoWorkdayConfig> restResp = new RestResponse<>();
        restResp.setData(service.findByYear(year));
        return restResp;
    }


}



  