ALTER TABLE TB_PRO_Report
    ADD COLUMN depriveStatus int(0) NOT NULL DEFAULT 0 COMMENT '扣发状态(0:未扣发 1:已扣发)';

CREATE TABLE TB_PRO_ReportDeprive
(
    id            varchar(50) NOT NULL COMMENT '主键id',
    projectId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '项目id',
    reportIds     varchar(1000) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报告id',
    deprivePerson varchar(50) NULL DEFAULT NULL COMMENT '扣发人',
    depriveTime   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '扣发日期',
    depriveReason varchar(255) NULL DEFAULT NULL COMMENT '扣发理由',
    orgId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate    datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate    datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);