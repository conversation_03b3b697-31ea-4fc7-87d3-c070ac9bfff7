package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.criteria.PerformanceStatisticForSampleDataCriteria;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForSampleData;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSamplingPersonConfig;
import com.sinoyd.lims.pro.repository.PerformanceStatisticForSampleDataRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.repository.SamplingPersonConfigRepository;
import com.sinoyd.lims.pro.service.PerformanceStatisticForSampleDataService;
import com.sinoyd.frame.base.util.BaseCriteria;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * PerformanceStatisticForSampleData操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
 @Service
public class PerformanceStatisticForSampleDataServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPerformanceStatisticForSampleData,String,PerformanceStatisticForSampleDataRepository> implements PerformanceStatisticForSampleDataService {

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private SamplingPersonConfigRepository samplingPersonConfigRepository;

    @Async
    @Override
    public void createSampleStatistic(DtoReceiveSampleRecord record,CurrentPrincipalUser principalContextUser) {
        List<DtoReceiveSampleRecord> records = new ArrayList<>();
        records.add(record);
        createSampleStatistic(records,principalContextUser);
    }
    @Async
    @Override
    public void createSampleStatistic(List<DtoReceiveSampleRecord> records,CurrentPrincipalUser principalContextUser) {
        //重新赋值登录的账号信息
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(principalContextUser, null, authorities));
        //送样单的ids
        List<String> receiveIds = records.stream().map(DtoReceiveSampleRecord::getId).distinct().collect(Collectors.toList());
        if (receiveIds.size() > 0) {
            //送样单相关的采样人员
            List<DtoSamplingPersonConfig> samplingPersonConfigs = samplingPersonConfigRepository.findByObjectIdIn(receiveIds);
            List<Object[]> list = sampleRepository.countSampleNumGroupByReceiveIdAndSampleTypeId(receiveIds);
            List<DtoPerformanceStatisticForSampleData> performanceStatisticForSampleDataList = new ArrayList<>();
            for (DtoReceiveSampleRecord receiveSampleRecord : records) {
                //.当前送样单相关的采样人员排除当前的登记人
                List<String> samplingPersonIds = samplingPersonConfigs.stream().filter(p -> p.getObjectId().equals(receiveSampleRecord.getId()))
                        .map(DtoSamplingPersonConfig::getSamplingPersonId).distinct().collect(Collectors.toList());
                for (String samplingPersonId : samplingPersonIds) {
                    String receiveId = receiveSampleRecord.getId();
                    String projectId = receiveSampleRecord.getProjectId();
                    String recordCode = receiveSampleRecord.getRecordCode();
                    String senderName = receiveSampleRecord.getSenderName();
                    Date samplingTime = receiveSampleRecord.getSamplingTime();
                    Date sendTime = receiveSampleRecord.getSendTime();
                    //检测类型的ids
                    List<String> sampleTypeIds = list.stream().filter(p -> p[1].equals(receiveId)).map(p -> (String) p[2]).collect(Collectors.toList());

                    for (String sampleTypeId : sampleTypeIds) {
                        Object[] object = list.stream().filter(p -> p[1].equals(receiveId) && p[2].equals(sampleTypeId)).findFirst().orElse(null);
                        DtoPerformanceStatisticForSampleData performanceStatisticForSampleData = new DtoPerformanceStatisticForSampleData();
                        Integer sampleNum = 0;//样品数量
                        if (StringUtil.isNotNull(object)) {
                            sampleNum = ((Long) object[0]).intValue();
                        }
                        performanceStatisticForSampleData.setReceiveId(receiveId);
                        performanceStatisticForSampleData.setProjectId(projectId);
                        performanceStatisticForSampleData.setRecordCode(recordCode);
                        performanceStatisticForSampleData.setSamplingPersonId(samplingPersonId);
                        performanceStatisticForSampleData.setSendTime(sendTime);
                        performanceStatisticForSampleData.setSamplingTime(samplingTime);
                        performanceStatisticForSampleData.setSampleTypeId(sampleTypeId);
                        performanceStatisticForSampleData.setSenderName(senderName);
                        performanceStatisticForSampleData.setSample(sampleNum);
                        performanceStatisticForSampleData.setOrgId(receiveSampleRecord.getOrgId());
                        performanceStatisticForSampleDataList.add(performanceStatisticForSampleData);
                    }
                }
            }
            repository.deleteByReceiveIdIn(receiveIds);
            if (performanceStatisticForSampleDataList.size() > 0) {
                repository.save(performanceStatisticForSampleDataList);
            }
        }
    }


    @Override
    public void findByPage(PageBean<DtoPerformanceStatisticForSampleData> pb, BaseCriteria baseCriteria) {
        PerformanceStatisticForSampleDataCriteria performanceStatisticForSampleDataCriteria = (PerformanceStatisticForSampleDataCriteria) baseCriteria;
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForSampleData(");
        stringBuilder.append("a.id,a.receiveId,a.samplingPersonId,a.recordCode,a.sendTime,a.sampleTypeId,d.typeName,");
        stringBuilder.append("a.sample,a.projectId,b.projectCode,b.projectName,b.projectTypeId,");
        stringBuilder.append("c.name,b.customerId,b.customerName,a.senderName");
        stringBuilder.append(") ");
        pb.setSelect(stringBuilder.toString());
        pb.setEntityName("DtoPerformanceStatisticForSampleData a,DtoProject b,DtoProjectType c,DtoSampleType d");
        pb.setSort(performanceStatisticForSampleDataCriteria.getSort());
        pb.setCondition(performanceStatisticForSampleDataCriteria.getCondition());
        comRepository.findByPage(pb, performanceStatisticForSampleDataCriteria.getValues());
    }

    /**
     * 获取总计行
     * @param baseCriteria
     * @return
     */
    @Override
    public  DtoPerformanceStatisticForSampleData findSumPerformanceStatistic(BaseCriteria baseCriteria) {
        PerformanceStatisticForSampleDataCriteria performanceStatisticForSampleDataCriteria = (PerformanceStatisticForSampleDataCriteria) baseCriteria;
        PageBean<Object[]> pb = new PageBean<>();
        pb.setSelect("select new sum(p.sample) ");
        pb.setEntityName("DtoPerformanceStatisticForSampleData p");
        pb.setCondition(performanceStatisticForSampleDataCriteria.getCondition());
        List<Object[]> list = comRepository.find(pb.getAutoQuery(), performanceStatisticForSampleDataCriteria.getValues());
        if (list.size() > 0) {
            Object[] objArray = list.get(0);
            return new DtoPerformanceStatisticForSampleData(Integer.valueOf(String.valueOf(objArray[0])));
        }
        return new DtoPerformanceStatisticForSampleData();
    }
}