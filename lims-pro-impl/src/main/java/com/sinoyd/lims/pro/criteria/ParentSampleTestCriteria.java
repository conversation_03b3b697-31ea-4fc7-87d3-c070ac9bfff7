package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 父类样品指标查询条件
 * <AUTHOR>
 * @version V1.0.0 2020年2月13日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParentSampleTestCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 样品id
     */
    private List<String> sampleIds;

    /**
     * 是否现场
     */
    private Boolean isCompleteField;

    /**
     * 是否平行
     */
    private Boolean isQCP;

    /**
     * 关键字
     */
    private String key;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and t.isDeleted = 0  and a.isDeleted = 0 ");
        condition.append(" and a.testId = t.id");
        if(StringUtil.isNotEmpty(sampleId)){
            condition.append(" and a.sampleId = :sampleId");
            values.put("sampleId", this.sampleId);
        }
        if(StringUtil.isNotEmpty(sampleIds)){
            condition.append(" and a.sampleId in :sampleIds");
            values.put("sampleIds", this.sampleIds);
        }
        //是否现场
        if (StringUtil.isNotNull(this.isCompleteField)) {
            condition.append(" and t.isCompleteField = :isCompleteField ");
            values.put("isCompleteField", this.isCompleteField);
        }
        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and ( t.redAnalyzeMethodName like :key or t.redCountryStandard like :key or t.redAnalyzeItemName like :key or t.fullPinYin like :key or t.pinYin like :key or t.testCode like :key)");
            values.put("key", "%" + this.key + "%");
        }
        //是否平行
        if (StringUtil.isNotNull(this.isQCP)) {
            condition.append(" and t.isQCP = :isQCP");
            values.put("isQCP", this.isQCP);
        }
        return condition.toString();
    }
}
