package com.sinoyd.lims.pro.service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRangeResult;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelation;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;

/**
 * AnalyseData操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface AnalyseDataService extends IBaseJpaService<DtoAnalyseData, String> {

    /**
     * 分包
     *
     * @param analyseDataList 分析数据
     */
    void subAnalyseData(List<DtoAnalyseData> analyseDataList);

    /**
     * 更新数据有效位、小数位
     *
     * @param objectId 工作单、送样单Id
     */
    void uploadDataByTest(String objectId);

    /**
     * 批量分包
     *
     * @param sampleIdList     样品id列表
     * @param subTestIdList    分包的测试项目id
     * @param notSubTestIdList 不分包的测试项目id
     */
    void batchSubAnalyseData(List<String> sampleIdList, List<String> subTestIdList,
                             List<String> notSubTestIdList, List<String> outSamplingTestIdList);

    /**
     * 查询样品分包情况
     *
     * @param sampleIdList 分析数据
     * @return 样品分析数据列表
     */
    Map<String, List<DtoTest>> getSampleSubInfo(List<String> sampleIdList);

    /**
     * 根据点位返回数据
     *
     * @param sampleFolderIds 点位id集合
     */
    List<DtoAnalyseData> findBySampleFolderIds(List<String> sampleFolderIds);

    /**
     * 获取待检测样品
     *
     * @param criteria 查询条件
     * @return 返回数据
     */
    List<Map<String, Object>> findAnalystWorkSheetSampleList(BaseCriteria criteria);

    /**
     * 根据项目id返回数据
     *
     * @param projectId 项目id
     */
    List<DtoAnalyseData> findByProjectId(String projectId);

    //#region数据添加

    /**
     * 获取待添加的分析数据
     *
     * @param dtoAnalyseDataAdd 分析数据添加实体
     * @return 待添加的分析数据
     */
    List<DtoAnalyseData> getAddAnalyseData(DtoAnalyseDataAdd dtoAnalyseDataAdd);

    /**
     * 获取待添加的分析数据
     *
     * @param dtoAnalyseDataAdd 分析数据添加实体
     * @param record            送样单信息
     * @return 待添加的分析数据
     */
    List<DtoAnalyseData> getAddAnalyseDataByOrderScheme(DtoAnalyseDataAdd dtoAnalyseDataAdd, DtoReceiveSampleRecord record, List<DtoDimension> dimensionList);

    /**
     * 添加分析数据
     *
     * @param dtoAnalyseDataAdd 分析数据添加实体
     * @return 变动的样品id
     */
    List<String> addAnalyseData(DtoAnalyseDataAdd dtoAnalyseDataAdd);

    /**
     * 当前工作单创建人是否为有证人员
     *
     * @param workSheetFolderId 工作单id
     * @return 结果
     */
    Boolean certifiedPersonByTestIds(String workSheetFolderId);

    /**
     * 比对评价计算
     *
     * @param recordId 送样单
     */
    void comparisonEvaluate(String recordId, Boolean isReport);

    /**
     * 添加质控分析数据
     *
     * @param qmAdd 分析数据添加实体
     */
    void addQMAnalyseData(DtoAnalyseDataQMAdd qmAdd);

    /**
     * 设置默认样品有效期
     *
     * @param anaData 分析数据
     */
    void defaultDateValue(DtoAnalyseData anaData);

    //#endregion

    //#region数据删除

    /**
     * 删除数据
     *
     * @param dtoAnalyseDataDelete 删除结构体
     */
    void deleteAnalyseData(DtoAnalyseDataDelete dtoAnalyseDataDelete);

    /**
     * @param ids 分析数据id
     */
    void deleteQMAnalyseData(List<String> ids);

    //#endregion

    /**
     * 修改频次方案的方法
     *
     * @param samples      样品
     * @param testList     修改后的测试项目
     * @param sampleTypeId 修改的检测类型id
     */
    void changeAnalyseMethod(List<DtoSample> samples, List<DtoTest> testList, String sampleTypeId);

    /**
     * 修改分析人
     *
     * @param ids         数据id
     * @param analystId   分析人
     * @param analystName 分析姓名
     */
    void changeAnalysePerson(List<String> ids, String analystId, String analystName);

    /**
     * 根据频次数据进行分包
     *
     * @param samples 样品
     */
    void subAnalyseData(List<DtoSample> samples, List<String> subItemIds, List<String> samOutIds, List<String> notSubItemIds);

    /**
     * 根据样品id返回数据进度
     *
     * @param sampleId 样品id
     * @return 样品数据进度
     */
    List<DtoAnalyseDataInquiry> findInquiry(String sampleId);

    /**
     * 保存现场数据
     *
     * @param subId                   领样单id
     * @param analyseDataPropertyList 数据信息
     */
    Map<String, Object> saveLocalData(String subId, String sortId, List<DtoAnalyseDataProperty> analyseDataPropertyList);

    /**
     * 检测单数据保存
     *
     * @param dtoWorkSheetSave 检测单数据保存
     */
    void saveAnalyseData(DtoWorkSheetSave dtoWorkSheetSave);

    /**
     * 计算的数据对象
     *
     * @param dtoWorkSheetProperty 子检测单数据
     * @return 返回相应的数据
     */
    List<Map<String, Object>> analyticalFormula(DtoWorkSheetProperty dtoWorkSheetProperty);

    /**
     * 计算的数据对象
     *
     * @param dtoAnalyseDataCalculation 计算的对象
     * @param rangeResult               平行样的范围值
     * @return 返回相应的数据
     */
    List<Map<String, Object>> analyticalFormula(DtoAnalyseDataCalculation dtoAnalyseDataCalculation, DtoTestQCRangeResult rangeResult);

    /**
     * 计算相应的检测结果
     *
     * @param dtoAnalyseDataCalculation 计算的对象
     * @param rangeResult               平行样的范围值
     * @return 返回相应的数据
     */
    List<Map<String, Object>> calculateAnalyticalResult(DtoAnalyseDataCalculation dtoAnalyseDataCalculation, DtoTestQCRangeResult rangeResult);

    /**
     * 刷新分析数据的参数
     *
     * @param anaIds 分析数据id
     * @return 将刷新后的数据返回
     */
    List<Map<String, Object>> refreshAnalyseDataParams(List<String> anaIds);

    /**
     * 获取计算的map对象
     *
     * @param formula       公式
     * @param analyseData   数据集合
     * @param paramsConfigs 公式参数配置
     * @return 返回计算的map对象
     */
    Map<String, Object> getCalculationMap(String formula, Map<String, Object> analyseData, List<DtoTestFormulaParamsConfig> paramsConfigs);

    /**
     * 分析项目关系提醒
     *
     * @param dtoAnalyseDataTemp 分析数据
     * @return 返回提醒信息
     */
    String analyseDataRelationRemind(DtoAnalyseDataTemp dtoAnalyseDataTemp);

    /**
     * @param analyzeItemId
     * @param analyzeItemName
     * @param analyseDatas
     * @param testValue
     * @param testValueDstr
     * @param limitValue
     * @param isItemMsg
     * @param sampleCode
     * @param relationList
     * @param itemRelationParamsList
     * @return 返回提醒信息
     */
    String relationRemind(String analyzeItemId, String analyzeItemName, List<DtoAnalyseData> analyseDatas,
                          String testValue, String testValueDstr, String limitValue, Boolean isItemMsg, String sampleCode,
                          List<DtoItemRelation> relationList, List<DtoItemRelationParams> itemRelationParamsList);

    /**
     * 获取相应的复核人员
     *
     * @param personId       当前人员id
     * @param workSheetTests 对象(主要是 传测试项目id及对应的检测类型id)
     * @return 返回复核人员
     */
    List<DtoKeyValue> getCheckPerson(String personId, List<DtoWorkSheetTest> workSheetTests);


    /**
     * 获取相应的审核人员
     *
     * @param personId  审核人员id
     * @param analystId 分析人员id
     * @param testIds   测试项目id
     * @return 返回想要的审核人员
     */
    List<DtoKeyValue> getAuditorPerson(String personId, String analystId, List<String> testIds);

    /**
     * 从检测单中剔除数据
     *
     * @param ids                  数据ids
     * @param workSheetFolderId    检测单id
     * @param isDelWorkSheetFolder 如果全部剔除是否要删除检测单
     */
    List<DtoAnalyseData> removeDataFromWorkSheet(List<String> ids, String workSheetFolderId, Boolean isDelWorkSheetFolder, Boolean isDeleteRelatedSample);

    /**
     * 从现场领养单剔除数据
     *
     * @param ids 数据ids
     */
    void removeDataFromSubRecord(List<String> ids, String subId);

    /**
     * 从检测单中剔除样品
     *
     * @param sampleIds         样品ids
     * @param workSheetFolderId 检测单id
     */
    List<String> removeSampleFromWorkSheet(List<String> sampleIds, String workSheetFolderId);


    /**
     * 删除检测单下的数据
     *
     * @param analyseDataList 分析数据
     * @param isRefreshCache  是否刷新缓存
     */
    void deleteWorkSheetAnalyseData(List<DtoAnalyseData> analyseDataList, Boolean isRefreshCache);

    /**
     * 表头参数计算
     *
     * @param calculateData  需要计算的参数
     * @param paramsDataList 表头参数
     * @return 所有表头参数
     */
    List<DtoParamsData> paramsCalculate(List<DtoParamsData> calculateData, List<DtoParamsData> paramsDataList);


    /**
     * 提交分析数据
     *
     * @param dtoWorkSheetFolder         检测单信息
     * @param isCheckInstrumentUseRecord 核对是否填写了仪器记录
     * @param workSheetProperties        提交的数据信息
     * @param analyzeTime                分析时间
     */
    void submitAnalyseData(DtoWorkSheetFolder dtoWorkSheetFolder,
                           Boolean isCheckInstrumentUseRecord,
                           List<DtoAnalyseDataProperty> workSheetProperties,
                           Date analyzeTime);


    /**
     * 退换分析数据
     *
     * @param ids     数据ids
     * @param opinion 意见
     */
    void backData(List<String> ids, String opinion);

    /**
     * 退回分析数据所对应的检测单
     *
     * @param ids     数据ids
     * @param opinion 意见
     */
    void backWorkSheetFolderForData(List<String> ids, String opinion);

    /**
     * 判断退回的数据中是否关联报告
     *
     * @param backAnalyseData 需要退回的数据
     * @param isSingleBack    是否为数据详细页面退回
     */
    void judgeIsBackAnaData(Collection<DtoAnalyseData> backAnalyseData, Boolean isSingleBack);

    /**
     * 退回分析数据所对应的送样单
     *
     * @param analyseDataList 分析数据对象列表
     * @param opinion         意见
     */
    void backReceiveSampleRecordForData(List<DtoAnalyseData> analyseDataList, String opinion);

    /**
     * 更新最新一次检测单数据提交时间
     *
     * @param id 检测单id
     */
    void updateLastNewSubmitTime(String id);

    /**
     * 根据人员查询待检样品检测类型(小类)
     *
     * @param personId
     * @return
     */
    List<DtoSampleType> findSampleTypeList(String personId);

    /**
     * 根据审核人员查询检测类型
     *
     * @param personId
     * @return
     */
    List<DtoSampleType> findCheckSampleTypeList(String personId);

    /**
     * 通过样品id获取数据
     *
     * @param sampleId 样品id
     * @return 数据集合
     */
    List<DtoAnalyseData> getAnalyseDataListBySampleId(String sampleId);

    /**
     * 获取仪器解析数据
     *
     * @param workSheetFolderId 工作单id
     * @param parseDataIds      仪器解析数据id列表
     * @return 仪器解析数据
     */
    List<Map<String, Object>> syncInstrumentParseData(String workSheetFolderId, List<String> parseDataIds, String sampleId);

    /**
     * 修改有效位、小数位(产品暂时用不到)
     *
     * @param dtoAnalyseData 数据对象
     */
    void modifyDecimalDigit(DtoAnalyseData dtoAnalyseData);

    /**
     * 判断现场平行是否要进行原样计算
     *
     * @return 是否进行平行计算
     */
    Boolean outParallelCalcAsSampleSwitch();

    /**
     * 提交分析数据时判断质控样合格情况
     *
     * @return 不合格提示
     */
    String checkQcPass(String worksheetFolderId);

    /**
     * 提交时判断工作单是否存在记录单
     *
     * @param worksheetFolderId 工作单id
     * @return 提示内容
     */
    String checkWorkSheetForm(String worksheetFolderId);

    /**
     * 检测结果修约
     *
     * @param dtoTest            测试项目
     * @param value              检测结果值
     * @param mostSignificance   有效位数
     * @param mostDecimal        小数位数
     * @param examLimitValue     检出限
     * @param examLimitValueLess 检出限显示
     * @return 检测结果修约值
     */
    String calculateTestValue(DtoTest dtoTest, String value, Integer mostSignificance, Integer mostDecimal,
                              String examLimitValue, String examLimitValueLess);

    /**
     * 检测结果修约
     *
     * @param dtoTest            测试项目
     * @param sampleTypeId       样品类型
     * @param value              检测结果值
     * @param examLimitValue     检出限
     * @param examLimitValueLess 检出限显示
     * @return 检测结果修约值
     */
    String calculateTestValue(DtoTest dtoTest, String sampleTypeId, String value,
                              String examLimitValue, String examLimitValueLess);

    /**
     * 检测结果修约
     *
     * @param dtoTest            测试项目
     * @param value              检测结果值
     * @param mostSignificance   有效位数
     * @param mostDecimal        小数位数
     * @param examLimitValue     检出限
     * @param examLimitValueLess 检出限显示
     * @param examLimitValueLess 是否科学计数法
     * @return 检测结果修约值
     */
    String calculateTestValue(DtoTest dtoTest, String value, Integer mostSignificance, Integer mostDecimal,
                              String examLimitValue, String examLimitValueLess, Boolean isSci);

    /**
     * 获取小于检出限结果
     *
     * @param test         测试项目
     * @param sampleTypeId 样品类型id
     * @return 小于检出限结果
     */
    String getExamLimitValueLess(DtoTest test, String sampleTypeId);

    /**
     * 计算标样偏差
     *
     * @param value   检测结果值
     * @param qcValue 质控值
     * @return 标样偏差
     */
    String calculateBzQcInfo(BigDecimal value, String qcValue);

    /**
     * 平行计算需要检出限一半计算
     *
     * @param value          数据
     * @param examLimitValue 检出限
     * @return 检出限一半
     */
    String halfLimit(String value, String examLimitValue);

    /**
     * 平行计算需要检出限一半计算
     *
     * @param value          数据
     * @param examLimitValue 检出限
     * @param type           类型
     * @return 检出限一半
     */
    String halfLimit(String value, String examLimitValue, String type);

    /**
     * 设置样品分析数据的分析时间为采样时间
     *
     * @param samples      样品列表
     * @param samplingTime 采样时间
     */
    void setAnalyzeTimeForSample(List<DtoSample> samples, Date samplingTime);

    /**
     * 查询分析项目关系数据
     *
     * @param id 分析数据id
     * @return 分析数据
     */
    List<DtoAnalyseData> relation(String id);

    /**
     * 获取配置信息
     *
     * @param type 类型
     * @return 配置信息
     */
    String getConfigValue(String type);

    /**
     * 根据样品ids获取数据集合
     *
     * @param sampleIds 样品ids
     * @return 返回数据集合
     */
    List<DtoAnalyseData> findDataBySampleIds(Collection<String> sampleIds);

    /**
     * 根据工作单id获取数据集合
     *
     * @param workSheetFolderId 工作单id
     * @return 数据集合
     */
    List<DtoAnalyseData> findByWorkSheetFolderId(String workSheetFolderId);

    /**
     * 更新领样时间
     *
     * @param dtoAnalyseDataUpdate 分析数据更新实体
     */
    void updateSampleReceiveDate(DtoAnalyseDataUpdate dtoAnalyseDataUpdate);

    /**
     * @param orignValue     检测结果
     * @param value          修约值
     * @param examLimitValue 检出限
     * @param test           测试项目
     * @return 检出限一半
     */
    String halfLimitTestOrignValue(String orignValue, String value, String examLimitValue, DtoTest test);

    /**
     * 计算现场平行样出证结果是否显示 均值
     *
     * @return true 均值，false 原始值
     */
    boolean outParallelSwitch();

    /**
     * 是否历史数据
     *
     * @return true 判断，false 不判断
     */
    boolean isHistoryValue();

    /**
     * 实验室分析-获取历史数据
     *
     * @param analyseDataHistoryVo 查询数据vo
     * @return 历史数据集合
     */
    List<DtoAnalyseDataHistory> getHistoryData(DtoAnalyseDataHistoryVo analyseDataHistoryVo);

    /**
     * 把原样和平行样的检测结果修约值收集到一个列表中
     *
     * @param testOriginValue  数据结果
     * @param inTestValue      偏差内容
     * @param mostSignificance 测试项目
     * @param mostDecimal      返回值
     * @return 样和平行样的检测结果修约值列表
     */
    List<String> collectYyPxVal(String testOriginValue, List<String> inTestValue, int mostSignificance, int mostDecimal);

    /**
     * 修约判断
     *
     * @param rateStr          质控信息
     * @param type             类型
     * @param mostSignificance 小数位
     * @param mostDecimal      有效位
     * @param t
     * @param <T>
     * @return
     */
    <T extends QualityControlKind> String checkAbsoluteDeviation(String rateStr, int type, Integer mostSignificance, Integer mostDecimal, T t);


    /**
     * 异常数据处理
     */
    void exceptionalData();

    /**
     * 平行样、原样 检测结果修约值是否都大于测定下限
     *
     * @param valueList 检测结果修约值
     * @param valueDown 测定下限
     * @return 判定结果
     */
    boolean outParallelQuality(List<String> valueList, String valueDown);

    /**
     * 根据嗅辨样品结果更新分析数据结果
     *
     * @param sampleCode       样品编号
     * @param odourConsistence 臭气浓度
     */
    void updateByOdsSample(String sampleCode, String odourConsistence);

    /**
     * 判断是否数据存在检测单中
     *
     * @return 判断是否数据存在检测单中
     */
    String checkWorkSheetFolder(Map<String, Object>  params);
}