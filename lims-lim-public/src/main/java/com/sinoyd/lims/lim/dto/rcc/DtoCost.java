package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.Cost;

import java.math.BigDecimal;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoCost实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_Cost")
 @Data
 @DynamicInsert
 public  class DtoCost extends Cost {
   private static final long serialVersionUID = 1L;

    /**
     * 大类的采样费
     */
    @Transient
    private BigDecimal bigSamplingCost;

     /**
     * 大类的分析费
     */
    @Transient
    private BigDecimal bigAnalyzeCost;

    @Transient
    private Integer cert;
 }