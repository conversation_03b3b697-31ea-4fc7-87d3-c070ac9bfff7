package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.TestService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 测试项目接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-13
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/test")
@Validated
public class TestController extends BaseJpaController<DtoTest, String, TestService> {

    /**
     * 根据id查询测试项目
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询测试项目", notes = "根据id查询测试项目")
    @GetMapping("/{id}")
    public RestResponse<DtoTest> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoTest> restResp = new RestResponse<>();

        DtoTest entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 据id查询测试项目(多个)
     *
     * @param testIds 测试项目id集合
     * @return 测试项目集合
     */
    @ApiOperation(value = "根据id查询测试项目(多个)", notes = "据id查询测试项目(多个)")
    @PostMapping("/multiple")
    public RestResponse<List<DtoTest>> findByIds(@RequestBody List<String> testIds) {
        RestResponse<List<DtoTest>> restResp = new RestResponse<>();
        restResp.setData(service.findAll(testIds));
        return restResp;
    }

    /**
     * 查询全部测试项目
     *
     * @return 测试项目集合
     */
    @ApiOperation(value = "查询全部测试项目", notes = "查询全部测试项目")
    @PostMapping("/all")
    public RestResponse<List<DtoTest>> findAll() {
        RestResponse<List<DtoTest>> restResp = new RestResponse<>();
        restResp.setData(service.findAll());
        return restResp;
    }

    /**
     * 分页动态条件查询测试项目
     *
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询测试项目", notes = "分页动态条件查询测试项目")
    @GetMapping
    public RestResponse<List<DtoTest>> findByPage(TestCriteria criteria) {

        RestResponse<List<DtoTest>> restResp = new RestResponse<>();

        PageBean<DtoTest> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 分页查询测试项目并根据参数排除测试项目，
     * 由于get请求长度限制无法携带过多测试项
     * 目id所以使用post方法
     *
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询测试项目并根据参数排除测试项目", notes = "分页动态条件查询测试项目并根据参数排除测试项目")
    @PostMapping("/exclude")
    public RestResponse<List<DtoTest>> findByPageExcludeSelectedTests(@RequestBody TestCriteria criteria) {

        RestResponse<List<DtoTest>> restResp = new RestResponse<>();

        PageBean<DtoTest> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    @ApiOperation(value = "分页动态条件查询测试项目", notes = "分页动态条件查询测试项目")
    @GetMapping("/analyzeItem")
    public RestResponse<List<DtoAnalyzeItem>> findAnalyzeItemBySampleTypeId(@RequestParam("sampleTypeId") String sampleTypeId) {
        RestResponse<List<DtoAnalyzeItem>> restResp = new RestResponse<>();
        List<DtoAnalyzeItem> list = service.findAnalyzeItemBySampleTypeId(sampleTypeId);
        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(list);
        restResp.setCount(list.size());
        return restResp;
    }

    /**
     * 新增测试项目
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增测试项目", notes = "新增测试项目")
    @PostMapping
    public RestResponse<DtoTest> save(@Validated @RequestBody DtoTest entity) {

        RestResponse<DtoTest> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoTest data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改测试项目
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改测试项目", notes = "修改测试项目")
    @PutMapping
    public RestResponse<DtoTest> update(@Validated @RequestBody DtoTest entity) {

        RestResponse<DtoTest> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoTest data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 排序值自增
     *
     * @param ids 测试项目id
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "排序值自增", notes = "排序值自增")
    @PutMapping("/increment")
    public RestResponse<Boolean> incrementOrderNum(@RequestBody List<String> ids) {

        RestResponse<Boolean> restResp = new RestResponse<>();
        service.incrementOrderNum(ids);
        restResp.setData(true);

        return restResp;
    }

    /**
     * 根据id删除测试项目
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id删除测试项目", notes = "根据id删除测试项目")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除分析方法
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除分析方法", notes = "批量删除分析方法")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 新增子测试项目
     *
     * @param test 根据parentId 父测试项目id和testList  子测试项目
     * @return 新增的记录数
     */
    @PostMapping("/addSonTests/save")
    public Object addSonTests(@Validated @RequestBody DtoTest test) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.addSonTests(test.getId(), test.getTestIds());
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 删除子测试项目
     *
     * @param test 根据parentId 父测试项目id和testList  子测试项目
     * @return 删除的记录数
     */
    @PostMapping("/deleteSonTests/delete")
    public Object deleteSonTests(@RequestBody DtoTest test) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.deleteSonTests(test.getId(), test.getTestIds());
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 获取子测试项目列表
     *
     * @param id 父测试项目的id
     * @return 返回子测试项目列表
     */
    @GetMapping("/getSonTestList/{id}")
    public RestResponse<Set<DtoTest>> getSonTestList(@PathVariable(name = "id") String id) {

        RestResponse<Set<DtoTest>> restResp = new RestResponse<>();

        Set<DtoTest> list = service.getSonTestList(id);
        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD
                : ERestStatus.SUCCESS);
        restResp.setData(list);
        restResp.setCount(list.size());

        return restResp;
    }

    /**
     * 检出限类型查询
     *
     * @return 检出限类型
     */
    @GetMapping("/examLimitType")
    public RestResponse<List<Object>> getExamLimitType() {
        RestResponse<List<Object>> restResp = new RestResponse<>();
        List<Object> list = service.examLimitType();
        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(list);
        restResp.setCount(list.size());
        return restResp;
    }

    /**
     * 迁移导出
     *
     * @param response 响应流
     */
    @GetMapping("/migrationExport")
    public RestResponse<String> migrationExport(TestCriteria criteria,HttpServletResponse response){
        RestResponse<String> restResponse = new RestResponse<>();
        service.migrationExport(criteria, response);
        return restResponse;
    }

    /**
     * 重置验证
     *
     * @param ids 测试项目ids
     */
    @PostMapping("/resetValidate")
    public RestResponse<String> resetValidate(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.resetValidate(ids);
        return restResp;
    }

    /**
     * 批量设置测试项目
     *
     * @param test 数据载体
     * @return RestResponse<Void>
     */
    @PostMapping("/batchSetTest")
    public RestResponse<Void> batchSetTest(@RequestBody Map<String, Object> test) {
        RestResponse<Void> response = new RestResponse<>();
        service.batchSetTest(test);
        return response;
    }

    /**
     * 获取计算方式接口
     *
     * @return RestResponse<Map<String, Integer>>
     */
    @GetMapping("/findComputeMode")
    public RestResponse<Map<String, Integer>> findComputeMode() {
        RestResponse<Map<String, Integer>> response = new RestResponse<>();
        response.setData(service.findComputeMode());
        return response;
    }

    /**
     * 合并统计
     *
     * @return RestResponse<List<DtoTest>>
     */
    @GetMapping("/combineStatistical")
    public RestResponse<List<DtoTest>> combineStatistical(TestCriteria criteria) {
        RestResponse<List<DtoTest>> response = new RestResponse<>();
        PageBean<DtoTest> pageBean = super.getPageBean();
        service.combineStatistical(pageBean,criteria);
        response.setData(pageBean.getData());
        response.setCount(pageBean.getRowsCount());
        return response;
    }

    /**
     * 合并统计导出
     *
     * @return RestResponse<List<DtoTest>>
     */
    @GetMapping("/combineStatistical/export")
    public RestResponse<Void> combineStatisticalExport(TestCriteria criteria,HttpServletResponse response) {
        RestResponse<Void> res = new RestResponse<>();
        service.combineStatisticalExport(criteria,response);
        return res;
    }
}