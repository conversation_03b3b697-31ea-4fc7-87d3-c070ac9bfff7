-- 样品假删数据删除
INSERT INTO tb_base_job(id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, isConcurrent, status,
                        remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('1529cbf1-61ef-44b0-ab27-d8e26eb3fa6d', '样品假删数据删除服务', 'DEFAULT', 'sampleProcessor.delete()',
        '0 0 0 1 * ?', 3, b'0', 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-07-16 15:53:29', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-07-16 17:36:18');

-- 分析数据假删数据删除服务
INSERT INTO tb_base_job(id, jobName, jobGroup, invokeTarget, cronExpression, misfirePolicy, isConcurrent, status,
                        remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('edc3452a-4c8d-445f-943d-f09210d30e30', '分析数据假删数据删除服务', 'DEFAULT', 'analyzeDataProcessor.delete()',
        '0 0 0 1 * ?', 3, b'0', 0, '', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-07-16 16:11:20', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2024-07-16 17:36:15');

