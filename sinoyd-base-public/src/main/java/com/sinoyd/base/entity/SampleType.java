package com.sinoyd.base.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * SampleType实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SampleType")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class SampleType implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  SampleType() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 行业类型（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("行业类型（Guid）")
	private String industryTypeId;
    
    /**
    * 父节点（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("父节点（Guid）")
	private String parentId;
    
    /**
    * 唯一编号（预留）
    */
    @Column(length=50)
    @ApiModelProperty("唯一编号（预留）")
	private String typeCode;
    
    /**
    * 类型名称
    */
    @Column(length=50)
    @ApiModelProperty("类型名称")
    @Length(message = "类型名称{validation.message.length}", max = 50)
    private String typeName;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
    * 所属实验室（Guid）（预留）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室（Guid）（预留）")
	private String laboratoryId;
    
    /**
    * 简称
    */
    @Column(length=50)
    @ApiModelProperty("简称")
    @Length(message = "简称{validation.message.length}", max = 50)
    private String shortName;

    /**
     * 别名
     */
    @Column(length=50)
    @ApiModelProperty("别名")
    @Length(message = "别名{validation.message.length}", max = 50)
    private String typeAlias;

    /**
     * 是否启用样品编号标识
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否启用样品编号标识")
    private Boolean isOpenGroupTag=false;

    /**
    * 是否删除
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
	private Boolean isDeleted=false;
    
    /**
    * 保留时长（预留）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("保留时长（预留）")
	private Integer keepLongTime;
    
    /**
    * 报告周期（预留）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("报告周期（预留）")
	private Integer reportingCycle;
    
    /**
    * 样品分类(枚举EnumSampleCategory：1.大类2.样品类型3.模板,4.方案模板 )
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("样品分类(枚举EnumSampleCategory：1.大类2.样品类型3.模板,4.方案模板 )")
	private Integer category;
    
    /**
    * 排序值
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
	private Integer orderNum;
    
    /**
    * 图标
    */
    @ApiModelProperty("图标")
    @Length(message = "图标{validation.message.length}", max = 255)
    private String icon;

    /**
    * 系统类型(枚举EnumSystemType：1.LIMS  2.环境质量 4.污染源)
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("系统类型(枚举EnumSystemType：1.LIMS  2.环境质量 4.污染源)")
	private Integer systemType;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;

    /**
     * 默认标签分组id
     */
    @Column(length=50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("默认标签分组id")
    private String defaultLabelGroupId;

    /**
     * 现场任务分组id
     */
    @Column(length=50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("现场任务分组id")
    private String fieldTaskGroupId;

    /**
     * 比对类型
     */
    @Column(length=50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("比对类型")
    private Integer checkType;

    /**
     * 类型颜色
     */
    @Column(length = 50)
    @ApiModelProperty("类型颜色")
    @Length(message = "类型颜色{validation.message.length}", max = 50)
    private String typeColor;

    /**
     * 是否排放速率
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否排放速率")
    private Boolean isEmissionRate = false;
}