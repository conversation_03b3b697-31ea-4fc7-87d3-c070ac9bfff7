package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParamData;
import com.sinoyd.lims.lim.repository.lims.OcrConfigParamDataRepository;
import com.sinoyd.lims.lim.service.OcrConfigParamDataService;
import org.springframework.stereotype.Service;

/**
 * ocr对象参数数据接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@Service
public class OcrConfigParamDataServiceImpl extends BaseJpaServiceImpl<DtoOcrConfigParamData, String, OcrConfigParamDataRepository>
        implements OcrConfigParamDataService {

}
