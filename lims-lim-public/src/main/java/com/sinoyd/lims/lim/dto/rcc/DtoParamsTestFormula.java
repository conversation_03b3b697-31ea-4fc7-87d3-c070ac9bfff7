package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.ParamsTestFormula;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoParamsTestFormula实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ParamsTestFormula")
@Data
@DynamicInsert
public class DtoParamsTestFormula extends ParamsTestFormula {
    public DtoParamsTestFormula() {
    }

    public DtoParamsTestFormula(DtoParams dtoParams) {
        this.setParamsId(dtoParams.getId());
        this.setParamsName(dtoParams.getParamName());
        this.setAlias(dtoParams.getParamName());
        this.setDimension(dtoParams.getDimension());
        this.setDimensionId(dtoParams.getDimensionId());
    }
}