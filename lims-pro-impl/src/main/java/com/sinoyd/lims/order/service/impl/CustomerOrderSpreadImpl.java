package com.sinoyd.lims.order.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import com.sinoyd.lims.pro.vo.OrderCustomerSpreadVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service(CustomerOrderAnaService.CUSTOMER_ORDER_SPREAD)
public class CustomerOrderSpreadImpl extends AbsCustomerOrder {

    AreaService areaService;

    /**
     * 客户地区分布签单信息
     *
     * @param year 年份
     * @return List<OrderCustomerSpreadVo>
     */
    @Override
    public List<BaseOrderCustomerAnalyzeVO> statisticsList(Integer year) {
        String currentYearBegin = CalendarUtil.getCurrentYearBegin(year);
        String currentYearEnd = CalendarUtil.getCurrentYearEnd(year);

        //找出每个地区对应的签单情况
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT areaId,t3.finalQuotation,t4.`name` FROM TB_BASE_Enterprise t1,(SELECT id,enterpriseId,projectTypeId FROM TB_PRO_OrderForm WHERE isDeleted = 0 and orderDate BETWEEN ");
        sb.append(" ? ");
        sb.append(" AND ");
        sb.append(" ? ");
        sb.append(" AND ");
        sb.append("grantStatus = ? ) ");
        sb.append("t2,TB_PRO_OrderQuotation t3,TB_LIM_ProjectType t4 WHERE t1.id = t2.enterpriseId AND t2.id = t3.orderId AND t2.projectTypeId = t4.id and t1.isDeleted = 0 and t3.isDeleted = 0 and t4.isDeleted = 0");
        Object[] params = new Object[] {currentYearBegin,currentYearEnd,EnumPRO.EnumOrderSignStatus.已签订.getValue()};
        List<Map<String, Object>> orderCustomerList = super.getCustomerOrderData(sb.toString(),params);
        if (orderCustomerList == null) {
            return null;
        }
        //找出每个省份对应的签单情况
        List<String> areaIds = orderCustomerList.stream().map(item -> item.get("areaId").toString()).distinct().collect(Collectors.toList());
        List<OrderCustomerSpreadVo> orderCustomerSpreadVoList = new ArrayList<>();
        for (String areaId : areaIds) {
            if (StringUtil.isNotEmpty(areaId)) {
                OrderCustomerSpreadVo vo = new OrderCustomerSpreadVo();
                int totalQuotation = 0;
                Set<String> set = new HashSet();
                int signNum = 0;
                for (Map<String, Object> map : orderCustomerList) {
                    String id = map.get("areaId").toString();
                    String name = map.get("name").toString();
                    String finalQuotationString = map.get("finalQuotation").toString();
                    int finalQuotation = new BigDecimal(finalQuotationString).intValue();
                    if (areaId.equals(id)) {
                        totalQuotation += finalQuotation;
                        set.add(name);
                        signNum++;

                    }
                }
                DtoArea dtoArea = getAreaName(areaId);
                vo.setAreaName(dtoArea.getAreaName());
                vo.setOrderQuotationTotal(totalQuotation);
                vo.setOrderNum(signNum);
                vo.setOrderType(set.size());
                orderCustomerSpreadVoList.add(vo);
            }
        }
        //去除重复的省份，将重复的省份的签单数据加一起
        Map<String, List<OrderCustomerSpreadVo>> provinceMap = orderCustomerSpreadVoList.parallelStream()
                .collect(Collectors.groupingBy(OrderCustomerSpreadVo::getAreaName));
        List<BaseOrderCustomerAnalyzeVO> baseOrderCustomerAnalyzeVOArrayList = new ArrayList<>();
        for (Map.Entry<String, List<OrderCustomerSpreadVo>> map : provinceMap.entrySet()) {
            String provinceName = map.getKey();
            List<OrderCustomerSpreadVo> list = map.getValue();
            OrderCustomerSpreadVo vo = new OrderCustomerSpreadVo();
            int orderQuotationTotal = 0;
            int orderNum = 0;
            int orderType = 0;
            for (OrderCustomerSpreadVo orderCustomerSpreadVo : list) {
                orderQuotationTotal += orderCustomerSpreadVo.getOrderQuotationTotal();
                orderNum += orderCustomerSpreadVo.getOrderNum();
                orderType += orderCustomerSpreadVo.getOrderType();
            }
            vo.setAreaName(provinceName)
                    .setOrderQuotationTotal(orderQuotationTotal)
                    .setOrderNum(orderNum)
                    .setOrderType(orderType);
            baseOrderCustomerAnalyzeVOArrayList.add(vo);
        }

        return baseOrderCustomerAnalyzeVOArrayList;
    }


    /**
     * 根据parentId寻找省的递归方法
     *
     * @param areaId 最小的地区的id
     * @return DtoArea
     */
    private DtoArea getAreaName(String areaId) {
        DtoArea dtoArea = areaService.findById(areaId);
        String parentId = dtoArea.getParentId();
        if (parentId.equals("0")) {
            return dtoArea;
        }
        return getAreaName(parentId);
    }

    @Autowired
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }

}
