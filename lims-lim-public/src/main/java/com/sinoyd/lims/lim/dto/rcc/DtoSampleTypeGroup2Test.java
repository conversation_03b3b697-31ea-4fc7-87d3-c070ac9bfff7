package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.SampleTypeGroup2Test;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoSampleTypeGroup2Test实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_SampleTypeGroup2Test")
 @Data
 @DynamicInsert
 public  class DtoSampleTypeGroup2Test extends SampleTypeGroup2Test {

 }