package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.MpnConfigDetails;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.validator.constraints.Length;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoMpnConfigDetails实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/2/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_MpnConfigDetails")
@Data
@DynamicInsert
public class DtoMpnConfigDetails extends MpnConfigDetails {

    /**
     * 参数1名称
     */
    @Transient
    private String param1Name;

    /**
     * 参数2名称
     */
    @Transient
    private String param2Name;

    /**
     * 参数3名称
     */
    @Transient
    private String param3Name;

    /**
     * 结果参数名称
     */
    @Transient
    private String resultParamName;
}
