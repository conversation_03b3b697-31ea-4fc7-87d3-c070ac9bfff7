package com.sinoyd.lims.api.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;

import java.util.List;
import java.util.Map;

public interface MobileJudgeService extends IBaseJpaService<DtoSampleJudgeData, String> {

    /**
     * 根据点位查询因子
     *
     * @param folderId  点位id
     * @param cycValue  周期值
     * @return List<Map<String, String>>
     */
    List<Map<String, String>> getAnalyzeItemByFolderId(String folderId, Integer cycValue);

    /**
     * 根据点位id查询比岁数据
     *
     * @param folderId 点位id
     * @param cycValue 周期值
     * @param testId   测试项目id
     * @return
     */
    List<DtoSampleJudgeData> findByFolderIdForApp(String folderId, Integer cycValue, String testId);

}
