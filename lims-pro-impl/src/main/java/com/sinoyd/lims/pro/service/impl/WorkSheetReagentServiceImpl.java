package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeMethodReagentConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.AnalyzeMethodReagentConfigService;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.dto.DtoWorkSheetReagent;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.WorkSheetFolderRepository;
import com.sinoyd.lims.pro.repository.WorkSheetReagentRepository;
import com.sinoyd.lims.pro.service.NewLogService;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.service.WorkSheetReagentService;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Column;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 试剂配置记录操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
 @Service
public class WorkSheetReagentServiceImpl extends BaseJpaServiceImpl<DtoWorkSheetReagent,String,WorkSheetReagentRepository> implements WorkSheetReagentService {

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

     @Autowired
     private WorkSheetFolderRepository workSheetFolderRepository;

    @Autowired
    @Lazy
    private AnalyzeMethodReagentConfigService analyzeMethodReagentConfigService;

    @Override
    public void findByPage(PageBean<DtoWorkSheetReagent> pb, BaseCriteria workSheetReagentCriteria) {
        pb.setEntityName("DtoWorkSheetReagent r,DtoAnalyzeMethodReagentConfig c");
        pb.setSelect("select r");
        super.findByPage(pb, workSheetReagentCriteria);
    }

    /**
     * 分页查询试剂配置记录
     */
    @Override
    public PageBean<DtoAnalyzeMethodReagentConfig> findConfigByPage(PageBean<DtoWorkSheetReagent> pb, BaseCriteria analyzeMethodReagentConfigCriteria) {
        PageBean<DtoAnalyzeMethodReagentConfig> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(pb.getRowsPerPage());
        pageBean.setPageNo(pb.getPageNo());
        pageBean.setSort(pb.getSort());
        pageBean.setSortOrder(pb.getSortOrder());
        analyzeMethodReagentConfigService.findByPage(pageBean, analyzeMethodReagentConfigCriteria);
        if (StringUtil.isNotNull(pageBean.getData()) && pageBean.getData().size() > 0) {
            List<String> ids = pageBean.getData().stream().map(DtoAnalyzeMethodReagentConfig::getId).collect(Collectors.toList());
            List<DtoKeyValue> keyValues = this.findByReagentConfigIdIn(ids);
            for (DtoAnalyzeMethodReagentConfig cfg : pageBean.getData()) {
                String workSheetCodes = String.join("、", keyValues.stream().filter(p -> p.getKey().equals(cfg.getId())).
                        map(p -> String.valueOf(p.getValue())).collect(Collectors.toList()));
                cfg.setWorkSheetCodes(workSheetCodes);
            }
        }
        return pageBean;
    }

    /**
     * 新增试剂配置记录，级联新增试剂配置
     * @param entity 实体
     * @return 试剂配置记录
     */
    @Override
    @Transactional
    public DtoWorkSheetReagent save (DtoWorkSheetReagent entity) {
        DtoWorkSheetFolder folder = workSheetFolderRepository.findOne(entity.getWorksheetFolderId());
        DtoAnalyzeMethodReagentConfig cfg = new DtoAnalyzeMethodReagentConfig();
        cfg.setAnalyzeMethodId(folder.getAnalyzeMethodId());
        cfg.setReagentName(entity.getReagentName());
        cfg.setReagentSpecification(entity.getReagentSpecification());
        cfg.setConfigurationSolution(entity.getConfigurationSolution());
        cfg.setConfigDate(entity.getConfigDate());
        cfg.setExpiryDate(entity.getExpiryDate());
        cfg.setCourse(entity.getCourse());
        cfg.setOpinion(entity.getOpinion());
        cfg.setContext(entity.getContext());
        cfg.setDiluent(entity.getDiluent());
        cfg.setSuitItem(entity.getSuitItem());
        cfg.setConcentration(entity.getConcentration());
        cfg.setConstantVolume(entity.getConstantVolume());
        cfg.setReagentVolumeQuality(entity.getReagentVolumeQuality());
//        //实验室分析添加的试剂配制记录默认为标准溶液 BUG2023073196912
//        cfg.setReagentType(EnumLIM.EnumReagentType.标准溶液.getValue());
        //实验室分析添加试剂配置记录，添加试剂类型选项 BUG2023080796980
        cfg.setReagentType(entity.getReagentType());
        analyzeMethodReagentConfigService.save(cfg);
        entity.setReagentConfigId(cfg.getId());
        if(StringUtil.isNull(entity.getConfigDate())){
            entity.setConfigDate(new Date());
        }
        if(StringUtil.isNull(entity.getExpiryDate())){
            entity.setExpiryDate(DateUtil.stringToDate("1753-01-01",DateUtil.YEAR));
        }
        super.save(entity);

        String content = entity.getContent();
        newLogService.createLog(entity.getWorksheetFolderId(), "新增试剂配置记录:" + content, "",
                EnumPRO.EnumLogType.检测单试剂配置.getValue(), EnumPRO.EnumLogObjectType.检测单.getValue(), EnumPRO.EnumLogOperateType.新增试剂配置.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        return entity;
    }

    /**
     * 修改试剂配置记录，级联修改试剂配置
     * @param entity 实体
     * @return 试剂配置记录
     */
    @Override
    @Transactional
    public DtoWorkSheetReagent update (DtoWorkSheetReagent entity) {
        DtoWorkSheetReagent reagent = repository.findOne(entity.getId());
        Map<String, Map<String, Object>> reagentChange = proService.getCompare(reagent, entity);
        //bug BUG2023062096452 实验室分析】①试剂配制记录添加后，编辑无法修改内容，需要将限制去掉，允许保存。②修改功能开放后，如果是选择已有试剂的情况下，
        // 需要排查是否同步修改了配置，如果同步修改了，需要将同步修改的机制去掉。
//        String userId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "";
//        DtoAnalyzeMethodReagentConfig cfg = analyzeMethodReagentConfigService.findOne(entity.getReagentConfigId());
//        if (!cfg.getCreator().equals(userId)) {
//            throw new BaseException("您无法修改其他人的试剂配置！");
//        }
//        cfg.setReagentName(entity.getReagentName());
//        cfg.setReagentSpecification(entity.getReagentSpecification());
//        cfg.setConfigurationSolution(entity.getConfigurationSolution());
//        cfg.setConfigDate(entity.getConfigDate());
//        cfg.setExpiryDate(entity.getExpiryDate());
//        cfg.setCourse(entity.getCourse());
//        cfg.setOpinion(entity.getOpinion());
//        cfg.setContext(entity.getContext());
//        analyzeMethodReagentConfigService.update(cfg);
        if(StringUtil.isNull(entity.getConfigDate())){
            entity.setConfigDate(new Date());
        }
        if(StringUtil.isNull(entity.getExpiryDate())){
            entity.setExpiryDate(DateUtil.stringToDate("1753-01-01",DateUtil.YEAR));
        }
        super.update(entity);
        List<String> contents = this.getChangeContent(reagentChange, EnumPRO.EnumWorkSheetReagentField.试剂名称, EnumPRO.EnumWorkSheetReagentField.试剂规格, EnumPRO.EnumWorkSheetReagentField.配置溶液,
                EnumPRO.EnumWorkSheetReagentField.配置过程, EnumPRO.EnumWorkSheetReagentField.配置日期, EnumPRO.EnumWorkSheetReagentField.有效期);
        if (contents.size() > 0) {
            newLogService.createLog(entity.getWorksheetFolderId(), String.format("修改试剂配置记录:%s", String.join(";", contents)), "",
                    EnumPRO.EnumLogType.检测单试剂配置.getValue(), EnumPRO.EnumLogObjectType.检测单.getValue(), EnumPRO.EnumLogOperateType.修改试剂配置.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
        return entity;
    }

    /**
     * 选择试剂配置
     * @param reagentConfigId 试剂配置id
     * @param workSheetFolderId 检测单id
     * @return 试剂配置记录
     */
    @Override
    @Transactional
    public DtoWorkSheetReagent relate (String reagentConfigId,String workSheetFolderId) {
        DtoAnalyzeMethodReagentConfig cfg = analyzeMethodReagentConfigService.findOne(reagentConfigId);

        DtoWorkSheetReagent reagent = new DtoWorkSheetReagent();
        reagent.setReagentName(cfg.getReagentName());
        reagent.setReagentSpecification(cfg.getReagentSpecification());
        reagent.setConfigurationSolution(cfg.getConfigurationSolution());
        reagent.setConfigDate(cfg.getConfigDate());
        reagent.setExpiryDate(cfg.getExpiryDate());
        reagent.setCourse(cfg.getCourse());
        reagent.setOpinion(cfg.getOpinion());
        reagent.setContext(cfg.getContext());
        reagent.setWorksheetFolderId(workSheetFolderId);
        reagent.setReagentConfigId(reagentConfigId);
        reagent.setCreator(cfg.getCreator());
        reagent.setDiluent(cfg.getDiluent());
        reagent.setReagentType(cfg.getReagentType());
        reagent.setConcentration(cfg.getConcentration());
        reagent.setConstantVolume(cfg.getConstantVolume());
        reagent.setReagentVolumeQuality(cfg.getReagentVolumeQuality());
        reagent.setSuitItem(cfg.getSuitItem());
        reagent.setRelateFlag(true);
        super.save(reagent);
        String content = reagent.getContent();
        newLogService.createLog(workSheetFolderId, "选择了已有配置记录:" + content, "",
                EnumPRO.EnumLogType.检测单试剂配置.getValue(), EnumPRO.EnumLogObjectType.检测单.getValue(), EnumPRO.EnumLogOperateType.新增试剂配置.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        return reagent;
    }

    @Override
    @Transactional
    public List<DtoWorkSheetReagent> batchRelate(List<String> reagentConfigIds, String workSheetFolderId) {
        List<DtoAnalyzeMethodReagentConfig> cfgs = analyzeMethodReagentConfigService.findAll(reagentConfigIds);
        List<DtoWorkSheetReagent> saveList = new ArrayList<>();
        List<DtoLog> logList = new ArrayList<>();
        for (DtoAnalyzeMethodReagentConfig cfg : cfgs) {
            DtoWorkSheetReagent reagent = new DtoWorkSheetReagent();
            reagent.setReagentName(cfg.getReagentName());
            reagent.setReagentSpecification(cfg.getReagentSpecification());
            reagent.setConfigurationSolution(cfg.getConfigurationSolution());
            reagent.setConfigDate(cfg.getConfigDate());
            reagent.setExpiryDate(cfg.getExpiryDate());
            reagent.setCourse(cfg.getCourse());
            reagent.setOpinion(cfg.getOpinion());
            reagent.setContext(cfg.getContext());
            reagent.setWorksheetFolderId(workSheetFolderId);
            reagent.setReagentConfigId(cfg.getId());
            reagent.setCreator(cfg.getCreator());
            reagent.setDiluent(cfg.getDiluent());
            reagent.setReagentType(cfg.getReagentType());
            reagent.setConcentration(cfg.getConcentration());
            reagent.setConstantVolume(cfg.getConstantVolume());
            reagent.setReagentVolumeQuality(cfg.getReagentVolumeQuality());
            reagent.setSuitItem(cfg.getSuitItem());
            reagent.setRelateFlag(true);
            saveList.add(reagent);
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.新增试剂配置.toString());
            log.setLogType(EnumPRO.EnumLogType.检测单试剂配置.getValue());
            log.setObjectId(reagent.getWorksheetFolderId());
            log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
            log.setComment("删除试剂配置记录:" + reagent.getContent());
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        newLogService.createLog(logList, EnumPRO.EnumLogType.检测单试剂配置.getValue());
        if (StringUtil.isNotEmpty(saveList)) {
            super.save(saveList);
        }
        return saveList;
    }

    /**
     * 批量删除试剂配置
     */
    @Transactional
    @Override
    public Integer logicDeleteById(List<String> reagentIds, String selectUserId) {
        List<DtoWorkSheetReagent> reagentList = super.findAll(reagentIds);
        Integer count = this.repository.logicDeleteById(reagentIds,new Date());
        List<DtoLog> logList = new ArrayList<>();
        for (DtoWorkSheetReagent reagent : reagentList) {
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.删除试剂配置.toString());
            log.setLogType(EnumPRO.EnumLogType.检测单试剂配置.getValue());
            log.setObjectId(reagent.getWorksheetFolderId());
            log.setObjectType(EnumPRO.EnumLogObjectType.检测单.getValue());
            log.setComment("删除试剂配置记录:" + reagent.getContent());
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        newLogService.createLog(logList, EnumPRO.EnumLogType.检测单试剂配置.getValue());

        List<String> reagentConfigIds = reagentList.stream().map(DtoWorkSheetReagent::getReagentConfigId).distinct().collect(Collectors.toList());
        List<DtoAnalyzeMethodReagentConfig> cfgList = analyzeMethodReagentConfigService.findAll(reagentConfigIds);
        String userId = StringUtils.isNotNullAndEmpty(selectUserId) ? selectUserId :
                (StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "");
        cfgList = cfgList.stream().filter(p -> p.getCreator().equals(userId)).collect(Collectors.toList());
        if (cfgList.size() > 0) {
            reagentConfigIds = cfgList.stream().map(DtoAnalyzeMethodReagentConfig::getId).collect(Collectors.toList());
            List<DtoWorkSheetReagent> allReagentList = repository.findByReagentConfigIdIn(reagentConfigIds);
            reagentConfigIds.removeAll(allReagentList.stream().map(DtoWorkSheetReagent::getReagentConfigId).collect(Collectors.toList()));
            if (reagentConfigIds.size() > 0) {
                analyzeMethodReagentConfigService.logicDeleteById(reagentConfigIds);
            }
        }
        return count;
    }

    /**
     * 获取关联
     *
     * @param reagentConfigIds 试剂配置id集合
     * @return 关联
     */
    private List<DtoKeyValue>findByReagentConfigIdIn(List<String> reagentConfigIds) {
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoWorkSheetReagent> pb = new PageBean<>();
        pb.setEntityName("DtoWorkSheetReagent wsr,DtoWorkSheetFolder wsf");
        pb.setSelect("select wsr.reagentConfigId,wsf.workSheetCode");
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.addCondition(" and wsr.worksheetFolderId = wsf.id");
        pb.addCondition(" and wsr.reagentConfigId in :reagentConfigIds");
        values.put("reagentConfigIds", reagentConfigIds);
        comRepository.findByPage(pb, values);

        Set<DtoKeyValue> keyValueSet = new HashSet<>();
        List<DtoWorkSheetReagent> datas = pb.getData();

        Iterator<DtoWorkSheetReagent> ite = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            String reagentConfigId = (String) objs[0];
            String workSheetCode = (String) objs[1];
            DtoKeyValue dto = new DtoKeyValue();
            dto.setKey(reagentConfigId);
            dto.setValue(workSheetCode);
            keyValueSet.add(dto);
        }

        return new ArrayList<>(keyValueSet);
    }

    private List<String> getChangeContent(Map<String, Map<String, Object>> map, EnumPRO.EnumWorkSheetReagentField... fields) {
        String format = "</br>%s由'%s',修改为'%s'";
        List<String> contents = new ArrayList<>();
        for (EnumPRO.EnumWorkSheetReagentField field : fields) {
            if (map.containsKey(field.getValue())) {
                String from = StringUtil.isNull(map.get(field.getValue()).get("from")) ? "" : map.get(field.getValue()).get("from").toString();
                String to = StringUtil.isNull(map.get(field.getValue()).get("to")) ? "" : map.get(field.getValue()).get("to").toString();
                contents.add(String.format(format, field.toString(), from, to));
            }
        }
        return contents;
    }
}