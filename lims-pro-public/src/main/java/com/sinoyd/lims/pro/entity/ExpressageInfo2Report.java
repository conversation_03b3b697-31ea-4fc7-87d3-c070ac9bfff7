package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;


/**
 * ExpressageInfo2Report实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ExpressageInfo2Report")
 @Data
 public  class ExpressageInfo2Report implements BaseEntity,Serializable {
 
 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 快递id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("快递id")
	private String expressageInfoId;
    
    /**
    * 项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告id")
	private String reportId;
    
 }