package com.sinoyd.lims.foreign.service.impl;


import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataReportVO;
import com.sinoyd.lims.foreign.DataSampleGroupVO;
import com.sinoyd.lims.foreign.DataSamplePreparationVO;
import com.sinoyd.lims.foreign.service.DataSampleGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 样品分组信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataSampleGroupServiceImpl extends AbsQueryDataServiceImpl<DataSampleGroupVO> implements DataSampleGroupService {


    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id,receiveId,sampleId,sampleTypeGroupName,analyseItemNames,fixer,");
        sql.append("containerName,pretreatmentMethod,sampleVolume,saveCondition,");
        sql.append("riskDescription,transportationCondition,isGroup,containerStatus,");
        sql.append("orgId,domainId ");
        sql.append(" from TB_PRO_SampleGroup ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime ", "syncTime");
        super.appendCondition(sql, map, "AND receiveId in (:receiveIds)", "receiveIds");
        super.appendCondition(sql, map, "AND sampleId in (:sampleIds) ", "sampleIds");

    }

    @Override
    protected RowMapper<DataSampleGroupVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataSampleGroupVO sampleGroupVO = new DataSampleGroupVO();
            sampleGroupVO.setId(UUIDHelper.NewID());
            sampleGroupVO.setReceiveId(rs.getString("receiveId"));
            sampleGroupVO.setSampleId(rs.getString("sampleId"));
            sampleGroupVO.setSampleTypeGroupName(rs.getString("sampleTypeGroupName"));
            sampleGroupVO.setAnalyseItemNames(rs.getString("analyseItemNames"));
            sampleGroupVO.setFixer(rs.getString("fixer"));
            sampleGroupVO.setContainerName(rs.getString("containerName"));
            sampleGroupVO.setPretreatmentMethod(rs.getString("pretreatmentMethod"));
            sampleGroupVO.setSampleVolume(rs.getString("sampleVolume"));
            sampleGroupVO.setSaveCondition(rs.getString("saveCondition"));
            sampleGroupVO.setRiskDescription(rs.getString("riskDescription"));
            sampleGroupVO.setTransportationCondition(rs.getString("transportationCondition"));
            sampleGroupVO.setIsGroup(rs.getInt("isGroup"));
            sampleGroupVO.setContainerStatus(rs.getInt("containerStatus"));
            sampleGroupVO.setOrgId(rs.getString("orgId"));
            sampleGroupVO.setDomainId(rs.getString("domainId"));
            sampleGroupVO.setSyncTime(new Date());
            return sampleGroupVO;
        };
    }
}
