package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.FlowCalibrationCriteria;
import com.sinoyd.lims.pro.dto.DtoFlowCalibration;
import com.sinoyd.lims.pro.service.FlowCalibrationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流量校准服务
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/14
 */
@Api(tags = "示例: 流量校准服务")
@RestController
@RequestMapping("api/pro/flowCalibration")
public class FlowCalibrationController extends BaseJpaController<DtoFlowCalibration,String, FlowCalibrationService> {

    /**
     * 分页动态条件查询
     *
     * @param criteria 条件参数
     * @return RestResponse<List < DtoFlowCalibration>>
     */
    @ApiOperation(value = "分页动态条件查询", notes = "分页动态条件查询")
    @GetMapping
    public RestResponse<List<DtoFlowCalibration>> findByPage(FlowCalibrationCriteria criteria) {
        PageBean<DtoFlowCalibration> pageBean = super.getPageBean();
        RestResponse<List<DtoFlowCalibration>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增
     *
     * @param flowCalibration 对象
     * @return RestResponse<DtoFlowCalibration>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping
    public RestResponse<DtoFlowCalibration> save(@RequestBody @Validated DtoFlowCalibration flowCalibration) {
        RestResponse<DtoFlowCalibration> restResponse = new RestResponse<>() ;
        restResponse.setData(service.save(flowCalibration));
        return restResponse;
    }

    /**
     * 修改
     *
     * @param flowCalibration 对象
     * @return RestResponse<DtoFlowCalibration>
     */
    @ApiOperation(value = "修改", notes = "修改")
    @PutMapping
    public RestResponse<DtoFlowCalibration> update(@RequestBody @Validated DtoFlowCalibration flowCalibration) {
        RestResponse<DtoFlowCalibration> restResponse = new RestResponse<>() ;
        restResponse.setData(service.update(flowCalibration));
        return restResponse;
    }

    /**
     * 批量删除校准记录
     *
     * @param ids 对象标识集合
     * @return RestResponse<Integer>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody List<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>() ;
        restResponse.setData(service.logicDeleteById(ids));
        return restResponse;
    }

    /**
     * 批量删除数据行
     *
     * @param ids 对象标识集合
     * @return RestResponse<Integer>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/row")
    public RestResponse<Integer> deleteRow(@RequestBody List<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>() ;
        restResponse.setData(service.deleteRow(ids));
        return restResponse;
    }

    /**
     * 详情
     *
     * @param id 标识
     * @return RestResponse<DtoFlowCalibration>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @GetMapping("/{id}")
    public RestResponse<DtoFlowCalibration> findOne(@PathVariable String id) {
        RestResponse<DtoFlowCalibration> restResponse = new RestResponse<>() ;
        restResponse.setData(service.findOne(id));
        return restResponse;
    }
}
