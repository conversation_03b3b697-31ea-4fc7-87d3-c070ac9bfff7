--  新增 tb_lim_person2test  tb_lim_testexpand 的删除触发器
drop trigger if exists before_delete_person2test;
DELIMITER $$
CREATE TRIGGER before_delete_person2test
    BEFORE DELETE
    ON tb_lim_person2test
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_person2test', old.id, '00000000-0000-0000-0000-000000000000', now(), 2, null, null, null,
            old.orgId, '00000000-0000-0000-0000-000000000000');
End $$
DELIMITER ;


drop trigger if exists before_delete_testexpand;
DELIMITER $$
CREATE TRIGGER before_delete_testexpand
    BEFORE DELETE
    ON tb_lim_testexpand
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_testexpand', old.id, '00000000-0000-0000-0000-000000000000', now(), 2, null, null, null,
            old.orgId, '00000000-0000-0000-0000-000000000000');
End $$
DELIMITER ;