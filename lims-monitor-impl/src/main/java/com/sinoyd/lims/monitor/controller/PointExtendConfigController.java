package com.sinoyd.lims.monitor.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.criteria.PointExtendConfigCriteria;
import com.sinoyd.lims.monitor.dto.rcc.DtoPointExtendConfig;
import com.sinoyd.lims.monitor.service.PointExtendConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 点位拓展字段配置服务接口定义
 *
 * <AUTHOR>
 * @version V5.2.0 2023/06/12
 * @since V100R001
 */
@Api(tags = "示例: PointExtendConfig服务")
@RestController
@RequestMapping("api/monitor/pointExtendConfig")
public class PointExtendConfigController extends BaseJpaController<DtoPointExtendConfig, String, PointExtendConfigService> {

    /**
     * 分页动态条件查询点位拓展配置
     *
     * @param criteria 查询条件
     * @return 分页查询数据
     */
    @ApiOperation(value = "分页动态条件查询点位拓展配置", notes = "分页动态条件查询点位拓展配置")
    @GetMapping
    public RestResponse<List<DtoPointExtendConfig>> findByPage(PointExtendConfigCriteria criteria) {
        PageBean<DtoPointExtendConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoPointExtendConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }


    /**
     * 根据Id查询单挑点位拓展字段配置数据
     *
     * @param id 拓展字段配置Id
     * @return 单条数据
     */
    @ApiOperation(value = "根据Id查询单挑点位拓展字段配置数据", notes = "根据Id查询单挑点位拓展字段配置数据")
    @GetMapping("/{id}")
    public RestResponse<DtoPointExtendConfig> findOne(@PathVariable("id") String id) {
        RestResponse<DtoPointExtendConfig> restResponse = new RestResponse<>();
        DtoPointExtendConfig data = service.findOne(id);
        restResponse.setRestStatus(StringUtil.isNull(data) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(data);
        return restResponse;
    }


    /**
     * 保存点位拓展配置数据
     *
     * @param pointExtendConfig 点位拓展配置数据
     * @return 保存后的点位拓展配置数据
     */
    @ApiOperation(value = "保存点位拓展配置数据", notes = "保存点位拓展配置数据")
    @PostMapping
    public RestResponse<DtoPointExtendConfig> save(@RequestBody @Validated DtoPointExtendConfig pointExtendConfig) {
        RestResponse<DtoPointExtendConfig> restResponse = new RestResponse<>();
        DtoPointExtendConfig save = service.save(pointExtendConfig);
        restResponse.setRestStatus(StringUtil.isNull(save) ? ERestStatus.ERROR : ERestStatus.SUCCESS);
        restResponse.setData(save);
        return restResponse;
    }


    /**
     * 更新点位拓展配置数据
     *
     * @param pointExtendConfig 点位拓展配置数据
     * @return 更新后的点位拓展配置数据
     */
    @ApiOperation(value = "更新点位拓展配置数据", notes = "更新点位拓展配置数据")
    @PutMapping
    public RestResponse<DtoPointExtendConfig> update(@RequestBody @Validated DtoPointExtendConfig pointExtendConfig) {
        RestResponse<DtoPointExtendConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.update(pointExtendConfig));
        restResponse.setMsg("操作成功");
        return restResponse;
    }


    /**
     * 删除点位拓展配置数据
     *
     * @param ids 需要删除的点位配置数据
     * @return 删除的条数
     */
    @ApiOperation(value = "删除点位拓展配置数据", notes = "删除点位拓展配置数据")
    @DeleteMapping
    public RestResponse<Integer> update(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        restResponse.setMsg("删除成功");
        return restResponse;
    }
}
