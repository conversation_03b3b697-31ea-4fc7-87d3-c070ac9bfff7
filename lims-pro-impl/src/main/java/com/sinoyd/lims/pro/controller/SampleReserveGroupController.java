package com.sinoyd.lims.pro.controller;


import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.SampleReserveGroupCriteria;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import com.sinoyd.lims.pro.dto.DtoSampleReserve;
import com.sinoyd.lims.pro.service.SampleReserveGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * SampleReserve服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/20
 * @since V100R001
 */
@Api(tags = "示例: SampleReserve服务")
@RestController
@RequestMapping("api/pro/sampleReserveGroup")
public class SampleReserveGroupController extends BaseJpaController<DtoSampleReserve, String, SampleReserveGroupService> {

    /**
     * 分页查询交接提交后的所有样品
     *
     * @return 查询结果
     */
    @ApiModelProperty(name = "分页查询交接提交后的所有样品", notes = "分页查询交接提交后的所有样品")
    @GetMapping("/samples")
    public RestResponse<List<DtoSampleGroup>> findSamples(SampleReserveGroupCriteria criteria) {
        PageBean<DtoSampleGroup> pageBean = super.getPageBean();
        RestResponse<List<DtoSampleGroup>> response = new RestResponse<>();
        service.findSamplesGroupByPage(pageBean, criteria);
        response.setData(pageBean.getData());
        response.setCount(pageBean.getRowsCount());
        response.setMsg("查询成功");
        return response;
    }


    /**
     * 查询详情
     *
     * @return 查询详情
     */
    @ApiModelProperty(name = "查询详情", notes = "查询详情")
    @GetMapping("/detail/{sampleGroupId}")
    public RestResponse<DtoSample> findSampleDetail(@PathVariable("sampleGroupId") String sampleGroupId) {
        RestResponse<DtoSample> response = new RestResponse<>();
        response.setData(service.findSampleDetail(sampleGroupId));
        response.setMsg("查询成功");
        return response;
    }

    /**
     * 查询所选择的样品列表的留样信息
     *
     * @return 查询结果
     */
    @ApiModelProperty(name = "查询所选择的样品列表的留样信息", notes = "查询所选择的样品列表的留样信息")
    @PostMapping("/details")
    public RestResponse<Map<String, Object>> findSampleDetails(@RequestBody List<String> sampleGroupIds) {
        RestResponse<Map<String, Object>> response = new RestResponse<>();
        response.setData(service.findSampleDetails(sampleGroupIds));
        response.setMsg("查询成功");
        return response;
    }

    /**
     * 根据样品Id获取对应的测试项目
     *
     * @param sampleGroupIds 样品分组id
     * @return 结果
     */
    @ApiModelProperty(name = "根据样品Id获取对应的测试项目", notes = "根据样品Id获取对应的测试项目")
    @PostMapping("/analyzeItems")
    public RestResponse<List<DtoAnalyzeItem>> findTestBySampleIds(@RequestBody List<String> sampleGroupIds) {
        RestResponse<List<DtoAnalyzeItem>> response = new RestResponse<>();
        List<DtoAnalyzeItem> tests = service.findAnalyzeItems(sampleGroupIds);
        response.setData(tests);
        response.setCount(tests.size());
        response.setMsg("查询成功");
        return response;
    }

    /**
     * 保存数据（领取/处置）
     *
     * @param reserve 需要保存的数据
     * @return 保存成功的数据
     */
    @ApiModelProperty(name = "保存数据", notes = "保存数据")
    @PostMapping
    public RestResponse<List<DtoSampleReserve>> save(@RequestBody DtoSampleReserve reserve) {
        RestResponse<List<DtoSampleReserve>> response = new RestResponse<>();
        response.setData(service.saveReserve(reserve));
        response.setMsg("保存成功");
        return response;
    }


    /**
     * 判断数据是否存在（领取/处置）
     *
     * @param reserve 需要保存的数据
     * @return 保存成功的数据
     */
    @ApiModelProperty(name = "判断数据是否存在", notes = "判断数据是否存在")
    @PostMapping("/judge")
    public RestResponse<List<DtoSampleReserve>> judgeSaveData(@RequestBody DtoSampleReserve reserve) {
        RestResponse<List<DtoSampleReserve>> response = new RestResponse<>();
        List<DtoSampleReserve> dtoSampleReserves = service.judgeSaveData(reserve);
        response.setData(dtoSampleReserves);
        response.setCount(dtoSampleReserves.size());
        response.setMsg(StringUtil.isNotEmpty(dtoSampleReserves) ? "查询成功" : "不存在已保存的数据");
        return response;
    }


    /**
     * 取消样品处置
     *
     * @param sampleGroupIds 样品管理id
     * @return 取消的数量
     */
    @ApiModelProperty(name = "取消样品处置", notes = "取消样品处置")
    @PutMapping
    public RestResponse<Integer> cancelDispose(@RequestBody List<String> sampleGroupIds) {
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.cancelDispose(sampleGroupIds));
        response.setMsg("操作成功");
        return response;
    }

}
