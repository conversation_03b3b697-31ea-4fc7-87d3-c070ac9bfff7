package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.SHSamplingInstrumentNewCriteria;
import com.sinoyd.lims.pro.dto.DtoSHSamplingInstrumentNew;
import com.sinoyd.lims.pro.service.SHSamplingInstrumentNewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SHSamplingInstrument服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/28
 * @since V100R001
 */
@Api(tags = "示例: SHSamplingInstrument服务")
@RestController
@RequestMapping("api/pro/SHSamplingInstrumentNew")
public class SHSamplingInstrumentNewController extends BaseJpaController<DtoSHSamplingInstrumentNew,String, SHSamplingInstrumentNewService> {

    @ApiOperation(value = "监管平台采样仪器列表", notes = "监管平台采样仪器列表")
    @GetMapping
    public RestResponse<List<DtoSHSamplingInstrumentNew>> find(SHSamplingInstrumentNewCriteria criteria){
        PageBean<DtoSHSamplingInstrumentNew> page = super.getPageBean();
        RestResponse<List<DtoSHSamplingInstrumentNew>> response = new RestResponse<>();
        service.findByPage(page,criteria);
        response.setData(page.getData());
        response.setCount(page.getRowsCount());
        return response;
    }

    /**
     * "根据id批量删除DtoSHSamplingInstrumentNew
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    @ApiOperation(value = "批量新增", notes = "批量新增")
    @PostMapping
    public RestResponse<List<DtoSHSamplingInstrumentNew>> save(@RequestBody List<DtoSHSamplingInstrumentNew> samplingInstruments) {
        RestResponse<List<DtoSHSamplingInstrumentNew>> response = new RestResponse<>();
        response.setData(service.save(samplingInstruments));
        return response;
    }

}
