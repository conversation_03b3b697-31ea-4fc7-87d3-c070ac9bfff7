package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SampleFolderEvaluate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SampleFolderEvaluate")
@Data
@DynamicInsert
public class DtoSampleFolderEvaluate extends SampleFolderEvaluate {
}
