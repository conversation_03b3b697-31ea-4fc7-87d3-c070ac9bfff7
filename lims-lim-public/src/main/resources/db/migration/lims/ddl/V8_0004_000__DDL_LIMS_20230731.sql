CREATE TABLE `tb_pro_samplegroup2test`
(
    `id`                   varchar(50) NOT NULL COMMENT 'id',
    `sampleGroupId`        varchar(50)          DEFAULT NULL COMMENT '样品分组id',
    `testId`               varchar(50)          DEFAULT NULL COMMENT '测试项目id',
    `analyzeItemId`        varchar(50) NOT NULL DEFAULT '' COMMENT '分析项目Id（Guid）',
    `redAnalyzeMethodName` varchar(255)         DEFAULT '' COMMENT '分析方法名称',
    `redCountryStandard`   varchar(100)         DEFAULT '' COMMENT '国家标准',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 样品分组表新增分组标识
ALTER table tb_pro_samplegroup
    add `reserveNums` int(10)  DEFAULT '0' COMMENT '领取数量';
ALTER table tb_pro_samplegroup
    add `analyseNums` int(10)  DEFAULT '0' COMMENT '分析项目数量';
