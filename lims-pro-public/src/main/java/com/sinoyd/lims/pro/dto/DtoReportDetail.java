package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReportDetail;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoReportDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ReportDetail")
 @Data
 @DynamicInsert
 public  class DtoReportDetail extends ReportDetail {

 }