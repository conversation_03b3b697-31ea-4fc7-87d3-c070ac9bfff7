package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 自动任务下达列表查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AutoTaskPlanCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用月份
     */
    private List<String> month;

    /**
     * 计划名称计划编码
     */
    private String key;

    /**
     * 监测计划名称
     */
    private String monitorName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (a.taskName like :key or a.taskCode like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.monitorName)) {
            condition.append(" and exists (select 1 from DtoTask2FixedProperty tf, DtoFixedPointProperty fp where a.id = tf.taskId and tf.fixedPropertyId = fp.id and fp.propertyName like :monitorName )");
            values.put("monitorName", "%" + this.monitorName + "%");
        }
        return condition.toString();
    }
}
