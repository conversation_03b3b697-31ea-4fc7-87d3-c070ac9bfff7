package com.sinoyd.lims.lim.repository.lims;

import java.util.Collection;
import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 测试项目人员配置仓储
 * <AUTHOR>
 * @version V1.0.0 2019/1/12
 * @since V100R001
 */
public interface Person2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoPerson2Test, String> {

    /**
     * 根据测试项目id、样品类型（小类）id删除测试人员
     *
     * @param testId       测试项目id
     * @param sampleTypeId 样品类型（小类）id
     * @return Integer
     */
    Integer deleteByTestIdAndSampleTypeId(String testId, String sampleTypeId);


    /**
     * 根据测试项目ids、样品类型（小类）删除相应的人员信息
     *
     * @param testIds      测试项目ids
     * @param sampleTypeId 检测类型id
     * @return Integer
     */
    Integer deleteByTestIdInAndSampleTypeId(List<String> testIds, String sampleTypeId);

    /**
     * 根据测试项目id、样品类型（小类）id查询测试人员
     *
     * @param testId       测试项目id
     * @param sampleTypeId 检测类型id
     * @return 返回想要的测试人员
     */
    @Query("select p from DtoPerson2Test p where p.testId = :testId and p.sampleTypeId = :sampleTypeId order by orderNum desc")
    List<DtoPerson2Test> findByTestIdAndSampleTypeId(@Param("testId") String testId, @Param("sampleTypeId") String sampleTypeId);


    /**
     * 根据测试项目id、样品类型（小类）id查询测试人员
     *
     * @param testIds      测试项目id
     * @param sampleTypeId 检测类型id
     * @return 返回想要的测试人员
     */
    @Query("select p from DtoPerson2Test p where p.testId in :testIds and p.sampleTypeId = :sampleTypeId order by orderNum desc")
    List<DtoPerson2Test> findByTestIdInAndSampleTypeId(@Param("testIds") List<String> testIds, @Param("sampleTypeId") String sampleTypeId);


    /**
     * 根据测试项目id、样品类型（小类）id查询测试人员
     *
     * @param testIds       测试项目ids
     * @param sampleTypeIds 检测类型ids
     * @return 返回想要的测试人员
     */
    List<DtoPerson2Test> findByTestIdInAndSampleTypeIdIn(List<String> testIds, List<String> sampleTypeIds);

    /**
     * 根据测试项目id查询测试人员
     *
     * @param testId
     * @return
     */
    List<DtoPerson2Test> findByTestId(String testId);

    /**
     * 根据测试项目id集合查询第一负责人员
     *
     * @param testIds 测试项目id集合
     * @return 第一负责人
     */
    List<DtoPerson2Test> findByIsDefaultPersonTrueAndTestIdIn(Collection<String> testIds);

    /**
     * 根据测试项目id查询第一负责人员
     *
     * @param testId 测试项目id
     * @return 第一负责人
     */
    List<DtoPerson2Test> findByIsDefaultPersonTrueAndTestId(String testId);

    /**
     * 根据测试项目id获取相关的检测人员
     *
     * @param testIds 测试项目ids
     * @return 返回想要的检测人员
     */
    List<DtoPerson2Test> findByTestIdIn(List<String> testIds);

    /**
     * 查询去从的检测类型id
     *
     * @param testId 测试项目id
     * @return 返回去重的检测类型id
     */
    @Query("select distinct p.sampleTypeId from DtoPerson2Test p where p.testId = :testId")
    List<String> findSampleTypeIdByTestId(@Param("testId") String testId);
}