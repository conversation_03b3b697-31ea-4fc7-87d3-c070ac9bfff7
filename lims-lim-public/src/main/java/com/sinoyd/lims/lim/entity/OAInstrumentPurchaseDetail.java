package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * OAInstrumentPurchaseDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OAInstrumentPurchaseDetail")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class OAInstrumentPurchaseDetail extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  OAInstrumentPurchaseDetail() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 仪器标识（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("仪器标识（Guid）")
	private String instrumentId;
    
    /**
    * 仪器名称
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("仪器名称")
    @Length(message = "仪器名称{validation.message.length}", max = 50)
	private String instrumentName;
    
    /**
    * 规格型号
    */
    @Column(length=50)
    @ApiModelProperty("规格型号")
    @Length(message = "规格型号{validation.message.length}", max = 50)
    private String materialModel;
    
    /**
    * 申请计划数量
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("申请计划数量")
    private Integer planNum;
    
    /**
    * 剩余未入库数量
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("剩余未入库数量")
    private Integer surplusNum;

    /**
     * 实际采购数量
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("实际采购数量")
    private Integer purchaseNum;

    /**
    * 供应时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("供应时间")
    private Date supplyTime;
    
    /**
    * 用途
    */
    @Column(length=100)
    @ApiModelProperty("用途")
    @Length(message = "用途{validation.message.length}", max = 100)
    private String purpose;
    
    /**
    * 技术要求
    */
    @ApiModelProperty("技术要求")
    @Length(message = "技术要求{validation.message.length}", max = 255)
    private String skillRequire;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }