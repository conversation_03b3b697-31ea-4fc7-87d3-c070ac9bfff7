package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.customer.DtoImportEnterprise;
import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.service.IndustryTypeService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.lims.lim.service.DownLoadEnterpriseTemplateService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DownLoadEnterpriseTemplateServiceImpl implements DownLoadEnterpriseTemplateService {
    //region 注入
    private ImportUtils importUtils;

    private IndustryTypeService industryTypeService;

    //endregion

    @Override
    public void downLoadTemplate(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        // 获取空数据
        List<DtoImportEnterprise> emptyList = getNullIns();
        //endregion
        //获取所有的行业类型
        List<DtoIndustryType> industryTypes = industryTypeService.findAll();
        List<String> industryNames = industryTypes.stream().map(DtoIndustryType::getIndustryName).distinct().collect(Collectors.toList());
        String[] industryNamesArr = new String[industryNames.size()];
        //region 赋值工作单并下载
        // 获取需要下载的工作单
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportEnterprise.class, emptyList);
        importUtils.selectList(workBook,8,8,industryNames.toArray(industryNamesArr));
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
        //endregion
    }

    /**
     * 获取空仪器检定数据
     *
     * @return 空list
     */
    private List<DtoImportEnterprise> getNullIns() {
        List<DtoImportEnterprise> enterpriseList = new ArrayList<>();
        DtoImportEnterprise enterprise = new DtoImportEnterprise();
        enterpriseList.add(enterprise);
        return enterpriseList;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setIndustryTypeService(IndustryTypeService industryTypeService) {
        this.industryTypeService = industryTypeService;
    }
}
