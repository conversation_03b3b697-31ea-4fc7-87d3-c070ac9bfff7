package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 送样单提交字段结构
 * <AUTHOR>
 * @version V1.0.0 2019/12/18
 * @since V100R001
 */
@Data
public class DtoRecordSubmitTemp {
    /**
     * 送样单id
     */
    private String id;

    /**
     * 送样单id集合
     */
    private List<String> ids = new ArrayList<>();

    /**
     * 提交类型
     */
    private String type;

    /**
     * 下一步操作人id
     */
    private String nextPersonId = UUIDHelper.GUID_EMPTY;

    /**
     * 下一步操作人
     */
    private String nextPerson = "";

    /**
     * 意见
     */
    private String opinion;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 接样人id
     */
    private String recipientId;

    /**
     * 是否编制报告
     */
    private Boolean isReport;

    /**
     * 现场数据
     */
    private List<DtoAnalyseDataProperty> analyseDataPropertyList = new ArrayList<>();

    /**
     * 将单个的数据对象放入到集合里面
     *
     * @param ids 数据对象ids
     */
    public void setIds(List<String> ids) {
        if (StringUtils.isNotNullAndEmpty(this.getId())) {
            if (!this.ids.contains(this.getId())) {
                ids.add(this.getId());
            }
        }
        this.ids = ids;
    }

    /**
     * 将单个的数据对象放入到集合里面
     */
    public List<String> getIds() {
        if (StringUtils.isNotNullAndEmpty(this.getId())) {
            if (!this.ids.contains(this.getId())) {
                ids.add(this.getId());
            }
        }
        return ids;
    }
}
