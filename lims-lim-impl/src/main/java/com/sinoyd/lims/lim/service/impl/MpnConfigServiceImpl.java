package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoMpnConfig;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.MpnConfigDetailsRepository;
import com.sinoyd.lims.lim.repository.lims.MpnConfigRepository;
import com.sinoyd.lims.lim.service.MpnConfigDetailsService;
import com.sinoyd.lims.lim.service.MpnConfigService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Mnp配置接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Service
public class MpnConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoMpnConfig, String, MpnConfigRepository> implements MpnConfigService {

    private TestService testService;

    private SampleTypeRepository sampleTypeRepository;

    private MpnConfigDetailsService mpnConfigDetailsService;

    @Override
    public void findByPage(PageBean<DtoMpnConfig> page, BaseCriteria criteria) {
        page.setEntityName("DtoMpnConfig m, DtoTest t");
        page.setSelect("select m");
        super.findByPage(page, criteria);
        List<DtoMpnConfig> data = page.getData();
        List<String> testIds = data.stream().map(DtoMpnConfig::getTestId).collect(Collectors.toList());
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll();
        for (DtoMpnConfig mpnConfig : data) {
            testList.stream().filter(p -> p.getId().equals(mpnConfig.getTestId())).findFirst().ifPresent(test -> {
                mpnConfig.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                mpnConfig.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                mpnConfig.setRedCountryStandard(test.getRedCountryStandard());
                sampleTypeList.stream().filter(p -> p.getId().equals(test.getSampleTypeId())).findFirst().ifPresent(sampleType -> {
                    mpnConfig.setSampleTypeName(sampleType.getTypeName());
                });
            });
        }
    }


    @Override
    @Transactional
    public DtoMpnConfig save(DtoMpnConfig entity) {
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoMpnConfig update(DtoMpnConfig entity) {
        DtoMpnConfig oldConfig = repository.findOne(entity.getId());
        // 检查参数值是否被修改
        boolean flag = !Objects.equals(entity.getParam1Id(), oldConfig.getParam1Id()) ||
                !Objects.equals(entity.getParam2Id(), oldConfig.getParam2Id()) ||
                !Objects.equals(entity.getParam3Id(), oldConfig.getParam3Id()) ||
                !Objects.equals(entity.getResultParamId(), oldConfig.getResultParamId());
        if (flag) {
            // 删除配置数据
            mpnConfigDetailsService.deleteByMpnConfigIdIn(Collections.singletonList(entity.getId()));
        }
        return super.update(entity);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        // 删除配置数据
        mpnConfigDetailsService.deleteByMpnConfigIdIn((List<String>) ids);
        return super.logicDeleteById(ids);
    }

    @Autowired
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setMpnConfigDetailsService(MpnConfigDetailsService mpnConfigDetailsService) {
        this.mpnConfigDetailsService = mpnConfigDetailsService;
    }
}
