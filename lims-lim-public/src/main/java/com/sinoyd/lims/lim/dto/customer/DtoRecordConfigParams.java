package com.sinoyd.lims.lim.dto.customer;


import lombok.Data;

/**
 *  记录单参数
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
@Data
public class DtoRecordConfigParams {

    /**
     * 主键id
     */
    private String id;

    /**
     * 记录单id
     */
    private String recordId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 父级id
     */
    private String parentId;

    /**
     * 参数名称
     */
    private String paramsName;

    /**
     * 别名
     */
    private String alias;

    /**
     * 量纲
     */
    private String dimension;

    /**
     * 量纲id
     */
    private String dimensionId;


    /**
     * 控件类型
     */
    private Integer defaultControl;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 有效位数
     */
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    private Integer mostDecimal;

    /**
     * 排序号
     */
    private Integer orderNum;


    /**
     * 公式
     */
    private String formula;

    /**
     * 公式id
     */
    private String formulaId;

    /**
     * 是否启用公式
     */
    private Boolean isEnabled = false;

    /**
     * 是否必填
     */
    private Boolean isRequired = false;

    /**
     * 数据源
     */
    private String dataSource;

    /**
     * 构造函数修改 RecordConfigParamsConfigServiceImpl下的方法 findTestParamsByTestIds 要统一纠正
     *
     * @param id               主键id
     * @param recordId         记录单id
     * @param testId           测试项目id
     * @param parentId         主父对象id
     * @param alias            别名
     * @param dimension        量纲
     * @param dimensionId      量纲id
     * @param defaultValue     默认值
     * @param defaultControl   控件类型
     * @param mostSignificance 有效位数
     * @param mostDecimal      小数位数
     * @param orderNum         排序号
     */
    public DtoRecordConfigParams(String id,
                                 String recordId,
                                 String testId,
                                 String parentId,
                                 String alias, String dimension,
                                 String dimensionId, String defaultValue,
                                 Integer defaultControl, Integer mostSignificance,
                                 Integer mostDecimal, Integer orderNum,
                                 Boolean isRequired,
                                 String dataSource
    ) {

        this.setId(id);
        this.setRecordId(recordId);
        this.setTestId(testId);
        this.setParentId(parentId);
        this.setAlias(alias);
        this.setDimension(dimension);
        this.setDimensionId(dimensionId);
        this.setDefaultValue(defaultValue);
        this.setDefaultControl(defaultControl);
        this.setMostSignificance(mostSignificance);
        this.setMostDecimal(mostDecimal);
        this.setOrderNum(orderNum);
        this.setIsRequired(isRequired);
        this.setDataSource(dataSource);
    }


    /**
     * 默认的构造函数
     */
    public DtoRecordConfigParams() {
    }

}
