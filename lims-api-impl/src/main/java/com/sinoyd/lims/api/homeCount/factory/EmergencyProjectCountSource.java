package com.sinoyd.lims.api.homeCount.factory;

import com.sinoyd.lims.api.constant.IHomeConstant;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * 应急监测代办数字
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Component(IHomeConstant.ProjectCountSourceKey.EMERGENCY_PROJECT)
public class EmergencyProjectCountSource extends AbsCountSource {

    @Override
    public long homeCount() {
        String code = "projectRegisterPage";
        String[] values = new String[]{"XC"};
        String projectTypeIds = fieldMonitoringService.getProjectTypeList(code, values)
                .stream().map(p -> p.get("id").toString()).collect(Collectors.joining(","));
        return getProjectList(2,projectTypeIds);
    }
}
