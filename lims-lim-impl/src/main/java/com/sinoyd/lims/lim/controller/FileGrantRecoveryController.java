package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.FileGrantRecoveryCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoFileGrantRecovery;
import com.sinoyd.lims.lim.service.FileGrantRecoveryService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件发放与回收
 * <AUTHOR> 修改：xuxb
 * @version V1.0.0 2019/3/6
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/fileGrantRecovery")
@Validated
public class FileGrantRecoveryController extends BaseJpaController<DtoFileGrantRecovery, String, FileGrantRecoveryService> {


    /**
     * 根据id获取信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取信息", notes = "根据id获取信息")
    @GetMapping("/{id}")
    public RestResponse<DtoFileGrantRecovery> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoFileGrantRecovery> restResp = new RestResponse<>();
        DtoFileGrantRecovery entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询信息
     *
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询信息", notes = "分页动态条件查询信息")
    @GetMapping
    public RestResponse<List<DtoFileGrantRecovery>> findByPage(FileGrantRecoveryCriteria criteria) {

        RestResponse<List<DtoFileGrantRecovery>> restResp = new RestResponse<>();

        PageBean<DtoFileGrantRecovery> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增信息
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增信息", notes = "新增信息")
    @PostMapping
    public RestResponse<DtoFileGrantRecovery> save(@Validated @RequestBody DtoFileGrantRecovery entity) {

        RestResponse<DtoFileGrantRecovery> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoFileGrantRecovery data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改信息
     *
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改信息", notes = "修改信息")
    @PutMapping
    public RestResponse<DtoFileGrantRecovery> update(@Validated @RequestBody DtoFileGrantRecovery entity) {

        RestResponse<DtoFileGrantRecovery> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoFileGrantRecovery data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 刪除信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "刪除信息", notes = "刪除信息")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        service.delete(id);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 批量删除信息
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除信息", notes = "批量删除信息")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}