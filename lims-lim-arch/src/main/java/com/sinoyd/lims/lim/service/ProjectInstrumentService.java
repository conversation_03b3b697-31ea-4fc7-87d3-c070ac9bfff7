package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails;

import java.util.List;


/**
 * ProjectInstrument操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
public interface ProjectInstrumentService extends IBaseJpaService<DtoProjectInstrument, String> {

    /**
     * 按主键列表删除仪器出入库记录
     *
     * @param ids 主键列表
     * @return 删除数量
     */
    int deleteByIds(List<String> ids);

    /**
     * 仪器入库
     *
     * @param projectInstrumentDetails 仪器出入库记录明细对象
     */
    int instrumentIn(DtoProjectInstrumentDetails projectInstrumentDetails);


    /**
     * 仪器取消入库
     *
     * @param projectInstrumentDetails 仪器出入库记录明细对象
     */
    int instrumentCancelIn(DtoProjectInstrumentDetails projectInstrumentDetails);

    /**
     * 新增/修改仪器出入库记录时获取待选仪器列表
     *
     * @param page     分页对象
     * @param criteria 查询条件对象
     */
    void getOutInstrument(PageBean<DtoInstrument> page, BaseCriteria criteria);

    /**
     * 仪器入库时获取待选仪器列表
     *
     * @param page     分页对象
     * @param criteria 查询条件对象
     */
    void getInInstrument(PageBean<DtoInstrument> page, BaseCriteria criteria);


}