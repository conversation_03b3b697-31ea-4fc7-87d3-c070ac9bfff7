package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.factory.quality.QualityBlank;
import com.sinoyd.base.factory.quality.QualityMark;
import com.sinoyd.base.factory.quality.QualityStandard;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.TestQCRemindConfig2TestService;
import com.sinoyd.lims.pro.criteria.AnalysisQualityStatisticsCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp;
import com.sinoyd.lims.pro.dto.customer.DtoQualityControlDetail;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.AnalysisQualityStatisticsService;
import com.sinoyd.lims.pro.service.QCResultEvaluationService;
import com.sinoyd.lims.pro.service.QualityControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分析质量统计实现
 *
 * <AUTHOR>
 * @version V1.0.0 2020/02/17
 * @since V100R001
 */
@Service
@SuppressWarnings({"rawtypes", "unchecked"})
@Slf4j
public class AnalysisQualityStatisticsServiceImpl implements AnalysisQualityStatisticsService {

    private CommonRepository commonRepository;

    private QualityControlService qualityControlService;

    private TestQCRemindConfig2TestService testQCRemindConfig2TestService;

    private SampleTypeService sampleTypeService;

    private QCResultEvaluationService qcResultEvaluationService;

    private QualityControlEvaluateRepository qualityControlEvaluateRepository;

    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;

    private AnalyseDataRepository analyseDataRepository;

    private SampleRepository sampleRepository;

    private QualityControlRepository qualityControlRepository;

    @Override
    public void  findQualityControlDetailGroupByAnalyseDate(PageBean<DtoQualityControlDetail> pb, BaseCriteria baseCriteria) {
        //获取分页的分组数据key
        List<DtoAnalyseDataTemp> groupKeyList = this.findGroupKeyList(pb, baseCriteria);
        if (groupKeyList.size() == 0) {
            return;
        }
        //按照分页的数据集重新缩小查询条件
        this.setCriteria(groupKeyList, baseCriteria);
        //获取原样数据
        List<DtoAnalyseDataTemp> yDataList = this.findYYAnalyseDataByCriteria(baseCriteria);
        //获取质控数据
        List<DtoAnalyseDataTemp> qcAnalyseDataList = new ArrayList<>();
        //获取查询条件内所有原因的送样单id
        List<String> yyReceiveIds = yDataList.stream().map(DtoAnalyseDataTemp::getReceiveId).distinct().collect(Collectors.toList());
        Set<DtoQualityControl> qcSet = new HashSet<>();
        //处理关联的样品质控数据
        this.setZKAnalyseDataByCriteria(baseCriteria, qcAnalyseDataList, qcSet);
        //处理工作单内无样品关联的质控数据
        this.setNonYYAnalyseDataByCriteria(baseCriteria, qcAnalyseDataList, qcSet);
        //处理质控任务数据
        this.setBlindAnalyseDataByCriteria(baseCriteria, qcAnalyseDataList);
        //处理现场数据录入质控
        Map<String, List<DtoAnalyseDataTemp>> receiveId2QcAnaData = this.findLocalZKAnaDataMap(baseCriteria, yyReceiveIds, qcSet);

        List<String> testIds = qcAnalyseDataList.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList());
        //获取质控评价
        List<String> qcAnalyseIdList = qcAnalyseDataList.stream().map(DtoAnalyseDataTemp::getId).distinct().collect(Collectors.toList());
        List<DtoQualityControlEvaluate> qualityControlEvaluateList = StringUtil.isNotEmpty(qcAnalyseIdList)
                ? qualityControlEvaluateRepository.findByObjectIdIn(qcAnalyseIdList) : new ArrayList<>();
        List<String> sampleTypeIds = yDataList.stream().map(DtoAnalyseDataTemp::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        List<DtoTestQCRemindTemp> remindList = testIds.size() > 0 ? testQCRemindConfig2TestService.findByTestIds(testIds) : new ArrayList<>();
        //获取密码样质控任务的评价结果
        List<String> projectIds = qcAnalyseDataList.stream().map(DtoAnalyseDataTemp::getProjectId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<DtoQCResultEvaluation> qmResultEvaluationList = qcResultEvaluationService.findByProjectIds(projectIds);
        for (DtoQCResultEvaluation qmResult : qmResultEvaluationList) {
            DtoQualityControlEvaluate qcResult = new DtoQualityControlEvaluate();
            qcResult.setObjectId(qmResult.getProjectId());
            qcResult.setIsPass("合格".equals(qmResult.getEvaluationResult()));
            qualityControlEvaluateList.add(qcResult);
        }

        //将list转为map
        Map<String, DtoTestQCRemindTemp> remindMap = remindList.stream().collect(Collectors.toMap(p -> String.format("%d-%d-%s",
                p.getQcGrade(), p.getQcType(), p.getTestId()), remind -> remind));

        List<DtoQualityControlDetail> detailList = new ArrayList<>();
        AnalysisQualityStatisticsCriteria criteria = (AnalysisQualityStatisticsCriteria) baseCriteria;
        if (criteria.getIsProjectGroup()) {
            // 按照项目分组展示
            for (String projectId : projectIds) {
                this.fullData(groupKeyList, projectId, yDataList, sampleTypes, qcAnalyseDataList, receiveId2QcAnaData, remindMap, qualityControlEvaluateList, detailList, criteria.getIsAnalyzeItemMerge());
            }
        } else {
            this.fullData(groupKeyList, null, yDataList, sampleTypes, qcAnalyseDataList, receiveId2QcAnaData, remindMap, qualityControlEvaluateList, detailList, criteria.getIsAnalyzeItemMerge());
        }
        pb.setData(detailList);
    }

    /**
     * 修改Criteria
     *
     * @param groupKeys    分组的数据集合
     * @param baseCriteria 查询条件
     */
    private void setCriteria(List<DtoAnalyseDataTemp> groupKeys, BaseCriteria baseCriteria) {
        AnalysisQualityStatisticsCriteria criteria = (AnalysisQualityStatisticsCriteria) baseCriteria;
        List<String> testIds = !criteria.getIsAnalyzeItemMerge() ?
                groupKeys.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList()) :
                groupKeys.stream().flatMap(p -> p.getTestIds().stream()).distinct().collect(Collectors.toList());
        criteria.setTestIds(testIds);

        List<String> sampleTypeIds = !criteria.getIsAnalyzeItemMerge() ?
                groupKeys.stream().map(DtoAnalyseDataTemp::getSampleTypeId).distinct().collect(Collectors.toList()) :
                groupKeys.stream().flatMap(p -> p.getSampleTypeIds().stream()).distinct().collect(Collectors.toList());
        if (sampleTypeIds.size() > 1) {
            criteria.setSampleTypeIds(sampleTypeIds);
            criteria.setSampleTypeId(UUIDHelper.GUID_EMPTY);
        } else {
            criteria.setSampleTypeIds(new ArrayList<>());
            criteria.setSampleTypeId(sampleTypeIds.get(0));
        }
    }

    /**
     * 按条件查询原样的数据
     *
     * @param baseCriteria 查询
     */
    private List<DtoAnalyseDataTemp> findGroupKeyList(PageBean<DtoQualityControlDetail> pb, BaseCriteria baseCriteria) {
        StringBuilder stringBuilder = new StringBuilder("select distinct a.sampleTypeId,a.testId,a.redAnalyzeItemName,a.redAnalyzeMethodName, a.redCountryStandard");
        stringBuilder.append(" from DtoAnalyseData a,DtoSample s,DtoProject p ");
        stringBuilder.append(" where a.sampleId=s.id and s.projectId=p.id");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and p.isDeleted = 0");
        stringBuilder.append(" and s.receiveId <> :receiveId");
        stringBuilder.append(baseCriteria.getCondition());
        Map<String, Object> values = baseCriteria.getValues();
        if (!values.containsKey("receiveId")) {
            values.put("receiveId", UUIDHelper.GUID_EMPTY);
        }
        setOrgId(stringBuilder, values);
        List groupList = commonRepository.find(stringBuilder.toString(), values);
        List<DtoAnalyseDataTemp> tempList = new ArrayList<>();
        for (Object[] objArray : (Iterable<Object[]>) groupList) {
            // 按查询顺序下标一一获取
            DtoAnalyseDataTemp analyseData = new DtoAnalyseDataTemp();
            analyseData.setSampleTypeId((String) objArray[0]);
            analyseData.setTestId((String) objArray[1]);
            analyseData.setRedAnalyzeItemName((String) objArray[2]);
            analyseData.setRedAnalyzeMethodName((String) objArray[3]);
            analyseData.setRedCountryStandard((String) objArray[4]);
            tempList.add(analyseData);
        }
        // 按照分析项目分页的话
        AnalysisQualityStatisticsCriteria criteria = (AnalysisQualityStatisticsCriteria) baseCriteria;
        if (criteria.getIsAnalyzeItemMerge()) {
            Map<String, List<DtoAnalyseDataTemp>> analyseDataGroupMap = tempList.stream().collect(Collectors.groupingBy(DtoAnalyseDataTemp::getRedAnalyzeItemName));
            // 根据分析项目去分组后重新赋值
            tempList = new ArrayList<>();
            for (Map.Entry<String, List<DtoAnalyseDataTemp>> entry : analyseDataGroupMap.entrySet()) {
                DtoAnalyseDataTemp analyseData = new DtoAnalyseDataTemp();
                analyseData.setRedAnalyzeItemName(entry.getKey());
                List<DtoAnalyseDataTemp> entryValue = entry.getValue();
                analyseData.setSampleTypeIds(entryValue.stream().map(DtoAnalyseDataTemp::getSampleTypeId).distinct().collect(Collectors.toList()));
                analyseData.setTestIds(entryValue.stream().map(DtoAnalyseDataTemp::getTestId).distinct().collect(Collectors.toList()));
                tempList.add(analyseData);
            }
            tempList.sort(Comparator.comparing(DtoAnalyseDataTemp::getRedAnalyzeItemName));
        } else {
            tempList.sort(Comparator.comparing(DtoAnalyseDataTemp::getSampleTypeId).thenComparing(DtoAnalyseDataTemp::getRedAnalyzeItemName));
        }
        pb.setRowsCount(tempList.size());
        return tempList.stream().skip((long) pb.getRowsPerPage() * (pb.getPageNo() - 1)).limit(pb.getRowsPerPage()).collect(Collectors.toList());
    }

    /**
     * 按条件查询原样的数据
     *
     * @param baseCriteria 查询
     */
    private List<DtoAnalyseDataTemp> findYYAnalyseDataByCriteria(BaseCriteria baseCriteria) {

        List<DtoAnalyseDataTemp> yyList = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("select a.id,a.analyzeTime,a.sampleId,a.testId,a.redAnalyzeItemName,a.qcId,a.qcGrade," +
                "a.qcType,a.testValue,a.testOrignValue,a.testValueD,a.testValueDstr,a.sampleTypeId, " +
                "a.gatherCode, a.qcInfo, s.blindType, a.isQm, p.id, a.workSheetFolderId, s.receiveId");
        stringBuilder.append(" from DtoAnalyseData a,DtoSample s,DtoProject p ");
        stringBuilder.append(" where a.sampleId=s.id and s.projectId=p.id");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and p.isDeleted = 0");
        stringBuilder.append(" and s.receiveId <> :receiveId");
        stringBuilder.append(baseCriteria.getCondition());
        Map<String, Object> values = baseCriteria.getValues();
        if (!values.containsKey("receiveId")) {
            values.put("receiveId", UUIDHelper.GUID_EMPTY);
        }
        setOrgId(stringBuilder, values);

        List dataList = commonRepository.find(stringBuilder.toString(), values);
        // 循环迭代获取JPQL中查询返回的属性
        for (Object[] objArray : (Iterable<Object[]>) dataList) {
            // 按查询顺序下标一一获取
            DtoAnalyseDataTemp analyseData = new DtoAnalyseDataTemp((String) objArray[0], (Date) objArray[1], (String) objArray[2], (String) objArray[3], (String) objArray[4],
                    (String) objArray[5], (Integer) objArray[6], (Integer) objArray[7], (String) objArray[8], (String) objArray[9], (BigDecimal) objArray[10],
                    (String) objArray[11], (String) objArray[12], UUIDHelper.GUID_EMPTY, (String) objArray[13], (String) objArray[14], (Integer) objArray[15],
                    (Boolean) objArray[16], (String) objArray[17], (String) objArray[18]);
            analyseData.setReceiveId((String) objArray[19]);
            yyList.add(analyseData);
        }
        return yyList;
    }

    /**
     * 按条件查询质控的数据
     *
     * @param baseCriteria 查询条件
     */
    private void setZKAnalyseDataByCriteria(BaseCriteria baseCriteria, List<DtoAnalyseDataTemp> qcAnalyseDataList, Set<DtoQualityControl> qcSet) {

        StringBuilder stringBuilder = new StringBuilder("select a.id,a.analyzeTime,a.sampleId,a.testId,a.redAnalyzeItemName,a.qcId,a.qcGrade," +
                "a.qcType,a.testValue,a.testOrignValue,a.testValueD,a.testValueDstr,a.sampleTypeId,qc.associateSampleId,qc, a.gatherCode, a.qcInfo," +
                "s.blindType,a.isQm,p.id,a.workSheetFolderId");
        stringBuilder.append(" from DtoAnalyseData a,DtoQualityControl qc,DtoSample s,DtoProject p ");
        stringBuilder.append(" where a.qcId = qc.id");
        stringBuilder.append(" and qc.associateSampleId = s.id");
        stringBuilder.append(" and s.projectId = p.id");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and s.isDeleted = 0");
        stringBuilder.append(" and p.isDeleted = 0");
        stringBuilder.append(" and qc.isDeleted = 0");
        stringBuilder.append(baseCriteria.getCondition());
        Map<String, Object> values = baseCriteria.getValues();
        setOrgId(stringBuilder, values);
        List dataList = commonRepository.find(stringBuilder.toString(), values);
        // 循环迭代获取JPQL中查询返回的属性
        for (Object[] objArray : (Iterable<Object[]>) dataList) {
            // 按查询顺序下标一一获取
            DtoAnalyseDataTemp analyseData = new DtoAnalyseDataTemp((String) objArray[0], (Date) objArray[1], (String) objArray[2], (String) objArray[3], (String) objArray[4],
                    (String) objArray[5], (Integer) objArray[6], (Integer) objArray[7], (String) objArray[8], (String) objArray[9], (BigDecimal) objArray[10],
                    (String) objArray[11], (String) objArray[12], (String) objArray[13], (String) objArray[15], (String) objArray[16], (Integer) objArray[17],
                    (Boolean) objArray[18], (String) objArray[19], (String) objArray[20]);

            qcAnalyseDataList.add(analyseData);
            DtoQualityControl qualityControl = (DtoQualityControl) objArray[14];
            if (qualityControl.getQcType().equals(new QualityMark().qcTypeValue())) {
                qcSet.add(qualityControl);
            }
        }
        //上面的sql没有考虑质控样被删除的情况,相关数据要排除
        List<String> sampleIds = qcAnalyseDataList.stream().map(DtoAnalyseDataTemp::getSampleId).collect(Collectors.toList());
        if(StringUtil.isNotEmpty(sampleIds)){
            List<String> deletedSampleIds = sampleRepository.findAll(sampleIds).stream().filter(v->Boolean.TRUE.equals(v.getIsDeleted()))
                    .map(DtoSample::getId).collect(Collectors.toList());
            qcAnalyseDataList.removeIf(v->deletedSampleIds.contains(v.getSampleId()));
        }
    }

    /**
     * 按条件查询现场数据录入质控的数据
     *
     * @param baseCriteria 查询条件
     * @param receiveIds   送样单id集合
     * @param qcSet        质控数据集合
     */
    private Map<String, List<DtoAnalyseDataTemp>> findLocalZKAnaDataMap(BaseCriteria baseCriteria, List<String> receiveIds, Set<DtoQualityControl> qcSet) {
        AnalysisQualityStatisticsCriteria anaStatisticsCriteria = (AnalysisQualityStatisticsCriteria) baseCriteria;
        Map<String, List<DtoAnalyseDataTemp>> receiveId2QcListMap = new HashMap<>();
        List<DtoReceiveSubSampleRecord> subRecords = receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIds);
        //过滤出现场领养单
        List<DtoReceiveSubSampleRecord> localSubRecords = subRecords.stream()
                .filter(p -> (EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue() & p.getSubStatus()) != 0)
                .collect(Collectors.toList());
        Map<String, List<DtoReceiveSubSampleRecord>> localSubRecordGroup = localSubRecords
                .stream().collect(Collectors.groupingBy(DtoReceiveSubSampleRecord::getReceiveId));
        //获取领养单关联的样品
        List<String> localSubRecordIds = localSubRecords.stream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
        List<DtoReceiveSubSampleRecord2Sample> sub2SampleList = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordIdIn(localSubRecordIds);
        //获取所有的样品数据
        List<String> sampleIds = sub2SampleList.stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sampleIds)) {
            List<DtoSample> sampleList = sampleRepository.findByIds(sampleIds);
            Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, p -> p));
            //过滤掉原样数据
            List<DtoSample> qcSamples = sampleList.stream().filter(p -> !EnumPRO.EnumSampleCategory.原样.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
            List<String> qcSampleIds = qcSamples.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
            //查询到所有的分析数据
            List<DtoAnalyseData> anaDataList = analyseDataRepository.findBySampleIdInAndTestIdInAndIsDeletedFalse(qcSampleIds, anaStatisticsCriteria.getTestIds());
            //获取所有的质控数据
            List<String> qcIds = qcSamples.stream().map(DtoSample::getQcId).filter(qcId -> !UUIDHelper.GUID_EMPTY.equals(qcId)).distinct().collect(Collectors.toList());
            List<DtoQualityControl> qualityControlList = StringUtil.isNotEmpty(qcIds) ? qualityControlRepository.findByIds(qcIds) : new ArrayList<>();
            qcSet.addAll(qualityControlList);
            //处理质控数据
            for (String receiveId : receiveIds) {
                List<DtoReceiveSubSampleRecord> subRecordsOfReceiveId = localSubRecordGroup.getOrDefault(receiveId, new ArrayList<>());
                List<String> subOfReceiveIds = subRecordsOfReceiveId.stream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
                List<String> sampleIdsOfReceive = sub2SampleList.stream()
                        .filter(p -> subOfReceiveIds.contains(p.getReceiveSubSampleRecordId()))
                        .map(DtoReceiveSubSampleRecord2Sample::getSampleId)
                        .distinct().collect(Collectors.toList());
                List<DtoAnalyseData> anaDataListOfReceive = anaDataList.stream().filter(p -> sampleIdsOfReceive.contains(p.getSampleId())).collect(Collectors.toList());
                List<DtoAnalyseDataTemp> anaQCTempList = new ArrayList<>();
                for (DtoAnalyseData anaData : anaDataListOfReceive) {
                    DtoSample sample = sampleMap.get(anaData.getSampleId());
                    if (sample != null) {
                        DtoAnalyseDataTemp analyseDataTemp = new DtoAnalyseDataTemp(anaData.getId(), anaData.getAnalyzeTime(), anaData.getSampleId(), anaData.getTestId(), anaData.getRedAnalyzeItemName(),
                                anaData.getQcId(), anaData.getQcGrade(), anaData.getQcType(), anaData.getTestValue(), anaData.getTestOrignValue(),
                                anaData.getTestValueD(), anaData.getTestValueDstr(), sample.getSampleTypeId(), sample.getAssociateSampleId(),
                                anaData.getGatherCode(), anaData.getQcInfo(), sample.getBlindType(), anaData.getIsQm(), sample.getProjectId(),
                                anaData.getWorkSheetFolderId());
                        analyseDataTemp.setReceiveId(receiveId);
                        anaQCTempList.add(analyseDataTemp);
                    }
                }
                receiveId2QcListMap.put(receiveId, anaQCTempList);
            }
        }
        return receiveId2QcListMap;
    }

    /**
     * 设置盲样考核质控任务下的密码样数据
     *
     * @param baseCriteria      查询条件
     * @param qcAnalyseDataList 质控数据集合
     */
    private void setBlindAnalyseDataByCriteria(BaseCriteria baseCriteria, List<DtoAnalyseDataTemp> qcAnalyseDataList) {
        StringBuilder stringBuilder = new StringBuilder("select a.id,a.analyzeTime,a.sampleId,a.testId,a.redAnalyzeItemName,a.qcId,a.qcGrade," +
                "a.qcType,a.testValue,a.testOrignValue,a.testValueD,a.testValueDstr,a.sampleTypeId,s.associateSampleId, a.gatherCode, a.qcInfo," +
                " s.blindType, a.isQm, p.id, a.workSheetFolderId");
        stringBuilder.append(" from DtoAnalyseData a,DtoSample s,DtoProject p");
        stringBuilder.append(" where a.sampleId = s.id");
        stringBuilder.append(" and s.projectId = p.id");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and a.isQm=1");
        stringBuilder.append(" and s.blindType in :blindTypes");
        stringBuilder.append(baseCriteria.getCondition());
        Map<String, Object> values = baseCriteria.getValues();
        if (!values.containsKey("blindTypes")) {
            List<Integer> types = new ArrayList<>();
            types.add(EnumPRO.EnumSampleBlindType.密码平行.getValue());
            types.add(EnumPRO.EnumSampleBlindType.密码加标.getValue());
            values.put("blindTypes", types);
        }
        setOrgId(stringBuilder, values);
        List dataList = commonRepository.find(stringBuilder.toString(), values);
        // 循环迭代获取JPQL中查询返回的属性
        for (Object[] objArray : (Iterable<Object[]>) dataList) {
            // 按查询顺序下标一一获取
            DtoAnalyseDataTemp analyseData = new DtoAnalyseDataTemp((String) objArray[0], (Date) objArray[1], (String) objArray[2], (String) objArray[3], (String) objArray[4],
                    (String) objArray[5], (Integer) objArray[6], (Integer) objArray[7], (String) objArray[8], (String) objArray[9], (BigDecimal) objArray[10],
                    (String) objArray[11], (String) objArray[12], (String) objArray[13], (String) objArray[14], (String) objArray[15], (Integer) objArray[16],
                    (Boolean) objArray[17], (String) objArray[18], (String) objArray[19]);
            qcAnalyseDataList.add(analyseData);
        }
    }

    /**
     * 找到相应的没有原样的质控数据
     *
     * @param baseCriteria      查询条件
     * @param qcAnalyseDataList 源数据
     * @param qcSet             质控数据
     */
    private void setNonYYAnalyseDataByCriteria(BaseCriteria baseCriteria, List<DtoAnalyseDataTemp> qcAnalyseDataList, Set<DtoQualityControl> qcSet) {

        StringBuilder stringBuilder = new StringBuilder("select a.id,a.analyzeTime,a.sampleId,a.testId,a.redAnalyzeItemName,a.qcId,a.qcGrade," +
                "a.qcType,a.testValue,a.testOrignValue,a.testValueD,a.testValueDstr,a.sampleTypeId,qc, a.gatherCode, a.qcInfo," +
                "a.isQm,p.id,a.workSheetFolderId");
        stringBuilder.append(" from DtoAnalyseData a,DtoQualityControl qc,DtoProject2WorkSheetFolder p2w,DtoProject p ");
        stringBuilder.append(" where a.workSheetFolderId = p2w.workSheetFolderId");
        stringBuilder.append(" and a.qcId = qc.id");
        stringBuilder.append(" and p2w.projectId = p.id");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and qc.isDeleted = 0");
        stringBuilder.append(" and qc.qcGrade = :qcGrade");
        stringBuilder.append(" and qc.qcType in :qcTypes");
        stringBuilder.append(" and p.isDeleted = 0");
        stringBuilder.append(baseCriteria.getCondition());
        Map<String, Object> values = baseCriteria.getValues();
        if (!values.containsKey("qcGrade")) {
            values.put("qcGrade", EnumLIM.EnumQCGrade.内部质控.getValue());
        }
        if (!values.containsKey("qcTypes")) {
            List<Integer> types = new ArrayList<>();
            types.add(new QualityBlank().qcTypeValue());
            types.add(new QualityStandard().qcTypeValue());
            values.put("qcTypes", types);
        }
        setOrgId(stringBuilder, values);
        List dataList = commonRepository.find(stringBuilder.toString(), values);
        Map<String, DtoAnalyseDataTemp> map = new HashMap<>();
        for (Object[] objArray : (Iterable<Object[]>) dataList) {
            // 按查询顺序下标一一获取
            DtoAnalyseDataTemp analyseData = new DtoAnalyseDataTemp((String) objArray[0], (Date) objArray[1], (String) objArray[2], (String) objArray[3], (String) objArray[4],
                    (String) objArray[5], (Integer) objArray[6], (Integer) objArray[7], (String) objArray[8], (String) objArray[9], (BigDecimal) objArray[10],
                    (String) objArray[11], (String) objArray[12], UUIDHelper.GUID_EMPTY, (String) objArray[14], (String) objArray[15],
                    EnumPRO.EnumSampleBlindType.非盲样.getValue(), (Boolean) objArray[16], (String) objArray[17], (String) objArray[18]);
            if (!map.containsKey(analyseData.getId())) {
                qcAnalyseDataList.add(analyseData);
                map.put(analyseData.getId(), analyseData);
            }
            DtoQualityControl qualityControl = (DtoQualityControl) objArray[13];
            if (qualityControl.getQcType().equals(new QualityStandard().qcTypeValue())) {
                qcSet.add(qualityControl);
            }
        }
    }

    /**
     * 分析质控数据填充
     *
     * @param groupKeyList               分析数据分组集合
     * @param projectId                  项目id
     * @param yDataList                  原样数据
     * @param sampleTypes                检测类型集合
     * @param qcAnalyseDataList          质控数据集合
     * @param receiveId2QcAnaData        现场质控数据集合
     * @param remindMap                  质控比例数据Map
     * @param qualityControlEvaluateList 质控评价数据
     * @param detailList                 分析质控数据返回值集合
     */
    private void fullData(List<DtoAnalyseDataTemp> groupKeyList,
                          String projectId,
                          List<DtoAnalyseDataTemp> yDataList,
                          List<DtoSampleType> sampleTypes,
                          List<DtoAnalyseDataTemp> qcAnalyseDataList,
                          Map<String, List<DtoAnalyseDataTemp>> receiveId2QcAnaData,
                          Map<String, DtoTestQCRemindTemp> remindMap,
                          List<DtoQualityControlEvaluate> qualityControlEvaluateList,
                          List<DtoQualityControlDetail> detailList,
                          Boolean isAnalyzeItemMerge) {
        for (DtoAnalyseDataTemp gk : groupKeyList) {
            if (isAnalyzeItemMerge && StringUtil.isEmpty(projectId)) {
                List<DtoAnalyseDataTemp> dataList = yDataList.stream()
                        .filter(p -> gk.getSampleTypeIds().contains(p.getSampleTypeId())
                                && gk.getTestIds().contains(p.getTestId())).collect(Collectors.toList());
                if (StringUtil.isEmpty(dataList) && StringUtil.isNotEmpty(projectId)) {
                    continue;
                }
                DtoQualityControlDetail detail = new DtoQualityControlDetail();
                ;
                detail.setAnalyseDate("");
                detail.setRedAnalyzeItemName(gk.getRedAnalyzeItemName());

                List<String> sampleIds = dataList.stream().map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
                detail.setAllSample(sampleIds.size());
                //质控数据（有原样的质控及无原样的质控）
                List<String> workSheetFolderIds = dataList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getWorkSheetFolderId())
                        && !p.getWorkSheetFolderId().equals(UUIDHelper.GUID_EMPTY)).map(DtoAnalyseDataTemp::getWorkSheetFolderId).distinct().collect(Collectors.toList());
                List<String> receiveIds = dataList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getReceiveId())
                        && !p.getReceiveId().equals(UUIDHelper.GUID_EMPTY)).map(DtoAnalyseDataTemp::getReceiveId).distinct().collect(Collectors.toList());
                List<DtoAnalyseDataTemp> thisQcDataList = qcAnalyseDataList.stream().filter(p -> (sampleIds.contains(p.getAssociateSampleId())
                                || (workSheetFolderIds.contains(p.getWorkSheetFolderId()) && gk.getSampleTypeIds().contains(p.getSampleTypeId())))
                                && gk.getTestIds().contains(p.getTestId())).collect(Collectors.toList());
                for (String receiveId : receiveIds) {
                    List<String> qcDataIds = thisQcDataList.stream().map(DtoAnalyseDataTemp::getId).distinct().collect(Collectors.toList());
                    List<DtoAnalyseDataTemp> localQcDataList = receiveId2QcAnaData.getOrDefault(receiveId, new ArrayList<>()).stream()
                            .filter(p -> gk.getSampleTypeIds().contains(p.getSampleTypeId())
                                    && !qcDataIds.contains(p.getId())
                                    && gk.getTestIds().contains(p.getTestId()))
                            .collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(localQcDataList)) {
                        thisQcDataList.addAll(localQcDataList);
                    }
                }
                qualityControlService.calculateAnalysisQuality(detail, thisQcDataList, remindMap, gk.getTestId(), qualityControlEvaluateList, gk.getSampleTypeIds());
                detailList.add(detail);
            } else {
                List<DtoAnalyseDataTemp> dataList = yDataList.stream().filter(p -> p.getSampleTypeId().equals(gk.getSampleTypeId())
                                && p.getTestId().equals(gk.getTestId())
                                && (StringUtil.isEmpty(projectId) || projectId.equals(p.getProjectId()))).collect(Collectors.toList());
                if (StringUtil.isEmpty(dataList) && StringUtil.isNotEmpty(projectId)) {
                    continue;
                }
                String sampleTypeName = "";
                DtoSampleType sampleType = sampleTypes.stream().filter(p -> p.getId().equals(gk.getSampleTypeId())).findFirst().orElse(null);
                DtoQualityControlDetail detail = new DtoQualityControlDetail();
                detail.setSampleTypeId(gk.getSampleTypeId());
                detail.setTestId(gk.getTestId());
                detail.setAnalyseDate("");
                detail.setRedAnalyzeItemName(gk.getRedAnalyzeItemName());
                detail.setRedAnalyzeMethodName(gk.getRedAnalyzeMethodName());
                detail.setRedCountryStandard(gk.getRedCountryStandard());
                detail.setProjectId(projectId);

                List<String> sampleIds = dataList.stream().map(DtoAnalyseDataTemp::getSampleId).distinct().collect(Collectors.toList());
                detail.setAllSample(sampleIds.size());
                if (sampleType != null) {
                    sampleTypeName = sampleType.getTypeName();
                }
                detail.setSampleTypeName(sampleTypeName);
                //质控数据（有原样的质控及无原样的质控）
                List<String> workSheetFolderIds = dataList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getWorkSheetFolderId())
                        && !p.getWorkSheetFolderId().equals(UUIDHelper.GUID_EMPTY)).map(DtoAnalyseDataTemp::getWorkSheetFolderId).distinct().collect(Collectors.toList());
                List<String> receiveIds = dataList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getReceiveId())
                        && !p.getReceiveId().equals(UUIDHelper.GUID_EMPTY)).map(DtoAnalyseDataTemp::getReceiveId).distinct().collect(Collectors.toList());
                List<DtoAnalyseDataTemp> thisQcDataList = qcAnalyseDataList.stream().filter(p -> (sampleIds.contains(p.getAssociateSampleId())
                                || (workSheetFolderIds.contains(p.getWorkSheetFolderId()) && p.getSampleTypeId().equals(gk.getSampleTypeId())))
                                && p.getTestId().equals(gk.getTestId())).collect(Collectors.toList());
                for (String receiveId : receiveIds) {
                    List<String> qcDataIds = thisQcDataList.stream().map(DtoAnalyseDataTemp::getId).distinct().collect(Collectors.toList());
                    List<DtoAnalyseDataTemp> localQcDataList = receiveId2QcAnaData.getOrDefault(receiveId, new ArrayList<>()).stream()
                            .filter(p -> gk.getSampleTypeId().equals(p.getSampleTypeId())
                                    && !qcDataIds.contains(p.getId())
                                    && gk.getTestId().equals(p.getTestId()))
                            .collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(localQcDataList)) {
                        thisQcDataList.addAll(localQcDataList);
                    }
                }
                qualityControlService.calculateAnalysisQuality(detail, thisQcDataList, remindMap, gk.getTestId(), qualityControlEvaluateList);
                detailList.add(detail);
            }
        }
    }

    private void setOrgId(StringBuilder stringBuilder, Map<String, Object> values) {
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            if (!values.containsKey("orgId")) {
                values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
            }
        }
    }

    @Autowired
    public void setQualityControlEvaluateRepository(QualityControlEvaluateRepository qualityControlEvaluateRepository) {
        this.qualityControlEvaluateRepository = qualityControlEvaluateRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    @Lazy
    public void setQualityControlService(QualityControlService qualityControlService) {
        this.qualityControlService = qualityControlService;
    }

    @Autowired
    @Lazy
    public void setTestQCRemindConfig2TestService(TestQCRemindConfig2TestService testQCRemindConfig2TestService) {
        this.testQCRemindConfig2TestService = testQCRemindConfig2TestService;
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    @Lazy
    public void setQcResultEvaluationService(QCResultEvaluationService qcResultEvaluationService) {
        this.qcResultEvaluationService = qcResultEvaluationService;
    }

    @Autowired
    public void setReceiveSubSampleRecordRepository(ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository) {
        this.receiveSubSampleRecordRepository = receiveSubSampleRecordRepository;
    }

    @Autowired
    public void setReceiveSubSampleRecord2SampleRepository(ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository) {
        this.receiveSubSampleRecord2SampleRepository = receiveSubSampleRecord2SampleRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setQualityControlRepository(QualityControlRepository qualityControlRepository) {
        this.qualityControlRepository = qualityControlRepository;
    }
}
