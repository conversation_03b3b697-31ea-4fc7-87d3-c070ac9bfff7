package com.sinoyd.lims.monitor.service;

import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;
import java.util.Map;


/**
 * Station操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface StationService extends IBaseJpaService<DtoStation, String> {

    /**
     * 根据当前登录人过滤所属机构测站
     *
     * @return Map<String, List<DtoStation>>
     */
    Map<String, List<DtoStation>> findStation();

}