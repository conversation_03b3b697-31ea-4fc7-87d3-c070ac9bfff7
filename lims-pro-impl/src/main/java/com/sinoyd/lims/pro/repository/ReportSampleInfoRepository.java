package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoReportSampleInfo;

import java.util.List;


/**
 * 电子报告样品数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
public interface ReportSampleInfoRepository extends IBaseJpaRepository<DtoReportSampleInfo, String> {

    /**
     * 按报告id查询样品数据
     *
     * @param reportId 报告id
     * @return 返回相应的报告点位数据
     */
    List<DtoReportSampleInfo> findByReportId(String reportId);

    /**
     * 按报告点位信息id查询样品数据
     *
     * @param reportFolderInfoId 报告点位信息id
     * @return 返回相应的报告样品数据
     */
    DtoReportSampleInfo findByReportFolderInfoId(String reportFolderInfoId);
}