package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoFlowCalibration;

import java.util.List;

/**
 * FlowCalibration操作接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/14
 */
public interface FlowCalibrationService extends IBaseJpaService<DtoFlowCalibration,String> {
    /**
     * 批量删除数据行
     * @param ids 数据行标识
     * @return 数量
     */
    Integer deleteRow(List<String> ids);
}
