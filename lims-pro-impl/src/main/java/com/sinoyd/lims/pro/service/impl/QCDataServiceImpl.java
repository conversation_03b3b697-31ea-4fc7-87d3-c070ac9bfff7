package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoQCData;
import com.sinoyd.lims.pro.dto.customer.DtoQCDataConfig;
import com.sinoyd.lims.pro.dto.customer.DtoQCDataDetail;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.QCDataRepository;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.service.QCDataService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * QCData操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class QCDataServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoQCData,String,QCDataRepository> implements QCDataService {


    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private DimensionService dimensionService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private AnalyseDataRepository analyseDataRepository;

    @Override
    public void findByPage(PageBean<DtoQCData> pb, BaseCriteria qCDataCriteria) {
        pb.setEntityName("DtoQCData a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, qCDataCriteria);
    }


    @Override
    public void saveConfigQCData(DtoQCDataConfig dtoQCDataConfig) {
        StringBuilder data = new StringBuilder();
        List<DtoQCDataDetail> qcDataDetails = dtoQCDataConfig.getQcDataDetails();
        if (StringUtil.isNull(qcDataDetails) || qcDataDetails.size() == 0) {
            throw new BaseException("请选择配置数据！");
        }
        List<String> analyseDataIds = qcDataDetails.stream().map(DtoQCDataDetail::getId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(analyseDataIds) ? analyseDataRepository.findAll(analyseDataIds) : new ArrayList<>();
        qcDataDetails = qcDataDetails.stream().sorted(Comparator.comparing(DtoQCDataDetail::getAnalyzeTime)).collect(Collectors.toList());
        BigDecimal temp;
        //平均值
        BigDecimal avgValue = new BigDecimal(0);
        //最大值
        BigDecimal max = new BigDecimal(0);
        //最小值
        BigDecimal min = new BigDecimal(0);
        Integer num = 0;
        Integer length = 0;
        List<Double> x = new ArrayList<>();
        String testId = dtoQCDataConfig.getTestId();
        DtoTest dtoTest = testService.findOne(testId);
        Integer ms = dtoTest.getMostSignificance();
        Integer md = dtoTest.getMostDecimal();
        for (DtoQCDataDetail dto : qcDataDetails) {
            DtoAnalyseData analyseData = analyseDataList.stream().filter(p -> dto.getId().equals(p.getId())).findFirst().orElse(null);
            String testValue = this.examLimitVal(dto.getTestValue(), StringUtil.isNotNull(analyseData) ? analyseData.getExamLimitValue() : "");
            if(StringUtil.isNotNull(analyseData)) {
                ms = analyseData.getMostSignificance();
                md = analyseData.getMostDecimal();
            }
            data.append(testValue).append(";").append(DateUtil.dateToString(dto.getAnalyzeTime(), DateUtil.YEAR)).append(";");
            if (!length.equals(qcDataDetails.size() - 1)) {
                data.append(dto.getParamsName()).append(",");
            }
            dto.setTestValue(testValue.replace("%", ""));
            try {
                temp = new BigDecimal(testValue);
            } catch (Exception ex) {
                throw new BaseException("所配置的数值存在问题！");
            }
            avgValue = avgValue.add(temp);
            max = temp.max(max);
            if (num++ == 0) {
                min = temp;
            }
            min = temp.min(min);
            x.add(Double.parseDouble(testValue));
        }
        //测试项目id
        String userId = dtoQCDataConfig.getPersonId();
        Integer type = dtoQCDataConfig.getType();

        //方差值
        BigDecimal stdValue = new BigDecimal(getStDev(x));
        avgValue = avgValue.divide(BigDecimal.valueOf(qcDataDetails.size()), 20, BigDecimal.ROUND_HALF_UP);
        String avgValueDStr = proService.getDecimal(ms, md, avgValue.toString());
        avgValue = new BigDecimal(avgValueDStr);

        DtoQCData qcData = repository.findByTestIdAndUserIdAndDataTypeAndParamsName(testId, userId, type, dtoQCDataConfig.getParamsName());
        if (StringUtil.isNull(qcData)) {
            qcData = new DtoQCData();
        }
        String upAssist = proService.getDecimal(ms, md, (avgValue.add(stdValue)).toString());
        String downAssist = proService.getDecimal(ms, md, (avgValue.subtract(stdValue)).toString());
        String upWarning = proService.getDecimal(ms, md, (avgValue.add(stdValue.multiply(new BigDecimal(2)))).toString());
        String downWarning = proService.getDecimal(ms, md, (avgValue.subtract(stdValue.multiply(new BigDecimal(2)))).toString());
        String upControl = proService.getDecimal(ms, md, (avgValue.add(stdValue.multiply(new BigDecimal(3)))).toString());
        String downControl = proService.getDecimal(ms, md, (avgValue.subtract(stdValue.multiply(new BigDecimal(3)))).toString());
        qcData.setParamsName(dtoQCDataConfig.getParamsName());
        qcData.setUpAssist(Double.parseDouble(upAssist));
        qcData.setDownAssist(Double.parseDouble(downAssist));
        qcData.setUpWarning(Double.parseDouble(upWarning));
        qcData.setDownWarning(Double.parseDouble(downWarning));
        qcData.setUpControl(Double.parseDouble(upControl));
        qcData.setDownControl(Double.parseDouble(downControl));
        qcData.setAvgValue(Double.parseDouble(avgValueDStr));
        qcData.setLastTime(new Date());
        qcData.setTestId(testId);
        qcData.setUserId(userId);
        qcData.setStdValue(stdValue.toString());
        qcData.setDataType(dtoQCDataConfig.getType());
        qcData.setDataStr(data.toString());
        repository.save(qcData);
    }

    @Override
    public DtoQCData findConfigQCData(String testId, String personId, Integer type,String paramsName) {
        if (!StringUtils.isNotNullAndEmpty(paramsName)) {
            paramsName = "";
        }
        DtoQCData dtoQCData = repository.findByTestIdAndUserIdAndDataTypeAndParamsName(testId, personId, type, paramsName);
        if (StringUtil.isNotNull(dtoQCData)) {
            //          String data = dtoQCData.getDataStr();
            //           List<String> dList = Arrays.asList(data.split(","));
//            List<DtoQCDataDetail> qcDataDetails = new ArrayList<>();
//            for (String d : dList) {
//                DtoQCDataDetail dtoQCDataDetail = new DtoQCDataDetail();
//                String[] arrays = d.split(";");
//                dtoQCDataDetail.setTestValue(arrays[0]);
//                dtoQCDataDetail.setAnalyzeTime(DateUtil.stringToDate(arrays[1], DateUtil.YEAR));
//                dtoQCDataDetail.setParamsName(arrays.length > 2 ? arrays[2] : "");
//                qcDataDetails.add(dtoQCDataDetail);
//            }
            String analystName = "";
            String dimensionName = "";
            DtoTest dtoTest = testService.findOne(testId);
            if (StringUtil.isNotNull(dtoTest)) {
                String dimensionId = dtoTest.getDimensionId();
                if (StringUtils.isNotNullAndEmpty(dimensionId) && !dimensionId.equals(UUIDHelper.GUID_EMPTY)) {
                    DtoDimension dtoDimension = dimensionService.findOne(dimensionId);
                    if (StringUtil.isNotNull(dtoDimension)) {
                        dimensionName = dtoDimension.getDimensionName();
                    }
                }
            }
            DtoPerson dtoPerson = personService.findOne(dtoQCData.getUserId());
            if (StringUtil.isNotNull(dtoPerson)) {
                analystName = dtoPerson.getCName();
            }
            dtoQCData.setAnalystName(analystName);
            dtoQCData.setDimension(dimensionName);
        }
        return dtoQCData;
    }

    /**
     * 获取小于检出限值
     *
     * @param value     结果值
     * @param examValue 检出限
     * @return 结果值
     */
    private String examLimitVal(String value, String examValue){
        String result = value;
        if (StringUtil.isNotEmpty(value) && StringUtil.isNotEmpty(examValue)){
            if (value.contains("<") || value.contains("＜") || value.contains("ND") || value.contains("L") || value.contains("DL")){
                result = examValue;
            }
        }
        return result;
    }

    private  Double  getStDev(List<Double> x) {
        Integer m = x.size();
        Double sum = 0d;
        for (Integer i = 0; i < m; i++) {//求和
            sum += x.get(i);
        }
        Double dAve = sum / m;//求平均值
        Double dVar = 0d;
        for (Integer i = 0; i < m; i++) {//求方差
            dVar += (x.get(i) - dAve) * (x.get(i) - dAve);
        }
        return Math.sqrt(dVar / m);
    }
}