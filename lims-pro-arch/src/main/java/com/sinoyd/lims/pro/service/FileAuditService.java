package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoFileAudit;

import java.util.Map;

/**
 * FileAudit操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/19
 * @since V100R001
 */
public interface FileAuditService extends IBaseJpaService<DtoFileAudit, String> {
    /**
     * 提交
     * @param params 参数
     */
    void submit(Map<String, Object> params);

    /**
     * 修正文件关联关系
     * @param fileAudit 对象
     */
    void updateFileRelation(DtoFileAudit fileAudit);

    /**
     * 附件上传
     * @param id 标识
     * @return 对象
     */
    DtoFileAudit findAttachPath(String id);
}
