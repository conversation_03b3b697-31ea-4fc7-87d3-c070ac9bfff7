package com.sinoyd.lims.lim.repository.lims;

import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthority;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * 文件夹权限仓储
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
public interface DocAuthorityRepository extends IBaseJpaPhysicalDeleteRepository<DtoDocAuthority, String> {

    /**
     * 根据objectId查询有权限的数据
     *
     * @param objectId 对象id
     * @return 返回查询到的数据个数
     */
    @Query("select p from DtoDocAuthority p where p.objectId = :objectId and p.authId = :docAuthorityCode and p.roleId in :roleIds and p.authState = 1")
    List<DtoDocAuthority> validateAuth(@Param("objectId") String objectId, @Param("docAuthorityCode") String docAuthorityCode, @Param("roleIds") List<String> roleIds);

    /**
     * 根据objectId查询有权限的数据
     *
     * @return 返回查询到的数据个数
     */
    @Query("select p from DtoDocAuthority p where  p.authId = :docAuthorityCode and p.roleId in :roleIds and p.authState = 1")
    List<DtoDocAuthority> findAuthFolder(@Param("docAuthorityCode") String docAuthorityCode, @Param("roleIds") List<String> roleIds);

    /**
     * 根据objectId查询权限
     *
     * @param objectId
     * @return 返回权限列表
     */
    List<DtoDocAuthority> findByObjectId(String objectId);

    /**
     * 根据objectId查询权限
     *
     * @param objectIds
     * @return 返回权限列表
     */
    List<DtoDocAuthority> findByObjectId(List<String> objectIds);


    /**
     * 批量删除当前数据的权限
     *
     * @param objectIds 对象ids
     * @return 返回删除行数
     */
    @Transactional
    Integer deleteByObjectIdIn(List<String> objectIds);

}