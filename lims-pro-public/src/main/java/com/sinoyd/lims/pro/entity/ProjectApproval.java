package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * ProjectApproval实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ProjectApproval")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ProjectApproval implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 项目id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目id")
    private String projectId;

    /**
     * 申请日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("申请日期")
    private Date approveDate;

    /**
     * 修改状态
     */
    @Column(length = 50)
    @ApiModelProperty("修改状态")
    private String modifyStatus;

    /**
     * 审核人员
     */
    @Column(length = 50)
    @ApiModelProperty("审核人员")
    private String approvePersonId;

    /**
     * 意见
     */
    @Column(length = 50)
    @ApiModelProperty("意见")
    private String comment;

}
