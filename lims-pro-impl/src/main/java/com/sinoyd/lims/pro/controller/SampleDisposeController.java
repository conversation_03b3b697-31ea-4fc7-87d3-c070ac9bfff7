package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.pro.dto.customer.DtoSampleDisposeTemp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.SampleDisposeService;
import com.sinoyd.lims.pro.criteria.SampleDisposeCriteria;
import com.sinoyd.lims.pro.dto.DtoSampleDispose;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * SampleDispose服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: SampleDispose服务")
@RestController
@RequestMapping("api/pro/sampleDispose")
public class SampleDisposeController extends BaseJpaController<DtoSampleDispose, String, SampleDisposeService> {


    /**
     * 分页动态条件查询SampleDispose
     *
     * @param sampleDisposeCriteria 条件参数
     * @return RestResponse<List < SampleDispose>>
     */
    @ApiOperation(value = "分页动态条件查询SampleDispose", notes = "分页动态条件查询SampleDispose")
    @GetMapping
    public RestResponse<List<DtoSampleDispose>> findByPage(SampleDisposeCriteria sampleDisposeCriteria) {
        PageBean<DtoSampleDispose> pageBean = super.getPageBean();
        RestResponse<List<DtoSampleDispose>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, sampleDisposeCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询SampleDispose
     *
     * @param id 主键id
     * @return RestResponse<DtoSampleDispose>
     */
    @ApiOperation(value = "按主键查询SampleDispose", notes = "按主键查询SampleDispose")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoSampleDispose> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoSampleDispose> restResponse = new RestResponse<>();
        DtoSampleDispose sampleDispose = service.findOne(id);
        restResponse.setData(sampleDispose);
        restResponse.setRestStatus(StringUtil.isNull(sampleDispose) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "按主键查询SampleDispose", notes = "按主键查询SampleDispose")
    @GetMapping(path = "/check/{sampleId}")
    public RestResponse<Boolean> check(@PathVariable(name = "sampleId") String sampleId) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.checkSampleIsDisposed(sampleId));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "检查多个样品是否已留样", notes = "检查多个样品是否已留样")
    @PostMapping(path = "/batchCheck")
    public RestResponse<Boolean> batchCheck(@RequestBody DtoSampleDispose sampleDispose) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setData(service.checkSamplesIsDisposed(sampleDispose));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增SampleDispose
     *
     * @param sampleDispose 实体列表
     * @return RestResponse<DtoSampleDispose>
     */
    @ApiOperation(value = "新增SampleDispose", notes = "新增SampleDispose")
    @PostMapping
    public RestResponse<DtoSampleDispose> create(@RequestBody @Validated DtoSampleDispose sampleDispose) {
        RestResponse<DtoSampleDispose> restResponse = new RestResponse<>();
        restResponse.setData(service.save(sampleDispose));
        return restResponse;
    }

    /**
     * 批量新增SampleDispose
     *
     * @param sampleDispose 实体
     * @return RestResponse<DtoSampleDispose>
     */
    @ApiOperation(value = "批量新增SampleDispose", notes = "批量新增SampleDispose")
    @PostMapping("/batch")
    public RestResponse<DtoSampleDispose> batchCreate(@RequestBody DtoSampleDispose sampleDispose) {
        RestResponse<DtoSampleDispose> restResponse = new RestResponse<>();
        restResponse.setData(service.batchSave(sampleDispose));
        return restResponse;
    }

    /**
     * 新增SampleDispose
     *
     * @param sampleDispose 实体列表
     * @return RestResponse<DtoSampleDispose>
     */
    @ApiOperation(value = "修改SampleDispose", notes = "修改SampleDispose")
    @PutMapping
    public RestResponse<DtoSampleDispose> update(@RequestBody @Validated DtoSampleDispose sampleDispose) {
        RestResponse<DtoSampleDispose> restResponse = new RestResponse<>();
        restResponse.setData(service.update(sampleDispose));
        return restResponse;
    }

    /**
     * "根据id批量删除SampleDispose
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除SampleDispose", notes = "根据id批量删除SampleDispose")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 处置样品
     *
     * @param sampleDisposeTemp 处置样品传输类
     * @return 处置结果
     */
    @ApiOperation(value = "处置样品", notes = "处置样品")
    @PutMapping("/dispose")
    public RestResponse<List<DtoSampleDispose>> dispose(@RequestBody DtoSampleDisposeTemp sampleDisposeTemp) {
        RestResponse<List<DtoSampleDispose>> restResponse = new RestResponse<>();
        List<DtoSampleDispose> result = service.disposal(sampleDisposeTemp);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(result.size());
        restResponse.setData(result);
        return restResponse;
    }
}