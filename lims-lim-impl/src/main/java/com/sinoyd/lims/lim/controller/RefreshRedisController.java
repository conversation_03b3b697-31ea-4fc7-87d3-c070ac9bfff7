package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.lims.lim.service.RefreshRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 刷新redis缓存
 * <AUTHOR>
 * @version V1.0.0 2021/09/14
 * @since V100R001
 */
@Api(tags = "示例: 刷新redis缓存")
@RestController
@RequestMapping("api/lim/refreshRedis")
public class RefreshRedisController extends ExceptionHandlerController<RefreshRedisService> {

    @ApiOperation(value = "获取常量配置的redis缓存模块名称及编码列表", notes = "获取常量配置的redis缓存模块名称及编码列表")
    @GetMapping
    public RestResponse<List<DtoCode>> findRedisModel() {
        RestResponse<List<DtoCode>> restResponse = new RestResponse<>();
        List<DtoCode> DtoCodeList = service.findRedisModel();
        restResponse.setRestStatus(StringUtil.isEmpty(DtoCodeList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(DtoCodeList);
        restResponse.setCount(DtoCodeList.size());
        return restResponse;
    }

    @ApiOperation(value = "根据常量配置的redis模块编码刷新redis缓存", notes = "根据常量配置的redis模块编码刷新redis缓存")
    @PostMapping
    public RestResponse<String> refreshRedis(@RequestBody List<DtoCode> modelCodes) {
        RestResponse<String> restResp = new RestResponse<>();
        service.refreshRedisByCodes(modelCodes);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        return restResp;
    }


    @ApiOperation(value = "刷新整个redis实例", notes = "刷新整个redis实例")
    @PostMapping("/flushAll")
    public RestResponse<String> flushAll() {
        RestResponse<String> restResp = new RestResponse<>();
        service.flushAll();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        return restResp;
    }


}
