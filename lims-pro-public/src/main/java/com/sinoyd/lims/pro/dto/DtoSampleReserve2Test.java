package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SampleReserve2Test;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoSampleReserve实体
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/20
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SampleReserve2Test")
@Data
@DynamicInsert
public class DtoSampleReserve2Test extends SampleReserve2Test {
}
