package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * OATaskHandleLog实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OATaskHandleLog")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class OATaskHandleLog implements BaseEntity,Serializable {
 
    public  OATaskHandleLog() {
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 任务标识
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("任务标识")
	private String taskId;
    
    /**
    * 是否同意
    */
    @Column(nullable=false)
    @ApiModelProperty("是否同意")
	private Boolean isAgree;
    
    /**
    * 批注
    */
    @ApiModelProperty("批注")
	private String comment;
    
    /**
    * 办理时间
    */
    @Column(nullable=false)
    @ApiModelProperty("办理时间")
	private Date completeTime;
    
    /**
    * 办理人id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("办理人id")
	private String assigneeId;
    
    /**
    * 办理人账号
    */
    @Column(length=50)
    @ApiModelProperty("办理人账号")
	private String assignee;
    
    /**
    * 办理人名称
    */
    @Column(length=100)
    @ApiModelProperty("办理人名称")
	private String assigneeName;
    
    /**
    * 工作流任务id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("工作流任务id")
	private String actTaskId;
    
    /**
    * 工作流任务节点key
    */
    @Column(length=50)
    @ApiModelProperty("工作流任务节点key")
	private String actTaskDefKey;
    
    /**
    * 工作流任务名称
    */
    @Column(length=50)
    @ApiModelProperty("工作流任务名称")
	private String actTaskName;
    
    /**
    * 是否为发起申请步骤
    */
    @Column(nullable=false)
    @ApiModelProperty("是否为发起申请步骤")
	private Boolean isFirstStep;

    @Column
    @ApiModelProperty("陪审人id，多个id用;隔开")
    private String jurorId;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }