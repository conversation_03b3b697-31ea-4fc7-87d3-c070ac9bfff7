package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * ReceiveSubSampleRecord查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReceiveSubSampleRecordCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 关键字（项目编号、项目名称、委托方、受检方）
     */
    private String key;

    /**
     * 包含样品
     */
    private String sample;

    /**
     * 分配状态
     */
    private Integer status = EnumPRO.EnumAssignStatus.所有.getValue();

    /**
     * 是否显示客户信息
     */
    private Boolean isShowCustomer = true;

    /**
     * 领样单id
     */
    private List<String> subIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and s.receiveId = r.id");
        condition.append(" and r.projectId = p.id");
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and r.sendTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and r.sendTime < :endTime");
            values.put("endTime", to);
        }
        if (status.equals(EnumPRO.EnumAssignStatus.所有.getValue())) {
            condition.append(" and bitand(s.subStatus , :subStatus) > 0");
            values.put("subStatus", EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue());
            condition.append(" and r.receiveStatus > :receiveStatus");
            values.put("receiveStatus", EnumLIM.EnumReceiveRecordStatus.新建.getValue());
        } else if (status.equals(EnumPRO.EnumAssignStatus.未分配.getValue())) {
            condition.append(" and bitand(s.subStatus , :subStatus) = :shouldStatus");
            values.put("subStatus", EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue() + EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue());
            values.put("shouldStatus", EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue());
            condition.append(" and r.receiveStatus = :receiveStatus");
            values.put("receiveStatus", EnumLIM.EnumReceiveRecordStatus.已经送样.getValue());
        } else if (status.equals(EnumPRO.EnumAssignStatus.已分配.getValue())) {
            condition.append(" and bitand(s.subStatus , :subStatus) = :subStatus");
            values.put("subStatus", EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue() + EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue());
        }

        if (StringUtil.isNotEmpty(this.key)) {
            StringBuilder builder = new StringBuilder(" and (p.projectCode like :key or p.projectName like :key or p.customerName like :key or p.inspectedEnt like :key)");
            if (!isShowCustomer){
                builder = new StringBuilder(" and (p.projectCode like :key or p.projectName like :key)");
            }
            condition.append(builder);
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.sample)) {
            condition.append(" and exists (select 1 from DtoSample sam,DtoReceiveSubSampleRecord2Sample r2s where s.id = r2s.receiveSubSampleRecordId" +
                    " and r2s.sampleId = sam.id and (sam.code like :sample or sam.redFolderName like :sample))");
            values.put("sample", "%" + this.sample + "%");
        }
        if (StringUtil.isNotEmpty(this.subIds)) {
            condition.append(" and s.id in :subIds ");
            values.put("subIds", subIds);
        }
        return condition.toString();
    }
}