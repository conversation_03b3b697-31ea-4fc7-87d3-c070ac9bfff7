-- 创建项目推送信息表
CREATE TABLE TB_PRO_ProjectPushInfo
(
    id        VARCHAR(50)                                                 NOT NULL,
    projectId VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    pushTime  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    orgId     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL
);
ALTER TABLE TB_PRO_ProjectPushInfo
    ADD CONSTRAINT PRIMARY KEY (id);
COMMENT
ON COLUMN TB_PRO_ProjectPushInfo.id IS 'id';
COMMENT
ON COLUMN TB_PRO_ProjectPushInfo.projectId IS '项目id';
COMMENT
ON COLUMN TB_PRO_ProjectPushInfo.pushTime IS '推送时间';
COMMENT
ON COLUMN TB_PRO_ProjectPushInfo.orgId IS '组织机构id';

CREATE INDEX IX_TB_PRO_ProjectPushInfo ON TB_PRO_ProjectPushInfo (projectId);