package com.sinoyd.lims.api.controller;

import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.api.criteria.DocumentPhoneCriteria;
import com.sinoyd.lims.api.criteria.ReceivePhoneCriteria;
import com.sinoyd.lims.api.dto.*;
import com.sinoyd.lims.api.dto.customer.DtoFolderCopy;
import com.sinoyd.lims.api.dto.customer.DtoMobilePointPic;
import com.sinoyd.lims.api.dto.customer.DtoMobileReceive;
import com.sinoyd.lims.api.service.ErgencyMonitoringService;
import com.sinoyd.lims.lim.dto.lims.DtoPersonFaceMsg;
import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;


@Api(tags = "示例: ergencyMonitoring服务")
@RestController
@RequestMapping("api/field/ergency")
public class ErgencyMonitorController extends BaseJpaController<DtoProject, String, ErgencyMonitoringService> {


    @Autowired
    private ReceiveSampleRecordService receiveSampleRecordService;

    /**
     * 新增项目
     *
     * @param receiveSampleRecordPhone 参数
     * @return 新增项目
     */
    @ApiOperation(value = "新增项目", notes = "新增项目")
    @PostMapping("/saveOutsideProject")
    public RestResponse<DtoReceiveSampleRecordPhone> saveOutsideProject(@RequestBody @Validated DtoReceiveSampleRecordPhone receiveSampleRecordPhone) {
        RestResponse<DtoReceiveSampleRecordPhone> restResp = new RestResponse<>();
        DtoReceiveSampleRecordPhone recordPhone = service.saveOutsideProject(receiveSampleRecordPhone);
        restResp.setData(recordPhone);
        restResp.setRestStatus(StringUtil.isNotNull(recordPhone) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 新增项目
     *
     * @param receiveSampleRecordPhone 参数
     * @return 新增项目
     */
    @ApiOperation(value = "新增项目", notes = "新增项目")
    @PostMapping("/updateOutsideProject")
    public RestResponse<DtoReceiveSampleRecordPhone> updateOutsideProject(@RequestBody @Validated DtoReceiveSampleRecordPhone receiveSampleRecordPhone) {
        RestResponse<DtoReceiveSampleRecordPhone> restResp = new RestResponse<>();
        DtoReceiveSampleRecordPhone recordPhone = service.updateOutsideProject(receiveSampleRecordPhone);
        restResp.setData(recordPhone);
        restResp.setRestStatus(StringUtil.isNotNull(recordPhone) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 新增项目
     *
     * @param receiveSampleRecordPhone 参数
     * @return 新增项目
     */
    @ApiOperation(value = "复制项目", notes = "复制项目")
    @PostMapping("/copyOutsideProject")
    public RestResponse<DtoReceiveSampleRecordPhone> copyOutsideProject(@RequestBody DtoReceiveSampleRecordPhone receiveSampleRecordPhone) {
        RestResponse<DtoReceiveSampleRecordPhone> restResp = new RestResponse<>();
        DtoReceiveSampleRecordPhone recordPhone = service.copyOutsideProject(receiveSampleRecordPhone);
        restResp.setData(recordPhone);
        restResp.setRestStatus(StringUtil.isNotNull(recordPhone) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 根据id批量删除送样单
     *
     * @param recordInfo receiveIds
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除送样单", notes = "根据id批量删除送样单")
    @PostMapping("/deleteReocrd")
    public RestResponse<String> delete(@RequestBody DtoRecordInfo recordInfo) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setCount(receiveSampleRecordService.logicDeleteById(recordInfo.getReceiveIds()));
        return restResp;
    }

    /**
     * 获取移动端版本
     *
     * @param folder 参数
     * @return 获取移动端版本
     */
    @ApiOperation(value = "获取移动端版本", notes = "获取移动端版本")
    @PostMapping("/versionInfo")
    public RestResponse<DtoVersionInfo> getVersionInfo(@RequestBody DtoFolderInfo folder) {
        RestResponse<DtoVersionInfo> restResp = new RestResponse<>();
        DtoVersionInfo versionInfo = service.getVersionInfo(folder.getType());
        restResp.setData(versionInfo);
        restResp.setRestStatus(StringUtil.isNotNull(versionInfo) ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResp;
    }

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位信息
     * @return 新增点位
     */
    @ApiOperation(value = "新增点位", notes = "新增点位")
    @PostMapping("/saveFolder")
    public RestResponse<String> saveSampleList(@RequestBody @Validated DtoSampleFolder dtoSampleFolder) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.saveSampleList(dtoSampleFolder));
        return restResp;
    }

    @ApiOperation(value = "定位服务判定", notes = "定位服务判定")
    @GetMapping("/isOrientation")
    public RestResponse<Map<String, String>> isOrientation() {
        RestResponse<Map<String, String>> restResponse = new RestResponse<>();
        restResponse.setData(service.isOrientation());
        return restResponse;
    }

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位信息
     * @return 新增点位
     */
    @ApiOperation(value = "新增点位", notes = "新增点位")
    @PostMapping("/checkSample")
    public RestResponse<String> checkSample(@RequestBody DtoSampleFolder dtoSampleFolder) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.checkSample(dtoSampleFolder.getId(), dtoSampleFolder.getProjectId()));
        return restResp;
    }

    /**
     * 获取当前人信息
     *
     * @param id 当前人id
     * @return RestResponse<DtoPersonPhone>
     */
    @ApiOperation(value = "按主键查询项目", notes = "按主键查询项目")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoPersonPhone> findPerson(@PathVariable(name = "id") String id) {
        RestResponse<DtoPersonPhone> restResponse = new RestResponse<>();
        DtoPersonPhone person = service.getPersonById(id);
        restResponse.setData(person);
        restResponse.setRestStatus(StringUtil.isNull(person) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 保存人员信息
     *
     * @param dtoPersonFaceMsg 人员信息
     */
    @ApiOperation(value = "保存人员信息", notes = "保存人员信息")
    @PostMapping("/savePersonFace")
    public RestResponse<Boolean> savePersonFace(@RequestBody DtoPersonFaceMsg dtoPersonFaceMsg) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.savePersonFace(dtoPersonFaceMsg.getPersonId(), dtoPersonFaceMsg.getFacePicture());
        restResp.setData(Boolean.TRUE);
        return restResp;
    }

    /**
     * 返回当前人员的脸部信息
     *
     * @param dtoPersonFaceMsg 人员信息
     */
    @ApiOperation(value = "返回当前人员的脸部信息", notes = "返回当前人员的脸部信息")
    @PostMapping("/personFaceCompare")
    public RestResponse<String> personFaceCompare(@RequestBody DtoPersonFaceMsg dtoPersonFaceMsg) throws Exception {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.personFaceCompare(dtoPersonFaceMsg.getPersonId()));
        return restResp;
    }

    /**
     * 修改点位名称
     *
     * @param folderPhone 点位信息
     */
    @ApiOperation(value = "修改点位名称", notes = "修改点位名称")
    @PostMapping("/updateSampleFolderName")
    public RestResponse<Boolean> updateSampleFolderName(@RequestBody @Validated DtoSampleFolderPhone folderPhone) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.updateSampleFolderName(folderPhone.getId(), folderPhone.getSampleFolderName());
        restResp.setData(Boolean.TRUE);
        return restResp;
    }

    /**
     * 修改样品编号
     *
     * @param samplePhone 样品信息
     */
    @ApiOperation(value = "修改样品编号", notes = "修改样品编号")
    @PostMapping("/updateSampleCode")
    public RestResponse<Void> updateSampleCode(@RequestBody @Validated DtoSamplePhone samplePhone) {
        RestResponse<Void> restResp = new RestResponse<>();
        service.updateSampleCode(samplePhone.getSamId(), samplePhone.getSampleCode());
        return restResp;
    }

    /**
     * 复制点位
     *
     * @param dto 实体
     * @return 点位方案
     */
    @ApiOperation(value = "复制点位", notes = "复制点位")
    @PostMapping("/folderCopy")
    public RestResponse<List<DtoSampleFolderTemp>> copyFolders(@RequestBody DtoFolderCopy dto) {
        RestResponse<List<DtoSampleFolderTemp>> restResponse = new RestResponse<>();
        List<DtoSampleFolderTemp> datas = service.copyFolders(dto.getIds(), dto.getTimes());
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtil.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNull(datas) ? 0 : datas.size());
        return restResponse;
    }

    /**
     * 移动端测点示意图列表
     *
     * @param criteria 查询条件
     * @return RestResponse<List < DtoDocument>>
     */
    @GetMapping(path = "/mobilePointPic")
    public RestResponse<List<DtoMobilePointPic>> getMobilePointPic(DocumentPhoneCriteria criteria) {
        RestResponse<List<DtoMobilePointPic>> response = new RestResponse<>();
        PageBean<DtoMobilePointPic> pageBean = super.getPageBean();
        List<DtoMobilePointPic> mobilePointPics = service.getMobilePointPic(pageBean, criteria);
        response.setData(mobilePointPics);
        response.setCount(pageBean.getRowsCount());
        response.setRestStatus(StringUtil.isNull(mobilePointPics) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return response;
    }

    /**
     * 根据示意图批量删除
     *
     * @param criteria 点位示意图ids
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据示意图批量删除", notes = "根据示意图批量删除")
    @PostMapping(path = "deletePointPic")
    public RestResponse<String> delete(@RequestBody DocumentPhoneCriteria criteria) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deletePointPic(criteria.getIds());
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 测点示意图(送样单列表)
     *
     * @param criteria 查询条件
     * @return RestResponse<List < DtoDocument>>
     */
    @ApiOperation(value = "测点示意图(送样单列表)", notes = "测点示意图(送样单列表)")
    @GetMapping(path = "/receives")
    public RestResponse<List<DtoMobileReceive>> getReceiveList(ReceivePhoneCriteria criteria) {
        RestResponse<List<DtoMobileReceive>> response = new RestResponse<>();
        PageBean<DtoMobileReceive> pageBean = super.getPageBean();
        List<DtoMobileReceive> mobilePointPics = service.getReceiveList(pageBean, criteria);
        response.setData(mobilePointPics);
        response.setCount(pageBean.getRowsCount());
        response.setRestStatus(StringUtil.isNull(mobilePointPics) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return response;
    }

    /**
     * 点位示意图替换
     *
     * @param dtoMobilePointPic 点位示意图传输实体
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "点位示意图替换", notes = "点位示意图替换")
    @PostMapping(path = "/replace")
    public RestResponse<String> replacePointPic(@RequestBody DtoMobilePointPic dtoMobilePointPic) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.replacePointPic(dtoMobilePointPic.getId(), dtoMobilePointPic.getReceiveId()));
        return restResp;
    }

    /**
     * 分页查询电子签名列表
     *
     * @param criteria 请求条件
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "分页查询电子签名列表", notes = "分页查询电子签名列表")
    @GetMapping(path = "/document")
    public RestResponse<List<DtoDocument>> findSignPage(DocumentCriteria criteria) {
        RestResponse<List<DtoDocument>> restResp = new RestResponse<>();
        PageBean<DtoDocument> pageBean = super.getPageBean();
        service.findSignPage(pageBean, criteria);
        restResp.setData(pageBean.getData());
        restResp.setCount(pageBean.getRowsCount());
        return restResp;
    }

    /**
     * 移动端电子签名
     *
     * @param request 请求体
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "移动端电子签名", notes = "移动端电子签名")
    @PostMapping(path = "/sign")
    public RestResponse<DtoDocument> replacePointPic(HttpServletRequest request) {
        RestResponse<DtoDocument> restResp = new RestResponse<>();
        restResp.setData(service.sign(request));
        return restResp;
    }


    /**
     * 移动端采样单签名
     *
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "移动端采样单签名", notes = "移动端采样单签名")
    @PostMapping(path = "/signReceive/{docId}")
    public RestResponse<Void> signReceive(@PathVariable() String docId) {
        RestResponse<Void> restResp = new RestResponse<>();
        service.signReceive(docId);
        return restResp;
    }

    /**
     * 根据送样单id 查询采样单
     *
     * @param folderId 送样单id
     * @return
     */
    @ApiOperation(value = "根据送样单id 查询采样单", notes = "根据送样单id 查询采样单")
    @GetMapping("/sampling/{folderId}")
    public RestResponse<List<DtoDocument>> getSampling(@PathVariable() String folderId) {
        RestResponse<List<DtoDocument>> restResponse = new RestResponse<>();
        restResponse.setData(service.getSamplingDocuments(folderId));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 清空采样单电子签名
     *
     * @param docId 附件id
     * @return
     */
    @ApiOperation(value = "清空采样单电子签名", notes = "清空采样单电子签名")
    @PostMapping("/removeSignature/{docId}")
    public RestResponse<Void> removeSignature(@PathVariable() String docId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.removeSignature(docId);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }


}
