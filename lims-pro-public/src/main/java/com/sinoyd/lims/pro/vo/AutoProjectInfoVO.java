package com.sinoyd.lims.pro.vo;

import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AutoProjectInfoVO {

    /**
     * 监测年月
     */
    private String ymMonitor;

    /**
     * 监测间隔
     */
    private String interval;

    /**
     * 监测点位
     */
    private String folderName;

    /**
     * 分析项目名称
     */
    private String anaItemName;

    /**
     * 监测点位数
     */
    private Integer folderCount;

    /**
     * 分析项目数
     */
    private Integer anaItemCount;

    /**
     * 任务单明细
     */
    private List<DtoQuotationDetail> detailList;
}
