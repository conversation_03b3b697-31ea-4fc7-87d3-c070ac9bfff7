package com.sinoyd.base.dto.customer;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 原始记录单与测试项目关系excel导入导出  poi实体
 * <AUTHOR>
 * @version V1.0.0 2023/10/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DtoImportRecordConfig2Test extends PoiBaseEntity{
    /**
     *  检测类型
     */
    @Excel(name = "检测类型",orderNum = "10",width = 20)
    private String sampleType;

    /**
     *  分析项目
     */
    @Excel(name = "分析项目",orderNum = "20",width = 20)
    private String analyzeItem;

    /**
     *  分析方法
     */
    @Excel(name = "分析方法",orderNum = "30",width = 20)
    private String analyzeMethod;

    /**
     *  原始记录单名称
     */
    @Excel(name = "原始记录单",orderNum = "40",width = 20)
    private String recordNames;
}
