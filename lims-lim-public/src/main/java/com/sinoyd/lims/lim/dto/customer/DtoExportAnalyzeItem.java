package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Table;
import javax.validation.constraints.Pattern;

/**
 * 分析项目导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/6
 * @since V100R001
 */
@Table(name = "TB_BASE_AnalyzeItem")
@Data
public class DtoExportAnalyzeItem extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id;

    /**
     * 名称
     */
    @Excel(name = "名称", orderNum = "20")
    private String analyzeItemName;

    /**
     * 分析因子编号
     */
    @Excel(name = "分析因子编号", orderNum = "30")
    private String analyzeItemCode;

    /**
     * 变量名称（预留，前台改为别名）
     */
    @Excel(name = "变量名称(别名)", orderNum = "40")
    private String variableName;

    /**
     * 拼音缩写
     */
    @Excel(name = "拼音缩写", orderNum = "50")
    private String pinYin;

    /**
     * 全拼
     */
    @Excel(name = "全拼", orderNum = "60")
    private String fullPinYin;

    /**
     * CAS号
     */
    @Excel(name = "CAS号", orderNum = "70")
    private String casNum;

    /**
     * 是否删除
     */
    @Excel(name = "是否删除", orderNum = "80")
    @Pattern(regexp = "^(true|false)$", message = "是否删除必须为布尔类型")
    private String isDeleted;

    /**
     * 排序值
     */
    @Excel(name = "排序值", orderNum = "90")
    @Pattern(regexp = "^-?\\d+$", message = "排序值必须为整数")
    private String orderNum;
}
