package com.sinoyd.lims.lim.service.statistics;

import com.sinoyd.lims.lim.constants.LimConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 仪器过期统计业务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/22
 */
@Service
@Slf4j
public class InstrumentOverdueStatisticsServiceImpl extends AbsResourceStatisticsServiceImpl {

    @Override
    public String getStatisticsSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select count(1) as num ")
                .append("from TB_BASE_Instrument ")
                .append("where isDeleted = 0 and state in (1,3) ")
                // 当仪器没有填写溯源日期及周期，列表上计算不了有效值时，首页仪器过期数量不计算此类仪器
                .append(" and DATE_FORMAT( originEndDate, '%Y-%m-%d' ) != '1753-01-01'")
                .append("and DATE_FORMAT(originEndDate, '%Y-%m-%d') < DATE_FORMAT(now(), '%Y-%m-%d') ");
        return sql.toString();
    }

    @Override
    public String getStatisticsItemName() {
        return LimConstants.StatisticsItemName.INSTRUMENT_OVERDUE;
    }

    @Override
    public String getUnit() {
        return "台";
    }
}