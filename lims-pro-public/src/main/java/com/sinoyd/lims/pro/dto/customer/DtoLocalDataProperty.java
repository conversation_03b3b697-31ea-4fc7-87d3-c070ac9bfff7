package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import lombok.Data;

import java.util.*;

/**
 * 现场数据录入的dto
 * <AUTHOR>
 * @version V1.0.0 2019/12/11
 * @since V100R001
 */
@Data
public class DtoLocalDataProperty {
    /**
     * 相关的分析数据
     */
    private List<Map<String, Object>> analyseData = new ArrayList<>();

    /**
     * 公式相关参数
     */
    private List<DtoTestFormulaParamsConfig> paramsConfig = new ArrayList<>();

    /**
     * 测试数据
     */
    private Map<String, Object> test = new HashMap<>();

    /**
     * 是否允许更换公式
     */
    private Boolean isAlterFormula;
}
