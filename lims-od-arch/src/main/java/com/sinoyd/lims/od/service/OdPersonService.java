package com.sinoyd.lims.od.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.od.dto.DtoOdPerson;

import java.util.List;

/**
 * 嗅辨员操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
public interface OdPersonService extends IBaseJpaService<DtoOdPerson, String> {

    /**
     * 根据嗅辨任务id查询 按照序号排序
     *
     * @param taskId 嗅辨任务id
     * @return 嗅辨员集合
     */
    List<DtoOdPerson> findByTaskIdOrderBySn(String taskId);
}
