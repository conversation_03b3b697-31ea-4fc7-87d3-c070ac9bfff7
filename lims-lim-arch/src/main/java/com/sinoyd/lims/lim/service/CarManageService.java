package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoCarManage;

/**
 * 车辆管理配置
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface CarManageService extends IBaseJpaService<DtoCarManage, String> {

    /**
     * 重新定义接口（为了返回调用该接口类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回车辆管理对象
     */
    @Override
    DtoCarManage findOne(String id);
}