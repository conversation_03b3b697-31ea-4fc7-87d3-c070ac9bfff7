package com.sinoyd.base.controller;

import com.sinoyd.base.criteria.Instrument2DetectorCriteria;
import com.sinoyd.base.dto.lims.DtoInstrument2Detector;
import com.sinoyd.base.service.Instrument2DetectorService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仪器管理-基本信息
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@Api(tags = "仪器基本信息管理")
@RestController
@RequestMapping("/api/base/instrument2Detector")
@Validated
public class Instrument2DetectorController extends BaseJpaController<DtoInstrument2Detector, String, Instrument2DetectorService> {

    /**
     * 根据id查询仪器
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询仪器", notes = "根据id查询仪器")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrument2Detector> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoInstrument2Detector> restResp = new RestResponse<>();

        DtoInstrument2Detector instrument = service.findOne(id);
        restResp.setData(instrument);

        restResp.setRestStatus(StringUtil.isNull(instrument) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询仪器
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询仪器", notes = "分页动态条件查询仪器")
    @GetMapping
    public RestResponse<List<DtoInstrument2Detector>> findByPage(Instrument2DetectorCriteria criteria) {

        RestResponse<List<DtoInstrument2Detector>> restResp = new RestResponse<>();

        PageBean<DtoInstrument2Detector> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增仪器
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增仪器", notes = "新增仪器")
    @PostMapping
    public RestResponse<DtoInstrument2Detector> save(@Validated @RequestBody DtoInstrument2Detector entity) {

        RestResponse<DtoInstrument2Detector> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrument2Detector data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改仪器
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改仪器", notes = "修改仪器")
    @PutMapping
    public RestResponse<DtoInstrument2Detector> update(@Validated @RequestBody DtoInstrument2Detector entity) {

        RestResponse<DtoInstrument2Detector> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrument2Detector data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id删除仪器(逻辑删除)
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id删除仪器(逻辑删除)", notes = "根据id删除仪器(逻辑删除)")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除仪器
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除仪器", notes = "批量删除仪器")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }


}