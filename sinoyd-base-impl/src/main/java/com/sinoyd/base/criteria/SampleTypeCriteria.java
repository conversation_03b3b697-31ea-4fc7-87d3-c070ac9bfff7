package com.sinoyd.base.criteria;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;

/**
 * 检测类型条件查询
 * <AUTHOR>
 * @version V1.0.0 2019/04/29
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleTypeCriteria extends BaseCriteria {

    private String parentId;

    private ArrayList<String> parentIds;

    private Integer category;

    private String industryTypeId;

//    private String sampleTypeId;

    private String typeName;

    //是否获取子节点
    private Boolean isShowChildren=false;

    /**
     * 当传parentId 有值的时候，看isShowParent是否要包含parentId=id的当前数据
     */
    private  Boolean isShowParent =false;

    /**
     * 查询模板时，是否查询父级检测类型下的模板
     */
    private Boolean isShowModelParent = false;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(category)) {
            if (category != 0) {
                condition.append(" and category = :category");
                values.put("category", category);
            } else {
                condition.append(" and (category = 1 or category = 2)");
            }
        }

        if (StringUtils.isNotNullAndEmpty(parentId) && !UUIDHelper.GUID_EMPTY.equals(this.parentId)) {
            if (isShowParent) {
                if (isShowChildren)//获取子节点
                {
                    condition.append(" and (parentId = :parentId or id = :parentId or parentId in (select q.id from DtoSampleType q where q.parentId = :parentId))");
                    values.put("parentId", parentId);
                } else {
                    condition.append(" and (parentId = :parentId or id = :parentId )");
                    values.put("parentId", parentId);
                }
            } else if(isShowModelParent){
                if (isShowChildren)//获取子节点
                {
                    condition.append(" and (parentId = :parentId or parentId in (select q.parentId from DtoSampleType q where q.id = :parentId) or parentId in (select q.id from DtoSampleType q where q.parentId = :parentId))");
                    values.put("parentId", parentId);
                } else {
                    condition.append(" and (parentId = :parentId or parentId in (select q.parentId from DtoSampleType q where q.id = :parentId))");
                    values.put("parentId", parentId);
                }
            } else if (isShowChildren)//获取子节点
            {
                condition.append(" and (parentId = :parentId or parentId in (select q.id from DtoSampleType q where q.parentId = :parentId))");
                values.put("parentId", parentId);
            }else {
                condition.append(" and parentId = :parentId ");
                values.put("parentId", parentId);
            }
        }

        if (StringUtil.isNotEmpty(parentIds)) {
            condition.append(" and parentId in :parentIds");
            values.put("parentIds", parentIds);
        }

        if (StringUtils.isNotNullAndEmpty(industryTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.industryTypeId)) {
            condition.append(" and industryTypeId = :industryTypeId");
            values.put("industryTypeId", industryTypeId);
        }
//
//        //传入的样品类型id是大类id时，表格显示大类下的检测模板和大类下小类下的所有检测模板
//        if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !UUIDHelper.GuidEmpty().equals(this.sampleTypeId)) {
//            if (category == EnumBase.EnumSampleTypeCategory.检测类型大类.getValue()) {
//                condition.append(" and (parentId = :sampleTypeId or parentId = (select q.id from SampleType q where q.parentId = :sampleTypeId))");
//                values.put("sampleTypeId", sampleTypeId);
//            } else {
//                condition.append(" and (parentId = :sampleTypeId)");
//                values.put("sampleTypeId", sampleTypeId);
//            }
//        }

        if (StringUtils.isNotNullAndEmpty(typeName)) {
            condition.append(" and typeName like :typeName");
            values.put("typeName", "%" + this.typeName + "%");
        }

        condition.append(" and isDeleted = 0");
        return condition.toString();
    }
}