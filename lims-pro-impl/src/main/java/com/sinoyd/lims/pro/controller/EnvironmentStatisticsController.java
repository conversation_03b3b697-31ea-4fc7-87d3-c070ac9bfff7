package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.EnvironmentStatisticsCriteria;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.customer.DtoDataDetail;
import com.sinoyd.lims.pro.service.EnvironmentStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 环境质量数据统计
 *
 * <AUTHOR>
 * @version V1.0.0 2020/04/28
 * @since V100R001
 */
@Api(tags = "示例: 环境质量数据统计")
@RestController
@RequestMapping("api/pro/findDtoProject")
public class EnvironmentStatisticsController extends BaseJpaController<DtoProject, String, EnvironmentStatisticsService> {

    /**
     * 分页动态条件查询DtoProject
     *
     * @param environmentStatisticsCriteria 条件参数
     * @return RestResponse<List < DtoProject>>
     */
    @ApiOperation(value = "分页动态条件查询DtoProject", notes = "分页动态条件查询DtoProject")
    @PostMapping
    public RestResponse<List<DtoProject>> findByPage(@RequestParam(defaultValue = "1") Integer page,
                                                     @RequestParam(defaultValue = "990000") Integer rows,
                                                     @RequestParam(defaultValue = "") String sort,
                                                     @RequestBody EnvironmentStatisticsCriteria environmentStatisticsCriteria) {
        PageBean<DtoProject> pageBean = super.getPageBean();
        RestResponse<List<DtoProject>> restResponse = new RestResponse<>();
        pageBean.setPageNo(page);
        pageBean.setRowsPerPage(rows);
        pageBean.setSort(sort);
        List<DtoProject> list = service.findDtoProject(pageBean, environmentStatisticsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(list);
        restResponse.setCount(list.size());
        return restResponse;
    }

    /**
     * 监测计划树
     *
     * @return
     */
    @ApiOperation(value = "监测计划树", notes = "监测计划树")
    @GetMapping("/tree")
    public RestResponse<List<TreeNode>> tree(@RequestParam(name = "year") Integer year, @RequestParam(value = "month", required = false) Integer month) {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.getPointProperty(year, month));
        return restResponse;
    }

    /**
     * 查询样品详细数据
     *
     * @param dataDetail dto
     * @return RestResponse<DtoDetailData>
     */
    @ApiOperation(value = "查询项目数据", notes = "查询项目数据")
    @PostMapping("/dataDetail")
    public RestResponse<Map<String, Object>> findDetailDataByProjectList(@RequestBody DtoDataDetail dataDetail) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        Map<String, Object> map = service.findDetailDataByProjectList(dataDetail);
        List<Map<String, Object>> analyseDataMapList = (List<Map<String, Object>>) map.get("analyseData");
        restResp.setRestStatus(StringUtil.isEmpty(analyseDataMapList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(map);
        restResp.setCount(analyseDataMapList.size());
        return restResp;
    }
}