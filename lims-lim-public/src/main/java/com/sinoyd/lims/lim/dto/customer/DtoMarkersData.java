package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * MarkersData 实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/1
 * @since V100R001
 */
@Data
public class DtoMarkersData {

    /**
     * 报表编码
     */
    private String reportCode;

    /**
     * 送样单id
     */
    private String receiveSampleRecordId;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 通用ids
     */
    private List<String> ids;

    /**
     * 是否更新测试项目验证次数
     */
    private Boolean isUpdateTestValidate = true;

    /**
     * 测试项目id集合
     */
    private List<DtoTest> testList = new ArrayList<>();

    /**
     * 公式数据
     */
    private List<DtoParamsFormula> paramsFormulaList = new ArrayList<>();
    /**
     * 质控限值
     */
    private List<DtoQualityControlLimit> qualityControlLimitList = new ArrayList<>();

    /**
     * 是否通过
     */
    private Boolean isPass = false;

    public DtoMarkersData(List<String> ids,  Boolean isPass) {
        this.ids = ids;
        this.isPass = isPass;
    }

    public DtoMarkersData() {
    }
}
