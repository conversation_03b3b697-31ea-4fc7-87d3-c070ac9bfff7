package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportInstrument;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoImportInstrumentExpend;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.ImportInstrumentService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.InstrumentVerifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪器导入
 *
 * <AUTHOR>
 * @version V1.0.0 2022/3/23
 * @since V100R001
 */
@Service
@Slf4j
public class ImportInstrumentServiceImpl implements ImportInstrumentService {

    private InstrumentRepository instrumentRepository;

    private DepartmentService departmentService;

    private CodeService codeService;

    private ImportUtils importUtils;

    private PersonRepository personRepository;

    private InstrumentService instrumentService;

    /**
     * 处理导入表格
     *
     * @param file      传入的文件
     * @param objectMap 业务参数
     * @return RestResponse
     */
    @Override
    @Transactional
    public List<DtoInstrument> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //是否导入仪器类型
        Boolean isImportInsType = (Boolean) objectMap.get(0);
        //获取导入校验器
        InstrumentVerifyHandler verify = getVerify(file, isImportInsType);
        //获取导入结果
        ExcelImportResult<DtoImportInstrument> result = getExcelData(verify, file, response);
        //校验后需要导入的仪器数据
        List<DtoImportInstrument> importInstruments = result.getList();
        //删除空行
        importInstruments.removeIf(p -> StringUtil.isEmpty(p.getInstrumentsCode()));
        //判断文件中是否存在数据
        if (StringUtil.isEmpty(importInstruments)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        //获取数据库所有部门
        List<DtoDepartment> dbDepartment = departmentService.findAll();
        //获取数据库中所有的仪器类型
        List<DtoCode> dbInsType = codeService.findCodes("LIM_InstrumentType");
        //处理是否导入仪器类型
        if (isImportInsType) {
            //导入不包含的仪器类型
            importInsType(dbInsType, file);
        }
        //导入数据库
        List<DtoInstrument> instruments = new ArrayList<>();
        for (DtoImportInstrument importInstrument : importInstruments) {
            String id = personRepository.findPersonIdByCName(importInstrument.getManagerName());
            List<String> deptIds = dbDepartment.stream().filter(p -> importInstrument.getDeptName().equals(p.getDeptName())).map(DtoDepartment::getId).collect(Collectors.toList());
            DtoInstrument instrument = new DtoInstrument();
            instrument.setManager(StringUtil.isNotEmpty(id) ? id : "");
            instrument.setBelongDeptId(StringUtil.isEmpty(deptIds) ? UUIDHelper.GUID_EMPTY : deptIds.get(0));
            instrument.importToEntity(importInstrument);
            instrument.setPurchaseDate(StringUtils.isNotNullAndEmpty(importInstrument.getPurchaseDate()) ? importUtils.stringToDateAllFormat(importInstrument.getPurchaseDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
            instrument.setOriginDate(StringUtils.isNotNullAndEmpty(importInstrument.getOriginDate()) ? importUtils.stringToDateAllFormat(importInstrument.getOriginDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
            instrument.setInspectDate(StringUtils.isNotNullAndEmpty(importInstrument.getInspectDate()) ? importUtils.stringToDateAllFormat(importInstrument.getInspectDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
            instrument.setMaintenanceDate(StringUtils.isNotNullAndEmpty(importInstrument.getMaintenanceDate()) ? importUtils.stringToDateAllFormat(importInstrument.getMaintenanceDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
            instrument.setRecentOpenDate(StringUtils.isNotNullAndEmpty(importInstrument.getRecentOpenDate()) ? importUtils.stringToDateAllFormat(importInstrument.getRecentOpenDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
            Date originEndDate = getOriginEndDate(instrument.getOriginDate(), instrument.getOriginCyc());
            if (StringUtil.isNotNull(originEndDate)) {
                instrument.setOriginEndDate(originEndDate);
            }
            instruments.add(instrument);
        }
        addData(instruments);
        return instrumentRepository.findAll();
    }

    /**
     * 批量添加数据
     *
     * @param instruments 需要添加的数据
     */
    @Override
    public void addData(List<DtoInstrument> instruments) {
        instrumentRepository.save(instruments);
    }


    /**
     * 获取文件需要导入的数据
     *
     * @param verifyHandler 校验器
     * @param file          传入的文件
     * @param response      响应体
     * @return 导入结果
     */
    @Override
    public ExcelImportResult<DtoImportInstrument> getExcelData(IExcelVerifyHandler<DtoImportInstrument> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        params.setVerifyHandler(verifyHandler);
        ExcelImportResult<DtoImportInstrument> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoImportInstrument.class, params);

        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "仪器导入错误信息");
            failWorkbook.removeSheetAt(1);
            PoiExcelUtils.downLoadExcel("仪器导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 获取导入数据
     *
     * @param file 传入的文件
     * @return List
     */
    @Override
    public ExcelImportResult<DtoImportInstrument> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    /**
     * 获取仪器导入校验器
     *
     * @param file            导入文件
     * @param isImportInsType 是否同步导入仪器类型
     * @return 导入校验器
     */
    private InstrumentVerifyHandler getVerify(MultipartFile file, Boolean isImportInsType) {
        Map<String, Boolean> relationMap = new HashMap<>();
        relationMap.put("isImportInsType", isImportInsType);
        //需要导入的部门
        List<DtoImportInstrumentExpend> importDeptNames = importUtils.getImportNames(file, DtoImportInstrumentExpend.class);
        return new InstrumentVerifyHandler(
                relationMap, departmentService.findAll(),
                codeService.findCodes("LIM_InstrumentType"),
                instrumentService.findAll(), importDeptNames);
    }

    //region 导入关联数据

    /**
     * 导入仪器类型
     *
     * @param dbInsType 数据库的所有仪器类型
     * @param file      传入的文件
     */
    private void importInsType(List<DtoCode> dbInsType, MultipartFile file) {
        //需要导入的部门
        List<DtoImportInstrumentExpend> importInsNames = importUtils.getImportNames(file, DtoImportInstrumentExpend.class);
        //数据库中所有的部门Id
        List<String> InsTypeNames = dbInsType.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //不包含的部门(需要导入的部门)
        List<String> isImportInsTypes = importInsNames.stream().map(p -> StringUtil.isEmpty(p.getInstrumentTypeName()) ? null : p.getInstrumentTypeName()).collect(Collectors.toList());
        importUtils.createCodes(isImportInsTypes, "LIM_InstrumentType", InsTypeNames);
    }

    /**
     * 获取相应的有效期
     *
     * @param originDate 开始时间
     * @param originCyc  周期
     * @return 返回想要的数据
     */
    private Date getOriginEndDate(Date originDate, BigDecimal originCyc) {
        if (StringUtil.isNotNull(originDate) && StringUtil.isNotNull(originCyc)) {
            // 溯源日期为"1753-01-01" 不计算有效期
            if (importUtils.stringToDateAllFormat("1753-01-01").compareTo(originDate) == 0) {
                return null;
            }
            Calendar c = Calendar.getInstance();
            c.setTime(originDate);
            c.set(Calendar.HOUR_OF_DAY, 23);
            c.set(Calendar.MINUTE, 59);
            c.set(Calendar.SECOND, 59);
            c.add(Calendar.MONTH, originCyc.intValue());
            //有效期 = 溯源日期+溯源周期-1
            c.add(Calendar.DAY_OF_YEAR, -1);
            return c.getTime();
        }
        return null;
    }

    @Autowired
    public void setInstrumentRepository(InstrumentRepository instrumentRepository) {
        this.instrumentRepository = instrumentRepository;
    }

    @Autowired
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setInstrumentService(InstrumentService instrumentService) {
        this.instrumentService = instrumentService;
    }
}
