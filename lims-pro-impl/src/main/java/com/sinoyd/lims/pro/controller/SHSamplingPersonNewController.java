package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.SHSamplingPersonNewCriteria;
import com.sinoyd.lims.pro.dto.DtoSHSamplingPersonNew;
import com.sinoyd.lims.pro.service.SHSamplingPersonNewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SHSamplingPerson服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/28
 * @since V100R001
 */
@Api(tags = "示例: SHSamplingPerson服务")
@RestController
@RequestMapping("api/pro/SHSamplingPersonNew")
public class SHSamplingPersonNewController extends BaseJpaController<DtoSHSamplingPersonNew,String, SHSamplingPersonNewService> {

    @ApiOperation(value = "监管平台采样人员列表", notes = "监管平台采样人员列表")
    @GetMapping
    public RestResponse<List<DtoSHSamplingPersonNew>> find(SHSamplingPersonNewCriteria criteria){
        PageBean<DtoSHSamplingPersonNew> page = super.getPageBean();
        RestResponse<List<DtoSHSamplingPersonNew>> response = new RestResponse<>();
        service.findByPage(page,criteria);
        response.setData(page.getData());
        response.setCount(page.getRowsCount());
        return response;
    }

    /**
     * "根据id批量删除DtoSHSamplingPersonNew
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    @ApiOperation(value = "批量新增", notes = "批量新增")
    @PostMapping
    public RestResponse<List<DtoSHSamplingPersonNew>> save(@RequestBody List<DtoSHSamplingPersonNew> samplingPeople) {
        RestResponse<List<DtoSHSamplingPersonNew>> response = new RestResponse<>();
        response.setData(service.save(samplingPeople));
        return response;
    }

}
