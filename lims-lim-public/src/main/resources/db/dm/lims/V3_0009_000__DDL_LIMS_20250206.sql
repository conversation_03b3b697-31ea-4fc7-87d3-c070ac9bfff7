CREATE TABLE TB_QA_PlanInternalAudit
(
    ID           VARCHAR(50)                                                 NOT NULL,
    ANNUALPLANID VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    AUDITTIME    TIMESTAMP(0)                                                NOT NULL,
    ATTENDEE     VARCHAR(2000)                                               NOT NULL,
    AUDITPURP    VARCHAR(2000)                                               NOT NULL,
    AUDITSCOPE   VARCHAR(2000)                                               NOT NULL,
    AUDITCONTENT VARCHAR(2000)                                               NOT NULL,
    AUDITGIST    VARCHAR(2000)                                               NOT NULL,
    STATUS       VARCHAR(50),
    ISDELETED    BIT          DEFAULT 0                                      NOT NULL,
    ORGID        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    CREATOR      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    CREATEDATE   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    DOMAINID     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    MODIFIER     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    MODIFYDATE   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT          CLUSTER PRIMARY KEY(ID)
) STORAGE(ON MAIN, CLUSTERBTR);

COMMENT ON TABLE TB_QA_PlanInternalAudit IS '内审计划信息';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.ID IS '主键';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.ANNUALPLANID IS '年度计划id';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.AUDITTIME IS '审核时间';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.ATTENDEE IS '审核参与人员';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.AUDITPURP IS '审核目的';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.AUDITSCOPE IS '审核范围';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.AUDITCONTENT IS '审核内容';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.AUDITGIST IS '审核依据';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.STATUS IS '状态';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.ISDELETED IS '是否删除';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.ORGID IS '组织机构id';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.CREATOR IS '创建人';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.CREATEDATE IS '创建时间';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.DOMAINID IS '所属实验室';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.MODIFIER IS '修改人';
COMMENT ON COLUMN TB_QA_PlanInternalAudit.MODIFYDATE IS '修改时间';


CREATE TABLE TB_QA_INTERNALAUDITDIVIDETHEWORK
(
    ID           VARCHAR(50)                                                  NOT NULL,
    AUDITTIME    TIMESTAMP(0)                                                 NOT NULL,
    AUDITEDDEPT  VARCHAR(100)  DEFAULT ''                                     NOT NULL,
    AUDITELEMENT VARCHAR(2000) DEFAULT '',
    AUDITLEADER  VARCHAR(50)   DEFAULT ''                                     NOT NULL,
    AUDITOR      VARCHAR(2000) DEFAULT ''                                     NOT NULL,
    ISDELETED    BIT           DEFAULT 0                                      NOT NULL,
    STATUS       VARCHAR(50)   DEFAULT '',
    ORGID        VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    CREATOR      VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    CREATEDATE   TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    DOMAINID     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    MODIFIER     VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    MODIFYDATE   TIMESTAMP(0)  DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    AUDITPLANID  VARCHAR(50)   DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT          CLUSTER PRIMARY KEY(ID)
) STORAGE(ON MAIN, CLUSTERBTR);

COMMENT ON TABLE TB_QA_INTERNALAUDITDIVIDETHEWORK IS '内审分工';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.AUDITTIME IS '审核时间';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.AUDITEDDEPT IS '审核部门';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.AUDITELEMENT IS '审核要素';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.AUDITLEADER IS '内审组长';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.AUDITOR IS '内审员';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.ISDELETED IS '是否删除';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.ORGID IS '组织机构id';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.CREATOR IS '创建人';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.CREATEDATE IS '创建时间';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.DOMAINID IS '所属实验室';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.MODIFIER IS '修改人';
COMMENT ON COLUMN TB_QA_INTERNALAUDITDIVIDETHEWORK.MODIFYDATE IS '修改时间';



ALTER TABLE TB_QA_NOTCONFORMITEM
    add column auditDivideTheWorkId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';
COMMENT
ON COLUMN TB_QA_NOTCONFORMITEM.auditDivideTheWorkId IS '内审分工Id';


