package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentInspect;

import java.util.Collection;
import java.util.List;

/**
 * 仪器期间勘察操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
public interface InstrumentInspectRepository extends IBaseJpaPhysicalDeleteRepository<DtoInstrumentInspect, String> {

    /**
     * 根据仪器id获取期间核查记录
     *
     * @param instrumentIds 仪器id
     */
    List<DtoInstrumentInspect> findByInstrumentIdIn(Collection<String> instrumentIds);

}