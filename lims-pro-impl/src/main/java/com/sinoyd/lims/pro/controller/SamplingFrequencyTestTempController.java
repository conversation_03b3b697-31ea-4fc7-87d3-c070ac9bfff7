package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.lims.pro.service.SampleFolderTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;


/**
 * 频次指标模板接口定义
 * <AUTHOR>
 * @version V1.0.0 2023/08/08
 * @since V100R001
 */
@Api(tags = "示例: SamplingFrequencyTestTemp服务")
@RestController
@RequestMapping("api/pro/samplingFrequencyTestTemplate")
public class SamplingFrequencyTestTempController extends ExceptionHandlerController<SampleFolderTemplateService> {

    @ApiOperation(value = "新增频次指标", notes = "新增频次指标")
    @PostMapping
    public RestResponse<Void> create(@RequestBody DtoSamplingFrequencyTest samplingFrequencyTest) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.addFrequencyAnalyseItems(samplingFrequencyTest.getSamplingFrequencyIds(), samplingFrequencyTest.getAnalyseItemIds(), samplingFrequencyTest.getTestIds());
        return restResponse;
    }


    @ApiOperation(value = "修改频次指标", notes = "修改频次指标")
    @PutMapping
    public RestResponse<Void> update(@RequestBody DtoSamplingFrequencyTest samplingFrequencyTest) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.modifySamplingFrequencyTest(samplingFrequencyTest.getSamplingFrequencyId(), samplingFrequencyTest.getAnalyseItemIds(), samplingFrequencyTest.getTestIds());
        return restResponse;
    }


    @ApiOperation(value = "批量删除频次指标", notes = "批量删除频次指标")
    @DeleteMapping
    public RestResponse<Boolean> delete(@RequestBody DtoSamplingFrequencyTest samplingFrequencyTest) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.deleteFrequencyAnalyseItems(samplingFrequencyTest.getSamplingFrequencyIds(),samplingFrequencyTest.getAnalyseItemIds());
        restResp.setData(true);
        return restResp;
    }

}
