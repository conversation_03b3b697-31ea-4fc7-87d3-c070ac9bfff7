package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * InstrumentUseRecord2Sample实体
 * <AUTHOR>
 * @version V1.0.0 2024/03/05
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="EnvironmentalRecord2Sample")
 @Data
 public  class EnvironmentalRecord2Sample implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;


    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 仪器使用信息id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("仪器使用记录id")
	private String environmentalRecordId;
    
    /**
    * 样品id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("样品id")
	private String sampleId;
    
 }