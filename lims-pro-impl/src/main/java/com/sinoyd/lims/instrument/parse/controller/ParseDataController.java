package com.sinoyd.lims.instrument.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.instrument.parse.dto.DtoParseData;
import com.sinoyd.lims.instrument.parse.service.ParseDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 仪器解析数据用户接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/9/7
 */
@Api(tags = "仪器解析数据用户接口")
@RestController
@RequestMapping("api/pro/parseData")
public class ParseDataController extends BaseJpaController<DtoParseData, String, ParseDataService> {

    /**
     * 根据日志id查询仪器解析数据
     *
     * @param logId 日志id
     * @return RestResponse<List < DtoParseData>>
     */
    @ApiOperation(value = "根据日志id查询仪器解析数据", notes = "根据日志id查询仪器解析数据")
    @GetMapping("/{logId}")
    public RestResponse<List<DtoParseData>> findByLogId(@PathVariable("logId") String logId, String gatherCode, String analyzeItemName, String paramName) {
        RestResponse<List<DtoParseData>> restResponse = new RestResponse<>();
        List<DtoParseData> parseDataList = service.findParseData(logId, gatherCode, analyzeItemName, paramName);
        restResponse.setRestStatus(StringUtil.isEmpty(parseDataList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(parseDataList);
        return restResponse;
    }

    /**
     * 检查工作单是否保存过
     *
     * @param worksheetFolderId 工作单id
     * @return RestResponse<Boolean> true:保存过; false:未保存
     */
    @ApiOperation(value = "根据日志id查询仪器解析数据", notes = "根据日志id查询仪器解析数据")
    @GetMapping("/check/{worksheetFolderId}")
    public RestResponse<Boolean> checkWorksheetFolderSaved(@PathVariable("worksheetFolderId") String worksheetFolderId) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.checkDuplicateSync(worksheetFolderId));
        return restResponse;
    }
}