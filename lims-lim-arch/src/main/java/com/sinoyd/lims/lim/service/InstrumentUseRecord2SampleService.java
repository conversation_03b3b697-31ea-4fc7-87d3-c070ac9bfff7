package com.sinoyd.lims.lim.service;

import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord2Sample;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * InstrumentUseRecord2Sample操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
public interface InstrumentUseRecord2SampleService extends IBaseJpaService<DtoInstrumentUseRecord2Sample, String> {

    /**
     * 根据仪器使用记录id 获取相关的样品数据
     *
     * @param instrumentUseRecordId 仪器使用id
     * @return 返回相关的样品数据
     */
    List<DtoInstrumentUseRecord2Sample> findByInstrumentUseRecordId(String instrumentUseRecordId);

}