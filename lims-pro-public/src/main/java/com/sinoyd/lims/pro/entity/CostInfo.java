package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import java.math.BigDecimal;


/**
 * CostInfo实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="CostInfo")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class CostInfo implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  CostInfo() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目id")
	private String projectId;
    
    /**
    * 采样总费
    */
    @Column(nullable=false)
    @ApiModelProperty("采样总费")
	private BigDecimal samplingCost= BigDecimal.valueOf(0);
    
    /**
    * 分析总费
    */
    @Column(nullable=false)
    @ApiModelProperty("分析总费")
	private BigDecimal analyzeCost= BigDecimal.valueOf(0);
    
    /**
    * 报告费
    */
    @Column(nullable=false)
    @ApiModelProperty("报告费")
	private BigDecimal reportCost= BigDecimal.valueOf(0);
    
    /**
    * 登高费
    */
    @Column(nullable=false)
    @ApiModelProperty("登高费")
	private BigDecimal climbCost= BigDecimal.valueOf(0);
    
    /**
    * 专家费
    */
    @Column(nullable=false)
    @ApiModelProperty("专家费")
	private BigDecimal expertCost= BigDecimal.valueOf(0);
    
    /**
    * 其它费
    */
    @Column(nullable=false)
    @ApiModelProperty("其它费")
	private BigDecimal otherCost= BigDecimal.valueOf(0);
    
    /**
    * 人数
    */
    @Column(nullable=false)
    @ApiModelProperty("人数")
	private Integer laborNum=1;
    
    /**
    * 人天
    */
    @Column(nullable=false)
    @ApiModelProperty("人天")
	private Integer laborDay=1;
    
    /**
    * 元/人天
    */
    @Column(nullable=false)
    @ApiModelProperty("元/人天")
	private BigDecimal laborUnit= BigDecimal.valueOf(0);
    
    /**
    * 人工费
    */
    @Column(nullable=false)
    @ApiModelProperty("人工费")
	private BigDecimal laborCost= BigDecimal.valueOf(0);
    
    /**
    * 车辆数
    */
    @Column(nullable=false)
    @ApiModelProperty("车辆数")
	private Integer carNum=1;
    
    /**
    * 车辆天
    */
    @Column(nullable=false)
    @ApiModelProperty("车辆天")
	private Integer carDay=1;
    
    /**
    * 元/辆天
    */
    @Column(nullable=false)
    @ApiModelProperty("元/辆天")
	private BigDecimal carUnit= BigDecimal.valueOf(0);
    
    /**
    * 车辆费
    */
    @Column(nullable=false)
    @ApiModelProperty("车辆费")
	private BigDecimal carCost= BigDecimal.valueOf(0);
    
    /**
    * 总计
    */
    @Column(nullable=false)
    @ApiModelProperty("总计")
	private BigDecimal expectedTotalCost= BigDecimal.valueOf(0);
    
    /**
    * 报告折扣率
    */
    @Column(nullable=false)
    @ApiModelProperty("报告折扣率")
	private BigDecimal reportRate= BigDecimal.valueOf(0);
    
    /**
    * 折扣率
    */
    @Column(nullable=false)
    @ApiModelProperty("折扣率")
	private BigDecimal offerRate= BigDecimal.valueOf(0);
    
    /**
    * 税收管理费
    */
    @Column(nullable=false)
    @ApiModelProperty("税收管理费")
	private BigDecimal taxManageCost= BigDecimal.valueOf(0);
    
    /**
    * 报价
    */
    @Column(nullable=false)
    @ApiModelProperty("报价")
	private BigDecimal acturalTotalCost= BigDecimal.valueOf(0);
    
    /**
    * 方案变更状态（ 枚举EnumSchemeChangeStatus：0.未变更 1.已变更）
    */
    @Column(nullable=false)
    @ApiModelProperty("方案变更状态（ 枚举EnumSchemeChangeStatus：0.未变更 1.已变更）")
	private Integer schemeChangeStatus=0;
    
    /**
    * 状态（新建、审核中、审批中、审核不通过、审批不通过、已完成）
    */
    @Column(length=20,nullable=false)
    @ApiModelProperty("状态（新建、审核中、审批中、审核不通过、审批不通过、已完成）")
    //@NotBlank(message = "状态（新建、审核中、审批中、审核不通过、审批不通过、已完成）{validation.message.blank}")
    @Length(message = "状态（新建、审核中、审批中、审核不通过、审批不通过、已完成）{validation.message.length}", max = 20)
	private String status;
    
    /**
    * 假删字段
    */
    @Column(nullable=false)
    @ApiModelProperty("假删字段")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }