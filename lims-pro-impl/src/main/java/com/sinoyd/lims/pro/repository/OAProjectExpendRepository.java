package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoOAProjectExpend;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目支出数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
public interface OAProjectExpendRepository extends IBaseJpaRepository<DtoOAProjectExpend, String> {

    /**
     * 对项目支出进行确认
     *
     * @param id        项目ID
     * @param isConfirm 是否确认
     * @return 返回更新结果
     */
    @Transactional
    @Modifying
    @Query("update DtoOAProjectExpend a set a.isConfirm=:isConfirm where id=:id")
    Integer confirm(@Param("id") String id, @Param("isConfirm") Boolean isConfirm);
}
