package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * 质控信息
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Data
public class DataQualityControlVO {
    /**
     * id
     */
    private String id;

    /**
     * 质控id
     */
    private String qcId;

    /**
     * 关联样品id
     */
    private String associateSampleId;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 质控值
     */
    private String qcValue;

    /**
     * 加标体积
     */
    private String qcVolume;

    /**
     * 添加质控人员id
     */
    private String qaId;

    /**
     * 添加质控时间
     */
    private Date qcTime;

    /**
     * 测定值
     */
    private String qcTestValue;

    /**
     * 样值
     */
    private String realSampleTestValue;

    /**
     * 标样编号
     */
    private String qcCode;

    /**
     * 原样的检测结果
     */
    private String qcOriginValue;

    /**
     * 标样的有效期
     */
    private Date qcValidDate;

    /**
     * 标样的配置日期
     */
    private Date qcStandardDate;

    /**
     * 加标液浓度
     */
    private String qcConcentration;

    /**
     * 加标体积量纲
     */
    private String qcVolumeDimension;

    /**
     * 加入标准量量纲
     */
    private String qcValueDimension;

    /**
     * 测定值量纲
     */
    private String qcTestValueDimension;

    /**
     * 样值量纲
     */
    private String realSampleTestValueDimension;

    /**
     * 加标液浓度量纲
     */
    private String qcConcentrationDimension;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;

}
