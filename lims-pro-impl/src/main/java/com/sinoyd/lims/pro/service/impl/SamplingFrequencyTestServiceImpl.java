package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.lims.pro.repository.SamplingFrequencyRepository;
import com.sinoyd.lims.pro.repository.SamplingFrequencyTestRepository;
import com.sinoyd.lims.pro.service.SamplingFrequencyTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * 点位频次指标操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/9
 * @since V100R001
 */
 @Service
public class SamplingFrequencyTestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSamplingFrequencyTest,String,SamplingFrequencyTestRepository> implements SamplingFrequencyTestService {
    @Autowired
    private SamplingFrequencyRepository samplingFrequencyRepository;

    /**
     * 根据频次添加频次指标
     *
     * @param samplingFrequencyId 频次id
     * @param testList            测试项目列表
     */
    @Transactional
    @Override
    public void addSamplingFrequencyTest(String samplingFrequencyId, List<DtoTest> testList) {
        List<DtoSamplingFrequencyTest> sftList = repository.findBySamplingFrequencyId(samplingFrequencyId);
        DtoSamplingFrequency frequency = samplingFrequencyRepository.findOne(samplingFrequencyId);
        List<DtoSamplingFrequencyTest> list = new ArrayList<>();
        for (DtoTest test : testList) {
            if (sftList.stream().noneMatch(p -> p.getTestId().equals(test.getId()))) {
                DtoSamplingFrequencyTest sft = new DtoSamplingFrequencyTest(test);
                sft.setSamplingFrequencyId(frequency.getId());
                sft.setSampleFolderId(frequency.getSampleFolderId());
                list.add(sft);
            }
        }
        if (list.size() > 0) {
            repository.save(list);
        }
    }

    /**
     * 根据点位添加频次指标
     *
     * @param sampleFolderId      点位id
     * @param testList            测试项目列表
     */
    @Transactional
    @Override
    public void addSampleFolderTest(String sampleFolderId,List<DtoTest> testList) {
        List<DtoSamplingFrequencyTest> sftList = repository.findBySampleFolderId(sampleFolderId);
        List<DtoSamplingFrequency> frequencyList = samplingFrequencyRepository.findBySampleFolderId(sampleFolderId);
        List<DtoSamplingFrequencyTest> list = new ArrayList<>();
        for (DtoSamplingFrequency frequency : frequencyList) {
            for (DtoTest test : testList) {
                if (sftList.stream().noneMatch(p -> p.getSamplingFrequencyId().equals(frequency.getId()) && p.getTestId().equals(test.getId()))) {
                    DtoSamplingFrequencyTest sft = new DtoSamplingFrequencyTest(test);
                    sft.setSamplingFrequencyId(frequency.getId());
                    sft.setSampleFolderId(sampleFolderId);
                    list.add(sft);
                }
            }
        }
        if (list.size() > 0) {
            repository.save(list);
        }
    }
}