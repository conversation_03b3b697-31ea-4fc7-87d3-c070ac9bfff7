package com.sinoyd.base.factory;

import com.sinoyd.base.factory.quality.*;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.boot.common.exception.BaseException;

import java.util.HashMap;
import java.util.Map;

public class QualityTaskFactory {

    private static final QualityTaskFactory qtf = new QualityTaskFactory();

    public static Map<Integer, QualityControlKind> qcMap = new HashMap<>();

    static {
        qcMap.put(1, new QualityParallel());
        qcMap.put(2, new QualityBlank());
        qcMap.put(4, new QualityMark());
        qcMap.put(8, new QualityStandard());
        qcMap.put(16, new QualitySampleCopy());
        qcMap.put(32, new QualitySampleSeries());
        qcMap.put(64, new CurveCheck());
        qcMap.put(128, new QualityDetergent());
        qcMap.put(256, new QualityTransportBlank());
        qcMap.put(512, new QualityInstrumentBlank());
        qcMap.put(1024, new QualityReagentBlank());
        qcMap.put(2048, new QualityJarBlank());
        qcMap.put(4096, new QualityCorrectionFactor());
        qcMap.put(8192, new QualityReplace());
        qcMap.put(16384, new QualityNegativeControl());
        qcMap.put(32768, new QualityPositiveControl());
        qcMap.put(65536, new QualitySamplingContainerBlank());
        qcMap.put(131072, new QualityBlankMark());
        qcMap.put(262144, new QualityControlSample());
        qcMap.put(524288, new QualityControlReplaceSample());
        qcMap.put(1048576, new QualityLocalBlank());
        qcMap.put(2097152, new QualityCipherParallel());
        qcMap.put(4194304, new QualityDilutionWater());
    }

    private QualityTaskFactory(){}

    public static QualityTaskFactory getInstance(){
        return qtf;
    }

    public QualityControlKind getQcSample(Integer value){
        if(!qcMap.containsKey(value)){
            throw new BaseException("所传质控类型不是有效的，请确认");
        }
        return qcMap.get(value);
    }
}
