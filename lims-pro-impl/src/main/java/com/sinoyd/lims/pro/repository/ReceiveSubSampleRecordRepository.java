package com.sinoyd.lims.pro.repository;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.frame.util.EnumHelper;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.enums.EnumPRO;
import io.swagger.models.auth.In;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * ReceiveSubSampleRecord数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface ReceiveSubSampleRecordRepository extends IBaseJpaRepository<DtoReceiveSubSampleRecord, String> {

    /**
     * 查询对应送样单id下的领样单
     *
     * @param receiveId 送样单id
     * @return 对应送样单id下的领样单
     */
    List<DtoReceiveSubSampleRecord> findByReceiveId(String receiveId);

    /**
     * 查询对应送样单id下的领样单
     *
     * @param receiveIds 送样单id
     * @return 对应送样单id下的领样单
     */
    List<DtoReceiveSubSampleRecord> findByReceiveIdIn(Collection<String> receiveIds);

    /**
     * 批量办结领样单状态
     *
     * @param ids       领样单的ids
     * @param subStatus 领样单状态
     * @param status    领样单状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSubSampleRecord r set r.subStatus = :subStatus,r.status = :status where r.id in :ids")
    Integer finishSubRecord(@Param("ids") List<String> ids,
                            @Param("subStatus") Integer subStatus,
                            @Param("status") String status);

    /**
     * 批量修改领样单状态
     *
     * @param ids       领样单的ids
     * @param subStatus 领样单状态
     * @param status    领样单状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSubSampleRecord r set r.subStatus = :subStatus,r.status = :status where r.id in :ids")
    Integer updateStatus(@Param("ids") List<String> ids,
                         @Param("subStatus") Integer subStatus,
                         @Param("status") String status);

    /**
     * 批量现场复核领样单状态
     *
     * @param ids       领样单的ids
     * @param subStatus 领样单状态
     * @param status    领样单状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSubSampleRecord r set r.subStatus = :subStatus,r.status = :status,r.checkerId = :checkerId," +
            "r.checkerName = :checkerName,r.checkTime = :checkTime where r.id in :ids")
    Integer checkLocalSubRecord(@Param("ids") List<String> ids,
                                @Param("subStatus") Integer subStatus,
                                @Param("status") String status,
                                @Param("checkerId") String checkerId,
                                @Param("checkerName") String checkerName,
                                @Param("checkTime") Date checkTime);

    /**
     * 批量现场审核领样单状态
     *
     * @param ids       领样单的ids
     * @param subStatus 领样单状态
     * @param status    领样单状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSubSampleRecord r set r.subStatus = :subStatus,r.status = :status,r.auditorId = :auditorId," +
            "r.auditorName = :auditorName,r.auditTime = :auditTime where r.id in :ids")
    Integer auditLocalSubRecord(@Param("ids") List<String> ids,
                                @Param("subStatus") Integer subStatus,
                                @Param("status") String status,
                                @Param("auditorId") String auditorId,
                                @Param("auditorName") String auditorName,
                                @Param("auditTime") Date auditTime);

    /**
     * 按照送样单id删除领样单
     *
     * @param receiveIds 送样单id集合
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReceiveSubSampleRecord a  set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.receiveId in :receiveIds")
    Integer deleteByReceiveIdIn(@Param("receiveIds") List<String> receiveIds,
                                @Param("modifier") String modifier,
                                @Param("modifyDate") Date modifyDate);
}