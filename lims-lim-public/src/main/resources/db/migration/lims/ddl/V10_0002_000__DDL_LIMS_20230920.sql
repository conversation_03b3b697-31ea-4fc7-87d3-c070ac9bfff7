-- 采样安排计划表
CREATE TABLE TB_PRO_SamplingArrange
(
    id                  varchar(50)   NOT NULL                                                COMMENT '主键'          primary key,
    planSamplingTime    datetime      NOT NULL DEFAULT  CURRENT_TIMESTAMP                     COMMENT '计划采样时间',
    samplingFrequencyId varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位频次id',
    team                varchar(50)   NULL                                                    COMMENT '采样小组',
    teamId              varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '采样小组标识',
    chargePersonId      varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '采样负责人标识',
    chargePerson        varchar(50)   NOT NULL DEFAULT ''                                     COMMENT '采样负责人',
    samplingPeople      varchar(255)  NULL                                                    COMMENT '采样人员',
    samplingPeopleIds   varchar(1000) NULL                                                    COMMENT '采样人员标识 ; 分割',
    car                 varchar(50)   NULL                                                    COMMENT '采样车辆',
    carId               varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '采样车辆标识',
    orgId               varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id'
) comment '采样安排计划表' ENGINE = InnoDB  CHARACTER SET = utf8  COLLATE = utf8_general_ci  ROW_FORMAT = Dynamic;
-- 点位批次表新增标记字段
ALTER TABLE tb_pro_samplingfrequency ADD COLUMN isArrange bit NOT NULL DEFAULT b'0' COMMENT '计划安排状态 0否 1是';
