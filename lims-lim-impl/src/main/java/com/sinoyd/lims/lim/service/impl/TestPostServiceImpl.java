package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.AuthorizeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.TestPostCriteria;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.entity.TestPost2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestPost2PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestPost2TestRepository;
import com.sinoyd.lims.lim.repository.lims.TestPostRepository;
import com.sinoyd.lims.lim.service.TestPostService;
import com.sinoyd.lims.lim.service.TestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.sinoyd.base.dto.rcc.DtoSampleType;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试岗位管理
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
@Service
@Slf4j
public class TestPostServiceImpl extends BaseJpaServiceImpl<DtoTestPost, String, TestPostRepository> implements TestPostService {

    private TestPost2PersonRepository testPost2PersonRepository;

    private TestPost2TestRepository testPost2TestRepository;

    private PersonRepository personRepository;

    private SampleTypeRepository sampleTypeRepository;

    private AuthorizeService authorizeService;

    private TestService testService;

    @Override
    public DtoTestPost findTestPost(String id, BaseCriteria criteria) {
        DtoTestPost testPost = repository.findOne(id);
        if (StringUtil.isNull(testPost)) {
            throw new BaseException("测试岗位不存在或已删除！");
        }
        //获取人员配置
        List<DtoPerson> personList = new ArrayList<>();
        List<DtoTestPost2Person> testPost2PersonList = testPost2PersonRepository.findByTestPostId(testPost.getId());
        if (StringUtil.isNotEmpty(testPost2PersonList)) {
            List<String> personIdList = testPost2PersonList.stream().map(DtoTestPost2Person::getPersonId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(personIdList)) {
                personList = personRepository.findAll(personIdList);
            }
        }
        personList.sort(Comparator.comparing(DtoPerson::getOrderNum).reversed());
        testPost.setPersonList(personList);
        //获取测试项目配置
        List<DtoTest> testList = new ArrayList<>();
        List<DtoTestPost2Test> testPost2TestList = testPost2TestRepository.findByTestPostId(testPost.getId());
        if (StringUtil.isNotEmpty(testPost2TestList)) {
            List<String> testIdList = testPost2TestList.stream().map(TestPost2Test::getTestId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(testIdList)) {
                testList = testService.findAll(testIdList);
            }
        }
        List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll();
        Map<String, DtoSampleType> sampleTypeMap = StringUtil.isNotEmpty(sampleTypeList)
                ? sampleTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, dto -> dto)) : new HashMap<>();

        testList.forEach(p -> {
            p.setTestPostName(testPost.getPostName());
            p.setTestPostId(testPost.getId());
            p.setSampleTypeName(sampleTypeMap.containsKey(p.getSampleTypeId()) ? sampleTypeMap.get(p.getSampleTypeId()).getTypeName() : "");
        });
        testList.sort(Comparator.comparing(DtoTest::getOrderNum).reversed());
        //过滤测试岗位下配置的测试项目
        TestPostCriteria testPostCriteria = (TestPostCriteria) criteria;
        String analyseItemKey = testPostCriteria.getAnalyzeItemKey();
        if (StringUtil.isNotEmpty(analyseItemKey)) {
            testList = testList.stream().filter(p -> p.getRedAnalyzeItemName().contains(analyseItemKey)
                    || (StringUtil.isNotEmpty(p.getFullPinYin()) && p.getFullPinYin().contains(analyseItemKey))
                    || (StringUtil.isNotEmpty(p.getPinYin()) && p.getPinYin().contains(analyseItemKey))).collect(Collectors.toList());
        }
        String analyseMethodKey = testPostCriteria.getAnalyzeMethodKey();
        if (StringUtil.isNotEmpty(analyseMethodKey)) {
            testList = testList.stream().filter(p -> p.getRedAnalyzeMethodName().contains(analyseMethodKey) || p.getRedCountryStandard().contains(analyseMethodKey)).collect(Collectors.toList());
        }
        testPost.setTestList(testList);
        return testPost;
    }

    @Override
    @Transactional
    public DtoTestPost updateConfig(DtoTestPost entity) {
        DtoTestPost oriTestPost = repository.findOne(entity.getId());
        if (StringUtil.isNull(oriTestPost)) {
            throw new BaseException("测试岗位不存在或已删除！");
        }
        List<String> toUptPersonIdList = StringUtil.isNotNull(entity.getPersonIdList())
                ? entity.getPersonIdList() : new ArrayList<>();
        //删除原来的人员配置
        testPost2PersonRepository.deleteByTestPostIdIn(Collections.singletonList(entity.getId()));
        List<DtoTestPost2Person> instTestPost2PersonList = new ArrayList<>();
        for (String personId : toUptPersonIdList) {
            DtoTestPost2Person testPost2Person = new DtoTestPost2Person();
            testPost2Person.setTestPostId(entity.getId());
            testPost2Person.setPersonId(personId);
            instTestPost2PersonList.add(testPost2Person);
        }
        //插入新的人员配置
        if (StringUtil.isNotEmpty(instTestPost2PersonList)) {
            testPost2PersonRepository.save(instTestPost2PersonList);
        }
//        List<String> toUptTestIdList = StringUtil.isNotNull(entity.getTestIdList())
//                ? entity.getTestIdList() : new ArrayList<>();
//        //删除原来的测试项目配置
//        testPost2TestRepository.deleteByTestPostIdIn(Collections.singletonList(entity.getId()));
//        List<DtoTestPost2Test> instTestPost2TestList = new ArrayList<>();
//        for (String testId : toUptTestIdList) {
//            DtoTestPost2Test testPost2Test = new DtoTestPost2Test();
//            testPost2Test.setTestPostId(entity.getId());
//            testPost2Test.setTestId(testId);
//            instTestPost2TestList.add(testPost2Test);
//        }
//        //插入新的测试项目配置
//        if (StringUtil.isNotEmpty(instTestPost2TestList)) {
//            testPost2TestRepository.save(instTestPost2TestList);
//        }
        oriTestPost.setPostType(entity.getPostType());
        oriTestPost.setPostCode(entity.getPostCode());
        oriTestPost.setCar(entity.getCar());
        oriTestPost.setCarId(entity.getCarId());
        oriTestPost.setChargePerson(entity.getChargePerson());
        oriTestPost.setChargePersonId(entity.getChargePersonId());
        super.save(oriTestPost);
        return entity;
    }

    @Override
    @Transactional
    public void deleteTestPost(String id) {
        //删除人员配置
        testPost2PersonRepository.deleteByTestPostIdIn(Collections.singletonList(id));
        //删除测试项目配置
        testPost2TestRepository.deleteByTestPostIdIn(Collections.singletonList(id));
        super.logicDeleteById(id);
    }

    @Override
    @Transactional
    public void deleteTestPost(List<String> ids) {
        if (StringUtil.isNotEmpty(ids)) {
            List<DtoTestPost> testPostList = repository.findAll(ids);
            if (StringUtil.isNotEmpty(testPostList)) {
                List<String> testPostIdList = testPostList.stream().map(DtoTestPost::getId).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(testPostIdList)) {
                    //删除人员配置
                    testPost2PersonRepository.deleteByTestPostIdIn(testPostIdList);
                    //删除测试项目配置
                    testPost2TestRepository.deleteByTestPostIdIn(testPostIdList);
                }
                super.logicDeleteById(ids);
            }
        }
    }

    @Override
    @Transactional
    public DtoTestPost save(DtoTestPost entity) {
        DtoTestPost testPost = repository.findByPostNameAndPostTypeAndIsDeletedFalse(entity.getPostName(), entity.getPostType());
        if (StringUtil.isNotNull(testPost)) {
            throw new BaseException("岗位名称或类型重复!");
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoTestPost update(DtoTestPost entity) {
        if (StringUtil.isEmpty(entity.getId())) {
            throw new BaseException("测试岗位不存在!");
        }
        DtoTestPost oriTestPost = repository.findOne(entity.getId());
        if (StringUtil.isNull(oriTestPost)) {
            throw new BaseException("测试岗位不存在!");
        }
        //判断岗位名称类型是否重复
        DtoTestPost dupTestPost = repository.findByPostNameAndPostTypeAndIsDeletedFalse(entity.getPostName(), entity.getPostType());
        if (StringUtil.isNotNull(dupTestPost) && !dupTestPost.getId().equals(entity.getId())) {
            throw new BaseException("岗位名称或类型重复!");
        }
        return super.update(entity);
    }

    @Override
    public List<DtoTestPost> getTestPostTree() {
        List<DtoTestPost> testPostList = repository.findAll();
        testPostList.sort(Comparator.comparing(DtoTestPost::getOrderNum).reversed());
        return testPostList;
    }

    @Override
    public List<DtoTestPost> findByPerson(String personId) {
        List<DtoTestPost> testPostList = new ArrayList<>();
        String permission = "LIMS_PRO_Position_All";
        boolean accessAllPost = false;
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            String userId = PrincipalContextUser.getPrincipal().getUserId();
            accessAllPost = authorizeService.haveActionPermission(userId, permission);
        }
        if (accessAllPost) {
            //当前登录人有查看所有岗位的权限，则直接返回所有岗位信息
            testPostList = repository.findAll().stream().filter(t -> EnumLIM.EnumPostType.分析.getValue().equals(t.getPostType())).collect(Collectors.toList());
            testPostList.sort(Comparator.comparing(DtoTestPost::getOrderNum).reversed());
            return testPostList;
        }
        List<DtoTestPost2Person> testPost2PersonList = testPost2PersonRepository.findByPersonId(personId);
        List<String> testPostIdList = testPost2PersonList.stream().map(DtoTestPost2Person::getTestPostId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(testPostIdList)) {
            testPostList = repository.findAll(testPostIdList).stream().filter(t -> EnumLIM.EnumPostType.分析.getValue().equals(t.getPostType())).collect(Collectors.toList());
            testPostList.sort(Comparator.comparing(DtoTestPost::getOrderNum).reversed());
        }
        return testPostList;
    }

    @Override
    public void findByPage(PageBean<DtoTestPost> page, BaseCriteria criteria) {
        page.setEntityName("DtoTestPost a");
        page.setSelect("select a ");
        super.findByPage(page, criteria);
        List<DtoTestPost> list = page.getData();
        //关联人员数据
        List<DtoTestPost2Person> testPost2PersonList = testPost2PersonRepository.findAll();
        List<String> testPost2PersonIdList = testPost2PersonList.stream().map(DtoTestPost2Person::getPersonId).collect(Collectors.toList());
        List<DtoPerson> dtoPersonList = new ArrayList<>();
        if(!testPost2PersonIdList.isEmpty()){
            dtoPersonList = personRepository.findAll(testPost2PersonIdList);
        }
        //获取人员配置
        for (DtoTestPost dtoTestPost:list) {
            List<String> personIdList = testPost2PersonList.stream().filter(t->dtoTestPost.getId().equals(t.getTestPostId()))
                    .map(DtoTestPost2Person::getPersonId).collect(Collectors.toList());
            List<DtoPerson> personList = dtoPersonList.stream().filter(p->personIdList.contains(p.getId())).collect(Collectors.toList());
            personList.sort(Comparator.comparing(DtoPerson::getOrderNum).reversed());
            dtoTestPost.setPersonList(personList);
        }
        page.setData(list);
    }

    @Override
    @Transactional
    public void deleteTest(String testPostId, List<String> testIds) {
        testPost2TestRepository.deleteByTestPostIdAndTestIdIn(testPostId, testIds);
    }

    @Override
    @Transactional
    public void addTest(String testPostId, List<String> testIds) {
        List<DtoTestPost2Test> saveList = new ArrayList<>();
        if (StringUtil.isNotEmpty(testPostId) && !UUIDHelper.GUID_EMPTY.equals(testPostId) && StringUtil.isNotEmpty(testIds)) {
            for (String testId : testIds) {
                DtoTestPost2Test save = new DtoTestPost2Test();
                save.setTestId(testId);
                save.setTestPostId(testPostId);
                saveList.add(save);
            }
        }
        if (StringUtil.isNotEmpty(saveList)) {
            testPost2TestRepository.save(saveList);
        }
    }

    @Autowired
    public void setTestPost2PersonRepository(TestPost2PersonRepository testPost2PersonRepository) {
        this.testPost2PersonRepository = testPost2PersonRepository;
    }

    @Autowired
    public void setTestPost2TestRepository(TestPost2TestRepository testPost2TestRepository) {
        this.testPost2TestRepository = testPost2TestRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setAuthorizeService(AuthorizeService authorizeService) {
        this.authorizeService = authorizeService;
    }
}