package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.TestStatisticsCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseStatisticsData;
import com.sinoyd.lims.pro.service.TestStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 检测数据服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/07/08
 * @since V100R001
 */
@Api(tags = "示例: Survey服务")
@RestController
@RequestMapping("api/pro/testStatistics")
public class TestStatisticsController extends ExceptionHandlerController<TestStatisticsService> {
    /**
     * 分页查询数据
     * @param criteria 查询条件
     * @return 查询结果
     */
    @ApiModelProperty(name = "分页查询数据",notes = "分页查询数据")
    @GetMapping
    public RestResponse<List<DtoAnalyseStatisticsData>> findByPage(TestStatisticsCriteria criteria){
        RestResponse<List<DtoAnalyseStatisticsData>> restResponse = new RestResponse<>();
        PageBean<DtoAnalyseStatisticsData> pageBean = super.getPageBean();
        service.findTestData(pageBean,criteria);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 分页查询数据
     * @param criteria 查询条件
     * @return 查询结果
     */
    @ApiModelProperty(name = "分页查询数据",notes = "分页查询数据")
    @GetMapping("/circularChart")
    public RestResponse<Map<String,Object>> findCircularChart(TestStatisticsCriteria criteria){
        RestResponse<Map<String,Object>> restResponse = new RestResponse<>();
        PageBean<DtoAnalyseStatisticsData> pageBean = super.getPageBean();
        Map<String, Object> circularChart = service.findCircularChart(pageBean, criteria);
        restResponse.setData(circularChart);
        return restResponse;
    }
}
