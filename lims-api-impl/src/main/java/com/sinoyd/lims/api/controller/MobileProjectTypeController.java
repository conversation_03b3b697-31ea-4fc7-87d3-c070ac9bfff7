package com.sinoyd.lims.api.controller;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 项目类型服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/10/23
 * @since V100R001
 */
 @Api(tags = "项目类型: 项目类型服务")
 @RestController
 @RequestMapping("api/mobile/projectType")
 @Validated
 public class MobileProjectTypeController extends BaseJpaController<DtoProjectType, String,ProjectTypeService> {

    /**
     * 返回项目类型的树
     *
     * @return
     */
    @ApiOperation(value = "根据编码获取项目类型树结构", notes = "获取项目类型树结构")
    @GetMapping("/tree/code")
    public RestResponse<List<TreeNode>> treeByCode(@RequestParam(name = "code") String code, @RequestParam(name = "values") String[] values) {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setData(service.getProjectTreeByCode(code,values));
        return restResponse;
    }
}