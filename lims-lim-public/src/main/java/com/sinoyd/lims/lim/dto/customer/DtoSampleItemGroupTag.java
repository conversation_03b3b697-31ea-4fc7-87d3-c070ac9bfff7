package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

/**
 * 样品因子对应的分组标识
 * <AUTHOR>
 * @version V1.0.0 2024/6/18
 * @since V100R001
 */
@Data
public class DtoSampleItemGroupTag {
    /**
     * 样品标识
     */
    private String sampleId;

    /**
     * 测试项目标识
     */
    private String testId;

    /**
     * 分组Id
     */
    private String groupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 分组标识
     */
    private String sampleCodeTag;

    /**
     * 关联样品id
     */
    private String associateSampleId;

    /**
     * 样品类型
     */
    private Integer sampleCategory;

    public DtoSampleItemGroupTag() {
    }

    /**
     * 查询开启样品编号标识的检测类型下的分组
     *
     * @param sampleId      样品标识
     * @param testId        测试项目标识
     * @param groupId       分组id
     * @param groupName     分组名称
     * @param sampleCodeTag 分组标识
     */
    public DtoSampleItemGroupTag(String sampleId, String testId, String groupId, String groupName,
                                 String sampleCodeTag, String associateSampleId, Integer sampleCategory) {
        this.sampleId = sampleId;
        this.testId = testId;
        this.groupId = groupId;
        this.groupName = groupName;
        this.sampleCodeTag = sampleCodeTag;
        this.sampleCategory = sampleCategory;
        this.associateSampleId = associateSampleId;
    }
}
