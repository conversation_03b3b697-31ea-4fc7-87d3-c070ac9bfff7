package com.sinoyd.lims.pro.repository;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoReport;
import io.swagger.models.auth.In;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;


/**
 * Report数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface ReportRepository extends IBaseJpaRepository<DtoReport, String> {

    /**
     * 按编号查询相应的报告信息
     *
     * @param code 报告编号
     * @return 返回相应的报告信息
     */
    DtoReport findByCode(String code);

    /**
     * 按项目id查询相应的报告列表
     *
     * @param projectId 项目id
     * @return 返回相应的报告信息
     */
    List<DtoReport> findByProjectId(String projectId);


    /**
     * 按项目及编制报告人分组报告数
     *
     * @param projectIds 项目id
     * @param creator    编制人
     * @return 报告数
     */
    @Query("select count(r.id),projectId  from DtoReport r where r.isDeleted = false and r.projectId in :projectIds and r.creator=:creator group by r.projectId")
    List<Object[]> countReportNumGroupByProjectIdsAndCreator(@Param("projectIds") List<String> projectIds, @Param("creator") String creator);

    /**
     * 按编号查询相应的报告信息
     *
     * @param code 报告编号
     * @param id   报告id
     * @return 返回相应的报告信息
     */
    Integer countByCodeAndIdNot(String code, String id);

    /**
     * @param ids        样品ids
     * @param status     状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReport r set r.status = :status,r.modifyDate = :modifyDate,r.modifier = :modifier  where r.id in :ids")
    Integer updateReportStatus(@Param("ids") List<String> ids, @Param("status") String status,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);

    /**
     * @param ids        样品ids
     * @param status     状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReport r set r.grantStatus = :grantStatus,r.modifyDate = :modifyDate,r.modifier = :modifier  where r.id in :ids")
    Integer updateGrantStatus(@Param("ids") List<String> ids, @Param("grantStatus") Integer status,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);


    /**
     * @param id         报告id
     * @param reportNum  报告数
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoReport r set r.reportNum = :reportNum,r.modifyDate = :modifyDate,r.modifier = :modifier  where r.id = :id")
    Integer updateReportNum(@Param("id") String id, @Param("reportNum") Integer reportNum,
                            @Param("modifier") String modifier,
                            @Param("modifyDate") Date modifyDate);

    /**
     * 批量修改报告更改状态
     *
     * @param ids 样品的ids
     * @return 返回相应的条数
     */
    @Transactional
    @Modifying
    @Query("update DtoReport r set r.dataChangeStatus = :dataChangeStatus,r.modifyDate=:modifyDate,r.modifier=:modifier where r.id in :ids")
    Integer updateDataChangeStatus(@Param("ids") List<String> ids,
                                   @Param("dataChangeStatus") Integer dataChangeStatus,
                                   @Param("modifier") String modifier,
                                   @Param("modifyDate") Date modifyDate);

    /**
     * 根据projectId List获取报告
     *
     * @param projectIds 项目id list
     * @return 报告对象集合
     */
    List<DtoReport> findByProjectIdIn(List<String> projectIds);
}