package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;

/**
 * 监测点位统计查询条件
 *
 * <AUTHOR>
 * @version V5.2.0 2025/4/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FixedPointStatCriteria extends BaseCriteria {

    /**
     * 点位id集合
     */
    private Collection<String> pointIds;

    /**
     * 样品id集合
     */
    private Collection<String> sampleIds;

    /**
     * 分析时间开始日期
     */
    private String startDate;

    /**
     * 分析时间结束日期
     */
    private String endDate;

    /**
     * 查询关键字（分析项目名称）
     */
    private String key;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.sampleIds)){
            condition.append(" and a.sampleId in (:sampleIds) ");
            values.put("sampleIds", this.sampleIds);
        }

        if (StringUtil.isNotEmpty(this.startDate)){
            Date from = DateUtil.stringToDate(this.startDate, DateUtil.YEAR);
            condition.append(" and a.analyzeTime >= :startDate");
            values.put("startDate", from);
        }
        if (StringUtil.isNotEmpty(this.endDate)){
            Date to = DateUtil.stringToDate(this.endDate, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and a.analyzeTime < :endDate");
            values.put("endDate", to);
        }
        if (StringUtil.isNotEmpty(this.key)){
            condition.append(" and a.redAnalyzeItemName like :key ");
            values.put("key", "%" + this.key + "%");
        }
        return condition.toString();
    }
}
