package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmental;
import com.sinoyd.lims.lim.repository.lims.EnvironmentalRepository;
import com.sinoyd.lims.lim.service.EnvironmentalService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 环境管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@Service
public class EnvironmentalServiceImpl extends BaseJpaServiceImpl<DtoEnvironmental,String, EnvironmentalRepository> implements EnvironmentalService {


    /**
     * 新增环境信息
     */
    @Transactional
    @Override
    public DtoEnvironmental save(DtoEnvironmental entity) {
        Integer count = repository.getByLabCodeAndName(entity.getId(),entity.getLabCode(), entity.getLabName());
        if(count > 0){
            throw new IllegalArgumentException("已存在相同名称和编号的实验室");
        }

        return super.save(entity);
    }

    @Transactional
    @Override
    public DtoEnvironmental update(DtoEnvironmental entity) {
        Integer count = repository.getByLabCodeAndName(entity.getId(),entity.getLabCode(), entity.getLabName());
        if(count > 0){
            throw new IllegalArgumentException("已存在相同名称和编号的实验室");
        }

        return super.update(entity);
    }
    
    /**
     * 分页查询环境信息
     */
    @Override
    public void findByPage(PageBean<DtoEnvironmental> page, BaseCriteria criteria) {

        // 设置查询的实体类名及别名
        page.setEntityName("DtoEnvironmental p");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select p");

        super.findByPage(page, criteria);
    }

    /**
     * 重新实现（为了返回调用该类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回环境对象
     */
    @Override
    public DtoEnvironmental findOne(String id) {
        return super.findOne(id);
    }
}