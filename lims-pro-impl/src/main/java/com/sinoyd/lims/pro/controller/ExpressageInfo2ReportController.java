package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.ExpressageInfo2ReportService;
import com.sinoyd.lims.pro.criteria.ExpressageInfo2ReportCriteria;
import com.sinoyd.lims.pro.dto.DtoExpressageInfo2Report;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * ExpressageInfo2Report服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: ExpressageInfo2Report服务")
 @RestController
 @RequestMapping("api/pro/expressageInfo2Report")
 public class ExpressageInfo2ReportController extends BaseJpaController<DtoExpressageInfo2Report, String,ExpressageInfo2ReportService> {


    /**
     * 分页动态条件查询ExpressageInfo2Report
     * @param expressageInfo2ReportCriteria 条件参数
     * @return RestResponse<List<ExpressageInfo2Report>>
     */
     @ApiOperation(value = "分页动态条件查询ExpressageInfo2Report", notes = "分页动态条件查询ExpressageInfo2Report")
     @GetMapping
     public RestResponse<List<DtoExpressageInfo2Report>> findByPage(ExpressageInfo2ReportCriteria expressageInfo2ReportCriteria) {
         PageBean<DtoExpressageInfo2Report> pageBean = super.getPageBean();
         RestResponse<List<DtoExpressageInfo2Report>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, expressageInfo2ReportCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询ExpressageInfo2Report
     * @param id 主键id
     * @return RestResponse<DtoExpressageInfo2Report>
     */
     @ApiOperation(value = "按主键查询ExpressageInfo2Report", notes = "按主键查询ExpressageInfo2Report")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoExpressageInfo2Report> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoExpressageInfo2Report> restResponse = new RestResponse<>();
         DtoExpressageInfo2Report expressageInfo2Report = service.findOne(id);
         restResponse.setData(expressageInfo2Report);
         restResponse.setRestStatus(StringUtil.isNull(expressageInfo2Report) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增ExpressageInfo2Report
     * @param expressageInfo2Report 实体列表
     * @return RestResponse<DtoExpressageInfo2Report>
     */
     @ApiOperation(value = "新增ExpressageInfo2Report", notes = "新增ExpressageInfo2Report")
     @PostMapping
     public RestResponse<DtoExpressageInfo2Report> create(@RequestBody DtoExpressageInfo2Report expressageInfo2Report) {
         RestResponse<DtoExpressageInfo2Report> restResponse = new RestResponse<>();
         restResponse.setData(service.save(expressageInfo2Report));
         return restResponse;
      }

     /**
     * 新增ExpressageInfo2Report
     * @param expressageInfo2Report 实体列表
     * @return RestResponse<DtoExpressageInfo2Report>
     */
     @ApiOperation(value = "修改ExpressageInfo2Report", notes = "修改ExpressageInfo2Report")
     @PutMapping
     public RestResponse<DtoExpressageInfo2Report> update(@RequestBody DtoExpressageInfo2Report expressageInfo2Report) {
         RestResponse<DtoExpressageInfo2Report> restResponse = new RestResponse<>();
         restResponse.setData(service.update(expressageInfo2Report));
         return restResponse;
      }

    /**
     * "根据id批量删除ExpressageInfo2Report
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ExpressageInfo2Report", notes = "根据id批量删除ExpressageInfo2Report")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }