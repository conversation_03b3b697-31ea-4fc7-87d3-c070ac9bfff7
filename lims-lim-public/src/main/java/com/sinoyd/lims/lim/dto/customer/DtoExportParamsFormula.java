package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Table;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * 公式导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/6
 * @since V100R001
 */
@Table(name = "TB_LIM_ParamsFormula")
@Data
public class DtoExportParamsFormula extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id;

    /**
     * 参数Id（测试项目、检测类型参数）
     */
    @Excel(name = "测试项目Id", orderNum = "20")
    private String objectId;

    /**
     * 公式
     */
    @Excel(name = "公式", orderNum = "30")
    private String formula;

    /**
     * 配置日期
     */
    @Excel(name = "配置日期", orderNum = "40")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}(\\.\\d+)?$", message = "配置日期不符合日期格式要求")
    private String configDate;

    /**
     * 原始公式
     */
    @Excel(name = "原始公式", orderNum = "50")
    private String orignFormula;

    /**
     * 原始公式类型（枚举EnumOrignFormulatType：0:手写html,1:图片）
     */
    @Excel(name = "原始公式类型", orderNum = "60")
    @Pattern(regexp = "^-?\\d+$", message = "原始公式类型必须为整数")
    private String orignFormulatType;

    /**
     * 类型（枚举EnumParamsFormulaObjectType：0.测试公式 1.检测类型参数公式）
     */
    @Excel(name = "类型", orderNum = "80")
    @Pattern(regexp = "^-?\\d+$", message = "类型必须为整数")
    private String objectType;

    /**
     * 检测类型id（仅用于测试项目）
     */
    @Excel(name = "检测类型id", orderNum = "90")
    private String sampleTypeId;

    /**
     * 验证状态 0未验证 1已验证
     */
    @Excel(name = "验证状态", orderNum = "160")
    @Pattern(regexp = "^-?\\d+$", message = "验证状态必须为整数")
    private String validate;

    /**
     * 使用次数
     */
    @Excel(name = "使用次数", orderNum = "170")
    @Pattern(regexp = "^-?\\d+$", message = "使用次数必须为整数")
    private String usageNum;
}
