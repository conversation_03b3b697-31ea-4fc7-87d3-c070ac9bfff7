package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * AnalyzeMethodReagentConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "AnalyzeMethodReagentConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class AnalyzeMethodReagentConfig extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public AnalyzeMethodReagentConfig() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 需求的配置过程
     */
    @Column(length = 1000)
    @ApiModelProperty("需求的配置过程 ")
    @Length(message = "需求的配置过程{validation.message.length}", max = 1000)
    private String context;

    /**
     * 分析方法id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("分析方法id（Guid）")
    private String analyzeMethodId;

    /**
     * 试剂名称
     */
    @Column(length = 100)
    @ApiModelProperty("试剂名称")
    @Length(message = "试剂名称{validation.message.length}", max = 100)
    private String reagentName;

    /**
     * 试剂规格
     */
    @Column(length = 50)
    @ApiModelProperty("试剂规格")
    @Length(message = "试剂规格{validation.message.length}", max = 500)
    private String reagentSpecification;

    /**
     * 配置溶液
     */
    @ApiModelProperty("配置溶液")
    @Length(message = "配置溶液{validation.message.length}", max = 500)
    private String configurationSolution;

    /**
     * 配置日期
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("配置日期")
    private Date configDate;

    /**
     * 有效期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("有效期")
    private Date expiryDate;

    /**
     * 排序值（预留）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值（预留）")
    private Integer orderNum;

    /**
     * 稀释过程记录
     */
    @Column(length = 1000)
    @ApiModelProperty("稀释过程记录")
    @Length(message = "稀释过程记录{validation.message.length}", max = 1000)
    private String course;

    /**
     * 其他情况
     */
    @Column(length = 1000)
    @ApiModelProperty("其他情况")
    @Length(message = "其他情况{validation.message.length}", max = 1000)
    private String opinion;

    /**
     * 试剂类型（1：一般试剂 2：标准溶液）
     */
    @Column(nullable = false)
    @ApiModelProperty("试剂类型（1：一般试剂 2：标准溶液）")
    private Integer reagentType;

    /**
     * 浓度
     */
    @Column
    @ApiModelProperty("浓度")
    @Length(message = "浓度{validation.message.length}", max = 200)
    private String concentration;

    /**
     * 试剂体积或质量
     */
    @Column
    @ApiModelProperty("试剂体积或质量")
    @Length(message = "试剂体积或质量{validation.message.length}", max = 200)
    private String reagentVolumeQuality;

    /**
     * 定容体积
     */
    @Column
    @ApiModelProperty("定容体积")
    @Length(message = "定容体积{validation.message.length}", max = 200)
    private String constantVolume;

    /**
     * 稀释液
     */
    @Column
    @ApiModelProperty("稀释液")
    @Length(message = "稀释液{validation.message.length}", max = 200)
    private String diluent;

    /**
     * 适用项目
     */
    @Column
    @ApiModelProperty("适用项目")
    @Length(message = "适用项目{validation.message.length}", max = 200)
    private String suitItem;

    /**
     * 假删字段
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}