package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 查新结果查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NewSearchResultCriteria extends BaseCriteria implements Serializable {

    /**
     * 关键字（任务名称）
     */
    private String key;


    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 任务状态
     */
    private Integer status = EnumLIM.EnumNewSearchStatus.所有.getValue();

    /**
     * 部门负责人
     */
    private String confirmId;

    /**
     * 确认任务确认人
     */
    private String executeConfirmId;

    /**
     * 宣贯任务宣贯人
     */
    private String executePropagateId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(key)) {
            condition.append(" and (t.standardName like :key or t.standardNum like :key)");
            values.put("key", "%" + key + "%");
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and t.newSearchDate >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and t.newSearchDate < :endTime");
            values.put("endTime", to);
        }

        if (StringUtil.isNotNull(status) && !EnumLIM.EnumNewSearchStatus.所有.getValue().equals(status)) {
            condition.append(" and t.status = :status");
            values.put("status", status);
        } else {
            condition.append(" and t.status in :status");
            List<Integer> statues = new ArrayList<>();
            statues.add(EnumLIM.EnumNewSearchStatus.待处理.getValue());
            statues.add(EnumLIM.EnumNewSearchStatus.已处理.getValue());
            values.put("status", statues);
        }
        if (StringUtil.isNotEmpty(confirmId)){
            condition.append(" and t.confirmId = :confirmId");
            values.put("confirmId", confirmId);
        }
        if (StringUtil.isNotEmpty(executeConfirmId)){
            condition.append(" and t.executeConfirmId = :executeConfirmId");
            values.put("executeConfirmId", executeConfirmId);
        }
        if (StringUtil.isNotEmpty(executePropagateId)){
            condition.append(" and t.executePropagateId = :executePropagateId");
            values.put("executePropagateId", executePropagateId);
        }
        return condition.toString();
    }
}
