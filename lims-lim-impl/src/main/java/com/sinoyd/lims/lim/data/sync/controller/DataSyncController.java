package com.sinoyd.lims.lim.data.sync.controller;

import com.sinoyd.base.criteria.EvaluationCriteriaCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.controller.BaseController;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.criteria.RecordConfigCriteria;
import com.sinoyd.lims.lim.criteria.ReportConfigCriteria;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataSyncParam;
import com.sinoyd.lims.lim.data.sync.service.DataSyncService;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 数据同步服务接口
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/21
 */
@Api(tags = "数据同步服务接口")
@RestController
@RequestMapping("/api/lim/sync")
public class DataSyncController extends BaseController {

    private DataSyncService dataSyncService;

    /**
     * 分页查询公共库测试项目
     *
     * @param criteria 分页条件
     * @return 返回分页数据
     */
    @ApiOperation(value = "分页查询公共库测试项目", notes = "分页查询公共库测试项目")
    @GetMapping("/standard/test")
    public RestResponse<List<DtoTest>> findStandardTestByPage(TestCriteria criteria) {
        RestResponse<List<DtoTest>> restResp = new RestResponse<>();
        PageBean<DtoTest> page = super.getPageBean();
        dataSyncService.findStandardTestByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 分页查询公共库评价标准配置
     *
     * @param criteria 分页条件
     * @return 返回分页数据
     */
    @ApiOperation(value = "分页查询公共库评价标准配置", notes = "分页查询公共库评价标准配置")
    @GetMapping("/standard/evaluationCriteria")
    public RestResponse<List<DtoEvaluationCriteria>> findStandardEvaluationCriteriaByPage(EvaluationCriteriaCriteria criteria) {
        RestResponse<List<DtoEvaluationCriteria>> restResp = new RestResponse<>();
        PageBean<DtoEvaluationCriteria> page = super.getPageBean();
        dataSyncService.findStandardEvaluationCriteriaByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 分页查询采样单配置
     *
     * @param criteria 分页条件
     * @return 返回分页数据
     */
    @ApiOperation(value = "分页查询采样单配置", notes = "分页查询采样单配置")
    @GetMapping("/standard/recordConfig")
    public RestResponse<List<DtoRecordConfig>> findStandardRecordConfigByPage(RecordConfigCriteria criteria) {
        RestResponse<List<DtoRecordConfig>> restResp = new RestResponse<>();
        PageBean<DtoRecordConfig> page = super.getPageBean();
        dataSyncService.findStandardRecordConfigByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 分页查询原始记录单配置
     *
     * @param criteria 分页条件
     * @return 返回分页数据
     */
    @ApiOperation(value = "分页查询公共库评价标准配置", notes = "分页查询公共库评价标准配置")
    @GetMapping("/standard/worksheetConfig")
    public RestResponse<List<DtoRecordConfig>> findStandardWorksheetConfigByPage(RecordConfigCriteria criteria) {
        RestResponse<List<DtoRecordConfig>> restResp = new RestResponse<>();
        PageBean<DtoRecordConfig> page = super.getPageBean();
        dataSyncService.findStandardWorksheetRecordConfigByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }


    /**
     * 分页查询公共库报表模板配置
     *
     * @param criteria 分页条件
     * @return 返回分页数据
     */
    @ApiOperation(value = "分页查询公共库报表模板配置", notes = "分页查询公共库报表模板配置")
    @GetMapping("/standard/reportConfig")
    public RestResponse<List<DtoReportConfig>> findStandardReportConfigByPage(ReportConfigCriteria criteria){
        RestResponse<List<DtoReportConfig>> restResp = new RestResponse<>();
        PageBean<DtoReportConfig> page = super.getPageBean();
        dataSyncService.findStandardReportConfigByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 公共库和项目库数据比较
     *
     * @param dtoDataSyncParam 比较参数
     * @return 返回分析项目结果
     */
    @ApiOperation(value = "公共库和项目库数据比较", notes = "公共库和项目库数据比较")
    @PostMapping("/compare")
    public RestResponse<Map<String, List<DtoDataCompareResult<?>>>> compareData(@RequestBody DtoDataSyncParam dtoDataSyncParam) {
        RestResponse<Map<String, List<DtoDataCompareResult<?>>>> restResp = new RestResponse<>();
        restResp.setData(dataSyncService.compare(dtoDataSyncParam));
        return restResp;
    }

    /**
     * 公共库和项目库数据同步
     *
     * @param dtoDataSyncParam 数据同步参数实体
     * @return 响应结果
     */
    @ApiOperation(value = "公共库和项目库数据同步", notes = "公共库和项目库数据同步")
    @PostMapping
    public RestResponse<String> syncData(@RequestBody DtoDataSyncParam dtoDataSyncParam) {
        RestResponse<String> restResp = new RestResponse<>();
        dataSyncService.syncData(dtoDataSyncParam);
        return restResp;
    }

    @Autowired
    @Lazy
    public void setDataSyncService(DataSyncService dataSyncService) {
        this.dataSyncService = dataSyncService;
    }
}