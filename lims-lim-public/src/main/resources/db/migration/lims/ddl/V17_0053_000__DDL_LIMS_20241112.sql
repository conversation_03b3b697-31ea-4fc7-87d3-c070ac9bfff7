-- 环境院项目推送迁移产品相关字段
-- TB_BASE_Enterprise
ALTER TABLE TB_BASE_Enterprise
    ADD COLUMN regulateId varchar(50) NULL DEFAULT NULL COMMENT '监管平台客户id';
ALTER TABLE TB_BASE_Enterprise
    ADD COLUMN regulateName varchar(50) NULL DEFAULT NULL COMMENT '监管平台客户名称';
ALTER TABLE TB_BASE_Enterprise
    ADD COLUMN socialCode varchar(50) NULL DEFAULT NULL COMMENT '社会信用代码';
ALTER TABLE TB_BASE_Enterprise
    ADD COLUMN pollutionCode varchar(50) NULL DEFAULT NULL COMMENT '污染源编号';
-- TB_BASE_Instrument
ALTER TABLE TB_BASE_Instrument
    ADD COLUMN regulateId varchar(50) NULL DEFAULT NULL COMMENT '监管平台仪器id';
ALTER TABLE TB_BASE_Instrument
    ADD COLUMN regulateName varchar(50) NULL DEFAULT NULL COMMENT '监管平台仪器名称';
ALTER TABLE TB_BASE_Instrument
    ADD COLUMN orderNum int(11) NOT NULL DEFAULT 0 COMMENT '排序值';
-- TB_LIM_Test
ALTER TABLE TB_LIM_Test
    ADD COLUMN shMethodId varchar(50) NULL DEFAULT '' COMMENT '监管平台分析项目id';
ALTER TABLE TB_LIM_Test
    ADD COLUMN shMethodName varchar(255) NULL DEFAULT '' COMMENT '监管平台分析项目名称';
ALTER TABLE TB_LIM_Test
    ADD COLUMN shSamplingMethodId varchar(50) NULL DEFAULT '' COMMENT '监管平台采样方法id';
ALTER TABLE TB_LIM_Test
    ADD COLUMN shSamplingMethodName varchar(255) NULL DEFAULT '' COMMENT '监管平台采样方法名称';
-- TB_LIM_Person
ALTER TABLE TB_LIM_Person
    ADD COLUMN regulateId varchar(50) NULL DEFAULT NULL COMMENT '监管平台id';
ALTER TABLE TB_LIM_Person
    ADD COLUMN regulateName varchar(50) NULL DEFAULT NULL COMMENT '监管平台名称';
-- TB_PRO_OrderContract
ALTER TABLE TB_PRO_OrderContract
    ADD COLUMN shanghaiEntId varchar(50) NOT NULL DEFAULT '' COMMENT '监管平台甲方企业id';
ALTER TABLE TB_PRO_OrderContract
    ADD COLUMN shanghaiEntName varchar(255) NOT NULL DEFAULT '' COMMENT '监管平台甲方企业名称';
ALTER TABLE TB_PRO_OrderContract
    ADD COLUMN isHavingPut bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已经推送';
ALTER TABLE TB_PRO_OrderContract
    ADD COLUMN cId varchar(50) NULL DEFAULT '' COMMENT '合同id';
-- TB_PRO_Project
ALTER TABLE TB_PRO_Project
    ADD COLUMN environmentCode varchar(50) NULL DEFAULT NULL COMMENT '环境院编号';
ALTER TABLE TB_PRO_Project
    ADD COLUMN addressName varchar(300) NULL DEFAULT NULL COMMENT '任务所在区名称';
ALTER TABLE TB_PRO_Project
    ADD COLUMN isCMA int(11) NULL COMMENT '是否加盖CMA章';
ALTER TABLE TB_PRO_Project
    ADD COLUMN regulateReportType VARCHAR(50) NULL COMMENT '报告类型';
ALTER TABLE TB_PRO_Project
    ADD COLUMN pollutionCode varchar(50) NULL DEFAULT NULL COMMENT '污染源编号';
-- TB_PRO_Report
ALTER TABLE TB_PRO_Report
    ADD COLUMN isCMA int(11) NULL DEFAULT NULL COMMENT '是否加盖CMA章';
ALTER TABLE TB_PRO_Report
    ADD COLUMN regulateReportType varchar(50) NULL DEFAULT NULL COMMENT '监管平台报告类型，多个id用英文逗号分隔';
ALTER TABLE TB_PRO_Report
    ADD COLUMN regulateCode varchar(50) NULL DEFAULT NULL COMMENT '监管平台系统编号';


