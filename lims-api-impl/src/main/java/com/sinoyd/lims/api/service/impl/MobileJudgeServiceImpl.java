package com.sinoyd.lims.api.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.api.service.MobileJudgeService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import com.sinoyd.lims.pro.repository.SampleJudgeDataRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.SampleJudgeDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MobileJudgeServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleJudgeData, String, SampleJudgeDataRepository> implements MobileJudgeService {

    private SampleRepository sampleRepository;

    private TestRepository testRepository;

    private SampleJudgeDataService sampleJudgeDataService;

    @Override
    public List<Map<String, String>> getAnalyzeItemByFolderId(String folderId, Integer cycValue) {
        List<Map<String, String>> data = new ArrayList<>();
        List<DtoSample> samples = sampleRepository.findBySampleFolderId(folderId).stream().filter(s -> cycValue.equals(s.getCycleOrder())).collect(Collectors.toList());
        List<String> sampleIds  = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSampleJudgeData> judgeDataList = repository.findBySampleIdIn(sampleIds);
        List<String> testIds = judgeDataList.stream().map(DtoSampleJudgeData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        for (DtoTest test : tests) {
            Map<String, String> map = new HashMap<>();
            map.put("testId", test.getId());
            map.put("analyzeItemName", test.getRedAnalyzeItemName());
            data.add(map);
        }
        return data;
    }

    @Override
    public List<DtoSampleJudgeData> findByFolderIdForApp(String folderId, Integer cycValue, String testId) {
        List<DtoSample> samples = sampleRepository.findBySampleFolderId(folderId).stream().filter(s -> cycValue.equals(s.getCycleOrder())).collect(Collectors.toList());
        List<String> sampleIds  = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSampleJudgeData> judgeDataList = repository.findBySampleIdInAndTestId(sampleIds, testId);
        sampleJudgeDataService.fillingTransientFields(judgeDataList);
        judgeDataList.sort(Comparator.comparing(DtoSampleJudgeData::getCompareType).thenComparing(DtoSampleJudgeData::getSampleCode));
        return judgeDataList;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setSampleJudgeDataService(SampleJudgeDataService sampleJudgeDataService) {
        this.sampleJudgeDataService = sampleJudgeDataService;
    }

}
