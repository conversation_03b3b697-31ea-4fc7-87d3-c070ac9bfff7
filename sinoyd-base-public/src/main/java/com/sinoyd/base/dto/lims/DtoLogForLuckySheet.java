package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.LogForLuckySheet;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoLogForLuckySheet实体
 * <AUTHOR>
 * @version V1.0.0 2022/10/24
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_LogForLuckySheet")
@Data
@DynamicInsert
public class DtoLogForLuckySheet extends LogForLuckySheet {

    private static final long serialVersionUID = 1L;

}
