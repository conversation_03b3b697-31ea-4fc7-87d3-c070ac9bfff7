package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoProject2FixedProperty;
import com.sinoyd.lims.pro.repository.Project2FixedPropertyRepository;
import com.sinoyd.lims.pro.service.Project2FixedPropertyService;
import org.springframework.stereotype.Service;


/**
 * Project2FixedProperty操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class Project2FixedPropertyServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoProject2FixedProperty,String,Project2FixedPropertyRepository> implements Project2FixedPropertyService {

    @Override
    public void findByPage(PageBean<DtoProject2FixedProperty> pb, BaseCriteria project2FixedPropertyCriteria) {
        pb.setEntityName("DtoProject2FixedProperty a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, project2FixedPropertyCriteria);
    }
}