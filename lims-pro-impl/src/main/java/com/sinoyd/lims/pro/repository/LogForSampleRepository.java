package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoLogForSample;

import java.util.List;


/**
 * LogForSample数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
public interface LogForSampleRepository extends IBaseJpaPhysicalDeleteRepository<DtoLogForSample, String> {

    List<DtoLogForSample> findByObjectIdIn(List<String> objectIds);

    /**
     * 根据对象id查询日志
     *
     * @param objectId 对象id
     * @return 日志列表
     */
    List<DtoLogForSample> findByObjectId(String objectId);
}