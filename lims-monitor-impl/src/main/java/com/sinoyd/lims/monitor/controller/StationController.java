package com.sinoyd.lims.monitor.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.StationService;
import com.sinoyd.lims.monitor.criteria.StationCriteria;
import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;
import java.util.Map;


/**
 * Station服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: Station服务")
 @RestController
 @RequestMapping("api/monitor/station")
 public class StationController extends BaseJpaController<DtoStation, String,StationService> {


    /**
     * 分页动态条件查询Station
     * @param stationCriteria 条件参数
     * @return RestResponse<List<Station>>
     */
     @ApiOperation(value = "分页动态条件查询Station", notes = "分页动态条件查询Station")
     @GetMapping
     public RestResponse<List<DtoStation>> findByPage(StationCriteria stationCriteria) {
         PageBean<DtoStation> pageBean = super.getPageBean();
         RestResponse<List<DtoStation>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, stationCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询Station
     * @param id 主键id
     * @return RestResponse<DtoStation>
     */
     @ApiOperation(value = "按主键查询Station", notes = "按主键查询Station")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoStation> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoStation> restResponse = new RestResponse<>();
         DtoStation station = service.findOne(id);
         restResponse.setData(station);
         restResponse.setRestStatus(StringUtil.isNull(station) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增Station
     * @param station 实体列表
     * @return RestResponse<DtoStation>
     */
     @ApiOperation(value = "新增Station", notes = "新增Station")
     @PostMapping
     public RestResponse<DtoStation> create(@RequestBody @Validated DtoStation station) {
         RestResponse<DtoStation> restResponse = new RestResponse<>();
         restResponse.setData(service.save(station));
         return restResponse;
      }

     /**
     * 新增Station
     * @param station 实体列表
     * @return RestResponse<DtoStation>
     */
     @ApiOperation(value = "修改Station", notes = "修改Station")
     @PutMapping
     public RestResponse<DtoStation> update(@RequestBody @Validated DtoStation station) {
         RestResponse<DtoStation> restResponse = new RestResponse<>();
         restResponse.setData(service.update(station));
         return restResponse;
      }

    /**
     * "根据id批量删除Station
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Station", notes = "根据id批量删除Station")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    @GetMapping("/findStation")
    public RestResponse<Map<String, List<DtoStation>>> findStation() {
        RestResponse<Map<String, List<DtoStation>>> response = new RestResponse<>();
        response.setData(service.findStation());
        return response;
    }
 }