package com.sinoyd.lims.lim.service.transform;


import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.lims.lim.dto.customer.*;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据迁移sheet导入策略接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
public interface TransformImportStrategy {

    String[] BASE_DATA_TYPE = {"新增", "已存在"};

    /**
     * sheet页数据导入
     *
     * @param inputStream 文件流
     * @param response    响应
     * @param params      读取参数
     * @throws Exception 异常
     */
    void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception;

    /**
     * 获取导入数据并检查
     *
     * @param substituteMap     替代物绑定数据
     * @param sampleTypeBindMap 检测类型绑定数据
     * @param dtoDataSyncParams 导入数据传输参数实体
     * @param testDependentData 导入数据源
     * @param importTestTemp    导入数据临时保存实体
     * @param exportData        导出数据实体
     */
    void getAddData(Map<String, DtoBaseData> substituteMap,
                    Map<String, DtoBaseData> sampleTypeBindMap,
                    DtoDataSyncParams dtoDataSyncParams,
                    DtoTestDependentData testDependentData,
                    DtoImportTestTemp importTestTemp,
                    DtoTestDependentData exportData);


    /**
     * 数据导入
     *
     * @param importTestTemp  导入数据临时保存实体
     * @param webSocketServer websockets服务端
     */
    void importData(DtoImportTestTemp importTestTemp,
                    WebSocketServer webSocketServer);

    /**
     * 获取新增数据时的排序值
     *
     * @return 排序值
     */
    int getAddDataOrderNum();

    /**
     * 导入的数据库表名
     *
     * @return 数据项名称
     */
    String getTableName();

    /**
     * 导入的数据库表描述
     *
     * @return 数据项名称
     */
    String getTableRemark();

    /**
     * 导入前数据检查
     *
     * @param dtoDataSyncParams 数据同步检查传输实体
     * @param testDependentData 测试项目迁移数据源
     * @return 检查结果
     */
    List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData);

    /**
     * 获取WebSocket推送消息
     *
     * @param dataNum    数据总数量
     * @param currentNum 当前数量
     * @return 推送消息
     */
    default String getMessage(int dataNum, int currentNum) {
        // 定义json消息，返回前段显示进度
        Map<String, Object> map = new HashMap<>();
        String result = "";
        // 表名
        map.put("tableName", getTableName());
        // 数据数量
        map.put("dataNum", dataNum);
        // 进度百分比
        if (dataNum == 0) {
            map.put("importProgress", 100);
        } else {
            double progress = ((double) currentNum / dataNum) * 100;
            map.put("importProgress", progress);
        }
        try {
            result = JsonUtil.toJson(map);
        } catch (JsonProcessingException e) {
            throw new BaseException(e.getMessage());
        }
        return result;
    }

}
