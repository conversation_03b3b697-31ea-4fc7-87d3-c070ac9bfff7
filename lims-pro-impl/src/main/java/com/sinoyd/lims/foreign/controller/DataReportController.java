package com.sinoyd.lims.foreign.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataOutSorceDataVO;
import com.sinoyd.lims.foreign.DataReportVO;
import com.sinoyd.lims.foreign.service.DataOutSorceDataService;
import com.sinoyd.lims.foreign.service.DataReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 报告信息服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataReport服务")
@RestController
@RequestMapping("api/pro/foreign/dataReport")
public class DataReportController extends ExceptionHandlerController<DataReportService> {


    /**
     * 数据同步
     *
     * @param map 同步参数
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataReportVO>> getSyncData(@RequestBody Map<String, Object> map) {
        List<DataReportVO> curveVOList = service.getSyncData(map);
        RestResponse<List<DataReportVO>> response = new RestResponse();
        response.setData(curveVOList);
        return response;
    }
}
