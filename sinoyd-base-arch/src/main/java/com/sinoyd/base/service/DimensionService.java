package com.sinoyd.base.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.base.dto.rcc.DtoDimension;

import java.util.List;

/**
 * 量纲配置
 * <AUTHOR>
 * @version v1.0.0 2018/12/5
 * @since V100R001
 */
public interface DimensionService extends IBaseJpaService<DtoDimension, String> {

    /**
     * 根据名称获取实体
     *
     * @param dimensionName
     * @return
     */
    DtoDimension findByDimensionName(String dimensionName);

    /**
     * <p>
     * 量纲换算
     * <p>
     * 量纲类型相同的才能换算，无法换算时返回原值
     * <p>
     * 根据基准值来换算
     *
     * @param toConvertValue 需要换算的值
     * @param fromDimensionId 原量纲id
     * @param toDimensionId   目标量纲id
     * @return 换算后的值
     */
    String convertById(String toConvertValue, String fromDimensionId, String toDimensionId);

    /**
     * <p>
     * 量纲换算
     * <p>
     * 量纲类型相同的才能换算，无法换算时返回原值
     * <p>
     * 根据基准值来换算
     *
     * @param toConvertValue   需要换算的值
     * @param fromDimensionName 原量纲名称
     * @param toDimensionName   目标量纲名称
     * @return 换算后的值
     */
    String convertByName(String toConvertValue, String fromDimensionName, String toDimensionName);


    /***
     * 根据id数组获取量纲
     * @param ids 量纲ids
     * @return 返回量纲数据
     */
    List<DtoDimension> findByIds(List<String> ids);


    /**
     * 获取redis下的量纲信息
     * @param ids 量纲ids
     * @return 返回量纲数据
     */
    List<DtoDimension> findRedisByIds(List<String> ids);

    /**
     * 根据用户Id获取有假删的数据
     *
     * @param ids 主键ids
     * @return 返回带有假删的数据
     */
    List<DtoDimension> findAllDeleted(List<String> ids);


    /**
     * 含假删的信息
     *
     * @return 返回假删信息
     */
    List<DtoDimension> findAllDeleted();

    /**
     * 保存相应的redis数据
     *
     * @param item 检测类型的实体对象
     */
    void saveRedis(DtoDimension item);

    /**
     * 自增排序值
     *
     * @param ids 量纲id
     */
    void incrementOrderNum(List<String> ids);

    /**
     * <p>
     * 量纲换算
     * <p>
     * 量纲类型相同的才能换算，无法换算时返回原值
     * <p>
     * 根据基准值来换算
     *
     * @param toConvertValue 需要换算的值
     * @param fromDimensionId 原量纲id
     * @param toDimensionId   目标量纲id
     * @return 换算后的值
     */
    String convertByIdNew(String toConvertValue, String fromDimensionId, String toDimensionId);
}