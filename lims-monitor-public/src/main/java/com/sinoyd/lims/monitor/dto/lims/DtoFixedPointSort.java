package com.sinoyd.lims.monitor.dto.lims;

import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.entity.FixedPointSort;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoFixedPointSort实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_FixedPointSort")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoFixedPointSort extends FixedPointSort {
    private static final long serialVersionUID = 1L;

    /**
     * 点位信息表的对象集合
     */
    @Transient // 此注解标识为非数据库表字段
    private List<DtoFixedpoint> dtoFixedpoint;

}