package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.dto.DtoContractCollectionPlan;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;
import java.util.Map;

/**
 * 收款计划管理
 * <AUTHOR>
 * @version V1.0.0 2019/2/25
 * @since V100R001
 */
public interface ContractCollectionPlanService extends IBaseJpaService<DtoContractCollectionPlan, String> {
    /**
     * 计算统计数据
     * @param page     分页信息
     * @param criteria 检索条件
     * @return  Map<String, Object>
     */
    Map<String, Object> analyzeProfileData(PageBean<DtoContractCollectionPlan> page, BaseCriteria criteria);

    /**
     * 构建下拉数据源
     * @return   List<String>
     */
    List<String> getCollectItemSelectList(String orderContractId);
}