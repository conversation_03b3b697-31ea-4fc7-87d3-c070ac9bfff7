//package com.sinoyd.base.service.impl;
//
//import com.sinoyd.EnableEurekaServerApplication;
//import com.sinoyd.base.service.CalculateService;
//import com.sinoyd.base.service.impl.ExcelComponent;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.http.entity.ContentType;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.File;
//import java.io.FileInputStream;
//import java.util.List;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = EnableEurekaServerApplication.class)
//@Slf4j
//public class CalculateServiceImplTest {
//
//    private CalculateService calculateService;
//
////    private ExcelComponent excelComponent;
//
//    @Test
//    public void isNumber() {
////        Assert.assertEquals("正确", true, calculateService.isNumber("1.234"));
////        Assert.assertEquals("正确", false, calculateService.isNumber(""));
////        Assert.assertEquals("正确", false, calculateService.isNumber("xcfd"));
//    }
//
//    @Test
//    public void revise() {
//        try {
//            File file = new File("G:\\DINGTALK\\数据修约测试用例.xls");
//            FileInputStream fis = new FileInputStream(file);
//            String fileName = file.getName();
//            fileName = fileName.substring(fileName.lastIndexOf(File.separator) + 1);
//            MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, ContentType.APPLICATION_OCTET_STREAM.toString(), fis);
//            List<ReviseDataVO> dataList = excelComponent.importExcel(multipartFile, 0, 1, ReviseDataVO.class);
//            int rowNo = 2;
//            for (ReviseDataVO vo : dataList) {
//                String value = calculateService.revise(vo.getSignificantDigits(), vo.getDecimalDigits(), vo.getValue());
//                vo.setRowNo(rowNo++);
//                vo.setCalculateValue(value);
//                if (!value.equals(vo.getExpectedValue())) {
//                    System.out.println(vo);
//                }
//            }
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    @Autowired
//    @Lazy
//    public void setCalculateService(CalculateService calculateService) {
//        this.calculateService = calculateService;
//    }
//
//    @Autowired
//    @Lazy
//    public void setExcelComponent(ExcelComponent excelComponent) {
//        this.excelComponent = excelComponent;
//    }
//}