package com.sinoyd.lims.lim.service;

import java.util.List;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig2Test;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp;


/**
 * TestQCRemindConfig2Test操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
public interface TestQCRemindConfig2TestService extends IBaseJpaService<DtoTestQCRemindConfig2Test, String> {
    /**
     * 获取对应测试项目下的比例配置
     *
     * @param testIds   测试项目id集合
     * @return 比例配置
     */
     List<DtoTestQCRemindTemp> findByTestIds(List<String>testIds);
}