package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoFlowCalibration2Frequency;

import java.util.List;

/**
 * DtoFlowCalibration2Frequency操作接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/18
 */
public interface FlowCalibration2FrequencyService extends IBaseJpaService<DtoFlowCalibration2Frequency,String> {
    /**
     * 查询送样单下流量校准记录关联的点位周期
     * @param receiveId 送样单标识
     * @param flowCalibrationId 流量校准标识
     * @return 数据
     */
    List<DtoFlowCalibration2Frequency> findByReceive(String receiveId, String flowCalibrationId);
}
