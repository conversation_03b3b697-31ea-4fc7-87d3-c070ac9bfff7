package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ReportConfigCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.service.ReportConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * ReportConfig服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/13
 * @since V100R001
 */
@Api(tags = "示例: ReportConfig服务")
@RestController
@RequestMapping("api/lim/reportConfig")
@Validated
public class ReportConfigController extends BaseJpaController<DtoReportConfig, String, ReportConfigService> {


    /**
     * 分页动态条件查询ReportConfig
     *
     * @param reportConfigCriteria 条件参数
     * @return RestResponse<List < ReportConfig>>
     */
    @ApiOperation(value = "分页动态条件查询ReportConfig", notes = "分页动态条件查询ReportConfig")
    @GetMapping
    public RestResponse<List<DtoReportConfig>> findByPage(ReportConfigCriteria reportConfigCriteria) {
        PageBean<DtoReportConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoReportConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, reportConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询ReportConfig
     *
     * @param id 主键id
     * @return RestResponse<DtoReportConfig>
     */
    @ApiOperation(value = "按主键查询ReportConfig", notes = "按主键查询ReportConfig")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoReportConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoReportConfig> restResponse = new RestResponse<>();
        DtoReportConfig reportConfig = service.findOne(id);
        restResponse.setData(reportConfig);
        restResponse.setRestStatus(StringUtil.isNull(reportConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增ReportConfig
     *
     * @param reportConfig 实体列表
     * @return RestResponse<DtoReportConfig>
     */
    @ApiOperation(value = "新增ReportConfig", notes = "新增ReportConfig")
    @PostMapping
    public RestResponse<DtoReportConfig> create(@Validated @RequestBody DtoReportConfig reportConfig) {
        RestResponse<DtoReportConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.save(reportConfig));
        return restResponse;
    }

    /**
     * 新增ReportConfig
     *
     * @param reportConfig 实体列表
     * @return RestResponse<DtoReportConfig>
     */
    @ApiOperation(value = "修改ReportConfig", notes = "修改ReportConfig")
    @PutMapping
    public RestResponse<DtoReportConfig> update(@Validated @RequestBody DtoReportConfig reportConfig) {
        RestResponse<DtoReportConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.update(reportConfig));
        return restResponse;
    }

    /**
     * "根据id批量删除ReportConfig
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除ReportConfig", notes = "根据id批量删除ReportConfig")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 文件下载
     *
     * @param configId 下载的Id
     * @param response 响应流
     * @return 返回数据
     */
    @ApiOperation(value = "下载文档", notes = "下载文档")
    @GetMapping("/download/{configId}")
    public RestResponse<String> fileDownload(@PathVariable String configId, HttpServletResponse response) throws IOException {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setMsg(service.downloadReport(configId, response));
        return restResp;
    }

    /**
     * 据id查询报表配置(多个)
     *
     * @param configIds 报表配置id集合
     * @return 报表配置集合
     */
    @ApiOperation(value = "根据id查询报表配置(多个)", notes = "据id查询报表配置(多个)")
    @PostMapping("/multiple")
    public RestResponse<List<DtoReportConfig>> findByIds(@RequestBody List<String> configIds) {
        RestResponse<List<DtoReportConfig>> restResp = new RestResponse<>();
        restResp.setData(service.findAll(configIds));
        return restResp;
    }

    /**
     * 查询全部报表配置
     *
     * @return 报表配置集合
     */
    @ApiOperation(value = "查询全部报表配置", notes = "查询全部报表配置")
    @PostMapping("/all")
    public RestResponse<List<DtoReportConfig>> findAll() {
        RestResponse<List<DtoReportConfig>> restResp = new RestResponse<>();
        restResp.setData(service.findAll());
        return restResp;
    }

    /**
     * 复制ReportConfig
     *
     * @param reportConfigId 报表配置标识
     * @param code 新code
     * @return RestResponse<DtoReportConfig>
     */
    @ApiOperation(value = "复制ReportConfig", notes = "复制ReportConfig")
    @PostMapping("/copy/{reportConfigId}/{code}")
    public RestResponse<DtoReportConfig> copyReportConfig(@PathVariable("reportConfigId") String reportConfigId,@PathVariable("code") String code) {
        RestResponse<DtoReportConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.copyReportConfig(reportConfigId,code));
        return restResponse;
    }
}