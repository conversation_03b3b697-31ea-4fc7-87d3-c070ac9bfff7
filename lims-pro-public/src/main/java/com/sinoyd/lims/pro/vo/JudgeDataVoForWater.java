package com.sinoyd.lims.pro.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

@Data
public class JudgeDataVoForWater implements Serializable {

    @Excel(name = "样品编号" ,orderNum = "50",width = 23)
    private String sampleCode;

    @Excel(name = "在线值",orderNum = "60",width = 23)
    private String onlineValue;

    @Excel(name = "实验室测定值与标样理论值",orderNum = "70",width = 23)
    private String expectedValue;

    @Excel(name = "量纲",orderNum = "80",width = 13)
    private String dimensionName;

    @Excel(name = "评判方式",orderNum = "90",width = 23)
    private String judgingMethod;

    @Excel(name = "检查项值",orderNum = "100",width = 23)
    private String checkItemValue;

    @Excel(name = "允许限值",orderNum = "110",width = 23)
    private String allowLimit;

    @Excel(name = "是否合格",orderNum = "120",width = 23)
    private String pass;

    @Excel(name = "结果评判", needMerge = true ,orderNum = "130",width = 23)
    private String folderPass;

    @Excel(name = "备注", needMerge = true ,orderNum = "140",width = 23)
    private String remark;

}
