package com.sinoyd.lims.monitor.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 通州统计表单导出查询条件
 */
@Data
public class StatisticCriteria extends BaseCriteria implements Serializable {
    /**
     * 分析项目ids
     */
    private List<String> analyzeItemIds;

    /**
     * 委托方
     */
    private String customerName;

    /***
     * 采样结束时间
     */
    private String endTime;

    /**
     * 受检方
     */
    private String inspectedEnt;

    /**
     * 参数配置ids
     */
    private List<String> paramsConfigIds;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目类型ids
     */
    private List<String> projectTypeIds;

    /**
     * 采样地点
     */
    private String samplePlace;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型id集合
     */
    private List<String> sampleTypeIds;

    /**
     * 排序方式
     */
    private String sortId;

    /**
     * 采样开始时间
     */
    private String startTime;

    /**
     * 项目ids
     */
    private List<String> projectIds;

    /***
     * 是否检出
     */
    private Boolean enableData;

    /**
     * 是否标红
     */
    private Boolean overRed;

    /**
     * 分析项目排序
     */
    private String itemSortId;

    /**
     * 点位排序
     */
    private String pointSortId;

    /**
     * 显示类型
     */
    private List<String> evaluationType;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 排序方式
     */
    private String sort;

    /**
     * 是否显示分析数据
     */
    private Boolean isShowAnaData = false;

    /**
     * 是否显示参数数据
     */
    private Boolean isShowParamData = false;

    /**
     * 是否远程请求
     */
    private Boolean isRemoting = false;

    /**
     * 是否只显示重点污染源数据
     */
    private Boolean isShowPollution = false;

    /**
     * 是否根据权限过滤
     */
    private Boolean isAction = false;

    /**
     * 组织机构Id集合
     */
    private String orgIds;

    /**
     * 区域id集合
     */
    private List<String> areaId;

    /**
     * 是否只显示已检毕数据
     */
    private Boolean isComplete = false;

    /**
     * 是否显示现场质控（具体项目用）
     */
    private Boolean isShowQc = false;

    /**
     * 是否项目进度导出
     */
    private Boolean isInquiry = false;

    @Override
    public String getCondition() {
        return null;
    }
}
