package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaForUpdate;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaParams;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaRevise;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.service.ImportTestFormulaUpdateService;
import com.sinoyd.lims.lim.verify.TestFormulaFullVerifyHandler;
import com.sinoyd.lims.lim.verify.TestFormulaParamsVerifyHandler;
import com.sinoyd.lims.lim.verify.TestFormulaReviseVerifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目迁移导入
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/8
 * @since V100R001
 */
@Service
@Slf4j
public class ImportTestFormulaUpdateServiceImpl implements ImportTestFormulaUpdateService {

    private TestRepository testRepository;
    private ParamsPartFormulaRepository paramsPartFormulaRepository;
    private ParamsTestFormulaRepository paramsTestFormulaRepository;
    private SampleTypeRepository sampleTypeRepository;
    private ParamsFormulaRepository paramsFormulaRepository;
    private DimensionRepository dimensionRepository;
    private ParamsRepository paramsRepository;

    private TestFormulaFullVerifyHandler testFormulaFullVerifyHandler;
    private TestFormulaParamsVerifyHandler testFormulaParamsVerifyHandler;
    private TestFormulaReviseVerifyHandler testFormulaReviseVerifyHandler;

    @Override
    @Transactional
    public void importExcel(MultipartFile file,HttpServletResponse response) throws Exception {
        //文件类型校验
        PoiExcelUtils.verifyFileType(file);
        //通用参数
        ImportParams params = new ImportParams();
        params.setHeadRows(2);
        //公式配置部分
        testFormulaFullVerifyHandler = new TestFormulaFullVerifyHandler();
        initHandleContainer();
        params.setStartSheetIndex(0);
        params.setNeedVerify(true);
        params.setVerifyHandler(testFormulaFullVerifyHandler);
        List<DtoImportTestFormulaForUpdate> forlumlaImportList = getImportTestFormulaForUpdateData(params,file,response);
        clearPartContainer();
        updateFormula(forlumlaImportList);
        //参数拓展配置部分
        testFormulaParamsVerifyHandler = new TestFormulaParamsVerifyHandler();
        initParasmHandleContainer();
        params.setStartSheetIndex(1);
        params.setNeedVerify(true);
        params.setVerifyHandler(testFormulaParamsVerifyHandler);
        List<DtoImportTestFormulaParams> paramsImportList = getImportTestFormulaParamsData(params,file,response);
        clearParamsPartContainer();
        updateFormulaParams(paramsImportList);
        //修约公式部分
        testFormulaReviseVerifyHandler = new TestFormulaReviseVerifyHandler();
        initReviseHandleContainer();
        params.setStartSheetIndex(2);
        params.setNeedVerify(true);
        params.setVerifyHandler(testFormulaReviseVerifyHandler);
        List<DtoImportTestFormulaRevise> reviseImportList = getImportTestFormulaRevise(params,file,response);
        clearRevisePartContainer();
        updateFormulaRevise(reviseImportList);
        //数据非空校验
        if (StringUtil.isEmpty(forlumlaImportList)&&StringUtil.isEmpty(paramsImportList)&&StringUtil.isEmpty(reviseImportList)) {
            throw new BaseException("文件中无可导入数据，请检查后导入");
        }
    }

    /**
     *
     * @param params  excel解析参数
     * @param file    excel文件
     * @param response 响应
     * @return  测试项目公式sheet导入实体列表
     * @throws Exception 异常
     */
    private List<DtoImportTestFormulaForUpdate> getImportTestFormulaForUpdateData(ImportParams params,MultipartFile file,HttpServletResponse response) throws Exception{
        ExcelImportResult<DtoImportTestFormulaForUpdate> formulaResult = ExcelImportUtil.importExcelMore( file.getInputStream(),DtoImportTestFormulaForUpdate.class, params);
        if (formulaResult.isVerfiyFail()) {
            Workbook failWorkbook = formulaResult.getFailWorkbook();
            failWorkbook.setSheetName(0, "测试项目公式导入(修改）错误信息");
            PoiExcelUtils.downLoadExcel("测试项目公式导入(修改）失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        List<DtoImportTestFormulaForUpdate> forlumlaImportList = formulaResult.getList();
        forlumlaImportList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        return  forlumlaImportList;
    }

    /**
     *
     * @param params  excel解析参数
     * @param file    excel文件
     * @param response 响应
     * @return  参数sheet导入实体列表
     * @throws Exception 异常
     */
    private List<DtoImportTestFormulaParams> getImportTestFormulaParamsData(ImportParams params,MultipartFile file,HttpServletResponse response) throws Exception {
        ExcelImportResult<DtoImportTestFormulaParams> formulaParamsResult = ExcelImportUtil.importExcelMore( file.getInputStream(),DtoImportTestFormulaParams.class, params);
        if (formulaParamsResult.isVerfiyFail()) {
            Workbook failWorkbook = formulaParamsResult.getFailWorkbook();
            failWorkbook.setSheetName(1, "测试项目公式导入(修改）错误信息");
            PoiExcelUtils.downLoadExcel("测试项目公式导入(修改）失败信息", response, failWorkbook);
            throw new BaseException("导入信息中参数拓展配置部分有数据不正确，确认后导入");
        }
        List<DtoImportTestFormulaParams> paramsImportList = formulaParamsResult.getList();
        paramsImportList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        return paramsImportList;
    }

    /**
     *
     * @param params  excel解析参数
     * @param file    excel文件
     * @param response 响应
     * @return  修约公式sheet导入实体列表
     * @throws Exception 异常
     */
    private List<DtoImportTestFormulaRevise> getImportTestFormulaRevise(ImportParams params,MultipartFile file,HttpServletResponse response) throws Exception {
        ExcelImportResult<DtoImportTestFormulaRevise> formulaReviseResult = ExcelImportUtil.importExcelMore( file.getInputStream(), DtoImportTestFormulaRevise.class, params);
        if (formulaReviseResult.isVerfiyFail()) {
            Workbook failWorkbook = formulaReviseResult.getFailWorkbook();
            failWorkbook.setSheetName(2, "测试项目公式导入(修改）错误信息");
            PoiExcelUtils.downLoadExcel("测试项目公式导入(修改）失败信息", response, failWorkbook);
            throw new BaseException("导入信息中修约公式部分有数据不正确，确认后导入");
        }
        List<DtoImportTestFormulaRevise> reviseImportList = formulaReviseResult.getList();
        reviseImportList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        return  reviseImportList;
    }

    /**
     * 更新公式参数相关数据
     * @param paramsImportList 原始数据列表
     */
    private void updateFormulaParams(List<DtoImportTestFormulaParams> paramsImportList) {
        //所有待更新列表
        List<DtoParamsTestFormula> waitUpdateParamList = new ArrayList<>();
        List<DtoParamsPartFormula> waitUpdatePartFormulaList = new ArrayList<>();
        //所有测试项目公式参数
        List<DtoParamsTestFormula> paramsTestFormulaList = paramsTestFormulaRepository.findAll();
        //所有参数公式
        List<DtoParamsPartFormula> paramsPartFormulaList = paramsPartFormulaRepository.findAll();
        //所有量纲
        List<DtoDimension> DimensionList = dimensionRepository.findAll();
        //所有参数
        List<DtoParams> paramsList = paramsRepository.findAllByIsDeletedFalse();
        for (DtoImportTestFormulaParams dtoImportTestFormulaParams:paramsImportList) {
            String paramStr = dtoImportTestFormulaParams.getParamName().replace("[","").replace("]","");
            //测试项目公式参数
            collectWaitUpdateParamList(dtoImportTestFormulaParams,waitUpdateParamList,paramStr,paramsTestFormulaList,DimensionList,paramsList);
            //测试项目公式参数的公式
            collectWaitUpdatePartFormulaList(dtoImportTestFormulaParams,waitUpdatePartFormulaList,paramStr,paramsPartFormulaList);
        }
        //数据更新
        saveParamsPartFormulaList(waitUpdatePartFormulaList);
        saveParamsTestFormulaList(waitUpdateParamList);
    }

    /**
     * 归集待更新测试项目公式参数
     * @param dtoImportTestFormulaParams 导入实体
     * @param waitUpdateParamList       数据更新容器
     * @param paramStr                  参数名称
     * @param paramsTestFormulaList     所有测试项目公式参数
     * @param DimensionList             所有量纲
     * @param paramsList                所有参数
     */
    private void collectWaitUpdateParamList(DtoImportTestFormulaParams dtoImportTestFormulaParams, List<DtoParamsTestFormula> waitUpdateParamList, String paramStr,
                                           List<DtoParamsTestFormula> paramsTestFormulaList,List<DtoDimension> DimensionList,List<DtoParams> paramsList){
        DtoParamsTestFormula dtoParamsTestFormula = paramsTestFormulaList.stream().filter(p->p.getObjId().equals(dtoImportTestFormulaParams.getId()))
                .filter(p->p.getParamsName().equals(paramStr)).findFirst().orElse(null);

        if(StringUtil.isNull(dtoParamsTestFormula)){
            DtoParams dtoParams = paramsList.stream().filter(p->p.getParamName().equals(paramStr)).findFirst().orElse(null);
            if(StringUtil.isNotNull(dtoParams)){
                dtoParamsTestFormula = new  DtoParamsTestFormula();
                dtoParamsTestFormula.setObjId(dtoImportTestFormulaParams.getId());
                dtoParamsTestFormula.setParamsId(dtoParams.getId());
                dtoParamsTestFormula.setParamsName(dtoParams.getParamName());
                dtoParamsTestFormula.setAlias(dtoParams.getParamName());
            }
        }
        if(StringUtil.isNotNull(dtoImportTestFormulaParams.getDefaultValue())){
            dtoParamsTestFormula.setDefaultValue(dtoImportTestFormulaParams.getDefaultValue());
        }
        if(StringUtil.isNotNull(dtoImportTestFormulaParams.getSortNo())){
            dtoParamsTestFormula.setOrderNum(dtoImportTestFormulaParams.getSortNo());
        }
        if(StringUtil.isNotNull(dtoImportTestFormulaParams.getDimension())){
            DtoDimension dtoDimension = DimensionList.stream().filter(d->d.getDimensionName().equals(dtoImportTestFormulaParams.getDimension())).findFirst().orElse(null);
            if(StringUtil.isNotNull(dtoDimension)){
                dtoParamsTestFormula.setDimensionId(dtoDimension.getId());
                dtoParamsTestFormula.setDimension(dtoDimension.getDimensionName());
            }
        }
        else{
            dtoParamsTestFormula.setDimensionId(UUIDHelper.GUID_EMPTY);
            dtoParamsTestFormula.setDimension(null);
        }
        waitUpdateParamList.add(dtoParamsTestFormula);
    }

    /**
     * 归集所有待更新测试项目公式参数的公式
     * @param dtoImportTestFormulaParams 导入实体
     * @param waitUpdatePartFormulaList  待更新数据容器
     * @param paramStr                   参数名称
     * @param paramsPartFormulaList      所有参数公式
     */
    private void collectWaitUpdatePartFormulaList(DtoImportTestFormulaParams dtoImportTestFormulaParams,List<DtoParamsPartFormula> waitUpdatePartFormulaList, String paramStr,
                                                  List<DtoParamsPartFormula> paramsPartFormulaList){
        DtoParamsPartFormula paramsPartFormula = paramsPartFormulaList.stream().filter(p->EnumLIM.EnumPartFormulaType.参数公式.getValue().equals(p.getFormulaType())
                &&paramStr.equals(p.getParamsName())&&p.getFormulaId().equals(dtoImportTestFormulaParams.getId())).findFirst().orElse(null);
        if(StringUtil.isNull(paramsPartFormula)){
            paramsPartFormula = new DtoParamsPartFormula();
            paramsPartFormula.setFormulaId(dtoImportTestFormulaParams.getId());
            paramsPartFormula.setParamsName(paramStr);
            paramsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.参数公式.getValue());
        }
        if(StringUtil.isNotEmpty(dtoImportTestFormulaParams.getPartFormula())){
            paramsPartFormula.setFormula(dtoImportTestFormulaParams.getPartFormula());
        }else {
            paramsPartFormula.setFormula("");
        }
        if(StringUtil.isNotNull(dtoImportTestFormulaParams.getMostSignificance())){
            paramsPartFormula.setMostSignificance(dtoImportTestFormulaParams.getMostSignificance());
        }
        if(StringUtil.isNotNull(dtoImportTestFormulaParams.getMostDecimal())){
            paramsPartFormula.setMostDecimal(dtoImportTestFormulaParams.getMostDecimal());
        }
        waitUpdatePartFormulaList.add(paramsPartFormula);
    }

    /**
     * 更新公式相关数据
     * @param forlumlaImportList 原始数据列表
     */
    private void updateFormula(List<DtoImportTestFormulaForUpdate> forlumlaImportList) {
        //所有待更新列表
        List<DtoParamsFormula> formulaList = new ArrayList<>();
        List<DtoParamsPartFormula> partFormulaList = new ArrayList<>();
        List<DtoParamsTestFormula> testFormulaList = new ArrayList<>();
        List<DtoParamsTestFormula> testFormulaListForDel = new ArrayList<>();
        List<DtoParamsTestFormula> testFormulaListForAdd = new ArrayList<>();
        //所有测试项目
        List<DtoTest> testList = testRepository.findAll();
        //所有检测类型
        List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll();
        //所有测试项目公式参数
        List<DtoParamsTestFormula> paramsTestFormulaList = paramsTestFormulaRepository.findAll();
        //所有测得公式
        List<DtoParamsPartFormula> paramsPartFormulaList = paramsPartFormulaRepository.findAll();
        //所有公式
        List<DtoParamsFormula> paramsFormulaList = paramsFormulaRepository.findAll();
        //所有参数
        List<DtoParams> paramsList = paramsRepository.findAllByIsDeletedFalse();
        for (DtoImportTestFormulaForUpdate dtoImportTestFormulaForUpdate:forlumlaImportList){
            DtoParamsFormula paramsFormula = paramsFormulaList.stream().filter(p->p.getId().equals(dtoImportTestFormulaForUpdate.getId())).findFirst().orElse(null);
            //测试项目公式
            collectFormulaList(dtoImportTestFormulaForUpdate,formulaList,paramsFormula,testList,sampleTypeList);
            //测得量公式：填写了则更新对应公式的的“测得量公式”.针对填写了检出限的，测得量公式中“是否使用测试项目检出限”开关需要关闭；
            collectPartFormulaListForGj(dtoImportTestFormulaForUpdate,partFormulaList,paramsFormula,paramsPartFormulaList);
            //串联出证公式
            collectPartFormulaListForCl(dtoImportTestFormulaForUpdate,partFormulaList,paramsFormula,paramsPartFormulaList);

            //“公式参数”列中的内容，需要根据“、”进行截取，并按填写顺序自动赋予排序值；（从左到右，对应排序值从大到小）；
            //参数有就只调整排序，无就新增，比 excel公式参数 列数据多的参数会被删除
            if(StringUtil.isNotEmpty(dtoImportTestFormulaForUpdate.getParams())){
                //构建排序map
                List<String> paramNameListWithBoder = Arrays.asList(dtoImportTestFormulaForUpdate.getParams().split("、"));
                List<String> paramNameList = new ArrayList<>();
                paramNameListWithBoder.forEach(p->{
                    paramNameList.add(p.replace("[","").replace("]",""));
                });
                int maxSortValue = 999;
                Map<String,Integer> sortMap = new HashMap<>();
                for (String paramName:paramNameList) {
                    sortMap.put(paramName,maxSortValue--);
                }

                //测试项目的所有公式参数
                List<DtoParamsTestFormula> allParams = paramsTestFormulaList.stream().filter(p->p.getObjId().equals(dtoImportTestFormulaForUpdate.getId())).collect(Collectors.toList());
                //删除和更新的公式参数
                collectParamsTestFormulaListForUpdateAndDel(allParams,sortMap,testFormulaList,testFormulaListForDel);
                //待新增的公式参数
                collectParamsTestFormulaListForAdd(allParams,sortMap,testFormulaListForAdd,paramNameList,paramsList,paramsFormula);
            }
        }
        //数据更新
        saveParamsFormulaList(formulaList);
        saveParamsPartFormulaList(partFormulaList);
        saveParamsTestFormulaList(testFormulaList);
        saveParamsTestFormulaList(testFormulaListForAdd);
        deleteParamsTestFormulaList(testFormulaListForDel);
    }

    /**
     * 归集待更新测试项目公式
     * @param dtoImportTestFormulaForUpdate 导入实体
     * @param formulaList                  更新容器
     * @param paramsFormula                测试项目公式
     * @param testList                     所有测试项目
     * @param sampleTypeList               所有检测类型
     */
    private void collectFormulaList(DtoImportTestFormulaForUpdate dtoImportTestFormulaForUpdate,List<DtoParamsFormula> formulaList,DtoParamsFormula paramsFormula,
                                    List<DtoTest> testList,List<DtoSampleType> sampleTypeList){
        //根据公式id进行唯一性验证，并判定公式是否被修改，如果修改则更新
        DtoTest test = testList.stream().filter(t->t.getRedAnalyzeMethodName().equals(dtoImportTestFormulaForUpdate.getAnalyzeMethod())
                &&t.getRedAnalyzeItemName().equals(dtoImportTestFormulaForUpdate.getAnalyzeItem()))
                .findFirst().orElse(null);
        if(StringUtil.isNotNull(test)){
            paramsFormula.setObjectId(test.getId());
        }
        paramsFormula.setFormula(dtoImportTestFormulaForUpdate.getFormula());
        DtoSampleType sampleType = sampleTypeList.stream().filter(s->s.getTypeName().equals(dtoImportTestFormulaForUpdate.getSampleTypeForFormula())).findFirst().orElse(null);
        if(StringUtil.isNotNull(sampleType)){
            paramsFormula.setSampleTypeId(sampleType.getId());
        }
        formulaList.add(paramsFormula);
    }

    /**
     * 归集所有待处理测定量公式
     * @param dtoImportTestFormulaForUpdate  导入
     * @param partFormulaList                数据处理容器
     * @param paramsFormula                  测试项目公式
     * @param paramsPartFormulaList          所有测得量格式
     */
    private void collectPartFormulaListForGj(DtoImportTestFormulaForUpdate dtoImportTestFormulaForUpdate,List<DtoParamsPartFormula> partFormulaList,
                                        DtoParamsFormula paramsFormula,List<DtoParamsPartFormula> paramsPartFormulaList){
        if (StringUtil.isNotEmpty(dtoImportTestFormulaForUpdate.getPartFormula())){
            DtoParamsPartFormula paramsPartFormula = paramsPartFormulaList.stream().filter(p->p.getFormulaId().equals(dtoImportTestFormulaForUpdate.getId())
                    &&p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.加标公式.getValue()))
                    .findFirst().orElse(null);
            if(StringUtil.isNull(paramsPartFormula)){
                paramsPartFormula = new DtoParamsPartFormula();
                paramsPartFormula.setFormulaId(paramsFormula.getId());
                paramsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.加标公式.getValue());
            }
            paramsPartFormula.importToEntity(dtoImportTestFormulaForUpdate);
            partFormulaList.add(paramsPartFormula);
        }
    }

    /**
     * 归集所有待处理串联公式
     * @param dtoImportTestFormulaForUpdate  导入
     * @param partFormulaList                数据处理容器
     * @param paramsFormula                  测试项目公式
     * @param paramsPartFormulaList          所有测得量格式
     */
    private void collectPartFormulaListForCl(DtoImportTestFormulaForUpdate dtoImportTestFormulaForUpdate,List<DtoParamsPartFormula> partFormulaList,
                                        DtoParamsFormula paramsFormula,List<DtoParamsPartFormula> paramsPartFormulaList){
        DtoParamsPartFormula paramsPartFormula = paramsPartFormulaList.stream().filter(p->p.getFormulaId().equals(dtoImportTestFormulaForUpdate.getId())
                &&p.getFormulaType().equals(EnumLIM.EnumPartFormulaType.串联公式.getValue()))
                .findFirst().orElse(null);
        if(StringUtil.isNotEmpty(dtoImportTestFormulaForUpdate.getSeriesFormula())){
            if(StringUtil.isNull(paramsPartFormula)){
                paramsPartFormula = new DtoParamsPartFormula();
                paramsPartFormula.setFormulaId(paramsFormula.getId());
                paramsPartFormula.setFormulaType(EnumLIM.EnumPartFormulaType.串联公式.getValue());
            }
            paramsPartFormula.setFormula(dtoImportTestFormulaForUpdate.getSeriesFormula());
            partFormulaList.add(paramsPartFormula);
        }
    }

    /**
     * 归集所有待更新和删除的参数
     * @param allParams                    测试项目所有参数
     * @param sortMap                      参数排序map
     * @param testFormulaList              数据更新容器
     * @param testFormulaListForDel        数据删除容器
     */
    private void collectParamsTestFormulaListForUpdateAndDel(List<DtoParamsTestFormula> allParams,Map<String,Integer> sortMap,List<DtoParamsTestFormula> testFormulaList,List<DtoParamsTestFormula> testFormulaListForDel){
        // 原有参数比 excel公式参数 列数据多的参数会被删除 存在就更新
        for (DtoParamsTestFormula dtoParamsTestFormula:allParams) {
            if(StringUtil.isNotNull(sortMap.get(dtoParamsTestFormula.getParamsName()))){
                dtoParamsTestFormula.setOrderNum(sortMap.get(dtoParamsTestFormula.getParamsName()));
                if(StringUtil.isEmpty(dtoParamsTestFormula.getAlias())){
                    dtoParamsTestFormula.setAlias(dtoParamsTestFormula.getParamsName());
                }
                testFormulaList.add(dtoParamsTestFormula);
            }
            else{
                testFormulaListForDel.add(dtoParamsTestFormula);
            }
        }
    }

    /**
     * 归集所有待新增的参数
     * @param allParams                  测试项目所有参数
     * @param sortMap                    参数排序map
     * @param testFormulaListForAdd      数据新增容器
     * @param paramNameList              excel中测试项目公式所有参数
     * @param paramsList                 所有参数
     * @param paramsFormula              测试项目公式
     */
    private void collectParamsTestFormulaListForAdd(List<DtoParamsTestFormula> allParams,Map<String,Integer> sortMap,List<DtoParamsTestFormula> testFormulaListForAdd,
                                                    List<String> paramNameList,List<DtoParams> paramsList,DtoParamsFormula paramsFormula){
        List<String> allParamsName = allParams.stream().map(DtoParamsTestFormula::getParamsName).collect(Collectors.toList());
        paramNameList.removeAll(allParamsName);
        for (String paramName:paramNameList) {
            String finalParamName = paramName;
            DtoParams dtoParams = paramsList.stream().filter(p->p.getParamName().equals(finalParamName)).findFirst().orElse(null);
            if(StringUtil.isNotNull(dtoParams)){
                DtoParamsTestFormula paramsTestFormula = new DtoParamsTestFormula();
                paramsTestFormula.setObjId(paramsFormula.getId());
                paramsTestFormula.setParamsId(dtoParams.getId());
                paramsTestFormula.setParamsName(dtoParams.getParamName());
                paramsTestFormula.setAlias(dtoParams.getParamName());
                paramsTestFormula.setOrderNum(sortMap.get(finalParamName));
                testFormulaListForAdd.add(paramsTestFormula);
            }
        }
    }


    /**
     * 数据更新
     * @param formulaList 数据列表
     */
    @Transactional
    public void saveParamsFormulaList(List<DtoParamsFormula> formulaList) {
        paramsFormulaRepository.save(formulaList);
    }

    /**
     * 数据更新
     * @param partFormulaList 数据列表
     */
    @Transactional
    public void saveParamsPartFormulaList(List<DtoParamsPartFormula> partFormulaList) {
        paramsPartFormulaRepository.save(partFormulaList);
    }

    /**
     * 数据更新
     * @param testFormulaList 数据列表
     */
    @Transactional
    public void saveParamsTestFormulaList(List<DtoParamsTestFormula> testFormulaList) {
        paramsTestFormulaRepository.save(testFormulaList);
    }

    /**
     * 数据删除
     * @param testFormulaListForDel 数据列表
     */
    @Transactional
    public void deleteParamsTestFormulaList(List<DtoParamsTestFormula> testFormulaListForDel) {
        paramsTestFormulaRepository.delete(testFormulaListForDel);
    }



    /**
     * 更新测试项目公式修约公式部分相关数据
     * @param reviseImportList 修约公司列表
     */
    private void updateFormulaRevise(List<DtoImportTestFormulaRevise> reviseImportList){
        //所有部分公式
        List<DtoParamsPartFormula> paramsPartFormulaList = paramsPartFormulaRepository.findAll();
        //待更新或新增列表
        List<DtoParamsPartFormula> list = new ArrayList<>();
        for (DtoImportTestFormulaRevise reviseData :reviseImportList) {
            List<DtoParamsPartFormula> existList = paramsPartFormulaList.stream().filter(p->reviseData.getId().equals(p.getFormulaId())&&
                                                                           EnumLIM.EnumPartFormulaType.修约公式.getValue().equals(p.getFormulaType())).collect(Collectors.toList());
            DtoParamsPartFormula exist = existList.stream().filter(e->e.getFormula().equals(reviseData.getReviseFormula())).findFirst().orElse(null);
            if(!existList.isEmpty()&&StringUtil.isNotNull(exist)){
                if(StringUtil.isNotNull(exist)){
                    if(StringUtil.isNotNull(reviseData.getReviseMostSignificance())){
                        exist.setMostSignificance(reviseData.getReviseMostSignificance());
                    }
                    if(StringUtil.isNotNull(reviseData.getReviseMostDecimal())){
                        exist.setMostDecimal(reviseData.getReviseMostDecimal());
                    }
                }
            }
            else{
                exist = new DtoParamsPartFormula();
                exist.setFormula(reviseData.getReviseFormula());
                exist.setFormulaId(reviseData.getId());
                exist.setFormulaType(EnumLIM.EnumPartFormulaType.修约公式.getValue());
                if(StringUtil.isNotNull(reviseData.getReviseMostSignificance())){
                    exist.setMostSignificance(reviseData.getReviseMostSignificance());
                }
                if(StringUtil.isNotNull(reviseData.getReviseMostDecimal())){
                    exist.setMostDecimal(reviseData.getReviseMostDecimal());
                }
            }
            list.add(exist);
        }
        //数据更新
        saveParamsPartFormulaList(list);
    }


    /**
     * 初始化校验所需数据，避免循环查询导致性能问题
     */
    private void initHandleContainer(){
        testFormulaFullVerifyHandler.setAllTestList(testRepository.findAll());
        testFormulaFullVerifyHandler.setAllSampleTypeList(sampleTypeRepository.findAll());
        testFormulaFullVerifyHandler.setAllParamsList(paramsRepository.findAllByIsDeletedFalse());
        testFormulaFullVerifyHandler.setDuplicationCheckList(new ArrayList<>());
    }

    /**
     * 初始化校验所需数据，避免循环查询导致性能问题
     */
    private void initParasmHandleContainer(){
        testFormulaParamsVerifyHandler.setAllParamsFormulaList(paramsFormulaRepository.findAll());
        testFormulaParamsVerifyHandler.setDuplicationCheckList(new ArrayList<>());
        testFormulaParamsVerifyHandler.setAllDimensionList(dimensionRepository.findAll());
        testFormulaParamsVerifyHandler.setAllParamsList(paramsRepository.findAllByIsDeletedFalse());
    }

    /**
     * 初始化校验所需数据，避免循环查询导致性能问题
     */
    private void initReviseHandleContainer(){
        testFormulaReviseVerifyHandler.setDuplicationCheckList(new ArrayList<>());
    }

    /**
     * 移除下次调用校验器需重新赋值的容器，避免数据混淆
     */
    private void clearPartContainer(){
        testFormulaFullVerifyHandler.getDuplicationCheckList().clear();
    }

    /**
     * 移除下次调用校验器需重新赋值的容器，避免数据混淆
     */
    private void clearParamsPartContainer(){
        testFormulaParamsVerifyHandler.getDuplicationCheckList().clear();
    }

    /**
     * 移除下次调用校验器需重新赋值的容器，避免数据混淆
     */
    private void clearRevisePartContainer(){
        testFormulaReviseVerifyHandler.getDuplicationCheckList().clear();
    }

    @Autowired
    @Lazy
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    @Lazy
    public void setParamsPartFormulaRepository(ParamsPartFormulaRepository paramsPartFormulaRepository) {
        this.paramsPartFormulaRepository = paramsPartFormulaRepository;
    }

    @Autowired
    @Lazy
    public void setParamsTestFormulaRepository(ParamsTestFormulaRepository paramsTestFormulaRepository) {
        this.paramsTestFormulaRepository = paramsTestFormulaRepository;
    }

    @Autowired
    @Lazy
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    @Lazy
    public void setParamsFormulaRepository(ParamsFormulaRepository paramsFormulaRepository) {
        this.paramsFormulaRepository = paramsFormulaRepository;
    }

    @Autowired
    @Lazy
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    @Lazy
    public void setParamsRepository(ParamsRepository paramsRepository) {
        this.paramsRepository = paramsRepository;
    }
}
