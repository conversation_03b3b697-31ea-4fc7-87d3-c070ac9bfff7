package com.sinoyd.base.service;

import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;

/**
 * 评价标准操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
public interface EvaluationCriteriaService extends IBaseJpaService<DtoEvaluationCriteria, String> {

    /**
     * 重新定义接口（为了返回调用该接口类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回评价标准对象
     */
    @Override
    DtoEvaluationCriteria findOne(String id);

    /**
     * 初始化指定的评价标准数据
     *
     * @param id 评价标准id
     * @return 返回评价信息
     */
    DtoEvaluationCriteria initEvaluationCriteriaRedisById(String id);

    /**
     * 新增评价标准redis数据
     */
    void saveEvaluationCriteriaRedis(DtoEvaluationCriteria dtoEvaluationCriteria);

    /**
     * 刷新数据
     *
     */
    void refresh();
}
