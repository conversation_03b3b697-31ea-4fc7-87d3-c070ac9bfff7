package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
* 报告基本信息
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataReportBaseInfoVO {
    /**
     * id
     */
    private String id;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 系统编号
     */
    private String systemCode;

    /**
     * 受检单位
     */
    private String inspectedEnt;

    /**
     * 受检单位地址
     */
    private String inspectedAddress;

    /**
     * 委托单位
     */
    private String customerName;

    /**
     * 委托单位地址
     */
    private String customerAddress;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
