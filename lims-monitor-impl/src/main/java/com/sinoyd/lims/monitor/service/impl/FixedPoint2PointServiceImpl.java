package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Point;
import com.sinoyd.lims.monitor.repository.rcc.FixedPoint2PointRepository;
import com.sinoyd.lims.monitor.service.FixedPoint2PointService;
import org.springframework.stereotype.Service;


/**
 * FixedPoint2Point操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class FixedPoint2PointServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFixedPoint2Point, String, FixedPoint2PointRepository> implements FixedPoint2PointService {


    @Override
    public void findByPage(PageBean<DtoFixedPoint2Point> pb, BaseCriteria fixedPoint2PointCriteria) {
        pb.setEntityName("DtoFixedPoint2Point a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fixedPoint2PointCriteria);
    }
}