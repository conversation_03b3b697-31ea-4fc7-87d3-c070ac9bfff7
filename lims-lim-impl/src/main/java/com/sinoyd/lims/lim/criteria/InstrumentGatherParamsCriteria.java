package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仪器接入参数查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentGatherParamsCriteria extends BaseCriteria {

    /**
     * 仪器接入id
     */
    private String instrumentGatherId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(instrumentGatherId)) {
            condition.append(" and x.instrumentGatherId = :instrumentGatherId");
            values.put("instrumentGatherId", instrumentGatherId);
        }
        return condition.toString();
    }
}