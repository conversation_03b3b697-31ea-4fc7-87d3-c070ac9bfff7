package com.sinoyd.base.service.impl;


import com.sinoyd.base.criteria.EnterpriseCriteria;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoEnterpriseEvaluate;
import com.sinoyd.base.dto.lims.DtoEnterpriseExtend;
import com.sinoyd.base.dto.lims.DtoPollutionDischargeSync;
import com.sinoyd.base.entity.Enterprise;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseEvaluateRepository;
import com.sinoyd.base.repository.lims.EnterpriseExtendRepository;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.lims.PollutionDischargeSyncRepository;
//import com.sinoyd.base.service.EnterpriseExtendService;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
@Service
@Slf4j
public class EnterpriseServiceImpl extends BaseJpaServiceImpl<DtoEnterprise, String, EnterpriseRepository> implements EnterpriseService {

    @Autowired
    private EnterpriseExtendRepository enterpriseExtendRepository;

    @Autowired
    private AreaService areaService;

    @Autowired
    private EnterpriseEvaluateRepository enterpriseEvaluateRepository;

    @Autowired
    private PollutionDischargeSyncRepository pollutionDischargeSyncRepository;

//    private EnterpriseExtendService enterpriseExtendService;

    /**
     * 新增客户
     *
     * @param enterprise
     */
    @Transactional
    @Override
    public DtoEnterprise save(DtoEnterprise enterprise) {
        String id = enterprise.getId();
        String socialCreditCode = enterprise.getSocialCreditCode();
        String name = enterprise.getName();
//        Integer nameCount = repository.getCountByName(id, name, enterprise.getType());
//        if (nameCount > 0) {
//            throw new BaseException("已存在相同名称的客户!");
//        }
        if (StringUtils.isNotNullAndEmpty(socialCreditCode)) {
            Integer codeCount = repository.getCountByNameAndSocialCreditCode(id, socialCreditCode, name, enterprise.getType());
            if (codeCount > 0) {
                throw new BaseException("已存在相同名称以及相同社会信用代码的客户!");
            }
        }
        if (StringUtils.isNotNullAndEmpty(name)) {
            String fullPinYin = PinYinUtil.getFullSpell(name), pinYin = PinYinUtil.getFirstSpell(name);
            validPy(fullPinYin, pinYin);
            enterprise.setFullPinYin(fullPinYin);
            enterprise.setPinYin(pinYin);
        }
//        //设置默认值 pollutionsourcetype
//        if(!StringUtil.isNotNull(enterprise.getPollutionSourceType())){
//            enterprise.setPollutionSourceType(EnumBase.EnumPollutionSourceType.污水处理厂.getValue());
//        }
        DtoEnterprise item = super.save(enterprise);
        enterprise.setId(item.getId());
        saveExtend(enterprise);
        saveEvaluate(enterprise);
        item.setEnterpriseExtend(enterprise.getEnterpriseExtend());
        return item;
    }


    private void saveExtend(DtoEnterprise enterprise) {
        DtoEnterpriseExtend extend = new DtoEnterpriseExtend();
        Boolean isNew = true;
        List<DtoEnterpriseExtend> extendList = enterpriseExtendRepository.findByEntId(enterprise.getId());
        if (StringUtil.isNotEmpty(extendList)) {
            extend = extendList.get(0);
            isNew = false;
        }
        extend.setEntId(enterprise.getId());
        List<Integer> pollutionSourceTypeList = enterprise.getPollutionSourceTypeList();
        String sourceTypeStr = "";
        if (StringUtil.isNotEmpty(pollutionSourceTypeList)) {
            List<String> strList = new ArrayList<>();
            pollutionSourceTypeList.forEach(p -> strList.add(String.valueOf(p)));
            sourceTypeStr = String.join(";", strList);
        }
        extend.setPollutionSourceType(sourceTypeStr);
        extend.setIsUsed(enterprise.getIsUsed());
        extend.setIsBreak(enterprise.getIsBreak());
        extend.setBreakInfo(enterprise.getBreakInfo());
        extend.setAttentionDegree(enterprise.getAttentionDegree());
        extend.setSubRate(enterprise.getSubRate());
        extend.setPollutionCode(enterprise.getPollutionCode());
        if (isNew) {
            enterpriseExtendRepository.save(extend);
        } else {
            comRepository.merge(extend);
//            enterpriseExtendService.update(extend);
        }
        enterprise.setEnterpriseExtend(extend);
    }

    /**
     * 更新客户
     *
     * @param enterprise
     */
    @Transactional
    @Override
    public DtoEnterprise update(DtoEnterprise enterprise) {
        String id = enterprise.getId();
        String socialCreditCode = enterprise.getSocialCreditCode();
        String name = enterprise.getName();
//        Integer nameCount = repository.getCountByName(id, name,enterprise.getType());
//        if (nameCount > 0) {
//            throw new BaseException("已存在相同名称的客户!");
//        }
        if (StringUtils.isNotNullAndEmpty(socialCreditCode)) {
            Integer codeCount = repository.getCountByNameAndSocialCreditCode(id, socialCreditCode, name, enterprise.getType());
            if (codeCount > 0) {
                throw new BaseException("已存在相同名称以及相同社会信用代码的客户!");
            }
        }

        saveExtend(enterprise);
        saveEvaluate(enterprise);
        if (StringUtils.isNotNullAndEmpty(name)) {
            String fullPinYin = PinYinUtil.getFullSpell(name), pinYin = PinYinUtil.getFirstSpell(name);
            validPy(fullPinYin, pinYin);
            enterprise.setFullPinYin(fullPinYin);
            enterprise.setPinYin(pinYin);
        }
        //处理排污许可证同步信息
        deletePdpSyncData(enterprise);
        DtoEnterprise update = super.update(enterprise);
        update.setEnterpriseExtend(enterprise.getEnterpriseExtend());
        //处理
        return update;
    }

    /**
     * 处理删除排污许可证同步信息
     *
     * @param enterprise 企业数据
     */
    private void deletePdpSyncData(DtoEnterprise enterprise) {
        DtoEnterprise dbEnt = findOne(enterprise.getId());
        if (StringUtil.isEmpty(enterprise.getPollutionDischargeCode()) || (StringUtil.isNotEmpty(dbEnt.getPollutionDischargeCode()) &&
                !dbEnt.getPollutionDischargeCode().equals(enterprise.getPollutionDischargeCode()))) {
            //设置企业的排污许可证同步状态为未同步
            enterprise.setIsSyncPollutionDischarge(false);
            //删除排污许可证同步信息
            DtoPollutionDischargeSync syncData = pollutionDischargeSyncRepository.findByEnterpriseId(enterprise.getId());
            if (StringUtil.isNotNull(syncData)) {
                pollutionDischargeSyncRepository.logicDeleteById(syncData.getId());
            }
        }

    }

    /**
     * 校验拼音长度
     *
     * @param fullPinYin 全拼
     * @param pinYin     拼音
     */
    private void validPy(String fullPinYin, String pinYin) {
        if (StringUtil.isNotNull(fullPinYin) && fullPinYin.length() > 100) {
            throw new BaseException("全拼最大长度不能超过100个字符");
        }
        if (StringUtil.isNotNull(pinYin) && pinYin.length() > 50) {
            throw new BaseException("拼音缩写最大长度不能超过50个字符");
        }
    }

    /**
     * 设置分页查询的表名以及查询
     *
     * @param pageBean 分页参数
     * @param criteria 分页参数
     */
    protected void setSelectEntity(PageBean<DtoEnterprise> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoEnterprise p");
        pageBean.setSelect("select p");
    }

    /**
     * 分页查询客户
     *
     * @param pageBean
     * @param criteria
     */
    @Override
    public void findByPage(PageBean<DtoEnterprise> pageBean, BaseCriteria criteria) {
        setSelectEntity(pageBean, criteria);
        //处理所属区域
        EnterpriseCriteria entCriteria = (EnterpriseCriteria) criteria;
        if (StringUtils.isNotNullAndEmpty(entCriteria.getAreaId()) && !UUIDHelper.GUID_EMPTY.equals(entCriteria.getAreaId())) {
            List<String> areaIds = new ArrayList<>();
            areaIds = getAreaIds(areaIds, entCriteria.getAreaId());
            entCriteria.setAreaIds(areaIds);
        }
        super.findByPage(pageBean, entCriteria);

        List<DtoEnterprise> list = pageBean.getData();

        List<String> entIds = list.stream().map(DtoEnterprise::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(entIds)) {
            List<DtoEnterpriseExtend> extendList = enterpriseExtendRepository.findByEntIds(entIds);
            Map<String, DtoEnterpriseExtend> extendMap = extendList.stream().collect(Collectors.toMap(DtoEnterpriseExtend::getEntId, dto -> dto));
            if (StringUtil.isNotEmpty(extendList)) {
                for (DtoEnterprise d : list) {
//                    List<DtoEnterpriseExtend> extendByIdList = extendList.stream().filter(p -> p.getEntId().equals(d.getId())).collect(Collectors.toList());
                    DtoEnterpriseExtend extend = extendMap.get(d.getId());
                    if (StringUtil.isNotNull(extend)) {
//                        DtoEnterpriseExtend extend = extendByIdList.get(0);

                        if ((d.getType() & EnumBase.EnumEnterpriseType.污染源.getValue()) != 0) {
                            d.setIsPollution(true);
                        } else {
                            d.setIsPollution(false);
                        }
                        String sourceType = extend.getPollutionSourceType();
                        List<Integer> sourceTypeList = new ArrayList<>();
                        if (StringUtil.isNotEmpty(sourceType)) {
                            List<String> sourceTypeStrList = Arrays.asList(sourceType.split(";"));
                            sourceTypeStrList.forEach(p -> sourceTypeList.add(Integer.valueOf(p)));
                        }
                        d.setPollutionSourceTypeList(sourceTypeList);
                        d.setIsUsed(extend.getIsUsed());
                        d.setIsBreak(extend.getIsBreak());
                        d.setBreakInfo(extend.getBreakInfo());
                        d.setAttentionDegree(extend.getAttentionDegree());
                        d.setSubRate(extend.getSubRate());
                        d.setPollutionCode(extend.getPollutionCode());
                    }
                }
            }
        }
        pageBean.setData(list);
    }

    @Override
    public List<DtoEnterprise> findEntByPage(PageBean<DtoEnterprise> pageBean, BaseCriteria criteria) {
        int pageNo = pageBean.getPageNo();
        int rowsPerPage = pageBean.getRowsPerPage();
        pageBean.setPageNo(1);
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        this.findByPage(pageBean, criteria);
        List<DtoEnterprise> list = pageBean.getData();
        List<String> entIds = list.stream().map(DtoEnterprise::getId).collect(Collectors.toList());
        List<DtoEnterpriseEvaluate> enterpriseEvaluates = enterpriseEvaluateRepository.findByEntIdIn(entIds);
        Map<String, DtoEnterpriseEvaluate> enterpriseEvaluateMap = enterpriseEvaluates.stream().collect(Collectors.toMap(DtoEnterpriseEvaluate::getEntId, p -> p));
        for (DtoEnterprise enterprise : list) {
            DtoEnterpriseEvaluate evaluate = enterpriseEvaluateMap.get(enterprise.getId());
            enterprise.setYearSn("");
            if (StringUtil.isNotNull(evaluate)) {
                enterprise.setYearSn(evaluate.getYearSn());
                enterprise.setCertName(evaluate.getCertName());
                enterprise.setCertCode(evaluate.getCertCode());
                enterprise.setCertEffectiveTime(evaluate.getCertEffectiveTime());
            }
        }
        list.sort(Comparator.comparing(DtoEnterprise::getYearSn).reversed().thenComparing(DtoEnterprise::getName));
        list = list.stream().skip((long) (pageNo - 1) * rowsPerPage).limit(rowsPerPage).collect(Collectors.toList());
        return list;
    }

    @Override
    public List<DtoEnterprise> findPollutionDischargeCodeList() {
        return findAll().stream()
                .filter(p -> StringUtil.isNotEmpty(p.getPollutionDischargeCode()))
                .sorted(Comparator.comparing(Enterprise::getCreateDate)).collect(Collectors.toList());
    }

    @Override
    public DtoEnterprise findOne(String key) {
        DtoEnterprise enterprise = super.findOne(key);
        if (StringUtil.isNotNull(enterprise)) {
            enterprise.setEnterpriseEvaluate(enterpriseEvaluateRepository.findByEntId(enterprise.getId()));
            List<DtoEnterpriseExtend> enterpriseExtendList = enterpriseExtendRepository.findByEntId(key);
            if (StringUtil.isNotEmpty(enterpriseExtendList)) {
                DtoEnterpriseExtend extend = enterpriseExtendList.get(0);
                if ((enterprise.getType() & EnumBase.EnumEnterpriseType.污染源.getValue()) != 0) {
                    enterprise.setIsPollution(true);
                }
                String sourceType = extend.getPollutionSourceType();
                List<Integer> sourceTypeList = new ArrayList<>();
                if (StringUtil.isNotEmpty(sourceType)) {
                    List<String> sourceTypeStrList = Arrays.asList(sourceType.split(";"));
                    sourceTypeStrList.forEach(p -> sourceTypeList.add(Integer.valueOf(p)));
                }
                enterprise.setPollutionSourceTypeList(sourceTypeList);
                enterprise.setIsUsed(extend.getIsUsed());
                enterprise.setIsBreak(extend.getIsBreak());
                enterprise.setBreakInfo(extend.getBreakInfo());
                enterprise.setAttentionDegree(extend.getAttentionDegree());
                enterprise.setSubRate(extend.getSubRate());
                enterprise.setPollutionCode(extend.getPollutionCode());
            }
        }
        return enterprise;
    }

    private List<String> getAreaIds(List<String> areaIds, String areaId) {
        areaIds.add(areaId);
        List<DtoArea> list = areaService.findByParentId(areaId);
        if (StringUtil.isNotEmpty(list)) {
            for (DtoArea area : list) {
                getAreaIds(areaIds, area.getId());
            }
        }
        return areaIds;
    }

    /**
     * 新增供应商评价V2
     *
     * @param enterprise 供应商实体
     */
    private void saveEvaluate(DtoEnterprise enterprise) {
        DtoEnterpriseEvaluate enterpriseEvaluate = enterprise.getEnterpriseEvaluate();
        if (StringUtil.isNotNull(enterpriseEvaluate)) {
            // 相同名称的存在
            List<DtoEnterprise> enterpriseList = repository.findByName(enterprise.getName())
                    .stream().filter(v -> v.getName().equals(enterprise.getName())).collect(Collectors.toList());
            enterpriseList.removeIf(DtoEnterprise::getIsDeleted);
            // 新增时判断
            if (StringUtil.isNotEmpty(enterpriseList)) {
                List<String> entIds = enterpriseList.stream().map(DtoEnterprise::getId).collect(Collectors.toList());
                entIds.removeIf(enterprise.getId()::equals);
                List<DtoEnterpriseEvaluate> evaluateList = enterpriseEvaluateRepository.findByEntIdIn(entIds);
                if (StringUtil.isNotEmpty(evaluateList) && evaluateList.stream().anyMatch(p -> p.getYearSn().equals(enterpriseEvaluate.getYearSn()))) {
                    throw new BaseException("同一年度中已存在相同名称的供应商!");
                }
            }
            enterpriseEvaluate.setEntId(enterprise.getId());
            DtoEnterpriseEvaluate evaluate = enterpriseEvaluateRepository.save(enterpriseEvaluate);
            enterprise.setEnterpriseEvaluate(evaluate);
        }
    }

    @Override
    public List<DtoEnterprise> findAllDeleted(List<String> ids) {
        return repository.findAllDeleted(ids);
    }

    @Override
    public List<DtoEnterprise> findAllDeleted() {
        return repository.findAllDeleted();
    }

    @Transactional
    @Override
    public List<DtoEnterprise> changePinYin() {
        List<DtoEnterprise> dtoEnterprises = repository.findPinYinIsNotExit();
        for (DtoEnterprise dtoEnterprise : dtoEnterprises) {
            dtoEnterprise.setPinYin(PinYinUtil.getFirstSpell(dtoEnterprise.getName()));
            dtoEnterprise.setPinYin(PinYinUtil.getFullSpell(dtoEnterprise.getName()));
        }
        return super.update(dtoEnterprises);
    }

    @Override
    public Map<String, String> findEntName(String entId) {
        DtoEnterprise enterpriseExpand = repository.findOne(entId);
        Map<String, String> map = new HashMap<>();
        if (StringUtil.isNotNull(enterpriseExpand)) {
            map.put("shanghaiEntId", enterpriseExpand.getRegulateId());
            map.put("shanghaiEntName", enterpriseExpand.getRegulateName());
        }
        return map;
    }


    @Override
    public DtoEnterprise findAttachPath(String id) {
        return super.findOne(id);
    }

    @Override
    public List<DtoEnterprise> findAllForCustomer() {
        return repository.findByIsDeletedFalseAndType(EnumBase.EnumEnterpriseType.客户.getValue() +
                EnumBase.EnumEnterpriseType.污染源.getValue());
    }

    @Override
    public List<DtoEnterpriseExtend> findEnterpriseExtend(Collection<String> entIds) {
        return enterpriseExtendRepository.findByEntIds(entIds);
    }

//    @Autowired
//    @Lazy
//    public void setEnterpriseExtendService(EnterpriseExtendService enterpriseExtendService) {
//        this.enterpriseExtendService = enterpriseExtendService;
//    }
}