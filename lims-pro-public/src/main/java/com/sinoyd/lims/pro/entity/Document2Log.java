package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;


/**
 * Document2Log实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Document2Log")
 @Data
 public  class Document2Log implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    /**
    * 文档id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("文档id")
	private String documentId;
    
    /**
    * 日志id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("日志id")
	private String logId;
    
 }