package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.OAProjectExpend;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoOAProjectExpend实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_OAProjectExpend") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoOAProjectExpend extends OAProjectExpend {

    /**
     * 标题
     */
    @Transient
    private String title;
}