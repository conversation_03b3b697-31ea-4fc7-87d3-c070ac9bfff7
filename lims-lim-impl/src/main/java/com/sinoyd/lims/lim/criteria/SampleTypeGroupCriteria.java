package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 样品分组查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年1月8日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleTypeGroupCriteria extends BaseCriteria {

    /**
     * 分组类型 1：分组规则  2：分组
     */
    private Integer groupType;
    /**
     * 父Id
     */
    private String parentId;
    //分组排序 -表示降序 +表示升序
    // private String sort;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型小类id
     */
    private String sonSampleTypeId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();

        condition.append(" and groupType  = :groupType");
        values.put("groupType", this.groupType);

        if (StringUtil.isNotEmpty(this.parentId) && !UUIDHelper.GUID_EMPTY.equals(this.parentId)) {
            condition.append(" and parentId = :parentId");
            values.put("parentId", this.parentId);
        }

        if (StringUtil.isNotEmpty(this.sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.sampleTypeId)) {
            condition.append(" and sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        return condition.toString();
    }
}