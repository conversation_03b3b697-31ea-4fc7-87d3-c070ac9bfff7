package com.sinoyd.lims.lim.data.qcconfig.controller;

import com.sinoyd.base.dto.rcc.DtoSubstitute;
import com.sinoyd.base.dto.vo.DeviationFormulaSrcVO;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.criteria.QualityControlLimitCriteria;
import com.sinoyd.lims.lim.data.qcconfig.dto.DtoQCTemp;
import com.sinoyd.lims.lim.data.qcconfig.service.QCConfigService;
import com.sinoyd.lims.lim.dto.customer.DtoQualityControlDeviation;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 质控限值配置接口
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/14
 */
@Api(tags = "数据同步服务接口")
@RestController
@RequestMapping("/api/lim/qcConfig")
@Validated
public class QCConfigController extends ExceptionHandlerController<QCConfigService> {

    private QCConfigService qcConfigService;

    /**
     * 根据测试项目Id和质控类型查询所有质控限值数据
     *
     * @param criteria 分页条件
     * @return 返回分页数据
     */
    @ApiOperation(value = "根据测试项目Id和质控类型查询所有质控限值数据", notes = "根据测试项目Id和质控类型查询所有质控限值数据")
    @GetMapping
    public RestResponse<List<DtoQualityControlLimit>> findByPage(QualityControlLimitCriteria criteria) {
        RestResponse<List<DtoQualityControlLimit>> restResp = new RestResponse<>();
        PageBean<DtoQualityControlLimit> page = super.getPageBean();
        qcConfigService.findByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 条件查询所有的测试项目
     *
     * @param criteria 分页条件
     * @return 返回分页数据
     */
    @ApiOperation(value = "条件查询所有的测试项目", notes = "条件查询所有的测试项目")
    @GetMapping("/test")
    public RestResponse<List<DtoTest>> findTestByPage(TestCriteria criteria) {
        RestResponse<List<DtoTest>> restResp = new RestResponse<>();
        PageBean<DtoTest> page = super.getPageBean();
        qcConfigService.findTestByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 查询所有的评判方式
     *
     * @return 查询结果
     */
    @ApiOperation(value = "查询所有的评判方式", notes = "查询所有的评判方式")
    @GetMapping("/JudgingMethod")
    public RestResponse<Map<Integer, String>> findJudgingMethod() {
        RestResponse<Map<Integer, String>> restResp = new RestResponse<>();
        restResp.setData(qcConfigService.findAllJudgingMethod());
        restResp.setMsg("获取成功");
        return restResp;
    }

    /**
     * 查询所有的替代物
     *
     * @return 查询结果
     */
    @ApiOperation(value = "查询所有的替代物", notes = "查询所有的替代物")
    @GetMapping("/Substitute")
    public RestResponse<List<DtoSubstitute>> findSubstitute() {
        RestResponse<List<DtoSubstitute>> response = new RestResponse<>();
        response.setData(qcConfigService.findSubstitute());
        response.setMsg("获取成功");
        return response;
    }

    /**
     * 查询所有的替代物
     *
     * @return 查询结果
     */
    @ApiOperation(value = "查询所有的替代物", notes = "查询所有的替代物")
    @GetMapping("/qcTemp/{testId}")
    public RestResponse<DtoQCTemp> findSubstitute(@PathVariable(name = "testId") String testId) {
        RestResponse<DtoQCTemp> response = new RestResponse<>();
        response.setData(qcConfigService.findQCTemp(testId));
        response.setMsg("获取成功");
        return response;
    }

    /**
     * 保存质控限值数据
     *
     * @param testQCRangeCopy 质控限值数据
     * @param qcType          质控类型
     * @return 已保存的质控限值数据
     */
    @ApiOperation(value = "保存质控限值数据", notes = "保存质控限值数据")
    @PostMapping
    public RestResponse<DtoQualityControlLimit> save(@Validated @RequestBody DtoQualityControlLimit testQCRangeCopy, Integer qcType) {
        RestResponse<DtoQualityControlLimit> response = new RestResponse<>();
        response.setData(qcConfigService.save(testQCRangeCopy, qcType));
        response.setMsg("保存成功");
        return response;
    }

    /**
     * 新增偏差公式
     *
     * @param qualityControlDeviation 实体对象
     * @return 新增的偏差公式
     */
    @ApiOperation(value = "新增偏差公式", notes = "新增偏差公式")
    @PostMapping("/deviation")
    public RestResponse<DtoQualityControlDeviation> saveDeviation(@RequestBody DtoQualityControlDeviation qualityControlDeviation) {
        RestResponse<DtoQualityControlDeviation> response = new RestResponse<>();
        response.setData(qcConfigService.saveDeviation(qualityControlDeviation));
        response.setMsg("保存成功");
        return response;
    }

    /**
     * 修改偏差公式
     *
     * @param qualityControlDeviation 实体对象
     * @return 修改的偏差公式
     */
    @ApiOperation(value = "修改偏差公式", notes = "修改偏差公式")
    @PutMapping("/deviation")
    public RestResponse<DtoQualityControlDeviation> updateDeviation(@RequestBody DtoQualityControlDeviation qualityControlDeviation) {
        RestResponse<DtoQualityControlDeviation> response = new RestResponse<>();
        response.setData(qcConfigService.updateDeviation(qualityControlDeviation));
        response.setMsg("修改成功");
        return response;
    }

    /**
     * 批量删除偏差公式
     *
     * @param ids 删除的id
     * @return 删除数量
     */
    @ApiOperation(value = "批量删除偏差公式", notes = "批量删除偏差公式")
    @DeleteMapping("/deviation")
    public RestResponse<Integer> deleteDeviation(@RequestBody List<String> ids) {
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(qcConfigService.deleteDeviation(ids));
        response.setMsg("删除成功");
        return response;
    }

    /**
     * 查询偏差公式
     *
     * @return 已保存的偏差公式
     */
    @ApiOperation(value = "查询偏差公式", notes = "查询偏差公式")
    @GetMapping("/deviation")
    public RestResponse<List<DtoQualityControlDeviation>> queryDeviation() {
        RestResponse<List<DtoQualityControlDeviation>> response = new RestResponse<>();
        response.setData(qcConfigService.queryDeviation());
        return response;
    }

    /**
     * 获取偏差公式设置质控类型下拉框数据源
     *
     * @return 质控类型下拉框数据源
     */
    @ApiOperation(value = "获取偏差公式设置质控类型下拉框数据源", notes = "获取偏差公式设置质控类型下拉框数据源")
    @GetMapping("/deviationFormulaSrc")
    public RestResponse<List<DeviationFormulaSrcVO>> queryDeviationFormulaSrc(String addFlag) {
        RestResponse<List<DeviationFormulaSrcVO>> response = new RestResponse<>();
        List<DeviationFormulaSrcVO> deviationFormulaSrcVOList = qcConfigService.getDeviationFormulaQcTypeDataSource();
        //新增偏差公式时，类型下拉框数据源中，“默认”去掉
        if ("1".equals(addFlag)) {
            deviationFormulaSrcVOList = deviationFormulaSrcVOList.stream().filter(p -> !"默认".equals(p.getQualityControlTypeName())).collect(Collectors.toList());
        }
        response.setData(deviationFormulaSrcVOList);
        return response;
    }

    /**
     * 修改质控限值数据
     *
     * @param testQCRangeCopy 质控限值数据
     * @param qcType          质控类型
     * @return 已保存的质控限值数据
     */
    @ApiOperation(value = "修改质控限值数据", notes = "保存质控限值数据")
    @PutMapping
    public RestResponse<DtoQualityControlLimit> update(@Validated @RequestBody DtoQualityControlLimit testQCRangeCopy, Integer qcType) {
        RestResponse<DtoQualityControlLimit> response = new RestResponse<>();
        response.setData(qcConfigService.save(testQCRangeCopy, qcType));
        response.setMsg("保存成功");
        return response;
    }


    /**
     * 复制限制配置
     *
     * @param qualityControlLimit 复制限制配置
     * @return 结果
     */
    @ApiOperation(value = "复制限制配置", notes = "复制限制配置")
    @PostMapping("/copy")
    public RestResponse<String> copy(@Validated @RequestBody DtoQualityControlLimit qualityControlLimit) {
        RestResponse<String> restResponse = new RestResponse<>();
        qcConfigService.copy(qualityControlLimit.getTestId(), qualityControlLimit.getTestIdList(), qualityControlLimit.getQcList());
        restResponse.setMsg("操作成功！");

        return restResponse;
    }

    /**
     * 批量删除
     *
     * @param ids 删除的id
     * @return 删除数量
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody List<String> ids) {
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(qcConfigService.delete(ids));
        response.setMsg("删除成功");
        return response;
    }

    /**
     * 批量清除测试项目对应的质控限值列表
     *
     * @param testIdList 测试项目id列表
     * @return 删除数量
     */
    @ApiOperation(value = "批量清除测试项目对应的质控限值列表", notes = "批量清除测试项目对应的质控限值列表")
    @PostMapping("/clear")
    public RestResponse<Integer> clearLimit(@RequestBody List<String> testIdList) {
        RestResponse<Integer> response = new RestResponse<>();
        qcConfigService.clearQcLimit(testIdList);
        response.setMsg("删除成功");
        return response;
    }

    /**
     * 配置设置是否默认
     *
     * @param dispositionId 配置Id
     * @return 是否成功
     */
    @ApiOperation(value = "配置设置是否默认", notes = "配置设置是否默认")
    @PostMapping("/defaultFormula/{dispositionId}")
    public RestResponse<Boolean> updateDefaultValue(@PathVariable(name = "dispositionId") String dispositionId) {
        RestResponse<Boolean> response = new RestResponse<>();
        qcConfigService.updateDefaultValue(dispositionId);
        response.setData(Boolean.TRUE);
        response.setMsg("设置成功");
        return response;
    }

    /**
     * 更新配置公式id
     *
     * @return 是否成功
     */
    @ApiOperation(value = "更新配置公式id", notes = "更新配置公式id")
    @PostMapping("/uploadOldLimit")
    public RestResponse<Boolean> uploadOldLimit() {
        RestResponse<Boolean> response = new RestResponse<>();
        qcConfigService.uploadOldLimit();
        response.setData(Boolean.TRUE);
        response.setMsg("配置成功");
        return response;
    }

    @Autowired
    @Lazy
    public void setQcConfigService(QCConfigService qcConfigService) {
        this.qcConfigService = qcConfigService;
    }

}
