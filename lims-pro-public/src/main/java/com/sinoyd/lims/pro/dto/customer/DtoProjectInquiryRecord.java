package com.sinoyd.lims.pro.dto.customer;

import com.jsoniter.JsonIterator;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;

import java.util.*;

/**
 * 项目进度送样单情况
 * <AUTHOR>
 * @version V1.0.0 2020/2/7
 * @since V100R001
 */
@Data
public class DtoProjectInquiryRecord {

    /**
     * 送样单id
     */
    private String id;

    /**
     * 样品个数
     */
    private Integer sampleNum = 0;

    /**
     * 送样单号
     */
    private String recordCode;

    /**
     * 送样人id
     */
    private String senderId;

    /**
     * 送样人
     */
    private String senderName;

    /**
     * 送样时间
     */
    private Date sendTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 检测类型id
     */
    private String sampleTypeIds = "";

    /**
     * 检测类型名称
     */
    private String sampleTypeNames = "";

    /**
     * 领样单状态
     */
    private Integer subStatus;

    /**
     * 模块状态
     */
    private Integer moduleStatus;

    public DtoProjectInquiryRecord(){

    }

    public DtoProjectInquiryRecord(String id,String recordCode,String senderId,String senderName,Date sendTime,String json,Integer subStatus,Integer moduleStatus){
        this.id = id;
        this.recordCode = recordCode;
        this.senderId = senderId;
        this.senderName = senderName;
        this.sendTime = sendTime;
        this.subStatus = subStatus;
        this.moduleStatus = moduleStatus;
        try{
            if(StringUtils.isNotNullAndEmpty(json)){
                Map<String, Object> jsonMap = JsonIterator.deserialize(json, Map.class);
                this.sampleNum = (Integer) jsonMap.get("sampleNum");
                this.sampleTypeIds = (String) jsonMap.get("sampleTypeIds");
            }
        }
        catch (BaseException ex){

        }


    }

}
