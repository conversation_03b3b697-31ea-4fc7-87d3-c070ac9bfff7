package com.sinoyd.base.controller;

import java.util.List;

import com.sinoyd.base.criteria.ConsumableOfMixedCriteria;
import com.sinoyd.base.dto.lims.DtoConsumableOfMixed;
import com.sinoyd.base.service.ConsumableOfMixedService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 混标信息
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Api(tags = "混标信息: 混标信息服务")
@RestController
@RequestMapping("/api/base/consumableOfMixed")
@Validated
public class ConsumableOfMixedController extends BaseJpaController<DtoConsumableOfMixed, String, ConsumableOfMixedService> {

    /**
     * 根据id获取混标信息
     *
     * @param id 混标信息id
     * @return RestResponse<DtoConsumableOfMixed>
     */
    @ApiOperation(value = "按id查询混标信息", notes = "按id查询混标信息")
    @GetMapping("/{id}")
    public RestResponse<DtoConsumableOfMixed> find(@PathVariable(name = "id") String id)
    {
        RestResponse<DtoConsumableOfMixed> restResp = new RestResponse<>();
        DtoConsumableOfMixed mixed =service.findOne(id);
        restResp.setData(mixed);
        restResp.setRestStatus(StringUtil.isNull(mixed) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    /**
     * 分页查询混标信息
     * @param criteria  混标信息查询条件
     * @return RestResponse<List<DtoConsumableOfMixed>>
     */
    @ApiOperation(value = "分页查询混标信息", notes = "分页查询混标信息")
    @GetMapping
    public RestResponse<List<DtoConsumableOfMixed>> findByPage(ConsumableOfMixedCriteria criteria)
    {
        RestResponse<List<DtoConsumableOfMixed>> restResp = new RestResponse<>();
        PageBean<DtoConsumableOfMixed> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增混标信息
     * @param entity 混标实体
     * @return RestResponse<DtoConsumableOfMixed>
     */
    @ApiOperation(value = "新增混标信息", notes = "新增混标信息")
    @PostMapping
    public RestResponse<DtoConsumableOfMixed> save(@Validated @RequestBody DtoConsumableOfMixed entity)
    {
        RestResponse<DtoConsumableOfMixed> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoConsumableOfMixed data = service.save(entity);
        restResp.setData(data);
        return restResp;
    }
    /**
     * 更新混标信息
     * @param entity 混标实体
     * @return RestResponse<DtoConsumableOfMixed>
     */
    @ApiOperation(value = "更新混标信息", notes = "更新混标信息")
    @PutMapping
    public RestResponse<DtoConsumableOfMixed> update(@Validated @RequestBody DtoConsumableOfMixed entity)
    {
        RestResponse<DtoConsumableOfMixed> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoConsumableOfMixed data = service.update(entity);
        restResp.setData(data);
        return restResp;
    }

    /**
     * 删除混标信息
     * @param id 混标信息id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除混标信息", notes = "根据id删除混标信息")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@RequestBody String id)
    {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 根据id批量删除混标信息
     * @param ids 混标信息id集合
     * @return 返回删除的记录数
     */
    @ApiOperation(value = "根据id批量删除混标信息", notes = "根据id批量删除混标信息")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids)
    {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}