package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.CarConsumerRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoCarConsumerRecord;
import com.sinoyd.lims.lim.service.CarConsumerRecordService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 车辆消费记录接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019/05/10
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/carConsumerRecord")
@Validated
public class CarConsumerRecordController extends BaseJpaController<DtoCarConsumerRecord, String, CarConsumerRecordService>{

    /**
     * 根据id获取车辆消费记录
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取车辆消费记录", notes = "根据id获取车辆消费记录")
    @GetMapping("/{id}")
    public RestResponse<DtoCarConsumerRecord> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoCarConsumerRecord> restResp = new RestResponse<>();
        DtoCarConsumerRecord contract = service.findOne(id);
        restResp.setData(contract);

        restResp.setRestStatus(StringUtil.isNull(contract) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询车辆消费记录
     * 
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询车辆消费记录", notes = "分页动态条件查询车辆消费记录")
    @GetMapping
    public RestResponse<List<DtoCarConsumerRecord>> findByPage(CarConsumerRecordCriteria criteria) {

        RestResponse<List<DtoCarConsumerRecord>> restResp = new RestResponse<>();

        PageBean<DtoCarConsumerRecord> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增车辆消费记录
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增车辆消费记录", notes = "新增车辆消费记录")
    @PostMapping
    public RestResponse<DtoCarConsumerRecord> save(@Validated @RequestBody DtoCarConsumerRecord entity) {

        RestResponse<DtoCarConsumerRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoCarConsumerRecord data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改车辆消费记录
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改车辆消费记录", notes = "修改车辆消费记录")
    @PutMapping
    public RestResponse<DtoCarConsumerRecord> update(@Validated @RequestBody DtoCarConsumerRecord entity) {

        RestResponse<DtoCarConsumerRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoCarConsumerRecord data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 刪除车辆消费记录
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "刪除车辆消费记录", notes = "刪除车辆消费记录")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除车辆消费记录
     * 
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除车辆消费记录", notes = "批量删除车辆消费记录")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}