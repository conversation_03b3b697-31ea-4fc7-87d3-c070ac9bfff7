package com.sinoyd.lims.lim.data.sync.service.impl;

import com.sinoyd.base.criteria.EvaluationCriteriaCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.criteria.RecordConfigCriteria;
import com.sinoyd.lims.lim.criteria.ReportConfigCriteria;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataSyncParam;
import com.sinoyd.lims.lim.data.sync.service.DataSyncService;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据同步实现类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/21
 */
@Service
@Slf4j
public class DataSyncServiceImpl implements DataSyncService {

    private WebSocketServer webSocketServer;

    private List<AbsDataSync<?>> dataSyncList;

    /**
     * 数据比较
     *
     * @param dtoDataSyncParam 参数
     * @return 比较结果
     */
    @Override
    public Map<String, List<DtoDataCompareResult<?>>> compare(DtoDataSyncParam dtoDataSyncParam) {
        List<DtoDataCompareResult<?>> compareResultList = new ArrayList<>();
        Optional<AbsDataSync<?>> optionalAbsDataSync = dataSyncList.parallelStream().filter(p -> p.getSyncDataType().equals(dtoDataSyncParam.getDataType()))
                .findFirst();
        optionalAbsDataSync.ifPresent(absDataSync -> {
            List<Integer> syncDataTypeList = absDataSync.getDependDataType();
            Map<Integer, List<String>> childItemMap = absDataSync.getChildItemMap(dtoDataSyncParam.getDataIds());
            dataSyncList.forEach(dataSync -> {
                if (syncDataTypeList.contains(dataSync.getSyncDataType())) {
                    List<String> dataIds = dtoDataSyncParam.getDataIds();
                    if(StringUtil.isNotEmpty(childItemMap) && childItemMap.containsKey(dataSync.getSyncDataType())){
                        dataIds = childItemMap.get(dataSync.getSyncDataType());
                    }
                    compareResultList.addAll(dataSync.compareData(dataIds));
                }
            });
        });
        compareResultList.sort(Comparator.comparing(DtoDataCompareResult::getOrderNum));
        return compareResultList.stream().collect(Collectors.groupingBy(DtoDataCompareResult::getCategory));
    }

    /**
     * 分页远程查询共同库测试项目
     *
     * @param page     分页对象
     * @param criteria 条件对象
     */
    @Override
    public void findStandardTestByPage(PageBean<DtoTest> page, TestCriteria criteria) {
        Optional<AbsDataSync<?>> absDataSyncOptional = dataSyncList.parallelStream()
                .filter(p -> EnumLIM.EnumDataSyncType.测试项目.getValue().equals(p.getSyncDataType())).findFirst();
        parseFindByPageResult(absDataSyncOptional, page, criteria);
    }


    /**
     * 分页远程查询共同库测试项目
     *
     * @param page     分页对象
     * @param criteria 条件对象
     */
    @Override
    public void findStandardEvaluationCriteriaByPage(PageBean<DtoEvaluationCriteria> page, EvaluationCriteriaCriteria criteria) {
        Optional<AbsDataSync<?>> absDataSyncOptional = dataSyncList.parallelStream()
                .filter(p -> EnumLIM.EnumDataSyncType.评价标准.getValue().equals(p.getSyncDataType())).findFirst();
        parseFindByPageResult(absDataSyncOptional,page,criteria);
    }

    /**
     * 分页远程查询共同库报表模板配置
     * @param page     分页对象
     * @param criteria 条件对象
     */
    @Override
    public void findStandardReportConfigByPage(PageBean<DtoReportConfig> page, ReportConfigCriteria criteria) {
        Optional<AbsDataSync<?>> absDataSyncOptional = dataSyncList.parallelStream()
                .filter(p -> EnumLIM.EnumDataSyncType.报表配置.getValue().equals(p.getSyncDataType())).findFirst();
        parseFindByPageResult(absDataSyncOptional, page, criteria);
    }


    @Override
    public void findStandardRecordConfigByPage(PageBean<DtoRecordConfig> page, RecordConfigCriteria criteria) {
        Optional<AbsDataSync<?>> absDataSyncOptional = dataSyncList.parallelStream()
                .filter(p -> EnumLIM.EnumDataSyncType.采样单配置.getValue().equals(p.getSyncDataType())).findFirst();
        parseFindByPageResult(absDataSyncOptional, page, criteria);
    }

    @Override
    public void findStandardWorksheetRecordConfigByPage(PageBean<DtoRecordConfig> page, RecordConfigCriteria criteria) {
        Optional<AbsDataSync<?>> absDataSyncOptional = dataSyncList.parallelStream()
                .filter(p -> EnumLIM.EnumDataSyncType.原始记录单配置.getValue().equals(p.getSyncDataType())).findFirst();
        parseFindByPageResult(absDataSyncOptional, page, criteria);
    }


    /**
     * 数据同步
     *
     * @param dtoDataSyncParam 同步参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(DtoDataSyncParam dtoDataSyncParam) {
        webSocketServer.sendMessage("开始同步数据...");
        Optional<AbsDataSync<?>> optionalAbsDataSync = dataSyncList.parallelStream().filter(p -> p.getSyncDataType().equals(dtoDataSyncParam.getDataType()))
                .findFirst();
        optionalAbsDataSync.ifPresent(absDataSync -> {
            List<Integer> syncDataTypeList = absDataSync.getDependDataType();
            Map<Integer, List<String>> childItemMap = absDataSync.getChildItemMap(dtoDataSyncParam.getDataIds());
            dataSyncList.forEach(dataSync -> {
                if (syncDataTypeList.contains(dataSync.getSyncDataType())) {
                    List<String> dataIds = dtoDataSyncParam.getDataIds();
                    if(StringUtil.isNotEmpty(childItemMap) && childItemMap.containsKey(dataSync.getSyncDataType())){
                        dataIds = childItemMap.get(dataSync.getSyncDataType());
                    }
                    dataSync.syncData(dataIds, webSocketServer);
                }
            });
        });
        webSocketServer.sendMessage("同步数据完成...");
    }

    /**
     * 解析分页查询公共库结果
     *
     * @param optional 数据同步类
     * @param page 分页实体
     * @param criteria 查询条件
     * @param <T> 实体类型
     */
    private <T> void parseFindByPageResult(Optional<AbsDataSync<?>> optional, PageBean<T> page, BaseCriteria criteria){
        optional.ifPresent(dataSync -> {
            ResponseEntity<JSONObject> queryResponse = dataSync.queryStandardData(criteria, page.getPageNo(), page.getRowsPerPage(), page.getSort());
            JSONObject jsonObject = queryResponse.getBody();
            int count = (int) jsonObject.get("count");
            List<Map<String, Object>> dataMapList = (List<Map<String, Object>>) jsonObject.get("data");
            List<T> dataList = (List<T>) dataSync.convertObject(dataMapList);
            page.setData(dataList);
            page.setRowsCount(count);
        });
    }

    @Autowired
    @Lazy
    public void setDataSyncList(List<AbsDataSync<?>> dataSyncList) {
        this.dataSyncList = dataSyncList;
    }

    @Autowired
    @Lazy
    public void setWebSocketServer(WebSocketServer webSocketServer) {
        this.webSocketServer = webSocketServer;
    }
}