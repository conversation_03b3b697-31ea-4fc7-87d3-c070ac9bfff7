package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentMaintainRecord;

import java.util.List;

/**
 * 仪器维护配置
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface InstrumentMaintainRecordService extends IBaseJpaService<DtoInstrumentMaintainRecord, String> {

    /***
     * 获取维护单位
     * @return
     */
    List<DtoInstrumentMaintainRecord> getMaintainDeptList();
}