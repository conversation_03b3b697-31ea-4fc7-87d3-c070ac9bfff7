package com.sinoyd.base.dto.rcc;

import com.sinoyd.base.entity.QualityControlLimit;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;
import java.util.Map;

/**
 * DtoTestQCRangeCopoy实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/6/14
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_QualityControlLimit")
@Data
@DynamicInsert
public class DtoQualityControlLimit extends QualityControlLimit {

    /**
     * 测试项目Id集合
     */
    @Transient
    private List<String> testIdList;


    /**
     * 质控类型集合
     */
    @Transient
    private Map<Integer, Integer> qcTypeMap;

    /**
     * 质控类型集合
     */
    @Transient
    private List<DtoQualityControlLimit> qcList;

    /**
     * 排序值
     */
    @Transient
    private Integer orderNumValue = 0;

    /**
     * 相对偏差公式
     */
    @Transient
    private String qcRangeFormula;

    @Transient
    private String judgeDataType;

    /**
     * 是否默认限值
     */
    @Transient
    private Boolean isDefault = false;

    /**
     * 实际数值范围
     */
    @Transient
    private String rangeConfigData;

    /**
     * 实际允许限制
     */
    @Transient
    private String allowLimitData;
}
