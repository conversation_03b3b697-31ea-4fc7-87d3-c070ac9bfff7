package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig;

import java.util.List;


/**
 * TestQCRemindConfig数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
public interface TestQCRemindConfigRepository extends IBaseJpaPhysicalDeleteRepository<DtoTestQCRemindConfig, String> {
    /**
     * 返回默认的比例配置
     *
     * @return 返回默认的比例配置
     */
    List<DtoTestQCRemindConfig> findByIsDefaultTrue();


    /**
     * 根据测试项目id，质控类型，质控等级进行获取
     *
     * @param qcType  质控类型
     * @param qcGrade 质控等级
     * @return 返回相应的配置信息
     */
    List<DtoTestQCRemindConfig> findByQcTypeAndQcGrade(Integer qcType, Integer qcGrade);
}