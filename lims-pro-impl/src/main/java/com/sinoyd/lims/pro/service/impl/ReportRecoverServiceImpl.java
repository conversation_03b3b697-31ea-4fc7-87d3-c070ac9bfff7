package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.lim.service.SerialIdentifierConfigService;
import com.sinoyd.lims.pro.criteria.ReportRecoverCriteria;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoReportRecover;
import com.sinoyd.lims.pro.dto.DtoReportRecover2Report;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.repository.ReportRecover2ReportRepository;
import com.sinoyd.lims.pro.repository.ReportRecoverRepository;
import com.sinoyd.lims.pro.repository.ReportRepository;
import com.sinoyd.lims.pro.service.NewLogService;
import com.sinoyd.lims.pro.service.ReportRecover2ReportService;
import com.sinoyd.lims.pro.service.ReportRecoverService;
import com.sinoyd.lims.pro.service.ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 报告发放回收接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
@Service
public class ReportRecoverServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportRecover, String, ReportRecoverRepository> implements ReportRecoverService {

    @Autowired
    private ReportRecover2ReportRepository toRecoverRepository;

    @Autowired
    private ReportRecover2ReportService toRecoverService;

    @Autowired
    private ReportService reportService;

    @Autowired
    private ReportRepository reportRepository;

    @Autowired
    private NewLogService newLogService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    @Lazy
    private SerialIdentifierConfigService serialIdentifierConfigService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;


    @Override
    public void findByPage(PageBean<DtoReportRecover> pb, BaseCriteria reportrecoverCriteria) {
        ReportRecoverCriteria criteria = (ReportRecoverCriteria)reportrecoverCriteria;
        pb.setEntityName("DtoReportRecover rc,DtoReportRecover2Report r2e,DtoReport rp,DtoProject p");
        pb.setSelect("select rc");
        super.findByPage(pb, criteria);
        List<DtoReportRecover> list = pb.getData();
        if(!list.isEmpty()){
            List<String> rcoverIds = list.stream().map(DtoReportRecover::getId).collect(Collectors.toList());
            List<DtoReportRecover2Report> reportRecover2ReportList = toRecoverRepository.findByRecoverIdIn(rcoverIds);
            List<String> reportIds = reportRecover2ReportList.stream() .map(DtoReportRecover2Report::getReportId).collect(Collectors.toList());
            List<DtoReport> reportList = reportIds.isEmpty()?new ArrayList<>():reportRepository.findAll(reportIds);
            List<String> projectIds = list.stream().map(DtoReportRecover::getProjectId).collect(Collectors.toList());
            List<DtoProject> projectList = projectRepository.findAll(projectIds);
            Map<String, String> reportTypeMap =  serialIdentifierConfigService.findListByConfigType(EnumLIM.EnumIdentifierConfig.报告编号.getValue())
                    .stream().collect(Collectors.toMap(DtoSerialIdentifierConfig::getId, DtoSerialIdentifierConfig::getConfigName));
            List<String> projectTypeIds = projectList.stream().map(DtoProject::getProjectTypeId).collect(Collectors.toList());
            List<DtoProjectType> projectTypes = projectTypeService.findRedisByIds(projectTypeIds);
            for (DtoReportRecover recover :list) {
                //报告相关属性
                reportRecover2ReportList.stream().filter(r2r->recover.getId().equals(r2r.getRecoverId())).findFirst()
                        .ifPresent(r2r->{
                            reportList.stream().filter(r->r2r.getReportId().equals(r.getId())).findFirst().ifPresent(r->{
                                recover.setReportId(r.getId());
                                recover.setCode(r.getCode());
                                recover.setStatus(r.getStatus());
                                recover.setReportYear(r.getReportYear());
                                if (reportTypeMap.containsKey(r.getReportTypeId())) {
                                    recover.setReportTypeName(reportTypeMap.get(r.getReportTypeId()));
                                }
                            });
                        });
                //项目属性
                projectList.stream().filter(p->recover.getProjectId().equals(p.getId())).findFirst()
                        .ifPresent(p->{
                            recover.setProjectName(p.getProjectName());
                            recover.setProjectCode(p.getProjectCode());
                            recover.setInspectedEnt(p.getInspectedEnt());
                            recover.setCustomerName(p.getCustomerName());
                            projectTypes.stream().filter(t->t.getId().equals(p.getProjectTypeId())).findFirst()
                                    .ifPresent(t->recover.setProjectTypeName(t.getName()));
                        });
            }
        }
    }


    /**
     * 保存回收数据
     *
     * @param entities 需要保存的数据
     * @return 已保存的数据
     */
    @Override
    @Transactional
    public List<DtoReportRecover> save(Collection<DtoReportRecover> entities) {
        List<DtoReportRecover> recovers = new ArrayList<>();
        //添加的回收数据中所有关联报告id
        List<String> reportIdsAll = new ArrayList<>();
        //回收信息Id
        List<String> recoverIds = new ArrayList<>();
        List<String> notRecoverReportCodes = new ArrayList<>();
        List<String> isRecoverReportCodes = new ArrayList<>();
        //需要更新的集合
        List<DtoReportRecover> updateList = new ArrayList<>();
        //需要新增的集合
        List<DtoReportRecover> insertList = new ArrayList<>();
        //关联报告集合
        List<DtoReportRecover2Report> updateListOfToReport = new ArrayList<>();
        for (DtoReportRecover entity : entities) {
            List<String> reportIdByEntity = entity.getReportRecover2ReportList().stream().map(DtoReportRecover2Report::getReportId).collect(Collectors.toList());
            if (repository.findOne(entity.getId())!=null) {
                updateList.add(entity);
                recoverIds.add(entity.getId());

            } else {
                //判断关联报告是否已经回收
                List<DtoReport> reportsNotRecover = reportService.findAll(reportIdByEntity).stream().filter(p -> p.getGrantStatus() == 1).collect(Collectors.toList());
                List<DtoReport> reportsIsRecover = reportService.findAll(reportIdByEntity).stream().filter(p -> p.getGrantStatus() == 3).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(reportsNotRecover)) {
                    notRecoverReportCodes.addAll(reportsNotRecover.stream().map(DtoReport::getCode).collect(Collectors.toList()));
                }
                if (StringUtil.isNotEmpty(reportsIsRecover)) {
                    isRecoverReportCodes.addAll(reportsIsRecover.stream().map(DtoReport::getCode).collect(Collectors.toList()));
                }
                entity.setId(UUIDHelper.NewID());
                insertList.add(entity);
            }
            for (DtoReportRecover2Report recover2Report : entity.getReportRecover2ReportList()) {
                reportIdsAll.add(recover2Report.getReportId());
                recover2Report.setRecoverId(entity.getId());
                updateListOfToReport.add(recover2Report);
            }
            recovers.add(entity);
        }

        if (StringUtil.isNotEmpty(isRecoverReportCodes)) {
            throw new BaseException("报告" + isRecoverReportCodes + "已回收，请确认后再操作！");
        }
        if (StringUtil.isNotEmpty(notRecoverReportCodes)) {
            throw new BaseException("报告" + notRecoverReportCodes + "未发放，请确认后再操作！");
        }

        //判断是否为修改
        if (StringUtil.isNotEmpty(updateList)) {
            super.update(updateList);
            //修改报告状态
            List<String> oldReportIds = toRecoverRepository.findByRecoverIdIn(recoverIds).stream().map(DtoReportRecover2Report::getReportId).collect(Collectors.toList());
            List<String> newReportIds = updateListOfToReport.stream().map(DtoReportRecover2Report::getReportId).collect(Collectors.toList());
            List<String> updateReportIds = oldReportIds.stream().filter(p -> !newReportIds.contains(p)).collect(Collectors.toList());
            //修改更新的报告状态
            if (StringUtil.isNotEmpty(updateReportIds)) {
                newLogService.createReportGrantInfoLog(updateReportIds,"删除回收");
                reportRepository.updateGrantStatus(updateReportIds, 2, PrincipalContextUser.getPrincipal().getUserId(), new Date());
            }
            if (!compareList(oldReportIds,newReportIds)){
                newLogService.createReportGrantInfoLog(reportIdsAll,"新增回收");
                reportRepository.updateGrantStatus(reportIdsAll, 3, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                toRecoverRepository.deleteByRecoverIdIn(recoverIds);
                toRecoverService.save(updateListOfToReport);
            }

        }
        if (StringUtil.isNotEmpty(insertList)) {
            newLogService.createReportGrantInfoLog(reportIdsAll,"新增回收");
            reportRepository.updateGrantStatus(reportIdsAll, 3, PrincipalContextUser.getPrincipal().getUserId(), new Date());
            super.save(insertList);
            toRecoverRepository.save(updateListOfToReport);
        }

        return recovers;
    }

    /**
     * 比较两个String集合是否相同
     * @param list1 集合1
     * @param list2 集合2
     * @return 是否相同
     */
    private Boolean compareList(List<String> list1 ,List<String> list2){
        if (list1.size() != list2.size()){
            return false;
        }
        Collections.sort(list1);
        Collections.sort(list2);
        for (int i = 0; i < list1.size(); i++) {
            if (!list1.get(i).equals(list2.get(i))){
                return false;
            }
        }
        return true;
    }

    /**
     * 获取项目关联回收信息
     *
     * @param projectId 项目Id
     * @param status    状态
     * @return 关联回收信息
     */
    @Override
    public List<DtoReportRecover> findRecover(String projectId, String status) {
        Integer grantStatusNum = 1;
        if (StringUtil.isEmpty(status)) {
            grantStatusNum = 3;
        }
        if ("已发放".contains(status)) {
            grantStatusNum = 2;
        }
        if ("已回收".contains(status)) {
            grantStatusNum = 3;
        }
        Map<String, Object> values = new HashMap<>();
        StringBuilder builder = new StringBuilder("select s");
        builder.append(" from DtoReport s where 1=1 and s.isDeleted = 0 ");
        builder.append(" and s.projectId = :projectId");
        builder.append(" and s.grantStatus = :grantStatus");
        values.put("projectId", projectId);
        values.put("grantStatus", grantStatusNum);
        List<DtoReport> reports = comRepository.find(builder.toString(), values);
        List<String> ids = reports.stream().map(DtoReport::getId).collect(Collectors.toList());
        List<DtoReportRecover> dtoReportRecover = repository.findByProjectId(projectId);
        if (StringUtil.isNotEmpty(dtoReportRecover)) {
            for (DtoReportRecover recover : dtoReportRecover) {
                List<DtoReportRecover2Report> byRecoverIdAndReportIdIn = toRecoverRepository.findByRecoverIdAndReportIdIn(recover.getId(), ids);
                recover.setReportRecover2ReportList(byRecoverIdAndReportIdIn);
            }
        }

        return dtoReportRecover;
    }

    /**
     * 获取状态为已发放的关联报告
     *
     * @param projectId 项目id
     * @return 关联报告
     */
    @Override
    public List<DtoReport> findReport(String projectId) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder builder = new StringBuilder("select s");
        builder.append(" from DtoReport s where 1=1 and s.isDeleted = 0 ");
        builder.append(" and s.projectId = :projectId");
        builder.append(" and (s.grantStatus = 2 or s.grantStatus = 3)");
        values.put("projectId", projectId);
        List<DtoReport> reports = comRepository.find(builder.toString(), values);
        return reports;
    }

    /**
     * 删除
     *
     * @param id  需要删除的回收信息id
     * @param <K> 类型
     * @return 删除的条数
     */
    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        List<String> idList = new ArrayList<>();
        idList.add(idStr);
        //删除快递报告关联信息
        List<DtoReportRecover2Report> list = toRecoverRepository.findByRecoverIdIn(idList);
        List<String> reportIds = list.stream().map(DtoReportRecover2Report::getReportId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(reportIds)) {
            List<DtoReport> updateReports = reportService.findAll(reportIds);
            updateReports.forEach(p -> p.setGrantStatus(2));
            reportService.save(updateReports);
        }
        newLogService.createReportGrantInfoLog(reportIds,"删除回收");
        toRecoverRepository.deleteByRecoverIdIn(idList);
        //删除快递信息
        return super.logicDeleteById(idStr);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<DtoReportRecover2Report> list = toRecoverRepository.findByRecoverIdIn((List<String>) ids);
        List<String> reportIds = list.stream().map(DtoReportRecover2Report::getReportId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(reportIds)) {
            List<DtoReport> updateReports = reportService.findAll(reportIds);
            updateReports.forEach(p -> p.setGrantStatus(2));
            reportService.save(updateReports);
        }
        newLogService.createReportGrantInfoLog(reportIds,"删除回收");
        toRecoverRepository.deleteByRecoverIdIn((List<String>) ids);
        return super.logicDeleteById(ids);
    }
}