package com.sinoyd.base.controller;

import com.sinoyd.base.criteria.SampleTypeCriteria;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 检测类型/模板
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
@Api(tags = "检测类型/模板管理: 检测类型/模板管理服务")
@RestController
@RequestMapping("/api/base/sampleType")
@Validated
public class SampleTypeController extends BaseJpaController<DtoSampleType, String, SampleTypeService> {

    /**
     * 通过小类id获取大类
     *
     * @param id 主键id
     * @return 返回相应的大类信息
     */
    @ApiOperation(value = "通过小类id获取大类", notes = "通过小类id获取大类")
    @GetMapping("/bigSampleType/{id}")
    public RestResponse<DtoSampleType> getBigSampleType(@PathVariable(name = "id") String id) {
        RestResponse<DtoSampleType> restResponse = new RestResponse<>();
        DtoSampleType sampleType = service.getBigSampleType(id);
        restResponse.setData(sampleType);

        return restResponse;
    }

    /**
     * 获取单个检测类型
     *
     * @param id 主键id
     * @return 返回检测类型
     */
    @ApiOperation(value = "根据id获取检测类型", notes = "根据id获取检测类型")
    @GetMapping("/{id}")
    public RestResponse<DtoSampleType> find(@PathVariable("id") String id) {
        RestResponse<DtoSampleType> restResponse = new RestResponse<>();
        DtoSampleType sampleType = service.findOne(id);
        restResponse.setData(sampleType);
        restResponse.setRestStatus(StringUtil.isNull(sampleType) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResponse;
    }

    /**
     * 分页列表
     * industryTypeId:行业类型id：全部是0
     * sampleTypeId:传入的样品类型id是大类id时，表格显示大类下的检测模板和大类下小类下的所有检测模板
     * typeName:模板名称模糊检索
     * category:类别，0：大类和小类，1：大类，2：小类，3：模板
     * parentIds:样品类型大类idList
     * parentId:样品类型大类id
     */
    @ApiOperation(value = "分页获取检测类型", notes = "分页获取检测类型")
    @GetMapping("")
    public RestResponse<List<DtoSampleType>> findByPage(SampleTypeCriteria sampleTypeCriteria) {
        RestResponse<List<DtoSampleType>> restResponse = new RestResponse<>();
        PageBean<DtoSampleType> page = super.getPageBean();
        service.findByPage(page, sampleTypeCriteria);

        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(page.getRowsCount());
        restResponse.setData(page.getData());

        return restResponse;

    }

    /**
     * 新增
     *
     * @param sampleType 检测类型
     * @return 返回新增值
     */
    @ApiOperation(value = "新增检测类型", notes = "新增检测类型")
    @PostMapping("")
    public RestResponse<DtoSampleType> create(@Validated @RequestBody DtoSampleType sampleType) {
        RestResponse<DtoSampleType> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoSampleType data = service.save(sampleType);
        restResponse.setData(data);

        return restResponse;
    }

    /**
     * 更新
     *
     * @param sampleType 检测类型
     * @return 返回更新值
     */
    @ApiOperation(value = "更新检测类型", notes = "更新检测类型")
    @PutMapping("")
    public RestResponse<DtoSampleType> update(@Validated @RequestBody DtoSampleType sampleType) {
        RestResponse<DtoSampleType> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoSampleType data = service.update(sampleType);
        restResponse.setData(data);

        return restResponse;
    }

    /**
     * 删除单个，级联删除
     *
     * @param id 主键id
     * @return 删除行
     */
    @ApiOperation(value = "根据id删除检测类型/模板", notes = "根据id删除检测类型/模板")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable("id") String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.delete(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量删除,级联删除
     *
     * @param ids 主键ids
     * @return 删除行
     */
    @ApiOperation(value = "根据id批量删除检测类型/模板", notes = "根据id批量删除检测类型/模板")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        for (String id : ids) {
            service.delete(id);
        }
        restResponse.setCount(ids.size());

        return restResponse;
    }

    /**
     * 返回检测类型的树
     *
     * @return 返回检测类型的树
     */
    @ApiOperation(value = "获取检测类型树结构", notes = "获取检测类型树结构")
    @GetMapping("/getSampleTypeListByIndsutryId")
    public RestResponse<List<TreeNode>> tree(@RequestParam("industryId") String industryId) {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        restResponse.setData(service.getSampleTypeListByIndustryId(industryId));

        return restResponse;
    }

    /**
     * 查询所有未删除的检测类型(排除检测模板)
     *
     * @return 检测类型
     */
    @ApiOperation(value = "查询所有未删除的检测类型(排除检测模板)", notes = "查询所有未删除的检测类型(排除检测模板)")
    @PostMapping("/all")
    public RestResponse<List<DtoSampleType>> findByCategory() {
        RestResponse<List<DtoSampleType>> restResponse = new RestResponse<>();
        restResponse.setData(service.findSampleType());
        return restResponse;
    }

}