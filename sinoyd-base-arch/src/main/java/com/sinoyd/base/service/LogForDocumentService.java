package com.sinoyd.base.service;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoLogForDocument;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;

public interface LogForDocumentService extends IBaseJpaService<DtoLogForDocument, String> {

    /**
     * 保存日志
     *
     * @param documentList 附件
     * @param type         文件类型附件
     */
    void saveLog(List<DtoDocument> documentList, String type);

    /**
     * 根据objectId获取日志
     *
     * @param documentId 附件id
     * @return 日志集合
     */
    List<DtoLogForDocument> findByDocumentId(String documentId);
}
