package com.sinoyd.lims.pro.view;


import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 质控样的数据统计
 */
@Entity
@Table(name = "VI_PRO_QCSampleView")
@Data
public class VQCSample {

    /**
     * 质控样id
     */
    @Id
    private String id;


    /**
     * 质控样的编号
     */
    private String code;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItems;

    /**
     * 采样人员id
     */
    private String samplingPersonId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;


    /**
     * 样品登记时间
     */
    private Date inceptTime;

    /**
     * 样品状态
     */
    private String status;

    /**
     * 原样的id
     */
    private String associateSampleId;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 原样编号
     */
    private String associateSampleCode;

    /**
     * 采样人员
     */
    private String samplingPersonName;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 检测类型大类id
     */
    private String bigSampleTypeId;

    /**
     * 检测机构id
     */
    private String orgId;
}
