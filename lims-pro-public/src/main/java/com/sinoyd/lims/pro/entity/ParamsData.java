package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;


/**
 * ParamsData实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ParamsData")
 @Data
 public  class ParamsData implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  ParamsData() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 对象Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("对象Id")
	private String objectId;
    
    /**
    * 对象类型（枚举EnumParamsDataType1:样品，2:检测单，3:企业，4.采样单）
    */
    @Column(nullable=false)
    @ApiModelProperty("对象类型（枚举EnumParamsDataType1:样品，2:检测单，3:企业，4.采样单）")
	private Integer objectType;

    /**
     * 分组Id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("分组Id")
    private String groupId;

    /**
    * 参数配置id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("参数配置id")
	private String paramsConfigId;
    
    /**
    * 参数名称（使用别名）
    */
    @Column(length=100)
    @ApiModelProperty("参数名称（使用别名）")
    @Length(message = "参数名称（使用别名）{validation.message.length}", max = 100)
	private String paramsName;
    
    /**
    * 参数值
    */
    @Column(length=500)
    @ColumnDefault("")
    @ApiModelProperty("参数值")
    @Length(message = "{validation.message.length}", max = 2000)
	private String paramsValue;
    
    /**
    * 计量单位
    */
    @Column(length=50)
    @ApiModelProperty("计量单位")
    @Length(message = "计量单位{validation.message.length}", max = 50)
	private String dimension;
    
    /**
    * 计量单位Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("计量单位Id")
	private String dimensionId;
    
    /**
    * 排序
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("排序")
	private Integer orderNum;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;
 }