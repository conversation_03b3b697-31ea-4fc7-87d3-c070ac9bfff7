package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * Task2FixedProperty实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/07
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "Task2FixedProperty")
@Data
public class Task2FixedProperty implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 任务id
     */
    @Column(length = 50)
    @ApiModelProperty("任务id")
    private String taskId;

    /**
     * 断面属性id
     */
    @Column(length = 50)
    @ApiModelProperty("断面属性id")
    private String fixedPropertyId;

}
