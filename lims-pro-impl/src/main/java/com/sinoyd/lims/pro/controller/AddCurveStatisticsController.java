package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.AddCurveStatisticsCriteria;
import com.sinoyd.lims.pro.criteria.BlankCurveStatisticsCriteria;
import com.sinoyd.lims.pro.service.AddCurveStatisticsService;
import com.sinoyd.lims.pro.service.BlankCurveStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * AddCurveStatistics加标曲线统计
 * <AUTHOR>
 * @version V1.0.0 2020/02/10
 * @since V100R001
 */
@Api(tags = "示例: addCurveStatistics服务")
@RestController
@RequestMapping("api/pro/addCurveStatistics")
public class AddCurveStatisticsController extends ExceptionHandlerController<AddCurveStatisticsService> {

    /**
     * 查询样品详细数据
     *
     * @param addCurveStatisticsCriteria dto
     * @return RestResponse<Map < String ,  O bject>>
     */
    @ApiOperation(value = "加标曲线查询", notes = "加标曲线查询")
    @GetMapping
    public RestResponse<Map<String, Object>> findAddQCData(AddCurveStatisticsCriteria addCurveStatisticsCriteria) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();

        Map<String, Object> map = service.findAddQCData(addCurveStatisticsCriteria);
        restResp.setRestStatus(StringUtil.isEmpty(map) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(map);
        return restResp;
    }
}
