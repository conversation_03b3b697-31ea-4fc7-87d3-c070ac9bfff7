package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.DtoReceiveSubSampleRecord2Sample;
import com.sinoyd.lims.pro.repository.ReceiveSubSampleRecord2SampleRepository;
import com.sinoyd.lims.pro.service.ReceiveSubSampleRecord2SampleService;
import org.springframework.stereotype.Service;


/**
 * ReceiveSubSampleRecord2Sample操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class ReceiveSubSampleRecord2SampleServiceImpl extends BaseJpaServiceImpl<DtoReceiveSubSampleRecord2Sample,String,ReceiveSubSampleRecord2SampleRepository> implements ReceiveSubSampleRecord2SampleService {

    @Override
    public void findByPage(PageBean<DtoReceiveSubSampleRecord2Sample> pb, BaseCriteria receiveSubSampleRecord2SampleCriteria) {
        pb.setEntityName("DtoReceiveSubSampleRecord2Sample a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, receiveSubSampleRecord2SampleCriteria);
    }
}