package com.sinoyd.lims.pro.service;

import java.util.Date;

import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoOAContract;
import com.sinoyd.lims.lim.entity.Contract;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;

/**
 * 合同审批 业务操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-02
 * @since V100R001
 */
public interface OAContractService {
    /**
     * 添加审批并启动流程
     * 
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<DtoOAContract> taskDto);

    /**
     * 查询任务详细信息
     * 
     * @param taskId 任务ID
     * @return
     */
    DtoOATaskDetail<DtoOAContract, String> findOATaskDetail(String taskId);

    /**
     * 查询可发起合同列表
     * @param page 分页对象
     * @param signBeginDate 签订开始日期
     * @param signEndDate 签订结束日期
     * @param key 关键字
     * @param salesManId 业务员Id
     * @param typeId 合同类型Id
     */
    void findCanSponsor(PageBean<Contract> page, Date signBeginDate, Date signEndDate, String key, String salesManId,
            String typeId);

    /**
     * 将合同审批流程的合同信息复写到原合同信息中
     * @param oaTask 合同审批流程
     */
    void oaContractToContract(DtoOATask oaTask);

    /**
     * 保存为草稿
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask saveAsDraft(DtoOATaskCreate<DtoOAContract> taskDto);

    /**
     * 草稿保存
     * @param taskDto  表单数据
     * @return         oa任务
     */
    OATask draftSave(DtoOATaskCreate<DtoOAContract> taskDto);

    /**
     * 草稿提交
     * @param taskDto  表单数据
     * @return         oa任务
     */
    String draftSubmit(DtoOATaskCreate<DtoOAContract> taskDto);
}
