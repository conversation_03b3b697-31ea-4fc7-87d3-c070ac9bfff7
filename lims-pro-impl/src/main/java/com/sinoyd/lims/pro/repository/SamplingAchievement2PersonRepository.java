package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSamplingAchievement2Person;

import java.util.List;


/**
 *  采样绩效repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/03/12
 */
public interface SamplingAchievement2PersonRepository extends IBaseJpaPhysicalDeleteRepository<DtoSamplingAchievement2Person, String> {

    /**
     * 根据人员id查询数据
     * @param personIds 人员id
     * @return List<DtoSamplingAchievement2Person>
     */
    List<DtoSamplingAchievement2Person> findByPersonIdIn(List<String> personIds);

}
