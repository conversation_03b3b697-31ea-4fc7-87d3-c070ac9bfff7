package com.sinoyd.base.service.impl;

import com.aspose.cells.License;
import com.sinoyd.base.service.BaseLicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;

/**
 * 注册的服务
 * <AUTHOR>
 * @version V1.0.0 2019年08月05日
 * @since   V100R001
 */
@Service
@Slf4j
public class BaseLicenseServiceImpl implements BaseLicenseService {


    @Override
    public Boolean isLicense() {
        Boolean result = false;
        try {
            InputStream is = this.getClass().getClassLoader().getResourceAsStream("license.xml");
            License license = new License();
            license.setLicense(is);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    @Override
    public Boolean isPdfLicense() {
        Boolean result = false;
        try {
            InputStream is = this.getClass().getClassLoader().getResourceAsStream("license.xml");
            com.aspose.pdf.License license = new com.aspose.pdf.License();
            license.setLicense(is);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    @Override
    public String getLicensePath() {
        try {
            String name = "license.xml";
            URL dirs = this.getClass().getClassLoader().getResource(name);
            if (dirs!=null) {
                String filePath = getRootPath(dirs).replace(name, "");
                String endPath = getEndPath(dirs).replace(name, "");
                if (!filePath.equals(endPath)) {
                    return endPath.replace(".jar!", "");
                } else {
                    return filePath.replace(".jar", "");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    private  String getEndPath(URL url) {
        String fileUrl = url.getPath();
        int pos = fileUrl.lastIndexOf('!');
        if (-1 == pos) {
            return fileUrl;
        }
        if (fileUrl.contains("file:/")) {
            return fileUrl.substring(5, pos);
        }
        return fileUrl.substring(0, pos);
    }

    private  String getRootPath(URL url) {
        String fileUrl = url.getPath();
        int pos = fileUrl.indexOf('!');
        if (-1 == pos) {
            return fileUrl;
        }
        if (fileUrl.contains("file:/")) {
            return fileUrl.substring(5, pos);
        }
        return fileUrl.substring(0, pos);
    }
}
