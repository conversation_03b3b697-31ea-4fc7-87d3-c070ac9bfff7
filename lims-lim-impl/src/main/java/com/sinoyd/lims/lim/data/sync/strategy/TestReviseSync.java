package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.TestExpandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 测试项目修约规则同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/24
 */
@Component
@DependsOn({"springContextAware", "testSync"})
@Order(10)
@Slf4j
public class TestReviseSync extends AbsDataSync<DtoTestExpand> {

    private TestExpandService testExpandService;

    /**
     * 数据比较
     *
     * @param testIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoTestExpand>> compareData(List<String> testIds) {
        //获取项目上全部分析项目
        List<DtoTestExpand> projectDataList = testExpandService.findAll();
        //公共库中的分析项目
        List<DtoTestExpand> standardDataList = queryStandardData();
        //如果testIdList不是空，则表示选择部分同步
        if (StringUtil.isNotEmpty(testIds) && StringUtil.isNotEmpty(testIds)) {
            standardDataList = standardDataList.parallelStream().filter(p -> testIds.contains(p.getTestId()))
                    .collect(Collectors.toList());
        }
        //比较数据
        return compareData(standardDataList, projectDataList);
    }

    /**
     * 同步数据
     *
     * @param testIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> testIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoTestExpand>> compareResult = compareData(testIds);
        Optional<DtoDataCompareResult<DtoTestExpand>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoTestExpand errorDto = null;
            try {
                for (DtoTestExpand dtoTestExpand : r.getAddDataList()) {
                    errorDto = dtoTestExpand;
                    testExpandService.save(dtoTestExpand);
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.测试项目修约配置.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.测试项目修约配置.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/testExpand";
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.测试项目修约配置.getValue();
    }

    @Autowired
    @Lazy
    public void setTestExpandService(TestExpandService testExpandService) {
        this.testExpandService = testExpandService;
    }
}