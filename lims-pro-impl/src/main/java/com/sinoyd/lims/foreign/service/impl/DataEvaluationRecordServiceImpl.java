package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataEvaluationRecordVO;
import com.sinoyd.lims.foreign.service.DataEvaluationRecordService;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 评价明细接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataEvaluationRecordServiceImpl extends AbsQueryDataServiceImpl<DataEvaluationRecordVO> implements DataEvaluationRecordService {

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT objectId,objectType,evaluationId,evaluationLevelId,testId,upperLimitSymble,upperLimitValue," +
                "lowerLimitSymble,lowerLimitValue,orgId,domainId");
        sql.append(" from TB_PRO_EvaluationRecord ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime", "syncTime");
        super.appendCondition(sql, map, "AND objectId in (:objectIds)", "objectIds");
    }

    @Override
    protected RowMapper<DataEvaluationRecordVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataEvaluationRecordVO evaluationRecordVO = new DataEvaluationRecordVO();
            evaluationRecordVO.setId(UUIDHelper.NewID());
            evaluationRecordVO.setObjectId(rs.getString("objectId"));
            evaluationRecordVO.setObjectType(rs.getInt("objectType"));
            evaluationRecordVO.setEvaluationId(rs.getString("evaluationId"));
            evaluationRecordVO.setEvaluationLevelId(rs.getString("evaluationLevelId"));
            evaluationRecordVO.setTestId(rs.getString("testId"));
            evaluationRecordVO.setUpperLimitSymble(rs.getString("upperLimitSymble"));
            evaluationRecordVO.setUpperLimitValue(rs.getString("upperLimitValue"));
            evaluationRecordVO.setLowerLimitSymble(rs.getString("lowerLimitSymble"));
            evaluationRecordVO.setLowerLimitValue(rs.getString("lowerLimitValue"));
            evaluationRecordVO.setOrgId(rs.getString("orgId"));
            evaluationRecordVO.setDomainId(rs.getString("domainId"));
            evaluationRecordVO.setSyncTime(new Date());
            return evaluationRecordVO;
        };
    }

}
