package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoOutSample;
import com.sinoyd.lims.pro.dto.customer.DtoOutSampleSave;

import java.util.List;

/**
 * 质控样品操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/5
 * @since V100R001
 */
public interface QMSampleService extends IBaseJpaService<DtoSample, String> {

    /**
     * 新增质控项目样品
     * @param dto 实体
     * @return 质控项目样品
     */
    DtoOutSample saveQmSample(DtoOutSample dto);

    /**
     * 修改质控项目样品
     * @param dto 实体
     */
    void updateQmSample(DtoOutSampleSave dto);
}