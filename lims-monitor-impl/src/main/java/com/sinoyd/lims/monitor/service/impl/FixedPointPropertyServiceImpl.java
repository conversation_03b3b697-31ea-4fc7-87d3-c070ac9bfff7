package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.monitor.dto.lims.DtoProperty2Point;
import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.lims.monitor.repository.rcc.FixedPoint2TestRepository;
import com.sinoyd.lims.monitor.repository.lims.FixedPointPropertyRepository;
import com.sinoyd.lims.monitor.repository.lims.Property2PointRepository;
import com.sinoyd.lims.monitor.repository.lims.PropertyPoint2TestRepository;
import com.sinoyd.lims.monitor.service.FixedPointPropertyService;
import com.sinoyd.lims.monitor.service.Property2PointService;
import com.sinoyd.lims.monitor.service.PropertyPoint2TestService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 监测计划管理业务实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class FixedPointPropertyServiceImpl extends BaseJpaServiceImpl<DtoFixedPointProperty, String, FixedPointPropertyRepository> implements FixedPointPropertyService {

    private Property2PointRepository property2PointRepository;

    private Property2PointService property2PointService;

    private FixedPoint2TestRepository fixedPoint2TestRepository;

    private TestService testService;

    private PropertyPoint2TestRepository propertyPoint2TestRepository;

    private PropertyPoint2TestService propertyPoint2TestService;

    private AnalyzeMethodRepository analyzeMethodRepository;

    @Override
    public void findByPage(PageBean<DtoFixedPointProperty> pb, BaseCriteria fixedPointPropertyCriteria) {
        pb.setEntityName("DtoFixedPointProperty a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fixedPointPropertyCriteria);
        List<DtoFixedPointProperty> dataList = pb.getData();
        if (StringUtil.isNotEmpty(dataList)) {
            List<String> parentIds = dataList.stream().map(DtoFixedPointProperty::getParentId).distinct().collect(Collectors.toList());
            List<DtoFixedPointProperty> parentDtoList = repository.findByIdIn(parentIds);
            dataList.forEach(dto -> {
                Optional<DtoFixedPointProperty> parentOptional = parentDtoList.parallelStream().filter(p -> p.getId().equals(dto.getParentId()))
                        .findFirst();
                parentOptional.ifPresent(p -> dto.setParentName(p.getPropertyName()));
            });
        }
    }

    /**
     * 按照月份批量新增监测计划
     *
     * @param entity 监测计划实体
     * @return 完成新增的监测计划
     */
    @Transactional
    @Override
    public List<DtoFixedPointProperty> batchSaveByMonths(DtoFixedPointProperty entity) {
        List<DtoFixedPointProperty> fixedPointProperties = new ArrayList<>();
        //父id不为空，表示创建子监测计划，同步父监测计划的年份到子监测计划
        if (StringUtil.isNotEmpty(entity.getParentId())) {
            DtoFixedPointProperty parentDto = repository.findOne(entity.getParentId());
            //如果是批量新增则取月份数组
            if (StringUtil.isNotEmpty(entity.getMonths())) {
                for (Integer month : entity.getMonths()) {
                    DtoFixedPointProperty item = new DtoFixedPointProperty();
                    BeanUtils.copyProperties(entity, item, "id");
                    item.setMonth(month);
                    item.setYear(parentDto.getYear());
                    fixedPointProperties.add(item);
                }
            }
            //如果月份数组为空则为单新增
            else {
                entity.setYear(parentDto.getYear());
                fixedPointProperties.add(entity);
            }
        } else {
            fixedPointProperties.add(entity);
        }
        return super.save(fixedPointProperties);
    }

    @Override
    public List<DtoFixedPointProperty> loadTree(Integer year) {
        List<DtoFixedPointProperty> dataList = repository.findByYear(year);
        return loadTree(dataList);
    }

    @Override
    public List<DtoTest> loadPointTest(List<String> pointIds) {
        List<DtoFixedPoint2Test> fixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointIdIn(pointIds);
        if (StringUtil.isNotEmpty(fixedPoint2TestList)) {
            List<String> testIds = fixedPoint2TestList.parallelStream().map(DtoFixedPoint2Test::getTestId)
                    .collect(Collectors.toList());
            List<DtoTest> testList = testService.findAll(testIds);
            testService.loadTest(testList);
            testList.sort(Comparator.comparing(DtoTest::getOrderNum, Comparator.reverseOrder()));
            return testList;
        }
        return null;
    }

    /**
     * 监测计划复制方法，复制监测计划需要复制如下几张表
     * 1. TB_MONITOR_FixedPointProperty 监测计划表
     * 2. TB_MONITOR_Property2Point 监测计划与点位关联表
     * 3. TB_MONITOR_PropertyPoint2Test 监测计划点位于测试项目关联表，
     * 需要注意的是这张表中的propertyPointId存的是TB_MONITOR_Property2Point表的id
     *
     * @param parameter 复制参数
     */
    @Transactional
    @Override
    @SuppressWarnings("unchecked")
    public void copyProperty(Map<String, Object> parameter) {
        //被复制的年份，如果复制子监测计划，该参数为null
        Integer fromYear = parameter.get("fromYear") == null ? null : (Integer) parameter.get("fromYear");
        //监测计划目标年份，如果复制子监测计划，该参数为null
        Integer toYear = parameter.get("toYear") == null ? null : (Integer) parameter.get("toYear");
        //源监测计划id，被复制的监测子计划id
        String sourceId = (String) parameter.get("id");
        //复制次数，仅用于年份复制
        Integer times = (Integer) parameter.get("times");
        //如果复制为检测子计划，则按照月份复制
        List<Integer> months = (List<Integer>) parameter.get("months");
        List<DtoFixedPointProperty> sourcePropertyList = new ArrayList<>();
        List<DtoFixedPointProperty> parentPropertyList;
        List<DtoFixedPointProperty> childPropertyList = new ArrayList<>();
        List<DtoFixedPointProperty> destDtoFixedPointPropertyList;
        //复制父监测计划或者监测子计划
        if (fromYear == null) {
            //根据id被复制的监测计划
            DtoFixedPointProperty sourceProperty = repository.findOne(sourceId);
            toYear = sourceProperty.getYear();
            sourcePropertyList.add(sourceProperty);
            if (UUIDHelper.GUID_EMPTY.equals(sourceProperty.getParentId())) {
                //复制父监测计划
                //获取父监测计划下的所有子监测计划
                childPropertyList.addAll(repository.findByParentIdIn(Collections.singletonList(sourceProperty.getId())));
                sourcePropertyList.addAll(childPropertyList);
                destDtoFixedPointPropertyList = copyFixedPointPropertyYear(Collections.singletonList(sourceProperty), childPropertyList, toYear, 1);
            } else {
                //按照月份复制子检测计划
                destDtoFixedPointPropertyList = copyFixedPointPropertyByMonths(sourcePropertyList, childPropertyList, toYear, months);
            }
        } else {
            //复制监测计划
            sourcePropertyList = repository.findByYear(fromYear);
            if (StringUtil.isEmpty(sourcePropertyList)) {
                throw new BaseException("该年份不存在监测计划!");
            }
            parentPropertyList = sourcePropertyList.parallelStream()
                    .filter(p -> UUIDHelper.GUID_EMPTY.equals(p.getParentId()))
                    .collect(Collectors.toList());
            childPropertyList = sourcePropertyList.parallelStream()
                    .filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getParentId()))
                    .collect(Collectors.toList());
            //按照年份复制监测计划
            destDtoFixedPointPropertyList = copyFixedPointPropertyYear(parentPropertyList, childPropertyList, toYear, times);
        }
        //复制监测计划和点位关联信息
        List<String> sourcePropertyIds = sourcePropertyList.parallelStream()
                .map(DtoFixedPointProperty::getId)
                .collect(Collectors.toList());
        List<DtoProperty2Point> sourceP2pList = property2PointRepository.findByPropertyIdIn(sourcePropertyIds);
        //源监测计划和点位关联信息存在，进行复制
        if (StringUtil.isNotEmpty(sourceP2pList)) {
            //复制监测计划和点位关联信息
            List<DtoProperty2Point> destP2pList = copyProperty2Point(sourceP2pList, destDtoFixedPointPropertyList);
            //获取被复制监测计划的关联表id
            List<String> sourceP2pIds = sourceP2pList.parallelStream().map(DtoProperty2Point::getId)
                    .distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(sourceP2pIds)) {
                //获取被复制监测计划的测试项目关联表
                List<DtoPropertyPoint2Test> sourcePp2tList = propertyPoint2TestRepository.findByPropertyPointIdIn(sourceP2pIds);
                //复制监测计划点位关联信息的关联测试项目信息
                copyPropertyPoint2Test(sourcePp2tList, destP2pList);
            }
        }
    }

    /**
     * 按照年份复制监测计划
     *
     * @param parentPropertyList 源监测计划列表
     * @param childPropertyList  源子监测计划列表
     * @param destYear           目标年
     * @param times              复制的月份
     * @return 复制后的监测计划列表
     */
    protected List<DtoFixedPointProperty> copyFixedPointPropertyYear(List<DtoFixedPointProperty> parentPropertyList,
                                                                     List<DtoFixedPointProperty> childPropertyList,
                                                                     Integer destYear,
                                                                     Integer times) {
        //目标监测计划
        List<DtoFixedPointProperty> destDtoList = new ArrayList<>();
        int i = 0;
        while (i < times) {
            //复制监测计划
            for (DtoFixedPointProperty parentDto : parentPropertyList) {
                DtoFixedPointProperty destDto = new DtoFixedPointProperty();
                if (StringUtil.isEmpty(childPropertyList)) {
                    //这里是复制监测子计划
                    destDto.setParentId(parentDto.getParentId());
                } else {
                    destDto.setParentId(UUIDHelper.GUID_EMPTY);
                }
                destDto.setPropertyName(parentDto.getPropertyName());
                destDto.setMonth(parentDto.getMonth());
                destDto.setCycleOrder(parentDto.getCycleOrder());
                destDto.setTimesOrder(parentDto.getTimesOrder());
                destDto.setSampleTypeId(parentDto.getSampleTypeId());
                destDto.setPointType(parentDto.getPointType());
                destDto.setYear(destYear);
                destDto.setSourceId(parentDto.getId());
                destDtoList.add(destDto);
            }
            //复制监测子计划
            for (DtoFixedPointProperty childDto : childPropertyList) {
                DtoFixedPointProperty destDto = new DtoFixedPointProperty();
                destDto.setParentId(UUIDHelper.GUID_EMPTY);
                Optional<DtoFixedPointProperty> sourceParent = parentPropertyList.parallelStream()
                        .filter(p -> p.getId().equals(childDto.getParentId())).findFirst();
                sourceParent.ifPresent(s -> {
                    String sourceId = s.getId();
                    Optional<DtoFixedPointProperty> destParent = destDtoList.parallelStream()
                            .filter(p -> p.getSourceId().equals(sourceId)).findFirst();
                    destParent.ifPresent(d -> destDto.setParentId(d.getId()));
                });
                destDto.setPropertyName(childDto.getPropertyName());
                destDto.setMonth(childDto.getMonth());
                destDto.setCycleOrder(childDto.getCycleOrder());
                destDto.setTimesOrder(childDto.getTimesOrder());
                destDto.setSampleTypeId(childDto.getSampleTypeId());
                destDto.setPointType(childDto.getPointType());
                destDto.setYear(destYear);
                destDto.setSourceId(childDto.getId());
                destDtoList.add(destDto);
            }
            i++;
        }
        super.save(destDtoList);
        return destDtoList;
    }

    /**
     * 按照月份复制检测计划
     *
     * @param parentPropertyList 源监测计划列表
     * @param childPropertyList  原监测子计划列表
     * @param destYear           年份
     * @param months             要复制的月份
     * @return 复制后的监测计划
     */
    protected List<DtoFixedPointProperty> copyFixedPointPropertyByMonths(List<DtoFixedPointProperty> parentPropertyList,
                                                                         List<DtoFixedPointProperty> childPropertyList,
                                                                         Integer destYear,
                                                                         List<Integer> months) {
        //目标监测计划
        List<DtoFixedPointProperty> destDtoList = new ArrayList<>();
        //按照月份复制
        for (Integer month : months) {
            //遍历被复制的监测计划
            for (DtoFixedPointProperty parentDto : parentPropertyList) {
                DtoFixedPointProperty destDto = new DtoFixedPointProperty();
                if (StringUtil.isEmpty(childPropertyList)) {
                    //这里是复制监测子计划
                    destDto.setParentId(parentDto.getParentId());
                } else {
                    destDto.setParentId(UUIDHelper.GUID_EMPTY);
                }
                destDto.setPropertyName(parentDto.getPropertyName());
                destDto.setMonth(month);
                destDto.setCycleOrder(parentDto.getCycleOrder());
                destDto.setTimesOrder(parentDto.getTimesOrder());
                destDto.setSampleTypeId(parentDto.getSampleTypeId());
                destDto.setPointType(parentDto.getPointType());
                destDto.setYear(destYear);
                //记录被复制监测计划的id用于后面的数据匹配
                destDto.setSourceId(parentDto.getId());
                destDtoList.add(destDto);
            }
        }
        super.save(destDtoList);
        return destDtoList;
    }

    /**
     * 复制监测计划和点位关联信息
     *
     * @param sourceP2pList                 源监测计划点位关联列表
     * @param destDtoFixedPointPropertyList 目标监测计划列表
     * @return 复制的监测计划点位关联实体
     */
    private List<DtoProperty2Point> copyProperty2Point(List<DtoProperty2Point> sourceP2pList,
                                                       List<DtoFixedPointProperty> destDtoFixedPointPropertyList) {
        List<DtoProperty2Point> destP2pList = new ArrayList<>();
        //遍历所有复制后的监测计划
        for (DtoFixedPointProperty destProperty : destDtoFixedPointPropertyList) {
            //获取当前监测计划父级的监测计划点位关联表
            List<DtoProperty2Point> property2PointsOfDestProperty = sourceP2pList.parallelStream()
                    .filter(p -> p.getPropertyId().equals(destProperty.getSourceId()))
                    .collect(Collectors.toList());
            //遍历父级的监测计划与点位关联表，并将关联表的sourceId存储为父级关联表的id
            for (DtoProperty2Point dtoProperty2Point : property2PointsOfDestProperty) {
                DtoProperty2Point p2p = new DtoProperty2Point();
                p2p.setFixedPointId(dtoProperty2Point.getFixedPointId());
                p2p.setPropertyId(destProperty.getId());
                p2p.setSourceId(dtoProperty2Point.getId());
                destP2pList.add(p2p);
            }
        }
        property2PointService.save(destP2pList);
        return destP2pList;
    }

    /**
     * 复制监测计划和点位关联信息的关联测试项目信息
     *
     * @param sourcePp2tList 源监测计划和点位关联信息的关联测试项目信息
     * @param destP2pList    目标监测计划和点位关联信息
     */
    private void copyPropertyPoint2Test(List<DtoPropertyPoint2Test> sourcePp2tList, List<DtoProperty2Point> destP2pList) {
        List<DtoPropertyPoint2Test> destPp2tList = new ArrayList<>();
        //遍历所有父级监测计划与测试项目关联表数据
        for (DtoPropertyPoint2Test sourcePp2t : sourcePp2tList) {
            //获取子级的监测计划与点位关联表数据
            List<DtoProperty2Point> property2Points = destP2pList.parallelStream()
                    .filter(p -> p.getSourceId().equals(sourcePp2t.getPropertyPointId()))
                    .collect(Collectors.toList());
            //遍历子级监测计划与点位关联表数据创建关联测试项目数据
            for (DtoProperty2Point property2Point : property2Points) {
                DtoPropertyPoint2Test destPp2t = new DtoPropertyPoint2Test();
                destPp2t.setPropertyPointId(property2Point.getId());
                destPp2t.setTestId(sourcePp2t.getTestId());
                destPp2tList.add(destPp2t);
            }
        }
        propertyPoint2TestService.save(destPp2tList);
    }

    @Transactional
    @Override
    public void selectRelationPoints(DtoFixedPointProperty dtoFixedPointProperty) {
        List<String> relationPointIds = dtoFixedPointProperty.getRelationPointIds();
        List<DtoProperty2Point> property2PointList = new ArrayList<>();
        for (String pointId : relationPointIds) {
            DtoProperty2Point property2Point = new DtoProperty2Point();
            property2Point.setFixedPointId(pointId);
            property2Point.setPropertyId(dtoFixedPointProperty.getId());
            property2PointList.add(property2Point);
        }
        property2PointRepository.save(property2PointList);
        List<DtoPropertyPoint2Test> point2TestList = new ArrayList<>();
        property2PointList.forEach(p -> {
            List<DtoTest> testList = dtoFixedPointProperty.getTestList();
            testList.forEach(t -> {
                DtoPropertyPoint2Test point2Test = new DtoPropertyPoint2Test();
                point2Test.setPropertyPointId(p.getId());
                point2Test.setTestId(t.getId());
                point2Test.setTimesOrder(t.getTimesOrder());
                point2Test.setSamplePeriod(t.getSamplePeriod());
                point2TestList.add(point2Test);
            });
        });
        propertyPoint2TestService.save(point2TestList);
    }

    @Transactional
    @Override
    @SuppressWarnings("unchecked")
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> pkList = (List<String>) ids;
        List<DtoFixedPointProperty> propertyList = repository.findByIdIn(pkList);
        //找出子监测计划
        List<DtoFixedPointProperty> childrenPropertyList = repository.findByParentIdIn(pkList);
        propertyList.addAll(childrenPropertyList);
        //获取监测计划id
        List<String> propertyIds = propertyList.parallelStream().map(DtoFixedPointProperty::getId).collect(Collectors.toList());
        //获取关联点位
        List<DtoProperty2Point> property2PointList = property2PointRepository.findByPropertyIdIn(propertyIds);
        if (StringUtil.isNotEmpty(property2PointList)) {
            //获取监测计划关联点位的关联测试项目
            List<String> p2pIds = property2PointList.parallelStream().map(DtoProperty2Point::getId).collect(Collectors.toList());
            List<DtoPropertyPoint2Test> pp2tList = propertyPoint2TestRepository.findByPropertyPointIdIn(p2pIds);
            if (StringUtil.isNotEmpty(pp2tList)) {
                //删除监测计划关联点位的关联测试项目
                propertyPoint2TestService.delete(pp2tList);
            }
            //删除关联点位
            property2PointRepository.delete(property2PointList);
        }
        return super.logicDeleteById(propertyIds);
    }

    /**
     * 加载树结构
     *
     * @param dataList 数据列表
     * @return 树结构
     */
    private List<DtoFixedPointProperty> loadTree(List<DtoFixedPointProperty> dataList) {
        List<DtoFixedPointProperty> treeList = new ArrayList<>();
        // 获取检测计划对应是否提示Map
        Map<String, Object> property2TipMap = getProperty2TipMap(dataList);
        for (DtoFixedPointProperty dto : getRootNode(dataList)) {
            DtoFixedPointProperty treeDto = loadChildList(dto, dataList, property2TipMap);
            treeList.add(treeDto);
        }
        return treeList;
    }

    /**
     * 给树中每个节点设置子节点
     *
     * @param dto      单个节点
     * @param dataList 数据列表
     * @return 单个节点
     */
    private DtoFixedPointProperty loadChildList(DtoFixedPointProperty dto, List<DtoFixedPointProperty> dataList,
                                                Map<String, Object> property2TipMap) {
        List<DtoFixedPointProperty> childList = new ArrayList<>();
        for (DtoFixedPointProperty property : dataList) {
            if (dto.getId().equals(property.getParentId())) {
                property.setParentName(dto.getPropertyName());
                childList.add(loadChildList(property, dataList, property2TipMap));
            }
        }

        if (StringUtil.isNotEmpty(childList)) {
            List<Integer> monthList = childList.parallelStream().map(DtoFixedPointProperty::getMonth)
                    .distinct().sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());
            List<String> monthStrList = new ArrayList<>();
            monthList.forEach(m -> monthStrList.add(m + ""));
            String displayName = dto.getPropertyName() + "(" + String.join("、", monthStrList) + ")";
            dto.setDisplayName(displayName);
            dto.setChildList(childList);
            dto.setIsTestTip(childList.stream().anyMatch(DtoFixedPointProperty::getIsTestTip));
        } else {
            if (dto.getMonth() > 0) {
                dto.setDisplayName(dto.getPropertyName() + "(" + dto.getMonth() + ")");
            } else {
                dto.setDisplayName(dto.getPropertyName());
            }
            dto.setIsTestTip(property2TipMap.containsKey(dto.getId()) ? (Boolean) property2TipMap.get(dto.getId()) : false);
        }
        //先按照排序值排序，再按照月份排序
        childList.sort(Comparator.comparing(DtoFixedPointProperty::getOrderNum, Comparator.reverseOrder())
                .thenComparing(DtoFixedPointProperty::getMonth));
        dto.setChildList(childList);
        return dto;
    }

    /**
     * 获取树结构的根节点
     *
     * @param dataList 数据列表
     * @return 根节点列表
     */
    private List<DtoFixedPointProperty> getRootNode(List<DtoFixedPointProperty> dataList) {
        List<DtoFixedPointProperty> rootList = new ArrayList<>();
        for (DtoFixedPointProperty dto : dataList) {
            if (StringUtil.isEmpty(dto.getParentId()) || UUIDHelper.GUID_EMPTY.equals(dto.getParentId())) {
                rootList.add(dto);
            }
        }
        rootList.sort(Comparator.comparing(DtoFixedPointProperty::getOrderNum, Comparator.reverseOrder()));
        return rootList;
    }


    /**
     * 根据检测计划获取计划id对应提示映射Map
     *
     * @param dataList 监测计划集合
     * @return 计划id对应提示映射Map
     */
    private Map<String, Object> getProperty2TipMap(List<DtoFixedPointProperty> dataList) {
        // 根据检测计划获取计划内所有点位信息，测试项目，分析方法
        List<String> propertyIds = dataList.stream().filter(p -> StringUtil.isNotEmpty(p.getParentId()) &&
                !UUIDHelper.GUID_EMPTY.equals(p.getParentId())).map(DtoFixedPointProperty::getId).collect(Collectors.toList());
        List<DtoProperty2Point> property2PointList = StringUtil.isNotEmpty(propertyIds) ?
                property2PointRepository.findByPropertyIdIn(propertyIds) : new ArrayList<>();
        List<String> p2pIds = property2PointList.parallelStream().map(DtoProperty2Point::getId).collect(Collectors.toList());
        List<DtoPropertyPoint2Test> pp2tList = StringUtil.isNotEmpty(p2pIds) ?
                propertyPoint2TestRepository.findByPropertyPointIdIn(p2pIds) : new ArrayList<>();
        List<String> testIds = pp2tList.parallelStream().map(DtoPropertyPoint2Test::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testService.findAllDeleted(testIds) : new ArrayList<>();
        List<String> analyzeMethodIds = testList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
        List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(analyzeMethodIds) ?
                analyzeMethodRepository.findAllDeleted(analyzeMethodIds) : new ArrayList<>();
        Map<String, Object> property2TipMap = new HashMap<>();
        for (String propertyId : propertyIds) {
            // 筛选计划内数据，判定是否需要提示
            boolean tip = false;
            List<DtoProperty2Point> property2Points = property2PointList.stream().filter(p -> p.getPropertyId().equals(propertyId)).collect(Collectors.toList());
            List<String> p2pByPropertyIds = property2Points.stream().map(DtoProperty2Point::getId).collect(Collectors.toList());
            List<DtoPropertyPoint2Test> thisPp2tList = pp2tList.parallelStream().filter(p -> p2pByPropertyIds.contains(p.getPropertyPointId()))
                    .collect(Collectors.toList());
            List<String> thisTestIds = thisPp2tList.parallelStream().map(DtoPropertyPoint2Test::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> thisTestList = testList.parallelStream().filter(t -> thisTestIds.contains(t.getId())).collect(Collectors.toList());
            List<String> methodIds = thisTestList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
            List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodList.stream().filter(p -> methodIds.contains(p.getId())).collect(Collectors.toList());
            // 当点位关联测试项目状态，存在“停用”、“作废”、“删除”的测试项目时，需要测试项目提示
            if (analyzeMethods.stream().anyMatch(p -> EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(p.getStatus())
                    || EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(p.getStatus()) || p.getIsDeleted()) ||
                    thisTestList.stream().anyMatch(DtoTest::getIsDeleted)) {
                tip = true;
            }
            property2TipMap.put(propertyId, tip);
        }
        return property2TipMap;
    }

    @Autowired
    public void setProperty2PointRepository(Property2PointRepository property2PointRepository) {
        this.property2PointRepository = property2PointRepository;
    }

    @Autowired
    @Lazy
    public void setProperty2PointService(Property2PointService property2PointService) {
        this.property2PointService = property2PointService;
    }

    @Autowired
    public void setFixedPoint2TestRepository(FixedPoint2TestRepository fixedPoint2TestRepository) {
        this.fixedPoint2TestRepository = fixedPoint2TestRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setPropertyPoint2TestRepository(PropertyPoint2TestRepository propertyPoint2TestRepository) {
        this.propertyPoint2TestRepository = propertyPoint2TestRepository;
    }

    @Autowired
    public void setPropertyPoint2TestService(PropertyPoint2TestService propertyPoint2TestService) {
        this.propertyPoint2TestService = propertyPoint2TestService;
    }

    @Autowired
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }
}