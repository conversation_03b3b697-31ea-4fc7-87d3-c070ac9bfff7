package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * 消息查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022-09-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageSendRecordCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 状态 (0：未读，1：已读)
     */
    private Integer status;

    /**
     * 关键字
     */
    private String key;

    /**
     * 检索开始时间
     */
    private String dtBegin;

    /**
     * 检索结束时间
     */
    private String dtEnd;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(dtBegin)) {
            Date from = DateUtil.stringToDate(this.dtBegin, DateUtil.YEAR);
            condition.append(" and a.sendTime >= :dtBegin");
            values.put("dtBegin", from);
        }
        if (StringUtil.isNotEmpty(dtEnd)) {
            Date to = DateUtil.stringToDate(this.dtEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and a.sendTime < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        if (EnumLIM.EnumMessageSendRecordStatus.未读.getValue().equals(status) || EnumLIM.EnumMessageSendRecordStatus.已读.getValue().equals(status)) {
            condition.append(" and a.status = :status");
            values.put("status", status);
        }
        if (StringUtil.isNotEmpty(messageType) && !UUIDHelper.GUID_EMPTY.equals(messageType)) {
            // 先定义逗号分割的为查询多个，后期需要更改
            if (messageType.contains(",")){
               List<String> messageTypes = Arrays.asList(messageType.split(","));
                condition.append(" and a.messageType in :messageTypes");
                values.put("messageTypes", messageTypes);
            }else {
                condition.append(" and a.messageType = :messageType");
                values.put("messageType", this.messageType);
            }
        }

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and messageContent like :key ");
            values.put("key", likeQuery(this.key));
        }

        //默认当前人的数据
        condition.append(" and receiver =:receiver");
        values.put("receiver", PrincipalContextUser.getPrincipal().getUserId());

        return condition.toString();
    }
}
