package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.pro.dto.DtoOrderQuotation;
import com.sinoyd.lims.pro.dto.customer.DtoOrderSubmit;
import com.sinoyd.lims.pro.dto.customer.DtoSignOrder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.OrderFormService;
import com.sinoyd.lims.pro.criteria.OrderFormCriteria;
import com.sinoyd.lims.pro.dto.DtoOrderForm;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * OrderForm服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @Api(tags = "示例: OrderForm服务")
 @RestController
 @RequestMapping("api/pro/orderForm")
 public class OrderFormController extends BaseJpaController<DtoOrderForm, String,OrderFormService> {


    /**
     * 分页动态条件查询OrderForm
     *
     * @param orderFormCriteria 条件参数
     * @return RestResponse<List < OrderForm>>
     */
    @ApiOperation(value = "分页动态条件查询OrderForm", notes = "分页动态条件查询OrderForm")
    @GetMapping
    public RestResponse<List<DtoOrderForm>> findByPage(OrderFormCriteria orderFormCriteria) {
        PageBean<DtoOrderForm> pageBean = super.getPageBean();
        RestResponse<List<DtoOrderForm>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, orderFormCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询OrderForm
     *
     * @param id 主键id
     * @return RestResponse<DtoOrderForm>
     */
    @ApiOperation(value = "按主键查询OrderForm", notes = "按主键查询OrderForm")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoOrderForm> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOrderForm> restResponse = new RestResponse<>();
        DtoOrderForm orderForm = service.findOne(id);
        restResponse.setData(orderForm);
        restResponse.setRestStatus(StringUtil.isNull(orderForm) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增OrderForm
     *
     * @param orderForm 实体列表
     * @return RestResponse<DtoOrderForm>
     */
    @ApiOperation(value = "新增OrderForm", notes = "新增OrderForm")
    @PostMapping
    public RestResponse<DtoOrderForm> create(@RequestBody @Validated DtoOrderForm orderForm) {
        RestResponse<DtoOrderForm> restResponse = new RestResponse<>();
        restResponse.setData(service.save(orderForm));
        return restResponse;
    }

    /**
     * 新增OrderForm
     *
     * @param orderForm 实体列表
     * @return RestResponse<DtoOrderForm>
     */
    @ApiOperation(value = "修改OrderForm", notes = "修改OrderForm")
    @PutMapping
    public RestResponse<DtoOrderForm> update(@RequestBody @Validated DtoOrderForm orderForm) {
        RestResponse<DtoOrderForm> restResponse = new RestResponse<>();
        restResponse.setData(service.update(orderForm));
        return restResponse;
    }

    /**
     * "根据id批量删除OrderForm
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除OrderForm", notes = "根据id批量删除OrderForm")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 更新费用明细
     *
     * @param orderQuotation 实体列表
     * @return RestResponse<DtoOrderQuotation>
     */
    @ApiOperation(value = "更新费用明细", notes = "更新费用明细")
    @PutMapping("/updateQuotation")
    public RestResponse<DtoOrderQuotation> updateQuotation(@RequestBody DtoOrderQuotation orderQuotation) {
        RestResponse<DtoOrderQuotation> restResponse = new RestResponse<>();
        restResponse.setData(service.updateQuotation(orderQuotation.getOrderId(), orderQuotation.getIsOther(),
                orderQuotation.getIsQuotation()));
        return restResponse;
    }

    /**
     * 更新已检+未检数
     *
     * @param orderForm 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "更新已检+未检数", notes = "更新已检+未检数")
    @PostMapping("/updateCount")
    public RestResponse<String> updateCount(@RequestBody DtoOrderForm orderForm) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.updateCount(orderForm.getId());
        return restResponse;
    }

    /**
     * 订单提交
     *
     * @param orderSubmit 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "订单提交", notes = "订单提交")
    @PostMapping("/submit")
    public RestResponse<String> submit(@RequestBody DtoOrderSubmit orderSubmit) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.submit(orderSubmit);
        return restResponse;
    }

    /**
     * 订单审核
     *
     * @param orderSubmit 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "订单审核", notes = "订单审核")
    @PostMapping("/auditOrder")
    public RestResponse<String> auditOrder(@RequestBody DtoOrderSubmit orderSubmit) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.auditOrder(orderSubmit);
        return restResponse;
    }

    /**
     * 复制订单
     *
     * @param orderIds 订单数据
     * @return RestResponse<String>
     */
    @ApiOperation(value = "复制订单", notes = "复制订单")
    @PostMapping("/copy")
    public RestResponse<String> copy(@RequestBody List<String> orderIds){
        RestResponse<String> response = new RestResponse<>();
        service.copyOrderForm(orderIds);
        response.setMsg("复制成功");
        return response;
    }

    /**
     * 复制订单
     *
     * @param signOrder 订单数据
     * @return RestResponse<String>
     */
    @ApiOperation(value = "订单签订", notes = "订单签订")
    @PostMapping("/signing")
    public RestResponse<String> signing(@RequestBody DtoSignOrder signOrder){
        RestResponse<String> response = new RestResponse<>();
        service.signingOrder(signOrder);
        response.setMsg("签订成功");
        return response;
    }
}