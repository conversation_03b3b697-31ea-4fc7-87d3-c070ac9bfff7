package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.output.JsonStream;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoFastNavigationTemplate;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.FastNavigationTemplateRepository;
import com.sinoyd.lims.lim.service.FastNavigationTemplateService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * FastNavigationTemplate操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/3/27
 * @since V100R001
 */
 @Service
public class FastNavigationTemplateServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFastNavigationTemplate,String,FastNavigationTemplateRepository> implements FastNavigationTemplateService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void findByPage(PageBean<DtoFastNavigationTemplate> pb, BaseCriteria fastNavigationTemplateCriteria) {
        pb.setEntityName("DtoFastNavigationTemplate a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fastNavigationTemplateCriteria);
    }


    @Transactional
    @Override
    public List<DtoFastNavigationTemplate> save(Collection<DtoFastNavigationTemplate> entities) {
        //对当前人自己的快速导航进行配置修改
        String userId = PrincipalContextUser.getPrincipal().getUserId();
        //删除当前人之前配置的快速导航，再重新配置
        repository.deleteByUserId(userId);
        for (DtoFastNavigationTemplate entity : entities) {
            entity.setUserId(userId);
        }
        List<DtoFastNavigationTemplate> item = super.save(entities);
        //利用通知的方式，要清除公告缓存
        Map<String, Object> map = new HashMap<>();
        map.put("userId", PrincipalContextUser.getPrincipal().getUserId());
        map.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        redisTemplate.convertAndSend(EnumLIM.EnumLIMRedisChannel.LIM_FastNavigation_Cache.name(), JsonStream.serialize(map));
        return item;
    }
}