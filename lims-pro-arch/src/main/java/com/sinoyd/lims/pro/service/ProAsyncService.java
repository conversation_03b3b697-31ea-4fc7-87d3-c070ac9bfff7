package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoSample;

import java.util.List;

/**
 * 检测业务操作接口(异步的调用)
 * <AUTHOR>
 * @version V1.0.0 2020/07/15
 * @since V100R001
 */
public interface ProAsyncService {

    /**
     * 核对样品状态
     *
     * @param samples 样品信息
     * @param project 已经知道哪个项目了
     */
    void checkSample(List<DtoSample> samples, DtoProject project,CurrentPrincipalUser principalContextUser);
}
