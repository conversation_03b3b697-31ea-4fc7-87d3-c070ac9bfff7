package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaRevise;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 测试项目公式导入(修改）修约公式sheet数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/8
 * @since V100R001
 */
@Data
public class TestFormulaReviseVerifyHandler implements IExcelVerifyHandler<DtoImportTestFormulaRevise> {
    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();


    /**
     *  重复数据校验容器
     */
    private List<DtoImportTestFormulaRevise> duplicationCheckList;

    /**
     * 数据校验
     *
     * @param importTestFormulaRevise 导入的实体
     * @return 导入结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportTestFormulaRevise importTestFormulaRevise) {
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(importTestFormulaRevise)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //数据去除前后空格
            importUtils.strToTrim(importTestFormulaRevise);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }


        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + (importTestFormulaRevise.getRowNum()-1) + "行数据校验有误");

        //region 必填校验
        importUtils.checkIsNull(result, importTestFormulaRevise.getId(), "公式id", failStr);
        importUtils.checkIsNull(result, importTestFormulaRevise.getReviseFormula(), "修约公式", failStr);

        //region 数据格式校验
        importUtils.checkNumTwo(result, importTestFormulaRevise.getReviseMostDecimal(), "小数位数", failStr);
        importUtils.checkNumTwo(result, importTestFormulaRevise.getReviseMostSignificance(), "有效位数", failStr);

        //excel重复数据判断
        isRepeatData(result, failStr, importTestFormulaRevise, duplicationCheckList);

        //更新重复校验容器
        duplicationCheckList.add(importTestFormulaRevise);

        //构建异常信息
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }

    /**
     * excel重复数据判断 根据公式id，参数名称，进行唯一性校验
     * @param result  校验结果
     * @param failStr 校验错误数据
     * @param importTestFormulaRevise 导入数据
     * @param tempList 已校验数据
     */
    private void isRepeatData(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTestFormulaRevise importTestFormulaRevise, List<DtoImportTestFormulaRevise> tempList) {
        if (StringUtil.isNotEmpty(importTestFormulaRevise.getId()) && StringUtil.isNotEmpty(importTestFormulaRevise.getReviseFormula())) {
            tempList.removeIf(p -> StringUtil.isEmpty(p.getId()) || StringUtil.isEmpty(p.getReviseFormula()));
            List<Integer> isRepeatRowNum = tempList.stream()
                    .filter(p -> importTestFormulaRevise.getId().equals(p.getId()) && importTestFormulaRevise.getReviseFormula().equals(p.getReviseFormula()))
                    .map(DtoImportTestFormulaRevise::getRowNum)
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isRepeatRowNum)) {
                result.setSuccess(false);
                failStr.append("；").append("修约公式与第").append(isRepeatRowNum).append("条重复");
            }
        }
    }
}
