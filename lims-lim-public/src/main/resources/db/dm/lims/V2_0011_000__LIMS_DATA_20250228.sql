-- 气相色谱质谱法测定土壤有机物原始记录报表模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('30544dc8-b872-4b5f-8314-8bf940dc44ad', 1, 'WorkSheetQXSPZPT', 'GJW-04-2019-YS-TR-022 气相色谱质谱法测定土壤有机物原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-022 气相色谱质谱法测定土壤有机物原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-022 气相色谱质谱法测定土壤有机物原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "QXSPZPTWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',
        3250, 2, NULL, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2022-01-05 11:00:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-25 09:23:16', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetQXSPZPT', 0, '', '', NULL, NULL, '',
        1, 26);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('54e871e1-8b50-4ee6-9340-dec2e1a593ba', '30544dc8-b872-4b5f-8314-8bf940dc44ad', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-25 09:24:59', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-25 09:24:59', NULL);

-- 液相色谱法测定土壤有机物原始记录报表配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('b7b152ab-0421-4c33-9535-2603890f3d59', 1, 'WorkSheetYXSPT', 'GJW-04-2019-YS-TR-024 液相色谱法测定土壤有机物原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-024 液相色谱法测定土壤有机物原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-024 液相色谱法测定土壤有机物原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "QXSPZPTWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',
        3450, 2, NULL, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2022-01-05 11:00:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-25 09:27:29', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetYXSPT', 0, '', '', NULL, NULL, '',
        1, 26);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('93f1b061-bbc2-4a61-97e7-eb264db71ec4', 'b7b152ab-0421-4c33-9535-2603890f3d59', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-25 15:38:34', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-25 15:38:34', NULL);

-- 气相色谱法测定土壤有机物原始记录报表配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName,
                                returnType, method, params, pageConfig, orderNum, bizType,
                                remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl,
                                isDefineFileName, defineFileName, beanName, versionNum,
                                controlNum, reportName, validate, usageNum)
VALUES ('4ee5957b-487c-4c0b-9a08-35f164cedbf1', 1, 'WorkSheetQXSPT', 'GJW-04-2019-YS-TR-020 气相色谱法测定土壤有机物原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-020 气相色谱法测定土壤有机物原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-020 气相色谱法测定土壤有机物原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "QXSPZPTWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',
        3550, 2, NULL, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2022-01-05 11:00:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-25 09:27:29', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetQXSPT', 0, '', '', NULL, NULL, '',
        1, 26);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('f6f14dce-151d-48e1-93d4-ce5f38e9b019', '4ee5957b-487c-4c0b-9a08-35f164cedbf1', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-25 15:38:34', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-25 15:38:34', NULL);

--  液相色谱质谱法测定土壤有机物原始记录报表模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('0618aca9-4a58-417b-b7fb-998802259f80', 1, 'WorkSheetYXSPZPT', 'GJW-04-2019-YS-TR-026 液相色谱质谱法测定土壤有机物原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-026 液相色谱质谱法测定土壤有机物原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-026 液相色谱质谱法测定土壤有机物原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "QXSPZPTWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',
        3750, 2, NULL, 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2022-01-05 11:00:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-25 16:35:47', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetYXSPZPT', 0, '', '', NULL, NULL, '',
        1, 26);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('1ae58b97-6f57-49ba-87d2-ad9ef3199e2b', '0618aca9-4a58-417b-b7fb-998802259f80', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-25 15:38:34', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-25 15:38:34', NULL);

-- 电感耦合等离子体质谱法测定土壤元素原始记录表模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('b496130a-4427-47e3-a74c-15a3795a17f6', 1, 'WorkSheetDGOHZPT',
        'GJW-04-2019-YS-TR-018 电感耦合等离子体质谱法测定土壤元素原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-018 电感耦合等离子体质谱法测定土壤元素原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-018 电感耦合等离子体质谱法测定土壤元素原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "DGOHZPTWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',
        3860, 2, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-26 08:41:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-26 08:41:01', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetDGOHZPT', 0, '', NULL, '', '', '', 0,
        NULL);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('27965d87-42ff-4859-b490-1365b1a00d74', 'b496130a-4427-47e3-a74c-15a3795a17f6', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-26 08:41:45', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-26 08:41:45', NULL);

-- 电感耦合等离子体发射光谱法测定土壤元素原始记录模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('aa7f5c6e-e16b-4514-9e89-2b63edddbc3b', 1, 'WorkSheetDGOHGPT',
        'GJW-04-2019-YS-TR-016 电感耦合等离子体发射光谱法测定土壤元素原始记录表.xlsx',
        'WorkSheet/GJW-04-2019-YS-TR-016 电感耦合等离子体发射光谱法测定土壤元素原始记录表.xlsx',
        'output/WorkSheet/GJW-04-2019-YS-TR-016 电感耦合等离子体发射光谱法测定土壤元素原始记录表.xlsx', 'application/excel',
        'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '',
        '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "DGOHZPTWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}',
        3870, 2, '', 0, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-26 08:50:23', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2025-02-26 08:50:23', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetDGOHGPT', 0, '', NULL, '', '', '', 0,
        NULL);

INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('5be40cb8-8a18-4ace-a584-993b09654de6', 'aa7f5c6e-e16b-4514-9e89-2b63edddbc3b', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-26 08:51:21', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-02-26 08:51:21', NULL);
