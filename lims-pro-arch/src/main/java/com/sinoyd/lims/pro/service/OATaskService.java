package com.sinoyd.lims.pro.service;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;

import org.activiti.engine.task.Attachment;
import org.springframework.web.multipart.MultipartFile;

/**
 * 审批任务信息业务操作接口
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
public interface OATaskService extends IBaseJpaService<DtoOATask, String> {
    /**
     * 发起流程(完成发起审批任务同时添加审批任务主信息和办理日志)
     *
     * @param taskType 流程类型
     * @param taskDto  任务参数对象
     * @param vars     流程变量
     * @return 审批任务
     */
    DtoOATask startProcess(EnumOATaskType taskType, DtoOATaskCreate<?> taskDto, Map<String, Object> vars);

    /**
     * 保存为草稿
     *
     * @param taskType 流程类型
     * @param taskDto  任务参数对象
     * @param vars     流程变量
     * @return 审批任务
     */
    DtoOATask saveAsDraft(EnumOATaskType taskType, DtoOATaskCreate<?> taskDto, Map<String, Object> vars);

    /**
     * 草稿保存
     * @param taskDto 数据
     */
    DtoOATask DraftSave(DtoOATaskCreate<?> taskDto);

    /**
     * 草稿提交
     *
     * @param taskDto 任务标识
     * @param vars 流程变量
     * @return 审批任务
     */
    String draftSubmit(DtoOATaskCreate<?> taskDto,Map<String, Object> vars);

    /**
     * 添加审批任务和业务关联
     *
     * @param taskId   审批任务ID
     * @param objectId 业务关联guid
     */
    void addOATaskRelation(String taskId, String objectId);

    /**
     * 根据任务Id查询审批任务信息
     *
     * @param taskId 审批任务ID
     * @return
     */
    DtoOATask findByTaskId(String taskId);

    /**
     * 获取流程所有附件信息
     *
     * @param procInstId
     * @return
     */
    List<DtoOATaskAttach> getOATaskAttachDTO(String procInstId);

    /**
     * 添加附件
     *
     * @param procInstId
     * @param taskId
     * @param files
     */
    void addAttachment(String procInstId, String taskId, List<MultipartFile> files);

    /**
     * 获取附件信息
     *
     * @param attachmentId 附件Id
     * @return InputStream
     */
    InputStream getAttachmentContent(String attachmentId);

    /**
     * 获取原始附件对象信息
     *
     * @param attachmentId 附件Id
     * @return Attachment
     */
    Attachment getAttachment(String attachmentId);

    /**
     * 查询待我审批任务
     *
     * @param page  分页对象
     * @param param 条件对象
     * @return
     */
    void findTodo(PageBean<DtoOATask> page, DtoOATaskQuery param);

    /**
     * 查询我已审批任务
     *
     * @param page  分页对象
     * @param param 条件对象
     * @return
     */
    void findFinish(PageBean<DtoOATask> page, DtoOATaskQuery param);

    /**
     * 查询我发起的任务
     *
     * @param page  分页对象
     * @param param 条件对象
     * @return
     */
    void findSponsor(PageBean<DtoOATask> page, DtoOATaskQuery param);

    /**
     * 查询所有任务
     *
     * @param page  分页对象
     * @param param 条件对象
     * @return
     */
    void findAll(PageBean<DtoOATask> page, DtoOATaskQuery param);

    /**
     * 完成任务
     *
     * @param param 参数对象
     */
    void completeTask(DtoOATaskHandle param);

    /**
     * 撤销任务
     *
     * @param taskId 审批任务ID
     * @param reason 撤销原因
     */
    void cancelTask(String taskId, String reason);


    /**
     * 将工作流中的附件数据同步到Document中
     *
     * @param procInstId   流程实例id
     * @param folderId 文件夹id
     * @param path     路径
     */
    void syncAttachmentToDocument(String procInstId,
                                  String folderId,
                                  String path);

    /**
     * 审批任务附件
     * @param id 主键
     * @return DtoOATask
     */
    DtoOATask findAttachPath(String id);
}
