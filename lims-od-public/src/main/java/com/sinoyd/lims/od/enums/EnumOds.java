package com.sinoyd.lims.od.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * EnumOds(枚举类)
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public class EnumOds {

    /**
     * 嗅辨类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumTaskType {

        固定源(0, 4, "stationarySourceResultStrategySummary"),

        环境空气(1, 6, "envGasResultStrategySummary");

        /**
         * 数据值
         */
        private Integer value;

        /**
         * 人员数量
         */
        private Integer num;

        private String beanName;

        public static String EnumTaskType(Integer value) {
            for (EnumOds.EnumTaskType c : EnumOds.EnumTaskType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

        public static String getBeanName(Integer value) {
            for (EnumOds.EnumTaskType enumTaskType : EnumOds.EnumTaskType.values()) {
                if (enumTaskType.getValue().equals(value)) {
                    return enumTaskType.getBeanName();
                }
            }
            return "";
        }
    }

    /**
     * 嗅辨任务状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumTaskState {

        未完成(0),

        已完成(1);

        private Integer value;

        public static String EnumTaskState(Integer value) {
            for (EnumTaskState c : EnumTaskState.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 嗅辨任务状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOdsampleState {

        未完成(0),

        已完成(1);

        private Integer value;

        public static String EnumOdsampleState(Integer value) {
            for (EnumOdsampleState c : EnumOdsampleState.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }


    /**
     * 嗅辨任务状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumLabState {

        未开始(-1),

        进行中(0),

        已完成(1);

        private Integer value;

        public static String EnumLabState(Integer value) {
            for (EnumLabState c : EnumLabState.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 嗅辨答案评价
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumEvaluation {

        正确(1),

        不明(2),

        错误(3);

        private Integer value;

        public static String EnumEvaluation(Integer value) {
            for (EnumEvaluation c : EnumEvaluation.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

        /**
         * 获取评价特殊标记
         *
         * @param value 评价结果值
         * @return 标记符号字符
         */
        public static String EnumEvaluationMark(Integer value) {
            String mark = "";
            switch (value) {
                case 1:
                    mark = "O";
                    break;
                case 2:
                    mark = "△";
                    break;
                case 3:
                    mark = "×";
            }
            return mark;
        }
    }


    /**
     * 自信度
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumConfidenceLvl {

        猜测(1),

        肯定(2);

        private Integer value;

        public static String EnumConfidenceLvl(Integer value) {
            for (EnumConfidenceLvl c : EnumConfidenceLvl.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }


}
