package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoLogForAnalyzeMethod;

import java.util.List;

/**
 * LogForAnalyzeMethod数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/01/22
 * @since V100R001
 */
public interface LogForAnalyzeMethodRepository extends IBaseJpaPhysicalDeleteRepository<DtoLogForAnalyzeMethod, String> {

    /**
     * 根据方法标识查询
     * @param analyzeMethodId 方法标识
     * @return 日志列表
     */
    List<DtoLogForAnalyzeMethod> findByObjectIdOrderByOperateTimeDesc(String analyzeMethodId);
}
