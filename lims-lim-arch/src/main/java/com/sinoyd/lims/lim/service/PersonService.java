package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoLoginExpand;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoPersonCertQuery;
import com.sinoyd.lims.lim.dto.customer.DtoPersonQuery;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 人员管理
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface PersonService extends IBaseJpaService<DtoPerson, String> {
    /**
     * 开通账户
     *
     * @param personId     人员id
     * @param loginId      用户名
     * @param pwd          密码
     * @param roleIds      角色ids
     * @param loginExpands 用户扩展信息列表
     */
    Boolean openAccount(String personId, String loginId, String pwd, List<String> roleIds, List<DtoLoginExpand> loginExpands);


    /**
     * 找到人员的归档路径
     *
     * @param id 人员id
     * @return 返回相应的归档路径
     */
    DtoPerson findPersonAttachment(String id, String name);


    /**
     * 根据用户Id获取有假删的数据
     *
     * @param ids 人员ids
     * @return 返回带有假删的用户数据
     */
    List<DtoPerson> findAllDeleted(List<String> ids);


    /**
     * 含假删的人员信息
     *
     * @return 返回人员信息
     */
    List<DtoPerson> findAllDeleted();

    /**
     * 返回人员列表
     *
     * @param queryDto 查询dto
     * @return 返回人员对象
     */
    List<DtoKeyValue> query(DtoPersonQuery queryDto);

    /**
     * 根据测试项目查询人员及有证信息
     *
     * @param testId     测试项目id
     * @param permission 权限
     * @return 人员及有证信息
     */
    List<Map<String, Object>> queryWithCert(String testId, String permission);

    /**
     * 根据条件查询人员及证书信息
     *
     * @param personCertQuery 查询条件
     * @return 人员信息
     */
    List<Map<String, Object>> queryWithCert(DtoPersonCertQuery personCertQuery);


    /**
     * 只查询到id，名称，组织名称
     *
     * @param queryDto 查询条件
     * @return 人员数据
     */
    List<Map<String, Object>> queryOnlyName(DtoPersonQuery queryDto);

    /**
     * 获取所有的图片地址
     *
     * @param request 请求
     * @return 返回路径
     */
    List<Map<String, Object>> findAllPhotoUrl(HttpServletRequest request);

    /**
     * 查询人员信息
     *
     * @param id                 人员id
     * @param isOnlyReturnPerson 是否只返回人员信息
     * @return 返回人员信息
     */
    DtoPerson findOne(String id, Boolean isOnlyReturnPerson);


    /**
     * 按id查询人员的姓名
     *
     * @param id 主键id
     * @return 返回数据
     */
    String findPersonNameById(String id);

    /**
     * 拼音转换
     *
     * @return
     */
    List<DtoPerson> changePinYinFull();


    /**
     * 上传人员签名
     *
     * @param request 请求体
     * @return 保存签名后的人员数据
     */
    DtoPerson uploadSignature(HttpServletRequest request);

    /**
     * 删除人员签名路径
     *
     * @param documentIds 人员签名文档id集合
     * @return 删除后的人员数据
     */
    DtoPerson deleteSignature(List<String> documentIds);

    /**
     * 判断当前登录人是否有签名文件
     *
     * @return 返回值
     */
    Boolean haveSignature();


    /**
     * 导出人员详情
     * @param personCriteria  查询条件
     * @param response        响应
     */
    void exportPersonDetails(BaseCriteria personCriteria, HttpServletResponse response);


    /**
     * 上岗证总览
     * @param page            分页参数
     * @param personCriteria  查询参数
     */
    Map<String,Object> certPreview(PageBean<DtoPerson> page, BaseCriteria personCriteria);
}