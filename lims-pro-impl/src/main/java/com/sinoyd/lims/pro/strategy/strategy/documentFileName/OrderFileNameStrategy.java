package com.sinoyd.lims.pro.strategy.strategy.documentFileName;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoOrderForm;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.OrderFormService;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单附件名称生成
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/28
 */
@Component(IFileNameConstant.FileNameStrategyKey.ORDER_FILENAME)
public class OrderFileNameStrategy extends AbsDocumentFileNameStrategy {

    private OrderFormService orderFormService;

    @Override
    public Map<String, String> generateDocumentName(Map<String, Object> map) {
        Map<String, String> orderMap = new HashMap<>();
        if (map.containsKey(EnumPRO.EnumDocumnetName.订单.getValue())) {
            String reportId = map.get(EnumPRO.EnumDocumnetName.订单.getValue()).toString();
            if(StringUtil.isNotEmpty(reportId)) {
                DtoOrderForm orderForm = orderFormService.findOne(reportId);
                if (StringUtil.isNotNull(orderForm)) {
                    orderMap.put("orderCode", orderForm.getOrderCode());
                    orderMap.put("orderName", orderForm.getOrderName());
                }
            }
        }
        return orderMap;
    }

    @Autowired
    @Lazy
    public void setOrderFormService(OrderFormService orderFormService) {
        this.orderFormService = orderFormService;
    }
}
