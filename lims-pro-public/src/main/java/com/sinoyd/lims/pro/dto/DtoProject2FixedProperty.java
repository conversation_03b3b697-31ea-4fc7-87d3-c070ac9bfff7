package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.Project2FixedProperty;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoProject2FixedProperty实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_Project2FixedProperty")
 @Data
 @DynamicInsert
 public  class DtoProject2FixedProperty extends Project2FixedProperty {
   private static final long serialVersionUID = 1L;
 }