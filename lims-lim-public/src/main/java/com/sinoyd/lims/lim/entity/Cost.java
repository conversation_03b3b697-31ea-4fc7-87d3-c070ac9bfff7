package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Cost实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Cost")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Cost implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Cost() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId =  StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 测试项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试项目id")
	private String testId;
    
    /**
    * 分析项目名称
    */
    @Column(length=50)
    @ApiModelProperty("分析项目名称")
    @Length(message = "分析项目名称{validation.message.length}", max = 50)
    private String redAnalyzeItemName;
    
    /**
    * 分析方法名称
    */
    @ApiModelProperty("分析方法名称")
    @Length(message = "分析方法名称{validation.message.length}", max = 255)
    private String redAnalyzeMethodName;
    
    /**
    * 标准编号
    */
    @Column(length=50)
    @ApiModelProperty("标准编号")
    @Length(message = "标准编号{validation.message.length}", max = 50)
    private String redCountryStandard;
    
    /**
    * 检测类型id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;
    
    /**
    * 采样费
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("采样费")
    private BigDecimal samplingCost= new BigDecimal(0);
    
    /**
    * 分析费
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("分析费")
    private BigDecimal analyzeCost= new BigDecimal(0);
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }