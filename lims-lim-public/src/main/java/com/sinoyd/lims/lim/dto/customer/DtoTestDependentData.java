package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 测试项目依赖数据
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/5
 * @since V100R001
 */
@Data
public class DtoTestDependentData {

    /**
     * 测试项目
     */
    private List<DtoExportTest> testList = new ArrayList<>();

    /**
     * 分析方法
     */
    private List<DtoExportAnalyzeMethod> analyzeMethodList = new ArrayList<>();
    /**
     * 分析项目
     */
    private List<DtoExportAnalyzeItem> analyzeItemList = new ArrayList<>();

    /**
     * 量纲
     */
    private List<DtoExportDimension> dimensionList = new ArrayList<>();

    /**
     * 参数
     */
    private List<DtoExportParams> paramsList = new ArrayList<>();

    /**
     * 测试项目拓展
     */
    private List<DtoExportTestExpand> testExpandList = new ArrayList<>();

    /**
     * 质控限值
     */
    private List<DtoExportQualityControlLimit> qualityControlLimitList = new ArrayList<>();

    /**
     * 公式配置
     */
    private List<DtoExportParamsFormula> paramsFormulaList = new ArrayList<>();

    /**
     * 公式参数数据
     */
    private List<DtoExportParamsTestFormula> paramsTestFormulaList = new ArrayList<>();

    /**
     * 测得量公式
     */
    private List<DtoExportParamsPartFormula> paramsPartFormulaList = new ArrayList<>();
}
