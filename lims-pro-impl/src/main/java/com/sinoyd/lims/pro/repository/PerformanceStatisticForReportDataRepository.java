package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForReportData;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * PerformanceStatisticForReportData数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/24
 * @since V100R001
 */
public interface PerformanceStatisticForReportDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoPerformanceStatisticForReportData, String> {


    /**
     * 删除已有的数据
     *
     * @param projectIds    项目idd
     * @param reportMakerId 编制报告人
     * @return 删除已有数据
     */
    @Transactional
    @Modifying
    @Query("delete  from  DtoPerformanceStatisticForReportData where projectId in :projectIds and reportMakerId = :reportMakerId")
    Integer deleteByProjectIdInAndReportMakerId(@Param("projectIds") List<String> projectIds,@Param("reportMakerId") String reportMakerId);
}