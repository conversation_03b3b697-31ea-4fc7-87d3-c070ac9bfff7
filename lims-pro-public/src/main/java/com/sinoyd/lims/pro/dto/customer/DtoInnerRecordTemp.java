package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 内部送样单新增传输结构
 * <AUTHOR>
 * @version V1.0.0 2019/12/10
 * @since V100R001
 */
@Data
public class DtoInnerRecordTemp {
    /**
     * 项目id
     */
    private String projectId;

    /**
     * 送样人id
     */
    private String senderId;

    /**
     * 送样人名称
     */
    @Length(message = "送样人（采样负责人）{validation.message.length}", max = 100)
    private String senderName;

    /**
     * 项目id
     */
    private Date sendTime;

    /**
     * 项目id
     */
    private Date samplingTime;

    /**
     * 备注
     */
    @Length(message = "备注{validation.message.length}", max = 255)
    private String remark;

    /**
     * 采样人
     */
    private List<String> samplingPersonIds = new ArrayList<>();

    /**
     * 采样车辆
     */
    private List<String> carIds = new ArrayList<>();

    /**
     * 样品id数组
     */
    private List<String> sampleIds = new ArrayList<>();
}
