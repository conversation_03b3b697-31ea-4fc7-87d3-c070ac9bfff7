package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoProject2Contract;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * Project2Contract数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface Project2ContractRepository extends IBaseJpaPhysicalDeleteRepository<DtoProject2Contract, String> {

    /** 根据项目id返回项目合同关联
     * @param projectId 项目id
     */
    DtoProject2Contract findByProjectId(String projectId);

    /** 根据合同id返回项目合同关联
     * @param contractId 合同id
     */
    List<DtoProject2Contract> findByContractId(String contractId);

    /** 按照合同id删除合同关联
     * @param contractId 合同id
     */
    Integer deleteByContractId(String contractId);
}