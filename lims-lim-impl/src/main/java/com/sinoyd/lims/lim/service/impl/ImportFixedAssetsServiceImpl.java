package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoFixedProperty;
import com.sinoyd.lims.lim.dto.customer.DtoImportFixedAssets;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.FixedAssetsService;
import com.sinoyd.lims.lim.service.ImportFixedAssetsService;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.FixedAssetsVerifyHandle;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 固定资产导入接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@Service
public class ImportFixedAssetsServiceImpl implements ImportFixedAssetsService {

    @Autowired
    private CodeService codeService;
    @Autowired
    private ImportUtils importUtils;
    @Autowired
    private FixedAssetsVerifyHandle fixedAssetsVerifyHandle;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private EnterpriseRepository enterpriseRepository;
    @Autowired
    private FixedAssetsService fixedAssetsService;
    @Autowired
    private PersonService personService;


    @Override
    public List<DtoFixedProperty> importExcel(MultipartFile file, Map<Integer, Object> map, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);

        // 供应商
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findByIsDeletedFalseAndType(EnumBase.EnumEnterpriseType.供应商.getValue());
        // 获取所有资产类型
        List<DtoCode> codeList = codeService.findCodes("PRO_Assets_Type");
        // 所属科室
        List<DtoDepartment> deptList = departmentService.findAll();
        List<DtoPerson> personList = personService.findAll();
        // 临时数据
        List<DtoImportFixedAssets> fixedAssets = new ArrayList<>();
        fixedAssetsVerifyHandle.getFixedAssetsTl().set(fixedAssets);
        fixedAssetsVerifyHandle.getEnterpriseTl().set(enterpriseList);
        fixedAssetsVerifyHandle.getCodeTl().set(codeList);
        fixedAssetsVerifyHandle.getDeptTl().set(deptList);
        fixedAssetsVerifyHandle.getPersonTl().set(personList);
        //获取导入结果
        ExcelImportResult<DtoImportFixedAssets> result = getExcelData(file, response);
        List<DtoImportFixedAssets> importFixedAssets = result.getList();
        //清除线程变量
        fixedAssetsVerifyHandle.getFixedAssetsTl().remove();
        fixedAssetsVerifyHandle.getEnterpriseTl().remove();
        fixedAssetsVerifyHandle.getCodeTl().remove();
        fixedAssetsVerifyHandle.getDeptTl().remove();
        fixedAssetsVerifyHandle.getPersonTl().remove();
        //删除空行
        importFixedAssets.removeIf(p -> StringUtil.isEmpty(p.getAssetsName()));
        //判断文件中是否存在数据
        if (StringUtil.isEmpty(importFixedAssets)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        // 导入数据转实体
        List<DtoFixedProperty> assets = addDataHandle(importFixedAssets, enterpriseList, personList, deptList);
        addData(assets);

        return fixedAssetsService.findAll();
    }


    @Override
    public void addData(List<DtoFixedProperty> list) {
        fixedAssetsService.insertBatch(list);
    }

    @Override
    public ExcelImportResult<DtoImportFixedAssets> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);

        params.setVerifyHandler(fixedAssetsVerifyHandle);
        ExcelImportResult<DtoImportFixedAssets> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoImportFixedAssets.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "固定资产导入错误信息");
            PoiExcelUtils.downLoadExcel("固定资产导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 导入数据转实体
     *
     * @param importFixedAssets 导入实体
     * @param enterpriseList    供应商
     * @param personList        用户
     * @param deptList          科室
     * @return 固定资产实体
     */
    private List<DtoFixedProperty> addDataHandle(List<DtoImportFixedAssets> importFixedAssets, List<DtoEnterprise> enterpriseList, List<DtoPerson> personList, List<DtoDepartment> deptList) {
        List<DtoFixedProperty> fixedAssetsList = new ArrayList<>();
        for (DtoImportFixedAssets importFixedAsset : importFixedAssets) {
            DtoFixedProperty fixedAssets = new DtoFixedProperty();
            fixedAssets.setPurchasePrice(StringUtil.isNotEmpty(importFixedAsset.getPurchasePrice()) ? Double.parseDouble(importFixedAsset.getPurchasePrice()) : 0.0);
            fixedAssets.importToFixedAssetsEntity(importFixedAsset);
            if (StringUtil.isNotEmpty(importFixedAsset.getSupplier())) {
                DtoEnterprise dtoEnterprise = enterpriseList.stream().filter(p -> importFixedAsset.getSupplier().equals(p.getName())).findFirst().orElse(null);
                fixedAssets.setSupplier(StringUtil.isNotNull(dtoEnterprise) ? dtoEnterprise.getId() : UUIDHelper.GUID_EMPTY);
            }
            DtoDepartment dtoDepartment = deptList.stream().filter(p -> p.getDeptName().equals(importFixedAsset.getDeptId())).findFirst().orElse(null);
            fixedAssets.setDeptId(StringUtil.isNotNull(dtoDepartment) ? dtoDepartment.getId() : UUIDHelper.GUID_EMPTY);
            Optional<DtoPerson> dtoPerson = personList.stream().filter(p -> p.getCName().equals(importFixedAsset.getManager())).findFirst();
            fixedAssets.setManager(dtoPerson.get().getId());
            fixedAssets.setPurchaseDate(StringUtil.isEmpty(importFixedAsset.getPurchaseDate()) ? importUtils.stringToDateAllFormat("1753-01-01") : importUtils.stringToDateAllFormat(importFixedAsset.getPurchaseDate()));
            fixedAssetsList.add(fixedAssets);
        }
        return fixedAssetsList;
    }
}
