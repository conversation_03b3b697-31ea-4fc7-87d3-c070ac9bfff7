package com.sinoyd.lims.foreign.service.impl;

import com.jsoniter.JsonIterator;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataParamsDataVO;
import com.sinoyd.lims.foreign.service.DataParamsDataService;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 参数信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataParamsDataServiceImpl extends AbsQueryDataServiceImpl<DataParamsDataVO> implements DataParamsDataService {


    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select t1.id,t1.objectId,t1.paramsName,t1.paramsValue,");
        sql.append("t2.dataSource,t1.dimension,t1.orgId,t1.domainId ");
        sql.append(" from TB_PRO_ParamsData t1");
        sql.append(" INNER JOIN TB_LIM_ParamsConfig t2 ON t1.paramsConfigId = t2.id");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND (t1.modifyDate > :syncTime or t2.modifyDate > :syncTime)", "syncTime");
        super.appendCondition(sql, map, "AND t1.objectId in (:objectIds) ", "objectIds");
    }

    @Override
    protected RowMapper<DataParamsDataVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataParamsDataVO paramsDataVO = new DataParamsDataVO();
            paramsDataVO.setId(UUIDHelper.NewID());
            paramsDataVO.setObjectId(rs.getString("objectId"));
            paramsDataVO.setParamsConfigName(rs.getString("paramsName"));
            paramsDataVO.setParamsValue(rs.getString("paramsValue"));
            paramsDataVO.setDataSource(rs.getString("dataSource"));
            paramsDataVO.setDimension(rs.getString("dimension"));
            paramsDataVO.setOrgId(rs.getString("orgId"));
            paramsDataVO.setDomainId(rs.getString("domainId"));
            paramsDataVO.setSyncTime(new Date());
            return paramsDataVO;
        };
    }

    @Override
    protected void individuationData(Map<String, Object> map,List<DataParamsDataVO> list) {
        StringBuilder sql2 = new StringBuilder();
        sql2.append("select id,analyseDataId,json,orgId,domainId");
        sql2.append(" from TB_PRO_AnalyseOriginalRecord ");
        sql2.append(" WHERE 1 = 1 ");
        // 分析数据ids
        super.appendCondition(sql2,map,"AND modifyDate > :syncTime ","syncTime");
        super.appendCondition(sql2,map,"AND analyseDataId in (:analyseDataIds) ","analyseDataIds");

        List<Map<String, Object>> analyseOriginalRecordList = namedParameterJdbcTemplate.queryForList(sql2.toString(), map);
        for (Map<String, Object> analyseOriginalRecord : analyseOriginalRecordList) {
            List<Map<String,Object>> jsonList = JsonIterator.deserialize(analyseOriginalRecord.get("json").toString(), List.class);
            for (Map<String, Object> json : jsonList) {
                DataParamsDataVO paramsDataVO = new DataParamsDataVO();
                paramsDataVO.setId(UUIDHelper.NewID());
                paramsDataVO.setParamsConfigName(json.get("alias").toString());
                paramsDataVO.setParamsValue(json.get("defaultValue").toString());
                paramsDataVO.setObjectId(analyseOriginalRecord.get("analyseDataId").toString());
                paramsDataVO.setOrgId(analyseOriginalRecord.get("orgId").toString());
                paramsDataVO.setDomainId(analyseOriginalRecord.get("domainId").toString());
                paramsDataVO.setSyncTime(new Date());
                list.add(paramsDataVO);
            }
        }
    }

}
