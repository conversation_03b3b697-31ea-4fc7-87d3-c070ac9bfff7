package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * 数据信息
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Data
public class DataAnalyseDataVO {

    /**
     * id
     */
    private String id;

    /**
     * 数据id
     */
    private String analyseDataId;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 小工作单id
     */
    private String workSheetId;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;

    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 国标名称
     */
    private String redCountryStandard;

    /**
     * 年度
     */
    private String yearSn;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 分析项目id
     */
    private String analyseItemId;

    /**
     * 质控id
     */
    private String qcId;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 质控类别
     */
    private Integer qcGrade;

    /**
     * 有效位数
     */
    private Integer mostSignificance;

    /**
     * 小数位数
     */
    private Integer mostDecimal;

    /**
     * 检出限
     */
    private String examLimitValue;

    /**
     * 量纲
     */
    private String dimension;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 检测结果
     */
    private String testOrignValue;

    /**
     * 检测结果修约值
     */
    private String testValueDstr;

    /**
     * 分析人员
     */
    private String analystName;

    /**
     * 数据分析时间
     */
    private Date analyzeTime;

    /**
     * 分析完成时间
     */
    private Date finishTime;

    /**
     * 有效性
     */
    private Boolean isDataEnabled;

    /**
     * 是否在现场完成
     */
    private Boolean isCompleteField;

    /**
     * 是否分析分包
     */
    private Boolean isOutsourcing;

    /**
     * 是否采样分包
     */
    private Boolean isSamplingOut;

    /**
     * 采集编号
     */
    private String gatherCode;

    /**
     * 质控信息
     */
    private String qcInfo;

    /**
     * 串联中间结果
     */
    private String seriesValue;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;

//    public DataAnalyseDataVO(){}
//
//    public DataAnalyseDataVO(String id, String analyseDataId, String workSheetFolderId,
//                             String sampleId, String testId, String workSheetId,
//                             String redAnalyzeItemName, String redAnalyzeMethodName,
//                             String redCountryStandard, String yearSn, String analyzeMethodId,
//                             String analyseItemId, String qcId, Integer qcType, Integer qcGrade,
//                             Integer mostSignificance, Integer mostDecimal, String examLimitValue,
//                             String dimension, String testValue, String testOrignValue,
//                             String testValueDstr, String analystName, Date analyzeTime,
//                             Date finishTime, Boolean isDataEnabled, Boolean isCompleteField,
//                             Boolean isOutsourcing, Boolean isSamplingOut, String gatherCode,
//                             String qcInfo, String seriesValue, String orgId, String domainId, Date syncTime) {
//        this.id = id;
//        this.analyseDataId = analyseDataId;
//        this.workSheetFolderId = workSheetFolderId;
//        this.sampleId = sampleId;
//        this.testId = testId;
//        this.workSheetId = workSheetId;
//        this.redAnalyzeItemName = redAnalyzeItemName;
//        this.redAnalyzeMethodName = redAnalyzeMethodName;
//        this.redCountryStandard = redCountryStandard;
//        this.yearSn = yearSn;
//        this.analyzeMethodId = analyzeMethodId;
//        this.analyseItemId = analyseItemId;
//        this.qcId = qcId;
//        this.qcType = qcType;
//        this.qcGrade = qcGrade;
//        this.mostSignificance = mostSignificance;
//        this.mostDecimal = mostDecimal;
//        this.examLimitValue = examLimitValue;
//        this.dimension = dimension;
//        this.testValue = testValue;
//        this.testOrignValue = testOrignValue;
//        this.testValueDstr = testValueDstr;
//        this.analystName = analystName;
//        this.analyzeTime = analyzeTime;
//        this.finishTime = finishTime;
//        this.isDataEnabled = isDataEnabled;
//        this.isCompleteField = isCompleteField;
//        this.isOutsourcing = isOutsourcing;
//        this.isSamplingOut = isSamplingOut;
//        this.gatherCode = gatherCode;
//        this.qcInfo = qcInfo;
//        this.seriesValue = seriesValue;
//        this.orgId = orgId;
//        this.domainId = domainId;
//        this.syncTime = syncTime;
//    }
}
