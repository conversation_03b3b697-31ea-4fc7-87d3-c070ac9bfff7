package com.sinoyd.lims.pro.service;


import com.sinoyd.frame.entity.CurrentPrincipalUser;

/**
 * 送样单缓存数据的接口
 * <AUTHOR>
 * @version V1.0.0 2020/03/06
 * @since V100R001
 */
public interface ReceiveSampleRecordCacheService {

    /**
     * 送样单json字段更新
     *
     * @param receiveId            送样单id
     * @param principalContextUser 当前人员信息
     */
    void updateRecordJson(String receiveId, CurrentPrincipalUser principalContextUser);
}
