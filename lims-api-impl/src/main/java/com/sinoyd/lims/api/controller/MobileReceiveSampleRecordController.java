package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.ReceiveSampleRecordCriteria;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.customer.DtoRecordSubmitTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ReceiveSampleRecord服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/9/1
 * @since V100R001
 */
@Api(tags = "示例: ReceiveSampleRecord服务")
@RestController
@RequestMapping("api/mobile/receiveSampleRecord")
public class MobileReceiveSampleRecordController extends BaseJpaController<DtoReceiveSampleRecord, String, ReceiveSampleRecordService> {
    /**
     * 分页动态条件查询送样单
     *
     * @param receiveSampleRecordCriteria 条件参数
     * @return RestResponse<List < ReceiveSampleRecord>>
     */
    @ApiOperation(value = "分页动态条件查询送样单", notes = "分页动态条件查询送样单")
    @GetMapping
    public RestResponse<List<DtoReceiveSampleRecord>> findByPage(ReceiveSampleRecordCriteria receiveSampleRecordCriteria) {
        PageBean<DtoReceiveSampleRecord> pageBean = super.getPageBean();
        RestResponse<List<DtoReceiveSampleRecord>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, receiveSampleRecordCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询送样单
     *
     * @param id 主键id
     * @return RestResponse<DtoReceiveSampleRecord>
     */
    @ApiOperation(value = "按主键查询送样单", notes = "按主键查询送样单")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoReceiveSampleRecord> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoReceiveSampleRecord> restResponse = new RestResponse<>();
        DtoReceiveSampleRecord receiveSampleRecord = service.findOne(id);
        restResponse.setData(receiveSampleRecord);
        restResponse.setRestStatus(StringUtil.isNull(receiveSampleRecord) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 提交送样单
     *
     * @param dto
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "提交送样单", notes = "提交送样单")
    @PostMapping(path = "/submit")
    public RestResponse<List<String>> submit(@RequestBody DtoRecordSubmitTemp dto) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        try {
            if(EnumPRO.EnumReceiveSubmitType.样品交接提交.getCode().equals(dto.getType())){
                service.canSubmitReceiveRecord(dto.getIds(), dto.getType());
            }
            restResponse.setData(service.submitReceiveRecord(dto.getIds(), dto.getType(), dto.getNextPersonId(), dto.getNextPerson(), dto.getOpinion(),
                    dto.getSubmitTime(), dto.getRecipientId()));
        } catch (Exception ex) {
            throw new BaseException(ex.getMessage());
        }
        return restResponse;
    }

    /**
     * 复核送样单
     *
     * @param dto    传输实体
     * @param isPass 是否通过
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "复核送样单", notes = "复核送样单")
    @PostMapping(path = "/check/{isPass}")
    public RestResponse<List<String>> check(@RequestBody DtoRecordSubmitTemp dto, @PathVariable(name = "isPass") Boolean isPass) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        restResponse.setData(service.checkRecords(dto.getIds(), isPass, dto.getOpinion()));
        return restResponse;
    }

    /**
     * 审核送样单
     *
     * @param dto    传输实体
     * @param isPass 是否通过
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "审核送样单", notes = "审核送样单")
    @PostMapping(path = "/audit/{isPass}")
    public RestResponse<List<String>> audit(@RequestBody DtoRecordSubmitTemp dto, @PathVariable(name = "isPass") Boolean isPass) {
        RestResponse<List<String>> restResponse = new RestResponse<>();
        restResponse.setData(service.auditRecords(dto.getIds(), isPass, dto.getOpinion(), dto.getType(), dto.getIsReport()));
        return restResponse;
    }
}
