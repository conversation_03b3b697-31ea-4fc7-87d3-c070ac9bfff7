package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.entity.TestPost;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoTestPost实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_TestPost")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoTestPost extends TestPost {

    /**
     * 岗位配置的人员列表
     */
    @Transient
    private List<DtoPerson> personList;

    /**
     * 岗位配置的人员id列表
     */
    @Transient
    private List<String> personIdList;

    /**
     * 岗位配置的测试项目列表
     */
    @Transient
    private List<DtoTest> testList;

    /**
     * 岗位配置的测试项目id列表
     */
    @Transient
    private List<String> testIdList;
}