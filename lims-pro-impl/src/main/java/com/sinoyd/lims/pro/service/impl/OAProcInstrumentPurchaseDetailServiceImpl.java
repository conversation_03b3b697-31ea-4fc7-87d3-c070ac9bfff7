package com.sinoyd.lims.pro.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentPurchaseDetail;
import com.sinoyd.lims.lim.service.OAInstrumentPurchaseDetailService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.service.OAProcInstrumentPurchaseDetailService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 仪器采购业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
@Service
public class OAProcInstrumentPurchaseDetailServiceImpl implements OAProcInstrumentPurchaseDetailService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    /**
     * 仪器采购服务
     */
    @Autowired
    @Lazy
    private OAInstrumentPurchaseDetailService instrumentPurchaseDetailService;

    @Override
    public String startProcess(DtoOATaskCreate<List<DtoOAInstrumentPurchaseDetail>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.仪器采购, taskDto, vars);

        List<DtoOAInstrumentPurchaseDetail> data = taskDto.getData();

        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        DtoOATaskRelation taskRelation = null;

        for (DtoOAInstrumentPurchaseDetail purchase : data) {
            purchase.setId(UUIDHelper.NewID());
            // 设置剩余数量为计划数量
            purchase.setSurplusNum(purchase.getPlanNum());

            taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(purchase.getId());

            taskRelations.add(taskRelation);
        }

        // 添加采购记录
        instrumentPurchaseDetailService.save(data);

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<List<DtoOAInstrumentPurchaseDetail>, String> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<List<DtoOAInstrumentPurchaseDetail>, String> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        List<DtoOATaskRelation> relations = oaTaskRelationService.findListByTaskId(taskId);

        // 采购信息
        List<DtoOAInstrumentPurchaseDetail> purchases = new ArrayList<>();

        for (DtoOATaskRelation relation : relations) {
            DtoOAInstrumentPurchaseDetail purchase = instrumentPurchaseDetailService.findOne(relation.getObjectId());
            purchases.add(purchase);
        }

        // 设置采购信息
        taskDetail.setDetail(purchases);

        return taskDetail;
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<List<DtoOAInstrumentPurchaseDetail>> taskDto) {
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }

    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<List<DtoOAInstrumentPurchaseDetail>> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.仪器采购, taskDto, vars);
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<List<DtoOAInstrumentPurchaseDetail>> taskDto,DtoOATask oaTask){
        List<DtoOAInstrumentPurchaseDetail> data = taskDto.getData();
        List<DtoOATaskRelation> taskRelations = new ArrayList<>();
        DtoOATaskRelation taskRelation = null;
        for (DtoOAInstrumentPurchaseDetail purchase : data) {
            // 设置剩余数量为计划数量
            purchase.setSurplusNum(purchase.getPlanNum());
            taskRelation = new DtoOATaskRelation();
            taskRelation.setTaskId(oaTask.getId());
            taskRelation.setObjectId(purchase.getId());
            taskRelations.add(taskRelation);
        }
        // 添加采购记录
        instrumentPurchaseDetailService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelations);
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<List<DtoOAInstrumentPurchaseDetail>> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }
}