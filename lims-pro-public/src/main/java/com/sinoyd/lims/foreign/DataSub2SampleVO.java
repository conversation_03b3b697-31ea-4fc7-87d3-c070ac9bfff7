package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
* 领样单和样品关系
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataSub2SampleVO {

    /**
     * id
     */
    private String id;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 领样单id
     */
    private String subId;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
