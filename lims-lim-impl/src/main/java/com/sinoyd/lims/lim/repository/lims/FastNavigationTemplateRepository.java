package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoFastNavigationTemplate;


/**
 * FastNavigationTemplate数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/3/27
 * @since V100R001
 */
public interface FastNavigationTemplateRepository extends IBaseJpaPhysicalDeleteRepository<DtoFastNavigationTemplate, String> {


    /**
     * 删除当前用户的快速导航信息
     *
     * @param userId 用户ID
     * @return 删除数据
     */
    Integer deleteByUserId(String userId);
}