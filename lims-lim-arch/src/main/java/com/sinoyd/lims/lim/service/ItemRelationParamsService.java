package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelationParams;

import java.util.Collection;
import java.util.List;


/**
 * ItemRelationParams操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
public interface ItemRelationParamsService extends IBaseJpaService<DtoItemRelationParams, String> {

    /**
     * 根据分析项目关系ids查询分析项目
     * @param ids 分析项目关系ids
     * @return 分析项目关系详情
     */
    List<DtoItemRelationParams> findByRelationIds(Collection<?> ids);

    /**
     * 新增分析项目关系详情
     * @param relationId 关系id
     * @param itemIds 分析项目
     */
    void create(String relationId, List<String> itemIds);

    /**
     * 根据分析项目获取关系详情
     * @param ids 分析项目ids
     * @return 分析项目详情
     */
    List<DtoItemRelationParams> findByItemIds(Collection<?> ids);
}