package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.dto.customer.DtoSigDocument;
import com.sinoyd.lims.pro.service.LuckySheetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * luckysheet相关操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/07/29
 * @since V100R001
 */
@Api(tags = "luckysheet相关操作接口")
@RestController
@RequestMapping("api/pro/luckySheet")
public class LuckySheetController extends ExceptionHandlerController<LuckySheetService> {

    @ApiOperation(value = "luckysheet同步后端excel插入签名", notes = "luckysheet同步后端excel插入签名")
    @PostMapping("/insertSig/{documentId}")
    public RestResponse<String> insertSig2Excel(@PathVariable String documentId, @RequestBody List<DtoSigDocument> sigDocumentList){
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setData(service.insertSig2Excel(documentId,sigDocumentList));
        return restResponse;
    }

}
