package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.dto.lims.DtoFileGrantRecovery;
import com.sinoyd.lims.lim.entity.FileGrantRecovery;
import com.sinoyd.lims.lim.repository.lims.FileControlApplyDetailRepository;
import com.sinoyd.lims.lim.repository.lims.FileGrantRecoveryRepository;
import com.sinoyd.lims.lim.service.FileControlApplyDetailService;
import com.sinoyd.lims.lim.service.FileGrantRecoveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 文件发放与文件回收
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
@Service
public class FileGrantRecoveryServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFileGrantRecovery, String, FileGrantRecoveryRepository> implements FileGrantRecoveryService {

    @Autowired
    private CodeService codeService;

    @Autowired
    @Lazy
    private FileControlApplyDetailService fileControlApplyDetailService;

    @Autowired
    private FileControlApplyDetailRepository fileControlApplyDetailRepository;

    /**
     * 新增
     */
    @Override
    @Transactional
    public DtoFileGrantRecovery save(DtoFileGrantRecovery entity) {
        //TODO:流水号需要修改成工厂模式
        List<DtoFileGrantRecovery> fileList = repository.getList();
        String code = "";
        if (fileList.size() > 0) {
            DtoFileGrantRecovery file = fileList.get(0);
            code = file.getCode();
            Date dd = new Date();
            String str = DateUtil.dateToString(dd, "yyyyMMdd");
            int index = code.indexOf(str);
            if (index < 0) {
                code = "FF" + str + "001";
            } else {
                int number = Integer.parseInt(code.split(str)[1].toString());
                number = number + 1;
                if (number < 10) {
                    code = "FF" + str + "00" + number;
                } else if (number > 10 && number < 100) {
                    code = "FF" + str + "0" + number;
                }
            }
        } else {
            Date dd = new Date();
            String str = DateUtil.dateToString(dd, "yyyyMMdd");
            code = "FF" + str + "001";
        }
        entity.setCode(code);
        DtoFileGrantRecovery file = super.save(entity);

        if (StringUtils.isNotNullAndEmpty(entity.getFileId())
                && !entity.getFileId().equals(UUIDHelper.GUID_EMPTY)) {
            DtoFileControlApplyDetail detail = fileControlApplyDetailService.findOne(entity.getFileId());
            detail.setGrantId(file.getId());
            fileControlApplyDetailService.update(detail);
        }

        return file;
    }

    /**
     * 修改发放回收信息
     */
    @Transactional
    @Override
    public DtoFileGrantRecovery update(DtoFileGrantRecovery fileGrantRecovery) {

        List<FileGrantRecovery> finds = super.findByProperty(FileGrantRecovery.class, "code", fileGrantRecovery.getCode());

        if (StringUtil.isNotEmpty(finds) && !finds.get(0).getId().equals(fileGrantRecovery.getId())) {
            throw new BaseException("已存在相同的发放编号！");
        }

        if (StringUtils.isNotNullAndEmpty(fileGrantRecovery.getFileId())
                && !fileGrantRecovery.getFileId().equals(UUIDHelper.GUID_EMPTY)) {
            DtoFileControlApplyDetail detail = fileControlApplyDetailService.findOne(fileGrantRecovery.getFileId());
            if (!fileGrantRecovery.getId().equals(detail.getGrantId())) {

                //先将原来文件的grantId重置为0
                List<DtoFileControlApplyDetail> list = fileControlApplyDetailRepository.getListByGrantId(fileGrantRecovery.getId());
                if (list.size() > 0) {
                    for (DtoFileControlApplyDetail old :
                            list) {
                        old.setGrantId(UUIDHelper.GUID_EMPTY);
                        fileControlApplyDetailService.update(old);
                    }
                }

                //更新最新的GrantId中
                detail.setGrantId(fileGrantRecovery.getId());
                fileControlApplyDetailService.update(detail);
            }
        }

        return super.update(fileGrantRecovery);
    }

    /**
     * 单个真删
     */
    @Transactional
    @Override
    public void delete(String id) {

        //先关联文件TB_LIM_FileControlApplyDetail的grantId重置为0
        List<DtoFileControlApplyDetail> list = fileControlApplyDetailRepository.getListByGrantId(id);
        if (list.size() > 0) {
            for (DtoFileControlApplyDetail old :
                    list) {
                old.setGrantId(UUIDHelper.GUID_EMPTY);
                fileControlApplyDetailService.update(old);
            }
        }

        super.delete(id);
    }

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoFileGrantRecovery> pageBean, BaseCriteria criteria) {
        // 多表关联查询返回自定义字段
        pageBean.setEntityName("DtoFileGrantRecovery fileGrantRecovery, DtoFileControlApplyDetail fileControlApplyDetail");
        pageBean.setSelect("select fileGrantRecovery, " +
                "fileControlApplyDetail.fileName as fileName, " +
                "fileControlApplyDetail.fileCode as fileCode, " +
                "fileControlApplyDetail.controlCode as controlCode, " +
                "fileControlApplyDetail.version as version, " +
                "fileControlApplyDetail.fileType as fileType, " +
                "fileControlApplyDetail.maker as maker, " +
                "fileControlApplyDetail.compileTime as compileTime, " +
                "fileControlApplyDetail.status as status, " +
                "fileControlApplyDetail.id as fileId ");

        super.findByPage(pageBean, criteria);

        List<DtoFileGrantRecovery> datas = pageBean.getData();
        List<DtoFileGrantRecovery> newDatas = new ArrayList<>();

        Iterator<DtoFileGrantRecovery> item = datas.iterator();

        List<DtoCode> fileTypeList = codeService.findCodes("LIM_FileType");
        // 循环迭代获取JPQL中查询返回的属性
        while (item.hasNext()) {
            Object obj = item.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoFileGrantRecovery dto = (DtoFileGrantRecovery) objs[0];
            dto.setFileName((String) objs[1]);
            dto.setFileCode((String) objs[2]);
            dto.setControlCode((String) objs[3]);
            dto.setVersion((String) objs[4]);
            dto.setFileType((String) objs[5]);
            dto.setMaker((String) objs[6]);
            dto.setCompileTime((Date) objs[7]);
            dto.setStatus((Integer) objs[8]);
            dto.setFileId((String) objs[9]);
            String fileTypeName = "";
            if (StringUtils.isNotNullAndEmpty(dto.getFileType())) {
                Optional<DtoCode> re = fileTypeList.stream().filter(p -> p.getId().equals(dto.getFileType())).findFirst();
                if (re.isPresent()) {
                    DtoCode type = re.get();
                    fileTypeName = type.getDictName();//文件类型
                }
            }
            dto.setFileTypeName(fileTypeName);
            newDatas.add(dto);
        }

        pageBean.setData(newDatas);

    }

}