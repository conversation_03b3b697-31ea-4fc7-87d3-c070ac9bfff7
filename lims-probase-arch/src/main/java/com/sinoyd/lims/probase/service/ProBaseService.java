package com.sinoyd.lims.probase.service;


import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;

import java.util.List;

public interface ProBaseService {
    /**
     * 核对样品状态
     *
     * @param ids 样品id列表
     */
    void checkSample(List<String> ids);

    /**
     * 核对送样单状态
     *
     * @param ids 送样单id列表
     */
    void checkReceiveSampleRecord(List<String> ids);

    /**
     * 核对项目状态
     *
     * @param ids 项目id列表
     */
    void checkProject(List<String> ids);

    
    /**
     * 核对样品状态
     *
     * @param samples 样品信息
     */
    void checkSamples(List<DtoSample> samples);

    /**
     * 核对样品状态
     *
     * @param samples 样品信息
     * @param recordList 送样单
     */
    void checkSample(List<DtoSample> samples, List<DtoReceiveSampleRecord> recordList);

    /**
     * 核对样品状态
     *
     * @param samples 样品信息
     * @param project 已经知道哪个项目了
     */
    void checkSample(List<DtoSample> samples, DtoProject project);

    /**
     * 批量核对送样单
     *
     * @param recList 送样单信息
     */
    void checkReceiveSampleRecords(List<DtoReceiveSampleRecord> recList);
}
