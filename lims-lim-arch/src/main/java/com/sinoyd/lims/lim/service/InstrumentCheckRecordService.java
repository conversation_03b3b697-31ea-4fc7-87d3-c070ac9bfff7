package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentCheckRecord;

import java.util.List;

/**
 * 仪器检定校准配置
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface InstrumentCheckRecordService extends IBaseJpaService<DtoInstrumentCheckRecord, String> {
    /**
     * 根据检测器ids查询是否存在检定校准记录
     *
     * @param instrument2DetectorIds 检测器ids
     * @return 是否存在检定校准记录
     */
    Boolean checkRecordByDetectorIds(List<String> instrument2DetectorIds);
}