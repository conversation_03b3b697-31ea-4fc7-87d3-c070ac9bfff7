package com.sinoyd.lims.monitor.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * FixedPointProperty查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FixedPointPropertyCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    private String id;

    /**
     * 年度
     */
    private Integer year;

    /**
     * 关键字
     */
    private String key;

    /**
     * 需要排除的监测计划id列表
     */
    private List<String> excludeIds;

    /**
     * 只显示子监测计划标记，1：只显示子监测计划
     */
    private Integer childIndicator;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        if(year != null){
            condition.append(" and year = :year");
            values.put("year", this.year);
        }
        if(StringUtil.isNotEmpty(key)){
            condition.append(" and propertyName like :propertyName");
            values.put("propertyName", "%" + this.key + "%");
        }
        if(StringUtil.isNotEmpty(excludeIds)){
            condition.append(" and id not in :excludeIds");
            values.put("excludeIds", excludeIds);
        }

        if(childIndicator != null && childIndicator == 1){
            condition.append(" and parentId <> '00000000-0000-0000-0000-000000000000' ");
        }

        return condition.toString();
    }
}