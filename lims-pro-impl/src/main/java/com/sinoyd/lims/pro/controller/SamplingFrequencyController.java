package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.SamplingFrequencyService;
import com.sinoyd.lims.pro.criteria.SamplingFrequencyCriteria;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * SamplingFrequency服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: SamplingFrequency服务")
 @RestController
 @RequestMapping("api/pro/samplingFrequency")
 public class SamplingFrequencyController extends BaseJpaController<DtoSamplingFrequency, String,SamplingFrequencyService> {

 }