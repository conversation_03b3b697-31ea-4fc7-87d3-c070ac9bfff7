package com.sinoyd.lims.api.dto.vo.offLine;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：分组关联项目表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineSampleGroupTestVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 分组id
     */
    private String sampleGroupId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 分析项目id
     */
    private String analyzeItemId;

    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    private String redCountryStandard;
}
