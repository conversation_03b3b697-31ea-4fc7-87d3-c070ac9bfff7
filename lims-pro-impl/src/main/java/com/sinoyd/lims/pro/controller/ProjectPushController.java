package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.criteria.PersonCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.criteria.ProjectPutCriteria;
import com.sinoyd.lims.pro.dto.customer.DtoProjectPut;
import com.sinoyd.lims.pro.service.ProjectPushService;
import com.sinoyd.lims.pro.service.ProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 上海环境院项目推送模块服务接口定义（由上海环境院项目迁移而来）
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/11
 * @since V100R001
 */
@Api(tags = "示例: 上海环境院项目推送模块服务接口定义")
@RestController
@RequestMapping("/api/pro/aboutPush/v1")
public class ProjectPushController extends ExceptionHandlerController<ProjectPushService> {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private EnterpriseService enterpriseService;

    @Autowired
    private PersonService personService;

    @ApiOperation(value = "项目推送列表", notes = "项目推送列表")
    @GetMapping("/projectPushList")
    public RestResponse<List<DtoProjectPut>> find(ProjectPutCriteria criteria){
        PageBean<DtoProjectPut> pageBean = super.getPageBean();
        RestResponse<List<DtoProjectPut>> response = new RestResponse<>();
        service.findByCondition(pageBean,criteria);
        response.setData(pageBean.getData());
        response.setCount(pageBean.getRowsCount());
        return response;
    }

    @ApiOperation(value = "获取数据", notes = "获取数据")
    @GetMapping
    public RestResponse<List<Map<String, Object>>> query(@RequestParam Map<String,String> req) {
        RestResponse<List<Map<String, Object>>> response = new RestResponse<>();
        List<Map<String, Object>> data = service.queryByMethodName(req,response);
        response.setData(data);
        return response;
    }

    @ApiOperation(value = "推送合同", notes = "推送合同")
    @PostMapping("/pushContract")
    public RestResponse<String> pushContract(@RequestBody String id){
        RestResponse<String> response = new RestResponse<>();
        response.setData(service.pushContract(id));
        return response;
    }

    @ApiOperation(value = "推送项目", notes = "推送项目")
    @PostMapping("/pushProject")
    public RestResponse<String> pushProject(@RequestBody DtoProjectPut projectPut){
        RestResponse<String> response = new RestResponse<>();
        response.setData(service.pushProject(projectPut));
        return response;
    }

    @ApiOperation(value = "推送方案", notes = "推送方案")
    @PostMapping("/pushScheme")
    public RestResponse<String> pushScheme(@RequestBody DtoProjectPut projectPut){
        RestResponse<String> response = new RestResponse<>();
        response.setData(service.pushFolder(projectPut));
        return response;
    }

    @ApiOperation(value = "获取采样计划列表", notes = "获取采样计划列表")
    @GetMapping("/getSamplingPlan")
    public RestResponse<List<Map<String,String>>> getSamplingPlan(@RequestParam String pId){
        RestResponse<List<Map<String,String>>> response = new RestResponse<>();
        response.setData(service.getSamplingPlan(pId));
        return response;
    }

    @ApiOperation(value = "批量修改采样时间", notes = "批量修改采样时间")
    @PostMapping("/updateSamplingTime")
    public RestResponse<String> updateSamplingTime(@RequestBody DtoProjectPut projectPut){
        RestResponse<String> response = new RestResponse<>();
        response.setData(service.updateSamplingTime(projectPut));
        return response;
    }

    @ApiOperation(value = "推送计划", notes = "推送计划")
    @PostMapping("/pushPlan")
    public RestResponse<Void> updateSamplingPerson(@RequestBody DtoProjectPut projectPut){
        RestResponse<Void> response = new RestResponse<>();
        service.pushPlan(projectPut);
        return response;
    }

    @ApiOperation(value = "方法匹配", notes = "方法匹配")
    @PostMapping("/methodMatch")
    public RestResponse<Void> methodMatch(@RequestBody DtoProjectPut projectPut){
        RestResponse<Void> response = new RestResponse<>();
        service.methodMatch(projectPut);
        return response;
    }

    @ApiOperation(value = "获取报告列表", notes = "获取报告列表")
    @GetMapping("/findReport")
    public RestResponse<List<DtoDocument>> findReport(@RequestParam String projectId){
        RestResponse<List<DtoDocument>> response = new RestResponse<>();
        response.setData(service.findReport(projectId));
        return response;
    }

    @ApiOperation(value = "推送报告", notes = "推送报告")
    @PostMapping("/pushReport")
    public RestResponse<String> pushReport(@RequestBody DtoProjectPut projectPut){
        RestResponse<String> response = new RestResponse<>();
        response.setData(service.pushReport(projectPut));
        return response;
    }

    @ApiOperation(value = "数据报告生成系统编号", notes = "数据报告生成系统编号")
    @GetMapping("/getReportNum")
    public RestResponse<String> getReportNum(@RequestParam String projectId){
        RestResponse<String> response = new RestResponse<>();
        response.setData(service.getReportNum(projectId));
        return response;
    }

    @ApiOperation(value = "更新人员拓展信息", notes = "更新人员拓展信息")
    @PostMapping("/updatePersonExpand")
    public RestResponse<DtoPerson> pushReport(@RequestBody DtoPerson person){
        RestResponse<DtoPerson> response = new RestResponse<>();
        response.setData(service.updatePersonExpand(person));
        return response;
    }

    @ApiOperation(value = "更新仪器拓展信息", notes = "更新仪器拓展信息")
    @PostMapping("/updateInstrumentExpand")
    public RestResponse<DtoInstrument> pushReport(@RequestBody DtoInstrument expand){
        RestResponse<DtoInstrument> response = new RestResponse<>();
        response.setData(service.updateInstrumentExpand(expand));
        return response;
    }

    /**
     * 根据甲方企业名称查询最新关联的监管平台企业
     *
     * @param entId 甲方企业id
     * @return RestResponse<Map<String,String>>
     */
    @ApiOperation(value = "查询关联的监管平台企业", notes = "查询关联的监管平台企业")
    @GetMapping("/getEntName")
    public RestResponse<Map<String,String>> getEnt(String entId){
        RestResponse<Map<String,String>> response = new RestResponse<>();
        response.setData(enterpriseService.findEntName(entId));
        return response;
    }

    @ApiOperation(value = "查询合同最新关联的推送信息", notes = "查询合同最新关联的推送信息")
    @GetMapping("/getProjectContract")
    public RestResponse<Map<String,Object>> getProjectContract(String contractId){
        RestResponse<Map<String,Object>> response = new RestResponse<>();
        response.setData(projectService.findProjectExpandByContractId(contractId));
        return response;
    }

    @GetMapping("/exportPersonDetails")
    public RestResponse<String> exportPersonDetails(PersonCriteria criteria, HttpServletResponse response) {
        RestResponse<String> restResponse = new RestResponse<>();
        personService.exportPersonDetails(criteria, response);
        return restResponse;
    }

}
