package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataAnalyseDataVO;
import com.sinoyd.lims.foreign.service.DataAnalyseDataService;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 数据信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataAnalyseDataServiceImpl extends AbsQueryDataServiceImpl<DataAnalyseDataVO> implements DataAnalyseDataService {

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT a.id, a.workSheetFolderId, a.sampleId," +
                "a.testId, a.workSheetId, a.redAnalyzeItemName, a.redAnalyzeMethodName, a.redCountryStandard," +
                "a.analyzeMethodId, a.analyseItemId, a.qcId, a.qcType, a.qcGrade,a.mostSignificance," +
                "a.mostDecimal,a.examLimitValue,a.dimension,a.testValue,a.testOrignValue,a.testValueDstr,a.analystName," +
                "a.analyzeTime,a.finishTime,a.isDataEnabled,a.isCompleteField,a.isOutsourcing,a.isSamplingOut,a.gatherCode," +
                "a.qcInfo,a.seriesValue,a.orgId,a.domainId,m.yearSn");
        sql.append(" FROM TB_PRO_AnalyseData a INNER JOIN TB_LIM_AnalyzeMethod m on a.analyzeMethodId = m.id ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }


    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND a.modifyDate > :syncTime ", "syncTime");
        super.appendCondition(sql, map, "AND a.workSheetFolderId in (:workSheetFolderIds)", "workSheetFolderIds");
        super.appendCondition(sql, map, "AND a.sampleId in (:sampleIds)", "sampleIds");
        super.appendCondition(sql, map, "AND a.id in (:analyseDataIds)", "analyseDataIds");
        super.appendCondition(sql, map, "AND a.workSheetId in (:workSheetIds) ", "workSheetIds");
    }

    @Override
    protected RowMapper<DataAnalyseDataVO> getEntityRowMapper() {
        return (rs, rowNum) -> {
            DataAnalyseDataVO dataAnalyseDataVO = new DataAnalyseDataVO();
            dataAnalyseDataVO.setId(UUIDHelper.NewID());
            dataAnalyseDataVO.setAnalyseDataId(rs.getString("id"));
            dataAnalyseDataVO.setWorkSheetFolderId(rs.getString("workSheetFolderId"));
            dataAnalyseDataVO.setSampleId(rs.getString("sampleId"));
            dataAnalyseDataVO.setTestId(rs.getString("testId"));
            dataAnalyseDataVO.setWorkSheetId(rs.getString("workSheetId"));
            dataAnalyseDataVO.setRedAnalyzeItemName(rs.getString("redAnalyzeItemName"));
            dataAnalyseDataVO.setRedAnalyzeMethodName(rs.getString("redAnalyzeMethodName"));
            dataAnalyseDataVO.setRedCountryStandard(rs.getString("redCountryStandard"));
            dataAnalyseDataVO.setYearSn(rs.getString("yearSn"));
            dataAnalyseDataVO.setAnalyzeMethodId(rs.getString("analyzeMethodId"));
            dataAnalyseDataVO.setAnalyseItemId(rs.getString("analyseItemId"));
            dataAnalyseDataVO.setQcId(rs.getString("qcId"));
            dataAnalyseDataVO.setQcType(rs.getInt("qcType"));
            dataAnalyseDataVO.setQcGrade(rs.getInt("qcGrade"));
            dataAnalyseDataVO.setMostSignificance(rs.getInt("mostSignificance"));
            dataAnalyseDataVO.setMostDecimal(rs.getInt("mostDecimal"));
            dataAnalyseDataVO.setExamLimitValue(rs.getString("examLimitValue"));
            dataAnalyseDataVO.setDimension(rs.getString("dimension"));
            dataAnalyseDataVO.setTestValue(rs.getString("testValue"));
            dataAnalyseDataVO.setTestOrignValue(rs.getString("testOrignValue"));
            dataAnalyseDataVO.setTestValueDstr(rs.getString("testValueDstr"));
            dataAnalyseDataVO.setAnalystName(rs.getString("analystName"));
            dataAnalyseDataVO.setAnalyzeTime(rs.getDate("analyzeTime"));
            dataAnalyseDataVO.setFinishTime(rs.getDate("finishTime"));
            dataAnalyseDataVO.setIsDataEnabled(rs.getBoolean("isDataEnabled"));
            dataAnalyseDataVO.setIsCompleteField(rs.getBoolean("isCompleteField"));
            dataAnalyseDataVO.setIsOutsourcing(rs.getBoolean("isOutsourcing"));
            dataAnalyseDataVO.setIsSamplingOut(rs.getBoolean("isSamplingOut"));
            dataAnalyseDataVO.setGatherCode(rs.getString("gatherCode"));
            dataAnalyseDataVO.setQcInfo(rs.getString("qcInfo"));
            dataAnalyseDataVO.setSeriesValue(rs.getString("seriesValue"));
            dataAnalyseDataVO.setOrgId(rs.getString("orgId"));
            dataAnalyseDataVO.setDomainId(rs.getString("domainId"));
            dataAnalyseDataVO.setSyncTime(new Date());
            return dataAnalyseDataVO;
        };
    }

}
