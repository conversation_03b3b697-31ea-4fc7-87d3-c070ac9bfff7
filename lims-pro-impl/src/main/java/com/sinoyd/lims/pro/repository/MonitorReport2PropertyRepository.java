package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoMonitorReport2Property;

import java.util.List;

/**
 * 报告与监测计划关联数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2025/3/19
 * @since V100R001
 */
public interface MonitorReport2PropertyRepository  extends IBaseJpaPhysicalDeleteRepository<DtoMonitorReport2Property, String> {
    /**
     * 根据报表标识查询
     * @param reportId 报表标识
     * @return 结果
     */
    List<DtoMonitorReport2Property> findByReportId(String reportId);

    /**
     * 根据报表标识查询
     * @param reportIds 报表标识
     * @return 结果
     */
    List<DtoMonitorReport2Property> findByReportIdIn(List<String> reportIds);
}
