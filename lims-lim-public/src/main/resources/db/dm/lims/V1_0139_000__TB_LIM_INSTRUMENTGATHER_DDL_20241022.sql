-- 仪器接入主表
CREATE TABLE "TB_LIM_INSTRUMENTGATHER"
(
    "id"           VARCHAR(50)                                                 NOT NULL,
    "instrumentId" varchar(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "mnNumber"     varchar(50)  DEFAULT ''                                     NOT NULL,
    "refresh"      INT          DEFAULT 1                                      NOT NULL,
    "isDeleted"    BIT          DEFAULT 0                                      NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL
);


-- 仪器接入参数配置表
CREATE TABLE "TB_LIM_INSTRUMENTGATHERPARAMS"
(
    "id"                 VARCHAR(50)                                                 NOT NULL,
    "instrumentGatherId" varchar(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "paramName"          varchar(100) NULL,
    "paramLabel"         varchar(100) NULL,
    "dataType"           varchar(10)  DEFAULT ''                                     NOT NULL,
    "isEnum"             BIT          DEFAULT 0,
    "enumDataSource"     varchar(255) NULL,
    "channelNum"         INT NULL,
    "dimension"          varchar(20) NULL,
    "orderNum"           INT          DEFAULT 0                                      NOT NULL,
    "remark"             varchar(255) NULL,
    "isDeleted"          BIT          DEFAULT 0                                      NOT NULL,
    "orgId"              VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "creator"            VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "createDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "domainId"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifier"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "modifyDate"         TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL
);

-- 仪器接入数据表
CREATE TABLE "TB_LIM_INSTRUMENTGATHERDATA"
(
    "id"                 VARCHAR(50)                                                 NOT NULL,
    "instrumentGatherId" VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "dataType"           VARCHAR(50)  DEFAULT ''                                     NOT NULL,
    "uploadTime"         TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL
);


-- 仪器接入数据详情表
CREATE TABLE "TB_LIM_INSTRUMENTGATHERDATADETAILS"
(
    "id"                       VARCHAR(50)                                                NOT NULL,
    "instrumentGatherDataId"   VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "InstrumentGatherParamsId" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "parmaName"                VARCHAR(50) NULL,
    "paramValue"               VARCHAR(50) NULL,
    "dimension"                VARCHAR(50) NULL
);



ALTER TABLE "TB_LIM_INSTRUMENTGATHER"
    ADD CONSTRAINT PRIMARY KEY (id);
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.id IS 'id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.instrumentId IS '仪器id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.mnNumber IS 'mn号';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.refresh IS '刷新间隔(秒)';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.isDeleted IS '是否删除（0不删除 1删除）';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.orgId IS '组织机构id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.creator IS '创建人';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.createDate IS '创建时间';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.domainId IS '所属实验室';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.modifier IS '修改人';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHER.modifyDate IS '修改时间';


ALTER TABLE "TB_LIM_INSTRUMENTGATHERPARAMS"
    ADD CONSTRAINT PRIMARY KEY (id);
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.id IS 'id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.instrumentGatherId IS '仪器接入表id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.paramName IS '参数名称';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.paramLabel IS '参数标识';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.dataType IS '数据类型枚举EnumInstrumentGatherDataType G:工况参数、CN2083:结果参数、Channel: 通道';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.isEnum IS '是否枚举';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.enumDataSource IS '枚举数据源';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.channelNum IS '通道数量';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.dimension IS '量纲';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.orderNum IS '排序值';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.remark IS '备注';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.isDeleted IS '是否删除（0不删除 1删除）';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.orgId IS '组织机构id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.creator IS '创建人';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.createDate IS '创建时间';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.domainId IS '所属实验室';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.modifier IS '修改人';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERPARAMS.modifyDate IS '修改时间';


ALTER TABLE "TB_LIM_INSTRUMENTGATHERDATA"
    ADD CONSTRAINT PRIMARY KEY (id);
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATA.id IS 'id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATA.instrumentGatherId IS '接入仪器id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATA.dataType IS '数据类型';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATA.uploadTime IS '上传时间';


ALTER TABLE "TB_LIM_INSTRUMENTGATHERDATADETAILS"
    ADD CONSTRAINT PRIMARY KEY (id);
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATADETAILS.id IS 'id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATADETAILS.instrumentGatherDataId IS '接入仪器id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATADETAILS.InstrumentGatherParamsId IS '参数id';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATADETAILS.parmaName IS '参数名称';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATADETAILS.paramValue IS '参数值';
COMMENT
ON COLUMN TB_LIM_INSTRUMENTGATHERDATADETAILS.dimension IS '量纲';