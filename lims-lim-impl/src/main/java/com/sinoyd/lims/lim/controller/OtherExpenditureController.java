package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.OtherExpenditureServiceCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoOtherExpenditure;
import com.sinoyd.lims.lim.dto.customer.DtoOtherExpenditureTotal;
import com.sinoyd.lims.lim.service.OtherExpenditureService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 非合同支出管理接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/otherExpenditure")
public class OtherExpenditureController
    extends BaseJpaController<DtoOtherExpenditure, String, OtherExpenditureService> {

    /**
     * 分页动态条件查询非合同支出
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询非合同支出", notes = "分页动态条件查询非合同支出")
    @GetMapping
    public RestResponse<List<DtoOtherExpenditure>> findByPage(OtherExpenditureServiceCriteria criteria) {

        RestResponse<List<DtoOtherExpenditure>> restResp = new RestResponse<>();

        PageBean<DtoOtherExpenditure> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 分页动态条件查询收付款记录
     *
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询收付款记录", notes = "分页动态条件查询收付款记录")
    @GetMapping("/getList")
    public RestResponse<DtoOtherExpenditureTotal> getList(OtherExpenditureServiceCriteria criteria) {

        RestResponse<DtoOtherExpenditureTotal> restResp = new RestResponse<>();

        DtoOtherExpenditureTotal dto = new DtoOtherExpenditureTotal();
        dto = service.getTotalNum(criteria);

        PageBean<DtoOtherExpenditure> page = super.getPageBean();
        service.findByPage(page, criteria);
        dto.setOtherExpenditureList(page.getData());

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(dto);
        restResp.setCount(page.getRowsCount());

        return restResp;
    }
}