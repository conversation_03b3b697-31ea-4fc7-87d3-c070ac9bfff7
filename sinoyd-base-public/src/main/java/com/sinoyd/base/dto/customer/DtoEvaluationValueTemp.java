package com.sinoyd.base.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 评价等级值保存的实体（单个条件）
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Data
public class DtoEvaluationValueTemp {


    /**
     * 主键ids
     */
    private List<String> ids = new ArrayList<>();

    /**
     * 上限值符号
     */
    private String upperLimitSymbol;


    /**
     * 下限值符号
     */
    private  String lowerLimitSymbol;

    /**
     * 评价标准id
     */
    private String evaluationId;

    /**
     * 评价等级id
     */
    private String levelId;

    /**
     * 分析项目ids
     */
    private List<String> analyzeItemIds = new ArrayList<>();

    /**
     * 量纲
     */
    private String dimensionId;
}
