package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoQualityManage;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * QualityManage数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface QualityManageRepository extends IBaseJpaPhysicalDeleteRepository<DtoQualityManage, String> {

    /**
     * 根据数据获取质控任务的相关数据
     *
     * @param anaIds 数据ids
     * @return 返回相关的质控任务数据
     */
    List<DtoQualityManage> findByAnaIdIn(List<String> anaIds);

    /**
     * 根据数据id删除质控任务相关数据
     *
     * @param anaIds 数据ids
     */
    @Transactional
    Integer deleteByAnaIdIn(List<String> anaIds);
}