package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.service.IFixedPointGenerateOrderDetailService;
import com.sinoyd.lims.pro.vo.OrderPollutionPointGenerateVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 企业点位生成订单详情 Controller
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/25
 * @since V100R001
 */
@RestController
@RequestMapping("/api/pro/fixedPointGenerateOrderDetail")
public class FixedPointGenerateOrderDetailController extends ExceptionHandlerController<IFixedPointGenerateOrderDetailService> {

    /**
     * 污染源点位生成订单详情
     *
     * @param generateVO 生成数据传参VO
     * @return 响应
     */
    @PostMapping("/generate")
    public RestResponse<Void> generateDetail(@RequestBody OrderPollutionPointGenerateVO generateVO) {
        service.generateDetail(generateVO);
        return new RestResponse<>();
    }
}
