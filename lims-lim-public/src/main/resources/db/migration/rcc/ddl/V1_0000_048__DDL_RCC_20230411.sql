-- 新增报告组件信息表
CREATE TABLE TB_LIM_ReportModule
(
    id              varchar(50)     NOT NULL                                                COMMENT '主键id',
    moduleCode      varchar(100)    NOT NULL                                                COMMENT '组件编码',
    moduleName      varchar(100)    NOT NULL                                                COMMENT '组件名称',
    tableName       varchar(100)    NOT NULL                                                COMMENT '组件主表名称',
    sourceTableName varchar(100)    NULL                                                    COMMENT '组件数据行表名称',
    sampleCount     int(11)         NOT NULL DEFAULT 0                                      COMMENT '组件每页样品数量',
    testCount       int(11)         NOT NULL DEFAULT 0                                      COMMENT '组件每页测试项目数量',
    sonTableJson    varchar(500)    NULL                                                    COMMENT '子组件配置信息（适用于复合组件）',
    isCompound      bit(1)          NOT NULL DEFAULT b'0'                                   COMMENT '是否复合组件',
    orgId           varchar(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator         varchar(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate      datetime(0)     NOT NULL DEFAULT CURRENT_TIMESTAMP (0)                  COMMENT '创建时间',
    domainId        varchar(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier        varchar(50)     NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate      datetime(0)     NOT NULL DEFAULT CURRENT_TIMESTAMP (0)                  COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT = '报告组件信息表';


-- 新增报告组件配置表
CREATE TABLE TB_LIM_ReportConfig2Module
(
    id             varchar(50) NOT NULL                                                 COMMENT 'id',
    reportConfigId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '报表配置id',
    reportModuleId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'  COMMENT '报告组件id（常量维护）',
    PRIMARY KEY (id)
) COMMENT = '报告组件配置表';

-- 新增报告各个组件配置的分页方式表
CREATE TABLE TB_LIM_ReportModule2GroupType
(
    id                   varchar(50)    NOT NULL                                                    COMMENT '主键id',
    reportConfigModuleId varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'     COMMENT '报告组件配置id',
    groupTypeName        varchar(255)   NULL                                                        COMMENT '分页类型名称（包含数据源，属性名称，分页方式）',
    priority             int(11)        NOT NULL DEFAULT -1                                         COMMENT '优先级（最外层分页的优先级最高）',
    PRIMARY KEY (id)
) COMMENT = '报告各个组件配置的分页方式表';

