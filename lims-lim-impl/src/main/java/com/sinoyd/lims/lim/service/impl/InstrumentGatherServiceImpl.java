package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGather;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherParams;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.InstrumentGatherParamsRepository;
import com.sinoyd.lims.lim.repository.lims.InstrumentGatherRepository;
import com.sinoyd.lims.lim.service.InstrumentGatherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 仪器接入操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Service
public class InstrumentGatherServiceImpl extends BaseJpaServiceImpl<DtoInstrumentGather, String, InstrumentGatherRepository> implements InstrumentGatherService {

    private InstrumentRepository instrumentRepository;

    private InstrumentGatherParamsRepository instrumentGatherParamsRepository;

    private DocumentRepository documentRepository;

    private DocumentService documentService;

    private RedisTemplate redisTemplate;

    @Override
    public void findByPage(PageBean<DtoInstrumentGather> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrumentGather x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");

        super.findByPage(page, criteria);
        List<DtoInstrumentGather> instrumentGathers = page.getData();
        // 填充冗余字段
        fillingTransientFields(instrumentGathers);
        // 根据仪器编号排序
        instrumentGathers.sort(Comparator.comparing(DtoInstrumentGather::getInstrumentsCode));
    }

    @Override
    @Transactional
    public DtoInstrumentGather save(DtoInstrumentGather entity) {
        Integer count = repository.countByNotIdAndInstrumentIdOrMnNumber(entity.getId(), entity.getInstrumentId(), entity.getMnNumber());
        if (count > 0) {
            throw new BaseException("存在相同的接入仪器或者MN号码，请确认！");
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoInstrumentGather update(DtoInstrumentGather entity) {
        Integer count = repository.countByNotIdAndInstrumentIdOrMnNumber(entity.getId(), entity.getInstrumentId(), entity.getMnNumber());
        if (count > 0) {
            throw new BaseException("存在相同的接入仪器或者MN号码，请确认！");
        }
        if (StringUtil.isEmpty(entity.getBase64Content())) {
            // 删除附件
            List<DtoDocument> documentList = documentRepository.findByFolderIdAndDocTypeIdOrderByCreateDateDesc(entity.getId(), "BASE_DocumentExtendType_XCYQDJ");
            if (StringUtil.isNotEmpty(documentList)) {
                documentRepository.delete(documentList);
            }
        }

        return super.update(entity);
    }

    /**
     * 获取接入仪器归档路径
     *
     * @param id 仪器接入id
     * @return
     */
    @Override
    public DtoInstrumentGather findInstrumentGatherAttachment(String id) {
        DtoInstrumentGather instrumentGather = repository.findOne(id);
        if (StringUtil.isNull(instrumentGather)) {
            instrumentGather = new DtoInstrumentGather();
            instrumentGather.setId(id);
        }
        return instrumentGather;
    }

    @Override
    public Map<String, Object> onlineStatus() {

        List<DtoInstrumentGather> instrumentGathers = findAll();
        // 先从redis中获取mq上传的状态
        String onlineStatusKey = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_InstrumentGatherOnlineStatus.getValue());
        Object obj = redisTemplate.opsForValue().get(onlineStatusKey);
        Map onlineStatusMap = new HashMap();
        if (StringUtil.isNotNull(obj)) {
            onlineStatusMap = JsonIterator.deserialize(obj.toString(), Map.class);
        }
        boolean updateRedisFlag = false;
        for (DtoInstrumentGather instrumentGather : instrumentGathers) {
            // 状态与MN号码绑定
            if (!onlineStatusMap.containsKey(instrumentGather.getMnNumber())) {
                onlineStatusMap.put(instrumentGather.getMnNumber(), "0");
                updateRedisFlag = true;
            }
        }
        if (updateRedisFlag) {
            redisTemplate.opsForValue().set(onlineStatusKey, JsonStream.serialize(onlineStatusMap), 5, TimeUnit.MINUTES);
        }

        return onlineStatusMap;
    }

    /**
     * 填充冗余字段
     *
     * @param dataList 数据集合
     */
    private void fillingTransientFields(List<DtoInstrumentGather> dataList) {
        List<String> instrumentIds = dataList.stream().map(DtoInstrumentGather::getInstrumentId).distinct().collect(Collectors.toList());
        List<String> instrumentGatherIds = dataList.stream().map(DtoInstrumentGather::getId).distinct().collect(Collectors.toList());
        List<DtoInstrument> instrumentList = StringUtil.isNotEmpty(instrumentIds) ? instrumentRepository.findAll(instrumentIds) : new ArrayList<>();
        List<DtoInstrumentGatherParams> instrumentGatherParams = instrumentGatherParamsRepository.findByInstrumentGatherIdIn(instrumentGatherIds);
        Map<String, List<DtoInstrumentGatherParams>> instrumentGatherParamsMap = instrumentGatherParams.stream().collect(Collectors.groupingBy(DtoInstrumentGatherParams::getInstrumentGatherId));

        List<DtoDocument> documentList = documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(instrumentGatherIds, "BASE_DocumentExtendType_XCYQDJ");

        dataList.forEach(data -> {
            Optional<DtoInstrument> instrumentOptional = instrumentList.stream().filter(a -> a.getId().equals(data.getInstrumentId())).findFirst();
            String instrumentCode = "";
            // 填充仪器数据
            if (instrumentOptional.isPresent()) {
                DtoInstrument instrument = instrumentOptional.get();
                data.setOriginEndDate(instrument.getOriginEndDate());
                data.setInstrumentName(instrument.getInstrumentName());
                data.setModel(instrument.getModel());
                instrumentCode = instrument.getInstrumentsCode();
            }
            data.setInstrumentsCode(instrumentCode);
            // 仪器接入参数数据
            List<DtoInstrumentGatherParams> gatherParams = instrumentGatherParamsMap.getOrDefault(data.getId(), new ArrayList<>());
            gatherParams.stream().filter(p -> EnumLIM.EnumInstrumentGatherDataType.通道数据.getValue().equals(p.getDataType())).findFirst().ifPresent(param -> {
                data.setChannelNum(param.getChannelNum());
            });
            documentList.stream().filter(p -> p.getFolderId().equals(data.getId())).findFirst().ifPresent(doc -> {
                data.setBase64Content(documentService.convertBase64Content(doc.getPath()));
            });
        });

    }

    @Autowired
    public void setInstrumentRepository(InstrumentRepository instrumentRepository) {
        this.instrumentRepository = instrumentRepository;
    }

    @Autowired
    public void setInstrumentGatherParamsRepository(InstrumentGatherParamsRepository instrumentGatherParamsRepository) {
        this.instrumentGatherParamsRepository = instrumentGatherParamsRepository;
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}