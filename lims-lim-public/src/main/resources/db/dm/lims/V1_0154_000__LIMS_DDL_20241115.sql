CREATE TABLE TB_PRO_FlowCalibration
(
    id           varchar(50) NOT NULL,
    instrumentId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    calibrationDate    datetime    NOT NULL DEFAULT '1753-01-01 00:00:00',
    calibrationPeople      varchar(1000) NOT NULL DEFAULT '',
    calibrationType      int,
    isDeleted    bit      NOT NULL DEFAULT 0,
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE TB_PRO_FlowCalibration ADD CONSTRAINT PRIMARY KEY (id);
COMMENT ON TABLE TB_PRO_FlowCalibration IS '流量校准记录表';
COMMENT ON COLUMN TB_PRO_FlowCalibration.id IS 'id';
COMMENT ON COLUMN TB_PRO_FlowCalibration.instrumentId IS '采样任务id';
COMMENT ON COLUMN TB_PRO_FlowCalibration.calibrationDate IS '仪器id';
COMMENT ON COLUMN TB_PRO_FlowCalibration.calibrationPeople IS '监管平台采样仪器配置';
COMMENT ON COLUMN TB_PRO_FlowCalibration.calibrationType IS 'id';
COMMENT ON COLUMN TB_PRO_FlowCalibration.isDeleted IS '假删字段';
COMMENT ON COLUMN TB_PRO_FlowCalibration.orgId IS '组织机构id';
COMMENT ON COLUMN TB_PRO_FlowCalibration.creator IS '创建人';
COMMENT ON COLUMN TB_PRO_FlowCalibration.createDate IS '创建时间';
COMMENT ON COLUMN TB_PRO_FlowCalibration.domainId IS '所属实验室';
COMMENT ON COLUMN TB_PRO_FlowCalibration.modifier IS '修改人';
COMMENT ON COLUMN TB_PRO_FlowCalibration.modifyDate IS '修改时间';

CREATE TABLE TB_PRO_FlowCalibrationRow
(
    id           varchar(50) NOT NULL,
    flowCalibrationId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    isDeleted    bit      NOT NULL DEFAULT 0,
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE TB_PRO_FlowCalibrationRow ADD CONSTRAINT PRIMARY KEY (id);
COMMENT ON TABLE TB_PRO_FlowCalibrationRow IS '流量校准数据表';
COMMENT ON COLUMN TB_PRO_FlowCalibrationRow.id IS 'id';
COMMENT ON COLUMN TB_PRO_FlowCalibrationRow.flowCalibrationId IS '校准记录id';
COMMENT ON COLUMN TB_PRO_FlowCalibrationRow.isDeleted IS '假删字段';
COMMENT ON COLUMN TB_PRO_FlowCalibrationRow.orgId IS '组织机构id';
COMMENT ON COLUMN TB_PRO_FlowCalibrationRow.creator IS '创建人';
COMMENT ON COLUMN TB_PRO_FlowCalibrationRow.createDate IS '创建时间';
COMMENT ON COLUMN TB_PRO_FlowCalibrationRow.domainId IS '所属实验室';
COMMENT ON COLUMN TB_PRO_FlowCalibrationRow.modifier IS '修改人';
COMMENT ON COLUMN TB_PRO_FlowCalibrationRow.modifyDate IS '修改时间';

CREATE TABLE TB_PRO_FlowCalibrationParamData
(
    id           varchar(50) NOT NULL COMMENT 'id',
    flowCalibrationRowId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    paramName    varchar(50) NOT NULL COMMENT '参数名称',
    paramValue   varchar(50) COMMENT '参数值',
    isDeleted    bit      NOT NULL DEFAULT 0,
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE TB_PRO_FlowCalibrationParamData ADD CONSTRAINT PRIMARY KEY (id);
COMMENT ON TABLE TB_PRO_FlowCalibrationParamData IS '流量校准数据参数表';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.id IS '主键';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.flowCalibrationRowId IS '校准数据id';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.paramName IS '参数名称';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.paramValue IS '参数值';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.isDeleted IS '假删字段';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.orgId IS '组织机构id';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.creator IS '创建人';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.createDate IS '创建时间';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.domainId IS '所属实验室';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.modifier IS '修改人';
COMMENT ON COLUMN TB_PRO_FlowCalibrationParamData.modifyDate IS '修改时间';

