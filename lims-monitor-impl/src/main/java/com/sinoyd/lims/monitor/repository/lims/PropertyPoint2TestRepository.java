package com.sinoyd.lims.monitor.repository.lims;

import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;


/**
 * PropertyPoint2Test数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface PropertyPoint2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoPropertyPoint2Test, String> {

    /**
     * 监测计划点位的测试项目集合
     * @param perpointIds 监测计划点位
     * @return 监测计划点位测试项目集合
     */
    List<DtoPropertyPoint2Test> findByPropertyPointIdIn(List<String> perpointIds);

    /**
     * 测试项目集合
     * @param perPointId 计划点位id
     * @param testIds 测试项目Ids
     * @return 测试项目集合
     */
    List<DtoPropertyPoint2Test> findByPropertyPointIdAndTestIdIn(String perPointId, Collection<String> testIds);
}