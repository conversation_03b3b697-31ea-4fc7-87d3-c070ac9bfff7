package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.pro.dto.DtoSolutionCalibrationRecord;
import com.sinoyd.lims.pro.repository.SolutionCalibrationRecordRepository;
import com.sinoyd.lims.pro.service.SolutionCalibrationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 溶液标定记录
 *
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
@Service
@Slf4j
public class SolutionCalibrationRecordServiceImpl extends BaseJpaServiceImpl<DtoSolutionCalibrationRecord, String, SolutionCalibrationRecordRepository>
        implements SolutionCalibrationRecordService {

    @Override
    public List<DtoSolutionCalibrationRecord> findBySolutionCalibrationId(String solutionCalibrationId) {
        return repository.findBySolutionCalibrationId(solutionCalibrationId);
    }
}
