package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Table;
import javax.validation.constraints.Pattern;

/**
 * 测得量公式导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/6
 * @since V100R001
 */
@Table(name = "TB_LIM_ParamsPartFormula")
@Data
public class DtoExportParamsPartFormula extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id",orderNum = "10")
    private String id;

    /**
     * 公式Id
     */
    @Excel(name = "公式Id",orderNum = "20")
    private String formulaId;

    /**
     * 部分公式
     */
    @Excel(name = "部分公式",orderNum = "30")
    private String formula;


    /**
     * 参数名称
     */
    @Excel(name = "参数名称",orderNum = "40")
    private String paramsName;

    /**
     * 有效位数
     */
    @Excel(name = "有效位数",orderNum = "50")
    @Pattern(regexp = "^-?\\d+$", message = "有效位数必须为整数")
    private String mostSignificance;

    /**
     * 小数位数
     */
    @Excel(name = "小数位数",orderNum = "60")
    @Pattern(regexp = "^-?\\d+$", message = "小数位数必须为整数")
    private String mostDecimal;

    /**
     * 排序值
     */
    @Excel(name = "排序值",orderNum = "70")
    @Pattern(regexp = "^-?\\d+$", message = "排序值必须为整数")
    private String orderNum;

    /**
     * 类型（EnumPartFormulaType：0.修约公式（用于测试公式及原始记录单参数公式中的修约） 1.检测类型参数公式 2.加标公式 3.BOD5判断公式 4.参数公式（如减空白后吸光度=吸光度-空白） 5.串联出证公式）
     */
    @Excel(name = "类型",orderNum = "80")
    @Pattern(regexp = "^-?\\d+$", message = "类型必须为整数")
    private String formulaType;

    /**
     * 检出限
     */
    @Excel(name = "检出限",orderNum = "90")
    private String detectionLimit;

    /**
     * 计算方式：枚举EnumCalculationMode：0.原始值, 1.检出限一半，2.取零
     */
    @Excel(name = "计算方式",orderNum = "100")
    @Pattern(regexp = "^-?\\d+$", message = "计算方式必须为整数(1.检出限一半，2.取零)")
    private String calculationMode;

    /**
     * 是否使用测试项目检出限
     */
    @Excel(name = "是否使用测试项目检出限",orderNum = "110")
    @Pattern(regexp = "^(true|false)$", message = "是否使用测试项目检出限必须为布尔类型")
    private String useTestLimit;
}
