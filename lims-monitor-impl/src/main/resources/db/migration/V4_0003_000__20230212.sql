-- 新增产品报表：气体样品分析结果计算表
delete from TB_LIM_ReportConfig where id = 'c7e4a25a-2683-4934-90b0-c52e7c9c60a1';
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum)
VALUES ('c7e4a25a-2683-4934-90b0-c52e7c9c60a1', 1, 'GasSampleAnalysisResults', '气体样品分析结果计算表_模板.xlsx',
        'SamplingRecords/气体样品分析结果计算表_模板.xlsx', 'output/SamplingRecords/气体样品分析结果计算表.xlsx', 'application/excel',
        'com.sinoyd.lims.wordreport.service.jx.wordReport.GasSampleAnalysisResultsService', '', '', 0, 3, '', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-08 09:20:14',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-09 15:18:53', '', 'Sampling',
        'GasSampleAnalysisResults', b'0', '', '', '', '');
-- 添加应用场景配置
delete from TB_LIM_ReportApply where id = 'e93f0800-ba9c-4164-b6a9-d59aba5a9072';
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location)
VALUES ('e93f0800-ba9c-4164-b6a9-d59aba5a9072', 'c7e4a25a-2683-4934-90b0-c52e7c9c60a1', 'ReportEdit', '报告编制',
        'GasSampleAnalysisResults', '气体样品及分析结果计算表', 1, 0, 1, '', '');
