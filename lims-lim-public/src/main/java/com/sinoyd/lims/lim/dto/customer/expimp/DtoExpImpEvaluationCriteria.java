package com.sinoyd.lims.lim.dto.customer.expimp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @date V1.0.0 2023/12/22
 * @version: V100R001
 */
@Data
public class DtoExpImpEvaluationCriteria extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id（新增时不填）", orderNum = "10", width = 20)
    private String id;

    /**
     * 评价标准名称
     */
    @Excel(name = "标准名称(必填)", orderNum = "100", width = 22)
    private String name;

    /**
     * 评价标准Id
     */
    private String evaluationCriteriaId;

    /**
     * 标准代码
     */
    @Excel(name = "标准代码(必填)", orderNum = "200", width = 22)
    private String code;

    /**
     * 实施时间
     */
    @Excel(name = "实施日期", orderNum = "300", width = 22)
    private String startTime;

    /**
     * 检测类型
     */
    @Excel(name = "样品类型(必填)", orderNum = "400", width = 22)
    private String sampleTypeName;

    private String sampleTypeId;

    /**
     * 评价条件
     */
    @Excel(name = "评价条件", orderNum = "500", width = 22)
    private String evaluationLevel;

    /**
     * 评价条件Id
     */
    private String evaluationLevelId;

    /**
     * 评价因子名称
     */
    @Excel(name = "评价因子", orderNum = "700", width = 22)
    private String analyzeItemNames;

    /**
     * 评价因子Id
     */
    private String analyzeItemId;

    /**
     * 下限运算符
     */
    @Excel(name = "下限表达式", orderNum = "800", width = 22)
    private String lowerLimitSymble;

    /**
     * 下限
     */
    @Excel(name = "下限", orderNum = "900", width = 22)
    private String lowerLimit;

    /**
     * 上限运算符
     */
    @Excel(name = "上限表达式", orderNum = "1000", width = 22)
    private String upperLimitSymble;

    /**
     * 上限
     */
    @Excel(name = "上限", orderNum = "1100", width = 22)
    private String upperLimit;

    /**
     * 量纲
     */
    @Excel(name = "量纲", orderNum = "1150", width = 22)
    private String dimension;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 排放速率
     */
    @Excel(name = "排放速率(kg/h)",orderNum = "1175",width = 22)
    private String emissionRate;



    /**
     * 评价限值中的备注
     */
    @Excel(name = "备注", orderNum = "1200", width = 22)
    private String evaluationValueRemark;

}
