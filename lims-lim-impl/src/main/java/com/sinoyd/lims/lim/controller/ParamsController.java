package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ParamsCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.service.ParamsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数管理控制器
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "参数管理: 参数管理服务")
@RestController
@RequestMapping("/api/lim/params")
@Validated
public class ParamsController extends BaseJpaController<DtoParams, String, ParamsService> {

    /**
     * 根据id获取参数
     * 
     * @param id 参数id
     * @return 参数实体
     */
    @ApiOperation(value = "按主键获取参数", notes = "按主键获取参数")
    @GetMapping("/{id}")
    public RestResponse<DtoParams> getById(@PathVariable String id)
    {
        RestResponse<DtoParams> restResponse = new RestResponse<>();
        DtoParams params = service.findOne(id);
        restResponse.setData(params);
        restResponse.setRestStatus(StringUtil.isNull(params) ?
        ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        
        return restResponse;
    }

    /**
     * 分页查询参数
     * 
     * @param paramsCriteria 参数列表:关键字(参数名称、参数编号、变量名称),排序,分页
     * @return 参数List
     */
    @ApiOperation(value = "分页动态条件获取参数", notes = "分页动态条件获取参数")
    @GetMapping("")
    public RestResponse<List<DtoParams>> findByPage(ParamsCriteria paramsCriteria)
    {
        RestResponse<List<DtoParams>> restResponse = new RestResponse<>();
        PageBean<DtoParams> pageBean = super.getPageBean();
        service.findByPage(pageBean, paramsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        
        return restResponse;
    }

    /**
     * 新增参数
     * 
     * @param params 参数实体
     * @return 新增的参数实体
     */
    @ApiOperation(value = "新增参数", notes = "新增参数")
    @PostMapping("")
    public RestResponse<DtoParams> create(@Validated @RequestBody DtoParams params)
    {
        RestResponse<DtoParams> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParams data = service.save(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新参数
     * 
     * @param params 参数实体
     * @return 更新后的参数实体
     */
    @ApiOperation(value = "更新参数", notes = "更新参数")
    @PutMapping("")
    public RestResponse<DtoParams> update(@Validated @RequestBody DtoParams params)
    {
        RestResponse<DtoParams> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParams data = service.update(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     * 
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id)
    {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     * 
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids)
    {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }
}