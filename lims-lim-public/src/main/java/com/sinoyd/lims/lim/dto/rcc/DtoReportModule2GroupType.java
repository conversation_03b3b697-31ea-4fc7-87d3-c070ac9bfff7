package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.ReportModule2GroupType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoReportModule2GroupType实体
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ReportModule2GroupType")
@Data
@DynamicInsert
public class DtoReportModule2GroupType extends ReportModule2GroupType {

    private static final long serialVersionUID = 1L;

    @Transient
    private String reportCode;

}
