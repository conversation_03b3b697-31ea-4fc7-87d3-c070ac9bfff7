package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.customer.DtoEvaluationRecordCopyVO;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.dto.customer.DtoEvaluationBatchProcess;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoAnalyseOriginalRecord;
import com.sinoyd.lims.pro.dto.DtoEvaluationRecord;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluationBatch;
import com.sinoyd.lims.pro.dto.customer.DtoEvaluationResult;

import java.util.List;
import java.util.Map;

import com.sinoyd.frame.service.IBaseJpaService;


/**
 * 评价记录操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
public interface EvaluationRecordService extends IBaseJpaService<DtoEvaluationRecord, String> {

    /**
     * 获取项目下的初始化配置列表
     *
     * @param eva
     */
    List<DtoEvaluationValue> getInitEvaluation(DtoEvaluationBatch eva);

    /**
     * 批量保存评价记录
     *
     * @param eva
     */
    void createBatch(DtoEvaluationBatch eva);

    /**
     * 查询评价记录
     *
     * @param entity 传输实体
     */
    List<DtoEvaluationRecord> query(DtoEvaluationResult entity);

    /**
     * 保存评价记录
     *
     * @param entity 保存实体
     */
    void saveRecord(DtoEvaluationResult entity);

    /**
     * 返回评价详情及结论
     *
     * @param dto 关联ids 类型 和点位计划
     */
    List<DtoEvaluationResult> getResult(DtoEvaluationRecord dto);

    /**
     * 查询评价分析结果
     *
     * @param criteria 查询条件实体
     */
    Map<String, Object> getAnalyzeResult(BaseCriteria criteria);

    /**
     * 情况评价记录
     *
     * @param folderIdList 点位id列表
     */
    void clearRecord(List<String> folderIdList);

    /**
     * 批处理评价记录
     *
     * @param evaluationBatchProcess 批处理传输实体
     */
    void batchProcess(DtoEvaluationBatchProcess evaluationBatchProcess);

    /**
     * 复制评价信息
     *
     * @param eva 复制传输实体
     */
    void copy(DtoEvaluationRecordCopyVO eva);

    /**
     * 更新排放速率
     *
     * @param sampleTypeIds              样品类型ids
     * @param evaluationRecordList   评价提醒集合
     * @param analyseOriginalRecords 分析公式数据集合
     * @param analyseDataList        分析数据集合
     */
    void updateEmissionRate(List<String> sampleTypeIds,
                            List<DtoEvaluationRecord> evaluationRecordList,
                            List<DtoAnalyseOriginalRecord> analyseOriginalRecords,
                            List<DtoAnalyseData> analyseDataList);
}