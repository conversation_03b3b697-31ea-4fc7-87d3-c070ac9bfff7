package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件夹权限查询条件
 * <AUTHOR>
 * @version 1.0.0 2019/5/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocAuthorityCriteria extends BaseCriteria {

    private String parentCode;

    private String parentId;

    private String objectId;
    
    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if(StringUtils.isNotNullAndEmpty(objectId) &&
                !UUIDHelper.GUID_EMPTY.equals(this.objectId))
        {
            condition.append(" and (objectId = :objectId)");
            values.put("objectId", this.objectId);
        }
        condition.append(" ");
        return condition.toString();
    }
}