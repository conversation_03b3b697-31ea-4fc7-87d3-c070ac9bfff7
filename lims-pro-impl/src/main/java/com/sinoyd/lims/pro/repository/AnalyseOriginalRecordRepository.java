package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoAnalyseOriginalRecord;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * AnalyseOriginalRecord数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface AnalyseOriginalRecordRepository extends IBaseJpaRepository<DtoAnalyseOriginalRecord, String> {


    /**
     * 根据分析数据id获取相关的原始记录数据
     *
     * @param anaIds 数据ids
     * @return 返回分析相关数据
     */
    List<DtoAnalyseOriginalRecord> findByAnalyseDataIdIn(Collection<String> anaIds);


    @Transactional
    @Modifying
    @Query("update DtoAnalyseOriginalRecord as a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.analyseDataId in :anaIds")
    Integer deleteByAnalyseDataIds(@Param("anaIds") List<String> anaIds,
                                   @Param("modifier") String modifier,
                                   @Param("modifyDate") Date modifyDate);
}