package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.MonitorReport2Property;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 报告与监测计划关联实体
 * <AUTHOR>
 * @version V1.0.0 2025/3/19
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_MonitorReport2Property")
@Data
@DynamicInsert
public class DtoMonitorReport2Property extends MonitorReport2Property {
}
