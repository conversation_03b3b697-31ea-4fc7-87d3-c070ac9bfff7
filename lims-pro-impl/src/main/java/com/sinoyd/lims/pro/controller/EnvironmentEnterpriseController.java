package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.EnvironmentEnterpriseService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Map;


/**
 * 环保企业通项目数据推送接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
@Api(tags = "示例: Report服务")
@RestController
@RequestMapping("api/pro/environmentEnterprise")
public class EnvironmentEnterpriseController extends ExceptionHandlerController<EnvironmentEnterpriseService> {

    /**
     * 下载报告
     *
     * @param request 请求对象
     * @return RestResponse<DtoReport>
     */
    @PostMapping("/download/stream")
    public RestResponse<byte[]> fileDownAsStream(HttpServletRequest request) {
        RestResponse<byte[]> restResp = new RestResponse<>();
        String id = request.getParameter("id");
        String token = request.getParameter("dn-qyt-token");
        restResp.setData(service.downReport(id, token));
        return restResp;
    }



    /**
     * 项目数据推送
     *
     * @param projectId 项目id
     * @return RestResponse<DtoReport>
     */
    @PostMapping("/push/{projectId}")
    public RestResponse<String> push(@PathVariable(name = "projectId") String projectId) {
        RestResponse<String> restResp = new RestResponse<>();
        service.push(projectId);
        restResp.setData("");
        return restResp;
    }

}