package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.HomePendingNo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 首页待办数据实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/17
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_HomePendingNo")
@Data
@DynamicInsert
public class DtoHomePendingNo extends HomePendingNo {
    private static final long serialVersionUID = 1L;
}
