package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;

import java.util.List;
import java.util.Map;

/**
 * 分析及时率图表数据查询业务接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/31
 */
public interface AnalyzeTimelinessChartService {

    /**
     * 总及时率图表查询
     *
     * @param criteria 查询条件
     * @return 图表结果
     */
    Map<String, Object> totalTimelinessChart(BaseCriteria criteria);

    /**
     * 及时率柱状图查询
     *
     * @param criteria 查询条件
     * @return 图表结果
     */
    Map<String, List<Object>> histogramChart(BaseCriteria criteria);
}