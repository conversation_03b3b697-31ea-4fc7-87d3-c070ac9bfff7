package com.sinoyd.lims.pro.view;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 项目数量统计的视图
 */
@Entity
@Table(name = "VI_PRO_ProjectZKSampleCountView")
@Data
public class VProjectZKSampleCount {

    /**
     * 主键id
     */
    @Id
    private String id = UUIDHelper.NewID();
    /**
     * 项目id
     */
    private String projectId;

    /**
     * 登记时间
     */
    private Date inceptTime;

    /**
     * 分析数据id
     */
    private String analyseDataId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 样品类型名称
     */
    private String sampleTypeName;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 数据假删
     */
    private Boolean isDeleted;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 组织机构id
     */
    private String orgId;
}
