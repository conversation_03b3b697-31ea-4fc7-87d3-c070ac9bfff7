package com.sinoyd.base.configuration;


import com.sinoyd.base.dto.vo.ComparisonConfigVo;
import com.sinoyd.base.dto.vo.QualityConfigVo;

import javax.xml.bind.annotation.*;

/**
 * 质控规则Xml映射类
 *
 * <AUTHOR>
 * @version 5.2.0
 * @since 2023/05/12
 */
@XmlRootElement(name = "rules")
@XmlAccessorType(XmlAccessType.FIELD)
public class QcRulesConfig {

    @XmlElement(name = "qualityConfig")
    private QualityConfigVo qualityReviseVo;

    @XmlElement(name = "comparisonConfig")
    private ComparisonConfigVo comparisonReviseVo;

    @XmlTransient
    public QualityConfigVo getQualityReviseVo() {
        return qualityReviseVo;
    }

    public void setQualityReviseVo(QualityConfigVo qualityReviseVo) {
        this.qualityReviseVo = qualityReviseVo;
    }

    @XmlTransient
    public ComparisonConfigVo getComparisonReviseVo() {
        return comparisonReviseVo;
    }

    public void setComparisonReviseVo(ComparisonConfigVo comparisonReviseVo) {
        this.comparisonReviseVo = comparisonReviseVo;
    }
}
