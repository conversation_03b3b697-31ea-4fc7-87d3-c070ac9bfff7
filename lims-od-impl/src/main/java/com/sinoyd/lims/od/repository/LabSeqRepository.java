package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.od.dto.DtoLabSeq;

import java.util.List;

/**
 * LabSeqRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface LabSeqRepository extends IBaseJpaRepository<DtoLabSeq, String> {


    /**
     * 根据实验组id查询
     *
     * @param groupId 实验组id
     * @return 试验次序集合
     */
    List<DtoLabSeq> findByGroupId(String groupId);

    /**
     * 根据实验组ids查询
     *
     * @param groupIds 实验组ids
     * @return 试验次序集合
     */
    List<DtoLabSeq> findByGroupIdIn(List<String> groupIds);
}
