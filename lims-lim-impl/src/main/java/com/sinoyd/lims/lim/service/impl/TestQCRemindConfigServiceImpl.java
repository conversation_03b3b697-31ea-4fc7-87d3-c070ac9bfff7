package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig;
import com.sinoyd.lims.lim.repository.rcc.TestQCRemindConfig2TestRepository;
import com.sinoyd.lims.lim.repository.rcc.TestQCRemindConfigRepository;
import com.sinoyd.lims.lim.service.TestQCRemindConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * TestQCRemindConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
 @Service
public class TestQCRemindConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoTestQCRemindConfig,String,TestQCRemindConfigRepository> implements TestQCRemindConfigService {

    @Autowired
    private TestQCRemindConfig2TestRepository testQCRemindConfig2TestRepository;

    @Override
    public void findByPage(PageBean<DtoTestQCRemindConfig> pb, BaseCriteria testQCRemindConfigCriteria) {
        pb.setEntityName("DtoTestQCRemindConfig a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, testQCRemindConfigCriteria);
    }


    /**
     * 修改
     */
    @Transactional
    @Override
    public DtoTestQCRemindConfig update(DtoTestQCRemindConfig entity) {

        //选择默认，清除相关配置的测试项目
        if (entity.getIsDefault()) {
            testQCRemindConfig2TestRepository.deleteByConfigId(entity.getId());
        }

        return super.update(entity);
    }

    @Override
    public DtoTestQCRemindConfig findByTestIdAndQcTypeAndQCGrade(String testId, Integer qcType, Integer qcGrade) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select a from DtoTestQCRemindConfig a,DtoTestQCRemindConfig2Test b ");
        stringBuilder.append(" where a.id=b.configId");
        stringBuilder.append(" and a.qcType=:qcType");
        stringBuilder.append(" and a.qcGrade=:qcGrade");
        stringBuilder.append(" and b.testId=:testId");
        values.put("qcType", qcType);
        values.put("qcGrade", qcGrade);
        values.put("testId", testId);
        List<DtoTestQCRemindConfig> testQCRemindConfigs = comRepository.find(stringBuilder.toString(), values);
        if (StringUtil.isNotNull(testQCRemindConfigs)) {
            //倒序排，优先默认数据
            return testQCRemindConfigs.stream().sorted(Comparator.comparing(DtoTestQCRemindConfig::getIsDefault).reversed()).findFirst().orElse(null);
        }
        return null;
    }
}