package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.ExamineType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 考核管理类别实体
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/09/14
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ExamineType")
@Where(clause = "isDeleted = 0 ")
@Data
@DynamicInsert
public class DtoExamineType extends ExamineType {

    private static final long serialVersionUID = 1L;

    @Transient
    private List<DtoExamineType> children;
}
