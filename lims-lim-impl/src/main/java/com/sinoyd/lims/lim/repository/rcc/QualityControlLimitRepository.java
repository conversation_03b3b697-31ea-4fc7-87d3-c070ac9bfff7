package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;

import java.util.Collection;
import java.util.List;

/**
 * TestQCRangeCopy数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/6/14
 * @since V100R001
 */
public interface QualityControlLimitRepository extends IBaseJpaPhysicalDeleteRepository<DtoQualityControlLimit, String>, LimsRepository<DtoQualityControlLimit,String> {
    /**
     * 根据测试项目Id查询数据
     *
     * @param testId 测试项目Id
     * @return 质控限值
     */
    List<DtoQualityControlLimit> findByTestId(String testId);

    /**
     * 根据测试项目Id集合查询数据
     *
     * @param testId 测试项目Id
     * @return 质控限值
     */
    List<DtoQualityControlLimit> findByTestIdIn(Collection<String> testId);

    /**
     * 根据测试项目Id和质控类型查询数据
     *
     * @param testId 测试项目Id
     * @param qcType 质控类型
     * @return 质控限值
     */
    List<DtoQualityControlLimit> findByTestIdAndQcType(String testId, Integer qcType);

    /**
     * 根据测试项目Id和质控类型查询数据
     *
     * @param testId  测试项目Id
     * @param qcTypes 质控类型
     * @return 质控限值
     */
    List<DtoQualityControlLimit> findByTestIdAndQcTypeIn(String testId, List<Integer> qcTypes);

    /**
     * 根据公式ids 获取数据集
     *
     * @param dispositionIds 公式ids
     * @return 数据集
     */
    List<DtoQualityControlLimit> findByDispositionIdIn(Collection<String> dispositionIds);

    /**
     * 查询公式不为空的配置
     *
     * @return 配置集
     */
    List<DtoQualityControlLimit> findByFormulaIsNotNull();

    /**
     * 根据质控类型+评判方式查找配置
     *
     * @param qcGrades 质控类型
     * @param qcTypes  质控种类
     * @param methods  评判方式
     * @return 数据集
     */
    List<DtoQualityControlLimit> findByQcGradeInAndQcTypeInAndJudgingMethodIn(Collection<Integer> qcGrades,
                                                                              Collection<Integer> qcTypes,
                                                                              Collection<Integer> methods);
}
