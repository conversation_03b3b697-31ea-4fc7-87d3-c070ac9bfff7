package com.sinoyd.lims.pro.util;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.service.ProService;

import java.math.BigDecimal;

/**
 * 计算工具类
 *
 * <AUTHOR>
 * @version V1.0.0 2024/8/19
 * @since V100R001
 */
public class CalculatedUtils {

    /**
     * 获取排放速率
     *
     * @param pfVal           排放浓度
     * @param bgl             标干流量
     * @param dimension       量纲
     * @param spdRoundingRule 修约规则
     * @return examLimitVal 检出限
     */
    public static String getPfSpeed(String pfVal, String bgl, String dimension, int[] spdRoundingRule, String examLimitVal, String defaultVal) {
        String pfSpeed = calPfSpeedNoRounding(pfVal, bgl, examLimitVal, dimension);
//        pfSpeed = proService.getDecimal(spdRoundingRule[0], spdRoundingRule[1], pfSpeed);
        pfSpeed = (!MathUtil.isNumeral(pfVal) && !pfVal.startsWith("＜")) ? pfVal : pfSpeed;
        pfSpeed = pfSpeed.equals("/") ? defaultVal : pfSpeed;
        return pfSpeed;
    }

    /**
     * 排放速率计算（不修约）
     *
     * @param tstVal  样品浓度
     * @param bgl     标干流量
     * @param lmtVal  检出限
     * @param dimName 量纲
     * @return 排放速率
     */
    public static String calPfSpeedNoRounding(String tstVal, String bgl, String lmtVal, String dimName) {
        EnumBase.EnumDimensionValue dimensionValue = EnumBase.EnumDimensionValue.getByCode(dimName);
        String countValue = "1000000";
        if (StringUtil.isNotNull(dimensionValue)) {
            countValue = dimensionValue.getValue();
        }
        return jsNoRounding(tstVal, bgl, lmtVal, countValue);
    }

    private static String jsNoRounding(String tstVal, String bgl, String lmtVal, String count) {
        boolean examFlag = Boolean.FALSE;
        String pfSpd = "/";
        //如果小于检出限，用检出限计算＜0.0015
        if (!MathUtil.isNumeral(tstVal) || (MathUtil.isNumeral(lmtVal) && new BigDecimal(tstVal).compareTo(new BigDecimal(lmtVal)) < 0)) {
            tstVal = lmtVal;
            examFlag = true;
        }
        if (MathUtil.isNumeral(tstVal) && MathUtil.isNumeral(bgl)) {
            final BigDecimal multiply = new BigDecimal(tstVal).multiply(new BigDecimal(bgl));
            if (multiply.compareTo(BigDecimal.ZERO) == 0) {
                pfSpd = "0";
            } else {
                pfSpd = multiply.divide(new BigDecimal(count), 20, BigDecimal.ROUND_HALF_EVEN).toString();
            }
            //不是带“E”的科学计数法格式并且小数位数超过三位则转为带“E”的科学计数法
            pfSpd = changeE(pfSpd);
            pfSpd = examFlag ? "＜" + pfSpd : pfSpd;
        }
        return pfSpd;
    }

    /**
     * 不是带“E”的科学计数法格式并且小数位数超过两位则转为带“E”的科学计数法
     * 调整 ：排放速率计算的，修约规则，现在代码中写死：“3,2”，并且出证结果小于0.1的情况下，排放速率需要强制转换成科学计数法
     *
     * @param pfSpd 原结果
     * @return 转换后的结果
     */
    public static String changeE(String pfSpd) {
//        if (!pfSpd.contains("E") && pfSpd.startsWith("0") && MathUtil.isNumeral(pfSpd)) {
//            //小数点的位置索引
//            int dotIdx = pfSpd.indexOf(".");
//            //小数位数
//            int decIdx = pfSpd.length() - 1 - dotIdx;
//            if (decIdx > 2) {
//                pfSpd = setSciFormat(pfSpd);
//            }
//        }
//        return pfSpd;
        ProService proService = SpringContextAware.getBean(ProService.class);
        return proService.getDecimal(3, 3, pfSpd, isPfSpdScience(pfSpd));
    }

    /**
     * 调整 ：排放速率计算的，修约规则，现在代码中写死：“3,2”，并且出证结果小于0.1的情况下，排放速率需要强制转换成科学计数法
     * 判断是否需要科学计数法
     *
     * @param pfSpd 排放速率
     * @return 是否计数法
     */
    public static Boolean isPfSpdScience(String pfSpd) {
        Boolean isScience = Boolean.FALSE;
        if (!pfSpd.contains("E") && pfSpd.startsWith("0") && MathUtil.isNumeral(pfSpd)) {
            BigDecimal pfSpdDec = new BigDecimal(pfSpd);
            if (pfSpdDec.compareTo(new BigDecimal("0.1")) < 0) {
                isScience = Boolean.TRUE;
            }
        }
        return isScience;
    }
}
