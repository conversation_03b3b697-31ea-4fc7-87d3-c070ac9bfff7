package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParams2ParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2Test;
import com.sinoyd.lims.lim.dto.customer.DtoRecordConfigParams;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.Params2ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsConfigRepository;
import com.sinoyd.lims.lim.repository.rcc.RecordConfig2TestRepository;
import com.sinoyd.lims.lim.service.Params2ParamsFormulaService;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import com.sinoyd.lims.lim.service.ParamsService;
import com.sinoyd.lims.lim.service.RecordConfigParamsConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 原始记录单相关表头参数或者数据参数
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Service
@Slf4j
public class RecordConfigParamsConfigServiceImpl implements RecordConfigParamsConfigService {

    @Autowired
    private ParamsConfigRepository paramsConfigRepository;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    private Params2ParamsFormulaRepository params2ParamsFormulaRepository;

    @Autowired
    @Lazy
    private Params2ParamsFormulaService params2ParamsFormulaService;

    @Autowired
    @Lazy
    private ParamsConfigService paramsConfigService;

    @Autowired
    private RecordConfig2TestRepository recordConfig2TestRepository;

    private ParamsService paramsService;

    @Override
    public List<DtoParamsConfig> findRecordConfigParamsConfigList(BaseCriteria criteria) {
        List<DtoParamsConfig> paramsConfigList = commonRepository.find("select a from DtoParamsConfig as a where 1=1", criteria);
        paramsConfigList = paramsConfigList.stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());
        return paramsConfigList;
    }


    @Override
    public DtoParamsConfig saveRecordConfigParams(DtoParamsConfig paramsConfig, Integer paramsConfigType) {
        String recordConfigId = paramsConfig.getObjId();
        //EnumParamsConfigType：6.原始记录单-数据参数 7.报告（预留）9.原始记录单-表头参数
        //存在的参数数据
        List<DtoParamsConfig> paramsConfigExistList = paramsConfigRepository.findByObjIdAndType(recordConfigId, paramsConfigType);
        //判断哪个参数重复了
        String alias = paramsConfig.getAlias();
        DtoParamsConfig item = paramsConfigExistList.stream().filter(p -> p.getAlias().equals(alias)).findFirst().orElse(null);
        if (StringUtil.isNotNull(item)) {
            throw new BaseException("别名有重复，请重新填写!");
        }
        //EnumParamsConfigType：6.原始记录单-数据参数 7.报告（预留）9.原始记录单-表头参数
        paramsConfig.setType(paramsConfigType);
        return paramsConfigRepository.save(paramsConfig);
    }

    @Transactional
    @Override
    public DtoParamsConfig updateRecordConfigParams(DtoParamsConfig paramsConfig, Integer paramsConfigType) {
        String recordConfigId = paramsConfig.getObjId();
        //EnumParamsConfigType：6.原始记录单-数据参数 7.报告（预留）9.原始记录单-表头参数
        //存在的参数数据
        List<DtoParamsConfig> paramsConfigExistList = paramsConfigRepository.findByObjIdAndType(recordConfigId, paramsConfigType);
        //判断哪个参数重复了
        String alias = paramsConfig.getAlias();
        DtoParamsConfig item = paramsConfigExistList.stream().filter(p -> p.getAlias().equals(alias)).findFirst().orElse(null);
        assert item != null;
        if (StringUtil.isNotNull(item)
                && !item.getId().equals(paramsConfig.getId())) {
            throw new BaseException("别名有重复，请重新填写!");
        }
        //EnumParamsConfigType：6.原始记录单-数据参数 7.报告（预留）9.原始记录单-表头参数
        paramsConfig.setType(paramsConfigType);
        String paramsConfigId = paramsConfig.getId();
        List<DtoParams2ParamsFormula> params2ParamsFormulas =
                params2ParamsFormulaRepository.findByRecordIdAndParamsConfigId(recordConfigId, paramsConfigId);
        //需要修改个性化的参数的别名及paramsId
        List<String> params2ParamsFormulaIds = params2ParamsFormulas.stream().map(DtoParams2ParamsFormula::getId).distinct().collect(Collectors.toList());
        if (params2ParamsFormulaIds.size() > 0) {
            paramsConfigRepository.updateAliasByObjectIds(params2ParamsFormulaIds, alias, paramsConfig.getParamsId());
        }
        return commonRepository.merge(paramsConfig);
    }

    @Transactional
    @Override
    public Integer deleteRecordConfigParams(List<String> ids) {
        Integer deleteRows = 0;
        if (ids.size() > 0) {
            //记录单相关的数据
            List<DtoParams2ParamsFormula> params2ParamsFormulas = params2ParamsFormulaRepository.findByParamsConfigIdIn(ids);
            //还要删除个性化的数据
            List<String> objectIds = params2ParamsFormulas.stream().map(DtoParams2ParamsFormula::getId).distinct().collect(Collectors.toList());
            if (objectIds.size() > 0) {
                params2ParamsFormulaService.logicDeleteById(objectIds);
                paramsConfigRepository.deleteByObjIdIn(objectIds);
            }
            deleteRows = paramsConfigService.logicDeleteById(ids);
        }
        return deleteRows;
    }


    @Override
    public List<DtoRecordConfigParams> findTestParamsByTestIds(List<String> testIds) {
        List<DtoRecordConfigParams> recordConfigParams = new ArrayList<>();
        if (testIds.size() > 0) {
            //相关的记录单ids
            List<DtoRecordConfig2Test> recordConfig2Tests = recordConfig2TestRepository.findByTestIdIn(testIds);
            //Date time4 = new Date();
            recordConfigParams = findTestParams(recordConfig2Tests, testIds);
//            Date time5 = new Date();
//            log.info("=======================================测试项目参数获取：" + (time5.getTime() - time4.getTime()) + "ms========================================");
        }
        return recordConfigParams;
    }


    @Override
    public List<DtoRecordConfigParams> findTestParamsByRecordId(List<String> testIds, String recordId) {
        //相关的记录单ids
        List<DtoRecordConfig2Test> recordConfig2Tests = recordConfig2TestRepository.findByRecordConfigId(recordId);
        return findTestParams(recordConfig2Tests, testIds);
    }


    /**
     * 跟就指定的测试项与原始记录单配置获取数据
     *
     * @param recordConfig2Tests 指定数据源
     * @param testIds            测试项目id
     * @return 返回数据
     */
    private List<DtoRecordConfigParams> findTestParams(List<DtoRecordConfig2Test> recordConfig2Tests, List<String> testIds) {
        List<DtoRecordConfigParams> recordConfigParams = new ArrayList<>();
        //原始记录单配置ids
        List<String> recordIds = recordConfig2Tests.stream().map(DtoRecordConfig2Test::getRecordConfigId).distinct().collect(Collectors.toList());
        if (recordIds.size() > 0) {
            //原始记录单配置的相关的参数
            List<DtoParamsConfig> paramsConfigList = paramsConfigRepository.findByObjIdInAndTypeAndIsDeletedFalse(recordIds, EnumLIM.EnumParamsConfigType.原始记录单表头参数.getValue());

            //每个测试项目个性化的数据
            Map<String, Object> values = new HashMap<>();
            StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.lim.dto.customer.DtoRecordConfigParams(");
            stringBuilder.append("a.id,b.recordId,b.objectId,b.paramsConfigId,a.alias,a.dimension,a.dimensionId,a.defaultValue,");
            stringBuilder.append("a.defaultControl,a.mostSignificance,a.mostDecimal,a.orderNum,a.isRequired,a.dataSource)");
            stringBuilder.append(" from DtoParamsConfig as a,");
            stringBuilder.append("DtoParams2ParamsFormula as b where 1=1");
            stringBuilder.append(" and a.objId=b.id and a.isDeleted = 0 and b.isDeleted = 0");
            stringBuilder.append(" and b.recordId in :recordIds");
            stringBuilder.append(" and b.objectId in :objectIds");
            values.put("recordIds", recordIds);
            values.put("objectIds", testIds);

            List<DtoRecordConfigParams> personParamsConfigList = commonRepository.find(stringBuilder.toString(), values);

            //需要判断参数是否配置了公式
            List<DtoParams2ParamsFormula> params2ParamsFormulas = params2ParamsFormulaRepository.findByRecordIdInAndObjectIdInAndIsDeletedFalse(recordIds, testIds);
            Set<String> paramIds = paramsConfigList.stream().map(DtoParamsConfig::getParamsId).collect(Collectors.toSet());
            List<DtoParams> paramsList = new ArrayList<>();
            if (paramIds.size() > 0) {
                paramsList = paramsService.findAll(paramIds);
            }
            for (String testId : testIds) {
                //获取包含当前测试项目的所有原始记录单
                if (StringUtil.isNotEmpty(recordConfig2Tests)) {
                    List<String> recordsIds = recordConfig2Tests.stream().filter(p -> p.getTestId().equals(testId)).map(DtoRecordConfig2Test::getRecordConfigId).collect(Collectors.toList());
                    //记录单相关的参数
                    List<DtoParamsConfig> recordParamsConfigList = paramsConfigList.stream().filter(p -> recordsIds.contains(p.getObjId())).collect(Collectors.toList());
                    for (DtoParamsConfig paramsConfig : recordParamsConfigList) {
                        DtoRecordConfigParams newParamsConfig = new DtoRecordConfigParams();
                        newParamsConfig.setId(paramsConfig.getId());
                        newParamsConfig.setTestId(testId);
                        newParamsConfig.setParamsName(paramsConfig.getAlias());
                        Optional<DtoParams> paramsOptional = paramsList.stream().filter(p -> paramsConfig.getParamsId().equals(p.getId())).findFirst();
                        paramsOptional.ifPresent(p -> newParamsConfig.setParamsName(p.getParamName()));
                        newParamsConfig.setAlias(paramsConfig.getAlias());
                        newParamsConfig.setDimension(paramsConfig.getDimension());
                        newParamsConfig.setDimensionId(paramsConfig.getDimensionId());
                        newParamsConfig.setRecordId(paramsConfig.getObjId());
                        newParamsConfig.setOrderNum(paramsConfig.getOrderNum());
                        newParamsConfig.setIsRequired(paramsConfig.getIsRequired());
                        newParamsConfig.setDefaultControl(paramsConfig.getDefaultControl());
                        newParamsConfig.setDataSource(paramsConfig.getDataSource());
                        newParamsConfig.setDefaultValue(paramsConfig.getDefaultValue());
                        //个性化配置，这边的parentId借用，个性化的parentId与主参数的id对应
                        DtoRecordConfigParams personParamsConfig = personParamsConfigList.stream().filter(p -> p.getTestId().equals(testId) && p.getRecordId().equals(paramsConfig.getObjId())
                                && p.getParentId().equals(paramsConfig.getId())).findFirst().orElse(null);
                        //主要是否有默认值的处理
                        if (StringUtil.isNotNull(personParamsConfig)) {
                            newParamsConfig.setDefaultValue(personParamsConfig.getDefaultValue());
                        }
                        DtoParams2ParamsFormula dtoParams2ParamsFormula = params2ParamsFormulas.stream().filter(p -> p.getRecordId().equals(paramsConfig.getObjId())
                                && p.getParamsConfigId().equals(paramsConfig.getId())
                                && p.getObjectId().equals(testId)).findFirst().orElse(null);
                        if (StringUtil.isNotNull(dtoParams2ParamsFormula)) {
                            newParamsConfig.setIsEnabled(dtoParams2ParamsFormula.getIsEnabled());
                            newParamsConfig.setFormula(dtoParams2ParamsFormula.getFormula());
                            newParamsConfig.setFormulaId(dtoParams2ParamsFormula.getId());
                        }
                        recordConfigParams.add(newParamsConfig);
                    }
                }
            }
        }
        return recordConfigParams;
    }

    @Autowired
    @Lazy
    public void setParamsService(ParamsService paramsService) {
        this.paramsService = paramsService;
    }
}
