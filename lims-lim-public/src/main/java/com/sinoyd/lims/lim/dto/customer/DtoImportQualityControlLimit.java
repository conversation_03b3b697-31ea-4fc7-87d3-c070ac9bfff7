package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import lombok.Data;

/**
 * 质控限值导入实体
 *
 * @version V1.0.0 2024/5/27
 * @author: hukq
 * @since V100R001
 */
@Data
public class DtoImportQualityControlLimit extends PoiBaseEntity {

    /**
     * 测试项目id
     */
    private String testId;

    @Excel(name = "检测类型", orderNum = "5", width = 25)
    private String sampleTypeName;

    @Excel(name = "分析项目", orderNum = "10", width = 25)
    private String redAnalyzeItemName;

    @Excel(name = "分析方法", orderNum = "20", width = 50)
    private String redAnalyzeMethodName;

    @Excel(name = "标准编号", orderNum = "30", width = 30)
    private String redCountryStandard;

    @Excel(name = "质控类型", orderNum = "40", width = 25)
    private String qcTypeName;

    @Excel(name = "评判方式", orderNum = "50", width = 25)
    private String judgeMethod;

    @Excel(name = "检查项", orderNum = "60", width = 25)
    private String checkItemStr;

    /**
     * 检查项
     */
    private Integer checkItem;

    /**
     * 检查项内容
     */
    private String checkItemOther;

    /**
     * 是否设定检查项
     */
    private Integer isCheckItem = 1;

    @Excel(name = "标准物质加入量范围", orderNum = "65", width = 25)
    private String standard;

    @Excel(name = "检查项范围", orderNum = "70", width = 25)
    private String rangeConfig;

    @Excel(name = "允许限值", orderNum = "80", width = 25)
    private String allowLimit;

    @Excel(name = "替代物名称", orderNum = "90", width = 25)
    private String substituteName;

    @Excel(name = "计算公式", orderNum = "100", width = 25)
    private String formula;

    @Excel(name = "技术说明", orderNum = "110", width = 25)
    private String description;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;
}
