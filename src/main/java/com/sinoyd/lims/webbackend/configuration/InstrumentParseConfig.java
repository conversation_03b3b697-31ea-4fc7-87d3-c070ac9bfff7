package com.sinoyd.lims.webbackend.configuration;

import com.sinoyd.base.constants.IBaseConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * 仪器解析数据源配置
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/26
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "instrumentParseEntityManagerFactory",
        transactionManagerRef = "instrumentParseTransactionManager",
        basePackages = {
                "com.sinoyd.lims.instrument.parse.repository"
        }
)
public class InstrumentParseConfig {

    private DataSource instrumentParseDataSourceConfig;

    private JpaProperties jpaProperties;

    @Bean(name = "instrumentParseEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean baseEntityManagerFactory(
            EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(instrumentParseDataSourceConfig)
                .packages(
                        "com.sinoyd.lims.instrument.parse.dto"
                )
                //后续如果要引用pro的那basePackages要继续加pro的底层数据包，如果引用到视图也要加进来
                .properties(jpaProperties.getHibernateProperties(instrumentParseDataSourceConfig))
                .persistenceUnit(IBaseConstants.INSTRUMENT_PARSE_PERSISTENCE_UNIT)
                .build();
    }

    @Bean(name = "instrumentParseTransactionManager")
    public PlatformTransactionManager baseTransactionManager(
            @Qualifier("instrumentParseEntityManagerFactory") EntityManagerFactory primaryEntityManagerFactory) {
        return new JpaTransactionManager(primaryEntityManagerFactory);
    }

    @Bean(name = "instrumentParseTransactionManager")
    PlatformTransactionManager transactionManagerSecondary(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(baseEntityManagerFactory(builder).getObject());
    }

    @Autowired
    @Qualifier("instrumentParseDataSource")
    public void setInstrumentParseDataSourceConfig(DataSource instrumentParseDataSourceConfig) {
        this.instrumentParseDataSourceConfig = instrumentParseDataSourceConfig;
    }

    @Autowired
    public void setJpaProperties(JpaProperties jpaProperties) {
        this.jpaProperties = jpaProperties;
    }
}