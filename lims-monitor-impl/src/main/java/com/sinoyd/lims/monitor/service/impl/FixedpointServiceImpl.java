package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.EvaluationCriteriaRepository;
import com.sinoyd.base.repository.rcc.EvaluationLevelRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.mapper.IUserMapper;
import com.sinoyd.boot.frame.sys.model.UserModel;
import com.sinoyd.common.utils.SortUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.monitor.criteria.FixedpointCriteria;
import com.sinoyd.lims.monitor.dto.lims.*;
import com.sinoyd.lims.monitor.dto.rcc.*;
import com.sinoyd.lims.monitor.entity.PointExtendData;
import com.sinoyd.lims.monitor.enums.EnumMonitor;
import com.sinoyd.lims.monitor.repository.lims.*;
import com.sinoyd.lims.monitor.repository.rcc.*;
import com.sinoyd.lims.monitor.service.FixedPointExpendService;
import com.sinoyd.lims.monitor.service.FixedpointService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * Fixedpoint操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
@Slf4j
public class FixedpointServiceImpl extends BaseJpaServiceImpl<DtoFixedpoint, String, FixedpointRepository> implements FixedpointService {

    @Autowired
    @Lazy
    private FixedPointExpendService fixedPointExpendService;

    @Autowired
    private FixedPointExpendRepository fixedPointExpendRepository;

    @Autowired
    private FixedPoint2TestRepository fixedPoint2TestRepository;

    @Autowired
    private CodeService codeService;

    @Autowired
    private EvaluationCriteriaRepository evaluationCriteriaRepository;

    @Autowired
    private EvaluationLevelRepository evaluationLevelRepository;

    @Autowired
    private WaterRepository waterRepository;

    @Autowired
    private FixedPoint2PointRepository fixedPoint2PointRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    private Property2PointRepository property2PointRepository;

    @Autowired
    private EnterpriseRepository enterpriseRepository;

    @Autowired
    private FixedPointPropertyRepository fixedPointPropertyRepository;

    @Autowired
    private StationRepository stationRepository;

    @Autowired
    private FixedpointRepository fixedpointRepository;

    @Autowired
    @Lazy
    private FixedpointService fixedpointService;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    private AreaService areaService;

    @Autowired
    private PointExtendDataRepository pointExtendDataRepository;

    @Autowired
    private PointExtendConfigRepository pointExtendConfigRepository;

    @Autowired
    private IUserMapper userMapper;

    @Autowired
    private PropertyPoint2TestRepository propertyPoint2TestRepository;

    @Autowired
    private AnalyzeMethodRepository analyzeMethodRepository;

    @Autowired
    private OicInformationRepository oicInformationRepository;

    @Autowired
    private OicInformation2TestRepository oicInformation2TestRepository;

    @Override
    public void findByPage(PageBean<DtoFixedpoint> pb, BaseCriteria fixedpointCriteria) {
        FixedpointCriteria criteria = (FixedpointCriteria) fixedpointCriteria;
        int pointType = criteria.getPointType(); //类型（枚举：环境质量 1 污染源 2）
        pb.setRowsPerPage(Integer.MAX_VALUE);
        if (EnumMonitor.EnumPointType.环境质量.getValue() == pointType) {
            List<UserModel> userModels = userMapper.findUserByLoginId(PrincipalContextUser.getPrincipal().getLoginId());
            List<String> orgIds = userModels.stream().map(UserModel::getOrgGuid).distinct().collect(Collectors.toList());
            criteria.setOrgIds(orgIds);
            queryForEnvQuality(pb, criteria);
        } else if (EnumMonitor.EnumPointType.污染源.getValue() == pointType) {
            queryForPollutant(pb, criteria);
        }
        List<DtoFixedpoint> list = pb.getData();
        if (StringUtil.isNotEmpty(list)) {
            list.sort((a, b) -> SortUtil.compareString(a.getPointName(), b.getPointName()));
            pb.setData(list);
        }
    }

    @Transactional
    @Override
    public DtoFixedpoint save(DtoFixedpoint entity) {
        //获取前端传的测站id，通过测站id去测站表查询测站名称
        DtoStation dtoStation = stationRepository.findOne(entity.getStationId());
        if (dtoStation != null) {
            entity.setStationName(dtoStation.getStname());
        }
        if (EnumMonitor.EnumPointType.污染源.getValue().equals(entity.getPointType())) {
            DtoEnterprise dtoEnterprise = enterpriseRepository.findOne(entity.getEnterpriseId());
            if (dtoEnterprise != null) {
                entity.setEnterpriseName(dtoEnterprise.getName());
            }
        }
        //处理经纬度
//        String location = entity.getLocation();
//        if (StringUtil.isNotEmpty(location)) {
//            if (location.contains("，") || location.contains(",")) {
//                location = location.replace("，", ",");
//                List<String> locations = Arrays.asList(location.split(","));
//                entity.setLon(locations.get(0));
//                entity.setLat(locations.get(1));
//            }
//        }
        super.save(entity);
        //存储拓展字段数据
        List<DtoPointExtendConfig> pointExtendDataList = entity.getPointExtendConfigs();
        if (StringUtil.isNotEmpty(pointExtendDataList)) {
            List<DtoPointExtendData> pointExtendData = new ArrayList<>();
            for (DtoPointExtendConfig config : pointExtendDataList) {
                DtoPointExtendData extendData = new DtoPointExtendData();
                extendData.setExtendConfigId(config.getId());
                extendData.setFixedPointId(entity.getId());
                extendData.setFiledName(config.getFiledName());
                extendData.setFiledAlias(config.getFiledAlias());
                extendData.setFiledValue(config.getFiledValue());
                extendData.setFiledValueDisplay(config.getFiledValueDisplay());
                extendData.setPointType(config.getPointType());
                extendData.setOrderNum(config.getOrderNum());
                pointExtendData.add(extendData);
            }
            if (StringUtil.isNotEmpty(pointExtendData)) {
                pointExtendDataRepository.save(pointExtendData);
            }
        }
        return entity;
    }

    @Override
    public DtoFixedpoint findOne(String id) {
        DtoFixedpoint dtoFixedpoint = super.findOne(id);
        //获取区域数据
        DtoArea dtoArea = areaService.findById(dtoFixedpoint.getAreaId());
        dtoFixedpoint.setAreaName(StringUtil.isNotNull(dtoArea) ? dtoArea.getAreaName() : "");
        //加载点位关联的信息
        if (EnumMonitor.EnumPointType.环境质量.getValue().equals(dtoFixedpoint.getPointType())) {
            loadRelationInfoForEnvQuality(dtoFixedpoint);
        } else {
            loadRelationInfoForPollutant(dtoFixedpoint);
        }
        return dtoFixedpoint;
    }

    @Override
    public Boolean duplicatePointCheck(DtoFixedpoint dtoFixedpoint) {
        Integer count;
        if (EnumMonitor.EnumPointType.环境质量.getValue().equals(dtoFixedpoint.getPointType())) { //环境质量
            if (StringUtil.isEmpty(dtoFixedpoint.getId())) { //新增
                count = repository.countByFolderTypeAndPointName(dtoFixedpoint.getFolderType(), dtoFixedpoint.getPointName());
            } else { //编辑
                count = repository.countByFolderTypeAndPointNameAndIdNot(dtoFixedpoint.getFolderType(), dtoFixedpoint.getPointName(),
                        dtoFixedpoint.getId());
            }
        } else { //污染源
            if (StringUtil.isEmpty(dtoFixedpoint.getId())) { //新增
                count = repository.countByFolderTypeAndPointNameAndEnterpriseId(dtoFixedpoint.getFolderType(), dtoFixedpoint.getPointName(),
                        dtoFixedpoint.getEnterpriseId());
            } else { //编辑
                count = repository.countByFolderTypeAndPointNameAndEnterpriseIdAndIdNot(dtoFixedpoint.getFolderType(), dtoFixedpoint.getPointName(),
                        dtoFixedpoint.getEnterpriseId(), dtoFixedpoint.getId());
            }
        }
        if (count != null && count.compareTo(0) > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    @Transactional
    @Override
    public void saveTest(DtoFixedpoint entity) {
        List<Map<String, Object>> testList = entity.getTestList();
        fixedPoint2TestRepository.deleteAllByFixedPointId(entity.getId());
        List<DtoFixedPoint2Test> dtoFixedPoint2Tests = new ArrayList<>();
        testList.forEach(test -> {
            String testId = (String) test.get("id");
            DtoFixedPoint2Test dto = new DtoFixedPoint2Test();
            dto.setFixedPointId(entity.getId());
            dto.setTestId(testId);
            dto.setTimesOrder((Integer) test.get("timesOrder"));
            dto.setSamplePeriod((Integer) test.get("samplePeriod"));
            dto.setProjectInterval((String) test.get("projectInterval"));
            dtoFixedPoint2Tests.add(dto);
        });
        fixedPoint2TestRepository.save(dtoFixedPoint2Tests);
    }

    @Transactional
    @Override
    public void selectRelationPoints(DtoFixedpoint dtoFixedpoint) {
        List<String> relationPointIds = dtoFixedpoint.getRelationPointIds();
        String currentPointId = dtoFixedpoint.getId();
        List<DtoFixedPoint2Point> fixedPoint2PointList = new ArrayList<>();
        for (String relationPointId : relationPointIds) {
            DtoFixedPoint2Point fixedPoint2Point = new DtoFixedPoint2Point();
            fixedPoint2Point.setFixedPointId(currentPointId);
            fixedPoint2Point.setPointId(relationPointId);
            fixedPoint2PointList.add(fixedPoint2Point);
        }
        fixedPoint2PointRepository.save(fixedPoint2PointList);
    }

    @Transactional
    @Override
    public void selectRelationProperty(DtoFixedpoint dtoFixedpoint) {
        List<String> relationPropertyIds = dtoFixedpoint.getRelationPropertyIds();
        String currentPointId = dtoFixedpoint.getId();
        List<DtoProperty2Point> property2PointList = new ArrayList<>();
        for (String relationPropertyId : relationPropertyIds) {
            DtoProperty2Point dtoProperty2Point = new DtoProperty2Point();
            dtoProperty2Point.setFixedPointId(currentPointId);
            dtoProperty2Point.setPropertyId(relationPropertyId);
            property2PointList.add(dtoProperty2Point);
        }
        property2PointRepository.save(property2PointList);
    }

    /**
     * 迁移点位拓展数据到新表中
     *
     * @param pointType 点位类型
     */
    @Override
    @Transactional
    public void moveFixedPointExtendData(Integer pointType) {
        List<DtoFixedpoint> allFixedPoint = repository.findAll();
        List<DtoFixedpoint> pointByType = allFixedPoint.stream().filter(p -> pointType.equals(p.getPointType())).collect(Collectors.toList());
        List<String> fixedPointIds = pointByType.stream().map(DtoFixedpoint::getId).distinct().collect(Collectors.toList());
        List<DtoFixedPointExpend> fixedPointExpendList = fixedPointExpendRepository.findByFixedPointIdIn(fixedPointIds);
        List<String> fixedPointIdsOfExtend = fixedPointExpendList.stream().map(DtoFixedPointExpend::getFixedPointId).distinct().collect(Collectors.toList());
        //转换之前，先清空所有的点位拓展数据，防止重复点击（不影响新增的数据）
        if (StringUtil.isNotEmpty(fixedPointIdsOfExtend)) {
            pointExtendDataRepository.deleteByFixedPointIdIn(fixedPointIdsOfExtend);
        }
        Map<String, DtoFixedPointExpend> fixedPointExpendMap = fixedPointExpendList.stream().collect(Collectors.toMap(DtoFixedPointExpend::getFixedPointId, dto -> dto));
        List<DtoPointExtendConfig> extendConfigs = loadPointExtendConfig(pointByType);
        List<DtoPointExtendData> extendDataList = new ArrayList<>();
        for (DtoFixedpoint fixedPoint : pointByType) {
            DtoFixedPointExpend fixedPointExpend = fixedPointExpendMap.getOrDefault(fixedPoint.getId(), null);
            Set<String> folderTypeOfPoint = getFolderTypes(fixedPoint.getFolderType());
            for (String folderTypeStr : folderTypeOfPoint) {
                List<DtoPointExtendConfig> configOfType = extendConfigs.stream().filter(p -> folderTypeStr.equals(p.getPointType())).collect(Collectors.toList());
                if (StringUtil.isNotNull(fixedPointExpend)) {
                    for (DtoPointExtendConfig config : configOfType) {
                        DtoPointExtendData extendData = new DtoPointExtendData();
                        extendData.setFixedPointId(fixedPoint.getId());
                        extendData.setPointType(folderTypeStr);
                        extendData.setExtendConfigId(config.getId());
                        extendData.setFiledName(config.getFiledName());
                        extendData.setFiledAlias(config.getFiledAlias());
                        String fieldValue = "";
                        List<Field> fields = new ArrayList<>();
                        Field[] declaredFields = fixedPointExpend.getClass().getDeclaredFields();
                        fields.addAll(Arrays.stream(declaredFields).collect(Collectors.toList()));
                        Field[] superDeclared = fixedPointExpend.getClass().getSuperclass().getDeclaredFields();
                        fields.addAll(Arrays.stream(superDeclared).collect(Collectors.toList()));
                        for (Field field : fields) {
                            field.setAccessible(true);
                            if (config.getFiledAlias().equals(field.getName())) {
                                try {
                                    if (StringUtil.isNotNull(field.get(fixedPointExpend))) {
                                        fieldValue = field.get(fixedPointExpend).toString();
                                    }
                                    break;
                                } catch (IllegalAccessException e) {
                                    log.error(e.getMessage(), e);
                                }
                            }
                        }
                        extendData.setFiledValue(fieldValue);
                        extendDataList.add(extendData);
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(extendDataList)) {
            pointExtendDataRepository.save(extendDataList);
        }
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> pointIds = (List<String>) ids;
        //删除关联点位
        List<DtoFixedPoint2Point> relationPoints = fixedPoint2PointRepository.findByFixedPointIdIn(pointIds);
        fixedPoint2PointRepository.delete(relationPoints);
        //删除关联测试项目
        List<DtoFixedPoint2Test> relationTests = fixedPoint2TestRepository.findByFixedPointIdIn(pointIds);
        fixedPoint2TestRepository.delete(relationTests);
        //删除关联监测计划
        List<DtoProperty2Point> property2PointList = property2PointRepository.findByFixedPointIdIn(pointIds);
        property2PointRepository.delete(property2PointList);
        //删除点位
        return super.logicDeleteById(ids);
    }

    /**
     * 获取点位的评价标准对象列表
     *
     * @param dataList 点位数据
     * @return 点位的评价标准对象列表
     */
    @Override
    public List<DtoEvaluationCriteria> loadEvaluationCriteria(List<DtoFixedpoint> dataList) {
        //评价标准id列表
        List<String> evaluationIdList = dataList.parallelStream().map(DtoFixedpoint::getEvaluationId).distinct().collect(Collectors.toList());
        List<DtoEvaluationCriteria> evaluationCriteriaList = new ArrayList<>();
        if (StringUtil.isNotEmpty(evaluationIdList)) {
            evaluationCriteriaList = evaluationCriteriaRepository.findAll(evaluationIdList);
        }
        return evaluationCriteriaList;
    }

    /**
     * 获取点位的评价等级对象列表
     *
     * @param dataList 点位数据
     * @return 点位的评价等级对象列表
     */
    @Override
    public List<DtoEvaluationLevel> loadEvaluationLevel(List<DtoFixedpoint> dataList) {
        List<String> evaluationLevelIdList = dataList.parallelStream().map(DtoFixedpoint::getEvaluationLevelId).distinct().collect(Collectors.toList());
        evaluationLevelIdList.removeIf(e -> !StringUtils.isNotNullAndEmpty(e));
        List<DtoEvaluationLevel> evaluationLevelList = new ArrayList<>();
        if (StringUtil.isNotEmpty(evaluationLevelIdList)) {
            evaluationLevelList = evaluationLevelRepository.findAll(evaluationLevelIdList);
        }
        return evaluationLevelList;
    }

    /**
     * 获取点位的所属水体列表
     *
     * @param fixedPointExpendList 点位扩展信息
     * @return 点位的所属水体列表
     */
    @Override
    public List<DtoWater> loadWater(List<DtoFixedPointExpend> fixedPointExpendList) {
        List<DtoWater> waterList = new ArrayList<>();
        List<String> waterIdList = fixedPointExpendList.parallelStream().map(DtoFixedPointExpend::getWaterId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(waterIdList)) {
            waterList = waterRepository.findAll(waterIdList);
        }
        return waterList;
    }

    /**
     * 获取点位的扩展信息
     *
     * @param dataList 点位数据
     * @return 点位的扩展信息
     */
    @Override
    public List<DtoFixedPointExpend> loadFixedPointExpendList(List<DtoFixedpoint> dataList) {
        List<String> fixedPointIdList = dataList.parallelStream().map(DtoFixedpoint::getId).collect(Collectors.toList());
        List<DtoFixedPointExpend> fixedPointExpendList = fixedPointExpendRepository.findByFixedPointIdIn(fixedPointIdList);
        return StringUtil.isNotEmpty(fixedPointExpendList) ? fixedPointExpendList : new ArrayList<>();
    }

    @Transactional
    @Override
    public void deleteTest(DtoFixedpoint dtoFixedpoint) {
        List<String> testIds = dtoFixedpoint.getTestIds();
        String fixedPointId = dtoFixedpoint.getId();
        List<DtoFixedPoint2Test> dtoFixedPoint2Tests = new ArrayList<>();
        List<DtoFixedPoint2Test> dataList = fixedPoint2TestRepository.findByFixedPointId(fixedPointId);
        testIds.parallelStream().forEach(id -> {
            Optional<DtoFixedPoint2Test> optional = dataList.parallelStream().filter(p -> id.equals(p.getTestId()))
                    .findFirst();
            optional.ifPresent(dto -> dtoFixedPoint2Tests.add(dto));
        });
        fixedPoint2TestRepository.delete(dtoFixedPoint2Tests);
    }

    @Transactional
    @Override
    public void deleteRelationProperty(DtoFixedpoint dtoFixedpoint) {
        List<String> relationPropertyIds = dtoFixedpoint.getRelationPropertyIds();
        String fixedPointId = dtoFixedpoint.getId();
        List<DtoProperty2Point> relationPropertyList = new ArrayList<>();
        List<DtoProperty2Point> dataList = property2PointRepository.findByFixedPointId(fixedPointId);
        relationPropertyIds.parallelStream().forEach(id -> {
            Optional<DtoProperty2Point> optional = dataList.parallelStream().filter(p -> id.equals(p.getPropertyId()))
                    .findFirst();
            optional.ifPresent(dto -> relationPropertyList.add(dto));
        });
        property2PointRepository.delete(relationPropertyList);
    }

    @Transactional
    @Override
    public DtoFixedpoint update(DtoFixedpoint entity) {
        //获取前端传的测站id，通过测站id去测站表查询测站名称
        DtoStation dtoStation = stationRepository.findOne(entity.getStationId());
        entity.setStationName(dtoStation.getStname());
        //处理经纬度
//        String location = entity.getLocation();
//        if (StringUtil.isNotEmpty(location)) {
//            if (location.contains("，") || location.contains(",")) {
//                location = location.replace("，", ",");
//                List<String> locations = Arrays.asList(location.split(","));
//                entity.setLon(locations.get(0));
//                entity.setLat(locations.get(1));
//            }
//        }
        List<DtoPointExtendData> extendDataOfPoint = pointExtendDataRepository.findByFixedPointId(entity.getId());
        if (StringUtil.isNotEmpty(extendDataOfPoint)) {
            pointExtendDataRepository.delete(extendDataOfPoint);
        }
        //存储拓展字段数据
        List<DtoPointExtendConfig> pointExtendDataList = entity.getPointExtendConfigs();
        if (StringUtil.isNotEmpty(pointExtendDataList)) {
            List<DtoPointExtendData> pointExtendData = new ArrayList<>();
            for (DtoPointExtendConfig config : pointExtendDataList) {
                DtoPointExtendData extendData = new DtoPointExtendData();
                extendData.setExtendConfigId(config.getId());
                extendData.setFixedPointId(entity.getId());
                extendData.setFiledName(config.getFiledName());
                extendData.setFiledAlias(config.getFiledAlias());
                extendData.setFiledValue(config.getFiledValue());
                extendData.setFiledValueDisplay(config.getFiledValueDisplay());
                extendData.setPointType(config.getPointType());
                extendData.setOrderNum(config.getOrderNum());
                pointExtendData.add(extendData);
            }
            if (StringUtil.isNotEmpty(pointExtendData)) {
                pointExtendDataRepository.save(pointExtendData);
            }
        }
        //编辑点位信息
        return super.update(entity);
    }

    /**
     * 环境质量点位列表查询
     *
     * @param pb       分页对象
     * @param criteria 查询条件
     */
    protected void queryForEnvQuality(PageBean<DtoFixedpoint> pb, FixedpointCriteria criteria) {
        pb.setEntityName("DtoFixedpoint p ");
        pb.setSelect("select p ");
        if (StringUtil.isNotEmpty(criteria.getAreaId()) && !UUIDHelper.GUID_EMPTY.equals(criteria.getAreaId())) {
            List<String> areaIds = new ArrayList<>();
            areaIds = getAreaIds(areaIds, criteria.getAreaId());
            criteria.setAreaIds(areaIds);
        }
        super.findByPage(pb, criteria);
        List<DtoFixedpoint> dataList = pb.getData();
        //查询结果不为空
        if (StringUtil.isNotEmpty(dataList)) {
            //点位类型字典列表
            List<DtoCode> folderTypeCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.ENV_QUALITY_FOLDER_TYPE);
            //等级字典列表
            List<DtoCode> levelCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.FOLDER_LEVEL);
            //评价标准列表
            List<DtoEvaluationCriteria> evaluationCriteriaList = loadEvaluationCriteria(dataList);
            //评价等级id列表
            List<DtoEvaluationLevel> evaluationLevelList = loadEvaluationLevel(dataList);
            //水体列表
            List<DtoWater> waterList = loadWaterByFixedPoint(dataList);
            for (DtoFixedpoint dto : dataList) {
                //加在点位类型名称
                loadFolderTypeNames(dto, folderTypeCodeList);
                //冗余点位等级名称
                Optional<DtoCode> levelOptional = levelCodeList.parallelStream().filter(p -> p.getDictCode().equals(dto.getLevel()))
                        .findFirst();
                levelOptional.ifPresent(dtoCode -> dto.setLevelName(dtoCode.getDictName()));
                //冗余评价标准名称
                Optional<DtoEvaluationCriteria> evaluationCriteriaOptional = evaluationCriteriaList.parallelStream().filter(p -> p.getId().equals(dto.getEvaluationId()))
                        .findFirst();
                evaluationCriteriaOptional.ifPresent(dtoEvaluationCriteria -> dto.setEvaluationName(dtoEvaluationCriteria.getName()));
                //冗余评价等级名称
                Optional<DtoEvaluationLevel> evaluationLevelOptional = evaluationLevelList.parallelStream().filter(p -> p.getId().equals(dto.getEvaluationLevelId()))
                        .findFirst();
                evaluationLevelOptional.ifPresent(dtoEvaluationLevel -> dto.setEvaluationLvlName(dtoEvaluationLevel.getName()));
                //冗余所属水体名称
                Optional<DtoWater> waterOptional = waterList.parallelStream().filter(p -> p.getId().equals(dto.getWaterId()))
                        .findFirst();
                waterOptional.ifPresent(dtoWater -> dto.setWaterName(dtoWater.getWaterName()));
                //加载组合经纬度
                loadLocation(dto);
            }
        }
    }

    /**
     * 获取点位的所属水体列表(根据点位数据获取)
     *
     * @param fixedPointList 点位信息
     * @return 点位的所属水体列表
     */
    @Override
    public List<DtoWater> loadWaterByFixedPoint(List<DtoFixedpoint> fixedPointList) {
        List<String> fixedPointIds = fixedPointList.stream().map(DtoFixedpoint::getId).distinct().collect(Collectors.toList());
        List<DtoPointExtendData> pointExtendDataList = pointExtendDataRepository.findByFixedPointIdIn(fixedPointIds);
        List<String> waterIds = new ArrayList<>();
        for (DtoPointExtendData extendData : pointExtendDataList) {
            if ("waterId".equals(extendData.getFiledAlias()) && StringUtil.isNotEmpty(extendData.getFiledValue())) {
                waterIds.add(extendData.getFiledValue());
            }
        }
        //冗余水体Id
        for (DtoFixedpoint fixedPoint : fixedPointList) {
            if (StringUtil.isNotEmpty(pointExtendDataList)) {
                DtoPointExtendData waterData = pointExtendDataList.stream()
                        .filter(p -> fixedPoint.getId().equals(p.getFixedPointId()) && "waterId".equals(p.getFiledAlias()))
                        .findFirst().orElse(null);
                fixedPoint.setWaterId(StringUtil.isNull(waterData) ? "" : StringUtil.isNotNull(waterData.getFiledValue()) ? waterData.getFiledValue() : "");
            }
        }
        return StringUtil.isNotEmpty(waterIds) ? waterRepository.findAll(waterIds) : new ArrayList<>();
    }

    /**
     * 获取点位拓展字段配置数据
     *
     * @param fixedPoints 点位数据
     * @return 字段配置数据
     */
    protected List<DtoPointExtendConfig> loadPointExtendConfig(List<DtoFixedpoint> fixedPoints) {
        //拓展字段配置数据
        List<String> folderTypeList = fixedPoints.stream().map(DtoFixedpoint::getFolderType).collect(Collectors.toList());
        Set<String> folderTypeSet = getFolderTypes(folderTypeList);
        List<DtoPointExtendConfig> extendConfigs = pointExtendConfigRepository.findByPointTypeIn(folderTypeSet);
        return extendConfigs;
    }

    /**
     * 获取点位对应的点位拓展字段数据
     *
     * @param fixedPoints 点位数据
     * @return 点位拓展字段数据
     */
    private Map<String, List<DtoPointExtendData>> loadPointExtendDataMap(List<DtoFixedpoint> fixedPoints) {
        Set<String> fixedPointIds = fixedPoints.stream().map(DtoFixedpoint::getId).collect(Collectors.toSet());
        //拓展字段数据
        List<DtoPointExtendData> extendData = pointExtendDataRepository.findByFixedPointIdIn(fixedPointIds);
        return extendData.stream().collect(Collectors.groupingBy(PointExtendData::getFixedPointId));
    }


    /**
     * 加载点位拓展字段数据
     *
     * @param dto             点位数据
     * @param extendConfigs   点位拓展字段配置数据
     * @param extendDataGroup 点位拓展字段数据
     */
    private void loadFixedPointExtendData(DtoFixedpoint dto, List<DtoPointExtendConfig> extendConfigs, Map<String, List<DtoPointExtendData>> extendDataGroup) {
        List<String> folderTypeOfPoint = new ArrayList<>(getFolderTypes(dto.getFolderType()));
        List<DtoPointExtendConfig> extendConfigsOfPoint = extendConfigs.stream().filter(p -> folderTypeOfPoint.contains(p.getPointType())).collect(Collectors.toList());
        List<DtoPointExtendData> extendDataOfPoint = extendDataGroup.getOrDefault(dto.getId(), new ArrayList<>());
        for (DtoPointExtendConfig config : extendConfigsOfPoint) {
            DtoPointExtendData folderTypeData = extendDataOfPoint.stream().filter(p -> config.getId().equals(p.getExtendConfigId())).findFirst().orElse(null);
            config.setFiledValue(StringUtil.isNotNull(folderTypeData) ? folderTypeData.getFiledValue() : "");
            config.setFiledValueDisplay(StringUtil.isNotNull(folderTypeData) ? folderTypeData.getFiledValueDisplay() : "");
        }
        if (StringUtil.isNotEmpty(extendConfigsOfPoint)) {
            extendConfigsOfPoint.sort(Comparator.comparing(DtoPointExtendConfig::getPointType).thenComparing(DtoPointExtendConfig::getOrderNum).reversed());
        }
        dto.setPointExtendConfigs(extendConfigsOfPoint);
    }

    /**
     * 污染源点位列表查询
     *
     * @param pb       分页对象
     * @param criteria 查询条件
     */
    protected void queryForPollutant(PageBean<DtoFixedpoint> pb, FixedpointCriteria criteria) {
        pb.setEntityName("DtoFixedpoint a ,DtoEnterprise b");
        pb.setSelect("select new com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint(a.id, b.name,a.folderType,a.pointCode,a.pointName,a.stationName,a.villageCode,a.isEnabled,a.cycleOrder,a.timesOrder,a.evaluationId,a.orderNum,a.sampleTypeId ) ");
        super.findByPage(pb, criteria);
        List<DtoFixedpoint> dataList = pb.getData();
        //查询结果不为空
        if (StringUtil.isNotEmpty(dataList)) {
            //点位类型字典列表
            List<DtoCode> folderTypeCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.POLLUTANT_FOLDER_TYPE);
            //获取id的集合
            List<String> ids = dataList.parallelStream().map(DtoFixedpoint::getId).collect(Collectors.toList());
            //DtoFixedPoint2Test表的集合
            List<DtoFixedPoint2Test> dtoFixedPoint2Tests = fixedPoint2TestRepository.findByFixedPointIdIn(ids);
            if (StringUtil.isNotEmpty(dtoFixedPoint2Tests)) {
                List<String> testIds = dtoFixedPoint2Tests.parallelStream().map(DtoFixedPoint2Test::getTestId).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(testIds)) {
                    //测试项目的集合
                    List<DtoTest> testList = testService.findAllDeleted(testIds);
                    List<String> analyzeMethodIds = testList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
                    List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(analyzeMethodIds) ? analyzeMethodRepository.findAllDeleted(analyzeMethodIds) : new ArrayList<>();
                    if (StringUtil.isNotEmpty(testList)) {
                        for (DtoFixedpoint dto : dataList) {
                            //获取DtoFixedPoint2Test表getObjectId和DtoTest表的ID比较得到关联的集合
                            List<DtoFixedPoint2Test> dtoFixedPoint2TestsList = dtoFixedPoint2Tests.parallelStream()
                                    .filter(p -> p.getFixedPointId().equals(dto.getId())).collect(Collectors.toList());
                            if (StringUtil.isNotEmpty(dtoFixedPoint2TestsList)) {
                                dtoFixedPoint2TestsList.stream().max(Comparator.comparing(DtoFixedPoint2Test::getTimesOrder)).ifPresent(t -> dto.setTimesOrder(t.getTimesOrder()));
                                //获取所有关联集合的testId
                                List<String> tests = dtoFixedPoint2TestsList.parallelStream().map(DtoFixedPoint2Test::getTestId).collect(Collectors.toList());
                                if (StringUtil.isNotEmpty(tests)) {
                                    //放测试项目名称的集合
                                    List<DtoTest> dtoTests = testList.parallelStream().filter(p -> tests.contains(p.getId())).collect(Collectors.toList());
                                    if (StringUtil.isNotEmpty(dtoTests)) {
                                        List<String> name = dtoTests.parallelStream().map(DtoTest::getRedAnalyzeItemName).collect(Collectors.toList());
                                        dto.setAnalyzeItems(String.join(",", name));// 将名字集合转换成字符串
                                        dto.setAnalyzeItemsCount(name.size());//因子个数
                                        List<String> analyzeMethodIdsByTest = dtoTests.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
                                        List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodList.stream().filter(p -> analyzeMethodIdsByTest.contains(p.getId())).collect(Collectors.toList());
                                        // 当点位关联测试项目状态，存在“停用”、“作废”、“删除”的测试项目时，需要测试项目提示
                                        if (analyzeMethods.stream().anyMatch(p -> EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(p.getStatus())
                                                || EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(p.getStatus()) || p.getIsDeleted()) ||
                                                dtoTests.stream().anyMatch(DtoTest::getIsDeleted)) {
                                            dto.setIsTestTip(Boolean.TRUE);
                                        }
                                    }
                                }
                            }
                            //加载组合经纬度
                            loadLocation(dto);
                        }
                    }
                }
            }
            dataList.forEach(p -> {
                //冗余点位类型名称
                Optional<DtoCode> folderTypeOptional = folderTypeCodeList.parallelStream().filter(code -> code.getDictCode().equals(p.getFolderType()))
                        .findFirst();
                folderTypeOptional.ifPresent(dtoCode -> p.setFolderTypeName(dtoCode.getDictName()));
            });
            //填充样品类型名称
            List<String> sampleTypeIds = dataList.stream()
                    .filter(d -> StringUtil.isNotEmpty(d.getSampleTypeId()))
                    .map(DtoFixedpoint::getSampleTypeId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(sampleTypeIds)) {
                List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll(sampleTypeIds);
                for (DtoFixedpoint dtoFixedpoint : dataList) {
                    Optional<DtoSampleType> sampleType = sampleTypes.stream().filter(s -> s.getId().equals(dtoFixedpoint.getSampleTypeId())).findFirst();
                    sampleType.ifPresent(s -> dtoFixedpoint.setSampleTypeName(s.getTypeName()));
                }
            }
        }
    }

    /**
     * 获取点位类型字段集合(单个点位)
     *
     * @param folderType 点位类型字符串
     * @return 点位类型字段集合
     */
    protected Set<String> getFolderTypes(String folderType) {
        return getFolderTypes(Stream.of(folderType).collect(Collectors.toList()));
    }

    /**
     * 获取点位类型字段集合(多个点位)
     *
     * @param folderTypeStrList 点位类型字符串集合
     * @return 点位类型字段集合
     */
    private Set<String> getFolderTypes(List<String> folderTypeStrList) {
        //拓展字段配置数据
        Set<String> folderTypeSet = new HashSet<>();
        for (String folderType : folderTypeStrList) {
            if (folderType.replace("，", ",").contains(",")) {
                List<String> folderTypes = Arrays.stream(folderType.split(",")).collect(Collectors.toList());
                folderTypeSet.addAll(folderTypes);
            } else {
                folderTypeSet.add(folderType);
            }
        }
        return folderTypeSet;
    }

    /**
     * 处理点位类型名称
     *
     * @param dtoFixedpoint      点位数据
     * @param folderTypeCodeList 点位类型常量集合
     */
    @Override
    public void loadFolderTypeNames(DtoFixedpoint dtoFixedpoint, List<DtoCode> folderTypeCodeList) {
        Set<String> folderTypes = getFolderTypes(dtoFixedpoint.getFolderType());
        //冗余点位类型名称
        List<DtoCode> folderTypeCodes = folderTypeCodeList.parallelStream().filter(p -> folderTypes.contains(p.getDictCode())).sorted(Comparator.comparing(DtoCode::getSortNum))
                .collect(Collectors.toList());
        List<String> folderTypeNames = folderTypeCodes.stream().map(DtoCode::getDictName).distinct().collect(Collectors.toList());
        dtoFixedpoint.setFolderTypeName(String.join(",", folderTypeNames));
    }

    /**
     * 加载污染源点位关联信息(比如测试项目)
     *
     * @param dtoFixedpoint 点位对象
     */
    private void loadRelationInfoForPollutant(DtoFixedpoint dtoFixedpoint) {
        //拓展字段数据
        Map<String, List<DtoPointExtendData>> extendDataGroup = loadPointExtendDataMap(Stream.of(dtoFixedpoint).collect(Collectors.toList()));
        //拓展字段配置数据
        List<DtoPointExtendConfig> extendConfigs = this.loadPointExtendConfig(Stream.of(dtoFixedpoint).collect(Collectors.toList()));
        //加载点位扩展信息
        this.loadFixedPointExtendData(dtoFixedpoint, extendConfigs, extendDataGroup);
        //通过企业id查到企业，拿到企业名称
        DtoEnterprise dtoEnterprise = enterpriseRepository.findOne(dtoFixedpoint.getEnterpriseId());
        dtoFixedpoint.setEnterpriseName(dtoEnterprise.getName());
        //污染源点位类型字典列表
        List<DtoCode> folderTypeCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.POLLUTANT_FOLDER_TYPE);
        for (DtoCode dtoCode : folderTypeCodeList) {
            if (dtoCode.getDictCode().equals(dtoFixedpoint.getFolderType())) {
                dtoFixedpoint.setFolderTypeName(dtoCode.getDictName());
            }
        }
        List<DtoFixedpoint> dtoFixedpoints = fixedpointRepository.findAll();
        List<DtoEvaluationCriteria> evaluationCriteriaList = fixedpointService.loadEvaluationCriteria(dtoFixedpoints);
        //冗余评价标准名称
        Optional<DtoEvaluationCriteria> evaluationCriteriaOptional = evaluationCriteriaList.parallelStream().filter(p -> p.getId().equals(dtoFixedpoint.getEvaluationId()))
                .findFirst();
        evaluationCriteriaOptional.ifPresent(dtoEvaluationCriteria -> dtoFixedpoint.setEvaluationName(dtoEvaluationCriteria.getName()));
        //评价等级id列表
        List<DtoEvaluationLevel> evaluationLevelList = loadEvaluationLevel(dtoFixedpoints);
        //冗余评价等级名称
        Optional<DtoEvaluationLevel> evaluationLevelOptional = evaluationLevelList.parallelStream().filter(p -> p.getId().equals(dtoFixedpoint.getEvaluationLevelId()))
                .findFirst();
        evaluationLevelOptional.ifPresent(dtoEvaluationLevel -> dtoFixedpoint.setEvaluationLvlName(dtoEvaluationLevel.getName()));
        //等级字典列表
        List<DtoCode> levelCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.FOLDER_LEVEL);
        //冗余点位等级名称
        Optional<DtoCode> levelOptional = levelCodeList.parallelStream().filter(p -> p.getDictCode().equals(dtoFixedpoint.getLevel()))
                .findFirst();
        levelOptional.ifPresent(dtoCode -> dtoFixedpoint.setLevelName(dtoCode.getDictName()));
        if (StringUtil.isNotEmpty(dtoFixedpoint.getSampleTypeId())) {
            //拿到检测类型id的类型名称
            DtoSampleType dtoSampleType = sampleTypeRepository.findOne(dtoFixedpoint.getSampleTypeId());
            if (dtoSampleType != null) {
                dtoFixedpoint.setSampleTypeName(dtoSampleType.getTypeName());
            }
        }
        loadRelationTest(dtoFixedpoint);
        //加载组合经纬度
        loadLocation(dtoFixedpoint);
    }

    /**
     * 加载环境质量点位关联信息(比如关联点位、关联监测计划)
     *
     * @param dtoFixedpoint 点位对象
     */
    private void loadRelationInfoForEnvQuality(DtoFixedpoint dtoFixedpoint) {
        //拓展字段数据
        Map<String, List<DtoPointExtendData>> extendDataGroup = loadPointExtendDataMap(Stream.of(dtoFixedpoint).collect(Collectors.toList()));
        //拓展字段配置数据
        List<DtoPointExtendConfig> extendConfigs = loadPointExtendConfig(Stream.of(dtoFixedpoint).collect(Collectors.toList()));
        //加载关联点位信息
        loadRelationPoint(dtoFixedpoint);
        //加载关联监测计划信息
        loadRelationPlan(dtoFixedpoint);
        //加载关联测试项目信息
        loadRelationTest(dtoFixedpoint);
        //加载组合经纬度
        loadLocation(dtoFixedpoint);
        //加载关联点位信息
        loadFixedPointExtendData(dtoFixedpoint, extendConfigs, extendDataGroup);
    }

    /**
     * 加载关联点位信息
     *
     * @param dtoFixedpoint 点位对象
     */
    private void loadRelationPoint(DtoFixedpoint dtoFixedpoint) {
        List<DtoFixedPoint2Point> fixedPoint2PointList = fixedPoint2PointRepository.findByFixedPointId(dtoFixedpoint.getId());
        if (StringUtil.isNotEmpty(fixedPoint2PointList)) {
            List<String> relationPointIds = fixedPoint2PointList.parallelStream().map(DtoFixedPoint2Point::getPointId)
                    .distinct().collect(Collectors.toList());
            List<DtoFixedpoint> relationPointList = repository.findAll(relationPointIds);
            //水体列表
            List<DtoWater> waterList = loadWaterByFixedPoint(relationPointList);
            for (DtoFixedpoint fixedpoint : relationPointList) {
                Optional<DtoWater> waterOptional = waterList.parallelStream().filter(p -> p.getId().equals(fixedpoint.getWaterId()))
                        .findFirst();
                waterOptional.ifPresent(dtoWater -> fixedpoint.setWaterName(dtoWater.getWaterName()));
            }
            //点位类型字典列表
            List<DtoCode> folderTypeCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.ENV_QUALITY_FOLDER_TYPE);
            relationPointList.parallelStream().forEach(fixedpoint -> loadFolderTypeNames(fixedpoint, folderTypeCodeList));
            fixedPoint2PointList.forEach(p2p -> {
                Optional<DtoFixedpoint> optional = relationPointList.parallelStream()
                        .filter(p -> p.getId().equals(p2p.getPointId())).findFirst();
                optional.ifPresent(p -> {
                    p2p.setPointCode(p.getPointCode());
                    p2p.setStationName(p.getStationName());
                    p2p.setPointName(p.getPointName());
                    p2p.setIsEnabled(p.getIsEnabled());
                    p2p.setPointCreateDate(p.getCreateDate());
                    p2p.setFolderType(p.getFolderType());
                    p2p.setFolderTypeName(p.getFolderTypeName());
                    p2p.setWaterName(p.getWaterName());
                });
            });
            //按照点位创建时间倒序
            fixedPoint2PointList.sort(Comparator.comparing(DtoFixedPoint2Point::getPointCreateDate, Comparator.reverseOrder()));
            dtoFixedpoint.setRelationPointList(fixedPoint2PointList);
        }
    }

    /**
     * 加载关联监测计划信息
     *
     * @param dtoFixedpoint 点位对象
     */
    protected void loadRelationPlan(DtoFixedpoint dtoFixedpoint) {
        List<DtoProperty2Point> property2PointList = property2PointRepository.findByFixedPointId(dtoFixedpoint.getId());
        if (StringUtil.isNotEmpty(property2PointList)) {
            List<String> propertyIds = property2PointList.parallelStream().map(DtoProperty2Point::getPropertyId).distinct().collect(Collectors.toList());
            List<DtoFixedPointProperty> propertyList = fixedPointPropertyRepository.findByIdIn(propertyIds);
            //获取监测子计划的parentId
            List<String> parentIds = propertyList.parallelStream().map(DtoFixedPointProperty::getParentId).distinct().collect(Collectors.toList());
            //获取监测计划
            List<DtoFixedPointProperty> parentPropertyList = fixedPointPropertyRepository.findByIdIn(parentIds);
            //遍历冗余信息供页面显示
            for (DtoProperty2Point p2p : property2PointList) {
                Optional<DtoFixedPointProperty> propertyOptional = propertyList.parallelStream()
                        .filter(p -> p.getId().equals(p2p.getPropertyId())).findFirst();
                propertyOptional.ifPresent(property -> {
                    //冗余监测计划信息
                    p2p.setChildPropertyName(property.getPropertyName() + "(" + property.getMonth() + ")");
                    p2p.setYear(property.getYear());
                    p2p.setChildPropertyOrderNum(property.getOrderNum());
                    //冗余父监测计划信息
                    Optional<DtoFixedPointProperty> parentOptional = parentPropertyList.parallelStream()
                            .filter(p -> p.getId().equals(property.getParentId())).findFirst();
                    parentOptional.ifPresent(parent -> {
                        p2p.setParentPropertyName(parent.getPropertyName());
                        p2p.setParentPropertyOrderNum(parent.getOrderNum());
                    });
                });
            }
            property2PointList.sort(Comparator.comparing(DtoProperty2Point::getParentPropertyOrderNum, Comparator.reverseOrder())
                    .thenComparing(DtoProperty2Point::getChildPropertyOrderNum, Comparator.reverseOrder()));
            dtoFixedpoint.setRelationPropertyList(property2PointList);
        }
    }

    /**
     * 加载点位关联的测试项目信息
     *
     * @param dtoFixedpoint 点位实体
     */
    private void loadRelationTest(DtoFixedpoint dtoFixedpoint) {
        //通过点位信息的主键查询DtoFixedPoint2Test集合
        List<DtoFixedPoint2Test> dtoFixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointId(dtoFixedpoint.getId());
        if (StringUtil.isNotEmpty(dtoFixedPoint2TestList)) {
            List<String> ids = dtoFixedPoint2TestList.parallelStream().map(DtoFixedPoint2Test::getTestId).collect(Collectors.toList());
            Map<String, Integer> testId2TimesOrderMap = new HashMap<>();
            Map<String, Integer> testId2SampleOrderMap = new HashMap<>();
            Map<String, String> testId2ProjectIntervalMap = new HashMap<>();
            dtoFixedPoint2TestList.forEach(p -> {
                testId2TimesOrderMap.put(p.getTestId(), p.getTimesOrder());
                testId2SampleOrderMap.put(p.getTestId(), p.getSamplePeriod());
                testId2ProjectIntervalMap.put(p.getTestId(), p.getProjectInterval());
            });
            //查询测试项目表
            List<DtoTest> tests = testService.findAllDeleted(ids);
            List<String> analyzeMethodIds = tests.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
            List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(analyzeMethodIds) ? analyzeMethodRepository.findAllDeleted(analyzeMethodIds) : new ArrayList<>();
            Map<String, DtoAnalyzeMethod> analyzeMethodMap = analyzeMethodList.stream().collect(Collectors.toMap(DtoAnalyzeMethod::getId, p -> p));
            if (StringUtil.isNotEmpty(tests)) {
                testService.loadTest(tests);
                for (DtoTest test : tests) {
                    Integer timesOrder = testId2TimesOrderMap.getOrDefault(test.getId(), 1);
                    Integer sampleOrder = testId2SampleOrderMap.getOrDefault(test.getId(), 1);
                    String projectInterval = testId2ProjectIntervalMap.getOrDefault(test.getId(), "");
                    test.setTimesOrder(timesOrder);
                    test.setSamplePeriod(sampleOrder);
                    test.setProjectInterval(projectInterval);
                    DtoAnalyzeMethod analyzeMethod = analyzeMethodMap.get(test.getAnalyzeMethodId());
                    String testStatus = "";
                    if ((!analyzeMethod.getIsDeleted() && !test.getIsDeleted())
                            || (EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(analyzeMethod.getStatus()) && test.getIsDeleted())) {
                        testStatus = EnumLIM.EnumAnalyzeMethodStatus.getByValue(analyzeMethod.getStatus());
                    } else {
                        testStatus = "删除";
                    }
                    test.setTestStatus(testStatus);
                }
                tests.sort(Comparator.comparing(DtoTest::getOrderNum, Comparator.reverseOrder()));
                dtoFixedpoint.setDtoTests(tests);
            }
        }
    }

    @Override
    @Transactional
    public void updateTimesOrder(DtoFixedPoint2Test fixedPoint2Test) {
        String fixPointId = fixedPoint2Test.getFixedPointId();
        DtoFixedpoint fixedPoint = repository.findOne(fixPointId);
        if (StringUtil.isNull(fixedPoint)) {
            throw new BaseException("污染源点位不存在！");
        }
        List<DtoFixedPoint2Test> fixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointId(fixedPoint.getId());
        fixedPoint2TestList = fixedPoint2TestList.stream().filter(p -> fixedPoint2Test.getTestIdList().contains(p.getTestId())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(fixedPoint2TestList)) {
            fixedPoint2TestList.forEach(p -> {
                p.setTimesOrder(fixedPoint2Test.getTimesOrder());
                p.setSamplePeriod(fixedPoint2Test.getSamplePeriod());
                p.setProjectInterval(fixedPoint2Test.getProjectInterval());
            });
            fixedPoint2TestRepository.save(fixedPoint2TestList);
        }
    }

    @Override
    @Transactional
    public void updatePointTimesOrder(DtoPropertyPoint2Test propertyPoint2Test) {
        String propertyPointId = propertyPoint2Test.getPropertyPointId();
        DtoProperty2Point property2Point = property2PointRepository.findOne(propertyPointId);
        if (StringUtil.isNull(property2Point)) {
            throw new BaseException("监测计划环境质量点位不存在！");
        }
        List<DtoPropertyPoint2Test> point2TestList = propertyPoint2TestRepository
                .findByPropertyPointIdAndTestIdIn(propertyPointId, propertyPoint2Test.getTestIdList());
        if (StringUtil.isNotEmpty(point2TestList)) {
            point2TestList.forEach(p -> {
                p.setTimesOrder(propertyPoint2Test.getTimesOrder());
                p.setSamplePeriod(propertyPoint2Test.getSamplePeriod());
            });
            propertyPoint2TestRepository.save(point2TestList);
        }
    }

    @Override
    @Transactional
    public DtoFixedpoint copyPoint(DtoFixedpoint fixedpoint) {
        if (StringUtil.isNotEmpty(fixedpoint.getFixedPointIds())) {
            oldCopyPoint(fixedpoint.getFixedPointIds());
            return fixedpoint;
        } else {
            DtoFixedpoint save = new DtoFixedpoint();
            BeanUtils.copyProperties(fixedpoint, save, "id", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate");
            this.save(save);
            List<DtoFixedPoint2Test> saveTestList = new ArrayList<>();
            List<DtoTest> testList = fixedpoint.getDtoTests();
            if (StringUtil.isNotEmpty(testList)) {
                for (DtoTest test : testList) {
                    DtoFixedPoint2Test saveTest = new DtoFixedPoint2Test();
                    saveTest.setFixedPointId(save.getId());
                    saveTest.setTestId(test.getId());
                    saveTest.setSamplePeriod(test.getSamplePeriod());
                    saveTest.setTimesOrder(test.getTimesOrder());
                    saveTest.setProjectInterval(test.getProjectInterval());
                    saveTestList.add(saveTest);
                }
            }
            List<DtoOicInformation> oicInformationList = oicInformationRepository.findByFixedPointId(fixedpoint.getId());
            List<String> oicIds = oicInformationList.stream().map(DtoOicInformation::getId).collect(Collectors.toList());
            List<DtoOicInformation2Test> oic2test = oicInformation2TestRepository.findByOicIdIn(oicIds);
            List<DtoOicInformation> addOicList = new ArrayList<>();
            List<DtoOicInformation2Test> addOic2TestList = new ArrayList<>();
            for (DtoOicInformation oicInformation : oicInformationList) {
                DtoOicInformation saveOic = new DtoOicInformation();
                BeanUtils.copyProperties(oicInformation, saveOic, "id", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate");
                saveOic.setFixedPointId(save.getId());
                addOicList.add(saveOic);
                List<DtoOicInformation2Test> oicInformation2Tests = oic2test.stream().filter(p -> oicInformation.getId().equals(p.getOicId())).collect(Collectors.toList());
                for (DtoOicInformation2Test information2Test : oicInformation2Tests) {
                    DtoOicInformation2Test dtoOicInformation2Test = new DtoOicInformation2Test();
                    dtoOicInformation2Test.setOicId(saveOic.getId());
                    dtoOicInformation2Test.setTestId(information2Test.getTestId());
                    addOic2TestList.add(dtoOicInformation2Test);
                }
            }
            if (StringUtil.isNotEmpty(saveTestList)) {
                fixedPoint2TestRepository.save(saveTestList);
            }
            if (StringUtil.isNotEmpty(addOicList)) {
                oicInformationRepository.save(addOicList);
            }
            if (StringUtil.isNotEmpty(addOic2TestList)) {
                oicInformation2TestRepository.save(addOic2TestList);
            }
            return save;
        }
    }

    /**
     * 旧版复制污染源点位
     *
     * @param ids 复制源ids
     */
    private void oldCopyPoint(List<String> ids) {
        List<DtoFixedpoint> fixedpoints = repository.findAll(ids);
        List<DtoFixedPoint2Test> fixedPoint2Tests = fixedPoint2TestRepository.findByFixedPointIdIn(ids);
        List<DtoFixedpoint> saveList = new ArrayList<>();
        List<DtoFixedPoint2Test> saveTestList = new ArrayList<>();
        for (DtoFixedpoint fixedpoint : fixedpoints) {
            DtoFixedpoint save = new DtoFixedpoint();
            BeanUtils.copyProperties(fixedpoint, save, "id", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate");
            save.setPointName(fixedpoint.getPointName() + "【复制】");
            saveList.add(save);
            List<DtoFixedPoint2Test> tests = fixedPoint2Tests.stream().filter(t -> fixedpoint.getId().equals(t.getFixedPointId())).collect(Collectors.toList());
            for (DtoFixedPoint2Test test : tests) {
                DtoFixedPoint2Test saveTest = new DtoFixedPoint2Test();
                BeanUtils.copyProperties(test, saveTest, "id", "fixedPointId");
                saveTest.setFixedPointId(save.getId());
                saveTestList.add(saveTest);
            }
        }
        if (StringUtil.isNotEmpty(saveList)) {
            repository.save(saveList);
        }
        if (StringUtil.isNotEmpty(saveTestList)) {
            fixedPoint2TestRepository.save(saveTestList);
        }
    }

    @Override
    @Transactional
    public void copyPointTest(DtoFixedpoint fixedPoint) {
        String id = fixedPoint.getId();
        List<String> targetFixedPointIds = fixedPoint.getFixedPointIds();
        // 过滤复制源id
        targetFixedPointIds.removeIf(id::equals);
        if (StringUtil.isNotEmpty(targetFixedPointIds)) {
            //删除关联测试项目
            List<DtoFixedPoint2Test> relationTests = fixedPoint2TestRepository.findByFixedPointIdIn(targetFixedPointIds);
            fixedPoint2TestRepository.delete(relationTests);
            //通过点位信息的主键查询DtoFixedPoint2Test集合
            List<DtoFixedPoint2Test> dtoFixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointId(id);
            List<DtoFixedPoint2Test> insertList = new ArrayList<>();
            for (DtoFixedPoint2Test point2Test : dtoFixedPoint2TestList) {
                for (String targetFixedPointId : targetFixedPointIds) {
                    DtoFixedPoint2Test fixedPoint2Test = new DtoFixedPoint2Test();
                    fixedPoint2Test.setFixedPointId(targetFixedPointId);
                    fixedPoint2Test.setTestId(point2Test.getTestId());
                    fixedPoint2Test.setTimesOrder(point2Test.getTimesOrder());
                    fixedPoint2Test.setSamplePeriod(point2Test.getSamplePeriod());
                    insertList.add(fixedPoint2Test);
                }
            }
            fixedPoint2TestRepository.save(insertList);
        }
    }

    @Override
    public List<DtoFixedpoint> findByEnterpriseId(String enterpriseId) {
        return repository.findByEnterpriseId(enterpriseId);
    }

    /**
     * 设置经纬度
     *
     * @param fixedPoint 点位数据
     */
    private void loadLocation(DtoFixedpoint fixedPoint) {
        if (StringUtil.isNotEmpty(fixedPoint.getLon()) && StringUtil.isNotEmpty(fixedPoint.getLat())) {
            fixedPoint.setLocation(String.join(",", fixedPoint.getLon(), fixedPoint.getLat()));
        }
    }

    /**
     * 获取下级区域集合
     *
     * @param areaIds 区域集合
     * @param areaId  父级区域
     * @return 区域集合
     */
    private List<String> getAreaIds(List<String> areaIds, String areaId) {
        areaIds.add(areaId);
        List<DtoArea> list = areaService.findByParentId(areaId);
        if (StringUtil.isNotEmpty(list)) {
            for (DtoArea area : list) {
                getAreaIds(areaIds, area.getId());
            }
        }
        return areaIds;
    }
}
