package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;


/**
 * PerformanceStatisticForWorkSheetData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/19
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="PerformanceStatisticForWorkSheetData")
 @Data
 public  class PerformanceStatisticForWorkSheetData implements BaseEntity,Serializable {

    private static final long serialVersionUID = 1L;

    public PerformanceStatisticForWorkSheetData() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }


    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 检测单id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测单id")
    private String workSheetFolderId;

    /**
     * 检测单编号
     */
    @Column(length = 20)
    @ApiModelProperty("检测单编号")
    private String workSheetCode;

    /**
     * 测试项目id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("测试项目id")
    private String testId;

    /**
     * 分析人Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析人Id")
    private String analystId;

    /**
     * 分析日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("分析日期")
    private Date analyzeTime;

    /**
     * 检测类型id
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;

    /**
     * 分析项目名称
     */
    @Column(length = 100)
    @ApiModelProperty("分析项目名称")
    private String redAnalyzeItemName;

    /**
     * 分析方法名称
     */
    @ApiModelProperty("分析方法名称")
    private String redAnalyzeMethodName;

    /**
     * 分析项目id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析项目id")
    private String analyseItemId;

    /**
     * 分析方法id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析方法id")
    private String analyzeMethodId;

    /**
     * 样品数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("样品数")
    private Integer sample = 0;

    /**
     * 全程序空白样品数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("全程序空白样品数")
    private Integer localeGap = 0;

    /**
     * 室内空白样品数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("室内空白样品数")
    private Integer interiorGap = 0;

    /**
     * 平行样品数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("平行样品数")
    private Integer parallel = 0;

    /**
     * 加标样品数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("加标样品数")
    private Integer addition = 0;

    /**
     * 曲线条数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("曲线条数")
    private Integer curveItem = 0;

    /**
     * 曲线个数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("曲线个数")
    private Integer curveEntries = 0;

    /**
     * 带点个数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("带点个数")
    private Integer point = 0;

    /**
     * 带质控数
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("带质控数")
    private Integer qcSample = 0;

    /**
     * 有效数据
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("有效数据")
    private Integer valid = 0;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;
}