package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.sample;

import com.sinoyd.lims.pro.dto.DtoSampleFolderTemplate;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTemp;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTestTemp;
import com.sinoyd.lims.pro.dto.customer.DtoLoadScheme;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component(IFileNameConstant.SchemeSynchronizationKey.ADD_SAMPLE)
public class AddSampleStrategy extends AbsSampleStrategy {

    @Override
    public void synchronizationSample(List<DtoSamplingFrequencyTemp> samplingFrequencyTempList) {
        //样品
        List<String> tempIds = samplingFrequencyTempList.stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        Set<String> folderTempIds = samplingFrequencyTempList.stream().map(DtoSamplingFrequencyTemp::getSampleFolderTempId).collect(Collectors.toSet());
        List<DtoSamplingFrequencyTestTemp> testTempList = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(tempIds);
        List<DtoSampleFolderTemplate> folderTemplateList = sampleFolderTemplateRepository.findAll(folderTempIds);
        Map<String, List<String>> addTestMap = new HashMap<>();
        for (DtoSamplingFrequencyTemp temp : samplingFrequencyTempList) {
            List<DtoSamplingFrequencyTestTemp> testTemps = testTempList.stream().filter(p -> p.getSamplingFrequencyTempId().equals(temp.getId())).collect(Collectors.toList());
            List<String> anaItemIds = testTemps.stream().map(DtoSamplingFrequencyTestTemp::getAnalyseItemId).distinct().collect(Collectors.toList());
            //新增周期批次
            Optional<DtoSampleFolderTemplate> tempOptional = folderTemplateList.stream()
                    .filter(s -> s.getId().equals(temp.getSampleFolderTempId())).findFirst();
            tempOptional.ifPresent(p -> {
                DtoLoadScheme loadScheme = schemeService.addSchemeTimes(p.getSampleFolderId(), temp.getPeriodCount(), 1, 1);
                if (loadScheme.getSamplingFrequency().size() > 0) {
                    addTestMap.put(loadScheme.getSamplingFrequency().get(0).getId(), anaItemIds);
                }
            });
        }
        //保存数据
        for (String frequencyKey : addTestMap.keySet()) {
            sampleFolderService.addFrequencyAnalyseItems(Collections.singletonList(frequencyKey),
                    addTestMap.get(frequencyKey), new ArrayList<>());
        }
    }
}
