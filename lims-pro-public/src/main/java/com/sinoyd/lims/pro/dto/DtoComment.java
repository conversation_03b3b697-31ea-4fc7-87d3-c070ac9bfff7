package com.sinoyd.lims.pro.dto;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.entity.Comment;
import javax.persistence.*;

import com.sinoyd.lims.pro.enums.EnumPRO;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoComment实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_Comment") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoComment extends Comment {
    private static final long serialVersionUID = 1L;

    public DtoComment() {

    }

    public DtoComment(DtoLog log, String objectId, Integer objectType) {
        this.setId(log.getId());
        this.setParentId(UUIDHelper.GUID_EMPTY);
        this.setObjectId(objectId);
        this.setObjectType(objectType);
        this.setCommentPersonId(log.getOperatorId());
        this.setCommentPersonName(log.getOperatorName());
        this.setCommentTime(log.getOperateTime());
        this.setComment(log.getComment());
        this.setCommentType(EnumPRO.EnumCommentType.日志.getValue());
        this.setIsDeleted(false);
    }
}