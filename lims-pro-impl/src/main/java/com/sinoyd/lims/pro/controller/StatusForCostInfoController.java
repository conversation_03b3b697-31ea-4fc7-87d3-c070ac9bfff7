package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.StatusForCostInfoService;
import com.sinoyd.lims.pro.criteria.StatusForCostInfoCriteria;
import com.sinoyd.lims.pro.dto.DtoStatusForCostInfo;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * StatusForCostInfo服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
 @Api(tags = "示例: StatusForCostInfo服务")
 @RestController
 @RequestMapping("api/pro/statusForCostInfo")
 public class StatusForCostInfoController extends BaseJpaController<DtoStatusForCostInfo, String,StatusForCostInfoService> {


    /**
     * 分页动态条件查询StatusForCostInfo
     * @param statusForCostInfoCriteria 条件参数
     * @return RestResponse<List<StatusForCostInfo>>
     */
     @ApiOperation(value = "分页动态条件查询StatusForCostInfo", notes = "分页动态条件查询StatusForCostInfo")
     @GetMapping
     public RestResponse<List<DtoStatusForCostInfo>> findByPage(StatusForCostInfoCriteria statusForCostInfoCriteria) {
         PageBean<DtoStatusForCostInfo> pageBean = super.getPageBean();
         RestResponse<List<DtoStatusForCostInfo>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, statusForCostInfoCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询StatusForCostInfo
     * @param id 主键id
     * @return RestResponse<DtoStatusForCostInfo>
     */
     @ApiOperation(value = "按主键查询StatusForCostInfo", notes = "按主键查询StatusForCostInfo")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoStatusForCostInfo> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoStatusForCostInfo> restResponse = new RestResponse<>();
         DtoStatusForCostInfo statusForCostInfo = service.findOne(id);
         restResponse.setData(statusForCostInfo);
         restResponse.setRestStatus(StringUtil.isNull(statusForCostInfo) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增StatusForCostInfo
     * @param statusForCostInfo 实体列表
     * @return RestResponse<DtoStatusForCostInfo>
     */
     @ApiOperation(value = "新增StatusForCostInfo", notes = "新增StatusForCostInfo")
     @PostMapping
     public RestResponse<DtoStatusForCostInfo> create(@RequestBody DtoStatusForCostInfo statusForCostInfo) {
         RestResponse<DtoStatusForCostInfo> restResponse = new RestResponse<>();
         restResponse.setData(service.save(statusForCostInfo));
         return restResponse;
      }

     /**
     * 新增StatusForCostInfo
     * @param statusForCostInfo 实体列表
     * @return RestResponse<DtoStatusForCostInfo>
     */
     @ApiOperation(value = "修改StatusForCostInfo", notes = "修改StatusForCostInfo")
     @PutMapping
     public RestResponse<DtoStatusForCostInfo> update(@RequestBody DtoStatusForCostInfo statusForCostInfo) {
         RestResponse<DtoStatusForCostInfo> restResponse = new RestResponse<>();
         restResponse.setData(service.update(statusForCostInfo));
         return restResponse;
      }

    /**
     * "根据id批量删除StatusForCostInfo
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除StatusForCostInfo", notes = "根据id批量删除StatusForCostInfo")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }