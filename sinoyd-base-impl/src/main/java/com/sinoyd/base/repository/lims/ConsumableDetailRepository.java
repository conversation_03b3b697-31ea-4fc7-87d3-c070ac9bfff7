package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

/**
 * 消耗品详细管理仓储
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
public interface ConsumableDetailRepository extends IBaseJpaPhysicalDeleteRepository<DtoConsumableDetail, String> {

    /***
     * 获取生产厂商
     * @param ids 消耗品ids
     * @return
     */
    @Query("select distinct d.manufacturerName from DtoConsumableDetail d where d.manufacturerName <> null and d.manufacturerName <> '' and d.parentId in :ids order by d.manufacturerName")
    List<String> findManufacturerName(@Param("ids") List<String> ids);

    /**
     * 根据消耗品id获取消耗品详情
     *
     * @param parentId
     * @return
     */
    @Query("select d from DtoConsumableDetail d where d.parentId = :parentId")
    List<DtoConsumableDetail> findByParentId(@Param("parentId") String parentId);

    /**
     * 查询明细集合
     * @param ids
     * @return
     */
    List<DtoConsumableDetail> findByParentIdIn(List<String> ids);

    /**
     * 根据消耗品id和有效期进行查询且根据有效期排序
     * @param parentId 消耗品id
     * @param expiryDate 有效期
     * @return
     */
    List<DtoConsumableDetail> findByParentIdAndExpiryDateGreaterThanEqualOrderByExpiryDate(@Param("parentId") String parentId, @Param("expiryDate") Date expiryDate);
    @Query("select d from DtoConsumableDetail d where d.parentId in :parentIds and d.expiryDate>:expiryDate")
    List<DtoConsumableDetail> findByParentIdsAndExpiryDate(@Param("parentIds") List<String> parentIds, @Param("expiryDate") Date expiryDate);

    /**
     * 根据parentIds 获取相关消耗品或者标样信息
     *
     * @param parentIds 父级id
     * @return 返回相应的信息
     */
    @Query("select d from DtoConsumableDetail d where d.parentId in :parentIds")
    List<DtoConsumableDetail> findByParentIds(@Param("parentIds") List<String> parentIds);

}