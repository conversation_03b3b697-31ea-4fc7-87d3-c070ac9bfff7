package com.sinoyd.lims.monitor.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;

/**
 * 污染源点位导入/导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/08
 * @since V100R001
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoImportFixedPointRS extends DtoImportFixedPointEQ{
    /**
     * 所属企业
     */
    private String enterpriseId;

    /**
     * 企业名称
     */
    @Excel(name = "企业名称(必填)",orderNum = "310",width = 24)
    private String enterpriseName;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型
     */
    @Excel(name = "检测类型(必填)",orderNum = "510",width = 24)
    private String sampleTypeName;
}
