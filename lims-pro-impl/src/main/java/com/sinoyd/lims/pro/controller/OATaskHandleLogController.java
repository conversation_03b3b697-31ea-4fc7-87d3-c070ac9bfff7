package com.sinoyd.lims.pro.controller;

import java.util.List;

import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ELogType;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.OATaskHandleLogCriteria;
import com.sinoyd.lims.pro.dto.DtoOATaskHandleLog;
import com.sinoyd.lims.pro.service.OATaskHandleLogService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 审批任务流程日志服务接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@RestController
@RequestMapping("/api/pro/oaTaskHandleLogs")
@Api(tags = "工作流: 审批日志服务")
public class OATaskHandleLogController extends BaseJpaController<DtoOATaskHandleLog, String, OATaskHandleLogService> {
    
    /**
     * 查询审批日志
     * 
     * @param taskId 任务ID
     * @return RestResponse<List<OATaskHandleLog>>
     */
    @ApiOperation(value = "查询审批日志", notes = "查询审批日志")
    @GetMapping()
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<List<DtoOATaskHandleLog>> findByTaskId(@RequestParam(name = "taskId") String taskId) {
        RestResponse<List<DtoOATaskHandleLog>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        PageBean<DtoOATaskHandleLog> page = super.getPageBean();

        OATaskHandleLogCriteria criteria = new OATaskHandleLogCriteria();
        criteria.setTaskId(taskId);

        service.findByPage(page, criteria);
        List<DtoOATaskHandleLog> data = page.getData();
        restResp.setData(data);
        restResp.setCount(data.size());

        return restResp;
    }
}
