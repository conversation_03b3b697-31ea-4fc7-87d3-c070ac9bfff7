package com.sinoyd.base.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.entity.Dimension;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;

import com.sinoyd.frame.util.UUIDHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 量纲接口实现
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
@Service
public class DimensionServiceImpl extends BaseJpaServiceImpl<DtoDimension, String, DimensionRepository> implements DimensionService {

    @Autowired
    private CodeService codeService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void findByPage(PageBean<DtoDimension> page, BaseCriteria criteria) {
        page.setEntityName("DtoDimension d");
        page.setSelect("select d");
        //默认按照排序值排序
        if (StringUtil.isEmpty(page.getSort())){
            page.setSort("orderNum-");
        }
        super.findByPage(page, criteria);
        List<DtoDimension> list = page.getData();
        List<String> dimensionTypeIds = list.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getDimensionTypeId())
                && !p.getDimensionTypeId().equals(UUIDHelper.GUID_EMPTY)).map(DtoDimension::getDimensionTypeId).collect(Collectors.toList());

        List<DtoCode> codeList = codeService.findByCodes(dimensionTypeIds);

        for (DtoDimension d : list) {

            String dimensionTypeName = "";
            Optional<DtoCode> optional = codeList.stream().filter(p -> p.getDictCode().equals(d.getDimensionTypeId())).findFirst();

            //取量纲类型名称
            if (StringUtil.isNotNull(optional) && optional.isPresent()) {
                dimensionTypeName = optional.get().getDictName();
            }

            d.setDimensionTypeName(dimensionTypeName);
        }
        page.setData(list);
    }

    @Override
    @Transactional
    public DtoDimension save(DtoDimension dimension) {
        if (!StringUtils.isNotNullAndEmpty(dimension.getDimensionName())) {
            throw new BaseException("量纲名称不能为空！");
        }
        // 判断名称重复
        if (repository.countByIdNotAndDimensionNameAndIsDeletedFalse(dimension.getId(), dimension.getDimensionName()) > 0) {
            throw new BaseException("已存在相同名称的量纲！");
        }
        DtoDimension item = super.save(dimension);
        saveRedis(item);
        return item;
    }

    @Override
    @Transactional
    public List<DtoDimension> save(Collection<DtoDimension> entities) {
        if(entities.stream().anyMatch(v->StringUtil.isEmpty(v.getDimensionName()))){
            throw new BaseException("量纲名称不能为空！");
        }
        List<DtoDimension> existDimensionList = repository.findAll();
        existDimensionList.addAll(entities);
        for (DtoDimension dimension:existDimensionList) {
            if(entities.stream().anyMatch(v->v.getDimensionName().equals(dimension.getDimensionName())&&!v.getId().equals(dimension.getId()))){
                throw new BaseException("已存在相同名称的量纲！");
            }
        }
        List<DtoDimension> saveList = super.save(entities);
        saveList.forEach(this::saveRedis);
        return saveList;
    }

    @Transactional
    @Override
    public DtoDimension update(DtoDimension dimension) {
        if (!StringUtils.isNotNullAndEmpty(dimension.getDimensionName())) {
            throw new BaseException("量纲名称不能为空！");
        }
        // 判断名称重复
        if (repository.getCountByName(dimension.getId(), dimension.getDimensionName()) > 0) {
            throw new BaseException("已存在相同名称的量纲！");
        }
        DtoDimension item = super.update(dimension);
        saveRedis(item);
        //利用通知的方式，告知相关的关联表，量纲相关信息修改了
        redisTemplate.convertAndSend(EnumBase.EnumBASRedisChannel.BAS_Dimension_Update.name(),JsonStream.serialize(item));
        return item;
    }

    @Override
    public DtoDimension findByDimensionName(String dimensionName) {
        return repository.findByDimensionName(dimensionName);
    }

    @Override
    public String convertById(String toConvertValue, String fromDimensionId, String toDimensionId) {
        Dimension fromDimension = repository.findOne(fromDimensionId);
        if ( StringUtil.isNull(fromDimension)) {
            throw new BaseException("原量纲不存在！");
        }
        Dimension toDimension = repository.findOne(toDimensionId);
        if ( StringUtil.isNull(toDimension)) {
            throw new BaseException("目标量纲不存在！");
        }
        return convertDimension(fromDimension, toDimension, toConvertValue);
    }

    @Override
    public String convertByIdNew(String toConvertValue, String fromDimensionId, String toDimensionId) {
        //mm（毫米），cm（厘米），dm（分米），m（米）， 如果mm基准值设为1，cm基准值应设为0.1，dm基准值设为0.01，m基准值设为0.001，
        // 系统获取对应选择的量纲的基准值进行换算，如果基准值没有设置，则不进行换算，仍然等于原值
        Dimension fromDimension = repository.findOne(fromDimensionId);
        if ( StringUtil.isNull(fromDimension)) {
            throw new BaseException("原量纲不存在！");
        }
        Dimension toDimension = repository.findOne(toDimensionId);
        if ( StringUtil.isNull(toDimension)) {
            throw new BaseException("目标量纲不存在！");
        }
        if (fromDimension.getDimensionTypeId().equals(toDimension.getDimensionTypeId())) {
            try {
                if (toDimension.getBaseValue().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal toConvertValueD = new BigDecimal(toConvertValue);
                    return toConvertValueD.multiply(toDimension.getBaseValue().divide(fromDimension.getBaseValue()))
                            .toString();
                } else {
                    throw new BaseException("目标量纲基准值不能为0！");
                }
            } catch (NumberFormatException e) {
                throw new BaseException("待转换值格式不正确！");
            }
        } else {
            throw new BaseException("原量纲类型和目标量纲类型不同！");
        }
    }

    @Override
    public String convertByName(String toConvertValue, String fromDimensionName, String toDimensionName) {
        Dimension fromDimension = repository.findByDimensionName(fromDimensionName);
        if (StringUtil.isNull(fromDimension)) {
            throw new BaseException("原量纲不存在！");
        }
        Dimension toDimension = repository.findByDimensionName(toDimensionName);
        if (StringUtil.isNull(toDimension)) {
            throw new BaseException("目标量纲不存在！");
        }

        return convertDimension(fromDimension, toDimension, toConvertValue);
    }


    @Override
    public DtoDimension findOne(String id) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_Dimension.getValue());
        Object json = redisTemplate.opsForHash().get(key, id);
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<DtoDimension> typeLiteral = new TypeLiteral<DtoDimension>() {
            };
            DtoDimension dtoDimension = JsonIterator.deserialize(json.toString(), typeLiteral);
            return dtoDimension;
        }
        DtoDimension dtoDimension = super.findOne(id);
        if (StringUtil.isNotNull(dtoDimension)) {
            saveRedis(dtoDimension);
        }
        return dtoDimension;
    }

    /**
     * 根据量纲id数组获取量纲
     *
     * @param ids 获取量纲数据
     * @return 返回量纲数据
     */
    @Override
    public List<DtoDimension> findByIds(List<String> ids) {
        return repository.findAll(ids);
    }

    @Override
    public List<DtoDimension> findRedisByIds(List<String> ids) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_Dimension.getValue());
        List<Object> dataList = redisTemplate.opsForHash().multiGet(key, ids);
        return setMaps(dataList, ids);
    }

    @Override
    public List<DtoDimension> findAllDeleted(List<String> ids) {
        return repository.findAll(ids);
    }

    @Override
    public List<DtoDimension> findAllDeleted() {
        return repository.findAllDeleted();
    }

    /**
     * 保存相应的redis数据
     *
     * @param item 检测类型的实体对象
     */
    @Override
    public void saveRedis(DtoDimension item) {
        String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_Dimension.getValue());
        redisTemplate.opsForHash().put(key, item.getId(), JsonStream.serialize(item));
    }

    @Override
    @Transactional
    public void incrementOrderNum(List<String> ids) {
        ids = ids.stream().filter(id -> StringUtils.isNotNullAndEmpty(id) && !UUIDHelper.GUID_EMPTY.equals(id)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(ids)) {
            List<DtoDimension> dtoDimensionList = findRedisByIds(ids);
            for (DtoDimension dtoDimension : dtoDimensionList) {
                DtoDimension dto = new DtoDimension();
                BeanUtils.copyProperties(dtoDimension, dto);
                if (StringUtil.isNotNull(dto.getOrderNum())) {
                    dto.setOrderNum(dto.getOrderNum() + 1);
                }
                saveRedis(dto);
            }
            repository.incrementOrderNum(ids);
        }
    }

    /**
     * 从redis中获取相应的数据
     *
     * @param dataList 数据集合
     * @return 返回测试项目数据
     */
    private List<DtoDimension> setMaps(List<Object> dataList, List<String> ids) {
        List<DtoDimension> itemList = new ArrayList<>();
        TypeLiteral<DtoDimension> typeLiteral = new TypeLiteral<DtoDimension>() {
        };
        List<String> existIds = new ArrayList<>();
        for (Object s : dataList) {
            if (StringUtil.isNotNull(s)) {
                try {
                    DtoDimension item = JsonIterator.deserialize(s.toString(), typeLiteral);
                    if (StringUtil.isNotNull(item)) {
                        existIds.add(item.getId());
                        itemList.add(item);
                    }
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
        }
        //将未缓存到redis的数据缓存起来（需要假删数据，防止业务库调用之后找不到这些数据）
        List<String> newIds = ids.stream().filter(p -> !existIds.contains(p)).collect(Collectors.toList());
        if (newIds.size() > 0) {
            List<DtoDimension> dtoDimensions = repository.findAllDeleted(newIds);
            itemList.addAll(dtoDimensions);
            Map<String, Object> map = new HashMap<>();
            for (DtoDimension dtoDimension : dtoDimensions) {
                map.put(dtoDimension.getId(), JsonStream.serialize(dtoDimension));
            }
            String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_Dimension.getValue());
            redisTemplate.opsForHash().putAll(key, map);

        }
        return itemList;
    }


    /**
     * 量纲转化的通用方法
     * @param fromDimension 来源量纲
     * @param toDimension 目标量纲
     * @return 返回转化值
     */
    private String convertDimension(Dimension fromDimension,Dimension toDimension,String toConvertValue) {
        if (fromDimension.getDimensionTypeId().equals(toDimension.getDimensionTypeId())) {
            try {
                BigDecimal toConvertValueD = new BigDecimal(toConvertValue);
                // 计算公式：转换后的值=待转换的值 * (原量纲基准值 / 目标量纲基准值)
                if (toDimension.getBaseValue().compareTo(BigDecimal.ZERO) != 0) {
                    return toConvertValueD.multiply(fromDimension.getBaseValue().divide(toDimension.getBaseValue()))
                            .toString();
                } else {
                    throw new BaseException("目标量纲基准值不能为0！");
                }
            } catch (NumberFormatException e) {
                throw new BaseException("待转换值格式不正确！");
            }
        } else {
            throw new BaseException("原量纲类型和目标量纲类型不同！");
        }
    }

    // region 注释的传Map的量纲转换
    // @Override
    // public String convert(Map<String, Object> map) {
    // String result = "";// 结果
    // if (StringUtils.isNotNullAndEmpty(map.get("value"))) {
    // String toConvertValue = map.get("value").toString();// 待转换值
    // BigDecimal fromBaseValue = new BigDecimal(0);// 原量纲基准值
    // BigDecimal toBaseValue = new BigDecimal(0);// 目标量纲基准值
    // // TOD O:判断量纲类型是否相同
    // try {
    // if (StringUtils.isNotNullAndEmpty(map.get("fromDimesionId"))
    // && ((String) map.get("fromDimesionId")).equals(baseCodeHelper.GUID_EMPTY)
    // && StringUtils.isNotNullAndEmpty(map.get("toDimesionId"))
    // && ((String) map.get("toDimesionId")).equals(baseCodeHelper.GUID_EMPTY)) {
    // // 传RowGuid的情形
    // Dimension fromDimension = dimensionRepository.findOneByRowGuid((String)
    // map.get("fromDimesionId"));
    // if (fromDimension != null) {
    // fromBaseValue = fromDimension.getBaseValue();
    // }
    // Dimension toDimension = dimensionRepository.findOneByRowGuid((String)
    // map.get("toDimesionId"));
    // if (toDimension != null) {
    // toBaseValue = toDimension.getBaseValue();
    // }
    // } else if (StringUtils.isNotNullAndEmpty(map.get("fromDimesionName"))
    // && StringUtils.isNotNullAndEmpty(map.get("toDimesionName"))) {
    // // 传量纲名称的情形
    // Dimension fromDimension = dimensionRepository
    // .findOneByDimensionName((String) map.get("fromDimesionName"));
    // if (fromDimension != null) {
    // fromBaseValue = fromDimension.getBaseValue();
    // }
    // Dimension toDimension = dimensionRepository
    // .findOneByDimensionName((String) map.get("toDimesionName"));
    // if (toDimension != null) {
    // toBaseValue = toDimension.getBaseValue();
    // }
    // } else {
    // throw new BaseException("原量纲和目标量纲都不能为空！");
    // }
    // } catch (Exception e) {
    // throw new BaseException("原量纲或目标量纲格式不正确！");
    // }

    // if (fromBaseValue.compareTo(new BigDecimal(0)) == 0 ||
    // toBaseValue.compareTo(new BigDecimal(0)) == 0) {
    // // 原量纲或者目标量纲其中有一个的基准值是零则无法计算
    // throw new BaseException("原量纲或者目标量纲基准值配置错误！");
    // } else {
    // // 计算公式：转换后的值=待转换的值 * (原量纲基准值 / 目标量纲基准值)
    // try {
    // BigDecimal toConvertValueD = new BigDecimal(toConvertValue);
    // result =
    // toConvertValueD.multiply(fromBaseValue.divide(toBaseValue)).toString();
    // } catch (NumberFormatException e) {
    // throw new BaseException("待转换值格式不正确！");
    // }
    // }

    // } else {
    // // 需要抛出异常表示传值错误吗？
    // throw new BaseException("待转换值不能为空！");
    // }
    // return result;
    // }
    // endregion
}