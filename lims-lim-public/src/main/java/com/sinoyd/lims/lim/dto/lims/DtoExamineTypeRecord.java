package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.ExamineTypeRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 考核管理类别考核记录实体
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/09/14
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ExamineTypeRecord")
@Where(clause = "isDeleted = 0 ")
@Data
@DynamicInsert
public class DtoExamineTypeRecord extends ExamineTypeRecord {

    private static final long serialVersionUID = 1L;


}
