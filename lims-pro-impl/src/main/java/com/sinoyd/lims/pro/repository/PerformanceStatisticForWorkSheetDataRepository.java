package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForWorkSheetData;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * PerformanceStatisticForWorkSheetData数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/19
 * @since V100R001
 */
public interface PerformanceStatisticForWorkSheetDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoPerformanceStatisticForWorkSheetData, String> {


    /**
     * 批量清除检测单相关的数据
     *
     * @param workSheetFolderIds 检测单ids
     * @return 返回删除行数
     */
    @Transactional
    @Modifying
    @Query("delete  from DtoPerformanceStatisticForWorkSheetData where workSheetFolderId in :workSheetFolderIds")
    Integer deleteByWorkSheetFolderIdIn(@Param("workSheetFolderIds") List<String> workSheetFolderIds);
}