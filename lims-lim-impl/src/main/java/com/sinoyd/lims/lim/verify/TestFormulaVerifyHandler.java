package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportTestFormula;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 测试项目公式导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/6
 * @since V100R001
 */
@Data
public class TestFormulaVerifyHandler implements IExcelVerifyHandler<DtoImportTestFormula> {

    /**
     *   正则表达式，用于从公式中提取参数
     */
    private static final Pattern MAP_PATTERN = Pattern.compile("(?<=\\[).*?(?=\\])");

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    /**
     *  重复数据校验容器
     */
    private List<DtoImportTestFormula> duplicationCheckList;

    /**
     *  测试项目校验容器，存放所有测试项目
     */
    private List<DtoTest> allTestList;

    /**
     *  检测类型校验容器，存放所有检测类型
     */
    private List<DtoSampleType> allSampleTypeList;

    /**
     *  参数校验容器，存放所有参数
     */
    private List<DtoParams> allParamsList;

    /**
     *  参数公式校验容器，存放所有参数公式
     */
    private List<DtoParamsFormula> allParamsFormulaList;

    /**
     * 数据校验
     * @param importTestFormula
     * @return 校验结果集
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportTestFormula importTestFormula) {
        //导入数据处理,跳过空行,数据去除前后空格
        try {
            if (importUtils.checkObjectIsNull(importTestFormula)) {
                return new ExcelVerifyHandlerResult(true);
            }
            importUtils.strToTrim(importTestFormula);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }

        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + (importTestFormula.getRowNum()-1) + "行数据校验有误");

        //必填校验
        importUtils.checkIsNull(result, importTestFormula.getSampleType(), "检测类型", failStr);
        importUtils.checkIsNull(result, importTestFormula.getAnalyzeItem(), "分析项目", failStr);
        importUtils.checkIsNull(result, importTestFormula.getAnalyzeMethod(), "分析方法", failStr);
        importUtils.checkIsNull(result, importTestFormula.getSampleTypeForFormula(), "检测类型（公式）", failStr);
        importUtils.checkIsNull(result, importTestFormula.getFormula(), "公式", failStr);
        //填写了检出限的 测得量公式必填
        if(StringUtil.isNotEmpty(importTestFormula.getDetectionLimit())){
            importUtils.checkIsNull(result, importTestFormula.getPartFormula(), "测得量公式", failStr);
        }


        //数据格式校验
        importUtils.checkNumTwo(result, importTestFormula.getMostSignificance(), "有效位数", failStr);
        importUtils.checkNumTwo(result, importTestFormula.getMostDecimal(), "小数位数", failStr);

        //业务数据校验
        //excel重复数据判断
        isRepeatData(result, failStr, importTestFormula, duplicationCheckList);
        //需要根据“检测类型”、“分析项目”、“分析方法”判定系统中是否存在该测试项目  需要验证“检测类型（公式）”是否属于“测试项目检测类型大类及其小类”的要求
        isExistTest(result, failStr, importTestFormula);

        //更新重复校验容器
        duplicationCheckList.add(importTestFormula);

        //构建异常信息
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }


    /**
     * 判断excel是否有重复数据
     *
     * @param result     校验结果
     * @param failStr    校验错误数据
     * @param importTestFormula 导入数据
     * @param tempList   临时数据
     */
    private void isRepeatData(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTestFormula importTestFormula, List<DtoImportTestFormula> tempList) {
        if (StringUtil.isNotEmpty(importTestFormula.getAnalyzeItem()) && StringUtil.isNotEmpty(importTestFormula.getAnalyzeMethod())) {
            tempList.removeIf(p -> StringUtil.isEmpty(p.getAnalyzeItem()) || StringUtil.isEmpty(p.getAnalyzeMethod()) || StringUtil.isEmpty(p.getSampleType())|| StringUtil.isEmpty(p.getSampleTypeForFormula()));
            List<Integer> isRepeatRowNum = tempList.stream()
                    .filter(p -> importTestFormula.getAnalyzeItem().equals(p.getAnalyzeItem())
                            && importTestFormula.getAnalyzeMethod().equals(p.getAnalyzeMethod())
                            && importTestFormula.getFormula().equals(p.getFormula()))
                    .map(DtoImportTestFormula::getRowNum)
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isRepeatRowNum)) {
                result.setSuccess(false);
                failStr.append("；").append("测试项目与第").append(isRepeatRowNum).append("条重复");
            }
        }
    }

    /**
     * 需要根据“检测类型”、“分析项目”、“分析方法”判定系统中是否存在该测试项目  需要验证“检测类型（公式）”是否属于“测试项目检测类型大类及其小类”的要求
     * @param result  校验结果
     * @param failStr 校验错误数据
     * @param importTestFormula 导入数据
     */
    private void isExistTest(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTestFormula importTestFormula) {
        DtoTest existTest = allTestList.stream().filter(test->test.getRedAnalyzeItemName().equals(importTestFormula.getAnalyzeItem())&&test.getRedAnalyzeMethodName().equals(importTestFormula.getAnalyzeMethod()))
                                              .findFirst().orElse(null);
        if(StringUtil.isNull(existTest)){
            result.setSuccess(false);
            failStr.append("；").append("测试项目不存在");
        }
        else{
            DtoSampleType existTestSampleType = allSampleTypeList.stream().filter(s->s.getId().equals(existTest.getSampleTypeId())).findFirst().orElse(null);
            if(!existTestSampleType.getTypeName().equals(importTestFormula.getSampleType())){
                result.setSuccess(false);
                failStr.append("；").append("测试项目与检测类型不匹配");
            }
            else{
                //检测类型（公式）与 检测类型都填写但不相同的情况 校验 检测类型（公式） 是否为检测类型的小类
                if(StringUtil.isNotEmpty(importTestFormula.getSampleType())&&StringUtil.isNotEmpty(importTestFormula.getSampleTypeForFormula())){
                    DtoSampleType sampleTypeP = allSampleTypeList.stream().filter(s->s.getTypeName().equals(importTestFormula.getSampleType())).findFirst().orElse(null);
                    if(StringUtil.isNull(sampleTypeP)){
                        result.setSuccess(false);
                        failStr.append("；").append("检测类型不存在");
                    }
                    DtoSampleType sampleTypeC = allSampleTypeList.stream().filter(s->s.getTypeName().equals(importTestFormula.getSampleTypeForFormula())).findFirst().orElse(null);
                    if(StringUtil.isNull(sampleTypeC)){
                        result.setSuccess(false);
                        failStr.append("；").append("检测类型（公式）不存在");
                    }
                    if((!importTestFormula.getSampleType().equals(importTestFormula.getSampleTypeForFormula()))
                            &&StringUtil.isNotNull(sampleTypeP)&&StringUtil.isNotNull(sampleTypeC)&&!sampleTypeC.getParentId().equals(sampleTypeP.getId())){
                        result.setSuccess(false);
                        failStr.append("；").append("检测类型（公式）与检测类型不匹配");
                    }
                    //测试项目+ 公式 + 检测类型 进行唯一性校验 避免多刺导入产生重复数据
                    if(StringUtil.isNotNull(sampleTypeP)&&StringUtil.isNotNull(sampleTypeC)&&StringUtil.isNotEmpty(importTestFormula.getFormula())){
                        List<DtoParamsFormula> existFormulaList =allParamsFormulaList.stream().filter(p->p.getObjectId().equals(existTest.getId())
                                &&p.getObjectType().equals(EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue())
                                &&p.getSampleTypeId().equals(sampleTypeC.getId())
                                &&p.getFormula().equals(importTestFormula.getFormula())).collect(Collectors.toList());
                        if(!existFormulaList.isEmpty()){
                            result.setSuccess(false);
                            failStr.append("；").append("已存在相同的测试项目公式");
                        }
                    }
                }
            }
        }
    }
}
