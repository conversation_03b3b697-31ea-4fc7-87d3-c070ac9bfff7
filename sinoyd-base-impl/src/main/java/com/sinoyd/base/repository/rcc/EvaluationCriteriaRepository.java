package com.sinoyd.base.repository.rcc;

import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

/**
 * 评价标准数据接口
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
public interface EvaluationCriteriaRepository  extends IBaseJpaPhysicalDeleteRepository<DtoEvaluationCriteria,String>, LimsRepository<DtoEvaluationCriteria,String> {
}
