package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

/**
 * 首页代办数字查询结果
 * <AUTHOR>
 * @version V1.0.0 2022/10/31
 * @since V100R001
 */
@Data
public class DtoTaskNum {
    /**
     * 用户id
     */
    private String userId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 人员下任务的数量
     */
    private Long count;

    public DtoTaskNum(String userId, String testId, Long count){
        this.userId = userId;
        this.testId = testId;
        this.count = count;
    }

    public DtoTaskNum(String userId, Long count) {
        this.userId = userId;
        this.count = count;
    }

    public DtoTaskNum(Long count){
        this.count = count;
    }

    public DtoTaskNum() {
    }
}
