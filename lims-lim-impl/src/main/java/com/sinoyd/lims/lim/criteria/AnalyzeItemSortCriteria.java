package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分析项目排序
 * <AUTHOR> 修改: xuxb
 * @version V1.0.0 2019/1/18
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeItemSortCriteria extends BaseCriteria {


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        return condition.toString();
    }
}