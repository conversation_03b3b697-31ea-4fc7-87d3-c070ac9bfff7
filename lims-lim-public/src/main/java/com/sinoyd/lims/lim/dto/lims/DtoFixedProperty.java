package com.sinoyd.lims.lim.dto.lims;


import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoImportFixedAssets;
import com.sinoyd.lims.lim.entity.FixedProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 固定资产
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_FixedProperty")
@Data
@DynamicInsert
public class DtoFixedProperty extends FixedProperty {

    private static final long serialVersionUID = 1L;

    /**
     * 设置导入数据
     *
     * @param importFixedAssets 导入的数据
     */
    public void importToFixedAssetsEntity(DtoImportFixedAssets importFixedAssets) {
        setAssetsName(importFixedAssets.getAssetsName());
        setAssetsNo(importFixedAssets.getAssetsNo());
        setBrandModel(importFixedAssets.getBrandModel());
        setAssetsType(importFixedAssets.getAssetsType());
        setStatus(StringUtil.isEmpty(importFixedAssets.getStatus()) ? 1 : Integer.valueOf(importFixedAssets.getStatus()));
    }
    /**
     * 管理人员
     */
    @Transient
    private String managerName;
    /**
     * 供应商
     */
    @Transient
    private String supplierName;
    /**
     * 所属科室
     */
    @Transient
    private String deptName;
}
