package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoProjectApproval;

import java.util.List;

/**
 *  ProjectApproval
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/08/02
 */
public interface ProjectApprovalRepository extends IBaseJpaPhysicalDeleteRepository<DtoProjectApproval, String> {

    /**
     * 根据项目id查询数据
     *
     * @param projectIds 项目id
     * @param statuses 状态
     * @return List<DtoProjectApproval>
     */
    List<DtoProjectApproval> findByProjectIdInAndModifyStatusIn(List<String> projectIds, List<String> statuses);

}
