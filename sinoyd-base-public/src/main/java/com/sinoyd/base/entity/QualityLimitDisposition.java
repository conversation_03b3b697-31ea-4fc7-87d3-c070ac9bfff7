package com.sinoyd.base.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * QualityLimitDisposition实体
 * <AUTHOR>
 * @version V1.0.0 2024/5/27
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="QualityLimitDisposition")
 @Data
 public  class QualityLimitDisposition implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  QualityLimitDisposition() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 质控等级
    */
    @Column(nullable=false)
    @ApiModelProperty("质控等级")
    private Integer qcGrade;
    
    /**
    * 质控类型
    */
    @Column(nullable=false)
    @ApiModelProperty("质控类型")
    private Integer qcType;
    
    /**
    * 公式
    */
    @ApiModelProperty("公式")
    @Length(message = "公式{validation.message.length}", max = 255)
    private String formula;
    
    /**
    * 评判方式
    */
    @Column(nullable=false)
    @ApiModelProperty("评判方式")
    private Integer judgingMethod;
    
    /**
    * 是否默认
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否默认")
	private Boolean isAcquiesce;

    /**
     * 组织机构id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
    
 }