package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.AuthorizeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSort;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.entity.ReportConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.AnalyzeItemSortRepository;
import com.sinoyd.lims.lim.repository.lims.ReportConfigRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.lim.service.ReportConfigService;
import com.sinoyd.lims.lim.service.SerialIdentifierConfigService;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSort;
import com.sinoyd.lims.monitor.repository.lims.FixedPointSortRepository;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.ReceiveSampleRecordCriteria;
import com.sinoyd.lims.pro.criteria.ReportCriteria;
import com.sinoyd.lims.pro.criteria.WorkSheetFolderCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.dto.customer.DtoQcSampleEvaluateDetail;
import com.sinoyd.lims.pro.entity.ExpressageInfo;
import com.sinoyd.lims.pro.entity.ExpressageInfo2Report;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.MathUtil;
import com.sinoyd.lims.strategy.context.DocumentFileNameContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.io.File;
import java.io.Serializable;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 报告操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
@Service
@Slf4j
public class ReportServiceImpl extends BaseJpaServiceImpl<DtoReport, String, ReportRepository> implements ReportService {

    //#region 注入
    @Autowired
    @Qualifier("report")
    @Lazy
    private SerialNumberService serialNumberService;

    @Autowired
    private CommonRepository comRepository;

    @Autowired
    private AuthorizeService authorizeService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectPlanRepository projectPlanRepository;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private ReportDetailService reportDetailService;

    @Autowired
    private ReportDetailRepository reportDetailRepository;

    @Autowired
    @Lazy
    private StatusForReportService statusForReportService;

    @Autowired
    private StatusForReportRepository statusForReportRepository;

    @Autowired
    @Lazy
    private WorkflowService workflowService;

    @Autowired
    @Lazy
    private SerialIdentifierConfigService serialIdentifierConfigService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private PerformanceStatisticForReportDataService performanceStatisticForReportDataService;

    @Autowired
    @Lazy
    private HomeService homeService;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    @Lazy
    private ReportConfigService reportConfigService;

    @Autowired
    @Lazy
    private WorkSheetFolderService workSheetFolderService;

    @Autowired
    private LogForReportRepository logForReportRepository;

    private DocumentFileNameContext documentFileNameContext;

    private QualityControlEvaluateService qualityControlEvaluateService;

    private Project2ReportRepository project2ReportRepository;
    private Project2ReportService project2ReportService;
    private ReportFolderSortInfoRepository reportFolderSortInfoRepository;
    private SampleFolderRepository sampleFolderRepository;
    private AnalyzeItemSortRepository analyzeItemSortRepository;
    private FixedPointSortRepository fixedPointSortRepository;
    private ReportNumberPoolRepository reportNumberPoolRepository;
    private ReportNumberPoolService reportNumberPoolService;
    private SignatureService signatureService;
    private DocumentRepository documentRepository;
    private ExpressageInfoRepository expressageInfoRepository;
    private ExpressageInfo2ReportRepository expressageInfo2ReportRepository;
    private ProjectContractRepository projectContractRepository;
    private MonitorReport2PropertyRepository monitorReport2PropertyRepository;
    private ReportConfigRepository reportConfigRepository;

    /**
     * 通用正则
     */
    protected static final Pattern PATTERN = Pattern.compile("(?<=\\[).*?(?=\\])");

    /**
     * 被退回的
     */
    private final Integer BACK_REFUSED = 0;

    /**
     * 待处理
     */
    private final Integer TODO = 1;

    /**
     * 流程中
     */
    private final Integer INPROCESS = 2;

    /**
     * 已签发
     */
    private final Integer SIGN_ALERADY = 3;

    /**
     * 未知情形
     */
    private final Integer UNKNOW_CONDITION = 999;

    //#endregion

    @Override
    public void findByPage(PageBean<DtoReport> pb, BaseCriteria reportCriteria) {
        ReportCriteria criteria = (ReportCriteria) reportCriteria;
        String userId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "";
        if (criteria.getModule().equals(EnumLIM.EnumReportModule.报告签发.getCode())) {
            if (!StringUtils.isNotNullAndEmpty(userId) || !authorizeService.haveActionPermission(userId, ProCodeHelper.REPORT_SIGN_AUTH)) {
                return;
            }
        }
        //报告发份模块 发放人无法直接查询，此处进行字段转换
        if (StringUtil.isNotEmpty(criteria.getSender())) {
            List<String> expressageInfoIds = expressageInfoRepository.findBySenderLike("%" + criteria.getSender() + "%").stream().map(ExpressageInfo::getId).collect(Collectors.toList());
            if (!expressageInfoIds.isEmpty()) {
                List<String> reportIds = expressageInfo2ReportRepository.findByExpressageInfoIdIn(expressageInfoIds)
                        .stream().map(ExpressageInfo2Report::getReportId).collect(Collectors.toList());
                if (!reportIds.isEmpty()) {
                    criteria.setIds(reportIds);
                } else {
                    //模拟查不出来的效果
                    criteria.setIds(Collections.singletonList(UUID.randomUUID().toString()));
                }
            } else {
                //模拟查不出来的效果
                criteria.setIds(Collections.singletonList(UUID.randomUUID().toString()));
            }
        }
        // 多表关联查询返回自定义字段
        pb.setEntityName("DtoReport r, DtoProject p,DtoProjectPlan pl, DtoStatusForReport s");
        pb.setSelect("select r, p.projectCode, p.projectName, p.projectTypeId, p.grade, p.customerId, p.customerName,p.status as projectStatus,pl.reportMakerId, " +
                "s.status as dealStatus, s.createDate as submitTime, s.lastNewOpinion,p.inspectedEntId,p.inspectedEnt,p.inspectedLinkMan,p.inspectedLinkPhone,p.inspectedAddress,p.reportStamp");

        super.findByPage(pb, reportCriteria);

        List<DtoReport> datas = pb.getData();
        List<DtoReport> newDatas = new ArrayList<>();
        List<DtoSerialIdentifierConfig> list = serialIdentifierConfigService.findListByConfigType(EnumLIM.EnumIdentifierConfig.报告编号.getValue());
        //将list转为map
        Map<String, String> reportTypeMap = list.stream().collect(Collectors.toMap(DtoSerialIdentifierConfig::getId, DtoSerialIdentifierConfig::getConfigName));

        Iterator<DtoReport> costInfoIte = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (costInfoIte.hasNext()) {
            Object obj = costInfoIte.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoReport report = (DtoReport) objs[0];
            report.setProjectCode((String) objs[1]);
            report.setProjectName((String) objs[2]);
            report.setProjectTypeId((String) objs[3]);
            report.setGrade((Integer) objs[4]);
            report.setCustomerId((String) objs[5]);
            report.setCustomerName((String) objs[6]);
            report.setProjectStatus((String) objs[7]);
            report.setReportMakerId((String) objs[8]);
            report.setDealStatus((Integer) objs[9]);
            report.setSubmitTime((Date) objs[10]);
            report.setComment((String) objs[11]);
            report.setInspectedEntId((String) objs[12]);
            report.setInspectedEnt((String) objs[13]);
            report.setInspectedLinkMan((String) objs[14]);
            report.setInspectedLinkPhone((String) objs[15]);
            report.setInspectedAddress((String) objs[16]);

            if (reportTypeMap.containsKey(report.getReportTypeId())) {
                report.setReportTypeName(reportTypeMap.get(report.getReportTypeId()));
            }

            newDatas.add(report);
        }

        //若传了项目id，需要特殊排序
        //优先显示退回的、其次显示待处理和流程中的，最后显示已签发的，并且需要按照报告添加时间倒序排列
        if (StringUtil.isNotEmpty(criteria.getProjectId()) && !UUIDHelper.GUID_EMPTY.equals(criteria.getProjectId())) {
            Comparator<DtoReport> comparator = (a, b) -> {
                if (this.getCompareValue(a).equals(this.getCompareValue(b))) {
                    return b.getCreateTime().compareTo(a.getCreateTime());//按照添加时间倒序
                }
                return this.getCompareValue(a).compareTo(this.getCompareValue(b));
            };
            newDatas.sort(comparator);
        }

        List<String> projectTypeIds = newDatas.stream().map(DtoReport::getProjectTypeId).distinct().collect(Collectors.toList());
        List<DtoProjectType> projectTypes = projectTypeService.findRedisByIds(projectTypeIds);
        //将list转为map
        Map<String, String> projectTypeMap = projectTypes.stream().collect(Collectors.toMap(DtoProjectType::getId, DtoProjectType::getName));

        Map<String, String> personMap = new HashMap<>();
        List<String> personIds = newDatas.stream().map(DtoReport::getReportMakerId).distinct().collect(Collectors.toList());
        if (personIds.size() > 0) {
            List<DtoPerson> persons = personService.findAllDeleted(personIds.stream().distinct().collect(Collectors.toList()));
            //将list转为map
            personMap = persons.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
        }
        //获取所有报告id
        List<String> reportIdList = newDatas.stream().map(DtoReport::getId).distinct().collect(Collectors.toList());
        List<DtoLogForReport> reList = StringUtil.isNotEmpty(reportIdList) ? logForReportRepository.findByObjectIdIn(reportIdList) : new ArrayList<>();
        List<DtoLog> logDataList = reList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
        //按照reportId分组
        Map<String, List<DtoLog>> reportId2LogListMap = logDataList.stream().collect(Collectors.groupingBy(DtoLog::getObjectId));

        List<DtoStatusForReport> statusForReportList = statusForReportRepository.findByReportIdInAndModule(reportIdList, EnumLIM.EnumReportModule.报告签发.getCode());
        statusForReportList = statusForReportList.stream().filter(p -> EnumStatus.已处理.getValue().equals(p.getStatus())).collect(Collectors.toList());
        Map<String, List<DtoStatusForReport>> statusForReportMap = statusForReportList.stream().collect(Collectors.groupingBy(DtoStatusForReport::getReportId));

        //发放信息
        List<DtoExpressageInfo2Report> expressageInfo2ReportList = reportIdList.isEmpty() ? new ArrayList<>() : expressageInfo2ReportRepository.findByReportIdIn(reportIdList);
        List<String> expressageInfoIds = expressageInfo2ReportList.stream().map(ExpressageInfo2Report::getExpressageInfoId).collect(Collectors.toList());
        List<DtoExpressageInfo> expressageInfoList = expressageInfoIds.isEmpty() ? new ArrayList<>() : expressageInfoRepository.findAll(expressageInfoIds);
        List<String> projectIds = newDatas.stream().map(DtoReport::getProjectId).collect(Collectors.toList());
        List<DtoProjectContract> projectContractList = new ArrayList<>();
        if (StringUtil.isNotEmpty(projectIds)) {
            projectContractList = projectContractRepository.findByProjectIdIn(projectIds);
        }
        //上报报告信息
        List<String> monitorReportConfigIds = newDatas.stream().map(DtoReport::getMonitorReportConfigId).collect(Collectors.toList());
        Map<String, DtoReportConfig> monitorReportConfigMap = new HashMap<>();
        if (StringUtil.isNotEmpty(monitorReportConfigIds)) {
            monitorReportConfigMap = reportConfigRepository.findAll(monitorReportConfigIds).stream().collect(Collectors.toMap(ReportConfig::getId, p -> p));
        }
        List<DtoMonitorReport2Property> monitorReport2PropertyList = StringUtil.isNotEmpty(reportIdList) ? monitorReport2PropertyRepository.findByReportIdIn(reportIdList) : new ArrayList<>();
        Map<String, List<DtoMonitorReport2Property>> monitorReport2PropertyMap = monitorReport2PropertyList.stream().collect(Collectors.groupingBy(DtoMonitorReport2Property::getReportId));
        List<DtoReportDetail> reportDetailList = StringUtil.isNotEmpty(reportIdList) ? reportDetailRepository.findByReportIdIn(reportIdList) : new ArrayList<>();
        List<String> sampleIds = reportDetailList.stream().map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findAll(sampleIds) : new ArrayList<>();
        Map<String,List<DtoReportDetail>> reportDetailMap = reportDetailList.stream().collect(Collectors.groupingBy(DtoReportDetail::getReportId));
        for (DtoReport rep : newDatas) {
            rep.setProjectTypeName(projectTypeMap.getOrDefault(rep.getProjectTypeId(), ""));
            String makerName = "";
            List<DtoLog> logList = reportId2LogListMap.get(rep.getId());
            if (StringUtil.isNotEmpty(logList)) {
                List<DtoLog> filterLogList = logList.stream().filter(p -> EnumPRO.EnumLogOperateType.增加报告.toString().equals(p.getOperateInfo()))
                        .sorted(Comparator.comparing(DtoLog::getOperateTime)).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(filterLogList)) {
                    makerName = filterLogList.get(0).getOperatorName();
                }
            }
            rep.setReportMakerName(makerName);
            rep.setSignDate("");
            if (statusForReportMap.containsKey(rep.getId())) {
                Date modDate = statusForReportMap.get(rep.getId()).get(0).getModifyDate();
                String modStr = StringUtil.isNotNull(modDate) ? DateUtil.dateToString(modDate, DateUtil.YEAR) : "";
                if (StringUtil.isNotEmpty(modStr) && !modStr.contains("1753")) {
                    rep.setSignDate(modStr);
                }
            }
            // 发放状态/发放人/发放日期
            if (rep.getGrantStatus() != null) {
                rep.setGrantStatusText(EnumPRO.EnumReportGrantStatus.getByValue(rep.getGrantStatus()));
            }
            expressageInfo2ReportList.stream().filter(e2r -> rep.getId().equals(e2r.getReportId())).findFirst()
                    .flatMap(e2r -> expressageInfoList.stream().filter(e -> e2r.getExpressageInfoId().equals(e.getId())).findFirst())
                    .ifPresent(rep::setExpressageInfo);

            projectContractList.stream().filter(v -> rep.getProjectId().equals(v.getProjectId())).findFirst()
                    .ifPresent(v -> rep.setIsPush(v.getIsPush()));
            //上报报表关联点位标识
            if (monitorReport2PropertyMap.containsKey(rep.getId())) {
                rep.setMonitorReportPropertyIds(monitorReport2PropertyMap.get(rep.getId()).stream().map(DtoMonitorReport2Property::getPropertyId).collect(Collectors.toList()));
            }
            //采样时间
            List<String> reportSampleIds = reportDetailMap.getOrDefault(rep.getId(), new ArrayList<>()).stream().map(DtoReportDetail::getObjectId).collect(Collectors.toList());
            String samplingTime = sampleList.stream().filter(p -> reportSampleIds.contains(p.getId())).filter(p -> StringUtil.isNotNull(p.getSamplingTimeBegin()))
                    .map(p-> DateUtil.dateToString(p.getSamplingTimeBegin(), DateUtil.YEAR)).distinct() .sorted().collect(Collectors.joining("、"));
            rep.setSamplingTime(samplingTime);
        }
        //设置报告分析项目排序名称，点位排序名称返回给前端
        setAnalyzeFolderSortName(newDatas);
        pb.setData(newDatas);
    }

    /**
     * 设置报告分析项目排序名称，点位排序名称返回给前端
     *
     * @param reportList 报告列表
     */
    private void setAnalyzeFolderSortName(List<DtoReport> reportList) {
        Set<String> analyzeItemSortIdSet = new HashSet<>(), folderSortIdSet = new HashSet<>();
        for (DtoReport report : reportList) {
            if (StringUtil.isNotEmpty(report.getAnalyseItemSortId())) {
                analyzeItemSortIdSet.add(report.getAnalyseItemSortId());
            }
            if (StringUtil.isNotEmpty(report.getFolderSortId())) {
                folderSortIdSet.add(report.getFolderSortId());
            }
        }
        List<DtoAnalyzeItemSort> analyzeItemSortList = StringUtil.isNotEmpty(analyzeItemSortIdSet) ? analyzeItemSortRepository.findAll(analyzeItemSortIdSet) : new ArrayList<>();
        Map<String, DtoAnalyzeItemSort> analyzeItemSortMap = analyzeItemSortList.stream().collect(Collectors.toMap(DtoAnalyzeItemSort::getId, dto -> dto));
        List<DtoFixedPointSort> fixedPointSortList = StringUtil.isNotEmpty(folderSortIdSet) ? fixedPointSortRepository.findAll(folderSortIdSet) : new ArrayList<>();
        Map<String, DtoFixedPointSort> fixedPointSortMap = fixedPointSortList.stream().collect(Collectors.toMap(DtoFixedPointSort::getId, dto -> dto));
        for (DtoReport report : reportList) {
            report.setAnalyseItemSortName(analyzeItemSortMap.containsKey(report.getAnalyseItemSortId())
                    ? analyzeItemSortMap.get(report.getAnalyseItemSortId()).getSortName() : "");
            report.setFolderSortName(fixedPointSortMap.containsKey(report.getFolderSortId()) ? fixedPointSortMap.get(report.getFolderSortId()).getSortName() : "");
        }
    }

    @Override
    public DtoReport findAttachPath(String id) {
        return repository.findOne(id);
    }

    /**
     * 获取报告信息
     *
     * @param id 报告id
     * @return 报告信息
     */
    @Override
    public DtoReport findOne(String id) {
        DtoReport report = super.findOne(id);
        //将报告下的关联id返回，用于前端判断该报告是否有关联
        List<DtoReportDetail> details = reportDetailRepository.findByReportId(id);
        if (StringUtil.isNotNull(details) && details.size() > 0) {
            List<String> objectIds = details.stream().map(DtoReportDetail::getObjectId).collect(Collectors.toList());
            report.setObjectIds(objectIds);
            //采样时间
            List<DtoSample> sampleList = sampleRepository.findAll(objectIds);
            String samplingTime = sampleList.stream().filter(p -> StringUtil.isNotNull(p.getSamplingTimeBegin()))
                    .map(p-> DateUtil.dateToString(p.getSamplingTimeBegin(), DateUtil.YEAR)).distinct() .sorted().collect(Collectors.joining("、"));
            report.setSamplingTime(samplingTime);
        } else {
            report.setObjectIds(new ArrayList<>());
        }
        //获取点位排序信息
        getFolderSortInfo(report);
        //获取分析项目排序名称
        getAnalyzeItemSortName(report);
        DtoProject project = projectRepository.findOne(report.getProjectId());
        if (StringUtil.isNotNull(project)) {
            report.setInspectedLinkMan(project.getInspectedLinkMan());
            report.setInspectedLinkPhone(project.getInspectedLinkPhone());
            report.setInspectedAddress(project.getInspectedAddress());
            report.setProjectCode(project.getProjectCode());
            report.setProjectName(project.getProjectName());
            report.setInspectedEnt(project.getInspectedEnt());
            DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());
            report.setProjectTypeName(projectType!= null?projectType.getName():"");
        }
        //报告盖章
        if(StringUtil.isNotEmpty(report.getReportStamp())){
            List<String> reportStampList = Arrays.stream(report.getReportStamp().split(",")).filter(MathUtil::isInteger)
                    .map(p->EnumProjectReportStamp.getNameByValue(Integer.valueOf(p))).distinct().sorted().collect(Collectors.toList());
            report.setReportStampStr(String.join("、",reportStampList));
        }
        return report;
    }

    @Transactional
    @Override
    public DtoReport save(DtoReport report) {
        validateForCreateReport(report);
        report.setSecurityCode(UUIDHelper.NewID().substring(0, 8).toUpperCase());
        report.setStatus(EnumReportState.编制报告中.name());
        report.setDataChangeStatus(EnumReportChangeStatus.未变更.getValue());
        report.setCreatePersonId(PrincipalContextUser.getPrincipal().getUserId());
        report.setCreateTime(new Date());
        //这里考虑性能，后端不判断样品是否已检毕，前端进行限制
        List<DtoReportDetail> details = new ArrayList<>();
        for (String objectId : report.getObjectIds()) {
            DtoReportDetail detail = new DtoReportDetail();
            detail.setObjectId(objectId);
            detail.setObjectType(report.getObjectType());
            detail.setReportId(report.getId());
            details.add(detail);
        }
        if (details.size() > 0) {
            reportDetailService.save(details);
        }
        workflowService.createInstance(EnumWorkflowCode.报告.getValue(), report.getId());
        //添加状态记录
        statusForReportService.createStatus(report.getId());
        if (!StringUtils.isNotNull(report.getMonitorReportConfigId())) {
            report.setMonitorReportConfigId(UUIDHelper.GUID_EMPTY);
        }
        super.save(report);
        //保存项目和报告的关联关系
        project2ReportRepository.save(new DtoProject2Report(report.getId(), report.getProjectId(), 0));
        //保存报告点位排序信息
        saveFolderSortInfo(report);
        //修改报告编号使用状态
        modifyReportStatus(report, 1, true);
        //保存例行报告配置
        Map<String, Object> monitorReportParamMap = new HashMap<>();
        monitorReportParamMap.put("monitorReportName", StringUtil.isNotEmpty(report.getMonitorReportName()) ? report.getMonitorReportName() : "");
        monitorReportParamMap.put("propertyIds", StringUtil.isNotEmpty(report.getMonitorReportPropertyIds()) ? report.getMonitorReportPropertyIds() : new ArrayList<>());
        saveMonitorReportConfig(report.getId(), report.getMonitorReportConfigId(), monitorReportParamMap);

        newLogService.createLog(report.getId(), "增加了报告:" + (StringUtils.isNotNullAndEmpty(report.getCode()) ? report.getCode() : ""), "",
                EnumLogType.项目报告.getValue(), EnumLogObjectType.报告.getValue(), EnumLogOperateType.增加报告.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //更改项目详情中的编制报告人为当前新增报告的人
        String projectId = report.getProjectId();
        if (StringUtil.isNotEmpty(projectId)) {
            DtoProjectPlan projectPlan = projectPlanRepository.findByProjectId(projectId);
            if (StringUtil.isNotNull(projectPlan)) {
                projectPlan.setReportMakerId(PrincipalContextUser.getPrincipal().getUserId());
                projectPlanRepository.save(projectPlan);
            }
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                proService.sendProMessage(EnumProAction.新增报告, report.getProjectId());
            }
        });

        return report;
    }

    /**
     * 修改报告编号使用状态
     *
     * @param report 报告
     * @param status 状态
     * @param status 是否更新时间
     */
    private void modifyReportStatus(DtoReport report, int status, boolean updateTime) {
        List<DtoReportNumberPool> numberPoolList = reportNumberPoolRepository.findByCodeAndYearAndReportTypeId(report.getCode(), Integer.parseInt(report.getReportYear()),
                report.getReportTypeId());
        if (StringUtil.isNotEmpty(numberPoolList)) {
            for (DtoReportNumberPool dtoReportNumberPool : numberPoolList) {
                dtoReportNumberPool.setStatus(status);
                if (updateTime) {
                    dtoReportNumberPool.setUsedDate(new Date());
                } else {
                    dtoReportNumberPool.setUsedDate(null);
                }
            }
            reportNumberPoolRepository.save(numberPoolList);
        }
    }

    /**
     * 修改报告编号使用状态
     *
     * @param reportList 报告列表
     * @param status     状态
     */
    private void modifyReportStatus(List<DtoReport> reportList, int status) {
        List<String> codeList = reportList.stream().map(DtoReport::getCode).collect(Collectors.toList());
        List<DtoReportNumberPool> numberPoolList = reportNumberPoolRepository.findByCodeIn(codeList);
        List<DtoReportNumberPool> updatePools = new ArrayList<>();
        if (StringUtil.isNotEmpty(numberPoolList)) {
            Map<String, List<DtoReportNumberPool>> poolMap = numberPoolList.stream().collect(Collectors.groupingBy(DtoReportNumberPool::getCode));
            for (DtoReport report : reportList) {
                List<DtoReportNumberPool> poolList = poolMap.getOrDefault(report.getCode(), new ArrayList<>());
                poolList = poolList.stream().filter(p -> report.getReportYear().equals(String.valueOf(p.getYear())) && report.getReportTypeId().equals(p.getReportTypeId()))
                        .collect(Collectors.toList());
                if (StringUtil.isNotEmpty(poolList)) {
                    updatePools.addAll(poolList);
                }
            }
        }
        if (StringUtil.isNotEmpty(updatePools)) {
            updatePools.forEach(p -> p.setStatus(status));
            reportNumberPoolRepository.save(updatePools);
        }
    }

    /**
     * 获取点位排序信息
     *
     * @param report 报告对象
     */
    private void getFolderSortInfo(DtoReport report) {
        List<DtoReportFolderSortInfo> folderSortInfoList = reportFolderSortInfoRepository.findByReportId(report.getId());
        folderSortInfoList.sort(Comparator.comparing(DtoReportFolderSortInfo::getOrderNum));
        List<String> sortFolderIdList = folderSortInfoList.stream().map(DtoReportFolderSortInfo::getFolderId).collect(Collectors.toList());
        List<DtoSampleFolder> folderList = StringUtil.isNotEmpty(sortFolderIdList) ? sampleFolderRepository.findAll(sortFolderIdList) : new ArrayList<>();
        Map<String, DtoSampleFolder> folderMap = folderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
        for (DtoReportFolderSortInfo sortInfo : folderSortInfoList) {
            DtoSampleFolder folder = folderMap.getOrDefault(sortInfo.getFolderId(), null);
            sortInfo.setFolderName(StringUtil.isNotNull(folder) ? folder.getWatchSpot() : "");
        }
        report.setFolderSortInfoList(folderSortInfoList);
        report.setSortFolderIdList(sortFolderIdList);
        String folderSortId = StringUtil.isNotEmpty(report.getFolderSortId()) ? report.getFolderSortId() : UUIDHelper.GUID_EMPTY;
        DtoFixedPointSort fixedPointSort = fixedPointSortRepository.findOne(folderSortId);
        report.setFolderSortName(StringUtil.isNotNull(fixedPointSort) ? fixedPointSort.getSortName() : "");
    }

    /**
     * 获取点位排序信息
     *
     * @param report 报告对象
     */
    private void getAnalyzeItemSortName(DtoReport report) {
        String sortId = StringUtil.isNotEmpty(report.getAnalyseItemSortId()) ? report.getAnalyseItemSortId() : UUIDHelper.GUID_EMPTY;
        DtoAnalyzeItemSort sort = analyzeItemSortRepository.findOne(sortId);
        report.setAnalyseItemSortName(StringUtil.isNotNull(sort) ? sort.getSortName() : "");
    }

    /**
     * 保存报告点位排序信息
     *
     * @param report 报告
     */
    private void saveFolderSortInfo(DtoReport report) {
        List<String> sortFolderIdList = report.getSortFolderIdList();
        if (StringUtil.isNotEmpty(sortFolderIdList)) {
            List<DtoReportFolderSortInfo> sortInfoList = getSortInfoList(report.getId(), sortFolderIdList);
            reportFolderSortInfoRepository.save(sortInfoList);
        }
    }

    /**
     * 初始化点位排序对象列表
     *
     * @param reportId 报告id
     */
    private List<DtoReportFolderSortInfo> getSortInfoList(String reportId, List<String> sortFolderIdList) {
        List<DtoReportFolderSortInfo> sortInfoList = new ArrayList<>();
        for (int i = 0; i < sortFolderIdList.size(); i++) {
            DtoReportFolderSortInfo sortInfo = new DtoReportFolderSortInfo();
            sortInfo.setReportId(reportId);
            sortInfo.setFolderId(sortFolderIdList.get(i));
            sortInfo.setOrderNum(i);
            sortInfoList.add(sortInfo);
        }
        return sortInfoList;
    }

    /**
     * 修改报告点位排序信息
     *
     * @param report 报告
     */
    private void updateFolderSortInfo(DtoReport report) {
        //先删除老的排序信息
        List<DtoReportFolderSortInfo> oldSortInfoList = reportFolderSortInfoRepository.findByReportId(report.getId());
        if (StringUtil.isNotEmpty(oldSortInfoList)) {
            reportFolderSortInfoRepository.logicDeleteById(oldSortInfoList.stream().map(DtoReportFolderSortInfo::getId).collect(Collectors.toList()), new Date());
        }
        //再插入新的
        if (StringUtil.isNotEmpty(report.getSortFolderIdList())) {
            List<DtoReportFolderSortInfo> sortInfoList = getSortInfoList(report.getId(), report.getSortFolderIdList());
            reportFolderSortInfoRepository.save(sortInfoList);
        }
    }

    /**
     * 新增报告时校验
     *
     * @param report 报告实体
     */
    protected void validateForCreateReport(DtoReport report) {
        DtoProject project = projectRepository.findOne(report.getProjectId());
        if (project.getReportStatus().equals(EnumReportStatus.已完成.getValue())) {
            throw new BaseException("该项目已完成报告，无法进行报告添加");
        }
        if (!StringUtils.isNotNullAndEmpty(report.getCode())) {
            throw new BaseException("请先生成报告编号");
        }
        DtoReport repByCode = this.findByCode(report.getCode());
        if (StringUtil.isNotNull(repByCode)) {
            throw new BaseException("报告编号重复，请重新输入");
        }
    }

    /**
     * 修改报告时校验
     *
     * @param report 报告实体
     */
    protected void validateForUpdateReport(DtoReport report) {
        if (!StringUtils.isNotNullAndEmpty(report.getCode())) {
            throw new BaseException("请先生成报告编号");
        }
        Integer count = repository.countByCodeAndIdNot(report.getCode(), report.getId());
        if (count > 0) {
            throw new BaseException("报告编号重复，请重新输入");
        }
    }

    @Transactional
    @Override
    public DtoReport update(DtoReport report) {
        validateForUpdateReport(report);
        DtoReport dto = repository.findOne(report.getId());
        StringBuilder builder = new StringBuilder();
        if (!dto.getReportYear().equals(report.getReportYear())) {
            builder.insert(0, String.format("</br>报告年份由'%s'修改为'%s':", dto.getReportYear(), report.getReportYear()));
        }
        if (!dto.getReportTypeId().equals(report.getReportTypeId())) {
            List<DtoSerialIdentifierConfig> reportTypes = serialIdentifierConfigService.findListByConfigType(EnumLIM.EnumIdentifierConfig.报告编号.getValue());
            builder.insert(0, String.format("</br>报告类型由'%s'修改为'%s':",
                    reportTypes.stream().filter(p -> p.getId().equals(dto.getReportTypeId())).map(DtoSerialIdentifierConfig::getConfigName).findFirst().orElse(""),
                    reportTypes.stream().filter(p -> p.getId().equals(report.getReportTypeId())).map(DtoSerialIdentifierConfig::getConfigName).findFirst().orElse("")));
        }
        if (!dto.getCode().equals(report.getCode())) {
            builder.insert(0, String.format("</br>报告编号由'%s'修改为'%s':", dto.getCode(), report.getCode()));
            //老的报告编号置为未使用
            modifyReportStatus(dto, 0, false);
            //新的报告编号置为已使用
            modifyReportStatus(report, 1, true);
        }
        String comment = builder.toString();
        if (StringUtils.isNotNullAndEmpty(comment)) {
            newLogService.createLog(report.getId(), "修改了报告:" + report.getCode() + comment, "",
                    EnumLogType.项目报告.getValue(), EnumLogObjectType.报告.getValue(), EnumLogOperateType.修改报告.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
        //修改报告点位排序信息
        updateFolderSortInfo(report);
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.报告关联样品, report.getProjectId());
                    }
                }
        );

        return comRepository.merge(report);
    }

    /**
     * 删除报告
     */
    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        DtoReport report = super.findOne(idStr);
        if (report.getStatus().equals(EnumReportState.已签发.toString())) {
            throw new BaseException("该报告已签发，无法进行删除");
        }
        DtoProject project = projectRepository.findOne(report.getProjectId());
        if (project.getReportStatus().equals(EnumReportStatus.已完成.getValue())) {
            throw new BaseException("该项目已完成报告，无法进行报告删除");
        }

        statusForReportRepository.deleteByReportId(idStr);
        reportDetailRepository.deleteByReportId(idStr);
        //报告编号使用状态置为未使用
        modifyReportStatus(report, 0, false);
        Integer count = super.logicDeleteById(idStr);
        //删除项目和报告的关联关系
        project2ReportService.deleteByReportId(report.getId());

        newLogService.createLog(report.getProjectId(), "删除了报告:" + report.getCode(), "",
                EnumLogType.项目信息.getValue(), EnumLogObjectType.报告.getValue(), EnumLogOperateType.删除报告.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.删除报告, report.getProjectId());
                    }
                }
        );
        return count;
    }

    /**
     * 删除报告(报告统计)
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        String userId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "";
        if (!authorizeService.haveActionPermission(userId, ProCodeHelper.REPORT_ALL_AUTH)) {
            throw new BaseException("您没有删除报告的权限！");
        }
        List<String> reportIds = new ArrayList<>();
        for (Object id : ids) {
            reportIds.add(String.valueOf(id));
        }
        if (reportIds.size() > 0) {
            List<DtoReport> reportList = repository.findAll(reportIds);

            List<DtoLog> logList = new ArrayList<>();
            for (DtoReport report : reportList) {
                DtoLog log = new DtoLog();
                log.setId(UUIDHelper.NewID());
                log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
                log.setOperateTime(new Date());
                log.setOperateInfo(EnumLogOperateType.删除报告.toString());
                log.setLogType(EnumLogType.项目信息.getValue());
                log.setObjectId(report.getProjectId());
                log.setObjectType(EnumLogObjectType.报告.getValue());
                log.setComment("删除了报告");
                log.setOpinion("");
                log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                log.setRemark("");
                logList.add(log);
            }
            newLogService.createLog(logList, EnumLogType.项目信息.getValue());
            modifyReportStatus(reportList, 0);

            statusForReportRepository.deleteByReportIdIn(reportIds);
            reportDetailRepository.deleteByReportIdIn(reportIds);
            Integer count = repository.logicDeleteById(ids, new Date());

            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            List<String> projectIds = reportList.stream().map(DtoReport::getProjectId).distinct().collect(Collectors.toList());
                            for (String projectId : projectIds) {
                                proService.sendProMessage(EnumProAction.删除报告, projectId);
                            }
                        }
                    }
            );

            return count;
        }
        return 0;
    }

    @Override
    public DtoReport findByCode(String code) {
        return repository.findByCode(code);
    }

    @Override
    public String createReportCode(String reportName, Integer year, String projectCode, String reportId, String reportCode) {
        return serialNumberService.createNewNumber(reportName, year, PrincipalContextUser.getPrincipal().getUserId(),
                projectCode, reportCode);
    }

    /**
     * 工作流操作
     *
     * @param dtoWorkflowSign 实体
     */
    @Transactional
    @Override
    public void reportSignal(DtoWorkflowSign dtoWorkflowSign) {
        try {
            List<DtoPerson> personList = personService.findAll();
            Map<String, DtoPerson> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, dto -> dto));
            if (StringUtils.isNotNullAndEmpty(dtoWorkflowSign.getNextOperatorId()) && !UUIDHelper.GUID_EMPTY.equals(dtoWorkflowSign.getNextOperatorId())) {
                DtoPerson per = personMap.get(dtoWorkflowSign.getNextOperatorId());
                dtoWorkflowSign.setNextOperator(StringUtil.isNotNull(per) ? per.getCName() : "");
            } else {
                dtoWorkflowSign.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            }
            if (dtoWorkflowSign.getObjectIds().size() > 0) {
                List<DtoReport> reportList = this.findAll(dtoWorkflowSign.getObjectIds());
                // 校验报告关联样品中的状态
                this.checkSampleStatus(reportList, dtoWorkflowSign.getSignal());
                Map<String, String> statusMap = reportList.stream().collect(Collectors.toMap(DtoReport::getId, DtoReport::getStatus));
                String to = workflowService.submitSign(dtoWorkflowSign);
                String from = "";//当前状态;
                if (!StringUtils.isNotNullAndEmpty(to)) {
                    to = EnumReportState.已签发.toString();
                }
                for (DtoReport rep : reportList) {
                    from = statusMap.get(rep.getId());
                    statusForReportService.modifyStatus(from, to, dtoWorkflowSign, rep, personMap);
                    if (to.equals(EnumReportState.已签发.toString())) {
                        proService.sendProMessageWoTransactional(EnumProAction.报告签发通过, rep.getProjectId());
                        repository.updateReportStatus(dtoWorkflowSign.getObjectIds(), to, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                    } else if (statusMap.get(rep.getId()).equals(EnumReportState.已签发.toString())) {
                        proService.sendProMessageWoTransactional(EnumProAction.报告签发退回, rep.getProjectId());
                    }
                }
                if (to.equals(EnumReportState.已签发.toString())) {
                    repository.updateReportStatus(dtoWorkflowSign.getObjectIds(), to, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                }
                //需要记录工作量统计
                if (dtoWorkflowSign.getSignal().equals("reportSubmit") || dtoWorkflowSign.getSignal().equals("reportSubmitForReview")) {
                    //编制报告提交时,保存一审人id，二审人id
                    String firstInstanceId = StringUtil.isNotEmpty(dtoWorkflowSign.getNextOperatorId()) ? dtoWorkflowSign.getNextOperatorId() : UUIDHelper.GUID_EMPTY;
                    String secondInstanceId = StringUtil.isNotEmpty(dtoWorkflowSign.getSecondInstanceId()) ? dtoWorkflowSign.getSecondInstanceId() : UUIDHelper.GUID_EMPTY;
                    for (DtoReport rep : reportList) {
                        rep.setFirstInstanceId(firstInstanceId);
                        rep.setSecondInstanceId(secondInstanceId);
                    }
                    repository.save(reportList);
                    List<String> projectIds = reportList.stream().map(DtoReport::getProjectId).distinct().collect(Collectors.toList());
                    performanceStatisticForReportDataService.createReportStatistic(projectIds,
                            PrincipalContextUser.getPrincipal().getUserId(),
                            new Date(),
                            PrincipalContextUser.getPrincipal());
                }
                if ("backToReport".equals(dtoWorkflowSign.getSignal())) {
                    List<DtoDocument> documents = documentRepository.findByFolderIdIn(dtoWorkflowSign.getObjectIds()).stream().filter(d -> !d.getIsDeleted() && d.getIsTranscript() && d.getFilename().contains("副本")).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(documents)) {
                        documentRepository.logicDeleteById(documents.stream().map(DtoDocument::getId).collect(Collectors.toList()), new Date());
                    }
                }
                //计算首页数据缓存
                String currentModule = EnumReportState.getModuleCode(from);
                String nextModule = EnumReportState.getModuleCode(to);
                homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                        PrincipalContextUser.getPrincipal().getOrgId()
                        , currentModule, nextModule);
                //保证事务提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                if (dtoWorkflowSign.getSignal().toLowerCase().contains("back") || dtoWorkflowSign.getSignal().toLowerCase().contains("not")) {
                                    createReportStatusBackLog(dtoWorkflowSign.getObjectIds(), dtoWorkflowSign.getOption());
                                } else {
                                    createReportStatusUpdateLog(dtoWorkflowSign.getObjectIds(), dtoWorkflowSign.getOption(),
                                            dtoWorkflowSign.getNextOperatorId(), dtoWorkflowSign.getNextOperator(), dtoWorkflowSign.getSignal(), personMap);
                                }
                            }
                        }
                );
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BaseException(ex.getMessage());
        }
    }

    /**
     * 检查报告中关联样品的状态
     *
     * @param reportList 报告数据
     */
    private void checkSampleStatus(List<DtoReport> reportList, String signal) {
        if (!signal.contains("NotPass") && !"backToReport".equals(signal)) {
            List<String> reportIds = reportList.stream().map(DtoReport::getId).collect(Collectors.toList());
            List<DtoReportDetail> reportDetailList = reportDetailRepository.findByReportIdIn(reportIds);
            List<String> sampleIds = reportDetailList.stream().map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
            List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findAll(sampleIds) : new ArrayList<>();
            String sampleCodes = sampleList.stream().filter(p -> !EnumSampleStatus.样品检毕.toString().equals(p.getStatus())).map(DtoSample::getCode).collect(Collectors.joining(","));
            if (StringUtil.isNotEmpty(sampleCodes)) {
                throw new BaseException(sampleCodes + "样品未检毕，请确认后再操作！");
            }
        }
        if (reportList.stream().anyMatch(p -> p.getStatus().equals(EnumReportState.报告未通过.name()))) {
            if (!signal.contains("reportSubmit")) {
                throw new BaseException("存在报告未通过的数据，请检索确认再操作！");
            }
        }
    }

    @Transactional
    @Override
    public void updateReportNum(String id, Integer reportNum) {
        repository.updateReportNum(id, reportNum, PrincipalContextUser.getPrincipal().getUserId(), new Date());
    }

    @Override
    public List<DtoReceiveSampleRecord> findReceiveSampleRecord(String reportId) {
        List<DtoReceiveSampleRecord> receiveSampleRecordList = new ArrayList<>();
        DtoReport report = repository.findOne(reportId);
        if (StringUtil.isNotNull(report)) {
            List<DtoReportDetail> reportDetailList = reportDetailRepository.findByReportId(reportId);
            if (StringUtil.isNotEmpty(reportDetailList)) {
                //获取报告下所有的样品id
                List<String> sampleIdList = reportDetailList.stream().map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(sampleIdList)) {
                    List<DtoSample> sampleList = sampleRepository.findByIdInAndIsDeletedFalse(sampleIdList);
                    List<String> receiveIdList = sampleList.stream().map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(receiveIdList)) {
                        //查询送样单
                        PageBean<DtoReceiveSampleRecord> pageBean = new PageBean<>();
                        pageBean.setPageNo(1);
                        pageBean.setRowsPerPage(Integer.MAX_VALUE);
                        pageBean.setSort("sendTime-, recordCode-");
                        ReceiveSampleRecordCriteria criteria = new ReceiveSampleRecordCriteria();
                        criteria.setModule(EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue());
                        criteria.setReceiveIdList(receiveIdList);
                        receiveSampleRecordService.findByPage(pageBean, criteria);
                        receiveSampleRecordList = pageBean.getData();
                        //过滤掉没有现场数据的送样单
//                        receiveSampleRecordList = filterSampleRecordWithoutField(receiveSampleRecordList);

                    }
                }
            }
        }
        return receiveSampleRecordList;
    }

    @Override
    public List<DtoWorkSheetFolder> findWorksheetFolder(String reportId) {
        List<DtoWorkSheetFolder> workSheetFolderList = new ArrayList<>();
        DtoReport report = repository.findOne(reportId);
        if (StringUtil.isNotNull(report)) {
            List<DtoReportDetail> reportDetailList = reportDetailRepository.findByReportId(reportId);
            if (StringUtil.isNotEmpty(reportDetailList)) {
                //获取报告下所有的样品id
                List<String> sampleIdList = reportDetailList.stream().map(DtoReportDetail::getObjectId).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(sampleIdList)) {
                    List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList);
                    List<String> workSheetFolderIdList = analyseDataList.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(workSheetFolderIdList)) {
                        PageBean<DtoWorkSheetFolder> pageBean = new PageBean<>();
                        pageBean.setPageNo(1);
                        pageBean.setRowsPerPage(Integer.MAX_VALUE);
                        pageBean.setSort("workSheetCode-");
                        WorkSheetFolderCriteria criteria = new WorkSheetFolderCriteria();
                        criteria.setWorkSheetFolderIdList(workSheetFolderIdList);
                        criteria.setIsShowAnalyzeItemName(true);
                        workSheetFolderService.findByPage(pageBean, criteria);
                        workSheetFolderList = pageBean.getData();
                    }
                }
            }
        }
        return workSheetFolderList;
    }

    /**
     * 获取报表名称
     *
     * @param map      生成报表id
     * @param configId 报表配置名称
     * @return 报表名称
     */
    @Override
    public DtoReportConfig documentFileName(Map<String, Object> map, String configId) {
        DtoReportConfig reportConfig = reportConfigService.findOne(configId);
        String fileName = reportConfig.getOutputName();
        if (reportConfig.getIsDefineFileName()) {
            Map<String, String> dataMap = documentFileNameContext.generateDocumentName(reportConfig.getBeanName(), map);
            //附件的名称
            fileName = reportConfig.getDefineFileName();
            if (StringUtil.isNotNull(reportConfig.getDefineFileName())) {
                Matcher matcher = PATTERN.matcher(reportConfig.getDefineFileName());
                while (matcher.find()) {
                    //如何得到key
                    String groupValue = matcher.group();
                    String value = "";
                    if (StringUtil.isNotNull(dataMap.get(groupValue))) {
                        value = dataMap.get(groupValue);
                    }
                    fileName = fileName.replace("[" + groupValue + "]", value);
                }
            }
        }
        String spValue = "";
        if (fileName.contains(File.separator)) {
            spValue = File.separator;
        } else if (fileName.contains("/")) {
            spValue = "/";
        }
        if (StringUtil.isNotEmpty(spValue)) {
            int len = fileName.split(spValue).length;
            fileName = fileName.split(spValue)[len - 1];
        }
        reportConfig.setFileName(fileName);
        return reportConfig;
    }

    /**
     * 校验报告所选样品对应的质控样中是否有不合格的样品
     *
     * @param reportIdList 报告id列表
     * @return 不合格样品信息
     */
    @Override
    public String checkQcSamplePass(PageBean<DtoQcSampleEvaluateDetail> pageBean, List<String> reportIdList) {
        DtoQcSampleEvaluateDetail evaluateDetail = new DtoQcSampleEvaluateDetail();
        evaluateDetail.setReportIdList(reportIdList);
        List<DtoQcSampleEvaluateDetail> evaluateDetailList = qualityControlEvaluateService.findEvaluateDetailByPage(pageBean, evaluateDetail);
        Set<String> unPassSampleCodeSet = new HashSet<>();
        for (DtoQcSampleEvaluateDetail detail : evaluateDetailList) {
            if (StringUtil.isNotEmpty(detail.getIsPass()) && "0".equals(detail.getIsPass())) {
                unPassSampleCodeSet.add(detail.getSampleCode());
            }
        }
        return StringUtil.isNotEmpty(unPassSampleCodeSet) ? "质控样品：" + String.join("、", unPassSampleCodeSet) + " 不合格!" : "";
    }

    @Override
    @Transactional
    public void saveSortFolder(String reportId, List<String> sortFolderIdList) {
        DtoReport report = repository.findOne(reportId);
        if (StringUtil.isNull(report)) {
            throw new BaseException("报告不存在！");
        }
        report.setSortFolderIdList(sortFolderIdList);
        updateFolderSortInfo(report);
    }

    @Override
    public DtoReportNumberPool createCode(DtoReportNumberPool reportNumberPool) {
        // 获取当前报告类型的最大流水号加一
        Map<String, Object> map = reportNumberPoolService.queryMaxNumber(reportNumberPool);
        Integer number = (Integer) map.get("number");
        String serialType = (String) map.get("serialType");
        List<DtoReportNumberPool> reportNumberPools = reportNumberPoolRepository.findByReportTypeIdAndYearAndSerialType(reportNumberPool.getReportTypeId(), reportNumberPool.getYear(), serialType);
        reportNumberPools.removeIf(p -> StringUtil.isNull(p.getNumber()));
        Optional<DtoReportNumberPool> dtoReportNumberPool = reportNumberPools.stream().max(Comparator.comparingInt(DtoReportNumberPool::getNumber));
        if (dtoReportNumberPool.isPresent()) {
            DtoReportNumberPool max = dtoReportNumberPool.get();
            DtoReport report = repository.findByCode(max.getCode());
            //如果当前编号没有保存报告，那么不需要再次生成
            if (StringUtil.isNull(report)) {
                return max;
            } else {
                if (number <= max.getNumber()) {
                    number = max.getNumber() + 1;
                }
            }
        }
        reportNumberPool.setCode(number.toString());
        // 报告编号池创建新数据
        return reportNumberPoolService.save(reportNumberPool);
    }

    @Transactional
    @Override
    public void reportSign(Map<String, Object> map) {
        try {
            String reportId = (String) map.get("reportId");
            List<String> docIds = (List<String>) map.get("docIds");
            // 先删除已经在的副本
            List<DtoDocument> documentList = documentRepository.findByIdIn(docIds);
            List<DtoDocument> documentAll = documentRepository.findByFolderId(reportId);
            List<DtoDocument> deleteList = new ArrayList<>();
            for (DtoDocument dtoDocument : documentList) {
                String[] split = dtoDocument.getFilename().split("\\.");
                String name = split[0];
                deleteList.addAll(documentAll.stream().filter(p -> p.getFilename().contains(name)
                        && p.getIsTranscript().equals(true)).collect(Collectors.toList()));
            }
            if (StringUtil.isNotEmpty(deleteList)) {
                deleteList.forEach(p -> {
                    p.setIsDeleted(true);
                });
                documentRepository.save(deleteList);
            }
            DtoReport dtoReport = super.findOne(reportId);
            if (signatureService.checkUpReport(reportId)) {
                List<List<String>> sigInfoList = signatureService.getUpReportSigInfo(reportId);
                for (List<String> sigInfo : sigInfoList) {
                    signatureService.sig(reportId, Collections.singletonList(sigInfo.get(1)), Integer.valueOf(sigInfo.get(0)), "", DateUtil.nowTime("yyyy.MM.dd"));
                }
                signatureService.changeExcelPdf(reportId);
            } else {
                Integer type = EnumPRO.EnumSigType.maker.getValue();
                // 签名
                signatureService.sig(reportId, docIds, Collections.singletonList(dtoReport.getCreator()), type, "", DateUtil.nowTime("yyyy.MM.dd"));
                // 签章并生成pdf
                signatureService.sealChangePdf(reportId);
            }
        } catch (Exception ex) {
            log.error("报告签名执行失败！", ex);
            throw new BaseException(ex.getMessage());
        }
    }

    @Transactional
    @Override
    public void reloadReport(String reportId) {
        DtoReport oldReport = repository.findOne(reportId);
        DtoReport report = new DtoReport();
        BeanUtils.copyProperties(oldReport, report, "id");
        report.setSecurityCode(UUIDHelper.NewID().substring(0, 8).toUpperCase());
        report.setStatus(EnumReportState.编制报告中.name());
        report.setDataChangeStatus(EnumReportChangeStatus.未变更.getValue());
        report.setCreatePersonId(PrincipalContextUser.getPrincipal().getUserId());
        report.setGrantStatus(1);
        report.setCreateTime(new Date());
        //编号
        DtoProject project = projectRepository.findOne(oldReport.getProjectId());
        report.setCode(this.createReportCode("重新编制", Integer.valueOf(report.getReportYear()),
                project.getProjectCode(), report.getId(), oldReport.getCode()));
        repository.save(report);
        List<DtoReportDetail> oldReportDetailList = reportDetailRepository.findByReportId(reportId);
        List<DtoReportDetail> detailList = new ArrayList<>();
        oldReportDetailList.forEach(p -> {
            DtoReportDetail detail = new DtoReportDetail();
            BeanUtils.copyProperties(p, detail, "id");
            detail.setReportId(report.getId());
            detailList.add(detail);
        });
        reportDetailRepository.save(detailList);

        workflowService.createInstance(EnumWorkflowCode.报告.getValue(), report.getId());
        //添加状态记录
        statusForReportService.createStatus(report.getId());
        //保存项目和报告的关联关系
        project2ReportRepository.save(new DtoProject2Report(report.getId(), report.getProjectId(), 0));

        newLogService.createLog(report.getId(), "增加了报告:" + report.getCode(), "",
                EnumLogType.项目报告.getValue(), EnumLogObjectType.报告.getValue(), EnumLogOperateType.增加报告.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                proService.sendProMessage(EnumProAction.新增报告, report.getProjectId());
            }
        });
    }

    @Override
    @Transactional
    public void saveMonitorReportConfig(String reportId, String reportConfigId, Map<String, Object> params) {
        String monitorReportName = params.containsKey("monitorReportName") ? (String) params.get("monitorReportName") : "";
        DtoReport report = repository.findOne(reportId);
        report.setMonitorReportName(monitorReportName);
        report.setMonitorReportConfigId(reportConfigId);
        repository.save(report);
        List<DtoMonitorReport2Property> existList = monitorReport2PropertyRepository.findByReportId(reportId);
        monitorReport2PropertyRepository.delete(existList);
        List<String> propertyIds = params.containsKey("propertyIds") ? (List<String>) params.get("propertyIds") : new ArrayList<>();
        if (StringUtil.isNotEmpty(propertyIds)) {
            List<DtoMonitorReport2Property> waitSaveList = new ArrayList<>();
            for (String propertyId : propertyIds) {
                DtoMonitorReport2Property monitorReport2Property = new DtoMonitorReport2Property();
                monitorReport2Property.setReportId(reportId);
                monitorReport2Property.setPropertyId(propertyId);
                waitSaveList.add(monitorReport2Property);
            }
            monitorReport2PropertyRepository.save(waitSaveList);
        }
    }

    @Override
    public List<Map<String, Object>> getExpressAgeInfo() {
        List<DtoExpressageInfo> expressAgeInfos = expressageInfoRepository.findAll();
        // 删除无效数据
        expressAgeInfos.removeIf(p -> StringUtil.isEmpty(p.getRecipients()) &&
                StringUtil.isEmpty(p.getRecipientsPhone()));
        List<Map<String, Object>> result = new ArrayList<>();
        expressAgeInfos.forEach(p -> {
            Map<String, Object> map = new HashMap<>();
            map.put("recipients", p.getRecipients());
            map.put("recipientsPhone", p.getRecipientsPhone());
            map.put("consigneeAddress", p.getConsigneeAddress());
            result.add(map);
        });
        return result;
    }

    /**
     * 过滤掉没有现场数据的送样单
     *
     * @param recordList 送样单列表
     * @return 过滤后的送样单
     */
    private List<DtoReceiveSampleRecord> filterSampleRecordWithoutField(List<DtoReceiveSampleRecord> recordList) {
        if (StringUtil.isNotEmpty(recordList)) {
            List<String> filterRecordIdList = new ArrayList<>();
            for (DtoReceiveSampleRecord record : recordList) {
                boolean filterFlag = true;
                List<DtoSample> samplesForRecord = sampleRepository.findByReceiveId(record.getId());
                List<String> sampleIdForRecord = samplesForRecord.stream().map(DtoSample::getId).collect(Collectors.toList());
                List<DtoSample> qcSamplesForRecord = StringUtil.isNotEmpty(sampleIdForRecord) ?
                        sampleRepository.findByAssociateSampleIdIn(sampleIdForRecord) : new ArrayList<>();
                if (StringUtil.isNotEmpty(qcSamplesForRecord)) {
                    samplesForRecord.addAll(qcSamplesForRecord);
                }
                sampleIdForRecord = samplesForRecord.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
                List<DtoAnalyseData> analyseDataForRecord = StringUtil.isNotEmpty(sampleIdForRecord) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdForRecord) : new ArrayList<>();
                for (DtoAnalyseData analyseData : analyseDataForRecord) {
                    if (analyseData.getIsCompleteField()) {
                        //有现场数据,不用过滤
                        filterFlag = false;
                        break;
                    }
                }
                if (filterFlag) {
                    filterRecordIdList.add(record.getId());
                }
            }
            if (StringUtil.isNotEmpty(filterRecordIdList)) {
                recordList = recordList.stream().filter(p -> !filterRecordIdList.contains(p.getId())).collect(Collectors.toList());
            }
        }
        return recordList;
    }

    /**
     * 新增项目流程状态退回日志
     *
     * @param ids     报告主键idS
     * @param opinion 意见
     */
    private void createReportStatusBackLog(List<String> ids, String opinion) {
        List<DtoLog> logList = new ArrayList<>();
        for (String id : ids) {
            String comment = "退回了报告";
            if (StringUtils.isNotNullAndEmpty(opinion)) {
                comment += String.format(",意见:%s", opinion);
            }
            comment += "。";

            DtoLog log = new DtoLog();
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.退回报告.toString());
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName("");
            log.setLogType(EnumPRO.EnumLogType.项目报告.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.报告.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            logList.add(log);
        }
        newLogService.createLog(logList, EnumPRO.EnumLogType.项目报告.getValue());
    }

    /**
     * 新增项目流程状态更新日志
     *
     * @param ids
     * @param opinion
     */
    private void createReportStatusUpdateLog(List<String> ids, String opinion, String nextOperatorId, String nextOperatorName, String signal, Map<String, DtoPerson> personMap) {
        List<DtoReport> reportList = repository.findAll(ids);
        List<DtoLog> logList = new ArrayList<>();
        for (String id : ids) {
            DtoReport rep = reportList.stream().filter(p -> p.getId().equals(id)).findFirst().orElse(null);

            DtoLog log = new DtoLog();
            String comment = "";
            String operateInfo = "";
            if (StringUtil.isNotNull(rep)) {
                if ("reviewPass".equals(signal)) {
                    //报告复核提交通过时下一步操作人获取报告对象中的二审人id
                    nextOperatorId = rep.getSecondInstanceId();
                    DtoPerson person = personMap.get(rep.getSecondInstanceId());
                    nextOperatorName = StringUtil.isNotNull(person) ? person.getCName() : "";
                }
                operateInfo = EnumLogOperateType.更新报告状态.toString();
                comment = String.format("更新报告状态为%s", rep.getStatus());
                if (StringUtils.isNotNullAndEmpty(opinion)) {
                    comment += String.format(",意见:%s", opinion);
                }
                if (StringUtils.isNotNullAndEmpty(nextOperatorName)) {
                    comment += String.format(",下一步操作人:%s", nextOperatorName);
                }
                comment += "。";
            } else {
                operateInfo = EnumLogOperateType.删除报告.toString();
                comment = "删除报告。";
            }
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(operateInfo);
            log.setNextOperatorId(StringUtils.isNotNullAndEmpty(nextOperatorId) ? nextOperatorId : UUIDHelper.GUID_EMPTY);
            log.setNextOperatorName(StringUtils.isNotNullAndEmpty(nextOperatorName) ? nextOperatorName : "");
            log.setLogType(EnumPRO.EnumLogType.项目报告.getValue());
            log.setObjectId(id);
            log.setObjectType(EnumPRO.EnumLogObjectType.报告.getValue());
            log.setComment(comment);
            log.setOpinion(opinion);
            log.setRemark("");
            logList.add(log);
        }
        newLogService.createLog(logList, EnumPRO.EnumLogType.项目报告.getValue());
    }

    /**
     * 获取报告排序值
     *
     * @param report 报告
     * @return 报告排序值
     */
    private Integer getCompareValue(DtoReport report) {
        //优先显示退回的、其次显示待处理和流程中的，最后显示已签发的，并且需要按照报告添加时间倒序排列
        if (report.getStatus().equals(EnumReportState.报告未通过.toString())) {
            return BACK_REFUSED;
        } else if (report.getDealStatus().equals(EnumStatus.待处理.getValue())) {
            return TODO;
        } else if (!report.getStatus().equals(EnumReportState.已签发.toString())) {
            return INPROCESS;
        } else if (report.getStatus().equals(EnumReportState.已签发.toString())) {
            return SIGN_ALERADY;
        }
        return UNKNOW_CONDITION;
    }


    @Autowired
    @Lazy
    public void setDocumentFileNameContext(DocumentFileNameContext documentFileNameContext) {
        this.documentFileNameContext = documentFileNameContext;
    }

    @Autowired
    @Lazy
    public void setQualityControlEvaluateService(QualityControlEvaluateService qualityControlEvaluateService) {
        this.qualityControlEvaluateService = qualityControlEvaluateService;
    }

    @Autowired
    public void setProject2ReportRepository(Project2ReportRepository project2ReportRepository) {
        this.project2ReportRepository = project2ReportRepository;
    }

    @Autowired
    @Lazy
    public void setProject2ReportService(Project2ReportService project2ReportService) {
        this.project2ReportService = project2ReportService;
    }

    @Autowired
    public void setReportFolderSortInfoRepository(ReportFolderSortInfoRepository reportFolderSortInfoRepository) {
        this.reportFolderSortInfoRepository = reportFolderSortInfoRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setAnalyzeItemSortRepository(AnalyzeItemSortRepository analyzeItemSortRepository) {
        this.analyzeItemSortRepository = analyzeItemSortRepository;
    }

    @Autowired
    public void setFixedPointSortRepository(FixedPointSortRepository fixedPointSortRepository) {
        this.fixedPointSortRepository = fixedPointSortRepository;
    }

    @Autowired
    public void setReportNumberPoolRepository(ReportNumberPoolRepository reportNumberPoolRepository) {
        this.reportNumberPoolRepository = reportNumberPoolRepository;
    }

    @Autowired
    public void setReportNumberPoolService(ReportNumberPoolService reportNumberPoolService) {
        this.reportNumberPoolService = reportNumberPoolService;
    }

    @Autowired
    public void setSignatureService(SignatureService signatureService) {
        this.signatureService = signatureService;
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    public void setExpressageInfoRepository(ExpressageInfoRepository expressageInfoRepository) {
        this.expressageInfoRepository = expressageInfoRepository;
    }

    @Autowired
    public void setExpressageInfo2ReportRepository(ExpressageInfo2ReportRepository expressageInfo2ReportRepository) {
        this.expressageInfo2ReportRepository = expressageInfo2ReportRepository;
    }

    @Autowired
    public void setProjectContractRepository(ProjectContractRepository projectContractRepository) {
        this.projectContractRepository = projectContractRepository;
    }

    @Autowired
    public void setMonitorReport2PropertyRepository(MonitorReport2PropertyRepository monitorReport2PropertyRepository) {
        this.monitorReport2PropertyRepository = monitorReport2PropertyRepository;
    }

    @Autowired
    public void setReportConfigRepository(ReportConfigRepository reportConfigRepository) {
        this.reportConfigRepository = reportConfigRepository;
    }
}