package com.sinoyd.lims.lim.controller;


import com.sinoyd.base.criteria.*;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.criteria.*;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.lim.service.transform.ExpDocumentManageService;
import com.sinoyd.lims.lim.service.transform.ExpImpPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 导出修改数据服务
 *
 * <AUTHOR>
 * @version V1.0.0 2022/3/23
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/export")
public class ExportController extends ExceptionHandlerController<ImportInstrumentService> {

    @Autowired
    private ExpImpFixedPropertyService expImpFixedPropertyService;
    @Autowired
    private ExpImpEnterpriseService expImpEnterpriseService;
    @Autowired
    private ExpImpConsumableService expImpConsumableService;
    @Autowired
    private ExpImpInstrumentCheckRecordService expImpInstrumentCheckRecordService;
    @Autowired
    private ExpImpStandardService expImpStandardService;
    @Autowired
    private ExpImpPersonService expImpPersonService;
    @Autowired
    private ExpImpPersonAbilityService expImpPersonAbilityService;
    @Autowired
    private ExpImpEvaluationCriteriaService expImpEvaluationCriteriaService;
    @Autowired
    private ExportNewSearchResultService exportNewSearchResultService;
    @Autowired
    private ExpImpInstrumentService expImpInstrumentService;
    @Autowired
    private ExpDocumentManageService expDocumentManageService;

    /**
     * 固定资产导出
     */
    @GetMapping("/fixedProperty")
    public void exportFixedProperty(FixedAssetsCriteria fixedAssetsCriteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "固定资产");
        expImpFixedPropertyService.export(fixedAssetsCriteria, response, sheetNames, "固定资产");
    }

    /**
     * 客户管理导出
     */
    @GetMapping("/enterprise")
    public void exportEnterprise(EnterpriseCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "客户数据");
        expImpEnterpriseService.export(criteria, response, sheetNames, "客户数据");
    }

    /**
     * 标准物质导出
     */
    @GetMapping("/standard")
    public void exportStandard(ConsumableCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "标准物质");
        sheetNames.put("secondName", "标样类型");
        expImpStandardService.export(criteria, response, sheetNames, "标准物质");
    }

    /**
     * 消耗品导出
     */
    @GetMapping("/consumable")
    public void exportConsumable(ConsumableCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "消耗品");
        sheetNames.put("secondName", "消耗品类型");
        expImpConsumableService.export(criteria, response, sheetNames, "消耗品");
    }

    /**
     * 仪器检定校准导出
     */
    @GetMapping("/instrumentCheckRecord")
    public void exportInstrumentCheckRecord(InstrumentCheckRecordCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "仪器检定校准");
        expImpInstrumentCheckRecordService.export(criteria, response, sheetNames, "仪器检定校准");
    }

    /**
     * 人员管理导出
     */
    @GetMapping("/person")
    public void exportPerson(PersonCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "人员管理");
        sheetNames.put("secondName", "关联信息");
        expImpPersonService.export(criteria, response, sheetNames, "人员管理");
    }

    /**
     * 人员检测能力导出
     */
    @GetMapping("/personAbility")
    public void exportPersonAbility(PersonAbilityCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "检测能力");
        expImpPersonAbilityService.export(criteria, response, sheetNames, "");
    }

    /**
     * 评价标准导出
     */
    @GetMapping("/evaluationCriteria")
    public void exportEvaluationCriteria(EvaluationCriteriaCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "评价标准");
        expImpEvaluationCriteriaService.export(criteria, response, sheetNames, "评价标准");
    }

    /**
     * 查新结果导出
     */
    @GetMapping("/newSearchResult")
    public void exportNewSearchResult(NewSearchResultCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "查新结果");
        exportNewSearchResultService.export(criteria, response, sheetNames, "查新结果");
    }

    /**
     * 仪器管理导出
     */
    @GetMapping("/instrument")
    public void exportNewSearchResult(InstrumentCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "仪器管理");
        sheetNames.put("secondName","关联信息");
        expImpInstrumentService.export(criteria, response, sheetNames, "仪器管理");
    }

    /**
     * 文件管理清单
     */
    @GetMapping("/fileManage")
    public void exportNewSearchResult(DocumentCriteria criteria, HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "文件清单");
        expDocumentManageService.export(criteria, response, sheetNames, "文件清单");
    }
}
