package com.sinoyd.lims.pro.service.impl;

import com.aspose.cells.Workbook;
import com.aspose.cells.WorkbookDesigner;
import com.aspose.pdf.CryptoAlgorithm;
import com.aspose.pdf.facades.DocumentPrivilege;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.RelativeHorizontalPosition;
import com.aspose.words.SaveFormat;
import com.aspose.words.WrapType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.BaseLicenseService;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.EnumHelper;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.SerialIdentifierConfigRepository;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.lim.service.ReviseService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.ReportCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.entity.SamplingFrequencyTest;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.MathUtil;
import com.sinoyd.lims.probase.service.impl.ProBaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 检测业务操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/05
 * @since V100R001
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProServiceImpl extends ProBaseServiceImpl implements ProService {

    //#region 注入
    @Autowired
    private CommonRepository comRepository;

    @Autowired
    private CodeService codeService;

    @Autowired
    @Lazy
    private WorkflowService workflowService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectPlanRepository projectPlanRepository;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    @Lazy
    private SamplingFrequencyTestService samplingFrequencyTestService;

    @Autowired
    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private ReportService reportService;

    @Autowired
    private ReportRepository reportRepository;

    @Autowired
    private QualityManageRepository qualityManageRepository;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    @Autowired
    private SubmitRecordRepository submitRecordRepository;

    @Autowired
    private StatusForProjectRepository statusForProjectRepository;

    @Autowired
    private StatusForRecordRepository statusForRecordRepository;

    @Autowired
    @Lazy
    private SubmitRecordService submitRecordService;

    @Autowired
    private Project2WorkSheetFolderRepository project2WorkSheetFolderRepository;

    @Autowired
    private WorkSheetFolderRepository workSheetFolderRepository;

    @Autowired
    @Lazy
    private WorkSheetFolderService workSheetFolderService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private TestRepository testRepository;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private DocumentService documentService;

    @Autowired
    @Lazy
    private AnalyseDataCacheService analyseDataCacheService;

    @Autowired
    @Lazy
    private SchemeCacheService schemeCacheService;

    @Autowired
    @Lazy
    private ProjectCacheService projectCacheService;

    @Autowired
    @Lazy
    private ReceiveSampleRecordCacheService receiveSampleRecordCacheService;

    @Autowired
    @Lazy
    private ReviseService reviseService;

    @Autowired
    private Project2WorkSheetFolderRepository toSheetFolderRepository;

    @Autowired
    private ReportDetailRepository reportDetailRepository;

    private final FilePathConfig filePathConfig;
    /**
     * 踏勘Repository
     */
    private final ExploreRepository exploreRepository;

    private final DocumentRepository documentRepository;

    @Autowired
    private SerialIdentifierConfigRepository serialIdentifierConfigRepository;

    @Autowired
    private BaseLicenseService licenseService;

    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private EvaluationRecordRepository evaluationRecordRepository;

    @Autowired
    @Lazy
    private Project2ReportService project2ReportService;

    @Autowired
    @Lazy
    private SampleJudgeDataService sampleJudgeDataService;

    //#endregion

    //#region 样品指标

    /**
     * 获取指标
     *
     * @param sampleId 样品id
     * @return 指标
     */
    @Override
    public String getAnalyzeItems(String sampleId) {
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdAndIsDeletedFalse(sampleId);
        return this.getAnalyzeItemsByAna(analyseDataList);
    }

    /**
     * 获取指标
     *
     * @param analyseDataList 分析数据
     * @return 指标
     */
    @Override
    public String getAnalyzeItemsByAna(List<DtoAnalyseData> analyseDataList) {
        analyseDataList = analyseDataList.stream().filter(p -> !p.getIsOutsourcing() && !p.getIsDeleted() && !p.getIsSamplingOut()
                && !p.getDataStatus().equals(EnumAnalyseDataStatus.作废.getValue())).sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName)).
                collect(Collectors.toList());
        return String.join(",", analyseDataList.stream().sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName)).map(DtoAnalyseData::getRedAnalyzeItemName).distinct().collect(Collectors.toList()));
    }

    /**
     * 获取指标
     *
     * @param sftList 频次指标
     * @return 指标
     */
    @Override
    public String getAnalyzeItemsByFrequencyTest(List<SamplingFrequencyTest> sftList) {
        sftList = sftList.stream().filter(p -> !p.getIsOutsourcing() && !p.getIsSamplingOut()).sorted(Comparator.comparing(SamplingFrequencyTest::getRedAnalyzeItemName)).
                collect(Collectors.toList());
        return sftList.stream().map(SamplingFrequencyTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining(","));
    }

    /**
     * 获取指标
     *
     * @param testList 测试项目
     * @return 指标
     */
    @Override
    public String getAnalyzeItemsByTest(List<DtoTest> testList) {
        testList = testList.stream().filter(p -> !p.getIsOutsourcing()).sorted(Comparator.comparing(DtoTest::getRedAnalyzeItemName)).
                collect(Collectors.toList());
        return testList.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining(","));
    }
    //endregion

    //#region 样品指标增删

    /**
     * 添加样品的分析数据及频次指标（样品登记添加测试项目）
     *
     * @param sampleId       样品id
     * @param testIds        测试项目id集合
     * @param isAddForFolder 该点位统一添加
     */
    @Transactional
    @Override
    public void addSampleTest(String sampleId, List<String> testIds, Boolean isAddForFolder) {
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        DtoSample dto = sampleRepository.findOne(sampleId);
        List<String> allTestIds = analyseDataRepository.findBySampleIdAndIsDeletedFalse(sampleId).stream()
                .map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        List<DtoSample> samples = new ArrayList<>();
        if (dto.getSampleCategory().equals(EnumSampleCategory.原样.getValue()) && isAddForFolder) {
            samples = sampleRepository.findBySampleFolderId(dto.getSampleFolderId());
            Set<String> receiveIds = samples.stream().map(DtoSample::getReceiveId).filter(receiveId -> !UUIDHelper.GUID_EMPTY.equals(receiveId)).collect(Collectors.toSet());
            List<DtoReceiveSampleRecord> records = receiveIds.size() > 0 ? receiveSampleRecordRepository.findAll(receiveIds) : new ArrayList<>();
            DtoProject project = projectRepository.findOne(dto.getProjectId());
            DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, samples, records, testList, true);
            analyseDataService.addAnalyseData(addDto);
            //添加点位频次指标
            samplingFrequencyTestService.addSampleFolderTest(dto.getSampleFolderId(), testList);
        } else {
            samples.add(dto);
            if (StringUtils.isNotNullAndEmpty(dto.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(dto.getReceiveId())) {
                DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(dto.getReceiveId());
                if (dto.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                    //原样需要添加点位频次指标
                    samplingFrequencyTestService.addSamplingFrequencyTest(dto.getSamplingFrequencyId(), testList);
                }
                DtoProject project = projectRepository.findOne(record.getProjectId());
                DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, dto, record, testList, true);
                analyseDataService.addAnalyseData(addDto);
                if (dto.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                    sampleJudgeDataService.createJudgeDataBySampleTest(Collections.singletonList(dto), testIds, allTestIds, project);
                }
            } else {
                DtoSample ySample = sampleRepository.findOne(dto.getAssociateSampleId());
                //如果ysample的projectId为空，那么还需要再往下再去找
                if (UUIDHelper.GUID_EMPTY.equals(ySample.getProjectId())) {
                    ySample = sampleRepository.findOne(ySample.getAssociateSampleId());
                }
                if (dto.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                    //原样需要添加点位频次指标
                    samplingFrequencyTestService.addSamplingFrequencyTest(dto.getSamplingFrequencyId(), testList);
                }
                DtoProject project = projectRepository.findOne(ySample.getProjectId());
                DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, dto, testList, true);
                analyseDataService.addAnalyseData(addDto);
            }
        }

        List<String> itemNames = testList.stream().sorted(Comparator.comparing(DtoTest::getRedAnalyzeItemName)).map(DtoTest::getRedAnalyzeItemName).collect(Collectors.toList());
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sample : samples) {
            String watchSpot = sample.getRedFolderName().replace(String.format("(%d-%d)", sample.getCycleOrder(), sample.getTimesOrder()), "");
            String comment = String.format("增加了样品%s检测项目:%s。", sampleService.getSampleName(sample, watchSpot), String.join("、", itemNames));
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.增加检测项目.toString());
            log.setLogType(EnumLogType.样品数据.getValue());
            log.setObjectId(sample.getId());
            log.setObjectType(EnumLogObjectType.样品.getValue());
            log.setComment(comment);
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        newLogService.createLog(logList, EnumLogType.样品数据.getValue());
    }

    @Transactional
    @Override
    public List<DtoTest> repeatSampleTest(String sampleId, List<String> testIds, Boolean isAddForFolder) {
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        DtoSample dto = sampleRepository.findOne(sampleId);
        //删除原有的周期频次绑定数据
        if (StringUtil.isNotNull(dto)) {
            samplingFrequencyTestRepository.deleteBySampleFolderId(dto.getSampleFolderId());
        }
        //删除原有样品的分析数据
        if (StringUtil.isNotNull(dto) && isAddForFolder) {
            List<DtoSample> samplesOfFolder = sampleRepository.findBySampleFolderId(dto.getSampleFolderId());
            List<String> sampleIdsOfFolder = samplesOfFolder.parallelStream()
                    .map(DtoSample::getId).collect(Collectors.toList());
            analyseDataRepository.deleteAllBySampleIdIn(sampleIdsOfFolder);
        } else if (!isAddForFolder) {
            analyseDataRepository.deleteAllBySampleId(dto.getId());
        }
        List<DtoSample> samples = new ArrayList<>();
        if (dto.getSampleCategory().equals(EnumSampleCategory.原样.getValue()) && isAddForFolder) {
            samples = sampleRepository.findBySampleFolderId(dto.getSampleFolderId());
            Set<String> receiveIds = samples.stream().map(DtoSample::getReceiveId).filter(receiveId -> !UUIDHelper.GUID_EMPTY.equals(receiveId)).collect(Collectors.toSet());
            List<DtoReceiveSampleRecord> records = receiveIds.size() > 0 ? receiveSampleRecordRepository.findAll(receiveIds) : new ArrayList<>();
            DtoProject project = projectRepository.findOne(dto.getProjectId());
            DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, samples, records, testList, true);
            analyseDataService.addAnalyseData(addDto);
            //添加点位频次指标
            samplingFrequencyTestService.addSampleFolderTest(dto.getSampleFolderId(), testList);
        } else {
            samples.add(dto);
            if (StringUtils.isNotNullAndEmpty(dto.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(dto.getReceiveId())) {
                DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(dto.getReceiveId());
                if (dto.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                    //原样需要添加点位频次指标
                    samplingFrequencyTestService.addSamplingFrequencyTest(dto.getSamplingFrequencyId(), testList);
                }
                DtoProject project = projectRepository.findOne(record.getProjectId());
                DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, dto, record, testList, true);
                analyseDataService.addAnalyseData(addDto);
            } else {
                DtoSample ySample = sampleRepository.findOne(dto.getAssociateSampleId());
                if (dto.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                    //原样需要添加点位频次指标
                    samplingFrequencyTestService.addSamplingFrequencyTest(dto.getSamplingFrequencyId(), testList);
                }
                DtoProject project = projectRepository.findOne(ySample.getProjectId());
                DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, dto, testList, true);
                analyseDataService.addAnalyseData(addDto);
            }
        }

        List<String> itemNames = testList.stream().sorted(Comparator.comparing(DtoTest::getRedAnalyzeItemName)).map(DtoTest::getRedAnalyzeItemName).collect(Collectors.toList());
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sample : samples) {
            String watchSpot = sample.getRedFolderName().replace(String.format("(%d-%d)", sample.getCycleOrder(), sample.getTimesOrder()), "");
            String comment = String.format("增加了样品%s检测项目:%s。", sampleService.getSampleName(sample, watchSpot), String.join("、", itemNames));
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.增加检测项目.toString());
            log.setLogType(EnumLogType.样品数据.getValue());
            log.setObjectId(sample.getId());
            log.setObjectType(EnumLogObjectType.样品.getValue());
            log.setComment(comment);
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        newLogService.createLog(logList, EnumLogType.样品数据.getValue());
        return testList;
    }

    /**
     * 删除样品指标
     *
     * @param dtoAnalyseDataDelete 删除结构体
     * @return 变动的样品id
     */
    @Transactional
    @Override
    public List<String> deleteAnalyseDataBySample(DtoAnalyseDataDelete dtoAnalyseDataDelete) {
        if (dtoAnalyseDataDelete.getAllSamples().size() > 0) {
            List<String> sampleIds = this.updateReportSample(dtoAnalyseDataDelete.getAllSamples().stream().map(DtoSample::getId).collect(Collectors.toList()));
            analyseDataService.deleteAnalyseData(dtoAnalyseDataDelete);
            List<String> samplingFrequencyIds = dtoAnalyseDataDelete.getSample().stream().map(DtoSample::getSamplingFrequencyId).collect(Collectors.toList());
            if (samplingFrequencyIds.size() > 0) {
                List<DtoSamplingFrequencyTest> sftList = samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(samplingFrequencyIds);
                sftList = sftList.stream().filter(p -> dtoAnalyseDataDelete.getTestIds().contains(p.getTestId()) || dtoAnalyseDataDelete.getAnalyseItemIds().contains(p.getAnalyseItemId())).collect(Collectors.toList());
                samplingFrequencyTestRepository.delete(sftList);
            }
            if (sampleIds.size() > 0) {
                //保证事务提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                sampleRepository.updateDataChangeStatus(sampleIds, EnumPRO.EnumSampleChangeStatus.已变更.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                            }
                        }
                );
            }
            return sampleIds;
        }
        return new ArrayList<>();
    }

    /**
     * 删除样品指标
     *
     * @param sampleId 样品id
     * @param testIds  测试项目id
     */
    @Transactional
    @Override
    public void deleteAnalyseData(String sampleId, List<String> testIds) {
        DtoSample sample = sampleRepository.findOne(sampleId);
        List<String> sampleIds = this.updateReportSample(Collections.singletonList(sampleId));

        DtoAnalyseDataDelete dtoAnalyseDataDelete = new DtoAnalyseDataDelete(sample.getProjectId(), Collections.singletonList(sample), testIds, new ArrayList<>(), true);
        analyseDataService.deleteAnalyseData(dtoAnalyseDataDelete);

        if (!sample.getSamplingFrequencyId().equals(UUIDHelper.GUID_EMPTY)) {
            //删除点位频次指标
            List<DtoSamplingFrequencyTest> sftList = samplingFrequencyTestRepository.findBySamplingFrequencyId(sample.getSamplingFrequencyId());
            List<String> removeIds = sftList.stream().filter(p -> testIds.contains(p.getTestId())).map(DtoSamplingFrequencyTest::getId).collect(Collectors.toList());
            if (removeIds.size() > 0) {
                samplingFrequencyTestRepository.logicDeleteById(removeIds);
            }
        }
        //判断删除的指标的点位下的其他样品中是否还存在此测试项目指标
        String sampleFolderId = sample.getSampleFolderId();
        //获取该点位下所有样品
        if (!UUIDHelper.GUID_EMPTY.equals(sampleFolderId)) {
            List<DtoSample> samplesOfFolder = sampleRepository.findBySampleFolderId(sampleFolderId);
            //获取点位下所有样品的指标
            List<String> sampleIdsOfFolder = samplesOfFolder.stream().map(DtoSample::getId).filter(p -> !sampleId.equals(p)).distinct().collect(Collectors.toList());
            List<DtoAnalyseData> analyseData = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdsOfFolder);
            //获取删除的指标的评价标准数据
            List<DtoEvaluationRecord> evaluationRecords = evaluationRecordRepository.findByObjectIdInAndTestIdIn(Collections.singletonList(sampleFolderId), testIds);
            List<String> deleteEvaRecordIds = new ArrayList<>();
            //判断此测试项目是否为点位下最后一个指标
            for (String testId : testIds) {
                List<DtoAnalyseData> anaDataListOfTest = analyseData.stream().filter(p -> testId.equals(p.getTestId())).collect(Collectors.toList());
                if (StringUtil.isEmpty(anaDataListOfTest)) {
                    deleteEvaRecordIds.addAll(evaluationRecords.stream().filter(p -> testId.equals(p.getTestId())).map(DtoEvaluationRecord::getId).collect(Collectors.toList()));
                }
            }

            if (StringUtil.isNotEmpty(deleteEvaRecordIds)) {
                evaluationRecordRepository.logicDeleteById(deleteEvaRecordIds, new Date());
            }
        }

        List<String> operationSampleIds = new ArrayList<>();
        operationSampleIds.add(sampleId);
        sampleJudgeDataService.deleteJudgeDataByTest(operationSampleIds, testIds, true);

        List<DtoTest> testList = testService.findRedisByIds(testIds);
        String testNames = testList.stream().map(DtoTest::getRedAnalyzeItemName).collect(Collectors.joining("、"));
        newLogService.createLog(sample.getId(), String.format("删除了样品%s检测项目:%s", sampleService.getSampleName(sample, ""), testNames), "",
                EnumLogType.样品数据.getValue(), EnumLogObjectType.样品.getValue(), EnumLogOperateType.删除检测项目.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        if (sampleIds.size() > 0) {
            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            sampleRepository.updateDataChangeStatus(sampleIds, EnumPRO.EnumSampleChangeStatus.已变更.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                        }
                    }
            );
        }
    }

    @Transactional
    @Override
    public List<String> updateReportSample(List<String> sampleIds) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoReportSample(");
        stringBuilder.append("s.id,r.id,r.projectId,r.reportNum,s.dataChangeStatus,r.dataChangeStatus)");
        stringBuilder.append(" from DtoSample s,DtoReportDetail rd,DtoReport r where 1=1");
        stringBuilder.append(" and s.id = rd.objectId");
        stringBuilder.append(" and rd.reportId = r.id");
        stringBuilder.append(" and r.isDeleted = 0 ");
        stringBuilder.append(" and s.id in :sampleIds");
        Map<String, Object> values = new HashMap<>();
        values.put("sampleIds", sampleIds);
        List<DtoReportSample> reportSamples = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleIds)) {
            reportSamples = comRepository.find(stringBuilder.toString(), values);
        }
        sampleIds = reportSamples.parallelStream().map(DtoReportSample::getId).collect(Collectors.toList());
        List<String> reportIds = reportSamples.parallelStream().filter(p -> p.getReportChangeStatus().equals(EnumPRO.EnumReportChangeStatus.未变更.getValue())).map(DtoReportSample::getReportId).collect(Collectors.toList());
        if (reportIds.size() > 0) {
            reportRepository.updateDataChangeStatus(reportIds, EnumPRO.EnumReportChangeStatus.已变更.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
        }
        if (reportSamples.size() > 0) {
            DtoProject project = projectRepository.findOne(reportSamples.get(0).getProjectId());
            DtoProjectJson projectJson = JsonIterator.deserialize(project.getJson(), DtoProjectJson.class);
            projectJson.setDataChangeStatus(EnumProjectChangeStatus.已变更.getValue());
            //写入json信息
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                projectRepository.updateJson(reportSamples.get(0).getProjectId(), objectMapper.writeValueAsString(projectJson));
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
            }
        }
        return sampleIds;
    }


    //#endregion

    //#region 删除项目

    /**
     * 项目删除
     *
     * @param projectId 项目id
     */
    @Transactional
    @Override
    public void deleteProject(String projectId) {
        sampleFolderRepository.deleteByProjectId(projectId, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        statusForProjectRepository.deleteByProjectId(projectId);
        List<String> projectIds = Collections.singletonList(projectId);
        this.deleteProjectSamples(projectIds);
    }

    /**
     * 项目删除
     *
     * @param projectIds 项目id集合
     */
    @Transactional
    @Override
    public void deleteProjects(List<String> projectIds) {
        //删除项目下的点位
        sampleFolderRepository.deleteByProjectIdIn(projectIds, PrincipalContextUser.getPrincipal().getUserId(), new Date());
        statusForProjectRepository.deleteByProjectIdIn(projectIds);
        this.deleteProjectSamples(projectIds);
    }

    /**
     * 删除项目下的样品
     *
     * @param projectIds 项目id
     */
    @Transactional
    public void deleteProjectSamples(List<String> projectIds) {
        if (projectIds.size() > 0) {
            List<DtoSample> sampleList = sampleRepository.findByProjectIdIn(projectIds);
            if (sampleList.size() > 0) {
                List<String> receiveIdList = sampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())).map(DtoSample::getReceiveId).collect(Collectors.toList());
                if (receiveIdList.size() > 0) {
                    //删除送样单、领样单
                    receiveSampleRecordRepository.logicDeleteById(receiveIdList, new Date());
                    receiveSubSampleRecordRepository.deleteByReceiveIdIn(receiveIdList, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                }

                List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                List<DtoSample> associateSamples = sampleRepository.findByAssociateSampleIdIn(sampleIdList);
                sampleIdList.addAll(associateSamples.stream().map(DtoSample::getId).collect(Collectors.toList()));
                //删除样品
                sampleRepository.deleteSample(sampleIdList, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                this.deleteProjectAnalyseDatas(sampleIdList);
                this.deleteProjectWorkSheet(projectIds);
            }
        }
    }

    /**
     * 删除项目关联工作单
     * w
     *
     * @param projectIds 项目id
     */
    @Transactional
    public void deleteProjectWorkSheet(List<String> projectIds) {
        List<DtoProject2WorkSheetFolder> toFolderData = toSheetFolderRepository.findByProjectIdIn(projectIds);
        //查询所有项目对应的检测单数据
        List<String> workSheetFolderIds = toFolderData.stream().map(DtoProject2WorkSheetFolder::getWorkSheetFolderId).collect(Collectors.toList());
        //查询关联的工作单id下的监测数据
        List<DtoAnalyseData> anaDataList = StringUtil.isNotEmpty(workSheetFolderIds)
                ? analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workSheetFolderIds)
                : new ArrayList<>();
        Map<String, List<DtoAnalyseData>> anaOfFolderMap = anaDataList.stream()
                .collect(Collectors.groupingBy(DtoAnalyseData::getWorkSheetFolderId));
        //需要删除的工作单id
        List<String> deleteIds = new ArrayList<>();
        this.findDeleteWorkSheetIds(toFolderData, anaOfFolderMap, deleteIds);
        if (StringUtil.isNotEmpty(deleteIds)) {
            workSheetFolderService.delete(deleteIds);
        }

    }

    /**
     * 删除样品下的数据
     *
     * @param sampleIds 样品id
     */
    @Transactional
    public void deleteProjectAnalyseDatas(List<String> sampleIds) {
        if (sampleIds.size() > 0) {
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
            analyseDataRepository.deleteAnalyseData(sampleIds, PrincipalContextUser.getPrincipal().getUserId(), new Date());

            if (analyseDataList.stream().anyMatch(DtoAnalyseData::getIsQm)) {
                qualityManageRepository.deleteByAnaIdIn(analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList()));
            }
            List<String> workSheetFolderIds = analyseDataList.stream().filter(p -> UUIDHelper.GUID_EMPTY.equals(p.getWorkSheetFolderId())).
                    map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());

            List<String> personIds = new ArrayList<>();
            List<DtoWorkSheetFolder> folders = workSheetFolderIds.size() > 0 ? workSheetFolderRepository.findAll(workSheetFolderIds) : new ArrayList<>();
            personIds.addAll(folders.stream().map(DtoWorkSheetFolder::getCheckerId).collect(Collectors.toList()));
            personIds.addAll(folders.stream().map(DtoWorkSheetFolder::getAuditorId).collect(Collectors.toList()));
            this.sendProMessageWoTransactional(EnumProAction.修改检测单, "", "", analyseDataList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()),
                    personIds.stream().distinct().collect(Collectors.toList()), workSheetFolderIds);
        }
    }

    //#endregion

    //#region 办结项目

    /**
     * 项目办结
     *
     * @param projectIds 项目idlist
     * @param opinion    办结意见
     */
    @Transactional
    @Override
    public void finishProject(List<String> projectIds, String opinion) {
        this.finishSample(projectIds);
        List<DtoProject> projectList = projectRepository.findAll(projectIds);
        List<DtoSubmitRecord> submitRecords = new ArrayList<>();
        for (DtoProject project : projectList) {
            DtoSubmitRecord submitRecord = new DtoSubmitRecord();
            submitRecord.setObjectId(project.getId());
            submitRecord.setObjectType(EnumSubmitObjectType.项目.getValue());
            submitRecord.setSubmitType(0);
            submitRecord.setSubmitPersonId(PrincipalContextUser.getPrincipal().getUserId());
            submitRecord.setSubmitPersonName(PrincipalContextUser.getPrincipal().getUserName());
            submitRecord.setSubmitTime(new Date());
            submitRecord.setNextPerson("");
            submitRecord.setSubmitRemark(opinion);
            submitRecord.setStateFrom(project.getStatus());
            submitRecord.setStateTo(EnumProjectStatus.已办结.toString());
            submitRecords.add(submitRecord);
        }
        statusForProjectRepository.finishStatusForProject(projectIds, EnumStatus.已处理.getValue(), PrincipalContextUser.getPrincipal().getUserId(), opinion, new Date());
        projectRepository.finishProject(projectIds, EnumSampledStatus.已采毕.getValue(), EnumProjectStatus.已办结.toString(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
        //利用通知的方式，告知项目需刷新样品json信息
        for (String projectId : projectIds) {
            workflowService.endInstance(projectId, opinion);
            projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
        }

        submitRecordService.createSubmitRecords(submitRecords,
                PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getUserName(),
                PrincipalContextUser.getPrincipal().getOrgId());
        newLogService.createFinishProjectLogs(projectIds, opinion, UUIDHelper.GUID_EMPTY, "");
    }

    /**
     * 办结项目下的样品
     *
     * @param projectIds 项目id
     */
    @Transactional
    public void finishSample(List<String> projectIds) {
        if (projectIds.size() > 0) {
            List<DtoSample> sampleList = sampleRepository.findByProjectIdIn(projectIds);
            if (sampleList.size() > 0) {
                List<String> receiveIdList = sampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())).map(DtoSample::getReceiveId).collect(Collectors.toList());
                if (receiveIdList.size() > 0) {
                    this.finishRecord(receiveIdList);
                }

                List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                List<DtoSample> associateSamples = sampleRepository.findByAssociateSampleIdIn(sampleIdList);
                sampleIdList.addAll(associateSamples.stream().map(DtoSample::getId).collect(Collectors.toList()));
                sampleRepository.finishSample(sampleIdList, EnumSampleStatus.样品检毕.toString(), EnumSamplingStatus.已经完成取样.getValue(),
                        EnumInnerReceiveStatus.已经领取.getValue(), EnumAnalyzeStatus.分析完成.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                this.finishAnalyseData(sampleIdList);
            } else {
                List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordRepository.findByProjectIdIn(projectIds);
                if (StringUtil.isNotEmpty(receiveSampleRecords)) {
                    this.finishRecord(receiveSampleRecords.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList()));
                }
            }
        }
    }

    /**
     * 办结样品下的数据
     *
     * @param sampleIds 样品id
     */
    @Transactional
    public void finishAnalyseData(List<String> sampleIds) {
        if (sampleIds.size() > 0) {
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
            analyseDataRepository.finishAnalyseData(sampleIds, EnumAnalyseDataStatus.已确认.toString(), EnumAnalyseDataStatus.已确认.getValue(),
                    PrincipalContextUser.getPrincipal().getUserId(), new Date());
            comRepository.clear();
            List<String> workSheetFolderIds = analyseDataList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getWorkSheetFolderId())).
                    map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
            List<String> personIds = new ArrayList<>();
            List<DtoWorkSheetFolder> folders = workSheetFolderIds.size() > 0 ? workSheetFolderRepository.findAll(workSheetFolderIds) : new ArrayList<>();
            personIds.addAll(folders.stream().map(DtoWorkSheetFolder::getCheckerId).collect(Collectors.toList()));
            personIds.addAll(folders.stream().map(DtoWorkSheetFolder::getAuditorId).collect(Collectors.toList()));

            workSheetFolderService.checkWorkSheetFolder(workSheetFolderIds);

            //遍历所有检测单，如果检测单中没有原样，则将该检测单置为已通过
            List<DtoWorkSheetFolder> uptFolderList = new ArrayList<>();
            if (StringUtil.isNotEmpty(workSheetFolderIds)) {
                List<DtoAnalyseData> allAnalyseData = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workSheetFolderIds);
                List<String> allSampleIdList = allAnalyseData.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<DtoSample> allSampleList = sampleRepository.findByIdInAndIsDeletedFalse(allSampleIdList);
                Map<String, DtoSample> allSampleMap = allSampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
                for (DtoWorkSheetFolder folder : folders) {
                    //找到当前检测单下的未确认的分析数据
                    List<DtoAnalyseData> anaDataList = allAnalyseData.stream().filter(p -> folder.getId().equals(p.getWorkSheetFolderId())
                            && !EnumAnalyseDataStatus.已确认.getValue().equals(p.getDataStatus())).collect(Collectors.toList());
                    if (StringUtil.isEmpty(anaDataList)) {
                        //分析数据都已经确认, 则需要将该检测单置为已通过
                        uptFolderList.add(folder);
                    } else {
                        //判断未确认的分析数据中是否有原样
                        List<String> unCheckSampleIdList = anaDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                        boolean hasYy = false;
                        for (String sampleId : unCheckSampleIdList) {
                            if (allSampleMap.containsKey(sampleId) && EnumSampleCategory.原样.getValue().equals(allSampleMap.get(sampleId).getSampleCategory())) {
                                hasYy = true;
                                break;
                            }
                        }
                        if (!hasYy) {
                            //没有原样则将该检测单置为已通过
                            uptFolderList.add(folder);
                        }
                    }
                }
                for (DtoWorkSheetFolder folder : uptFolderList) {
                    workSheetFolderRepository.updateStatus(Collections.singletonList(folder.getId()), EnumPRO.EnumWorkSheetStatus.审核通过.toString(), EnumPRO.EnumWorkSheetStatus.审核通过.getValue(),
                            PrincipalContextUser.getPrincipal().getUserId(), new Date(), "", folder.getAnalyzeTime());
                    workflowService.endInstance(folder.getId(), "");
                    newLogService.createLog(folder.getId(), "检测单下的分析数据均已审核通过，检测单置为审核通过。", "",
                            EnumPRO.EnumLogType.检测单流程.getValue(), EnumPRO.EnumLogObjectType.检测单.getValue(), EnumPRO.EnumLogOperateType.修改检测单.toString(),
                            PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
                }
            }

            //项目办结时删除对应的检测单 -- 去掉
            //delFolderForFinishProject(sampleIds);

            sendProMessageWoTransactional(EnumProAction.修改检测单, "", "", analyseDataList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()),
                    personIds.stream().distinct().collect(Collectors.toList()));
        }
    }


    /**
     * 过滤出需要删除的工作单id
     *
     * @param foldersOfProject 根据项目分组的工作单数据
     * @param anaOfFolderMap   根据工作单分组的监测数据
     * @param deleteIds        需要删除的工作单id集合
     */
    private void findDeleteWorkSheetIds(List<DtoProject2WorkSheetFolder> foldersOfProject,
                                        Map<String, List<DtoAnalyseData>> anaOfFolderMap,
                                        List<String> deleteIds) {
        //获取此项目对于的所有检测单Id
        List<String> folderIdsOfProject = foldersOfProject.stream()
                .map(DtoProject2WorkSheetFolder::getWorkSheetFolderId)
                .collect(Collectors.toList());
        for (String folderId : folderIdsOfProject) {
            //获取项目检测数据删除后的工作单数据
            List<DtoAnalyseData> anaDataOfSheetFolder = anaOfFolderMap.get(folderId);
            if (StringUtil.isNotEmpty(anaDataOfSheetFolder)) {
                //获取工作单下不是室内质控的数据
                List<DtoAnalyseData> anaNotQc = anaDataOfSheetFolder.stream()
                        .filter(p -> !EnumLIM.EnumQCGrade.内部质控.getValue().equals(p.getQcGrade()))
                        .collect(Collectors.toList());
                //判断删除后的工作单中是否存在其他项目的数据
                if (StringUtil.isEmpty(anaNotQc)) {
                    deleteIds.add(folderId);
                }
            } else {
                deleteIds.add(folderId);
            }
        }
    }

    /**
     * 项目办结时删除对应的检测单(检测单对应多个项目时，需判断项目是否都已办结)
     *
     * @param sampleIds 样品ID列表
     */
    private void delFolderForFinishProject(List<String> sampleIds) {
        List<DtoSample> sampleList = sampleRepository.findByIdInAndIsDeletedFalse(sampleIds);
        List<String> projectIdList = sampleList.stream().map(DtoSample::getProjectId).distinct().collect(Collectors.toList());
        projectIdList = projectIdList.stream().filter(p -> !"".equals(p) && !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
        List<DtoProject2WorkSheetFolder> project2WorkSheetFolderList = StringUtil.isNotEmpty(projectIdList)
                ? project2WorkSheetFolderRepository.findByProjectIdIn(projectIdList) : new ArrayList<>();
        List<String> folderIdForProList = project2WorkSheetFolderList.stream().map(DtoProject2WorkSheetFolder::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        List<DtoProject2WorkSheetFolder> allProject2FolderList = StringUtil.isNotEmpty(folderIdForProList)
                ? project2WorkSheetFolderRepository.findByWorkSheetFolderIdIn(folderIdForProList) : new ArrayList<>();
        List<String> allProjectIdList = allProject2FolderList.stream().map(DtoProject2WorkSheetFolder::getProjectId).distinct().collect(Collectors.toList());
        List<DtoProject> allProjectList = StringUtil.isNotEmpty(allProjectIdList) ? projectRepository.findAll(allProjectIdList) : new ArrayList<>();
        Map<String, DtoProject> projectMap = allProjectList.stream().collect(Collectors.toMap(DtoProject::getId, dto -> dto));
        Map<String, List<String>> folderId2ProListMap = new HashMap<>();
        for (DtoProject2WorkSheetFolder projectFolder : allProject2FolderList) {
            if (!folderId2ProListMap.containsKey(projectFolder.getWorkSheetFolderId())) {
                folderId2ProListMap.put(projectFolder.getWorkSheetFolderId(), new ArrayList<>());
            }
            folderId2ProListMap.get(projectFolder.getWorkSheetFolderId()).add(projectFolder.getProjectId());
        }

        //要删除的检测单
        Set<String> folderIdForDel = new HashSet<>();
        for (String projectId : projectIdList) {
            List<DtoProject2WorkSheetFolder> project2WorkSheetFolderForPro = project2WorkSheetFolderList.stream()
                    .filter(p -> projectId.equals(p.getProjectId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(project2WorkSheetFolderForPro)) {
                //当前项目下的检测单id
                List<String> folderIdForProject = project2WorkSheetFolderForPro.stream().map(DtoProject2WorkSheetFolder::getWorkSheetFolderId).distinct().collect(Collectors.toList());
                //遍历项目下的每个检测单
                for (String folderId : folderIdForProject) {
                    List<String> proIdList = folderId2ProListMap.get(folderId);
                    if (StringUtil.isNotEmpty(proIdList)) {
                        //过滤掉当前遍历的项目
                        List<String> fltProIdList = proIdList.stream().filter(p -> !projectId.equals(p)).collect(Collectors.toList());
                        if (StringUtil.isEmpty(fltProIdList)) {
                            folderIdForDel.add(folderId);
                        } else {
                            boolean canDel = true;
                            for (String proId : fltProIdList) {
                                DtoProject project = projectMap.get(proId);
                                if (StringUtil.isNotNull(project) && !EnumProjectStatus.已办结.getValue().equals(project.getStatus())) {
                                    canDel = false;
                                    break;
                                }
                            }
                            if (canDel) {
                                folderIdForDel.add(folderId);
                            }
                        }
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(folderIdForDel)) {
            workSheetFolderService.delete(new ArrayList<>(folderIdForDel));
        }
    }

    /**
     * 办结送样单
     *
     * @param receiveIds 送样单id
     */
    @Transactional
    public void finishRecord(List<String> receiveIds) {
        if (receiveIds.size() > 0) {
            statusForRecordRepository.finishStatusForRecord(receiveIds, EnumStatus.已处理.getValue());
            List<DtoReceiveSampleRecord> recordList = receiveSampleRecordRepository.findAll(receiveIds);
            if (recordList.size() > 0) {
                receiveSampleRecordRepository.finishRecord(receiveIds, EnumLIM.EnumReceiveRecordStatus.已数据确认.getValue(),
                        EnumLIM.EnumReceiveRecordStatus.已数据确认.toString(), EnumReceiveInfoStatus.已确认.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());

                List<DtoReceiveSubSampleRecord> subRecordList = receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIds);
                if (subRecordList.size() > 0) {
                    this.finishSubRecord(
                            subRecordList.stream().filter(p -> (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0).map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList()),
                            EnumSubRecordType.分析.getValue());

                    this.finishSubRecord(
                            subRecordList.stream().filter(p -> (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0).map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList()),
                            EnumSubRecordType.现场.getValue());
                }
            }
        }
    }

    /**
     * 办结领样单
     *
     * @param subIds 领样单id
     * @param type   领样单类型
     */
    @Transactional
    public void finishSubRecord(List<String> subIds, String type) {
        if (subIds.size() > 0) {
            //analyseData上的数据均未出证，所以这边仅置为可确认状态
            Integer[] anaValues = {EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue(), EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue(),
                    EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue()};

            Integer[] localValues = {EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue(), EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue(),
                    EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue()};

            Integer subStatus = type.equals(EnumSubRecordType.现场.getValue()) ? EnumHelper.GetIntByArray(localValues) : EnumHelper.GetIntByArray(anaValues);
            receiveSubSampleRecordRepository.finishSubRecord(subIds, subStatus, EnumReceiveSubRecordStatusName.已经确认.toString());
        }
    }

    //#endregion

    //#region 项目进度

    /**
     * 获取项目的进度详情
     *
     * @param projectId 项目id
     */
    @Override
    public DtoProjectInquiry findProjectInquiry(String projectId) {
        DtoProject project = projectRepository.findOne(projectId);
        DtoProjectPlan projectPlan = projectPlanRepository.findByProjectId(projectId);
        String projectRegisterPage = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        List<String> modules = this.getProjectInquiryModule(projectPlan, projectRegisterPage);
        EnumProjectStatus projectStatus = EnumProjectStatus.getByName(project.getStatus());
        DtoProjectInquiry inquiry = new DtoProjectInquiry();
        inquiry.setProjectName(project.getProjectName());
        inquiry.setProjectCode(project.getProjectCode());
        if (modules.contains(EnumProjectInquiryModule.项目状态.getValue())) {
            this.setProjectStatus(inquiry, project, projectStatus, projectRegisterPage);
        }
        if (modules.contains(EnumProjectInquiryModule.采样情况.getValue())) {
            this.setSamplingInfo(inquiry, project);
        }
        if (modules.contains(EnumProjectInquiryModule.现场任务.getValue())) {
            this.setLocalTask(inquiry, project, projectStatus, projectRegisterPage);
        }
        if (modules.contains(EnumProjectInquiryModule.样品分配.getValue())) {
            this.setSampleAssign(inquiry, project, projectStatus, projectRegisterPage);
        }
        if (modules.contains(EnumProjectInquiryModule.检测情况.getValue())) {
            this.setAnalyzeInfo(inquiry, project, projectStatus, projectRegisterPage);
        }
        if (modules.contains(EnumProjectInquiryModule.报告.getValue())) {
            this.setReport(inquiry, project, projectStatus);
        }
        return inquiry;
    }

    public List<String> getProjectInquiryModule(DtoProjectPlan projectPlan, String projectRegisterPage) {
        List<String> modules = Arrays.stream(EnumProjectInquiryModule.values()).map(EnumProjectInquiryModule::getValue).collect(Collectors.toList());
        //非委托类或未编制方案，去除采样情况模块
        if (!projectPlan.getIsMakePlan() || !projectRegisterPage.equals(EnumProjectType.委托类.getValue()) || !EnumProjectType.例行类.getValue().equals(projectRegisterPage) || !EnumProjectType.全流程.getValue().equals(projectRegisterPage)) {
            modules.remove(EnumProjectInquiryModule.采样情况.getValue());
        }
        //外部送样类去除现场任务模块
        if (projectRegisterPage.equals(EnumProjectType.送样类.getValue())) {
            modules.remove(EnumProjectInquiryModule.现场任务.getValue());
        }
        return modules;
    }

    private void setProjectStatus(DtoProjectInquiry inquiry, DtoProject project, EnumProjectStatus projectStatus, String projectRegisterPage) {
        List<DtoSubmitRecord> submitRecordList = submitRecordRepository.findByObjectTypeAndObjectId(EnumSubmitObjectType.项目.getValue(), project.getId());
        List<String> statusList = Arrays.stream(EnumProjectInquiryStatus.values()).map(EnumProjectInquiryStatus::getValue).collect(Collectors.toList());
        if (projectRegisterPage.equals(EnumProjectType.现场类.getValue())) {
            statusList.remove(EnumProjectInquiryStatus.审核下达.getValue());
        }
        List<String> doingList = new ArrayList<>();
        String doingStatus = "";
        if (StringUtil.isNotNull(projectStatus)) {
            doingList.add(EnumProjectInquiryStatus.项目登记.getValue());
            doingStatus = EnumProjectInquiryStatus.项目登记.getValue();
            if (projectStatus.getValue() >= EnumProjectStatus.技术审核中.getValue()) {
                doingList.add(EnumProjectInquiryStatus.审核下达.getValue());
                doingStatus = EnumProjectInquiryStatus.审核下达.getValue();
            }
            if (projectStatus.getValue() >= EnumProjectStatus.开展中.getValue()) {
                doingList.add(EnumProjectInquiryStatus.开展中.getValue());
                doingStatus = EnumProjectInquiryStatus.开展中.getValue();
            }
            if (projectStatus.getValue() >= EnumProjectStatus.已办结.getValue()) {
                doingList.add(EnumProjectInquiryStatus.已办结.getValue());
                doingStatus = "";
            }
        }
        List<Map<String, Object>> projectStatusList = new ArrayList<>();
        for (String status : statusList) {
            Map<String, Object> map = new HashMap<>();
            map.put("statusName", status);
            map.put("opinion", "");
            if (doingList.contains(status)) {
                Integer statusInt = doingStatus.endsWith(status) ? EnumStatus.待处理.getValue() : EnumStatus.已处理.getValue();
                map.put("statusInteger", statusInt);
                if (statusInt.equals(EnumStatus.已处理.getValue())) {
                    String opinion = this.getProjectInquiryOpinion(submitRecordList, status);
                    map.put("opinion", opinion);
                }
            } else {
                map.put("statusInteger", null);
            }
            projectStatusList.add(map);
        }
        inquiry.setProjectStatus(projectStatusList);
    }

    private String getProjectInquiryOpinion(List<DtoSubmitRecord> submitRecordList, String projectInquiry) {
        EnumProjectInquiryStatus projectInquiryStatus = EnumProjectInquiryStatus.getByValue(projectInquiry);
        DtoSubmitRecord status;
        if (StringUtil.isNotNull(projectInquiryStatus)) {
            switch (projectInquiryStatus) {
                case 项目登记:
                    status = submitRecordList.stream().sorted(Comparator.comparing(DtoSubmitRecord::getSubmitTime).reversed()).filter(p -> p.getStateFrom().equals(EnumProjectStatus.项目登记中.toString())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(status)) {
                        return String.format("%s于<br>%s提交了项目<br>项目状态更新为：<span style=\"color: blue\">%s</span><br>下一步操作人：%s<br>意见：%s",
                                status.getSubmitPersonName(),
                                DateUtil.dateToString(status.getSubmitTime(), DateUtil.FULL),
                                status.getStateTo(),
                                StringUtils.isNotNullAndEmpty(status.getNextPerson()) ? status.getNextPerson() : "",
                                StringUtils.isNotNullAndEmpty(status.getSubmitRemark()) ? status.getSubmitRemark() : "");
                    }
                    return "";

                case 审核下达:
                    status = submitRecordList.stream().sorted(Comparator.comparing(DtoSubmitRecord::getSubmitTime).reversed()).filter(p -> p.getStateFrom().equals(EnumProjectStatus.技术审核中.toString()) ||
                            p.getStateFrom().equals(EnumProjectStatus.项目下达中.toString())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(status)) {
                        return String.format("%s于<br>%s提交了项目<br>项目状态更新为：<span style=\"color: blue\">%s</span><br>下一步操作人：%s<br>意见：%s",
                                status.getSubmitPersonName(),
                                DateUtil.dateToString(status.getSubmitTime(), DateUtil.FULL),
                                status.getStateTo(),
                                StringUtils.isNotNullAndEmpty(status.getNextPerson()) ? status.getNextPerson() : "",
                                StringUtils.isNotNullAndEmpty(status.getSubmitRemark()) ? status.getSubmitRemark() : "");
                    }
                    return "";
                case 已办结:
                    status = submitRecordList.stream().sorted(Comparator.comparing(DtoSubmitRecord::getSubmitTime).reversed()).filter(p -> p.getStateTo().equals(EnumProjectStatus.已办结.toString())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(status)) {
                        return String.format("%s于<br>%s办结项目<br>意见：%s", status.getSubmitPersonName(),
                                DateUtil.dateToString(status.getModifyDate(), DateUtil.FULL),
                                StringUtils.isNotNullAndEmpty(status.getSubmitRemark()) ? status.getSubmitRemark() : "");
                    }
                    return "";
            }
        }
        return "";
    }

    private void setSamplingInfo(DtoProjectInquiry inquiry, DtoProject project) {
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoProjectInquirySamplingInfoDetail> pb = new PageBean<>();
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoProjectInquirySamplingInfoDetail(");
        stringBuilder.append("st.parentId,s.sampleTypeId,st.typeName,s.sampleFolderId,sf.watchSpot,s.samplingStatus)");
        pb.setEntityName("DtoSample s,DtoSampleFolder sf,DtoSampleType st");
        pb.setSelect(stringBuilder.toString());
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.addCondition(" and s.isDeleted = 0");
        pb.addCondition(" and sf.isDeleted = 0");
        pb.addCondition(" and s.projectId = :projectId");
        values.put("projectId", project.getId());
        pb.addCondition(" and s.sampleFolderId = sf.id");
        pb.addCondition(" and s.sampleTypeId = st.id");
        comRepository.findByPage(pb, values);

        List<DtoProjectInquirySamplingInfo> infoList = new ArrayList<>();
        List<DtoProjectInquirySamplingInfoDetail> samplingInfos = pb.getData().stream().filter(p -> p.getNotSampled() > 0).collect(Collectors.toList());
        samplingInfos.stream().collect(Collectors.groupingBy(DtoProjectInquirySamplingInfoDetail::getBigSampleTypeId, Collectors.toList())).forEach((bigSampleTypeId, list) -> {
            DtoSampleType sampleType = sampleTypeService.findOne(bigSampleTypeId);
            DtoProjectInquirySamplingInfo info = new DtoProjectInquirySamplingInfo();
            info.setBigSampleTypeId(bigSampleTypeId);
            info.setBigSampleTypeName(sampleType.getTypeName());
            info.setNotSampled(list.size());
            List<DtoProjectInquirySamplingInfoDetail> detailList = new ArrayList<>();
            list.stream().collect(Collectors.groupingBy(DtoProjectInquirySamplingInfoDetail::getSampleFolderId, Collectors.toList())).forEach((sampleFolderId, folderList) -> {
                DtoProjectInquirySamplingInfoDetail detail = new DtoProjectInquirySamplingInfoDetail(bigSampleTypeId,
                        folderList.get(0).getSampleTypeId(),
                        folderList.get(0).getSampleTypeName(),
                        sampleFolderId,
                        folderList.get(0).getWatchSpot(),
                        folderList.get(0).getSamplingStatus());
                detail.setNotSampled(folderList.size());
                detailList.add(detail);
            });
            info.setDetail(detailList);
            infoList.add(info);
        });
        inquiry.setSamplingInfo(infoList);
    }

    private void setLocalTask(DtoProjectInquiry inquiry, DtoProject project, EnumProjectStatus projectStatus, String projectRegisterPage) {
        Integer projectStatusInt = StringUtil.isNotNull(projectStatus) ? projectStatus.getValue() : 0;
        List<DtoProjectInquiryRecord> recordList = new ArrayList<>();
        if (projectStatusInt >= EnumProjectStatus.开展中.getValue() || projectRegisterPage.equals(EnumProjectType.现场类.getValue())) {
            List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordRepository.findByInfoStatusNotAndProjectId(EnumReceiveInfoStatus.已确认.getValue(), project.getId());

            Map<String, List<String>> sampleTypeMap = new HashMap<>();
            Set<String> sampleTypeIdSet = new HashSet<>();
            for (DtoReceiveSampleRecord recRecord : receiveSampleRecords) {
                DtoProjectInquiryRecord record = new DtoProjectInquiryRecord(recRecord.getId(), recRecord.getRecordCode(), recRecord.getSenderId(), recRecord.getSenderName(),
                        recRecord.getSendTime(), recRecord.getJson(), 0, 0);
                record.setStatus(recRecord.getInfoStatus().toString());
                if (StringUtils.isNotNullAndEmpty(record.getSampleTypeIds())) {
                    sampleTypeMap.put(recRecord.getId(), Arrays.stream(record.getSampleTypeIds().split(",")).collect(Collectors.toList()));
                    sampleTypeIdSet.addAll(sampleTypeMap.get(recRecord.getId()));
                }
                recordList.add(record);
            }

            List<DtoSampleType> samTypeList = sampleTypeIdSet.size() > 0 ? sampleTypeService.findRedisByIds(new ArrayList<>(sampleTypeIdSet)) : new ArrayList<>();

            for (DtoProjectInquiryRecord record : recordList) {
                if (sampleTypeMap.containsKey(record.getId())) {
                    List<String> sampleTypes = samTypeList.stream().filter(p -> sampleTypeMap.get(record.getId()).contains(p.getId())).map(DtoSampleType::getTypeName).collect(Collectors.toList());
                    record.setSampleTypeNames(String.join(",", sampleTypes));
                }
            }
        }
        inquiry.setLocalTask(recordList);
    }

    private void setSampleAssign(DtoProjectInquiry inquiry, DtoProject project, EnumProjectStatus projectStatus, String projectRegisterPage) {
        Integer projectStatusInt = StringUtil.isNotNull(projectStatus) ? projectStatus.getValue() : 0;
        List<DtoProjectInquiryRecord> recordList = new ArrayList<>();
        Map<String, List<String>> sampleTypeMap = new HashMap<>();
        Set<String> sampleTypeIdSet = new HashSet<>();
        if (projectStatusInt >= EnumProjectStatus.开展中.getValue()) {
            if (projectRegisterPage.equals(EnumProjectType.委托类.getValue()) || EnumProjectType.例行类.getValue().equals(projectRegisterPage) || EnumProjectType.全流程.getValue().equals(projectRegisterPage)) {//可能有多张单子
                Map<String, Object> values = new HashMap<>();
                PageBean<DtoProjectInquiryRecord> pb = new PageBean<>();
                StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoProjectInquiryRecord(");
                stringBuilder.append("r.id,r.recordCode,r.senderId,r.senderName,r.sendTime,r.json,rs.subStatus,s.status)");
                pb.setEntityName("DtoReceiveSampleRecord r,DtoReceiveSubSampleRecord rs,DtoStatusForRecord s");
                pb.setSelect(stringBuilder.toString());
                pb.setRowsPerPage(Integer.MAX_VALUE);
                pb.addCondition(" and r.projectId = :projectId");
                values.put("projectId", project.getId());
                pb.addCondition(" and r.id = rs.receiveId");
                pb.addCondition(" and r.id = s.receiveId");
                pb.addCondition(" and s.module = :module");
                values.put("module", EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                comRepository.findByPage(pb, values);
                pb.getData().stream().collect(Collectors.groupingBy(DtoProjectInquiryRecord::getId, Collectors.toList())).forEach((receiveId, recList) -> {
                    if (recList.stream().anyMatch(p -> p.getModuleStatus().equals(EnumStatus.待处理.getValue()))) {
                        DtoProjectInquiryRecord rec = recList.get(0);
                        rec.setStatus("交接中");
                        sampleTypeMap.put(receiveId, Arrays.stream(rec.getSampleTypeIds().split(",")).collect(Collectors.toList()));
                        sampleTypeIdSet.addAll(sampleTypeMap.get(receiveId));
                        recordList.add(rec);
                    } else {
                        DtoProjectInquiryRecord rec = recList.stream().filter(p -> (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0
                                && (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue()) == 0).findFirst().orElse(null);
                        if (StringUtil.isNotNull(rec)) {
                            rec.setStatus("分配中");
                            sampleTypeMap.put(receiveId, Arrays.stream(rec.getSampleTypeIds().split(",")).collect(Collectors.toList()));
                            sampleTypeIdSet.addAll(sampleTypeMap.get(receiveId));
                            recordList.add(rec);
                        }
                    }
                });
            } else {//仅可能一张单子
                List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordRepository.findByProjectId(project.getId());
                if (receiveSampleRecords.size() > 0) {
                    DtoReceiveSampleRecord record = receiveSampleRecords.get(0);
                    String status = "";
                    List<DtoStatusForRecord> statusForRecordList = statusForRecordRepository.findByReceiveId(record.getId());
                    if (statusForRecordList.stream().anyMatch(p -> p.getModule().equals(EnumLIM.EnumReceiveRecordModule.样品交接.getValue()))) {//存在样品交接的模块，表明已经流转到样品交接及之后
                        if (statusForRecordList.stream().anyMatch(p -> p.getModule().equals(EnumLIM.EnumReceiveRecordModule.样品交接.getValue())
                                && p.getStatus().equals(EnumStatus.待处理.getValue()))) {
                            //处于样品交接中
                            status = "交接中";
                        } else {
                            List<DtoReceiveSubSampleRecord> subRecordList = receiveSubSampleRecordRepository.findByReceiveId(record.getId());
                            //存在实验室领样单且未领取，则表明在样品分配中
                            if (subRecordList.stream().anyMatch(p -> (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0
                                    && (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue()) == 0)) {
                                status = "分配中";
                            }
                        }
                    }
                    if (StringUtils.isNotNullAndEmpty(status)) {
                        DtoProjectInquiryRecord rec = new DtoProjectInquiryRecord(record.getId(), record.getRecordCode(), record.getSenderId(), record.getSenderName(),
                                record.getSendTime(), record.getJson(), 0, 0);
                        rec.setStatus(status);

                        List<String> sampleTypeIdList = Arrays.stream(rec.getSampleTypeIds().split(",")).collect(Collectors.toList());
                        List<DtoSampleType> samTypeList = sampleTypeIdList.size() > 0 ? sampleTypeService.findRedisByIds(sampleTypeIdList) : new ArrayList<>();
                        List<String> sampleTypes = samTypeList.stream().map(DtoSampleType::getTypeName).collect(Collectors.toList());
                        rec.setSampleTypeNames(String.join(",", sampleTypes));
                        inquiry.setSampleAssign(Collections.singletonList(rec));
                        return;
                    }
                }
            }
        }
        List<DtoSampleType> samTypeList = sampleTypeIdSet.size() > 0 ? sampleTypeService.findRedisByIds(new ArrayList<>(sampleTypeIdSet)) : new ArrayList<>();

        for (DtoProjectInquiryRecord record : recordList) {
            if (sampleTypeMap.containsKey(record.getId())) {
                List<String> sampleTypes = samTypeList.stream().filter(p -> sampleTypeMap.get(record.getId()).contains(p.getId())).map(DtoSampleType::getTypeName).collect(Collectors.toList());
                record.setSampleTypeNames(String.join(",", sampleTypes));
            }
        }
        inquiry.setSampleAssign(recordList);
    }

    private void setAnalyzeInfo(DtoProjectInquiry inquiry, DtoProject project, EnumProjectStatus projectStatus, String projectRegisterPage) {
        Integer projectStatusInt = StringUtil.isNotNull(projectStatus) ? projectStatus.getValue() : 0;
        Map<String, List<DtoProjectInquiryAnalyse>> map = new HashMap<>();
        map.put("wait", new ArrayList<>());
        map.put("monitor", new ArrayList<>());
        if (projectStatusInt >= EnumProjectStatus.开展中.getValue()) {
            if (!projectRegisterPage.equals(EnumProjectType.委托类.getValue()) && !EnumProjectType.例行类.getValue().equals(projectRegisterPage) && !EnumProjectType.全流程.getValue().equals(projectRegisterPage) &&
                    StringUtil.isNotNull(inquiry.getSampleAssign()) && inquiry.getSampleAssign().size() > 0) {//只有一张单子且该单子在样品交接或样品分配中，则不存在任何实验室数据
                inquiry.setAnalyzeInfo(map);
                return;
            }
            PageBean<DtoProjectInquiryAnalyse> pb = new PageBean<>();
            Map<String, Object> values = new HashMap<>();
            StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoProjectInquiryAnalyse(");
            stringBuilder.append("a.id,a.testId,a.redAnalyzeItemName,a.redAnalyzeMethodName,a.redCountryStandard,a.analystId,a.analystName)");
            pb.setEntityName("DtoAnalyseData a,DtoSample s");
            pb.setSelect(stringBuilder.toString());
            pb.setRowsPerPage(Integer.MAX_VALUE);
            pb.addCondition(" and s.isDeleted = 0 and a.isDeleted = 0 ");
            pb.addCondition(" and s.projectId = :projectId");
            values.put("projectId", project.getId());
            pb.addCondition(" and a.sampleId = s.id");
            pb.addCondition(" and a.workSheetFolderId = :workSheetFolderId");
            values.put("workSheetFolderId", UUIDHelper.GUID_EMPTY);
            pb.addCondition(" and s.innerReceiveStatus >= :innerReceiveStatus");
            values.put("innerReceiveStatus", EnumInnerReceiveStatus.已经领取.getValue());
            comRepository.findByPage(pb, values);

            List<DtoProjectInquiryAnalyse> analyseWaitList = new ArrayList<>();
            pb.getData().stream().collect(Collectors.groupingBy(DtoProjectInquiryAnalyse::getTestId, Collectors.toList())).forEach((testId, list) -> {
                DtoProjectInquiryAnalyse analyse = list.get(0);
                analyse.setSampleNum(list.size());
                analyse.setStatus("待检测");
                analyseWaitList.add(analyse);
            });
            map.put("wait", analyseWaitList);
            pb = new PageBean<>();
            values = new HashMap<>();
            stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoProjectInquiryAnalyse(");
            stringBuilder.append("a.id,a.testId,a.redAnalyzeItemName,a.redAnalyzeMethodName,a.redCountryStandard,w.id,w.workSheetCode,");
            stringBuilder.append("w.analystId,w.analystName,w.analyzeTime,w.workStatus,w.status)");
            pb.setEntityName("DtoAnalyseData a,DtoSample s,DtoWorkSheetFolder w");
            pb.setSelect(stringBuilder.toString());
            pb.setRowsPerPage(Integer.MAX_VALUE);
            pb.addCondition(" and s.isDeleted = 0 and a.isDeleted = 0 and w.isDeleted = 0 ");
            pb.addCondition(" and s.projectId = :projectId");
            values.put("projectId", project.getId());
            pb.addCondition(" and a.sampleId = s.id");
            pb.addCondition(" and a.workSheetFolderId = w.id");
            pb.addCondition(" and w.workStatus != 32");
            comRepository.findByPage(pb, values);
            List<DtoProjectInquiryAnalyse> analyzingList = new ArrayList<>();
            pb.getData().stream().collect(Collectors.groupingBy(DtoProjectInquiryAnalyse::getWorkSheetFolderId, Collectors.toList())).forEach((key, list) -> {
                DtoProjectInquiryAnalyse analyse = list.get(0);
                String redAnalyzeItemName = String.join(",", list.stream().map(DtoProjectInquiryAnalyse::getRedAnalyzeItemName).distinct().collect(Collectors.toList()));
                analyse.setRedAnalyzeItemName(redAnalyzeItemName);
                analyse.setSampleNum(list.size());
                analyzingList.add(analyse);
            });
            analyzingList.sort(Comparator.comparing(DtoProjectInquiryAnalyse::getAnalyzeTime, Comparator.reverseOrder()));
            map.put("monitor", analyzingList);
        }
        inquiry.setAnalyzeInfo(map);
    }

    private void setReport(DtoProjectInquiry inquiry, DtoProject project, EnumProjectStatus projectStatus) {
        Integer projectStatusInt = StringUtil.isNotNull(projectStatus) ? projectStatus.getValue() : 0;
        if (projectStatusInt >= EnumProjectStatus.开展中.getValue()) {
            PageBean<DtoReport> pb = new PageBean<>();
            ReportCriteria criteria = new ReportCriteria();
            criteria.setProjectId(project.getId());
            criteria.setReportStatusList(Stream.of(EnumReportState.编制报告中.name(), EnumReportState.报告未通过.name()).collect(Collectors.toList()));
            pb.setRowsPerPage(Integer.MAX_VALUE);
            reportService.findByPage(pb, criteria);
            inquiry.setReport(pb.getData());
            return;
        }
        inquiry.setReport(new ArrayList<>());
    }

    //#endregion 项目进度

    //#region 项目文档

    /**
     * 获取项目文档
     *
     * @param projectId 项目id
     * @return 项目文档
     */
    @Override
    public Map<String, Object> findDocByProjectId(String projectId, Integer type, String docTypeId) {
        Map<String, Object> result = new HashMap<>();
        //项目登记文档，包含登记文档和踏勘附件
        Map<String, Object> project = new HashMap<>();
        List<DtoDocument> projectFile = this.findProjectDoc(Collections.singletonList(projectId));
        project.put("projectFile", projectFile);
        List<DtoDocument> ExploreFile = this.findExploreFile(Collections.singletonList(projectId));
        project.put("ExploreFile", ExploreFile);
        List<DtoDocument> projectDzbd = this.findProjectDzbd(Collections.singletonList(projectId));
        project.put("projectDzbd", projectDzbd);

        //采样附件，包含采样记录单附件,人员签名和现场附件
        Map<String, Object> samplingRecord = new HashMap<>();
        log.warn("执行fillingSamplingRecordFile方法开始");
        this.fillingSamplingRecordFile(Collections.singletonList(projectId), type, samplingRecord, docTypeId);
        log.warn("执行fillingSamplingRecordFile方法完成");
        log.warn("执行findFieldFile(查询现场数据附件)方法开始");
        samplingRecord.put("fieldFile", this.findFieldFile(projectId, type));
        log.warn("执行findFieldFile(查询现场数据附件)方法完成");
        log.warn("执行findSignature(采样人员签名)方法开始");
        samplingRecord.put("signature", this.findSignature(Collections.singletonList(projectId), type));
        log.warn("执行findSignature(采样人员签名)方法完成");

        //原始记录单，包含原始记录单与解析及图谱文件
        Map<String, Object> originalRecord = new HashMap<>();
        originalRecord.put("originalRecordFile", this.findOriginalRecordDoc(projectId));
        originalRecord.put("resolveFile", this.findResolveFile(projectId));

        ////报告，包含报告文档和电子报告文档
        Map<String, Object> report = new HashMap<>();
        List<DtoDocument> reportFile = this.findReportDoc(projectId);
        report.put("reportFile", reportFile);
        report.put("electronicReport", this.findElectronicReportDoc(projectId));

        //项目交接单(包含样品交接电子表单)
        List<DtoDocument> deliverySpreadDocumentList = this.findDeliverySpreadDocument(projectId);

        result.put("project", project);
        result.put("samplingRecord", samplingRecord);
        result.put("originalRecord", originalRecord);
        result.put("report", report);
        result.put("delivery", deliverySpreadDocumentList);

        //总览  委托单、样品交接单、采样记录单、采样单附件、分析记录单、图谱文件、电子报告
        Map<String, Object> overview = new HashMap<>();
        List<DtoDocument> wtFiles = new ArrayList<>();
        wtFiles.addAll(projectFile);
        wtFiles.addAll(ExploreFile);
        wtFiles.addAll(projectDzbd);
        overview.put("委托单", wtFiles);
        overview.put("样品交接单", deliverySpreadDocumentList);
        overview.put("采样记录单", samplingRecord.get("samplingRecordFile"));
        List<DtoDocument> samplingRecordFiles = new ArrayList<>();
        samplingRecordFiles.addAll((List<DtoDocument>) samplingRecord.get("otherAttachments"));
        samplingRecordFiles.addAll((List<DtoDocument>) samplingRecord.get("mobileRecord"));
        samplingRecordFiles.addAll((List<DtoDocument>) samplingRecord.get("fieldFile"));
        samplingRecordFiles.addAll((List<DtoDocument>) samplingRecord.get("signature"));
        overview.put("采样单附件", samplingRecordFiles);
        List<DtoDocument> recordFiles = (List<DtoDocument>) originalRecord.get("originalRecordFile");
        overview.put("分析记录单", recordFiles.stream().filter(v -> StringUtil.isNotEmpty(v.getFilename())).collect(Collectors.toList()));
        overview.put("图谱文件", originalRecord.get("resolveFile"));
        List<DtoDocument> dzbgFiles = new ArrayList<>(reportFile);
        dzbgFiles.addAll(findElectronicReportDocDocument(projectId));
        overview.put("电子报告", dzbgFiles);
        result.put("overview", overview);
        return result;
    }

    /**
     * 获取项目文档
     *
     * @param reportId 项目id
     * @return 项目文档
     */
    @Override
    public Map<String, Object> findDocByReportId(String reportId, Integer type, String docTypeId) {
        //找到报告下的样品
        List<String> sampleIds = reportDetailRepository.findByReportId(reportId).stream().filter(p -> p.getObjectType()
                .equals(EnumReportDetailType.样品.getValue())).map(DtoReportDetail::getObjectId).collect(Collectors.toList());
        if (sampleIds.size() == 0) {
            return new HashMap<>();
        }
        List<DtoSample> sampleList = sampleRepository.findAll(sampleIds);
        //通过样品找到对应的项目
        List<String> projectIds = sampleList.stream().map(DtoSample::getProjectId).filter(projectId -> !projectId.equals(UUIDHelper.GUID_EMPTY))
                .distinct().collect(Collectors.toList());
        List<DtoProject> projectList = projectRepository.findAll(projectIds);
        Map<String, Object> result = new HashMap<>();
        //项目登记文档，包含登记文档和踏勘附件
        Map<String, Object> project = new HashMap<>();
        List<DtoDocument> projectFileList = this.findProjectDoc(projectIds);
        projectFileList.forEach(p -> {
            Optional<DtoProject> projectOptional = projectList.stream().filter(s -> p.getFolderId().equals(s.getId())).findFirst();
            projectOptional.ifPresent(o -> p.setProjectCode(o.getProjectCode()));
        });
        project.put("projectFile", projectFileList);
        List<DtoDocument> exploreFileList = this.findExploreFile(projectIds);
        exploreFileList.forEach(p -> {
            Optional<DtoProject> projectOptional = projectList.stream().filter(s -> p.getFolderId().equals(s.getId())).findFirst();
            projectOptional.ifPresent(o -> p.setProjectCode(o.getProjectCode()));
        });
        project.put("ExploreFile", exploreFileList);
        //采样附件，包含采样记录单附件,人员签名和现场附件
        Map<String, Object> samplingRecord = new HashMap<>();
        //根据样品找送样单
        List<String> receiveIds = sampleList.stream().map(DtoSample::getReceiveId).filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY))
                .distinct().collect(Collectors.toList());
        log.warn("执行fillingSamplingRecordFile方法开始");
        this.fillingSamplingRecordFile(receiveIds, type, samplingRecord, docTypeId);
        log.warn("执行fillingSamplingRecordFile方法完成");
        log.warn("执行findFieldFile(查询现场数据附件)方法开始");
        samplingRecord.put("fieldFile", this.findFieldFile(sampleList, receiveIds));
        log.warn("执行findFieldFile(查询现场数据附件)方法完成");
        log.warn("执行findSignature(采样人员签名)方法开始");
        samplingRecord.put("signature", this.findSignature(receiveIds, type));
        log.warn("执行findSignature(采样人员签名)方法完成");

        //原始记录单，包含原始记录单与解析及图谱文件
        Map<String, Object> originalRecord = new HashMap<>();
        List<String> worksheetFolderIds = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds).stream()
                .map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        originalRecord.put("originalRecordFile", this.findAllWorkSheetDoc(worksheetFolderIds));
        originalRecord.put("resolveFile", this.worksheetResolvFile(worksheetFolderIds));

        result.put("project", project);
        result.put("samplingRecord", samplingRecord);
        result.put("originalRecord", originalRecord);
        return result;
    }

    /**
     * 判断一项一档文件是否为空
     *
     * @param doc 一项一档文件容器
     * @return true为空，false为非空
     */
    private boolean checkFileContainerIsEmpty(Map<String, Object> doc) {
        boolean docCollectionEmptyFlag = true;
        if (StringUtil.isNotEmpty(doc)) {
            for (Map.Entry<String, Object> e : doc.entrySet()) {
                Object v = e.getValue();
                if (v.getClass().getName().contains("Map")) {
                    Map<String, Object> mapItem = (Map<String, Object>) v;
                    if (StringUtil.isNotEmpty(mapItem)) {
                        for (Map.Entry<String, Object> entry : mapItem.entrySet()) {
                            Object docV = entry.getValue();
                            if (docV.getClass().getName().contains("List")) {
                                List<DtoDocument> docItem = (List<DtoDocument>) docV;
                                if (StringUtil.isNotEmpty(docItem)) {
                                    docCollectionEmptyFlag = false;
                                }
                            }
                        }
                    }
                }
            }
        }
        return docCollectionEmptyFlag;
    }

    @Override
    public String batchDownload(String projectId, HttpServletResponse response) {
        DtoProject project = projectRepository.findOne(projectId);
        if (StringUtils.isNull(project)) {
            throw new BaseException("未找到该项目");
        }
        //获取所有一项一档文件
        Map<String, Object> doc = this.findDocByProjectId(projectId, 1, LimConstants.DOCUMENT_TYPE_ALL);
        groupOriginalRecordByTest(doc);
        if (this.checkFileContainerIsEmpty(doc)) {
            throw new BaseException("项目中无一项一档文件");
        }
        //如果有文档，在outputPath路径中按照项目名称创建临时文件夹
        String tempOutDirectoryStr = filePathConfig.getOutputPath() + File.separator + "zip" + File.separator + project.getProjectName();
        File tempDocDirectoryFile = new File(tempOutDirectoryStr);
        if (!tempDocDirectoryFile.exists()) {
            tempDocDirectoryFile.mkdirs();
        }
        //将文件复制到outputPath中，按照map中的key名分文件夹
        doc.forEach((k, v) -> {
            //第一层遍历，根据一项一档类别分组
            if (v.getClass().getName().contains("Map")) {
                String tempGroup = tempOutDirectoryStr + File.separator;
                Map<String, Object> docGroup = (Map<String, Object>) v;
                //第二层遍历，根据每个分组中具体的步骤分组
                docGroup.forEach((groupK, groupV) -> {
                    if ("originalRecordFile".equals(groupK) || "resolveFile".equals(groupK)) {
                        //再次遍历各个测试项目下的原始记录及解析图谱文件
                        Map<String, List<DtoDocument>> item2DocMap = (Map<String, List<DtoDocument>>) groupV;
                        if (StringUtil.isNotEmpty(item2DocMap)) {
                            for (Map.Entry<String, List<DtoDocument>> entry : item2DocMap.entrySet()) {
                                String itemName = entry.getKey();
                                for (DtoDocument document : entry.getValue()) {
                                    if ("originalRecordFile".equals(groupK) && StringUtil.isNotEmpty(document.getWorkSheetPathList())) {
                                        for (String path : document.getWorkSheetPathList()) {
                                            String toPath = tempGroup + File.separator + convertFilePath2Chinese(groupK) + File.separator + itemName;
                                            batchDownload(toPath, document, path);
                                        }
                                    } else {
                                        String toPath = tempGroup + File.separator + convertFilePath2Chinese(groupK) + File.separator + itemName;
                                        batchDownload(toPath, document, document.getPath());
                                    }
                                }
                            }
                        }
                    } else {
                        List<DtoDocument> docs = (List<DtoDocument>) groupV;
                        for (DtoDocument document : docs) {
                            //处理原始记录单，原始记录单为合并数据
                            if ("originalRecordFile".equals(groupK) && StringUtil.isNotEmpty(document.getWorkSheetPathList())) {
                                for (String path : document.getWorkSheetPathList()) {
                                    batchDownload(tempGroup, groupK, document, path);
                                }
                            } else {
                                batchDownload(tempGroup, groupK, document, document.getPath());
                            }
                        }
                    }
                });
            }
        });
        //压缩组装完成的临时存储文件夹，完成压缩的压缩包放在流中返回，完成压缩后临时文件夹删除
        documentService.directoryToZip(tempOutDirectoryStr, response);
        return null;
    }

    /**
     * 将检测单原始记录文件及解析图谱文件按照测试项目进行分组
     *
     * @param doc 项目一项一档文件映射对象
     */
    private void groupOriginalRecordByTest(Map<String, Object> doc) {
        Map<String, Object> originalRecordMap = (Map<String, Object>) doc.get("originalRecord");
        if (StringUtil.isNotEmpty(originalRecordMap)) {
            List<DtoDocument> recordFileList = (List<DtoDocument>) originalRecordMap.get("originalRecordFile");
            Map<String, List<String>> itemName2FolderIdsMap = new HashMap<>();
            Map<String, List<DtoDocument>> item2RecordFileDocMap = new HashMap<>();
            if (StringUtil.isNotEmpty(recordFileList)) {
                itemName2FolderIdsMap = getItem2FolderIdsMap(recordFileList);
                for (Map.Entry<String, List<String>> entry : itemName2FolderIdsMap.entrySet()) {
                    List<DtoDocument> docForItem = recordFileList.stream().filter(p -> entry.getValue().contains(p.getFolderId())).collect(Collectors.toList());
                    item2RecordFileDocMap.put(entry.getKey(), docForItem);
                }
                if (StringUtil.isEmpty(item2RecordFileDocMap)) {
                    item2RecordFileDocMap.put("分析项目", recordFileList);
                }
            }
            originalRecordMap.put("originalRecordFile", item2RecordFileDocMap);
            List<DtoDocument> resolveFileList = (List<DtoDocument>) originalRecordMap.get("resolveFile");
            Map<String, List<DtoDocument>> item2ResolveFileDocMap = new HashMap<>();
            if (StringUtil.isNotEmpty(resolveFileList)) {
                itemName2FolderIdsMap = getItem2FolderIdsMap(resolveFileList);
                for (Map.Entry<String, List<String>> entry : itemName2FolderIdsMap.entrySet()) {
                    List<DtoDocument> docForItem = resolveFileList.stream().filter(p -> entry.getValue().contains(p.getFolderId())).collect(Collectors.toList());
                    item2ResolveFileDocMap.put(entry.getKey(), docForItem);
                }
                if (StringUtil.isEmpty(item2ResolveFileDocMap)) {
                    item2ResolveFileDocMap.put("分析项目", resolveFileList);
                }
            }
            originalRecordMap.put("resolveFile", item2ResolveFileDocMap);
        }
    }

    private Map<String, List<String>> getItem2FolderIdsMap(List<DtoDocument> recordFileList) {
        List<String> workFolderIdList = recordFileList.stream().map(DtoDocument::getFolderId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<DtoAnalyseData> anaDataList = StringUtil.isNotEmpty(workFolderIdList) ? analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workFolderIdList) : new ArrayList<>();
        Map<String, List<String>> itemName2FolderIdsMap = anaDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getRedAnalyzeItemName,
                Collectors.collectingAndThen(Collectors.toList(), value -> value.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList()))));
        return itemName2FolderIdsMap;
    }


    /**
     * 批量针对路径文件进行下载
     *
     * @param tempGroup 临时文件夹路径
     * @param groupK    附件类型
     * @param document  附件数据
     * @param path      附件数据下的附件路径集合
     */
    private void batchDownload(String tempGroup, String groupK, DtoDocument document, String path) {
        String toPath = tempGroup + File.separator + convertFilePath2Chinese(groupK);
        batchDownload(toPath, document, path);
    }

    /**
     * 批量针对路径文件进行下载
     *
     * @param toPath 临时文件夹路径
     * @param document  附件数据
     * @param path      附件数据下的附件路径集合
     */
    private void batchDownload(String toPath, DtoDocument document, String path) {
        File toDir = new File(toPath);
        if (!toDir.exists()) {
            toDir.mkdirs();
        }
        //获取具体附件文件的父文件夹，将整个文件夹复制到outputPath中
        String srcDiStr = filePathConfig.getFilePath() + path;
        File srcDir = new File(srcDiStr);
        if (srcDir.exists()) {
            try {
                FileUtils.copyFileToDirectory(srcDir, toDir);
                File destFile = new File(toDir + File.separator + document.getPhysicalName());
                if (destFile.exists()) {
                    destFile.renameTo(new File(toDir + File.separator + document.getFilename()));
                }
            } catch (Exception e) {
                throw new BaseException("批量下载文件出错");
            }
        }
    }

    /**
     * 判断测试项目数据中的测试项目在系统中是否被删除是否存在
     *
     * @param samplingFrequencyTest 测试项目数据
     */
    @Override
    public void checkTestIsExists(List<DtoSamplingFrequencyTest> samplingFrequencyTest) {
        if (StringUtil.isNotEmpty(samplingFrequencyTest)) {
            List<String> notExistsTests = new ArrayList<>();
            List<String> testIds = samplingFrequencyTest.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toList());
            List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
            for (DtoSamplingFrequencyTest frequencyTest : samplingFrequencyTest) {
                String testId = frequencyTest.getTestId();
                List<DtoTest> redisTests = testList.stream().filter(p -> testId.equals(p.getId())).collect(Collectors.toList());
                if (StringUtil.isEmpty(redisTests)) {
                    notExistsTests.add("分析因子:" + frequencyTest.getRedAnalyzeItemName() + ",分析方法:" + frequencyTest.getRedAnalyzeMethodName());
                }
            }
            if (StringUtil.isNotEmpty(notExistsTests)) {
                notExistsTests = notExistsTests.stream().distinct().collect(Collectors.toList());
                throw new BaseException(notExistsTests + "已不存在，无法复制到新建对象!");
            }
        }
    }

    /**
     * 批量下载时把英文路径名转换成中文路径名
     *
     * @param englishName 英文路径名
     * @return 中文路径名
     */
    private String convertFilePath2Chinese(String englishName) {
        switch (englishName) {
            case "reportFile":
            case "electronicReport":
                return "报告附件";
            case "projectFile":
                return "登记附件";
            case "ExploreFile":
                return "踏勘附件";
            case "originalRecordFile":
                return "原始记录单";
            case "resolveFile":
                return "解析及图谱文件";
            case "samplingRecordFile":
                return "采样记录单";
            case "fieldFile":
            case "mobileRecord":
            case "otherAttachments":
                return "现场附件";
            case "signature":
                return "人员签名";
            case "LawEnforcementAnnex":
                return "执法附件";
            default:
                return "未知文件";
        }
    }

    /**
     * 获取项目文档
     *
     * @param projectIds 项目ids
     * @return 项目文档
     */
    private List<DtoDocument> findProjectDoc(List<String> projectIds) {
        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = new DocumentCriteria();
        criteria.setFolderIds(projectIds);
        criteria.setDocTypeIds(Arrays.asList(BaseCodeHelper.DOCUMENT_EXTEND_REGISTER, BaseCodeHelper.DOCUMENT_EXTEND_PHOTO_VIDEO));
        documentService.findByPage(pageBean, criteria);

        if (StringUtil.isNotNull(pageBean.getData())) {
            pageBean.getData().sort(Comparator.comparing(DtoDocument::getCreateDate));
            return pageBean.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 获取项目
     *
     * @param projectIds 项目ids
     * @return 项目文档
     */
    private List<DtoDocument> findProjectDzbd(List<String> projectIds) {
        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = new DocumentCriteria();
        criteria.setFolderIds(projectIds);
        criteria.setDocTypeIds(Collections.singletonList(BaseCodeHelper.DOCUMENT_EXTEND_PROJECT_SHEET));
        documentService.findByPage(pageBean, criteria);

        if (StringUtil.isNotNull(pageBean.getData())) {
            pageBean.getData().sort(Comparator.comparing(DtoDocument::getCreateDate));
            return pageBean.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 查询项目踏勘附件
     *
     * @param projectIds 项目id
     * @return 踏勘附件文件list集合
     */
    private List<DtoDocument> findExploreFile(List<String> projectIds) {
        List<DtoExplore> explores = exploreRepository.findByProjectIdIn(projectIds);
        List<String> exploreIds = explores.stream().map(DtoExplore::getId).collect(Collectors.toList());
        return documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(exploreIds, BaseCodeHelper.DOCUMENT_EXTEND_EXPLORE);
    }

    /**
     * 查询现场数据附件
     *
     * @param projectId 项目id
     * @return 当前项目的现场附件
     */
    private List<DtoDocument> findFieldFile(String projectId, Integer type) {
        List<String> folderIds;
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findByProjectId(projectId);
        List<DtoSample> sampleList = new ArrayList<>();
        if (new Integer(2).equals(type)) {
            sampleList = sampleRepository.findByReceiveId(projectId);
            folderIds = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
            sampleFolders = sampleFolderRepository.findAll(folderIds);
        }
        if (sampleFolders.size() == 0) {
            return new ArrayList<>();
        }
        folderIds = sampleFolders.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());

        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = new DocumentCriteria();
        if (folderIds.size() > 1) {
            criteria.setFolderIds(folderIds);
        } else {
            criteria.setFolderId(folderIds.get(0));
        }
        // 移动端采样附件拆分为采样前，采样中，采样后，所以查三种附件
        List<String> docTypeIds = new ArrayList<>();
        docTypeIds.add(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_AFTER);
        docTypeIds.add(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_IN);
        docTypeIds.add(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_FRONT);
        criteria.setDocTypeIds(docTypeIds);
        documentService.findByPage(pageBean, criteria);

        if (StringUtil.isNotNull(pageBean.getData())) {
            List<DtoReceiveSampleRecord> records = receiveSampleRecordRepository.findByProjectId(projectId);
            pageBean.getData().sort(Comparator.comparing(DtoDocument::getCreateDate));
            //遍历填充送样人和送样单号
            for (DtoDocument datum : pageBean.getData()) {
                List<DtoSample> samplesOfSampleFolder = sampleList.stream()
                        .filter(s -> s.getSampleFolderId().equals(datum.getFolderId()))
                        .collect(Collectors.toList());
                String receiveId = samplesOfSampleFolder.stream()
                        .filter(s -> StringUtil.isNotEmpty(s.getReceiveId())
                                && UUIDHelper.GUID_EMPTY.equals(s.getReceiveId()))
                        .map(DtoSample::getReceiveId)
                        .findFirst()
                        .orElse("");
                if (StringUtil.isNotEmpty(receiveId)) {
                    Optional<DtoReceiveSampleRecord> receiveSampleRecord = records.stream()
                            .filter(r -> r.getId().equals(receiveId))
                            .findFirst();
                    receiveSampleRecord.ifPresent(r -> {
                        datum.setRecordCode(r.getRecordCode());
                        datum.setSenderName(r.getSenderName());
                    });
                }

            }
            //去除其他的类型文件
            return pageBean.getData();
        }
        return new ArrayList<>();
    }

    private List<DtoDocument> findFieldFile(List<DtoSample> sampleList, List<String> receiveIds) {
        List<String> folderIds = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findAll(folderIds);
        if (sampleFolders.size() == 0) {
            return new ArrayList<>();
        }
        folderIds = sampleFolders.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());

        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = new DocumentCriteria();
        if (folderIds.size() > 1) {
            criteria.setFolderIds(folderIds);
        } else {
            criteria.setFolderId(folderIds.get(0));
        }
        // 移动端采样附件拆分为采样前，采样中，采样后，所以查三种附件
        List<String> docTypeIds = new ArrayList<>();
        docTypeIds.add(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_AFTER);
        docTypeIds.add(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_IN);
        docTypeIds.add(BaseCodeHelper.DOCUMENT_SAMPLEFOLDER_SAMPLINGPICTURE_FRONT);
        criteria.setDocTypeIds(docTypeIds);
        documentService.findByPage(pageBean, criteria);

        if (StringUtil.isNotNull(pageBean.getData())) {
            List<DtoReceiveSampleRecord> records = receiveSampleRecordRepository.findAll(receiveIds);
            pageBean.getData().sort(Comparator.comparing(DtoDocument::getCreateDate));
            //遍历填充送样人和送样单号
            for (DtoDocument datum : pageBean.getData()) {
                List<DtoSample> samplesOfSampleFolder = sampleList.stream()
                        .filter(s -> s.getSampleFolderId().equals(datum.getFolderId()))
                        .collect(Collectors.toList());
                String receiveId = samplesOfSampleFolder.stream()
                        .filter(s -> StringUtil.isNotEmpty(s.getReceiveId())
                                && UUIDHelper.GUID_EMPTY.equals(s.getReceiveId()))
                        .map(DtoSample::getReceiveId)
                        .findFirst()
                        .orElse("");
                if (StringUtil.isNotEmpty(receiveId)) {
                    Optional<DtoReceiveSampleRecord> receiveSampleRecord = records.stream()
                            .filter(r -> r.getId().equals(receiveId))
                            .findFirst();
                    receiveSampleRecord.ifPresent(r -> {
                        datum.setRecordCode(r.getRecordCode());
                        datum.setSenderName(r.getSenderName());
                    });
                }

            }
            //去除其他的类型文件
            return pageBean.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 采样人员签名
     *
     * @param projectIds
     * @return
     */
    private List<DtoDocument> findSignature(List<String> projectIds, Integer type) {
        List<String> folderIds;
        List<DtoReceiveSampleRecord> records = receiveSampleRecordRepository.findByProjectIdIn(projectIds);
        if (new Integer(2).equals(type)) {
            records = receiveSampleRecordRepository.findAll(projectIds);
        }
        if (records.size() == 0) {
            return new ArrayList<>();
        }
        folderIds = records.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());

        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = new DocumentCriteria();
        if (folderIds.size() > 1) {
            criteria.setFolderIds(folderIds);
        } else {
            criteria.setFolderId(folderIds.get(0));
        }
        criteria.setDocTypeId(BaseCodeHelper.DOCUMENT_RECEIVESAMPLERECORD_SIGNERATURE);
        documentService.findByPage(pageBean, criteria);

        if (StringUtil.isNotNull(pageBean.getData())) {
            pageBean.getData().sort(Comparator.comparing(DtoDocument::getCreateDate));
            //去除其他的类型文件
            return pageBean.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 解析及图谱文件
     *
     * @param projectId 项目id
     * @return 当前项目的解析及图谱文件
     */
    private List<DtoDocument> findResolveFile(String projectId) {
        List<DtoProject2WorkSheetFolder> p2wList = project2WorkSheetFolderRepository.findByProjectId(projectId);
        if (p2wList.size() == 0) {
            return new ArrayList<>();
        }
        List<String> workSheetFolderIds = p2wList.stream()
                .map(DtoProject2WorkSheetFolder::getWorkSheetFolderId)
                .collect(Collectors.toList());
        if (p2wList.size() > 1) {
            //过滤掉为空的folderId
            workSheetFolderIds = workSheetFolderIds.stream().filter(p -> !"".equals(p) && !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
            if (StringUtil.isEmpty(workSheetFolderIds)) {
                return new ArrayList<>();
            }
        } else {
            String worksheetFolderId = p2wList.get(0).getWorkSheetFolderId();
            if ("".equals(worksheetFolderId) || UUIDHelper.GUID_EMPTY.equals(worksheetFolderId)) {
                return new ArrayList<>();
            }
            workSheetFolderIds.add(worksheetFolderId);
        }
        return worksheetResolvFile(workSheetFolderIds);
    }

    private List<DtoDocument> worksheetResolvFile(List<String> workSheetFolderIds) {
        List<DtoWorkSheetFolder> workSheetFolders = new ArrayList<>();
        if (StringUtil.isNotEmpty(workSheetFolderIds)) {
            workSheetFolders = workSheetFolderRepository.findAll(workSheetFolderIds);
        }
        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = new DocumentCriteria();
        criteria.setFolderIds(workSheetFolderIds);
        criteria.setDocTypeId(BaseCodeHelper.DOCUMENT_WORKSHEETFOLDER_ANALYSEIS);
        documentService.findByPage(pageBean, criteria);
        if (StringUtil.isNotNull(pageBean.getData())) {
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workSheetFolderIds);
            pageBean.getData().sort(Comparator.comparing(DtoDocument::getCreateDate));
            //遍历数据填充工作单号和分析项目名称
            for (DtoDocument datum : pageBean.getData()) {
                Optional<DtoWorkSheetFolder> workSheetFolderOptional = workSheetFolders.parallelStream()
                        .filter(w -> w.getId().equals(datum.getFolderId()))
                        .findFirst();
                workSheetFolderOptional.ifPresent(w -> {
                    List<DtoAnalyseData> analyseDataOfFolder = analyseDataList.parallelStream()
                            .filter(a -> a.getWorkSheetFolderId().equals(w.getId()))
                            .collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(analyseDataOfFolder)) {
                        String analyseNames = analyseDataOfFolder.stream()
                                .sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName))
                                .map(DtoAnalyseData::getRedAnalyzeItemName)
                                .distinct()
                                .collect(Collectors.joining("、"));
                        datum.setAnalyseNames(analyseNames);
                    }
                    datum.setWorkSheetCode(w.getWorkSheetCode());
                });
            }
            return pageBean.getData();
        }
        return new ArrayList<>();
    }

    protected void fillingSamplingRecordFile(List<String> projectIds, Integer type, Map<String, Object> samplingRecord, String docTypeId) {
        List<DtoReceiveSampleRecord> records = receiveSampleRecordRepository.findByProjectIdIn(projectIds);
        if (new Integer(2).equals(type)) {
            records = receiveSampleRecordRepository.findAll(projectIds);
        }
        List<DtoDocument> sampleRecord = new ArrayList<>();
        List<DtoDocument> otherAttachments = new ArrayList<>();
        List<DtoDocument> mobileRecord = new ArrayList<>();
        List<DtoDocument> pointPic = new ArrayList<>();

        if (StringUtil.isNotEmpty(records)) {
            List<String> folderIds = records.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
            PageBean<DtoDocument> pageBean = new PageBean<>();
            pageBean.setRowsPerPage(Integer.MAX_VALUE);
            List<String> docType = Arrays.asList("sampleRecord", "otherAttachments", "PRO_MobileRecord_Delivery", "BASE_DocumentExtendType_ReceiveSampleRecord");
            if (!LimConstants.DOCUMENT_TYPE_ALL.equals(docTypeId)) {
                docType = Arrays.asList(docTypeId.split(",").clone());
            }
            DocumentCriteria criteria = new DocumentCriteria();
            if (StringUtil.isNotEmpty(folderIds)) {
                criteria.setFolderIds(folderIds);
                criteria.setDocTypeIds(docType);
            }
            log.warn("开始分页查询document");
            documentService.findByPage(pageBean, criteria);
            log.warn("分页查询document完成");
            List<DtoDocument> allDocuments = pageBean.getData();
            if (StringUtil.isNotEmpty(allDocuments)) {
                allDocuments.sort(Comparator.comparing(DtoDocument::getCreateDate));
                //填充一项一档中的送样单号和送样人名
                for (DtoDocument datum : allDocuments) {
                    Optional<DtoReceiveSampleRecord> receiveSampleRecord = records.parallelStream()
                            .filter(r -> r.getId().equals(datum.getFolderId()))
                            .findFirst();
                    receiveSampleRecord.ifPresent(r -> {
                        datum.setRecordCode(r.getRecordCode());
                        datum.setSenderName(r.getSenderName());
                        if (r.getSamplingTime() != null) {
                            datum.setSamplingDate(DateUtil.dateToString(r.getSamplingTime(), DateUtil.YEAR));
                        }
                        if (StringUtil.isNotEmpty(r.getJson())) {
                            TypeLiteral<Map<String, Object>> typeLiteral = new TypeLiteral<Map<String, Object>>() {
                            };
                            Map<String, Object> map = JsonIterator.deserialize(r.getJson(), typeLiteral);
                            String sampleTypeIds = map.containsKey("sampleTypeIds") ? map.get("sampleTypeIds").toString() : "";
                            if (StringUtil.isNotEmpty(sampleTypeIds)) {
                                List<String> sampleTypeIdList = Arrays.asList(sampleTypeIds.split(","));
                                List<String> sampleTypeList = sampleTypeService.findAll().stream().filter(v -> sampleTypeIdList.contains(v.getId()))
                                        .map(DtoSampleType::getTypeName).sorted().collect(Collectors.toList());
                                datum.setTag(String.join("、", sampleTypeList));
                            }
                        }
                    });
                }
                //去除其他的类型文件
                log.warn("开始去除其他的类型文件");
                List<String> finalDocType = docType;
                List<DtoDocument> documents = allDocuments.stream()
                        .filter(p -> finalDocType.contains(p.getDocTypeId()))
                        .sorted(Comparator.comparing(DtoDocument::getCreateDate).reversed())
                        .collect(Collectors.toList());
                for (DtoDocument document : documents) {
                    if (document.getDocTypeId().equals(BaseCodeHelper.DOCUMENT_SAMPLE_RECORD)) {
                        sampleRecord.add(document);
                    } else if (document.getDocTypeId().equals(BaseCodeHelper.DOCUMENT_SAMPLE_OTHERATTACH)
                            || document.getDocTypeId().equals(BaseCodeHelper.DOCUMENT_EXTEND_RECEIVE_RECORD)) {
                        otherAttachments.add(document);
                    } else if (document.getDocTypeId().equals(BaseCodeHelper.DOCUMENT_MOBILERECORD)) {
                        mobileRecord.add(document);
                    }
                }
                log.warn("去除其他的类型文件完成");
            }
        }
        samplingRecord.put("samplingRecordFile", sampleRecord);
        samplingRecord.put("otherAttachments", otherAttachments);
        samplingRecord.put("mobileRecord", mobileRecord);
    }


    /**
     * 处理合并原始记录单附件数据
     *
     * @param workSheetFolderIds 原始记录单id集合
     * @param workSheetDocGroup  原始记录单附件数据
     * @return 合并后的原始记录单附件
     */
    private List<DtoDocument> hanldeDoc(List<String> workSheetFolderIds, Map<String, List<DtoDocument>> workSheetDocGroup) {
        //获取需要返回的原始记录单附件
        List<DtoDocument> documents = new ArrayList<>();
        workSheetFolderIds.remove(UUIDHelper.GUID_EMPTY);
        //获取所有原始记录单数据
        List<DtoWorkSheetFolder> workSheetFolders = StringUtil.isNotEmpty(workSheetFolderIds) ? workSheetFolderRepository.findByIdIn(workSheetFolderIds) : new ArrayList<>();
        //获取工作单的子工作单数据
        List<DtoWorkSheet> workSheetList = StringUtil.isNotEmpty(workSheetFolderIds) ? workSheetRepository.findByParentIdIn(workSheetFolderIds) : new ArrayList<>();
        //子工作单按照工作单id分组
        Map<String, List<DtoWorkSheet>> workSheetGroup = workSheetList.stream().collect(Collectors.groupingBy(DtoWorkSheet::getParentId));
        //工作单相关数据
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(workSheetFolderIds) ?
                analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workSheetFolderIds) : new ArrayList<>();
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findAll(sampleIds) : new ArrayList<>();
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = sampleTypeIds.size() > 0 ? sampleTypeService.findAll(sampleTypeIds) : new ArrayList<>();
        //获取所有的工作单数据
        for (DtoWorkSheetFolder workSheetFolder : workSheetFolders) {
            DtoDocument dtoDocument = new DtoDocument();
            List<DtoDocument> docOfWorkSheet = StringUtil.isNotEmpty(workSheetDocGroup) ? workSheetDocGroup.getOrDefault(workSheetFolder.getId(), new ArrayList<>()) : new ArrayList<>();
            dtoDocument.setWorkSheetCode(workSheetFolder.getWorkSheetCode());
            dtoDocument.setFolderId(workSheetFolder.getId());
            if (StringUtil.isNotEmpty(docOfWorkSheet)) {
                List<String> pathLsit = docOfWorkSheet.stream().map(DtoDocument::getPath).collect(Collectors.toList());
                //附件id
                List<String> docIds = docOfWorkSheet.stream().map(DtoDocument::getId).distinct().collect(Collectors.toList());
                //附件名称
                List<String> docNames = docOfWorkSheet.stream().map(DtoDocument::getFilename).collect(Collectors.toList());
                //附件上传人
                String uploadPerson = docOfWorkSheet.stream().map(DtoDocument::getUploadPerson).findFirst().orElse("");
                //文件类型
                String docSuffix = docOfWorkSheet.stream().map(DtoDocument::getDocSuffix).findFirst().orElse("");
                //文件大小
                List<Integer> docSizeList = docOfWorkSheet.stream().map(DtoDocument::getDocSize).collect(Collectors.toList());
                Integer docSize = 0;
                for (Integer size : docSizeList) {
                    docSize += size;
                }
                dtoDocument.setWorkSheetPathList(pathLsit);
                dtoDocument.setUploadPerson(uploadPerson);
                dtoDocument.setDocSuffix(docSuffix);
                dtoDocument.setDocSize(docSize);
                dtoDocument.setId(String.join(",", docIds));
                dtoDocument.setFilename(String.join(",", docNames));
                List<Date> uploadTimes = docOfWorkSheet.stream().map(DtoDocument::getCreateDate).collect(Collectors.toList());
                dtoDocument.setCreateDate(Collections.max(uploadTimes));
            }
            //分析项目名称
            String analyzeItemName = "";
            if (StringUtil.isNotEmpty(workSheetGroup)) {
                List<DtoWorkSheet> workSheetOfParent = workSheetGroup.getOrDefault(workSheetFolder.getId(), new ArrayList<>());
                List<String> analyzeItemNames = workSheetOfParent.stream().map(DtoWorkSheet::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
                analyzeItemName = StringUtil.isNotEmpty(analyzeItemNames) ? String.join(",", analyzeItemNames) : "";
            }
            dtoDocument.setAnalyseNames(analyzeItemName);
            //检测类型
            List<String> workSheetFolderSampleIds = analyseDataList.stream().filter(a -> dtoDocument.getFolderId().equals(a.getWorkSheetFolderId()))
                    .map(DtoAnalyseData::getSampleId).collect(Collectors.toList());
            List<String> workSheetFolderSampleTypeIds = sampleList.stream().filter(s -> workSheetFolderSampleIds.contains(s.getId()))
                    .map(DtoSample::getSampleTypeId).collect(Collectors.toList());
            String sampleTypes = sampleTypeList.stream().filter(t -> workSheetFolderSampleTypeIds.contains(t.getId()))
                    .map(DtoSampleType::getTypeName).sorted().collect(Collectors.joining("、"));
            dtoDocument.setSampleTypes(sampleTypes);
            dtoDocument.setTag(dtoDocument.getAnalyseNames());
            documents.add(dtoDocument);
        }
        return documents;
    }


    /**
     * 根据项目查询所有的工作单附件数据
     *
     * @param projectId 项目id
     * @return 工作单附件
     */
    private List<DtoDocument> findAllWorkSheetDoc(String projectId) {
        List<DtoProject2WorkSheetFolder> p2wList = project2WorkSheetFolderRepository.findByProjectId(projectId);
        //根据项目id获取所有的原始记录单id
        List<DtoDocument> workSheetRecords = findOriginalRecordDoc(projectId);
        //将原始记录单附件分组
        Map<String, List<DtoDocument>> workSheetDocGroup = workSheetRecords.stream().collect(Collectors.groupingBy(DtoDocument::getFolderId));
        //将所有的附件数据按照原始记录单id分组
        List<String> workSheetFolderIds = p2wList.stream().map(DtoProject2WorkSheetFolder::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        //获取需要返回的原始记录单附件
        return hanldeDoc(workSheetFolderIds, workSheetDocGroup);
    }

    /**
     * 根据原始记录单id集合查询所有的工作单附件数据
     *
     * @param workSheetFolderIds 原始记录单id
     * @return 工作单附件
     */
    private List<DtoDocument> findAllWorkSheetDoc(List<String> workSheetFolderIds) {
        //根据项目id获取所有的原始记录单id
        List<DtoDocument> workSheetRecords = worksheetRecordDoc(workSheetFolderIds);
        //将原始记录单附件分组
        Map<String, List<DtoDocument>> workSheetDocGroup = workSheetRecords.stream().collect(Collectors.groupingBy(DtoDocument::getFolderId));
        //获取需要返回的原始记录单附件
        return hanldeDoc(workSheetFolderIds, workSheetDocGroup);
    }

    /**
     * 获取原始记录单文档
     *
     * @param projectId 项目id
     * @return 项目文档
     */
    protected List<DtoDocument> findOriginalRecordDoc(String projectId) {
        List<DtoProject2WorkSheetFolder> p2wList = project2WorkSheetFolderRepository.findByProjectId(projectId);
        if (p2wList.size() == 0) {
            return new ArrayList<>();
        }
        List<String> workSheetFolderIds = new ArrayList<>();
        if (p2wList.size() > 1) {
            workSheetFolderIds = p2wList.stream().map(DtoProject2WorkSheetFolder::getWorkSheetFolderId).collect(Collectors.toList());
            //过滤掉为空的folderId
            workSheetFolderIds = workSheetFolderIds.stream().filter(p -> !"".equals(p) && !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
            if (StringUtil.isEmpty(workSheetFolderIds)) {
                return new ArrayList<>();
            }
        } else {
            String worksheetFolderId = p2wList.get(0).getWorkSheetFolderId();
            if ("".equals(worksheetFolderId) || UUIDHelper.GUID_EMPTY.equals(worksheetFolderId)) {
                return new ArrayList<>();
            }
            workSheetFolderIds.add(worksheetFolderId);
        }
        return worksheetRecordDoc(workSheetFolderIds);
    }

    private List<DtoDocument> worksheetRecordDoc(List<String> workSheetFolderIds) {
        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = new DocumentCriteria();
        criteria.setFolderIds(workSheetFolderIds);
        criteria.setDocTypeId(BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD);
        documentService.findByPage(pageBean, criteria);
        if (StringUtil.isNotNull(pageBean.getData())) {
            List<DtoWorkSheetFolder> workSheetFolders = workSheetFolderRepository.findAll(workSheetFolderIds);
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByWorkSheetFolderIdInAndIsDeletedFalse(workSheetFolderIds);
            pageBean.getData().sort(Comparator.comparing(DtoDocument::getCreateDate));
            //遍历数据填充工作单号和分析项目名称
            for (DtoDocument datum : pageBean.getData()) {
                Optional<DtoWorkSheetFolder> workSheetFolderOptional = workSheetFolders.parallelStream()
                        .filter(w -> w.getId().equals(datum.getFolderId()))
                        .findFirst();
                workSheetFolderOptional.ifPresent(w -> {
                    List<DtoAnalyseData> analyseDataOfFolder = analyseDataList.parallelStream()
                            .filter(a -> a.getWorkSheetFolderId().equals(w.getId()))
                            .collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(analyseDataOfFolder)) {
                        String analyseNames = analyseDataOfFolder.stream()
                                .sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName))
                                .map(DtoAnalyseData::getRedAnalyzeItemName)
                                .distinct()
                                .collect(Collectors.joining(","));
                        datum.setAnalyseNames(analyseNames);
                    }
                    datum.setWorkSheetCode(w.getWorkSheetCode());
                });
            }
            return pageBean.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 获取报告文档
     *
     * @param projectId 项目id
     * @return 项目文档
     */
    private List<DtoDocument> findReportDoc(String projectId) {
        List<DtoDocument> result = new ArrayList<>();
        List<DtoReport> reports = reportRepository.findByProjectId(projectId);
        if (reports.size() == 0) {
            return result;
        }
        PageBean<DtoDocument> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = new DocumentCriteria();
        if (reports.size() > 1) {
            criteria.setFolderIds(reports.stream().map(DtoReport::getId).collect(Collectors.toList()));
        } else {
            criteria.setFolderId(reports.get(0).getId());
        }
//        criteria.setDocTypeId(BaseCodeHelper.DOCUMENT_EXTEND_REPORT);
        criteria.setDocTypeIds(Arrays.asList(BaseCodeHelper.DOCUMENT_EXTEND_REPORT, BaseCodeHelper.DOCUMENT_GENERATE_REPORT));
        documentService.findByPage(pageBean, criteria);
        result = StringUtil.isNotNull(pageBean.getData()) ? pageBean.getData() : new ArrayList<>();
        //获取报告副本文件一并返回
        criteria.setIsTranscript(true);
        criteria.setDocTypeIds(new ArrayList<>());
        PageBean<DtoDocument> bean = new PageBean<>();
        bean.setRowsPerPage(Integer.MAX_VALUE);
        documentService.findByPage(bean, criteria);
        if (StringUtil.isNotEmpty(bean.getData())) {
            result.addAll(bean.getData());
        }
        if (StringUtil.isNotEmpty(reports)) {
            List<String> reportTypeIds = reports.parallelStream()
                    .map(DtoReport::getReportTypeId)
                    .distinct()
                    .collect(Collectors.toList());
            List<DtoSerialIdentifierConfig> serialIdentifierConfigs = new ArrayList<>();
            if (StringUtil.isNotEmpty(reportTypeIds)) {
                serialIdentifierConfigs = serialIdentifierConfigRepository.findAll(reportTypeIds);
            }
            for (DtoDocument dtoDocument : result) {
                Optional<DtoReport> optionalDtoReport = reports.stream()
                        .filter(r -> r.getId().equals(dtoDocument.getFolderId()))
                        .findFirst();
                List<DtoSerialIdentifierConfig> finalSerialIdentifierConfigs = serialIdentifierConfigs;
                optionalDtoReport.ifPresent(r -> {
                    dtoDocument.setReportCode(r.getCode());
                    dtoDocument.setReportYear(r.getReportYear());
                    Optional<DtoSerialIdentifierConfig> serialIdentifierConfig = finalSerialIdentifierConfigs.stream()
                            .filter(s -> s.getId().equals(r.getReportTypeId()))
                            .findFirst();
                    serialIdentifierConfig.ifPresent(s -> dtoDocument.setReportType(s.getConfigName()));
                });
            }
        }
        result.sort(Comparator.comparing(DtoDocument::getCreateDate));

        return result;
    }

    /**
     * 获取电子报告文档
     *
     * @param projectId 项目id
     * @return 项目文档
     */
    private List<DtoProject2Report> findElectronicReportDoc(String projectId) {
        List<DtoProject2Report> project2ReportList = project2ReportService.findForProject(projectId);
        //过滤掉未上传附件的记录,并且报告状态为已签发，发放状态为已发放的记录
        project2ReportList = project2ReportList.stream().filter(p -> p.getUploadStatus().equals(1) && p.getGrantStatus().equals(2)
                && EnumReportState.已签发.toString().equals(p.getReportStatus())).collect(Collectors.toList());
        return project2ReportList;
    }

    /**
     * 获取电子报告文档
     *
     * @param projectId 项目id
     * @return 项目文档
     */
    private List<DtoDocument> findElectronicReportDocDocument(String projectId) {
        List<DtoProject2Report> project2ReportList = findElectronicReportDoc(projectId);
        return documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(
                project2ReportList.stream().map(DtoProject2Report::getId).collect(Collectors.toList()), "BASE_DocumentExtendType_DZBG");

    }

    /**
     * 获取样品交接单
     *
     * @param projectId 项目id
     * @return 项目文档
     */
    private List<DtoDocument> findDeliverySpreadDocument(String projectId) {
        List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordRepository.findByProjectId(projectId);
        if (StringUtil.isNotEmpty(receiveSampleRecords)) {
            PageBean<DtoDocument> pageBean = new PageBean<>();
            pageBean.setRowsPerPage(Integer.MAX_VALUE);
            DocumentCriteria criteria = new DocumentCriteria();
            criteria.setFolderIds(receiveSampleRecords.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList()));
            criteria.setDocTypeIds(Arrays.asList(BaseCodeHelper.DOCUMENT_EXTEND_DELIVERY_SPREAD, "deliverySpread"));
            documentService.findByPage(pageBean, criteria);
            if (StringUtil.isNotNull(pageBean.getData())) {
                pageBean.getData().sort(Comparator.comparing(DtoDocument::getCreateDate));
                return pageBean.getData();
            }
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void sendProMessage(EnumProAction action, Object... params) {
        sendProMessageWoTransactional(action, params);
    }


    @Override
    public void sendProMessageWoTransactional(EnumProAction action, Object... params) {
        //项目id
        String projectId = params.length > 0 ? (String) params[0] : "";

        //送样单id
        String receiveId = params.length > 1 ? (String) params[1] : "";

        //分析人id
        List<String> analystIds = params.length > 2 ? (List<String>) params[2] : null;

        //复审人id
        List<String> personIds = params.length > 3 ? (List<String>) params[3] : null;

        switch (action) {
            case 方案新增点位:
            case 方案复制点位:
            case 方案删除点位:
            case 方案新增指标:
            case 方案删除指标:
            case 方案设置分包:
            case 方案新增周期次数:
            case 方案复制周期:
            case 方案删除周期:
            case 方案复制批次:
            case 方案删除批次:
            case 方案新增次数:
            case 方案复制次数:
            case 方案删除次数:
            case 方案修改指标:
            case 方案修改方法:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    schemeCacheService.checkSchemeChange(projectId, PrincipalContextUser.getPrincipal());
                    projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
                }
                break;

            case 复制项目:
            case 添加外部样品:
            case 复制外部样品:
            case 删除样品:
            case 作废样品:
            case 取消作废:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    schemeCacheService.checkSchemeChange(projectId, PrincipalContextUser.getPrincipal());
                    projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
                }

                if (StringUtils.isNotNullAndEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
                    receiveSampleRecordCacheService.updateRecordJson(receiveId, PrincipalContextUser.getPrincipal());
                }
                break;

            case 建立样品编号:
            case 清除样品编号:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
                }
                if (StringUtils.isNotNullAndEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
                    receiveSampleRecordCacheService.updateRecordJson(receiveId, PrincipalContextUser.getPrincipal());
                }
                if (StringUtil.isNotNull(analystIds)) {
                    for (String analystId : analystIds) {
                        analyseDataCacheService.saveAnalyseDataCache(analystId, PrincipalContextUser.getPrincipal().getOrgId());
                    }
                }
                if (StringUtil.isNotNull(personIds)) {
                    for (String personId : personIds) {
                        if (StringUtils.isNotNullAndEmpty(personId) && !UUIDHelper.GUID_EMPTY.equals(personId)) {
                            analyseDataCacheService.saveAuditInfo(personId, PrincipalContextUser.getPrincipal().getOrgId());
                        }
                    }
                }
                break;

            case 添加现场质控样:
            case 新建内部送样单:
            case 删除送样单:
            case 送样单选择样品:
            case 送样单剔除样品:
                if (StringUtils.isNotNullAndEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
                    receiveSampleRecordCacheService.updateRecordJson(receiveId, PrincipalContextUser.getPrincipal());
                }
                break;

            case 添加测试项目:
            case 删除测试项目:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    schemeCacheService.checkSchemeChange(projectId, PrincipalContextUser.getPrincipal());
                    projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
                }
                if (StringUtils.isNotNullAndEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
                    receiveSampleRecordCacheService.updateRecordJson(receiveId, PrincipalContextUser.getPrincipal());
                }
                if (StringUtil.isNotNull(analystIds)) {
                    for (String analystId : analystIds) {
                        analyseDataCacheService.saveAnalyseDataCache(analystId, PrincipalContextUser.getPrincipal().getOrgId());
                    }
                }
                if (StringUtil.isNotNull(personIds)) {
                    for (String personId : personIds) {
                        if (StringUtils.isNotNullAndEmpty(personId) && !UUIDHelper.GUID_EMPTY.equals(personId)) {
                            analyseDataCacheService.saveAuditInfo(personId, PrincipalContextUser.getPrincipal().getOrgId());
                        }
                    }
                }
                break;
            case 确认分包:
            case 取消分包:
            case 样品分配:
            case 更换人员:
            case 创建检测单:
            case 加入检测单:
            case 修改检测单:
            case 删除检测单:
            case 剔除检测单数据:
            case 添加室内质控样:
            case 提交检测单:
            case 确认检测单:
            case 检测单复核通过:
            case 检测单审核通过:
            case 检测单退回:
            case 分析数据退回:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
                }
                if (StringUtil.isNotNull(analystIds)) {
                    for (String analystId : analystIds) {
                        analyseDataCacheService.saveAnalyseDataCache(analystId, PrincipalContextUser.getPrincipal().getOrgId());
                    }
                }
                if (StringUtil.isNotNull(personIds)) {
                    for (String personId : personIds) {
                        if (StringUtils.isNotNullAndEmpty(personId) && !UUIDHelper.GUID_EMPTY.equals(personId)) {
                            analyseDataCacheService.saveAuditInfo(personId, PrincipalContextUser.getPrincipal().getOrgId());
                        }
                    }
                }
                break;
            case 更换方法:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    schemeCacheService.checkSchemeChange(projectId, PrincipalContextUser.getPrincipal());
                    projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
                    schemeCacheService.checkScheme(projectId, PrincipalContextUser.getPrincipal());
                }
                if (StringUtil.isNotNull(analystIds)) {
                    for (String analystId : analystIds) {
                        analyseDataCacheService.saveAnalyseDataCache(analystId, PrincipalContextUser.getPrincipal().getOrgId());
                    }
                }
                if (StringUtil.isNotNull(personIds)) {
                    for (String personId : personIds) {
                        if (StringUtils.isNotNullAndEmpty(personId) && !UUIDHelper.GUID_EMPTY.equals(personId)) {
                            analyseDataCacheService.saveAuditInfo(personId, PrincipalContextUser.getPrincipal().getOrgId());
                        }
                    }
                }
                break;

            case 新增报告:
            case 报告签发通过:
            case 报告签发退回:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    projectCacheService.updateProjectReportJson(projectId, PrincipalContextUser.getPrincipal());
                }
                break;
            case 删除报告:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    projectCacheService.updateProjectReportJson(projectId, PrincipalContextUser.getPrincipal());
                    projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
                }
            case 报告关联样品:
            case 委托现场送样单删除:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
                }
                break;

            case 状态纠正:
                if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    projectCacheService.updateProjectReportJson(projectId, PrincipalContextUser.getPrincipal());
                    projectCacheService.updateProjectSampleJson(projectId, PrincipalContextUser.getPrincipal());
                }
            default:
                break;
        }
    }

    //#region 数值修约
    @Override
    public String getDecimal(Integer mostSignificance, Integer mostDecimal, String value) {
        return reviseService.getDecimal(mostSignificance, mostDecimal, value);
    }

    @Override
    public String getDecimal(Integer mostSignificance, Integer mostDecimal, String value, Boolean isSci) {
        return reviseService.getDecimal(mostSignificance, mostDecimal, value, isSci);
    }

    @Override
    public String getDecimal(String testId, String sampleTypeId, String value) {
        return reviseService.getDecimal(testId, sampleTypeId, value);
    }

    @Override
    public String getDecimal(DtoTest dtoTest, String sampleTypeId, String value) {
        return reviseService.getDecimal(dtoTest, sampleTypeId, value);
    }

    @Override
    public String getDecimal(String testId, String value) {
        return reviseService.getDecimal(testId, value);
    }

    @Override
    public String getDecimal(DtoTest dtoTest, String value) {
        return reviseService.getDecimal(dtoTest, value);
    }

    @Override
    public String getExamValue(String testValue, String examLimitValue, String examLimitValueLess) {
        String result = testValue;
//        try {
        if ((StringUtil.isNotEmpty(testValue) && MathUtil.isNumeral(testValue))
                && (StringUtil.isNotNull(examLimitValue) && MathUtil.isNumeral(examLimitValue))) {
            if (Double.parseDouble(testValue) < Double.parseDouble(examLimitValue)) {
                //检出限为测试项目上的,如果是数字则不判断
//                if (!StringUtils.isNotNullAndEmpty(examLimitValueLess)) {
//                    result = examLimitValue + "L";
//                } else if ("小于".equals(examLimitValueLess)) {
//                    result = "<" + examLimitValue;
//                } else {
//                    result = examLimitValueLess.replace("<", "＜").replace("&lt;", "＜").replace(">", "＞");
//                }
                EnumLIM.EnumExamLimitType examLimitType = EnumLIM.EnumExamLimitType.getByCode(examLimitValueLess);
                result = "ND";
                if (StringUtil.isNotNull(examLimitType)) {
                    switch (examLimitType) {
                        case 小于DL:
                            result = "＜DL";
                            break;
                        case 检出限L:
                            result = examLimitValue + "L";
                            break;
                        case 小于检出限:
                            result = "＜" + examLimitValue;
                            break;
                        default:
                            result = "ND";
                            break;
                    }
                }
            }
        }
//        } catch (Exception ex) {
//            System.out.println(ex.getMessage());
//        }
        return result;
    }

    @Override
    public String getExamValueWoSymbol(String value, String examLimitValue) {
        if (StringUtil.isEmpty(value) || StringUtil.isEmpty(examLimitValue)) {
            return value;
        }
        BigDecimal decimalValue = new BigDecimal(value.trim());
        BigDecimal decimalExamLimitValue = new BigDecimal(examLimitValue.trim());
        //比较结果小于检出限，则返回检出限的一半
        if (decimalValue.compareTo(decimalExamLimitValue) < 0) {
            return decimalExamLimitValue.divide(new BigDecimal(2)).toString();
        } else {
            return value;
        }
    }

    @Override
    public Boolean isExamValue(String testValue, String examLimitValue) {
        Boolean isExamValue = false;// 是否检出
        try {
            //检出限为测试项目上的,如果是数字则不判断
            isExamValue = (Double.parseDouble(testValue) < Double.parseDouble(examLimitValue));
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
        return isExamValue;
    }
    //#endregion

    /**
     * 比对数据库数据与web端提交的数据变化
     *
     * @param from 数据库实体
     * @param to   web提交实体
     * @param <LT> 继承entity的dto，仅比对数据库字段
     * @param <T>  接收web数据的dto
     * @return 记录修改的字段与修改信息（记录修改前、修改后的值）的map
     */
    @Override
    public <LT, T> Map<String, Map<String, Object>> getCompare(LT from, T to) {
        try {
            Map<String, Map<String, Object>> map = new HashMap<>();
            Field[] fields = from.getClass().getSuperclass().getDeclaredFields();
            List<Field> superTargetFields = Arrays.asList(to.getClass().getSuperclass().getDeclaredFields());
            List<Field> targetFields = Arrays.asList(to.getClass().getDeclaredFields());
            for (Field field : fields) {
                Field targetField = superTargetFields.stream().filter(p -> p.getName().equals(field.getName())).findFirst().orElse(null);
                if (StringUtil.isNull(targetField)) {
                    targetField = targetFields.stream().filter(p -> p.getName().equals(field.getName())).findFirst().orElse(null);
                }
                if (StringUtil.isNotNull(targetField)) {
                    field.setAccessible(true);
                    targetField.setAccessible(true);
                    Object fromValue = field.get(from);
                    Object toValue = targetField.get(to);
                    if (field.getType().equals(Date.class)) {
                        fromValue = StringUtil.isNotNull(fromValue) ? DateUtil.dateToString((Date) fromValue, DateUtil.YEAR) : "";
                        if (fromValue.equals("1753-01-01")) {
                            fromValue = "";
                        }
                    }
                    if (targetField.getType().equals(Date.class)) {
                        toValue = StringUtil.isNotNull(toValue) ? DateUtil.dateToString((Date) toValue, DateUtil.YEAR) : "";
                        if (toValue.equals("1753-01-01")) {
                            toValue = "";
                        }
                    }
                    if ((fromValue instanceof String && ((String) fromValue).toLowerCase().equals("null")) || StringUtil.isNull(fromValue)) {
                        fromValue = "";
                    }
                    if (toValue instanceof String && ((String) toValue).toLowerCase().equals("null") || StringUtil.isNull(toValue)) {
                        toValue = "";
                    }
                    //满足改前、改后均不为null，且改前、改后存在一个为null或改前和改后的值不一样，即判定为修改过
                    if (!(StringUtil.isNull(fromValue) && StringUtil.isNull(toValue)) &&
                            (StringUtil.isNull(fromValue) || StringUtil.isNull(toValue) || !fromValue.equals(toValue))) {
                        Map<String, Object> fieldMap = new HashMap<>();
                        fieldMap.put("from", fromValue);
                        fieldMap.put("to", toValue);
                        map.put(field.getName(), fieldMap);
                    }
                }
            }
            return map;
        } catch (SecurityException | IllegalAccessException | IllegalArgumentException ex) {
            System.out.println(ex.getMessage());
            throw new BaseException("异常错误");
        }
    }

    /**
     * 判断开关是否开启
     *
     * @param switchCode 开关编码
     * @return 是否开启
     */
    @Override
    public Boolean switchIsOpen(String switchCode) {
        if (ProCodeHelper.SKIP_SAMPLE_RECEIVE.equals(switchCode)) {
            return false;
        }
        DtoCode code = codeService.findByCode(switchCode);
        return StringUtil.isNotNull(code) && code.getDictValue().equals("1");
    }

    /**
     * 核对采样准备状态
     *
     * @param project 项目
     * @param samples 样品id集合
     */
    @Transactional
    @Override
    public void checkPrepareSample(DtoProject project, List<DtoSample> samples) {
        List<String> sampleIds = samples.parallelStream().map(DtoSample::getId).collect(Collectors.toList());
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select distinct a.sampleId,a.analystId,a.isCompleteField");
        stringBuilder.append(" from DtoAnalyseData a where 1=1");
        stringBuilder.append(" and a.isDeleted = 0");
        stringBuilder.append(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        stringBuilder.append(" and a.sampleId in :sampleIds");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        values.put("sampleIds", sampleIds);
        List<Object[]> datas = comRepository.find(stringBuilder.toString(), values);

        List<DtoSampleTestType> sampleTestTypes = sampleIds.stream().map(p -> new DtoSampleTestType(p, EnumPRO.EnumSampleTestType.无指标.getValue())).collect(Collectors.toList());
        for (DtoSampleTestType stt : sampleTestTypes) {
            List<Boolean> isCompleteFieldList = datas.stream().filter(p -> p[0].equals(stt.getSampleId())).map(p -> (Boolean) p[2]).collect(Collectors.toList());
            for (Boolean isCompleteField : isCompleteFieldList) {
                if (isCompleteField) {
                    stt.setType(stt.getType() + EnumPRO.EnumSampleTestType.现场指标.getValue());
                } else {
                    stt.setType(stt.getType() + EnumPRO.EnumSampleTestType.实验室指标.getValue());
                }
            }
        }

        Map<Integer, List<DtoSampleTestType>> groupSampleTestType = sampleTestTypes.stream().collect(Collectors.groupingBy(DtoSampleTestType::getType));
        //按分组进行遍历
        List<DtoSample> temps = new ArrayList<>();
        for (Integer type : groupSampleTestType.keySet()) {
            //提取该分组下的样品
            List<String> groupSampleIds = groupSampleTestType.get(type).stream().map(DtoSampleTestType::getSampleId).collect(Collectors.toList());
            List<DtoSample> groupSamples = samples.stream().filter(p -> groupSampleIds.contains(p.getId()) && !p.getIsDeleted()).collect(Collectors.toList());
            //找到当前分组下的样品类型
            List<String> samTypeIds = groupSamples.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            //将该组的第一个样品当成模板
            if (groupSamples.size() > 0) {
                for (String samTypeId : samTypeIds) {
                    DtoSample temp = groupSamples.stream().filter(p -> samTypeId.equals(p.getSampleTypeId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(temp)) {
                        temps.add(temp);
                    }
                }
            }
        }
        checkSample(temps, project);
        List<String> tempIds = temps.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
        for (DtoSample sample : temps) {
            List<DtoSample> groupSamples = samples.stream().filter(p -> !tempIds.contains(p.getId()) &&
                    sample.getSampleTypeId().equals(p.getSampleTypeId()) && !p.getIsDeleted()).collect(Collectors.toList());
            for (DtoSample sam : groupSamples) {
                sam.setStatus(sample.getStatus());
                sam.setSamplingStatus(sample.getSamplingStatus());
                sam.setAnanlyzeStatus(sample.getAnanlyzeStatus());
                sam.setInnerReceiveStatus(sample.getInnerReceiveStatus());
                sam.setModifyDate(new Date());
                sam.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                comRepository.merge(sam);
            }
        }
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        List<String> analystIdList = datas.stream().map(p -> (String) p[1]).distinct().collect(Collectors.toList());
                        sendProMessage(EnumProAction.建立样品编号, project.getId(), "", analystIdList);
                        List<String> receiveIds = samples.stream().map(DtoSample::getReceiveId).collect(Collectors.toList());
                        for (String receiveId : receiveIds) {
                            sendProMessage(EnumProAction.建立样品编号, "", receiveId);
                        }
                    }
                }
        );
    }

    /**
     * 获取pdf合并需要的附件数据
     *
     * @param documentCriteria 附件参数
     * @return 附件集合
     */
    protected List<DtoDocument> findPdfDocuments(DocumentCriteria documentCriteria) {
        return findPdfDocuments(documentCriteria.getDocTypeId(), documentCriteria.getProjectId(), documentCriteria.getIds());
    }

    /**
     * 获取pdf合并需要的附件数据
     *
     * @param docTypeId 文档类型id
     * @param projectId 项目id
     * @return 附件集合
     */
    protected List<DtoDocument> findPdfDocuments(String docTypeId, String projectId) {
        return findPdfDocuments(docTypeId, projectId, null);
    }

    /**
     * 获取pdf合并需要的附件数据
     *
     * @param docTypeId 文档类型id
     * @param projectId 项目id
     * @param ids       文档id集合
     * @return 附件集合
     */
    protected List<DtoDocument> findPdfDocuments(String docTypeId, String projectId, List<String> ids) {
        List<DtoDocument> documents = new ArrayList<>();
        //根据对应的附件类型查询到对应的附件数据
        if (BaseCodeHelper.DOCUMENT_SAMPLE_RECORD.equals(docTypeId)) {
            Map<String, Object> samplingRecord = new HashMap<>();
            this.fillingSamplingRecordFile(Collections.singletonList(projectId), 1, samplingRecord, BaseCodeHelper.DOCUMENT_SAMPLE_RECORD);
            Set<String> keySet = samplingRecord.keySet();
            for (String key : keySet) {
                documents.addAll((List<DtoDocument>) samplingRecord.get(key));
            }
        } else if (BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD.equals(docTypeId)) {
            documents = findOriginalRecordDoc(projectId);
            //BUG2024051601412 “导出PDF”能够根据检索条件，进行导出，即导出内容同列表保持一致
            if (StringUtil.isNotEmpty(ids)) {
                documents = documents.stream().filter(d -> ids.contains(d.getId())).collect(Collectors.toList());
            }
        }
        return documents;
    }


    /**
     * 导出PDF
     *
     * @param criteria 参数容器
     * @return 文档名称
     */
    @Override
    public String downLoadPDF(BaseCriteria criteria, HttpServletResponse response) {
        DocumentCriteria documentCriteria = (DocumentCriteria) criteria;
        //返回下载数据
        return handlePDFDoc(findPdfDocuments(documentCriteria), documentCriteria.getIsRemoting(), documentCriteria.getDocTypeId(), response);
    }

    /**
     * 处理PDF合并文件
     *
     * @param documents  需要做合并的文档数据
     * @param isRemoting 是否为远程请求（决定是否删除临时文件）
     * @param docTypeId  附件类型（用于生成临时文件分类, 放在文件名称上）
     * @param response   响应体
     * @return 文件路径
     */
    protected String handlePDFDoc(List<DtoDocument> documents, Boolean isRemoting, String docTypeId, HttpServletResponse response) {
        if (!licenseService.isLicense()) {
            return "aspose版权存在问题！";
        }
        if (!licenseService.isPdfLicense()) {
            return "asposePDF版权存在问题！";
        }
        //过滤掉图片数据
        List<String> pdfFilePathList = new ArrayList<>();
        //删除路径集合
        List<String> removePathList = new ArrayList<>();
        for (DtoDocument document : documents) {
            //根路径
            String rootPath = filePathConfig.getFilePath();
            //文档路径
            String filePath = "";
            //如果文件已经是pdf则直接返回文件路径地址
            if (document.getDocSuffix().toLowerCase().contains("pdf")) {
                filePath = rootPath + document.getPath();
                pdfFilePathList.add(filePath);
                continue;
            }
            //转换pdf
            filePath = formatPdf(document);
            if (StringUtil.isNotEmpty(filePath)) {
                pdfFilePathList.add(filePath);
                //删除路径中只包含转换的临时pdf,原始文件是pdf的不删除
                removePathList.add(filePath);
            }
        }
        String downLoadName = "";
        if (StringUtil.isNotEmpty(pdfFilePathList)) {
            //根据pdf路径合并pdf
            String pdfFilePath = mergePdf(docTypeId, pdfFilePathList);
            pdfEncrypt(pdfFilePath);
            //获取文件名称
            String pdfFileName = pdfFilePath.substring(pdfFilePath.lastIndexOf("/"), pdfFilePath.length());
            if (isRemoting) {
                //删除临时文件(不删除合并的pdf路径)
                removeFiles(removePathList);
                //返回转换后的pdf文件路径
                return pdfFilePath;
            } else {
                try {
                    //下载文件
                    downLoadName = documentService.fileDownload(pdfFilePath, pdfFileName, response);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new BaseException("文件下载出错!");
                }
                //将临时的合并PDF加入删除路径
                removePathList.add(pdfFilePath);
            }
        } else {
            throw new BaseException("列表中无附件可导出!");
        }
        //删除临时文件
        removeFiles(removePathList);
        return downLoadName;
    }

    /**
     * 导出解析图谱文件zip
     *
     * @param projectId 项目id
     * @param response  响应体
     * @return 文档
     */
    @Override
    public String exportAtlasZip(String projectId, HttpServletResponse response) {

        DtoProject project = projectRepository.findOne(projectId);
        if (StringUtils.isNull(project)) {
            throw new BaseException("未找到该项目");
        }
        //获取解析图谱文件
        Map<String, Object> doc = new HashMap<>();
        Map<String, Object> originalRecord = new HashMap<>();
        originalRecord.put("resolveFile", this.findResolveFile(projectId));
        doc.put("originalRecord", originalRecord);
        if (this.checkFileContainerIsEmpty(doc)) {
            throw new BaseException("项目中无解析图谱文件");
        }
        //如果有文档，在outputPath路径中按照项目名称创建临时文件夹
        String tempOutDirectoryStr = filePathConfig.getOutputPath() + File.separator + "zip" + File.separator + project.getProjectName();
        File tempDocDirectoryFile = new File(tempOutDirectoryStr);
        if (!tempDocDirectoryFile.exists()) {
            tempDocDirectoryFile.mkdirs();
        }
        //将文件复制到outputPath中，按照map中的key名分文件夹
        doc.forEach((k, v) -> {
            //第一层遍历，根据一项一档类别分组
            if (v.getClass().getName().contains("Map")) {
                String tempGroup = tempOutDirectoryStr + File.separator;
                Map<String, Object> docGroup = (Map<String, Object>) v;
                //第二层遍历，根据每个分组中具体的步骤分组
                docGroup.forEach((groupK, groupV) -> {
                    List<DtoDocument> docs = (List<DtoDocument>) groupV;
                    for (DtoDocument document : docs) {
                        batchDownload(tempGroup, groupK, document, document.getPath());
                    }
                });
            }
        });
        response.setHeader("Content-Disposition", "attachment;Filename=" + project.getProjectName() + ".zip");
        //压缩组装完成的临时存储文件夹，完成压缩的压缩包放在流中返回，完成压缩后临时文件夹删除
        documentService.directoryToZip(tempOutDirectoryStr, response);
        return null;
    }

    @Override
    public void getPointPicDocument(PageBean<DtoDocument> page, BaseCriteria criteria) {
        DocumentCriteria documentCriteria = (DocumentCriteria) criteria;
        List<String> objectIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(documentCriteria.getProjectId())) {
            List<DtoReport> reports = reportRepository.findByProjectId(documentCriteria.getProjectId());
            List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordRepository.findByProjectId(documentCriteria.getProjectId());
            List<String> reportIds = reports.stream().map(DtoReport::getId).collect(Collectors.toList());
            List<String> receiveIds = receiveSampleRecords.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
            objectIds.addAll(reportIds);
            objectIds.addAll(receiveIds);
        } else {
            page.setSelect("select new com.sinoyd.base.dto.lims.DtoDocument(p.physicalName, p.path, p.id, p.filename, p.folderId)");

            page.setEntityName("DtoDocument p");
            List<DtoProject> projectList = projectRepository.findAll();
            if (StringUtils.isNotNullAndEmpty(documentCriteria.getStartTime())) {
                Date from = DateUtil.stringToDate(documentCriteria.getStartTime(), DateUtil.YEAR);
                projectList = projectList.stream().filter(p -> p.getInputTime().compareTo(from) >= 0).collect(Collectors.toList());
            }
            if (StringUtils.isNotNullAndEmpty(documentCriteria.getEndTime())) {
                Date to = DateUtil.stringToDate(documentCriteria.getEndTime(), DateUtil.YEAR);
                Calendar endCalendar = Calendar.getInstance();
                endCalendar.setTime(to);
                endCalendar.add(Calendar.DAY_OF_YEAR, 1);
                projectList = projectList.stream().filter(p -> p.getInputTime().compareTo(to) < 0).collect(Collectors.toList());
                List<DtoDocument> documents = documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(objectIds, documentCriteria.getDocTypeId());
                documents.removeIf(DtoDocument::getIsDeleted);
                page.setData(documents);
                page.setRowsCount(documents.size());
            }
            if (StringUtils.isNotNullAndEmpty(documentCriteria.getProjectTypeId())) {
                projectList = projectList.stream().filter(p -> p.getProjectTypeId().equals(documentCriteria.getProjectTypeId())).collect(Collectors.toList());
            }
            if (StringUtils.isNotNullAndEmpty(documentCriteria.getProjectKey())) {
                projectList = projectList.stream().filter(p -> p.getProjectName().contains(documentCriteria.getProjectKey())
                        || p.getProjectCode().contains(documentCriteria.getProjectKey())
                        || p.getInspectedEnt().contains(documentCriteria.getProjectKey())).collect(Collectors.toList());
            }
            List<String> projectIds = projectList.stream().map(DtoProject::getId).collect(Collectors.toList());
            List<DtoReport> reports = reportRepository.findByProjectIdIn(projectIds);
            List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordRepository.findByProjectIdIn(projectIds);
            List<String> reportIds = reports.stream().map(DtoReport::getId).collect(Collectors.toList());
            List<String> receiveIds = receiveSampleRecords.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
            objectIds.addAll(reportIds);
            objectIds.addAll(receiveIds);
            documentCriteria.setFolderIds(objectIds);
            comRepository.findByPage(page, documentCriteria);
            List<DtoDocument> documentList = page.getData();
            List<DtoProject> finalProjectList = projectList;
            documentList.forEach(p -> {
                Optional<DtoReport> reportOptional = reports.stream().filter(r -> r.getId().equals(p.getFolderId())).findFirst();
                Optional<DtoReceiveSampleRecord> receiveOptional = receiveSampleRecords.stream().filter(r -> r.getId().equals(p.getFolderId())).findFirst();
                List<String> proIds = new ArrayList<>();
                reportOptional.ifPresent(r -> {
                    proIds.add(r.getProjectId());
                });
                receiveOptional.ifPresent(r -> {
                    proIds.add(r.getProjectId());
                });
                Optional<DtoProject> projectOptional = finalProjectList.stream().filter(r -> proIds.contains(r.getId())).findFirst();
                //t.projectCode, t.projectName, t.inspectedEnt, t.inputTime,
                projectOptional.ifPresent(pro -> {
                    p.setProjectCode(pro.getProjectCode());
                    p.setProjectName(pro.getProjectName());
                    p.setInspectedEnt(pro.getInspectedEnt());
                    p.setInputTime(pro.getInputTime());
                });
            });
        }
    }

    /**
     * 删除文件
     *
     * @param removePathList 需要删除文件路径
     */
    private void removeFiles(List<String> removePathList) {
        for (String pathStr : removePathList) {
            File file = new File(pathStr);
            if (file.exists()) {
                try {
                    Files.delete(file.toPath());
                } catch (Exception e) {
                    log.error("删除临时文件失败：" + e.getMessage(), e);
                }

            }
        }
    }

    /**
     * 将多个pdf合并为一个pdf
     *
     * @param docTypeId   附件类型
     * @param pdfPathList 所有处理后的pdf文件路径
     * @return 合并后的pdf文件路径
     */
    private String mergePdf(String docTypeId, List<String> pdfPathList) {
        com.itextpdf.text.Document document = null;
        PdfReader reader = null;
        PdfReader mainReader = null;
        PdfCopy copy = null;
        FileOutputStream mergedFileOPS = null;
        try {
            //根据pdf路径合并pdf
            String mergedFileName = "/" + docTypeId + "_" + DateUtil.nowTime("yyyyMMddHHmmssSSS") + ".pdf";
            String mergedFilePath = filePathConfig.getFilePath() + mergedFileName;
            //以第一个文件作为载体
            mainReader = new PdfReader(pdfPathList.get(0));
            document = new com.itextpdf.text.Document(mainReader.getPageSize(1));
            //根据载体创建PDF对象
            mergedFileOPS = new FileOutputStream(mergedFilePath);
            copy = new PdfCopy(document, mergedFileOPS);
            //去掉加密
            PdfReader.unethicalreading = true;
            //打开载体
            document.open();
            //遍历文件作为页数加到载体中
            for (int i = 0; i < pdfPathList.size(); i++) {
                reader = new PdfReader(pdfPathList.get(i));
                int n = reader.getNumberOfPages();
                for (int j = 1; j <= n; j++) {
                    document.newPage();
                    PdfImportedPage page = copy.getImportedPage(reader, j);
                    copy.addPage(page);
                }
                reader.close();
            }

            //再度加密
            PdfReader.unethicalreading = false;
            return mergedFilePath;
        } catch (Exception e) {
            throw new BaseException("合并pdf失败");
        } finally {
            if (document != null) {
                document.close();
            }
            if (mainReader != null) {
                mainReader.close();
            }
            if (mergedFileOPS != null) {
                try {
                    mergedFileOPS.close();
                } catch (IOException e) {
                    log.error("pdf合并文件流关闭失败", e);
                }
            }
            if (reader != null) {
                reader.close();
            }
            if (copy != null) {
                copy.close();
            }
        }

    }

    /**
     * Word转PDF
     *
     * @param dtoDocument 文档数据
     * @return 保存后的文档路径
     */
    private String formatPdf(DtoDocument dtoDocument) {
        try {
            String pdfFilePath = "";
            if (StringUtil.isNotEmpty(dtoDocument.getDocSuffix())) {
                //根路径
                String rootPath = filePathConfig.getFilePath();
                // 日期格式
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                //获取子路径
                String subPath = dtoDocument.getPath().substring(0, dtoDocument.getPath().lastIndexOf("/"));
                String docFilePath = rootPath + dtoDocument.getPath();
                String pdfFileName = dtoDocument.getFilename().substring(0, dtoDocument.getFilename().lastIndexOf(".")) + ".pdf";
                pdfFilePath = rootPath + "/" + subPath + "/" + dateFormat.format(new Date()) + "_" + pdfFileName;
                if (dtoDocument.getDocSuffix().toLowerCase().contains("doc")) {
                    convertDocumentByDoc(docFilePath, pdfFilePath);
                } else if (dtoDocument.getDocSuffix().toLowerCase().contains("xls")) {
                    convertDocumentByExcel(docFilePath, pdfFilePath);
                } else if (dtoDocument.getDocSuffix().toLowerCase().contains("jpg") || dtoDocument.getDocSuffix().toLowerCase().contains("png")) {
                    pdfFilePath = imageToPdf(dtoDocument, rootPath);
                } else {
                    pdfFilePath = "";
                }
            }
            return pdfFilePath;
        } catch (Exception e) {
            throw new BaseException("转换PDF失败!");
        }
    }

    private void convertDocumentByDoc(String docFilePath, String pdfFilePath) throws Exception {
        com.aspose.words.Document doc = new com.aspose.words.Document(docFilePath);
        doc.save(pdfFilePath, SaveFormat.PDF);
    }

    private void convertDocumentByExcel(String docFilePath, String pdfFilePath) throws Exception {
        WorkbookDesigner designer = new WorkbookDesigner();
        designer.setWorkbook(new Workbook(docFilePath));
        designer.getWorkbook().save(pdfFilePath, com.aspose.cells.SaveFormat.PDF);
    }

    /**
     * 图片转Pdf
     *
     * @param dtoDocument 文档数据
     * @param rootPath    绝对路径
     * @return 转pdf后的路径
     */
    private String imageToPdf(DtoDocument dtoDocument, String rootPath) {
        String pdfFilePath = "";
        String docFilePath = rootPath + dtoDocument.getPath();
        // 日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        try {
            String subPath = dtoDocument.getPath().substring(0, dtoDocument.getPath().lastIndexOf("/"));
            String pdfFileName = dtoDocument.getFilename().substring(0, dtoDocument.getFilename().lastIndexOf(".")) + ".pdf";
            pdfFilePath = rootPath + "/" + subPath + "/" + dateFormat.format(new Date()) + "_" + pdfFileName;
            //新建一个word文档
            com.aspose.words.Document doc = new com.aspose.words.Document();
            DocumentBuilder docBuilder = new DocumentBuilder(doc);
            BufferedImage image = ImageIO.read(new File(docFilePath));
            docBuilder.insertImage(docFilePath, RelativeHorizontalPosition.MARGIN, 0, RelativeHorizontalPosition.MARGIN,
                    0, image.getWidth(), image.getHeight(), WrapType.INLINE);
            doc.save(pdfFilePath, SaveFormat.PDF);
            return pdfFilePath;
        } catch (Exception e) {
            throw new BaseException("图片转换PDF失败!");
        }
    }

    /**
     * pdf加密
     *
     * @param pdfFilePath 路径
     */
    private void pdfEncrypt(String pdfFilePath) {
        com.aspose.pdf.Document pdfDoc = null;
        try {
            pdfDoc = new com.aspose.pdf.Document(pdfFilePath);
            DocumentPrivilege documentPrivilege = DocumentPrivilege.getForbidAll();
            documentPrivilege.setAllowPrint(true);
            Date time1 = new Date();
            pdfDoc.encrypt("", "sinoyd**..", documentPrivilege, CryptoAlgorithm.RC4x40, false);
            Date time2 = new Date();
            log.info("======================文档加密时长:" + (time2.getTime() - time1.getTime()) + "ms====================");
            pdfDoc.save();
            Date time3 = new Date();
            log.info("======================文档第一次保存时长:" + (time3.getTime() - time2.getTime()) + "ms====================");
            pdfDoc.decrypt();
            Date time4 = new Date();
            log.info("======================文档解密时长:" + (time4.getTime() - time3.getTime()) + "ms====================");
            pdfDoc.save();
            Date time5 = new Date();
            log.info("======================文档第二次保存时长:" + (time5.getTime() - time4.getTime()) + "ms====================");
        } catch (Exception e) {
            throw new BaseException("文档加密失败！" + e.getMessage(), e);
        } finally {
            if (pdfDoc != null) {
                pdfDoc.close();
            }
        }
    }


    /**
     * 获取通知消息
     */
    private String getMsg(String msg) {
        DtoProChannelMsg dto = new DtoProChannelMsg();
        dto.setMsg(msg);
        return JsonStream.serialize(dto);
    }

    /**
     * 根据有效位数修约
     *
     * @param value            值
     * @param mostSignificance 有效位数
     * @return 返回修约值
     */
    private String revisionByMostSignificance(BigDecimal value, Integer mostSignificance) {
        String result = value.toPlainString();
        boolean isNeedRevision = false;// 整数部分和小数部分位数之和大于有效位数的
        boolean isNeedScienceRevision = false;// 是否需要修约成科学计数法
        if (value.abs().compareTo(new BigDecimal(1)) >= 0) {// 绝对值区间在[1,+∞)情况
            String valueStr = result;
            if (valueStr.contains(".")) {
                // 有小数部分
                if (valueStr.split("\\.")[0].replace("-", "").length() > mostSignificance) {
                    // 整数部分的位数比有效位数大
                    isNeedScienceRevision = true;
                } else if (valueStr.replace(".", "").replace("-", "").length() > mostSignificance) {
                    isNeedRevision = true;
                }
            } else {
                if (valueStr.length() > mostSignificance) {
                    isNeedScienceRevision = true;
                }
            }
        } else {// 值在区间(0,1)情况
            String valueStr = result.replace("0.", "").replace("-", "");
            if (valueStr.length() > mostSignificance) {
                DecimalFormat df = new DecimalFormat();
                StringBuffer style = new StringBuffer("0");
                for (int i = 0; i < mostSignificance; i++) {
                    if (i == 0) {
                        style.append(".");
                    }
                    style.append("0");
                }
                df.applyPattern(style.toString());
                result = df.format(value);
            }
        }
        if (isNeedRevision) {// 按有效位数修约
            DecimalFormat df = new DecimalFormat();
            StringBuffer style = new StringBuffer("0");
            for (int i = 0; i < mostSignificance - (result.split("\\.")[0].replace("-", "")).length(); i++) {
                if (i == 0) {
                    style.append(".");
                }
                style.append("0");
            }
            df.applyPattern(style.toString());
            result = df.format(value);
        }
        if (isNeedScienceRevision) {// 修成科学计数法
            DecimalFormat df = new DecimalFormat();
            StringBuffer style = new StringBuffer("0");
            for (int i = 0; i < mostSignificance - 1; i++) {
                if (i == 0) {
                    style.append(".");
                }
                style.append("0");
            }
            style.append("E0");
            df.applyPattern(style.toString());
            result = df.format(value);
        }
        return result;
    }
}
