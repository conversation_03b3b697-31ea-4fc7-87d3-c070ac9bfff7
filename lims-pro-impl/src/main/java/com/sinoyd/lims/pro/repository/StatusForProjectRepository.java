package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoStatusForProject;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * 项目状态数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/16
 * @since V100R001
 */
public interface StatusForProjectRepository extends IBaseJpaPhysicalDeleteRepository<DtoStatusForProject, String> {
    /**
     * 查询指定项目的状态信息
     *
     * @param projectId 项目id
     * @return 对应项目下的状态信息
     */
    List<DtoStatusForProject> findByProjectId(String projectId);

    /**
     * 查询指定项目的状态信息
     *
     * @param projectIds 项目id集合
     * @return 对应项目下的状态信息
     */
    List<DtoStatusForProject> findByProjectIdIn(List<String> projectIds);

    /**
     * 批量办结项目状态
     *
     * @param ids    项目的ids
     * @param status 状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoStatusForProject s set s.status = :status,s.modifyDate = :modifyDate,s.modifier = :modifier,s.lastNewOpinion = :lastNewOpinion where s.projectId in :ids")
    Integer finishStatusForProject(@Param("ids") List<String> ids,
                                   @Param("status") Integer status,
                                   @Param("modifier") String modifier,
                                   @Param("lastNewOpinion") String lastNewOpinion,
                                   @Param("modifyDate") Date modifyDate);

    /**
     * 按照项目id及模块修改状态
     *
     * @param id         项目id
     * @param status     状态
     * @param module     模块
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 修改条数
     */
    @Transactional
    @Modifying
    @Query("update DtoStatusForProject s set s.status = :status,s.modifyDate = :modifyDate,s.modifier = :modifier where s.projectId = :id and s.module = :module")
    Integer updateStatusByProjectIdAndModule(@Param("id") String id,
                                             @Param("status") Integer status,
                                             @Param("module") String module,
                                             @Param("modifier") String modifier,
                                             @Param("modifyDate") Date modifyDate);

    /**
     * 批量清除项目的点位
     *
     * @param projectId 项目id
     * @return 返回删除的条数
     */
    @Transactional
    Integer deleteByProjectId(String projectId);

    /**
     * 批量清除项目的点位
     *
     * @param projectIds 项目id集合
     * @return 返回删除的条数
     */
    @Transactional
    Integer deleteByProjectIdIn(List<String> projectIds);
}