package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.DtoWorkSheetCalibrationCurveDetail;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 校准曲线信息
 * <AUTHOR>
 * @version V1.0.0 2019/12/26
 * @since V100R001
 */
@Data
public class DtoWorkSheetCalibrationCurveTemp {
    /**
     * 校准曲线id
     */
    private String id = UUIDHelper.NewID();

    /**
     * 相关系数
     */
    private String coefficient;

    /**
     * 方程
     */
    private String equation;


    /**
     * 曲线类型
     */
    private Integer curveType;

    /**
     * 曲线种类
     */
    private Integer curveMode;

    /**
     * b值
     */
    private String bValue;

    /**
     * k值
     */
    private String kValue;

    /**
     * 标线id
     */
    private String standardCurveId;

    /**
     * 子检测单id
     */
    private String worksheetId;

    /**
     * 校准曲线明细
     */
    private List<DtoWorkSheetCalibrationCurveDetail> curveDetail = new ArrayList<>();
}
