package com.sinoyd.lims.api.controller;

import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Api(tags = "文件管理: 文件管理服务")
@RestController
@RequestMapping("/api/mobile/document")
public class MobileDocumentController extends BaseJpaController<DtoDocument, String, DocumentService> {
    /**
     * 分页获取文件
     *
     * @param documentCriteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询文件", notes = "分页动态条件查询文件")
    @GetMapping("")
    public RestResponse<List<DtoDocument>> findByPage(DocumentCriteria documentCriteria) {
        RestResponse<List<DtoDocument>> restResponse = new RestResponse<>();

        PageBean<DtoDocument> page = super.getPageBean();
        service.findByPage(page, documentCriteria);

        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(page.getData());
        restResponse.setCount(page.getRowsCount());
        return restResponse;

    }

    /**
     * 提供统一接口获取相应的文件路径
     *
     * @param code 编号
     * @param map  map数据参数
     * @return 返回数据
     */
    @ApiOperation(value = "获取路径", notes = "获取路径")
    @PostMapping("/{code}")
    public RestResponse<String> getDocumentPath(@PathVariable String code, @RequestBody Map<String, Object> map) throws Exception {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.getDocumentPath(code, map));
        return restResp;
    }

    @ApiOperation(value = "上传文档", notes = "上传文档")
    @PostMapping("/upload")
    public RestResponse<List<DtoDocument>> fileUpload(HttpServletRequest request) {
        RestResponse<List<DtoDocument>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.upload(request, null));
        return restResponse;
    }

    /**
     * 文件预览
     *
     * @param vo       文件预览传输对象
     * @param response 响应流
     */
    @PostMapping("/preview/{code}")
    public void preview(@PathVariable String code, @RequestBody DocumentPreviewVO vo, HttpServletResponse response) {
        service.preview(code, vo, response);
    }

    /**
     * 批量删除(假删)
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量删除文档", notes = "根据id批量删除文档")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isNotEmpty(ids) ? service.logicDeleteById(ids) : 0);
        return restResponse;
    }
}
