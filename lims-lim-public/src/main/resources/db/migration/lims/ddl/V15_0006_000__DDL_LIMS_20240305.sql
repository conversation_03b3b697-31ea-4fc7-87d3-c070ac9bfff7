CREATE TABLE tb_lim_environmentalrecord2sample (
                                       id varchar(50) NOT NULL  COMMENT 'id',
                                       environmentalRecordId varchar(50) NOT NULL  COMMENT '仪器使用记录id',
                                       sampleId varchar(50) NOT NULL  COMMENT '样品id',
                                       PRIMARY KEY (id)
) COMMENT='仪器使用记录与样品关联表';


CREATE TABLE tb_lim_environmentalrecord2test (
                                                   id varchar(50) NOT NULL  COMMENT 'id',
                                                   environmentalRecordId varchar(50) NOT NULL  COMMENT '仪器使用记录id',
                                                   testId varchar(50) NOT NULL  COMMENT '测试项目id',
                                                   PRIMARY KEY (id)
) COMMENT='仪器使用记录与测试项目关联表';

ALTER TABLE tb_pro_sample MODIFY COLUMN collectNo varchar(50) null COMMENT '采集编号';