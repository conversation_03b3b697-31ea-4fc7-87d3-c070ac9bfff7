package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.Project2ContractService;
import com.sinoyd.lims.pro.criteria.Project2ContractCriteria;
import com.sinoyd.lims.pro.dto.DtoProject2Contract;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * Project2Contract服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: Project2Contract服务")
 @RestController
 @RequestMapping("api/pro/project2Contract")
 public class Project2ContractController extends BaseJpaController<DtoProject2Contract, String,Project2ContractService> {


    /**
     * 分页动态条件查询Project2Contract
     * @param project2ContractCriteria 条件参数
     * @return RestResponse<List<Project2Contract>>
     */
     @ApiOperation(value = "分页动态条件查询Project2Contract", notes = "分页动态条件查询Project2Contract")
     @GetMapping
     public RestResponse<List<DtoProject2Contract>> findByPage(Project2ContractCriteria project2ContractCriteria) {
         PageBean<DtoProject2Contract> pageBean = super.getPageBean();
         RestResponse<List<DtoProject2Contract>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, project2ContractCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询Project2Contract
     * @param id 主键id
     * @return RestResponse<DtoProject2Contract>
     */
     @ApiOperation(value = "按主键查询Project2Contract", notes = "按主键查询Project2Contract")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoProject2Contract> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoProject2Contract> restResponse = new RestResponse<>();
         DtoProject2Contract project2Contract = service.findOne(id);
         restResponse.setData(project2Contract);
         restResponse.setRestStatus(StringUtil.isNull(project2Contract) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增Project2Contract
     * @param project2Contract 实体列表
     * @return RestResponse<DtoProject2Contract>
     */
     @ApiOperation(value = "新增Project2Contract", notes = "新增Project2Contract")
     @PostMapping
     public RestResponse<DtoProject2Contract> create(@RequestBody DtoProject2Contract project2Contract) {
         RestResponse<DtoProject2Contract> restResponse = new RestResponse<>();
         restResponse.setData(service.save(project2Contract));
         return restResponse;
      }

     /**
     * 新增Project2Contract
     * @param project2Contract 实体列表
     * @return RestResponse<DtoProject2Contract>
     */
     @ApiOperation(value = "修改Project2Contract", notes = "修改Project2Contract")
     @PutMapping
     public RestResponse<DtoProject2Contract> update(@RequestBody DtoProject2Contract project2Contract) {
         RestResponse<DtoProject2Contract> restResponse = new RestResponse<>();
         restResponse.setData(service.update(project2Contract));
         return restResponse;
      }

    /**
     * "根据id批量删除Project2Contract
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除Project2Contract", notes = "根据id批量删除Project2Contract")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }