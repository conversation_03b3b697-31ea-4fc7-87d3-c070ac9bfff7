package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.WorkSheetCalibrationCurve;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoWorkSheetCalibrationCurve实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_WorkSheetCalibrationCurve")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoWorkSheetCalibrationCurve extends WorkSheetCalibrationCurve {
    private static final long serialVersionUID = 1L;

    /**
     * 曲线的k的值
     */
    @Transient
    private String k;

    /**
     * 曲线的b的值
     */
    @Transient
    private String b;

    /**
     * 曲线的c值
     */
    @Transient
    private String c;
}