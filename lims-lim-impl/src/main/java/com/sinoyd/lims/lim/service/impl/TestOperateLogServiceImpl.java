package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTestOperateLog;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestOperateLogRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.TestOperateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * TestOperateLog操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/02/20
 * @since V100R001
 */
@Service
public class TestOperateLogServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoTestOperateLog, String, TestOperateLogRepository> implements TestOperateLogService {

    private PersonService personService;

    @Override
    public void findByPage(PageBean<DtoTestOperateLog> page, BaseCriteria criteria) {
        page.setEntityName("DtoTestOperateLog p");
        page.setSelect("select p");
        super.findByPage(page, criteria);
        List<DtoTestOperateLog> list = page.getData();
        if(!list.isEmpty()){
            loadTransientFields(list);
        }
    }

    /**
     * 填充附加属性
     * @param data 原始值
     */
    private void loadTransientFields(List<DtoTestOperateLog> data) {
        List<String> operatorIds = data.stream().map(DtoTestOperateLog::getOperatorId).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = personService.findAll(operatorIds);
        for (DtoTestOperateLog testOperateLog:data) {
            //操作人员文本
            Optional<DtoPerson> personOptional = personList.stream().filter(p-> p.getId().equals(testOperateLog.getOperatorId())).findFirst();
            personOptional.ifPresent(dtoPerson -> testOperateLog.setOperator(dtoPerson.getCName()));
            //操作类型文本
            testOperateLog.setOperateTypeText(EnumLIM.EnumTestOperateLogOperateType.getByValue(testOperateLog.getOperateType()));
        }
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }
}