package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ReportInfo实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ReportInfo")
@Data
@EntityListeners(AuditingEntityListener.class)
public class ReportBaseInfo implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ReportBaseInfo() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 报告id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("报告id")
    private String reportId;

    /**
     * 项目名称
     */
    @Column
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 系统编号
     */
    @Column
    @ApiModelProperty("系统编号")
    private String systemCode;

    /**
     * 受检单位
     */
    @Column
    @ApiModelProperty("受检单位")
    private String inspectedEnt;

    /**
     * 受检单位地址
     */
    @Column
    @ApiModelProperty("受检单位地址")
    private String inspectedAddress;

    /**
     * 委托单位
     */
    @Column
    @ApiModelProperty("委托单位")
    private String customerName;

    /**
     * 委托单位地址
     */
    @Column
    @ApiModelProperty("委托单位地址")
    private String customerAddress;

    @Column
    @ApiModelProperty("检测目的")
    private String testPurpose;

    @ColumnDefault("1")
    @ApiModelProperty("是否分析项目合并")
    private Boolean analyzeItemMerge = true;

    @Column
    @ColumnDefault("1")
    @ApiModelProperty("是否显示总称")
    private Boolean totalTest = true;

    @Column
    @ApiModelProperty("合并总称测试项目id，多个用,隔开")
    private String totalTestIds;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;

    @Column
    @ApiModelProperty("报告日期")
    private String reportDate;

    @Column
    @ApiModelProperty("技术说明备注")
    private String technicalRemark;

    @ColumnDefault("0")
    @ApiModelProperty("是否评价")
    private Boolean resultEvaluation = false;

    @Column
    @ApiModelProperty("补充总量（分析项目关系id，多个用逗号隔开）")
    private String itemRelationIds;

    @ColumnDefault("1")
    @ApiModelProperty("是否仅显示关联样品质控")
    private Boolean associateSampleQc = true;

    @Column
    @ApiModelProperty("副本备注")
    private String copyRemark;
}