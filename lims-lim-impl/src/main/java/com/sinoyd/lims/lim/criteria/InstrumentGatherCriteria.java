package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仪器接入查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentGatherCriteria extends BaseCriteria {


    /**
     * 参数key mn号码、仪器名称、规格型号、仪器编号
     */
    private String key;


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();

        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (exists(select 1 from DtoInstrument i where i.id = x.instrumentId ");
            condition.append(" and (i.instrumentName like :key or i.model like :key or i.instrumentsCode like :key or i.serialNo like :key)) ");
            condition.append(" or x.mnNumber like :key) ");
            values.put("key", "%" + this.key + "%");
        }
        return condition.toString();
    }
}