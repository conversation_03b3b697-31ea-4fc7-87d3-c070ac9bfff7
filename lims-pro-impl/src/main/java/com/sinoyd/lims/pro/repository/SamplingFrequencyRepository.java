package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * SamplingFrequency数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface SamplingFrequencyRepository extends IBaseJpaPhysicalDeleteRepository<DtoSamplingFrequency, String> {

    /**
     * 按点位周期次数查询点位频次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @return 返回对应点位周期次数的点位频次
     */
    List<DtoSamplingFrequency> findBySampleFolderIdAndPeriodCountAndTimePerPeriod(String sampleFolderId, Integer periodCount, Integer timePerPeriod);

    /**
     * 查询相同点位的频次个数
     *
     * @param sampleFolderId 点位id
     * @param id             频次id
     * @return 返回相同点位的频次个数
     */
    Integer countBySampleFolderIdAndIdNot(String sampleFolderId, String id);

    /**
     * 查询对应点位下的频次
     *
     * @param sampleFolderId 点位id
     * @return 返回对应点位下的频次
     */
    List<DtoSamplingFrequency> findBySampleFolderId(String sampleFolderId);

    /**
     * 查询对应点位集合下的频次
     *
     * @param sampleFolderIds 点位id集合
     * @return 返回对应点位集合下的频次
     */
    List<DtoSamplingFrequency> findBySampleFolderIdIn(Collection<String> sampleFolderIds);

    /**
     * 删除对应点位下的频次
     *
     * @param sampleFolderId 点位id
     * @return 删除的条数
     */
    @Transactional
    @Modifying
    @Query("delete from DtoSamplingFrequency where sampleFolderId = :sampleFolderId")
    Integer deleteBySampleFolderId(@Param("sampleFolderId") String sampleFolderId);

    @Transactional
    Integer deleteBySampleFolderIdIn(List<String> sampleFolderIds);

    /**
     * 获取点位下最大周期对应的频次
     *
     * @param sampleFolderId 点位id
     * @return 最大周期频次
     */
    List<DtoSamplingFrequency> findBySampleFolderIdOrderByPeriodCountDesc(String sampleFolderId);

    /**
     * 获取点位周期下最大次数对应的频次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @return 最大次数频次
     */
    List<DtoSamplingFrequency> findBySampleFolderIdAndPeriodCountOrderByTimePerPeriodDesc(String sampleFolderId, Integer periodCount);

    /**
     * 获取点位周期下最大样品数对应的频次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePeriod    批次
     * @return 最大样品数
     */
    List<DtoSamplingFrequency> findBySampleFolderIdAndPeriodCountAndTimePerPeriodOrderBySamplePerTimeDesc(String sampleFolderId, Integer periodCount,Integer timePeriod);

    /**
     * 查询对应点位周期下的频次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @return 返回对应点位周期下的频次
     */
    List<DtoSamplingFrequency> findBySampleFolderIdAndPeriodCount(String sampleFolderId, Integer periodCount);

    /**
     * @param id             频次id
     * @param sampleFolderId 点位id
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSamplingFrequency s set s.sampleFolderId = :sampleFolderId where s.id = :id")
    Integer updateSampleFolderId(@Param("id") String id,
                                 @Param("sampleFolderId") String sampleFolderId);

    /**
     * 根据ids批量获取
     * @param ids  点位周期标识列表
     * @return    点位周期列表
     */
    List<DtoSamplingFrequency> findAllByIdIn(List<String> ids);
}