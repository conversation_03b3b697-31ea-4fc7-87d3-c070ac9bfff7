package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.entity.ParamsConfig;

import java.util.Map;
import java.util.Set;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoParamsConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_ParamsConfig")
 @Data
 @DynamicInsert
 public  class DtoParamsConfig extends ParamsConfig{

	public DtoParamsConfig(){

	}

	public DtoParamsConfig(DtoParams p){
		this.paramsName = p.getParamName();
	}

	public DtoParamsConfig(Map<String,Object> dataMap){
		if(dataMap.get("dimensionId")!=null&& StringUtil.isNotEmpty((String) dataMap.get("dimensionId"))){
			setDimensionId((String) dataMap.get("dimensionId"));
		}
		if(dataMap.get("dimension")!=null&& StringUtil.isNotEmpty((String) dataMap.get("dimension"))){
			setDimension((String) dataMap.get("dimension"));
		}
		if(dataMap.get("mostSignificance")!=null){
			setMostSignificance((Integer) dataMap.get("mostSignificance"));
		}
		if(dataMap.get("mostDecimal")!=null){
			setMostDecimal((Integer) dataMap.get("mostDecimal"));
		}
	}

    @Transient
	private String paramName;
	
	 /**
    * 参数名称
    */
	@Transient
	private String paramsName;
	
	 /**
    * 参数值
    */
	@Transient
    private String paramsValue;

	@Transient
	private String paramTypeName;

    @Transient
    private String paramFormula;

    @Transient
    private String analyzeItemName;

	@Transient
	private Boolean isFormulaConfigured;

	@Transient
	private Set<DtoParamsConfig> anaParamsConfigList;

	/**
	 * 原始记录单表头参数配置的默认值
	 */
	@Transient
	private String paramDefaultValue;

	/**
	 * 工作单表头参数排序
	 */
	@Transient
	private int worksheetOrderNum;

	/**
	 * 分组Id
	 */
	@Transient
	private String groupId;

	/**
	 * 检测类型名称（用于数据查询）
	 */
	@Transient
	private String sampleTypeName;


	/**
	 * 检测类型排序值
	 */
	@Transient
	private Integer sampleTypeOrder;
 }