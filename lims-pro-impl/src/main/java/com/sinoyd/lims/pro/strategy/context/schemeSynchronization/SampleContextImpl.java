package com.sinoyd.lims.pro.strategy.context.schemeSynchronization;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTemp;
import com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.sample.AbsSampleStrategy;
import com.sinoyd.lims.strategy.context.schemeSynchronization.SampleContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Component
public class SampleContextImpl implements SampleContext {

    /**
     * 所有具体生成策略字典
     */
    private final Map<String, AbsSampleStrategy> sampleStrategyMap = new ConcurrentHashMap<>();

    @Autowired
    public SampleContextImpl(Map<String, AbsSampleStrategy> sampleStrategyMap) {
        this.sampleStrategyMap.putAll(sampleStrategyMap);
    }

    @Override
    public void synchronizationSample(Integer operateType, List<DtoSamplingFrequencyTemp> samplingFrequencyTempList) {
        String beanName = EnumBase.EnumSchemeSynchronization.getBeanName(operateType);
        if (!StringUtil.isNotNull(this.sampleStrategyMap.get(beanName + "Sample"))) {
            throw new BaseException("调用方法不合法");
        }
        this.sampleStrategyMap.get(beanName + "Sample").synchronizationSample(samplingFrequencyTempList);
    }
}
