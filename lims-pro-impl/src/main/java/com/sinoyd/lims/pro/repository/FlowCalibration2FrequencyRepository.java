package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoFlowCalibration2Frequency;

import java.util.Collection;
import java.util.List;

/**
 * DtoFlowCalibration2Frequency数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/15
 */
public interface FlowCalibration2FrequencyRepository extends IBaseJpaPhysicalDeleteRepository<DtoFlowCalibration2Frequency,String> {
    /**
     * 根据流量校准标识查询
     * @param flowCalibrationIds 流量校准标识
     * @return 数据
     */
    List<DtoFlowCalibration2Frequency> findByFlowCalibrationIdIn(List<String> flowCalibrationIds);

    /**
     * 根据点位标识及周期查询数量
     * @param flowCalibrationId 流量校准标识
     * @param sampleFolderId 点位标识
     * @param periodCount   周期
     * @return  数量
     */
    Integer countByFlowCalibrationIdAndSampleFolderIdAndPeriodCountAndIdNot(String flowCalibrationId, String sampleFolderId,Integer periodCount,String id);


    /**
     * 根据点位标识查询
     * @param sampleFolderIds 流量校准标识
     * @return 数据
     */
    List<DtoFlowCalibration2Frequency> findBySampleFolderIdIn(Collection<String> sampleFolderIds);
}
