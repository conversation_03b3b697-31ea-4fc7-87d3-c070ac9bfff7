package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 仪器接入实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "InstrumentGatherData")
@Data
@EntityListeners(AuditingEntityListener.class)
public class InstrumentGatherData implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();


    /**
     * 仪器接入表id
     */
    @ApiModelProperty("仪器接入表id")
    @Column(length = 50)
    private String instrumentGatherId;

    /**
     * 数据类型枚举: G:工况参数、CN2083:结果参数
     */
    @ApiModelProperty("数据类型枚举: G:工况参数、CN2083:结果参数")
    @Column(length = 10)
    @Length(message = "数据类型枚举{validation.message.length}", max = 10)
    private String dataType;

    /**
     * 上传时间
     */
    @ApiModelProperty("上传时间")
    @ColumnDefault("'1753-1-1'")
    @Column(nullable = false)
    private Date uploadTime;

}