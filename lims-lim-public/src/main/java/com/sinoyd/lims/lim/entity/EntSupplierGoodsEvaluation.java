package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * EntSupplierGoodsEvaluation实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="EntSupplierGoodsEvaluation")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class EntSupplierGoodsEvaluation implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  EntSupplierGoodsEvaluation() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 企业Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("企业Id（Guid）")
	private String entId;
    
    /**
    * 供应品Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("供应品Id（Guid）")
	private String goodsId;
    
    /**
    * 评价Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("评价Id（Guid）")
    private String evaluationId;
    
    /**
    * 最短供货时间
    */
    @Column(length=10)
    @ApiModelProperty("最短供货时间")
    @Length(message = "最短供货时间{validation.message.length}", max = 10)
    private String shortestDeliveryTime;
    
    /**
    * 最低报价
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("最低报价")
    private BigDecimal lowestPrice;
    
    /**
    * 评价
    */
    @Column(length=1000)
    @ApiModelProperty("评价")
    @Length(message = "评价{validation.message.length}", max = 1000)
    private String comment;
    
    /**
    * 试用结果
    */
    @Column(length=1000)
    @ApiModelProperty("试用结果")
    @Length(message = "试用结果{validation.message.length}", max = 1000)
    private String trialResult;
    
    /**
    * 试用评价
    */
    @Column(length=1000)
    @ApiModelProperty("试用评价")
    @Length(message = "试用评价{validation.message.length}", max = 1000)
    private String trialComment;
    
    /**
    * 试用人Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("试用人Id（Guid）")
    private String trialPerson;
    
    /**
    * 试用开始日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("试用开始日期")
    private Date trialBeginDate;
    
    /**
    * 试用时长
    */
    @ApiModelProperty("试用时长")
    @Length(message = "试用时长{validation.message.length}", max = 255)
    private String trialTimeLen;
    
    /**
    * 审核人Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("审核人Id（Guid）")
    private String auditChecker;
    
    /**
    * 审核日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("审核日期")
    private Date auditData;
    
    /**
    * 主营产品
    */
    @Column(length=1000)
    @ApiModelProperty("主营产品")
    @Length(message = "主营产品{validation.message.length}", max = 1000)
    private String serviceContent;
    
    /**
    * 简介
    */
    @Column(length=1000)
    @ApiModelProperty("简介")
    @Length(message = "简介{validation.message.length}", max = 1000)
    private String info;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }