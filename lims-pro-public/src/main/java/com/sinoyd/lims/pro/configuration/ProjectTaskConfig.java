package com.sinoyd.lims.pro.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 *  首页业务流程的任务配置
 * <AUTHOR>
 * @version V1.0.0 2020/03/11
 * @since V100R001
 */
@Component
@ConfigurationProperties(prefix="projectTask")
@Data
public class ProjectTaskConfig {

    /**
     * 配置的相应的模块信息
     */
    private List<ProjectModule> projectModules = new ArrayList<>();
}
