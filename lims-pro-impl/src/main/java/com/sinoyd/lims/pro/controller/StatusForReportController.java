package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.StatusForReportService;
import com.sinoyd.lims.pro.criteria.StatusForReportCriteria;
import com.sinoyd.lims.pro.dto.DtoStatusForReport;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * StatusForReport服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
 @Api(tags = "示例: StatusForReport服务")
 @RestController
 @RequestMapping("api/pro/statusForReport")
 public class StatusForReportController extends BaseJpaController<DtoStatusForReport, String,StatusForReportService> {


    /**
     * 分页动态条件查询StatusForReport
     * @param statusForReportCriteria 条件参数
     * @return RestResponse<List<StatusForReport>>
     */
     @ApiOperation(value = "分页动态条件查询StatusForReport", notes = "分页动态条件查询StatusForReport")
     @GetMapping
     public RestResponse<List<DtoStatusForReport>> findByPage(StatusForReportCriteria statusForReportCriteria) {
         PageBean<DtoStatusForReport> pageBean = super.getPageBean();
         RestResponse<List<DtoStatusForReport>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, statusForReportCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询StatusForReport
     * @param id 主键id
     * @return RestResponse<DtoStatusForReport>
     */
     @ApiOperation(value = "按主键查询StatusForReport", notes = "按主键查询StatusForReport")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoStatusForReport> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoStatusForReport> restResponse = new RestResponse<>();
         DtoStatusForReport statusForReport = service.findOne(id);
         restResponse.setData(statusForReport);
         restResponse.setRestStatus(StringUtil.isNull(statusForReport) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增StatusForReport
     * @param statusForReport 实体列表
     * @return RestResponse<DtoStatusForReport>
     */
     @ApiOperation(value = "新增StatusForReport", notes = "新增StatusForReport")
     @PostMapping
     public RestResponse<DtoStatusForReport> create(@RequestBody DtoStatusForReport statusForReport) {
         RestResponse<DtoStatusForReport> restResponse = new RestResponse<>();
         restResponse.setData(service.save(statusForReport));
         return restResponse;
      }

     /**
     * 新增StatusForReport
     * @param statusForReport 实体列表
     * @return RestResponse<DtoStatusForReport>
     */
     @ApiOperation(value = "修改StatusForReport", notes = "修改StatusForReport")
     @PutMapping
     public RestResponse<DtoStatusForReport> update(@RequestBody DtoStatusForReport statusForReport) {
         RestResponse<DtoStatusForReport> restResponse = new RestResponse<>();
         restResponse.setData(service.update(statusForReport));
         return restResponse;
      }

    /**
     * "根据id批量删除StatusForReport
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除StatusForReport", notes = "根据id批量删除StatusForReport")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }