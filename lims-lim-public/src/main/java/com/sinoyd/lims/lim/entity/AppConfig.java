package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * app应用配置实体
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/1/12
 **/
@MappedSuperclass
@ApiModel(description="AppConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class AppConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public AppConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 应用名称
     */
    @Column(length=100,nullable = false)
    @ApiModelProperty("应用名称")
    @Length(message = "应用名称{validation.message.length}", max = 100)
    private String name;

    /**
     * 应用编码
     */
    @Column(length=50,nullable = false)
    @ApiModelProperty("应用编码")
    @Length(message = "应用编码{validation.message.length}", max = 50)
    private String code;

    /**
     * 链接地址
     */
    @Column(length=1000)
    @ApiModelProperty("链接地址")
    @Length(message = "链接地址{validation.message.length}", max = 1000)
    private String linkAddress;

    /**
     * 角色
     */
    @Column(length=1000)
    @ApiModelProperty("角色")
    private String roleId;

    /**
     * 启用状态
     */
    @Column
    @ColumnDefault("1")
    @ApiModelProperty("启用状态")
    private Boolean status = true;

    /**
     * 排序值
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum = 0;

    /**
     * 备注
     */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 组织机构id
     */
    @Column(length=50,nullable = false)
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length=50,nullable = false)
    @ApiModelProperty("创建人")
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ApiModelProperty("创建时间")
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable = false)
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length=50,nullable = false)
    @ApiModelProperty("修改人")
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ApiModelProperty("修改时间")
    @LastModifiedDate
    private Date modifyDate;

    /**
     * 应用分类
     */
    @Column(length=50)
    @ApiModelProperty("应用分类")
    @Length(message = "应用分类{validation.message.length}", max = 50)
    private String type;

    /**
     * 类型排序值
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("类型排序值")
    private Integer typeOrderNum ;
}
