package com.sinoyd.lims.monitor.strategy.pollutantDischargePermit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.common.http.HTTPCaller;
import com.sinoyd.common.http.NameValuePair;
import com.sinoyd.common.utils.MD5Util;
import io.swagger.models.HttpMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 排污许可证数据请求基类
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/23
 * @since V100R001
 */
@Component
@Slf4j
public abstract class AbstractPDPRequest {

    protected RedisTemplate<String, String> redisTemplate;

    /**
     * 获取请求后的html数据
     *
     * @param httpMethod 请求方式
     * @param paramList  请求参数
     * @return html数据
     */
    public String getHtml(String httpMethod, List<NameValuePair> paramList) {
        //请求数据并获取结果
        String response = doRequest(httpMethod, paramList);
        //返回请求结果
        JSONObject jsonObject = verifyResponse(httpMethod, response, paramList);
        //返回数据
        return handleResponse(jsonObject);
    }


    /**
     * 请求数据并获取结果
     *
     * @param httpMethod 请求方式
     * @param paramList  请求参数
     * @return 请求结果
     */
    public String doRequest(String httpMethod, List<NameValuePair> paramList) {
        String response;
        //设置请求参数中的请求类型
        setRequestType(paramList);
        //请求数据并获取结果
        List<NameValuePair> headerList = initHeader();
        if (HttpMethod.GET.name().equals(httpMethod)) {
            response = HTTPCaller.getInstance(true).getJsonString(getGateUrl(), getUrl(), headerList, paramList);
        } else if (HttpMethod.POST.name().equals(httpMethod)) {
            response = HTTPCaller.getInstance(true).postAsString(getGateUrl(), getUrl(), headerList, paramList);
        } else {
            throw new BaseException(String.format("暂不支持此方式的请求[%s]", httpMethod));
        }
        return response;
    }

    /**
     * 设置请求参数中的请求类型（用于获取数据类型）
     *
     * @param paramList 请求参数
     */
    protected abstract void setRequestType(List<NameValuePair> paramList);

    /**
     * 处理返回结果
     *
     * @param jsonObject 请求结果
     * @return 处理后数据
     */
    public abstract String handleResponse(JSONObject jsonObject);

    /**
     * 获取请求网关地址
     *
     * @return 请求网关地址
     */
    public abstract String getGateUrl();

    /**
     * 获取请求地址
     *
     * @return 请求地址
     */
    protected abstract String getUrl();

    /**
     * 获取ddc-token的appId
     *
     * @return appId
     */
    protected abstract String getAppId();

    /**
     * 获取ddc-token的app秘钥
     *
     * @return app秘钥
     */
    protected abstract String getAppSecret();

    /**
     * 获取redis缓存key
     *
     * @return redis缓存key
     */
    protected abstract String getRedisKey();

    /**
     * 校验返回结果
     *
     * @param httpMethod       请求方式
     * @param response         请求返回结果
     * @return 转化后的JsonObj
     */
    protected JSONObject verifyResponse(String httpMethod, String response, List<NameValuePair> paramList) {
        JSONObject jsonObject = JSON.parseObject(response);
        String message = jsonObject.getString("message");
        if (jsonObject.getInteger("code") == 400) {
            log.error("数据获取失败, 原因：{}", message);
        }
        if (jsonObject.getInteger("code") == 428) {
            log.error("数据尚未获取, {}", message);
        }
        if (jsonObject.getInteger("code") == 500) {
            //如果显示token过期，则重试请求并重新校验，并将token刷新到缓存中
            if ("ddc-token已过期".equals(message)) {
                saveRedisToken(generateDdcToken());
                //重新请求并重新校验
                return verifyResponse(httpMethod, doRequest(httpMethod, paramList), paramList);
            }
            log.error("远程获取数据失败，原因：{}", "远程获取数据服务器错误：" + message);
        }
        return jsonObject;
    }

    /**
     * 初始化请求头参数
     *
     * @return 请求头参数
     */
    protected abstract List<NameValuePair> initHeader();

    /**
     * 获取ddc_token
     *
     * @return ddc_token
     */
    protected String getDDCToken() {
        String ddcToken = redisTemplate.opsForValue().get(getRedisKey());
        if (ddcToken == null) {
            ddcToken = generateDdcToken();
        }
        saveRedisToken(ddcToken);
        //解密ddc-token
        return ddcToken;
    }

    /**
     * tokne存入缓存
     *
     * @param ddcToken ddc令牌
     */
    protected void saveRedisToken(String ddcToken) {
        // 将ddc_token放入缓存, 有效期10分钟
        redisTemplate.opsForValue().set(getRedisKey(), ddcToken, 10, TimeUnit.MINUTES);
    }

    /**
     * 生成DDC令牌
     *
     * @return 生成的DDC令牌
     */
    protected String generateDdcToken() {
        String a = getAppId() + "-" + System.currentTimeMillis() / 1000;
        String b = "IiI=";
        String c = MD5Util.encode(a + "." + b + "." + getAppSecret());
        return a + "." + b + "." + c;
    }

    @Autowired
    public final void setRedisTemplate(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
