package com.sinoyd.base.dto.customer;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 复数主键\外键的结构体 可扩展字段
 * <AUTHOR>
 * @version V1.0.0 2019/11/14
 * @since V100R001
 */
@Data
public class DtoComplexQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键\外键 的集合
     */
    private List<String> ids;

    /**
     * 类别
     */
    private String type;

    /**
     * 意见
     */
    private String opinion;

     /**
     * 次数
     */
    private Integer times;
}
