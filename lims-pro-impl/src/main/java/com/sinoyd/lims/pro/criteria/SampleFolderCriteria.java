package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.util.UUIDHelper;



/**
 * 点位查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年11月11日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleFolderCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 项目id
    */
    private String projectId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 按分析项目排序
     */
    private Boolean sortByItem = false;

    /**
     * 是否项目登记
     */
    private Boolean isProject = false;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.projectId) && !UUIDHelper.GUID_EMPTY.equals(this.projectId)) {
            condition.append(" and s.projectId = :projectId");
            values.put("projectId", this.projectId);
        }
        if (StringUtil.isNotEmpty(this.sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.sampleTypeId)) {
            condition.append(" and s.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        if (StringUtil.isNotEmpty(this.receiveId) && !UUIDHelper.GUID_EMPTY.equals(this.receiveId)) {
            condition.append(" and exists(select 1 from DtoSample sam where sam.isDeleted = 0 and s.id = sam.sampleFolderId and sam.receiveId = :receiveId)");
            values.put("receiveId", this.receiveId);
        }
        return condition.toString();
    }
}