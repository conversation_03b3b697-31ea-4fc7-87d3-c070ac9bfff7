-- 国家标准方法
CREATE TABLE TB_LIM_StandardMethod
(
    id              varchar(50) NOT NULL COMMENT 'id',
    methodId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '方法id',
    itemName        varchar(255)         DEFAULT NULL COMMENT '项目名称',
    sampleType      varchar(255)         DEFAULT NULL COMMENT '监测类别（检测类型）',
    countryStandard varchar(255)         DEFAULT NULL COMMENT '国家标准编号',
    methodName      varchar(255)         DEFAULT '' COMMENT '标准名称',
    orgId           varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='国家标准方法';

-- 标准方法配对LIMS
CREATE TABLE TB_LIM_StandardMethodDetail
(
    id       varchar(50) NOT NULL COMMENT 'id',
    methodId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '方法id',
    objectId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '关联id（测试项目、分析方法）',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='国家标准方法关联lims';