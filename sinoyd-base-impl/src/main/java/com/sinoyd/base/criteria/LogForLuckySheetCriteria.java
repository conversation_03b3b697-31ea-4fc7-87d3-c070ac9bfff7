package com.sinoyd.base.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * lcukySheet操作记录查询条件
 * <AUTHOR>
 * @version V1.0.0 2022/10/24
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogForLuckySheetCriteria extends BaseCriteria {

    /**
     * 对象id
     */
    private String objectId;

    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.objectId) &&
                !UUIDHelper.GUID_EMPTY.equals(this.objectId)) {
            condition.append(" and objectId = :objectId");
            values.put("objectId", this.objectId);
        }
        return condition.toString();
    }

}
