package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ELogType;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.LogForOrderFormCriteria;
import com.sinoyd.lims.pro.dto.DtoLogForOrderForm;
import com.sinoyd.lims.pro.service.LogForOrderFormService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 日志数据管理
 * @author: swj
 * @date: 2023-11-22
 */
@Validated
@RestController
@RequestMapping("api/pro/logForOrderForm")
public class LogForOrderFormController extends BaseJpaController<DtoLogForOrderForm, String, LogForOrderFormService> {

    /**
     * 日志列表
     *
     * @param criteria 查询条件
     * @return 实体
     */
    @GetMapping
    @BusinessLog(logType = ELogType.ACCESS, moduleName = "日志列表", actionName = "日志列表")
    public RestResponse<List<DtoLogForOrderForm>> findByPage(LogForOrderFormCriteria criteria) {
        RestResponse<List<DtoLogForOrderForm>> restResponse = new RestResponse<>();
        PageBean<DtoLogForOrderForm> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResponse.setCount(page.getRowsCount());
        restResponse.setData(page.getData());
        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

}
