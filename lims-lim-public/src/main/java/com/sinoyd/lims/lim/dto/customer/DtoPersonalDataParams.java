package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.lims.lim.dto.rcc.DtoParams2ParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *  个性化的数据参数
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
@Data
public class DtoPersonalDataParams {

    /**
     * 记录单Id
     */
    private String recordId;


    /**
     * 参数配置id
     */
    private String paramsConfigId;

    /**
     * 冗余的是公式上的id
     */
    private String objectId;

    /**
     * 参数id
     */
    private String paramsId;

    /**
     * 公式
     */
    private String formula;

    /**
     * 别名
     */
    private String alias;

    /**
     * 是否使用
     */
    private Boolean isUse = false;

    /**
     * 量纲
     */
    private String dimension;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 有效位
     */
    private Integer mostSignificance;


    /**
     * 小数位
     */
    private Integer mostDecimal;

    /**
     * 部分公式
     */
    private List<DtoParamsPartFormula> paramsPartFormulas=new ArrayList<>();

    /**
     * 设置参数配置
     *
     * @param dtoParams2ParamsFormula          记录单参数
     * @param dtoPersonalDataParams 个性化配置
     */
    public void setDtoParams2ParamsFormula(DtoParams2ParamsFormula dtoParams2ParamsFormula, DtoPersonalDataParams dtoPersonalDataParams) {
        dtoParams2ParamsFormula.setRecordId(dtoPersonalDataParams.getRecordId());
        dtoParams2ParamsFormula.setParamsConfigId(dtoPersonalDataParams.getParamsConfigId());
        dtoParams2ParamsFormula.setFormula(dtoPersonalDataParams.getFormula());
        dtoParams2ParamsFormula.setObjectId(dtoPersonalDataParams.getObjectId());
    }

    /**
     * 设置参数配置
     *
     * @param paramsConfig          参数配置
     * @param dtoPersonalDataParams 个性化配置
     */
    public void setDtoParamsConfig(DtoParamsConfig paramsConfig, DtoPersonalDataParams dtoPersonalDataParams) {
        paramsConfig.setMostDecimal(dtoPersonalDataParams.getMostDecimal());
        paramsConfig.setMostSignificance(dtoPersonalDataParams.getMostSignificance());
        paramsConfig.setDimension(dtoPersonalDataParams.getDimension());
        paramsConfig.setDimensionId(dtoPersonalDataParams.getDimensionId());
        paramsConfig.setParamsId(dtoPersonalDataParams.getParamsId());
        paramsConfig.setAlias(dtoPersonalDataParams.getAlias());
    }
}
