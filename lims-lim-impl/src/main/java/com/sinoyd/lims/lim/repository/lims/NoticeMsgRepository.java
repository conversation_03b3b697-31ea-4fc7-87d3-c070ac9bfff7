package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoNoticeMsg;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * NoticeMsg数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/22
 * @since V100R001
 */
public interface NoticeMsgRepository extends IBaseJpaPhysicalDeleteRepository<DtoNoticeMsg, String> {

    /**
     * 根据noticeIds获取相关
     *
     * @param noticeIds 公告id数组
     * @return DtoNoticeMsg
     */
    @Query("select p from DtoNoticeMsg p where p.noticeId in :noticeIds")
    List<DtoNoticeMsg> getByNoticeIds(@Param("noticeIds") List<String> noticeIds);
}