package com.sinoyd.base.dto.vo;

import com.sinoyd.base.annotations.HtmlTableHead;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Html解析自行检测数据行VO
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/21
 * @since V100R001
 */
@Data
@Accessors(chain = true)
public class HtmlMonitorDataVO {

    /**
     * 匹配的测试项目id
     */
    private String testId;

    /**
     * 匹配测试项目监测周期
     */
    private String projectInterval;

    /**
     * 匹配的测试项目批次
     */
    private Integer batchCount;

    /**
     * 匹配的测试项目样次
     */
    private Integer sampleBatchCount;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型大类Id
     */
    private String bigSampleTypeId;

    /**
     * 本系统匹配检测类型名称
     */
    private String sampleTypeName;

    /**
     * 污染源类别（检测类型）
     */
    @HtmlTableHead("污染源类别")
    private String sampleType;


    /**
     * 排放口编号（点位编号）
     */
    @HtmlTableHead({"排放口编号", "编号"})
    private String pointCode;

    /**
     * 排放口名称（点位名称）
     */
    @HtmlTableHead({"排放口名称", "名称"})
    private String pointName;

    /**
     * 监测内容
     */
    @HtmlTableHead("监测内容")
    private String monitorContent;

    /**
     * 污染物名称（分析项目名称）
     */
    @HtmlTableHead("污染物名称")
    private String analyzeItemName;

    /**
     * 监测方式
     */
    @HtmlTableHead("监测设施")
    private String monitorMethod;

    /**
     * 是否自动联网
     */
    @HtmlTableHead("自动监测是否联网")
    private Boolean isAutoOnline;

    /**
     * 自动监测仪器名称
     */
    @HtmlTableHead("自动监测仪器名称")
    private String instrumentName;

    /**
     * 自动监测设施安装位置
     */
    @HtmlTableHead("自动监测设施安装位置")
    private String insSite;

    /**
     * 自动监测设施是否符合安装、运行、维护等管理要求
     */
    @HtmlTableHead("自动监测设施是否符合安装、运行、维护等管理要求")
    private Boolean isAccordingSafe;


    /**
     * 手工监测采样方法及个数
     */
    @HtmlTableHead("手工监测采样方法及个数")
    private String samplingMethodAndCount;

    /**
     * 手工监测频次（监测频次）
     */
    @HtmlTableHead("手工监测频次")
    private String monitoringFrequency;

    /**
     * 手工测定方法（分析方法）
     */
    @HtmlTableHead("手工测定方法")
    private String analyzeMethodName;

    /**
     * 其他信息
     */
    @HtmlTableHead("其他信息")
    private String otherContent;
}
