package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 曲线校核样
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/14
 */
public class CurveCheck extends QualityControlKind {

    @Override
    public Integer qcTypeValue() {
        return 64;
    }

    @Override
    public String qcTypeName() {
        return "曲线校核";
    }

    @Override
    public String getSampleProperty(Integer qcGrade) {
        return "曲线校核样";
    }

    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了曲线校核样qcCode，原样为oldCode。";
    }

    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        return "曲线校核样";
    }

    @Override
    public DtoQualityConfig getQualityConfig() {
        return getConfig("CurveCheck");
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return getConfig("CurveCheck", orderReviseVOList);
    }

    /**
     * 计算偏差
     *
     * @param testValue 检测结果
     * @param qcValue   加入量
     * @return 偏差百分比
     */
    public BigDecimal calculateDeviation(BigDecimal testValue, BigDecimal qcValue) {
        return testValue.subtract(qcValue).divide(qcValue, 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}