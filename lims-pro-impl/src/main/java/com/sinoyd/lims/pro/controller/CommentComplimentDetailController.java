package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.CommentComplimentDetailService;
import com.sinoyd.lims.pro.criteria.CommentComplimentDetailCriteria;
import com.sinoyd.lims.pro.dto.DtoCommentComplimentDetail;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * CommentComplimentDetail服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: CommentComplimentDetail服务")
 @RestController
 @RequestMapping("api/pro/commentComplimentDetail")
 public class CommentComplimentDetailController extends BaseJpaController<DtoCommentComplimentDetail, String,CommentComplimentDetailService> {


    /**
     * 分页动态条件查询CommentComplimentDetail
     * @param commentComplimentDetailCriteria 条件参数
     * @return RestResponse<List<CommentComplimentDetail>>
     */
     @ApiOperation(value = "分页动态条件查询CommentComplimentDetail", notes = "分页动态条件查询CommentComplimentDetail")
     @GetMapping
     public RestResponse<List<DtoCommentComplimentDetail>> findByPage(CommentComplimentDetailCriteria commentComplimentDetailCriteria) {
         PageBean<DtoCommentComplimentDetail> pageBean = super.getPageBean();
         RestResponse<List<DtoCommentComplimentDetail>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, commentComplimentDetailCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询CommentComplimentDetail
     * @param id 主键id
     * @return RestResponse<DtoCommentComplimentDetail>
     */
     @ApiOperation(value = "按主键查询CommentComplimentDetail", notes = "按主键查询CommentComplimentDetail")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoCommentComplimentDetail> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoCommentComplimentDetail> restResponse = new RestResponse<>();
         DtoCommentComplimentDetail commentComplimentDetail = service.findOne(id);
         restResponse.setData(commentComplimentDetail);
         restResponse.setRestStatus(StringUtil.isNull(commentComplimentDetail) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增CommentComplimentDetail
     * @param commentComplimentDetail 实体列表
     * @return RestResponse<DtoCommentComplimentDetail>
     */
     @ApiOperation(value = "新增CommentComplimentDetail", notes = "新增CommentComplimentDetail")
     @PostMapping
     public RestResponse<DtoCommentComplimentDetail> create(@RequestBody @Validated DtoCommentComplimentDetail commentComplimentDetail) {
         RestResponse<DtoCommentComplimentDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.save(commentComplimentDetail));
         return restResponse;
      }

     /**
     * 新增CommentComplimentDetail
     * @param commentComplimentDetail 实体列表
     * @return RestResponse<DtoCommentComplimentDetail>
     */
     @ApiOperation(value = "修改CommentComplimentDetail", notes = "修改CommentComplimentDetail")
     @PutMapping
     public RestResponse<DtoCommentComplimentDetail> update(@RequestBody @Validated DtoCommentComplimentDetail commentComplimentDetail) {
         RestResponse<DtoCommentComplimentDetail> restResponse = new RestResponse<>();
         restResponse.setData(service.update(commentComplimentDetail));
         return restResponse;
      }

    /**
     * "根据id批量删除CommentComplimentDetail
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除CommentComplimentDetail", notes = "根据id批量删除CommentComplimentDetail")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }