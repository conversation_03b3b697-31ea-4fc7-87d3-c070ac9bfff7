package com.sinoyd.lims.pro.service.redis;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.lims.DtoContract;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProject2Contract;
import com.sinoyd.lims.pro.dto.customer.DtoProjectJson;
import com.sinoyd.lims.pro.repository.Project2ContractRepository;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目合同接收通道
 */
@Component
@Service
public class ProjectContractChannel {
    @Autowired
    private Project2ContractRepository project2ContractRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Async
    @Transactional
    public void updateProjectContractJson(String contractJson) {
        TypeLiteral<DtoContract> typeLiteral = new TypeLiteral<DtoContract>() {
        };

        if (StringUtils.isNotNullAndEmpty(contractJson)) {
            DtoContract dtoContract = JsonIterator.deserialize(JsonIterator.deserialize(contractJson).toString(), typeLiteral);

            if (StringUtil.isNotNull(dtoContract)) {
                if (dtoContract.getType().equals(LimCodeHelper.CollectionContract)) {//收款合同才进行更新
                    CurrentPrincipalUser currentPrincipalUser = new CurrentPrincipalUser();
                    currentPrincipalUser.setOrgId(dtoContract.getOrgId());
                    UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(currentPrincipalUser, null);
                    SecurityContext ctx = SecurityContextHolder.getContext();
                    ctx.setAuthentication(token);

                    List<DtoProject2Contract> p2cList = project2ContractRepository.findByContractId(dtoContract.getId());
                    if (StringUtil.isNotNull(p2cList) && p2cList.size() > 0) {
                        List<String> projectIdList = p2cList.stream().map(DtoProject2Contract::getProjectId).collect(Collectors.toList());
                        List<DtoProject> projectList = projectRepository.findAll(projectIdList);
                        for (DtoProject project : projectList) {
                            DtoProjectJson projectJson = JsonIterator.deserialize(project.getJson(), DtoProjectJson.class);

                            projectJson.setContractCode(dtoContract.getIsDeleted() ? "" : dtoContract.getContractCode());
                            projectJson.setCollectionStatus(dtoContract.getIsDeleted() ? null : dtoContract.getCollectionStatus());
                            //依次为已收款，坏账，总额
                            projectJson.setCollectionDetail(dtoContract.getIsDeleted() ? null : new BigDecimal[]{dtoContract.getArrivalAmount(), dtoContract.getBadAmount(), dtoContract.getTotalAmount()});

                            String json = "";
                            try {
                                ObjectMapper objectMapper = new ObjectMapper();
                                json = objectMapper.writeValueAsString(projectJson);
                            } catch (Exception ex) {
                                System.out.println(ex.getMessage());
                            }
                            projectRepository.updateJson(project.getId(), json);
                        }

                        if (dtoContract.getIsDeleted()) {//若合同已被清除，则进行关联清除
                            project2ContractRepository.deleteByContractId(dtoContract.getId());
                        }
                    }
                }
            }
        }
    }
}
