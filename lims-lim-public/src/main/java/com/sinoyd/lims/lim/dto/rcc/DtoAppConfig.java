package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.AppConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * app应用配置传输类
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/1/12
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Lim_AppConfig")
@Data
public class DtoAppConfig extends AppConfig {

    /**
     * 代办数量
     */
    @Transient
    private long agentCount;

    /**
     * 显示图片地址
     */
    @Transient
    private String iconPath;
}
