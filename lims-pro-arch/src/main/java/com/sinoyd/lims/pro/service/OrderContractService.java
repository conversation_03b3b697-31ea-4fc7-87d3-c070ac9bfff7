package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoOrderContract;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *  合同管理service
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2022/12/30
 */
public interface OrderContractService extends IBaseJpaService<DtoOrderContract,String> {

    /**
     * 获取附件上传路径
     *
     * @param id 主键
     * @return DtoOrderContract
     */
    DtoOrderContract findAttachPath(String id);

    /**
     * 编号配置自动生成合同编号
     *
     * @return
     */
    String generateOrderContractCode();

    /**
     * 根据订单ids获取合并信息
     *
     * @param orderIds 订单ids
     * @return 合并集合
     */
    List<DtoOrderContract> findByOrderIds(Collection<String> orderIds);
}
