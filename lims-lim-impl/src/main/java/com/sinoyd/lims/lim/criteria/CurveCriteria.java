package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * Curve查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CurveCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 检测大类id
     */
    private String sampleTypeId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 配置人员id
     */
    private String configPersonId;

    @Override
    public String getCondition() {
        /**
         * 清除条件数据
         */
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.testId = t.id");
        condition.append(" and t.sampleTypeId = st.id");

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.configDate >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and a.configDate < :endTime");
            values.put("endTime", to);
        }

        if (StringUtil.isNotEmpty(this.testId) && !UUIDHelper.GUID_EMPTY.equals(this.testId)) {
            condition.append(" and a.testId = :testId");
            values.put("testId", this.testId);
        }

        if (StringUtil.isNotEmpty(this.configPersonId) && !UUIDHelper.GUID_EMPTY.equals(this.configPersonId)) {
            condition.append(" and a.configPersonId = :configPersonId");
            values.put("configPersonId", this.configPersonId);
        }

        if (StringUtil.isNotEmpty(this.sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.sampleTypeId)) {
            condition.append(" and t.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        return condition.toString();
    }
}