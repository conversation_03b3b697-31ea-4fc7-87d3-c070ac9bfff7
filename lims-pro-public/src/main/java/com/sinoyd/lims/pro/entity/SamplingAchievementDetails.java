package com.sinoyd.lims.pro.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * SamplingAchievementDetails实体
 * <AUTHOR>
 * @version V1.0.0 2023/3/13
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="SamplingAchievementDetails")
@Data
public class SamplingAchievementDetails implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 人员绩效id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("人员绩效id")
    private String achievementId;

    /**
     * 样品id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("样品id")
    private String sampleId;

    /**
     * 样品编号
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("样品编号")
    @Excel(name = "样品编号",needMerge = true,orderNum = "20",width = 11)
    @Length(message = "样品编号{validation.message.length}", max = 50)
    private String sampleCode;

    /**
     * 点位id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("点位id")
    private String sampleFolderId;

    /**
     * 点位名称
     */
    @Column(nullable=false)
    @ApiModelProperty("点位名称")
    @Excel(name = "点位名称",needMerge = true,orderNum = "30",width = 11)
    @Length(message = "点位名称{validation.message.length}", max = 100)
    private String sampleFolderName;

    /**
     * 检测类型id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;

    /**
     * 送样单id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("送样单id")
    private String receiveId;

    /**
     * 送样单号
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("送样单号")
    @Excel(name = "送样单号",needMerge = true,orderNum = "40",width = 11)
    @Length(message = "送样单号{validation.message.length}", max = 50)
    private String recordCode;


    /**
     * 产值
     */
    @ApiModelProperty("产值")
    @Excel(name = "产值",needMerge = true,orderNum = "60",width = 11)
    private BigDecimal totalAmount;

    /**
     * 采样日期
     */
    @ApiModelProperty("采样日期")
    @Excel(name = "签订日期",exportFormat = "yyyy-MM-dd",needMerge = true,orderNum = "50",width = 11)
    private Date samplingTime;

    /**
     * 采样人员
     */
    @Column(length=4000)
    @ApiModelProperty("采样人员")
    private String samplingPersonIds;

}
