package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoCostInfoDetail;

import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;


/**
 * 费用明细数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface CostInfoDetailRepository extends IBaseJpaPhysicalDeleteRepository<DtoCostInfoDetail, String> {
    /**
     * 查询指定费用的费用明细
     *
     * @param costInfoId 费用id
     * @return 对应费用下的费用明细
     */
    List<DtoCostInfoDetail> findByCostInfoIdOrderBySampleTypeIdAscRedAnalyzeItemNameAsc (String costInfoId);
}