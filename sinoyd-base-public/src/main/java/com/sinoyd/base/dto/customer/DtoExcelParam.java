package com.sinoyd.base.dto.customer;

/**
 * excel参数实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/7/12
 * @since V100R001
 */

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Data
public class DtoExcelParam implements Serializable {

    private static final long serialVersionUID = -7114351754000940359L;

    //行数
    private int row;

    //列数
    private int col;

    //单元格值
    private String value;

    //sheet名
    private String sheetName;

    //原始值
    private String oldValue;

    //字体大小
    private String fontSize;

    //第一个单元格的x坐标
    private Integer dx1;

    //第一个单元格的y坐标
    private Integer dy1;

    //第二个单元格的x坐标
    private Integer dx2;

    //第二个单元格的y坐标
    private Integer dy2;

    //第一个单元格的列
    private Integer col1;

    //行高
    private Integer rowHeight;

    //列宽
    private Integer colWidth;

    public DtoExcelParam(String sheetName, Integer dx1, Integer dy1, Integer dx2, Integer dy2, Integer col1, Integer row1, Integer col2, Integer row2, MultipartFile file) {
        this.sheetName = sheetName;
        this.dx1 = dx1;
        this.dy1 = dy1;
        this.dx2 = dx2;
        this.dy2 = dy2;
        this.col1 = col1;
        this.row1 = row1;
        this.col2 = col2;
        this.row2 = row2;
        this.file = file;
    }

    //第一个单元格的行
    private Integer row1;

    //第二个单元格的列
    private Integer col2;

    //第二个单元格的行
    private Integer row2;

    private MultipartFile file;

}
