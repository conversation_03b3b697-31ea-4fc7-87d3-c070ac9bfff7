package com.sinoyd.lims.api.controller;

import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.api.dto.customer.DtoSampleSchedule;
import com.sinoyd.lims.api.dto.customer.DtoWorkSheetTask;
import com.sinoyd.lims.api.service.LargeScreenService;
import com.sinoyd.lims.lim.dto.customer.DtoPersonQuery;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 大屏（实验室专题）管理服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
@Api(tags = "示例: LargeScreen服务")
@RestController
@RequestMapping("/api/field/largeScreen")
public class LargeScreenController extends ExceptionHandlerController<LargeScreenService> {


    /**
     * 采样概况
     *
     * @param date 日期
     * @return 采样概况
     */
    @ApiOperation(value = "采样概况", notes = "采样概况")
    @GetMapping("/sampleOverview")
    public RestResponse<List<Map<String, String>>> sampleOverview(String date) {
        RestResponse<List<Map<String, String>>> restResp = new RestResponse<>();
        restResp.setData(service.sampleOverview(date));
        return restResp;
    }


    /**
     * 分析概况
     *
     * @return 分析概况
     */
    @ApiOperation(value = "分析概况", notes = "分析概况")
    @GetMapping("/analyseOverview")
    public RestResponse<List<Map<String, Object>>> analyseOverview() {
        RestResponse<List<Map<String, Object>>> restResp = new RestResponse<>();
        restResp.setData(service.analyseOverview());
        return restResp;
    }


    /**
     * 采样一览
     *
     * @return 采样一览
     */
    @ApiOperation(value = "采样一览", notes = "采样一览")
    @GetMapping("/sampleList")
    public RestResponse<List<DtoSampleSchedule>> sampleList(String date) {
        RestResponse<List<DtoSampleSchedule>> restResp = new RestResponse<>();
        restResp.setData(service.sampleList(date));
        return restResp;
    }


    /**
     * 检测任务
     *
     * @return 检测任务
     */
    @ApiOperation(value = "检测任务", notes = "检测任务")
    @GetMapping("/workSheetTask")
    public RestResponse<List<DtoWorkSheetTask>> workSheetTask(DtoWorkSheetTask dtoWorkSheetTask) {
        RestResponse<List<DtoWorkSheetTask>> restResp = new RestResponse<>();
        restResp.setData(service.workSheetTask(dtoWorkSheetTask));
        return restResp;
    }


    /**
     * 数据统计
     *
     * @return 数据统计
     */
    @ApiOperation(value = "数据统计", notes = "数据统计")
    @GetMapping("/statisticsData")
    public RestResponse<Map<String, Object>> statisticsData(@RequestParam("year") Integer year) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        restResp.setData(service.statisticsData(year));
        return restResp;
    }


    /**
     * 超期任务
     *
     * @return 超日任务列表
     */
    @ApiOperation(value = "超期任务", notes = "超期任务")
    @GetMapping("/overdueTask")
    public RestResponse<List<Map<String, Object>>> overdueTask() {
        RestResponse<List<Map<String, Object>>> restResp = new RestResponse<>();
        restResp.setData(service.overdueTask());
        return restResp;
    }

    /**
     * 获取人员信息配置
     *
     * @return 人员列表
     */
    @ApiOperation(value = "获取人员信息配置", notes = "获取人员信息配置")
    @PostMapping("/person")
    public RestResponse<List<DtoKeyValue>> person(@RequestBody DtoPersonQuery queryDto) {
        RestResponse<List<DtoKeyValue>> restResp = new RestResponse<>();
        restResp.setData(service.person(queryDto));
        return restResp;
    }

    /**
     * 岗位配置信息
     *
     * @return 岗位列表
     */
    @ApiOperation(value = "岗位配置信息", notes = "岗位配置信息")
    @GetMapping("/testPost")
    public RestResponse<List<DtoTestPost>> testPost() {
        RestResponse<List<DtoTestPost>> restResp = new RestResponse<>();
        restResp.setData(service.testPost());
        return restResp;
    }

    /**
     * 岗位配置信息
     *
     * @return 是否开启测试岗位
     */
    @ApiOperation(value = "岗位配置信息", notes = "岗位配置信息")
    @GetMapping("/isTestPost")
    public RestResponse<Boolean> isTestPost() {
        RestResponse<Boolean> restResp = new RestResponse<>();
        restResp.setData(service.isTestPost());
        return restResp;
    }

    /**
     * 获取所有机构
     *
     * @return 机构列表
     */
    @ApiOperation(value = "获取所有机构", notes = "获取所有机构")
    @GetMapping("/org")
    public RestResponse<List<OrgModel>> getOrgList() {
        RestResponse<List<OrgModel>> restResp = new RestResponse<>();
        restResp.setData(service.getOrgList());
        return restResp;
    }

    /**
     * 年度机构信息统计(安徽调用)
     *
     * @return 统计信息
     */
    @ApiOperation(value = "年度机构信息统计", notes = "年度机构信息统计")
    @GetMapping("/preview/{year}")
    public RestResponse<Map<String, Object>> yearPreview(@PathVariable Integer year) {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        restResp.setData(service.yearPreview(year));
        return restResp;
    }

    /**
     * 实验室简介(安徽调用)
     *
     * @return 实验室简介
     */
    @ApiOperation(value = "实验室简介", notes = "实验室简介")
    @GetMapping("/lab/overview")
    public RestResponse<Map<String, Object>> labOverview() {
        RestResponse<Map<String, Object>> restResp = new RestResponse<>();
        restResp.setData(service.labOverview());
        return restResp;
    }
}
