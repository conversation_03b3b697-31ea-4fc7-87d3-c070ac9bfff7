package com.sinoyd.lims.lim.service;


import com.sinoyd.lims.lim.dto.customer.DtoDataSyncParams;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 测试项目数据迁移导入接口
 */
public interface ImportTestTransformerService {
    /**
     * 文件解析，数据导入
     *
     * @param file     文件
     * @param response 响应
     * @throws Exception 异常
     */
    void importExcel(MultipartFile file, HttpServletResponse response) throws Exception;


    /**
     * 获取导入基础数据
     *
     * @param file 文件
     * @return Map<String, List < String>>
     */
    DtoDataSyncParams getBaseData(MultipartFile file, HttpServletResponse response);

    /**
     * 导入前检查
     *
     * @param dataSyncBindCheck 数据传输实体
     * @return 检查后数据
     */
    DtoDataSyncParams getCheckData(DtoDataSyncParams dataSyncBindCheck, HttpServletResponse response);

    /**
     * 获取导入表名
     */
    List<Map<String,String>> getDataTable();

    /**
     * 数据同步
     *
     * @param dtoDataSyncParams 同步参数
     */
    void importData(DtoDataSyncParams dtoDataSyncParams);


}
