package com.sinoyd.lims.lim.constants;

/**
 * LIMS模块常量
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/23
 */
public interface LimConstants {

    /**
     * 详细数据查询常量
     */
    interface DetailDataConstants{
        /**
         * 赋值为空的常量
         */
        String EMPTY_VALUE = "/";

        /**
         * 空字符串
         */
        String EMPTY_STRING = "";

        /**
         * 加密字符串
         */
        String ENCRYPT_STRING = "***";

        /**
         * 详细数据查看权限(有权限的可以查看数据，没有权限的人不可查看数据)
         */
        String ANALYSE_DATA_SHOW = "pro_analyseData_detail";

        /**
         * 详细数据查看权限(没有权限的人只可以查看审核后的数据。有权限的人可以查看未审核的数据)
         */
        String ANALYSE_AUDIT_SHOW = "pro_analyseData_audit_show";
    }

    /**
     * 资源统计类名名称
     */
    interface StatisticsItemName {

        /**
         * 仪器过期
         */
        String INSTRUMENT_OVERDUE = "仪器过期";

        /**
         * 仪器即将过期
         */
        String INSTRUMENT_WILLOVERDUE = "仪器即将过期";

        /**
         * 耗材过期
         */
        String CONSUMABLE_OVERDUE = "耗材过期";

        /**
         * 耗材库存警告
         */
        String CONSUMABLE_INVENTORY_WARN = "耗材低库存";

        /**
         * 上岗证过期
         */
        String JOB_CERT_OVERDUE = "上岗证过期";
    }

    /**
     * 导入的常量
     */
    interface ImportConstants{

        /**
         * 污染源客户类型字典编码
         */
        String LIM_POLLUTION_SOURCE_TYPE = "LIM_PollutionSourceType";
        /**
         * 评价标准类型（国标）
         */
        String EVALUATION_TYPE_GB = "BASE_EvaluateType_GB";

        /**
         * 环境质量点位类型的字典编码
         */
        String FIXED_POINT_TYPE_HJ = "LIM_EnvQualityPointType";

        /**
         * 污染源点位类型的字典编码
         */
        String FIXED_POINT_TYPE_WR = "LIM_PollutionPointType";

        /**
         * 控制等级的字典编码
         */
        String CONTROL_LEVEL = "LIM_ControlLevel";

        /**
         * 英文评价标准等级分隔符号
         */
        String ENGLISH_SPLIT_CHAR = "##";

        /**
         * 中文评价标准等级分隔符号
         */
        String CHINESE_SPLIT_CHAR = "##";

        /**
         * 表1的key
         */
        String FIRST_SHEET_NAME = "firstName";

        /**
         * 表2的key
         */
        String SECOND_SHEET_NAME = "secondName";

        /**
         * 默认次数（例行任务点位导入，周期，次数）
         */
        Integer DEFAULT_ORDER_NUM = 1;
    }

    /**
     * 常量的编码
     */
    interface codeConstants{
        /**
         * 实验室分析是否按岗位显示
         */
        String PRO_AnalyseAllocationRules_Post = "PRO_AnalyseAllocationRules_Post";
    }

    /**
     * 获取附件的文档类型（所有类型）
     */
    String DOCUMENT_TYPE_ALL = "all";
}