package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoCalendarDate;
import com.sinoyd.lims.lim.dto.rcc.DtoHolidayConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoWorkdayConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.CalendarDateRepository;
import com.sinoyd.lims.lim.service.CalendarDateService;
import com.sinoyd.lims.lim.utils.CalendarUtil;
import com.sinoyd.lims.lim.vo.CalendarDayVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 日历日期实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/19
 */
@Service
public class CalendarDateServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCalendarDate, String, CalendarDateRepository> implements CalendarDateService {

    @Override
    public List<DtoCalendarDate> findByYear(Integer year) {
        return this.findByCalendarDateBetween(CalendarUtil.getFirstDateOfYear(year), CalendarUtil.getLastDateOfYear(year));
    }

    @Override
    @Transactional
    public List<DtoCalendarDate> initCalendarDate(Integer year) {
        List<CalendarDayVO> calendarDayVOS = CalendarUtil.loadCalendarDayByYear(year);
        List<DtoCalendarDate> dtoList = new ArrayList<>();
        calendarDayVOS.forEach(item -> {
            DtoCalendarDate dto = new DtoCalendarDate();
            dto.setCalendarDate(item.getDay());
            dto.setType(item.getType());
            dto.setWeekday(item.getWeekDay());
            dtoList.add(dto);
        });
        return super.save(dtoList);
    }

    @Override
    public List<DtoCalendarDate> findByCalendarDateBetween(Date beginDate, Date endDate) {
        return repository.findByCalendarDateBetweenOrderByCalendarDate(beginDate, endDate);
    }

    @Override
    @Transactional
    public void refreshCalendarDate(Date fromDate, Date endDate, DtoWorkdayConfig dtoWorkdayConfig) {
        List<DtoCalendarDate> calendarDateList = findByCalendarDateBetween(fromDate, endDate);
        if (StringUtil.isEmpty(dtoWorkdayConfig.getWorkday())) {
            setWorkAndHoliday(calendarDateList, false, true, null, null);
        } else if (StringUtil.isEmpty(dtoWorkdayConfig.getWeekendDay())) {
            setWorkAndHoliday(calendarDateList, true, false, null, null);
        } else {
            List<Integer> workday = Arrays.stream(dtoWorkdayConfig.getWorkday().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            List<Integer> weekendDay = Arrays.stream(dtoWorkdayConfig.getWeekendDay().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            setWorkAndHoliday(calendarDateList, false, false, workday, weekendDay);
        }
        super.save(calendarDateList);
    }

    @Override
    @Transactional
    public void refreshCalendarDate(List<DtoHolidayConfig> dtoHolidayConfigList) {
        List<DtoCalendarDate> calendarDates = new ArrayList<>();
        if (StringUtil.isNotEmpty(dtoHolidayConfigList)) {
            List<Date> dateRange = findHolidayConfigDateRange(dtoHolidayConfigList);
            List<DtoCalendarDate> calendarDateList = findByCalendarDateBetween(dateRange.get(0), dateRange.get(1));
            for (DtoHolidayConfig dto : dtoHolidayConfigList) {
                List<DtoCalendarDate> dateList = calendarDateList.stream().filter(p -> p.getCalendarDate().compareTo(dto.getBeginDate()) >= 0
                        && p.getCalendarDate().compareTo(dto.getEndDate()) <= 0).collect(Collectors.toList());
                for (DtoCalendarDate dtoCalendarDate : dateList) {
                    dtoCalendarDate.setHolidayName(dto.getHolidayName());
                    dtoCalendarDate.setType(EnumLIM.EnumCalendarDateType.休息日.getValue());
                }
                calendarDates.addAll(dateList);
            }
            super.save(calendarDates);
        }
    }

    /**
     * 从节假日配置中找出最早的开始日期和最晚的结束日期
     *
     * @param dtoHolidayConfigList 节假日配置集合
     * @return 日期集合
     */
    private List<Date> findHolidayConfigDateRange(List<DtoHolidayConfig> dtoHolidayConfigList) {
        Date minFromDate = dtoHolidayConfigList.stream().map(DtoHolidayConfig::getBeginDate).min(Date::compareTo).get();
        Date maxEndDate = dtoHolidayConfigList.stream().map(DtoHolidayConfig::getEndDate).max(Date::compareTo).get();
        List<Date> list = new ArrayList<>();
        list.add(minFromDate);
        list.add(maxEndDate);
        return list;
    }

    /**
     * 对工作日以及节假日进行设置
     *
     * @param calendarDateList 日历日期
     * @param isAllWork        是否都是工作日
     * @param isAllHoliday     是否都是节假日
     * @param workday          工作日周数
     * @param weekendDay       节假日周数
     */
    private void setWorkAndHoliday(List<DtoCalendarDate> calendarDateList, boolean isAllWork, boolean isAllHoliday, List<Integer> workday, List<Integer> weekendDay) {
        calendarDateList.forEach(item -> {
            if (isAllWork) {
                item.setType(EnumLIM.EnumCalendarDateType.工作日.getValue());
            } else if (isAllHoliday) {
                item.setType(EnumLIM.EnumCalendarDateType.休息日.getValue());
            } else {
                if (workday.contains(item.getWeekday())) {
                    item.setType(EnumLIM.EnumCalendarDateType.工作日.getValue());
                } else if (weekendDay.contains(item.getWeekday())) {
                    item.setType(EnumLIM.EnumCalendarDateType.休息日.getValue());
                }
            }

            item.setHolidayName(null);
        });
    }
}