CREATE  TRIGGER "AFTER_UPDATE_PERSON2TEST"
    AFTER  UPDATE
    ON "TB_LIM_PERSON2TEST"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    if new.personId != old.personId or (new.personId is null and old.personId is not null) or
       (new.personId is not null and old.personId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'personId',
        old.personId, new.personId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.testId != old.testId or (new.testId is null and old.testId is not null) or
       (new.testId is not null and old.testId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'testId',
        old.testId,
        new.testId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
       (new.orderNum is not null and old.orderNum is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'orderNum',
        old.orderNum, new.orderNum, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.sampleTypeId != old.sampleTypeId or (new.sampleTypeId is null and old.sampleTypeId is not null) or
       (new.sampleTypeId is not null and old.sampleTypeId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'sampleTypeId',
        old.sampleTypeId, new.sampleTypeId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.isDefaultPerson != old.isDefaultPerson or
       (new.isDefaultPerson is null and old.isDefaultPerson is not null) or
       (new.isDefaultPerson is not null and old.isDefaultPerson is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'isDefaultPerson',
        old.isDefaultPerson, new.isDefaultPerson, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.isDefaultAuditPerson != old.isDefaultAuditPerson or
       (new.isDefaultAuditPerson is null and old.isDefaultAuditPerson is not null) or
       (new.isDefaultAuditPerson is not null and old.isDefaultAuditPerson is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'isDefaultAuditPerson',
        old.isDefaultAuditPerson, new.isDefaultAuditPerson, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
end;
