package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * LogForAnalyzeMethod实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/01/22
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "LogForAnalyzeMethod")
@Data
public class LogForAnalyzeMethod implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public LogForAnalyzeMethod() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";

    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 操作者Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("操作者Id")
    private String operatorId;

    /**
     * 操作者名字
     */
    @Column(length = 50)
    @ApiModelProperty("操作者名字")
    private String operatorName;

    /**
     * 操作时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("操作时间")
    private Date operateTime;

    /**
     * 操作类型（新建、保存、修改等）
     */
    @Column(length = 500)
    @ApiModelProperty("操作类型（新建、保存、修改等）")
    @Length(message = "操作类型{validation.message.length}", max = 500)
    private String operateInfo;

    /**
     * 对象id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("对象id")
    private String objectId;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 所属实验室id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室id")
    private String domainId;
}