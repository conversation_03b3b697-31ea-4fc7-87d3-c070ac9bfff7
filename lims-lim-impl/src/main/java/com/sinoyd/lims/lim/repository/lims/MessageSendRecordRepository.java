package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoMessageSendRecord;

import java.util.Collection;


/**
 * 消息数据访问接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022-09-22
 */
public interface MessageSendRecordRepository extends IBaseJpaPhysicalDeleteRepository<DtoMessageSendRecord, String> {

    /**
     * 根据接收人和状态统计消息数
     *
     * @param receiverId 接收人id
     * @param status     状态
     * @return 消息数
     */
    Integer countByReceiverAndStatus(String receiverId, Integer status);

    /**
     * 根据接收人和状态和消息类型统计消息数
     *
     * @param receiverId   接收人id
     * @param status       状态
     * @param messageTypes 消息类型
     * @return 消息数
     */
    Integer countByReceiverAndStatusAndMessageTypeIn(String receiverId, Integer status, Collection<String> messageTypes);
}
