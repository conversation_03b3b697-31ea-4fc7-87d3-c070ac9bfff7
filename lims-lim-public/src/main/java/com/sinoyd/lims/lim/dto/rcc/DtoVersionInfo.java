package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.VersionInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoVersionInfo实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_VersionInfo")
@Data
@DynamicInsert
public class DtoVersionInfo extends VersionInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 返回前端的二维码
     */
    @Transient
    private String base64Content;

    /**
     * 类别名称
     */
    @Transient
    private String verTypeName;

    /**
     * 附件路径
     */
    @Transient
    private String realUrl;

    /**
     * 文件的路径
     */
    @Transient
    private Integer realSize;

    /**
     * 上传文件的id
     */
    @Transient
    private String documentId;

    @Transient
    private String documentName;

    /**
     * 是否强制更新
     */
    @Transient
    private Boolean isForceRenewal;
}