package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoExplore;

import java.util.List;


/**
 * Explore数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
public interface ExploreRepository extends IBaseJpaPhysicalDeleteRepository<DtoExplore, String> {

    /**
     * 查询项目的踏勘信息
     *
     * @param projectId 项目id
     * @return 踏勘信息列表
     */
    List<DtoExplore> findByProjectIdOrderByCreateDate(String projectId);

    /**
     * 根据项目id列表查询踏勘信息
     *
     * @param projectIds 项目id列表
     * @return 踏勘信息列表
     */
    List<DtoExplore> findByProjectIdIn(List<String> projectIds);
}