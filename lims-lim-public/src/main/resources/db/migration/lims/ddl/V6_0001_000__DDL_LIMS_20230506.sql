DROP TABLE IF EXISTS TB_PRO_AnalyseAchievement2Person;
CREATE TABLE TB_PRO_AnalyseAchievement2Person
(
    id         varchar(50) NOT NULL COMMENT '主键',
    personId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '人员id',
    orgId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS TB_PRO_AnalyseAchievementDetails;
CREATE TABLE TB_PRO_AnalyseAchievementDetails
(
    id                varchar(50)    NOT NULL COMMENT '主键',
    achievementId     varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '绩效id',
    sampleId          varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '样品id',
    sampleCode        varchar(50) NULL DEFAULT '' COMMENT '样品编号',
    testId            varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '测试项目id',
    analyzeItemName   varchar(100) NULL DEFAULT NULL COMMENT '分析项目名称',
    analyzeMethodName varchar(255) NULL DEFAULT NULL COMMENT '分析方法名称',
    countryStandard   varchar(100) NULL DEFAULT '' COMMENT '标准编号',
    analystId         varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '分析人员id',
    analystName       varchar(50) NULL DEFAULT '' COMMENT '分析人员名称',
    analyzeTime       datetime(0) NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '数据分析时间',
    sampleTypeId      varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型id',
    totalAmount       decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '产值',
    status            varchar(50)    NULL DEFAULT '' COMMENT '状态',
    orgId             varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator           varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate        datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId          varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier          varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate        datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);


DROP TABLE IF EXISTS TB_PRO_OrderContractAchievement2Person;
CREATE TABLE TB_PRO_OrderContractAchievement2Person
(
    id         varchar(50) NOT NULL COMMENT '主键',
    personId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '人员id',
    orgId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);


DROP TABLE IF EXISTS TB_PRO_OrderContractAchievementDetails;
CREATE TABLE TB_PRO_OrderContractAchievementDetails
(
    id             varchar(50)    NOT NULL COMMENT '主键',
    achievementId  varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '人员绩效id',
    contractCode   varchar(50)    NOT NULL DEFAULT '' COMMENT '合同编号',
    contractName   varchar(255)   NOT NULL DEFAULT '' COMMENT '合同名称',
    firstEntName   varchar(255)   NOT NULL DEFAULT '' COMMENT '甲方名称',
    totalAmount    decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '合同金额',
    signDate       datetime(0) NULL DEFAULT '1753-01-01 00:00:00' COMMENT '签订日期',
    signPersonId   text NULL COMMENT '签订人员id',
    contractNature varchar(50)    NOT NULL DEFAULT '' COMMENT '合同性质',
    orgId          varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);


DROP TABLE IF EXISTS TB_PRO_ReportAchievement2Person;
CREATE TABLE TB_PRO_ReportAchievement2Person
(
    id         varchar(50) NOT NULL COMMENT '主键',
    personId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '人员id',
    orgId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS TB_PRO_ReportAchievementDetails;
CREATE TABLE TB_PRO_ReportAchievementDetails
(
    id             varchar(50) NOT NULL COMMENT '主键',
    achievementId  varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '绩效id',
    reportCode     varchar(50) NULL DEFAULT NULL COMMENT '报告编号',
    reportId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报告id',
    reportTypeId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报告类型id',
    reportPersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报告编制人id',
    reportTime     datetime(0) NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '编制时间',
    projectCode    varchar(50) NULL DEFAULT NULL COMMENT '项目编号',
    projectName    varchar(100) NULL DEFAULT NULL COMMENT '项目名称',
    entId          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '委托方id',
    entName        varchar(255) NULL DEFAULT NULL COMMENT '委托方名称',
    orgId          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS TB_PRO_SamplingAchievement2Person;
CREATE TABLE TB_PRO_SamplingAchievement2Person
(
    id         varchar(50) NOT NULL COMMENT '主键',
    personId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '人员id',
    orgId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id) USING BTREE
);

DROP TABLE IF EXISTS TB_PRO_SamplingAchievementDetails;
CREATE TABLE TB_PRO_SamplingAchievementDetails
(
    id                varchar(50)    NOT NULL COMMENT '主键',
    achievementId     varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '绩效id',
    sampleId          varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '样品id',
    sampleCode        varchar(50) NULL DEFAULT '' COMMENT '样品编号',
    sampleFolderId    varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位id',
    sampleFolderName  varchar(100) NULL DEFAULT '' COMMENT '点位名称',
    sampleTypeId      varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型id',
    receiveId         varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '送样单id',
    recordCode        varchar(50) NULL DEFAULT '' COMMENT '送样单号',
    samplingPersonIds text NULL COMMENT '采样人id,逗号隔开',
    samplingTime      datetime(0) NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '采样时间',
    totalAmount       decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '产值',
    orgId             varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator           varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate        datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId          varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier          varchar(50)    NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate        datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);
