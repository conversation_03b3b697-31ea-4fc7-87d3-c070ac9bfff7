package com.sinoyd.lims.od.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.od.dto.DtoOdPerson;

import java.util.List;

/**
 * OdPersonRepository
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
public interface OdPersonRepository extends IBaseJpaRepository<DtoOdPerson, String> {

    /**
     * 根据嗅辨任务Id查询
     *
     * @param taskId 嗅辨任务id
     * @return 嗅辨员集合
     */
    List<DtoOdPerson> findByTaskId(String taskId);

    /**
     * 根据嗅辨任务Ids查询
     *
     * @param taskIds 嗅辨任务ids
     * @return 嗅辨员集合
     */
    List<DtoOdPerson> findByTaskIdIn(List<String> taskIds);

}
