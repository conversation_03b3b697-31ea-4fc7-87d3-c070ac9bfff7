package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * ParamsData查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParamsDataCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 对象ids
    */
    private List<String> objectIds;


    /**
     * 对象类型
     */
    private Integer objectType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.paramsConfigId = b.id");
        if (StringUtil.isNotNull(this.objectType)) {
            condition.append(" and a.objectType = :objectType");
            values.put("objectType", this.objectType);
        }
        if (StringUtil.isNotNull(this.objectIds) && objectIds.size() > 0) {
            condition.append(" and a.objectId in :objectIds");
            values.put("objectIds", this.objectIds);
        }
        return condition.toString();
    }
}