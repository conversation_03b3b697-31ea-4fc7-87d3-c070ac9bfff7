package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.criteria.ConsumableCriteria;
import com.sinoyd.base.criteria.EnterpriseCriteria;
import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.repository.lims.ConsumableDetailRepository;
import com.sinoyd.base.repository.lims.ConsumableRepository;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.service.ConsumableService;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableExtend;
import com.sinoyd.lims.lim.dto.customer.DtoImportConsumableStandardExtend;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpConsumable;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpStandard;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.ExpImpConsumableService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyConsumableVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sinoyd.base.core.BaseCodeHelper.ConsumableGrade;

/**
 * 消耗品导入修改实现
 *
 * @version V1.0.0 2023/12/20
 * @author: hukq
 * @since V100R001
 */
@Service
public class ExpImpConsumableServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoConsumable, String, ConsumableRepository> implements ExpImpConsumableService {

    private ConsumableService consumableService;
    private ImportUtils importUtils;
    private PersonRepository personRepository;
    private CodeService codeService;
    private ConsumableDetailRepository consumableDetailRepository;
    private DimensionRepository dimensionRepository;
    private EnterpriseService enterpriseService;
    private ImpModifyConsumableVerify impModifyConsumableVerify;

    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoConsumable> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        ConsumableCriteria criteria = (ConsumableCriteria) baseCriteria;
        consumableService.findByPage(page, criteria);
        List<DtoConsumable> dtoConsumables = page.getData();
        List<String> consumableIds = dtoConsumables.stream().map(DtoConsumable::getId).collect(Collectors.toList());
        List<DtoPerson> personList = personRepository.findAll();
        Map<String, String> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
        List<DtoConsumableDetail> consumableDetails = consumableDetailRepository.findByParentIdIn(consumableIds);
        Map<String, List<DtoConsumableDetail>> consumableDetailMap = consumableDetails.stream().collect(Collectors.groupingBy(DtoConsumableDetail::getParentId));
        Date year1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        List<DtoExpImpConsumable> dtoExpImpConsumables = new ArrayList<>();
        for (DtoConsumable dtoConsumable : dtoConsumables) {
            List<DtoConsumableDetail> detailMapOrDefault = consumableDetailMap.getOrDefault(dtoConsumable.getId(), new ArrayList<>());
            for (DtoConsumableDetail dtoConsumableDetail : detailMapOrDefault) {
                DtoExpImpConsumable expImpConsumable = new DtoExpImpConsumable();
                BeanUtils.copyProperties(dtoConsumable, expImpConsumable);
                String expiryDate = dtoConsumableDetail.getExpiryDate().compareTo(year1753) == 0 ? "" : DateUtil.dateToString(dtoConsumableDetail.getExpiryDate(), DateUtil.YEAR);
                expImpConsumable.setExpiryDate(expiryDate);
                String storageDate = dtoConsumableDetail.getStorageDate().compareTo(year1753) == 0 ? "" : DateUtil.dateToString(dtoConsumableDetail.getStorageDate(), DateUtil.YEAR);
                expImpConsumable.setStorageDate(storageDate);
                expImpConsumable.setDetailId(dtoConsumableDetail.getId());
                expImpConsumable.setProductionCode(dtoConsumableDetail.getProductionCode());
                expImpConsumable.setManufacturerName(dtoConsumableDetail.getManufacturerName());
                expImpConsumable.setSupplierName(dtoConsumableDetail.getSupplierName());
                expImpConsumable.setKeepPlace(dtoConsumableDetail.getKeepPlace());
                expImpConsumable.setRemark(dtoConsumableDetail.getRemark());
                expImpConsumable.setUnitPrice(dtoConsumableDetail.getUnitPrice().stripTrailingZeros().toPlainString());
                expImpConsumable.setInventory(dtoConsumable.getInventory().stripTrailingZeros().toPlainString());
                expImpConsumable.setWarningNum(dtoConsumable.getWarningNum().stripTrailingZeros().toPlainString());
                // 提醒人员
                String warnUser = personMap.getOrDefault(dtoConsumable.getSendWarnUserId(), "");
                expImpConsumable.setSendWarnUserName(warnUser);
                dtoExpImpConsumables.add(expImpConsumable);
            }
        }
        List<String> consCategoryList = codeService.findCodes("LIM_ConsumableCategory").stream().map(DtoCode::getDictName).collect(Collectors.toList());
        List<String> consGradeList = codeService.findCodes("LIM_ConsumableGrade").stream().map(DtoCode::getDictName).collect(Collectors.toList());
        List<DtoImportConsumableStandardExtend> extendList = getStandardExtendData(consCategoryList, consGradeList);

        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpConsumable.class, DtoImportConsumableStandardExtend.class, dtoExpImpConsumables, extendList);
        // 设置下拉框、
        // 消耗品等级、种类
        String[] grade = consGradeList.toArray(new String[0]);
        String[] category = consCategoryList.toArray(new String[0]);

        importUtils.selectList(workBook, 3, 3, category);
        importUtils.selectList(workBook, 5, 5, grade);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    @Transactional
    public List<DtoConsumable> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //获取是否导入消耗品类型
        Boolean isImportStandardType = (Boolean) objectMap.get(0);
        ExcelImportResult<DtoImportConsumableExtend> extendResult = importUtils.getExcelData(file, DtoImportConsumableExtend.class, 0, 1, 1, true, null);
        List<DtoCode> grades = codeService.findCodes(ConsumableGrade);
        List<DtoCode> consumableCategory = codeService.findCodes("LIM_ConsumableCategory");
        List<DtoPerson> personList = personRepository.findAll();
        List<DtoDimension> dtoDimensions = dimensionRepository.findAll();
        List<DtoEnterprise> enterprise = getEnterprise();
        List<DtoConsumable> consumableAllList = repository.findAll();

        // 临时数据
        List<DtoExpImpConsumable> impStandards = new ArrayList<>();
        impModifyConsumableVerify.getStandardCategoryTl().set(consumableCategory);
        impModifyConsumableVerify.getGradesTl().set(grades);
        impModifyConsumableVerify.getPersonTl().set(personList);
        impModifyConsumableVerify.getEnterpriseTl().set(enterprise);
        impModifyConsumableVerify.getStandardTl().set(impStandards);
        impModifyConsumableVerify.getIsImportStandardTypeTl().set(isImportStandardType);
        impModifyConsumableVerify.getImportConsumableExtend().set(extendResult.getList());
        impModifyConsumableVerify.getDimensionList().set(dtoDimensions);

        //获取导入结果
        ExcelImportResult<DtoExpImpConsumable> result = getExcelData(file, response);
        List<DtoExpImpConsumable> impStandardList = result.getList();
        impModifyConsumableVerify.getStandardCategoryTl().remove();
        impModifyConsumableVerify.getGradesTl().remove();
        impModifyConsumableVerify.getPersonTl().remove();
        impModifyConsumableVerify.getEnterpriseTl().remove();
        impModifyConsumableVerify.getStandardTl().remove();
        impModifyConsumableVerify.getIsImportStandardTypeTl().remove();
        impModifyConsumableVerify.getImportConsumableExtend().remove();
        impModifyConsumableVerify.getDimensionList().remove();

        impStandardList.removeIf(p -> StringUtil.isEmpty(p.getConsumableName()));
        //判断导入中是否存在数据
        if (StringUtil.isEmpty(impStandardList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }

        if (isImportStandardType) {
            //导入消耗品类型
            importExtend(file, consumableCategory);
        }

        List<String> detailId = impStandardList.stream().map(DtoExpImpConsumable::getDetailId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<DtoConsumableDetail> consumableDetails = StringUtil.isNotEmpty(detailId) ? consumableDetailRepository.findAll(detailId) : new ArrayList<>();
        Map<String, DtoConsumableDetail> consumableDetailMap = consumableDetails.stream().collect(Collectors.toMap(DtoConsumableDetail::getId, p -> p));

        //转换实体（消耗品基本信息）
        List<DtoConsumable> consumables = getConsumableEntity(impStandardList, personList, consumableAllList);
        addData(consumables);
        // 处理量纲
        handleDimension(consumables, dtoDimensions);
        List<DtoConsumableDetail> details = getDetailEntity(impStandardList, enterprise, personList, consumableDetailMap);
        consumableDetailRepository.save(details);
        return repository.findAll();
    }

    @Override
    @Transactional
    public void addData(List<DtoConsumable> data) {
        if (StringUtil.isNotEmpty(data)) {
            consumableService.save(data);
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpConsumable> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);

        params.setVerifyHandler(impModifyConsumableVerify);
        ExcelImportResult<DtoExpImpConsumable> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoExpImpConsumable.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "消耗品导入错误信息");
            PoiExcelUtils.downLoadExcel("消耗品导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 获取标样导出模板的拓展数据
     *
     * @param consCategoryList 标样类型
     * @param consGradeList    等级
     * @return 标样导出关联数据
     */
    private List<DtoImportConsumableStandardExtend> getStandardExtendData(List<String> consCategoryList, List<String> consGradeList) {
        //返回的数据集合
        List<DtoImportConsumableStandardExtend> extendList = new ArrayList<>();

        // 获取最长
        List<Integer> size = new ArrayList<>();
        size.add(consCategoryList.size());
        size.add(consGradeList.size());
        Integer max = Collections.max(size);
        // 循环赋值第二种Sheet内容
        for (int i = 0; i < max; i++) {
            DtoImportConsumableStandardExtend extendData = new DtoImportConsumableStandardExtend();
            extendData.setConsumableCategory(consCategoryList.size() < i + 1 ? null : consCategoryList.get(i));
            extendData.setGrade(consGradeList.size() < i + 1 ? null : consGradeList.get(i));
            extendList.add(extendData);
        }
        return extendList;
    }

    private List<DtoEnterprise> getEnterprise() {
        EnterpriseCriteria criteria = new EnterpriseCriteria();
        PageBean<DtoEnterprise> pb = new PageBean<>();
        criteria.setType(4);
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        enterpriseService.findByPage(pb, criteria);
        return pb.getData();
    }

    /**
     * 导入消耗品类型
     *
     * @param file       文件流
     * @param dbConsType 消耗品类型数据
     */
    private void importExtend(MultipartFile file, List<DtoCode> dbConsType) {
        //获取导入的关联信息
        List<DtoImportConsumableExtend> extendResult = importUtils.getImportNames(file, DtoImportConsumableExtend.class);
        //数据库中类型
        List<String> dbExtendData = dbConsType.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //关联表中类型
        List<String> resultNames = extendResult.stream().map(DtoImportConsumableExtend::getConsumableStandardCategory).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        importUtils.createCodes(resultNames, "LIM_StandardCategory", dbExtendData);
    }

    /**
     * 获取消耗品基本信息实体
     *
     * @param impStandardList 导入实体
     * @param personList      导入实体
     * @return 消耗品实体
     */
    private List<DtoConsumable> getConsumableEntity(List<DtoExpImpConsumable> impStandardList, List<DtoPerson> personList, List<DtoConsumable> consumableAllList) {

        Map<String, DtoConsumable> consumableMap = consumableAllList.stream().collect(Collectors.toMap(DtoConsumable::getId, p -> p));
        List<DtoConsumable> consumables = new ArrayList<>();
        // id为空值，默认为新增，
        impStandardList = impStandardList.stream().peek(p -> {
            if (StringUtil.isEmpty(p.getId())) {
                p.setId(UUIDHelper.NewID());
            }
        }).collect(Collectors.toList());

        Map<String, List<DtoExpImpConsumable>> groupImportList = impStandardList.stream().collect(Collectors.groupingBy(DtoExpImpConsumable::getId));
        for (Map.Entry<String, List<DtoExpImpConsumable>> entry : groupImportList.entrySet()) {
            String key = entry.getKey();
            List<DtoExpImpConsumable> entryValue = entry.getValue();
            // 主键id不为空时，表示修改，筛选出第一个进行赋值
            if (StringUtil.isNotEmpty(key)) {
                Optional<DtoExpImpConsumable> consumableOptional = entryValue.stream().findFirst();
                if (consumableOptional.isPresent()) {
                    DtoExpImpConsumable importConsumable = consumableOptional.get();
                    DtoConsumable consumable = consumableMap.getOrDefault(importConsumable.getId(), new DtoConsumable());
                    // 消耗品数据赋值
                    setConsumableValue(importConsumable, consumable, consumables, personList);
                }
                // id 为空时表示新增
            } else {
                for (DtoExpImpConsumable importConsumable : entryValue) {
                    importConsumable.setId(UUIDHelper.NewID());
                    DtoConsumable consumable = new DtoConsumable();
                    // 消耗品数据赋值
                    setConsumableValue(importConsumable, consumable, consumables, personList);
                }
            }
        }
        return consumables;
    }


    /**
     * 消耗品数据赋值
     *
     * @param importConsumable 导入数据源
     * @param consumable       赋值实体
     * @param consumables      汇总集合
     * @param personList       人员信息集合
     */
    private void setConsumableValue(DtoExpImpConsumable importConsumable, DtoConsumable consumable, List<DtoConsumable> consumables, List<DtoPerson> personList) {
        BeanUtils.copyProperties(importConsumable, consumable);
        consumable.setInventory(new BigDecimal(importConsumable.getInventory()));
        consumable.setGrade(StringUtil.isNotNull(importConsumable.getGrade()) ? importConsumable.getGrade() : "");
        consumable.setWarningNum(StringUtil.isEmpty(importConsumable.getWarningNum()) ? BigDecimal.ZERO : new BigDecimal(importConsumable.getWarningNum()));
        consumable.setSendWarnUserId(personList.stream().filter(p -> p.getCName().equals(importConsumable.getSendWarnUserName())).map(DtoPerson::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY));
        consumables.add(consumable);
    }

    /**
     * 处理量纲数据
     *
     * @param consumables 需要导入的消耗品
     * @param dbDimension 所有的量纲数据
     */
    private void handleDimension(List<DtoConsumable> consumables, List<DtoDimension> dbDimension) {
        List<String> dimensionNames = dbDimension.stream().map(DtoDimension::getDimensionName).collect(Collectors.toList());
        List<String> importDimensionNames = consumables.stream().map(DtoConsumable::getUnit).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> selectDimensionNames = consumables.stream().map(DtoConsumable::getDimensionName).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        importDimensionNames.addAll(selectDimensionNames);
        importDimensionNames = importDimensionNames.stream().distinct().collect(Collectors.toList());
        List<String> notExistNames = importDimensionNames.stream().filter(p -> !dimensionNames.contains(p)).collect(Collectors.toList());
        List<DtoDimension> addDimension = new ArrayList<>();
        for (String notExistName : notExistNames) {
            DtoDimension dtoDimension = new DtoDimension();
            dtoDimension.setDimensionName(notExistName);
            dtoDimension.setDimensionTypeId("/");
            dtoDimension.setBaseValue(BigDecimal.ZERO);
            addDimension.add(dtoDimension);
        }
        if (StringUtil.isNotEmpty(addDimension)) {
            List<DtoDimension> save = dimensionRepository.save(addDimension);
            dbDimension.addAll(save);
        }
        //处理实体中的量纲Id
        dbDimension.removeIf(p -> StringUtil.isEmpty(p.getDimensionName()));
        Map<String, List<DtoDimension>> dimensionMap = dbDimension.stream().collect(Collectors.groupingBy(DtoDimension::getDimensionName));
        for (DtoConsumable consumable : consumables) {
            List<DtoDimension> dimensionOfName = dimensionMap.get(consumable.getUnit());
            if (StringUtil.isNotNull(dimensionOfName)) {
                Optional<DtoDimension> dimension = dimensionOfName.stream().findFirst();
                dimension.ifPresent(p -> consumable.setUnitId(p.getId()));
            }
            List<DtoDimension> dimensionOfSelect = dimensionMap.get(consumable.getDimensionName());
            if (StringUtil.isNotNull(dimensionOfSelect)) {
                Optional<DtoDimension> selectDimension = dimensionOfSelect.stream().findFirst();
                selectDimension.ifPresent(p -> consumable.setDimensionId(p.getId()));
            }
        }
    }

    /**
     * 获取消耗品基本信息实体
     *
     * @param importConsumables 导入实体
     * @return 消耗品实体
     */
    private List<DtoConsumableDetail> getDetailEntity(List<DtoExpImpConsumable> importConsumables,
                                                      List<DtoEnterprise> enterprise,
                                                      List<DtoPerson> personList,
                                                      Map<String, DtoConsumableDetail> consumableDetailMap) {
        List<DtoConsumableDetail> details = new ArrayList<>();
        for (DtoExpImpConsumable importConsumable : importConsumables) {
            DtoConsumableDetail consumable = new DtoConsumableDetail();
            if (consumableDetailMap.containsKey(importConsumable.getDetailId())) {
                consumable = consumableDetailMap.get(importConsumable.getDetailId());
            }
            consumable.setId(StringUtil.isNotEmpty(importConsumable.getDetailId()) ? importConsumable.getDetailId() : UUIDHelper.NewID());
            consumable.setParentId(importConsumable.getId());
            consumable.setStorage(new BigDecimal(importConsumable.getInventory()));
            consumable.setInventory(new BigDecimal(importConsumable.getInventory()));
            consumable.setUnitPrice(StringUtil.isEmpty(importConsumable.getUnitPrice()) ? BigDecimal.ZERO : new BigDecimal(importConsumable.getUnitPrice()));
            consumable.setKeepPlace(importConsumable.getKeepPlace());
            consumable.setCheckerId(personList.stream().filter(p -> p.getCName().equals(importConsumable.getSendWarnUserName())).map(DtoPerson::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY));
            consumable.setRemark(importConsumable.getRemark());
            consumable.setProductionCode(importConsumable.getProductionCode());
            consumable.setManufacturerName(importConsumable.getManufacturerName());
            consumable.setStorageDate(importUtils.stringToDateAllFormat(importConsumable.getStorageDate()));
            Date expiryDate;
            if (StringUtil.isNotNull(importConsumable.getExpiryDate())){
                expiryDate = importUtils.stringToDateAllFormat(importConsumable.getExpiryDate());
            }else {
                expiryDate = DateUtil.stringToDate("1753-01-01 00:00:00",DateUtil.FULL);
            }
            consumable.setExpiryDate(expiryDate);
            consumable.setSupplierName(importConsumable.getSupplierName());
            consumable.setSupplierId(enterprise.stream().filter(p -> p.getName().equals(importConsumable.getSupplierName())).map(DtoEnterprise::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY));
            details.add(consumable);
        }
        return details;
    }


    @Autowired
    public void setConsumableService(ConsumableService consumableService) {
        this.consumableService = consumableService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setConsumableDetailRepository(ConsumableDetailRepository consumableDetailRepository) {
        this.consumableDetailRepository = consumableDetailRepository;
    }

    @Autowired
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    public void setImpModifyConsumableVerify(ImpModifyConsumableVerify impModifyConsumableVerify) {
        this.impModifyConsumableVerify = impModifyConsumableVerify;
    }
}
