package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherData;
import io.swagger.models.auth.In;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 仪器接入数据操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
public interface InstrumentGatherDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoInstrumentGatherData, String> {


    /**
     * 根据仪器接入id和数据类型查询
     *
     * @param instrumentGatherId 仪器接入id
     * @param dataType           数据类型
     * @return 仪器接入数据集合
     */
    List<DtoInstrumentGatherData> findByInstrumentGatherIdAndDataType(String instrumentGatherId, String dataType);


    /**
     * 根据仪器接入id和数据类型查询
     *
     * @param instrumentGatherId 仪器接入id
     * @return 仪器接入数据集合
     */
    List<DtoInstrumentGatherData> findByInstrumentGatherId(String instrumentGatherId);
}