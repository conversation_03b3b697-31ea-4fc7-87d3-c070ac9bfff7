<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
    <groupId>com.sinoyd.lims.api</groupId>
	<artifactId>lims-api</artifactId>
	<version>fix-5.4.13-SNAPSHOT</version>
	<name>lims-api</name>
    <description>LIMS APP模块</description>
    <packaging>pom</packaging>

    <build>
        <finalName>lims-api</finalName>
    </build>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.15.RELEASE</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <frame.arch>0.0.2Cloud-SNAPSHOT</frame.arch>
        <workflow.version>1.0.5-SNAPSHOT</workflow.version>

        <api.version>fix-5.4.13</api.version>
        <pro.version>fix-5.4.13</pro.version>
    </properties>

    <modules>
        <module>lims-api-public</module>
        <module>lims-api-arch</module>
        <module>lims-api-impl</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sinoyd.lims.api</groupId>
                <artifactId>lims-api-public</artifactId>
                <version>${api.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.api</groupId>
                <artifactId>lims-api-arch</artifactId>
                <version>${api.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.api</groupId>
                <artifactId>lims-api-impl</artifactId>
                <version>${api.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.pro</groupId>
                <artifactId>lims-pro-impl</artifactId>
                <version>${pro.version}-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id><!--这里需要和settings.xml中配置的私有库名称一致-->
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>
</project>
