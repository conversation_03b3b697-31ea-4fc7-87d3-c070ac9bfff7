CREATE TABLE "TB_LIM_STANDARDMETHOD"
(
    "ID"              VARCHAR(50)                                                 NOT NULL,
    "METHODID"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "ITEMNAME"        VARCHAR(255),
    "SAMPLETYPE"      VARCHAR(255),
    "COUNTRYSTANDARD" VARCHAR(255),
    "METHODNAME"      VARCHAR(255) DEFAULT '',
    "ORGID"           VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATOR"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATEDATE"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "DOMAINID"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFIER"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "<PERSON><PERSON><PERSON>Y<PERSON><PERSON>"      TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT               CLUSTER PRIMARY KEY("ID")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_STANDARDMETHOD" IS '国家标准方法';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."ID" IS 'id';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."METHODID" IS '方法id';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."ITEMNAME" IS '项目名称';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."SAMPLETYPE" IS '监测类别（检测类型）';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."COUNTRYSTANDARD" IS '国家标准编号';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."METHODNAME" IS '标准名称';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."ORGID" IS '组织机构id';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."CREATOR" IS '创建人';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."CREATEDATE" IS '创建时间';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."DOMAINID" IS '所属实验室';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."MODIFIER" IS '修改人';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHOD"."MODIFYDATE" IS '修改时间';


CREATE TABLE "TB_LIM_STANDARDMETHODDETAIL"
(
    "ID"       VARCHAR(50)                                                NOT NULL,
    "METHODID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "OBJECTID" VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    NOT        CLUSTER PRIMARY KEY("ID")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_STANDARDMETHODDETAIL" IS '国家标准方法关联lims';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHODDETAIL"."ID" IS 'id';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHODDETAIL"."METHODID" IS '方法id';

COMMENT
ON COLUMN "TB_LIM_STANDARDMETHODDETAIL"."OBJECTID" IS '关联id（测试项目、分析方法）';

