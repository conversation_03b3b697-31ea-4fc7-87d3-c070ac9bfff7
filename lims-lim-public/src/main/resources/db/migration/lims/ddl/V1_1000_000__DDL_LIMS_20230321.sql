-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关DDL脚本 -----------
-- -------------- 相关视图 ----------------
-- ----------------------------------------------

DROP VIEW IF EXISTS TB_VIEW_Data;
create view TB_VIEW_Data as
select a.id                   AS id,
       a.redAnalyzeItemName   AS redanalyzeitemname,
       a.redAnalyzeMethodName AS redanalyzemethodname,
       a.redCountryStandard   AS redcountrystandard,
       a.status               AS status,
       a.dimension            AS dimension,
       a.analystName          AS analystname,
       a.analyzeTime          AS analyzetime,
       s.code                 AS code,
       s.redFolderName        AS redfoldername,
       s.redAnalyzeItems      AS redanalyzeitems,
       s.inspectedEnt         AS inspectedent,
       s.samplingTimeBegin    AS samplingtimebegin,
       s.status               AS samplestatus,
       st.typeName            AS typename
from TB_PRO_AnalyseData a
         join TB_PRO_Sample s on a.sampleId = s.id and a.isDeleted = 0
         join TB_LIM_Test t on a.testId = t.id
         join TB_BASE_SampleType st on st.id = t.sampleTypeId;


DROP VIEW IF EXISTS VI_PRO_AnalyseDataOfProject;
create view VI_PRO_AnalyseDataOfProject as
select distinct a.workSheetFolderId AS id
from TB_PRO_Sample s
         join TB_PRO_AnalyseData a on a.sampleId = s.id and s.status <> '样品检毕'
where exists(select 1 from TB_PRO_Project p where s.projectId = p.id and p.status = '已办结');


DROP VIEW IF EXISTS VI_PRO_CheckProjectScheme;
create view VI_PRO_CheckProjectScheme as
select count(b.id)     AS cou,
       a.id            AS id,
       a.redFolderName AS redFolderName,
       a.projectId     AS projectId,
       a.orgId         AS orgId
from TB_PRO_Sample a
         left join TB_PRO_AnalyseData b on a.isDeleted = 0 and a.id = b.sampleId and b.isDeleted = 0
group by a.id, a.redFolderName, a.redAnalyzeItems, a.projectId, a.orgId;

DROP VIEW IF EXISTS VI_PRO_ProjectCountStatisticView;
create view VI_PRO_ProjectCountStatisticView as
select uuid()              AS id,
       p.id                AS projectId,
       pt.id               AS projectTypeId,
       pt.name             AS projectTypeName,
       ptp.name            AS parentName,
       p.inceptTime        AS inceptTime,
       s.samplingTimeBegin AS samplingTime,
       p.orgId             AS orgId
from TB_PRO_Project p
         join TB_LIM_ProjectType pt on p.isDeleted = 0 and p.projectTypeId = pt.id
    and p.parentId = '00000000-0000-0000-0000-000000000000'
         left join TB_LIM_ProjectType ptp on ptp.id = pt.parentId
         left join TB_PRO_Sample s on s.projectId = p.id;

DROP VIEW IF EXISTS VI_PRO_ProjectYYSampleCountView;
create view VI_PRO_ProjectYYSampleCountView as
select uuid()              AS id,
       s.projectId         AS projectId,
       p.inceptTime        AS inceptTime,
       a.id                AS analyseDataId,
       a.sampleId          AS sampleId,
       s.sampleTypeId      AS sampleTypeId,
       st.typeName         AS sampleTypeName,
       p.projectTypeId     AS projectTypeId,
       a.isDeleted         AS isDeleted,
       s.samplingTimeBegin AS samplingTime,
       s.orgId             AS orgId
from TB_PRO_Sample s
         join TB_PRO_Project p on s.projectId = p.id and s.isDeleted = 0 and p.isDeleted = 0 and s.sampleCategory = 0
         join TB_PRO_AnalyseData a on a.sampleId = s.id
         join TB_BASE_SampleType st on st.id = s.sampleTypeId
union all
select uuid()              AS id,
       s.projectId         AS projectId,
       p.inceptTime        AS inceptTime,
       a.id                AS analyseDataId,
       a.sampleId          AS sampleId,
       s.sampleTypeId      AS sampleTypeId,
       st.typeName         AS sampleTypeName,
       p.projectTypeId     AS projectTypeId,
       a.isDeleted         AS isDeleted,
       s.samplingTimeBegin AS samplingTime,
       s.orgId             AS orgId
from TB_PRO_Sample qs
         join TB_PRO_Sample s on qs.isDeleted = 0 and s.isDeleted = 0 and qs.sampleCategory = 2
    and qs.associateSampleId = s.id
         join TB_PRO_Project p on p.isDeleted = 0 and s.projectId = p.id
         join TB_PRO_AnalyseData a on qs.id = a.sampleId
         join TB_BASE_SampleType st on s.sampleTypeId = st.id;

DROP VIEW IF EXISTS VI_PRO_ProjectZKSampleCountView;
create view VI_PRO_ProjectZKSampleCountView as
select uuid()              AS id,
       s.projectId         AS projectId,
       p.inceptTime        AS inceptTime,
       a.id                AS analyseDataId,
       a.sampleId          AS sampleId,
       s.sampleTypeId      AS sampleTypeId,
       st.typeName         AS sampleTypeName,
       p.projectTypeId     AS projectTypeId,
       a.isDeleted         AS isDeleted,
       s.samplingTimeBegin AS samplingTime,
       s.orgId             AS orgId
from TB_PRO_Sample qs
         join TB_PRO_Sample s on qs.associateSampleId = s.id and qs.isDeleted = 0
    and s.isDeleted = 0 and qs.sampleCategory = 1
         join TB_PRO_Project p on s.projectId = p.id and p.isDeleted = 0
         join TB_PRO_AnalyseData a on qs.id = a.sampleId and a.qcType not in (16, 32)
         join TB_BASE_SampleType st on s.sampleTypeId = st.id;


DROP VIEW IF EXISTS VI_PRO_QCSampleView;
create view VI_PRO_QCSampleView as
select s.id                AS id,
       s.code              AS code,
       s.redFolderName     AS redFolderName,
       s.redAnalyzeItems   AS redAnalyzeItems,
       s.samplingPersonId  AS samplingPersonId,
       s.sampleTypeId      AS sampleTypeId,
       s.inceptTime        AS inceptTime,
       s.status            AS status,
       s.associateSampleId AS associateSampleId,
       qc.qcGrade          AS qcGrade,
       qc.qcType           AS qcType,
       qs.code             AS associateSampleCode,
       p.cName             AS samplingPersonName,
       st.typeName         AS sampleTypeName,
       st.parentId         AS bigSampleTypeId,
       s.orgId             AS orgId
from TB_PRO_Sample s
         join TB_LIM_Person p on s.samplingPersonId = p.id and s.isDeleted = 0 and s.sampleCategory = 1
         left join TB_PRO_Sample qs on s.associateSampleId = qs.id
         join TB_BASE_SampleType st on st.id = s.sampleTypeId
         left join TB_PRO_QualityControl qc on s.qcId = qc.id;

