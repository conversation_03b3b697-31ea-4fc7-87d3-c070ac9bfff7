package com.sinoyd.base.dto.customer;

import com.sinoyd.frame.base.entity.BaseEntity;

import lombok.Data;

import java.util.*;

//  @JsonInclude(JsonInclude.Include.NON_NULL)

/**
 * 树节点实体类
 * <AUTHOR>
 * @version V1.0.0 2019/3/13
 * @since V100R001
 */
@Data
public class TreeNode implements BaseEntity {

    private String id;

    private String parentId;

    private String label;

    private Integer category;

    private String type;

    private Integer orderNum;

    private Boolean isLeaf;

    private List<TreeNode> children;

    //预留三个字段
    private String extent1;
    private String extent2;
    private String extent3;
}