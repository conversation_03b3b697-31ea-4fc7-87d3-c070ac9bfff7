package com.sinoyd.lims.pro.listener;

import com.sinoyd.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.pro.service.AnalyseDataService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 检测单提交更新样品最新检测时间监听通知
 *
 * <AUTHOR>
 * @version V1.0.0 2020-05-11
 * @since V100R001
 */
@Component
@Slf4j
public class SampleLastNewSubmitListener implements ExecutionListener {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void notify(DelegateExecution execution) {
        AnalyseDataService service = SpringContextAware.getBean(AnalyseDataService.class);
        service.updateLastNewSubmitTime(execution.getProcessBusinessKey());
    }
}
