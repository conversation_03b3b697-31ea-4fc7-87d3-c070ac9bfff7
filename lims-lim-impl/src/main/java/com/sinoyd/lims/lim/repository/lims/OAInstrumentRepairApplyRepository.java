package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.lims.lim.dto.lims.DtoOAInstrumentRepairApply;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;


/**
 * OAInstrumentRepairApply数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/4/3
 * @since V100R001
 */
public interface OAInstrumentRepairApplyRepository extends IBaseJpaPhysicalDeleteRepository<DtoOAInstrumentRepairApply, String> {

}