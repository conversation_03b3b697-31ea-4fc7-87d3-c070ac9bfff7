package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SamplingFrequencyTemp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoProjectApproval实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SamplingFrequencyTemp")
@Data
@DynamicInsert
public class DtoSamplingFrequencyTemp extends SamplingFrequencyTemp {
}
