package com.sinoyd.lims.lim.service.transform;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import java.lang.reflect.Field;
import java.util.*;

/**
 *  数据迁移导入通用校验接口默认实现
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
public class ImportCommonCheckImpl implements ImportCommonCheck{
    /**
     * 重复行号
     */
    private int isRepeatRowNum  = 0;

    @Override
    public void checkSeetDataRepeat(ExcelVerifyHandlerResult result, StringBuilder failStr, List list, Object o, List fields) throws Exception{
        if(isFieldDataRepeat(list,o,fields,true)){
            result.setSuccess(false);
            failStr.append("；").append("数据与第").append(isRepeatRowNum+1).append("行重复");
        }
    }

    @Override
    public void checkRepoDataRepeat(ExcelVerifyHandlerResult result, StringBuilder failStr, List list, Object o, List fields) throws Exception{
        if(isFieldDataRepeat(list,o,fields,false)){
            result.setSuccess(false);
            failStr.append("；数据在系统中已存在");
        }
    }

    /**
     * 数据比对
     * @param list         原有数据集
     * @param entity       校验实体
     * @param fields       校验字段
     * @return             是否重复
     * @throws Exception   异常
     */
    private boolean isFieldDataRepeat(List list, Object entity, List fields,boolean needRepeatRowNum) throws Exception {
        Map<String,Object> originDataMap = new HashMap<>();
        List< Map<String,Object>> compareDataList = new ArrayList<>();
        for (Object fieldName:fields ) {
            Field[] entityFields = entity.getClass().getDeclaredFields();
            for (Field field:entityFields) {
                if(field.getName().equals(fieldName.toString())){
                    field.setAccessible(true);
                    String entityFiledValue = (String) field.get(entity);
                    originDataMap.put(field.getName(),entityFiledValue);
                }
            }
        }
        for (Object checkEntity:list) {
            Map<String,Object> compareDataMap = new HashMap<>();
            List<Field> checkEntityAllFieldList = new ArrayList<>();
            checkEntityAllFieldList.addAll(Arrays.asList(checkEntity.getClass().getDeclaredFields()));
            checkEntityAllFieldList.addAll(Arrays.asList(checkEntity.getClass().getSuperclass().getDeclaredFields()));
            for (Object fieldName:fields ){
                for (Field checkEntityField:checkEntityAllFieldList) {
                    if(checkEntityField.getName().equals(fieldName.toString())){
                        checkEntityField.setAccessible(true);
                        String checkEntityFiledValue = (String) checkEntityField.get(checkEntity);
                        compareDataMap.put(fieldName.toString(),checkEntityFiledValue);
                    }
                    if("rowNum".equals(checkEntityField.getName())){
                        checkEntityField.setAccessible(true);
                        compareDataMap.put("rowNum", checkEntityField.get(checkEntity));
                    }
                }
            }
            compareDataList.add(compareDataMap);
        }

        for ( Map<String,Object> singleMap:compareDataList) {
            boolean allFieldEqualFlag = true;
            for (Object fieldName:fields ){
                if(!singleMap.get(fieldName).equals(originDataMap.get(fieldName))){
                    allFieldEqualFlag = false;
                }
            }
            if(allFieldEqualFlag){
                if(needRepeatRowNum){
                    isRepeatRowNum = (int) singleMap.get("rowNum");
                }
                return true;
            }
        }
        return  false;
    }
}
