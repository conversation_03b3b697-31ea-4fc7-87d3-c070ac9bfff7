package com.sinoyd.lims.pro.strategy.strategy.documentFileName;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.ProjectService;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 项目附件名称生成
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/28
 */
@Component(IFileNameConstant.FileNameStrategyKey.PROJECT_FILENAME)
public class ProjectFileNameStrategy extends AbsDocumentFileNameStrategy {

    private ProjectService projectService;

    @Override
    public Map<String, String> generateDocumentName(Map<String,Object> map) {
        Map<String, String> projectMap = new HashMap<>();
        if (map.containsKey(EnumPRO.EnumDocumnetName.项目.getValue())) {
            String reportId = map.get(EnumPRO.EnumDocumnetName.项目.getValue()).toString();
            if (StringUtil.isNotEmpty(reportId)) {
                DtoProject project = projectService.findOne(reportId);
                if (StringUtil.isNotNull(project)) {
                    projectMap.put("projectCode", project.getProjectCode());
                    projectMap.put("projectName", project.getProjectName());
                }
            }
        }
        return projectMap;
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }
}
