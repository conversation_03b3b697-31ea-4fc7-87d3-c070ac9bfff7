CREATE  TRIGGER "AFTER_UPDATE_PARAMSTESTFORMULA"
    AFTER  UPDATE
    ON "TB_LIM_PARAMSTESTFORMULA"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    if new.objId != old.objId or (new.objId is null and old.objId is not null) or
       (new.objId is not null and old.objId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'objId',
        old.objId, new.objId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.paramsId != old.paramsId or (new.paramsId is null and old.paramsId is not null) or
            (new.paramsId is not null and old.paramsId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'paramsId', old.paramsId, new.paramsId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.paramsName != old.paramsName or (new.paramsName is null and old.paramsName is not null) or
            (new.paramsName is not null and old.paramsName is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'paramsName', old.paramsName, new.paramsName, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.alias != old.alias or (new.alias is null and old.alias is not null) or
            (new.alias is not null and old.alias is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'alias',
        old.alias, new.alias, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.defaultValue != old.defaultValue or (new.defaultValue is null and old.defaultValue is not null) or
            (new.defaultValue is not null and old.defaultValue is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'defaultValue', old.defaultValue, new.defaultValue, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
            (new.orderNum is not null and old.orderNum is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'orderNum', old.orderNum, new.orderNum, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.aliasInReport != old.aliasInReport or (new.aliasInReport is null and old.aliasInReport is not null) or
            (new.aliasInReport is not null and old.aliasInReport is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'aliasInReport', old.aliasInReport, new.aliasInReport, new.orgId,
        '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.dimensionId != old.dimensionId or (new.dimensionId is null and old.dimensionId is not null) or
            (new.dimensionId is not null and old.dimensionId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'dimensionId', old.dimensionId, new.dimensionId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.dimension != old.dimension or (new.dimension is null and old.dimension is not null) or
            (new.dimension is not null and old.dimension is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'dimension', old.dimension, new.dimension, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.sourceType != old.sourceType or (new.sourceType is null and old.sourceType is not null) or
            (new.sourceType is not null and old.sourceType is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'sourceType', old.sourceType, new.sourceType, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.isMust != old.isMust or (new.isMust is null and old.isMust is not null) or
            (new.isMust is not null and old.isMust is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'isMust',
        old.isMust, new.isMust, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.isEditable != old.isEditable or (new.isEditable is null and old.isEditable is not null) or
            (new.isEditable is not null and old.isEditable is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'isEditable', old.isEditable, new.isEditable, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.detectionLimit != old.detectionLimit or
            (new.detectionLimit is null and old.detectionLimit is not null) or
            (new.detectionLimit is not null and old.detectionLimit is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'detectionLimit', old.detectionLimit, new.detectionLimit, new.orgId,
        '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.calculationMode != old.calculationMode or
            (new.calculationMode is null and old.calculationMode is not null) or
            (new.calculationMode is not null and old.calculationMode is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramstestformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'calculationMode', old.calculationMode, new.calculationMode, new.orgId,
        '00000000-0000-0000-0000-000000000000');
END IF;
end;
