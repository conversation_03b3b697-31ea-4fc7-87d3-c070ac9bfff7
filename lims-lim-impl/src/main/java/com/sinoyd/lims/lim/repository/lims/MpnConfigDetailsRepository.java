package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoMpnConfigDetails;

import java.util.Collection;
import java.util.List;

/**
 * MpnConfigDetails仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2025/02/12
 * @since V100R001
 */
public interface MpnConfigDetailsRepository extends IBaseJpaPhysicalDeleteRepository<DtoMpnConfigDetails, String> {

    /**
     * 根据mpnConfigId 查询
     *
     * @param mpnConfigId mnp配置Id
     * @return 配置详情
     */
    List<DtoMpnConfigDetails> findByMpnConfigId(String mpnConfigId);

    /**
     * 根据mpnConfigIds 查询
     *
     * @param mpnConfigIds mnp配置Ids
     * @return 配置详情
     */
    List<DtoMpnConfigDetails> findByMpnConfigIdIn(Collection<String> mpnConfigIds);

}
