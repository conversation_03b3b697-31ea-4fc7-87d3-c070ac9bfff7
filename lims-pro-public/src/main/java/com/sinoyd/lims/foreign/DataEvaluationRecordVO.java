package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * 评价明细
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Data
public class DataEvaluationRecordVO {
    /**
     * id
     */
    private String id;

    /**
     * 关联id
     */
    private String objectId;

    /**
     * 类型
     */
    private Integer objectType;

    /**
     * 评价标准id
     */
    private String evaluationId;

    /**
     * 评价等级id
     */
    private String evaluationLevelId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 上限运算符
     */
    private String upperLimitSymble;

    /**
     * 上限
     */
    private String upperLimitValue;

    /**
     * 下限运算符
     */
    private String lowerLimitSymble;

    /**
     * 下限
     */
    private String lowerLimitValue;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
