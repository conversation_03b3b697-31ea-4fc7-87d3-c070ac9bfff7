package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoOutSample;
import com.sinoyd.lims.pro.dto.customer.DtoOutSampleSave;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import org.aspectj.lang.annotation.DeclareAnnotation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 质控样品类
 *
 * <AUTHOR>
 * @version V1.0.0 2020/02/11
 * @since V100R001
 */
@Service
public class QMSampleServiceImpl extends BaseJpaServiceImpl<DtoSample, String, SampleRepository> implements QMSampleService {

    //#region 注入

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    @Lazy
    private ReceiveSubSampleRecordService receiveSubSampleRecordService;

    @Autowired
    @Lazy
    private SampleFolderService sampleFolderService;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    @Lazy
    private QualityManageService qualityManageService;

    @Autowired
    private SamplingFrequencyRepository samplingFrequencyRepository;

    @Autowired
    @Lazy
    private QCProjectService qcProjectService;

    @Autowired
    private LocalTaskPeopleCompareRepository localTaskPeopleCompareRepository;

    //#endregion 注入

    @Transactional
    @Override
    public DtoOutSample saveQmSample(DtoOutSample dto) {
        int count = StringUtils.isNotNullAndEmpty(dto.getCode()) ? repository.countByCode(dto.getCode()) : 0;
        if (count > 0) {
            throw new BaseException("样品编号已经存在");
        }
        DtoProject project = projectRepository.findOne(dto.getProjectId());
        dto.setId(UUIDHelper.NewID());
        List<DtoReceiveSampleRecord> recordList = new ArrayList<>();
        if(qcProjectService.isLocalPeopleCompare(project.getId())){
            List<String> receiveIds = localTaskPeopleCompareRepository.findByProjectId(project.getId()).stream().map(DtoLocalTaskPeopleCompare::getReceiveId)
                    .collect(Collectors.toList());
            recordList = receiveSampleRecordRepository.findAll(receiveIds);
        }else{
            DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(dto.getReceiveId());
            recordList.add(record);
        }
        recordList.forEach(record->this.saveQMSample(dto, record, project));
        String watchSpot = dto.getRedFolderName().replace(String.format("(%d-%d)", dto.getCycleOrder(), dto.getTimesOrder()), "") + String.format("(%d-%d)", dto.getCycleOrder(), dto.getTimesOrder());
        String comment = String.format("新增了样品%s", watchSpot);
        newLogService.createLog(dto.getReceiveId(), comment, "", EnumLogType.送样单样品信息.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.增加样品.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        return dto;
    }

    /**
     * 保存外部样品信息
     *
     * @param qmSampleSave 样品信息
     * @param record       送样单
     * @param project      项目信息
     */
    private void saveQMSample(DtoOutSample qmSampleSave,
                              DtoReceiveSampleRecord record,
                              DtoProject project) {
        DtoSample dto = new DtoSample();
        BeanUtils.copyProperties(qmSampleSave, dto,"id");
        dto.setReceiveId(record.getId());
        DtoSamplingFrequency frequency = sampleFolderService.addQMFolder(dto);
        dto.setPreparedStatus(EnumPreParedStatus.未制备.getValue());
        dto.setSampleFolderId(frequency.getSampleFolderId());
        dto.setSamplingFrequencyId(frequency.getId());
        dto.setRedFolderName(frequency.getRedFolderName());
        dto.setInceptTime(new Date());
        dto.setSamplingTimeBegin(record.getSamplingTime());
        dto.setSamplingTimeEnd(record.getSamplingTime());
        dto.setSampleCategory(EnumSampleCategory.原样.getValue());
        dto.setSampleOrder(1);
        dto.setSamplingPersonId(PrincipalContextUser.getPrincipal().getUserId());

        dto.setAssociateSampleId(UUIDHelper.GUID_EMPTY);
        dto.setSubProjectId(UUIDHelper.GUID_EMPTY);
        dto.setDataChangeStatus(EnumSampleChangeStatus.未变更.getValue());
        dto.setQcId(UUIDHelper.GUID_EMPTY);
        if (!StringUtils.isNotNullAndEmpty(dto.getParentSampleId())) {
            dto.setParentSampleId(UUIDHelper.GUID_EMPTY);
        }
        dto.setSamplingConfig(EnumSamplingConfig.未分配.getValue());
        dto.setIsKeep(false);
        dto.setIsPrint(EnumPrintStatus.未打印.getValue());
        if (StringUtil.isNull(dto.getBlindType())) {
            dto.setBlindType(EnumSampleBlindType.非盲样.getValue());
        }
        dto.setConsistencyValidStatus(0);
        dto.setSignerId(UUIDHelper.GUID_EMPTY);
        dto.setSignTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        dto.setSamplingRecordId(UUIDHelper.GUID_EMPTY);
        dto.setIsOutsourcing(0);
        dto.setSamKind(0);
        dto.setIsQualified(false);
        dto.setIsReturned(false);
        dto.setKeepLongTime(0);
        dto.setLoseEfficacyTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        dto.setLastNewSubmitTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));

        dto.setInnerReceiveStatus(EnumInnerReceiveStatus.不能领取.getValue());
        //默认样品未领样
        dto.setStatus(EnumSampleStatus.样品未领样.toString());
        if (record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue())) {
            dto.setStatus(EnumSampleStatus.样品未领样.toString());
            dto.setAnanlyzeStatus(EnumAnalyzeStatus.不能分析.getValue());
        } else {
            if (!record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue())) {
                record.setStatus(EnumLIM.EnumReceiveRecordStatus.已经送样.toString());
                record.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.已经送样.getValue());
                receiveSampleRecordService.update(record);
            }

            if (StringUtil.isNotNull(qmSampleSave.getQualityManage())) {
                List<DtoSample> list = Collections.singletonList(dto);
                //存在不分包的实验室指标
                if (qmSampleSave.getQualityManage().stream().anyMatch(p -> !p.getIsCompleteField() && !p.getIsOutsourcing())) {
                    DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumPRO.EnumSubRecordType.分析.getValue());
                    if (StringUtil.isNull(subRecord)) {
                        receiveSubSampleRecordService.createSubRecord(record, list, EnumPRO.EnumSubRecordType.分析.getValue());
                        dto.setStatus(EnumPRO.EnumSampleStatus.样品未领样.toString());
                        dto.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue());
                        dto.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.不能分析.getValue());
                    } else {
                        if ((subRecord.getSubStatus() & (EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue())) != 0) {
                            dto.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
                            dto.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
                            dto.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                        } else {
                            dto.setStatus(EnumPRO.EnumSampleStatus.样品未领样.toString());
                            dto.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue());
                            dto.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.不能分析.getValue());
                        }
                    }
                }
                //存在不分包的现场指标
                if (qmSampleSave.getQualityManage().stream().anyMatch(p -> p.getIsCompleteField() && !p.getIsOutsourcing())) {
                    DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumPRO.EnumSubRecordType.现场.getValue());
                    if (StringUtil.isNull(subRecord)) {
                        receiveSubSampleRecordService.createSubRecord(record, list, EnumPRO.EnumSubRecordType.现场.getValue());
                    }
                    dto.setStatus(EnumPRO.EnumSampleStatus.样品待检.toString());
                    //只存在现场数据，改成已经领取
                    if (dto.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue())) {
                        dto.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
                    }
                    dto.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                }
                //所有指标均分包
                if (qmSampleSave.getQualityManage().stream().allMatch(DtoQualityManage::getIsOutsourcing)) {
                    dto.setStatus(EnumSampleStatus.样品检毕.toString());
                    dto.setInnerReceiveStatus(EnumInnerReceiveStatus.不能领取.getValue());
                    //若送样单下存在实验室领样单，则调整领取状态为对应的
                    DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumSubRecordType.分析.getValue());
                    if (StringUtil.isNotNull(subRecord)) {
                        if ((subRecord.getSubStatus() & (EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue())) != 0) {
                            dto.setInnerReceiveStatus(EnumInnerReceiveStatus.已经领取.getValue());
                        } else {
                            dto.setInnerReceiveStatus(EnumInnerReceiveStatus.可以领取.getValue());
                        }
                    }
                    dto.setAnanlyzeStatus(EnumAnalyzeStatus.不需要分析.getValue());
                }
            }
        }

        dto.setSamplingStatus(EnumSamplingStatus.已经完成取样.getValue());
        dto.setStoreStatus(EnumStoreStatus.不能存储.getValue());
        dto.setMakeStatus(EnumMakeStatus.不需要制样.getValue());
        sampleService.saveSampleParams(dto.getId(), qmSampleSave.getParamsData());
        dto.setSortNum("");
        DtoSample sample = repository.save(dto);
        List<DtoQualityManage> qualityManages = new ArrayList<>(qmSampleSave.getQualityManage());
        qualityManages.forEach(v->v.setId(UUIDHelper.NewID()));
        qualityManageService.addQualityManages(dto, record, qualityManages);
        String comment = String.format("新增了样品%s", sampleService.getSampleName(dto, frequency.getWatchSpot()));
        newLogService.createLog(dto.getId(), comment, "", EnumLogType.样品增删作废.getValue(), EnumLogObjectType.样品.getValue(), EnumLogOperateType.增加样品.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        proService.checkSample(Collections.singletonList(sample), project);

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.添加外部样品, dto.getProjectId(), dto.getReceiveId());
                    }
                }
        );
    }

    @Transactional
    @Override
    public void updateQmSample(DtoOutSampleSave sampleSave) {
        String fieldName = sampleSave.getField();
        DtoSample sample = repository.findOne(sampleSave.getId());
        String watchSpot = sample.getRedFolderName().replace(String.format("(%d-%d)", sample.getCycleOrder(), sample.getTimesOrder()), "");
        if (StringUtil.isNotEmpty(fieldName)) {
            //#region 获取修改信息
            Object newValue;
            Object oldValue;
            String annotation = "";
            try {
                Field field = DtoOutSampleSave.class.getDeclaredField(fieldName);
                field.setAccessible(true);
                newValue = field.get(sampleSave);
                for (Annotation anno : field.getAnnotations()) {
                    if (anno instanceof DeclareAnnotation) {
                        annotation = ((DeclareAnnotation) anno).value();
                        break;
                    }
                }
                if (fieldName.equals("redFolderName")) {
                    oldValue = watchSpot;
                } else {
                    Field samField = DtoSample.class.getSuperclass().getDeclaredField(fieldName);
                    samField.setAccessible(true);
                    oldValue = samField.get(sample);
                }
            } catch (NoSuchFieldException | IllegalAccessException ex) {
                System.out.println(ex.getMessage());
                throw new BaseException("异常错误");
            }
            //#endregion

            sampleSave.loadFromSave(sample);
            if (fieldName.equals("code")) {
                if (StringUtil.isNull(sample.getCode())) {
                    throw new BaseException("样品编号不能为空");
                }
                Integer count = repository.countByCodeAndIdNot(sample.getCode(), sample.getId());
                if (count > 0) {
                    throw new BaseException("样品编号已经存在");
                }
            }
            if ((fieldName.equals("redFolderName") || fieldName.equals("cycleOrder") || fieldName.equals("timesOrder"))) {
                //核对点位
                if (fieldName.equals("redFolderName")) {
                    watchSpot = sampleSave.getRedFolderName();
                    String redFolderName = watchSpot + String.format("(%d-%d)", sample.getCycleOrder(), sample.getTimesOrder());
                    sample.setRedFolderName(redFolderName);
                    DtoSampleFolder folder = sampleFolderRepository.findOne(sample.getSampleFolderId());
                    folder.setWatchSpot(watchSpot);
                    commonRepository.merge(folder);
                } else {
                    DtoSamplingFrequency samplingFrequency = samplingFrequencyRepository.findOne(sample.getSamplingFrequencyId());
                    samplingFrequency.setPeriodCount(sample.getCycleOrder());
                    samplingFrequency.setTimePerPeriod(sample.getTimesOrder());
                    commonRepository.merge(samplingFrequency);
                    sample.setRedFolderName(watchSpot + String.format("(%d-%d)", sample.getCycleOrder(), sample.getTimesOrder()));
                }
            }
            //修改样品
            comRepository.merge(sample);

            String comment = String.format("修改了样品%s信息,%s由'%s'修改为'%s'", watchSpot, annotation, oldValue, newValue);
            newLogService.createLog(sample.getId(), comment, "", EnumLogType.样品信息.getValue(), EnumLogObjectType.样品.getValue(), EnumLogOperateType.修改样品.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
    }
}
