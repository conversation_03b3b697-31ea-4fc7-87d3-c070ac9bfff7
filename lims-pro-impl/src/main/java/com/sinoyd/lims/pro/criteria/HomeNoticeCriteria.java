package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 首页公共的
 * <AUTHOR>
 * @version V1.0.0 20200319
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HomeNoticeCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and n.isRelease=:isRelease");
        values.put("isRelease", true);
        return condition.toString();
    }
}
