package com.sinoyd.base.criteria;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;


/**
 * 消耗品详单查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0 2019/04/30
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsumableDetailCriteria extends BaseCriteria {

    // 库存类别（1现有库存 2所有库存）
    private Integer inventoryType;
    // 消耗品/标准样品id
    private String consumableId;

    /**
     * 开始入库日期
     */
    private String startStorageDate;

    /**
     * 结束入库时间
     */
    private String endStorageDate;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if ( StringUtil.isNotNull(inventoryType) && inventoryType.equals(1)) {
            condition.append(" and storage != 0");
        }
        if (StringUtils.isNotNullAndEmpty(consumableId)) {
            condition.append(" and (parentId = :consumableId)");
            values.put("consumableId", this.consumableId);
        }

        if (StringUtil.isNotEmpty(this.startStorageDate)){
            Date date = DateUtil.stringToDate(this.startStorageDate, DateUtil.YEAR);
            condition.append(" and x.storageDate >= :startStorageDate ");
            values.put("startStorageDate", date);
        }
        Calendar calendar = new GregorianCalendar();
        if (StringUtil.isNotEmpty(this.endStorageDate)) {
            Date date = DateUtil.stringToDate(this.endStorageDate, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and x.storageDate < :endStorageDate ");
            values.put("endStorageDate", date);
        }

        return condition.toString();
    }
}