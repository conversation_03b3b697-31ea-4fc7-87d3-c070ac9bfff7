package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;

import java.util.List;

/**
 *  测试项目关联监管平台信息service
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2022/11/28
 */
public interface FolderExtendService {

    /**
     * 批量修改分析方法
     * @param test 参数
     */
    void batchUpdateAnalyzeMethod(DtoTest test);

    /**
     * 批量修改采样方法
     * @param test 参数
     */
    void batchUpdateSamplingMethod(DtoTest test);

    /**
     * 根据项目id查询点位
     * @param projectId 项目id
     * @return List<DtoSampleFolder>
     */
    List<DtoSampleFolder> findFoldersByProjectId(String projectId);

    /**
     * 根据点位id查询改点位下所有测试项目配置
     * @param criteria 条件
     * @return List<DtoFolderExtend>
     */
    List<DtoTest> findBySampleFolderId(BaseCriteria criteria);

    /**
     * 根据项目id查询该项目下所有测试项目
     * @param projectId 项目id
     * @return List<DtoTest>
     */
    List<DtoTest> findByProjectId(String projectId);

}
