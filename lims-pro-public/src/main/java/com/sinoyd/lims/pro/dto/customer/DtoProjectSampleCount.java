package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 项目样品数量统计的结果数据
 */
@Data
public class DtoProjectSampleCount {

    /**
     * 样品类型
     */
    private String sampleTypeName;

    /**
     * 分析数据
     */
    private Integer analyseDataCount;

    /**
     * 样品数
     */
    private Integer sampleCount;

    /**
     * 质控样数
     */
    private Integer qualityControlCount;

    /**
     * 默认的构造
     */
    public DtoProjectSampleCount(){

    }

    public DtoProjectSampleCount(String sampleTypeName,
                                 Integer analyseDataCount,
                                 Integer sampleCount) {
        this.sampleTypeName = sampleTypeName;
        this.analyseDataCount = analyseDataCount;
        this.sampleCount = sampleCount;
    }
}
