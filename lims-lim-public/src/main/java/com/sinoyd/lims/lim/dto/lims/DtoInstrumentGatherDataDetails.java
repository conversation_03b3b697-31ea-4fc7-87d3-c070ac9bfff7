package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.InstrumentGatherDataDetails;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * InstrumentGatherDataDetails实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_InstrumentGatherDataDetails")
@Data
@DynamicInsert
public class DtoInstrumentGatherDataDetails extends InstrumentGatherDataDetails {


    /**
     * 排序值
     */
    @Transient
    private Integer orderNum;

    public DtoInstrumentGatherDataDetails() {
    }

    public DtoInstrumentGatherDataDetails(String instrumentGatherDataId,
                                          String instrumentGatherParamsId,
                                          String parmaName,
                                          String paramValue,
                                          String dimension) {
        this.setInstrumentGatherDataId(instrumentGatherDataId);
        this.setInstrumentGatherParamsId(instrumentGatherParamsId);
        this.setParmaName(parmaName);
        this.setParamValue(paramValue);
        this.setDimension(dimension);
    }


}