package com.sinoyd.lims.pro.repository;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;


/**
 * ParamsData数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface ParamsDataRepository extends IBaseJpaRepository<DtoParamsData, String>, LimsRepository<DtoParamsData, String> {

    /**
     * 查询对应关联id和关联类型下的参数数据
     *
     * @param objectType 关联类型
     * @param objectId   关联id
     * @return 对应关联id和关联类型下的参数数据
     */
    List<DtoParamsData> findByObjectTypeAndObjectId(Integer objectType, String objectId);

    /**
     * 查询对应关联id集合和关联类型下的参数数据
     *
     * @param objectIds  关联id集合
     * @param objectType 关联类型
     * @return 对应关联id集合和关联类型下的参数数据
     */
    List<DtoParamsData> findByObjectIdInAndObjectType(Collection<String> objectIds, Integer objectType);

    /**
     * 查询对应关联id集合和关联类型下的参数数据
     *
     * @param objectIds      关联id集合
     * @param objectType     关联类型
     * @param paramsConfigId 配置id
     * @return 对应关联id集合和关联类型下的参数数据
     */
    List<DtoParamsData> findByObjectTypeAndObjectIdInAndParamsConfigId(Integer objectType, Collection<String> objectIds, String paramsConfigId);

    /**
     * 查询对应关联id集合和关联类型下的参数数据
     *
     * @param objectType      关联类型
     * @param objectIds       关联id集合
     * @param paramsConfigIds 配置id集合
     * @return 对应关联id集合和关联类型下的参数数据
     */
    List<DtoParamsData> findByObjectTypeAndObjectIdInAndParamsConfigIdIn(Integer objectType, Collection<String> objectIds, Collection<String> paramsConfigIds);

    /**
     * 根据工作单Id集合查询配置的参数数据
     *
     * @param objectIds 小工作单Id集合
     * @return
     */
    List<DtoParamsData> findByObjectIdIn(List<String> objectIds);
}