package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;

import java.util.List;

/**
 * 迁移导入前检查实体
 *
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/06/17
 */
@Data
public class DtoImportCheck {

    /**
     * 检查项
     */
    private String checkItem;

    /**
     * 检查类型
     */
    private String checkType;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 处理描述
     */
    private String description;

    /**
     * 检查数据详情
     */
    private List<DtoDataCheck> dataChecks;

    public DtoImportCheck() {
    }

    public DtoImportCheck(String checkItem, String checkType, Integer num, String description, List<DtoDataCheck> dataChecks) {
        this.checkItem = checkItem;
        this.checkType = checkType;
        this.num = num;
        this.description = description;
        this.dataChecks = dataChecks;
    }
}
