package com.sinoyd.lims.pro.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 质控样评价明细信息
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/21
 * @since V100R001
 */
@Data
@AllArgsConstructor
public class DtoQcSampleEvaluateDetail {

    /**
     * 质控评价id
     */
    private String evaluateId;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 检查项
     */
    private String checkItem;

    /**
     * 评判方式（枚举EnumJudgingMethod：1.限值判定，2.小于检出限，3.回收率，4.相对偏差，5.相对误差，7.穿透率，6.绝对偏差）
     */
    private Integer judgingMethod;

    /**
     * 检查项值
     */
    private String checkItemValue;

    /**
     * 允许限值
     */
    private String allowLimit;

    /**
     * 是否合格 (1：合格 0：合格 2：未评价)
     */
    private String isPass;

    /**
     * 点位名称
     */
    private String sampleFolderName;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 报告id列表
     */
    private List<String> reportIdList;

    /**
     * 质控类型名称
     */
    private String qcTypeName;

    public DtoQcSampleEvaluateDetail() {}
}
