package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;

import java.util.List;


/**
 * ParamsTestFormula操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
public interface ParamsTestFormulaService extends IBaseJpaService<DtoParamsTestFormula, String> {
    /**
     * 删除参数公式相关参数
     *
     * @param objId 参数公式id
     * @return 返回删除行数
     */
    Integer deleteByObjId(String objId);


    /**
     * 根据公式id查询相应的公式参数
     *
     * @param objIds 公式ids
     * @return 返回想要的公式参数数据
     */
    List<DtoParamsTestFormula> findByObjIds(List<String> objIds);


    /**
     * 根据公式id查询相应的公式参数
     *
     * @param objectId 对象id
     * @return 返回想要的公式的数据
     */
    List<DtoParamsTestFormula> findByObjectId(String objectId);
}