package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * WorkSheet查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkSheetCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String workSheetFolderId;


    /**
     * 是否分包
     */
    private Boolean isOutsourcing;


    /**
     * 是否现场
     */
    private Boolean isCompleteField;

    /**
     * 分析数据ids
     */
    private List<String> analyzeDataIds;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and b.isDeleted = 0 and a.isDeleted = 0 ");
        condition.append(" and a.sampleId = b.id");
        condition.append(" and b.sampleTypeId = c.id");
        if (StringUtil.isNotNull(this.isOutsourcing)) {
            if(!this.isOutsourcing) {
                condition.append(" and a.isOutsourcing = :isOutsourcing and a.isSamplingOut = :isOutsourcing");
            }else{
                condition.append(" and ( a.isOutsourcing = :isOutsourcing or a.isSamplingOut = :isOutsourcing ) ");
            }
            values.put("isOutsourcing", this.isOutsourcing);
        }
        if (StringUtil.isNotNull(this.isCompleteField)) {
            condition.append(" and a.isCompleteField = :isCompleteField");
            values.put("isCompleteField", this.isCompleteField);
        }
        if (StringUtils.isNotNullAndEmpty(this.workSheetFolderId) && !this.workSheetFolderId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and a.workSheetFolderId = :workSheetFolderId");
            values.put("workSheetFolderId", this.workSheetFolderId);
        }
        if (StringUtil.isNotNull(this.analyzeDataIds) && analyzeDataIds.size() > 0) {
            condition.append(" and a.id in :analyzeDataIds");
            values.put("analyzeDataIds", this.analyzeDataIds);
        }
        return condition.toString();
    }
}