package com.sinoyd.base.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * LogForLuckySheet实体
 * <AUTHOR>
 * @version V1.0.0 2022/10/24
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="LogForLuckySheet")
@Data
@EntityListeners(AuditingEntityListener.class)
public class LogForLuckySheet implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public LogForLuckySheet() {
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 关联业务对象id
     */
    @Column(length=25,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("关联业务对象id")
    private String objectId;

    /**
     * 操作人id
     */
    @Column(length=25,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("操作人id")
    private String operatorId;

    /**
     * 操作人名称
     */
    @Column(length=25,nullable=false)
    @ApiModelProperty("操作人名称")
    private String operatorName;

    /**
     * 操作时间
     */
    @Column(nullable=false)
    @ApiModelProperty("操作时间")
    private Date operateTime;

    /**
     * 操作类型
     */
    @Column(length = 500)
    @ApiModelProperty("操作类型（新建、保存、修改等）")
    @Length(message = "操作类型{validation.message.length}", max = 500)
    private String operateInfo;

    /**
     * 说明
     */
    @Column(length = 4000)
    @ApiModelProperty("说明")
    @Length(message = "说明{validation.message.length}", max = 1000)
    private String comment;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 所属实验室
     */
    @Column(length=25,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 组织机构id
     */
    @Column(length=25,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

}
