package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoReportConfig2Module;

import java.util.List;


/**
 * 报告模板配置与报告包含的组件的关联关系
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
public interface ReportConfig2ModuleRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportConfig2Module, String> {

    /**
     * 根据报表配置id获取相应的数据
     * @param reportConfigId 报告配置id
     * @return 报告组件配置列表
     */
    List<DtoReportConfig2Module> findByReportConfigId(String reportConfigId);

    /**
     * 根据报表配置id获取相应的数据
     * @param reportConfigIdList 报告配置id列表
     * @return 报告组件配置列表
     */
    List<DtoReportConfig2Module> findByReportConfigIdIn(List<String> reportConfigIdList);

    /**
     * 根据组件id获取相应的数据
     * @param moduleIds 组件id列表
     * @return 报告组件配置列表
     */
    List<DtoReportConfig2Module> findByReportModuleIdIn(List<String> moduleIds);

}