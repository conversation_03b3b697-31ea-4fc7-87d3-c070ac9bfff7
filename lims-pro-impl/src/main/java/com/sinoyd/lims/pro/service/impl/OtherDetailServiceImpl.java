package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoOrderQuotation;
import com.sinoyd.lims.pro.dto.DtoOtherDetail;
import com.sinoyd.lims.pro.repository.OrderQuotationRepository;
import com.sinoyd.lims.pro.repository.OtherDetailRepository;
import com.sinoyd.lims.pro.service.OtherDetailService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;


/**
 * OtherDetail操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @Service
public class OtherDetailServiceImpl extends BaseJpaServiceImpl<DtoOtherDetail,String,OtherDetailRepository> implements OtherDetailService {

     private OrderQuotationRepository orderQuotationRepository;

     public final static String formulaB = "折后检测费*收费标准";
     public final static String formulaA = "收费标准*天数*数量";

    @Override
    public void findByPage(PageBean<DtoOtherDetail> pb, BaseCriteria otherDetailCriteria) {
        pb.setEntityName("DtoOtherDetail a,DtoFeeConfig f");
        pb.setSelect("select a,f.typeName");
        comRepository.findByPage(pb, otherDetailCriteria);
        List<DtoOtherDetail> datas = pb.getData();

        List<DtoOtherDetail> newDatas = new ArrayList<>();
        if (StringUtil.isNotEmpty(datas)) {
            Iterator<DtoOtherDetail> ite = datas.iterator();
            while (ite.hasNext()) {
                Object obj = ite.next();
                Object[] objects = (Object[]) obj;
                DtoOtherDetail otherDetail = (DtoOtherDetail) objects[0];
                otherDetail.setTypeName((String) objects[1]);
                newDatas.add(otherDetail);
            }
        }
        pb.setData(newDatas);
    }

    @Transactional
    @Override
    public DtoOtherDetail save(DtoOtherDetail otherDetail) {
        setPrice(otherDetail);
        otherDetail.setQuotedPrice(otherDetail.getPrice());
        return super.save(otherDetail);
    }

    @Transactional
    @Override
    public DtoOtherDetail update(DtoOtherDetail otherDetail) {
        setPrice(otherDetail);
        //客户人为修改的值
        if (!otherDetail.getIsChange()) {
            otherDetail.setQuotedPrice(otherDetail.getPrice());
        }
        DtoOtherDetail detail = super.update(otherDetail);
        detail.setTypeName(otherDetail.getTypeName());
        detail.setIsChange(false);
        return detail;
    }

    @Override
    @Transactional
    public void batchSave(List<DtoOtherDetail> otherDetails) {
        for (DtoOtherDetail otherDetail : otherDetails) {
            setPrice(otherDetail);
            otherDetail.setQuotedPrice(otherDetail.getPrice());
        }
        super.save(otherDetails);
    }

    /**
     * 设置其他费用小计
     *
     * @param otherDetail 其他费用
     */
    private void setPrice(DtoOtherDetail otherDetail) {
        if (formulaA.equals(otherDetail.getFormula())) {
            otherDetail.setPrice(otherDetail.getStandard().multiply(otherDetail.getDays()).multiply(otherDetail.getCount()));
        } else if (formulaB.equals(otherDetail.getFormula())) {
            DtoOrderQuotation orderQuotation = orderQuotationRepository.findOne(otherDetail.getQuotationId());
            if (StringUtil.isNotNull(orderQuotation)) {
                otherDetail.setPrice(otherDetail.getStandard().multiply(orderQuotation.getDiscountPrice()).divide(new BigDecimal("100")));
            }
        } else {
            otherDetail.setPrice(otherDetail.getStandard().multiply(otherDetail.getDays()).multiply(otherDetail.getCount()));
        }
    }

    @Autowired
    public void setOrderQuotationRepository(OrderQuotationRepository orderQuotationRepository) {
        this.orderQuotationRepository = orderQuotationRepository;
    }
}