package com.sinoyd.lims.api.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.api.dto.DtoHomeNotice;
import com.sinoyd.lims.api.dto.DtoHomePage;
import com.sinoyd.lims.lim.dto.lims.DtoNotice;

import java.util.List;

/**
 * 移动端首页接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
public interface MonitorHomeService {

    /**
     * 首页代办内容
     *
     * @return 首页内容
     */
    List<DtoHomePage> findHomePage();

    /**
     * 首页代办内容
     *
     * @return 首页内容
     */
    DtoHomeNotice findNotice();

    /**
     * 分页查询公告
     *
     * @param pageBean       pb
     * @param noticeCriteria 查询条件
     * @return
     */
    List<DtoNotice> findNoticePage(PageBean<DtoNotice> pageBean, BaseCriteria noticeCriteria);

    /**
     * 根据id查询公告
     *
     * @param id id
     * @return 公告
     */
    DtoNotice findNoticeById(String id);
}
