CREATE TABLE tb_pro_project2inspect
(
    id             varchar(50) NOT NULL COMMENT 'id',
    projectId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '项目id',
    inspectType    int(10) NOT NULL COMMENT '期间核查类型',
    objectId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '核查对象ID',
    objName varchar(100)         DEFAULT NULL COMMENT '核查对象名称',
    code varchar(100)         DEFAULT NULL COMMENT '核查对象编号',
    model varchar(100)         DEFAULT NULL COMMENT '核查对象型号',
    inspectContent varchar(100)         DEFAULT NULL COMMENT '核查内容',
    PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='期间核查关联质控项目表';