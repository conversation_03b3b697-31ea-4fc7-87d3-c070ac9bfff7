package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.criteria.OrderContractAchievement2PersonCriteria;
import com.sinoyd.lims.pro.criteria.OrderContractAchievementDetailsCriteria;
import com.sinoyd.lims.pro.criteria.OrderContractCriteria;
import com.sinoyd.lims.pro.dto.DtoOrderContract;
import com.sinoyd.lims.pro.dto.DtoOrderContractAchievementDetails;
import com.sinoyd.lims.pro.dto.DtoOrderContractAchievement2Person;
import com.sinoyd.lims.pro.repository.OrderContractAchievement2PersonRepository;
import com.sinoyd.lims.pro.repository.OrderContractAchievementDetailsRepository;
import com.sinoyd.lims.pro.service.OrderContractAchievement2PersonService;
import com.sinoyd.lims.pro.service.OrderContractAchievementDetailsService;
import com.sinoyd.lims.pro.service.OrderContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OrderContractAchievement2Person操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Service
public class OrderContractAchievement2PersonServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoOrderContractAchievement2Person, String, OrderContractAchievement2PersonRepository> implements OrderContractAchievement2PersonService {

    private DepartmentService departmentService;

    private PersonService personService;

    private OrderContractAchievementDetailsService orderContractAcihievementDetailsService;

    private OrderContractService orderContractService;

    private OrderContractAchievementDetailsRepository orderContractAchievementDetailsRepository;

    @Override
    public void findByPage(PageBean<DtoOrderContractAchievement2Person> page, BaseCriteria criteria) {
        OrderContractAchievement2PersonCriteria achievement2PersonCriteria = (OrderContractAchievement2PersonCriteria) criteria;
        page.setEntityName("DtoOrderContractAchievement2Person a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData(), achievement2PersonCriteria);
        page.getData().sort(Comparator.comparing(DtoOrderContractAchievement2Person::getPersonName,Comparator.nullsLast(Comparator.naturalOrder())));
    }

    @Override
    @Transactional
    public void selectPerson(List<String> personIds) {
        List<DtoOrderContractAchievement2Person> saveList = new ArrayList<>();
        personIds.forEach(p -> {
            DtoOrderContractAchievement2Person data = new DtoOrderContractAchievement2Person();
            data.setPersonId(p);
            saveList.add(data);
        });
        if (StringUtil.isNotEmpty(saveList)) {
            super.save(saveList);
        }
    }

    @Override
    public void verifySelectPerson(List<String> personIds) {
        List<DtoOrderContractAchievement2Person> achievements = repository.findByPersonIdIn(personIds);
        List<DtoPerson> personList = personService.findAll(personIds);
        List<String> personNames = new ArrayList<>();
        personList.forEach(p -> {
            Optional<DtoOrderContractAchievement2Person> achievement = achievements.stream().filter(a -> a.getPersonId().equals(p.getId())).findFirst();
            achievement.ifPresent(a -> personNames.add(p.getCName()));
        });
        if (StringUtil.isNotEmpty(personNames)) {
            throw new BaseException(String.join(",", personNames) + "的绩效已存在");
        }
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        orderContractAchievementDetailsRepository.deleteByAchievementIdIn((List<String>) ids);
        return super.logicDeleteById(ids);
    }

    @Override
    @Transactional
    public void updateData(Integer year, List<String> ids) {
        PageBean<DtoOrderContract> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        OrderContractCriteria criteria = new OrderContractCriteria();
        criteria.setSignStartTime(CalendarUtil.getCurrentYearBegin(year));
        criteria.setSignEndTime(CalendarUtil.getCurrentYearEnd(year));
        orderContractService.findByPage(pb, criteria);
        List<DtoOrderContract> contracts = pb.getData();
        List<DtoOrderContractAchievement2Person> achievements = repository.findAll(ids);
        orderContractAchievementDetailsRepository.deleteByAchievementIdInAndSignDateBetween(ids, DateUtil.stringToDate(CalendarUtil.getCurrentYearBegin(year), DateUtil.YEAR),
                DateUtil.stringToDate(CalendarUtil.getCurrentYearEnd(year), DateUtil.YEAR));
        List<DtoOrderContractAchievementDetails> saveList = new ArrayList<>();
        achievements.forEach(a -> {
            List<DtoOrderContract> contracts2Person = contracts.stream().filter(c -> c.getSignPersonId().contains(a.getPersonId())).collect(Collectors.toList());
            contracts2Person.forEach(c -> {
                DtoOrderContractAchievementDetails data = new DtoOrderContractAchievementDetails();
                List<String> signPersonIds = Arrays.asList(c.getSignPersonId().split(","));
                data.setAchievementId(a.getId());
                data.setContractName(c.getContractName());
                data.setContractCode(c.getContractCode());
                data.setContractNature(c.getContractNature());
                data.setFirstEntName(c.getFirstEntName());
                data.setSignDate(c.getSignDate());
                data.setTotalAmount(c.getTotalAmount().divide(new BigDecimal(signPersonIds.size()), 0, BigDecimal.ROUND_HALF_UP));
                data.setSignPersonId(c.getSignPersonId());
                saveList.add(data);
            });
        });
        if (StringUtil.isNotEmpty(saveList)) {
            orderContractAcihievementDetailsService.save(saveList);
        }
    }

    @Override
    public Map<String, BigDecimal> chartForPerMonth() {
        List<DtoOrderContractAchievementDetails> details = orderContractAchievementDetailsRepository.findAll();
        Map<String, BigDecimal> map = details.stream().collect(Collectors.groupingBy(d -> DateUtil.dateToString(d.getSignDate(), "yyyy-MM"),
                Collectors.mapping(DtoOrderContractAchievementDetails::getTotalAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        return map;
    }

    private void fillingTransientFields(List<DtoOrderContractAchievement2Person> acihievements, OrderContractAchievement2PersonCriteria criteria) {
        PageBean<DtoOrderContractAchievementDetails> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        OrderContractAchievementDetailsCriteria achievementDetailsCriteria = new OrderContractAchievementDetailsCriteria();
        achievementDetailsCriteria.setSignStartTime(criteria.getSignStartTime());
        achievementDetailsCriteria.setSignEndTime(criteria.getSignEndTime());
        orderContractAcihievementDetailsService.findByPage(pb, achievementDetailsCriteria);
        List<DtoOrderContractAchievementDetails> details = pb.getData();
        List<String> personIds = acihievements.stream().map(DtoOrderContractAchievement2Person::getPersonId).collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
        List<DtoDepartment> departments = departmentService.findAll();
        acihievements.forEach(a -> {
            Optional<DtoPerson> person = personList.stream().filter(p -> p.getId().equals(a.getPersonId())).findFirst();
            person.ifPresent(p -> {
                a.setPersonName(p.getCName());
                Optional<DtoDepartment> dtoDepartment = departments.stream().filter(d -> d.getId().equals(p.getDeptId())).findFirst();
                dtoDepartment.ifPresent(d -> a.setDeptName(d.getDeptName()));
            });
            a.setTotal(details.stream().filter(d -> d.getAchievementId().equals(a.getId()))
                    .collect(Collectors.mapping(DtoOrderContractAchievementDetails::getTotalAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        });
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    @Lazy
    public void setOrderContractAcihievementDetailsService(OrderContractAchievementDetailsService orderContractAcihievementDetailsService) {
        this.orderContractAcihievementDetailsService = orderContractAcihievementDetailsService;
    }

    @Autowired
    @Lazy
    public void setOrderContractService(OrderContractService orderContractService) {
        this.orderContractService = orderContractService;
    }

    @Autowired
    @Lazy
    public void setOrderContractAchievementDetailsRepository(OrderContractAchievementDetailsRepository orderContractAchievementDetailsRepository) {
        this.orderContractAchievementDetailsRepository = orderContractAchievementDetailsRepository;
    }
}
