CREATE TRIGGER "AFTER_INSERT_PERSON2TEST"
    AFTER INSERT
    ON "TB_LIM_PERSON2TEST"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 1, null, null, null,
        new.orgId, '00000000-0000-0000-0000-000000000000');
end;
