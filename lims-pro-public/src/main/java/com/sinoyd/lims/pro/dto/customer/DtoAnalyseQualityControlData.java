package com.sinoyd.lims.pro.dto.customer;


import lombok.Data;

import java.util.Map;

/**
 * 分析数据及质控信息对象
 *
 * <AUTHOR>
 * @version V1.0.0 2022/11/10
 * @since V100R001
 */
@Data
public class DtoAnalyseQualityControlData {

    /**
     * 分析数据id
     */
    private String analyseId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 关联样品id
     */
    private String associateSampleId;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 检测结果修约值
     */
    private String testValueDst;

    /**
     * 检测结果
     */
    private String testOriginValue;

    /**
     * 中间值
     */
    private String seriesValue;

    /**
     * 平行样和原样的均值结果
     */
    private String pxAverageValue;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 标准值
     */
    private String qcValue;

    /**
     * 质控信息
     */
    private String qcInfo;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 检出限
     */
    private String examLimitValue;

    /**
     * 有效位
     */
    private Integer mostSignificance;

    /**
     * 小数位
     */
    private Integer mostDecimal;

    /**
     * 公式参数名称和值的映射
     */
    private Map<String, String> paramMap;

    /**
     * 测定下限
     */
    private String lowerLimit;

    /**
     * 质控id
     */
    private String qcId;

    /**
     * 不确定度
     */
    private Integer uncertainType;


    /**
     * 标样区间范围低点
     */
    private String rangeLow;

    /**
     * 标样区间范围高点
     */
    private String rangeHigh;

    /**
     * 稀释水是否接种
     */
    private Integer isVaccinate;


    public DtoAnalyseQualityControlData(String analyseId, String sampleId, String associateSampleId, String testValue,
                                        String testValueDst, String testOriginValue, Integer qcType, Integer qcGrade,
                                        String qcValue, String qcInfo, String testId, String examLimitValue, String lowerLimit,
                                        Integer mostSignificance, Integer mostDecimal, Map<String, String> paramMap,
                                        String seriesValue, String qcId) {
        this.analyseId = analyseId;
        this.sampleId = sampleId;
        this.associateSampleId = associateSampleId;
        this.testValue = testValue;
        this.testValueDst = testValueDst;
        this.testOriginValue = testOriginValue;
        this.qcType = qcType;
        this.qcGrade = qcGrade;
        this.qcValue = qcValue;
        this.qcInfo = qcInfo;
        this.testId = testId;
        this.examLimitValue = examLimitValue;
        this.mostSignificance = mostSignificance;
        this.mostDecimal = mostDecimal;
        this.paramMap = paramMap;
        this.lowerLimit = lowerLimit;
        this.seriesValue = seriesValue;
        this.qcId = qcId;
    }

    public DtoAnalyseQualityControlData(String analyseId, String sampleId, String associateSampleId, String testValue,
                                        String testValueDst, String testOriginValue, Integer qcType, Integer qcGrade,
                                        String qcValue, String qcInfo, String testId, String examLimitValue, String lowerLimit,
                                        Integer mostSignificance, Integer mostDecimal, Map<String, String> paramMap,
                                        String seriesValue, String qcId, Integer uncertainType) {
        this.analyseId = analyseId;
        this.sampleId = sampleId;
        this.associateSampleId = associateSampleId;
        this.testValue = testValue;
        this.testValueDst = testValueDst;
        this.testOriginValue = testOriginValue;
        this.qcType = qcType;
        this.qcGrade = qcGrade;
        this.qcValue = qcValue;
        this.qcInfo = qcInfo;
        this.testId = testId;
        this.examLimitValue = examLimitValue;
        this.mostSignificance = mostSignificance;
        this.mostDecimal = mostDecimal;
        this.paramMap = paramMap;
        this.lowerLimit = lowerLimit;
        this.seriesValue = seriesValue;
        this.qcId = qcId;
        this.uncertainType = uncertainType;
    }

    public DtoAnalyseQualityControlData(String analyseId, String sampleId, String associateSampleId, String testValue,
                                        String testValueDst, String testOriginValue, Integer qcType, Integer qcGrade,
                                        String qcValue, String qcInfo, String testId, String examLimitValue, String lowerLimit,
                                        Integer mostSignificance, Integer mostDecimal, Map<String, String> paramMap,
                                        String seriesValue, String qcId, Integer uncertainType, String rangeLow, String rangeHigh) {
        this.analyseId = analyseId;
        this.sampleId = sampleId;
        this.associateSampleId = associateSampleId;
        this.testValue = testValue;
        this.testValueDst = testValueDst;
        this.testOriginValue = testOriginValue;
        this.qcType = qcType;
        this.qcGrade = qcGrade;
        this.qcValue = qcValue;
        this.qcInfo = qcInfo;
        this.testId = testId;
        this.examLimitValue = examLimitValue;
        this.mostSignificance = mostSignificance;
        this.mostDecimal = mostDecimal;
        this.paramMap = paramMap;
        this.lowerLimit = lowerLimit;
        this.seriesValue = seriesValue;
        this.qcId = qcId;
        this.uncertainType = uncertainType;
        this.rangeLow = rangeLow;
        this.rangeHigh = rangeHigh;
    }

    public DtoAnalyseQualityControlData(String analyseId, String sampleId, String associateSampleId, String testValue,
                                        String testValueDst, String testOriginValue, String pxAverageValue, Integer qcType,
                                        Integer qcGrade, String qcValue, String qcInfo, String testId, String examLimitValue,
                                        String lowerLimit, Integer mostSignificance, Integer mostDecimal, Map<String, String> paramMap,
                                        String seriesValue, String qcId, Integer uncertainType, String rangeLow, String rangeHigh) {
        this.analyseId = analyseId;
        this.sampleId = sampleId;
        this.associateSampleId = associateSampleId;
        this.testValue = testValue;
        this.testValueDst = testValueDst;
        this.testOriginValue = testOriginValue;
        this.qcType = qcType;
        this.qcGrade = qcGrade;
        this.qcValue = qcValue;
        this.qcInfo = qcInfo;
        this.testId = testId;
        this.examLimitValue = examLimitValue;
        this.mostSignificance = mostSignificance;
        this.mostDecimal = mostDecimal;
        this.paramMap = paramMap;
        this.lowerLimit = lowerLimit;
        this.seriesValue = seriesValue;
        this.qcId = qcId;
        this.uncertainType = uncertainType;
        this.rangeLow = rangeLow;
        this.rangeHigh = rangeHigh;
        this.pxAverageValue = pxAverageValue;
    }

    public DtoAnalyseQualityControlData(String analyseId, String sampleId, String associateSampleId, String testValue,
                                        String testValueDst, String testOriginValue, String pxAverageValue, Integer qcType,
                                        Integer qcGrade, String qcValue, String qcInfo, String testId, String examLimitValue,
                                        String lowerLimit, Integer mostSignificance, Integer mostDecimal, Map<String, String> paramMap,
                                        String seriesValue, String qcId, Integer uncertainType, String rangeLow, String rangeHigh, Integer isVaccinate) {
        this.analyseId = analyseId;
        this.sampleId = sampleId;
        this.associateSampleId = associateSampleId;
        this.testValue = testValue;
        this.testValueDst = testValueDst;
        this.testOriginValue = testOriginValue;
        this.qcType = qcType;
        this.qcGrade = qcGrade;
        this.qcValue = qcValue;
        this.qcInfo = qcInfo;
        this.testId = testId;
        this.examLimitValue = examLimitValue;
        this.mostSignificance = mostSignificance;
        this.mostDecimal = mostDecimal;
        this.paramMap = paramMap;
        this.lowerLimit = lowerLimit;
        this.seriesValue = seriesValue;
        this.qcId = qcId;
        this.uncertainType = uncertainType;
        this.rangeLow = rangeLow;
        this.rangeHigh = rangeHigh;
        this.pxAverageValue = pxAverageValue;
        this.isVaccinate = isVaccinate;
    }
}
