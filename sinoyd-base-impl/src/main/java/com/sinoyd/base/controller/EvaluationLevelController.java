package com.sinoyd.base.controller;

import com.sinoyd.base.dto.customer.TreeNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.base.service.EvaluationLevelService;
import com.sinoyd.base.criteria.EvaluationLevelCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * EvaluationLevel服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/18
 * @since V100R001
 */
 @Api(tags = "示例: EvaluationLevel服务")
 @RestController
 @RequestMapping("api/base/evaluationLevel")
 @Validated
 public class EvaluationLevelController extends BaseJpaController<DtoEvaluationLevel, String,EvaluationLevelService> {


    /**
     * 分页动态条件查询EvaluationLevel
     * @param evaluationLevelCriteria 条件参数
     * @return RestResponse<List<EvaluationLevel>>
     */
     @ApiOperation(value = "分页动态条件查询EvaluationLevel", notes = "分页动态条件查询EvaluationLevel")
     @GetMapping
     public RestResponse<List<DtoEvaluationLevel>> findByPage(EvaluationLevelCriteria evaluationLevelCriteria) {
         PageBean<DtoEvaluationLevel> pageBean = super.getPageBean();
         RestResponse<List<DtoEvaluationLevel>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, evaluationLevelCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

    @ApiOperation(value = "查询评价条件的树结构", notes = "查询评价条件的树结构")
    @GetMapping("/tree")
    public RestResponse<List<TreeNode>> findTreeNode(@RequestParam("evaluationId") String evaluationId) {
        RestResponse<List<TreeNode>> restResponse = new RestResponse<>();
        List<TreeNode> treeNodeList = service.findTreeNodeByEvaluationId(evaluationId);
        restResponse.setRestStatus(StringUtil.isEmpty(treeNodeList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(treeNodeList);
        restResponse.setCount(treeNodeList.size());
        return restResponse;
    }

     /**
     * 按主键查询EvaluationLevel
     * @param id 主键id
     * @return RestResponse<DtoEvaluationLevel>
     */
     @ApiOperation(value = "按主键查询EvaluationLevel", notes = "按主键查询EvaluationLevel")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoEvaluationLevel> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoEvaluationLevel> restResponse = new RestResponse<>();
         DtoEvaluationLevel evaluationLevel = service.findOne(id);
         restResponse.setData(evaluationLevel);
         restResponse.setRestStatus(StringUtil.isNull(evaluationLevel) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增EvaluationLevel
     * @param evaluationLevel 实体列表
     * @return RestResponse<DtoEvaluationLevel>
     */
     @ApiOperation(value = "新增EvaluationLevel", notes = "新增EvaluationLevel")
     @PostMapping
     public RestResponse<DtoEvaluationLevel> create(@Validated @RequestBody DtoEvaluationLevel evaluationLevel) {
         RestResponse<DtoEvaluationLevel> restResponse = new RestResponse<>();
         restResponse.setData(service.save(evaluationLevel));
         return restResponse;
      }

     /**
     * 新增EvaluationLevel
     * @param evaluationLevel 实体列表
     * @return RestResponse<DtoEvaluationLevel>
     */
     @ApiOperation(value = "修改EvaluationLevel", notes = "修改EvaluationLevel")
     @PutMapping
     public RestResponse<DtoEvaluationLevel> update(@Validated @RequestBody DtoEvaluationLevel evaluationLevel) {
         RestResponse<DtoEvaluationLevel> restResponse = new RestResponse<>();
         restResponse.setData(service.update(evaluationLevel));
         return restResponse;
      }

    /**
     * "根据id批量删除EvaluationLevel
     * @param id id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除EvaluationLevel", notes = "根据id批量删除EvaluationLevel")
    @DeleteMapping(path = "/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);
        return restResp;
    }
 }