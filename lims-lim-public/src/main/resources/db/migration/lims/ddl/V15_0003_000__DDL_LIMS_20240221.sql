-- tb_lim_test
drop trigger if exists after_insert_test;
DELIMITER $$
CREATE TRIGGER after_insert_test
    AFTER INSERT
    ON tb_lim_test
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 1, null, null, null, new.orgId, new.domainId);
end $$
DELIMITER ;

drop trigger if exists after_update_test;
DELIMITER $$
CREATE TRIGGER after_update_test
    AFTER UPDATE
    ON tb_lim_test
    FOR EACH ROW
BEGIN
    IF new.isDeleted = 1 THEN
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
                new.domainId);
    else
        if new.parentId != old.parentId or (new.parentId is null and old.parentId is not null) or
           (new.parentId is not null and old.parentId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'parentId', old.parentId,
                    new.parentId, new.orgId, new.domainId);
        END IF;
        if new.analyzeMethodId != old.analyzeMethodId or
           (new.analyzeMethodId is null and old.analyzeMethodId is not null) or
           (new.analyzeMethodId is not null and old.analyzeMethodId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'analyzeMethodId',
                    old.analyzeMethodId, new.analyzeMethodId, new.orgId, new.domainId);
        END IF;
        if new.redAnalyzeMethodName != old.redAnalyzeMethodName or
           (new.redAnalyzeMethodName is null and old.redAnalyzeMethodName is not null) or
           (new.redAnalyzeMethodName is not null and old.redAnalyzeMethodName is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'redAnalyzeMethodName',
                    old.redAnalyzeMethodName, new.redAnalyzeMethodName, new.orgId, new.domainId);
        END IF;
        if new.redCountryStandard != old.redCountryStandard or
           (new.redCountryStandard is null and old.redCountryStandard is not null) or
           (new.redCountryStandard is not null and old.redCountryStandard is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'redCountryStandard',
                    old.redCountryStandard, new.redCountryStandard, new.orgId, new.domainId);
        END IF;
        if new.analyzeItemId != old.analyzeItemId or (new.analyzeItemId is null and old.analyzeItemId is not null) or
           (new.analyzeItemId is not null and old.analyzeItemId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'analyzeItemId', old.analyzeItemId,
                    new.analyzeItemId, new.orgId, new.domainId);
        END IF;
        if new.redAnalyzeItemName != old.redAnalyzeItemName or
           (new.redAnalyzeItemName is null and old.redAnalyzeItemName is not null) or
           (new.redAnalyzeItemName is not null and old.redAnalyzeItemName is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'redAnalyzeItemName',
                    old.redAnalyzeItemName, new.redAnalyzeItemName, new.orgId, new.domainId);
        END IF;
        if new.fullPinYin != old.fullPinYin or (new.fullPinYin is null and old.fullPinYin is not null) or
           (new.fullPinYin is not null and old.fullPinYin is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'fullPinYin', old.fullPinYin,
                    new.fullPinYin, new.orgId, new.domainId);
        END IF;
        if new.pinYin != old.pinYin or (new.pinYin is null and old.pinYin is not null) or
           (new.pinYin is not null and old.pinYin is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'pinYin', old.pinYin, new.pinYin,
                    new.orgId, new.domainId);
        END IF;
        if new.sampleTypeId != old.sampleTypeId or (new.sampleTypeId is null and old.sampleTypeId is not null) or
           (new.sampleTypeId is not null and old.sampleTypeId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'sampleTypeId', old.sampleTypeId,
                    new.sampleTypeId, new.orgId, new.domainId);
        END IF;
        if new.testCode != old.testCode or (new.testCode is null and old.testCode is not null) or
           (new.testCode is not null and old.testCode is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'testCode', old.testCode,
                    new.testCode, new.orgId, new.domainId);
        END IF;
        if new.dimensionId != old.dimensionId or (new.dimensionId is null and old.dimensionId is not null) or
           (new.dimensionId is not null and old.dimensionId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'dimensionId', old.dimensionId,
                    new.dimensionId, new.orgId, new.domainId);
        END IF;
        if new.examLimitValue != old.examLimitValue or
           (new.examLimitValue is null and old.examLimitValue is not null) or
           (new.examLimitValue is not null and old.examLimitValue is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'examLimitValue', old.examLimitValue,
                    new.examLimitValue, new.orgId, new.domainId);
        END IF;
        if new.validTime != old.validTime or (new.validTime is null and old.validTime is not null) or
           (new.validTime is not null and old.validTime is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'validTime', old.validTime,
                    new.validTime, new.orgId, new.domainId);
        END IF;
        if new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
           (new.orderNum is not null and old.orderNum is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'orderNum', old.orderNum,
                    new.orderNum, new.orgId, new.domainId);
        END IF;
        if new.mostSignificance != old.mostSignificance or
           (new.mostSignificance is null and old.mostSignificance is not null) or
           (new.mostSignificance is not null and old.mostSignificance is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'mostSignificance',
                    old.mostSignificance, new.mostSignificance, new.orgId, new.domainId);
        END IF;
        if new.mostDecimal != old.mostDecimal or (new.mostDecimal is null and old.mostDecimal is not null) or
           (new.mostDecimal is not null and old.mostDecimal is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'mostDecimal', old.mostDecimal,
                    new.mostDecimal, new.orgId, new.domainId);
        END IF;
        if new.cert != old.cert or (new.cert is null and old.cert is not null) or
           (new.cert is not null and old.cert is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'cert', old.cert, new.cert,
                    new.orgId, new.domainId);
        END IF;
        if new.testName != old.testName or (new.testName is null and old.testName is not null) or
           (new.testName is not null and old.testName is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'testName', old.testName,
                    new.testName, new.orgId, new.domainId);
        END IF;
        if new.isOutsourcing != old.isOutsourcing or (new.isOutsourcing is null and old.isOutsourcing is not null) or
           (new.isOutsourcing is not null and old.isOutsourcing is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isOutsourcing', old.isOutsourcing,
                    new.isOutsourcing, new.orgId, new.domainId);
        END IF;
        if new.isCompleteField != old.isCompleteField or
           (new.isCompleteField is null and old.isCompleteField is not null) or
           (new.isCompleteField is not null and old.isCompleteField is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isCompleteField',
                    old.isCompleteField, new.isCompleteField, new.orgId, new.domainId);
        END IF;
        if new.isQCP != old.isQCP or (new.isQCP is null and old.isQCP is not null) or
           (new.isQCP is not null and old.isQCP is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isQCP', old.isQCP, new.isQCP,
                    new.orgId, new.domainId);
        END IF;
        if new.isQCB != old.isQCB or (new.isQCB is null and old.isQCB is not null) or
           (new.isQCB is not null and old.isQCB is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isQCB', old.isQCB, new.isQCB,
                    new.orgId, new.domainId);
        END IF;
        if new.isSeries != old.isSeries or (new.isSeries is null and old.isSeries is not null) or
           (new.isSeries is not null and old.isSeries is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isSeries', old.isSeries,
                    new.isSeries, new.orgId, new.domainId);
        END IF;
        if new.isUseFormula != old.isUseFormula or (new.isUseFormula is null and old.isUseFormula is not null) or
           (new.isUseFormula is not null and old.isUseFormula is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isUseFormula', old.isUseFormula,
                    new.isUseFormula, new.orgId, new.domainId);
        END IF;
        if new.examLimitValueLess != old.examLimitValueLess or
           (new.examLimitValueLess is null and old.examLimitValueLess is not null) or
           (new.examLimitValueLess is not null and old.examLimitValueLess is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'examLimitValueLess',
                    old.examLimitValueLess, new.examLimitValueLess, new.orgId, new.domainId);
        END IF;
        if new.samplePeriod != old.samplePeriod or (new.samplePeriod is null and old.samplePeriod is not null) or
           (new.samplePeriod is not null and old.samplePeriod is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'samplePeriod', old.samplePeriod,
                    new.samplePeriod, new.orgId, new.domainId);
        END IF;
        if new.lowerLimit != old.lowerLimit or (new.lowerLimit is null and old.lowerLimit is not null) or
           (new.lowerLimit is not null and old.lowerLimit is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'lowerLimit', old.lowerLimit,
                    new.lowerLimit, new.orgId, new.domainId);
        END IF;
        if new.totalTestName != old.totalTestName or (new.totalTestName is null and old.totalTestName is not null) or
           (new.totalTestName is not null and old.totalTestName is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'totalTestName', old.totalTestName,
                    new.totalTestName, new.orgId, new.domainId);
        END IF;
        if new.isShowTotalTest != old.isShowTotalTest or
           (new.isShowTotalTest is null and old.isShowTotalTest is not null) or
           (new.isShowTotalTest is not null and old.isShowTotalTest is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isShowTotalTest',
                    old.isShowTotalTest, new.isShowTotalTest, new.orgId, new.domainId);
        END IF;
        if new.isTotalTest != old.isTotalTest or (new.isTotalTest is null and old.isTotalTest is not null) or
           (new.isTotalTest is not null and old.isTotalTest is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isTotalTest', old.isTotalTest,
                    new.isTotalTest, new.orgId, new.domainId);
        END IF;
        if new.isUseQTFormula != old.isUseQTFormula or
           (new.isUseQTFormula is null and old.isUseQTFormula is not null) or
           (new.isUseQTFormula is not null and old.isUseQTFormula is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isUseQTFormula', old.isUseQTFormula,
                    new.isUseQTFormula, new.orgId, new.domainId);
        END IF;
        if new.kValueFormat != old.kValueFormat or (new.kValueFormat is null and old.kValueFormat is not null) or
           (new.kValueFormat is not null and old.kValueFormat is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'kValueFormat', old.kValueFormat,
                    new.kValueFormat, new.orgId, new.domainId);
        END IF;
        if new.bValueFormat != old.bValueFormat or (new.bValueFormat is null and old.bValueFormat is not null) or
           (new.bValueFormat is not null and old.bValueFormat is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'bValueFormat', old.bValueFormat,
                    new.bValueFormat, new.orgId, new.domainId);
        END IF;
        if new.cValueFormat != old.cValueFormat or (new.cValueFormat is null and old.cValueFormat is not null) or
           (new.cValueFormat is not null and old.cValueFormat is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'cValueFormat', old.cValueFormat,
                    new.cValueFormat, new.orgId, new.domainId);
        END IF;
        if new.samplingCharge != old.samplingCharge or
           (new.samplingCharge is null and old.samplingCharge is not null) or
           (new.samplingCharge is not null and old.samplingCharge is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'samplingCharge', old.samplingCharge,
                    new.samplingCharge, new.orgId, new.domainId);
        END IF;
        if new.testingCharge != old.testingCharge or (new.testingCharge is null and old.testingCharge is not null) or
           (new.testingCharge is not null and old.testingCharge is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'testingCharge', old.testingCharge,
                    new.testingCharge, new.orgId, new.domainId);
        END IF;
        if new.reportDimensionId != old.reportDimensionId or
           (new.reportDimensionId is null and old.reportDimensionId is not null) or
           (new.reportDimensionId is not null and old.reportDimensionId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'reportDimensionId',
                    old.reportDimensionId, new.reportDimensionId, new.orgId, new.domainId);
        END IF;
        if new.reportMostSignificance != old.reportMostSignificance or
           (new.reportMostSignificance is null and old.reportMostSignificance is not null) or
           (new.reportMostSignificance is not null and old.reportMostSignificance is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'reportMostSignificance',
                    old.reportMostSignificance, new.reportMostSignificance, new.orgId, new.domainId);
        END IF;
        if new.reportMostDecimal != old.reportMostDecimal or
           (new.reportMostDecimal is null and old.reportMostDecimal is not null) or
           (new.reportMostDecimal is not null and old.reportMostDecimal is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'reportMostDecimal',
                    old.reportMostDecimal, new.reportMostDecimal, new.orgId, new.domainId);
        END IF;
        if new.remark != old.remark or (new.remark is null and old.remark is not null) or
           (new.remark is not null and old.remark is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'remark', old.remark, new.remark,
                    new.orgId, new.domainId);
        END IF;
        if new.isInsUseRecord != old.isInsUseRecord or
           (new.isInsUseRecord is null and old.isInsUseRecord is not null) or
           (new.isInsUseRecord is not null and old.isInsUseRecord is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isInsUseRecord', old.isInsUseRecord,
                    new.isInsUseRecord, new.orgId, new.domainId);
        END IF;
        if new.isSubSync != old.isSubSync or (new.isSubSync is null and old.isSubSync is not null) or
           (new.isSubSync is not null and old.isSubSync is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isSubSync', old.isSubSync,
                    new.isSubSync, new.orgId, new.domainId);
        END IF;
        if new.inputMode != old.inputMode or (new.inputMode is null and old.inputMode is not null) or
           (new.inputMode is not null and old.inputMode is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'inputMode', old.inputMode,
                    new.inputMode, new.orgId, new.domainId);
        END IF;
        if new.domainCode != old.domainCode or (new.domainCode is null and old.domainCode is not null) or
           (new.domainCode is not null and old.domainCode is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'domainCode', old.domainCode,
                    new.domainCode, new.orgId, new.domainId);
        END IF;
        if new.redYearSn != old.redYearSn or (new.redYearSn is null and old.redYearSn is not null) or
           (new.redYearSn is not null and old.redYearSn is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'redYearSn', old.redYearSn,
                    new.redYearSn, new.orgId, new.domainId);
        END IF;
        if new.testTimelen != old.testTimelen or (new.testTimelen is null and old.testTimelen is not null) or
           (new.testTimelen is not null and old.testTimelen is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'testTimelen', old.testTimelen,
                    new.testTimelen, new.orgId, new.domainId);
        END IF;
        if new.basicWorkload != old.basicWorkload or (new.basicWorkload is null and old.basicWorkload is not null) or
           (new.basicWorkload is not null and old.basicWorkload is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'basicWorkload', old.basicWorkload,
                    new.basicWorkload, new.orgId, new.domainId);
        END IF;
        if new.unitWorkload != old.unitWorkload or (new.unitWorkload is null and old.unitWorkload is not null) or
           (new.unitWorkload is not null and old.unitWorkload is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'unitWorkload', old.unitWorkload,
                    new.unitWorkload, new.orgId, new.domainId);
        END IF;
        if new.externalId != old.externalId or (new.externalId is null and old.externalId is not null) or
           (new.externalId is not null and old.externalId is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'externalId', old.externalId,
                    new.externalId, new.orgId, new.domainId);
        END IF;
        if new.reviseType != old.reviseType or (new.reviseType is null and old.reviseType is not null) or
           (new.reviseType is not null and old.reviseType is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'reviseType', old.reviseType,
                    new.reviseType, new.orgId, new.domainId);
        END IF;
        if new.kDecimalFormat != old.kDecimalFormat or
           (new.kDecimalFormat is null and old.kDecimalFormat is not null) or
           (new.kDecimalFormat is not null and old.kDecimalFormat is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'kDecimalFormat', old.kDecimalFormat,
                    new.kDecimalFormat, new.orgId, new.domainId);
        END IF;
        if new.bDecimalFormat != old.bDecimalFormat or
           (new.bDecimalFormat is null and old.bDecimalFormat is not null) or
           (new.bDecimalFormat is not null and old.bDecimalFormat is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'bDecimalFormat', old.bDecimalFormat,
                    new.bDecimalFormat, new.orgId, new.domainId);
        END IF;
        if new.cDecimalFormat != old.cDecimalFormat or
           (new.cDecimalFormat is null and old.cDecimalFormat is not null) or
           (new.cDecimalFormat is not null and old.cDecimalFormat is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'cDecimalFormat', old.cDecimalFormat,
                    new.cDecimalFormat, new.orgId, new.domainId);
        END IF;
        if new.calculateWay != old.calculateWay or (new.calculateWay is null and old.calculateWay is not null) or
           (new.calculateWay is not null and old.calculateWay is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'calculateWay', old.calculateWay,
                    new.calculateWay, new.orgId, new.domainId);
        END IF;
        if new.mergeBase != old.mergeBase or (new.mergeBase is null and old.mergeBase is not null) or
           (new.mergeBase is not null and old.mergeBase is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'mergeBase', old.mergeBase,
                    new.mergeBase, new.orgId, new.domainId);
        END IF;
        if new.analyseDayLen != old.analyseDayLen or (new.analyseDayLen is null and old.analyseDayLen is not null) or
           (new.analyseDayLen is not null and old.analyseDayLen is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'analyseDayLen', old.analyseDayLen,
                    new.analyseDayLen, new.orgId, new.domainId);
        END IF;
        if new.averageCompute != old.averageCompute or
           (new.averageCompute is null and old.averageCompute is not null) or
           (new.averageCompute is not null and old.averageCompute is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'averageCompute', old.averageCompute,
                    new.averageCompute, new.orgId, new.domainId);
        END IF;
        if new.validate != old.validate or (new.validate is null and old.validate is not null) or
           (new.validate is not null and old.validate is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'validate', old.validate,
                    new.validate, new.orgId, new.domainId);
        END IF;
        if new.usageNum != old.usageNum or (new.usageNum is null and old.usageNum is not null) or
           (new.usageNum is not null and old.usageNum is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'usageNum', old.usageNum,
                    new.usageNum, new.orgId, new.domainId);
        END IF;
        if new.isQCTransport != old.isQCTransport or (new.isQCTransport is null and old.isQCTransport is not null) or
           (new.isQCTransport is not null and old.isQCTransport is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isQCTransport', old.isQCTransport,
                    new.isQCTransport, new.orgId, new.domainId);
        END IF;
        if new.isQCInstrument != old.isQCInstrument or
           (new.isQCInstrument is null and old.isQCInstrument is not null) or
           (new.isQCInstrument is not null and old.isQCInstrument is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isQCInstrument', old.isQCInstrument,
                    new.isQCInstrument, new.orgId, new.domainId);
        END IF;
        if new.airPollution != old.airPollution or (new.airPollution is null and old.airPollution is not null) or
           (new.airPollution is not null and old.airPollution is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'airPollution', old.airPollution,
                    new.airPollution, new.orgId, new.domainId);
        END IF;
        if new.isAbolish != old.isAbolish or (new.isAbolish is null and old.isAbolish is not null) or
           (new.isAbolish is not null and old.isAbolish is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isAbolish', old.isAbolish,
                    new.isAbolish, new.orgId, new.domainId);
        END IF;
        if new.isQCLocal != old.isQCLocal or (new.isQCLocal is null and old.isQCLocal is not null) or
           (new.isQCLocal is not null and old.isQCLocal is null) then
            INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                               operateField, oldValue, newValue, orgId, domainId)
            VALUES (UUID(), 'tb_lim_test', new.id, new.modifier, new.modifyDate, 3, 'isQCLocal', old.isQCLocal,
                    new.isQCLocal, new.orgId, new.domainId);
        END IF;
    END IF;
end $$
DELIMITER ;

-- tb_lim_person2test
drop trigger if exists after_insert_person2test;
DELIMITER $$
CREATE TRIGGER after_insert_person2test
    AFTER INSERT
    ON tb_lim_person2test
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 1, null, null, null,
            new.orgId, '00000000-0000-0000-0000-000000000000');
end $$
DELIMITER ;

drop trigger if exists after_update_person2test;
DELIMITER $$
CREATE TRIGGER after_update_person2test
    AFTER UPDATE
    ON tb_lim_person2test
    FOR EACH ROW
BEGIN
    if new.personId != old.personId or (new.personId is null and old.personId is not null) or
       (new.personId is not null and old.personId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'personId',
                old.personId, new.personId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.testId != old.testId or (new.testId is null and old.testId is not null) or
       (new.testId is not null and old.testId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'testId',
                old.testId,
                new.testId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
       (new.orderNum is not null and old.orderNum is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'orderNum',
                old.orderNum, new.orderNum, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.sampleTypeId != old.sampleTypeId or (new.sampleTypeId is null and old.sampleTypeId is not null) or
       (new.sampleTypeId is not null and old.sampleTypeId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'sampleTypeId',
                old.sampleTypeId, new.sampleTypeId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.isDefaultPerson != old.isDefaultPerson or
       (new.isDefaultPerson is null and old.isDefaultPerson is not null) or
       (new.isDefaultPerson is not null and old.isDefaultPerson is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'isDefaultPerson',
                old.isDefaultPerson, new.isDefaultPerson, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.isDefaultAuditPerson != old.isDefaultAuditPerson or
       (new.isDefaultAuditPerson is null and old.isDefaultAuditPerson is not null) or
       (new.isDefaultAuditPerson is not null and old.isDefaultAuditPerson is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_person2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'isDefaultAuditPerson',
                old.isDefaultAuditPerson, new.isDefaultAuditPerson, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
end $$
DELIMITER ;


-- tb_lim_testexpand
drop trigger if exists after_insert_testexpand;
DELIMITER $$
CREATE TRIGGER after_insert_testexpand
    AFTER INSERT
    ON tb_lim_testexpand
    FOR EACH ROW
BEGIN
    INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                       oldValue, newValue, orgId, domainId)
    VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 1, null, null, null,
            new.orgId, '00000000-0000-0000-0000-000000000000');
end $$
DELIMITER ;

drop trigger if exists after_update_testexpand;
DELIMITER $$
CREATE TRIGGER after_update_testexpand
    AFTER UPDATE
    ON tb_lim_testexpand
    FOR EACH ROW
BEGIN
    if new.testId != old.testId or (new.testId is null and old.testId is not null) or
       (new.testId is not null and old.testId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'testId',
                old.testId, new.testId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.sampleTypeId != old.sampleTypeId or (new.sampleTypeId is null and old.sampleTypeId is not null) or
       (new.sampleTypeId is not null and old.sampleTypeId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'sampleTypeId',
                old.sampleTypeId, new.sampleTypeId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.mostSignificance != old.mostSignificance or
       (new.mostSignificance is null and old.mostSignificance is not null) or
       (new.mostSignificance is not null and old.mostSignificance is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'mostSignificance', old.mostSignificance, new.mostSignificance, new.orgId,
                '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.mostDecimal != old.mostDecimal or (new.mostDecimal is null and old.mostDecimal is not null) or
       (new.mostDecimal is not null and old.mostDecimal is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'mostDecimal',
                old.mostDecimal, new.mostDecimal, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.examLimitValue != old.examLimitValue or (new.examLimitValue is null and old.examLimitValue is not null) or
       (new.examLimitValue is not null and old.examLimitValue is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'examLimitValue',
                old.examLimitValue, new.examLimitValue, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.dimensionId != old.dimensionId or (new.dimensionId is null and old.dimensionId is not null) or
       (new.dimensionId is not null and old.dimensionId is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'dimensionId',
                old.dimensionId, new.dimensionId, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.examLimitValueLess != old.examLimitValueLess or
       (new.examLimitValueLess is null and old.examLimitValueLess is not null) or
       (new.examLimitValueLess is not null and old.examLimitValueLess is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
                'examLimitValueLess', old.examLimitValueLess, new.examLimitValueLess, new.orgId,
                '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.samplePeriod != old.samplePeriod or (new.samplePeriod is null and old.samplePeriod is not null) or
       (new.samplePeriod is not null and old.samplePeriod is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'samplePeriod',
                old.samplePeriod, new.samplePeriod, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
    if new.lowerLimit != old.lowerLimit or (new.lowerLimit is null and old.lowerLimit is not null) or
       (new.lowerLimit is not null and old.lowerLimit is null) then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'lowerLimit',
                old.lowerLimit, new.lowerLimit, new.orgId, '00000000-0000-0000-0000-000000000000');
    END IF;
end $$
DELIMITER ;