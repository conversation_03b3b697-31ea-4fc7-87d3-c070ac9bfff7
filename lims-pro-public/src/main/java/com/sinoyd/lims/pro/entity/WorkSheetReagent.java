package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;



/**
 * WorkSheetReagent实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="WorkSheetReagent")
 @Data
 public  class WorkSheetReagent extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

   public WorkSheetReagent() {
      this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
      this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

   /**
    * 主键id
    */
   @Id
   @Column(length = 50)
   private String id = UUIDHelper.NewID();

   /**
    * 检测单id
    */
   @Column(length = 50, nullable = false)
   @ApiModelProperty("检测单id")
   private String worksheetFolderId;

   /**
    * 试剂配置记录id
    */
   @Column(length = 50, nullable = false)
   @ApiModelProperty("试剂配置记录id")
   private String reagentConfigId;

   /**
    * 配置记录
    */
   @Column(length = 1000)
   @ApiModelProperty("配置记录")
   @Length(message = "配置记录{validation.message.length}", max = 1000)
   private String reagent;

   /**
    * 需求的配置过程
    */
   @Column(length = 1000, name ="[context]")
   @ApiModelProperty("需求的配置过程 ")
   @Length(message = "需求的配置过程 {validation.message.length}", max = 1000)
   private String context;

   /**
    * 试剂名称
    */
   @ApiModelProperty("试剂名称")
   @Length(message = "试剂名称{validation.message.length}", max = 255)
   private String reagentName;

   /**
    * 试剂规格
    */
   @ApiModelProperty("试剂规格")
   @Length(message = "试剂规格{validation.message.length}", max = 255)
   private String reagentSpecification;

   /**
    * 配置溶液
    */
   @ApiModelProperty("配置溶液")
   @Length(message = "配置溶液{validation.message.length}", max = 255)
   private String configurationSolution;

   /**
    * 配置日期
    */
   @Column(nullable = false)
   @ColumnDefault("getdate")
   @ApiModelProperty("配置日期")
   private Date configDate;

   /**
    * 有效期
    */
   @Column(nullable = false)
   @ColumnDefault("'1753-1-1'")
   @ApiModelProperty("有效期")
   private Date expiryDate;

   /**
    * 稀释过程记录
    */
   @Column(length = 1000)
   @ApiModelProperty("稀释过程记录")
   @Length(message = "稀释过程记录{validation.message.length}", max = 1000)
   private String course;

   /**
    * 其他情况
    */
   @Column(length = 1000)
   @ApiModelProperty("其他情况")
   @Length(message = "其他情况{validation.message.length}", max = 1000)
   private String opinion;

   /**
    * 稀释液
    */
   @Column
   @ApiModelProperty("稀释液")
   @Length(message = "稀释液{validation.message.length}", max = 200)
   private String diluent;

   /**
    * 试剂类型（1：一般试剂 2：标准溶液）
    */
   @Column(nullable = false)
   @ApiModelProperty("试剂类型（1：一般试剂 2：标准溶液）")
   private Integer reagentType;

   /**
    * 浓度
    */
   @Column(length = 200)
   @ApiModelProperty("浓度")
   @Length(message = "浓度{validation.message.length}", max = 200)
   private String concentration;

   /**
    * 定容体积
    */
   @Column(length = 200)
   @ApiModelProperty("定容体积")
   @Length(message = "定容体积{validation.message.length}", max = 200)
   private String constantVolume;

   /**
    * 试剂体积或质量
    */
   @Column(length = 200)
   @ApiModelProperty("试剂体积或质量")
   @Length(message = "试剂体积或质量{validation.message.length}", max = 200)
   private String reagentVolumeQuality;

   /**
    * 适用项目
    */
   @Column
   @ApiModelProperty("适用项目")
   @Length(message = "适用项目{validation.message.length}", max = 200)
   private String suitItem;

   /**
    * 是否已有试剂选择
    */
   @ApiModelProperty("是否已有试剂选择")
   private Boolean relateFlag;

   /**
    * 配置人员id
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("配置人员id")
   private String creator;

   /**
    * 组织机构id
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("组织机构id")
   private String orgId;

   /**
    * 创建时间
    */
   @Column(nullable = false)
   @ColumnDefault("getdate")
   @CreatedDate
   @ApiModelProperty("创建时间")
   private Date createDate;

   /**
    * 所属实验室
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @ApiModelProperty("所属实验室")
   private String domainId;

   /**
    * 修改人
    */
   @Column(length = 50, nullable = false)
   @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
   @LastModifiedBy
   @ApiModelProperty("修改人")
   private String modifier;

   /**
    * 修改时间
    */
   @Column(nullable = false)
   @ColumnDefault("getdate")
   @LastModifiedDate
   @ApiModelProperty("修改时间")
   private Date modifyDate;

   /**
    * 假删
    */
   @Column(nullable = false)
   @ColumnDefault("0")
   @ApiModelProperty("假删")
   private Boolean isDeleted = false;
}