package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2Test;

import java.util.List;
import java.util.Map;


/**
 * 原始记录单配置相关的测试项目
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
public interface RecordConfig2TestService extends IBaseJpaService<DtoRecordConfig2Test, String> {

    /**
     * 批量保存数据
     *
     * @param testIds        测试项目ids
     * @param recordConfigId 记录配置id
     * @return 返回数据
     */
    List<DtoRecordConfig2Test> save(List<String> testIds, String recordConfigId);


    /**
     * 查询相应的公式
     *
     * @param recordConfigId 记录单id
     * @param paramsConfigId 参数id
     * @param isAll          是否配置
     * @param analyzeItem    分析项目
     * @param analyzeMethod  分析方法
     * @param formula        公式
     * @return 返回数据
     */
    List<Map<String, Object>> findTestFormula(String recordConfigId,
                                              String paramsConfigId,
                                              Integer isAll, String analyzeItem,String analyzeMethod,String formula);



    /**
     * 查询相应的测试项目
     *
     * @param recordConfigId 记录单id
     * @param paramsConfigId 参数id
     * @param isAll          是否配置
     * @return 返回数据
     */
    List<Map<String, Object>> findTests(String recordConfigId,
                                        String paramsConfigId,
                                        Integer isAll,String key);

    /**
     * 批量新增公式参数
     * @param paramsIdList            参数列表
     * @param testFormulaIdList     测试项目公式标识列表
     */
    void batchAddParams(List<String> paramsIdList, List<String> testFormulaIdList);
}