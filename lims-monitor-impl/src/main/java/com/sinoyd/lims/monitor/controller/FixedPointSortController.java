package com.sinoyd.lims.monitor.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.FixedPointSortService;
import com.sinoyd.lims.monitor.criteria.FixedPointSortCriteria;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSort;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * FixedPointSort服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: FixedPointSort服务")
 @RestController
 @RequestMapping("api/monitor/fixedPointSort")
 public class FixedPointSortController extends BaseJpaController<DtoFixedPointSort, String,FixedPointSortService> {


    /**
     * 分页动态条件查询FixedPointSort
     * @param fixedPointSortCriteria 条件参数
     * @return RestResponse<List<FixedPointSort>>
     */
     @ApiOperation(value = "分页动态条件查询FixedPointSort", notes = "分页动态条件查询FixedPointSort")
     @GetMapping
     public RestResponse<List<DtoFixedPointSort>> findByPage(FixedPointSortCriteria fixedPointSortCriteria) {
         PageBean<DtoFixedPointSort> pageBean = super.getPageBean();
         RestResponse<List<DtoFixedPointSort>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fixedPointSortCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FixedPointSort
     * @param id 主键id
     * @return RestResponse<DtoFixedPointSort>
     */
     @ApiOperation(value = "按主键查询FixedPointSort", notes = "按主键查询FixedPointSort")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFixedPointSort> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFixedPointSort> restResponse = new RestResponse<>();
         DtoFixedPointSort fixedPointSort = service.findOne(id);
         restResponse.setData(fixedPointSort);
         restResponse.setRestStatus(StringUtil.isNull(fixedPointSort) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FixedPointSort
     * @param fixedPointSort 实体列表
     * @return RestResponse<DtoFixedPointSort>
     */
     @ApiOperation(value = "新增FixedPointSort", notes = "新增FixedPointSort")
     @PostMapping
     public RestResponse<DtoFixedPointSort> create(@RequestBody @Validated DtoFixedPointSort fixedPointSort) {
         RestResponse<DtoFixedPointSort> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fixedPointSort));
         return restResponse;
      }

     /**
     * 新增FixedPointSort
     * @param fixedPointSort 实体列表
     * @return RestResponse<DtoFixedPointSort>
     */
     @ApiOperation(value = "修改FixedPointSort", notes = "修改FixedPointSort")
     @PutMapping
     public RestResponse<DtoFixedPointSort> update(@RequestBody @Validated DtoFixedPointSort fixedPointSort) {
         RestResponse<DtoFixedPointSort> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fixedPointSort));
         return restResponse;
      }

    /**
     * "根据id批量删除FixedPointSort
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除FixedPointSort", notes = "根据id批量删除FixedPointSort")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }