package com.sinoyd.lims.pro.repository;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoBusinessSerialNumber;

import java.util.Collection;
import java.util.List;

/**
 * 业务流水号数据访问接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/2/29
 */
public interface BusinessSerialNumberRepository extends IBaseJpaRepository<DtoBusinessSerialNumber, String>,
        LimsRepository<DtoBusinessSerialNumber, String> {

    /**
     * 根据业务类型和业务id查询
     *
     * @param businessType 业务类型枚举值
     * @param businessIds  业务id集合
     * @return 实体集合
     */
    List<DtoBusinessSerialNumber> findByBusinessTypeAndBusinessIdIn(String businessType, Collection<String> businessIds);

    /**
     * 根据流水号类型查询
     *
     * @param serialNumberTypes 流水号类型
     * @return 实体集合
     */
    List<DtoBusinessSerialNumber> findBySerialNumberTypeIn(Collection<String> serialNumberTypes);
}
