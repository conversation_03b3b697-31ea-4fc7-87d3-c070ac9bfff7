insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('0c9b50fc348c4beaa0ab24161a80cc6c', '小于检出限计算', 'sys_pro_unlessexamlimit_type', '检出限一半', 1, 0, '小于检出限计算，一般应用于两类：带入偏差计算（qualityControl）、带入浓度均值计算（sample）。这两个需要分别配置。
①带入偏差计算（qualityControl）：一般应用于“曲线校核样、校正系数检验”偏差计算，小于检出限带入的结果。一般默认取“0”;
②带入浓度均值计算（sample）：一般用于平行样的出证结果、报告中点位均值、批次均值的时候，小于检出限带入的结果。一般默认取“检出限一半”。', '2023-04-11 10:41:20', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2023-12-28 15:46:36', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('0d107c18921849ca8a543c83745e6bfe', '标签二维码大小配置', 'sys.picConfig.configData', '{"picWidth":56,"picHeight":52,"picSize":129,"picLeft":1,"picTop":1}', 1, 0, '应用于样品标签，左上角二维码大小的设置；
+ picWidth：宽度
+ picHeight：高度
+ picSize：二维码存储内容大小，一般值越大越好，用于控制二维码本身四周的白边大小
+ picLeft：左边距
+ picTop：上边距', '2024-02-02 14:02:47', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-02-02 14:05:17', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('117ca63d2068405f89da5bfdf4ffc0bb', '加标样样值计算是否考虑实验室平行', 'sys.jbValue.judgment', 'true', 1, 0, '实验室分析，原样同时添加了“室内平行样”和“加标样”，用于控制“加标样样值”计算是否与平行样求均值，项目一般默认开启；', '2023-09-18 16:25:50', '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, null, '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('1960acd5-176c-4e4c-9a74-451716a4424c', '电子报告点位备注固定字符串（流量信息）', 'sys_pro_report_folderRemarkStr_ll', '、流量数据来自于污染源监测实时在线监测流量瞬时数据。', 1, 0, '报告中，有关“流量信息”的固定描述，在“编制报告-修改报告信息”中通过“自动生成”功能进行获取，并同步生成到报告对应位置。项目上一般默认为空。', '2024-02-28 17:30:20', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-02-28 15:30:20', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('1f5d596bb31a4750a0d5b3b8d91ba863', '初始化密码', 'sys.user.initPassword', '111111', 1, 800, '用于初始化密码用户密码，初始密码不会进行复杂度验证；', '2019-06-21 11:24:24', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2023-08-23 17:17:43', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('2b6c07c3-6130-4cda-9ee7-968f80ca6068', '电子报告点位备注固定字符串（评价信息）', 'sys_pro_report_folderRemarkStr_pj', '排放浓度参照', 1, 0, '拼接点位备注时用该参数配置的固定字串', '2024-02-28 17:30:20', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-02-28 15:30:20', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('3605215632934c3b8842a681ba9afee0', '检测单提交判断是否存在记录单', 'sys.worksheet.form', 'false', 1, 0, '实验室分析提交，是否强制判定原始记录单是否存在。项目上一般默认false，正常只有半绑的项目，然后强制要求将扫描件上传至系统的情况，会配置成true。', '2023-05-17 13:31:11', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-02-04 14:41:34', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('419270a31faa4b36ba26791bdc708ced', '移动端是否强制更新', 'phone.force.renewal', 'false', 1, 0, '设置移动端是否强制更新至最新版本。项目上一般默认关闭；', '2023-08-10 13:08:28', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2023-08-23 17:19:17', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('554103f7-cb14-43a9-bc03-9e8931f6972d', '报告技术说明备注', 'sys_pro_report_technicalRemark', '{检测项目、检测依据由委托单位指定，样品类别由委托单位提供。}{样品检测结果与现场采样、盛样容器、样品运送条件和时效密切相关，上述环节的合规性由委托单位负责。}', 1, 0, '应用于编制报告-电子报告-报告基本信息中，能够配置的内容初始化到“技术说明备注”中。', '2024-02-28 15:30:20', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-03-29 08:56:31', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('6143410359eb440e83ec4cd280f89e9f', '默认样品有效期', 'sys.default.Date', '5', 1, 0, '用于计算样品是否过期，计算的时候，优先获取测试项目上配置的“样品有效期（h）”字段中配置的内容，如果未配置，再调用这里配置的时长；', '2023-12-07 09:30:16', '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, null, '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('7d9d47c5a63f46d791e71cdfcccd08e2', '在线编辑方式', 'sys.editing.mode', 'luckySheet.true;iWebOffice.false', 1, 0, '系统中，除了编制报告模块，其余（例如：现场任务-采样单、实验室分析-原始记录单）默认在线编辑打开方式。考虑iWeboffice系统兼容性，以及国产化要求，项目上一般**默认使用luckysheet。', '2023-05-16 15:17:02', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2023-09-21 13:14:47', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('94dede903be6448b9da616f73151b3cd', '水印全局配置', 'sys.watermark.configData', '{   "show": true,   "txt": "实验室资源管理系统",   "color": "#000",   "width": 350,   "height": 150,   "fontsize": "18px",   "angle": 15,   "alpha": 0.1,   "xSpace": 100,   "ySpace": 50 }', 1, 0, '水印全局配置', '2024-07-03 09:59:56', '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, null, '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('b5d2358c608a4de3a63d76e0e9bb0049', '用户默认首页', 'sys.user.indexUrl', '', 1, 900, '目前仅应用于常州项目，用于不同机构登陆后调用不同的首页。项目初始化默认为空。', '2019-07-01 10:07:49', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2019-09-10 11:14:34', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('c50d718b664a4feca4d1e828b9ea51e9', '现场平行是否参与实验室出证计算', 'sys.sceneparallel.compute', 'false', 1, 0, '用于控制“实验室分析”检测单中，计算出证结果的时候，现场平行样是否带入最终出证结果的计算，项目上一般默认关闭。', '2023-12-21 16:50:55', '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, null, '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('c7fe0b1742364ba1a9cb1387a685a861', '移动端网关地址', 'app.gate.path', 'http://192.168.11.94:6200', 1, 0, '用于设置移动端网关访问地址，用于移动端与平台的数据通信；项目上需要按实际路径进行初始化设置；', '2023-08-10 13:08:04', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2023-08-28 10:14:45', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('ee5f4f424ff44bdebcd9bc6bb29dbae0', '登录页是否显示二维码', 'sys.login.QRCode', 'true', 1, 0, '用于控制登陆界面，是否显示下载移动端的二维码，项目一般默认关闭，有移动端的项目可以开启。', '2023-08-10 13:07:32', '59141356591b48e18e139aa54d9dd351', '超级管理员', null, null, null, '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('fb0f578747e94cf8a644c9b657fa1328', '退出登录跳转地址', 'sys.login.url', 'login', 1, 1000, '用户退出登录的时候，默认跳转的页面，一般应用于有“综合门户”的项目。', '2019-06-21 11:21:56', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2023-02-23 13:57:57', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
insert into "t_sys_config" ("id", "configName", "configKey", "configValue", "enabled", "sortNum", "note", "createDate", "createUserGuid", "createUserName", "updateDate", "updateUserGuid", "updateUserName", "orgGuid", "deptGuid", "deleted") values ('fbc701e7-a124-45b9-a903-33db4d6db719', '电子报告点位备注固定字符串（工况信息）', 'sys_pro_report_folderRemarkStr_gk', '、运行负荷98.5%，工况信息：', 1, 0, '报告中，有关“工况信息”的固定描述，在“编制报告-修改报告信息”中通过“自动生成”功能进行获取，并同步生成到报告对应位置。项目上一般默认为空。', '2024-02-28 17:30:20', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-02-28 15:30:20', '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', null, 0);
