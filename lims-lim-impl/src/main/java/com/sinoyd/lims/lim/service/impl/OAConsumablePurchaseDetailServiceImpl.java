package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePurchaseDetail;
import com.sinoyd.lims.lim.repository.lims.OAConsumablePurchaseDetailRepository;
import com.sinoyd.lims.lim.service.OAConsumablePurchaseDetailService;

import org.springframework.stereotype.Service;

/**
 * 消耗品采购明细 业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-03
 * @since V100R001
 */
@Service
public class OAConsumablePurchaseDetailServiceImpl
        extends BaseJpaServiceImpl<DtoOAConsumablePurchaseDetail, String, OAConsumablePurchaseDetailRepository>
        implements OAConsumablePurchaseDetailService {

}
