package com.sinoyd.lims.pro.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoProjectPlan2Person;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.ProjectPlan2PersonRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.QCProjectCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoProjectJson;
import com.sinoyd.lims.pro.dto.customer.DtoQCProject;
import com.sinoyd.lims.pro.dto.customer.DtoQCProjectCopy;
import com.sinoyd.lims.pro.dto.customer.DtoRecordJson;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;

/**
 * 质控任务操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/2/4
 * @since V100R001
 */
@Service
@Slf4j
public class QCProjectServiceImpl extends BaseJpaServiceImpl<DtoProject, String, ProjectRepository> implements QCProjectService {

    //#region 注入
    @Autowired
    @Qualifier("zkProject")
    private SerialNumberService serialNumberService;

    @Autowired
    @Lazy
    private CodeService codeService;

    @Autowired
    @Lazy
    protected WorkflowService workflowService;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private ProjectPlanService projectPlanService;

    @Autowired
    private ProjectPlanRepository projectPlanRepository;

    @Autowired
    @Lazy
    protected StatusForQCProjectService statusForQCProjectService;

    @Autowired
    @Lazy
    private StatusForRecordService statusForRecordService;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private SamplingPersonConfigRepository samplingPersonConfigRepository;

    @Autowired
    private QCResultEvaluationRepository qcResultEvaluationRepository;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    @Lazy
    protected HomeService homeService;

    @Autowired
    @Lazy
    private SchemeService schemeService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    private StatusForProjectRepository statusForProjectRepository;

    private ProjectPlan2PersonRepository projectPlan2PersonRepository;

    private SampleRepository sampleRepository;

    private SampleTypeRepository sampleTypeRepository;
    //#endregion

    private EnvironmentEnterpriseService environmentEnterpriseService;

    private LocalTaskPeopleCompareRepository localTaskPeopleCompareRepository;

    private Project2InspectRepository project2InspectRepository;

    @Override
    public void findQCProjectByPage(PageBean<DtoQCProject> pb, BaseCriteria qcProjectCriteria) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoQCProject(");
        stringBuilder.append("p.id,p.projectCode,p.projectTypeId,p.projectName,p.status,p.inputTime,");
        stringBuilder.append("p.inceptTime,p.sampleDescription,p.sampleQuantity,p.customerId,p.customerName,");
        stringBuilder.append("p.linkMan,p.linkPhone,p.customerAddress,pl.qcGrade,pl.qcType,pl.qcSource,");
        stringBuilder.append("pl.judgment,pl.responsePerson,p.reportMethod,pl.reportDate,pl.deadLine,p.inceptPersonId,p.isAssist)");
        pb.setEntityName("DtoProject p,DtoProjectPlan pl,DtoStatusForProject s");
        pb.setSelect(stringBuilder.toString());
        comRepository.findByPage(pb, qcProjectCriteria);
        QCProjectCriteria criteria = (QCProjectCriteria) qcProjectCriteria;
        boolean isInspect = criteria.getIsInspect();
        if (StringUtil.isNotNull(pb.getData())) {
            List<String> projectIds = pb.getData().stream().map(DtoQCProject::getId).collect(Collectors.toList());
            List<DtoQCResultEvaluation> qcResultEvaluationList = projectIds.size() > 0 ? qcResultEvaluationRepository.findByProjectIdIn(projectIds) :
                    new ArrayList<>();
            Map<String, String> opinionMap = findStatusOptionMaps(projectIds);
            List<DtoStatusForProject> sfpList = statusForProjectRepository.findByProjectIdIn(projectIds);
            Map<String, String> lastNewOpinionMap = sfpList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getLastNewOpinion())).
                    sorted(Comparator.comparing(DtoStatusForProject::getModifyDate).reversed()).
                    collect(Collectors.groupingBy(DtoStatusForProject::getProjectId, collectingAndThen(Collectors.toList(), value -> value.get(0).getLastNewOpinion())));
            List<String> personIds = pb.getData().stream().map(DtoQCProject::getInceptPersonId).distinct().collect(Collectors.toList());
            List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
            List<DtoReceiveSampleRecord> receiveSampleRecordList = receiveSampleRecordRepository.findByProjectIdIn(projectIds);
            Map<String, DtoProject2Inspect> project2InspectMap = new HashMap<>();
            if (isInspect) {
                List<DtoProject2Inspect> project2InspectList = project2InspectRepository.findByProjectIdIn(projectIds);
                project2InspectMap = project2InspectList.stream().collect(Collectors.toMap(DtoProject2Inspect::getProjectId, p -> p));
            }// 核查对象
            //将list转为map
            Map<String, String> qcResultEvaluationMap = qcResultEvaluationList.stream().collect(Collectors.toMap(DtoQCResultEvaluation::getProjectId, DtoQCResultEvaluation::getId));
            for (DtoQCProject project : pb.getData()) {
                project.setEvaluationId(qcResultEvaluationMap.getOrDefault(project.getId(), UUIDHelper.GUID_EMPTY));
                String lastOpinion = opinionMap.getOrDefault(project.getId(), "");
                project.setLastNewOpinion(lastOpinion);
                if (StringUtils.isNotNullAndEmpty(project.getLastNewOpinion())) {
                    project.setLastNewOpinion(lastNewOpinionMap.getOrDefault(project.getId(), ""));
                }
                Optional<DtoPerson> person = personList.stream().filter(p -> p.getId().equals(project.getInceptPersonId())).findFirst();
                person.ifPresent(p -> project.setInceptPersonName(p.getCName()));
                List<String> receiveIds = receiveSampleRecordList.stream().filter(v -> project.getId().equals(v.getProjectId())).map(DtoReceiveSampleRecord::getId)
                        .collect(Collectors.toList());
                project.setReceiveIds(receiveIds);
                project.setReceiveId(StringUtil.isNotEmpty(receiveIds) ? receiveIds.get(0) : UUIDHelper.GUID_EMPTY);
                if (isInspect) {
                    DtoProject2Inspect project2Inspect = project2InspectMap.getOrDefault(project.getId(), null);
                    project.setProject2Inspect(project2Inspect);
                }
            }
        }
    }

    /**
     * 获取对应模块下的意见Map
     *
     * @param projectIdList 项目id列表
     * @return 意见集合
     */
    protected Map<String, String> findStatusOptionMaps(List<String> projectIdList) {
        List<DtoStatusForProject> sfpList = statusForProjectRepository.findByProjectIdIn(projectIdList);
        return sfpList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getLastNewOpinion())).
                sorted(Comparator.comparing(DtoStatusForProject::getModifyDate).reversed()).
                collect(Collectors.groupingBy(DtoStatusForProject::getProjectId, collectingAndThen(Collectors.toList(), value -> value.get(0).getLastNewOpinion())));
    }

    @Transactional
    public String createProjectCode(String projectTypeId, Date inputTime) {
        return serialNumberService.createNewNumber(projectTypeId, inputTime);
    }

    /**
     * 获取项目详情
     *
     * @param id 项目id
     * @return 项目
     */
    @Override
    public DtoQCProject findQCProject(String id) {
        DtoProject project = repository.findOne(id);
        DtoProjectPlan plan = projectPlanRepository.findByProjectId(id);
        if (StringUtil.isNotNull(plan)) {
            List<DtoProjectPlan2Person> plan2PersonList = projectPlan2PersonRepository.findAllByProjectPlanId(plan.getId());
            plan.setAssessPersonIds(plan2PersonList.stream().map(DtoProjectPlan2Person::getAssessPersonId).collect(Collectors.toList()));
            project.loadFromPlan(plan);
        }

        DtoQCProject qcProject = new DtoQCProject();

        BeanUtils.copyProperties(project, qcProject);
        if (StringUtils.isNotNullAndEmpty(qcProject.getReportMethod())) {
            qcProject.setReportMethodArr(Arrays.asList(project.getReportMethod().split(",")));
        }
        //BUG2024040900655 【重要】【2024-4-11】【马川江】【dev】现场任务模块，针对任务类型为质控任务的，项目点开，应该是质控任务的信息；
        List<DtoReceiveSampleRecord> recordList = receiveSampleRecordRepository.findByProjectId(id);
        if (!recordList.isEmpty()) {
            qcProject.setReceiveId(recordList.get(0).getId());
        }
        qcProject.setReceiveIds(recordList.stream().map(DtoReceiveSampleRecord::getId).collect(Collectors.toList()));
        qcProject.setLocalTaskPeopleCompareList(localTaskPeopleCompareRepository.findByProjectId(id));
        // 核查对象
        qcProject.setProject2Inspect(project2InspectRepository.findByProjectId(id));
        return qcProject;
    }

    @Transactional
    @Override
    public DtoProject save(DtoProject dto) {
        List<DtoProjectType> qcProjectTypes = projectTypeService.findByWorkflowId(EnumWorkflowCode.质控项目.getValue());
        // 筛选对应质控项目类型
        qcProjectTypes = qcProjectTypes.stream().filter(p -> {
            Object typeCOde = JsonIterator.deserialize(p.getConfig(), Map.class).get("LIM_ProjectTypeCode_IND");
            boolean hcTypeCode = StringUtil.isNotNull(typeCOde) && String.valueOf(typeCOde).equals("HC");
            return dto.getIsInspect() == hcTypeCode;
        }).collect(Collectors.toList());

        List<DtoProjectType> qcProjectTypesWithoutSolution = projectTypeService.findByWorkflowId(EnumWorkflowCode.质控项目无方案.getValue());
        if (qcProjectTypes.size() == 0 && qcProjectTypesWithoutSolution.size() == 0) {
            throw new BaseException("请先联系管理员进行质控项目类型的维护！");
        }

        //设置质控任务的项目类型
        this.writeProjectType(dto, qcProjectTypesWithoutSolution, qcProjectTypes);
        DtoProjectType dtoProjectType = projectTypeService.findOne(dto.getProjectTypeId());
        dto.setInputTime(new Date());
        if (StringUtil.isEmpty(dto.getProjectCode())) {
            //表示用户没有输入项目编号，系统自动协助生成
            String projectCode = this.createProjectCode(dto.getProjectTypeId(), dto.getInputTime());
            dto.setProjectCode(projectCode);
        } else {
            //表示用户输入了项目编号，此时需要做唯一性校验
            Integer count = repository.countByProjectCode(dto.getProjectCode());
            if (count > 0) {
                throw new BaseException("项目编号已经存在，请更换项目编号");
            }
        }

        dto.setInceptTime(DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));
        dto.setStatus(EnumProjectStatus.项目登记中.toString());

        workflowService.createInstance(dtoProjectType.getWorkflowId(), dto.getId());

        DtoProjectPlan plan = new DtoProjectPlan();
        plan.loadFromProject(dto);
        projectPlanService.save(plan);
        statusForQCProjectService.createStatus(dto.getId(), EnumLIM.EnumQCProjectModule.项目登记.getCode());
        statusForQCProjectService.createStatus(dto.getId(), EnumLIM.EnumQCProjectModule.项目进度.getCode());
        if (dto.getIsInspect()) {
            DtoProject2Inspect periodCheck = dto.getProject2Inspect();
            periodCheck.setProjectId(dto.getId());
            periodCheck.setInspectType(dto.getQcType());
            project2InspectRepository.save(periodCheck);
        }
        if (isLocalPeopleCompare(dto.getId())) {
            //现场任务人员比对 有几组就创建几个送样单
            List<DtoLocalTaskPeopleCompare> localTaskPeopleCompareList = dto.getLocalTaskPeopleCompareList();
            createLocalPeopleCompareRecord(dto, localTaskPeopleCompareList);
            localTaskPeopleCompareRepository.save(localTaskPeopleCompareList);
            dto.setReceiveId(StringUtil.isNotEmpty(localTaskPeopleCompareList) ? localTaskPeopleCompareList.get(0).getReceiveId() : UUIDHelper.GUID_EMPTY);
            dto.setReceiveIds(localTaskPeopleCompareList.stream().map(DtoLocalTaskPeopleCompare::getReceiveId).collect(Collectors.toList()));
        } else {
            DtoReceiveSampleRecord record = this.createRecord(dto);
            dto.setReceiveId(record.getId());
        }

        //写入json信息
        DtoProjectJson jsonEntity = new DtoProjectJson();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            dto.setJson(objectMapper.writeValueAsString(jsonEntity));
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
        repository.save(dto);

        newLogService.createProjectInfoUpdateLog(Collections.singletonList(dto.getId()), "", EnumLogOperateType.增加项目.toString());
        return dto;
    }

    @Override
    @Transactional
    public void localSubmit(String qcProjectId) {
        List<DtoReceiveSampleRecord> recordList = receiveSampleRecordRepository.findByProjectId(qcProjectId);
        if (!recordList.isEmpty()) {
            recordList.forEach(v -> {
                statusForRecordService.createStatus(v.getId(), EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue());
            });
        }
        DtoProject project = repository.findOne(qcProjectId);
        DtoWorkflowSign workflowSign = new DtoWorkflowSign();
        workflowSign.setNextOperatorId(UUIDHelper.GUID_EMPTY);
        workflowSign.setNextOperator("");
        workflowSign.setOption("");
        workflowSign.setIsAutoStatus(false);
        workflowSign.setSignal("projectLaunch");
        workflowSign.setObjectId(project.getId());
        try {
            workflowService.submitSign(workflowSign);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("项目提交失败!");
        }
        statusForQCProjectService.modifyStatus(project.getStatus(), EnumProjectStatus.开展中.name(), workflowSign, project);
        project.setStatus(EnumProjectStatus.开展中.toString());
        repository.save(project);
        //获取到项目下所有的样品
        List<DtoSample> samples = sampleRepository.findByProjectId(project.getId());
        proService.checkSample(samples, project);
        //环保企业通推送项目信息
        environmentEnterpriseService.pushProjectData(Collections.singletonList(project), EnumProjectStatus.开展中.name());
    }

    @Transactional
    @Override
    public DtoProject update(DtoProject dto) {
        Integer count = repository.countByProjectCodeAndIdNot(dto.getProjectCode(), dto.getId());
        if (count > 0) {
            throw new BaseException("该质控编号已被占用");
        }
        DtoProjectPlan plan = projectPlanRepository.findByProjectId(dto.getId());

        DtoProject project = repository.findOne(dto.getId());
        Map<String, Map<String, Object>> planChange = proService.getCompare(plan, dto);
        Map<String, Map<String, Object>> projectChange = proService.getCompare(project, dto);
        List<String> contents = this.getChangeContent(projectChange, EnumProjectField.项目名称);
        //获取送样单数据
        if (StringUtil.isNotEmpty(dto.getReceiveId()) && EnumPorjectQCGrade.现场质控.getValue().equals(plan.getQcGrade())) {
            //获取考核负责人名称
            if (StringUtil.isNotEmpty(dto.getLeaderId())) {
                DtoPerson leaderPerson = personService.findOne(dto.getLeaderId());
                dto.setLeaderName(leaderPerson.getCName());
            }
            List<DtoSamplingPersonConfig> samplingPersonConfigs = new ArrayList<>();
            DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(dto.getReceiveId());
            handleLocalQcReceiveData(dto, record, samplingPersonConfigs);
            receiveSampleRecordRepository.save(record);
            samplingPersonConfigRepository.deleteByObjectId(record.getId());
            samplingPersonConfigRepository.save(samplingPersonConfigs);
        }
        if (EnumPorjectQCGrade.现场质控.getValue().equals(dto.getQcGrade()) && EnumInnerQCType.人员比对.getValue().equals(dto.getQcType())) {
            List<DtoLocalTaskPeopleCompare> localTaskPeopleCompareList;
            if (dto.getLocalTaskPeopleCompareList() != null) {
                localTaskPeopleCompareList = localTaskPeopleCompareRepository.save(dto.getLocalTaskPeopleCompareList());
            } else {
                localTaskPeopleCompareList = localTaskPeopleCompareRepository.findByProjectId(dto.getId());
            }
            List<String> receiveIds = localTaskPeopleCompareList.stream().map(DtoLocalTaskPeopleCompare::getReceiveId).collect(Collectors.toList());
            List<DtoReceiveSampleRecord> receiveSampleRecordList = StringUtil.isNotEmpty(receiveIds) ?
                    receiveSampleRecordRepository.findAll(receiveIds) : new ArrayList<>();
            List<DtoSample> sampleList = sampleRepository.findByProjectId(dto.getId());
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds) : new ArrayList<>();
            localTaskPeopleCompareList.forEach(config -> {
                String checkPeopleId = config.getCheckPeopleId().split(",")[0];
                String cName = personService.findAll().stream().filter(v -> v.getId().equals(checkPeopleId)).map(DtoPerson::getCName).findFirst().orElse("");
                receiveSampleRecordList.stream().filter(record -> record.getId().equals(config.getReceiveId())).findFirst()
                        .ifPresent(record -> {
                            List<DtoSamplingPersonConfig> samplingPersonConfigs = new ArrayList<>();
                            handleLocalPcQcReceiveData(config, record, samplingPersonConfigs);
                            receiveSampleRecordRepository.save(record);
                            samplingPersonConfigRepository.deleteByObjectId(record.getId());
                            samplingPersonConfigRepository.save(samplingPersonConfigs);
                            List<String> recordSampleIdList = sampleList.stream().filter(v -> record.getId().equals(v.getReceiveId()))
                                    .map(DtoSample::getId).collect(Collectors.toList());
                            analyseDataList.stream().filter(v -> recordSampleIdList.contains(v.getSampleId())).forEach(v -> {
                                v.setAnalystId(checkPeopleId);
                                v.setAnalystName(cName);
                            });
                        });
            });
            analyseDataRepository.save(analyseDataList);
        }
        //处理外部质控的数据内容
        this.handleSpecialData(plan, projectChange, planChange, contents);
//        if (plan.getQcGrade().equals(EnumPorjectQCGrade.外部质控.getValue())) {
//            contents.addAll(this.getChangeContent(projectChange, EnumProjectField.样品描述, EnumProjectField.样品数量, EnumProjectField.委托方,
//                    EnumProjectField.联系人, EnumProjectField.联系电话, EnumProjectField.地址));
//            contents.addAll(this.getChangeContent(planChange, EnumProjectField.质量来源, EnumProjectField.判断依据, EnumProjectField.责任人));
//        }
        contents.addAll(this.getChangeContent(planChange, EnumProjectField.出具报告日期, EnumProjectField.要求完成时间));
        plan.loadFromProject(dto);
        projectPlanService.update(plan);
        super.update(dto);
        if (contents.size() > 0) {
            newLogService.createProjectInfoUpdateLog(Collections.singletonList(dto.getId()), String.join(";", contents), EnumLogOperateType.修改项目.toString());
        }
        return dto;
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        proService.deleteProject(String.valueOf(id));
        return super.logicDeleteById(id);
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> projectIds = new ArrayList<>();
        for (Object id : ids) {
            projectIds.add(String.valueOf(id));
        }
        proService.deleteProjects(projectIds);
        return super.logicDeleteById(ids);
    }

    /**
     * 复制项目
     *
     * @param copy 传输结构
     */
    @Transactional
    @Override
    public void copyProject(DtoQCProjectCopy copy) {
        DtoProject oldProject = repository.findOne(copy.getProjectId());

        DtoProject project = new DtoProject();
        BeanUtils.copyProperties(oldProject, project);
        project.setProjectName(copy.getProjectName());
        project.setInputTime(new Date());
        String projectCode = this.createProjectCode(oldProject.getProjectTypeId(), project.getInputTime());
        project.setProjectCode(projectCode);
        project.setInceptTime(DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));
        project.setStatus(EnumProjectStatus.项目登记中.toString());
        DtoProject dto = new DtoProject();
        project.setId(dto.getId());
        project.setSamplingStatus(dto.getSamplingStatus());
        project.setReportStatus(dto.getReportStatus());
        project.setIsOnline(dto.getIsOnline());
        project.setPushStatus(dto.getPushStatus());
        project.setCreator(dto.getCreator());
        project.setCreateDate(dto.getCreateDate());
        project.setDomainId(dto.getDomainId());
        project.setModifier(dto.getModifier());
        project.setModifyDate(dto.getModifyDate());
        repository.save(project);
        DtoProject2Inspect project2Inspect = project2InspectRepository.findByProjectId(copy.getProjectId());
        if (StringUtil.isNotNull(project2Inspect)) {
            DtoProject2Inspect dtoProject2Inspect = new DtoProject2Inspect();
            BeanUtils.copyProperties(project2Inspect, dtoProject2Inspect, "id");
            dtoProject2Inspect.setProjectId(project.getId());
            project2InspectRepository.save(dtoProject2Inspect);
        }

        DtoProjectPlan projectPlan = new DtoProjectPlan();
        DtoProjectPlan oldPlan = projectPlanRepository.findByProjectId(copy.getProjectId());
        projectPlan.setQcGrade(oldPlan.getQcGrade());
        projectPlan.setQcType(oldPlan.getQcType());
        projectPlan.setQcSource(oldPlan.getQcSource());
        projectPlan.setJudgment(oldPlan.getJudgment());
        projectPlan.setResponsePerson(oldPlan.getResponsePerson());
        projectPlan.setReportDate(copy.getReportDate());
        projectPlan.setDeadLine(copy.getDeadLine());
        projectPlan.setProjectId(project.getId());
        projectPlanRepository.save(projectPlan);
        project.setQcType(projectPlan.getQcType());
        project.setQcGrade(projectPlan.getQcGrade());

        workflowService.createInstance(EnumWorkflowCode.质控项目.getValue(), project.getId());
        statusForQCProjectService.createStatus(project.getId(), EnumLIM.EnumQCProjectModule.项目登记.getCode());
        statusForQCProjectService.createStatus(project.getId(), EnumLIM.EnumQCProjectModule.项目进度.getCode());

        DtoReceiveSampleRecord record = this.createRecord(project);

        List<DtoReceiveSampleRecord> oldRecords = receiveSampleRecordRepository.findByProjectId(oldProject.getId());
        schemeService.copyOutsideProjectSample(oldProject.getId(), oldRecords.get(0).getId(), record, project);

        reloadRecordJson(record);
    }

    /**
     * 重新加载送样单Json数据
     *
     * @param record 送样单数据
     */
    private void reloadRecordJson(DtoReceiveSampleRecord record) {
        List<DtoSample> samList = sampleRepository.findByReceiveId(record.getId());
        if (samList.size() > 0) {
            Set<String> samTypeIds = samList.stream().map(DtoSample::getSampleTypeId).collect(Collectors.toSet());
            List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll(samTypeIds);
            Set<String> sampleTypeNames = sampleTypes.stream().map(DtoSampleType::getTypeName).collect(Collectors.toSet());
            DtoRecordJson json = record.getJson() != null
                    ? JsonIterator.deserialize(record.getJson(), DtoRecordJson.class)
                    : new DtoRecordJson();
            json.setSampleTypeIds(String.join(",", samTypeIds));
            json.setLabSampleTypes(String.join(",", sampleTypeNames));
            json.setSampleNum(samList.size());
            record.setJson(JsonStream.serialize(json));
        }
        receiveSampleRecordRepository.save(record);
    }

    /**
     * 工作流操作
     *
     * @param dtoWorkflowSign 实体
     */
    @Transactional
    @Override
    public void projectSignal(DtoWorkflowSign dtoWorkflowSign) {
        try {
            if ("evaluateSubmit".equals(dtoWorkflowSign.getSignal())) {
                newLogService.createFinishProjectLogs(Arrays.asList(dtoWorkflowSign.getObjectId()), dtoWorkflowSign.getOption(), UUIDHelper.GUID_EMPTY, "");
            }
            if (StringUtils.isNotNullAndEmpty(dtoWorkflowSign.getNextOperatorId()) && !UUIDHelper.GUID_EMPTY.equals(dtoWorkflowSign.getNextOperatorId())) {
                DtoPerson per = personService.findOne(dtoWorkflowSign.getNextOperatorId());
                dtoWorkflowSign.setNextOperator(StringUtil.isNotNull(per) ? per.getCName() : "");
            } else {
                dtoWorkflowSign.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            }
            if (dtoWorkflowSign.getObjectIds().size() > 0) {
                dtoWorkflowSign.setIsAutoStatus(false);
                List<DtoProject> projectList = this.findAll(dtoWorkflowSign.getObjectIds());
                String from = projectList.get(0).getStatus();
                String to = workflowService.submitSign(dtoWorkflowSign);
                if (!StringUtils.isNotNullAndEmpty(to) && "evaluateSubmit".equals(dtoWorkflowSign.getSignal())) {
                    to = EnumProjectStatus.已办结.toString();
                }
                for (DtoProject project : projectList) {
                    statusForQCProjectService.modifyStatus(from, to, dtoWorkflowSign, project);
                }
                repository.updateProjectStatus(dtoWorkflowSign.getObjectIds(), to, PrincipalContextUser.getPrincipal().getUserId(), new Date());

                //计算首页数据缓存
                String currentModule = from.equals(EnumProjectStatus.项目登记中.toString()) ? EnumLIM.EnumHomeTaskModule.质控任务登记.getValue() : EnumProjectStatus.getModuleCode(from);
                List<String> nextModules = EnumProjectStatus.getModuleCodes(to);
                homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                        PrincipalContextUser.getPrincipal().getOrgId()
                        , currentModule, nextModules);
            }
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            throw new BaseException("异常错误");
        }
    }

    @Override
    public boolean isLocalPeopleCompare(String projectId) {
        DtoProjectPlan plan = projectPlanRepository.findByProjectId(projectId);
        return plan != null && EnumPorjectQCGrade.现场质控.getValue().equals(plan.getQcGrade()) && EnumInnerQCType.人员比对.getValue().equals(plan.getQcType());
    }

    /**
     * 处理项目类型
     *
     * @param dto                           质控任务
     * @param qcProjectTypesWithoutSolution 不编制方案的项目类型
     * @param qcProjectTypes                编制方案的项目类型
     */
    protected void writeProjectType(DtoProject dto, List<DtoProjectType> qcProjectTypesWithoutSolution,
                                    List<DtoProjectType> qcProjectTypes) {
        if (EnumInnerQCType.理论考核.getValue().equals(dto.getQcType()) || EnumInnerQCType.现场操作.getValue().equals(dto.getQcType())) {
            dto.setProjectTypeId(qcProjectTypesWithoutSolution.get(0).getId());
        } else {
            dto.setProjectTypeId(qcProjectTypes.get(0).getId());
        }
    }

    /**
     * 处理特殊的项目字段
     *
     * @param plan          项目计划
     * @param projectChange 项目更新字段
     * @param planChange    项目计划更新字段
     * @param contents      内容
     */
    protected void handleSpecialData(DtoProjectPlan plan, Map<String, Map<String, Object>> projectChange,
                                     Map<String, Map<String, Object>> planChange, List<String> contents) {
        if (plan.getQcGrade().equals(EnumPorjectQCGrade.外部质控.getValue())) {
            contents.addAll(this.getChangeContent(projectChange, EnumProjectField.样品描述, EnumProjectField.样品数量, EnumProjectField.委托方,
                    EnumProjectField.联系人, EnumProjectField.联系电话, EnumProjectField.地址));
            contents.addAll(this.getChangeContent(planChange, EnumProjectField.质量来源, EnumProjectField.判断依据, EnumProjectField.责任人));
        }
    }

    /**
     * 处理现场质控送样单数据
     *
     * @param dto     更新项目实体
     * @param record  送样单
     * @param spcList 送样单采样人员
     */
    protected void handleLocalQcReceiveData(DtoProject dto,
                                            DtoReceiveSampleRecord record,
                                            List<DtoSamplingPersonConfig> spcList) {
        //送样人 - > 考核负责人
        record.setSenderId(dto.getLeaderId());
        record.setSenderName(dto.getLeaderName());
        //采样日期/送样日期->考核日期
        record.setSendTime(dto.getDeadLine());
        record.setSamplingTime(dto.getDeadLine());
        //采样人-> 考核人员
        List<DtoPerson> personList = personService.findAll(dto.getAssessPersonIds());
        for (DtoPerson person : personList) {
            DtoSamplingPersonConfig spc = new DtoSamplingPersonConfig();
            spc.setObjectId(record.getId());
            spc.setObjectType(EnumSamplingType.送样单.getValue());
            spc.setSamplingPersonId(person.getId());
            spc.setSamplingPerson(person.getCName());
            spcList.add(spc);
        }
    }

    /**
     * 处理现场质控送样单数据
     *
     * @param dto     更新项目实体
     * @param record  送样单
     * @param spcList 送样单采样人员
     */
    protected void handleLocalPcQcReceiveData(DtoLocalTaskPeopleCompare dto,
                                              DtoReceiveSampleRecord record,
                                              List<DtoSamplingPersonConfig> spcList) {
        //送样人 - > 考核负责人
        record.setSenderId(dto.getLeaderId());
        List<DtoPerson> personList = personService.findAll();
        personList.stream().filter(v -> v.getId().equals(dto.getLeaderId())).findFirst().ifPresent(v -> record.setSenderName(v.getCName()));
        //采样日期/送样日期->考核日期
        record.setSendTime(dto.getCheckDate());
        record.setSamplingTime(dto.getCheckDate());
        //采样人-> 考核人员
        List<String> checkPeopleIds = Arrays.asList(dto.getCheckPeopleId().split(","));
        List<DtoPerson> checkPersonList = personList.stream().filter(v -> checkPeopleIds.contains(v.getId())).collect(Collectors.toList());
        for (DtoPerson person : checkPersonList) {
            DtoSamplingPersonConfig spc = new DtoSamplingPersonConfig();
            spc.setObjectId(record.getId());
            spc.setObjectType(EnumSamplingType.送样单.getValue());
            spc.setSamplingPersonId(person.getId());
            spc.setSamplingPerson(person.getCName());
            spcList.add(spc);
        }
    }

    /**
     * 添加送样单
     *
     * @param project 项目
     */
    private List<DtoReceiveSampleRecord> createLocalPeopleCompareRecord(DtoProject project, List<DtoLocalTaskPeopleCompare> compareConfigList) {
        List<DtoReceiveSampleRecord> list = new ArrayList<>();
        //获取考核负责人名称
        List<String> leaderIds = compareConfigList.stream().map(DtoLocalTaskPeopleCompare::getLeaderId)
                .filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        List<DtoPerson> allPersonList = personService.findAll();
        if (StringUtil.isNotEmpty(leaderIds)) {
            List<String> names = allPersonList.stream().filter(v -> leaderIds.contains(v.getId())).map(DtoPerson::getCName)
                    .sorted().collect(Collectors.toList());
            project.setLeaderName(String.join("、", names));
        }
        compareConfigList.forEach(v -> {
            DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
            v.setReceiveId(record.getId());
            v.setProjectId(project.getId());
            record.setProjectId(project.getId());
            record.setRecordCode(receiveSampleRecordService.createReceiveSampleRecordCode());
            record.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.新建.getValue());
            record.setStatus(EnumLIM.EnumReceiveRecordStatus.新建.toString());
            record.setReceiveType(EnumReceiveType.现场送样.getValue());
            List<DtoSamplingPersonConfig> spcList = new ArrayList<>();
            //送样人 - > 考核负责人
            record.setSenderId(v.getLeaderId());
            allPersonList.stream().filter(p -> p.getId().equals(v.getLeaderId())).map(DtoPerson::getCName).findFirst().ifPresent(record::setSenderName);
            //采样日期/送样日期->考核日期
            record.setSendTime(v.getCheckDate());
            record.setSamplingTime(v.getCheckDate());
            //采样人-> 考核人员
            List<String> checkPersonIds = Arrays.asList(v.getCheckPeopleId().split(","));
            List<DtoPerson> personList = allPersonList.stream().filter(p -> checkPersonIds.contains(p.getId())).collect(Collectors.toList());
            for (DtoPerson person : personList) {
                DtoSamplingPersonConfig spc = new DtoSamplingPersonConfig();
                spc.setObjectId(record.getId());
                spc.setObjectType(EnumSamplingType.送样单.getValue());
                spc.setSamplingPersonId(person.getId());
                spc.setSamplingPerson(person.getCName());
                spcList.add(spc);
            }
            record.setRecorderId(PrincipalContextUser.getPrincipal().getUserId());
            record.setReceiveTime(project.getInputTime());
            record.setRemark("");
            record.setUploadStatus(EnumReceiveUploadStatus.数据录入中.getValue());
            record.setUploadTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
            samplingPersonConfigRepository.save(spcList);
            receiveSampleRecordRepository.save(record);
            newLogService.createLog(record.getId(), String.format("创建了送样单:%s", record.getRecordCode()), "",
                    EnumLogType.送样单信息.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.创建送样单.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
            newLogService.createLog(record.getProjectId(), String.format("创建了送样单:%s", record.getRecordCode()), "",
                    EnumLogType.项目送样单.getValue(), EnumLogObjectType.项目.getValue(), EnumLogOperateType.创建送样单.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        });

        return list;
    }

    /**
     * 添加送样单
     *
     * @param project 项目
     */
    private DtoReceiveSampleRecord createRecord(DtoProject project) {
        //获取考核负责人名称
        if (StringUtil.isNotEmpty(project.getLeaderId())) {
            DtoPerson leaderPerson = personService.findOne(project.getLeaderId());
            project.setLeaderName(leaderPerson.getCName());
        }
        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
        record.setProjectId(project.getId());
        record.setRecordCode(receiveSampleRecordService.createReceiveSampleRecordCode());
        record.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.新建.getValue());
        record.setStatus(EnumLIM.EnumReceiveRecordStatus.新建.toString());
        record.setReceiveType(EnumReceiveType.现场送样.getValue());
        List<DtoSamplingPersonConfig> spcList = new ArrayList<>();
        if (EnumPorjectQCGrade.现场质控.getValue().equals(project.getQcGrade())) {
            record.setInfoStatus(EnumReceiveInfoStatus.信息登记中.getValue());
            //送样人 - > 考核负责人  采样人-> 考核人员  采样日期/送样日期->考核日期
            handleLocalQcReceiveData(project, record, spcList);
        } else {
            record.setInfoStatus(EnumReceiveInfoStatus.已确认.getValue());
            record.setSendTime(project.getInputTime());
            record.setSamplingTime(project.getInputTime());
            record.setSenderId(PrincipalContextUser.getPrincipal().getUserId());
            record.setSenderName(PrincipalContextUser.getPrincipal().getUserName());
            DtoSamplingPersonConfig spc = new DtoSamplingPersonConfig();
            spc.setObjectId(record.getId());
            spc.setObjectType(EnumSamplingType.送样单.getValue());
            spc.setSamplingPersonId(PrincipalContextUser.getPrincipal().getUserId());
            spc.setSamplingPerson(PrincipalContextUser.getPrincipal().getUserName());
            spcList.add(spc);
        }
        record.setRecorderId(PrincipalContextUser.getPrincipal().getUserId());
        record.setReceiveTime(project.getInputTime());
        record.setRemark("");
        record.setUploadStatus(EnumReceiveUploadStatus.数据录入中.getValue());
        record.setUploadTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        samplingPersonConfigRepository.save(spcList);
        receiveSampleRecordRepository.save(record);
        //理论考核和现场操作无需编制方案，无需创建送样单，这里创建送样单只是为了质控任务进度查询使用
        if (!EnumInnerQCType.理论考核.getValue().equals(project.getQcType()) && !EnumInnerQCType.现场操作.getValue().equals(project.getQcType())) {
            newLogService.createLog(record.getId(), String.format("创建了送样单:%s", record.getRecordCode()), "",
                    EnumLogType.送样单信息.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.创建送样单.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
            newLogService.createLog(record.getProjectId(), String.format("创建了送样单:%s", record.getRecordCode()), "",
                    EnumLogType.项目送样单.getValue(), EnumLogObjectType.项目.getValue(), EnumLogOperateType.创建送样单.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
        return record;
    }

    protected List<String> getChangeContent(Map<String, Map<String, Object>> map, EnumProjectField... fields) {
        String format = "</br>%s由'%s',修改为'%s'";
        List<DtoPerson> personList = personService.findAll();
        List<String> contents = new ArrayList<>();
        for (EnumProjectField field : fields) {
            if (map.containsKey(field.getValue())) {
                String from = StringUtil.isNull(map.get(field.getValue()).get("from")) ? "" : map.get(field.getValue()).get("from").toString();
                String to = StringUtil.isNull(map.get(field.getValue()).get("to")) ? "" : map.get(field.getValue()).get("to").toString();
                switch (field) {
                    case 责任人:
                        contents.add(String.format(format, field.toString(),
                                personList.stream().filter(p -> p.getId().equals(from)).map(DtoPerson::getCName).findFirst().orElse(""),
                                personList.stream().filter(p -> p.getId().equals(to)).map(DtoPerson::getCName).findFirst().orElse("")));
                        break;

                    case 报告出具方式:
                        List<DtoCode> reportMethods = codeService.findCodes(ProCodeHelper.REPORT_METHOD);
                        contents.add(String.format(format, field.toString(),
                                String.join(",", reportMethods.stream().filter(p -> Arrays.asList(from.split(",")).contains(p.getDictCode())).map(DtoCode::getDictName).collect(Collectors.toList())),
                                String.join(",", reportMethods.stream().filter(p -> Arrays.asList(to.split(",")).contains(p.getDictCode())).map(DtoCode::getDictName).collect(Collectors.toList()))));
                        break;

                    default:
                        contents.add(String.format(format, field.toString(), from, to));
                        break;
                }
            }
        }
        return contents;
    }

    @Override
    public DtoProject findAttachPath(String id) {
        return super.findOne(id);
    }

    @Autowired
    public void setStatusForProjectRepository(StatusForProjectRepository statusForProjectRepository) {
        this.statusForProjectRepository = statusForProjectRepository;
    }

    @Autowired
    public void setProjectPlan2PersonRepository(ProjectPlan2PersonRepository projectPlan2PersonRepository) {
        this.projectPlan2PersonRepository = projectPlan2PersonRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    @Lazy
    public void setEnvironmentEnterpriseService(EnvironmentEnterpriseService environmentEnterpriseService) {
        this.environmentEnterpriseService = environmentEnterpriseService;
    }

    @Autowired
    public void setLocalTaskPeopleCompareRepository(LocalTaskPeopleCompareRepository localTaskPeopleCompareRepository) {
        this.localTaskPeopleCompareRepository = localTaskPeopleCompareRepository;
    }

    @Autowired
    public void setProject2InspectRepository(Project2InspectRepository project2InspectRepository) {
        this.project2InspectRepository = project2InspectRepository;
    }
}
