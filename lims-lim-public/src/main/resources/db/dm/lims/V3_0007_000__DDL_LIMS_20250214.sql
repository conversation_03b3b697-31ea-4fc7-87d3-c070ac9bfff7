ALTER TABLE TB_QA_YearlyManagementReviewPlan  add column detailId  varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';
comment on column TB_QA_YearlyManagementReviewPlan.detailId is '计划标识';
ALTER TABLE TB_QA_YearlyQualityControlPlan  add column deptId  varchar(50);
comment on column TB_QA_YearlyQualityControlPlan.deptId is '责任部门标识';
ALTER TABLE TB_QA_YearlyInnerAuditPlan  add column deptId  varchar(50);
comment on column TB_QA_YearlyInnerAuditPlan.deptId is '责任部门标识';
ALTER TABLE TB_QA_YearlyManagementReviewPlan  add column deptId  varchar(50);
comment on column TB_QA_YearlyManagementReviewPlan.deptId is '责任部门标识';
ALTER TABLE TB_QA_YearlyInnerAuditPlan RENAME COLUMN chargePersonId TO personId;
ALTER TABLE TB_QA_YearlyManagementReviewPlan RENAME COLUMN chargePersonId TO personId;
ALTER TABLE TB_QA_YearlyInnerAuditPlan  add column planType int NOT NULL DEFAULT 0;
comment on column TB_QA_YearlyInnerAuditPlan.planType is '计划状态,0计划内/1计划外';
ALTER TABLE TB_QA_YearlyManagementReviewPlan  add column planType int  NOT NULL DEFAULT 0;
comment on column TB_QA_YearlyManagementReviewPlan.planType is '计划状态,0计划内/1计划外';


