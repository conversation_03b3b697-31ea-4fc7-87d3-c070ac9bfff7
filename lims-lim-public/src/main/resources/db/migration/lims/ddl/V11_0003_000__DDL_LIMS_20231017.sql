-- 固定资产管理
CREATE TABLE `TB_LIM_FixedProperty`
(
    `id`            varchar(50) NOT NULL COMMENT '主键id',
    `assetsName`    varchar(50) NOT NULL COMMENT '固定资产名称',
    `brandModel`    varchar(50)          DEFAULT NULL COMMENT '品牌型号',
    `assetsNo`      varchar(11) NOT NULL COMMENT '资产编号',
    `purchaseDate`  datetime             DEFAULT NULL COMMENT '采购时间',
    `purchasePrice` decimal(10, 2)       DEFAULT NULL COMMENT '采购价格',
    `supplier`      varchar(50)          DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '供应商（Guid）',
    `assetsType`    varchar(50) NOT NULL COMMENT '资产类型',
    `deptId`        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属科室（Guid）',
    `status`        int(10) NOT NULL COMMENT '资产状态 (1使用中，2已报废)',
    `manager`       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '管理人员（Guid）',
    `orgId`         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    `creator`       varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    `createDate`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `domainId`      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    `modifier`      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    `modifyDate`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;