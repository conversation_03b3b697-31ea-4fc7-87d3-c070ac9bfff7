package com.sinoyd.base.controller;

import com.sinoyd.base.criteria.LogForLuckySheetCriteria;
import com.sinoyd.base.dto.lims.DtoLogForLuckySheet;
import com.sinoyd.base.service.LogForLuckySheetService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * luckySheet操作记录
 * <AUTHOR>
 * @version V1.0.0 2022/10/24
 * @since V100R001
 */
@Api(tags = "luckySheet操作记录")
@RestController
@RequestMapping("/api/base/logForLuckySheet")
public class LogForLuckySheetController extends BaseJpaController<DtoLogForLuckySheet,String,LogForLuckySheetService> {

    /**
     * 分页查询luckysheet操作记录
     * @param criteria 查询条件
     * @return RestResponse<List<DtoLogForLuckySheet>>
     */
    @ApiOperation(value = "分页查询luckysheet操作记录", notes = "分页查询luckysheet操作记录")
    @GetMapping
    public RestResponse<List<DtoLogForLuckySheet>> findByPage(LogForLuckySheetCriteria criteria){
        RestResponse<List<DtoLogForLuckySheet>> restResp = new RestResponse<>();
        PageBean<DtoLogForLuckySheet> page = super.getPageBean();
        service.findByPage(page,criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

}
