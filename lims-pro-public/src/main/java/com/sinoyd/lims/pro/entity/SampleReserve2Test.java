package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * SampleReserve2Sample实体
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/20
 */
@MappedSuperclass
@ApiModel(description = "SampleReserve2Sample")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SampleReserve2Test implements BaseEntity, Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("样品领取标识")
    private String reserveId;

    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析项目id")
    private String analyzeItemId;

    @Column(length = 50)
    @ApiModelProperty("分析方法名称")
    private String redAnalyseItemName;
}
