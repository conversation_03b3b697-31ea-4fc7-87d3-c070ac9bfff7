package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.base.criteria.EvaluationCriteriaCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.repository.rcc.EvaluationCriteriaRepository;
import com.sinoyd.base.service.EvaluationCriteriaService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 评价标准同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/23
 */
@Component
@DependsOn({"springContextAware"})
@Order(16)
@Slf4j
public class EvaluationSync extends AbsDataSync<DtoEvaluationCriteria> {

    @Autowired
    private EvaluationCriteriaService service;

    @Autowired
    private EvaluationCriteriaRepository repository;

    /**
     * 数据比较
     *
     * @param evaluationIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoEvaluationCriteria>> compareData(List<String> evaluationIds) {
        List<DtoEvaluationCriteria> projectData = service.findAll();

        List<DtoEvaluationCriteria> standardData = queryStandardData();
        //如果testIdList不是空，则表示选择部分同步
        if (StringUtil.isNotEmpty(evaluationIds)&&StringUtil.isNotEmpty(standardData)) {
            standardData = standardData.parallelStream().filter(p -> evaluationIds.contains(p.getId()))
                    .collect(Collectors.toList());
        }
        //比较数据
        return compareData(standardData, projectData);
    }

    /**
     * 同步数据
     *
     * @param evaluationIds         需要同步的数据id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> evaluationIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoEvaluationCriteria>> compareResult = compareData(evaluationIds);
        Optional<DtoDataCompareResult<DtoEvaluationCriteria>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoEvaluationCriteria errorDto = null;
            try {
                for (DtoEvaluationCriteria dtoTest : r.getAddDataList()) {
                    errorDto = dtoTest;
                    if (repository.findOne(dtoTest.getId()) != null) {
                        service.update(dtoTest);
                    }else{
                        service.save(dtoTest);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    @Override
    public List<DtoEvaluationCriteria> queryStandardData() {
        String url = standardDataConfig.getHost() + getStandardDataQueryUrl() +
                "?page=1&rows=" + + Integer.MAX_VALUE + "&sampleTypeId=&key=&status=&categoryId=&sort=";
        List<Map<String, Object>> resultMapList = (List<Map<String, Object>>) queryStandardData(url).getBody().get("data");
        return convertObject(resultMapList);
    }

    @Override
    public ResponseEntity<JSONObject> queryStandardData(BaseCriteria baseCriteria, int page, int rows, String sort) {
        EvaluationCriteriaCriteria criteria = (EvaluationCriteriaCriteria) baseCriteria;
        String url = standardDataConfig.getHost() + getStandardDataQueryUrl() +
                "?page=" + page + "&rows=" + rows + "&sampleTypeId=" + criteria.getSampleTypeId() +
                "&key=" + criteria.getKey() + "&status=&categoryId=" + "&sort=" + sort;
        return queryStandardData(url);
    }

    @Override
    public boolean mustSync() {
        return true;
    }

    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.评价标准.name();
    }

    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.评价标准.getValue();
    }

    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/base/evaluationCriteria";
    }

    @Override
    public List<Integer> getDependDataType() {
        return Arrays.asList(
                EnumLIM.EnumDataSyncType.分析项目.getValue(),
                EnumLIM.EnumDataSyncType.评价标准.getValue(),
                EnumLIM.EnumDataSyncType.评价标准等级.getValue(),
                EnumLIM.EnumDataSyncType.评价标准限值.getValue(),
                EnumLIM.EnumDataSyncType.评价标准分析项目.getValue()
        );
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.评价标准.getValue();
    }

}
