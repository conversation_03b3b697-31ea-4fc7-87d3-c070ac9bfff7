package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoCurve;

import java.util.List;


/**
 * Curve数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/5
 * @since V100R001
 */
public interface CurveRepository extends IBaseJpaRepository<DtoCurve, String> {

    /**
     * 根据测试项目获取相应的曲线
     *
     * @param testIds 测试项目ids
     * @return 返回相应的曲线
     */
    List<DtoCurve> findByTestIdIn(List<String> testIds);
}