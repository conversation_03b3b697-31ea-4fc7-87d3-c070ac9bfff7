package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoCompareJudge;
import com.sinoyd.lims.lim.repository.rcc.CompareJudgeRepository;
import com.sinoyd.lims.lim.service.CompareJudgeService;
import com.sinoyd.lims.lim.vo.CompareJudgeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * CompareJudge操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/14
 * @since V100R001
 */
@Service
public class CompareJudgeServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCompareJudge, String, CompareJudgeRepository> implements CompareJudgeService {


    private AnalyzeItemRepository analyzeItemRepository;

    @Override
    public void findByPage(PageBean<DtoCompareJudge> page, BaseCriteria criteria) {
        page.setEntityName("DtoCompareJudge a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData());
        page.getData().sort(Comparator.comparing(DtoCompareJudge::getCheckType).thenComparing(DtoCompareJudge::getAnalyzeItemName));
    }

    @Transactional
    @Override
    public DtoCompareJudge saveCompareJudge(CompareJudgeVO vo) {
        DtoCompareJudge compareJudge = getCompareJudge(vo);
        return save(compareJudge);
    }

    /**
     * 转换为评判标准dto对象
     *
     * @param vo 评判标准vo对象
     * @return 评判标准dto对象
     */
    private DtoCompareJudge getCompareJudge(CompareJudgeVO vo) {
        String defaultStandardNum = vo.getDefaultStandardNum();
        Integer dftNum = null;
        if (StringUtil.isNotEmpty(defaultStandardNum)) {
            try {
                dftNum = Integer.parseInt(defaultStandardNum);
            } catch (NumberFormatException e) {
                throw new BaseException("默认标样数最大长度不能超过2147483647");
            }
        }
        DtoCompareJudge compareJudge = new DtoCompareJudge();
        BeanUtils.copyProperties(vo, compareJudge, "defaultStandardNum", "orgId", "domainId");
        compareJudge.setDefaultStandardNum(dftNum);
        return compareJudge;
    }

    @Override
    @Transactional
    public DtoCompareJudge save(DtoCompareJudge entity) {
        verify(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoCompareJudge updateCompareJudge(CompareJudgeVO vo) {
        DtoCompareJudge compareJudge = getCompareJudge(vo);
        return update(compareJudge);
    }

    @Override
    @Transactional
    public DtoCompareJudge update(DtoCompareJudge entity) {
        verify(entity);
        return super.update(entity);
    }

    /**
     * 校验分析项目和检测类型唯一性
     *
     * @param entity
     */
    private void verify(DtoCompareJudge entity) {
        if (repository.countByAnalyzeItemIdAndCheckTypeAndIdNot(entity.getAnalyzeItemId(), entity.getCheckType(), entity.getId()) > 0) {
            throw new BaseException("已添加过该分析项目相关比对评判标准");
        }
    }

    /**
     * 填充冗余字段
     *
     * @param dataList 数据集合
     */
    private void fillingTransientFields(List<DtoCompareJudge> dataList) {
        List<String> analyzeItemIds = dataList.stream().map(DtoCompareJudge::getAnalyzeItemId).distinct().collect(Collectors.toList());
        List<DtoAnalyzeItem> analyzeItems = StringUtil.isNotEmpty(analyzeItemIds) ? analyzeItemRepository.findAll(analyzeItemIds) : new ArrayList<>();
        dataList.forEach(data -> {
            Optional<DtoAnalyzeItem> analyzeItem = analyzeItems.stream().filter(a -> a.getId().equals(data.getAnalyzeItemId())).findFirst();
            analyzeItem.ifPresent(a -> data.setAnalyzeItemName(a.getAnalyzeItemName()));
        });
    }

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }
}
