package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoCurveDetail;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * CurveDetail数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/5
 * @since V100R001
 */
public interface CurveDetailRepository extends IBaseJpaPhysicalDeleteRepository<DtoCurveDetail, String> {
    /**
     * 删除对应标准曲线下的明细
     *
     * @param curveId 标准曲线id
     * @return 删除的条数
     */
    @Transactional
    Integer deleteByCurveId(String curveId);

    /**
     * 删除对应标准曲线下的明细
     *
     * @param curveIds 标准曲线id集合
     * @return 删除的条数
     */
    @Transactional
    Integer deleteByCurveIdIn(List<String> curveIds);

    /**
     * 按标准曲线id查询明细
     *
     * @param curveId 标准曲线id
     * @return 返回标准曲线明细
     */
    List<DtoCurveDetail> findByCurveIdOrderByOrderNumDesc(String curveId);

    /**
     * 根据曲线获取相关的曲线明细（只获取高低浓度点的数据，及加入体积为0，或者加入
     *
     * @param curveIds 曲线id
     * @return 返回曲线的明细
     */
    List<DtoCurveDetail> findByCurveIdIn(List<String> curveIds);
}