package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品管理查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntSupplierServiceCriteria extends BaseCriteria {

    /**
     * 关键字：商品名称、商品编码、商品规格、商品类型
     */
    private String key;
    /**
     * 企业id
     */
    private String entId;

    @Override
    public String getCondition() {

        values.clear();

        StringBuffer condition = new StringBuffer();

        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (goodsName like :key or goodsCode like :key or goodsModel like :key or goodsType like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtils.isNotNullAndEmpty(entId) && !UUIDHelper.GUID_EMPTY.equals(this.entId)) {
            condition.append(" and entId = :entId");
            values.put("entId", this.entId);
        }

        return condition.toString();
    }
}