package com.sinoyd.lims.lim.repository.lims;

import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoFileGrantRecovery;

import org.springframework.data.jpa.repository.Query;
/**
 * FileGrantRecoveryRepository数据访问接口
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
public interface FileGrantRecoveryRepository extends IBaseJpaPhysicalDeleteRepository<DtoFileGrantRecovery, String> {

    /**
     * 获取最近的一条记录
     * @return 
     */
    @Query("select p from DtoFileGrantRecovery p order by p.createDate desc")
    public List<DtoFileGrantRecovery> getList();

}