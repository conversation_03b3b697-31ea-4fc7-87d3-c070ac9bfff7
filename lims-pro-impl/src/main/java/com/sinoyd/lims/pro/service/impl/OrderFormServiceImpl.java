package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoFeeConfig;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.repository.lims.FeeConfigRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.ProjectTypeRepository;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.dto.customer.DtoOrderSubmit;
import com.sinoyd.lims.pro.dto.customer.DtoSignOrder;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.LogForOrderFormService;
import com.sinoyd.lims.pro.service.NewLogService;
import com.sinoyd.lims.pro.service.OrderFormService;
import com.sinoyd.lims.pro.service.SerialNumberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * OrderForm操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
@Service
@Slf4j
public class OrderFormServiceImpl extends BaseJpaServiceImpl<DtoOrderForm, String, OrderFormRepository> implements OrderFormService {

    @Autowired
    private ProjectTypeService projectTypeService;

    @Autowired
    private ProjectTypeRepository projectTypeRepository;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private OrderQuotationRepository orderQuotationRepository;

    @Autowired
    private QuotationDetailRepository quotationDetailRepository;

    @Autowired
    private OtherDetailRepository otherDetailRepository;

    @Autowired
    private QuotationDetail2TestRepository quotationDetail2TestRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private FeeConfigRepository feeConfigRepository;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private OrderContractRepository orderContractRepository;

    @Autowired
    @Qualifier("project")
    @Lazy
    private SerialNumberService serialNumberService;

    @Autowired
    private AreaService areaService;

    @Autowired
    @Lazy
    private LogForOrderFormService logForOrderFormService;

    @Autowired
    private PersonRepository personRepository;

    @Override
    public void findByPage(PageBean<DtoOrderForm> pb, BaseCriteria orderFormCriteria) {
        pb.setEntityName("DtoOrderForm a ");
        pb.setSelect("select a ");
        comRepository.findByPage(pb, orderFormCriteria);
        List<DtoOrderForm> datas = pb.getData();

        List<DtoOrderForm> newDatas = new ArrayList<>();
        if (StringUtil.isNotEmpty(datas)) {
            Iterator<DtoOrderForm> ite = datas.iterator();
            while (ite.hasNext()) {
                Object obj = ite.next();
                DtoOrderForm orderForm = (DtoOrderForm) obj;
                orderForm.setOrderStatusName(Objects.requireNonNull(EnumPRO.EnumOrderStatus.getByValue(orderForm.getOrderStatus())).name());
                orderForm.setPushStatusName(Objects.requireNonNull(EnumPRO.EnumPushStatus.getByValue(orderForm.getPushStatus())).name());
                newDatas.add(orderForm);
            }
            List<String> projectTypeIds = newDatas.stream().map(DtoOrderForm::getProjectTypeId).distinct().collect(Collectors.toList());
            List<DtoProjectType> projectTypeList = projectTypeService.findAll(projectTypeIds);
            List<String> orderIds = newDatas.stream().map(DtoOrderForm::getId).collect(Collectors.toList());
            //获取操作日志记录
            List<DtoLog> logList = newLogService.getLogByOrderIds(orderIds);
            Map<String, String> lastNewOpinionMap = logList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getOpinion())).
                    sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).
                    collect(Collectors.groupingBy(DtoLog::getObjectId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getOpinion())));
            // 获取合同信息
            List<DtoOrderContract> contractList = orderContractRepository.findByOrderIdIn(orderIds);
            List<DtoOrderQuotation> orderQuotationList = orderQuotationRepository.findByOrderIdIn(orderIds);
            Map<String, List<DtoOrderContract>> contractMap = contractList.stream().collect(Collectors.groupingBy(DtoOrderContract::getOrderId));
            for (DtoOrderForm form : newDatas) {
                Optional<DtoProjectType> projectType = projectTypeList.stream().filter(p -> p.getId().equals(form.getProjectTypeId())).findFirst();
                projectType.ifPresent(dtoProjectType -> form.setProjectTypeName(dtoProjectType.getName()));
                form.setOpinion(lastNewOpinionMap.getOrDefault(form.getId(), ""));
                List<DtoOrderContract> contracts = contractMap.getOrDefault(form.getId(), new ArrayList<>());
                String grantStatusStr = contracts.stream().map(DtoOrderContract::getContractStatus).map(String::valueOf).distinct().collect(Collectors.joining(","));
                // 2:无合同
                form.setGrantStatusStr(StringUtil.isNotEmpty(grantStatusStr) ? grantStatusStr : "2");
                orderQuotationList.stream().filter(v->form.getId().equals(v.getOrderId())).findFirst().ifPresent(form::setOrderQuotation);
            }
        }
        pb.setData(newDatas);
    }

    /**
     * 保存订单信息
     *
     * @param orderForm 订单信息
     * @return 订单信息
     */
    @Transactional
    @Override
    public DtoOrderForm save(DtoOrderForm orderForm) {
        //新增订单
        if (StringUtil.isEmpty(orderForm.getOrderCode())) {
            String orderCode = this.createOrderCode(orderForm.getProjectTypeId(), orderForm.getOrderDate());
            orderForm.setOrderCode(orderCode);
        }
        orderForm.setPushStatus(EnumPRO.EnumPushStatus.未推送.getValue());
        orderForm.setOrderStatus(EnumPRO.EnumOrderStatus.登记中.getValue());
        DtoOrderForm newForm = super.save(orderForm);
        //创建工作流
        workflowService.createInstance(EnumPRO.EnumWorkflowCode.订单.getValue(), newForm.getId());
        //新增费用记录
        DtoOrderQuotation orderQuotation = new DtoOrderQuotation();
        orderQuotation.setOrderId(newForm.getId());
        //默认测试小计折扣率为100
        orderQuotation.setTestDiscount(new BigDecimal("100"));
        //默认总价折扣率为100
        orderQuotation.setTotalDiscount(new BigDecimal("100"));
        //默认税率为6
        orderQuotation.setTaxRate(new BigDecimal("6"));
        newForm.setOrderQuotation(orderQuotationRepository.save(orderQuotation));
        //保存日志
        logForOrderFormService.save(new DtoLogForOrderForm("新增订单", newForm.getId(), 1, 1,
                PrincipalContextUser.getPrincipal().getUserName() + "于" + DateUtil.dateToString(new Date(), DateUtil.YEAR) + "创建订单：" + newForm.getOrderCode(), "", ""));
        return newForm;
    }

    @Transactional
    @Override
    public String createOrderCode(String projectTypeId, Date inputTime) {
        return serialNumberService.createNewNumber(projectTypeId, inputTime);
    }

    /**
     * 通过订单id获取订单明细
     *
     * @param id 订单id
     * @return 订单明细
     */
    @Override
    public DtoOrderForm findOne(String id) {
        //订单信息
        DtoOrderForm newForm = super.findOne(id);
        //费用记录
        DtoOrderQuotation quotation = orderQuotationRepository.findByOrderId(id);
        // 赋值最终折扣率
        setFinalDiscount(quotation);
        newForm.setOrderQuotation(quotation);
        //行政区域
        DtoArea area = areaService.findById(newForm.getAreaId());
        newForm.setAreaName(StringUtil.isNotNull(area) ? area.getAreaName() : "");
        //费用明细
        //先更新检测数量
        List<DtoQuotationDetail> quotationDetailList = updateCount(id);
        if (quotationDetailList.size() == 0) {
            quotationDetailList = quotationDetailRepository.findByOrderId(id);
        }
        //点位名称
        List<String> sampleTypeIds = quotationDetailList.stream().map(DtoQuotationDetail::getSampleTypeId).distinct().collect(Collectors.toList());
        if (sampleTypeIds.size() > 0) {
            List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);
            quotationDetailList.forEach(p -> {
                if (StringUtil.isNotEmpty(p.getFolderName())) {
                    p.setFolderList(p.getFolderName().split(","));
                    p.setFolderCount(p.getFolderList().length);
                } else {
                    p.setFolderCount(0);
                }
                Optional<DtoSampleType> sampleType = sampleTypeList.stream().filter(q -> p.getSampleTypeId().equals(q.getId())).findFirst();
                sampleType.ifPresent(dtoSampleType -> p.setSampleTypeName(dtoSampleType.getTypeName()));
            });
        }
        //按照检测类型排序
        if (StringUtil.isNotEmpty(quotationDetailList)) {
            quotationDetailList.sort(Comparator.comparing(DtoQuotationDetail::getSampleTypeId).thenComparing(DtoQuotationDetail::getAnalyseMethodId).thenComparing(DtoQuotationDetail::getAnalyseItemId));
        }
        newForm.setQuotationDetailList(quotationDetailList);
        //其他费用
        List<DtoOtherDetail> otherDetailList = otherDetailRepository.findByOrderId(id);
        List<String> typeIds = otherDetailList.stream().map(DtoOtherDetail::getTypeId).collect(Collectors.toList());
        if (typeIds.size() > 0) {
            List<DtoFeeConfig> feeConfigList = feeConfigRepository.findAll(typeIds);
            otherDetailList.forEach(p -> {
                Optional<DtoFeeConfig> config = feeConfigList.stream().filter(q -> q.getId().equals(p.getTypeId())).findFirst();
                config.ifPresent(dtoFeeConfig -> p.setTypeName(dtoFeeConfig.getTypeName()));
            });
        }
        newForm.setOtherDetailList(otherDetailList);
        //项目信息
        List<DtoProject> projectList = projectRepository.findByOrderId(id);
        newForm.setProjectList(projectList);
        return newForm;
    }

    /**
     * 获取最终折扣率
     *
     * @param orderQuotation 订单费用实体
     */
    protected void setFinalDiscount(DtoOrderQuotation orderQuotation) {
        if(StringUtil.isNotNull(orderQuotation)) {
            String finalDiscount = "0.0";
            if (!(orderQuotation.getTotalPrice().compareTo(BigDecimal.ZERO) == 0) && !(orderQuotation.getFinalQuotation().compareTo(BigDecimal.ZERO) == 0)) {
                // 最终总价/参考总价*100,保留一位小数
                finalDiscount = orderQuotation.getFinalQuotation().divide(orderQuotation.getTotalPrice(), 10, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(1, BigDecimal.ROUND_HALF_UP).toPlainString();
            }
            orderQuotation.setFinalDiscount(finalDiscount);
        }
    }

    /**
     * 更新费用明细
     *
     * @param orderId 订单id
     * @param isOther 是否其他费用
     * @return 更新之后的费用明细
     */
    @Transactional
    @Override
    public DtoOrderQuotation updateQuotation(String orderId, Boolean isOther, Boolean isQuotation) {
        DtoOrderQuotation orderQuotation = orderQuotationRepository.findByOrderId(orderId);
        List<DtoOtherDetail> otherDetailList = otherDetailRepository.findByOrderId(orderId);
        //是否费用明细调整
        if (isQuotation) {
            List<DtoQuotationDetail> quotationDetailList = quotationDetailRepository.findByOrderId(orderId);
            //费用明细求和
            Optional<BigDecimal> decimalOptional = quotationDetailList.stream().map(DtoQuotationDetail::getQuotationPrice).reduce(BigDecimal::add);
            orderQuotation.setTestPrice(decimalOptional.orElse(BigDecimal.ZERO));
        }
        //折后检测费 = 检测费小计 * 检测费折扣 / 100
        orderQuotation.setDiscountPrice(orderQuotation.getTestPrice().multiply(orderQuotation.getTestDiscount()).divide(new BigDecimal("100")));
        //更新其他费用明细
        if (isQuotation) {
            for (DtoOtherDetail otherDetail : otherDetailList) {
                if (OtherDetailServiceImpl.formulaB.equals(otherDetail.getFormula())) {
                    otherDetail.setPrice(otherDetail.getStandard().multiply(orderQuotation.getDiscountPrice()).divide(new BigDecimal("100")));
                    otherDetail.setQuotedPrice(otherDetail.getPrice());
                }
            }
        }
        //是否其他费用调整
        if (isOther) {
            //其他费用求和
            Optional<BigDecimal> decimalOptional = otherDetailList.stream().map(DtoOtherDetail::getQuotedPrice).reduce(BigDecimal::add);
            orderQuotation.setOtherPrice(decimalOptional.orElse(BigDecimal.ZERO));
        }
        //税前总价=折后检测费+其他费用总计
        orderQuotation.setPreTax(orderQuotation.getDiscountPrice().add(orderQuotation.getOtherPrice()));
        //总价=税前总价*(1+税收费率)*总价折扣率
        orderQuotation.setTotalPrice(orderQuotation.getPreTax()
                .multiply((new BigDecimal(100).add(orderQuotation.getTaxRate()))).divide(new BigDecimal(100))
                .multiply(orderQuotation.getTotalDiscount()).divide(new BigDecimal(100)));
        //保存费用明细
        orderQuotationRepository.save(orderQuotation);
        otherDetailRepository.save(otherDetailList);
        //更新费用明细
        logForOrderFormService.save(new DtoLogForOrderForm("更新订单费用明细", orderId, 1, 1,
                PrincipalContextUser.getPrincipal().getUserName() + "于" + DateUtil.dateToString(new Date(), DateUtil.YEAR) + "更新费用明细", "", ""));
        // 赋值最终折扣率
        setFinalDiscount(orderQuotation);
        return orderQuotation;
    }


    /**
     * 更新检测数已检总检数
     */
    @Transactional
    @Override
    public List<DtoQuotationDetail> updateCount(String orderId) {
        //找到对应的所有项目
        List<DtoProject> projectList = projectRepository.findByOrderId(orderId);

        List<DtoQuotationDetail> quotationDetailList = new ArrayList<>();
        if (StringUtil.isEmpty(projectList)) {
            quotationDetailList = quotationDetailRepository.findByOrderId(orderId);
            quotationDetailList.forEach(p -> {
                //已检数
                p.setInspectedCount(0);
                //剩检数
                p.setResidueCount((p.getSampleOrder() - p.getInspectedCount()));
            });
            return quotationDetailList;
        }
        //找到所有的样品
        List<String> proIds = projectList.stream().map(DtoProject::getId).collect(Collectors.toList());
        List<DtoSample> sampleList = new ArrayList<>();
        if (proIds.size() > 0) {
            sampleList = sampleRepository.findByProjectIdIn(proIds);
        }
        //找到所有点位
        List<String> folderIds = sampleList.stream().map(DtoSample::getSampleFolderId).collect(Collectors.toList());
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findAll(folderIds);

        //找到所有的分析数据
        List<String> samIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        if (samIds.size() > 0) {
            analyseDataList = analyseDataRepository.findBySampleIdIn(samIds);
        }

        if (analyseDataList.size() > 0) {
            //测试项目的下达的个数
            Map<String, Integer> testCountList = analyseDataList.stream().collect(
                    Collectors.groupingBy(DtoAnalyseData::getTestId,
                            Collectors.collectingAndThen(Collectors.toList(), List::size)));
            //总称的测试项目另外算
            quotationDetailList = quotationDetailRepository.findByOrderId(orderId);
            List<String> totalTestIds = quotationDetailList.stream().filter(p -> p.getIsTotal()).map(DtoQuotationDetail::getTestId).collect(Collectors.toList());
            List<DtoTest> testList = new ArrayList<>();
            if (totalTestIds.size() > 0) {
                testList = testRepository.findByParentIdIn(totalTestIds);
            }
            List<DtoTest> finalTestList = testList;
            List<DtoSample> finalSampleList = sampleList;
            List<DtoAnalyseData> finalAnalyseDataList = analyseDataList;
            quotationDetailList.forEach(p -> {
                List<DtoTest> testOfSample;
                //获取订单详细的点位名称集合
                List<String> folderNames = new ArrayList<>();
                if (StringUtil.isNotEmpty(p.getFolderName())) {
                    folderNames = Arrays.stream(p.getFolderName().split(",")).collect(Collectors.toList());
                }
                List<String> finalFolderNames = folderNames;
                List<DtoSampleFolder> folderOfDetail = sampleFolders.stream()
                        .filter(n -> finalFolderNames.contains(n.getWatchSpot())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(folderOfDetail)) {
                    //获取到此订单详细的点位
                    List<String> folderIdsOfDetail = folderOfDetail.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
                    //根据点位名称获取样品
                    List<DtoSample> samples = finalSampleList.stream()
                            .filter(sample -> folderIdsOfDetail.contains(sample.getSampleFolderId()))
                            .collect(Collectors.toList());
                    //获取样品id
                    List<String> samplesIds = StringUtil.isNotEmpty(samples) ? samples.stream().map(DtoSample::getId).collect(Collectors.toList()) : new ArrayList<>();
                    //根据样品获取检测数据
                    List<DtoAnalyseData> anaDataOfSample = finalAnalyseDataList.stream()
                            .filter(anaData -> samplesIds.contains(anaData.getSampleId())).collect(Collectors.toList());
                    //获取测试项目分组集合
                    List<String> testIdsOfSample = StringUtil.isNotEmpty(anaDataOfSample) ? anaDataOfSample.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList()) : new ArrayList<>();
                    testOfSample = finalTestList.stream().filter(test -> testIdsOfSample.contains(test.getId())).collect(Collectors.toList());

                    Map<String, Integer> testCountOfSample = anaDataOfSample.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.collectingAndThen(Collectors.toList(), List::size)));
                    //判断是否是总称的测试项目
                    if (p.getIsTotal()) {
                        List<DtoTest> parentList = testOfSample.stream().filter(q -> q.getParentId().equals(p.getTestId())).collect(Collectors.toList());
                        if (parentList.size() > 0) {
                            //子项测试项目ids
                            List<String> pIds = parentList.stream().map(DtoTest::getId).collect(Collectors.toList());
                            List<Integer> maxCount = new ArrayList<>();
                            testCountOfSample.forEach((t, v) -> {
                                if (pIds.contains(t)) {
                                    maxCount.add(v);
                                }
                            });
                            //已检数
                            if (maxCount.size() > 0) {
                                p.setInspectedCount(Collections.max(maxCount));
                            }
                        }
                    } else {
                        if (!testCountOfSample.containsKey(p.getTestId())) {
                            p.setInspectedCount(0);
                        }
                        //已检数
                        if (StringUtil.isNotNull(testCountOfSample.get(p.getTestId()))) {
                            p.setInspectedCount(testCountOfSample.get(p.getTestId()));
                        }
                    }
                } else {
                    p.setInspectedCount(0);
                }
                //剩检数
                p.setResidueCount((p.getSampleOrder() - p.getInspectedCount()));
            });
            quotationDetailRepository.save(quotationDetailList);
        }
        //更新费用明细
        logForOrderFormService.save(new DtoLogForOrderForm("更新订单检测数已检总检数", orderId, 1, 1,
                PrincipalContextUser.getPrincipal().getUserName() + "于" + DateUtil.dateToString(new Date(), DateUtil.YEAR) + "更新订单检测数已检总检数", "", ""));
        return quotationDetailList;
    }

    /**
     * 订单提交
     *
     * @param orderSubmit 提交信息
     */
    @Transactional
    @Override
    public void submit(DtoOrderSubmit orderSubmit) {
        log.warn("=================================开始订单提交================================================");
        DtoOrderForm orderForm = super.findOne(orderSubmit.getOrderId());
        //保存流程类型
        orderForm.setFlowType(orderSubmit.getFlowType());
        //一审人员
        orderForm.setFirstPersonId(orderSubmit.getAuditPersonId());
        //二审人员
        orderForm.setSecondPersonId(orderSubmit.getReviewPersonId());
        //三审人员
        orderForm.setThreePersonId(orderSubmit.getAffirmPersonId());
        log.warn("=================================订单提交：提交订单工作流之前（未判断设置订单状态），订单状态：" + orderForm.getOrderStatus() + "============================================");
        //提交的时候判断是否需要审核
        if (orderSubmit.getIsAudit()) {
            orderForm.setOrderStatus(EnumPRO.EnumOrderStatus.一审中.getValue());
        } else {
            //状态变更
            orderForm.setOrderStatus(EnumPRO.EnumOrderStatus.审核通过.getValue());
        }
        String comment = getSubmitComment(orderForm, orderSubmit);
        log.warn("=================================订单提交：提交订单工作流之前（设置完订单状态），订单状态：" + orderForm.getOrderStatus() + "============================================");
        orderForm.setOrderStatus(orderWorkFlow(orderSubmit, orderForm, EnumPRO.EnumLogOperateType.提交订单.name(), comment));
        log.warn("=================================订单提交：提交订单工作流之后，订单状态：" + orderForm.getOrderStatus() + "============================================");
        //订单提交
        logForOrderFormService.save(new DtoLogForOrderForm("订单提交", orderSubmit.getOrderId(), 1, 1,
                PrincipalContextUser.getPrincipal().getUserName() + "于" + DateUtil.dateToString(new Date(), DateUtil.YEAR) + comment, "", ""));
        super.save(orderForm);
    }

    /**
     * 审核订单
     *
     * @param orderSubmit 提交信息
     */
    @Transactional
    @Override
    public void auditOrder(DtoOrderSubmit orderSubmit) {
        log.warn("=================================开始订单审核================================================");
        DtoOrderForm orderForm = super.findOne(orderSubmit.getOrderId());
        CurrentPrincipalUser principalContextUser = PrincipalContextUser.getPrincipal();
        String status = "订单审核通过";
        //判断是否审核通过
        if (!orderSubmit.getIsPass()) {
            status = "订单审核不通过";
        }
        String comment = String.format("%s审核了订单%s,%s,意见是：%s。", principalContextUser.getUserName(), orderForm.getOrderCode(), status, orderSubmit.getOpinion());
        //提交工作流
        log.warn("=================================订单审核：提交订单工作流之前，订单状态：" + orderForm.getOrderStatus() + "============================================");
        orderForm.setOrderStatus(orderWorkFlow(orderSubmit, orderForm, EnumPRO.EnumLogOperateType.审核订单.name(), comment));
        log.warn("=================================订单审核：提交订单工作流之后，订单状态：" + orderForm.getOrderStatus() + "============================================");
        logForOrderFormService.save(new DtoLogForOrderForm("订单审核", orderSubmit.getOrderId(), 1, 1,
                comment, "", ""));
        super.save(orderForm);
    }

    /**
     * 赋值订单基本数据
     *
     * @param orderIds 订单数据
     */
    @Override
    @Transactional
    public void copyOrderForm(List<String> orderIds) {
        //新订单数据
        List<DtoOrderForm> orderFormInsert = new ArrayList<>();
        //新订单折扣信息
        List<DtoOrderQuotation> quotationInsert = new ArrayList<>();
        //新订单检测数据
        List<DtoQuotationDetail> quotationDetailsInsert = new ArrayList<>();
        //新检测关联测试项目数据
        List<DtoQuotationDetail2Test> quotationDetailToTestsInsert = new ArrayList<>();
        //新订单其他费用
        List<DtoOtherDetail> otherDetailsInsert = new ArrayList<>();
        //循环为复制的数据赋值
        for (String orderId : orderIds) {
            DtoOrderForm orderForm = repository.findOne(orderId);
            if (StringUtil.isNotNull(orderForm)) {
                //新订单数据
                DtoOrderForm orderFormNew = new DtoOrderForm();
                //新订单折扣信息
                DtoOrderQuotation quotationNew = new DtoOrderQuotation();
                //新订单检测数据
                List<DtoQuotationDetail> quotationDetailsNew = new ArrayList<>();
                //新检测关联测试项目数据
                List<DtoQuotationDetail2Test> quotationDetailToTestsNew = new ArrayList<>();
                //新订单其他费用
                List<DtoOtherDetail> otherDetailsNew = new ArrayList<>();

                BeanUtils.copyProperties(orderForm, orderFormNew,
                        "id", "orderCode", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate");
                orderFormNew.setOrderDate(new Date());
                orderFormNew.setOrderStatus(EnumPRO.EnumOrderStatus.登记中.getValue());
                if (StringUtil.isEmpty(orderFormNew.getOrderCode())) {
                    String orderCode = this.createOrderCode(orderFormNew.getProjectTypeId(), orderFormNew.getOrderDate());
                    orderFormNew.setOrderCode(orderCode);
                    orderFormNew.setOrderName(orderForm.getOrderName() + "复制");
                }
                //复制后订单登记人员为当前用户
                orderFormNew.setRegistrantId(PrincipalContextUser.getPrincipal().getUserId());
                orderFormNew.setRegistrantName(PrincipalContextUser.getPrincipal().getUserName());
                //获取订单折扣信息
                DtoOrderQuotation quotations = orderQuotationRepository.findByOrderId(orderForm.getId());
                //获取订单检测数据
                List<DtoQuotationDetail> quotationDetails = quotationDetailRepository.findByOrderId(orderForm.getId());
                List<String> quotationIds = quotationDetails.stream().map(DtoQuotationDetail::getId).collect(Collectors.toList());
                List<DtoQuotationDetail2Test> quotationDetail2TestList = quotationDetail2TestRepository.findByDetailIdIn(quotationIds);
                Map<String, List<DtoQuotationDetail2Test>> quotation2TestMap = quotationDetail2TestList.stream().collect(Collectors.groupingBy(DtoQuotationDetail2Test::getDetailId));
                //获取订单其他费用数据
                List<DtoOtherDetail> otherDetails = otherDetailRepository.findByOrderId(orderForm.getId());

                if (StringUtil.isNotNull(quotations)) {
                    BeanUtils.copyProperties(quotations, quotationNew,
                            "id", "orderId", "orgId", "orderCode", "creator", "createDate", "domainId",
                            "modifier", "modifyDate");
                    quotationNew.setOrderId(orderFormNew.getId());
                }
                //赋值订单检测数据
                if (StringUtil.isNotEmpty(quotationDetails)) {
                    for (DtoQuotationDetail quotationDetail : quotationDetails) {
                        DtoQuotationDetail quotationDetailNew = new DtoQuotationDetail();
                        //设置关联检测项目数据
                        BeanUtils.copyProperties(quotationDetail, quotationDetailNew,
                                "id", "orderId", "orgId", "orderCode", "creator", "createDate", "domainId",
                                "modifier", "modifyDate");
                        quotationDetailNew.setOrderId(orderFormNew.getId());
                        quotationDetailNew.setQuotationId(quotationNew.getId());
                        quotationDetailsNew.add(quotationDetailNew);
                        //设置关联测试项目数据
                        List<DtoQuotationDetail2Test> quotationDetail2Tests = quotation2TestMap.get(quotationDetail.getId());
                        if (StringUtil.isNotEmpty(quotationDetail2Tests)) {
                            quotationDetail2Tests.forEach(p -> {
                                DtoQuotationDetail2Test quotationDetail2Test = new DtoQuotationDetail2Test();
                                quotationDetail2Test.setTestId(p.getTestId());
                                quotationDetail2Test.setDetailId(quotationDetailNew.getId());
                                quotationDetailToTestsNew.add(quotationDetail2Test);
                            });
                        }
                    }
                }
                if (StringUtil.isNotEmpty(otherDetails)) {
                    for (DtoOtherDetail otherDetail : otherDetails) {
                        DtoOtherDetail otherDetailNew = new DtoOtherDetail();
                        BeanUtils.copyProperties(otherDetail, otherDetailNew,
                                "id", "orderId", "orgId", "orderCode", "creator", "createDate", "domainId",
                                "modifier", "modifyDate");
                        otherDetailNew.setQuotationId(quotationNew.getId());
                        otherDetailNew.setOrderId(orderFormNew.getId());
                        otherDetailsNew.add(otherDetailNew);
                    }
                }
                //添加需要复制的数据
                orderFormInsert.add(orderFormNew);
                quotationInsert.add(quotationNew);
                quotationDetailsInsert.addAll(quotationDetailsNew);
                quotationDetailToTestsInsert.addAll(quotationDetailToTestsNew);
                otherDetailsInsert.addAll(otherDetailsNew);
            }
        }
        //处理订单基础数据
        orderFormInsert = this.handleOrderForm(orderFormInsert);
        //保存数据
        if (StringUtil.isNotEmpty(orderFormInsert)) {
            repository.save(orderFormInsert);
            orderFormInsert.forEach(p -> {
                //创建工作流
                workflowService.createInstance(EnumPRO.EnumWorkflowCode.订单.getValue(), p.getId());
            });
            if (StringUtil.isNotEmpty(quotationInsert)) {
                orderQuotationRepository.save(quotationInsert);
            }
            if (StringUtil.isNotEmpty(quotationDetailsInsert)) {
                quotationDetailRepository.save(quotationDetailsInsert);
                if (StringUtil.isNotEmpty(quotationDetailToTestsInsert)) {
                    quotationDetail2TestRepository.save(quotationDetailToTestsInsert);
                }
            }
            if (StringUtil.isNotEmpty(otherDetailsInsert)) {
                otherDetailRepository.save(otherDetailsInsert);
            }
        }
    }

    @Override
    @Transactional
    public void signingOrder(DtoSignOrder signOrder) {
        String orderId = signOrder.getOrderId();
        if (StringUtil.isNotEmpty(orderId)) {
            DtoOrderForm orderForm = repository.findOne(orderId);
            if (StringUtil.isNotNull(orderForm)) {
                String signDataStr = signOrder.getSignDate();
                Integer signStatus = signOrder.getGrantStatus();
                Integer finalQuotation = signOrder.getFinalQuotation();
                DtoOrderQuotation orderQuotation = orderQuotationRepository.findByOrderId(orderForm.getId());
                Date date = new Date();
                if (StringUtil.isNotEmpty(signDataStr)) {
                    date = DateUtil.stringToDate(signDataStr, DateUtil.YEAR);
                }
                orderForm.setSignDate(date);
                orderForm.setGrantStatus(signStatus);
                BigDecimal finalPrice = new BigDecimal(finalQuotation);
                orderQuotation.setFinalQuotation(finalPrice);
                repository.save(orderForm);
                orderQuotationRepository.save(orderQuotation);
            }
        }
        logForOrderFormService.save(new DtoLogForOrderForm("订单签订", orderId, 1, 1,
                PrincipalContextUser.getPrincipal().getUserName() + "于" + DateUtil.dateToString(new Date(), DateUtil.YEAR) + "签订订单", "", ""));
    }

    @Override
    public DtoOrderForm findAttachPath(String id) {
        return repository.findOne(id);
    }

    /**
     * 处理订单基本数据
     * ‘
     *
     * @param orderForms 订单基础数据
     * @return 订单数据
     */
    protected List<DtoOrderForm> handleOrderForm(List<DtoOrderForm> orderForms) {
        return orderForms;
    }

    /**
     * 订单流程
     *
     * @param orderSubmit 订单提交信息
     * @param orderForm   订单
     * @param type        提交类型
     * @param message     操作记录
     */
    private Integer orderWorkFlow(DtoOrderSubmit orderSubmit, DtoOrderForm orderForm, String type, String message) {
        //工作流处理
        String status = "";
        try {
            log.warn("===================================订单工作流提交=====================================");
            log.warn("===================================工作流提交信号值：" + orderSubmit.getSign() + "=====================================");
            //工作流提交
            DtoWorkflowSign dtoWorkflowSign = new DtoWorkflowSign();
            dtoWorkflowSign.setObjectId(orderSubmit.getOrderId());
            dtoWorkflowSign.setSignal(orderSubmit.getSign());
            dtoWorkflowSign.setOption(orderSubmit.getOpinion());
            status = workflowService.submitSign(dtoWorkflowSign);
            log.warn("===================================工作流提交后获得的状态：" + status + "========================================");
            //记录操作日志
            DtoLog workSheetLog = new DtoLog();
            workSheetLog.setComment(message);
            workSheetLog.setOpinion(orderSubmit.getOpinion());
            workSheetLog.setLogType(EnumPRO.EnumLogType.订单流程.getValue());
            workSheetLog.setObjectId(orderForm.getId());
            workSheetLog.setObjectType(EnumPRO.EnumLogObjectType.订单.getValue());
            workSheetLog.setOperateInfo(type);
            newLogService.createLog(workSheetLog);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
        log.warn("======================================订单工作流提交结束=======================================");
        return Objects.requireNonNull(EnumPRO.EnumOrderStatus.getByName(status)).getValue();
    }

    /**
     * 获取提交订单时的日志说明
     *
     * @param orderSubmit 数据载体
     */
    private String getSubmitComment(DtoOrderForm orderForm, DtoOrderSubmit orderSubmit) {
        String comment = String.format("提交了订单%s", orderForm.getOrderCode());
        List<String> personIds = Arrays.asList(orderSubmit.getAuditPersonId(), orderSubmit.getReviewPersonId(), orderSubmit.getAffirmPersonId()).stream().filter(p -> StringUtils.isNotNullAndEmpty(p) && !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personRepository.findAll(personIds) : new ArrayList<>();
        Map<String, DtoPerson> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, dto -> dto));
        if (StringUtils.isNotNullAndEmpty(orderSubmit.getAuditPersonId()) && !UUIDHelper.GUID_EMPTY.equals(orderSubmit.getAuditPersonId())) {
            DtoPerson person = personMap.get(orderSubmit.getAuditPersonId());
            if (StringUtil.isNotNull(person)) {
                comment += "，一审人员：" + person.getCName();
            }
        }
        if (StringUtils.isNotNullAndEmpty(orderSubmit.getReviewPersonId()) && !UUIDHelper.GUID_EMPTY.equals(orderSubmit.getReviewPersonId())) {
            DtoPerson person = personMap.get(orderSubmit.getReviewPersonId());
            if (StringUtil.isNotNull(person)) {
                comment += "，二审人员：" + person.getCName();
            }
        }
        if (StringUtils.isNotNullAndEmpty(orderSubmit.getAffirmPersonId()) && !UUIDHelper.GUID_EMPTY.equals(orderSubmit.getAffirmPersonId())) {
            DtoPerson person = personMap.get(orderSubmit.getAffirmPersonId());
            if (StringUtil.isNotNull(person)) {
                comment += "，三审人员：" + person.getCName();
            }
        }
        if (StringUtils.isNotNullAndEmpty(orderSubmit.getOpinion())) {
            comment += "，意见：" + orderSubmit.getOpinion();
        }
        comment += "。";
        return comment;
    }
}