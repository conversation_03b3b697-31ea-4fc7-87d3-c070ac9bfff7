package com.sinoyd.lims.api.dto.customer;

import lombok.Data;

/**
 * 采样一览表实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
@Data
public class DtoSampleSchedule {

    /**
     * 送样单id
     */
    private String id;
    /**
     * 送样单号
     */
    private String recordCode;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 项目编号
     */
    private String projectCode;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 检测类型
     */
    private String sampleType;
    /**
     * 样品数量
     */
    private Integer sampleNum;
    /**
     * 检测项目
     */
    private String test;
    /**
     * 送养人
     */
    private String senderName;
    /**
     * 交接状态
     */
    private String innerStatus;
    /**
     * 分配状态
     */
    private String receiveSubstatus;

    public DtoSampleSchedule() {
    }

    public DtoSampleSchedule(String id, String recordCode, String projectId, String sampleType, String senderName) {
        this.id = id;
        this.recordCode = recordCode;
        this.projectId = projectId;
        this.sampleType = sampleType;
        this.senderName = senderName;
    }
}
