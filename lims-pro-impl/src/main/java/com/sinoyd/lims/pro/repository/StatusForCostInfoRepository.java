package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoStatusForCostInfo;

import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;


/**
 * 费用状态数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
public interface StatusForCostInfoRepository extends IBaseJpaPhysicalDeleteRepository<DtoStatusForCostInfo, String> {
     /**
     * 查询指定费用的状态信息
     *
     * @param costInfoId 费用id
     * @return 对应费用下的状态信息
     */
    List<DtoStatusForCostInfo> findByCostInfoId (String costInfoId);
}