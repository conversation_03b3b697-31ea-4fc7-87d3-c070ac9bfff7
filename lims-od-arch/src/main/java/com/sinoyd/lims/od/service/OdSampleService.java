package com.sinoyd.lims.od.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;
import com.sinoyd.lims.od.dto.DtoOdSample;

import java.util.List;

/**
 * 嗅辨样品操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
public interface OdSampleService extends IBaseJpaService<DtoOdSample, String> {

    /**
     * 根据任务id查询样品
     *
     * @param taskIds 任务id
     * @return 杨平集合
     */
    List<DtoOdSample> findByTaskIdIn(List<String> taskIds);

    /**
     * 完成样品
     *
     * @param id 主键id
     */
    DtoOdSample completeOdSample(String id);
}
