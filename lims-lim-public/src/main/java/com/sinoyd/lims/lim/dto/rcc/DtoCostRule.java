package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.CostRule;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoCostRule实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_CostRule")
 @Data
 @DynamicInsert
 public  class DtoCostRule extends CostRule {
   private static final long serialVersionUID = 1L;
 }