package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.lims.lim.entity.AnalyzeItemSort;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.List;


/**
 * DtoAnalyzeItemSort实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_AnalyzeItemSort")
 @Data
 @DynamicInsert
 public  class DtoAnalyzeItemSort extends AnalyzeItemSort {

 @Transient
 private List<DtoAnalyzeItem> analyzeItemList;
 }