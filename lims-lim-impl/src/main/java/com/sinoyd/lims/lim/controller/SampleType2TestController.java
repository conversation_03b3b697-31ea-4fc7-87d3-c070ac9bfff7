package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.criteria.SampleTypeCriteria;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleType2Test;
import com.sinoyd.lims.lim.dto.customer.DtoSampleTypeTemplate;
import com.sinoyd.lims.lim.service.SampleType2TestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 样品分组配置
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "样品模板2测试项目: 样品模板2测试项目管理服务")
@RestController
@RequestMapping("/api/lim/sampleType2Test")
@Validated
public class SampleType2TestController extends BaseJpaController<DtoSampleType2Test, String, SampleType2TestService> {


    /**
     * 样品模板获取
     *
     * @param id 样品模板获取id
     * @return 样品模板
     */
    @ApiOperation(value = "按主键获取样品模板", notes = "按主键获取样品模板")
    @GetMapping("/getSampleType/{id}")
    public RestResponse<DtoSampleTypeTemplate> getSampleTypeById(@PathVariable(name = "id") String id) {
        RestResponse<DtoSampleTypeTemplate> restResponse = new RestResponse<DtoSampleTypeTemplate>();
        DtoSampleTypeTemplate sampleTypeTemplate = service.getSampleTypeById(id);
        restResponse.setData(sampleTypeTemplate);
        restResponse.setRestStatus(StringUtil.isNull(sampleTypeTemplate) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResponse;
    }

    /**
     * 新增检测模板
     *
     * @param sampleTypeTemplate 检测模板
     * @return 新增的检测模板
     */
    @PostMapping("/createTemplate")
    public RestResponse<DtoSampleTypeTemplate> createTemp(@Validated @RequestBody DtoSampleTypeTemplate sampleTypeTemplate) {
        RestResponse<DtoSampleTypeTemplate> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoSampleTypeTemplate data = service.createTemplate(sampleTypeTemplate);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改检测模板
     *
     * @param sampleTypeTemplate 检测模板
     * @return 修改的检测模板
     */
    @PostMapping("/updateTemplate")
    public RestResponse<DtoSampleTypeTemplate> updateTemplate(@Validated @RequestBody DtoSampleTypeTemplate sampleTypeTemplate) {
        RestResponse<DtoSampleTypeTemplate> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoSampleTypeTemplate data = service.updateTemplate(sampleTypeTemplate);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    @DeleteMapping("/delete")
    public RestResponse<Void> deleteTest(@RequestBody DtoSampleTypeTemplate sampleTypeTemplate) {
        RestResponse<Void> response = new RestResponse<>();
        service.deleteTest(sampleTypeTemplate.getId(), sampleTypeTemplate.getTestIds());
        return response;
    }


    /**
     * 复制检测模板
     *
     * @return 检测类型
     */
    @ApiOperation(value = "复制检测模板", notes = "复制检测模板")
    @PostMapping("/copy/{id}")
    public RestResponse<DtoSampleType> findByCategory(@PathVariable(name = "id") String id) {
        RestResponse<DtoSampleType> restResponse = new RestResponse<>();
        restResponse.setData(service.copyTemplate(id));
        return restResponse;
    }

    /**
     * 修改样品模板测试项目频次及样品数
     *
     * @param testList 模板测试项目
     */
    @PostMapping("/updateTestMsg")
    public RestResponse<Boolean> updateTestMsg(@RequestBody List<DtoSampleType2Test> testList) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.updateTestMsg(testList);
        restResp.setData(Boolean.TRUE);
        return restResp;
    }

    /**
     * 分页列表,由于base模块查不到test信息，所以拓展出一个新的接口
     */
    @ApiOperation(value = "分页获取检测类型", notes = "分页获取检测类型")
    @GetMapping("/findSampleTypeByPage")
    public RestResponse<List<DtoSampleType>> findByPage(SampleTypeCriteria sampleTypeCriteria) {
        RestResponse<List<DtoSampleType>> restResponse = new RestResponse<>();
        PageBean<DtoSampleType> page = super.getPageBean();
        service.findSampleTypeByPage(page, sampleTypeCriteria);

        restResponse.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(page.getRowsCount());
        restResponse.setData(page.getData());

        return restResponse;

    }

    /**
     * 修改样品模板测试项目频次及样品数 取测试项目上的配置
     *
     * @param sampleTypeId 模板标识
     */
    @PutMapping("/updateFrequencyByConfig/{sampleTypeId}")
    public RestResponse<DtoSampleTypeTemplate> updateFrequencyByConfig(@PathVariable String sampleTypeId) {
        RestResponse<DtoSampleTypeTemplate> restResp = new RestResponse<>();
        restResp.setData(service.updateFrequencyByConfig(sampleTypeId));
        return restResp;
    }

}