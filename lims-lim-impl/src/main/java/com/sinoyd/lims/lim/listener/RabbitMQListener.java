package com.sinoyd.lims.lim.listener;

import com.jsoniter.output.JsonStream;
import com.rabbitmq.client.Channel;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0.0 2024/10/23
 * @since V100R001
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class RabbitMQListener {

    private RedisTemplate redisTemplate;


    /**
     * Mq数据监听（监听设备上下限报文）
     *
     * @param message 报文解析字符串
     * @param channel 通道
     */
    @RabbitListener(queues = "dschannel-lims-onoff")
    public void listenerMessage(String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            message = asciiToJsonString(message);
            try {
                Map map = JsonUtil.toObject(message, Map.class);
                // 根据消息报文处理仪器在线情况
                if (StringUtil.isNotNull(map) && map.containsKey("station")) {
                    String onlineStatusKey = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_InstrumentGatherOnlineStatus.getValue());
                    Object onlineStatusObj = redisTemplate.opsForValue().get(onlineStatusKey);
                    Map onlineStatusMap = StringUtil.isNotNull(onlineStatusObj) ?
                            JsonUtil.toObject(onlineStatusObj.toString(), Map.class) : new HashMap();
                    Map<String, Object> stringMap = (Map<String, Object>) map.get("station");
                    String mn = stringMap.get("mn").toString();
                    String type = map.get("type").toString();
                    if (onlineStatusMap.containsKey(mn) && StringUtil.isNotEmpty(type) && StringUtil.isNotEmpty(mn)) {
                        log.info("仪器上下线报文 " + message);
                        // 根据仪器上班状态写入redis
                        onlineStatusMap.put(mn, type.equals("OFF") ? "0" : "1");
                        redisTemplate.opsForValue().set(onlineStatusKey, JsonStream.serialize(onlineStatusMap), 5, TimeUnit.MINUTES);
                    }
                }
            } catch (Exception e) {

            }
            // 确认已经成功处理的消息，确保 RabbitMQ 从队列中将其移除，以避免重复消费
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            log.info("接收消息失败,重新放回队列");
            try {
                // 否认消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ie) {
                ie.printStackTrace();
            }
        }
    }

    /**
     * ascii转json字符串
     *
     * @param asciiStr ascii字符串
     * @return json字符串
     * @throws UnsupportedEncodingException
     */
    private String asciiToJsonString(String asciiStr) throws UnsupportedEncodingException {
        String[] asciiValues = asciiStr.split(",");
        byte[] bytes = new byte[asciiValues.length];
        for (int i = 0; i < asciiValues.length; i++) {
            bytes[i] = (byte) Integer.parseInt(asciiValues[i]);
        }
        String utf8String = new String(bytes, "UTF-8");
        return utf8String;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
