package com.sinoyd.lims.lim.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;


@Data
public class PersonCertVO implements Serializable {

    @Excel(name = "证书名称",orderNum = "470",width = 26)
    private String certName;

    @Excel(name = "证书编号",orderNum = "480",width = 26)
    private String certCode;

    @Excel(name = "发证日期",orderNum = "490",width = 26)
    private String issueCertTime;

    @Excel(name = "有效期至",orderNum = "500",width = 26)
    private String certEffectiveTime;

    @Excel(name = "证书类型",orderNum = "510",width = 26)
    private String certType;

    public PersonCertVO() {
        this.certName = "";
        this.certCode = "";
        this.issueCertTime = "";
        this.certEffectiveTime = "";
        this.certType = "";
    }
}
