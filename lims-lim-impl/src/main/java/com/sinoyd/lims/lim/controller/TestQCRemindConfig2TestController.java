package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.TestQCRemindConfig2TestCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig2Test;
import com.sinoyd.lims.lim.service.TestQCRemindConfig2TestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * TestQCRemindConfig2Test服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
@Api(tags = "示例: TestQCRemindConfig2Test服务")
@RestController
@RequestMapping("api/lim/testQCRemindConfig2Test")
@Validated
public class TestQCRemindConfig2TestController extends BaseJpaController<DtoTestQCRemindConfig2Test, String, TestQCRemindConfig2TestService> {


    /**
     * 分页动态条件查询TestQCRemindConfig2Test
     *
     * @param testQCRemindConfig2TestCriteria 条件参数
     * @return RestResponse<List       <       TestQCRemindConfig2Test>>
     */
    @ApiOperation(value = "分页动态条件查询TestQCRemindConfig2Test", notes = "分页动态条件查询TestQCRemindConfig2Test")
    @GetMapping
    public RestResponse<List<DtoTestQCRemindConfig2Test>> findByPage(TestQCRemindConfig2TestCriteria testQCRemindConfig2TestCriteria) {
        PageBean<DtoTestQCRemindConfig2Test> pageBean = super.getPageBean();
        RestResponse<List<DtoTestQCRemindConfig2Test>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, testQCRemindConfig2TestCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询TestQCRemindConfig2Test
     *
     * @param id 主键id
     * @return RestResponse<DtoTestQCRemindConfig2Test>
     */
    @ApiOperation(value = "按主键查询TestQCRemindConfig2Test", notes = "按主键查询TestQCRemindConfig2Test")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoTestQCRemindConfig2Test> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoTestQCRemindConfig2Test> restResponse = new RestResponse<>();
        DtoTestQCRemindConfig2Test testQCRemindConfig2Test = service.findOne(id);
        restResponse.setData(testQCRemindConfig2Test);
        restResponse.setRestStatus(StringUtil.isNull(testQCRemindConfig2Test) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增TestQCRemindConfig2Test
     *
     * @param List<DtoTestQCRemindConfig2Test> 实体列表
     * @return RestResponse<DtoTestQCRemindConfig2Test>
     */
    @ApiOperation(value = "新增TestQCRemindConfig2Test", notes = "新增TestQCRemindConfig2Test")
    @PostMapping("/mutiConfigs")
    public RestResponse<String> mutiConfigs(@Validated @RequestBody List<DtoTestQCRemindConfig2Test> list) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.save(list);
        restResponse.setCount(list.size());
        return restResponse;
    }


    /**
     * "根据id批量删除TestQCRemindConfig2Test
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除TestQCRemindConfig2Test", notes = "根据id批量删除TestQCRemindConfig2Test")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
}