package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportTestFormulaParams;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 测试项目公式导入(修改）参数拓展配置sheet数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/8
 * @since V100R001
 */
@Data
public class TestFormulaParamsVerifyHandler implements IExcelVerifyHandler<DtoImportTestFormulaParams> {
    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    /**
     *  重复数据校验容器
     */
    private List<DtoImportTestFormulaParams> duplicationCheckList;

    /**
     *  参数公式校验容器，存放所有参数公式
     */
    private List<DtoParamsFormula> allParamsFormulaList;

    private List<DtoDimension> allDimensionList;

    private List<DtoParams> allParamsList;

    /**
     * 数据校验
     *
     * @param importTestFormulaParams 导入的实体
     * @return 导入结果
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportTestFormulaParams importTestFormulaParams) {
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(importTestFormulaParams)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //数据去除前后空格
            importUtils.strToTrim(importTestFormulaParams);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }

        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + importTestFormulaParams.getRowNum() + "行数据校验有误");

        // 必填校验
        importUtils.checkIsNull(result, importTestFormulaParams.getId(), "公式id", failStr);
        importUtils.checkIsNull(result, importTestFormulaParams.getSampleType(), "检测类型", failStr);
        importUtils.checkIsNull(result, importTestFormulaParams.getAnalyzeItem(), "分析项目", failStr);
        importUtils.checkIsNull(result, importTestFormulaParams.getAnalyzeMethod(), "分析方法", failStr);
        importUtils.checkIsNull(result, importTestFormulaParams.getSampleTypeForFormula(), "检测类型（公式）", failStr);
        importUtils.checkIsNull(result, importTestFormulaParams.getFormula(), "公式", failStr);
        importUtils.checkIsNull(result, importTestFormulaParams.getParamName(), "参数名称", failStr);

        // 数据格式校验
        importUtils.checkNumTwo(result, importTestFormulaParams.getMostDecimal(), "小数位数", failStr);
        importUtils.checkNumTwo(result, importTestFormulaParams.getMostSignificance(), "有效位数", failStr);

        // 业务数据校验
        //excel重复数据判断 根据公式id，参数名称，进行唯一性校验
        isRepeatData(result, failStr, importTestFormulaParams, duplicationCheckList);
        //公式是否存在
        isExistFormula(result, failStr, importTestFormulaParams);
        //量纲是否存在
        isExistDimension(result, failStr, importTestFormulaParams);
        //原始参数是否存在
        isExistOriginParasms(result, failStr, importTestFormulaParams);

        //更新重复校验容器
        duplicationCheckList.add(importTestFormulaParams);

        //构建异常信息
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }



    /**
     * excel重复数据判断 根据公式id，参数名称，进行唯一性校验
     * @param result  校验结果
     * @param failStr 校验错误数据
     * @param importTestFormulaParams 导入数据
     * @param tempList 已校验数据
     */
    private void isRepeatData(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTestFormulaParams importTestFormulaParams, List<DtoImportTestFormulaParams> tempList) {
        if (StringUtil.isNotEmpty(importTestFormulaParams.getId()) && StringUtil.isNotEmpty(importTestFormulaParams.getParamName())) {
            tempList.removeIf(p -> StringUtil.isEmpty(p.getId()) || StringUtil.isEmpty(p.getParamName()));
            List<Integer> isRepeatRowNum = tempList.stream()
                    .filter(p -> importTestFormulaParams.getId().equals(p.getId()) && importTestFormulaParams.getParamName().equals(p.getParamName()))
                    .map(DtoImportTestFormulaParams::getRowNum)
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isRepeatRowNum)) {
                result.setSuccess(false);
                failStr.append("；").append("参数拓展配置与第").append(isRepeatRowNum).append("条重复");
            }
        }
    }

    /**
     * 校验公式是否存在
     * @param result 校验结果
     * @param failStr 校验错误数据
     * @param importTestFormulaParams 导入数据
     */
    private void isExistFormula(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTestFormulaParams importTestFormulaParams) {
        DtoParamsFormula paramsFormula = allParamsFormulaList.stream().filter(p->p.getId().equals(importTestFormulaParams.getId())).findFirst().orElse(null);
        if(StringUtil.isNull(paramsFormula)){
            result.setSuccess(false);
            failStr.append("；").append("未查询到公式");
        }
    }

    /**
     * 校验量纲是否存在
     * @param result 校验结果
     * @param failStr 校验错误数据
     * @param importTestFormulaParams 导入数据
     */
    private void isExistDimension(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTestFormulaParams importTestFormulaParams) {
        if(StringUtil.isNotEmpty(importTestFormulaParams.getDimension())){
            DtoDimension dto = allDimensionList.stream().filter(d->importTestFormulaParams.getDimension().equals(d.getDimensionName())).findFirst().orElse(null);
            if(dto==null){
                result.setSuccess(false);
                failStr.append("；").append("未查询到量纲");
            }
        }
    }

    /**
     * 校验量纲是否存在
     * @param result 校验结果
     * @param failStr 校验错误数据
     * @param importTestFormulaParams 导入数据
     */
    private void isExistOriginParasms(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportTestFormulaParams importTestFormulaParams) {
        if(StringUtil.isNotEmpty(importTestFormulaParams.getParamName())){
            String paramsStr = importTestFormulaParams.getParamName().replace("[","").replace("]","");
            DtoParams dtoParams  = allParamsList.stream().filter(p->p.getParamName().equals(paramsStr)).findFirst().orElse(null);
            if(dtoParams==null){
                result.setSuccess(false);
                failStr.append("；").append(paramsStr).append("参数未配置");
            }
        }
    }
}
