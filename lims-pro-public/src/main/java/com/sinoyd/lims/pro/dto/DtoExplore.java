package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.Explore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoExplore实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_Explore")
@Data
@DynamicInsert
public class DtoExplore extends Explore {
    private static final long serialVersionUID = 1L;

    /**
     * 受检方名称
     */
    @Transient
    private String inspectedEnt;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;
}