package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoSampleDisposeTemp;
import com.sinoyd.lims.pro.entity.SampleDispose;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.SampleDisposeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * SampleDispose操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Service
public class SampleDisposeServiceImpl extends BaseJpaServiceImpl<DtoSampleDispose, String, SampleDisposeRepository> implements SampleDisposeService {

    private SampleRepository sampleRepository;

    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    private SampleDispose2TestRepository sampleDispose2TestRepository;

    private TestRepository testRepository;

    private AnalyseDataRepository analyseDataRepository;

    private AnalyzeItemRepository analyzeItemRepository;

    private SampleTypeRepository sampleTypeRepository;

    private SampleReserveRepository sampleReserveRepository;

    @Override
    public void findByPage(PageBean<DtoSampleDispose> pb, BaseCriteria sampleDisposeCriteria) {
        pb.setEntityName("DtoSampleDispose a,DtoSample b");
        pb.setSelect("select new com.sinoyd.lims.pro.dto.DtoSampleDispose(" +
                " a.id,a.sampleId,b.code,a.sampleSource,a.sampleCount,a.reserveDate," +
                " a.reserveLocation,a.redAnalyzeItems,a.disposePersonId,a.disposeDate," +
                " a.disposeSolution,a.disposeRemarks,a.isDisposed,b.samplingTimeEnd,b.receiveId)");
        comRepository.findByPage(pb, sampleDisposeCriteria);
        List<DtoSampleDispose> data = pb.getData();
        List<String> receiveIds = data.stream().map(DtoSampleDispose::getReceiveId).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordRepository.findAll(receiveIds);
        //格式化时间
        for (DtoSampleDispose dispose : data) {
            Optional<DtoReceiveSampleRecord> recordOption = receiveSampleRecords
                    .stream().filter(r -> r.getId().equals(dispose.getReceiveId())).findFirst();
            recordOption.ifPresent(r -> dispose.setReceivingTime(DateUtil.dateToString(r.getSendTime(), DateUtil.YEAR)));
        }
    }

    @Override
    public DtoSampleDispose findOne(String key) {
        DtoSampleDispose sampleDispose = repository.findOne(key);
        //获取样品，填充样品相关信息
        DtoSample sample = sampleRepository.findOne(sampleDispose.getSampleId());
        if (StringUtil.isNotNull(sample)) {
            sampleDispose.setSampleCode(sample.getCode());
            sampleDispose.setSampleTypeId(sample.getSampleTypeId());
            DtoSampleType sampleType = sampleTypeRepository.findOne(sample.getSampleTypeId());
            if (StringUtil.isNotNull(sampleType)) {
                sampleDispose.setBigSampleTypeId(sampleType.getParentId());
            }
        }
        //填充关联数据
        List<DtoSampleDispose2Test> sampleDispose2Tests = sampleDispose2TestRepository.findBySampleDisposeId(key);
        sampleDispose2Tests.sort(Comparator.comparing(DtoSampleDispose2Test::getRedAnalyzeItemName));
        sampleDispose.setSampleDispose2Tests(sampleDispose2Tests);
        //填充留样处置关联样品下的所有测试项目
        List<DtoAnalyseData> analyseData = analyseDataRepository.findBySampleId(sampleDispose.getSampleId());
        Map<String, String> tests = new HashMap<>();
        for (DtoAnalyseData data : analyseData) {
            tests.put(data.getAnalyseItemId(), data.getRedAnalyzeItemName());
        }
        sampleDispose.setTests(tests);
        return sampleDispose;
    }

    /**
     * 保存数据，主表保存时在关联子表中加入数据
     *
     * @param entity 要新增的实体
     * @return 保存完成的实体
     */
    @Transactional
    @Override
    public DtoSampleDispose save(DtoSampleDispose entity) {
        if (StringUtil.isNotEmpty(entity.getDisposeDateString())) {
            entity.setDisposeDate(DateUtil.stringToDate(entity.getDisposeDateString(), DateUtil.YEAR));
        }
        if (StringUtil.isNotEmpty(entity.getReserveDateString())) {
            entity.setReserveDate(DateUtil.stringToDate(entity.getReserveDateString(), DateUtil.YEAR));
        }
        if (StringUtil.isEmpty(entity.getDisposeSolution())) {
            entity.setDisposeSolution("");
        }
        super.save(entity);
        this.createSampleDispose2Test(entity.getId(), entity.getTestIds());
        return entity;
    }

    /**
     * 批量保存数据，主表保存时在关联子表中加入数据
     *
     * @param dispose 要新增的实体
     * @return 保存完成的实体
     */
    @Transactional
    @Override
    public DtoSampleDispose batchSave(DtoSampleDispose dispose) {
        if (StringUtil.isNotEmpty(dispose.getDisposeDateString())) {
            dispose.setDisposeDate(DateUtil.stringToDate(dispose.getDisposeDateString(), DateUtil.YEAR));
        }
        if (StringUtil.isNotEmpty(dispose.getReserveDateString())) {
            dispose.setReserveDate(DateUtil.stringToDate(dispose.getReserveDateString(), DateUtil.YEAR));
        }
        dispose.setDisposeSolution(StringUtil.isNotEmpty(dispose.getDisposeSolution()) ? dispose.getDisposeSolution() : "");
        List<String> anaItemIdList = dispose.getTestIds();
        List<DtoAnalyzeItem> analyzeItemList = StringUtil.isNotEmpty(anaItemIdList) ? analyzeItemRepository.findAll(anaItemIdList) : new ArrayList<>();
        String[] sampleIdArr = dispose.getSampleId().split(",");
        List<String> sampleIdList = sampleIdArr.length > 0 ? Arrays.asList(sampleIdArr) : new ArrayList<>();
        List<DtoSampleDispose> newDisposeList = new ArrayList<>();
        //所有分析项目都已留样的样品id
        List<String> completeSampleIdList = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleIdList)) {
            List<DtoSampleDispose> disposeForSampleList = repository.findBySampleIdIn(sampleIdList).stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
            Map<String, List<DtoSampleDispose>> disposeMap = disposeForSampleList.stream().collect(Collectors.groupingBy(DtoSampleDispose::getSampleId));
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList);
            analyseDataList.removeIf(DtoAnalyseData::getIsCompleteField);
            analyseDataList.removeIf(DtoAnalyseData::getIsDeleted);

            // 筛选所选样品中时候存在总称
            List<String> testIdList = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
            List<DtoTest> dtoTests = testRepository.findAll(testIdList);
            List<String> parentIds = dtoTests.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getParentId())).map(DtoTest::getParentId).distinct().collect(Collectors.toList());
            List<DtoTest> parentTests = StringUtil.isNotEmpty(parentIds) ? testRepository.findAll(parentIds) : new ArrayList<>();

            Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
            List<DtoSample> sampleList = sampleRepository.findAll(sampleIdList);
            Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
            for (String sampleId : sampleIdList) {
                List<DtoAnalyseData> analyseDataForSample = analyseDataMap.get(sampleId);
                if (StringUtil.isNotEmpty(analyseDataForSample)) {
                    //过滤出当前遍历样品需要留样的分析项目id列表
                    List<String> analyseItemIdForSample = analyseDataForSample.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
                    List<String> anaItemIdsToDispose = analyseItemIdForSample.stream().filter(anaItemIdList::contains).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(anaItemIdsToDispose)) {
                        analyseItemIdForSample.removeAll(anaItemIdsToDispose);
                        DtoSampleDispose newDispose = new DtoSampleDispose();
                        newDispose.setTestIds(anaItemIdsToDispose);
                        BeanUtils.copyProperties(dispose, newDispose, "id");
                        newDispose.setSampleId(sampleId);
                        newDispose.setSampleCode(sampleMap.containsKey(sampleId) ? sampleMap.get(sampleId).getCode() : "");
                        List<String> anaItemNameList = analyzeItemList.stream().filter(p -> anaItemIdsToDispose.contains(p.getId())).map(DtoAnalyzeItem::getAnalyzeItemName).distinct().collect(Collectors.toList());
                        newDispose.setRedAnalyzeItems(StringUtil.isNotEmpty(anaItemNameList) ? String.join(",", anaItemNameList) : "");
                        newDisposeList.add(newDispose);
                    }
                    // 存在总称的情况
                    if (StringUtil.isNotEmpty(parentTests)) {
                        // 获取当前样品下的所有分析方法
                        List<String> analyzeMethodIds = analyseDataForSample.stream().map(DtoAnalyseData::getAnalyzeMethodId).distinct().collect(Collectors.toList());
                        // 获取当前样品下的所选的总称测试项目
                        List<String> analyzeItemId = parentTests.stream().filter(p -> anaItemIdList.contains(p.getAnalyzeItemId()) && analyzeMethodIds.contains(p.getAnalyzeMethodId())).map(DtoTest::getAnalyzeItemId)
                                .collect(Collectors.toList());
                        List<String> anaItemNameList = parentTests.stream().filter(p -> anaItemIdList.contains(p.getAnalyzeItemId()) && analyzeMethodIds.contains(p.getAnalyzeMethodId())).map(DtoTest::getRedAnalyzeItemName)
                                .collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(analyzeItemId)){
                            DtoSampleDispose newDispose = new DtoSampleDispose();
                            newDispose.setTestIds(analyzeItemId);
                            BeanUtils.copyProperties(dispose, newDispose, "id");
                            newDispose.setSampleId(sampleId);
                            newDispose.setSampleCode(sampleMap.containsKey(sampleId) ? sampleMap.get(sampleId).getCode() : "");
                            newDispose.setRedAnalyzeItems(StringUtil.isNotEmpty(anaItemNameList) ? String.join(",", anaItemNameList) : "");
                            newDisposeList.add(newDispose);
                        }
                    }
                    //判断样品所有分析项目是否都已留样
                    collectCompleteSampleId(sampleId, disposeMap, analyseItemIdForSample, completeSampleIdList);
                }
            }
        }
        if (StringUtil.isNotEmpty(newDisposeList)) {
            super.save(newDisposeList);
            this.createSampleDispose2TestBatch(newDisposeList, dispose.getTestIds());
        }
        //所有分析项目都留样完成的样品进行自动处置
        autoDispose(completeSampleIdList);
        return dispose;
    }

    /**
     * 判断样品所有分析项目是否都已留样
     *
     * @param sampleId             样品id
     * @param leftAnalyseItemId    剩余的分析项目id
     * @param completeSampleIdList 已留样完成的样品id
     * @pparam disposeMap          样品留样映射
     */
    private void collectCompleteSampleId(String sampleId, Map<String, List<DtoSampleDispose>> disposeMap, List<String> leftAnalyseItemId, List<String> completeSampleIdList) {
        List<DtoAnalyzeItem> leftAnalyseItemList = StringUtil.isNotEmpty(leftAnalyseItemId) ? analyzeItemRepository.findAll(leftAnalyseItemId) : new ArrayList<>();
        List<String> leftAnalyseItemNameList = leftAnalyseItemList.stream().map(DtoAnalyzeItem::getAnalyzeItemName).distinct().collect(Collectors.toList());
        List<DtoSampleDispose> disposeList = disposeMap.getOrDefault(sampleId, new ArrayList<>());
        Set<String> disposedItemNameSet = new HashSet<>();
        for (DtoSampleDispose dispose : disposeList) {
            String[] itemNames = dispose.getRedAnalyzeItems().split(",");
            disposedItemNameSet.addAll(Arrays.asList(itemNames));
        }
        leftAnalyseItemNameList.removeAll(new ArrayList<>(disposedItemNameSet));
        if (StringUtil.isEmpty(leftAnalyseItemNameList)) {
            completeSampleIdList.add(sampleId);
        }
    }

    /**
     * 所有分析项目都留样完成的样品进行自动处置
     *
     * @param completeSampleIdList 分析项目都留样完成的样品id
     */
    private void autoDispose(List<String> completeSampleIdList) {
        if (StringUtil.isNotEmpty(completeSampleIdList)) {
            List<DtoSampleReserve> existReserveList = sampleReserveRepository.findBySampleIdIn(completeSampleIdList);
            existReserveList = existReserveList.stream().filter(p -> !p.getIsDeleted() && EnumLIM.EnumReserveType.处置.getValue().equals(p.getReserveType()))
                    .collect(Collectors.toList());
            List<String> existSampleIdList = existReserveList.stream().map(DtoSampleReserve::getSampleId).distinct().collect(Collectors.toList());
            completeSampleIdList.removeAll(existSampleIdList);
            Date date = new Date();
            date = DateUtil.stringToDate(DateUtil.dateToString(date, DateUtil.YEAR), DateUtil.YEAR);
            String userId = PrincipalContextUser.getPrincipal().getUserId();
            List<DtoSampleReserve> reserveList = new ArrayList<>();
            for (String sampleId : completeSampleIdList) {
                DtoSampleReserve sampleReserve = new DtoSampleReserve();
                sampleReserve.setSampleId(sampleId);
                sampleReserve.setReserveDate(date);
                sampleReserve.setReservePersonId(userId);
                sampleReserve.setReserveType(EnumLIM.EnumReserveType.处置.getValue());
                sampleReserve.setDisposeMethod("见留样处置管理");
                sampleReserve.setRemark("");
                reserveList.add(sampleReserve);
            }
            if (StringUtil.isNotEmpty(reserveList)) {
                sampleReserveRepository.save(reserveList);
            }
        }

    }

    /**
     * 更新数据同时更新数据绑定的关联数据
     *
     * @param entity 需要更新的数据
     * @return 完成更新的数据
     */
    @Transactional
    @Override
    public DtoSampleDispose update(DtoSampleDispose entity) {
        DtoSampleDispose exist = repository.findOne(entity.getId());
        exist.setReserveLocation(entity.getReserveLocation());
        exist.setSampleCount(entity.getSampleCount());
        exist.setSampleSource(entity.getSampleSource());
        exist.setRedAnalyzeItems(entity.getRedAnalyzeItems());
        exist.setSaveCondition(entity.getSaveCondition());
        if (StringUtil.isNotNull(entity.getReserveDateString())) {
            exist.setReserveDate(DateUtil.stringToDate(entity.getReserveDateString(), "yyyy-MM-dd"));
        }
        exist.setDisposeRemarks(entity.getDisposeRemarks());
        repository.save(exist);
        //直接删除现在存在的关联数据并把新的关联数据重新保存
        sampleDispose2TestRepository.deleteAllBySampleDisposeId(entity.getId());
        this.createSampleDispose2Test(entity.getId(), entity.getTestIds());
        if (StringUtil.isNotNull(entity)) {
            if (entity.getIsDisposed()) {
                DtoSampleDisposeTemp temp = new DtoSampleDisposeTemp();
                temp.setDisposeDate(DateUtil.dateToString(entity.getDisposeDate(), "yyyy-MM-dd HH:mm:ss"));
                temp.setDisposePersonId(entity.getDisposePersonId());
                temp.setDisposeRemark(entity.getDisposeRemarks());
                temp.setDisposeType(entity.getDisposeSolution());
                temp.setDisposeIds(Arrays.asList(entity.getId()));
                this.disposal(temp);
            }
        }
        return exist;
    }

    /**
     * 插入留样处置与测试项目关联数据
     *
     * @param sampleDisposeId 留样处置id
     * @param testIds         测试项目id集合
     */
    private void createSampleDispose2Test(String sampleDisposeId, List<String> testIds) {
        //绑定测试项目
        List<DtoTest> tests = testRepository.findAll(testIds);
        List<DtoAnalyzeItem> analyzeItems = analyzeItemRepository.findAll(testIds);
        List<DtoSampleDispose2Test> sampleDispose2Tests = new ArrayList<>();
        for (String testId : testIds) {
            DtoSampleDispose2Test sampleDispose2Test = new DtoSampleDispose2Test();
            sampleDispose2Test.setSampleDisposeId(sampleDisposeId);
            sampleDispose2Test.setTestId(testId);
            if (StringUtil.isNotEmpty(tests)) {
                //填充分析项目名称
                Optional<DtoTest> testOptional = tests.parallelStream()
                        .filter(t -> t.getId().equals(testId)).findFirst();
                testOptional.ifPresent(t -> sampleDispose2Test.setRedAnalyzeItemName(t.getRedAnalyzeItemName()));
            }
            if (StringUtil.isNotEmpty(analyzeItems)) {
                //填充分析项目名称
                Optional<DtoAnalyzeItem> anaItemOptional = analyzeItems.parallelStream()
                        .filter(t -> t.getId().equals(testId)).findFirst();
                anaItemOptional.ifPresent(t -> sampleDispose2Test.setRedAnalyzeItemName(t.getAnalyzeItemName()));
            }
            sampleDispose2Tests.add(sampleDispose2Test);
        }
        sampleDispose2TestRepository.save(sampleDispose2Tests);
    }

    /**
     * 批量插入留样处置与测试项目关联数据
     *
     * @param disposeList 留样处置对象列表
     */
    private void createSampleDispose2TestBatch(List<DtoSampleDispose> disposeList, List<String> allTestIds) {
        //绑定测试项目
        List<DtoTest> tests = StringUtil.isNotEmpty(allTestIds) ? testRepository.findAll(allTestIds) : new ArrayList<>();
        List<DtoSampleDispose2Test> sampleDispose2Tests = new ArrayList<>();
        for (DtoSampleDispose dispose : disposeList) {
            List<String> testIds = dispose.getTestIds();
            for (String testId : testIds) {
                DtoSampleDispose2Test sampleDispose2Test = new DtoSampleDispose2Test();
                sampleDispose2Test.setSampleDisposeId(dispose.getId());
                sampleDispose2Test.setTestId(testId);
                //填充分析项目名称
                Optional<DtoTest> testOptional = tests.parallelStream()
                        .filter(t -> t.getId().equals(testId)).findFirst();
                testOptional.ifPresent(t -> sampleDispose2Test.setRedAnalyzeItemName(t.getRedAnalyzeItemName()));
                sampleDispose2Tests.add(sampleDispose2Test);
            }
        }
        sampleDispose2TestRepository.save(sampleDispose2Tests);
    }

    /**
     * 处置留样
     *
     * @param disposeTemp 要处置的留样list
     * @return 处置的留样数据
     */
    @Transactional
    @Override
    public List<DtoSampleDispose> disposal(DtoSampleDisposeTemp disposeTemp) {
        List<DtoSampleDispose> exists = repository.findAll(disposeTemp.getDisposeIds());
        DtoSampleDispose hasBeenDispose = exists.stream().filter(SampleDispose::getIsDisposed).findFirst().orElse(null);
        if (StringUtil.isNotNull(hasBeenDispose)) {
            throw new BaseException("只能对未处置的样品进行处置，请确认后再操作！");
        }
        //更新数据
        for (DtoSampleDispose exist : exists) {
            exist.setDisposePersonId(disposeTemp.getDisposePersonId());
            exist.setDisposeDate(DateUtil.stringToDate(disposeTemp.getDisposeDate(), DateUtil.YEAR));
            exist.setDisposeRemarks(disposeTemp.getDisposeRemark());
            exist.setDisposeSolution(disposeTemp.getDisposeType());
            exist.setIsDisposed(Boolean.TRUE);
        }
        repository.save(exists);
        return exists;
    }

    /**
     * 抽血删除方法，在删除同事删除关联表中的数据
     *
     * @param ids 需要删除的数据id集合
     * @return 删除的条数
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        //删除关联表中数据
        sampleDispose2TestRepository.deleteAllBySampleDisposeIdIn(ids);
        return super.logicDeleteById(ids);
    }

    /**
     * 根据样品id判断是否已经有留样处置数据
     *
     * @param sampleId 样品id
     * @return true为已经存在，false为不存在
     */
    @Override
    public Boolean checkSampleIsDisposed(String sampleId) {
        List<DtoSampleDispose> sampleDisposes = repository.findBySampleId(sampleId);
        if (StringUtil.isNotEmpty(sampleDisposes)) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    /**
     * 根据样品id列表判断是否已经有留样处置数据
     *
     * @param sampleDispose 留样管理实体
     * @return true为已经存在，false为不存在
     */
    @Override
    public Boolean checkSamplesIsDisposed(DtoSampleDispose sampleDispose) {
        List<String> sampleIds = sampleDispose.getSampleIds();
        List<String> testIds = sampleDispose.getTestIds();
        List<DtoSampleDispose> sampleDisposes = repository.findBySampleIdIn(sampleIds);
        List<String> disposeIds = sampleDisposes.stream().map(DtoSampleDispose::getId).collect(Collectors.toList());
        List<DtoSampleDispose2Test> sampleDispose2TestList = sampleDispose2TestRepository.findBySampleDisposeIdIn(disposeIds);
        if (StringUtil.isNotEmpty(sampleDispose2TestList)) {
            List<DtoSampleDispose2Test> dispose2TestsToTestId = sampleDispose2TestList.stream().filter(p -> testIds.contains(p.getTestId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(dispose2TestsToTestId)) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        } else {
            return Boolean.FALSE;
        }
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setReceiveSampleRecordRepository(ReceiveSampleRecordRepository receiveSampleRecordRepository) {
        this.receiveSampleRecordRepository = receiveSampleRecordRepository;
    }

    @Autowired
    public void setSampleDispose2TestRepository(SampleDispose2TestRepository sampleDispose2TestRepository) {
        this.sampleDispose2TestRepository = sampleDispose2TestRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    public void setSampleReserveRepository(SampleReserveRepository sampleReserveRepository) {
        this.sampleReserveRepository = sampleReserveRepository;
    }
}