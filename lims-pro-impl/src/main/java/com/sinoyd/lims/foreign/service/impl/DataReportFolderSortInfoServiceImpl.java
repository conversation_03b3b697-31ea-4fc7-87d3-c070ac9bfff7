package com.sinoyd.lims.foreign.service.impl;


import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataReportFolderSortInfoVO;
import com.sinoyd.lims.foreign.service.DataReportFolderSortInfoService;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 报告点位排序接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataReportFolderSortInfoServiceImpl extends AbsQueryDataServiceImpl<DataReportFolderSortInfoVO> implements DataReportFolderSortInfoService {


    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id,reportId,folderId,");
        sql.append("orderNum,orgId,domainId");
        sql.append(" from TB_PRO_ReportFolderSortInfo ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime", "syncTime");
        super.appendCondition(sql, map, "AND reportId in (:reportIds)", "reportIds");
        super.appendCondition(sql, map, "AND folderId in (:folderIds) ", "folderIds");
    }

    @Override
    protected RowMapper<DataReportFolderSortInfoVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataReportFolderSortInfoVO reportFolderSortInfoVO = new DataReportFolderSortInfoVO();
            reportFolderSortInfoVO.setId(UUIDHelper.NewID());
            reportFolderSortInfoVO.setReportId(rs.getString("reportId"));
            reportFolderSortInfoVO.setFolderId(rs.getString("folderId"));
            reportFolderSortInfoVO.setOrderNum(rs.getInt("orderNum"));
            reportFolderSortInfoVO.setOrgId(rs.getString("orgId"));
            reportFolderSortInfoVO.setDomainId(rs.getString("domainId"));
            reportFolderSortInfoVO.setSyncTime(new Date());
            return reportFolderSortInfoVO;
        };
    }

}
