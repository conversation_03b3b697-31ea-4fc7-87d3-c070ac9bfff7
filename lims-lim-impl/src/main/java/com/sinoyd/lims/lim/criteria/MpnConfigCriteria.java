package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * MpnConfig查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MpnConfigCriteria extends BaseCriteria implements Serializable {

    /**
     * 分项项目
     */
    private String item;

    /**
     * 分析方法、标准编号
     */
    private String method;


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and m.testId = t.id");
        if (StringUtil.isNotEmpty(item)) {
            condition.append(" and t.redAnalyzeItemName like :item");
            values.put("item", "%" + item + "%");
        }
        if (StringUtil.isNotEmpty(method)) {
            condition.append(" and (t.redAnalyzeMethodName like :method or redCountryStandard like :method)");
            values.put("method", "%" + method + "%");
        }
        return condition.toString();
    }

}
