package com.sinoyd.lims.strategy;

/**
 * 订单附件名称生成
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/28
 */
public interface IFileNameConstant {

    /**
     * 质控策略Key
     */
    interface FileNameStrategyKey {
        String ORDER_FILENAME = "orderFileNameStrategy";

        String PROJECT_FILENAME = "projectFileNameStrategy";

        String REPORT_FILENAME = "reportFileNameStrategy";

        String SAMPLING_FILENAME = "samplingFileNameStrategy";

        String WORKSHEET_FILENAME = "worksheetFileNameStrategy";
    }

    /**
     * 质控策略Key
     */
    interface JudgeDataStrategyKey {
        String WATER_JUDGEDATA = "waterJudgeData";

        String GAS_JUDGEDATA = "gasJudgeData";
    }

    /**
     * 质控策略Key
     */
    interface SchemeSynchronizationKey {
        String DELETED_SAMPLE = "deletedSample";
        String ADD_SAMPLE = "addSample";

        String UPDATE_SAMPLEFOLDER = "updateSampleFolder";
        String DELETED_SAMPLEFOLDER = "deletedSampleFolder";
        String ADD_SAMPLEFOLDER = "addSampleFolder";

        String DELETED_TEST = "deletedTest";
        String ADD_TEST = "addTest";
        String UPDATE_TEST = "updateTest";
    }

    /**
     * 数据校验策略Key
     */
    interface DataValidateStrategyKey {
        String DATE_VALIDATE = "dateValidate";
        String DATE_TIME_VALIDATE = "dateTimeValidate";
        String TIME_VALIDATE = "timeValidate";
        String DATA_SOURCE_VALIDATE = "dataSourceValidate";
        String NUMBER_VALIDATE = "numberValidate";
    }

    /**
     * 数据提交策略Key
     */
    interface SubmitStrategyKey {
        String SUBMIT_ORDER = "submitOrder";
        String SUBMIT_PROJECT = "submitProject";
        String SUBMIT_RECEIVE_TASK = "submitReceiveTask";
        String SUBMIT_RECEIVE_SAMPLE = "submitReceiveSample";
        String SUBMIT_SAMPLE_DISTRIBUTION = "submitSampleDistribution";
        String SUBMIT_WORKSHEET_DATA = "submitWorkSheetData";
        String SUBMIT_REPORT = "submitReport";
        String FINISH_REPORT = "finishReport";
        String SUBMIT_RECEIVE = "submitReceive";
    }
}
