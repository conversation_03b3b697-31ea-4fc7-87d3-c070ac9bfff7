package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * 采样项目的工作量统计
 * <AUTHOR>
 * @version V1.0.0 2020年2月21日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceStatisticForReportDataCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分析开始时间
     */
    private String startTime;

    /**
     * 分析结束时间
     */
    private String endTime;

    /**
     * 人员id
     */
    private String userId;

    /**
     * 排序方式
     */
    private String sort;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.projectId=b.id");
        condition.append(" and b.projectTypeId=c.id");
        if (StringUtils.isNotNullAndEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.reportTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtils.isNotNullAndEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and a.reportTime < :endTime");
            values.put("endTime", c.getTime());
        }
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            condition.append(" and a.orgId =:orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        condition.append(" and a.reportMakerId =:reportMakerId");
        values.put("reportMakerId", this.userId);
        return condition.toString();
    }
}
