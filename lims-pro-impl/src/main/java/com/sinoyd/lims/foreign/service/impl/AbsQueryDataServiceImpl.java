package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.foreign.service.QueryDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * TCD数据同步抽象类
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/15
 * @since V100R001
 */
@Component
@Slf4j
public abstract class AbsQueryDataServiceImpl<T> implements QueryDataService<T> {

    protected NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public List<T> getSyncData(Map<String, Object> map) {
        // 获取sql 语句
        StringBuilder sql = getSql();
        // 拼接条件
        appendCondition(sql, map, "", "");
        Date start = new Date();
        log.info("查询语句" + sql.toString());
        List<T> list = namedParameterJdbcTemplate.query(sql.toString(), map, getEntityRowMapper());
        Date end = new Date();
        log.info("查询耗时=======================" + (end.getTime() - start.getTime()) + "毫秒===========================");
        // 个性化数据处理
        individuationData(map, list);
        log.info("总数=======================" + list.size() + "条===========================");
        return list;
    }

    /**
     * 获取sql 语句
     *
     * @return
     */
    public abstract StringBuilder getSql();

    /**
     * 动态拼接sql条件
     *
     * @param sql       sql
     * @param map       参数值
     * @param paramName 参数名称
     * @param condition 条件
     */
    protected void appendCondition(StringBuilder sql,
                                   Map<String, Object> map,
                                   String condition,
                                   String paramName) {
        if (StringUtil.isNotEmpty(condition)) {
            Object mapValue = map.get(paramName);
            if (mapValue instanceof String && StringUtil.isNotEmpty(mapValue.toString())) {
                sql.append(" ").append(condition);
            } else if (mapValue instanceof List<?>) {
                List<?> list = (List<?>) mapValue;
                if (StringUtil.isNotEmpty(list)) {
                    sql.append(" ").append(condition);
                }
            }
        }
    }

    /**
     * 获取实体映射数据
     *
     * @return
     */
    protected abstract RowMapper<T> getEntityRowMapper();

    /**
     * 预留个性化数据处理
     *
     * @param list 数据源
     */
    protected void individuationData(Map<String, Object> map, List<T> list) {
    }

    @Autowired
    public void setNamedParameterJdbcTemplate(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
    }
}
