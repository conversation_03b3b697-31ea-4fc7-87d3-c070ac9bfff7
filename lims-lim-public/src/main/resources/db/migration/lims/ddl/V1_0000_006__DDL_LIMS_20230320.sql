-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关DDL脚本 -----------
-- ---------------- Monitor模块 -----------------
-- ----------------------------------------------

create table TB_MONITOR_FixedPointProperty
(
    id           varchar(50)                                                not null comment '主键' primary key,
    parentId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '关联id',
    propertyName varchar(255)                                               null comment '监测计划名称',
    orderNum     int         default -1                                     not null comment '排序值',
    year         int         default -1                                     not null comment '年份',
    month        int         default -1                                     not null comment '月份',
    cycleOrder   int         default 1                                      not null comment '周期',
    timesOrder   int         default 1                                      not null comment '次数',
    sampleTypeId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样品类型',
    pointType    varchar(50)                                                null comment '点位类型：常量（河流、湖库、饮用水、功能区噪声、区域环境噪声、交通噪声、底泥、大气、地下水）',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    isDeleted    bit         default b'0'                                   not null comment '假删',
    remark       varchar(2000)                                              null comment '备注',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_MONITOR_FixedPointSort
(
    id         varchar(50)                                                not null comment '主键' primary key,
    sortName   varchar(255)                                               null comment '排序名称',
    orderNum   int         default -1                                     not null comment '排序值',
    isDeleted  bit         default b'0'                                   not null comment '假删',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_MONITOR_FixedPointSortDetil
(
    id           varchar(50)                                                not null
        primary key,
    sortId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '排序id',
    fixedPointId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '点位id',
    orderNum     int         default -1                                     not null comment '排序值',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_MONITOR_OicInformation
(
    id              varchar(50)                                                not null comment '主键'
        primary key,
    fixedPointId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属点位',
    instrumentName  varchar(50)                                                null comment '仪器名称',
    instrumentModel varchar(50)                                                null comment '仪器型号',
    instrumentCode  varchar(50)                                                null comment '出厂编号',
    `range`         varchar(50)                                                null comment '量程',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_MONITOR_OicInformation2Test
(
    id     varchar(50) not null comment '主键' primary key,
    oicId  varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '在线仪器id',
    testId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '测试项目id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_MONITOR_Property2Point
(
    id           varchar(50) not null comment '主键' primary key,
    fixedPointId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '点位id',
    propertyId   varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '监测计划id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table tb_monitor_propertypoint2test
(
    id              varchar(50) not null comment '主键' primary key,
    propertyPointId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '检测计划和点位关联id',
    testId          varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '测试项目id'
)
    ENGINE = InnoDB
    CHARACTER SET = utf8
    COLLATE = utf8_general_ci
    ROW_FORMAT = Dynamic;

