package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoMpnConfigDetails;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * Mnp配置接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
public interface MpnConfigDetailsService extends IBaseJpaService<DtoMpnConfigDetails, String> {


    /**
     * 分页查询动态详情
     *
     * @param pageBean     分页条件
     * @param baseCriteria 查询条件
     * @return 动态详情
     */
    Map<String, Object> findDetailsByPage(PageBean<DtoMpnConfigDetails> pageBean, BaseCriteria baseCriteria);

    /**
     * 导出模版
     *
     * @param mpnConfigId 配置id
     * @param response    响应提
     */
    void exportTemplate(String mpnConfigId, HttpServletResponse response);

    /**
     * 根据测试项目ids查询MNP配置详情
     *
     * @param testIds 测试项目ids
     * @return MNP配置详情集合
     */
    List<DtoMpnConfigDetails> findByTestIdIn(List<String> testIds);

    /**
     * 根据mnp配置ids 删除
     *
     * @param mpnConfigIds mnp配置Ids
     */
    void deleteByMpnConfigIdIn(List<String> mpnConfigIds);


    /**
     * 数据导入
     *
     * @param file        导入文件
     * @param mpnConfigId 配置id
     * @param response    响应体
     */
    void importData(MultipartFile file, String mpnConfigId, HttpServletResponse response);

    /**
     * 数据导出
     *
     * @param mpnConfigCriteria 查询条件
     * @param response          响应体
     */
    void export(BaseCriteria mpnConfigCriteria, HttpServletResponse response);
}
