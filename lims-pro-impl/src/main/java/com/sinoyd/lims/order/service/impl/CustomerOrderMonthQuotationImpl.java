package com.sinoyd.lims.order.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import com.sinoyd.lims.pro.vo.OrderMonthQuotationVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service(CustomerOrderAnaService.CUSTOMER_ORDER_MONTH_QUOTATION)
public class CustomerOrderMonthQuotationImpl extends AbsCustomerOrder {

    /**
     * 合同额年度趋势
     *
     * @param year 年份
     * @return List<OrderMonthQuotationVo>
     */
    @Override
    public List<BaseOrderCustomerAnalyzeVO> statisticsList(Integer year) {
        String currentYearBegin = CalendarUtil.getCurrentYearBegin(year);
        String currentYearEnd = CalendarUtil.getCurrentYearEnd(year);
        //新增订单sql
        StringBuilder orderStatusSb = new StringBuilder();
        orderStatusSb.append("SELECT t1.orderDate,t2.finalQuotation FROM TB_PRO_OrderForm t1,TB_PRO_OrderQuotation t2 WHERE t1.isDeleted = 0 and t2.isDeleted = 0 and orderDate BETWEEN");
        orderStatusSb.append(" ? ");
        orderStatusSb.append(" AND ");
        orderStatusSb.append(" ? ");
        orderStatusSb.append(" AND ");
        orderStatusSb.append("orderStatus = ? ");
        orderStatusSb.append(" AND t1.id = t2.orderId");
        Object[] orderStatusParams = new Object[] {currentYearBegin,currentYearEnd,EnumPRO.EnumOrderStatus.审核通过.getValue() };
        List<Map<String, Object>> orderSatatusList = super.getCustomerOrderData(orderStatusSb.toString(),orderStatusParams);
        if (orderSatatusList == null) {
            return null;
        }
        List<BaseOrderCustomerAnalyzeVO> orderMonthQuotationVoList = new ArrayList<>();
        Map<Integer, Integer> monthSatatusMap = getMonthQuotation(orderSatatusList);
        for (Map.Entry<Integer, Integer> entry : monthSatatusMap.entrySet()) {
            OrderMonthQuotationVo vo = new OrderMonthQuotationVo();
            vo.setType("addNum");
            vo.setMonth(entry.getKey());
            vo.setFinalQuotation(entry.getValue());
            orderMonthQuotationVoList.add(vo);
        }

        //合同签单sql
        StringBuilder orderSignStatusSb = new StringBuilder();
        orderSignStatusSb.append("SELECT t1.signDate orderDate,t2.finalQuotation FROM TB_PRO_OrderForm t1,TB_PRO_OrderQuotation t2 WHERE t1.isDeleted = 0 and t2.isDeleted = 0 and signDate BETWEEN");
        orderSignStatusSb.append(" ? ");
        orderSignStatusSb.append(" AND ");
        orderSignStatusSb.append(" ? ");
        orderSignStatusSb.append(" AND ");
        orderSignStatusSb.append("grantStatus = ? ");
        orderSignStatusSb.append(" AND t1.id = t2.orderId");
        Object[] orderSignStatusParams = new Object[]{currentYearBegin,currentYearEnd,EnumPRO.EnumOrderSignStatus.已签订.getValue()};
        List<Map<String, Object>> orderSignSatatusList = super.getCustomerOrderData(orderSignStatusSb.toString(),orderSignStatusParams);
        if (orderSignSatatusList == null) {
            return null;
        }
        Map<Integer, Integer> monthSignSatatusMap = getMonthQuotation(orderSignSatatusList);
        for (Map.Entry<Integer, Integer> entry : monthSignSatatusMap.entrySet()) {
            OrderMonthQuotationVo vo = new OrderMonthQuotationVo();
            vo.setType("signNum");
            vo.setMonth(entry.getKey());
            vo.setFinalQuotation(entry.getValue());
            orderMonthQuotationVoList.add(vo);
        }
        return orderMonthQuotationVoList;
    }

    /**
     * 合同额年度趋势按照月份返回订单
     *
     * @param satatusList
     * @return
     */
    private Map<Integer, Integer> getMonthQuotation(List<Map<String, Object>> satatusList) {
        Map<Integer, Integer> map = new HashMap<>();
        for (int i = 0; i < satatusList.size(); i++) {
            String finalQuotationString = satatusList.get(i).get("finalQuotation").toString();
            String orderDate = satatusList.get(i).get("orderDate").toString();
            Date date = DateUtil.stringToDate(orderDate, DateUtil.YEAR);
            Integer month = CalendarUtil.getMonth(date);
            Integer finalQuotation = new BigDecimal(finalQuotationString).intValue();
            if (i == 0) {
                map.put(month, finalQuotation);
            } else {
                int x = 0;
                for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
                    if (entry.getKey().equals(month)) {
                        x++;
                        map.put(month, entry.getValue() + finalQuotation);
                    }
                }
                if (x == 0) {
                    map.put(month, finalQuotation);
                }
            }
        }
        return map;
    }

}
