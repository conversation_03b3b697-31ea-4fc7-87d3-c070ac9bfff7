package com.sinoyd.lims.order.controller;


import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.order.service.statistics.CustomerOrderStatisticsContextImpl;
import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 领导驾驶舱客户订单分析大屏展示
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/09/26
 */
@Api(tags = "领导驾驶舱客户订单分析大屏展示")
@RestController
@RequestMapping("api/pro/customerOrderAna")
public class CustomerOrderAnaController extends ExceptionHandlerController<CustomerOrderStatisticsContextImpl> {

    /**
     * 人员订单签单额统计
     * @param year 年份
     * @return RestResponse<List<BaseOrderCustomerAnalyzeVO>>
     */
    @ApiOperation(value = "人员订单签单额统计", notes = "人员订单签单额统计")
    @GetMapping("/orderStatistics")
    public RestResponse<List<BaseOrderCustomerAnalyzeVO>> getOrderStatisticsData(Integer year) {
        List<BaseOrderCustomerAnalyzeVO> orderStatisticsData = service.getCustomerOrderList(CustomerOrderAnaService.CUSTOMER_ORDER_STATISTICS,year);
        RestResponse<List<BaseOrderCustomerAnalyzeVO>> response = new RestResponse();
        response.setData(orderStatisticsData);
        return response;
    }

    /**
     * 单笔签单额top10
     * @param year 年份
     * @return RestResponse<List<BaseOrderCustomerAnalyzeVO>>
     */
    @ApiOperation(value = "单笔签单额top10", notes = "单笔签单额top10")
    @GetMapping("/orderTopTen")
    public RestResponse<List<BaseOrderCustomerAnalyzeVO>> getOrderTopTen(Integer year) {
        List<BaseOrderCustomerAnalyzeVO> orderStatisticsData = service.getCustomerOrderList(CustomerOrderAnaService.CUSTOMER_ORDER_TOP_TEN,year);
        RestResponse<List<BaseOrderCustomerAnalyzeVO>> response = new RestResponse();
        response.setData(orderStatisticsData);
        return response;
    }

    /**
     * 合同类型分布
     * @param year 年份
     * @return RestResponse<List<BaseOrderCustomerAnalyzeVO>>
     */
    @ApiOperation(value = "合同类型分布", notes = "合同类型分布")
    @GetMapping("/orderTypeSpread")
    public RestResponse<List<BaseOrderCustomerAnalyzeVO>> getOrderTypeSpread(Integer year) {
        List<BaseOrderCustomerAnalyzeVO> orderStatisticsData = service.getCustomerOrderList(CustomerOrderAnaService.CUSTOMER_ORDER_TYPE_SPREAD,year);
        RestResponse<List<BaseOrderCustomerAnalyzeVO>> response = new RestResponse();
        response.setData(orderStatisticsData);
        return response;
    }

    /**
     * 合同额年度趋势
     * @param year 年份
     * @return RestResponse<List<BaseOrderCustomerAnalyzeVO>>
     */
    @ApiOperation(value = "合同额年度趋势", notes = "合同额年度趋势")
    @GetMapping("/orderMonthQuotation")
    public RestResponse<List<BaseOrderCustomerAnalyzeVO>> getOrderMonthQuotation(Integer year) {
        List<BaseOrderCustomerAnalyzeVO> orderStatisticsData = service.getCustomerOrderList(CustomerOrderAnaService.CUSTOMER_ORDER_MONTH_QUOTATION,year);
        RestResponse<List<BaseOrderCustomerAnalyzeVO>> response = new RestResponse();
        response.setData(orderStatisticsData);
        return response;
    }

    /**
     * 合同额分析
     * @param year 年份
     * @return RestResponse<BaseOrderCustomerAnalyzeVO>
     */
    @ApiOperation(value = "合同额分析", notes = "合同额分析")
    @GetMapping("/orderQuotationAnl")
    public RestResponse<BaseOrderCustomerAnalyzeVO> getOrderQuotationAnl(Integer year) {
        BaseOrderCustomerAnalyzeVO orderStatisticsData = service.getCustomerOrderData(CustomerOrderAnaService.CUSTOMER_ORDER_QUOTATION_ANL,year);
        RestResponse<BaseOrderCustomerAnalyzeVO> response = new RestResponse();
        response.setData(orderStatisticsData);
        return response;
    }

    /**
     * 客户数量分析
     * @param year 年份
     * @return RestResponse<BaseOrderCustomerAnalyzeVO>
     */
    @ApiOperation(value = "客户数量分析", notes = "客户数量分析")
    @GetMapping("/orderCustomerNumAnl")
    public RestResponse<BaseOrderCustomerAnalyzeVO> getOrderCustomerAnl(Integer year) {
        BaseOrderCustomerAnalyzeVO orderStatisticsData = service.getCustomerOrderData(CustomerOrderAnaService.CUSTOMER_ORDER_NUM_ANL,year);
        RestResponse<BaseOrderCustomerAnalyzeVO> response = new RestResponse();
        response.setData(orderStatisticsData);
        return response;
    }

    /**
     * 客户地区分布签单信息
     * @param year 年份
     * @return RestResponse<List<BaseOrderCustomerAnalyzeVO>>
     */
    @ApiOperation(value = "客户地区分布签单信息", notes = "客户地区分布签单信息")
    @GetMapping("/orderCustomerSpread")
    public RestResponse<List<BaseOrderCustomerAnalyzeVO>> getOrderCustomerSpread(Integer year) {
        List<BaseOrderCustomerAnalyzeVO> orderStatisticsData = service.getCustomerOrderList(CustomerOrderAnaService.CUSTOMER_ORDER_SPREAD,year);
        RestResponse<List<BaseOrderCustomerAnalyzeVO>> response = new RestResponse();
        response.setData(orderStatisticsData);
        return response;
    }
}
