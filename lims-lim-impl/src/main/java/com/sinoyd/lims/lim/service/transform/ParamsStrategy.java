package com.sinoyd.lims.lim.service.transform;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.ParamsMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Order(30)
public class ParamsStrategy implements TransformImportStrategy {

    private ParamsMapper paramsMapper;
    private ParamsRepository paramsRepository;

    @Override
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception {

    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {
        // 获取待导入参数
        List<DtoExportParams> paramsList = testDependentData.getParamsList();
        List<DtoImportCheck> importChecks = dtoDataSyncParams.getImportChecks();
        // 获取导入前检查的参数和量纲
        List<DtoDataCheck> dimensionCheck = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.量纲表.getCheckItem()))
                .flatMap(p -> p.getDataChecks().stream()).collect(Collectors.toList());
        List<DtoDataCheck> paramsCheck = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.参数表.getCheckItem()))
                .flatMap(p -> p.getDataChecks().stream()).collect(Collectors.toList());
        Map<String, DtoDataCheck> dimensionCheckMap = dimensionCheck.stream().collect(Collectors.toMap(DtoDataCheck::getName, p -> p));

        List<String> addName = paramsCheck.stream().filter(p -> BASE_DATA_TYPE[0].equals(p.getType())).map(DtoDataCheck::getName).collect(Collectors.toList());
        // 根据名称筛选出待新增数据
        List<DtoParams> dtoParamsList = paramsList.stream()
                .filter(p -> addName.contains(p.getParamName()))
                .map(p -> {
                    DtoParams params = paramsMapper.toDtoParams(p);
                    DtoDataCheck orDefault = dimensionCheckMap.getOrDefault(p.getDimension(), new DtoDataCheck());
                    params.setDimension(orDefault.getName());
                    params.setDimensionId(orDefault.getId());
                    return params;
                }).collect(Collectors.toList());
        importTestTemp.setParamsTemps(dtoParamsList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoParams> paramsTemps = importTestTemp.getParamsTemps();
        if (StringUtil.isNotEmpty(paramsTemps)) {
            //已同步记录数
            int i = 0;
            for (DtoParams params : paramsTemps) {
                paramsRepository.save(params);
                webSocketServer.sendMessage(getMessage(paramsTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(paramsTemps.size(), 0));
        }
    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.参数表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.参数表.getSource();
    }


    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.参数表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        List<DtoExportParams> paramsList = testDependentData.getParamsList();
        List<DtoImportCheck> importChecks = new ArrayList<>();
        List<DtoDataCheck> paramsCheckList = new ArrayList<>();
        if (StringUtil.isNotEmpty(paramsList)) {
            List<DtoParams> params = paramsRepository.findAll();
            paramsCheckList = paramsList.parallelStream()
                    .map(p -> {
                        DtoDataCheck dtoDataCheck = new DtoDataCheck();
                        dtoDataCheck.setName(p.getParamName());
                        Map<String, Object> otherField = new HashMap<>();
                        otherField.put("paramName", p.getParamName());
                        dtoDataCheck.setOtherField(otherField);
                        Optional<DtoParams> paramsOptional = params.stream().filter(r -> r.getParamName().equals(p.getParamName())).findFirst();
                        if (paramsOptional.isPresent()) {
                            dtoDataCheck.setType(BASE_DATA_TYPE[1]);
                            dtoDataCheck.setId(paramsOptional.get().getId());
                        } else {
                            dtoDataCheck.setType(BASE_DATA_TYPE[0]);
                            dtoDataCheck.setId(p.getId());
                        }
                        return dtoDataCheck;
                    }).collect(Collectors.toList());
        }
        List<DtoDataCheck> existsList = paramsCheckList.stream().filter(p -> BASE_DATA_TYPE[1].equals(p.getType())).collect(Collectors.toList());
        List<DtoDataCheck> noExistsList = paramsCheckList.stream().filter(p -> BASE_DATA_TYPE[0].equals(p.getType())).collect(Collectors.toList());
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.参数表.getCheckItem(),
                BASE_DATA_TYPE[1], existsList.size(), "所有外键ID关联改为系统内的ID，不进行导入。",
                existsList));
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.参数表.getCheckItem(),
                BASE_DATA_TYPE[0], noExistsList.size(), "新增参数，导入时将进行插入。",
                noExistsList));
        return importChecks;
    }

    @Autowired
    @Lazy
    public void setParamsMapper(ParamsMapper paramsMapper) {
        this.paramsMapper = paramsMapper;
    }

    @Autowired
    @Lazy
    public void setParamsRepository(ParamsRepository paramsRepository) {
        this.paramsRepository = paramsRepository;
    }
}
