package com.sinoyd.lims.pro.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoOtherExpenditure;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.OtherExpenditureService;
import com.sinoyd.lims.pro.dto.DtoOADepartmentExpend;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.repository.OADepartmentExpendRepository;
import com.sinoyd.lims.pro.service.OADepartmentExpendService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 部门支出 业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@Service
public class OADepartmentExpendServiceImpl
        extends BaseJpaServiceImpl<DtoOADepartmentExpend, String, OADepartmentExpendRepository>
        implements OADepartmentExpendService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    @Autowired
    @Lazy
    private OtherExpenditureService otherExpenditureService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    @Override
    public String startProcess(DtoOATaskCreate<DtoOADepartmentExpend> taskDto) {
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        Map<String, Object> vars = new HashMap<>();

//        // 获取用户对应的部门负责人
//        DtoUser deptPrincipal = deptService.findPrincipal(user.getUserId());
//        if (StringUtil.isNotNull(deptPrincipal)) {
//            taskDto.setNextAssignee(deptPrincipal.getLoginId());
//            taskDto.setNextAssigneeId(deptPrincipal.getId());
//            taskDto.setNextAssigneeName(deptPrincipal.getUserName());
//        }
//        else {
//            taskDto.setNextAssignee(user.getLoginId());
//            taskDto.setNextAssigneeId(user.getUserId());
//            taskDto.setNextAssigneeName(user.getUserName());
//        }

        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.部门支出, taskDto, vars);

        DtoOADepartmentExpend deptExpend = taskDto.getData();
        deptExpend.setDescription(taskDto.getDescription());
        deptExpend.setDeptId(StringUtil.isNotNull(user.getDeptId()) ? user.getDeptId() : UUIDHelper.GUID_EMPTY);
        // 添加支出信息
        super.save(deptExpend);

        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(deptExpend.getId());

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelation);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<DtoOADepartmentExpend, String> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<DtoOADepartmentExpend, String> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(taskId);

        DtoOADepartmentExpend deptExpend = super.findOne(relation.getObjectId());
        taskDetail.setDetail(deptExpend);

        return taskDetail;
    }

    @Override
    public void confirm(String id) {
        repository.confirm(id, true);
    }


    public void syncOtherExpenditure(String id, DtoOATask oaTask) {
        DtoOADepartmentExpend oaDepartmentExpend = repository.findOne(id);
        if (StringUtil.isNotNull(oaDepartmentExpend)) {
            DtoOtherExpenditure otherExpenditure = new DtoOtherExpenditure();
            otherExpenditure.setOperatorId(oaTask.getSponsorId());
            otherExpenditure.setOperator(oaTask.getSponsorName());
            otherExpenditure.setOperateDate(oaDepartmentExpend.getExpendDate());
            otherExpenditure.setDeptId(oaTask.getDeptId());
            otherExpenditure.setPaytype(oaDepartmentExpend.getTypeId());
            otherExpenditure.setCategory(EnumLIM.EnumExpenditureCategory.支出.getValue());
            otherExpenditure.setAmount(oaDepartmentExpend.getAmount());
            otherExpenditure.setExplain(oaTask.getDescription());
            otherExpenditureService.save(otherExpenditure);
        }
    }

    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<DtoOADepartmentExpend> taskDto) {
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.部门支出, taskDto, new HashMap<>());
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<DtoOADepartmentExpend> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<DtoOADepartmentExpend> taskDto,DtoOATask oaTask){
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        DtoOADepartmentExpend deptExpend = taskDto.getData();
        deptExpend.setDescription(taskDto.getDescription());
        deptExpend.setDeptId(StringUtil.isNotNull(user.getDeptId()) ? user.getDeptId() : UUIDHelper.GUID_EMPTY);
        // 添加支出信息
        super.save(deptExpend);
        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(deptExpend.getId());
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelation);
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<DtoOADepartmentExpend> taskDto) {
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }
}
