package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * Person2Test实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Person2Test")
 @Data
 public  class Person2Test implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Person2Test() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 人员Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("人员Id")
	private String personId;
    
    /**
    * 测试项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试项目id")
	private String testId;
    
    /**
    * 排序值
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("排序值")
    private Integer orderNum;
    
    /**
    * 样品类型id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("样品类型id")
    private String sampleTypeId;
    
    /**
    * 是否默认分析人员
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否默认人员")
    private Boolean isDefaultPerson;

    /**
     * 是否默认复核人员
     */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否默认审核人员")
    private Boolean isDefaultAuditPerson;

 /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }