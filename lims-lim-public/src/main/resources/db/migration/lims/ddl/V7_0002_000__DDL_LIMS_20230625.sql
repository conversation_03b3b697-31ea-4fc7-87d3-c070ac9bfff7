DROP TABLE IF EXISTS TB_PRO_SampleJudgeData;
CREATE TABLE TB_PRO_SampleJudgeData  (
                                         id varchar(50) NOT NULL COMMENT '主键',
                                         sampleId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '样品id',
                                         testId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '测试项目id',
                                         judgingMethod int(11) NULL DEFAULT NULL COMMENT '评判方式',
                                         compareType int(11) NULL DEFAULT NULL COMMENT '比对类型',
                                         checkType int(11) NULL DEFAULT NULL COMMENT '检测类型（0-废水比对，1-废气比对）',
                                         onlineValue varchar(50) NOT NULL DEFAULT '' COMMENT '在线值',
                                         expectedValue varchar(50) NOT NULL DEFAULT '' COMMENT '理论值',
                                         qcRateValue varchar(255) NULL DEFAULT NULL COMMENT '判定结果',
                                         pass varchar(50) NULL DEFAULT NULL COMMENT '是否合格',
                                         dimensionId varchar(50) NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '量纲id',
                                         checkItemValue varchar(50) NULL DEFAULT NULL COMMENT '检查项值',
                                         allowLimit varchar(50) NULL DEFAULT NULL COMMENT '允许限值',
                                         orgId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '机构id',
                                         creator varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
                                         createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                         domainId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
                                         modifier varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
                                         modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                         PRIMARY KEY (id)
) ;

DROP TABLE IF EXISTS TB_PRO_SampleFolderEvaluate;
CREATE TABLE TB_PRO_SampleFolderEvaluate  (
                                              id varchar(50) NOT NULL COMMENT '主键',
                                              sampleFolderId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位id',
                                              testId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '测试项目id',
                                              qcRateValue varchar(255) NULL DEFAULT NULL COMMENT '判定结果',
                                              folderPass varchar(50) NULL DEFAULT '' COMMENT '是否合格',
                                              remark varchar(255) NULL DEFAULT NULL COMMENT '备注',
                                              resultEvaluate varchar(50) NULL DEFAULT '' COMMENT '结果评价',
                                              PRIMARY KEY (id)
);