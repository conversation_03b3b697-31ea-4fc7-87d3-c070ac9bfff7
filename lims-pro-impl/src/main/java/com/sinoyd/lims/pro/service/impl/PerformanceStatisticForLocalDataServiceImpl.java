package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.factory.quality.QualityBlank;
import com.sinoyd.base.factory.quality.QualityParallel;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.criteria.PerformanceStatisticForLocalDataCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.repository.PerformanceStatisticForLocalDataRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.PerformanceStatisticForLocalDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * PerformanceStatisticForLocalData操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
 @Service
public class PerformanceStatisticForLocalDataServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoPerformanceStatisticForLocalData,String,PerformanceStatisticForLocalDataRepository> implements PerformanceStatisticForLocalDataService {


    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Async
    @Override
    public void createLocalAnalyseStatistic(DtoReceiveSampleRecord record, List<DtoAnalyseData> analyseDataList,
                                            CurrentPrincipalUser principalContextUser) {
        List<DtoReceiveSampleRecord> records = new ArrayList<>();
        records.add(record);
        createLocalAnalyseStatistic(records, analyseDataList, principalContextUser);
    }

    @Async
    @Override
    public void createLocalAnalyseStatistic(List<DtoReceiveSampleRecord> records,
                                            List<DtoAnalyseData> analyseDataList,
                                            CurrentPrincipalUser principalContextUser
    ) {
        //重新赋值登录的账号信息
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(principalContextUser, null, authorities));
        List<String> recordIds = records.stream().map(DtoReceiveSampleRecord::getId).distinct().collect(Collectors.toList());
        //只处理现场数据
        analyseDataList = analyseDataList.stream().filter(AnalyseData::getIsCompleteField).collect(Collectors.toList());
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = new ArrayList<>();
        if (sampleIds.size() > 0) {
            samples = sampleRepository.findByIds(sampleIds);
        }
        //按检测类型+测试项目id进行分组处理
        List<DtoAnalyseData> newDataList = new ArrayList<>();
        for (DtoAnalyseData analyseData : analyseDataList) {
            //样品id
            String sampleId = analyseData.getSampleId();
            //检测类型id
            String sampleTypeId = UUIDHelper.GUID_EMPTY;
            //送样单id
            String receiveId = UUIDHelper.GUID_EMPTY;
            DtoSample sample = samples.stream().filter(p -> p.getId().equals(sampleId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(sample)) {
                sampleTypeId = sample.getSampleTypeId();
                receiveId = sample.getReceiveId();
            }
            DtoAnalyseData newData = analyseData;
            newData.setSampleTypeId(sampleTypeId);
            newData.setReceiveId(receiveId);
            newDataList.add(newData);
        }
        List<DtoPerformanceStatisticForLocalData> list = new ArrayList<>();
        newDataList.stream().collect(Collectors.groupingBy(PerformanceStatisticForLocalDataServiceImpl::fetchGroupKey, Collectors.toList())).forEach((key, dataList) -> {
            List<String> yKeys = Arrays.asList(key.split(","));
            //检测类型id
            String sampleTypeId = yKeys.get(0);
            //测试项目id
            String testId = yKeys.get(1);
            //检测单id
            String receiveId = yKeys.get(2);
            DtoReceiveSampleRecord receiveSampleRecord = records.stream().filter(p -> p.getId().equals(receiveId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(receiveSampleRecord)) {
                String recorderId = receiveSampleRecord.getRecorderId();
                String recordCode = receiveSampleRecord.getRecordCode();
                Date sendTime = receiveSampleRecord.getSendTime();
                Date samplingTime = receiveSampleRecord.getSamplingTime();
                DtoPerformanceStatisticForLocalData performanceStatisticForLocalData = new DtoPerformanceStatisticForLocalData();
                performanceStatisticForLocalData.setSampleTypeId(sampleTypeId);
                performanceStatisticForLocalData.setTestId(testId);
                performanceStatisticForLocalData.setRecorderId(recorderId);
                performanceStatisticForLocalData.setReceiveId(receiveId);
                performanceStatisticForLocalData.setSendTime(sendTime);
                performanceStatisticForLocalData.setSamplingTime(samplingTime);
                performanceStatisticForLocalData.setRedAnalyzeItemName(dataList.get(0).getRedAnalyzeItemName());
                performanceStatisticForLocalData.setRedAnalyzeMethodName(dataList.get(0).getRedAnalyzeMethodName());
                performanceStatisticForLocalData.setAnalyseItemId(dataList.get(0).getAnalyseItemId());
                performanceStatisticForLocalData.setAnalyzeMethodId(dataList.get(0).getAnalyzeMethodId());
                performanceStatisticForLocalData.setRecordCode(recordCode);
                //样品数
                Integer sampleNum = ((Long) dataList.stream().map(DtoAnalyseData::getSampleId).distinct().count()).intValue();
                //全程序空白数
                Integer localeGapNum = ((Long) dataList.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())
                        && p.getQcType().equals(new QualityBlank().qcTypeValue())).map(DtoAnalyseData::getSampleId).distinct().count()).intValue();
                Integer pxSampleNum = ((Long) dataList.stream().filter(p -> p.getQcType().equals(new QualityParallel().qcTypeValue()))
                        .map(DtoAnalyseData::getSampleId).distinct().count()).intValue();

                //有效数据
                Integer validNum = sampleNum + localeGapNum + pxSampleNum;
                performanceStatisticForLocalData.setSample(sampleNum);
                performanceStatisticForLocalData.setLocaleGap(localeGapNum);
                performanceStatisticForLocalData.setParallel(pxSampleNum);
                performanceStatisticForLocalData.setValid(validNum);
                performanceStatisticForLocalData.setOrgId(receiveSampleRecord.getOrgId());
                list.add(performanceStatisticForLocalData);
            }
        });
        if (recordIds.size() > 0) {
            //要清除原先送样单相关的数据
            repository.deleteByReceiveIdIn(recordIds);
        }
        if (list.size() > 0) {
            repository.save(list);
        }
    }

    @Override
    public void findByPage(PageBean<DtoPerformanceStatisticForLocalData> pb, BaseCriteria baseCriteria) {
        PerformanceStatisticForLocalDataCriteria performanceStatisticForLocalDataCriteria = (PerformanceStatisticForLocalDataCriteria) baseCriteria;
        pb.setSelect("select p ");
        pb.setEntityName("DtoPerformanceStatisticForLocalData p");
        pb.setSort(performanceStatisticForLocalDataCriteria.getSort());
        pb.setCondition(performanceStatisticForLocalDataCriteria.getCondition());
        comRepository.findByPage(pb, performanceStatisticForLocalDataCriteria.getValues());
        List<String> sampleTypeIds = pb.getData().stream().map(DtoPerformanceStatisticForLocalData::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = new ArrayList<>();
        if (sampleTypeIds.size() > 0) {
            sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        }
        Iterator<DtoPerformanceStatisticForLocalData> iterable = pb.getData().iterator();
        List<DtoPerformanceStatisticForLocalData> newList = new ArrayList<>();
        while (iterable.hasNext()) {
            DtoPerformanceStatisticForLocalData performanceStatisticForLocalData = iterable.next();
            String sampleTypeId = performanceStatisticForLocalData.getSampleTypeId();
            String sampleTypeName = "";
            DtoSampleType sampleType = sampleTypes.stream().filter(p -> p.getId().equals(sampleTypeId)).findFirst().orElse(null);
            if (StringUtil.isNotNull(sampleType)) {
                sampleTypeName = sampleType.getTypeName();
            }
            performanceStatisticForLocalData.setSampleTypeName(sampleTypeName);
            newList.add(performanceStatisticForLocalData);
        }
        pb.setData(newList);
    }

    /**
     * 获取总计行
     * @param baseCriteria
     * @return
     */
    @Override
    public  DtoPerformanceStatisticForLocalData findSumPerformanceStatistic(BaseCriteria baseCriteria) {
        PerformanceStatisticForLocalDataCriteria performanceStatisticForLocalDataCriteria = (PerformanceStatisticForLocalDataCriteria) baseCriteria;
        PageBean<Object[]> pb = new PageBean<>();
        pb.setSelect("select sum(p.sample),sum(p.localeGap),sum(p.parallel),sum(p.valid) ");
        pb.setEntityName("DtoPerformanceStatisticForLocalData p");
        pb.setCondition(performanceStatisticForLocalDataCriteria.getCondition());
        List<Object[]> list = comRepository.find(pb.getAutoQuery(), performanceStatisticForLocalDataCriteria.getValues());
        if (list.size() > 0) {
            Object[] objArray = list.get(0);
            return new DtoPerformanceStatisticForLocalData(Integer.valueOf(String.valueOf(objArray[0])),
                    Integer.valueOf(String.valueOf(objArray[1])),
                    Integer.valueOf(String.valueOf(objArray[2])),
                    Integer.valueOf(String.valueOf(objArray[3]))
            );
        }
        return new DtoPerformanceStatisticForLocalData();
    }

    /**
     * 进行数据的分组
     *
     * @param analyseData 分析数据
     * @return 得到分组的主键
     */
    private static String fetchGroupKey(DtoAnalyseData analyseData) {
        return analyseData.getSampleTypeId() + "," + analyseData.getTestId() + "," + analyseData.getReceiveId();
    }
}