package com.sinoyd.lims.pro.dto;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.entity.LogForRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoLogForRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_LogForRecord")
 @Data
 @DynamicInsert
 public  class DtoLogForRecord extends LogForRecord {
    private static final long serialVersionUID = 1L;

    /**
     * 默认的构造函数
     */
    public DtoLogForRecord() {

    }

    /**
     * 参数的构造函数
     * @param dtoLog 参数
     */
    public DtoLogForRecord(DtoLog dtoLog) {
        if (StringUtils.isNotNullAndEmpty(dtoLog.getId()) && !dtoLog.getId().equals(UUIDHelper.GUID_EMPTY)) {
            this.setId(dtoLog.getId());
        }
        if (StringUtils.isNotNullAndEmpty(dtoLog.getOperatorId())
                && !dtoLog.getOperatorId().equals(UUIDHelper.GUID_EMPTY)) {
            this.setOperatorId(dtoLog.getOperatorId());
            this.setOperatorName(dtoLog.getOperatorName());
        } else  if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            this.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            this.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
        }
        this.setOperateTime(dtoLog.getOperateTime());
        this.setOperateInfo(dtoLog.getOperateInfo());
        this.setNextOperatorId(dtoLog.getNextOperatorId());
        this.setNextOperatorName(dtoLog.getNextOperatorName());
        this.setLogType(dtoLog.getLogType());
        this.setObjectId(dtoLog.getObjectId());
        this.setObjectType(dtoLog.getObjectType());
        this.setComment(dtoLog.getComment());
        this.setOpinion(dtoLog.getOpinion());
        this.setRemark(dtoLog.getRemark());
    }
}