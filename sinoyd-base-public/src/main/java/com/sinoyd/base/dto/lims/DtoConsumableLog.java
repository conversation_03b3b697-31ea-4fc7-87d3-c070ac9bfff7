package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.ConsumableLog;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.math.BigDecimal;
import java.util.Date;


/**
 * DtoConsumableLog实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_ConsumableLog")
 @Data
 @DynamicInsert
 public  class DtoConsumableLog extends ConsumableLog {

   /**
    * 消耗品名称
    */
   @Transient
   private String consumableName;
   /**
    * 规格型号
    */
   @Transient
   private String specification;

    /**
     * 编号（本站编号）
     */
   @Transient
   private String codeInStation;

    /**
     * 生产批号
     */
    @Transient
    private String productionCode;

    /**
     * 单位
     */
   @Transient
   private String unit;

    /**
     * 提醒人id（管理人员）
     */
    @Transient
    private String sendWarnUserId;

    /**
     * 提醒人名称（管理人员名称）
     */
    @Transient
    private String sendWarnUserName;

    /**
     * 唯一性编号（标样编号）
     */
    @Transient
    private String consumableCode;

   public DtoConsumableLog() {

   }

    public DtoConsumableLog(BigDecimal amount, BigDecimal balance, Date occurrenceTime,
                            String consumingPersonsName, String remark, String consumableName, String specification) {
        this.setAmount(amount);
        this.setBalance(balance);
        this.setOccurrenceTime(occurrenceTime);
        this.setConsumingPersonsName(consumingPersonsName);
        this.setRemark(remark);
        this.setConsumableName(consumableName);
        this.setSpecification(specification);
    }

    public DtoConsumableLog(BigDecimal amount, BigDecimal balance, Date occurrenceTime,
                            String consumingPersonsName, String remark, String consumableName, String specification, String codeInStation) {
        this(amount,balance,occurrenceTime,consumingPersonsName,remark,consumableName,specification);
        this.setCodeInStation(codeInStation);
    }

    public DtoConsumableLog(BigDecimal amount, BigDecimal balance, Date occurrenceTime,
                            String consumingPersonsName, String remark, String consumableName, String specification, String codeInStation,
                            String consumableId) {
        this(amount,balance,occurrenceTime,consumingPersonsName,remark,consumableName,specification,codeInStation);
        this.setConsumableId(consumableId);
    }
 }