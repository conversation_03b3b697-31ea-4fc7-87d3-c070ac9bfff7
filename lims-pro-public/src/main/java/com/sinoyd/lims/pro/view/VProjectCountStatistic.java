package com.sinoyd.lims.pro.view;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 项目数量统计的视图
 */
@Entity
@Table(name = "VI_PRO_ProjectCountStatisticView")
@Data
public class VProjectCountStatistic {

    /**
     * 主键id
     */
    @Id
    private String id = UUIDHelper.NewID();

    /**
     * 项目id
     */
    private String projectId;


    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 项目类型父类
     */
    private String parentName;


    /**
     * 登记时间
     */
    private Date inceptTime;

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 组织机构id
     */
    private String orgId;


}
