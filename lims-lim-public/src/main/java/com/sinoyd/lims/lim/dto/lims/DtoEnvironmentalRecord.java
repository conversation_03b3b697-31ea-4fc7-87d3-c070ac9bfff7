package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.EnvironmentalRecord;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.ArrayList;
import java.util.List;


/**
 * DtoEnvironmentalRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_EnvironmentalRecord")
 @Data
 @DynamicInsert
 public  class DtoEnvironmentalRecord extends EnvironmentalRecord {
    /*
     * 仪器使用记录
     */
    @Transient
    private List<DtoInstrumentUseRecord> instrumentUseRecord = new ArrayList<>();

    /*
     * 仪器使用记录关联样品id集合
     */
    @Transient
    private List<String> sampleIds = new ArrayList<>();

    /*
     * 测试项目id集合
     */
    @Transient
    private List<String> testIds = new ArrayList<>();

    /*
     * 分析项目
     */
    @Transient
    private String redAnalyzeItemNames;

    /**
     * 使用记录id
     */
    @Transient
    private List<String> useRecordIds;

    /*
     * 批量设置时id集合
     */
    @Transient
    private List<String> batchSaveIds;

    /**
     * 复制源id （现场仪器复制）
     */
    @Transient
    private String sourceId;

    /**
     * 复制目标ids （现场仪器复制）
     */
    @Transient
    private List<String> targetIds;
}