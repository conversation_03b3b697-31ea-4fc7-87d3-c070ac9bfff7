package com.sinoyd.base.controller;

import com.sinoyd.base.dto.vo.LoginParamVO;
import com.sinoyd.base.service.ILoginAuthService;
import com.sinoyd.boot.auth.server.dto.AuthResponseDto;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 统一登录门户认证相关 Controller
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/28
 */
@RestController
@RequestMapping("/api/lims/basic/auth")
public class LoginAuthController extends ExceptionHandlerController<ILoginAuthService> {

    /**
     * 登录
     *
     * @param paramVO 登录参数
     * @return 响应结果
     */
    @PostMapping("/login")
    public RestResponse<AuthResponseDto> login(@RequestBody LoginParamVO paramVO) {
        RestResponse<AuthResponseDto> restResponse = new RestResponse<>();
        restResponse.setData(service.login(paramVO));
        return restResponse;
    }
}
