package com.sinoyd.lims.monitor.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.sinoyd.frame.base.entity.BaseEntity;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.validator.constraints.Length;


/**
 * Property2Point实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "Property2Point")
@Data
public class Property2Point implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 关联点位id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("关联点位id")
    private String fixedPointId;

    /**
     * 监测计划id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("监测计划id")
    private String propertyId;

    /**
     * 关联点位的点位编号
     */
    @Column(length = 100)
    @ApiModelProperty("关联点位的点位编号")
    @Length(message = "监测计划上关联的点位编号{validation.message.length}", max = 100)
    private String pointCode;

}