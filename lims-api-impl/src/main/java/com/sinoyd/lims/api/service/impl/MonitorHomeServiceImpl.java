package com.sinoyd.lims.api.service.impl;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoRole;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.lims.api.dto.DtoHomeNotice;
import com.sinoyd.lims.api.dto.DtoHomePage;
import com.sinoyd.lims.api.homeCount.task.HomeCountTask;
import com.sinoyd.lims.api.service.MonitorHomeService;
import com.sinoyd.lims.lim.criteria.NoticeCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoAppConfig;
import com.sinoyd.lims.lim.dto.lims.DtoNotice;
import com.sinoyd.lims.lim.service.AppConfigService;
import com.sinoyd.lims.lim.service.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 移动端首页
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
@Service
public class MonitorHomeServiceImpl implements MonitorHomeService {

    private AppConfigService appConfigService;

    private UserService userService;

    private HomeCountTask homeCountTask;

    private DocumentRepository documentRepository;

    private NoticeService noticeService;

    /**
     * 首页代办内容
     *
     * @return 首页内容
     */
    @Override
    public List<DtoHomePage> findHomePage() {
        //获取启动的配置代办
        List<DtoAppConfig> appConfigList = appConfigService.findAllConfig();
        //当前登录人所属角色
        List<String> roleIds = userService.findRoleById(PrincipalContextUser.getPrincipal().getUserId()).stream()
                .map(DtoRole::getRoleId).collect(Collectors.toList());
        //根据角色过滤显示代办内容
        appConfigList = appConfigList.stream().filter(p -> roleIds.containsAll(Arrays.asList
                (p.getRoleId().split(",").clone()))
                || !StringUtil.isNotEmpty(p.getRoleId())).collect(Collectors.toList());
        List<String> configIds = appConfigList.stream().map(DtoAppConfig::getId).collect(Collectors.toList());
        List<DtoDocument> documentList = documentRepository.findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(configIds,
                BaseCodeHelper.DOCUMENT_MONITOR_ICON);
        documentList.removeIf(DtoDocument::getIsDeleted);
        appConfigList.forEach(p -> {
            p.setAgentCount(homeCountTask.homeCount(p.getCode()));
            Optional<DtoDocument> document = documentList.stream().filter(doc -> doc.getFolderId().equals(p.getId())).findFirst();
            document.ifPresent(doc -> p.setIconPath(doc.getPath()));
        });
        Map<String, List<DtoAppConfig>> configMap = appConfigList.stream().collect(Collectors.groupingBy(DtoAppConfig::getType));
        List<DtoHomePage> homePageList = new ArrayList<>();
        for (String key : configMap.keySet()) {
            DtoHomePage homePage = new DtoHomePage();
            homePage.setConfigName(key);
            List<DtoAppConfig> configList = configMap.get(key);
            if (configList.size() > 0) {
                homePage.setConfigList(configMap.get(key).stream().sorted(Comparator.comparing(DtoAppConfig::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList()));
                homePage.setTypeNum(configList.get(0).getTypeOrderNum());
            }
            homePageList.add(homePage);
        }
        homePageList.sort(Comparator.comparing(DtoHomePage::getTypeNum, Comparator.reverseOrder()));
        return homePageList;
    }

    /**
     * 通知公告内容
     * @return 公告信息
     */
    @Override
    public DtoHomeNotice findNotice() {
        DtoHomeNotice homeNotice = new DtoHomeNotice();
        //获取公告内容
        PageBean<DtoNotice> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(10);
        pb.setSort(StringUtils.filterSQLCondition("isRelease+,isTop-,releaseTime-"));
        NoticeCriteria criteria = new NoticeCriteria();
        noticeService.findByPage(pb, criteria);
        homeNotice.setNoticeList(pb.getData());
        //最新公告数量
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        //当前时间
        criteria.setTimeEnd(DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR));
        //一个月前
        calendar.set(Calendar.MONTH, -1);
        criteria.setTimeStart(DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR));
        noticeService.findByPage(pb, criteria);
        homeNotice.setNewCount(pb.getRowsCount());
        return homeNotice;
    }

    @Override
    public List<DtoNotice> findNoticePage(PageBean<DtoNotice> pageBean, BaseCriteria criteria) {
        pageBean.setSort(StringUtils.filterSQLCondition("isRelease+,isTop-,releaseTime-"));
        noticeService.findByPage(pageBean, criteria);
        return pageBean.getData();
    }

    @Override
    public DtoNotice findNoticeById(String id) {
        return noticeService.findOne(id);
    }

    @Autowired
    @Lazy
    public void setAppConfigService(AppConfigService appConfigService) {
        this.appConfigService = appConfigService;
    }

    @Autowired
    @Lazy
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    public void setHomeCountTask(HomeCountTask homeCountTask) {
        this.homeCountTask = homeCountTask;
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    @Lazy
    public void setNoticeService(NoticeService noticeService) {
        this.noticeService = noticeService;
    }
}
