package com.sinoyd.lims.lim.service;

import com.sinoyd.lims.lim.dto.rcc.DtoCost;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Collection;
import java.util.List;


/**
 * Cost操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/9
 * @since V100R001
 */
public interface CostService extends IBaseJpaService<DtoCost, String> {
    /**
     * 根据检测类型返回检测费配置
     *
     * @param sampleTypeId 检测类型id
     * @param analyzeItemKey           分析项目、拼音
     * @param analyzeMethodKey         分析方法、标准编号
     * @param cert               检测资质
     * @return 根据检测类型返回检测费配置
     */
    List<DtoCost> findBySampleType(String sampleTypeId, String analyzeItemKey,String analyzeMethodKey,Integer cert);

    /**
     * 根据测试项目id集合返回检测费配置
     *
     * @param testIds 测试项目id集合
     * @return 返回测试项目id集合的检测费配置
     */
    List<DtoCost> findByTestIdIn(Collection<String> testIds);
}