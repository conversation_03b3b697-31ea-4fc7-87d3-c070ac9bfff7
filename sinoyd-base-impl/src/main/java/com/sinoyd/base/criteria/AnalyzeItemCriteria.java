package com.sinoyd.base.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分析项目管理查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeItemCriteria extends BaseCriteria {

    private String key;// 关键字：分析项目名称，拼音缩写和全拼

    /**
     * 是否精确查询
     */
    private Boolean isPrecision = Boolean.FALSE;

    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNull(isPrecision) || !isPrecision){
            // 关键字模糊查找
            if (StringUtils.isNotNullAndEmpty(this.key)) {
                condition.append(" and (analyzeItemName like :key or fullPinYin like :key or pinYin like :key)");
                values.put("key", "%" + this.key + "%");
            }
        }else {
            if (StringUtils.isNotNullAndEmpty(this.key)) {
                condition.append(" and (analyzeItemName like :key or fullPinYin = :key or pinYin = :key)");
                values.put("key",  this.key );
            }
        }

        condition.append(" and isDeleted = 0 ");

        return condition.toString();
    }
}