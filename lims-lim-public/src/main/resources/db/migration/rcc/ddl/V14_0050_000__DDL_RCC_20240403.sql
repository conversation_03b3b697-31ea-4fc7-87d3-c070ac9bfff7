-- 污染源点位测试项目关联表，新增频次字段
DROP PROCEDURE IF EXISTS `add_col`;
DELIMITER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_MONITOR_FixedPoint2Test' AND column_name = 'samplePeriod')) THEN
ALTER TABLE TB_MONITOR_FixedPoint2Test ADD COLUMN samplePeriod INT (11) NOT NULL DEFAULT 1 COMMENT '样次';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;