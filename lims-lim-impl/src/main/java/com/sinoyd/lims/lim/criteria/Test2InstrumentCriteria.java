package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 使用仪器
 * <AUTHOR>
 * @version v1.0.0 2019/5/14
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Test2InstrumentCriteria extends BaseCriteria{

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        return condition.toString();
    }

 
}