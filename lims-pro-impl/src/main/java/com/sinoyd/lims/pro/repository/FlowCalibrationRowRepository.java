package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoFlowCalibrationRow;

import java.util.Collection;
import java.util.List;

/**
 * FlowCalibrationRow数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/14
 */
public interface FlowCalibrationRowRepository extends IBaseJpaRepository<DtoFlowCalibrationRow,String> {

    /**
     * 根据校准记录标识集合查询
     * @param flowCalibrationIds 校准记录标识集合
     * @return List<DtoFlowCalibrationRow>
     */
    List<DtoFlowCalibrationRow> findByFlowCalibrationIdIn(Collection<String> flowCalibrationIds);
}
