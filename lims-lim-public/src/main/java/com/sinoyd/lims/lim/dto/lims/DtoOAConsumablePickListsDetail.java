package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.OAConsumablePickListsDetail;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoOAConsumablePickListsDetail实体
 *
 * <AUTHOR>
 * @version V1.0.0 2020/4/2
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_OAConsumablePickListsDetail")
@Data
@DynamicInsert
public class DtoOAConsumablePickListsDetail extends OAConsumablePickListsDetail {
    private static final long serialVersionUID = 1L;

    /**
     * 审批任务状态
     */
    @Transient
    private Integer oaTaskStatus;
}