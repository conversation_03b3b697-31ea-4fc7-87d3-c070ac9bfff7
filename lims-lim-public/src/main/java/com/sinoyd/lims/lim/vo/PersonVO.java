package com.sinoyd.lims.lim.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class PersonVO implements Serializable {

    /**
     * 中文名称
     */
    @Excel(name = "姓名",needMerge = true,orderNum = "20",width = 11)
    private String chineseName;

    /**
     * 编号
     */
    @Excel(name = "编号",needMerge = true,orderNum = "30",width = 11)
    private String userNo;

    /**
     * 所属科室（Guid）
     */
    @Excel(name = "所属科室",needMerge = true,orderNum = "40",width = 23)
    private String deptId;

    /**
     * 职务（常量（Guid）：LIM_Post）
     */
    @Excel(name = "职务",needMerge = true,orderNum = "50",width = 17)
    private String postId;

    /**
     * 职称（常量（Guid）：LIM_TechnicalTitle）
     */
    @Excel(name = "职称",needMerge = true,orderNum = "60",width = 24)
    private String technicalTitleId;

    /**
     * 职称获得日期
     */
    @Excel(name = "职称获得日期",needMerge = true,orderNum = "70",width = 17)
    private String technicalTitleDate;

    /**
     * 性别（枚举EnumSex：  1代表男   2代表女）
     */
    @Excel(name = "性别",needMerge = true,orderNum = "80",width = 11)
    private String sex;

    /**
     * 英文名
     */
    @Excel(name = "英文名",needMerge = true,orderNum = "90",width = 20)
    private String enName;

    /**
     * 身份证
     */
    @Excel(name = "身份证号",needMerge = true,orderNum = "100",width = 20)
    private String idCard;

    /**
     * 状态（枚举EnumPersonStatus:1代表在职 2代表离职 3代表休假）
     */
    @Excel(name = "人员状态",needMerge = true,orderNum = "110",width = 25)
    private String status;

    /**
     * 入党日期
     */
    @Excel(name = "入党日期",needMerge = true,orderNum = "120",width = 17)
    private String joinPartyDate;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期",needMerge = true,orderNum = "130",width = 17)
    private String birthDay;

    /**
     * 政治面貌
     */
    @Excel(name = "政治面貌",needMerge = true,orderNum = "140",width = 17)
    private String politicalFace;

    /**
     * 户籍所在地
     */
    @Excel(name = "户籍所在地",needMerge = true,orderNum = "150",width = 17)
    private String homeTown;

    /**
     * 民族
     */
    @Excel(name = "民族",needMerge = true,orderNum = "160",width = 17)
    private String volk;

    /**
     * 籍贯
     */
    @Excel(name = "籍贯",needMerge = true,orderNum = "170",width = 11)
    private String nativePlace;

    /**
     * 档案所在地
     */
    @Excel(name = "档案所在地",needMerge = true,orderNum = "180",width = 11)
    private String archivesPlace;

    /**
     * 所学专业
     */
    @Excel(name = "所学专业",needMerge = true,orderNum = "190",width = 11)
    private String specialty;

    /**
     * 国籍
     */
    @Excel(name = "国籍",needMerge = true,orderNum = "200",width = 11)
    private String nation;

    /**
     * 学历（常量（Guid）：LIM_Degree：大专、本科、硕士研究生、博士研究生）
     */
    @Excel(name = "学历",needMerge = true,orderNum = "210",width = 14)
    private String degree;

    /**
     * 毕业院校
     */
    @Excel(name = "毕业院校",needMerge = true,orderNum = "220",width = 26)
    private String school;

    /**
     * 出生地
     */
    @Excel(name = "出生地",needMerge = true,orderNum = "230",width = 26)
    private String birthPlace;

    /**
     * 手机
     */
    @Excel(name = "手机",needMerge = true,orderNum = "240",width = 17)
    private String mobile;

    /**
     * 固定电话
     */
    @Excel(name = "固定电话",needMerge = true,orderNum = "250",width = 17)
    private String homeTel;

    /**
     * 个人邮箱
     */
    @Excel(name = "个人邮箱",needMerge = true,orderNum = "260",width = 20)
    private String email;

    /**
     * 住址
     */
    @Excel(name = "家庭住址",needMerge = true,orderNum = "270",width = 41)
    private String homeAddress;

    /**
     * 紧急联络人
     */
    @Excel(name = "紧急联络人",needMerge = true,orderNum = "280",width = 14)
    private String emergentLinkMan;

    /**
     * 联络方法
     */
    @Excel(name = "联络方式",needMerge = true,orderNum = "290",width = 14)
    private String contactMethod;

    /**
     * 入职时间
     */
    @Excel(name = "入职日期",needMerge = true,orderNum = "300",width = 17)
    private String joinCompanyTime;

    /**
     * 入职年限
     */
    @Excel(name = "入职年限",needMerge = true,orderNum = "310",width = 17)
    private String workStartTime;

    /**
     * 在岗时间
     */
    @Excel(name = "在岗时间",needMerge = true,orderNum = "320",width = 11,type = 10)
    private BigDecimal yearsInThePosition;

    /**
     * 离职时间
     */
    @Excel(name = "离职日期",needMerge = true,orderNum = "330",width = 17)
    private String leaveCompanyTime;

    /**
     * 准入证书
     */
    @Excel(name = "准入证书",needMerge = true,orderNum = "340",width = 26)
    private String certificateNO;

    /**
     * 准入证书获取时间
     */
    @Excel(name = "准入证书获取时间",needMerge = true,orderNum = "350",width = 17)
    private String certificateDate;

    /**
     * 开始工作时间
     */
    @Excel(name = "开始工作时间",needMerge = true,orderNum = "360",width = 17)
    private String beginWorkTime;

    /**
     * 关键岗位
     */
    @Excel(name = "关键岗位",needMerge = true,orderNum = "370",width = 26,type = 10)
    private Integer keyPost;

    /**
     * 技术能力
     */
    @Excel(name = "技术能力",needMerge = true,orderNum = "380",width = 26)
    private String tecCompetence;

    /**
     * 从事检测和/或校准工作方面的职责
     */
    @Excel(name = "从事检测和/或校准工作方面的职责",needMerge = true,orderNum = "390",width = 26)
    private String testResWork;

    /**
     * 检测和/或校准策划和结果评价方面的职责
     */
    @Excel(name = "检测和/或校准策划和结果评价方面的职责",needMerge = true,orderNum = "400",width = 26)
    private String testResEvaluation;

    /**
     * 提交意见和解释的职责
     */
    @Excel(name = "提交意见和解释的职责",needMerge = true,orderNum = "410",width = 26)
    private String submissionDuty;

    /**
     * 方法改进新方法制定和确认方面的职责
     */
    @Excel(name = "方法改进新方法制定和确认方面的职责",needMerge = true,orderNum = "420",width = 26)
    private String developMethodRes;

    /**
     * 所需的专业知识和经验
     */
    @Excel(name = "所需的专业知识和经验",needMerge = true,orderNum = "430",width = 26)
    private String experienceRequired;

    /**
     * 资格和培训计划
     */
    @Excel(name = "资格和培训计划",needMerge = true,orderNum = "440",width = 26)
    private String trainingPrograms;

    /**
     * 管理职责
     */
    @Excel(name = "管理职责",needMerge = true,orderNum = "450",width = 26)
    private String manageRes;

    /**
     * 备注说明
     */
    @Excel(name = "备注说明",needMerge = true,orderNum = "460",width = 26)
    private String remark;

    @ExcelCollection(name = "资历证书",orderNum = "470")
    private List<PersonCertVO> certs = new ArrayList<>();

    public PersonVO() {
    }

    public PersonVO(DtoPerson person) {
        setChineseName(person.getCName());
        setUserNo(person.getUserNo());
        setPostId(UUIDHelper.GUID_EMPTY.equals(person.getPostId())?"":person.getPostId());
        setTechnicalTitleId(UUIDHelper.GUID_EMPTY.equals(person.getTechnicalTitleId())?"":person.getTechnicalTitleId());
        setTechnicalTitleDate("1753-01-01".equals(DateUtil.dateToString(person.getTechnicalTitleDate(),DateUtil.YEAR))?"":DateUtil.dateToString(person.getTechnicalTitleDate(),DateUtil.YEAR));
        setSex(EnumLIM.EnumSex.getName(person.getSex()));
        setEnName(person.getEName());
        setIdCard(person.getIdCard());
        setStatus(EnumLIM.EnumPersonStatus.getName(person.getStatus()));
        setJoinPartyDate("1753-01-01".equals(DateUtil.dateToString(person.getJoinPartyDate(),DateUtil.YEAR))?"":DateUtil.dateToString(person.getJoinPartyDate(),DateUtil.YEAR));
        setBirthDay("1753-01-01".equals(DateUtil.dateToString(person.getBirthDay(),DateUtil.YEAR))?"":DateUtil.dateToString(person.getBirthDay(),DateUtil.YEAR));
        setPoliticalFace(person.getPoliticalFace());
        setHomeTown(person.getHomeTown());
        setVolk(person.getVolk());
        setNativePlace(person.getNativePlace());
        setArchivesPlace(person.getArchivesPlace());
        setSpecialty(UUIDHelper.GUID_EMPTY.equals(person.getSpecialty())?"":person.getSpecialty());
        setNation(person.getNation());
        setDegree(UUIDHelper.GUID_EMPTY.equals(person.getDegree())?"":person.getDegree());
        setSchool(person.getSchool());
        setBirthPlace(person.getBirthPlace());
        setMobile(person.getMobile());
        setHomeTel(person.getHomeTel());
        setEmail(person.getEmail());
        setHomeAddress(person.getHomeAddress());
        setEmergentLinkMan(person.getEmergentLinkMan());
        setContactMethod(person.getContactMethod());
        setJoinCompanyTime("1753-01-01".equals(DateUtil.dateToString(person.getJoinCompanyTime(),DateUtil.YEAR))?"":DateUtil.dateToString(person.getJoinCompanyTime(),DateUtil.YEAR));
        setWorkStartTime("1753-01-01".equals(DateUtil.dateToString(person.getWorkStartTime(),DateUtil.YEAR))?"":DateUtil.dateToString(person.getWorkStartTime(),DateUtil.YEAR));
        setYearsInThePosition(person.getYearsInThePosition());
        setLeaveCompanyTime("1753-01-01".equals(DateUtil.dateToString(person.getLeaveCompanyTime(),DateUtil.YEAR))?"":DateUtil.dateToString(person.getLeaveCompanyTime(),DateUtil.YEAR));
        setCertificateNO(person.getCertificateNO());
        setCertificateDate("1753-01-01".equals(DateUtil.dateToString(person.getCertificateDate(),DateUtil.YEAR))?"":DateUtil.dateToString(person.getCertificateDate(),DateUtil.YEAR));
        setBeginWorkTime("1753-01-01".equals(DateUtil.dateToString(person.getBeginWorkTime(),DateUtil.YEAR))?"":DateUtil.dateToString(person.getBeginWorkTime(),DateUtil.YEAR));
        setKeyPost(person.getKeyPost());
        setTecCompetence(person.getTecCompetence());
        setTestResWork(person.getTestResWork());
        setTestResEvaluation(person.getTestResEvaluation());
        setSubmissionDuty(person.getSubmissionDuty());
        setDevelopMethodRes(person.getDevelopMethodRes());
        setExperienceRequired(person.getExperienceRequired());
        setTrainingPrograms(person.getTrainingPrograms());
        setManageRes(person.getManageRes());
        setRemark(person.getRemark());
        if(StringUtil.isNotNull(person.getCerts()) && StringUtil.isNotEmpty(person.getCerts())) {
            for(DtoPersonCert personCert : person.getCerts()) {
                PersonCertVO certVO = new PersonCertVO();
                certVO.setCertName(personCert.getCertName());
                certVO.setCertCode(personCert.getCertCode());
                List<String> certTypes = Arrays.asList(personCert.getCertType().split(","));
                List<String> certTypeNames = new ArrayList<>();
                certTypes.forEach(c->{
                    if("1".equals(c)){
                        certTypeNames.add("水");
                    } else if("2".equals(c)) {
                        certTypeNames.add("气");
                    } else if("3".equals(c)) {
                        certTypeNames.add("声");
                    } else if("4".equals(c)) {
                        certTypeNames.add("土");
                    } else if("5".equals(c)) {
                        certTypeNames.add("其他");
                    }
                });
                certVO.setCertType(String.join(",",certTypeNames));
                certVO.setIssueCertTime("1753-01-01".equals(DateUtil.dateToString(personCert.getIssueCertTime(),DateUtil.YEAR))?"":DateUtil.dateToString(personCert.getIssueCertTime(),DateUtil.YEAR));
                certVO.setCertEffectiveTime("1753-01-01".equals(DateUtil.dateToString(personCert.getCertEffectiveTime(),DateUtil.YEAR))?"":DateUtil.dateToString(personCert.getCertEffectiveTime(),DateUtil.YEAR));
                this.certs.add(certVO);
            }
        } else {
            this.certs.add(new PersonCertVO());
        }
    }



}
