CREATE  TRIGGER "AFTER_UPDATE_TESTEXPAND"
    AFTER  UPDATE
    ON "TB_LIM_TESTEXPAND"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    if new.testId != old.testId or (new.testId is null and old.testId is not null) or
       (new.testId is not null and old.testId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'testId',
        old.testId, new.testId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.sampleTypeId != old.sampleTypeId or (new.sampleTypeId is null and old.sampleTypeId is not null) or
       (new.sampleTypeId is not null and old.sampleTypeId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'sampleTypeId',
        old.sampleTypeId, new.sampleTypeId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.mostSignificance != old.mostSignificance or
       (new.mostSignificance is null and old.mostSignificance is not null) or
       (new.mostSignificance is not null and old.mostSignificance is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'mostSignificance', old.mostSignificance, new.mostSignificance, new.orgId,
        '00000000-0000-0000-0000-000000000000');
END IF;
if new.mostDecimal != old.mostDecimal or (new.mostDecimal is null and old.mostDecimal is not null) or
       (new.mostDecimal is not null and old.mostDecimal is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'mostDecimal',
        old.mostDecimal, new.mostDecimal, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.examLimitValue != old.examLimitValue or (new.examLimitValue is null and old.examLimitValue is not null) or
       (new.examLimitValue is not null and old.examLimitValue is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'examLimitValue',
        old.examLimitValue, new.examLimitValue, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.dimensionId != old.dimensionId or (new.dimensionId is null and old.dimensionId is not null) or
       (new.dimensionId is not null and old.dimensionId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'dimensionId',
        old.dimensionId, new.dimensionId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.examLimitValueLess != old.examLimitValueLess or
       (new.examLimitValueLess is null and old.examLimitValueLess is not null) or
       (new.examLimitValueLess is not null and old.examLimitValueLess is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'examLimitValueLess', old.examLimitValueLess, new.examLimitValueLess, new.orgId,
        '00000000-0000-0000-0000-000000000000');
END IF;
if new.samplePeriod != old.samplePeriod or (new.samplePeriod is null and old.samplePeriod is not null) or
       (new.samplePeriod is not null and old.samplePeriod is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'samplePeriod',
        old.samplePeriod, new.samplePeriod, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if new.lowerLimit != old.lowerLimit or (new.lowerLimit is null and old.lowerLimit is not null) or
       (new.lowerLimit is not null and old.lowerLimit is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_testexpand', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'lowerLimit',
        old.lowerLimit, new.lowerLimit, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
end;
