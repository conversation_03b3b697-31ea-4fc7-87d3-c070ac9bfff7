package com.sinoyd.lims.lim.service.transform;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.data.mapper.ParamsFormulaMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.entity.ParamsFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsPartFormulaRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.verify.TransformParamsFormulaVerifyHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目数据迁移测试项目公式导入策略类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
@Component
@Order(90)
public class ParamsFormulaStrategy implements TransformImportStrategy {
    /**
     * sheet页序号
     */
    private static final int sheetIndex = 5;

    private ParamsFormulaRepository paramsFormulaRepository;

    private TransformParamsFormulaVerifyHandler transformParamsFormulaVerifyHandler;

    private ParamsFormulaMapper paramsFormulaMapper;

    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    private CommonRepository commonRepository;

    @Override
    @Transactional
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception {
        //参数初始化
        handleInit();
        params.setStartSheetIndex(sheetIndex);
        params.setVerifyHandler(transformParamsFormulaVerifyHandler);
        //获取校验结果集
        ExcelImportResult<DtoExportParamsFormula> result = ExcelImportUtil.importExcelMore(inputStream, DtoExportParamsFormula.class, params);
        // 检验失败抛出异常并返回错误文档
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(sheetIndex, "导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        //构建可保存数据
        List<DtoParamsFormula> waitSaveList = new ArrayList<>();
        buildRightData(result, waitSaveList);
        //数据保存
        paramsFormulaRepository.save(waitSaveList);
    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {
        List<DtoExportParamsFormula> paramsFormulaList = testDependentData.getParamsFormulaList();
        if (StringUtil.isNotEmpty(paramsFormulaList)) {
            List<DtoExportTest> testList = exportData.getTestList();
            List<String> exportTestIds = StringUtil.isNotEmpty(testList) ? testList.stream().map(DtoExportTest::getId).collect(Collectors.toList()) : new ArrayList<>();
            // 导入数据处理，类型转化
            List<DtoParamsFormula> paramsFormulas = paramsFormulaList.stream().filter(p -> !exportTestIds.contains(p.getObjectId()))
                    .map(p -> {
                        DtoParamsFormula dtoParamsFormula = paramsFormulaMapper.toDtoParamsFormula(p);
                        DtoBaseData sampleTypeBindMapOrDefault = sampleTypeBindMap.getOrDefault(dtoParamsFormula.getSampleTypeId(), new DtoBaseData());
                        dtoParamsFormula.setSampleTypeId(sampleTypeBindMapOrDefault.getTargetId());
                        return dtoParamsFormula;
                    }).collect(Collectors.toList());
            importTestTemp.setParamsFormulaTemps(paramsFormulas);
            // 导出数据处理
            if (StringUtil.isNotEmpty(exportTestIds)) {
                List<DtoExportParamsFormula> exportParamsFormulas = paramsFormulaList.stream().filter(p -> exportTestIds.contains(p.getObjectId())).collect(Collectors.toList());
                exportData.setParamsFormulaList(exportParamsFormulas);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoParamsFormula> paramsFormulaTemps = importTestTemp.getParamsFormulaTemps();
        if (StringUtil.isNotEmpty(paramsFormulaTemps)) {
            // 删除测试项目相同公式相同的捞数据
            deletedOldData(paramsFormulaTemps);
            //已同步记录数
            int i = 0;
            for (DtoParamsFormula dtoParamsFormula : paramsFormulaTemps) {
                paramsFormulaRepository.save(dtoParamsFormula);
                webSocketServer.sendMessage(getMessage(paramsFormulaTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(paramsFormulaTemps.size(), 0));
        }
    }

    /**
     * 删除测试项目相同公式相同的捞数据
     *
     * @param paramsFormulaTemps 导入数据集合
     */
    private void deletedOldData(List<DtoParamsFormula> paramsFormulaTemps) {
        List<String> testIds = paramsFormulaTemps.stream().map(DtoParamsFormula::getObjectId).distinct().collect(Collectors.toList());
        List<DtoParamsFormula> paramsFormulaList = paramsFormulaRepository.findByObjectIdsDeleted(testIds);
        List<String> deleteIds = new ArrayList<>();
        List<String> deleteIds2 = new ArrayList<>();
        for (DtoParamsFormula formula : paramsFormulaList) {

            Optional<DtoParamsFormula> paramsFormula = paramsFormulaTemps.stream().filter(p -> p.getId().equals(formula.getId())).findFirst();
            if (paramsFormula.isPresent()){
                deleteIds2.add(formula.getId());
            }else {
                deleteIds.add(formula.getId());
            }
        }
        if (StringUtil.isNotEmpty(deleteIds)) {
            paramsFormulaRepository.deleteByIdIn(deleteIds);
            deleteIds.addAll(deleteIds2);
            paramsPartFormulaRepository.deleteByObjIdIn(deleteIds);
            paramsTestFormulaRepository.deleteByObjIdIn(deleteIds);
        }

    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.计算公式表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.计算公式表.getSource();
    }

    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.计算公式表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        List<DtoExportParamsFormula> exportParamsFormulas = testDependentData.getParamsFormulaList();
        List<DtoImportCheck> importChecks = new ArrayList<>();
        List<DtoDataCheck> paramsFormulaCheckList = new ArrayList<>();
        if (StringUtil.isNotEmpty(exportParamsFormulas)){
            List<String> testIds = exportParamsFormulas.stream().map(DtoExportParamsFormula::getObjectId).distinct().collect(Collectors.toList());
            List<DtoParamsFormula> paramsFormulaList = paramsFormulaRepository.findByObjectIdsDeleted(testIds);
            List<DtoExportTest> testList = testDependentData.getTestList();
            for (DtoExportParamsFormula exportParamsFormula : exportParamsFormulas) {
                List<DtoParamsFormula> formulaList = paramsFormulaList.stream().filter(p -> p.getObjectId().equals(exportParamsFormula.getObjectId())
                        && p.getFormula().equals(exportParamsFormula.getFormula())).collect(Collectors.toList());

                DtoExportTest exportTest = testList.stream().filter(p -> p.getId().equals(exportParamsFormula.getObjectId())).findFirst().orElse(new DtoExportTest());
                DtoDataCheck dtoDataCheck = new DtoDataCheck();
                Map<String, Object> otherField = new HashMap<>();
                otherField.put("redAnalyzeItemName", exportTest.getRedAnalyzeItemName());
                otherField.put("redAnalyzeMethodName", exportTest.getRedAnalyzeMethodName());
                otherField.put("redCountryStandard", exportTest.getRedCountryStandard());
                otherField.put("sampleType", exportTest.getSampleTypeId());
                otherField.put("formula", exportParamsFormula.getFormula());
                dtoDataCheck.setOtherField(otherField);
                if (StringUtil.isNotEmpty(formulaList)){
                    dtoDataCheck.setType(BASE_DATA_TYPE[1]);
                }else {
                    dtoDataCheck.setType(BASE_DATA_TYPE[0]);
                }
                paramsFormulaCheckList.add(dtoDataCheck);
            }
        }
        List<DtoDataCheck> existsList = paramsFormulaCheckList.stream().filter(p -> BASE_DATA_TYPE[1].equals(p.getType())).collect(Collectors.toList());
        List<DtoDataCheck> noExistsList = paramsFormulaCheckList.stream().filter(p -> BASE_DATA_TYPE[0].equals(p.getType())).collect(Collectors.toList());
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.计算公式表.getCheckItem(),
                BASE_DATA_TYPE[1], existsList.size(), "先删除原有公式（含扩展公式），再执行新增操作，导入时将进行插入。",
                existsList));
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.计算公式表.getCheckItem(),
                BASE_DATA_TYPE[0], noExistsList.size(), "新增测试项目公式，导入时将进行插入。",
                noExistsList));

        return importChecks;
    }

    /**
     * 校验容器初始化
     */
    private void handleInit() {
        transformParamsFormulaVerifyHandler = new TransformParamsFormulaVerifyHandler();
        transformParamsFormulaVerifyHandler.setRepoDataList(paramsFormulaRepository.findAll());
        transformParamsFormulaVerifyHandler.setSheetExistDataList(new ArrayList<>());
    }

    /**
     * 构建可保存数据
     *
     * @param result       导入结果集
     * @param waitSaveList 待保存结果容器
     */
    private void buildRightData(ExcelImportResult<DtoExportParamsFormula> result, List<DtoParamsFormula> waitSaveList) {
        List<DtoExportParamsFormula> importList = result.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        for (DtoExportParamsFormula exportParamsFormula : importList) {
            DtoParamsFormula dtoParamsFormula = new DtoParamsFormula();
            BeanUtils.copyProperties(exportParamsFormula, dtoParamsFormula);
            waitSaveList.add(dtoParamsFormula);
        }
    }

    @Autowired
    public void setParamsFormulaRepository(ParamsFormulaRepository paramsFormulaRepository) {
        this.paramsFormulaRepository = paramsFormulaRepository;
    }

    @Autowired
    public void setParamsFormulaMapper(ParamsFormulaMapper paramsFormulaMapper) {
        this.paramsFormulaMapper = paramsFormulaMapper;
    }

    @Autowired
    public void setParamsPartFormulaRepository(ParamsPartFormulaRepository paramsPartFormulaRepository) {
        this.paramsPartFormulaRepository = paramsPartFormulaRepository;
    }

    @Autowired
    public void setParamsTestFormulaRepository(ParamsTestFormulaRepository paramsTestFormulaRepository) {
        this.paramsTestFormulaRepository = paramsTestFormulaRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }
}
