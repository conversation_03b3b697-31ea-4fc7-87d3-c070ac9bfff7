package com.sinoyd.lims.monitor.service.impl;


import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.monitor.dto.customer.DtoImportFixedExpand;
import com.sinoyd.lims.monitor.dto.customer.DtoImportFixedPointEQ;
import com.sinoyd.lims.monitor.dto.customer.DtoImportFixedPointRS;
import com.sinoyd.lims.monitor.service.DownLoadFixedPointTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 环境质量/污染源导入模板下载实现
 * <AUTHOR>
 * @version V1.0.0 2022/12/08
 * @since V100R001
 */
@Service
@Slf4j
public class DownLoadFixedPointTemplateServiceImpl implements DownLoadFixedPointTemplateService {

    private ImportUtils importUtils;

    private CodeService codeService;

    /**
     * 下载环境质量点位导入模板
     *
     * @param response      响应流
     * @param fileName      文件名
     */
    @Override
    public void downLoadEQ(HttpServletResponse response, String fileName) {
        Map<String,String> sheetNames = new HashMap<>();
        //设置导出的Sheet名称
        sheetNames.put(LimConstants.ImportConstants.FIRST_SHEET_NAME,"环境质量点位数据");
        sheetNames.put(LimConstants.ImportConstants.SECOND_SHEET_NAME,"点位类型");
        //获取点位环境质量的点位类型
        List<DtoImportFixedExpand> pointTypes = findAllPointType(LimConstants.ImportConstants.FIXED_POINT_TYPE_HJ);
        //设置点位类型数据
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportFixedPointEQ.class, DtoImportFixedExpand.class, new ArrayList<>(), pointTypes);
        //下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName,response,workBook);
    }

    /**
     * 下载污染源点位导入模板
     *
     * @param response      响应流
     * @param fileName      文件名
     */
    @Override
    public void downLoadRS(HttpServletResponse response, String fileName) {
        Map<String,String> sheetNames = new HashMap<>();
        //设置导出的Sheet名称
        sheetNames.put(LimConstants.ImportConstants.FIRST_SHEET_NAME,"污染源点位数据");
        sheetNames.put(LimConstants.ImportConstants.SECOND_SHEET_NAME,"点位类型");
        //获取点位环境质量的点位类型
        List<DtoImportFixedExpand> pointTypes = findAllPointType(LimConstants.ImportConstants.FIXED_POINT_TYPE_WR);
        //设置点位类型数据
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportFixedPointRS.class, DtoImportFixedExpand.class, new ArrayList<>(), pointTypes);
        //下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName,response,workBook);
    }

    /**
     * 获取拓展的点位类型
     *
     * @param codeValue 字典编码
     * @return 点位类型数据
     */
    private List<DtoImportFixedExpand> findAllPointType(String codeValue){
        List<DtoImportFixedExpand> fixedExpands = new ArrayList<>();
        List<DtoCode> codes = codeService.findCodes(codeValue);
        if (StringUtil.isNotEmpty(codes)){
            for (DtoCode code : codes) {
                DtoImportFixedExpand expand = new DtoImportFixedExpand();
                expand.setPointType(code.getDictName());
                fixedExpands.add(expand);
            }
        }
        return fixedExpands;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }
}
