package com.sinoyd.lims.strategy.context.schemeSynchronization;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTemp;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SampleContext {

    /**
     * 修改样品内容
     *
     * @param operateType               修改类型
     * @param samplingFrequencyTempList 修改样品内容
     */
    void synchronizationSample(Integer operateType, List<DtoSamplingFrequencyTemp> samplingFrequencyTempList);
}
