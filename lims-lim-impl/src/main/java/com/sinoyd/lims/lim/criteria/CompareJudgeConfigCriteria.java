package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * CompareJudgeConfig查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/06/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CompareJudgeConfigCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    private String compareId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.compareId)) {
            condition.append(" and a.testId = :compareId");
            values.put("compareId", this.compareId);
        }
        return condition.toString();
    }

}
