package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.lims.DtoContract;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.ContractRepository;
import com.sinoyd.lims.lim.service.ContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 合同管理接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
@Service
public class ContractServiceImpl extends BaseJpaServiceImpl<DtoContract, String, ContractRepository>
        implements ContractService {

    @Autowired
    @Lazy
    private EnterpriseService enterpriseService;


    @Autowired
    private RedisTemplate redisTemplate;

    /***
     * 分页查询合同列表
     *
     * @param page
     * @param criteria
     */
    @Override
    public void findByPage(PageBean<DtoContract> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoContract x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");
        super.findByPage(page, criteria);
    }

    /**
     * 新增收款/委外合同
     */
    @Transactional
    @Override
    public DtoContract save(DtoContract entity) {

        // 根据合同编号跟id判断是否已存在
        Integer count = repository.getCountByCode(entity.getContractCode(), entity.getId());
        if (count > 0) {
            throw new BaseException("已存在相同编号的合同！");
        }

        // 判断当前企业是否存在
        if (StringUtils.isNotNullAndEmpty(entity.getEntName())) { //首先要判断企业名称是否输入
            if (!StringUtils.isNotNullAndEmpty(entity.getEntId())
                    || entity.getEntId().equals(UUIDHelper.GUID_EMPTY)) {
                DtoEnterprise ent = new DtoEnterprise();
                ent.setContactMan(entity.getLinkMan());
                ent.setContactTelPhone(entity.getLinkPhone());
                ent.setAddress(entity.getAddress());
                ent.setType(EnumBase.EnumEnterpriseType.客户.getValue());
                enterpriseService.save(ent);
                // 设置企业id
                entity.setEntId(ent.getId());
            }
        }
        entity.setLessAmount(entity.getTotalAmount());
        entity.setArrivalAmount(BigDecimal.ZERO);
        entity.setBadAmount(BigDecimal.ZERO);
        if (entity.getType().equals(LimCodeHelper.CollectionContract)) {
            // 收款状态根据剩余金额自动判断；当剩余金额=总金额-坏账金额，收款状态为未收款
            // 当0<剩余金额<总金额-坏账金额，收款状态为部分收款
            // 当剩余金额=0，收款状态为全部收款
            // if(contract.getLessAmount().compareTo(contract.getTotalAmount().subtract(contract.getBadAmount()))
            // == 0){
            entity.setCollectionStatus(EnumLIM.EnumCollectionStatus.未收款.getValue());
        } else {
            // 付款状态根据剩余金额自动判断；当剩余金额=总金额，付款状态为未付款
            // 当0<剩余金额<总金额,付款状态为部分付款
            // 当剩余金额=0，付款状态为已付款
            entity.setCollectionStatus(EnumLIM.EnumPaySatus.未付款.getValue());
        }
        return super.save(entity);
    }

    /**
     * 修改收款/委外合同
     */
    @Transactional
    @Override
    public DtoContract update(DtoContract entity) {
        // 根据合同编号跟id判断是否已存在
        Integer count = repository.getCountByCode(entity.getContractCode(), entity.getId());
        if (count > 0) {
            throw new BaseException("已存在相同编号的合同！");
        }

        // 判断当前企业是否存在
        if (StringUtils.isNotNullAndEmpty(entity.getEntName())) { //首先要判断企业名称是否输入
            if (!StringUtils.isNotNullAndEmpty(entity.getEntId()) ||
                    entity.getEntId().equals(UUIDHelper.GUID_EMPTY)) {
                DtoEnterprise ent = new DtoEnterprise();
                ent.setContactMan(entity.getLinkMan());
                ent.setContactTelPhone(entity.getLinkPhone());
                ent.setAddress(entity.getAddress());
                enterpriseService.save(ent);
                // 设置企业id
                entity.setEntId(ent.getId());
            }
        }
        entity.setLessAmount(entity.getTotalAmount().subtract(entity.getArrivalAmount().add(entity.getBadAmount())));
        if (entity.getType().equals(LimCodeHelper.CollectionContract)) {
            // 收款状态根据剩余金额自动判断；当剩余金额=总金额-坏账金额，收款状态为未收款
            // 当0<剩余金额<总金额-坏账金额，收款状态为部分收款
            // 当剩余金额=0，收款状态为全部收款
            if (entity.getLessAmount().compareTo(BigDecimal.ZERO) > 0
                    && entity.getLessAmount().compareTo(entity.getTotalAmount().subtract(entity.getBadAmount())) == 0) {
                entity.setCollectionStatus(EnumLIM.EnumCollectionStatus.未收款.getValue());
            } else if (entity.getLessAmount().compareTo(BigDecimal.ZERO) > 0
                    && entity.getLessAmount().compareTo(entity.getTotalAmount().subtract(entity.getBadAmount())) < 0) {
                entity.setCollectionStatus(EnumLIM.EnumCollectionStatus.部分收款.getValue());
            } else if (entity.getLessAmount().compareTo(BigDecimal.ZERO) == 0) {
                entity.setCollectionStatus(EnumLIM.EnumCollectionStatus.已收款.getValue());
            }
        } else {
            // 付款状态根据剩余金额自动判断；当剩余金额=总金额，付款状态为未付款
            // 当0<剩余金额<总金额,付款状态为部分付款
            // 当剩余金额=0，付款状态为已付款
            if (entity.getTotalAmount().compareTo(entity.getLessAmount()) == 0) {
                entity.setCollectionStatus(EnumLIM.EnumPaySatus.未付款.getValue());
            } else if (entity.getLessAmount().compareTo(BigDecimal.ZERO) > 0
                    && entity.getLessAmount().compareTo(entity.getTotalAmount()) < 0) {
                entity.setCollectionStatus(EnumLIM.EnumPaySatus.部分付款.getValue());
            } else if (entity.getLessAmount().compareTo(BigDecimal.ZERO) == 0) {
                entity.setCollectionStatus(EnumLIM.EnumPaySatus.已付款.getValue());
            }
        }

        //利用通知的方式，告知项目合同，合同信息修改了
        redisTemplate.convertAndSend(EnumLIM.EnumLIMRedisChannel.LIM_Contract_UpdateDelete.name(), JsonStream.serialize(entity));
        return super.update(entity);
    }

    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        DtoContract dto = super.findOne(String.valueOf(id));
        dto.setIsDeleted(true);
        //利用通知的方式，告知项目合同，合同信息删除了
        redisTemplate.convertAndSend(EnumLIM.EnumLIMRedisChannel.LIM_Contract_UpdateDelete.name(), JsonStream.serialize(dto));

        return super.logicDeleteById(id);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String>contractIds = new ArrayList<>();
        for(Object id:ids){
            contractIds.add(String.valueOf(id));
        }
        List<DtoContract> dtoList = super.findAll(contractIds);
        for(DtoContract dto:dtoList){
            dto.setIsDeleted(true);
            //利用通知的方式，告知项目合同，合同信息删除了
            redisTemplate.convertAndSend(EnumLIM.EnumLIMRedisChannel.LIM_Contract_UpdateDelete.name(), JsonStream.serialize(dto));
        }
        return super.logicDeleteById(ids);
    }


    @Override
    @Transactional
    public  Integer updateContractStatus(String id,Integer status) {
        return repository.updateContractStatus(id, status, PrincipalContextUser.getPrincipal().getUserId(), new Date());
    }

    // #region 注释
    // /**
    // * 新增收款/委外合同
    // */
    // @Override
    // public DtoContract create(DtoContract contract) {
    // if(contractRepository.getCountByCode(contract.getContractCode()) > 0){
    // throw new BaseException("已存在相同编号的合同！");
    // }
    // //判断当前企业是否存在
    // if(!StringUtils.isNotNullAndEmpty(contract.getEntId()) ||
    // contract.getEntId().equals(BaseCodeHelper.GUID_EMPTY)){
    // Enterprise ent = new Enterprise();
    // ent.setContactMan(contract.getLinkMan());
    // ent.setContactTelPhone(contract.getLinkPhone());
    // ent.setAddress(contract.getAddress());
    // enterpriseRepository.save(ent);
    // contract.setEntId(ent.getRowGuid());
    // }
    // contract.setLessAmount(contract.getTotalAmount());
    // contract.setArrivalAmount(BigDecimal.ZERO);
    // contract.setBadAmount(BigDecimal.ZERO);
    // if(contract.getType().equals(LimCodeHelper.CollectionContract)){
    // //收款状态根据剩余金额自动判断；当剩余金额=总金额-坏账金额，收款状态为未收款
    // //当0<剩余金额<总金额-坏账金额，收款状态为部分收款
    // //当剩余金额=0，收款状态为全部收款
    // //
    // if(contract.getLessAmount().compareTo(contract.getTotalAmount().subtract(contract.getBadAmount()))
    // == 0){
    // contract.setCollectionStatus(EnumLIM.EnumCollectionStatus.未收款.getValue());
    // }else{
    // //付款状态根据剩余金额自动判断；当剩余金额=总金额，付款状态为未付款
    // //当0<剩余金额<总金额,付款状态为部分付款
    // //当剩余金额=0，付款状态为已付款
    // contract.setCollectionStatus(EnumLIM.EnumPaySatus.未付款.getValue());
    // }
    // return contractRepository.save(contract);
    // }

    /**
     * 删除合同
     */
    // @Transactional
    // @Override
    // public Boolean delete(String rowGuid) {
    // return contractRepository.delete(rowGuid) > 0;
    // }

    // /**
    // * 获取合同列表
    // */
    // @Override
    // public void findByPage(PageBean pb, BaseCriteria baseCriteria) {
    // pb.setEntityName("Contract p");
    // pb.setSelect("select p");
    // commonRepository.findByPage(pb, baseCriteria);
    // }

    /**
     * 获取合同信息
     */
    // @Override
    // public Contract getById(String rowGuid) {
    // return contractRepository.getByRowGuid(rowGuid);
    // }

    /**
     * 修改收款/委外合同
     */
    // @Transactional
    // @Override
    // public Contract update(Contract contract) {
    // if (contractRepository.getCountByCode(contract.getContractCode(),
    // contract.getRowGuid()) > 0) {
    // throw new BaseException("已存在相同编号的合同！");
    // }
    // // 判断当前企业是否存在
    // if (!StringUtils.isNotNullAndEmpty(contract.getEntId())
    // || contract.getEntId().equals(BaseCodeHelper.GUID_EMPTY)) {
    // Enterprise ent = new Enterprise();
    // ent.setContactMan(contract.getLinkMan());
    // ent.setContactTelPhone(contract.getLinkPhone());
    // ent.setAddress(contract.getAddress());
    // enterpriseRepository.save(ent);
    // contract.setEntId(ent.getRowGuid());
    // }
    // contract.setLessAmount(contract.getTotalAmount().subtract(contract.getArrivalAmount()));
    // if (contract.getType().equals(LimCodeHelper.CollectionContract)) {
    // // 收款状态根据剩余金额自动判断；当剩余金额=总金额-坏账金额，收款状态为未收款
    // // 当0<剩余金额<总金额-坏账金额，收款状态为部分收款
    // // 当剩余金额=0，收款状态为全部收款
    // if
    // (contract.getLessAmount().compareTo(contract.getTotalAmount().subtract(contract.getBadAmount()))
    // == 0) {
    // contract.setCollectionStatus(EnumLIM.EnumCollectionStatus.未收款.getValue());
    // } else if (contract.getLessAmount().compareTo(BigDecimal.ZERO) > 0 &&
    // contract.getLessAmount()
    // .compareTo(contract.getTotalAmount().subtract(contract.getBadAmount())) < 0)
    // {
    // contract.setCollectionStatus(EnumLIM.EnumCollectionStatus.部分收款.getValue());
    // } else if (contract.getLessAmount().compareTo(BigDecimal.ZERO) == 0) {
    // contract.setCollectionStatus(EnumLIM.EnumCollectionStatus.已收款.getValue());
    // }
    // } else {
    // // 付款状态根据剩余金额自动判断；当剩余金额=总金额，付款状态为未付款
    // // 当0<剩余金额<总金额,付款状态为部分付款
    // // 当剩余金额=0，付款状态为已付款
    // if (contract.getTotalAmount().compareTo(contract.getLessAmount()) == 0) {
    // contract.setCollectionStatus(EnumLIM.EnumPaySatus.未付款.getValue());
    // } else if (contract.getLessAmount().compareTo(BigDecimal.ZERO) > 0
    // && contract.getLessAmount().compareTo(contract.getTotalAmount()) < 0) {
    // contract.setCollectionStatus(EnumLIM.EnumPaySatus.部分付款.getValue());
    // } else if (contract.getLessAmount().compareTo(BigDecimal.ZERO) == 0) {
    // contract.setCollectionStatus(EnumLIM.EnumPaySatus.已付款.getValue());
    // }
    // }
    // return commonRepository.merge(contract);
    // }
    // #endregion

    /**
     * 重新实现（为了返回调用该类的时候可以直接使用）
     *
     * @param id 主键id
     * @return 返回合同对象
     */
    @Override
    public DtoContract findOne(String id) {
        return super.findOne(id);
    }
}