package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import com.sinoyd.lims.pro.dto.DtoSamplingPersonConfig;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 采样准备建立样品编号传输结构
 * <AUTHOR>
 * @version V1.0.0 2019/12/4
 * @since V100R001
 */
@Data
public class DtoPrepareSample {
    /**
     * 项目id
     */
    private String projectId;

    /**
     * 样品id集合
     */
    private List<String> ids = new ArrayList<>();

    /**
     * 采样时间
     */
    private Date samplingTime;

    /**
     * 采样负责人id
     */
    private String samplingPersonId;

    /**
     * 采样负责人
     */
    private String samplingPerson;

    /**
     * 采样人员
     */
    private List<DtoSamplingPersonConfig> samplingPersonConfig = new ArrayList<>();
}
