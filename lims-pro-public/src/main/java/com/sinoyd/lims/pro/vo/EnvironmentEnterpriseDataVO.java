package com.sinoyd.lims.pro.vo;

import lombok.Data;

/**
 * 环保企业通检测数据信息推送VO
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2024/11/01
 **/
@Data
public class EnvironmentEnterpriseDataVO {

    public EnvironmentEnterpriseDataVO() {
    }

    public EnvironmentEnterpriseDataVO(String id, String code, String name, String result, String unit, String remark, String minLimit, String maxLimit) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.result = result;
        this.unit = unit;
        this.remark = remark;
        this.minLimit = minLimit;
        this.maxLimit = maxLimit;
    }

    /**
     * 检测结果主键id，要求项目id+检测结果id组合唯一
     */
    private String id;

    /**
     * 因子编号
     */
    private String code;

    /**
     * 因子名称
     */
    private String name;

    /**
     * 检测结果
     */
    private String result;

    /**
     * 因子单位
     */
    private String unit;

    /**
     * 备注
     */
    private String remark;

    /**
     * 参考下限
     */
    private String minLimit;

    /**
     * 参考上限
     */
    private String maxLimit;
}
