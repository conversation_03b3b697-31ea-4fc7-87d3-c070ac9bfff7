package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ELogType;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.OAProjectExpendCriteria;
import com.sinoyd.lims.pro.dto.DtoOAProjectExpend;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.service.OAProjectExpendService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 项目支出服务接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@Api(tags = "工作流: 项目支出服务")
@RestController
@RequestMapping("/api/pro/oaProjectExpends")
public class OAProjectExpendController extends BaseJpaController<DtoOAProjectExpend, String, OAProjectExpendService> {
    
    /**
     * 添加项目支出
     * 
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加支出", notes = "添加支出启动流程")
    @PostMapping
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> startProcess(@RequestBody DtoOATaskCreate<DtoOAProjectExpend> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        String procInstId = service.startProcess(taskDto);
        restResp.setData(procInstId);

        return restResp;
    }

    /**
     * 保存为草稿
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "保存为草稿", notes = "保存为草稿")
    @PostMapping("/saveAsDraft")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> saveAsDraft(@RequestBody DtoOATaskCreate<DtoOAProjectExpend> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.saveAsDraft(taskDto));
        return restResp;
    }

    /**
     * 草稿保存
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "草稿保存", notes = "草稿保存")
    @PostMapping("/draftSave")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> draftSave(@RequestBody DtoOATaskCreate<DtoOAProjectExpend> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSave(taskDto));
        return restResp;
    }

    /**
     * 草稿提交
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加审批", notes = "添加审批启动流程")
    @PostMapping("/draftSubmit")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> draftSubmit(@RequestBody DtoOATaskCreate<DtoOAProjectExpend> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSubmit(taskDto));
        return restResp;
    }

    /**
     * 查询项目支出信息
     * 
     * @param taskId 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "查询支出信息", notes = "查询支出信息")
    @GetMapping(path = "/task/{taskId}")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<DtoOATaskDetail<DtoOAProjectExpend, String>> findDetailByTaskId(@PathVariable(name = "taskId") String taskId) {
        RestResponse<DtoOATaskDetail<DtoOAProjectExpend, String>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoOATaskDetail<DtoOAProjectExpend, String> detail = service.findOATaskDetail(taskId);
        restResp.setData(detail);

        return restResp;
    }

    /**
     * 分页动态条件查询项目支出
     *
     * @param oaProjectExpendCriteria 条件参数
     * @return RestResponse<List<DtoOAProjectExpend>>
     */
    @ApiOperation(value = "分页动态条件查询项目支出", notes = "分页动态条件查询项目支出")
    @GetMapping
    public RestResponse<List<DtoOAProjectExpend>> findByPage(OAProjectExpendCriteria oaProjectExpendCriteria) {
        PageBean<DtoOAProjectExpend> pageBean = super.getPageBean();
        RestResponse<List<DtoOAProjectExpend>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, oaProjectExpendCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

}
