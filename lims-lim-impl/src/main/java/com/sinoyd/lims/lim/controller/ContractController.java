package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ContractCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoContract;
import com.sinoyd.lims.lim.service.ContractService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 合同接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-09
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/contract")
@Validated
public class ContractController extends BaseJpaController<DtoContract, String, ContractService> {

    /**
     * 根据id获取合同信息
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取合同信息", notes = "根据id获取合同信息")
    @GetMapping("/{id}")
    public RestResponse<DtoContract> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoContract> restResp = new RestResponse<>();
        DtoContract contract = service.findOne(id);
        restResp.setData(contract);

        restResp.setRestStatus(StringUtil.isNull(contract) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询合同信息
     * 
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询合同信息", notes = "分页动态条件查询合同信息")
    @GetMapping
    public RestResponse<List<DtoContract>> findByPage(ContractCriteria criteria) {

        RestResponse<List<DtoContract>> restResp = new RestResponse<>();

        PageBean<DtoContract> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增合同信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增合同信息", notes = "新增合同信息")
    @PostMapping
    public RestResponse<DtoContract> save(@Validated @RequestBody DtoContract entity) {

        RestResponse<DtoContract> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoContract data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改合同信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改合同信息", notes = "修改合同信息")
    @PutMapping
    public RestResponse<DtoContract> update(@Validated @RequestBody DtoContract entity) {

        RestResponse<DtoContract> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoContract data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 刪除单个合同
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "刪除单个合同", notes = "刪除单个合同")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除合同
     * 
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除合同", notes = "批量删除合同")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}