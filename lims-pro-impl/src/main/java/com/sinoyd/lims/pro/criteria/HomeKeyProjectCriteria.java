package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 重点项目的查询条件
 * <AUTHOR>
 * @version V1.0.0 20200319
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HomeKeyProjectCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.id = pl.projectId");
        condition.append(" and p.status<>:status");
        condition.append(" and p.isDeleted=0");
        condition.append(" and p.isStress=:isStress");
        values.put("status", EnumPRO.EnumProjectStatus.已办结.name());
        values.put("isStress", true);
        return condition.toString();
    }
}
