package com.sinoyd.lims.pro.vo;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 监测点位统计测试项目VO
 *
 * <AUTHOR>
 * @version V5.2.0 2025/4/14
 * @since V100R001
 */
@Data
public class FixedPointStatTestVO {

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 分析项目名称
     */
    private String redAnalyzeItemName;

    /**
     * 分析方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 日期X轴项
     */
    private List<String> dateValues = new ArrayList<>();

    /**
     * 点位详情
     */
    private List<FixedPointStatPointVO> pointDetail = new ArrayList<>();

    public FixedPointStatTestVO(DtoTest test, List<String> dateValues) {
        this.testId = test.getId();
        this.redAnalyzeItemName = test.getRedAnalyzeItemName();
        this.redAnalyzeMethodName = test.getRedAnalyzeMethodName();
        this.dimensionId = test.getDimensionId();
//        this.dateValues = dateValues;
    }
}
