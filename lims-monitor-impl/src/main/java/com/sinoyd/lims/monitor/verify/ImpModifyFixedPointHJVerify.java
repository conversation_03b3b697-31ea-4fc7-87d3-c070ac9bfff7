package com.sinoyd.lims.monitor.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.monitor.dto.customer.DtoExpImpFixedPointHJ;
import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 环境质量点位导入校验器
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Data
public class ImpModifyFixedPointHJVerify implements IExcelVerifyHandler<DtoExpImpFixedPointHJ> {


    private ImportUtils importUtils = new ImportUtils();

    /**
     * 所有测站数据
     */
    private final List<DtoStation> stationList;

    /**
     * 所有的区域数据
     */
    private final List<DtoArea> dbArea;

    /**
     * 控制等级
     */
    private final Map<String, List<DtoCode>> codeMap;

    /**
     * 所有企业数据
     */
    private final List<DtoEnterprise> enterpriseList;

    /**
     * 所有的检测类型
     */
    private final List<DtoSampleType> sampleTypes;

    public ImpModifyFixedPointHJVerify(List<DtoStation> stationList,
                                       List<DtoArea> dbArea,
                                       Map<String, List<DtoCode>> codeMap,
                                       List<DtoEnterprise> enterpriseList,
                                       List<DtoSampleType> sampleTypes) {
        this.stationList = stationList;
        this.dbArea = dbArea;
        this.codeMap = codeMap;
        this.enterpriseList = enterpriseList;
        this.sampleTypes = sampleTypes;
    }

    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExpImpFixedPointHJ fixedPoint) {
        //导入参数处理
        try {
            //去除空行
            if (importUtils.checkObjectIsNull(fixedPoint)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //去除首尾空格
            importUtils.strToTrim(fixedPoint);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        StringBuilder failStr = new StringBuilder("第" + (fixedPoint.getRowNum() + 1) + "行数据校验错误");

        //控制等级
        List<DtoCode> controlLevels = codeMap.get(LimConstants.ImportConstants.CONTROL_LEVEL);
        //环境质量的点位类型
        List<DtoCode> pointTypeHJ = codeMap.get(LimConstants.ImportConstants.FIXED_POINT_TYPE_HJ);
        //处理关联数据Id
        //处理是否启用
        if (StringUtil.isNotEmpty(fixedPoint.getEnabledStatus())) {
            fixedPoint.setIsEnabled(!"否".equals(fixedPoint.getEnabledStatus()));
        }
        //处理测站id
        if (StringUtil.isNotEmpty(fixedPoint.getStationName()) && StringUtil.isNotEmpty(stationList)) {
            Optional<DtoStation> stationOp = stationList
                    .stream().filter(p -> fixedPoint.getStationName().equals(p.getStname())).findFirst();
            stationOp.ifPresent(p -> fixedPoint.setStationId(p.getId()));
        }

        //非空字段判断
        importUtils.checkIsNull(result, fixedPoint.getFolderTypeName(), "点位类型", failStr);
        importUtils.checkIsNull(result, fixedPoint.getPointName(), "点位名称", failStr);
        importUtils.checkIsNull(result, fixedPoint.getStationName(), "所属测站", failStr);


        //判断点位类型是否存在
        isExistPointType(result, failStr, pointTypeHJ, fixedPoint);
        //判断等级是否存在
        isExistControlLevel(result, failStr, fixedPoint, controlLevels);
        //判断测站是否存在
        isExistStation(result, failStr, fixedPoint, stationList);
        //处理区域数据
        handlerData(dbArea, fixedPoint);
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }

    /**
     * 判断等级是否存在
     *
     * @param result        校验结果
     * @param failStr       校验错误信息
     * @param fixedPoint    当前导入的点位数据
     * @param controlLevels 所有的控制等级数据
     */
    private void isExistControlLevel(ExcelVerifyHandlerResult result, StringBuilder failStr,
                                     DtoExpImpFixedPointHJ fixedPoint, List<DtoCode> controlLevels) {
        //判断等级是否存在
        if (StringUtil.isNotEmpty(fixedPoint.getLevelName())) {
            List<DtoCode> existCode = controlLevels.stream()
                    .filter(p -> fixedPoint.getLevelName().equals(p.getDictName()))
                    .collect(Collectors.toList());
            if (StringUtil.isEmpty(existCode)) {
                result.setSuccess(false);
                failStr.append(";等级不存在");
            }
        }
    }

    /**
     * 判断测站是否存在
     *
     * @param result      校验结果
     * @param failStr     校验错误信息
     * @param fixedPoint  当前导入的点位信息
     * @param stationList 所有的测站数据
     */
    private void isExistStation(ExcelVerifyHandlerResult result, StringBuilder failStr,
                                DtoExpImpFixedPointHJ fixedPoint, List<DtoStation> stationList) {
        if (StringUtil.isNotEmpty(fixedPoint.getStationName()) && StringUtil.isNotEmpty(stationList)) {
            List<DtoStation> existStation = stationList.stream()
                    .filter(p -> fixedPoint.getStationName().equals(p.getStname()))
                    .collect(Collectors.toList());
            if (StringUtil.isEmpty(existStation)) {
                result.setSuccess(false);
                failStr.append(";所属测站不存在");
            }
        }
    }


    /**
     * 获取导出的所有点位类型
     *
     * @param folderTypeNames 点位类型导入字符串
     * @return 点位类型字符串集合
     */
    private Set<String> getFolderTypeNames(String folderTypeNames) {
        if (StringUtil.isNotEmpty(folderTypeNames) && folderTypeNames.contains("、")) {
            return Arrays.stream(folderTypeNames.split("、")).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }


    /**
     * 判断点位类型是否存在
     *
     * @param result      校验结果
     * @param failStr     校验错误信息
     * @param pointTypeHJ 环境质量点位类型
     * @param fixedPoint  当前导入的数据
     */
    private void isExistPointType(ExcelVerifyHandlerResult result, StringBuilder failStr,
                                  List<DtoCode> pointTypeHJ,
                                  DtoExpImpFixedPointHJ fixedPoint) {
        //判断点位类型是否存在
        if (StringUtil.isNotEmpty(fixedPoint.getFolderTypeName())) {
            String folderTypeName = fixedPoint.getFolderTypeName();
            Set<String> folderTypeNames = this.getFolderTypeNames(folderTypeName);
            Set<String> codeNames = pointTypeHJ.stream().map(DtoCode::getDictName).collect(Collectors.toSet());
            List<String> notExistCode = folderTypeNames.stream()
                    .filter(p -> !codeNames.contains(p))
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(notExistCode)) {
                result.setSuccess(false);
                failStr.append(";点位类型").append(notExistCode).append("不存在");
            }
        }
    }

    /**
     * 判断区域是否必填
     *
     * @param result       校验结果
     * @param fixedPointRS 导入的数据
     * @param failStr      校验结果字符串
     */
    private void checkAreaIsNull(ExcelVerifyHandlerResult result, DtoExpImpFixedPointHJ fixedPointRS, StringBuilder failStr) {
        //当填市级区域时，省级为必填项
        if (StringUtil.isNotEmpty(fixedPointRS.getCityAreaName())) {
            importUtils.checkIsNull(result, fixedPointRS.getProvinceAreaName(), "省级区域", failStr);
        }
        //当填写县、区级区域时，省级与市级区域为必填项
        if (StringUtil.isNotEmpty(fixedPointRS.getAreaName())) {
            importUtils.checkIsNull(result, fixedPointRS.getProvinceAreaName(), "省级区域", failStr);
            importUtils.checkIsNull(result, fixedPointRS.getCityAreaName(), "市级区域", failStr);
        }
    }

    /**
     * 判断填写区域是否存在
     *
     * @param result       校验结果
     * @param fixedPointRS 导入的企业数据
     * @param dbArea       所有区域信息
     * @param failStr      错误信息
     */
    private void isExistArea(ExcelVerifyHandlerResult result, DtoExpImpFixedPointHJ fixedPointRS, List<DtoArea> dbArea, StringBuilder failStr) {
        Map<String, List<DtoArea>> areaGroup = dbArea.stream().collect(Collectors.groupingBy(DtoArea::getAreaName));
        String provinceName = fixedPointRS.getProvinceAreaName();
        String cityName = fixedPointRS.getCityAreaName();
        String areaName = fixedPointRS.getAreaName();
        judgeArea(provinceName, areaGroup, result, failStr);
        judgeArea(cityName, areaGroup, result, failStr);
        judgeArea(areaName, areaGroup, result, failStr);
    }

    /**
     * 判断区域是否存在
     *
     * @param name      区域名称
     * @param areaGroup 区域分组数据
     * @param result    校验结果
     * @param failStr   错误信息
     */
    private void judgeArea(String name, Map<String, List<DtoArea>> areaGroup, ExcelVerifyHandlerResult result, StringBuilder failStr) {
        List<DtoArea> isExistArea;
        if (StringUtil.isNotEmpty(name)) {
            isExistArea = areaGroup.get(name);
            if (StringUtil.isEmpty(isExistArea)) {
                result.setSuccess(false);
                failStr.append("；").append(name).append("在系统中不存在");
            }
        }
    }

    /**
     * 处理区域id
     *
     * @param dbArea       区域数据
     * @param fixedPointRS 导入数据
     */
    private void handlerData(List<DtoArea> dbArea, DtoExpImpFixedPointHJ fixedPointRS) {
        //获取到导入的省、市、县、区
        String provinceName = fixedPointRS.getProvinceAreaName();
        String cityName = fixedPointRS.getCityAreaName();
        String areaName = fixedPointRS.getAreaName();
        //处理省级区域id
        if (StringUtil.isNotEmpty(provinceName)) {
            Optional<DtoArea> areaOp = dbArea.stream().filter(p -> provinceName.equals(p.getAreaName())).findFirst();
            areaOp.ifPresent(p -> fixedPointRS.setProvinceId(p.getId()));
        }
        //处理市级区域id
        if (StringUtil.isNotEmpty(cityName) && StringUtil.isNotEmpty(fixedPointRS.getProvinceId())) {
            Optional<DtoArea> areaOp = dbArea.stream().filter(p -> cityName.equals(p.getAreaName())
                    && fixedPointRS.getProvinceId().equals(p.getParentId())).findFirst();
            areaOp.ifPresent(p -> fixedPointRS.setCityId(p.getId()));
        }
        //处理县、区级区域id
        if (StringUtil.isNotEmpty(areaName) && StringUtil.isNotEmpty(fixedPointRS.getCityId())) {
            Optional<DtoArea> areaOp = dbArea.stream().filter(p -> areaName.equals(p.getAreaName())
                    && fixedPointRS.getCityId().equals(p.getParentId())).findFirst();
            areaOp.ifPresent(p -> fixedPointRS.setAreaId(p.getId()));
        }
    }


}
