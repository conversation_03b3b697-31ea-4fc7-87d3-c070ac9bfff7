package com.sinoyd.lims.pro.strategy.strategy.fixedPointStat;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.entity.Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.FixedPointStatCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.repository.SampleFolderRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.AnalyseDataService;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.util.MathUtil;
import com.sinoyd.lims.pro.vo.FixedPointStatAnaDataVO;
import com.sinoyd.lims.pro.vo.FixedPointStatDateVO;
import com.sinoyd.lims.pro.vo.FixedPointStatPointVO;
import com.sinoyd.lims.pro.vo.FixedPointStatTestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监测点位统计策略基类
 *
 * <AUTHOR>
 * @version V5.2.0 2025/4/14
 * @since V100R001
 */
@Component
public abstract class AbsFixedPointStat {

    protected TestRepository testRepository;

    protected SampleRepository sampleRepository;

    protected SampleFolderRepository sampleFolderRepository;

    protected FixedpointRepository fixedpointRepository;

    protected CommonRepository commonRepository;

    protected AnalyseDataService analyseDataService;

    protected ProService proService;

    /**
     * 统计点位详情数据
     *
     * @param criteria 查询条件
     * @param pb       分页参数
     * @return 统计结果
     */
    public List<FixedPointStatTestVO> stat(BaseCriteria criteria, PageBean<DtoAnalyseData> pb) {
        FixedPointStatCriteria statCriteria = (FixedPointStatCriteria) criteria;
        //获取日期区间内所有天数
        List<String> dateXVal = getDateXVal(statCriteria);
        //查询分析数据
        List<FixedPointStatAnaDataVO> analyseData = findAnalyseData(statCriteria);
        //处理统计数据
        return handleStatData(analyseData, dateXVal, pb);
    }


    /**
     * 处理查询条件
     *
     * @param criteria 查询条件
     */
    protected void handleCriteria(FixedPointStatCriteria criteria) {
        List<String> sampleIds = new ArrayList<>();
        if (StringUtil.isEmpty(criteria.getPointIds())) {
            sampleIds.add(UUIDHelper.GUID_EMPTY);
        } else {
            List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findByFixedPointIdIn(new ArrayList<>(criteria.getPointIds()));
            if (StringUtil.isNotEmpty(sampleFolders)) {
                //获取到点位下的样品数据
                List<String> sampleFolderIds = sampleFolders.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
                //查询样品数据
                List<DtoSample> sampleList = sampleRepository.findBySampleFolderIdIn(sampleFolderIds);
                sampleIds.addAll(sampleList.stream().map(DtoSample::getId).collect(Collectors.toList()));
            } else {
                sampleIds.add(UUIDHelper.GUID_EMPTY);
            }
        }
        criteria.setSampleIds(sampleIds);
    }

    /**
     * 查询获取分析数据
     *
     * @param criteria 查询条件
     * @return 分析数据
     */
    protected List<FixedPointStatAnaDataVO> findAnalyseData(FixedPointStatCriteria criteria) {
        //处理查询条件
        handleCriteria(criteria);
        PageBean<DtoAnalyseData> pb = new PageBean<>();
        pb.setEntityName("DtoAnalyseData a");
        pb.setSelect("select a");
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        commonRepository.findByPage(pb, criteria);
        List<DtoAnalyseData> anaDataList = pb.getData();
        //转换数据
        List<FixedPointStatAnaDataVO> anaDataVOList = anaDataList.stream().map(FixedPointStatAnaDataVO::new).collect(Collectors.toList());
        //处理冗余字段
        loadDataFields(anaDataVOList);
        return anaDataVOList;
    }

    /**
     * 处理统计数据
     *
     * @param analyseData 查询分析数据
     * @param dateXVal    日期区间天数数据
     * @param pb          分页参数
     * @return 统计结果
     */
    protected List<FixedPointStatTestVO> handleStatData(List<FixedPointStatAnaDataVO> analyseData, List<String> dateXVal, PageBean<DtoAnalyseData> pb) {
        //统计结果
        List<FixedPointStatTestVO> result = new ArrayList<>();
        if (StringUtil.isEmpty(analyseData)) {
            return result;
        }
        //查询所有测试项目数据【处理分页】
        Set<String> testIds = analyseData.stream().map(FixedPointStatAnaDataVO::getTestId).collect(Collectors.toSet());
        List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        pb.setRowsCount(testIds.size());
        Map<String, DtoTest> testMap = testList.stream()
                .sorted(Comparator.comparing(Test::getRedAnalyzeItemName))
                .skip((long) (pb.getPageNo() - 1) * pb.getRowsPerPage())
                .limit(pb.getRowsPerPage())
                .collect(Collectors.toMap(DtoTest::getId, v -> v));
        //查询所有点位数据
        Set<String> pointIds = analyseData.stream().map(FixedPointStatAnaDataVO::getFixedPointId).collect(Collectors.toSet());
        Map<String, DtoFixedpoint> pointMap = fixedpointRepository.findAll(pointIds)
                .stream().collect(Collectors.toMap(DtoFixedpoint::getId, v -> v));
        //按照测试项目分组
        Map<String, List<FixedPointStatAnaDataVO>> testGroupMap = analyseData.stream()
                .collect(Collectors.groupingBy(FixedPointStatAnaDataVO::getTestId));
        List<String> showXVal = new ArrayList<>();
        for (Map.Entry<String, List<FixedPointStatAnaDataVO>> testEntry : testGroupMap.entrySet()) {
            DtoTest test = testMap.get(testEntry.getKey());
            if (test != null) {
                FixedPointStatTestVO testStatVo = new FixedPointStatTestVO(test, dateXVal);
                //获取分析数据中最新一条分析数据的量纲
                Optional<FixedPointStatAnaDataVO> newestData = testEntry.getValue().stream().filter(p -> StringUtil.isNotNull(p.getAnalyzeTime())).max(Comparator.comparing(FixedPointStatAnaDataVO::getAnalyzeTime));
                if (newestData.isPresent()) {
                    testStatVo.setDimensionId(newestData.get().getDimensionId());
                    testStatVo.setDimensionName(newestData.get().getDimensionName());
                }
                //数据按照点位分组
                Map<String, List<FixedPointStatAnaDataVO>> pointGroupMap = testEntry.getValue().stream()
                        .collect(Collectors.groupingBy(FixedPointStatAnaDataVO::getFixedPointId));
                //处理点位数据
                for (Map.Entry<String, List<FixedPointStatAnaDataVO>> pointEntry : pointGroupMap.entrySet()) {
                    DtoFixedpoint point = pointMap.get(pointEntry.getKey());
                    FixedPointStatPointVO pointStatVo = new FixedPointStatPointVO(point);
                    //根据分析日期分组
                    Map<String, List<FixedPointStatAnaDataVO>> dateGroupMap = pointEntry.getValue().stream()
                            .collect(Collectors.groupingBy(FixedPointStatAnaDataVO::getAnalyzeTimeDay));
                    //循环日期区间天数，统计天数内的数据的出证结果（如果当天内出现多个分析数据的，只取数据中的第一条数据）
                    for (String day : dateXVal) {
                        List<FixedPointStatAnaDataVO> dateDataList = dateGroupMap.getOrDefault(day, new ArrayList<>());
                        String val = "0";
                        //多个数据，计算数据平均值、修约获取数据中第一条数据的修约规则
                        if (dateDataList.size() > 1) {
                            val = calculateAvg(dateDataList, test);
                            pointStatVo.getStatisticalChartsDetail().add(new FixedPointStatDateVO(day, val, checkVal(val)));
                            showXVal.add(day);
                        } else {
                            Optional<FixedPointStatAnaDataVO> first = dateDataList.stream().findFirst();
                            if (first.isPresent()) {
                                val = first.get().getTestValue();
                                pointStatVo.getStatisticalChartsDetail().add(new FixedPointStatDateVO(day, val, checkVal(val)));
                                showXVal.add(day);
                            }
                        }
                    }
                    testStatVo.getPointDetail().add(pointStatVo);
                }
                result.add(testStatVo);
            }
        }

        //排序要显示的日期数据
        if (StringUtil.isNotEmpty(showXVal)){
            showXVal = showXVal.stream().distinct().sorted().collect(Collectors.toList());
        }
        //处理其余日期值
        for (FixedPointStatTestVO testVo : result) {
            for (FixedPointStatPointVO pointVo : testVo.getPointDetail()) {
                //处理其余日期值
                Map<String, String> dateValMap = pointVo.getStatisticalChartsDetail().stream().collect(Collectors.toMap(FixedPointStatDateVO::getDate, FixedPointStatDateVO::getValue));
                List<FixedPointStatDateVO> dateVos = new ArrayList<>();
                for (String day : showXVal) {
                    String val = dateValMap.getOrDefault(day, "-");
                    dateVos.add(new FixedPointStatDateVO(day, val, checkVal(val)));
                }
                pointVo.setDateDetail(dateVos);
            }
        }
        return result;

    }

    /**
     * 计算多个结果的均值
     *
     * @param dateDataList 数据
     * @param test         当前测试项目
     * @return 均值
     */
    protected String calculateAvg(List<FixedPointStatAnaDataVO> dateDataList, DtoTest test) {
        //有效位数
        Integer mostSignificance = dateDataList.get(0).getMostSignificance();
        //小数位数
        Integer mostDecimal = dateDataList.get(0).getMostDecimal();
        //计算均值
        List<String> values = new ArrayList<>();
        for (FixedPointStatAnaDataVO data : dateDataList) {
            //判断测试项目配置的修约计算规则，是先修约还是先计算
            String calculateVal;
            if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(test.getCalculateWay())) {
                calculateVal = data.getTestValueDstr();
            } else {
                calculateVal = data.getTestOrignValue();
            }
            if (checkVal(data.getTestValue())) {
                //比较检出限获取值
                calculateVal = limitValue(calculateVal, data.getExamLimitValue());
            }
            values.add(StringUtil.isEmpty(calculateVal) ? "0" : calculateVal);
        }
        values.removeIf("/"::equals);
        BigDecimal avg = values.stream().map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(new BigDecimal(values.size()), ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
        //修约结果
        return proService.getDecimal(mostSignificance, mostDecimal, avg.toString());
    }

    /**
     * 判断是否小于检出限
     *
     * @param val 检测结果
     * @return 判断结果
     */
    protected Boolean checkVal(String val) {
        return val.contains("-") || val.contains("<") || val.contains("＜") || val.contains("ND")
                || val.contains("L") || val.contains("DL");
    }

    /**
     * 获取小于检出限的数据
     *
     * @param value          检测结果
     * @param examLimitValue 检出限
     * @return 计算值
     */
    protected String limitValue(String value, String examLimitValue) {
        //根据参数值处理小于检出限的数值
        String result = value;
        //如果redis有key值
        String configValue = analyseDataService.getConfigValue("sample");
        if (StringUtil.isNotEmpty(configValue)) {
            if (EnumBase.EnumLessExamLimit.检出限一半.getValue().equals(configValue)) {
                result = (MathUtil.getBigDecimal(examLimitValue).divide(new BigDecimal(2), BigDecimal.ROUND_HALF_EVEN)).toString();
            }
            if (EnumBase.EnumLessExamLimit.检出限.getValue().equals(configValue)) {
                result = examLimitValue;
            }
            if (EnumBase.EnumLessExamLimit.零.getValue().equals(configValue)) {
                result = "0";
            }
            if (EnumBase.EnumLessExamLimit.检测结果.getValue().equals(configValue)) {
                result = value;
            }
        } else {
            result = (MathUtil.getBigDecimal(examLimitValue).divide(new BigDecimal(2), BigDecimal.ROUND_HALF_EVEN)).toString();
        }
        return result;
    }

    /**
     * 处理冗余字段
     *
     * @param anaDataVOList 数据
     */
    protected void loadDataFields(Collection<FixedPointStatAnaDataVO> anaDataVOList) {
        if (StringUtil.isNotEmpty(anaDataVOList)) {
            Set<String> sampleIds = anaDataVOList.stream().map(FixedPointStatAnaDataVO::getSampleId).collect(Collectors.toSet());
            //查询样品数据
            List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(sampleIds) : new ArrayList<>();
            Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, v -> v));
            //获取点位数据
            Set<String> sampleFolderIds = sampleList.stream().map(DtoSample::getSampleFolderId).collect(Collectors.toSet());
            List<DtoSampleFolder> sampleFolderList = sampleFolderRepository.findAll(sampleFolderIds);
            Map<String, DtoSampleFolder> folderMap = sampleFolderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, v -> v));
            for (FixedPointStatAnaDataVO data : anaDataVOList) {
                DtoSample sample = sampleMap.get(data.getSampleId());
                if (sample != null) {
                    DtoSampleFolder sampleFolder = folderMap.get(sample.getSampleFolderId());
                    data.setSampleFolderId(sample.getSampleFolderId());
                    if (sampleFolder != null) {
                        data.setFixedPointId(sampleFolder.getFixedPointId());
                    }
                }
            }
        }
    }

    /**
     * 根据查询查询中的日期范围获取X轴日期数据（按照日期天数获取）
     *
     * @param criteria 查询条件
     * @return 日期X轴数据
     */
    protected List<String> getDateXVal(FixedPointStatCriteria criteria) {
        List<String> dateList = new ArrayList<>();
        if (criteria.getStartDate() == null || criteria.getEndDate() == null) {
            return dateList;
        }
        LocalDate startDate = LocalDate.parse(criteria.getStartDate());
        LocalDate endDate = LocalDate.parse(criteria.getEndDate());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        while (!startDate.isAfter(endDate)) {
            dateList.add(startDate.format(formatter));
            startDate = startDate.plusDays(1);
        }
        return dateList;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setFixedpointRepository(FixedpointRepository fixedpointRepository) {
        this.fixedpointRepository = fixedpointRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }

    @Autowired
    @Lazy
    public void setProService(ProService proService) {
        this.proService = proService;
    }
}
