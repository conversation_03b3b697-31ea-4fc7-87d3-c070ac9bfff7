package com.sinoyd.base.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 量纲查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DimensionCriteria extends BaseCriteria {

    private String key;// 关键字：量纲名称、量纲编号

    private String type;// 量纲类型：常量Guid

    /**
     * 根据字段组成查询条件
     */
    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        // 关键字模糊查找
        if (StringUtils.isNotNullAndEmpty(this.key)) {
            condition.append(" and (dimensionName like :key or code like :key)");
            values.put("key", "%" + this.key + "%");
        }
        // 量纲类型查找
        if (StringUtils.isNotNullAndEmpty(this.type) && !UUIDHelper.GUID_EMPTY.toString().equals(this.type)) {
            condition.append(" and dimensionTypeId = :type");
            values.put("type", this.type);
        }
        condition.append(" and isDeleted = 0 ");

        return condition.toString();
    }

}