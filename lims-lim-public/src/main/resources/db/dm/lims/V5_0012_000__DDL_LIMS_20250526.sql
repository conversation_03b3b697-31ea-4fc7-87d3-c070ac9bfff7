CREATE TABLE tb_pro_fileAudit (
                                  id varchar(50) PRIMARY KEY,
                                  title varchar(500) NOT NULL DEFAULT '',
                                  type int NOT NULL DEFAULT -1,
                                  folderId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
                                  description varchar(2000) DEFAULT NULL,
                                  documentIds varchar(2000) DEFAULT NULL,
                                  fileName CLOB,
                                  isSetValidDate bit NOT NULL DEFAULT 0,
                                  validDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
                                  issueRange varchar(500) DEFAULT NULL,
                                  reviseDeptId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
                                  revisePersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
                                  auditPeople varchar(2000) DEFAULT NULL,
                                  techLeaderId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
                                  approvePersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
                                  receivePeople varchar(2000) DEFAULT NULL,
                                  registerPersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
                                  stepCode varchar(50) NOT NULL,
                                  stepStatus int NOT NULL DEFAULT -1,
                                  creator varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
                                  createDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
                                  startDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
                                  passDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00'
);
COMMENT ON TABLE tb_pro_fileAudit IS '文件审核记录';
COMMENT ON COLUMN tb_pro_fileAudit.id IS '主键';
COMMENT ON COLUMN tb_pro_fileAudit.title IS '名称';
COMMENT ON COLUMN tb_pro_fileAudit.type IS '申请类型，关联枚举EnumFileAuditApplyType';
COMMENT ON COLUMN tb_pro_fileAudit.folderId IS '文件目录';
COMMENT ON COLUMN tb_pro_fileAudit.description IS '内容描述';
COMMENT ON COLUMN tb_pro_fileAudit.documentIds IS '文件标识';
COMMENT ON COLUMN tb_pro_fileAudit.fileName IS '文件名称';
COMMENT ON COLUMN tb_pro_fileAudit.isSetValidDate IS '是否设定启用日期';
COMMENT ON COLUMN tb_pro_fileAudit.issueRange IS '发放范围';
COMMENT ON COLUMN tb_pro_fileAudit.reviseDeptId IS '修编部门标识';
COMMENT ON COLUMN tb_pro_fileAudit.revisePersonId IS '修编人标识';
COMMENT ON COLUMN tb_pro_fileAudit.auditPeople IS '参与评审人员，多个guid英文逗号拼接';
COMMENT ON COLUMN tb_pro_fileAudit.techLeaderId IS '技术或质量负责人';
COMMENT ON COLUMN tb_pro_fileAudit.approvePersonId IS '批准人员';
COMMENT ON COLUMN tb_pro_fileAudit.receivePeople IS '接收人员，多个guid英文逗号拼接';
COMMENT ON COLUMN tb_pro_fileAudit.registerPersonId IS '备案人员';
COMMENT ON COLUMN tb_pro_fileAudit.stepCode IS '模块编码（枚举EnumFileAuditStep）';
COMMENT ON COLUMN tb_pro_fileAudit.stepStatus IS '状态，关联枚举EnumFileAuditStatus';
COMMENT ON COLUMN tb_pro_fileAudit.creator IS '发起人';
COMMENT ON COLUMN tb_pro_fileAudit.createDate IS '创建时间';
COMMENT ON COLUMN tb_pro_fileAudit.startDate IS '发起时间';
COMMENT ON COLUMN tb_pro_fileAudit.passDate IS '通过时间';

CREATE TABLE tb_pro_fileAuditStatus (
                                        id varchar(50) PRIMARY KEY,
                                        fileAuditId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
                                        stepCode varchar(50) NOT NULL,
                                        isHandle bit NOT NULL DEFAULT 0,
                                        isPass  bit NOT NULL DEFAULT 0,
                                        currentPersonId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
                                        lastNewOpinion CLOB,
                                        lastOperateDate datetime NOT NULL DEFAULT '1753-01-01 00:00:00'
);
COMMENT ON TABLE tb_pro_fileAuditStatus IS '文件审核处理状态';
COMMENT ON COLUMN tb_pro_fileAuditStatus.id IS '主键';
COMMENT ON COLUMN tb_pro_fileAuditStatus.fileAuditId IS '文件审核标识';
COMMENT ON COLUMN tb_pro_fileAuditStatus.stepCode IS '模块编码（枚举EnumFileAuditStep）';
COMMENT ON COLUMN tb_pro_fileAuditStatus.isHandle IS '是否处理';
COMMENT ON COLUMN tb_pro_fileAuditStatus.isPass IS '是否通过';
COMMENT ON COLUMN tb_pro_fileAuditStatus.currentPersonId IS '当前操作人Id';
COMMENT ON COLUMN tb_pro_fileAuditStatus.lastNewOpinion IS '最新一条意见';
COMMENT ON COLUMN tb_pro_fileAuditStatus.lastOperateDate IS '最新一次操作时间';
