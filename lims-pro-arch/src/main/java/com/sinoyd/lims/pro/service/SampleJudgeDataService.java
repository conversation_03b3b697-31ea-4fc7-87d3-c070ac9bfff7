package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import com.sinoyd.lims.pro.vo.SampleJudgeDataVo;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 样品评判数据管理service
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/06/13
 */
public interface SampleJudgeDataService extends IBaseJpaService<DtoSampleJudgeData, String> {

    /**
     * 根据点位id查询评价数据
     *
     * @param vo 信息
     * @return Map<String, List < DtoSampleJudgeData>>
     */
    Map<String, List<DtoSampleJudgeData>> findByFolderId(SampleJudgeDataVo vo);

    /**
     * 根据送样单id查询评价数据
     *
     * @param criteria 查询条件
     * @return Object
     */
    Object evaluteList(BaseCriteria criteria);

    /**
     * 添加质控样
     *
     * @param qcGrade 参数
     */
    void addQcSample(Integer qcGrade, Integer qcType, String standardCode, String expectedValue, Integer num, List<String> ids);

    /**
     * 同步数据
     *
     * @param params 参数
     */
    void syncData(Map<String, Object> params);

    /**
     * 同步配置
     *
     * @param params 参数
     */
    void syncConfig(Map<String, Object> params);

    /**
     * 导出
     *
     * @param criteria 查询条件
     */
    void export(BaseCriteria criteria, HttpServletResponse response);

    /**
     * 判断送样单下有没有比对数据
     *
     * @param receiveId 送样单id
     * @return Map<String, Boolean>
     */
    Map<String, Object> havingData(String receiveId);

    /**
     * 判断报告下有没有比对数据
     *
     * @param reportId 报告id
     * @return Map<String, Boolean>
     */
    Map<String, Object> havingDataForReport(String reportId);

    /**
     * 判断项目下有没有比对数据
     *
     * @param projectId 报告id
     * @return Map<String, Boolean>
     */
    Map<String, Object> havingDataForProject(String projectId);

    /**
     * 填充冗余数据
     *
     * @param dataList 数据列表
     */
    void fillingTransientFields(List<DtoSampleJudgeData> dataList);

    /**
     * 修改比对数据
     *
     * @param receiveId 送样单id
     */
    void updateJudgeData(String receiveId);

    /**
     * 根据样品和测试项目创建比对数据
     *
     * @param sampleList 样品集合
     * @param testIds    测试项目ids
     * @param allTestIds 已存在的测试项目
     * @param project    项目信息
     */
    void createJudgeDataBySampleTest(List<DtoSample> sampleList, Collection<String> testIds, Collection<String> allTestIds, DtoProject project);

    /**
     * 根据测试项目修改比对数据
     *
     * @param sampleIds  样品ids
     * @param testIds    测试项目ids
     * @param newTestIds 新测试项目ids
     */
    void updateJudgeDataByTest(List<String> sampleIds, List<String> testIds, List<String> newTestIds);

    /**
     * 删除比对数据
     *
     * @param sampleIds 样品ids
     * @param testIds   测试项目ids
     * @param isDeleted 是否删除
     */
    void deleteJudgeDataByTest(List<String> sampleIds, List<String> testIds, Boolean isDeleted);

    /**
     * 比对数据作废
     *
     * @param judgeDataIds 比对ids
     */
    void cancellationData(List<String> judgeDataIds);

    /**
     * 取消比对数据作废
     *
     * @param judgeDataIds 比对ids
     */
    void cancellationCancelData(List<String> judgeDataIds);

    /**
     * 根据查询条件获取数据
     *
     * @param criteria 查询条件
     * @return List<DtoSampleJudgeData>
     */
    List<DtoSampleJudgeData> getData(BaseCriteria criteria);

    /**
     * 更新是否不参与评价状态
     *
     * @param ids           比对数据ids
     * @param isNotEvaluate 是否不参与
     */
    void updateEvaluateState(List<String> ids, Boolean isNotEvaluate);
}
