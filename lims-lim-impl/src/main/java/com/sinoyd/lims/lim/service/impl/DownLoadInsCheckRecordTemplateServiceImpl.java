package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoImportInstrumentCheckRecord;
import com.sinoyd.lims.lim.dto.customer.DtoImportInstrumentCheckRecordNoCode;
import com.sinoyd.lims.lim.service.DownLoadInsCheckRecordTemplateService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 模板下载
 *
 * <AUTHOR>
 * @version V1.0.0 2022/3/24
 * @since V100R001
 */
@Service
public class DownLoadInsCheckRecordTemplateServiceImpl implements DownLoadInsCheckRecordTemplateService {

    //region 注入
    private ImportUtils importUtils;
    //endregion

    /**
     * 下载模板
     *
     * @param response     响应流
     * @param sheetNames   需要赋值的sheet名
     * @param fileName     文件名
     * @param instrumentId 仪器id
     */
    @Override
    public void downLoadExcel(HttpServletResponse response, Map<String, String> sheetNames, String fileName, String instrumentId) {
        // 获取空数据
        List<DtoImportInstrumentCheckRecord> emptyList = getNullIns();
        //endregion

        //region 赋值工作单并下载
        // 获取需要下载的工作单
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportInstrumentCheckRecord.class, emptyList);
        if (StringUtil.isNotEmpty(instrumentId)) {
            List<DtoImportInstrumentCheckRecordNoCode> emptyListNoCode = new ArrayList<>();
            BeanUtils.copyProperties(emptyList, emptyList);
            workBook = importUtils.getWorkBook(sheetNames, DtoImportInstrumentCheckRecordNoCode.class, emptyListNoCode);
        }
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
        //endregion
    }

    /**
     * 获取空仪器检定数据
     *
     * @return 空list
     */
    private List<DtoImportInstrumentCheckRecord> getNullIns() {
        List<DtoImportInstrumentCheckRecord> instrumentCheckRecords = new ArrayList<>();
        DtoImportInstrumentCheckRecord checkRecord = new DtoImportInstrumentCheckRecord();
        instrumentCheckRecords.add(checkRecord);
        return instrumentCheckRecords;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }
}
