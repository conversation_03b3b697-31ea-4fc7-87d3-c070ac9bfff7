package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.sampleFolder;

import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.DtoSampleFolderTemplate;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTemp;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTestTemp;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component(IFileNameConstant.SchemeSynchronizationKey.ADD_SAMPLEFOLDER)
public class AddSampleFolderStrategy extends AbsSampleFolderStrategy {

    /**
     * 调整点位方案 -- 新增点位需要调整
     *
     * @param sampleFolderTemplateList 修改点位内容
     */
    @Override
    public void synchronizationSampleFolder(String projectId, List<DtoSampleFolderTemplate> sampleFolderTemplateList) {
        List<String> folderIds = sampleFolderTemplateList.stream().map(DtoSampleFolderTemplate::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTemp> tempList = samplingFrequencyTempRepository.findBySampleFolderTempIdIn(folderIds);
        Map<String, List<DtoSamplingFrequencyTemp>> frequencyMap = tempList.stream()
                .collect(Collectors.groupingBy(DtoSamplingFrequencyTemp::getSampleFolderTempId));
        List<String> tempIds = tempList.stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTestTemp> testTempList = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(tempIds);
        for (String frequencyKey : frequencyMap.keySet()) {
            List<DtoSamplingFrequencyTemp> frequencyTempList = frequencyMap.get(frequencyKey);
            int periodCount = frequencyTempList.stream().max(Comparator.comparing(DtoSamplingFrequencyTemp::getPeriodCount))
                    .map(DtoSamplingFrequencyTemp::getPeriodCount).get();
            int timePerPeriod = frequencyTempList.stream().max(Comparator.comparing(DtoSamplingFrequencyTemp::getTimePerPeriod))
                    .map(DtoSamplingFrequencyTemp::getTimePerPeriod).get();
            int samplePerTime = frequencyTempList.stream().max(Comparator.comparing(DtoSamplingFrequencyTemp::getSamplePerTime))
                    .map(DtoSamplingFrequencyTemp::getSamplePerTime).get();
            Optional<DtoSampleFolderTemplate> folderTemplate = sampleFolderTemplateList.stream()
                    .filter(p -> frequencyKey.equals(p.getId())).findFirst();
            List<String> freTempIds = frequencyTempList.stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
            List<String> anaItemIds = testTempList.stream().filter(p -> freTempIds.contains(p.getSamplingFrequencyTempId()))
                    .map(DtoSamplingFrequencyTestTemp::getAnalyseItemId).distinct().collect(Collectors.toList());
            folderTemplate.ifPresent(f -> {
                DtoSampleFolder folder = new DtoSampleFolder();
                folder.setWatchSpot(f.getWatchSpot());
                folder.setPeriodCount(periodCount);
                folder.setTimePerPeriod(timePerPeriod);
                folder.setSampleOrder(samplePerTime);
                folder.setLon(f.getLon());
                folder.setLat(f.getLat());
                folder.setSampleTypeId(f.getSampleTypeId());
                folder.setProjectId(projectId);
                folder.setAnalyseItemIds(anaItemIds);
                sampleFolderService.addFolder(folder);
            });
        }
    }
}
