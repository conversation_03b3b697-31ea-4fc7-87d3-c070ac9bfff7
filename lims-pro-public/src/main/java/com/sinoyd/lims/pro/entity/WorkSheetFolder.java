package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * WorkSheetFolder实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "WorkSheetFolder")
@Data
@EntityListeners(AuditingEntityListener.class)
public class WorkSheetFolder implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public WorkSheetFolder() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 检测单号
     */
    @Column(length = 20)
    @ApiModelProperty("检测单号")
    @Length(message = "工作单号{validation.message.length}", max = 20)
    private String workSheetCode;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 分析人Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("分析人Id")
    private String analystId;

    /**
     * 排序id
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("排序id")
    private String sortId;

    /**
     * 分析人名称
     */
    @Column(length = 50)
    @ApiModelProperty("分析人名称")
    @Length(message = "分析人名称{validation.message.length}", max = 50)
    private String analystName;

    /**
     * 分析日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("分析日期")
    private Date analyzeTime;

    /**
     * 检测方法id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测方法id")
    private String analyzeMethodId;

    /**
     * 复核人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("复核人id")
    private String checkerId;

    /**
     * 复核人名称
     */
    @Column(length = 50)
    @ApiModelProperty("复核人名称")
    @Length(message = "复核人名称{validation.message.length}", max = 50)
    private String checkerName;

    /**
     * 复核日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("复核日期")
    private Date checkDate;

    /**
     * 审核人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("审核人id")
    private String auditorId;

    /**
     * 审核人姓名
     */
    @Column(length = 50)
    @ApiModelProperty("审核人姓名")
    @Length(message = "审核人姓名{validation.message.length}", max = 50)
    private String auditorName;

    /**
     * 上岗证人员id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("上岗证人员id")
    private String certificatorId;

    /**
     * 上岗证人员姓名
     */
    @Column(length = 50)
    @ApiModelProperty("上岗证人员姓名")
    @Length(message = "上岗证人员姓名{validation.message.length}", max = 50)
    private String certificatorName;

    /**
     * 审核日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("审核日期")
    private Date auditDate;

    /**
     * 检测单状态（字符串，枚举EnumWorkSheetStatus：1.新建 2.已经保存 6.检测单拒绝 8.已经提交 24.复核通过 32.审核通过）
     */
    @Column(length = 50)
    @ApiModelProperty("检测单状态（字符串，枚举EnumWorkSheetStatus：1.新建 2.已经保存 6.检测单拒绝 8.已经提交 24.复核通过 32.审核通过）")
    @Length(message = "工作单状态{validation.message.length}", max = 50)
    private String status;

    /**
     * 检测单状态（int，枚举EnumWorkSheetStatus：1.新建 2.已经保存 6.检测单拒绝 8.已经提交 24.复核通过 32.审核通过）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("检测单状态（int，枚举EnumWorkSheetStatus：1.新建 2.已经保存 6.检测单拒绝 8.已经提交 24.复核通过 32.审核通过）")
    private Integer workStatus;

    /**
     * 退回意见（最新一个）
     */
    @Column(length = 1000)
    @ApiModelProperty("退回意见（最新一个）")
    @Length(message = "退回意见（最新一个）{validation.message.length}", max = 1000)
    private String backOpinion;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 退回次数
     */
    @ApiModelProperty("退回次数")
    private Integer backTimes;

    /**
     * 提交时间
     */
    @ApiModelProperty("提交时间")
    private Date submitTime;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;


    /**
     * 分析完成日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("分析完成日期")
    private Date finishTime;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;
}