package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;


/**
 * 频次指标数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/19
 * @since V100R001
 */
public interface SamplingFrequencyTestRepository extends IBaseJpaPhysicalDeleteRepository<DtoSamplingFrequencyTest, String> {

    /**
     * 按点位频次查询对应频次指标
     *
     * @param samplingFrequencyId 点位频次id
     * @return 返回对应点位频次的频次指标
     */
    List<DtoSamplingFrequencyTest> findBySamplingFrequencyId(String samplingFrequencyId);

    /**
     * 按点位查询对应频次指标
     *
     * @param sampleFolderId 点位id
     * @return 返回对应点位的频次指标
     */
    List<DtoSamplingFrequencyTest> findBySampleFolderId(String sampleFolderId);

    /**
     * 按点位集合查询频次指标
     *
     * @param sampleFolderIds 点位id集合
     * @return 返回对应点位集合的频次指标
     */
    List<DtoSamplingFrequencyTest> findBySampleFolderIdIn(Collection<String> sampleFolderIds);

    /**
     * 按频次id集合查询频次指标
     *
     * @param samplingFrequencyIds 频次id集合
     * @return 返回对应频次集合的频次指标
     */
    List<DtoSamplingFrequencyTest> findBySamplingFrequencyIdIn(Collection<String> samplingFrequencyIds);

    /**
     * 按点位以及频次查询频次指标（刘庄卓添加 2022/4/20）
     *
     * @param samplingFolderIds 点位id集合
     * @param samplingFrequencyIds 频次id集合
     * @return 返回对应频次集合的频次指标
     */
    List<DtoSamplingFrequencyTest> findBySampleFolderIdInAndSamplingFrequencyIdIn(Collection<String> samplingFolderIds,Collection<String> samplingFrequencyIds);

    /**
     * 删除对应点位下的频次指标
     *
     * @param sampleFolderId 点位id
     * @return 删除的条数
     */
    @Transactional
    @Modifying
    @Query("delete from DtoSamplingFrequencyTest where sampleFolderId = :sampleFolderId")
    Integer deleteBySampleFolderId(@Param("sampleFolderId")String sampleFolderId);

    @Transactional
    Integer deleteBySampleFolderIdIn(List<String> folderIds);


    /**
     * 删除对应频次id集合下的频次指标
     *
     * @param samplingFrequencyIds 频次id集合
     * @return 删除的条数
     */
    @Transactional
    @Modifying
    @Query("delete from DtoSamplingFrequencyTest where samplingFrequencyId in :samplingFrequencyIds")
    Integer deleteBySamplingFrequencyIdIn(@Param("samplingFrequencyIds") List<String> samplingFrequencyIds);

    /**
     * @param samplingFrequencyId 频次id
     * @param sampleFolderId      点位id
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSamplingFrequencyTest s set s.sampleFolderId = :sampleFolderId where s.samplingFrequencyId = :samplingFrequencyId")
    Integer updateSampleFolderId(@Param("samplingFrequencyId") String samplingFrequencyId,
                                 @Param("sampleFolderId") String sampleFolderId);

    /**
     * @param oldFrequencyId 旧频次id
     * @param newFrequencyId 新频次id
     * @param sampleFolderId 点位id
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoSamplingFrequencyTest s set s.sampleFolderId = :sampleFolderId,s.samplingFrequencyId = :newFrequencyId  where s.samplingFrequencyId = :oldFrequencyId")
    Integer updateFrequencyIdAndSampleFolderId(@Param("oldFrequencyId") String oldFrequencyId,
                                               @Param("newFrequencyId") String newFrequencyId,
                                               @Param("sampleFolderId") String sampleFolderId);
}