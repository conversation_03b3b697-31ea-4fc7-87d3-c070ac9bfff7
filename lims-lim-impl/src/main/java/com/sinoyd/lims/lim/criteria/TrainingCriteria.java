package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * 培训管理查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TrainingCriteria extends BaseCriteria {

    private static final long serialVersionUID = 1L;

    /**
     * 培训开始时间
     */
    private String startTime;

    /**
     * 培训结束时间
     */
    private String endTime;

    /**
     * 培训方式
     */
    private String way;

    /**
     * 培训人员
     */
    private List<String> participants;

    /**
     * 培训状态
     */
    private Integer status;

    /**
     * 培训名称，培训内容
     */
    private String trainName;


    @Override
    public String getCondition() {

        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        Calendar calendar = new GregorianCalendar();
        //开始时间查询
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.trainingDate >= :startTime");
            values.put("startTime", date);
        }
        //结束时间查询
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.trainingDate < :endTime");
            values.put("endTime", date);
        }

        if (StringUtil.isNotEmpty(this.way)) {
            condition.append(" and a.way = :way");
            values.put("way", this.way);
        }

        if (StringUtil.isNotNull(this.status)) {
            condition.append(" and a.status = :status");
            values.put("status", this.status);
        }
        if (StringUtil.isNotEmpty(this.trainName)) {
            condition.append(" and (a.trainingName like :trainingName or a.content like :trainingName)");
            values.put("trainingName", "%" + this.trainName + "%");
        }
        if (StringUtil.isNotEmpty(this.participants)) {
            condition.append(" and a.id in (select DISTINCT trainingId from DtoTraining2Participants where participantsId in :participants )");
            values.put("participants", this.participants);
        }
        // 筛选不需要审批 或者已经审批通过的数据
        condition.append(" and (a.auditInd = 0 or exists (select 1 from DtoOATaskRelation t2, DtoOATask t3 where a.id = t2.objectId and t2.taskId = t3.id and t3.dataStatus = 1))");
        condition.append(" and isDeleted = 0 ");
        return condition.toString();
    }
}
