package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 仪器导入关联字段
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoImportInstrumentExpend {
    @Excel(name = "部门")
    private String deptName;

    @Excel(name = "仪器类型")
    private String instrumentTypeName;

    @Excel(name = "状态")
    private String status;

    @Excel(name = "计量类型")
    private String meteringType;
}
