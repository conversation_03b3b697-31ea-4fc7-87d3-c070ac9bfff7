package com.sinoyd.lims.lim.dto.rcc;

import javax.persistence.*;

import com.sinoyd.lims.lim.entity.RecordConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.util.List;


/**
 * DtoRecordConfig实体
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_RecordConfig") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoRecordConfig extends RecordConfig {
   private static final long serialVersionUID = 1L;

    /**
     * 参数配置id集合
     */
   @Transient
   private List<String> paramsConfigIds;

    /**
     * 检测类型名称
     */
   @Transient
   private String sampleTypeName;

    /**
     * 检测类型大类id
     */
   @Transient
   private String bigSampleTypeId;

    /**
     * 报表路径
     */
   @Transient
   private String workPath;

    /**
     * 报表名称
     */
   @Transient
   private String workName;

    /**
     * 模版文档id(对应document表id)
     */
   @Transient
   private String templateDocId;

    /**
     * 源配置id
     */
   @Transient
   private String sourceRecordConfigId;
 }