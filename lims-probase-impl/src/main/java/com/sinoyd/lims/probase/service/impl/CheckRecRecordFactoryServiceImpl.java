package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecord;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.probase.service.CheckRecRecordFactoryService;
import com.sinoyd.lims.probase.service.CheckRecRecordService;
import com.sinoyd.lims.probase.service.CheckSampleService;
import org.springframework.stereotype.Service;

@Service
public class CheckRecRecordFactoryServiceImpl implements CheckRecRecordFactoryService {

    /**
     * 不同项目核对送样单状态
     *
     * @param type 包名
     * @param id   送样单id
     */
    @Override
    public void checkRecRecordByType(String type, String id) {
        checkRecRecord(type, id);
    }

    @Override
    public void checkRecRecordByType(String type, DtoReceiveSampleRecord receiveSampleRecord) {
        checkRecRecord(type, receiveSampleRecord);
    }

    public void checkRecRecord(String type, Object obj) {
        try {
            Class checkClass = Class.forName(type);//用描述作为包的命名
            CheckRecRecordService checkRecRecordService = SpringContextAware.getBean(checkClass);
            if (obj instanceof DtoReceiveSampleRecord) {
                checkRecRecordService.checkReceiveSampleRecord((DtoReceiveSampleRecord) obj);
            } else if (obj instanceof String) {
                checkRecRecordService.checkReceiveSampleRecord((String) obj);
            }
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            throw new BaseException(ex);
        }
    }
}
