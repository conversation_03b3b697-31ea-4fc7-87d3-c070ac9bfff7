package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoExamineTypeRecord;

import java.util.List;

/**
 *  考核类型填写记录repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/09/14
 */
public interface ExamineTypeRecordRepository extends IBaseJpaRepository<DtoExamineTypeRecord, String> {

    /**
     * 获取所有大小项对应的填写记录
     * @param ids 大小项标识列表
     * @return 填写记录列表
     */
    List<DtoExamineTypeRecord> findAllByExamineTypeIdIn(List<String> ids);

    /**
     * 获取所有大小项对应的填写记录
     * @param ids 大小项标识列表
     * @return 填写记录列表
     */
    List<DtoExamineTypeRecord> findAllByExamineTypeId(String ids);
}
