package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoSamplingCarConfig;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * SamplingCarConfig数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface SamplingCarConfigRepository extends IBaseJpaPhysicalDeleteRepository<DtoSamplingCarConfig, String> {

    /**
     * 查询对应关联下的采样车辆
     *
     * @param objectType 关联类型
     * @param objectId 关联id
     * @return 对应的采样车辆
     */
    List<DtoSamplingCarConfig> findByObjectTypeAndObjectId(Integer objectType,String objectId);

    /**
     * 删除对应关联下的采样车辆
     *
     * @param objectId 关联id
     */
    void deleteByObjectId(String objectId);
}