package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * QualityControl实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "QualityControl")
@Data
public class QualityControl extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public QualityControl() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 关联样品Id（平行样的关联样品）
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("关联样品Id（平行样的关联样品）")
    private String associateSampleId;

    /**
     * 质控等级（枚举EnumQCGrade：0.外部质控  1.内部质控）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("质控等级（枚举EnumQCGrade：0.外部质控  1.内部质控）")
    private Integer qcGrade;

    /**
     * 质控类型（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("质控类型（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）")
    private Integer qcType;

    /**
     * 质控值（标准样的值/加标的值/平行样为空）
     */
    @Column(length = 50)
    @ApiModelProperty("质控值（标准样的值/加标的值/平行样为空）")
    @Length(message = "质控值（标准样的值/加标的值/平行样为空）{validation.message.length}", max = 50)
    private String qcValue;

    /**
     * 加标体积
     */
    @Column(length = 50)
    @ApiModelProperty("加标体积")
    @Length(message = "加标体积{validation.message.length}", max = 50)
    private String qcVolume;

    /**
     * 范围低点
     */
    @Column(length = 50)
    @ApiModelProperty("范围低点")
    private String rangeLow;

    /**
     * 范围高点
     */
    @Column(length = 50)
    @ApiModelProperty("范围高点")
    private String rangeHigh;

    /**
     * 不确定度类型
     */
    @ApiModelProperty("不确定度类型")
    private Integer uncertainType;

    /**
     * 添加质控人员id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("添加质控人员id")
    private String qaId;

    /**
     * 添加质控时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("添加质控时间")
    private Date qcTime;

    /**
     * 测定值
     */
    @Column(length = 50)
    @ApiModelProperty("测定值")
    @Length(message = "测定值{validation.message.length}", max = 50)
    private String qcTestValue;

    /**
     * 样值（可以是质量的也可以是浓度的）
     */
    @Column(length = 50)
    @ApiModelProperty("样值（可以是质量的也可以是浓度的）")
    @Length(message = "样值（可以是质量的也可以是浓度的）{validation.message.length}", max = 50)
    private String realSampleTestValue;

    /**
     * 标样编号
     */
    @Column(length = 50)
    @ApiModelProperty("标样编号")
    @Length(message = "标样编号{validation.message.length}", max = 50)
    private String qcCode;

    /**
     * 加标液浓度
     */
    @Column(length = 50)
    @ApiModelProperty("加标液浓度")
    @Length(message = "加标液浓度{validation.message.length}", max = 50)
    private String qcConcentration;

    /**
     * 原样的检测结果（找限值范围）
     */
    @Column(length = 50)
    @ApiModelProperty("原样的检测结果（找限值范围）")
    @Length(message = "原样的检测结果（找限值范围）{validation.message.length}", max = 50)
    private String qcOriginValue;

    /**
     * 标样的有效期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("标样的有效期")
    private Date qcValidDate;

    /**
     * 标样的有效期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("标样的配置日期")
    private Date qcStandardDate;

    /**
     * 标样id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("标样id")
    private String qcStandardId;

    @Column(length = 50)
    @ApiModelProperty("标准溶液浓度")
    @Length(message = "标准溶液浓度{validation.message.length}", max = 50)
    private String ssConcentration;

    @Column(length = 50)
    @ApiModelProperty("定容体积")
    //@NotBlank(message = "定容体积{validation.message.blank}")
    @Length(message = "定容体积{validation.message.length}", max = 50)
    private String constantVolume;

    @Column(length = 50)
    @ApiModelProperty("标准溶液浓度量纲标识")
    private String ssConcentrationDimensionId;

    @Column(length = 50)
    @ApiModelProperty("定容体积量纲标识")
    private String constantVolumeDimensionId;

    @Column(length = 500)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 500)
    private String remark;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    @Column(length = 50)
    @ApiModelProperty("加标体积/标准溶液加入体积量纲id")
    private String qcVolumeDimensionId;

    @Column(length = 50)
    @ApiModelProperty("加入标准量/标准物质加入量/替代物加入量量纲id")
    private String qcValueDimensionId;

    @Column(length = 50)
    @ApiModelProperty("测定值量纲id")
    private String qcTestValueDimensionId;

    @Column(length = 50)
    @ApiModelProperty("样值量纲id")
    private String realSampleTestValueDimensionId;

    @Column(length = 50)
    @ApiModelProperty("加标液浓度量纲id")
    private String qcConcentrationDimensionId;

    /**
     * 稀释水是否接种（1.是，2.否）
     */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("稀释水是否接种（1.是，2.否）")
    private Integer isVaccinate;

    /**
     * 创建人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;
}