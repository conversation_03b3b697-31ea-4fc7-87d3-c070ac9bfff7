package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Table;
import javax.validation.constraints.Pattern;

/**
 * 测试项目扩展导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/6
 * @since V100R001
 */
@Table(name = "TB_LIM_TestExpand")
@Data
public class DtoExportTestExpand extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id;

    /**
     * 测试Id（Guid）
     */
    @Excel(name = "测试项目Id", orderNum = "20")
    private String testId;

    /**
     * 样品类型Id（Guid）
     */
    @Excel(name = "样品类型Id", orderNum = "30")
    private String sampleTypeId;

    /**
     * 有效位数
     */
    @Excel(name = "有效位数", orderNum = "40")
    @Pattern(regexp = "^-?\\d+$", message = "有效位数必须为整数")
    private String mostSignificance;

    /**
     * 小数位数
     */
    @Excel(name = "小数位数", orderNum = "50")
    @Pattern(regexp = "^-?\\d+$", message = "小数位数必须为整数")
    private String mostDecimal;

    /**
     * 检出限
     */
    @Excel(name = "检出限", orderNum = "60")
    private String examLimitValue;

    /**
     * 单位（Guid）
     */
    @Excel(name = "单位", orderNum = "70")
    private String dimensionId;

    /**
     * 小于检出限（预留）
     */
    @Excel(name = "小于检出限", orderNum = "80")
    private String examLimitValueLess;

    /**
     * 周期数
     */
    @Excel(name = "周期数", orderNum = "90")
    @Pattern(regexp = "^-?\\d+$", message = "周期数必须为整数")
    private String timesOrder;

    /**
     * 样品数量
     */
    @Excel(name = "样品数量", orderNum = "90")
    @Pattern(regexp = "^-?\\d+$", message = "样品数量必须为整数")
    private String samplePeriod;

    /**
     * 测定下限
     */
    @Excel(name = "测定下限", orderNum = "100")
    private String lowerLimit;
}
