package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumCostInfoModule;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumStatus;



/**
 * 费用查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年11月5日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CostInfoCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 开始时间
    */
    private String startTime;

    /**
    * 结束时间
    */
    private String endTime;

    /**
    * 登记人id
    */
    private String inceptPersonId;

    /**
    * 关键字（项目编号、项目名称、委托方）
    */
    private String key;

    /**
     * 当前操作人id
     */
    private String currentPersonId;

    /**
    * 模块编码
    */
    private String module = EnumCostInfoModule.费用核算.getCode();

    /**
    * 状态
    */
    private Integer status = EnumStatus.所有.getValue();

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and c.projectId = p.id");
        condition.append(" and c.id = s.costInfoId");

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and p.inceptTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and p.inceptTime < :endTime");
            values.put("endTime", to);
        }
        if (StringUtil.isNotEmpty(this.inceptPersonId) && !UUIDHelper.GUID_EMPTY.equals(this.inceptPersonId)) {
            condition.append(" and p.inceptPersonId = :inceptPersonId");
            values.put("inceptPersonId", this.inceptPersonId);
        }

        condition.append(" and s.module = :module");
        values.put("module", this.module);

        if (StringUtil.isNotEmpty(this.currentPersonId) && !UUIDHelper.GUID_EMPTY.equals(this.currentPersonId)) {
            condition.append(" and s.currentPersonId = :currentPersonId");
            values.put("currentPersonId", this.currentPersonId);
        }

        if (!EnumStatus.所有.getValue().equals(this.status)) {
            condition.append(" and s.status = :status");
            values.put("status", this.status);
        }

        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (p.projectCode like :key or p.projectName like :key or p.customerName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        return condition.toString();
    }
}