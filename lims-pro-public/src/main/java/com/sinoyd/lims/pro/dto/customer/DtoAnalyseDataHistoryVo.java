package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 实验室分析，历史数据参数接收实体
 *
 * <AUTHOR>
 * @date V1.0.0 2023/12/28
 * @version: V100R001
 */
@Data
public class DtoAnalyseDataHistoryVo {

    /**
     * 工作单id
     */
    private String worksheetFolderId;

    /**
     * 分析开始日期
     */
    private String startAnalyzeTime;

    /**
     * 分析结束日期
     */
    private String endAnalyzeTime;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 点位名称
     */
    private String watchSpot;

    /**
     * 分析项目
     */
    private String redAnalyzeItemName;
}
