package com.sinoyd.lims.pro.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * SamplingAchievementDetails实体
 * <AUTHOR>
 * @version V1.0.0 2023/3/13
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description="SamplingAchievementDetails")
@Data
public class AnalyseAchievementDetails implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 人员绩效id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("人员绩效id")
    private String achievementId;

    /**
     * 样品id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("样品id")
    private String sampleId;

    /**
     * 样品编号
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("样品编号")
    @Excel(name = "样品编号",needMerge = true,orderNum = "20",width = 11)
    private String sampleCode;

    /**
     * 测试项目id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试项目id")
    private String testId;

    /**
     * 分析项目名称
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("分析项目名称")
    @Excel(name = "分析项目",needMerge = true,orderNum = "30",width = 11)
    private String analyzeItemName;

    /**
     * 分析方法名称
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("分析方法名称")
    @Excel(name = "分析方法",needMerge = true,orderNum = "40",width = 11)
    private String analyzeMethodName;

    /**
     * 标准编号
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("标准编号")
    @Excel(name = "标准编号",needMerge = true,orderNum = "50",width = 11)
    private String countryStandard;

    /**
     * 分析人员id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("分析人员id")
    private String analystId;

    /**
     * 分析人员名称
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("分析人员名称")
    @Excel(name = "分析人",needMerge = true,orderNum = "60",width = 11)
    private String analystName;

    /**
     * 数据分析时间
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("数据分析时间")
    @Excel(name = "分析日期",exportFormat = "yyyy-MM-dd",needMerge = true,orderNum = "70",width = 11)
    private Date analyzeTime;

    /**
     * 检测类型id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("检测类型id")
    private String sampleTypeId;

    /**
     * 状态
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("状态")
    private String status;

    /**
     * 产值
     */
    @ApiModelProperty("产值")
    @Excel(name = "产值",needMerge = true,orderNum = "90",width = 11)
    private BigDecimal totalAmount;

}
