package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.TestOperateLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoTestOperateLog实体
 * <AUTHOR>
 * @version V1.0.0 2024/02/20
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tb_lim_testoperatelog")
@Data
@DynamicInsert
public class DtoTestOperateLog extends TestOperateLog {
    /**
     * 操作人名称
     */
    @Transient
    private String operator;

    /**
     * 操作类型文本
     */
    @Transient
    private String operateTypeText;
}
