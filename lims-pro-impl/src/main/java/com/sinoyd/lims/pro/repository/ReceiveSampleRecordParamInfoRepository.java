package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoReceiveSampleRecordParamInfo;
import com.sinoyd.lims.pro.dto.DtoReportFolderInfo;

import java.util.List;


/**
 * 送样单参数访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/09/19
 * @since V100R001
 */
public interface ReceiveSampleRecordParamInfoRepository extends IBaseJpaPhysicalDeleteRepository<DtoReceiveSampleRecordParamInfo, String> {

    /**
     * 根据模板id查询
     *
     * @param templateId 模板id
     * @return 模板参数列表
     */
    List<DtoReceiveSampleRecordParamInfo> findByTemplateId(String templateId);

    /**
     * 根据模板id列表查询
     *
     * @param templateIdList 模板id列表
     * @return 模板参数列表
     */
    List<DtoReceiveSampleRecordParamInfo> findByTemplateIdIn(List<String> templateIdList);
}