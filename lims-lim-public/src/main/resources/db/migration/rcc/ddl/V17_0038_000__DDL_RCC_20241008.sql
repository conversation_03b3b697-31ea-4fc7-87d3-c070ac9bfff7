
DROP PROCEDURE IF EXISTS `add_col`;
<PERSON><PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE add_col()
BEGIN
	IF(NOT EXISTS (SELECT column_name FROM information_schema.COLUMNS WHERE table_schema = DATABASE() AND table_name = 'TB_LIM_RecordConfig' AND column_name = ('orderNum'))) THEN
        ALTER TABLE TB_LIM_RecordConfig ADD COLUMN orderNum int(0) NOT NULL DEFAULT 0 COMMENT '排序值';
END IF;
END $$
DELIMITER;
CALL add_col ();
DROP PROCEDURE IF EXISTS `add_col`;