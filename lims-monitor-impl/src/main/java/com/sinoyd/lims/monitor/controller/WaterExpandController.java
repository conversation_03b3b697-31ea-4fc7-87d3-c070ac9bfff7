package com.sinoyd.lims.monitor.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.monitor.service.WaterExpandService;
import com.sinoyd.lims.monitor.criteria.WaterExpandCriteria;
import com.sinoyd.lims.monitor.dto.rcc.DtoWaterExpand;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * WaterExpand服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @Api(tags = "示例: WaterExpand服务")
 @RestController
 @RequestMapping("api/monitor/waterExpand")
 public class WaterExpandController extends BaseJpaController<DtoWaterExpand, String,WaterExpandService> {


    /**
     * 分页动态条件查询WaterExpand
     * @param waterExpandCriteria 条件参数
     * @return RestResponse<List<WaterExpand>>
     */
     @ApiOperation(value = "分页动态条件查询WaterExpand", notes = "分页动态条件查询WaterExpand")
     @GetMapping
     public RestResponse<List<DtoWaterExpand>> findByPage(WaterExpandCriteria waterExpandCriteria) {
         PageBean<DtoWaterExpand> pageBean = super.getPageBean();
         RestResponse<List<DtoWaterExpand>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, waterExpandCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询WaterExpand
     * @param id 主键id
     * @return RestResponse<DtoWaterExpand>
     */
     @ApiOperation(value = "按主键查询WaterExpand", notes = "按主键查询WaterExpand")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoWaterExpand> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoWaterExpand> restResponse = new RestResponse<>();
         DtoWaterExpand waterExpand = service.findOne(id);
         restResponse.setData(waterExpand);
         restResponse.setRestStatus(StringUtil.isNull(waterExpand) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增WaterExpand
     * @param waterExpand 实体列表
     * @return RestResponse<DtoWaterExpand>
     */
     @ApiOperation(value = "新增WaterExpand", notes = "新增WaterExpand")
     @PostMapping
     public RestResponse<DtoWaterExpand> create(@RequestBody @Validated DtoWaterExpand waterExpand) {
         RestResponse<DtoWaterExpand> restResponse = new RestResponse<>();
         restResponse.setData(service.save(waterExpand));
         return restResponse;
      }

     /**
     * 新增WaterExpand
     * @param waterExpand 实体列表
     * @return RestResponse<DtoWaterExpand>
     */
     @ApiOperation(value = "修改WaterExpand", notes = "修改WaterExpand")
     @PutMapping
     public RestResponse<DtoWaterExpand> update(@RequestBody @Validated DtoWaterExpand waterExpand) {
         RestResponse<DtoWaterExpand> restResponse = new RestResponse<>();
         restResponse.setData(service.update(waterExpand));
         return restResponse;
      }

    /**
     * "根据id批量删除WaterExpand
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除WaterExpand", notes = "根据id批量删除WaterExpand")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }