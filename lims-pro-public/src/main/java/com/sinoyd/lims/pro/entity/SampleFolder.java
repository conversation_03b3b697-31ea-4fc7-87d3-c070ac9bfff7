package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * SampleFolder实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SampleFolder")
 @Data
 public  class SampleFolder implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  SampleFolder() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目id")
	private String projectId;
    
    /**
    * 子项目的id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("子项目的id")
	private String subProjectId;
    
    /**
    * 点位名称
    */
    @Column(length=100)
    @ApiModelProperty("点位名称")
    @Length(message = "点位名称{validation.message.length}", max = 100)
	private String watchSpot;
    
    /**
    * 断面id（断面扩展id）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("断面id（断面扩展id）")
	private String fixedPointId;
    
    /**
    * 检测类型id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("检测类型id")
	private String sampleTypeId;
    
    /**
    * 点位号
    */
    @Column(length=50)
    @ApiModelProperty("点位号")
    @Length(message = "点位号{validation.message.length}", max = 50)
	private String folderCode;
    
    /**
    * 费用系数（默认1）
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("费用系数（默认1）")
	private BigDecimal chargeRate;
    
    /**
    * 点位类型（常量 PRO_FolderType：进口、出口）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("点位类型（常量 PRO_FolderType：进口、出口）")
	private String folderTypeId;
    
    /**
    * 点位类型名称
    */
    @Column(length=50)
    @ApiModelProperty("点位类型名称")
    @Length(message = "点位类型名称{validation.message.length}", max = 50)
	private String folderTypeName;
    
    /**
    * 分析项目
    */
    @Column(length=1000)
    @ApiModelProperty("分析项目")
    @Length(message = "分析项目{validation.message.length}", max = 1000)
	private String redAnalyzeItems;
    
    /**
    * 经度（计划）
    */
    @Column(length=20)
    @ApiModelProperty("经度（计划）")
    @Length(message = "经度（计划）{validation.message.length}", max = 20)
	private String lon;
    
    /**
    * 纬度（计划）
    */
    @Column(length=20)
    @ApiModelProperty("纬度（计划）")
    @Length(message = "纬度（计划）{validation.message.length}", max = 20)
	private String lat;
    
    /**
    * 地图级别（默认1）
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("地图级别（默认1）")
	private Integer grade;
    
    /**
    * 排气管高度
    */
    @Column(length=20)
    @ApiModelProperty("排气管高度")
    @Length(message = "排气管高度{validation.message.length}", max = 20)
	private String exhaustPipeHeight;
    
    /**
    *  预留分包状态（枚举EnumOutSourcing：0.不分包1.全部分包2.部分分包）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty(" 预留分包状态（枚举EnumOutSourcing：0.不分包1.全部分包2.部分分包）")
	private Integer isOutsourcing;
    
    /**
    * 工艺设备名称
    */
    @Column(length=100)
    @ApiModelProperty("工艺设备名称")
    @Length(message = "工艺设备名称{validation.message.length}", max = 100)
	private String craftFacilityName;
    
    /**
    * 净化设备名称
    */
    @Column(length=100)
    @ApiModelProperty("净化设备名称")
    @Length(message = "净化设备名称{validation.message.length}", max = 100)
	private String purificateFacilityName;
    
    /**
    * 污染源种类
    */
    @Column(length=100)
    @ApiModelProperty("污染源种类")
    @Length(message = "污染源种类{validation.message.length}", max = 100)
	private String pollutionType;
    
    /**
    * 锅炉制造单位
    */
    @Column(length=100)
    @ApiModelProperty("锅炉制造单位")
    @Length(message = "锅炉制造单位{validation.message.length}", max = 100)
	private String boilerMakeUnit;
    
    /**
    * 名称(型号)
    */
    @Column(length=100)
    @ApiModelProperty("名称(型号)")
    @Length(message = "名称(型号){validation.message.length}", max = 100)
	private String equipmentTypeName;
    
    /**
    * 锅炉投运日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("锅炉投运日期")
	private Date boilerUseDate;
    
    /**
    * 烟囱高度
    */
    @Column(length=20)
    @ApiModelProperty("烟囱高度")
    @Length(message = "烟囱高度{validation.message.length}", max = 20)
	private String chimneyHeight;
    
    /**
    * 净化设备制造单位
    */
    @Column(length=100)
    @ApiModelProperty("净化设备制造单位")
    @Length(message = "净化设备制造单位{validation.message.length}", max = 100)
	private String purificateFacilityUnit;
    
    /**
    * 净化设备型号
    */
    @Column(length=100)
    @ApiModelProperty("净化设备型号")
    @Length(message = "净化设备型号{validation.message.length}", max = 100)
	private String purificateFacilityType;
    
    /**
    * 净化设备投运日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("净化设备投运日期")
	private Date purificateFacilityUseDate;
    
    /**
    * 燃料类型
    */
    @Column(length=50)
    @ApiModelProperty("燃料类型")
    @Length(message = "燃料类型{validation.message.length}", max = 50)
	private String fuelType;
    
    /**
    * 炉窖设备编号
    */
    @Column(length=50)
    @ApiModelProperty("炉窖设备编号")
    @Length(message = "炉窖设备编号{validation.message.length}", max = 50)
	private String stoveFacilityCode;
    
    /**
    * 工艺设备/启用时间
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("工艺设备/启用时间")
	private Date craftFacilityUseDate;
    
    /**
    * 是否进行折算
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否进行折算")
	private Boolean isTransition;
    
    /**
    * 受检单位ID
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("受检单位ID")
	private String inspectedEntId;
    
    /**
    * 受检单位
    */
    @Column(length=100)
    @ApiModelProperty("受检单位")
    @Length(message = "受检单位{validation.message.length}", max = 100)
	private String inspectedEnt;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;

    /**
     * 创建人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;
 }