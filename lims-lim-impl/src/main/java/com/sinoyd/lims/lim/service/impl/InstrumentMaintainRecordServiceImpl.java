package com.sinoyd.lims.lim.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentMaintainRecord;
import com.sinoyd.lims.lim.repository.lims.InstrumentMaintainRecordRepository;
import com.sinoyd.lims.lim.service.InstrumentMaintainRecordService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 仪器维护记录接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
@Service
public class InstrumentMaintainRecordServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoInstrumentMaintainRecord, String, InstrumentMaintainRecordRepository>
        implements InstrumentMaintainRecordService {

    @Autowired
    @Lazy
    private InstrumentService instrumentService;

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoInstrumentMaintainRecord> page, BaseCriteria criteria) {

        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrumentMaintainRecord x");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select x");

        super.findByPage(page, criteria);
    }
    /**
     * 维护记录同步更新基础信息
     */
    @Transactional
    @Override
    public DtoInstrumentMaintainRecord update(DtoInstrumentMaintainRecord entity) {
        // 仪器对象
        DtoInstrument insModel = instrumentService.findOne(entity.getInstrumentId());
        insModel.setMaintenanceDate(entity.getEndTime()); // 维护日期(截止日期)
        insModel.setMaintenanceContent(entity.getMaintainContent()); // 维护内容
        instrumentService.update(insModel); // 更新基础信息
        
        return super.update(entity);
    }

    /**
     * 新增维护信息
     */
    @Transactional
    @Override
    public DtoInstrumentMaintainRecord save(DtoInstrumentMaintainRecord entity) {

        // 仪器对象
        DtoInstrument insModel = instrumentService.findOne(entity.getInstrumentId());
        insModel.setMaintenanceDate(entity.getEndTime()); // 维护日期(截止日期)
        insModel.setMaintenanceContent(entity.getMaintainContent()); // 维护内容
        instrumentService.update(insModel); // 更新基础信息

        return super.save(entity);
    }
    
    /**
     * 批量新增
     */
    @Transactional
    @Override
    public List<DtoInstrumentMaintainRecord> save(Collection<DtoInstrumentMaintainRecord> entities) {

        List<DtoInstrumentMaintainRecord> saves = new ArrayList<>();

        entities.forEach(x -> saves.add(this.save(x)));

        return saves;
    }

    /***
     * 获取维护单位
     * @return
     */
    @Override
    public List<DtoInstrumentMaintainRecord> getMaintainDeptList() {
        List<DtoInstrumentMaintainRecord> list = new ArrayList<>();

        List<String> names = repository.findMaintainDeptName();

        for (String name : names) {
            DtoInstrumentMaintainRecord item = new DtoInstrumentMaintainRecord();
            item.setMaintainDeptName(name);
            list.add(item);
        }
        list = list.stream().sorted(Comparator.comparing(DtoInstrumentMaintainRecord::getMaintainDeptName).reversed()).collect(Collectors.toList());

        return list;
    }

}