package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.customer.DtoImportParamsConfig;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.transform.ImportCommonCheckImpl;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *   原始记录单数据参数导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2023/10/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataParamsConfigVerifyHandler extends ImportCommonCheckImpl implements IExcelVerifyHandler<DtoImportParamsConfig> {

    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    /**
     *   excel数据重复校验容器
     */
    private List<DtoImportParamsConfig> sheetExistDataList;

    private List<DtoTest> allTestList;

    private List<DtoParamsTestFormula> allParamsTestFormulaList;

    private List<DtoRecordConfig2Test> allRecordConfig2TestList;

    private List<DtoSampleType> allSampleTypeList;

    private List<DtoParamsFormula> allParamsFormulaList;

    private String recordConfigId;

    @SneakyThrows
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportParamsConfig importParamsConfig) {
        //导入数据处理,跳过空行,数据去除前后空格
        try {
            if (importUtils.checkObjectIsNull(importParamsConfig)) {
                return new ExcelVerifyHandlerResult(true);
            }
            importUtils.strToTrim(importParamsConfig);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + (importParamsConfig.getRowNum()+1) + "行数据校验有误");
        //必填校验
        importUtils.checkIsNull(result, importParamsConfig.getAnalyzeItem(), "分析项目", failStr);
        importUtils.checkIsNull(result, importParamsConfig.getAnalyzeMethod(), "分析项目", failStr);
        importUtils.checkIsNull(result, importParamsConfig.getSampleType(), "检测类型", failStr);
        importUtils.checkIsNull(result, importParamsConfig.getParamsConfigName(), "原始记录单参数名称", failStr);
        importUtils.checkIsNull(result, importParamsConfig.getFormula(), "公式", failStr);
        //避免因未填导致的问题
        if(result.isSuccess()){
            //本sheet内数据重复
            checkSeetDataRepeat(result,failStr,sheetExistDataList,importParamsConfig, Arrays.asList("paramsConfigName","analyzeItem","analyzeMethod","sampleType","formula"));
            //判定测试项目是否存在
            DtoTest existTest = checkIsExistTest(result,failStr,importParamsConfig,allTestList,allSampleTypeList);
            if(existTest!=null){
                //判定列参数是否正确
                checkColumnParamsRight(result,failStr,importParamsConfig,existTest,allParamsFormulaList,allParamsTestFormulaList);
            }
            //更新重复校验容器
            sheetExistDataList.add(importParamsConfig);
        }
        //构建异常信息
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }

    /**
     * 校验列参数是否正确
     * @param result                       校验结果
     * @param failStr                      异常信息
     * @param importParamsConfig           导入实体
     * @param allParamsTestFormulaList     测试项目公式参数
     */
    private void checkColumnParamsRight(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportParamsConfig importParamsConfig,
                                        DtoTest existTest,List<DtoParamsFormula> allParamsFormulaList,List<DtoParamsTestFormula> allParamsTestFormulaList) {
        DtoSampleType dtoSampleType = allSampleTypeList.stream().filter(s->s.getTypeName().equals(importParamsConfig.getSampleType())).findFirst().orElse(null);
        if(dtoSampleType!=null){
            DtoParamsFormula dtoParamsFormula = allParamsFormulaList.stream().filter(f->existTest.getId().equals(f.getObjectId())
                                                                                       &&dtoSampleType.getId().equals(f.getSampleTypeId())
                                                                                       && EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue().equals(f.getObjectType()))
                                                                                     .findFirst().orElse(null);
            if(dtoParamsFormula!=null){
                List<String> existParamsTestFormulaNameList = allParamsTestFormulaList.stream().filter(t->dtoParamsFormula.getId().equals(t.getObjId()))
                                                                                               .map(DtoParamsTestFormula::getParamsName)
                                                                                               .collect(Collectors.toList());
                String[] paramNameList = importParamsConfig.getColumnParams().split("、");
                for (String name:paramNameList) {
                    String tempName = name.replace("[","").replace("]","");
                    if(!existParamsTestFormulaNameList.contains(tempName)){
                        result.setSuccess(false);
                        failStr.append("； 不存在测试项目公式参数").append(name);
                    }
                }
            }
            else{
                result.setSuccess(false);
                failStr.append("； 测试项目公式未配置");
            }
        }
    }

    /**
     * 判定测试项目是否存在
     * @param result                     校验结果
     * @param failStr                    校验错误数据
     * @param importParamsConfig    导入实体
     * @param allTestList                所有测试项目
     */
    private DtoTest checkIsExistTest(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportParamsConfig importParamsConfig, List<DtoTest> allTestList,List<DtoSampleType> allSampleTypeList) {
        DtoTest dtoTest = null;
        DtoSampleType dtoSampleType = allSampleTypeList.stream().filter(s->s.getTypeName().equals(importParamsConfig.getSampleType())).findFirst().orElse(null);
        if(dtoSampleType!=null){
             dtoTest = allTestList.stream().filter(t->t.getRedAnalyzeItemName().equals(importParamsConfig.getAnalyzeItem())
                    &&t.getRedAnalyzeMethodName().equals(importParamsConfig.getAnalyzeMethod())
                    &&dtoSampleType.getId().equals(t.getSampleTypeId()))
                    .findFirst().orElse(null);
            if(dtoTest==null){
                result.setSuccess(false);
                failStr.append("； 测试项目在系统中不存在");
            }
        }
        else{
            result.setSuccess(false);
            failStr.append("； 检测类型").append(importParamsConfig.getSampleType()).append("在系统中不存在");
        }
        return dtoTest;
    }
}
