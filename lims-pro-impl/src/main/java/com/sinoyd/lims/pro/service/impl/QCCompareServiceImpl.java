package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRemindConfig;
import com.sinoyd.lims.lim.service.TestQCRemindConfigService;
import com.sinoyd.lims.pro.criteria.QCCompareStatisticsCriteria;
import com.sinoyd.lims.pro.dto.customer.DtoQCCompareCount;
import com.sinoyd.lims.pro.dto.customer.DtoQCCompareCurve;
import com.sinoyd.lims.pro.service.QCCompareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * 质控比例的统计
 *
 * <AUTHOR>
 * @version V1.0.0 2020/02/11
 * @since V100R001
 */
@Service
public class QCCompareServiceImpl implements QCCompareService {

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    @Lazy
    private TestQCRemindConfigService testQCRemindConfigService;

    @Override
    public List<DtoQCCompareCurve>  findQCCompareStatistics(BaseCriteria baseCriteria) {
        StringBuilder select = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoQCCompareCount");
        select.append("(b.analyzeTime,count(distinct a.id) as sampleCount)");
        select.append(" from DtoSample a,DtoAnalyseData b,DtoSampleType c where 1=1 and b.isDeleted = 0 ");
        QCCompareStatisticsCriteria qcCompareStatisticsCriteria = (QCCompareStatisticsCriteria) baseCriteria;

        //先计算每天的原样样品数
        QCCompareStatisticsCriteria yyCriteria = qcCompareStatisticsCriteria;
        yyCriteria.setIsZk(false);
        List<DtoQCCompareCount> yyList = commonRepository.find(select.toString(), yyCriteria);

        //再计算每天的质控样品数
        QCCompareStatisticsCriteria zkCriteria = qcCompareStatisticsCriteria;
        zkCriteria.setIsZk(true);
        List<DtoQCCompareCount> zkList = commonRepository.find(select.toString(), zkCriteria);

        //开始时间
        String startTime = yyCriteria.getStartTime();

        //结束时间
        String endTime = yyCriteria.getEndTime();

        //质控等级
        Integer qcGrade = yyCriteria.getQcGrade();

        //质控类型
        Integer qcType = yyCriteria.getQcType();

        //测试项目id
        String testId = yyCriteria.getTestId();

        //天数
        Integer day = yyCriteria.getDay();

        Date from = new Date();//开始时间
        Date to;//结束时间
        Calendar startCalendar = Calendar.getInstance();//定义日期实例
        Calendar endCalendar = Calendar.getInstance();
        if (StringUtils.isNotNullAndEmpty(startTime)) {
            from = DateUtil.stringToDate(startTime, DateUtil.YEAR);
        }
        if (StringUtils.isNotNullAndEmpty(endTime)) {
            to = DateUtil.stringToDate(endTime, DateUtil.YEAR);
            endCalendar.setTime(to);
            endCalendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        startCalendar.setTime(from);//设置日期起始时间
        Integer standardValue = 0;
        DtoTestQCRemindConfig testQCRemindConfig = testQCRemindConfigService.findByTestIdAndQcTypeAndQCGrade(testId, qcType, qcGrade);
        if (StringUtil.isNotNull(testQCRemindConfig)) {
            standardValue = testQCRemindConfig.getQcRemindPercent();
        }
        List<DtoQCCompareCurve> curves = new ArrayList<>();
        //计算最后的曲线数据
        Integer i = 0;
        while (startCalendar.getTime().before(endCalendar.getTime())) {//判断是否到结束日期
            //未加之前的日期
            Calendar sCalendar = Calendar.getInstance();
            sCalendar.setTime(startCalendar.getTime());
            startCalendar.add(Calendar.DATE, day);
            //加完天数后的日期
            Calendar eCalendar = Calendar.getInstance();
            eCalendar.setTime(startCalendar.getTime());
            //原样数
            Long yyCount = yyList.stream().filter(p -> p.getAnalyzeTime().compareTo(sCalendar.getTime()) >= 0
                    && p.getAnalyzeTime().compareTo(eCalendar.getTime()) < 0).mapToLong(DtoQCCompareCount::getSampleCount).sum();

            //质控数
            Long zkCount = zkList.stream().filter(p -> p.getAnalyzeTime().compareTo(sCalendar.getTime()) >= 0
                    && p.getAnalyzeTime().compareTo(eCalendar.getTime()) < 0).mapToLong(DtoQCCompareCount::getSampleCount).sum();
            Double d = 0d;
            if (yyCount != 0) {
                d = (zkCount / Double.parseDouble(yyCount.toString())) * 100;
            }
            Calendar curveCalendar = Calendar.getInstance();
            curveCalendar.setTime(from);
            curveCalendar.add(Calendar.DATE, i * day);

            Calendar nextCurveCalendar = Calendar.getInstance();
            nextCurveCalendar.setTime(from);
            nextCurveCalendar.add(Calendar.DATE, (i + 1) * day);
            DtoQCCompareCurve curve = new DtoQCCompareCurve();
            curve.setRealityValue(d);
            curve.setStandardValue(standardValue);
            // todo 数据要进行修约，保留2位
            if (nextCurveCalendar.getTime().compareTo(DateUtil.stringToDate(endTime, DateUtil.YEAR)) > 0) {
                curve.setDateTime(DateUtil.dateToString(DateUtil.stringToDate(endTime, DateUtil.YEAR), DateUtil.YEAR));
            } else {
                curve.setDateTime(DateUtil.dateToString(curveCalendar.getTime(), DateUtil.YEAR));
            }
            curves.add(curve);
            i++;
        }
        return curves;
    }
}
