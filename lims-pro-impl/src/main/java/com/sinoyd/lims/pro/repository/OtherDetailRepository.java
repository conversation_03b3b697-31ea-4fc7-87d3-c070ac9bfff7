package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoOtherDetail;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import java.util.List;


/**
 * OtherDetail数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
public interface OtherDetailRepository extends IBaseJpaRepository<DtoOtherDetail, String> {

    /**
     * 根据订单id获取其他费用
     * @param orderId 订单id
     * @return 其他费用
     */
    List<DtoOtherDetail> findByOrderId(String orderId);
}