package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * ReportConfig2Module实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ReportConfig2Module")
@Data
public class ReportConfig2Module implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 报告配置id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("报告配置id")
    private String reportConfigId;

    /**
     * 报告组件id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("报告组件id")
    private String reportModuleId;

}