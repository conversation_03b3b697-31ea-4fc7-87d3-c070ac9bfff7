package com.sinoyd.base.controller;

import com.sinoyd.base.dto.lims.DtoLogForDocument;
import com.sinoyd.base.service.LogForDocumentService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 附件日志
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-29
 * @since V100R001
 */
@Api(tags = "附件日志")
@RestController
@RequestMapping("/api/base/logfordocument")
@Validated
public class LogForDocumentController extends BaseJpaController<DtoLogForDocument, String, LogForDocumentService> {

    /**
     * 根据documentId查询仪器
     *
     * @param documentId 附件id
     * @return 日志集合
     */
    @ApiOperation(value = "根据id查询仪器", notes = "根据id查询仪器")
    @GetMapping("documentlog/{documentId}")
    public RestResponse<List<DtoLogForDocument>> find(@PathVariable(name = "documentId") String documentId) {
        RestResponse<List<DtoLogForDocument>> restResp = new RestResponse<>();
        List<DtoLogForDocument> logForDocumentList = service.findByDocumentId(documentId);
        restResp.setData(logForDocumentList);
        restResp.setRestStatus(StringUtil.isNull(logForDocumentList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }
}
