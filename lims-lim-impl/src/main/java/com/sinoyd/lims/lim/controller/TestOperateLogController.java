package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.TestOperateLogCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoTestOperateLog;
import com.sinoyd.lims.lim.service.TestOperateLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * testOperateLog服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2024/02/20
 * @since V100R001
 */
 @Api(tags = "testOperateLog服务")
 @RestController
 @RequestMapping("api/lim/testOperateLog")
 public class TestOperateLogController extends BaseJpaController<DtoTestOperateLog, String, TestOperateLogService> {

    /**
     * 分页动态条件查询日志
     *
     * @param criteria 条件
     * @return RestResponse<List < DtoTestOperateLog>>
     */
    @ApiOperation(value = "分页动态条件查询日志", notes = "分页动态条件查询日志")
    @GetMapping
    public RestResponse<List<DtoTestOperateLog>> findByPage(TestOperateLogCriteria criteria) {
        RestResponse<List<DtoTestOperateLog>> restResp = new RestResponse<>();
        PageBean<DtoTestOperateLog> page = super.getPageBean();
        service.findByPage(page, criteria);
        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());
        return restResp;
    }

    /**
     * 批量删除日志
     *
     * @param ids 标识集合
     * @return RestResponse<List < DtoTestOperateLog>>
     */
    @ApiOperation(value = "批量删除日志", notes = "批量删除日志")
    @DeleteMapping
    public RestResponse<List<DtoTestOperateLog>> findByPage(@RequestBody List<String> ids) {
        RestResponse<List<DtoTestOperateLog>> restResp = new RestResponse<>();
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }
}