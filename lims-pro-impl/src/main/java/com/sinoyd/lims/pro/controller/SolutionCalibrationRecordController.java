package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoSolutionCalibrationRecord;
import com.sinoyd.lims.pro.service.SolutionCalibrationRecordService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SamplingCarConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
@Api(tags = "SolutionCalibrationRecordController")
@RestController
@RequestMapping("api/pro/solutionCalibrationRecord")
public class SolutionCalibrationRecordController extends BaseJpaController<DtoSolutionCalibrationRecord, String, SolutionCalibrationRecordService> {

    /**
     * 批量删除
     * @param ids 记录标识
     * @return RestResponse<Integer>
     */
    @DeleteMapping
    public RestResponse<Integer> batchDelete(@RequestBody List<String> ids){
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        return  restResponse;
    }
}
