package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 参数管理仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
public interface ParamsConfigRepository extends IBaseJpaRepository<DtoParamsConfig, String> {

    /**
     * 新增时判断是否存在同名
     *
     * @param alias 分析方法名称
     * @return 相同名称的分析方法数量
     */
    @Query("select count(p.id) from DtoParamsConfig p where p.isDeleted = 0 and p.alias = :alias and p.objId = :objId and p.parentId = '00000000-0000-0000-0000-000000000000'")
    Integer getCountByName(@Param("alias") String alias, @Param("objId") String objId);

    /**
     * 更新时判断是否存在同名
     *
     * @param alias 参数别名
     * @param id    要更新的id
     * @return 相同名称的数量
     */
    @Query("select count(p.id) from DtoParamsConfig p where p.isDeleted = 0 and p.alias = :alias and p.id <> :id and p.objId = :objId and p.parentId = '00000000-0000-0000-0000-000000000000'")
    Integer getCountByNameAndId(@Param("alias") String alias, @Param("id") String id, @Param("objId") String objId);


    /**
     * 更新时判断是否存在同名
     *
     * @param id 要更新的id
     * @return 相同名称的数量
     */
    @Query("select count(p.id) from DtoParamsConfig p where p.isDeleted = 0 and p.id <> :id and p.objId = :objId and p.analyzeItemId =:analyzeItemId and p.parentId = '00000000-0000-0000-0000-000000000000'")
    Integer getCountByAnalyzeItemIdAndId(@Param("id") String id, @Param("objId") String objId, @Param("analyzeItemId") String analyzeItemId);

    /**
     * 返回父类相关数据
     *
     * @param parentId 父类id
     * @return 返回父类相关数据
     */
    List<DtoParamsConfig> findByParentId(String parentId);

    /**
     * 返回父类相关数据
     *
     * @param parentIds 父类id
     * @return 返回父类相关数据
     */
    List<DtoParamsConfig> findByParentIdIn(List<String> parentIds);

    /**
     * 根据父级id集合、分析项目id集合获取配置信息
     *
     * @param parentIds      父级id集合
     * @param analyzeItemIds 分析项目id集合
     * @return 对应父级id集合、分析项目id集合获取配置信息
     */
    @Query("SELECT p from DtoParamsConfig p where p.isDeleted = 0 and p.parentId in :parentIds and p.analyzeItemId in :analyzeItemIds and p.isShow=1")
    List<DtoParamsConfig> findByParentIdInAndAnalyzeItemIdInAndIsShowTrue(@Param("parentIds") Collection<String> parentIds, @Param("analyzeItemIds") Collection<String> analyzeItemIds);

    /**
     * 根据父级id集合获取配置信息
     *
     * @param parentIds 父级id集合
     * @return 对应父级id集合、分析项目id集合获取配置信息
     */
    @Query("SELECT p from DtoParamsConfig p where p.isDeleted = 0 and p.parentId in :parentIds and p.isShow=1")
    List<DtoParamsConfig> findByParentIdInAndIsShowTrue(@Param("parentIds") Collection<String> parentIds);

    /**
     * @param dimensionId 量纲id
     * @return 根据量纲id获取相关的参数信息
     */
    @Query("select p from DtoParamsConfig p where p.isDeleted = 0 and p.dimensionId = :dimensionId")
    List<DtoParamsConfig> findByDimensionId(@Param("dimensionId") String dimensionId);

    /**
     * 批量修改参数的量纲信息
     *
     * @param ids           参数的ids
     * @param dimensionName 量纲名称
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoParamsConfig d set d.dimension = :dimensionName where d.id in :ids")
    Integer updateDimension(@Param("ids") List<String> ids, @Param("dimensionName") String dimensionName);


    /**
     * 重写获取所有的方法
     *
     * @return 返回未删除参数配置信息
     */
    @Override
    @Query("select p from DtoParamsConfig p where p.isDeleted = 0")
    List<DtoParamsConfig> findAll();

    /**
     * 根据id数组获取参数配置信息
     *
     * @param ids 参数配置ids
     * @return 返回参数配置
     */
    @Query("select d from DtoParamsConfig d where d.isDeleted = 0 and d.id in :ids")
    @Override
    List<DtoParamsConfig> findAll(@Param("ids") Iterable<String> ids);


    /**
     * 返回所有的带假删的参数配置信息
     *
     * @return 返回带删除的参数配置信息
     */
    @Query("select p from DtoParamsConfig p")
    List<DtoParamsConfig> findAllDeleted();

    /**
     * 返回所有的带假删的测试项目信息
     *
     * @param ids 参数配置的ids
     * @return 返回带删除的参数配置信息
     */
    @Query("select p from DtoParamsConfig p where p.id in :ids")
    List<DtoParamsConfig> findAllDeleted(@Param("ids") List<String> ids);


    /**
     * 根据对象获取相应的参数数据
     *
     * @param objIds 选择的对象ids
     * @param type   类型
     * @return 返回参数配置
     */
    @Query("SELECT p FROM DtoParamsConfig p where  p.objId in :objIds and p.type = :type and p.isDeleted=0")
    List<DtoParamsConfig> findByObjIdInAndType(@Param("objIds") List<String> objIds, @Param("type") Integer type);

    /**
     * 根据对象获取相应的参数数据
     * @param objIds 选择的对象ids
     * @param type 类型
     * @return 返回参数配置
     */
    List<DtoParamsConfig> findByObjIdInAndTypeAndIsDeletedFalse(List<String> objIds,Integer type);

    /**
     * 根据对象获取相应的参数数据
     *
     * @param objIds 选择的对象ids
     * @param type   类型
     * @return 返回参数配置
     */
    @Query("SELECT p FROM DtoParamsConfig p where  p.objId in :objIds " +
            "and p.type = :type and p.parentId = :parentId " +
            "and p.isDeleted = false order by p.orderNum desc ")
    List<DtoParamsConfig> findByObjIdInAndTypeAndParentId(@Param("objIds") List<String> objIds,
                                                          @Param("type") Integer type,
                                                          @Param("parentId") String parentId);


    /**
     * 根据对象获取相应的参数数据
     *
     * @param objId 选择的对象ids
     * @param type  类型
     * @return 返回参数配置
     */
    @Query("SELECT p FROM DtoParamsConfig p where  p.objId = :objId and p.type = :type and p.isDeleted=0")
    List<DtoParamsConfig> findByObjIdAndType(@Param("objId") String objId, @Param("type") Integer type);

    /**
     * 根据对象获取相应的参数数据
     *
     * @param objId 选择的对象ids
     * @param type  类型
     * @return 返回参数配置
     */
    @Query("SELECT p FROM DtoParamsConfig p where  p.objId = :objId and p.type = :type and p.parentId= :parentId and p.isDeleted=0")
    List<DtoParamsConfig> findByObjIdAndType(@Param("objId") String objId, @Param("type") Integer type, @Param("parentId") String parentId);

    /**
     * 根据对象获取相应的参数数据
     *
     * @param objIds 选择的对象ids
     * @param type   类型
     * @return 返回参数配置
     */
    @Query("SELECT p FROM DtoParamsConfig p where  p.objId in :objIds and p.type = :type and p.parentId= :parentId and p.isDeleted=0")
    List<DtoParamsConfig> findByObjIdInAndType(@Param("objIds") List<String> objIds, @Param("type") Integer type, @Param("parentId") String parentId);

    /**
     * 根据对象获取相应的参数数据
     *
     * @param objId 选择的对象ids
     * @param type  类型
     * @return 返回参数配置
     */
    @Query("SELECT p FROM DtoParamsConfig p where  p.objId = :objId and p.type = :type and p.parentId= :parentId " +
            "and p.isDeleted=0 and p.paramsId not in :paramsIds")
    List<DtoParamsConfig> findByObjIdAndTypeAndParamsIdNotIn(@Param("objId") String objId,
                                                             @Param("type") Integer type,
                                                             @Param("parentId") String parentId,
                                                             @Param("paramsIds") List<String> paramsIds);

    /**
     * 根据对象获取相应的参数数据
     *
     * @param objIds 选择的对象ids
     * @param type   类型
     * @return 返回参数配置
     */
    @Query("SELECT p FROM DtoParamsConfig p where  p.objId in :objIds and p.type = :type and p.parentId= :parentId " +
            "and p.isDeleted=0 and p.paramsId not in :paramsIds")
    List<DtoParamsConfig> findByObjIdInAndTypeAndParamsIdNotIn(@Param("objIds") List<String> objIds,
                                                               @Param("type") Integer type,
                                                               @Param("parentId") String parentId,
                                                               @Param("paramsIds") List<String> paramsIds);


    /**
     * 根据对象id删除参数（主要在原始记录单配置中用到）
     *
     * @param objectIds 对ids
     * @return 返回数据
     */
    @Transactional
    @Modifying
    @Query("update DtoParamsConfig as a set a.isDeleted=0 where a.objId in :objectIds")
    Integer deleteByObjIdIn(@Param("objectIds") List<String> objectIds);

    /**
     * 更新参数是否配置全
     *
     * @param id          参数id
     * @param isAllConfig 是否配置全
     * @return 返回数据
     */
    @Transactional
    @Modifying
    @Query("update DtoParamsConfig as a set a.isAllConfig=:isAllConfig where a.id=:id")
    Integer updateParamsConfigIsAllConfig(@Param("id") String id, @Param("isAllConfig") Boolean isAllConfig);


    /**
     * 更新参数是否配置全
     *
     * @param ids         参数ids
     * @param isAllConfig 是否配置全
     * @return 返回数据
     */
    @Transactional
    @Modifying
    @Query("update DtoParamsConfig as a set a.isAllConfig=:isAllConfig where a.id in :ids")
    Integer updateParamsConfigIsAllConfig(@Param("ids") List<String> ids, @Param("isAllConfig") Boolean isAllConfig);

    /**
     * 批量修改别名及参数ids（主要是原始记录单配置修改了某个参数之后，需要修改个性化的别名及id)
     *
     * @param objIds   对象ids
     * @param alias    别名
     * @param paramsId 参数id
     * @return 返回数据
     */
    @Transactional
    @Modifying
    @Query("update DtoParamsConfig as a set a.alias=:alias,a.paramsId=:paramsId where a.objId in :objIds")
    Integer updateAliasByObjectIds(@Param("objIds") List<String> objIds, @Param("alias") String alias,
                                   @Param("paramsId") String paramsId);

    /**
     * 按对象真删除（主要原始记录单配置复制的时候用到真删方法）
     *
     * @param objectIds 对象ids
     * @return 返回数据
     */
    @Transactional
    @Modifying
    @Query("delete from DtoParamsConfig as a where a.objId in :objectIds")
    Integer deleteTrueByObjIdIn(@Param("objectIds") List<String> objectIds);

    /**
     * 根据Id查询参数配置对象
     *
     * @param ids id集合
     * @return
     */
    List<DtoParamsConfig> findByIdIn(List<String> ids);

    /**
     * 根据对象Id查询
     *
     * @param objId 对象id
     * @return 参数配置对象列表
     */
    List<DtoParamsConfig> findByObjId(String objId);
}