package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Data
public class DataProjectVO {
    /**
     * 租户号
     */
    private String id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 项目登记人
     */
    private String inceptPersonName;

    /**
     * 项目负责人
     */
    private String leader;

    /**
     * 报告编制人
     */
    private String reportMakerName;

    /**
     * 项目登记时间
     */
    private Date inceptTime;

    /**
     * 监测目的
     */
    private String monitorPurp;

    /**
     * 监测方式
     */
    private String monitorMethods;

    /**
     * 监测要求及说明
     */
    private String customerRequired;

    /**
     * 保存条件
     */
    private String saveCondition;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 委托单位
     */
    private String customerName;

    /**
     * 地址
     */
    private String customerAddress;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 电话
     */
    private String linkPhone;

    /**
     * 受检单位
     */
    private String inspectedEnt;

    /**
     * 受检方联系人
     */
    private String inspectedLinkMan;

    /**
     * 受检方联系电话
     */
    private String inspectedLinkPhone;

    /**
     * 受检方地址
     */
    private String inspectedAddress;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;

}
