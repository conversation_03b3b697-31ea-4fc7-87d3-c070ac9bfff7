package com.sinoyd.lims.order.service.statistics;

import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 策略分配实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/26
 */
@Component
public class CustomerOrderStatisticsContextImpl {
    private Map<String, CustomerOrderAnaService> customerOrderAnalyzeStrategyMap;


    /**
     * 返回单个vo
     * @param strategyKey map的key
     * @param year 年份
     * @return BaseOrderCustomerAnalyzeVO
     */
    public BaseOrderCustomerAnalyzeVO getCustomerOrderData(String strategyKey,Integer year) {
        return customerOrderAnalyzeStrategyMap.get(strategyKey).statistics(year);
    }

    /**
     *返回多个vo
     * @param strategyKey map的key
     * @param year 年份
     * @return List<BaseOrderCustomerAnalyzeVO>
     */
    public List<BaseOrderCustomerAnalyzeVO> getCustomerOrderList(String strategyKey, Integer year) {
        return customerOrderAnalyzeStrategyMap.get(strategyKey).statisticsList(year);
    }

    @Autowired
    public void setCustomerOrderAnalyzeStrategyMap(Map<String, CustomerOrderAnaService> customerOrderAnalyzeStrategyMap) {
        this.customerOrderAnalyzeStrategyMap = customerOrderAnalyzeStrategyMap;
    }
}
