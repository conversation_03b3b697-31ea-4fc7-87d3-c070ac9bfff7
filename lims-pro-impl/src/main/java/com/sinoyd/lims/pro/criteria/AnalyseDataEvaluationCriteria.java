package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 工作单分析数据评价标准查询条件
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date ：2021/10/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyseDataEvaluationCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    private String sampleCode;

    private String analyseItem;

    private String workSheetFolderId;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.isDeleted = 0 and (s.isDeleted = 0 or s.status = :status)");
        values.put("status", EnumPRO.EnumSampleStatus.样品作废.name());
        condition.append(" and s.sampleCategory = :sampleCategory");
        values.put("sampleCategory", EnumPRO.EnumSampleCategory.原样.getValue());
        condition.append(" and s.id = a.sampleId");
        if (StringUtil.isNotEmpty(this.sampleCode)) {
            condition.append(" and s.code like :sampleCode");
            values.put("sampleCode", "%" + this.sampleCode + "%");
        }
        if (StringUtil.isNotEmpty(this.analyseItem)) {
            condition.append(" and a.redAnalyzeItemName like :analyseItem");
            values.put("analyseItem", "%" + this.analyseItem + "%");
        }
        //把前端传回的状态查询标识放到容器中，用于Service过滤数据
        if (StringUtil.isNotEmpty(workSheetFolderId)) {
            condition.append(" and a.workSheetFolderId = :workSheetFolderId");
            values.put("workSheetFolderId", this.workSheetFolderId);
        }
        return condition.toString();
    }
}