package com.sinoyd.base.repository.lims;


import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * DocumentRepository数据接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
public interface DocumentRepository extends IBaseJpaRepository<DtoDocument, String> {

    /**
     * 根据对象id 获取上传的相关文件
     *
     * @param folderId 对象id
     * @return 返回相应的文件信息
     */
    List<DtoDocument> findByFolderId(String folderId);

    /**
     * 根据对象id 获取上传的相关文件(过滤假删)
     *
     * @param folderId 对象id
     * @return 返回相应的文件信息
     */
    List<DtoDocument> findByFolderIdAndIsDeletedFalse(String folderId);

    /**
     * 根据对象id 获取上传的相关文件
     *
     * @param folderIds 对象id集合
     * @return 返回相应的文件信息
     */
    List<DtoDocument> findByFolderIdIn(Collection<String> folderIds);


    /**
     * 按照外键id删除之前的文件
     *
     * @param folderIds 外键ids
     * @return 返回删除行数
     */
    @Transactional
    @Modifying
    @Query("update DtoDocument a set a.isDeleted=1 where a.folderId in :folderIds")
    Integer deleteByFolderIds(@Param("folderIds") List<String> folderIds);

    /**
     * 按照外键id删除之前的文件
     *
     * @param folderId 外键ids
     * @return 返回删除行数
     */
    @Transactional
    @Modifying
    @Query("update DtoDocument a set a.isDeleted=1 where a.folderId = :folderId")
    Integer deleteByFolderId(@Param("folderId") String folderId);

    /**
     * 根据id集合查询
     *
     * @param ids id集合
     * @return
     */
    List<DtoDocument> findByIdIn(List<String> ids);

    /**
     * 根据文件夹id和文件类型查询
     *
     * @param folderIds 文件夹id集合
     * @param docType   文件类型
     * @return
     */
    List<DtoDocument> findByFolderIdInAndDocTypeIdOrderByCreateDateDesc(List<String> folderIds, String docType);

    /**
     * 根据文件夹id和文件类型查询
     *
     * @param folderId 文件夹id
     * @param docType   文件类型
     * @return 实体集合
     */
    List<DtoDocument> findByFolderIdAndDocTypeIdOrderByCreateDateDesc(String folderId, String docType);

    /**
     * 根据文件夹id和文件类型查询
     *
     * @param folderIds 文件夹id集合
     * @param docType   文件类型
     * @return
     */
    List<DtoDocument> findByFolderIdInAndDocTypeIdAndIsDeletedFalseOrderByCreateDateDesc(List<String> folderIds, String docType);

    /**
     * 根据文件类型查询
     *
     * @param docType   文件类型
     * @return 实体集合
     */
    List<DtoDocument> findByDocTypeIdAndIsDeletedFalse(String docType);
}