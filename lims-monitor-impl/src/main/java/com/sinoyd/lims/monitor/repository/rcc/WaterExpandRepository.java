package com.sinoyd.lims.monitor.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.monitor.dto.rcc.DtoWaterExpand;


/**
 * WaterExpand数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface WaterExpandRepository extends IBaseJpaPhysicalDeleteRepository<DtoWaterExpand, String> {

    /**
     * 根据 waterId 查询对象
     *
     * @param waterId
     * @return
     */
    DtoWaterExpand findByWaterId(String waterId);


}