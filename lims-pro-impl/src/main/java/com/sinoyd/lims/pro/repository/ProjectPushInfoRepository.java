package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import com.sinoyd.lims.pro.dto.DtoProjectPushInfo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 项目推送信息数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/11/06
 * @since V100R001
 */
public interface ProjectPushInfoRepository extends IBaseJpaPhysicalDeleteRepository<DtoProjectPushInfo, String> {

    /**
     * 根据项目id查询推送记录
     *
     * @param projectIdList 项目ids
     * @return 推送记录
     */
    List<DtoProjectPushInfo> findByProjectIdIn(List<String> projectIdList);
}