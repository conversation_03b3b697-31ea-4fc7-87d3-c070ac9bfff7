package com.sinoyd.lims.od.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.od.dto.*;
import com.sinoyd.lims.od.enums.EnumOds;
import com.sinoyd.lims.od.repository.*;
import com.sinoyd.lims.od.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 实验次序接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
public class LabSeqServiceImpl extends BaseJpaServiceImpl<DtoLabSeq, String, LabSeqRepository> implements LabSeqService {

    private OdSampleService odSampleService;

    private TaskService taskService;

    private LabGroupRepository labGroupRepository;

    private OdPersonAnswerRepository odPersonAnswerRepository;

    private OdPersonAnswerService odPersonAnswerService;

    private OdPersonRepository odPersonRepository;

    private PersonRepository personRepository;

    private EnvGasResultService envGasResultService;

    private EnvGasResultRepository envGasResultRepository;

    @Override
    public void findByPage(PageBean<DtoLabSeq> page, BaseCriteria criteria) {
        page.setEntityName("DtoLabSeq a, DtoLabGroup g");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        //填充前端展示的临时字段
        loadTransientFields(page.getData());

    }

    @Override
    public DtoLabSeq initLab(DtoLabSeq lebTemp) {
        String sampleId = lebTemp.getSampleId();
        // 样品下所有实验组
        List<DtoLabGroup> labGroups = labGroupRepository.findBySampleId(sampleId);
        DtoOdSample sample = odSampleService.findOne(sampleId);
        if (sample.getSampleState().equals(EnumOds.EnumOdsampleState.已完成.getValue())) {
            throw new BaseException("当前样品已完成实验，无法继续创建。");
        }
        // 初始化实验组
        DtoLabSeq initLab = new DtoLabSeq();
        initLab.setId(null);
        initLab.setSampleId(sampleId);
        // 查询任务
        DtoTask task = taskService.findOne(sample.getTaskId());
        // 定义下一轮需要剔除的嗅辨员
        List<String> nextPersonIds = new ArrayList<>();
        if (StringUtil.isEmpty(labGroups)) {
            initLab.setSn(1);
            initLab.setGroupSn(1);
        } else {
            // 根据最大实验组和实验次序
            Optional<DtoLabGroup> maxGroupOpt = labGroups.stream()
                    .max(Comparator.comparing(DtoLabGroup::getSn));
            Optional<DtoLabSeq> maxSeqOpt = maxGroupOpt.flatMap(dtoLabGroup -> repository.findByGroupId(dtoLabGroup.getId()).stream()
                    .max(Comparator.comparing(DtoLabSeq::getSn)));
            if (maxGroupOpt.isPresent() && maxSeqOpt.isPresent()) {
                DtoLabGroup maxLabGroup = maxGroupOpt.get();
                DtoLabSeq maxLabSeq = maxSeqOpt.get();
                if (!maxLabSeq.getLabState().equals(EnumOds.EnumLabState.已完成.getValue())) {
                    throw new BaseException("上一轮实验未完成，不能添加实验");
                }
                // 根据任务类型分支处理
                if (task.getTaskType().equals(EnumOds.EnumTaskType.固定源.getValue())) {
                    handleStationarySourceTask(maxLabGroup, maxLabSeq, initLab, nextPersonIds);
                } else if (task.getTaskType().equals(EnumOds.EnumTaskType.环境空气.getValue())) {
                    handleEnvGasTask(maxLabGroup, maxLabSeq, initLab);
                }
            }
        }
        initLab.buildSnStr(initLab);
        initLab.setLabState(EnumOds.EnumLabState.未开始.getValue());
        // 初始化嗅辨员实验数据
        initPersonAnswers(initLab, sample.getTaskId(), nextPersonIds);
        return initLab;
    }


    @Override
    @Transactional
    public DtoLabSeq save(DtoLabSeq labSeq) {
        labSeq.setId(UUIDHelper.NewID());
        String groupId = labSeq.getGroupId();
        if (StringUtil.isEmpty(groupId)) {
            // 新增实验组
            DtoLabGroup labGroup = new DtoLabGroup();
            labGroup.setSampleId(labSeq.getSampleId());
            labGroup.setSn(labSeq.getGroupSn());
            labGroupRepository.save(labGroup);
            groupId = labGroup.getId();
        }
        labSeq.setGroupId(groupId);
        // 检查环境空气稀释倍数是否与第一轮一致
        checkDilutionRate(labSeq);
        repository.save(labSeq);
        labSeq.setId(labSeq.getId());
        // 新增嗅辨员答案数据
        savePersonAnswer(labSeq);
        return labSeq;
    }

    @Override
    public DtoLabSeq findOne(String key) {
        DtoLabSeq labSeq = super.findOne(key);
        DtoLabGroup labGroup = labGroupRepository.findOne(labSeq.getGroupId());
        labSeq.setGroupSn(labGroup.getSn());
        labSeq.buildSnStr(labSeq);
        labSeq.setPersonAnswerList(odPersonAnswerService.findByLabSeqId(labSeq.getId()));
        return labSeq;
    }

    @Override
    @Transactional
    public DtoLabSeq update(DtoLabSeq labSeq) {
        // 检查环境空气稀释倍数是否与第一轮一致
        checkDilutionRate(labSeq);
        // 更新嗅辨答案
        List<DtoOdPersonAnswer> personAnswerList = labSeq.getPersonAnswerList();
        odPersonAnswerRepository.save(personAnswerList);
        return super.update(labSeq);
    }

    /**
     * 检查固定源任务，同一组中是否存在相同的稀释倍数的实验
     *
     * @param labSeq 实验次序实体
     * @return 相同稀释倍数的次序
     */
    @Override
    public String getExistsDilutionRat(DtoLabSeq labSeq) {
        if (labSeq.getSn() != 1) {
            String groupId = labSeq.getGroupId();
            DtoLabGroup labGroup = labGroupRepository.findOne(groupId);
            String sampleId = labGroup.getSampleId();
            DtoOdSample odSample = odSampleService.findOne(sampleId);
            DtoTask task = taskService.findOne(odSample.getTaskId());
            // 查询同一组实验中的其他轮次
            List<DtoLabSeq> labSeqList = repository.findByGroupId(groupId);
            labSeqList.removeIf(p -> labSeq.getId().equals(p.getId()));
            if (task.getTaskType().equals(EnumOds.EnumTaskType.固定源.getValue())) {
                List<Integer> snList = labSeqList.stream().filter(p -> p.getDilutionRate().equals(labSeq.getDilutionRate())).map(DtoLabSeq::getSn).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(snList)) {
                    return snList.stream().map(String::valueOf).collect(Collectors.joining("、"));
                }
            }
        }
        return "";

    }

    /**
     * 检查环境空气稀释倍数是否与第一轮一致
     * 检查固定源同一组中、轮次、稀释倍数做唯一性校验
     *
     * @param labSeq 实验次序实体
     */
    private void checkDilutionRate(DtoLabSeq labSeq) {
        if (labSeq.getSn() != 1) {
            String groupId = labSeq.getGroupId();
            DtoLabGroup labGroup = labGroupRepository.findOne(groupId);
            String sampleId = labGroup.getSampleId();
            DtoOdSample odSample = odSampleService.findOne(sampleId);
            DtoTask task = taskService.findOne(odSample.getTaskId());
            // 查询同一组实验中的其他轮次
            List<DtoLabSeq> labSeqList = repository.findByGroupId(groupId);
            labSeqList.removeIf(p -> labSeq.getId().equals(p.getId()));
            if (task.getTaskType().equals(EnumOds.EnumTaskType.环境空气.getValue())) {
                labSeqList.stream().filter(p -> p.getSn() == 1).findFirst().ifPresent(seq -> {
                    if (!labSeq.getDilutionRate().equals(seq.getDilutionRate())) {
                        throw new BaseException("稀释倍数与第1轮不一致");
                    }
                });
            } else if (task.getTaskType().equals(EnumOds.EnumTaskType.固定源.getValue())) {
                if (labSeqList.stream().anyMatch(p -> p.getSn().equals(labSeq.getSn()) && p.getDilutionRate().equals(labSeq.getDilutionRate()))) {
                    throw new BaseException("当前实验组存在相同的实验次序和稀释倍数");
                }
            }
        }
    }


    /**
     * 开始实验
     *
     * @param labSeq 实验次序
     */
    @Override
    @Transactional
    public DtoLabSeq startLab(DtoLabSeq labSeq) {
        DtoLabSeq dtoLabSeq = super.findOne(labSeq.getId());
        // 不存在实验次序时先保存
        if (StringUtil.isNull(dtoLabSeq)) {
            this.save(labSeq);
        }
        labSeq.setLabState(EnumOds.EnumLabState.进行中.getValue());
        repository.save(labSeq);
        return labSeq;
    }

    @Override
    public Map<String, Object> findMaxSn(String sampleId) {
        Map<String, Object> result = new HashMap<>();
        List<DtoLabGroup> labGroups = labGroupRepository.findBySampleId(sampleId);
        List<String> groupIds = labGroups.stream().map(DtoLabGroup::getId).collect(Collectors.toList());
        // 根据最大实验组和实验次序
        Optional<DtoLabGroup> maxGroupOpt = labGroups.stream()
                .max(Comparator.comparing(DtoLabGroup::getSn));
        Optional<DtoLabSeq> maxSeqOpt = StringUtil.isNotEmpty(groupIds) ? repository.findByGroupIdIn(groupIds).stream()
                .max(Comparator.comparing(DtoLabSeq::getSn)) : Optional.empty();
        result.put("groupSn", maxGroupOpt.isPresent() ? maxGroupOpt.get().getSn() : 0);
        result.put("seqSn", maxSeqOpt.isPresent() ? maxSeqOpt.get().getSn() : 0);
        return result;
    }


    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        // 筛选次序为1 的删除实验组
        List<DtoLabSeq> labSeqList = repository.findAll(idList);
        List<String> groupIds = labSeqList.stream().filter(p -> p.getSn() == 1).map(DtoLabSeq::getGroupId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(groupIds)) {
            labGroupRepository.logicDeleteById(groupIds, new Date());
        }
        // 删除嗅辨实验数据
        List<DtoOdPersonAnswer> personAnswerList = odPersonAnswerRepository.findByLabSeqIdIn(idList);
        if (StringUtil.isNotEmpty(personAnswerList)) {
            odPersonAnswerRepository.logicDeleteById(personAnswerList.stream().map(DtoOdPersonAnswer::getId).collect(Collectors.toList()), new Date());
        }
        return super.logicDeleteById(ids);
    }


    @Override
    @Transactional
    public DtoLabSeq refreshAnswer(String labSeqId) {
        DtoLabSeq labSeq = this.findOne(labSeqId);
        // 判断所有嗅辨员是否全部完成解答
        List<DtoOdPersonAnswer> personAnswerList = labSeq.getPersonAnswerList();
        if (personAnswerList.stream().allMatch(DtoOdPersonAnswer::getIsCompleted) && !labSeq.getLabState().equals(EnumOds.EnumLabState.已完成.getValue())) {
            labSeq.setLabState(EnumOds.EnumLabState.已完成.getValue());
            repository.save(labSeq);
            // 计算环境空气的M值
            envGasResultService.calculateMValue(labSeq);
        }
        return labSeq;
    }

    @Override
    public List<DtoLabSeq> findByGroupIdIn(List<String> groupIds) {
        return repository.findByGroupIdIn(groupIds);
    }


    /**
     * 新增嗅辨分析数据
     *
     * @param labSeq 实验次序实体
     */
    private void savePersonAnswer(DtoLabSeq labSeq) {
        List<DtoOdPersonAnswer> personAnswerList = labSeq.getPersonAnswerList();
        personAnswerList.forEach(p -> p.setLabSeqId(labSeq.getId()));
        odPersonAnswerRepository.save(personAnswerList);
    }

    /**
     * 处理固定源任务逻辑
     *
     * @param maxLabGroup   最大实验组
     * @param maxLabSeq     最大次序
     * @param initLab       初始化实验组实体
     * @param nextPersonIds 下一轮嗅辨员ids
     */
    private void handleStationarySourceTask(DtoLabGroup maxLabGroup, DtoLabSeq maxLabSeq,
                                            DtoLabSeq initLab, List<String> nextPersonIds) {
        List<DtoOdPersonAnswer> answers = odPersonAnswerRepository.findByLabSeqId(maxLabSeq.getId());
        boolean allWrong = answers.stream()
                .noneMatch(p -> p.getStandardAnswer().equals(p.getPersonAnswer()));

        if (allWrong) {
            initLab.setGroupSn(maxLabGroup.getSn() + 1);
            initLab.setSn(1);
        } else {
            initLab.setGroupId(maxLabGroup.getId());
            initLab.setGroupSn(maxLabGroup.getSn());
            initLab.setSn(maxLabSeq.getSn() + 1);
            // 收集错误人员ID
            nextPersonIds.addAll(answers.stream()
                    .filter(p -> p.getStandardAnswer().equals(p.getPersonAnswer()))
                    .map(DtoOdPersonAnswer::getPersonId)
                    .collect(Collectors.toList()));
        }
    }

    /**
     * 处理环境空气任务逻辑
     *
     * @param maxLabGroup 最大实验组
     * @param maxLabSeq   最大次序
     * @param initLab     初始化实验组
     */
    private void handleEnvGasTask(DtoLabGroup maxLabGroup, DtoLabSeq maxLabSeq, DtoLabSeq initLab) {
        DtoEnvGasResult result = envGasResultRepository.findByGroupId(maxLabSeq.getId());
        if (result != null && new BigDecimal(result.getMValue()).compareTo(new BigDecimal("0.58")) <= 0) {
            throw new BaseException("上一组实验已通过");
        }
        if (maxLabSeq.getSn() == 3) {
            initLab.setGroupSn(maxLabGroup.getSn() + 1);
            initLab.setSn(1);
        } else {
            initLab.setGroupSn(maxLabGroup.getSn());
            initLab.setSn(maxLabSeq.getSn() + 1);
            initLab.setGroupId(maxLabGroup.getId());
            // 默认上一轮的稀释倍数
            initLab.setDilutionRate(maxLabSeq.getDilutionRate());
        }
    }

    /**
     * 填充前端显示字段
     *
     * @param pageData 查询结果
     */
    private void loadTransientFields(List<DtoLabSeq> pageData) {
        List<String> groupIds = pageData.stream().map(DtoLabSeq::getGroupId).collect(Collectors.toList());
        List<String> seqIds = pageData.stream().map(DtoLabSeq::getId).collect(Collectors.toList());
        // 实验组数据
        List<DtoLabGroup> groupList = StringUtil.isNotEmpty(groupIds) ? labGroupRepository.findAll(groupIds) : new ArrayList<>();
        Map<String, DtoLabGroup> groupMap = labGroupRepository.findAll(groupIds).stream()
                .collect(Collectors.toMap(DtoLabGroup::getId, p -> p));
        // 嗅辨实验数据
        List<DtoOdPersonAnswer> personAnswerList = StringUtil.isNotEmpty(seqIds) ? odPersonAnswerRepository.findByLabSeqIdIn(seqIds) : new ArrayList<>();
        Map<String, Map<String, DtoOdPersonAnswer>> answerMap = odPersonAnswerRepository.findByLabSeqIdIn(seqIds).stream()
                .collect(Collectors.groupingBy(DtoOdPersonAnswer::getLabSeqId,
                        Collectors.toMap(DtoOdPersonAnswer::getPersonId, p -> p)));
        // 嗅辨员
        List<String> odPersonIds = personAnswerList.stream().map(DtoOdPersonAnswer::getPersonId).distinct().collect(Collectors.toList());
        List<DtoOdPerson> odPersonList = StringUtil.isNotEmpty(odPersonIds) ? odPersonRepository.findAll(odPersonIds) : new ArrayList<>();
        odPersonList.sort(Comparator.comparing(DtoOdPerson::getPersonSn));
        for (DtoLabSeq labSeq : pageData) {
            DtoLabGroup group = groupMap.get(labSeq.getGroupId());
            if (null != group) {
                labSeq.setGroupSn(group.getSn());
                labSeq.buildSnStr(labSeq);
                Map<String, DtoOdPersonAnswer> seqAnswers = answerMap.getOrDefault(labSeq.getId(), Collections.emptyMap());
                List<DtoOdPersonAnswer> odPersonAnswerList = new ArrayList<>();
                for (DtoOdPerson odPerson : odPersonList) {
                    DtoOdPersonAnswer answer = seqAnswers.get(odPerson.getId());
                    if (answer == null) {
                        answer = new DtoOdPersonAnswer();
                        answer.setEvaluation(-1);
                    }
                    answer.setPersonSn(odPerson.getPersonSn());
                    odPersonAnswerList.add(answer);
                }
                labSeq.setPersonAnswerList(odPersonAnswerList);
            }
        }
    }

    /**
     * 初始化嗅辨员实验数据
     *
     * @param initLab       初始化实验数据实体
     * @param taskId        任务id
     * @param nextPersonIds 下一轮嗅辨人员ids
     */
    private void initPersonAnswers(DtoLabSeq initLab, String taskId, List<String> nextPersonIds) {
        // 初始化嗅辨员
        List<DtoOdPerson> odPersonList = odPersonRepository.findByTaskId(taskId);
        // 过滤需要进行下一轮实验的的嗅辨员
        if (StringUtil.isNotEmpty(nextPersonIds)) {
            odPersonList = odPersonList.stream().filter(p -> nextPersonIds.contains(p.getId())).collect(Collectors.toList());
        }
        List<String> personIds = odPersonList.stream().map(DtoOdPerson::getPersonId).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personRepository.findAll(personIds) : new ArrayList<>();
        List<DtoOdPersonAnswer> personAnswerList = new ArrayList<>();
        for (DtoOdPerson person : odPersonList) {
            DtoOdPersonAnswer personAnswer = new DtoOdPersonAnswer();
            // 嗅辨员id
            personAnswer.setPersonId(person.getId());
            // 次序
            personAnswer.setPersonSn(person.getPersonSn());
            personList.stream().filter(p -> p.getId().equals(person.getPersonId())).findFirst().ifPresent(per -> {
                personAnswer.setPersonName(per.getCName());
            });
            personAnswerList.add(personAnswer);
        }
        // 嗅辨流水排序
        personAnswerList.sort(Comparator.comparing(DtoOdPersonAnswer::getPersonSn));
        initLab.setPersonAnswerList(personAnswerList);
    }

    @Autowired
    @Lazy
    public void setOdSampleService(OdSampleService odSampleService) {
        this.odSampleService = odSampleService;
    }

    @Autowired
    @Lazy
    public void setTaskService(TaskService taskService) {
        this.taskService = taskService;
    }

    @Autowired
    @Lazy
    public void setLabGroupRepository(LabGroupRepository labGroupRepository) {
        this.labGroupRepository = labGroupRepository;
    }

    @Autowired
    @Lazy
    public void setOdPersonAnswerRepository(OdPersonAnswerRepository odPersonAnswerRepository) {
        this.odPersonAnswerRepository = odPersonAnswerRepository;
    }

    @Autowired
    @Lazy
    public void setOdPersonRepository(OdPersonRepository odPersonRepository) {
        this.odPersonRepository = odPersonRepository;
    }

    @Autowired
    @Lazy
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    @Lazy
    public void setOdPersonAnswerService(OdPersonAnswerService odPersonAnswerService) {
        this.odPersonAnswerService = odPersonAnswerService;
    }

    @Autowired
    @Lazy
    public void setEnvGasResultService(EnvGasResultService envGasResultService) {
        this.envGasResultService = envGasResultService;
    }

    @Autowired
    @Lazy
    public void setEnvGasResultRepository(EnvGasResultRepository envGasResultRepository) {
        this.envGasResultRepository = envGasResultRepository;
    }
}
