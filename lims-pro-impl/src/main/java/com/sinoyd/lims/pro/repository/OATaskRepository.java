package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoOATask;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 审批任务信息数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
public interface OATaskRepository extends IBaseJpaRepository<DtoOATask, String>
{
    /**
     * 根据流程实例id查询申请任务
     * @param procInstId 工作流实例id
     * @return DtoOATask
     */
    @Query("select t from DtoOATask t where t.procInstId = :procInstId")
    List<DtoOATask> findByProcInstId(@Param("procInstId") String procInstId);
}
