package com.sinoyd.lims.pro.service.redis;

import com.jsoniter.JsonIterator;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.lims.pro.service.HomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 首页的缓存
 */
@Component
@Service
public class HomeCacheChannel {

    @Autowired
    private HomeService homeService;

    /**
     * 清除首页公告的信息
     * @param msg 消息
     */
    @Async
    public void clearCacheNotices(String msg) {
        if (StringUtils.isNotNullAndEmpty(msg)) {
            Map<String, Object> map = JsonIterator.deserialize(JsonIterator.deserialize(msg).toString(), Map.class);
            if (StringUtil.isNotNull(map)) {
                String userId = (String) map.get("userId");
                String orgId = (String) map.get("orgId");
                homeService.clearCacheNotices(userId, orgId);
            }
        }
    }


    /**
     * 清除首页公告的信息
     * @param msg 消息
     */
    @Async
    public void clearFastNavigationCache(String msg) {
        if (StringUtils.isNotNullAndEmpty(msg)) {
            Map<String, Object> map = JsonIterator.deserialize(JsonIterator.deserialize(msg).toString(), Map.class);
            if (StringUtil.isNotNull(map)) {
                String userId = (String) map.get("userId");
                String orgId = (String) map.get("orgId");
                homeService.clearFastNavigationCache(userId, orgId);
            }
        }
    }
}
