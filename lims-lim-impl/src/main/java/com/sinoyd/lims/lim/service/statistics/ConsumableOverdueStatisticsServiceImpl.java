package com.sinoyd.lims.lim.service.statistics;

import com.sinoyd.lims.lim.constants.LimConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 耗材（消耗品+标样）过期统计业务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/22
 */
@Service
@Slf4j
public class ConsumableOverdueStatisticsServiceImpl extends AbsResourceStatisticsServiceImpl {

    @Override
    public String getStatisticsSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select count(c.id) as num ")
                .append("from TB_BASE_Consumable c join TB_BASE_ConsumableDetail d on c.id = d.parentId ")
                .append("where d.expiryDate <> '1753-01-01 00:00:00' ")
                .append(" and DATE_FORMAT(d.expiryDate, '%Y-%m-%d') < DATE_FORMAT(now(), '%Y-%m-%d') ")
                .append(" and d.storage > 0 ");
        return sql.toString();
    }

    @Override
    public String getStatisticsItemName() {
        return LimConstants.StatisticsItemName.CONSUMABLE_OVERDUE;
    }

    @Override
    public String getUnit() {
        return "件";
    }
}