package com.sinoyd.base.dto.rcc;

import com.sinoyd.base.entity.Substitute;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoSubstitute实体
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_Substitute") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoSubstitute extends Substitute {
   private static final long serialVersionUID = 1L;
 }