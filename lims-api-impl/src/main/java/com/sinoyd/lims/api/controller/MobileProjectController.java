package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.service.ProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Project服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/9/1
 * @since V100R001
 */
@Api(tags = "示例: Project服务")
@RestController
@RequestMapping("api/mobile/project")
public class MobileProjectController extends BaseJpaController<DtoProject, String, ProjectService> {
    /**
     * 提交验证
     *
     * @param objectId     objId
     * @param restrictType 类型
     * @param status       状态
     * @return RestResponse<List < DtoSubmitRestrictVo>>
     */
    @ApiOperation(value = "获取项目文档", notes = "获取项目文档")
    @GetMapping(path = "/restrictMsgById")
    public RestResponse<List<DtoSubmitRestrictVo>> submitMsgById(@RequestParam(name = "objectId") String objectId,
                                                                 @RequestParam(name = "restrictType") String restrictType,
                                                                 @RequestParam(name = "status") String status) {
        RestResponse<List<DtoSubmitRestrictVo>> restResponse = new RestResponse<>();
        restResponse.setData(service.submitMsgList(objectId, restrictType, status));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }
}
