package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.RecordConfig2TestRepository;
import com.sinoyd.lims.lim.service.RecordConfig2TestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 采样单对应测试项目配置同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/24
 */
@Component
@DependsOn({"springContextAware"})
@Order(22)
@Slf4j
public class RecordConfig2TestSync extends AbsDataSync<DtoRecordConfig2Test> {

    @Autowired
    private RecordConfig2TestService service;
    @Autowired
    private RecordConfig2TestRepository repository;

    /**
     * 设置关联依赖表
     *
     * @param recordIds 需要同步的采样单配置id
     * @return 依赖结果
     */
    @Override
    public List<DtoDataCompareResult<DtoRecordConfig2Test>> compareData(List<String> recordIds) {
        List<DtoRecordConfig2Test> projectData = service.findAll();
        List<DtoRecordConfig2Test> standardData = queryStandardData();
        if (StringUtil.isNotEmpty(standardData) && StringUtil.isNotEmpty(recordIds)){
            standardData = standardData.stream().filter(p->recordIds.contains(p.getRecordConfigId())).collect(Collectors.toList());
        }
        return compareData(standardData,projectData);
    }

    /**
     * 同步数据
     *
     * @param recordIds       需要同步的采样单配置id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> recordIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoRecordConfig2Test>> compareResult = compareData(recordIds);
        Optional<DtoDataCompareResult<DtoRecordConfig2Test>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoRecordConfig2Test errorDto = null;
            try {
                for (DtoRecordConfig2Test dtoEvaluationAnalyzeItem : r.getAddDataList()) {
                    errorDto = dtoEvaluationAnalyzeItem;
                    if (repository.findOne(dtoEvaluationAnalyzeItem.getId()) != null) {
                        service.update(dtoEvaluationAnalyzeItem);
                    } else {
                        service.save(dtoEvaluationAnalyzeItem);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.记录单测试项目配置.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.记录单测试项目配置.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "api/sinoyd-lims/lim/recordConfig2Test";
    }

    /**
     * 获取类型
     *
     * @return 类型
     */
    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.记录单测试项目配置.getValue();
    }
}
