package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.lims.pro.dto.DtoCommentComplimentDetail;
import com.sinoyd.lims.pro.repository.CommentComplimentDetailRepository;
import com.sinoyd.lims.pro.service.CommentComplimentDetailService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.stereotype.Service;


/**
 * CommentComplimentDetail操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class CommentComplimentDetailServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCommentComplimentDetail,String,CommentComplimentDetailRepository> implements CommentComplimentDetailService {

    @Override
    public void findByPage(PageBean<DtoCommentComplimentDetail> pb, BaseCriteria commentComplimentDetailCriteria) {
        pb.setEntityName("DtoCommentComplimentDetail a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, commentComplimentDetailCriteria);
    }
}