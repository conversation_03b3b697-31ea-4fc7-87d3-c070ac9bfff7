CREATE TRIGGER "AFTER_UPDATE_PROJECT"
    AFTER UPDATE
    ON "TB_PRO_PROJECT"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    IF new.isDeleted = 1 THEN
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_pro_project', new.id, new.modifier, new.modifyDate, 3, null,
        null, null, new.orgId, new.domainId);
END IF;
end;