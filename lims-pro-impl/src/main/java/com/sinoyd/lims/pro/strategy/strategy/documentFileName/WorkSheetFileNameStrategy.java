package com.sinoyd.lims.pro.strategy.strategy.documentFileName;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.dto.DtoWorkSheet;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.WorkSheetRepository;
import com.sinoyd.lims.pro.service.WorkSheetFolderService;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作单附件名称生成
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/28
 */
@Component(IFileNameConstant.FileNameStrategyKey.WORKSHEET_FILENAME)
public class WorkSheetFileNameStrategy extends AbsDocumentFileNameStrategy {

    private WorkSheetFolderService workSheetFolderService;

    private WorkSheetRepository workSheetRepository;

    private TestService testService;

    @Override
    public Map<String, String> generateDocumentName(Map<String, Object> map) {
        Map<String, String> workMap = new HashMap<>();
        if (map.containsKey(EnumPRO.EnumDocumnetName.工作单.getValue())) {
            String reportId = map.get(EnumPRO.EnumDocumnetName.工作单.getValue()).toString();
            if (StringUtil.isNotEmpty(reportId)) {
                DtoWorkSheetFolder folder = workSheetFolderService.findOne(reportId);
                if (StringUtil.isNotNull(folder)) {
                    workMap.put("workSheetCode", folder.getWorkSheetCode());
                    workMap.put("factorName", getFactorName(folder));
                }
            }
        }
        return workMap;
    }

    /**
     * 获取因子名称
     *
     * @param folder 工作单实体
     * @return 因子名称字符
     */
    private String getFactorName(DtoWorkSheetFolder folder) {
        List<DtoWorkSheet> workSheetList = workSheetRepository.findByParentId(folder.getId());
        List<String> testIds = workSheetList.stream().map(DtoWorkSheet::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        List<String> parentIds = testList.stream().map(DtoTest::getParentId).distinct().collect(Collectors.toList());
        // 总称测试项目
        List<DtoTest> testOfTotal = StringUtil.isNotEmpty(parentIds) ? testService.findAllDeleted(parentIds) : new ArrayList<>();
        if (StringUtil.isNotNull(testOfTotal)) {
            testList = testService.getTestTotal(testList, testOfTotal);
        }
        return testList.stream().map(DtoTest::getRedAnalyzeItemName).collect(Collectors.joining("、"));
    }

    @Autowired
    @Lazy
    public void setWorkSheetFolderService(WorkSheetFolderService workSheetFolderService) {
        this.workSheetFolderService = workSheetFolderService;
    }

    @Autowired
    @Lazy
    public void setWorkSheetRepository(WorkSheetRepository workSheetRepository) {
        this.workSheetRepository = workSheetRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }
}
