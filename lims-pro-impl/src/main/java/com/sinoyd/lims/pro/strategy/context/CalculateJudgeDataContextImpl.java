package com.sinoyd.lims.pro.strategy.context;

import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;
import com.sinoyd.lims.pro.strategy.strategy.judgeData.AbsJudgeDataStrategy;
import com.sinoyd.lims.strategy.context.CalculateJudgeDataContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Component
public class CalculateJudgeDataContextImpl implements CalculateJudgeDataContext {

    /**
     * 所有具体生成策略字典
     */
    private final Map<String, AbsJudgeDataStrategy> judgeDataStrategyMap = new ConcurrentHashMap<>();

    @Autowired
    public CalculateJudgeDataContextImpl(Map<String, AbsJudgeDataStrategy> judgeDataStrategyMap) {
        this.judgeDataStrategyMap.putAll(judgeDataStrategyMap);
    }

    /**
     * 生成附件名称
     *
     * @param judgeDataList 比对数据
     */
    @Override
    public void calculateJudgeData(Integer checkType, List<DtoSampleJudgeData> judgeDataList) {
        String beanName = EnumBase.EnumCheckType.getBeanName(checkType);
        if (!StringUtil.isNotNull(this.judgeDataStrategyMap.get(beanName))) {
            throw new BaseException("调用方法不合法");
        }
        this.judgeDataStrategyMap.get(beanName).calculateJudgeData(judgeDataList);
    }
}
