package com.sinoyd.lims.monitor.service.impl;


import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.monitor.dto.customer.DtoTestPointVo;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.lims.DtoProperty2Point;
import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.monitor.repository.lims.Property2PointRepository;
import com.sinoyd.lims.monitor.repository.lims.PropertyPoint2TestRepository;
import com.sinoyd.lims.monitor.service.FixedpointService;
import com.sinoyd.lims.monitor.service.Property2PointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * Property2Point操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class Property2PointServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoProperty2Point, String, Property2PointRepository> implements Property2PointService {

    @Autowired
    private FixedpointRepository fixedpointRepository;

    @Autowired
    private FixedpointService fixedpointService;

    @Autowired
    private PropertyPoint2TestRepository propertyPoint2TestRepository;

    @Autowired
    private CodeService codeService;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private AnalyzeMethodRepository analyzeMethodRepository;

    @Override
    public void findByPage(PageBean<DtoProperty2Point> pb, BaseCriteria property2PointCriteria) {
        pb.setEntityName("DtoProperty2Point a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, property2PointCriteria);
    }

    @Override
    public List<DtoProperty2Point> loadProperty2PointList(String propertyId) {
        List<DtoProperty2Point> dataList = repository.findByPropertyId(propertyId);
        //冗余点位信息
        if (StringUtil.isNotEmpty(dataList)) {
            List<String> p2pIds = dataList.parallelStream().map(DtoProperty2Point::getId).collect(Collectors.toList());

            //获取点位类型字典列表
            List<DtoCode> folderTypeCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.ENV_QUALITY_FOLDER_TYPE);
            //获取关联点位实体列表
            List<String> pointIds = dataList.parallelStream().map(DtoProperty2Point::getFixedPointId).collect(Collectors.toList());
            List<DtoFixedpoint> pointList = fixedpointRepository.findByIdIn(pointIds);
            //获取关联点位的关联测试项目列表
            List<DtoPropertyPoint2Test> pp2tList = new ArrayList<>();
            List<DtoTest> testList = new ArrayList<>();
            if (StringUtil.isNotEmpty(p2pIds)) {
                pp2tList = propertyPoint2TestRepository.findByPropertyPointIdIn(p2pIds);
                List<String> testIds = pp2tList.parallelStream().map(DtoPropertyPoint2Test::getTestId).distinct().collect(Collectors.toList());
                testList = StringUtil.isNotEmpty(testIds) ? testService.findAllDeleted(testIds) : new ArrayList<>();
            }
            List<String> analyzeMethodIds = testList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
            List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(analyzeMethodIds) ? analyzeMethodRepository.findAllDeleted(analyzeMethodIds) : new ArrayList<>();
            for (DtoProperty2Point p2p : dataList) {
                DtoFixedpoint point = pointList.parallelStream().filter(p -> p2p.getFixedPointId().equals(p.getId()))
                        .findFirst().orElse(null);
                List<DtoPropertyPoint2Test> thisPp2tList = pp2tList.parallelStream().filter(p -> p2p.getId().equals(p.getPropertyPointId()))
                        .collect(Collectors.toList());
                //冗余点位关联测试项目名称、因子个数
                if (StringUtil.isNotEmpty(thisPp2tList)) {
                    List<String> thisTestIds = thisPp2tList.parallelStream().map(DtoPropertyPoint2Test::getTestId).distinct().collect(Collectors.toList());
                    List<DtoTest> thisTestList = testList.parallelStream().filter(t -> thisTestIds.contains(t.getId())).collect(Collectors.toList());
                    thisTestList.sort(Comparator.comparing(DtoTest::getRedAnalyzeItemName));
                    List<String> analyzeItemNameList = thisTestList.stream().map(DtoTest::getRedAnalyzeItemName).collect(Collectors.toList());
                    point.setFactorNum(thisTestList.size());
                    point.setTestIds(thisTestIds);
                    point.setTestName(String.join("、", analyzeItemNameList));
                    List<String> methodIds = thisTestList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
                    List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodList.stream().filter(p -> methodIds.contains(p.getId())).collect(Collectors.toList());
                    // 当点位关联测试项目状态，存在“停用”、“作废”、“删除”的测试项目时，需要测试项目提示
                    if (analyzeMethods.stream().anyMatch(p -> EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(p.getStatus())
                            || EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(p.getStatus()) || p.getIsDeleted()) ||
                            thisTestList.stream().anyMatch(DtoTest::getIsDeleted)) {
                        point.setIsTestTip(Boolean.TRUE);
                    }
                }
                //冗余点位类型名称
                fixedpointService.loadFolderTypeNames(point, folderTypeCodeList);
//                Optional<DtoCode> folderTypeOptional = folderTypeCodeList.parallelStream()
//                        .filter(p -> p.getDictCode().equals(point.getFolderType())).findFirst();
//                folderTypeOptional.ifPresent(code -> point.setFolderTypeName(code.getDictName()));
                p2p.setDtoFixedpoint(point);
            }
        }
        dataList.sort((o1, o2) -> {
            DtoFixedpoint p1 = o1.getDtoFixedpoint();
            DtoFixedpoint p2 = o2.getDtoFixedpoint();
            return p2.getOrderNum().compareTo(p1.getOrderNum());
        });
        return dataList;
    }

    @Transactional
    @Override
    public void batchAddTest(Map<String, Object> paramters) {
        List<String> pp2pIds = (List<String>) paramters.get("pp2pIds");
        List<Map<String, Object>> testList = (List<Map<String, Object>>) paramters.get("testList");

        List<DtoPropertyPoint2Test> pp2tList = propertyPoint2TestRepository.findByPropertyPointIdIn(pp2pIds);
        List<DtoPropertyPoint2Test> addList = new ArrayList<>();
        pp2pIds.forEach(pp2pId -> {
            testList.forEach(test -> {
                String testId = (String) test.get("id");
                Optional<DtoPropertyPoint2Test> optional = pp2tList.parallelStream()
                        .filter(p -> p.getPropertyPointId().equals(pp2pId)
                                && p.getTestId().equals(testId)).findFirst();
                //判定是否已经存在该测试项目，如果存在就不添加
                if (!optional.isPresent()) {
                    DtoPropertyPoint2Test dto = new DtoPropertyPoint2Test();
                    dto.setPropertyPointId(pp2pId);
                    dto.setTestId(testId);
                    dto.setTimesOrder((Integer) test.get("timesOrder"));
                    dto.setSamplePeriod((Integer) test.get("samplePeriod"));
                    addList.add(dto);
                }
            });
        });
        propertyPoint2TestRepository.save(addList);
    }

    @Transactional
    @Override
    public void batchDeleteTest(Map<String, List<String>> parameters) {
        List<String> testIds = parameters.get("testIds");
        List<String> pp2pIds = parameters.get("pp2pIds");
        List<DtoPropertyPoint2Test> pp2tList = propertyPoint2TestRepository.findByPropertyPointIdIn(pp2pIds);
        List<DtoPropertyPoint2Test> deleteDtoList = new ArrayList<>();
        for (String pp2pId : pp2pIds) {
            for (String testId : testIds) {
                Optional<DtoPropertyPoint2Test> optional = pp2tList.parallelStream()
                        .filter(p -> p.getPropertyPointId().equals(pp2pId) && testId.equals(p.getTestId()))
                        .findFirst();
                optional.ifPresent(deleteDtoList::add);
            }
        }
        propertyPoint2TestRepository.delete(deleteDtoList);
    }

    /**
     * 批量修改批次及样品数
     *
     * @param pointVo 集合
     */
    @Transactional
    @Override
    public void batchTimesOrSampleOrder(DtoTestPointVo pointVo) {
        List<DtoPropertyPoint2Test> point2TestList = propertyPoint2TestRepository
                .findByPropertyPointIdAndTestIdIn(pointVo.getPropertyPointId(), pointVo.getTestIds());
        point2TestList.forEach(p -> {
            p.setTimesOrder(pointVo.getTimesOrder());
            p.setSamplePeriod(pointVo.getSamplePeriod());
        });
        propertyPoint2TestRepository.save(point2TestList);
    }
}