package com.sinoyd.lims.od.strategy.context;

import com.sinoyd.lims.od.dto.DtoLabGroup;
import com.sinoyd.lims.od.dto.DtoSampleResult;
import com.sinoyd.lims.od.dto.customer.DtoGroupSummaryTemp;
import com.sinoyd.lims.od.dto.customer.DtoSampleResultTemp;
import io.swagger.models.auth.In;

import java.util.List;

public interface SampleResultContext {


    /**
     * 获取最终结果实验组
     *
     * @param taskType 任务类型
     * @param sampleId 样品id
     * @return 获取最终结果实验组
     */
    List<DtoGroupSummaryTemp> findDefaultGroupBySampleId(Integer taskType, String sampleId);


    /**
     * 获取实验组汇总信息
     *
     * @param taskType    任务类型
     * @param groupSummaryTemps 实验数据汇总临时传输实体
     * @return 样品结果
     */
    List<DtoLabGroup> findGroupSummary(Integer taskType, List<DtoGroupSummaryTemp> groupSummaryTemps);


    /**
     * 计算样品结果
     *
     * @param taskType         任务类型
     * @param sampleResultTemp 样品结果汇总传输实体
     * @return 计算样品结果
     */
    DtoSampleResult calculate(Integer taskType, DtoSampleResultTemp sampleResultTemp);
}
