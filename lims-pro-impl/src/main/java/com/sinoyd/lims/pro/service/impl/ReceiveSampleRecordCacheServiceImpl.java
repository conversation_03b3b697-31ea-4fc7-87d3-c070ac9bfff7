package com.sinoyd.lims.pro.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.customer.DtoRecordJson;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.AnalyseDataRepository;
import com.sinoyd.lims.pro.repository.ReceiveSampleRecordRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 送样单缓存数据的实现
 * <AUTHOR>
 * @version V1.0.0 2020/03/06
 * @since V100R001
 */
@Service
public class ReceiveSampleRecordCacheServiceImpl implements ReceiveSampleRecordCacheService {
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    /**
     * 自增初始值
     */
    private final long INITIAL_VALUE = 1L;

    /**
     * 送样单json字段更新
     *
     * @param receiveId            送样单id
     * @param principalContextUser 当前人员信息
     */
    @Transactional
    @Async
    @Override
    public void updateRecordJson(String receiveId, CurrentPrincipalUser principalContextUser) {
        //重新赋值登录的账号信息
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(principalContextUser,
                null, authorities));
        updateRecordJson(receiveId);
    }

    /**
     * 送样单json字段更新
     *
     * @param receiveId 送样单id
     */
    private void updateRecordJson(String receiveId) {
        String key = EnumPRO.EnumPRORedis.getRedisKey(EnumPRO.EnumPRORedis.PRO_OrgId_ReceiveJsonTemp.getValue()) + receiveId;
        //先进行自增，若原先没有该key，则自增后值为1
        long value = redisTemplate.opsForValue().increment(key, 1);
        redisTemplate.expire(key, 1, TimeUnit.MINUTES);
        if (value == INITIAL_VALUE) {//表明之前没有，刚刚自增到1
            while (true) {
                List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
                //排除比对质控
                sampleList = sampleList.stream().filter(p -> !EnumPRO.EnumSampleCategory.比对评价样.getValue().equals(p.getSampleCategory()))
                        .collect(Collectors.toList());
                String sampleTypeIds = String.join(",", sampleList.stream().map(DtoSample::getSampleTypeId).collect(Collectors.toSet()));

                //写入json信息
                DtoRecordJson jsonEntity = new DtoRecordJson();
                jsonEntity.setSampleNum(sampleList.size());
                jsonEntity.setSampleTypeIds(sampleTypeIds);

                List<DtoAnalyseData> analyseDataList = sampleList.size() > 0 ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleList.stream().map(DtoSample::getId).collect(Collectors.toList())) : new ArrayList<>();
                List<String> labSampleIds = analyseDataList.stream().filter(p -> !p.getIsCompleteField() && !p.getIsOutsourcing() && !p.getIsSamplingOut()).map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
                List<String> labSampleTypeIds = sampleList.stream().filter(p -> labSampleIds.contains(p.getId())).map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
                List<DtoSampleType> sampleTypes = labSampleTypeIds.size() > 0 ? sampleTypeService.findRedisByIds(labSampleTypeIds) : new ArrayList<>();
                sampleTypes.sort(Comparator.comparing(DtoSampleType::getTypeName));
                jsonEntity.setLabSampleTypes(String.join(",", sampleTypes.stream().map(DtoSampleType::getTypeName).collect(Collectors.toList())));

                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    receiveSampleRecordRepository.updateJson(receiveId, objectMapper.writeValueAsString(jsonEntity));
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
                if (!redisTemplate.hasKey(key)) {
                    break;
                } else if (String.valueOf(redisTemplate.opsForValue().get(key)).equals(String.valueOf(INITIAL_VALUE))) {
                    redisTemplate.delete(key);//先移除该key
                    break;
                } else {
                    redisTemplate.opsForValue().set(key, INITIAL_VALUE);
                    redisTemplate.expire(key, 1, TimeUnit.MINUTES);
                }
            }
        }
    }
}
