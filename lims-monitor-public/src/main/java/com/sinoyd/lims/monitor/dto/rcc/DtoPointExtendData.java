package com.sinoyd.lims.monitor.dto.rcc;

import com.sinoyd.lims.monitor.entity.PointExtendData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * TB_MONITOR_PointExtendData实体
 *
 * <AUTHOR>
 * @version V5.2.0 2023/06/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_PointExtendData")
@Data
@DynamicInsert
public class DtoPointExtendData extends PointExtendData {

    /**
     * 常量类型
     */
    @Transient
    private String codeType;

    /**
     * 拓展字段配置的数据源类型
     */
    @Transient
    private Integer dataSourceType;

    /**
     * 水体数据
     */
    @Transient
    private DtoWater water;
}
