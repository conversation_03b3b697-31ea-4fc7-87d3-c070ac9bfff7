package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.probase.service.CheckProjectFactoryService;
import com.sinoyd.lims.probase.service.CheckProjectService;
import org.springframework.stereotype.Service;

@Service
public class CheckProjectFactoryServiceImpl implements CheckProjectFactoryService {

    /**
     * 不同项目核对项目状态
     * @param type 包名
     * @param id 项目id
     */
    public void checkProjectByType(String type, String  id)
    {
        try {
            Class checkClass = Class.forName(type);
            //用描述作为包的命名
            CheckProjectService checkProjectService = SpringContextAware.getBean(checkClass);
            checkProjectService.checkProject(id);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            throw new BaseException(ex);
        }
    }
}
