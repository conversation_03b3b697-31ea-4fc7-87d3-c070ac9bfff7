package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoAnalyseAchievementDetails;

import javax.servlet.http.HttpServletResponse;

public interface AnalyseAchievementDetailsService extends IBaseJpaService<DtoAnalyseAchievementDetails, String> {

    /**
     * 导出
     * @param response 响应
     * @param criteria 查询条件
     */
    void export(HttpServletResponse response, BaseCriteria criteria);

}
