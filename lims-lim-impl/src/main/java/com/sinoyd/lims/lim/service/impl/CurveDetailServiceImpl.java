package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoCurve;
import com.sinoyd.lims.lim.dto.lims.DtoCurveDetail;
import com.sinoyd.lims.lim.dto.customer.LeastSquare;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.CurveDetailRepository;
import com.sinoyd.lims.lim.service.CurveDetailService;
import com.sinoyd.lims.lim.service.ReviseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * Curve操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/5
 * @since V100R001
 */
@Service
public class CurveDetailServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCurveDetail, String, CurveDetailRepository> implements CurveDetailService {

    /**
     * 默认斜率有效位数
     */
    private final Integer K_VALUE_FORMAT = 3;

    /**
     * 默认截距小数位数
     */
    private final Integer B_VALUE_FORMAT = 3;

    /**
     * 默认实数有效位数
     */
    private final Integer C_VALUE_FORMAT = 3;


    private ReviseService reviseService;

    /**
     * 默认截距，斜率，实数有效位数
     */
    protected final Integer DEFAULT_SIGNIFICANT_DIGITS = 3;
    /**
     * 默认截距，斜率，实数小数位数
     */
    protected final Integer DEFAULT_DECIMAL_DIGITS = 3;

    /**
     * 正则表达式,数值判断
     */
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");

    @Override
    public void findByPage(PageBean<DtoCurveDetail> pb, BaseCriteria curveCriteria) {
        pb.setEntityName("DtoCurveDetail a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, curveCriteria);
    }

    @Override
    @Transactional
    public List<DtoCurveDetail> save(Collection<DtoCurveDetail> entities) {
        List<DtoCurveDetail> details = new ArrayList<>();
        for (DtoCurveDetail detail : entities) {
            if(Boolean.TRUE.equals(detail.getIsDimensionRow())){
                super.save(detail);
            }else{
                if (this.isNumber(detail.getXValue()) && this.isNumber(detail.getYValue())) {
                    super.save(detail);
                    details.add(detail);
                }
            }
        }
        return details;
    }

    @Override
    public List<DtoCurveDetail> findCurveDetailByCurveIds(List<String> curveIds) {
        return repository.findByCurveIdIn(curveIds);
    }

    /**
     * 计算曲线斜率截距
     *
     * @param curveDetail 曲线详单List
     * @param curve       标准曲线
     */
    @Override
    public DtoCurve calculation(List<DtoCurveDetail> curveDetail, DtoCurve curve) {
        List<BigDecimal> xValues = new ArrayList<>();
        List<BigDecimal> yValues = new ArrayList<>();
        BigDecimal zeroPoint = this.isNumber(curve.getZeroPoint()) ? new BigDecimal(curve.getZeroPoint()) : BigDecimal.ZERO;
        for (DtoCurveDetail detail : curveDetail) {
            if (this.isNumber(detail.getXValue()) && this.isNumber(detail.getYValue())) {
                dealXYValues(xValues, yValues, zeroPoint, detail);
            } else if (StringUtils.isNotNullAndEmpty(detail.getXValue()) && StringUtils.isNotNullAndEmpty(detail.getYValue())) {
                throw new BaseException("存在输入数值格式不正确！");
            } else {
                throw new BaseException("存在未输入的数值！");
            }
        }

        List<BigDecimal> sameXValue = xValues.stream().distinct().collect(Collectors.toList());
        if (sameXValue.size() == 1) {
            throw new BaseException("至少需要一个不同的X值才可计算");
        }
        List<BigDecimal> sameYValue = yValues.stream().distinct().collect(Collectors.toList());
        if (sameYValue.size() == 1) {
            throw new BaseException("至少需要一个不同的Y值才可计算");
        }

        Integer should = curve.getCurveType().equals(EnumLIM.EnumCurveType.二次型.getValue()) ? 3 : 2;
        if (xValues.size() < should) {
            throw new BaseException(String.format("数据列表至少添加%s组数据才能够计算！",
                    curve.getCurveType().equals(EnumLIM.EnumCurveType.二次型.getValue()) ? "三" : "二"));
        }
        try {
            LeastSquare square = new LeastSquare(xValues.toArray(new BigDecimal[xValues.size()]), yValues.toArray(new BigDecimal[yValues.size()]),
                    curve.getCurveType().equals(EnumLIM.EnumCurveType.二次型.getValue()) ? 3 : 2, curve.getForcedZero());
            //相关系数 使用去尾法 Round_Down 保留四位 存在正负的情况
            BigDecimal coefficent = square.computeRCoefficent();
            coefficent = coefficent.setScale(4, BigDecimal.ROUND_DOWN);
            curve.setCoefficient(coefficent.toString());
            BigDecimal k = curve.getCurveType().equals(EnumLIM.EnumCurveType.二次型.getValue()) ? square.getCoefficient()[2] : square.getCoefficient()[1];
            BigDecimal b = curve.getCurveType().equals(EnumLIM.EnumCurveType.二次型.getValue()) ? square.getCoefficient()[1] : square.getCoefficient()[0];
            BigDecimal c = curve.getCurveType().equals(EnumLIM.EnumCurveType.二次型.getValue()) ? square.getCoefficient()[0] : BigDecimal.ZERO;

            if (useSci()) {
                curve.setKValue(this.calcFormat(curve.getKValueFormat(), curve.getKDecimalFormat(), k));
                curve.setBValue(this.calcFormat(curve.getBValueFormat(), curve.getBDecimalFormat(), b));
            } else {
                //有效位数
                curve.setKValue(this.getKValue(curve, k));
                //小数位数
                curve.setBValue(this.getBValue(curve, b));
            }

            if (EnumLIM.EnumCurveType.二次型.getValue().equals(curve.getCurveType())) {
                if (useSci()) {
                    curve.setCValue(this.calcFormat(curve.getCValueFormat(), curve.getCDecimalFormat(), c));
                } else {
                    curve.setCValue(this.getCValue(curve, c));
                }
            } else {
                curve.setCValue("");
            }
        } catch (Exception e) {
            throw new BaseException("输入的数据不满足计算条件");
        }

        return curve;
    }

    protected void dealXYValues(List<BigDecimal> xValues, List<BigDecimal> yValues, BigDecimal zeroPoint, DtoCurveDetail detail) {
        xValues.add(new BigDecimal(detail.getXValue()));
        yValues.add((new BigDecimal(detail.getYValue())).subtract(zeroPoint));
    }

    /**
     * 是否使用科学计数法，产品默认不使用
     *
     * @return true: 使用， false: 不使用
     */
    protected boolean useSci() {
        return Boolean.FALSE;
    }

    /**
     * 字符串是否是数字
     *
     * @param string 字符串
     * @return 是否数字
     */
    private boolean isNumber(String string) {
        if (StringUtil.isNull(string)) {
            return false;
        }
        return NUMBER_PATTERN.matcher(string).matches();
    }

    protected String calcFormat(int significantDigits, int decimalDigits, BigDecimal value) {
        if (significantDigits == -1 && decimalDigits == -1) {
            significantDigits = DEFAULT_SIGNIFICANT_DIGITS;
            decimalDigits = DEFAULT_DECIMAL_DIGITS;
        }
        //修约
        String reviseValue =  reviseService.getDecimal(significantDigits,decimalDigits,value.toString());
        return reviseValue;
    }

    /**
     * 获取k值
     *
     * @param curve 标线
     * @param k     k值
     * @return k值
     */
    private String getKValue(DtoCurve curve, BigDecimal k) {
        Integer significance = curve.getKValueFormat() < 0 ? K_VALUE_FORMAT : curve.getKValueFormat();
        k = new BigDecimal(k.toString(), new MathContext(significance, RoundingMode.HALF_EVEN));
        StringBuilder stringBuilder = new StringBuilder(k.toString());
        String kValue = k.toString().replace(".", "").replace("-", "");
        if (significance > kValue.length() && !k.toString().contains(".")) {
            stringBuilder.append(".");
        }
        for (Integer i = 0; i < significance - kValue.length(); i++) {
            stringBuilder.append("0");
        }
        return stringBuilder.toString();
    }

    /**
     * 获取b值
     *
     * @param curve 标线
     * @param b     b值
     * @return b值
     */
    private String getBValue(DtoCurve curve, BigDecimal b) {
        Integer decimals = curve.getBValueFormat() < 0 ? B_VALUE_FORMAT : curve.getBValueFormat();
        if (decimals.equals(0)) {
            return b.setScale(0, BigDecimal.ROUND_HALF_EVEN).toString();
        }
        return String.format("%." + decimals + "f", b.setScale(decimals, BigDecimal.ROUND_HALF_EVEN));
    }

    /**
     * 获取c值
     *
     * @param curve 标线
     * @param c     c值
     * @return c值
     */
    private String getCValue(DtoCurve curve, BigDecimal c) {
        Integer significance = curve.getCValueFormat() < 0 ? C_VALUE_FORMAT : curve.getCValueFormat();
        c = new BigDecimal(c.toString(), new MathContext(significance, RoundingMode.HALF_EVEN));
        StringBuilder stringBuilder = new StringBuilder(c.toString());
        String cValue = c.toString().replace(".", "").replace("-", "");
        if (significance > cValue.length() && !c.toString().contains(".")) {
            stringBuilder.append(".");
        }
        for (Integer i = 0; i < significance - cValue.length(); i++) {
            stringBuilder.append("0");
        }
        return stringBuilder.toString();
    }


    @Autowired
    @Lazy
    public void setDataFormatService(ReviseService reviseService) {
        this.reviseService = reviseService;
    }
}