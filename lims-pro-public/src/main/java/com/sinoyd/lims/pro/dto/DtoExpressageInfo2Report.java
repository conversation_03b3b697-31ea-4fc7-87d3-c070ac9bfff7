package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ExpressageInfo2Report;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoExpressageInfo2Report实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_ExpressageInfo2Report")
 @Data
 @DynamicInsert
 public  class DtoExpressageInfo2Report extends ExpressageInfo2Report {

    /** 
     * 报告编号
    */
    @Transient
    private String code;
 }