package com.sinoyd.base.service;

import com.sinoyd.frame.base.entity.BaseEntity;

import java.util.Collection;
import java.util.List;

public interface CommonBatchRepository {

    /**
     * 批量插入
     *
     * @param entities 实体集合
     */
    void batchInsert(List<? extends BaseEntity> entities);

    /**
     * 批量更新
     *
     * @param entities 实体集合
     */
    void batchUpdate(List<? extends BaseEntity> entities);

    /**
     * 批量删除
     *
     * @param ids 主键集合
     * @param entityClass 实体类型
     */
    void batchDelete(Collection<?> ids, Class<?> entityClass);
}
