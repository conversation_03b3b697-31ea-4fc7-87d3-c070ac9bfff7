CREATE  TRIGGER "AFTER_UPDATE_PARAMSPARTFORMULA"
    AFTER  UPDATE
    ON "TB_LIM_PARAMSPARTFORMULA"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    if new.formulaId != old.formulaId or (new.formulaId is null and old.formulaId is not null) or
       (new.formulaId is not null and old.formulaId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'formulaId', old.formulaId, new.formulaId, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.formula != old.formula or (new.formula is null and old.formula is not null) or
            (new.formula is not null and old.formula is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'formula',
        old.formula, new.formula, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.paramsName != old.paramsName or (new.paramsName is null and old.paramsName is not null) or
            (new.paramsName is not null and old.paramsName is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'paramsName', old.paramsName, new.paramsName, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.mostSignificance != old.mostSignificance or
            (new.mostSignificance is null and old.mostSignificance is not null) or
            (new.mostSignificance is not null and old.mostSignificance is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'mostSignificance', old.mostSignificance, new.mostSignificance, new.orgId,
        '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.mostDecimal != old.mostDecimal or (new.mostDecimal is null and old.mostDecimal is not null) or
            (new.mostDecimal is not null and old.mostDecimal is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'mostDecimal', old.mostDecimal, new.mostDecimal, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.orderNum != old.orderNum or (new.orderNum is null and old.orderNum is not null) or
            (new.orderNum is not null and old.orderNum is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'orderNum', old.orderNum, new.orderNum, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.detectionLimit != old.detectionLimit or
            (new.detectionLimit is null and old.detectionLimit is not null) or
            (new.detectionLimit is not null and old.detectionLimit is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'detectionLimit', old.detectionLimit, new.detectionLimit, new.orgId,
        '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.calculationMode != old.calculationMode or
            (new.calculationMode is null and old.calculationMode is not null) or
            (new.calculationMode is not null and old.calculationMode is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'calculationMode', old.calculationMode, new.calculationMode, new.orgId,
        '00000000-0000-0000-0000-000000000000');
END IF;
if
            new.useTestLimit != old.useTestLimit or (new.useTestLimit is null and old.useTestLimit is not null) or
            (new.useTestLimit is not null and old.useTestLimit is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_paramspartformula', new.id, '00000000-0000-0000-0000-000000000000', now(), 3,
        'useTestLimit', old.useTestLimit, new.useTestLimit, new.orgId, '00000000-0000-0000-0000-000000000000');
END IF;
end;
