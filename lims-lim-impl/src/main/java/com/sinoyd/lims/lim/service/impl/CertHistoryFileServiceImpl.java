package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoCertHistoryFile;
import com.sinoyd.lims.lim.repository.lims.CertHistoryFileRepository;
import com.sinoyd.lims.lim.service.CertHistoryFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CertHistoryFileServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoCertHistoryFile,String, CertHistoryFileRepository>
        implements CertHistoryFileService {

    @Override
    public DtoCertHistoryFile findAttachPath(String id) {
        return repository.findOne(id);
    }
}
