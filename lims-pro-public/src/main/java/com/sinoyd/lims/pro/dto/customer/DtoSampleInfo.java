package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 现场任务样品信息
 * <AUTHOR>
 * @version V1.0.0 2019/12/20
 * @since V100R001
 */
@Data
public class DtoSampleInfo {
    /**
     * 参数配置
     */
    private List<DtoParamsConfig> paramsConfig = new ArrayList<>();

    /**
     * 样品数据
     */
    private List<Map<String, Object>> sample = new ArrayList<>();

    /**
     * 排序类型
     */
    private Integer sortType = EnumPRO.EnumSampleParamsSortType.按照样品编号排序.getValue();

    /**
     * 是否按照点位录入
     */
    private Boolean isFolder = false;
}
