package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * WorkSheetReagent查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkSheetReagentCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 检测单id
     */
    private String workSheetFolderId;

    /**
     * 方法id
     */
    private String analyzeMethodId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and r.reagentConfigId = c.id");

        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and c.configDate >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and c.configDate < :endTime");
            values.put("endTime", to);
        }
        if (StringUtils.isNotNullAndEmpty(this.workSheetFolderId) && !UUIDHelper.GUID_EMPTY.equals(this.workSheetFolderId)) {
            condition.append(" and r.worksheetFolderId = :workSheetFolderId");
            values.put("workSheetFolderId", this.workSheetFolderId);
        }

        if (StringUtils.isNotNullAndEmpty(this.analyzeMethodId) && !UUIDHelper.GUID_EMPTY.equals(this.analyzeMethodId)) {
            condition.append(" and c.analyzeMethodId = :analyzeMethodId");
            values.put("analyzeMethodId", this.analyzeMethodId);
        }
        return condition.toString();
    }
}