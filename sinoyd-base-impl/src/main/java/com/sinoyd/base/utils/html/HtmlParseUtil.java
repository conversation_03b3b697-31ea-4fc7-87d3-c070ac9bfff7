package com.sinoyd.base.utils.html;

import com.sinoyd.base.annotations.HtmlTableHead;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.stream.Collectors;


/**
 * Html解析工具类
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/21
 * @since V100R001
 */
public class HtmlParseUtil {

    /**
     * 设置字段值
     * 1.根据注解中的col属性来进行列值绑定
     *
     * @param entity 实体对象
     * @param header 列名称
     * @param value  列值
     * @param <T>    泛型
     */
    public static <T> void setFieldValue(T entity, String header, String value) {
        Field[] fields = entity.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(HtmlTableHead.class)) {
                HtmlTableHead annotation = field.getAnnotation(HtmlTableHead.class);
                if (Arrays.stream(annotation.cols()).collect(Collectors.toSet()).contains(header)
                        || Arrays.stream(annotation.value()).collect(Collectors.toSet()).contains(header)) {
                    field.setAccessible(true);
                    try {
                        // 支持更多字段类型转换
                        if (field.getType() == String.class) {
                            field.set(entity, value);
                        } else if (field.getType() == Boolean.class || field.getType() == boolean.class) {
                            field.set(entity, "是".equals(value));
                        } else if (field.getType() == Integer.class || field.getType() == int.class) {
                            field.set(entity, Integer.parseInt(value));
                        } else if (field.getType() == Double.class || field.getType() == double.class) {
                            field.set(entity, Double.parseDouble(value));
                        } else {
                            throw new IllegalArgumentException("不支持的字段类型: " + field.getType());
                        }
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException("无法设置字段值: " + field.getName(), e);
                    } catch (NumberFormatException e) {
                        throw new RuntimeException("字段值转换失败: " + field.getName(), e);
                    }
                    break;
                }
            }
        }
    }
}
