-- 项目表添加触发器，删除记录
drop trigger if exists after_update_project;
DELIMITER $$
CREATE TRIGGER after_update_project
    AFTER UPDATE
    ON tb_pro_project
    FOR EACH ROW
BEGIN
    if new.isDeleted = 1 then
        INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                           oldValue, newValue, orgId, domainId)
        VALUES (UUID(), 'tb_pro_project', new.id, new.modifier, new.modifyDate, 2, null,
                null, null, new.orgId, new.domainId);
END IF;
end $$
DELIMITER ;
