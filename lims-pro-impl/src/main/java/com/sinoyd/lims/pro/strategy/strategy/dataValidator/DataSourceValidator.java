package com.sinoyd.lims.pro.strategy.strategy.dataValidator;

import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.strategy.IFileNameConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 日期时间校验
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/09/23
 */
@Component(IFileNameConstant.DataValidateStrategyKey.DATA_SOURCE_VALIDATE)
@Slf4j
public class DataSourceValidator extends AbsDataValidator {

    @Override
    public Boolean validate(Object value, Map<String, Object> map) {
        String valStr = StringUtil.isNotNull(value) ? value.toString() : "";
        if (StringUtil.isEmpty(valStr)) {
            return true;
        }
        String dataSource = map.getOrDefault("dataSource", "").toString();
        if (StringUtil.isNotEmpty(dataSource)) {
            try {
                List<Map<String, Object>> dataSrcMapList = JsonUtil.toObject(dataSource, List.class);
                List<String> keyList = new ArrayList<>();
                for (Map<String, Object> dataSrcMap : dataSrcMapList) {
                    String loopKey = StringUtil.isNotNull(dataSrcMap.get("key")) ? dataSrcMap.getOrDefault("key", "").toString() : "";
                    if (!keyList.contains(loopKey)) {
                        keyList.add(loopKey);
                    }
                }
                if(!keyList.contains(valStr)) {
                    return false;
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return true;
    }

    @Override
    public Integer getControlType() {
        return 4;
    }
}
