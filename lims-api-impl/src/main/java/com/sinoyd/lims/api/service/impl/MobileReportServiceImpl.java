package com.sinoyd.lims.api.service.impl;

import com.aspose.cells.SaveFormat;
import com.aspose.cells.Workbook;
import com.aspose.cells.WorkbookDesigner;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.http.SinoydHttpTool;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.AsposeLicenseUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.api.service.MobileReportService;
import com.sinoyd.lims.lim.criteria.ReportConfigCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.RecordConfigRepository;
import com.sinoyd.lims.lim.service.ReportConfigService;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.repository.CommentRepository;
import com.sinoyd.lims.pro.repository.SampleRepository;
import com.sinoyd.lims.pro.service.ReportService;
import dm.jdbc.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 移动端report服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/05/08
 * @since V100R001
 */
@Service
@Slf4j
public class MobileReportServiceImpl implements MobileReportService {

    @Value("${centerReport.url:}")
    private String url;

    private ReportConfigService reportConfigService;

    private ReportService reportService;

    private SampleRepository sampleRepository;

    @Autowired
    private FilePathConfig filePathConfig;

    private SampleTypeRepository sampleTypeRepository;

    private RecordConfigRepository recordConfigRepository;

    private CommonRepository commonRepository;

    @Override
    public String generate(String reportCode, Map<String, Object> map, HttpServletRequest request) {

        Map data = (Map) generateWebOff(reportCode, map, request);
        if (!(Boolean) data.get("success")) {
            throw new BaseException(data.get("msg").toString());
        }
        File outputFile = new File(filePathConfig.getOutputPath());
        log.info("===================================outputPath：" + filePathConfig.getOutputPath() + "=================================");
        if (!outputFile.exists()) {
            outputFile.mkdirs();
        }
        String webName = outputFile.getName();
        Map resultMap = (Map) data.get("data");
        String webUrl = (String) resultMap.get("webUrl");
        String excelFileName = (String) resultMap.get("fileName");
        log.info("==================================weburl：" + webUrl + "======================================");
        log.info("==================================excelFileName：" + excelFileName + "======================================");
        String fileName = excelFileName.substring(0, excelFileName.lastIndexOf(".")) + ".pdf";
        log.info("====================================pdfFileName：" + fileName + "===================================");
//        webUrl = webUrl.substring(webUrl.lastIndexOf("/"));
        WorkbookDesigner designer = new WorkbookDesigner();
        String pdfOutPath = filePathConfig.getOutputPath() + "/" + fileName;
        log.info("===================================pdfpath：" + pdfOutPath + "===============================");
        try {
            log.info("======================================excelPath：" + filePathConfig.getOutputPath() + "/" + excelFileName + "================================");
            designer.setWorkbook(new Workbook(filePathConfig.getOutputPath() + "/" + excelFileName));
            designer.getWorkbook().save(pdfOutPath, SaveFormat.PDF);
        } catch (Exception e) {
            log.info(e.getMessage());
            throw new BaseException("表单生成失败");
        }

        return webName + "/" + fileName;
    }

    @Override
    public String generateFile(String reportCode, Map<String, Object> map, HttpServletRequest request, HttpServletResponse response) {
        Map data = (Map) generateWebOff(reportCode, map, request);
        if (!(Boolean) data.get("success")) {
            throw new BaseException(data.get("msg").toString());
        }
        String outputPath = filePathConfig.getOutputPath();
        File outputFile = new File(outputPath);
        log.info("===================================outputPath：" + filePathConfig.getOutputPath() + "=================================");
        if (!outputFile.exists()) {
            outputFile.mkdirs();
        }
        Map resultMap = (Map) data.get("data");
        String fileName = (String) resultMap.get("fileName");
        // 导出报表
        return export(outputPath + "/" + fileName, response);
    }


    /**
     * 生成weboffice 报表
     *
     * @param reportCode 报表编码
     * @param map        请求参数map
     * @param request    请求体
     * @return
     */
    private Object generateWebOff(String reportCode, Map<String, Object> map, HttpServletRequest request) {
        if (!AsposeLicenseUtil.isLicense()) {
            throw new RuntimeException("验证远大文件体系证书失败");
        }
        if (!AsposeLicenseUtil.isPdfLicense()) {
            throw new RuntimeException("验证远大文件体系证书失败");
        }
        String receiveId = map.get("receiveSampleRecordId").toString();
        if (StringUtil.isNotEmpty(receiveId) && !UUIDHelper.GUID_EMPTY.equals(receiveId)) {
            List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            map.put("sampleIds", sampleIds);
        }
        String result = SinoydHttpTool.doPost(url + "/report/webOffice/" + reportCode, map,
                SinoydHttpTool.getHttpHeaders(request.getHeader("Authorization")));
        Map data;
        try {
            data = JsonUtil.toObject(result, Map.class);
        } catch (IOException e) {
            throw new BaseException(e.getMessage());
        }
        return data;
    }

    /**
     * 导出附件
     *
     * @param fileName 文件名称
     * @param response 响应头
     * @return
     */
    private String export(String fileName, HttpServletResponse response) {
        File file = new File(fileName);
        //得到该文件
        if (!file.exists()) {
            System.out.println("Have no file!");
            return "没有权限" + fileName;//文件不存在就退出方法
        }
        FileInputStream fileInputStream = null;
        OutputStream outputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
            //设置Http响应头告诉浏览器下载这个附件
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
            outputStream = response.getOutputStream();
            byte[] bytes = new byte[2048];
            int len;
            while ((len = fileInputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
            fileInputStream.close();
            outputStream.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("文件下载出错!");
        } finally {
            FileUtil.close(fileInputStream);
            FileUtil.close(outputStream);
        }
        return null;
    }

    @Override
    public List<Map<String, String>> getSamplingReport(String receiveId, String module, String location) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select r, a.name from DtoReportConfig r,DtoReportApply a where 1 = 1 and a.reportConfigId = r.id and r.isDeleted = 0 ");
        sql.append(" and a.module = :module and a.location = :location and a.type = 1 ");
        Map<String, Object> map = new HashMap<>();
        map.put("module", module);
        map.put("location", location);
        List<Object[]> objects = commonRepository.find(sql.toString(), map);
        objects = objects.stream().distinct().collect(Collectors.toList());
        Iterator<Object[]> iterator = objects.iterator();
        List<DtoReportConfig> reportConfigs = new ArrayList<>();
        while (iterator.hasNext()){
            Object obj = iterator.next();
            Object[] object = (Object[]) obj;
            DtoReportConfig dtoReportConfig = (DtoReportConfig) object[0];
            dtoReportConfig.setName((String) object[1]);
            reportConfigs.add(dtoReportConfig);
        }
        reportConfigs.sort(Comparator.comparing(DtoReportConfig::getOrderNum).reversed().thenComparing(DtoReportConfig::getName));
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeAllList = sampleTypeRepository.getList();
        List<String> bigSampleTypeIds = sampleTypeAllList.stream().filter(p -> sampleTypeIds.contains(p.getId())).map(DtoSampleType::getParentId).distinct().collect(Collectors.toList());
        // 获取所有采样单配置
        List<DtoRecordConfig> recordConfigs = recordConfigRepository.findAllByRecordType(EnumLIM.EnumRecordType.采样记录单.getValue());
        // 根据监测类型过滤，小类-》大类-》 未绑定检测类型
        // 小类
        List<String> reportConfigIdOfSmall = recordConfigs.stream().filter(p -> sampleTypeIds.contains(p.getSampleTypeId())).map(DtoRecordConfig::getReportConfigId).distinct().collect(Collectors.toList());
        // 大类
        List<String> reportConfigIdOfBig = recordConfigs.stream().filter(p -> bigSampleTypeIds.contains(p.getSampleTypeId())).map(DtoRecordConfig::getReportConfigId).distinct().collect(Collectors.toList());
        List<DtoReportConfig> reportConfigList = reportConfigs.stream().filter(p -> reportConfigIdOfSmall.contains(p.getId())).collect(Collectors.toList());
        reportConfigList.addAll(reportConfigs.stream().filter(p -> reportConfigIdOfBig.contains(p.getId()) &&
                !reportConfigIdOfSmall.contains(p.getId())).collect(Collectors.toList()));
        // 未绑定检测类型
        List<DtoReportConfig> notBindRecord = reportConfigs.stream().filter(p -> !recordConfigs.stream().map(DtoRecordConfig::getReportConfigId).collect(Collectors.toList()).contains(p.getId()) &&
                !reportConfigIdOfBig.contains(p.getId()) &&
                !reportConfigIdOfSmall.contains(p.getId())).collect(Collectors.toList());
        reportConfigList.addAll(notBindRecord);

        List<Map<String, String>> maps = new ArrayList<>();
        reportConfigList.forEach(re -> {
            maps.add(new HashMap<String, String>() {
                {
                    put("id", re.getId());
                    put("reportCode", re.getReportCode());
                    put("templateName", re.getName());
                    String spValue = "";
                    String fileName = re.getOutputName();
                    if (fileName.contains(File.separator)) {
                        spValue = File.separator;
                    } else if (fileName.contains("/")) {
                        spValue = "/";
                    }
                    if (StringUtil.isNotEmpty(spValue)) {
                        int len = fileName.split(spValue).length;
                        fileName = fileName.split(spValue)[len - 1];
                    }
                    put("outputName", fileName);
                }
            });
        });
        return maps;
    }

    @Override
    public Map<String, String> documentFileName(Map<String, Object> map, String configId) {
        DtoReportConfig reportConfig = reportService.documentFileName(map, configId);
        Map<String, String> result = new HashMap<>();
        result.put("id", reportConfig.getId());
        result.put("reportCode", reportConfig.getReportCode());
        result.put("fileName", reportConfig.getFileName());
        return result;
    }

    @Autowired
    @Lazy
    public void setReportConfigService(ReportConfigService reportConfigService) {
        this.reportConfigService = reportConfigService;
    }

    @Autowired
    @Lazy
    public void setReportService(ReportService reportService) {
        this.reportService = reportService;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setRecordConfigRepository(RecordConfigRepository recordConfigRepository) {
        this.recordConfigRepository = recordConfigRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }
}
