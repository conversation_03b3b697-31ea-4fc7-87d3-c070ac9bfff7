package com.sinoyd.lims.api.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;

@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectPhoneCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目类型ids
     */
    private String projectTypeIds;

    /**
     * 受检单位id
     */
    private String inspectedEntIds;

    /**
     * 关键字
     */
    private String key;

    /**
     * 项目等级
     */
    private Integer grade = EnumPRO.EnumProjectGrade.所有.getValue();

    /**
     * 状态
     */
    private Integer status = EnumPRO.EnumStatus.所有.getValue();

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 查询页面（1 ：数据查询 0：现场监测 2：应急监测）
     */
    private Integer astrict;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.id = pl.projectId ");

        if (!new Integer(1).equals(this.astrict)) {
            //非数据查询的时候需要排除不编制方案的项目
            condition.append(" and pl.isMakePlan = 1 ");
            List<String> statusList = new ArrayList<>();
            for (EnumPRO.EnumProjectStatus status : EnumPRO.EnumProjectStatus.values()) {
                if (status.getValue() >= EnumPRO.EnumProjectStatus.开展中.getValue()) {
                    statusList.add(status.name());
                }
            }
            if(new Integer(2).equals(this.astrict)){
                statusList.add(EnumPRO.EnumProjectStatus.项目登记中.name());
            }
            condition.append(" and p.status in :statusList");
            values.put("statusList", statusList);
        } else {
            if (StringUtil.isNotEmpty(this.projectStatus)) {
                List<String> psList = Arrays.asList(this.projectStatus.split(",").clone());
                condition.append(" and p.status in :projectStatus");
                values.put("projectStatus", psList);
            }
        }

        //单个项目类型
        if (StringUtil.isNotEmpty(this.projectTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.projectTypeId)) {
            condition.append(" and p.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        }
        //多个项目类型
        if (StringUtil.isNotEmpty(this.projectTypeIds)) {
            List<String> typeIds = Arrays.asList(this.projectTypeIds.split(",").clone());
            condition.append(" and p.projectTypeId in :projectTypeIds");
            values.put("projectTypeIds", typeIds);
        }
        //多个受检单位id
        if (StringUtil.isNotEmpty(this.inspectedEntIds)) {
            List<String> entIds = Arrays.asList(this.inspectedEntIds.split(",").clone());
            condition.append(" and p.inspectedEntId in :inspectedEntIds");
            values.put("inspectedEntIds", entIds);
        }
        //开始时间
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and p.inceptTime >= :startTime");
            values.put("startTime", from);
        }
        //结束时间
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and p.inceptTime < :endTime");
            values.put("endTime", to);
        }
        //关键字（项目名称、项目编号、委托方）
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (p.projectCode like :key or p.projectName like :key or p.customerName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        //项目等级
        if (!EnumPRO.EnumProjectGrade.所有.getValue().equals(this.grade)) {
            condition.append(" and p.grade = :grade");
            values.put("grade", this.grade);
        }
        //状态(-1:所有 1:未完成 2:已完成)
        if (!EnumPRO.EnumStatus.所有.getValue().equals(this.status)) {
            if (EnumPRO.EnumStatus.待处理.getValue().equals(this.status)) {
                condition.append(" and ( exists(select 1 from DtoSample as sam where sam.receiveId = '00000000-0000-0000-0000-000000000000' " +
                        "and sam.projectId = p.id and sam.isDeleted = 0 ) " +
                        "or exists(select 1 from DtoReceiveSampleRecord as rec where rec.projectId = p.id and rec.uploadStatus = 0))");
            } else if (EnumPRO.EnumStatus.已处理.getValue().equals(this.status)) {
                condition.append(" and not ( exists(select 1 from DtoSample as sam where sam.receiveId = '00000000-0000-0000-0000-000000000000' " +
                        "and  sam.projectId = p.id and sam.isDeleted = 0 ) " +
                        "or exists(select 1 from DtoReceiveSampleRecord as rec where rec.projectId = p.id and rec.uploadStatus = 0))" +
                        " and ( exists(select 1 from DtoSample as sam where sam.projectId = p.id and sam.isDeleted = 0))");
            }
        }
        return condition.toString();
    }
}
