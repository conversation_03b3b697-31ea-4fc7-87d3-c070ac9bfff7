package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * SamplingAchievementDetails查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamplingAchievementDetailsCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 绩效id
     */
    private String achievementId;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 样品编号，点位名称
     */
    private String key;


    private String projectKey;

    private String sampleTypeId;

    @Override
    public String getCondition() {
        values.clear();
        Calendar calendar = new GregorianCalendar();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.personId)) {
            condition.append(" and exists(select 1 from DtoSamplingAchievement2Person p where p.id = a.achievementId and p.personId = :personId)");
            values.put("personId", this.personId);
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.samplingTime >= :startTime");
            values.put("startTime", date);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.samplingTime < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (a.sampleCode like :key or a.sampleFolderName like :key)");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtil.isNotEmpty(this.achievementId)) {
            condition.append(" and a.achievementId = :achievementId");
            values.put("achievementId", this.achievementId);
        }
        if (StringUtil.isNotEmpty(this.projectKey)) {
            condition.append(" and exists (select 1 from DtoProject p,DtoSample s where s.id = a.sampleId and p.id = s.projectId and (p.projectName like :projectKey or p.projectCode like :projectKey or p.customerName like :projectKey))");
            values.put("projectKey", "%" + this.projectKey + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleTypeId)) {
            condition.append(" and a.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        return condition.toString();
    }
}
