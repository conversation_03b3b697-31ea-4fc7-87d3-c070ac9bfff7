package com.sinoyd.lims.pro.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 及时率统计VO
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/31
 */
@Data
@Accessors(chain = true)
public class AnalyzeTimelinessVO {

    /**
     * 分析人员id
     */
    private String analystId;

    /**
     * 分析人员名称
     */
    private String analystName;

    /**
     * 分析项目总数（对应UI上样品总数：样品总数只是客户叫法）
     */
    private Integer totalCount;

    /**
     * 即将超期数量
     */
    private Integer willOverdueCount;

    /**
     * 已超期数量
     */
    private Integer overdueCount;

    /**
     * 及时率
     */
    private String timelinessRate;
}