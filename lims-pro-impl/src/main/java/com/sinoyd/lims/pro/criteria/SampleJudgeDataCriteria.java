package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 比对数据查询条件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleJudgeDataCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    private String folderName;

    private String pass;

    private String folderPass;

    private String analyzeItemName;

    private Integer checkType;

    private List<String> sampleIds;

    private String receiveId;

    private String reportId;

    private String projectId;

    /**
     *  是否过滤不参与评价
     */
    private Boolean isFilterNotEvaluate;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotNull(this.checkType)) {
            condition.append(" and a.checkType = :checkType");
            values.put("checkType", this.checkType);
        }
        if (StringUtil.isNotEmpty(this.folderName)) {
            condition.append(" and exists (select 1 from DtoSampleFolder f, DtoSample s where f.id = s.sampleFolderId and a.sampleId = s.id and f.watchSpot like :folderName)");
            values.put("folderName", "%" + this.folderName + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeItemName)) {
            condition.append(" and exists (select 1 from DtoTest t where t.id = a.testId and t.redAnalyzeItemName like :analyzeItemName)");
            values.put("analyzeItemName", "%" + this.analyzeItemName + "%");
        }
        if (StringUtil.isNotEmpty(this.pass)) {
            condition.append(" and a.pass = :pass");
            values.put("pass", this.pass);
        }
        if (StringUtil.isNotEmpty(this.sampleIds)) {
            condition.append(" and a.sampleId in :sampleIds");
            values.put("sampleIds", this.sampleIds);
        }

        if (StringUtil.isNotNull(isFilterNotEvaluate) && isFilterNotEvaluate){
            condition.append(" and a.isNotEvaluate <> 1");
        }

        return condition.toString();
    }
}
