package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoHolidayConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoWorkdayConfig;
import com.sinoyd.lims.lim.repository.rcc.HolidayConfigRepository;
import com.sinoyd.lims.lim.service.CalendarDateService;
import com.sinoyd.lims.lim.service.HolidayConfigService;
import com.sinoyd.lims.lim.service.WorkdayConfigService;
import com.sinoyd.lims.lim.utils.CalendarUtil;
import com.sinoyd.lims.lim.vo.WorkHolidayConfigVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;

/**
 * 节假日管理配置实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/18
 */
@Service
public class HolidayConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoHolidayConfig, String, HolidayConfigRepository> implements HolidayConfigService {

    private WorkdayConfigService workdayConfigService;

    private CalendarDateService calendarDateService;


    @Override
    public List<DtoHolidayConfig> findByYear(Integer year) {
        return repository.findByYear(year);
    }


    @Override
    @Transactional
    public List<DtoHolidayConfig> save(WorkHolidayConfigVO vo) {
        DtoWorkdayConfig dtoWorkdayConfig = vo.getDtoWorkdayConfig();
        List<DtoHolidayConfig> dtoHolidayConfigList = vo.getDtoHolidayConfigList();
        if(StringUtil.isNotNull(dtoHolidayConfigList)) {
            //对休息日时间区间是否重叠进行判断
            validationHolidayConfig(dtoHolidayConfigList);
            //对节假日进行设置
            calendarDateService.refreshCalendarDate(dtoHolidayConfigList);
            //保存节假日配置
            if (StringUtil.isNotEmpty(dtoHolidayConfigList)) {
                dtoHolidayConfigList = super.save(dtoHolidayConfigList);
            }
        }

        //对工作日休息日进行设置
        if(StringUtil.isNotNull(dtoWorkdayConfig)) {
            calendarDateService.refreshCalendarDate(CalendarUtil.getFirstDateOfYear(vo.getYear()),
                    CalendarUtil.getLastDateOfYear(vo.getYear()),
                    dtoWorkdayConfig);
            workdayConfigService.save(dtoWorkdayConfig);
        }

        return dtoHolidayConfigList;
    }

    /**
     * 根据id删除节假日配置以及修改日历数据
     *
     * @param id  主键
     * @param <K> 主键泛型
     * @return 删除的条数
     */
    @Override
    @Transactional
    public <K extends Serializable> Integer logicDeleteById(K id) {
        DtoHolidayConfig dto = super.findOne(id.toString());
        DtoWorkdayConfig dtoWorkdayConfig = workdayConfigService.findByYear(dto.getYear());
        calendarDateService.refreshCalendarDate(dto.getBeginDate(), dto.getEndDate(), dtoWorkdayConfig);
        return super.logicDeleteById(id);
    }

    /**
     * 假节日保存时，进行校验:
     * 1、校验节假日时间区间是否存在重叠
     *
     * @param dtoHolidayConfigList 节假日配置集合
     */
    private void validationHolidayConfig(List<DtoHolidayConfig> dtoHolidayConfigList) {
        for (DtoHolidayConfig externalConfig : dtoHolidayConfigList) {
            for (DtoHolidayConfig internalConfig : dtoHolidayConfigList) {
                //排除掉自己和自己比较
                if (!externalConfig.equals(internalConfig)) {
                    if (!internalConfig.getBeginDate().after(externalConfig.getEndDate())
                            && !internalConfig.getEndDate().before(externalConfig.getBeginDate())) {
                        throw new BaseException("节假日[" + externalConfig.getHolidayName() + "、" + internalConfig.getHolidayName() + "]日期存在重叠");
                    }
                }
            }
        }
    }

    @Autowired
    @Lazy
    public void setWorkdayConfigService(WorkdayConfigService workdayConfigService) {
        this.workdayConfigService = workdayConfigService;
    }

    @Autowired
    @Lazy
    public void setCalendarDateService(CalendarDateService calendarDateService) {
        this.calendarDateService = calendarDateService;
    }

}