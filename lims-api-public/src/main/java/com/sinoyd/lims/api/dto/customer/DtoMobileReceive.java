package com.sinoyd.lims.api.dto.customer;

import lombok.Data;

import java.util.Date;

/**
 * 移动端送样单实体
 *
 * <AUTHOR>
 * @date V1.0.0 2024/02/28
 * @version: V100R001
 */
@Data
public class DtoMobileReceive {

    /**
     * 点位示意图id
     */
    private String id;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目类型id
     */
    private String projectTypeId;
    /**
     * 项目类型名称
     */
    private String projectTypeName;
    /**
     * 项目编号
     */
    private String projectCode;
    /**
     * 送样单号
     */
    private String recordCode;
    /**
     * 采样人员
     */
    private String samplingPerson;
    /**
     * 采样日期
     */
    private Date samplingTime;

    /**
     * 检测类型ids
     */
    private String sampleTypeIds;

    /**
     * 检测类型名称
     */
    private String sampleTypeNames;


    /**
     * 送样单列表构造
     */
    public DtoMobileReceive(String id, String projectName, String projectTypeId,
                             String projectCode, String recordCode, Date samplingTime, String sampleTypeIds) {
        this.id = id;
        this.projectName = projectName;
        this.projectTypeId = projectTypeId;
        this.projectCode = projectCode;
        this.recordCode = recordCode;
        this.samplingTime = samplingTime;
        this.sampleTypeIds = sampleTypeIds;
    }

}
