-- 查新计划
CREATE TABLE "TB_LIM_NEWSEARCHPLAN"
(
    "ID"            VARCHAR(50)                                                 NOT NULL,
    "PLANNAME"      VARCHAR(100)                                                NOT NULL,
    "PLANTYPE"      INT                                                         NOT NULL,
    "EXECUTOR"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "DEALCYCLE"     INT          DEFAULT 0                                      NOT NULL,
    "DEALDATE"      INT                                                         NOT NULL,
    "TASKSTARTDATE" TIMESTAMP(0)                                                NOT NULL,
    "TASKENDDATE"   TIMESTAMP(0)                                                NOT NULL,
    "STATUS"        INT          DEFAULT 0                                      NOT NULL,
    "REMARK"        VARCHAR(255),
    "ISDELETED"     BIT          DEFAULT 0                                      NOT NULL,
    "ORGID"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATOR"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATEDATE"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "DOMAINID"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFIER"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFYDATE"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT             CLUSTER PRIMARY KEY("ID")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_NEWSEARCHPLAN" IS '查新计划';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."CREATEDATE" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."CREATOR" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."DEALCYCLE" IS '执行周期EnumNewSearchPlanDealCycle';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."DEALDATE" IS '每月执行日期(日)';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."DOMAINID" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."EXECUTOR" IS '执行人';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."ID" IS 'id';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."ISDELETED" IS '是否删除';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."MODIFIER" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."MODIFYDATE" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."ORGID" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."PLANNAME" IS '计划名称';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."PLANTYPE" IS '计划类型';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."REMARK" IS '备注';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."STATUS" IS '计划状态 0:未提交 1:已提交';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."TASKENDDATE" IS '任务结束日期';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHPLAN"."TASKSTARTDATE" IS '任务开始日期';


-- 查新结果
CREATE TABLE "TB_LIM_NEWSEARCHRESULT"
(
    "ID"            VARCHAR(50)                                                 NOT NULL,
    "TASKID"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "ISNEWSTANDARD" BIT          DEFAULT 0                                      NOT NULL,
    "STANDARDNAME"  VARCHAR(100),
    "STANDARDNUM"   VARCHAR(50),
    "YEAR"          VARCHAR(50),
    "RELEASEDATE"   TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "EFFECTIVEDATE" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "NEWSEARCHDATE" TIMESTAMP(0) DEFAULT '1753-01-01 00:00:00'                  NOT NULL,
    "REPLACENUM"    VARCHAR(50),
    "STATUS"        INT                                                         NOT NULL,
    "ISCONFIRM"     BIT          DEFAULT 0,
    "CONFIRMATION"  BIT          DEFAULT 0,
    "ISPROPAGATE"   BIT          DEFAULT 0,
    "ORGID"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATOR"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATEDATE"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "DOMAINID"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFIER"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFYDATE"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT             CLUSTER PRIMARY KEY("ID")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_NEWSEARCHRESULT" IS '查新结果';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."CONFIRMATION" IS '确认情况';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."CREATEDATE" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."CREATOR" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."DOMAINID" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."EFFECTIVEDATE" IS '实施日期';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."ID" IS 'id';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."ISCONFIRM" IS '是否确认';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."ISNEWSTANDARD" IS '是否新标准';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."ISPROPAGATE" IS '是否宣贯';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."MODIFIER" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."MODIFYDATE" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."NEWSEARCHDATE" IS '查新日期';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."ORGID" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."RELEASEDATE" IS '发布日期';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."REPLACENUM" IS '替代标准号';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."STANDARDNAME" IS '标准名称';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."STANDARDNUM" IS '标准编号';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."STATUS" IS '状态 0:新建 1:待处理 2:已处理';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."TASKID" IS '查新任务id';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHRESULT"."YEAR" IS '年份';


-- 查新任务
CREATE TABLE "TB_LIM_NEWSEARCHTASK"
(
    "ID"         VARCHAR(50)                                                 NOT NULL,
    "TASKNAME"   VARCHAR(100)                                                NOT NULL,
    "TASKTYPE"   INT                                                         NOT NULL,
    "STATUS"     INT                                                         NOT NULL,
    "EXECUTOR"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "ORGID"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATOR"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "CREATEDATE" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    "DOMAINID"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFIER"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
    "MODIFYDATE" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()                    NOT NULL,
    NOT          CLUSTER PRIMARY KEY("ID")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "TB_LIM_NEWSEARCHTASK" IS '查新任务';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."CREATEDATE" IS '创建时间';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."CREATOR" IS '创建人';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."DOMAINID" IS '所属实验室';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."EXECUTOR" IS '执行人';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."ID" IS 'id';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."MODIFIER" IS '修改人';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."MODIFYDATE" IS '修改时间';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."ORGID" IS '组织机构id';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."STATUS" IS '任务状态 1:待处理 2:已处理';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."TASKNAME" IS '任务名称';
COMMENT
ON COLUMN "TB_LIM_NEWSEARCHTASK"."TASKTYPE" IS '任务类型';