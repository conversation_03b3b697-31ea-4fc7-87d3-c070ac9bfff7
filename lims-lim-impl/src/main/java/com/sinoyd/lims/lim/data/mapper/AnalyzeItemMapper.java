package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.lims.lim.dto.customer.DtoExportAnalyzeItem;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * 分析项目实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface AnalyzeItemMapper {


    /**
     * DtoAnalyzeItem 实例转换成DtoExportAnalyzeItem实例
     *
     * @param analyzeItem 分析项目实体
     * @return DtoExportAnalyzeItem 实例
     */
    @Mapping(source = "isDeleted", target = "isDeleted")
    @Mapping(source = "orderNum", target = "orderNum")
    DtoExportAnalyzeItem toExportAnalyzeItem(DtoAnalyzeItem analyzeItem);

    /**
     * DtoAnalyzeItem 实例集合转换成DtoExportAnalyzeItem 实例集合
     *
     * @param analyzeItemList 分析项目实例集合
     * @return DtoExportAnalyzeItem 实例集合
     */
    @InheritConfiguration(name = "toExportAnalyzeItem")
    List<DtoExportAnalyzeItem> toExportAnalyzeItemList(List<DtoAnalyzeItem> analyzeItemList);


    /**
     * DtoExportAnalyzeItem 实例转换成DtoAnalyzeItem 实例
     *
     * @param exportAnalyzeItem 分析项目实体
     * @return DtoExportAnalyzeItem 实例
     */
    @Mapping(source = "isDeleted", target = "isDeleted")
    @Mapping(source = "orderNum", target = "orderNum")
    DtoAnalyzeItem toDtoAnalyzeItem(DtoExportAnalyzeItem exportAnalyzeItem);

    /**
     * DtoExportAnalyzeItem 实例集合转换成DtoAnalyzeItem 实例集合
     *
     * @param exportAnalyzeItemList 分析项目导入导出实例集合
     * @return DtoAnalyzeItem 实例集合
     */
    @InheritConfiguration(name = "toDtoAnalyzeItem")
    List<DtoAnalyzeItem> toDtoAnalyzeItemList(List<DtoExportAnalyzeItem> exportAnalyzeItemList);
}
