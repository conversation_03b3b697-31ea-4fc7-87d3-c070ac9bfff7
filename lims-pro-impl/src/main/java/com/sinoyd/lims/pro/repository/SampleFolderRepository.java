package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * SampleFolder数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface SampleFolderRepository extends IBaseJpaRepository<DtoSampleFolder, String> {

    /**
     * 按项目id和点位名称查询点位
     *
     * @param projectId 项目id
     * @param watchSpot 点位名称
     * @return 返回对应项目id和点位名称的点位列表
     */
    List<DtoSampleFolder> findByProjectIdAndSampleTypeIdAndWatchSpot(String projectId, String sampleTypeId, String watchSpot);

    /**
     * 按项目id查询点位
     *
     * @param projectId 项目id
     * @return 返回对应项目id的点位
     */
    List<DtoSampleFolder> findByProjectId(String projectId);

    /**
     * 按项目id查询点位
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @return 返回对应项目id的点位
     */
    List<DtoSampleFolder> findByProjectIdAndSampleTypeId(String projectId, String sampleTypeId);


    /**
     * 按照检测类型查询项目下的点位数据
     *
     * @param projectIds   项目id集合
     * @param sampleTypeId 检测类型id
     * @return 点位数据
     */
    List<DtoSampleFolder> findByProjectIdInAndSampleTypeId(Collection<String> projectIds, String sampleTypeId);

    /**
     * 批量清除项目的点位
     *
     * @param projectId 项目id
     * @return 返回删除的条数
     */
    @Transactional
    @Modifying
    @Query("update DtoSampleFolder a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.projectId = :projectId")
    Integer deleteByProjectId(@Param("projectId") String projectId,
                              @Param("modifier") String modifier,
                              @Param("modifyDate") Date modifyDate);

    /**
     * 批量清除项目的点位
     *
     * @param projectIds 项目id集合
     * @return 返回删除的条数
     */
    @Transactional
    @Modifying
    @Query("update DtoSampleFolder a set a.isDeleted = true,a.modifier = :modifier,a.modifyDate= :modifyDate where a.projectId in :projectIds")
    Integer deleteByProjectIdIn(@Param("projectIds") List<String> projectIds,
                                @Param("modifier") String modifier,
                                @Param("modifyDate") Date modifyDate);

    /**
     * 通过项目projectId集合查询
     *
     * @param projectId
     * @return DtoSampleFolder集合
     */
    List<DtoSampleFolder> findByProjectId(List<String> projectId);

    /**
     * 通过环境质量点位或污染源点位id集合查询
     *
     * @param fixedPointIds 境质量点位或污染源点位id
     * @return DtoSampleFolder集合
     */
    List<DtoSampleFolder> findByFixedPointIdIn(List<String> fixedPointIds);
}