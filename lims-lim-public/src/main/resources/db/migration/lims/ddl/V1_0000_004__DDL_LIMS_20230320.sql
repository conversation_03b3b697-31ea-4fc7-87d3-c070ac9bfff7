-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关DDL脚本 -----------
-- ---------------- LIM模块 ---------------------
-- ----------------------------------------------

create table TB_LIM_AnalyzeItemSort
(
    id           varchar(50)                                                not null comment 'id'
        primary key,
    sortName     varchar(250)                                               null comment '排序名称',
    sampleTypeId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样品类型id（预留：样品类型小类）',
    orderNum     int         default 0                                      not null comment '排序值',
    sortCode     varchar(250)                                               null comment '编号（预留：可能会有其他标识时借用）',
    type         int         default 3                                      not null comment '类型（预留：枚举EnumAnalyzeItemSortType：1.只显示排序 2.只显示测试方案 3.全部显示）',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    remark       varchar(255)                                               null
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_AnalyzeItemSortDetail
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    sortId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目排序Id',
    analyzeItemId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析项目Id',
    analyzeItemName varchar(50) comment '分析项目名称',
    orderNum        int         default 0                                      not null comment '排序值',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_AnalyzeMethodReagentConfig
(
    id                    varchar(50)                                                not null comment 'id'
        primary key,
    context               varchar(1000)                                              null comment '需求的配置过程 ',
    analyzeMethodId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '分析方法id（Guid）',
    reagentName           varchar(100)                                               null comment '试剂名称',
    reagentSpecification  varchar(50)                                                null comment '试剂规格',
    configurationSolution varchar(255)                                               null comment '配置溶液',
    configDate            datetime    default CURRENT_TIMESTAMP                      not null comment '配置日期',
    expiryDate            datetime    default '1753-01-01 00:00:00'                  not null comment '有效期',
    orderNum              int         default 0                                      not null comment '排序值（预留）',
    course                varchar(1000)                                              null comment '稀释过程记录',
    opinion               varchar(1000)                                              null comment '其他情况',
    isDeleted             bit         default b'0'                                   not null comment '假删字段',
    orgId                 varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate            datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate            datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '试剂配置' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;


create table TB_LIM_CarConsumerRecord
(
    id         varchar(50)                                                not null comment 'id'
        primary key,
    carId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '车辆id（Guid）',
    type       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '记录类型（常量LIM_CarConsumerRecordType：维修、保养、ETC、年检、车险、GIS流量费、其他）',
    amount     decimal(18, 2)                                             not null comment '消费金额',
    salesManId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '消费人员id（Guid）',
    salseDate  datetime    default '1753-01-01 00:00:00'                  not null comment '消费日期',
    content    varchar(1000)                                              null comment '消费说明',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_CarGpsConfig
(
    id           varchar(50)                                                   not null comment 'id'
        primary key,
    carId        varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '车辆id（Guid）',
    gpsModel     varchar(50)                                                   not null comment 'gps型号',
    gpsCode      varchar(50)                                                   not null comment 'gps编号（MN号）',
    gpsPwd       varchar(50)                                                   null comment 'gps密码',
    simNumber    varchar(50)                                                   null comment 'SIM号码',
    rateLimited  varchar(50)                                                   null comment '限速',
    rangeConfig  decimal(18, 2) default 500.00                                 not null comment '采样地点范围(m)',
    gpsFrequency varchar(20)    default '5'                                    null comment 'gps的接受频率(min)',
    orgId        varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_CarGpsData
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    carId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '车辆id（Guid）',
    carSn       varchar(50)                                                null comment '车辆编码(用于GPS数据传输)',
    drivingLine mediumtext                                                 null comment '行驶轨迹',
    drivingTime decimal(8, 2)                                              not null comment '驾驶时长',
    mileage     decimal(8, 2)                                              not null comment '英里数',
    avgSpeed    decimal(8, 2)                                              not null comment '平均速度',
    maxSpeed    decimal(8, 2)                                              not null comment '最高时速',
    beginTime   datetime    default '1753-01-01 00:00:00'                  not null comment '开始时间',
    endTime     datetime    default '1753-01-01 00:00:00'                  not null comment '结束时间',
    fuelCon     decimal(8, 2)                                              not null comment '耗油量',
    userId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '使用人员id（Guid）',
    userName    varchar(50)                                                null comment '使用人员',
    isSkeptical int         default 0                                      not null comment '是否最高级',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_CarGpsRealData
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    carId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '车辆id（Guid）',
    carDate     datetime    default CURRENT_TIMESTAMP                      not null comment '车辆行驶时间',
    lat         varchar(50)                                                null comment '纬度',
    lon         varchar(50)                                                null comment '经度',
    speed       varchar(50)                                                null comment '速度',
    `precision` varchar(50)                                                null comment 'GPS精度',
    direction   varchar(50)                                                null comment '运行方向',
    sigWatch    varchar(50)                                                null comment '网络强度',
    operator    varchar(50)                                                null comment '运营商',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_CarManage
(
    id                varchar(50)                                                not null comment 'id'
        primary key,
    carCode           varchar(50)                                                not null comment '车牌号码',
    carModel          varchar(50)                                                null comment '车辆型号',
    carType           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '车辆类型（常量LIM_CarType:轿车、货车、商务车、SUV）',
    state             int         default 1                                      not null comment '车辆状态(枚举EnumCarState:1:正常 2:维修,3:停用;4:过期)',
    managerId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '负责人Id（Guid）',
    buyDate           datetime    default '1753-01-01 00:00:00'                  not null comment '购置日期',
    engineCode        varchar(50)                                                null comment '发动机号码',
    annualReviewDate  datetime    default '1753-01-01 00:00:00'                  not null comment '最近年检日期',
    annualReviewCycle int         default 12                                     not null comment '年检周期（月）',
    oilConsumption    varchar(50)                                                null comment '油耗',
    remark            varchar(1000)                                              null comment '备注',
    carBrand          varchar(50)                                                null comment '商标',
    carColor          varchar(50)                                                null comment '车辆颜色',
    useState          int         default 0                                      not null comment '使用状态（枚举EnumCarUseState 0：空闲，1：使用中）',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_ConsumableStorage
(
    id                varchar(50)                                                not null comment 'id'
        primary key,
    purchaseDetailId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '消耗品采购明细标识（Guid）',
    consumableId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '消耗品id（Guid）',
    dimensionId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '单位Id（冗余）（Guid）',
    dimensionName     varchar(100)                                               null comment '单位名称（冗余）',
    productionCode    varchar(20)                                                null comment '生产批号',
    checkerId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '验收人Id（Guid）',
    checkerName       varchar(50)                                                null comment '验收人名字',
    checkerDate       datetime    default '1753-01-01 00:00:00'                  not null comment '验收日期',
    checkerResult     int         default 1                                      not null comment '验收结论(枚举EnumCheckerResult：1:合格，0：不合格)',
    manufacturerName  varchar(100)                                               null comment '生产厂商名字',
    expiryDate        datetime    default '1753-01-01 00:00:00'                  not null comment '有效日期',
    storageNum        decimal(18, 4)                                             not null comment '入库数量',
    storageTime       datetime    default '1753-01-01 00:00:00'                  not null comment '入库时间',
    balance           decimal(18, 2)                                             not null comment '入库结存',
    unitPrice         decimal(18, 2)                                             not null comment '单价',
    totalPrice        decimal(18, 2)                                             not null comment '总价',
    supplyCompanyId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '供应商Id（Guid）',
    supplyCompanyName varchar(100)                                               null comment '供应商名称',
    operatorId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '入库人Id（Guid）',
    operatorName      varchar(50)                                                null comment '入库人名称',
    appearance        varchar(100)                                               null comment '外观、状态',
    checkItem         varchar(100)                                               null comment '检验/验证项目',
    buyReason         varchar(1000)                                              null comment '购买原因',
    keepPlace         varchar(100)                                               null comment '存放位置',
    remark            varchar(1000)                                              null comment '备注',
    isDeleted         bit         default b'0'                                   not null comment '假删',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '消耗品入库记录' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;



create table TB_LIM_Contract
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    contractName     varchar(255)                                               not null comment '合同名称',
    contractCode     varchar(50)                                                null comment '合同编号',
    type             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '合同类型（常量：收款合同、委外合同    编码:LIM_ContractType）',
    `period`         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '合同周期（常量：长期检测合同、单次检测合同    编码：LIM_ContractPeriod）',
    totalAmount      decimal(18, 2)                                             not null comment '总金额',
    arrivalAmount    decimal(18, 2)                                             not null comment '已收/付款',
    lessAmount       decimal(18, 2)                                             not null comment '剩余额',
    badAmount        decimal(18, 2)                                             not null comment '坏账',
    salesManId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '业务员id',
    salesManName     varchar(50)                                                null comment '业务员',
    signDate         datetime    default '1753-01-01 00:00:00'                  not null comment '签订日期',
    timeBegin        datetime    default '1753-01-01 00:00:00'                  not null comment '开始日期',
    timeEnd          datetime    default '1753-01-01 00:00:00'                  not null comment '结束日期',
    status           int         default 0                                      not null comment '状态（枚举EnumContractStatus： 0未签  1已签）',
    collectionStatus int         default 0                                      not null comment '收/付款状态(枚举EnumContractCollectionStatus：0:未收/付款、1:部分收/付款、2:已收/付款)',
    remindDays       int         default 0                                      not null comment '提醒天数',
    entId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '客户/外包单位',
    entName          varchar(50)                                                null comment '单位名称',
    linkMan          varchar(50)                                                null comment '联系人',
    linkPhone        varchar(50)                                                null comment '联系方式',
    address          varchar(100)                                               null comment '地址',
    explains         varchar(255)                                               null comment '工期要求说明',
    attentions       varchar(255)                                               null comment '注意事项',
    remark           varchar(1000)                                              null comment '备注',
    isDeleted        bit         default b'0'                                   not null comment '假删',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_ContractCollectionPlan
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    contractId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '合同id',
    collectDate datetime    default '1753-01-01 00:00:00'                  not null comment '计划收款日期',
    amount      decimal(18, 2)                                             not null comment '计划收款金额',
    status      int         default 0                                      not null comment '收款状态(枚举EnumContractCollectionStatus：0:未收款、1:部分收款、2:已收款)',
    remark      varchar(1000)                                              null comment '备注',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_Curve
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    testId           varchar(50)                                                not null comment '测试项目id',
    configPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '配置人员id',
    configName       varchar(50)                                                null comment '配置人员姓名',
    kRange           varchar(50)                                                null comment '斜率范围',
    bRange           varchar(50)                                                null comment '截距范围',
    coefficientRange varchar(50)                                                null comment '相关系数范围',
    coefficient      varchar(50)                                                not null comment '相关系数',
    configDate       datetime    default CURRENT_TIMESTAMP                      not null comment '配置日期',
    `period`         int         default 30                                     not null comment '周期（天）',
    zeroPoint        varchar(50)                                                null comment '曲线零点',
    kValue           varchar(50)                                                null comment '斜率a',
    bValue           varchar(50)                                                null comment '截距b',
    cValue           varchar(50)                                                null comment '实数c',
    isDouble         bit         default b'0'                                   not null comment '是否双曲线',
    curveType        int         default 0                                      not null comment '曲线类型（枚举(EnumCurveType:0直线型 1Log型  2二次)）',
    curveMode        int         default 0                                      not null comment '曲线模型（枚举（EnumCurveModel：0普通 1紫外 2荧光 3石墨  4离子电极））',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    forcedZero       bit         default b'0'                                   not null comment '强制零点',
    doubleName       varchar(50)                                                null comment '双曲线名称'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_CurveDetail
(
    id                  varchar(50)                                                not null comment 'id'
        primary key,
    curveId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '标准曲线标识',
    vValue              varchar(50)                                                null comment '标准溶液加入体积',
    hValue              varchar(50)                                                null comment '含量值',
    xValue              varchar(50)                                                null comment 'X值',
    yValue              varchar(50)                                                null comment 'Y值',
    orderNum            int         default 0                                      not null comment '排序',
    remark              varchar(1000)                                              null comment '备注',
    blank220            varchar(50)                                                null comment '220吸光度',
    blank275            varchar(50)                                                null comment '275吸光度',
    lessBlank           varchar(50)                                                null comment '减空白吸光度',
    aValueBG            varchar(50)                                                null comment '背景吸光度',
    orgId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    colorAbsorbance     varchar(50)                                                null comment '显色吸光度A1',
    interfereAbsorbance varchar(50)                                                null comment '干扰吸光度A2',
    blankAbsorbance     varchar(50)                                                null comment '空白吸光度A0'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_CustomerViolate
(
    id                 varchar(50)                                                not null comment '主键id' primary key,
    enterpriseId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '企业id',
    registerPersonId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '登记人员id',
    registerTime       datetime    default '1753-01-01 00:00:00'                  not null comment '登记时间',
    happenedTime       datetime    default '1753-01-01 00:00:00'                  not null comment '发生时间',
    hasHandle          bit         default b'0'                                   not null comment '是否已处理',
    requiredHandleTime datetime    default '1753-01-01 00:00:00'                  not null comment '要求处理时间',
    handleTime         datetime    default '1753-01-01 00:00:00'                  not null comment '处理时间',
    violateContent     varchar(2000)                                              null comment '违约内容',
    handlePersonId     varchar(50) default '00000000-0000-0000-0000-000000000000' null comment '处理人',
    handleWay          varchar(2000)                                              null comment '处理措施',
    orgId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '客户违约信息' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;


create table TB_LIM_DocAuthority
(
    id        varchar(50)                                                not null comment 'id'
        primary key,
    objectId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文件夹，文件Id（Guid）',
    authId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '权限Id（常量Guid，常量名称 LIM_AuthType）',
    authName  varchar(250)                                               null comment '权限名称',
    roleId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '角色Id（Guid）',
    roleName  varchar(250)                                               null comment '角色名称',
    authState bit         default b'0'                                   not null comment '权限状态',
    orgId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_EntEvaluation
(
    id            varchar(50)                                                not null comment 'id'
        primary key,
    entId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '供应商id（Guid）',
    content       varchar(1000)                                              null comment '评价内容',
    startPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '评价人员id（Guid）',
    auditPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '审核人员id（Guid）',
    auditOpinion  varchar(1000)                                              null comment '审核意见',
    status        int         default 0                                      not null comment '评价状态（枚举EnumEvaluateStatus：0未评价 1已评价）',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_EntSupplierGoodsEvaluation
(
    id                   varchar(50)                                                   not null comment 'id'
        primary key,
    entId                varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '企业Id（Guid）',
    goodsId              varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '供应品Id（Guid）',
    evaluationId         varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '评价Id（Guid）',
    shortestDeliveryTime varchar(10)                                                   null comment '最短供货时间',
    lowestPrice          decimal(18, 2) default -1.00                                  not null comment '最低报价',
    comment              varchar(1000)                                                 null comment '评价',
    trialResult          varchar(1000)                                                 null comment '试用结果',
    trialComment         varchar(1000)                                                 null comment '试用评价',
    trialPerson          varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '试用人Id（Guid）',
    trialBeginDate       datetime       default '1753-01-01 00:00:00'                  not null comment '试用开始日期',
    trialTimeLen         varchar(255)                                                  null comment '试用时长',
    auditChecker         varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '审核人Id（Guid）',
    auditData            datetime       default '1753-01-01 00:00:00'                  not null comment '审核日期',
    serviceContent       varchar(1000)                                                 null comment '主营产品',
    info                 varchar(1000)                                                 null comment '简介',
    remark               varchar(1000)                                                 null comment '备注',
    orgId                varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator              varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate           datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId             varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier             varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate           datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_EntSupplierService
(
    id         varchar(50)                                                not null comment 'id'
        primary key,
    entId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '供应商id（Guid）',
    goodsName  varchar(100)                                               not null comment '商品名称',
    goodsCode  varchar(20)                                                null comment '商品编码',
    goodsModel varchar(50)                                                null comment '商品规格',
    goodsType  varchar(100)                                               null comment '商品类型',
    remark     varchar(1000)                                              null comment '备注',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_Environmental
(
    id                 varchar(50)                                                not null comment 'id'
        primary key,
    labName            varchar(50)                                                not null comment '实验室名称',
    labCode            varchar(50)                                                null comment '实验室编号',
    lowestTemperature  varchar(50)                                                null comment '最低温度',
    highestTemperature varchar(50)                                                null comment '最高温度',
    lowestHumidity     varchar(50)                                                null comment '最低湿度',
    highestHumidity    varchar(50)                                                null comment '最高湿度',
    lowestPressure     varchar(50)                                                null comment '最低气压',
    highestPressure    varchar(50)                                                null comment '最高气压',
    personInChargeId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '负责人id',
    personInCharge     varchar(50)                                                null comment '负责人',
    isDeleted          bit         default b'0'                                   not null comment '假删',
    orgId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_EnvironmentalLog
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    environmentalId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '实验室id',
    temperature     varchar(50)                                                null comment '温度',
    humidity        varchar(50)                                                null comment '湿度',
    pressure        varchar(50)                                                null comment '大气压',
    updateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '更新时间',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '温湿度仪' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;


create table TB_LIM_EnvironmentalRecord
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    objectId        varchar(50)                                                not null comment '对象id（送样单-采样、领样单-现场分析、工作单-实验室）',
    objectType      int         default -1                                     not null comment '对象类型（枚举EnumEnvRecObjType1：采样，2：实验室分析，4：现场分析）',
    environmentalId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '实验室id',
    temperature     varchar(50)                                                null comment '温度',
    humidity        varchar(50)                                                null comment '湿度',
    pressure        varchar(50)                                                null comment '大气压',
    usePersonId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '使用人id',
    startTime       datetime    default '1753-01-01 00:00:00'                  not null comment '开始使用时间',
    endTime         datetime    default '1753-01-01 00:00:00'                  not null comment '结束使用时间',
    useTime         decimal(8, 1)                                              null comment '使用时长',
    remark          varchar(1000)                                              null comment '备注',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_FeeConfig
(
    id         varchar(50)                                                not null comment 'id' primary key,
    typeName   varchar(50)                                                null comment '费用类型名称',
    standard   decimal(18, 2)                                             not null comment '收费标准',
    unit       varchar(50)                                                null comment '单位',
    days       decimal(18, 2)                                             not null comment '天数',
    count      decimal(18, 2)                                             not null comment '数量',
    remark     varchar(255)                                               null comment '备注',
    isDeleted  bit         default b'0'                                   not null comment '是否删除',
    orgId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    externalId varchar(50)                                                null comment '关联系统产品编号'
) comment '费用类型' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;



create table TB_LIM_FileControlApply
(
    id            varchar(50)                                                not null comment 'id'
        primary key,
    controlCode   varchar(100)                                               null comment '受控申请编号，修订申请编号，废止申请编号',
    applyPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '申请人id（Guid）',
    applyDesc     varchar(1000)                                              null comment '申请描述',
    applyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '申请日期',
    status        varchar(50)                                                null comment '状态',
    controlDate   datetime    default '1753-01-01 00:00:00'                  not null comment '受控日期',
    reviseDate    datetime    default '1753-01-01 00:00:00'                  not null comment '修订日期',
    reviseContent varchar(1000)                                              null comment '修订内容',
    abolishReason varchar(1000)                                              null comment '废止的原因',
    abolishDate   datetime    default '1753-01-01 00:00:00'                  not null comment '废止的日期',
    applyType     int         default 1                                      not null comment '申请类型（枚举EnumFileControlApplyType：1：受控申请，2：修订申请，3：废止申请）',
    isDeleted     bit         default b'0'                                   not null comment '假删',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '文件控制申请信息' ENGINE = InnoDB
                             CHARACTER SET = utf8
                             COLLATE = utf8_general_ci
                             ROW_FORMAT = Dynamic;


create table TB_LIM_FileControlApplyDetail
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    parentId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '父级id(最新的处理文件信息的父级id为Int，之前的文件信息的父级id是最新的文件信息的id)',
    fileApplyId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '申请单id（最新的文件记录，申请单id为Id）',
    grantId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '发放回收id（只有发放回收的时候id才有数据，如果发放回收记录删除，id要清空）',
    controlCode varchar(100)                                               null comment '受控编号',
    fileName    varchar(100)                                               null comment '文件名称',
    fileCode    varchar(100)                                               null comment '文件编号',
    fileType    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）',
    maker       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '编制人id',
    compileTime datetime    default '1753-01-01 00:00:00'                  not null comment '编制时间',
    status      int         default 1                                      not null comment '状态（枚举EnumFileControlApplyDetailStatus：1：未受控、2：受控申请中、3：修订中、4：废止中、5：已受控、6：已废止）',
    controlDate datetime    default '1753-01-01 00:00:00'                  not null comment '受控日期',
    abolishDate datetime    default '1753-01-01 00:00:00'                  not null,
    version     varchar(50)                                                null comment '版本号',
    isDeleted   bit         default b'0'                                   not null comment '假删',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '文件控制管理明细' ENGINE = InnoDB
                             CHARACTER SET = utf8
                             COLLATE = utf8_general_ci
                             ROW_FORMAT = Dynamic;

create table TB_LIM_FileGrantRecovery
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    code             varchar(255)                                               null comment '发放编号',
    materialPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '领用人id',
    recoveryPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '回收人id',
    grantTime        datetime    default '1753-01-01 00:00:00'                  not null comment '发放时间',
    recoveryTime     datetime    default '1753-01-01 00:00:00'                  not null comment '回收日期',
    remark           varchar(1000)                                              null comment '备注',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '文件发放与回收' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;


create table TB_LIM_InstrumentCheckRecord
(
    id            varchar(50)                                                   not null comment 'id'
        primary key,
    instrumentId  varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '仪器Id',
    originType    int            default -1                                     not null comment '溯源方式(枚举：EnumOriginType:1检定、2校准、3自校)',
    checkDeptName varchar(100)                                                  null comment '检定/校准单位名称',
    checkPersonId varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '检定/校准人员id（Guid）',
    checkPerson   varchar(50)                                                   null comment '检定/校准人员',
    checkTime     datetime       default '1753-01-01 00:00:00'                  not null on update CURRENT_TIMESTAMP comment '检定/校准日期',
    checkResult   int            default 1                                      not null comment '检定/校准结果(枚举：EnumOriginResult：1：合格:0：不合格)',
    certiCode     varchar(100)                                                  null,
    usability     bit            default b'1'                                   not null comment '适用性(是、否)',
    originCyc     decimal(18, 1) default 12.0                                   not null comment '溯源周期',
    checkContent  varchar(1000)                                                 null comment '校准项',
    calibration   varchar(1000)                                                 null comment '校准值（修正值）',
    indicate      varchar(1000)                                                 null comment '指示值',
    deviation     varchar(1000)                                                 null comment '误差（ABS(指示值-校准值)/校准值）',
    remark        mediumtext                                                    null comment '备注',
    checkEndDate  datetime       default '1753-01-01 00:00:00'                  not null on update CURRENT_TIMESTAMP comment '检定/校准有效期',
    cost          decimal(18, 2)                                                not null comment '费用',
    orgId         varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间',
    checkWay      varchar(50)                                                   null
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_InstrumentInspect
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    instrumentId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器Id',
    inspectPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '期间核查人员id',
    inspectPerson   varchar(50)                                                null comment '期间核查人员',
    inspectTime     datetime    default '1753-01-01 00:00:00'                  not null comment '期间核查时间',
    inspectContent  varchar(1000)                                              null comment '期间核查内容',
    inspectResult   int         default 1                                      not null comment '期间核查结果(枚举：EnumInspectResult：1合格、0不合格)',
    remark          varchar(1000)                                              null comment '备注',
    cost            decimal(18, 2)                                             not null comment '费用',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_InstrumentMaintainRecord
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    instrumentId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '设备Id',
    maintainDeptName varchar(100)                                               null comment '维护单位',
    startTime        datetime    default '1753-01-01 00:00:00'                  not null comment '开始维护时间',
    endTime          datetime    default '1753-01-01 00:00:00'                  not null comment '结束维护时间',
    mainTainPersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '维护人员id',
    maintainPerson   varchar(50)                                                null comment '维护人员',
    maintainContent  varchar(1000)                                              null comment '维护内容',
    maintainRule     varchar(1000)                                              null comment '维护规则',
    maintainremark   varchar(1000)                                              null comment '备注',
    cost             decimal(18, 2)                                             not null comment '费用',
    maintainTypeId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '维护类型（3.2保留,及时未启用）',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_InstrumentRepairRecord
(
    id                      varchar(50)                                                not null comment 'id'
        primary key,
    instrumentId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器Id',
    purchaseApplyID         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '维修申请ID',
    requestNoteCode         varchar(20)                                                null comment '维修申请单号',
    repairResult            int         default 1                                      not null comment '维修结果(枚举：EnumRepairRusult：1合格、0不合格)',
    failureDesc             varchar(1000)                                              null comment '设备故障描述',
    failureDescPersonId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '描述人id',
    failureDescPerson       varchar(50)                                                null comment '描述人',
    failureDescDate         datetime    default '1753-01-01 00:00:00'                  not null comment '描述日期',
    repairResultCheckerId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '验证人id',
    repairResultChecker     varchar(50)                                                null comment '验证人',
    repairResultCheckDate   datetime    default '1753-01-01 00:00:00'                  not null comment '验证日期',
    repairContentRecorderId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '维修记录人id',
    repairContentRecorder   varchar(50)                                                null comment '维修记录人',
    repairContentRecordDate datetime    default '1753-01-01 00:00:00'                  not null comment '维修记录日期',
    cost                    decimal(18, 2)                                             not null comment '费用',
    repairContent           varchar(1000)                                              null comment '维修内容',
    checkContent            varchar(1000)                                              null comment '验收情况',
    remark                  varchar(1000)                                              null comment '备注',
    orgId                   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator                 varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate              datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId                varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier                varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate              datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_InstrumentStorage
(
    id                varchar(50)                                                not null comment 'id'
        primary key,
    purchaseDetailId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器采购明细标识（Guid）',
    instrumentId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器Id',
    instrumentsCode   varchar(20)                                                null comment '本站编号',
    instrumentTypeId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器类型（常量：LIM_InstrumentType）',
    instrumentName    varchar(255)                                               null comment '设备名称',
    model             varchar(255)                                               null comment '规格型号',
    operator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '入库人id（Guid）',
    operatorName      varchar(50)                                                null comment '入库人姓名',
    storagNum         int         default 0                                      not null comment '入库数量',
    storagTtime       datetime    default '1753-01-01 00:00:00'                  not null comment '入库时间',
    balance           decimal(18, 2)                                             not null comment '入库结存',
    unitPrice         decimal(18, 2)                                             not null comment '单价',
    totalPrice        decimal(18, 2)                                             not null comment '总价',
    supplyCompanyId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '供应商Id',
    supplyCompanyName varchar(100)                                               null comment '供应商名称',
    serialNo          varchar(20)                                                null comment '出厂编号',
    factoryName       varchar(100)                                               null comment '制造厂商名称',
    isDeleted         bit         default b'0'                                   not null comment '假删',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '仪器入库记录' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;


create table TB_LIM_InstrumentUseRecord
(
    id                    varchar(50)                                                not null comment 'id'
        primary key,
    instrumentId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器id',
    objectId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '表单id（送样单-采样、领样单-现场分析、工作单-实验室）',
    environmentalManageId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '环境记录id',
    objectType            int         default -1                                     not null comment '使用对象类型（枚举EnumInsUseObjType：1采样，2：实验室分析，4：现场分析）',
    usePersonId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '使用人id',
    startTime             datetime    default '1753-01-01 00:00:00'                  not null comment '开始使用时间',
    endTime               datetime    default '1753-01-01 00:00:00'                  not null comment '结束使用时间',
    testIds               varchar(1000)                                              null comment '测试项目id支持多条',
    temperature           varchar(50)                                                null comment '温度',
    humidity              varchar(50)                                                null comment '湿度',
    pressure              varchar(50)                                                null comment '大气压',
    beforeUseSituation    varchar(255)                                               null comment '使用前情况',
    beforeAfterSituation  varchar(255)                                               null comment '使用后情况',
    isAssistInstrument    bit         default b'0'                                   not null comment '是否辅助仪器',
    remark                varchar(1000)                                              null comment '备注',
    insOriginDate         datetime    default '1753-01-01 00:00:00'                  not null comment '仪器的有效期',
    orgId                 varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate            datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate            datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_InstrumentUseRecord2Sample
(
    id                    varchar(50) not null comment 'id'
        primary key,
    instrumentUseRecordId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '仪器使用信息id',
    sampleId              varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '样品id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_MessageSendRecord
(
    id             varchar(50)                                                not null comment '主键id' primary key,
    jobId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '任务id',
    messageType    varchar(100)                                               not null comment '消息类型',
    messageContent mediumtext                                                 null comment '消息内容',
    receiver       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '接收人id',
    sendTime       datetime                                                   null comment '发送时间',
    status         int         default 0                                      not null comment '状态 (0：未读，1：已读)',
    isConcern      bit         default b'0'                                   not null comment '是否重点关注',
    sendType       int         default 1                                      not null comment '1：平台  2：短信  4：APP  8：微信  16：钉钉',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室'
) comment '消息发送记录' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;


create table TB_LIM_Notice
(
    id          varchar(50)                                                  not null comment 'id'
        primary key,
    title       varchar(255)                                                 not null comment '公告标题',
    category    varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '公告类型（常量）：LIM_NoticeCategory(1通知、2行政、4标准规范、8内部管理、16其他)',
    content     mediumtext                                                   null comment '公告内容',
    isRelease   bit           default b'0'                                   not null comment '是否发布（0.不发布 1.发布）',
    releaseId   varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '发布人id',
    releaseMan  varchar(255)                                                 null comment '发布人',
    releaseTime datetime      default CURRENT_TIMESTAMP                      not null comment '发布时间',
    isTop       bit           default b'0'                                   not null comment '是否置顶(0.不置顶 1.置顶)',
    label       varchar(1000) default '00000000-0000-0000-0000-000000000000' null comment '公告标签（常量）：LIM_NoticeLabel（一般、紧急、重要）',
    status      varchar(50)                                                  null comment '状态',
    orderNum    int           default 0                                      not null comment '排序值',
    clickNumber int           default 0                                      not null comment '浏览次数',
    orgId       varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime      default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime      default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_NoticeMsg
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    noticeId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '公告id',
    content         mediumtext                                                 null comment '内容',
    messagePersonId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '留言人id',
    messagePerson   varchar(50)                                                null comment '留言人',
    msgTime         datetime    default CURRENT_TIMESTAMP                      not null comment '留言时间',
    status          int         default 1                                      not null comment '状态（枚举EnumNoticeMsgStatus：1.正常 2.置顶 3.精华 4.假删）：1正常 2置顶 3精华 4假删  等情况预留用）',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate      datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate      datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_OAConsumablePickListsDetail
(
    id             varchar(50)                                                not null comment 'Id'
        primary key,
    consumableId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '消耗品Id',
    consumableName varchar(255)                                               null comment '名称',
    specification  varchar(255)                                               null comment '规格',
    materialNum    decimal(18, 2)                                             not null comment '领用数量',
    materialUnit   varchar(100)                                               null comment '单位',
    materialUse    varchar(100)                                               null comment '用途',
    remark         varchar(500)                                               null comment '备注',
    gradeName      varchar(100)                                               null comment '等级',
    codeInStation  varchar(100)                                               null comment '标准编号',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'

) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_OAConsumablePurchaseDetail
(
    id                varchar(50)                                                not null comment 'id'
        primary key,
    consumableId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '消耗品标识（Guid）',
    consumableName    varchar(50)                                                not null comment '消耗品名称',
    codeInStation     varchar(50)                                                null comment '编号（本站编号）',
    consumableCode    varchar(20)                                                null comment '标样编号',
    materialType      int         default 1                                      not null comment '物资种类（枚举EnumMaterialType：1.消耗品 2.标样）',
    materialModel     varchar(50)                                                null comment '规格型号',
    gradeId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '等级常量（Guid）（LIM_ConsumableGrade:进口、分析纯、FMP、高纯等）',
    dimensionId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '计量单位Id（Guid）',
    dimensionName     varchar(50)                                                null comment '计量单位名称',
    categoryId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '类别常量（Guid）（LIM_ConsumableCategory:高压气体、易制毒品、化学试剂等）',
    planNum           int         default 0                                      not null comment '申请计划数量',
    surplusNum        int         default 0                                      not null comment '剩余未入库数量',
    supplyTime        datetime    default '1753-01-01 00:00:00'                  not null comment '供应时间',
    purpose           varchar(100)                                               null comment '用途',
    skillRequire      varchar(255)                                               null comment '技术要求',
    isPoison          bit         default b'0'                                   not null comment '是否易制毒',
    keepCondition     varchar(1000)                                              null comment '保存条件',
    safetyInstruction varchar(1000)                                              null comment '安全须知',
    dilutedSolution   varchar(255)                                               null comment '稀释液',
    dilutionMethod    varchar(255)                                               null comment '稀释方法',
    concentration     varchar(255)                                               null comment '浓度',
    uncertainty       varchar(255)                                               null comment '不确定度',
    isMixedStandard   bit         default b'0'                                   not null comment '是否混标（0代表否  1代表是）',
    remark            varchar(1000)                                              null comment '备注',
    isDeleted         bit         default b'0'                                   not null comment '假删',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    brand             varchar(255)                                               null comment '品牌',
    unitPrice         decimal(10, 2)                                             null comment '单价',
    budgetAmount      decimal(10, 2)                                             null comment '预算总额',
    articleNo         varchar(255)                                               null comment '货号',
    userId            varchar(50)                                                null comment '使用人id'
) comment '消耗品采购明细' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;


create table TB_LIM_OAContract
(
    id           varchar(50)                                                not null comment '主键id' primary key,
    contractId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null,
    contractName varchar(255)                                               not null comment '合同名称',
    contractCode varchar(50)                                                null comment '合同编号',
    type         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '合同类型（常量：收款合同、委外合同    编码:LIM_ContractType）',
    `period`     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '合同周期（常量：长期检测合同、单次检测合同    编码：LIM_ContractPeriod）',
    totalAmount  decimal(18, 2)                                             not null comment '总金额',
    salesManId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '业务员id',
    salesManName varchar(50)                                                null comment '业务员',
    signDate     datetime    default '1753-01-01 00:00:00'                  not null comment '签订日期',
    timeBegin    datetime    default '1753-01-01 00:00:00'                  not null comment '开始日期',
    timeEnd      datetime    default '1753-01-01 00:00:00'                  not null comment '结束日期',
    entName      varchar(50)                                                null comment '单位名称',
    linkMan      varchar(50)                                                null comment '联系人',
    linkPhone    varchar(50)                                                null comment '联系方式',
    address      varchar(100)                                               null comment '地址',
    explains     varchar(255)                                               null comment '工期要求说明',
    attentions   varchar(255)                                               null comment '注意事项',
    remark       varchar(1000)                                              null comment '备注',
    isDeleted    bit         default b'0'                                   not null comment '假删',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment 'oa的合同管理信息' ENGINE = InnoDB
                             CHARACTER SET = utf8
                             COLLATE = utf8_general_ci
                             ROW_FORMAT = Dynamic;


create table TB_LIM_OAFileAbolish
(
    id            varchar(50)                                                not null comment 'id'
        primary key,
    fileName      varchar(100)                                               not null comment '文件名称',
    fileId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文件标识关联',
    abolishReason varchar(1000)                                              null comment '废止原因',
    abolishDate   datetime    default '1753-01-01 00:00:00'                  not null comment '废止日期',
    isDeleted     bit         default b'0'                                   not null comment '假删',
    orgId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '文件废止' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;

create table TB_LIM_OAFileControl
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    fileName    varchar(100)                                               not null comment '文件名称',
    fileId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文件标识关联',
    controlCode varchar(100)                                               null comment '受控编号',
    fileCode    varchar(100)                                               null comment '文件编号',
    version     varchar(50)                                                null comment '版本号',
    fileTypeId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）',
    maker       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '编制人id',
    makerName   varchar(50)                                                null comment '编制人名称',
    compileTime datetime    default '1753-01-01 00:00:00'                  not null comment '编制时间',
    isDeleted   bit         default b'0'                                   not null comment '假删',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '文件受控' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;


create table TB_LIM_OAFileRevision
(
    id                varchar(50)                                                not null comment 'id'
        primary key,
    fileName          varchar(100)                                               not null comment '文件名称',
    fileId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文件标识关联',
    fileCode          varchar(100)                                               null comment '文件编号',
    fileTypeId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '文件类型（常量LIM_FileControlType：程序文件、标准文件、质量手册、作业指导书、记录单）',
    makerId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '编制人id',
    makerName         varchar(50)                                                null comment '编制人名称',
    compileTime       datetime    default '1753-01-01 00:00:00'                  not null comment '编制时间',
    sourceControlCode varchar(100)                                               null comment '原受控编号',
    sourceVersion     varchar(50)                                                not null comment '原版本号',
    controlCode       varchar(100)                                               null comment '受控编号',
    version           varchar(50)                                                not null comment '版本号',
    reviseDate        datetime    default '1753-01-01 00:00:00'                  not null comment '修订日期',
    reviseContent     varchar(1000)                                              null comment '修订内容',
    isDeleted         bit         default b'0'                                   not null comment '假删',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate        datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '文件修订' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;


create table TB_LIM_OAInstrumentPurchaseDetail
(
    id             varchar(50)                                                not null comment 'id'
        primary key,
    instrumentId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器标识（Guid）',
    instrumentName varchar(50)                                                not null comment '仪器名称',
    materialModel  varchar(50)                                                null comment '规格型号',
    planNum        int         default 0                                      not null comment '申请计划数量',
    surplusNum     int         default 0                                      not null comment '剩余未入库数量',
    supplyTime     datetime    default '1753-01-01 00:00:00'                  not null comment '供应时间',
    purpose        varchar(100)                                               null comment '用途',
    skillRequire   varchar(255)                                               null comment '技术要求',
    remark         varchar(1000)                                              null comment '备注',
    isDeleted      bit         default b'0'                                   not null comment '假删',
    orgId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate     datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate     datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '仪器采购明细' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;


create table TB_LIM_OAInstrumentRepairApply
(
    id           varchar(50)                                                not null comment '主键id' primary key,
    instrumentId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器Id',
    failureDesc  varchar(1000)                                              null comment '设备故障描述',
    remark       varchar(1000)                                              null comment '备注',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '仪器的维修申请' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;

create table TB_LIM_OAInstrumentScrap
(
    id           varchar(50)                                                not null comment 'id'
        primary key,
    instrumentId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器标识',
    scrapDesc    varchar(1000)                                              null comment '报废描述',
    remark       varchar(1000)                                              null comment '备注',
    isDeleted    bit         default b'0'                                   not null comment '假删',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '仪器报废' ENGINE = InnoDB
                     CHARACTER SET = utf8
                     COLLATE = utf8_general_ci
                     ROW_FORMAT = Dynamic;


create table TB_LIM_OtherExpenditure
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    operatorId  varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作人id',
    operator    varchar(50)                                                not null comment '操作人',
    operateDate datetime    default CURRENT_TIMESTAMP                      not null comment '操作 日期',
    deptId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属部门（Guid）',
    paytype     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '支出类型（常量编码：LIM_ExpenditureType）',
    category    int         default 1                                      not null comment '支出种类（枚举EnumOtherPayCategory：1支出，2收入）',
    amount      decimal(18, 2)                                             not null comment '金额',
    `explain`   varchar(1000)                                              null comment '说明',
    projectId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    projectCode varchar(200)                                               null comment '项目编号',
    projectName varchar(1000)                                              null comment '项目名称',
    entId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '企业id',
    entName     varchar(1000)                                              null comment '企业名称',
    status      varchar(50)                                                null comment '状态',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_Person
(
    id                 varchar(50)                                                 not null comment 'id'
        primary key,
    cName              varchar(50)                                                 not null comment '姓名',
    pinYin             varchar(100)                                                null comment '拼音',
    fullPinYin         varchar(100)                                                null comment '全拼',
    userNo             varchar(20)                                                 null comment '编号',
    deptId             varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '所属科室（Guid）',
    postId             varchar(50) comment '职务（常量（Guid）：LIM_Post）',
    technicalTitleId   varchar(50) comment '职称（常量（Guid）：LIM_TechnicalTitle）',
    technicalTitleDate datetime     default '1753-01-01 00:00:00'                  not null comment '职称获得日期',
    eName              varchar(50)                                                 null comment '英文名',
    birthDay           datetime     default '1753-01-01 00:00:00'                  not null comment '出生日期',
    email              varchar(100)                                                null,
    sex                int          default 1                                      not null comment '性别（枚举EnumSex：  1代表男   2代表女）',
    status             int          default 1                                      not null comment '状态（枚举EnumPersonStatus:1代表在职 2代表离职 3代表休假）',
    idCard             varchar(20)                                                 null comment '身份证',
    politicalFace      varchar(20)                                                 null comment '政治面貌',
    volk               varchar(20)                                                 null comment '民族',
    nativePlace        varchar(100)                                                null comment '籍贯',
    archivesPlace      varchar(100)                                                null comment '档案所在地',
    homeTown           varchar(100)                                                null comment '户籍所在地',
    nation             varchar(20)                                                 null comment '国籍',
    signature          varchar(100)                                                null comment '人员签名图片路径',
    codeSigning        varchar(20)                                                 null comment '签名密码',
    photoUrl           varchar(500)                                                null comment '人员头像图片路径',
    orderNum           int          default 0                                      not null comment '排序值',
    degree             varchar(50) comment '学历（常量（Guid）：LIM_Degree：大专、本科、硕士研究生、博士研究生）',
    school             varchar(50)                                                 null comment '毕业院校',
    specialty          varchar(50)                                                 null comment '专业',
    mobile             varchar(50)                                                 null comment '手机',
    homeTel            varchar(50)                                                 null comment '固定电话',
    birthPlace         varchar(100)                                                null comment '出生地',
    homeAddress        varchar(100)                                                null comment '住址',
    emergentLinkMan    varchar(50)                                                 null comment '紧急联络人',
    contactMethod      varchar(50)                                                 null comment '联络方法',
    certificateNO      varchar(100)                                                null comment '准入证书',
    certificateDate    datetime     default '1753-01-01 00:00:00'                  not null comment '准入证书获取时间',
    yearsInThePosition decimal(18)                                                 not null comment '在岗时间',
    workStartTime      datetime     default '1753-01-01 00:00:00'                  not null comment '入职年限（参加工作时间）',
    joinCompanyTime    datetime     default '1753-01-01 00:00:00'                  not null comment '入职时间',
    beginWorkTime      datetime     default '1753-01-01 00:00:00'                  not null comment '开始工作时间',
    leaveCompanyTime   datetime     default '1753-01-01 00:00:00'                  not null comment '离职时间',
    joinPartyDate      datetime     default '1753-01-01 00:00:00'                  not null comment '入党日期',
    isDeleted          bit          default b'0'                                   not null comment '假删',
    keyPost            int          default 0                                      not null comment '关键岗位',
    tecCompetence      varchar(255)                                                null comment '技术能力',
    testResWork        varchar(255)                                                null comment '从事检测和/或校准工作方面的职责',
    testResEvaluation  varchar(255)                                                null comment '检测和/或校准策划和结果评价方面的职责',
    submissionDuty     varchar(255)                                                null comment '提交意见和解释的职责',
    developMethodRes   varchar(255)                                                null comment '方法改进新方法制定和确认方面的职责',
    experienceRequired varchar(255)                                                null comment '所需的专业知识和经验',
    trainingPrograms   varchar(255)                                                null comment '资格和培训计划',
    manageRes          varchar(255)                                                null comment '管理职责',
    remark             varchar(255)                                                null comment '备注说明',
    externalId         varchar(100) default '00000000-0000-0000-0000-000000000000' not null comment '关联系统人员编号',
    orgId              varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator            varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate         datetime     default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId           varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier           varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate         datetime     default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_Person2Test
(
    id              varchar(50)                                                not null comment 'id'
        primary key,
    personId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '人员Id',
    testId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    orderNum        int         default 0                                      not null comment '排序值',
    sampleTypeId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样品类型id',
    isDefaultPerson bit         default b'0'                                   not null comment '是否默认人员',
    orgId           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_PersonAbility
(
    id                varchar(50)                                                not null comment 'id'
        primary key,
    personId          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '人员Id',
    testId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试项目Id',
    personCertId      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '证书Id',
    achieveDate       datetime    default '1753-01-01 00:00:00'                  not null comment '证书获得日期',
    certEffectiveTime datetime    default '1753-01-01 00:00:00'                  not null comment '有效期至',
    orgId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;



create table TB_LIM_PersonCert
(
    id                varchar(50)                                                 not null comment 'id'
        primary key,
    personId          varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '人员Id（Guid）',
    certName          varchar(100)                                                not null comment '证书名称',
    certCode          varchar(50)                                                 not null comment '证书编号',
    issueCertTime     datetime     default '1753-01-01 00:00:00'                  not null comment '发证日期',
    certEffectiveTime datetime     default '1753-01-01 00:00:00'                  not null on update CURRENT_TIMESTAMP comment '有效期至',
    certType          varchar(100) default '-1'                                   not null comment '证书类型（枚举EnumCertType：1.水 2.气 3.声 4.土 5.其他    多选用","隔开）',
    phoneUrl          varchar(500)                                                null comment '图片存放位置',
    issuingAuthority  varchar(100)                                                null comment '发证机关（预留，3.2）',
    remark            varchar(1000)                                               null comment '备注（预留，3.2）',
    orgId             varchar(50)  default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    personName        varchar(255)                                                null
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_PersonFaceMsg
(
    id          varchar(50)                                                not null
        primary key,
    personId    varchar(50) default '00000000-0000-0000-0000-000000000000' null,
    facePicture longtext                                                   null,
    isDeleted   bit         default b'0'                                   not null comment '假删',
    orgId       varchar(50)                                                not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_ProjectInstrument
(
    id                varchar(50)                                                  not null comment '主键'
        primary key,
    projectId         varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '项目id',
    useDate           datetime      default '1753-01-01 00:00:00'                  not null comment '使用日期',
    administratorId   varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '管理人员Id',
    administratorName varchar(50)                                                  null comment '管理人员名称',
    userIds           varchar(2000) default '00000000-0000-0000-0000-000000000000' not null comment '使用人员id，多个用英文逗号间隔',
    userNames         varchar(2000)                                                null comment '使用人员名称，多个用英文逗号间隔',
    outQualified      int           default -1                                     not null comment '出库合格情况，0 - 不合格，1 - 合格',
    intQualified      int           default -1                                     not null comment '入库合格情况，0 - 不合格，1 - 合格',
    outRemarks        varchar(1000)                                                null comment '出库备注',
    inRemarks         varchar(1000)                                                null comment '入库备注',
    isDeleted         bit           default b'0'                                   not null comment '是否删除',
    orgId             varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator           varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate        datetime      default '1753-01-01 00:00:00'                  not null comment '创建时间',
    domainId          varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室id',
    modifier          varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '最近修改人',
    modifyDate        datetime      default '1753-01-01 00:00:00'                  not null comment '最新修改时间',
    projectName       varchar(100)                                                 null comment '项目名称'
) comment '项目仪器表' ENGINE = InnoDB
                       CHARACTER SET = utf8
                       COLLATE = utf8_general_ci
                       ROW_FORMAT = Dynamic;

create table TB_LIM_ProjectInstrumentDetails
(
    id                  varchar(50)                                                not null comment '主键'
        primary key,
    projectInstrumentId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '项目仪器表id',
    instrumentId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器id',
    inDate              datetime    default '1753-01-01 00:00:00'                  not null comment '入库日期',
    outDate             datetime    default '1753-01-01 00:00:00'                  not null comment '出库日期',
    inQualified         int         default -1                                     not null comment '入库合格情况，0 - 不合格，1 - 合格',
    outQualified        int         default -1                                     not null comment '出库合格情况，0 - 不合格，1 - 合格',
    isStorage           bit         default b'0'                                   not null comment '是否已入库 1：是 0：否',
    inPerson            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '入库人',
    outPerson           varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '出库人',
    isConfirm           bit         default b'0'                                   not null comment '是否出库确认',
    orgId               varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate          datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate          datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'

) comment '项目仪器使用详细记录' ENGINE = InnoDB
                                 CHARACTER SET = utf8
                                 COLLATE = utf8_general_ci
                                 ROW_FORMAT = Dynamic;


create table TB_LIM_RecAndPayRecord
(
    id           varchar(50)                                                not null comment 'id'
        primary key,
    contractId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '合同id',
    moneyType    int         default -1                                     not null comment '类型(枚举EnumMoneyType： 1.收款 2.付款 3.坏账)',
    amount       decimal(18, 2)                                             not null comment '金额',
    operatorId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '操作人id',
    operatorName varchar(50)                                                null comment '操作人姓名',
    operatorDate datetime    default CURRENT_TIMESTAMP                      not null comment '操作日期',
    invoiceNum   varchar(50)                                                null comment '发票号码',
    invoiceCode  varchar(50)                                                null comment '发票代码',
    invoiceDate  datetime    default '1753-01-01 00:00:00'                  not null comment '开票日期',
    isDeleted    bit         default b'0'                                   not null comment '假删',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator      varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate   datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate   datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_ReportConfig
(
    id               varchar(50)                                                not null comment 'id'
        primary key,
    type             int         default 1                                      not null comment '报表类型（枚举：EnumReportConfigType:1文件，2统计面板，3json数据源)',
    reportCode       varchar(100)                                               null comment '报表编码',
    templateName     varchar(100)                                               null comment '模板名称',
    template         varchar(100)                                               null comment '模版文件全名',
    outputName       varchar(100)                                               null comment '输出文件名',
    returnType       varchar(20)                                                null comment '返回文件类型（doc/docx/pdf……）',
    method           varchar(255)                                               null comment '调用方法全名',
    params           varchar(500)                                               null comment '参数键值对设置',
    pageConfig       mediumtext                                                 null comment 'json格式的页面配置',
    orderNum         int         default 0                                      not null comment '排序值',
    bizType          int         default -1                                     not null comment '业务分类（枚举：EnumReportType（1.报表 2.原始记录单）',
    remark           varchar(500)                                               null comment '备注',
    isDeleted        bit         default b'0'                                   not null comment '假删',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间',
    dataMethod       varchar(255)                                               null comment '数据方法调用',
    typeCode         varchar(50)                                                null comment '类型编码',
    strUrl           varchar(50)                                                null comment '接口路径',
    isDefineFileName bit         default b'0'                                   not null comment '是否定义生成报表名称',
    defineFileName   varchar(500)                                               null comment '配置报表名称',
    beanName         varchar(500)                                               null comment '配置方法名称',
    versionNum       varchar(100)                                               null comment '版本号',
    controlNum       varchar(100)                                               null comment '受控编号',
    reportName       varchar(1000)                                              null comment '报表名称（每个sheet页的名称用“;”分隔）'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_Test
(
    id                     varchar(50)                                                   not null comment 'id'
        primary key,
    parentId               varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '父级Id',
    analyzeMethodId        varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '分析方法Id（Guid）',
    redAnalyzeMethodName   varchar(255)                                                  null comment '分析方法名称',
    redCountryStandard     varchar(100)                                                  null comment '国家标准',
    analyzeItemId          varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '分析项目Id（Guid）',
    redAnalyzeItemName     varchar(100)                                                  null comment '分析项目名称',
    fullPinYin             varchar(255)                                                  null comment '分析项目全拼',
    pinYin                 varchar(100)                                                  null comment '分析项目拼音缩写',
    sampleTypeId           varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '样品类型（Guid）',
    testCode               varchar(50)                                                   null comment '测试编码',
    dimensionId            varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '计量单位（Guid）',
    examLimitValue         varchar(50)                                                   null comment '检出限',
    validTime              decimal(18, 2) default -1.00                                  not null comment '样品有效期（h）',
    orderNum               int            default 0                                      not null comment '排序值',
    mostSignificance       int            default -1                                     not null comment '有效位数',
    mostDecimal            int            default -1                                     not null comment '小数位数',
    cert                   int            default 4                                      not null comment '测试资质(枚举EnumCert：0非认可认证、1认证、2认可、4认证认可）',
    testName               varchar(1000)                                                 null comment '测试名称',
    isOutsourcing          bit            default b'0'                                   not null comment '是否分包',
    isCompleteField        bit            default b'0'                                   not null comment '是否现场数据',
    isDeleted              bit            default b'0'                                   not null comment '是否删除',
    isQCP                  bit            default b'1'                                   not null comment '是否做质控平行',
    isQCB                  bit            default b'1'                                   not null comment '是否做质控空白',
    isSeries               bit            default b'1'                                   not null comment '是否做串联样',
    isUseFormula           bit            default b'1'                                   not null comment '是否启用公式',
    examLimitValueLess     varchar(50)                                                   null comment '小于检出限出证结果',
    sampleCount            int            default 1                                      not null comment '样品数量',
    lowerLimit             varchar(50)                                                   null comment '测定下限',
    totalTestName          varchar(1000)                                                 null comment '总称（冗余分析项目名称）',
    isShowTotalTest        bit            default b'1'                                   not null comment '是否显示总称',
    isTotalTest            bit            default b'0'                                   not null comment '是否总称',
    isUseQTFormula         bit            default b'0'                                   not null comment '是否启用嵌套公式(检测结果计算出证结果)默认false',
    kValueFormat           int            default -1                                     not null comment '斜率有效位数',
    bValueFormat           int            default -1                                     not null comment '截距有效位数',
    cValueFormat           int            default -1                                     not null comment '实数有效位数',
    samplingCharge         decimal(18, 2)                                                not null comment '采样费金额',
    testingCharge          decimal(18, 2)                                                not null comment '检测费金额',
    reportDimensionId      varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '报告计量单位（Guid）',
    reportMostSignificance int            default -1                                     not null comment '报告有效位数',
    reportMostDecimal      int            default -1                                     not null comment '报告小数位数',
    remark                 varchar(1000)                                                 null comment '备注（预留）',
    isInsUseRecord         bit            default b'0'                                   not null comment '是否填写仪器使用记录（预留）',
    isSubSync              bit            default b'0'                                   not null comment '是否是检测机构传输过来的测试项目（预留）',
    inputMode              int            default 0                                      not null comment '录入方式（预留）（常量Int，常量名称Lim_TestInputMode）（0：默认，1：生物多样性）',
    domainCode             varchar(50)                                                   null comment '实验室编号（预留）',
    redYearSn              varchar(100)                                                  null comment '年份（预留）',
    testTimelen            int            default -1                                     not null comment '分配时长（预留）',
    basicWorkload          decimal(18, 2) default -1.00                                  not null comment '基础工作量（预留）',
    unitWorkload           decimal(18, 2) default -1.00                                  not null comment '单位工作量（预留）',
    externalId             varchar(100)   default '00000000-0000-0000-0000-000000000000' null comment '关联系统测试项目编号',
    orgId                  varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator                varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate             datetime       default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId               varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier               varchar(50)    default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate             datetime       default CURRENT_TIMESTAMP                      not null comment '修改时间',
    reviseType             int            default 1                                      not null,
    kDecimalFormat         int            default -1                                     not null comment '斜率小数位数',
    bDecimalFormat         int            default -1                                     not null comment '截距小数位数',
    cDecimalFormat         int            default -1                                     not null comment '实数小数位数',
    calculateWay           int            default 0                                      not null comment '计算方式',
    mergeBase              int                                                           not null comment '合并基数',
    analyseDayLen          int            default 2                                      not null comment '分析时长（天数）'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create index IX_TB_LIM_Test on tb_lim_test (sampleTypeId);


create table TB_LIM_Test2Instrument
(
    id           varchar(50)                                                not null comment 'id'
        primary key,
    testId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试标识（Guid）',
    instrumentId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '仪器标识（Guid）',
    useType      int         default -1                                     not null comment '使用类型（枚举EnumInsUseObjType：1采样，2：实验室分析，4：现场分析）',
    orgId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_TestExpand
(
    id                 varchar(50)                                                not null comment 'id'
        primary key,
    testId             varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试Id（Guid）',
    sampleTypeId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '样品类型Id（Guid）',
    mostSignificance   int         default -1                                     not null comment '有效位数',
    mostDecimal        int         default -1                                     not null comment '小数位数',
    examLimitValue     varchar(50)                                                null comment '检出限',
    dimensionId        varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '单位（Guid）',
    examLimitValueLess varchar(50)                                                null comment '小于检出限（预留）',
    sampleCount        int         default 1                                      not null comment '样品数量',
    lowerLimit         varchar(50)                                                null comment '测定下限',
    orgId              varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_TestPost
(
    id          varchar(50)                                                not null comment 'id'
        primary key,
    postCode    varchar(100)                                               null comment '岗位编码',
    postName    varchar(100)                                               not null comment '岗位名称',
    orderNum    int         default 0                                      not null comment '排序值',
    description varchar(1000)                                              null comment '岗位描述',
    isDeleted   bit         default b'0'                                   not null comment '是否删除',
    orgId       varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    creator     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate  datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier    varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate  datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;


create table TB_LIM_TestPost2Person
(
    id         varchar(50)                                                not null comment 'id'
        primary key,
    testPostId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试岗位Id',
    personId   varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '人员Id',
    orderNum   int         default 0                                      not null comment '排序值'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_TestPost2Test
(
    id         varchar(50)                                                not null comment 'id'
        primary key,
    testPostId varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试岗位Id',
    testId     varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '测试项目id',
    orderNum   int         default 0                                      not null comment '排序值'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_SampleType2Test
(
    id           varchar(50) not null
        primary key,
    sampleTypeId varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '检测类型id',
    testId       varchar(50) not null default '00000000-0000-0000-0000-000000000000' comment '测试项目id'
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

create table TB_LIM_SerialIdentifierConfig
(
    id            varchar(50)                                                  not null comment '主键id'
        primary key,
    configCode    varchar(100)                                                 null comment '配置编号(预留)',
    configName    varchar(100)                                                 null comment '配置名称',
    configType    int           default 1                                      not null comment '1:项目编号,2:样品编号,3:质控样编号,4:送样单编号,5:报告编号,6:工作单编号',
    configRule    varchar(1000)                                                null comment '配置规则',
    qcType        int           default 0                                      not null comment '质控类型--只有当选择质控样编号的时候才启用（枚举EnumQCGrade：0.外部质控  1.内部质控）',
    qcGrade       int           default 0                                      not null comment '质控等级--只有当选择质控样编号的时候才启用（枚举EnumQCType：0.空白 1.平行 2.标准 3.加标）',
    orderNum      int           default 0                                      not null comment '排序号',
    remark        varchar(1000)                                                null comment '备注',
    orgId         varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    isDeleted     bit           default b'0'                                   not null comment '假删',
    creator       varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate    datetime      default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId      varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier      varchar(50)   default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate    datetime      default CURRENT_TIMESTAMP                      not null comment '修改时间',
    projectTypeId varchar(1000) default '00000000-0000-0000-0000-000000000000' null
) comment '序列号定义配置' ENGINE = InnoDB
                           CHARACTER SET = utf8
                           COLLATE = utf8_general_ci
                           ROW_FORMAT = Dynamic;

create table TB_LIM_SerialNumberConfig
(
    id               varchar(50)                                                not null comment '主键id'
        primary key,
    serialNumberType varchar(100)                                               null comment '序号生成类型',
    para0            varchar(100)                                               null comment '参数1',
    para1            varchar(100)                                               null comment '参数2',
    para2            varchar(100)                                               null comment '参数3',
    para3            varchar(100)                                               null comment '参数4',
    orgId            varchar(50)                                                null comment '组织机构id',
    lastUpdateTime   datetime    default CURRENT_TIMESTAMP                      not null comment '最新更新时间',
    creator          varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '创建人',
    createDate       datetime    default CURRENT_TIMESTAMP                      not null comment '创建时间',
    domainId         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '所属实验室',
    modifier         varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '修改人',
    modifyDate       datetime    default CURRENT_TIMESTAMP                      not null comment '修改时间'
) comment '流水号的配置' ENGINE = InnoDB
                         CHARACTER SET = utf8
                         COLLATE = utf8_general_ci
                         ROW_FORMAT = Dynamic;
