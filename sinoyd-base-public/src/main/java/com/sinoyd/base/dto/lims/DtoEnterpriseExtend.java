package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.EnterpriseExtend;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoEnterpriseExtend实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_EnterpriseExtend")
 @Data
 @DynamicInsert
 public  class DtoEnterpriseExtend extends EnterpriseExtend {

 }