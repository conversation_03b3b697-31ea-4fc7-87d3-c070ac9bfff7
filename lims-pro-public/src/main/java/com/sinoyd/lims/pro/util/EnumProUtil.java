package com.sinoyd.lims.pro.util;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.configuration.ProjectModule;
import com.sinoyd.lims.pro.enums.EnumPRO;

import java.util.List;
import java.util.Map;

/**
 * PRO枚举工具类
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/15
 * @since V100R001
 */
public class EnumProUtil {
    /**
     * 首页缓存刷新查询对应的查询条件
     *
     * @param module 模块信息
     * @return 返回值
     */
    public static String findHomeTaskQueryCondition(EnumLIM.EnumHomeTaskModule module, ProjectModule projectModule,
                                                    String currentPersonId, Map<String, Object> values,
                                                    List<String> outTypeIds) {
        StringBuilder condition = new StringBuilder("");
        switch (module) {
            case 项目登记:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.项目登记.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and exists (select 1 from DtoProject b");
                    condition.append(" where a.projectId=b.id and b.inceptPersonId=:currentPersonId ");
                    if (StringUtil.isNotEmpty(outTypeIds)) {
                        condition.append("and b.projectTypeId not in :outTypeIds) ");
                        values.put("outTypeIds", outTypeIds);
                    } else {
                        condition.append(") ");
                    }

                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 例行登记:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.项目登记.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and exists (select 1 from DtoProject b");
                    condition.append(" where a.projectId=b.id and b.inceptPersonId=:currentPersonId and b.isMultiEnterprise = false ");
                    if (StringUtil.isNotEmpty(outTypeIds)) {
                        condition.append("and b.projectTypeId in :outTypeIds) ");
                        values.put("outTypeIds", outTypeIds);
                    } else {
                        condition.append(") ");
                    }
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 多企业污染源:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.项目登记.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and exists (select 1 from DtoProject b");
                    condition.append(" where a.projectId=b.id and b.inceptPersonId=:currentPersonId and b.isMultiEnterprise = true ");
                    if (StringUtil.isNotEmpty(outTypeIds)) {
                        condition.append("and b.projectTypeId in :outTypeIds) ");
                        values.put("outTypeIds", outTypeIds);
                    } else {
                        condition.append(") ");
                    }
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 项目审核:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.技术审核.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 技术下达:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.项目下达.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 方案编制:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.方案编制.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 方案审核:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.方案审核.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 方案确认:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.方案确认.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 采样准备:
                condition.append(String.format(" module='%s'", EnumLIM.EnumProjectModule.采样准备.getCode()));
                condition.append(" and exists (select 1 from DtoProject b");
                condition.append(" where a.projectId=b.id and b.samplingStatus=:samplingStatus)");
                values.put("samplingStatus", EnumPRO.EnumStatus.待处理.getValue());
                break;
            case 现场委托送样:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.委托现场送样.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 现场任务:
                condition.append(String.format(" a.status=%d and a.module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumReceiveRecordModule.现场数据录入.getValue()));
                condition.append(" and a.receiveId=b.id and b.projectId=c.id and c.isDeleted=0 ");
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    //需要按送样人及采样人员进行过滤
                    condition.append(" and (exists (select 1 from DtoSamplingPersonConfig sfc");
                    condition.append(" where a.receiveId=sfc.objectId and sfc.objectType =:objectType");
                    condition.append(" and sfc.samplingPersonId = :currentPersonId)");
                    condition.append(" or b.senderId =:currentPersonId");
                    condition.append("))");
                    values.put("objectType", EnumPRO.EnumSamplingType.送样单.getValue());
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 现场数据复核:
                condition.append(String.format(" a.status=%d and a.module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue()));
                condition.append(" and a.receiveId=b.id and b.projectId=c.id and c.isDeleted=0 ");
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 现场数据审核:
                condition.append(String.format(" a.status=%d and a.module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue()));
                condition.append(" and a.receiveId=b.id and b.projectId=c.id and c.isDeleted=0 ");
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 样品交接:
                condition.append(String.format(" a.status=%d and a.module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue()));
                condition.append(" and a.receiveId=b.id and b.projectId=c.id and c.isDeleted=0 ");
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 样品分配:
                condition.append(String.format(" bitand(a.subStatus ,%d) = %d",
                        EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue() + EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue(),
                        EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()));
                condition.append(" and a.receiveId=b.id and b.projectId=c.id and c.isDeleted=0 ");
                condition.append(String.format(" and b.receiveStatus=%d", EnumLIM.EnumReceiveRecordStatus.已经送样.getValue()));
                break;
            case 报告编制:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.报告管理.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and exists (select 1 from DtoProjectPlan b");
                    condition.append(" where a.projectId=b.projectId and b.reportMakerId=:currentPersonId)");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 报告审核:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumReportModule.报告审核.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 报告校核:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumReportModule.报告校核.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 报告复核:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumReportModule.报告复核.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 报告签发:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumReportModule.报告签发.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 任务办结:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.任务办结.getCode()));
                //显示数字为报告完成的任务  报告流程状态（EnumReportStatus： 0.未完成，1.已完成）
                condition.append(" and exists (select 1 from DtoProject p where a.projectId = p.id and p.reportStatus = :reportStatus)");
                values.put("reportStatus", EnumPRO.EnumReportStatus.已完成.getValue());
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 费用管理:
                condition.append(String.format(" a.status=%d and a.module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumPRO.EnumCostInfoModule.费用核算.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and exists (select 1 from DtoCostInfo b,DtoProject c");
                    condition.append(" where a.costInfoId = b.id and b.projectId = c.id and c.inceptPersonId=:currentPersonId)");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 费用审核:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumPRO.EnumCostInfoModule.费用审核.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 费用审批:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumPRO.EnumCostInfoModule.费用审批.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 质控任务登记:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumQCProjectModule.项目登记.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 数据汇总:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumQCProjectModule.数据汇总.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            case 评价结果:
                condition.append(String.format(" status=%d and module='%s'", EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumQCProjectModule.评价结果.getCode()));
                if (projectModule.getValue().equals(EnumLIM.EnumProjectTaskCache.人员.getValue())) {
                    condition.append(" and currentPersonId = :currentPersonId");
                    values.put("currentPersonId", currentPersonId);
                }
                break;
            default:
                break;
        }
        return condition.toString();
    }
}
