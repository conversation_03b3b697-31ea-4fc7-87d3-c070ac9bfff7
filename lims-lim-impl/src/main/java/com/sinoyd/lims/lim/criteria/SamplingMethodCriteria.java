package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采样方法查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/7/25
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamplingMethodCriteria extends BaseCriteria {

    private static final long serialVersionUID = 1L;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 关键字
     */
    private String key;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.sampleTypeId)) {
            condition.append(" and a.sampleTypeId = :sampleTypeId ");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        if (StringUtil.isNotNull(this.status) && !this.status.equals(-1)) {
            condition.append(" and a.status = :status ");
            values.put("status", this.status);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (a.methodName like :key or a.standardCode like :key) ");
            values.put("key", "%" + this.key + "%");
        }
        return condition.toString();
    }
}
