package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.customer.DtoQCProject;
import com.sinoyd.lims.pro.dto.customer.DtoQCProjectCopy;

/**
 * 质控项目操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/4
 * @since V100R001
 */
public interface QCProjectService extends IBaseJpaService<DtoProject, String> {

    void findQCProjectByPage(PageBean<DtoQCProject> pb, BaseCriteria qcProjectCriteria);

    /**
     * 获取项目详情
     *
     * @param id 项目id
     * @return 项目
     */
    DtoQCProject findQCProject(String id);

    /**
     * 复制项目
     *
     * @param copy    传输结构
     */
    void copyProject(DtoQCProjectCopy copy);

    /**
     * 工作流操作
     *
     * @param dtoWorkflowSign 实体
     */
    public void projectSignal(DtoWorkflowSign dtoWorkflowSign);

    DtoProject findAttachPath(String id);

    /**
     * 现场质控提交
     * @param qcProjectId 质控任务标识
     */
    void localSubmit(String qcProjectId);

    /**
     * 是否为现场质控人员比对
     * @param projectId 项目标识
     * @return 是否为现场质控人员比对
     */
    boolean isLocalPeopleCompare(String projectId);
}
