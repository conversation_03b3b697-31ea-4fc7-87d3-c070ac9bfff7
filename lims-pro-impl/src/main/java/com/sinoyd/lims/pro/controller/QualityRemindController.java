package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.dto.customer.DtoQualityRemind;
import com.sinoyd.lims.pro.service.QualityRemindService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * QualityRemind服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: QualityRemind服务")
@RestController
@RequestMapping("api/pro/qualityRemind")
public class QualityRemindController extends ExceptionHandlerController<QualityRemindService> {
    /**
     * 查询检测单的质控提醒
     *
     * @param workSheetFolderId 项目id
     * @return RestResponse<DtoQualityRemind>
     */
    @ApiOperation(value = "查询检测单的质控提醒", notes = "查询检测单的质控提醒")
    @GetMapping
    public RestResponse<List<DtoQualityRemind>> findByWorkSheetFolderId(@RequestParam(name = "workSheetFolderId") String workSheetFolderId) {
        RestResponse<List<DtoQualityRemind>> restResponse = new RestResponse<>();
        List<DtoQualityRemind> qualityRemindList = service.findByWorkSheetFolderId(workSheetFolderId);
        restResponse.setData(qualityRemindList);
        restResponse.setRestStatus(qualityRemindList.size() > 0 ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }

    /**
     * 查询送样单的质控提醒
     *
     * @param receiveIds 送样单id
     * @return RestResponse<DtoQualityRemind>
     */
    @ApiOperation(value = "查询检测单的质控提醒", notes = "查询检测单的质控提醒")
    @PostMapping(path = "/receive")
    public RestResponse<List<DtoQualityRemind>> findByReceiveIds(@RequestBody List<String> receiveIds) {
        RestResponse<List<DtoQualityRemind>> restResponse = new RestResponse<>();
        List<DtoQualityRemind> qualityRemindList = service.findByReceiveIds(receiveIds);
        restResponse.setData(qualityRemindList);
        restResponse.setRestStatus(qualityRemindList.size() > 0 ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        return restResponse;
    }
}
