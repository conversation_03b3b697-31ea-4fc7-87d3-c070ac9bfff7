package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.EntEvaluationCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoEntEvaluation;
import com.sinoyd.lims.lim.service.EntEvaluationService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商管理-评价信息
 * <AUTHOR>  修改：xuxb
 * @version V1.0.0 2019/3/8
 * @since V100R001
 * */
@RestController
@RequestMapping("/api/lim/entEvaluation")
@Validated
public class EntEvaluationController extends BaseJpaController<DtoEntEvaluation, String, EntEvaluationService> {

    /**
     * 根据id获取评价信息
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取评价信息", notes = "根据id获取评价信息")
    @GetMapping("/{id}")
    public RestResponse<DtoEntEvaluation> find(@PathVariable(name = "id") String id) {

        RestResponse<DtoEntEvaluation> restResp = new RestResponse<>();
        DtoEntEvaluation entity = service.findOne(id);
        restResp.setData(entity);

        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询评价信息
     * 
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询评价信息", notes = "分页动态条件查询评价信息")
    @GetMapping
    public RestResponse<List<DtoEntEvaluation>> findByPage(EntEvaluationCriteria criteria) {

        RestResponse<List<DtoEntEvaluation>> restResp = new RestResponse<>();

        PageBean<DtoEntEvaluation> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增评价信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增评价信息", notes = "新增评价信息")
    @PostMapping
    public RestResponse<DtoEntEvaluation> save(@Validated @RequestBody DtoEntEvaluation entity) {

        RestResponse<DtoEntEvaluation> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoEntEvaluation data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改评价信息
     * 
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改评价信息", notes = "修改评价信息")
    @PutMapping
    public RestResponse<DtoEntEvaluation> update(@Validated @RequestBody DtoEntEvaluation entity) {

        RestResponse<DtoEntEvaluation> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoEntEvaluation data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 刪除评价信息
     * 
     * @param id
     * @return
     */
    @ApiOperation(value = "刪除评价信息", notes = "刪除评价信息")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量删除评价信息
     * 
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除评价信息", notes = "批量删除评价信息")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }
}