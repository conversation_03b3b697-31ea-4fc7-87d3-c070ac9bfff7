package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 外部样品装载数据结构
 *
 * <AUTHOR>
 * @version V1.0.0 2020/6/10
 * @since V100R001
 */
@Data
public class DtoLoadOutSample {
    /**
     * 样品
     */
    private List<DtoSample> sample = new ArrayList<>();

    /**
     * 点位
     */
    private List<DtoSampleFolder> sampleFolder = new ArrayList<>();

    /**
     * 点位频次
     */
    private List<DtoSamplingFrequency> samplingFrequency = new ArrayList<>();

    /**
     * 点位频次指标
     */
    private List<DtoSamplingFrequencyTest> samplingFrequencyTest = new ArrayList<>();

    /**
     * 领样单
     */
    private List<DtoReceiveSubSampleRecord> receiveSubSampleRecord = new ArrayList<>();

    /**
     * 领样单样品关联
     */
    private List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2Sample = new ArrayList<>();

    /**
     * 分析数据
     */
    private List<DtoAnalyseData> analyseData = new ArrayList<>();

    /**
     * 质控管理
     */
    private List<DtoQualityManage> qualityManage = new ArrayList<>();

    /**
     * 参数数据
     */
    private List<DtoParamsData> paramsData = new ArrayList<>();

    /**
     * 添加样品
     *
     * @param dto 样品
     */
    public void addSample(DtoSample dto) {
        this.sample.add(dto);
    }

    /**
     * 添加点位
     *
     * @param dto 点位
     */
    public void addSampleFolder(DtoSampleFolder dto) {
        this.sampleFolder.add(dto);
    }

    /**
     * 添加点位频次
     *
     * @param dto 点位频次
     */
    public void addSamplingFrequency(DtoSamplingFrequency dto) {
        this.samplingFrequency.add(dto);
    }

    /**
     * 添加频次指标
     *
     * @param dto 频次指标
     */
    public void addSamplingFrequencyTest(DtoSamplingFrequencyTest dto) {
        this.samplingFrequencyTest.add(dto);
    }

    /**
     * 添加领样单
     *
     * @param dto 领样单
     */
    public void addReceiveSubSampleRecord(DtoReceiveSubSampleRecord dto) {
        this.receiveSubSampleRecord.add(dto);
    }

    /**
     * 添加领样单样品关联
     *
     * @param dto 领样单
     */
    public void addReceiveSubSampleRecord2Sample(DtoReceiveSubSampleRecord2Sample dto) {
        this.receiveSubSampleRecord2Sample.add(dto);
    }

    /**
     * 添加分析数据
     *
     * @param dto 分析数据
     */
    public void addAnalyseData(DtoAnalyseData dto) {
        this.analyseData.add(dto);
    }

    /**
     * 添加质控管理
     *
     * @param dto 质控管理
     */
    public void addQualityManage(DtoQualityManage dto) {
        this.qualityManage.add(dto);
    }

    /**
     * 添加参数数据
     *
     * @param dto 参数数据
     */
    public void addParamsData(DtoParamsData dto) {
        this.paramsData.add(dto);
    }

    /**
     * 样品字典
     */
    private Map<String, String> sampleMap = new HashMap<>();

    /**
     * 设置新的样品id
     *
     * @param sourceSampleId 源样品id
     * @param targetSampleId 目标样品id
     * @param extraKeys      额外拼装部分
     */
    public void putNewSampleId(String sourceSampleId, String targetSampleId, String... extraKeys) {
        String key = extraKeys.length > 0 ? String.format("%s_%s", sourceSampleId, String.join("_", extraKeys)) : sourceSampleId;
        this.sampleMap.put(key, targetSampleId);
    }

    /**
     * 获取目标样品id
     *
     * @param sourceSampleId 源样品id
     * @param extraKeys      额外拼装部分
     * @return 目标样品id
     */
    public String getNewSampleId(String sourceSampleId, String... extraKeys) {
        String key = extraKeys.length > 0 ? String.format("%s_%s", sourceSampleId, String.join("_", extraKeys)) : sourceSampleId;
        return this.sampleMap.getOrDefault(key, UUIDHelper.GUID_EMPTY);
    }

    /**
     * 点位字典
     */
    private Map<String, String> sampleFolderMap = new HashMap<>();

    /**
     * 设置新的点位id
     *
     * @param sourceSampleFolderId 源点位id
     * @param targetSampleFolderId 目标点位id
     * @param extraKeys            额外拼装部分
     */
    public void putNewSampleFolderId(String sourceSampleFolderId, String targetSampleFolderId, String... extraKeys) {
        String key = extraKeys.length > 0 ? String.format("%s_%s", sourceSampleFolderId, String.join("_", extraKeys)) : sourceSampleFolderId;
        this.sampleFolderMap.put(key, targetSampleFolderId);
    }

    /**
     * 获取目标点位id
     *
     * @param sourceSampleFolderId 源点位id
     * @param extraKeys            额外拼装部分
     * @return 目标点位id
     */
    public String getNewSampleFolderId(String sourceSampleFolderId, String... extraKeys) {
        String key = extraKeys.length > 0 ? String.format("%s_%s", sourceSampleFolderId, String.join("_", extraKeys)) : sourceSampleFolderId;
        return this.sampleFolderMap.getOrDefault(key, UUIDHelper.GUID_EMPTY);
    }

    /**
     * 点位频次字典
     */
    private Map<String, String> samplingFrequencyMap = new HashMap<>();

    /**
     * 设置新的点位频次id
     *
     * @param sourceSamplingFrequencyId 源点位频次id
     * @param targetSamplingFrequencyId 目标点位频次id
     * @param extraKeys                 额外拼装部分
     */
    public void putNewSamplingFrequencyId(String sourceSamplingFrequencyId, String targetSamplingFrequencyId, String... extraKeys) {
        String key = extraKeys.length > 0 ? String.format("%s_%s", sourceSamplingFrequencyId, String.join("_", extraKeys)) : sourceSamplingFrequencyId;
        this.samplingFrequencyMap.put(key, targetSamplingFrequencyId);
    }

    /**
     * 获取目标点位频次id
     *
     * @param sourceSamplingFrequencyId 源点位频次id
     * @param extraKeys                 额外拼装部分
     * @return 目标点位频次id
     */
    public String getNewSamplingFrequencyId(String sourceSamplingFrequencyId, String... extraKeys) {
        String key = extraKeys.length > 0 ? String.format("%s_%s", sourceSamplingFrequencyId, String.join("_", extraKeys)) : sourceSamplingFrequencyId;
        return this.samplingFrequencyMap.getOrDefault(key, UUIDHelper.GUID_EMPTY);
    }

    /**
     * 领样单字典
     */
    private Map<String, String> receiveSubSampleRecordMap = new HashMap<>();

    /**
     * 设置新的领样单id
     *
     * @param sourceReceiveSubSampleRecordId 源领样单id
     * @param targetReceiveSubSampleRecordId 目标领样单id
     * @param extraKeys                      额外拼装部分
     */
    public void putNewReceiveSubSampleRecordId(String sourceReceiveSubSampleRecordId, String targetReceiveSubSampleRecordId, String... extraKeys) {
        String key = extraKeys.length > 0 ? String.format("%s_%s", sourceReceiveSubSampleRecordId, String.join("_", extraKeys)) : sourceReceiveSubSampleRecordId;
        this.receiveSubSampleRecordMap.put(key, targetReceiveSubSampleRecordId);
    }

    /**
     * 获取目标领样单id
     *
     * @param sourceReceiveSubSampleRecordId 源领样单id
     * @param extraKeys                      额外拼装部分
     * @return 目标领样单id
     */
    public String getNewReceiveSubSampleRecordId(String sourceReceiveSubSampleRecordId, String... extraKeys) {
        String key = extraKeys.length > 0 ? String.format("%s_%s", sourceReceiveSubSampleRecordId, String.join("_", extraKeys)) : sourceReceiveSubSampleRecordId;
        return this.receiveSubSampleRecordMap.getOrDefault(key, UUIDHelper.GUID_EMPTY);
    }

    public List<String> getSampleIds() {
        return this.sample.stream().map(DtoSample::getId).collect(Collectors.toList());
    }

    public List<String> getSampleFolderIds() {
        return this.sampleFolder.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
    }

    public List<String> getReceiveSubSampleRecordIds() {
        return this.receiveSubSampleRecord.stream().map(DtoReceiveSubSampleRecord::getId).collect(Collectors.toList());
    }

    public List<String> getAnalyseDataIds() {
        return this.analyseData.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
    }

    /**
     * 获取样品指标类型
     *
     * @param sampleId 样品id
     * @return 指标类型
     */
    public Integer getSampleTestType(String sampleId) {
        Integer type = EnumPRO.EnumSampleTestType.无指标.getValue();
        List<String> subIds = this.receiveSubSampleRecord2Sample.stream().filter(p -> p.getSampleId().equals(sampleId)).map(DtoReceiveSubSampleRecord2Sample::getReceiveSubSampleRecordId).collect(Collectors.toList());
        if (subIds.size() > 0) {
            List<Integer> statusList = this.receiveSubSampleRecord.stream().filter(p -> subIds.contains(p.getId())).map(DtoReceiveSubSampleRecord::getSubStatus).collect(Collectors.toList());
            for (Integer status : statusList) {
                if ((status & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0) {
                    type += EnumPRO.EnumSampleTestType.现场指标.getValue();
                } else if ((status & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0) {
                    type += EnumPRO.EnumSampleTestType.实验室指标.getValue();
                }
            }
        }
        return type;
    }
}
