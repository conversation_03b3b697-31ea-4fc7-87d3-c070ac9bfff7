package com.sinoyd.lims.monitor.dto.lims;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.entity.Property2Point;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * DtoProperty2Point实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_MONITOR_Property2Point")
@Data
@DynamicInsert
public class DtoProperty2Point extends Property2Point {
    private static final long serialVersionUID = 1L;

    /**
     * 冗余点位信息
     */
    @Transient
    private DtoFixedpoint dtoFixedpoint;

    /**
     * 冗余测试项目id (用于批量新增测试项目时，前端给后端传参)
     */
    @Transient
    private List<String> testIds;

    /**
     * 冗余测试项目列表(用于批量新增测试项目时，前端给后端传参)
     */
    @Transient
    private List<DtoTest> testList;

    /**
     * 源对象的主键，用于复制
     */
    @Transient
    private String sourceId;

    /**
     * 监测计划名称
     */
    @Transient
    private String parentPropertyName;

    /**
     * 监测子计划名称
     */
    @Transient
    private String childPropertyName;

    /**
     * 年度
     */
    @Transient
    private Integer year;

    /**
     * 监测计划排序值
     */
    @Transient
    private Integer parentPropertyOrderNum;

    /**
     * 监测子计划排序值
     */
    @Transient
    private Integer childPropertyOrderNum;
}