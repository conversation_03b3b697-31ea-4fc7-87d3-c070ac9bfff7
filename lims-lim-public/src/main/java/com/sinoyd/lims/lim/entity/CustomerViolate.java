package com.sinoyd.lims.lim.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * CustomerViolate实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "CustomerViolate")
@Data
@EntityListeners(AuditingEntityListener.class)
public class CustomerViolate implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public CustomerViolate() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 企业id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("企业id")
    private String enterpriseId;

    /**
     * 登记人员id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("登记人员id")
    private String registerPersonId;

    /**
     * 登记时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("登记时间")
    private Date registerTime;

    /**
     * 发生时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("发生时间")
    private Date happenedTime;

    /**
     * 是否已处理
     */
    @Column(nullable = false)
    @ApiModelProperty("是否已处理")
    private Boolean hasHandle;

    /**
     * 要求处理时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("要求处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date requiredHandleTime;

    /**
     * 处理时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date handleTime;

    /**
     * 违约内容
     */
    @Column(length = 2000)
    @ApiModelProperty("违约内容")
    @Length(message = "违约内容{validation.message.length}", max = 2000)
    private String violateContent;

    /**
     * 处理人
     */
    @Column(length = 50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("处理人")
    @Length(message = "处理人{validation.message.length}", max = 50)
    private String handlePersonId;

    /**
     * 处理措施
     */
    @Column(length = 2000)
    @ApiModelProperty("处理措施")
    @Length(message = "处理措施{validation.message.length}", max = 2000)
    private String handleWay;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}