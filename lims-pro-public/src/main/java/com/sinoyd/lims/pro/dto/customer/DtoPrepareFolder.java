package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequency;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 采样准备点位结构
 * <AUTHOR>
 * @version V1.0.0 2019/12/1
 * @since V100R001
 */
@Data
public class DtoPrepareFolder {
    /**
     * 频次列表
     */
    private List<DtoSamplingFrequency> samplingFrequency = new ArrayList<>();

    /**
     * 样品数据
     */
    private List<DtoSample> sample = new ArrayList<>();
}
