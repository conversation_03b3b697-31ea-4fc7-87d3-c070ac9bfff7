package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import com.sinoyd.lims.lim.dto.customer.DtoPerson2TestTemp;
import com.sinoyd.lims.lim.service.Person2TestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 测试人员配置
 * <AUTHOR> 修改：guqx
 * @version V1.0.0 2019/2/1
 */
@Api(tags = "测试人员管理: 测试人员管理服务")
@RestController
@RequestMapping("/api/lim/person2Test")
@Validated
public class Person2TestController extends BaseJpaController<DtoPerson2Test, String, Person2TestService> {

    /**
     * 批量新增测试人员配置
     *
     * @param entities 测试人员实体
     * @return RestResponse<List < DtoPerson2Test>>
     */
    @ApiOperation(value = "批量新增测试人员", notes = "批量新增测试人员")
    @PostMapping("/save")
    public RestResponse<List<DtoPerson2Test>> save(@Validated @RequestBody List<DtoPerson2Test> entities) {
        RestResponse<List<DtoPerson2Test>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        List<DtoPerson2Test> data = service.save(entities);
        restResp.setData(data);
        restResp.setCount(data.size());
        return restResp;
    }

    @ApiOperation(value = "批量新增测试人员", notes = "批量新增测试人员")
    @PostMapping("/batch")
    public RestResponse<List<DtoPerson2Test>> save(@RequestBody DtoPerson2TestTemp dtoPerson2TestTemp) {
        RestResponse<List<DtoPerson2Test>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        List<DtoPerson2Test> data = service.save(dtoPerson2TestTemp.getPerson2Tests(), dtoPerson2TestTemp.getTestIds());
        restResp.setData(data);
        restResp.setCount(data.size());
        return restResp;
    }

    /**
     * 删除测试人员配置
     *
     * @param entity 根据testId 测试项目id和sampleTypeId 检测类型id
     * @return 是否删除成功
     */
    @ApiOperation(value = "删除测试人员", notes = "删除测试人员")
    @PostMapping("/delete")
    public RestResponse<String> delete(@RequestBody DtoPerson2Test entity) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.delete(entity.getTestId(), entity.getSampleTypeId());
        return restResp;
    }

    /**
     * 获取测试人员
     *
     * @param entity 根据testId 测试项目id和sampleTypeId 检测类型id
     * @return RestResponse<List < Person2Test>>
     */
    @PostMapping("/getTestPersons")
    public RestResponse<List<DtoPerson2Test>> getTestPersons(@Validated @RequestBody DtoPerson2Test entity) {
        RestResponse<List<DtoPerson2Test>> restResp = new RestResponse<>();
        List<DtoPerson2Test> person2Tests = service.getTestPersons(entity.getTestId(), entity.getSampleTypeId());
        restResp.setData(person2Tests);
        restResp.setRestStatus(StringUtil.isEmpty(person2Tests) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResp;
    }

    @GetMapping("/sampleType")
    public RestResponse<List<String>> findSampleTypeIdByTestId(@RequestParam(value = "testId") String testId) {
        RestResponse<List<String>> restResp = new RestResponse<>();
        List<String> sampleTypeIds = service.findSampleTypeIdByTestId(testId);
        restResp.setData(sampleTypeIds);
        return restResp;
    }
}