package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import com.sinoyd.lims.pro.dto.customer.DtoScanMessage;
import com.sinoyd.lims.pro.service.EnvironmentalUseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * EnvironmentalUseController 提供环境信息的保存
 * <AUTHOR>
 * @version V1.0.0 2019/12/03
 * @since V100R001
 */
@Api(tags = "流程环境信息服务")
@RestController
@RequestMapping("/api/pro/environmentalUse")
public class EnvironmentalUseController extends ExceptionHandlerController<EnvironmentalUseService> {

    @ApiOperation(value = "保存环境信息", notes = "保存环境信息")
    @PostMapping
    public RestResponse<DtoEnvironmentalRecord> save(@RequestBody @Validated DtoEnvironmentalRecord dto) {
        RestResponse<DtoEnvironmentalRecord> restResp = new RestResponse<>();
        restResp.setData(service.save(dto));
        return restResp;
    }

    @ApiOperation(value = "保存环境信息", notes = "保存环境信息")
    @PostMapping("/saveShareInstrument")
    public RestResponse<List<DtoEnvironmentalRecord>> saveShareInstrument(@RequestBody DtoEnvironmentalRecord dto) {
        RestResponse<List<DtoEnvironmentalRecord>> response = new RestResponse<>();
        response.setData(service.saveShareInstrument(dto.getUseRecordIds(), dto.getObjectId(), dto.getObjectType()));
        return response;
    }


    @ApiOperation(value = "检查仪器使用情况", notes = "检查仪器使用情况")
    @PostMapping("/check")
    public RestResponse<String> check(@RequestBody DtoEnvironmentalRecord dto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.check(dto));
        return restResp;
    }

    @ApiOperation(value = "批量检查仪器使用情况", notes = "批量检查仪器使用情况")
    @PostMapping("/checkBatch")
    public RestResponse<String> checkBatch(@RequestBody List<DtoEnvironmentalRecord> environmentalRecordList) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.checkBatch(environmentalRecordList));
        return restResp;
    }

    /**
     * 批量删除环境记录
     *
     * @param ids
     * @return
     */
    @ApiOperation(value = "批量删除环境记录", notes = "批量删除环境记录")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.delete(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 批量删除环境记录--移动端
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "批量删除环境记录", notes = "批量删除环境记录")
    @PostMapping("/removeInstrumentPhone")
    public RestResponse<String> removeInstrument(@RequestBody DtoScanMessage dto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.delete(dto.getIds());
        restResp.setCount(count);
        return restResp;
    }
}

