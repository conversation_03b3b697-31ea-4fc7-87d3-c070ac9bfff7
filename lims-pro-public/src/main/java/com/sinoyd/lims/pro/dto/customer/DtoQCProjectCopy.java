package com.sinoyd.lims.pro.dto.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 质控任务复制数据结构
 * <AUTHOR>
 * @version V1.0.0 2020/2/11
 * @since V100R001
 */
@Data
public class DtoQCProjectCopy {
    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 出具报告日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reportDate;

    /**
     * 要求完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadLine;
}
