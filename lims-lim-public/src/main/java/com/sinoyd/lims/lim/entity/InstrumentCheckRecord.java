package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.Digits;
import java.math.BigDecimal;
import java.util.Date;


/**
 * InstrumentCheckRecord实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description = "InstrumentCheckRecord")
@Data
@EntityListeners(AuditingEntityListener.class)
public class InstrumentCheckRecord extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public InstrumentCheckRecord() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 仪器Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("仪器Id")
    private String instrumentId;

    /**
     * 溯源方式(枚举：EnumOriginType:1检定、2校准、3自校)
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("溯源方式(枚举：EnumOriginType:1检定、2校准、3自校)")
    private Integer originType;

    /**
     * 检定/校准单位名称
     */
    @Column(length = 100)
    @Length(max = 100, message = "检定/校准单位名称长度不能超过100")
    @ApiModelProperty("检定/校准单位名称")
    private String checkDeptName;

    /**
     * 检定/校准人员id（Guid）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检定/校准人员id（Guid）")
    private String checkPersonId;

    /**
     * 检定/校准人员
     */
    @Column(length = 50)
    @Length(max = 50, message = "检定/校准人员长度不能超过50")
    @ApiModelProperty("检定/校准人员")
    private String checkPerson;

    /**
     * 检定/校准日期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("检定/校准日期")
    private Date checkTime;

    /**
     * 检定/校准结果(枚举：EnumOriginResult：1：合格:0：不合格)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("检定/校准结果(枚举：EnumOriginResult：1：合格:0：不合格)")
    private Integer checkResult;

    /**
     * 检定/校准方式(枚举：EnumCheckWay：1：送检:2：上门)
     */
    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("检定/校准结果(枚举：EnumCheckWay：1：送检:2：上门)")
    private Integer checkWay;

    /**
     * 证书(记录)编号
     */
    @Column(length = 100)
    @Length(max = 100, message = "证书(记录)编号长度不能超过100")
    @ApiModelProperty("证书(记录)编号")
    private String certiCode;

    /**
     * 适用性(是、否)
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("适用性(是、否)")
    private Boolean usability;

    /**
     * 溯源周期
     */
    @Column(nullable = false)
    @ColumnDefault("12")
    @ApiModelProperty("溯源周期")
    private BigDecimal originCyc;

    /**
     * 校准项
     */
    @Column(length = 1000)
    @ApiModelProperty("校准项")
    @Length(max = 1000, message = "校准项长度不能超过1000")
    private String checkContent;

    /**
     * 校准值（修正值）
     */
    @Column(length = 1000)
    @ApiModelProperty("校准值（修正值）")
    @Length(max = 1000, message = "校准值长度不能超过1000")
    private String calibration;

    /**
     * 指示值
     */
    @Column(length = 1000)
    @ApiModelProperty("指示值")
    @Length(max = 1000, message = "指示值长度不能超过1000")
    private String indicate;

    /**
     * 误差（ABS(指示值-校准值)/校准值）
     */
    @Column(length = 1000)
    @ApiModelProperty("误差（ABS(指示值-校准值)/校准值）")
    @Length(max = 1000, message = "误差长度不能超过1000")
    private String deviation;

    /**
     * 备注
     */
    @Column(length = 4000)
    @ApiModelProperty("备注")
    @Length(max = 4000, message = "备注长度不能超过4000")
    private String remark;

    /**
     * 检定/校准有效期
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("检定/校准有效期")
    private Date checkEndDate;

    /**
     * 费用
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @Digits(integer = 18, fraction = 2, message = "费用整数位精度18小数位经度2")
    @ApiModelProperty("费用")
    private BigDecimal cost;

    /**
     * 责任人标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("责任人标识")
    private String personId;

    /**
     * 责任部门标识
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("责任部门标识")
    private String deptId;

    /**
     * 技术指标
     */
    @ApiModelProperty("技术指标")
    private String indicator;

    /**
     * 确认依据
     */
    @ApiModelProperty("确认依据")
    private String basis;

    /**
     * 仪器关联多检测器id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("仪器关联多检测器id")
    private String instrument2DetectorId;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}