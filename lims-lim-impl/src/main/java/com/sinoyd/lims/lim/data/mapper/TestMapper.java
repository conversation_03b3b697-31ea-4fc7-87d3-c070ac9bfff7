package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.lims.lim.dto.customer.DtoExportTest;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * 测试项目实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface TestMapper {

    /**
     * DtoTest 实例转换成DtoExportTest实例
     *
     * @param test 分析项目实体
     * @return DtoExportTest 实例
     */
    @Mapping(source = "validTime", target = "validTime")
    @Mapping(source = "orderNum", target = "orderNum")
    @Mapping(source = "mostSignificance", target = "mostSignificance")
    @Mapping(source = "mostDecimal", target = "mostDecimal")
    @Mapping(source = "cert", target = "cert")
    @Mapping(source = "isOutsourcing", target = "isOutsourcing")
    @Mapping(source = "isCompleteField", target = "isCompleteField")
    @Mapping(source = "isDeleted", target = "isDeleted")
    @Mapping(source = "isQCP", target = "isQCP")
    @Mapping(source = "isQCB", target = "isQCB")
    @Mapping(source = "isSeries", target = "isSeries")
    @Mapping(source = "isUseFormula", target = "isUseFormula")
    @Mapping(source = "timesOrder", target = "timesOrder")
    @Mapping(source = "samplePeriod", target = "samplePeriod")
    @Mapping(source = "isShowTotalTest", target = "isShowTotalTest")
    @Mapping(source = "isTotalTest", target = "isTotalTest")
    @Mapping(source = "isUseQTFormula", target = "isUseQTFormula")
    @Mapping(source = "samplingCharge", target = "samplingCharge")
    @Mapping(source = "testingCharge", target = "testingCharge")
    @Mapping(source = "reportMostSignificance", target = "reportMostSignificance")
    @Mapping(source = "reportMostDecimal", target = "reportMostDecimal")
    @Mapping(source = "isInsUseRecord", target = "isInsUseRecord")
    @Mapping(source = "isSubSync", target = "isSubSync")
    @Mapping(source = "inputMode", target = "inputMode")
    @Mapping(source = "averageCompute", target = "averageCompute")
    @Mapping(source = "testTimelen", target = "testTimelen")
    @Mapping(source = "basicWorkload", target = "basicWorkload")
    @Mapping(source = "unitWorkload", target = "unitWorkload")
    @Mapping(source = "reviseType", target = "reviseType")
    @Mapping(source = "calculateWay", target = "calculateWay")
    @Mapping(source = "mergeBase", target = "mergeBase")
    @Mapping(source = "analyseDayLen", target = "analyseDayLen")
    @Mapping(source = "validate", target = "validate")
    @Mapping(source = "usageNum", target = "usageNum")
    DtoExportTest toExportTest(DtoTest test);

    /**
     * DtoTest 实例集合转换成DtoExportTest 实例集合
     *
     * @param testList 分析项目实例集合
     * @return DtoExportTest 实例集合
     */
    @InheritConfiguration(name = "toExportTest")
    List<DtoExportTest> toExportTestList(List<DtoTest> testList);


    /**
     * DtoExportTest 实例转换成DtoTest 实例
     *
     * @param exportTest 分析项目实体
     * @return DtoExportTest 实例
     */

    @Mapping(source = "validTime", target = "validTime")
    @Mapping(source = "orderNum", target = "orderNum")
    @Mapping(source = "mostSignificance", target = "mostSignificance")
    @Mapping(source = "mostDecimal", target = "mostDecimal")
    @Mapping(source = "cert", target = "cert")
    @Mapping(source = "isOutsourcing", target = "isOutsourcing")
    @Mapping(source = "isCompleteField", target = "isCompleteField")
    @Mapping(source = "isDeleted", target = "isDeleted")
    @Mapping(source = "isQCP", target = "isQCP")
    @Mapping(source = "isQCB", target = "isQCB")
    @Mapping(source = "isSeries", target = "isSeries")
    @Mapping(source = "isUseFormula", target = "isUseFormula")
    @Mapping(source = "timesOrder", target = "timesOrder")
    @Mapping(source = "samplePeriod", target = "samplePeriod")
    @Mapping(source = "isShowTotalTest", target = "isShowTotalTest")
    @Mapping(source = "isTotalTest", target = "isTotalTest")
    @Mapping(source = "isUseQTFormula", target = "isUseQTFormula")
    @Mapping(source = "samplingCharge", target = "samplingCharge")
    @Mapping(source = "testingCharge", target = "testingCharge")
    @Mapping(source = "reportMostSignificance", target = "reportMostSignificance")
    @Mapping(source = "reportMostDecimal", target = "reportMostDecimal")
    @Mapping(source = "isInsUseRecord", target = "isInsUseRecord")
    @Mapping(source = "isSubSync", target = "isSubSync")
    @Mapping(source = "inputMode", target = "inputMode")
    @Mapping(source = "averageCompute", target = "averageCompute")
    @Mapping(source = "testTimelen", target = "testTimelen")
    @Mapping(source = "basicWorkload", target = "basicWorkload")
    @Mapping(source = "unitWorkload", target = "unitWorkload")
    @Mapping(source = "reviseType", target = "reviseType")
    @Mapping(source = "calculateWay", target = "calculateWay")
    @Mapping(source = "mergeBase", target = "mergeBase")
    @Mapping(source = "analyseDayLen", target = "analyseDayLen")
    @Mapping(source = "validate", target = "validate")
    @Mapping(source = "usageNum", target = "usageNum")
    DtoTest toDtoTest(DtoExportTest exportTest);

    /**
     * DtoExportTest 实例集合转换成DtoTest 实例集合
     *
     * @param exportTestList 分析项目导入导出实例集合
     * @return DtoTest 实例集合
     */
    @InheritConfiguration(name = "toDtoTest")
    List<DtoTest> toDtoTestList(List<DtoExportTest> exportTestList);

}
