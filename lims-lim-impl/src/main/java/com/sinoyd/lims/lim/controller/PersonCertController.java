package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.PersonCertCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;
import com.sinoyd.lims.lim.service.CertHistoryInfoService;
import com.sinoyd.lims.lim.service.PersonCertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 人员证书
 * <AUTHOR>  修改：guqx
 * @version V1.0.0 2019/3/6
 * @since V100R001
 */
@Api(tags = "人员证书管理: 人员证书管理服务")
@RestController
@RequestMapping("/api/lim/personCert")
@Validated
public class PersonCertController extends BaseJpaController<DtoPersonCert,String,PersonCertService> {

    @Autowired
    @Lazy
    private CertHistoryInfoService certHistoryInfoService;

    /**
     * 分页查询人员证书
     * @param personCertCriteria 条件
     * @return RestResponse<List<DtoPersonCert>>
     */
    @ApiOperation(value = "分页查询人员证书", notes = "分页查询人员证书")
    @GetMapping
    public RestResponse<List<DtoPersonCert>> findByPage(PersonCertCriteria personCertCriteria)
    {
        RestResponse<List<DtoPersonCert>> restResp = new RestResponse<>();
        PageBean<DtoPersonCert> page = super.getPageBean();
        service.findByPage(page, personCertCriteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ?
        ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }
    /**
     * 根据id删除人员证书
     * @param id 
     * @return RestResponse<String>
     */
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable("id") String id)
    {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "批量假删", notes = "批量假删")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }

    /**
     * 新增证书
     * @param entity 人员证书实体
     * @return RestResponse<DtoPersonCert>
     */
    @ApiOperation(value = "新增证书", notes = "新增证书")
    @PostMapping
    public RestResponse<DtoPersonCert> save(@Validated @RequestBody DtoPersonCert entity) {
        RestResponse<DtoPersonCert> restResp = new RestResponse<DtoPersonCert>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoPersonCert data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改证书
     * @param entity 人员证书实体
     * @return RestResponse<DtoPersonCert>
     */
    @ApiOperation(value = "修改证书", notes = "修改证书")
    @PutMapping
    public RestResponse<DtoPersonCert> update(@Validated @RequestBody DtoPersonCert entity) {
        RestResponse<DtoPersonCert> restResp = new RestResponse<DtoPersonCert>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        DtoPersonCert data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 上岗证过期详情
     * @return 上岗证过期列表
     */
    @ApiOperation(value = "新增人员证书", notes = "新增人员证书")
    @GetMapping("/overDue")
    public RestResponse<List<DtoPersonAbility>> getOverDueData() {
        RestResponse<List<DtoPersonAbility>> restResp = new RestResponse<>();
        List<DtoPersonAbility> list = service. getOverDueData();
        restResp.setRestStatus(StringUtil.isNotEmpty(list)?ERestStatus.SUCCESS:ERestStatus.UNMATCH_RECORD);
        restResp.setData(list);
        restResp.setCount(list.size());
        return restResp;
    }

    /**
     * 证书历史信息
     * @return 上岗证过期列表
     */
    @ApiOperation(value = "新增人员证书", notes = "新增人员证书")
    @GetMapping("/history/{projectId}")
    public RestResponse<Map<String,Object>> queryHistoryInfo(@PathVariable String projectId) {
        RestResponse<Map<String,Object>> restResp = new RestResponse<>();
        restResp.setData(certHistoryInfoService.queryHistoryInfo(projectId));
        return restResp;
    }

    /**
     * 获取历史附件
     * @return 附件
     */
    @ApiOperation(value = "获取历史附件", notes = "获取历史附件")
    @PostMapping("/history/queryHistoryFile")
    public RestResponse<List<DtoDocument>> queryHistoryFile(@RequestBody List<String> certHistoryFileIds) {
        RestResponse<List<DtoDocument>> restResp = new RestResponse<>();
        restResp.setData(certHistoryInfoService.queryHistoryFile(certHistoryFileIds));
        return restResp;
    }
}