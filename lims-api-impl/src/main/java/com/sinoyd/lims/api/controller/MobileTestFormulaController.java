package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.criteria.TestFormulaCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormula;
import com.sinoyd.lims.lim.service.TestFormulaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 测试项目公式
 * <AUTHOR>
 * @version V1.0.0 2019/12/17
 * @since V100R001
 */
@Api(tags = "测试项目公式")
@RestController
@RequestMapping("/api/mobile/testFormula")
public class MobileTestFormulaController extends ExceptionHandlerController<TestFormulaService> {

    @ApiOperation(value = "分页动态条件查询人员", notes = "分页动态条件查询人员")
    @GetMapping
    public RestResponse<List<DtoTestFormula>> findByPage(TestFormulaCriteria testFormulaCriteria) {
        RestResponse<List<DtoTestFormula>> restResp = new RestResponse<>();
        PageBean<DtoTestFormula> page = super.getPageBean();
        service.findByPage(page, testFormulaCriteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    @ApiOperation(value = "分页动态条件查询人员", notes = "分页动态条件查询人员")
    @PostMapping("/query")
    public RestResponse<List<DtoTestFormula>> findPostByPage(@RequestBody TestFormulaCriteria testFormulaCriteria) {
        return findByPage(testFormulaCriteria);
    }
}
