package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.SamplingAchievement2PersonCriteria;
import com.sinoyd.lims.pro.dto.DtoSamplingAchievement2Person;
import com.sinoyd.lims.pro.service.SamplingAchievement2PersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * SamplingAchievement2Person服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/13
 * @since V100R001
 */
@Api(tags = "示例: SamplingAchievement2Person服务")
@RestController
@RequestMapping("api/pro/samplingAchievement2Person")
public class SamplingAchievement2PersonController extends BaseJpaController<DtoSamplingAchievement2Person, String, SamplingAchievement2PersonService> {

    /**
     * 分页动态条件查询DtoSamplingAchievement2Person
     *
     * @param criteria 条件参数
     * @return RestResponse<List <DtoSamplingAchievement2Person>>
     */
    @ApiOperation(value = "分页动态条件查询DtoSamplingAchievement2Person", notes = "分页动态条件查询DtoSamplingAchievement2Person")
    @GetMapping
    public RestResponse<List<DtoSamplingAchievement2Person>> findByPage(SamplingAchievement2PersonCriteria criteria) {
        PageBean<DtoSamplingAchievement2Person> pageBean = super.getPageBean();
        RestResponse<List<DtoSamplingAchievement2Person>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 选择人员
     * @param personIds
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "选择人员", notes = "选择人员")
    @PostMapping("/selectPerson")
    public RestResponse<Void> selectPerson(@RequestBody List<String> personIds) {
        RestResponse<Void> response = new RestResponse<>();
        service.selectPerson(personIds);
        return response;
    }

    /**
     * 校验选择人员
     * @param personIds
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "校验选择人员", notes = "校验选择人员")
    @PostMapping("/verifySelectPerson")
    public RestResponse<Void> verifySelectPerson(@RequestBody List<String> personIds) {
        RestResponse<Void> response = new RestResponse<>();
        service.verifySelectPerson(personIds);
        return response;
    }

    /**
     * 更新数据
     * @param map 数据
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "更新数据", notes = "更新数据")
    @PostMapping("/updateData")
    public RestResponse<Void> updateData(@RequestBody Map<String, Object> map) {
        RestResponse<Void> response = new RestResponse<>();
        service.updateData((Integer)map.get("year"), (List<String>)map.get("ids"));
        return response;
    }

    /**
     * 根据id批量删除
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    @ApiOperation(value = "获取月度数据统计", notes = "获取月度数据统计")
    @GetMapping("/chartForPerMonth")
    public RestResponse<Map<String, BigDecimal>> chartForPerMonth() {
        RestResponse<Map<String, BigDecimal>> response = new RestResponse<>();
        response.setData(service.chartForPerMonth());
        return response;
    }

}
