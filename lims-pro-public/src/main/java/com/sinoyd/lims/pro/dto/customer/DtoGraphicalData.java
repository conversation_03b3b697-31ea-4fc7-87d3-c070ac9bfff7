package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 检测数据统计图表传输实体
 *
 * <AUTHOR>
 * @version V5.3.3 2022/07/09
 * @since V100R001
 */
@Data
public class DtoGraphicalData {
    /**
     * 分析人
     */
    private String personName;

    /**
     * 待检样品数
     */
    private Integer pendingTestNum;

    /**
     * 已检样品数
     */
    private Integer completedTestNum;

    /**
     * 待检数量（饼状图数据）
     */
    private Integer totalPendingTestNum;

    /**
     * 未领数量（饼状图数据）
     */
    private Integer totalNotReceiveNum;

    /**
     * 检测中数量（饼状图数据）
     */
    private Integer totalTestingNum;

    /**
     * 确认数量（饼状图数据）
     */
    private Integer totalConfirmNum;

    /**
     * 复核中数量（饼状图数据）
     */
    private Integer totalReviewNum;

}
