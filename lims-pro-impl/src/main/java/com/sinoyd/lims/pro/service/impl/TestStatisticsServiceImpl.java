package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.criteria.TestStatisticsCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.DtoAnalyseStatisticsData;
import com.sinoyd.lims.pro.dto.customer.DtoGraphicalData;
import com.sinoyd.lims.pro.dto.customer.DtoSubRecordVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.QualityControlService;
import com.sinoyd.lims.pro.service.SampleService;
import com.sinoyd.lims.pro.service.TestStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 检测统计实现类
 *
 * <AUTHOR>
 * @version V5.2.0 2022/7/8
 */
@Service
@Slf4j
public class TestStatisticsServiceImpl implements TestStatisticsService {

    private SampleService sampleService;

    private ProjectRepository projectRepository;

    private ProjectTypeService projectTypeService;

    private SampleTypeService sampleTypeService;

    private QualityControlService qualityControlService;

    private WorkSheetFolderRepository workSheetFolderRepository;

    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;

    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    private CommonRepository comRepository;


    @Override
    public void findTestData(PageBean<DtoAnalyseStatisticsData> page, BaseCriteria criteria) {
        page.setEntityName("DtoAnalyseStatisticsData a");
        page.setSelect("select a");
        long t1 = System.currentTimeMillis();
        comRepository.findByPage(page, criteria);
        log.info("原始数据查询：" + (System.currentTimeMillis() - t1));
    }

    @Override
    public Map<String, Object> findCircularChart(PageBean<DtoAnalyseStatisticsData> pageBean, BaseCriteria criteria) {

        long t1 = System.currentTimeMillis();
        pageBean.setEntityName("DtoAnalyseStatisticsData a");
        pageBean.setSelect("select a");
        comRepository.findByPage(pageBean, criteria);
        List<DtoAnalyseStatisticsData> analyseDataList = pageBean.getData();
        log.info("原始数据查询2：" + (System.currentTimeMillis() - t1));
        Map<String, Object> result = new HashMap<>();
        Map<String, Long> statusCountMap = analyseDataList.stream()
                .collect(Collectors.groupingBy(DtoAnalyseStatisticsData::getAnalyseDataStatus, Collectors.counting()));
        result.put("circularChart", statusCountMap);
        //柱状图数据
        List<DtoGraphicalData> graphicalDataList = new ArrayList<>();
        Map<String, List<DtoAnalyseStatisticsData>> analyseStatisticsDataMap = analyseDataList.stream()
                .filter(p -> StringUtil.isNotEmpty(p.getAnalystName())).collect(Collectors.groupingBy(DtoAnalyseStatisticsData::getAnalystName));

        analyseStatisticsDataMap.forEach((personName, analyseStatisticsDataList) -> {
            DtoGraphicalData graphicalData = new DtoGraphicalData();
            graphicalData.setPersonName(personName);
            Map<String, Integer> personMap = analyseStatisticsDataList.stream()
                    .collect(Collectors.groupingBy(DtoAnalyseStatisticsData::getAnalyseDataStatus, Collectors.summingInt(e -> 1)));
            graphicalData.setPendingTestNum((personMap.getOrDefault("未领", 0) + personMap.getOrDefault("待检", 0)));
            graphicalData.setCompletedTestNum(personMap.getOrDefault("检测中", 0));
            graphicalDataList.add(graphicalData);
        });
        result.put("Histogram", graphicalDataList);
        return result;
    }

    private List<String> getProjectIds(TestStatisticsCriteria testStatisticsCriteria) {
        List<String> projectIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(testStatisticsCriteria.getProjectTypeId())) {
            projectIds = projectRepository.findByProjectTypeIdIn
                    (Collections.singletonList(testStatisticsCriteria.getProjectTypeId()))
                    .stream().map(DtoProject::getId).collect(Collectors.toList());
        }
        return projectIds;
    }

    private List<String> getReceiveIds(TestStatisticsCriteria testStatisticsCriteria, List<String> projectIds) {

        StringBuilder stringBuilder = new StringBuilder();
        Map<String, Object> values = new HashMap<>();
        stringBuilder.append("select rs.id ");
        stringBuilder.append(" from DtoReceiveSampleRecord rs");
        stringBuilder.append(" where 1=1 and rs.isDeleted = 0 ");
        stringBuilder.append(" and status !=:status");
        values.put("status", EnumLIM.EnumReceiveRecordStatus.新建.name());
        if (projectIds.size() > 0) {
            stringBuilder.append(" and rs.projectId in :projectIds");
            values.put("projectIds", projectIds);
        }
        if (StringUtil.isNotEmpty(testStatisticsCriteria.getBeginTime())) {
            Date from = DateUtil.stringToDate(testStatisticsCriteria.getBeginTime(), DateUtil.YEAR);
            stringBuilder.append(" and rs.receiveTime >= :beginTime");
            values.put("beginTime", from);
        }

        if (StringUtil.isNotEmpty(testStatisticsCriteria.getEndTime())) {
            Date to = DateUtil.stringToDate(testStatisticsCriteria.getEndTime(), DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            to = c.getTime();
            stringBuilder.append(" and rs.receiveTime <= :endTime");
            values.put("endTime", to);
        }
        return comRepository.find(stringBuilder.toString(), values);
    }

    private List<DtoSubRecordVo> getSubReceiveIds(TestStatisticsCriteria testStatisticsCriteria, List<String> receiveIds) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        int count = 0;
        for (int index = 0; index < receiveIds.size(); index += 100) {
            List<String> recIds = receiveIds.stream().skip(index).limit(100).collect(Collectors.toList());
            if (index > 0) {
                stringBuilder.append(" union ");
                count++;
            }
            sqlSubReceive(stringBuilder, count, recIds, testStatisticsCriteria, values);
        }
        List<DtoSubRecordVo> recordVoList = new ArrayList<>();
        if (StringUtil.isNotEmpty(stringBuilder.toString())) {
            recordVoList = namedParameterJdbcTemplate.query(stringBuilder.toString(), values, (rs, rowNum) -> {
                DtoSubRecordVo recordVo = new DtoSubRecordVo();
                recordVo.setId(rs.getString("id"));
                recordVo.setStatus(rs.getString("status"));
                return recordVo;
            });
        }
        return recordVoList;
    }

    private void sqlSubReceive(StringBuilder stringBuilder, int index, List<String> recIds,
                               TestStatisticsCriteria testStatisticsCriteria, Map<String, Object> values) {
        stringBuilder.append("select rss" + index + ".id,rss" + index + ".status");
        stringBuilder.append(" from tb_pro_receivesubsamplerecord rss" + index);
        stringBuilder.append(" where 1=1");
        stringBuilder.append(" and rss" + index + ".receiveId in (:recIds" + index + ")");
        //有实验室数据
        values.put("recIds" + index, recIds);

        //判断是否是未领取
        if (StringUtil.isNotEmpty(testStatisticsCriteria.getStatus())) {
            if (EnumPRO.EnumStatisticDataStatus.未领.name().equals(testStatisticsCriteria.getStatus())) {
                stringBuilder.append(" and rss" + index + ".status =:status" + index);
                values.put("status" + index, EnumPRO.EnumReceiveSubRecordStatusName.未领取.name());
            } else {
                stringBuilder.append(" and rss" + index + ".status !=:status" + index);
                values.put("status" + index, EnumPRO.EnumReceiveSubRecordStatusName.未领取.name());
            }
        }
    }

    private List<DtoAnalyseStatisticsData> getAnalyseDataIds(TestStatisticsCriteria testStatisticsCriteria, List<String> samIds) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder();
        int count = 0;
        for (int index = 0; index < samIds.size(); index += 100) {
            List<String> sIds = samIds.stream().skip(index).limit(100).collect(Collectors.toList());
            if (index > 0) {
                stringBuilder.append(" union ");
                count++;
            }
            sqlAnalyseData(stringBuilder, count, sIds, values, testStatisticsCriteria);
        }
        List<DtoAnalyseStatisticsData> dataList = new ArrayList<>();
        if (StringUtil.isNotEmpty(stringBuilder.toString())) {
            dataList = namedParameterJdbcTemplate.query(stringBuilder.toString(), values, (rs, rowNum) -> {
                DtoAnalyseStatisticsData data = new DtoAnalyseStatisticsData();
                data.setId(rs.getString("id"));
                data.setAnalystName(rs.getString("analystName"));
                data.setAnalyseDataStatus(rs.getString("status"));
                data.setSampleId(rs.getString("sampleId"));
                data.setWorkSheetFolderId(rs.getString("workSheetFolderId"));
                return data;
            });
        }
        return dataList;
    }

    private void sqlAnalyseData(StringBuilder stringBuilder, int count,
                                List<String> samIds, Map<String, Object> values,
                                TestStatisticsCriteria testStatisticsCriteria) {
        stringBuilder.append("select ana" + count + ".id,ana" + count + ".analystName," +
                "ana" + count + ".status,ana" + count + ".sampleId,ana" + count + ".workSheetFolderId ");
        stringBuilder.append(" from tb_pro_analyseData ana" + count + ",tb_pro_sample sam" + count);
        stringBuilder.append(" where ana" + count + ".sampleId = sam" + count + ".id");
        stringBuilder.append(" and ana" + count + ".isDeleted = 0 and ana" + count + ".isCompleteField = 0");
        stringBuilder.append(" and sam" + count + ".id in (:samIds" + count + ")");
        values.put("samIds" + count, samIds);
        //样品编号
        if (StringUtil.isNotEmpty(testStatisticsCriteria.getSampleCode())) {
            stringBuilder.append(" and sam" + count + ".code like :sampleCode" + count);
            values.put("sampleCode" + count, "%" + testStatisticsCriteria.getSampleCode() + "%");
        }
        //分析项目
        if (StringUtil.isNotEmpty(testStatisticsCriteria.getAnalyzeItem())) {
            stringBuilder.append(" and ana" + count + ".redAnalyzeItemName like :analyzeItem" + count);
            values.put("analyzeItem" + count, "%" + testStatisticsCriteria.getAnalyzeItem() + "%");
        }
        if (StringUtil.isNotEmpty(testStatisticsCriteria.getSampleTypeId())) {
            stringBuilder.append(" and sam" + count + ".sampleTypeId = :sampleTypeId" + count);
            values.put("sampleTypeId" + count, testStatisticsCriteria.getSampleTypeId());
        }
        if (StringUtil.isNotEmpty(testStatisticsCriteria.getAnalyzePerson())) {
            stringBuilder.append(" and ana" + count + ".analystId = :analyzePerson" + count);
            values.put("analyzePerson" + count, testStatisticsCriteria.getAnalyzePerson());
        }
        //人员
        if (StringUtils.isNotNullAndEmpty(testStatisticsCriteria.getAnalyzePersons())) {
            String replace = testStatisticsCriteria.getAnalyzePersons().replace("，", ",");
            List<String> analyzePersonsList = Arrays.asList(replace.split(","));
            stringBuilder.append(" and ana" + count + ".analystId in (:analyzePersons" + count + ")");
            values.put("analyzePersons" + count, analyzePersonsList);
        }
        //岗位
        if (StringUtils.isNotNullAndEmpty(testStatisticsCriteria.getTestPostId())) {
            //当选择岗位而不选择人员时查询此岗位下的所有人员
            stringBuilder.append(" and ana" + count + ".analystId in (select pp.personId from tb_lim_testpost2person pp " +
                    "where pp.testPostId = :testPostId" + count + ")");
            values.put("testPostId" + count, testStatisticsCriteria.getTestPostId());
        }
        if (StringUtil.isNotEmpty(testStatisticsCriteria.getStatus())) {
            //不用判断未领 -- 领样单已经判断了
            if (EnumPRO.EnumStatisticDataStatus.待检.name().equals(testStatisticsCriteria.getStatus())) {
                stringBuilder.append(" and ana" + count + ".workSheetFolderId =:workSheetFolderId" + count);
                values.put("workSheetFolderId" + count, UUIDHelper.GUID_EMPTY);
                stringBuilder.append(" and ana" + count + ".dataStatus =:status" + count);
                values.put("status" + count, EnumPRO.EnumAnalyseDataStatus.未测.getValue());
            } else if (EnumPRO.EnumStatisticDataStatus.检测中.name().equals(testStatisticsCriteria.getStatus())) {
                stringBuilder.append(" and ana" + count + ".dataStatus in (:status" + count + ")");
                List<Integer> statusList = new ArrayList<>();
                statusList.add(EnumPRO.EnumAnalyseDataStatus.拒绝.getValue());
                statusList.add(EnumPRO.EnumAnalyseDataStatus.在测.getValue());
                values.put("status" + count, statusList);
            } else if (EnumPRO.EnumStatisticDataStatus.复核中.name().equals(testStatisticsCriteria.getStatus())) {
                stringBuilder.append(" and ana" + count + ".dataStatus in (:status" + count + ")");
                List<Integer> statusList = new ArrayList<>();
                statusList.add(EnumPRO.EnumAnalyseDataStatus.已测.getValue());
                statusList.add(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue());
                values.put("status" + count, statusList);
            } else if (EnumPRO.EnumStatisticDataStatus.已确认.name().equals(testStatisticsCriteria.getStatus())) {
                stringBuilder.append(" and ana" + count + ".dataStatus =:status" + count);
                values.put("status" + count, EnumPRO.EnumAnalyseDataStatus.已确认.getValue());
            }
        }
    }

    private void setAnalyseDataStatus(List<DtoAnalyseStatisticsData> analyseDataList,
                                      List<DtoReceiveSubSampleRecord2Sample> r2sList,
                                      List<DtoSubRecordVo> subRecordVoList,
                                      Map<String, Integer> map, String personName) {
        //未领
        List<String> wlRecordIds = subRecordVoList.stream()
                .filter(p -> p.getStatus().equals(EnumPRO.EnumReceiveSubRecordStatusName.未领取.name()))
                .map(DtoSubRecordVo::getId).collect(Collectors.toList());
        List<String> samIds = r2sList.stream().filter(p -> wlRecordIds.contains(p.getReceiveSubSampleRecordId()))
                .map(DtoReceiveSubSampleRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
        List<DtoAnalyseStatisticsData> dataList = analyseDataList.stream().filter(p -> samIds.contains(p.getSampleId())
                && (StringUtil.isNotEmpty(p.getAnalystName()) && (!StringUtil.isNotEmpty(personName)
                || p.getAnalystName().equals(personName)))).collect(Collectors.toList());
        List<String> anaIds = dataList.stream().map(DtoAnalyseStatisticsData::getId).collect(Collectors.toList());
        map.put("未领", dataList.size());
        //待检
        dataList = analyseDataList.stream().filter(p -> UUIDHelper.GUID_EMPTY.equals(p.getWorkSheetFolderId())
                && !anaIds.contains(p.getId())
                && p.getAnalyseDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.未测.name())
                && (StringUtil.isNotEmpty(p.getAnalystName()) && (!StringUtil.isNotEmpty(personName)
                || p.getAnalystName().equals(personName)))).collect(Collectors.toList());
        map.put("待检", dataList.size());
        //检测中
        dataList = analyseDataList.stream().filter(p -> (p.getAnalyseDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.拒绝.name())
                || p.getAnalyseDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.在测.name()))
                && (StringUtil.isNotEmpty(p.getAnalystName()) && (!StringUtil.isNotEmpty(personName)
                || p.getAnalystName().equals(personName)))).collect(Collectors.toList());
        map.put("检测中", dataList.size());
        //复核中
        dataList = analyseDataList.stream().filter(p -> (p.getAnalyseDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已测.name())
                || p.getAnalyseDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.name()))
                && (StringUtil.isNotEmpty(p.getAnalystName()) && (!StringUtil.isNotEmpty(personName)
                || p.getAnalystName().equals(personName)))).collect(Collectors.toList());
        map.put("复核中", dataList.size());
        //已确认
        dataList = analyseDataList.stream().filter(p -> p.getAnalyseDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.name())
                && (StringUtil.isNotEmpty(p.getAnalystName()) && (!StringUtil.isNotEmpty(personName)
                || p.getAnalystName().equals(personName)))).collect(Collectors.toList());
        map.put("已确认", dataList.size());
    }

    @Autowired
    public void setSampleService(SampleService sampleService) {
        this.sampleService = sampleService;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setProjectTypeService(ProjectTypeService projectTypeService) {
        this.projectTypeService = projectTypeService;
    }

    @Autowired
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    public void setWorkSheetFolderRepository(WorkSheetFolderRepository workSheetFolderRepository) {
        this.workSheetFolderRepository = workSheetFolderRepository;
    }

    @Autowired
    public void setQualityControlService(QualityControlService qualityControlService) {
        this.qualityControlService = qualityControlService;
    }

    @Autowired
    public void setComRepository(CommonRepository comRepository) {
        this.comRepository = comRepository;
    }

    @Autowired
    public void setReceiveSubSampleRecord2SampleRepository(ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository) {
        this.receiveSubSampleRecord2SampleRepository = receiveSubSampleRecord2SampleRepository;
    }

    @Autowired
    public void setNamedParameterJdbcTemplate(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
    }
}
