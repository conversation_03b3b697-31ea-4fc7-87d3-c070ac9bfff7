package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.InstrumentGather;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;


/**
 * DtoInstrumentGather实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_InstrumentGather")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoInstrumentGather extends InstrumentGather {

    /**
     * 设备名称
     */
    @Transient
    private String instrumentName;

    /**
     * 规格型号
     */
    @Transient
    private String model;

    /**
     * 本站编号
     */
    @Transient
    private String instrumentsCode;

    /**
     * 过期时间
     */
    @Transient
    private Date originEndDate;

    /**
     * 通道数据
     */
    @Transient
    private Integer channelNum;

    /**
     * base64Content的内容
     */
    @Transient
    private String base64Content;

}