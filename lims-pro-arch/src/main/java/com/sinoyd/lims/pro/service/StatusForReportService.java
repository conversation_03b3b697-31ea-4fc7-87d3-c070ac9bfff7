package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoStatusForReport;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Map;


/**
 * StatusForReport操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
public interface StatusForReportService extends IBaseJpaService<DtoStatusForReport, String> {

     /**
     * 创建状态
     *
     * @param reportId 报告id
     */
     void createStatus(String reportId);

    /**
     * 修改状态数据
     *
     * @param from   状态起
     * @param to     状态止
     * @param sign   工作流信号对象
     * @param report 报告实体
     * @param personMap 人员映射
     */
     void modifyStatus(String from, String to, DtoWorkflowSign sign, DtoReport report, Map<String, DtoPerson> personMap);
}