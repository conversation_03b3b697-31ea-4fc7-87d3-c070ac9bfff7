package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.ReportModule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoReportModule实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_ReportModule")
@Data
@DynamicInsert
public class DtoReportModule extends ReportModule {

    private static final long serialVersionUID = 1L;

    @Transient
    private List<DtoReportModule2GroupType> reportModule2GroupTypeList;

}
