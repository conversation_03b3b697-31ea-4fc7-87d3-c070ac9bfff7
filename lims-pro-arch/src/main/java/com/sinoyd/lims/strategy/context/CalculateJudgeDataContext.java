package com.sinoyd.lims.strategy.context;

import com.sinoyd.lims.pro.dto.DtoSampleJudgeData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/29
 */
public interface CalculateJudgeDataContext {

    /**
     * 生成附件名称
     *
     * @param checkType     计算名称
     * @param judgeDataList 报表id
     */
    void calculateJudgeData(Integer checkType, List<DtoSampleJudgeData> judgeDataList);
}
