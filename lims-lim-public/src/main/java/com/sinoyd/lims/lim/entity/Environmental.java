package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * Environmental实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="Environmental")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Environmental implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Environmental() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 实验室名称
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("实验室名称")
    @Length(message = "实验室名称{validation.message.length}", max = 50)
	private String labName;
    
    /**
    * 实验室编号
    */
    @Column(length=50)
    @ApiModelProperty("实验室编号")
    @Length(message = "实验室编号{validation.message.length}", max = 50)
    private String labCode;
    
    /**
    * 最低温度
    */
    @Column(length=50)
    @ApiModelProperty("最低温度")
    @Length(message = "最低温度{validation.message.length}", max = 50)
    private String lowestTemperature;
    
    /**
    * 最高温度
    */
    @Column(length=50)
    @ApiModelProperty("最高温度")
    @Length(message = "最高温度{validation.message.length}", max = 50)
    private String highestTemperature;
    
    /**
    * 最低湿度
    */
    @Column(length=50)
    @ApiModelProperty("最低湿度")
    @Length(message = "最低湿度{validation.message.length}", max = 50)
    private String lowestHumidity;
    
    /**
    * 最高湿度
    */
    @Column(length=50)
    @ApiModelProperty("最高湿度")
    @Length(message = "最高湿度{validation.message.length}", max = 50)
    private String highestHumidity;
    
    /**
    * 最低气压
    */
    @Column(length=50)
    @ApiModelProperty("最低气压")
    @Length(message = "最低气压{validation.message.length}", max = 50)
    private String lowestPressure;
    
    /**
    * 最高气压
    */
    @Column(length=50)
    @ApiModelProperty("最高气压")
    @Length(message = "最高气压{validation.message.length}", max = 50)
    private String highestPressure;
    
    /**
    * 负责人id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("负责人id")
    private String personInChargeId;
    
    /**
    * 负责人
    */
    @Column(length=50)
    @ApiModelProperty("负责人")
    private String personInCharge;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }