package com.sinoyd.base.dto.rcc;

import javax.persistence.*;

import com.sinoyd.base.entity.QualityLimitDisposition;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoQualityLimitDisposition实体
 * <AUTHOR>
 * @version V1.0.0 2024/5/27
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_QualityLimitDisposition")
 @Data
 @DynamicInsert
 public  class DtoQualityLimitDisposition extends QualityLimitDisposition {
   private static final long serialVersionUID = 1L;

   /**
    * 关联指标数
    */
   @Transient
   private Integer testCount;
}