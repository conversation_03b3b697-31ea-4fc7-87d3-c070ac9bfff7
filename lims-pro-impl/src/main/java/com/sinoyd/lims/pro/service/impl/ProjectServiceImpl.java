package com.sinoyd.lims.pro.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.EvaluationValueRepository;
import com.sinoyd.base.service.*;
import com.sinoyd.base.utils.reflection.DynamicBean;
import com.sinoyd.base.utils.reflection.ReflectUtil;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.QrCodeUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup2Test;
import com.sinoyd.lims.lim.entity.ParamsConfig;
import com.sinoyd.lims.lim.entity.Person2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsConfigRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroup2TestRepository;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointProperty;
import com.sinoyd.lims.monitor.dto.lims.DtoProperty2Point;
import com.sinoyd.lims.monitor.dto.lims.DtoPropertyPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Test;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPointExpend;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.entity.FixedPointProperty;
import com.sinoyd.lims.monitor.entity.Property2Point;
import com.sinoyd.lims.monitor.enums.EnumMonitor;
import com.sinoyd.lims.monitor.repository.lims.FixedPointPropertyRepository;
import com.sinoyd.lims.monitor.repository.lims.Property2PointRepository;
import com.sinoyd.lims.monitor.repository.lims.PropertyPoint2TestRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedPoint2TestRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedPointExpendRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.ProjectCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.entity.Project2FixedProperty;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.MathUtil;
import com.sinoyd.lims.strategy.context.SubmitRestrictContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.awt.image.BufferedImage;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.*;


/**
 * 项目操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/15
 * @since V100R001
 */
@Service
@Slf4j
public class ProjectServiceImpl extends BaseJpaServiceImpl<DtoProject, String, ProjectRepository> implements ProjectService {

    //#region 注入
    @Autowired
    @Qualifier("project")
    @Lazy
    private SerialNumberService serialNumberService;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    @Lazy
    private WorkflowService workflowService;

    @Autowired
    private CodeService codeService;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private ProjectPlanService projectPlanService;

    @Autowired
    private ProjectPlanRepository projectPlanRepository;

    @Autowired
    @Lazy
    private StatusForProjectService statusForProjectService;

    @Autowired
    private StatusForProjectRepository statusForProjectRepository;

    @Autowired
    @Lazy
    private SamplingFrequencyService samplingFrequencyService;

    @Autowired
    private SamplingFrequencyRepository samplingFrequencyRepository;

    @Autowired
    @Lazy
    private Project2ContractService project2ContractService;

    @Autowired
    private Project2ContractRepository project2ContractRepository;

    @Autowired
    @Lazy
    private SampleFolderService sampleFolderService;

    @Autowired
    private Project2FixedPropertyService project2FixedPropertyService;

    @Autowired
    private Project2FixedPropertyRepository project2FixedPropertyRepository;

    @Autowired
    private FixedPointPropertyRepository fixedPointPropertyRepository;

    @Autowired
    private FixedPointExpendRepository fixedPointExpendRepository;

    @Autowired
    private FixedpointRepository fixedpointRepository;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    private EvaluationRecordRepository evaluationRecordRepository;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private ReportRepository reportRepository;

    @Autowired
    @Lazy
    private SubmitRecordService submitRecordService;

    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    @Lazy
    private ContractService contractService;

    @Autowired
    @Lazy
    private DocumentService documentService;

    @Autowired
    @Lazy
    private IndustryTypeService industryTypeService;

    @Autowired
    @Lazy
    private HomeService homeService;

    @Autowired
    @Lazy
    private SchemeService schemeService;

    @Autowired
    private Property2PointRepository property2PointRepository;

    @Autowired
    private PropertyPoint2TestRepository propertyPoint2TestRepository;

    @Autowired
    private EvaluationValueRepository evaluationValueRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    @Autowired
    private FixedPoint2TestRepository fixedPoint2TestRepository;

    @Autowired
    private EnterpriseRepository enterpriseRepository;

    @Autowired
    private ExploreRepository exploreRepository;

    @Autowired
    private FolderSignRepository folderSignRepository;

    @Autowired
    private QuotationDetailRepository quotationDetailRepository;

    @Autowired
    private QuotationDetail2TestRepository quotationDetail2TestRepository;

    @Autowired
    private OrderFormRepository orderFormRepository;

    @Autowired
    @Lazy
    private TestExpandService testExpandService;

    @Autowired
    @Lazy
    private Person2TestService person2TestService;

    @Autowired
    @Lazy
    private DimensionService dimensionService;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    @Lazy
    private ReceiveSubSampleRecordService receiveSubSampleRecordService;

    @Autowired
    private Project2CustomerRepository project2CustomerRepository;

    @Autowired
    private ParamsDataRepository paramsDataRepository;

    @Autowired
    private ParamsConfigRepository paramsConfigRepository;

    @Autowired
    private Project2ReportRepository project2ReportRepository;

    @Autowired
    private SampleTypeGroup2TestRepository sampleTypeGroup2TestRepository;

    @Autowired
    private SubmitRestrictContext submitRestrictContext;

    @Autowired
    private AnalyzeMethodRepository analyzeMethodRepository;

    private EnvironmentEnterpriseService environmentEnterpriseService;

    @Autowired
    private ProjectContractRepository projectContractRepository;

    @Autowired
    private SignatureService signatureService;

    @Autowired
    @Lazy
    private AreaService areaService;

    //#endregion

    @SuppressWarnings("unchecked")
    @Override
    public void findByPage(PageBean<DtoProject> pb, BaseCriteria baseCriteria) {
        //设置例行任务相关，为criteria中条件判断做准备
        ProjectCriteria projectCriteria = (ProjectCriteria) baseCriteria;
        if (StringUtil.isNotNull(projectCriteria.getIsProjectApprove()) && projectCriteria.getIsProjectApprove()) {
            String[] values = {EnumProjectType.委托类.getValue(), EnumProjectType.例行类.getValue(), EnumProjectType.全流程.getValue()};
            List<Map<String, Object>> projectTypes = projectTypeService.getProjectTypeByCode("projectRegisterPage", values);
            projectCriteria.setFilterTypeIds(projectTypes.stream().map(map -> (String) map.get("id")).collect(toList()));
        }
        List<String> outTypeIds = new ArrayList<>();
        if (projectCriteria.getModule().equals(EnumLIM.EnumProjectModule.项目登记.getCode())) {
            List<DtoProjectType> projectTypeList = projectTypeService.findByTypeCode("HJ");
            projectTypeList.addAll(projectTypeService.findByTypeCode("WR"));
            if (projectTypeList.size() > 0) {
                outTypeIds.addAll(projectTypeList.stream().map(DtoProjectType::getId).distinct().collect(Collectors.toList()));
            }
            projectCriteria.setOutTypeIds(outTypeIds);
        } else if (projectCriteria.getModule().equals(EnumLIM.EnumProjectModule.污染源例行登记.getCode())) {
            List<DtoProjectType> projectTypeList = projectTypeService.findByTypeCode("WR");
            if (projectTypeList.size() > 0) {
                outTypeIds.addAll(projectTypeList.stream().map(DtoProjectType::getId).distinct().collect(Collectors.toList()));
            }
            projectCriteria.setOutTypeIds(outTypeIds);
        } else if (projectCriteria.getModule().equals(EnumLIM.EnumProjectModule.环境例行登记.getCode())) {
            List<DtoProjectType> projectTypeList = projectTypeService.findByTypeCode("HJ");
            if (projectTypeList.size() > 0) {
                outTypeIds.addAll(projectTypeList.stream().map(DtoProjectType::getId).distinct().collect(Collectors.toList()));
            }
            projectCriteria.setOutTypeIds(outTypeIds);
        } else if (EnumLIM.EnumProjectModule.报告管理.getCode().equals(projectCriteria.getModule())) {
            List<DtoProjectType> projectTypeList = projectTypeService.findByTypeCode("FB");
            List<String> fbTypeIds = StringUtil.isNotEmpty(projectTypeList) ? projectTypeList.stream().map(DtoProjectType::getId).collect(toList()) : Collections.singletonList(UUIDHelper.GUID_EMPTY);
            projectCriteria.setFbTypeIds(fbTypeIds);
        }
        if (StringUtil.isEmpty(projectCriteria.getProjectTypeId()) && StringUtil.isNotEmpty(projectCriteria.getProjectTypeName())) {
            projectTypeService.findAll().stream().filter(v -> projectCriteria.getProjectTypeName().equals(v.getName()))
                    .findFirst().ifPresent(v -> projectCriteria.setProjectTypeId(v.getId()));
        }

        pb.setEntityName("DtoProject p,DtoProjectPlan pl,DtoStatusForProject s");
        pb.setSelect("select p,pl,json_value(json,'$.contractCode') as contractCode,json_value(json,'$.collectionStatus') as collectionStatus" +
                ",json_value(json,'$.collectionDetail') as collectionDetail,json_value(json,'$.notSampled') as notSampled,json_value(json,'$.analyzeSummary') as analyzeSummary" +
                ",json_value(json,'$.analyzeDetail') as analyzeDetail,json_value(json,'$.reportDetail') as reportDetail,json_value(json,'$.dataChangeStatus') as dataChangeStatus");
        comRepository.findByPage(pb, projectCriteria);
        List<DtoProject> datas = pb.getData();
        List<DtoProject> newDatas = new ArrayList<>();
        Iterator<DtoProject> projectIte = datas.iterator();
//        List<DtoEnterprise> enterpriseList = enterpriseRepository.findAll();
        List<String> proIds = new ArrayList<>();

        // 循环迭代获取JPQL中查询返回的属性
        while (projectIte.hasNext()) {
            Object obj = projectIte.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoProject project = (DtoProject) objs[0];
            proIds.add(project.getId());
            DtoProjectPlan plan = (DtoProjectPlan) objs[1];
            project.loadFromPlan(plan);
            if (StringUtils.isNotNullAndEmpty(project.getJson())) {
                Map jsonMap = JsonIterator.deserialize(project.getJson(), Map.class);
                if (StringUtil.isNotNull(objs[2])) {
                    project.setContractCode((String) objs[2]);
                }
                if (MathUtil.isInteger(objs[3])) {
                    project.setCollectionStatus(Integer.valueOf(String.valueOf(objs[3])));
                }
                if (jsonMap.containsKey("collectionDetail") && StringUtil.isNotNull(jsonMap.get("collectionDetail"))) {
                    project.setCollectionDetail((List<BigDecimal>) jsonMap.get("collectionDetail"));
                }
                if (MathUtil.isInteger(objs[5])) {
                    project.setNotSampled(Integer.valueOf(String.valueOf(objs[5])));
                }
                if (StringUtil.isNotNull(objs[6])) {
                    project.setAnalyzeSummary((String) objs[6]);
                }
                if (jsonMap.containsKey("analyzeDetail") && StringUtil.isNotNull(jsonMap.get("analyzeDetail"))) {
                    project.setAnalyzeDetail((List<Integer>) jsonMap.get("analyzeDetail"));
                }
                if (jsonMap.containsKey("reportDetail") && StringUtil.isNotNull(jsonMap.get("reportDetail"))) {
                    project.setReportDetail((List<Integer>) jsonMap.get("reportDetail"));
                }
                project.setDataChangeStatus(EnumProjectChangeStatus.未变更.getValue());
//                if (MathUtil.isInteger(objs[9])) {
//                    project.setDataChangeStatus(Integer.valueOf(String.valueOf(objs[9])));
//                }
            }
            //分页查询时，应该查询项目上的冗余字段，不应该查询配置中的企业名称（2023-07-06【长城提】）
//            Optional<DtoEnterprise> enterpriseOptional = enterpriseList.parallelStream()
//                    .filter(p -> p.getId().equals(project.getCustomerId())).findFirst();
//            enterpriseOptional.ifPresent(p -> project.setCustomerName(p.getName()));
            newDatas.add(project);
        }
        List<DtoReport> reportList = reportRepository.findByProjectIdIn(proIds);
        Map<String, String> lastNewOpinionMap = findStatusOptionMaps(projectCriteria, newDatas);

        List<String> projectTypeIds = newDatas.stream().map(DtoProject::getProjectTypeId).distinct().collect(Collectors.toList());
        List<DtoProjectType> projectTypes = projectTypeService.findRedisByIds(projectTypeIds);

        List<DtoSample> samples = StringUtil.isNotEmpty(proIds) ? sampleRepository.findByProjectIdIn(proIds) : new ArrayList<>();
        List<String> sampleTypeIds = samples.stream().map(DtoSample::getSampleTypeId).distinct().collect(toList());
        List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findAll(sampleTypeIds) : new ArrayList<>();

        Map<String, String> personMap = new HashMap<>();
        List<String> personIds = newDatas.stream().map(DtoProject::getInceptPersonId).distinct().collect(Collectors.toList());
        personIds.addAll(newDatas.stream().map(DtoProject::getLeaderId).distinct().collect(Collectors.toList()));
        personIds.addAll(newDatas.stream().map(DtoProject::getReportMakerId).distinct().collect(Collectors.toList()));
        if (personIds.size() > 0) {
            List<DtoPerson> persons = personService.findAllDeleted(personIds.stream().distinct().collect(Collectors.toList()));
            //将list转为map
            personMap = persons.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
        }
        //子项目集合
        List<DtoProject> subProjectList = this.findByParentIds(proIds);
        List<DtoExplore> exploreList = new ArrayList<>();
        //获取当前页所有项目的报告
        List<String> projectIdsForReport = newDatas.stream().map(DtoProject::getId).collect(Collectors.toList());
        List<DtoReport> reports = reportRepository.findByProjectIdIn(projectIdsForReport);
        List<DtoProjectContract> projectContractList = new ArrayList<>();
        //获取所有的报告明细
        if (StringUtil.isNotEmpty(reports)) {
            List<String> reportIds = reports.stream().map(DtoReport::getId).collect(Collectors.toList());
        }
//        Map<String, List<DtoProject2Customer>> projectId2CustomerListMap = new HashMap<>();
        if (StringUtil.isNotEmpty(newDatas)) {
            List<String> projectIdList = newDatas.stream().map(DtoProject::getId).collect(Collectors.toList());
//            List<DtoProject2Customer> project2CustomerList = project2CustomerRepository.findByProjectIdIn(projectIdList);
//            projectId2CustomerListMap = project2CustomerList.stream().collect(Collectors.groupingBy(DtoProject2Customer::getProjectId));
            exploreList = exploreRepository.findByProjectIdIn(projectIdList);
            projectContractList = projectContractRepository.findByProjectIdIn(projectIdList);
        }
        for (DtoProject project : newDatas) {
            Optional<DtoProjectType> projectTypeOptional = projectTypes.parallelStream().filter(p -> project.getProjectTypeId().equals(p.getId()))
                    .findFirst();
            projectTypeOptional.ifPresent(p -> {
                project.setWorkflowId(p.getWorkflowId());
                project.setProjectTypeName(StringUtil.isNotEmpty(p.getName()) ? p.getName() : "");
            });
            List<String> sampleTypeIds2Project = samples.stream().filter(s -> s.getProjectId().equals(project.getId())).map(DtoSample::getSampleTypeId).distinct().collect(toList());
            project.setSampleTypeNames(sampleTypes.stream().filter(s -> sampleTypeIds2Project.contains(s.getId())).map(DtoSampleType::getTypeName).collect(Collectors.toList()));

            project.setInceptPersonName(personMap.getOrDefault(project.getInceptPersonId(), ""));
            project.setLeaderName(personMap.getOrDefault(project.getLeaderId(), ""));
            project.setReportMakerName(personMap.getOrDefault(project.getReportMakerId(), ""));
            project.setLastNewOpinion(lastNewOpinionMap.getOrDefault(project.getId(), ""));
            //判断当前项目是否已踏勘
            if (StringUtil.isNotEmpty(exploreList)) {
                Long exploreRecordCount = exploreList.parallelStream().filter(p -> p.getProjectId().equals(project.getId()))
                        .count();
                if (exploreRecordCount != null && exploreRecordCount > 0) {
                    project.setExploreIndicator(Boolean.TRUE);
                }
            }
            if (reportList.stream().anyMatch(p -> project.getId().equals(p.getProjectId())
                    && EnumReportChangeStatus.已变更.getValue().equals(p.getDataChangeStatus()))) {
                project.setDataChangeStatus(EnumProjectChangeStatus.已变更.getValue());
            }
//            //多企业的情况下，企业id，企业名称需要从关联表中获取
//            if (project.getIsMultiEnterprise()) {
//                List<DtoProject2Customer> project2CustomerList = projectId2CustomerListMap.get(project.getId());
//                if (StringUtil.isEmpty(project.getCustomerId()) || UUIDHelper.GUID_EMPTY.equals(project.getCustomerId())) {
//                    if (StringUtil.isNotEmpty(project2CustomerList)) {
//                        List<String> customerIdList = project2CustomerList.stream().map(DtoProject2Customer::getCustomerId).distinct().collect(Collectors.toList());
//                        project.setCustomerId(String.join(",", customerIdList));
//                    }
//                }
//                if (StringUtil.isEmpty(project.getCustomerName()) || UUIDHelper.GUID_EMPTY.equals(project.getCustomerName())) {
//                    if (StringUtil.isNotEmpty(project2CustomerList)) {
//                        List<String> customerNameList = project2CustomerList.stream().map(DtoProject2Customer::getCustomerName).distinct().collect(Collectors.toList());
//                        project.setCustomerName(String.join(",", customerNameList));
//                    }
//                }
//            }
            List<DtoProject> subProList = subProjectList.stream().filter(p -> project.getId().equals(p.getParentId())).collect(Collectors.toList());
            project.setSubProjectCount(subProList.size());

            List<DtoProject> subEndProList = subProList.stream().filter(p -> p.getStatus().equals(EnumProjectStatus.已办结.name())).collect(Collectors.toList());
            project.setSubEndProjectCount(subEndProList.size());
            //获取已采样子项目数量
            int subStartProjectCnt = getSubStartProjectCnt(subProList);
            project.setSubStartProjectCount(subStartProjectCnt);
        }
        //项目进度页面处理相关显示
        if (projectCriteria.getModule().equals(EnumLIM.EnumProjectModule.项目进度.getCode())) {
            checkProgressStepSampleReport(newDatas);
        }
        //任务办结页面查询时，需要获取电子报告未上传的数量
        if (projectCriteria.getModule().equals(EnumLIM.EnumProjectModule.任务办结.getCode())) {
            getNotUploadCnt(newDatas);
        }
        if (projectCriteria.getModule().equals(EnumLIM.EnumProjectModule.报告管理.getCode()) || projectCriteria.getModule().equals(EnumLIM.EnumProjectModule.项目进度.getCode())) {
            List<DtoProjectContract> finalProjectContractList = projectContractList;
            newDatas.forEach(project -> {
                Map<String, Long> reports2Project = reports.stream().filter(r -> r.getProjectId().equals(project.getId())).collect(Collectors.groupingBy(DtoReport::getStatus, Collectors.counting()));
                Arrays.stream(EnumReportState.values()).map(EnumReportState::name).forEach(state -> {
                    if (!reports2Project.containsKey(state)) {
                        reports2Project.put(state, 0L);
                    }
                });
                project.setReportStateMap(reports2Project);
                finalProjectContractList.stream().filter(c -> project.getId().equals(c.getProjectId())).findFirst().ifPresent(c -> project.setProjectContract(c));
            });
        }
        // 订单信息中项目列表，按照-后流水号正序排序
        if (StringUtil.isNotEmpty(projectCriteria.getOrderId()) && !UUIDHelper.GUID_EMPTY.equals(projectCriteria.getOrderId())) {
            newDatas.sort(Comparator.comparing(p -> {
                String part = "00";
                if (p.getProjectCode().contains("-")) {
                    String[] split = p.getProjectCode().split("-");
                    part = split.length > 0 ? split[1] : "00";
                }
                // 将字符串转换为整数进行比较
                return Integer.parseInt(part);
            }));
        }
        pb.setData(newDatas);
        //设置项目的采样时间（项目下样品的最新采样时间）
        setLastSamplingTimeForProject(newDatas);
    }

    /**
     * 项目进度模块统计项目部分流程完成情况/样品采样情况/报告完成性情况
     *
     * @param newDatas 原始数据
     */
    private void checkProgressStepSampleReport(List<DtoProject> newDatas) {
        if (StringUtil.isNotEmpty(newDatas)) {
            List<String> projectIds = newDatas.stream().map(DtoProject::getId).collect(Collectors.toList());
            // 任务登记/任务审核/任务下达/方案编制/方案审核/办结中(放在“报告已签发”后面)
            List<DtoStatusForProject> statusForProjectList = statusForProjectRepository.findByProjectIdIn(projectIds);
            List<EnumLIM.EnumProjectModule> codes = Stream.of(EnumLIM.EnumProjectModule.项目登记, EnumLIM.EnumProjectModule.技术审核,
                    EnumLIM.EnumProjectModule.项目下达, EnumLIM.EnumProjectModule.方案编制, EnumLIM.EnumProjectModule.方案审核,
                    EnumLIM.EnumProjectModule.任务办结).sorted(Comparator.comparing(EnumLIM.EnumProjectModule::getSortNum)).collect(Collectors.toList());
            // 采样状态 ：a/b。a代表已经生成样品编号的样品数量，b代表样品总数
            List<DtoSample> sampleList = sampleRepository.findByProjectIdIn(projectIds).stream()
                    .filter(v -> EnumSampleCategory.原样.getValue().equals(v.getSampleCategory())).collect(toList());
            for (DtoProject project : newDatas) {
                List<DtoStatusForProject> projectStatusList = statusForProjectList.stream().filter(v -> project.getId().equals(v.getProjectId())).collect(toList());
                Map<String, Object> stepMap = new HashMap<>();
                DtoStatusForProject localTask = projectStatusList.stream().filter(v -> EnumLIM.EnumProjectModule.现场任务.getCode().equals(v.getModule()))
                        .findFirst().orElse(null);
                if (localTask != null) {
                    if (localTask.getStatus() == 1) {
                        stepMap.put(EnumLIM.EnumProjectModule.项目登记.name(), false);
                        stepMap.put(EnumLIM.EnumProjectModule.技术审核.name(), null);
                        stepMap.put(EnumLIM.EnumProjectModule.项目下达.name(), null);
                        stepMap.put(EnumLIM.EnumProjectModule.方案编制.name(), null);
                        stepMap.put(EnumLIM.EnumProjectModule.方案审核.name(), null);
                        stepMap.put(EnumLIM.EnumProjectModule.任务办结.name(), null);
                    } else {
                        stepMap.put(EnumLIM.EnumProjectModule.项目登记.name(), true);
                        stepMap.put(EnumLIM.EnumProjectModule.技术审核.name(), true);
                        stepMap.put(EnumLIM.EnumProjectModule.项目下达.name(), true);
                        stepMap.put(EnumLIM.EnumProjectModule.方案编制.name(), true);
                        stepMap.put(EnumLIM.EnumProjectModule.方案审核.name(), true);
                        DtoStatusForProject localTaskEnd = projectStatusList.stream().filter(v -> EnumLIM.EnumProjectModule.任务办结.getCode().equals(v.getModule()))
                                .findFirst().orElse(null);
                        if (localTaskEnd != null && EnumReportStatus.已完成.getValue().equals(project.getReportStatus())) {
                            stepMap.put(EnumLIM.EnumProjectModule.任务办结.name(), localTaskEnd.getStatus() == 2);
                        } else {
                            stepMap.put(EnumLIM.EnumProjectModule.任务办结.name(), null);
                            DtoStatusForProject statusForProject = projectStatusList.stream().filter(v -> EnumLIM.EnumProjectModule.任务办结.getCode().equals(v.getModule())
                                    && project.getId().equals(v.getProjectId())).findFirst().orElse(null);
                            if (statusForProject != null) {
                                boolean isFinish = statusForProject.getStatus() == 2;
                                if (isFinish) {
                                    stepMap.put(EnumLIM.EnumProjectModule.任务办结.name(), true);
                                }
                            }
                        }
                    }
                } else {
                    Integer sortNum = 0;
                    for (EnumLIM.EnumProjectModule module : codes) {
                        DtoStatusForProject statusForProject = projectStatusList.stream().filter(v -> module.getCode().equals(v.getModule())
                                && project.getId().equals(v.getProjectId())).findFirst().orElse(null);
                        //现场任务特殊处理
                        if (statusForProject != null) {
                            boolean isFinish = statusForProject.getStatus() == 2;
                            stepMap.put(module.name(), isFinish);
                            if (!isFinish && EnumReportStatus.已完成.getValue().equals(project.getReportStatus())) {
                                sortNum = module.getSortNum();
                            } else {
                                if (EnumLIM.EnumProjectModule.任务办结.name().equals(module.name())) {
                                    sortNum = module.getSortNum();
                                    stepMap.put(module.name(), null);
                                    if (isFinish) {
                                        stepMap.put(module.name(), true);
                                    }
                                }
                            }
                        } else {
                            stepMap.put(module.name(), null);
                        }
                    }
                    //当样品在某一个环节时，前面环节均显示标绿
                    Integer finalSortNum = sortNum;
                    codes.stream().filter(v -> v.getSortNum() < finalSortNum).map(EnumLIM.EnumProjectModule::name).forEach(v -> stepMap.put(v, true));
                }
                project.setStepMap(stepMap);
                List<DtoSample> projectSamples = sampleList.stream().filter(v -> project.getId().equals(v.getProjectId())).collect(toList());
                List<DtoSample> projectSampleWithCode = projectSamples.stream().filter(v -> StringUtil.isNotEmpty(v.getCode())).collect(toList());
                project.setSamplingRate(projectSampleWithCode.size() + "/" + projectSamples.size());
            }
        }
    }

    /**
     * 获取已采样子项目数量
     *
     * @param subProList 子项目列表
     * @return 已采样子项目数量
     */
    private int getSubStartProjectCnt(List<DtoProject> subProList) {
        List<String> subProjectIdList = subProList.stream().map(DtoProject::getId).collect(toList());
        //找到项目下所有的样品
        List<DtoSample> sampleForSubProject = StringUtil.isNotEmpty(subProjectIdList) ? sampleRepository.findByProjectIdIn(subProjectIdList) : new ArrayList<>();
        sampleService.addSampleByAssId(sampleForSubProject, sampleForSubProject);
        Map<String, List<DtoSample>> sampleMap = sampleForSubProject.stream().collect(Collectors.groupingBy(DtoSample::getProjectId));
        List<DtoSample> sampleWithoutProId = sampleMap.getOrDefault(UUIDHelper.GUID_EMPTY, new ArrayList<>());
        sampleWithoutProId.addAll(sampleMap.getOrDefault("", new ArrayList<>()));
        sampleMap.remove(UUIDHelper.GUID_EMPTY);
        sampleMap.remove("");
        if (StringUtil.isNotEmpty(sampleWithoutProId)) {
            for (DtoSample sample : sampleWithoutProId) {
                String projectId = getProIdByAssId(sample, sampleForSubProject);
                if (StringUtil.isNotEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
                    if (!sampleMap.containsKey(projectId)) {
                        sampleMap.put(projectId, new ArrayList<>());
                    }
                    sampleMap.get(projectId).add(sample);
                }
            }
        }
        int cnt = 0;
        for (String projectId : subProjectIdList) {
            if (sampleMap.containsKey(projectId)) {
                long sampleCntWithCode = sampleMap.get(projectId).stream().filter(p -> StringUtil.isNotEmpty(p.getCode())).count();
                //如果子项目中的所有样品中有任意一个样品的编号不为空，则认定该项目采样完成
                if (sampleCntWithCode > 0) {
                    cnt++;
                }
            }
        }
        return cnt;
    }

    /**
     * 获取样品对应的项目id（质控样通过关联样进行查询）
     *
     * @param sample              样品对象
     * @param sampleForSubProject 多个项目下的所有样品
     * @return 项目id
     */
    private String getProIdByAssId(DtoSample sample, List<DtoSample> sampleForSubProject) {
        int sampleSize = sampleForSubProject.size();
        List<String> dupIdList = new ArrayList<>();
        dupIdList.add(sample.getId());
        Map<String, DtoSample> sampleMap = sampleForSubProject.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
        String projectId = sample.getProjectId();
        while ((dupIdList.size() < sampleSize) && (StringUtil.isEmpty(projectId) || UUIDHelper.GUID_EMPTY.equals(projectId))
                && sampleMap.containsKey(sample.getAssociateSampleId())) {
            DtoSample loopSample = sampleMap.get(sample.getAssociateSampleId());
            if (!dupIdList.contains(loopSample.getId())) {
                projectId = loopSample.getProjectId();
                dupIdList.add(loopSample.getId());
            } else {
                break;
            }
        }
        return projectId;
    }

    /**
     * 设置项目采样日期（项目下所有样品的最近的采样日期）
     *
     * @param projectList 项目列表
     */
    private void setLastSamplingTimeForProject(List<DtoProject> projectList) {
        if (StringUtil.isNotEmpty(projectList)) {
            List<String> projectIdList = projectList.stream().map(DtoProject::getId).collect(toList());
            List<DtoSample> sampleList = sampleRepository.findByProjectIdIn(projectIdList);
            //获取所有与项目关联的样品
            sampleService.addAssSample(sampleList);
            Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
            Map<String, List<DtoSample>> sampleMapForProject = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getProjectId));
            //找出没有项目id的样品（质控样）
            List<DtoSample> qcSampleList = sampleMapForProject.getOrDefault(UUIDHelper.GUID_EMPTY, new ArrayList<>());
            if (StringUtil.isNotEmpty(qcSampleList)) {
                for (DtoSample qcSample : qcSampleList) {
                    DtoSample yySample = getYySample(qcSample, sampleMap);
                    if (StringUtil.isNotNull(yySample)) {
                        if (!sampleMapForProject.containsKey(yySample.getProjectId())) {
                            sampleMapForProject.put(yySample.getProjectId(), new ArrayList<>());
                        }
                        sampleMapForProject.get(yySample.getProjectId()).add(qcSample);
                    }
                }
            }
            //遍历项目，设置最新的采样时间
            for (DtoProject project : projectList) {
                String lastSampleTime = "";
                if (sampleMapForProject.containsKey(project.getId())) {
                    List<DtoSample> smpList = sampleMapForProject.get(project.getId());
                    List<Date> sampleTimeList = smpList.stream().map(DtoSample::getSamplingTimeBegin).filter(StringUtil::isNotNull).distinct().collect(toList());
                    if (StringUtil.isNotEmpty(sampleTimeList)) {
                        String lastDateStr = DateUtil.dateToString(Collections.max(sampleTimeList), DateUtil.YEAR);
                        if (StringUtil.isNotEmpty(lastDateStr) && !lastDateStr.contains("1753")) {
                            lastSampleTime = lastDateStr;
                        }
                    }
                }
                project.setSamplingDate(lastSampleTime);
            }
        }
    }

    /**
     * 获取质控样对应的原样
     *
     * @param qcSample  质控样
     * @param sampleMap 样品映射
     * @return 原样
     */
    private DtoSample getYySample(DtoSample qcSample, Map<String, DtoSample> sampleMap) {
        List<String> distinctAssIdList = new ArrayList<>();
        String assId = qcSample.getAssociateSampleId();
        distinctAssIdList.add(assId);
        while (sampleMap.containsKey(assId)) {
            DtoSample temp = sampleMap.get(assId);
            if (StringUtil.isNotEmpty(temp.getProjectId()) && !UUIDHelper.GUID_EMPTY.equals(temp.getProjectId())) {
                return temp;
            }
            assId = temp.getAssociateSampleId();
            if (distinctAssIdList.contains(assId)) {
                break;
            }
            distinctAssIdList.add(assId);
        }
        return null;
    }

    /**
     * 获取电子报告未上传的数量
     *
     * @param projectList 项目列表
     */
    private void getNotUploadCnt(List<DtoProject> projectList) {
        if (StringUtil.isNotEmpty(projectList)) {
            List<String> projectIdList = projectList.stream().map(DtoProject::getId).collect(toList());
            List<DtoReport> reportList = reportRepository.findByProjectIdIn(projectIdList);
            Map<String, List<DtoReport>> reportMap = reportList.stream().collect(Collectors.groupingBy(DtoReport::getProjectId));
            List<DtoProject2Report> project2ReportList = project2ReportRepository.findByProjectIdIn(projectIdList);
            project2ReportList = project2ReportList.stream().filter(p -> p.getUploadStatus().equals(0)).collect(toList());
            Map<String, List<DtoProject2Report>> project2ReportMap = project2ReportList.stream().collect(Collectors.groupingBy(DtoProject2Report::getProjectId));
            for (DtoProject project : projectList) {
                int notUploadCnt = 0;
                if (reportMap.containsKey(project.getId())) {
                    List<DtoReport> reportListForProject = reportMap.get(project.getId());
                    List<String> reportIdListForProject = reportListForProject.stream().map(DtoReport::getId).collect(toList());
                    List<DtoProject2Report> project2ReportForProject = project2ReportMap.get(project.getId());
                    if (StringUtil.isNotEmpty(project2ReportForProject)) {
                        notUploadCnt = (int) project2ReportForProject.stream().filter(p -> reportIdListForProject.contains(p.getReportId())).count();
                    }
                }
                project.setNotUploadCount(notUploadCnt);
            }
        }
    }

    /**
     * 根据订单id获取任务集合
     *
     * @param orderId 订单id
     * @return 任务集合
     */
    @Override
    public List<DtoProject> findByOrderId(String orderId) {
        return repository.findByOrderId(orderId);
    }

    /**
     * 获取合同下的项目列表
     *
     * @param contractId 合同id
     * @return 合同下的项目列表
     */
    @Override
    public List<DtoProject> findByContractId(String contractId) {
        List<DtoProject2Contract> p2cList = project2ContractRepository.findByContractId(contractId);
        if (p2cList.size() > 0) {
            List<String> projectIds = p2cList.stream().map(DtoProject2Contract::getProjectId).distinct().collect(Collectors.toList());
            List<DtoProject> projectList = repository.findAll(projectIds);
            List<DtoProjectPlan> planList = projectPlanRepository.findByProjectIdIn(projectIds);
            List<String> leaderIds = planList.stream().map(DtoProjectPlan::getLeaderId).distinct().collect(Collectors.toList());
            List<DtoPerson> personList = leaderIds.size() > 0 ? personService.findAllDeleted(leaderIds) : new ArrayList<>();
            for (DtoProject project : projectList) {
                String leaderId = planList.stream().filter(p -> p.getProjectId().equals(project.getId())).map(DtoProjectPlan::getLeaderId).findFirst().orElse("");
                String leaderName = personList.stream().filter(p -> p.getId().equals(leaderId)).map(DtoPerson::getCName).findFirst().orElse("");
                project.setLeaderName(leaderName);
            }
            projectList.sort(Comparator.comparing(DtoProject::getProjectCode));
            return projectList;
        }
        return new ArrayList<>();
    }

    /**
     * 根据主项目 查询子项目信息
     *
     * @param ids 主项目id
     * @return 项目集合
     */
    @Override
    public List<DtoProject> findByParentIds(Collection<?> ids) {
        return repository.findByParentIdIn(ids);
    }

    /**
     * 获取项目详情
     *
     * @param id 项目id
     * @return 项目
     */
    @Override
    public DtoProject findOne(String id) {
        DtoProject project = repository.findOne(id);
        DtoProjectPlan plan = projectPlanRepository.findByProjectId(id);
        DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());

        project.setWorkflowId(projectType.getWorkflowId());
        project.setProjectTypeName(projectType.getName());
        if (StringUtil.isNotNull(plan)) {
            project.loadFromPlan(plan);
        }
        DtoProject2Contract p2c = project2ContractRepository.findByProjectId(id);
        if (StringUtil.isNotNull(p2c)) {
            DtoContract contract = contractService.findOne(p2c.getContractId());
            if (StringUtil.isNotNull(contract)) {//赋值合同的信息
                project.setContractId(contract.getId());
                project.setContractCode(contract.getContractCode());
                project.setContractName(contract.getContractName());
                project.setTimeBegin(DateUtil.dateToString(contract.getTimeBegin(), DateUtil.YEAR));
                project.setTimeEnd(DateUtil.dateToString(contract.getTimeEnd(), DateUtil.YEAR));
                project.setTotalAmount(contract.getTotalAmount());
                project.setContractStatus(contract.getStatus());
            }
        }
        if (StringUtils.isNotNullAndEmpty(project.getSampleType())) {
            project.setSampleTypeArr(project.getSampleType().split(","));
        }
        try {
            project.setBase64Content(documentService.convertBase64Content(project.getQrCodeUrl()));
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
        if (StringUtil.isNotEmpty(project.getJson())) {
            Map jsonMap = JsonIterator.deserialize(project.getJson(), Map.class);
            if (jsonMap.containsKey("analyzeDetail") && StringUtil.isNotNull(jsonMap.get("analyzeDetail"))) {
                project.setAnalyzeDetail((List<Integer>) jsonMap.get("analyzeDetail"));
            }
        }
        //污染源类型多企业的情况下查询项目方案中已有点位的企业
        List<DtoSampleFolder> folderList = sampleFolderRepository.findByProjectId(project.getId());
        if (StringUtil.isNotEmpty(folderList)) {
            List<String> fixedPointIdList = folderList.stream().filter(p -> StringUtil.isNotEmpty(p.getFixedPointId())
                    && !UUIDHelper.GUID_EMPTY.equals(p.getFixedPointId())).map(DtoSampleFolder::getFixedPointId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(fixedPointIdList)) {
                List<String> enterpriseIdList = fixedpointRepository.findAll(fixedPointIdList).stream().filter(p -> StringUtil.isNotEmpty(p.getEnterpriseId())
                        && !UUIDHelper.GUID_EMPTY.equals(p.getEnterpriseId())).map(DtoFixedpoint::getEnterpriseId).distinct().collect(Collectors.toList());
                project.setEnterpriseIdWithScheme(enterpriseIdList);
            }
        }

        List<String> sampleTypeIds = sampleRepository.findByProjectId(id).stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findAll(sampleTypeIds) : new ArrayList<>();
        project.setSampleTypeIds(StringUtil.isNotEmpty(sampleTypes) ? sampleTypes.stream().map(DtoSampleType::getId).collect(Collectors.toList()) : new ArrayList<>());
        project.setSampleTypeNames(StringUtil.isNotEmpty(sampleTypes) ? sampleTypes.stream().map(DtoSampleType::getTypeName).collect(Collectors.toList()) : new ArrayList<>());
        project.setSampleTypes(sampleTypes);

//        //污染源类型多企业的情况下企业id，企业名称从关联表中获取
//        if (project.getIsMultiEnterprise()) {
//            DtoProject newProject = new DtoProject();
//            BeanUtils.copyProperties(project, newProject);
//            List<DtoProject2Customer> project2CustomerList = project2CustomerRepository.findByProjectId(newProject.getId());
//            if (StringUtil.isEmpty(newProject.getCustomerId()) || UUIDHelper.GUID_EMPTY.equals(newProject.getCustomerId())) {
//                if (StringUtil.isNotEmpty(project2CustomerList)) {
//                    newProject.setCustomerId(String.join(",", project2CustomerList.stream().map(DtoProject2Customer::getCustomerId).distinct().collect(Collectors.toList())));
//                }
//            }
//            if (StringUtil.isEmpty(newProject.getCustomerName()) || UUIDHelper.GUID_EMPTY.equals(newProject.getCustomerName())) {
//                if (StringUtil.isNotEmpty(project2CustomerList)) {
//                    newProject.setCustomerName(String.join(",", project2CustomerList.stream().map(DtoProject2Customer::getCustomerName).distinct().collect(Collectors.toList())));
//                }
//            }
//            return newProject;
//        }
        //处理登记人
        List<DtoPerson> personList = personService.findAll();
        Map<String, List<DtoPerson>> personGroup = personList.stream().collect(Collectors.groupingBy(DtoPerson::getId));
        if (StringUtil.isNotEmpty(project.getInceptPersonId())) {
            List<DtoPerson> inceptPersons = personGroup.get(project.getInceptPersonId());
            if (StringUtil.isNotEmpty(inceptPersons)) {
                Optional<DtoPerson> person = inceptPersons.stream().findFirst();
                person.ifPresent(p -> project.setInceptPersonName(p.getCName()));
            }
        }
        //处理交付方式
        project.setPostMethodKeys(findPostMethodKeys(project));
        List<DtoProjectContract> projectContractList = projectContractRepository.findByProjectId(id);
        if (StringUtil.isNotEmpty(projectContractList)) {
            DtoProjectContract projectContract = projectContractList.get(0);
            if (StringUtil.isNotEmpty(projectContract.getTaskLocation())) {
                DtoArea area = areaService.findById(projectContract.getTaskLocation());
                if (area != null) {
                    projectContract.setAddressName(area.getAreaName());
                }
            }
            project.setProjectContract(projectContract);
        }
        return project;
    }

    /**
     * 获取送样项目详情
     *
     * @param id 项目id
     * @return 送样项目
     */
    @Override
    public DtoReceiveSampleRecordTemp findOutsideSendSample(String id) {
        DtoProject project = this.findOne(id);
        Map<String, Object> extendMap = new HashMap<>();
        extendMap.put("inceptPersonName", StringUtil.isNotEmpty(project.getInceptPersonName()) ? project.getInceptPersonName() : "");
        DtoReceiveSampleRecord record = receiveSampleRecordService.findOutsideSendSampleById(id);
        DtoReceiveSampleRecordTemp temp = new DtoReceiveSampleRecordTemp();
        temp.loadFromProject(project);
        if (StringUtil.isNotNull(record)) {
            List<String> sampleTypeIds = sampleRepository.findByProjectId(id).stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findAll(sampleTypeIds) : new ArrayList<>();
            temp.setSampleTypeIds(StringUtil.isNotEmpty(sampleTypes) ? sampleTypes.stream().map(DtoSampleType::getId).collect(Collectors.toList()) : new ArrayList<>());
            temp.setSampleTypeNames(StringUtil.isNotEmpty(sampleTypes) ? sampleTypes.stream().map(DtoSampleType::getTypeName).collect(Collectors.toList()) : new ArrayList<>());
            temp.setRecordCode(record.getRecordCode());
            temp.setId(record.getId());
            temp.setReceiveType(record.getReceiveType());
            temp.setSamplingTime(record.getSamplingTime());
            temp.setSenderId(record.getSenderId());
            temp.setSenderName(record.getSenderName());
            temp.setSendTime(record.getSendTime());
            temp.setSamplingPersonIds(record.getSamplingPersonIds());
            temp.setCarIds(record.getCarIds());
            temp.setReceiveRemark(record.getRemark());
        } else {
            temp.setId("");
        }
        DynamicBean dynamicBean = ReflectUtil.getDynamicBean(temp, extendMap);
        temp = (DtoReceiveSampleRecordTemp) ReflectUtil.getTarget(dynamicBean);
        List<DtoProjectContract> projectContractList = projectContractRepository.findByProjectId(id);
        if (StringUtil.isNotEmpty(projectContractList)) {
            temp.setProjectContract(projectContractList.get(0));
        }
        return temp;
    }

    @Transactional
    @Override
    public DtoProject save(DtoProject dto) {

        dto.setInputTime(new Date());
        if (StringUtil.isEmpty(dto.getProjectCode())) {
            dto.setProjectCode(judgeCreateProjectCode(dto));
        }
        //当项目编号可编辑时，新增项目要做编号唯一性校验
        if (projectCodeEditIndicator()) {
            if (StringUtil.isNotEmpty(dto.getProjectCode())) {
                Integer count = repository.countByProjectCode(dto.getProjectCode());
                if (count > 0) {
                    throw new BaseException("项目编号已经存在，请更换项目编号");
                }
            }
        }

        if (StringUtil.isNotNull(dto.getIsMultiEnterprise()) && dto.getIsMultiEnterprise()) {
            dto.setCustomerId(UUIDHelper.GUID_EMPTY);
            dto.setCustomerName("");
        }

        dto.setStatus(EnumProjectStatus.项目登记中.toString());

        //写入json信息
        DtoProjectJson jsonEntity = new DtoProjectJson();

        if (StringUtils.isNotNullAndEmpty(dto.getContractId())) {
            DtoProject2Contract p2c = new DtoProject2Contract();
            p2c.setContractId(dto.getContractId());
            p2c.setProjectId(dto.getId());
            project2ContractService.save(p2c);

            DtoContract contract = contractService.findOne(dto.getContractId());
            if (StringUtil.isNotNull(contract)) {
                jsonEntity.setContractCode(contract.getContractCode());
                jsonEntity.setCollectionStatus(contract.getCollectionStatus());
                //依次为已收款，坏账，总额
                jsonEntity.setCollectionDetail(new BigDecimal[]{contract.getArrivalAmount(), contract.getBadAmount(), contract.getTotalAmount()});
            }
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            dto.setJson(objectMapper.writeValueAsString(jsonEntity));
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }

        BufferedImage image = QrCodeUtil.createImageCode(dto.getProjectCode(), "UTF-8", 100);
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("projectCode", dto.getProjectCode());
            String path = "/" + documentService.getDocumentPathByPlaceholder("projectQRCode", map);
            String fileName = String.format("%s二维码.png", dto.getProjectCode());
            QrCodeUtil.saveImage(filePathConfig.getFilePath() + path, image, fileName);
            dto.setQrCodeUrl(path + "/" + fileName);
            dto.setBase64Content(documentService.convertBase64Content(dto.getQrCodeUrl()));
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }

        DtoProjectType projectType = projectTypeService.findOne(dto.getProjectTypeId());
        if (StringUtil.isNotNull(projectType) && StringUtils.isNotNullAndEmpty(projectType.getWorkflowId())) {
            workflowService.createInstance(projectType.getWorkflowId(), dto.getId());
        }
        DtoProjectPlan plan = new DtoProjectPlan();
        plan.loadFromProject(dto);
        projectPlanService.save(plan);

        String projectRegisterPage = projectTypeService.getConfigValue(dto.getProjectTypeId(), "projectRegisterPage");
        if (EnumProjectType.现场类.getValue().equals(projectRegisterPage)) {
            statusForProjectService.createStatus(dto.getId(), EnumLIM.EnumProjectModule.现场任务.getCode());
        } else {
            statusForProjectService.createStatus(dto.getId(), EnumLIM.EnumProjectModule.项目登记.getCode());
        }

        statusForProjectService.createStatus(dto.getId(), EnumLIM.EnumProjectModule.项目进度.getCode());
        super.save(dto);
        newLogService.createProjectInfoUpdateLog(Collections.singletonList(dto.getId()), "", EnumLogOperateType.增加项目.toString());
        //区分例行和普通项目
        String module = EnumLIM.EnumHomeTaskModule.项目登记.getValue();
        String configValue = projectTypeService.getConfigValue(dto.getProjectTypeId(), "LIM_ProjectTypeCode_IND");
        if ("HJ".equals(configValue) || "WR".equals(configValue)) {
            module = EnumLIM.EnumHomeTaskModule.例行登记.getValue();
        }
        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId()
                , module, "");
        dto.setWorkflowId(projectType.getWorkflowId());
        //环保企业通项目信息推送
        environmentEnterpriseService.pushProjectData(Collections.singletonList(dto), EnumProjectStatus.项目登记中.name());
        //项目推送信息保存
        if (StringUtil.isNotNull(dto.getProjectContract())) {
            dto.getProjectContract().setProjectId(dto.getId());
            projectContractRepository.save(dto.getProjectContract());
        }
        return dto;
    }

    @Transactional
    @Override
    public void saveBatchProject(List<DtoProject> projectList) {
        this.saveProjectAll(projectList, false);
    }

    private void saveProjectAll(List<DtoProject> dtoList, Boolean isOrder) {
        List<DtoProject2Contract> project2ContractList = new ArrayList<>();
        List<DtoProjectPlan> planList = new ArrayList<>();
        List<DtoStatusForProject> statusForProjectList = new ArrayList<>();
        List<String> contractIds = dtoList.stream().map(DtoProject::getContractId)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, List<DtoContract>> contractList = new HashMap<>();
        if (contractIds.size() > 0) {
            contractList = contractService.findAll(contractIds).stream().collect(groupingBy(DtoContract::getId));
        }
        String projectTypeId = dtoList.stream().map(DtoProject::getProjectTypeId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
        DtoProjectType projectType = projectTypeService.findOne(projectTypeId);
        //添加状态表数据
        String projectRegisterPage = projectTypeService.getConfigValue(projectTypeId, "projectRegisterPage");
        Integer projectNumber = 0;
        for (DtoProject dto : dtoList) {
            dto.setInputTime(new Date());
            if (isOrder) {
                if (StringUtil.isEmpty(dto.getProjectCode())) {
                    dto.setProjectCode(this.judgeCreateProjectCode(dto, projectNumber));
                    projectNumber++;
                }
                //当项目编号可编辑时，新增项目要做编号唯一性校验
                if (projectCodeEditIndicator()) {
                    if (StringUtil.isNotEmpty(dto.getProjectCode())) {
                        Integer count = repository.countByProjectCode(dto.getProjectCode());
                        if (count > 0) {
                            throw new BaseException("项目编号已经存在，请更换项目编号");
                        }
                    }
                }
            }
            dto.setStatus(EnumProjectStatus.项目登记中.toString());
            //写入json信息
            DtoProjectJson jsonEntity = new DtoProjectJson();
            if (StringUtils.isNotNullAndEmpty(dto.getContractId())) {
                DtoProject2Contract p2c = new DtoProject2Contract();
                p2c.setContractId(dto.getContractId());
                p2c.setProjectId(dto.getId());
                project2ContractList.add(p2c);

                if (contractList.size() > 0) {
                    List<DtoContract> conList = contractList.get(dto.getContractId());
                    if (StringUtil.isNotNull(conList) && conList.size() > 0) {
                        DtoContract contract = conList.get(0);
                        jsonEntity.setContractCode(contract.getContractCode());
                        jsonEntity.setCollectionStatus(contract.getCollectionStatus());
                        //依次为已收款，坏账，总额
                        jsonEntity.setCollectionDetail(new BigDecimal[]{contract.getArrivalAmount(), contract.getBadAmount(), contract.getTotalAmount()});
                    }
                }
            }
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                dto.setJson(objectMapper.writeValueAsString(jsonEntity));
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
            }
            BufferedImage image = QrCodeUtil.createImageCode(dto.getProjectCode(), "UTF-8", 100);
            try {
                Map<String, Object> map = new HashMap<>();
                map.put("projectCode", dto.getProjectCode());
                String path = "/" + documentService.getDocumentPathByPlaceholder("projectQRCode", map);
                String fileName = String.format("%s二维码.png", dto.getProjectCode());
                QrCodeUtil.saveImage(filePathConfig.getFilePath() + path, image, fileName);
                dto.setQrCodeUrl(path + "/" + fileName);
                dto.setBase64Content(documentService.convertBase64Content(dto.getQrCodeUrl()));
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
            }
            dto.setWorkflowId(projectType.getWorkflowId());
            DtoProjectPlan plan = new DtoProjectPlan();
            plan.loadFromProject(dto);
            planList.add(plan);
            //创建工作流
            if (StringUtil.isNotNull(projectType) && StringUtils.isNotNullAndEmpty(projectType.getWorkflowId())) {
                workflowService.createInstance(projectType.getWorkflowId(), dto.getId());
            }
            if (EnumProjectType.现场类.getValue().equals(projectRegisterPage)) {
                statusForProjectList.add(statusForProjectService.statusForProject(dto.getId(), EnumLIM.EnumProjectModule.现场任务.getCode()));
            } else {
                statusForProjectList.add(statusForProjectService.statusForProject(dto.getId(), EnumLIM.EnumProjectModule.项目登记.getCode()));
            }
            statusForProjectList.add(statusForProjectService.statusForProject(dto.getId(), EnumLIM.EnumProjectModule.项目进度.getCode()));
        }
        //新增项目
        if (dtoList.size() > 0) {
            repository.save(dtoList);
        }
        //新增项目计划
        if (planList.size() > 0) {
            projectPlanRepository.save(planList);
        }
        //新增状态
        if (statusForProjectList.size() > 0) {
            statusForProjectRepository.save(statusForProjectList);
        }
        //记录日志
        newLogService.createProjectInfoUpdateLog(dtoList.stream().map(DtoProject::getId).collect(toList()), "", EnumLogOperateType.增加项目.toString());
        //区分例行和普通项目
        String module = EnumLIM.EnumHomeTaskModule.项目登记.getValue();
        String configValue = projectTypeService.getConfigValue(projectTypeId, "LIM_ProjectTypeCode_IND");
        if ("HJ".equals(configValue) || "WR".equals(configValue)) {
            module = EnumLIM.EnumHomeTaskModule.例行登记.getValue();
        }
        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId(), module, "");
    }

    /**
     * 获取历史内容（监测目的）
     *
     * @return 监测目的
     */
    @Override
    public List<String> findHistoryMsg() {
        List<String> monitoringPurpose = new ArrayList<>();
        List<DtoProject> projectList = repository.findAll();
        if (StringUtil.isNotEmpty(projectList)) {
            monitoringPurpose = projectList.stream().map(DtoProject::getMonitorPurp).filter(StringUtil::isNotEmpty).distinct().collect(toList());
        }
        return monitoringPurpose;
    }

    /**
     * 新增分包项目
     *
     * @param dto 项目
     * @return
     */
    @Transactional
    @Override
    public DtoProject createSubcontractedProject(DtoProject dto) {
        dto.setInputTime(new Date());
        if (StringUtil.isEmpty(dto.getProjectCode())) {
            //项目编号生成 从inputTime 改为 inceptTime
            String projectCode = this.createProjectCode(dto.getProjectTypeId(), dto.getInceptTime());
            dto.setProjectCode(projectCode);
        }

        //项目直接从开展中开始，因为如果是项目登记中则编制方案处无法显示数据
        dto.setStatus(EnumProjectStatus.开展中.toString());

        BufferedImage image = QrCodeUtil.createImageCode(dto.getProjectCode(), "UTF-8", 100);
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("projectCode", dto.getProjectCode());
            String path = "/" + documentService.getDocumentPathByPlaceholder("projectQRCode", map);
            String fileName = String.format("%s二维码.png", dto.getProjectCode());
            QrCodeUtil.saveImage(filePathConfig.getFilePath() + path, image, fileName);
            dto.setQrCodeUrl(path + "/" + fileName);
            dto.setBase64Content(documentService.convertBase64Content(dto.getQrCodeUrl()));
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }

        // 改为前端选择项目类型
//        List<DtoProjectType> projectTypes = projectTypeService.findByTypeCode("FB");
//        if (StringUtil.isEmpty(projectTypes)) {
//            throw new BaseException("未配置分包项目的项目类型！");
//        }
//        DtoProjectType projectType = projectTypes.get(0);
//        dto.setProjectTypeId(projectType.getId());

        if (StringUtil.isNull(dto.getJson())) {
            DtoProjectJson jsonEntity = new DtoProjectJson();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                dto.setJson(objectMapper.writeValueAsString(jsonEntity));
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
            }
        }

        DtoProjectPlan plan = new DtoProjectPlan();
        plan.loadFromProject(dto);
        projectPlanService.save(plan);

        statusForProjectService.createStatus(dto.getId(), EnumLIM.EnumProjectModule.报告管理.getCode());

        statusForProjectService.createStatus(dto.getId(), EnumLIM.EnumProjectModule.项目进度.getCode());
        super.save(dto);
        newLogService.createProjectInfoUpdateLog(Collections.singletonList(dto.getId()), "", EnumLogOperateType.增加项目.toString());
        return dto;
    }

    /**
     * 更新分包项目
     *
     * @param dto 项目
     * @return
     */
    @Transactional
    @Override
    public DtoProject updateSubcontractedProject(DtoProject dto) {
        dto.setSampleTypeArr(dto.getSampleTypeArr());
        DtoProjectPlan plan = projectPlanRepository.findByProjectId(dto.getId());
        DtoProject project = repository.findOne(dto.getId());
        Map<String, Map<String, Object>> planChange = proService.getCompare(plan, dto);
        Map<String, Map<String, Object>> projectChange = proService.getCompare(project, dto);
        String projectRegisterPage = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        List<String> contents = this.getChangeContent(projectChange, EnumProjectField.项目名称, EnumProjectField.项目等级, EnumProjectField.登记日期,
                EnumProjectField.登记人员, EnumProjectField.要求报告数, EnumProjectField.是否着重关注, EnumProjectField.受检方, EnumProjectField.受检方联系人,
                EnumProjectField.受检方联系方式, EnumProjectField.受检方地址, EnumProjectField.监测目的, EnumProjectField.监测方式, EnumProjectField.保存条件,
                EnumProjectField.交付方式, EnumProjectField.监测要求及说明, EnumProjectField.其他说明, EnumProjectField.监测类型);
        if (projectRegisterPage.equals(EnumProjectType.委托类.getValue()) || EnumProjectType.全流程.getValue().equals(projectRegisterPage)) {
            contents.addAll(this.getChangeContent(projectChange, EnumProjectField.委托方, EnumProjectField.委托方联系人,
                    EnumProjectField.委托方联系方式, EnumProjectField.委托方传真, EnumProjectField.委托方邮箱, EnumProjectField.委托方邮政编码, EnumProjectField.委托方地址));
        } else if (projectRegisterPage.equals(EnumProjectType.送样类.getValue())) {
            contents.addAll(this.getChangeContent(projectChange, EnumProjectField.送样方, EnumProjectField.送样方联系人,
                    EnumProjectField.送样方联系方式, EnumProjectField.送样方传真, EnumProjectField.送样方邮箱, EnumProjectField.送样方邮政编码, EnumProjectField.送样方地址));
        }
        contents.addAll(this.getChangeContent(planChange, EnumProjectField.项目负责人, EnumProjectField.编制报告人, EnumProjectField.要求完成日期,
                EnumProjectField.要求报告日期, EnumProjectField.是否提醒, EnumProjectField.提前提醒天数));

        plan.loadFromProject(dto);
        projectPlanService.update(plan);

        super.update(dto);
        if (contents.size() > 0) {
            newLogService.createProjectInfoUpdateLog(Collections.singletonList(dto.getId()), String.join(";", contents), EnumLogOperateType.修改项目.toString());
        }
        return dto;
    }


    /**
     * 新增送样项目
     *
     * @param dto 实体
     * @return 送样项目
     */
    @Transactional
    @Override
    public DtoReceiveSampleRecordTemp saveOutsideSendSample(DtoReceiveSampleRecordTemp dto) {
        DtoProject project = new DtoProject();
        project.loadFromReceiveTemp(dto);
        loadFromReceiveTempInd(project, dto);
        loadInspectedData(project, dto);
        project = this.save(project);
        dto.setQrCodeUrl(project.getQrCodeUrl());
        dto.setBase64Content(project.getBase64Content());
        dto.setProjectCode(project.getProjectCode());
        dto.setProjectId(project.getId());

        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
        record.loadFromTemp(dto);
        record = receiveSampleRecordService.save(record);
        dto.setId(record.getId());
        dto.setRecordCode(record.getRecordCode());
        if (StringUtil.isNotNull(dto.getProjectContract())) {
            projectContractRepository.save(dto.getProjectContract());
        }
        return dto;
    }

    @Override
    @Transactional
    public void updateOutReceiveRecord(DtoReceiveSampleRecordTemp dto) {
        if (StringUtil.isNotEmpty(dto.getProjectId()) &&
                dto.getReceiveType().equals(EnumPRO.EnumReceiveType.外部送样.getValue())) {
            DtoReceiveSampleRecord record = receiveSampleRecordService.findOutsideSendSampleById(dto.getProjectId());
            if (null == record) {
                DtoProject project = repository.findOne(dto.getProjectId());
                // 送样单id为空时重新创建外部送样单
                record = new DtoReceiveSampleRecord();
                record.loadFromTemp(dto);
                record.setReceiveType(EnumPRO.EnumReceiveType.外部送样.getValue());
                record.setSenderName(project.getExtendStr1());
                record.setSendTime(project.getExtendDate2());
                record.setSamplingTime(project.getExtendDate1());
                record = receiveSampleRecordService.save(record);
                dto.setId(record.getId());
                EnumProjectStatus projectStatus = EnumProjectStatus.getByName(project.getStatus());
                // 创建外部送样单状态-->样品交接
                if (projectStatus.getValue() >= EnumProjectStatus.开展中.getValue()) {
                    receiveSampleRecordService.createOutsideStatus(project.getId());
                }
            }
        }
    }

    @Override
    @Transactional
    public void saveBatchProject(List<DtoProject> projectList, Boolean isSampling, Map<String, List<DtoQuotationDetail>> projectDetailMap) {
        this.saveProjectAll(projectList, true);
        List<Future<String>> asyncDetailList = new ArrayList<>();
        List<DtoQuotationDetail> allDetailList = new ArrayList<>();
        for (String projectId : projectDetailMap.keySet()) {
            allDetailList.addAll(projectDetailMap.get(projectId));
        }
        allDetailList = allDetailList.stream().distinct().collect(toList());
        List<String> detailIds = allDetailList.stream().map(DtoQuotationDetail::getId).collect(toList());
        //获取所有的测试项目id
        List<String> testIds = allDetailList.stream().filter(p -> !p.getIsTotal()).map(DtoQuotationDetail::getTestId).collect(Collectors.toList());
        List<DtoQuotationDetail2Test> quotationDetail2TestList = quotationDetail2TestRepository.findByDetailIdIn(detailIds);
        List<String> parentTestIds = quotationDetail2TestList.stream().map(DtoQuotationDetail2Test::getTestId).distinct().collect(Collectors.toList());
        testIds.addAll(parentTestIds);
        testIds = testIds.stream().distinct().collect(Collectors.toList());
        List<DtoTest> testList = new ArrayList<>();
        List<DtoTestExpand> testExpandList = new ArrayList<>();
        List<DtoPerson2Test> p2tList = new ArrayList<>();
        List<String> dimensionIds = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findRedisByIds(testIds);
            // 根据分析方法状态，剔除停用、废止的方法对应的测试项目
            testService.removeByMethodStatus(testList);

            dimensionIds.addAll(testList.stream().map(DtoTest::getDimensionId).filter(StringUtil::isNotEmpty).collect(Collectors.toList()));
            //获取对应的测试扩展
            testExpandList = testExpandService.findRedisByTestIds(testIds);
            dimensionIds.addAll(testExpandList.stream().map(DtoTestExpand::getDimensionId).filter(StringUtil::isNotEmpty).collect(Collectors.toList()));
            //获取第一测试人员
            p2tList = person2TestService.findByTestIds(testIds);
        }
        List<DtoDimension> dimensionList = new ArrayList<>();
        if (dimensionIds.size() > 0) {
            dimensionList = dimensionService.findRedisByIds(dimensionIds);
        }
        if (!isSampling) {
            List<DtoReceiveSampleRecord> recordList = new ArrayList<>();
            for (DtoProject project : projectList) {
                DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
                DtoReceiveSampleRecordTemp recordTemp = new DtoReceiveSampleRecordTemp();
                recordTemp.loadFromProject(project);
                record.loadFromTemp(recordTemp);
                record.setRecordCode(receiveSampleRecordService.createReceiveSampleRecordCode());
                record.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.新建.getValue());
                record.setStatus(EnumLIM.EnumReceiveRecordStatus.新建.toString());
                record.setInfoStatus(EnumReceiveInfoStatus.已确认.getValue());
                recordList.add(record);
            }
            List<DtoReceiveSampleRecord> receiveSampleRecordList = receiveSampleRecordService.save(recordList);
            for (DtoReceiveSampleRecord sampleRecord : receiveSampleRecordList) {
                if (StringUtil.isNotNull(sampleRecord)) {
                    Optional<DtoProject> project = projectList.stream().filter(p -> p.getId().equals(sampleRecord.getProjectId())).findFirst();
                    //多线程处理排序
                    List<DtoQuotationDetail> quotationDetailList = projectDetailMap.get(sampleRecord.getProjectId());
                    List<DtoDimension> finalDimensionList = dimensionList;
                    List<DtoPerson2Test> finalP2tList = p2tList;
                    List<DtoTest> finalTestList = testList;
                    List<DtoTestExpand> finalTestExpandList = testExpandList;
                    project.ifPresent(dtoProject -> asyncDetailList.add(this.dealMatchOrderScheme(quotationDetailList, dtoProject, sampleRecord,
                            finalTestList, quotationDetail2TestList, finalTestExpandList, finalDimensionList, finalP2tList)));
                }
            }
        } else {
            for (DtoProject project : projectList) {
                //多线程处理排序
                List<DtoQuotationDetail> quotationDetailList = projectDetailMap.get(project.getId());
                List<DtoDimension> finalDimensionList = dimensionList;
                List<DtoPerson2Test> finalP2tList = p2tList;
                List<DtoTest> finalTestList = testList;
                List<DtoTestExpand> finalTestExpandList = testExpandList;
                asyncDetailList.add(this.dealMatchOrderScheme(quotationDetailList, project, null, finalTestList,
                        quotationDetail2TestList, finalTestExpandList, finalDimensionList, finalP2tList));
            }
        }

        //处理多线程处理的结果
        try {
            for (Future<String> asyncResult : asyncDetailList) {
                while (true) {
                    if (asyncResult.isDone() && !asyncResult.isCancelled()) {
                        break;
                    } else {
                        //防止CPU高速轮询被耗空
                        Thread.sleep(1);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("......多线程批量计算出错......");
        } finally {

        }
    }

    @Transactional
    @Override
    public DtoProject update(DtoProject dto) {
        dto.setSampleTypeArr(dto.getSampleTypeArr());
        DtoProjectPlan plan = projectPlanRepository.findByProjectId(dto.getId());
        DtoProject project = repository.findOne(dto.getId());
        Map<String, Map<String, Object>> planChange = proService.getCompare(plan, dto);
        Map<String, Map<String, Object>> projectChange = proService.getCompare(project, dto);
        String projectRegisterPage = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        List<String> contents = this.getChangeContent(projectChange, EnumProjectField.项目名称, EnumProjectField.项目等级, EnumProjectField.登记日期,
                EnumProjectField.登记人员, EnumProjectField.要求报告数, EnumProjectField.是否着重关注, EnumProjectField.受检方, EnumProjectField.受检方联系人,
                EnumProjectField.受检方联系方式, EnumProjectField.受检方地址, EnumProjectField.监测目的, EnumProjectField.监测方式, EnumProjectField.保存条件,
                EnumProjectField.交付方式, EnumProjectField.监测要求及说明, EnumProjectField.其他说明, EnumProjectField.监测类型);
        if (projectRegisterPage.equals(EnumProjectType.委托类.getValue()) || EnumProjectType.全流程.getValue().equals(projectRegisterPage)) {
            contents.addAll(this.getChangeContent(projectChange, EnumProjectField.委托方, EnumProjectField.委托方联系人,
                    EnumProjectField.委托方联系方式, EnumProjectField.委托方传真, EnumProjectField.委托方邮箱, EnumProjectField.委托方邮政编码, EnumProjectField.委托方地址));
        } else if (projectRegisterPage.equals(EnumProjectType.送样类.getValue())) {
            contents.addAll(this.getChangeContent(projectChange, EnumProjectField.送样方, EnumProjectField.送样方联系人,
                    EnumProjectField.送样方联系方式, EnumProjectField.送样方传真, EnumProjectField.送样方邮箱, EnumProjectField.送样方邮政编码, EnumProjectField.送样方地址));
        }
        contents.addAll(this.getChangeContent(planChange, EnumProjectField.项目负责人, EnumProjectField.编制报告人, EnumProjectField.要求完成日期,
                EnumProjectField.要求报告日期, EnumProjectField.是否提醒, EnumProjectField.提前提醒天数));

        plan.loadFromProject(dto);
        projectPlanService.update(plan);

//        DtoProjectJson projectJson = JsonIterator.deserialize(dto.getJson(), DtoProjectJson.class);
        //从数据库中获取最新的项目json信息
        DtoProjectJson projectJson = JsonIterator.deserialize(project.getJson(), DtoProjectJson.class);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            dto.setJson(objectMapper.writeValueAsString(projectJson));
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }

        String format = "</br>%s由'%s',修改为'%s'";
        DtoProject2Contract p2c = project2ContractRepository.findByProjectId(dto.getId());
        DtoContract sourceContract = StringUtil.isNotNull(p2c) ? contractService.findOne(p2c.getContractId()) : null;
        //若修改的合同id与原来不一致或原来不存在合同，需修改
        if (StringUtils.isNotNullAndEmpty(dto.getContractId()) && (StringUtil.isNull(p2c) || !p2c.getContractId().equals(dto.getContractId()))) {
            DtoContract contract = contractService.findOne(dto.getContractId());
            if (StringUtil.isNotNull(contract)) {//赋值合同的信息
                if (StringUtil.isNull(p2c)) {
                    p2c = new DtoProject2Contract();
                    p2c.setContractId(dto.getContractId());
                    p2c.setProjectId(dto.getId());
                    project2ContractService.save(p2c);
                } else {
                    p2c.setContractId(dto.getContractId());
                    project2ContractService.update(p2c);
                }
                projectJson.setContractCode(contract.getContractCode());
                projectJson.setCollectionStatus(contract.getCollectionStatus());
                //依次为已收款，坏账，总额
                projectJson.setCollectionDetail(new BigDecimal[]{contract.getArrivalAmount(), contract.getBadAmount(), contract.getTotalAmount()});
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    dto.setJson(objectMapper.writeValueAsString(projectJson));
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
            contents.add(String.format(format, "合同", StringUtil.isNotNull(sourceContract) ? sourceContract.getContractName() : "",
                    StringUtil.isNotNull(contract) ? contract.getContractName() : ""));
        } else if (!StringUtils.isNotNullAndEmpty(dto.getContractId()) && StringUtil.isNotNull(p2c)) {//若删除了合同关联
            project2ContractService.logicDeleteById(p2c.getId());
            projectJson.setContractCode("");
            projectJson.setCollectionStatus(null);
            projectJson.setCollectionDetail(null);
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                dto.setJson(objectMapper.writeValueAsString(projectJson));
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
            }
            contents.add(String.format(format, "合同", StringUtil.isNotNull(sourceContract) ? sourceContract.getContractName() : "", ""));
        }
        super.update(dto);
        this.updateSampleOfProject(dto);
        if (contents.size() > 0) {
            newLogService.createProjectInfoUpdateLog(Collections.singletonList(dto.getId()), String.join(";", contents), EnumLogOperateType.修改项目.toString());
        }
        if (StringUtils.isNotNull(dto.getProjectContract())) {
            projectContractRepository.save(dto.getProjectContract());
        }
        return dto;
    }

    /**
     * 修改项目时调用修改样品中绑定的受检单位
     *
     * @param project 修改的
     */
    private void updateSampleOfProject(DtoProject project) {
        List<DtoSample> samplesOfProject = sampleRepository.findByProjectId(project.getId());
        Set<String> sampleIds = samplesOfProject.parallelStream().map(DtoSample::getId).collect(Collectors.toSet());
        if (StringUtil.isNotEmpty(sampleIds)) {
            List<DtoSample> qcSamples = sampleRepository.findByAssociateSampleIdIn(sampleIds);
            samplesOfProject.addAll(qcSamples);
        }
        for (DtoSample sample : samplesOfProject) {
            sample.setInspectedEnt(project.getInspectedEnt());
            sample.setInspectedEntId(project.getInspectedEntId());
        }
        sampleRepository.save(samplesOfProject);
    }

    /**
     * 修改送样项目
     *
     * @param dto 实体
     * @return 送样项目
     */
    @Transactional
    @Override
    public DtoReceiveSampleRecordTemp updateOutsideSendSample(DtoReceiveSampleRecordTemp dto) {
        DtoProject project = this.findOne(dto.getProjectId());
        project.loadFromReceiveTemp(dto);
        this.update(project);
        DtoReceiveSampleRecord record = new DtoReceiveSampleRecord();
        record.loadFromTemp(dto);
        record.setId(dto.getId());
        receiveSampleRecordService.updateRecord(record);
        this.updateSampleOfProject(project);
        if (StringUtil.isNotNull(project.getProjectContract())) {
            projectContractRepository.save(project.getProjectContract());
        }
        return dto;
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        DtoProject dtoProject = repository.findOne(String.valueOf(id));
        //环保企业通项目信息推送
        DtoProject projectForPush = new DtoProject();
        BeanUtils.copyProperties(dtoProject, projectForPush);
        projectForPush.setIsDeleted(true);
        environmentEnterpriseService.pushProjectData(Collections.singletonList(projectForPush), null);
        String currentModule = EnumProjectStatus.getModuleCode(dtoProject.getStatus());
        proService.deleteProject(String.valueOf(id));
        newLogService.createProjectInfoUpdateLog(Collections.singletonList(String.valueOf(id)), "", EnumLogOperateType.删除项目.toString());
        Integer deleteRows = super.logicDeleteById(id);
        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId()
                , currentModule, "");
        return deleteRows;
    }

    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> projectIds = new ArrayList<>();
        for (Object id : ids) {
            projectIds.add(String.valueOf(id));
        }
        List<DtoProject> projects = repository.findAll(projectIds);
        List<DtoProject> projectListForPush = new ArrayList<>();
        for (DtoProject project : projects) {
            DtoProject projectForPush = new DtoProject();
            BeanUtils.copyProperties(project, projectForPush);
            projectForPush.setIsDeleted(true);
            projectListForPush.add(projectForPush);
        }
        //环保企业通项目信息推送
        environmentEnterpriseService.pushProjectData(projectListForPush, null);
        //找到对应的主项目
        List<String> proIds = projects.stream().map(DtoProject::getParentId).filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId))
                .distinct().collect(Collectors.toList());
        //判断是否存在子项目的情况
        if (proIds.size() > 0) {
            //找到主项目下的企业信息
            List<DtoProject2Customer> project2CustomerList = project2CustomerRepository.findByProjectIdIn(proIds);
            //找到对应要删除的企业
            List<DtoProject2Customer> deleteCustomerList = new ArrayList<>();
            projects.forEach(p -> {
                Optional<DtoProject2Customer> customerOptional = project2CustomerList.stream()
                        .filter(c -> p.getParentId().equals(c.getProjectId()) && p.getCustomerId().equals(c.getCustomerId()))
                        .findFirst();
                customerOptional.ifPresent(deleteCustomerList::add);
            });
            project2CustomerRepository.delete(deleteCustomerList);
        }

        List<String> statusList = projects.stream().map(DtoProject::getStatus).distinct().collect(Collectors.toList());

        proService.deleteProjects(projectIds);
        newLogService.createProjectInfoUpdateLog(projectIds, "", EnumLogOperateType.删除项目.toString());
        Integer deleteRows = super.logicDeleteById(ids);
        for (String status : statusList) {
            String currentModule = EnumProjectStatus.getModuleCode(status);
            homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(), currentModule, "");
        }
        return deleteRows;
    }

    @Transactional
    @Override
    public String createProjectCode(String projectTypeId, Date inputTime) {
        return serialNumberService.createNewNumber(projectTypeId, inputTime);
    }

    /**
     * 复制项目
     *
     * @param receiveSampleRecord 前端传回的采样单对象
     */
    @Transactional
    @Override
    public DtoProject copyProject(DtoReceiveSampleRecord receiveSampleRecord) {
        DtoProject oldProject = this.findOne(receiveSampleRecord.getProjectId());
        //获取到复制的项目实体
        DtoProject project = getCopyProjectData(oldProject, receiveSampleRecord);
        DtoProject item = this.save(project);
        String projectRegisterPage = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        if (StringUtil.isEmpty(projectRegisterPage)) {
            projectRegisterPage = projectTypeService.getConfigValue(project.getProjectTypeId(), "LIM_ProjectTypeCode_IND");
        }
        String projectTypeCodeInd = projectTypeService.getConfigValue(project.getProjectTypeId(), "LIM_ProjectTypeCode_IND");
        if (EnumProjectType.送样类.getValue().equals(projectRegisterPage)
                || EnumProjectType.现场类.getValue().equals(projectRegisterPage)) {
            receiveSampleRecordService.copyRecord(oldProject, receiveSampleRecord.getSamplingTime(), item, receiveSampleRecord.getIsCopySample());
        } else if ((EnumProjectType.委托类.getValue().equals(projectRegisterPage)
                || EnumProjectType.全流程.getValue().equals(projectRegisterPage)
                || (EnumProjectType.例行类.getValue().equals(projectRegisterPage) && "WR".equals(projectTypeCodeInd))) && project.getIsMakePlan()) {
            schemeService.copyScheme(receiveSampleRecord.getProjectId(), item.getId());
        }
        if (project.getIsMultiEnterprise()) {
            //多企业情况下，需要复制项目和企业的关联关系
            List<DtoProject2Customer> oldProject2CustomerList = project2CustomerRepository.findByProjectId(oldProject.getId());
            if (StringUtil.isNotEmpty(oldProject2CustomerList)) {
                List<DtoProject2Customer> newProject2CustomerList = new ArrayList<>();
                for (DtoProject2Customer project2Customer : oldProject2CustomerList) {
                    DtoProject2Customer addDto = new DtoProject2Customer();
                    addDto.setProjectId(project.getId());
                    addDto.setCustomerId(project2Customer.getCustomerId());
                    addDto.setCustomerName(project2Customer.getCustomerName());
                    newProject2CustomerList.add(addDto);
                }
                project2CustomerRepository.save(newProject2CustomerList);
            }
        }
        //委托类的非编制方案不需要复制点位
        if ("WR".equals(projectTypeCodeInd)) {
            homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(), EnumLIM.EnumHomeTaskModule.例行登记.getValue(), "");
        } else {
            homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(), EnumLIM.EnumHomeTaskModule.项目登记.getValue(), "");
        }
        return project;
    }

    /**
     * 复制项目
     *
     * @param projCode     项目编号
     * @param samplingTime 采样时间
     */
    @Transactional
    @Override
    public List<DtoProject> copyProject(String projCode, Date samplingTime, int copyTimes) {
        List<DtoProject> list = new ArrayList<>();
        DtoProject oldProject = repository.findByProjectCode(projCode);
        oldProject = this.findOne(oldProject.getId());
        if (oldProject == null) {
            throw new BaseException("源项目编号不存在");
        }
        for (int i = 0; i < copyTimes; i++) {
            DtoProject project = new DtoProject();
            BeanUtils.copyProperties(oldProject, project, "dbExtendMap");
            project.setProjectName(project.getProjectName() + ("复制项目" + i));
            project.setInceptTime(DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));
            project.setInputTime(DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));

            DtoProject dto = new DtoProject();
            project.setId(dto.getId());
            project.setSamplingStatus(dto.getSamplingStatus());
            project.setReportStatus(dto.getReportStatus());
            project.setIsOnline(dto.getIsOnline());
            project.setPushStatus(dto.getPushStatus());
            project.setCreator(dto.getCreator());
            project.setCreateDate(dto.getCreateDate());
            project.setDomainId(dto.getDomainId());
            project.setModifier(dto.getModifier());
            project.setModifyDate(dto.getModifyDate());
            //项目编号生成 从inputTime 改为 inceptTime
            String projectCode = this.createProjectCode(project.getProjectTypeId(), project.getInceptTime());
            project.setProjectCode(projectCode);
            DtoProject item = this.save(project);
            list.add(item);
            String projectRegisterPage = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
            if (StringUtil.isEmpty(projectRegisterPage)) {
                projectRegisterPage = projectTypeService.getConfigValue(project.getProjectTypeId(), "LIM_ProjectTypeCode_IND");
            }
            if (EnumProjectType.送样类.getValue().equals(projectRegisterPage)
                    || EnumProjectType.现场类.getValue().equals(projectRegisterPage)) {
                receiveSampleRecordService.copyRecord(oldProject, samplingTime, item, true);
            } else if ((EnumProjectType.委托类.getValue().equals(projectRegisterPage)
                    || EnumProjectType.全流程.getValue().equals(projectRegisterPage)) && project.getIsMakePlan()) {
                schemeService.copyScheme(oldProject.getId(), item.getId());
            }
            //委托类的非编制方案不需要复制点位
            homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                    PrincipalContextUser.getPrincipal().getOrgId(), EnumLIM.EnumHomeTaskModule.项目登记.getValue(), "");
        }
        return list;
    }


    /**
     * 复制方案
     *
     * @param oldProjectId 源项目id
     * @param projectId    目标项目id
     */
    @Transactional
    @Override
    public void copyScheme(String oldProjectId, String projectId) {
        DtoProject project = this.findOne(oldProjectId);
        //先删除原项目方案
        List<String> folderIds = sampleFolderRepository.findByProjectId(projectId).stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        this.deleteSampleFolder(folderIds, projectId);
        //再进行方案复制
        schemeService.copyScheme(oldProjectId, projectId);

        String comment = String.format("%s复制了编号为%s的项目方案", PrincipalContextUser.getPrincipal().getUserName(), project.getProjectCode());
        String operateInfo = "复制方案";
        newLogService.createLog(projectId, comment, comment, EnumLogType.复制方案.getValue(),
                EnumLogObjectType.方案.getValue(), operateInfo, PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    /**
     * 工作流操作
     *
     * @param dtoWorkflowSign 实体
     */
    @Transactional
    @Override
    public void projectSignal(DtoWorkflowSign dtoWorkflowSign) {
        try {
            if (StringUtils.isNotNullAndEmpty(dtoWorkflowSign.getNextOperatorId()) && !UUIDHelper.GUID_EMPTY.equals(dtoWorkflowSign.getNextOperatorId())) {
                DtoPerson per = personService.findOne(dtoWorkflowSign.getNextOperatorId());
                dtoWorkflowSign.setNextOperator(StringUtil.isNotNull(per) ? per.getCName() : "");
            } else {
                dtoWorkflowSign.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            }
            if (dtoWorkflowSign.getObjectIds().size() > 0) {
                dtoWorkflowSign.setIsAutoStatus(false);
                List<DtoProject> projectList = repository.findAll(dtoWorkflowSign.getObjectIds());
                List<DtoProjectPlan> projectPlanList = projectPlanRepository.findByProjectIdIn(dtoWorkflowSign.getObjectIds());
                //找到对应的样品
                List<DtoSample> sampleList = sampleRepository.findByProjectIdIn(dtoWorkflowSign.getObjectIds());
                String to = workflowService.submitSign(dtoWorkflowSign);
                List<String> projectIds = new ArrayList<>();
                if (!StringUtils.isNotNullAndEmpty(to) && "projectEnd".equals(dtoWorkflowSign.getSignal())) {
                    to = EnumProjectStatus.已办结.toString();
                }
                String from = projectList.get(0).getStatus();
                List<DtoSubmitRecord> submitRecords = new ArrayList<>();
                for (DtoProject project : projectList) {
                    if (StringUtils.isNotNullAndEmpty(dtoWorkflowSign.getNextOperatorId()) &&
                            !UUIDHelper.GUID_EMPTY.equals(dtoWorkflowSign.getNextOperatorId())) {
                        projectIds.add(project.getId());
                    }
                    List<DtoSample> samList = sampleList.stream().filter(p -> project.getId().equals(p.getProjectId())).collect(toList());
                    if (samList.stream().allMatch(p -> p.getSamplingStatus().equals(EnumSamplingStatus.已经完成取样.getValue()))) {
                        project.setSamplingStatus(EnumSampledStatus.已采毕.getValue());
                    }
                    DtoSubmitRecord submitRecord = new DtoSubmitRecord();
                    submitRecord.setObjectId(project.getId());
                    submitRecord.setObjectType(EnumSubmitObjectType.项目.getValue());
                    submitRecord.setSubmitType(EnumSubmitType.无.getValue());
                    submitRecord.setSubmitPersonId(PrincipalContextUser.getPrincipal().getUserId());
                    submitRecord.setSubmitPersonName(PrincipalContextUser.getPrincipal().getUserName());
                    submitRecord.setSubmitTime(new Date());
                    submitRecord.setNextPerson(dtoWorkflowSign.getNextOperator());
                    submitRecord.setSubmitRemark(dtoWorkflowSign.getOption());
                    submitRecord.setStateFrom(project.getStatus());
                    submitRecord.setStateTo(to);
                    submitRecords.add(submitRecord);
                    DtoProject dtoProject = new DtoProject();
                    BeanUtils.copyProperties(project, dtoProject);
                    dtoProject.setStatus(to);
                    //全流程项目登记直接提交到方案编制，下一步操作人就是项目负责人
                    if (EnumProjectStatus.方案编制中.toString().equals(to)) {
                        DtoProjectPlan dtoProjectPlan = projectPlanList.stream().filter(p -> p.getProjectId().equals(project.getId()))
                                .findFirst().orElse(null);
                        //判断项目负责人是否为空，避免设后续保存空的项目负责人
                        if (StringUtil.isNotNull(dtoProjectPlan) && StringUtil.isNotEmpty(dtoProjectPlan.getLeaderId())) {
                            if (StringUtil.isEmpty(dtoWorkflowSign.getNextOperatorId())) {
                                dtoWorkflowSign.setNextOperatorId(dtoProjectPlan.getLeaderId());
                            }
                        }
                    }
                    handleStatus(from, to, dtoWorkflowSign, dtoProject);
                    project.setStatus(to);
                    project.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                    project.setModifyDate(new Date());
                }
                //修改项目状态及采样状态
                repository.save(projectList);
//                repository.updateProjectStatus(dtoWorkflowSign.getObjectIds(), to, PrincipalContextUser.getPrincipal().getUserId(), new Date());
                if (projectIds.size() > 0) {
                    List<DtoProjectPlan> dtoProjectPlanList = projectPlanRepository.findByProjectIdIn(projectIds);
                    //保存项目负责人或者编制报告人
                    saveProjectLeader(dtoProjectPlanList, dtoWorkflowSign);
                    projectPlanRepository.flush();
                }

                submitRecordService.createSubmitRecords(submitRecords,
                        PrincipalContextUser.getPrincipal().getUserId(),
                        PrincipalContextUser.getPrincipal().getUserName(),
                        PrincipalContextUser.getPrincipal().getOrgId());
                //计算首页数据缓存
                clearProjectTask(from, to);
                //清除缓存，保证后续数据能取到最新的数据
                comRepository.clear();
                //保证事务提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                if (dtoWorkflowSign.getSignal().contains("back")) {
                                    newLogService.createProjectStatusBackLog(dtoWorkflowSign.getObjectIds(),
                                            dtoWorkflowSign.getOption(), dtoWorkflowSign.getNextOperatorId(), dtoWorkflowSign.getNextOperator());
                                } else {
                                    List<String> dtoWorkflowSignObjectIds = dtoWorkflowSign.getObjectIds();
                                    // 如果是任务下达,则没有下一步操作人
                                    if (StringUtil.isNotEmpty(projectIds) && StringUtil.isNotEmpty(dtoWorkflowSignObjectIds)) {
                                        dtoWorkflowSignObjectIds.removeAll(projectIds);
                                        newLogService.createProjectStatusUpdateLog(projectIds,
                                                dtoWorkflowSign.getOption(), UUIDHelper.GUID_EMPTY, "");
                                    }
                                    // 如果是其他的项目流程操作,日志正常生成
                                    if (StringUtil.isNotEmpty(dtoWorkflowSignObjectIds)) {
                                        newLogService.createProjectStatusUpdateLog(dtoWorkflowSignObjectIds,
                                                dtoWorkflowSign.getOption(), dtoWorkflowSign.getNextOperatorId(), dtoWorkflowSign.getNextOperator());
                                    }
                                }
                                String signal = dtoWorkflowSign.getSignal();
                                if ("registerSubmit".equals(signal) || "projectAudit".equals(signal) || "projectGive".equals(signal)) {
                                    //环保企业通推送项目信息
                                    environmentEnterpriseService.pushProjectData(projectList, EnumProjectStatus.技术审核中.name());
                                } else if ("projectLaunch".equals(signal)) {
                                    //环保企业通推送项目信息
                                    environmentEnterpriseService.pushProjectData(projectList, EnumProjectStatus.开展中.name());
                                }
                                // 电子表单签名
                                signProjectSheet(projectList, signal);
                            }
                        }
                );
            }
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            throw new BaseException("异常错误");
        }
    }

    @Override
    public List<Integer> findPostMethodKeys(DtoProject project) {
        //BUG2024110701431 “报告交付方式”支持多选
        //由于字段为int  存储通过按位与计算
        List<Integer> list = new ArrayList<>();
        if (project.getPostMethod() != null && project.getPostMethod() != 0) {
            List<DtoCode> postMethods = codeService.findCodes(ProCodeHelper.POST_METHOD);
            postMethods.forEach(v -> {
                if (StringUtil.isNotEmpty(v.getDictValue()) && MathUtil.isInteger(v.getDictValue())) {
                    int dictValue = Integer.parseInt(v.getDictValue());
                    if ((project.getPostMethod() & dictValue) != 0) {
                        list.add(dictValue);
                    }
                }
            });
        }
        return list;
    }

    /**
     * 处理项目状态数据
     *
     * @param from            起始状态
     * @param to              终止状态
     * @param dtoWorkflowSign 工作流
     * @param dtoProject      项目数据
     */
    protected void handleStatus(String from, String to, DtoWorkflowSign dtoWorkflowSign, DtoProject dtoProject) {
        statusForProjectService.modifyStatus(from, to, dtoWorkflowSign, dtoProject);
    }

    /**
     * 刷新任务的缓存数量
     *
     * @param from 起始状态
     * @param to   终止状态
     */
    protected void clearProjectTask(String from, String to) {
        //计算首页数据缓存
        String currentModule = EnumProjectStatus.getModuleCode(from);
        List<String> nextModules = EnumProjectStatus.getModuleCodes(to);
        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId()
                , currentModule, nextModules);
    }

    /**
     * 获取项目下未采样品的个数
     *
     * @param projectId 样品id
     * @return 项目下未采样品的个数
     */
    @Override
    public Integer countNotSampleByProjectId(String projectId) {
        return sampleRepository.countByProjectIdAndSamplingStatus(projectId, EnumSamplingStatus.需要取样还未取样.getValue());
    }

    /**
     * 新增项目流程状态退回日志
     *
     * @param projectId 对象ids
     * @param opinion   意见
     */
    @Transactional
    @Override
    public void projectSchemeModify(String projectId, String opinion) {
        newLogService.createProjectSchemeModifyLog(projectId, opinion);
    }

    /**
     * 获取项目下的检测类型
     *
     * @param projectId 项目id
     */
    @Override
    public List<DtoSampleType> findProjectSampleTypes(String projectId) {
        List<DtoSampleFolder> folders = sampleFolderRepository.findByProjectId(projectId);
        if (StringUtil.isNotNull(folders) && folders.size() > 0) {
            List<String> sampleTypeIds = folders.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
            List<String> industryTypeIds = sampleTypes.stream().map(DtoSampleType::getIndustryTypeId).distinct().collect(Collectors.toList());
            if (industryTypeIds.size() > 1) {
                List<DtoIndustryType> industryTypes = industryTypeService.findAllDeleted(industryTypeIds);
                Map<String, String> industryTypeMap = industryTypes.stream().collect(Collectors.toMap(DtoIndustryType::getId, DtoIndustryType::getIndustryName));
                for (DtoSampleType sampleType : sampleTypes) {
                    sampleType.setIndustryTypeName(industryTypeMap.getOrDefault(sampleType.getIndustryTypeId(), ""));
                    if (StringUtils.isNotNullAndEmpty(sampleType.getIndustryTypeName())) {
                        sampleType.setSampleTypeName(String.format("%s-%s", sampleType.getIndustryTypeName(), sampleType.getSampleTypeName()));
                    }
                }
                sampleTypes.sort(Comparator.comparing(DtoSampleType::getIndustryTypeName).thenComparing(Comparator.comparing(DtoSampleType::getParentId))
                        .thenComparing(Comparator.comparing(DtoSampleType::getOrderNum)));
                return sampleTypes;
            } else {
                sampleTypes.sort(Comparator.comparing(DtoSampleType::getParentId).thenComparing(Comparator.comparing(DtoSampleType::getOrderNum)));
                return sampleTypes;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取项目下检测大类
     *
     * @param projectId 项目id
     */
    @Override
    public List<DtoSampleType> findProjectBigSampleTypes(String projectId) {
        List<DtoSampleType> resultList = new ArrayList<>();
        List<DtoSampleFolder> sampleFolderList = sampleFolderRepository.findByProjectId(projectId);
        if (StringUtil.isNotEmpty(sampleFolderList)) {
            List<String> sampleTypeIds = sampleFolderList.stream().map(DtoSampleFolder::getSampleTypeId).distinct()
                    .collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeService.findAll(sampleTypeIds);
            List<String> parentIds = sampleTypeList.stream().map(DtoSampleType::getParentId).collect(Collectors.toList());
            resultList = sampleTypeService.findAll(parentIds);
        }
        resultList.sort(Comparator.comparing(DtoSampleType::getOrderNum, Comparator.reverseOrder()));
        return resultList;
    }

    /**
     * 核对项目操作条件
     *
     * @param projectIds 项目id集合
     * @param type       场景类型
     */
    @Override
    public void checkCondition(List<String> projectIds, String type) {
        EnumProjectConditionType condition = EnumProjectConditionType.getByValue(type);
        DtoErrorInfo errorInfo = new DtoErrorInfo(projectIds.size() > 1);
        if (StringUtil.isNotNull(condition)) {
            for (String projectId : projectIds) {
                switch (condition) {
                    case 登记提交:
                        this.checkProjectSubmitCondition(projectId, errorInfo);
                        break;

                    case 报告完成:
                        this.checkReportCondition(projectId, errorInfo);
                        break;

                    case 任务办结:
                        this.checkEndCondition(projectId, errorInfo);
                        break;

                    default:
                }
            }
            String msg = errorInfo.getMsg();
            if (StringUtils.isNotNullAndEmpty(msg)) {
                throw new BaseException(msg);
            }
        }
    }

    /**
     * 校验必填参数是否有值
     *
     * @param projectIds 项目id集合
     * @return 是否有必填参数没有填写参数值
     */
    @Override
    public boolean checkSampleParamRequired(List<String> projectIds) {
        //找到项目下的所有样品
        if (StringUtil.isNotEmpty(projectIds)) {
            List<DtoSample> allSampleList = sampleRepository.findByProjectIdIn(projectIds);
            if (StringUtil.isNotEmpty(allSampleList)) {
                //加上质控样品
                List<DtoSample> extraSampleList;
                do {
                    List<String> sampleIdList = allSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                    extraSampleList = sampleRepository.findByAssociateSampleIdIn(sampleIdList)
                            .stream().filter(p -> !sampleIdList.contains(p.getId())).collect(Collectors.toList());
                    allSampleList.addAll(extraSampleList);
                } while (StringUtil.isNotEmpty(extraSampleList));
                //找到所有样品下的参数
                if (StringUtil.isNotEmpty(allSampleList)) {
                    List<String> allSampleIdList = allSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                    List<DtoParamsData> allParamsDataList = paramsDataRepository.findByObjectIdIn(allSampleIdList);
                    //找到所有参数值为空的参数
                    List<DtoParamsData> emptyParamsDataList = allParamsDataList.stream().filter(p -> StringUtil.isEmpty(p.getParamsValue())).collect(Collectors.toList());
                    emptyParamsDataList = filterAnaParamsData(emptyParamsDataList, allParamsDataList, allSampleIdList);
                    if (StringUtil.isNotEmpty(emptyParamsDataList)) {
                        List<String> emptyParamsConfigIdList = emptyParamsDataList.stream().map(DtoParamsData::getParamsConfigId).distinct().collect(Collectors.toList());
                        long count = paramsConfigRepository.findAll(emptyParamsConfigIdList).stream().filter(ParamsConfig::getIsRequired).count();
                        if (count > 0) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 分析项目参数默认会有一调没有分组id的paramsData记录，其paramsValue为空，过滤掉该记录，避免影响参数非空校验
     *
     * @param emptyParamsDataList 参数值为空的参数列表
     * @param allParamsDataList   所有参数列表
     * @param allSampleIdList     所有样品id列表
     * @return 参数值为空的参数列表
     */
    private List<DtoParamsData> filterAnaParamsData(List<DtoParamsData> emptyParamsDataList, List<DtoParamsData> allParamsDataList, List<String> allSampleIdList) {
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(allSampleIdList)
                ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(allSampleIdList) : new ArrayList<>();
        List<String> groupIdList = allParamsDataList.stream().map(DtoParamsData::getGroupId).distinct().collect(toList());
        List<DtoSampleTypeGroup2Test> group2TestList = StringUtil.isNotEmpty(groupIdList) ? sampleTypeGroup2TestRepository.findBySampleTypeGroupIds(groupIdList) : new ArrayList<>();
        Map<String, List<DtoSampleTypeGroup2Test>> group2TestMap = group2TestList.stream().collect(Collectors.groupingBy(DtoSampleTypeGroup2Test::getSampleTypeGroupId));
        if (StringUtil.isNotEmpty(analyseDataList)) {
            Map<String, List<DtoAnalyseData>> analyseDataMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
            List<String> removeParamIdList = new ArrayList<>();
            for (DtoParamsData paramsData : emptyParamsDataList) {
                boolean removeFlag = false;
                List<DtoParamsData> sameNameParamList = allParamsDataList.stream().filter(p -> !p.getId().equals(paramsData.getId()) && StringUtil.isNotEmpty(p.getParamsValue())
                        && p.getParamsConfigId().equals(paramsData.getParamsConfigId()) && (p.getParamsName().equals(paramsData.getParamsName()) || (p.getParamsName().contains("-")
                        && p.getParamsName().split("-").length > 1 && p.getParamsName().split("-")[1].equals(paramsData.getParamsName())))).collect(toList());
                if (StringUtil.isNotEmpty(sameNameParamList)) {
                    List<String> testIdForSample = analyseDataMap.containsKey(paramsData.getObjectId()) ? analyseDataMap.get(paramsData.getObjectId())
                            .stream().map(DtoAnalyseData::getTestId).distinct().collect(toList()) : new ArrayList<>();
                    if (StringUtil.isNotEmpty(testIdForSample)) {
                        for (DtoParamsData sameNameParam : sameNameParamList) {
                            if (sameNameParam.getParamsName().equals(paramsData.getParamsName()) && sameNameParam.getGroupId().equals(paramsData.getGroupId())) {
                                removeParamIdList.add(paramsData.getId());
                                removeFlag = true;
                                break;
                            } else {
                                if (UUIDHelper.GUID_EMPTY.equals(sameNameParam.getGroupId())) {
                                    removeParamIdList.add(paramsData.getId());
                                    removeFlag = true;
                                    break;
                                } else {
                                    List<DtoSampleTypeGroup2Test> group2Tests = group2TestMap.getOrDefault(sameNameParam.getGroupId(), new ArrayList<>());
                                    List<String> testIds = group2Tests.stream().map(DtoSampleTypeGroup2Test::getTestId).distinct().collect(toList());
                                    testIdForSample.removeAll(testIds);
                                }
                            }
                        }
                        if (!removeFlag && StringUtil.isEmpty(testIdForSample)) {
                            removeParamIdList.add(paramsData.getId());
                        }
                    }
                }
            }
            emptyParamsDataList = emptyParamsDataList.stream().filter(p -> !removeParamIdList.contains(p.getId())).collect(toList());
        }
        return emptyParamsDataList;
    }

    /**
     * 修改项目报告状态
     *
     * @param projectIds   项目idlist
     * @param reportStatus 报告状态
     */
    @Transactional
    @Override
    public void changeReportStatus(List<String> projectIds, Integer reportStatus, String comment) {
        List<DtoLog> logList = new ArrayList<>();
        List<DtoLog> reportLogs = new ArrayList<>();
        List<DtoReport> reportList = StringUtil.isNotEmpty(projectIds) ? reportRepository.findByProjectIdIn(projectIds) : new ArrayList<>();
        List<DtoSample> sampleList = sampleRepository.findByProjectIdIn(projectIds);
        for (String projectId : projectIds) {
            DtoProject project = repository.findOne(projectId);
            if (project.getReportStatus().equals(reportStatus)) {
                continue;
            }
            if (sampleList.stream().anyMatch(p -> projectId.equals(p.getProjectId())
                    && !EnumSampleStatus.样品检毕.name().equals(p.getStatus()))) {
                project.setStatus(EnumPRO.EnumProjectStatus.开展中.name());
            } else {
                project.setStatus(EnumPRO.EnumProjectStatus.数据汇总中.name());
            }
            project.setReportStatus(reportStatus);
            comRepository.merge(project);
            statusForProjectService.modifyStatus(project.getStatus(), project.getStatus(), new DtoWorkflowSign(), project);
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.修改项目.toString());
            log.setLogType(EnumLogType.项目流程.getValue());
            log.setObjectId(projectId);
            log.setObjectType(EnumLogObjectType.项目.getValue());
            log.setComment(reportStatus.equals(EnumReportStatus.未完成.getValue()) ? "项目报告重新编制" : "项目报告完成");
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
            List<String> reportIds = reportList.stream().filter(r -> r.getProjectId().equals(projectId)).map(DtoReport::getId).collect(toList());
            if (EnumReportStatus.未完成.getValue().equals(reportStatus)) {
                for (String reportId : reportIds) {
                    DtoLog reportLog = new DtoLog();
                    reportLog.setId(UUIDHelper.NewID());
                    reportLog.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                    reportLog.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
                    reportLog.setOperateTime(new Date());
                    reportLog.setOperateInfo(EnumLogOperateType.更新报告状态.toString());
                    reportLog.setLogType(EnumLogType.项目报告.getValue());
                    reportLog.setObjectId(reportId);
                    reportLog.setLogType(EnumLogObjectType.报告.getValue());
                    reportLog.setComment("重新编制报告：" + comment);
                    reportLog.setOpinion("");
                    reportLog.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                    reportLog.setRemark("");
                    reportLogs.add(reportLog);
                }
            }
        }
        if (logList.size() > 0) {
            newLogService.createLog(logList, EnumLogType.项目流程.getValue());
        }
        if (reportLogs.size() > 0) {
            newLogService.createLog(reportLogs, EnumLogType.项目报告.getValue());
        }
        //要重新计算需要编制报告的项目数
        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId()
                , EnumLIM.EnumHomeTaskModule.报告编制.getValue(), "");

    }

    /**
     * 修改项目采样状态
     *
     * @param projectIds     项目idlist
     * @param samplingStatus 报告状态
     */
    @Transactional
    @Override
    public void changeSamplingStatus(List<String> projectIds, Integer samplingStatus) {
        if (samplingStatus.equals(EnumSampledStatus.已采毕.getValue())) {
            List<DtoReceiveSampleRecord> records = receiveSampleRecordRepository.findByProjectIdIn(projectIds);
            if (records.stream().anyMatch(p -> p.getInfoStatus().equals(EnumReceiveInfoStatus.新建.getValue()))) {
                throw new BaseException("存在项目没有送样单或送样单未提交");
            }
            if (records.stream().map(DtoReceiveSampleRecord::getProjectId).distinct().collect(Collectors.toList()).size() < projectIds.size()) {
                throw new BaseException("存在项目没有送样单或送样单未提交");
            }
        }
        List<DtoLog> logList = new ArrayList<>();
        for (String projectId : projectIds) {
            DtoProject project = repository.findOne(projectId);
            if (project.getSamplingStatus().equals(samplingStatus)) {
                continue;
            }
            project.setSamplingStatus(samplingStatus);
            comRepository.merge(project);
            statusForProjectService.modifyStatus(project.getStatus(), project.getStatus(), new DtoWorkflowSign(), project);
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.修改项目.toString());
            log.setLogType(EnumLogType.项目流程.getValue());
            log.setObjectId(projectId);
            log.setObjectType(EnumLogObjectType.项目.getValue());
            log.setComment(samplingStatus.equals(EnumSampledStatus.未采毕.getValue()) ? "项目重新采样" : "项目采样完成");
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
        }
        if (logList.size() > 0) {
            newLogService.createLog(logList, EnumLogType.项目流程.getValue());
        }
        //重新计算当前首页的缓存数据
        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId(),
                EnumLIM.EnumHomeTaskModule.现场委托送样.getValue(), ""
        );
    }

    /**
     * 更换编制报告人
     *
     * @param projectId     项目id
     * @param reportMakerId 编制报告人id
     * @param opinion       意见
     */
    @Transactional
    @Override
    public void changeReportMaker(String projectId, String reportMakerId, String opinion) {
        DtoProjectPlan projectPlan = projectPlanRepository.findByProjectId(projectId);
        if (!projectPlan.getReportMakerId().equals(reportMakerId)) {
            newLogService.createUpdateReportMakerLog(projectId, projectPlan.getReportMakerId(), reportMakerId, opinion);
            projectPlan.setReportMakerId(reportMakerId);
            projectPlanService.update(projectPlan);
        }
    }

    @Override
    public DtoProject findAttachPath(String id) {
        return repository.findOne(id);
    }

    //#region 核查项目操作条件

    //region 例行任务登记

    /***
     * 保存环境质量任务
     * @param project 项目信息
     * @param pIds 监测子计划
     * @param pollution 是否污染源
     * @param scheme 是否更新方案
     */
    @Transactional
    @Override
    public DtoProject saveProject(DtoProject project, List<String> pIds, Boolean pollution, Boolean scheme) {
        //保存项目信息
        DtoProject dtoProject;
        String customerId = project.getCustomerId();
        String customerName = project.getCustomerName();
        //如果没有受检单位id则填充空的uuid，否则会报错
        if (StringUtil.isNull(project.getInspectedEntId())) {
            project.setInspectedEntId(UUIDHelper.GUID_EMPTY);
        }
//        //多企业的情况下，获取企业名称列表，用“、”隔开
//        if (project.getIsMultiEnterprise() != null && project.getIsMultiEnterprise()) {
//            List<String> customerIdList = StringUtil.isNotEmpty(project.getCustomerId()) ? Arrays.asList(project.getCustomerId().split(",")) : new ArrayList<>();
//            List<DtoEnterprise> enterpriseList = StringUtil.isNotEmpty(customerIdList) ? enterpriseRepository.findAll(customerIdList) : new ArrayList<>();
//            //保存项目id和企业的关联关系
//            //删除所有的关联企业后重新添加关联企业
//            List<DtoProject2Customer> projectToCustomer = project2CustomerRepository.findByProjectId(project.getId());
//            if (StringUtil.isNotEmpty(projectToCustomer)) {
//                //删除之前的关联企业数据
//                project2CustomerRepository.delete(projectToCustomer);
//            }
//            List<DtoProject2Customer> project2CustomerList = new ArrayList<>();
//            for (DtoEnterprise enterprise : enterpriseList) {
//                DtoProject2Customer project2Customer = new DtoProject2Customer();
//                project2Customer.setProjectId(project.getId());
//                project2Customer.setCustomerId(enterprise.getId());
//                project2Customer.setCustomerName(enterprise.getName());
//                project2CustomerList.add(project2Customer);
//            }
//            if (StringUtil.isNotEmpty(project2CustomerList)) {
//                project2CustomerRepository.batchInsert(project2CustomerList);
//            }
//            project.setCustomerId(UUIDHelper.GUID_EMPTY);
//            project.setCustomerName("");
////            List<String> customerNameList = enterpriseList.stream().map(DtoEnterprise::getName).distinct().collect(Collectors.toList());
////            project.setCustomerName(String.join("、", customerNameList));
//        }
        if (scheme) {
            dtoProject = this.save(project);
        } else {
            dtoProject = this.update(project);
        }
        if (pIds != null) {
            if (pollution) {
                savePollutionMonitorScheme(dtoProject, pIds.stream().distinct().collect(Collectors.toList()), pollution, scheme);
            } else {
                saveRoutineMonitoringScheme(dtoProject, pIds.stream().distinct().collect(Collectors.toList()), pollution, scheme);
            }
        }
        //设置客户信息
        DtoProject projectTemp = new DtoProject();
        BeanUtils.copyProperties(dtoProject, projectTemp);
        projectTemp.setCustomerId(customerId);
        projectTemp.setCustomerName(customerName);
        return projectTemp;
    }

    /**
     * 更新污染源方案
     *
     * @param projectId 任务id
     * @param pIds      点位集合
     */
    @Transactional
    @Override
    public void pollutionMonitorScheme(String projectId, List<String> pIds) {
        if (pIds != null && pIds.size() > 0) {
            DtoProject dtoProject = repository.findOne(projectId);
            savePollutionMonitorScheme(dtoProject, pIds.stream().distinct().collect(Collectors.toList()), true, true);
        }
    }

    /**
     * 更新例行方案
     *
     * @param projectId 任务id
     * @param pIds      监测计划集合
     */
    @Transactional
    @Override
    public void routineMonitorScheme(String projectId, List<String> pIds) {
        if (pIds != null && pIds.size() > 0) {
            DtoProject dtoProject = repository.findOne(projectId);
            saveRoutineMonitoringScheme(dtoProject, pIds.stream().distinct().collect(Collectors.toList()), false, true);
        }
    }

    /***
     * 通过子计划生成任务方案（例行监测）
     * @param project 任务
     * @param pIds 计划ids
     * @param pollution 是否污染源
     */
    private void saveRoutineMonitoringScheme(DtoProject project, List<String> pIds, Boolean pollution, Boolean scheme) {
        //任务id
        String projectId = project.getId();

        //获取该任务老的配置属性
        List<DtoProject2FixedProperty> project2FixedPropertyList = project2FixedPropertyRepository.findByProjectId(projectId);
        if (scheme) {
            List<String> oldPropertyIds = project2FixedPropertyList.stream().map(DtoProject2FixedProperty::getFixedPropertyId).distinct().collect(Collectors.toList());

            //删除的配置id
            List<String> removeIds = new ArrayList<>(oldPropertyIds);
            removeIds.removeAll(pIds);

            //保留的配置id
            List<String> modifyIds = new ArrayList<>(oldPropertyIds);
            modifyIds.removeAll(removeIds);

            //新增的配置id
            List<String> createIds = new ArrayList<>(pIds);
            createIds.removeAll(oldPropertyIds);

            //获取任务下的点位
            List<DtoSampleFolder> sampleFolderList = sampleFolderRepository.findByProjectId(projectId);
            List<String> samPointIds = sampleFolderList.stream().map(DtoSampleFolder::getFixedPointId).distinct().collect(Collectors.toList());

            //删除属性相关数据
            this.deleteProperty(removeIds, samPointIds, sampleFolderList, projectId);

            //保留属性相关数据
            this.modifyProperty(modifyIds, samPointIds, sampleFolderList, project, pollution);

            //新增属性相关数据
            this.createProperty(createIds, project, pollution);
        }
        //删除原有项目属性关系
        project2FixedPropertyService.delete(project2FixedPropertyList);

        //保存现有项目属性关系
        List<DtoProject2FixedProperty> dtoProject2FixedPropertyList = new ArrayList<>();
        for (String id : pIds) {
            DtoProject2FixedProperty dtoProject2FixedProperty = new DtoProject2FixedProperty();
            dtoProject2FixedProperty.setProjectId(project.getId());
            dtoProject2FixedProperty.setFixedPropertyId(id);
            dtoProject2FixedPropertyList.add(dtoProject2FixedProperty);
        }
        project2FixedPropertyService.save(dtoProject2FixedPropertyList);
        if (scheme) {
            //纠正项目状态
            proService.checkProject(Collections.singletonList(projectId));
        }
    }

    //region 删除属性相关数据

    /**
     * 删除属性
     *
     * @param removeIds        要删除的属性id集合
     * @param samPointIds      样品点位ids
     * @param sampleFolderList 样品点位集合
     * @param projectId        任务id
     */
    private void deleteProperty(List<String> removeIds, List<String> samPointIds, List<DtoSampleFolder> sampleFolderList, String projectId) {

        //删除的计划点位
        List<String> delPointIds = property2PointRepository.findByPropertyIdIn(removeIds).stream()
                .map(DtoProperty2Point::getFixedPointId).collect(Collectors.toList());
        delPointIds.removeAll(samPointIds);

        //要删除的点位
        List<String> delFolderIds = sampleFolderList.stream().filter(p -> delPointIds.contains(p.getFixedPointId()))
                .map(DtoSampleFolder::getId).distinct().collect(Collectors.toList());
        //删除点位信息
        this.deleteSampleFolder(delFolderIds, projectId);
    }

    /**
     * 删除点位及点位关联
     *
     * @param delFolderIds 点位ids
     * @param projectId    任务id
     */
    private void deleteSampleFolder(List<String> delFolderIds, String projectId) {
        List<DtoSampleFolder> folderList = sampleFolderService.findAll(delFolderIds);
        List<String> sampleTypeIds = folderList.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypeList = new ArrayList<>();
        if (sampleTypeIds.size() > 0) {
            sampleTypeList = sampleTypeService.findAll(sampleTypeIds);
        }
        for (DtoSampleFolder sampleFolder : folderList) {
            DtoSampleType sampleType = sampleTypeList.stream().filter(p -> p.getId().equals(sampleFolder.getSampleTypeId()))
                    .findFirst().orElse(null);
            if (sampleType != null) {
                String comment = String.format("删除了点位%s(%s)。", sampleFolder.getWatchSpot(), sampleType.getTypeName());
                newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumLogType.方案点位信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.删除点位.toString(),
                        PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
            }
        }
        //删除对应点位下的频次及指标
        samplingFrequencyService.deleteBySampleFolderIdIn(delFolderIds);
        samplingFrequencyTestRepository.deleteBySampleFolderIdIn(delFolderIds);

        //删除点位样品相关信息
        this.deleteSampleFolderIds(projectId, delFolderIds);

        //删除点位评价
        evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(delFolderIds, EnumEvaluationType.点位.getValue(), EnumEvaluationPlan.计划.getValue());

        //删除点位
        if (delFolderIds.size() > 0) {
            sampleFolderService.logicDeleteById(delFolderIds);

            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            proService.sendProMessage(EnumProAction.方案删除点位, projectId);
                        }
                    }
            );
        }
    }

    /**
     * 删除点位信息
     *
     * @param projectId       任务id
     * @param sampleFolderIds 点位ids
     */
    private void deleteSampleFolderIds(String projectId, List<String> sampleFolderIds) {
        if (sampleFolderIds.size() > 0) {
            Map<String, Object> values = new HashMap<>();
            StringBuilder stringBuilder = new StringBuilder("select s.id,s.receiveId");
            stringBuilder.append(" from DtoSample s where 1=1");
            if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
                stringBuilder.append(" and s.orgId = :orgId");
                values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
            }
            stringBuilder.append(" and s.isDeleted = 0");
            stringBuilder.append(" and s.sampleFolderId in :sampleFolderIds ");
            values.put("sampleFolderIds", sampleFolderIds);
            List<Object[]> samples = comRepository.find(stringBuilder.toString(), values);
            Map<String, List<String>> recMap = new HashMap<>();

            //对需要删除的样品按照送样单id分组
            if (samples.size() > 0) {
                for (Object[] sample : samples) {
                    String sampleId = (String) sample[0];
                    String receiveId = (String) sample[1];
                    if (!recMap.containsKey(receiveId)) {
                        recMap.put(receiveId, new ArrayList<>());
                    }
                    recMap.get(receiveId).add(sampleId);
                }
                schemeService.deleteSample(projectId, recMap);
            }
        }
    }
    //endregion

    //region 保留属性相关数据

    /**
     * 保留属性相关数据
     *
     * @param modifyIds        监测计划的id或者污染源配置的点位
     * @param samPointIds      配置点位的id
     * @param sampleFolderList 方案点位的集合
     * @param project          项目
     * @param pollution        是否污染源
     */
    private void modifyProperty(List<String> modifyIds, List<String> samPointIds, List<DtoSampleFolder> sampleFolderList, DtoProject project, Boolean pollution) {

        List<String> modifyFolderIds = new ArrayList<>();
        modifyFolderIds.addAll(modifyIds);
        if (!pollution) {
            modifyFolderIds = property2PointRepository.findByPropertyIdIn(modifyIds).stream()
                    .map(DtoProperty2Point::getFixedPointId).collect(Collectors.toList());
        }
        List<String> samFolderIds = sampleFolderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());

        List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdIn(samFolderIds);

        //缺少的点位
        List<String> createPointIds = new ArrayList<>();
        createPointIds.addAll(modifyFolderIds);
        //如果当前任务已经添加点位就不再添加
        createPointIds.removeAll(samPointIds);
        //保留属性中缺少的点位
        this.addSampleFolder(project, createPointIds, modifyIds, pollution);

        //保留属性中已经存在的点位
        List<String> modifyPointIds = new ArrayList<>();
        modifyPointIds.addAll(modifyFolderIds);
        modifyPointIds.removeAll(createPointIds);
        this.checkSampleFolder(project, sampleFolderList, modifyPointIds, modifyIds, samplingFrequencyList, pollution);
    }

    /**
     * 新增点位
     *
     * @param project        任务
     * @param createPointIds 需要创建的计划点位
     * @param modifyIds      需要创建的监测计划 或者污染源点位
     * @param pollution      是否污染源
     */
    private void addSampleFolder(DtoProject project, List<String> createPointIds, List<String> modifyIds, Boolean pollution) {
        List<DtoFixedpoint> fixedpointList = fixedpointRepository.findByIdIn(createPointIds);
        List<DtoFixedPointExpend> fixedPointExpendList = fixedPointExpendRepository.findByFixedPointIdIn(createPointIds);
        List<DtoFixedPointProperty> fixedPointPropertyList = new ArrayList<>();
        List<DtoProperty2Point> property2PointList = new ArrayList<>();
        List<DtoPropertyPoint2Test> propertyPoint2TestList = new ArrayList<>();
        List<DtoFixedPoint2Test> fixedPoint2TestList = new ArrayList<>();
        List<String> allTestIds = new ArrayList<>();
        if (!pollution) {//环境质量
            fixedPointPropertyList = fixedPointPropertyRepository.findByIdIn(modifyIds);
            property2PointList = property2PointRepository.findByPropertyIdInAndFixedPointIdIn(modifyIds, createPointIds);
            List<String> p2pIds = property2PointList.stream().map(DtoProperty2Point::getId).collect(Collectors.toList());
            propertyPoint2TestList = propertyPoint2TestRepository.findByPropertyPointIdIn(p2pIds);
            allTestIds = propertyPoint2TestList.stream().map(DtoPropertyPoint2Test::getTestId).collect(Collectors.toList());
        } else {//污染源
            fixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointIdIn(createPointIds);
            allTestIds = fixedPoint2TestList.stream().map(DtoFixedPoint2Test::getTestId).distinct().collect(Collectors.toList());
        }
        List<String> levelIds = fixedpointList.stream().map(DtoFixedpoint::getEvaluationLevelId).distinct().collect(Collectors.toList());
        List<DtoEvaluationValue> evaluationValueList = evaluationValueRepository.findByLevelIdIn(levelIds);

        List<String> entIds = fixedpointList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getEnterpriseId()))
                .map(DtoFixedpoint::getEnterpriseId).distinct().collect(Collectors.toList());

        List<DtoEnterprise> enterpriseList = new ArrayList<>();
        if (entIds.size() > 0) {
            enterpriseList = enterpriseRepository.findAll(entIds);
        }
        List<DtoTest> testList = new ArrayList<>();
        if (allTestIds.size() > 0) {
            testList = testRepository.findAll(allTestIds);
            // 根据分析方法状态，剔除停用、废止的方法对应的测试项目
            testService.removeByMethodStatus(testList);
        }
        //添加点位
        DtoLoadScheme targetScheme = new DtoLoadScheme();
        Map<String, Object> database;
        List<DtoEvaluationRecord> evaluationRecordList = new ArrayList<>();
        for (DtoFixedpoint dtoFixedpoint : fixedpointList) {
            DtoFixedpoint fixedpoint = new DtoFixedpoint();
            BeanUtils.copyProperties(dtoFixedpoint, fixedpoint);
            DtoFixedPointExpend fixedPointExpend = fixedPointExpendList.stream()
                    .filter(p -> p.getFixedPointId().equals(fixedpoint.getId())).findFirst().orElse(null);
            List<String> propertyIds = property2PointList.stream().filter(p -> p.getFixedPointId().equals(fixedpoint.getId()))
                    .map(DtoProperty2Point::getPropertyId).distinct().collect(Collectors.toList());
            List<DtoFixedPointProperty> fixedPointProperties = fixedPointPropertyList.stream()
                    .filter(p -> propertyIds.contains(p.getId())).collect(Collectors.toList());
            DtoEnterprise enterprise = enterpriseList.stream().filter(p -> p.getId().equals(fixedpoint.getEnterpriseId())).findFirst().orElse(new DtoEnterprise());
            if (StringUtils.isNotNullAndEmpty(enterprise.getName())) {
                fixedpoint.setEnterpriseName(enterprise.getName());
            }
            if (!pollution) {
                Map<String, List<DtoFixedPointProperty>> samTypeId2Property = fixedPointProperties.stream().collect(Collectors.groupingBy(DtoFixedPointProperty::getSampleTypeId));
                for (Map.Entry<String, List<DtoFixedPointProperty>> entry : samTypeId2Property.entrySet()) {
                    List<DtoFixedPointProperty> entryProperty = entry.getValue();
                    Integer cycleOrder = entryProperty.stream().max(Comparator.comparing(DtoFixedPointProperty::getCycleOrder))
                            .map(DtoFixedPointProperty::getCycleOrder).get();
                    fixedpoint.setCycleOrder(cycleOrder);
                    Set<String> p2pIds = property2PointList.stream().filter(p -> p.getFixedPointId().equals(fixedpoint.getId()))
                            .map(DtoProperty2Point::getId).collect(toSet());
                    Integer timesOrder = propertyPoint2TestList.stream().filter(p -> p2pIds.contains(p.getPropertyPointId()))
                            .max(Comparator.comparing(DtoPropertyPoint2Test::getTimesOrder)).map(DtoPropertyPoint2Test::getTimesOrder).get();
                    Integer sampleCount = propertyPoint2TestList.stream().filter(p -> p2pIds.contains(p.getPropertyPointId()))
                            .max(Comparator.comparing(DtoPropertyPoint2Test::getSamplePeriod)).map(DtoPropertyPoint2Test::getSamplePeriod).get();
                    fixedpoint.setTimesOrder(timesOrder);
                    fixedpoint.setSamplePeriod(sampleCount);
                    database = this.saveSampleFolder(project, fixedpoint, entryProperty.get(0), fixedPointExpend,
                            property2PointList, propertyPoint2TestList, evaluationValueList, testList, targetScheme, entryProperty);
                    targetScheme = (DtoLoadScheme) database.get("scheme");
                    evaluationRecordList.addAll((List<DtoEvaluationRecord>) database.get("record"));
                }
            } else {
                // 跳过没有配置测试项目的点位
                List<DtoFixedPoint2Test> fixedPoint2TestsByPoint = fixedPoint2TestList.stream().filter(p -> p.getFixedPointId().equals(fixedpoint.getId())).collect(toList());
                if (StringUtil.isEmpty(fixedPoint2TestsByPoint)) {
                    continue;
                }
                Integer timesOrder = fixedPoint2TestList.stream().max(Comparator.comparing(DtoFixedPoint2Test::getTimesOrder)).map(DtoFixedPoint2Test::getTimesOrder).get();
                Integer sampleCount = fixedPoint2TestList.stream().max(Comparator.comparing(DtoFixedPoint2Test::getSamplePeriod)).map(DtoFixedPoint2Test::getSamplePeriod).get();
                fixedpoint.setTimesOrder(timesOrder);
                fixedpoint.setSamplePeriod(sampleCount);
                database = this.saveSampleFolder(project, fixedpoint, new DtoFixedPointProperty(), fixedPointExpend,
                        property2PointList, propertyPoint2TestList, evaluationValueList, testList, targetScheme, new ArrayList<>());
                targetScheme = (DtoLoadScheme) database.get("scheme");
                evaluationRecordList.addAll((List<DtoEvaluationRecord>) database.get("record"));
            }
        }

        if (evaluationRecordList.size() > 0) {
            evaluationRecordRepository.save(evaluationRecordList);
        }
        this.persistLoadScheme(targetScheme);
    }

    /**
     * 保存点位信息
     *
     * @param project                任务
     * @param fixedpoint             点位信息
     * @param fixedPointProperty     监测计划
     * @param fixedPointExpend       点位扩展
     * @param property2PointList     计划与点位的关联
     * @param propertyPoint2TestList 计划点位与测试项目的关联
     * @param evaluationValueList    评价标准
     * @param testList               测试项目
     */
    private Map<String, Object> saveSampleFolder(DtoProject project, DtoFixedpoint fixedpoint, DtoFixedPointProperty fixedPointProperty,
                                                 DtoFixedPointExpend fixedPointExpend, List<DtoProperty2Point> property2PointList,
                                                 List<DtoPropertyPoint2Test> propertyPoint2TestList,
                                                 List<DtoEvaluationValue> evaluationValueList, List<DtoTest> testList,
                                                 DtoLoadScheme targetScheme, List<DtoFixedPointProperty> mergedProperty) {

        Map<String, Object> saveBase = new HashMap<>();
        if (propertyPoint2TestList.size() > 0) {
            setPropertyPeriod(fixedPointProperty, property2PointList, propertyPoint2TestList);
        }
        //region 新增点位信息
        DtoSampleFolder sampleFolder = new DtoSampleFolder();
        sampleFolder.setProjectId(project.getId());
        sampleFolder.setWatchSpot(fixedpoint.getPointName());
        sampleFolder.setFixedPointId(fixedpoint.getId());
        if (property2PointList.size() > 0) {
            sampleFolder.setSampleTypeId(fixedPointProperty.getSampleTypeId());
        } else {
            sampleFolder.setSampleTypeId(fixedpoint.getSampleTypeId());
        }
        sampleFolder.setFolderCode(fixedpoint.getPointCode());
        sampleFolder.setChargeRate(new BigDecimal(1));
        sampleFolder.setLon(fixedpoint.getLon());
        sampleFolder.setLat(fixedpoint.getLat());
        sampleFolder.setPeriodCount(fixedpoint.getCycleOrder());
        sampleFolder.setTimePerPeriod(fixedpoint.getTimesOrder());
        sampleFolder.setSampleOrder(fixedpoint.getSamplePeriod());
        sampleFolder.setInspectedEntId(fixedpoint.getEnterpriseId());
        sampleFolder.setInspectedEnt(fixedpoint.getEnterpriseName());
        sampleFolder.setGrade(1);
        //endregion

        sampleFolder.setOrgId(project.getOrgId());
        DtoSampleFolder folder = sampleFolderService.save(sampleFolder);
        folder.setPeriodCount(fixedpoint.getCycleOrder());
        folder.setTimePerPeriod(fixedpoint.getTimesOrder());
        folder.setSampleOrder(fixedpoint.getSamplePeriod());

        String propertyPointId = property2PointList.stream().filter(p -> p.getPropertyId()
                .equals(fixedPointProperty.getId()) && p.getFixedPointId().equals(fixedpoint.getId()))
                .map(DtoProperty2Point::getId).findFirst().orElse(null);
        List<DtoTest> validTestList = testList;
        List<String> testIds;
        List<DtoFixedPoint2Test> fixedPoint2TestList = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(propertyPointId)) {
            testIds = propertyPoint2TestList.stream().filter(p -> p.getPropertyPointId().equals(propertyPointId))
                    .map(DtoPropertyPoint2Test::getTestId).distinct().collect(Collectors.toList());
        } else {
            fixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointId(fixedpoint.getId());
            testIds = fixedPoint2TestList.stream().map(DtoFixedPoint2Test::getTestId).collect(Collectors.toList());
        }
        if (testIds.size() > 0) {
            validTestList = testList.stream().filter(p -> testIds.contains(p.getId())).collect(Collectors.toList());
        }
        if (mergedProperty.size() > 1) {
            targetScheme = addSchemeFolder(sampleFolder, testList, targetScheme, fixedpoint, mergedProperty, property2PointList, propertyPoint2TestList);
        } else {
            //保存点位数据
            if (fixedPoint2TestList.size() == 0) {
                List<String> tIds = validTestList.stream().map(DtoTest::getId).collect(toList());
                List<DtoPropertyPoint2Test> propertyTestList = propertyPoint2TestList.stream().filter(p -> tIds.contains(p.getTestId())).collect(toList());
                for (DtoPropertyPoint2Test pointTest : propertyTestList) {
                    DtoFixedPoint2Test point2Test = new DtoFixedPoint2Test();
                    point2Test.setTestId(pointTest.getTestId());
                    point2Test.setTimesOrder(pointTest.getTimesOrder());
                    point2Test.setSamplePeriod(pointTest.getSamplePeriod());
                    fixedPoint2TestList.add(point2Test);
                }
            }
            targetScheme = this.addSchemeFolder(folder, validTestList, fixedPoint2TestList, targetScheme);
        }


        List<DtoEvaluationRecord> evaluationRecordList = new ArrayList<>();
        //等点位保存之后再保存评价标准和等级 -- 点位
        for (DtoTest test : validTestList) {
            DtoEvaluationRecord evaluationRecord = new DtoEvaluationRecord();
            evaluationRecord.setObjectId(folder.getId());
            evaluationRecord.setOrgId(project.getOrgId());
            evaluationRecord.setEvaluationId(fixedpoint.getEvaluationId());
            evaluationRecord.setEvaluationLevelId(fixedpoint.getEvaluationLevelId());
            evaluationRecord.setTestId(test.getId());
            DtoEvaluationValue evaluationValue = evaluationValueList.stream().filter(p -> p.getEvaluationId()
                    .equals(fixedpoint.getEvaluationId()) && p.getLevelId().equals(fixedpoint.getEvaluationLevelId())
                    && p.getAnalyzeItemId().equals(test.getAnalyzeItemId())).findFirst().orElse(null);
            evaluationRecord.setFolderPlan(EnumEvaluationPlan.实际.getValue());
            evaluationRecord.setObjectType(EnumEvaluationType.点位.getValue());
            if (evaluationValue != null) {
                try {
                    evaluationRecord.setLowerLimitValue(StringUtil.isNotEmpty(evaluationValue.getLowerLimit()) ? evaluationValue.getLowerLimit() : "");
                    evaluationRecord.setUpperLimitValue(StringUtil.isNotEmpty(evaluationValue.getUpperLimit()) ? evaluationValue.getUpperLimit() : "");
                    evaluationRecord.setLowerLimitSymble(StringUtil.isNotEmpty(evaluationValue.getLowerLimitSymble()) ? evaluationValue.getLowerLimitSymble() : "");
                    evaluationRecord.setUpperLimitSymble(StringUtil.isNotEmpty(evaluationValue.getUpperLimitSymble()) ? evaluationValue.getUpperLimitSymble() : "");
                } catch (Exception ex) {
                    throw new BaseException(ex.getMessage());
                }
            }
            evaluationRecordList.add(evaluationRecord);
        }
        List<DtoAnalyseData> anaList = targetScheme.getAnalyseData();

        for (DtoAnalyseData ana : anaList) {
            if (!ana.getIsEvaluationRecord()) {
                DtoEvaluationRecord evaluationRecord = new DtoEvaluationRecord();
                evaluationRecord.setObjectId(ana.getId());
                evaluationRecord.setOrgId(project.getOrgId());
                evaluationRecord.setEvaluationId(fixedpoint.getEvaluationId());
                evaluationRecord.setEvaluationLevelId(fixedpoint.getEvaluationLevelId());
                evaluationRecord.setTestId(ana.getTestId());
                DtoEvaluationValue evaluationValue = evaluationValueList.stream().filter(p -> p.getEvaluationId()
                        .equals(fixedpoint.getEvaluationId()) && p.getLevelId().equals(fixedpoint.getEvaluationLevelId())
                        && p.getAnalyzeItemId().equals(ana.getAnalyseItemId())).findFirst().orElse(null);
                evaluationRecord.setFolderPlan(EnumEvaluationPlan.分析数据.getValue());
                evaluationRecord.setObjectType(EnumEvaluationType.分析数据.getValue());
                if (evaluationValue != null) {
                    try {
                        evaluationRecord.setLowerLimitValue(StringUtil.isNotEmpty(evaluationValue.getLowerLimit()) ? evaluationValue.getLowerLimit() : "");
                        evaluationRecord.setUpperLimitValue(StringUtil.isNotEmpty(evaluationValue.getUpperLimit()) ? evaluationValue.getUpperLimit() : "");
                        evaluationRecord.setLowerLimitSymble(StringUtil.isNotEmpty(evaluationValue.getLowerLimitSymble()) ? evaluationValue.getLowerLimitSymble() : "");
                        evaluationRecord.setUpperLimitSymble(StringUtil.isNotEmpty(evaluationValue.getUpperLimitSymble()) ? evaluationValue.getUpperLimitSymble() : "");
                    } catch (Exception ex) {
                        throw new BaseException(ex.getMessage());
                    }
                }
                evaluationRecordList.add(evaluationRecord);
                ana.setIsEvaluationRecord(Boolean.TRUE);
            }
        }


        evaluationRecordRepository.save(evaluationRecordList);
        saveBase.put("scheme", targetScheme);
        saveBase.put("record", evaluationRecordList);
        return saveBase;
    }

    /**
     * 环境质量添加点位方案
     *
     * @param sampleFolder           点位
     * @param testList               测试项目
     * @param targetScheme           方案
     * @param mergedProperty         合并的计划
     * @param property2PointList     计划点位列表
     * @param propertyPoint2TestList 计划点位测试项目列表
     * @return 添加的方案
     */
    private DtoLoadScheme addSchemeFolder(DtoSampleFolder sampleFolder, List<DtoTest> testList, DtoLoadScheme targetScheme,
                                          DtoFixedpoint fixedpoint, List<DtoFixedPointProperty> mergedProperty,
                                          List<DtoProperty2Point> property2PointList, List<DtoPropertyPoint2Test> propertyPoint2TestList) {
        Map<String, List<String>> property2FolderNum = new HashMap<>();
        for (DtoFixedPointProperty property : mergedProperty) {
            List<String> folderNum = new ArrayList<>();
            setPropertyPeriod(property, property2PointList, propertyPoint2TestList);
            for (Integer i = 1; i <= property.getCycleOrder(); i++) {
                for (Integer j = 1; j <= property.getTimesOrder(); j++) {
                    for (Integer v = 1; v <= property.getSamplePeriod(); v++) {
                        folderNum.add(String.format("%d_%d_%d", i, j, v));
                    }
                }
            }
            property2FolderNum.put(property.getId(), folderNum);
        }
        Map<String, List<DtoTest>> folderNum2Test = new HashMap<>();
        for (DtoFixedPointProperty property : mergedProperty) {
            for (Integer i = 1; i <= property.getCycleOrder(); i++) {
                for (Integer j = 1; j <= property.getTimesOrder(); j++) {
                    for (Integer v = 1; v <= property.getSamplePeriod(); v++) {
                        if (!folderNum2Test.containsKey(String.format("%d_%d_%d", i, j, v))) {
                            List<String> propertyIds = new ArrayList<>();
                            Integer s = i.intValue();
                            Integer z = j.intValue();
                            Integer x = v.intValue();
                            property2FolderNum.forEach((k, r) -> {
                                if (r.contains(String.format("%d_%d_%d", s, z, x))) {
                                    propertyIds.add(k);
                                }
                            });
                            List<String> property2PointIds = property2PointList.stream().filter(p -> propertyIds.contains(p.getPropertyId()) && fixedpoint.getId().equals(p.getFixedPointId())).map(DtoProperty2Point::getId).collect(toList());
                            List<String> propertyTestIds = propertyPoint2TestList.stream().filter(p -> property2PointIds.contains(p.getPropertyPointId())).map(DtoPropertyPoint2Test::getTestId).distinct().collect(toList());
                            List<DtoTest> propertyTestList = testList.stream().filter(t -> propertyTestIds.contains(t.getId())).collect(toList());
                            folderNum2Test.put(String.format("%d_%d_%d", i, j, v), propertyTestList);
                        }
                    }
                }
            }
        }
        Map<String, Integer> testId2Sum = new HashMap<>();
        folderNum2Test.forEach((folderNum, tests) -> {
            tests.forEach(test -> {
                if (!testId2Sum.containsKey(test.getId())) {
                    testId2Sum.put(test.getId(), 1);
                } else {
                    Integer num = testId2Sum.get(test.getId());
                    num++;
                    testId2Sum.put(test.getId(), num);
                }
            });
        });
        folderNum2Test.forEach((folderNum, tests) -> {
            Map<String, Long> anaItem2Sum = tests.stream().collect(Collectors.groupingBy(DtoTest::getAnalyzeItemId, Collectors.counting()));
            anaItem2Sum.forEach((k, v) -> {
                if (v.compareTo(1L) > 0) {
                    List<String> testIds = tests.stream().filter(t -> t.getAnalyzeItemId().equals(k)).map(DtoTest::getId).collect(toList());
                    testIds.sort(new Comparator<String>() {
                        @Override
                        public int compare(String o1, String o2) {
                            return testId2Sum.get(o2).compareTo(testId2Sum.get(o1));
                        }
                    });
                    String testId = testIds.get(0);
                    testIds.removeIf(t -> t.equals(testId));
                    tests.removeIf(t -> testIds.contains(t.getId()));
                }
            });
        });
        List<DtoTest> mergedTestList = folderNum2Test.get("1_1_1");
        //用于复制的模板数据载体
        DtoLoadScheme templateScheme = new DtoLoadScheme();
        DtoProject project = super.findOne(sampleFolder.getProjectId());
        for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
            for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                for (Integer v = 1; v <= sampleFolder.getSampleOrder(); v++) {
                    DtoSamplingFrequency frequency = createSamplingFrequency(i, j, v, sampleFolder);
                    targetScheme.addSamplingFrequency(frequency);
                    templateScheme.putNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY, frequency.getId(), String.valueOf(i),
                            String.valueOf(j), String.valueOf(v));
                    for (DtoTest test : folderNum2Test.get(String.format("%d_%d_%d", i, j, v))) {
                        DtoSamplingFrequencyTest sft = createSamplingFrequencyTest(sampleFolder, frequency, test);
                        targetScheme.addSamplingFrequencyTest(sft);
                    }
                }
            }
        }
        DtoSample temp = createSample(templateScheme, sampleFolder, project, folderNum2Test.get("1_1_1"), 1, 1, 1);
        templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, temp.getId(), "1", "1", "1");
        temp = sampleRepository.save(temp);
        for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
            for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                for (Integer v = 1; v <= sampleFolder.getSampleOrder(); v++) {
                    if (i.equals(1) && j.equals(1) && v.equals(1)) {
                        continue;
                    }
                    DtoSample targetSample = createSample(templateScheme, sampleFolder, project,
                            folderNum2Test.get(String.format("%d_%d_%d", i, j, v)), i, j, v);
                    templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, targetSample.getId(), String.valueOf(i),
                            String.valueOf(j), String.valueOf(v));
                    targetScheme.addSample(targetSample);
                }
            }
        }
        if (mergedTestList.size() > 0) {
            DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, temp, mergedTestList, false);
            addDto.setIsAddAssociateTest(false);
            analyseDataService.addAnalyseData(addDto);
            List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findBySampleIdAndIsDeletedFalse(temp.getId());
            for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
                for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                    for (Integer v = 1; v <= sampleFolder.getSampleOrder(); v++) {
                        if (i.equals(1) && j.equals(1) && v.equals(1)) {
                            continue;
                        }
                        List<String> anaTestIds = folderNum2Test.get(String.format("%d_%d_%d", i, j, v)).stream().map(DtoTest::getId).collect(toList());
                        List<DtoAnalyseData> anaSourceAnalyseDatas = sourceAnalyseDatas.stream().filter(a -> anaTestIds.contains(a.getTestId())).collect(toList());
                        for (DtoAnalyseData analyseData : anaSourceAnalyseDatas) {
                            DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(analyseData);
                            targetAnalyseData.setSampleId(templateScheme.getNewSampleId(UUIDHelper.GUID_EMPTY,
                                    String.valueOf(i), String.valueOf(j), String.valueOf(v)));
                            targetScheme.addAnalyseData(targetAnalyseData);
                        }
                    }
                }
            }
        }
        sampleFolderRepository.save(sampleFolder);
        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("新增点位%s(%s),%d周期,%d次,%d个样", sampleFolder.getWatchSpot(), sampleType.getTypeName(),
                sampleFolder.getPeriodCount(), sampleFolder.getTimePerPeriod(), sampleFolder.getSampleOrder());
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(),
                EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(),
                UUIDHelper.GUID_EMPTY, "");
        //新增点位消息
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案新增点位, project.getId());
                    }
                });
        return targetScheme;
    }

    /**
     * 添加点位方案
     *
     * @param sampleFolder        点位
     * @param testList            测试项目
     * @param fixedPoint2TestList 点位测试项目关联关系
     * @return 添加的方案
     */
    private DtoLoadScheme addSchemeFolder(DtoSampleFolder sampleFolder, List<DtoTest> testList, List<DtoFixedPoint2Test> fixedPoint2TestList, DtoLoadScheme targetScheme) {
        //用于复制的模板数据载体
        DtoLoadScheme templateScheme = new DtoLoadScheme();
        //用于插入的数据载体
        DtoProject project = super.findOne(sampleFolder.getProjectId());
        for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
            for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                for (Integer v = 1; v <= sampleFolder.getSampleOrder(); v++) {
                    if (!judgmentTest(fixedPoint2TestList, j, v, UUIDHelper.GUID_EMPTY)) {
                        continue;
                    }
                    DtoSamplingFrequency frequency = createSamplingFrequency(i, j, v, sampleFolder);
                    targetScheme.addSamplingFrequency(frequency);
                    templateScheme.putNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY, frequency.getId(), String.valueOf(i),
                            String.valueOf(j), String.valueOf(v));
                    for (DtoTest test : testList) {
                        if (judgmentTest(fixedPoint2TestList, j, v, test.getId())) {
                            DtoSamplingFrequencyTest sft = createSamplingFrequencyTest(sampleFolder, frequency, test);
                            targetScheme.addSamplingFrequencyTest(sft);
                        }
                    }
                }
            }
        }
        DtoSample temp = createSample(templateScheme, sampleFolder, project, testList, 1, 1, 1);
        templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, temp.getId(), "1", "1", "1");
        temp = sampleRepository.save(temp);
        for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
            for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                for (Integer v = 1; v <= sampleFolder.getSampleOrder(); v++) {
                    if (i.equals(1) && j.equals(1) && v.equals(1)) {
                        continue;
                    }
                    if (!judgmentTest(fixedPoint2TestList, j, v, UUIDHelper.GUID_EMPTY)) {
                        continue;
                    }
                    DtoSample targetSample = createSample(templateScheme, temp, i, j, v, sampleFolder);
                    List<DtoTest> loopTestList = new ArrayList<>();
                    for (DtoTest test : testList) {
                        if (judgmentTest(fixedPoint2TestList, j, v, test.getId())) {
                            loopTestList.add(test);
                        }
                    }
                    targetSample.setRedAnalyzeItems(proService.getAnalyzeItemsByTest(loopTestList));
                    templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, targetSample.getId(), String.valueOf(i),
                            String.valueOf(j), String.valueOf(v));
                    targetScheme.addSample(targetSample);
                }
            }
        }

        if (testList.size() > 0) {
            DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, temp, testList, false);
            addDto.setIsAddAssociateTest(false);
            analyseDataService.addAnalyseData(addDto);
            List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findBySampleIdAndIsDeletedFalse(temp.getId());
            for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
                for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                    for (Integer v = 1; v <= sampleFolder.getSampleOrder(); v++) {
                        if (i.equals(1) && j.equals(1) && v.equals(1)) {
                            continue;
                        }
                        if (!judgmentTest(fixedPoint2TestList, j, v, UUIDHelper.GUID_EMPTY)) {
                            continue;
                        }
                        Integer finalJ = j;
                        Integer finalV = v;
                        Set<String> tIds = fixedPoint2TestList.stream().filter(p -> p.getTimesOrder() >= finalJ
                                && p.getSamplePeriod() >= finalV).map(DtoFixedPoint2Test::getTestId).collect(Collectors.toSet());
                        List<DtoAnalyseData> dataList = sourceAnalyseDatas.stream().filter(p -> tIds.contains(p.getTestId()))
                                .collect(toList());
                        for (DtoAnalyseData analyseData : dataList) {
                            DtoAnalyseData targetAnalyseData = this.getSchemeCloneAnalyseData(analyseData);
                            if (judgmentTest(fixedPoint2TestList, j, v, analyseData.getTestId())) {
                                targetAnalyseData.setSampleId(templateScheme.getNewSampleId(UUIDHelper.GUID_EMPTY,
                                        String.valueOf(i), String.valueOf(j), String.valueOf(v)));
                                targetScheme.addAnalyseData(targetAnalyseData);
                            }
                        }
                    }
                }
            }
        }

        sampleFolderRepository.save(sampleFolder);
//        this.persistLoadScheme(targetScheme);

        DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        String comment = String.format("新增点位%s(%s),%d周期,%d次。", sampleFolder.getWatchSpot(), sampleType.getTypeName(), sampleFolder.getPeriodCount(), sampleFolder.getTimePerPeriod());
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

//        targetScheme.setSampleFolder(Collections.singletonList(sampleFolder));
        //新增点位消息
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumPRO.EnumProAction.方案新增点位, project.getId());
                    }
                });
        //proService.sendProMessage(EnumPRO.EnumProAction.方案新增点位, project.getId());

        return targetScheme;
    }

    private void setPropertyPeriod(DtoFixedPointProperty property, List<DtoProperty2Point> property2PointList,
                                   List<DtoPropertyPoint2Test> propertyPoint2TestList) {
        List<String> pertyPointIds = property2PointList.stream().filter(p -> property.getId().equals(p.getPropertyId()))
                .map(DtoProperty2Point::getId).collect(toList());
        List<DtoPropertyPoint2Test> point2TestList = propertyPoint2TestList.stream()
                .filter(p -> pertyPointIds.contains(p.getPropertyPointId())).collect(toList());
        Integer timeOrder = Collections.max(point2TestList.stream().map(DtoPropertyPoint2Test::getTimesOrder).collect(toList()));
        Integer sampleCount = Collections.max(point2TestList.stream().map(DtoPropertyPoint2Test::getSamplePeriod).collect(toList()));
        property.setTimesOrder(timeOrder);
        property.setSamplePeriod(sampleCount);
    }

    private void setFixpointPeriod(DtoFixedpoint point, List<DtoPropertyPoint2Test> propertyPoint2TestList) {
        List<DtoPropertyPoint2Test> point2TestList = propertyPoint2TestList.stream()
                .filter(p -> point.getId().equals(p.getPropertyPointId())).collect(toList());
        Integer timeOrder = Collections.max(point2TestList.stream().map(DtoPropertyPoint2Test::getTimesOrder).collect(toList()));
        Integer sampleCount = Collections.max(point2TestList.stream().map(DtoPropertyPoint2Test::getSamplePeriod).collect(toList()));
        point.setTimesOrder(timeOrder);
        point.setSamplePeriod(sampleCount);
    }

    private boolean judgmentTest(List<DtoFixedPoint2Test> fixedPoint2TestList, Integer j, Integer v, String testId) {
        List<DtoFixedPoint2Test> point2Test = fixedPoint2TestList.stream().filter(p -> p.getTimesOrder() >= j
                && p.getSamplePeriod() >= v).collect(toList());
        if (!UUIDHelper.GUID_EMPTY.equals(testId)) {
            point2Test = point2Test.stream().filter(p -> p.getTestId().equals(testId)).collect(toList());
        }
        return point2Test.size() > 0;
    }

    /**
     * 创建周期频次
     *
     * @param i            周期
     * @param j            频次
     * @param sampleFolder 点位
     * @return
     */
    private DtoSamplingFrequency createSamplingFrequency(Integer i, Integer j, Integer v, DtoSampleFolder sampleFolder) {
        DtoSamplingFrequency frequency = new DtoSamplingFrequency();
        frequency.setPeriodCount(i);
        frequency.setTimePerPeriod(j);
        frequency.setSamplePerTime(v);
        frequency.setSampleFolderId(sampleFolder.getId());
        return frequency;
    }

    /**
     * 创建方案详情
     *
     * @param sampleFolder 点位
     * @param frequency    点位频次
     * @param test         测试项目
     * @return
     */
    private DtoSamplingFrequencyTest createSamplingFrequencyTest(DtoSampleFolder sampleFolder, DtoSamplingFrequency frequency, DtoTest test) {
        DtoSamplingFrequencyTest sft = new DtoSamplingFrequencyTest(test);
        sft.setSampleFolderId(sampleFolder.getId());
        sft.setSamplingFrequencyId(frequency.getId());
        return sft;
    }

    /**
     * 创建样品（点位周期）
     *
     * @param templateScheme 模板数据载体
     * @param sampleFolder   点位
     * @param project        项目
     * @param testList       测试项目集合
     * @return
     */
    private DtoSample createSample(DtoLoadScheme templateScheme, DtoSampleFolder sampleFolder, DtoProject project,
                                   List<DtoTest> testList, Integer i, Integer j, Integer v) {
        DtoSample temp = new DtoSample(true);
        temp.setSamplingFrequencyId(templateScheme.getNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY, i.toString(), j.toString(), v.toString()));
        temp.setSampleFolderId(sampleFolder.getId());
        temp.setCycleOrder(i);
        temp.setTimesOrder(j);
        temp.setSampleOrder(v);
        temp.setProjectId(sampleFolder.getProjectId());
        temp.setRedFolderName(this.getFolderName(sampleFolder, i, j, v));
        temp.setSampleTypeId(sampleFolder.getSampleTypeId());
        temp.setInspectedEntId(sampleFolder.getInspectedEntId());
        temp.setInspectedEnt(sampleFolder.getInspectedEnt());
        temp.setInceptTime(new Date());
        temp.setStatus(EnumPRO.EnumSampleStatus.样品未采样.toString());
        temp.setSamplingStatus(EnumPRO.EnumSamplingStatus.需要取样还未取样.getValue());
        temp.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue());
        temp.setAnanlyzeStatus(testList.size() > 0 ? EnumPRO.EnumAnalyzeStatus.不能分析.getValue() : EnumPRO.EnumAnalyzeStatus.不需要分析.getValue());
        temp.setStoreStatus(EnumPRO.EnumStoreStatus.不能存储.getValue());
        temp.setMakeStatus(EnumPRO.EnumMakeStatus.不需要制样.getValue());
        temp.setSampleCategory(EnumPRO.EnumSampleCategory.原样.getValue());
        temp.setRedAnalyzeItems(proService.getAnalyzeItemsByTest(testList));
        return temp;
    }

    /**
     * 创建样品
     *
     * @param temp         复制的样品
     * @param i            周期
     * @param j            频次
     * @param sampleFolder 点位
     * @return
     */
    private DtoSample createSample(DtoLoadScheme templateScheme, DtoSample temp, Integer i, Integer j, Integer v, DtoSampleFolder sampleFolder) {
        DtoSample targetSample = new DtoSample();
        BeanUtils.copyProperties(temp, targetSample);
        targetSample.setCycleOrder(i);
        targetSample.setTimesOrder(j);
        targetSample.setSampleOrder(v);
        targetSample.setSamplingFrequencyId(templateScheme.getNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY,
                String.valueOf(i), String.valueOf(j), String.valueOf(v)));
        targetSample.setId(UUIDHelper.NewID());
        targetSample.setRedFolderName(this.getFolderName(sampleFolder, i, j, v));
        return targetSample;
    }

    /**
     * 匹配保留的点位信息
     *
     * @param project               任务
     * @param sampleFolderList      点位集合
     * @param modifyPointIds        保留的点位
     * @param modifyIds             保留的监测计划
     * @param samplingFrequencyList 方案集合
     * @param pollution             是否污染源
     */
    private void checkSampleFolder(DtoProject project, List<DtoSampleFolder> sampleFolderList, List<String> modifyPointIds,
                                   List<String> modifyIds, List<DtoSamplingFrequency> samplingFrequencyList,
                                   Boolean pollution) {
        //用于复制的模板数据载体
        DtoLoadScheme templateScheme = new DtoLoadScheme();
        //用于插入的数据载体
        DtoLoadScheme targetScheme = new DtoLoadScheme();

        //所有样品
        List<DtoSample> sampleList = sampleRepository.findByProjectId(project.getId());

        //点位信息
        List<DtoFixedpoint> fixedpointList = fixedpointRepository.findByIdIn(modifyPointIds);
        List<DtoFixedPointExpend> fixedPointExpendList = fixedPointExpendRepository.findByFixedPointIdIn(modifyPointIds);
        List<DtoFixedPointProperty> fixedPointPropertyList = new ArrayList<>();
        List<DtoPropertyPoint2Test> propertyPoint2TestList = new ArrayList<>();
        List<DtoProperty2Point> property2PointList = new ArrayList<>();
        List<DtoFixedPoint2Test> fixedPoint2TestList = new ArrayList<>();
        List<String> allTestIds = new ArrayList<>();
        if (!pollution) {//环境质量
            //方案信息
            fixedPointPropertyList = fixedPointPropertyRepository.findByIdIn(modifyIds);
            property2PointList = property2PointRepository.findByPropertyIdInAndFixedPointIdIn(modifyIds, modifyPointIds);
            List<String> p2pIds = property2PointList.stream().map(DtoProperty2Point::getId).collect(Collectors.toList());
            propertyPoint2TestList = propertyPoint2TestRepository.findByPropertyPointIdIn(p2pIds);
            allTestIds = propertyPoint2TestList.stream().map(DtoPropertyPoint2Test::getTestId).collect(Collectors.toList());
        } else {
            fixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointIdIn(modifyPointIds);
            allTestIds = fixedPoint2TestList.stream().map(DtoFixedPoint2Test::getTestId)
                    .distinct().collect(Collectors.toList());
        }
        List<String> levelIds = fixedpointList.stream().map(DtoFixedpoint::getEvaluationLevelId).distinct().collect(Collectors.toList());
        List<DtoEvaluationValue> evaluationValueList = evaluationValueRepository.findByLevelIdIn(levelIds);

        List<DtoTest> testList = new ArrayList<>();
        if (allTestIds.size() > 0) {
            testList = testRepository.findAll(allTestIds);
        }

        List<String> delSamplingFrequencyIds = new ArrayList<>();
        List<DtoTest> validTestList = new ArrayList<>();
        Map<String, Object> checkBase = new HashMap<>();
        //比较已有的点位
        for (DtoFixedpoint dtoFixedpoint : fixedpointList) {
            DtoFixedpoint fixedpoint = new DtoFixedpoint();
            BeanUtils.copyProperties(dtoFixedpoint, fixedpoint);
            DtoFixedPointExpend fixedPointExpend = fixedPointExpendList.stream()
                    .filter(p -> p.getFixedPointId().equals(fixedpoint.getId())).findFirst().orElse(null);
            List<String> propertyIds = property2PointList.stream().filter(p -> p.getFixedPointId().equals(fixedpoint.getId()))
                    .map(DtoProperty2Point::getPropertyId).distinct().collect(Collectors.toList());
            List<DtoFixedPointProperty> fixedPointProperties = fixedPointPropertyList.stream()
                    .filter(p -> propertyIds.contains(p.getId())).collect(Collectors.toList());
            if (!pollution) {
                for (DtoFixedPointProperty fixedPointProperty : fixedPointProperties) {
                    DtoSampleFolder sampleFolder = sampleFolderList.stream().filter(p -> p.getProjectId()
                            .equals(project.getId()) && p.getFixedPointId().equals(fixedpoint.getId())
                            && p.getSampleTypeId().equals(fixedPointProperty.getSampleTypeId())).findFirst().orElse(null);
                    setPropertyPeriod(fixedPointProperty, property2PointList, propertyPoint2TestList);
                    String propertyPointId = property2PointList.stream().filter(p -> p.getPropertyId()
                            .equals(fixedPointProperty.getId()) && p.getFixedPointId().equals(fixedpoint.getId()))
                            .map(DtoProperty2Point::getId).findFirst().orElse(null);
                    fixedpoint.setCycleOrder(fixedPointProperty.getCycleOrder());
                    if (StringUtils.isNotNullAndEmpty(propertyPointId)) {
                        List<String> testIds = propertyPoint2TestList.stream().filter(p -> p.getPropertyPointId().equals(propertyPointId))
                                .map(DtoPropertyPoint2Test::getTestId).distinct().collect(Collectors.toList());
                        validTestList = testList.stream().filter(p -> testIds.contains(p.getId())).collect(Collectors.toList());
                        Integer timesOrder = propertyPoint2TestList.stream().filter(p -> p.getPropertyPointId().equals(propertyPointId))
                                .max(Comparator.comparing(DtoPropertyPoint2Test::getTimesOrder)).map(DtoPropertyPoint2Test::getTimesOrder).get();
                        Integer sampleCount = propertyPoint2TestList.stream().filter(p -> p.getPropertyPointId().equals(propertyPointId))
                                .max(Comparator.comparing(DtoPropertyPoint2Test::getSamplePeriod)).map(DtoPropertyPoint2Test::getSamplePeriod).get();
                        fixedpoint.setTimesOrder(timesOrder);
                        fixedpoint.setSamplePeriod(sampleCount);
                    }
                    checkBase = checkPointFolder(sampleFolder, fixedpoint.getCycleOrder(),
                            fixedpoint.getTimesOrder(), fixedpoint.getSamplePeriod(), samplingFrequencyList, validTestList, sampleList, project,
                            templateScheme, targetScheme, delSamplingFrequencyIds, fixedpoint, fixedPointExpend, property2PointList,
                            testList, propertyPoint2TestList, evaluationValueList);
                    targetScheme = (DtoLoadScheme) checkBase.get("scheme");
                    delSamplingFrequencyIds.addAll((List<String>) checkBase.get("delFrequency"));
                }
            } else {
                setFixpointPeriod(fixedpoint, propertyPoint2TestList);
                DtoSampleFolder sampleFolder = sampleFolderList.stream().filter(p -> p.getProjectId()
                        .equals(project.getId()) && p.getFixedPointId().equals(fixedpoint.getId())
                        && p.getSampleTypeId().equals(fixedpoint.getSampleTypeId())).findFirst().orElse(null);
                List<String> testIds = fixedPoint2TestList.stream().filter(t -> fixedpoint.getId().equals(t.getFixedPointId()))
                        .map(DtoFixedPoint2Test::getTestId).distinct().collect(toList());
                validTestList = testList.stream().filter(p -> testIds.contains(p.getId())).collect(Collectors.toList());
                Integer timesOrder = fixedPoint2TestList.stream().filter(t -> fixedpoint.getId().equals(t.getFixedPointId()))
                        .max(Comparator.comparing(DtoFixedPoint2Test::getTimesOrder)).map(DtoFixedPoint2Test::getTimesOrder).get();
                Integer sampleCount = fixedPoint2TestList.stream().filter(t -> fixedpoint.getId().equals(t.getFixedPointId()))
                        .max(Comparator.comparing(DtoFixedPoint2Test::getSamplePeriod)).map(DtoFixedPoint2Test::getSamplePeriod).get();
                fixedpoint.setTimesOrder(timesOrder);
                fixedpoint.setSamplePeriod(sampleCount);
                checkBase = checkPointFolder(sampleFolder, fixedpoint.getCycleOrder(),
                        fixedpoint.getTimesOrder(), fixedpoint.getSamplePeriod(), samplingFrequencyList,
                        validTestList, sampleList, project, templateScheme, targetScheme, delSamplingFrequencyIds,
                        fixedpoint, fixedPointExpend, property2PointList, testList, propertyPoint2TestList, evaluationValueList);
                targetScheme = (DtoLoadScheme) checkBase.get("scheme");
                delSamplingFrequencyIds.addAll((List<String>) checkBase.get("delFrequency"));
            }
        }
        if (delSamplingFrequencyIds.size() > 0) {
            //删除多余的频次
            delSamplingFrequencyIds = delSamplingFrequencyIds.stream().distinct().collect(Collectors.toList());
            schemeService.deleteSamplingFrequency(project.getId(), delSamplingFrequencyIds);
        }
        this.persistLoadScheme(targetScheme);
    }


    /**
     * 纠正配置方案点位信息
     *
     * @param sampleFolder            点位信息
     * @param cycleOrder              周期
     * @param timesOrder              次数
     * @param samplingFrequencyList   点位周期频次
     * @param validTestList           添加的测试项目
     * @param sampleList              点位样品集合
     * @param project                 项目
     * @param templateScheme          用于复制的模板数据载体
     * @param targetScheme            用于插入的数据载体
     * @param delSamplingFrequencyIds 删除的周期频次
     * @param fixedpoint              配置点位
     * @param fixedPointExpend        点位扩展
     * @param property2PointList      监测计划点位
     * @param testList                测试项目
     * @param propertyPoint2TestList  监测计划点位测试项目
     * @param evaluationValueList     评价标准信息
     * @return 删除的周期频次ids
     */
    private Map<String, Object> checkPointFolder(DtoSampleFolder sampleFolder, Integer cycleOrder, Integer timesOrder, Integer samplePeriod,
                                                 List<DtoSamplingFrequency> samplingFrequencyList, List<DtoTest> validTestList,
                                                 List<DtoSample> sampleList, DtoProject project, DtoLoadScheme templateScheme,
                                                 DtoLoadScheme targetScheme, List<String> delSamplingFrequencyIds,
                                                 DtoFixedpoint fixedpoint, DtoFixedPointExpend fixedPointExpend,
                                                 List<DtoProperty2Point> property2PointList, List<DtoTest> testList,
                                                 List<DtoPropertyPoint2Test> propertyPoint2TestList, List<DtoEvaluationValue> evaluationValueList) {
        Map<String, Object> database;
        Map<String, Object> checkBase = new HashMap<>();
        //判断是否存在当前点位
        if (sampleFolder != null) {
            //判断方案
            for (Integer i = 1; i <= cycleOrder; i++) {
                Integer perNumber = i;
                for (Integer j = 1; j <= timesOrder; j++) {
                    Integer timeNumber = j;
                    for (Integer v = 1; v <= samplePeriod; v++) {
                        Integer samNumber = v;
                        DtoSamplingFrequency samplingFrequency = samplingFrequencyList.stream()
                                .filter(p -> p.getSampleFolderId().equals(sampleFolder.getId()) &&
                                        p.getPeriodCount().equals(perNumber) && p.getTimePerPeriod().equals(timeNumber)
                                        && p.getSamplePerTime().equals(samNumber)).findFirst().orElse(null);
                        if (samplingFrequency != null) {
                            //判断样品
                            DtoSample sample = sampleList.stream().filter(p -> p.getSampleFolderId().equals(sampleFolder.getId())
                                    && p.getCycleOrder().equals(perNumber) && p.getTimesOrder().equals(timeNumber)
                                    && p.getSampleOrder().equals(samNumber)).findFirst().orElse(null);
                            if (sample != null) {
                                //添加数据
                                List<String> oldTestIds = samplingFrequencyTestRepository.findBySamplingFrequencyId(samplingFrequency.getId())
                                        .stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
                                List<String> createTestIds = validTestList.stream().map(DtoTest::getId).collect(Collectors.toList());
                                createTestIds.removeAll(oldTestIds);
                                List<DtoTest> createTestList = validTestList.stream().filter(p -> createTestIds.contains(p.getId()))
                                        .collect(Collectors.toList());
                                if (createTestList.size() > 0) {
                                    DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, sample, createTestList, Boolean.FALSE);
                                    addDto.setIsAddAssociateTest(Boolean.FALSE);
                                    analyseDataService.addAnalyseData(addDto);
                                    //插入周期频次
                                    for (DtoTest test : createTestList) {
                                        DtoSamplingFrequencyTest sft = createSamplingFrequencyTest(sampleFolder, samplingFrequency, test);
                                        targetScheme.addSamplingFrequencyTest(sft);
                                    }
                                }
                            } else {
                                //添加样品
                                DtoSample sam = createSample(templateScheme, sampleFolder, project, validTestList, i, j, v);
                                sam.setSamplingFrequencyId(samplingFrequency.getId());
                                templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, sam.getId(), String.valueOf(i), String.valueOf(j), String.valueOf(v));
                                targetScheme.addSample(sam);
                                //添加数据
                                if (validTestList.size() > 0) {
                                    DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, sam, validTestList, Boolean.FALSE);
                                    addDto.setIsAddAssociateTest(Boolean.FALSE);
                                    analyseDataService.addAnalyseData(addDto);
                                    //插入周期频次
                                    for (DtoTest test : validTestList) {
                                        DtoSamplingFrequencyTest sft = createSamplingFrequencyTest(sampleFolder, samplingFrequency, test);
                                        targetScheme.addSamplingFrequencyTest(sft);
                                    }
                                }
                            }
                        } else {
                            //添加点位频次
                            DtoSamplingFrequency frequency = createSamplingFrequency(i, j, v, sampleFolder);
                            targetScheme.addSamplingFrequency(frequency);
                            templateScheme.putNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY, frequency.getId(), String.valueOf(i),
                                    String.valueOf(j), String.valueOf(v));

                            //添加样品
                            DtoSample targetSample = createSample(templateScheme, sampleFolder, project, validTestList, i, j, v);
                            templateScheme.putNewSampleId(UUIDHelper.GUID_EMPTY, targetSample.getId(), String.valueOf(i),
                                    String.valueOf(j), String.valueOf(v));
                            targetScheme.addSample(targetSample);

                            //添加数据
                            if (validTestList.size() > 0) {
                                DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, targetSample, validTestList, Boolean.FALSE);
                                addDto.setIsAddAssociateTest(Boolean.FALSE);
                                analyseDataService.addAnalyseData(addDto);
                                //插入周期频次
                                for (DtoTest test : validTestList) {
                                    DtoSamplingFrequencyTest sft = createSamplingFrequencyTest(sampleFolder, frequency, test);
                                    targetScheme.addSamplingFrequencyTest(sft);
                                }
                            }
                        }
                    }
                }
            }

            //需要保留属性的最大周期
            Integer maxPeriod = Collections.max(samplingFrequencyList.stream().filter(p -> p.getSampleFolderId().equals(sampleFolder.getId()))
                    .map(DtoSamplingFrequency::getPeriodCount).collect(Collectors.toList()));
            for (int i = cycleOrder + 1; i <= maxPeriod; i++) {
                Integer perCount = i;
                List<String> frequencyIds = samplingFrequencyList.stream()
                        .filter(p -> p.getSampleFolderId().equals(sampleFolder.getId()) && p.getPeriodCount().equals(perCount))
                        .map(DtoSamplingFrequency::getId).collect(Collectors.toList());
                if (frequencyIds.size() > 0) {
                    delSamplingFrequencyIds.addAll(frequencyIds);
                }
            }
            //保留属性最大的次数
            for (int i = 1; i <= cycleOrder; i++) {
                Integer perCount = i;
                Integer maxTimeor = Collections.max(samplingFrequencyList.stream()
                        .filter(p -> p.getSampleFolderId().equals(sampleFolder.getId())
                                && p.getPeriodCount().equals(perCount))
                        .map(DtoSamplingFrequency::getTimePerPeriod).collect(Collectors.toList()));
                for (int j = timesOrder + 1; j <= maxTimeor; j++) {
                    Integer timeor = j;
                    List<String> frequencyIds = samplingFrequencyList.stream()
                            .filter(p -> p.getSampleFolderId().equals(sampleFolder.getId())
                                    && p.getPeriodCount().equals(perCount) && p.getTimePerPeriod().equals(timeor))
                            .map(DtoSamplingFrequency::getId).collect(Collectors.toList());
                    if (frequencyIds.size() > 0) {
                        delSamplingFrequencyIds.addAll(frequencyIds);
                    }
                }
            }
            //保留属性最大的样品数
            for (int i = 1; i <= cycleOrder; i++) {
                Integer perCount = i;
                Integer maxTimeor = Collections.max(samplingFrequencyList.stream()
                        .filter(p -> p.getSampleFolderId().equals(sampleFolder.getId())
                                && p.getPeriodCount().equals(perCount))
                        .map(DtoSamplingFrequency::getTimePerPeriod).collect(Collectors.toList()));
                for (int j = timesOrder + 1; j <= maxTimeor; j++) {
                    Integer timeor = j;
                    Integer maxSamCount = Collections.max(samplingFrequencyList.stream()
                            .filter(p -> p.getSampleFolderId().equals(sampleFolder.getId())
                                    && p.getPeriodCount().equals(perCount) && p.getTimePerPeriod().equals(timeor))
                            .map(DtoSamplingFrequency::getSamplePerTime).collect(Collectors.toList()));
                    for (int v = samplePeriod + 1; v <= maxSamCount; v++) {
                        Integer samCount = v;
                        List<String> frequencyIds = samplingFrequencyList.stream()
                                .filter(p -> p.getSampleFolderId().equals(sampleFolder.getId()) && p.getPeriodCount().equals(perCount)
                                        && p.getTimePerPeriod().equals(timeor) && p.getSamplePerTime().equals(samCount))
                                .map(DtoSamplingFrequency::getId).collect(Collectors.toList());
                        if (frequencyIds.size() > 0) {
                            delSamplingFrequencyIds.addAll(frequencyIds);
                        }
                    }
                }
            }
        } else {
            //缺少的点位
            database = this.saveSampleFolder(project, fixedpoint, new DtoFixedPointProperty(), fixedPointExpend,
                    property2PointList, propertyPoint2TestList, evaluationValueList, testList, targetScheme, new ArrayList<>());
            targetScheme = (DtoLoadScheme) database.get("scheme");
            List<DtoEvaluationRecord> recordList = (List<DtoEvaluationRecord>) database.get("record");
            if (recordList.size() > 0) {
                evaluationRecordRepository.save(recordList);
            }
        }
        //this.persistLoadScheme(targetScheme);
        checkBase.put("scheme", targetScheme);
        checkBase.put("delFrequency", delSamplingFrequencyIds);
        return checkBase;
    }

    /**
     * 插入数据
     *
     * @param load 委托数据载体
     */
    private void persistLoadScheme(DtoLoadScheme load) {
        if (load.getSample().size() > 0) {
            comRepository.insert(load.getSample());
        }
        if (load.getSampleFolder().size() > 0) {
            comRepository.insert(load.getSampleFolder());
        }
        if (load.getSamplingFrequency().size() > 0) {
//            comRepository.insert(load.getSamplingFrequency());
            samplingFrequencyService.save(load.getSamplingFrequency());
        }
        if (load.getSamplingFrequencyTest().size() > 0) {
            comRepository.insert(load.getSamplingFrequencyTest());
        }
        if (load.getAnalyseData().size() > 0) {
            comRepository.insert(load.getAnalyseData());
            testService.incrementOrderNum(load.getAnalyseData().stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList()));
            dimensionService.incrementOrderNum(load.getAnalyseData().stream().map(DtoAnalyseData::getDimensionId).distinct().collect(toList()));
        }
    }

    /**
     * 插入数据
     *
     * @param load 委托数据载体
     */
    private void persistLoadByLimsRepository(DtoLoadScheme load) {
        if (load.getSample().size() > 0) {
            sampleRepository.save(load.getSample());
        }
        if (load.getSampleFolder().size() > 0) {
            sampleFolderRepository.save(load.getSampleFolder());
        }
        if (load.getSamplingFrequency().size() > 0) {
            samplingFrequencyService.save(load.getSamplingFrequency());
        }
        if (load.getSamplingFrequencyTest().size() > 0) {
            samplingFrequencyTestRepository.save(load.getSamplingFrequencyTest());
        }
        if (load.getAnalyseData().size() > 0) {
            analyseDataRepository.save(load.getAnalyseData());
            testService.incrementOrderNum(load.getAnalyseData().stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList()));
            dimensionService.incrementOrderNum(load.getAnalyseData().stream().map(DtoAnalyseData::getDimensionId).distinct().collect(toList()));
        }
    }

    /**
     * 获取数据的拷贝
     *
     * @param sourceAnalyseData 源数据
     * @return 拷贝数据
     */
    private DtoAnalyseData getSchemeCloneAnalyseData(DtoAnalyseData sourceAnalyseData) {
        DtoAnalyseData targetAnalyseData = new DtoAnalyseData();
        BeanUtils.copyProperties(sourceAnalyseData, targetAnalyseData);
        targetAnalyseData.setId(UUIDHelper.NewID());
        targetAnalyseData.setWorkSheetId(UUIDHelper.GUID_EMPTY);
        targetAnalyseData.setWorkSheetFolderId(UUIDHelper.GUID_EMPTY);
        targetAnalyseData.setSubId(UUIDHelper.GUID_EMPTY);
        targetAnalyseData.setReceiveSubId(UUIDHelper.GUID_EMPTY);
        targetAnalyseData.setTestValue("");
        targetAnalyseData.setTestValueD(BigDecimal.ZERO);
        targetAnalyseData.setTestOrignValue("");
        targetAnalyseData.setTestValueDstr("");
        targetAnalyseData.setStatus(EnumPRO.EnumAnalyseDataStatus.未测.toString());
        targetAnalyseData.setDataStatus(EnumPRO.EnumAnalyseDataStatus.未测.getValue());
        targetAnalyseData.setDataChangeStatus(EnumPRO.EnumDataChangeStatus.未变更.getValue());
        targetAnalyseData.setAnalyzeTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        targetAnalyseData.setDataInputTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        analyseDataService.defaultDateValue(targetAnalyseData);
        targetAnalyseData.setIsDataEnabled(false);
        targetAnalyseData.setCreateDate(new Date());
        targetAnalyseData.setCreator(PrincipalContextUser.getPrincipal().getUserId());
        targetAnalyseData.setModifyDate(new Date());
        targetAnalyseData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
        return targetAnalyseData;
    }

    /**
     * 获取样品点位名称
     *
     * @param sampleFolder  点位
     * @param periodCount   周期
     * @param timePerPeriod 次数
     * @return 返回样品点位名称
     */
    private String getFolderName(DtoSampleFolder sampleFolder, Integer periodCount, Integer timePerPeriod, Integer samplePeriod) {
//        String folderName = String.format("%s%s", StringUtils.isNotNullAndEmpty(sampleFolder.getWatchSpot()) ?
//                sampleFolder.getWatchSpot() : "", StringUtils.isNotNullAndEmpty(sampleFolder.getFolderCode()) ? "_" + sampleFolder.getFolderCode() : "");
        String folderName = String.format("%s", StringUtils.isNotNullAndEmpty(sampleFolder.getWatchSpot()) ?
                sampleFolder.getWatchSpot() : "");
        if (StringUtils.isNotNullAndEmpty(folderName)) {
            folderName = String.format("%s(%d-%d-%d)", folderName, periodCount, timePerPeriod, samplePeriod);
        }
        return folderName;
    }

    //endregion

    //region 新增属性相关数据

    /**
     * 根据计划创建方案
     *
     * @param createIds 计划ids
     * @param project   任务id
     * @param pollution 是否污染源
     */
    private void createProperty(List<String> createIds, DtoProject project, Boolean pollution) {
        List<String> createFolderIds = new ArrayList<>(createIds);
        //添加方案点位
        if (!pollution) {
            createFolderIds = property2PointRepository.findByPropertyIdIn(createIds).stream()
                    .map(Property2Point::getFixedPointId).collect(Collectors.toList());
            //查询当前任务已经添加的点位，如果已经存在就不重复添加
            List<String> exitFolderIds = sampleFolderRepository.findByProjectId(project.getId())
                    .parallelStream().map(DtoSampleFolder::getFixedPointId).collect(Collectors.toList());
            createFolderIds.removeAll(exitFolderIds);
        }
        this.addSampleFolder(project, createFolderIds, createIds, pollution);
    }

    //endregion

    /**
     * 通过污染源点位生成任务方案（例行监测）
     *
     * @param project   任务
     * @param pIds      污染源点位id
     * @param pollution 是否污染源
     */
    private void savePollutionMonitorScheme(DtoProject project, List<String> pIds, Boolean pollution, Boolean scheme) {
        //任务id
        String projectId = project.getId();

        List<DtoProject2FixedProperty> project2FixedPropertyList = project2FixedPropertyRepository.findByProjectId(projectId);
        if (scheme) {
            //获取该任务老的污染源点位
            List<String> oldPropertyIds = project2FixedPropertyList.stream().map(DtoProject2FixedProperty::getFixedPropertyId).distinct().collect(Collectors.toList());

            //删除的污染配置点位id
            List<String> removeIds = new ArrayList<>(oldPropertyIds);
            removeIds.removeAll(pIds);

            //保留的污染源配置点位id
            List<String> modifyIds = new ArrayList<>(oldPropertyIds);
            modifyIds.removeAll(removeIds);

            //新增的污染源配置点位id
            List<String> createIds = new ArrayList<>(pIds);
            createIds.removeAll(oldPropertyIds);

            //获取任务下的点位
            List<DtoSampleFolder> sampleFolderList = sampleFolderRepository.findByProjectId(projectId);
            List<String> samPointIds = sampleFolderList.stream().map(DtoSampleFolder::getFixedPointId).distinct().collect(Collectors.toList());

            //删除污染源配置点位的相关数据
            List<DtoSampleFolder> delFolderList = sampleFolderList.stream().filter(p -> removeIds.contains(p.getFixedPointId())).collect(Collectors.toList());

            //删除点位
            deleteSampleFolder(delFolderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList()), projectId);


            //保留属性相关数据
            this.modifyProperty(modifyIds, samPointIds, sampleFolderList, project, pollution);

            //新增属性相关数据
            this.createProperty(createIds, project, pollution);
        }
        //删除原有项目属性关系
        project2FixedPropertyService.delete(project2FixedPropertyList);

        //保存现有项目属性关系
        List<DtoProject2FixedProperty> dtoProject2FixedPropertyList = new ArrayList<>();
        for (String id : pIds) {
            DtoProject2FixedProperty dtoProject2FixedProperty = new DtoProject2FixedProperty();
            dtoProject2FixedProperty.setProjectId(project.getId());
            dtoProject2FixedProperty.setFixedPropertyId(id);
            dtoProject2FixedPropertyList.add(dtoProject2FixedProperty);
        }
        project2FixedPropertyService.save(dtoProject2FixedPropertyList);
        if (scheme) {
            //纠正项目状态
            proService.checkProject(Collections.singletonList(projectId));
        }

    }

    /**
     * 获取污染源配置点位信息
     *
     * @param projectId    任务id
     * @param entId        企业id
     * @param enableStatus 是否启用(-1：所有 1：启用 0：不启用)
     * @param showConfig   显示配置查询
     */
    @Override
    public List<DtoFixedpoint> getPollutionFolder(String projectId, String entId, Integer enableStatus, Boolean showConfig) {
        List<DtoFixedpoint> fixedpointList = new ArrayList<>();
        List<DtoEnterprise> enterpriseList = new ArrayList<>();
        DtoEnterprise enterprise = new DtoEnterprise();
        if (enableStatus == null) {
            enableStatus = -1;
        }
        Boolean status = enableStatus == 1;
        List<DtoProject2FixedProperty> project2FixedPropertyList = project2FixedPropertyRepository.findByProjectId(projectId);
        if (!showConfig) {
            if (StringUtils.isNotNullAndEmpty(entId) && !entId.equals(UUIDHelper.GUID_EMPTY)) {
                enterprise = enterpriseRepository.findOne(entId);
                if (enableStatus == -1) {
                    fixedpointList = fixedpointRepository.findByEnterpriseIdAndPointType(entId, EnumMonitor.EnumPointType.污染源.getValue());
                } else {
                    fixedpointList = fixedpointRepository.findByEnterpriseIdAndIsEnabledAndPointType(entId, status, EnumMonitor.EnumPointType.污染源.getValue());
                }
            } else {
                if (enableStatus == -1) {
                    fixedpointList = fixedpointRepository.findByPointType(EnumMonitor.EnumPointType.污染源.getValue());
                } else {
                    fixedpointList = fixedpointRepository.findByIsEnabledAndPointType(status, EnumMonitor.EnumPointType.污染源.getValue());
                }
            }
        } else {
            List<String> pointIds = project2FixedPropertyList.stream().map(DtoProject2FixedProperty::getFixedPropertyId).distinct().collect(Collectors.toList());
            fixedpointList = fixedpointRepository.findByIdIn(pointIds);
        }

        List<String> entIds = fixedpointList.stream().map(DtoFixedpoint::getEnterpriseId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(entIds)) {
            enterpriseList = enterpriseRepository.findAll(entIds);
        }

        List<String> fixedPointIds = fixedpointList.stream().map(DtoFixedpoint::getId).collect(Collectors.toList());

        List<DtoFixedPoint2Test> fixedPoint2TestList = new ArrayList<>();
        if (StringUtil.isNotEmpty(fixedPointIds)) {
            fixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointIdIn(fixedPointIds);
        }

        List<String> testIds = fixedPoint2TestList.stream().map(DtoFixedPoint2Test::getTestId).collect(Collectors.toList());
        List<DtoTest> testList = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testRepository.findAll(testIds);
        }

        //点位类型字典列表
        List<DtoCode> folderTypeCodeList = codeService.findCodes(LimCodeHelper.MonitorCodeTypes.POLLUTANT_FOLDER_TYPE);
        if (fixedpointList.size() > 0) {
            for (DtoFixedpoint fixedpoint : fixedpointList) {
                DtoEnterprise enter = enterpriseList.stream().filter(p -> p.getId().equals(fixedpoint.getEnterpriseId())).findFirst().orElse(null);
                if (enter != null) {
                    fixedpoint.setEnterpriseName(enter.getName());
                } else {
                    if (StringUtils.isNotNullAndEmpty(entId) && !entId.equals(UUIDHelper.GUID_EMPTY)) {
                        fixedpoint.setEnterpriseName(enterprise.getName());
                    }
                }
                Optional<DtoCode> folderTypeOptional = folderTypeCodeList.parallelStream().filter(p -> p.getDictCode().equals(fixedpoint.getFolderType()))
                        .findFirst();
                folderTypeOptional.ifPresent(dtoCode -> fixedpoint.setFolderTypeName(dtoCode.getDictName()));
                List<DtoProject2FixedProperty> project2FixedProperties = project2FixedPropertyList.stream()
                        .filter(p -> p.getFixedPropertyId().equals(fixedpoint.getId())).collect(Collectors.toList());
                fixedpoint.setProjectRelevance(project2FixedProperties.size() > 0);
                List<String> tIds = fixedPoint2TestList.stream().filter(p -> p.getFixedPointId().equals(fixedpoint.getId()))
                        .map(DtoFixedPoint2Test::getTestId).distinct().collect(Collectors.toList());
                fixedpoint.setAnalyzeItemsCount(tIds.size());
                List<String> anaItemName = testList.stream().filter(p -> tIds.contains(p.getId()))
                        .map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
                fixedpoint.setAnalyzeItems(org.apache.commons.lang.StringUtils.join(anaItemName, ","));
            }
        }
        return fixedpointList;
    }

    /**
     * 获取例行监测计划信息(树)
     *
     * @param key   关键字检索（计划名称、子计划名称）
     * @param year  年份
     * @param month 月份
     */
    @Override
    public List<TreeNode> getPointProperty(String key, Integer year, Integer month) {
        List<DtoFixedPointProperty> fixedPointPropertyList;
        if (month == -1) {
            fixedPointPropertyList = fixedPointPropertyRepository.findByYear(year);
        } else {
            fixedPointPropertyList = fixedPointPropertyRepository.findByYearAndMonth(year, month);
        }
        //通过监测计划名称过滤内容
        List<DtoFixedPointProperty> planPropertyList = new ArrayList<>(fixedPointPropertyList);
        if (StringUtils.isNotNullAndEmpty(key)) {
            planPropertyList = fixedPointPropertyList.stream().filter(p -> p.getPropertyName().contains(key)).collect(Collectors.toList());
        }
        //复核的监测子计划的监测计划
        List<String> parentIds = planPropertyList.stream().map(DtoFixedPointProperty::getParentId)
                .filter(parentId -> !parentId.equals(UUIDHelper.GUID_EMPTY)).distinct().collect(Collectors.toList());
        //复核的监测计划
        List<String> mainIds = planPropertyList.stream().filter(p -> p.getParentId().equals(UUIDHelper.GUID_EMPTY))
                .map(DtoFixedPointProperty::getId).distinct().collect(Collectors.toList());
        //所有复核的监测计划的子计划
        List<String> mainPlanIds = fixedPointPropertyList.stream().filter(p -> mainIds.contains(p.getParentId()))
                .map(DtoFixedPointProperty::getId).distinct().collect(Collectors.toList());
        //所有复核要求的监测计划
        List<String> propertyIds = planPropertyList.stream().map(DtoFixedPointProperty::getId).collect(Collectors.toList());
        //没有监测计划的监测子计划
        parentIds.removeAll(propertyIds);
        //没有监测子计划的监测计划
        mainPlanIds.removeAll(propertyIds);

        //遗漏的监测计划ids
        List<String> outIds = new ArrayList<>();
        outIds.addAll(parentIds);
        outIds.addAll(mainPlanIds);
        //缺少的监测计划
        List<DtoFixedPointProperty> outPropertyList = fixedPointPropertyRepository.findByIdIn(outIds);

        //项目上存在的监测子计划
        List<DtoProject2FixedProperty> project2FixedPropertyList = project2FixedPropertyRepository.findByFixedPropertyIdIn(propertyIds);

        planPropertyList.addAll(outPropertyList);

        for (DtoFixedPointProperty fixedPointProperty : planPropertyList) {
            //监测计划名字
            if (fixedPointProperty.getParentId().equals(UUIDHelper.GUID_EMPTY)) {
                List<Integer> monthList = planPropertyList.stream()
                        .filter(p -> p.getParentId().equals(fixedPointProperty.getId())).map(DtoFixedPointProperty::getMonth)
                        .distinct().sorted().collect(Collectors.toList());
                fixedPointProperty.setPropertyName(String.format("%s（%s）", fixedPointProperty.getPropertyName(),
                        org.apache.commons.lang.StringUtils.join(monthList, "、")));
            } else {
                fixedPointProperty.setPropertyName(String.format("%s（%d）", fixedPointProperty.getPropertyName(), fixedPointProperty.getMonth(), fixedPointProperty.getMonth()));
            }
            //是否与任务关联
            List<DtoProject2FixedProperty> project2FixedProperties = project2FixedPropertyList.stream()
                    .filter(p -> p.getFixedPropertyId().equals(fixedPointProperty.getId())).collect(Collectors.toList());
            if (project2FixedProperties.size() > 0) {
                fixedPointProperty.setProjectRelevance("是");
            } else {
                fixedPointProperty.setProjectRelevance("否");
            }
        }
        return this.tree(planPropertyList);
    }

    /**
     * 已选监测计划列表
     *
     * @param projectId 任务id
     * @return 监测计划集合
     */
    @Override
    public List<DtoFixedPointProperty> getProjectProperty(String projectId) {
        List<String> propertyIds = project2FixedPropertyRepository.findByProjectId(projectId).stream()
                .map(Project2FixedProperty::getFixedPropertyId).collect(Collectors.toList());
        //监测子计划
        List<DtoFixedPointProperty> fixedPointPropertyList = fixedPointPropertyRepository.findByIdIn(propertyIds);
        List<String> parentIds = fixedPointPropertyList.stream().map(FixedPointProperty::getParentId).collect(Collectors.toList());
        //监测计划
        List<DtoFixedPointProperty> parentPropertyList = fixedPointPropertyRepository.findByIdIn(parentIds);
        for (DtoFixedPointProperty fixedPointProperty : fixedPointPropertyList) {
            parentPropertyList.stream().filter(p -> p.getId()
                    .equals(fixedPointProperty.getParentId())).findFirst().ifPresent(property -> fixedPointProperty.setPropertyName(String.format("%s-%s(%d)", property.getPropertyName(),
                    fixedPointProperty.getPropertyName(), fixedPointProperty.getMonth())));
        }
        return fixedPointPropertyList;
    }

    /**
     * 显示监测计划树
     *
     * @param list 监测计划
     * @return 监测计划树
     */
    private List<TreeNode> tree(List<DtoFixedPointProperty> list) {
        ArrayList<TreeNode> nodeLists = new ArrayList<>();//最终返回的监测计划-子计划树
        String planId = UUIDHelper.NewID();
        //将监测计划转换成树类型
        TreeNode node = new TreeNode();
        node.setId(planId);
        node.setParentId(UUIDHelper.GUID_EMPTY);
        node.setCategory(null);
        node.setLabel("监测计划");
        node.setOrderNum(0);
        node.setType("PropertyPlan");
        node.setChildren(new ArrayList<>());
        nodeLists.add(node);
        ArrayList<TreeNode> nodes = new ArrayList<>();
        for (DtoFixedPointProperty var : list.stream().filter(p -> p.getParentId()
                .equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList())) {
            node = new TreeNode();
            node.setId(var.getId());
            if (var.getParentId().equals(UUIDHelper.GUID_EMPTY)) {
                node.setParentId(planId);
            } else {
                node.setParentId(var.getParentId());
            }
            node.setCategory(null);
            node.setLabel(var.getPropertyName());
            node.setOrderNum(var.getOrderNum());
            node.setExtent1(var.getProjectRelevance());
            String type = "PropertyPlan" + "~" + var.getParentId();
            node.setType(type);
            node.setChildren(new ArrayList<>());
            nodes.add(node);
        }

        //遍历检测类型,将list类型转换成tree树
        //排序
        List<TreeNode> lists = nodes.stream().sorted(Comparator.comparing(TreeNode::getOrderNum).reversed()).collect(Collectors.toList());
        for (TreeNode var : lists) {
            List<DtoFixedPointProperty> planList = list.stream().filter(p -> p.getParentId().equals(var.getId()))
                    .sorted(Comparator.comparing(DtoFixedPointProperty::getMonth).reversed().thenComparing(DtoFixedPointProperty::getOrderNum).reversed()).collect(Collectors.toList());
            if (planList.size() > 0) {
                var.setIsLeaf(false);
                int count = (int) planList.stream().filter(p -> p.getProjectRelevance().equals("是")).count();
                if (count == planList.size()) {
                    var.setExtent1("是");
                }
                for (DtoFixedPointProperty plan : planList) {
                    node = new TreeNode();
                    node.setId(plan.getId());
                    node.setParentId(plan.getParentId());
                    node.setCategory(null);
                    node.setLabel(plan.getPropertyName());
                    node.setOrderNum(plan.getOrderNum());
                    node.setExtent1(plan.getProjectRelevance());
                    String type = "PropertyPlan" + "~" + plan.getParentId();
                    node.setType(type);
                    node.setChildren(new ArrayList<>());
                    var.getChildren().add(node);
                }
            } else {
                var.setIsLeaf(true);
            }
        }
        for (TreeNode var : nodeLists) {
            var.setChildren(lists);
        }
        return lists;
    }
    //endregion

    /**
     * 项目提交，委托类需核定编制方案，送样类核定样品及数据
     *
     * @param projectId 项目id
     * @param errorInfo 错误信息
     */
    private void checkProjectSubmitCondition(String projectId, DtoErrorInfo errorInfo) {
        DtoProject project = repository.findOne(projectId);
        String projectTypeCode = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
        if (projectTypeCode.equals(EnumProjectType.委托类.getValue())
                || projectTypeCode.equals(EnumProjectType.例行类.getValue())
                || EnumProjectType.全流程.getValue().equals(projectTypeCode)) {
            DtoProjectPlan plan = projectPlanRepository.findByProjectId(projectId);
            if (plan.getIsMakePlan()) {
                Integer count = sampleRepository.countByProjectId(projectId);
                if (count.equals(0)) {
                    errorInfo.add(project.getProjectCode(), EnumErrorCode.项目未编方案.getCode());
                }
                //判断某次数是否有测试项目
                checkSampleScheme(projectId);
            }
        } else if (projectTypeCode.equals(EnumProjectType.送样类.getValue())) {
            List<DtoReceiveSampleRecord> recordList = receiveSampleRecordRepository.findByProjectId(projectId);
            DtoReceiveSampleRecord record = StringUtil.isEmpty(recordList) ? null : recordList.get(0);
            if (StringUtil.isNotNull(record)) {
                try {
                    receiveSampleRecordService.canSubmitReceiveRecord(record.getId(), EnumReceiveSubmitType.项目登记提交.getCode());
                } catch (BaseException ex) {
                    errorInfo.add(project.getProjectCode(), EnumErrorCode.项目无指标.getCode());
                }
            }
            if (checkSampleParamRequired(Collections.singletonList(projectId))) {
                throw new BaseException("项目样品中存在必填项的参数未填写");
            }
        }
    }

    /**
     * 项目报告完成，如果不满足签发报告数等于实际报告数并且大于等于要求报告数，需要有相应提示信息
     *
     * @param projectId 项目id
     * @param errorInfo 错误信息
     */
    @Override
    public void checkReportCondition(String projectId, DtoErrorInfo errorInfo) {
        DtoProject project = repository.findOne(projectId);
        List<DtoReport> reports = reportRepository.findByProjectId(projectId);
        if (StringUtil.isNull(reports) || reports.size() == 0) {
            errorInfo.add(project.getProjectCode(), EnumErrorCode.项目报告不足.getCode());
        }
        if (reports.stream().anyMatch(p -> !p.getStatus().equals(EnumReportState.已签发.toString()))) {
            errorInfo.add(project.getProjectCode(), EnumErrorCode.项目报告未签.getCode());
        }
        if (StringUtils.isNotNullAndEmpty(project.getReportNum()) && MathUtil.isNumeral(project.getReportNum())) {
            if (MathUtil.getBigDecimal(project.getReportNum()).compareTo(BigDecimal.valueOf(reports.size())) > 0) {
                errorInfo.add(project.getProjectCode(), EnumErrorCode.项目报告不足.getCode());
            }
        }
    }

    /**
     * 项目的非正常办结（存在未检毕的样品），需要有一个不可逆的警告提示（项目无法还原成办结时的状态）
     *
     * @param projectId 项目id
     * @param errorInfo 错误信息
     */
    private void checkEndCondition(String projectId, DtoErrorInfo errorInfo) {
        DtoProject project = this.findOne(projectId);
        if (StringUtils.isNotNullAndEmpty(project.getJson())) {
            Map<String, Object> jsonMap = JsonIterator.deserialize(project.getJson(), Map.class);
            if (jsonMap.containsKey("analyzeSummary") && StringUtil.isNotNull(jsonMap.get("analyzeSummary"))) {
                String[] analyzeSummaryArr = String.valueOf(jsonMap.get("analyzeSummary")).split("/");
                if (analyzeSummaryArr.length == 2 && !analyzeSummaryArr[0].equals(analyzeSummaryArr[1])) {
                    errorInfo.add(project.getProjectCode(), EnumErrorCode.项目未检毕.getCode());
                }
            }
        }
    }

    /**
     * 检查该方案是有测试项目
     *
     * @param projectId 项目id
     */
    private void checkSampleScheme(String projectId) {
        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select a.id, a.redFolderName from DtoSample as a ");
        stringBuilder.append(" where 1=1 ");
        stringBuilder.append(" and a.projectId=:projectId");
        values.put("projectId", projectId);
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and a.orgId=:orgId ");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and a.isDeleted = 0 ");
        List<Object[]> sampleIdFolderNameList = comRepository.find(stringBuilder.toString(), values);
        List<String> sampleIdList = new ArrayList<>();
        Map<String, String> sampleId2FolderNameMap = new HashMap<>();
        for (Object[] arr : sampleIdFolderNameList) {
            String sampleId = (String) arr[0];
            String redFolderName = (String) arr[1];
            sampleIdList.add(sampleId);
            sampleId2FolderNameMap.put(sampleId, redFolderName);
        }

        if (StringUtil.isNotEmpty(sampleIdList)) {
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList);
            List<String> sampleIdsWithAnaData = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            //移除有对应分析数据的样品id,剩下的样品id是没有分析数据的
            sampleIdList.removeAll(sampleIdsWithAnaData);
            if (StringUtil.isNotEmpty(sampleIdList)) {
                throw new BaseException(String.format("点位%s未配置测试项目，请确认！", sampleId2FolderNameMap.getOrDefault(sampleIdList.get(0), "")));
            }
        }
    }
    //#endregion

    private List<String> getChangeContent(Map<String, Map<String, Object>> map, EnumProjectField... fields) {
        String format = "</br>%s由'%s',修改为'%s'";
        List<DtoPerson> personList = personService.findAll();
        List<String> contents = new ArrayList<>();
        for (EnumProjectField field : fields) {
            if (map.containsKey(field.getValue())) {
                String from = StringUtil.isNull(map.get(field.getValue()).get("from")) ? "" : map.get(field.getValue()).get("from").toString();
                String to = StringUtil.isNull(map.get(field.getValue()).get("to")) ? "" : map.get(field.getValue()).get("to").toString();
                switch (field) {
                    case 登记人员:
                    case 项目负责人:
                    case 编制报告人:
                        contents.add(String.format(format, field.toString(),
                                personList.stream().filter(p -> p.getId().equals(from)).map(DtoPerson::getCName).findFirst().orElse(""),
                                personList.stream().filter(p -> p.getId().equals(to)).map(DtoPerson::getCName).findFirst().orElse("")));
                        break;

                    case 是否着重关注:
                    case 是否提醒:
                        contents.add(String.format(format, field.toString(), from.toLowerCase().equals("false") ? "否" : "是", to.toLowerCase().equals("false") ? "否" : "是"));
                        break;

                    case 项目等级:
                        contents.add(String.format(format, field.toString(),
                                MathUtil.isInteger(from) ? EnumProjectGrade.getByValue(Integer.valueOf(from)) : "",
                                MathUtil.isInteger(to) ? EnumProjectGrade.getByValue(Integer.valueOf(to)) : ""));
                        break;

                    case 监测方式:
                        List<DtoCode> methods = codeService.findCodes(ProCodeHelper.MONITOR_METHODS);
                        contents.add(String.format(format, field.toString(),
                                methods.stream().filter(p -> p.getDictCode().equals(from)).map(DtoCode::getDictName).findFirst().orElse(""),
                                methods.stream().filter(p -> p.getDictCode().equals(to)).map(DtoCode::getDictName).findFirst().orElse("")));
                        break;

                    case 保存条件:
                        List<DtoCode> conditions = codeService.findCodes(ProCodeHelper.SAVE_CONDITION);
                        contents.add(String.format(format, field.toString(),
                                conditions.stream().filter(p -> p.getDictCode().equals(from)).map(DtoCode::getDictName).findFirst().orElse(""),
                                conditions.stream().filter(p -> p.getDictCode().equals(to)).map(DtoCode::getDictName).findFirst().orElse("")));
                        break;

                    case 交付方式:
                        List<DtoCode> postMethods = codeService.findCodes(ProCodeHelper.POST_METHOD);
                        contents.add(String.format(format, field.toString(),
                                postMethods.stream().filter(p -> p.getDictValue().equals(from)).map(DtoCode::getDictName).findFirst().orElse("未设置"),
                                postMethods.stream().filter(p -> p.getDictValue().equals(to)).map(DtoCode::getDictName).findFirst().orElse("未设置")));
                        break;

                    case 监测类型:
                        List<DtoCode> sampleTypes = codeService.findCodes(ProCodeHelper.SAMPLE_TYPE);
                        contents.add(String.format(format, field.toString(),
                                String.join(",", sampleTypes.stream().filter(p -> Arrays.asList(from.split(",")).contains(p.getDictCode())).map(DtoCode::getDictName).collect(Collectors.toList())),
                                String.join(",", sampleTypes.stream().filter(p -> Arrays.asList(to.split(",")).contains(p.getDictCode())).map(DtoCode::getDictName).collect(Collectors.toList()))));
                        break;

                    default:
                        contents.add(String.format(format, field.toString(), from, to));
                        break;
                }
            }
        }
        return contents;
    }

    /**
     * 项目进度，点位地图数据查询
     *
     * @param projectId 项目id
     * @param keyWord   查询关键字
     * @return 封装查询结果
     */
    @Override
    public List<DtoSampleFolderMap> getSampleFolderMapOfProject(String projectId, String keyWord) {
        //查询项目下的所有点位
        List<DtoSampleFolder> sampleFoldersOfProject = sampleFolderRepository.findByProjectId(projectId);
        //根据关键字过滤点位名称
        if (StringUtil.isNotEmpty(keyWord)) {
            sampleFoldersOfProject = sampleFoldersOfProject.stream()
                    .filter(p -> p.getWatchSpot().contains(keyWord)).collect(Collectors.toList());
        }
        //根据点位名称获取采样签到数据
        List<String> sampleFolderIds = sampleFoldersOfProject.parallelStream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        List<DtoFolderSign> folderSigns = new ArrayList<>();

        //2021-12-27 存在点位是送样类型的点位，经纬度是在样品上的，所以点位经纬度获取样品上随便一个经纬度
        List<DtoSample> sampleList = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleFolderIds)) {
            folderSigns = folderSignRepository.findBySampleFolderIdIn(sampleFolderIds);
            sampleList = sampleRepository.findBySampleFolderIdIn(sampleFolderIds);
        }

        //定义返回结果
        List<DtoSampleFolderMap> result = new ArrayList<>();
        for (DtoSampleFolder sampleFolder : sampleFoldersOfProject) {
            DtoSampleFolderMap map = new DtoSampleFolderMap();
            map.setSampleFolderId(sampleFolder.getId());
            map.setSampleFolderName(sampleFolder.getWatchSpot());
            map.setSampleFolderLat(sampleFolder.getLat());
            map.setSampleFolderLon(sampleFolder.getLon());
            //如果点位上没有经纬度，那么从样品上找
            List<DtoSample> samples = sampleList.parallelStream().filter(p -> sampleFolder.getId().equals(p.getSampleFolderId())
                    && (StringUtil.isNotEmpty(p.getLon()) || StringUtil.isNotEmpty(p.getLat()))).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(samples) && (!StringUtil.isNotEmpty(sampleFolder.getLat())
                    && !StringUtil.isNotEmpty(sampleFolder.getLon()))) {
                DtoSample sample = samples.get(0);
                map.setSampleFolderLat(sample.getLat());
                map.setSampleFolderLon(sample.getLon());
            }
            //获取点位签到信息
            List<DtoFolderSign> folderSignsOfFolderItem = folderSigns.parallelStream()
                    .filter(f -> f.getSampleFolderId().equals(sampleFolder.getId()))
                    .collect(Collectors.toList());
            //取点位上最新一条的签到信息
            if (StringUtil.isNotEmpty(folderSignsOfFolderItem)) {
                folderSignsOfFolderItem.sort(Comparator.comparing(DtoFolderSign::getSignTime).reversed());
                DtoFolderSign tempSign = folderSignsOfFolderItem.get(0);
                map.setSignLat(tempSign.getSignLat());
                map.setSignLon(tempSign.getSignLon());
                map.setRemark(tempSign.getSignTip());
            }
            //把有点位经纬度或者签到经纬度的数据返回
            if (StringUtil.isNotEmpty(map.getSampleFolderLat())
                    || StringUtil.isNotEmpty(map.getSampleFolderLon())
                    || StringUtil.isNotEmpty(map.getSignLon())
                    || StringUtil.isNotEmpty(map.getSignLat())) {
                result.add(map);
            }
        }
        result.sort(Comparator.comparing(DtoSampleFolderMap::getSampleFolderName));
        return result;
    }

    /**
     * 导入订单方案
     *
     * @param quotationDetailIds 详情ids
     * @param projectId          项目id
     */
    @Transactional
    @Override
    public void matchOrderScheme(List<String> quotationDetailIds, String projectId) {
        matchOrderSchemeFolder(quotationDetailIds, projectId, new ArrayList<>());
    }

    /**
     * 导入订单方案-按点位导入
     *
     * @param quotationDetailIds 详情ids
     * @param projectId          项目id
     * @param filterList         点位
     */
    @Transactional
    @Override
    public void matchOrderSchemeFolder(List<String> quotationDetailIds, String projectId, List<String> filterList) {
        //先删除项目下所有方案
        DtoProject project = repository.findOne(projectId);
        //删除点位、频次、样品、送样单、工作单、数据
        if (filterList.size() == 0) {
            //删除点位、频次、样品、送样单、工作单、数据
            List<DtoSampleFolder> samFolderList = sampleFolderRepository.findByProjectId(projectId);
            if (samFolderList.size() > 0) {
                this.deleteSampleFolder(samFolderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList()), projectId);
            }
        }
        //外部送样的送样单
        DtoReceiveSampleRecord record = receiveSampleRecordService.findOutsideSendSampleById(projectId);
        //先找到订单的方案
        List<DtoQuotationDetail> originQuotationDetailList = quotationDetailRepository.findAll(quotationDetailIds);
        List<DtoQuotationDetail> quotationDetailList = new ArrayList<>();
        List<String> filterQuotationDetailIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(filterList)) {
            //按点位录入
            Set<String> filterSet = new HashSet<>(filterList);
            for (DtoQuotationDetail orginQuotationDetail : originQuotationDetailList) {
                if (StringUtil.isNotEmpty(orginQuotationDetail.getFolderName())) {
                    Set<String> originFolderSet = new HashSet<>(Arrays.asList(orginQuotationDetail.getFolderName().split(",")));
                    originFolderSet.retainAll(filterSet);
                    if (originFolderSet.size() > 0) {
                        filterQuotationDetailIds.add(orginQuotationDetail.getId());
                        DtoQuotationDetail copy = new DtoQuotationDetail();
                        BeanUtils.copyProperties(orginQuotationDetail, copy);
                        copy.setFolderName(String.join(",", originFolderSet));
                        quotationDetailList.add(copy);
                    }
                }
            }
        } else {
            quotationDetailList = originQuotationDetailList;
            filterQuotationDetailIds = quotationDetailIds;
        }
        //获取所有的测试项目id
        List<String> testIds = quotationDetailList.stream().filter(p -> !p.getIsTotal()).map(DtoQuotationDetail::getTestId).collect(Collectors.toList());
        List<DtoQuotationDetail2Test> quotationDetail2TestList = StringUtil.isNotEmpty(filterQuotationDetailIds) ?
                quotationDetail2TestRepository.findByDetailIdIn(filterQuotationDetailIds) : new ArrayList<>();
        List<String> parentTestIds = quotationDetail2TestList.stream().map(DtoQuotationDetail2Test::getTestId).distinct().collect(Collectors.toList());
        testIds.addAll(parentTestIds);
        testIds = testIds.stream().distinct().collect(Collectors.toList());
        List<DtoTest> testList = new ArrayList<>();
        List<DtoTestExpand> testExpandList = new ArrayList<>();
        List<DtoPerson2Test> p2tList = new ArrayList<>();
        List<String> dimensionIds = new ArrayList<>();
        if (testIds.size() > 0) {
            testList = testService.findRedisByIds(testIds);
            // 根据分析方法状态，剔除停用、废止的方法对应的测试项目
            testService.removeByMethodStatus(testList);

            dimensionIds.addAll(testList.stream().map(DtoTest::getDimensionId).filter(StringUtil::isNotEmpty).collect(Collectors.toList()));
            //获取对应的测试扩展
            testExpandList = testExpandService.findRedisByTestIds(testIds);
            dimensionIds.addAll(testExpandList.stream().map(DtoTestExpand::getDimensionId).filter(StringUtil::isNotEmpty).collect(Collectors.toList()));
            //获取第一测试人员
            p2tList = person2TestService.findByTestIds(testIds);
        }
        List<DtoDimension> dimensionList = new ArrayList<>();
        if (dimensionIds.size() > 0) {
            dimensionList = dimensionService.findRedisByIds(dimensionIds);
        }
        matchOrderScheme(quotationDetailList, project, record, testList, quotationDetail2TestList, testExpandList, dimensionList, p2tList);
    }

    @Async(AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Future<String> dealMatchOrderScheme(List<DtoQuotationDetail> quotationDetailList, DtoProject project, DtoReceiveSampleRecord record,
                                               List<DtoTest> testList, List<DtoQuotationDetail2Test> quotationDetail2TestList,
                                               List<DtoTestExpand> testExpandList, List<DtoDimension> dimensionList, List<DtoPerson2Test> p2tList) {
        try {
            this.matchOrderScheme(quotationDetailList, project, record, testList, quotationDetail2TestList, testExpandList, dimensionList, p2tList);
        } catch (Exception e) {
            throw new BaseException(e);
        } finally {
        }
        return new AsyncResult<>("");
    }

    /**
     * 导入订单方案
     */
    private void matchOrderScheme(List<DtoQuotationDetail> quotationDetailList, DtoProject project, DtoReceiveSampleRecord record,
                                  List<DtoTest> testList, List<DtoQuotationDetail2Test> quotationDetail2TestList,
                                  List<DtoTestExpand> testExpandList, List<DtoDimension> dimensionList, List<DtoPerson2Test> p2tList) {
        //统计一共有多少个点位
        Map<String, List<String>> folderList = new HashMap<>();
        //点位对应的最大周期频次数
        Map<String, List<Integer>> frequent = new HashMap<>();
        quotationDetailList.forEach(quotationDetail -> {
            if (StringUtil.isNotEmpty(quotationDetail.getFolderName())) {
                quotationDetail.setFolderList(quotationDetail.getFolderName().split(","));
                //存在包含了的样品类型
                List<String> folder = new ArrayList<>();
                if (folderList.containsKey(quotationDetail.getSampleTypeId())) {
                    //当前订单详情中的点位
                    Arrays.stream(quotationDetail.getFolderName().split(",")).forEach(folderName -> {
                        //订单点位不在样品类型点位下
                        if (!folderList.get(quotationDetail.getSampleTypeId()).contains(folderName.trim())) {
                            //不存在的点位添加到样品类型点位下
                            folderList.get(quotationDetail.getSampleTypeId()).add(folderName.trim());
                        }
                        getFrquenceMap(folderName.trim() + quotationDetail.getSampleTypeId(), frequent, quotationDetail);
                    });
                } else {
                    Arrays.stream(quotationDetail.getFolderName().split(",")).forEach(folderName -> {
                        if (!folder.contains(folderName.trim())) {
                            folder.add(folderName.trim());
                        }
                        getFrquenceMap(folderName.trim() + quotationDetail.getSampleTypeId(), frequent, quotationDetail);
                    });
                    folderList.put(quotationDetail.getSampleTypeId(), folder);
                }
            }
        });

        //创建点位及方案
        List<DtoSampleFolder> sampleFolderList = new ArrayList<>();
        DtoLoadScheme targetScheme = new DtoLoadScheme();
        DtoLoadScheme templateScheme = new DtoLoadScheme();
        List<DtoTest> finalTestList = testList;
        List<DtoTestExpand> finalTestExpandList = testExpandList;
        List<DtoPerson2Test> finalP2tList = p2tList;

        List<DtoDimension> finalDimensionList = dimensionList;
        //定义变量记录样品数量，用于更新project对象json字段中记录的样品数量
        AtomicInteger sampleCount = new AtomicInteger();
        List<DtoQuotationDetail> finalQuotationDetailList = quotationDetailList;
        folderList.forEach((k, v) -> {
            Collections.sort(v);
            v.forEach(s -> {
                //创建点位
                DtoSampleFolder sampleFolder = new DtoSampleFolder();
                sampleFolder.setSampleTypeId(k);
                sampleFolder.setProjectId(project.getId());
                sampleFolder.setWatchSpot(s);
                sampleFolder.setChargeRate(new BigDecimal(1));
                sampleFolder.setGrade(1);
                sampleFolder.setOrgId(project.getOrgId());
                sampleFolder.setInspectedEnt(project.getInspectedEnt());
                sampleFolder.setInspectedEntId(project.getInspectedEntId());
                sampleFolderList.add(sampleFolder);
                //创建方案
                List<Integer> frquence = frequent.get(s + k);
                //周期
                for (int i = 1; i <= frquence.get(0); i++) {
                    //频次
                    for (int j = 1; j <= frquence.get(1); j++) {
                        //费用想起中点位存在同时，周期和频次同时大于等于当前的周期频次
                        for (int m = 1; m <= frquence.get(2); m++) {
                            int finalI = i;
                            int finalJ = j;
                            int finalM = m;
                            //找到满足条件的费用明细
                            List<DtoQuotationDetail> quotationDetails = finalQuotationDetailList.stream()
                                    .filter(p -> Arrays.stream(p.getFolderList()).anyMatch(f -> f.contains(s))
                                            && p.getCycleOrder() >= finalI && p.getTimesOrder() >= finalJ
                                            && p.getSampleCount() >= finalM && p.getSampleTypeId().equals(k)).collect(Collectors.toList());
                            List<String> qdIds = quotationDetails.stream().map(DtoQuotationDetail::getId).collect(Collectors.toList());
                            List<String> tIds = quotationDetails.stream().filter(p -> !p.getIsTotal()).map(DtoQuotationDetail::getTestId).collect(Collectors.toList());
                            List<String> ptIds = quotationDetail2TestList.stream().filter(p ->
                                    qdIds.contains(p.getDetailId())).map(DtoQuotationDetail2Test::getTestId).collect(Collectors.toList());
                            tIds.addAll(ptIds);
                            //找到满足点位的周期频次的测试项目
                            List<DtoTest> tests = finalTestList.stream().filter(p -> tIds.contains(p.getId())).distinct().collect(Collectors.toList());
                            if (StringUtil.isEmpty(tests)) {
                                continue;
                            }
                            DtoSamplingFrequency frequency = createSamplingFrequency(i, j, m, sampleFolder);
                            targetScheme.addSamplingFrequency(frequency);
                            templateScheme.putNewSamplingFrequencyId(UUIDHelper.GUID_EMPTY, frequency.getId(),
                                    String.valueOf(i), String.valueOf(j), String.valueOf(m));
                            for (DtoTest test : tests) {
                                DtoSamplingFrequencyTest sft = createSamplingFrequencyTest(sampleFolder, frequency, test);
                                targetScheme.addSamplingFrequencyTest(sft);
                            }
                            //创建样品
                            DtoSample temp = createSample(templateScheme, sampleFolder, project, tests, finalI, finalJ, finalM);
                            //如果是送样类的项目，则需要给样品赋值送样id
                            if (StringUtil.isNotNull(record)) {
                                temp.setReceiveId(record.getId());
                                temp.setSamplingTimeBegin(record.getSamplingTime());
                                temp.setCode(sampleService.createSampleCode(project.getId(), project.getProjectTypeId(), k, UUIDHelper.GUID_EMPTY, record.getSamplingTime(), true,
                                        StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "", null, record));
                            }
                            sampleCount.getAndIncrement();
                            targetScheme.addSample(temp);
                            //创建数据
                            if (tests.size() > 0) {
                                DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, temp, tests, false);
                                addDto.setIsAddAssociateTest(false);
                                //测试扩展
                                List<DtoTestExpand> expandList = finalTestExpandList.stream().filter(p -> k.equals(p.getSampleTypeId())).collect(Collectors.toList());
                                if (expandList.size() > 0) {
                                    addDto.setTestExpandList(expandList);
                                }
                                //现找到样品类型是小类上的测试人员，再过滤出剩余测试项目上大类的测试人员
                                List<DtoPerson2Test> person2TestList = finalP2tList.stream().filter(p -> k.equals(p.getSampleTypeId())).collect(Collectors.toList());
                                List<String> ttIds = person2TestList.stream().map(DtoPerson2Test::getTestId).collect(Collectors.toList());
                                List<String> atIds = tests.stream().map(DtoTest::getId).collect(Collectors.toList());
                                //小类上没有的测试项目，再到大类上去找
                                if (atIds.stream().anyMatch(a -> !ttIds.contains(a))) {
                                    Optional<String> bigSampleType = tests.stream().map(DtoTest::getSampleTypeId).findFirst();
                                    List<DtoPerson2Test> finalPerson2TestList = person2TestList;
                                    bigSampleType.ifPresent(value -> finalPerson2TestList.addAll(finalP2tList.stream().filter(t -> value.equals(t.getSampleTypeId()) &&
                                            !ttIds.contains(t.getTestId()) && atIds.contains(t.getTestId())).collect(Collectors.toList())));
                                }
                                //测试人员
                                if (person2TestList.size() > 0) {
                                    person2TestList = person2TestList.stream().collect(collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(Person2Test::getTestId))), ArrayList::new));
                                    addDto.setPerson2TestList(person2TestList);
                                }
                                analyseDataService.getAddAnalyseDataByOrderScheme(addDto, record, finalDimensionList).forEach(targetScheme::addAnalyseData);
                            }
                        }
                    }
                }
            });
        });
        updateProjectSmapleInfo(project, sampleCount.get());
        //统一添加点位
        targetScheme.setSampleFolder(sampleFolderList);
        //如果是送样项目
        if (StringUtil.isNotNull(record)) {
            //需要更新送样单json字段
            DtoRecordJson jsonEntity = new DtoRecordJson();
            jsonEntity.setSampleNum(sampleCount.get());
            jsonEntity.setSampleTypeIds(String.join(",", folderList.keySet().stream().distinct().collect(Collectors.toList())));
            if (folderList.keySet().stream().distinct().collect(Collectors.toList()).size() > 0) {
                List<String> typeNames = sampleTypeService.findRedisByIds(folderList.keySet().stream().distinct().collect(Collectors.toList()))
                        .stream().map(DtoSampleType::getTypeName).collect(Collectors.toList());
                jsonEntity.setLabSampleTypes(String.join(",", typeNames));
            }
            try {
                record.setJson(JsonStream.serialize(jsonEntity));
            } catch (Exception ex) {
                System.out.println(ex.getMessage());
            }
            //需要创建新的subRecord，同时需要把subRecord绑定样品
            //存在不分包的实验室指标
            if (testList.stream().anyMatch(p -> !p.getIsCompleteField() && !p.getIsOutsourcing())) {
                receiveSubSampleRecordService.createSubRecord(record, targetScheme.getSample(), EnumSubRecordType.分析.getValue());
            }
            //存在不分包的现场指标
            if (testList.stream().anyMatch(p -> p.getIsCompleteField() && !p.getIsOutsourcing())) {
                receiveSubSampleRecordService.createSubRecord(record, targetScheme.getSample(), EnumSubRecordType.现场.getValue());
            }
        }
        //统一保存数据
        this.persistLoadByLimsRepository(targetScheme);
        proService.sendProMessageWoTransactional(EnumProAction.方案新增点位, project.getId(),
                StringUtil.isNotNull(record) ? record.getId() : "", null, null);
    }

    @Override
    public void queryProject(PageBean<DtoProject> pb, BaseCriteria baseCriteria) {
//        pb.setEntityName("DtoProject p,DtoProjectPlan pl,DtoStatusForProject s");
        pb.setEntityName("DtoProject p,DtoProjectPlan pl");
        pb.setSelect("select p,pl");
        comRepository.findByPage(pb, baseCriteria);
        List<DtoProject> datas = pb.getData();
        List<DtoProject> newDatas = new ArrayList<>();
        Iterator<DtoProject> projectIte = datas.iterator();
        List<String> proIds = new ArrayList<>();
        // 循环迭代获取JPQL中查询返回的属性
        while (projectIte.hasNext()) {
            Object obj = projectIte.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoProject project = (DtoProject) objs[0];
            proIds.add(project.getId());
            DtoProjectPlan plan = (DtoProjectPlan) objs[1];
            project.loadFromPlan(plan);
            newDatas.add(project);
        }
        pb.setData(newDatas);
    }

    /**
     * 提交提醒明细
     *
     * @param objectIds    objId
     * @param restrictType 类型
     * @param status       状态
     * @return 返回明细
     */
    @Override
    public List<DtoSubmitRestrictVo> submitMsgList(List<String> objectIds, String restrictType, String status) {
        return submitRestrictContext.submitJudgment(restrictType, objectIds, status);
    }

    /**
     * 提交提醒明细
     *
     * @param objectId     objId
     * @param restrictType 类型
     * @param status       状态
     * @return 返回明细
     */
    @Override
    public List<DtoSubmitRestrictVo> submitMsgList(String objectId, String restrictType, String status) {
        return submitRestrictContext.submitJudgment(restrictType, objectId, status);
    }

    /**
     * 复制方案时检查方案中测试项目
     *
     * @param map 传输实体
     */
    @Override
    public void checkTest(Map<String, Object> map) {
        String projectId = map.containsKey("projectId") ? map.get("projectId").toString() : "";
        List<String> quotationDetailIds = map.containsKey("detailIds") ? (List<String>) map.get("detailIds") : new ArrayList<>();
        List<String> propertyIds = map.containsKey("propertyIds") ? (List<String>) map.get("propertyIds") : new ArrayList<>();
        // 定义旧测试项目集合，用做比较
        List<Map<String, Object>> oldTestMapList = new ArrayList<>();
        // 例行任务更新方案
        if (StringUtil.isNotEmpty(propertyIds)) {
            // 是否污染源
            boolean pollution = map.containsKey("pollution") && (boolean) map.get("pollution");
            if (!pollution) {//环境质量
                List<String> p2pIds = property2PointRepository.findByPropertyIdIn(propertyIds).stream()
                        .map(DtoProperty2Point::getId).collect(Collectors.toList());
                List<DtoPropertyPoint2Test> propertyPoint2TestList = propertyPoint2TestRepository.findByPropertyPointIdIn(p2pIds);
                oldTestMapList = propertyPoint2TestList.stream().map(p -> {
                    Map<String, Object> testMap = new HashMap<>();
                    testMap.put("id", p.getTestId());
                    return testMap;
                }).distinct().collect(Collectors.toList());
            } else {//污染源
                List<DtoFixedPoint2Test> fixedPoint2TestList = fixedPoint2TestRepository.findByFixedPointIdIn(propertyIds);
                oldTestMapList = fixedPoint2TestList.stream().map(p -> {
                    Map<String, Object> testMap = new HashMap<>();
                    testMap.put("id", p.getTestId());
                    return testMap;
                }).distinct().collect(Collectors.toList());
            }
        } else if (StringUtil.isNotEmpty(quotationDetailIds)) {
            // 先找到订单的方案
            List<DtoQuotationDetail> quotationDetailList = quotationDetailRepository.findAll(quotationDetailIds);
            Map<String, List<DtoQuotationDetail>> quotationDetailGroupMap = quotationDetailList.stream().collect(groupingBy(DtoQuotationDetail::getTestId));
            // 组装旧测试项目
            oldTestMapList = quotationDetailGroupMap.entrySet().parallelStream().map(p -> {
                DtoQuotationDetail quotationDetail = p.getValue().get(0);
                Map<String, Object> testMap = new HashMap<>();
                testMap.put("id", p.getKey());
                testMap.put("redAnalyzeItemName", quotationDetail.getRedAnalyseItemName());
                testMap.put("redAnalyzeMethodName", quotationDetail.getRedAnalyseMethod());
                return testMap;
            }).collect(toList());
        } else {
            // 获取项目下方案信息
            List<DtoSampleFolder> sourceSampleFolders = sampleFolderRepository.findByProjectId(projectId);
            List<String> sampleTypeIds = sourceSampleFolders.stream().map(DtoSampleFolder::getId).collect(toList());
            List<DtoSamplingFrequencyTest> sourceSamplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleTypeIds);
            // 频次测试项目信息根据测试项目id分组。
            Map<String, List<DtoSamplingFrequencyTest>> samplingFrequencyTestGroupMap = sourceSamplingFrequencyTests.stream().collect(groupingBy(DtoSamplingFrequencyTest::getTestId));
            // 组装旧测试项目
            oldTestMapList = samplingFrequencyTestGroupMap.entrySet().parallelStream().map(p -> {
                DtoSamplingFrequencyTest samplingFrequencyTest = p.getValue().get(0);
                Map<String, Object> testMap = new HashMap<>();
                testMap.put("id", p.getKey());
                testMap.put("redAnalyzeItemName", samplingFrequencyTest.getRedAnalyzeItemName());
                testMap.put("redAnalyzeMethodName", samplingFrequencyTest.getRedAnalyzeMethodName());
                return testMap;
            }).collect(toList());
        }
        // 获取数据库现有测试项目包括假删
        oldTestMapList = oldTestMapList.stream().distinct().collect(toList());
        Set<String> testIds = oldTestMapList.stream().map(p -> p.get("id").toString()).collect(Collectors.toSet());
        List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testService.findAllDeleted(testIds) : new ArrayList<>();
        List<String> analyzeMethodIds = testList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
        List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(analyzeMethodIds) ? analyzeMethodRepository.findAllDeleted(analyzeMethodIds) : new ArrayList<>();

        // 遍历分组，与测试项目、分析方法比较
        List<String> resultList = new ArrayList<>();
        for (Map<String, Object> oldTestMap : oldTestMapList) {
            String id = oldTestMap.getOrDefault("id", "").toString();
            String redAnalyzeItemName = oldTestMap.getOrDefault("redAnalyzeItemName", "").toString();
            String redAnalyzeMethodName = oldTestMap.getOrDefault("redAnalyzeMethodName", "").toString();
            DtoTest test = testList.stream().filter(p -> p.getId().equals(id)).findFirst().orElse(null);
            // 比较方法状态、测试项目状态
            if (null == test || test.getIsDeleted()) {
                resultList.add(String.format("<b>%s</b>测试项目已删除，未正常导入方案;", test != null ? test.getRedAnalyzeItemName() : redAnalyzeItemName));
            } else {
                Optional<DtoAnalyzeMethod> methodOptional = analyzeMethodList.stream().filter(p -> test.getAnalyzeMethodId().equals(p.getId())
                        && EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(p.getStatus())).findFirst();
                methodOptional.ifPresent(method -> {
                    resultList.add(String.format("%s方法已停用，未正常导入方案;", test.getRedAnalyzeMethodName()));
                });
                // 比较分析项目、分析方法,污染源和环境质量点位记录的测试项目未冗余名称和方法，所以不进行判断是否被修改
                if (StringUtil.isNotEmpty(redAnalyzeItemName) && !redAnalyzeItemName.equals(test.getRedAnalyzeItemName())) {
                    resultList.add(String.format("<b>%s</b>分析项目已修改，原分析项目：%s，现分析项目：%s;",
                            test.getRedAnalyzeItemName(), redAnalyzeItemName, test.getRedAnalyzeItemName()));
                }
                if (StringUtil.isNotEmpty(redAnalyzeMethodName) && !redAnalyzeMethodName.equals(test.getRedAnalyzeMethodName())) {
                    resultList.add(String.format("<b>%s</b>分析方法已修改，原分析方法：%s，现分析方法：%s;",
                            test.getRedAnalyzeItemName(), redAnalyzeMethodName, test.getRedAnalyzeMethodName()));
                }
            }
        }
        if (StringUtil.isNotEmpty(resultList)) {
            throw new BaseException(String.join("</br>", resultList));
        }
    }


    @Override
    public Map<String, Object> findProjectExpandByContractId(String contractId) {
        Map<String, Object> map = new HashMap<>();
        map.put("projectContract", new DtoProjectContract());
        map.put("projectExpand", new DtoProject());
        List<DtoProjectContract> projectContracts = projectContractRepository.findByContractIdAndHasPush(contractId, 1);
        List<String> projectIds = projectContracts.stream().map(DtoProjectContract::getProjectId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(projectIds)) {
            List<DtoProject> projects = findAll(projectIds);
            DtoProject project = projects.stream().max(Comparator.comparing(DtoProject::getInputTime)).orElse(null);
            if (StringUtil.isNotNull(project)) {
                map.put("projectExpand", project);
                DtoProjectContract projectContract = projectContracts.stream().filter(c -> c.getProjectId().equals(project.getId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(projectContract)) {
                    map.put("projectContract", projectContract);
                }
            }
        }
        return map;
    }

    /**
     * 电子表单推送至移动端签名模块
     *
     * @param documentId 附件id
     */
    @Override
    public void pushSign(String documentId) {
        DtoDocument dtoDocument = documentService.findOne(documentId);
        dtoDocument.setSignStatus(EnumBase.EnumSignStatus.未签.getValue());
        documentService.save(dtoDocument);
    }

    @Override
    public String getEarliestSamplingTime(String projectId) {
        String dateStr = "";
        List<DtoSample> sampleList = sampleRepository.findByProjectId(projectId);
        sampleList = sampleList.stream().filter(p -> !DateUtil.dateToString(p.getSamplingTimeBegin(), DateUtil.YEAR).contains("1753")).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sampleList)) {
            sampleList.sort(Comparator.comparing(DtoSample::getSamplingTimeBegin));
            Date date = sampleList.get(0).getSamplingTimeBegin();
            dateStr = DateUtil.dateToString(date, DateUtil.YEAR);
        }
        return dateStr;
    }

    @Override
    public boolean getIsAllOutsourcing(String projectId) {
        String dateStr = "";
        List<DtoSample> sampleList = sampleRepository.findByProjectId(projectId);
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(toList());
        List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ?
                analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds) : new ArrayList<>();
        boolean IsAllOutsourcing = false;
        if (StringUtil.isNotEmpty(analyseDataList)) {
            IsAllOutsourcing = analyseDataList.stream().allMatch(DtoAnalyseData::getIsOutsourcing);
        }
        return IsAllOutsourcing;
    }

    private void updateProjectSmapleInfo(DtoProject project, int sampleCount) {
        if (sampleCount != 0) {
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("notSampled", sampleCount);
            updateProjectJson(project, jsonMap);
        }
    }

    private void updateProjectJson(DtoProject project, Map<String, Object> map) {
        if (StringUtil.isNotEmpty(map)) {
            String projectJson = project.getJson();
            TypeLiteral<DtoProjectJson> typeLiteral = new TypeLiteral<DtoProjectJson>() {
            };
            DtoProjectJson dtoProjectJson = JsonIterator.deserialize(projectJson, typeLiteral);
            dtoProjectJson.setNotSampled((Integer) map.get("notSampled"));
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                project.setJson(objectMapper.writeValueAsString(dtoProjectJson));
            } catch (Exception ex) {
                throw new BaseException("更新项目信息失败！");
            }
            repository.save(project);
        }
    }

    /**
     * 存储点位频次信息内容
     *
     * @param folderName 点位
     * @param frequent   点位频次信息
     * @param p          明细
     */
    private void getFrquenceMap(String folderName, Map<String, List<Integer>> frequent, DtoQuotationDetail p) {
        List<Integer> fInt = new ArrayList<>();
        //点位存在频次集合中
        if (frequent.containsKey(folderName)) {
            fInt.add(getMaxCount(frequent.get(folderName).get(0), p.getCycleOrder()));
            fInt.add(getMaxCount(frequent.get(folderName).get(1), p.getTimesOrder()));
            fInt.add(getMaxCount(frequent.get(folderName).get(2), p.getSampleCount()));
            frequent.replace(folderName, fInt);
        } else {
            //周期
            fInt.add(p.getCycleOrder());
            //频次
            fInt.add(p.getTimesOrder());
            //样品数
            fInt.add(p.getSampleCount());
            frequent.put(folderName, fInt);
        }
    }

    /**
     * 比较两个整数，取大的值
     *
     * @param a 值a
     * @param b 值b
     * @return 返回大的值
     */
    private Integer getMaxCount(Integer a, Integer b) {
        if (a < b) {
            return b;
        } else {
            return a;
        }
    }

    /**
     * 项目电子表单签名
     *
     * @param projectList 项目集合
     * @param signal      工作流信号值
     */
    private void signProjectSheet(List<DtoProject> projectList, String signal) {
        try {
            for (DtoProject dtoProject : projectList) {
                if (signal.equals("registerSubmit")) {
                    String inputTime = DateUtil.dateToString(dtoProject.getInputTime(), DateUtil.YEAR);
                    signatureService.sig(dtoProject.getId(), Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.登记人.getValue(), BaseCodeHelper.DOCUMENT_EXTEND_PROJECT_SHEET, inputTime, false);
                } else if (signal.equals("projectAudit")) {
                    String now = DateUtil.dateToString(new Date(), DateUtil.YEAR);
                    signatureService.sig(dtoProject.getId(), Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.审核人.getValue(), BaseCodeHelper.DOCUMENT_EXTEND_PROJECT_SHEET, now, false);
                } else if (signal.equals("makeSolution")) {
                    String now = DateUtil.dateToString(new Date(), DateUtil.YEAR);
                    signatureService.sig(dtoProject.getId(), Collections.singletonList(PrincipalContextUser.getPrincipal().getUserId()), EnumSigType.下达人.getValue(), BaseCodeHelper.DOCUMENT_EXTEND_PROJECT_SHEET, now, false);
                } else if (signal.equals("back")) {
                    // 驳回项目时，移除电子签名
                    Map<String, Integer> mapType = new HashMap<>();
                    mapType.put(EnumPRO.EnumSigType.登记人.getName(), 1);
                    mapType.put(EnumPRO.EnumSigType.审核人.getName(), 1);
                    mapType.put(EnumPRO.EnumSigType.下达人.getName(), 1);
                    signatureService.removeWorkSheetSig(dtoProject.getId(), mapType);
                }
            }
        } catch (Exception e) {
            log.error("电子表单签名失败" + e.getMessage());
        }
    }

    /**
     * 保存项目负责人或者编制报告人
     *
     * @param dtoProjectPlanList 项目方案信息
     * @param dtoWorkflowSign    流程信息
     */
    protected void saveProjectLeader(List<DtoProjectPlan> dtoProjectPlanList, DtoWorkflowSign dtoWorkflowSign) {
        dtoProjectPlanList.forEach(p -> {
            //更新项目提交时选定的项目负责人
            //现在项目下达才会修改项目负责人
            if ("makeSolution".equals(dtoWorkflowSign.getSignal())) {
                p.setLeaderId(dtoWorkflowSign.getNextOperatorId());
            }
            //如果提交时有选编制报告人，则需要更新编制报告人
            if (StringUtil.isNotEmpty(dtoWorkflowSign.getReportMakerId())) {
                p.setReportMakerId(dtoWorkflowSign.getReportMakerId());
            }
        });
        projectPlanRepository.save(dtoProjectPlanList);
    }

    /**
     * 设置复制项目的项目基本信息
     *
     * @param oldProject          老项目数据
     * @param receiveSampleRecord 送样单数据
     */
    protected DtoProject getCopyProjectData(DtoProject oldProject, DtoReceiveSampleRecord receiveSampleRecord) {
        DtoProject project = new DtoProject();
        //复制原项目的信息
        BeanUtils.copyProperties(oldProject, project,
                "id", "samplingStatus", "reportStatus", "isOnline", "pushStatus",
                "creator", "createDate", "domainId", "modifier", "modifyDate", "dbExtendMap");
        project.setProjectName(project.getProjectName() + "复制项目");
        project.setInceptTime(DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));
        project.setInputTime(DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));
        project.setDeadLine(receiveSampleRecord.getDeadLine());
        project.setInceptPersonId(PrincipalContextUser.getPrincipal().getUserId());
        project.setReportDate(receiveSampleRecord.getReportDate());
        project.setProjectCode(judgeCreateProjectCode(project));
        //项目推送合同信息设置
        if (StringUtil.isNotNull(oldProject.getProjectContract())) {
            DtoProjectContract projectContract = new DtoProjectContract();
            BeanUtils.copyProperties(oldProject.getProjectContract(), projectContract,
                    "id", "pId", "hasPush", "isHandle",
                    "schemeHasPush", "planHasPush", "reportHasPush", "dbExtendMap");
            project.setProjectContract(projectContract);
        }
        return project;
    }

    private String judgeCreateProjectCode(DtoProject project, Integer count) {
        String projectCode = "";
        //判断是否是订单中创建的项目
        if (StringUtil.isNotEmpty(project.getOrderId()) && !UUIDHelper.GUID_EMPTY.equals(project.getOrderId())) {
            DtoOrderForm orderForm = orderFormRepository.findOne(project.getOrderId());
            //找到订单下有多少个项目包括被删除的
            Optional<DtoProject> pro = repository.findByOrderId(orderForm.getId()).stream().filter(p -> p.getProjectCode().contains("-")).max(Comparator.comparing(p -> {
                // 使用split方法按"-"分隔字符串，并取第二位（索引为1）
                String[] split = p.getProjectCode().split("-");
                String part = split.length > 0 ? split[1] : "00";
                // 将字符串转换为整数进行比较
                return Integer.parseInt(part);
            }));
            Integer number = 1;
            DecimalFormat df = new DecimalFormat("00");
            if (pro.isPresent()) {
                //找到最大的流水号加一
                number = Integer.valueOf(pro.get().getProjectCode().replace(orderForm.getOrderCode() + "-", ""));
                number++;
            }
            number = number + count;
            projectCode = String.format("%s-%s", orderForm.getOrderCode(), df.format(number));
        } else {
            if (project.getIsGeneratedProCode()) {
                //项目编号生成 从inputTime 改为 inceptTime
                projectCode = this.createProjectCode(project.getProjectTypeId(), project.getInceptTime());
            }
        }
        return projectCode;
    }

    /**
     * 根据判断是否为订单内获取项目编号
     *
     * @param project 项目实体
     * @return 项目编号
     */
    private String judgeCreateProjectCode(DtoProject project) {
        return this.judgeCreateProjectCode(project, 0);
    }

    /**
     * 获取对应模块下的意见Map
     *
     * @param baseCriteria 查询条件
     * @param newDatas     查询到的项目数据
     * @return 意见集合
     */
    protected Map<String, String> findStatusOptionMaps(BaseCriteria baseCriteria, List<DtoProject> newDatas) {
        Map<String, String> lastNewOpinionMap = new HashMap<>();
        ProjectCriteria criteria = (ProjectCriteria) baseCriteria;
        if (criteria.getModule().equals(EnumLIM.EnumProjectModule.项目进度.getCode()) ||
                criteria.getModule().equals(EnumLIM.EnumProjectModule.项目登记.getCode()) ||
                criteria.getModule().equals(EnumLIM.EnumProjectModule.技术审核.getCode()) ||
                criteria.getModule().equals(EnumLIM.EnumProjectModule.项目下达.getCode()) ||
                criteria.getModule().equals(EnumLIM.EnumProjectModule.方案编制.getCode()) ||
                criteria.getModule().equals(EnumLIM.EnumProjectModule.方案审核.getCode()) ||
                criteria.getModule().equals(EnumLIM.EnumProjectModule.方案确认.getCode()) ||
                criteria.getModule().equals(EnumLIM.EnumProjectModule.环境例行登记.getCode()) ||
                criteria.getModule().equals(EnumLIM.EnumProjectModule.污染源例行登记.getCode())
        ) {
            List<String> projectIds = newDatas.stream().map(DtoProject::getId).collect(Collectors.toList());
            List<DtoStatusForProject> sfpList = statusForProjectRepository.findByProjectIdIn(projectIds);
            lastNewOpinionMap = sfpList.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getLastNewOpinion())).
                    sorted(Comparator.comparing(DtoStatusForProject::getModifyDate).reversed()).
                    collect(Collectors.groupingBy(DtoStatusForProject::getProjectId, collectingAndThen(Collectors.toList(), value -> value.get(0).getLastNewOpinion())));
        }
        return lastNewOpinionMap;
    }

    /**
     * 项目编号是否允许编辑标记，主要为了方便子类个性化覆盖
     *
     * @return true: 新增项目时项目编号可编辑；false: 新增项目时项目编号不可编辑
     */
    protected boolean projectCodeEditIndicator() {
        return Boolean.FALSE;
    }

    protected void loadFromReceiveTempInd(DtoProject project, DtoReceiveSampleRecordTemp dto) {
    }

    /**
     * 处理受检单位信息
     *
     * @param project 项目对象
     * @param dto     送样单对象
     */
    protected void loadInspectedData(DtoProject project, DtoReceiveSampleRecordTemp dto) {
        project.setInspectedLinkMan(dto.getInspectedLinkMan());
        project.setInspectedLinkPhone(dto.getInspectedLinkPhone());
        project.setInspectedAddress(dto.getInspectedAddress());
    }

    @Autowired
    public void setFolderSignRepository(FolderSignRepository folderSignRepository) {
        this.folderSignRepository = folderSignRepository;
    }

    @Autowired
    @Lazy
    public void setEnvironmentEnterpriseService(EnvironmentEnterpriseService environmentEnterpriseService) {
        this.environmentEnterpriseService = environmentEnterpriseService;
    }
}