package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import lombok.Data;

/**
 * 上岗证导入实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Data
public class DtoImportPersonCert extends PoiBaseEntity {

    @Excel(name = "姓名", orderNum = "10", width = 17)
    private String personName;

    @Excel(name = "证书编号", orderNum = "20", width = 17)
    private String certCode;

    @Excel(name = "监测类别", orderNum = "30", width = 17)
    private String sampleType;

    @Excel(name = "项目名称", orderNum = "40", width = 17)
    private String redAnalyzeItemName;

    @Excel(name = "标准编号", orderNum = "50", width = 17)
    private String redCountryStandard;

    @Excel(name = "标准名称", orderNum = "60", width = 17)
    private String redAnalyzeMethodName;

    @Excel(name = "方法id", orderNum = "70", width = 17)
    private String methodId;

    @Excel(name = "有效开始日期", orderNum = "80", width = 17)
    private String achieveDate;

    @Excel(name = "有效结束日期", orderNum = "90", width = 17)
    private String certEffectiveTime;


}
