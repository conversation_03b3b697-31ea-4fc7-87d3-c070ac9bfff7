package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collection;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;



/**
 * 点位频次指标查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年11月9日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamplingFrequencyTestCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 项目id
    */
    private String projectId;

    /**
    * 点位id集合
    */
    private Collection sampleFolderIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and sft.samplingFrequencyId = sf.id");
        condition.append(" and sf.sampleFolderId = s.id");

        if (StringUtil.isNotEmpty(this.projectId)) {
            condition.append(" and s.projectId = :projectId");
            values.put("projectId", this.projectId);
        }

        if (StringUtil.isNotNull(this.sampleFolderIds) && this.sampleFolderIds.size()>0) {
            condition.append(" and s.id in :sampleFolderIds");
            values.put("sampleFolderIds", this.sampleFolderIds);
        }
        return condition.toString();
    }
}