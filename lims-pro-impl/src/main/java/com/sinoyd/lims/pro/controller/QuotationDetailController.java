package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.customer.DtoQuotationData;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;
import com.sinoyd.lims.pro.vo.AutoProjectInfoVO;
import com.sinoyd.lims.pro.vo.SetDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.QuotationDetailService;
import com.sinoyd.lims.pro.criteria.QuotationDetailCriteria;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * QuotationDetail服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @Api(tags = "示例: QuotationDetail服务")
 @RestController
 @RequestMapping("api/pro/quotationDetail")
 public class QuotationDetailController extends BaseJpaController<DtoQuotationDetail, String,QuotationDetailService> {


    /**
     * 分页动态条件查询QuotationDetail
     *
     * @param quotationDetailCriteria 条件参数
     * @return RestResponse<List < QuotationDetail>>
     */
    @ApiOperation(value = "分页动态条件查询QuotationDetail", notes = "分页动态条件查询QuotationDetail")
    @GetMapping
    public RestResponse<List<DtoQuotationDetail>> findByPage(QuotationDetailCriteria quotationDetailCriteria) {
        PageBean<DtoQuotationDetail> pageBean = super.getPageBean();
        RestResponse<List<DtoQuotationDetail>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, quotationDetailCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询QuotationDetail
     *
     * @param id 主键id
     * @return RestResponse<DtoQuotationDetail>
     */
    @ApiOperation(value = "按主键查询QuotationDetail", notes = "按主键查询QuotationDetail")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoQuotationDetail> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoQuotationDetail> restResponse = new RestResponse<>();
        DtoQuotationDetail quotationDetail = service.findOne(id);
        restResponse.setData(quotationDetail);
        restResponse.setRestStatus(StringUtil.isNull(quotationDetail) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增QuotationDetail
     *
     * @param quotationDetail 实体列表
     * @return RestResponse<DtoQuotationDetail>
     */
    @ApiOperation(value = "新增QuotationDetail", notes = "新增QuotationDetail")
    @PostMapping
    public RestResponse<DtoQuotationDetail> create(@RequestBody @Validated DtoQuotationDetail quotationDetail) {
        RestResponse<DtoQuotationDetail> restResponse = new RestResponse<>();
        restResponse.setData(service.save(quotationDetail));
        return restResponse;
    }

    /**
     * 新增QuotationDetail
     *
     * @param quotationDetail 实体列表
     * @return RestResponse<DtoQuotationDetail>
     */
    @ApiOperation(value = "新增QuotationDetail", notes = "新增QuotationDetail")
    @PostMapping("/saveDetails")
    public RestResponse<String> saveDetails(@RequestBody DtoQuotationDetail quotationDetail) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.save(quotationDetail.getTestList(), quotationDetail.getOrderId(), quotationDetail.getQuotationId(), quotationDetail.getSampleTypeId(), quotationDetail.getTestTemplateId());
        return restResponse;
    }

    /**
     * 新增QuotationDetail
     *
     * @param quotationDetail 实体列表
     * @return RestResponse<DtoQuotationDetail>
     */
    @ApiOperation(value = "修改QuotationDetail", notes = "修改QuotationDetail")
    @PutMapping
    public RestResponse<DtoQuotationDetail> update(@RequestBody @Validated DtoQuotationDetail quotationDetail) {
        RestResponse<DtoQuotationDetail> restResponse = new RestResponse<>();
        restResponse.setData(service.update(quotationDetail));
        return restResponse;
    }

    /**
     * "根据id批量删除QuotationDetail
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除QuotationDetail", notes = "根据id批量删除QuotationDetail")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 根据明细查子项测试项目
     *
     * @param id 明细id
     * @return RestResponse<List < DtoTest>>
     */
    @ApiOperation(value = "根据明细查子项测试项目", notes = "根据明细查子项测试项目")
    @GetMapping("/findTestList/{id}")
    public RestResponse<List<DtoTest>> findTestList(@PathVariable(name = "id") String id) {
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        List<DtoTest> testData = service.findByTestList(id);
        restResponse.setRestStatus(StringUtil.isEmpty(testData) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(testData);
        restResponse.setCount(testData.size());
        return restResponse;
    }

    /**
     * 新增明细子项
     *
     * @param quotationData 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "新增明细子项", notes = "新增明细子项")
    @PostMapping("/addTestList")
    public RestResponse<String> addTestList(@RequestBody DtoQuotationData quotationData) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.addTestList(quotationData.getDetailId(), quotationData.getTestIds());
        return restResponse;
    }

    /**
     * 删除明细子项
     *
     * @param quotationData 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "删除明细子项", notes = "删除明细子项")
    @PostMapping("/deleteTestList")
    public RestResponse<String> deleteTestList(@RequestBody DtoQuotationData quotationData) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.deleteTestList(quotationData.getDetailId(), quotationData.getTestIds());
        return restResponse;
    }

    /**
     * 批量修改明细
     *
     * @param quotationData 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量修改明细", notes = "批量修改明细")
    @PostMapping("/updateBatch")
    public RestResponse<String> updateBatch(@RequestBody DtoQuotationData quotationData) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.updateBatch(quotationData.getDetailIds(), quotationData);
        return restResponse;
    }

    /**
     * 更新检测费
     *
     * @param quotationData 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "更新检测费", notes = "更新检测费")
    @PostMapping("/updatePrice")
    public RestResponse<String> updatePrice(@RequestBody DtoQuotationData quotationData) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.updatePrice(quotationData.getDetailIds());
        return restResponse;
    }

    /**
     * 批量新增点位
     *
     * @param quotationData 实体列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "批量新增点位", notes = "批量新增点位")
    @PostMapping("/testFolders")
    public RestResponse<String> insertFolderBatch(@RequestBody DtoQuotationData quotationData) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.insertFolderBatch(quotationData.getDetailIds(), quotationData);
        return restResponse;
    }

    /**
     * 新增企业点位
     *
     * @param quotationData 订单数据
     * @return RestResponse<String>
     */
    @ApiOperation(value = "新增企业点位", notes = "新增企业点位")
    @PostMapping("/addFixedPoint")
    public RestResponse<String> addFixedPoint(@RequestBody DtoQuotationData quotationData) {
        RestResponse<String> response = new RestResponse<>();
        service.addFixedPoint(quotationData);
        return response;
    }

    /**
     * 获取检测费用明细下检测类型
     *
     * @param orderId 订单id
     * @return RestResponse<List < D t o SampleType>>
     */
    @ApiOperation(value = "获取检测费用明细下检测类型", notes = "获取检测费用明细下检测类型")
    @GetMapping(path = "/sampleTypes")
    public RestResponse<List<DtoSampleType>> findSampleTypes(@RequestParam(name = "orderId") String orderId) {
        RestResponse<List<DtoSampleType>> restResponse = new RestResponse<>();
        restResponse.setData(service.findSampleTypes(orderId));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 获取明细下所有点位
     *
     * @param quotationData 订单id
     * @return RestResponse<Collection < String>>
     */
    @ApiOperation(value = "获取订单下所有点位", notes = "获取订单下所有点位")
    @PostMapping(path = "/findAllFolder")
    public RestResponse<Collection<String>> findAllFolder(@RequestBody DtoQuotationData quotationData) {
        RestResponse<Collection<String>> response = new RestResponse<>();
        response.setData(service.findAllFolder(quotationData.getDetailIds()));
        return response;
    }

    /**
     * 批量删除点位
     *
     * @param quotationData 数据
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "批量删除点位", notes = "批量删除点位")
    @PostMapping(path = "/batchDeleteFolder")
    public RestResponse<Void> batchDeleteFolder(@RequestBody DtoQuotationData quotationData) {
        RestResponse<Void> response = new RestResponse<>();
        service.batchDeleteFolder(quotationData.getDetailIds(), quotationData);
        return response;
    }

    /**
     * 检测费明细转点位方案展示
     *
     * @param orderId 订单标识
     * @return RestResponse<List<Map<String,Object>>>
     */
    @ApiOperation(value = "检测费明细转点位方案展示", notes = "检测费明细转点位方案展示")
    @GetMapping(path = "/schemaFolder/{orderId}")
    public RestResponse<List<Map<String,Object>>> schemaFolderShow(@PathVariable String orderId) {
        RestResponse<List<Map<String,Object>>> response = new RestResponse<>();
        response.setData(service.schemaFolderShow(orderId));
        return response;
    }

    /**
     * 订单自动拆单
     *
     * @param detailVO 拆单设置信息
     * @return RestResponse<List < AutoProjectInfoVO>>
     */
    @ApiOperation(value = "订单自动拆单", notes = "订单自动拆单")
    @PostMapping(path = "/autoProject")
    public RestResponse<List<AutoProjectInfoVO>> autoDisassembleOrderProject(@RequestBody SetDetailVO detailVO) {
        RestResponse<List<AutoProjectInfoVO>> restResponse = new RestResponse<>();
        restResponse.setData(service.autoDisassembleOrderProject(detailVO.getYear(), detailVO.getMonth(),
                detailVO.getCount(), detailVO.getOrderId()));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 方案预览
     *
     * @param details 订单详情
     * @return RestResponse<List < DtoSampleFolderTemp>>
     */
    @ApiOperation(value = "方案预览", notes = "方案预览")
    @PostMapping(path = "/generatePlan")
    public RestResponse<List<DtoSampleFolderTemp>> generateProjectPlans(@RequestBody List<DtoQuotationDetail> details) {
        RestResponse<List<DtoSampleFolderTemp>> restResponse = new RestResponse<>();
        restResponse.setData(service.generateProjectPlans(details));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }

    /**
     * 自定拆单，批量创建项目
     *
     * @param detailVO 保存信息
     * @return RestResponse<List < AutoProjectInfoVO>>
     */
    @ApiOperation(value = "自定拆单，批量创建项目", notes = "自定拆单，批量创建项目")
    @PostMapping(path = "/autoSave")
    public RestResponse<Void> autoProjectByDetails(@RequestBody SetDetailVO detailVO) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.autoProjectByDetails(detailVO.getOrderId(), detailVO.getInfoVOList(), detailVO.getIsSampling());
        return restResponse;
    }

}