package com.sinoyd.lims.pro.strategy.context;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.strategy.strategy.fixedPointStat.AbsFixedPointStat;
import com.sinoyd.lims.pro.vo.FixedPointStatTestVO;
import com.sinoyd.lims.strategy.context.FixedPointStatContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 监测点位统计策略管理上下文实现
 *
 * <AUTHOR>
 * @version V5.2.0 2025/4/15
 * @since V100R001
 */
@Service
@Slf4j
public class FixedPointStatContextImpl implements FixedPointStatContext {

    private Map<String, AbsFixedPointStat> fixedPointStatMap;

    @Override
    public List<FixedPointStatTestVO> stat(BaseCriteria criteria, String code, PageBean<DtoAnalyseData> pb) {
        EnumPRO.EnumFixedPointStatType statType = EnumPRO.EnumFixedPointStatType.getByCode(code);
        if (statType != null) {
            AbsFixedPointStat fixedPointStat = fixedPointStatMap.get(statType.getBeanName());
            if (fixedPointStat != null) {
                return fixedPointStat.stat(criteria, pb);
            } else {
                log.error("统计类型未实现: " + statType.getBeanName());
                throw new BaseException("统计类型未实现!");
            }
        } else {
            log.error("无法获取到统计类型:" + code);
            throw new BaseException("无法获取到统计类型:" + code);
        }
    }

    @Autowired
    public void setFixedPointStatMap(Map<String, AbsFixedPointStat> fixedPointStatMap) {
        this.fixedPointStatMap = fixedPointStatMap;
    }
}
