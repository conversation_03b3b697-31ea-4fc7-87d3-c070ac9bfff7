package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * 频次指标操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/22
 * @since V100R001
 */
public interface SamplingFrequencyTestService extends IBaseJpaService<DtoSamplingFrequencyTest, String> {
    /**
     * 根据频次添加频次指标
     *
     * @param samplingFrequencyId 频次id
     * @param testList            测试项目列表
     */
    void addSamplingFrequencyTest(String samplingFrequencyId, List<DtoTest> testList);

    /**
     * 根据点位添加频次指标
     *
     * @param sampleFolderId      点位id
     * @param testList            测试项目列表
     */
    void addSampleFolderTest(String sampleFolderId,List<DtoTest> testList);
}