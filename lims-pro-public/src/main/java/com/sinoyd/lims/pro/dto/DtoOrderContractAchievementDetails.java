package com.sinoyd.lims.pro.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.lims.pro.entity.OrderContractAchievementDetails;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoOrderContractAchievementDetails实体
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_OrderContractAchievementDetails")
@Data
@DynamicInsert
public class DtoOrderContractAchievementDetails extends OrderContractAchievementDetails {

    private static final long serialVersionUID = 1L;

    @Transient
    @Excel(name = "签订人员",needMerge = true,orderNum = "70",width = 11)
    private String signPersonName;

}
