package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 订单签订接收参数
 *
 * <AUTHOR>
 * @version V1.0.0 2022/09/30
 * @since V100R001
 */
@Data
@Accessors(chain = true)
public class DtoSignOrder {
    /**
     * 订单id
     */
    String orderId;

    /**
     * 签订状态
     */
    Integer grantStatus;

    /**
     * 最终报价
     */
    Integer finalQuotation;

    /**
     * 签订日期
     */
    String signDate;
}
