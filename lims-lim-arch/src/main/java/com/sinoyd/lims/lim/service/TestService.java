package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 测试项目接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/16
 * @since V100R001
 */
public interface TestService extends IBaseJpaService<DtoTest, String> {

    /**
     * 新增子测试项目
     *
     * @param parentId 父测试项目id
     * @param testIds  子测试项目id数组
     * @return 增加成功的条数
     */
    Integer addSonTests(String parentId, Collection<String> testIds);

    /**
     * 删除子测试项目
     *
     * @param parentId 父测试项目id
     * @param testIds  子测试项目id数组
     * @return 增加成功的条数
     */
    Integer deleteSonTests(String parentId, Collection<String> testIds);

    /**
     * 获取子测试项目列表
     *
     * @param parentId 父测试项目id
     * @return
     */
    Set<DtoTest> getSonTestList(String parentId);

    /**
     * 根据分析方法id获取测试项目
     *
     * @param analyzeMethodId 分析方法id
     * @return 测试项目集合
     */
    List<DtoTest> getListByAnalyzeMethodId(String analyzeMethodId);

    /**
     * 根据分析方法id删除测试项目
     *
     * @param analyzeMethodIds 分析方法id
     * @return 删除测试项目个数
     */
    Integer deleteByAnalyzeMethodIds(Collection<String> analyzeMethodIds);


    /**
     * 获取所有的redis下测试项目信息
     *
     * @param ids 测试项目ids
     * @return 返回相应的测试项目信息
     */
    List<DtoTest> findRedisByIds(List<String> ids);

    /**
     * 获取默认的测试项目
     *
     * @param sampleTypeId   检测类型id
     * @param analyseItemIds 分析项目id集合
     * @return 测试项目集合
     */
    List<DtoTest> findCommonTestBySampleTypeIdAndAnalyzeItemIdIn(String sampleTypeId, List<String> analyseItemIds);

    /**
     * 获取检测类型下的分析项目
     *
     * @param sampleTypeId 检测类型
     * @return 返回检测类型下的分析项目
     */
    List<DtoAnalyzeItem> findAnalyzeItemBySampleTypeId(String sampleTypeId);

    /**
     * 根据Id获取有假删的数据
     *
     * @param ids 方法ids
     * @return 返回带有假删的测试项目数据
     */
    List<DtoTest> findAllDeleted(Collection<String> ids);


    /**
     * 含假删的测试项目信息
     *
     * @return 返回测试项目
     */
    List<DtoTest> findAllDeleted();

    /**
     * 自增排序值
     */
    void incrementOrderNum(List<String> ids);

    /**
     * 自增排序值
     */
    void decrementOrderNum(List<String> ids);

    /**
     * 冗余测试项目非映射信息
     *
     * @param testList 测试项目列表
     */
    void loadTest(List<DtoTest> testList);

    /**
     * 重新加载缓存
     */
    void initRedis();

    /**
     * 根据分析项目id获取测试项目
     *
     * @param itemId 分析项目id
     * @return 测试项目集合
     */
    List<DtoTest> findByAnalyzeItemId(String itemId);

    /**
     * 修改分析项目信息
     *
     * @param id         测试项目id
     * @param tName      测试项目名称
     * @param aName      分析项目名称
     * @param fullPinYin 全拼
     * @param pinYin     拼音
     * @return 修改个数
     */
    Integer updateAnalyzeItemInfo(String id, String tName, String aName, String fullPinYin, String pinYin);

    /**
     * 修改分析项目统计别名
     *
     * @param id         测试项目id
     * @param itemStatisticalAlias      测试项目分析项目统计别名
     * @return 修改个数
     */
    Integer updateAnalyzeItemStatisticalAlias(String id, String itemStatisticalAlias);


    /**
     * 根据检测类型id获取测试项目
     *
     * @param typeId 检测类型id
     * @return 测试项目集合
     */
    List<DtoTest> findBySampleTypeId(String typeId);

    /**
     * 检出限类型查询
     *
     * @return 检出限类型
     */
    List<Object> examLimitType();

    /**
     * 迁移导出
     *
     * @param criteria 筛选条件
     * @param response 相应流
     */
    void migrationExport(BaseCriteria criteria, HttpServletResponse response);

    /**
     * 重置验证
     *
     * @param ids 测试项目ids
     */
    void resetValidate(List<String> ids);

    /**
     * 批量设置测试项目
     */
    void batchSetTest(Map<String, Object> test);

    /**
     * 获取计算方式
     *
     * @return Map<String, Integer>
     */
    Map<String, Integer> findComputeMode();

    /**
     * 根据分析方法状态剔除测试项目（停用、废止）
     *
     * @param testList 测试项目集合
     */
    void removeByMethodStatus(List<DtoTest> testList);

    /**
     * 根据分析方法状态（停用、废止）过滤出相应测试项目
     *
     * @param testList 测试项目集合
     * @return 满足条件的测试项目集合
     */
    List<DtoTest> filterByMethodStatus(List<DtoTest> testList);


    /**
     * 获取合并的测试项目名称
     *
     * @param testList       需要汇总的测试项目
     * @param parentTestList 总称测试项目
     * @return 合并后的测试项目
     */
    List<DtoTest> getTestTotal(List<DtoTest> testList, List<DtoTest> parentTestList);

    /**
     * 合并统计测试项目
     * @return 结果
     */
    void combineStatistical(PageBean<DtoTest> pageBean,BaseCriteria criteria);

    /**
     * 合并统计测试项目导出
     * @return 结果
     */
    void combineStatisticalExport(BaseCriteria criteria, HttpServletResponse response);

    /**
     * 根据分析项目或者分析方法名称查询测试项目
     *
     * @param analyzeItemNames   分析项目名称集合
     * @return 测试项目集合
     */
    List<DtoTest> findByAnalyzeItemNames(Collection<String> analyzeItemNames);
}