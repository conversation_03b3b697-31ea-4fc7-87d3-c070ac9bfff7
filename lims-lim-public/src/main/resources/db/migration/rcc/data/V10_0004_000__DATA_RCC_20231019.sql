-- 有组织报告应用配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('f963746c-1d74-468d-a8a2-3bb42f3de0a7', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7', 'ReportEdit', '报告编制',
        'OrgGasStd', '有组织气报告', 1, 0, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-10-19 08:40:47', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-10-19 08:40:47', NULL);

-- 有组织报告组件配置
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('1b539b2f-5460-484a-a243-3580a5ceeb42', 'orgGasStdDataSource', '标准版有组织报告检测数据主表', 'dtDataSource', '', 0, 0,
        '{\"headModule\":\"dtOrgHeadStdTable\", \"bodyModule\":\"dtOrgStdTable\", \"secondBodyModule\":\"dtOrgCompoundStdTable\", \"thirdBodyModule\":\"\"}',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2023-10-19 08:41:35',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-10-19 08:41:35');
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('5af3f142-1873-4e0b-80ef-867f1cddcc52', 'dtOrgHeadStdTable', '标准版有组织报告表头组件', 'dtOrgHeadStdTable', '', 0, 0, '',
        b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2023-10-19 09:04:55',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-10-19 09:04:55');
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('7fa8f96d-7d80-4430-9b83-efbe13dde5fc', 'dtOrgCompoundStdTable', '标准版有组织化合物检测结果表组件', 'dtOrgCompoundStdTable',
        'dtOrgCompoundSource', 20, 1, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2023-10-19 08:43:57', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2023-10-19 08:43:57');
INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName, sampleCount, testCount,
                                sonTableJson, isCompound, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('bc73b465-89c2-47d0-ad3e-1f6bd6be99da', 'dtOrgStdTable', '标准版有组织检测结果表组件', 'dtOrgStdTable', 'dtOrgSource', 0, 20,
        '', b'0', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2023-10-19 08:42:06',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2023-10-19 08:42:06');


--有组织报告组件模板关联配置
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('1528fdab-50ee-4c49-b38c-ae430327a16f', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        '9b557e2c-bd2c-4c73-8a6c-4a55e38f52ba');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('17e4eaa9-41cb-48fd-a153-986d512cf161', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        '3477d948-c1eb-418f-8fbd-81fc1295cee6');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('221a4db0-7a13-4e83-b384-adc0951a9ed4', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        'fe05546e-47f5-436a-a99f-970a96bcd590');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('51128521-561d-4d52-bbcc-74de37f715ef', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        '1b539b2f-5460-484a-a243-3580a5ceeb42');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('a58b1e75-fb69-4aeb-b65a-e9c9a11fc53d', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        '76d9545f-8eb1-4ab2-8aa4-f9decfe2a54e');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b563a2aa-68cf-4af5-b4a1-29881823519e', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        '55fe89e1-dc8c-4b86-b03b-e6dbf3143340');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b6e399f7-9421-40e1-a655-509d534506ab', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('c82c335d-adc8-4a78-9d01-d04e084f68a0', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        '44c0ef90-1cee-4379-bdec-e502a875c0a7');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('ff88fbf8-1011-47d8-8ad0-ac327bc1b099', '56b3f7b9-bee9-4419-a986-c4cf5e2812b7',
        'fff21a0d-5e6f-4608-b966-d09bf86cc5f9');



INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('431e7e92-a38d-44e5-99bd-2fb8e2e4f1ca', '51128521-561d-4d52-bbcc-74de37f715ef',
        'sampleData_sampleFolderId_fieldGroupType', 10);
INSERT INTO TB_LIM_ReportModule2GroupType(id, reportConfigModuleId, groupTypeName, priority)
VALUES ('6076702e-3691-4228-aadb-6b6c936bb9e3', '51128521-561d-4d52-bbcc-74de37f715ef',
        'sampleData_cycleOrder_fieldGroupType', 9);
