package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.OdPersonAnswerCriteria;
import com.sinoyd.lims.od.dto.DtoOdPersonAnswer;
import com.sinoyd.lims.od.service.OdPersonAnswerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * odPersonAnswer服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例: odPersonAnswer服务")
@RestController
@RequestMapping("api/ods/odPersonAnswer")
@Validated
public class OdPersonAnswerController extends BaseJpaController<DtoOdPersonAnswer, String, OdPersonAnswerService> {

    /**
     * 分页动态条件查询OdPersonAnswer
     *
     * @param odPersonAnswerCriteria 条件参数
     * @return RestResponse<List < OdPersonAnswer>>
     */
    @ApiOperation(value = "分页动态条件查询ReportApply", notes = "分页动态条件查询ReportApply")
    @GetMapping
    public RestResponse<List<DtoOdPersonAnswer>> findByPage(OdPersonAnswerCriteria odPersonAnswerCriteria) {
        PageBean<DtoOdPersonAnswer> pageBean = super.getPageBean();
        RestResponse<List<DtoOdPersonAnswer>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, odPersonAnswerCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }
    /**
     * 根据id查询odPersonAnswer
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询odPersonAnswer", notes = "根据id查询odPersonAnswer")
    @GetMapping("/{id}")
    public RestResponse<DtoOdPersonAnswer> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOdPersonAnswer> restResp = new RestResponse<>();
        DtoOdPersonAnswer entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增odPersonAnswer
     *
     * @param odPersonAnswer odPersonAnswer实体
     * @return 新增的odPersonAnswer实体
     */
    @ApiOperation(value = "新增odPersonAnswer", notes = "新增odPersonAnswer")
    @PostMapping("")
    public RestResponse<DtoOdPersonAnswer> create(@Validated @RequestBody DtoOdPersonAnswer odPersonAnswer) {
        RestResponse<DtoOdPersonAnswer> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoOdPersonAnswer data = service.save(odPersonAnswer);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新odPersonAnswer
     *
     * @param odPersonAnswer odPersonAnswer实体
     * @return 更新后的odPersonAnswer实体
     */
    @ApiOperation(value = "更新odPersonAnswer", notes = "更新odPersonAnswer")
    @PutMapping("")
    public RestResponse<DtoOdPersonAnswer> update(@Validated @RequestBody DtoOdPersonAnswer odPersonAnswer) {
        RestResponse<DtoOdPersonAnswer> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoOdPersonAnswer data = service.update(odPersonAnswer);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }


    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }

    /**
     * 提交odPersonAnswer
     *
     * @param odPersonAnswer odPersonAnswer实体
     * @return 新增的odPersonAnswer实体
     */
    @ApiOperation(value = "提交odPersonAnswer", notes = "提交odPersonAnswer")
    @PostMapping("/submit")
    public RestResponse<DtoOdPersonAnswer> submit(@Validated @RequestBody DtoOdPersonAnswer odPersonAnswer) {
        RestResponse<DtoOdPersonAnswer> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoOdPersonAnswer data = service.submit(odPersonAnswer);
        restResponse.setData(data);
        restResponse.setCount(1);
        return restResponse;
    }
}
