package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.NoticeMsgCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoNoticeMsg;
import com.sinoyd.lims.lim.service.NoticeMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 公告留言
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/22
 * @since V100R001
 */
@Api(tags = "公告留言: NoticeMsg服务")
@RestController
@RequestMapping("api/lim/noticeMsg")
@Validated
public class NoticeMsgController extends BaseJpaController<DtoNoticeMsg, String, NoticeMsgService> {


    /**
     * 分页动态条件查询NoticeMsg
     *
     * @param noticeMsgCriteria 条件参数
     * @return RestResponse<List   <   NoticeMsg>>
     */
    @ApiOperation(value = "分页动态条件查询NoticeMsg", notes = "分页动态条件查询NoticeMsg")
    @GetMapping
    public RestResponse<List<DtoNoticeMsg>> findByPage(NoticeMsgCriteria noticeMsgCriteria) {
        PageBean<DtoNoticeMsg> pageBean = super.getPageBean();
        RestResponse<List<DtoNoticeMsg>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, noticeMsgCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询NoticeMsg
     *
     * @param id 主键id
     * @return RestResponse<DtoNoticeMsg>
     */
    @ApiOperation(value = "按主键查询NoticeMsg", notes = "按主键查询NoticeMsg")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoNoticeMsg> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoNoticeMsg> restResponse = new RestResponse<>();
        DtoNoticeMsg noticeMsg = service.findOne(id);
        restResponse.setData(noticeMsg);
        restResponse.setRestStatus(StringUtil.isNull(noticeMsg) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增NoticeMsg
     *
     * @param noticeMsg 实体列表
     * @return RestResponse<DtoNoticeMsg>
     */
    @ApiOperation(value = "新增NoticeMsg", notes = "新增NoticeMsg")
    @PostMapping
    public RestResponse<DtoNoticeMsg> create(@Validated @RequestBody DtoNoticeMsg noticeMsg) {
        RestResponse<DtoNoticeMsg> restResponse = new RestResponse<>();
        restResponse.setData(service.save(noticeMsg));
        return restResponse;
    }

    /**
     * 新增NoticeMsg
     *
     * @param noticeMsg 实体列表
     * @return RestResponse<DtoNoticeMsg>
     */
    @ApiOperation(value = "修改NoticeMsg", notes = "修改NoticeMsg")
    @PutMapping
    public RestResponse<DtoNoticeMsg> update(@Validated @RequestBody DtoNoticeMsg noticeMsg) {
        RestResponse<DtoNoticeMsg> restResponse = new RestResponse<>();
        restResponse.setData(service.update(noticeMsg));
        return restResponse;
    }

    /**
     * "根据id批量删除NoticeMsg
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除NoticeMsg", notes = "根据id批量删除NoticeMsg")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 单个删除 真
     *
     * @param id
     */
    @ApiOperation(value = "根据id删除公告留言", notes = "根据id删除公告留言")
    @DeleteMapping("/{id}")
    public RestResponse<String> deleteOne(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.delete(id);
        restResp.setCount(1);

        return restResp;
    }
}