package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelationParams;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeItemRelationParamsRepository;
import com.sinoyd.lims.lim.service.AnalyzeItemRelationParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 分析项目关系参数接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-08
 * @since V100R001
 */
@Service
public class AnalyzeItemRelationParamsServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoAnalyzeItemRelationParams, String, AnalyzeItemRelationParamsRepository>
        implements AnalyzeItemRelationParamsService {

    @Autowired
    @Lazy
    private AnalyzeItemService analyzeItemService;

    /**
     * 获取分析项目关系参数列表
     */
    @Override
    public List<DtoAnalyzeItemRelationParams> getList(String relationId, String orderNum) {
        return repository.getList(relationId);
    }

    /**
     * 新增分析项目关系参数
     */
    @Transactional
    @Override
    public Integer create(String relationId, Collection<String> analyzeItemIds) {

        List<DtoAnalyzeItemRelationParams> params = new ArrayList<>();

        repository.deleteByRelationId(relationId);

        List<DtoAnalyzeItem> entities = analyzeItemService.findByIds(analyzeItemIds);
        for (DtoAnalyzeItem entity : entities) {
            DtoAnalyzeItemRelationParams relationParam = new DtoAnalyzeItemRelationParams();
            relationParam.setAnalyzeItemId(entity.getId());
            relationParam.setAnalyzeItemName(entity.getAnalyzeItemName());
            relationParam.setOrderNum(0);
            relationParam.setRelationId(relationId);
            params.add(relationParam);
        }

        repository.save(params);

        return params.size();
    }

    /**
     * 删除分析项目关系参数
     */
    @Transactional
    @Override
    public Integer deleteParams(String relationId, Collection<String> analyzeItemIds) {
        return repository.deleteAnalyzeItemId(relationId,analyzeItemIds);
    }

    @Override
    public  List<DtoAnalyzeItemRelationParams> findByRelationId(String relationId) {
        return repository.getList(relationId);
    }
}