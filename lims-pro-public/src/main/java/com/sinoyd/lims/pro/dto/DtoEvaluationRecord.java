package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.dto.customer.DtoEvaluationExceed;
import com.sinoyd.lims.pro.entity.EvaluationRecord;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoEvaluationRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_EvaluationRecord")
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoEvaluationRecord extends EvaluationRecord {

   /**
    * 检测小类id
    */
   @Transient
   private String sampleTypeId;

   /**
    * 检测小类名称
    */
   @Transient
   private String sampleTypeName;

   /**
    * 分析项目id
    */
   @Transient
   private String analyzeItemId;

   /**
    * 分析因子名称
    */
   @Transient
   private String redAnalyzeItemName;

   /**
    * 分析方法名称
    */
   @Transient
   private String redAnalyzeMethodName;

   /**
    * 国家标准
    */
   @Transient
   private String redCountryStandard;

   /**
    * 评价标准名称
    */
   @Transient
   private String evaluationName = "";

   /**
    * 评价标准等级名称
    */
   @Transient
   private String evaluationLevelName = "";

   /**
    * 关联id集合
    */
   @Transient
   private List<String> objectIds = new ArrayList<>();

   /**
    * 平均值超标信息
    */
   @Transient
   private DtoEvaluationExceed avg;

   /**
    * 最小值超标信息
    */
   @Transient
   private DtoEvaluationExceed min;

   /**
    * 最大值超标信息
    */
   @Transient
   private DtoEvaluationExceed max;

   @Transient
   private Integer testOrder;

   /**
    * 量纲名称
    */
   @Transient
   private String dimensionName;
}