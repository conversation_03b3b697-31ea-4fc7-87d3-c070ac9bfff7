package com.sinoyd.lims.api.dto.vo;

import com.sinoyd.lims.instrument.parse.dto.DtoTcpData;
import lombok.Data;

import java.util.List;

@Data
public class WWFolderVO {

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 受检方
     */
    private String enterpriseName;

    /**
     * 仪器名称
     */
    private String devName;

    /**
     * 设备编号
     */
    private String devNum;

    /**
     * 解析时间
     */
    private String parseTime;

    /**
     * 解析数据
     */
    private List<DtoTcpData> tcpDataList;
}
