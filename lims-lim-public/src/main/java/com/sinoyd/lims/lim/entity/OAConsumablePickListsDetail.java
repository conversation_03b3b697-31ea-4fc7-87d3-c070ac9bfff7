package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * OAConsumablePickListsDetail实体
 * <AUTHOR>
 * @version V1.0.0 2020/4/2
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OAConsumablePickListsDetail")
 @Data
 public  class OAConsumablePickListsDetail extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

   public  OAConsumablePickListsDetail() {
      this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
      this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
    * 消耗品Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("消耗品Id")
	private String consumableId;

    /**
    * 名称
    */
    @ApiModelProperty("名称")
    @Length(message = "名称{validation.message.length}", max = 255)
    private String consumableName;

    /**
    * 规格
    */
    @ApiModelProperty("规格")
    @Length(message = "规格{validation.message.length}", max = 255)
    private String specification;

    /**
    * 领用数量
    */
    @Column(nullable=false)
    @ApiModelProperty("领用数量")
    private BigDecimal materialNum;

    /**
    * 单位
    */
    @Column(length=100)
    @ApiModelProperty("单位")
    @Length(message = "单位{validation.message.length}", max = 100)
    private String materialUnit;

    /**
    * 用途
    */
    @Column(length=100)
    @ApiModelProperty("用途")
    @Length(message = "用途{validation.message.length}", max = 100)
    private String materialUse;

    /**
    * 备注
    */
    @Column(length=500)
    @ApiModelProperty("备注")
    @Length(message = "规格{validation.message.length}", max = 500)
    private String remark;

    /**
    * 等级
    */
    @Column(length=100)
    @ApiModelProperty("等级")
    @Length(message = "等级{validation.message.length}", max = 100)
    private String gradeName;

    /**
    * 标准编号
    */
    @Column(length=100)
    @ApiModelProperty("编号")
    @Length(message = "编号{validation.message.length}", max = 100)
    private String codeInStation;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
 }