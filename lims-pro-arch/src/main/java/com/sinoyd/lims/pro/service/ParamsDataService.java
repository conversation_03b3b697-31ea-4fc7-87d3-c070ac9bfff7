package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.customer.DtoSampleInfo;
import com.sinoyd.lims.pro.dto.customer.DtoSampleItemParams;
import com.sinoyd.lims.pro.dto.customer.DtoSampleParamsTemp;

import java.util.*;


/**
 * 参数数据操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/21
 * @since V100R001
 */
public interface ParamsDataService extends IBaseJpaService<DtoParamsData, String> {

    /**
     * 保存参数数据
     *
     * @param entitys 参数数据
     */
    void saveAsync(Collection<DtoParamsData>entitys);

    /**
     * 根据对象id获取相关的数据
     *
     * @param objectIds  对象id
     * @param objectType 对象的类型
     * @return 返回相应数据
     */
    List<DtoParamsData> findObjectIdsAndObjectType(List<String> objectIds, Integer objectType);

    /**
     * 根据样品获取样品参数
     *
     * @param sampleIds 样品id集合
     */
    List<DtoParamsData>findBySampleIds(List<String>sampleIds);

    /**
     * 保存样品分析项目参数
     *
     * @param temp 传输结构
     */
    void saveAnalyzeItemParamsData(DtoSampleItemParams temp);

    /**
     * 保存样品参数
     *
     * @param temp 传输结构
     */
    void saveSampleParamsData(DtoSampleParamsTemp temp);

    /**
     * 计算样品参数
     *
     * @param info 样品信息
     */
    List<Map<String, Object>> calculateParamsData(DtoSampleInfo info);

    /**
     * 异常数据处理
     */
    void exceptionalData();

    /**
     * 将样品其余分组参数同步为选定的分组参数数据
     */
    void syncGroupParams(String groupId,List<String> paramsConfigIds,List<Map<String,Object>> sampleList);
}