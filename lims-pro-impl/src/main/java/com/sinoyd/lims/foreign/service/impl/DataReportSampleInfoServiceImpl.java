package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataReportFolderSortInfoVO;
import com.sinoyd.lims.foreign.DataReportSampleInfoVO;
import com.sinoyd.lims.foreign.DataSamplePreparationVO;
import com.sinoyd.lims.foreign.service.DataReportSampleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报告样品信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataReportSampleInfoServiceImpl extends AbsQueryDataServiceImpl<DataReportSampleInfoVO> implements DataReportSampleInfoService {


    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id,reportId,sampleId,sampleCode,");
        sql.append("sampleRemark,reportFolderInfoId,orgId,domainId ");
        sql.append(" from TB_PRO_ReportSampleInfo ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime ", "syncTime");
        super.appendCondition(sql, map, "AND reportId in (:reportIds)", "reportIds");
        super.appendCondition(sql, map, "AND sampleId in (:sampleIds)", "sampleIds");
    }

    @Override
    protected RowMapper<DataReportSampleInfoVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataReportSampleInfoVO reportSampleInfoVO = new DataReportSampleInfoVO();
            reportSampleInfoVO.setId(UUIDHelper.NewID());
            reportSampleInfoVO.setReportId(rs.getString("reportId"));
            reportSampleInfoVO.setSampleId(rs.getString("sampleId"));
            reportSampleInfoVO.setSampleCode(rs.getString("sampleCode"));
            reportSampleInfoVO.setSampleRemark(rs.getString("sampleRemark"));
            reportSampleInfoVO.setReportFolderId(rs.getString("reportFolderInfoId"));
            reportSampleInfoVO.setOrgId(rs.getString("orgId"));
            reportSampleInfoVO.setDomainId(rs.getString("domainId"));
            reportSampleInfoVO.setSyncTime(new Date());
            return reportSampleInfoVO;
        };
    }

}
