package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTest;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataDelete;
import com.sinoyd.lims.pro.dto.customer.DtoProjectInquiry;
import com.sinoyd.lims.pro.entity.SamplingFrequencyTest;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.probase.service.ProBaseService;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 检测业务操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/05
 * @since V100R001
 */
public interface ProService extends ProBaseService {

    /**
     * 获取指标
     *
     * @param sampleId 样品id
     * @return 指标
     */
    String getAnalyzeItems(String sampleId);

    /**
     * 获取指标
     *
     * @param analyseDataList 分析数据
     * @return 指标
     */
    String getAnalyzeItemsByAna(List<DtoAnalyseData> analyseDataList);

    /**
     * 获取指标
     *
     * @param sftList 频次指标
     * @return 指标
     */
    String getAnalyzeItemsByFrequencyTest(List<SamplingFrequencyTest> sftList);

    /**
     * 获取指标
     *
     * @param testList 测试项目
     * @return 指标
     */
    String getAnalyzeItemsByTest(List<DtoTest> testList);

    /**
     * 添加样品的分析数据及频次指标（样品登记添加测试项目）
     *
     * @param sampleId       样品id
     * @param testIds        测试项目id集合
     * @param isAddForFolder 该点位统一添加
     */
    void addSampleTest(String sampleId, List<String> testIds, Boolean isAddForFolder);

    /**
     * 覆盖样品的分析数据及频次指标
     *
     * @param sampleId       样品id
     * @param testIds        测试项目id集合
     * @param isAddForFolder 该点位统一添加
     * @return
     */
    List<DtoTest> repeatSampleTest(String sampleId, List<String> testIds, Boolean isAddForFolder);

    /**
     * 删除样品指标
     *
     * @param dtoAnalyseDataDelete 删除结构体
     * @return 变动的样品id
     */
    List<String> deleteAnalyseDataBySample(DtoAnalyseDataDelete dtoAnalyseDataDelete);

    /**
     * 删除样品指标
     *
     * @param sampleId 样品id
     * @param testIds  测试项目id
     */
    void deleteAnalyseData(String sampleId, List<String> testIds);

    /**
     * 修改样品变更状态
     *
     * @param sampleIds 样品id
     */
    List<String> updateReportSample(List<String> sampleIds);

    /**
     * 项目删除
     *
     * @param projectId 项目id
     */
    void deleteProject(String projectId);

    /**
     * 项目删除
     *
     * @param projectIds 项目id集合
     */
    void deleteProjects(List<String> projectIds);

    /**
     * 项目办结
     *
     * @param projectIds 项目idlist
     * @param opinion    办结意见
     */
    void finishProject(List<String> projectIds, String opinion);

    /**
     * 获取项目进度
     *
     * @param projectId 项目id
     * @return 项目进度
     */
    DtoProjectInquiry findProjectInquiry(String projectId);

    /**
     * 获取项目文档
     *
     * @param projectId 项目id
     * @return 项目文档
     */
    Map<String, Object> findDocByProjectId(String projectId, Integer type, String docTypeId);

    /**
     * 获取项目文档
     *
     * @param reportId  报告id
     * @param type      类型
     * @param docTypeId 附件类型
     * @return 项目文档
     */
    Map<String, Object> findDocByReportId(String reportId, Integer type, String docTypeId);

    /**
     * 流程通知，开启单独事务
     *
     * @param action 触发动作
     * @param params 参数序列
     */
    void sendProMessage(EnumPRO.EnumProAction action, Object... params);

    /**
     * 流程通知，不开启单独事务
     *
     * @param action 触发动作
     * @param params 参数序列
     */
    void sendProMessageWoTransactional(EnumPRO.EnumProAction action, Object... params);

    /**
     * 比对数据库数据与web端提交的数据变化
     *
     * @param from 数据库实体
     * @param to   web提交实体
     * @param <LT> 继承entity的dto，仅比对数据库字段
     * @param <T>  接收web数据的dto
     * @return 记录修改的字段与修改信息（记录修改前、修改后的值）的map
     */
    <LT, T> Map<String, Map<String, Object>> getCompare(LT from, T to);

    /**
     * 判断开关是否开启
     *
     * @param switchCode 开关编码
     * @return 是否开启
     */
    Boolean switchIsOpen(String switchCode);

    /**
     * 数据修约
     *
     * @param mostSignificance 有效位数
     * @param mostDecimal      小数位数
     * @param value            值
     * @return 返回修约值
     */
    String getDecimal(Integer mostSignificance, Integer mostDecimal, String value);

    /**
     * 数据修约
     *
     * @param mostSignificance 有效位数
     * @param mostDecimal      小数位数
     * @param value            值
     * @param isSci            是否强制科学计数法
     * @return 返回修约值
     */
    String getDecimal(Integer mostSignificance, Integer mostDecimal, String value,Boolean isSci);


    /**
     * 根据测试项目，检测类型id，值进行修约
     *
     * @param testId       测试项目id
     * @param sampleTypeId 检测类型id
     * @param value        值
     * @return 返回修约值
     */
    String getDecimal(String testId, String sampleTypeId, String value);


    /**
     * 根据测试项目，检测类型id，值进行修约
     *
     * @param test         测试项目信息
     * @param sampleTypeId 检测类型id
     * @param value        值
     * @return 返回修约值
     */
    String getDecimal(DtoTest test, String sampleTypeId, String value);

    /**
     * 根据测试项目id进行数据修约
     *
     * @param testId 测试项目id
     * @param value  修约值
     * @return 返回修约值
     */
    String getDecimal(String testId, String value);

    /**
     * 根据测试项目进行数据修约
     *
     * @param test  测试项目信息
     * @param value 修约值
     * @return 返回修约值
     */
    String getDecimal(DtoTest test, String value);

    /**
     * 检出限的判断
     *
     * @param testValue          出证结果
     * @param examLimitValue     检出限值
     * @param examLimitValueLess 检出限比较符合
     * @return 返回相应的比较结果
     */
    String getExamValue(String testValue, String examLimitValue, String examLimitValueLess);

    /**
     * 检出限的判断(用于计算，小于检出限，则拿检出限的一半返回进行计算)
     *
     * @param value          出证结果
     * @param examLimitValue 检出限值
     * @return 返回相应的比较结果
     */
    String getExamValueWoSymbol(String value, String examLimitValue);

    /**
     * 判断值是否小于检出限
     *
     * @param testValue      判断值
     * @param examLimitValue 检出限
     * @return 是否小于检出限
     */
    Boolean isExamValue(String testValue, String examLimitValue);

    /**
     * 核对样品状态
     *
     * @param project 项目
     * @param samples 样品信息
     */
    void checkPrepareSample(DtoProject project, List<DtoSample> samples);

    /**
     * 批量下载项目一项一档文件，打包成zip下载
     *
     * @param projectId 项目id
     * @param response  response流
     * @return 结果标示
     */
    String batchDownload(String projectId, HttpServletResponse response);

    /**
     * 判断测试项目数据中的测试项目在系统中是否被删除是否存在
     *
     * @param samplingFrequencyTest 点位频次id
     */
    void checkTestIsExists(List<DtoSamplingFrequencyTest> samplingFrequencyTest);

    /**
     * 导出PDF
     *
     * @param criteria   参数集合
     * @param response   响应体
     * @return 文档名称
     */
    String downLoadPDF(BaseCriteria criteria, HttpServletResponse response) throws IOException;

    /**
     * 导出解析图谱文件zip
     *
     * @param projectId 项目id
     * @param response  响应体
     * @return 文档
     */
    String exportAtlasZip(String projectId, HttpServletResponse response);

    /**
     * 查询测点示意图
     *
     * @param criteria 查询条件
     */
    void getPointPicDocument(PageBean<DtoDocument> page, BaseCriteria criteria);
}
