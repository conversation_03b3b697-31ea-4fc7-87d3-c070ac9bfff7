package com.sinoyd.base.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.dto.vo.LoginParamVO;
import com.sinoyd.base.service.ILoginAuthService;
import com.sinoyd.boot.auth.server.dto.AuthResponseDto;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.common.http.HTTPCaller;
import com.sinoyd.common.http.NameValuePair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 门户登录服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/29
 */
@Service
@Slf4j
public class LoginAuthServiceImpl implements ILoginAuthService {

    /**
     * 是否启用https
     */
    @Value("${lims.https.enabled:false}")
    private Boolean enabledHttps;

    /**
     * 网关地址
     */
    @Value("${frame-boot.gateUrlPrefix:none}")
    private String gateWay;

    private final static String INVALID_USER_VALIDATION_MSG = "登录账号无权限，请联系管理员或尝试其他账号";

    @Override
    public AuthResponseDto login(LoginParamVO paramVO) {
        return login(paramVO.getUid(), paramVO.getPid());
    }

    /**
     * 登录
     *
     * @param uid uid
     * @param pid pid
     * @return 登录结果
     */
    private AuthResponseDto login(String uid, String pid) {
        try {
            List<NameValuePair> nameValuePairList = new ArrayList<>();
            nameValuePairList.add(new NameValuePair("uid", uid));
            nameValuePairList.add(new NameValuePair("pid", pid));
            String loginResult = HTTPCaller.getInstance(enabledHttps).postAsString(getHost(), "/api/proxy/auth/login", Collections.emptyList(), nameValuePairList);
            RestResponse<AuthResponseDto> response = JsonUtil.toObject(loginResult, RestResponse.class);
            if (response.isSuccess()) {
                return JSON.toJavaObject(JSONObject.parseObject(loginResult).getJSONObject("data"),
                        AuthResponseDto.class);
            } else {
                throw new RuntimeException(INVALID_USER_VALIDATION_MSG);
            }
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            throw new BaseException("登录失败，请确认账号密码是否正确");
        }
    }

    /**
     * 获取网关地址, ip:port
     *
     * @return 网关地址
     */
    protected String getHost() {
        return this.gateWay.replace("/api/proxy", "");
    }
}
