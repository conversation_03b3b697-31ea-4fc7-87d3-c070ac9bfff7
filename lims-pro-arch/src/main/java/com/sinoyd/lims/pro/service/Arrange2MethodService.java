package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoArrange2Method;

import java.util.List;

/**
 * 采样方法操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/7/25
 * @since V100R001
 */
public interface Arrange2MethodService extends IBaseJpaService<DtoArrange2Method, String> {

    /**
     * 根据采样计划id查询采样方法
     *
     * @param samplingPlanId    采样计划id
     * @return  采样方法
     */
    List<DtoArrange2Method> findBySamplingPlanId(String samplingPlanId);

}
