package com.sinoyd.lims.pro.service;

import java.util.List;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePickListsDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;

/**
 * 领料申请操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-02
 * @since V100R001
 */
public interface OAConsumablePickListsService {

    /**
     * 分页查询消耗品列表
     *
     * @param pageBean     分页条件
     * @param baseCriteria 查询条件
     */
    void findConsumableByPage(PageBean<DtoConsumable> pageBean, BaseCriteria baseCriteria);

    /**
     * 添加审批并启动流程
     *
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto);

    /**
     * 查询任务详细信息
     *
     * @param taskId 任务ID
     * @return
     */
    DtoOATaskDetail<List<DtoOAConsumablePickListsDetail>, List<DtoConsumable>> findOATaskDetail(String taskId);

    /**
     * 保存为草稿
     *
     * @param taskDto 表单数据
     * @return oa任务
     */
    OATask saveAsDraft(DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto);

    /**
     * 保存为草稿
     *
     * @param taskDto 表单数据
     * @return oa任务
     */
    OATask draftSave(DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto);

    /**
     * 草稿提交
     *
     * @param taskDto 表单数据
     * @return oa任务
     */
    String draftSubmit(DtoOATaskCreate<List<DtoOAConsumablePickListsDetail>> taskDto);
}