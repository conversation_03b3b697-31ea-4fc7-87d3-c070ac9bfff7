package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.FeeConfig;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoFeeConfig实体
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_FeeConfig") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoFeeConfig extends FeeConfig {
   private static final long serialVersionUID = 1L;
 }