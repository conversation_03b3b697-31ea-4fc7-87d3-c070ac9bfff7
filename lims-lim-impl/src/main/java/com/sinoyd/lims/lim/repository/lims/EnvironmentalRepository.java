package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmental;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 环境管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
public interface EnvironmentalRepository extends IBaseJpaRepository<DtoEnvironmental, String> {


    /**
     * 根据实验室名称、编号进行查重
     * @param labCode
     * @param labName
     * @return
     */
    @Query("select count(p.id) from DtoEnvironmental p where p.id != :id and p.labName = :labName and p.labCode = :labCode and p.isDeleted != 1")
    Integer getByLabCodeAndName(@Param("id")String id,@Param("labCode")String labCode,@Param("labName")String labName);
}