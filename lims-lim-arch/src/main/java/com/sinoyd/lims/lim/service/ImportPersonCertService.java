package com.sinoyd.lims.lim.service;

import com.sinoyd.lims.lim.dto.customer.DtoImportPersonCert;
import com.sinoyd.lims.lim.dto.customer.DtoImportStandardMethod;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethod;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 国家标准方法导入接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/14
 * @since V100R001
 */
public interface ImportPersonCertService extends ImportExcelService<DtoImportPersonCert, DtoPersonCert> {
    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    void downLoadExcel(HttpServletResponse response, Map<String, String> sheetNames, String fileName);

}
