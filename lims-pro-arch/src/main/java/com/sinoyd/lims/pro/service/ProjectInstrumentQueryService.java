package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.lims.lim.dto.customer.DtoProjectInstrumentAccess;


/**
 * ProjectInstrument查询操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
public interface ProjectInstrumentQueryService extends IBaseJpaService<DtoProjectInstrument, String> {

    void findAccessRecordByPage(PageBean<DtoProjectInstrumentAccess> pb, BaseCriteria instrumentAccessRecordCriteria);

    /**
     * 查询仪器出入库记录
     * @param pb 分页记录
     * @param ProjectInstrumentQueryCriteria 查询条件
     */
    void findByPageQuery(PageBean<DtoProjectInstrument> pb, BaseCriteria ProjectInstrumentQueryCriteria);
}