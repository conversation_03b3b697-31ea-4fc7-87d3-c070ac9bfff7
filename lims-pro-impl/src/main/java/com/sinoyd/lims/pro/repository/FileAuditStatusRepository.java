package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoFileAuditStatus;

import java.util.Collection;
import java.util.List;


/**
 * FileAuditStatus数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/19
 * @since V100R001
 */
public interface FileAuditStatusRepository extends IBaseJpaPhysicalDeleteRepository<DtoFileAuditStatus, String> {
    /**
     * 根据文件审批标识集合删除
     * @param fileAuditIds 文件审批标识集合
     * @return 数量
     */
    Integer deleteByFileAuditIdIn(Collection<String> fileAuditIds);

    /**
     * 根据文件审批标识和步骤编码查询
     * @param fileAuditId 文件审批标识
     * @param stepCode   步骤编码
     * @return 结果
     */
    List<DtoFileAuditStatus> findByFileAuditIdAndStepCode(String fileAuditId,String stepCode);

    /**
     * 根据文件审批标识查询
     * @param fileAuditId 文件审批标识
     * @return 结果
     */
    List<DtoFileAuditStatus> findByFileAuditId(String fileAuditId);
}