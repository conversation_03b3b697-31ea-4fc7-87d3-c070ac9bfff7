package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.dto.customer.DtoImportTest;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.PinYinUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPerson2Test;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.entity.Person2Test;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.*;
import com.sinoyd.lims.lim.service.ExpImpTestService;
import com.sinoyd.lims.lim.service.Person2TestService;
import com.sinoyd.lims.lim.service.RecordConfig2TestService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyTestVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 测试项目导入修改实现
 *
 * @version V1.0.0 2023/12/20
 * @author: hukq
 * @since V100R001
 */
@Service
public class ExpImpTestServiceImpl extends BaseJpaServiceImpl<DtoTest, String, TestRepository> implements ExpImpTestService {

    //region 依赖
    private ParamsFormulaRepository paramsFormulaRepository;
    private ParamsTestFormulaRepository paramsTestFormulaRepository;
    private ParamsPartFormulaRepository paramsPartFormulaRepository;
    private Params2ParamsFormulaRepository params2ParamsFormulaRepository;
    private ParamsRepository paramsRepository;
    private ParamsConfigRepository paramsConfigRepository;
    private AnalyzeItemRepository analyzeItemRepository;
    private AnalyzeMethodRepository methodRepository;
    private DimensionRepository dimensionRepository;
    private TestService testService;
    private RecordConfig2TestService recordConfig2TestService;
    private SampleTypeRepository sampleTypeRepository;
    private PersonRepository personRepository;
    private RecordConfigRepository recordConfigRepository;
    private Person2TestService person2TestService;
    private ImportUtils importUtils;
    private RedisTemplate redisTemplate;
    //endregion

    @Override
    @Transactional
    public List<DtoTest> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);
        //是否关联导入分析项目
        Boolean isImportAnaItem = (Boolean) objectMap.get(0);
        //是否关联导入分析方法
        Boolean isImportAnaMethod = (Boolean) objectMap.get(1);
        //是否关联导入量纲
        Boolean isImportDimension = (Boolean) objectMap.get(2);
        //是否关联导入公式
        Boolean isImportFormula = (Boolean) objectMap.get(3);
        //是否关联导入测试人员
        Boolean isImportTestPerson = (Boolean) objectMap.get(4);
        //获取校验器
        ImpModifyTestVerify verifyHandler = getVerifyHandler(isImportAnaItem, isImportAnaMethod, isImportDimension, isImportFormula, isImportTestPerson);
        //导入结果
        ExcelImportResult<DtoImportTest> importResult = getExcelData(verifyHandler, file, response);
        //校验正确数据
        List<DtoImportTest> importList = importResult.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getRedAnalyzeItemName()));

        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }

        //校验是否存在原始记录单未提前配置好
        Boolean existReportWithoutConfig = checkReportConfig(importList);
        if (existReportWithoutConfig) {
            throw new BaseException("存在原始记录单未配置，请检查后导入");
        }

        //获取导入的所有的测试项目编号
        List<String> importTestIds = importList.stream().map(DtoImportTest::getId).collect(Collectors.toList());
        //获取导入的所有的分析因子的名称
        List<String> importAnaItems = importList.stream().map(DtoImportTest::getRedAnalyzeItemName).collect(Collectors.toList());
        //存放所有分析方法数据（系统中的所有分析方法 + 本次导入新增的分析方法）
        List<DtoAnalyzeMethod> methodList = new ArrayList<>();
        //获取导入的所有的量纲
        List<String> importDimension = importList.stream().map(DtoImportTest::getDimensionId).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        //获取系统中的所有检测类型
        List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll();
        //获取系统中的所有原始记录单
        List<DtoRecordConfig> dtoRecordConfigList = recordConfigRepository.findAllByRecordType(EnumLIM.EnumRecordType.原始记录单.getValue());
        //endregion

        //region 是否导入分析项目
        importAnaItems(importAnaItems, isImportAnaItem);
        // 是否导入分析方法
        importAnaMethods(importList, isImportAnaMethod, methodList);
        // 是否导入量纲
        importDimensions(importDimension, isImportDimension);
        // 公式参数处理
        paramsFormulaHandle(importList, sampleTypeList, isImportFormula);
        // 检测人员处理
        testToPersonHandle(importList, sampleTypeList, isImportTestPerson);
        //添加数据处理
        List<DtoTest> importTests = addDataHandle(importList, importAnaItems, methodList, importDimension, sampleTypeList);
        //添加数据
        addData(importTests);
        addRelation(importList, dtoRecordConfigList);
        //endregion

        return testService.findRedisByIds(importTestIds);
    }

    @Override
    @Transactional
    public void addData(List<DtoTest> data) {
        if (StringUtil.isNotEmpty(data)) {
            repository.save(data);
            testService.initRedis();
        }
    }

    @Override
    public ExcelImportResult<DtoImportTest> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    @Override
    public ExcelImportResult<DtoImportTest> getExcelData(IExcelVerifyHandler<DtoImportTest> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        ImpModifyTestVerify impModifyTestVerify = (ImpModifyTestVerify) verifyHandler;
        params.setVerifyHandler(impModifyTestVerify);
        ExcelImportResult<DtoImportTest> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoImportTest.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "测试项目导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 获取测试项目导入校验器
     *
     * @param isImportAnaItem    是否同步导入分析因子
     * @param isImportAnaMethod  是否同步导入分析方法
     * @param isImportDimension  是否同步导入量纲
     * @param isImportFormula    是否同步导入 公式
     * @param isImportTestPerson 是否同步导入检测人员
     * @return 校验器
     */
    private ImpModifyTestVerify getVerifyHandler(Boolean isImportAnaItem, Boolean isImportAnaMethod, Boolean isImportDimension,
                                                 Boolean isImportFormula, Boolean isImportTestPerson) {
        Map<String, Boolean> tempMap = new HashMap<>();
        tempMap.put("isImportAnaItem", isImportAnaItem);
        tempMap.put("isImportAnaMethod", isImportAnaMethod);
        tempMap.put("isImportDimension", isImportDimension);
        tempMap.put("isImportFormula", isImportFormula);
        tempMap.put("isImportTestPerson", isImportTestPerson);
        return new ImpModifyTestVerify(tempMap, testService.findAllDeleted(), dimensionRepository.findAll(),
                sampleTypeRepository.findAll(), analyzeItemRepository.findAllDeleted(), methodRepository.findAllDeleted(),
                personRepository.findAll(), person2TestService.findAll());
    }

    /**
     * 关联导入量纲
     *
     * @param importDimension   导入数据
     * @param isImportDimension 是否关联导入
     */
    private void importDimensions(List<String> importDimension, Boolean isImportDimension) {
        if (isImportDimension) {
            importDimension.removeIf(Objects::isNull);
            if (StringUtil.isNotEmpty(importDimension)) {
                //数据库中所有的量纲
                List<String> dbDimension = dimensionRepository.findAll().stream().map(DtoDimension::getDimensionName).collect(Collectors.toList());
                //数据库中不包含的量纲
                List<String> isImportDimensionNames = importDimension.stream().filter(p -> !dbDimension.contains(p.trim())).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(isImportDimensionNames)) {
                    //存放需要导入的分析项目
                    List<DtoDimension> importDimensions = new ArrayList<>();
                    for (String dimensionName : isImportDimensionNames) {
                        DtoDimension dimension = new DtoDimension();
                        Date timeNow = new Date();
                        dimension.setBaseValue(BigDecimal.ZERO);
                        dimension.setDimensionTypeId(UUIDHelper.GUID_EMPTY);
                        dimension.setDimensionName(dimensionName);
                        dimension.setCreator(PrincipalContextUser.getPrincipal().getUserId());
                        dimension.setCreateDate(timeNow);
                        dimension.setModifyDate(timeNow);
                        importDimensions.add(dimension);
                    }
                    dimensionRepository.save(importDimensions);
                }
            }
        }
    }

    /**
     * 关联导入分析方法
     *
     * @param importList        导入数据
     * @param isImportAnaMethod 是否关联导入
     * @param methodList        所有的分析方法数据（本系统分析方法 + 此次导入新增分析方法）
     */
    private void importAnaMethods(List<DtoImportTest> importList, Boolean isImportAnaMethod, List<DtoAnalyzeMethod> methodList) {
        //数据库中所有的分析方法
        List<DtoAnalyzeMethod> dbMethodList = methodRepository.findAll();
        methodList.addAll(dbMethodList);
        if (isImportAnaMethod) {
            //数据库中所有的分析方法
            List<String> dbAnaMethod = dbMethodList.stream()
                    .map(p -> this.splicingMethod(p.getMethodName(), p.getCountryStandard())).collect(Collectors.toList());
            //数据库中不包含的分析方法
            List<DtoImportTest> isImportAnaMethods = new ArrayList<>(importList.stream()
                    .filter(p -> !dbAnaMethod.contains(this.splicingMethod(p.getRedAnalyzeMethodName(), p.getRedCountryStandard())))
                    .distinct().collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DtoImportTest::getRedAnalyzeMethodName)))));
            if (StringUtil.isNotEmpty(isImportAnaMethods)) {
                //存放需要导入的分析方法
                List<DtoAnalyzeMethod> importMethods = new ArrayList<>();
                for (DtoImportTest dto : isImportAnaMethods) {
                    DtoAnalyzeMethod analyzeMethod = new DtoAnalyzeMethod();
                    analyzeMethod.setIsInputBySample(false);
                    analyzeMethod.setMethodName(dto.getRedAnalyzeMethodName());
                    analyzeMethod.setCountryStandard(dto.getRedCountryStandard());
                    analyzeMethod.setCountryStandardName(dto.getRedCountryStandard());
                    importMethods.add(analyzeMethod);
                }
                importMethods = importMethods.stream().distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(importMethods)) {
                    methodRepository.save(importMethods);
                    methodList.addAll(importMethods);
                }
            }
        }
    }

    /**
     * 关联导入分析项目
     *
     * @param importAnaItems  导入的分析项目数据
     * @param isImportAnaItem 是否关联导入
     */
    private void importAnaItems(List<String> importAnaItems, Boolean isImportAnaItem) {
        if (isImportAnaItem) {
            //数据库中所有的分析项目
            List<String> dbAnaItems = analyzeItemRepository.findAll().stream().map(DtoAnalyzeItem::getAnalyzeItemName).collect(Collectors.toList());
            //数据库中不包含的分析项目
            List<String> isImportAnaItems = importAnaItems.stream().filter(p -> !dbAnaItems.contains(p.trim())).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(isImportAnaItems)) {
                //存放需要导入的分析项目
                List<DtoAnalyzeItem> importItems = new ArrayList<>();
                for (String anaItem : isImportAnaItems) {
                    DtoAnalyzeItem analyzeItem = new DtoAnalyzeItem();
                    analyzeItem.setAnalyzeItemName(anaItem);
                    importItems.add(analyzeItem);
                }
                List<DtoAnalyzeItem> save = analyzeItemRepository.save(importItems);
                refreshAnalyzeItemRedis(save);
            }
        }
    }

    /**
     * 刷新分析项目redis缓存
     *
     * @param analyzeItemList 分析项目集合
     */
    private void refreshAnalyzeItemRedis(List<DtoAnalyzeItem> analyzeItemList) {
        if (StringUtil.isNotEmpty(analyzeItemList)) {
            for (DtoAnalyzeItem dtoAnalyzeItem : analyzeItemList) {
                String key = EnumBase.EnumBASRedis.getRedisKey(EnumBase.EnumBASRedis.BAS_OrgId_AnalyzeItem.getValue());
                redisTemplate.opsForHash().put(key, dtoAnalyzeItem.getId(), JsonStream.serialize(dtoAnalyzeItem));
            }
        }
    }

    /**
     * 处理参数公式
     *
     * @param importList      导入数据
     * @param sampleTypeList  检测类型
     * @param isImportFormula 是否导入公式
     */
    private void paramsFormulaHandle(List<DtoImportTest> importList, List<DtoSampleType> sampleTypeList, Boolean isImportFormula) {
        //导入公式
        if (isImportFormula) {
            //导入的所有参数
            List<String> allImportParams = new ArrayList<>();

            //region 参数处理
            paramsHandle(importList, allImportParams);
            //endregion

            //region 公式处理
            //需要添加的其他公式数据
            formulaHandle(importList, sampleTypeList, allImportParams);
            //endregion
        }
    }

    /**
     * 参数数据处理
     *
     * @param importList      导入的参数
     * @param allImportParams 存放所有导入参数的集合
     */
    private void paramsHandle(List<DtoImportTest> importList, List<String> allImportParams) {
        //获取数据库所有参数
        List<DtoParams> dbParams = paramsRepository.findAll();
        //获取导入的所有公司参数
        for (DtoImportTest test : importList) {
            //获取导入的本测试项目的公式参数
            getParams(test, allImportParams);
        }
        //获取数据库所有参数名称
        List<String> dbParamsNames = dbParams.stream().map(DtoParams::getParamName).collect(Collectors.toList());
        //获取需要添加的参数
        List<String> isImportParams = allImportParams.stream().filter(p -> !dbParamsNames.contains(p)).distinct().collect(Collectors.toList());
        List<DtoParams> params = new ArrayList<>();
        for (String paramName : isImportParams) {
            DtoParams param = new DtoParams();
            param.setParamName(paramName);
            param.setVariableName(paramName);
            params.add(param);
        }
        paramsRepository.save(params);
    }

    /**
     * 检测人员数据处理
     *
     * @param importList     导入的数据
     * @param sampleTypeList 样品类型
     */
    private void testToPersonHandle(List<DtoImportTest> importList, List<DtoSampleType> sampleTypeList, Boolean isImportTestPerson) {
        if (isImportTestPerson) {
            List<DtoPerson2Test> importToTest = new ArrayList<>();
            //获取所有人员数据
            List<DtoPerson> dbPersonList = personRepository.findAll();
            // 获取所有测试项目已存在的检测人员
            List<String> testIds = importList.stream().map(DtoImportTest::getId).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
            List<DtoPerson2Test> person2Tests = person2TestService.findByTestIds(testIds);
            Map<String, List<DtoPerson2Test>> person2TestMap = person2Tests.stream().collect(Collectors.groupingBy(DtoPerson2Test::getTestId));
            for (DtoImportTest test : importList) {
                //获取数据库中存在的检测类型
                String typeId = sampleTypeList.stream().filter(p -> test.getSampleTypeId().equals(p.getTypeName())).collect(Collectors.toList()).get(0).getId();
                //获取导入数据中默认检测人员
                String defaultPerson = test.getDefaultPerson();
                //获取导入数据中有检测能力的检测人员
                String abilityPerson = test.getAbilityPerson();

                List<DtoPerson2Test> dtoPerson2Tests = person2TestMap.getOrDefault(test.getId(), new ArrayList<>());

                if (StringUtil.isNotEmpty(defaultPerson)) {
                    //通过检测人员名称获取实体集合
                    String personIdByCName = dbPersonList.stream().filter(p -> defaultPerson.equals(p.getCName())).map(DtoPerson::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
                    //新建关联实体
                    DtoPerson2Test person2Test = new DtoPerson2Test(personIdByCName, test.getId(), typeId, test.getDefaultPerson(), true);
                    importToTest.add(person2Test);
                }
                if (StringUtil.isNotEmpty(abilityPerson)) {
                    List<String> persons = importUtils.personStrToList(abilityPerson);
                    List<DtoPerson> personByCName = dbPersonList.stream().filter(p -> persons.contains(p.getCName())).collect(Collectors.toList());
                    for (DtoPerson person : personByCName) {
                        DtoPerson2Test person2Test1 = new DtoPerson2Test(person.getId(), test.getId(), typeId, person.getCName(), false);
                        importToTest.add(person2Test1);
                    }
                }
                // 处理默认复核人
                Optional<DtoPerson2Test> person2TestOptional = dtoPerson2Tests.stream().filter(DtoPerson2Test::getIsDefaultAuditPerson).findFirst();
                person2TestOptional.ifPresent(importToTest::add);
            }
            //添加人员信息
            if (StringUtil.isNotEmpty(importToTest)) {
                person2TestService.save(importToTest);
            }
        }
    }

    /**
     * 公式数据处理
     *
     * @param importList      导入的数据
     * @param sampleTypeList  检测类型
     * @param allImportParams 所有的需要导入的公式参数
     */
    private void formulaHandle(List<DtoImportTest> importList, List<DtoSampleType> sampleTypeList, List<String> allImportParams) {
        //需要添加的其他公式数据
        List<DtoParamsPartFormula> importPartFormulas = new ArrayList<>();
        //需要添加的公式数据
        List<DtoParamsFormula> importFormulas = new ArrayList<>();
        //需要添加的公式参数数据
        List<DtoParamsTestFormula> importTestFormulas = new ArrayList<>();
        //查询所有参数数据
        List<DtoParams> dbParamsList = paramsRepository.findAll();
        for (DtoImportTest test : importList) {
            //获取检测类型
            String sampleTypeId = test.getSampleTypeId();
            //获取数据库中存在的检测类型
            List<DtoSampleType> dtoSampleTypeStream = sampleTypeList.stream().filter(p -> sampleTypeId.equals(p.getTypeName())).collect(Collectors.toList());
            //获取此导入数据下的所有公式参数
            List<String> paramsNames = getParams(test, allImportParams);
            //获取需要添加的参数
            List<DtoParams> paramsList = dbParamsList.stream().filter(p -> paramsNames.contains(p.getParamName())).collect(Collectors.toList());
            //设置需添加的公式及公式参数
            paramsInTestFormula(test, paramsList, importFormulas, importTestFormulas, importPartFormulas, dtoSampleTypeStream.get(0).getId());
        }
        //添加测试项目公式数据
        if (StringUtil.isNotEmpty(importFormulas)) {
            List<String> objIds = importFormulas.stream().map(DtoParamsFormula::getObjectId).collect(Collectors.toList());
            List<DtoParamsFormula> formulaByObjIds = paramsFormulaRepository.findByObjectIds(objIds);
            //修改已经存在的关联公式删除状态
            if (StringUtil.isNotEmpty(formulaByObjIds)) {
                formulaByObjIds.forEach(p -> p.setIsDeleted(true));
                paramsFormulaRepository.save(formulaByObjIds);
            }
            paramsFormulaRepository.save(importFormulas);
            paramsTestFormulaRepository.save(importTestFormulas);
            if (StringUtil.isNotEmpty(importPartFormulas)) {
                paramsPartFormulaRepository.save(importPartFormulas);
            }
        }
    }

    /**
     * 处理添加数据
     *
     * @param importList      导入的数据
     * @param importAnaItems  导入的分析项目名称
     * @param methodList      导入的分析方法的名称
     * @param importDimension 导入的量纲数据
     * @param sampleTypeList  检测类型
     * @return 需要添加的测试项目实体
     */
    private List<DtoTest> addDataHandle(List<DtoImportTest> importList, List<String> importAnaItems,
                                        List<DtoAnalyzeMethod> methodList, List<String> importDimension,
                                        List<DtoSampleType> sampleTypeList) {
        List<DtoTest> importTests = new ArrayList<>();
        //查询量纲
        List<DtoDimension> dimensionByNames = new ArrayList<>();
        importDimension.removeIf(Objects::isNull);
        if (StringUtil.isNotEmpty(importDimension)) {
            dimensionByNames = dimensionRepository.findByDimensionNameIn(importDimension);
        }
        //查询分析项目
        List<DtoAnalyzeItem> itemsByNames = analyzeItemRepository.findByAnalyzeItemNameIn(importAnaItems);
        // 根据id获取系统中已存在的测试项目
        List<String> testIds = importList.stream().map(DtoImportTest::getId).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? repository.findAll(testIds) : new ArrayList<>();
        Map<String, DtoTest> testMap = testList.stream().collect(Collectors.toMap(DtoTest::getId, p -> p));

        for (DtoImportTest dto : importList) {
            //查询检测类型
            String sampleTypeId = UUIDHelper.GUID_EMPTY;
            DtoSampleType sampleType = sampleTypeList.stream().filter(p -> dto.getSampleTypeId().equals(p.getTypeName())).findFirst().orElse(null);
            if (sampleType != null) {
                sampleTypeId = UUIDHelper.GUID_EMPTY.equals(sampleType.getParentId()) ? sampleType.getId() : sampleType.getParentId();
            }
            //获取分析项目Id
            String anaItemId = itemsByNames.stream().filter(p -> dto.getRedAnalyzeItemName().equals(p.getAnalyzeItemName())).map(DtoAnalyzeItem::getId).collect(Collectors.toList()).get(0);
            //获取分析方法Id
            DtoAnalyzeMethod method = methodList.stream()
                    .filter(p -> this.splicingMethod(dto.getRedAnalyzeMethodName(), dto.getRedCountryStandard())
                            .equals(this.splicingMethod(p.getMethodName(), p.getCountryStandard())))
                    .findFirst().orElseThrow(() -> new BaseException(String.format("分析方法无法匹配: 编号：[%s]", dto.getId())));
            //获取量纲Id
            String dimensionId = UUIDHelper.GUID_EMPTY;
            dimensionByNames.removeIf(Objects::isNull);
            if (StringUtil.isNotEmpty(dimensionByNames) && StringUtil.isNotEmpty(dto.getDimensionId())) {
                dimensionId = dimensionByNames.stream().filter(p -> dto.getDimensionId().equals(p.getDimensionName())).map(DtoDimension::getId).collect(Collectors.toList()).get(0);
            }
            //拼接测试项目名称
            String testName = dto.getRedAnalyzeItemName() + "-" + dto.getRedAnalyzeMethodName() + " " + dto.getRedCountryStandard();
            //添加实体集合
            addEntity(testMap, dto, importTests, testName, anaItemId, dimensionId, sampleTypeId, method);
        }
        return importTests;
    }

    /**
     * 添加实体集合
     *
     * @param dto          需要添加的数据
     * @param importTests  实体集合
     * @param testName     测试项目名称
     * @param anaItemId    分析项目Id
     * @param dimensionId  量纲Id
     * @param sampleTypeId 检测类型Id
     * @param method       分析方法
     */
    private void addEntity(Map<String, DtoTest> testMap, DtoImportTest dto, List<DtoTest> importTests, String testName, String anaItemId, String dimensionId, String sampleTypeId, DtoAnalyzeMethod method) {
        DtoTest test = new DtoTest();
        if (testMap.containsKey(dto.getId())) {
            test = testMap.get(dto.getId());
        }
        test.importToEntity(dto);
        test.setTestName(testName);
        test.setAnalyzeItemId(anaItemId);
        test.setFullPinYin(PinYinUtil.getFullSpell(dto.getRedAnalyzeItemName()));
        test.setPinYin(PinYinUtil.getFirstSpell(dto.getRedAnalyzeItemName()));
        test.setAnalyzeMethodId(method.getId());
        test.setRedCountryStandard(method.getCountryStandard());
        test.setDimensionId(dimensionId);
        test.setIndustryTypeName("环境监测");
        test.setSampleTypeId(sampleTypeId);
        importTests.add(test);
    }

    /**
     * 设置需添加的公式及公式参数
     *
     * @param test               单次的测试项目数据
     * @param paramsList         需要添加的参数名称
     * @param importFormulas     需要添加的公式数据
     * @param importTestFormulas 需要添加的公式参数数据
     * @param importPartFormulas 需要添加的其他公式数据
     * @param sampleTypeId       检测类型Id
     */
    private void paramsInTestFormula(DtoImportTest test, List<DtoParams> paramsList, List<DtoParamsFormula> importFormulas, List<DtoParamsTestFormula> importTestFormulas, List<DtoParamsPartFormula> importPartFormulas, String sampleTypeId) {
        if (StringUtil.isNotEmpty(test.getTestFormula())) {
            //设置公式数据
            DtoParamsFormula formula = new DtoParamsFormula();
            formula.setFormula(test.getTestFormula());
            formula.setObjectId(test.getId());
            formula.setObjectType(0);
            Date dateNow = new Date();
            formula.setConfigDate(dateNow);
            formula.setSampleTypeId(sampleTypeId);
            importFormulas.add(formula);
            //设置关联的TestFormula
            for (DtoParams importParams : paramsList) {
                DtoParamsTestFormula testFormula = new DtoParamsTestFormula();
                testFormula.setParamsId(importParams.getId());
                testFormula.setObjId(formula.getId());
                testFormula.setParamsName(importParams.getParamName());
                testFormula.setAlias(importParams.getParamName());
                testFormula.setDimensionId(importParams.getDimensionId());
                testFormula.setDimension(importParams.getDimension());
                importTestFormulas.add(testFormula);
            }
            //region 测得量计算公式
            if (StringUtil.isNotEmpty(test.getMeasureFormula())) {
                DtoParamsPartFormula partFormula = new DtoParamsPartFormula();
                partFormula.setFormula(test.getMeasureFormula());
                partFormula.setFormulaId(formula.getId());
                partFormula.setFormulaType(EnumLIM.EnumPartFormulaType.加标公式.getValue());
                importPartFormulas.add(partFormula);
            }
            //endregion
        }

    }

    /**
     * 获取测试项目的公式参数
     *
     * @param test            测试项目实体
     * @param allImportParams 所有的参数集合
     * @return 本测试项目公式参数
     */
    private List<String> getParams(DtoImportTest test, List<String> allImportParams) {
        List<String> paramsNames = new ArrayList<>();
        if (StringUtil.isNotEmpty(test.getTestFormula())) {
            String[] strings = test.getTestFormula().split("\\+|-|\\*|/|%|×|<|>|<=|>=|=|==|!=");
            List<String> allParams = new ArrayList<>();
            Collections.addAll(allParams, strings);
            allParams.removeIf(p -> p.equals("") || (!p.contains("[") && !p.contains("]")));
            for (String string : allParams) {
                String subParams = string.substring(string.indexOf("[") + 1, string.indexOf("]"));
                paramsNames.add(subParams);
                allImportParams.add(subParams);
                paramsNames = paramsNames.stream().distinct().collect(Collectors.toList());
            }
        }
        return paramsNames;
    }

    /**
     * 校验原始记录单是否已全部配置好，名称匹配
     *
     * @param importList excel文件导入数据
     * @return 校验结果
     */
    private Boolean checkReportConfig(List<DtoImportTest> importList) {
        Boolean checkResult = false;
        List<String> reportConfigNameList = importList.stream().filter(data -> StringUtil.isNotEmpty(data.getRelatedReport())).map(DtoImportTest::getRelatedReport).collect(Collectors.toList());
        if (!reportConfigNameList.isEmpty()) {
            List<String> singleReportNameList = new ArrayList<>();
            reportConfigNameList.forEach(reportArr -> {
                //ps：一个测试项目应用于多张原始记录单的时候，用“顿号”隔开
                singleReportNameList.addAll(Arrays.asList(reportArr.split("；")));
            });
            List<DtoRecordConfig> dtoReportConfigList = recordConfigRepository.findAllByRecordType(EnumLIM.EnumRecordType.原始记录单.getValue());
            List<String> validReportNameList = dtoReportConfigList.stream().map(DtoRecordConfig::getRecordName).collect(Collectors.toList());
            if (!validReportNameList.containsAll(singleReportNameList)) {
                checkResult = true;
            }
        }
        return checkResult;
    }

    /**
     * 构建原始记录单与测试项目关联关系列表
     *
     * @param importList          excel文件导入数据
     * @param dtoRecordConfigList 所有原始记录单数据
     */
    private void addRelation(List<DtoImportTest> importList, List<DtoRecordConfig> dtoRecordConfigList) {
        List<DtoRecordConfig2Test> resultList = new ArrayList<>();
        List<DtoRecordConfig2Test> allRelationList = recordConfig2TestService.findAll();
        // 多对多关系，双循环构建关系列表
        for (DtoImportTest dtoImportTest : importList) {
            String recordNameArr = dtoImportTest.getRelatedReport();
            // 关联为非必填 一个测试项目应用于多张原始记录单的时候，用“顿号”隔开
            if (StringUtil.isNotEmpty(recordNameArr)) {
                List<String> recordNameList = Arrays.asList(recordNameArr.split("；"));
                for (String recordName : recordNameList) {
                    //名称匹配获取原始记录单id
                    DtoRecordConfig dtoRecordConfig = dtoRecordConfigList.stream().filter(recordConfig -> recordName.equals(recordConfig.getRecordName())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(dtoRecordConfig)) {
                        DtoRecordConfig2Test existRelation = allRelationList.stream().filter(relation -> dtoImportTest.getId().equals(relation.getTestId()) && dtoRecordConfig.getId().equals(relation.getRecordConfigId()))
                                .findFirst().orElse(null);
                        //关联关系不存在时才会加入 待新增列表
                        if (StringUtil.isNull(existRelation)) {
                            DtoRecordConfig2Test relation = new DtoRecordConfig2Test();
                            relation.setTestId(dtoImportTest.getId());
                            relation.setRecordConfigId(dtoRecordConfig.getId());
                            resultList.add(relation);
                        }
                    }
                }
            }
        }
        if (resultList.size() > 0) {
            recordConfig2TestService.save(resultList);
            //新增测试项目之后，所有的原始记录单参数都要改成未配置
            List<String> recordConfigIds = resultList.stream().map(DtoRecordConfig2Test::getRecordConfigId).distinct().collect(Collectors.toList());
            List<DtoParams2ParamsFormula> params2ParamsFormulas = params2ParamsFormulaRepository.findByRecordIdIn(recordConfigIds);
            List<String> paramsConfigIds = params2ParamsFormulas.stream().map(DtoParams2ParamsFormula::getParamsConfigId).distinct().collect(Collectors.toList());
            if (paramsConfigIds.size() > 0) {
                paramsConfigRepository.updateParamsConfigIsAllConfig(paramsConfigIds, false);
            }
        }
    }


    /**
     * 拼接分析方法
     *
     * @param methodName      分析方法名称
     * @param countryStandard 标准编号名称
     * @return 分析方法全名
     */
    private String splicingMethod(String methodName, String countryStandard) {
        return (StringUtil.isNotEmpty(methodName) ? methodName.trim() : "") + "&&" + (StringUtil.isNotEmpty(countryStandard) ? countryStandard.trim() : "");
    }

    @Autowired
    public void setParamsFormulaRepository(ParamsFormulaRepository paramsFormulaRepository) {
        this.paramsFormulaRepository = paramsFormulaRepository;
    }

    @Autowired
    public void setParamsTestFormulaRepository(ParamsTestFormulaRepository paramsTestFormulaRepository) {
        this.paramsTestFormulaRepository = paramsTestFormulaRepository;
    }

    @Autowired
    public void setParamsPartFormulaRepository(ParamsPartFormulaRepository paramsPartFormulaRepository) {
        this.paramsPartFormulaRepository = paramsPartFormulaRepository;
    }

    @Autowired
    public void setParamsRepository(ParamsRepository paramsRepository) {
        this.paramsRepository = paramsRepository;
    }

    @Autowired
    public void setAnalyzeItemRepository(AnalyzeItemRepository analyzeItemRepository) {
        this.analyzeItemRepository = analyzeItemRepository;
    }

    @Autowired
    public void setMethodRepository(AnalyzeMethodRepository methodRepository) {
        this.methodRepository = methodRepository;
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    public void setPerson2TestService(Person2TestService person2TestService) {
        this.person2TestService = person2TestService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setParams2ParamsFormulaRepository(Params2ParamsFormulaRepository params2ParamsFormulaRepository) {
        this.params2ParamsFormulaRepository = params2ParamsFormulaRepository;
    }

    @Autowired
    public void setParamsConfigRepository(ParamsConfigRepository paramsConfigRepository) {
        this.paramsConfigRepository = paramsConfigRepository;
    }

    @Autowired
    public void setRecordConfig2TestService(RecordConfig2TestService recordConfig2TestService) {
        this.recordConfig2TestService = recordConfig2TestService;
    }

    @Autowired
    public void setRecordConfigRepository(RecordConfigRepository recordConfigRepository) {
        this.recordConfigRepository = recordConfigRepository;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
