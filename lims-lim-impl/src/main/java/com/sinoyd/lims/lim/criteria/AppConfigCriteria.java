package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * app应用配置查询条件
 * 
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023-1-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppConfigCriteria extends BaseCriteria {


    
    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
             
        return condition.toString();
    }
}