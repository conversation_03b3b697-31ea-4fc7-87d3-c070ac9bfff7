package com.sinoyd.lims.lim.service.impl;

import java.io.Serializable;
import java.util.Collection;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSort;
import com.sinoyd.lims.lim.repository.lims.AnalyzeItemSortDetailRepository;
import com.sinoyd.lims.lim.repository.lims.AnalyzeItemSortRepository;
import com.sinoyd.lims.lim.service.AnalyzeItemSortService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 分析项目排序接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-14
 * @since V100R001
 */
@Service
public class AnalyzeItemSortServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoAnalyzeItemSort,String, AnalyzeItemSortRepository> implements AnalyzeItemSortService {

    @Autowired
    private AnalyzeItemSortDetailRepository analyzeItemSortDetailRepository;

    /**
     * 新增分析项目排序
     */
    @Override
    @Transactional
    public DtoAnalyzeItemSort save(DtoAnalyzeItemSort entity) {
        Integer count = repository.getByIdAndName(entity.getId(), entity.getSortName());
        if(count > 0) {
            throw new BaseException("已存在相同名称的分析项目排序");
        }
        return super.save(entity);
    }

    /**
     * 更新分析项目排序
     */
    @Transactional
    @Override
    public DtoAnalyzeItemSort update(DtoAnalyzeItemSort entity) {
        Integer count = repository.getByIdAndName(entity.getId(), entity.getSortName());
        if(count > 0){
            throw new BaseException("已存在相同名称的分析项目排序");
        }
        return super.update(entity);
    }
    
    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoAnalyzeItemSort> page, BaseCriteria criteria) {
            // 设置查询的实体类名及别名
            page.setEntityName("DtoAnalyzeItemSort x");
            // 设置查询返回的字段、实体别名表示所有字段
            page.setSelect("select x");

            super.findByPage(page, criteria);
    }

    /**
     * 删除
     */
    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {

        //级联删除分析项目排序详情
        analyzeItemSortDetailRepository.deleteBySortId((String)id);

        return super.logicDeleteById(id);
    }

    /**
     * 批量删除
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {

        int count = 0;

        for (Object id : ids)
        {
            count += this.logicDeleteById((String)id);
        }
        return count;
    }
}