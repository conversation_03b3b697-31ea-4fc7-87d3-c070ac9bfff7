<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
    <groupId>com.sinoyd.lims.monitor</groupId>
	<artifactId>lims-monitor</artifactId>
	<version>fix-5.4.13-SNAPSHOT</version>
	<name>lims-monitor</name>
    <description>LIMS Monitor模块</description>
    <packaging>pom</packaging>

    <build>
        <finalName>lims-monitor</finalName>
    </build>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.15.RELEASE</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>

        <monitor.version>fix-5.4.13</monitor.version>
        <lim.version>fix-5.4.13</lim.version>
    </properties>

    <modules>
        <module>lims-monitor-public</module>
        <module>lims-monitor-arch</module>
        <module>lims-monitor-impl</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sinoyd.lims.monitor</groupId>
                <artifactId>lims-monitor-public</artifactId>
                <version>${monitor.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.monitor</groupId>
                <artifactId>lims-monitor-arch</artifactId>
                <version>${monitor.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.monitor</groupId>
                <artifactId>lims-monitor-impl</artifactId>
                <version>${monitor.version}-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims.lim</groupId>
                <artifactId>lims-lim-impl</artifactId>
                <version>${lim.version}-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id><!--这里需要和settings.xml中配置的私有库名称一致-->
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>
</project>
