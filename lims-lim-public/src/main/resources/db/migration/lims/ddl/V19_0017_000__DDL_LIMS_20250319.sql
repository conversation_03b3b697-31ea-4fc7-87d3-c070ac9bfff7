alter table tb_pro_report add column monitorReportConfigId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' comment '监测报表标识';

-- 报告与监测计划关联表，用来生成监测上报报表
CREATE TABLE TB_PRO_MonitorReport2Property
(
    id       varchar(50) NOT NULL COMMENT 'id',
    reportId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '报告标识',
    propertyId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '监测计划标识',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报告与监测计划关联表';