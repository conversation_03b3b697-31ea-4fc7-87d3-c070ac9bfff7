package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoSamplingPersonConfig;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * SamplingPersonConfig数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface SamplingPersonConfigRepository extends IBaseJpaPhysicalDeleteRepository<DtoSamplingPersonConfig, String> {

    /**
     * 查询对应关联下的采样人
     *
     * @param objectType 关联类型
     * @param objectId 关联id
     * @return 对应的采样人
     */
    List<DtoSamplingPersonConfig> findByObjectTypeAndObjectId(Integer objectType, String objectId);

    /**
     * 根据对象id获取相的采样人信息
     * @param objectIds 对象ids
     * @return 相关的采样人信息
     */
    List<DtoSamplingPersonConfig> findByObjectIdIn(List<String> objectIds);
    /**
     * 删除对应关联下的采样车辆
     *
     * @param objectId 关联id
     */
    @Transactional
    void deleteByObjectId(String objectId);
}