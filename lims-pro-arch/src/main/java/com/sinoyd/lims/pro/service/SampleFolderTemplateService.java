package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.DtoSampleFolderTemplate;
import com.sinoyd.lims.pro.dto.customer.DtoProjectScheme;
import com.sinoyd.lims.pro.dto.customer.DtoProjectTest;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;

import java.util.List;

/**
 *  SampleFolderTemplateService
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/08/02
 */
public interface SampleFolderTemplateService extends IBaseJpaService<DtoSampleFolderTemplate, String> {

    /**
     * 新增方案中点位
     *
     * @param dtoSampleFolder 实体
     */
    void addFolder(DtoSampleFolder dtoSampleFolder);

    /**
     * 选择点位
     *
     * @param folderIds 点位id
     */
    void selectSampleFolder(List<String> folderIds, String approveId, Boolean isMarkDelete);

    /**
     * 新增点位周期频次
     *
     * @param sampleFolderTempId 点位模板id
     * @param periodCount        周期
     * @param timePerPeriod      次数
     * @param sampleOrder      样品数
     */
    void addSchemePeriodTimes(String sampleFolderTempId, Integer periodCount, Integer timePerPeriod, Integer sampleOrder);

    /**
     * 复制点位模板
     *
     * @param ids       点位id
     * @param copyTimes 复制次数
     */
    void copySampleFolderTemplate(List<String> ids, Integer copyTimes);

    /**
     * 标记删除点位
     *
     * @param folderTempIds 点位模板id
     */
    void markDeleteFolderTemp(List<String> folderTempIds, String approveId);

    /**
     * 添加次数
     *
     * @param sampleFolderTempId 点位模板id
     * @param periodCount        周期
     * @param timePerPeriod      次数
     */
    void addTimes(String sampleFolderTempId, Integer periodCount, Integer timePerPeriod);

    /**
     * 复制周期
     *
     * @param sampleFolderTempId 点位id
     * @param periodCount        周期
     * @param copyTimes          复制次数
     * @return 周期
     */
    void copyPeriod(String sampleFolderTempId, Integer periodCount, Integer copyTimes);

    /**
     * 标记删除周期频次
     *
     * @param frequencyTempId 周期频次id
     */
    void markDeleteFrequencyTemp(String frequencyTempId);

    /**
     * 批量删除周期频次
     *
     * @param frequencyTempIds
     */
    void batchMarkDeleteFrequencyTemp(List<String> frequencyTempIds);

    /**
     * 取消点位
     *
     * @param folderTempIds 点位模板id
     */
    void cancelFolderTemplate(List<String> folderTempIds);

    /**
     * 查询项目对应检测类型下的方案
     *
     * @param approveId 项目id
     * @return 方案
     */
    DtoProjectScheme findScheme(String approveId);

    /**
     * 获取点位列表
     *
     * @param approveId 方案id
     * @return List<DtoSampleFolder>
     */
    List<DtoSampleFolder> getFolderList(String approveId, String sampleTypeId, String watchSpot);

    /**
     * 批量设置分包
     *
     * @param samplingFrequencyIds 频次id集合
     * @param projectTests         项目指标集合
     */
    void sub(List<String> samplingFrequencyIds, List<DtoProjectTest> projectTests);

    /**
     * 批量添加测试项目
     *
     * @param samplingFrequencyIds 频次id集合
     * @param analyseItemIds       分析项目id集合
     * @return 添加的频次指标
     */
    void addFrequencyAnalyseItems(List<String> samplingFrequencyIds, List<String> analyseItemIds, List<String> testIds);

    /**
     * 批量删除测试项目
     *
     * @param frequencyIds   频次id集合
     * @param analyseItemIds 分析项目id集合
     */
    void deleteFrequencyAnalyseItems(List<String> frequencyIds, List<String> analyseItemIds);

    /**
     * 修改频次指标
     *
     * @param samplingFrequencyId 频次id
     * @param analyseItemIds      分析项目id集合
     */
    void modifySamplingFrequencyTest(String samplingFrequencyId, List<String> analyseItemIds, List<String> testIds);

    /**
     * 删除周期
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     */
    void deletePeriod(String sampleFolderId, Integer periodCount);

    /**
     * 复制次数
     *
     * @param sampleFolderId      点位id
     * @param samplingFrequencyId 频次id
     * @param copyTimes           复制次数
     * @return 次数
     */
    void copyTimes(String sampleFolderId, String samplingFrequencyId, Integer copyTimes);

    /**
     * 取消删除点位
     *
     * @param folderIds 点位id
     */
    void cancelDeleteFolders(List<String> folderIds);

    /**
     * 取消删除周期频次
     *
     * @param frequencyIds 周期频次id
     */
    void cancelDeleteFrequency(List<String> frequencyIds);

    /**
     * 恢复点位
     *
     * @param folderIds 点位id
     */
    void restoreFolders(List<String> folderIds);

    /**
     * 恢复周期频次
     *
     * @param frequencyIds 周期频次id
     */
    void restoreFrequency(List<String> frequencyIds);

}
