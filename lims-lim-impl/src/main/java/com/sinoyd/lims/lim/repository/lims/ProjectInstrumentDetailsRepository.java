package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrumentDetails;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * ProjectInstrumentDetails数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
public interface ProjectInstrumentDetailsRepository extends IBaseJpaPhysicalDeleteRepository<DtoProjectInstrumentDetails, String> {

//    /**
//     * 删除仪器入库记录明细
//     *
//     * @param projectInstrumentId 仪器入库记录主键
//     * @return 返回删除数量
//     */
//    @Modifying
//    @Query("delete from DtoProjectInstrumentDetails a where a.projectInstrumentId = :projectInstrumentId")
//    Integer deleteByProjectInstrumentId(String projectInstrumentId);


    /**
     * 删除仪器入库记录明细
     *
     * @param projectInstrumentIds 仪器入库记录主键列表
     * @return 返回删除数量
     */
    @Modifying
    Integer deleteByProjectInstrumentIdIn(@Param("projectInstrumentIds") List<String> projectInstrumentIds);

    /**
     * 根据仪器出入库记录主键查找
     *
     * @param projectInstrumentId 仪器出入库记录主键
     * @return 仪器出入库记录明细
     */
    List<DtoProjectInstrumentDetails> findByProjectInstrumentId(String projectInstrumentId);

    /**
     * 根据仪器出入库记录主键查找
     *
     * @param projectInstrumentIds 仪器出入库记录主键Ids
     * @return 仪器出入库记录明细
     */
    List<DtoProjectInstrumentDetails> findByProjectInstrumentIdIn(List<String> projectInstrumentIds);

    /**
     * 根据仪器id，获取已入库的仪器出入库记录明细
     *
     * @param instrumentId 仪器id
     * @return 已入库的仪器出入库记录明细
     */
    List<DtoProjectInstrumentDetails> findByInstrumentIdAndIsStorageTrue(String instrumentId);

    /**
     * 根据仪器id，获取未入库的仪器出入库记录明细
     *
     * @param instrumentId 仪器id
     * @return 未入库的仪器出入库记录明细
     */
    List<DtoProjectInstrumentDetails> findByInstrumentIdAndIsStorageFalse(String instrumentId);

    /**
     * 根据仪器id列表，获取未入库的仪器出入库记录明细
     *
     * @param instrumentIdList 仪器id列表
     * @return 未入库的仪器出入库记录明细
     */
    List<DtoProjectInstrumentDetails> findByInstrumentIdInAndIsStorageFalse(List<String> instrumentIdList);

    /**
     * 根据仪器id，项目仪器表id获取未入库的仪器出入库记录明细
     *
     * @param instrumentId        仪器id
     * @param projectInstrumentId 项目仪器表id
     * @return 未入库的仪器出入库记录明细
     */
    List<DtoProjectInstrumentDetails> findByInstrumentIdAndProjectInstrumentIdAndIsStorageFalse(String instrumentId, String projectInstrumentId);

    /**
     * 根据仪器出入库记录主键查找已出库的仪器条数
     *
     * @param projectInstrumentIds 仪器出入库记录主键Ids
     * @return 仪器出入库记录明细条数
     */
    Integer countByProjectInstrumentIdInAndIsStorageTrue(List<String> projectInstrumentIds);

    /**
     * 根据仪器出入库详情主键查找已入库的数量
     * @param ids 主键
     * @return 已入库的数量
     */
    Integer countByIdInAndIsStorageTrue(List<String> ids);
}