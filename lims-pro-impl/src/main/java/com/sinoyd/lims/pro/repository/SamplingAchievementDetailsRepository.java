package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoSamplingAchievementDetails;

import java.util.Date;
import java.util.List;

/**
 *  采样绩效明细repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/03/12
 */
public interface SamplingAchievementDetailsRepository extends IBaseJpaPhysicalDeleteRepository<DtoSamplingAchievementDetails, String> {

    /**
     * 根据条件删除数据
     * @param achievementIds 绩效id集合
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 删除条数
     */
    Integer deleteByAchievementIdInAndSamplingTimeBetween(List<String> achievementIds, Date startTime, Date endTime);

    /**
     * 根据绩效id删除明细
     * @param achievementIds 删除条数s 绩效id集合
     * @return 删除条数
     */
    Integer deleteByAchievementIdIn(List<String> achievementIds);

}
