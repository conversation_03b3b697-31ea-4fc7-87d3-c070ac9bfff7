package com.sinoyd.base.service.impl.divationMethod;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.utils.base.DivationUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 小于检出限
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
public class DetectionLimitServiceImpl implements QualityDivationService {

    @Override
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        String retStr = "";
        if (valueList.size() > 1) {
            String samValue = valueList.get(0);
            String limitValue = valueList.get(1);
            if (DivationUtils.isNumber(samValue) && DivationUtils.isNumber(limitValue)) {
                //比较数据大于检出限
                if (new BigDecimal(samValue).compareTo(new BigDecimal(limitValue)) > 0) {
                    retStr = "不合格";
                } else {
                    retStr = "合格";
                }
            }
        }
        map.put("qcRate", retStr);
    }
}
