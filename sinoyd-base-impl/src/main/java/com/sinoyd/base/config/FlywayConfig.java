package com.sinoyd.base.config;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Flyway配置类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/17
 */
@Configuration
@EnableTransactionManagement
public class FlywayConfig {

    /**
     * 框架相关sql文件位置
     */
    @Value("${flyway.path.frame:NONE}")
    private String FRAME_SQL_LOCATION;

    /**
     * lims ddl sql文件位置
     */
    @Value("${flyway.path.lims.ddl:NONE}")
    private String LIMS_DDL_SQL_LOCATION;

    /**
     * lims 初始化数据 sql文件位置
     */
    @Value("${flyway.path.lims.data:NONE}")
    private String LIMS_DATA_SQL_LOCATION;

    /**
     * rcc ddl sql文件位置
     */
    @Value("${flyway.path.rcc.ddl:NONE}")
    private String RCC_DDL_SQL_LOCATION;

    /**
     * rcc 初始化数据 sql文件位置
     */
    @Value("${flyway.path.rcc.data:NONE}")
    private String RCC_DATA_SQL_LOCATION;

    /**
     * 工作流 初始化数据 sql文件位置
     */
    @Value("${flyway.path.act.data:NONE}")
    private String ACT_DATA_SQL_LOCATION;

    /**
     * 项目上个性化sql文件位置
     */
    @Value("${flyway.path.project:NONE}")
    private String PROJECT_SQL_LOCATION;

    /**
     * flyway使用的编码
     */
    private static final String ENCODING = "UTF-8";

    @Autowired
    private Map<String, DataSource> dataSourceMap;

    @PostConstruct
    public void migrate() {
        for (Map.Entry<String, DataSource> map : dataSourceMap.entrySet()) {
            String dsName = map.getKey();
            if ("primaryDataSource".equals(dsName)) {
                Flyway flyway = Flyway.configure()
                        .dataSource(map.getValue())
                        .encoding(ENCODING)
                        .baselineOnMigrate(true)
                        .locations(loadSqlPath())
                        .validateOnMigrate(false)
                        .load();
                flyway.migrate();
            }
            if ("frameDataSource".equals(dsName) && !"NONE".equalsIgnoreCase(FRAME_SQL_LOCATION)) {
                Flyway flyway = Flyway.configure()
                        .dataSource(map.getValue())
                        .encoding(ENCODING)
                        .baselineOnMigrate(true)
                        .locations(FRAME_SQL_LOCATION)
                        .validateOnMigrate(false)
                        .load();
                flyway.migrate();
            }
        }
    }

    /**
     * 获取flyway脚本路径
     *
     * @return flyway脚本路径
     */
    private String[] loadSqlPath() {
        Set<String> sqlPathList = new HashSet<>();
        if (!"NONE".equalsIgnoreCase(LIMS_DDL_SQL_LOCATION)) {
            sqlPathList.add(LIMS_DDL_SQL_LOCATION);
        }
        if (!"NONE".equalsIgnoreCase(LIMS_DATA_SQL_LOCATION)) {
            sqlPathList.add(LIMS_DATA_SQL_LOCATION);
        }
        if (!"NONE".equalsIgnoreCase(RCC_DDL_SQL_LOCATION)) {
            sqlPathList.add(RCC_DDL_SQL_LOCATION);
        }
        if (!"NONE".equalsIgnoreCase(RCC_DATA_SQL_LOCATION)) {
            sqlPathList.add(RCC_DATA_SQL_LOCATION);
        }
        if (!"NONE".equalsIgnoreCase(PROJECT_SQL_LOCATION)) {
            sqlPathList.add(PROJECT_SQL_LOCATION);
        }
        if (!"NONE".equalsIgnoreCase(ACT_DATA_SQL_LOCATION)) {
            sqlPathList.add(ACT_DATA_SQL_LOCATION);
        }
        if (StringUtil.isEmpty(sqlPathList)) {
            throw new BaseException("flyway 脚本路径配置不正确...");
        }
        return sqlPathList.toArray(new String[0]);
    }

}