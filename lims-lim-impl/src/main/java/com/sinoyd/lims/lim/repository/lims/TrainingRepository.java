package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTraining;

import java.util.List;

/**
 * 培训仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/6
 * @since V100R001
 */
public interface TrainingRepository extends IBaseJpaRepository<DtoTraining, String> {

    /**
     * 根据id集合查询
     *
     * @param trainingIds id集合
     * @return 培训集合
     */
    List<DtoTraining> findByidIn(List<String> trainingIds);
}
