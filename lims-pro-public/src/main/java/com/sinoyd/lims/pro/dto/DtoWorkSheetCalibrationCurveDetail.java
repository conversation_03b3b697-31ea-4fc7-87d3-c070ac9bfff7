package com.sinoyd.lims.pro.dto;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.entity.WorkSheetCalibrationCurveDetail;
import javax.persistence.*;

import com.sinoyd.lims.pro.util.MathUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;


/**
 * DtoWorkSheetCalibrationCurveDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_WorkSheetCalibrationCurveDetail")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoWorkSheetCalibrationCurveDetail extends WorkSheetCalibrationCurveDetail {
    private static final long serialVersionUID = 1L;

    /**
     * 小检测单id
     */
    @Transient
    private String workSheetId;

    @Transient
    private String redAnalyzeItemName;

    public BigDecimal getLessBlankAbsorbanceDoubleValue() {
        String absorbance = getAbsorbance();
        String absorbanceB = getAbsorbanceB();
        BigDecimal av = new BigDecimal("0.0");
        BigDecimal am = new BigDecimal("0.0");
        if (StringUtil.isNotEmpty(absorbance) && MathUtil.isNumeral(absorbance)) {
            av = new BigDecimal(absorbance);
        }
        if (StringUtil.isNotEmpty(absorbanceB) && MathUtil.isNumeral(absorbanceB)) {
            am = new BigDecimal(absorbanceB);
        }
        return av.subtract(am.multiply(new BigDecimal("2")));
    }
}