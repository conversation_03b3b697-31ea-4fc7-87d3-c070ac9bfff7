package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoAnalyseAchievementDetails;
import com.sinoyd.lims.pro.repository.AnalyseAchievementDetailsRepository;
import com.sinoyd.lims.pro.service.AnalyseAchievementDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * AnalyseAchievementDetails操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Service
public class AnalyseAchievementDetailsServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoAnalyseAchievementDetails, String, AnalyseAchievementDetailsRepository> implements AnalyseAchievementDetailsService {

    private SampleTypeService sampleTypeService;

    @Override
    public void findByPage(PageBean<DtoAnalyseAchievementDetails> page, BaseCriteria criteria) {
        page.setEntityName("DtoAnalyseAchievementDetails a");
        page.setSelect("select a");
        page.setSort("a.analyzeTime-, a.analystId-, a.sampleCode-, a.analyzeMethodName-, a.analyzeItemName-");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData());
    }

    @Override
    public void export(HttpServletResponse response, BaseCriteria criteria) {
        PageBean<DtoAnalyseAchievementDetails> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.setSort("a.analyzeTime-, a.analystId-, a.sampleCode-, a.analyzeMethodName-, a.analyzeItemName-");
        findByPage(pb, criteria);
        PoiExcelUtils.exportExcel(pb.getData(), null, "分析绩效明细表格", DtoAnalyseAchievementDetails.class, "分析绩效明细表格" + "_" + DateUtil.nowTime("yyyyMMddHHmmss"), response);
    }

    private void fillingTransientFields(List<DtoAnalyseAchievementDetails> dataList) {
        List<String> sampleTypeIds = dataList.stream().map(DtoAnalyseAchievementDetails::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findAll(sampleTypeIds) : new ArrayList<>();
        dataList.forEach(d -> {
            Optional<DtoSampleType> sampleType = sampleTypes.stream().filter(s -> s.getId().equals(d.getSampleTypeId())).findFirst();
            sampleType.ifPresent(s -> d.setSampleTypeName(s.getTypeName()));
        });
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }
}
