package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.vo.HtmlMonitorDataVO;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.service.PDPMonitorDataService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 排污许可证自行监测数据 Controller
 *
 * <AUTHOR>
 * @version V5.2.0 2025/04/23
 * @since V100R001
 */
@RestController
@RequestMapping("api/monitor/pdpMonitorData")
public class PDPMonitorDataController extends ExceptionHandlerController<PDPMonitorDataService> {

    /**
     * 查询排污许可证自行监测数据
     *
     * @param enterpriseId 企业id
     * @return 自行监测数据
     */
    @GetMapping("/{enterpriseId}")
    public RestResponse<List<HtmlMonitorDataVO>> findMonitorData(@PathVariable("enterpriseId") String enterpriseId) {
        RestResponse<List<HtmlMonitorDataVO>> response = new RestResponse<>();
        response.setData(service.findPointMonitorData(enterpriseId));
        return response;
    }


    /**
     * 同步自行监测数据
     *
     * @param dataList     自行监测数据
     * @param enterpriseId 企业id
     * @return 同步结果
     */
    @PostMapping("/sync/{enterpriseId}")
    public RestResponse<Void> sync(@RequestBody Collection<HtmlMonitorDataVO> dataList, @PathVariable("enterpriseId") String enterpriseId) {
        service.sync(dataList, enterpriseId);
        return new RestResponse<>();
    }


    /**
     * 批量获取自行监测数据
     *
     * @param enterpriseIds 企业id集合
     * @return 无返回值
     */
    @PostMapping("/sync/batch")
    public RestResponse<Void> batchSync(@RequestBody Collection<String> enterpriseIds) {
        service.syncPointMonitorData(enterpriseIds);
        return new RestResponse<>();
    }
}
