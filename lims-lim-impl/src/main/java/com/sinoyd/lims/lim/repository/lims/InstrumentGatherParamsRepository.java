package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGather;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGatherParams;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 仪器接入参数操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
public interface InstrumentGatherParamsRepository extends IBaseJpaRepository<DtoInstrumentGatherParams, String> {


    /**
     * 根据仪器接入ids查询参数集合
     *
     * @param instrumentGatherIds 仪器接入ids
     * @return 参数集合
     */
    List<DtoInstrumentGatherParams> findByInstrumentGatherIdIn(Collection<String> instrumentGatherIds);

    /**
     * 根据仪器接入id查询参数集合
     *
     * @param instrumentGatherId 仪器接入id
     * @return 参数集合
     */
    List<DtoInstrumentGatherParams> findByInstrumentGatherId(String instrumentGatherId);

    /**
     * 新增是判断是否存在相同标识的参数
     *
     * @param paramName          参数名称
     * @param instrumentGatherId 仪器接入id
     * @return 数量
     */
    @Query("select count(x.id) from DtoInstrumentGatherParams x where x.isDeleted = 0 and x.paramName = :paramName and x.dataType = :dataType and x.instrumentGatherId = :instrumentGatherId ")
    Integer countByParamName(@Param("paramName") String paramName,
                             @Param("dataType") String dataType,
                             @Param("instrumentGatherId") String instrumentGatherId);

    /**
     * 更新是判断是否存在相同标识的参数
     *
     * @param id                 id
     * @param paramName          参数名称
     * @param instrumentGatherId 仪器接入id
     * @return 数量
     */
    @Query("select count(x.id) from DtoInstrumentGatherParams x where x.isDeleted = 0 and x.id <> :id and x.paramName = :paramName and x.dataType = :dataType and x.instrumentGatherId = :instrumentGatherId ")
    Integer countByParamNameAndId(@Param("id") String id,
                                  @Param("paramName") String paramName,
                                  @Param("dataType") String dataType,
                                  @Param("instrumentGatherId") String instrumentGatherId);
}