package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 错误信息
 * <AUTHOR>
 * @version V1.0.0 2020/5/28
 * @since V100R001
 */
@Data
public class DtoErrorInfo {

    /**
     * 构造
     *
     * @param batchFlag 批量标记
     */
    public DtoErrorInfo(Boolean batchFlag) {
        this.batchFlag = batchFlag;
    }

    /**
     * 批量标记
     */
    private Boolean batchFlag;

    /**
     * 错误码Map
     */
    private Map<String, String> errorCodeMap = new HashMap<>();

    /**
     * 添加
     * @param obj 对象
     * @param errorCode 错误码
     */
    public void add(String obj, String errorCode) {
        this.errorCodeMap.put(obj, errorCode);
    }

    public String getMsg() {
        if (errorCodeMap.size() > 0) {
            if (batchFlag) {
                Map<String, List<String>> msgMap = new HashMap<>();
                for (String key : errorCodeMap.keySet()) {
                    if (!msgMap.containsKey(errorCodeMap.get(key))) {
                        msgMap.put(errorCodeMap.get(key), new ArrayList<>());
                    }
                    msgMap.get(errorCodeMap.get(key)).add(key);
                }
                List<String> msgList = new ArrayList<>();
                for (String errorCode : msgMap.keySet()) {
                    EnumPRO.EnumErrorCode enumErrorCode = EnumPRO.EnumErrorCode.getByCode(errorCode);
                    if (StringUtil.isNotNull(enumErrorCode)) {
                        msgList.add(String.format("所选%s中：%s%s", enumErrorCode.getDomain(), String.join("、", msgMap.get(errorCode)), enumErrorCode.getDescription()));
                    }
                }
                return String.join(";", msgList);
            }
            for (String errorCode : errorCodeMap.values()) {
                EnumPRO.EnumErrorCode enumErrorCode = EnumPRO.EnumErrorCode.getByCode(errorCode);
                if (StringUtil.isNotNull(enumErrorCode)) {
                    return enumErrorCode.getDescription();
                }
            }
        }
        return "";
    }
}
