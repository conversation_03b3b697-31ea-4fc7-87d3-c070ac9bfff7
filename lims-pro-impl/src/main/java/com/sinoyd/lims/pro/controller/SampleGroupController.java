package com.sinoyd.lims.pro.controller;

import com.sinoyd.lims.pro.criteria.SampleGroupRecordCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.SampleGroupService;
import com.sinoyd.lims.pro.criteria.SampleGroupCriteria;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * SampleGroup服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@Api(tags = "示例: SampleGroup服务")
@RestController
@RequestMapping("api/pro/sampleGroup")
public class SampleGroupController extends BaseJpaController<DtoSampleGroup, String, SampleGroupService> {


    /**
     * 分页动态条件查询SampleGroup
     *
     * @param sampleGroupCriteria 条件参数
     * @return RestResponse<List < SampleGroup>>
     */
    @ApiOperation(value = "分页动态条件查询SampleGroup", notes = "分页动态条件查询SampleGroup")
    @GetMapping
    public RestResponse<List<DtoSampleGroup>> findByPage(SampleGroupCriteria sampleGroupCriteria) {
        PageBean<DtoSampleGroup> pageBean = super.getPageBean();
        RestResponse<List<DtoSampleGroup>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, sampleGroupCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询SampleGroup
     *
     * @param id 主键id
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "按主键查询SampleGroup", notes = "按主键查询SampleGroup")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoSampleGroup> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoSampleGroup> restResponse = new RestResponse<>();
        DtoSampleGroup sampleGroup = service.findOne(id);
        restResponse.setData(sampleGroup);
        restResponse.setRestStatus(StringUtil.isNull(sampleGroup) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 按条件查询对应送样单下的SampleGroup
     *
     * @param sampleGroupRecordCriteria 条件对象
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "按送样单id查询SampleGroup", notes = "按送样单id查询SampleGroup")
    @GetMapping(path = "/receiveSampleRecord")
    public RestResponse<List<DtoSampleGroup>> findForRecord(SampleGroupRecordCriteria sampleGroupRecordCriteria) {
        RestResponse<List<DtoSampleGroup>> restResponse = new RestResponse<>();
        List<DtoSampleGroup> sampleGroupList = service.findForReceiveSampleRecord(sampleGroupRecordCriteria);
        restResponse.setData(sampleGroupList);
        restResponse.setRestStatus(StringUtil.isNull(sampleGroupList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 按条件查询对应送样单下的SampleGroup
     *
     * @param sampleGroupRecordCriteria 条件对象
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "按送样单id查询SampleGroup", notes = "按送样单id查询SampleGroup")
    @GetMapping(path = "/sample")
    public RestResponse<List<DtoSampleGroup>> findBySamples(SampleGroupRecordCriteria sampleGroupRecordCriteria) {
        RestResponse<List<DtoSampleGroup>> restResponse = new RestResponse<>();
        List<DtoSampleGroup> sampleGroupList = service.findBySamples(sampleGroupRecordCriteria);
        restResponse.setData(sampleGroupList);
        restResponse.setRestStatus(StringUtil.isNull(sampleGroupList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增SampleGroup
     *
     * @param sampleGroup 实体列表
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "新增SampleGroup", notes = "新增SampleGroup")
    @PostMapping
    public RestResponse<DtoSampleGroup> create(@RequestBody @Validated DtoSampleGroup sampleGroup) {
        RestResponse<DtoSampleGroup> restResponse = new RestResponse<>();
        restResponse.setData(service.save(sampleGroup));
        return restResponse;
    }

    /**
     * 修改送样单下的SampleGroup信息
     *
     * @param sampleGroup 实体列表
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "修改送样单下的SampleGroup信息", notes = "修改送样单下的SampleGroup信息")
    @PutMapping("/receiveSampleRecord")
    public RestResponse<DtoSampleGroup> updateForRecord(@RequestBody DtoSampleGroup sampleGroup) {
        RestResponse<DtoSampleGroup> restResponse = new RestResponse<>();
        service.updateForRecord(sampleGroup);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }

    /**
     * 更新送样单下样品的SampleGroup信息
     *
     * @param criteria 实体列表
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "更新送样单下样品的SampleGroup信息", notes = "更新送样单下样品的SampleGroup信息")
    @PostMapping("/receiveSampleRecord/updateGroupInfo")
    public RestResponse<String> updateGroupInfo(@RequestBody SampleGroupRecordCriteria criteria) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.updateGroupInfo(criteria);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }

    /**
     * 新增SampleGroup
     *
     * @param sampleGroup 实体列表
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "修改SampleGroup", notes = "修改SampleGroup")
    @PutMapping
    public RestResponse<DtoSampleGroup> update(@RequestBody @Validated DtoSampleGroup sampleGroup) {
        RestResponse<DtoSampleGroup> restResponse = new RestResponse<>();
        restResponse.setData(service.update(sampleGroup));
        return restResponse;
    }

    /**
     * "根据id批量删除SampleGroup
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除SampleGroup", notes = "根据id批量删除SampleGroup")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 判断送样单下是否存在SampleGroup数据
     *
     * @param receiveId 送样单id
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "判断送样单下是否存在SampleGroup数据", notes = "判断送样单下是否存在SampleGroup数据")
    @GetMapping(path = "/checkExist/{receiveId}")
    public RestResponse<Boolean> checkExist(@PathVariable(name = "receiveId") String receiveId) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        Boolean exist = service.checkExist(receiveId);
        restResponse.setData(exist);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 批量填写接样人接样时间
     *
     * @param sampleGroup 实体列表
     * @return RestResponse<DtoSampleGroup>
     */
    @ApiOperation(value = "批量填写接样人接样时间", notes = "批量填写接样人接样时间")
    @PostMapping("/batch")
    public RestResponse<DtoSampleGroup> batchUpdate(@RequestBody DtoSampleGroup sampleGroup) {
        RestResponse<DtoSampleGroup> restResponse = new RestResponse<>();
        service.batchUpdate(sampleGroup);
        restResponse.setMsg("操作成功！");
        return restResponse;
    }
}