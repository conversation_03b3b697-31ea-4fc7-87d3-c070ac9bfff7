package com.sinoyd.lims.pro.dto;

import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.lims.pro.entity.FlowCalibration;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;
import java.util.Map;

/**
 * DtoFlowCalibration实体
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/14
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_FlowCalibration")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoFlowCalibration extends FlowCalibration {
    /**
     * 数据行
     */
    @Transient
    private List<DtoFlowCalibrationRow> rowList;

    /**
     * 行参数配置
     */
    @Transient
    private List<Map<String,Object>> paramConfigList;

    /**
     * 行参数数据 保存时触发计算
     */
    @Transient
    private List<Map<String,Object>> dataList;

    /**
     * 仪器名称
     */
    @Transient
    private String instrumentName;

    /**
     * 规格型号
     */
    @Transient
    private String model;

    /**
     * 站内编号
     */
    @Transient
    private String instrumentsCode;

    /**
     * 出厂编号
     */
    @Transient
    private String serialNo;

    /**
     * 校准人
     */
    @Transient
    private String calibrationPeopleName;
}
