package com.sinoyd.lims.order.service;

import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;

import java.util.List;

/**
 * 领导驾驶舱客户订单分析大屏展示
 *
 * <AUTHOR>
 * @version V1.0.0 2022/09/26
 */
public interface CustomerOrderAnaService {
    //策略中map的key
    String CUSTOMER_ORDER_MONTH_QUOTATION = "合同额年度趋势";

    String CUSTOMER_ORDER_NUM_ANL = "客户数量分析";

    String CUSTOMER_ORDER_QUOTATION_ANL = "合同额分析";

    String CUSTOMER_ORDER_SPREAD = "客户地区分布签单信息";

    String CUSTOMER_ORDER_STATISTICS="销售人员订单额分析";

    String CUSTOMER_ORDER_TOP_TEN = "单笔签订单额top10";

    String CUSTOMER_ORDER_TYPE_SPREAD="合同类型分布";

    /**
     * 策略模式调用返回单个对象
     * @param year 年份
     * @return BaseOrderCustomerAnalyzeVO
     */
    BaseOrderCustomerAnalyzeVO statistics(Integer year);

    /**
     * 策略模式调用返回list对象
     * @param year 年份
     * @return List<BaseOrderCustomerAnalyzeVO>
     */
    List<BaseOrderCustomerAnalyzeVO> statisticsList(Integer year);
}
