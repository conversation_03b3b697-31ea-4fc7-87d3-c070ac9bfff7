package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoWorkdayConfig;

/**
 * 工作休息日管理配置仓储
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/18
 */
public interface WorkdayConfigRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoWorkdayConfig, String> {

    /**
     * 根据年份查询工作日休息日配置
     *
     * @param year 年份
     * @return DtoWorkdayConfig
     */
    DtoWorkdayConfig findByYear(Integer year);


}