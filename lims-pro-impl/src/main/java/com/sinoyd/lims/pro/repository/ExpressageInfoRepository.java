package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoExpressageInfo;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;


/**
 * 快递数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
public interface ExpressageInfoRepository extends IBaseJpaPhysicalDeleteRepository<DtoExpressageInfo, String> {
    /**
     * 判断是否存在同快递号
     *
     * @param expressNumbers 快递单号
     * @param ids   排除的快递id
     * @return 相同快递单号的数量
     */
    Integer countByExpressNumberInAndIdNotIn(Collection expressNumbers, Collection ids);

    /**
     * 判断是否存在同快递号
     * @param expressNumber 快递单号
     * @param id 排除的快递id
     * @return 相同快递单号的数量
     */
    Integer countByExpressNumberAndIdIsNot(String expressNumber,String id);

    /**
     * 根据项目id查询
     * @param projectId 项目id
     * @return 集合
     */
    List<DtoExpressageInfo> findByProjectId(String projectId);

    /**
     * 发放人模糊检索
     * @param sender 发放人
     * @return 集合
     */
    List<DtoExpressageInfo> findBySenderLike(String sender);
}