package com.sinoyd.lims.od.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.od.dto.DtoLabGroup;
import com.sinoyd.lims.od.dto.DtoOdSample;
import com.sinoyd.lims.od.dto.DtoTask;
import com.sinoyd.lims.od.enums.EnumOds;
import com.sinoyd.lims.od.repository.OdSampleRepository;
import com.sinoyd.lims.od.repository.TaskRepository;
import com.sinoyd.lims.od.service.LabGroupService;
import com.sinoyd.lims.od.service.OdSampleService;
import com.sinoyd.lims.od.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.avalon.framework.Enum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
public class OdSampleServiceImpl extends BaseJpaServiceImpl<DtoOdSample, String, OdSampleRepository> implements OdSampleService {


    private LabGroupService labGroupService;

    private TaskRepository taskRepository;

    @Override
    public void findByPage(PageBean<DtoOdSample> page, BaseCriteria criteria) {
        page.setEntityName("DtoOdSample a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
    }

    @Override
    public DtoOdSample save(DtoOdSample entity) {
        entity.setSampleState(EnumOds.EnumOdsampleState.未完成.getValue());
        DtoTask task = taskRepository.findOne(entity.getTaskId());
        // 新增样品时，已完成的任务置为未完成
        if (task.getTaskState().equals(EnumOds.EnumTaskState.已完成.getValue())) {
            taskRepository.updateStatus(Collections.singletonList(entity.getTaskId()), EnumOds.EnumTaskState.未完成.getValue());
        }
        return super.save(entity);
    }

    @Override
    public List<DtoOdSample> findByTaskIdIn(List<String> taskIds) {
        return repository.findByTaskIdIn(taskIds);
    }

    @Override
    public DtoOdSample completeOdSample(String id) {
        DtoOdSample odSample = repository.findOne(id);
        // 更新样品状态
        odSample.setSampleState(EnumOds.EnumOdsampleState.已完成.getValue());
        DtoOdSample sample = repository.save(odSample);
        List<DtoOdSample> odSampleList = repository.findByTaskId(sample.getTaskId());
        odSampleList.removeIf(p -> p.getId().equals(id));
        // 剔除当前样品后，当任务下不存在其他样品或者其他样品全部完成时，任务状态变为已完成
        if (StringUtil.isEmpty(odSampleList) || odSampleList.stream().allMatch(p -> p.getSampleState().equals(EnumOds.EnumOdsampleState.已完成.getValue()))) {
            taskRepository.updateStatus(Collections.singletonList(sample.getTaskId()), EnumOds.EnumTaskState.已完成.getValue());
        }
        return sample;
    }

    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idList = (List<String>) ids;
        List<DtoLabGroup> labGroups = labGroupService.findBySampleIdIn(idList);
        // 删除实验组数据
        if (StringUtil.isNotEmpty(labGroups)) {
            labGroupService.logicDeleteById(labGroups.stream().map(DtoLabGroup::getId).collect(Collectors.toList()));
        }
        // 更新任务状态
        updateTaskState(idList);
        return super.logicDeleteById(ids);
    }

    /**
     * 更新任务状态
     *
     * @param sampleIds 排除的样品ids
     */
    private void updateTaskState(List<String> sampleIds) {
        List<DtoOdSample> sampleList = repository.findAll(sampleIds);
        List<String> taskIds = sampleList.stream().map(DtoOdSample::getTaskId).distinct().collect(Collectors.toList());
        List<DtoOdSample> odSampleList = repository.findByTaskIdIn(taskIds);
        odSampleList.removeIf(p -> sampleIds.contains(p.getId()));
        Map<String, List<DtoOdSample>> odSampleMap = odSampleList.stream().collect(Collectors.groupingBy(DtoOdSample::getTaskId));
        List<DtoTask> taskList = StringUtil.isNotEmpty(taskIds) ? taskRepository.findAll(taskIds) : new ArrayList<>();
        List<String> incompleteIdList = new ArrayList<>();
        List<String> completeIdList = new ArrayList<>();
        for (DtoTask task : taskList) {
            List<DtoOdSample> odSamples = odSampleMap.get(task.getId());
            if ((StringUtil.isEmpty(odSamples) || odSamples.stream().anyMatch(p -> p.getSampleState().equals(EnumOds.EnumOdsampleState.未完成.getValue())))) {
                incompleteIdList.add(task.getId());
            } else if (odSamples.stream().allMatch(p -> p.getSampleState().equals(EnumOds.EnumOdsampleState.已完成.getValue()))) {
                completeIdList.add(task.getId());
            }
        }
        if (StringUtil.isNotEmpty(incompleteIdList)) {
            taskRepository.updateStatus(incompleteIdList, EnumOds.EnumTaskState.未完成.getValue());
        }
        if (StringUtil.isNotEmpty(completeIdList)) {
            taskRepository.updateStatus(completeIdList, EnumOds.EnumTaskState.已完成.getValue());
        }
    }

    @Autowired
    @Lazy
    public void setLabGroupService(LabGroupService labGroupService) {
        this.labGroupService = labGroupService;
    }

    @Autowired
    @Lazy
    public void setTaskRepository(TaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }
}
