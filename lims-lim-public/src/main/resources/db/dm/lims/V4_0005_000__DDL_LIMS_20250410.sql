ALTER TABLE TB_PRO_Project
    ADD COLUMN otherRemark VARCHAR(255);
ALTER TABLE TB_PRO_Project
    ADD COLUMN coordinate VARCHAR(255);

comment
on column TB_PRO_Project.otherRemark is '其他说明';

comment
on column TB_PRO_Project.coordinate is '协调内容';

ALTER TABLE "TB_PRO_Project" ADD COLUMN "sampleSaveMethod" INT NULL;
ALTER TABLE "TB_PRO_Project" ADD COLUMN "isSaveContainer" INT NULL;
ALTER TABLE "TB_PRO_Project" ADD COLUMN "isSampleCountRight" INT NULL;
ALTER TABLE "TB_PRO_Project" ADD COLUMN "sampleValidityPeriod" INT NULL;
ALTER TABLE "TB_PRO_Project" ADD COLUMN "sampleConfirmRemark" VARCHAR(500);
COMMENT ON COLUMN "TB_PRO_Project"."sampleSaveMethod" IS '保存方式：符合规范、不符合规范、不确定（单选）';
COMMENT ON COLUMN "TB_PRO_Project"."isSaveContainer" IS '保存容器：正确、不正确、不确定（单选）';
COMMENT ON COLUMN "TB_PRO_Project"."isSampleCountRight" IS '样品数量：正确、不正确、不确定（单选）';
COMMENT ON COLUMN "TB_PRO_Project"."sampleValidityPeriod" IS '样品有效期：在有效期内、不在有效期内、不确定（单选）';
COMMENT ON COLUMN "TB_PRO_Project"."sampleConfirmRemark" IS '样品确认其他意见';