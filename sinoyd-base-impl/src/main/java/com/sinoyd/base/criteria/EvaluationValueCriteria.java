package com.sinoyd.base.criteria;

import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * EvaluationValue查询条件
 * <AUTHOR>
 * @version V1.0.0 2019/4/15
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EvaluationValueCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    private String levelId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder conditionSb = new StringBuilder();
        conditionSb.append(" and a.analyzeItemId = b.id and b.isDeleted = 0");

        if (StringUtils.isNotNullAndEmpty(this.levelId)) {
            conditionSb.append(" and a.levelId = :levelId");
            values.put("levelId", this.levelId);
        }
        return conditionSb.toString();
    }
}