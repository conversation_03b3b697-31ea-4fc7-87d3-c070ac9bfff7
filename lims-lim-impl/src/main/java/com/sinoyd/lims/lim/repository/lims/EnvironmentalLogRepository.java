package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalLog;

/**
 * 环境日志
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
public interface EnvironmentalLogRepository extends IBaseJpaPhysicalDeleteRepository<DtoEnvironmentalLog, String> {

    /**
     * 根据environmentalId与objectId查询
     * @param environmentalId 环境标识
     * @param objectId 对象标识
     * @return 环境日志
     */
    DtoEnvironmentalLog findByEnvironmentalIdAndObjectId(String environmentalId,String objectId);
}