package com.sinoyd.lims.od.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.od.dto.DtoOdPerson;
import com.sinoyd.lims.od.repository.OdPersonRepository;
import com.sinoyd.lims.od.service.OdPersonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
public class OdPersonServiceImpl extends BaseJpaServiceImpl<DtoOdPerson, String, OdPersonRepository> implements OdPersonService {

    private PersonService personService;

    @Override
    public List<DtoOdPerson> findByTaskIdOrderBySn(String taskId) {
        List<DtoOdPerson> odPersonList = repository.findByTaskId(taskId);
        // 赋值人员名称
        List<String> personIds = odPersonList.stream().map(DtoOdPerson::getPersonId).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
        odPersonList.forEach(p -> personList.stream().filter(per -> p.getPersonId().equals(per.getId())).findFirst().ifPresent(person -> {
            p.setPersonName(person.getCName());
        }));
        // 按照嗅辨员次序排序
        odPersonList.sort(Comparator.comparing(DtoOdPerson::getPersonSn));
        return odPersonList;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }
}
