package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoReportConfig2Module;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule2GroupType;
import com.sinoyd.lims.lim.repository.rcc.ReportConfig2ModuleRepository;
import com.sinoyd.lims.lim.repository.rcc.ReportModule2GroupTypeRepository;
import com.sinoyd.lims.lim.repository.rcc.ReportModuleRepository;
import com.sinoyd.lims.lim.service.ReportModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 报告组件操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Service
public class ReportModuleServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportModule, String, ReportModuleRepository> implements ReportModuleService {

    private ReportConfig2ModuleRepository reportConfig2ModuleRepository;
    private ReportModule2GroupTypeRepository reportModule2GroupTypeRepository;

    @Override
    public void findByPage(PageBean<DtoReportModule> pb, BaseCriteria recordConfigCriteria) {
        pb.setEntityName("DtoReportModule a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, recordConfigCriteria);
    }

    @Override
    @Transactional
    public DtoReportModule save(DtoReportModule entity) {
        List<DtoReportModule> reportModuleList = repository.findByModuleCodeIn(Collections.singletonList(entity.getModuleCode()));
        if (StringUtil.isNotEmpty(reportModuleList)) {
            throw new BaseException("组件编码已存在！");
        }
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoReportModule update(DtoReportModule entity) {
        List<DtoReportModule> reportModuleList = repository.findByModuleCodeIn(Collections.singletonList(entity.getModuleCode()));
        reportModuleList = reportModuleList.stream().filter(p -> !p.getId().equals(entity.getId())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(reportModuleList)) {
            throw new BaseException("组件编码已存在！");
        }
        return super.update(entity);
    }

    @Override
    @Transactional
    public int deleteModule(List<String> ids) {
        if (StringUtil.isNotEmpty(ids)) {
            //删除组件信息
            int cnt = repository.logicDeleteById(ids);
            //删除组件和报告关联关系
            List<DtoReportConfig2Module> config2ModuleList = reportConfig2ModuleRepository.findByReportModuleIdIn(ids);
            if (StringUtil.isNotEmpty(config2ModuleList)) {
                List<String> config2ModuleIdList = config2ModuleList.stream().map(DtoReportConfig2Module::getId).collect(Collectors.toList());
                reportConfig2ModuleRepository.delete(config2ModuleList);
                //删除报告组件分页方式配置
                List<DtoReportModule2GroupType> reportModule2GroupTypeList = reportModule2GroupTypeRepository.findByReportConfigModuleIdIn(config2ModuleIdList);
                if (StringUtil.isNotEmpty(reportModule2GroupTypeList)) {
                    reportModule2GroupTypeRepository.delete(reportModule2GroupTypeList);
                }
            }
            return cnt;
        }
        return 0;
    }

    @Autowired
    public void setReportConfig2ModuleRepository(ReportConfig2ModuleRepository reportConfig2ModuleRepository) {
        this.reportConfig2ModuleRepository = reportConfig2ModuleRepository;
    }

    @Autowired
    public void setReportModule2GroupTypeRepository(ReportModule2GroupTypeRepository reportModule2GroupTypeRepository) {
        this.reportModule2GroupTypeRepository = reportModule2GroupTypeRepository;
    }
}