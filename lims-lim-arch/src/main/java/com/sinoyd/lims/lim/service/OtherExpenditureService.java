package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoOtherExpenditure;
import com.sinoyd.lims.lim.dto.customer.DtoOtherExpenditureTotal;

/**
 * 收款计划管理
 * <AUTHOR>
 * @version V1.0.0 2019/2/25
 * @since V100R001
 */
public interface OtherExpenditureService extends IBaseJpaService<DtoOtherExpenditure, String> {

    DtoOtherExpenditureTotal getTotalNum(BaseCriteria criteria);
}