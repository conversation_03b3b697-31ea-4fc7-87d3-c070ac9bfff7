package com.sinoyd.lims.lim.service.statistics;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoResourceStatistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Map;

/**
 * 资源统计抽象类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/22
 */
public abstract class AbsResourceStatisticsServiceImpl implements ResourceStatisticsService {

    private JdbcTemplate jdbcTemplate;

    @Override
    public DtoResourceStatistics statistics() {
        String sql = getStatisticsSql();
        Map<String, Object> map = jdbcTemplate.queryForMap(sql);
        if (StringUtil.isEmpty(map)) {
            return new DtoResourceStatistics()
                    .setItemName(getStatisticsItemName())
                    .setNum(0)
                    .setUnit(getUnit());
        } else {
            Long count = (Long) map.get("num");
            return new DtoResourceStatistics()
                    .setItemName(getStatisticsItemName())
                    .setNum(count.intValue())
                    .setUnit(getUnit());
        }
    }

    /**
     * 获取统计查询sql
     *
     * @return 查询sql
     */
    public abstract String getStatisticsSql();

    /**
     * 获取统计类目名称
     *
     * @return 统计类目名称
     */
    public abstract String getStatisticsItemName();

    /**
     * 获取单位
     *
     * @return 单位
     */
    public abstract String getUnit();


    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}