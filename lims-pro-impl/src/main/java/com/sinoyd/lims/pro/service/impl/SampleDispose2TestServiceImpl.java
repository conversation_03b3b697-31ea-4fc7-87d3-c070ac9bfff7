package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoSampleDispose2Test;
import com.sinoyd.lims.pro.repository.SampleDispose2TestRepository;
import com.sinoyd.lims.pro.service.SampleDispose2TestService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.stereotype.Service;


/**
 * SampleDispose2Test操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/11/8
 * @since V100R001
 */
 @Service
public class SampleDispose2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleDispose2Test,String,SampleDispose2TestRepository> implements SampleDispose2TestService {

    @Override
    public void findByPage(PageBean<DtoSampleDispose2Test> pb, BaseCriteria sampleDispose2TestCriteria) {
        pb.setEntityName("DtoSampleDispose2Test a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, sampleDispose2TestCriteria);
    }
}