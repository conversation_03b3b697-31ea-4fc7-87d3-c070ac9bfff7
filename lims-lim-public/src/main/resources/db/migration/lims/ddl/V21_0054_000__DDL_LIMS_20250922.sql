
alter table tb_lim_ocrconfig add column wwInstrumentCode varchar(255)  comment '万维仪器编号';

DROP TABLE IF EXISTS tb_pro_folderperiodwwinfo;
CREATE TABLE tb_pro_folderperiodwwinfo  (
         id varchar(50) NOT NULL COMMENT '主键',
         folderId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '点位id',
         periodCount int(11) NULL DEFAULT NULL,
         issueTime datetime NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '推送时间',
         taskId varchar(50) NULL DEFAULT NULL,
         collectNo varchar(50) NULL DEFAULT NULL,
         PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='点位周期万维信息';
SET FOREIGN_KEY_CHECKS = 1;
