package com.sinoyd.lims.monitor.controller;

import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.monitor.service.DownLoadFixedPointTemplateService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 数据导入模板下载
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
@RestController
@RequestMapping("/api/monitor/downloadTemplate")
public class DownLoadTemplateController  extends ExceptionHandlerController<DownLoadFixedPointTemplateService> {
    /**
     * 环境质量点位导入模板下载
     */
    @GetMapping("/fixedPointHJ")
    public void DownLoadFixedPointHJ(HttpServletResponse response){
        service.downLoadEQ(response,"【导入模板】环境质量点位信息");
    }

    /**
     * 污染源点位导入模板下载
     */
    @GetMapping("/fixedPointWR")
    public void DownLoadFixedPointWR(HttpServletResponse response){
        service.downLoadRS(response,"【导入模板】污染源点位信息");
    }
}
