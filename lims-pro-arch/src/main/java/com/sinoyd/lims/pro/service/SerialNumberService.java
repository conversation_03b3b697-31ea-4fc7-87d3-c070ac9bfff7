package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;

public interface SerialNumberService {
    /**
     * 生成编号方法
     *
     * @param params 参数
     * @return 返回数据
     */
    String createNewNumber(Object... params);


    /**
     * 自动创建编号，需要手动保存流水号
     *
     * @param isAutoCommitSN 是否自动提交SN
     * @param params         参数
     * @return 返回数据
     */
    DtoGenerateSN createNewNumber(Boolean isAutoCommitSN, Object... params);

    /**
     * 根据业务编号查询流水号配置
     *
     * @param serialNumber 业务编号
     * @return 流水号配置实体
     */
    default DtoSerialNumberConfig findByBusinessSerialNumber(String serialNumber){
        return null;
    };
}
