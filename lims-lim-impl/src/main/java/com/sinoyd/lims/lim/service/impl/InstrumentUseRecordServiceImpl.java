package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.InstrumentUseRecordCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoInstrumentUseRecordBath;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.*;
import com.sinoyd.lims.lim.service.InstrumentUseRecord2SampleService;
import com.sinoyd.lims.lim.service.InstrumentUseRecordService;

import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪器使用记录接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-08
 * @since V100R001
 */
@Service
public class InstrumentUseRecordServiceImpl
        extends BaseJpaServiceImpl<DtoInstrumentUseRecord, String, InstrumentUseRecordRepository>
        implements InstrumentUseRecordService {

    @Autowired
    private InstrumentUseRecord2SampleRepository instrumentUseRecord2SampleRepository;

    @Autowired
    @Lazy
    private InstrumentUseRecord2SampleService instrumentUseRecord2SampleService;

    @Autowired
    @Lazy
    private TestService testService;


    @Autowired
    @Lazy
    private PersonService personService;

    @Autowired
    private UserService userService;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private EnvironmentalRecord2SampleRepository environmentalRecord2SampleRepository;

    @Autowired
    private EnvironmentalRecordRepository environmentalRecordRepository;

    @Autowired
    private EnvironmentalRecord2TestRepository environmentalRecord2TestRepository;

    /**
     * 分页查询
     */
    @Override
    public void findByPage(PageBean<DtoInstrumentUseRecord> page, BaseCriteria criteria) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord(");
        stringBuilder.append("x.id,x.instrumentId,x.objectId,x.environmentalManageId,x.objectType,x.usePersonId,x.startTime,");
        stringBuilder.append("x.endTime,x.testIds,x.temperature,x.humidity,x.pressure,x.beforeUseSituation,x.beforeAfterSituation,");
        stringBuilder.append("x.isAssistInstrument,x.remark,x.insOriginDate,i.instrumentName,i.instrumentsCode,i.model,i.inspectResult,i.serialNo, i.originEndDate)");

        // 设置查询的实体类名及别名
        page.setEntityName("DtoInstrumentUseRecord x,DtoInstrument i");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect(stringBuilder.toString());

        InstrumentUseRecordCriteria instrumentUseRecordCriteria = (InstrumentUseRecordCriteria) criteria;
        int pageNum = page.getPageNo();
        int rowsPerPage = page.getRowsPerPage();
        if (StringUtil.isNotNull(instrumentUseRecordCriteria.getIsFilter()) && instrumentUseRecordCriteria.getIsFilter()) {
            page.setPageNo(1);
            page.setRowsPerPage(Integer.MAX_VALUE);
        }
        super.findByPage(page, criteria);
        page.setPageNo(pageNum);
        page.setRowsPerPage(rowsPerPage);

        List<DtoInstrumentUseRecord> datas = page.getData();

        //相关的测试项目id
        List<String> tAllIds = datas.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getTestIds())).map(DtoInstrumentUseRecord::getTestIds).collect(Collectors.toList());

        Set<String> testAllIds = new HashSet<>();

        for (String testId : tAllIds) {
            testAllIds.addAll(Arrays.asList(testId.split(",")));
        }

        //实验室仪器使用记录不存储testIds，需要从检测单中获取
        Map<String, List<String>> worksheetFolderId2TestIdListMap = new HashMap<>();
        List<String> testIdListForFolder = new ArrayList<>();
        List<String> workSheetFolderIdList = datas.stream().filter(p -> StringUtil.isEmpty(p.getTestIds())
                && EnumLIM.EnumInsUseObjType.实验室分析.getValue().equals(p.getObjectType())).map(DtoInstrumentUseRecord::getObjectId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(workSheetFolderIdList)) {
            List<Object[]> objects = comRepository.find("select a.workSheetFolderId, a.testId from DtoAnalyseData a where a.workSheetFolderId in :folderIds and a.isDeleted = 0",
                    Collections.singletonMap("folderIds", workSheetFolderIdList));
            for (Object[] obj : objects) {
                String folderId = obj[0].toString();
                String testId = obj[1].toString();
                if (!testIdListForFolder.contains(testId)) {
                    testIdListForFolder.add(testId);
                }
                if (!worksheetFolderId2TestIdListMap.containsKey(folderId)) {
                    worksheetFolderId2TestIdListMap.put(folderId, new ArrayList<>());
                }
                if (!worksheetFolderId2TestIdListMap.get(folderId).contains(testId)) {
                    worksheetFolderId2TestIdListMap.get(folderId).add(testId);
                }
            }
        }
        if (StringUtil.isNotEmpty(testIdListForFolder)) {
            testAllIds.addAll(testIdListForFolder);
        }

        List<DtoTest> testList = new ArrayList<>();
        if (testAllIds.size() > 0) {
            testList = testService.findRedisByIds(new ArrayList<>(testAllIds));
        }

        //使用人员
        List<String> userPersonIds = datas.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getUsePersonId())).map(DtoInstrumentUseRecord::getUsePersonId).collect(Collectors.toList());
        List<DtoPerson> personList = new ArrayList<>();
        List<DtoUser> userList = new ArrayList<>();
        if (userPersonIds.size() > 0) {
            personList = personService.findAllDeleted(userPersonIds);

            List<String> personIds = personList.stream().map(DtoPerson::getId).distinct().collect(Collectors.toList());

            //剩余的数据从用户中查询
            List<String> otherIds = userPersonIds.stream().filter(p -> !personIds.contains(p)).collect(Collectors.toList());

            if (otherIds.size() > 0) {
                userList = userService.findByIds(otherIds);
            }
        }
        List<DtoInstrumentUseRecord> newDatas = new ArrayList<>();

        Iterator<DtoInstrumentUseRecord> recordIte = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (recordIte.hasNext()) {
            DtoInstrumentUseRecord dtoInstrumentUseRecord = recordIte.next();
            String testName = "";
            String testIds = dtoInstrumentUseRecord.getTestIds();
            List<String> testIdList;
            if (StringUtils.isNotNullAndEmpty(testIds)) {
                testIdList = Arrays.asList(testIds.split(","));
                List<String> names = testList.stream().filter(p -> testIdList.contains(p.getId())).map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
                testName = String.join(",", names);
            } else if (EnumLIM.EnumInsUseObjType.实验室分析.getValue().equals(dtoInstrumentUseRecord.getObjectType())) {
                testIdList = worksheetFolderId2TestIdListMap.getOrDefault(dtoInstrumentUseRecord.getObjectId(), new ArrayList<>());
                List<String> names = testList.stream().filter(p -> testIdList.contains(p.getId())).map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
                testName = String.join(",", names);
            }else{
                testIdList = new ArrayList<>();
            }
            String userPersonId = dtoInstrumentUseRecord.getUsePersonId();
            String userPerson = "";
            Optional<DtoPerson> dtoPersonOptional = personList.stream().filter(p -> p.getId().equals(userPersonId)).findFirst();
            if (dtoPersonOptional.isPresent()) {
                userPerson = dtoPersonOptional.get().getCName();
            } else {
                Optional<DtoUser> dtoUserOptional = userList.stream().filter(p -> p.getId().equals(userPersonId)).findFirst();
                if (dtoUserOptional.isPresent()) {
                    userPerson = dtoUserOptional.get().getUserName();
                }
            }
            dtoInstrumentUseRecord.setUsePerson(userPerson);
            dtoInstrumentUseRecord.setTestName(testName);
            dtoInstrumentUseRecord.setTestIdArray(testIdList);
            newDatas.add(dtoInstrumentUseRecord);
        }
        if (instrumentUseRecordCriteria.getIsFilter()) {
            List<String> managerIds = newDatas.stream().map(DtoInstrumentUseRecord::getEnvironmentalManageId).distinct().collect(Collectors.toList());
            List<DtoInstrumentUseRecord> allInstrumentUseRecords = StringUtil.isNotEmpty(managerIds) ? repository.findByEnvironmentalManageIdIn(managerIds) : new ArrayList<>();
            Map<String, Long> manageId2RecordNum = allInstrumentUseRecords.stream().collect(Collectors.groupingBy(DtoInstrumentUseRecord::getEnvironmentalManageId, Collectors.counting()));
            List<String> filterManagerIds = new ArrayList<>();
            manageId2RecordNum.forEach((id, num) -> {
                if (num.equals(1L)) {
                    filterManagerIds.add(id);
                }
            });
            newDatas = newDatas.stream().filter(d -> filterManagerIds.contains(d.getEnvironmentalManageId())).collect(Collectors.toList());
            List<DtoInstrumentUseRecord> filterDatas = new ArrayList<>();
            for (DtoInstrumentUseRecord data : newDatas) {
                if (filterDatas.stream().noneMatch(d -> d.getInstrumentId().equals(data.getInstrumentId()) && d.getStartTime().equals(data.getStartTime()) && d.getEndTime().equals(data.getEndTime()))) {
                    filterDatas.add(data);
                }
            }
            page.setRowsCount(filterDatas.size());
            filterDatas = filterDatas.stream().skip((long) (page.getPageNo() - 1) * page.getRowsPerPage()).limit(page.getRowsPerPage()).collect(Collectors.toList());
            page.setData(filterDatas);
        } else {
            page.setData(newDatas);
        }
    }

    /**
     * 获取仪器使用记录
     *
     * @param environmentalManageIds 环境记录id集合
     * @return 仪器使用记录
     */
    @Override
    public List<DtoInstrumentUseRecord> findByEnvironmentalManageIds(List<String> environmentalManageIds) {
        if (StringUtil.isNotNull(environmentalManageIds) && environmentalManageIds.size() > 0) {
            PageBean<DtoInstrumentUseRecord> page = new PageBean<>();
            page.setRowsPerPage(Integer.MAX_VALUE);
            page.setRowsCount(Integer.MAX_VALUE);
            page.setSort("x.testIds-");
            InstrumentUseRecordCriteria criteria = new InstrumentUseRecordCriteria();
            criteria.setEnvironmentalManageIds(environmentalManageIds);
            this.findByPage(page, criteria);
            return page.getData();
        }
        return new ArrayList<>();
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        instrumentUseRecord2SampleRepository.deleteByInstrumentUseRecordId(idStr);
        return super.logicDeleteById(id);
    }

    @Override
    @Transactional
    public Integer newDelete(String id) {
        DtoInstrumentUseRecord instrumentUseRecord = repository.findOne(id);
        List<DtoInstrumentUseRecord> list = repository.findByObjectIdAndObjectType(instrumentUseRecord.getObjectId(),instrumentUseRecord.getObjectType());
        list = list.stream().filter(r->instrumentUseRecord.getEnvironmentalManageId().equals(r.getEnvironmentalManageId())&&instrumentUseRecord.getInstrumentId().equals(r.getInstrumentId()))
                .collect(Collectors.toList());
        return super.logicDeleteById(list.stream().map(DtoInstrumentUseRecord::getId).collect(Collectors.toList()));
    }

    /**
     * 根据关联id及类型获取仪器使用记录
     *
     * @param objectId   关联id
     * @param objectType 关联类型
     * @return 仪器使用记录
     */
    @Override
    public List<DtoInstrumentUseRecord> findByObjectIdAndObjectType(String objectId, Integer objectType) {
        return repository.findByObjectIdAndObjectType(objectId, objectType);
    }

    /**
     * 根据关联id及类型获取仪器使用记录
     *
     * @param objectIds  关联id集合
     * @param objectType 关联类型
     * @return 仪器使用记录
     */
    @Override
    public List<DtoInstrumentUseRecord> findByObjectIdInAndObjectType(List<String> objectIds, Integer objectType) {
        return repository.findByObjectIdInAndObjectType(objectIds, objectType);
    }

    @Override
    @Transactional
    public DtoInstrumentUseRecord saveInstrumentUseRecord(DtoInstrumentUseRecord dtoInstrumentUseRecord) {
        if (StringUtil.isNotEmpty(dtoInstrumentUseRecord.getTestIdArray())){
            dtoInstrumentUseRecord.setTestIds(String.join(",",dtoInstrumentUseRecord.getTestIdArray()));
        }
        DtoInstrumentUseRecord item = super.save(dtoInstrumentUseRecord);
        List<String> sampleIds = dtoInstrumentUseRecord.getSampleIds();
        saveInstrumentUseRecord2Sample(sampleIds, item.getId());
        item.setSampleIds(sampleIds);
        return item;
    }


    @Override
    @Transactional
    public List<DtoInstrumentUseRecord> newSave(String environmentalRecordId, List<DtoInstrumentUseRecord> entityList) {
        List<DtoEnvironmentalRecord2Sample> record2SampleList = environmentalRecord2SampleRepository.findAllByEnvironmentalRecordIdIn(Collections.singletonList(environmentalRecordId));
        List<String> sampleIds = record2SampleList.stream().map(DtoEnvironmentalRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
        List<DtoEnvironmentalRecord2Test> record2TestList = environmentalRecord2TestRepository.findAllByEnvironmentalRecordIdIn(Collections.singletonList(environmentalRecordId));
        List<String> testIds = record2TestList.stream().map(DtoEnvironmentalRecord2Test::getTestId).distinct().collect(Collectors.toList());
        DtoEnvironmentalRecord environmentalRecord = environmentalRecordRepository.findOne(environmentalRecordId);
        String objectId = environmentalRecord.getObjectId();
        Integer objectType = environmentalRecord.getObjectType();
        List<DtoInstrumentUseRecord> saveList = new ArrayList<>();
        List<DtoInstrumentUseRecord2Sample> i2sList = new ArrayList<>();
        for (DtoInstrumentUseRecord useRecord : entityList) {
            for (String testId : testIds) {
                DtoInstrumentUseRecord record = initUseRecord(environmentalRecordId, objectId, testId, objectType, useRecord);
                saveList.add(record);
                fillRecord2SampleList(sampleIds, i2sList, record.getId());
            }
        }
        instrumentUseRecord2SampleRepository.save(i2sList);
        return repository.save(saveList);
    }

    /**
     * 初始化一个新的仪器使用记录对象
     *
     * @param environmentalRecordId 环境记录id
     * @param objectId              关联对象id
     * @param testId                测试项目id
     * @param objectType            关联对象类型
     * @param useRecord             仪器使用记录对象
     */
    private DtoInstrumentUseRecord initUseRecord(String environmentalRecordId, String objectId, String testId, Integer objectType, DtoInstrumentUseRecord useRecord) {
        DtoInstrumentUseRecord record = new DtoInstrumentUseRecord();
        record.setTestIds(testId);
        record.setInstrumentId(useRecord.getInstrumentId());
        record.setInsOriginDate(useRecord.getInsOriginDate());
        record.setBeforeUseSituation(useRecord.getBeforeUseSituation());
        record.setBeforeAfterSituation(useRecord.getBeforeAfterSituation());
        record.setIsAssistInstrument(useRecord.getIsAssistInstrument());
        record.setRemark(useRecord.getRemark());
        record.setStartTime(useRecord.getStartTime());
        record.setEndTime(useRecord.getEndTime());
        record.setTemperature(useRecord.getTemperature());
        record.setHumidity(useRecord.getHumidity());
        record.setPressure(useRecord.getPressure());
        record.setObjectId(objectId);
        record.setObjectType(objectType);
        record.setEnvironmentalManageId(environmentalRecordId);
        record.setUsePersonId(useRecord.getUsePersonId());
        return record;
    }

    @Override
    @Transactional
    public List<DtoInstrumentUseRecord> newSaveBath(List<DtoInstrumentUseRecordBath> useRecordBathList) {
        List<String> environmentalRecordIdList = useRecordBathList.stream().map(DtoInstrumentUseRecordBath::getEnvironmentalRecordId).distinct().collect(Collectors.toList());
        List<DtoEnvironmentalRecord2Sample> record2SampleList = environmentalRecord2SampleRepository.findAllByEnvironmentalRecordIdIn(environmentalRecordIdList);
        Map<String, List<DtoEnvironmentalRecord2Sample>> record2SampleMap = record2SampleList.stream().collect(Collectors.groupingBy(DtoEnvironmentalRecord2Sample::getEnvironmentalRecordId));
        Map<String, List<String>> recordId2SampleIdMap = new HashMap<>();
        record2SampleMap.forEach((k, v) -> recordId2SampleIdMap.put(k, v.stream().map(DtoEnvironmentalRecord2Sample::getSampleId).distinct().collect(Collectors.toList())));
        Map<String, List<DtoEnvironmentalRecord2Test>> record2TestMap = environmentalRecord2TestRepository.findAllByEnvironmentalRecordIdIn(environmentalRecordIdList)
                .stream().collect(Collectors.groupingBy(DtoEnvironmentalRecord2Test::getEnvironmentalRecordId));
        Map<String, List<String>> recordId2TestIdMap = new HashMap<>();
        record2TestMap.forEach((k,v) -> recordId2TestIdMap.put(k, v.stream().map(DtoEnvironmentalRecord2Test::getTestId).distinct().collect(Collectors.toList())));
        Map<String, DtoEnvironmentalRecord> environmentalRecordMap = environmentalRecordRepository.findAll(environmentalRecordIdList).stream().collect(Collectors.toMap(DtoEnvironmentalRecord::getId, dto -> dto));
        List<DtoInstrumentUseRecord> saveList = new ArrayList<>();
        List<DtoInstrumentUseRecord2Sample> i2sList = new ArrayList<>();
        for (DtoInstrumentUseRecordBath useRecordBath : useRecordBathList) {
            String environmentRecordId = useRecordBath.getEnvironmentalRecordId();
            List<String> sampleIds = recordId2SampleIdMap.getOrDefault(environmentRecordId, new ArrayList<>());
            DtoEnvironmentalRecord environmentalRecord = environmentalRecordMap.getOrDefault(environmentRecordId, null);
            String objectId = StringUtil.isNotNull(environmentalRecord) ? environmentalRecord.getObjectId() : UUIDHelper.GUID_EMPTY;
            Integer objectType = StringUtil.isNotNull(environmentalRecord) ? environmentalRecord.getObjectType() : -1;
            List<String> testIds = recordId2TestIdMap.getOrDefault(environmentRecordId, new ArrayList<>());
            for (DtoInstrumentUseRecord useRecord : useRecordBath.getUseRecordList()) {
                for (String testId : testIds) {
                    DtoInstrumentUseRecord record = initUseRecord(environmentRecordId, objectId, testId, objectType, useRecord);
                    saveList.add(record);
                    fillRecord2SampleList(sampleIds, i2sList, record.getId());
                }
            }
        }
        instrumentUseRecord2SampleRepository.save(i2sList);
        return repository.save(saveList);
    }

    /**
     * 填充仪器使用记录与样品关联关系列表
     *
     * @param sampleIds 样品id列表
     * @param i2sList   仪器使用记录与样品关联关系列表
     * @param recordId 仪器使用记录id
     */
    private void fillRecord2SampleList(List<String> sampleIds, List<DtoInstrumentUseRecord2Sample> i2sList, String recordId) {
        if (StringUtil.isNotNull(sampleIds) && sampleIds.size() > 0) {
            for (String sampleId : sampleIds) {
                DtoInstrumentUseRecord2Sample i2s = new DtoInstrumentUseRecord2Sample();
                i2s.setSampleId(sampleId);
                i2s.setInstrumentUseRecordId(recordId);
                i2sList.add(i2s);
            }
        }
    }

    @Transactional
    @Override
    public DtoInstrumentUseRecord updateInstrumentUseRecord(DtoInstrumentUseRecord dtoInstrumentUseRecord) {
        //先删除所有的使用记录相关的样品
        instrumentUseRecord2SampleRepository.deleteByInstrumentUseRecordId(dtoInstrumentUseRecord.getId());
        if (StringUtil.isNotEmpty(dtoInstrumentUseRecord.getTestIdArray())){
            dtoInstrumentUseRecord.setTestIds(String.join(",",dtoInstrumentUseRecord.getTestIdArray()));
        }
        DtoInstrumentUseRecord item = super.update(dtoInstrumentUseRecord);
        List<String> sampleIds = dtoInstrumentUseRecord.getSampleIds();
        saveInstrumentUseRecord2Sample(sampleIds, item.getId());
        item.setSampleIds(sampleIds);
        return item;
    }

    @Override
    @Transactional
    public List<DtoInstrumentUseRecord> newUpdate(DtoInstrumentUseRecord instrumentUseRecord) {
        List<DtoInstrumentUseRecord> list = repository.findByObjectIdAndObjectType(instrumentUseRecord.getObjectId(),instrumentUseRecord.getObjectType());
        list = list.stream().filter(r->instrumentUseRecord.getEnvironmentalManageId().equals(r.getEnvironmentalManageId())&&instrumentUseRecord.getInstrumentId().equals(r.getInstrumentId()))
                .collect(Collectors.toList());
        for (DtoInstrumentUseRecord record:list) {
            BeanUtils.copyProperties(instrumentUseRecord, record, "id", "creator", "createDate", "modifier", "modifyDate", "orgId", "domainId");
        }
        return repository.save(list);
    }

    @Override
    public List<DtoInstrumentUseRecord> findByInstrumentIdInAndObjectTypeAndStartTimeBeforeAndEndTimeAfter
            (List<String> instrumentIds, Integer objectType, Date start, Date end) {
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(start);
        startCalendar.add(Calendar.SECOND, -1);

        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(end);
        endCalendar.add(Calendar.SECOND, 1);

        StringBuilder select = new StringBuilder("select a from DtoInstrumentUseRecord as a");

        select.append(" where 1=1 and a.isDeleted = 0");
        select.append(" and a.instrumentId in :instrumentIds");

        //2022-3-3 过滤仪器的时候，不需要通过仪器使用类型进行区分
        //select.append(" and a.objectType=:objectType");

        //开始时间在时间范围内，或者结束时间在时间范围内，或者开始时间和结束时间包含整改时间
        select.append(" and (( a.startTime < :start  and  a.endTime > :start ) or ( a.startTime < :end and  a.endTime > :end ) or ( a.startTime > :start and a.endTime < :end ))");

        Map<String, Object> values = new HashMap<>();
        values.put("instrumentIds", instrumentIds);
        //values.put("objectType", objectType);
        values.put("start", startCalendar.getTime());
        values.put("end", endCalendar.getTime());
        return comRepository.find(select.toString(), values);

    }


    /**
     * 保存仪器使用记录相关样品
     *
     * @param sampleIds 样品ids
     * @param id        仪器使用记录id
     */
    private void saveInstrumentUseRecord2Sample(List<String> sampleIds, String id) {
        if (StringUtil.isNotNull(sampleIds) && sampleIds.size() > 0) {
            List<DtoInstrumentUseRecord2Sample> dtoInstrumentUseRecord2Samples = new ArrayList<>();
            for (String sampleId : sampleIds) {
                DtoInstrumentUseRecord2Sample dtoInstrumentUseRecord2Sample = new DtoInstrumentUseRecord2Sample();
                dtoInstrumentUseRecord2Sample.setInstrumentUseRecordId(id);
                dtoInstrumentUseRecord2Sample.setSampleId(sampleId);
                dtoInstrumentUseRecord2Samples.add(dtoInstrumentUseRecord2Sample);
            }
            instrumentUseRecord2SampleService.save(dtoInstrumentUseRecord2Samples);
        }
    }
}