package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.OcrConfigParamData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;
import java.util.Map;

/**
 * DtoOcrConfigParamData
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tb_lim_ocrConfigParamData")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoOcrConfigParamData extends OcrConfigParamData {
    /**
     * 参数名称
     */
    @Transient
    private String paramName;

    /**
     * 参数别名
     */
    @Transient
    private String paramNameAlias;

    /**
     * 参数类型
     */
    @Transient
    private Integer paramType;

    /**
     * 因子
     */
    @Transient
    private String analyzeItemId;

    /**
     * 参数名称
     */
    @Transient
    private String dimension;

    /**
     * 排序值
     */
    @Transient
    private Integer orderNum = 0;

    /**
     * 识别人
     */
    @Transient
    private String creatorName;
}
