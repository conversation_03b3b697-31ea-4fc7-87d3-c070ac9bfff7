package com.sinoyd.base.dto.lims;

import com.sinoyd.base.entity.EnterpriseEvaluate;
import com.sinoyd.frame.base.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 供应商评价信息DTO
 *
 * <AUTHOR>
 * @version V1.0.0 2024/3/14
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_EnterpriseEvaluate")
@Data
@DynamicInsert
public class DtoEnterpriseEvaluate extends EnterpriseEvaluate {

} 