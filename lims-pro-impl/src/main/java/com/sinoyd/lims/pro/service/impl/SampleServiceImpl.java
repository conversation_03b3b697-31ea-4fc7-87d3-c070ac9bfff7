package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.SpringContextAware;
import com.sinoyd.base.configuration.XmlConfig;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.factory.QualityTaskFactory;
import com.sinoyd.base.factory.quality.*;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.SortUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.customer.DtoSampleTypeTemplate;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRangeResult;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestPost2PersonRepository;
import com.sinoyd.lims.lim.repository.lims.TestPostRepository;
import com.sinoyd.lims.lim.repository.rcc.*;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.rcc.DtoPointExtendData;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.monitor.repository.rcc.PointExtendDataRepository;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.SampleCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.*;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.ArrayUtil;
import com.sinoyd.lims.pro.util.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.DeclareAnnotation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.Collator;
import java.text.DecimalFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 样品操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/19
 * @since V100R001
 */
@Service
@Slf4j
public class SampleServiceImpl extends BaseJpaServiceImpl<DtoSample, String, SampleRepository> implements SampleService {

    //#region 注入
    @Autowired
    @Qualifier("sample")
    @Lazy
    private SerialNumberService serialNumberService;

    @Autowired
    @Qualifier("innerSample")
    @Lazy
    private SerialNumberService innerSampleSerialNumberService;

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectTypeRepository projectTypeRepository;

    @Autowired
    @Lazy
    private ReceiveSampleRecordService receiveSampleRecordService;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private ReceiveSubSampleRecord2SampleRepository receiveSubSampleRecord2SampleRepository;

    @Autowired
    @Lazy
    private SampleFolderService sampleFolderService;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    @Lazy
    private SamplingFrequencyService samplingFrequencyService;

    @Autowired
    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    @Lazy
    private ReceiveSubSampleRecordService receiveSubSampleRecordService;

    @Autowired
    private ReceiveSubSampleRecordRepository receiveSubSampleRecordRepository;

    @Autowired
    @Lazy
    private WorkSheetFolderService workSheetFolderService;

    @Autowired
    @Lazy
    private ParamsDataService paramsDataService;

    @Autowired
    private ParamsDataRepository paramsDataRepository;

    @Autowired
    @Lazy
    private QualityControlService qualityControlService;

    @Autowired
    private QualityControlRepository qualityControlRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private ParamsConfigService paramsConfigService;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private SampleType2TestService sampleType2TestService;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    @Lazy
    private InstrumentUseRecord2SampleService instrumentUseRecord2SampleService;

    @Autowired
    @Lazy
    private ParamsTestFormulaService paramsTestFormulaService;

    @Autowired
    @Lazy
    private HomeService homeService;

    @Autowired
    private LogForSampleRepository logForSampleRepository;

    @Autowired
    private LogForDataRepository logForDataRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    private FixedpointRepository fixedpointRepository;

    @Autowired
    private AnalyseOriginalRecordRepository analyseOriginalRecordRepository;

    @Autowired
    @Lazy
    private StatusForRecordService statusForRecordService;

    @Autowired
    @Lazy
    private CodeService codeService;

    @Autowired
    @Lazy
    private SampleTypeGroup2TestService sampleTypeGroup2TestService;

    @Autowired
    @Lazy
    private SampleTypeGroupRepository sampleTypeGroupRepository;

    @Autowired
    @Lazy
    private StatusForRecordRepository statusForRecordRepository;

    @Autowired
    private SerialNumberConfigRepository serialNumberConfigRepository;

    @Autowired
    private QualityControlLimitRepository qualityControlLimitRepository;

    @Autowired
    private QualityControlEvaluateRepository qualityControlEvaluateRepository;

    @Autowired
    @Lazy
    private QualityControlEvaluateService qualityControlEvaluateService;

    @Autowired
    private DimensionRepository dimensionRepository;

    @Autowired
    @Lazy
    private DimensionService dimensionService;

    private TestPostRepository testPostRepository;

    private TestPost2PersonRepository testPost2PersonRepository;

    private SampleJudgeDataService sampleJudgeDataService;

    private AnalyseDataFutureService analyseDataFutureService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CompareJudgeRepository compareJudgeRepository;

    @Autowired
    private SampleJudgeDataRepository sampleJudgeDataRepository;

    private SampleGroupService sampleGroupService;

    @Autowired
    private PersonRepository personRepository;

    private BusinessSerialNumberService businessSerialNumberService;

    @Autowired
    private PointExtendDataRepository pointExtendDataRepository;

    @Autowired
    @Lazy
    private OcrConfigService ocrConfigService;

    @Autowired
    @Lazy
    private OcrConfigRecordService ocrConfigRecordService;

    @Autowired
    private FolderSignRepository folderSignRepository;

    //#endregion 注入

    @Override
    public void findByPage(PageBean<DtoSample> pb, BaseCriteria sampleCriteria) {
        // 多表关联查询返回自定义字段
        pb.setEntityName("DtoSample s");
        pb.setSelect("select s");

        SampleCriteria criteria = (SampleCriteria) sampleCriteria;
        if (criteria.getIsShowQc()) {
            if (StringUtil.isNotEmpty(criteria.getProjectId()) && !UUIDHelper.GUID_EMPTY.equals(criteria.getProjectId())) {
                List<String> recIds = receiveSampleRecordRepository.findByProjectId(criteria.getProjectId()).stream()
                        .map(DtoReceiveSampleRecord::getId).collect(Collectors.toList());
                criteria.setReceiveIds(recIds);
            }
        }

        super.findByPage(pb, criteria);
        List<DtoSample> newDatas = pb.getData();
        if (newDatas.size() > 0) {
            List<String> sampleTypeIds = newDatas.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
            List<String> parentSampleIds = sampleTypes.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getParentId())
                    && !p.getParentId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType::getParentId).distinct().collect(Collectors.toList());
            List<DtoSampleType> parentSampleTypes = sampleTypeService.findRedisByIds(parentSampleIds);
            List<String> sampleIdList = newDatas.stream().map(DtoSample::getId).collect(Collectors.toList());
            String anaDataSql = " select a.sampleId, a.isOutsourcing from DtoAnalyseData a where a.isDeleted = 0 and a.sampleId in :sampleIdList ";
            Map<String, Object> values = new HashMap<>();
            values.put("sampleIdList", sampleIdList);
            List<Object[]> anaDataList = comRepository.find(anaDataSql, values);
            Map<String, Boolean> sampleId2OutSrcMap = new HashMap<>();
            for (Object[] anaData : anaDataList) {
                String sampleId = anaData[0].toString();
                if (!sampleId2OutSrcMap.containsKey(sampleId) || !sampleId2OutSrcMap.get(sampleId)) {
                    sampleId2OutSrcMap.put(sampleId, (Boolean) anaData[1]);
                }
            }
            Date date = new Date();
//            List<DtoAnalyseData> anaData = analyseDataRepository.findBySampleIdIn(sampleIdList);
            List<DtoAnalyseData> anaData = new ArrayList<>();
            List<Future<List<DtoAnalyseData>>> analyseDataResultList = new ArrayList<>();
            List<String> list = null;
            final int batchSize = 30;
            for (String sampleId : sampleIdList) {
                if (list == null) {
                    list = new ArrayList<>();
                }
                if (list.size() < batchSize) {
                    list.add(sampleId);
                } else if (list.size() == batchSize) {
                    //多线程处理排序
                    analyseDataResultList.add(analyseDataFutureService.getListBySampleIdIn(list));
                    list = new ArrayList<>();
                    list.add(sampleId);
                }
            }
            //如果存在最后一批样，需要单独去排序处理
            if (StringUtil.isNotEmpty(list)) {
                analyseDataResultList.add(analyseDataFutureService.getListBySampleIdIn(list));
            }
            //处理多线程处理的结果
            try {
                for (Future<List<DtoAnalyseData>> analyseDataResult : analyseDataResultList) {
                    while (true) {
                        if (analyseDataResult.isDone() && !analyseDataResult.isCancelled()) {
                            anaData.addAll(analyseDataResult.get());
                            break;
                        } else {
                            //防止CPU高速轮询被耗空
                            Thread.sleep(1);
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BaseException("......多线程处理分析数据出错......");
            }

            anaData.removeIf(AnalyseData::getIsDeleted);
            Map<String, List<DtoAnalyseData>> anaDataGroup = anaData.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
            Date date2 = new Date();
            log.info("==========获取分析数据耗时：{}", date2.getTime() - date.getTime());

            for (DtoSample sample : newDatas) {
                DtoSampleType sampleType = sampleTypes.stream().filter(p -> p.getId().equals(sample.getSampleTypeId())).findFirst().orElse(null);
                String bigSampleTypeId = "";
                String sampleTypeName = "";
                String icon = "";
                if (StringUtil.isNotNull(sampleType)) {
                    sampleTypeName = sampleType.getTypeName();
                    DtoSampleType parentSampleType = parentSampleTypes.stream().filter(p -> p.getParentId()
                            .equals(sampleType.getParentId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(parentSampleType)) {
                        icon = parentSampleType.getIcon();
                    }
                    bigSampleTypeId = sampleType.getParentId();
                }
                if (StringUtil.isNotEmpty(anaDataGroup)) {
                    List<DtoAnalyseData> analyseDataList = anaDataGroup.getOrDefault(sample.getId(), new ArrayList<>());
                    sample.setAnalyseData(analyseDataList);
                    String analyzeItemContainOut = analyseDataList.stream().filter(p -> !p.getIsOutsourcing()).map(DtoAnalyseData::getRedAnalyzeItemName).collect(Collectors.joining(","));
                    sample.setAnalyzeItemContainOut(analyzeItemContainOut);

                }
                sample.setSampleTypeName(sampleTypeName);
                sample.setIcon(icon);
                sample.setBigSampleTypeId(bigSampleTypeId);
                sample.setIsOutsourcing(0);
                if (sampleId2OutSrcMap.containsKey(sample.getId()) && sampleId2OutSrcMap.get(sample.getId())) {
                    sample.setIsOutsourcing(1);
                }
            }

            //根据前端传的每页条数来判断是否需要进行排序
//            if (pb.getRowsPerPage() >= BaseCodeHelper.MAX_ROWS) {
//                newDatas = this.sortPrepareSample(newDatas, false);
//            }
            pb.setData(newDatas);
        }
    }

    @Override
    public void findSampleAndProjectByPage(PageBean<DtoSample> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoSample s,DtoProject p");
        pageBean.setSelect("select new com.sinoyd.lims.pro.dto.DtoSample(s.id,s.code,s.sampleTypeId,s.redFolderName,s.samplingTimeBegin," +
                "p.projectName,p.projectCode,p.inspectedEnt,s.status)");
        commonRepository.findByPage(pageBean, criteria);
        List<DtoSample> samples = pageBean.getData();
        List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        //获取分析数据
        List<DtoAnalyseData> analyseData = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        List<String> sampleTypeIds = samples.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        //获取样品类型（小类）
        List<DtoSampleType> sampleTypes = new ArrayList<>();
        //获取样品类型（大类）
        List<DtoSampleType> parentSampleTypes = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleTypeIds)) {
            sampleTypes = sampleTypeRepository.findAll(sampleTypeIds);
            if (StringUtil.isNotEmpty(sampleTypes)) {
                List<String> parentSampleTypeIds = sampleTypes.parallelStream()
                        .map(DtoSampleType::getParentId).distinct().collect(Collectors.toList());
                if (StringUtil.isNotEmpty(parentSampleTypeIds)) {
                    parentSampleTypes = sampleTypeRepository.findAll(parentSampleTypeIds);
                }
            }
        }
        for (DtoSample sample : samples) {
            List<DtoAnalyseData> analyseDataOfSampleItem = analyseData.stream()
                    .filter(a -> a.getSampleId().equals(sample.getId())).collect(Collectors.toList());
            //填充测试项目
            List<String> analyseItemNamesOfSampleItem = analyseDataOfSampleItem.stream()
                    .map(DtoAnalyseData::getRedAnalyzeItemName).collect(Collectors.toList());
            Collections.sort(analyseItemNamesOfSampleItem);
            sample.setAnalyseItems(String.join(",", analyseItemNamesOfSampleItem));
            sample.setAnalyseData(analyseDataOfSampleItem);
            //填充样品类型
            Optional<DtoSampleType> sampleTypeOptional = sampleTypes.stream()
                    .filter(s -> s.getId().equals(sample.getSampleTypeId())).findFirst();
            List<DtoSampleType> finalParentSampleTypes = parentSampleTypes;
            sampleTypeOptional.ifPresent(s -> {
                sample.setSampleTypeName(s.getTypeName());
                Optional<DtoSampleType> parentSampleOptional = finalParentSampleTypes.stream()
                        .filter(p -> p.getId().equals(s.getParentId())).findFirst();
                parentSampleOptional.ifPresent(p -> sample.setBigSampleTypeId(p.getId()));
            });

        }
    }

    @Override
    public void findSampleTestByPage(PageBean<DtoTest> pb, BaseCriteria criteria) {
        // 多表关联查询返回自定义字段
        pb.setEntityName("DtoAnalyseData a,DtoTest t");
        pb.setSelect("select t");
        comRepository.findByPage(pb, criteria);
        List<DtoTest> testList = pb.getData();
        if (StringUtil.isNotEmpty(testList)) {
            List<String> sampleTypeIds = testList.stream().map(DtoTest::getSampleTypeId).collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeRepository.findAll(sampleTypeIds);
            testList.forEach(t -> sampleTypeList.stream().filter(s -> s.getId().equals(t.getSampleTypeId())).findFirst().
                    ifPresent(s -> t.setSampleTypeName(s.getTypeName())));
        }
    }

    @Override
    public DtoSample findByCode(String code) {
        List<DtoSample> sampleList = repository.findByCode(code);
        if (StringUtil.isNotNull(sampleList) && sampleList.size() > 0) {
            return sampleList.get(0);
        }
        return null;
    }

    @Override
    public String createSpaceSampleCode(Date sampleDate, String sampleTypeId, Boolean isCreate) {
        return createSampleCode(UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY, sampleTypeId, UUIDHelper.GUID_EMPTY, sampleDate, isCreate,
                PrincipalContextUser.getPrincipal().getUserId(), null, null);
    }

    //#region 生成样品编号

    @Override
    public String createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   String associateSampleCode) {

        DtoSampleType dtoSampleType = sampleTypeService.findOne(sampleTypeId);

        DtoSampleFolder dtoSampleFolder = sampleFolderService.findOne(sampleFolderId);

        DtoSampleType dtoSampleTypeParent = sampleTypeService.findOne(dtoSampleType.getParentId());

        return createSampleCode(dtoProject, dtoProjectType,
                dtoSampleType, dtoSampleTypeParent, dtoSampleFolder, testPost, record, samplingTimeBegin,
                isCreate, currentUserId, isQC, associateSampleId, qcId, sampleCategory, associateSampleCode);
    }

    @Override
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          String sampleTypeId,
                                          String sampleFolderId,
                                          Date samplingTimeBegin,
                                          String samplingPersonId,
                                          String sampleId,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isQC,
                                          String associateSampleId,
                                          String qcId,
                                          Integer sampleCategory,
                                          Boolean isAutoCommitSN,
                                          String associateSampleCode) {

        DtoSampleType dtoSampleType = sampleTypeService.findOne(sampleTypeId);

        DtoSampleFolder dtoSampleFolder = sampleFolderService.findOne(sampleFolderId);

        DtoSampleType dtoSampleTypeParent = sampleTypeService.findOne(dtoSampleType.getParentId());

        return createSampleCode(dtoProject, dtoProjectType,
                dtoSampleType, dtoSampleTypeParent, dtoSampleFolder, null, null, samplingTimeBegin,
                isCreate, currentUserId, isQC, associateSampleId, qcId, sampleCategory, -1, -1, isAutoCommitSN, associateSampleCode);
    }

    @Override
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          String sampleTypeId,
                                          String sampleFolderId,
                                          Date samplingTimeBegin,
                                          String samplingPersonId,
                                          String sampleId,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isQC,
                                          String associateSampleId,
                                          String qcId,
                                          Integer sampleCategory,
                                          Integer qcType, Integer qcGrade,
                                          Boolean isAutoCommitSN,
                                          String associateSampleCode) {
        DtoSampleType dtoSampleType = sampleTypeService.findOne(sampleTypeId);
        DtoSampleFolder dtoSampleFolder = null;
        if (StringUtils.isNotNullAndEmpty(sampleFolderId) && !UUIDHelper.GUID_EMPTY.equals(sampleFolderId)) {
            dtoSampleFolder = sampleFolderService.findOne(sampleFolderId);
        }
        //根据采样人，获取岗位
        DtoTestPost testPost = null;
        if (StringUtils.isNotNullAndEmpty(samplingPersonId) && !UUIDHelper.GUID_EMPTY.equals(samplingPersonId)) {
            List<DtoTestPost2Person> testPost2PersonList = testPost2PersonRepository.findByPersonId(samplingPersonId);
            List<String> testPostIdList = testPost2PersonList.stream().map(DtoTestPost2Person::getTestPostId).distinct().collect(Collectors.toList());
            List<DtoTestPost> testPostList = StringUtil.isNotEmpty(testPostIdList) ? testPostRepository.findAll(testPostIdList) : new ArrayList<>();
            testPostList = testPostList.stream().filter(p -> EnumLIM.EnumPostType.现场.getValue().equals(p.getPostType())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(testPostList)) {
                testPost = testPostList.get(0);
            }
        }

        //根据原样获取送样单
        DtoReceiveSampleRecord record = null;
        if (StringUtils.isNotNullAndEmpty(sampleId) && !UUIDHelper.GUID_EMPTY.equals(sampleId)) {
            DtoSample sample = repository.findOne(sampleId);
            if (StringUtil.isNotNull(sample) && StringUtils.isNotNullAndEmpty(sample.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(sample.getReceiveId())) {
                record = receiveSampleRecordRepository.findOne(sample.getReceiveId());
            }
        }

        DtoSampleType dtoSampleTypeParent = sampleTypeService.findOne(dtoSampleType.getParentId());

        return createSampleCode(dtoProject, dtoProjectType,
                dtoSampleType, dtoSampleTypeParent, dtoSampleFolder, testPost, record, samplingTimeBegin,
                isCreate, currentUserId, isQC, associateSampleId, qcId, sampleCategory, qcType, qcGrade, isAutoCommitSN, associateSampleCode);
    }

    @Override
    public String createSampleCode(String projectId,
                                   String projectTypeId,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   String associateSampleCode,
                                   DtoTestPost testPost, DtoReceiveSampleRecord record) {

        DtoProject dtoProject = projectRepository.findOne(projectId);

        DtoProjectType dtoProjectType = projectTypeService.findOne(projectTypeId);

        return createSampleCode(dtoProject, dtoProjectType, testPost, record, sampleTypeId,
                sampleFolderId, samplingTimeBegin,
                isCreate, currentUserId,
                isQC, associateSampleId, qcId, sampleCategory, associateSampleCode);
    }

    @Override
    public String createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoSampleType dtoSampleType,
                                   DtoSampleType dtoSampleTypeParent,
                                   DtoSampleFolder dtoSampleFolder,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId,
                                   Boolean isQC,
                                   String associateSampleId,
                                   String qcId,
                                   Integer sampleCategory,
                                   String associateSampleCode) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                dtoSampleType,
                dtoSampleTypeParent,
                dtoSampleFolder,
                testPost,
                record,
                samplingTimeBegin,
                isCreate,
                currentUserId,
                isQC,
                associateSampleId,
                qcId,
                sampleCategory, -1, -1, true, associateSampleCode).getCode();
    }

    @Override
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          DtoSampleType dtoSampleType,
                                          DtoSampleType dtoSampleTypeParent,
                                          DtoSampleFolder dtoSampleFolder,
                                          DtoTestPost testPost,
                                          DtoReceiveSampleRecord record,
                                          Date samplingTimeBegin,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isQC,
                                          String associateSampleId,
                                          String qcId,
                                          Integer sampleCategory,
                                          Integer qcType, Integer qcGrade,
                                          Boolean isAutoCommitSN,
                                          String associateSampleCode) {
        return serialNumberService.createNewNumber(isAutoCommitSN, dtoProject,
                dtoProjectType,
                dtoSampleType,
                dtoSampleTypeParent,
                dtoSampleFolder,
                samplingTimeBegin,
                isCreate,
                currentUserId,
                isQC,
                associateSampleId,
                qcId,
                sampleCategory, qcType, qcGrade, associateSampleCode, testPost, record);
    }

    @Override
    public String createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoTestPost testPost,
                                   DtoReceiveSampleRecord record,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                testPost,
                record,
                sampleTypeId,
                sampleFolderId,
                samplingTimeBegin,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumSampleCategory.原样.getValue(), "");
    }

    @Override
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          String sampleTypeId,
                                          String sampleFolderId,
                                          Date samplingTimeBegin,
                                          String samplingPersonId,
                                          String sampleId,
                                          Boolean isCreate,
                                          String currentUserId, Boolean isAutoCommitSN) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                sampleTypeId,
                sampleFolderId,
                samplingTimeBegin,
                samplingPersonId,
                sampleId,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumSampleCategory.原样.getValue(), -1, -1, isAutoCommitSN, "");
    }

    @Override
    public String createSampleCode(String projectId,
                                   String projectTypeId,
                                   String sampleTypeId,
                                   String sampleFolderId,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId, DtoTestPost testPost, DtoReceiveSampleRecord record) {
        return createSampleCode(projectId,
                projectTypeId,
                sampleTypeId,
                sampleFolderId,
                samplingTimeBegin,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumSampleCategory.原样.getValue(), "", testPost, record);
    }

    @Override
    public String createSampleCode(DtoProject dtoProject,
                                   DtoProjectType dtoProjectType,
                                   DtoSampleType dtoSampleType,
                                   DtoSampleType dtoSampleTypeParent,
                                   DtoSampleFolder dtoSampleFolder,
                                   Date samplingTimeBegin,
                                   Boolean isCreate,
                                   String currentUserId) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                dtoSampleType,
                dtoSampleTypeParent,
                dtoSampleFolder,
                null,
                null,
                samplingTimeBegin,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumSampleCategory.原样.getValue(), "");
    }


    @Override
    public DtoGenerateSN createSampleCode(DtoProject dtoProject,
                                          DtoProjectType dtoProjectType,
                                          DtoSampleType dtoSampleType,
                                          DtoSampleType dtoSampleTypeParent,
                                          DtoSampleFolder dtoSampleFolder,
                                          Date samplingTimeBegin,
                                          Boolean isCreate,
                                          String currentUserId,
                                          Boolean isAutoCommitSN) {
        return createSampleCode(dtoProject,
                dtoProjectType,
                dtoSampleType,
                dtoSampleTypeParent,
                dtoSampleFolder,
                null,
                null,
                samplingTimeBegin,
                isCreate,
                currentUserId, false,
                UUIDHelper.GUID_EMPTY,
                UUIDHelper.GUID_EMPTY,
                EnumSampleCategory.原样.getValue(), -1, -1, isAutoCommitSN, "");
    }

    @Override
    public String createInnerSampleCode(Integer sampleCategory, Integer qcGrade, Integer qcType, String associateSampleId,
                                        String workSheetFolderId, String standardSampleCode) {
        DtoWorkSheetFolder dtoWorkSheetFolder = workSheetFolderService.findOne(workSheetFolderId);
        DtoSample associateSample = super.findOne(associateSampleId);
        return createInnerSampleCode(sampleCategory, qcGrade, qcType, associateSample, dtoWorkSheetFolder, standardSampleCode);
    }

    @Override
    public String createInnerSampleCode(Integer sampleCategory, Integer qcGrade, Integer qcType, DtoSample associateSample,
                                        String workSheetFolderId, String standardSampleCode) {
        DtoWorkSheetFolder dtoWorkSheetFolder = workSheetFolderService.findOne(workSheetFolderId);
        return createInnerSampleCode(sampleCategory, qcGrade, qcType, associateSample, dtoWorkSheetFolder, standardSampleCode);
    }

    @Override
    public String createInnerSampleCode(Integer sampleCategory, Integer qcGrade, Integer qcType,
                                        String associateSampleId, DtoWorkSheetFolder dtoWorkSheetFolder, String standardSampleCode) {
        DtoSample associateSample = super.findOne(associateSampleId);
        return createInnerSampleCode(sampleCategory, qcGrade, qcType, associateSample, dtoWorkSheetFolder, standardSampleCode);
    }

    @Override
    public String createInnerSampleCode(Integer sampleCategory, Integer qcGrade, Integer qcType,
                                        DtoSample associateSample, DtoWorkSheetFolder dtoWorkSheetFolder, String standardSampleCode) {
        return innerSampleSerialNumberService.createNewNumber(sampleCategory, qcGrade, qcType, associateSample, dtoWorkSheetFolder, standardSampleCode);
    }

    //#endregion

    /**
     * 删除质控数据
     *
     * @param samples   勾选的待删除样品
     * @param qcSamples 待选样品的质控样
     */
    private void deleteQCData(List<DtoSample> samples, List<DtoSample> qcSamples) {
        //关联样的质控id集合
        List<String> qcIds = qcSamples.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getQcId())).map(DtoSample::getQcId).distinct().collect(Collectors.toList());
        //本身勾选删除中的关联样的质控id集合，并合并在一起，获取质控数据
        List<String> removeQcIds = samples.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getQcId())).map(DtoSample::getQcId).distinct().collect(Collectors.toList());
        qcIds.addAll(removeQcIds);
        List<DtoQualityControl> qcList = qcIds.size() > 0 ? qualityControlRepository.findAll(qcIds) : new ArrayList<>();
        //筛选出待删除的质控数据及关联样中非空白非标准的质控数据
        qcList = qcList.stream().filter(p -> !(p.getQcType().equals(new QualityBlank().qcTypeValue())
                || p.getQcType().equals(new QualityStandard().qcTypeValue()))
                || removeQcIds.contains(p.getId())).collect(Collectors.toList());
        if (qcList.size() > 0) {
            qualityControlService.logicDeleteById(qcList.stream().map(DtoQualityControl::getId).collect(Collectors.toList()));
        }
        List<String> finalQcIds = qcList.stream().map(DtoQualityControl::getId).collect(Collectors.toList());
        qcSamples = qcSamples.stream().filter(p -> finalQcIds.contains(p.getQcId()) || !p.getSampleCategory().equals(EnumSampleCategory.质控样.getValue())).collect(Collectors.toList());
        samples.addAll(qcSamples);
    }

    //#region 采样准备

    /**
     * 采样准备获取点位信息
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param key          关键字
     * @return 点位信息
     */
    @Override
    public DtoPrepareFolder findPrepareFolder(String projectId, String sampleTypeId, String key, Integer status) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("sampleTypeId", sampleTypeId);
        params.put("key", key);
        params.put("status", status);
        return findPrepareFolderCommon(params);
    }

    @Override
    public DtoPrepareFolder findPrepareFolderV2(Map<String, Object> params) {
        return findPrepareFolderCommon(params);
    }

    /**
     * 采样准备树形排序方法
     *
     * @param samplingFrequencies 需要进行排序的list
     */
    @Override
    public void sortPrepareFolder(List<DtoSamplingFrequency> samplingFrequencies) {
        Collator collator = Collator.getInstance();
        List<String> sampleTypeIds = new ArrayList<>();
        List<String> sampleFolderIds = new ArrayList<>();
        for (DtoSamplingFrequency samplingFrequency : samplingFrequencies) {
            sampleTypeIds.add(samplingFrequency.getSampleTypeId());
            sampleFolderIds.add(samplingFrequency.getSampleFolderId());
        }

        if (StringUtil.isNotEmpty(sampleTypeIds)) {
            //填充大类
            List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll(sampleTypeIds);
            for (DtoSampleType sampleType : sampleTypes) {
                for (DtoSamplingFrequency samplingFrequency : samplingFrequencies) {
                    if (samplingFrequency.getSampleTypeId().equals(sampleType.getId())) {
                        samplingFrequency.setBigSampleTypeId(sampleType.getParentId());
                    }
                }
            }
        }

        if (StringUtil.isNotEmpty(sampleFolderIds)) {
            //填充类型点位排序值
            List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findAll(sampleFolderIds);
            List<String> fixedIds = sampleFolders.parallelStream().map(DtoSampleFolder::getFixedPointId).collect(Collectors.toList());
            List<DtoFixedpoint> fixedpoints = fixedpointRepository.findAll(fixedIds);
            for (DtoSampleFolder sampleFolder : sampleFolders) {
                for (DtoSamplingFrequency samplingFrequency : samplingFrequencies) {
                    if (sampleFolder.getId().equals(samplingFrequency.getSampleFolderId())) {
                        Optional<DtoFixedpoint> fixedpointOptional = fixedpoints.parallelStream()
                                .filter(f -> f.getId().equals(sampleFolder.getFixedPointId())).findFirst();
                        fixedpointOptional.ifPresent(f -> samplingFrequency.setFixedPointOrderNum(f.getOrderNum()));
                    }
                }
            }
        }
        Comparator<DtoSamplingFrequency> watchSpot = getComparator(samplingFrequencies);
        //排序规则：周期-》样品大类-》样品小类-》例行点位排序值-》点位名称
        samplingFrequencies.sort(Comparator.comparing(DtoSamplingFrequency::getSamplePerTime)
                .thenComparing(DtoSamplingFrequency::getBigSampleTypeId)
                .thenComparing(DtoSamplingFrequency::getSampleTypeName, collator)
                .thenComparing(DtoSamplingFrequency::getFixedPointOrderNum, Comparator.reverseOrder())
                .thenComparing(watchSpot));
    }

    /**
     * 定义比较器
     *
     * @param folderTemp 排序的Map
     * @return 比较器
     */
    private Comparator<DtoSamplingFrequency> getComparator(List<DtoSamplingFrequency> folderTemp) {
        return (a, b) -> SortUtil.compareString(a.getWatchSpot(), b.getWatchSpot());
    }

    /**
     * 采样准备创建样品编号
     *
     * @param projectId        项目id
     * @param ids              样品ids
     * @param samplingTime     采样时间
     * @param samplingPersonId 采样负责人id
     * @param samplingPerson   采样负责人名称
     * @param samplingPersons  采样人员
     */
    @Override
    @Transactional
    public List<DtoSample> createSampleCode(String projectId, List<String> ids, Date samplingTime, String samplingPersonId, String samplingPerson, List<DtoSamplingPersonConfig> samplingPersons) {
        ids.forEach(p -> log.info("===========================创建样品编号时前端传递的样品id: " + p + "============================"));
        DtoProject project = projectRepository.findOne(projectId);
        List<DtoSample> sampleList = new ArrayList<>();
        createSampleCode(project, ids, samplingTime, samplingPersonId, samplingPerson, samplingPersons, sampleList);
        comRepository.clear();
        //现场任务和样品交接并行，不卡先后顺序
        flowToReceiveSample(ids, sampleList);
        if (sampleList.size() > 0) {
            //插入样品生成编号日志
            this.createPrepareLogs(sampleList);
            //核对采样准备样品状态
            proService.checkPrepareSample(project, sampleList);
            //比对生成评价数据
            doSaveCompareJudgeData(sampleList, project);
        }
        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId(),
                "",
                EnumLIM.EnumHomeTaskModule.现场任务.getValue()
        );

        return sampleList;
    }

    /**
     * 采样准备获取点位信息
     *
     * @param params 参数
     * @return 点位信息
     */
    private DtoPrepareFolder findPrepareFolderCommon(Map<String, Object> params) {
        DtoPrepareFolder prepareFolder = new DtoPrepareFolder();
        List<Object> resList = samplingFrequencyService.findPrepareSamplingFrequencyV2(params);
        List<DtoSamplingFrequency> frequencyList = (List<DtoSamplingFrequency>) resList.get(0);
        List<DtoSample> samples = (List<DtoSample>) resList.get(1);
        samples = samples.stream().filter(s -> !EnumSampleCategory.比对评价样.getValue().equals(s.getSampleCategory())).collect(Collectors.toList());
        this.sortPrepareFolder(frequencyList);
        prepareFolder.setSamplingFrequency(frequencyList);
        List<String> sampleFolderIds = frequencyList.stream().map(DtoSamplingFrequency::getSampleFolderId).collect(Collectors.toList());
        //找到所有数据，判断样品是否都是分包数据
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        if (samples.size() > 0) {
            List<String> samIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
            analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(samIds);
        }
        if (sampleFolderIds.size() > 0) {
            Map<String, String> samTypeMap = frequencyList.stream().collect(Collectors.groupingBy(DtoSamplingFrequency::getSampleTypeId,
                    Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getSampleTypeName())));
            for (DtoSample sample : samples) {
                sample.setSampleTypeName(samTypeMap.getOrDefault(sample.getSampleTypeId(), ""));
                sample.setTimesName(String.format("第%s批次", MathUtil.toChinese(sample.getTimesOrder())));
                sample.setSampleTimesName(String.format("第%s次", MathUtil.toChinese(sample.getSampleOrder())));
                if (!sample.getIsDeleted() && analyseDataList.stream().noneMatch(p -> sample.getId().equals(p.getSampleId()) && !p.getIsOutsourcing())) {
                    sample.setRedFolderName(String.format("%s【采测分包】", sample.getRedFolderName()));
                    sample.setSampleTimesName(String.format("%s【采测分包】", sample.getSampleOrder()));
                }
            }
            samples.sort(Comparator.comparing(DtoSample::getTimesOrder).thenComparing(DtoSample::getSampleOrder));
            prepareFolder.setSample(samples);
        }
        return prepareFolder;
    }


    /**
     * 样品编号创建后，如有实验室指标则同时流转到样品交接(需要根据配置开关决定)
     *
     * @param sampleIds 样品id列表
     */
    private void flowToReceiveSample(List<String> sampleIds, List<DtoSample> instSampleList) {
        DtoCode code = codeService.findByCode(ProCodeHelper.LIM_FLOW_RECEIVE_SAMPLE);
        if (code != null && "1".equals(code.getDictValue())) {
            //获取样品相关的数据
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
            //找出非现场和非分包的数据
            analyseDataList = analyseDataList.parallelStream().filter(p -> !p.getIsCompleteField()
                    && !p.getIsSamplingOut() && !p.getIsOutsourcing()).collect(Collectors.toList());
            //对实验室的数据进行处理，让其相关的送样单流转到样品交接
            if (StringUtil.isNotEmpty(analyseDataList)) {
                sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());

                Map<String, String> smpId2ReceiveIdMap = new HashMap<>();
                for (DtoSample sample : instSampleList) {
                    if (StringUtil.isNotNull(sample.getReceiveId())) {
                        smpId2ReceiveIdMap.put(sample.getId(), sample.getReceiveId());
                    }
                }

                List<DtoSample> sampleList = repository.findByIds(sampleIds);
                //获取样品相关的送样单id
                List<String> receiveIds = new ArrayList<>();
                for (DtoSample sample : sampleList) {
                    String loopReceiveId = sample.getReceiveId();
                    if ((StringUtil.isEmpty(loopReceiveId) || UUIDHelper.GUID_EMPTY.equals(loopReceiveId))
                            && smpId2ReceiveIdMap.containsKey(sample.getId())) {
                        loopReceiveId = smpId2ReceiveIdMap.get(sample.getId());
                    }
                    if (!receiveIds.contains(loopReceiveId)) {
                        receiveIds.add(loopReceiveId);
                    }
                }

                //获取相关送样单
                List<DtoReceiveSampleRecord> receiveSampleRecordList = receiveSampleRecordRepository.findAll(receiveIds);
                //流转到样品交接
                for (DtoReceiveSampleRecord dtoReceiveSampleRecord : receiveSampleRecordList) {
                    if (EnumReceiveType.内部送样.getValue().equals(dtoReceiveSampleRecord.getReceiveType())) {
                        DtoStatusForRecord record = statusForRecordRepository.findByReceiveIdAndModule(dtoReceiveSampleRecord.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                        if (!StringUtil.isNotNull(record)) {
                            statusForRecordService.createStatus(dtoReceiveSampleRecord.getId(), EnumLIM.EnumReceiveRecordModule.样品交接.getValue());
                        }
                    }
                }
            }
        }
    }


    private void createSampleCode(DtoProject project, List<String> ids, Date samplingTime, String samplingPersonId,
                                  String samplingPerson, List<DtoSamplingPersonConfig> samplingPersons,
                                  List<DtoSample> sampleList) {
        List<DtoSample> samples = repository.findAll(ids);
        //需要排除已经生成过样品编号的数据
        samples = samples.stream().filter(p -> !StringUtils.isNotNullAndEmpty(p.getCode()) && p.getReceiveId().equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList());
        List<String> qcIds = samples.stream().map(DtoSample::getQcId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p))
                .collect(Collectors.toList());
        List<DtoQualityControl> qcList = qualityControlRepository.findAll(qcIds);
        samples.forEach(p -> {
            Optional<DtoQualityControl> qc = qcList.stream().filter(q -> p.getQcId().equals(q.getId())).findFirst();
            qc.ifPresent(q -> {
                p.setQcGrade(q.getQcGrade());
                p.setQcType(q.getQcType());
            });
        });
        DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());
        Map<String, List<DtoSample>> sampleMap = samples.stream().collect(Collectors.groupingBy(DtoSample::getSampleTypeId));
        //需要新增的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //需要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();

        //存放样品编号和样品编号生成时相关流水号信息
        List<DtoBusinessSerialNumber> businessSerialNumberList = new ArrayList<>();

        for (String sampleTypeId : sampleMap.keySet()) {
            List<DtoSample> samList = sampleMap.get(sampleTypeId);
            log.info("===========排序前: ");
            samList = this.sortPrepareSample(samList, true);
            if (samList.size() > 0) {
                log.info("===========排序后: ");
                DtoReceiveSampleRecord record = receiveSampleRecordService.createReceiveRecord(project, samList, samplingTime, samplingPersonId, samplingPerson, samplingPersons.stream().map(DtoSamplingPersonConfig::getSamplingPersonId).collect(Collectors.toList()));
                //存储生成的样品编号列表，用于校验编号重复
                List<String> codeList = new ArrayList<>();
                Map<String, String> codeMap = new HashMap<>();
                for (DtoSample sample : samList) {
                    log.info("===========样品编号: " + sample.getCode() + "; 检测类型id: " + sample.getSampleTypeId() + "================");
                    String sampleCode = "";
                    DtoGenerateSN targetGenerateSN = new DtoGenerateSN();
                    //设置送样单号
                    sample.setReceiveId(record.getId());
                    //如果编号重复，跳过当前
                    while (repository.countByCode(sampleCode) > 0 || codeList.contains(sampleCode)) {
                        targetGenerateSN = genSampleCode(record, sample, project, projectType, sampleTypeId, samplingTime, samplingPersonId, codeMap);
                        sampleCode = targetGenerateSN.getCode();
                    }

                    codeMap.put(sample.getId(), targetGenerateSN.getCode());
                    codeList.add(targetGenerateSN.getCode());

                    sample.setCode(targetGenerateSN.getCode());
                    sample.setSamplingStatus(EnumSamplingStatus.采样中.getValue());
                    sample.setSamplingTimeBegin(samplingTime);
                    sample.setSamplingTimeEnd(samplingTime);
                    sample.setSamplingPersonId(samplingPersonId);
                    sample.setSortNum(getSortOrder(sample, getQualityList()));
                    sampleList.add(sample);

                    DtoBusinessSerialNumber dtoBusinessSerialNumber = new DtoBusinessSerialNumber();
                    dtoBusinessSerialNumber.setBusinessType(EnumLogObjectType.样品.name());
                    dtoBusinessSerialNumber.setBusinessId(sample.getId());
                    dtoBusinessSerialNumber.setBusinessNumber(sample.getCode());
                    dtoBusinessSerialNumber.setSerialNumberType(targetGenerateSN.getCurrentSerialNumberType());
                    dtoBusinessSerialNumber.setPara0(targetGenerateSN.getCurrentPara0());
                    dtoBusinessSerialNumber.setPara1(targetGenerateSN.getCurrentPara1());
                    dtoBusinessSerialNumber.setPara2(targetGenerateSN.getCurrentPara2());
                    dtoBusinessSerialNumber.setPara3(targetGenerateSN.getCurrentPara3());
                    businessSerialNumberList.add(dtoBusinessSerialNumber);
                }
            }
        }
        if (sampleList.size() > 0) {
//            comRepository.updateBatch(sampleList);
            repository.save(sampleList);
        }
        if (serialNumberConfigCreateList.size() > 0) {
            comRepository.insert(serialNumberConfigCreateList);
        }
        if (serialNumberConfigUpdateList.size() > 0) {
//            comRepository.updateBatch(serialNumberConfigUpdateList);
            serialNumberConfigRepository.save(serialNumberConfigCreateList);
        }

        if (StringUtil.isNotEmpty(businessSerialNumberList)) {
            businessSerialNumberService.save(businessSerialNumberList);
        }
    }

    /**
     * 插入样品生成编号日志
     *
     * @param sampleList 样品
     */
    private void createPrepareLogs(List<DtoSample> sampleList) {
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sample : sampleList) {
            String watchSpot = sample.getRedFolderName();
            DtoSampleType samType = sampleTypeService.findOne(sample.getSampleTypeId());
            String sampleTypeName = StringUtil.isNotNull(samType) ? samType.getTypeName() : "";
            String comment = String.format("样品%s(%s)生成了样品编号%s", watchSpot, sampleTypeName, sample.getCode());

            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.修改样品.toString());
            log.setLogType(EnumLogType.样品信息.getValue());
            log.setObjectId(sample.getId());
            log.setObjectType(EnumLogObjectType.样品.getValue());
            log.setComment(comment);
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");

            logList.add(log);
        }
        if (logList.size() > 0) {
            newLogService.createLog(logList, EnumLogType.样品信息.getValue());
        }
    }

    /**
     * 采样准备清除样品编号
     *
     * @param ids 样品id集合
     */
    @Transactional
    @Override
    public void clearSampleCode(List<String> ids) {
        clearCodeBackRecordIds(ids);
    }

    /**
     * 采样准备清除样品编号
     *
     * @param ids 样品id集合
     */
    @Transactional
    @Override
    public List<String> clearCodeBackRecordIds(List<String> ids) {
        //修改样品状态
        List<DtoSample> samples = repository.findAll(ids);
        List<String> oldIds = new ArrayList<>(ids);
        //纠正送样单状态
        List<String> receiveIds = samples.stream().map(DtoSample::getReceiveId).filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY)).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> recList = receiveSampleRecordRepository.findAll(receiveIds);
        if (recList.stream().anyMatch(p -> !EnumLIM.EnumReceiveRecordStatus.新建.getValue().equals(p.getReceiveStatus()))) {
            throw new BaseException("样品已经送样，无法清空样品编号！");
        }
        sampleJudgeDataService.deleteJudgeDataByTest(ids, new ArrayList<>(), Boolean.TRUE);
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(ids);
        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        for (DtoAnalyseData analyseData : analyseDataList) {
            //BUG2024011799427 原始记录单】原始记录单可以获取到被删除、作废、清空样品编号的原样的全程序空白样
            // （清除原样样品编号时，不删除关联的全程序空白样的分析数据状态，此时全程序空白样得以留在检测单中）
            if ((EnumLIM.EnumQCType.空白.getValue().equals(analyseData.getQcType()) && EnumLIM.EnumQCGrade.外部质控.getValue().equals(analyseData.getQcGrade()))
                    && !oldIds.contains(analyseData.getSampleId())) {
                continue;
            }
            DtoAnalyseData anaData = new DtoAnalyseData();
            anaData.setId(analyseData.getId());
            anaData.setWorkSheetId(UUIDHelper.GUID_EMPTY);
            anaData.setWorkSheetFolderId(UUIDHelper.GUID_EMPTY);
            anaData.setDataStatus(EnumAnalyseDataStatus.未测.getValue());
            anaData.setIsDataEnabled(false);
            anaData.setModifyDate(new Date());
            anaData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            anaDatas.add(anaData);
        }
//        comRepository.updateBatch(anaDatas);
        analyseDataRepository.save(anaDatas);

        //删除样品分组
        List<DtoSampleGroup> sampleGroupList = sampleGroupService.findBySampleIds(ids);
        sampleGroupService.delete(sampleGroupList);

        // 删除对应的领样单中的样品
        List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2Samples = receiveSubSampleRecord2SampleRepository.findBySampleIdIn(oldIds);
        receiveSubSampleRecord2SampleRepository.delete(receiveSubSampleRecord2Samples);

        List<DtoSample> sampleDatas = new ArrayList<>();
        for (DtoSample sample : samples) {
            DtoSample sampleData = new DtoSample();
            sampleData.setId(sample.getId());
            sampleData.setCode("");
            if (!sample.getIsDeleted()) {
                sampleData.setSamplingStatus(EnumSamplingStatus.需要取样还未取样.getValue());
                sampleData.setInnerReceiveStatus(EnumInnerReceiveStatus.不能领取.getValue());
                sampleData.setAnanlyzeStatus(EnumAnalyzeStatus.不能分析.getValue());
                sampleData.setReceiveId(UUIDHelper.GUID_EMPTY);
                sampleData.setStatus(EnumSampleStatus.样品未采样.toString());
            } else {
                sampleData.setSamplingStatus(sample.getSamplingStatus());
                sampleData.setInnerReceiveStatus(sample.getInnerReceiveStatus());
                sampleData.setAnanlyzeStatus(sample.getAnanlyzeStatus());
                sampleData.setReceiveId(sample.getReceiveId());
                sampleData.setStatus(sample.getStatus());
            }
            //保证作废的样品的isdeleted字段不被修改
            sampleData.setIsDeleted(sample.getIsDeleted());
            sampleData.setModifyDate(new Date());
            sampleData.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            sampleDatas.add(sampleData);
        }
//        comRepository.updateBatch(sampleDatas);
        repository.save(sampleDatas);

//        comRepository.clear();
        //清除样品编号的时候需要清除缓存
        String redisKey = "sampleCode:" + PrincipalContextUser.getPrincipal().getOrgId();
//        Object value = redisTemplate.opsForValue().get(redisKey);
//        if (StringUtils.isNotNullAndEmpty(value)) {
//            redisTemplate.delete(redisKey);
//        }

        businessSerialNumberService.clearBusinessSerialNumber(EnumLogObjectType.样品,
                sampleDatas.stream().map(DtoSample::getId).collect(Collectors.toList()), redisKey);

        //插入清除编号的日志
        this.createClearSampleLogs(samples);

        //mysql 执行check方法，由于updatebatch缓存数据还存在，纠正方法错误
        if (receiveIds.size() > 0) {
            //proService.checkReceiveSampleRecord(receiveIds);
        }
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        for (String receiveId : receiveIds) {
                            proService.sendProMessageWoTransactional(EnumProAction.清除样品编号, "", receiveId);
                        }
                        if (samples.stream().anyMatch(p -> !UUIDHelper.GUID_EMPTY.equals(p.getProjectId()))) {
                            String projectId = samples.stream().sorted(Comparator.comparing(DtoSample::getProjectId)
                                    .reversed()).collect(Collectors.toList()).get(0).getProjectId();
                            proService.sendProMessageWoTransactional(EnumProAction.清除样品编号, projectId, "",
                                    analyseDataList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()), null,
                                    analyseDataList.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList()));
                        }
                    }
                }
        );

//        for (String receiveId : receiveIds) {
//            proService.sendProMessageWoTransactional(EnumProAction.清除样品编号, "", receiveId);
//        }
//
//        if (samples.stream().anyMatch(p -> !UUIDHelper.GUID_EMPTY.equals(p.getProjectId()))) {
//            String projectId = samples.stream().sorted(Comparator.comparing(DtoSample::getProjectId).reversed()).collect(Collectors.toList()).get(0).getProjectId();
//            proService.sendProMessageWoTransactional(EnumProAction.清除样品编号, projectId, "", analyseDataList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()), null,
//                    analyseDataList.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList()));
//        }

        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getOrgId(),
                "",
                EnumLIM.EnumHomeTaskModule.现场任务.getValue()
        );
        return receiveIds;
    }

    /**
     * 样品作废
     *
     * @param ids    作废的样品ids
     * @param remark 作废的原因
     */
    @Transactional
    @Override
    public void invalidSamples(List<String> ids, String remark) {
        //需要作废的样品
        List<DtoSample> sampleList = super.findAll(ids);
        List<String> asSampleId = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        //获取关联的串联样和洗涤剂，样品作废时关联的的串联样和洗涤剂也要一起作废
        List<DtoSample> associateSampleList = repository.findByAssociateSampleIdIn(asSampleId);
        if (StringUtil.isNotEmpty(associateSampleList)) {
            associateSampleList = associateSampleList.parallelStream().filter(p -> EnumSampleCategory.串联样.getValue().equals(p.getSampleCategory()) ||
                    EnumSampleCategory.洗涤剂.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(associateSampleList)) {
                sampleList.addAll(associateSampleList);
            }
        }
        //作废样品只会有与原样有关的质控样
        List<DtoQualityControl> qcList = qualityControlRepository.findByAssociateSampleIdIn(ids);
        List<String> qcIds = qcList.stream().filter(p -> !(new QualityStandard().qcTypeValue()).equals(p.getQcType()) &&
                !(new QualityBlank().qcTypeValue()).equals(p.getQcType())).map(DtoQualityControl::getId).collect(Collectors.toList());
        //原样关联的质控样
        List<DtoSample> qcSamList = new ArrayList<>();
        if (StringUtil.isNotEmpty(qcIds)) {
            qcSamList = repository.findByQcIdIn(qcIds);
        }
        List<String> qcSamIds = qcSamList.stream().map(DtoSample::getId).collect(Collectors.toList());
        //对应选择样品的数据
        List<DtoAnalyseData> samDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(ids);
        //对应的质控数据
        List<DtoAnalyseData> qcSamDataList = new ArrayList<>();
        if (StringUtil.isNotEmpty(qcSamIds)) {
            qcSamDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(qcSamIds);
        }
        List<String> recIds = sampleList.stream().map(DtoSample::getReceiveId)
                .filter(receiveId -> !UUIDHelper.GUID_EMPTY.equals(receiveId)).collect(Collectors.toList());
        //删除的对应数据
        List<DtoAnalyseData> delAnaList = new ArrayList<>();
        //需要删除的样品
        List<DtoSample> delSamList = new ArrayList<>();
        //作废的样品是质控样，需要做删除
        List<DtoQualityControl> delQualitControlList = new ArrayList<>();
        for (DtoSample sample : sampleList) {
            List<DtoAnalyseData> anaList = samDataList.stream().filter(p -> p.getSampleId().equals(sample.getId())).collect(Collectors.toList());
            if (!sample.getQcId().equals(UUIDHelper.GUID_EMPTY)) {
                DtoQualityControl qualityControl = qualityControlRepository.findOne(sample.getQcId());
                delQualitControlList.add(qualityControl);
                //数据的处理（删除样品）
                updateSampleInfo(sample, true);
                for (DtoAnalyseData analyseData : anaList) {
                    updateAnalyseInfo(analyseData, true);
                    delAnaList.add(analyseData);
                }
            } else {
                //数据的处理（删除样品）
                updateSampleInfo(sample, false);
                for (DtoAnalyseData analyseData : anaList) {
                    updateAnalyseInfo(analyseData, false);
                    delAnaList.add(analyseData);
                }
            }
            //删除相关质控样--去掉空白样和标样
            List<DtoQualityControl> delQcList = qcList.stream().filter(p -> p.getAssociateSampleId()
                    .equals(sample.getId()) && !p.getAssociateSampleId().equals(UUIDHelper.GUID_EMPTY)
                    && !(new QualityStandard().qcTypeValue()).equals(p.getQcType())
                    && !(new QualityBlank().qcTypeValue()).equals(p.getQcType()))
                    .collect(Collectors.toList());

            if (StringUtil.isNotNull(delQcList)) {
                List<String> delQcIds = delQcList.stream().map(DtoQualityControl::getId).collect(Collectors.toList());
                //找到质控样
                List<DtoSample> delQcSamList = qcSamList.stream().filter(p -> delQcIds.contains(p.getQcId())).collect(Collectors.toList());
                //删除质控信息
                delQualitControlList.addAll(delQcList);
                List<String> delSamIds = delQcSamList.stream().map(DtoSample::getId).collect(Collectors.toList());
                List<DtoAnalyseData> delQcDataList = qcSamDataList.stream().filter(p -> delSamIds.contains(p.getSampleId())).collect(Collectors.toList());
                //删除质控样
                for (DtoSample qcSam : delQcSamList) {
                    updateSampleInfo(qcSam, true);
                    delSamList.add(qcSam);
                }
                for (DtoAnalyseData analyseData : delQcDataList) {
                    updateAnalyseInfo(analyseData, true);
                    delAnaList.add(analyseData);
                }
            }
            delSamList.add(sample);
        }
        //修改样品
        if (StringUtil.isNotEmpty(delSamList)) {
            super.update(delSamList);
        }
        //修改数据
        if (StringUtil.isNotEmpty(delAnaList)) {
            analyseDataService.update(delAnaList);
        }
        //删除质控
        if (StringUtil.isNotEmpty(delQualitControlList.stream().distinct().collect(Collectors.toList()))) {
            qualityControlService.logicDeleteById(delQualitControlList.stream().map(DtoQualityControl::getId).collect(Collectors.toList()));
        }
        //删除领样单和样品的关系
        List<String> allDelSamIds = delSamList.stream().map(DtoSample::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(allDelSamIds)) {
            receiveSubSampleRecord2SampleRepository.deleteBySampleIdIn(allDelSamIds, PrincipalContextUser.getPrincipal().getUserId(), new Date());
            //纠正样品状态
            proService.checkSample(allDelSamIds);
        }
        //纠正送样单状态
        if (StringUtil.isNotEmpty(recIds)) {
            proService.checkReceiveSampleRecord(recIds);
        }
        //样品作废日志
        newLogService.createSampleInvalidLog(ids, remark, "样品作废");

        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        if (StringUtil.isNotEmpty(delSamList)) {
                            if (delSamList.stream().anyMatch(p -> !UUIDHelper.GUID_EMPTY.equals(p.getProjectId()))) {
                                String projectId = delSamList.stream().sorted(Comparator.comparing(DtoSample::getProjectId).reversed()).collect(Collectors.toList()).get(0).getProjectId();
                                proService.sendProMessage(EnumProAction.作废样品, projectId, "", delAnaList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()), null,
                                        delAnaList.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList()));
                            }
                        }
                        if (StringUtil.isNotEmpty(recIds)) {
                            for (String receiveId : recIds) {
                                proService.sendProMessage(EnumProAction.作废样品, "", receiveId);
                            }
                        }
                        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                                PrincipalContextUser.getPrincipal().getOrgId(),
                                "",
                                EnumLIM.EnumHomeTaskModule.现场任务.getValue()
                        );
                    }
                }
        );
    }

    /**
     * 样品作废修改样品
     *
     * @param sample 样品
     * @param isQc   是否质控样
     */
    private void updateSampleInfo(DtoSample sample, Boolean isQc) {
        if (!isQc) {
            if (sample.getSamplingStatus().equals(EnumSamplingStatus.已经完成取样.getValue()) ||
                    sample.getSamplingStatus().equals(EnumSamplingStatus.采样中.getValue())) {
                sample.setSamplingStatus(EnumSamplingStatus.采样中.getValue());
            } else {
                sample.setSamplingStatus(EnumSamplingStatus.需要取样还未取样.getValue());
            }
            sample.setInnerReceiveStatus(EnumInnerReceiveStatus.不能领取.getValue());
            sample.setAnanlyzeStatus(EnumAnalyzeStatus.不需要分析.getValue());
            sample.setStatus(EnumSampleStatus.样品作废.toString());
            sample.setIsDeleted(true);
            sample.setReceiveId(UUIDHelper.GUID_EMPTY);
            sample.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            sample.setModifyDate(new Date());
        } else {
            sample.setIsDeleted(true);
            sample.setReceiveId(UUIDHelper.GUID_EMPTY);
        }
    }

    /**
     * 样品作废修改数据
     *
     * @param analyseData 数据
     * @param isQc        是否质控样
     */
    private void updateAnalyseInfo(DtoAnalyseData analyseData, Boolean isQc) {
        if (isQc) {
            analyseData.setWorkSheetId(UUIDHelper.GUID_EMPTY);
            analyseData.setIsDeleted(true);
        } else {
            analyseData.setWorkSheetId(UUIDHelper.GUID_EMPTY);
            analyseData.setIsDataEnabled(false);
            analyseData.setStatus(EnumAnalyseDataStatus.作废.name());
            analyseData.setDataStatus(EnumAnalyseDataStatus.作废.getValue());
            analyseData.setIsDeleted(true);
        }
    }

    /**
     * 取消作废
     *
     * @param ids    取消作废的样品ids
     * @param remark 取消作废的原因
     */
    @Transactional
    @Override
    public void invalidCancelSamples(List<String> ids, String remark) {
        //找到作废的样品
        List<DtoSample> sampleList = super.findAll(ids);
        List<DtoSample> cancelSampleList = sampleList.stream().filter(p -> EnumSampleStatus.样品作废.name().equals(p.getStatus())).collect(Collectors.toList());
        //作废的数据
        List<String> canSamIds = cancelSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List analyseDataList = analyseDataRepository.findAnalyseDataStatusAll(canSamIds);
        List<DtoSample> changeSamList = new ArrayList<>();
        List<DtoAnalyseData> changeAnaList = new ArrayList<>();
        List<String> changeAnaIds = new ArrayList<>();

        for (DtoSample sample : cancelSampleList) {
            sample.setStatus(EnumSampleStatus.样品未采样.name());
            sample.setAnanlyzeStatus(EnumAnalyzeStatus.不能分析.getValue());
            sample.setIsDeleted(false);
            sample.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            sample.setModifyDate(new Date());
            changeSamList.add(sample);
            List<String> canAnaIds = (List<String>) analyseDataList.stream().filter(p -> sample.getId().equals(((Object[]) p)[1]))
                    .map(p -> ((Object[]) p)[0].toString()).collect(Collectors.toList());
            changeAnaIds.addAll(canAnaIds);
        }
        changeAnaIds = changeAnaIds.stream().distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(changeAnaIds)) {
            //修改数据
            analyseDataRepository.updateDeleteAnalyseStatus(changeAnaIds);
            changeAnaList = analyseDataRepository.findAll(changeAnaIds);
        }
        if (StringUtil.isNotEmpty(changeSamList)) {
            //修改样品
            super.update(changeSamList);
            //纠正样品状态
            proService.checkSample(changeSamList.stream().map(DtoSample::getId).collect(Collectors.toList()));
            //取消作废日志
            newLogService.createSampleInvalidLog(ids, remark, "取消作废");
        }
        List<DtoAnalyseData> finalChangeAnaList = changeAnaList;
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        if (StringUtil.isNotEmpty(changeSamList)) {
                            if (changeSamList.stream().anyMatch(p -> !UUIDHelper.GUID_EMPTY.equals(p.getProjectId()))) {
                                String projectId = changeSamList.stream().sorted(Comparator.comparing(DtoSample::getProjectId).reversed()).collect(Collectors.toList()).get(0).getProjectId();
                                proService.sendProMessage(EnumProAction.作废样品, projectId, "", finalChangeAnaList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()), null,
                                        finalChangeAnaList.stream().map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList()));
                            }
                        }
                        homeService.clearProjectTask(PrincipalContextUser.getPrincipal().getUserId(),
                                PrincipalContextUser.getPrincipal().getOrgId(),
                                "",
                                EnumLIM.EnumHomeTaskModule.现场任务.getValue()
                        );
                    }
                }
        );
    }

    /**
     * 插入样品清除编号日志
     *
     * @param sampleList 样品
     */
    private void createClearSampleLogs(List<DtoSample> sampleList) {
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sample : sampleList) {
            String watchSpot = this.getSampleWatchSpot(sample);
            String comment = String.format("清除了样品%s样品编号", this.getSampleName(sample, watchSpot));
            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.修改样品.toString());
            log.setLogType(EnumLogType.样品信息.getValue());
            log.setObjectId(sample.getId());
            log.setObjectType(EnumLogObjectType.样品.getValue());
            log.setComment(comment);
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");

            logList.add(log);
        }
        if (logList.size() > 0) {
            newLogService.createLog(logList, EnumLogType.样品信息.getValue());
        }
    }
    //#endregion

    /**
     * 核对样品
     *
     * @param ids 样品id集合
     */
    @Override
    @Transactional
    public void check(List<String> ids) {
        List<DtoSample> samples = repository.findAll(ids);
        List<String> projectIds = samples.stream().map(DtoSample::getProjectId).distinct().collect(Collectors.toList());
        proService.checkSamples(samples);

        //保证事物提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {

                        for (String projectId : projectIds) {
                            proService.sendProMessage(EnumProAction.状态纠正, projectId);
                        }
                    }
                }
        );
    }

    /**
     * 初始化样品
     *
     * @param dtoOutSample 外部样品实体
     * @return 初始化的样品信息
     */
    @Override
    public DtoOutSample initOutSample(DtoOutSample dtoOutSample) {
        String receiveId = dtoOutSample.getReceiveId();
        String projectId = dtoOutSample.getProjectId();
        String sampleTypeId = dtoOutSample.getSampleTypeId();
        DtoOutSample dto = new DtoOutSample();
        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(receiveId);
        List<DtoSample> sampleList = repository.findByIsDeletedFalseAndReceiveIdAndSampleTypeId(receiveId, sampleTypeId);
        DtoSample sample = StringUtil.isNotEmpty(sampleList) ? sampleList.get(0) : null;
        List<DtoTest> testList = new ArrayList<>();
        List<String> testIdList = new ArrayList<>();
        if (StringUtil.isNotNull(sample)) {
            BeanUtils.copyProperties(sample, dto);
            dto.setRedFolderName("");
            dto.setBlindType(EnumSampleBlindType.非盲样.getValue());
            List<DtoAnalyseData> anaDataList = analyseDataRepository.findBySampleIdAndIsDeletedFalse(sample.getId());
            testIdList = anaDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            testList = testList.size() > 0 ? testService.findRedisByIds(testIdList) : new ArrayList<>();
        }
        dto.setCycleOrder(1);
        dto.setTimesOrder(1);
        dto.setSampleOrder(1);
//        Set<String> analyzeItemIds = testList.stream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toSet());
        List<DtoParamsConfig> paramsConfigs;
//        if (analyzeItemIds.size() == 0) {
//            paramsConfigs = paramsConfigService.findBySampleTypeId(sampleTypeId);
//        } else {
//            paramsConfigs = paramsConfigService.findBySampleTypeId(sampleTypeId, analyzeItemIds);
//        }
        paramsConfigs = paramsConfigService.findByTypeIdAndTestIds(sampleTypeId, testIdList).stream()
                .filter(p -> EnumLIM.EnumParamsType.样品参数.getValue().equals(p.getParamsType())).collect(Collectors.toList());

        for (DtoParamsConfig pc : paramsConfigs) {
            if (StringUtils.isNotNullAndEmpty(pc.getDefaultValue())) {
                pc.setParamsValue(pc.getDefaultValue());
            } else {
                pc.setParamsValue("");
            }
        }

        DtoProject project = projectRepository.findOne(projectId);
        dto.setInspectedEntId(project.getInspectedEntId());
        dto.setInspectedEnt(project.getInspectedEnt());

        Date samplingTime = StringUtil.isNotNull(dtoOutSample.getSamplingTimeBegin()) ? dtoOutSample.getSamplingTimeBegin() : record.getSamplingTime();
        if (EnumSampleBlindType.密码平行.getValue().equals(dtoOutSample.getBlindType()) || EnumSampleBlindType.密码加标.getValue().equals(dtoOutSample.getBlindType())) {
            DtoSample originSample = repository.findOne(dtoOutSample.getAssociateSampleId());
            samplingTime = originSample.getSamplingTimeBegin();
        }
        String sendId = StringUtil.isNotNull(record) ? record.getSenderId() : UUIDHelper.GUID_EMPTY;
        DtoTestPost testPost = findTestPostCode(sendId);
        dto.setCode(this.createSampleCode(projectId, project.getProjectTypeId(), sampleTypeId, StringUtil.isNotNull(sample)
                ? sample.getSampleFolderId() : UUIDHelper.GUID_EMPTY, samplingTime, false, getSamplingPerson(record), testPost, record));
        dto.setId("");
        dto.setReceiveId(record.getId());
        dto.setSamplingTimeBegin(samplingTime);
        dto.setProjectId(projectId);
        dto.setSampleTypeId(sampleTypeId);
        dto.setTest(testList);
        dto.setParamsData(paramsConfigs);
        return dto;
    }

    /**
     * 获取采样人负责人id，目前产品使用当前操作人，抽取该API为项目个性化，比如南通按照采样负责人流水
     *
     * @return 采样负责人id
     */
    protected String getSamplingPerson(DtoReceiveSampleRecord record) {
        return PrincipalContextUser.getPrincipal().getUserId();
    }

    private DtoTestPost findTestPostCode(String sendId) {
        DtoTestPost testPost = null;
        if (StringUtil.isNotEmpty(sendId) && !UUIDHelper.GUID_EMPTY.equals(sendId)) {
            List<DtoTestPost2Person> testPost2PersonList = testPost2PersonRepository.findByPersonId(sendId);
            List<String> testPostIdList = testPost2PersonList.stream().map(DtoTestPost2Person::getTestPostId).distinct().collect(Collectors.toList());
            List<DtoTestPost> testPostList = StringUtil.isNotEmpty(testPostIdList) ? testPostRepository.findAll(testPostIdList) : new ArrayList<>();
            testPostList = testPostList.stream().filter(p -> EnumLIM.EnumPostType.现场.getValue().equals(p.getPostType())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(testPostList)) {
                testPost = testPostList.get(0);
            }
        } else {
            //送样人id为空，则表示是送样类任务，岗位编号默认为 “S”
            testPost = new DtoTestPost();
            testPost.setPostCode("S");
        }
        return testPost;
    }


    @Override
    public DtoSample findOne(String id) {
        DtoSample sample = repository.findOne(id);
        DtoSample dto = new DtoSample();
        BeanUtils.copyProperties(sample, dto);
        if (dto.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
            dto.setRedFolderName(this.getSampleWatchSpot(dto));
        }
        DtoSampleType samType = sampleTypeService.findOne(dto.getSampleTypeId());
        dto.setSampleTypeName(samType.getTypeName());
        dto.setBigSampleTypeId(samType.getParentId());
        if (!UUIDHelper.GUID_EMPTY.equals(dto.getAssociateSampleId())) {
            DtoSample associateSample = repository.findOne(dto.getAssociateSampleId());
            dto.setAssociateSampleCode(associateSample.getCode());
        }
        return dto;
    }

    /**
     * 获取样品详情
     *
     * @param id 样品id
     * @return 样品详情
     */
    @Override
    public DtoOutSample findDetail(String id) {
        DtoOutSample outSample = new DtoOutSample();
        DtoSample sample = repository.findOne(id);
        //设置经度
        outSample.setLon(sample.getLon());
        //设置纬度
        outSample.setLat(sample.getLat());
        if (sample.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
            sample.setRedFolderName(this.getSampleWatchSpot(sample));
        }
        DtoSampleType samType = sampleTypeService.findOne(sample.getSampleTypeId());
        if (StringUtil.isNotNull(samType)) {
            sample.setSampleTypeName(samType.getTypeName());
        }
        BeanUtils.copyProperties(sample, outSample);
        List<DtoAnalyseData> anaDataList = analyseDataRepository.findBySampleIdAndIsDeletedFalse(id);
        Set<String> analyseItemIds = anaDataList.stream().map(DtoAnalyseData::getAnalyseItemId).collect(Collectors.toSet());
        List<DtoParamsConfig> paramsConfigs;

//        if (analyseItemIds.size() == 0) {
//            paramsConfigs = paramsConfigService.findBySampleTypeId(sample.getSampleTypeId());
//        } else {
//            paramsConfigs = paramsConfigService.findBySampleTypeId(sample.getSampleTypeId(), analyseItemIds);
//        }

        paramsConfigs = paramsConfigService.findByTypeIdAndTestIds(sample.getSampleTypeId(), anaDataList.stream()
                .map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList())).stream()
                .filter(p -> EnumLIM.EnumParamsType.样品参数.getValue().equals(p.getParamsType())).collect(Collectors.toList());

        List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectTypeAndObjectId(EnumParamsDataType.样品.getValue(), id);
        //将list转为map
//        Map<String, String> paramsDataMap = paramsDataList.stream().collect(Collectors.toMap(DtoParamsData::getParamsConfigId, pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : ""));
        Map<String, List<DtoParamsData>> configId2ParamsDataListMap = paramsDataList.stream().filter(p -> StringUtil.isNotEmpty(p.getGroupId())).collect(Collectors.groupingBy(DtoParamsData::getParamsConfigId));
        Map<String, String> paramsDataMap = new HashMap<>();
        for (Map.Entry<String, List<DtoParamsData>> entry : configId2ParamsDataListMap.entrySet()) {
            DtoParamsData fstParamsData = entry.getValue().get(0);
            String paramsVal = StringUtil.isNotEmpty(fstParamsData.getParamsValue()) ? fstParamsData.getParamsValue() : "";
            paramsDataMap.put(entry.getKey(), paramsVal);
        }
        List<DtoParamsData> paramsDatas = new ArrayList<>();
        for (DtoParamsConfig paramsConfig : paramsConfigs) {
            if (paramsDataMap.containsKey(paramsConfig.getId())) {
                paramsConfig.setParamsValue(paramsDataMap.get(paramsConfig.getId()));
            } else {//配置中有，数据库中没有的进行预先插入
                String paramsValue = StringUtils.isNotNullAndEmpty(paramsConfig.getParamsValue()) ? paramsConfig.getParamsValue() : "";
                DtoParamsData paramsData = new DtoParamsData();
                paramsData.setObjectId(sample.getId());
                paramsData.setObjectType(EnumParamsDataType.样品.getValue());
                paramsData.setParamsConfigId(paramsConfig.getId());
                paramsData.setParamsName(paramsConfig.getAlias());
                paramsData.setParamsValue(paramsValue);
                paramsData.setDimension(paramsConfig.getDimension());
                paramsData.setDimensionId(paramsConfig.getDimensionId());
                paramsData.setOrderNum(paramsConfig.getOrderNum());
                paramsData.setGroupId(UUIDHelper.GUID_EMPTY);
                paramsDatas.add(paramsData);
                paramsConfig.setParamsValue(paramsValue);
            }
        }
        outSample.setParamsData(paramsConfigs);
        if (!UUIDHelper.GUID_EMPTY.equals(sample.getReceiveId())) {
            DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(sample.getReceiveId());
            outSample.setRecordCode(record.getRecordCode());
            outSample.setSenderName(record.getSenderName());
            outSample.setSendTime(record.getSendTime());
            DtoProject project = projectRepository.findOne(record.getProjectId());
            outSample.setProjectCode(project.getProjectCode());
            outSample.setCustomerName(project.getCustomerName());
        }
        if (!UUIDHelper.GUID_EMPTY.equals(outSample.getParentSampleId())) {
            DtoSample parentSample = repository.findOne(outSample.getParentSampleId());
            outSample.setParentSampleCode(StringUtil.isNotNull(parentSample) ? parentSample.getCode() : "");
        }
        if (paramsDatas.size() > 0) {
            paramsDataService.saveAsync(paramsDatas);
        }
        return outSample;
    }

    /**
     * 获取样品信息
     *
     * @param receiveId      送样单id
     * @param sampleTypeId   检测类型id
     * @param sampleFolderId 点位id
     * @return 样品信息
     */
    @Override
    public DtoSampleInfo findFolderDetails(String receiveId, String sampleTypeId, String sampleFolderId, String samKey, String paramsKey, Integer sortType) {
        DtoSampleInfo info = new DtoSampleInfo();
        Boolean folderFlag = StringUtils.isNotNullAndEmpty(sampleFolderId) && !UUIDHelper.GUID_EMPTY.equals(sampleFolderId);

        //找到所有需要显示的样品
        List<DtoSample> sampleList = this.findSampleByReceiveIdAndSampleTypeIdAndSampleFolderId(receiveId, sampleTypeId, sampleFolderId, true);
        //通过样品编号和点位名称过滤样品
        if (StringUtil.isNotEmpty(samKey)) {
            sampleList = sampleList.stream().filter(p -> p.getCode().contains(samKey) || p.getRedFolderName().contains(samKey)).collect(Collectors.toList());
        }
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        if (sampleIds.size() == 0) {
            return info;
        }
        //质控样信息
        List<DtoQualityControl> qcList = qualityControlRepository.findAll(sampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getQcId())).map(DtoSample::getQcId).collect(Collectors.toList()));
        //样品排序
        sampleList = this.sortPrepareSample(sampleList, false);

        List<String> qcIds = sampleList.stream().map(DtoSample::getQcId).filter(q -> !UUIDHelper.GUID_EMPTY.equals(q) && StringUtils.isNotNullAndEmpty(q)).distinct().collect(Collectors.toList());
        List<DtoQualityControl> qualityControls = StringUtil.isNotEmpty(qcIds) ? qualityControlRepository.findAll(qcIds) : new ArrayList<>();
        Map<String, DtoQualityControl> qcMap = qualityControls.stream().collect(Collectors.toMap(DtoQualityControl::getId, dto -> dto));

        //获取例行任务点位扩展信息
        List<String> folderIdList = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSampleFolder> folderList = StringUtil.isNotEmpty(folderIdList) ? sampleFolderRepository.findAll(folderIdList) : new ArrayList<>();
        Map<String, DtoSampleFolder> folderMap = folderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
        List<String> pointIdList = folderList.stream().map(DtoSampleFolder::getFixedPointId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<DtoFixedpoint> fixedpointList = StringUtil.isNotEmpty(pointIdList) ? fixedpointRepository.findAll(pointIdList) : new ArrayList<>();
        List<DtoPointExtendData> pointExtendDataList = StringUtil.isNotEmpty(pointIdList) ? pointExtendDataRepository.findByFixedPointIdIn(pointIdList) : new ArrayList<>();
        Map<String, List<DtoPointExtendData>> pointExtendDataMap = pointExtendDataList.stream().collect(Collectors.groupingBy(DtoPointExtendData::getFixedPointId));

        //样品类型
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        Map<String, String> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));

        //测试项目
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        Map<String, List<String>> sampleDataMap = analyseDataList.stream().
                collect(Collectors.groupingBy(DtoAnalyseData::getSampleId, Collectors.collectingAndThen(Collectors.toList(), value -> value.stream().map(DtoAnalyseData::getAnalyseItemId).collect(Collectors.toList()))));

        Set<String> analyseItemIds = analyseDataList.stream().map(DtoAnalyseData::getAnalyseItemId).collect(Collectors.toSet());
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        //获取所有参数
//        List<DtoParamsConfig> paramsConfigs = analyseItemIds.size() > 0 ? paramsConfigService.findBySampleTypeId(sampleTypeId, analyseItemIds) : paramsConfigService.findBySampleTypeId(sampleTypeId);
        List<DtoParamsConfig> paramsConfigs = paramsConfigService.findByTypeIdAndTestIds(sampleTypeId, testIds);
        //参数名称过滤参数
        if (StringUtil.isNotEmpty(paramsKey)) {
            if (folderFlag) {
                paramsConfigs = paramsConfigs.stream().filter(p -> (p.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())
                        || p.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())) || p.getAlias().contains(paramsKey)).collect(Collectors.toList());
            } else {
                paramsConfigs = paramsConfigs.stream().filter(p -> !p.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())
                        || p.getAlias().contains(paramsKey)).collect(Collectors.toList());
            }
        }
        //参数排序
        if (paramsConfigs.size() > 0) {
            //按照排序值进行排序
            paramsConfigs = paramsConfigs.stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());
        }

        //参数数据结果
        List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectIdInAndObjectType(sampleIds, EnumParamsDataType.样品.getValue());
        Map<String, String> paramsDataMap = paramsDataList.stream().
                collect(Collectors.groupingBy(DtoParamsData::getParamsConfigId, Collectors.collectingAndThen(Collectors.toList(),
                        value -> ArrayUtil.topFrequent(value.stream().map(pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "").collect(Collectors.toList()).toArray(new String[value.size()])))));

        //样品id+参数id对应数据
        Map<String, String> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(),
                p.getGroupId()), pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "", (p1, p2) -> p1));

        List<DtoParamsData> paramsDatas = new ArrayList<>();
        //绑定参数数据
        for (DtoParamsConfig paramsConfig : paramsConfigs) {
            if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue()) || (
                    folderFlag && paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue()
                    ))) {
                paramsConfig.setParamsValue(paramsDataMap.getOrDefault(paramsConfig.getId(), paramsConfig.getDefaultValue()));
                for (DtoSample sample : sampleList) {
                    String key = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                    String paramsValue = StringUtils.isNotNullAndEmpty(paramsConfig.getParamsValue()) ? paramsConfig.getParamsValue() : "";
                    if (!allParamsDataMap.containsKey(key)) {
                        //例行任务点位时需要根据点位的扩展信息参数名称匹配参数，匹配成功则获取点位扩展信息上配置的参数值
                        String newParamsValue = resetParamsValueForPoint(paramsConfig, paramsValue, folderMap.get(sample.getSampleFolderId()), pointExtendDataMap, fixedpointList);
                        //配置中有，数据库中没有的进行预先插入
                        DtoParamsData paramsData = new DtoParamsData();
                        paramsData.setObjectId(sample.getId());
                        paramsData.setObjectType(EnumParamsDataType.样品.getValue());
                        paramsData.setParamsConfigId(paramsConfig.getId());
                        paramsData.setParamsName(paramsConfig.getAlias());
                        paramsData.setParamsValue(newParamsValue);
                        paramsData.setDimension(paramsConfig.getDimension());
                        paramsData.setDimensionId(paramsConfig.getDimensionId());
                        paramsData.setOrderNum(paramsConfig.getOrderNum());
                        paramsData.setGroupId(UUIDHelper.GUID_EMPTY);
                        paramsDatas.add(paramsData);
                    } else if (!allParamsDataMap.get(key).equals(paramsValue)) {
                        DtoQualityControl qc = qcMap.getOrDefault(sample.getQcId(), null);
                        Boolean flag = !(qc != null && EnumLIM.EnumQCGrade.外部质控.getValue().equals(qc.getQcGrade())
                                && (new QualityLocalBlank().qcTypeValue().equals(qc.getQcType()) || new QualityBlank().qcTypeValue().equals(qc.getQcType())
                                || new QualityInstrumentBlank().qcTypeValue().equals(qc.getQcType()) || new QualityTransportBlank().qcTypeValue().equals(qc.getQcType()))
                                && EnumLIM.EnumParamsType.点位参数.getValue().equals(paramsConfig.getParamsType()));
                        if (flag) {
                            Optional<DtoParamsData> pd = paramsDataList.stream().filter(p -> p.getObjectId().equals(sample.getId()) && p.getParamsConfigId().equals(paramsConfig.getId())).findFirst();
                            if (pd.isPresent()) {//TODO 测试这种情况下的save是否为更新
                                DtoParamsData paramsData = pd.get();
                                paramsData.setParamsValue(paramsValue);
                                paramsDatas.add(paramsData);
                            }
                        }
                    }
                }
            }
        }
        List<Map<String, Object>> sampleInfoList = new ArrayList<>();
        //找到样品类型
        List<String> bigTypeIds = samTypes.stream().map(DtoSampleType::getId).collect(Collectors.toList());
        List<DtoSampleType> bigTypes = sampleTypeService.findRedisByIds(bigTypeIds);
        //找到分组Ids
        List<String> groupIds = bigTypes.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getFieldTaskGroupId())
                && !p.getFieldTaskGroupId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType::getFieldTaskGroupId)
                .distinct().collect(Collectors.toList());
        List<DtoSampleTypeGroup> dtoSampleGroups = new ArrayList<>();
        List<DtoSampleTypeGroup2Test> group2TestList = new ArrayList<>();
        if (StringUtil.isNotEmpty(groupIds)) {
            dtoSampleGroups = sampleTypeGroupRepository.findByParentIdInAndGroupType(groupIds, EnumLIM.EnumGroupType.分组.getValue());
            if (StringUtil.isNotEmpty(dtoSampleGroups)) {
                group2TestList = sampleTypeGroup2TestService.findBySampleTypeGroupIds(dtoSampleGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList()));
            }
        }

        //样品参数和分析项目参数+点位参数
        List<DtoParamsConfig> configList = paramsConfigs.stream().filter(p -> !p.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())).collect(Collectors.toList());
        if (folderFlag) {
            configList = configList.stream().filter(p -> !p.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())).collect(Collectors.toList());
        }
        //循环样品
        for (DtoSample sample : sampleList) {
            //所有数据
            List<DtoAnalyseData> dataList = analyseDataList.stream().filter(p -> p.getSampleId().equals(sample.getId())).collect(Collectors.toList());
            //所有测试项目
            List<String> allTestIds = dataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
            Optional<DtoSampleType> typeOptional = samTypes.stream().filter(p -> p.getId().equals(sample.getSampleTypeId())).findFirst();
            List<DtoSampleTypeGroup> sampleTypeGroupList = new ArrayList<>();
            if (typeOptional.isPresent()) {
                Optional<DtoSampleType> bigType = bigTypes.stream().filter(p -> p.getId().equals(typeOptional.get().getId())).findFirst();
                if (bigType.isPresent()) {
                    sampleTypeGroupList = dtoSampleGroups.stream().filter(p -> p.getParentId().equals(bigType.get().getFieldTaskGroupId())).collect(Collectors.toList());
                }
            }
            DtoSampleTypeGroup samGroup = new DtoSampleTypeGroup();
            samGroup.setId(UUIDHelper.GUID_EMPTY);
            samGroup.setGroupName("/");
            //参数--排除点位参数和共参数
            for (DtoParamsConfig paramsConfig : configList) {
                //测试项目分组
                if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.样品参数.getValue()) ||
                        paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())) {
                    String redAnalyzeItems = sample.getRedAnalyzeItems();
                    if (StringUtil.isNotEmpty(redAnalyzeItems) && redAnalyzeItems.contains(",")) {
                        String[] array = redAnalyzeItems.split(",");
                        List<String> list = Arrays.asList(array);
                        list.sort(Comparator.comparing(p -> p));
                        redAnalyzeItems = String.join(",", list);
                    }
                    sampleInfoList.add(sampleParams(sample, samTypeMap, redAnalyzeItems, qcList, samGroup
                            , paramsConfig, allParamsDataMap));
                } else if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())) {
                    //现场任务分组
                    for (DtoSampleTypeGroup group : sampleTypeGroupList) {
                        List<String> groupTestIds = group2TestList.stream().filter(p -> p.getSampleTypeGroupId().equals(group.getId()))
                                .map(DtoSampleTypeGroup2Test::getTestId).collect(Collectors.toList());
                        //已有分组的测试项目去掉
                        allTestIds = allTestIds.stream().filter(p -> !groupTestIds.contains(p)).collect(Collectors.toList());
                        List<String> anaName = dataList.stream().filter(p -> groupTestIds.contains(p.getTestId()))
                                .map(DtoAnalyseData::getRedAnalyzeItemName).sorted(Comparator.comparing(p -> p))
                                .distinct().collect(Collectors.toList());
                        //存在分析项目才添加
                        if (anaName.size() > 0) {
                            sampleInfoList.add(sampleParams(sample, samTypeMap, String.join(",", anaName), qcList, group
                                    , paramsConfig, allParamsDataMap));
                        }
                    }
                    //如果分组完成之后还有多余的测试项目
                    if (allTestIds.size() > 0) {
                        List<String> finalAllTestIds = allTestIds;
                        List<String> anaName = dataList.stream().filter(p -> finalAllTestIds.contains(p.getTestId()))
                                .map(DtoAnalyseData::getRedAnalyzeItemName).sorted(Comparator.comparing(p -> p))
                                .distinct().collect(Collectors.toList());
                        sampleInfoList.add(sampleParams(sample, samTypeMap, String.join(",", anaName), qcList, samGroup
                                , paramsConfig, allParamsDataMap));
                    }
                }
            }
        }

        if (EnumSampleParamsSortType.按照样品编号排序.getValue().equals(sortType)) {
            sampleInfoList.sort(Comparator.comparing(SampleServiceImpl::comparingByCode)
                    .thenComparing(SampleServiceImpl::comparingByOrderNum, Comparator.reverseOrder())
                    .thenComparing(SampleServiceImpl::comparingByParamsName)
                    .thenComparing(SampleServiceImpl::comparingByGroupName));
        } else if (EnumSampleParamsSortType.按照参数排序.getValue().equals(sortType)) {
            sampleInfoList.sort(Comparator.comparing(SampleServiceImpl::comparingByOrderNum, Comparator.reverseOrder())
                    .thenComparing(SampleServiceImpl::comparingByParamsName)
                    .thenComparing(SampleServiceImpl::comparingByCode)
                    .thenComparing(SampleServiceImpl::comparingByGroupName));
        } else if (EnumSampleParamsSortType.按照分组排序.getValue().equals(sortType)) {
            sampleInfoList.sort(Comparator.comparing(SampleServiceImpl::comparingByGroupName)
                    .thenComparing(SampleServiceImpl::comparingByCode)
                    .thenComparing(SampleServiceImpl::comparingByOrderNum, Comparator.reverseOrder())
                    .thenComparing(SampleServiceImpl::comparingByParamsName));
        }

        info.setSample(sampleInfoList);
        info.setParamsConfig(paramsConfigs);
        if (paramsDatas.size() > 0) {
            paramsDataService.saveAsync(paramsDatas);
        }
        return info;
    }

    private static Integer comparingByOrderNum(Map<String, Object> map) {
        return (Integer) map.get("paramsOrderNumber");
    }

    private static String comparingByCode(Map<String, Object> map) {
        return (String) map.get("code");
    }

    private static String comparingByGroupName(Map<String, Object> map) {
        return (String) map.get("groupName");
    }

    private static String comparingByParamsName(Map<String, Object> map) {
        return (String) map.get("paramsName");
    }

    /**
     * 获取样品信息
     *
     * @param receiveId      送样单id
     * @param sampleTypeId   检测类型id
     * @param sampleFolderId 点位id
     * @return 样品信息
     */
    @Override
    public DtoSampleInfo findDetails(String receiveId, String sampleTypeId, String sampleFolderId) {
        return findDetails(receiveId, sampleTypeId, sampleFolderId, false);
    }

    /**
     * 获取样品信息
     *
     * @param receiveId      送样单id
     * @param sampleTypeId   检测类型id
     * @param sampleFolderId 点位id
     * @return 样品信息
     */
    @Override
    public DtoSampleInfo findDetails(String receiveId, String sampleTypeId, String sampleFolderId, Boolean isProject) {
        DtoSampleInfo info = new DtoSampleInfo();
        List<DtoSample> sampleList = this.findSampleByReceiveIdAndSampleTypeIdAndSampleFolderId(receiveId, sampleTypeId, sampleFolderId, true);
        sampleList = sampleList.stream().filter(s -> !EnumSampleCategory.比对评价样.getValue().equals(s.getSampleCategory())).collect(Collectors.toList());
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        if (sampleIds.size() == 0) {
            return info;
        }
        List<DtoQualityControl> qcList = qualityControlRepository.findAll(sampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getQcId())).map(DtoSample::getQcId).collect(Collectors.toList()));
        sampleList = this.sortSampleByReceiveDataAndCode(sampleList);

        //获取例行任务点位扩展信息
        List<String> folderIdList = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSampleFolder> folderList = StringUtil.isNotEmpty(folderIdList) ? sampleFolderRepository.findAll(folderIdList) : new ArrayList<>();
        Map<String, DtoSampleFolder> folderMap = folderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
        List<String> pointIdList = folderList.stream().map(DtoSampleFolder::getFixedPointId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<DtoFixedpoint> fixedpointList = StringUtil.isNotEmpty(pointIdList) ? fixedpointRepository.findAll(pointIdList) : new ArrayList<>();
        List<DtoPointExtendData> pointExtendDataList = StringUtil.isNotEmpty(pointIdList) ? pointExtendDataRepository.findByFixedPointIdIn(pointIdList) : new ArrayList<>();
        Map<String, List<DtoPointExtendData>> pointExtendDataMap = pointExtendDataList.stream().collect(Collectors.groupingBy(DtoPointExtendData::getFixedPointId));

        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        Map<String, String> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));

        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);

        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        //已经不存在分析项目参数
//        List<DtoParamsConfig> paramsConfigs = analyseItemIds.size() > 0 ? paramsConfigService.findBySampleTypeId(sampleTypeId, analyseItemIds) : paramsConfigService.findBySampleTypeId(sampleTypeId);
        List<DtoParamsConfig> paramsConfigs = paramsConfigService.findByTypeIdAndTestIds(sampleTypeId, testIds);
        if (paramsConfigs.size() > 0) {
            //按照排序值进行排序
            paramsConfigs = paramsConfigs.stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());
        }

        List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectIdInAndObjectType(sampleIds, EnumParamsDataType.样品.getValue());
        Map<String, String> paramsDataMap = paramsDataList.stream().
                collect(Collectors.groupingBy(DtoParamsData::getParamsConfigId, Collectors.collectingAndThen(Collectors.toList(),
                        value -> ArrayUtil.topFrequent(value.stream().map(pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "").collect(Collectors.toList()).toArray(new String[value.size()])))));

        Map<String, String> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(),
                p.getGroupId()), pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "", (p1, p2) -> p1));
        List<DtoParamsConfig> retConfigs = new ArrayList<>();
        List<DtoParamsData> paramsDatas = new ArrayList<>();
        for (DtoParamsConfig paramsConfig : paramsConfigs) {
            if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue()) || (
                    paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())) ||
                    paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.样品参数.getValue()) ||
                    paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())) {
                paramsConfig.setParamsValue(paramsDataMap.getOrDefault(paramsConfig.getId(), paramsConfig.getDefaultValue()));
                for (DtoSample sample : sampleList) {
                    String key = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                    String paramsValue = StringUtils.isNotNullAndEmpty(paramsConfig.getParamsValue()) ? paramsConfig.getParamsValue() : "";
                    if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.样品参数.getValue()) || paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())) {
                        paramsValue = allParamsDataMap.getOrDefault(key, "");
                    }
                    if (!allParamsDataMap.containsKey(key)) {
                        //例行任务点位时需要根据点位的扩展信息参数名称匹配参数，匹配成功则获取点位扩展信息上配置的参数值
                        String newParamsValue = resetParamsValueForPoint(paramsConfig, paramsValue, folderMap.get(sample.getSampleFolderId()), pointExtendDataMap, fixedpointList);
                        // 现场任务-样品信息中，全程序空白、运输空白、现场空白、设备空白，参数区域在新增后默认'/'
                        newParamsValue = resetParamsValueForQc(allParamsDataMap, key, newParamsValue, qcList, sample, paramsConfig);
                        //配置中有，数据库中没有的进行预先插入
                        DtoParamsData paramsData = new DtoParamsData();
                        paramsData.setObjectId(sample.getId());
                        paramsData.setObjectType(EnumParamsDataType.样品.getValue());
                        paramsData.setParamsConfigId(paramsConfig.getId());
                        paramsData.setParamsName(paramsConfig.getAlias());
                        paramsData.setParamsValue(newParamsValue);
                        paramsData.setDimension(paramsConfig.getDimension());
                        paramsData.setDimensionId(paramsConfig.getDimensionId());
                        paramsData.setOrderNum(paramsConfig.getOrderNum());
                        paramsData.setGroupId(UUIDHelper.GUID_EMPTY);
                        paramsDatas.add(paramsData);
                    } else if (!allParamsDataMap.get(key).equals(paramsValue) && !paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())) {
                        Optional<DtoParamsData> pd = paramsDataList.stream().filter(p -> p.getObjectId().equals(sample.getId()) && p.getParamsConfigId().equals(paramsConfig.getId())).findFirst();
                        if (pd.isPresent()) {//TODO 测试这种情况下的save是否为更新
                            DtoParamsData paramsData = pd.get();
                            paramsData.setParamsValue(paramsValue);
                            paramsDatas.add(paramsData);
                        }
                    }
                }
                if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())) {
                    if (!StringUtil.isNotEmpty(paramsConfig.getGroupId())) {
                        paramsConfig.setGroupId(UUIDHelper.GUID_EMPTY);
                    }
                    retConfigs.add(paramsConfig);
                }
            }
        }
        List<Map<String, Object>> sampleInfoList = new ArrayList<>();
        //样品类型
        List<String> bigTypeIds = samTypes.stream().map(DtoSampleType::getId).collect(Collectors.toList());
        List<DtoSampleType> bigTypes = sampleTypeService.findRedisByIds(bigTypeIds);
        //找到分组Ids
        List<String> groupIds = bigTypes.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getFieldTaskGroupId())
                && !p.getFieldTaskGroupId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType::getFieldTaskGroupId)
                .distinct().collect(Collectors.toList());
        List<DtoSampleTypeGroup> dtoSampleGroups = new ArrayList<>();
        List<DtoSampleTypeGroup2Test> group2TestList = new ArrayList<>();
        if (StringUtil.isNotEmpty(groupIds)) {
            dtoSampleGroups = sampleTypeGroupRepository.findByParentIdInAndGroupType(groupIds, EnumLIM.EnumGroupType.分组.getValue());
            if (StringUtil.isNotEmpty(dtoSampleGroups)) {
                group2TestList = sampleTypeGroup2TestService.
                        findBySampleTypeGroupIds(dtoSampleGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList()));
            }
        }

        //样品参数和分析项目参数+点位参数
        List<DtoParamsConfig> configList = paramsConfigs.stream().filter(p -> !p.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())).collect(Collectors.toList());
        if (isProject) {
            configList = configList.stream().filter(p -> !p.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())).collect(Collectors.toList());
        }
        configList = configList.stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum)).collect(Collectors.toList());
        this.checkAnalyseItemNamesOfSamples(sampleList);
        for (DtoSample sample : sampleList) {
            //所有数据
            List<DtoAnalyseData> dataList = analyseDataList.stream().filter(p -> p.getSampleId().equals(sample.getId())).collect(Collectors.toList());
            //所有测试项目
            List<String> allTestIds = dataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
            Optional<DtoSampleType> typeOptional = samTypes.stream().filter(p -> p.getId().equals(sample.getSampleTypeId())).findFirst();
            List<DtoSampleTypeGroup> sampleTypeGroupList = new ArrayList<>();
            if (typeOptional.isPresent()) {
                Optional<DtoSampleType> bigType = bigTypes.stream().filter(p -> p.getId().equals(typeOptional.get().getId())).findFirst();
                if (bigType.isPresent()) {
                    sampleTypeGroupList = dtoSampleGroups.stream().filter(p -> p.getParentId().equals(bigType.get().getFieldTaskGroupId())).collect(Collectors.toList());
                }
            }
            DtoSampleTypeGroup samGroup = new DtoSampleTypeGroup();
            samGroup.setId(UUIDHelper.GUID_EMPTY);
            samGroup.setGroupName("/");
            String redAnalyzeItems = sample.getRedAnalyzeItems();
            Map<String, Object> paramsValue = new HashMap<>();
            //参数--排除点位参数和共参数
            for (DtoParamsConfig paramsConfig : configList) {
                //测试项目分组
                String key = String.format("%s;%s", paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                //key = paramsConfig.getId();
                String valueKey = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.样品参数.getValue()) ||
                        paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())) {
                    paramsValue.put(key, allParamsDataMap.getOrDefault(valueKey, ""));
                    if ((retConfigs.size() > 0 && retConfigs.stream().noneMatch(p -> paramsConfig.getParamsName().equals(p.getParamsName())))
                            || retConfigs.size() == 0) {
                        retConfigs.add(paramsConfig);
                    }
                } else if (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())) {
                    //现场任务分组
                    String paramsName = paramsConfig.getParamsName();
                    String aliasName = paramsConfig.getAlias();
                    for (DtoSampleTypeGroup group : sampleTypeGroupList) {
                        List<String> groupTestIds = group2TestList.stream().filter(p -> p.getSampleTypeGroupId().equals(group.getId()))
                                .map(DtoSampleTypeGroup2Test::getTestId).collect(Collectors.toList());
                        //已有分组的测试项目去掉
                        allTestIds = allTestIds.stream().filter(p -> !groupTestIds.contains(p)).collect(Collectors.toList());
                        List<String> anaName = dataList.stream().filter(p -> groupTestIds.contains(p.getTestId()))
                                .map(DtoAnalyseData::getRedAnalyzeItemName).sorted(Comparator.comparing(p -> p))
                                .distinct().collect(Collectors.toList());
                        //存在分析项目才添加
                        if (anaName.size() > 0) {
                            String pName = String.format("%s-%s", group.getGroupName(), paramsName);
                            String aName = String.format("%s-%s", group.getGroupName(), aliasName);
                            DtoParamsConfig groupConfig = new DtoParamsConfig();
                            BeanUtils.copyProperties(paramsConfig, groupConfig);
                            groupConfig.setParamsName(pName);
                            groupConfig.setAlias(aName);
                            groupConfig.setGroupId(group.getId());
                            key = String.format("%s;%s", paramsConfig.getId(), group.getId());
                            //key = paramsConfig.getId();
                            valueKey = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), group.getId());
                            String value = allParamsDataMap.getOrDefault(valueKey, paramsConfig.getDefaultValue());
                            //参数体积类型，需要获取分组配置上的内容
                            if ((aliasName.equals("体积类型") || paramsName.equals("体积类型")) && !StringUtil.isNotEmpty(value)) {
                                value = group.getVolumeType();
                                //需要保存体积类型的数据
                                saveParamsDataVolume(sample, groupConfig, value, group.getId());
                                allParamsDataMap.put(valueKey, StringUtil.isNotEmpty(value) ? value : "");
                            }
                            if (!allParamsDataMap.containsKey(valueKey)) {
                                value = resetParamsValueForQc(allParamsDataMap, valueKey, value, qcList, sample, paramsConfig);
                                //处理初始化参数值
                                DtoParamsData paramsData = new DtoParamsData();
                                paramsData.setObjectId(sample.getId());
                                paramsData.setObjectType(EnumParamsDataType.样品.getValue());
                                paramsData.setParamsConfigId(paramsConfig.getId());
                                paramsData.setParamsName(pName);
                                paramsData.setParamsValue(value);
                                paramsData.setDimension(paramsConfig.getDimension());
                                paramsData.setDimensionId(paramsConfig.getDimensionId());
                                paramsData.setOrderNum(paramsConfig.getOrderNum());
                                paramsData.setGroupId(group.getId());
                                paramsDatas.add(paramsData);
                            } else if (!allParamsDataMap.get(valueKey).equals(value)) {
                                Optional<DtoParamsData> pd = paramsDataList.stream().filter(p -> p.getObjectId().equals(sample.getId()) && p.getParamsConfigId().equals(paramsConfig.getId())).findFirst();
                                if (pd.isPresent()) {//TODO 测试这种情况下的save是否为更新
                                    DtoParamsData paramsData = pd.get();
                                    paramsData.setParamsValue(value);
                                    paramsDatas.add(paramsData);
                                }
                            }
                            paramsValue.put(key, value);

                            if ((retConfigs.size() > 0 && retConfigs.stream().noneMatch(p -> p.getParamsName().equals(pName)))
                                    || retConfigs.size() == 0) {
                                retConfigs.add(groupConfig);
                            }
                        }
                    }
                    //如果分组完成之后还有多余的测试项目
                    if (allTestIds.size() > 0) {
                        key = String.format("%s;%s", paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                        //key = paramsConfig.getId();
                        valueKey = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                        paramsValue.put(key, allParamsDataMap.getOrDefault(valueKey, ""));
                        if ((retConfigs.size() > 0 && retConfigs.stream().noneMatch(p -> p.getParamsName().equals(paramsName)))
                                || retConfigs.size() == 0) {
                            retConfigs.add(paramsConfig);
                        }
                    }
                }
            }
            sampleInfoList.add(sampleParams(sample, samTypeMap, redAnalyzeItems, qcList, paramsValue));
        }
        Map<String, Integer> groupMap = new HashMap<>();
        //保证分组设置排序值的时候遍历的顺序和移动端那边一致
        retConfigs.sort(Comparator.comparing(DtoParamsConfig::getOrderNum, Comparator.reverseOrder()).thenComparing(DtoParamsConfig::getParamsName));
        retConfigs.forEach(r -> {
            if (!StringUtil.isNotEmpty(r.getGroupId())) {
                r.setGroupId(UUIDHelper.GUID_EMPTY);
            } else {
                //【紧急】【2023-12-20】【潘长城】【现场任务】现场任务参数排序调整
                if (!UUIDHelper.GUID_EMPTY.equals(r.getGroupId())) {
                    int count = groupMap.size() + 1;
                    if (!groupMap.containsKey(r.getGroupId())) {
                        groupMap.put(r.getGroupId(), count);
                        r.setOrderNum((r.getOrderNum() + 1000 * count));
                    } else {
                        r.setOrderNum((r.getOrderNum() + 1000 * groupMap.get(r.getGroupId())));
                    }
                }
            }
        });

        retConfigs = retConfigs.stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());
        info.setSample(sampleInfoList);
        info.setParamsConfig(retConfigs);
        if (paramsDatas.size() > 0) {
            paramsDataService.saveAsync(paramsDatas);
        }
        return info;
    }

    /**
     * 例行任务点位时需要根据点位的扩展信息参数匹配公共参数，匹配成功则获取点位扩展信息上配置的参数值
     *
     * @param paramsConfig       参数配置
     * @param value              原来的参数值
     * @param folder             点位
     * @param pointExtendDataMap 点位扩展信息映射
     * @return 新的参数值
     */
    private String resetParamsValueForPoint(DtoParamsConfig paramsConfig, String value, DtoSampleFolder folder, Map<String, List<DtoPointExtendData>> pointExtendDataMap,
                                            List<DtoFixedpoint> fixedpointList) {
        String newValue = getParamsValByFixedPoint(paramsConfig.getAlias(), value, folder, pointExtendDataMap, fixedpointList);
        paramsConfig.setParamsValue(newValue);
        return newValue;
    }

    /**
     * 根据例行点位获取点位拓展数据值（包含点位经纬度）
     *
     * @param paramsName         参数名称
     * @param value              原始值
     * @param folder             样品点位
     * @param pointExtendDataMap 例行点位拓展数据
     * @param fixedpointList     例行点位集合
     * @return
     */
    private String getParamsValByFixedPoint(String paramsName, String value, DtoSampleFolder folder, Map<String, List<DtoPointExtendData>> pointExtendDataMap,
                                            List<DtoFixedpoint> fixedpointList) {
        String newValue = value;
        if (StringUtil.isNotNull(folder) && pointExtendDataMap.containsKey(folder.getFixedPointId())) {
            List<DtoPointExtendData> extendDataList = pointExtendDataMap.get(folder.getFixedPointId());
            String alias = StringUtil.isNotEmpty(paramsName) ? paramsName : "";
            DtoPointExtendData extendData = extendDataList.stream().filter(p -> alias.equals(p.getFiledName())).findFirst().orElse(null);
            if (StringUtil.isNotNull(extendData)) {
                newValue = extendData.getFiledValue();
            }
        }
        // 赋值经度纬度
        if (StringUtil.isNotNull(folder)) {
            if (paramsName.equals("经度") || paramsName.equals("纬度") || paramsName.equals("经纬度")) {
                Optional<DtoFixedpoint> fixedpoint = fixedpointList.stream().filter(p -> p.getId().equals(folder.getFixedPointId())).findFirst();
                if (fixedpoint.isPresent()) {
                    DtoFixedpoint dtoFixedpoint = fixedpoint.get();
                    newValue = paramsName.equals("经纬度") ? dtoFixedpoint.getLon() + "," + dtoFixedpoint.getLat() :
                            paramsName.equals("经度") ? dtoFixedpoint.getLon() : dtoFixedpoint.getLat();
                }
            }
        }
        return newValue;
    }

    /**
     * 重新赋值 现场任务-样品信息中，全程序空白、运输空白、现场空白、设备空白，参数区域在新增后默认'/'
     *
     * @param allParamsDataMap 参数数据分组
     * @param key              采纳数key
     * @param value            值
     * @param qcList           质控数据
     * @param sample           样品
     * @param paramsConfig     参数配置
     * @return
     */
    private String resetParamsValueForQc(Map<String, String> allParamsDataMap, String key, String value, List<DtoQualityControl> qcList, DtoSample sample, DtoParamsConfig paramsConfig) {
        String newParamsValue = value;
        DtoQualityControl qualityControl = qcList.stream().filter(p -> p.getId().equals(sample.getQcId())
                && (p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())
                && (p.getQcType().equals(EnumLIM.EnumQCType.空白.getValue()) ||
                p.getQcType().equals(EnumLIM.EnumQCType.运输空白.getValue()) ||
                p.getQcType().equals(EnumLIM.EnumQCType.现场空白.getValue()) ||
                p.getQcType().equals(EnumLIM.EnumQCType.仪器空白.getValue())))).findFirst().orElse(null);
        if (StringUtil.isNotNull(qualityControl) && (paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.样品参数.getValue()) ||
                paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())
                || paramsConfig.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue()))) {
            newParamsValue = "/";
            allParamsDataMap.put(key, newParamsValue);
        }
        return newParamsValue;
    }

    /**
     * 根据送样单、检测类型、点位查询样品
     *
     * @param receiveId      送样单id
     * @param sampleTypeId   检测类型id
     * @param sampleFolderId 点位id
     * @param isSameRecord   是否限制同张送样单下
     * @return 样品
     */
    @Override
    public List<DtoSample> findSampleByReceiveIdAndSampleTypeIdAndSampleFolderId(String receiveId, String sampleTypeId, String sampleFolderId, Boolean isSameRecord) {
        List<DtoSample> sampleList = new ArrayList<>();
        if (isSameRecord || !StringUtils.isNotNullAndEmpty(sampleFolderId) || UUIDHelper.GUID_EMPTY.equals(sampleFolderId)) {//同张送样单下
            sampleList = repository.findByReceiveId(receiveId);
            sampleList = sampleList.stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId)).collect(Collectors.toList());

            if (StringUtils.isNotNullAndEmpty(sampleFolderId) && !UUIDHelper.GUID_EMPTY.equals(sampleFolderId)) {
                List<String> samIds = sampleList.stream().filter(p -> p.getSampleFolderId().equals(sampleFolderId)).map(DtoSample::getId).collect(Collectors.toList());
                sampleList = sampleList.stream().filter(p -> p.getSampleFolderId().equals(sampleFolderId) || samIds.contains(p.getAssociateSampleId())).collect(Collectors.toList());
            }
        } else {//不同送样单下，返回该点位下的样品
            sampleList = repository.findBySampleFolderId(sampleFolderId);
            sampleList.addAll(getLocalAssociateSample(sampleList.stream().map(DtoSample::getId).collect(Collectors.toList())));
        }
        sampleList.sort(Comparator.comparing(DtoSample::getCode));
        return sampleList;
    }

    @Transactional
    @Override
    public DtoSample updateSample(DtoSample sample) {
        DtoSample dto = repository.findOne(sample.getId());
        Map<String, Map<String, Object>> sampleChange = proService.getCompare(dto, sample);
        if (!StringUtils.isNotNullAndEmpty(dto.getCode()) &&
                StringUtils.isNotNullAndEmpty(sample.getCode())) {
            throw new BaseException("请进行样品编号生成");
        }
        if (StringUtils.isNotNullAndEmpty(dto.getCode()) &&
                !StringUtils.isNotNullAndEmpty(sample.getCode())) {
            throw new BaseException("样品编号不能为空");
        }
        if (!dto.getCode().equals(sample.getCode())) {
            Integer count = repository.countByCodeAndIdNot(sample.getCode(), sample.getId());
            if (count > 0) {
                throw new BaseException("样品编号已经存在");
            }
            verifySample(sample);
        }

        if (dto.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
            DtoSampleFolder folder = sampleFolderRepository.findOne(dto.getSampleFolderId());
            if (!folder.getWatchSpot().equals(sample.getRedFolderName())) {
                DtoSampleFolder sf = new DtoSampleFolder();
                BeanUtils.copyProperties(folder, sf);
                sf.setWatchSpot(sample.getRedFolderName());
                sample.setRedFolderName(this.getFolderName(sample, sample.getRedFolderName(), folder.getFolderCode()));
                sampleFolderService.update(sf);
            } else {
                //点位名称未改变则将该字段置为null，防止后续进行更新。因为判断的时候是去了(1-1)这种后缀判断的，不设置为null会导致属性被改掉。
                sample.setRedFolderName(null);
                sampleChange.remove(EnumSampleField.点位名称.getValue());
            }
        } else {
            String watchSpot = this.getSampleWatchSpot(dto);
            if (!watchSpot.equals(sample.getRedFolderName())) {
                throw new BaseException("只有原样可以修改点位名称");
            }
        }

        comRepository.merge(sample);

        List<String> contents = this.getChangeContent(sampleChange, EnumSampleField.样品编号, EnumSampleField.点位名称, EnumSampleField.备注);
        if (contents.size() > 0) {
            newLogService.createLog(sample.getId(), String.format("修改了样品%s信息,具体如下:%s", this.getSampleName(sample, this.getSampleWatchSpot(sample)), String.join(";", contents)), "",
                    EnumLogType.样品信息.getValue(), EnumLogObjectType.样品.getValue(), EnumLogOperateType.修改样品.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
        return sample;
    }

    /**
     * 新增外部样品
     *
     * @param dto 样品
     * @return 返回外部样品
     */
    @Transactional
    @Override
    public DtoOutSample saveOutsideSample(DtoOutSample dto) {
        Integer count = StringUtils.isNotNullAndEmpty(dto.getCode()) ? repository.countByCode(dto.getCode()) : 0;
        if (count > 0) {
            throw new BaseException("样品编号已经存在");
        }
        //外部送样没有点位编号，所以可以直接替换(%d-%d-%d)
        String redFolderName = dto.getRedFolderName().replace(String.format("(%d-%d-%d)", dto.getCycleOrder(),
                dto.getTimesOrder(), dto.getSampleOrder()), "") + String.format("(%d-%d-%d)",
                dto.getCycleOrder(), dto.getTimesOrder(), dto.getSampleOrder());

        count = repository.countByReceiveIdAndSampleTypeIdAndRedFolderName(dto.getReceiveId(), dto.getSampleTypeId(), redFolderName);
        if (count > 0) {
            throw new BaseException("已存在样品点位" + redFolderName);
        }

        DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(dto.getReceiveId());
        DtoProject project = projectRepository.findOne(record.getProjectId());
        dto.setId(UUIDHelper.NewID());
        DtoSample sam = this.saveOutSample(dto, record, project);
        //新增原样的时候返回的次数加1
        //2022-6-10 【重要】【2022-6-9】【嘉兴】【样品登记】送样、应急、委托现场送样等送样类任务，样品登记时，周期频次固定为：1-1-1
        //dto.setTimesOrder(dto.getTimesOrder() + 1);
        DtoTestPost testPost = findTestPostCode(record.getSenderId());
        dto.setCode(this.createSampleCode(dto.getProjectId(), project.getProjectTypeId(), dto.getSampleTypeId(),
                StringUtils.isNotNullAndEmpty(dto.getSampleFolderId()) ? dto.getSampleFolderId() : UUIDHelper.GUID_EMPTY,
                StringUtil.isNotNull(dto.getSamplingTimeBegin()) ? dto.getSamplingTimeBegin() : record.getSamplingTime(), true,
                getSamplingPerson(record), testPost, record));
        String watchSpot = dto.getRedFolderName().replace(String.format("(%d-%d-%d)", dto.getCycleOrder(),
                dto.getTimesOrder(), dto.getSampleOrder()), "") + String.format("(%d-%d-%d)", dto.getCycleOrder(),
                dto.getTimesOrder(), dto.getSampleOrder());
        String comment = String.format("新增了样品%s", this.getSampleName(sam, watchSpot));
        newLogService.createLog(dto.getReceiveId(), comment, "", EnumLogType.送样单样品信息.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.增加样品.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        for (DtoParamsConfig pc : dto.getParamsData()) {
            if (StringUtils.isNotNullAndEmpty(pc.getDefaultValue())) {
                pc.setParamsValue(pc.getDefaultValue());
            } else {
                pc.setParamsValue("");
            }
        }

        return dto;
    }

    /**
     * 修改外部样品
     *
     * @param sampleSave 样品修改信息
     */
    @Transactional
    @Override
    public void updateOutsideSample(DtoOutSampleSave sampleSave) {
        DtoSample sample = repository.findOne(sampleSave.getId());
        String watchSpot = sample.getRedFolderName().replace(String.format("(%d-%d-%d)", sample.getCycleOrder(),
                sample.getTimesOrder(), sample.getSampleOrder()), "");

        //#region 获取修改信息
        Object newValue = null;
        Object oldValue = null;
        String fieldName = sampleSave.getFieldByOldSample(sample);

        String annotation = "";
        //判断下来存在修改内容
        if (StringUtil.isNotEmpty(fieldName)) {
            try {
                Field field = DtoOutSampleSave.class.getDeclaredField(fieldName);
                field.setAccessible(true);
                newValue = field.get(sampleSave);
                for (Annotation anno : field.getAnnotations()) {
                    if (anno instanceof DeclareAnnotation) {
                        annotation = ((DeclareAnnotation) anno).value();
                        break;
                    }
                }
                if (fieldName.equals("redFolderName")) {
                    oldValue = watchSpot;
                } else {
                    Field samField = DtoSample.class.getSuperclass().getDeclaredField(fieldName);
                    samField.setAccessible(true);
                    oldValue = samField.get(sample);
                }
                //如果是修改了经纬度，前端组件选中后会同时修改经度和纬度两个字段，所以要修改两个字段
                if (fieldName.equals("lon") || fieldName.equals("lat")) {
                    sample.setLat(sampleSave.getLat());
                    sample.setLon(sampleSave.getLon());
                    // 修改点位上的计划经纬度
                    this.updateSampleFolder(sample);
                }
            } catch (NoSuchFieldException | IllegalAccessException ex) {
                System.out.println(ex.getMessage());
                throw new BaseException("异常错误");
            }
            //#endregion

            sampleSave.loadFromSave(sample);
            if (fieldName.equals("code")) {
                if (StringUtil.isNull(sample.getCode())) {
                    throw new BaseException("样品编号不能为空");
                }
                Integer count = repository.countByCodeAndIdNot(sample.getCode(), sample.getId());
                if (count > 0) {
                    throw new BaseException("样品编号已经存在");
                }
                if (!newValue.equals(sample.getCode())) {
                    verifySample(sample);
                }
            }
            if ((fieldName.equals("redFolderName") || fieldName.equals("cycleOrder") || fieldName.equals("timesOrder")
                    || fieldName.equals("sampleOrder"))) {
                if (!sample.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                    throw new BaseException("仅原样可以修改点位名称及周期批次样品数");
                }

                //核对点位
                if (fieldName.equals("redFolderName")) {
                    watchSpot = sampleSave.getRedFolderName();
                    //外部送样没有点位编号，所以可以直接替换(%d-%d-%d)
                    String redFolderName = watchSpot + String.format("(%d-%d-%d)", sample.getCycleOrder(),
                            sample.getTimesOrder(), sample.getSampleOrder());
                    Integer count = repository.countByProjectIdAndRedFolderNameAndIdNot(sample.getProjectId(),
                            redFolderName, sample.getId());
                    if (count > 0) {
                        throw new BaseException("已存在样品点位" + redFolderName);
                    }
                    this.checkSampleFolder(sample);
                } else {
                    DtoSamplingFrequency samplingFrequency = samplingFrequencyService.findOne(sample.getSamplingFrequencyId());
                    samplingFrequency.setPeriodCount(sample.getCycleOrder());
                    samplingFrequency.setTimePerPeriod(sample.getTimesOrder());
                    samplingFrequency.setSamplePerTime(sample.getSampleOrder());
                    samplingFrequencyService.update(samplingFrequency);
                    sample.setRedFolderName(watchSpot + String.format("(%d-%d-%d)", sample.getCycleOrder(),
                            sample.getTimesOrder(), sample.getSampleOrder()));
                }
            }
            //填充点位名称
            String redFolderName = sample.getRedFolderName().replace(String.format("(%d-%d-%d)", sample.getCycleOrder(),
                    sample.getTimesOrder(), sample.getSampleOrder()), "") +
                    String.format("(%d-%d-%d)", sample.getCycleOrder(), sample.getTimesOrder(), sample.getSampleOrder());
            sample.setRedFolderName(redFolderName);
            //修改样品
            comRepository.merge(sample);
            if ((fieldName.equals("redFolderName") || fieldName.equals("cycleOrder")
                    || fieldName.equals("timesOrder") || fieldName.equals("sampleOrder"))
                    || fieldName.equals("lon") || fieldName.equals("lat")) {
                this.updateRelateSamples(sample);
            }
        }
        String comment = String.format("修改了样品%s信息,%s由'%s'修改为'%s'", this.getSampleName(sample, ""), annotation, oldValue, newValue);
        newLogService.createLog(sample.getId(), comment, "", EnumLogType.样品信息.getValue(), EnumLogObjectType.样品.getValue(), EnumLogOperateType.修改样品.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Transactional
    //@Async
    public void updateRelateSamples(DtoSample sample) {
        List<DtoSample> associateSamples = repository.findByAssociateSampleId(sample.getId());
        if (StringUtil.isNotNull(associateSamples) && associateSamples.size() > 0) {//修改关联样的点位名称
            List<String> qcIds = associateSamples.stream().map(DtoSample::getQcId).
                    filter(qcId -> !UUIDHelper.GUID_EMPTY.equals(qcId)).collect(Collectors.toList());
            List<DtoQualityControl> qcList = qcIds.size() > 0 ? qualityControlRepository.findAll(qcIds) : new ArrayList<>();
            Map<String, DtoQualityControl> qcMap = new HashMap<>();
            if (StringUtil.isNotNull(qcList) && qcList.size() > 0) {
                qcMap = qcList.stream().collect(Collectors.toMap(DtoQualityControl::getId, qc -> qc));
            }
            for (DtoSample associateSample : associateSamples) {
                if (qcMap.containsKey(associateSample.getQcId())) {
                    DtoQualityControl qc = qcMap.get(associateSample.getQcId());
                    associateSample.setRedFolderName(this.getQCRedFolderName(qc.getQcGrade(), qc.getQcType(), sample));
                } else {
                    associateSample.setRedFolderName(this.getAssociateRedFolderName(associateSample.getSampleCategory(), null, null, sample));
                }
                associateSample.setCycleOrder(sample.getCycleOrder());
                associateSample.setTimesOrder(sample.getTimesOrder());
                associateSample.setSampleOrder(sample.getSampleOrder());
                comRepository.merge(associateSample);
            }
        }
    }

    /**
     * 修改点位名称
     *
     * @param sampleFolderId 点位id
     * @param watchSpot      点位名称
     * @param folderCode     点位编号
     */
    @Transactional
    @Override
    public void updateWatchSpot(String sampleFolderId, String watchSpot, String folderCode) {
        List<DtoSample> samples = repository.findBySampleFolderId(sampleFolderId);
        for (DtoSample sample : samples) {
            sample.setRedFolderName(this.getFolderName(sample, watchSpot, folderCode));
            comRepository.merge(sample);
        }

        List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSample> associateSamples = StringUtil.isNotEmpty(sampleIds) ? repository.findByAssociateSampleIdIn(sampleIds) : new ArrayList<>();
        if (StringUtil.isNotNull(associateSamples) && associateSamples.size() > 0) {//修改关联样的点位名称
            List<String> qcIds = associateSamples.stream().map(DtoSample::getQcId).filter(qcId -> !UUIDHelper.GUID_EMPTY.equals(qcId)).collect(Collectors.toList());
            List<DtoQualityControl> qcList = qcIds.size() > 0 ? qualityControlRepository.findAll(qcIds) : new ArrayList<>();
            Map<String, DtoQualityControl> qcMap = new HashMap<>();
            if (StringUtil.isNotNull(qcList) && qcList.size() > 0) {
                qcMap = qcList.stream().collect(Collectors.toMap(DtoQualityControl::getId, qc -> qc));
            }
            Map<String, List<DtoSample>> associateSampleMap = associateSamples.stream().collect(Collectors.groupingBy(DtoSample::getAssociateSampleId));
            Map<String, DtoSample> sampleMap = samples.stream().collect(Collectors.toMap(DtoSample::getId, sam -> sam));
            for (String sampleId : associateSampleMap.keySet()) {
                if (!sampleMap.containsKey(sampleId)) {
                    continue;
                }
                DtoSample oldSample = sampleMap.get(sampleId);
                for (DtoSample associateSample : associateSampleMap.get(sampleId)) {
                    if (qcMap.containsKey(associateSample.getQcId())) {
                        DtoQualityControl qc = qcMap.get(associateSample.getQcId());
                        associateSample.setRedFolderName(this.getQCRedFolderName(qc.getQcGrade(), qc.getQcType(), oldSample));
                    } else {
                        associateSample.setRedFolderName(this.getAssociateRedFolderName(associateSample.getSampleCategory(), null, null, oldSample));
                    }
                }
            }
            for (DtoSample sample : associateSamples) {
                comRepository.merge(sample);
            }
        }
    }

    /**
     * 根据点位修改样品经纬度
     *
     * @param sampleFolder 点位
     */
    @Transactional
    @Override
    public void updateSampleLonAndLat(DtoSampleFolder sampleFolder) {
        List<DtoSample> samples = repository.findBySampleFolderId(sampleFolder.getId());
        List<DtoSample> saveList = new ArrayList<>();
        for (DtoSample sample : samples) {
            sample.setLon(sampleFolder.getLon());
            sample.setLat(sampleFolder.getLat());
            saveList.add(sample);
        }
        super.update(saveList);
    }

    @Override
    public List<DtoSample> findSamplesByUseRecord(String instrumentUseRecordId) {
        List<DtoSample> samples = new ArrayList<>();
        List<DtoInstrumentUseRecord2Sample> instrumentUseRecord2Samples = instrumentUseRecord2SampleService.findByInstrumentUseRecordId(instrumentUseRecordId);
        if (StringUtil.isNotNull(instrumentUseRecord2Samples)) {
            List<String> sampleIds = instrumentUseRecord2Samples.stream().map(DtoInstrumentUseRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
            if (sampleIds.size() > 0) {
                PageBean<DtoSample> pageBean = new PageBean<>();
                pageBean.setRowsPerPage(Integer.MAX_VALUE);
                SampleCriteria sampleCriteria = new SampleCriteria();
                sampleCriteria.setSampleIds(sampleIds);
                findByPage(pageBean, sampleCriteria);
                samples = pageBean.getData();
            }
        }
        return samples;
    }

    /**
     * 新增关联样
     *
     * @param ids            样品id集合
     * @param sampleCategory 样品类别
     * @param qcGrade        质控等级
     * @param qcType         质控类型
     */
    @Transactional
    @Override
    public List<DtoSample> addAssociateSample(Collection<String> ids, Integer sampleCategory, Integer qcGrade, Integer qcType) {
        return this.addAssociateSample(ids, sampleCategory, qcGrade, qcType, false);
    }

    /**
     * 新增关联样
     *
     * @param ids            样品id集合
     * @param sampleCategory 样品类别
     * @param qcGrade        质控等级
     * @param qcType         质控类型
     */
    @Transactional
    @Override
    public List<DtoSample> addAssociateSample(Collection<String> ids, Integer sampleCategory, Integer qcGrade, Integer qcType, Boolean isApi) {
        Date to = new Date();
        List<DtoSample> samples = repository.findAll(ids);
        List<DtoSample> qcSampleList = samples.stream().filter(p -> !p.getSampleCategory().equals(EnumSampleCategory.原样.getValue())).collect(Collectors.toList());
        List<String> projectIds = new ArrayList<>();
        //空白样可以添加串联样 2022-07-27 所有质控样都可以添加串联样
        if (EnumSampleCategory.串联样.getValue().equals(sampleCategory)) {
            List<String> qcIds = qcSampleList.stream().map(DtoSample::getQcId).distinct().collect(Collectors.toList());
            List<String> yyIds = qcSampleList.stream().map(DtoSample::getAssociateSampleId).distinct().collect(Collectors.toList());
            projectIds = repository.findAll(yyIds).stream().map(DtoSample::getProjectId).distinct().collect(Collectors.toList());
            //空白样的质控id
            //List<String> kbQcIds = qualityControlRepository.findAll(qcIds).stream().filter(p ->(new QualityBlank()).qcTypeValue().equals(p.getQcType())).map(DtoQualityControl::getId).collect(Collectors.toList());
            //qcSampleList = qcSampleList.stream().filter(p -> !kbQcIds.contains(p.getQcId())).collect(Collectors.toList());
            qcSampleList.clear();
        }
        if (qcSampleList.size() > 0) {
            throw new BaseException("所选样品包含质控样，请重新选择");
        }
        if (samples.size() == 0) {
            throw new BaseException("请选择所需添加质控样的原样");
        }

        //数据源
        DtoLoadOutSample source = this.findLoadYuanSample(samples);

        DtoProject project = projectRepository.findOne(samples.get(0).getProjectId());

        //如果选择的是空白样添加串联样，那么project有可能会是null，需要重新获取project信息
        if (project == null && projectIds.size() > 0) {
            project = projectRepository.findOne(projectIds.get(0));
        }

        //最终需要插入的数据载体
        DtoLoadOutSample target = new DtoLoadOutSample();
        target.setReceiveSubSampleRecord(source.getReceiveSubSampleRecord());

        //单独插入的样品
        List<DtoSample> qcSamples = new ArrayList<>();
        //需纠正状态的样品
        List<DtoSample> checkSamples = new ArrayList<>();
        //提取涉及到的测试项目id并进行提取及筛选
        List<String> testIds = source.getAnalyseData().stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        if (sampleCategory.equals(EnumSampleCategory.串联样.getValue())) {
            testList = testList.stream().filter(DtoTest::getIsSeries).collect(Collectors.toList());
        } else if (qcType.equals(new QualityParallel().qcTypeValue())) {
            testList = testList.stream().filter(DtoTest::getIsQCP).collect(Collectors.toList());
        } else if (qcType.equals(new QualityBlank().qcTypeValue())) {
            testList = testList.stream().filter(DtoTest::getIsQCB).collect(Collectors.toList());
        } else if (qcType.equals(new QualityTransportBlank().qcTypeValue())) {
            testList = testList.stream().filter(DtoTest::getIsQCTransport).collect(Collectors.toList());
        } else if (qcType.equals(new QualityInstrumentBlank().qcTypeValue())) {
            testList = testList.stream().filter(DtoTest::getIsQCInstrument).collect(Collectors.toList());
        } else if (qcType.equals(new QualityLocalBlank().qcTypeValue())) {
            testList = testList.stream().filter(DtoTest::getIsQCLocal).collect(Collectors.toList());
        } else if (qcType.equals(new QualityCipherParallel().qcTypeValue())) {
            testList = testList.stream().filter(DtoTest::getIsMMP).collect(Collectors.toList());
        }

        HashMap<String, String> sample2Qc = new HashMap<>();
        //遍历原样，添加质控样
        List<DtoQualityControl> qualityControls = new ArrayList<>();
        //要新的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();
        for (DtoSample sample : samples) {
            Map<String, Object> returnMap = this.getAssociateSample(sampleCategory, qcGrade,
                    qcType, sample, "", "", "", "", null,
                    PrincipalContextUser.getPrincipal().getUserId(), null, project, null, "", null);
            DtoSample qcSample = (DtoSample) returnMap.get("sample");
            qcSample.setPreparedStatus(sample.getPreparedStatus());
            DtoQualityControl qc = (DtoQualityControl) returnMap.get("qualityControl");
            DtoGenerateSN generateSN = (DtoGenerateSN) returnMap.get("generateSN");
            source.putNewSampleId(sample.getId(), qcSample.getId());
            sample2Qc.put(qcSample.getId(), qcSample.getQcId());
            //若样品编号存在，则进行保存，防止编号唯一性出错
            //todo 如果是移动端，可以不进行创建样品编号先加入送样单，这样创建质控样的时候会存在空的情况
            if (StringUtils.isNotNullAndEmpty(qcSample.getCode()) || isApi) {
                //同时该样品需要进行纠正
                checkSamples.add(qcSample);
            }
            //统一做处理保存
            qcSamples.add(qcSample);
            if (StringUtil.isNotNull(qc)) {
                qualityControls.add(qc);
            }
            if (StringUtil.isNotNull(generateSN)) {
                DtoSerialNumberConfig serialNumberConfigCreate = generateSN.getSerialNumberConfigCreate();
                if (StringUtil.isNotNull(serialNumberConfigCreate)) {
                    serialNumberConfigCreateList.add(serialNumberConfigCreate);
                }
                DtoSerialNumberConfig serialNumberConfigUpdate = generateSN.getSerialNumberConfigUpdate();
                if (StringUtil.isNotNull(serialNumberConfigUpdate)) {
                    serialNumberConfigUpdateList.add(serialNumberConfigUpdate);
                }
            }
        }
        List<DtoQualityControlEvaluate> evaluateList = new ArrayList<>();
        List<String> testIdList = source.getAnalyseData().stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        //获取当前测试项目的质控限值配置
        List<DtoQualityControlLimit> qualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(testIdList);
        //遍历原样指标，进行数据插入
        for (DtoAnalyseData analyseData : source.getAnalyseData()) {
            if (testList.stream().anyMatch(p -> p.getId().equals(analyseData.getTestId())) && !analyseData.getIsOutsourcing()) {
                DtoAnalyseData targetAnalyseData = new DtoAnalyseData();
                BeanUtils.copyProperties(analyseData, targetAnalyseData);
                targetAnalyseData.setSampleId(source.getNewSampleId(analyseData.getSampleId()));
                targetAnalyseData.setQcId(sample2Qc.getOrDefault(targetAnalyseData.getSampleId(), UUIDHelper.GUID_EMPTY));
                if (!targetAnalyseData.getQcId().equals(UUIDHelper.GUID_EMPTY)) {
                    targetAnalyseData.setQcGrade(qcGrade);
                    targetAnalyseData.setQcType(qcType);
                }
                targetAnalyseData.setWorkSheetId(UUIDHelper.GUID_EMPTY);
                targetAnalyseData.setWorkSheetFolderId(UUIDHelper.GUID_EMPTY);
                targetAnalyseData.setTestValue("");
                //静安个性化，新增选程序空白时检出限设置为0
                if (allProcessBlankExamLimitValueDefaultZero()) {
                    if (EnumLIM.EnumQCGrade.外部质控.getValue().equals(qcGrade) && new QualityBlank().qcTypeValue().equals(qcType)) {
                        targetAnalyseData.setExamLimitValue("0");
                    }
                }
                targetAnalyseData.setTestValueD(BigDecimal.ZERO);
                targetAnalyseData.setTestOrignValue("");
                targetAnalyseData.setTestValueDstr("");
                //若数据不为分包，则置为未测，若为分包则与原先保持一致
                if (!analyseData.getIsOutsourcing() && !analyseData.getIsSamplingOut()) {
                    targetAnalyseData.setStatus(EnumAnalyseDataStatus.未测.toString());
                    targetAnalyseData.setDataStatus(EnumAnalyseDataStatus.未测.getValue());
                    targetAnalyseData.setDataChangeStatus(EnumDataChangeStatus.未变更.getValue());
                    targetAnalyseData.setIsDataEnabled(false);
                }
//                targetAnalyseData.setAnalyzeTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
                targetAnalyseData.setCreateDate(new Date());
                targetAnalyseData.setCreator(PrincipalContextUser.getPrincipal().getUserId());
                targetAnalyseData.setModifyDate(new Date());
                targetAnalyseData.setModifier(PrincipalContextUser.getPrincipal().getUserId());

                targetAnalyseData.setId(UUIDHelper.NewID());
                target.addAnalyseData(targetAnalyseData);
                //初始化质控评价记录
                evaluateList.add(qualityControlEvaluateService.initQualityControlEvaluate(targetAnalyseData.getId(), targetAnalyseData.getTestId(),
                        qcType, qcGrade, qualityControlLimitList, "", targetAnalyseData.getQcId()));
            }
        }

        //插入领样单样品关联
        for (DtoReceiveSubSampleRecord subRecord : source.getReceiveSubSampleRecord()) {
            for (DtoSample checkSample : checkSamples) {
                if ((subRecord.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0 &&
                        target.getAnalyseData().stream().anyMatch(p -> p.getSampleId().equals(checkSample.getId()) && !p.getIsCompleteField())) {
                    DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                    r2s.setSampleId(checkSample.getId());
                    r2s.setReceiveSubSampleRecordId(subRecord.getId());
                    target.addReceiveSubSampleRecord2Sample(r2s);
                }
                if ((subRecord.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0 &&
                        target.getAnalyseData().stream().anyMatch(p -> p.getSampleId().equals(checkSample.getId()) && p.getIsCompleteField())) {
                    DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                    r2s.setSampleId(checkSample.getId());
                    r2s.setReceiveSubSampleRecordId(subRecord.getId());
                    target.addReceiveSubSampleRecord2Sample(r2s);
                }
            }
        }
        if (serialNumberConfigCreateList.size() > 0) {
            comRepository.insert(serialNumberConfigCreateList);
        }
        if (serialNumberConfigUpdateList.size() > 0) {
            comRepository.updateBatch(serialNumberConfigUpdateList);
        }
        if (qualityControls.size() > 0) {
            comRepository.insert(qualityControls);
        }
        //先进行数据插入，保证后续纠正状态时存在对应的数据
        if (target.getAnalyseData().size() > 0) {
            comRepository.insertBatch(target.getAnalyseData());
        }
        if (StringUtil.isNotEmpty(evaluateList)) {
            qualityControlEvaluateRepository.save(evaluateList);
        }
        if (target.getReceiveSubSampleRecord2Sample().size() > 0) {
            comRepository.insertBatch(target.getReceiveSubSampleRecord2Sample());
        }
        //对纠正样品修改分析项目
        for (DtoSample checkSample : checkSamples) {
            checkSample.setRedAnalyzeItems(proService.getAnalyzeItemsByAna(target.getAnalyseData().stream().filter(p -> p.getSampleId().equals(checkSample.getId())).collect(Collectors.toList())));
        }
        //若存在需要直接插入的样品，进行分析项目的赋值后直接插入数据库
        if (qcSamples.size() > 0) {
            for (DtoSample qcSample : qcSamples) {
                qcSample.setRedAnalyzeItems(proService.getAnalyzeItemsByAna(target.getAnalyseData().stream().filter(p -> p.getSampleId().equals(qcSample.getId())).collect(Collectors.toList())));
            }
            comRepository.insertBatch(qcSamples);
        }
        //存在纠正状态的样品，进行样品状态纠正
        Map<Integer, DtoSample> sampleStatusMap = new HashMap<>();
        if (checkSamples.size() > 0) {
            List<String> templateSampleIds = new ArrayList<>();
            for (DtoSample checkSample : checkSamples) {
                Integer sampleTestType = target.getSampleTestType(checkSample.getId());
                if (!sampleStatusMap.containsKey(sampleTestType)) {
                    sampleStatusMap.put(sampleTestType, checkSample);
                    templateSampleIds.add(checkSample.getId());
                }
            }
            for (DtoSample checkSample : checkSamples) {
                Integer sampleTestType = target.getSampleTestType(checkSample.getId());
                if (!templateSampleIds.contains(checkSample.getId())) {
                    checkSample.setStatus(sampleStatusMap.get(sampleTestType).getStatus());
                    checkSample.setSamplingStatus(sampleStatusMap.get(sampleTestType).getSamplingStatus());
                    checkSample.setInnerReceiveStatus(sampleStatusMap.get(sampleTestType).getInnerReceiveStatus());
                    checkSample.setAnanlyzeStatus(sampleStatusMap.get(sampleTestType).getAnanlyzeStatus());
                    comRepository.merge(checkSample);
                }
            }
        } else {
            //否则进行项目状态纠正
            if (!StringUtil.isNotEmpty(projectIds)) {
                projectIds = samples.stream().map(DtoSample::getProjectId).distinct().collect(Collectors.toList());
            }
            proService.checkProject(projectIds);
        }
        //统一处理日志
        List<DtoLog> logList = new ArrayList<>();
        Map<String, String> samCommentMap = new HashMap<>();
        for (DtoSample sample : samples) {//遍历原样，获取对应的质控样信息，并塞到map和list中
            DtoSample qcSample = qcSamples.stream().filter(p -> p.getAssociateSampleId().equals(sample.getId())).findFirst().orElse(null);

            EnumSampleCategory enumSampleCategory = EnumSampleCategory.getByValue(sampleCategory);
            //EnumQCType enumQCType = EnumQCType.getByValue(qcType);
            String enumQCType = "";
            try {
                enumQCType = QualityTaskFactory.getInstance().getQcSample(qcType).qcTypeName();
            } catch (Exception ex) {
            }
            String comment = String.format("原样%s增加了%s样%s", this.getSampleName(sample, this.getSampleWatchSpot(sample)),
                    StringUtil.isNotNull(enumSampleCategory) && !enumSampleCategory.equals(EnumSampleCategory.质控样) ? enumSampleCategory.toString() :
                            StringUtil.isNotNull(enumQCType) ? enumQCType : "质控", StringUtil.isNotNull(qcSample) ? qcSample.getCode() : "");

            DtoLog log = new DtoLog();
            log.setId(UUIDHelper.NewID());
            log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            log.setOperateTime(new Date());
            log.setOperateInfo(EnumLogOperateType.增加质控样.toString());
            log.setLogType(EnumLogType.样品质控.getValue());
            log.setObjectId(sample.getId());
            log.setObjectType(EnumLogObjectType.样品.getValue());
            log.setComment(comment);
            log.setOpinion("");
            log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
            log.setRemark("");
            logList.add(log);
            samCommentMap.put(sample.getId(), comment);
        }
        newLogService.createLog(logList, EnumLogType.样品质控.getValue());

        //进行领样单与样品的关联添加
        List<String> receiveIds = samples.stream().map(DtoSample::getReceiveId)
                .filter(receiveId -> !UUIDHelper.GUID_EMPTY.equals(receiveId)).collect(Collectors.toList());
        for (String receiveId : receiveIds) {//理论上只会有一个送样单，所以直接遍历了
            DtoReceiveSampleRecord record = receiveSampleRecordRepository.findOne(receiveId);
            List<DtoSample> samListInRecord = samples.stream().filter(p -> p.getReceiveId().equals(receiveId)).collect(Collectors.toList());
            List<String> sampleIdsInRecord = samListInRecord.stream().map(DtoSample::getId).collect(Collectors.toList());
            newLogService.createLog(receiveId, String.format("在送样单%s中,%s。", record.getRecordCode(), samCommentMap.entrySet().stream().filter(p -> sampleIdsInRecord.contains(p.getKey())).map(p -> p.getValue()).collect(Collectors.joining(";"))), "",
                    EnumLogType.送样单样品信息.getValue(), EnumLogObjectType.送样单.getValue(), EnumLogOperateType.增加质控样.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
        //必须在事务提交之后，才能纠正样品状态，否则mySql缓存存在找不到数据情况
        if (sampleStatusMap.values().size() > 0) {
            proService.checkSample(new ArrayList<>(sampleStatusMap.values()), project);
        }
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        for (String receiveId : receiveIds) {
                            proService.sendProMessage(EnumProAction.添加现场质控样, "", receiveId);
                        }
                    }
                }
        );
        //添加现场平行样时，现场平行样的样品参数需要自动复制对应原样的参数
        if (new QualityParallel().qcTypeValue().equals(qcType) && EnumLIM.EnumQCGrade.外部质控.getValue().equals(qcGrade)) {
            copySampleParams(qcSamples);
        }
//        qcSamples.addAll(checkSamples);
        return qcSamples;
    }

    @Override
    public List<DtoSample> findSampleBySubId(String subId) {
        List<String> sampleIds = receiveSubSampleRecord2SampleRepository.findByReceiveSubSampleRecordId(subId)
                .stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
        return super.findAll(sampleIds);
    }

    @Override
    public List<DtoSample> findBySampleIds(Collection<String> samIds) {
        List<DtoSample> sampleList = repository.findAll(samIds);
        List<String> qcIds = sampleList.stream().map(DtoSample::getQcId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p))
                .collect(Collectors.toList());
        List<DtoQualityControl> qcList = qualityControlRepository.findAll(qcIds);
        sampleList.forEach(p -> {
            Optional<DtoQualityControl> qc = qcList.stream().filter(q -> p.getQcId().equals(q.getId())).findFirst();
            qc.ifPresent(q -> {
                p.setQcGrade(q.getQcGrade());
                p.setQcType(q.getQcType());
            });
        });
        return sampleList;
    }

    protected DtoSample findBySampleId(String sampleId) {
        DtoSample sample = repository.findOne(sampleId);
        if (null == sample) {
            return null;
        }
        Optional<DtoQualityControl> qc = Optional.ofNullable(qualityControlRepository.findOne(sample.getQcId()));
        qc.ifPresent(p -> {
            sample.setQcGrade(p.getQcGrade());
            sample.setQcType(p.getQcType());
        });
        return sample;
    }

    @Override
    public String getSortOrder(DtoSample sample, List<OrderReviseVO> orderReviseVOList) {
        String orderNum = "30-" + sample.getCode() + "-30";
        if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(sample.getSampleCategory())) {
            Integer qcType = sample.getQcType();
            if (!qcType.equals(new QualityCipherParallel().qcTypeValue())) {
                if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(sample.getSampleCategory())) {
                    qcType = EnumPRO.EnumSampleCategory.串联样.getQcType();
                }
                if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(sample.getSampleCategory())) {
                    qcType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
                }
                DtoQualityConfig qualityConfig = QualityTaskFactory.getInstance().getQcSample(qcType).getQualityConfig(orderReviseVOList);
                if (!(EnumLIM.EnumQCGrade.外部质控.getValue().equals(sample.getQcGrade())
                        && (new QualityBlank().qcTypeValue().equals(qcType)
                        || new QualityParallel().qcTypeValue().equals(qcType)
                        || new QualityTransportBlank().qcTypeValue().equals(qcType)))) {
                    //是否跟随原样
                    if (qualityConfig.getIsFollowSample()) {
                        //原样的上方还是下方(true 是下 false 是 上)
                        Integer qcNumber = qualityConfig.getOrderNumber();
                        if (!qualityConfig.getUpordown()) {
                            qcNumber = 30 - qualityConfig.getOrderNumber() / 10;
                        }
//                    Optional<DtoAnalyseDataTemp> tempOptional = tempList.stream().filter(p -> p.getSampleId().equals(sample.getId())).findFirst();
//                    String code = tempOptional.isPresent() ? codeMap.getOrDefault(tempOptional.get().getGroupSampleId(), "") : sample.getCode();
                        String code = getGroupSampleCode(sample);
//                    tempOptional.ifPresent(p -> sample.setCode(codeMap.getOrDefault(p.getGroupSampleId(), "")));
                        orderNum = String.format("%s-%s-%s", "30", code, qcNumber);
                        //质控信息是否关联
//                    Optional<DtoAnalyseDataTemp> assDataTemp = tempList.stream().filter(p -> p.getSampleId()
//                            .equals(sample.getAssociateSampleId())).findFirst();
                        Optional<DtoSample> assDataTemp = Optional.ofNullable(findBySampleId(sample.getAssociateSampleId()));
                        if (assDataTemp.isPresent()) {
                            //关联的样品不是原样同时不是全程序空白和现场平行
                            Integer assType = assDataTemp.get().getQcType();
                            if (EnumPRO.EnumSampleCategory.串联样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                                assType = EnumPRO.EnumSampleCategory.串联样.getQcType();
                            }
                            if (EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(assDataTemp.get().getSampleCategory())) {
                                assType = EnumPRO.EnumSampleCategory.原样加原样.getQcType();
                            }
                            if (assType.equals(new QualityCipherParallel().qcTypeValue())) {
                                return orderNum;
                            }
                            if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(assDataTemp.get().getSampleCategory())
                                    && !(EnumLIM.EnumQCGrade.外部质控.getValue().equals(assDataTemp.get().getQcGrade())
                                    && (new QualityBlank().qcTypeValue().equals(assType)
                                    || new QualityParallel().qcTypeValue().equals(assType)
                                    || new QualityTransportBlank().qcTypeValue().equals(assType)))) {
                                DtoQualityConfig assQc = QualityTaskFactory.getInstance().getQcSample(assType).getQualityConfig(orderReviseVOList);
                                Integer number = assQc.getOrderNumber();
                                //原样为不关联样品
                                if (!assQc.getExtension()) {
                                    if (!EnumPRO.EnumSampleCategory.原样加原样.getValue().equals(sample.getSampleCategory())) {
                                        orderNum = String.format("%s-%s-%s", number, code, qualityConfig.getOrderNumber());
                                    } else {
                                        if (assQc.getIsFollowSample()) {
                                            if (!assQc.getUpordown()) {
                                                number = 30 - assQc.getOrderNumber() / 10;
                                            }
                                            orderNum = String.format("%s-%s-%s", "30", code, number);
                                        } else {
                                            orderNum = String.format("%s-%s-%s", number, code, qualityConfig.getOrderNumber());
                                        }
                                    }
                                } else {
                                    orderNum = String.format("%s-%s", orderNum, number);
                                }
                            }
                        }
                    } else {
                        //原样的上方还是下方(true 是下 false 是 上)
                        orderNum = String.format("%s-%s-%s", qualityConfig.getOrderNumber(), sample.getCode(), "30");
                    }
                }
            }
        }
        return orderNum;
    }

    protected String getGroupSampleCode(DtoSample sample) {
        if (StringUtil.isNotNull(sample)) {
            if (check(sample)) {
                return getGroupSampleCode(findBySampleId(sample.getAssociateSampleId()));
            } else {
                return sample.getCode();
            }
        } else {
            return "";
        }
    }

    private Boolean check(DtoSample sample) {
        return StringUtils.isNotNullAndEmpty(sample.getAssociateSampleId()) &&
                !UUIDHelper.GUID_EMPTY.equals(sample.getAssociateSampleId()) &&
                !sample.getQcType().equals((new QualityBlank().qcTypeValue())) &&
                //外部质控不需要往上穷举关联样，当原样处理
                !EnumLIM.EnumQCGrade.外部质控.getValue().equals(sample.getQcGrade())
                || (EnumLIM.EnumQCGrade.外部质控.getValue().equals(sample.getQcGrade())
                && sample.getQcType().equals((new QualityBlank().qcTypeValue())) && UUIDHelper.GUID_EMPTY.equals(sample.getQcId()));
    }

    protected List<OrderReviseVO> getQualityList() {
        XmlConfig xmlConfig = SpringContextAware.getBean(XmlConfig.class);
        return xmlConfig.getQcRulesConfigVO().getQualityReviseVo().getOrderAndRevise();
    }

    /**
     * 复制对应原样的样品参数
     *
     * @param samples 质控样列表
     */
    protected void copySampleParams(List<DtoSample> samples) {
        List<String> assSampleIdList = samples.stream().map(DtoSample::getAssociateSampleId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(assSampleIdList)) {
            List<DtoParamsData> yyParamsDataList = paramsDataRepository.findByObjectIdInAndObjectType(assSampleIdList, EnumParamsDataType.样品.getValue());
            //过滤出样品参数 2022-11-18 不进行过滤,复制所有参数
            List<DtoParamsData> instParamDataList = new ArrayList<>();
            for (DtoSample qcSample : samples) {
                String assId = qcSample.getAssociateSampleId();
                List<DtoParamsData> paramsDataListForCopy = yyParamsDataList.stream().filter(p -> assId.equals(p.getObjectId())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(paramsDataListForCopy)) {
                    for (DtoParamsData paramsData : paramsDataListForCopy) {
                        DtoParamsData copyData = new DtoParamsData();
                        BeanUtils.copyProperties(paramsData, copyData, "id");
                        copyData.setObjectId(qcSample.getId());
                        instParamDataList.add(copyData);
                    }
                }
            }
            if (StringUtil.isNotEmpty(instParamDataList)) {
                paramsDataRepository.save(instParamDataList);
            }
        }
    }

    /**
     * 获取原样样品相关数据结构
     *
     * @param sampleList 样品集合
     * @return 原样样品相关数据结构
     */
    private DtoLoadOutSample findLoadYuanSample(List<DtoSample> sampleList) {
        List<String> receiveIds = sampleList.stream().map(DtoSample::getReceiveId).filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList());
        DtoLoadOutSample source = new DtoLoadOutSample();
        //获取源样品
        source.setSample(sampleList);

        //获取源领样单
        if (receiveIds.size() > 0) {
            List<DtoReceiveSubSampleRecord> sourceReceiveSubSampleRecords = receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIds);
            source.setReceiveSubSampleRecord(sourceReceiveSubSampleRecords);
        }

        //获取分析数据
        List<DtoAnalyseData> sourceAnalyseDatas = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(source.getSampleIds());
        source.setAnalyseData(sourceAnalyseDatas);

        return source;
    }

    /**
     * 获取原样以及相关的现场关联样
     *
     * @param ids 样品id集合
     * @return 原样以及相关的现场关联样
     */
    @Override
    public List<DtoSample> findAllLocalSample(Collection<String> ids) {
        List<DtoSample> sampleList = super.findAll(ids);
        sampleList = sampleList.stream().filter(p -> p.getSampleCategory().equals(EnumSampleCategory.原样.getValue())).sorted
                (Comparator.comparing(DtoSample::getSampleTypeId).thenComparing(DtoSample::getRedFolderName)).collect(Collectors.toList());

        List<DtoSample> localAssociateSampleList = this.getLocalAssociateSample(ids);
        //空白样可以添加串联样 所有质控都可以添加串联样
        List<String> qcSamIds = localAssociateSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(qcSamIds)) {
            List<DtoSample> kbclAssociateSampleList = this.getLocalAssociateSample(qcSamIds);
            sampleList.addAll(kbclAssociateSampleList);
        }
        sampleList.addAll(localAssociateSampleList);
        this.checkAnalyseItemNamesOfSamples(sampleList);
        sampleList = this.sortByList(sampleList);
        return sampleList;
    }

    /**
     * 获取原样的现场关联样
     *
     * @param ids 原样id集合
     * @return 原样的现场关联样
     */
    @Override
    public List<DtoSample> getLocalAssociateSample(Collection<String> ids) {
        if (ids.size() > 0) {
            List<DtoSample> samples = repository.findByAssociateSampleIdIn(ids);
            samples = samples.stream().filter(p -> p.getSampleCategory().equals(EnumSampleCategory.质控样.getValue()) ||
                    p.getSampleCategory().equals(EnumSampleCategory.串联样.getValue()) ||
                    EnumSampleCategory.洗涤剂.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());

            List<String> qcIds = samples.stream().map(DtoSample::getQcId).filter(qcId -> !UUIDHelper.GUID_EMPTY.equals(qcId)).collect(Collectors.toList());
            List<String> clSampleIds = samples.stream().filter(p-> p.getSampleCategory().equals(EnumSampleCategory.串联样.getValue()))
                    .map(DtoSample::getId).filter(qcId -> !UUIDHelper.GUID_EMPTY.equals(qcId)).collect(Collectors.toList());
            List<DtoAnalyseData> clAnalyseDataList = StringUtil.isNotEmpty(clSampleIds) ? analyseDataRepository.findBySampleIdIn(clSampleIds) : new ArrayList<>();
            Map<String, List<DtoAnalyseData>> clAnalyseDataMap = clAnalyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
            List<DtoQualityControl> qcList = qcIds.size() > 0 ? qualityControlRepository.findAll(qcIds) : new ArrayList<>();
            if (StringUtil.isNotNull(qcList) && qcList.size() > 0) {
                Map<String, DtoQualityControl> qcMap = qcList.stream().filter(p -> p.getQcGrade().equals(EnumLIM.EnumQCGrade.外部质控.getValue())).collect(Collectors.toMap(DtoQualityControl::getId, qc -> qc));
                List<DtoSample> list = new ArrayList<>();
                for (DtoSample sample : samples) {
                    if (sample.getSampleCategory().equals(EnumSampleCategory.串联样.getValue()) || EnumSampleCategory.洗涤剂.getValue().equals(sample.getSampleCategory())) {
                        // 排除实验室串联
                        if (sample.getSampleCategory().equals(EnumSampleCategory.串联样.getValue()) && clAnalyseDataMap.containsKey(sample.getId())){
                            List<DtoAnalyseData> analyseDataList = clAnalyseDataMap.get(sample.getId());
                            if (analyseDataList.stream().allMatch(p-> p.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                                    && p.getQcType().equals(EnumLIM.EnumQCType.串联样.getValue()))){
                                continue;
                            }
                        }
                        list.add(sample);
                    } else if (qcMap.containsKey(sample.getQcId())) {
                        sample.setQcGrade(EnumLIM.EnumQCGrade.外部质控.getValue());
                        sample.setQcType(qcMap.get(sample.getQcId()).getQcType());
                        list.add(sample);
                    }
                }
                return list;
            }

            //说明不存在质控样，只存在现场串联样或本身就为空
            return samples;
        } else {
            throw new BaseException("请选择有效样品！");
        }
    }

    /**
     * 更新样品中冗余的分析项目名称
     *
     * @param samples 需要更新的样品集合
     */
    private void checkAnalyseItemNamesOfSamples(List<DtoSample> samples) {
        if (StringUtil.isNotEmpty(samples)) {
            List<String> sampleIds = samples.parallelStream().map(DtoSample::getId).collect(Collectors.toList());
            //获取参数样品中所有的分析项目
            List<DtoAnalyseData> analyseData = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
            List<String> testIds = analyseData.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> tests = StringUtil.isNotEmpty(testIds) ? testService.findAll(testIds) : new ArrayList<>();
            List<String> parentTestIds = tests.stream().filter(t -> StringUtils.isNotNullAndEmpty(t.getParentId()) && !UUIDHelper.GUID_EMPTY.equals(t.getParentId())).map(DtoTest::getParentId).distinct().collect(Collectors.toList());
            List<DtoTest> parentTests = StringUtil.isNotEmpty(parentTestIds) ? testService.findAll(parentTestIds) : new ArrayList<>();
            for (DtoSample sample : samples) {
                //获取当前样品中的分析项目
                List<DtoAnalyseData> analyseDataOfSampleItem = analyseData.parallelStream()
                        .filter(a -> a.getSampleId().equals(sample.getId())).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(analyseDataOfSampleItem)) {
                    List<String> testIds2Sample = analyseDataOfSampleItem.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
                    List<String> analyzeItemNames = new ArrayList<>();
                    tests.stream().filter(t -> testIds2Sample.contains(t.getId())).collect(Collectors.groupingBy(DtoTest::getParentId)).forEach((parentId, childTests) -> {
                        DtoTest parentTest = parentTests.stream().filter(t -> t.getId().equals(parentId)).findFirst().orElse(null);
                        if (UUIDHelper.GUID_EMPTY.equals(parentId) || StringUtil.isNull(parentTest)) {
                            analyzeItemNames.addAll(childTests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList()));
                        } else if (parentTest != null) {
                            if (childTests.size() >= parentTest.getMergeBase()) {
                                analyzeItemNames.add(parentTest.getRedAnalyzeItemName());
                            } else {
                                analyzeItemNames.addAll(childTests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList()));
                            }
                        }
                    });
                    Collections.sort(analyzeItemNames);
                    sample.setRedAnalyzeItems(String.join(",", analyzeItemNames));
//                    List<String> analyseItemNamesOfSampleItem = analyseDataOfSampleItem.parallelStream()
//                            .sorted(Comparator.comparing(DtoAnalyseData::getRedAnalyzeItemName)).map(DtoAnalyseData::getRedAnalyzeItemName).distinct().collect(Collectors.toList());
//                    //更新字段
//                    sample.setRedAnalyzeItems(org.apache.commons.lang3.StringUtils.join(analyseItemNamesOfSampleItem, ','));
                }
            }
        }
    }

    /**
     * 获取待选的样品
     *
     * @param projectId      项目id
     * @param sampleTypeId   检测类型id
     * @param sampleFolderId 点位id
     * @param key            关键字
     * @return 原样的现场关联样
     */
    @Override
    public List<DtoSample> findRemovedSamples(String projectId, String sampleTypeId, String sampleFolderId, String key) {
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoSample> pb = new PageBean<>();
        pb.setEntityName("DtoSample s,DtoSampleType st,DtoProject p");
        if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
            pb.setSelect("select s");
        } else {
            pb.setSelect("select s,st.typeName,p.projectTypeId,p.projectName");
        }
        pb.addCondition(" and s.isDeleted = 0");
        pb.addCondition(" and s.sampleTypeId = st.id");
        pb.addCondition(" and s.projectId = p.id");
        pb.addCondition(" and s.receiveId = :receiveId");
        values.put("receiveId", UUIDHelper.GUID_EMPTY);
        if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
            pb.addCondition(" and s.projectId = :projectId");
            values.put("projectId", projectId);
        }
        if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(sampleTypeId)) {
            pb.addCondition(" and s.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", sampleTypeId);
        }
        if (StringUtils.isNotNullAndEmpty(sampleFolderId) && !UUIDHelper.GUID_EMPTY.equals(sampleFolderId)) {
            pb.addCondition(" and s.sampleFolderId = :sampleFolderId");
            values.put("sampleFolderId", sampleFolderId);
        }
        pb.addCondition(" and s.code <> '' and s.code is not null");
        pb.addCondition(" and (s.samplingStatus = :samplingStatus1 or s.samplingStatus = :samplingStatus2)");
        values.put("samplingStatus1", EnumSamplingStatus.已经完成取样.getValue());
        values.put("samplingStatus2", EnumSamplingStatus.采样中.getValue());
        pb.addCondition(" and s.status <> :status");
        values.put("status", EnumSampleStatus.样品作废.toString());
        if (StringUtils.isNotNullAndEmpty(key)) {
            pb.addCondition(" and (s.code like :key or p.projectName like :key or p.projectCode like :key)");
            values.put("key", "%" + key + "%");
        }

        List<Object> datas = comRepository.find(pb.getAutoQuery(), values);
        List<DtoSample> sampleDatas = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(projectId) && !UUIDHelper.GUID_EMPTY.equals(projectId)) {
            sampleDatas = datas.stream().map(p -> (DtoSample) p).collect(Collectors.toList());

            DtoProject project = projectRepository.findOne(projectId);
            DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());
            List<String> sampleTypeIds = sampleDatas.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypes = sampleTypeIds.size() > 0 ? sampleTypeService.findRedisByIds(sampleTypeIds) : new ArrayList<>();
            Map<String, String> samTypeMap = sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));

            for (DtoSample sample : sampleDatas) {
                sample.setSampleTypeName(samTypeMap.getOrDefault(sample.getSampleTypeId(), ""));
                sample.setProjectName(project.getProjectName());
                sample.setProjectTypeName(projectType.getName());
            }
            sampleDatas.sort(Comparator.comparing(DtoSample::getCode));
        } else {
            for (Object data : datas) {
                Object[] objs = (Object[]) data;
                DtoSample sample = (DtoSample) objs[0];
                sample.setSampleTypeName((String) objs[1]);
                sample.setProjectTypeId((String) objs[2]);
                sample.setProjectName((String) objs[3]);
                sampleDatas.add(sample);
            }

            List<String> projectTypeIds = sampleDatas.stream().map(DtoSample::getProjectTypeId).distinct().collect(Collectors.toList());
            List<DtoProjectType> projectTypeList = projectTypeService.findRedisByIds(projectTypeIds);
            Map<String, String> projectTypeMap = projectTypeList.stream().collect(Collectors.toMap(DtoProjectType::getId, DtoProjectType::getName));
            for (DtoSample sample : sampleDatas) {
                sample.setProjectTypeName(projectTypeMap.getOrDefault(sample.getProjectTypeId(), ""));
            }
            sampleDatas.sort(Comparator.comparing(DtoSample::getProjectId).thenComparing(DtoSample::getSampleTypeId).thenComparing(DtoSample::getCode));
        }

        return sampleDatas;
    }

    /**
     * 获取样品下的模板明细
     *
     * @param para 传参
     * @return 模板明细
     */
    @Override
    public List<Map<String, Object>> findSampleTemplateDetail(DtoSampleItemParams para) {
        List<Map<String, Object>> templates = new ArrayList<>();

        //根据检测类型获取对应检测类型下的模板信息
        DtoSampleType samType = sampleTypeService.findOne(para.getSampleTypeId());
        List<DtoSampleTypeTemplate> templateList = sampleType2TestService.findTemplateBySampleTypeIdIn(Arrays.asList(para.getSampleTypeId(), samType.getParentId()));
        templateList.sort(Comparator.comparing(DtoSampleTypeTemplate::getTypeName));
        DtoSampleTypeTemplate defaultTemp = new DtoSampleTypeTemplate();
        defaultTemp.setId("");
        defaultTemp.setTypeName("");
        defaultTemp.setTestList(new ArrayList<>());
        templateList.add(0, defaultTemp);

        //获取对应样品下的所有指标信息
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(para.getSampleIds());

        analyseDataList = new ArrayList<>(analyseDataList.stream().
                collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values());

        List<DtoTest> totalTestList = new ArrayList<>();
        for (DtoAnalyseData analyseData : analyseDataList) {
            DtoTest test = new DtoTest();
            test.setId(analyseData.getTestId());
            test.setAnalyzeItemId(analyseData.getAnalyseItemId());
            test.setRedAnalyzeItemName(analyseData.getRedAnalyzeItemName());
            totalTestList.add(test);
        }

        if (para.getParamsConfig().size() > 0) {
            List<String> analyzeItemIds = para.getParamsConfig().stream().map(DtoParamsConfig::getAnalyzeItemId).distinct().collect(Collectors.toList());
            totalTestList = totalTestList.stream().filter(p -> analyzeItemIds.contains(p.getAnalyzeItemId())).collect(Collectors.toList());
        }
        totalTestList.sort(Comparator.comparing(DtoTest::getRedAnalyzeItemName));

        //遍历模板，筛选模板下的指标为对应样品下的指标，并添加对应指标下关联的参数配置id集合
        for (DtoSampleTypeTemplate template : templateList) {
            Map<String, Object> mapObj = new HashMap<>();
            mapObj.put("id", template.getId());
            mapObj.put("typeName", template.getTypeName());
            List<String> testIds = template.getTestList().stream().map(DtoTest::getId).collect(Collectors.toList());
            List<DtoTest> thisTest = StringUtils.isNotNullAndEmpty(template.getId()) ?
                    totalTestList.stream().filter(p -> testIds.contains(p.getId())).collect(Collectors.toList()) : totalTestList;
            thisTest.sort(Comparator.comparing(DtoTest::getRedAnalyzeItemName));
            List<Map<String, Object>> tests = new ArrayList<>();
            for (DtoTest test : thisTest) {//模板下的测试项目默认不存在重复
                Map<String, Object> testObj = new HashMap<>();
                testObj.put("analyzeItemId", test.getAnalyzeItemId());
                testObj.put("redAnalyzeItemName", test.getRedAnalyzeItemName());
                tests.add(testObj);
            }
            if (tests.size() > 0) {
                mapObj.put("analyzeItem", tests);
                templates.add(mapObj);
            }
        }
        return templates;
    }

    /**
     * 获取质控样样品详情
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param testId       测试项目id
     * @return 质控样样品详情
     */
    @Override
    public List<DtoQCSample> findQCSample(String projectId, String sampleTypeId, String testId) {
        DtoTest test = testService.findOne(testId);
        List<DtoQCSample> qcSamples = test.getIsCompleteField() ? this.findQCSampleCompleteField(projectId, sampleTypeId, testId) : this.findQCSampleInRecord(projectId, sampleTypeId, testId);
        if (!test.getIsCompleteField()) {
            qcSamples.addAll(this.findQCSampleInner(projectId, sampleTypeId, testId));
        }
        qcSamples.sort(Comparator.comparing(DtoQCSample::getSampleCategory).thenComparing(Comparator.comparing(DtoQCSample::getQcGrade))
                .thenComparing(Comparator.comparing(DtoQCSample::getQcType)).thenComparing(Comparator.comparing(DtoQCSample::getSampleCode)));

        return qcSamples;
    }

    /**
     * 根据样品ID查询样品日志
     *
     * @param sampleId 样品id
     * @return 样品相关日志信息
     */
    @Override
    public List<DtoLog> findSampleLog(String sampleId) {
        List<DtoLogForSample> sampleLogList = logForSampleRepository.findByObjectId(sampleId);
        List<DtoLog> logList = sampleLogList.parallelStream().map(DtoLog::new).collect(Collectors.toList());
        //数据日志
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdAndIsDeletedFalse(sampleId);
        if (StringUtil.isNotEmpty(analyseDataList)) {
            List<String> analyseDataIds = analyseDataList.parallelStream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            List<DtoLogForData> analyseDataLogList = logForDataRepository.findByObjectIdIn(analyseDataIds);
            logList.addAll(analyseDataLogList.parallelStream().map(DtoLog::new).collect(Collectors.toList()));
        }
        List<DtoUser> userList = userService.findAll();
        logList = newLogService.dealLogs(logList, userList);
        return logList.stream().sorted(Comparator.comparing(DtoLog::getOperateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取实验室指标的质控样样品详情（送样单内）
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param testId       测试项目id
     * @return 质控样样品详情
     */
    private List<DtoQCSample> findQCSampleInRecord(String projectId, String sampleTypeId, String testId) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoQCSample(");
        stringBuilder.append("s.id,s.code,s.redFolderName,r.recordCode,w.workSheetCode,s.sampleCategory,a.qcGrade,a.qcType)");
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoQCSample> pb = new PageBean<>();
        pb.setEntityName("DtoAnalyseData a,DtoSample s,DtoReceiveSampleRecord r,DtoWorkSheetFolder w");
        pb.setSelect(stringBuilder.toString());
        pb.addCondition(" and s.isDeleted = 0  and a.isDeleted = 0 ");
        pb.addCondition(" and a.sampleId = s.id");
        pb.addCondition(" and s.receiveId = r.id");
        pb.addCondition(" and s.sampleCategory in :category");
        List<Integer> categorys = new ArrayList<>();
        categorys.add(EnumSampleCategory.原样.getValue());
        categorys.add(EnumSampleCategory.质控样.getValue());
        values.put("category", categorys);
        pb.addCondition(" and a.workSheetFolderId = w.id");
        pb.addCondition(" and r.projectId = :projectId");
        values.put("projectId", projectId);
        pb.addCondition(" and s.sampleTypeId = :sampleTypeId");
        values.put("sampleTypeId", sampleTypeId);
        pb.addCondition(" and a.testId = :testId");
        values.put("testId", testId);
        pb.addCondition(" and a.dataStatus <> :dataStatus");
        values.put("dataStatus", EnumAnalyseDataStatus.未测.getValue());
        return comRepository.find(pb.getAutoQuery(), values);
    }

    /**
     * 获取实验室指标的质控样样品详情（室内）
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param testId       测试项目id
     * @return 质控样样品详情
     */
    private List<DtoQCSample> findQCSampleInner(String projectId, String sampleTypeId, String testId) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoQCSample(");
        stringBuilder.append("s.id,s.code,s.redFolderName,w.workSheetCode,s.sampleCategory,a.qcGrade,a.qcType)");
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoQCSample> pb = new PageBean<>();
        pb.setEntityName("DtoAnalyseData a,DtoSample s,DtoWorkSheetFolder w,DtoProject2WorkSheetFolder p2w");
        pb.setSelect(stringBuilder.toString());
        pb.addCondition(" and s.isDeleted = 0 and a.isDeleted = 0 ");
        pb.addCondition(" and a.sampleId = s.id");
        pb.addCondition(" and a.qcGrade = :qcGrade");
        values.put("qcGrade", EnumLIM.EnumQCGrade.内部质控.getValue());
        pb.addCondition(" and a.workSheetFolderId = p2w.workSheetFolderId");
        pb.addCondition(" and p2w.workSheetFolderId = w.id");
        pb.addCondition(" and p2w.projectId = :projectId");
        values.put("projectId", projectId);
        pb.addCondition(" and s.sampleTypeId = :sampleTypeId");
        values.put("sampleTypeId", sampleTypeId);
        pb.addCondition(" and a.testId = :testId");
        values.put("testId", testId);
        return comRepository.find(pb.getAutoQuery(), values);
    }

    /**
     * 获取现场指标的质控样样品详情
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     * @param testId       测试项目id
     * @return 质控样样品详情
     */
    private List<DtoQCSample> findQCSampleCompleteField(String projectId, String sampleTypeId, String testId) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoQCSample(");
        stringBuilder.append("s.id,s.code,s.redFolderName,r.recordCode,'',s.sampleCategory,a.qcGrade,a.qcType)");
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoQCSample> pb = new PageBean<>();
        pb.setEntityName("DtoAnalyseData a,DtoSample s,DtoReceiveSampleRecord r");
        pb.setSelect(stringBuilder.toString());
        pb.addCondition(" and s.isDeleted = 0 and a.isDeleted = 0 ");
        pb.addCondition(" and a.sampleId = s.id");
        pb.addCondition(" and s.receiveId = r.id");
        pb.addCondition(" and s.sampleCategory in :category");
        List<Integer> categorys = new ArrayList<>();
        categorys.add(EnumSampleCategory.原样.getValue());
        categorys.add(EnumSampleCategory.质控样.getValue());
        values.put("category", categorys);
        pb.addCondition(" and r.projectId = :projectId");
        values.put("projectId", projectId);
        pb.addCondition(" and s.sampleTypeId = :sampleTypeId");
        values.put("sampleTypeId", sampleTypeId);
        pb.addCondition(" and a.testId = :testId");
        values.put("testId", testId);
        pb.addCondition(" and a.dataStatus <> :dataStatus");
        values.put("dataStatus", EnumAnalyseDataStatus.未测.getValue());
        return comRepository.find(pb.getAutoQuery(), values);
    }

    //#region 公有方法

    /**
     * 新增参数数据
     *
     * @param sampleId       样品id
     * @param paramsDataList 参数数据
     */
    @Transactional
    @Override
    public void saveSampleParams(String sampleId, List<DtoParamsConfig> paramsDataList) {
        if (StringUtil.isNull(paramsDataList) || paramsDataList.size() == 0) {
            return;
        }

        List<DtoParamsData> list = new ArrayList<>();
        for (DtoParamsConfig pd : paramsDataList) {
            DtoParamsData paramsData = new DtoParamsData();
            paramsData.setObjectId(sampleId);
            paramsData.setObjectType(EnumParamsDataType.样品.getValue());
            paramsData.setParamsConfigId(pd.getId());
            paramsData.setParamsValue(pd.getParamsValue());
            paramsData.setParamsName(pd.getAlias());
            paramsData.setDimension(pd.getDimension());
            paramsData.setDimensionId(pd.getDimensionId());
            paramsData.setOrderNum(pd.getOrderNum());
            paramsData.setGroupId(UUIDHelper.GUID_EMPTY);
            list.add(paramsData);
        }

        if (list.size() > 0) {
            comRepository.insertBatch(list);
        }
    }

    /**
     * 核对点位信息
     *
     * @param dto 样品
     */
    @Transactional
    public void checkSampleFolder(DtoSample dto) {
        String sampleFolderId = dto.getSampleFolderId();
        //原有点位下的其它样品数
        Integer sampleCount = repository.countBySampleFolderIdAndIdNot(sampleFolderId, dto.getId());
        //改后的点位
        List<DtoSampleFolder> sampleFolderList = sampleFolderRepository.findByProjectIdAndSampleTypeIdAndWatchSpot(dto.getProjectId(), dto.getSampleTypeId(), dto.getRedFolderName());
        DtoSampleFolder sampleFolder = StringUtil.isNotEmpty(sampleFolderList) ? sampleFolderList.get(0) : null;
        if (StringUtil.isNotNull(sampleFolder)) {//存在被修改后的该点位
            //直接将原有的频次下的点位id替换成修改后的点位id
            samplingFrequencyService.updateSampleFolderId(dto.getSamplingFrequencyId(), sampleFolder.getId());
            samplingFrequencyTestRepository.updateSampleFolderId(dto.getSamplingFrequencyId(), sampleFolder.getId());
            if (sampleCount.equals(0)) {//若不存在其它样品数，删除原有点位
                sampleFolderRepository.logicDeleteById(sampleFolderId, new Date());
            }
            dto.setSampleFolderId(sampleFolder.getId());
            dto.setRedFolderName(dto.getRedFolderName() + String.format("(%d-%d-%d)", dto.getCycleOrder(), dto.getTimesOrder(), dto.getSampleOrder()));
        } else {//不存在修改后的点位
            if (sampleCount > 0) {//存在原有点位，则新建一个点位
                String samplingFrequencyId = dto.getSamplingFrequencyId();
                DtoSamplingFrequency frequency = sampleFolderService.addOutsideFolder(UUIDHelper.GUID_EMPTY, dto, new ArrayList<>());
                dto.setSampleFolderId(frequency.getSampleFolderId());
                dto.setSamplingFrequencyId(frequency.getId());
                dto.setRedFolderName(frequency.getRedFolderName());
                samplingFrequencyService.delete(samplingFrequencyId);//删除原有频次
                samplingFrequencyTestRepository.updateFrequencyIdAndSampleFolderId(samplingFrequencyId, frequency.getId(), frequency.getSampleFolderId());//将原有频次指标的频次id及点位id替换成新的
            } else {//否则直接修改原有点位名称
                DtoSampleFolder folder = new DtoSampleFolder();
                folder.setId(sampleFolderId);
                folder.setWatchSpot(dto.getRedFolderName());
                comRepository.merge(folder);
                dto.setRedFolderName(dto.getRedFolderName() + String.format("(%d-%d-%d)", dto.getCycleOrder(), dto.getTimesOrder(), dto.getSampleOrder()));
            }
        }
    }

    /**
     * 创建质控样
     *
     * @param sampleIds         原样id集合
     * @param qcType            质控样类型
     * @param qcGrade           质控类型
     * @param workSheetFolderId 工作单id
     * @param qualityControls   质控信息列表
     * @return 完成创建的质控样列表
     */
    @Override
    @Transactional
    public List<DtoSample> createQcSamples(Collection<String> sampleIds, int qcType, int qcGrade, String workSheetFolderId, List<DtoQualityControl> qualityControls) {
        List<DtoSample> result = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleIds)) {
            List<DtoSample> samples = repository.findAll(sampleIds);
            for (DtoSample sample : samples) {
                if (QualityTaskFactory.getInstance().getQcSample(qcType).qcTypeName().equals("替代")) {
                    //替代样需要根据前端传递的多个替代物，每个替代物都对应添加一个质控样
                    for (DtoQualityControl qualityControl : qualityControls) {
                        DtoSample qcSample = new DtoSample();
                        fillingQcSampleInfo(sample, qcSample, qcType, qualityControl);
                        if (EnumLIM.EnumQCGrade.内部质控.getValue().equals(qcGrade)) {
                            qcSample.setCode(createInnerSampleCode(qcSample.getSampleCategory(), qcGrade, qcType, qcSample.getAssociateSampleId(), workSheetFolderId, ""));
                        }
                        result.add(qcSample);
                    }
                } else {
                    DtoSample qcSample = new DtoSample();
                    fillingQcSampleInfo(sample, qcSample, qcType, null);
                    if (EnumLIM.EnumQCGrade.内部质控.getValue().equals(qcGrade)) {
                        qcSample.setCode(createInnerSampleCode(qcSample.getSampleCategory(), qcGrade, qcType, sample.getAssociateSampleId(), workSheetFolderId, ""));
                    }
                    result.add(qcSample);
                }
            }
        }
        return result;
    }

    /**
     * 获取添加的点位数据
     *
     * @param dto       样品dto
     * @param outSample 外部样品
     * @return 点位频次数据
     */
    protected DtoSamplingFrequency getFrequency(DtoSample dto, DtoOutSample outSample) {
        return sampleFolderService.addOutsideFolder(outSample.getFixedPointId(), dto, outSample.getTest());
    }

    /**
     * 填充创建完成的质控样信息
     *
     * @param sample         原样
     * @param qcSample       质控样
     * @param qcType         质控类型
     * @param qualityControl 质控信息对象
     */
    private void fillingQcSampleInfo(DtoSample sample, DtoSample qcSample, int qcType, DtoQualityControl qualityControl) {
        qcSample.setAssociateSampleId(sample.getId());
        qcSample.setCycleOrder(sample.getCycleOrder());
        qcSample.setTimesOrder(sample.getTimesOrder());
        qcSample.setSampleOrder(sample.getSampleOrder());
        qcSample.setSamplingPersonId(sample.getSamplingPersonId());
        qcSample.setSampleTypeId(sample.getSampleTypeId());
        qcSample.setSampleCategory(EnumSampleCategory.质控样.getValue());
        qcSample.setInspectedEnt(sample.getInspectedEnt());
        qcSample.setInspectedEntId(sample.getInspectedEntId());
        qcSample.setInceptTime(sample.getInceptTime());
        qcSample.setSamplingTimeBegin(sample.getSamplingTimeBegin());
        qcSample.setSamplingTimeEnd(sample.getSamplingTimeEnd());
        qcSample.setStatus(sample.getStatus());
        qcSample.setSamplingStatus(sample.getSamplingStatus());
        qcSample.setInnerReceiveStatus(sample.getInnerReceiveStatus());
        qcSample.setAnanlyzeStatus(sample.getAnanlyzeStatus());
        qcSample.setStoreStatus(sample.getStoreStatus());
        qcSample.setMakeStatus(sample.getMakeStatus());
        QualityTaskFactory factory = QualityTaskFactory.getInstance();
        QualityControlKind controlKind = factory.getQcSample(qcType);
        if (controlKind != null) {
            qcSample.setRedFolderName(controlKind.qcTypeName());
        }
        if (StringUtil.isNotNull(qualityControl) && QualityTaskFactory.getInstance().getQcSample(qcType).qcTypeName().equals("替代")) {
            qcSample.setQcId(qualityControl.getId());
        }
    }

    /**
     * 获取质控样
     *
     * @param sampleCategory     样品类别
     * @param qcGrade            质控等级
     * @param qcType             质控类型
     * @param oldSample          原样
     * @param qcValue            质控值（标准样的值/加标的值/平行样为空）
     * @param qcAliasName        质控样别名
     * @param qcCode             标样编号
     * @param qcVolume           加标体积
     * @param qcValidDate        标样的有效期
     * @param currentUserId      指定添加质控样品的人员
     * @param dtoWorkSheetFolder 检测单数据
     * @param project            项目
     * @param sampleCode         样品编号
     */
    private Map<String, Object> getAssociateSample(Integer sampleCategory,
                                                   Integer qcGrade,
                                                   Integer qcType,
                                                   DtoSample oldSample,
                                                   String qcValue,
                                                   String qcAliasName,
                                                   String qcCode,
                                                   String qcVolume,
                                                   Date qcValidDate,
                                                   String currentUserId,
                                                   DtoWorkSheetFolder dtoWorkSheetFolder,
                                                   DtoProject project,
                                                   String sampleCode,
                                                   Date qcStandardDate,
                                                   String qcStandardId,
                                                   String qcConcentration) {
        Map<String, Object> returnMap = new HashMap<>();
        DtoSample sample = new DtoSample(true);
        String associateSampleId = this.getAssociateSampleId(sampleCategory, qcGrade, qcType, oldSample);
        if (sampleCategory.equals(EnumSampleCategory.质控样.getValue())) {
            DtoQualityControl qc = new DtoQualityControl();
            qc.setAssociateSampleId(associateSampleId);
            qc.setQcValidDate(qcValidDate);
            qc.setQcGrade(qcGrade);
            qc.setQcType(qcType);
            qc.setQcValue(qcValue);
            qc.setQcVolume(qcVolume);
            qc.setQcCode(qcCode);
            qc.setQaId(currentUserId);
            qc.setQcStandardDate(qcStandardDate);
            qc.setQcStandardId(qcStandardId);
            qc.setQcConcentration(qcConcentration);
//            qualityControlService.save(qc);
            sample.setQcId(qc.getId());
            returnMap.put("qualityControl", qc);
        } else {
            sample.setQcId(UUIDHelper.GUID_EMPTY);
        }

        sample.setAssociateSampleId(associateSampleId);
        sample.setAssociateSampleCode(UUIDHelper.GUID_EMPTY.equals(associateSampleId) ? "" : oldSample.getCode());
        sample.setSampleCategory(sampleCategory);
        sample.setCycleOrder(oldSample.getCycleOrder());
        sample.setTimesOrder(oldSample.getTimesOrder());
        sample.setSampleOrder(oldSample.getSampleOrder());
        sample.setRedFolderName(this.getAssociateRedFolderName(sampleCategory, qcGrade, qcType, oldSample));
        if (StringUtil.isNotEmpty(qcAliasName)) {
            sample.setRedFolderName(qcAliasName);
        }
        sample.setInceptTime(new Date());
        sample.setSamplingPersonId(currentUserId);
        sample.setSampleTypeId(oldSample.getSampleTypeId());
        sample.setInspectedEnt(oldSample.getInspectedEnt());
        sample.setInspectedEntId(oldSample.getInspectedEntId());
        sample.setSamplingTimeBegin(oldSample.getSamplingTimeBegin());
        sample.setSamplingTimeEnd(oldSample.getSamplingTimeEnd());
        sample.setStoreStatus(EnumStoreStatus.不能存储.getValue());
        sample.setMakeStatus(EnumMakeStatus.不需要制样.getValue());
        sample.setKeepLongTime(oldSample.getKeepLongTime());
        sample.setStorageConditions(oldSample.getStorageConditions());
        sample.setIsKeep(oldSample.getIsKeep());
        sample.setPack(oldSample.getPack());
        sample.setSampleWeight(oldSample.getSampleWeight());
        sample.setWeightOrQuantity(oldSample.getWeightOrQuantity());
        sample.setSamColor(oldSample.getSamColor());
        sample.setSampleExplain(oldSample.getSampleExplain());
        sample.setStorageConditions(oldSample.getStorageConditions());
        sample.setVolume(oldSample.getVolume());
        sample.setSamplingPlace(oldSample.getSamplingPlace());
        sample.setRemark(oldSample.getRemark());
        sample.setLoseEfficacyTime(oldSample.getLoseEfficacyTime());
        sample.setSamKind(oldSample.getSamKind());
        sample.setMakeSamPerId(oldSample.getMakeSamPerId());
        sample.setIsQualified(oldSample.getIsQualified());
        sample.setSampleSource(oldSample.getSampleSource());
        sample.setOriginalStatus(oldSample.getOriginalStatus());
        sample.setIsReturned(oldSample.getIsReturned());
        sample.setPreTreatmentCases(oldSample.getPreTreatmentCases());
        sample.setUnqualifiedReason(oldSample.getUnqualifiedReason());
        sample.setDisposeMeasure(oldSample.getDisposeMeasure());
        sample.setConsistencyValidStatus(oldSample.getConsistencyValidStatus());
        sample.setSubProjectId(UUIDHelper.GUID_EMPTY);
        sample.setDataChangeStatus(EnumSampleChangeStatus.未变更.getValue());
        sample.setParentSampleId(UUIDHelper.GUID_EMPTY);
        sample.setSamplingConfig(EnumSamplingConfig.未分配.getValue());
        sample.setIsPrint(EnumPrintStatus.未打印.getValue());
        sample.setBlindType(EnumSampleBlindType.非盲样.getValue());
        sample.setSignerId(UUIDHelper.GUID_EMPTY);
        sample.setSignTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        sample.setIsOutsourcing(0);
        sample.setLastNewSubmitTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));

        if (qcGrade.equals(EnumLIM.EnumQCGrade.内部质控.getValue()) || sampleCategory.equals(EnumSampleCategory.原样加原样.getValue())) {
            sample.setStatus(EnumSampleStatus.样品在检.toString());
            sample.setSamplingStatus(EnumSamplingStatus.已经完成取样.getValue());
            sample.setInnerReceiveStatus(EnumInnerReceiveStatus.已经领取.getValue());
            sample.setAnanlyzeStatus(EnumAnalyzeStatus.可以分析.getValue());
            //如果传了样品编号过来，那么用同一个样品编号
            if (StringUtil.isNotEmpty(sampleCode)) {
                sample.setCode(sampleCode);
            } else {
                String innerCode = this.createInnerSampleCode(sampleCategory, qcGrade, qcType, oldSample, dtoWorkSheetFolder, qcCode);
                sample.setCode(innerCode);
            }
        } else {
            if (StringUtils.isNotNullAndEmpty(oldSample.getCode())) {
                log.info("==========================开始创建外部质控样样品编号==========================");
                log.info("==========================原样样品编号" + oldSample.getCode());
                log.info("==========================原样采样时间" + oldSample.getSamplingTimeBegin());
                if ("1753-01-01 00:00:00".equals(DateUtil.dateToString(oldSample.getSamplingTimeBegin(), DateUtil.FULL))) {
                    log.warn("===================================创建样品编号时，原样采样时间为1753");
                }
                DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());
                DtoGenerateSN generateSN = this.createSampleCode(project, projectType, oldSample.getSampleTypeId(), oldSample.getSampleFolderId(), oldSample.getSamplingTimeBegin(),
                        UUIDHelper.GUID_EMPTY, oldSample.getId(), false, StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "",
                        true, sample.getAssociateSampleId(), sample.getQcId(), sampleCategory, qcType, qcGrade, false, "");
                sample.setCode(generateSN.getCode());
                returnMap.put("generateSN", generateSN);
            } else {
                sample.setCode("");
            }
            sample.setReceiveId(oldSample.getReceiveId());

            EnumSamplingStatus samplingStatus = EnumSamplingStatus.getByValue(oldSample.getSamplingStatus());
            switch (samplingStatus) {
                case 不需要取样:
                    sample.setStatus(EnumSampleStatus.样品未采样.toString());
                    sample.setSamplingStatus(EnumSamplingStatus.不需要取样.getValue());
                    sample.setInnerReceiveStatus(EnumInnerReceiveStatus.不能领取.getValue());
                    sample.setAnanlyzeStatus(EnumAnalyzeStatus.不能分析.getValue());
                    break;

                case 需要取样还未取样:
                    sample.setStatus(EnumSampleStatus.样品未采样.toString());
                    sample.setSamplingStatus(EnumSamplingStatus.需要取样还未取样.getValue());
                    sample.setInnerReceiveStatus(EnumInnerReceiveStatus.不能领取.getValue());
                    sample.setAnanlyzeStatus(EnumAnalyzeStatus.不能分析.getValue());
                    break;

                case 采样中:
                    sample.setStatus(EnumSampleStatus.样品未采样.toString());
                    sample.setSamplingStatus(EnumSamplingStatus.采样中.getValue());
                    sample.setInnerReceiveStatus(EnumInnerReceiveStatus.不能领取.getValue());
                    sample.setAnanlyzeStatus(EnumAnalyzeStatus.不能分析.getValue());
                    break;

                case 已经完成取样:
                    sample.setSamplingStatus(EnumSamplingStatus.已经完成取样.getValue());
                    if (oldSample.getStatus().equals(EnumSampleStatus.样品未领样.toString())) {
                        sample.setStatus(EnumSampleStatus.样品未领样.toString());
                        sample.setInnerReceiveStatus(oldSample.getInnerReceiveStatus());
                        sample.setAnanlyzeStatus(oldSample.getAnanlyzeStatus());
                    } else {
                        sample.setStatus(EnumSampleStatus.样品待检.toString());
//                        sample.setInnerReceiveStatus(EnumInnerReceiveStatus.已经领取.getValue());
//                        sample.setAnanlyzeStatus(EnumAnalyzeStatus.可以分析.getValue());

                        //现场送样 样品中存在现场测试项目，添加质控样，样品的领取状态可能存在未领取的情况，不应该直接等于已经领取
                        sample.setInnerReceiveStatus(oldSample.getInnerReceiveStatus());
                        sample.setAnanlyzeStatus(oldSample.getAnanlyzeStatus());
                    }
                    break;

                default:
                    break;
            }

            sample.setLon(oldSample.getLon());
            sample.setLat(oldSample.getLat());
            sample.setSamplingRecordId(oldSample.getSamplingRecordId());
        }
        sample.setQcType(qcType);
        sample.setQcGrade(qcGrade);
        sample.setSortNum(getSortOrder(sample, getQualityList()));
        returnMap.put("sample", sample);
        return returnMap;
    }

    /**
     * 获取质控样
     *
     * @param sampleCategory     样品类别
     * @param qcGrade            质控等级
     * @param qcType             质控类型
     * @param oldSample          原样
     * @param qcValue            质控值（标准样的值/加标的值/平行样为空）
     * @param qcAliasName        质控样别名
     * @param qcCode             标样编号
     * @param qcVolume           加标体积
     * @param qcValidDate        标样的有效期
     * @param currentUserId      指定添加质控样品的人员
     * @param dtoWorkSheetFolder 检测单数据
     * @param project            项目
     */
    @Transactional
    @Override
    public Map<String, Object> getAssociateSample(Integer sampleCategory,
                                                  Integer qcGrade,
                                                  Integer qcType,
                                                  DtoSample oldSample,
                                                  String qcValue,
                                                  String qcAliasName,
                                                  String qcCode,
                                                  String qcVolume,
                                                  Date qcValidDate,
                                                  String currentUserId,
                                                  DtoWorkSheetFolder dtoWorkSheetFolder,
                                                  DtoProject project,
                                                  Date qcStandardDate,
                                                  String qcStandardId, String qcConcentration) {
        return this.getAssociateSample(sampleCategory, qcGrade, qcType, oldSample, qcValue, qcAliasName,
                qcCode, qcVolume, qcValidDate, currentUserId, dtoWorkSheetFolder, project, "", qcStandardDate, qcStandardId, qcConcentration);
    }

    @Transactional
    @Override
    public List<Map<String, Object>> addInnerSample(DtoQualityControlTemp qualityControl, Boolean isXc) {
        Integer qcGrade = qualityControl.getQcGrade();
        Integer qcType = qualityControl.getQcType();
        Integer uncertainType = qualityControl.getUncertainType();
        String qcValue = qualityControl.getQcValue();
        String rangeLow = qualityControl.getRangeLow();
        String rangeHigh = qualityControl.getRangeHigh();
        String qcAliasName = qualityControl.getQcAliasName();
        String qcCode = qualityControl.getQcCode();
        String qcVolume = qualityControl.getQcVolume();
        Date qcValidDate = qualityControl.getQcValidDate();
        Integer mostSignificance = qualityControl.getMostSignificance();
        Integer mostDecimal = qualityControl.getMostDecimal();
        String dimensionId = qualityControl.getDimensionId();
        String dimension = qualityControl.getDimension();
        String examLimitValue = qualityControl.getExamLimitValue();
        Integer copyTimes = qualityControl.getCopyTimes();
        Integer sampleCategory = qualityControl.getSampleCategory();
        String workSheetFolderId = qualityControl.getWorkSheetFolderId();
        List<Map<String, Object>> analyseDataMaps = qualityControl.getAnalyseData();
        List<DtoTestFormulaParamsConfig> paramsConfigs = qualityControl.getParamsConfig();
        String casCode = qualityControl.getCasCode();
        String compoundName = qualityControl.getCompoundName();
        String addition = qualityControl.getAddition();
        String qcTestValue = qualityControl.getQcTestValue();
        String realSampleTestValue = qualityControl.getRealSampleTestValue();
        String qcRecoverRate = qualityControl.getQcRecoverRate();
        Date qcStandardDate = qualityControl.getQcStandardDate();
        String qcStandardId = qualityControl.getQcStandardId();
        String qcConcentration = qualityControl.getQcConcentration();
        Integer isVaccinate = qualityControl.getIsVaccinate();
        if (new QualityReplace().qcTypeValue().equals(qcType)) {
            qcVolume = compoundName;
            qcCode = casCode;
            qcValue = addition;
        }
        //量纲排序值自增
        List<String> dimensionIds = Arrays.asList(dimensionId, qualityControl.getQcValueDimensionId(), qualityControl.getQcVolumeDimensionId(), qualityControl.getQcTestValueDimensionId(), qualityControl.getRealSampleTestValueDimensionId()).stream().distinct().collect(Collectors.toList());
        dimensionIds.removeIf(d -> UUIDHelper.GUID_EMPTY.equals(d) || !StringUtils.isNotNullAndEmpty(d));
        if (StringUtil.isNotEmpty(dimensionIds)) {
            dimensionService.incrementOrderNum(dimensionIds);
        }
        //相应的分析数据
        List<String> analyseDataIds = analyseDataMaps.stream().map(p -> (String) p.get("id")).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findAll(analyseDataIds);
        List<DtoAnalyseOriginalRecord> analyseOriginalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataIds);
        DtoWorkSheetFolder dtoWorkSheetFolder = workSheetFolderId != null ? workSheetFolderService.findOne(workSheetFolderId) : null;
        //样品ids
        List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
        List<String> testIdList = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        //获取当前测试项目的质控限值配置
        List<DtoQualityControlLimit> qualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(testIdList);
        List<DtoSample> samples = repository.findAll(sampleIds);
        List<String> receiveIds = samples.stream().map(DtoSample::getReceiveId).filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList());
        if (StringUtil.isEmpty(receiveIds)) {
            getReceiveIdsBySamples(samples, receiveIds);
        }
        List<DtoReceiveSubSampleRecord> sourceReceiveSubSampleRecords = receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIds);
        String format = "";
        if (StringUtil.isNotNull(sampleCategory) && sampleCategory.equals(EnumSampleCategory.原样加原样.getValue())) {
            format = "增加了原样加原样qcCode。";
        } else {
            Map<String, String> map = new HashMap<>();
            map.put("qcCode", qcCode);
            map.put("qcValue", qcValue);
            map.put("uncertainType", uncertainType + "");
            format = QualityTaskFactory.getInstance().getQcSample(qcType).getFormt(map);
        }

        HashMap<String, List<DtoParamsTestFormula>> formulaMap = new HashMap<>();

        List<Map<String, Object>> mapList = new ArrayList<>();
        //复制次数
        List<DtoLog> logList = new ArrayList<>();
        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        List<DtoQualityControlEvaluate> evaluateList = new ArrayList<>();
        List<DtoAnalyseOriginalRecord> newAnalyseOriginalRecords = new ArrayList<>();
        List<DtoQualityControl> qualityControls = new ArrayList<>();
        List<DtoSample> sampleList = new ArrayList<>();
        List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2Sample = new ArrayList<>();
        //要新的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();
        List<DtoDimension> dimensionList = dimensionRepository.findAll();
        Map<String, DtoDimension> dimensionMap = dimensionList.stream().collect(Collectors.toMap(DtoDimension::getId, dto -> dto));
        for (int times = 0; times < copyTimes; times++) {
            for (DtoAnalyseData analyseData : analyseDataList) {
                Optional<DtoAnalyseData> analyseDataOptional = analyseDataList.stream().filter(p -> p.getId().equals(analyseData.getId())).findFirst();
                Optional<Map<String, Object>> mapOptional = analyseDataMaps.stream().filter(p -> p.get("id").equals(analyseData.getId())).findFirst();
                Optional<DtoSample> oldSampleOptional = samples.stream().filter(p -> p.getId().equals(analyseData.getSampleId())).findFirst();
                if (oldSampleOptional.isPresent() && analyseDataOptional.isPresent()) {
                    DtoSample oldSample = oldSampleOptional.get();
                    Map<String, Object> returnMap = this.getAssociateSample(sampleCategory, qcGrade, qcType, oldSample, qcValue, qcAliasName,
                            qcCode, qcVolume, qcValidDate, PrincipalContextUser.getPrincipal().getUserId(), dtoWorkSheetFolder, null, qcStandardDate, qcStandardId, qcConcentration);
                    DtoSample dtoSample = (DtoSample) returnMap.get("sample");
                    if (isXc && new QualityParallel().qcTypeValue().equals(qcType)) {
                        //现场任务添加室内平行样时，点位名称改为"平行样"
                        dtoSample.setRedFolderName(changeParallelRedFolderName(dtoSample.getRedFolderName()));
                    }
                    DtoQualityControl qc = (DtoQualityControl) returnMap.get("qualityControl");
                    if (qc != null && proService.switchIsOpen(ProCodeHelper.LIM_JB_EDITABLE)) {
                        qc.setQcTestValue(qcTestValue);
                        qc.setRealSampleTestValue(realSampleTestValue);
                    }
                    DtoGenerateSN generateSN = (DtoGenerateSN) returnMap.get("generateSN");
                    if (StringUtil.isNotNull(qc)) {
                        if (new QualityMark().qcTypeValue().equals(qc.getQcType())) {
                            //获取当前测试项目上一次加对应类型质控样（加标，曲线校核）的量纲信息
                            getQcDimensionInfo(qc, analyseData.getTestId());
                        } else {
                            qc.setQcValueDimensionId(qualityControl.getQcValueDimensionId());
                            qc.setQcVolumeDimensionId(qualityControl.getQcVolumeDimensionId());
                            qc.setQcTestValueDimensionId(qualityControl.getQcTestValueDimensionId());
                            qc.setRealSampleTestValueDimensionId(qualityControl.getRealSampleTestValueDimensionId());
                        }
                        qc.setUncertainType(uncertainType);
                        qc.setRangeLow(rangeLow);
                        qc.setRangeHigh(rangeHigh);
                        qc.setIsVaccinate(isVaccinate);
                        qualityControls.add(qc);
                    }
                    if (StringUtil.isNotNull(generateSN)) {
                        if (StringUtil.isNotNull(generateSN.getSerialNumberConfigCreate())) {
                            serialNumberConfigCreateList.add(generateSN.getSerialNumberConfigCreate());
                        }
                        if (StringUtil.isNotNull(generateSN.getSerialNumberConfigUpdate())) {
                            serialNumberConfigUpdateList.add(generateSN.getSerialNumberConfigUpdate());
                        }
                    }
                    //获取新的要保存的数据
                    DtoAnalyseData newAnalyseData = this.getInnerAnalyseData(analyseData, dtoSample, qcGrade, qcType, qcValue, qcCode, mostSignificance, mostDecimal, dimensionId, dimension, examLimitValue);
                    // 标样的样品样品有效期需要获取标样的有效期而不是原样的有效期
                    if (new QualityStandard().qcTypeValue().equals(qcType)) {
                        newAnalyseData.setRequireDeadLine(qcValidDate);
                    }
                    //添加替代样时，分析项目名称为替代物化合物名称, 分析数据量纲获取替代样配置的量纲
                    if (new QualityReplace().qcTypeValue().equals(qcType)) {
                        newAnalyseData.setRedAnalyzeItemName(compoundName);
                    }
                    if (new QualityReplace().qcTypeValue().equals(qcType) || new CurveCheck().qcTypeValue().equals(qcType)
                            || new QualityCorrectionFactor().qcTypeValue().equals(qcType)) {
                        String qcValueDimId = StringUtil.isNotNull(qc) ? qc.getQcValueDimensionId() : UUIDHelper.GUID_EMPTY;
                        DtoDimension qcDim = dimensionMap.get(qcValueDimId);
                        newAnalyseData.setDimension(StringUtil.isNotNull(qcDim) ? qcDim.getDimensionName() : "");
                        newAnalyseData.setDimensionId(qcValueDimId);
                    }
                    if (proService.switchIsOpen(ProCodeHelper.LIM_JB_EDITABLE)) {
                        newAnalyseData.setQcInfo(qcRecoverRate);
                    }
                    dtoSample.setRedAnalyzeItems(newAnalyseData.getRedAnalyzeItemName());

                    //获取源数据的公式信息及检测类型名称
                    String formula = "";
                    String formulaId = "";
                    String sampleTypeName = "";
                    if (mapOptional.isPresent()) {
                        Map<String, Object> anaMap = mapOptional.get();
                        formula = (String) anaMap.get("formula");
                        formulaId = (String) anaMap.get("formulaId");
                        sampleTypeName = (String) anaMap.get("sampleTypeName");

                        if (!formulaMap.containsKey(formulaId)) {
                            List<DtoParamsTestFormula> paramsTestFormulas = paramsTestFormulaService.findByObjectId(formulaId);
                            paramsTestFormulas = paramsTestFormulas.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getDefaultValue()) &&
                                    !p.getAlias().equals("k") && !p.getAlias().equals("b")).collect(Collectors.toList());
                            formulaMap.put(formulaId, paramsTestFormulas);
                        }
                    }

                    //准备AnalyseOriginRecord数据，添加质控样要插入相关数据
                    DtoAnalyseOriginalRecord yyAnalyseOriginalRecord = analyseOriginalRecordList.parallelStream()
                            .filter(p -> analyseData.getId().equals(p.getAnalyseDataId())).findFirst().orElse(null);
                    if (yyAnalyseOriginalRecord != null) {
                        DtoAnalyseOriginalRecord newDtoAnalyseOriginalRecord = new DtoAnalyseOriginalRecord();
                        newDtoAnalyseOriginalRecord.setAnalyseDataId(newAnalyseData.getId());
                        //质控样参数数据值只获取k和b的值
                        TypeLiteral<List<DtoParamsTestFormula>> typeLiteral = new TypeLiteral<List<DtoParamsTestFormula>>() {
                        };
                        List<DtoParamsTestFormula> paramsTestFormulas = JsonIterator.deserialize(yyAnalyseOriginalRecord.getJson(), typeLiteral);
                        for (DtoParamsTestFormula paramsTestFormula : paramsTestFormulas) {
                            if (!(paramsTestFormula.getAlias().toLowerCase().equals("k") || paramsTestFormula.getAlias().toLowerCase().equals("b"))) {
                                paramsTestFormula.setDefaultValue("");
                            }
                        }
                        //添加质控样时，公式中参数配置的默认值也要带出来，加标、平行、质控、原样、空白等
                        if (formulaMap.containsKey(formulaId)) {
                            for (DtoParamsTestFormula paramsTestFormula : paramsTestFormulas) {
                                DtoParamsTestFormula defValParamsTestFormula = formulaMap.get(formulaId).stream()
                                        .filter(p -> p.getAlias().equals(paramsTestFormula.getAlias())).findFirst().orElse(null);
                                if (StringUtil.isNotNull(defValParamsTestFormula)) {
                                    paramsTestFormula.setDefaultValue(defValParamsTestFormula.getDefaultValue());
                                }
                            }
                        }
                        String json = JsonStream.serialize(paramsTestFormulas);
                        newDtoAnalyseOriginalRecord.setJson(json);
                        newDtoAnalyseOriginalRecord.setTestFormulaId(yyAnalyseOriginalRecord.getTestFormulaId());
                        newDtoAnalyseOriginalRecord.setTestFormula(yyAnalyseOriginalRecord.getTestFormula());
                        newAnalyseOriginalRecords.add(newDtoAnalyseOriginalRecord);
                    }
                    //样品保存
                    //repository.save(dtoSample);
                    sampleList.add(dtoSample);
                    anaDatas.add(newAnalyseData);
                    //初始化质控评价记录
                    if (EnumLIM.EnumQCType.曲线校核.getValue().equals(qcType) && EnumLIM.EnumQCGrade.内部质控.getValue().equals(qcGrade)) {
                        //曲线校核质控限值会配置多个，需要初始化多个质控评价信息 BUG2024030199954
                        evaluateList.addAll(qualityControlEvaluateService.initQualityControlEvaluateList(newAnalyseData.getId(), newAnalyseData.getTestId(), qcType, qcGrade,
                                qualityControlLimitList, qcValue, StringUtil.isNotNull(qc) ? qc.getId() : UUIDHelper.GUID_EMPTY));
                    } else if (!new QualitySampleCopy().qcTypeValue().equals(qcType)) {
                        evaluateList.add(qualityControlEvaluateService.initQualityControlEvaluate(newAnalyseData.getId(), newAnalyseData.getTestId(), qcType, qcGrade,
                                qualityControlLimitList, qcValue, StringUtil.isNotNull(qc) ? qc.getId() : UUIDHelper.GUID_EMPTY, uncertainType, rangeLow, rangeHigh,isVaccinate));
                    }
                    String sampleRemark = "";
                    if (qcGrade.equals(EnumLIM.EnumQCGrade.内部质控.getValue()) && qcType.equals(new QualityStandard().qcTypeValue())) {
                        sampleRemark = "标准编号：" + qcCode + " 标准值±不确定值：" + qcValue;
                    }

                    //装载map对象返回给前端
                    Map<String, Object> map = getAnalyseDataMap(newAnalyseData, dtoSample, formula, formulaId, qcValue, qcVolume, qcCode, sampleRemark);
                    map.put("uncertainType", uncertainType);
                    map.put("rangeLow", rangeLow);
                    map.put("rangeHigh", rangeHigh);
                    map.put("sampleTypeName", sampleTypeName);
                    map.put("isVaccinate", isVaccinate);
                    if (qcType.equals(new QualityBlank().qcTypeValue()) || qcType.equals(new QualityStandard().qcTypeValue())) {
                        map.put("groupSampleId", dtoSample.getId());
                        map.put("orderNum", qcType.equals(new QualityBlank().qcTypeValue()) ? "000" + dtoSample.getCode() : "999" + dtoSample.getCode());
                    } else {
                        if (oldSample.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                            map.put("orderNum", oldSample.getCode() + "001");
                            map.put("groupSampleId", oldSample.getId());
                        } else {
                            map.put("groupSampleId", oldSample.getAssociateSampleId());
                            if (sampleCategory.equals(EnumSampleCategory.原样.getValue())) {
                                map.put("orderNum", oldSample.getCode() + "001");
                            } else {
                                map.put("orderNum", qcType.equals(new QualityParallel().qcTypeValue()) ? oldSample.getCode() + "002" : oldSample.getCode() + "003");
                            }
                        }
                    }
//                    analyseDataRepository.save(newAnalyseData);
                    mapList.add(map);

                    String comment = format.replace("qcCode", dtoSample.getCode()).replace("oldCode", oldSample.getCode());

                    DtoLog log = new DtoLog();
                    log.setId(UUIDHelper.NewID());
                    log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                    log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
                    log.setOperateTime(new Date());
                    log.setOperateInfo(EnumLogOperateType.增加质控样.toString());
                    log.setLogType(EnumLogType.检测单增删样品.getValue());
                    log.setObjectId(analyseData.getWorkSheetFolderId());
                    log.setObjectType(EnumLogObjectType.检测单.getValue());
                    log.setComment(comment);
                    log.setOpinion("");
                    log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                    log.setRemark("");
                    logList.add(log);
                }
            }
        }
        if (isXc) {
            for (DtoReceiveSubSampleRecord subRecord : sourceReceiveSubSampleRecords) {
                for (DtoSample checkSample : sampleList) {
                    if ((subRecord.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0 &&
                            anaDatas.stream().anyMatch(p -> p.getSampleId().equals(checkSample.getId()) && !p.getIsCompleteField())) {
                        DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                        r2s.setSampleId(checkSample.getId());
                        r2s.setReceiveSubSampleRecordId(subRecord.getId());
                        receiveSubSampleRecord2Sample.add(r2s);
                    }
                    if ((subRecord.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0 &&
                            anaDatas.stream().anyMatch(p -> p.getSampleId().equals(checkSample.getId()) && p.getIsCompleteField())) {
                        DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                        r2s.setSampleId(checkSample.getId());
                        r2s.setReceiveSubSampleRecordId(subRecord.getId());
                        receiveSubSampleRecord2Sample.add(r2s);
                    }
                }
            }
            if (receiveSubSampleRecord2Sample.size() > 0) {
                comRepository.insertBatch(receiveSubSampleRecord2Sample);
            }
        }

        if (serialNumberConfigCreateList.size() > 0) {
            comRepository.insert(serialNumberConfigCreateList);
        }
        if (serialNumberConfigUpdateList.size() > 0) {
            comRepository.updateBatch(serialNumberConfigUpdateList);
        }
        //质控数据
        if (qualityControls.size() > 0) {
            comRepository.insert(qualityControls);
        }
        //样品数据
        if (sampleList.size() > 0) {
            comRepository.insert(sampleList);
        }
        //插入分析数据
        if (anaDatas.size() > 0) {
            comRepository.insert(anaDatas);
        }
        if (StringUtil.isNotEmpty(evaluateList)) {
            qualityControlEvaluateRepository.save(evaluateList);
        }
        if (newAnalyseOriginalRecords.size() > 0) {
            comRepository.insert(newAnalyseOriginalRecords);
        }

        newLogService.createLog(logList, EnumLogType.检测单增删样品.getValue());
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.添加室内质控样, "", "", analyseDataList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()));
                    }
                }
        );
        return mapList;
    }

    @Override
    public List<DtoAnalyseData> findByProjectId(String projectId) {
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        List<DtoSample> sampleList = repository.findByProjectId(projectId);
        if (sampleList.size() > 0) {
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
        }
        return analyseDataList;
    }

    @Override
    public List<DtoAnalyseData> findDataByReceiveId(String receiveId) {
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        List<DtoSample> sampleList = repository.findByReceiveId(receiveId);
        if (sampleList.size() > 0) {
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIds);
        }
        return analyseDataList;
    }

    @Override
    public List<Map<String, Object>> getOcrSelectList() {
        List<Map<String, Object>> list = new ArrayList<>();
        PageBean<DtoSample> page = new PageBean<>();
        page.setRowsPerPage(9999);
        page.setSort("code-");
        page.setEntityName("DtoSample s");
        page.setSelect("select s");
        SampleCriteria criteria = new SampleCriteria();
        //样品类型：噪声、气类型
        List<String> bigName = new ArrayList<>();
        bigName.add("环境空气和废气");
        bigName.add("噪声和振动");
        List<String> bigTypeIds = sampleTypeService.findAllBigSampleType().stream()
                .filter(p -> bigName.contains(p.getTypeName())).map(DtoSampleType::getId).collect(Collectors.toList());
        if (bigTypeIds.size() > 0) {
            List<String> sampleTypeIds = sampleTypeService.findByParentIds(bigTypeIds).stream().map(DtoSampleType::getId).collect(Collectors.toList());
            criteria.setSampleTypeIds(sampleTypeIds);
        }
        criteria.setStatusList(Arrays.asList(EnumSampleStatus.样品未领样.name(), EnumSampleStatus.样品待检.name(), EnumSampleStatus.样品在检.name()));
        super.findByPage(page, criteria);
        page.getData().stream().filter(s -> StringUtil.isNotEmpty(s.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(s.getReceiveId()))
                .forEach(sample -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("sampleId", sample.getId());
                    map.put("code", sample.getCode());
                    map.put("folderName", sample.getRedFolderName());
                    list.add(map);
                });
        return list;
    }

    @Override
    public List<Map<String, Object>> getSampleGroupSelectList(String sampleId) {
        List<Map<String, Object>> list = new ArrayList<>();
        List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectTypeAndObjectId(EnumParamsDataType.样品.getValue(), sampleId);
        List<String> groupIds = paramsDataList.stream().filter(P -> StringUtil.isNotEmpty(P.getGroupId()) && !UUIDHelper.GUID_EMPTY.equals(P.getGroupId()))
                .map(DtoParamsData::getGroupId).distinct().collect(Collectors.toList());
        if (!groupIds.isEmpty()) {
            List<DtoSampleTypeGroup> groupList = sampleTypeGroupRepository.findAll(groupIds);
            List<DtoSampleTypeGroup2Test> allGroup2TestList = sampleTypeGroup2TestService.findBySampleTypeGroupIds(groupIds);
            List<String> testIds = allGroup2TestList.stream().map(DtoSampleTypeGroup2Test::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> testList = new ArrayList<>();
            if (StringUtil.isNotEmpty(testIds)) {
                testList = testService.findAll(testIds);
            }
            for (DtoSampleTypeGroup group : groupList) {
                List<String> group2TestList = allGroup2TestList.stream().filter(t -> group.getId().equals(t.getSampleTypeGroupId()))
                        .map(DtoSampleTypeGroup2Test::getTestId).distinct().collect(Collectors.toList());
                String groupTestName = testList.stream().filter(t -> group2TestList.contains(t.getId())).map(DtoTest::getRedAnalyzeItemName)
                        .distinct().sorted().collect(Collectors.joining("、"));
                Map<String, Object> map = new HashMap<>();
                map.put("groupId", group.getId());
                map.put("groupName", group.getGroupName());
                map.put("groupTestName", groupTestName);
                list.add(map);
            }
        }
        return list;
    }

    /**
     * 现场数据录入添加室内平行样时，点位名称展示为 “平行样”
     *
     * @param name 原来的点位名称
     * @return 新的点位名称
     */
    private String changeParallelRedFolderName(String name) {
        String newName = name;
        if (StringUtil.isNotEmpty(name)) {
            int idx = name.lastIndexOf("室内平行样");
            if (idx != -1) {
                newName = name.substring(0, idx) + "平行样" + name.substring(idx + 5);
            }
        }
        return newName;
    }

    /**
     * 现场数据录入根据质控样查找原样送样单id
     *
     * @param samples    样品
     * @param receiveIds 送样单id
     */
    private void getReceiveIdsBySamples(List<DtoSample> samples, List<String> receiveIds) {
        List<String> associateSampleIds = samples.stream().map(DtoSample::getAssociateSampleId).filter(associateSampleId -> !UUIDHelper.GUID_EMPTY.equals(associateSampleId)).distinct().collect(Collectors.toList());
        List<DtoSample> associateSamples = repository.findAll(associateSampleIds);
        if (associateSamples.size() > 0) {
            receiveIds.addAll(associateSamples.stream().map(DtoSample::getReceiveId).filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList()));
            if (receiveIds.size() > 0) {
                getReceiveIdsBySamples(associateSamples, receiveIds);
            }
        }
    }

    @Transactional
    @Override
    public List<Map<String, Object>> addInnerSample(List<DtoQualityControlTemp> qualityControlTempList) {
        return addInnerSample(qualityControlTempList, false);
    }

    @Transactional
    @Override
    public List<Map<String, Object>> addInnerSample(List<DtoQualityControlTemp> qualityControlTempList, Boolean isXc) {
        Map<String, String> codeList = new HashMap<>();
        List<Map<String, Object>> mapList = new ArrayList<>();

        //判断是否是同一个样的数据
        List<String> samIds = qualityControlTempList.stream().map(DtoQualityControlTemp::getSampleId).distinct().collect(Collectors.toList());
        if (samIds.size() > 0) {
            List<String> sampleCodes = repository.findAll(samIds).stream().map(DtoSample::getCode).collect(Collectors.toList());
            if (sampleCodes.size() > 0) {
                List<String> sIds = repository.findByCodeIn(sampleCodes).stream().filter(p -> !p.getSampleCategory().equals(EnumSampleCategory.原样.getValue()))
                        .map(DtoSample::getId).collect(Collectors.toList());
                samIds.addAll(sIds);
            }
        }
        List<DtoSample> allSamples = StringUtil.isNotEmpty(samIds) ? repository.findAll(samIds) : new ArrayList<>();
        //复制次数
        List<DtoLog> logList = new ArrayList<>();
        List<DtoAnalyseData> anaDatas = new ArrayList<>();
        List<DtoAnalyseOriginalRecord> newAnalyseOriginalRecords = new ArrayList<>();
        List<DtoQualityControl> qualityControls = new ArrayList<>();
        List<DtoSample> sampleList = new ArrayList<>();
        //要新的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();
        //存在修改的数据
        List<DtoAnalyseData> dataList = new ArrayList<>();
        //获取测试项目的质控限值配置
        List<String> testIdList = qualityControlTempList.stream().map(DtoQualityControlTemp::getTestId).distinct().collect(Collectors.toList());
        List<DtoQualityControlLimit> qualityControlLimitList = qualityControlLimitRepository.findByTestIdIn(testIdList);
        List<DtoTest> testList = testService.findRedisByIds(testIdList);
        List<DtoQualityControlEvaluate> evaluateList = new ArrayList<>();
        for (DtoQualityControlTemp qualityControlTemp : qualityControlTempList) {
            DtoSample sample = allSamples.stream().filter(s -> s.getId().equals(qualityControlTemp.getSampleId())).findFirst().orElse(null);
            //相应的分析数据
            //List<String> analyseDataIds = qualityControlTemp.getAnalyseData().stream().map(p -> (String) p.get("id")).collect(Collectors.toList());
            //通过测试项目id和样品id找到对应的数据
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdAndTestIdAndIsDeletedFalse(qualityControlTemp.getSampleId(), qualityControlTemp.getTestId());//analyseDataRepository.findAll(analyseDataIds);
            if (analyseDataList.size() == 0) {
                analyseDataList = analyseDataRepository.findBySampleIdInAndTestIdAndIsDeletedFalse(samIds, qualityControlTemp.getTestId());
            }
            Boolean sampleFlag = false;
            if (analyseDataList.size() == 0) {
                // 加标、空白加标、平行样需要根据原样的分析项目进行过滤
                if (!EnumLIM.EnumQCType.平行.getValue().equals(qualityControlTemp.getQcType())
                        && !EnumLIM.EnumQCType.空白加标.getValue().equals(qualityControlTemp.getQcType())
                        && !EnumLIM.EnumQCType.加标.getValue().equals(qualityControlTemp.getQcType())) {
                    List<DtoAnalyseData> allAnaDataList = analyseDataRepository.findByWorkSheetFolderIdAndIsDeletedFalse(qualityControlTemp.getWorkSheetFolderId()).stream().filter(a -> a.getTestId().equals(qualityControlTemp.getTestId())).collect(Collectors.toList());
                    if (allAnaDataList.size() > 0) {
                        analyseDataList = allAnaDataList.stream().limit(1).collect(Collectors.toList());
                        sampleFlag = true;
                    }
                }
            }
            dataList.addAll(analyseDataList);
            List<DtoAnalyseOriginalRecord> analyseOriginalRecordList = analyseOriginalRecordRepository.findByAnalyseDataIdIn(analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList()));
            DtoWorkSheetFolder dtoWorkSheetFolder = workSheetFolderService.findOne(qualityControlTemp.getWorkSheetFolderId());
            //样品ids
            List<String> sampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());
            List<DtoSample> samples = repository.findAll(sampleIds);

            String format = "";
            if (StringUtil.isNotNull(qualityControlTemp.getSampleCategory()) && qualityControlTemp.getSampleCategory().equals(EnumSampleCategory.原样加原样.getValue())) {
                format = "增加了原样加原样qcCode。";
            } else {
                Map<String, String> map = new HashMap<>();
                map.put("qcCode", qualityControlTemp.getQcCode());
                map.put("qcValue", qualityControlTemp.getQcValue());
                map.put("uncertainType", qualityControlTemp.getUncertainType() + "");
                format = QualityTaskFactory.getInstance().getQcSample(qualityControlTemp.getQcType()).getFormt(map);
            }
            HashMap<String, List<DtoParamsTestFormula>> formulaMap = new HashMap<>();
            for (Integer times = 0; times < qualityControlTemp.getCopyTimes(); times++) {
                //批量添加相同批次内的质控样，样品编号是同一个
                String samCode = "";
                if (codeList.containsKey(sample.getCode() + "_" + times)) {
                    samCode = codeList.get(sample.getCode() + "_" + times);
                }
                for (DtoAnalyseData analyseData : analyseDataList) {
                    Optional<DtoAnalyseData> analyseDataOptional = analyseDataList.stream().filter(p -> p.getId().equals(analyseData.getId())).findFirst();
                    //和具体的数据没有关系
                    Optional<Map<String, Object>> mapOptional = qualityControlTemp.getAnalyseData().stream().findFirst();
                    Optional<DtoSample> oldSampleOptional = samples.stream().filter(p -> p.getId().equals(analyseData.getSampleId())).findFirst();
                    if (oldSampleOptional.isPresent() && analyseDataOptional.isPresent()) {
                        DtoSample oldSample = oldSampleOptional.get();
                        if (sampleFlag) {
                            oldSample = repository.findOne(qualityControlTemp.getSampleId());
                        }
                        Map<String, Object> returnMap = this.getAssociateSample(qualityControlTemp.getSampleCategory(), qualityControlTemp.getQcGrade(),
                                qualityControlTemp.getQcType(), oldSample, qualityControlTemp.getQcValue(), qualityControlTemp.getQcAliasName(),
                                qualityControlTemp.getQcCode(), qualityControlTemp.getQcVolume(), qualityControlTemp.getQcValidDate(),
                                PrincipalContextUser.getPrincipal().getUserId(), dtoWorkSheetFolder, null, samCode,
                                qualityControlTemp.getQcStandardDate(), qualityControlTemp.getQcStandardId(), qualityControlTemp.getQcConcentration());
                        DtoSample dtoSample = (DtoSample) returnMap.get("sample");
                        if (isXc && new QualityParallel().qcTypeValue().equals(qualityControlTemp.getQcType())) {
                            //现场任务添加室内平行样时，点位名称改为"平行样"
                            dtoSample.setRedFolderName(changeParallelRedFolderName(dtoSample.getRedFolderName()));
                        }
                        DtoQualityControl qc = (DtoQualityControl) returnMap.get("qualityControl");
                        DtoGenerateSN generateSN = (DtoGenerateSN) returnMap.get("generateSN");
                        if (!StringUtil.isNotEmpty(samCode)) {
                            samCode = dtoSample.getCode();
                            codeList.put(sample.getCode() + "_" + times, dtoSample.getCode());
                        }
                        if (StringUtil.isNotNull(qc)) {
                            qc.setQcValueDimensionId(qualityControlTemp.getQcValueDimensionId());
                            qc.setQcVolumeDimensionId(qualityControlTemp.getQcVolumeDimensionId());
                            qc.setQcTestValueDimensionId(qualityControlTemp.getQcTestValueDimensionId());
                            qc.setRealSampleTestValueDimensionId(qualityControlTemp.getRealSampleTestValueDimensionId());
                            qc.setSsConcentration(qualityControlTemp.getSsConcentration());
                            qc.setConstantVolume(qualityControlTemp.getConstantVolume());
                            qc.setSsConcentrationDimensionId(qualityControlTemp.getSsConcentrationDimensionId());
                            qc.setConstantVolumeDimensionId(qualityControlTemp.getConstantVolumeDimensionId());
                            qc.setRemark(qualityControlTemp.getRemark());
                            qc.setUncertainType(qualityControlTemp.getUncertainType());
                            qc.setRangeLow(qualityControlTemp.getRangeLow());
                            qc.setRangeHigh(qualityControlTemp.getRangeHigh());
                            qc.setIsVaccinate(qualityControlTemp.getIsVaccinate());
                            qualityControls.add(qc);
                        }
                        if (StringUtil.isNotNull(generateSN)) {
                            if (StringUtil.isNotNull(generateSN.getSerialNumberConfigCreate())) {
                                serialNumberConfigCreateList.add(generateSN.getSerialNumberConfigCreate());
                            }
                            if (StringUtil.isNotNull(generateSN.getSerialNumberConfigUpdate())) {
                                serialNumberConfigUpdateList.add(generateSN.getSerialNumberConfigUpdate());
                            }
                        }
                        //获取新的要保存的数据
                        DtoAnalyseData newAnalyseData = this.getInnerAnalyseData(analyseData, dtoSample,
                                qualityControlTemp.getQcGrade(), qualityControlTemp.getQcType(),
                                qualityControlTemp.getQcValue(), qualityControlTemp.getQcCode(),
                                qualityControlTemp.getMostSignificance(), qualityControlTemp.getMostDecimal(),
                                qualityControlTemp.getDimensionId(), qualityControlTemp.getDimension(),
                                qualityControlTemp.getExamLimitValue());
                        setInnerAnalyseDataInd(newAnalyseData, analyseData, dtoSample, qualityControlTemp.getQcGrade(), qualityControlTemp.getQcType(), oldSample);
                        //当为替代物添加质控样时，分析项目名称重置下
                        if (EnumLIM.EnumQCType.替代物.getValue().equals(analyseData.getQcType())) {
                            testList.stream().filter(v -> v.getId().equals(newAnalyseData.getTestId())).findFirst()
                                    .ifPresent(v -> newAnalyseData.setRedAnalyzeItemName(v.getRedAnalyzeItemName()));
                        }
                        //添加替代样时，分析项目名称为替代物化合物名称
                        if (new QualityReplace().qcTypeValue().equals(qualityControlTemp.getQcType())) {
                            newAnalyseData.setRedAnalyzeItemName(qualityControlTemp.getCompoundName());
                        }
                        if (new CurveCheck().qcTypeValue().equals(qualityControlTemp.getQcType())) {
                            //曲线校核样的加入量量纲更新到分析数据的量纲上去
                            newAnalyseData.setDimensionId(qualityControlTemp.getQcValueDimensionId());
                            String dimId = StringUtil.isNotEmpty(qualityControlTemp.getQcValueDimensionId()) ? qualityControlTemp.getQcValueDimensionId() : "";
                            DtoDimension dimension = dimensionService.findOne(dimId);
                            newAnalyseData.setDimension(StringUtil.isNotNull(dimension) ? dimension.getDimensionName() : "");
                        }
                        dtoSample.setRedAnalyzeItems(newAnalyseData.getRedAnalyzeItemName());

                        DtoAnalyseOriginalRecord yyAnalyseOriginalRecord = analyseOriginalRecordList.parallelStream()
                                .filter(p -> analyseData.getId().equals(p.getAnalyseDataId())).findFirst().orElse(null);

                        //获取源数据的公式信息及检测类型名称
                        String formula = "";
                        String formulaId = "";
                        String sampleTypeName = "";
                        if (mapOptional.isPresent()) {
                            Map<String, Object> anaMap = mapOptional.get();
                            formula = (String) anaMap.get("formula");
                            formulaId = (String) anaMap.get("formulaId");
                            sampleTypeName = (String) anaMap.get("sampleTypeName");

                            if (!formulaMap.containsKey(formulaId)) {
                                List<DtoParamsTestFormula> paramsTestFormulas = paramsTestFormulaService.findByObjectId(formulaId);
                                paramsTestFormulas = paramsTestFormulas.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getDefaultValue()) &&
                                        !p.getAlias().equals("k") && !p.getAlias().equals("b")).collect(Collectors.toList());
                                formulaMap.put(formulaId, paramsTestFormulas);
                            }
                        }

                        if (yyAnalyseOriginalRecord != null) {
                            DtoAnalyseOriginalRecord newDtoAnalyseOriginalRecord = new DtoAnalyseOriginalRecord();
                            newDtoAnalyseOriginalRecord.setAnalyseDataId(newAnalyseData.getId());
                            //质控样参数数据值只获取k和b的值
                            TypeLiteral<List<DtoParamsTestFormula>> typeLiteral = new TypeLiteral<List<DtoParamsTestFormula>>() {
                            };
                            List<DtoParamsTestFormula> paramsTestFormulas = JsonIterator.deserialize(yyAnalyseOriginalRecord.getJson(), typeLiteral);
                            for (DtoParamsTestFormula paramsTestFormula : paramsTestFormulas) {
                                if (!(paramsTestFormula.getAlias().toLowerCase().equals("k") || paramsTestFormula.getAlias().toLowerCase().equals("b"))) {
                                    paramsTestFormula.setDefaultValue("");
                                }
                            }
                            //如果公式配置中的参数有默认值时需要带出默认值到的新的originalRecord中
                            if (formulaMap.containsKey(formulaId)) {
                                for (DtoParamsTestFormula paramsTestFormula : paramsTestFormulas) {
                                    DtoParamsTestFormula defValParamsTestFormula = formulaMap.get(formulaId).stream()
                                            .filter(p -> p.getAlias().equals(paramsTestFormula.getAlias())).findFirst().orElse(null);
                                    if (StringUtil.isNotNull(defValParamsTestFormula)) {
                                        paramsTestFormula.setDefaultValue(defValParamsTestFormula.getDefaultValue());
                                    }
                                }
                            }
                            String json = JsonStream.serialize(paramsTestFormulas);
                            newDtoAnalyseOriginalRecord.setJson(json);
                            newDtoAnalyseOriginalRecord.setTestFormulaId(yyAnalyseOriginalRecord.getTestFormulaId());
                            newDtoAnalyseOriginalRecord.setTestFormula(yyAnalyseOriginalRecord.getTestFormula());
                            newAnalyseOriginalRecords.add(newDtoAnalyseOriginalRecord);
                        }

                        //样品保存
                        //repository.save(dtoSample);
                        sampleList.add(dtoSample);
                        anaDatas.add(newAnalyseData);
                        //初始化质控评价记录
                        if (EnumLIM.EnumQCType.曲线校核.getValue().equals(qualityControlTemp.getQcType())
                                && EnumLIM.EnumQCGrade.内部质控.getValue().equals(qualityControlTemp.getQcGrade())) {
                            //曲线校核质控限值会配置多个，需要初始化多个质控平均信息 BUG2024030199954
                            evaluateList.addAll(qualityControlEvaluateService.initQualityControlEvaluateList(newAnalyseData.getId(), newAnalyseData.getTestId(),
                                    qualityControlTemp.getQcType(), qualityControlTemp.getQcGrade(), qualityControlLimitList, qualityControlTemp.getQcValue(), StringUtil.isNotNull(qc) ? qc.getId() : UUIDHelper.GUID_EMPTY));
                        } else if (!new QualitySampleCopy().qcTypeValue().equals(qualityControlTemp.getQcType())) {
                            evaluateList.add(qualityControlEvaluateService.initQualityControlEvaluate(newAnalyseData.getId(), newAnalyseData.getTestId(),
                                    qualityControlTemp.getQcType(), qualityControlTemp.getQcGrade(), qualityControlLimitList, qualityControlTemp.getQcValue(), StringUtil.isNotNull(qc) ? qc.getId() : UUIDHelper.GUID_EMPTY,
                                    qualityControlTemp.getUncertainType(),qualityControlTemp.getIsVaccinate()));
                        }
                        String sampleRemark = "";
                        if (qualityControlTemp.getQcGrade().equals(EnumLIM.EnumQCGrade.内部质控.getValue()) && qualityControlTemp.getQcType().equals(new QualityStandard().qcTypeValue())) {
                            sampleRemark = "标准编号：" + qualityControlTemp.getQcCode() + " 标准值±不确定值：" + qualityControlTemp.getQcValue();
                        }

                        //装载map对象返回给前端
                        Map<String, Object> map = getAnalyseDataMap(newAnalyseData, dtoSample, formula, formulaId,
                                qualityControlTemp.getQcValue(), qualityControlTemp.getQcVolume(), qualityControlTemp.getQcCode(), sampleRemark);
                        map.put("sampleTypeName", sampleTypeName);
                        map.put("isVaccinate", qualityControlTemp.getIsVaccinate());
                        if (qualityControlTemp.getQcType().equals(new QualityBlank().qcTypeValue()) || qualityControlTemp.getQcType().equals(new QualityStandard().qcTypeValue())) {
                            map.put("groupSampleId", dtoSample.getId());
                            map.put("orderNum", qualityControlTemp.getQcType().equals(new QualityBlank().qcTypeValue()) ? "000" + dtoSample.getCode() : "999" + dtoSample.getCode());
                        } else {
                            if (oldSample.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                                map.put("orderNum", oldSample.getCode() + "001");
                                map.put("groupSampleId", oldSample.getId());
                            } else {
                                map.put("groupSampleId", oldSample.getAssociateSampleId());
                                if (qualityControlTemp.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                                    map.put("orderNum", oldSample.getCode() + "001");
                                } else {
                                    map.put("orderNum", qualityControlTemp.getQcType().equals(new QualityParallel().qcTypeValue()) ? oldSample.getCode() + "002" : oldSample.getCode() + "003");
                                }
                            }
                        }

                        mapList.add(map);

                        String comment = format.replace("qcCode", dtoSample.getCode()).replace("oldCode", oldSample.getCode());

                        DtoLog log = new DtoLog();
                        log.setId(UUIDHelper.NewID());
                        log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                        log.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
                        log.setOperateTime(new Date());
                        log.setOperateInfo(EnumLogOperateType.增加质控样.toString());
                        log.setLogType(EnumLogType.检测单增删样品.getValue());
                        log.setObjectId(analyseData.getWorkSheetFolderId());
                        log.setObjectType(EnumLogObjectType.检测单.getValue());
                        log.setComment(comment);
                        log.setOpinion("");
                        log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                        log.setRemark("");
                        logList.add(log);
                    }
                }
            }
        }
        if (isXc) {
            List<String> receiveIds = allSamples.stream().map(DtoSample::getReceiveId).filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList());
            List<DtoReceiveSubSampleRecord> sourceReceiveSubSampleRecords = receiveSubSampleRecordRepository.findByReceiveIdIn(receiveIds);
            List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2Sample = new ArrayList<>();
            for (DtoReceiveSubSampleRecord subRecord : sourceReceiveSubSampleRecords) {
                for (DtoSample checkSample : sampleList) {
                    if ((subRecord.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0 &&
                            anaDatas.stream().anyMatch(p -> p.getSampleId().equals(checkSample.getId()) && !p.getIsCompleteField())) {
                        DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                        r2s.setSampleId(checkSample.getId());
                        r2s.setReceiveSubSampleRecordId(subRecord.getId());
                        receiveSubSampleRecord2Sample.add(r2s);
                    }
                    if ((subRecord.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0 &&
                            anaDatas.stream().anyMatch(p -> p.getSampleId().equals(checkSample.getId()) && p.getIsCompleteField())) {
                        DtoReceiveSubSampleRecord2Sample r2s = new DtoReceiveSubSampleRecord2Sample();
                        r2s.setSampleId(checkSample.getId());
                        r2s.setReceiveSubSampleRecordId(subRecord.getId());
                        receiveSubSampleRecord2Sample.add(r2s);
                    }
                }
                if (receiveSubSampleRecord2Sample.size() > 0) {
                    comRepository.insertBatch(receiveSubSampleRecord2Sample);
                }
            }
        }
        if (serialNumberConfigCreateList.size() > 0) {
            comRepository.insert(serialNumberConfigCreateList);
        }
        if (serialNumberConfigUpdateList.size() > 0) {
            comRepository.updateBatch(serialNumberConfigUpdateList);
        }
        //质控数据
        if (qualityControls.size() > 0) {
            comRepository.insert(qualityControls);
        }
        //样品数据
        if (sampleList.size() > 0) {
            comRepository.insert(sampleList);
        }
        //插入分析数据
        if (anaDatas.size() > 0) {
            comRepository.insert(anaDatas);
        }
        //插入质控评价记录
        if (StringUtil.isNotEmpty(evaluateList)) {
            qualityControlEvaluateRepository.save(evaluateList);
        }
        //插入参数
        if (newAnalyseOriginalRecords.size() > 0) {
            comRepository.insert(newAnalyseOriginalRecords);
        }

        newLogService.createLog(logList, EnumLogType.检测单增删样品.getValue());
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.添加室内质控样, "", "", dataList.stream().map(DtoAnalyseData::getAnalystId).distinct().collect(Collectors.toList()));
                    }
                }
        );
        return mapList;
    }

    /**
     * 个性化分析数据
     *
     * @param newAnalyseData 新的分析数据
     * @param analyseData    源分析数据
     * @param qcSample       质控样
     * @param qcGrade        质控等级
     * @param qcType         质控类型
     * @param oldSample      源样品
     * @return 质控数据
     */
    protected void setInnerAnalyseDataInd(DtoAnalyseData newAnalyseData, DtoAnalyseData analyseData, DtoSample qcSample, Integer qcGrade, Integer qcType, DtoSample oldSample) {

    }

    /**
     * 获取质控数据
     *
     * @param analyseData      源分析数据
     * @param qcSample         质控样
     * @param qcGrade          质控等级
     * @param qcType           质控类型
     * @param qcValue          质控值
     * @param qcCode           质控编号
     * @param mostSignificance 有效位数
     * @param mostDecimal      小数位数
     * @param dimensionId      量纲id
     * @param dimension        量纲
     * @param examLimitValue   检出限
     * @return 质控数据
     */
    private DtoAnalyseData getInnerAnalyseData(DtoAnalyseData analyseData,
                                               DtoSample qcSample,
                                               Integer qcGrade,
                                               Integer qcType,
                                               String qcValue,
                                               String qcCode,
                                               Integer mostSignificance,
                                               Integer mostDecimal,
                                               String dimensionId,
                                               String dimension,
                                               String examLimitValue) {
        //新的要保存的数据
        DtoAnalyseData innerAnalyseData = new DtoAnalyseData();
        innerAnalyseData.setWorkSheetId(analyseData.getWorkSheetId());
        innerAnalyseData.setAssociateSampleId(qcSample.getAssociateSampleId());
        innerAnalyseData.setWorkSheetFolderId(analyseData.getWorkSheetFolderId());
        innerAnalyseData.setSubId(analyseData.getSubId());
        innerAnalyseData.setSampleId(qcSample.getId());
        innerAnalyseData.setSampleTypeId(qcSample.getSampleTypeId());
        innerAnalyseData.setTestId(analyseData.getTestId());
        innerAnalyseData.setRedAnalyzeItemName(analyseData.getRedAnalyzeItemName());
        innerAnalyseData.setRedAnalyzeMethodName(analyseData.getRedAnalyzeMethodName());
        innerAnalyseData.setRedCountryStandard(analyseData.getRedCountryStandard());
        innerAnalyseData.setAnalyseItemId(analyseData.getAnalyseItemId());
        innerAnalyseData.setAnalyzeMethodId(analyseData.getAnalyzeMethodId());
        innerAnalyseData.setQcId(qcSample.getQcId());
        innerAnalyseData.setQcType(qcType);
        innerAnalyseData.setQcGrade(qcGrade);
        innerAnalyseData.setIsQm(analyseData.getIsQm());
        innerAnalyseData.setReceiveSubId(analyseData.getReceiveSubId());
        innerAnalyseData.setAnalyzeTime(analyseData.getAnalyzeTime());
        innerAnalyseData.setLowerLimit(analyseData.getLowerLimit());
        if (qcGrade.equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                && (qcType.equals(new QualityStandard().qcTypeValue())
                || qcType.equals(new CurveCheck().qcTypeValue()) || qcType.equals(new QualityReplace().qcTypeValue())
                || (proService.switchIsOpen(ProCodeHelper.LIM_JB_EDITABLE) && new QualityMark().qcTypeValue().equals(qcType)))) {
            innerAnalyseData.setMostSignificance(mostSignificance);
            innerAnalyseData.setMostDecimal(mostDecimal);
            innerAnalyseData.setDimensionId(dimensionId);
            innerAnalyseData.setDimension(dimension);
        } else {
            innerAnalyseData.setMostSignificance(analyseData.getMostSignificance());
            innerAnalyseData.setMostDecimal(analyseData.getMostDecimal());
            innerAnalyseData.setDimensionId(analyseData.getDimensionId());
            innerAnalyseData.setDimension(analyseData.getDimension());
        }
        //根据开关判断空白样有效位数和小数位数
        if (qualityBlankCanSetDecimalByCustomer()) {
            if (qcGrade.equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                    && qcType.equals(new QualityBlank().qcTypeValue())) {
                innerAnalyseData.setMostSignificance(mostSignificance);
                innerAnalyseData.setMostDecimal(mostDecimal);
            }
        }
        if (StringUtil.isNotEmpty(examLimitValue)) {
            innerAnalyseData.setExamLimitValue(examLimitValue);
        } else {
            innerAnalyseData.setExamLimitValue(analyseData.getExamLimitValue());
        }
        innerAnalyseData.setStatus(EnumAnalyseDataStatus.未测.name());
        innerAnalyseData.setDataStatus(EnumAnalyseDataStatus.未测.getValue());
        innerAnalyseData.setAnalystId(analyseData.getAnalystId());
        innerAnalyseData.setAnalystName(analyseData.getAnalystName());
        innerAnalyseData.setIsDataEnabled(false);
        innerAnalyseData.setIsCompleteField(analyseData.getIsCompleteField());
        innerAnalyseData.setIsOutsourcing(analyseData.getIsOutsourcing());
        innerAnalyseData.setRequireDeadLine(analyseData.getRequireDeadLine());
        innerAnalyseData.setGrade(analyseData.getGrade());
        innerAnalyseData.setDeptId(analyseData.getDeptId());
        innerAnalyseData.setGatherCode(qcSample.getCode());
        //如果是替代物，采集编号需要与原样保持一致
        if (new QualityReplace().qcTypeValue().equals(qcType)) {
            innerAnalyseData.setGatherCode(analyseData.getGatherCode());
        }
        return innerAnalyseData;
    }

    /**
     * 采样准备样品排序
     *
     * @param samples 样品集合
     */
    @Override
    public List<DtoSample> sortPrepareSample(List<DtoSample> samples, Boolean isFilter) {
        if (isFilter) {
            samples = samples.stream().filter(p -> !StringUtils.isNotNullAndEmpty(p.getCode())).collect(Collectors.toList());
        }
        if (samples.size() > 0) {
            List<String> qcIds = samples.stream().map(DtoSample::getQcId).filter(qcId -> !qcId.equals(UUIDHelper.GUID_EMPTY)).distinct().collect(Collectors.toList());
            List<DtoQualityControl> qcList = qcIds.size() > 0 ? qualityControlRepository.findAll(qcIds) : new ArrayList<>();
            // 获取空白样，并剔除原样集合中的空白样
            List<DtoSample> blankSamples = getBlankSamples(samples, qcList);
            // 处理根据项目类型排序样品，送样任务和现场任务，按照单位名称排序
            List<DtoSample> projectTypeSortSamples = handleProjectTypeSort(samples, blankSamples);
            if (StringUtil.isNotEmpty(projectTypeSortSamples)) {
                return projectTypeSortSamples;
            }
            SortSampleByKey(samples, qcList);
            setBlankSample(blankSamples, samples);
        }
        return samples;
    }

    /**
     * 现场任务样品排序（先按照采样日期正序，然后按照样品编号正序）
     *
     * @param samples 样品集合
     */
    @Override
    public List<DtoSample> sortSampleByReceiveDataAndCode(List<DtoSample> samples) {
        if (samples.size() > 0) {
            List<String> qcIds = samples.stream().map(DtoSample::getQcId).filter(qcId -> !qcId.equals(UUIDHelper.GUID_EMPTY)).distinct().collect(Collectors.toList());
            List<DtoQualityControl> qcList = qcIds.size() > 0 ? qualityControlRepository.findAll(qcIds) : new ArrayList<>();
            // 获取空白样，并剔除原样集合中的空白样
            List<DtoSample> blankSamples = getBlankSamples(samples, qcList);
            // 处理根据项目类型排序样品，送样任务和现场任务，按照单位名称排序
            List<DtoSample> projectTypeSortSamples = handleProjectTypeSort(samples, blankSamples);
            if (StringUtil.isNotEmpty(projectTypeSortSamples)) {
                return projectTypeSortSamples;
            }
            // 按照采样日期和样品编号排序
            Map<String, String> sampleValMap = this.getSampleValueMapForSortByCode(samples, qcList);
            Comparator<DtoSample> comparator = (a, b) -> {
                Comparable<String> aKey = getKeyForSort(sampleValMap, a);
                String bKey = getKeyForSort(sampleValMap, b);
                return aKey.compareTo(bKey);
            };
            samples.sort(comparator);
            setBlankSample(blankSamples, samples);
        }
        return samples;
    }


    /**
     * 现场任务根据原样获取空白样，并剔除原样集合中的空白样
     *
     * @param samples 原样集合
     * @param qcList  质控信息集合
     * @return 空白样集合
     */
    private List<DtoSample> getBlankSamples(List<DtoSample> samples, List<DtoQualityControl> qcList) {
        List<DtoSample> blankSamples = new ArrayList<>();
        List<String> blankQcIds = getBlankIds(qcList);
        if (blankQcIds.size() > 0) {
            blankSamples = samples.stream().filter(p -> blankQcIds.contains(p.getQcId())).collect(Collectors.toList());
            samples.removeAll(blankSamples);
            //空白关联的质控样
            List<String> blankSamIds = blankSamples.stream().map(DtoSample::getId).distinct().collect(Collectors.toList());
            List<DtoSample> qcBlankSamples = samples.stream().filter(p -> blankSamIds.contains(p.getAssociateSampleId())).collect(Collectors.toList());
            samples.removeAll(qcBlankSamples);
            blankSamples.addAll(qcBlankSamples);
        }
        return blankSamples;
    }


    /**
     * 根据项目类型排序样品（现场类、送样类）
     *
     * @param samples      原样集合
     * @param blankSamples 空白样集合
     * @return 排序后的样品
     */
    private List<DtoSample> handleProjectTypeSort(List<DtoSample> samples, List<DtoSample> blankSamples) {
        // 获取项目信息
        List<DtoSample> resultSample = new ArrayList<>();
        String projectId = samples.stream().map(DtoSample::getProjectId).distinct().collect(Collectors.toList()).stream().findFirst().orElse("");
        DtoProject project = projectRepository.findOne(projectId);
        if (StringUtil.isNotNull(project)) {
            //获取项目类型
            DtoProjectType projectType = projectTypeService.findOne(project.getProjectTypeId());
            String projectTypeCode = (String) JsonIterator.deserialize(projectType.getConfig(), Map.class).get("projectRegisterPage");
            if (EnumProjectType.现场类.getValue().equals(projectTypeCode) || EnumProjectType.送样类.getValue().equals(projectTypeCode)) {
                sortSampleForXC(samples);
                setBlankSample(blankSamples, samples);
                resultSample = samples;
            }
        }
        return resultSample;
    }

    /**
     * 根据key值排序样品
     *
     * @param samples 样品列表
     * @param qcList  质控信息列表
     */
    @Override
    public void SortSampleByKey(List<DtoSample> samples, List<DtoQualityControl> qcList) {
        Map<String, String> sampleValMap = getSampleValueMapForSort(samples, qcList);
        Comparator<DtoSample> comparator = (a, b) -> {
            Comparable<String> aKey = getKeyForSort(sampleValMap, a);
            String bKey = getKeyForSort(sampleValMap, b);
            return aKey.compareTo(bKey);
        };
        samples.sort(comparator);
    }

    /**
     * 现场/送样任务样品排序
     *
     * @param sampleList 样品列表
     */
    @Override
    public void sortSampleForXC(List<DtoSample> sampleList) {
        sampleList.sort(Comparator.comparing(DtoSample::getCode));
    }

    /**
     * 获取样品排序key值
     *
     * @param sampleValMap 样品排序的key值映射
     * @param sample       样品对象
     */
    @Override
    public String getKeyForSort(Map<String, String> sampleValMap, DtoSample sample) {
        String aKey = "";
        if (sampleValMap.containsKey(sample.getId()) || sampleValMap.containsKey(sample.getAssociateSampleId())) {
            aKey = sampleValMap.containsKey(sample.getId()) ? sampleValMap.get(sample.getId()) :
                    String.format("%s_%d", sampleValMap.get(sample.getAssociateSampleId()), sample.getSampleCategory());
        }
        return aKey;
    }

    /**
     * 获取样品排序的key值映射
     *
     * @param samples 样品列表
     * @param qcList  质控信息列表
     *                样品排序的key值映射
     */
    @Override
    public Map<String, String> getSampleValueMapForSort(List<DtoSample> samples, List<DtoQualityControl> qcList) {
        Map<String, String> sampleValMap = new HashMap<>();
        Format format = new DecimalFormat("000");
        for (DtoSample sam : samples) {
            if (sam.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                String val = String.format("%s_%s_%s_%s", sam.getRedFolderName().replace(String.format("(%d-%d-%d)",
                        sam.getCycleOrder(), sam.getTimesOrder(), sam.getSampleOrder()), ""), format.format(sam.getCycleOrder()),
                        format.format(sam.getTimesOrder()), format.format(sam.getSampleOrder()));
                sampleValMap.put(sam.getId(), val);
            } else if (sam.getSampleCategory().equals(EnumSampleCategory.质控样.getValue())) {
                //平行样
                Optional<DtoQualityControl> qcOptional = qcList.stream().filter(p -> p.getId().equals(sam.getQcId())).findFirst();
                if (qcOptional.isPresent()) {
                    if (qcOptional.get().getQcType().equals(new QualityParallel().qcTypeValue())) {
                        //找到原样的点位名称
                        Optional<DtoSample> yySample = samples.stream().filter(p -> p.getId().equals(sam.getAssociateSampleId())).findFirst();
                        if (yySample.isPresent()) {
                            String val = String.format("%s_%s_%s_%s_%s", yySample.get().getRedFolderName().replace(String.format("(%d-%d-%d)",
                                    yySample.get().getCycleOrder(), yySample.get().getTimesOrder(), yySample.get().getSampleOrder()), ""),
                                    format.format(yySample.get().getCycleOrder()), format.format(yySample.get().getTimesOrder()),
                                    format.format(yySample.get().getSampleOrder()), sam.getSampleCategory());
                            sampleValMap.put(sam.getId(), val);
                        }
                    }
                }
            }
        }
        return sampleValMap;
    }


    /**
     * 获取样品排序的key值映射 根据采样日期和样品编号组装value
     *
     * @param samples 样品集合
     * @param qcList  质控信息集合
     * @return 样品排序的key值映射
     */
    public Map<String, String> getSampleValueMapForSortByCode(List<DtoSample> samples, List<DtoQualityControl> qcList) {
        Map<String, String> sampleValMap = new HashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.YEAR);
        for (DtoSample sam : samples) {
            if (StringUtil.isNotEmpty(sam.getCode())) {
                if (sam.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
                    String val = String.format("%s_%s", dateFormat.format(sam.getSamplingTimeBegin()), sam.getCode());
                    sampleValMap.put(sam.getId(), val);
                } else if (sam.getSampleCategory().equals(EnumSampleCategory.质控样.getValue())) {
                    //平行样
                    Optional<DtoQualityControl> qcOptional = qcList.stream().filter(p -> p.getId().equals(sam.getQcId())).findFirst();
                    if (qcOptional.isPresent()) {
                        if (new QualityParallel().qcTypeValue().equals(qcOptional.get().getQcType())
                                || new QualityCipherParallel().qcTypeValue().equals(qcOptional.get().getQcType())) {
                            //找到原样的点位名称
                            Optional<DtoSample> yySample = samples.stream().filter(p -> p.getId().equals(sam.getAssociateSampleId())).findFirst();
                            if (yySample.isPresent()) {
                                String val = String.format("%s_%s_%s", dateFormat.format(yySample.get().getSamplingTimeBegin()), yySample.get().getCode(),
                                        yySample.get().getSampleCategory());
                                sampleValMap.put(sam.getId(), val);
                            }
                        }
                    }
                }
            }
        }
        return sampleValMap;
    }

    /**
     * 排序时需要把空白数据放到最下面
     *
     * @param qcList 质控数据集合
     * @return 空白样品ids
     */
    protected List<String> getBlankIds(List<DtoQualityControl> qcList) {
        return qcList.stream().filter(p -> p.getQcType().equals(new QualityBlank().qcTypeValue())
                || p.getQcType().equals(new QualityTransportBlank().qcTypeValue())
                || p.getQcType().equals(new QualityLocalBlank().qcTypeValue())
                || p.getQcType().equals(new QualityInstrumentBlank().qcTypeValue()))
                .map(DtoQualityControl::getId).collect(Collectors.toList());
    }

    /**
     * 把空白样放回样品列表
     *
     * @param blankSamples 空白样集合
     * @param samples      样品集合
     */
    protected void setBlankSample(List<DtoSample> blankSamples, List<DtoSample> samples) {
        if (blankSamples.size() > 0) {
            for (DtoSample blank : blankSamples) {
                blank.setQcType(new QualityBlank().qcTypeValue());
            }
            // 需要根据一个唯一标识排序，保证空白样在采样准备查询中和生成样品编号中，两边排序方式保持一致
            blankSamples.sort(Comparator.comparing(DtoSample::getCode).thenComparing(DtoSample::getId));
            samples.addAll(blankSamples);
        }
    }

    protected List<DtoSample> sortByList(List<DtoSample> sampleList) {
        return this.sortPrepareSample(sampleList, false);
    }

    /**
     * 获取样品名称
     *
     * @param sample    样品
     * @param watchSpot 点位名称
     * @return 样品名称
     */
    @Override
    public String getSampleName(DtoSample sample, String watchSpot) {
        if (!StringUtils.isNotNullAndEmpty(watchSpot)) {
            watchSpot = sample.getRedFolderName().replace(String.format("(%d-%d)", sample.getCycleOrder(), sample.getTimesOrder()), "");
        }
        DtoSampleType samType = sampleTypeService.findOne(sample.getSampleTypeId());
        String sampleTypeName = StringUtil.isNotNull(samType) ? samType.getTypeName() : "";
        String folderName = UUIDHelper.GUID_EMPTY.equals(sample.getSampleFolderId()) ? sample.getRedFolderName() : watchSpot;
        return String.format("%s%s%s",
                folderName,
                StringUtils.isNotNullAndEmpty(sampleTypeName) ? String.format("(%s)", sampleTypeName) : "",
                StringUtils.isNotNullAndEmpty(sample.getCode()) ? "-" + sample.getCode() : "");
    }

    /**
     * 获取关联样品id
     *
     * @param sampleCategory 样品类别
     * @param qcGrade        质控等级
     * @param qcType         质控类型
     * @param oldSample      原样
     * @return 关联样品id
     */
    private String getAssociateSampleId(Integer sampleCategory, Integer qcGrade, Integer qcType, DtoSample oldSample) {
        if (sampleCategory.equals(EnumSampleCategory.质控样.getValue())) {
            if (qcType.equals(EnumLIM.EnumQCType.质控样.getValue()) || qcType.equals(new QualityStandard().qcTypeValue()) ||
                    (qcGrade.equals(EnumLIM.EnumQCGrade.内部质控.getValue())
                            && (qcType.equals(new QualityBlank().qcTypeValue()) || qcType.equals(new QualityDilutionWater().qcTypeValue())))) {
                return UUIDHelper.GUID_EMPTY;
            }
            return oldSample.getId();
        } else if (sampleCategory.equals(EnumSampleCategory.原样加原样.getValue())) {
            return oldSample.getSampleCategory().equals(EnumSampleCategory.原样加原样.getValue()) ? oldSample.getAssociateSampleId() : oldSample.getId();
        }
        return oldSample.getId();
    }

    /**
     * 获取样品点位名称
     *
     * @param sample 样品
     * @param spots  点位名称、点位编号...
     * @return 返回样品点位名称
     */
    private String getFolderName(DtoSample sample, String... spots) {
        String folderName = Arrays.stream(spots).filter(StringUtils::isNotNullAndEmpty).collect(Collectors.joining("_"));
        if (StringUtils.isNotNullAndEmpty(folderName)) {
            folderName = String.format("%s(%d-%d-%d)", folderName, sample.getCycleOrder(),
                    sample.getTimesOrder(), sample.getSampleOrder());
        }
        return folderName;
    }

    /**
     * 获取样品的点位名称
     *
     * @param sample 样品
     * @return 返回样品的点位名称
     */
    private String getSampleWatchSpot(DtoSample sample) {
        if (sample.getSampleCategory().equals(EnumSampleCategory.原样.getValue())) {
            DtoSampleFolder folder = sampleFolderRepository.findOne(sample.getSampleFolderId());
            if (StringUtil.isNotNull(folder)) {
                return folder.getWatchSpot();
            }
        }
        return sample.getRedFolderName();
    }

    /**
     * 核查是否存在内部送样的原样
     *
     * @param samples 样品集合
     */
    private void checkInnerSample(List<DtoSample> samples) {
        if (samples.stream().anyMatch(p -> p.getReceiveId().equals(UUIDHelper.GUID_EMPTY) && p.getSampleCategory().equals(EnumSampleCategory.原样.getValue()))) {
            throw new BaseException("所选样品存在原样，请重新确认！");
        }
        List<String> receiveIds = samples.stream().filter(p -> p.getSampleCategory().equals(EnumSampleCategory.原样.getValue())).map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        receiveIds.remove(UUIDHelper.GUID_EMPTY);
        if (receiveIds.size() > 0) {
            List<DtoReceiveSampleRecord> records = receiveSampleRecordRepository.findAll(receiveIds);
            if (records.stream().anyMatch(p -> p.getReceiveType().equals(EnumReceiveType.内部送样.getValue()))) {
                throw new BaseException("所选样品存在原样，请重新确认！");
            }
        }
    }

    /**
     * 获取关联样的点位名称
     *
     * @param sampleCategory 样品类别
     * @param qcGrade        质控等级
     * @param qcType         质控类型
     * @param oldSample      原样
     * @return 关联样品id
     */
    private String getAssociateRedFolderName(Integer sampleCategory, Integer qcGrade, Integer qcType, DtoSample oldSample) {
        EnumSampleCategory category = EnumSampleCategory.getByValue(sampleCategory);
        if (StringUtil.isNull(category)) {
            return "";
        }
        switch (category) {
            case 串联样:
            case 比对样:
            case 洗涤剂:
                return StringUtils.isNotNullAndEmpty(oldSample.getRedFolderName()) ? String.format("%s(%s)", oldSample.getRedFolderName(), category) : category.toString();
            case 原样加原样:
                return StringUtils.isNotNullAndEmpty(oldSample.getRedFolderName()) ? oldSample.getRedFolderName() : "原样";
            case 质控样:
                return this.getQCRedFolderName(qcGrade, qcType, oldSample);
        }
        return "";
    }

    /**
     * 获取质控样的点位名称
     *
     * @param qcGrade   质控等级
     * @param qcType    质控类型
     * @param oldSample 原样
     * @return 关联样品id
     */
    private String getQCRedFolderName(Integer qcGrade, Integer qcType, DtoSample oldSample) {
        return QualityTaskFactory.getInstance().getQcSample(qcType).getRedFolderName(oldSample.getRedFolderName(), qcGrade);
    }


    /**
     * 获取相应的map数据源
     *
     * @param dtoAnalyseData 分析数据
     * @param dtoSample      样品数据
     * @param formula        公式
     * @param formulaId      公式id
     * @param qcValue        质控值
     * @param qcVolume       加标体积
     * @param qcCode         标样编号
     * @param sampleRemark   备注
     * @return 返回想要的map集合
     */
    @Override
    public Map<String, Object> getAnalyseDataMap(DtoAnalyseData dtoAnalyseData,
                                                 DtoSample dtoSample,
                                                 String formula,
                                                 String formulaId,
                                                 String qcValue,
                                                 String qcVolume,
                                                 String qcCode,
                                                 String sampleRemark
    ) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", dtoAnalyseData.getId());
        map.put("analyzeItemId", dtoAnalyseData.getAnalyseItemId());
        map.put("analyzeMethodId", dtoAnalyseData.getAnalyzeMethodId());
        map.put("associateSampleId", dtoAnalyseData.getAssociateSampleId());
        map.put("dataInputTime", dtoAnalyseData.getDataInputTime());
        map.put("dimension", dtoAnalyseData.getDimension());
        map.put("dimensionId", dtoAnalyseData.getDimensionId());
        map.put("examLimitValue", dtoAnalyseData.getExamLimitValue());
        map.put("grade", dtoAnalyseData.getGrade());
        map.put("isQC", StringUtils.isNotNullAndEmpty(dtoSample.getQcId()) && !dtoSample.getQcId().equals(UUIDHelper.GUID_EMPTY));
        map.put("mostDecimal", dtoAnalyseData.getMostDecimal());
        map.put("mostSignificance", dtoAnalyseData.getMostSignificance());
        map.put("qcCode", qcCode);
        map.put("qcGrade", dtoAnalyseData.getQcGrade());
        map.put("qcId", dtoAnalyseData.getQcId());
        map.put("qcType", dtoAnalyseData.getQcType());
        map.put("qcValue", qcValue);
        map.put("qcVolume", qcVolume);
        map.put("receiveId", dtoSample.getReceiveId());
        map.put("redAnalyzeItemName", dtoAnalyseData.getRedAnalyzeItemName());
        map.put("redAnalyzeMethodName", dtoAnalyseData.getRedAnalyzeMethodName());
        map.put("redCountryStandard", dtoAnalyseData.getRedCountryStandard());
        map.put("redFolderName", dtoSample.getRedFolderName());
        map.put("requireDeadLine", dtoAnalyseData.getRequireDeadLine());
        map.put("sampleCode", dtoSample.getCode());
        map.put("sampleId", dtoAnalyseData.getSampleId());
        map.put("sampleRemark", sampleRemark);
        map.put("sampleTypeId", dtoSample.getSampleTypeId());
        map.put("sampleTypeName", dtoAnalyseData.getSampleTypeName());
        map.put("sampleTime", dtoSample.getSamplingTimeBegin());
        map.put("testId", dtoAnalyseData.getTestId());
        map.put("testOrignValue", dtoAnalyseData.getTestOrignValue());
        map.put("testValue", dtoAnalyseData.getTestValue());
        map.put("testValueD", dtoAnalyseData.getTestValueD());
        map.put("testValueDstr", dtoAnalyseData.getTestValueDstr());
        map.put("workSheetId", dtoAnalyseData.getWorkSheetId());
        map.put("dataStatus", dtoAnalyseData.getDataStatus());
        map.put("formula", formula);
        map.put("formulaId", formulaId);
        map.put("sampleCategory", dtoSample.getSampleCategory());
        map.put("inspectedEnt", dtoSample.getInspectedEnt());
        map.put("inspectedEntId", dtoSample.getInspectedEntId());
        map.put("gatherCode", dtoAnalyseData.getGatherCode());
        return map;
    }


    /**
     * 保存外部样品信息
     *
     * @param outSample 样品信息
     * @param record    送样单
     * @param project   项目信息
     * @return 返回相应的信息
     */
    private DtoSample saveOutSample(DtoOutSample outSample,
                                    DtoReceiveSampleRecord record,
                                    DtoProject project) {
        DtoSample dto = new DtoSample();
        BeanUtils.copyProperties(outSample, dto);
        DtoSamplingFrequency frequency = this.getFrequency(dto, outSample);
        dto.setSampleFolderId(frequency.getSampleFolderId());
        outSample.setSampleFolderId(frequency.getSampleFolderId());
        dto.setSamplingFrequencyId(frequency.getId());
        dto.setRedFolderName(frequency.getRedFolderName());
        dto.setInceptTime(new Date());
        dto.setSamplingTimeBegin(StringUtil.isNotNull(outSample.getSamplingTimeBegin()) ? outSample.getSamplingTimeBegin() : record.getSamplingTime());
        dto.setSamplingTimeEnd(StringUtil.isNotNull(outSample.getSamplingTimeBegin()) ? outSample.getSamplingTimeBegin() : record.getSamplingTime());
        dto.setSampleCategory(EnumSampleCategory.原样.getValue());
        dto.setSampleOrder(outSample.getSampleOrder());
        dto.setSamplingPersonId(PrincipalContextUser.getPrincipal().getUserId());
        dto.setAssociateSampleId(UUIDHelper.GUID_EMPTY);
        dto.setSubProjectId(UUIDHelper.GUID_EMPTY);
        dto.setDataChangeStatus(EnumSampleChangeStatus.未变更.getValue());
        dto.setQcId(UUIDHelper.GUID_EMPTY);
        dto.setParentSampleId(UUIDHelper.GUID_EMPTY);
        dto.setSamplingConfig(EnumSamplingConfig.未分配.getValue());
        dto.setIsKeep(false);
        dto.setIsPrint(EnumPrintStatus.未打印.getValue());
        dto.setBlindType(EnumSampleBlindType.非盲样.getValue());
        dto.setPreparedStatus(EnumPreParedStatus.未制备.getValue());
        dto.setConsistencyValidStatus(0);
        dto.setSignerId(UUIDHelper.GUID_EMPTY);
        dto.setSignTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        dto.setSamplingRecordId(UUIDHelper.GUID_EMPTY);
        dto.setIsOutsourcing(0);
        dto.setSamKind(0);
        dto.setIsQualified(false);
        dto.setIsReturned(false);
        dto.setKeepLongTime(0);
        dto.setLoseEfficacyTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        dto.setLastNewSubmitTime(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
        dto.setInnerReceiveStatus(EnumInnerReceiveStatus.不能领取.getValue());
        dto.setStatus(EnumSampleStatus.样品未领样.toString());//默认样品未领样
        dto.setLat(StringUtil.isNotEmpty(outSample.getLat()) ? outSample.getLat() : "");
        dto.setLon(StringUtil.isNotEmpty(outSample.getLon()) ? outSample.getLon() : "");
        if (record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue())) {
            dto.setStatus(EnumSampleStatus.样品未领样.toString());
            dto.setAnanlyzeStatus(EnumAnalyzeStatus.不能分析.getValue());
        } else {
            if (!record.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue())) {
                record.setStatus(EnumLIM.EnumReceiveRecordStatus.已经送样.toString());
                record.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.已经送样.getValue());
                receiveSampleRecordService.update(record);
            }

            if (StringUtil.isNotNull(outSample.getTest())) {
                List<DtoSample> list = Collections.singletonList(dto);
                dto.setRedAnalyzeItems(proService.getAnalyzeItemsByTest(outSample.getTest()));
                if (outSample.getTest().stream().anyMatch(p -> !p.getIsCompleteField() && !p.getIsOutsourcing())) {//存在不分包的实验室指标
                    DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumSubRecordType.分析.getValue());
                    if (StringUtil.isNull(subRecord)) {
                        receiveSubSampleRecordService.createSubRecord(record, list, EnumSubRecordType.分析.getValue());
                        dto.setStatus(EnumSampleStatus.样品未领样.toString());
                        dto.setInnerReceiveStatus(EnumInnerReceiveStatus.可以领取.getValue());
                        dto.setAnanlyzeStatus(EnumAnalyzeStatus.不能分析.getValue());
                    } else {
                        if ((subRecord.getSubStatus() & (EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue())) != 0) {
                            dto.setStatus(EnumSampleStatus.样品待检.toString());
                            dto.setInnerReceiveStatus(EnumInnerReceiveStatus.已经领取.getValue());
                            dto.setAnanlyzeStatus(EnumAnalyzeStatus.可以分析.getValue());
                        } else {
                            dto.setStatus(EnumSampleStatus.样品未领样.toString());
                            dto.setInnerReceiveStatus(EnumInnerReceiveStatus.可以领取.getValue());
                            dto.setAnanlyzeStatus(EnumAnalyzeStatus.不能分析.getValue());
                        }
                    }
                }
                if (outSample.getTest().stream().anyMatch(p -> p.getIsCompleteField() && !p.getIsOutsourcing())) {//存在不分包的现场指标
                    DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumSubRecordType.现场.getValue());
                    if (StringUtil.isNull(subRecord)) {
                        receiveSubSampleRecordService.createSubRecord(record, list, EnumSubRecordType.现场.getValue());
                    }
                    dto.setStatus(EnumSampleStatus.样品待检.toString());
                    //只存在现场数据，改成已经领取
                    if (dto.getInnerReceiveStatus().equals(EnumInnerReceiveStatus.不能领取.getValue())) {
                        dto.setInnerReceiveStatus(EnumInnerReceiveStatus.已经领取.getValue());
                    }
                    dto.setAnanlyzeStatus(EnumAnalyzeStatus.可以分析.getValue());
                }
                if (outSample.getTest().stream().allMatch(DtoTest::getIsOutsourcing)) {//所有指标均分包
                    dto.setStatus(EnumSampleStatus.样品检毕.toString());
                    dto.setInnerReceiveStatus(EnumInnerReceiveStatus.不能领取.getValue());
                    //若送样单下存在实验室领样单，则调整领取状态为对应的
                    DtoReceiveSubSampleRecord subRecord = receiveSubSampleRecordService.findByReceiveIdAndType(record.getId(), EnumSubRecordType.分析.getValue());
                    if (StringUtil.isNotNull(subRecord)) {
                        if ((subRecord.getSubStatus() & (EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue())) != 0) {
                            dto.setInnerReceiveStatus(EnumInnerReceiveStatus.已经领取.getValue());
                        } else {
                            dto.setInnerReceiveStatus(EnumInnerReceiveStatus.可以领取.getValue());
                        }
                    }
                    dto.setAnanlyzeStatus(EnumAnalyzeStatus.不需要分析.getValue());
                }
            }
        }

        dto.setSamplingStatus(EnumSamplingStatus.已经完成取样.getValue());
        dto.setStoreStatus(EnumStoreStatus.不能存储.getValue());
        dto.setMakeStatus(EnumMakeStatus.不需要制样.getValue());
        dto.setSortNum(getSortOrder(dto, getQualityList()));
        this.saveSampleParams(dto.getId(), outSample.getParamsData());
        DtoSample sample = repository.save(dto);

        DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, sample, record, outSample.getTest(), false);
        addDto.setIsAddAssociateTest(false);
        analyseDataService.addAnalyseData(addDto);

        Set<String> tIds = outSample.getTest().stream().map(DtoTest::getId).collect(Collectors.toSet());
        sampleJudgeDataService.createJudgeDataBySampleTest(Collections.singletonList(sample), tIds, new ArrayList<>(), project);

        String comment = String.format("新增了样品%s", this.getSampleName(dto, frequency.getWatchSpot()));
        newLogService.createLog(dto.getId(), comment, "", EnumLogType.样品增删作废.getValue(), EnumLogObjectType.样品.getValue(), EnumLogOperateType.增加样品.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        proService.checkSample(Collections.singletonList(sample), project);
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.添加外部样品, dto.getProjectId(), dto.getReceiveId());
                    }
                }
        );
        return dto;
    }

    private List<String> getChangeContent(Map<String, Map<String, Object>> map, EnumSampleField... fields) {
        String format = "</br>%s由'%s',修改为'%s'";
        List<String> contents = new ArrayList<>();
        for (EnumSampleField field : fields) {
            if (map.containsKey(field.getValue())) {
                String from = StringUtil.isNull(map.get(field.getValue()).get("from")) ? "" : map.get(field.getValue()).get("from").toString();
                String to = StringUtil.isNull(map.get(field.getValue()).get("to")) ? "" : map.get(field.getValue()).get("to").toString();
                contents.add(String.format(format, field.toString(), from, to));
            }
        }
        return contents;
    }

    private Map<String, Object> sampleParams(DtoSample sample, Map<String, String> samTypeMap, String redAnalyzeItems,
                                             List<DtoQualityControl> qcList, DtoSampleTypeGroup group, DtoParamsConfig paramsConfig,
                                             Map<String, String> allParamsDataMap) {
        Map<String, Object> mapObject = new HashMap<>();
        mapObject.put("id", sample.getId());
        mapObject.put("code", sample.getCode());
        mapObject.put("sampleTypeId", sample.getSampleTypeId());
        mapObject.put("sampleTypeName", samTypeMap.getOrDefault(sample.getSampleTypeId(), ""));
        mapObject.put("redFolderName", sample.getRedFolderName());
        mapObject.put("samplingTimeBegin", sample.getSamplingTimeBegin());
        mapObject.put("inspectedEntId", sample.getInspectedEntId());
        mapObject.put("inspectedEnt", sample.getInspectedEnt());
        mapObject.put("sampleFolderId", sample.getSampleFolderId());
        mapObject.put("cycleOrder", sample.getCycleOrder());
        mapObject.put("timesOrder", sample.getTimesOrder());
        mapObject.put("sampleCategory", sample.getSampleCategory());
        mapObject.put("associateSampleId", sample.getAssociateSampleId());
        DtoQualityControl qualityControl = qcList.stream().filter(p -> p.getId().equals(sample.getQcId())).findFirst().orElse(null);
        if (StringUtil.isNotNull(qualityControl)) {
            mapObject.put("qcGrade", qualityControl.getQcGrade());
            mapObject.put("qcType", qualityControl.getQcType());
        } else {
            mapObject.put("qcGrade", -1);
            mapObject.put("qcType", -1);
        }
        String key = String.format("%s;%s;%s", sample.getId(), paramsConfig.getId(), group.getId());
        mapObject.put("groupId", group.getId());
        mapObject.put("paramsId", paramsConfig.getId());
        mapObject.put("paramsName", paramsConfig.getAlias());
        mapObject.put("dimension", paramsConfig.getDimension());
        String paramsValue = allParamsDataMap.getOrDefault(key, "");
        //参数判定为体积类型，需要获取分组上的体积类型
        if (paramsConfig.getAlias().equals("体积类型") && !StringUtil.isNotEmpty(paramsValue)) {
            paramsValue = group.getVolumeType();
            //需要保存体积类型的数据
            saveParamsDataVolume(sample, paramsConfig, paramsValue, group.getId());
        }
        mapObject.put("paramsValue", paramsValue);
        mapObject.put("paramsType", EnumLIM.EnumParamsType.EnumParamsType(paramsConfig.getParamsType()));
        mapObject.put("paramsOrderNumber", paramsConfig.getOrderNum());
        mapObject.put("groupName", group.getGroupName());
        mapObject.put("redAnalyzeItems", redAnalyzeItems);
        return mapObject;
    }

    private Map<String, Object> sampleParams(DtoSample sample, Map<String, String> samTypeMap, String redAnalyzeItems,
                                             List<DtoQualityControl> qcList, Map<String, Object> paramsValue) {
        Map<String, Object> mapObject = new HashMap<>();
        mapObject.put("id", sample.getId());
        mapObject.put("code", sample.getCode());
        mapObject.put("sampleTypeId", sample.getSampleTypeId());
        mapObject.put("sampleTypeName", samTypeMap.getOrDefault(sample.getSampleTypeId(), ""));
        mapObject.put("redFolderName", sample.getRedFolderName());
        mapObject.put("samplingTimeBegin", sample.getSamplingTimeBegin());
        mapObject.put("inspectedEntId", sample.getInspectedEntId());
        mapObject.put("inspectedEnt", sample.getInspectedEnt());
        mapObject.put("sampleFolderId", sample.getSampleFolderId());
        mapObject.put("cycleOrder", sample.getCycleOrder());
        mapObject.put("timesOrder", sample.getTimesOrder());
        mapObject.put("sampleCategory", sample.getSampleCategory());
        mapObject.put("associateSampleId", sample.getAssociateSampleId());
        DtoQualityControl qualityControl = qcList.stream().filter(p -> p.getId().equals(sample.getQcId())).findFirst().orElse(null);
        if (StringUtil.isNotNull(qualityControl)) {
            mapObject.put("qcGrade", qualityControl.getQcGrade());
            mapObject.put("qcType", qualityControl.getQcType());
        } else {
            mapObject.put("qcGrade", -1);
            mapObject.put("qcType", -1);
        }
        mapObject.put("redAnalyzeItems", redAnalyzeItems);
        paramsValue.forEach(mapObject::put);
        return mapObject;
    }

    private DtoGenerateSN genSampleCode(DtoReceiveSampleRecord record, DtoSample sample, DtoProject project, DtoProjectType projectType, String sampleTypeId,
                                        Date samplingTime, String samplingPersonId, Map<String, String> codeMap) {
        DtoGenerateSN targetGenerateSN;
        if (EnumSampleCategory.原样.getValue().equals(sample.getSampleCategory())) {
            targetGenerateSN = this.createSampleCode(project, projectType, sampleTypeId, sample.getSampleFolderId(), samplingTime, getSamplingPerson(record), sample.getId(), false,
                    getSamplingPerson(record), false);
        } else {
            targetGenerateSN = this.createSampleCode(project, projectType, sampleTypeId, sample.getSampleFolderId(), samplingTime, getSamplingPerson(record), sample.getId(), false,
                    getSamplingPerson(record), true, sample.getAssociateSampleId(),
                    sample.getQcId(), sample.getSampleCategory(), false, codeMap.getOrDefault(sample.getAssociateSampleId(), ""));
        }
        //需要新增的序列号
        if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigCreate())) {
            serialNumberConfigRepository.saveAndFlush(targetGenerateSN.getSerialNumberConfigCreate());
        }
        //需要修改的序列号
        if (StringUtil.isNotNull(targetGenerateSN.getSerialNumberConfigUpdate())) {
            serialNumberConfigRepository.saveAndFlush(targetGenerateSN.getSerialNumberConfigUpdate());
        }

        return targetGenerateSN;
    }

    //#endregion

    /**
     * 空白样的有效位数和小数位数是否使用客户填写的值
     *
     * @return 静安个性化
     */
    protected Boolean qualityBlankCanSetDecimalByCustomer() {
        return Boolean.FALSE;
    }

    /**
     * 新增全程序空白时设置默认检出限为0
     *
     * @return 静安个性化，产品返回false关闭开关
     */
    protected Boolean allProcessBlankExamLimitValueDefaultZero() {
        return Boolean.FALSE;
    }

    /**
     * 保存体积类型
     *
     * @param sample       样品
     * @param paramsConfig 参数
     * @param paramsValue  参数值
     * @param groupId      分组id
     */
    private void saveParamsDataVolume(DtoSample sample, DtoParamsConfig paramsConfig, String paramsValue, String groupId) {
        //先做一个判断
        Optional<DtoParamsData> optional = paramsDataRepository.findByObjectTypeAndObjectIdInAndParamsConfigId(EnumParamsDataType.样品.getValue(),
                Arrays.asList(sample.getId()), paramsConfig.getId()).stream().filter(p -> StringUtil.isNotEmpty(p.getGroupId()) && p.getGroupId().equals(groupId)).findFirst();
        if (!optional.isPresent()) {
            DtoParamsData paramsData = new DtoParamsData();
            paramsData.setObjectId(sample.getId());
            paramsData.setObjectType(EnumParamsDataType.样品.getValue());
            paramsData.setParamsConfigId(paramsConfig.getId());
            paramsData.setParamsName(paramsConfig.getAlias());
            paramsData.setParamsValue(paramsValue);
            paramsData.setDimension(paramsConfig.getDimension());
            paramsData.setDimensionId(paramsConfig.getDimensionId());
            paramsData.setOrderNum(paramsConfig.getOrderNum());
            paramsData.setGroupId(groupId);
            paramsDataRepository.save(paramsData);
        } else {
            optional.get().setParamsValue(paramsValue);
            paramsDataRepository.save(optional.get());
        }
    }

    /**
     * 获取当前测试项目上一次加对应类型质控样（加标，曲线校核）的量纲信息
     *
     * @param qc     质控对象
     * @param testId 测试项目id
     */
    protected void getQcDimensionInfo(DtoQualityControl qc, String testId) {
        List<Object[]> objArrList = commonRepository.find("select a.id, a.qcVolumeDimensionId, a.qcValueDimensionId, a.qcTestValueDimensionId, a.realSampleTestValueDimensionId from DtoQualityControl a " +
                "where a.isDeleted = 0 and a.qcType = :qcType", Collections.singletonMap("qcType", qc.getQcType()));
        if (StringUtil.isNotEmpty(objArrList)) {
            List<String> qcIdList = objArrList.stream().map(p -> p[0].toString()).distinct().collect(Collectors.toList());
            List<Object[]> sampleIdQcIdArrList = commonRepository.find("select a.id, a.qcId from DtoSample a where a.qcId in :qcIdList and a.isDeleted = 0",
                    Collections.singletonMap("qcIdList", qcIdList));
            List<String> sampleIdList = new ArrayList<>();
            Map<String, String> sampleId2QcIdMap = new HashMap<>();
            for (Object[] sampleIdQcId : sampleIdQcIdArrList) {
                sampleId2QcIdMap.put(sampleIdQcId[0].toString(), sampleIdQcId[1].toString());
                sampleIdList.add(sampleIdQcId[0].toString());
            }
            if (StringUtil.isNotEmpty(sampleIdList)) {
                Map<String, Object> values = new HashMap<>();
                values.put("sampleIdList", sampleIdList);
                values.put("testId", testId);
                List<String> fltSampleIdList = commonRepository.find("select a.sampleId from DtoAnalyseData a where a.testId = :testId and a.sampleId in :sampleIdList and a.isDeleted = 0 " +
                        "order by createDate desc", values);
                if (StringUtil.isNotEmpty(fltSampleIdList)) {
                    String qcId = sampleId2QcIdMap.getOrDefault(fltSampleIdList.get(0), null);
                    if (StringUtil.isNotNull(qcId)) {
                        Object[] objArr = objArrList.stream().filter(p -> p[0].equals(qcId)).findFirst().orElse(null);
                        if (StringUtil.isNotNull(objArr)) {
                            qc.setQcVolumeDimensionId(StringUtil.isNotNull(objArr[1]) ? objArr[1].toString() : null);
                            qc.setQcValueDimensionId(StringUtil.isNotNull(objArr[2]) ? objArr[2].toString() : null);
                            qc.setQcTestValueDimensionId(StringUtil.isNotNull(objArr[3]) ? objArr[3].toString() : null);
                            qc.setRealSampleTestValueDimensionId(StringUtil.isNotNull(objArr[4]) ? objArr[4].toString() : null);
                        }
                    }
                }
            }
        }
    }

    /**
     * 批量添加质控样时获取当前测试项目上一次配置的量纲
     *
     * @param qcType     质控类型
     * @param testIdList 测试项目id列表
     */
    @Override
    public Map<String, Map<String, String>> getQcDimensionInfoForTests(int qcType, List<String> testIdList) {
        Map<String, Map<String, String>> resMap = new HashMap<>();
        Map<String, Map<String, String>> existMap = new HashMap<>();
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findByTestIdInAndQcTypeAndIsDeletedFalse(testIdList, qcType);
        if (StringUtil.isNotEmpty(analyseDataList)) {
            List<String> qcIdList = analyseDataList.stream().map(DtoAnalyseData::getQcId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(qcIdList)) {
                List<DtoQualityControl> qcList = qualityControlRepository.findAll(qcIdList);
                Map<String, List<DtoAnalyseData>> sampleIdCreateDateArrMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId));
                for (Map.Entry<String, List<DtoAnalyseData>> entry : sampleIdCreateDateArrMap.entrySet()) {
                    List<DtoAnalyseData> loopArrList = entry.getValue();
                    List<String> qcIds = loopArrList.stream().map(DtoAnalyseData::getQcId).collect(Collectors.toList());
                    Optional<DtoQualityControl> objArr = qcList.stream().filter(p -> qcIds.contains(p.getId())).max(Comparator.comparing(DtoQualityControl::getQcTime));
                    if (objArr.isPresent()) {
                        Map<String, String> dimensionIdMap = new HashMap<>();
                        dimensionIdMap.put("qcVolumeDimensionId", objArr.get().getQcVolumeDimensionId());
                        dimensionIdMap.put("qcValueDimensionId", objArr.get().getQcValueDimensionId());
                        dimensionIdMap.put("qcTestValueDimensionId", objArr.get().getQcTestValueDimensionId());
                        dimensionIdMap.put("realSampleTestValueDimensionId", objArr.get().getRealSampleTestValueDimensionId());
                        dimensionIdMap.put("ssConcentrationDimensionId", objArr.get().getSsConcentrationDimensionId());
                        dimensionIdMap.put("constantVolumeDimensionId", objArr.get().getConstantVolumeDimensionId());
                        Optional<DtoAnalyseData> anaOptional = loopArrList.stream().filter(p -> objArr.get().getId().equals(p.getQcId())).findFirst();
                        if (anaOptional.isPresent()) {
                            dimensionIdMap.put("mostDecimal", anaOptional.get().getMostDecimal().toString());
                            dimensionIdMap.put("mostSignificance", anaOptional.get().getMostSignificance().toString());
                            dimensionIdMap.put("dataDimensionId", anaOptional.get().getDimensionId());
                        }
                        existMap.put(entry.getKey(), dimensionIdMap);
                    }
                }
            }
        }
        for (String testId : testIdList) {
            if (existMap.containsKey(testId)) {
                resMap.put(testId, existMap.get(testId));
            } else {
                Map<String, String> dimensionIdMap = new HashMap<>();
                dimensionIdMap.put("qcVolumeDimensionId", "");
                dimensionIdMap.put("qcValueDimensionId", "");
                dimensionIdMap.put("qcTestValueDimensionId", "");
                dimensionIdMap.put("realSampleTestValueDimensionId", "");
                dimensionIdMap.put("ssConcentrationDimensionId", "");
                dimensionIdMap.put("constantVolumeDimensionId", "");
                dimensionIdMap.put("mostDecimal", "");
                dimensionIdMap.put("mostSignificance", "");
                dimensionIdMap.put("dataDimensionId", "");
                resMap.put(testId, dimensionIdMap);
            }
        }
        return resMap;
    }

    /**
     * 生成样品编号时保存比对数据
     *
     * @param sampleList 样品列表
     */
    @Transactional
    @Override
    public void doSaveCompareJudgeData(List<DtoSample> sampleList, DtoProject project) {
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        sampleJudgeDataRepository.deleteBySampleIdIn(sampleIds);
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        List<String> analyzeItemIds = analyseDataList.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
        List<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll(sampleTypeIds);
        sampleList.forEach(s -> {
            Optional<DtoSampleType> sampleType = sampleTypes.stream().filter(st -> st.getId().equals(s.getSampleTypeId())).findFirst();
            sampleType.ifPresent(st -> s.setSampleTypeName(st.getTypeName()));
        });
        Map<String, DtoSample> sampleId2Sample = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
        List<DtoCompareJudge> compareJudges = compareJudgeRepository.findByAnalyzeItemIdIn(analyzeItemIds);
        List<String> compareIds = compareJudges.stream().map(DtoCompareJudge::getId).collect(Collectors.toList());
        List<DtoQualityControlLimit> compareJudgeConfigs = qualityControlLimitRepository.findByTestIdIn(compareIds);
        Map<String, List<DtoSample>> folderId2Sample = sampleList.stream().collect(Collectors.groupingBy(DtoSample::getSampleFolderId));
        List<DtoSampleJudgeData> saveList = new ArrayList<>();
        //要新的序列
        List<DtoSerialNumberConfig> serialNumberConfigCreateList = new ArrayList<>();
        //要修改的序列
        List<DtoSerialNumberConfig> serialNumberConfigUpdateList = new ArrayList<>();
        List<DtoSample> qcSamples = new ArrayList<>();
        //获取源领样单
        List<DtoQualityControl> qualityControls = new ArrayList<>();
        folderId2Sample.forEach((folderId, samples) -> {
            if (!UUIDHelper.GUID_EMPTY.equals(folderId)) {
                List<String> sampleIds2Folder = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
                List<DtoAnalyseData> analyseDataList2Folder = analyseDataList.stream().filter(a -> sampleIds2Folder.contains(a.getSampleId())).collect(Collectors.toList());
                List<String> analyzaItemIds2Folder = analyseDataList2Folder.stream().map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList());
                DtoSampleType samType = sampleTypes.stream().filter(s -> s.getId().equals(samples.get(0).getSampleTypeId())).findFirst().get();
                List<DtoCompareJudge> compareJudges2Folder = compareJudges.stream().filter(c -> analyzaItemIds2Folder.contains(c.getAnalyzeItemId()) && c.getCheckType().equals(samType.getCheckType())).collect(Collectors.toList());
                for (DtoCompareJudge compareJudge : compareJudges2Folder) {
                    for (int i = 1; i <= compareJudge.getDefaultStandardNum(); i++) {
                        Map<String, Object> returnMap = this.getAssociateSample(1, EnumLIM.EnumQCGrade.外部质控.getValue(),
                                EnumLIM.EnumQCType.质控样.getValue(), samples.get(0), "", "", "", "", null,
                                PrincipalContextUser.getPrincipal().getUserId(), null, project, null, "", null);
                        DtoSample qcSample = (DtoSample) returnMap.get("sample");
                        qcSample.setSampleCategory(EnumSampleCategory.比对评价样.getValue());
                        qcSample.setSampleFolderId(folderId);
                        qcSample.setRedFolderName("质控样");
                        qcSample.setAssociateSampleId(UUIDHelper.GUID_EMPTY);
                        qcSample.setSampleTypeName(samples.get(0).getSampleTypeName());
                        qcSample.setStatus(EnumSampleStatus.样品在检.name());
                        DtoQualityControl qc = (DtoQualityControl) returnMap.get("qualityControl");
//                    DtoGenerateSN generateSN = (DtoGenerateSN) returnMap.get("generateSN");
                        DtoGenerateSN generateSN = createSampleCode(project,
                                null,
                                samples.get(0).getSampleTypeId(),
                                samples.get(0).getSampleFolderId(),
                                new Date(),
                                PrincipalContextUser.getPrincipal().getUserId(),
                                samples.get(0).getId(),
                                true,
                                PrincipalContextUser.getPrincipal().getUserId(), true,
                                UUIDHelper.GUID_EMPTY,
                                UUIDHelper.GUID_EMPTY,
                                EnumPRO.EnumSampleCategory.质控样.getValue(), EnumLIM.EnumQCType.质控样.getValue(), 1, true, "1");
                        qcSample.setCode(generateSN.getCode());
                        String testId = analyseDataList2Folder.stream().filter(a -> compareJudge.getAnalyzeItemId().equals(a.getAnalyseItemId())).findFirst().get().getTestId();
                        DtoQualityControlLimit compareJudgeConfig = compareJudgeConfigs.stream().filter(c -> compareJudge.getId().equals(c.getTestId()) && EnumBase.EnumJudgingType.质控样比对.getValue().equals(c.getQcType())).findFirst().orElse(null);
                        if (compareJudgeConfig != null) {
                            DtoAnalyseData analyseData = analyseDataList2Folder.stream().filter(a -> a.getTestId().equals(testId)).findFirst().get();
                            DtoSampleJudgeData sampleJudgeData = new DtoSampleJudgeData();
                            sampleJudgeData.setSampleId(qcSample.getId());
                            sampleJudgeData.setTestId(testId);
                            sampleJudgeData.setCheckType(compareJudge.getCheckType());
                            sampleJudgeData.setAllowLimit(compareJudgeConfig.getAllowLimit());
                            sampleJudgeData.setCheckItemValue(compareJudgeConfig.getRangeConfig());
                            sampleJudgeData.setCompareType(compareJudgeConfig.getQcType());
                            sampleJudgeData.setJudgingMethod(compareJudgeConfig.getJudgingMethod());
                            sampleJudgeData.setDimensionId(analyseData.getDimensionId());
                            saveList.add(sampleJudgeData);
                            qcSamples.add(qcSample);
                            qualityControls.add(qc);
                            if (StringUtil.isNotNull(generateSN)) {
                                DtoSerialNumberConfig serialNumberConfigCreate = generateSN.getSerialNumberConfigCreate();
                                if (StringUtil.isNotNull(serialNumberConfigCreate)) {
                                    serialNumberConfigCreateList.add(serialNumberConfigCreate);
                                }
                                DtoSerialNumberConfig serialNumberConfigUpdate = generateSN.getSerialNumberConfigUpdate();
                                if (StringUtil.isNotNull(serialNumberConfigUpdate)) {
                                    serialNumberConfigUpdateList.add(serialNumberConfigUpdate);
                                }
                            }
                        }
                    }
                }
                analyseDataList2Folder.forEach(a -> {
                    DtoSample sample = sampleId2Sample.get(a.getSampleId());
                    DtoSampleType sampleType = sampleTypes.stream().filter(s -> s.getId().equals(sample.getSampleTypeId())).findFirst().get();
                    Optional<DtoCompareJudge> compareJudge = compareJudges.stream().filter(c -> c.getCheckType().equals(sampleType.getCheckType())
                            && c.getAnalyzeItemId().equals(a.getAnalyseItemId())).findFirst();
                    compareJudge.ifPresent(c -> {
                        DtoQualityControlLimit compareJudgeConfig = null;
                        List<DtoQualityControlLimit> compareJudgeConfigs2AnaData;
                        if (EnumBase.EnumCheckType.废水比对.getValue().equals(compareJudge.get().getCheckType())) {
                            compareJudgeConfigs2AnaData = compareJudgeConfigs.stream()
                                    .filter(config -> c.getId().equals(config.getTestId()) && EnumBase.EnumJudgingType.实际水样比对.getValue().equals(config.getQcType())).collect(Collectors.toList());
                        } else {
                            compareJudgeConfigs2AnaData = compareJudgeConfigs.stream()
                                    .filter(config -> c.getId().equals(config.getTestId()) && EnumBase.EnumJudgingType.烟气比对.getValue().equals(config.getQcType())).collect(Collectors.toList());
                        }
                        if (StringUtil.isNotEmpty(compareJudgeConfigs2AnaData)) {
                            if (compareJudgeConfigs2AnaData.get(0).getIsCheckItem().equals(1)) {
                                compareJudgeConfig = new DtoQualityControlLimit();
                                compareJudgeConfig.setQcType(compareJudgeConfigs2AnaData.get(0).getQcType());
                                compareJudgeConfig.setAllowLimit("");
                                compareJudgeConfig.setJudgingMethod(null);
                                compareJudgeConfig.setRangeConfig("");
                            } else {
                                compareJudgeConfig = compareJudgeConfigs2AnaData.get(0);
                            }
                        }
                        if (compareJudgeConfig != null) {
                            DtoSampleJudgeData sampleJudgeData = new DtoSampleJudgeData();
                            sampleJudgeData.setTestId(a.getTestId());
                            sampleJudgeData.setSampleId(a.getSampleId());
                            sampleJudgeData.setExpectedValue("/");
                            sampleJudgeData.setCheckType(c.getCheckType());
                            sampleJudgeData.setAllowLimit(compareJudgeConfig.getAllowLimit());
                            sampleJudgeData.setCheckItemValue(compareJudgeConfig.getRangeConfig());
                            sampleJudgeData.setCompareType(compareJudgeConfig.getQcType());
                            sampleJudgeData.setJudgingMethod(compareJudgeConfig.getJudgingMethod());
                            sampleJudgeData.setDimensionId(a.getDimensionId());
                            sampleJudgeData.setStandardCode("/");
                            saveList.add(sampleJudgeData);
                        }
                    });
                });
            }
        });
        if (qualityControls.size() > 0) {
            comRepository.insertBatch(qualityControls);
        }
        if (StringUtil.isNotEmpty(saveList)) {
            sampleJudgeDataRepository.save(saveList);
        }
        if (serialNumberConfigCreateList.size() > 0) {
            comRepository.insert(serialNumberConfigCreateList);
        }
        if (serialNumberConfigUpdateList.size() > 0) {
            comRepository.updateBatch(serialNumberConfigUpdateList);
        }
        if (qcSamples.size() > 0) {
            comRepository.insertBatch(qcSamples);
        }
    }

    /**
     * 获取所有关联样品,加入原有样品列表中
     *
     * @param allSampleList 样品列表
     */
    @Override
    public void addAssSample(List<DtoSample> allSampleList) {
        List<String> assIdList = allSampleList.stream().filter(p -> StringUtil.isNotEmpty(p.getAssociateSampleId())
                && !UUIDHelper.GUID_EMPTY.equals(p.getAssociateSampleId()))
                .map(DtoSample::getAssociateSampleId).distinct().collect(Collectors.toList());
        List<String> allIdList = allSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        assIdList.removeAll(allIdList);
        if (StringUtil.isNotEmpty(assIdList)) {
            List<DtoSample> newAssSample = repository.findAll(assIdList);
            if (StringUtil.isNotEmpty(newAssSample)) {
                allSampleList.addAll(newAssSample);
                addAssSample(allSampleList);
            }
        }
    }

    /**
     * 通过关联样id获取样品
     *
     * @param allSampleList 样品列表
     */
    @Override
    public void addSampleByAssId(List<DtoSample> allSampleList, List<DtoSample> assSampleList) {
        List<String> allSampleIdList = allSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<String> assSampleIdList = assSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        assSampleList = StringUtil.isNotEmpty(assSampleIdList) ? repository.findByAssociateSampleIdIn(assSampleIdList) : new ArrayList<>();
        assSampleList = assSampleList.stream().filter(p -> !allSampleIdList.contains(p.getId())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(assSampleList)) {
            allSampleList.addAll(assSampleList);
            addSampleByAssId(allSampleList, assSampleList);
        }
    }

    @Override
    public List<DtoSample> findDateRegisteredSampleList(String date) {
        StringBuilder detailBuilder = new StringBuilder("select x");
        detailBuilder.append(" from DtoSample as x")
                .append(" where isDeleted = 0 and sampleCategory = 0 ")
                .append(" and DATE_FORMAT( inceptTime, '%Y-%m-%d' ) = :inceptTime and status != :status ");
        Map<String, Object> values = new HashMap<>();
        values.put("inceptTime", date);
        values.put("status", EnumPRO.EnumSampleStatus.样品未采样.name());
        List<DtoSample> list = commonRepository.find(detailBuilder.toString(), values);
        //查询关联数据
        Set<String> projectIds = list.stream().map(DtoSample::getProjectId).collect(Collectors.toSet());
        if (projectIds.size() > 0) {
            List<DtoProject> dtoProjectList = projectRepository.findAll(projectIds);
            List<DtoSampleType> dtoSampleTypeList = sampleTypeRepository.findAll();
            List<DtoPerson> dtoPersonList = personRepository.findAll();
            for (DtoSample dtoSample : list) {
                //项目名称 项目编号
                DtoProject dtoProject = dtoProjectList.stream().filter(p -> p.getId().equals(dtoSample.getProjectId())).findFirst().orElse(null);
                if (dtoProject != null) {
                    dtoSample.setProjectName(dtoProject.getProjectName());
                    dtoSample.setProjectCode(dtoProject.getProjectCode());
                }
                //检测类型
                DtoSampleType dtoSampleType = dtoSampleTypeList.stream().filter(s -> s.getId().equals(dtoSample.getSampleTypeId())).findFirst().orElse(null);
                if (dtoSampleType != null) {
                    dtoSample.setSampleTypeName(dtoSampleType.getTypeName());
                }
                //登记人-> 采样人
                DtoPerson dtoPerson = dtoPersonList.stream().filter(p -> p.getId().equals(dtoSample.getSamplingPersonId())).findFirst().orElse(null);
                if (dtoPerson != null) {
                    dtoSample.setSenderName(dtoPerson.getCName());
                }
            }
            //排序 项目编号 检测类型 样品名称（点位名称）
            list.sort(Comparator.comparing(DtoSample::getProjectCode).thenComparing(DtoSample::getSampleTypeName).thenComparing(DtoSample::getRedFolderName));
        }
        return list;
    }

    @Override
    public List<DtoSample> findByReceiveId(String receiveId) {
        return repository.findByReceiveId(receiveId);
    }

    /**
     * 修改样品编号时，校验当前样品能否修改编号
     *
     * @param sample 样品数据
     */
    private void verifySample(DtoSample sample) {
        if (StringUtils.isNotNullAndEmpty(sample.getReceiveId()) && !UUIDHelper.GUID_EMPTY.equals(sample.getReceiveId())) {
            List<DtoStatusForRecord> statusList = statusForRecordRepository.findByReceiveId(sample.getReceiveId());
            List<String> modules = Arrays.asList(EnumLIM.EnumReceiveRecordModule.现场数据审核.getValue(), EnumLIM.EnumReceiveRecordModule.现场数据复核.getValue());
            if (statusList.stream().anyMatch(s -> modules.contains(s.getModule()))) {
                throw new BaseException("当前样品已检测，无法修改样品编号！");
            }
        }
        List<DtoAnalyseData> analyseData = analyseDataRepository.findBySampleIdAndIsDeletedFalse(sample.getId());
        if (analyseData.stream().anyMatch(a -> StringUtils.isNotNullAndEmpty(a.getWorkSheetFolderId()) && !UUIDHelper.GUID_EMPTY.equals(a.getWorkSheetFolderId()))) {
            throw new BaseException("当前样品已检测，无法修改样品编号！");
        }
    }

    /**
     * 修改点位数据经纬度
     *
     * @param sample
     */
    private void updateSampleFolder(DtoSample sample) {
        // 获取点位数据并修改经纬度
        if (StringUtil.isNotEmpty(sample.getSampleFolderId())) {
            DtoSampleFolder sampleFolder = sampleFolderRepository.findOne(sample.getSampleFolderId());
            if (StringUtil.isNotNull(sampleFolder)) {
                sampleFolder.setLon(sample.getLon());
                sampleFolder.setLat(sample.getLat());
                sampleFolderRepository.save(sampleFolder);
            }
        }
    }

    @Override
    @Transactional
    public void syncOcrParamsData(DtoOcrDataSyncParams ocrDataSyncParams) {
        //样品参数
        if (EnumLIM.EnumOcrConfigParamType.样品参数.getValue().equals(ocrDataSyncParams.getType()) && StringUtil.isNotEmpty(ocrDataSyncParams.getSampleList())) {
            List<Map<String, Object>> sampleList = ocrDataSyncParams.getSampleList();
            //获取所有ocr识别数据
            List<String> sampleCodes = sampleList.stream().map(s -> (String) s.get("code")).collect(Collectors.toList());
            Map<String, DtoOcrConfigRecord> ocrDataMap;
            if (StringUtil.isNotEmpty(ocrDataSyncParams.getOcrConfigRecordIds())) {
                // 根据选中历史记录查询
                ocrDataMap = ocrConfigRecordService.getDataMapByRecordIds(ocrDataSyncParams.getOcrConfigRecordIds(), sampleCodes);
            } else {
                ocrDataMap = ocrConfigRecordService.getLatestDataMapByCode(sampleCodes);
            }

            //获取所有样品参数数据
            List<String> sampleIds = sampleList.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
            List<DtoParamsData> allParamsDataList = paramsDataRepository.findByObjectIdInAndObjectType(sampleIds, EnumParamsDataType.样品.getValue());
            Map<String, String> allParamsDataMap = allParamsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(),
                    p.getGroupId()), pd -> StringUtils.isNotNullAndEmpty(pd.getParamsValue()) ? pd.getParamsValue() : "", (p1, p2) -> p1));
            //补全未为预先插入的参数,details接口缺失点位参数的预插入，此处补全
            List<DtoParamsData> waitInitParamsDataList = new ArrayList<>();
            List<DtoParamsConfig> paramsConfigs = ocrDataSyncParams.getParamsConfig();
            paramsConfigs = paramsConfigs.stream().filter(p -> p.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())).collect(Collectors.toList());
            for (DtoParamsConfig paramsConfig : paramsConfigs) {
                for (Map<String, Object> sampleMap : sampleList) {
                    String sampleId = (String) sampleMap.get("id");
                    String key = String.format("%s;%s;%s", sampleId, paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                    if (!allParamsDataMap.containsKey(key)) {
                        String paramsValue = StringUtils.isNotNullAndEmpty(paramsConfig.getParamsValue()) ? paramsConfig.getParamsValue() : "";
                        DtoParamsData paramsData = new DtoParamsData();
                        paramsData.setObjectId(sampleId);
                        paramsData.setObjectType(EnumParamsDataType.样品.getValue());
                        paramsData.setParamsConfigId(paramsConfig.getId());
                        paramsData.setParamsName(paramsConfig.getAlias());
                        paramsData.setParamsValue(paramsValue);
                        paramsData.setDimension(paramsConfig.getDimension());
                        paramsData.setDimensionId(paramsConfig.getDimensionId());
                        paramsData.setOrderNum(paramsConfig.getOrderNum());
                        paramsData.setGroupId(UUIDHelper.GUID_EMPTY);
                        waitInitParamsDataList.add(paramsData);
                        allParamsDataList.add(paramsData);
                    }
                }
            }
            paramsDataRepository.save(waitInitParamsDataList);
            //遍历同步数据
            List<DtoParamsData> allSyncParamList = new ArrayList<>();
            for (Map<String, Object> sampleMap : sampleList) {
                //样品ocr数据
                DtoOcrConfigRecord ocrConfigRecord = ocrDataMap.get(sampleMap.get("code"));
                if (ocrConfigRecord != null && StringUtil.isNotEmpty(ocrConfigRecord.getParamDataList())) {
                    List<DtoOcrConfigParamData> ocrDataList = ocrConfigRecord.getParamDataList();
                    ocrDataList = ocrDataList.stream().filter(d -> EnumLIM.EnumOcrConfigParamType.样品参数.getValue().equals(d.getParamType())).collect(Collectors.toList());
                    //样品参数数据
                    String sampleId = (String) sampleMap.get("id");
                    List<DtoParamsData> sampleParamsDataList = allParamsDataList.stream().filter(d -> sampleId.equals(d.getObjectId())).collect(Collectors.toList());
                    //判断是否选择了分组
                    boolean isGroup = StringUtil.isNotEmpty(ocrConfigRecord.getGroupId()) && !UUIDHelper.GUID_EMPTY.equals(ocrConfigRecord.getGroupId());
                    // 选择了分组,还需同步这个分组下参数
                    if (isGroup) {
                        List<String> groupIds = Arrays.asList(ocrConfigRecord.getGroupId().split(";"));
                        sampleParamsDataList = sampleParamsDataList.stream().filter(p -> StringUtil.isEmpty(p.getGroupId())
                                || UUIDHelper.GUID_EMPTY.equals(p.getGroupId())
                                || groupIds.contains(p.getGroupId())).collect(Collectors.toList());
                    }//不同步分组参数
                    else {
                        sampleParamsDataList = sampleParamsDataList.stream().filter(p -> StringUtil.isEmpty(p.getGroupId())
                                || UUIDHelper.GUID_EMPTY.equals(p.getGroupId())).collect(Collectors.toList());
                    }

                    //别名匹配参数名称同步数据
                    for (DtoOcrConfigParamData ocrConfigParamData : ocrDataList) {
                        List<DtoParamsData> syncParamList;
                        if (isGroup) {
                            // 多个分组名称处理
                            String[] groupNames = ocrConfigRecord.getGroupName().split(";");
                            List<String> groupParamsNameList = new ArrayList<>();
                            for (String groupName : groupNames) {
                                groupParamsNameList.add(String.format("%s-%s", groupName, ocrConfigParamData.getParamNameAlias()));
                            }
                            syncParamList = sampleParamsDataList.stream().filter(p -> ocrConfigParamData.getParamNameAlias().equals(p.getParamsName()) ||
                                    groupParamsNameList.contains(p.getParamsName())).collect(Collectors.toList());
                        } else {
                            syncParamList = sampleParamsDataList.stream().filter(p -> ocrConfigParamData.getParamNameAlias().equals(p.getParamsName()))
                                    .collect(Collectors.toList());
                        }
                        syncParamList.forEach(v -> v.setParamsValue(ocrConfigParamData.getSaveValue()));
                        allSyncParamList.addAll(syncParamList);
                    }

                }
            }
            paramsDataRepository.save(allSyncParamList);
        }//现场数据
        else if (EnumLIM.EnumOcrConfigParamType.现场数据.getValue().equals(ocrDataSyncParams.getType()) && StringUtil.isNotEmpty(ocrDataSyncParams.getAnalyseDataList())) {
            List<Map<String, Object>> analyseDataList = ocrDataSyncParams.getAnalyseDataList();
            //获取所有样品参数数据
            List<String> sampleCodes = analyseDataList.stream().map(s -> (String) s.get("sampleCode")).collect(Collectors.toList());
            Map<String, DtoOcrConfigRecord> ocrDataMap = ocrConfigRecordService.getLatestDataMapByCode(sampleCodes);
            //获取所有分析数据
            List<String> anaIds = analyseDataList.stream().map(s -> (String) s.get("id")).collect(Collectors.toList());
            List<DtoAnalyseData> allAnalyseData = analyseDataRepository.findAll(anaIds);
            //遍历同步数据
            List<DtoAnalyseData> allSyncAnaList = new ArrayList<>();
            for (Map<String, Object> anaMap : analyseDataList) {
                //样品ocr数据
                DtoOcrConfigRecord ocrConfigRecord = ocrDataMap.get(anaMap.get("sampleCode"));
                if (ocrConfigRecord != null && StringUtil.isNotEmpty(ocrConfigRecord.getParamDataList())) {
                    List<DtoOcrConfigParamData> ocrDataList = ocrConfigRecord.getParamDataList();
                    ocrDataList = ocrDataList.stream().filter(d -> EnumLIM.EnumOcrConfigParamType.现场数据.getValue().equals(d.getParamType())).collect(Collectors.toList());
                    //样品分析数据
                    DtoAnalyseData analyseData = allAnalyseData.stream().filter(a -> a.getId().equals(anaMap.get("id"))).findFirst().orElse(null);
                    if (analyseData != null) {
                        //根据ocr参数分析项目 获取到检测结果和出证结果中
                        for (DtoOcrConfigParamData ocrConfigParamData : ocrDataList) {
                            if (StringUtil.isNotEmpty(ocrConfigParamData.getAnalyzeItemId())) {
                                List<String> analyzeItemIds = Arrays.asList(ocrConfigParamData.getAnalyzeItemId().split(","));
                                if (analyzeItemIds.contains(anaMap.get("analyzeItemId"))) {
                                    if ("检测结果".equals(ocrConfigParamData.getParamNameAlias())) {
                                        analyseData.setTestOrignValue(ocrConfigParamData.getSaveValue());
                                        allSyncAnaList.add(analyseData);
                                    }
                                    if ("出证结果".equals(ocrConfigParamData.getParamNameAlias())) {
                                        analyseData.setTestValue(ocrConfigParamData.getSaveValue());
                                        allSyncAnaList.add(analyseData);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            analyseDataRepository.save(allSyncAnaList);
        }
    }

    @Override
    @Transactional
    public Map<String, Object> batchCalculateFormula(List<DtoSampleDataPhone> sampleDataPhoneList) {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> errorMsgList = new ArrayList<>();
        //根据sampleId 反查现场领样单，获取现场数据
        String sampleId = sampleDataPhoneList.get(0).getSampleId();
        long t1 = System.currentTimeMillis();
        List<DtoReceiveSubSampleRecord2Sample> receiveSubSampleRecord2SampleList = receiveSubSampleRecord2SampleRepository.findBySampleId(sampleId);
        if (!receiveSubSampleRecord2SampleList.isEmpty()) {
            //查询现场领样单
            List<String> subIds = receiveSubSampleRecord2SampleList.stream().map(DtoReceiveSubSampleRecord2Sample::getReceiveSubSampleRecordId).collect(Collectors.toList());
            List<DtoReceiveSubSampleRecord> subSampleRecordList = receiveSubSampleRecordRepository.findAll(subIds);
            String xcSubId = subSampleRecordList.stream().filter(r -> StringUtil.isNotEmpty(r.getCode()) && r.getCode().contains("XC"))
                    .map(DtoReceiveSubSampleRecord::getId).findFirst().orElse("");
            if (StringUtil.isNotEmpty(xcSubId)) {
                //数据保存容器
                List<DtoAnalyseDataProperty> waitSaveAnalyseDataPropertyList = new ArrayList<>();
                //获取现场数据
                List<DtoLocalDataProperty> localDataPropertyList = receiveSubSampleRecordService.findLocalData(xcSubId, "localDataInput", 1, "", "");
                long t2 = System.currentTimeMillis();
                log.info("计算前数据查询耗时：" + (t2 - t1));
                //测试项目遍历计算
                for (DtoSampleDataPhone sampleDataPhone : sampleDataPhoneList) {
                    long t21 = System.currentTimeMillis();
                    DtoLocalDataProperty localDataProperty = localDataPropertyList.stream()
                            .filter(p -> p.getTest() != null && sampleDataPhone.getTestId().equals(p.getTest().get("testId"))).findFirst().orElse(null);
                    if (localDataProperty != null && !localDataProperty.getAnalyseData().isEmpty()) {
                        // 所有样品数据
                        List<Map<String, Object>> analyseDatas = localDataProperty.getAnalyseData();
                        Map<String, Object> analyseData = analyseDatas.stream().filter(a -> sampleDataPhone.getAnaId().equals(a.get("id"))).findFirst().orElse(null);
                        if (analyseData != null) {
                            //公式计算传参实体
                            DtoAnalyseDataCalculation dtoAnalyseDataCalculation = new DtoAnalyseDataCalculation();
                            //测试项目公式参数
                            dtoAnalyseDataCalculation.setParamsConfig(localDataProperty.getParamsConfig());
                            //原样(更新前端填入参数）
                            analyseData.putAll(sampleDataPhone.getParamMap());
                            dtoAnalyseDataCalculation.setAnalyseData(analyseData);
                            //质控样（pc端前端逻辑为 item.groupSampleId === sample.sampleId || item.sampleId === sample.groupSampleId || item.sampleId === sample.sampleId
                            List<Map<String, Object>> correlation = analyseDatas.stream().filter(a -> analyseData.get("sampleId").equals(a.get("groupSampleId"))
                                    || analyseData.get("groupSampleId").equals(a.get("sampleId"))
                                    || analyseData.get("sampleId").equals(a.get("sampleId")))
                                    .collect(Collectors.toList());
                            dtoAnalyseDataCalculation.setCorrelation(correlation);
                            //计算并返回结果
                            DtoTestQCRangeResult rangeResult = new DtoTestQCRangeResult();
                            List<Map<String, Object>> mapList = analyseDataService.analyticalFormula(dtoAnalyseDataCalculation, rangeResult);
                            if (StringUtil.isNotNull(rangeResult.getIsPass()) && !rangeResult.getIsPass()) {
                                errorMsgList.add(rangeResult.getRangeConfig());
                            }
                            //结果保存容器
                            DtoAnalyseDataProperty analyseDataProperty = new DtoAnalyseDataProperty();
                            analyseDataProperty.setAnalyseData(mapList);
                            analyseDataProperty.setParamsConfig(localDataProperty.getParamsConfig());
                            waitSaveAnalyseDataPropertyList.add(analyseDataProperty);
                        }
                    }
                    long t22 = System.currentTimeMillis();
                    log.info("单测试项目计算耗时：" + (t22 - t21));
                }
                long t3 = System.currentTimeMillis();
                log.info("全部测试项目计算耗时：" + (t3 - t2));
                //保存新数据
                resultMap = analyseDataService.saveLocalData(xcSubId, "", waitSaveAnalyseDataPropertyList);
                long t4 = System.currentTimeMillis();
                log.info("数据保存耗时：" + (t4 - t3));
            }
        }
        String fullErrorMsg = String.join("，", errorMsgList);
        if (StringUtil.isNotEmpty(fullErrorMsg)) {
            throw new BaseException(fullErrorMsg);
        }
        return resultMap;
    }

    @Override
    @Transactional
    public void syncFixedPoint(String receiveId) {
        List<DtoSample> sampleList = repository.findByReceiveId(receiveId);
        Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
        List<String> sampleFolderIds = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findAll(sampleFolderIds);
        List<String> fixedPointIds = sampleFolders.stream().map(DtoSampleFolder::getFixedPointId).distinct().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(fixedPointIds)) {
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoParamsData> paramsDataList = paramsDataRepository.findByObjectIdIn(sampleIds);
            Map<String, DtoSampleFolder> folderMap = sampleFolders.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
            List<DtoFixedpoint> fixedpointList = StringUtil.isNotEmpty(fixedPointIds) ? fixedpointRepository.findAll(fixedPointIds) : new ArrayList<>();
            List<DtoPointExtendData> pointExtendDataList = StringUtil.isNotEmpty(fixedPointIds) ? pointExtendDataRepository.findByFixedPointIdIn(fixedPointIds) : new ArrayList<>();
            Map<String, List<DtoPointExtendData>> pointExtendDataMap = pointExtendDataList.stream().collect(Collectors.groupingBy(DtoPointExtendData::getFixedPointId));
            for (DtoParamsData paramsData : paramsDataList) {
                // 根据例行点位获取点位拓展数据值（包含点位经纬度）
                DtoSample sample = sampleMap.get(paramsData.getObjectId());
                String newVal = getParamsValByFixedPoint(paramsData.getParamsName(), paramsData.getParamsValue(), folderMap.get(sample.getSampleFolderId()), pointExtendDataMap, fixedpointList);
                paramsData.setParamsValue(newVal);
            }
            paramsDataRepository.save(paramsDataList);
        }

    }

    @Override
    @Transactional
    public void syncFolderLocation(String receiveId) {
        List<DtoSample> sampleList = repository.findByReceiveId(receiveId);
        if (StringUtil.isNotEmpty(sampleList)) {
            List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoParamsData> allParamsDataList = paramsDataRepository.findByObjectIdIn(sampleIds);
            List<String> sampleFolderIds = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
            List<DtoFolderSign> allFolderSignList = folderSignRepository.findBySampleFolderIdIn(sampleFolderIds);
            List<DtoParamsData> saveList = new ArrayList<>();
            for (DtoSample sample : sampleList) {
                String sampleFolderId = sample.getSampleFolderId();
                //质控样跟随原样点位
                if (StringUtil.isEmpty(sampleFolderId) || UUIDHelper.GUID_EMPTY.equals(sampleFolderId)) {
                    sampleFolderId = sampleList.stream().filter(v -> v.getId().equals(sample.getAssociateSampleId())).map(DtoSample::getSampleFolderId)
                            .findFirst().orElse("");
                }
                if (StringUtil.isNotEmpty(sampleFolderId) && !UUIDHelper.GUID_EMPTY.equals(sampleFolderId)) {
                    String finalSampleFolderId = sampleFolderId;
                    DtoFolderSign folderSign = allFolderSignList.stream().filter(v -> finalSampleFolderId.equals(v.getSampleFolderId()))
                            .sorted(Comparator.comparing(DtoFolderSign::getSignTime)).findFirst().orElse(null);
                    if (folderSign != null) {
                        List<DtoParamsData> paramsDataList = allParamsDataList.stream().filter(v -> sample.getId().equals(v.getObjectId())).collect(Collectors.toList());
                        for (DtoParamsData paramsData : paramsDataList) {
                            String paramsName = paramsData.getParamsName();
                            String value = "";
                            if (paramsName.equals("经度") || paramsName.equals("纬度") || paramsName.equals("经纬度")) {
                                value = paramsName.equals("经纬度") ? folderSign.getSignLon() + "," + folderSign.getSignLat() :
                                        paramsName.equals("经度") ? folderSign.getSignLon() : folderSign.getSignLat();
                                if (StringUtil.isNotEmpty(value)) {
                                    paramsData.setParamsValue(value);
                                    saveList.add(paramsData);
                                }
                            }
                        }
                    }
                }
            }
            paramsDataRepository.save(saveList);
        }
    }

    @Override
    @Transactional
    public void batchAddTest(DtoSamplingFrequencyTest samplingFrequencyTest) {
        List<DtoSample> sampleList = repository.findAll(samplingFrequencyTest.getSampleIds());
        List<DtoSample> yySampleList = sampleList.stream().filter(p -> p.getSampleCategory().equals(EnumSampleCategory.原样.getValue())).collect(Collectors.toList());
        List<DtoSample> qcSampleList = sampleList.stream().filter(p -> !p.getSampleCategory().equals(EnumSampleCategory.原样.getValue())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(yySampleList)) {
            List<String> samplingFrequencyIds = yySampleList.stream().map(DtoSample::getSamplingFrequencyId).collect(Collectors.toList());
            sampleFolderService.addFrequencyAnalyseItems(samplingFrequencyIds, new ArrayList<>(), samplingFrequencyTest.getTestIds());
        }
        if (StringUtil.isNotEmpty(qcSampleList)) {
            qcSampleList.forEach(p -> proService.addSampleTest(p.getId(), samplingFrequencyTest.getTestIds(), false));
        }
    }

    @Autowired
    public void setTestPostRepository(TestPostRepository testPostRepository) {
        this.testPostRepository = testPostRepository;
    }

    @Autowired
    public void setTestPost2PersonRepository(TestPost2PersonRepository testPost2PersonRepository) {
        this.testPost2PersonRepository = testPost2PersonRepository;
    }

    @Autowired
    @Lazy
    public void setSampleJudgeDataService(SampleJudgeDataService sampleJudgeDataService) {
        this.sampleJudgeDataService = sampleJudgeDataService;
    }

    @Autowired
    @Lazy
    public void setSampleGroupService(SampleGroupService sampleGroupService) {
        this.sampleGroupService = sampleGroupService;
    }

    @Autowired
    @Lazy
    public void setBusinessSerialNumberService(BusinessSerialNumberService businessSerialNumberService) {
        this.businessSerialNumberService = businessSerialNumberService;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataFutureService(AnalyseDataFutureService analyseDataFutureService) {
        this.analyseDataFutureService = analyseDataFutureService;
    }
}