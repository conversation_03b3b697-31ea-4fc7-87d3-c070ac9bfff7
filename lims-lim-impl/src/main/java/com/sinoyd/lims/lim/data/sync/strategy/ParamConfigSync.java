package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsConfigRepository;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 参数配置同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/27
 */
@Component
@DependsOn({"springContextAware"})
@Order(7)
@Slf4j
public class ParamConfigSync extends AbsDataSync<DtoParamsConfig> {

    private ParamsConfigService paramsConfigService;

    private ParamsConfigRepository paramsConfigRepository;

    /**
     * 数据比较
     *
     * @param testIds 需要同步的测试项目id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoParamsConfig>> compareData(List<String> testIds) {
        //获取项目上全部分析方法
        List<DtoParamsConfig> projectDataList = paramsConfigService.findAll();
        //公共库中的分析项目
        List<DtoParamsConfig> standardParamsConfigList = queryStandardData();
        //过滤出选择的测试项目相关的测试项目参数
        if(StringUtil.isNotEmpty(standardParamsConfigList) && StringUtil.isNotEmpty(testIds)){
            standardParamsConfigList = standardParamsConfigList.parallelStream()
                    .filter(p -> EnumLIM.EnumParamsConfigType.分析项目参数.getValue().equals(p.getType()) && testIds.contains(p.getObjId()))
                    .collect(Collectors.toList());
        }
        //比较数据
        return compareData(standardParamsConfigList, projectDataList);
    }

    /**
     * 同步数据
     *
     * @param testIds         需要同步的测试项目id集合
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> testIds, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoParamsConfig>> compareResult = compareData(testIds);
        Optional<DtoDataCompareResult<DtoParamsConfig>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoParamsConfig errorDto = null;
            try {
                for (DtoParamsConfig dtoParamsConfig : r.getAddDataList()) {
                    errorDto = dtoParamsConfig;
                    //防止公共库未被假删，但项目库被假删，此种情况直接更新
                    if (paramsConfigRepository.findOne(dtoParamsConfig.getId()) != null) {
                        paramsConfigService.update(dtoParamsConfig);
                    } else {
                        paramsConfigService.save(dtoParamsConfig);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return true: 必须同步， false：不用必须同步
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 同步的数据项名称
     *
     * @return 数据项名称
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.参数配置.name();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.参数配置.getValue();
    }

    /**
     * 获取标准库数据查询url
     *
     * @return url
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/paramsConfig";
    }

    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.参数配置.getValue();
    }

    @Autowired
    public void setParamsConfigRepository(ParamsConfigRepository paramsConfigRepository) {
        this.paramsConfigRepository = paramsConfigRepository;
    }

    @Lazy
    @Autowired
    public void setParamsConfigService(ParamsConfigService paramsConfigService) {
        this.paramsConfigService = paramsConfigService;
    }
}