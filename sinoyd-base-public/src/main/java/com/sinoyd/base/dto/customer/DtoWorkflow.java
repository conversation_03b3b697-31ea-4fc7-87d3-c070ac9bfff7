package com.sinoyd.base.dto.customer;

import lombok.Data;
import org.activiti.engine.repository.ProcessDefinition;

/**
 * DtoWorkflow 工作流流信息
 * <AUTHOR>
 * @version V1.0.0 2019/10/30
 * @since V100R001
 */
@Data
public class DtoWorkflow {


    /**
     * 工作流编号
     */
    private String workflowCode;


    /**
     * 工作流程id
     */
    private String workflowId;


    /**
     * 工作流名称
     */
    private String workflowName;

    /**
     * 工作流版本
     */
    private Integer workflowVersion;


    /**
     * 工作流描述
     */
    private String description;


    /**
     * 重新构造
     *
     * @param processDefinition 框架中流程定义对象
     */
    public DtoWorkflow(ProcessDefinition processDefinition) {
        this.workflowCode = processDefinition.getKey();
        this.workflowName = processDefinition.getName();
        this.workflowId = processDefinition.getId();
        this.description = processDefinition.getDescription();
        this.workflowVersion = processDefinition.getVersion();
    }
}
