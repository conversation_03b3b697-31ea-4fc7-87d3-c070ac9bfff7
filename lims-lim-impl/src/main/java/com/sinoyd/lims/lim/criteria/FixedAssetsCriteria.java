package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 固定资产查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FixedAssetsCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 固定资产名称
     */
    private String assetsName;

    /**
     * 管理人员（Guid）
     */
    private String manager;

    /**
     * 所属科室（Guid）
     */
    private String deptId;

    /**
     * 资产类型
     */
    private String assetsType;


    /**
     * 资产状态 (1使用中，2已报废)
     */
    private Integer status;


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(assetsName)) {
            condition.append(" and a.assetsName like :assetsName");
            values.put("assetsName", "%" + this.assetsName + "%");
        }
        if (StringUtil.isNotEmpty(manager)){
            condition.append(" and a.manager = :manager");
            values.put("manager", this.manager);
        }
        if (StringUtil.isNotEmpty(deptId)){
            condition.append(" and a.deptId = :deptId");
            values.put("deptId", this.deptId);
        }

        if (StringUtil.isNotEmpty(assetsType)){
            condition.append(" and a.assetsType = :assetsType");
            values.put("assetsType", this.assetsType);
        }

        if (StringUtil.isNotNull(status)){
            condition.append(" and a.status = :status");
            values.put("status", this.status);
        }
        return condition.toString();
    }
}
