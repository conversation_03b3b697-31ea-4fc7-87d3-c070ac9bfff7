package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtoImportConsumableExtend {

    @Excel(name = "等级",orderNum = "2",width = 22)
    private String grade;

    @Excel(name = "消耗品类型",orderNum = "1",width = 22)
    private String consumableCategory;

    @Excel(name = "标样类型",orderNum = "1",width = 22,isColumnHidden = true)
    private String consumableStandardCategory;
}
