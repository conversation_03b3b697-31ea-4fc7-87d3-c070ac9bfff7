package com.sinoyd.lims.lim.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import com.aspose.cells.PageSetup;
import com.aspose.cells.Workbook;
import com.aspose.cells.WorkbookDesigner;
import com.aspose.cells.WorksheetCollection;
import com.aspose.words.*;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.criteria.DocumentCriteria;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoSystemConfig;
import com.sinoyd.base.repository.lims.SystemConfigRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.AsposeLicenseUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApply;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.enums.EnumLIM.EnumFileControlStatus;
import com.sinoyd.lims.lim.repository.lims.FileControlApplyDetailRepository;
import com.sinoyd.lims.lim.repository.lims.FileControlApplyRepository;
import com.sinoyd.lims.lim.service.FileControlApplyDetailService;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件信息（右侧Grid）
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/2
 * @since V100R001
 */
@Service
public class FileControlApplyDetailServiceImpl
        extends BaseJpaServiceImpl<DtoFileControlApplyDetail, String, FileControlApplyDetailRepository>
        implements FileControlApplyDetailService {

    @Autowired
    private FileControlApplyRepository fileControlApplyRepository;

    @Autowired
    private CodeService codeService;

    @Autowired
    @Lazy
    private DocumentService documentService;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    private SystemConfigRepository systemConfigRepository;

    /**
     * 新增文件
     */
    @Override
    @Transactional
    public DtoFileControlApplyDetail save(DtoFileControlApplyDetail fileControlApplyDetail) {
        // 如果受控编号存在,则进行验证
        if (StringUtil.isNotEmpty(fileControlApplyDetail.getControlCode()) && repository.getCountByControlCode(fileControlApplyDetail.getControlCode(),
                fileControlApplyDetail.getId()) > 0) {
            throw new BaseException("已存在相同的受控编号！");
        }
        return super.save(fileControlApplyDetail);
    }

    /**
     * 分页查询文件
     */
    @Override
    public void findByPage(PageBean<DtoFileControlApplyDetail> pageBean, BaseCriteria criteria) {
        // [TODO:]【刘梧桐】只对FileControlApplyDetail表进行了操作，没有对FileControlApply操作
        // 是否需要级联删除
        pageBean.setEntityName("DtoFileControlApplyDetail p");
        pageBean.setSelect("select p");
        super.findByPage(pageBean, criteria);

        List<DtoFileControlApplyDetail> list = pageBean.getData();

        List<String> appIds = list.stream().filter(p -> !p.getFileApplyId().equals(UUIDHelper.GUID_EMPTY)).map(DtoFileControlApplyDetail::getFileApplyId).distinct().collect(Collectors.toList());
        List<DtoFileControlApply> appList = new ArrayList<>();
        if (appIds.size() > 0) {
            appList = fileControlApplyRepository.findByIds(appIds);
        }
        List<DtoCode> typeList = codeService.findCodes("LIM_FileType");
        for (DtoFileControlApplyDetail detail : list) {
            if (!detail.getFileApplyId().equals(UUIDHelper.GUID_EMPTY) && appList.size() > 0) {
                Optional<DtoFileControlApply> re = appList.stream().filter(p -> p.getId().equals(detail.getFileApplyId())).findFirst();
                if (re.isPresent()) {
                    DtoFileControlApply apply = re.get();
                    detail.setControlDate(apply.getControlDate());//受控日期
                    detail.setControlDate(apply.getAbolishDate());//废止日期
                }
            }

            if (!detail.getFileType().equals(UUIDHelper.GUID_EMPTY) && typeList.size() > 0) {
                Optional<DtoCode> ap = typeList.stream().filter(p -> p.getId().equals(detail.getFileType())).findFirst();
                if (ap.isPresent()) {
                    DtoCode type = ap.get();
                    detail.setFileTypeName(type.getDictName());//文件类型名称
                }
            }
        }

        pageBean.setData(list);// 重新封装到pageBean返回到上层
    }

    /**
     * 修改文件信息
     */
    @Transactional
    @Override
    public DtoFileControlApplyDetail update(DtoFileControlApplyDetail fileControlApplyDetail) {
        if (repository.getCountByControlCode(fileControlApplyDetail.getControlCode(),
                fileControlApplyDetail.getId()) > 0) {
            if (StringUtil.isNotEmpty(fileControlApplyDetail.getControlCode())) {
                throw new BaseException("已存在相同的受控编号！");
            }
        }

        return super.update(fileControlApplyDetail);
    }


    @Override
    @Transactional
    public void updateFileStatus(String fileId, EnumFileControlStatus status) {
        if (StringUtil.isNotEmpty(fileId) && !UUIDHelper.GUID_EMPTY.equals(fileId)) {
            // 更新文件状态
            repository.updateFileStatus(fileId, status.getValue());
        }
    }

    @Override
    @Transactional
    public void updateControlFileStatus(String fileId, EnumFileControlStatus status, Date controlDate) {
        if (StringUtil.isNotEmpty(fileId) && !UUIDHelper.GUID_EMPTY.equals(fileId)) {
            // 更新文件状态
            repository.updateControlFileStatus(fileId, status.getValue(), controlDate);
        }
    }


    @Override
    @Transactional
    public void updateAbolishFileStatus(String fileId, EnumFileControlStatus status, Date abolishDate) {
        if (StringUtil.isNotEmpty(fileId) && !UUIDHelper.GUID_EMPTY.equals(fileId)) {
            // 更新文件状态
            repository.updateAbolishFileStatus(fileId, status.getValue(), abolishDate);
        }
    }

    @Override
    @Transactional
    public void updateFileStatus(String fileId, String controlCode, String versionCode, EnumFileControlStatus status) {
        if (StringUtil.isNotEmpty(fileId) && !UUIDHelper.GUID_EMPTY.equals(fileId)) {
            DtoFileControlApplyDetail controlDetail = this.findOne(fileId);
            controlDetail.setStatus(status.getValue());
            controlDetail.setControlCode(controlCode);
            controlDetail.setVersion(versionCode);
            // 更新文件状态
            this.update(controlDetail);
        }
    }

    @Override
    public DtoFileControlApplyDetail getFileControlPath(String id) {
        DtoFileControlApplyDetail dtoFileControlApplyDetail = super.findOne(id);
        String subPath = "";
        if (StringUtil.isNotNull(dtoFileControlApplyDetail)) {
            DtoCode dtoCode = codeService.findByCode(dtoFileControlApplyDetail.getFileType());
            while (StringUtil.isNotNull(dtoCode)) {
                subPath = dtoCode.getDictName() + "/" + subPath;
                dtoCode = codeService.findById(dtoCode.getParentId());
            }
        }
        dtoFileControlApplyDetail.setPath(subPath);
        return dtoFileControlApplyDetail;
    }

    @Override
    @Transactional
    public void updateFileStatus(DtoFileControlApplyDetail detail) {
        if (EnumFileControlStatus.已受控.getValue().equals(detail.getStatus())) {
            repository.updateControlFileStatusByIds(detail.getFileIds(), detail.getStatus(), new Date());
        } else if (EnumFileControlStatus.已废止.getValue().equals(detail.getStatus())) {
            repository.updateAbolishFileStatusByIds(detail.getFileIds(), detail.getStatus(), new Date());
        }
    }

    @Override
    @Transactional
    public void downLoadFile(String fileId, String documentId, HttpServletResponse response) throws Exception {
        if (!AsposeLicenseUtil.isLicense()) {
            throw new RuntimeException("验证远大文件体系证书失败");
        }
        DtoDocument document = documentService.findOne(documentId);
        // 文件下载
        this.fileDownload(document, fileId, response);
    }

    /**
     * 文件设置页眉后下载
     *
     * @param document 文件实体
     * @param fileId   问价夹id
     * @param response 相应
     * @throws Exception
     */
    @Transactional
    protected void fileDownload(DtoDocument document, String fileId, HttpServletResponse response) throws Exception {
        if (StringUtil.isNotNull(document)) {
            List<DtoSystemConfig> systemConfigList = systemConfigRepository.findAll();
            DtoSystemConfig systemConfig = StringUtil.isNotEmpty(systemConfigList) ? systemConfigList.get(0) : new DtoSystemConfig();
            String outputPath = getPath(fileId, document, systemConfig);
            documentService.fileDownload(outputPath, document.getFilename(), response);
        }
    }

    @NotNull
    private String getPath(String fileId, DtoDocument document, DtoSystemConfig systemConfig) throws Exception {
        DtoFileControlApplyDetail detail = super.findOne(fileId);
        String compileTime = DateUtil.dateToString(detail.getCompileTime(), DateUtil.YEAR_ZH_CN);
        if (compileTime.contains("1753")) {
            compileTime = "";
        }
        String word = systemConfig.getCompanyName() + " " + compileTime;
        String path = filePathConfig.getFilePath() + document.getPath();
        List<String> excelTypes = Arrays.asList("xlsx", "xls"), docTypes = Arrays.asList("doc", "docx");
        String outputPath = filePathConfig.getOutputPath() + "/" + document.getFilename();
        if (excelTypes.stream().anyMatch(type -> document.getDocSuffix().contains(type))) {
            WorkbookDesigner designer = new WorkbookDesigner();
            designer.getWorkbook().getWorksheets().clear();
            getWorkbookDesigner(path, designer);
            setPageHeadFoot(word, designer);
            designer.getWorkbook().save(outputPath);
        } else if (docTypes.stream().anyMatch(type -> document.getDocSuffix().contains(type))) {
            Document doc = new Document(path);
            HeaderFooter footer = new HeaderFooter(doc, HeaderFooterType.FOOTER_PRIMARY);
            Paragraph paragraph = new Paragraph(doc);
            Run run = new Run(doc);
            run.setText(word);
            paragraph.appendChild(run);
            footer.appendChild(paragraph);
            for (Section section : doc.getSections()) {
                HeaderFooterCollection headerFooters = section.getHeadersFooters();
                if (headerFooters.getCount() > 0) {
                    for (HeaderFooter foot : headerFooters) {
                        if (HeaderFooterType.FOOTER_PRIMARY == foot.getHeaderFooterType()) {
                            for (Paragraph footParagraph : foot.getParagraphs()) {
                                footParagraph.appendChild(new Run(doc, word));
                            }
                        }
                    }
                } else {
                    headerFooters.add(footer);
                }
            }
            doc.save(outputPath);
        } else {
            return path;
        }
        return outputPath;
    }

    @Override
    public void batchDownLoad(List<String> fileIds, HttpServletResponse response) throws Exception {
        if (!AsposeLicenseUtil.isLicense()) {
            throw new RuntimeException("验证远大文件体系证书失败");
        }
        List<DtoDocument> documents = getDocumentsByFolderIds(fileIds);
        this.batchDownLoad(fileIds, documents, response);
    }

    /**
     * 批量下载
     *
     * @param fileIds   文件夹id
     * @param documents 文件集合
     * @param response  响应头
     * @throws Exception
     */
    protected void batchDownLoad(List<String> fileIds, List<DtoDocument> documents, HttpServletResponse response) throws Exception {
        List<DtoFileControlApplyDetail> details = StringUtil.isNotEmpty(fileIds) ? repository.findAll(fileIds) : new ArrayList<>();
        List<DtoSystemConfig> systemConfigList = systemConfigRepository.findAll();
        DtoSystemConfig systemConfig = StringUtil.isNotEmpty(systemConfigList) ? systemConfigList.get(0) : new DtoSystemConfig();

        String outputPath = filePathConfig.getOutputPath() + "/" + "compressed.zip";
        FileOutputStream fos = new FileOutputStream(outputPath);
        ZipOutputStream zos = new ZipOutputStream(fos);
        for (DtoDocument document : documents) {
            FileInputStream fis = new FileInputStream(getPath(document.getFolderId(), document, systemConfig));
            String sub = details.stream().filter(d -> document.getFolderId().equals(d.getId())).map(d -> d.getFileName() + "_" + d.getControlCode()).findFirst().orElse(null);
            zos.putNextEntry(new ZipEntry(sub != null ? sub + "/" + document.getFilename() : document.getFilename()));
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                zos.write(buffer, 0, length);
            }
            zos.closeEntry();
            fis.close();
        }
        zos.close();
        documentService.fileDownload(outputPath, "compressed.zip", response);
    }

    /**
     * 根据文件夹id获取附件
     *
     * @param folderIds 文件夹id
     * @return 附件集合
     */
    protected List<DtoDocument> getDocumentsByFolderIds(List<String> folderIds) {
        PageBean<DtoDocument> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        DocumentCriteria criteria = new DocumentCriteria();
        criteria.setFolderIds(folderIds);
        documentService.findByPage(pb, criteria);
        return pb.getData();
    }

    /**
     * 获取路径
     *
     * @param templateName 模板名称
     * @param designer     excel工作单
     */
    private void getWorkbookDesigner(String templateName, WorkbookDesigner designer) {
        try {
            File file = new File(templateName);
            InputStream stream;
            if (file.exists()) {
                stream = new FileInputStream(file);
            } else {
                stream = this.getClass().getClassLoader().getResourceAsStream(templateName);
            }
            designer.setWorkbook(new Workbook(stream));
            stream.close();
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    private void setPageHeadFoot(String word, WorkbookDesigner designer) {
        WorksheetCollection worksheetCollection = designer.getWorkbook().getWorksheets();
        for (int i = 0; i < worksheetCollection.getCount(); i++) {
            PageSetup pageSetup = worksheetCollection.get(i).getPageSetup();
            if (StringUtil.isNotEmpty(word)) {
                pageSetup.setFooter(2, "&\"Times New Roman,Bold\"&10 " + word);
            }
        }
    }

    @Override
    @Transactional
    public DtoFileControlApplyDetail saveFileControlApplyDetail(DtoFileControlApplyDetail dtoFileControlApplyDetail) {
        return super.save(dtoFileControlApplyDetail);
    }

    @Override
    public Boolean isExistControlCode(String controlCode) {
        return repository.getCountByControlCode(controlCode) > 0;
    }

    @Override
    public Boolean isExistControlCode(String controlCode, String id) {
        return repository.getCountByControlCode(controlCode, id) > 0;
    }

    @Override
    @Transactional
    public Integer deleteByDictCode(List<String> ids) {
        //删除文件类型之后，同时要删除文件
        //由于文件里面存储的是常量编码，需要找常量编号再删除
        List<DtoCode> codes = codeService.findByIds(ids);
        List<String> folderCodes = codes.stream().map(DtoCode::getDictCode).distinct().collect(Collectors.toList());
        codeService.delete(ids);
        // 查询需要进行删除的文件明细
        List<DtoFileControlApplyDetail> dtoFileControlApplyDetails = repository.findByFileTypeIn(folderCodes);
        List<String> fileApplyDetailIds = dtoFileControlApplyDetails.stream().map(DtoFileControlApplyDetail::getId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(fileApplyDetailIds)) {
            return super.logicDeleteById(fileApplyDetailIds);
        } else {
            return 0;
        }
    }

    @Override
    public List<TreeNode> getAllFileTypes() {
        List<DtoCode> typeList = codeService.findCodes("LIM_FileType");
        List<TreeNode> treeNodes = new ArrayList<>();
        for (DtoCode code : typeList) {
            TreeNode treeNode = new TreeNode();
            treeNode.setId(code.getId());
            treeNode.setParentId(code.getParentId());
            treeNode.setLabel(code.getDictName());
            treeNode.setOrderNum(code.getSortNum());
            treeNode.setType(code.getDictCode());
            treeNode.setExtent1(code.getDictType());
            treeNode.setExtent2(code.getDictValue());
            treeNodes.add(treeNode);
        }
        return buildTree(treeNodes);
    }

    private List<TreeNode> buildTree(List<TreeNode> treeNodeList) {
        List<TreeNode> trees = new ArrayList<>();
        sortTreeNode(treeNodeList);
        for (TreeNode treeNode : treeNodeList) {
            if (StringUtil.isEmpty(treeNode.getParentId()) || UUIDHelper.GUID_EMPTY.equals(treeNode.getParentId()) || "0".equals(treeNode.getParentId())) {
                trees.add(findChildren(treeNode, treeNodeList));
            }
        }
        return trees;
    }

    /**
     * 递归查找子节点
     *
     * @param currentTreeNode 当前树节点
     * @param treeNodeList    树节点列表
     * @return 完整结构的树节点
     */
    private TreeNode findChildren(TreeNode currentTreeNode, List<TreeNode> treeNodeList) {
        for (TreeNode treeNode : treeNodeList) {
            if (currentTreeNode.getId().equals(treeNode.getParentId())) {
                if (currentTreeNode.getChildren() == null) {
                    currentTreeNode.setChildren(new ArrayList<>());
                }
                //是否还有子节点，如果有的话继续往下遍历，如果没有则直接返回
                currentTreeNode.getChildren().add(findChildren(treeNode, treeNodeList));
            }
        }
        return currentTreeNode;
    }

    /**
     * 排序方法
     *
     * @param treeNodeList 需要排序的节点list
     */
    private void sortTreeNode(List<TreeNode> treeNodeList) {
        treeNodeList.sort(Comparator.comparing(TreeNode::getOrderNum, Comparator.reverseOrder()));
    }
}