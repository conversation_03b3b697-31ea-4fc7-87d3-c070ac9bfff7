package com.sinoyd.base.dto.rcc;

import com.sinoyd.base.entity.Dimension;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoDimension实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_BASE_Dimension")
 @Data
 @DynamicInsert
 public  class DtoDimension extends Dimension {

 /**
  * 量纲类型名称
  */
 @Transient
 private String dimensionTypeName;




 }