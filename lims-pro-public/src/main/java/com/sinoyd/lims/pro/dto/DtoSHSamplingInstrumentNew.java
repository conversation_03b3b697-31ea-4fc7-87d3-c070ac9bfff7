package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SHSamplingInstrumentNew;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 监管平台采样仪器配置
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/11/28
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SHSamplingInstrument")
@Data
@DynamicInsert
public class DtoSHSamplingInstrumentNew extends SHSamplingInstrumentNew {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器名称
     */
    @Transient
    private String instrumentName;

    /**
     * 监管平台仪器id
     */
    @Transient
    private String regulateId;

    /**
     * 监管平台仪器名称
     */
    @Transient
    private String regulateName;

    /**
     * 出厂编号
     */
    @Transient
    private String code;

    /**
     * 规格型号
     */
    @Transient
    private String model;

}
