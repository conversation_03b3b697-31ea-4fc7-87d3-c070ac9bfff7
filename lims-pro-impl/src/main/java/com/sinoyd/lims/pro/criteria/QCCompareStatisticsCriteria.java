package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * 质控比例的查询条件
 * <AUTHOR>
 * @version V1.0.0 2020/02/11
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QCCompareStatisticsCriteria extends BaseCriteria implements Serializable {

    /**
     * 天数
     */
    private Integer day;

    /**
     * 分析开始时间
     */
    private String startTime;

    /**
     * 分析结束时间
     */
    private String endTime;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 质控类型
     */
    private Integer qcGrade;

    /**
     * 质控等级
     */
    private Integer qcType;


    /**
     *  是否是质控
     */
    private Boolean isZk=false;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.id=b.sampleId");
        condition.append(" and a.sampleTypeId=c.id");
        condition.append(" and a.isDeleted=0");
        condition.append(" and b.isDeleted=0");
        if (StringUtils.isNotNullAndEmpty(this.testId)) {
            condition.append(" and b.testId=:testId");
            values.put("testId", testId);
        }
        if (StringUtils.isNotNullAndEmpty(this.sampleTypeId)) {
            condition.append(" and c.parentId=:sampleTypeId");
            values.put("sampleTypeId", sampleTypeId);
        }
        if (StringUtils.isNotNullAndEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and b.analyzeTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtils.isNotNullAndEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and b.analyzeTime < :endTime");
            values.put("endTime", c.getTime());
        }
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            condition.append(" and a.orgId=:orgId");
            condition.append(" and b.orgId=:orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        if (isZk) { //是否是质控与原样数据的条件区分
            condition.append(" and a.sampleCategory=:sampleCategory");
            values.put("sampleCategory", EnumPRO.EnumSampleCategory.质控样.getValue());
            condition.append(" and b.qcGrade=:qcGrade");
            values.put("qcGrade", this.qcGrade);
            condition.append(" and b.qcType=:qcType");
            values.put("qcType", this.qcType);
        } else {
            condition.append(" and a.sampleCategory=:sampleCategory");
            values.put("sampleCategory", EnumPRO.EnumSampleCategory.原样.getValue());
        }
        condition.append(" group by b.analyzeTime");
        condition.append(" order by b.analyzeTime");
        return condition.toString();
    }
}
