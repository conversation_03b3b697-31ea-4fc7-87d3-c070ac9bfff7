package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.QCProjectCriteria;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.customer.DtoQCProject;
import com.sinoyd.lims.pro.dto.customer.DtoQCProjectCopy;
import com.sinoyd.lims.pro.service.QCProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 质控项目服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/2/4
 * @since V100R001
 */
@Api(tags = "示例: 质控项目服务")
@RestController
@RequestMapping("api/pro/qcProject")
public class QCProjectController extends BaseJpaController<DtoProject, String,QCProjectService> {
    /**
     * 分页动态条件查询项目
     *
     * @param qcProjectCriteria 条件参数
     * @return RestResponse<List<DtoQCProject>>
     */
    @ApiOperation(value = "分页动态条件查询项目", notes = "分页动态条件查询项目")
    @GetMapping
    public RestResponse<List<DtoQCProject>> findByPage(QCProjectCriteria qcProjectCriteria) {
        PageBean<DtoQCProject> pageBean = super.getPageBean();
        RestResponse<List<DtoQCProject>> restResponse = new RestResponse<>();
        service.findQCProjectByPage(pageBean, qcProjectCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询项目
     *
     * @param id 主键id
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "按主键查询项目", notes = "按主键查询项目")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoQCProject> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoQCProject> restResponse = new RestResponse<>();
        DtoQCProject project = service.findQCProject(id);
        restResponse.setData(project);
        restResponse.setRestStatus(StringUtil.isNull(project) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增项目
     *
     * @param project 实体
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "新增项目", notes = "新增项目")
    @PostMapping
    public RestResponse<DtoProject> create(@RequestBody @Validated DtoProject project) {
        RestResponse<DtoProject> restResponse = new RestResponse<>();
        restResponse.setData(service.save(project));
        return restResponse;
    }

    /**
     * 现场质控提交
     *
     * @param qcProjectId 质控项目标识
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "现场质控提交", notes = "现场质控提交")
    @PostMapping("/local/submit/{qcProjectId}")
    public RestResponse<Void> localSubmit(@PathVariable String qcProjectId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.localSubmit(qcProjectId);
        return restResponse;
    }

    /**
     * 修改项目
     *
     * @param project 实体列表
     * @return RestResponse<DtoProject>
     */
    @ApiOperation(value = "修改项目", notes = "修改项目")
    @PutMapping
    public RestResponse<DtoProject> update(@RequestBody @Validated DtoProject project) {
        RestResponse<DtoProject> restResponse = new RestResponse<>();
        restResponse.setData(service.update(project));
        return restResponse;
    }

    /**
     * "根据id删除项目
     *
     * @param id id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除项目", notes = "根据id删除项目")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setCount(service.logicDeleteById(id));

        return restResp;
    }

    /**
     * "根据id批量删除项目
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除项目", notes = "根据id批量删除项目")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setCount(service.logicDeleteById(ids));
        return restResp;
    }

    /**
     * 复制项目
     *
     * @param dto 接收字段
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "复制项目", notes = "复制项目")
    @PostMapping(path = "/copy")
    public RestResponse<Boolean> copy(@RequestBody DtoQCProjectCopy dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.copyProject(dto);
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * "信号操作
     *
     * @param dto 实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "信号操作", notes = "信号操作")
    @PostMapping("/signal")
    public RestResponse<Boolean> signal(@RequestBody DtoWorkflowSign dto) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.projectSignal(dto);
        restResp.setData(true);
        return restResp;
    }
}
