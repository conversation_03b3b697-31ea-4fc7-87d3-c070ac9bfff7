package com.sinoyd.lims.pro.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.boot.frame.sys.enums.ERedisRssTopic;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.redis.*;
import com.sinoyd.lims.pro.service.redis.*;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.stereotype.Component;

@Configuration
@EnableCaching
public class RedisConfig extends CachingConfigurerSupport {
    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target,  method,  params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getName());
            sb.append(method.getName());
            for (Object obj : params) {
                sb.append(obj.toString());
            }
            return sb.toString();
        };
    }

    @SuppressWarnings("rawtypes")
    @Bean
    public CacheManager cacheManager(RedisTemplate redisTemplate) {
        RedisCacheManager rcm = new RedisCacheManager(redisTemplate);
        //设置缓存过期时间
        rcm.setDefaultExpiration(60);//秒
        return rcm;
    }

    @Bean
    @Primary
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory factory) {
        StringRedisTemplate template = new StringRedisTemplate(factory);
        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public RedisTemplate<String, String> redisJacksonTemplate(RedisConnectionFactory factory) {
        StringRedisTemplate template = new StringRedisTemplate(factory);

        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        template.setValueSerializer(jackson2JsonRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }

    @Bean
    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory,
                                            MessageListenerAdapter listenerAdapterForCreateUser,
                                            MessageListenerAdapter listenerAdapterForUpdateAnalyzeItem,
                                            MessageListenerAdapter listenerAdapterForUpdateParamsDimension,
                                            MessageListenerAdapter listenerAdapterForUpdateParamsConfigDimension,
                                            MessageListenerAdapter listenerAdapterForCreateConsumableStorage,
                                            MessageListenerAdapter listenerAdapterForUpdateDeleteContract,
                                            MessageListenerAdapter listenerAdapterForAnalysePersonChange,
                                            MessageListenerAdapter listenerAdapterForClearCacheNotice,
                                            MessageListenerAdapter listenerAdapterForClearFastNavigationCache
    ) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        //订阅了一个叫chat 的通道
        container.addMessageListener(listenerAdapterForCreateUser, new PatternTopic(ERedisRssTopic.USER_ADD.getChannel()));
        container.addMessageListener(listenerAdapterForUpdateAnalyzeItem, new PatternTopic(EnumBase.EnumBASRedisChannel.BAS_AnalyzeItem_Update.name()));
        container.addMessageListener(listenerAdapterForUpdateParamsDimension, new PatternTopic(EnumBase.EnumBASRedisChannel.BAS_Dimension_Update.name()));
        container.addMessageListener(listenerAdapterForUpdateParamsConfigDimension, new PatternTopic(EnumBase.EnumBASRedisChannel.BAS_Dimension_Update.name()));
        container.addMessageListener(listenerAdapterForCreateConsumableStorage, new PatternTopic(EnumBase.EnumBASRedisChannel.BAS_ConsumableDetail_Create.name()));
        container.addMessageListener(listenerAdapterForUpdateDeleteContract, new PatternTopic(EnumLIM.EnumLIMRedisChannel.LIM_Contract_UpdateDelete.name()));
        container.addMessageListener(listenerAdapterForAnalysePersonChange, new PatternTopic(EnumLIM.EnumLIMRedisChannel.LIM_Person2Test_Save.name()));
        container.addMessageListener(listenerAdapterForClearCacheNotice, new PatternTopic(EnumLIM.EnumLIMRedisChannel.LIM_Notice_Cache.name()));
        container.addMessageListener(listenerAdapterForClearFastNavigationCache, new PatternTopic(EnumLIM.EnumLIMRedisChannel.LIM_FastNavigation_Cache.name()));
        //这个container 可以添加多个 messageListener
        return container;
    }
    @Bean
    MessageListenerAdapter listenerAdapter(MessageReceiver receiver) {
        //这个地方 是给messageListenerAdapter 传入一个消息接受的处理器，利用反射的方法调用“receiveMessage”
        //也有好几个重载方法，这边默认调用处理器的方法 叫handleMessage 可以自己到源码里面看
        return new MessageListenerAdapter(receiver, "receiveMessage");
    }

    @Bean
    @Scope("prototype")
    MessageListenerAdapter listenerAdapterForCreateUser(UserChannel receiver) {
        return new MessageListenerAdapter(receiver, "createUser");
    }

    @Bean
    @Scope("prototype")
    MessageListenerAdapter listenerAdapterForUpdateAnalyzeItem(TestChannel receiver) {
        return new MessageListenerAdapter(receiver, "updateAnalyzeItem");
    }

    @Bean
    @Scope("prototype")
    MessageListenerAdapter listenerAdapterForUpdateParamsDimension(ParamsChannel receiver) {
        return new MessageListenerAdapter(receiver, "updateDimension");
    }

    @Bean
    @Scope("prototype")
    MessageListenerAdapter listenerAdapterForUpdateParamsConfigDimension(ParamsConfigChannel receiver) {
        return new MessageListenerAdapter(receiver, "updateDimension");
    }

    @Bean
    @Scope("prototype")
    MessageListenerAdapter listenerAdapterForCreateConsumableStorage(ConsumableStorageChannel receiver) {
        return new MessageListenerAdapter(receiver, "createConsumableStorage");
    }

    //
    @Bean
    @Scope("prototype")
    MessageListenerAdapter listenerAdapterForUpdateDeleteContract(ProjectContractChannel receiver) {
        return new MessageListenerAdapter(receiver, "updateProjectContractJson");
    }
    @Bean
    @Scope("prototype")
    MessageListenerAdapter listenerAdapterForAnalysePersonChange(AnalysePersonChangeChannel receiver) {
        return new MessageListenerAdapter(receiver, "updateAnalystId");
    }

    @Bean
    @Scope("prototype")
    MessageListenerAdapter listenerAdapterForClearCacheNotice(HomeCacheChannel receiver) {
        return new MessageListenerAdapter(receiver, "clearCacheNotices");
    }

    @Bean
    @Scope("prototype")
    MessageListenerAdapter listenerAdapterForClearFastNavigationCache(HomeCacheChannel receiver) {
        return new MessageListenerAdapter(receiver, "clearFastNavigationCache");
    }
    /**redis 消息处理器*/
    @Component
    public class MessageReceiver {

        /**接收消息的方法*/
        public void receiveMessage(String message){
            System.out.println(message);
        }

    }
}
