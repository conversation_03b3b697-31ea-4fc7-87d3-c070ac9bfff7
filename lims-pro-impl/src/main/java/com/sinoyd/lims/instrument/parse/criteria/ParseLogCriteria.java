package com.sinoyd.lims.instrument.parse.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * 仪器解析数据查询条件实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/9/3
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParseLogCriteria extends BaseCriteria implements Serializable {

    /**
     * 解析开始日期
     */
    private String startTime;

    /**
     * 解析结束日期
     */
    private String endTime;

    /**
     * 关键字: 仪器名称或编号
     */
    private String key;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 解析状态(1:解析中 2:解析成功 3:解析失败 )
     */
    private String parseStatus;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        //关联条件
        condition.append(" and l.appId = p.id ");
        //解析成功
//        condition.append(" and l.parseStatus = '2' ");
        // 获取解析类型为不等于文件解析调试
        condition.append(" and p.parseType <> '3' ");
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and l.beginTime >= :startDate ");
            values.put("startDate", from);
        }

        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            to = c.getTime();
            condition.append(" and l.beginTime < :endDate ");
            values.put("endDate", to);
        }

        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (p.instrumentCode like :key or p.instrumentName like :key) ");
            values.put("key", "%" + this.key + "%");
        }

        if (StringUtil.isNotEmpty(this.fileName)) {
            condition.append(" and l.fileOrgName like :fileName  ");
            values.put("fileName", "%" + this.fileName + "%");
        }
        if (StringUtil.isNotNull(this.parseStatus) && !this.parseStatus.equals("-1")) {
            condition.append(" and l.parseStatus = :parseStatus ");
            values.put("parseStatus", this.parseStatus);
        }

        return condition.toString();
    }
}