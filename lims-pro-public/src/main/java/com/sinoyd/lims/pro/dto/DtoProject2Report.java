package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.Project2Report;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoProject2Report 实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/04/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_Project2Report")
@Data
@DynamicInsert
public class DtoProject2Report extends Project2Report {

    /*
     * 报告编号
     */
    @Transient
    private String reportCode;

    /**
     * 编制人id
     */
    @Transient
    private String maker;

    /**
     * 编制人名称
     */
    @Transient
    private String makerName;

    /**
     * 编制日期
     */
    @Transient
    private String makeDate;

    /**
     * 报告状态
     */
    @Transient
    private String reportStatus;

    /**
     * 报告发放回收状态
     */
    @Transient
    private Integer grantStatus;

    /**
     *  发放人
     */
    @Transient
    private String sender;

    /**
     * 发放日期
     */
    @Transient
    private String sendDate;

    /**
     * 电子报告附件名称
     */
    @Transient
    private String fileName;

    /**
     * 文件后缀
     */
    @Transient
    private String docSuffix;

    /**
     * 文件路径
     */
    @Transient
    private String path;

    public DtoProject2Report(String reportId, String projectId, Integer uploadStatus) {
        this.setReportId(reportId);
        this.setProjectId(projectId);
        this.setUploadStatus(uploadStatus);
    }

    public DtoProject2Report() {

    }
}