package com.sinoyd.lims.pro.dto.customer;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;

/**
 * 质控样数据统计Dto
 * <AUTHOR>
 * @version V1.0.0 2022/11/20
 * @since V100R001
 */
@Data
@AllArgsConstructor
public class DtoQCSampleCensus {
    /**
     * 质控样id
     */
    private String id;

    /**
     * 质控样样品编号
     */
    private String sampleCode;

    /**
     * 原样样品编号
     */
    private String associateCode;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 测试项目名称
     */
    private String redAnalyseItems;

    /**
     * 分析方法
     */
    private String redAnalyseMethods;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 样品状态
     */
    private String sampleStatus;

    /**
     * 原样样品id
     */
    private String associateSampleId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 分析时间
     */
    private String analyseTime = "";

    /**
     * 分析时间（多测试项目分析时间不同的）
     */
    private Map<String,Object> analyseTimes;

    /**
     * 分析人
     */
    private String analysePerson;
    /**
     * 是否合格（0：否，1：是，2：未评价）
     */
    private Integer isPass;

    public DtoQCSampleCensus(String id, String sampleCode, String associateCode, String redFolderName, String redAnalyseItems,
                             Integer qcGrade, Integer qcType, String sampleTypeName, String sampleStatus, String associateSampleId,
                             String sampleTypeId) {
        this.id = id;
        this.sampleCode = sampleCode;
        this.associateCode = associateCode;
        this.redFolderName = redFolderName;
        this.redAnalyseItems = redAnalyseItems;
        this.qcGrade = qcGrade;
        this.qcType = qcType;
        this.sampleTypeName = sampleTypeName;
        this.sampleStatus = sampleStatus;
        this.associateSampleId = associateSampleId;
        this.sampleTypeId = sampleTypeId;
    }
    public DtoQCSampleCensus(){}
}
