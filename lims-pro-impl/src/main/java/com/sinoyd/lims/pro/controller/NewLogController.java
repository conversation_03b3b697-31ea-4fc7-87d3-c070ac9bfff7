package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.NewLogCriteria;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.service.NewLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 操作日志接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/02/18
 * @since V100R001
 */
@Api(tags = "示例: 操作日志")
@RestController
@RequestMapping("api/pro/newLog")
public class NewLogController extends ExceptionHandlerController<NewLogService> {
    /**
     * 动态条件查询日志
     *
     * @param criteria 条件参数
     * @return RestResponse<List < D toLog>>
     */
    @ApiOperation(value = "动态条件查询项目", notes = "动态条件查询项目")
    @GetMapping
    public RestResponse<List<DtoLog>> findByPage(NewLogCriteria criteria) {
        RestResponse<List<DtoLog>> restResponse = new RestResponse<>();
        restResponse.setData(service.findNewLog(criteria));
        restResponse.setRestStatus(StringUtil.isEmpty(restResponse.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(StringUtil.isEmpty(restResponse.getData()) ? 0 : restResponse.getData().size());
        return restResponse;
    }
}
