package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoFolder;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.FolderRepository;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.pro.criteria.FileAuditCriteria;
import com.sinoyd.lims.pro.dto.DtoFileAudit;
import com.sinoyd.lims.pro.dto.DtoFileAuditStatus;
import com.sinoyd.lims.pro.dto.DtoOATaskHandleLog;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.FileAuditRepository;
import com.sinoyd.lims.pro.repository.FileAuditStatusRepository;
import com.sinoyd.lims.pro.repository.OATaskHandleLogRepository;
import com.sinoyd.lims.pro.service.FileAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * FileAudit操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/19
 * @since V100R001
 */
@Service
public class FileAuditServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFileAudit, String, FileAuditRepository> implements FileAuditService {

    private DocumentRepository documentRepository;

    private PersonRepository personRepository;

    private DepartmentService departmentService;

    private FileAuditStatusRepository fileAuditStatusRepository;

    private FolderRepository folderRepository;

    private CommonRepository commonRepository;

    private OATaskHandleLogRepository oaTaskHandleLogRepository;

    @Override
    @Transactional
    public DtoFileAudit save(DtoFileAudit entity) {
        //更新文件名称
        updateFileName(entity);
        entity.setStepCode(EnumPRO.EnumFileAuditStep.文件申请.getCode());
        entity.setStepStatus(EnumPRO.EnumFileAuditStatus.新建.getValue());
        entity.setCreator(PrincipalContextUser.getPrincipal().getUserId());
        entity.setCreateDate(new Date());
        DtoFileAudit save = super.save(entity);
        //创建状态实体
        DtoFileAuditStatus fileAuditStatus = new DtoFileAuditStatus();
        fileAuditStatus.setFileAuditId(entity.getId());
        fileAuditStatus.setStepCode(EnumPRO.EnumFileAuditStep.文件申请.getCode());
        fileAuditStatus.setCurrentPersonId(PrincipalContextUser.getPrincipal().getUserId());
        fileAuditStatus.setLastOperateDate(new Date());
        fileAuditStatusRepository.save(fileAuditStatus);
        save.setCurrentPersonId(fileAuditStatus.getCurrentPersonId());
        //处理日志
        DtoOATaskHandleLog log = new DtoOATaskHandleLog();
        log.setTaskId(entity.getId());
        log.setIsAgree(false);
        log.setIsFirstStep(true);
        log.setCompleteTime(new Date());
        log.setAssigneeId(PrincipalContextUser.getPrincipal().getUserId());
        log.setAssigneeName(PrincipalContextUser.getPrincipal().getUserName());
        log.setActTaskName(EnumPRO.EnumFileAuditStep.文件申请.name());
        log.setActTaskDefKey("fileAudit");
        oaTaskHandleLogRepository.save(log);
        return save;
    }

    @Override
    @Transactional
    public DtoFileAudit update(DtoFileAudit entity) {
        //更新文件名称
        updateFileName(entity);
        updateFileRelated(entity.getFileRelations());
        return super.update(entity);
    }

    @Override
    public void findByPage(PageBean<DtoFileAudit> page, BaseCriteria criteria) {
        FileAuditCriteria fileAuditCriteria = (FileAuditCriteria) criteria;
        if (StringUtil.isEmpty(fileAuditCriteria.getStepCode())) {
            //必传 步骤编码
            return;
        }
        PageBean<Object[]> tempPage = new PageBean<>();
        tempPage.setEntityName("DtoFileAudit p,DtoFileAuditStatus s");
        tempPage.setSelect("select p,s.currentPersonId,s.isHandle");
        tempPage.setSort(page.getSort());
        commonRepository.findByPage(tempPage,criteria);
        page.setRowsCount(tempPage.getRowsCount());
        List<Object[]> tempList = tempPage.getData();
        if(StringUtil.isNotEmpty(tempList)){
            List<DtoFileAudit> list = new ArrayList<>();
            for (Object[] objArr:tempList ) {
                DtoFileAudit entity = (DtoFileAudit) objArr[0];
                entity.setCurrentPersonId((String) objArr[1]);
                entity.setIsHandle((Boolean) objArr[2]);
                list.add(entity);
            }
            if (StringUtil.isNotEmpty(list)) {
                loadTransientFields(list);
            }
            page.setData(list);
        }
    }

    @Override
    public DtoFileAudit findOne(String key) {
        DtoFileAudit entity = super.findOne(key);
        loadTransientFields(Collections.singletonList(entity));
        return entity;
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        fileAuditStatusRepository.deleteByFileAuditIdIn((Collection<String>) ids);
        return super.logicDeleteById(ids);
    }

    @Override
    @Transactional
    public void submit(Map<String, Object> params) {
        List<String> ids = params.containsKey("ids") ? (List<String>) params.get("ids") : new ArrayList<>();
        String opinion = params.containsKey("opinion") ? (String) params.get("opinion") : "";
        Boolean isPass = params.containsKey("isPass") ? (Boolean) params.get("isPass") : null;
        if (ids != null && isPass != null) {
            for (String fileAuditId : ids) {
                submitFileAudit(fileAuditId, isPass, opinion);
            }
        }
    }

    @Override
    @Transactional
    public void updateFileRelation(DtoFileAudit fileAudit) {
        DtoFileAudit entity = repository.findOne(fileAudit.getId());
        entity.setDocumentIds(fileAudit.getDocumentIds());
        updateFileName(entity);
        repository.save(entity);
        updateFileRelated(fileAudit.getFileRelations());
    }

    @Override
    public DtoFileAudit findAttachPath(String id) {
        return repository.findOne(id);
    }

    /**
     * 更新文件名称
     */
    private void updateFileName(DtoFileAudit entity) {
        if (StringUtil.isNotEmpty(entity.getDocumentIds())) {
            List<String> documentIds = Arrays.asList(entity.getDocumentIds().split(","));
            if (StringUtil.isNotEmpty(documentIds)) {
                entity.setFileName(documentRepository.findAll(documentIds).stream().map(DtoDocument::getFilename).collect(Collectors.joining(",")));
            }
        }
    }

    /**
     * 填充附加字段
     *
     * @param list 原始数据
     */
    private void loadTransientFields(List<DtoFileAudit> list) {
        if (StringUtil.isNotEmpty(list)) {
            List<DtoPerson> personList = personRepository.findAll();
            Map<String, String> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
            List<DtoDepartment> departmentList = departmentService.findAll();
            Map<String, String> departmentMap = departmentList.stream().collect(Collectors.toMap(DtoDepartment::getId, DtoDepartment::getDeptName));
            List<DtoFolder> folderList = folderRepository.findAll();
            Map<String, String> folderMap = folderList.stream().collect(Collectors.toMap(DtoFolder::getId, DtoFolder::getFolderName));
            List<DtoDocument> allDocumentList = documentRepository.findAll().stream().filter(v->Boolean.FALSE.equals(v.getIsDeleted())).collect(Collectors.toList());
            for (DtoFileAudit entity : list) {
                //审批类型
                entity.setTypeStr(EnumPRO.EnumFileAuditApplyType.getNameByValue(entity.getType()));
                //审批状态
                entity.setStepStatusStr(EnumPRO.EnumFileAuditStatus.getNameByValue(entity.getStepStatus()));
                //修编人
                entity.setRevisePersonStr(personMap.getOrDefault(entity.getRevisePersonId(), ""));
                //修编部门
                entity.setReviseDeptStr(departmentMap.getOrDefault(entity.getReviseDeptId(), ""));
                //文件目录名称
                entity.setFolderStr(folderMap.getOrDefault(entity.getFolderId(), ""));
                //参与审核人员
                entity.setAuditPeopleStr("");
                if (StringUtil.isNotEmpty(entity.getAuditPeople())) {
                    List<String> auditPeopleIds = Arrays.asList(entity.getAuditPeople().split(","));
                    entity.setAuditPeopleStr(personList.stream().filter(v -> auditPeopleIds.contains(v.getId())).map(DtoPerson::getCName)
                            .distinct().sorted().collect(Collectors.joining("、")));
                }
                //技术负责人
                entity.setTechLeaderStr(personMap.getOrDefault(entity.getTechLeaderId(), ""));
                //批准人员
                entity.setApprovePersonStr(personMap.getOrDefault(entity.getApprovePersonId(), ""));
                //接收人员
                entity.setReceivePeopleStr("");
                if (StringUtil.isNotEmpty(entity.getReceivePeople())) {
                    List<String> receivePeopleIds = Arrays.asList(entity.getReceivePeople().split(","));
                    entity.setReceivePeopleStr(personList.stream().filter(v -> receivePeopleIds.contains(v.getId())).map(DtoPerson::getCName)
                            .distinct().sorted().collect(Collectors.joining("、")));
                }
                //文件备案人员
                entity.setRegisterPersonStr(personMap.getOrDefault(entity.getRegisterPersonId(), ""));
                //文件
                entity.setDocumentList(new ArrayList<>());
                if (StringUtil.isNotEmpty(entity.getDocumentIds())) {
                    List<String> documentIds = Arrays.asList(entity.getDocumentIds().split(","));
                    entity.setDocumentList(allDocumentList.stream().filter(v -> documentIds.contains(v.getId())).collect(Collectors.toList()));
                }
            }
        }
    }

    /**
     * 提交单条文件审批            会签步骤不允许退回
     *
     * @param fileAuditId 文件审批标识
     * @param isPass      通过/退回
     * @param opinion     意见
     */
    private void submitFileAudit(String fileAuditId, Boolean isPass, String opinion) {
        List<DtoFileAuditStatus> waitUpdateList = new ArrayList<>();
        List<DtoDocument> fileUpdateList = new ArrayList<>();
        Date now = new Date();
        DtoFileAudit fileAudit = repository.findOne(fileAuditId);
        String stepCode = fileAudit.getStepCode();
        List<DtoFileAuditStatus> existStepStatusList = fileAuditStatusRepository.findByFileAuditId(fileAudit.getId());
        if (EnumPRO.EnumFileAuditStep.文件申请.getCode().equals(stepCode)) {
            //只能是提交
            createNextStepStatus(fileAudit, EnumPRO.EnumFileAuditStep.审核会签);
            existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.文件申请.getCode().equals(v.getStepCode())).forEach(v -> {
                v.setIsHandle(true);
                v.setIsPass(true);
                v.setLastOperateDate(now);
                waitUpdateList.add(v);
            });
            fileAudit.setStartDate(new Date());
            fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.审核会签.getCode());
            fileAudit.setStepStatus(EnumPRO.EnumFileAuditStep.审核会签.getStandardStatus());
        } else if (EnumPRO.EnumFileAuditStep.审核会签.getCode().equals(stepCode)) {
            // 改为可以退回，当最后一名审核人员提交时，若存在一名及以上审核人员选择审核不通过，则退回至文件审批申请。
            existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.审核会签.getCode().equals(v.getStepCode())
                    && PrincipalContextUser.getPrincipal().getUserId().equals(v.getCurrentPersonId())&&Boolean.FALSE.equals(v.getIsHandle())).findFirst()
                    .ifPresent(v -> {
                        v.setIsHandle(true);
                        v.setIsPass(isPass);
                        v.setLastNewOpinion(opinion);
                        v.setLastOperateDate(now);
                        waitUpdateList.add(v);
                    });
            //会签需都通过才创建下一步状态
            List<DtoFileAuditStatus> batchAuditList = existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.审核会签.getCode().equals(v.getStepCode())).collect(Collectors.toList());
            if (batchAuditList.stream().allMatch(v -> Boolean.TRUE.equals(v.getIsHandle()) && Boolean.TRUE.equals(v.getIsPass()))) {
                createNextStepStatus(fileAudit, EnumPRO.EnumFileAuditStep.负责人审核);
                fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.负责人审核.getCode());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStep.负责人审核.getStandardStatus());
            } else if (batchAuditList.stream().allMatch(DtoFileAuditStatus::getIsHandle) && 
                      batchAuditList.stream().anyMatch(v -> Boolean.FALSE.equals(v.getIsPass()))) {                // 退回至文件申请，删除审核会签流程
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.文件申请.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(false);
                    v.setIsPass(false);
                    v.setLastNewOpinion(null);
                    waitUpdateList.add(v);
                });
                fileAuditStatusRepository.delete(existStepStatusList.stream().filter(v->EnumPRO.EnumFileAuditStep.审核会签.getCode().equals(v.getStepCode())).collect(Collectors.toList()));
                fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.文件申请.getCode());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStatus.退回.getValue());
            }
        } else if (EnumPRO.EnumFileAuditStep.负责人审核.getCode().equals(stepCode)) {
            //提交
            if(Boolean.TRUE.equals(isPass)){
                createNextStepStatus(fileAudit, EnumPRO.EnumFileAuditStep.文件批准);
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.负责人审核.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(true);
                    v.setIsPass(true);
                    v.setLastNewOpinion(opinion);
                    v.setLastOperateDate(now);
                    waitUpdateList.add(v);
                });
                fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.文件批准.getCode());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStep.文件批准.getStandardStatus());
            }else{
                //退回到文件申请步骤，多人审批步骤还需看不见记录
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.负责人审核.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(true);
                    v.setIsPass(false);
                    v.setLastNewOpinion(opinion);
                    v.setLastOperateDate(now);
                    waitUpdateList.add(v);
                });
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.文件申请.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(false);
                    v.setIsPass(false);
                    v.setLastNewOpinion(null);
                    waitUpdateList.add(v);
                });
                fileAuditStatusRepository.delete(existStepStatusList.stream().filter(v->EnumPRO.EnumFileAuditStep.审核会签.getCode().equals(v.getStepCode())).collect(Collectors.toList()));
                fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.文件申请.getCode());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStatus.退回.getValue());
            }
        } else if (EnumPRO.EnumFileAuditStep.文件批准.getCode().equals(stepCode)) {
            //提交
            if(Boolean.TRUE.equals(isPass)){
                createNextStepStatus(fileAudit, EnumPRO.EnumFileAuditStep.接收会签);
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.文件批准.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(true);
                    v.setIsPass(true);
                    v.setLastNewOpinion(opinion);
                    v.setLastOperateDate(now);
                    waitUpdateList.add(v);
                });
                fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.接收会签.getCode());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStep.接收会签.getStandardStatus());
            }else{
                //退回
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.文件批准.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(true);
                    v.setIsPass(false);
                    v.setLastNewOpinion(opinion);
                    v.setLastOperateDate(now);
                    waitUpdateList.add(v);
                });
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.文件申请.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(false);
                    v.setIsPass(false);
                    v.setLastNewOpinion(null);
                    waitUpdateList.add(v);
                });
                List<String> delCodeList = Arrays.asList(EnumPRO.EnumFileAuditStep.审核会签.getCode(),EnumPRO.EnumFileAuditStep.负责人审核.getCode());
                fileAuditStatusRepository.delete(existStepStatusList.stream().filter(v->delCodeList.contains(v.getStepCode())).collect(Collectors.toList()));
                fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.文件申请.getCode());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStatus.退回.getValue());
            }
        } else if (EnumPRO.EnumFileAuditStep.接收会签.getCode().equals(stepCode)) {
            //提交
            existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.接收会签.getCode().equals(v.getStepCode())
                    && PrincipalContextUser.getPrincipal().getUserId().equals(v.getCurrentPersonId())&&Boolean.FALSE.equals(v.getIsHandle())).findFirst()
                    .ifPresent(v -> {
                        v.setIsHandle(true);
                        v.setIsPass(isPass);
                        v.setLastNewOpinion(opinion);
                        v.setLastOperateDate(now);
                        waitUpdateList.add(v);
                    });
            //会签需都通过才创建下一步状态
            List<DtoFileAuditStatus> batchReceiveList = existStepStatusList.stream()
                    .filter(v -> EnumPRO.EnumFileAuditStep.接收会签.getCode().equals(v.getStepCode())).collect(Collectors.toList());
            if (batchReceiveList.stream().allMatch(DtoFileAuditStatus::getIsHandle)&&
                    existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.接收会签.getCode().equals(v.getStepCode()))
                    .allMatch(v -> Boolean.TRUE.equals(v.getIsPass()))) {
                createNextStepStatus(fileAudit, EnumPRO.EnumFileAuditStep.备案);
                fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.备案.getCode());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStep.备案.getStandardStatus());
            }
            if (batchReceiveList.stream().allMatch(DtoFileAuditStatus::getIsHandle)&&
                    existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.接收会签.getCode().equals(v.getStepCode()))
                    .anyMatch(v -> Boolean.FALSE.equals(v.getIsPass()))) {
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.文件申请.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(false);
                    v.setIsPass(false);
                    v.setLastNewOpinion(null);
                    waitUpdateList.add(v);
                });
                fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.文件申请.getCode());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStatus.退回.getValue());
            }
        } else if (EnumPRO.EnumFileAuditStep.备案.getCode().equals(stepCode)) {
            //提交
            if(Boolean.TRUE.equals(isPass)){
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.备案.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(true);
                    v.setIsPass(true);
                    v.setLastOperateDate(now);
                    waitUpdateList.add(v);
                });
                fileAudit.setPassDate(new Date());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStatus.审批通过.getValue());
                fileUpdateList = collectAuditFile(fileAudit);
            }else{
                //退回到负责人审核步骤 多人接收步骤还行看不见
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.备案.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(true);
                    v.setIsPass(false);
                    v.setLastNewOpinion(opinion);
                    v.setLastOperateDate(now);
                    waitUpdateList.add(v);
                });
                existStepStatusList.stream().filter(v -> EnumPRO.EnumFileAuditStep.负责人审核.getCode().equals(v.getStepCode())).forEach(v -> {
                    v.setIsHandle(false);
                    v.setIsPass(false);
                    v.setLastNewOpinion(null);
                    waitUpdateList.add(v);
                });
                fileAuditStatusRepository.delete(existStepStatusList.stream().filter(v->EnumPRO.EnumFileAuditStep.接收会签.getCode().equals(v.getStepCode())).collect(Collectors.toList()));
                fileAudit.setStepCode(EnumPRO.EnumFileAuditStep.负责人审核.getCode());
                fileAudit.setStepStatus(EnumPRO.EnumFileAuditStatus.退回.getValue());
            }
        }
        fileAuditStatusRepository.save(waitUpdateList);
        repository.save(fileAudit);
        documentRepository.save(fileUpdateList);
        //处理日志
        DtoOATaskHandleLog log = new DtoOATaskHandleLog();
        log.setTaskId(fileAuditId);
        log.setIsAgree(isPass);
        log.setComment(opinion);
        log.setCompleteTime(new Date());
        log.setAssigneeId(PrincipalContextUser.getPrincipal().getUserId());
        log.setAssigneeName(PrincipalContextUser.getPrincipal().getUserName());
        log.setIsFirstStep(false);
        log.setActTaskName(EnumPRO.EnumFileAuditStep.getByCode(stepCode)!=null?EnumPRO.EnumFileAuditStep.getByCode(stepCode).name():"");
        log.setActTaskDefKey("fileAudit");
        oaTaskHandleLogRepository.save(log);
    }


    /**
     * 初始化后续步骤的状态记录
     *
     * @param fileAudit 文件审批
     */
    private void createNextStepStatus(DtoFileAudit fileAudit, EnumPRO.EnumFileAuditStep v) {
        List<DtoFileAuditStatus> nextStepStatusList = fileAuditStatusRepository.findByFileAuditIdAndStepCode(fileAudit.getId(), v.getCode());
        //首次提交
        if (nextStepStatusList.isEmpty()) {
            List<DtoFileAuditStatus> fileAuditStatusList = new ArrayList<>();
            if (EnumPRO.EnumFileAuditStep.审核会签.equals(v)) {
                if (StringUtil.isNotEmpty(fileAudit.getAuditPeople())) {
                    Arrays.asList(fileAudit.getAuditPeople().split(",")).forEach(p -> {
                        DtoFileAuditStatus fileAuditStatus = new DtoFileAuditStatus();
                        fileAuditStatus.setFileAuditId(fileAudit.getId());
                        fileAuditStatus.setStepCode(EnumPRO.EnumFileAuditStep.审核会签.getCode());
                        fileAuditStatus.setCurrentPersonId(p);
                        fileAuditStatusList.add(fileAuditStatus);
                    });
                }
            } else if (EnumPRO.EnumFileAuditStep.负责人审核.equals(v)) {
                DtoFileAuditStatus fileAuditStatus = new DtoFileAuditStatus();
                fileAuditStatus.setFileAuditId(fileAudit.getId());
                fileAuditStatus.setStepCode(EnumPRO.EnumFileAuditStep.负责人审核.getCode());
                fileAuditStatus.setCurrentPersonId(fileAudit.getTechLeaderId());
                fileAuditStatusList.add(fileAuditStatus);
            } else if (EnumPRO.EnumFileAuditStep.文件批准.equals(v)) {
                DtoFileAuditStatus fileAuditStatus = new DtoFileAuditStatus();
                fileAuditStatus.setFileAuditId(fileAudit.getId());
                fileAuditStatus.setStepCode(EnumPRO.EnumFileAuditStep.文件批准.getCode());
                fileAuditStatus.setCurrentPersonId(fileAudit.getApprovePersonId());
                fileAuditStatusList.add(fileAuditStatus);
            } else if (EnumPRO.EnumFileAuditStep.接收会签.equals(v)) {
                if (StringUtil.isNotEmpty(fileAudit.getReceivePeople())) {
                    Arrays.asList(fileAudit.getReceivePeople().split(",")).forEach(p -> {
                        DtoFileAuditStatus fileAuditStatus = new DtoFileAuditStatus();
                        fileAuditStatus.setFileAuditId(fileAudit.getId());
                        fileAuditStatus.setStepCode(EnumPRO.EnumFileAuditStep.接收会签.getCode());
                        fileAuditStatus.setCurrentPersonId(p);
                        fileAuditStatusList.add(fileAuditStatus);
                    });
                }
            } else if (EnumPRO.EnumFileAuditStep.备案.equals(v)) {
                DtoFileAuditStatus fileAuditStatus = new DtoFileAuditStatus();
                fileAuditStatus.setFileAuditId(fileAudit.getId());
                fileAuditStatus.setStepCode(EnumPRO.EnumFileAuditStep.备案.getCode());
                fileAuditStatus.setCurrentPersonId(fileAudit.getRegisterPersonId());
                fileAuditStatusList.add(fileAuditStatus);
            }
            fileAuditStatusRepository.save(fileAuditStatusList);
        }else{
            //退回再提交，重置下一步状态
            nextStepStatusList.forEach(e->{
                e.setIsHandle(false);
                e.setIsPass(false);
                e.setLastNewOpinion("");
                e.setLastOperateDate(DateUtil.stringToDate("1753-01-01", DateUtil.YEAR));
            });
            fileAuditStatusRepository.save(nextStepStatusList);
        }
    }

    /**
     * 整理需要处理的问价
     * @param fileAudit 文件审批对象
     * @return 结果
     */
    private List<DtoDocument> collectAuditFile(DtoFileAudit fileAudit) {
        List<DtoDocument> list = new ArrayList<>();
        List<String> documentIds = Arrays.asList(fileAudit.getDocumentIds().split(","));
        if(StringUtil.isNotEmpty(documentIds)){
            List<DtoDocument> documentList = documentRepository.findAll(documentIds);
            if(EnumPRO.EnumFileAuditApplyType.修订.getValue().equals(fileAudit.getType())||EnumPRO.EnumFileAuditApplyType.作废.getValue().equals(fileAudit.getType())){
                //针对文件的作废，需要自动将文件状态置为作废状态，记录作废日期。
                //-针对文件的修编，需要根据新文件的启用日期，将原文件置为作废状态，记录作废日期。
                Date abolishDate = fileAudit.getIsSetValidDate()?fileAudit.getValidDate():fileAudit.getPassDate();
                String operatePersonId = PrincipalContextUser.getPrincipal().getUserId();
                String operatePerson =  PrincipalContextUser.getPrincipal().getUserName();
                documentList.stream().filter(v-> UUIDHelper.GUID_EMPTY.equals(v.getRelateId())).forEach(v->{
                    v.setAuditStatus(EnumBase.EnumDocumentStatus.作废.getValue());
                    v.setOperateDate(abolishDate);
                    v.setOperatePersonId(operatePersonId);
                    v.setOperatePerson(operatePerson);
                    list.add(v);
                });

                //修编还需将新文件上传对应目录下
                if(EnumPRO.EnumFileAuditApplyType.修订.getValue().equals(fileAudit.getType())){
                    documentList.stream().filter(v-> !UUIDHelper.GUID_EMPTY.equals(v.getRelateId())).forEach(v->{
                        v.setFolderId(fileAudit.getFolderId());
                        v.setRelateId(UUIDHelper.GUID_EMPTY);
                        list.add(v);
                    });
                }
            }else if(EnumPRO.EnumFileAuditApplyType.新增.getValue().equals(fileAudit.getType())){
                documentList.forEach(v->v.setFolderId(fileAudit.getFolderId()));
                list.addAll(documentList);
            }
        }
        return list;
    }

    /**
     * 更新文件关联关系
     * @param relations 关系
     */
    private void updateFileRelated(List<String> relations){
        if(StringUtil.isNotEmpty(relations)){
            List<DtoDocument> documents = documentRepository.findAll().stream().filter(v->Boolean.FALSE.equals(v.getIsDeleted())).collect(Collectors.toList());
            List<DtoDocument> waitSaveList = new ArrayList<>();
            relations.forEach(v->{
                String[] idArr = v.split(";");
                if(idArr.length>1){
                     documents.stream().filter(d->d.getId().equals(idArr[0])).findFirst()
                            .ifPresent(d->{
                                d.setRelateId(idArr[1]);
                                waitSaveList.add(d);
                            });
                }
            });
            documentRepository.save(waitSaveList);
        }
    }

    @Autowired
    public void setDocumentRepository(DocumentRepository documentRepository) {
        this.documentRepository = documentRepository;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    public void setFileAuditStatusRepository(FileAuditStatusRepository fileAuditStatusRepository) {
        this.fileAuditStatusRepository = fileAuditStatusRepository;
    }

    @Autowired
    public void setFolderRepository(FolderRepository folderRepository) {
        this.folderRepository = folderRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setOaTaskHandleLogRepository(OATaskHandleLogRepository oaTaskHandleLogRepository) {
        this.oaTaskHandleLogRepository = oaTaskHandleLogRepository;
    }
}