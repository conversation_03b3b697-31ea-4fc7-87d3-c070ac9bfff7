package com.sinoyd.lims.pro.strategy.strategy.schemeSynchronization.sample;

import com.sinoyd.lims.pro.dto.DtoSamplingFrequencyTemp;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component(IFileNameConstant.SchemeSynchronizationKey.DELETED_SAMPLE)
public class DeletedSampleStrategy extends AbsSampleStrategy {

    @Override
    public void synchronizationSample(List<DtoSamplingFrequencyTemp> samplingFrequencyTempList) {
        List<String> frequencyIds = samplingFrequencyTempList.stream().map(DtoSamplingFrequencyTemp::getSamplingFrequencyId)
                .distinct().collect(Collectors.toList());
        List<String> workSheetFolderIds = sampleFolderService.getWorkSheetIdsBySamplingFrequencyId(frequencyIds);
        sampleFolderService.deleteTimes(frequencyIds);
        workSheetFolderService.checkWorkSheetFolder(workSheetFolderIds);
    }
}
