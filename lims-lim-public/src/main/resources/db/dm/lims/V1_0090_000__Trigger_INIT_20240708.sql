CREATE  TRIGGER "AFTER_UPDATE_RECORDCONFIG"
    AFTER  UPDATE
    ON "TB_LIM_RECORDCONFIG"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    IF new.isDeleted = 1 THEN
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 2, null, null, null, new.orgId,
        new.domainId);
else
       if new.recordName != old.recordName or (new.recordName is null and old.recordName is not null) or
           (new.recordName is not null and old.recordName is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'recordName',
        old.recordName, new.recordName, new.orgId, new.domainId);
END IF;
if new.recordType != old.recordType or (new.recordType is null and old.recordType is not null) or
           (new.recordType is not null and old.recordType is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'recordType',
        old.recordType, new.recordType, new.orgId, new.domainId);
END IF;
if new.reportConfigId != old.reportConfigId or (new.reportConfigId is null and old.reportConfigId is not null) or
           (new.reportConfigId is not null and old.reportConfigId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'reportConfigId',
        old.reportConfigId, new.reportConfigId, new.orgId, new.domainId);
END IF;
if new.sampleTypeId != old.sampleTypeId or (new.sampleTypeId is null and old.sampleTypeId is not null) or
           (new.sampleTypeId is not null and old.sampleTypeId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'sampleTypeId',
        old.sampleTypeId, new.sampleTypeId, new.orgId, new.domainId);
END IF;
if new.remark != old.remark or (new.remark is null and old.remark is not null) or
           (new.remark is not null and old.remark is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType,
                                   operateField, oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig', new.id, new.modifier, new.modifyDate, 3, 'remark',
        old.remark, new.remark, new.orgId, new.domainId);
END IF;
END IF;
end;
