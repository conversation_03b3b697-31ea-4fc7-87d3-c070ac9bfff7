CREATE TABLE "TB_QUERY_ITEM"
(
    "id"         VARCHAR(50)  NOT NULL,
    "itemName"   VARCHAR(100) NOT NULL,
    "itemDesc"   VARCHAR(255) NULL,
    "viewId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "usedTimes"  INT          DEFAULT 0
                              NOT NULL,
    "isDeleted"  BIT          DEFAULT 0
        NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL,
    "checkOut"   BIT          DEFAULT 0
                              NOT NULL
);
CREATE TABLE "TB_QUERY_ITEMCOLUMN"
(
    "id"             VARCHAR(50) NOT NULL,
    "itemId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "viewFieldId"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "columnLength"   INT          DEFAULT 5
                                 NOT NULL,
    "sortType"       VARCHAR(50) NOT NULL,
    "sortSeq"        INT          DEFAULT 0
                                 NOT NULL,
    "isShow"         BIT          DEFAULT 0
                                 NOT NULL,
    "isScreen"       BIT          DEFAULT 0
                                 NOT NULL,
    "isDeleted"      BIT          DEFAULT 0
                                 NOT NULL,
    "orgId"          VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "creator"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "createDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "domainId"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifier"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                 NOT NULL,
    "modifyDate"     TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                 NOT NULL,
    "dataSource"     VARCHAR(2000) NULL,
    "defaultControl" INT          DEFAULT 0
                                 NOT NULL
);
CREATE TABLE "TB_QUERY_ITEMCONDITION"
(
    "id"            VARCHAR(50) NOT NULL,
    "itemId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "dbCondition"   VARCHAR(2000) NULL,
    "pageCondition" VARCHAR(2000) NULL,
    "isDeleted"     BIT          DEFAULT 0
        NULL,
    "orgId"         VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "creator"       VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "createDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL,
    "domainId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "modifier"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                                NOT NULL,
    "modifyDate"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                                NOT NULL
);
CREATE TABLE "TB_QUERY_VIEW"
(
    "id"         VARCHAR(50)  NOT NULL,
    "viewName"   VARCHAR(100) NOT NULL,
    "typeName"   VARCHAR(100) NOT NULL,
    "typeDesc"   VARCHAR(255) NULL,
    "orderNum"   INT          DEFAULT 0
                              NOT NULL,
    "isDeleted"  BIT          DEFAULT 0
                              NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                              NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                              NOT NULL
);
CREATE TABLE "TB_QUERY_VIEWFIELD"
(
    "id"         VARCHAR(50) NOT NULL,
    "viewId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "dbField"    VARCHAR(50) NOT NULL,
    "pageField"  VARCHAR(255) NULL,
    "isShow"     BIT          DEFAULT 0
                             NOT NULL,
    "isDeleted"  BIT          DEFAULT 0
                             NOT NULL,
    "orgId"      VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "creator"    VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "createDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL,
    "domainId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifier"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                             NOT NULL,
    "modifyDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                             NOT NULL
);
ALTER TABLE "TB_QUERY_ITEM"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QUERY_ITEMCOLUMN"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QUERY_ITEMCONDITION"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QUERY_VIEW"
    ADD CONSTRAINT PRIMARY KEY ("id");

ALTER TABLE "TB_QUERY_VIEWFIELD"
    ADD CONSTRAINT PRIMARY KEY ("id");

COMMENT
ON TABLE "TB_QUERY_ITEM" IS '用户查询表，记录用户查询的信息';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."id" IS 'id';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."itemName" IS '查询名称';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."itemDesc" IS '查询描述';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."viewId" IS '查询所用视图id';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."usedTimes" IS '查询使用次数';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QUERY_ITEM"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QUERY_ITEMCOLUMN" IS '用户查询的列信息';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."id" IS '主键';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."itemId" IS '用户查询表id';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."viewFieldId" IS '视图字段表id';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."columnLength" IS '列宽';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."sortType" IS '排序类型，升序、降序';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."sortSeq" IS '排序节点';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."isShow" IS '是否显示';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."isScreen" IS '是否过滤';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."modifyDate" IS '修改时间';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."dataSource" IS '数据源';

COMMENT
ON COLUMN "TB_QUERY_ITEMCOLUMN"."defaultControl" IS '默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）';

COMMENT
ON TABLE "TB_QUERY_ITEMCONDITION" IS '用户查询条件信息表';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."id" IS 'id';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."itemId" IS '用户查询信息表id';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."dbCondition" IS '查询条件DB形式';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."pageCondition" IS '查询条件页面形式';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QUERY_ITEMCONDITION"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QUERY_VIEW" IS '自定义查询视图表';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."id" IS '主键';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."viewName" IS '视图名称';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."typeName" IS '查询类别(页面显示)';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."typeDesc" IS '查询类别描述';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."orderNum" IS '排序值';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QUERY_VIEW"."modifyDate" IS '修改时间';

COMMENT
ON TABLE "TB_QUERY_VIEWFIELD" IS '视图字段表';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."id" IS '主键';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."viewId" IS '视图表主键';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."dbField" IS 'DB字段名称';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."pageField" IS '页面字段名称';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."isShow" IS '是否显示在客户端';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."isDeleted" IS '是否删除';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."orgId" IS '组织机构id';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."creator" IS '创建人';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."createDate" IS '创建时间';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."domainId" IS '所属实验室';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."modifier" IS '修改人';

COMMENT
ON COLUMN "TB_QUERY_VIEWFIELD"."modifyDate" IS '修改时间';

