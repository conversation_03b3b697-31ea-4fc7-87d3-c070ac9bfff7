package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.validator.constraints.Length;


/**
 * SampleDispose2Test实体
 * <AUTHOR>
 * @version V1.0.0 2021/11/8
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SampleDispose2Test")
 @Data
 public  class SampleDispose2Test implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String sampleDisposeId;
    
    /**
    * 
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("")
	private String testId;
    
    /**
    * 分析项目名称
    */
    @Column(length=100)
    @ApiModelProperty("分析项目名称")
    @Length(message = "分析项目名称{validation.message.length}", max = 100)
	private String redAnalyzeItemName;
    
 }