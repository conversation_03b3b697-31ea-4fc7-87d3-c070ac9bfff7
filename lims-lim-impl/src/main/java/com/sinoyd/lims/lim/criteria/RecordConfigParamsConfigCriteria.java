package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 记录单参数的查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordConfigParamsConfigCriteria extends BaseCriteria implements Serializable {

    /**
     * 记录单的id
     */
    private String recordConfigId;


    /**
     * 记录单参数类型
     */
    private Integer paramsConfigType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and isDeleted=0");
        if (StringUtils.isNotNullAndEmpty(this.recordConfigId)) {
            condition.append(" and objId = :recordConfigId");
            values.put("recordConfigId", this.recordConfigId);
        }
        if (StringUtil.isNotNull(paramsConfigType)) {
            condition.append(" and type = :paramsConfigType");
            values.put("paramsConfigType", this.paramsConfigType);
        }
        return condition.toString();
    }
}
