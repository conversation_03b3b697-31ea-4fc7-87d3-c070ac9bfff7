package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.FixedAssetsCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoFixedProperty;
import com.sinoyd.lims.lim.service.FixedAssetsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 固定资产服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
@Api(tags = "示例: FixedAssets服务")
@RestController
@RequestMapping("api/lim/fixedAssets")
@Validated
public class FixedAssetsController extends BaseJpaController<DtoFixedProperty, String, FixedAssetsService> {

    /**
     * 分页动态条件查询FixedAssets
     *
     * @param fixedAssetsCriteria 条件参数
     * @return RestResponse<List < FixedAssets>>
     */
    @ApiOperation(value = "分页动态条件查询FixedAssets", notes = "分页动态条件查询FixedAssets")
    @GetMapping
    public RestResponse<List<DtoFixedProperty>> findByPage(FixedAssetsCriteria fixedAssetsCriteria) {
        PageBean<DtoFixedProperty> pageBean = super.getPageBean();
        RestResponse<List<DtoFixedProperty>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, fixedAssetsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询固定资产
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询固定资产", notes = "根据id查询固定资产")
    @GetMapping("/{id}")
    public RestResponse<DtoFixedProperty> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoFixedProperty> restResp = new RestResponse<>();
        DtoFixedProperty entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增固定资产
     *
     * @param fixedAssets 固定资产实体
     * @return 新增的固定资产实体
     */
    @ApiOperation(value = "新增固定资产", notes = "新增固定资产")
    @PostMapping()
    public RestResponse<DtoFixedProperty> create(@Validated @RequestBody DtoFixedProperty fixedAssets) {
        RestResponse<DtoFixedProperty> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.save(fixedAssets));
        return restResponse;
    }

    /**
     * 更新固定资产
     *
     * @param fixedAssets 固定资产实体
     * @return 更新后的固定资产实体
     */
    @ApiOperation(value = "更新固定资产", notes = "更新固定资产")
    @PutMapping("")
    public RestResponse<DtoFixedProperty> update(@Validated @RequestBody DtoFixedProperty fixedAssets) {
        RestResponse<DtoFixedProperty> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.update(fixedAssets));
        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);
        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);
        return restResponse;
    }

    /**
     * 固定资产导入模板下载
     */
    @GetMapping("/downLoadFixedAssetsTemplate")
    public void DownLoadFixedAssetsTemplate(HttpServletResponse response){
        Map<String,String> sheetNames = new HashMap<>();
        sheetNames.put("firstName","固定资产");
        service.downLoadExcel(response,sheetNames,"【导入模板】固定资产");
    }

    /**
     * 固定资产导出
     */
    @GetMapping("/export")
    public void export(FixedAssetsCriteria fixedAssetsCriteria,HttpServletResponse response){
        Map<String,String> sheetNames = new HashMap<>();
        sheetNames.put("firstName","固定资产");
        service.export(fixedAssetsCriteria,response,sheetNames,"固定资产");
    }
}
