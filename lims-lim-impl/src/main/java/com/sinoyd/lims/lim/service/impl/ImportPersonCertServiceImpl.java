package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoImportPersonCert;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.repository.lims.*;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.service.ImportPersonCertService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.PersonCertVerifyHandle;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员上岗证导入实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Service
public class ImportPersonCertServiceImpl implements ImportPersonCertService {

    @Autowired
    private PersonCertRepository personCertRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private ImportUtils importUtils;

    @Autowired
    private StandardMethodRepository standardMethodRepository;

    @Autowired
    private StandardMethodDetailRepository standardMethodDetailRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private AnalyzeMethodRepository analyzeMethodRepository;

    @Autowired
    private PersonAbilityRepository personAbilityRepository;

    /**
     * 人员上岗证导入
     *
     * @param file      传入的文件
     * @param objectMap 业务数据Map
     * @return List<DtoPersonCert>
     * @throws Exception 异常抛出
     */
    @Override
    @Transactional
    public List<DtoPersonCert> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);

        // 准备校验所需数据
        List<DtoPerson> persons = personRepository.findAll();

        List<DtoPersonCert> personCertList = personCertRepository.findAll();

        // 获取所有标准方法
        List<DtoStandardMethod> standardMethods = standardMethodRepository.findAll();

        List<DtoTest> testList = testRepository.findAll();
        Map<String, DtoTest> testGroupMap = testList.stream().collect(Collectors.toMap(DtoTest::getId, p -> p));
        // 采样方法
        List<DtoAnalyzeMethod> analyzeMethodList = analyzeMethodRepository.findAll().stream().filter(p->Boolean.TRUE.equals(p.getIsSamplingMethod())).collect(Collectors.toList());
        Map<String, DtoAnalyzeMethod> analyzeMethodMap = analyzeMethodList.stream().collect(Collectors.toMap(DtoAnalyzeMethod::getId, p -> p));

        Map<String, DtoStandardMethod> methodMap = standardMethods.stream()
                .collect(Collectors.toMap(DtoStandardMethod::getMethodId, method -> method));
        List<String> standardMethodIds = standardMethods.stream().map(DtoStandardMethod::getId).collect(Collectors.toList());
        List<DtoStandardMethodDetail> standardMethodDetails = StringUtil.isNotEmpty(standardMethodIds) ?
                standardMethodDetailRepository.findByMethodIdIn(standardMethodIds) : new ArrayList<>();
        Map<String, List<DtoStandardMethodDetail>> methodDetailMap = standardMethodDetails.stream().collect(Collectors.groupingBy(DtoStandardMethodDetail::getMethodId));

        // 创建校验器实例
        PersonCertVerifyHandle verifyHandler = new PersonCertVerifyHandle(persons, methodMap, methodDetailMap, personCertList);
        ExcelImportResult<DtoImportPersonCert> importResult = getExcelData(verifyHandler, file, response);

        // 获取校验成功的导入数据
        List<DtoImportPersonCert> importList = importResult.getList();
        // 移除空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getPersonName()));
        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }

        // 转换为实体并保存
        List<DtoPersonCert> personCerts = importToEntity(importList, standardMethods, standardMethodDetails, testGroupMap,
                analyzeMethodMap, persons);
        if (StringUtil.isNotEmpty(personCerts)) {
            addData(personCerts);
        }

        return personCertRepository.findAll();
    }

    /**
     * 导入数据保存到数据库
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoPersonCert> data) {
        personCertRepository.save(data);
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param file     传入的文件
     * @param response 响应
     * @return List
     */
    @Override
    public ExcelImportResult<DtoImportPersonCert> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param verifyHandler 校验器
     * @param file          传入的文件
     * @param response      响应体
     * @return 导入结果
     */
    @Override
    public ExcelImportResult<DtoImportPersonCert> getExcelData(IExcelVerifyHandler<DtoImportPersonCert> verifyHandler,
                                                               MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        // 设置表头区域
        params.setTitleRows(0);
        // 设置表头开始行
        params.setHeadRows(1);
        // 设置开始工作簿
        params.setStartSheetIndex(0);
        // 设置是否校验
        params.setNeedVerify(true);
        // 设置校验handle
        params.setVerifyHandler(verifyHandler);

        ExcelImportResult<DtoImportPersonCert> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(), DtoImportPersonCert.class, params);

        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "人员上岗证导入错误信息");
            PoiExcelUtils.downLoadExcel("人员上岗证导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 导入数据转换为实体
     *
     * @param importList 导入数据
     * @return 人员上岗证实体列表
     */
    private List<DtoPersonCert> importToEntity(List<DtoImportPersonCert> importList, List<DtoStandardMethod> standardMethods,
                                               List<DtoStandardMethodDetail> standardMethodDetails,
                                               Map<String, DtoTest> testGroupMap,
                                               Map<String, DtoAnalyzeMethod> analyzeMethodMap,
                                               List<DtoPerson> persons) {
        // 根据证书编号分组
        Map<String, List<DtoImportPersonCert>> importPersonCertGroup = importList.stream().collect(Collectors.groupingBy(DtoImportPersonCert::getCertCode));
        List<DtoPersonCert> addPersonCertList = new ArrayList<>();
        List<DtoPersonAbility> addPersonAbiliytList = new ArrayList<>();
        for (Map.Entry<String, List<DtoImportPersonCert>> entry : importPersonCertGroup.entrySet()) {
            String certCode = entry.getKey();
            List<DtoImportPersonCert> importPersonCerts = entry.getValue();
            DtoImportPersonCert importPersonCert = importPersonCerts.get(0);
            DtoPersonCert personCert = new DtoPersonCert();
            Optional<DtoPerson> optionalDtoPerson = persons.stream().filter(p -> p.getCName().equals(importPersonCert.getPersonName())).findFirst();
            String peronId = optionalDtoPerson.isPresent() ? optionalDtoPerson.get().getId() : UUIDHelper.GUID_EMPTY;
            personCert.setPersonId(peronId);
            personCert.setCertCode(certCode);
            personCert.setCertName(certCode);
            personCert.setIssueCertTime(importUtils.stringToDateAllFormat(importPersonCert.getAchieveDate()));
            personCert.setCertEffectiveTime(importUtils.stringToDateAllFormat(importPersonCert.getCertEffectiveTime()));
            Set<String> certTypeList = new HashSet<>();
            List<String> methodIds = importPersonCerts.stream().map(DtoImportPersonCert::getMethodId).distinct().collect(Collectors.toList());
            Map<String, DtoStandardMethod> standardMethodMap = standardMethods.stream().collect(Collectors.toMap(DtoStandardMethod::getId, p -> p));
            List<String> standardMethodIds = standardMethods.stream().filter(p -> methodIds.contains(p.getMethodId())).map(DtoStandardMethod::getId).collect(Collectors.toList());
            List<DtoStandardMethodDetail> standardMethodDetailList = standardMethodDetails.stream().filter(p -> standardMethodIds.contains(p.getMethodId())).collect(Collectors.toList());
            for (DtoStandardMethodDetail methodDetail : standardMethodDetailList) {
                DtoPersonAbility personAbility = new DtoPersonAbility();
                personAbility.setPersonId(peronId);
                personAbility.setPersonCertId(personCert.getId());
                personAbility.setAchieveDate(personCert.getIssueCertTime());
                personAbility.setCertEffectiveTime(personCert.getCertEffectiveTime());
                if (testGroupMap.containsKey(methodDetail.getObjectId())) {
                    DtoTest test = testGroupMap.get(methodDetail.getObjectId());
                    personAbility.setSampleTypeId(test.getSampleTypeId());
                    personAbility.setTestId(test.getId());
                    personAbility.setAbilityType("6");
                    certTypeList.add("6");
                } else if (analyzeMethodMap.containsKey(methodDetail.getObjectId())) {
                    DtoAnalyzeMethod analyzeMethod = analyzeMethodMap.get(methodDetail.getObjectId());
                    personAbility.setSampleTypeId(analyzeMethod.getSampleTypeId());
                    personAbility.setSamplingMethodId(analyzeMethod.getId());
                    personAbility.setAbilityType(analyzeMethod.getId());
                    personAbility.setAbilityType("7");
                    certTypeList.add("7");
                    personAbility.setRedAnalyzeItemName("");
                    DtoStandardMethod standardMethod = standardMethodMap.get(methodDetail.getMethodId());
                    if(standardMethod!=null){
                        importPersonCerts.stream().filter(p->standardMethod.getMethodId().equals(p.getMethodId()))
                                .findFirst().ifPresent(p->personAbility.setRedAnalyzeItemName(p.getRedAnalyzeItemName()));
                    }
                }
                addPersonAbiliytList.add(personAbility);
            }
            personCert.setCertType(certTypeList.stream().sorted().collect(Collectors.joining(",")));
            addPersonCertList.add(personCert);
        }
        if (StringUtil.isNotEmpty(addPersonAbiliytList)) {
            personAbilityRepository.save(addPersonAbiliytList);
        }

        return addPersonCertList;
    }

    /**
     * 下载模板
     *
     * @param response   响应流
     * @param sheetNames 需要赋值的sheet名
     * @param fileName   文件名
     */
    @Override
    public void downLoadExcel(HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        // 获取人员上岗证空数据
        List<DtoImportPersonCert> importPersonCerts = new ArrayList<>();

        // 获取需要下载的工作单
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoImportPersonCert.class, importPersonCerts);
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }
}
