package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoDocAuthorityConfig;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 文件夹权限仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/3
 * @since V100R001
 */
public interface DocAuthorityConfigRepository extends IBaseJpaPhysicalDeleteRepository<DtoDocAuthorityConfig, String> {


    /**
     * 根据objectId，authCode查询
     *
     * @param objectId 文件夹id
     * @param authCode   权限编码
     * @return 返回权限列表
     */
    List<DtoDocAuthorityConfig> findByObjectIdAndAuthCode(String objectId, String authCode);

    /**
     * 批量删除当前数据的权限
     *
     * @param ids ids
     * @return 返回删除行数
     */
    @Transactional
    Integer deleteByIdIn(List<String> ids);

    /**
     * 根据文件夹id 权限id,用户id查询
     * @param objectId 文件夹id
     * @param authCode 权限编码
     * @param userId 用户id
     * @return  权限配置
     */
    Integer countByObjectIdAndAuthCodeAndUserId(String objectId, String authCode, String userId);

    /**
     * objectIds，authCode,userId查询
     *
     * @param objectIds 文件夹id
     * @param authCode   权限编码集合
     * @param userId 用户id
     * @return 返回权限列表
     */
    List<DtoDocAuthorityConfig> findByObjectIdInAndAuthCodeAndUserId(List<String> objectIds, String authCode, String userId);

    /**
     * 根据objectId，authId查询
     *
     * @param objectId 文件夹id
     * @param authCodes   权限编码集合
     * @return 返回权限列表
     */
    List<DtoDocAuthorityConfig> findByObjectIdAndAuthCodeIn(String objectId, List<String> authCodes);

    /**
     * 根据文件夹id查询
     *
     * @param objectIds 文件夹id
     * @return 返回权限列表
     */
    List<DtoDocAuthorityConfig> findByObjectIdInAndAuthorityListId(List<String> objectIds, String authorityListId);

    /**
     * 根据文件夹id查询
     *
     * @param objectId 文件夹id
     * @return 返回权限列表
     */
    List<DtoDocAuthorityConfig> findByObjectId(String objectId);

    /**
     * 根据条件删除
     *
     * @param objectId  文件夹id
     * @param authCodes 文件权限编码
     * @param userId    用户id
     * @return
     */
    Integer deleteByObjectIdAndAuthCodeInAndUserIdNot(String objectId, List<String> authCodes, String userId);

    /**
     * 根据权限id删除配置
     *
     * @param ids  权限id集合
     * @return Integer
     */
    Integer deleteByAuthorityListIdInAndUserIdNot(List<String> ids, String userId);
}
