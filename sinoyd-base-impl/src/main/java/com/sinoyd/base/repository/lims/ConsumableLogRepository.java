package com.sinoyd.base.repository.lims;

import com.sinoyd.base.dto.lims.DtoConsumableLog;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;

/**
 * 消耗品领用仓储
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
public interface ConsumableLogRepository extends IBaseJpaPhysicalDeleteRepository<DtoConsumableLog, String>
{
    /**
     * 根据消耗品id获取消耗品领用信息
     * @param consumableIds 消耗品id
     * @return 消耗品库存
     */
    List<DtoConsumableLog> findByConsumableIdIn(List<String> consumableIds);

    /**
     * 根据id获取消耗品领用信息
     * @param consumablePickId 领用id
     * @return 消耗品库存
     */
    List<DtoConsumableLog> findAllByConsumablePickId(String consumablePickId);

    /**
     * 根据id获取消耗品领用信息
     * @param consumablePickId 领用id
     * @return 消耗品库存
     */
    List<DtoConsumableLog> findAllByConsumablePickIdIn(List<String> consumablePickId);
}