package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataAnalyseDataVO;
import com.sinoyd.lims.foreign.DataCurveDetailVO;
import com.sinoyd.lims.foreign.DataCurveVO;
import com.sinoyd.lims.foreign.DataReportFolderSortInfoVO;
import com.sinoyd.lims.foreign.service.DataCurveDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 曲线明细详情接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataCurveDetailServiceImpl extends AbsQueryDataServiceImpl<DataCurveDetailVO> implements DataCurveDetailService {


    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t2.standardCurveId,t1.analyseCode,t1.addVolume," +
                "t1.addAmount,t1.absorbance,t1.lessBlankAbsorbance,t1.absorbanceB," +
                "t1.relativeDeviation,t1.aValueTTZ,t1.aValueTSF,t1.orgId,t1.domainId ");
        sql.append(" from TB_PRO_WorkSheetCalibrationCurveDetail t1");
        sql.append(" INNER JOIN TB_PRO_WorkSheetCalibrationCurve t2 ON t1.workSheetCalibrationCurveId = t2.id");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND (t1.modifyDate > :syncTime or t2.modifyDate > :syncTime) ", "syncTime");
        super.appendCondition(sql, map, "AND t2.workSheetId in (:workSheetIds)", "workSheetIds");
    }

    @Override
    protected RowMapper<DataCurveDetailVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataCurveDetailVO dataCurveDetailVO = new DataCurveDetailVO();
            dataCurveDetailVO.setId(UUIDHelper.NewID());
            dataCurveDetailVO.setCurveId(rs.getString("standardCurveId"));
            dataCurveDetailVO.setAnalyseCode(rs.getString("analyseCode"));
            dataCurveDetailVO.setAddVolume(rs.getString("addVolume"));
            dataCurveDetailVO.setAddAmount(rs.getString("addAmount"));
            dataCurveDetailVO.setAbsorbance(rs.getString("absorbance"));
            dataCurveDetailVO.setLessBlankAbsorbance(rs.getString("lessBlankAbsorbance"));
            dataCurveDetailVO.setAbsorbanceB(rs.getString("absorbanceB"));
            dataCurveDetailVO.setRelativeDeviation(rs.getString("relativeDeviation"));
            dataCurveDetailVO.setAValueTTZ(rs.getString("aValueTTZ"));
            dataCurveDetailVO.setAValueTSF(rs.getString("aValueTSF"));
            dataCurveDetailVO.setOrgId(rs.getString("orgId"));
            dataCurveDetailVO.setDomainId(rs.getString("domainId"));
            dataCurveDetailVO.setSyncTime(new Date());
            return dataCurveDetailVO;
        };
    }
}
