package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentUseRecord;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoParamsCheckTemp;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 现场任务提交
 *
 * <AUTHOR>
 */
@Component(IFileNameConstant.SubmitStrategyKey.SUBMIT_RECEIVE_TASK)
public class ReceiveTaskSubmitStrategy extends AbsSubmitRestrictStrategy {
    @Override
    public List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        String recordId = objMap.toString();
        DtoReceiveSampleRecord record = receiveSampleRecordService.findOne(recordId);
        //校验提交送样单状态
        restrictVoList.add(super.checkStatus(status, record.getInfoStatus().toString(), EnumPRO.EnumRestrictItem.提交状态验证));
        //排除比对样 -- 质控和替代
        List<DtoSample> sampleList = sampleService.findByReceiveId(recordId).stream()
                .filter(p -> !p.getIsDeleted() &&
                        !EnumPRO.EnumSampleCategory.比对评价样.getValue().equals(p.getSampleCategory())).collect(Collectors.toList());
        List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        Set<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toSet());
        List<DtoAnalyseData> analyseDataList = analyseDataService.findDataBySampleIds(sampleIds).stream()
                .filter(p -> !p.getIsOutsourcing() && (!p.getIsDeleted() || p.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.作废.getValue())))
                .collect(Collectors.toList());
        List<DtoParamsData> paramsDataList = paramsDataService.findBySampleIds(sampleIdList);
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
        List<DtoTest> testList = testService.findAll(testIds);
        //总称测试项目
        Set<String> parentIds = testList.stream().map(DtoTest::getParentId)
                .filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).collect(Collectors.toSet());
        if (parentIds.size() > 0) {
            testIds.addAll(parentIds);
        }
        List<DtoTestQCRemindTemp> config2TestList = testQCRemindConfig2TestService.findByTestIds(testIds);
        Boolean isRecord = Boolean.FALSE;
        if (EnumPRO.EnumReceiveInfoStatus.新建.getValue().equals(record.getInfoStatus())
                || EnumPRO.EnumReceiveInfoStatus.信息登记中.getValue().equals(record.getInfoStatus())) {
            isRecord = Boolean.TRUE;
        }
        List<DtoReceiveSubSampleRecord> subRecords = receiveSubSampleRecordRepository.findByReceiveIdIn(Collections.singletonList(recordId));
        subRecords = subRecords.stream().filter(p -> (p.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) > 0).collect(Collectors.toList());
        //获取领样单id与送样单id的关联
        Map<String, String> subMap = subRecords.stream().collect(Collectors.toMap(DtoReceiveSubSampleRecord::getId, DtoReceiveSubSampleRecord::getReceiveId));
        List<DtoInstrumentUseRecord> useRecordList = subMap.size() > 0 ? instrumentUseRecordService.findByObjectIdInAndObjectType
                (new ArrayList<>(subMap.keySet()), EnumLIM.EnumEnvRecObjType.现场分析.getValue()) : new ArrayList<>();
        if (isRecord) {
            //检查送样人员 -ok
//            restrictVoList.add(checkSender(record));
            //参数必填验证 -ok
            restrictVoList.add(checkParamsData(sampleList, analyseDataList, paramsDataList));
            //出证必填验证 -ok
            restrictVoList.add(checkTestValue(analyseDataList));
            //仪器使用记录 -ok
            //将仪器使用记录的领样单关联id改为送样单id
            for (DtoInstrumentUseRecord useRecord : useRecordList) {
                useRecord.setObjectId(subMap.getOrDefault(useRecord.getObjectId(), ""));
            }
            restrictVoList.add(checkInstrumentUseRecord(analyseDataList, useRecordList));
            //是否添加样品 -ok
            restrictVoList.add(checkSample(sampleList, analyseDataList));
            //是否存在空的样品编号
            restrictVoList.add(checkSampleCode(sampleList));
        }
        // 仪器判定冲突
        restrictVoList.add(checkInstrumentConflict(useRecordList, recordId));
        //是否生成采样单 -ok
        restrictVoList.add(checkSampling(recordId));
        //质控比例 -ok
        config2TestList = config2TestList.stream().filter(p ->
                EnumLIM.EnumQCGrade.外部质控.getValue().equals(p.getQcGrade())).collect(Collectors.toList());
        if (config2TestList.size() > 0) {
            restrictVoList.add(super.checkRemind(recordId));
        }
        //质控比例配置 -ok
        restrictVoList.add(checkRemindConfig(config2TestList, testList, EnumPRO.EnumRestrictItem.现场质控比例配置));
        //质控评价 -ok
        String subId = subRecords.stream().map(DtoReceiveSubSampleRecord::getId).findFirst().orElse(UUIDHelper.GUID_EMPTY);
        restrictVoList.addAll(super.checkControlEvaluate(subId, EnumPRO.EnumRestrictItem.现场质控评价, testList,
                EnumPRO.EnumRestrictItem.现场质控限制配置, analyseDataList));
        //电子签名是否存在 -ok
        List<String> personIds = new ArrayList<>();
        personIds.add(PrincipalContextUser.getPrincipal().getUserId());
        if (isRecord) {
            personIds = record.getSamplingPersonIds();
            if (!UUIDHelper.GUID_EMPTY.equals(record.getSenderId())) {
                personIds.add(record.getSenderId());
            }
        }
        // 采样上岗证 -ok
        restrictVoList.add(this.checkSamplingAbility(record,testList));
        restrictVoList.add(super.checkSigUrl(personIds, EnumPRO.EnumRestrictItem.现场电子签名));
        restrictVoList.add(this.checkTime(record));
        return restrictVoList;
    }

    /**
     * 参数必填验证
     *
     * @param sampleList     样品集合
     * @param paramsDataList 参数集合
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkParamsData(List<DtoSample> sampleList,
                                                List<DtoAnalyseData> analyseDataList,
                                                List<DtoParamsData> paramsDataList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        Set<String> sampleTypeIds = sampleList.stream().map(DtoSample::getSampleTypeId).collect(Collectors.toSet());
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<String> msgList = new ArrayList<>();
        for (String sampleTypeId : sampleTypeIds) {
            List<DtoParamsConfig> paramsConfigList = paramsConfigService.findByTypeIdAndTestIds(sampleTypeId, testIds);
            //分析项目配置
            List<DtoParamsConfig> itemConfigs = paramsConfigList.stream()
                    .filter(p -> p.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue()))
                    .collect(Collectors.toList());
            Map<String, DtoParamsConfig> itemCfgMap = itemConfigs.stream()
                    .collect(Collectors.toMap(DtoParamsConfig::getId, cfg -> cfg));
            List<DtoSample> samples = sampleList.stream()
                    .filter(p -> p.getSampleTypeId().equals(sampleTypeId)).collect(Collectors.toList());
            if (samples.size() > 0) {
                for (DtoSample sample : samples) {
                    List<String> paramsNames = new ArrayList<>();
                    DtoParamsCheckTemp temp = receiveSampleRecordService.findParamsCheckTemp(paramsConfigList);
                    Boolean[][] flagArr = temp.getFlagArr();
                    List<DtoParamsData> pdList = paramsDataList.stream()
                            .filter(p -> p.getObjectId().equals(sample.getId())).collect(Collectors.toList());

                    //获取该样品下没有的指标，将之全部置为false
                    List<String> analyseItemIds = new ArrayList<>(temp.getItemMap().keySet());
                    analyseItemIds.removeAll(analyseDataList.stream().filter(p -> p.getSampleId().equals(sample.getId()))
                            .map(DtoAnalyseData::getAnalyseItemId).distinct().collect(Collectors.toList()));
                    for (String analyseItemId : analyseItemIds) {
                        if (!UUIDHelper.GUID_EMPTY.equals(analyseItemId)) {
                            for (int i = 0; i < temp.getCfgMap().size(); i++) {
                                flagArr[i][temp.getItemMap().get(analyseItemId)] = false;
                            }
                        }
                    }

                    for (DtoParamsData pd : pdList) {
                        //遍历参数数据，若参数为空且必填，则为true，其余情况均为false
                        if (pd.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY) && temp.getCfgMap().containsKey(pd.getParamsConfigId())) {
                            flagArr[temp.getCfgMap().get(pd.getParamsConfigId())][0] = !StringUtils.isNotNullAndEmpty(pd.getParamsValue()) &&
                                    flagArr[temp.getCfgMap().get(pd.getParamsConfigId())][0];
                        } else if (!pd.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY) && itemCfgMap.containsKey(pd.getParamsConfigId())) {
                            DtoParamsConfig pc = itemCfgMap.get(pd.getParamsConfigId());
                            flagArr[temp.getCfgMap().get(pc.getParentId())][temp.getItemMap().get(pc.getAnalyzeItemId())] =
                                    !StringUtils.isNotNullAndEmpty(pd.getParamsValue()) &&
                                            flagArr[temp.getCfgMap().get(pc.getParentId())][temp.getItemMap().get(pc.getAnalyzeItemId())];
                        }
                    }
                    for (int row = 0; row < temp.getCfgMap().size(); row++) {
                        //遍历二维数组，值为true则表示未填写参数，多张单子的时候直接跳出并移除该送样单，单张单子需拼装返回值
                        for (int column = 0; column < temp.getItemMap().size(); column++) {
                            if (flagArr[row][column]) {
                                paramsNames.add(temp.getParamsNameArr()[row][column]);
                            }
                        }
                    }

                    if (paramsNames.size() > 0) {
                        String msg = String.format("%s参数未填：%s", sample.getCode(), String.join(",", paramsNames));
                        msgList.add(msg);
                    }
                }
            }
        }
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.参数必填验证.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.参数必填验证.getModuleName());
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 出证必填验证
     *
     * @param analyseDataList 样品集合
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkTestValue(List<DtoAnalyseData> analyseDataList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        //排除数据
        analyseDataList = analyseDataList.stream().filter(p -> p.getIsCompleteField() && !p.getIsOutsourcing()
                && !p.getIsSamplingOut() && (!StringUtils.isNotNullAndEmpty(p.getTestOrignValue())
                || !StringUtils.isNotNullAndEmpty(p.getTestValue()))).collect(Collectors.toList());
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.现场出证必填验证.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.现场出证必填验证.getModuleName());
        if (analyseDataList.size() > 0) {
            Set<String> itemName = analyseDataList.stream().map(DtoAnalyseData::getRedAnalyzeItemName).collect(Collectors.toSet());
            restrictVo.setExceptionOption(String.format("%s数据未录入完整", String.join("、", itemName)));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 仪器使用记录验证
     *
     * @param analyseDataList 样品集合
     * @param useRecordList   使用记录
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkInstrumentUseRecord(List<DtoAnalyseData> analyseDataList, List<DtoInstrumentUseRecord> useRecordList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        Set<String> testIdList = analyseDataList.stream().filter(DtoAnalyseData::getIsCompleteField).map(DtoAnalyseData::getTestId).collect(Collectors.toSet());
        if (testIdList.size() > 0) {
            List<DtoTest> testList = testService.findAllDeleted(testIdList).stream().filter(DtoTest::getIsInsUseRecord).collect(Collectors.toList());
            testIdList = testList.stream().map(DtoTest::getId).collect(Collectors.toSet());
            Set<String> recordTestList = useRecordList.stream().map(DtoInstrumentUseRecord::getTestIds)
                    .filter(StringUtils::isNotNullAndEmpty).collect(Collectors.toSet());
            Set<String> useTestIds = Arrays.stream(String.join(",", recordTestList).split(",")).collect(Collectors.toSet());
            testIdList.removeAll(useTestIds);
        }
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.现场仪器使用记录.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.现场仪器使用记录.getModuleName());
        if (testIdList.size() > 0) {
            restrictVo.setExceptionOption("未添加仪器使用记录");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }


    /**
     * 判定仪器冲突
     *
     * @param useRecordList 现场分析仪器集合
     * @param recordId      送样单id
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkInstrumentConflict(List<DtoInstrumentUseRecord> useRecordList, String recordId) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        // 现场采样仪器
        useRecordList.addAll(instrumentUseRecordService.findByObjectIdAndObjectType(recordId, EnumLIM.EnumEnvRecObjType.采样.getValue()));
        List<String> instrumentIds = useRecordList.stream().map(DtoInstrumentUseRecord::getInstrumentId).distinct().collect(Collectors.toList());
        List<DtoInstrumentUseRecord> instrumentUseRecordList = instrumentUseRecordRepository.findByInstrumentIdIn(instrumentIds);
        List<DtoInstrumentUseRecord> checkResult = new ArrayList<>();
        for (DtoInstrumentUseRecord useRecord : useRecordList) {
            List<DtoInstrumentUseRecord> useRecords = instrumentUseRecordList.stream().filter(p ->
                    !p.getObjectId().equals(useRecord.getObjectId())
                            && p.getObjectType().equals(useRecord.getObjectType())
                            && !p.getStartTime().after(useRecord.getEndTime())
                            && !p.getEndTime().before(useRecord.getStartTime())
                            && !p.getUsePersonId().equals(useRecord.getUsePersonId())).collect(Collectors.toList());
            checkResult.addAll(useRecords);
        }
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.现场仪器判定冲突.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.现场仪器判定冲突.getModuleName());
        if (checkResult.size() > 0) {
            List<String> receiveIds = checkResult.stream().map(DtoInstrumentUseRecord::getObjectId).distinct().collect(Collectors.toList());
            // 可能为领样单关联，先查询领样单。
            List<DtoReceiveSubSampleRecord> receiveSubSampleRecords = receiveSubSampleRecordRepository.findAll(receiveIds);
            receiveIds.addAll(receiveSubSampleRecords.stream().map(DtoReceiveSubSampleRecord::getReceiveId).distinct().collect(Collectors.toList()));
            List<DtoReceiveSampleRecord> receiveSampleRecords = receiveSampleRecordService.findAll(receiveIds);

            String recordCode = receiveSampleRecords.stream().map(DtoReceiveSampleRecord::getRecordCode).map(p -> "(" + p + ")").collect(Collectors.joining("、"));
            restrictVo.setExceptionOption("仪器使用记录重合，关联送样单号：" + recordCode);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 是否存在样品
     *
     * @param sampleList 样品集合
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkSample(List<DtoSample> sampleList, List<DtoAnalyseData> analyseDataList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        Set<String> existSampleIds = analyseDataList.stream().map(DtoAnalyseData::getSampleId).collect(Collectors.toSet());
        List<String> msgList = new ArrayList<>();
        if (StringUtil.isNull(sampleList) || sampleList.size() == 0 || existSampleIds.size() < sampleList.size()) {
            String msg = "该送样单下没有添加样品";
            if (existSampleIds.size() < sampleList.size()) {
                for (DtoSample sample : sampleList) {
                    if (!existSampleIds.contains(sample.getId())) {
                        msg = String.format("样品%s未配置测试项目", sample.getCode());
                        msgList.add(msg);
                    }
                }
            } else {
                msgList.add(msg);
            }
        }
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.是否添加样品.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.是否添加样品.getModuleName());
        if (msgList.size() > 0) {
            restrictVo.setExceptionOption(String.join(";", msgList));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 检查送样人员
     *
     * @param record 送样单
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkSender(DtoReceiveSampleRecord record) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        String userId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getUserId() : "";
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.核查送样人.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.核查送样人.getModuleName());
        List<String> personIds = record.getSamplingPersonIds();
        personIds.add(record.getSenderId());
        //送样人和采样人 判断
        if (!personIds.contains(userId)) {
            restrictVo.setExceptionOption("无法提交其他送样人的送样单");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 是否存在采样单
     *
     * @param recordId 送样单Id
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkSampling(String recordId) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.采样单.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.采样单.getModuleName());
        if (super.checkDocument(recordId, BaseCodeHelper.DOCUMENT_SAMPLE_RECORD).size() == 0) {
            restrictVo.setExceptionOption("未生成采样单");
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 样品编号验证
     *
     * @param sampleList 样品集合
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkSampleCode(List<DtoSample> sampleList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        //样品编号
        sampleList = sampleList.stream().filter(p -> !StringUtil.isNotEmpty(p.getCode())).collect(Collectors.toList());
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.是否空样品编号.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.是否空样品编号.getModuleName());
        if (sampleList.size() > 0) {
            restrictVo.setExceptionOption("存在样品编号为空的样品");
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }

    /**
     * 采样上岗证检查
     *
     * @param record     送样单
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkSamplingAbility(DtoReceiveSampleRecord record,List<DtoTest> testList) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.采样上岗证检测.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.采样上岗证检测.getModuleName());
        List<DtoSamplingPersonConfig> samplingPersonConfigList = samplingPersonConfigRepository.findByObjectTypeAndObjectId(EnumPRO.EnumSamplingType.送样单.getValue(), record.getId());
        List<String> samplingPersonIds = samplingPersonConfigList.stream().map(DtoSamplingPersonConfig::getSamplingPersonId).collect(Collectors.toList());
        samplingPersonIds.add(record.getSenderId());
        samplingPersonIds = samplingPersonIds.stream().distinct().collect(Collectors.toList());
        List<DtoPerson> personList = personRepository.findByIdIn(samplingPersonIds);
        List<String> sampleTypeIds = testList.stream().map(DtoTest::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll(sampleTypeIds);

        // 上岗证
        List<DtoPersonAbility> samplingAbilityList = personAbilityRepository.findByPersonIdIn(samplingPersonIds);
        List<String> exceptionOption = new ArrayList<>();
        for (String samplingPersonId : samplingPersonIds){
            DtoPerson person = personList.stream().filter(p -> p.getId().equals(samplingPersonId)).findFirst().orElse(new DtoPerson());
            for (DtoTest test:testList){
                DtoSampleType sampleType = sampleTypes.stream().filter(p -> p.getId().equals(test.getSampleTypeId())).findFirst().orElse(new DtoSampleType());
                //现场指标判断有无同指标的分析上岗证明细
                if(test.getIsCompleteField()){
                    if(samplingAbilityList.stream().noneMatch(v->person.getId().equals(v.getPersonId())&&test.getId().equals(v.getTestId())&&
                            EnumLIM.EnumPersonCertType.分析.getValue().equals(v.getAbilityType()))){
                        exceptionOption.add(String.format("%s无【%s】检测上岗证", person.getCName(),test.getRedAnalyzeItemName()));
                    }
                }else{
                    //实验室指标匹配判断有无同大类的采样上岗证明细
                    if(samplingAbilityList.stream().noneMatch(v->person.getId().equals(v.getPersonId())&&test.getSampleTypeId().equals(v.getSampleTypeId())&&
                            EnumLIM.EnumPersonCertType.采样.getValue().equals(v.getAbilityType()))){
                        exceptionOption.add(String.format("%s无【%s】采样上岗证", person.getCName(),sampleType.getTypeName()));
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(exceptionOption)) {
            restrictVo.setExceptionOption(exceptionOption.stream().distinct().sorted().collect(Collectors.joining("、")));
            restrictVo.setIsUnusual(false);
        }
        return restrictVo;
    }

    /**
     * 检查送样日期不能早于采样日期
     *
     * @param record 送样单
     * @return 返回验证结果
     */
    private DtoSubmitRestrictVo checkTime(DtoReceiveSampleRecord record) {
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.送样日期验证.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.送样日期验证.getModuleName());
        //送样日期不能早于采样日期
        if (record.getSendTime().before(record.getSamplingTime())) {
            restrictVo.setExceptionOption("送样日期不能早于采样日期");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        return restrictVo;
    }
}
