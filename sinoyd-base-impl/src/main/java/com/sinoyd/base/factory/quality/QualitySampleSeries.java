package com.sinoyd.base.factory.quality;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.dto.customer.DtoQualityConfig;
import com.sinoyd.base.dto.vo.OrderReviseVO;
import com.sinoyd.base.factory.task.QualityControlKind;
import com.sinoyd.frame.service.CalculationService;

import java.util.List;
import java.util.Map;

/**
 * 洗涤剂
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/30
 */
public class QualitySampleSeries extends QualityControlKind {
    @Override
    public Integer qcTypeValue() {
        return 32;
    }

    @Override
    public String qcTypeName() {
        return "串联样";
    }

    @Override
    public String getSampleProperty(Integer qcGrade) {
        if (qcGrade == 1) {
            return "串联样";
        } else {
            return "室内串联样";
        }
    }

    @Override
    public String getFormt(Map<String, String> map) {
        return "增加了串联样qcCode，原样为oldCode。";
    }

    @Override
    public String getRedFolderName(String folderName, Integer qcGrade) {
        if (qcGrade == 1) {
            return "串联样";
        } else {
            return "室内串联样";
        }
    }

    @Override
    public DtoQualityConfig getQualityConfig(){
        return getConfig("SampleSeries");
    }

    @Override
    public DtoQualityConfig getQualityConfig(List<OrderReviseVO> orderReviseVOList) {
        return getConfig("SampleSeries", orderReviseVOList);
    }

    @Override
    public Boolean deviationQualified(DtoQualityControlLimit controlLimit, String value) {
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        return deviationQualified(controlLimit, value, calculationService);
    }
}