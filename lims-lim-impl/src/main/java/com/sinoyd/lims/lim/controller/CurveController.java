package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.CurveCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoCurveTemp;
import com.sinoyd.lims.lim.dto.lims.DtoCurve;
import com.sinoyd.lims.lim.service.CurveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.ConstraintViolationException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 标准曲线服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/27
 * @since V100R001
 */
@Api(tags = "示例: Curve服务")
@RestController
@RequestMapping("api/lim/curve")
public class CurveController extends BaseJpaController<DtoCurve, String, CurveService> {

    /**
     * 分页动态条件查询标准曲线
     *
     * @param curveCriteria 条件参数
     * @return RestResponse<List < DtoCurve>>
     */
    @ApiOperation(value = "分页动态条件查询标准曲线", notes = "分页动态条件查询标准曲线")
    @GetMapping
    public RestResponse<List<DtoCurve>> findByPage(CurveCriteria curveCriteria) {
        PageBean<DtoCurve> pageBean = super.getPageBean();
        RestResponse<List<DtoCurve>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, curveCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键id查询标线信息
     *
     * @param id 标线id
     */
    @ApiOperation(value = "按主键id查询标线信息", notes = "按主键id查询标线信息")
    @GetMapping("/{id}")
    public RestResponse<List<DtoCurveTemp>> find(@PathVariable String id) {
        RestResponse<List<DtoCurveTemp>> restResp = new RestResponse<>();
        restResp.setData(service.findList(id));
        return restResp;
    }

    /**
     * 新增标线
     *
     * @param dtoList 标线
     * @return RestResponse<List < DtoCurveTemp>>
     */
    @ApiOperation(value = "新增标线", notes = "新增标线")
    @PostMapping
    public RestResponse<List<DtoCurveTemp>> createList(@RequestBody List<DtoCurveTemp> dtoList) {
        RestResponse<List<DtoCurveTemp>> restResponse = new RestResponse<>();
        List<DtoCurveTemp> dataList = new ArrayList<>();
        try {
            dataList = service.saveList(dtoList);
        } catch (ConstraintViolationException e) {
            if (StringUtil.isNotEmpty(e.getConstraintViolations())) {
                String msg = (new ArrayList<>(e.getConstraintViolations())).get(0).getMessage();
                throw new BaseException(msg);
            }
        }
        restResponse.setData(dataList);
        return restResponse;
    }

    /**
     * 修改标线
     *
     * @param dtoList 标线
     * @return RestResponse<DtoCurveTemp>
     */
    @ApiOperation(value = "修改标线", notes = "修改标线")
    @PutMapping
    public RestResponse<List<DtoCurveTemp>> updateList(@RequestBody List<DtoCurveTemp> dtoList) {
        RestResponse<List<DtoCurveTemp>> restResponse = new RestResponse<>();
        restResponse.setData(service.updateList(dtoList));
        return restResponse;
    }

    /**
     * 计算曲线
     *
     * @param dto 曲线信息
     * @return RestResponse<DtoCurveTemp>
     */
    @ApiOperation(value = "计算曲线", notes = "计算曲线")
    @PostMapping("/calculate")
    public RestResponse<DtoCurveTemp> calculate(@RequestBody DtoCurveTemp dto) {
        RestResponse<DtoCurveTemp> restResponse = new RestResponse<>();
        restResponse.setData(service.calculate(dto));
        return restResponse;
    }

    /**
     * 刪除标准曲线
     *
     * @param id 标线id
     * @return RestResponse<String>
     */
    @ApiOperation(value = "刪除标准曲线", notes = "刪除标准曲线")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable(name = "id") String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResponse.setCount(count);
        return restResponse;
    }

    /**
     * "根据id批量删除刪除标准曲线
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除刪除标准曲线", notes = "根据id批量删除刪除标准曲线")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);
        return restResponse;
    }

    @ApiOperation(value = "根据曲线id复制一条曲线", notes = "根据曲线id复制一条曲线")
    @PostMapping("/copy")
    public RestResponse<List<DtoCurveTemp>> copyList(@RequestBody String curveId) {
        RestResponse<List<DtoCurveTemp>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.copyList(curveId));
        return restResponse;
    }

    @ApiOperation(value = "获取上次填写的最新曲线信息", notes = "获取上次填写的最新曲线信息")
    @GetMapping("/getLastNewCurveInfo")
    public RestResponse<DtoCurve> getLastNewCurveInfo(@RequestParam(name = "testId") String testId) {
        RestResponse<DtoCurve> response = new RestResponse<>();
        response.setData(service.getLastNewCurveInfo(testId));
        return response;
    }


    /**
     * 获取上次填最新的曲线修约信息
     * @param testId 测试项目id
     * @return 修约不为-1 的曲线信息
     */
    @ApiOperation(value = "获取上次填最新的曲线修约信息", notes = "获取上次填最新的曲线修约信息")
    @GetMapping("/getCurveMost")
    public RestResponse<DtoCurve> getCurveMost(@RequestParam(name = "testId") String testId) {
        RestResponse<DtoCurve> response = new RestResponse<>();
        response.setData(service.getCurveMost(testId));
        return response;
    }

    @ApiOperation(value = "获取修约数据", notes = "获取修约数据")
    @PostMapping("/getDecimal")
    public RestResponse<String> getDecimal(@RequestBody Map<String,Object> map) {
        RestResponse<String> response = new RestResponse<>();
        response.setData(service.getDecimal(map));
        return response;
    }


}