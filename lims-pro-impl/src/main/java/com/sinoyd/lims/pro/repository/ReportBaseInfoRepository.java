package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoReportBaseInfo;

import java.util.List;


/**
 * 电子报告基础数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
public interface ReportBaseInfoRepository extends IBaseJpaRepository<DtoReportBaseInfo, String> {

    /**
     * 按报告id查询基础数据
     *
     * @param reportId 报告id
     * @return 返回相应的报告基础数据
     */
    DtoReportBaseInfo findByReportId(String reportId);

    /**
     * 按报告id列表查询基础数据
     *
     * @param reportIdList 报告id列表
     * @return 返回相应的报告基础数据
     */
    List<DtoReportBaseInfo> findByReportIdIn(List<String> reportIdList);
}