package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * 质控样品数量的查询条件
 * <AUTHOR>
 * @version V1.0.0 2020/02/11
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QCSampleStatisticsCriteria extends BaseCriteria implements Serializable {

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 分析开始时间
     */
    private String startTime;

    /**
     * 分析结束时间
     */
    private String endTime;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 质控类型
     */
    private Integer qcGrade;

    /**
     * 质控等级
     */
    private Integer qcType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and exists (select 1 from DtoAnalyseData b where b.isDeleted = 0 and b.sampleId = a.id ");

        if (StringUtils.isNotNullAndEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and b.analyzeTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtils.isNotNullAndEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            to = c.getTime();
            condition.append(" and b.analyzeTime < :endTime");
            values.put("endTime", to);
        }
        if (StringUtils.isNotNullAndEmpty(testId) && !testId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and b.testId=:testId");
            values.put("testId", testId);
        }
        condition.append(")");

        if (StringUtil.isNotNull(qcGrade) && this.qcGrade > 0) {
            condition.append(" and qcGrade=:qcGrade");
            values.put("qcGrade", qcGrade);
        }
        if (StringUtil.isNotNull(qcType) && this.qcType > 0) {
            condition.append(" and qcType=:qcType");
            values.put("qcType", qcType);
        }

        if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and bigSampleTypeId=:bigSampleTypeId");
            values.put("bigSampleTypeId", sampleTypeId);
        }

        if (StringUtils.isNotNullAndEmpty(sampleCode)) {
            condition.append(" and (code like :code or associateSampleCode like :code)");
            values.put("code", "%" + sampleCode + "%");
        }

        return condition.toString();
    }
}
