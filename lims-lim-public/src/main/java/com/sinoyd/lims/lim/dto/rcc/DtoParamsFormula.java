package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.base.dto.customer.DtoImportTestFormula;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.entity.ParamsFormula;

import java.util.List;
import java.util.Set;

import javax.persistence.*;

import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoParamsFormula实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_ParamsFormula")
 @Data
 @DynamicInsert
 public  class DtoParamsFormula extends ParamsFormula {
	@Transient
	private Set<DtoParamsConfig> paramsConfigList;

    @Transient
    private List<DtoParamsTestFormula> paramsTestFormulaList;

    public DtoParamsFormula(){};

    /**
     * 测试项目公式导入(新增)时调用构造
     * @param importTestFormula 导入实体
     * @param testList          所有测试项目
     * @param sampleTypeList    所有检测类型
     */
    public DtoParamsFormula(DtoImportTestFormula importTestFormula,List<DtoTest> testList,List<DtoSampleType> sampleTypeList){
        DtoTest test = testList.stream().filter(t->t.getRedAnalyzeMethodName().equals(importTestFormula.getAnalyzeMethod())&&t.getRedAnalyzeItemName().equals(importTestFormula.getAnalyzeItem()))
                .findFirst().orElse(null);
        if(StringUtil.isNotNull(test)){
            this.setObjectId(test.getId());
        }
        this.setFormula(importTestFormula.getFormula());
        this.setObjectType(EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue());
        DtoSampleType sampleType = sampleTypeList.stream().filter(s->s.getTypeName().equals(importTestFormula.getSampleTypeForFormula())).findFirst().orElse(null);
        if(StringUtil.isNotNull(sampleType)){
            this.setSampleTypeId(sampleType.getId());
        }
    }
 }