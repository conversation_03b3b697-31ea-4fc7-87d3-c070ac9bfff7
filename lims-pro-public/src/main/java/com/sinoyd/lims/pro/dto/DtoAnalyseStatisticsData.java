package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.AnalyseStatisticsData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 检测情况统计实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/07/10
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_AnalyseStatisticsData")
@Data
@Where(clause = "isDeleted = 0")
@DynamicInsert
public class DtoAnalyseStatisticsData extends AnalyseStatisticsData {

}
