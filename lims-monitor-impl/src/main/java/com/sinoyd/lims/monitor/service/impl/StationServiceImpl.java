package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.frame.sys.mapper.IUserMapper;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.boot.frame.sys.model.UserModel;
import com.sinoyd.boot.frame.sys.service.IOrgService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.monitor.criteria.StationCriteria;
import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import com.sinoyd.lims.monitor.repository.rcc.StationRepository;
import com.sinoyd.lims.monitor.service.StationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * Station操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class StationServiceImpl extends BaseJpaServiceImpl<DtoStation, String, StationRepository> implements StationService {

    @Autowired
    private AreaService areaService;

    @Autowired
    private IOrgService iOrgService;

    @Autowired
    private IUserMapper userMapper;

    @Override
    public void findByPage(PageBean<DtoStation> pb, BaseCriteria stationCriteria) {
        pb.setEntityName("DtoStation a");
        pb.setSelect("select a");
        super.findByPage(pb, stationCriteria);
        List<DtoStation> dataList = pb.getData();
        List<String> staddressIds = dataList.parallelStream().map(DtoStation::getStaddress).collect(Collectors.toList());
        List<String> orgids = dataList.parallelStream().map(DtoStation::getEntId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(dataList)){
            //行政区域所有的集合
            List<DtoArea> list = areaService.findByIds(staddressIds);
            //所属单位集合
            List<OrgModel> orgModelList = iOrgService.selectBatchIds(orgids);
            for (DtoStation dto: dataList) {
                if (StringUtil.isNotEmpty(list)) {
                    Optional<DtoArea> optional = list.parallelStream()
                            .filter(p -> p.getId().equals(dto.getStaddress())).findFirst();
                    if (optional.isPresent()) {
                        dto.setStaddressName(optional.get().getAreaName());
                    }
                }
                if (StringUtil.isNotEmpty(orgModelList)) {
                    Optional<OrgModel> optional = orgModelList.parallelStream()
                            .filter(p -> p.getId().equals(dto.getEntId())).findFirst();
                    if (optional.isPresent()) {
                        dto.setEndName(optional.get().getOrgName());
                    }
                }
            }
        }
    }

    @Transactional
    @Override
    public DtoStation save(DtoStation entity) {
        // 根据测站代码查询是否已存在
        if (!entity.getStcode().equals("")){
            Integer exist = repository.countByStcode(entity.getStcode());
            if (exist > 0) {
                throw new BaseException("已存在测站代码为" + entity.getStcode() + "的测站");
            }
        }
        return super.save(entity);
    }

    @Transactional
    @Override
    public DtoStation update(DtoStation entity) {
        // 根据测站代码查询是否已存在
        if (!entity.getStcode().equals("")) {
            Integer exist = repository.countByStcode(entity.getStcode());
            //获取前端传的测站编码，查到对象
            DtoStation dtoStation = repository.findByStcode(entity.getStcode());
            if (dtoStation != null){
                //如果前端传的编码对象和本身的主键相符合，就代表是本身，不用判断
                if (entity.getId().equals(dtoStation.getId())){
                    return super.save(entity);
                }
            }
            if (exist > 0) {
                throw new BaseException("已存在测站代码为" + entity.getStcode() + "的测站");
            }
        }
        return super.save(entity);
    }

    @Override
    public Map<String, List<DtoStation>> findStation() {
        //根据上下文中用户id查询出当前登录用户的 userModel，多次一步主要防止登录用户用的用户名登录
        UserModel userModel = userMapper.selectById(PrincipalContextUser.getPrincipal().getUserId());
        //防止当前用户存在于多个机构
        List<UserModel> userModelList = userMapper.findUserByLoginId(userModel.getLoginID());
        List<String> orgIds = userModelList.stream().map(UserModel::getOrgGuid).distinct().collect(Collectors.toList());
        StationCriteria criteria = new StationCriteria();
        criteria.setIsEndable(1);
        PageBean<DtoStation> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.setSort("orderNum-");
        findByPage(pb, criteria);
        List<DtoStation> stations = pb.getData().stream().filter(s -> orgIds.contains(s.getEntId())).collect(Collectors.toList());
        return stations.stream().collect(Collectors.groupingBy(DtoStation::getEndName, LinkedHashMap::new, Collectors.toList()));
    }
}