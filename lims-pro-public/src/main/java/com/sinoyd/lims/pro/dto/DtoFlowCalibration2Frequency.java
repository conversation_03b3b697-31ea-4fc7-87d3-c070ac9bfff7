package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.FlowCalibration2Frequency;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoFlowCalibration2Frequency实体
 * <AUTHOR>
 * @version V1.0.0
 * @since 2024/11/15
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_FlowCalibration2Frequency")
@Data
@DynamicInsert
public class DtoFlowCalibration2Frequency extends FlowCalibration2Frequency {

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 点位名称
     */
    @Transient
    private String watchSpot;

    /**
     * 检测类型
     */
    @Transient
    private  String sampleType;

    /**
     *  週期顯示文本
     */
    @Transient
    private String periodName;

    /**
     * 分析项目名称
     */
    @Transient
    private String redAnalyzeItemName;

    /**
     * 采样时间，多个、拼接
     */
    @Transient
    private String actualDateStr;

    /**
     * 送样单号，多个、拼接
     */
    @Transient
    private String recordCode;

    /**
     * 采样人员，多个、拼接
     */
    @Transient
    private String actualPeopleStr;
}
