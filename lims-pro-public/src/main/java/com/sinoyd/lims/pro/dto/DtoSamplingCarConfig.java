package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SamplingCarConfig;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.Date;


/**
 * DtoSamplingCarConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_SamplingCarConfig")
 @Data
 @DynamicInsert
 public  class DtoSamplingCarConfig extends SamplingCarConfig {
    private static final long serialVersionUID = 1L;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 送样单号
     */
    @Transient
    private String recordCode;

    /**
     * 委托单位
     */
    @Transient
    private String customerName;

    /**
     * 使用人
     */
    @Transient
    private String usePerson;

    /**
     * 使用日期
     */
    @Transient
    private Date useDate;
}