package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * CarManage实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="CarManage")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class CarManage implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  CarManage() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 车牌号码
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("车牌号码")
    @Length(message = "车牌号码{validation.message.length}", max = 50)
	private String carCode;
    
    /**
    * 车辆型号
    */
    @Column(length=50)
    @ApiModelProperty("车辆型号")
    @Length(message = "车辆型号{validation.message.length}", max = 50)
    private String carModel;
    
    /**
    * 车辆类型（常量LIM_CarType:轿车、货车、商务车、SUV）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("车辆类型（常量LIM_CarType:轿车、货车、商务车、SUV）")
    @Length(message = "车辆类型{validation.message.length}", max = 50)
	private String carType;
    
    /**
    * 车辆状态(枚举EnumCarState:1:正常 2:维修,3:停用;4:过期)
    */
    @Column(nullable=false)
    @ColumnDefault("1")
    @ApiModelProperty("车辆状态(枚举EnumCarState:1:正常 2:维修,3:停用;4:过期)")
    private Integer state;
    
    /**
    * 负责人Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("负责人Id（Guid）")
    private String managerId;
    
    /**
    * 购置日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("购置日期")
    private Date buyDate;
    
    /**
    * 发动机号码
    */
    @Column(length=50)
    @ApiModelProperty("发动机号码")
    @Length(message = "发动机号码{validation.message.length}", max = 50)
    private String engineCode;
    
    /**
    * 最近年检日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("最近年检日期")
    private Date annualReviewDate;
    
    /**
    * 年检周期（月）
    */
    @Column(nullable=false)
    @ColumnDefault("12")
    @ApiModelProperty("年检周期（月）")
    private Integer annualReviewCycle;
    
    /**
    * 油耗
    */
    @Column(length=50)
    @ApiModelProperty("油耗")
    @Length(message = "油耗{validation.message.length}", max = 50)
    private String oilConsumption;
    
    /**
    * 备注
    */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;
    
    /**
    * 商标
    */
    @Column(length=50)
    @ApiModelProperty("商标")
    @Length(message = "商标{validation.message.length}", max = 50)
    private String carBrand;
    
    /**
    * 车辆颜色
    */
    @Column(length=50)
    @ApiModelProperty("车辆颜色")
    @Length(message = "车辆颜色{validation.message.length}", max = 50)
    private String carColor;
    
    /**
    * 使用状态（枚举EnumCarUseState 0：空闲，1：使用中）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("使用状态（枚举EnumCarUseState 0：空闲，1：使用中）")
    private Integer useState;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }