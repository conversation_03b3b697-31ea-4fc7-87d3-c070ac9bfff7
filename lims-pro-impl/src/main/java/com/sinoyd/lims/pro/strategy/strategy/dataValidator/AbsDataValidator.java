package com.sinoyd.lims.pro.strategy.strategy.dataValidator;

import java.util.Map;

/**
 * 数据校验策略
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/09/23
 */
public abstract class AbsDataValidator {

    /**
     * 校验数据
     *
     * @param value 被校验的数据
     * @param map   参数映射
     * @return 校验结果
     */
    public abstract Boolean validate(Object value, Map<String, Object> map);

    /**
     * 获取控件类型
     *
     * @return 控件类型
     */
    public abstract Integer getControlType();
}
