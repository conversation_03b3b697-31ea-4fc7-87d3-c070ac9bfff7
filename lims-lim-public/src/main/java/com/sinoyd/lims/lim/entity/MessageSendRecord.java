package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 消息发送记录实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/15
 */
@MappedSuperclass
@ApiModel(description = "MessageSendRecord")
@Data
@EntityListeners(AuditingEntityListener.class)
public class MessageSendRecord implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public MessageSendRecord() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 任务id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("任务id")
    private String jobId;

    /**
     * 消息类型
     */
    @Column(length = 100, nullable = false)
    @ApiModelProperty("消息类型")
    @Length(message = "消息类型{validation.message.length}", max = 100)
    private String messageType;

    /**
     * 消息内容
     */
    @Column(length = 4000)
    @ApiModelProperty("消息内容")
    private String messageContent;

    /**
     * 接收人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("接收人id")
    private String receiver;

    /**
     * 发送时间
     */
    @Column(nullable = false)
    @ApiModelProperty("发送时间")
    private Date sendTime;

    /**
     * 状态 (0：未读，1：已读)
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("状态 (0：未读，1：已读)")
    private Integer status = 0;

    /**
     * 是否重点关注
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否重点关注")
    private Boolean isConcern = false;

    /**
     * 1：平台  2：短信  4：APP  8：微信  16：钉钉
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("1：平台  2：短信  4：APP  8：微信  16：钉钉")
    private Integer sendType = 1;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;


    /**
     * 是否提醒（用作页面抖动提醒状态）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否提醒")
    private Boolean isRemind = false;

}