package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.lims.lim.dto.customer.DtoExportParamsPartFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * 测试项目部分公式实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ParamsPartFormulaMapper {


    /**
     * DtoParamsPartFormula 实例转换成DtoExportParamsPartFormula实例
     *
     * @param paramsPartFormula 分析项目实体
     * @return DtoExportParamsPartFormula 实例
     */
    @Mapping(source = "mostSignificance", target = "mostSignificance")
    @Mapping(source = "mostDecimal", target = "mostDecimal")
    @Mapping(source = "orderNum", target = "orderNum")
    @Mapping(source = "formulaType", target = "formulaType")
    @Mapping(source = "calculationMode", target = "calculationMode")
    @Mapping(source = "useTestLimit", target = "useTestLimit")
    DtoExportParamsPartFormula toExportParamsPartFormula(DtoParamsPartFormula paramsPartFormula);

    /**
     * DtoParamsPartFormula 实例集合转换成DtoExportParamsPartFormula 实例集合
     *
     * @param paramsPartFormulaList 分析项目实例集合
     * @return DtoExportParamsPartFormula 实例集合
     */
    @InheritConfiguration(name = "toExportParamsPartFormula")
    List<DtoExportParamsPartFormula> toExportParamsPartFormulaList(List<DtoParamsPartFormula> paramsPartFormulaList);


    /**
     * DtoExportParamsPartFormula 实例转换成DtoParamsPartFormula 实例
     *
     * @param exportParamsPartFormula 分析项目实体
     * @return DtoExportParamsPartFormula 实例
     */
    @Mapping(source = "mostSignificance", target = "mostSignificance")
    @Mapping(source = "mostDecimal", target = "mostDecimal")
    @Mapping(source = "orderNum", target = "orderNum")
    @Mapping(source = "formulaType", target = "formulaType")
    @Mapping(source = "calculationMode", target = "calculationMode")
    @Mapping(source = "useTestLimit", target = "useTestLimit")
    DtoParamsPartFormula toDtoParamsPartFormula(DtoExportParamsPartFormula exportParamsPartFormula);

    /**
     * DtoExportParamsPartFormula 实例集合转换成DtoParamsPartFormula 实例集合
     *
     * @param exportParamsPartFormulaList 分析项目导入导出实例集合
     * @return DtoParamsPartFormula 实例集合
     */
    @InheritConfiguration(name = "toDtoParamsPartFormula")
    List<DtoParamsPartFormula> toDtoParamsPartFormulaList(List<DtoExportParamsPartFormula> exportParamsPartFormulaList);

}
