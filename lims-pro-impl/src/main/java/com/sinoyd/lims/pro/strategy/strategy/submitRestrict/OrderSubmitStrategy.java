package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.QuotationDetailService;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单提交
 * <AUTHOR>
 */
@Component(IFileNameConstant.SubmitStrategyKey.SUBMIT_ORDER)
public class OrderSubmitStrategy extends AbsSubmitRestrictStrategy {

    private QuotationDetailService quotationDetailService;

    @Override
    public List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        String orderId = objMap.toString();
        List<DtoQuotationDetail> detailList = quotationDetailService.findByOrderId(orderId);
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.检测费用明细.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.检测费用明细.getModuleName());
        if (detailList.size() == 0) {
            restrictVo.setExceptionOption("未添加检测费用明细");
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        restrictVoList.add(restrictVo);
        return restrictVoList;
    }

    @Autowired
    @Lazy
    public void setQuotationDetailService(QuotationDetailService quotationDetailService) {
        this.quotationDetailService = quotationDetailService;
    }
}
