-- 创建送样单现场数据模板表
CREATE TABLE TB_PRO_ReceiveSampleRecordParamTemplate
(
    id           varchar(50) NOT NULL COMMENT '主键id',
    receiveId    varchar(50) NOT NULL COMMENT '送样单id',
    templateName varchar(500) NULL DEFAULT NULL COMMENT '模板名称',
    sampleTypeId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '检测类型id',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);


-- 创建送样单现场数据模板参数信息表
CREATE TABLE TB_PRO_ReceiveSampleRecordParamInfo
(
    id         varchar(50) NOT NULL COMMENT '主键id',
    templateId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '送样单参数模板id',
    paramId    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '参数id/测试项目id',
    paramName  varchar(500) NULL DEFAULT NULL COMMENT '参数名称/分析项目名称',
    orderNum   int(11) NOT NULL DEFAULT -1 COMMENT '排序值',
    type       int(11) NULL DEFAULT NULL COMMENT '对象类型（枚举EnumParamsType：2.样品参数，4.点位参数，3.分析项目',
    orgId      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
);