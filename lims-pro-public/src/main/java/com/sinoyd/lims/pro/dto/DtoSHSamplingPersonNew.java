package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SHSamplingPersonNew;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 监管平台采样人员配置
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/11/15
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SHSamplingPerson")
@Data
@DynamicInsert
public class DtoSHSamplingPersonNew extends SHSamplingPersonNew {

    private static final long serialVersionUID = 1L;

    /**
     * 人员名称
     */
    @Transient
    private String personName;

    /**
     * 监管平台人员id
     */
    @Transient
    private String regulateId;

    /**
     * 监管平台人员名称
     */
    @Transient
    private String regulateName;

    /**
     * 部门名称
     */
    @Transient
    private String deptName;

    /**
     * 电话号码
     */
    @Transient
    private String phone;

}
