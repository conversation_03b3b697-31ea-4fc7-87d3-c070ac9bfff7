package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;

/**
 * 测试岗位仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
public interface TestPostRepository extends IBaseJpaRepository<DtoTestPost, String> {

    /**
     * 根据测试岗位名称查找
     *
     * @param postName 测试岗位id
     * @return 测试岗位人员配置列表
     */
    DtoTestPost findByPostNameAndIsDeletedFalse(String postName);

    /**
     * 根据测试岗位名称和岗位类型查找
     *
     * @param postName 测试岗位id
     * @return 测试岗位人员配置列表
     */
    DtoTestPost findByPostNameAndPostTypeAndIsDeletedFalse(String postName, Integer postType);
}