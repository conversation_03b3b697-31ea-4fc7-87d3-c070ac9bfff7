package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoEntEvaluation;
import com.sinoyd.lims.lim.repository.lims.EntEvaluationRepository;
import com.sinoyd.lims.lim.service.EntEvaluationService;

import org.springframework.stereotype.Service;

/**
 * 供应商管理-评价信息
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Service
public class EntEvaluationServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoEntEvaluation, String, EntEvaluationRepository>
        implements EntEvaluationService {

    /***
     *
     * @param page
     * @param criteria
     */
    @Override
    public void findByPage(PageBean<DtoEntEvaluation> page, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        page.setEntityName("DtoEntEvaluation p");
        // 设置查询返回的字段、实体别名表示所有字段
        page.setSelect("select p");

        super.findByPage(page, criteria);
    }
}