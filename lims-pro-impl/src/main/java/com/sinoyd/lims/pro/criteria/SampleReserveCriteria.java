package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * SampleReserveCriteria查询Sample时的条件
 *
 * <AUTHOR>
 * @version V5.2.0 2022年6月21日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleReserveCriteria extends BaseCriteria implements Serializable {

    /**
     * 需要过滤的样品类型id集合
     */
    private List<String> ignoreSampleTypeIds;

    /**
     * 采样开始日期
     */
    private String beginTime;

    /**
     * 采样结束日期
     */
    private String endDate;

    /**
     * 检测类型
     */
    private String sampleType;

    /**
     * 项目查询条件
     */
    private String projectCondition;

    /**
     * 样品查询条件
     */
    private String sampleCondition;

    /**
     * 分析因子
     */
    private String analyzeItemName;

    /**
     * 分析方法，编号
     */
    private String analyzeMethodCondition;

    /**
     * 处置状态
     */
    private Integer disposeStatus;

    /**
     * 领取状态
     */
    private String receiveStatus;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and exists (SELECT 1 FROM DtoReceiveSampleRecord r WHERE s.receiveId = r.id " +
                "and r.receiveStatus > 1)");
        if (StringUtils.isNotNullAndEmpty(this.beginTime)) {
            Date from = DateUtil.stringToDate(this.beginTime, DateUtil.YEAR);
            condition.append(" and s.samplingTimeBegin >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtils.isNotNullAndEmpty(this.endDate)) {
            Date to = DateUtil.stringToDate(this.endDate, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            to = c.getTime();
            condition.append(" and s.samplingTimeBegin < :endTime");
            values.put("endTime", to);
        }
        if (StringUtil.isNotEmpty(this.sampleType) && !UUIDHelper.GUID_EMPTY.equals(this.sampleType)) {
            condition.append(" and s.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleType);
        }
        if (StringUtil.isNotEmpty(this.ignoreSampleTypeIds)) {
            condition.append(" and s.sampleTypeId not in (:ignoreSampleTypeIds)");
            values.put("ignoreSampleTypeIds", this.ignoreSampleTypeIds);
        }
        if (StringUtil.isNotEmpty(this.projectCondition)) {
            condition.append(" and exists (select 1 from DtoProject p where s.projectId = p.id and " +
                    "(p.projectName like :projectCondition or p.projectCode like :projectCondition))");
            values.put("projectCondition", "%" + this.projectCondition + "%");
        }
        if (StringUtil.isNotEmpty(this.sampleCondition)) {
            condition.append(" and (s.code like :sampleCondition or s.redFolderName like :sampleCondition)");
            values.put("sampleCondition", "%" + this.sampleCondition + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeItemName)) {
            condition.append(" and exists (select 1 from DtoAnalyseData a where s.id = a.sampleId " +
                    "and a.redAnalyzeItemName like :analyzeItemName and a.isCompleteField = 0)");
            values.put("analyzeItemName", "%" + this.analyzeItemName + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeMethodCondition)) {
            condition.append(" and exists (select 1 from DtoAnalyseData a where s.id = a.sampleId " +
                    "and (a.redAnalyzeMethodName like :analyzeMethodCondition or a.redCountryStandard like :analyzeMethodCondition) and a.isCompleteField = 0)");
            values.put("analyzeMethodCondition", "%" + this.analyzeMethodCondition + "%");
        }
        if (StringUtil.isNotNull(this.disposeStatus)) {
            if (this.disposeStatus.equals(1)) {
                condition.append(" and exists (select 1 from DtoSampleReserve sr where s.id = sr.sampleId and " +
                        "sr.reserveType = 2 and isDeleted = 0) ");
            } else if (this.disposeStatus.equals(0)) {
                condition.append(" and not exists (select 1 from DtoSampleReserve sr where s.id = sr.sampleId and " +
                        "sr.reserveType = 2 and isDeleted = 0) ");
            }
        }
        return condition.toString();
    }
}
