package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchPlan;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchTask;

/**
 * 查新计划仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
public interface NewSearchTaskRepository extends IBaseJpaPhysicalDeleteRepository<DtoNewSearchTask, String> {

}
