package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.AnalyzeItemRelationParams;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoAnalyzeItemRelationParams实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_AnalyzeItemRelationParams")
 @Data
 @DynamicInsert
 public  class DtoAnalyzeItemRelationParams extends AnalyzeItemRelationParams {

 }