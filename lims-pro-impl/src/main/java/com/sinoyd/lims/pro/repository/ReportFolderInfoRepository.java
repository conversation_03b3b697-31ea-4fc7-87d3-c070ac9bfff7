package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoReportFolderInfo;

import java.util.List;


/**
 * 电子报告点位数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/03
 * @since V100R001
 */
public interface ReportFolderInfoRepository extends IBaseJpaRepository<DtoReportFolderInfo, String> {

    /**
     * 按报告id查询点位数据
     *
     * @param reportId 报告id
     * @return 返回相应的报告点位数据
     */
    List<DtoReportFolderInfo> findByReportId(String reportId);
}