package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoFixedProperty;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 固定资产接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/13
 * @since V100R001
 */
public interface FixedAssetsService extends IBaseJpaService<DtoFixedProperty, String> {
    /**
     * 固定资产导入模板下载
     *
     * @param response   响应头
     * @param sheetNames sheet名称对应Map
     * @param fileName   文件名
     */
    void downLoadExcel(HttpServletResponse response, Map<String, String> sheetNames, String fileName);

    /**
     * 导出
     *
     * @param baseCriteria 查询条件
     * @param response     响应头
     * @param sheetNames   sheet名称对应Map
     * @param fileName     固定资产
     */
    void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName);

    DtoFixedProperty findAttachPath(String id);
}
