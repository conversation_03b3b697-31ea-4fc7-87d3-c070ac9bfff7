CREATE  TRIGGER "AFTER_UPDATE_RECORDCONFIG2TEST"
    AFTER  UPDATE
    ON "TB_LIM_RECORDCONFIG2TEST"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

    if new.recordConfigId != old.recordConfigId or (new.recordConfigId is null and old.recordConfigId is not null) or
       (new.recordConfigId is not null and old.recordConfigId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'recordConfigId',
        old.recordConfigId, new.recordConfigId, '00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000');
END IF;
if new.testId != old.testId or (new.testId is null and old.testId is not null) or
       (new.testId is not null and old.testId is null) then
INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig2test', new.id, '00000000-0000-0000-0000-000000000000', now(), 3, 'testId',
        old.testId, new.testId, '00000000-0000-0000-0000-000000000000','00000000-0000-0000-0000-000000000000');
END IF;
end;
