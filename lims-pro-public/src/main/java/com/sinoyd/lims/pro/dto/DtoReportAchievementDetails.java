package com.sinoyd.lims.pro.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.lims.pro.entity.AnalyseAchievementDetails;
import com.sinoyd.lims.pro.entity.ReportAchievementDetails;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoReportAchievementDetails实体
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ReportAchievementDetails")
@Data
@DynamicInsert
public class DtoReportAchievementDetails extends ReportAchievementDetails {

    private static final long serialVersionUID = 1L;

    @Transient
    @Excel(name = "报告类型",needMerge = true,orderNum = "20",width = 11)
    private String reportType;

    @Transient
    @Excel(name = "报告编制人",needMerge = true,orderNum = "30",width = 11)
    private String reportPersonName;

}
