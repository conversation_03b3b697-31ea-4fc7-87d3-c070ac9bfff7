package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.lims.lim.dto.customer.DtoExportTestExpand;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * 测试项目拓展实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface TestExpandMapper {


    /**
     * DtoTestExpand 实例转换成DtoExportTestExpand实例
     *
     * @param testExpand 分析项目实体
     * @return DtoExportTestExpand 实例
     */
    @Mapping(source = "mostSignificance", target = "mostSignificance")
    @Mapping(source = "mostDecimal", target = "mostDecimal")
    @Mapping(source = "timesOrder", target = "timesOrder")
    @Mapping(source = "samplePeriod", target = "samplePeriod")
    DtoExportTestExpand toExportTestExpand(DtoTestExpand testExpand);

    /**
     * DtoTestExpand 实例集合转换成DtoExportTestExpand 实例集合
     *
     * @param testExpandList 分析项目实例集合
     * @return DtoExportTestExpand 实例集合
     */
    @InheritConfiguration(name = "toExportTestExpand")
    List<DtoExportTestExpand> toExportTestExpandList(List<DtoTestExpand> testExpandList);


    /**
     * DtoExportTestExpand 实例转换成DtoTestExpand 实例
     *
     * @param exportTestExpand 分析项目实体
     * @return DtoExportTestExpand 实例
     */
    DtoTestExpand toDtoTestExpand(DtoExportTestExpand exportTestExpand);

    /**
     * DtoExportTestExpand 实例集合转换成DtoTestExpand 实例集合
     *
     * @param exportTestExpandList 分析项目导入导出实例集合
     * @return DtoTestExpand 实例集合
     */
    @InheritConfiguration(name = "toDtoTestExpand")
    List<DtoTestExpand> toDtoTestExpandList(List<DtoExportTestExpand> exportTestExpandList);

}
