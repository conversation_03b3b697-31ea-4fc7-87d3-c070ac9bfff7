package com.sinoyd.lims.api.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface MobileReportService {

    /**
     * 移动端生成报表服务
     *
     * @param reportCode 报表编码
     * @param map        请求参数
     * @return 文件路径
     */
    String generate(String reportCode, Map<String, Object> map, HttpServletRequest request);

    /**
     * 移动端生成报表服务
     *
     * @param reportCode 报表编码
     * @param map        请求参数
     * @return 文件路径
     */
    String generateFile(String reportCode, Map<String, Object> map, HttpServletRequest request, HttpServletResponse response);

    /**
     * 获取采样单列表
     *
     * @return List<Map < String, String>>
     */
    List<Map<String, String>> getSamplingReport(String receiveId, String module,String location);

    /**
     * 获取报表名称
     *
     * @param map      生成报表id
     * @param configId 报表配置名称
     * @return 报表名称
     */
    Map<String, String> documentFileName(Map<String, Object> map, String configId);

}
