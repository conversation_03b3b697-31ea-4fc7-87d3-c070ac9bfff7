package com.sinoyd.lims.lim.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * Cost查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CostCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    private String id;

    @Override
    public String getCondition() {
        /**
         * 清除条件数据
         */
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        return condition.toString();
    }
}