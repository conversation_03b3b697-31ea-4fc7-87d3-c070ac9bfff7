package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoOrderContractAchievement2Person;

import java.util.List;


/**
 *  人员签订合同绩效repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/03/12
 */
public interface OrderContractAchievement2PersonRepository extends IBaseJpaPhysicalDeleteRepository<DtoOrderContractAchievement2Person, String> {

    /**
     * 根据人员id查询数据
     * @param personIds 人员id
     * @return List<DtoOrderContractAcihievement2Person>
     */
    List<DtoOrderContractAchievement2Person> findByPersonIdIn(List<String> personIds);

}
