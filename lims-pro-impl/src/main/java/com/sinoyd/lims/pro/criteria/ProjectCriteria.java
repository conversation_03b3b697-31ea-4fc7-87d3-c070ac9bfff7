package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.lim.enums.EnumLIM.EnumProjectModule;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;


/**
 * 项目查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年11月16日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目类型
     */
    private String projectTypeId;

    /**
     * 项目类型
     */
    private String projectTypeName;

    /**
     * 登记人id
     */
    private String inceptPersonId;

    /**
     * 项目负责人id
     */
    private String leaderId;

    /**
     * 编制报告人id
     */
    private String reportMakerId;

    /**
     * 送样人id
     */
    private String senderId;

    /**
     * 当前操作人id
     */
    private String currentPersonId;

    /**
     * 关键字
     */
    private String key;

    /**
     * 包含样品
     */
    private String sample;

    /**
     * 模块编码
     */
    private String module;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 状态
     */
    private Integer status = EnumPRO.EnumStatus.所有.getValue();

    /**
     * 检测类型
     */
    private String examineTypeId;

    /**
     * 判断是否例行任务
     */
    private List<String> outTypeIds;

    /**
     * 检测状态
     */
    private Integer monitorStatus;

    /**
     * 报告流程状态（EnumReportStatus： 0.未完成，1.已完成）
     */
    private Integer reportStatus;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 是否多企业
     */
    private Integer isMultiEnter = EnumLIM.EnumBoolenType.否.getValue();

    /**
     * 查询子项目信息
     */
    private String parentId;

    /**
     * 是否过滤没有样品的污染源子任务项目
     */
    private Boolean isFilter;

    /**
     * 方案变更选择项目列表过滤字段
     */
    private Boolean isProjectApprove;

    /**
     * 过滤的项目类型id
     */
    private List<String> filterTypeIds;

    /**
     * 项目类型ids
     */
    private List<String> projectTypeIds;

    /**
     * 检测类型ids
     */
    private List<String> sampleTypeIds;

    /**
     * 是否采样（根据项目中是否存在生成编号的样品来判定是否采样）
     */
    private Boolean isSampling;

    /**
     * 采样时间开始
     */
    private String samplingDateStart;

    /**
     * 采样时间结束
     */
    private String samplingDateEnd;

    /**
     * 分包项目类型id
     */
    private List<String> fbTypeIds;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.id = pl.projectId");
        condition.append(" and p.id = s.projectId");
        if (StringUtil.isNotNull(this.isProjectApprove) && isProjectApprove) {
            condition.append(" and not exists (select 1 from DtoProjectApproval pa where pa.projectId = p.id and pa.modifyStatus in :approveStatus)");
            values.put("approveStatus", Arrays.asList(EnumLIM.EnumPojectApproveStatus.登记中.name(), EnumLIM.EnumPojectApproveStatus.审核中.name(), EnumLIM.EnumPojectApproveStatus.审核不通过.name()));
            if (StringUtil.isNotEmpty(this.filterTypeIds)) {
                condition.append(" and p.projectTypeId in :filterTypeIds");
                values.put("filterTypeIds", this.filterTypeIds);
            }
        }
        if (StringUtil.isNotEmpty(this.sampleTypeIds)) {
            condition.append(" and exists (select 1 from DtoSample sp where sp.isDeleted = 0 and sp.projectId = p.id and sp.sampleTypeId in :sampleTypeIds)");
            values.put("sampleTypeIds", this.sampleTypeIds);
        }
        //存在同时查多企业污染源项目和普通类项目的情况
        if (!this.isMultiEnter.equals(EnumLIM.EnumBoolenType.所有.getValue())) {
            condition.append(" and p.isMultiEnterprise = :isMultiEnter");
            values.put("isMultiEnter", this.isMultiEnter.equals(EnumLIM.EnumBoolenType.是.getValue()));
        }
        if (StringUtil.isNotNull(isFilter) && isFilter) {
            condition.append(" and (p.parentId = :emptyId or (p.parentId <> :emptyId and exists (select 1 from DtoSample s where s.projectId = p.id and s.isDeleted = 0)))");
            values.put("emptyId", UUIDHelper.GUID_EMPTY);
        } else {
            if (StringUtil.isNotEmpty(this.parentId) && !UUIDHelper.GUID_EMPTY.equals(this.parentId)) {
                condition.append(" and p.parentId = :parentId");
                values.put("parentId", this.parentId);
            }
        }
        if (StringUtil.isNotEmpty(this.projectTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.projectTypeId)) {
            condition.append(" and p.projectTypeId = :projectTypeId");
            values.put("projectTypeId", this.projectTypeId);
        } else if (EnumLIM.EnumProjectModule.环境例行登记.getCode().equals(this.module) || EnumLIM.EnumProjectModule.污染源例行登记.getCode().equals(this.module)) {
            //例行任务登记页面列表需要区分环境质量和污染源
            condition.append(" and p.projectTypeId in :outTypeIds");
            values.put("outTypeIds", this.outTypeIds);
        }

        if (StringUtil.isNotNull(this.projectTypeIds) && this.projectTypeIds.size() > 0) {
            condition.append(" and p.projectTypeId in :projectTypeIds");
            values.put("projectTypeIds", this.projectTypeIds);
        }

        if (StringUtil.isNotEmpty(this.inceptPersonId) && !UUIDHelper.GUID_EMPTY.equals(this.inceptPersonId)) {
            condition.append(" and p.inceptPersonId = :inceptPersonId");
            values.put("inceptPersonId", this.inceptPersonId);
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and p.inceptTime >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and p.inceptTime < :endTime");
            values.put("endTime", to);
        }

        if (StringUtil.isNotEmpty(this.leaderId) && !UUIDHelper.GUID_EMPTY.equals(this.leaderId)) {
            condition.append(" and pl.leaderId = :leaderId");
            values.put("leaderId", this.leaderId);
        }
        if (StringUtil.isNotEmpty(this.reportMakerId) && !UUIDHelper.GUID_EMPTY.equals(this.reportMakerId)) {
            condition.append(" and pl.reportMakerId = :reportMakerId");
            values.put("reportMakerId", this.reportMakerId);
        }
        if (StringUtil.isNotEmpty(this.currentPersonId) && !UUIDHelper.GUID_EMPTY.equals(this.currentPersonId)) {
            condition.append(" and s.currentPersonId = :currentPersonId");
            values.put("currentPersonId", this.currentPersonId);
        }
        if (StringUtil.isNotEmpty(this.senderId) && !UUIDHelper.GUID_EMPTY.equals(this.senderId)) {
            condition.append(" and exists (select 1 from DtoReceiveSampleRecord r where p.id = r.projectId and r.senderId = :senderId)");
            values.put("senderId", this.senderId);
        }

        if (reportStatus != null && reportStatus != -1) {
            condition.append(" and p.reportStatus = :reportStatus ");
            values.put("reportStatus", reportStatus);
        }

        if (StringUtil.isNotEmpty(this.module)) {
            condition.append(" and s.module = :module");
            //项目支出就用项目登记的数据作为查询结果
            if (this.module.equals(EnumLIM.EnumProjectModule.项目支出.getCode())) {
                values.put("module", EnumLIM.EnumProjectModule.项目登记.getCode());
            } else if (this.module.equals(EnumLIM.EnumProjectModule.环境例行登记.getCode()) || this.module.equals(EnumLIM.EnumProjectModule.污染源例行登记.getCode())) {
                values.put("module", EnumLIM.EnumProjectModule.项目登记.getCode());
            } else if (EnumLIM.EnumProjectModule.方案编制.getCode().equals(this.module)) {
                values.put("module", EnumLIM.EnumProjectModule.方案编制.getCode());
            } else if (EnumLIM.EnumProjectModule.方案审核.getCode().equals(this.module)) {
                values.put("module", EnumLIM.EnumProjectModule.方案审核.getCode());
            } else if (EnumLIM.EnumProjectModule.方案确认.getCode().equals(this.module)) {
                values.put("module", EnumLIM.EnumProjectModule.方案确认.getCode());
            } else {
                values.put("module", this.module);
            }
            if (this.module.equals(EnumLIM.EnumProjectModule.项目登记.getCode()) && StringUtil.isNotEmpty(outTypeIds)) {
                condition.append(" and p.projectTypeId not in :projectTypeIds");
                values.put("projectTypeIds", this.outTypeIds);
            }
        }
        //新增报告处检索不显示登记中的数据
        if (StringUtil.isNotEmpty(this.module) && EnumLIM.EnumProjectModule.新增报告检索.getCode().equals(this.module)) {
            condition.append(" and p.status != :projectStatus");
            values.put("projectStatus", EnumPRO.EnumProjectStatus.项目登记中.name());
        }
        if (!EnumPRO.EnumStatus.所有.getValue().equals(this.status)) {
            condition.append(" and s.status = :status");
            values.put("status", this.status);
        }
        if (StringUtil.isNotEmpty(this.projectStatus)) {
            condition.append(" and p.status = :projectStatus");
            values.put("projectStatus", this.projectStatus);
        }
        if (StringUtil.isNotEmpty(this.orderId)) {
            condition.append(" and p.orderId = :orderId");
            values.put("orderId", this.orderId);
        }
        //报告编制增加检索状态查询
        if (StringUtil.isNotEmpty(this.module) && EnumLIM.EnumProjectModule.报告管理.getCode().equals(this.module)) {
            List<String> samplingStatusList = new ArrayList<>();
            samplingStatusList.add(EnumPRO.EnumSampleStatus.样品未采样.name());
            samplingStatusList.add(EnumPRO.EnumSampleStatus.样品未领样.name());
            samplingStatusList.add(EnumPRO.EnumSampleStatus.样品待检.name());
            samplingStatusList.add(EnumPRO.EnumSampleStatus.样品在检.name());
            if (EnumPRO.EnumMonitorType.未检毕.getValue().equals(monitorStatus)) {
                condition.append(" and exists (select 1 from DtoSample where status in :samplingStatus and projectId = p.id and isDeleted = 0 ) ");
                values.put("samplingStatus", samplingStatusList);
            } else if (EnumPRO.EnumMonitorType.已检毕.getValue().equals(monitorStatus)) {
                condition.append(" and not exists (select 1 from DtoSample where status in :samplingStatus and projectId = p.id and isDeleted = 0 ) ");
                values.put("samplingStatus", samplingStatusList);
                //BUG2024022099734 报告编制检测状态为：已检毕，需要过滤除了新建的分包任务（编制报告界面创建的任务）以外没有任何样品的任务（0/0/0）
                condition.append(" and ( exists (select 1 from DtoSample where projectId = p.id and isDeleted = 0 ) or p.projectTypeId in :fbTypeId) ");
                values.put("fbTypeId", fbTypeIds);

            }
        }
        appendCriteriaByModule(condition, module, key);
        if (StringUtil.isNotEmpty(this.sample)) {
            condition.append(" and exists (select 1 from DtoSample s where s.isDeleted = 0 and p.id = s.projectId " +
                    "and (s.code like :sample or s.redFolderName like :sample))");
            values.put("sample", "%" + sample + "%");
        }
        if (StringUtil.isNotNull(this.isSampling)) {
            if (this.isSampling) {
                condition.append(" and exists (select 1 from DtoSample s where s.isDeleted = 0 and p.id = s.projectId and (s.code <> '' or s.code != null))");
            } else {
                condition.append(" and not exists (select 1 from DtoSample s where s.isDeleted = 0 and p.id = s.projectId and (s.code <> '' or s.code != null))");
            }
        }
        if (StringUtil.isNotEmpty(this.samplingDateStart)) {
            Date date = DateUtil.stringToDate(this.samplingDateStart, DateUtil.YEAR);
            condition.append(" and exists (select 1 from DtoSample s where s.isDeleted = 0 and s.projectId = p.id and s.samplingTimeBegin >= :samplingDateStart and s.samplingTimeBegin != '1753-01-01 00:00:00') ");
            values.put("samplingDateStart", date);
        }
        if (StringUtil.isNotEmpty(this.samplingDateEnd)) {
            Date to = DateUtil.stringToDate(this.samplingDateEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and not exists (select 1 from DtoSample s where s.isDeleted = 0 and s.projectId = p.id and s.samplingTimeBegin >= :samplingDateEnd and s.samplingTimeBegin != '1753-01-01 00:00:00')");
            values.put("samplingDateEnd", to);
        }
        return condition.toString();
    }

    /**
     * 根据模块编码获拼接查询语句
     *
     * @param condition 查询语句
     * @param module    模块编码
     * @param key       查询关键字
     */
    protected void appendCriteriaByModule(StringBuilder condition, String module, String key) {
        if (StringUtil.isNotEmpty(this.module) && StringUtil.isNotEmpty(this.key)) {
            EnumProjectModule projectModule = EnumLIM.EnumProjectModule.getByCode(this.module);
            switch (projectModule) {
                case 现场任务:
                    break;
                case 项目进度:
                case 项目登记:
                case 方案编制:
                case 方案审核:
                case 方案确认:
                case 技术审核:
                case 项目下达:
                case 项目支出:
                case 采样准备:
                    addKeyCondition(condition, this.key);
                    break;
                case 任务办结:
                    addKeyConditionForEnd(condition, this.key);
                    break;

                case 委托现场送样:
                    addKeyConditionWithRecord(condition, this.key);
                    break;

                case 报告管理:
                    addKeyConditionWithReport(condition, this.key);
                    break;

                case 环境例行登记:
                    addKeyConditionWithSampleFolder(condition, this.key);
                    break;

                case 污染源例行登记:
                    addPollutionPointKey(condition, this.key);
                    break;

                default:
                    break;
            }
        }
    }

    //关键字 项目名称、项目编号、委托方
    private void addKeyCondition(StringBuilder condition, String key) {
        condition.append(" and (p.projectCode like :key or p.projectName like :key or p.customerName like :key)");
        values.put("key", "%" + key + "%");
    }

    //关键字 项目名称、项目编号、委托方、受检方、报告编号
    private void addKeyConditionForEnd(StringBuilder condition, String key) {
        condition.append(" and (p.projectCode like :key or p.projectName like :key or p.customerName like :key or p.inspectedEnt like :key or exists (select 1 from DtoReport re where p.id = re.projectId and re.code like :key))");
        values.put("key", "%" + key + "%");
    }

    //关键字 项目名称、项目编号
    private void addKeyConditionWithSampleFolder(StringBuilder condition, String key) {
        condition.append(" and (p.projectCode like :key or p.projectName like :key )");
        values.put("key", "%" + key + "%");
    }

    //关键字 项目编号、项目名称、委托方、受检方、送样单号
    private void addKeyConditionWithRecord(StringBuilder condition, String key) {
        condition.append(" and (p.projectCode like :key or p.projectName like :key or p.customerName like :key or p.inspectedEnt like :key " +
                "or exists (select 1 from DtoReceiveSampleRecord r where p.id = r.projectId and r.recordCode like :key))");
        values.put("key", "%" + key + "%");
    }

    //项目编号、项目名称、委托方、报告编号、检测状态、受检方
    private void addKeyConditionWithReport(StringBuilder condition, String key) {
        condition.append(" and (p.projectCode like :key or p.projectName like :key or p.customerName like :key or p.inspectedEnt like :key " +
                "or exists (select 1 from DtoReport rpt where p.id = rpt.projectId and rpt.code like :key))");
        values.put("key", "%" + key + "%");
    }

    //项目编号、项目名称、企业名称
    private void addPollutionPointKey(StringBuilder condition, String key) {
        condition.append(" and ( p.projectCode like :key or p.projectName like :key or p.customerName like :key " +
                " or exists (select 1 from DtoSampleFolder folder where p.id = folder.projectId and folder.inspectedEnt like :key)" +
                " or exists (select 1 from DtoProject2Customer pc where p.id = pc.projectId and pc.customerName like :key) ) ");
        values.put("key", "%" + key + "%");
    }
}