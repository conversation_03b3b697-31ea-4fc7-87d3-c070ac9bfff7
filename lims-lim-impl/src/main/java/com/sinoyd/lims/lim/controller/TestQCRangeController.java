package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.TestQCRangeCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRange;
import com.sinoyd.lims.lim.service.TestQCRangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * TestQCRange服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/10/8
 * @since V100R001
 */
 @Api(tags = "示例: TestQCRange服务")
 @RestController
 @RequestMapping("api/lim/testQCRange")
 @Validated
 public class TestQCRangeController extends BaseJpaController<DtoTestQCRange, String,TestQCRangeService> {


    /**
     * 分页动态条件查询TestQCRange
     * @param testQCRangeCriteria 条件参数
     * @return RestResponse<List<TestQCRange>>
     */
     @ApiOperation(value = "分页动态条件查询TestQCRange", notes = "分页动态条件查询TestQCRange")
     @GetMapping
     public RestResponse<List<DtoTestQCRange>> findByPage(TestQCRangeCriteria testQCRangeCriteria) {
         PageBean<DtoTestQCRange> pageBean = super.getPageBean();
         RestResponse<List<DtoTestQCRange>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, testQCRangeCriteria);
         restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询TestQCRange
     * @param id 主键id
     * @return RestResponse<DtoTestQCRange>
     */
     @ApiOperation(value = "按主键查询TestQCRange", notes = "按主键查询TestQCRange")
     @GetMapping(path = "/{id}")
     public RestResponse<DtoTestQCRange> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoTestQCRange> restResponse = new RestResponse<>();
         DtoTestQCRange testQCRange = service.findOne(id);
         restResponse.setData(testQCRange);
         restResponse.setRestStatus(StringUtil.isNull(testQCRange) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增TestQCRange
     * @param testQCRange 实体列表
     * @return RestResponse<DtoTestQCRange>
     */
     @ApiOperation(value = "新增TestQCRange", notes = "新增TestQCRange")
     @PostMapping
     public RestResponse<DtoTestQCRange> create(@Validated @RequestBody DtoTestQCRange testQCRange) {
         RestResponse<DtoTestQCRange> restResponse = new RestResponse<>();
         restResponse.setData(service.save(testQCRange));
         return restResponse;
      }

     /**
     * 新增TestQCRange
     * @param testQCRange 实体列表
     * @return RestResponse<DtoTestQCRange>
     */
     @ApiOperation(value = "修改TestQCRange", notes = "修改TestQCRange")
     @PutMapping
     public RestResponse<DtoTestQCRange> update(@Validated @RequestBody DtoTestQCRange testQCRange) {
         RestResponse<DtoTestQCRange> restResponse = new RestResponse<>();
         restResponse.setData(service.update(testQCRange));
         return restResponse;
      }

    /**
     * "根据id批量删除TestQCRange
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除TestQCRange", notes = "根据id批量删除TestQCRange")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
    /**
     * 复制限制配置
     *
     * @param testQCRange 复制限制配置
     * @return
     */
    @ApiOperation(value = "复制限制配置", notes = "复制限制配置")
    @PostMapping("/copyTestQCRange")
    public RestResponse<String> copyTestQCRange(@Validated @RequestBody DtoTestQCRange testQCRange) {

        RestResponse<String> restResponse = new RestResponse<>();
        service.copy(testQCRange.getTestId(), testQCRange.getTestIds());
        restResponse.setMsg("操作成功！");

        return restResponse;
    }
 }