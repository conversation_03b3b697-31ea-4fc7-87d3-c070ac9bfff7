-- 测试项目新增是否嗅辨开关字段
ALTER table TB_LIM_Test
    add COLUMN isOd bit not null DEFAULT 0;


COMMENT
ON COLUMN TB_LIM_Test.isOd is '是否嗅辨';

-- 添加app应用配置
INSERT INTO tb_lim_appconfig(id, name, code, linkAddress, roleId, status, orderNum, remark, orgId, creator, createDate,
                             domainId, modifier, modifyDate, type, typeOrderNum)
VALUES ('855b7f1a-6e33-49fa-98ae-53cfe4910f3b', '嗅辩分析', 'smellAnalyse', '', '', 1, 0, '',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-25 16:20:49',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-25 16:20:49', '嗅辩', 0);
INSERT INTO tb_lim_appconfig(id, name, code, linkAddress, roleId, status, orderNum, remark, orgId, creator, createDate,
                             domainId, modifier, modifyDate, type, typeOrderNum)
VALUES ('872b40e6-b488-4653-a667-c03ec9cac807', '嗅辩管理', 'smellManage', '', '', 1, 0, '',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-25 16:17:22',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2025-03-25 16:17:22', '嗅辩', 0);

-- 嗅辨原始记录单
INSERT INTO tb_lim_reportconfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('6ea45a26-f968-4d06-93bd-23441d2bfc49', 1, 'EnvGasData', '0827三点比较式臭袋法厂界环境臭气测定原始记录(新).xlsx',
        'WorkSheet/0827三点比较式臭袋法厂界环境臭气测定原始记录(新).xlsx', 'output/Sampling/0827三点比较式臭袋法厂界环境臭气测定原始记录(新).xlsx',
        'application/excel', 'com.sinoyd.lims.worksheet.service.workSheet.ods.EnvGasDataSourceService',
        '{"isSignature0":true,"isSelfCell0":true}', '', 0, 2, '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-03-20 16:54:15', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-03-24 09:28:40', 'sampleIds', 'WorkSheet', 'EnvGasData', 0, '',
        NULL, '', '', '', 1, 7);
INSERT INTO tb_lim_reportconfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('ff8796e7-0b01-4b21-9489-39bc8f474685', 1, 'StationarySource', '0828三点比较式臭袋法污染源臭气测定原始记录(新).xlsx',
        'WorkSheet/0828三点比较式臭袋法污染源臭气测定原始记录(新).xlsx', 'output/Sampling/0828三点比较式臭袋法污染源臭气测定原始记录(新).xlsx',
        'application/excel', 'com.sinoyd.lims.worksheet.service.workSheet.ods.StationarySourceDataSourceService',
        '{"isSignature0":true,"isSelfCell0":true}', '', 0, 2, '', 0, '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-03-20 13:54:31', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2025-03-24 11:17:10', 'sampleIds', 'WorkSheet', 'StationarySource', 0,
        '', NULL, '', '', '', 1, 28);


INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('500d3af0-ae16-4c24-9b17-507079efff76', 'ff8796e7-0b01-4b21-9489-39bc8f474685', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-20 13:55:11', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-20 13:55:11', NULL);
INSERT INTO tb_lim_reportapply(id, reportConfigId, module, moduleName, code, name, type, isRedact, isShow, remark,
                               location, orgId, creator, createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('733fbef5-c68e-4110-a726-8008dbd534cf', '6ea45a26-f968-4d06-93bd-23441d2bfc49', 'AnalyseDataManage', '实验室分析',
        '原始记录单', '生成', 0, 0, 1, '', '实验室分析:数据录入', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-20 16:54:39', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2025-03-20 16:54:39', NULL);
