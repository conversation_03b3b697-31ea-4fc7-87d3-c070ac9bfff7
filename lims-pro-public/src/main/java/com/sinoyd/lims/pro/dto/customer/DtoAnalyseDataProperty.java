package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import com.sinoyd.lims.pro.dto.DtoParamsData;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 打开检测单数据属性
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoAnalyseDataProperty {

    /**
     * 相关的分析数据
     */
    private List<Map<String, Object>> analyseData = new ArrayList<>();


    /**
     * 公式相关参数
     */
    private List<DtoTestFormulaParamsConfig> paramsConfig = new ArrayList<>();


    /**
     * 检测单相关参数数据
     */
    private List<DtoParamsData> worksheetParamsData = new ArrayList<>();

    /**
     * 现场数据,是否为当前tab
     */
    private Boolean currentUse = false;
}