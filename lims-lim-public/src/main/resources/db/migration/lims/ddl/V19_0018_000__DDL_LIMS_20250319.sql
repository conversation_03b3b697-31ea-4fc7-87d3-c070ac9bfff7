
CREATE TABLE TB_BASE_LogForDocument
(
    id       varchar(50) NOT NULL COMMENT 'id',
    objectId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '附件id',
    operatorId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '操作人id',
    operatorName     varchar(255)                                                null comment '操作者名字',
    operateTime      datetime    default CURRENT_TIMESTAMP                      not null comment '操作时间',
    operateInfo      varchar(500)                                               null comment '操作信息',
    orgId            varchar(50) default '00000000-0000-0000-0000-000000000000' not null comment '组织机构id',
    domainId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室id',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='附件下载日志';

