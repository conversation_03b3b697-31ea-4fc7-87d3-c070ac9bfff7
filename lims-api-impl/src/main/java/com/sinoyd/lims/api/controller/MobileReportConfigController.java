package com.sinoyd.lims.api.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ReportConfigCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.service.ReportConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * ReportConfig服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/13
 * @since V100R001
 */
@Api(tags = "示例: ReportConfig服务")
@RestController
@RequestMapping("api/mobile/reportConfig")
@Validated
public class MobileReportConfigController extends BaseJpaController<DtoReportConfig, String, ReportConfigService> {


    /**
     * 分页动态条件查询ReportConfig
     *
     * @param reportConfigCriteria 条件参数
     * @return RestResponse<List < ReportConfig>>
     */
    @ApiOperation(value = "分页动态条件查询ReportConfig", notes = "分页动态条件查询ReportConfig")
    @GetMapping
    public RestResponse<List<DtoReportConfig>> findByPage(ReportConfigCriteria reportConfigCriteria) {
        PageBean<DtoReportConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoReportConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, reportConfigCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }
}