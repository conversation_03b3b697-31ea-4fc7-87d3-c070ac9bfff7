package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentRepairRecord;
import com.sinoyd.lims.lim.repository.lims.InstrumentRepairRecordRepository;
import com.sinoyd.lims.lim.service.InstrumentRepairRecordService;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 仪器维修记录接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
@Service
public class InstrumentRepairRecordServiceImpl
                extends BaseJpaPhysicalDeleteServiceImpl<DtoInstrumentRepairRecord, String, InstrumentRepairRecordRepository>
                implements InstrumentRepairRecordService {

        @Override
        public void findByPage(PageBean<DtoInstrumentRepairRecord> page, BaseCriteria criteria) {
                // 设置查询的实体类名及别名
                page.setEntityName("DtoInstrumentRepairRecord x");
                // 设置查询返回的字段、实体别名表示所有字段
                page.setSelect("select x");

                super.findByPage(page, criteria);
        }

        /**
         * 根据申请单id查询维修记录
         * @param purchaseApplyID 申请单id
         * @return
         */
        @Override
        public List<DtoInstrumentRepairRecord> findByPurchaseApplyID(String purchaseApplyID) {
                List<DtoInstrumentRepairRecord> instrumentRepairRecords = null;
                if (StringUtil.isNotEmpty(purchaseApplyID)) {
                        instrumentRepairRecords = repository.findByPurchaseApplyID(purchaseApplyID);
                }
                return instrumentRepairRecords;
        }
}