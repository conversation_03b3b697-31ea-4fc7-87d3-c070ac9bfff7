package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * 查新计划
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "NewSearchPlan")
@Data
@EntityListeners(AuditingEntityListener.class)
public class NewSearchPlan implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public NewSearchPlan() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }


    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 计划名称
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("计划名称")
    @Length(message = "计划名称{validation.message.length}", max = 100)
    private String planName;

    /**
     * 计划类型
     */
    @Column(nullable = false)
    @ApiModelProperty("计划类型")
    private Integer planType;

    /**
     * 执行人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("执行人")
    @Length(message = "执行人{validation.message.length}", max = 50)
    private String executor;

    /**
     * 执行周期(EnumNewSearchPlanDealCycle)
     */
    @Column(nullable = false)
    @ApiModelProperty("执行周期")
    private Integer dealCycle;

    /**
     * 每月执行日期(日)
     */
    @ApiModelProperty("每月执行日期")
    private Integer dealDate;

    /**
     * 任务开始日期
     */
    @ApiModelProperty("任务开始日期")
    private Date taskStartDate;

    /**
     * 任务结束日期
     */
    @ApiModelProperty("任务结束日期")
    private Date taskEndDate;

    /**
     * 计划状态 0:未提交 1:已提交
     */
    @Column(nullable = false)
    @ApiModelProperty("计划状态 1:未提交 2:已提交")
    private Integer status;

    /**
     * 备注
     */
    @Column(length = 255)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 255)
    private String remark;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}