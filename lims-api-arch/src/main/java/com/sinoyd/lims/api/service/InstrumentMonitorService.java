package com.sinoyd.lims.api.service;

import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.api.dto.*;
import com.sinoyd.lims.lim.criteria.EnvironmentalRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;
import com.sinoyd.lims.lim.dto.lims.DtoProjectInstrument;
import com.sinoyd.lims.pro.dto.DtoProject;

import java.util.List;

/**
 * 仪器出入库移动端内容接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/10/25
 * @since V100R001
 */
public interface InstrumentMonitorService extends IBaseJpaService<DtoProjectInstrument, String> {

    /**
     * 仪器出入库列表查询
     *
     * @param pb                             pb
     * @param projectInstrumentPhoneCriteria 查询条件
     * @return 仪器出入库记录
     */
    List<DtoProjectInstrumentPhone> getProjectInstrumentList(PageBean<DtoProjectInstrument> pb, BaseCriteria projectInstrumentPhoneCriteria);

    /**
     * 按照出入库记录id查询出入库记录明细
     *
     * @param key 出入库id
     * @return 出入库明细
     */
    DtoProjectInstrumentPhone findProjectInstrumentPhone(String key);

    /**
     * 新增仪器出入库记录
     *
     * @param instrumentPhone 仪器出入库信息
     * @return 仪器出入库记录信息
     */
    DtoProjectInstrumentPhone saveInstrumentPhone(DtoProjectInstrumentPhone instrumentPhone);

    /**
     * 修改仪器出入库记录
     *
     * @param instrumentPhone 仪器出入库记录信息
     * @return 出入库记录
     */
    DtoProjectInstrumentPhone updateInstrumentPhone(DtoProjectInstrumentPhone instrumentPhone);

    /**
     * 添加仪器出库明细
     *
     * @param instrumentId      仪器id
     * @param instrumentPhoneId 仪器出入库记录id
     */
    void addInstrumentDetails(String instrumentId, String instrumentPhoneId);

    /**
     * 批量添加仪器出库明细
     *
     * @param instrumentIdList  仪器id列表
     * @param instrumentPhoneId 仪器出入库记录id
     */
    void batchAddInstrumentDetails(List<String> instrumentIdList, String instrumentPhoneId);

    /**
     * 添加仪器入库明细
     *
     * @param instrumentId      仪器id
     * @param instrumentPhoneId 仪器出入库记录id
     */
    void addInstrumentDetailsIn(String instrumentId, String instrumentPhoneId);

    /**
     * 批量添加仪器入库明细
     *
     * @param instrumentIdList      仪器id列表
     * @param instrumentPhoneId     仪器出入库记录id
     */
    void addInstrumentDetailsBatchIn(List<String> instrumentIdList, String instrumentPhoneId);

    /**
     * 扫码出库弹出界面
     *
     * @param instrumentId 仪器id
     * @return 仪器信息
     */
    DtoInstrumentOutPhone findInstrumentOutPhone(String instrumentId);

    /**
     * 扫码入库弹出界面
     *
     * @param instrumentId      仪器id
     * @return 仪器入库扫码实体
     */
    DtoInstrumentInPhone findInstrumentInPhone(String instrumentId);

    /**
     * 仪器出入库记录批量删除
     *
     * @param instrumentPhoneIds 仪器出入库记录id
     */
    void deleteInstrumentPhone(List<String> instrumentPhoneIds);

    /**
     * 仪器出入库明细批量删除
     * @param instrumentPhoneDetailIds 仪器出入库明细ids
     */
    void deleteInstrumentDetailPhone(List<String> instrumentPhoneDetailIds);

    /**
     * 仪器入库列表分页查询
     * @param pb 分页条件
     * @param ProjectInstrumentDetailCriteria 查询条件
     */
    void findInstrumentInByPage(PageBean<DtoProjectInstrumentDetailPhone> pb, BaseCriteria ProjectInstrumentDetailCriteria);

    /**
     * 查询项目列表
     * @param pb 分页查询
     * @param projectCriteria 查询条件
     * @return 项目列表
     */
    List<DtoProjectPhone> findProjectByPage(PageBean<DtoProject> pb, BaseCriteria projectCriteria);

    /**
     * 查询仪器列表
     *
     * @param pb              分页查询
     * @param instrumentCriteria 查询条件
     * @return 仪器列表
     */
    List<DtoInstrument> findInstrumentByPage(PageBean<DtoInstrument> pb, BaseCriteria instrumentCriteria);

    /**
     * 查询仪器使用记录
     * @param page 分页查询
     * @param criteria 查询条件
     */
    void findInstrumentRecordByPage(PageBean<DtoEnvironmentalRecord> page, BaseCriteria criteria);

    /**
     * 查询环境记录
     * @param environmentalRecord 环境记录
     */
    Boolean findReceiveStatus(DtoEnvironmentalRecord environmentalRecord);
}
