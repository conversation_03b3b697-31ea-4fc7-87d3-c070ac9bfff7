package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoStatusForProject;
import com.sinoyd.lims.pro.dto.DtoStatusForRecord;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * StatusForRecord数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
public interface StatusForRecordRepository extends IBaseJpaPhysicalDeleteRepository<DtoStatusForRecord, String> {

    /**
     * 查询指定送样单的状态信息
     *
     * @param receiveId 送样单id
     * @return 对应送样单下的状态信息
     */
    List<DtoStatusForRecord> findByReceiveId(String receiveId);

    /**
     * 根据送样单ids获取数据
     * @param receiveIds 送样单ids
     * @return 返回数据
     */
    List<DtoStatusForRecord> findByReceiveIdIn(List<String> receiveIds);

    /**
     * 查询指定送样单模块的状态
     *
     * @param receiveId 送样单id
     * @param module    模块
     * @return 对应送样单模块的状态
     */
    DtoStatusForRecord findByReceiveIdAndModule(String receiveId, String module);

    /**
     * 批量办结送样单状态
     *
     * @param ids    送样单的ids
     * @param status 状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoStatusForRecord s set s.status = :status where s.receiveId in :ids")
    Integer finishStatusForRecord(@Param("ids") List<String> ids,
                                  @Param("status") Integer status);

    /**
     * 批量更新送样单状态
     *
     * @param id            送样单的id
     * @param status         状态
     * @param nextPersonId   下一步操作人id
     * @param nextPersonName 下一步操作人
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoStatusForRecord s set s.status = :status,s.nextPersonId = :nextPersonId,s.nextPersonName = :nextPersonName,s.lastNewOpinion = :lastNewOpinion where s.id = :id")
    Integer updateStatusForRecord(@Param("id") String id,
                                  @Param("status") Integer status,
                                  @Param("nextPersonId") String nextPersonId,
                                  @Param("nextPersonName") String nextPersonName,
                                  @Param("lastNewOpinion") String lastNewOpinion);
}