package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Set;


/**
 * 原始记录单相关测试项目条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordConfig2TestCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 原始记录单的id
     */
    private String recordConfigId;

    /**
     * 关键字: 分析项目
     */
    private String analyzeItemKey;

    /**
     * 关键字: 分析方法、标准编号
     */
    private String analyzeMethodKey;

    /**
     * 测试项目id
     */
    private Set<String> testIds;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotEmpty(this.recordConfigId)) {
            condition.append(" and ct.recordConfigId = :recordConfigId ");
            values.put("recordConfigId", this.recordConfigId);
        }
        if (StringUtil.isNotEmpty(this.testIds)) {
            condition.append(" and ct.testId in :testIds ");
            values.put("testIds", this.testIds);
        }
        condition.append(" and exists (select 1 from DtoTest as t where ct.testId = t.id and t.isDeleted = 0 ) ");
        return condition.toString();
    }
}