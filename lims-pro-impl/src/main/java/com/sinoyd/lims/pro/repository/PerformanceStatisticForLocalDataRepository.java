package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoPerformanceStatisticForLocalData;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * PerformanceStatisticForLocalData数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/21
 * @since V100R001
 */
public interface PerformanceStatisticForLocalDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoPerformanceStatisticForLocalData, String> {

    /**
     * 删除送样单记录相关的绩效
     *
     * @param recordIds 送样单id
     * @return 返回删除行数
     */
    @Transactional
    @Modifying
    @Query("delete  from  DtoPerformanceStatisticForLocalData where receiveId in :recordIds")
    Integer deleteByReceiveIdIn(@Param("recordIds") List<String> recordIds);
}