package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoRecAndPayRecord;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 收付款记录仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2019-05-10
 * @since V100R001
 */
public interface RecAndPayRecordRepository extends IBaseJpaRepository<DtoRecAndPayRecord, String> {


    @Query("select sum(p.amount) from DtoRecAndPayRecord p where p.operatorDate >= :dtBegin and p.operatorDate < :dtEnd and p.isDeleted = 0 and p.moneyType = :moneyType")
    BigDecimal getTotalNum(@Param("dtBegin") Date dtBegin, @Param("dtEnd") Date dtEnd, @Param("moneyType") Integer moneyType);

    List<DtoRecAndPayRecord> findAllByContractIdIn(Collection<String> contractIdList);
}