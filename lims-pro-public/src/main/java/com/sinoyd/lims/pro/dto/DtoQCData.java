package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.dto.customer.DtoQCDataDetail;
import com.sinoyd.lims.pro.entity.QCData;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.ArrayList;
import java.util.List;


/**
 * DtoQCData实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_QCData")
 @Data
 @DynamicInsert
 public  class DtoQCData extends QCData {
    private static final long serialVersionUID = 1L;


    /**
     * 量纲名称
     */
    @Transient
    private String dimension;

    /**
     * 分析人员
     */
    @Transient
    private String analystName;
}