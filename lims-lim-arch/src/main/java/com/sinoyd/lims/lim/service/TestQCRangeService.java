package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoTestQCRange;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRangeResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * 质控限值管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/18
 * @since V100R001
 */
public interface TestQCRangeService extends IBaseJpaService<DtoTestQCRange, String> {

    /**
     * 复制质控限值
     *
     * @param currentTestId 被复制的测试项目id
     * @param copyTestIds   需要复制的测试项目id数组
     */
    void copy(String currentTestId, List<String> copyTestIds);


    /**
     * 计算质控数据是否超出范围
     *
     * @param testId 测试项目id
     * @param qcType 质控类型
     * @param data1  数据1
     * @param data2  数据2
     * @param data3  数据3
     * @return 是否超出范围
     */
    DtoTestQCRangeResult qcRangeIsPass(String testId, Integer qcType, BigDecimal data1, BigDecimal data2, BigDecimal data3);

    /**
     * 计算质控数据是否超出范围
     *
     * @param testQCRanges 质控限值集合
     * @param qcType 质控类型
     * @param data1  数据1
     * @param data2  数据2
     * @param data3  数据3
     * @return 是否超出范围
     */
    DtoTestQCRangeResult qcRangeIsPass(List<DtoTestQCRange>testQCRanges, Integer qcType, BigDecimal data1, BigDecimal data2, BigDecimal data3);


    /**
     * 计算加标数据是否合格
     *
     * @param value  数据值
     * @param rate   加标回收率
     * @param testId 测试项目id
     * @return 返回是否超出范围
     */
    DtoTestQCRangeResult calculateJBPass(BigDecimal value, BigDecimal rate, String testId);


    /**
     * 计算加标数据是否合格
     *
     * @param max    最大值
     * @param min    最小值
     * @param testId 测试项目id
     * @return 返回是否超出范围
     */
    DtoTestQCRangeResult calculatePXPass(BigDecimal avg, BigDecimal max, BigDecimal min, String testId);

        /**
     * 根据测试项目id获取质控限值
     *
     * @param testIds 测试项目id
     * @return 返回相应的质控限值
     */
    List<DtoTestQCRange> findByTestIdIn(List<String> testIds);

}