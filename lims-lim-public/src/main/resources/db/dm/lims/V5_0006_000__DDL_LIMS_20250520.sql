DROP TABLE IF EXISTS "TB_BASE_PollutionDischargeSync";
CREATE TABLE "TB_BASE_PollutionDischargeSync"
(
    "id"           VARCHAR(50) NOT NULL,
    "enterpriseId" VARCHAR(50) NOT NULL,
    "requestTime"  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isSuccess"    BIT         NOT NULL DEFAULT 0,
    "dataContent"  CLOB COMMENT '数据内容',
    PRIMARY KEY (id)
);

COMMENT ON TABLE "TB_BASE_PollutionDischargeSync" IS '排污许可证同步';

COMMENT ON COLUMN "TB_BASE_PollutionDischargeSync"."id" IS '主键';
COMMENT ON COLUMN "TB_BASE_PollutionDischargeSync"."enterpriseId" IS '企业id';
COMMENT ON COLUMN "TB_BASE_PollutionDischargeSync"."requestTime" IS '发起时间';
COMMENT ON COLUMN "TB_BASE_PollutionDischargeSync"."isSuccess" IS '是否同步成功';
COMMENT ON COLUMN "TB_BASE_PollutionDischargeSync"."dataContent" IS '数据内容';


-- 企业中添加排污许可证方案同步状态
ALTER TABLE "TB_BASE_Enterprise"
    ADD COLUMN "isSyncPollutionDischarge" bit NULL DEFAULT 0;

COMMENT ON COLUMN "TB_BASE_Enterprise"."isSyncPollutionDischarge" IS '排污许可证方案同步状态';