package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Table;

/**
 * 参数导出
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/24
 * @since V100R001
 */
@Table(name = "TB_LIM_Params")
@Data
public class DtoExportParams extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id;

    /**
     * 唯一编号
     */
    @Excel(name = "唯一编号", orderNum = "20")
    private String paramCode;

    /**
     * 参数名称
     */
    @Excel(name = "参数名称", orderNum = "30")
    private String paramName;

    /**
     * 备注
     */
    @Excel(name = "备注", orderNum = "40")
    private String remark;

    /**
     * 变量名称
     */
    @Excel(name = "变量名称", orderNum = "50")
    private String variableName;

    /**
     * 正则表达式验证
     */
    @Excel(name = "正则表达式验证", orderNum = "60")
    private String regex;

    /**
     * 计量单位
     */
    @Excel(name = "计量单位", orderNum = "70")
    private String dimension;

    /**
     * 计量单位Id（Guid）
     */
    @Excel(name = "计量单位Id", orderNum = "80")
    private String dimensionId;

}
