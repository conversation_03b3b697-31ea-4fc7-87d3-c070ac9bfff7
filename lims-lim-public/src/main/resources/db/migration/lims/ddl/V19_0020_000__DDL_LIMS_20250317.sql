-- 供应商评价信息V2
CREATE TABLE tb_base_enterpriseEvaluate
(
    id                VARCHAR(50) NOT NULL COMMENT 'id',
    entId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '供应商id',
    yearSn            VARCHAR (10) NOT NULL DEFAULT ''  COMMENT '年度',
    certName          VARCHAR(255)         DEFAULT NULL COMMENT '资质证书',
    certCode          VARCHAR(255)         DEFAULT NULL COMMENT '证书编号',
    certEffectiveTime datetime    NOT NULL DEFAULT '1753-01-01 00:00:00' COMMENT '有效期至',
    isQualified       bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否合格供应商',
    orgId             VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator           <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '国家标准方法';