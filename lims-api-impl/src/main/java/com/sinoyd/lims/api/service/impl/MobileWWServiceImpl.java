package com.sinoyd.lims.api.service.impl;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.api.dto.vo.ProjectVO;
import com.sinoyd.lims.api.dto.vo.WWFolderVO;
import com.sinoyd.lims.api.dto.vo.WWProjectVO;
import com.sinoyd.lims.api.service.MobileWWService;
import com.sinoyd.lims.instrument.parse.dto.DtoTcpData;
import com.sinoyd.lims.instrument.parse.service.TcpDataService;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfig;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParam;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.OcrConfigParamService;
import com.sinoyd.lims.lim.service.OcrConfigService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MobileWWServiceImpl implements MobileWWService {

    private ProjectService projectService;
    private OrderFormService orderFormService;
    private OrderContractService orderContractService;
    private DocumentService documentService;
    private EnterpriseService enterpriseService;
    private RecAndPayRecordService recAndPayRecordService;
    private FolderPeriodWWInfoService folderPeriodWWInfoService;
    private TcpDataService tcpDataService;
    private SampleGroupService sampleGroupService;
    private ParamsDataService paramsDataService;
    private OcrConfigService ocrConfigService;
    private OcrConfigParamService ocrConfigParamService;
    private SampleGroup2TestService sampleGroup2TestService;
    private AnalyseDataService analyseDataService;

    @Override
    public WWProjectVO findByProAndFolder(ProjectVO projectVO) {
        DtoProject project = projectService.findOne(projectVO.getProjectId());
        WWProjectVO wwProjectVO = new WWProjectVO();
        wwProjectVO.setTaskId(DateUtil.dateToString(new Date(), "yyyyMMddHHmmss"));
        wwProjectVO.setProjectName(project.getProjectName());
        wwProjectVO.setProjectNumber(project.getProjectCode());
        wwProjectVO.setPeriodStartTime(project.getInceptTime());
        wwProjectVO.setPeriodEndTime(project.getDeadLine());
        wwProjectVO.setBindTaskNum(1);
        //采集单总数
        wwProjectVO.setCollectNumber(1);
        //采集号
        wwProjectVO.setCollectNO(DateUtil.dateToString(new Date(), "yyMMddHHmmss"));
        //找到对应的订单
        if (StringUtil.isNotNull(project.getOrderId()) && !UUIDHelper.GUID_EMPTY.equals(project.getOrderId())) {
            DtoOrderForm orderForm = orderFormService.findOne(project.getOrderId());
            if (StringUtil.isNotNull(orderForm)) {
                List<DtoProject> projectList = projectService.findByOrderId(orderForm.getId());
                wwProjectVO.setBindTaskNum(projectList.size());
                //通过订单找到对应的合同
                Optional<DtoOrderContract> orderContract = orderContractService
                        .findByOrderIds(Collections.singletonList(orderForm.getId())).stream().findFirst();
                orderContract.ifPresent(p -> {
                    wwProjectVO.setContractName(p.getContractName());
                    wwProjectVO.setContractNature(p.getContractNature());
                    //合同附件
                    Optional<DtoDocument> document = documentService.findByObjectId(p.getId()).stream().findFirst();
                    document.ifPresent(d -> wwProjectVO.setContractAdd(d.getFilename()));
                    wwProjectVO.setInstitutionNameA(p.getFirstEntName());
                    wwProjectVO.setInstitutionNameB(p.getSecondEntName());
                    wwProjectVO.setLegalPersonA("");
                    wwProjectVO.setEnterpriseTypeA("");
                    if (StringUtil.isNotNull(p.getFirstEntId())) {
                        DtoEnterprise firstEnt = enterpriseService.findOne(p.getFirstEntId());
                        if (StringUtil.isNotNull(firstEnt)) {
                            wwProjectVO.setLegalPersonA(firstEnt.getCorporationName());
                            wwProjectVO.setEnterpriseTypeA(firstEnt.getRegTypeId());
                        }
                    }
                    //乙方法人 没有
                    wwProjectVO.setLegalPersonB("");
                    wwProjectVO.setEnterpriseTypeB(p.getSecondEntType());
                    wwProjectVO.setPeriodStartTime(p.getExcuteStartTime());
                    wwProjectVO.setPeriodEndTime(p.getExcuteEndTime());
                    wwProjectVO.setContractSum(p.getTotalAmount());
                    //收款
                    List<DtoRecAndPayRecord> recAndPayRecordList = recAndPayRecordService.findByContractIds(Collections.singletonList(p.getId()));
                    if (recAndPayRecordList.size() > 0) {
                        BigDecimal cumulativeTaskSum = BigDecimal.ONE;
                        for (DtoRecAndPayRecord dtoRecAndPayRecord : recAndPayRecordList) {
                            cumulativeTaskSum = cumulativeTaskSum.add(dtoRecAndPayRecord.getAmount());
                        }
                        wwProjectVO.setCumulativeTaskSum(cumulativeTaskSum);
                    }
                });
            }
        }
        return wwProjectVO;
    }

    @Override
    public void saveFolderPeriodWWInfo(ProjectVO projectVO) {
        DtoFolderPeriodWWInfo wwInfo = new DtoFolderPeriodWWInfo();
        wwInfo.setFolderId(projectVO.getFolderId());
        wwInfo.setPeriodCount(projectVO.getPeriodCount());
        wwInfo.setIssueTime(new Date());
        wwInfo.setTaskId(projectVO.getTaskId());
        wwInfo.setCollectNo(projectVO.getCollectNo());
        folderPeriodWWInfoService.save(wwInfo);
    }

    @Override
    public List<DtoSampleGroup> findBySampleId(String sampleId) {
        return sampleGroupService.findBySampleIds(Collections.singletonList(sampleId)).stream()
                .filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getSampleTypeGroupId())).collect(Collectors.toList());
    }

    @Override
    public List<WWFolderVO> findWWInfo(ProjectVO projectVO) {
        DtoFolderPeriodWWInfo wwInfo = folderPeriodWWInfoService.findByFolderIdAndPeriodCount(projectVO.getFolderId(),
                projectVO.getPeriodCount()).stream().findFirst().orElse(null);
        List<WWFolderVO> folderVOList = new ArrayList<>();
        if (StringUtil.isNotNull(wwInfo)) {
            List<DtoTcpData> tcpDataList = tcpDataService.findByTaskIdAndSampleId(wwInfo.getTaskId(), wwInfo.getCollectNo());
            List<DtoTcpData> addTcpDataList = new ArrayList<>();
            for (DtoTcpData tcpData : tcpDataList) {
                if (addTcpDataList.stream().noneMatch(p -> p.getId().equals(tcpData.getId()))) {
                    WWFolderVO folderVO = new WWFolderVO();
                    folderVO.setDevNum(tcpData.getWwDevNum());
                    folderVO.setDevName(tcpData.getWwDevName());
                    folderVO.setProjectCode(tcpData.getWwProNo());
                    folderVO.setProjectName(tcpData.getWwProName());
                    folderVO.setEnterpriseName(tcpData.getWwEnterpriseName());
                    folderVO.setParseTime(DateUtil.dateToString(tcpData.getParseDataTime(), DateUtil.FULL));
                    List<DtoTcpData> dataList = tcpDataList.stream().filter(p -> p.getWwDevNum().equals(tcpData.getWwDevNum())
                            && p.getWwDevName().equals(tcpData.getWwDevName()) && p.getWwProNo().equals(tcpData.getWwProNo())
                            && p.getWwProName().equals(tcpData.getWwProName()) && p.getWwEnterpriseName().equals(tcpData.getWwEnterpriseName())).collect(Collectors.toList());
                    folderVO.setTcpDataList(dataList);
                    folderVOList.add(folderVO);
                    addTcpDataList.addAll(dataList);
                }
            }
        }
        return folderVOList;
    }

    @Override
    public void syncParamsInfo(ProjectVO projectVO) {
        List<DtoParamsData> paramsDataList = paramsDataService.findBySampleIds(Collections.singletonList(projectVO.getSampleId()));
        //找到对应的配置仪器
        DtoOcrConfig ocrConfig = ocrConfigService.findByCode(projectVO.getWwFolderVO().getDevNum()).stream().findFirst().orElse(null);
        if (StringUtil.isNotNull(ocrConfig)) {
            //对应仪器的参数
            List<DtoOcrConfigParam> paramList = ocrConfigParamService.findByConfigId(ocrConfig.getId());
            //同步参数数据或者分组参数数据
            Set<String> paramsNameList = paramList.stream().filter(p -> EnumLIM.EnumOcrConfigParamType.样品参数.getValue().equals(p.getParamType()))
                    .map(DtoOcrConfigParam::getParamNameAlias).collect(Collectors.toSet());
            List<DtoTcpData> tcpDataList = projectVO.getWwFolderVO().getTcpDataList().stream()
                    .filter(p -> paramsNameList.contains(p.getParamName())).collect(Collectors.toList());
            setParamsDataValue(tcpDataList, paramList, paramsDataList, UUIDHelper.GUID_EMPTY);
            //同步现场数据值
            Set<String> testParamsList = paramList.stream().filter(p -> EnumLIM.EnumOcrConfigParamType.现场数据.getValue().equals(p.getParamType()))
                    .map(DtoOcrConfigParam::getParamNameAlias).collect(Collectors.toSet());
            List<DtoTcpData> testTcpDataList = projectVO.getWwFolderVO().getTcpDataList().stream()
                    .filter(p -> testParamsList.contains(p.getParamName())).collect(Collectors.toList());
            //如果分组id不是空的
            List<DtoAnalyseData> analyseDataList = analyseDataService.findDataBySampleIds(Collections.singletonList(projectVO.getSampleId()))
                    .stream().filter(p -> p.getIsCompleteField() && !p.getIsOutsourcing()).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(projectVO.getGroupId()) && !UUIDHelper.GUID_EMPTY.equals(projectVO.getGroupId())) {
                DtoSampleGroup sampleGroup = sampleGroupService.findOne(projectVO.getGroupId());
                if (!UUIDHelper.GUID_EMPTY.equals(sampleGroup.getSampleTypeGroupId())) {
                    setParamsDataValue(tcpDataList, paramList, paramsDataList, sampleGroup.getSampleTypeGroupId());
                    List<String> testIds = sampleGroup2TestService.findByGroupId(sampleGroup.getSampleTypeGroupId())
                            .stream().map(DtoSampleGroup2Test::getTestId).collect(Collectors.toList());
                    analyseDataList = analyseDataList.stream().filter(p -> testIds.contains(p.getTestId())).collect(Collectors.toList());
                }
            }
            for (DtoTcpData tcpData : testTcpDataList) {
                analyseDataList.forEach(p -> {
                    p.setTestValue(tcpData.getParamvalue());
                });
            }
            paramsDataService.saveAsync(paramsDataList);
        }
    }

    private void setParamsDataValue(List<DtoTcpData> tcpDataList, List<DtoOcrConfigParam> paramList,
                                    List<DtoParamsData> paramsDataList, String groupId) {
        for (DtoTcpData tcpData : tcpDataList) {
            Optional<DtoOcrConfigParam> configParamOptional = paramList.stream()
                    .filter(p -> EnumLIM.EnumOcrConfigParamType.样品参数.getValue().equals(p.getParamType())
                            && p.getParamNameAlias().equals(tcpData.getParamName())).findFirst();
            if (configParamOptional.isPresent()) {
                //参数相同且分组数据的进行更新
                Optional<DtoParamsData> paramsDataOptional = paramsDataList.stream()
                        .filter(p -> p.getParamsName().contains(configParamOptional.get().getParamName())
                                && p.getGroupId().equals(groupId)).findFirst();
                paramsDataOptional.ifPresent(dtoParamsData -> dtoParamsData.setParamsValue(tcpData.getParamvalue()));
            }
        }
    }

    @Autowired
    @Lazy
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    @Autowired
    @Lazy
    public OrderFormService setOrderFormService(OrderFormService orderFormService) {
        return this.orderFormService = orderFormService;
    }

    @Autowired
    @Lazy
    public OrderContractService setOrderContractService(OrderContractService orderContractService) {
        return this.orderContractService = orderContractService;
    }

    @Autowired
    @Lazy
    public DocumentService setDocumentService(DocumentService documentService) {
        return this.documentService = documentService;
    }

    @Autowired
    @Lazy
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }

    @Autowired
    @Lazy
    public void setRecAndPayRecordService(RecAndPayRecordService recAndPayRecordService) {
        this.recAndPayRecordService = recAndPayRecordService;
    }

    @Autowired
    @Lazy
    public void setFolderPeriodWWInfoService(FolderPeriodWWInfoService folderPeriodWWInfoService) {
        this.folderPeriodWWInfoService = folderPeriodWWInfoService;
    }

    @Autowired
    @Lazy
    public void setTcpDataService(TcpDataService tcpDataService) {
        this.tcpDataService = tcpDataService;
    }

    @Autowired
    @Lazy
    public void setSampleGroupService(SampleGroupService sampleGroupService) {
        this.sampleGroupService = sampleGroupService;
    }

    @Autowired
    @Lazy
    public void setParamsDataService(ParamsDataService paramsDataService) {
        this.paramsDataService = paramsDataService;
    }

    @Autowired
    @Lazy
    public void setOcrConfigService(OcrConfigService ocrConfigService) {
        this.ocrConfigService = ocrConfigService;
    }

    @Autowired
    @Lazy
    public void setOcrConfigParamService(OcrConfigParamService ocrConfigParamService) {
        this.ocrConfigParamService = ocrConfigParamService;
    }

    @Autowired
    @Lazy
    public void setSampleGroup2TestService(SampleGroup2TestService sampleGroup2TestService) {
        this.sampleGroup2TestService = sampleGroup2TestService;
    }

    @Autowired
    @Lazy
    public void setAnalyseDataService(AnalyseDataService analyseDataService) {
        this.analyseDataService = analyseDataService;
    }
}
