-- 环境院项目推送迁移产品相关表
CREATE TABLE TB_PRO_SHSamplingInstrument
(
    id           varchar(50) NOT NULL,
    taskId       varchar(50) NOT NULL DEFAULT '',
    instrumentId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
);
ALTER TABLE TB_PRO_SHSamplingInstrument ADD CONSTRAINT PRIMARY KEY (id);
COMMENT ON TABLE TB_PRO_SHSamplingInstrument IS '监管平台采样仪器配置';
COMMENT ON COLUMN TB_PRO_SHSamplingInstrument.id IS 'id';
COMMENT ON COLUMN TB_PRO_SHSamplingInstrument.taskId IS '采样任务id';
COMMENT ON COLUMN TB_PRO_SHSamplingInstrument.instrumentId IS '仪器id';

CREATE TABLE TB_PRO_SHSamplingPerson
(
    id       varchar(50) NOT NULL,
    taskId   varchar(50) NOT NULL DEFAULT '',
    personId varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
);
ALTER TABLE TB_PRO_SHSamplingPerson ADD CONSTRAINT PRIMARY KEY (id);
COMMENT ON TABLE TB_PRO_SHSamplingPerson IS '监管平台采样人员配置';
COMMENT ON COLUMN TB_PRO_SHSamplingPerson.id IS 'id';
COMMENT ON COLUMN TB_PRO_SHSamplingPerson.taskId IS '采样任务id';
COMMENT ON COLUMN TB_PRO_SHSamplingPerson.personId IS '人员id';

CREATE TABLE TB_PRO_ProjectContract
(
    id            varchar(50) NOT NULL,
    contractId    varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    projectId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    contractName  varchar(50) NULL DEFAULT NULL,
    taskPrice     decimal(18, 2) NULL,
    taskTypeId    varchar(50) NULL,
    taskType      varchar(50) NULL DEFAULT NULL,
    isSampleId    varchar(50) NULL DEFAULT NULL,
    isSample      varchar(20) NULL DEFAULT NULL,
    taskSourceId  varchar(50) NULL,
    taskSource    varchar(20) NULL DEFAULT NULL,
    taskLocation  varchar(1000) NULL DEFAULT NULL,
    sampleContact varchar(50) NULL DEFAULT NULL,
    contactPhone  varchar(20) NULL DEFAULT NULL,
    taskAddress   varchar(50) NULL DEFAULT NULL,
    fileExplain   varchar(255) NULL DEFAULT NULL,
    taskContent   text NULL,
    isPush        int NULL DEFAULT 0,
    pId           varchar(50),
    hasPush       int NULL DEFAULT 0,
    isHandle      int NULL DEFAULT 0,
    schemeHasPush int NULL DEFAULT 0,
    planHasPush   int NULL DEFAULT 0,
    reportHasPush int NULL DEFAULT 0
);
ALTER TABLE TB_PRO_ProjectContract ADD CONSTRAINT PRIMARY KEY (id);
COMMENT ON COLUMN TB_PRO_ProjectContract.id IS '主键';
COMMENT ON COLUMN TB_PRO_ProjectContract.contractId IS '合同id';
COMMENT ON COLUMN TB_PRO_ProjectContract.projectId IS '项目id';
COMMENT ON COLUMN TB_PRO_ProjectContract.contractName IS '合同名称';
COMMENT ON COLUMN TB_PRO_ProjectContract.taskPrice IS '任务金额';
COMMENT ON COLUMN TB_PRO_ProjectContract.taskTypeId IS '任务类型id';
COMMENT ON COLUMN TB_PRO_ProjectContract.taskType IS '任务类型';
COMMENT ON COLUMN TB_PRO_ProjectContract.isSampleId IS '是否采样id';
COMMENT ON COLUMN TB_PRO_ProjectContract.isSample IS '是否采样';
COMMENT ON COLUMN TB_PRO_ProjectContract.taskSourceId IS '任务来源id';
COMMENT ON COLUMN TB_PRO_ProjectContract.taskSource IS '任务来源';
COMMENT ON COLUMN TB_PRO_ProjectContract.taskLocation IS '任务所在地';
COMMENT ON COLUMN TB_PRO_ProjectContract.sampleContact IS '采样联系人';
COMMENT ON COLUMN TB_PRO_ProjectContract.contactPhone IS '联系电话';
COMMENT ON COLUMN TB_PRO_ProjectContract.taskAddress IS '任务地址';
COMMENT ON COLUMN TB_PRO_ProjectContract.fileExplain IS '附件说明';
COMMENT ON COLUMN TB_PRO_ProjectContract.taskContent IS '任务概述';
COMMENT ON COLUMN TB_PRO_ProjectContract.isPush IS '是否推送（0：否，1：是）';
COMMENT ON COLUMN TB_PRO_ProjectContract.pId IS '上海监测站项目id';
COMMENT ON COLUMN TB_PRO_ProjectContract.hasPush IS '是否已经推送（0：否，1：是）';
COMMENT ON COLUMN TB_PRO_ProjectContract.isHandle IS '是否处理（0：否，1：是）';
COMMENT ON COLUMN TB_PRO_ProjectContract.schemeHasPush IS '方案是否已经推送（0：否，1：是）';
COMMENT ON COLUMN TB_PRO_ProjectContract.planHasPush IS '计划是否已经推送（0：否，1：是）';
COMMENT ON COLUMN TB_PRO_ProjectContract.reportHasPush IS '报告是否已经推送（0：否，1：是）';

-- 环境院项目推送迁移产品相关字段
-- TB_BASE_Enterprise
ALTER TABLE TB_BASE_Enterprise ADD COLUMN regulateId varchar(50) NULL DEFAULT NULL;
ALTER TABLE TB_BASE_Enterprise ADD COLUMN regulateName varchar(50) NULL DEFAULT NULL;
ALTER TABLE TB_BASE_Enterprise ADD COLUMN socialCode varchar(50) NULL DEFAULT NULL;
ALTER TABLE TB_BASE_Enterprise ADD COLUMN pollutionCode varchar(50) NULL DEFAULT NULL;
COMMENT ON COLUMN TB_BASE_Enterprise.regulateId IS '监管平台客户id';
COMMENT ON COLUMN TB_BASE_Enterprise.regulateName IS '监管平台客户名称';
COMMENT ON COLUMN TB_BASE_Enterprise.socialCode IS '社会信用代码';
COMMENT ON COLUMN TB_BASE_Enterprise.pollutionCode IS '污染源编号';
-- TB_BASE_Instrument
ALTER TABLE TB_BASE_Instrument ADD COLUMN regulateId varchar(50) NULL DEFAULT NULL;
ALTER TABLE TB_BASE_Instrument ADD COLUMN regulateName varchar(50) NULL DEFAULT NULL;
ALTER TABLE TB_BASE_Instrument ADD COLUMN orderNum int NOT NULL DEFAULT 0;
COMMENT ON COLUMN TB_BASE_Instrument.regulateId IS '监管平台仪器id';
COMMENT ON COLUMN TB_BASE_Instrument.regulateName IS '监管平台仪器名称';
COMMENT ON COLUMN TB_BASE_Instrument.orderNum IS '排序值';
-- TB_LIM_Test
ALTER TABLE TB_LIM_Test ADD COLUMN shMethodId varchar(50) NULL DEFAULT '';
ALTER TABLE TB_LIM_Test ADD COLUMN shMethodName varchar(255) NULL DEFAULT '';
ALTER TABLE TB_LIM_Test ADD COLUMN shSamplingMethodId varchar(50) NULL DEFAULT '';
ALTER TABLE TB_LIM_Test ADD COLUMN shSamplingMethodName varchar(255) NULL DEFAULT '';
COMMENT ON COLUMN TB_LIM_Test.shMethodId IS '监管平台分析项目id';
COMMENT ON COLUMN TB_LIM_Test.shMethodName IS '监管平台分析项目名称';
COMMENT ON COLUMN TB_LIM_Test.shSamplingMethodId IS '监管平台采样方法id';
COMMENT ON COLUMN TB_LIM_Test.shSamplingMethodName IS '监管平台采样方法名称';
-- TB_LIM_Person
ALTER TABLE TB_LIM_Person ADD COLUMN regulateId varchar(50) NULL DEFAULT NULL;
ALTER TABLE TB_LIM_Person ADD COLUMN regulateName varchar(50) NULL DEFAULT NULL;
COMMENT ON COLUMN TB_LIM_Person.regulateId IS '监管平台id';
COMMENT ON COLUMN TB_LIM_Person.regulateName IS '监管平台名称';
-- TB_PRO_OrderContract
ALTER TABLE TB_PRO_OrderContract ADD COLUMN shanghaiEntId varchar(50) NOT NULL DEFAULT '';
ALTER TABLE TB_PRO_OrderContract ADD COLUMN shanghaiEntName varchar(255) NOT NULL DEFAULT '';
ALTER TABLE TB_PRO_OrderContract ADD COLUMN isHavingPut bit NOT NULL DEFAULT 0;
ALTER TABLE TB_PRO_OrderContract ADD COLUMN cId varchar(50) NULL DEFAULT '';
COMMENT ON COLUMN TB_PRO_OrderContract.shanghaiEntId IS '监管平台甲方企业id';
COMMENT ON COLUMN TB_PRO_OrderContract.shanghaiEntName IS '监管平台甲方企业名称';
COMMENT ON COLUMN TB_PRO_OrderContract.isHavingPut IS '是否已经推送';
COMMENT ON COLUMN TB_PRO_OrderContract.cId IS '合同id';
-- TB_PRO_Project
ALTER TABLE TB_PRO_Project ADD COLUMN environmentCode varchar(50) NULL DEFAULT NULL;
ALTER TABLE TB_PRO_Project ADD COLUMN addressName varchar(300) NULL DEFAULT NULL;
ALTER TABLE TB_PRO_Project ADD COLUMN isCMA int NULL DEFAULT NULL;
ALTER TABLE TB_PRO_Project ADD COLUMN regulateReportType VARCHAR(50) NULL;
ALTER TABLE TB_PRO_Project ADD COLUMN pollutionCode varchar(50) NULL DEFAULT NULL;
COMMENT ON COLUMN TB_PRO_Project.environmentCode IS '环境院编号';
COMMENT ON COLUMN TB_PRO_Project.addressName IS '任务所在区名称';
COMMENT ON COLUMN TB_PRO_Project.isCMA IS '是否加盖CMA章';
COMMENT ON COLUMN TB_PRO_Project.regulateReportType IS '报告类型';
COMMENT ON COLUMN TB_PRO_Project.pollutionCode IS '污染源编号';
-- TB_PRO_Report
ALTER TABLE TB_PRO_Report ADD COLUMN isCMA int NULL DEFAULT NULL;
ALTER TABLE TB_PRO_Report ADD COLUMN regulateReportType varchar(50) NULL DEFAULT NULL;
ALTER TABLE TB_PRO_Report ADD COLUMN regulateCode varchar(50) NULL DEFAULT NULL;
COMMENT ON COLUMN TB_PRO_Report.isCMA IS '是否加盖CMA章';
COMMENT ON COLUMN TB_PRO_Report.regulateReportType IS '监管平台报告类型，多个id用英文逗号分隔';
COMMENT ON COLUMN TB_PRO_Report.regulateCode IS '监管平台系统编号';