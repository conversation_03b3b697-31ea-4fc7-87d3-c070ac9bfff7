package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class DtoImportPersonExtend {
    @Excel(name = "部门",orderNum = "1",width = 17)
    private String deptName;

    @Excel(name = "角色",orderNum = "2",width = 17)
    private String roleName;

    @Excel(name = "职务",orderNum = "3",width = 17)
    private String post;

    @Excel(name = "职称",orderNum = "4",width = 17)
    private String title;

    @Excel(name = "学历",orderNum = "5",width = 17)
    private String education;
}
