package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * ParamsPartFormula数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
public interface ParamsPartFormulaRepository extends IBaseJpaPhysicalDeleteRepository<DtoParamsPartFormula, String>, LimsRepository<DtoParamsPartFormula, String> {


    /**
     * 根据公式id 获取相关的部分公式数据
     *
     * @param formulaIds 公式ids
     * @return 返回部分公式数据
     */
    List<DtoParamsPartFormula> findByFormulaIdIn(List<String> formulaIds);

    /**
     * 根据公式id 获取相关的部分公式数据
     *
     * @param formulaId 公式id
     * @return 返回部分公式数据
     */
    List<DtoParamsPartFormula> findByFormulaId(String formulaId);

    /**
     * 删除关联数据
     *
     * @param formulaId 测试公式id
     * @return 返回删除数量
     */
    @Transactional
    @Modifying
    @Query("delete from DtoParamsPartFormula p where p.formulaId = :formulaId")
    Integer deleteByObjId(@Param("formulaId") String formulaId);

    @Transactional
    @Modifying
    Integer deleteAllByFormulaIdAndFormulaType(String formulaId, Integer type);


    /**
     * 删除关联数据
     *
     * @param formulaIds 测试公式id
     * @return 返回删除数量
     */
    @Transactional
    @Modifying
    @Query("delete from DtoParamsPartFormula p where p.formulaId in :formulaIds")
    Integer deleteByObjIdIn(@Param("formulaIds") List<String> formulaIds);
}