-- 修改审核时间字段默认值及注释
ALTER TABLE tb_qa_planinternalaudit
    MODIFY auditTime DATETIME DEFAULT '1753-01-01 00:00:00';
COMMENT ON COLUMN tb_qa_planinternalaudit.auditTime IS '审核时间';

-- 新增评审组长字段
ALTER TABLE tb_qa_planinternalaudit
    ADD auditLeader VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL;
COMMENT ON COLUMN tb_qa_planinternalaudit.auditLeader IS '评审组长';

-- 新增审核日期字段
ALTER TABLE tb_qa_planinternalaudit
    ADD auditTimeStr VARCHAR(2000) DEFAULT '' NOT NULL;
COMMENT ON COLUMN tb_qa_planinternalaudit.auditTimeStr IS '审核日期';

ALTER TABLE tb_qa_planinternalaudit
    ADD auditLocation VARCHAR(2000) DEFAULT '' NOT NULL;
COMMENT ON COLUMN tb_qa_planinternalaudit.auditLocation IS '审核地点';

ALTER TABLE tb_qa_planinternalaudit
    ADD workPlan VARCHAR(2000) DEFAULT '' NOT NULL;
COMMENT ON COLUMN tb_qa_planinternalaudit.workPlan IS '工作计划';

-- 仪器表新增多检测器标记
ALTER TABLE tb_base_instrument
    ADD isManyDetector BIT DEFAULT 0 NOT NULL;
COMMENT ON COLUMN tb_base_instrument.isManyDetector IS '是否多检测器';

-- 检定校准记录表新增检测器ID
ALTER TABLE TB_LIM_InstrumentCheckRecord
    ADD instrument2DetectorId VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL;
COMMENT ON COLUMN TB_LIM_InstrumentCheckRecord.instrument2DetectorId IS '仪器关联多检测器id';

-- 年度计划表新增检测器相关字段
ALTER TABLE TB_QA_YearlyInstrumentConfirmPlan
    ADD instrument2DetectorId VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL;
COMMENT ON COLUMN TB_QA_YearlyInstrumentConfirmPlan.instrument2DetectorId IS '仪器关联多检测器id';

ALTER TABLE TB_QA_YearlyInstrumentConfirmPlan
    ADD detectorName VARCHAR(255) NOT NULL DEFAULT '';
COMMENT ON COLUMN TB_QA_YearlyInstrumentConfirmPlan.detectorName IS '检测器名称';

-- 创建仪器多检测器关联表
CREATE TABLE TB_BASE_Instrument2Detector (
             id            VARCHAR(50) PRIMARY KEY,
             instrumentId  VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
             detectorName  VARCHAR(255) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
             originCyc     DECIMAL(18, 1) DEFAULT 12.0 NOT NULL,
             originType    INT DEFAULT -1 NOT NULL,
             originUnit    VARCHAR(100) DEFAULT '',
             originDate    DATETIME DEFAULT '1753-01-01 00:00:00' NOT NULL,
             originEndDate DATETIME DEFAULT '1753-01-01 00:00:00' NOT NULL,
             originResult  INT DEFAULT 1 NOT NULL,
             originRemark  VARCHAR(1000) DEFAULT '',
             isDeleted     BIT DEFAULT 0 NOT NULL,
             orgId         VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
             creator       VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
             createDate    DATETIME DEFAULT SYSDATE NOT NULL,
             domainId      VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
             modifier      VARCHAR(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL,
             modifyDate    DATETIME DEFAULT SYSDATE NOT NULL
);
COMMENT ON TABLE TB_BASE_Instrument2Detector IS '仪器关联多检测器溯源';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.id IS 'id';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.instrumentId IS '仪器Id';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.detectorName IS '检测器名称';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.originCyc IS '溯源周期(月)';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.originType IS '溯源方式(枚举：1检定、2校准、3自校)';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.originUnit IS '溯源单位';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.originDate IS '最近日期（溯源）';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.originEndDate IS '过期日期（溯源）';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.originResult IS '溯源结果(1合格、0不合格)';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.originRemark IS '溯源备注';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.isDeleted IS '是否删除';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.orgId IS '组织机构id';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.creator IS '创建人';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.createDate IS '创建时间';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.domainId IS '所属实验室';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.modifier IS '修改人';
COMMENT ON COLUMN TB_BASE_Instrument2Detector.modifyDate IS '修改时间';