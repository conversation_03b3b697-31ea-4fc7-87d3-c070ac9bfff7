package com.sinoyd.lims.foreign.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.foreign.DataCurveVO;
import com.sinoyd.lims.foreign.service.DataCurveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 曲线信息服务
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Api(tags = "示例: dataCurve服务")
@RestController
@RequestMapping("api/pro/foreign/dataCurve")
public class DataCurveController extends ExceptionHandlerController<DataCurveService> {

    /**
     * 曲线信息数据同步
     *
     * @param map 同步时间
     * @return RestResponse<List < DataAnalyseDataVO>>
     */
    @ApiOperation(value = "数据同步", notes = "数据同步")
    @PostMapping("/syncData")
    public RestResponse<List<DataCurveVO>> getSyncData(@RequestBody Map<String, Object> map) {
        List<DataCurveVO> curveVOList = service.getSyncData( map);
        RestResponse<List<DataCurveVO>> response = new RestResponse();
        response.setData(curveVOList);
        return response;
    }
}
