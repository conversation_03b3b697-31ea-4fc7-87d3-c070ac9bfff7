package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 报告批量配置
 * <AUTHOR>
 * @version V1.0.0 2020/3/20
 * @since V100R001
 */
@Data
public class DtoEvaluationBatch {
    /**
     * 类型
     */
    private Integer objectType;

    /**
     * 点位计划
     */
    private Integer folderPlan;
    /**
     * 关联id集合
     */
    private List<String> objectIds = new ArrayList<>();
    /**
     * 标准
     */
    private List<DtoEvaluationValue> evaluationValue = new ArrayList<>();
}
