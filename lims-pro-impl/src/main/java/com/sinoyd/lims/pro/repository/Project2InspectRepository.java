package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoProject2Inspect;

import java.util.List;

/**
 * Project2InspectRepository数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface Project2InspectRepository extends IBaseJpaPhysicalDeleteRepository<DtoProject2Inspect, String> {
    /**
     * 根据项目ids 查询
     *
     * @param projectIds 项目ids
     * @return 核查对象集合
     */
    List<DtoProject2Inspect> findByProjectIdIn(List<String> projectIds);

    /**
     * 根据项目id 查询
     *
     * @param id 项目id
     * @return 核查对象
     */
    DtoProject2Inspect findByProjectId(String id);
}
