package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ProjectPushInfo实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "ProjectPushInfo")
@Data
public class ProjectPushInfo implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public ProjectPushInfo() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.NewID();

    /**
     * 项目Id
     */
    @Column
    @ApiModelProperty("项目Id")
    private String projectId;

    /**
     * 推送时间
     */
    @Column
    @ApiModelProperty("推送时间")
    private Date pushTime;

    /**
     * 组织机构id
     */
    @Column
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

}