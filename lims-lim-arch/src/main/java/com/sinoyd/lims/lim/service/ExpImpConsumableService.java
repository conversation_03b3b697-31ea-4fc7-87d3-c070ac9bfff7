package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpConsumable;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 消耗品导入导出接口
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
public interface ExpImpConsumableService extends ImportExcelService<DtoExpImpConsumable, DtoConsumable> {
    /**
     * 导出
     *
     * @param criteria   请求参数
     * @param response   响应体
     * @param sheetNames sheet页名称
     * @param fileName   附件名称
     */
    void export(BaseCriteria criteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName);
}
