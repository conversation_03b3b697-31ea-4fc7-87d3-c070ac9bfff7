package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
 * 试剂配置记录信息
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Data
public class DataWorksheetReagentVO {

    /**
     * id
     */
    private String id;

    /**
     * 工作单id
     */
    private String workSheetFolderId;

    /**
     * 试剂配置记录id
     */
    private String reagentConfigId;

    /**
     * 配置记录
     */
    private String reagent;

    /**
     * 需求的配置过程
     */
    private String context;

    /**
     * 试剂名称
     */
    private String reagentName;

    /**
     * 试剂规格
     */
    private String reagentSpecification;

    /**
     * 配置溶液
     */
    private String configurationSolution;

    /**
     * 配置日期
     */
    private Date configDate;

    /**
     * 有效期
     */
    private Date expiryDate;

    /**
     * 稀释过程记录
     */
    private String course;

    /**
     * 稀释液
     */
    private String diluent;

    /**
     * 试剂类型
     */
    private Integer reagentType;

    /**
     * 适用项目
     */
    private String suitItem;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
