package com.sinoyd.lims.monitor.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.monitor.criteria.FixedpointCriteria;
import com.sinoyd.lims.monitor.dto.customer.DtoExpImpFixedPointWR;
import com.sinoyd.lims.monitor.dto.customer.DtoImportFixedExpand;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPointExpend;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.dto.rcc.DtoStation;
import com.sinoyd.lims.monitor.enums.EnumMonitor;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.monitor.repository.rcc.StationRepository;
import com.sinoyd.lims.monitor.service.ExportFixedPointWRService;
import com.sinoyd.lims.monitor.service.FixedpointService;
import com.sinoyd.lims.monitor.verify.ImpModifyFixedPointWRVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 污染源点位导入导出接口实现
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Service
public class ExportFixedPointWRServiceImpl extends BaseJpaServiceImpl<DtoFixedpoint, String, FixedpointRepository> implements ExportFixedPointWRService {

    private ImportUtils importUtils;

    private CodeService codeService;

    private FixedpointService fixedpointService;

    private StationRepository stationRepository;

    private EnterpriseRepository enterpriseRepository;

    private SampleTypeRepository sampleTypeRepository;

    private AreaService areaService;


    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        List<DtoFixedpoint> fixedpointList = getData(baseCriteria);

        // 获取所有的检测类型
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll();
        Map<String, DtoSampleType> sampleTypeMap = sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, p -> p));

        // 获取所有的控制等级
        List<DtoCode> controlLevels = codeService.findCodes(LimConstants.ImportConstants.CONTROL_LEVEL);

        List<String> fixedIds = fixedpointList.stream().map(DtoFixedpoint::getId).collect(Collectors.toList());
        List<DtoFixedpoint> fixedpointServiceAll = fixedpointService.findAll(fixedIds);
        Map<String, DtoFixedpoint> fixedpointMap = fixedpointServiceAll.stream().collect(Collectors.toMap(DtoFixedpoint::getId, p -> p));

        List<DtoExpImpFixedPointWR> expImpFixedPointWRS = new ArrayList<>();
        for (DtoFixedpoint fixedpoint : fixedpointList) {
            DtoExpImpFixedPointWR expImpFixedPointWR = new DtoExpImpFixedPointWR();
            BeanUtils.copyProperties(fixedpoint, expImpFixedPointWR);
            expImpFixedPointWR.setEnabledStatus(fixedpoint.getIsEnabled() ? "是" : "否");
            // 获取系统中的点位数据
            DtoFixedpoint dtoFixedpoint = fixedpointMap.getOrDefault(fixedpoint.getId(), new DtoFixedpoint());

            Optional<DtoCode> codeOptional = controlLevels.stream().filter(p -> p.getDictCode().equals(dtoFixedpoint.getLevel())).findFirst();
            codeOptional.ifPresent(p -> expImpFixedPointWR.setLevelName(p.getDictName()));
            expImpFixedPointWR.setLon(dtoFixedpoint.getLon());
            expImpFixedPointWR.setLat(dtoFixedpoint.getLat());
            expImpFixedPointWR.setRemark(dtoFixedpoint.getRemark());
            DtoSampleType dtoSampleType = sampleTypeMap.containsKey(fixedpoint.getSampleTypeId()) ? sampleTypeMap.get(fixedpoint.getSampleTypeId()) : new DtoSampleType();
            expImpFixedPointWR.setSampleTypeName(dtoSampleType.getTypeName());
            expImpFixedPointWRS.add(expImpFixedPointWR);
        }

        // 获取点位环境质量的点位类型
        List<DtoImportFixedExpand> pointTypes = findAllPointType(LimConstants.ImportConstants.FIXED_POINT_TYPE_WR);
        // 设置点位类型数据
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpFixedPointWR.class, DtoImportFixedExpand.class, expImpFixedPointWRS, pointTypes);
        // 设置下拉框
        //点位类型(必填)
        String[] pointTypeWRs = codeService.findCodes(LimConstants.ImportConstants.FIXED_POINT_TYPE_WR)
                .stream().map(DtoCode::getDictName).toArray(String[]::new);
        //所属测站(必填)
        String[] stations = stationRepository.findAll().stream().map(DtoStation::getStname).toArray(String[]::new);
        // 等级
        String[] controls = controlLevels.stream().map(DtoCode::getDictName).toArray(String[]::new);
        //启用状态(是,否)
        String[] status = new String[]{"是", "否"};
        // 检测类型
        String[] sampleSmallArray = getSampleSmallArray(sampleTypes);

        importUtils.selectList(workBook, 1, 1, pointTypeWRs);
        importUtils.selectList(workBook, 5, 5, stations);
        importUtils.selectList(workBook, 6, 6, controls);
        importUtils.selectList(workBook, 7, 7, sampleSmallArray);
        importUtils.selectList(workBook, 11, 11, status);
        // 下载Excel返回响应流
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    public List<DtoFixedpoint> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        //校验文件类型
        PoiExcelUtils.verifyFileType(file);
        //获取业务参数
        //获取所有的检测类型
        List<DtoSampleType> sampleTypes = sampleTypeRepository.findAll();
        //获取所有的企业
        List<DtoEnterprise> enterpriseList = enterpriseRepository.findAll();
        //获取所有的测站
        List<DtoStation> stationList = stationRepository.findAll();
        //获取所有区域信息
        List<DtoArea> dbAreaList = areaService.findAll();
        //获取所有的控制等级
        List<DtoCode> controlLevels = codeService.findCodes(LimConstants.ImportConstants.CONTROL_LEVEL);
        //获取所有的点位类型（污染源）
        List<DtoCode> pointTypeWR = codeService.findCodes(LimConstants.ImportConstants.FIXED_POINT_TYPE_WR);
        Map<String, List<DtoCode>> codeMap = new HashMap<>();
        codeMap.put(LimConstants.ImportConstants.CONTROL_LEVEL, controlLevels);
        codeMap.put(LimConstants.ImportConstants.FIXED_POINT_TYPE_WR, pointTypeWR);
        List<DtoFixedpoint> fixedpoints = repository.findAll();
        // 初始化校验器
        ImpModifyFixedPointWRVerify impModifyFixedPointWRVerify = new ImpModifyFixedPointWRVerify(stationList, dbAreaList, codeMap, enterpriseList, sampleTypes, fixedpoints);

        //获取导入校验后的数据
        ExcelImportResult<DtoExpImpFixedPointWR> excelData = getExcelData(impModifyFixedPointWRVerify, file, response);
        List<DtoExpImpFixedPointWR> list = excelData.getList();
        list.removeIf(p -> StringUtil.isEmpty(p.getPointName()));
        if (StringUtil.isEmpty(list)) {
            throw new BaseException("文件中无数据或者上传文件模板错误，请检查后重新上传");
        }
        List<String> ids = list.stream().map(DtoExpImpFixedPointWR::getId).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        List<DtoFixedpoint> fixedpointList = repository.findAll(ids);

        //转换实体
        List<DtoFixedpoint> fixedPoints = importToEntity(list, controlLevels, pointTypeWR, fixedpointList);
        //获取点位拓展数据
        List<DtoFixedPointExpend> fixedPointExpends = handleFixedPointExpand(fixedPoints);
        //保存数据
        addData(fixedPoints);
        return fixedPoints;
    }

    @Override
    public void addData(List<DtoFixedpoint> data) {
        if (StringUtil.isNotEmpty(data)) {
            repository.save(data);
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpFixedPointWR> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    @Override
    public ExcelImportResult<DtoExpImpFixedPointWR> getExcelData(IExcelVerifyHandler<DtoExpImpFixedPointWR> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        //设置校验handle
        params.setVerifyHandler(verifyHandler);
        ExcelImportResult<DtoExpImpFixedPointWR> result = ExcelImportUtil.importExcelMore(file.getInputStream(), DtoExpImpFixedPointWR.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "企业信息");
            PoiExcelUtils.downLoadExcel("企业导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }

    /**
     * 数据源获取
     *
     * @param baseCriteria 查询条件
     * @return DtoFixedpoint 集合
     */
    private List<DtoFixedpoint> getData(BaseCriteria baseCriteria) {
        PageBean<DtoFixedpoint> page = new PageBean<>();
        FixedpointCriteria criteria = (FixedpointCriteria) baseCriteria;
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        fixedpointService.findByPage(page, criteria);
        return page.getData();
    }

    /**
     * 获取检测类型数组
     *
     * @param sampleTypes
     * @return
     */
    private String[] getSampleSmallArray(List<DtoSampleType> sampleTypes) {

        // 筛选检测类型大类
        List<DtoSampleType> bigSampleTypes = sampleTypes.stream().filter(p -> p.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue()))
                .sorted(Comparator.comparing(DtoSampleType::getOrderNum).reversed()).collect(Collectors.toList());

        List<String> result = new ArrayList<>();
        for (DtoSampleType dtoSampleType : bigSampleTypes) {
            List<DtoSampleType> smallSampleTypes = sampleTypes.stream().filter(p -> dtoSampleType.getId().equals(p.getParentId())
                    && p.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型小类.getValue()))
                    .sorted(Comparator.comparing(DtoSampleType::getOrderNum).reversed()).collect(Collectors.toList());
            for (DtoSampleType smallSampleType : smallSampleTypes) {
                result.add(smallSampleType.getTypeName());
            }
        }
        return result.toArray(new String[0]);
    }


    /**
     * 获取拓展的点位类型
     *
     * @param codeValue 字典编码
     * @return 点位类型数据
     */
    private List<DtoImportFixedExpand> findAllPointType(String codeValue) {
        List<DtoImportFixedExpand> fixedExpands = new ArrayList<>();
        List<DtoCode> codes = codeService.findCodes(codeValue);
        if (StringUtil.isNotEmpty(codes)) {
            for (DtoCode code : codes) {
                DtoImportFixedExpand expand = new DtoImportFixedExpand();
                expand.setPointType(code.getDictName());
                fixedExpands.add(expand);
            }
        }
        return fixedExpands;
    }


    /**
     * 转换为需要保存的实体
     *
     * @param importList 导入的数据
     * @return 实体集合
     */
    private List<DtoFixedpoint> importToEntity(List<DtoExpImpFixedPointWR> importList,
                                               List<DtoCode> controlLevel,
                                               List<DtoCode> pointTypeWR,
                                               List<DtoFixedpoint> fixedpointList) {
        Map<String, DtoFixedpoint> fixedpointMap = fixedpointList.stream().collect(Collectors.toMap(DtoFixedpoint::getId, p -> p));

        //点位数据
        List<DtoFixedpoint> fixedPoints = new ArrayList<>();
        for (DtoExpImpFixedPointWR dto : importList) {
            DtoFixedpoint fixedPoint = new DtoFixedpoint();
            //设置类型
            dto.setPointType(EnumMonitor.EnumPointType.污染源.getValue());
            //设置污染源的点位类型
            if (StringUtil.isNotEmpty(pointTypeWR)) {
                Optional<DtoCode> pointTypeOp = pointTypeWR
                        .stream().filter(p -> dto.getFolderTypeName().equals(p.getDictName())).findFirst();
                pointTypeOp.ifPresent(p -> dto.setFolderType(p.getDictCode()));
            }

            //设置控制等级
            if (StringUtil.isNotEmpty(dto.getLevelName()) && StringUtil.isNotEmpty(controlLevel)) {
                Optional<DtoCode> controlLevelOp = controlLevel.stream().filter(p -> dto.getLevelName().equals(p.getDictName())).findFirst();
                controlLevelOp.ifPresent(p -> dto.setLevel(p.getDictCode()));
            }
            if (fixedpointMap.containsKey(dto.getId())) {
                fixedPoint = fixedpointMap.get(dto.getId());
            } else {
                dto.setId(UUIDHelper.NewID());
                //处理周期，次数默认为1
                fixedPoint.setCycleOrder(LimConstants.ImportConstants.DEFAULT_ORDER_NUM);
                fixedPoint.setTimesOrder(LimConstants.ImportConstants.DEFAULT_ORDER_NUM);
                fixedPoint.setEvaluationId("");
                fixedPoint.setEvaluationLevelId("");
                //根据所属区域名称获取区域数据
                String areaId = "";
                fixedPoint.setAreaId(areaId);
            }
            //赋值属性
            BeanUtils.copyProperties(dto, fixedPoint);
            //添加数据
            fixedPoints.add(fixedPoint);
        }
        return fixedPoints;
    }

    /**
     * 获取点位拓展数据
     *
     * @param fixedPoints 点位数据
     * @return 点位拓展数据
     */
    private List<DtoFixedPointExpend> handleFixedPointExpand(List<DtoFixedpoint> fixedPoints) {
        //点位拓展数据集合
        List<DtoFixedPointExpend> fixedPointExpends = new ArrayList<>();

        for (DtoFixedpoint fixedPoint : fixedPoints) {
            DtoFixedPointExpend expend = new DtoFixedPointExpend();
            //设置点位id
            expend.setFixedPointId(fixedPoint.getId());
            //设置水体id为空
            expend.setWaterId("");
            //当是污染源点位导入时,设置设备启用时间与设备运投日期
            expend.setCraftFacilityUseDate(new Date());
            expend.setPurificateFacilityUseDate(new Date());
            //添加到保存集合中
            fixedPointExpends.add(expend);
        }

        return fixedPointExpends;
    }


    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setFixedpointService(FixedpointService fixedpointService) {
        this.fixedpointService = fixedpointService;
    }

    @Autowired
    public void setStationRepository(StationRepository stationRepository) {
        this.stationRepository = stationRepository;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    public void setSampleTypeRepository(SampleTypeRepository sampleTypeRepository) {
        this.sampleTypeRepository = sampleTypeRepository;
    }

    @Autowired
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }
}
