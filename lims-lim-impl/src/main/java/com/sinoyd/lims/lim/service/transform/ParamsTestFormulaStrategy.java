package com.sinoyd.lims.lim.service.transform;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.ParamsTestFormulaMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.verify.TransformParamsTestFormulaVerifyHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 测试项目数据迁移测试项目公式参数导入策略类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
@Component
@Order(100)
public class ParamsTestFormulaStrategy implements TransformImportStrategy {
    /**
     * sheet页序号
     */
    private static final int sheetIndex = 6;

    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    private TransformParamsTestFormulaVerifyHandler transformParamsTestFormulaVerifyHandler;

    private ParamsTestFormulaMapper paramsTestFormulaMapper;

    @Override
    @Transactional
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception {
        //参数初始化
        handleInit();
        params.setStartSheetIndex(sheetIndex);
        params.setVerifyHandler(transformParamsTestFormulaVerifyHandler);
        //获取校验结果集
        ExcelImportResult<DtoExportParamsTestFormula> result = ExcelImportUtil.importExcelMore(inputStream, DtoExportParamsTestFormula.class, params);
        // 检验失败抛出异常并返回错误文档
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(sheetIndex, "导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        //构建可保存数据
        List<DtoParamsTestFormula> waitSaveList = new ArrayList<>();
        buildRightData(result, waitSaveList);
        //数据保存
        paramsTestFormulaRepository.save(waitSaveList);
    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {
        List<DtoExportParamsTestFormula> paramsTestFormulaList = testDependentData.getParamsTestFormulaList();
        if (StringUtil.isNotEmpty(paramsTestFormulaList)) {
            // 根据导出的测试项目公式，筛选待导出的测试项目参数公式。
            List<DtoExportParamsFormula> paramsFormulaList = exportData.getParamsFormulaList();
            List<String> formulaIds = paramsFormulaList.stream().map(DtoExportParamsFormula::getId).collect(Collectors.toList());

            // 导入数据
            List<DtoImportCheck> importChecks = dtoDataSyncParams.getImportChecks();
            List<DtoDataCheck> paramsChecks = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.参数表.getCheckItem()))
                    .flatMap(p -> p.getDataChecks().stream()).collect(Collectors.toList());
            Map<String, DtoDataCheck> paramsCheckMap = paramsChecks.stream().collect(Collectors.toMap(DtoDataCheck::getName, p -> p));
            List<DtoDataCheck> dimensionChecks = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.量纲表.getCheckItem()))
                    .flatMap(p -> p.getDataChecks().stream()).collect(Collectors.toList());
            Map<String, DtoDataCheck> dimensionCheckMap = dimensionChecks.stream().collect(Collectors.toMap(DtoDataCheck::getName, p -> p));
            List<DtoParamsTestFormula> testFormulas = paramsTestFormulaList.stream()
                    .filter(p -> !formulaIds.contains(p.getId()))
                    .map(p -> {
                        DtoParamsTestFormula dtoParamsTestFormula = paramsTestFormulaMapper.toDtoParamsTestFormula(p);
                        DtoDataCheck paramsCheck = paramsCheckMap.getOrDefault(p.getParamsName(), new DtoDataCheck());
                        dtoParamsTestFormula.setParamsId(paramsCheck.getId());
                        dtoParamsTestFormula.setParamsName(paramsCheck.getName());
                        DtoDataCheck dimensionCheck = dimensionCheckMap.getOrDefault(p.getDimension(), new DtoDataCheck());
                        dtoParamsTestFormula.setDimensionId(dimensionCheck.getId());
                        dtoParamsTestFormula.setDimension(dimensionCheck.getName());
                        return dtoParamsTestFormula;
                    }).collect(Collectors.toList());
            importTestTemp.setParamsTestFormulaTemps(testFormulas);


            if (StringUtil.isNotEmpty(formulaIds)) {
                List<DtoExportParamsTestFormula> exportParamsTestFormulas = paramsTestFormulaList.stream().filter(p -> formulaIds.contains(p.getObjId())).collect(Collectors.toList());
                exportData.setParamsTestFormulaList(exportParamsTestFormulas);

                // 筛选导出数据中的量纲和参数
                List<String> paramsIds = exportParamsTestFormulas.stream().map(DtoExportParamsTestFormula::getParamsId).distinct().collect(Collectors.toList());
                List<String> dimensionIds = exportParamsTestFormulas.stream().map(DtoExportParamsTestFormula::getDimensionId).distinct().collect(Collectors.toList());
                List<DtoExportParams> paramsList = exportData.getParamsList();
                List<DtoExportDimension> dimensionList = exportData.getDimensionList();
                // 去除待导入的；量纲和参数
                importTestTemp.getDimensionTemps().removeIf(p -> dimensionIds.contains(p.getId()));
                importTestTemp.getParamsTemps().removeIf(p -> paramsIds.contains(p.getId()));
                List<DtoExportParams> exportParams = testDependentData.getParamsList().stream().filter(p -> paramsIds.contains(p.getId())).collect(Collectors.toList());
                List<DtoExportDimension> exportDimensions = testDependentData.getDimensionList().stream().filter(p -> dimensionIds.contains(p.getId())).collect(Collectors.toList());
                paramsList.addAll(exportParams);
                dimensionList.addAll(exportDimensions);
                exportData.setParamsList(paramsList.stream().distinct().collect(Collectors.toList()));
                exportData.setDimensionList(dimensionList.stream().distinct().collect(Collectors.toList()));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoParamsTestFormula> paramsTestFormulaTemps = importTestTemp.getParamsTestFormulaTemps();
        if (StringUtil.isNotEmpty(paramsTestFormulaTemps)) {
            //已同步记录数
            int i = 0;
            for (DtoParamsTestFormula testFormula : paramsTestFormulaTemps) {
                paramsTestFormulaRepository.save(testFormula);
                webSocketServer.sendMessage(getMessage(paramsTestFormulaTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(paramsTestFormulaTemps.size(), 0));
        }
    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.公式参数表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.公式参数表.getSource();
    }

    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.公式参数表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        return null;
    }

    /**
     * 校验容器初始化
     */
    private void handleInit() {
        transformParamsTestFormulaVerifyHandler = new TransformParamsTestFormulaVerifyHandler();
        transformParamsTestFormulaVerifyHandler.setRepoDataList(paramsTestFormulaRepository.findAll());
        transformParamsTestFormulaVerifyHandler.setSheetExistDataList(new ArrayList<>());
    }

    /**
     * 构建可保存数据
     *
     * @param result       导入结果集
     * @param waitSaveList 待保存结果容器
     */
    private void buildRightData(ExcelImportResult<DtoExportParamsTestFormula> result, List<DtoParamsTestFormula> waitSaveList) {
        List<DtoExportParamsTestFormula> importList = result.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        for (DtoExportParamsTestFormula exportParamsTestFormula : importList) {
            DtoParamsTestFormula dtoParamsTestFormula = new DtoParamsTestFormula();
            BeanUtils.copyProperties(exportParamsTestFormula, dtoParamsTestFormula);
            waitSaveList.add(dtoParamsTestFormula);
        }
    }


    @Autowired
    @Lazy
    public void setParamsTestFormulaRepository(ParamsTestFormulaRepository paramsTestFormulaRepository) {
        this.paramsTestFormulaRepository = paramsTestFormulaRepository;
    }

    @Autowired
    @Lazy
    public void setParamsTestFormulaMapper(ParamsTestFormulaMapper paramsTestFormulaMapper) {
        this.paramsTestFormulaMapper = paramsTestFormulaMapper;
    }
}
