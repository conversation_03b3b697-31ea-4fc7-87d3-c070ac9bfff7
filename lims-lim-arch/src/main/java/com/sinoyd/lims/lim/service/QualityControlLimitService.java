package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Collection;
import java.util.List;

public interface QualityControlLimitService extends IBaseJpaService<DtoQualityControlLimit, String> {

    /**
     * 根据testId获取质控评价集合
     *
     * @param testId 测试项目id
     * @return 质控评价集合
     */
    List<DtoQualityControlLimit> findByTestId(String testId);

    /**
     * 根据testIds获取质控评价集合
     *
     * @param testIds 测试项目ids
     * @return 质控评价集合
     */
    List<DtoQualityControlLimit> findByTestIdIn(Collection<String> testIds);

    /**
     * 质控评价处理信息
     *
     * @param controlLimitList 质控评价集合
     * @param limitValue       检出限
     * @param lowerLimit       测定下限
     * @return 质控评价集合
     */
    List<DtoQualityControlLimit> fillControlMsg(List<DtoQualityControlLimit> controlLimitList, String limitValue, String lowerLimit);
}
