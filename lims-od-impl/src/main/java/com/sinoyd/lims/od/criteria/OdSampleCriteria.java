package com.sinoyd.lims.od.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * OdSample查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OdSampleCriteria extends BaseCriteria {

    /**
     * 嗅辨任务id
     */
    private String taskId;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 培训开始时间
     */
    private String startTime;

    /**
     * 培训结束时间
     */
    private String endTime;

    @Override
    public String getCondition() {

        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        Calendar calendar = new GregorianCalendar();
        //开始时间查询
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.odDate >= :startTime");
            values.put("startTime", date);
        }
        //结束时间查询
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.odDate < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotNull(taskId)) {
            condition.append(" and a.taskId = :taskId");
            values.put("taskId", taskId);
        }

        if (StringUtil.isNotEmpty(sampleCode)) {
            condition.append(" and a.sampleCode like :sampleCode");
            values.put("sampleCode", "%" + sampleCode + "%");
        }

        return condition.toString();

    }
}
