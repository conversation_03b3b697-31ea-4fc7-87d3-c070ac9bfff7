package com.sinoyd.lims.pro.strategy.strategy.submitRestrict;

import com.sinoyd.lims.pro.dto.customer.DtoSubmitRestrictVo;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 样品分配提交
 * <AUTHOR>
 */
@Component(IFileNameConstant.SubmitStrategyKey.SUBMIT_SAMPLE_DISTRIBUTION)
public class SampleDistributionStrategy extends AbsSubmitRestrictStrategy {
    @Override
    public List<DtoSubmitRestrictVo> generateSubmitRestrict(Object objMap, String status) {
        List<DtoSubmitRestrictVo> restrictVoList = new ArrayList<>();
        String subReceiveId = objMap.toString();
        Map<String, List<String>> msgMap = receiveSubSampleRecordService.canSubRecord(Collections.singletonList(subReceiveId));
        DtoSubmitRestrictVo restrictVo = new DtoSubmitRestrictVo();
        restrictVo.setCheckItem(EnumPRO.EnumRestrictItem.人员检测.getCheckItem());
        restrictVo.setModuleName(EnumPRO.EnumRestrictItem.人员检测.getModuleName());
        if (msgMap.size() > 0) {
            restrictVo.setExceptionOption(String.format("未配置检测人员：%s", String.join(",", msgMap.get(subReceiveId))));
            restrictVo.setIsPass(Boolean.FALSE);
            restrictVo.setIsUnusual(Boolean.FALSE);
        }
        restrictVoList.add(restrictVo);
        return restrictVoList;
    }
}
