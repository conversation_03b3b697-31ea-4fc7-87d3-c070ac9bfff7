package com.sinoyd.base.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仪器查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019/04/29
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Instrument2DetectorCriteria extends BaseCriteria {

    /**
     * 仪器Id
     */
    private String instrumentId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuffer condition = new StringBuffer();
        condition.append(" and isDeleted =0");
        if (StringUtil.isNotEmpty(instrumentId)) {
            condition.append(" and a.instrumentId = :instrumentId");
            values.put("instrumentId", instrumentId);
        }

        return condition.toString();
    }
}