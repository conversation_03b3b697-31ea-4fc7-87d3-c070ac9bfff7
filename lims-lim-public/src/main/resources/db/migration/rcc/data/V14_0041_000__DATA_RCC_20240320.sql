-- 地表水，地下水报告模板组件配置
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('aa7e18ab-0d52-45b5-a8a7-f12f27012928', 'd2e016dd-5676-46c5-b326-f867d9a2a573', 'ReportEditNew', '报告编制',
        'GroundWaterStd', '地下水报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-03-20 10:26:24', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-03-20 10:26:24', NULL);
INSERT INTO TB_LIM_ReportApply(id, reportConfigId, module, moduleName, code, name, type,
                               isRedact, isShow, remark, location, orgId, creator,
                               createDate, domainId, modifier, modifyDate, blankFill)
VALUES ('bfcabfde-6a0a-4358-bd4a-e60e316a6a92', '79914b30-6bdf-499e-b81f-1e85f6ce7195', 'ReportEditNew', '报告编制',
        'SurfaceWaterStd', '地表水报告', 1, 1, 1, '', '', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-03-20 10:25:45', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-03-20 10:25:45', NULL);

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest)
VALUES ('37b51443-895e-460e-96d6-692c3ec610b4', 'groundWaterStdDataSource', '标准版地表水报告检测数据主表', 'dtDataSource', '', 0, 0,
        '[\"dtWaterHeadStdTable\", \"dtGroundWaterStdTable\", \"dtCompoundStdNewTable\", \"outParallelStdDataSource\"]',
        b'1', '5f7bcf90feb545968424b0a872863876', '00000000-0000-0000-0000-000000000000', '2024-03-20 09:00:08',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2024-03-20 09:00:08', '1');

INSERT INTO TB_LIM_ReportModule(id, moduleCode, moduleName, tableName, sourceTableName,
                                sampleCount, testCount, sonTableJson, isCompound, orgId,
                                creator, createDate, domainId, modifier, modifyDate,
                                totalTest)
VALUES ('b01a7055-a235-4548-a9c1-18ab4ae8439c', 'dtGroundWaterStdTable', '标准版地表水检测结果表组件', 'dtGroundWaterStdTable',
        'dtGroundWaterSource', 5, 13, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '00000000-0000-0000-0000-000000000000', '2024-03-20 08:56:10', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '2024-03-20 08:56:10', '1');

INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId, remark,
                                isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('8128c4f2-0d2d-49e5-b7cd-5c770a49b399', '地下水报告', 3, 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-03-20 10:30:51', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-03-20 10:30:51');
INSERT INTO TB_LIM_RecordConfig(id, recordName, recordType, reportConfigId, sampleTypeId, remark,
                                isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('7e749103-a297-4c74-8150-bc75eeff5594', '地表水报告', 3, '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        '00000000-0000-0000-0000-000000000000', '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-03-20 10:30:31', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-03-20 10:30:31');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('0aeb4836-96fe-4e2d-8bef-e618b06a6094', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        '37b51443-895e-460e-96d6-692c3ec610b4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('2e549ea5-ce35-45eb-9bd5-f1db5b0716a1', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        '76d9545f-8eb1-4ab2-8aa4-f9decfe2a54e');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('372f44c5-5850-4e77-96b8-3307a840cec7', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('4a96d310-c0f8-489b-882f-60f440dfac0e', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        '7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('5de317dd-d69c-4ddb-ab6c-f356b3b221c5', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        'f33aa5b0-7ddd-4a62-8d7a-2b219f574bad');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('6b419b5a-ec28-49ee-b3f1-b50feb19f03a', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        '6130da13-013a-4a25-a856-bd1f16fc691b');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('759498c2-8da2-42f7-87a5-6e444a79d0ec', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        'fff21a0d-5e6f-4608-b966-d09bf86cc5f9');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('8cfbe1fd-76bd-4822-949b-9edabd647295', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        '9b557e2c-bd2c-4c73-8a6c-4a55e38f52ba');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b0f381d1-624e-46c6-bdcf-22770d997dfa', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        '3477d948-c1eb-418f-8fbd-81fc1295cee6');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b48aa3ac-e95d-46cf-bbda-3cfc6afc4963', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        '44c0ef90-1cee-4379-bdec-e502a875c0a7');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('b94b3705-e49e-4fc6-9251-c778bd887cc0', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        'fe05546e-47f5-436a-a99f-970a96bcd590');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('d2ad2683-3c0d-4076-9055-8ee8348e62ac', '79914b30-6bdf-499e-b81f-1e85f6ce7195',
        '55fe89e1-dc8c-4b86-b03b-e6dbf3143340');

INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('00d96eb3-232d-4a77-b86d-974fd8f607b0', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        'ba52a4c7-960f-4ede-ad34-6a4ea0335ba4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('02f049aa-8d8e-47b7-b9e4-3e9edb565dc1', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        '44c0ef90-1cee-4379-bdec-e502a875c0a7');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('18d5be38-ed07-43bf-982d-2954023d080c', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        '37b51443-895e-460e-96d6-692c3ec610b4');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('29159653-8d80-4733-bfc0-c97d0515c464', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        '7abbfd3f-7f71-4d2c-9bd3-9b4fc08b20cf');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('3600ebc1-8bce-44a2-abf8-23e034bcf9ba', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        '55fe89e1-dc8c-4b86-b03b-e6dbf3143340');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('423c2d62-5a96-4a7c-ae70-824088a43092', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        'fe05546e-47f5-436a-a99f-970a96bcd590');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('58969ee4-b87b-4586-9b1e-0c6681a239db', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        'fff21a0d-5e6f-4608-b966-d09bf86cc5f9');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('611e284e-04a7-4c40-8840-e6c7e283a9aa', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        '76d9545f-8eb1-4ab2-8aa4-f9decfe2a54e');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('748a55aa-b8b0-44cb-94c6-3a9fd846ec5e', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        '9b557e2c-bd2c-4c73-8a6c-4a55e38f52ba');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('755e573f-fda2-4519-9889-0c953d42d3c4', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        '3477d948-c1eb-418f-8fbd-81fc1295cee6');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('9464b610-79ed-4c84-a486-d79064124245', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        '6130da13-013a-4a25-a856-bd1f16fc691b');
INSERT INTO TB_LIM_ReportConfig2Module(id, reportConfigId, reportModuleId)
VALUES ('a0455d4d-0e1f-4f4b-9885-a07ed1ef104e', 'd2e016dd-5676-46c5-b326-f867d9a2a573',
        'f33aa5b0-7ddd-4a62-8d7a-2b219f574bad');

update TB_LIM_ReportModule
set sonTableJson = '["normalWaterStdDataSource", "groundWaterStdDataSource", "groundWaterStdDataSource", "orgGasStdDataSource", "unOrgGasStdDataSource", "solidStdDataSource", "noiseDayNightDataSource", "soilStdDataSource"]'
where moduleCode = 'comprehensiveStdDataSource';