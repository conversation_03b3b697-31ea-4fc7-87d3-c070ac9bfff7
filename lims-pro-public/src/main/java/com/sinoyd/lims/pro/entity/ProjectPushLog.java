package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;


/**
 * ProjectPushLog实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ProjectPushLog")
 @Data
 public  class ProjectPushLog implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  ProjectPushLog() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 标题
    */
    @ApiModelProperty("标题")
    @Length(message = "标题{validation.message.length}", max = 255)
	private String title;
    
    /**
    * 信息
    */
    @ApiModelProperty("信息")
	private String message;
    
    /**
    * 地址
    */
    @ApiModelProperty("地址")
    @Length(message = "地址{validation.message.length}", max = 255)
	private String url;
    
    /**
    * 人员id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("人员id")
	private String personId;
    
    /**
    * 推送时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("推送时间")
	private Date pushTime;
    
    /**
    * 推送状态
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("推送状态")
	private Boolean pushStatus;
    
    /**
    * 推送返回信息
    */
    @Column(length=1000)
    @ApiModelProperty("推送返回信息")
    @Length(message = "推送返回信息{validation.message.length}", max = 1000)
	private String pushResponse;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }