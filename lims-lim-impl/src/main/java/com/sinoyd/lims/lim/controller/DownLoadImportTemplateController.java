package com.sinoyd.lims.lim.controller;

import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据导入模板下载
 *
 * <AUTHOR>
 * @version V1.0.0 2022/3/24
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/downloadTemplate")
public class DownLoadImportTemplateController extends ExceptionHandlerController<DownLoadInstrumentTemplateService> {

    private DownLoadConsumableTemplateService downLoadConsumableTemplateService;

    private DownLoadPersonTemplateService downLoadPersonTemplateService;

    private DownLoadTestTemplateService downLoadTestTemplateService;

    private DownLoadInsCheckRecordTemplateService downLoadInsCheckRecordTemplateService;

    private DownLoadEnterpriseTemplateService downLoadEnterpriseTemplateService;

    private DownLoadEvaluationTemplateService downLoadEvaluationTemplateService;

    private DownLoadFormulaTemplateService downLoadFormulaTemplateService;

    private DownLoadRecoedConfig2TestTemplateService downLoadRecoedConfig2TestTemplateService;

    private DownLoadQcLimitTemplateService downLoadQcLimitTemplateService;

    private ImportPersonCertService importPersonCertService;

    private ImportStandardMethodService importStandardMethodService;

    /**
     * 仪器导入模板下载
     */
    @GetMapping("/downLoadInsTemplate")
    public void DownLoadInstrumentTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "仪器信息");
        sheetNames.put("secondName", "关联信息");
        service.downLoadExcel(response, sheetNames, "【导入模板】仪器信息");
    }

    /**
     * 消耗品模板下载
     */
    @GetMapping("/downLoadConsTemplate")
    public void DownLoadConsumableTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "消耗品清单");
        sheetNames.put("secondName", "消耗品类型");
        downLoadConsumableTemplateService.downLoadConsumable(response, sheetNames, "【导入模板】消耗品信息");
    }

    /**
     * 消耗品模板下载
     */
    @GetMapping("/downLoadStandardTemplate")
    public void DownLoadConsumableStandardTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "标样清单");
        sheetNames.put("secondName", "标样类型");
        downLoadConsumableTemplateService.downLoadStandard(response, sheetNames, "【导入模板】标样信息");
    }

    /**
     * 消耗品模板下载
     */
    @GetMapping("/downLoadOfMixed")
    public void DownLoadConsumableOfMixedTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "混标数据");
        downLoadConsumableTemplateService.downLoadConsumableOfMixed(response, sheetNames, "【导入模板】混标导入");
    }

    /**
     * 人员模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/downLoadPersonTemplate")
    public void DownLoadPersonTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "人员清单");
        sheetNames.put("secondName", "关联信息");
        downLoadPersonTemplateService.downLoadPersonTemplate(response, sheetNames, "【导入模板】人员信息");
    }

    /**
     * 人员模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/downLoadTestTemplate")
    public void DownLoadTestTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "测试项目数据");
        downLoadTestTemplateService.downLoadTestTemplate(response, sheetNames, "【导入模板】测试项目信息");
    }

    /**
     * 原始记录单与测试项目关系模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/downLoadRecoedConfig2TestTemplate")
    public void DownLoadRecoedConfig2TestTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "原始记录单与测试项目关系");
        downLoadRecoedConfig2TestTemplateService.downLoadTestTemplate(response, sheetNames, "【导入模板】原始记录单与测试项目关系");
    }

    /**
     * 仪器检定校准导入模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/checkRecord")
    public void DownLoadCheckRecordTemplate(HttpServletResponse response, String instrumentId) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "仪器检定校准信息");
        downLoadInsCheckRecordTemplateService.downLoadExcel(response, sheetNames, "【导入模板】仪器检定校准信息", instrumentId);
    }

    /**
     * 企业导入模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/enterprise")
    public void DownLoadEnterpriseTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "企业信息");
        downLoadEnterpriseTemplateService.downLoadTemplate(response, sheetNames, "【导入模板】企业信息");
    }

    /**
     * 评价标准导入模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/evaluationCriteria")
    public void DownLoadEvaluationCriteriaTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "评价标准信息");
        downLoadEvaluationTemplateService.downLoadTemplate(response, sheetNames, "【导入模板】评价标准");
    }

    /**
     * 公式导入模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/paramsFormula")
    public void DownLoadParamsFormulaTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "公式配置");
        downLoadFormulaTemplateService.downLoadTemplate(response, sheetNames, "【导入模板】公式模板");
    }

    /**
     * 公式导入模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/paramsFormulaUpdate")
    public void DownLoadParamsFormulaUpdateTemplate(HttpServletResponse response) {
        downLoadFormulaTemplateService.downLoadTemplateUpdate(response);
    }

    /**
     * 质控限值导入模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/qualityControlLimit")
    public void DownLoadQualityControlLimitTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "质控限值");
        downLoadQcLimitTemplateService.downLoadTemplate(response, sheetNames, "【导入模板】质控限值模板");
    }

    /**
     * 上岗证导入模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/personCert")
    public void DownLoadPersonCertTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "上岗证导入模版");
        importPersonCertService.downLoadExcel(response, sheetNames, "【导入模板】上岗证模板");
    }

    /**
     * 方法导入导入模板下载
     *
     * @param response 响应流
     */
    @GetMapping("/standardMethod")
    public void DownLoadStandardMethodTemplate(HttpServletResponse response) {
        Map<String, String> sheetNames = new HashMap<>();
        sheetNames.put("firstName", "方法导入模版");
        importStandardMethodService.downLoadExcel(response, sheetNames, "【导入模板】方法导入模板");
    }


    @Autowired
    public void setDownLoadConsumableTemplateService(DownLoadConsumableTemplateService downLoadConsumableTemplateService) {
        this.downLoadConsumableTemplateService = downLoadConsumableTemplateService;
    }

    @Autowired
    public void setDownLoadPersonTemplateService(DownLoadPersonTemplateService downLoadPersonTemplateService) {
        this.downLoadPersonTemplateService = downLoadPersonTemplateService;
    }

    @Autowired
    public void setDownLoadTestTemplateService(DownLoadTestTemplateService downLoadTestTemplateService) {
        this.downLoadTestTemplateService = downLoadTestTemplateService;
    }

    @Autowired
    public void setDownLoadInsCheckRecordTemplateService(DownLoadInsCheckRecordTemplateService downLoadInsCheckRecordTemplateService) {
        this.downLoadInsCheckRecordTemplateService = downLoadInsCheckRecordTemplateService;
    }

    @Autowired
    public void setDownLoadEnterpriseTemplateService(DownLoadEnterpriseTemplateService downLoadEnterpriseTemplateService) {
        this.downLoadEnterpriseTemplateService = downLoadEnterpriseTemplateService;
    }

    @Autowired
    public void setDownLoadEvaluationTemplateService(DownLoadEvaluationTemplateService downLoadEvaluationTemplateService) {
        this.downLoadEvaluationTemplateService = downLoadEvaluationTemplateService;
    }

    @Autowired
    public void setDownLoadFormulaTemplateService(DownLoadFormulaTemplateService downLoadFormulaTemplateService) {
        this.downLoadFormulaTemplateService = downLoadFormulaTemplateService;
    }

    @Autowired
    public void setDownLoadRecoedConfig2TestTemplateService(DownLoadRecoedConfig2TestTemplateService downLoadRecoedConfig2TestTemplateService) {
        this.downLoadRecoedConfig2TestTemplateService = downLoadRecoedConfig2TestTemplateService;
    }

    @Autowired
    public void setDownLoadQcLimitTemplateService(DownLoadQcLimitTemplateService downLoadQcLimitTemplateService) {
        this.downLoadQcLimitTemplateService = downLoadQcLimitTemplateService;
    }

    @Autowired
    public void setImportPersonCertService(ImportPersonCertService importPersonCertService) {
        this.importPersonCertService = importPersonCertService;
    }

    @Autowired
    public void setImportStandardMethodService(ImportStandardMethodService importStandardMethodService) {
        this.importStandardMethodService = importStandardMethodService;
    }
}
