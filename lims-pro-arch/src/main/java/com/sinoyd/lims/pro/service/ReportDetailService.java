package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoReportDetail;
import com.sinoyd.lims.pro.dto.customer.DtoReportDetailQuery;
import com.sinoyd.lims.pro.dto.DtoSample;

import java.util.List;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.customer.DtoSampleTemp;


/**
 * 报告明细操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/13
 * @since V100R001
 */
public interface ReportDetailService extends IBaseJpaService<DtoReportDetail, String> {
/**
     * 查询样品
     * @param queryDto 查询dto
     */
     List<DtoSampleTemp> query(DtoReportDetailQuery queryDto);

     /**
      * 查询样品--报告关联样品（样品分析项目合并总称）
      * @param queryDto 查询dto
      */
     List<DtoSampleTemp> queryMergeItemName(DtoReportDetailQuery queryDto);

     /**
      * 查询样品--报告关联样品
      * @param queryDto 查询dto
      */
     List<DtoSampleTemp> queryReport(DtoReportDetailQuery queryDto);

     /**
      * 批量修改报告关联样品
      * @param report 报告对象
      */
     List<DtoReportDetail> batchUpdate(DtoReport report);

     /**
      * 批量修改报告关联样品
      * @param report 报告对象
      * @return 删除的个数
      */
     int DeleteByRepIdTypeAndObjIds(DtoReport report);

     /**
      * 退回送样单判断样品
      * @param recIds 送样单ids
      * @param isPass 是否判断
      * @return 判断样品
      */
     Boolean isBackSampleByReceiveIds(List<String> recIds, Boolean isPass);

     /**
      * 退回工作单判断样品
      * @param workSheetFolderIds 工作单ids
      * @return 判断样品
      */
     Boolean isBackSampleByWorkSheetFolderIds(List<String> workSheetFolderIds);
}