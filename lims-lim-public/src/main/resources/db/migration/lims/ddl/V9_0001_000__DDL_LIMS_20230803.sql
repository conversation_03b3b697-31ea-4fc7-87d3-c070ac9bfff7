-- 新增电子报告基础信息表
CREATE TABLE TB_PRO_ReportBaseInfo
(
    id               varchar(50) NOT NULL COMMENT '主键id',
    reportId         varchar(50) NOT NULL COMMENT '报告id',
    projectName      varchar(200) NULL COMMENT '项目名称',
    systemCode       varchar(100) NULL COMMENT '系统编号',
    inspectedEnt     varchar(200) NULL COMMENT '受检单位',
    inspectedAddress varchar(300) NULL COMMENT '受检单位地址',
    customerName     varchar(200) NULL COMMENT '委托单位',
    customerAddress  varchar(300) NULL COMMENT '委托单位地址',
    orgId            varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator          varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate       datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier         varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate       datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT = '电子报告基础信息表';

-- 新增电子报告点位信息表
CREATE TABLE TB_PRO_ReportFolderInfo
(
    id           varchar(50) NOT NULL COMMENT '主键id',
    reportId     varchar(50) NOT NULL COMMENT '报告id',
    folderName   varchar(200) NULL COMMENT '点位名称',
    folderCode   varchar(100) NULL COMMENT '点位编码',
    folderRemark varchar(800) NULL COMMENT '点位备注，多个用;隔开',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT = '电子报告点位信息表';

-- 新增电子报告样品信息表
CREATE TABLE TB_PRO_ReportSampleInfo
(
    id                 varchar(50) NOT NULL COMMENT '主键id',
    reportId           varchar(50) NOT NULL COMMENT '报告id',
    sampleCode         varchar(100) NULL COMMENT '样品编号',
    sampleRemark       varchar(800) NULL COMMENT '样品备注，多个用;隔开',
    reportFolderInfoId varchar(50) NOT NULL COMMENT '报告点位信息id',
    orgId              varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator            varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate         datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId           varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier           varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate         datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT = '电子报告样品信息表';