package com.sinoyd.base.utils.reflection;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtilsBean;

import java.beans.PropertyDescriptor;
import java.util.HashMap;
import java.util.Map;

/**
 * 反射工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/24
 */
@Slf4j
public class ReflectUtil {

    /**
     * 获取扩展后的目标对象
     *
     * @param des           扩展前的目标对象
     * @param addProperties 扩展属性映射
     * @return 扩展后的目标对象
     */
    public static Object getTarget(Object des, Map<String, Object> addProperties) {
        return getDynamicBean(des, addProperties).getTarget();
    }

    /**
     * 获取扩展后的目标对象
     *
     * @return 扩展后的目标对象
     */
    public static Object getTarget(DynamicBean dynamicBean) {
        return dynamicBean.getTarget();
    }

    /**
     * 获取属性的值
     *
     * @param dynamicBean 实例
     * @param property    属性名
     * @return 属性值
     */
    public static Object getExtendPropertyValue(DynamicBean dynamicBean, String property) {
        return dynamicBean.getValue(property);
    }

    /**
     * 获取扩展后的动态bean
     *
     * @param des           扩展前的目标对象
     * @param addProperties 扩展属性映射
     * @return 扩展后的bean
     */
    public static DynamicBean getDynamicBean(Object des, Map<String, Object> addProperties) {
        PropertyUtilsBean propertyUtilsBean = new PropertyUtilsBean();
        PropertyDescriptor[] descriptors = propertyUtilsBean.getPropertyDescriptors(des);
        Map<String, Class> propertyMap = new HashMap<>();
        for (PropertyDescriptor d : descriptors) {
            if (!"class".equalsIgnoreCase(d.getName())) {
                propertyMap.put(d.getName(), d.getPropertyType());
            }
        }
        addProperties.forEach((k, v) -> propertyMap.put(k, v.getClass()));
        DynamicBean dynamicBean = new DynamicBean(des.getClass(), propertyMap);
        propertyMap.forEach((k, v) -> {
            try {
                if (!addProperties.containsKey(k)) {
                    dynamicBean.setValue(k, propertyUtilsBean.getNestedProperty(des, k));
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        addProperties.forEach((k, v) -> {
            try {
                dynamicBean.setValue(k, v);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        return dynamicBean;
    }

}