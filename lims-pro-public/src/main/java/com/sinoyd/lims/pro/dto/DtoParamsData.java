package com.sinoyd.lims.pro.dto;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.pro.entity.ParamsData;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoParamsData实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ParamsData")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoParamsData extends ParamsData {
    private static final long serialVersionUID = 1L;


    /**
     * 默认的控件类型
     */
    @Transient
    private Integer defaultControl;


    /**
     * 数据源
     */
    @Transient
    private String dataSource;


    /**
     * 是否必填
     */
    @Transient
    private Boolean isRequired;


    /**
     * 分析项目id
     */
    @Transient
    private String analyzeItemId;


    /**
     * 别名
     */
    @Transient
    private String alias;

    /**
     * 是否启用公式
     */
    @Transient
    private Boolean isEnabled = false;

    /**
     * 相关公式
     */
    @Transient
    private String formula;

    /**
     * 相关公式
     */
    @Transient
    private String formulaId;

    @Transient
    private Integer paramsType;

    /**
     * 默认的构造函数
     */
    public DtoParamsData() {

    }

    /**
     * ParamsDataServiceImpl 的findByPage 方法，如果构造函数变动，这边也要改变
     *
     * @param id             主键id
     * @param objectId       对象id
     * @param objectType     对象类型
     * @param paramsConfigId 配置id
     * @param paramsName     参数名称
     * @param paramsValue    参数值
     * @param dimension      量纲
     * @param dimensionId    量纲id
     * @param orderNum       排序号
     * @param analyzeItemId  分析项目id
     */
    public DtoParamsData(String id,
                         String objectId,
                         Integer objectType,
                         String paramsConfigId, String paramsName,
                         String paramsValue, String dimension,
                         String dimensionId, Integer orderNum,
                         String analyzeItemId,
                         String alias) {
        this.setId(id);
        this.setObjectId(objectId);
        this.setObjectType(objectType);
        this.setParamsConfigId(paramsConfigId);
        this.setParamsName(paramsName);
        this.setParamsValue(paramsValue);
        this.setDimension(dimension);
        this.setDimensionId(dimensionId);
        this.setOrderNum(orderNum);
        this.setAnalyzeItemId(analyzeItemId);
        this.setAlias(alias);
    }

    public DtoParamsData(String id,
                         String paramsName,
                         String paramsValue,
                         String paramsConfigId, String dimension,
                         String dimensionId, String objectId,
                         String analyzeItemId) {
        this.setId(id);
        this.setObjectId(objectId);
        this.setParamsConfigId(paramsConfigId);
        this.setParamsName(paramsName);
        this.setParamsValue(paramsValue);
        this.setDimension(dimension);
        this.setDimensionId(dimensionId);
        this.setAnalyzeItemId(analyzeItemId);
    }

    /**
     * ParamsDataServiceImpl 的findByPage 方法，如果构造函数变动，这边也要改变
     *
     * @param id             主键id
     * @param objectId       对象id
     * @param objectType     对象类型
     * @param paramsConfigId 配置id
     * @param paramsName     参数名称
     * @param paramsValue    参数值
     * @param dimension      量纲
     * @param dimensionId    量纲id
     * @param orderNum       排序号
     * @param analyzeItemId  分析项目id
     */
    public DtoParamsData(String id, String objectId, Integer objectType,
                         String paramsConfigId, String paramsName,
                         String paramsValue, String dimension,
                         String dimensionId, Integer orderNum,
                         String analyzeItemId, String alias,String groupId) {
        this.setId(id);
        this.setObjectId(objectId);
        this.setObjectType(objectType);
        this.setParamsConfigId(paramsConfigId);
        this.setParamsName(paramsName);
        this.setParamsValue(paramsValue);
        this.setDimension(dimension);
        this.setDimensionId(dimensionId);
        this.setOrderNum(orderNum);
        this.setAnalyzeItemId(analyzeItemId);
        this.setAlias(alias);
        this.setGroupId(groupId);
    }

    /**
     * 使用参数配置填充参数数据
     *
     * @param paramsConfig 参数配置
     * @return 完成初始化的参数数据实体
     */
    public void initParamDataByParamConfig(DtoParamsConfig paramsConfig) {
        this.setParamsConfigId(paramsConfig.getId());
        this.setParamsName(paramsConfig.getAlias());
        if (StringUtil.isEmpty(this.getParamsValue())) {
            this.setParamsValue(paramsConfig.getDefaultValue());
        }
        this.setDimension(paramsConfig.getDimension());
        this.setDimensionId(paramsConfig.getDimensionId());
        this.setOrderNum(paramsConfig.getOrderNum());
        this.setAlias(paramsConfig.getAlias());
        this.setDefaultControl(paramsConfig.getDefaultControl());
        this.setDataSource(paramsConfig.getDataSource());
        this.setFormulaId(paramsConfig.getFormulaId());
        this.setIsRequired(paramsConfig.getIsRequired());
    }

    public DtoParamsData(String id, String paramsName, String paramsValue,
                         String paramsConfigId, String dimension, String dimensionId,
                         String objectId, String analyzeItemId, String dataSource,
                         int paramsType) {
        this.setId(id);
        this.setObjectId(objectId);
        this.setParamsConfigId(paramsConfigId);
        this.setParamsName(paramsName);
        this.setParamsValue(paramsValue);
        this.setDimension(dimension);
        this.setDimensionId(dimensionId);
        this.setAnalyzeItemId(analyzeItemId);
        this.setDataSource(dataSource);
        this.setParamsType(paramsType);
    }
}