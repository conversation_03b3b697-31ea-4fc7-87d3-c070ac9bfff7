package com.sinoyd.lims.lim.service;

import com.sinoyd.lims.lim.dto.lims.DtoOAConsumablePickListsDetail;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Collection;
import java.util.List;


/**
 * OAConsumablePickListsDetail操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/4/2
 * @since V100R001
 */
public interface OAConsumablePickListsDetailService extends IBaseJpaService<DtoOAConsumablePickListsDetail, String> {

    /**
     * 根据消耗品ID查询消耗品领料明细
     *
     * @param consumableIds 消耗品ID集合
     * @return 消耗品领料明细集合
     */
    List<DtoOAConsumablePickListsDetail> findByConsumableIds(Collection<String> consumableIds);

}