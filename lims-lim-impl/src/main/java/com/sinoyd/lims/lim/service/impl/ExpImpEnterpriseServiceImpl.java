package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.sinoyd.base.criteria.EnterpriseCriteria;
import com.sinoyd.base.dto.customer.DtoImportEnterprise;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoEnterpriseExtend;
import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.EnterpriseExtendRepository;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.IndustryTypeRepository;
import com.sinoyd.base.service.EnterpriseService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.constants.LimConstants;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpEnterprise;
import com.sinoyd.lims.lim.service.ExpImpEnterpriseService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyEnterpriseVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 企业导入导出接口实现
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
@Service
public class ExpImpEnterpriseServiceImpl extends BaseJpaServiceImpl<DtoEnterprise, String, EnterpriseRepository> implements ExpImpEnterpriseService {

    private EnterpriseService enterpriseService;
    private ImportUtils importUtils;
    private IndustryTypeRepository industryTypeRepository;
    private AreaService areaService;
    private CodeService codeService;
    private ImpModifyEnterpriseVerify impModifyEnterpriseVerify;
    private EnterpriseExtendRepository enterpriseExtendRepository;
    private EnterpriseRepository enterpriseRepository;


    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoEnterprise> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        EnterpriseCriteria criteria = (EnterpriseCriteria) baseCriteria;
        enterpriseService.findByPage(page, criteria);
        List<DtoEnterprise> dtoEnterprises = page.getData();
        // 行业类型
        List<DtoIndustryType> industryTypes = industryTypeRepository.findAll();

        List<String> entIds = dtoEnterprises.stream().map(DtoEnterprise::getId).collect(Collectors.toList());
        List<DtoEnterpriseExtend> extendList = enterpriseExtendRepository.findByEntIds(entIds);
        Map<String, DtoEnterpriseExtend> extendMap = extendList.stream().collect(Collectors.toMap(DtoEnterpriseExtend::getEntId, dto -> dto));
        // 地区数据
        List<DtoArea> areaAll = areaService.findAll();
        // 污染源类型数据
        List<DtoCode> pollutionSourceTypeList = codeService.findCodes("LIM_PollutionSourceType");
        Map<String, String> pollTypeMap = pollutionSourceTypeList.stream().collect(Collectors.toMap(DtoCode::getDictValue, DtoCode::getDictName));

        Map<String, String> industryTypeMap = industryTypes.stream().collect(Collectors.toMap(DtoIndustryType::getId, DtoIndustryType::getIndustryName));
        List<DtoExpImpEnterprise> expImpEnterpriseList = new ArrayList<>();
        for (DtoEnterprise enterprise : dtoEnterprises) {
            DtoExpImpEnterprise expImpEnterprise = new DtoExpImpEnterprise();
            BeanUtils.copyProperties(enterprise, expImpEnterprise);
            expImpEnterprise.setIsPollutionStr(enterprise.getIsPollution() ? "是" : "否");
            if (enterprise.getIsPollution()) {
                DtoEnterpriseExtend extend = extendMap.get(enterprise.getId());
                String sourceType = extend.getPollutionSourceType();
                expImpEnterprise.setPollutionType(getPollutionTypeName(sourceType, pollTypeMap));
            }
            if (StringUtil.isNotEmpty(enterprise.getAreaId())) {
                setAreaData(areaAll, enterprise.getAreaId(), expImpEnterprise);
            }
            expImpEnterprise.setBusinessTypeId(industryTypeMap.getOrDefault(enterprise.getBusinessTypeId(), ""));
            expImpEnterpriseList.add(expImpEnterprise);
        }
        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpEnterprise.class, expImpEnterpriseList);
        // 设置下拉框
        String[] industryTypeNames = new String[industryTypes.size()];
        for (int i = 0; i < industryTypes.size(); i++) {
            industryTypeNames[i] = industryTypes.get(i).getIndustryName();
        }
        importUtils.selectList(workBook, 9, 9, industryTypeNames);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Override
    @Transactional
    public List<DtoEnterprise> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {

        PoiExcelUtils.verifyFileType(file);
        //region 业务参数
        //获取所有区域信息
        List<DtoArea> dbAreaList = areaService.findAll();
        //获取所有的企业信息
        List<DtoEnterprise> dbEnterpriseList = repository.findAll();
        //endregion

        //region 线程变量赋值
        impModifyEnterpriseVerify.getDbEnterpriseTl().set(dbEnterpriseList);
        impModifyEnterpriseVerify.getDbAreaTl().set(dbAreaList);
        List<DtoExpImpEnterprise> enterpriseTemp = new ArrayList<>();
        impModifyEnterpriseVerify.getEnterpriseTl().set(enterpriseTemp);
        List<DtoCode> pollutionTypeCodes = codeService.findCodes(LimConstants.ImportConstants.LIM_POLLUTION_SOURCE_TYPE);
        Map<String, List<DtoCode>> codeMap = new HashMap<>();
        codeMap.put(LimConstants.ImportConstants.LIM_POLLUTION_SOURCE_TYPE, pollutionTypeCodes);
        impModifyEnterpriseVerify.getCodeTl().set(codeMap);
        //endregion

        //region 数据参数
        ExcelImportResult<DtoExpImpEnterprise> importResult = getExcelData(file, response);

        //region 线程变量清理
        impModifyEnterpriseVerify.getDbEnterpriseTl().remove();
        impModifyEnterpriseVerify.getDbAreaTl().remove();
        impModifyEnterpriseVerify.getEnterpriseTl().remove();
        impModifyEnterpriseVerify.getCodeTl().remove();
        //endregion


        //获取校验成功的数据
        List<DtoExpImpEnterprise> importList = importResult.getList();

        //移除空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getName()));

        if (StringUtil.isEmpty(importList)) {
            throw new BaseException("文件中无数据或模板不正确，请检查后导入");
        }
        //region 添加数据
        List<DtoEnterpriseExtend> extendList = new ArrayList<>();

        List<DtoEnterprise> enterpriseList = importToEntity(dbEnterpriseList, importList, extendList);

        this.addData(enterpriseList);

        this.addExtendList(extendList);

        //endregion

        return enterpriseService.findAll();
    }


    @Override
    @Transactional
    public void addData(List<DtoEnterprise> data) {
        if (StringUtil.isNotEmpty(data)) {
            repository.save(data);
        }
    }

    @Override
    public ExcelImportResult<DtoExpImpEnterprise> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);

        params.setVerifyHandler(impModifyEnterpriseVerify);
        ExcelImportResult<DtoExpImpEnterprise> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoExpImpEnterprise.class, params);
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "客户信息导入错误信息");
            PoiExcelUtils.downLoadExcel("客户信息导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }


    /**
     * 区域赋值
     *
     * @param areaList         地区集合
     * @param id               地区id
     * @param expImpEnterprise 赋值实体
     */
    private void setAreaData(List<DtoArea> areaList, String id, DtoExpImpEnterprise expImpEnterprise) {

        DtoArea area = areaList.stream().filter(p -> p.getId().equals(id)).findFirst().orElse(null);
        if (StringUtil.isNotNull(area)) {
            expImpEnterprise.setAreaName("");
            if ("0".equals(area.getParentId())) {
                expImpEnterprise.setProvinceId(area.getId());
                expImpEnterprise.setProvinceAreaName(area.getAreaName());
            } else {
                DtoArea parentArea = areaList.stream().filter(p -> p.getId().equals(area.getParentId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(parentArea)) {
                    if ("0".equals(parentArea.getParentId())) {
                        expImpEnterprise.setProvinceId(parentArea.getId());
                        expImpEnterprise.setProvinceAreaName(parentArea.getAreaName());
                        expImpEnterprise.setCityId(area.getId());
                        expImpEnterprise.setCityAreaName(area.getAreaName());
                    } else {
                        DtoArea dtoArea = areaList.stream().filter(p -> p.getId().equals(parentArea.getParentId())).findFirst().orElse(null);
                        if (StringUtil.isNotNull(dtoArea)) {
                            if ("0".equals(dtoArea.getParentId())) {
                                expImpEnterprise.setProvinceId(dtoArea.getId());
                                expImpEnterprise.setProvinceAreaName(dtoArea.getAreaName());
                                expImpEnterprise.setCityId(parentArea.getId());
                                expImpEnterprise.setCityAreaName(parentArea.getAreaName());
                                expImpEnterprise.setAreaId(area.getId());
                                expImpEnterprise.setAreaName(area.getAreaName());
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 导入实体转换为企业实体
     *
     * @param importEnterprises 导入数据
     * @return 人员实体
     */
    private List<DtoEnterprise> importToEntity(List<DtoEnterprise> dbEnterpriseList, List<DtoExpImpEnterprise> importEnterprises, List<DtoEnterpriseExtend> extendList) {
        List<DtoEnterprise> enterpriseList = new ArrayList<>();
        Map<String, DtoEnterprise> dtoEnterpriseMap = dbEnterpriseList.stream().collect(Collectors.toMap(DtoEnterprise::getId, p -> p));

        List<String> enterprisesIds = importEnterprises.stream().map(DtoExpImpEnterprise::getId).collect(Collectors.toList());
        enterprisesIds.removeIf(StringUtil::isEmpty);

        List<DtoEnterpriseExtend> enterpriseExtends = enterpriseExtendRepository.findByEntIds(enterprisesIds);
        Map<String, DtoEnterpriseExtend> extendMap = enterpriseExtends.stream().collect(Collectors.toMap(DtoEnterpriseExtend::getEntId, p -> p));
        BiMap<String, String> industryNameMap = HashBiMap.create();
        List<DtoIndustryType> industryTypes = industryTypeRepository.findAll();
        for (DtoIndustryType industryType : industryTypes) {
            industryNameMap.put(industryType.getId(), industryType.getIndustryName());
        }
        for (DtoExpImpEnterprise importEnterprise : importEnterprises) {
            DtoEnterprise enterprise = new DtoEnterprise();
            DtoEnterpriseExtend enterpriseExtend = new DtoEnterpriseExtend();
            if (StringUtil.isNotEmpty(importEnterprise.getId())) {
                DtoEnterprise sourceEnterprise = dtoEnterpriseMap.getOrDefault(importEnterprise.getId(), new DtoEnterprise());
                BeanUtils.copyProperties(sourceEnterprise, enterprise);
                enterpriseExtend = extendMap.getOrDefault(importEnterprise.getId(), new DtoEnterpriseExtend());
            } else {
                importEnterprise.setId(UUIDHelper.NewID());
            }
            BeanUtils.copyProperties(importEnterprise, enterprise, "businessTypeId");
            // 扩展信息赋值
            enterpriseExtend.setEntId(enterprise.getId());
            enterpriseExtend.setIsUsed(enterprise.getIsUsed());
            enterpriseExtend.setIsBreak(enterprise.getIsBreak());
            enterpriseExtend.setBreakInfo(enterprise.getBreakInfo());
            enterpriseExtend.setAttentionDegree(enterprise.getAttentionDegree());
            enterpriseExtend.setSubRate(enterprise.getSubRate());

            enterprise.setBusinessTypeId(industryNameMap.inverse().getOrDefault(importEnterprise.getBusinessTypeId(), UUIDHelper.GUID_EMPTY));
            //根据所属区域名称获取区域数据
            String areaName = "";
            String areaId = "";
            if (StringUtil.isNotEmpty(importEnterprise.getProvinceAreaName()) && StringUtil.isNotEmpty(importEnterprise.getProvinceId())) {
                areaName = importEnterprise.getProvinceAreaName();
                areaId = importEnterprise.getProvinceId();
            }
            if (StringUtil.isNotEmpty(importEnterprise.getCityAreaName()) && StringUtil.isNotEmpty(importEnterprise.getCityId())) {
                areaName = importEnterprise.getCityAreaName();
                areaId = importEnterprise.getCityId();
            }
            if (StringUtil.isNotEmpty(importEnterprise.getAreaName()) && StringUtil.isNotEmpty(importEnterprise.getAreaId())) {
                areaName = importEnterprise.getAreaName();
                areaId = importEnterprise.getAreaId();
            }
            if (StringUtil.isNotEmpty(importEnterprise.getIsPollutionStr()) && "是".equals(importEnterprise.getIsPollutionStr())) {
                enterprise.setType(EnumBase.EnumEnterpriseType.污染源.getValue());
                enterpriseExtend.setPollutionCode(importEnterprise.getPollutionCode());
                enterpriseExtend.setPollutionSourceType(getPollutionTypeValue(importEnterprise.getPollutionType()));
            } else {
                enterprise.setType(EnumBase.EnumEnterpriseType.客户.getValue());
                enterpriseExtend.setPollutionSourceType("");
            }
            enterprise.setAreaName(areaName);
            enterprise.setIndustryKind(importEnterprise.getIndustryKind());
            enterprise.setAreaId(areaId);
            enterprise.setRegTypeId("");
            enterpriseList.add(enterprise);
            extendList.add(enterpriseExtend);
        }
        return enterpriseList;
    }

    /**
     * 保存企业拓展数据
     *
     * @param extendList 企业拓展数据
     */
    private void addExtendList(List<DtoEnterpriseExtend> extendList) {
        if (StringUtil.isNotEmpty(extendList)) {
            enterpriseExtendRepository.save(extendList);
        }
    }

    /**
     * 获取污染源类型的value值
     *
     * @param typeName 污染源类型名称
     * @return 污染源类型值
     */
    private String getPollutionTypeValue(String typeName) {
        List<String> resList = new ArrayList<>();
        Map<String, String> pollutionTypeMap = new HashMap<>();
        List<DtoCode> pollutionSourceTypeList = codeService.findCodes("LIM_PollutionSourceType");
        for (DtoCode code : pollutionSourceTypeList) {
            pollutionTypeMap.put(code.getDictName(), code.getDictValue());
        }
        if (StringUtil.isNotEmpty(typeName)) {
            String replace = typeName.replace("，", ",");
            String[] typeNameList = replace.split(",");
            for (String name : typeNameList) {
                if (pollutionTypeMap.containsKey(name)) {
                    resList.add(pollutionTypeMap.get(name));
                }
            }
        }
        return StringUtil.isNotEmpty(resList) ? String.join(";", resList) : "";
    }

    /**
     * 获取污染源类型的名称值
     *
     * @param typeValue 污染源类型名称
     * @return 污染源类型值
     */
    private String getPollutionTypeName(String typeValue, Map<String, String> pollTypeMap) {
        List<String> resList = new ArrayList<>();
        if (StringUtil.isNotEmpty(typeValue)) {
            String[] typeValueList = typeValue.split(";");
            for (String value : typeValueList) {
                if (pollTypeMap.containsKey(value)) {
                    resList.add(pollTypeMap.get(value));
                }
            }
        }
        return StringUtil.isNotEmpty(resList) ? String.join(",", resList) : "";
    }


    @Autowired
    public void setEnterpriseService(EnterpriseService enterpriseService) {
        this.enterpriseService = enterpriseService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setIndustryTypeRepository(IndustryTypeRepository industryTypeRepository) {
        this.industryTypeRepository = industryTypeRepository;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setImpModifyEnterpriseVerify(ImpModifyEnterpriseVerify impModifyEnterpriseVerify) {
        this.impModifyEnterpriseVerify = impModifyEnterpriseVerify;
    }

    @Autowired
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }

    @Autowired
    public void setEnterpriseExtendRepository(EnterpriseExtendRepository enterpriseExtendRepository) {
        this.enterpriseExtendRepository = enterpriseExtendRepository;
    }
    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }
}
