package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.enums.EnumLIM.EnumFileControlStatus;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 文件管理-文件信息（右侧Grid）
 * <AUTHOR>
 * @version V1.0.0 2019/2/25
 * @since V100R001
 */
public interface FileControlApplyDetailService extends IBaseJpaService<DtoFileControlApplyDetail, String> {

    /**
     * 更新受控文件原始文件状态
     *
     * @param fileId 文件标识
     * @param status 状态
     */
    void updateFileStatus(String fileId, EnumFileControlStatus status);


    /**
     * 更新受控文件原始文件状态
     *
     * @param fileId 文件标识
     * @param status 状态
     * @param  controlDate 受控日期
     */
    void updateControlFileStatus(String fileId, EnumFileControlStatus status,Date controlDate);



    /**
     * 更新废止原始文件状态
     *
     * @param fileId 文件标识
     * @param status 状态
     * @param  abolishDate 废止日期
     */
    void updateAbolishFileStatus(String fileId, EnumFileControlStatus status,Date abolishDate);

    /**
     * 更新原始文件状态
     *
     * @param fileId      文件标识
     * @param status      状态
     * @param controlCode 受控编号
     * @param versionCode 版本号
     */
    void updateFileStatus(String fileId, String controlCode, String versionCode, EnumFileControlStatus status);

    /**
     * @param id 主键id
     * @return 返回文件对象
     */
    DtoFileControlApplyDetail getFileControlPath(String id);

    /**
     * 保存文件受控信息
     *
     * @param dtoFileControlApplyDetail 文件受控
     * @return 返回受控信息
     */
    DtoFileControlApplyDetail saveFileControlApplyDetail(DtoFileControlApplyDetail dtoFileControlApplyDetail);

    /**
     * 是否存在受控编号
     *
     * @param controlCode 受控编号
     * @return 是否存在
     */
    Boolean isExistControlCode(String controlCode);


    /**
     * 是否存在受控编号
     *
     * @param controlCode 受控编号
     * @param id          主键id
     * @return 是否存在
     */
    Boolean isExistControlCode(String controlCode, String id);

    /**
     *  根据文件类型常量id进行相关受控文件明细删除
     * @param ids 常量编码id集合
     * @return 删除的记录条数
     */
    Integer deleteByDictCode(List<String> ids);

    /**
     * 获取所有文件类型，树结构
     *
     * @return List<TreeNode>
     */
    List<TreeNode> getAllFileTypes();

    /**
     * 更新文件状态
     *
     * @param detail 数据载体
     */
    void updateFileStatus(DtoFileControlApplyDetail detail);

    /**
     * 下载文件
     *
     * @param fileId 文件id
     * @param documentId 上传文件id
     * @param response 响应
     */
    void downLoadFile(String fileId, String documentId, HttpServletResponse response) throws Exception;

    /**
     * 批量下载文件
     *
     * @param fileIds   文件ids
     * @param response  响应
     */
    void batchDownLoad(List<String> fileIds, HttpServletResponse response) throws Exception;
}