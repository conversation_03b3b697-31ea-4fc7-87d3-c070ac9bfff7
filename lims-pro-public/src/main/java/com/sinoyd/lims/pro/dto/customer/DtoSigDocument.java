package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

/**
 * 签名位置及人员实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/02
 * @since V100R001
 */
@Data
public class DtoSigDocument {

    /**
     * 签名类型
     */
    private Integer sigType;

    /**
     * 签名人员id
     */
    private String personId;

    /**
     * 签名时间
     */
    private String timeStr;

    /**
     * 签名间距
     */
    private Integer sigIndex;

    /**
     * 是否拆分时间
     */
    private Boolean isSplitDate;

    /**
     * 拆分时间间距
     */
    private Integer splitIndex;

    /**
     * 签时间间隔
     */
    private Integer timeIndex;
}
