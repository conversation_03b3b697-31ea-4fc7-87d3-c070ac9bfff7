package com.sinoyd.lims.api.service;

import com.sinoyd.base.dto.customer.DtoKeyValue;
import com.sinoyd.boot.frame.sys.model.OrgModel;
import com.sinoyd.lims.api.dto.customer.DtoSampleSchedule;
import com.sinoyd.lims.api.dto.customer.DtoWorkSheetTask;
import com.sinoyd.lims.lim.dto.customer.DtoPersonQuery;
import com.sinoyd.lims.lim.dto.lims.DtoTestPost;

import java.util.List;
import java.util.Map;

/**
 * 大屏（实验室专题）管理接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
public interface LargeScreenService {

    /**
     * 采样概况
     *
     * @param date 日期
     * @return 采样概况
     */
    List<Map<String, String>> sampleOverview(String date);

    /**
     * 分析概况
     *
     * @return 分析概况
     */
    List<Map<String, Object>> analyseOverview();

    /**
     * 采样一览
     *
     * @param date 日期
     * @return 采样一览
     */
    List<DtoSampleSchedule> sampleList(String date);

    /**
     * 检测任务
     *
     * @return
     */
    List<DtoWorkSheetTask> workSheetTask(DtoWorkSheetTask dtoWorkSheetTask);

    /**
     * 数据统计
     *
     * @param year 年份
     * @return 数据统计
     */
    Map<String, Object> statisticsData(Integer year);

    /**
     * 超期任务
     *
     * @return 超期任务
     */
    List<Map<String, Object>> overdueTask();

    /**
     * 返回人员列表
     *
     * @param queryDto 查询dto
     * @return 返回人员对象
     */
    List<DtoKeyValue> person(DtoPersonQuery queryDto);

    /**
     * 返回岗位列表
     *
     * @return 返回岗位列表
     */
    List<DtoTestPost> testPost();

    /**
     * 是否岗位分配
     *
     * @return 是否
     */
    Boolean isTestPost();

    /**
     * 获取所有机构
     *
     * @return 机构集合
     */
    List<OrgModel> getOrgList();


    /**
     * 年度机构信息统计(安徽调用)
     *
     * @return 统计信息
     */
    Map<String, Object> yearPreview(Integer year);

    /**
     * 实验室简介(安徽调用)
     *
     * @return 实验室简介
     */
    Map<String, Object> labOverview();
}
