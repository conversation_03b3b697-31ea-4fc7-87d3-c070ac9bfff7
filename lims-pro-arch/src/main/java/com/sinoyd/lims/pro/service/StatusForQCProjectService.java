package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.probase.service.StatusForProjectBaseService;

/**
 * StatusForRecord操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/2/11
 * @since V100R001
 */
public interface StatusForQCProjectService extends StatusForProjectBaseService {
    /**
     * 修改状态数据
     *
     * @param from    状态起
     * @param to      状态止
     * @param sign    工作流信号对象
     * @param project 项目实体
     */
    void modifyStatus(String from, String to, DtoWorkflowSign sign, DtoProject project);
}
