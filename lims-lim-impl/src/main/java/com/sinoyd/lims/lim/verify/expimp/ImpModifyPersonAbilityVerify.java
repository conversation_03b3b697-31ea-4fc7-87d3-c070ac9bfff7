package com.sinoyd.lims.lim.verify.expimp;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoPersonAbility;
import com.sinoyd.lims.lim.dto.lims.DtoPersonCert;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 人员检测能力导入更新数据校验器
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/12
 * @since V100R001
 */
@Data
public class ImpModifyPersonAbilityVerify implements IExcelVerifyHandler<DtoExpImpPersonAbility> {


    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    /**
     * 测试项目
     */
    private final List<DtoTest> testList;

    /**
     * 证书
     */
    private final List<DtoPersonCert> personCerts;

    /**
     * 检测类型
     */
    private final List<DtoSampleType> sampleTypes;

    /**
     * 系统中的检测能力
     */
    private final List<DtoPersonAbility> personAbilityList;

    private final List<DtoPerson> personList;

    /**
     * 临时数据 每校验一行存入当前校验的数据，用于判断导入数据重复
     */
    private List<DtoExpImpPersonAbility> expImpPersonAbilities;

    /**
     * 分析方法
     */
    private List<DtoAnalyzeMethod> analyzeMethodList;

    /**
     * 证书类别
     */
    private List<DtoCode> personCertCode;


    public ImpModifyPersonAbilityVerify(List<DtoTest> testList,
                                        List<DtoPersonCert> personCerts,
                                        List<DtoPersonAbility> personAbilityList,
                                        List<DtoSampleType> sampleTypes,
                                        List<DtoPerson> personList,
                                        List<DtoAnalyzeMethod> analyzeMethodList,
                                        List<DtoCode> personCertCode) {
        this.testList = testList;
        this.personCerts = personCerts;
        this.personAbilityList = personAbilityList;
        this.sampleTypes = sampleTypes;
        this.personList = personList;
        this.analyzeMethodList = analyzeMethodList;
        this.personCertCode = personCertCode;
    }

    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExpImpPersonAbility dto) {
        //region 导入数据处理
        try {
            //跳过空行
            if (importUtils.checkObjectIsNull(dto)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //前后去空格
            importUtils.strToTrim(dto);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        //endregion

        // 默认值
        //region 参数
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //校验错误总数据
        StringBuilder failStr = new StringBuilder("第" + dto.getRowNum() + "行数据校验有误");
        // 校验数据
        importUtils.checkIsNull(result, dto.getName(), "姓名", failStr);
        importUtils.checkIsNull(result, dto.getRedAnalyzeItemName(), "分析项目", failStr);
        importUtils.checkIsNull(result, dto.getRedAnalyzeMethodName(), "分析方法", failStr);
        importUtils.checkIsNull(result, dto.getSampleTypeName(), "检测类型", failStr);
        importUtils.checkIsNull(result, dto.getPersonCertCode(), "证书编号", failStr);
        importUtils.checkIsNull(result, dto.getAchieveDate(), "发证日期", failStr);
        importUtils.checkIsNull(result, dto.getAbilityTypeName(), "类别", failStr);
        importUtils.checkDateTwo(result, dto.getAchieveDate(), "发证日期", failStr);
        importUtils.checkDateTwo(result, dto.getCertEffectiveTime(), "有效期至", failStr);

        if (StringUtil.isEmpty(expImpPersonAbilities)) {
            expImpPersonAbilities = new ArrayList<>();
        }
        // 校验测试项目是否存在
        isTestData(result, testList, dto, sampleTypes, failStr);

        isExistCert(result, dto, personCerts, failStr);

        isExistCertType(result, dto, personCertCode, failStr);

        isExistPerson(result, dto, personList, failStr);

        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        expImpPersonAbilities.add(dto);

        return result;
    }

    private void isExistCertType(ExcelVerifyHandlerResult result, DtoExpImpPersonAbility dto, List<DtoCode> personCertCode, StringBuilder failStr) {
        List<DtoCode> abilityTypes = personCertCode.stream().filter(p -> dto.getAbilityTypeName().equals(p.getDictName())).collect(Collectors.toList());
        if (StringUtil.isEmpty(abilityTypes)) {
            result.setSuccess(false);
            failStr.append("；证书类别不存在");
        } else {
            dto.setAbilityType(abilityTypes.get(0).getDictValue());
        }
    }

    private void isExistPerson(ExcelVerifyHandlerResult result, DtoExpImpPersonAbility dto, List<DtoPerson> personList, StringBuilder failStr) {
        List<DtoPerson> dtoPeople = personList.stream().filter(p -> p.getCName().equals(dto.getName())).collect(Collectors.toList());
        if (StringUtil.isEmpty(dtoPeople)) {
            result.setSuccess(false);
            failStr.append("；人员姓名不存在");
        }
    }

    private void isExistCert(ExcelVerifyHandlerResult result, DtoExpImpPersonAbility dto, List<DtoPersonCert> personCerts, StringBuilder failStr) {
        List<DtoPersonCert> dtoPersonCerts = personCerts.stream().filter(p -> dto.getPersonCertCode().equals(p.getCertCode())).collect(Collectors.toList());
        if (StringUtil.isEmpty(dtoPersonCerts)) {
            result.setSuccess(false);
            failStr.append("；证书编号不存在");
        }
    }

    //校验测试项目数据
    private void isTestData(ExcelVerifyHandlerResult result, List<DtoTest> testList, DtoExpImpPersonAbility dto, List<DtoSampleType> sampleTypes, StringBuilder failStr) {
        // 检测类型id赋值
        Optional<DtoSampleType> sampleTypeOptional = sampleTypes.stream().filter(p -> dto.getSampleTypeName().equals(p.getTypeName())).findFirst();
        if (sampleTypeOptional.isPresent()) {
            dto.setSampleTypeId(sampleTypeOptional.get().getId());
        } else {
            result.setSuccess(false);
            failStr.append("；检测类型不存在");
        }
        dto.setTestId(UUIDHelper.GUID_EMPTY);
        if (dto.getAbilityTypeName().equals("分析")) {
            List<DtoTest> dtoTests = testList.stream().filter(p -> p.getRedAnalyzeItemName().equals(dto.getRedAnalyzeItemName())
                    && p.getRedAnalyzeMethodName().equals(dto.getRedAnalyzeMethodName())
                    && p.getSampleTypeId().equals(dto.getSampleTypeId())).collect(Collectors.toList());
            if (StringUtil.isEmpty(dtoTests)) {
                result.setSuccess(false);
                failStr.append("；测试项目不存在");
            } else {
                DtoTest dtoTest = dtoTests.get(0);
                dto.setTestId(dtoTest.getId());
            }
        } else if (dto.getAbilityTypeName().equals("采样")) {
            List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodList.stream().filter(p -> p.getMethodName().equals(dto.getRedAnalyzeMethodName())
                    && p.getSampleTypeId().equals(dto.getSampleTypeId()) && p.getIsSamplingMethod()).collect(Collectors.toList());
            if (StringUtil.isEmpty(analyzeMethods)) {
                result.setSuccess(false);
                failStr.append("；采样方法不存在");
            }
        }
    }
}
