package com.sinoyd.base.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件路径定义
 * <AUTHOR>
 * @version V1.0.0 2019/09/17
 * @since V100R001
 */
@Component
@ConfigurationProperties(prefix="fileProps")
@Data
public class FilePathConfig {
    /**
     * 文件路径配置
     */
    private String filePath;

    /**
     * 输出路径
     */
    private String outputPath;

    /**
     * 模板名称
     */
    private String templatePath;

    /**
     * 允许上传的文件类型
     */
    private String fileSuffix;

    /**
     * 仪器解析文件路径配置
     */
    private String instrumentParseFilePath;

    /**
     * 上传文件时单个文件最大大小
     */
    private Integer singleFileSize;

    /**
     * 上传文件时所有文件最大大小
     */
    private Integer totalFileSize;
}
