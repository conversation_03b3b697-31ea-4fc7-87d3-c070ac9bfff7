package com.sinoyd.base.utils.http;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 远大HttpClient服务
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/11/10
 */
@Slf4j
public class SinoydHttpClient {

    private CloseableHttpClient httpClient;
    private RequestConfig requestConfig;

    public SinoydHttpClient() {
        init();
    }

    /**
     * http请求设置
     */
    public void init() {
        this.httpClient = HttpClients.createDefault();
        this.requestConfig = RequestConfig.custom()
                .setConnectTimeout(10000)
                .setConnectionRequestTimeout(10000)
                .setSocketTimeout(10000)
                .build();
    }

    /**
     * 关闭http请求
     */
    public void close() {
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error("=============关闭http请求失败===============");
                throw new BaseException("=============关闭http请求失败===============");
            }
        }
    }

    /**
     * post请求， 返回applicationo/json形式
     *
     * @param url     请求url
     * @param params  参数
     * @param headers 请求头部信息
     * @return 请求结果的String形式
     */
    public String doPost(String url, Object params, Map<String, String> headers) {
        //指定POST方式
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestConfig);
        //添加请求头部信息
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }
        //执行请求
        String result = "";
        try {
            //设置参数
            httpPost.setEntity(new StringEntity(JsonUtil.toJson(params), ContentType.create("application/json", "UTF-8")));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                HttpEntity entity = response.getEntity();
                result = EntityUtils.toString(entity);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BaseException("Http Post请求失败: " + url);
        }
        return result;
    }

    /**
     * HttpGet远程请求API
     *
     * @param url    路径
     * @param params 参数信息
     * @return 请求结果
     */
    public String doGet(String url, Map<String, Object> params, Map<String, String> headers) {
        try {

            URIBuilder uriBuilder = new URIBuilder(url);
            //设置参数
            if (params != null) {
                for (String key : params.keySet()) {
                    uriBuilder.setParameter(key, params.get(key).toString());
                }
            }
            HttpGet httpGet = new HttpGet(uriBuilder.build());
            httpGet.setConfig(requestConfig);
            //添加请求头部信息
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpGet.addHeader(entry.getKey(), entry.getValue());
                }
            }
            //执行请求
            CloseableHttpResponse response = httpClient.execute(httpGet);
            //获取请求结果
            HttpEntity resultEntity = response.getEntity();
            //获取请求结果的String形式
            return EntityUtils.toString(resultEntity);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BaseException("Http Get失败: " + url);
        }
    }

    /**
     * webservice访问接口
     *
     * @param reqData  接口参数
     * @param url      接口地址
     * @return         返回字符串
     */
    public String doRequest(String reqData, String url) {
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            //设置请求头
            httpPost.setHeader("Content-Type", "text/xml;charset=utf-8");
            StringEntity stringEntity = new StringEntity(reqData, StandardCharsets.UTF_8);
            httpPost.setEntity(stringEntity);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            return EntityUtils.toString(entity);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BaseException("Http 请求失败: " + url);
        }
    }

}