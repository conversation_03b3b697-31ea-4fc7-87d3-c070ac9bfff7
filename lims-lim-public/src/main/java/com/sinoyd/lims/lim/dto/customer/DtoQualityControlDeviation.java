package com.sinoyd.lims.lim.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

/**
 * 质控类的偏差公式配置前端传输对象
 * <AUTHOR>
 * @version V1.0.0 2023/04/24
 * @since V100R001
 */
@Data
public class DtoQualityControlDeviation {

    /**
     * id值
     */
    private String id = UUIDHelper.NewID();

    /**
     * 质控类型名称
     */
    private String qualityControlTypeName;

    /**
     * 质控类型编码
     */
    private Integer qualityControlTypeCode;

    /**
     * 偏差公式
     */
    private String deviationFormula;
}
