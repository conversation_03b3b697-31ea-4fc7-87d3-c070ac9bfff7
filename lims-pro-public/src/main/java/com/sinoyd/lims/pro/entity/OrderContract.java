package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;
import java.util.Date;


/**
 * OrderContract实体
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OrderContract")
 @Data
 public  class OrderContract extends LimsBaseEntity {

   private static final long serialVersionUID = 1L;

    public  OrderContract() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 订单id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("订单id")
	private String orderId;
    
    /**
    * 合同编号
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("合同编号")
    //@NotBlank(message = "合同编号{validation.message.blank}")
    @Length(message = "合同编号{validation.message.length}", max = 50)
	private String contractCode;
    
    /**
    * 合同名称
    */
    @Column(nullable=false)
    @ApiModelProperty("合同名称")
    //@NotBlank(message = "合同名称{validation.message.blank}")
    @Length(message = "合同名称{validation.message.length}", max = 255)
	private String contractName;
    
    /**
    * 合同性质
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("合同性质")
    //@NotBlank(message = "合同性质{validation.message.blank}")
    @Length(message = "合同性质{validation.message.length}", max = 50)
	private String contractNature;
    
    /**
    * 甲方id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("甲方id")
	private String firstEntId;
    
    /**
    * 甲方名称
    */
    @Column(nullable=false)
    @ApiModelProperty("甲方名称")
    //@NotBlank(message = "甲方名称{validation.message.blank}")
    @Length(message = "甲方名称{validation.message.length}", max = 255)
	private String firstEntName;
    
    /**
    * 甲方联系人
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("甲方联系人")
    //@NotBlank(message = "甲方联系人{validation.message.blank}")
    @Length(message = "甲方联系人{validation.message.length}", max = 50)
	private String firstEntPersonName;
    
    /**
    * 乙方名称
    */
    @Column(nullable=false)
    @ApiModelProperty("乙方名称")
    //@NotBlank(message = "乙方名称{validation.message.blank}")
    @Length(message = "乙方名称{validation.message.length}", max = 255)
	private String secondEntName;
    
    /**
    * 乙方联系人
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("乙方联系人")
    //@NotBlank(message = "乙方联系人{validation.message.blank}")
    @Length(message = "乙方联系人{validation.message.length}", max = 50)
	private String secondEntPersonName;
    
    /**
    * 乙方企业类型
    */
    @Column(length=50)
    @ApiModelProperty("乙方企业类型")
    @Length(message = "乙方企业类型{validation.message.length}", max = 50)
	private String secondEntType;
    
    /**
    * 合同金额
    */
    @ApiModelProperty("合同金额")
	private BigDecimal totalAmount;
    
    /**
    * 登记人
    */
    @Column(length=50)
    @ApiModelProperty("登记人")
    @Length(message = "登记人{validation.message.length}", max = 50)
	private String registrant;
    
    /**
    * 签订日期
    */
    @ApiModelProperty("签订日期")
	private Date signDate;
    
    /**
    * 合同履行开始时间
    */
    @ApiModelProperty("合同履行开始时间")
	private Date excuteStartTime;
    
    /**
    * 合同履行结束时间
    */
    @ApiModelProperty("合同履行结束时间")
	private Date excuteEndTime;
    
    /**
    * 
    */
    @Column(length=4000)
    @ApiModelProperty("")
	private String signPersonId;
    
    /**
    * 
    */
    @Column(length=0)
    @ApiModelProperty("")
	private Boolean isHavingSub;
    
    /**
    * 
    */
    @ApiModelProperty("")
	private BigDecimal subAmount;
    
    /**
    * 分包机构
    */
    @ApiModelProperty("分包机构")
    @Length(message = "分包机构{validation.message.length}", max = 255)
	private String subOrgs;
    
    /**
    * 合同概述
    */
    @Column(length=1000)
    @ApiModelProperty("合同概述")
    @Length(message = "合同概述{validation.message.length}", max = 1000)
	private String summary;

    /**
     *  甲方联系方式
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("甲方联系方式")
    //@NotBlank(message = "甲方联系方式{validation.message.blank}")
    @Length(message = "甲方联系方式{validation.message.length}", max = 50)
    private String firstEntPhone;

    /**
     *  备注
     */
    @Column(length=1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 合同状态（1签订,0未签订）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("合同状态（1签订,0未签订）")
    private Integer contractStatus;

    /**
    * 评审记录
    */
    @Column(length=1000)
    @ApiModelProperty("评审记录")
    @Length(message = "评审记录{validation.message.length}", max = 1000)
	private String assessRecord;

    /**
     * 监管平台甲方企业id
     */
    @Column(length = 25, nullable = false)
    @ApiModelProperty("监管平台甲方企业id")
    private String shanghaiEntId;

    /**
     * 监管平台甲方企业名称
     */
    @Column(length = 128, nullable = false)
    @ApiModelProperty("监管平台甲方企业名称")
    //@NotBlank(message = "监管平台甲方企业名称{validation.message.blank}")
    @Length(message = "监管平台甲方企业名称{validation.message.length}", max = 255)
    private String shanghaiEntName;

    /**
     * 是否推送
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否推送")
    private Boolean isHavingPut;

    /**
     * 合同id
     */
    @Column()
    @ApiModelProperty("合同id")
    private String cId;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
    
 }