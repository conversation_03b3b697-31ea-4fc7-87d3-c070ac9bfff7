package com.sinoyd.base.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.base.service.EvaluationCriteriaService;
import com.sinoyd.base.criteria.EvaluationCriteriaCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * EvaluationCriteria服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/16
 * @since V100R001
 */
@Api(tags = "示例: EvaluationCriteria服务")
@RestController
@RequestMapping("api/base/evaluationCriteria")
@Validated
public class EvaluationCriteriaController extends BaseJpaController<DtoEvaluationCriteria, String, EvaluationCriteriaService> {


    /**
     * 分页动态条件查询EvaluationCriteria
     *
     * @param evaluationCriteriaCriteria 条件参数
     * @return RestResponse<List < EvaluationCriteria>>
     */
    @ApiOperation(value = "分页动态条件查询EvaluationCriteria", notes = "分页动态条件查询EvaluationCriteria")
    @GetMapping
    public RestResponse<List<DtoEvaluationCriteria>> findByPage(EvaluationCriteriaCriteria evaluationCriteriaCriteria) {
        PageBean<DtoEvaluationCriteria> pageBean = super.getPageBean();
        RestResponse<List<DtoEvaluationCriteria>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, evaluationCriteriaCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询EvaluationCriteria
     *
     * @param id 主键id
     * @return RestResponse<DtoEvaluationCriteria>
     */
    @ApiOperation(value = "按主键查询EvaluationCriteria", notes = "按主键查询EvaluationCriteria")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoEvaluationCriteria> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoEvaluationCriteria> restResponse = new RestResponse<>();
        DtoEvaluationCriteria evaluationCriteria = service.findOne(id);
        restResponse.setData(evaluationCriteria);
        restResponse.setRestStatus(StringUtil.isNull(evaluationCriteria) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增EvaluationCriteria
     *
     * @param evaluationCriteria 实体列表
     * @return RestResponse<DtoEvaluationCriteria>
     */
    @ApiOperation(value = "新增EvaluationCriteria", notes = "新增EvaluationCriteria")
    @PostMapping
    public RestResponse<DtoEvaluationCriteria> create(@Validated @RequestBody DtoEvaluationCriteria evaluationCriteria) {
        RestResponse<DtoEvaluationCriteria> restResponse = new RestResponse<>();
        restResponse.setData(service.save(evaluationCriteria));
        return restResponse;
    }

    /**
     * 新增EvaluationCriteria
     *
     * @param evaluationCriteria 实体列表
     * @return RestResponse<DtoEvaluationCriteria>
     */
    @ApiOperation(value = "修改EvaluationCriteria", notes = "修改EvaluationCriteria")
    @PutMapping
    public RestResponse<DtoEvaluationCriteria> update(@Validated @RequestBody DtoEvaluationCriteria evaluationCriteria) {
        RestResponse<DtoEvaluationCriteria> restResponse = new RestResponse<>();
        restResponse.setData(service.update(evaluationCriteria));
        return restResponse;
    }

    /**
     * "根据id批量删除EvaluationCriteria
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除EvaluationCriteria", notes = "根据id批量删除EvaluationCriteria")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 刷新数据
     *
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "刷新数据", notes = "刷新数据")
    @PostMapping("/refresh")
    public RestResponse<Void> refresh() {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.refresh();
        return restResponse;
    }


}