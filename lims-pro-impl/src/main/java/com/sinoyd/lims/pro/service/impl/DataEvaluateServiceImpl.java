package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationLevel;
import com.sinoyd.base.dto.rcc.DtoEvaluationValue;
import com.sinoyd.base.service.EvaluationCriteriaService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.lims.pro.service.DataEvaluateService;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 数据评价操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
 @Service
public class DataEvaluateServiceImpl implements DataEvaluateService {

    @Autowired
    private CalculationService calculationService;

    @Autowired
    @Lazy
    private EvaluationCriteriaService evaluationCriteriaService;
    
    /**
     * 获取数据上实际的等级
     * @param evaluationId 评价标准id
     * @param parentId 条件项id
     * @param analyzeItemId 因子id
     * @param testValue 待评价值
     * @return 返回数据上实际的等级
     */
    @Override
    public DtoEvaluationLevel getRealLevel(String evaluationId, String parentId,String analyzeItemId, String testValue ) {
        DtoEvaluationCriteria eva = evaluationCriteriaService.findOne(evaluationId);
        //若所传分析项目id不为空且该评价标准配置了该因子
        if (StringUtils.isNotNullAndEmpty(analyzeItemId) &&
                StringUtil.isNotNull(eva.getEvaluationAnalyzeItem().stream().filter(p -> p.getAnalyzeItemId().equals(analyzeItemId)).findFirst())) {
            List<DtoEvaluationLevel> levels = eva.getEvaluationLevel().stream().filter(p -> p.getParentId().equals(parentId)).sorted(Comparator.comparing(DtoEvaluationLevel::getOrderNum)).collect(Collectors.toList());
            for (DtoEvaluationLevel level : levels) {
                Optional<DtoEvaluationValue> evaValue = level.getEvaluationValue().stream().filter(p -> p.getAnalyzeItemId().equals(analyzeItemId)).findFirst();
                if (StringUtil.isNotNull(evaValue) && evaValue.isPresent()) {
                    if (this.getIsPass(evaValue.get(), testValue)) {
                        return level;
                    }
                }
            }
        }

        DtoEvaluationLevel realLevel = new DtoEvaluationLevel();
        realLevel.setId("");
        realLevel.setName("");

        return realLevel;
    }

    /**
     * 判断是否合格
     * @param evaValue 评价值
     * @param testValue 待评价值
     * @return 返回是否合格
     */
    @Override
    public Boolean getIsPass(DtoEvaluationValue evaValue, String testValue) {
        if (MathUtil.isNumeral(testValue)) {
            List<String> limits = new ArrayList<>();
            if (StringUtils.isNotNullAndEmpty(evaValue.getLowerLimit()) && !evaValue.getLowerLimit().equals("-1")) {
                //默认>下限
                limits.add(String.format("[x] %s %s",
                        StringUtils.isNotNullAndEmpty(evaValue.getLowerLimitSymble()) ? evaValue.getLowerLimitSymble() : ">",
                        evaValue.getLowerLimit()));
            }
            if (StringUtils.isNotNullAndEmpty(evaValue.getUpperLimit()) && !evaValue.getUpperLimit().equals("-1")) {
                //默认<=上限
                limits.add(String.format("[x] %s %s",
                        StringUtils.isNotNullAndEmpty(evaValue.getUpperLimitSymble()) ? evaValue.getUpperLimitSymble() : "<=",
                        evaValue.getUpperLimit()));
            }
            if (limits.size() > 0) {
                String formula = String.join(" && ", limits);
                formula = formula.replace("＞", ">").replace("＜", "<");
                formula = formula.replace("≤", "<=").replace("≥", ">=");
                Map<String, Object> params = new HashMap<>();
                params.put("x", MathUtil.getBigDecimal(testValue));
                Object result = calculationService.calculationExpression(formula, params);
                if (StringUtil.isNotNull(result) && "FALSE".equals(String.valueOf(result).toUpperCase())) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取超标倍数
     * @param evaValue 评价值
     * @param testValue 待评价值
     * @return 返回超标倍数
     */
    @Override
    public String getOverProofTimes(DtoEvaluationValue evaValue, String testValue) {
        //若待评价值为数字才计算超标倍数
        if (MathUtil.isNumeral(testValue)) {
            if (StringUtils.isNotNullAndEmpty(evaValue.getLowerLimit())) {
                BigDecimal lowerLimit = new BigDecimal(evaValue.getLowerLimit());
                if (!lowerLimit.equals(BigDecimal.valueOf(0))) {
                    //配置了下限则不计算超标倍数
                    return "--";
                }
            }

            if (StringUtils.isNotNullAndEmpty(evaValue.getUpperLimit())) {
                BigDecimal value = new BigDecimal(testValue);
                BigDecimal upperLimit = new BigDecimal(evaValue.getUpperLimit());
                if (!upperLimit.equals(BigDecimal.valueOf(0))) {
                    //返回超标值/限值
                    return value.divide(upperLimit,2).toString();
                }
            }
        }
        return "--";
    }
}