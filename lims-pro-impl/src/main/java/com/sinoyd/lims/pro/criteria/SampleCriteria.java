package com.sinoyd.lims.pro.criteria;

import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumSampleCategory;


/**
 * Sample查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 送样单ids
     */
    private List<String> receiveIds;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 样品类型ids
     */
    private List<String> sampleTypeIds;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 领样单id
     */
    private String receiveSubId;

    /**
     * 是否样品管理
     */
    private Boolean isSampleManage = false;

    /**
     * 关键字（样品编号、委托方、受检方、点位）
     */
    private String key;

    /**
     * 样品类别
     */
    private Integer sampleCategory = -1;

    /**
     * 排序类型（1、按照样品编号排序 2、按照参数排序）
     */
    private Integer sortType;

    /**
     * 样品ids
     */
    private List<String> sampleIds;

    /**
     * 是否显示现场质控（具体项目用）
     */
    private Boolean isShowQc = true;

    /**
     * 留样处置样品搜索关键
     */
    private String sampleKey;

    /**
     * 留样处置项目搜索关键字
     */
    private String projectKey;

    /**
     * 标注访问来源，留样处置为1
     */
    private int pageFrom = -1;

    /**
     * 盲样类型
     */
    private Integer blindType = -1;

    /**
     * 样品状态
     */
    private List<String> statusList;

    /**
     * 是否包含现场指标
     */
    private Boolean withLocalTest;

    /**
     * 送样单编号,样品查询中使用
     */
    private String recordCode;

    /**
     * 是否项目登记
     */
    private Boolean isProject = false;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (pageFrom == 1) {
            condition.append(" and p.id = s.projectId");
            //留样处置只显示已经有样品编号的样品
            condition.append(" and s.code != ''");
        }
        condition.append(" and (s.isDeleted = 0 or s.status = :status)");
        condition.append(" and s.sampleCategory != 6");
        values.put("status", EnumPRO.EnumSampleStatus.样品作废.name());
        if (StringUtil.isNotEmpty(this.projectId) && !UUIDHelper.GUID_EMPTY.equals(this.projectId)) {
            if (isShowQc) {
                //查询从receiveIds中获取数据
                //condition.append(" and (s.projectId = :projectId or exists (select 1 from DtoReceiveSampleRecord r where s.receiveId = r.id and r.projectId = :projectId))");
                if (this.receiveIds.size() > 0) {
                    condition.append(" and (s.projectId = :projectId or s.receiveId in :receiveIds )");
                    values.put("receiveIds", this.receiveIds);
                } else {
                    condition.append(" and s.projectId = :projectId");
                }
            } else {
                condition.append(" and s.projectId = :projectId");
            }
            values.put("projectId", this.projectId);
        }
        if (StringUtil.isNotEmpty(this.receiveId) && !UUIDHelper.GUID_EMPTY.equals(this.receiveId)) {
            condition.append(" and s.receiveId = :receiveId");
            values.put("receiveId", this.receiveId);
        }
        if (StringUtil.isNotEmpty(receiveIds)) {
            condition.append(" and s.receiveId in :receiveIds");
            values.put("receiveIds", this.receiveIds);
        }
        if (StringUtil.isNotEmpty(this.sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(this.sampleTypeId)) {
            condition.append(" and s.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        if (StringUtil.isNotNull(this.sampleTypeIds) && this.sampleTypeIds.size() > 0) {
            condition.append(" and s.sampleTypeId in :sampleTypeIds");
            values.put("sampleTypeIds", this.sampleTypeIds);
        }
        if (StringUtils.isNotNullAndEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and s.samplingTimeBegin >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtils.isNotNullAndEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            to = c.getTime();
            condition.append(" and s.samplingTimeBegin < :endTime");
            values.put("endTime", to);
        }
        if (this.sampleCategory != -1) {
            condition.append(" and s.sampleCategory = :sampleCategory");
            values.put("sampleCategory", this.sampleCategory);
        }
        if (this.isSampleManage) {
            condition.append(" and s.code <> ''");
            condition.append(" and (s.sampleCategory = :sampleCategory or exists (select 1 from DtoReceiveSampleRecord r where s.receiveId = r.id))");
            values.put("sampleCategory", EnumSampleCategory.原样.getValue());

        }
        // 其他查询也会使用，所以没有放在 isSampleManage = true 下
        if (StringUtil.isNotEmpty(recordCode)) {
            condition.append(" and exists (select 1 from DtoReceiveSampleRecord r where r.id = s.receiveId and r.recordCode like :recordCode)");
            values.put("recordCode", "%" + this.recordCode + "%");
        }
        if (StringUtil.isNotEmpty(this.receiveSubId) && !UUIDHelper.GUID_EMPTY.equals(this.receiveSubId)) {
            condition.append(" and exists (select 1 from DtoReceiveSubSampleRecord2Sample r2s where s.id = r2s.sampleId and r2s.receiveSubSampleRecordId = :receiveSubId)");
            values.put("receiveSubId", this.receiveSubId);
        }
        if (StringUtil.isNotEmpty(this.key)) {
            condition.append(" and (s.code like :key or s.redFolderName like :key or s.inspectedEnt like :key)");
            values.put("key", "%" + this.key + "%");
        }

        if (StringUtil.isNotNull(sampleIds) && sampleIds.size() > 0) {
            condition.append(" and s.id in :sampleIds");
            values.put("sampleIds", this.sampleIds);
        }

        if (StringUtil.isNotEmpty(this.sampleKey)) {
            condition.append(" and (s.code like :sampleKey or s.redFolderName like :sampleKey) ");
            values.put("sampleKey", "%" + this.sampleKey + "%");
        }

        if (StringUtil.isNotEmpty(this.projectKey)) {
            condition.append(" and (p.projectName like :projectKey or p.projectCode like :projectKey or p.inspectedEnt like :projectKey)");
            values.put("projectKey", "%" + this.projectKey + "%");
        }
        if (this.blindType != -1) {
            condition.append(" and s.blindType = :blindType");
            values.put("blindType", this.blindType);
        }

        if (StringUtil.isNotEmpty(this.statusList)) {
            condition.append(" and s.status in (:statusList)");
            values.put("statusList", this.statusList);
        }
        if (withLocalTest != null) {
            if (Boolean.TRUE.equals(withLocalTest)) {
                condition.append(" and s.id in (select sampleId from DtoAnalyseData a where a.isCompleteField = :isCompleteField and  a.isDeleted = :isDeleted )");
            } else {
                condition.append(" and s.id  not in (select sampleId from DtoAnalyseData a where a.isCompleteField = :isCompleteField and  a.isDeleted = :isDeleted )");
            }
            values.put("isCompleteField", true);
            values.put("isDeleted", false);
        }
        return condition.toString();
    }
}