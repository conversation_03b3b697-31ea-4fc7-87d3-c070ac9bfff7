
DROP TABLE IF EXISTS TB_LIM_HolidayConfig;
CREATE TABLE TB_LIM_HolidayConfig  (
 id varchar(50)  NOT NULL COMMENT '主键',
 year int NOT NULL COMMENT '年份',
 holidayName varchar(50) NOT NULL COMMENT '节假日名称',
 beginDate date NOT NULL COMMENT '开始时间',
 endDate date NOT NULL COMMENT '结束时间',
 orgId varchar(50) NOT NULL COMMENT '组织机构id',
 creator varchar(50)  NOT NULL COMMENT '创建人',
 createDate datetime NOT NULL COMMENT '创建时间',
 domainId varchar(50) NOT NULL COMMENT '所属实验室',
 modifier varchar(50) NOT NULL COMMENT '修改人',
 modifyDate datetime NOT NULL COMMENT '修改时间',
 PRIMARY KEY (id)
)  COMMENT = '节假日管理配置';

DROP TABLE IF EXISTS TB_LIM_WorkdayConfig;
CREATE TABLE TB_LIM_WorkdayConfig  (
 id varchar(50)  NOT NULL COMMENT '主键',
 workday varchar(20) NOT NULL COMMENT '工作日（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六 以英文逗号拼接）',
 weekendDay varchar(20) NOT NULL COMMENT '休息日（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六 以英文逗号拼接）',
 year int NOT NULL COMMENT '年份',
 orgId varchar(50) NOT NULL COMMENT '组织机构id',
 creator varchar(50) NOT NULL COMMENT '创建人',
 createDate datetime NOT NULL COMMENT '创建时间',
 domainId varchar(50) NOT NULL COMMENT '所属实验室',
 modifier varchar(50) NOT NULL COMMENT '修改人',
 modifyDate datetime NOT NULL COMMENT '修改时间',
 PRIMARY KEY (id)
)  COMMENT = '工作休息日管理配置';

DROP TABLE IF EXISTS TB_LIM_CalendarDate;
CREATE TABLE TB_LIM_CalendarDate  (
 id varchar(50)  NOT NULL COMMENT '主键',
 holidayName varchar(50)  NULL DEFAULT NULL COMMENT '节假日名称',
 calendarDate date NOT NULL COMMENT '日历日期',
 weekday int NOT NULL COMMENT '星期数（1：周日，2：周一，3：周二 ，4：周三，5：周四，6：周五，7：周六）',
 type int NOT NULL COMMENT '类型（0：工作日，1：休息日）',
 orgId varchar(50) NOT NULL COMMENT '组织机构id',
 creator varchar(50) NOT NULL COMMENT '创建人',
 createDate datetime NOT NULL COMMENT '创建时间',
 domainId varchar(50) NOT NULL COMMENT '所属实验室',
 modifier varchar(50) NOT NULL COMMENT '修改人',
 modifyDate datetime NOT NULL COMMENT '修改时间',
 PRIMARY KEY (id)
) COMMENT = '日历日期';


