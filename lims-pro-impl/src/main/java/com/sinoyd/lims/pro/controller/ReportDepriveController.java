package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoReportDeprive;
import com.sinoyd.lims.pro.service.ReportDepriveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ReportDeprive服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
@Api(tags = "示例: reportrecover服务")
@RestController
@RequestMapping("api/pro/reportDeprive")
public class ReportDepriveController extends BaseJpaController<DtoReportDeprive, String, ReportDepriveService> {

    @ApiOperation(value = "项目Id查询扣发信息", notes = "项目Id查询扣发信息")
    @GetMapping("/findDeprive/{projectId}")
    public RestResponse<List<DtoReportDeprive>> findReport(@PathVariable("projectId") String projectId){
        RestResponse<List<DtoReportDeprive>> response = new RestResponse<>();
        response.setData(service.findByProjectId(projectId));
        response.setMsg("查询成功");
        return response;
    }


    @ApiOperation(value = "保存扣发信息", notes = "保存扣发信息")
    @PostMapping("/saveReportDeprive")
    public RestResponse<List<DtoReportDeprive>> save(@RequestBody List<DtoReportDeprive> reportDeprives){
        RestResponse<List<DtoReportDeprive>> response = new RestResponse<>();
        response.setData(service.save(reportDeprives));
        response.setMsg("保存成功");
        return response;
    }

    /**
     * "根据id删除报告扣发
     *
     * @param id id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id删除报告扣发", notes = "根据id删除报告扣发")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);

        return restResp;
    }
}
