package com.sinoyd.lims.lim.data.qcconfig.strategy;

import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.lims.lim.data.qcconfig.strategy.base.AbsQCConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.QualityControlLimitRepository;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 空白加标样接口
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/14
 */
@Component
@DependsOn({"springContextAware"})
@Slf4j
public class KBJBQCConfig extends AbsQCConfig {

    private QualityControlLimitRepository qualityControlLimitRepository;

    /**
     * 保存质控限值
     *
     * @param testQCRangeCopy 质控数据
     * @return 保存后的质控限值
     */
    @Override
    @Transactional
    public DtoQualityControlLimit save(DtoQualityControlLimit testQCRangeCopy) {
        //设置检查项范围与检查项类型
        if (testQCRangeCopy.getIsCheckItem() == 0){
            testQCRangeCopy.setRangeConfig(null);
            testQCRangeCopy.setCheckItem(-1);
            testQCRangeCopy.setQcType(getQcType());
        }
        return super.save(testQCRangeCopy);
    }

    /**
     * 查询此质控类型的所有测试项目关联的数据
     *
     * @param testId 测试项目编号
     * @return 查询结果
     */
    @Override
    public List<DtoQualityControlLimit> queryData(String testId) {
        return qualityControlLimitRepository.findByTestIdAndQcType(testId,getQcType());
    }

    /**
     * 获取质控类型
     *
     * @return 质控类型
     */
    @Override
    public Integer getQcType() {
        return EnumLIM.EnumQCType.空白加标.getValue();
    }

    /**
     * 获取排序值
     *
     * @return 排序值
     */
    @Override
    public Integer getOrderNum() {
        return 13;
    }

    /**
     * 获取质控类型名称
     *
     * @return 质控类型名称
     */
    @Override
    public String getQcTypeName() {
        return EnumLIM.EnumQCType.空白加标.name();
    }

    @Autowired
    public void setQualityControlLimitRepository(QualityControlLimitRepository qualityControlLimitRepository) {
        this.qualityControlLimitRepository = qualityControlLimitRepository;
    }
}
