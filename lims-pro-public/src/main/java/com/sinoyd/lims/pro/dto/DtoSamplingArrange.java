package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SamplingArrange;
import com.sinoyd.lims.pro.dto.DtoArrange2Method;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoSamplingArrange实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/20
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SamplingArrange")
@Data
@DynamicInsert
public class DtoSamplingArrange extends SamplingArrange {
    private static final long serialVersionUID = 1L;

    /**
     * 采样人员标识列表
     */
    @Transient
    private List<String> personIdList;

    /**
     * 采样人员名称列表
     */
    @Transient
    private List<String> personList;

    /**
     *  批量保存时对象回传
     */
    @Transient
    private List<String> idList;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 点位名称
     */
    @Transient
    private String watchSpot;

    /**
     * 点位编号
     */
    @Transient
    private  String folderCode;

    /**
     * 检测类型
     */
    @Transient
    private  String sampleType;

    /**
     *  週期顯示文本
     */
    @Transient
    private String periodName;

    /**
     * 分析项目名称
     */
    @Transient
    private String redAnalyzeItemName;

    /**
     * 是否设置为已安排
     */
    @Transient
    private Boolean isChangeStatus;

    /**
     * 采样方案实体
     */
    @Transient
    private List<DtoSamplingArrange> samplingArrangeList;

    /**
     * 采样方法实体
     */
    @Transient
    private List<DtoArrange2Method> arrange2MethodList;

    /**
     * 采样时间，多个、拼接
     */
    @Transient
    private String actualDateStr;

    /**
     * 送样单号，多个、拼接
     */
    @Transient
    private String recordCode;

    /**
     * 采样人员，多个、拼接
     */
    @Transient
    private String actualPeopleStr;
}
