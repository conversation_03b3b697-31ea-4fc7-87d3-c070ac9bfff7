package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReportNumberPool;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoReportNumberPool实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/8/1
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ReportNumberPool")
@Data
@DynamicInsert
public class DtoReportNumberPool extends ReportNumberPool {

    private static final long serialVersionUID = 1L;

    /**
     * 报告类型名称
     */
    @Transient
    private String reportName;


    /**
     * 流水号数量
     */
    @Transient
    private Integer serialNumber;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

}
