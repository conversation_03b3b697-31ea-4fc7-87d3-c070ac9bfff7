package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoLogForData;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 分析数据日志业务接口
 *
 * @version V1.0.0 2024/4/22
 * @author: hukq
 * @since V100R001
 */
public interface LogForDataService {

    /**
     * 多线程获取分析数据日志
     *
     * @param anaDataIds 分析数据id
     * @return 日志集合
     */
    Future<List<DtoLogForData>> getLogForData(List<String> anaDataIds);

}
