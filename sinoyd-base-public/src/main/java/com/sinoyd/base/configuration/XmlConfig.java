package com.sinoyd.base.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Scope;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.InputStream;

/**
 * 通用XML映射配置类
 *
 * <AUTHOR>
 * @version 5.2.0
 * @since 2023/05/12
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class XmlConfig {

    @Bean
    public QcRulesConfig getQcRulesConfigVO() {
        InputStream fileInputStream = null;
        try {
            JAXBContext ctx = JAXBContext.newInstance(QcRulesConfig.class);
            Unmarshaller unmarshaller = ctx.createUnmarshaller();
            ClassPathResource classPathResource = new ClassPathResource("rules/OrderRevise.xml");
            fileInputStream = classPathResource.getInputStream();
            QcRulesConfig vo = (QcRulesConfig) unmarshaller.unmarshal(fileInputStream);
            return vo;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            closeResource(fileInputStream);
        }
    }

    @Bean
    public QcDeviationFormulaConfig getQcDeviationFormulaConfigVO() {
        InputStream fileInputStream = null;
        try {
            JAXBContext ctx = JAXBContext.newInstance(QcDeviationFormulaConfig.class);
            Unmarshaller unmarshaller = ctx.createUnmarshaller();
            ClassPathResource classPathResource = new ClassPathResource("rules/DeviationFormulaSrc.xml");
            fileInputStream = classPathResource.getInputStream();
            QcDeviationFormulaConfig vo = (QcDeviationFormulaConfig) unmarshaller.unmarshal(fileInputStream);
            return vo;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            closeResource(fileInputStream);
        }
    }

    /**
     * 关闭资源
     *
     * @param is 输入流
     */
    private void closeResource(InputStream is) {
        if (is != null) {
            try {
                is.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}