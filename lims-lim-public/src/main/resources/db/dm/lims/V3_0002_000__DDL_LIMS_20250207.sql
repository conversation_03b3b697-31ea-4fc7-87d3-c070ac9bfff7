-- 人员培训计划
CREATE TABLE TB_QA_YearlyStaffTrainingPlan
(
    id             varchar(50)             NOT NULL,
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    name           varchar(50),
    content        varchar(500),
    people         varchar(500),
    deptId         varchar(50),
    timeRequire    varchar(50),
    method         varchar(50),
    target         varchar(50),
    personId       varchar(50),
    completion     varchar(500),
    planType       int                     NOT NULL DEFAULT 0,
    status         int                     NOT NULL DEFAULT 0,
    executeType    int                     NOT NULL DEFAULT 0,
    isDeleted      bit                     NOT NULL DEFAULT 0,
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP ()
);
ALTER TABLE TB_QA_YearlyStaffTrainingPlan ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_QA_YearlyStaffTrainingPlan is '人员培训计划';
comment on column TB_QA_YearlyStaffTrainingPlan.planId is '年度计划标识';
comment on column TB_QA_YearlyStaffTrainingPlan.detailId is '计划标识';
comment on column TB_QA_YearlyStaffTrainingPlan.name is '培训名称';
comment on column TB_QA_YearlyStaffTrainingPlan.content is '培训内容';
comment on column TB_QA_YearlyStaffTrainingPlan.people is '参训人员';
comment on column TB_QA_YearlyStaffTrainingPlan.deptId is '责任部门';
comment on column TB_QA_YearlyStaffTrainingPlan.timeRequire is '时间要求/完成时间';
comment on column TB_QA_YearlyStaffTrainingPlan.method is '培训方式';
comment on column TB_QA_YearlyStaffTrainingPlan.target is '培训对象';
comment on column TB_QA_YearlyStaffTrainingPlan.personId is '责任人标识';
comment on column TB_QA_YearlyStaffTrainingPlan.completion is '完成情况';
comment on column TB_QA_YearlyStaffTrainingPlan.status is '执行状态,0执行中/1完成';
comment on column TB_QA_YearlyStaffTrainingPlan.planType is '计划状态,0计划内/1计划外';
comment on column TB_QA_YearlyStaffTrainingPlan.executeType is '计划类型,0计划/1执行';
comment on column TB_QA_YearlyStaffTrainingPlan.isDeleted is '是否删除';
comment on column TB_QA_YearlyStaffTrainingPlan.orgId is '组织机构id';
comment on column TB_QA_YearlyStaffTrainingPlan.creator is '创建人';
comment on column TB_QA_YearlyStaffTrainingPlan.createDate is '创建时间';
comment on column TB_QA_YearlyStaffTrainingPlan.domainId is '所属实验室';
comment on column TB_QA_YearlyStaffTrainingPlan.modifier is '修改人';
comment on column TB_QA_YearlyStaffTrainingPlan.modifyDate is '修改时间';

-- 仪器期间核查计划
CREATE TABLE TB_QA_YearlyInstrumentCheckPlan
(
    id             varchar(50)             NOT NULL,
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    instrumentId   varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    planCheckTime  TIMESTAMP(0) ,
    content        varchar(500),
    deptId         varchar(50),
    personId       varchar(50),
    method         varchar(255),
    isQualified    bit                     NOT NULL DEFAULT 1,
    completion     varchar(500),
    planType       int                     NOT NULL DEFAULT 0,
    status         int                     NOT NULL DEFAULT 0,
    executeType    int                     NOT NULL DEFAULT 0,
    isDeleted      bit                     NOT NULL DEFAULT 0,
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP ()
);
ALTER TABLE TB_QA_YearlyInstrumentCheckPlan ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_QA_YearlyInstrumentCheckPlan is '仪器期间核查计划';
comment on column TB_QA_YearlyInstrumentCheckPlan.planId is '年度计划标识';
comment on column TB_QA_YearlyInstrumentCheckPlan.detailId is '计划标识';
comment on column TB_QA_YearlyInstrumentCheckPlan.instrumentId is '仪器标识';
comment on column TB_QA_YearlyInstrumentCheckPlan.planCheckTime is '计划核查时间/核查日期';
comment on column TB_QA_YearlyInstrumentCheckPlan.content is '核查内容';
comment on column TB_QA_YearlyInstrumentCheckPlan.personId is '责任人标识/核查人员标识';
comment on column TB_QA_YearlyInstrumentCheckPlan.deptId is '责任部门';
comment on column TB_QA_YearlyInstrumentCheckPlan.method is '核查方法';
comment on column TB_QA_YearlyInstrumentCheckPlan.isQualified is '核查结果，0合格/1不合格';
comment on column TB_QA_YearlyInstrumentCheckPlan.completion is '完成情况';
comment on column TB_QA_YearlyInstrumentCheckPlan.status is '执行状态,0执行中/1完成';
comment on column TB_QA_YearlyInstrumentCheckPlan.planType is '计划状态,0计划内/1计划外';
comment on column TB_QA_YearlyInstrumentCheckPlan.executeType is '计划类型,0计划/1执行';
comment on column TB_QA_YearlyInstrumentCheckPlan.isDeleted is '是否删除';
comment on column TB_QA_YearlyInstrumentCheckPlan.orgId is '组织机构id';
comment on column TB_QA_YearlyInstrumentCheckPlan.creator is '创建人';
comment on column TB_QA_YearlyInstrumentCheckPlan.createDate is '创建时间';
comment on column TB_QA_YearlyInstrumentCheckPlan.domainId is '所属实验室';
comment on column TB_QA_YearlyInstrumentCheckPlan.modifier is '修改人';
comment on column TB_QA_YearlyInstrumentCheckPlan.modifyDate is '修改时间';

-- 仪器检定校准计划
CREATE TABLE TB_QA_YearlyInstrumentConfirmPlan
(
    id             varchar(50)             NOT NULL,
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    instrumentId   varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    checkTime      TIMESTAMP(0),
    originType     int                     NOT NULL DEFAULT 1,
    frequency      int,
    isQualified    bit                     NOT NULL DEFAULT 1,
    enterpriseId   varchar(50),
    deptId         varchar(50),
    personId       varchar(50),
    indicator      varchar(50),
    basis          varchar(500),
    completion     varchar(500),
    planType       int                     NOT NULL DEFAULT 0,
    status         int                     NOT NULL DEFAULT 0,
    executeType    int                     NOT NULL DEFAULT 0,
    isDeleted      bit                     NOT NULL DEFAULT 0,
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP ()
);
ALTER TABLE TB_QA_YearlyInstrumentConfirmPlan ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_QA_YearlyInstrumentConfirmPlan is '仪器检定校准计划';
comment on column TB_QA_YearlyInstrumentConfirmPlan.planId is '年度计划标识';
comment on column TB_QA_YearlyInstrumentConfirmPlan.detailId is '计划标识';
comment on column TB_QA_YearlyInstrumentConfirmPlan.instrumentId is '仪器标识';
comment on column TB_QA_YearlyInstrumentConfirmPlan.checkTime is '检定校准日期';
comment on column TB_QA_YearlyInstrumentConfirmPlan.originType is '溯源方式(枚举：EnumOriginType:1检定、2校准、3自校)';
comment on column TB_QA_YearlyInstrumentConfirmPlan.frequency is '周期';
comment on column TB_QA_YearlyInstrumentConfirmPlan.isQualified is '检定结果，0合格/1不合格';
comment on column TB_QA_YearlyInstrumentConfirmPlan.enterpriseId is '检定单位';
comment on column TB_QA_YearlyInstrumentConfirmPlan.deptId is '责任部门';
comment on column TB_QA_YearlyInstrumentConfirmPlan.personId is '责任人标识';
comment on column TB_QA_YearlyInstrumentConfirmPlan.indicator is '技术指标';
comment on column TB_QA_YearlyInstrumentConfirmPlan.basis is '确认依据';
comment on column TB_QA_YearlyInstrumentConfirmPlan.completion is '完成情况';
comment on column TB_QA_YearlyInstrumentConfirmPlan.status is '执行状态,0执行中/1完成';
comment on column TB_QA_YearlyInstrumentConfirmPlan.planType is '计划状态,0计划内/1计划外';
comment on column TB_QA_YearlyInstrumentConfirmPlan.executeType is '计划类型,0计划/1执行';
comment on column TB_QA_YearlyInstrumentConfirmPlan.isDeleted is '是否删除';
comment on column TB_QA_YearlyInstrumentConfirmPlan.orgId is '组织机构id';
comment on column TB_QA_YearlyInstrumentConfirmPlan.creator is '创建人';
comment on column TB_QA_YearlyInstrumentConfirmPlan.createDate is '创建时间';
comment on column TB_QA_YearlyInstrumentConfirmPlan.domainId is '所属实验室';
comment on column TB_QA_YearlyInstrumentConfirmPlan.modifier is '修改人';
comment on column TB_QA_YearlyInstrumentConfirmPlan.modifyDate is '修改时间';

-- 标物期间核查计划
CREATE TABLE TB_QA_YearlyStandardCheckPlan
(
    id             varchar(50)             NOT NULL,
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    consumeId      varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    planCheckTime  TIMESTAMP(0),
    content        varchar(500),
    deptId         varchar(50),
    personId       varchar(50),
    method         varchar(255),
    isQualified    bit                     NOT NULL DEFAULT 0,
    completion     varchar(500),
    planType       int                     NOT NULL DEFAULT 0,
    status         int                     NOT NULL DEFAULT 0,
    executeType    int                     NOT NULL DEFAULT 0,
    isDeleted      bit                     NOT NULL DEFAULT 0,
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     TIMESTAMP(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP ()
);
ALTER TABLE TB_QA_YearlyStandardCheckPlan ADD CONSTRAINT PRIMARY KEY ("id");
comment on table TB_QA_YearlyStandardCheckPlan is '标物期间核查计划';
comment on column TB_QA_YearlyStandardCheckPlan.planId is '年度计划标识';
comment on column TB_QA_YearlyStandardCheckPlan.detailId is '计划标识';
comment on column TB_QA_YearlyStandardCheckPlan.consumeId is '标准物质标识';
comment on column TB_QA_YearlyStandardCheckPlan.planCheckTime is '计划核查时间/核查日期';
comment on column TB_QA_YearlyStandardCheckPlan.content is '核查内容';
comment on column TB_QA_YearlyStandardCheckPlan.deptId is '责任部门';
comment on column TB_QA_YearlyStandardCheckPlan.personId is '责任人标识/核查人员标识';
comment on column TB_QA_YearlyStandardCheckPlan.method is '核查方法';
comment on column TB_QA_YearlyStandardCheckPlan.isQualified is '核查结果，0合格/1不合格';
comment on column TB_QA_YearlyStandardCheckPlan.completion is '完成情况';
comment on column TB_QA_YearlyStandardCheckPlan.status is '执行状态,0执行中/1完成';
comment on column TB_QA_YearlyStandardCheckPlan.planType is '计划状态,0计划内/1计划外';
comment on column TB_QA_YearlyStandardCheckPlan.executeType is '计划类型,0计划/1执行';
comment on column TB_QA_YearlyStandardCheckPlan.isDeleted is '是否删除';
comment on column TB_QA_YearlyStandardCheckPlan.orgId is '组织机构id';
comment on column TB_QA_YearlyStandardCheckPlan.creator is '创建人';
comment on column TB_QA_YearlyStandardCheckPlan.createDate is '创建时间';
comment on column TB_QA_YearlyStandardCheckPlan.domainId is '所属实验室';
comment on column TB_QA_YearlyStandardCheckPlan.modifier is '修改人';
comment on column TB_QA_YearlyStandardCheckPlan.modifyDate is '修改时间';
