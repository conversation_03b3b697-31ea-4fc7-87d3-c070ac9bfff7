package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoProjectType;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.SerialIdentifierConfigRepository;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.lim.service.SerialIdentifierConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * SerialIdentifierConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @Service
public class SerialIdentifierConfigServiceImpl extends BaseJpaServiceImpl<DtoSerialIdentifierConfig,String,SerialIdentifierConfigRepository> implements SerialIdentifierConfigService {


    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Override
    public void findByPage(PageBean<DtoSerialIdentifierConfig> pb, BaseCriteria serialIdentifierConfigCriteria) {
        pb.setEntityName("DtoSerialIdentifierConfig a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, serialIdentifierConfigCriteria);
    }


    @Transactional
    @Override
    public DtoSerialIdentifierConfig save(DtoSerialIdentifierConfig entity) {
        //判断编号重复性
        if (StringUtils.isNotNullAndEmpty(entity.getConfigCode()) &&
                repository.countByConfigCode(entity.getConfigCode()) > 0) {
            throw new BaseException("【" + entity.getConfigCode() + "】重复，请确认");
        }
        DtoSerialIdentifierConfig item = super.save(entity);
        saveRedis(item);
        return item;
    }

    @Transactional
    @Override
    public DtoSerialIdentifierConfig update(DtoSerialIdentifierConfig entity) {
        //判断编号重复性
        if (StringUtils.isNotNullAndEmpty(entity.getConfigCode()) &&
                repository.countByConfigCodeAndIdNot(entity.getConfigCode(), entity.getId()) > 0) {
            throw new BaseException("【" + entity.getConfigCode() + "】重复，请确认");
        }
        DtoSerialIdentifierConfig item = super.update(entity);
        saveRedis(item);
        return item;
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String newId = id.toString();
        deleteRedis(newId);
        return super.logicDeleteById(id);
    }

    @Override
    public DtoSerialIdentifierConfig findByConfigType(Integer configType) {
        List<DtoSerialIdentifierConfig> configList = findListByConfigType(configType);
        Optional<DtoSerialIdentifierConfig> optional = configList.stream().max(Comparator.comparing(DtoSerialIdentifierConfig::getOrderNum));
        if (StringUtil.isNotNull(optional) && optional.isPresent()) {
            return optional.get();
        }
        return null;
    }

    @Override
    public DtoSerialIdentifierConfig findByConfigTypeAndProjectType(Integer configType, String projectTypeId) {
        List<DtoSerialIdentifierConfig> configList = findListByConfigType(configType);
        Optional<DtoSerialIdentifierConfig> optional = configList.stream().filter(p -> StringUtil.isNotEmpty(p.getProjectTypeId())
                && p.getProjectTypeId().contains(projectTypeId)).max(Comparator.comparing(DtoSerialIdentifierConfig::getOrderNum));
        if (StringUtil.isNotNull(optional) && optional.isPresent()) {
            return optional.get();
        }
        return null;
    }

    @Override
    public DtoSerialIdentifierConfig findByConfigType(Integer configType, Integer qcGrade, Integer qcType, String projectTypeId) {
        List<DtoSerialIdentifierConfig> configList = findListByConfigType(configType).stream()
                .filter(p -> p.getQcGrade().equals(qcGrade) && p.getQcType().equals(qcType))
                .sorted(Comparator.comparing(DtoSerialIdentifierConfig::getOrderNum).reversed())
                .collect(Collectors.toList());
        Optional<DtoSerialIdentifierConfig> optional = configList.stream().findFirst();
        if(StringUtil.isNotEmpty(projectTypeId)) {
            optional = configList.stream().filter(p -> StringUtil.isNotEmpty(p.getProjectTypeId()) && p.getProjectTypeId().contains(projectTypeId)).findFirst();
        }
        if (StringUtil.isNotNull(optional) && optional.isPresent()) {
            return optional.get();
        } else {
            optional = configList.stream().filter(p -> p.getQcGrade().equals(qcGrade)
                    && p.getQcType().equals(qcType)).max(Comparator.comparing(DtoSerialIdentifierConfig::getOrderNum));
            if (StringUtil.isNotNull(optional) && optional.isPresent()) {
                return optional.get();
            }
        }
        return null;
    }

    @Override
    public DtoSerialIdentifierConfig findByConfigType(Integer configType, Integer qcGrade, Integer qcType) {
        List<DtoSerialIdentifierConfig> configList = findListByConfigType(configType);
        Optional<DtoSerialIdentifierConfig> optional = configList.stream().filter(p -> p.getQcGrade().equals(qcGrade)
                && p.getQcType().equals(qcType)).max(Comparator.comparing(DtoSerialIdentifierConfig::getOrderNum));
        if (StringUtil.isNotNull(optional) && optional.isPresent()) {
            return optional.get();
        }
        return null;
    }

    @Override
    public DtoSerialIdentifierConfig findByConfigTypeAndConfigName(Integer configType, String configName) {
        List<DtoSerialIdentifierConfig> configList = findListByConfigType(configType);
        Optional<DtoSerialIdentifierConfig> optional = configList.stream().filter(p -> p.getConfigName().equals(configName)).max(Comparator.comparing(DtoSerialIdentifierConfig::getOrderNum));
        if (StringUtil.isNotNull(optional) && optional.isPresent()) {
            return optional.get();
        }
        return null;
    }

    @Override
    public List<DtoSerialIdentifierConfig> findListByConfigType(Integer configType) {
        List<DtoSerialIdentifierConfig> configList = new ArrayList<>();
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_IdentifierConfig.getValue());
        Object json = redisTemplate.opsForHash().get(key, configType.toString());
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<List<DtoSerialIdentifierConfig>> typeLiteral = new TypeLiteral<List<DtoSerialIdentifierConfig>>() {
            };
            configList = JsonIterator.deserialize(json.toString(), typeLiteral);

        }

        //从数据库中获取，并且将数据存入到redis中
        if (StringUtil.isNull(configList) || configList.size() == 0) {
            configList = repository.findByConfigType(configType);
            redisTemplate.opsForHash().put(key, configType.toString(), JsonStream.serialize(configList));
        }

        return configList;
    }

    @Override
    public Boolean validRule(DtoSerialIdentifierConfig entity) {
        String format = entity.getConfigRule().trim();
        String regex = "(?<=\\[).*?(?=\\])";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(format);
        List<String> rule = new ArrayList<>();
        switch (EnumLIM.EnumIdentifierConfig.getEnumIdentifierConfig(entity.getConfigType())) {
            case 项目编号:
            case 质控任务编号:
                rule = Arrays.asList(LimCodeHelper.PROJECT_FORMAT);
                break;
            case 样品编号:
                rule = Arrays.asList(LimCodeHelper.ASSOCIATE_FORMAT);
                break;
            case 质控样编号:
                rule = Arrays.asList(LimCodeHelper.QC_FORMAT);
                break;
            case 送样单编号:
                rule = Arrays.asList(LimCodeHelper.RECEVIESAMPLE_FORMAT);
                break;
            case 报告编号:
            case 回收报告编号:
                rule = Arrays.asList(LimCodeHelper.REPORT_FORMAT);
                break;
            case 检测单编号:
                rule = Arrays.asList(LimCodeHelper.WORKSHEET_FORMAT);
                break;
            case 合同编号:
                rule = Arrays.asList(LimCodeHelper.ORDERCONTRACT_FORMAT);
        }
        final List<Integer> sampleConfigType = Arrays.asList(EnumLIM.EnumIdentifierConfig.样品编号.getValue(), EnumLIM.EnumIdentifierConfig.质控样编号.getValue());
        Boolean needVerify = sampleConfigType.contains(entity.getConfigType());
        List<String> xcProjectTypeIds = new ArrayList<>();
        List<DtoProjectType> dtoProjectTypes = projectTypeService.findAll();
        Arrays.asList("XC", "SY").forEach(code -> {
            List<DtoProjectType> projectTypes = dtoProjectTypes.parallelStream()
                    .filter(item -> StringUtil.isNotEmpty(item.getConfig())
                            && (code.equals(JsonIterator.deserialize(item.getConfig(), Map.class).get("LIM_ProjectTypeCode_IND")) || code.equals(JsonIterator.deserialize(item.getConfig(), Map.class).get("projectRegisterPage"))))
                    .collect(Collectors.toList());
            xcProjectTypeIds.addAll(projectTypes.stream().map(DtoProjectType::getId).collect(Collectors.toList()));
        });
        List<String> verifyTypeIds = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(entity.getProjectTypeId())) {
            verifyTypeIds.addAll(Arrays.asList(entity.getProjectTypeId().split(",")));
        }
        while (matcher.find()) {
            //如何得到key
            String groupValue = matcher.group();
            if (groupValue.contains(LimCodeHelper.SN)) { //说明包含流水号
                //截取后续流水号的配置
                String newGroupValue = groupValue.substring(groupValue.indexOf("[") + 1, groupValue.length());
                if (!rule.contains(newGroupValue)) {
                    throw new BaseException("[" + groupValue + "]配置不对，请确认");
                }
            } else if (!rule.contains(groupValue)) {
                throw new BaseException("[" + groupValue + "]配置不对，请确认");
            }
            if (needVerify && StringUtil.isNotEmpty(verifyTypeIds) && groupValue.contains("receiveSampleRecord")) {
                for (String verifyTypeId : verifyTypeIds) {
                    if (!xcProjectTypeIds.contains(verifyTypeId)) {
                        throw new BaseException("[" + groupValue + "]配置，只能配置送样类任务");
                    }
                }
            }
        }
        return true;
    }

    /**
     * 保存相应的redis数据
     *
     * @param item 可配置编号的实体对象
     */
    @Override
    public void saveRedis(DtoSerialIdentifierConfig item) {
        List<DtoSerialIdentifierConfig> configList = new ArrayList<>();
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_IdentifierConfig.getValue());
        Object json = redisTemplate.opsForHash().get(key, item.getConfigType().toString());
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<List<DtoSerialIdentifierConfig>> typeLiteral = new TypeLiteral<List<DtoSerialIdentifierConfig>>() {
            };
            configList = JsonIterator.deserialize(json.toString(), typeLiteral);

            if (item.getQcGrade() != null && item.getQcGrade() != 0 && item.getQcType() != null && item.getQcType() != 0) {
                configList = configList.stream().filter(p -> !p.getId().equals(item.getId()) &&
                        !(p.getConfigType().equals(item.getConfigType()) && item.getQcGrade().equals(p.getQcGrade()) && item.getQcType().equals(p.getQcType())
                                && item.getProjectTypeId().equals(p.getProjectTypeId()))).distinct().collect(Collectors.toList());
            } else {
                configList = configList.stream().filter(p -> !p.getId().equals(item.getId())).distinct().collect(Collectors.toList());
            }
        }
        configList.add(item);
        redisTemplate.opsForHash().put(key, item.getConfigType().toString(), JsonStream.serialize(configList));
    }

    /**
     * 删除指定的redis相应的数据
     *
     * @param id 主键id
     */
    private void deleteRedis(String id) {
        DtoSerialIdentifierConfig item = super.findOne(id);
        List<DtoSerialIdentifierConfig> configList = new ArrayList<>();
        String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_IdentifierConfig.getValue());
        Object json = redisTemplate.opsForHash().get(key, item.getConfigType().toString());
        if (StringUtils.isNotNullAndEmpty(json)) {
            //解析相应的配置数据
            TypeLiteral<List<DtoSerialIdentifierConfig>> typeLiteral = new TypeLiteral<List<DtoSerialIdentifierConfig>>() {
            };
            configList = JsonIterator.deserialize(json.toString(), typeLiteral);

            configList = configList.stream().filter(p -> !p.getId().equals(item.getId()) &&
                    !(p.getConfigType().equals(item.getConfigType()) && p.getQcGrade().equals(item.getQcGrade())
                            && p.getQcType().equals(item.getQcType()))).distinct().collect(Collectors.toList());
        }
        redisTemplate.opsForHash().put(key, item.getConfigType().toString(), JsonStream.serialize(configList));
    }
}