package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * MpnConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/2/12
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "MpnConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class MpnConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public MpnConfig() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 测试项目ID
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("测试项目ID")
    @Length(message = "测试项目ID{validation.message.length}", max = 50)
    private String testId;

    /**
     * 参数1
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("参数1")
    @Length(message = "参数1{validation.message.length}", max = 50)
    private String param1Id;



    /**
     * 参数2
     */
    @Column(length = 50)
    @ApiModelProperty("参数2")
    @Length(message = "参数2{validation.message.length}", max = 50)
    private String param2Id;



    /**
     * 参数3
     */
    @Column(length = 50)
    @ApiModelProperty("参数3")
    @Length(message = "参数3{validation.message.length}", max = 50)
    private String param3Id;


    /**
     * 结果参数
     */
    @Column(length = 50)
    @ApiModelProperty("结果参数")
    @Length(message = "结果参数{validation.message.length}", max = 50)
    private String resultParamId;



    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}