package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 质控样品添加结构
 * <AUTHOR>
 * @version V1.0.0 2020/1/15
 * @since V100R001
 */
@Data
public class DtoQCSampleSave {
    /**
     * 样品id集合
     */
    private List<String> ids = new ArrayList<>();

    /**
     * 样品类别
     */
    private Integer sampleCategory;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    /**
     * 质控类型
     */
    private Integer qcType;
}
