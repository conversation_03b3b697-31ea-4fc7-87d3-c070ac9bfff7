package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleType2Test;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 检测模板-测试项目
 * <AUTHOR>
 * @version V1.0.0 2019/3/4
 * @since V100R001
 */
public interface SampleType2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleType2Test, String> {

    /**
     * 根据sampleTypeId获取相关测试项目
     *
     * @param sampleTypeId
     * @return 分析方法集合
     */
    @Query("select p.testId from DtoSampleType2Test p where p.sampleTypeId = :sampleTypeId")
    List<String> getListBySampleTypeId(@Param("sampleTypeId") String sampleTypeId);


    /**
     * 删除关联数据
     *
     * @param sampleTypeId 样品模板id
     * @return 返回删除数量
     */
    @Modifying
    @Query("delete from DtoSampleType2Test p where p.sampleTypeId = :sampleTypeId")
    Integer deleteBySampleTypeId(@Param("sampleTypeId") String sampleTypeId);

    /**
     * 根据sampleTypeId获取相关测试项目
     * @param sampleTypeId 样品类型id
     * @return 分析方法集合
     */
    List<DtoSampleType2Test> findBySampleTypeId(@Param("sampleTypeId") String sampleTypeId);

    /**
     * 根据模板id和测试项目id删除数据
     *
     * @param sampleTypeId 模板id
     * @param testIds 测试项目id
     * @return
     */
    Integer deleteBySampleTypeIdAndTestIdIn(String sampleTypeId, List<String> testIds);

    /**
     * 根据模板id和测试项目id查询数据
     *
     * @param sampleTypeId   模板id
     * @param testIds        测试项目id
     * @return  List<DtoSampleType2Test>
     */
    List<DtoSampleType2Test> findBySampleTypeIdAndTestIdIn(String sampleTypeId, List<String> testIds);

    /**
     * 根据总成配置id查询子测试项目
     *
     * @param parentIds  父级id集合
     * @return  List<DtoSampleType2Test>
     */
    List<DtoSampleType2Test> findByParentIdIn(List<String> parentIds);

    /**
     * 根据sampleTypeIds获取相关测试项目
     * @param sampleTypeIds 样品类型id集合
     * @return 分析方法集合
     */
    List<DtoSampleType2Test> findBySampleTypeIdIn(Collection<String> sampleTypeIds);
}