package com.sinoyd.lims.pro.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.base.dto.customer.DtoEvaluationBatchProcess;
import com.sinoyd.base.dto.customer.DtoEvaluationRecordCopyVO;
import com.sinoyd.base.dto.rcc.*;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.base.repository.rcc.EvaluationValueRepository;
import com.sinoyd.base.service.EvaluationCriteriaService;
import com.sinoyd.base.service.EvaluationValueService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.service.AnalyzeItemSortDetialService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSortDetil;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.repository.lims.FixedPointSortDetilRepository;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.pro.core.ProCodeHelper;
import com.sinoyd.lims.pro.criteria.EvaluationAnalyzeResultCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.entity.AnalyseData;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumEvaluationPlan;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumEvaluationType;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumReportEvaluationType;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.CalculatedUtils;
import com.sinoyd.lims.pro.util.MathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 评价记录操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/10
 * @since V100R001
 */
@Service
public class EvaluationRecordServiceImpl extends BaseJpaServiceImpl<DtoEvaluationRecord, String, EvaluationRecordRepository> implements EvaluationRecordService {
    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    @Lazy
    private DataEvaluateService dataEvaluateService;

    @Autowired
    @Lazy
    private SampleFolderService sampleFolderService;

    @Autowired
    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private EvaluationCriteriaService evaluationCriteriaService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    private FixedPointSortDetilRepository fixedPointSortDetilRepository;

    @Autowired
    private FixedpointRepository fixedpointRepository;

    @Autowired
    @Lazy
    private AnalyzeItemSortDetialService analyzeItemSortDetialService;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    private EvaluationRecordRepository evaluationRecordRepository;

    @Autowired
    private DimensionRepository dimensionRepository;

    @Autowired
    private EvaluationValueRepository evaluationValueRepository;

    @Autowired
    private EvaluationValueService evaluationValueService;

    @Override
    public void findByPage(PageBean<DtoEvaluationRecord> pb, BaseCriteria evaluationRecordCriteria) {
        pb.setEntityName("DtoEvaluationRecord a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, evaluationRecordCriteria);
    }

    /**
     * 获取项目下的初始化配置列表
     *
     * @param eva 点位id集合
     */
    @Override
    public List<DtoEvaluationValue> getInitEvaluation(DtoEvaluationBatch eva) {
        if ((eva.getObjectType().equals(EnumEvaluationType.点位.getValue()) &&
                eva.getFolderPlan().equals(EnumEvaluationPlan.实际.getValue()))
                || (eva.getObjectType().equals(EnumEvaluationType.分析数据.getValue()) &&
                eva.getFolderPlan().equals(EnumEvaluationPlan.分析数据.getValue()))) {
            //不需要带出来原来已经配置的评价标准，只需要返回空的数据源
            List<DtoSampleFolder> folders = sampleFolderService.findAll(eva.getObjectIds());
            List<String> sampleTypeIds = folders.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
            List<String> bigSampleTypeIds = sampleTypes.stream().map(DtoSampleType::getParentId).distinct().collect(Collectors.toList());
            List<DtoSampleType> bigSampleTypes = sampleTypeService.findRedisByIds(bigSampleTypeIds);
            List<DtoEvaluationValue> values = new ArrayList<>();
            for (DtoSampleType bigSamType : bigSampleTypes) {
                DtoEvaluationValue value = new DtoEvaluationValue();
                value.setSampleTypeId(bigSamType.getId());
                value.setSampleTypeName(bigSamType.getTypeName());
                values.add(value);
            }

            return values;
        } else {
            //TODO 点位实际评价时才有数据
            throw new BaseException("传参错误");
        }
    }

    /**
     * 批量保存评价记录
     *
     * @param eva
     */
    @Transactional
    @Override
    public void createBatch(DtoEvaluationBatch eva) {
        if ((eva.getObjectType().equals(EnumEvaluationType.点位.getValue()) &&
                eva.getFolderPlan().equals(EnumEvaluationPlan.实际.getValue()))
                || (eva.getObjectType().equals(EnumEvaluationType.分析数据.getValue()) &&
                eva.getFolderPlan().equals(EnumEvaluationPlan.分析数据.getValue()))) {

            //先按照分析数据id进行保存
            eva.setObjectType(EnumEvaluationType.分析数据.getValue());
            eva.setFolderPlan(EnumEvaluationPlan.分析数据.getValue());
            //将list转为map
            Map<String, DtoEvaluationValue> valueMap = eva.getEvaluationValue().stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getEvaluationId()) && !UUIDHelper.GUID_EMPTY.equals(p.getEvaluationId()) &&
                    StringUtils.isNotNullAndEmpty(p.getLevelId()) && !UUIDHelper.GUID_EMPTY.equals(p.getLevelId())).
                    collect(Collectors.toMap(DtoEvaluationValue::getSampleTypeId, value -> value));
            if (valueMap.keySet().size() == 0) {
                throw new BaseException("请选择需要批量配置的评价标准");
            }
            List<DtoSampleFolder> folders = sampleFolderService.findAll(eva.getObjectIds());
            Map<String, List<DtoSampleFolder>> groupFolders = folders.stream().collect(Collectors.groupingBy(DtoSampleFolder::getSampleTypeId, Collectors.toList()));

            List<String> analyseIdList = new ArrayList<>();
            List<DtoEvaluationRecord> records = new ArrayList<>();
            for (String sampleTypeId : groupFolders.keySet()) {//理论上不存在检测类型过多的场景，故这边遍历执行逻辑了
                DtoSampleType samType = sampleTypeService.findOne(sampleTypeId);
                if (valueMap.containsKey(samType.getParentId())) {
                    List<String> folderIds = groupFolders.get(sampleTypeId).stream().map(DtoSampleFolder::getId).distinct().collect(Collectors.toList());
                    List<DtoAnalyseData> loopAnalyseDataList = analyseDataService.findBySampleFolderIds(folderIds);
                    List<String> loopAnalyseIdList = loopAnalyseDataList.stream().map(DtoAnalyseData::getId).distinct().collect(Collectors.toList());
                    analyseIdList.addAll(loopAnalyseIdList);
                    DtoEvaluationValue evaValue = valueMap.get(samType.getParentId());
                    List<DtoEvaluationRecord> samTypeRecords = this.getAllRecordByEvaluation_anaData(evaValue, loopAnalyseDataList);
                    records.addAll(samTypeRecords);
                }
            }
            if (analyseIdList.size() > 0) {
                repository.deleteByObjectIdInAndFolderPlan(analyseIdList,
                        EnumEvaluationPlan.分析数据.getValue(),
                        PrincipalContextUser.getPrincipal().getUserId(), new Date());

                this.save(records);
            }

            //再按照点位d进行保存
            eva.setObjectType(EnumEvaluationType.点位.getValue());
            eva.setFolderPlan(EnumEvaluationPlan.实际.getValue());
//            List<DtoSampleFolder> folders2 = sampleFolderService.findAll(eva.getObjectIds());
//            Map<String, List<DtoSampleFolder>> groupFolders2 = folders2.stream().collect(Collectors.groupingBy(DtoSampleFolder::getSampleTypeId, Collectors.toList()));

            List<DtoEvaluationRecord> fldRecords = new ArrayList<>();
            List<String> sampleFolderIds = new ArrayList<>();
            for (String sampleTypeId : groupFolders.keySet()) {//理论上不存在检测类型过多的场景，故这边遍历执行逻辑了
                DtoSampleType samType = sampleTypeService.findOne(sampleTypeId);
                if (valueMap.containsKey(samType.getParentId())) {
                    List<String> folderIds = groupFolders.get(sampleTypeId).stream().map(DtoSampleFolder::getId).distinct().collect(Collectors.toList());
                    sampleFolderIds.addAll(folderIds);
                    DtoEvaluationValue evaValue = valueMap.get(samType.getParentId());
                    List<DtoEvaluationRecord> samTypeRecords = this.getAllRecordByEvaluation(evaValue, folderIds);
                    fldRecords.addAll(samTypeRecords);
                }
            }
            if (sampleFolderIds.size() > 0) {
                repository.deleteByObjectIdInAndFolderPlan(sampleFolderIds, EnumEvaluationPlan.实际.getValue(),
                        PrincipalContextUser.getPrincipal().getUserId(), new Date());
                this.save(fldRecords);
            }
        } else {
            //TODO 点位实际评价时才有数据
            throw new BaseException("传参错误");
        }
    }

    /**
     * 查询评价记录
     *
     * @param entity 传输实体
     */
    @Override
    public List<DtoEvaluationRecord> query(DtoEvaluationResult entity) {
        if (StringUtil.isNull(entity.getObjectIds()) || entity.getObjectIds().size() == 0) {
            throw new BaseException("请选择关联对象");
        }
        List<DtoEvaluationRecord> records = new ArrayList<>();
        Map<String, DtoTest> testMap = new HashMap<>();
        List<DtoTest> allTests = new ArrayList<>();

        if (entity.getObjectType().equals(EnumEvaluationType.点位.getValue()) &&
                entity.getFolderPlan().equals(EnumEvaluationPlan.实际.getValue())) {
            //读取web端所传点位相关的指标信息，并读取对应的测试项目
            List<DtoSamplingFrequencyTest> samplingFrequencyTests = this.getSamplingFrequencyTest(entity);
            List<String> allTestIds = samplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
            allTests = testService.findRedisByIds(allTestIds);
            //将list转为map
            testMap = allTests.stream().collect(Collectors.toMap(DtoTest::getId, test -> test));

            //读取web端所传点位相关的评价记录信息
            records = repository.findByObjectIdInAndObjectTypeAndFolderPlan(entity.getObjectIds(), entity.getObjectType(), entity.getFolderPlan());
            records = new ArrayList<>(records.stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getTestId,
                    Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values());
        } else if (entity.getObjectType().equals(EnumEvaluationType.分析数据.getValue()) &&
                entity.getFolderPlan().equals(EnumEvaluationPlan.分析数据.getValue())) {
            //读取前端传递的点位列表对应的analyseData数据列表，并从中获取到测试项目列表
            List<DtoAnalyseData> analyseDataList = new ArrayList<>();
            if (StringUtil.isNotEmpty(entity.getObjectIds())) {
                analyseDataList = analyseDataService.findBySampleFolderIds(entity.getObjectIds());
            }
            List<String> allTestIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
            allTests = testService.findRedisByIds(allTestIds);
            //建立测试项目id和测试项目对象的映射关系
            testMap = allTests.stream().collect(Collectors.toMap(DtoTest::getId, test -> test));
            //读取web端所传点位相关的评价记录信息
            List<String> anaIds = new ArrayList<>();
            analyseDataList.forEach(p -> anaIds.add(p.getId()));
            if (StringUtil.isNotEmpty(anaIds)) {
                records = repository.findByObjectIdInAndObjectTypeAndFolderPlan(anaIds, entity.getObjectType(), entity.getFolderPlan());
            }
            records = new ArrayList<>(records.stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getTestId,
                    Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values());
        } else {
            //TODO 点位实际评价时才有数据
            throw new BaseException("传参错误");
        }
        // 获取量纲
        List<String> dimensionIds = records.stream().map(DtoEvaluationRecord::getDimensionId).distinct().collect(Collectors.toList());
        List<DtoDimension> dimensionList = StringUtil.isNotEmpty(dimensionIds) ? dimensionRepository.findAll(dimensionIds) : new ArrayList<>();

        for (DtoEvaluationRecord record : records) {//对评价进行标准名称等信息赋值
            record.setEvaluationName(this.getEvaluationName(record.getEvaluationId()));
            record.setEvaluationLevelName(this.getEvaluationLevelName(record.getEvaluationId(), record.getEvaluationLevelId()));
            dimensionList.stream().filter(p -> p.getId().equals(record.getDimensionId())).findFirst().ifPresent(dimension -> {
                record.setDimensionName(dimension.getDimensionName());
            });
            if (testMap.containsKey(record.getTestId())) {
                DtoTest test = testMap.get(record.getTestId());
                record.setAnalyzeItemId(test.getAnalyzeItemId());
                record.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                record.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                record.setRedCountryStandard(test.getRedCountryStandard());
                record.setSampleTypeId(test.getSampleTypeId());//返回测试项目上的检测大类id，以供前端选择对应的评价标准
                record.setTestOrder(test.getOrderNum());
            } else {
                record.setRedAnalyzeMethodName("");
                record.setTestOrder(-1);
            }
        }

        //根据评价中的测试项目id过滤出点位上有的但未评价的测试指标，将其赋空标准并塞入评价记录一并返回
        List<String> recordTestIds = records.stream().map(DtoEvaluationRecord::getTestId).collect(Collectors.toList());
        allTests = allTests.stream().filter(p -> !recordTestIds.contains(p.getId())).collect(Collectors.toList());
        for (DtoTest test : allTests) {
            DtoEvaluationRecord record = new DtoEvaluationRecord();
            record.setTestId(test.getId());
            record.setAnalyzeItemId(test.getAnalyzeItemId());
            record.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
            record.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
            record.setRedCountryStandard(test.getRedCountryStandard());
            record.setSampleTypeId(test.getSampleTypeId());//返回测试项目上的检测大类id，以供前端选择对应的评价标准
            record.setTestOrder(test.getOrderNum());
            records.add(record);
        }
        records.stream().filter(v -> StringUtil.isEmpty(v.getDimensionId())).forEach(v -> v.setDimensionId(UUIDHelper.GUID_EMPTY));
        //先按照方法排序，在按照测试项目排序值排序
        records.sort(Comparator.comparing(DtoEvaluationRecord::getRedAnalyzeMethodName)
                .thenComparing(DtoEvaluationRecord::getTestOrder).reversed());
        return records;
    }

    /**
     * 保存评价记录
     *
     * @param entity 保存实体
     */
    @Transactional
    @Override
    public void saveRecord(DtoEvaluationResult entity) {
        if ((entity.getObjectType().equals(EnumEvaluationType.点位.getValue()) &&
                entity.getFolderPlan().equals(EnumEvaluationPlan.实际.getValue()))
                || (entity.getObjectType().equals(EnumEvaluationType.分析数据.getValue()) &&
                entity.getFolderPlan().equals(EnumEvaluationPlan.分析数据.getValue()))) {
            //先按照分析数据id保存
            entity.setObjectType(EnumEvaluationType.分析数据.getValue());
            entity.setFolderPlan(EnumEvaluationPlan.分析数据.getValue());
            List<String> testDistinctIds = entity.getEvaluationRecord().stream().map(DtoEvaluationRecord::getTestId).distinct().collect(Collectors.toList());
            //验证测试项目是否重复
            if (testDistinctIds.size() == entity.getEvaluationRecord().size()) {
                //将web端填写的限值信息按照测试项目id进行分组
                Map<String, DtoEvaluationRecord> recordMap = entity.getEvaluationRecord().stream().collect(Collectors.toMap(DtoEvaluationRecord::getTestId, record -> record));
                //读取web端所传点位相关的分析数据对象列表
                List<DtoAnalyseData> analyseDataList = new ArrayList<>();
                if (StringUtil.isNotEmpty(entity.getObjectIds())) {
                    analyseDataList = analyseDataService.findBySampleFolderIds(entity.getObjectIds());
                }
                //按照分析数据id对分析数据对象列表进行分组，将对应的分析数据id及测试项目的记录添加到list中
                List<DtoEvaluationRecord> list = new ArrayList<>();
                List<String> analyseIdList = new ArrayList<>();
                for (DtoAnalyseData analyseData : analyseDataList) {
                    analyseIdList.add(analyseData.getId());
                    createEvaluationRecord(analyseData.getTestId(), recordMap, analyseData.getId(), entity.getObjectType(), entity.getFolderPlan(), list);
                }

                //找出原有的评价数据
                List<DtoEvaluationRecord> records = repository.findByObjectIdInAndObjectTypeAndFolderPlan(analyseIdList, entity.getObjectType(), entity.getFolderPlan());
                list.addAll(records);

                //对预处理数据排序
                list.sort(Comparator.comparing(DtoEvaluationRecord::getId));
                List<DtoEvaluationRecord> forDel = new ArrayList<>();//删除的数据
                List<DtoEvaluationRecord> forUpt = new ArrayList<>();//更新的数据
                List<DtoEvaluationRecord> forAdd = new ArrayList<>();//新增的数据

                //按照分析数据id进行分组
                Map<String, List<DtoEvaluationRecord>> anaId2EvaRecListMap = list.stream().
                        collect(Collectors.groupingBy(DtoEvaluationRecord::getObjectId, Collectors.toList()));

                for (List<DtoEvaluationRecord> value : anaId2EvaRecListMap.values()) {
                    colAddUptDelEvaRecList(forAdd, forUpt, forDel, value);
                }

                if (forDel.size() > 0) {//删除需要删除的评价记录
                    this.delete(forDel);
                }
                if (forUpt.size() > 0) {//更新需要更新的评价记录
                    this.update(forUpt);
                }
                if (forAdd.size() > 0) {//添加需要添加的评价记录
                    this.save(forAdd);
                }

            } else {
                throw new BaseException("设置的测试项目不能重复，请确认！");
            }

            //再按照点位id保存
            entity.setObjectType(EnumEvaluationType.点位.getValue());
            entity.setFolderPlan(EnumEvaluationPlan.实际.getValue());
            List<String> testDistinctIds2 = entity.getEvaluationRecord().stream().map(DtoEvaluationRecord::getTestId).distinct().collect(Collectors.toList());
            //验证测试项目是否重复
            if (testDistinctIds2.size() == entity.getEvaluationRecord().size()) {
                //将web端填写的限值信息按照测试项目id进行分组
                Map<String, DtoEvaluationRecord> recordMap = entity.getEvaluationRecord().stream().collect(Collectors.toMap(DtoEvaluationRecord::getTestId, record -> record));

                //读取web端所传点位相关的指标信息
                List<DtoSamplingFrequencyTest> samplingFrequencyTests = this.getSamplingFrequencyTest(entity);

                //按照点位id进行频次指标的分组，将对应点位既及指标的记录扔到list
                List<DtoEvaluationRecord> list = new ArrayList<>();
                samplingFrequencyTests.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSampleFolderId, Collectors.toList())).forEach((sampleFolderId, datas) -> {
                    Set<String> testIds = datas.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
                    for (String testId : testIds) {
                        if (recordMap.containsKey(testId)) {
                            DtoEvaluationRecord temp = recordMap.get(testId);
                            DtoEvaluationRecord record = new DtoEvaluationRecord();
                            record.setId("");
                            record.setObjectId(sampleFolderId);
                            record.setObjectType(entity.getObjectType());
                            record.setFolderPlan(entity.getFolderPlan());
                            record.setTestId(testId);
                            record.setEvaluationId(UUIDHelper.GUID_EMPTY);
                            record.setEvaluationLevelId(UUIDHelper.GUID_EMPTY);
                            if (StringUtils.isNotNullAndEmpty(temp.getEvaluationId())) {
                                record.setEvaluationId(temp.getEvaluationId());
                            }
                            if (StringUtils.isNotNullAndEmpty(temp.getEvaluationLevelId())) {
                                record.setEvaluationLevelId(temp.getEvaluationLevelId());
                            }
                            record.setLowerLimitValue(temp.getLowerLimitValue());
                            record.setUpperLimitValue(temp.getUpperLimitValue());
                            record.setLowerLimitSymble(temp.getLowerLimitSymble());
                            record.setUpperLimitSymble(temp.getUpperLimitSymble());
                            record.setDimensionId(temp.getDimensionId());
                            record.setEmissionRate(temp.getEmissionRate());
                            list.add(record);
                        }
                    }
                });

                List<DtoEvaluationRecord> records = repository.findByObjectIdInAndObjectTypeAndFolderPlan(entity.getObjectIds(), entity.getObjectType(), entity.getFolderPlan());
                list.addAll(records);

                //对预处理数据排序
                list.sort(Comparator.comparing(DtoEvaluationRecord::getId));
                List<DtoEvaluationRecord> forDel = new ArrayList<>();//删除的数据
                List<DtoEvaluationRecord> forUpt = new ArrayList<>();//更新的数据
                List<DtoEvaluationRecord> forAdd = new ArrayList<>();//新增的数据

                //按照点位指标进行分组
                Map<String, Map<String, List<DtoEvaluationRecord>>> ftMap = list.stream().
                        collect(Collectors.groupingBy(DtoEvaluationRecord::getObjectId, Collectors.
                                groupingBy(DtoEvaluationRecord::getTestId, Collectors.toList())));

                for (Map<String, List<DtoEvaluationRecord>> tMap : ftMap.values()) {
                    for (List<DtoEvaluationRecord> value : tMap.values()) {
                        colAddUptDelEvaRecList(forAdd, forUpt, forDel, value);
                    }
                }

                if (forDel.size() > 0) {//删除需要删除的评价记录
                    this.logicDeleteById(forDel.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()));
                }
                if (forUpt.size() > 0) {//更新需要更新的评价记录
                    this.update(forUpt);
                }
                if (forAdd.size() > 0) {//添加需要添加的评价记录
                    this.save(forAdd);
                }
            } else {
                throw new BaseException("设置的测试项目不能重复，请确认！");
            }
        } else {
            //TODO 点位实际评价时才有数据
            throw new BaseException("传参错误");
        }
    }

    @Transactional
    @Override
    public void batchProcess(DtoEvaluationBatchProcess evaluationBatchProcess) {
        if ((evaluationBatchProcess.getObjectType().equals(EnumEvaluationType.点位.getValue()) &&
                evaluationBatchProcess.getFolderPlan().equals(EnumEvaluationPlan.实际.getValue()))
                || (evaluationBatchProcess.getObjectType().equals(EnumEvaluationType.分析数据.getValue()) &&
                evaluationBatchProcess.getFolderPlan().equals(EnumEvaluationPlan.分析数据.getValue()))) {
            //先按分析数据id设置评价记录
            List<DtoAnalyseData> analyseDataList = new ArrayList<>();
            if (StringUtil.isNotEmpty(evaluationBatchProcess.getSampleFolderIdList())) {
                analyseDataList = analyseDataService.findBySampleFolderIds(evaluationBatchProcess.getSampleFolderIdList());
            }
            List<DtoEvaluationValue> evaluationValueList = new ArrayList<>();
            if (StringUtil.isNotEmpty(evaluationBatchProcess.getEvaluationId()) && StringUtil.isNotEmpty(evaluationBatchProcess.getEvaluationLevelId())) {
                evaluationValueList = evaluationValueRepository.findByLevelId(evaluationBatchProcess.getEvaluationLevelId());
            }
            //按照分析项目分组
            Map<String, List<DtoEvaluationValue>> anaId2EvaValListMap = evaluationValueList.stream().collect(Collectors.groupingBy(DtoEvaluationValue::getAnalyzeItemId));
            //放置要新增的评价记录
            List<DtoEvaluationRecord> instList = new ArrayList<>();
            Map<String, DtoEvaluationRecord> recordMap = new HashMap<>();
            List<String> analyseIdList = new ArrayList<>();
            for (DtoAnalyseData analyseData : analyseDataList) {
                analyseIdList.add(analyseData.getId());
                DtoEvaluationRecord evaluationRecord = new DtoEvaluationRecord();
                evaluationRecord.setEvaluationId(evaluationBatchProcess.getEvaluationId());
                evaluationRecord.setEvaluationLevelId(evaluationBatchProcess.getEvaluationLevelId());
                if (anaId2EvaValListMap.containsKey(analyseData.getAnalyseItemId())) {
                    DtoEvaluationValue evaluationValue = anaId2EvaValListMap.get(analyseData.getAnalyseItemId()).get(0);
                    evaluationRecord.setLowerLimitValue(evaluationValue.getLowerLimit());
                    evaluationRecord.setUpperLimitValue(evaluationValue.getUpperLimit());
                    evaluationRecord.setUpperLimitSymble(evaluationValue.getUpperLimitSymble());
                    evaluationRecord.setLowerLimitSymble(evaluationValue.getLowerLimitSymble());
                    evaluationRecord.setDimensionId(evaluationValue.getDimensionId());
                    evaluationRecord.setEmissionRate(evaluationValue.getEmissionRate());
                }
                recordMap.put(analyseData.getTestId(), evaluationRecord);
                createEvaluationRecord(analyseData.getTestId(), recordMap, analyseData.getId(), EnumEvaluationType.分析数据.getValue(),
                        EnumEvaluationPlan.分析数据.getValue(), instList);
            }

            //找出原有的评价数据
            List<DtoEvaluationRecord> anaDataIdRecords = repository.findByObjectIdInAndObjectTypeAndFolderPlan(analyseIdList, EnumEvaluationType.分析数据.getValue(),
                    EnumEvaluationPlan.分析数据.getValue());
            if (StringUtil.isNotEmpty(anaDataIdRecords)) {
                //删除原有的记录
                repository.logicDeleteById(anaDataIdRecords.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }
            //插入新增的记录
            if (StringUtil.isNotEmpty(instList)) {
                for (DtoEvaluationRecord evaluationRecord : instList) {
                    evaluationRecord.setId(UUIDHelper.NewID());
                }
                repository.save(instList);
            }
            //再按照点位id设置评价记录
            List<DtoEvaluationRecord> fldInstlist = new ArrayList<>();
            List<DtoSamplingFrequencyTest> samplingFrequencyTests = new ArrayList<>();
            if (StringUtil.isNotEmpty(evaluationBatchProcess.getSampleFolderIdList())) {
                samplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(evaluationBatchProcess.getSampleFolderIdList());
            }
            samplingFrequencyTests.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSampleFolderId, Collectors.toList())).forEach((sampleFolderId, datas) -> {
                Set<String> testIds = datas.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
                Map<String, String> tstId2AnaIdMap = new HashMap<>();
                datas.forEach(p -> tstId2AnaIdMap.put(p.getTestId(), p.getAnalyseItemId()));
                for (String testId : testIds) {
                    DtoEvaluationRecord record = new DtoEvaluationRecord();
                    record.setObjectId(sampleFolderId);
                    record.setObjectType(EnumEvaluationType.点位.getValue());
                    record.setFolderPlan(EnumEvaluationPlan.实际.getValue());
                    record.setTestId(testId);
                    record.setEvaluationId(StringUtils.isNotNullAndEmpty(evaluationBatchProcess.getEvaluationId())
                            ? evaluationBatchProcess.getEvaluationId() : UUIDHelper.GUID_EMPTY);
                    record.setEvaluationLevelId(StringUtils.isNotNullAndEmpty(evaluationBatchProcess.getEvaluationLevelId())
                            ? evaluationBatchProcess.getEvaluationLevelId() : UUIDHelper.GUID_EMPTY);
                    String anaId = tstId2AnaIdMap.get(testId);
                    if (anaId2EvaValListMap.containsKey(anaId)) {
                        DtoEvaluationValue evaluationValue = anaId2EvaValListMap.get(anaId).get(0);
                        record.setLowerLimitValue(evaluationValue.getLowerLimit());
                        record.setUpperLimitValue(evaluationValue.getUpperLimit());
                        record.setUpperLimitSymble(evaluationValue.getUpperLimitSymble());
                        record.setLowerLimitSymble(evaluationValue.getLowerLimitSymble());
                        record.setDimensionId(evaluationValue.getDimensionId());
                        record.setEmissionRate(evaluationValue.getEmissionRate());
                    }
                    fldInstlist.add(record);
                }
            });
            //找出原有的记录
            List<DtoEvaluationRecord> fldIdRecords = repository.findByObjectIdInAndObjectTypeAndFolderPlan(evaluationBatchProcess.getSampleFolderIdList(),
                    EnumEvaluationType.点位.getValue(), EnumEvaluationPlan.实际.getValue());
            //删除原有的记录
            if (StringUtil.isNotEmpty(fldIdRecords)) {
                repository.logicDeleteById(fldIdRecords.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }
            //插入新增的记录
            if (StringUtil.isNotEmpty(fldInstlist)) {
                repository.save(fldInstlist);
            }
        }
    }

    /**
     * 复制评价信息
     *
     * @param eva 复制传输实体
     */
    @Override
    @Transactional
    public void copy(DtoEvaluationRecordCopyVO eva) {
        String sourceSampleFolderId = eva.getSampleFolderId();
        // 复制源-点位评价数据
        List<DtoEvaluationRecord> sourceEvaluationList = repository.findByObjectIdInAndObjectTypeAndFolderPlan(Collections.singleton(sourceSampleFolderId), eva.getObjectType(), eva.getFolderPlan());
        if (StringUtil.isNotEmpty(sourceEvaluationList)) {

            List<String> testIds = sourceEvaluationList.stream().map(DtoEvaluationRecord::getTestId).distinct().collect(Collectors.toList());
            Map<String, DtoEvaluationRecord> recordMap = sourceEvaluationList.stream().collect(Collectors.toMap(DtoEvaluationRecord::getTestId, record -> record));

            // 获取复制源-分析数据评价信息
            List<DtoAnalyseData> targetAnalyseDataList = new ArrayList<>();
            if (StringUtil.isNotEmpty(eva.getTargetSampleFolderIds())) {
                targetAnalyseDataList = analyseDataService.findBySampleFolderIds(eva.getTargetSampleFolderIds());
            }
            // 根据测试项目过滤
            targetAnalyseDataList = targetAnalyseDataList.stream().filter(p -> testIds.contains(p.getTestId())).collect(Collectors.toList());
            List<DtoEvaluationRecord> instList = new ArrayList<>();
            List<String> analyseIdList = new ArrayList<>();
            for (DtoAnalyseData analyseData : targetAnalyseDataList) {
                analyseIdList.add(analyseData.getId());
                // 创建分析数据评价信息
                createEvaluationRecord(analyseData.getTestId(), recordMap, analyseData.getId(), EnumEvaluationType.分析数据.getValue(),
                        EnumEvaluationPlan.分析数据.getValue(), instList);
            }
            //找出原有的评价数据，删除原有的记录
            List<DtoEvaluationRecord> anaDataIdRecords = repository.findByObjectIdInAndObjectTypeAndFolderPlan(analyseIdList, EnumEvaluationType.分析数据.getValue(),
                    EnumEvaluationPlan.分析数据.getValue());
            if (StringUtil.isNotEmpty(anaDataIdRecords)) {
                repository.logicDeleteById(anaDataIdRecords.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }
            //插入新增的记录
            if (StringUtil.isNotEmpty(instList)) {
                for (DtoEvaluationRecord evaluationRecord : instList) {
                    evaluationRecord.setId(UUIDHelper.NewID());
                }
                repository.save(instList);
            }
            // 点位评价复制
            List<DtoSamplingFrequencyTest> samplingFrequencyTests = new ArrayList<>();
            if (StringUtil.isNotEmpty(eva.getTargetSampleFolderIds())) {
                samplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(eva.getTargetSampleFolderIds());
            }
            List<DtoEvaluationRecord> fldInstlist = new ArrayList<>();
            samplingFrequencyTests.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSampleFolderId, Collectors.toList())).forEach((sampleFolderId, datas) -> {
                Set<String> testIdList = datas.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet());
                for (String testId : testIdList) {
                    DtoEvaluationRecord evaluationRecord = recordMap.get(testId);
                    DtoEvaluationRecord record = new DtoEvaluationRecord();
                    record.setObjectId(sampleFolderId);
                    record.setObjectType(EnumEvaluationType.点位.getValue());
                    record.setFolderPlan(EnumEvaluationPlan.实际.getValue());
                    record.setTestId(testId);
                    if (StringUtil.isNotNull(evaluationRecord)) {
                        record.setEvaluationLevelId(evaluationRecord.getEvaluationLevelId());
                        record.setEvaluationId(evaluationRecord.getEvaluationId());
                        record.setLowerLimitValue(evaluationRecord.getLowerLimitValue());
                        record.setUpperLimitValue(evaluationRecord.getUpperLimitValue());
                        record.setUpperLimitSymble(evaluationRecord.getUpperLimitSymble());
                        record.setLowerLimitSymble(evaluationRecord.getLowerLimitSymble());
                        record.setDimensionId(evaluationRecord.getDimensionId());
                        record.setEmissionRate(evaluationRecord.getEmissionRate());
                    }
                    fldInstlist.add(record);
                }
            });
            //找出原有的记录
            List<DtoEvaluationRecord> fldIdRecords = repository.findByObjectIdInAndObjectTypeAndFolderPlan(eva.getTargetSampleFolderIds(),
                    EnumEvaluationType.点位.getValue(), EnumEvaluationPlan.实际.getValue());
            //删除原有的记录
            if (StringUtil.isNotEmpty(fldIdRecords)) {
                repository.logicDeleteById(fldIdRecords.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }
            //插入新增的记录
            if (StringUtil.isNotEmpty(fldInstlist)) {
                repository.save(fldInstlist);
            }
        }
    }

    @Transactional
    @Override
    public void clearRecord(List<String> folderIdList) {
        if (StringUtil.isNotEmpty(folderIdList)) {
            List<DtoAnalyseData> analyseDataList = analyseDataService.findBySampleFolderIds(folderIdList);
            List<String> analyseDataIdList = analyseDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            //先清空objectId为分析数据id的记录
            List<DtoEvaluationRecord> anaIdRecordList = repository.findByObjectIdInAndObjectTypeAndFolderPlan(analyseDataIdList,
                    EnumEvaluationType.分析数据.getValue(), EnumEvaluationPlan.分析数据.getValue());
            if (StringUtil.isNotEmpty(anaIdRecordList)) {
                repository.logicDeleteById(anaIdRecordList.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }
            //再清空objectId为点位id的记录
            List<DtoEvaluationRecord> fldIdRecordList = repository.findByObjectIdInAndObjectTypeAndFolderPlan(folderIdList,
                    EnumEvaluationType.点位.getValue(), EnumEvaluationPlan.实际.getValue());
            if (StringUtil.isNotEmpty(fldIdRecordList)) {
                repository.logicDeleteById(fldIdRecordList.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }
        }
    }

    /**
     * 更新排放速率
     *
     * @param sampleTypeIds              样品类型ids
     * @param evaluationRecordList   评价提醒集合
     * @param analyseOriginalRecords 分析公式数据集合
     * @param analyseDataList        分析数据集合
     */
    @Override
    @Transactional
    public void updateEmissionRate(List<String> sampleTypeIds,
                                   List<DtoEvaluationRecord> evaluationRecordList,
                                   List<DtoAnalyseOriginalRecord> analyseOriginalRecords,
                                   List<DtoAnalyseData> analyseDataList) {
        List<DtoEvaluationRecord> updateEvaluationRecords = new ArrayList<>();
        // 更新评价提醒
        List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findRedisByIds(sampleTypeIds) : new ArrayList<>();
        boolean isEmissionRate = StringUtil.isNotEmpty(sampleTypes) ? sampleTypes.stream().anyMatch(DtoSampleType::getIsEmissionRate) : Boolean.FALSE;
        if (isEmissionRate) {
            Map<String, DtoAnalyseOriginalRecord> analyseOriginalRecordMap = analyseOriginalRecords.stream().collect(Collectors.toMap(DtoAnalyseOriginalRecord::getAnalyseDataId, p -> p, (p1, p2) -> p1));
            TypeLiteral<List<DtoAnalyseOriginalJson>> typeLiteral = new TypeLiteral<List<DtoAnalyseOriginalJson>>() {
            };
            for (DtoAnalyseData dtoAnalyseData : analyseDataList) {
                Optional<DtoEvaluationRecord> evaluationRecordOptional = evaluationRecordList.stream().filter(p -> p.getObjectId().equals(dtoAnalyseData.getId())).findFirst();
                if (evaluationRecordOptional.isPresent()) {
                    DtoEvaluationRecord evaluationRecord = evaluationRecordOptional.get();
                    // 标干流量
                    String bgl = "";
                    DtoAnalyseOriginalRecord analyseOriginalRecord = analyseOriginalRecordMap.getOrDefault(dtoAnalyseData.getId(), null);
                    if (StringUtil.isNotNull(analyseOriginalRecord)) {
                        List<DtoAnalyseOriginalJson> deserialize = JsonIterator.deserialize(analyseOriginalRecord.getJson(), typeLiteral);
                        DtoAnalyseOriginalJson originalJson = deserialize.stream().filter(p -> p.getAlias().equals("标干流量")).findFirst().orElse(null);
                        if (null != originalJson) {
                            bgl = originalJson.getDefaultValue();
                        }
                    }
                    // 计算排放速率
                    String pfSpeed = CalculatedUtils.getPfSpeed(dtoAnalyseData.getTestValue(), bgl, dtoAnalyseData.getDimension(), null, dtoAnalyseData.getExamLimitValue(), "/");
                    evaluationRecord.setEmissionRateValue(pfSpeed);
                    updateEvaluationRecords.add(evaluationRecord);
                }
            }
            if (StringUtil.isNotEmpty(updateEvaluationRecords)) {
                repository.save(updateEvaluationRecords);
            }
        }
    }

    /**
     * 构造DtoEvaluationRecord对象放入list中
     *
     * @param testId     测试项目id
     * @param recordMap  测试项目id和评价数据对象的关联关系
     * @param objectId   DtoEvaluationRecord对象id
     * @param objectType 类型
     * @param folderPlan 点位计划
     * @param list       DtoEvaluationRecord对象列表
     */
    private void createEvaluationRecord(String testId, Map<String, DtoEvaluationRecord> recordMap, String objectId,
                                        int objectType, int folderPlan, List<DtoEvaluationRecord> list) {
        if (recordMap.containsKey(testId)) {
            DtoEvaluationRecord temp = recordMap.get(testId);
            DtoEvaluationRecord record = new DtoEvaluationRecord();
            record.setId("");
            record.setObjectId(objectId);
            record.setObjectType(objectType);
            record.setFolderPlan(folderPlan);
            record.setTestId(testId);
            record.setEvaluationId(UUIDHelper.GUID_EMPTY);
            record.setEvaluationLevelId(UUIDHelper.GUID_EMPTY);
            if (StringUtils.isNotNullAndEmpty(temp.getEvaluationId())) {
                record.setEvaluationId(temp.getEvaluationId());
            }
            if (StringUtils.isNotNullAndEmpty(temp.getEvaluationLevelId())) {
                record.setEvaluationLevelId(temp.getEvaluationLevelId());
            }
            record.setLowerLimitValue(temp.getLowerLimitValue());
            record.setUpperLimitValue(temp.getUpperLimitValue());
            record.setLowerLimitSymble(temp.getLowerLimitSymble());
            record.setUpperLimitSymble(temp.getUpperLimitSymble());
            record.setDimensionId(temp.getDimensionId());
            record.setEmissionRate(temp.getEmissionRate());
            list.add(record);
        }
    }

    /**
     * 收集要新增，修改，删除的DtoEvaluationRecord对象放入对应的list中
     *
     * @param forAdd 要新增的评价数据对象列表
     * @param forUpt 要修改的评价数据对象列表
     * @param forDel 要删除的评价数据对象列表
     * @param value  当前遍历的评价数据对象列表
     */
    private void colAddUptDelEvaRecList(List<DtoEvaluationRecord> forAdd, List<DtoEvaluationRecord> forUpt, List<DtoEvaluationRecord> forDel,
                                        List<DtoEvaluationRecord> value) {
        //分组遍历，同一个分组下只可能为1条数据或2条数据
        if (value.size() == 1) {
            DtoEvaluationRecord record = value.get(0);
            if (record.getId().equals("")) {
                //表明是新的评价记录，故需要添加
                record.setId(UUIDHelper.NewID());
                forAdd.add(record);
            } else {
                //表明是原来的评价记录，此情形不存在新的，说明需移除
                forDel.add(record);
            }
        } else {
            //上面经过排序后第一个为新的，第二个为老的
            DtoEvaluationRecord newRecord = value.get(0);
            DtoEvaluationRecord oldRecord = value.get(1);
            if (!this.recordEqual(newRecord, oldRecord)) {
                oldRecord.setEvaluationId(newRecord.getEvaluationId());
                oldRecord.setEvaluationLevelId(newRecord.getEvaluationLevelId());
                oldRecord.setLowerLimitValue(newRecord.getLowerLimitValue());
                oldRecord.setUpperLimitValue(newRecord.getUpperLimitValue());
                oldRecord.setLowerLimitSymble(newRecord.getLowerLimitSymble());
                oldRecord.setUpperLimitSymble(newRecord.getUpperLimitSymble());
                oldRecord.setLowerLimitSymble(newRecord.getLowerLimitSymble());
                oldRecord.setUpperLimitSymble(newRecord.getUpperLimitSymble());
                oldRecord.setDimensionId(newRecord.getDimensionId());
                oldRecord.setEmissionRate(newRecord.getEmissionRate());
                forUpt.add(oldRecord);
            }
        }
    }


    /**
     * 返回评价详情及结论
     *
     * @param dto 关联ids 类型 和点位计划
     */
    @Override
    public List<DtoEvaluationResult> getResult(DtoEvaluationRecord dto) {
        if (StringUtil.isNull(dto.getObjectIds()) || dto.getObjectIds().size() == 0) {
            throw new BaseException("请选择关联对象");
        }
        if (dto.getObjectType().equals(EnumEvaluationType.点位.getValue()) &&
                dto.getFolderPlan().equals(EnumEvaluationPlan.实际.getValue())) {

            //读取已配置的点位评价记录
            List<DtoEvaluationRecord> datas = repository.findByObjectIdInAndObjectTypeAndFolderPlan(dto.getObjectIds(), dto.getObjectType(), dto.getFolderPlan());

            //读取点位数据
            List<DtoSampleFolder> folders = sampleFolderService.findAll(dto.getObjectIds());
            List<String> sampleTypeIds = folders.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
            //将list转为map
            Map<String, String> sampleTypeMap = sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));

            //将list转为map
            Map<String, DtoSampleFolder> folderMap = folders.stream().collect(Collectors.toMap(DtoSampleFolder::getId, folder -> folder));

            //读取对应点位list的分析数据列表
            List<DtoAnalyseData> anaDatas = analyseDataService.findBySampleFolderIds(dto.getObjectIds());

            List<DtoEvaluationResult> resultList = new ArrayList<>();
            //对分析数据按照点位分组
            anaDatas.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleFolderId, Collectors.toList())).forEach((sampleFolderId, folderDatas) -> {
                if (folderMap.containsKey(sampleFolderId)) {
                    DtoSampleFolder folder = folderMap.get(sampleFolderId);

                    DtoEvaluationResult result = new DtoEvaluationResult();
                    result.setId(sampleFolderId);
                    result.setWatchSpot(folder.getWatchSpot());

                    //读取对应点位下的评价记录list
                    List<DtoEvaluationRecord> evaRecords = datas.stream().filter(p -> p.getObjectId().equals(sampleFolderId)).collect(Collectors.toList());
                    //将list转为map
                    Map<String, DtoEvaluationRecord> evaRecordMap = evaRecords.stream().collect(Collectors.toMap(DtoEvaluationRecord::getTestId, record -> record));
                    List<DtoEvaluationRecord> sampleFolderRecords = new ArrayList<>();
                    //对点位分析数据按照测试项目id分组
                    folderDatas.stream().collect(Collectors.groupingBy(DtoAnalyseData::getTestId, Collectors.toList())).forEach((testId, testDatas) -> {
                        //拼装对应的评价结论详情
                        DtoEvaluationRecord record = new DtoEvaluationRecord();
                        record.setTestId(testId);
                        if (evaRecordMap.containsKey(testId)) {//对点位评价记录中存在则取点位评价记录的
                            record = evaRecordMap.get(testId);
                        } else {
                            record.setEvaluationId(UUIDHelper.GUID_EMPTY);
                            record.setEvaluationLevelId(UUIDHelper.GUID_EMPTY);
                        }

                        DtoAnalyseData ana = testDatas.get(0);
                        record.setRedAnalyzeItemName(ana.getRedAnalyzeItemName());
                        record.setRedAnalyzeMethodName(ana.getRedAnalyzeMethodName());
                        record.setRedCountryStandard(ana.getRedCountryStandard());
                        record.setSampleTypeId(folder.getSampleTypeId());
                        record.setSampleTypeName(sampleTypeMap.getOrDefault(folder.getSampleTypeId(), ""));
                        if (StringUtils.isNotNullAndEmpty(record.getEvaluationId()) && !UUIDHelper.GUID_EMPTY.equals(record.getEvaluationId())) {
                            record.setEvaluationName(this.getEvaluationName(record.getEvaluationId()));
                            if (StringUtils.isNotNullAndEmpty(record.getEvaluationLevelId())) {
                                record.setEvaluationLevelName(this.getEvaluationLevelName(record.getEvaluationId(), record.getEvaluationLevelId()));
                            }
                        }

                        //写入超标信息
                        this.setExceedInfo(record, testDatas);
                        sampleFolderRecords.add(record);
                    });

                    result.setEvaluationRecord(sampleFolderRecords);
                    this.setResultInfo(result);
                    resultList.add(result);
                }
            });
            return resultList;
        } else {
            //TODO 点位实际评价时才有数据
            throw new BaseException("传参错误");
        }
    }

    @Override
    public Map<String, Object> getAnalyzeResult(BaseCriteria criteria) {
        //存放接口响应数据集
        Map<String, Object> resMap = new HashMap<>();
        EvaluationAnalyzeResultCriteria evaluationAnalyzeResultCriteria = (EvaluationAnalyzeResultCriteria) criteria;
        //获取该项目下所有样品
        List<DtoSample> sampleInProject = sampleRepository.findByProjectId(evaluationAnalyzeResultCriteria.getProjectId());
        //过滤出样品编号为空的样品
        List<DtoSample> emptyCodeSampleList = sampleInProject.stream().filter(p -> "".equals(p.getCode())).collect(Collectors.toList());
        //设置默认样品编号
        String defaultCode = "*";
        for (DtoSample sample : emptyCodeSampleList) {
            sample.setCode(defaultCode);
            defaultCode += "*";
        }
        //判断是否需要过滤掉未检毕的样品
        boolean detectionComplete = evaluationAnalyzeResultCriteria.getDetectionComplete();
//        if (evaluationAnalyzeResultCriteria.getDetectionComplete()) {
//            sampleInProject = sampleInProject.stream().filter(p -> EnumPRO.EnumSampleStatus.样品检毕.name().equals(p.getStatus())).collect(Collectors.toList());
//        }
        //放置评价分析结果数据
        List<Map<String, String>> dataRowMapList = new ArrayList<>();
        //放置测试项目数据
        List<DtoDetailDataColumn> detailDataColumnList = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleInProject)) {
            //点位id和点位对象的映射
            Map<String, DtoFixedpoint> pntId2PntMap = new HashMap<>();
            //点位排序下所有点位id
            List<String> fixedPointIdList = new ArrayList<>();
            //获取点位排序信息
            fetchFixPointInfo(evaluationAnalyzeResultCriteria.getPointSortId(), fixedPointIdList, pntId2PntMap);
            //所有样品id
            List<String> sampleIdList = new ArrayList<>();
            //所有样品按照点位id分组
            Map<String, List<DtoSample>> fldId2SmpListMap = new HashMap<>();
            //样品类型id和样品类型映射
            Map<String, DtoSampleType> typeId2SmpTypeMap = new HashMap<>();
            //放置排好序的所有点位id，包括 sampleFolder和 fixedPoint
            List<String> sortedFldIdList = new ArrayList<>();
            //点位id和点位对象的映射
            Map<String, DtoSampleFolder> smpFldId2FldMap = new HashMap<>();
            //送样单id和送样单对象映射
            Map<String, DtoReceiveSampleRecord> recId2RecMap = new HashMap<>();
            //获取该项目下所有测试项目
            List<DtoTest> allTestList = new ArrayList<>();
            //样品id和分析数据映射
            Map<String, List<DtoAnalyseData>> smpId2AnaDataListMap = new HashMap<>();
            //量纲id和量纲对象映射
            Map<String, DtoDimension> dimId2DimMap = new HashMap<>();
            //获取样品相关信息
            fetchSampleInfo(sampleInProject, sampleIdList, fldId2SmpListMap, fixedPointIdList, smpFldId2FldMap, sortedFldIdList, typeId2SmpTypeMap, recId2RecMap,
                    smpId2AnaDataListMap, allTestList, dimId2DimMap, detectionComplete);
            setAnalyzeTestInfo(evaluationAnalyzeResultCriteria.getItemSortId(), allTestList, detailDataColumnList, dimId2DimMap);
            //测试项目id和测试项对象映射
            Map<String, DtoTest> id2TestMap = allTestList.stream().collect(Collectors.toMap(DtoTest::getId, dto -> dto));
            //排序好的测试项目id
            List<String> sortedTstIdList = new ArrayList<>();
            detailDataColumnList.forEach(p -> sortedTstIdList.add(p.getTestId()));
            //评价类型列表（原始值，最大值，最小值，点位均值）
            List<String> evaluationTypeList = StringUtil.isNotEmpty(evaluationAnalyzeResultCriteria.getEvaluationType())
                    ? evaluationAnalyzeResultCriteria.getEvaluationType() : new ArrayList<>();
            Map<String, List<DtoEvaluationRecord>> fldId2EvaRcdListMap = new HashMap<>();
            //判断是否需要超标标红
            boolean overRed = evaluationAnalyzeResultCriteria.getOverRed();
            if (overRed) {
                //需要标红，获取评价标准上下限
                fldId2EvaRcdListMap = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(sortedFldIdList, 2, 1)
                        .stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getObjectId));
            }
            //判断是否要统计原始值
            boolean originalValueFlag = evaluationTypeList.contains("initialValue");
            //遍历每个点位，每个样品,获取样品分析结果信息
            setAnalyzeSampleInfo(sortedFldIdList, fldId2EvaRcdListMap, fldId2SmpListMap, pntId2PntMap, smpFldId2FldMap, originalValueFlag, typeId2SmpTypeMap,
                    recId2RecMap, smpId2AnaDataListMap, sortedTstIdList, evaluationTypeList, dataRowMapList, overRed, id2TestMap);
        }
        resMap.put("testData", detailDataColumnList);
        //将默认添加的样品编号重新置空
        for (Map<String, String> dataRowMap : dataRowMapList) {
            if (dataRowMap.containsKey("sampleCode") && dataRowMap.get("sampleCode").contains("*")) {
                dataRowMap.put("sampleCode", "");
            }
        }
        resMap.put("analyseData", dataRowMapList);
        return resMap;
    }

    /**
     * 获取点位排序信息
     *
     * @param sortedTstIdList      排序好的测试项目id
     * @param originalValueFlag    是否要统计原始值标记
     * @param fldId2SmpListMap     点位id样品列表映射
     * @param pntId2PntMap         点位id和点位对象映射
     * @param smpFldId2FldMap      采样日期
     * @param sortedFldIdList      排序好的点位id列表
     * @param typeId2SmpTypeMap    类型id和样品类型对象映射
     * @param recId2RecMap         送样单id和送样单映射
     * @param smpId2AnaDataListMap 样品id分析数据映射
     * @param evaluationTypeList   评价类型列表（原始值，最大值，最小值，点位均值）
     * @param dataRowMapList       评价分析结果数据
     * @param overRed              是否需要超标标红
     * @param id2TestMap           测试项目映射
     */
    private void setAnalyzeSampleInfo(List<String> sortedFldIdList, Map<String, List<DtoEvaluationRecord>> fldId2EvaRcdListMap,
                                      Map<String, List<DtoSample>> fldId2SmpListMap, Map<String, DtoFixedpoint> pntId2PntMap,
                                      Map<String, DtoSampleFolder> smpFldId2FldMap, boolean originalValueFlag, Map<String, DtoSampleType> typeId2SmpTypeMap,
                                      Map<String, DtoReceiveSampleRecord> recId2RecMap, Map<String, List<DtoAnalyseData>> smpId2AnaDataListMap, List<String> sortedTstIdList,
                                      List<String> evaluationTypeList, List<Map<String, String>> dataRowMapList, boolean overRed, Map<String, DtoTest> id2TestMap) {
        //先按照点位分组
        for (String fldId : sortedFldIdList) {
            Map<String, DtoEvaluationRecord> tstId2EvaRcdMap = fldId2EvaRcdListMap.getOrDefault(fldId, new ArrayList<>())
                    .stream().collect(Collectors.toMap(DtoEvaluationRecord::getTestId, dto -> dto));
            //放置当前点位下每个测试项目的出证结果数据
            Map<String, List<String>> tstId2TstValListMap = new HashMap<>();
            Map<String, List<String>> tstId2ExamLimitListMap = new HashMap<>();
            //获取点位下的所有样品按照样品编号排序
            List<DtoSample> fldSmpList = fldId2SmpListMap.get(fldId);
            //按样品编号分组
            Map<String, List<DtoSample>> smpCode2SmpListMap = fldSmpList.stream().collect(Collectors.groupingBy(DtoSample::getCode));
            List<String> fldCodeList = new ArrayList<>(smpCode2SmpListMap.keySet());
            //同一点位下样品按照样品编号排列
            Collections.sort(fldCodeList);
            //点位名称
            String watchSpot = pntId2PntMap.containsKey(fldId)
                    ? pntId2PntMap.get(fldId).getPointName() : smpFldId2FldMap.get(fldId).getWatchSpot();
            String sampleTypeName = "";
            //遍历每个样品编号获取数据
            for (String smpCode : fldCodeList) {
                //同一个样品编号下的样品列表
                List<DtoSample> codeSmpList = smpCode2SmpListMap.get(smpCode);
                if (originalValueFlag) {
                    //需要获取原始值的情况
                    Map<String, String> dataRowMap = new HashMap<>();
                    String sampleTypeId = codeSmpList.get(0).getSampleTypeId();
                    if (StringUtil.isEmpty(sampleTypeName) && typeId2SmpTypeMap.containsKey(sampleTypeId)) {
                        sampleTypeName = typeId2SmpTypeMap.get(sampleTypeId).getTypeName();
                    }
                    String receiveId = StringUtil.isNotEmpty(codeSmpList.get(0).getReceiveId()) ? codeSmpList.get(0).getReceiveId() : "";
                    String receiveTime = recId2RecMap.containsKey(receiveId) ? DateUtil.dateToString(recId2RecMap.get(receiveId).getReceiveTime(), DateUtil.YEAR) : "";
                    String samplingTime = DateUtil.dateToString(codeSmpList.get(0).getSamplingTimeBegin(), DateUtil.YEAR);
                    //设置dataRowMap通用信息（样品编号，点位，检测类型，采样接样日期，评价类型）
                    setRowMapCommonData(dataRowMap, smpCode, watchSpot, sampleTypeName, samplingTime, receiveTime, "initialValue");
                    //根据评价类型获取并设置评价结果行的测试项目数据
                    setRowDataByEvaluationType("initialValue", dataRowMap, codeSmpList, smpId2AnaDataListMap, sortedTstIdList, tstId2TstValListMap, tstId2EvaRcdMap, overRed, id2TestMap,tstId2ExamLimitListMap);
                    dataRowMapList.add(dataRowMap);
                } else if (StringUtil.isNotEmpty(evaluationTypeList)) {
                    //不需要获取原始值，但最大值，最小值，点位均值 这三个值中至少有一个需要获取,则需要收集当前点位下每个测试项目对应的出证结果
                    Map<String, DtoAnalyseData> tstId2AnaDataMap = initTstId2AnaDataMap(codeSmpList, smpId2AnaDataListMap);
                    for (String testId : sortedTstIdList) {
                        if (tstId2AnaDataMap.containsKey(testId)) {
                            //收集当前测试项目下对应的出证结果
                            colTestValue(tstId2AnaDataMap.get(testId).getTestValue(), testId, tstId2AnaDataMap.get(testId), tstId2TstValListMap,tstId2ExamLimitListMap);
                        }
                    }
                }
            }
            //当前点位下所有样品数据都收集完成之后，判断是否需要获取最大值，最小值和点位均值
            evaluationTypeList.remove("initialValue");
            if (StringUtil.isNotEmpty(evaluationTypeList)) {
                for (String evaluationType : evaluationTypeList) {
                    Map<String, String> dataRowMap = new HashMap<>();
                    //设置dataRowMap通用信息（样品编号，点位，检测类型，采样接样日期，评价类型）
                    setRowMapCommonData(dataRowMap, "", watchSpot, sampleTypeName, "", "", evaluationType);
                    //根据评价类型获取并设置dataRowMap的测试项目数据
                    setRowDataByEvaluationType(evaluationType, dataRowMap, null, null, sortedTstIdList, tstId2TstValListMap, tstId2EvaRcdMap, overRed, id2TestMap,tstId2ExamLimitListMap);
                    dataRowMapList.add(dataRowMap);
                }
            }
        }
    }

    /**
     * 获取点位排序信息
     *
     * @param pointSortId      点位排序id
     * @param fixedPointIdList 排序的点位id列表
     * @param pntId2PntMap     点位id和点位对象映射
     */
    private void fetchFixPointInfo(String pointSortId, List<String> fixedPointIdList, Map<String, DtoFixedpoint> pntId2PntMap) {
        List<DtoFixedPointSortDetil> fixedPointSortDetails = StringUtils.isNotNullAndEmpty(pointSortId)
                ? fixedPointSortDetilRepository.findBySortId(pointSortId) : new ArrayList<>();
        if (StringUtil.isNotEmpty(fixedPointSortDetails)) {
            //点位排序对象列表按照排序值排序
            fixedPointSortDetails.sort(Comparator.comparing(DtoFixedPointSortDetil::getOrderNum).reversed());
            fixedPointIdList.addAll(fixedPointSortDetails.stream().map(DtoFixedPointSortDetil::getFixedPointId).distinct().collect(Collectors.toList()));
            //找到点位排序下所有的点位
            List<DtoFixedpoint> fixedPointList = fixedpointRepository.findByIdIn(fixedPointIdList);
            //点位id和点位对象的映射
            pntId2PntMap.putAll(fixedPointList.stream().collect(Collectors.toMap(DtoFixedpoint::getId, dto -> dto)));
        }
    }

    /**
     * 设置评价结果测试项目数据信息
     *
     * @param itemSortId           分析项目排序id
     * @param allTestList          所有测试项目列表
     * @param detailDataColumnList 测试项目数据对象列表
     * @param dimId2DimMap         量纲id和量纲对象映射
     */
    private void setAnalyzeTestInfo(String itemSortId, List<DtoTest> allTestList, List<DtoDetailDataColumn> detailDataColumnList, Map<String, DtoDimension> dimId2DimMap) {
        List<DtoAnalyzeItemSortDetail> analyzeItemSortDetails = StringUtil.isNotEmpty(itemSortId)
                ? analyzeItemSortDetialService.getSortDetailList(itemSortId) : new ArrayList<>();
        //已排序的分析项目id和orderNum的映射
        Map<String, Integer> sortedAnaId2OrderNumMap = StringUtil.isNotEmpty(analyzeItemSortDetails)
                ? analyzeItemSortDetails.stream().collect(Collectors.toMap(DtoAnalyzeItemSortDetail::getAnalyzeItemId, DtoAnalyzeItemSortDetail::getOrderNum)) : new HashMap<>();
        //遍历所有测试项目构建DtoDetailDataColumn实体列表
        for (DtoTest test : allTestList) {
            DtoDetailDataColumn detailDataColumn = new DtoDetailDataColumn();
            detailDataColumn.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
            String dimId = test.getDimensionId();
            detailDataColumn.setDimensionName(dimId2DimMap.containsKey(dimId) ? dimId2DimMap.get(dimId).getDimensionName() : "");
            detailDataColumn.setTestId(test.getId());
            detailDataColumn.setOrderNum(sortedAnaId2OrderNumMap.getOrDefault(test.getAnalyzeItemId(), -1));
            detailDataColumnList.add(detailDataColumn);
        }
        //按照orderNum倒序排列
        detailDataColumnList.sort(Comparator.comparing(DtoDetailDataColumn::getOrderNum).reversed());
    }

    /**
     * 设置评价分析结果数据行通用信息（样品编号，点位，检测类型，采样接样日期，评价类型）
     *
     * @param sampleInProject      所有样品
     * @param sampleIdList         样品编号列表
     * @param fldId2SmpListMap     点位id样品列表映射
     * @param fixedPointIdList     点位id列表
     * @param smpFldId2FldMap      采样日期
     * @param sortedFldIdList      排序好的点位id列表
     * @param typeId2SmpTypeMap    类型id和样品类型对象映射
     * @param recId2RecMap         送样单id和送样单映射
     * @param smpId2AnaDataListMap 样品id分析数据映射
     * @param allTestList          所有测试项目列表
     * @param dimId2DimMap         量纲id量纲对象的映射
     * @param detectionComplete    是否过滤已检毕数据
     */
    private void fetchSampleInfo(List<DtoSample> sampleInProject, List<String> sampleIdList, Map<String, List<DtoSample>> fldId2SmpListMap, List<String> fixedPointIdList,
                                 Map<String, DtoSampleFolder> smpFldId2FldMap, List<String> sortedFldIdList, Map<String, DtoSampleType> typeId2SmpTypeMap,
                                 Map<String, DtoReceiveSampleRecord> recId2RecMap, Map<String, List<DtoAnalyseData>> smpId2AnaDataListMap,
                                 List<DtoTest> allTestList, Map<String, DtoDimension> dimId2DimMap, boolean detectionComplete) {
        List<String> sampleTypeIdList = new ArrayList<>();
        List<String> receiveIdList = new ArrayList<>();
        List<String> sampleFolderIdList = new ArrayList<>();
        for (DtoSample sample : sampleInProject) {
            sampleIdList.add(sample.getId());
            if (!sampleTypeIdList.contains(sample.getSampleTypeId())) {
                sampleTypeIdList.add(sample.getSampleTypeId());
            }
            if (!receiveIdList.contains(sample.getReceiveId())) {
                receiveIdList.add(sample.getReceiveId());
            }
            String loopFldId = sample.getSampleFolderId();
            if (!sampleFolderIdList.contains(loopFldId)) {
                sampleFolderIdList.add(loopFldId);
            }
            if (!fldId2SmpListMap.containsKey(loopFldId)) {
                fldId2SmpListMap.put(loopFldId, new ArrayList<>());
            }
            fldId2SmpListMap.get(loopFldId).add(sample);
        }
        //获取所有样品类型
        List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIdList);
        typeId2SmpTypeMap.putAll(sampleTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, dto -> dto)));
        //获取所有送样单
        List<DtoReceiveSampleRecord> receiveSampleRecordList = StringUtil.isNotEmpty(receiveIdList) ? receiveSampleRecordRepository.findAll(receiveIdList) : new ArrayList<>();
        recId2RecMap.putAll(receiveSampleRecordList.stream().collect(Collectors.toMap(DtoReceiveSampleRecord::getId, dto -> dto)));
        //所有点位对象
        List<DtoSampleFolder> sampleFolderList = StringUtil.isNotEmpty(sampleFolderIdList) ? sampleFolderRepository.findAll(sampleFolderIdList) : new ArrayList<>();
        Map<String, DtoSampleFolder> sampleFolderMap = sampleFolderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto));
        //过滤出fixedPointId 不为空的点位对象
        List<DtoSampleFolder> smpFolderWithFixedPoint = sampleFolderList.stream().filter(p -> fixedPointIdList.contains(p.getFixedPointId())).collect(Collectors.toList());
        //过滤出fixedPointId 不为空的点位id
        List<String> smpFolderIdsWithFixedPoint = smpFolderWithFixedPoint.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        //所有点位id中移除需要排序的id
        sampleFolderIdList.removeAll(smpFolderIdsWithFixedPoint);
        smpFldId2FldMap.putAll(sampleFolderList.stream().collect(Collectors.toMap(DtoSampleFolder::getId, dto -> dto)));
        //smpFixPointIdList按照排序值排序
        for (String fixedPointId : fixedPointIdList) {
            DtoSampleFolder loopFld = smpFolderWithFixedPoint.stream().filter(p -> fixedPointId.equals(p.getFixedPointId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(loopFld)) {
                sortedFldIdList.add(loopFld.getId());
            }
        }
        List<DtoSampleFolderTemp> sampleFolderTempList = new ArrayList<>();
        int tmpOrderNum = sortedFldIdList.size();
        for (String fldId : sortedFldIdList) {
            DtoSampleFolder loopFolder = sampleFolderMap.get(fldId);
            DtoSampleFolderTemp temp = createSampleFolderTemp(loopFolder, typeId2SmpTypeMap);
            temp.setOrderNum(tmpOrderNum);
            sampleFolderTempList.add(temp);
            tmpOrderNum--;
        }
        for (String fldId : sampleFolderIdList) {
            DtoSampleFolder loopFolder = sampleFolderMap.get(fldId);
            DtoSampleFolderTemp temp = createSampleFolderTemp(loopFolder, typeId2SmpTypeMap);
            temp.setOrderNum(-1);
            sampleFolderTempList.add(temp);
        }
        //获取中文排序工具
        Collator collator = Collator.getInstance();
        //先按照检测大类排序，再按照检测小类名称排序，再按照排序值倒序，再按照点位名称
        sampleFolderTempList.sort(Comparator.comparing(DtoSampleFolderTemp::getBigSampleTypeId)
                .thenComparing(DtoSampleFolderTemp::getSampleTypeName, collator)
                .thenComparing(DtoSampleFolderTemp::getOrderNum, Comparator.reverseOrder())
                .thenComparing(DtoSampleFolderTemp::getWatchSpot, collator));
        sortedFldIdList.clear();
        sampleFolderTempList.forEach(p -> sortedFldIdList.add(p.getSampleFolderId()));
        //该项目下所有分析数据
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdIn(sampleIdList);
        //过滤已检毕数据
        if (detectionComplete) {
            analyseDataList = analyseDataList.stream().filter(AnalyseData::getIsDataEnabled).collect(Collectors.toList());
        }
        smpId2AnaDataListMap.putAll(analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId)));
        List<String> tstIdList = StringUtil.isNotEmpty(analyseDataList) ?
                analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList()) : new ArrayList<>();
        //获取该项目下所有测试项目
        if (StringUtil.isNotEmpty(tstIdList)) {
            allTestList.addAll(testRepository.findAll(tstIdList));
            List<String> dimIdList = allTestList.stream().map(DtoTest::getDimensionId).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(dimIdList)) {
                List<DtoDimension> dimensionList = dimensionRepository.findAll(dimIdList);
                dimId2DimMap.putAll(dimensionList.stream().collect(Collectors.toMap(DtoDimension::getId, dto -> dto)));
            }
        }
    }

    /**
     * 创建DtoSampleFolderTemp对象
     *
     * @param loopFolder        点位对象
     * @param typeId2SmpTypeMap 样品类型映射
     * @return DtoSampleFolderTemp对象
     */
    private DtoSampleFolderTemp createSampleFolderTemp(DtoSampleFolder loopFolder, Map<String, DtoSampleType> typeId2SmpTypeMap) {
        String bigSampleTypeId = "";
        String sampleTypeName = "";
        if (typeId2SmpTypeMap.containsKey(loopFolder.getSampleTypeId())) {
            DtoSampleType sampleType = typeId2SmpTypeMap.get(loopFolder.getSampleTypeId());
            bigSampleTypeId = StringUtil.isNotEmpty(sampleType.getParentId()) ? sampleType.getParentId() : "";
            sampleTypeName = StringUtil.isNotEmpty(sampleType.getTypeName()) ? sampleType.getTypeName() : "";
        }
        DtoSampleFolderTemp temp = new DtoSampleFolderTemp();
        temp.setSampleFolderId(loopFolder.getId());
        temp.setBigSampleTypeId(bigSampleTypeId);
        temp.setSampleTypeName(sampleTypeName);
        temp.setWatchSpot(loopFolder.getWatchSpot());
        return temp;
    }

    /**
     * 设置评价分析结果数据行通用信息（样品编号，点位，检测类型，采样接样日期，评价类型）
     *
     * @param dataRowMap     评价结果行数据map
     * @param sampleCode     样品编号
     * @param watchSpot      点位名称
     * @param sampleTypeName 检测类型名称
     * @param samplingTime   采样日期
     * @param receiveTime    接样日期
     * @param evaluationType 评价类型 （原始值,最大值，最小值，点位均值）
     */
    private void setRowMapCommonData(Map<String, String> dataRowMap, String sampleCode, String watchSpot, String sampleTypeName,
                                     String samplingTime, String receiveTime, String evaluationType) {
        dataRowMap.put("sampleCode", sampleCode);
        dataRowMap.put("folderName", watchSpot);
        dataRowMap.put("sampleTypeName", sampleTypeName);
        dataRowMap.put("samplingTime", samplingTime);
        dataRowMap.put("receiveTime", receiveTime);
        dataRowMap.put("evaluationTypeName", evaluationType);
    }

    /**
     * 根据评价类型获取并设置评价结果行的测试项目数据
     *
     * @param evaluationType       评价类型 （原始值,最大值，最小值，均值）
     * @param dataRowMap           评价结果行数据map
     * @param codeSmpList          同一样品编号下的样品列表
     * @param smpId2AnaDataListMap 样品编号和分析数据对象的映射
     * @param sortedTstIdList      排序好的测试项目id列表
     * @param tstId2TstValListMap  点位下每个测试项目的出证结果数据
     * @param tstId2EvaRcdMap      测试项目id和评价标准对象映射
     * @param overRed              是否超标标红标记位
     * @param id2TestMap           测试项目映射
     * @param tstId2TstValListMap  点位下每个测试项目的检出限数据
     */
    private void setRowDataByEvaluationType(String evaluationType, Map<String, String> dataRowMap, List<DtoSample> codeSmpList,
                                            Map<String, List<DtoAnalyseData>> smpId2AnaDataListMap, List<String> sortedTstIdList,
                                            Map<String, List<String>> tstId2TstValListMap, Map<String, DtoEvaluationRecord> tstId2EvaRcdMap,
                                            boolean overRed, Map<String, DtoTest> id2TestMap, Map<String, List<String>> tstId2ExamLimitListMap) {
        if ("initialValue".equals(evaluationType)) {
            setOriginalValue(dataRowMap, codeSmpList, smpId2AnaDataListMap, sortedTstIdList, tstId2TstValListMap, tstId2EvaRcdMap, overRed,tstId2ExamLimitListMap);
        } else {
            setMaxMinAvgValue(dataRowMap, sortedTstIdList, tstId2TstValListMap, evaluationType, tstId2EvaRcdMap, overRed, id2TestMap,tstId2ExamLimitListMap);
        }
    }

    /**
     * 设置评价结果行的测试项目数据(原始值)
     *
     * @param dataRowMap           评价结果行数据map
     * @param codeSmpList          同一样品编号下的样品列表
     * @param smpId2AnaDataListMap 样品编号和分析数据对象的映射
     * @param sortedTstIdList      排序好的测试项目id列表
     * @param tstId2TstValListMap  点位下每个测试项目的出证结果数据
     * @param tstId2EvaRcdMap      测试项目id和评价标准对象映射
     * @param overRed              是否超标标红
     * @param tstId2TstValListMap  点位下每个测试项目的检出限数据
     */
    private void setOriginalValue(Map<String, String> dataRowMap, List<DtoSample> codeSmpList,
                                  Map<String, List<DtoAnalyseData>> smpId2AnaDataListMap, List<String> sortedTstIdList,
                                  Map<String, List<String>> tstId2TstValListMap, Map<String, DtoEvaluationRecord> tstId2EvaRcdMap, boolean overRed,
                                  Map<String, List<String>> tstId2ExamLimitListMap) {

        //同一样品编号下的所有样品不会出现重复的测试项目id（不考虑原样加原样的情况）
        Map<String, DtoAnalyseData> tstId2AnaDataMap = initTstId2AnaDataMap(codeSmpList, smpId2AnaDataListMap);
        for (String testId : sortedTstIdList) {
            //先对每个测试项目的原始值赋默认值
            dataRowMap.put(testId, "");
            if (tstId2AnaDataMap.containsKey(testId)) {
                DtoAnalyseData analyseData = tstId2AnaDataMap.get(testId);
                String testValue = analyseData.getTestValue();
                //收集当前测试项目下对应的出证结果
                colTestValue(testValue, testId, analyseData, tstId2TstValListMap,tstId2ExamLimitListMap);
                //判断该测试项目是否配置了评价标准
                if (overRed && tstId2EvaRcdMap.containsKey(testId)) {
                    //判断是否超标，超标则标红
                    testValue = checkAndSetOverRed(tstId2EvaRcdMap.get(testId), testValue);
                }
                dataRowMap.put(testId, testValue);
            }
        }
    }

    /**
     * 设置评价结果行的测试项目数据(最大值,最小值，均值)
     *
     * @param dataRowMap          评价结果行数据map
     * @param sortedTstIdList     排序好的测试项目id列表
     * @param tstId2TstValListMap 点位下每个测试项目的出证结果数据
     * @param tstId2ExamLimitListMap 点位下每个测试项目的检出限数据
     * @param evaluationType      评价类型
     * @param tstId2EvaRcdMap     测试项目id和评价标准对象映射
     * @param overRed             是否超标标红
     * @param id2TestMap          测试项目映射
     *
     */
    private void setMaxMinAvgValue(Map<String, String> dataRowMap, List<String> sortedTstIdList, Map<String, List<String>> tstId2TstValListMap,
                                   String evaluationType, Map<String, DtoEvaluationRecord> tstId2EvaRcdMap, boolean overRed, Map<String, DtoTest> id2TestMap,
                                   Map<String, List<String>> tstId2ExamLimitListMap) {
        for (String testId : sortedTstIdList) {
            //先赋默认值
            dataRowMap.put(testId, "");
            List<String> tstValList = tstId2TstValListMap.get(testId);
            List<String> examLimitList = tstId2ExamLimitListMap.get(testId);

            if (StringUtil.isNotEmpty(tstValList)) {
                String actVal;
                //小于检出限的出证结果列表(ND, 0.5L , <0.5 ...)
                List<String> ndValList = new ArrayList<>();
                //正常结果的列表
                List<BigDecimal> normalValDecList = new ArrayList<>();
                //检出限一半结果列表
                List<BigDecimal> partExamValDecList = new ArrayList<>();
                for (String tstVal : tstValList) {
                    if (tstVal.contains("_")) {
                        String[] valArr = tstVal.split("_");
                        ndValList.add(valArr[0]);
                        //收集检出限一半结果
                        partExamValDecList.add(new BigDecimal(valArr[1]));
                    } else {
                        normalValDecList.add(new BigDecimal(tstVal));
                    }
                }
                if ("maxValue".equals(evaluationType)) {
                    if (StringUtil.isEmpty(ndValList)) {
                        //所有出证结果都大于检出限则直接取最大值
                        actVal = Collections.max(normalValDecList).toString();
                    } else {
                        if (ndValList.size() == tstValList.size()) {
                            //所有出证结果都是小于检出限的情况，则最大值为小于检出限结果(暂时默认取第一个)
                            actVal = ndValList.get(0);
                        } else {
                            //有小于检出限的结果，也有大于检出限的结果，则在大于检出限结果列表中获取最大值
                            actVal = Collections.max(normalValDecList).toString();
                        }
                    }
                } else if ("minValue".equals(evaluationType)) {
                    if (StringUtil.isEmpty(ndValList)) {
                        //所有出证结果都大于检出限则直接取最小值
                        actVal = Collections.min(normalValDecList).toString();
                    } else {
                        //否则最小值直接取小于检出限结果(暂时默认取第一个)
                        actVal = ndValList.get(0);
                    }
                } else {
                    //求均值
                    BigDecimal sumDec = BigDecimal.ZERO;
                    //将大于检出限的结果列表和检出限一半的结果列表合并,再求均值
                    normalValDecList.addAll(partExamValDecList);
                    for (BigDecimal tstValDec : normalValDecList) {
                        sumDec = sumDec.add(tstValDec);
                    }
                    int sig = -1;
                    int dec = -1;
                    DtoTest test = id2TestMap.get(testId);
                    if (StringUtil.isNotNull(test)) {
                        sig = test.getMostSignificance();
                        dec = test.getMostDecimal();
                    }
                    actVal = sumDec.divide(new BigDecimal(String.valueOf(normalValDecList.size())),
                            ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN).stripTrailingZeros().toString();
                    actVal = proService.getDecimal(sig, dec, actVal);
                    //判断均值是否小于检出限，小于检出限则显示测试项目上配置的小于检出限结果,若小于检出限结果没有配置，则默认取 "ND"，若小于检出限结果配置为 “L”,则显示为：检出限 + L
                    String examLimitValue = "";
                    if(StringUtil.isNotEmpty(examLimitList)){
                        examLimitValue = examLimitList.get(0);
                    }
                    if(StringUtil.isNotEmpty(examLimitValue)&&new BigDecimal(actVal).compareTo(new BigDecimal(examLimitValue)) < 0){
                        if (StringUtil.isEmpty(test.getExamLimitValueLess())) {
                            actVal = "ND";
                        } else {
                            actVal = "检出限L".equals(test.getExamLimitValueLess()) ? examLimitValue + "L" : test.getExamLimitValueLess();
                        }
                    }
                }
                //判断该测试项目是否配置了评价标准
                if (overRed && MathUtil.isNumeral(actVal) && tstId2EvaRcdMap.containsKey(testId)) {
                    //判断是否超标，超标则标红
                    actVal = checkAndSetOverRed(tstId2EvaRcdMap.get(testId), actVal);
                }
                dataRowMap.put(testId, actVal);
            }
        }
    }

    /**
     * 判断是否超标，超标则标红
     *
     * @param evaluationRecord 评价标准对象
     * @param testValue        出证结果
     */
    private String checkAndSetOverRed(DtoEvaluationRecord evaluationRecord, String testValue) {
        DtoEvaluationValue evaluationValue = new DtoEvaluationValue();
        evaluationValue.setLowerLimitSymble(evaluationRecord.getLowerLimitSymble());
        evaluationValue.setLowerLimit(evaluationRecord.getLowerLimitValue());
        evaluationValue.setUpperLimitSymble(evaluationRecord.getUpperLimitSymble());
        evaluationValue.setUpperLimit(evaluationRecord.getUpperLimitValue());
        if (!dataEvaluateService.getIsPass(evaluationValue, testValue)) {
            testValue = "Red:" + testValue;
        }
        return testValue;
    }

    /**
     * 收集测试项目下对应的出证结果
     *
     * @param testValue           出证结果
     * @param testId              测试项目id
     * @param analyseData         分析数据对象
     * @param tstId2TstValListMap 测试项目id和出证结果列表的映射
     * @param tstId2ExamLimitListMap 测试项目id和检出限列表的映射
     */
    private void colTestValue(String testValue, String testId, DtoAnalyseData analyseData, Map<String, List<String>> tstId2TstValListMap,
                              Map<String, List<String>> tstId2ExamLimitListMap) {
        if (testValue.contains("ND") || testValue.contains("<") || testValue.contains("L") || MathUtil.isNumeral(testValue)) {
            if (!tstId2TstValListMap.containsKey(testId)) {
                tstId2TstValListMap.put(testId, new ArrayList<>());
            }
            if (testValue.contains("ND") || testValue.contains("<") || testValue.contains("L")) {
                //小于检出限取检出限一半计算
                BigDecimal partLmtVal = new BigDecimal(analyseData.getExamLimitValue()).divide(new BigDecimal("2"),
                        ProCodeHelper.COMMON_ROUNDING_MODE, BigDecimal.ROUND_HALF_EVEN);
                tstId2TstValListMap.get(testId).add(testValue + "_" + partLmtVal.toString());
            } else {
                tstId2TstValListMap.get(testId).add(testValue);
            }
        }
        if (!tstId2ExamLimitListMap.containsKey(testId)) {
            tstId2ExamLimitListMap.put(testId, new ArrayList<>());
        }
        if(StringUtil.isNotEmpty(analyseData.getExamLimitValue())){
            tstId2ExamLimitListMap.get(testId).add(analyseData.getExamLimitValue());
        }
    }

    /**
     * 获取同一样品编号的所有样品下测试项目id和分析数据对象的映射关系
     *
     * @param codeSmpList      同一样品编号下的所有样品对象
     * @param smpId2AnaDataMap 样品id和分析数据对象的映射
     * @return 测试项目id和分析数据对象的映射关系
     */
    private Map<String, DtoAnalyseData> initTstId2AnaDataMap(List<DtoSample> codeSmpList, Map<String, List<DtoAnalyseData>> smpId2AnaDataMap) {
        Map<String, DtoAnalyseData> tstId2AnaDataMap = new HashMap<>();
        for (DtoSample codeSmp : codeSmpList) {
            List<DtoAnalyseData> analyseDataList = smpId2AnaDataMap.containsKey(codeSmp.getId()) ? smpId2AnaDataMap.get(codeSmp.getId()) : new ArrayList<>();
            for (DtoAnalyseData analyseData : analyseDataList) {
                tstId2AnaDataMap.put(analyseData.getTestId(), analyseData);
            }
        }
        return tstId2AnaDataMap;
    }

    //#region

    /**
     * 根据评价配置返回所有评价记录
     *
     * @param value           评价配置
     * @param sampleFolderIds 点位id
     */
    private List<DtoEvaluationRecord> getAllRecordByEvaluation(DtoEvaluationValue value, List<String> sampleFolderIds) {
        DtoEvaluationResult entity = new DtoEvaluationResult();
        entity.setFolderPlan(EnumEvaluationPlan.实际.getValue());
        entity.setObjectType(EnumEvaluationType.点位.getValue());
        entity.setObjectIds(sampleFolderIds);
        List<DtoSamplingFrequencyTest> samplingFrequencyTests = this.getSamplingFrequencyTest(entity);

        List<DtoEvaluationRecord> list = new ArrayList<>();
        DtoEvaluationCriteria eva = evaluationCriteriaService.findOne(value.getEvaluationId());
        Optional<DtoEvaluationLevel> level = eva.getEvaluationLevel().stream().filter(p -> p.getId().equals(value.getLevelId())).findFirst();
        if (StringUtil.isNotNull(level) && level.isPresent()) {
            //因子标准值的map
            Map<String, DtoEvaluationValue> itemLimitMap = level.get().getEvaluationValue().stream().collect(Collectors.toMap(DtoEvaluationValue::getAnalyzeItemId, val -> val));
            samplingFrequencyTests.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSampleFolderId, Collectors.toList())).forEach((sampleFolderId, folderDatas) -> {
                folderDatas.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getTestId, Collectors.toList())).forEach((testId, datas) -> {
                    DtoSamplingFrequencyTest sft = datas.get(0);
                    DtoEvaluationRecord record = new DtoEvaluationRecord();
                    record.setObjectId(sampleFolderId);
                    record.setTestId(testId);
                    record.setObjectType(EnumEvaluationType.点位.getValue());
                    record.setFolderPlan(EnumEvaluationPlan.实际.getValue());
                    record.setEvaluationId(value.getEvaluationId());
                    record.setEvaluationLevelId(value.getLevelId());
                    setUpperLowLimitValue(itemLimitMap, record, sft.getAnalyseItemId());
                    list.add(record);
                });
            });
        }
        return list;
    }

    /**
     * 根据评价配置返回所有评价记录(obejctId为分析数据id)
     *
     * @param value           评价配置
     * @param analyseDataList 分析数据对象列表
     * @return List<DtoEvaluationRecord>
     */
    private List<DtoEvaluationRecord> getAllRecordByEvaluation_anaData(DtoEvaluationValue value, List<DtoAnalyseData> analyseDataList) {
        List<DtoEvaluationRecord> list = new ArrayList<>();
        DtoEvaluationCriteria eva = evaluationCriteriaService.findOne(value.getEvaluationId());
        Optional<DtoEvaluationLevel> level = eva.getEvaluationLevel().stream().filter(p -> p.getId().equals(value.getLevelId())).findFirst();
        if (StringUtil.isNotNull(level) && level.isPresent()) {
            //因子标准值的map
            Map<String, DtoEvaluationValue> itemLimitMap = level.get().getEvaluationValue().stream().collect(Collectors.toMap(DtoEvaluationValue::getAnalyzeItemId, val -> val));

            for (DtoAnalyseData dtoAnalyseData : analyseDataList) {
                DtoEvaluationRecord record = new DtoEvaluationRecord();
                record.setObjectId(dtoAnalyseData.getId());
                record.setTestId(dtoAnalyseData.getTestId());
                record.setObjectType(EnumEvaluationType.分析数据.getValue());
                record.setFolderPlan(EnumEvaluationPlan.分析数据.getValue());
                record.setEvaluationId(value.getEvaluationId());
                record.setEvaluationLevelId(value.getLevelId());
                setUpperLowLimitValue(itemLimitMap, record, dtoAnalyseData.getAnalyseItemId());
                list.add(record);
            }
        }
        return list;
    }

    /**
     * 设置评价信息对象的上下限值
     *
     * @param itemLimitMap  分析项目id和EvaluationValue对象的映射关系
     * @param record        评价信息对象
     * @param analyseItemId 分析项目id
     */
    private void setUpperLowLimitValue(Map<String, DtoEvaluationValue> itemLimitMap, DtoEvaluationRecord record, String analyseItemId) {
        if (itemLimitMap.containsKey(analyseItemId)) {
            DtoEvaluationValue evaVal = itemLimitMap.get(analyseItemId);
            if (MathUtil.isNumeral(evaVal.getUpperLimit())) {
                record.setUpperLimitValue(evaVal.getUpperLimit());
            }
            record.setUpperLimitSymble(evaVal.getUpperLimitSymble());
            if (MathUtil.isNumeral(evaVal.getLowerLimit())) {
                record.setLowerLimitValue(evaVal.getLowerLimit());
            }
            record.setLowerLimitSymble(evaVal.getLowerLimitSymble());
        }
    }

    /**
     * 获取点位频次指标信息
     *
     * @param entity 传参
     */
    private List<DtoSamplingFrequencyTest> getSamplingFrequencyTest(DtoEvaluationResult entity) {
        //读取web端所传点位相关的指标信息
        if (StringUtil.isNull(entity.getObjectIds()) || entity.getObjectIds().size() == 0) {
            return new ArrayList<>();
        }
        return samplingFrequencyTestRepository.findBySampleFolderIdIn(entity.getObjectIds());
    }

    /**
     * 写入超标信息
     *
     * @param record 评价记录
     * @param datas  分析数据
     */
    private void setExceedInfo(DtoEvaluationRecord record, List<DtoAnalyseData> datas) {
        DtoAnalyseData temp = datas.get(0);
        //DtoTest test = testService.findOne(record.getTestId());
        int mostSignificance = temp.getMostSignificance();
        int mostDecimal = temp.getMostDecimal();

        Map<String, String> testValueMap = new HashMap<>();
        testValueMap.put(EnumReportEvaluationType.平均值.getValue(), temp.getTestValue());
        testValueMap.put(EnumReportEvaluationType.最大值.getValue(), temp.getTestValue());
        testValueMap.put(EnumReportEvaluationType.最小值.getValue(), temp.getTestValue());

        List<String> testValues = new ArrayList<>();
        for (DtoAnalyseData data : datas) {
            if (MathUtil.isNumeral(data.getTestValue())) {
                testValues.add(data.getTestValue());
            }
        }

        if (testValues.size() > 0) {
            testValues.sort(Comparator.comparing((String str) -> MathUtil.getBigDecimal(str)));
            testValueMap.put(EnumReportEvaluationType.最大值.getValue(), testValues.get(testValues.size() - 1));
            BigDecimal sum = MathUtil.sumBigDecimal(testValues.toArray());
            if (datas.size() > testValues.size()) {
                BigDecimal examLimitValue = BigDecimal.valueOf(0);
                if (MathUtil.isNumeral(temp.getExamLimitValue())) {
                    examLimitValue = MathUtil.getBigDecimal(temp.getExamLimitValue());
                }
                //将检出限的一半当成出证值进行求和
                sum = MathUtil.sumBigDecimal(sum, MathUtil.multiplyBigDecimal(examLimitValue, 0.5, datas.size() - testValues.size()));
            } else {
                testValueMap.put(EnumReportEvaluationType.最小值.getValue(), testValues.get(0));
            }
            //求均值
            String testValue = proService.getDecimal(mostSignificance, mostDecimal, String.valueOf(sum.divide(new BigDecimal(datas.size()))));
            testValueMap.put(EnumReportEvaluationType.平均值.getValue(), testValue);
        }

        try {
            for (EnumReportEvaluationType c : EnumReportEvaluationType.values()) {
                String testValue = testValueMap.get(c.getValue());
                DtoEvaluationExceed exceedInfo = new DtoEvaluationExceed(testValue);

                DtoEvaluationValue evaValue = new DtoEvaluationValue();
                if (StringUtil.isNotNull(record.getUpperLimitValue())) {
                    evaValue.setUpperLimit(record.getUpperLimitValue().toString());
                }
                if (StringUtil.isNotNull(record.getLowerLimitValue())) {
                    evaValue.setLowerLimit(record.getLowerLimitValue().toString());
                }
                if (!dataEvaluateService.getIsPass(evaValue, testValue)) {
                    exceedInfo.setIsExceed(true);//改为超标
                    String overProofTimes = dataEvaluateService.getOverProofTimes(evaValue, testValue);
                    exceedInfo.setOverProofTimes(overProofTimes);
                }

                Field field = record.getClass().getDeclaredField(c.getValue());
                field.setAccessible(true);
                field.set(record, exceedInfo);
            }
        } catch (NoSuchFieldException | SecurityException | IllegalAccessException | IllegalArgumentException ex) {
            System.out.println(ex.getMessage());
            throw new BaseException("异常错误");
        }
    }

    /**
     * 写入评价结论
     *
     * @param evaluationResult 结论实体
     */
    private void setResultInfo(DtoEvaluationResult evaluationResult) {
        try {
            for (EnumReportEvaluationType c : EnumReportEvaluationType.values()) {
                List<String> results = new ArrayList<>();
                evaluationResult.getEvaluationRecord().stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getEvaluationLevelId, Collectors.toList())).forEach((evaluationLevelId, records) -> {
                    List<String> thisLevelResults = new ArrayList<>();
                    for (DtoEvaluationRecord record : records) {
                        try {
                            Field field = record.getClass().getDeclaredField(c.getValue());
                            field.setAccessible(true);
                            DtoEvaluationExceed exceed = (DtoEvaluationExceed) field.get(record);
                            if (exceed.getIsExceed()) {//超标
                                if (exceed.getOverProofTimes().equals("--")) {
                                    thisLevelResults.add(String.format("%s超标", record.getRedAnalyzeItemName()));
                                } else {
                                    thisLevelResults.add(String.format("%s(超标%s倍)", record.getRedAnalyzeItemName(), exceed.getOverProofTimes()));
                                }
                            }
                        } catch (NoSuchFieldException | SecurityException | IllegalAccessException | IllegalArgumentException ex) {
                            System.out.println(ex.getMessage());
                            throw new BaseException("异常错误");
                        }
                    }
                    DtoEvaluationRecord rec = records.get(0);
                    if (thisLevelResults.size() > 0) {
                        results.add(String.format("依据%s%s,%s", rec.getEvaluationName(), rec.getEvaluationLevelName(), String.join(",", thisLevelResults)));
                    }
                });
                String result = results.size() > 0 ? String.join(";", results) : "--";

                Field field = evaluationResult.getClass().getDeclaredField(String.format("%sResult", c.getValue()));
                field.setAccessible(true);
                field.set(evaluationResult, result);
            }
        } catch (NoSuchFieldException | SecurityException | IllegalAccessException | IllegalArgumentException ex) {
            System.out.println(ex.getMessage());
            throw new BaseException("异常错误");
        }
    }

    /**
     * 获取标准名称
     *
     * @param evaluationId 评价标准id
     */
    private String getEvaluationName(String evaluationId) {
        DtoEvaluationCriteria eva = evaluationCriteriaService.findOne(evaluationId);
        if (StringUtil.isNotNull(eva)) {
            return eva.getName() + "(" + eva.getCode() + ")";
        }
        return "";
    }

    /**
     * 获取标准名称
     *
     * @param evaluationId      评价标准id
     * @param evaluationLevelId 评价标准等级id
     */
    private String getEvaluationLevelName(String evaluationId, String evaluationLevelId) {
        String parentId = "", conditionName = "", levelName = "";
        DtoEvaluationCriteria eva = evaluationCriteriaService.findOne(evaluationId);
        if (StringUtil.isNotNull(eva)) {
            Optional<DtoEvaluationLevel> level = eva.getEvaluationLevel().stream().filter(p -> p.getId().equals(evaluationLevelId)).findFirst();
            if (StringUtil.isNotNull(level) && level.isPresent()) {
                levelName = level.get().getName();
                parentId = level.get().getParentId();
            }
            String finalParentId = parentId;
            Optional<DtoEvaluationLevel> parentLevel = eva.getEvaluationLevel().stream().filter(p -> p.getId().equals(finalParentId)).findFirst();
            if (StringUtil.isNotNull(parentLevel) && parentLevel.isPresent()) {
                conditionName = parentLevel.get().getName();
            }
        }
        List<String> names = new ArrayList<>();
        if (StringUtils.isNotNullAndEmpty(conditionName)) {
            names.add(conditionName);
        }
        if (StringUtils.isNotNullAndEmpty(levelName)) {
            names.add(levelName);
        }
        return String.join("-", names);
    }

    /**
     * 判断是否一致
     */
    private Boolean recordEqual(DtoEvaluationRecord a, DtoEvaluationRecord b) {
        boolean isSameLowerLimitValue = Boolean.TRUE;
        boolean isSameUpperLimitValue = Boolean.TRUE;
        boolean isUpperLimitSymble = Boolean.TRUE;
        boolean isLowerLimitSymble = Boolean.TRUE;
        boolean isDimensionId = Boolean.TRUE;
        boolean isEmissionRate = Boolean.TRUE;
        if (a.getLowerLimitValue() == null && b.getLowerLimitValue() != null) {
            isSameLowerLimitValue = Boolean.FALSE;
        } else if (a.getLowerLimitValue() != null && b.getLowerLimitValue() == null) {
            isSameLowerLimitValue = Boolean.FALSE;
        } else if (a.getLowerLimitValue() == null && b.getLowerLimitValue() == null) {
            isSameLowerLimitValue = Boolean.TRUE;
        } else {
            isSameLowerLimitValue = a.getLowerLimitValue().compareTo(b.getLowerLimitValue()) == 0;
        }
        if (a.getUpperLimitValue() == null && b.getUpperLimitValue() != null) {
            isSameUpperLimitValue = Boolean.FALSE;
        } else if (a.getUpperLimitValue() != null && b.getUpperLimitValue() == null) {
            isSameUpperLimitValue = Boolean.FALSE;
        } else if (a.getUpperLimitValue() == null && b.getUpperLimitValue() == null) {
            isSameUpperLimitValue = Boolean.TRUE;
        } else {
            isSameUpperLimitValue = a.getUpperLimitValue().compareTo(b.getUpperLimitValue()) == 0;
        }
        if (a.getUpperLimitSymble() == null && b.getUpperLimitSymble() != null) {
            isUpperLimitSymble = Boolean.FALSE;
        } else if (a.getUpperLimitSymble() != null && b.getUpperLimitSymble() == null) {
            isUpperLimitSymble = Boolean.FALSE;
        } else if (a.getUpperLimitSymble() == null && b.getUpperLimitSymble() == null) {
            isUpperLimitSymble = Boolean.TRUE;
        } else {
            isUpperLimitSymble = a.getUpperLimitSymble().compareTo(b.getUpperLimitSymble()) == 0;
        }
        if (a.getLowerLimitSymble() == null && b.getLowerLimitSymble() != null) {
            isLowerLimitSymble = Boolean.FALSE;
        } else if (a.getLowerLimitSymble() != null && b.getLowerLimitSymble() == null) {
            isLowerLimitSymble = Boolean.FALSE;
        } else if (a.getLowerLimitSymble() == null && b.getLowerLimitSymble() == null) {
            isLowerLimitSymble = Boolean.TRUE;
        } else {
            isLowerLimitSymble = a.getLowerLimitSymble().compareTo(b.getLowerLimitSymble()) == 0;
        }
        if (a.getDimensionId() == null && b.getDimensionId() != null) {
            isDimensionId = Boolean.FALSE;
        } else if (a.getDimensionId() != null && b.getDimensionId() == null) {
            isDimensionId = Boolean.FALSE;
        } else if (a.getDimensionId() == null && b.getDimensionId() == null) {
            isDimensionId = Boolean.TRUE;
        } else {
            isDimensionId = a.getDimensionId().compareTo(b.getDimensionId()) == 0;
        }
        if (a.getEmissionRate() == null && b.getEmissionRate() != null) {
            isEmissionRate = Boolean.FALSE;
        } else if (a.getEmissionRate() != null && b.getEmissionRate() == null) {
            isEmissionRate = Boolean.FALSE;
        } else if (a.getEmissionRate() == null && b.getEmissionRate() == null) {
            isEmissionRate = Boolean.TRUE;
        } else {
            isEmissionRate = a.getEmissionRate().compareTo(b.getEmissionRate()) == 0;
        }
        return a.getEvaluationId().equals(b.getEvaluationId()) &&
                a.getEvaluationLevelId().equals(b.getEvaluationLevelId()) &&
                isSameLowerLimitValue && isSameUpperLimitValue
                && isUpperLimitSymble && isLowerLimitSymble && isDimensionId && isEmissionRate;
    }
    //#endregion

}