package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoItemRelation;

import java.util.List;


/**
 * ItemRelation数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/11/1
 * @since V100R001
 */
public interface ItemRelationRepository extends IBaseJpaPhysicalDeleteRepository<DtoItemRelation, String> {
    /**
     * 根据类型查询
     * @param type 类型
     * @return 结果
     */
    List<DtoItemRelation> findByType(Integer type);
}