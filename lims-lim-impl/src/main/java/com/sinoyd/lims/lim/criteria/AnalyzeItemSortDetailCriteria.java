package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 分析项目排序实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/23
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeItemSortDetailCriteria extends BaseCriteria implements Serializable {
    /**
     * 排序Id
     */
    private String sortId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder conditionSb = new StringBuilder();
        conditionSb.append(" and a.analyzeItemId = b.id and b.isDeleted = 0");

        if (StringUtils.isNotNullAndEmpty(this.sortId)) {
            conditionSb.append(" and a.sortId = :sortId");
            values.put("sortId", this.sortId);
        }
        return conditionSb.toString();
    }
}
