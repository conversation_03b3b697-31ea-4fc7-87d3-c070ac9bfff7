package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.monitor.dto.lims.DtoOicInformation;
import com.sinoyd.lims.monitor.dto.lims.DtoOicInformation2Test;
import com.sinoyd.lims.monitor.repository.lims.OicInformation2TestRepository;
import com.sinoyd.lims.monitor.repository.lims.OicInformationRepository;
import com.sinoyd.lims.monitor.service.OicInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;


/**
 * OicInformation操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
@Service
public class OicInformationServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoOicInformation, String, OicInformationRepository> implements OicInformationService {

    @Autowired
    private OicInformation2TestRepository oicInformation2TestRepository;

    @Autowired
    private AnalyzeItemRepository analyzeItemRepository;


    @Override
    public void findByPage(PageBean<DtoOicInformation> pb, BaseCriteria oicInformationCriteria) {
        pb.setEntityName("DtoOicInformation a");
        pb.setSelect("select a");
        super.findByPage(pb, oicInformationCriteria);
        List<DtoOicInformation> dataList = pb.getData();
        //拿到仪器表的id集合
        List<String> ids = dataList.parallelStream().map(DtoOicInformation::getId).collect(Collectors.toList());
        //通过仪器表的id集合拿到关联表的集合
        List<DtoOicInformation2Test> oicInformation2TestList = oicInformation2TestRepository.findByOicIdIn(ids);
        if (StringUtil.isNotEmpty(oicInformation2TestList)) {
            //拿到检测项目的id集合(去重)
            List<String> idList = oicInformation2TestList.parallelStream().distinct().map(DtoOicInformation2Test::getTestId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(idList)) {
                //拿到对应的分析项目
                List<DtoAnalyzeItem> dtoAnalyzeItems = analyzeItemRepository.findAll(idList);
                if (StringUtil.isNotEmpty(dataList)) {
                    for (DtoOicInformation dto : dataList) {
                        //获取单个仪器的分析项目集合
                        List<DtoOicInformation2Test> dtoOicInformation2Tests = oicInformation2TestList.parallelStream().filter(p -> p.getOicId().equals(dto.getId())).collect(Collectors.toList());
                        List<String> testList = dtoOicInformation2Tests.parallelStream().map(DtoOicInformation2Test::getTestId).collect(Collectors.toList());
                        if (StringUtil.isNotEmpty(dtoAnalyzeItems)) {
                            List<DtoAnalyzeItem> dtoAnalyzeItemsList = dtoAnalyzeItems.parallelStream().filter(p -> testList.contains(p.getId())).collect(Collectors.toList());
                            //拿到分析项目的名字集合
                            List<String> name = dtoAnalyzeItemsList.parallelStream().map(DtoAnalyzeItem::getAnalyzeItemName).collect(Collectors.toList());
                            // 将名字集合转换成字符串
                            dto.setAnalyzeItemName(String.join(",", name));
                            //筛选当前点位的仪器测试表
                            List<DtoOicInformation2Test> dtoOicInformation2TestList = oicInformation2TestList.parallelStream().filter(p -> p.getOicId().equals(dto.getId())).collect(Collectors.toList());
                            //获取当前仪器测试表的testid
                            List<String> list = dtoOicInformation2TestList.parallelStream().map(DtoOicInformation2Test::getTestId).collect(Collectors.toList());
                            dto.setAnalysis(list);
                        }
                    }
                }
            }
        }
    }

    @Transactional
    @Override
    public DtoOicInformation save(DtoOicInformation entity) {
        List<String> list = entity.getAnalysis();
        super.save(entity);
        List<DtoOicInformation2Test> dtoOicInformation2TestList = new ArrayList<>();
        for (String s : list) {
            DtoOicInformation2Test dtoOicInformation2Test = new DtoOicInformation2Test();
            dtoOicInformation2Test.setOicId(entity.getId());
            dtoOicInformation2Test.setTestId(s);
            dtoOicInformation2TestList.add(dtoOicInformation2Test);
        }
        oicInformation2TestRepository.save(dtoOicInformation2TestList);
        return entity;
    }


    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> idlist = (List<String>) ids;
        List<DtoOicInformation2Test> dtoOicInformation2TestList = oicInformation2TestRepository.findByOicIdIn(idlist);
        if (StringUtil.isNotEmpty (dtoOicInformation2TestList)) {
            List<String> idsList = dtoOicInformation2TestList.parallelStream().map(DtoOicInformation2Test::getId).collect(Collectors.toList());
            oicInformation2TestRepository.logicDeleteById(idsList);
        }
        return super.logicDeleteById(ids);
    }

    @Transactional
    @Override
    public DtoOicInformation update(DtoOicInformation entity) {
        //找到前端要修改的仪器分析项目表的集合
        List<DtoOicInformation2Test> dtoOicInformation2TestList = oicInformation2TestRepository.findByOicId(entity.getId());
        if (StringUtil.isNotEmpty(dtoOicInformation2TestList)){
            //获取仪器项目表的主键集合进行删除操作
            List<String> list = dtoOicInformation2TestList.parallelStream().map(DtoOicInformation2Test::getId).collect(Collectors.toList());
            oicInformation2TestRepository.logicDeleteById(list);
        }
        return this.save(entity);
    }
}