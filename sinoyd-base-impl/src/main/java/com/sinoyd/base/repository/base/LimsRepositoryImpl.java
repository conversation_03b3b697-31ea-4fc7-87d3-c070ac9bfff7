package com.sinoyd.base.repository.base;

import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.beans.PropertyDescriptor;
import java.io.Serializable;
import java.util.*;

/**
 * LIMS 默认Repository实现
 *
 * @param <T>  实体
 * @param <ID> 主键
 * <AUTHOR>
 * @version V1.0.0 2022/3/29
 * @since V100R001
 */
@Slf4j
@NoRepositoryBean
public class LimsRepositoryImpl<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> implements LimsRepository<T, ID> {


    private final int BATCH_SIZE = 500;
    private final EntityManager em;
    private final JpaEntityInformation<T, ?> entityInformation;

    public LimsRepositoryImpl(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.em = entityManager;
        this.entityInformation = entityInformation;
    }

    public LimsRepositoryImpl(Class<T> domainClass, EntityManager em) {
        super(domainClass, em);
        this.em = em;
        this.entityInformation = JpaEntityInformationSupport.getEntityInformation(domainClass, em);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <S extends T> S save(S s) {
        return commonSave(s,false);
    }

    /**
     * 该方法会批量处理保存和新增的数据，会自行判断是新增还是修改数据，但是效率没有单一的批量插入和修改好
     * 数据量不大的情况下可以使用
     *
     * @param entities 实体集合
     * @param <S>      实体类型
     * @return 实体
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public <S extends T> List<S> save(Iterable<S> entities) {
        return commonSave(entities,false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <S extends T> S saveWithNull(S s) {
        return commonSave(s,true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <S extends T> List<S> saveWithNull(Iterable<S> entities) {
        return commonSave(entities,true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Iterable<? extends T> entities) {
        deleteInBatch((Iterable<T>) entities);
    }

    /**
     * 批量新增
     *
     * @param entities 实体集合
     * @return 实体
     */
    @Override
    public <S extends T> List<S> batchInsert(Iterable<S> entities) {
        Assert.notNull(entities, "parameter can not be null");
        Iterator<S> iterator = entities.iterator();
        List<S> list = new ArrayList<>();
        int index = 0;
        while (iterator.hasNext()) {
            S s = iterator.next();
            em.persist(s);
            index++;
            if (index % BATCH_SIZE == 0) {
                em.flush();
                em.clear();
            }
            list.add(s);
        }
        if (index % BATCH_SIZE != 0) {
            em.flush();
            em.clear();
        }
        entities.forEach(list::add);
        return list;
    }

    /**
     * 批量更新
     *
     * @param entities 实体集合
     * @return 实体
     */
    @Override
    public <S extends T> List<S> batchUpdate(Iterable<S> entities) {
        Assert.notNull(entities, "parameter can not be null");
        Iterator<S> iterator = entities.iterator();
        List<S> list = new ArrayList<>();
        int index = 0;
        while (iterator.hasNext()) {
            S s = iterator.next();
            em.merge(s);
            index++;
            if (index % BATCH_SIZE == 0) {
                em.flush();
                em.clear();
            }
            list.add(s);
        }
        if (index % BATCH_SIZE != 0) {
            em.flush();
            em.clear();
        }
        entities.forEach(list::add);
        return list;
    }

    /**
     * 获取对象的空属性
     *
     * @param src 对象
     * @return 空属性数组
     */
    private static String[] getNullProperties(Object src) {
        //获取Bean
        BeanWrapper srcBean = new BeanWrapperImpl(src);
        //获取Bean的属性描述
        PropertyDescriptor[] pds = srcBean.getPropertyDescriptors();
        //获取Bean的空属性
        Set<String> properties = new HashSet<>();
        for (PropertyDescriptor propertyDescriptor : pds) {
            String propertyName = propertyDescriptor.getName();
            Object propertyValue = srcBean.getPropertyValue(propertyName);
            if (propertyValue == null) {
                srcBean.setPropertyValue(propertyName, null);
                properties.add(propertyName);
            }
        }
        return properties.toArray(new String[0]);
    }

    /**
     *
     * @param s         实体
     * @param saveNull  是否保存null值
     * @param <S>       类型
     * @return          实体
     */
    private <S extends T> S commonSave(S s,boolean saveNull){
        Assert.notNull(s, "parameter can not be null");
        ID id = (ID) entityInformation.getId(s);
        T target = findOne(id);
        if (target == null) {
            em.persist(s);
            return s;
        } else {
            if(saveNull){
                BeanUtils.copyProperties(s, target);
            }else{
                String[] nullProperties = getNullProperties(s);
                BeanUtils.copyProperties(s, target, nullProperties);
            }
            em.merge(target);
            return (S)target;
        }
    }

    /**
     *
     * @param entities 实体集合
     * @param <S>      实体类型
     * @return 实体
     */
    private <S extends T> List<S> commonSave(Iterable<S> entities,boolean saveNull) {
        Assert.notNull(entities, "parameter can not be null");
        Iterator<S> iterator = entities.iterator();
        List<S> list = new ArrayList<>();
        int index = 0;
        while (iterator.hasNext()) {
            S s = iterator.next();
            ID id = (ID) entityInformation.getId(s);
            T target = findOne(id);
            if (target == null) {
                em.persist(s);
                list.add(s);
            } else {
                if(saveNull){
                    BeanUtils.copyProperties(s, target);
                }else{
                    String[] nullProperties = getNullProperties(s);
                    BeanUtils.copyProperties(s, target, nullProperties);
                }
                em.merge(target);
                list.add((S)target);
            }
            index++;
            if (index % BATCH_SIZE == 0) {
                em.flush();
                em.clear();
            }
        }
        if (index % BATCH_SIZE != 0) {
            em.flush();
            em.clear();
        }
        return list;
    }

}