package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.CostInfoService;
import com.sinoyd.lims.pro.criteria.CostInfoCriteria;
import com.sinoyd.lims.pro.dto.DtoCostInfo;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * 费用服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: 费用服务")
 @RestController
 @RequestMapping("api/pro/costInfo")
 public class CostInfoController extends BaseJpaController<DtoCostInfo, String,CostInfoService> {
    /**
     * 分页动态条件查询CostInfo
     *
     * @param costInfoCriteria 条件参数
     * @return RestResponse<List < CostInfo>>
     */
    @ApiOperation(value = "分页动态条件查询CostInfo", notes = "分页动态条件查询CostInfo")
    @GetMapping
    public RestResponse<List<DtoCostInfo>> findByPage(CostInfoCriteria costInfoCriteria) {
        PageBean<DtoCostInfo> pageBean = super.getPageBean();
        RestResponse<List<DtoCostInfo>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, costInfoCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询CostInfo
     *
     * @param id 主键id
     * @return RestResponse<DtoCostInfo>
     */
    @ApiOperation(value = "按主键查询CostInfo", notes = "按主键查询CostInfo")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoCostInfo> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoCostInfo> restResponse = new RestResponse<>();
        DtoCostInfo costInfo = service.findOne(id);
        restResponse.setData(costInfo);
        restResponse.setRestStatus(StringUtil.isNull(costInfo) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增CostInfo
     *
     * @param costInfo 实体列表
     * @return RestResponse<DtoCostInfo>
     */
    @ApiOperation(value = "新增CostInfo", notes = "新增CostInfo")
    @PostMapping
    public RestResponse<DtoCostInfo> create(@RequestBody @Validated DtoCostInfo costInfo) {
        RestResponse<DtoCostInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.save(costInfo));
        return restResponse;
    }

    /**
     * 新增CostInfo
     *
     * @param costInfo 实体列表
     * @return RestResponse<DtoCostInfo>
     */
    @ApiOperation(value = "修改CostInfo", notes = "修改CostInfo")
    @PutMapping
    public RestResponse<DtoCostInfo> update(@RequestBody @Validated DtoCostInfo costInfo) {
        RestResponse<DtoCostInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.update(costInfo));
        return restResponse;
    }

    /**
     * "根据id批量删除CostInfo
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除CostInfo", notes = "根据id批量删除CostInfo")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 刷新费用
     *
     * @param costInfo 费用实体，接收费用主键id
     * @return RestResponse<DtoCostInfo>
     */
    @ApiOperation(value = "刷新费用", notes = "刷新费用")
    @PostMapping(path = "/reload")
    public RestResponse<DtoCostInfo> reload(@RequestBody DtoCostInfo costInfo) {
        RestResponse<DtoCostInfo> restResponse = new RestResponse<>();
        restResponse.setData(service.reload(costInfo.getId()));
        return restResponse;
    }

    /**
     * "信号操作
     *
     * @param dto 实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "信号操作", notes = "信号操作")
    @PostMapping("/signal")
    public RestResponse<Boolean> signal(@RequestBody DtoWorkflowSign dto) {
        RestResponse<Boolean> restResp = new RestResponse<>();
        service.costInfoSignal(dto);
        restResp.setData(true);
        return restResp;
    }
}