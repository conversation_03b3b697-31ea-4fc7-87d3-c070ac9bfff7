package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import javax.persistence.Transient;

@Data
public class DtoImport<PERSON>erson implements IExcelDataModel, IExcelModel {

    /**
     * 中文名称
     */
    @Excel(name = "姓名(必填)",orderNum = "20",width = 11)
    private String chineseName;

    /**
     * 用户名
     */
    @Excel(name = "用户名",orderNum = "21",width = 15)
    private String userName;

    /**
     * 个人邮箱
     */
    @Excel(name = "个人邮箱",orderNum = "22",width = 20)
    private String email;

    /**
     * 编号
     */
    @Excel(name = "编号(必填)",orderNum = "30",width = 11)
    private String userNo;

    /**
     * 所属科室（Guid）
     */
    @Excel(name = "所属科室(必填)",orderNum = "40",width = 23)
    private String deptId;

    /**
     * 职务（常量（Guid）：LIM_Post）
     */
    @Excel(name = "职务(必填)",orderNum = "50",width = 17)
    private String postId;

    /**
     * 职称（常量（Guid）：LIM_TechnicalTitle）
     */
    @Excel(name = "职称",orderNum = "60",width = 24)
    private String technicalTitleId;

    /**
     * 职称获得日期
     */
    @Excel(name = "职称获得日期",orderNum = "70",width = 17,format = "yyyy-MM-dd")
    private String technicalTitleDate;

    /**
     * 性别（枚举EnumSex：  1代表男   2代表女）
     */
    @Excel(name = "性别(必填)",orderNum = "80",width = 11)
    private String sex;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期",orderNum = "90",width = 17,format = "yyyy-MM-dd")
    private String birthDay;

    /**
     * 籍贯
     */
    @Excel(name = "籍贯",orderNum = "100",width = 11)
    private String nativePlace;

    /**
     * 身份证
     */
    @Excel(name = "身份证号",orderNum = "110",width = 20)
    private String idCard;

    /**
     * 政治面貌
     */
    @Excel(name = "政治面貌",orderNum = "120",width = 17)
    private String politicalFace;

    /**
     * 入党日期
     */
    @Excel(name = "入党日期",orderNum = "130",width = 17,format = "yyyy-MM-dd")
    private String joinPartyDate;

    /**
     * 手机
     */
    @Excel(name = "手机号(必填)",orderNum = "140",width = 17)
    private String mobile;

    /**
     * 固定电话
     */
    @Excel(name = "固定电话",orderNum = "150",width = 17)
    private String homeTel;

    /**
     * 住址
     */
    @Excel(name = "家庭住址",orderNum = "170",width = 41)
    private String homeAddress;

    /**
     * 学历（常量（Guid）：LIM_Degree：大专、本科、硕士研究生、博士研究生）
     */
    @Excel(name = "学历",orderNum = "180",width = 14)
    private String degree;

    /**
     * 专业
     */
    @Excel(name = "专业",orderNum = "190",width = 14)
    private String specialty;

    /**
     * 毕业院校
     */
    @Excel(name = "毕业院校",orderNum = "200",width = 26)
    private String school;

    /**
     * 入职时间
     */
    @Excel(name = "入职日期",orderNum = "210",width = 26,format = "yyyy-MM-dd")
    private String joinCompanyTime;

    /**
     * 离职时间
     */
    @Excel(name = "离职日期",orderNum = "220",width = 21,format = "yyyy-MM-dd")
    private String leaveCompanyTime;

    /**
     * 状态（枚举EnumPersonStatus:1代表在职 2代表离职 3代表休假）
     */
    @Excel(name = "人员状态(必填)",orderNum = "230",width = 25,replace = {"在职_1","离职_2","休假_3","_null"})
    private String status;

    /**
     * 紧急联络人
     */
    @Excel(name = "紧急联络人",orderNum = "240",width = 14)
    private String emergentLinkMan;

    /**
     * 联络方法
     */
    @Excel(name = "联络方式",orderNum = "250",width = 14)
    private String contactMethod;

    /**
     * 角色
     */
    @Transient
    @Excel(name = "角色",orderNum = "260",width = 25)
    private String roleNames;

    private Boolean isCreateUser;

    private Integer rowNum;

    private String errorMsg;


    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }
}

