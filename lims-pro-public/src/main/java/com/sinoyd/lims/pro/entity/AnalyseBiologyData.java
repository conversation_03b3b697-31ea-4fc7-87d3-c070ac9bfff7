package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;


/**
 * AnalyseBiologyData实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="AnalyseBiologyData")
 @Data
 public  class AnalyseBiologyData implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  AnalyseBiologyData() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 父级Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("父级Id")
	private String parentId;
    
    /**
    * 数据Id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("数据Id")
	private String analyseDataId;
    
    /**
    * 生物Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("生物Id")
	private String taxonomyId;
    
    /**
    * 数据结果
    */
    @Column(length=100)
    @ApiModelProperty("数据结果")
    @Length(message = "数据结果{validation.message.length}", max = 100)
	private String testValue;
    
    /**
    * 数据结果（用于计算）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("数据结果（用于计算）")
	private BigDecimal testValueD;
    
    /**
    * 计量单位
    */
    @Column(length=50)
    @ApiModelProperty("计量单位")
    @Length(message = "计量单位{validation.message.length}", max = 50)
	private String dimension;
    
    /**
    * 计量单位Id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("计量单位Id")
	private String dimensionId;
    
    /**
    * 体积
    */
    @Column(length=50)
    @ApiModelProperty("体积")
    @Length(message = "体积{validation.message.length}", max = 50)
	private String volume;
    
    /**
    * 计数值（统计量）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("计数值（统计量）")
	private Integer countValue;
    
    /**
    * 次数
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("次数")
	private Integer times;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }