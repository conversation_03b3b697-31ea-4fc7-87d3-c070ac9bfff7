package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.rcc.DtoSubstitute;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.customer.DtoImportQualityControlLimit;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V100R001
 * @date V1.0.0 2024/05/27
 */
@Data
public class QualityControlLimitVerifyHandler implements IExcelVerifyHandler<DtoImportQualityControlLimit> {

    /**
     * 计算公式
     */
    private static final List<String> formulaList = Arrays.asList("a/b*100%", "|(b-a)/a|*100%", "|(b-a)/(a+b)|*100%",
            "|(b-a)/((a+b)/2)|*100%", "(b-a)/a*100%", "(b-a)/(a+b)*100%", "(b-a)/((a+b)/2)*100%", "[B]/([A]+[B])*100", "[B]/[A]*100");
    /**
     * 质控类型集合
     */
    private static final List<EnumLIM.EnumQCType> qcTypeList = Arrays.stream(EnumLIM.EnumQCType.values()).collect(Collectors.toList());

    /**
     * 测试项目校验容器，存放所有测试项目
     */
    private List<DtoTest> allTestList;

    /**
     * 所有替代物数据
     */
    private List<DtoSubstitute> allSubstituteList;

    /**
     * 检测类型
     */
    private List<DtoSampleType> allSampleTypeList;

    private final ImportUtils importUtils = new ImportUtils();


    /**
     * 导入校验方法
     *
     * @param importQcLimit 当前对象
     * @return
     */
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportQualityControlLimit importQcLimit) {

        //导入数据处理,跳过空行,数据去除前后空格
        try {
            if (importUtils.checkObjectIsNull(importQcLimit)) {
                return new ExcelVerifyHandlerResult(true);
            }
            importUtils.strToTrim(importQcLimit);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }

        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + (importQcLimit.getRowNum() - 1) + "行数据校验有误");

        importUtils.checkIsNull(result, importQcLimit.getSampleTypeName(), "检测类型", failStr);
        importUtils.checkIsNull(result, importQcLimit.getRedAnalyzeItemName(), "分析项目", failStr);
        importUtils.checkIsNull(result, importQcLimit.getRedAnalyzeMethodName(), "分析方法", failStr);
        importUtils.checkIsNull(result, importQcLimit.getRedCountryStandard(), "标准编号", failStr);
        checkTest(result, failStr, importQcLimit, allTestList, allSampleTypeList);

        // 根据不同的质控类型校验
        checkByQcType(result, failStr, importQcLimit);
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }

    /**
     * 检查测试项目
     *
     * @param result            校验结果
     * @param failStr           校验错误数据
     * @param importQcLimit     导入行数据
     * @param allTestList       测试项目
     * @param allSampleTypeList 检测类型
     */
    private void checkTest(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportQualityControlLimit importQcLimit, List<DtoTest> allTestList, List<DtoSampleType> allSampleTypeList) {

        if (StringUtil.isNotEmpty(importQcLimit.getSampleTypeName())) {
            Optional<DtoSampleType> sampleTypeOptional = allSampleTypeList.stream().filter(p -> p.getTypeName().equals(importQcLimit.getSampleTypeName())).findFirst();
            if (sampleTypeOptional.isPresent()) {
                String sampleTypeId = sampleTypeOptional.get().getId();
                Optional<DtoTest> testOptional = allTestList.stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId)
                        && p.getRedAnalyzeItemName().equals(importQcLimit.getRedAnalyzeItemName())
                        && p.getRedAnalyzeMethodName().equals(importQcLimit.getRedAnalyzeMethodName())).findFirst();
                if (testOptional.isPresent()) {
                    importQcLimit.setTestId(testOptional.get().getId());
                } else {
                    result.setSuccess(false);
                    failStr.append("；").append("测试项目不存在");
                }
            } else {
                result.setSuccess(false);
                failStr.append("；").append("检测类型不存在");
            }
        }

    }

    /**
     * 根据质控类型校验
     *
     * @param result        校验结果
     * @param failStr       校验错误数据
     * @param importQcLimit 导入行数据
     */
    private void checkByQcType(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportQualityControlLimit importQcLimit) {

        importQcLimit.setQcGrade(EnumLIM.EnumQCGrade.内部质控.getValue());
        qcTypeList.stream().filter(p -> p.name().equals(importQcLimit.getQcTypeName())).findFirst().ifPresent(qcType -> {
            importQcLimit.setQcType(qcType.getValue());
        });
        importUtils.checkIsNull(result, importQcLimit.getQcTypeName(), "质控类型", failStr);
        importUtils.checkIsNull(result, importQcLimit.getJudgeMethod(), "评判方式", failStr);
        String qcTypeName = importQcLimit.getQcTypeName();
        List<String> judgingMethods = new ArrayList<>();
        // 、现场空白、运输空白、设备空白、室内空白、仪器空白、试剂空白、采样介质空白
        if ("全程序空白".equals(qcTypeName) || EnumLIM.EnumQCType.现场空白.name().equals(qcTypeName) || EnumLIM.EnumQCType.运输空白.name().equals(qcTypeName)
                || "设备空白".equals(qcTypeName) || EnumLIM.EnumQCType.仪器空白.name().equals(qcTypeName) || "室内空白".equals(qcTypeName)
                || EnumLIM.EnumQCType.试剂空白.name().equals(qcTypeName) || EnumLIM.EnumQCType.采样介质空白.name().equals(qcTypeName)) {
            importUtils.checkIsNull(result, importQcLimit.getCheckItemStr(), "检查项", failStr);
            judgingMethods = Arrays.asList(EnumBase.EnumJudgingMethod.限值判定.name(), EnumBase.EnumJudgingMethod.小于检出限.name(),
                    EnumBase.EnumJudgingMethod.小于测定下限.name());
            if (!EnumLIM.EnumCheckItemType.出证结果.name().equals(importQcLimit.getCheckItemStr())) {
                importQcLimit.setCheckItemOther(importQcLimit.getCheckItemStr());
                importQcLimit.setCheckItem(EnumLIM.EnumCheckItemType.公式参数.getValue());
            } else {
                importQcLimit.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.getValue());
            }
            if (EnumBase.EnumJudgingMethod.限值判定.name().equals(importQcLimit.getJudgeMethod())) {
                importUtils.checkIsNull(result, importQcLimit.getAllowLimit(), "允许限值", failStr);
            }

            if ("全程序空白".equals(qcTypeName) || EnumLIM.EnumQCType.现场空白.name().equals(qcTypeName) || EnumLIM.EnumQCType.运输空白.name().equals(qcTypeName) ||
                    "设备空白".equals(qcTypeName)) {
                importQcLimit.setQcGrade(EnumLIM.EnumQCGrade.外部质控.getValue());
                if ("设备空白".equals(qcTypeName)) {
                    importQcLimit.setQcType(EnumLIM.EnumQCType.仪器空白.getValue());
                }
            }
            if ("全程序空白".equals(qcTypeName) || "室内空白".equals(qcTypeName)) {
                importQcLimit.setQcType(EnumLIM.EnumQCType.空白.getValue());
            }
            if (StringUtil.isNotEmpty(importQcLimit.getStandard())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入标准物质加入量范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getRangeConfig())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入检查项范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getSubstituteName())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入替代物");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getFormula())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入计算公式。");
            }


        } else if ("现场平行".equals(qcTypeName) || "室内平行".equals(qcTypeName)) {
            importUtils.checkIsNull(result, importQcLimit.getAllowLimit(), "允许限值", failStr);
            judgingMethods = Arrays.asList(EnumBase.EnumJudgingMethod.相对偏差.name(), EnumBase.EnumJudgingMethod.相对误差.name(),
                    EnumBase.EnumJudgingMethod.绝对偏差.name(), EnumBase.EnumJudgingMethod.绝对误差.name());
            // 是否设置检查项
            if (StringUtil.isEmpty(importQcLimit.getRangeConfig())) {
                importQcLimit.setIsCheckItem(0);
                importQcLimit.setCheckItem(-1);
            }
            if ("现场平行".equals(qcTypeName)) {
                importQcLimit.setQcGrade(EnumLIM.EnumQCGrade.外部质控.getValue());
            }
            importQcLimit.setQcType(EnumLIM.EnumQCType.平行.getValue());
            if (StringUtil.isNotEmpty(importQcLimit.getStandard())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入标准物质加入量范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getSubstituteName())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入替代物。");
            }
        } else if (EnumLIM.EnumQCType.串联样.name().equals(qcTypeName) || "室内串联样".equals(qcTypeName)) {
            importUtils.checkIsNull(result, importQcLimit.getAllowLimit(), "允许限值", failStr);
            importUtils.checkIsNull(result, importQcLimit.getFormula(), "穿透公式", failStr);
            judgingMethods = Collections.singletonList(EnumBase.EnumJudgingMethod.穿透率.name());
            // 是否设置检查项
            if (StringUtil.isEmpty(importQcLimit.getRangeConfig())) {
                importQcLimit.setIsCheckItem(0);
                importQcLimit.setCheckItem(-1);
            }
            if ("串联样".equals(qcTypeName)) {
                importQcLimit.setQcGrade(EnumLIM.EnumQCGrade.外部质控.getValue());
            }
            importQcLimit.setQcType(EnumLIM.EnumQCType.串联样.getValue());
            if (StringUtil.isNotEmpty(importQcLimit.getStandard())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入标准物质加入量范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getRangeConfig())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入检查项范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getSubstituteName())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入替代物。");
            }


        } else if (EnumLIM.EnumQCType.标准.name().equals(qcTypeName) || "标样".equals(qcTypeName)) {
            importUtils.checkIsNull(result, importQcLimit.getCheckItemStr(), "检查项", failStr);
            judgingMethods = Arrays.asList(EnumBase.EnumJudgingMethod.相对偏差.name(), EnumBase.EnumJudgingMethod.范围判定.name());
            // 评判方式填写为“范围判定”的时候，无需填写计算公式（不导入）
            if (EnumBase.EnumJudgingMethod.范围判定.name().equals(importQcLimit.getJudgeMethod())) {
                importQcLimit.setFormula("");
                if (StringUtil.isNotEmpty(importQcLimit.getAllowLimit())) {
                    result.setSuccess(false);
                    failStr.append("；").append("不需要导入允许限值。");
                }
                if (StringUtil.isNotEmpty(importQcLimit.getFormula())) {
                    result.setSuccess(false);
                    failStr.append("；").append("不需要导入计算公式。");
                }
            } else {
                importUtils.checkIsNull(result, importQcLimit.getAllowLimit(), "允许限值", failStr);
            }
            if (StringUtil.isNotEmpty(importQcLimit.getStandard())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入标准物质加入量范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getRangeConfig())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入检查项范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getSubstituteName())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入替代物。");
            }
            if (!EnumLIM.EnumCheckItemType.出证结果.name().equals(importQcLimit.getCheckItemStr())) {
                importQcLimit.setCheckItemOther(importQcLimit.getCheckItemStr());
                importQcLimit.setCheckItem(EnumLIM.EnumCheckItemType.公式参数.getValue());
            } else {
                importQcLimit.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.getValue());
            }
        } else if (EnumLIM.EnumQCType.加标.name().equals(qcTypeName) || EnumLIM.EnumQCType.空白加标.name().equals(qcTypeName)) {
            importUtils.checkIsNull(result, importQcLimit.getAllowLimit(), "允许限值", failStr);
            judgingMethods = Collections.singletonList(EnumBase.EnumJudgingMethod.回收率.name());
            // 是否设置检查项
            if (StringUtil.isEmpty(importQcLimit.getRangeConfig())) {
                importQcLimit.setIsCheckItem(0);
                importQcLimit.setCheckItem(-1);
            }
            if (StringUtil.isNotEmpty(importQcLimit.getCheckItemStr())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要检查项。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getStandard())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入标准物质加入量范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getSubstituteName())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入替代物。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getFormula())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入计算公式。");
            }

        } else if (EnumLIM.EnumQCType.曲线校核.name().equals(qcTypeName)) {
            importUtils.checkIsNull(result, importQcLimit.getCheckItemStr(), "检查项", failStr);
            importUtils.checkIsNull(result, importQcLimit.getAllowLimit(), "允许限值", failStr);
            importUtils.checkIsNull(result, importQcLimit.getStandard(), "标准物质加入量范围", failStr);
            judgingMethods = Arrays.asList(EnumBase.EnumJudgingMethod.相对偏差.name(), EnumBase.EnumJudgingMethod.相对误差.name());
            if (!EnumLIM.EnumCheckItemType.出证结果.name().equals(importQcLimit.getCheckItemStr())) {
                importQcLimit.setCheckItemOther(importQcLimit.getCheckItemStr());
                importQcLimit.setCheckItem(EnumLIM.EnumCheckItemType.公式参数.getValue());
            } else {
                importQcLimit.setCheckItem(EnumLIM.EnumCheckItemType.出证结果.getValue());
            }

            if (StringUtil.isNotEmpty(importQcLimit.getRangeConfig())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入检查项范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getSubstituteName())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入替代物。");
            }
            importQcLimit.setRangeConfig(importQcLimit.getStandard());

        } else if (EnumLIM.EnumQCType.替代物.name().equals(qcTypeName)) {
            importUtils.checkIsNull(result, importQcLimit.getAllowLimit(), "允许限值", failStr);
            importUtils.checkIsNull(result, importQcLimit.getSubstituteName(), "替代物名称", failStr);
            judgingMethods = Collections.singletonList(EnumBase.EnumJudgingMethod.回收率.name());
            // 检查替代物
            checkSubstitute(result, failStr, importQcLimit, allTestList, allSubstituteList);

            if (StringUtil.isNotEmpty(importQcLimit.getCheckItemStr())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要检查项。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getStandard())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入标准物质加入量范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getRangeConfig())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入检查项范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getFormula())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入计算公式。");
            }

        } else if (EnumLIM.EnumQCType.阴性对照试验.name().equals(qcTypeName) || EnumLIM.EnumQCType.阳性对照试验.name().equals(qcTypeName)) {
            importUtils.checkIsNull(result, importQcLimit.getAllowLimit(), "允许限值", failStr);
            judgingMethods = Collections.singletonList(EnumBase.EnumJudgingMethod.限值判定.name());
            if (StringUtil.isNotEmpty(importQcLimit.getCheckItemStr())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要检查项。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getStandard())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入标准物质加入量范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getRangeConfig())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入检查项范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getSubstituteName())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入替代物。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getFormula())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入计算公式。");
            }
        } else if (EnumLIM.EnumQCType.校正系数检验.name().equals(qcTypeName)) {
            importUtils.checkIsNull(result, importQcLimit.getAllowLimit(), "允许限值", failStr);
            judgingMethods = Arrays.asList(EnumBase.EnumJudgingMethod.相对偏差.name(), EnumBase.EnumJudgingMethod.相对误差.name());
            if (StringUtil.isNotEmpty(importQcLimit.getCheckItemStr())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要检查项。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getStandard())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入标准物质加入量范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getRangeConfig())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入检查项范围。");
            }
            if (StringUtil.isNotEmpty(importQcLimit.getSubstituteName())) {
                result.setSuccess(false);
                failStr.append("；").append("不需要导入替代物。");
            }
        }

        // 判定方式检查
        checkJudgingMethod(result, failStr, importQcLimit, judgingMethods);
        // 标准物质加入量范围、检查项范围、允许限值，需要进行正则验证；
        validateRangeLimit(result, failStr, "标准物质加入量范围", importQcLimit.getStandard());
        validateRangeLimit(result, failStr, "检查项范围", importQcLimit.getRangeConfig());
        validateRangeLimit(result, failStr, "允许限值", importQcLimit.getAllowLimit());
        // 检查计算公式
        checkFormula(result, failStr, importQcLimit);
    }

    /**
     * 检查替代物属性
     *
     * @param result        校验结果
     * @param failStr       校验错误数据
     * @param importQcLimit 导入行数据
     * @param allTestList   测试项目
     * @param allSubstitute 替代物
     */
    private void checkSubstitute(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportQualityControlLimit importQcLimit, List<DtoTest> allTestList, List<DtoSubstitute> allSubstitute) {

        if (StringUtil.isNotEmpty(importQcLimit.getTestId())) {
            Optional<DtoTest> testOptional = allTestList.stream().filter(p -> p.getId().equals(importQcLimit.getTestId())).findFirst();
            if (testOptional.isPresent() && !testOptional.get().getIsTotalTest()) {
                result.setSuccess(false);
                failStr.append("；").append("替代物只能添加到“是否总称”为“是”的测试项目上");
            }
        }

        List<DtoSubstitute> sub = allSubstitute.stream().filter(p -> p.getCompoundName().equals(importQcLimit.getSubstituteName())).collect(Collectors.toList());
        if (StringUtil.isEmpty(sub)) {
            result.setSuccess(false);
            failStr.append("；").append("替代物不存在");
        }
    }

    /**
     * 检查计算公式
     *
     * @param result        校验结果
     * @param failStr       校验错误数据
     * @param importQcLimit 导入行数据
     */
    private void checkFormula(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportQualityControlLimit importQcLimit) {
        if (StringUtil.isNotEmpty(importQcLimit.getFormula())) {
            List<String> formulas = formulaList.stream().filter(p -> importQcLimit.getFormula().equals(p)).collect(Collectors.toList());
            if (StringUtil.isEmpty(formulas)) {
                result.setSuccess(false);
                failStr.append("；").append("计算公式不正确");
            }
        }
    }

    /**
     * 检查判定方式
     *
     * @param result         校验结果
     * @param failStr        校验错误数据
     * @param importQcLimit  导入行数据
     * @param judgingMethods 评判方式集合
     */
    private void checkJudgingMethod(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportQualityControlLimit importQcLimit, List<String> judgingMethods) {
        List<String> juds = judgingMethods.stream().filter(p -> p.equals(importQcLimit.getJudgeMethod())).collect(Collectors.toList());
        if (StringUtil.isEmpty(juds)) {
            result.setSuccess(false);
            failStr.append("；").append(importQcLimit.getQcTypeName()).append("评判方式不正确");
        }
    }


    /**
     * 检验质控限值的数值范围和允许限值格式是否正确
     *
     * @param result    校验结果
     * @param failStr   校验错误数据
     * @param resultStr 校验结果
     * @param value     质控限值配置对象
     * @return 校验是否通过
     */
    private void validateRangeLimit(ExcelVerifyHandlerResult result, StringBuilder failStr, String resultStr, String value) {
        boolean validate = true;
        if (StringUtil.isNotEmpty(value) && !validateFormat(value)) {
            validate = false;
        }
        if (!validate) {
            result.setSuccess(false);
            failStr.append("；").append(resultStr).append("数值范围或允许限值格式有误");
        }
    }

    /**
     * 判断给定字符串是否满足允许限值的配置格式(正确的格式示例："[x] > 5" , "[x] >= 6 and [x] <= 10")
     *
     * @param s 给定字符串
     * @return 校验是否通过
     */
    private boolean validateFormat(String s) {
        s = s.replace(" ", "");
        if (StringUtil.isNotEmpty(s)) {
            if (s.contains("and")) {
                String[] arr = s.split("and");
                return arr.length == 2 && validateFormat(arr[0]) && validateFormat(arr[1]);
            } else {
                if (s.startsWith("[x]") && DivationUtils.cntSubStr(s, "[x]") == 1) {
                    s = s.replace("[x]", "");
                    if (s.startsWith("<=") || s.startsWith(">=")) {
                        s = s.substring(2);
                    } else if (s.startsWith("<") || s.startsWith(">")) {
                        s = s.substring(1);
                    }
                    if (s.contains("c") || s.contains("d")) {
                        return true;
                    } else {
                        return DivationUtils.isNumber(s);
                    }
                }
            }
        }
        return false;
    }
}
