package com.sinoyd.lims.pro.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jsoniter.JsonIterator;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.ProjectTypeService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoProjectPlan;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.customer.DtoProjectJson;
import com.sinoyd.lims.pro.dto.customer.DtoReportDetailQuery;
import com.sinoyd.lims.pro.dto.customer.DtoSampleTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.ProjectPlanRepository;
import com.sinoyd.lims.pro.repository.ProjectRepository;
import com.sinoyd.lims.pro.repository.ReportRepository;
import com.sinoyd.lims.pro.repository.StatusForProjectRepository;
import com.sinoyd.lims.pro.service.ProjectCacheService;
import com.sinoyd.lims.pro.service.ReportDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 处理项目的一些字段的缓存数据
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/11
 * @since V100R001
 */
@Service
public class ProjectCacheServiceImpl implements ProjectCacheService {

    //#region 注入方式
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private ReportDetailService reportDetailService;

    @Autowired
    private StatusForProjectRepository statusForProjectRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectPlanRepository projectPlanRepository;

    @Autowired
    @Lazy
    private ProjectTypeService projectTypeService;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    private ReportRepository reportRepository;
    //#endregion

    /**
     * 自增初始值
     */
    private final long INITIAL_VALUE = 1L;

    @Transactional(rollbackFor = Exception.class)
    @Async
    @Override
    public void updateProjectSampleJson(String projectId, CurrentPrincipalUser principalContextUser) {
        //重新赋值登录的账号信息
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(principalContextUser,
                null, authorities));
        updateProjectSampleJson(projectId);
    }

    @Override
    public void updateProjectReportJson(String projectId, CurrentPrincipalUser principalContextUser) {
        //重新赋值登录的账号信息
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(principalContextUser,
                null, authorities));
        updateProjectReportJson(projectId);
    }


    /**
     * 更新项目上样品的字段
     * @param projectId 项目id
     */
    private void updateProjectSampleJson(String projectId) {
        String key = EnumPRO.EnumPRORedis.getRedisKey(EnumPRO.EnumPRORedis.PRO_OrgId_ProjectSampleJsonTemp.getValue()) + projectId;
        //先进行自增，若原先没有该key，则自增后值为1
        long value = redisTemplate.opsForValue().increment(key, 1);
        redisTemplate.expire(key, 1, TimeUnit.MINUTES);
        //表明之前没有，刚刚自增到1
        if (value == INITIAL_VALUE) {
            while (true) {
                //清除缓存数据，保证读取的数据是最新的
                commonRepository.clear();
                DtoReportDetailQuery queryDto = new DtoReportDetailQuery();
                queryDto.setProjectId(projectId);
                List<DtoSampleTemp> sampleList = reportDetailService.query(queryDto);
                //未采样品数
                Integer notSampled = 0;
                //出证样品数
                Integer certificated = 0;
                //未检毕样品数
                Integer monitoring = 0;
                //已检毕样品数
                Integer monitored = 0;

                for (DtoSampleTemp sample : sampleList) {
                    if (sample.getSamplingStatus().equals(EnumPRO.EnumSamplingStatus.需要取样还未取样.getValue())) {
                        notSampled++;
                    }
                    if (sample.getStatus().equals(EnumPRO.EnumMonitorType.已检毕.toString())) {
                        if (sample.getCertificateStatus().equals(EnumPRO.EnumCertificateType.已出证.toString())) {
                            certificated++;
                        }
                        monitored++;
                    } else {
                        monitoring++;
                    }
                }
                DtoProject project = projectRepository.findOne(projectId);
                DtoProjectJson projectJson = StringUtils.isNotNullAndEmpty(project.getJson()) ?
                        JsonIterator.deserialize(project.getJson(), DtoProjectJson.class) : new DtoProjectJson();
                projectJson.setNotSampled(notSampled);
                projectJson.setAnalyzeSummary(String.format("%d/%d", monitored, sampleList.size()));
                Integer[] analyzeDetail = {certificated, monitored, monitoring};
                projectJson.setAnalyzeDetail(analyzeDetail);

                String projectType = projectTypeService.getConfigValue(project.getProjectTypeId(), "projectRegisterPage");
                //目前的逻辑对开展中的才进行核对
                if ((projectType.equals(EnumPRO.EnumProjectType.委托类.getValue()) || EnumPRO.EnumProjectType.例行类.getValue().equals(projectType) || projectType.equals(EnumPRO.EnumProjectType.全流程.getValue())) && project.getStatus().equals(EnumPRO.EnumProjectStatus.开展中.toString())) {
                    DtoProjectPlan plan = projectPlanRepository.findByProjectId(projectId);
                    if (plan.getIsMakePlan()) {
                        if (project.getSamplingStatus().equals(EnumPRO.EnumSampledStatus.已采毕.getValue()) && notSampled > 0) {
                            projectRepository.updateSamplingStatus(projectId, EnumPRO.EnumSampledStatus.未采毕.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                            statusForProjectRepository.updateStatusByProjectIdAndModule(project.getId(), EnumPRO.EnumStatus.待处理.getValue(), EnumLIM.EnumProjectModule.采样准备.getCode(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                        } else if (project.getSamplingStatus().equals(EnumPRO.EnumSampledStatus.未采毕.getValue()) && notSampled.equals(0)) {
                            projectRepository.updateSamplingStatus(projectId, EnumPRO.EnumSampledStatus.已采毕.getValue(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                            statusForProjectRepository.updateStatusByProjectIdAndModule(project.getId(), EnumPRO.EnumStatus.已处理.getValue(), EnumLIM.EnumProjectModule.采样准备.getCode(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                        }
                    }
                }
                //写入json信息
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    projectRepository.updateJson(projectId, objectMapper.writeValueAsString(projectJson));
                } catch (Exception ex) {
                    throw new BaseException("更新Project Sample Json失败");
                }
                if (!redisTemplate.hasKey(key)) {
                    break;
                } else if (String.valueOf(redisTemplate.opsForValue().get(key)).equals(String.valueOf(INITIAL_VALUE))) {
                    //先移除该key
                    redisTemplate.delete(key);
                    break;
                } else {
                    redisTemplate.opsForValue().set(key, INITIAL_VALUE);
                    redisTemplate.expire(key, 1, TimeUnit.MINUTES);
                }
            }
        }
    }


    /**
     * 更新项目上报告的字段
     * @param projectId 项目id
     */
    private void updateProjectReportJson(String projectId){
        List<DtoReport> reportList = reportRepository.findByProjectId(projectId);
        DtoProject project = projectRepository.findOne(projectId);
        DtoProjectJson projectJson = JsonIterator.deserialize(project.getJson(), DtoProjectJson.class);
        Integer[] reportDetail = {reportList.stream().mapToInt(p -> p.getStatus().equals(EnumPRO.EnumReportState.已签发.toString()) ? 1 : 0).sum(),
                reportList.size()};
        projectJson.setReportDetail(reportDetail);

        //写入json信息
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            projectRepository.updateJson(projectId, objectMapper.writeValueAsString(projectJson));
        } catch (Exception ex) {
            throw new BaseException("更新Project Report Json失败");
        }
    }
}
