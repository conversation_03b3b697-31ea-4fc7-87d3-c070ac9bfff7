package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.pro.dto.DtoContractCollectionPlan;
import com.sinoyd.lims.pro.dto.DtoRecAndPayRecord;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.repository.ContractCollectionPlanRepository;
import com.sinoyd.lims.pro.repository.RecAndPayRecordRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.pro.dto.DtoOrderContract;
import com.sinoyd.lims.pro.repository.OrderContractRepository;
import com.sinoyd.lims.pro.service.OrderContractService;
import com.sinoyd.lims.pro.service.SerialNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * OrderContract操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
@Service
public class OrderContractServiceImpl extends BaseJpaServiceImpl<DtoOrderContract, String, OrderContractRepository> implements OrderContractService {

    private PersonService personService;

    private RecAndPayRecordRepository recAndPayRecordRepository;

    private ContractCollectionPlanRepository contractCollectionPlanRepository;

    @Autowired
    @Qualifier("orderContract")
    @Lazy
    private SerialNumberService serialNumberService;

    @Override
    public void findByPage(PageBean<DtoOrderContract> page, BaseCriteria criteria) {
        page.setEntityName("DtoOrderContract a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        fillingTransient(page.getData());
    }

    @Override
    public DtoOrderContract findOne(String id) {
        DtoOrderContract result = super.findOne(id);
        fillingTransient(Stream.of(result).collect(Collectors.toList()));
        return result;
    }

    @Override
    @Transactional
    public DtoOrderContract save(DtoOrderContract contract) {
        verify(contract);
        //判断是否为新增 新增才需对标号做处理
        if (repository.findOne(contract.getId()) == null) {
            // 判断当前保存的编号是否与当前应该新增的流水一致，不一致则证明手动修改编号，流水不往后加
            if (contract.getContractCode().equals(serialNumberService.createNewNumber())) {
                serialNumberService.createNewNumber(true);
            }
        }
        return super.save(contract);
    }

    @Override
    @Transactional
    public DtoOrderContract update(DtoOrderContract contract) {
        verify(contract);
        return super.update(contract);
    }

    @Override
    public DtoOrderContract findAttachPath(String id) {
        return super.findOne(id);
    }

    @Override
    public List<DtoOrderContract> findByOrderIds(Collection<String> orderIds) {
        return repository.findByOrderIdIn(orderIds);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<?> ids) {
        //级联删除收款计划及收款记录
        List<DtoContractCollectionPlan> planList = contractCollectionPlanRepository.findAllByContractIdIn((List<String>) ids);
        contractCollectionPlanRepository.delete(planList);
        List<DtoRecAndPayRecord> receiveList = recAndPayRecordRepository.findAllByContractIdIn((List<String>) ids);
        recAndPayRecordRepository.delete(receiveList);
        return super.logicDeleteById(ids);
    }

    /**
     * 验证合同编码是否重复
     *
     * @param contract
     */
    private void verify(DtoOrderContract contract) {
        if (repository.countByContractCodeAndIdNotAndIsDeletedFalse(contract.getContractCode(), contract.getId()) > 0) {
            throw new BaseException("合同编码重复");
        }
    }

    /**
     * 填充冗余字段
     *
     * @param contractList 合同列表
     */
    private void fillingTransient(List<DtoOrderContract> contractList) {
        List<DtoPerson> people = personService.findAll();
        List<String> contractIdList = contractList.stream().map(DtoOrderContract::getId).collect(Collectors.toList());
        List<DtoRecAndPayRecord> recAndPayRecordList = recAndPayRecordRepository.findAllByContractIdIn(contractIdList);
        contractList.forEach(c -> {
            List<String> signPersonIds = Arrays.asList(c.getSignPersonId().split(","));
            String signPerson = people.stream().filter(p -> signPersonIds.contains(p.getId())).map(DtoPerson::getCName).collect(Collectors.joining(","));
            c.setSignPersonName(signPerson);
            //计算收款进度
            calProgress(c, recAndPayRecordList);
        });
    }

    private void calProgress(DtoOrderContract c, List<DtoRecAndPayRecord> recAndPayRecordList) {
        //“合同中收款金额”：①类型为“收款”②状态为“已到款”的记录中“开票金额”总和；③保留1位小数；
        List<DtoRecAndPayRecord> contractReceiveList = recAndPayRecordList.stream().filter(r -> c.getId().equals(r.getContractId()) && Boolean.TRUE.equals(r.getIsReceive()) && EnumLIM.EnumMoneyType.收款.getValue().equals(r.getMoneyType()))
                .collect(Collectors.toList());
        BigDecimal receiveSum = new BigDecimal("0.0");
        for (DtoRecAndPayRecord receive : contractReceiveList) {
            receiveSum = receiveSum.add(receive.getAmount());
        }
        c.setRecAmount(receiveSum);
        c.setNotRecAmount(c.getTotalAmount().subtract(receiveSum));
        //BUG2023122299084 未收款金额不应该是负数。当已收款金额大于等于合同额的时候，未收款金额就恒等于0。
        if (BigDecimal.ZERO.compareTo(c.getNotRecAmount()) > 0) {
            c.setNotRecAmount(BigDecimal.ZERO);
        }
        double progress = 0;
        if (c.getTotalAmount().compareTo(BigDecimal.ZERO) != 0) {
            progress = receiveSum.multiply(new BigDecimal("100")).divide(c.getTotalAmount(), 1, RoundingMode.HALF_UP).doubleValue();
        }
        c.setReceiveProgress(progress);
    }

    @Override
    @Transactional
    public String generateOrderContractCode() {
        return serialNumberService.createNewNumber();
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    public void setRecAndPayRecordRepository(RecAndPayRecordRepository recAndPayRecordRepository) {
        this.recAndPayRecordRepository = recAndPayRecordRepository;
    }

    @Autowired
    public void setContractCollectionPlanRepository(ContractCollectionPlanRepository contractCollectionPlanRepository) {
        this.contractCollectionPlanRepository = contractCollectionPlanRepository;
    }
}
