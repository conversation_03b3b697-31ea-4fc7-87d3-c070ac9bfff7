package com.sinoyd.lims.lim.data.mapper;

import com.sinoyd.lims.lim.dto.customer.DtoExportParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsFormula;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * 测试项目公式实体转导入导出实体映射接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/12/7
 * @since V100R001
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ParamsFormulaMapper {


    /**
     * DtoParamsFormula 实例转换成DtoExportParamsFormula实例
     *
     * @param paramsFormula 分析项目实体
     * @return DtoExportParamsFormula 实例
     */
    @Mapping(source = "configDate", target = "configDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "orignFormulatType", target = "orignFormulatType")
    @Mapping(source = "objectType", target = "objectType")
    @Mapping(source = "validate", target = "validate")
    @Mapping(source = "usageNum", target = "usageNum")
    DtoExportParamsFormula toExportParamsFormula(DtoParamsFormula paramsFormula);

    /**
     * DtoParamsFormula 实例集合转换成DtoExportParamsFormula 实例集合
     *
     * @param paramsFormulaList 分析项目实例集合
     * @return DtoExportParamsFormula 实例集合
     */
    @InheritConfiguration(name = "toExportParamsFormula")
    List<DtoExportParamsFormula> toExportParamsFormulaList(List<DtoParamsFormula> paramsFormulaList);


    /**
     * DtoExportParamsFormula 实例转换成DtoParamsFormula 实例
     *
     * @param exportParamsFormula 分析项目实体
     * @return DtoExportParamsFormula 实例
     */
    @Mapping(source = "configDate", target = "configDate",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "orignFormulatType", target = "orignFormulatType")
    @Mapping(source = "objectType", target = "objectType")
    @Mapping(source = "validate", target = "validate")
    @Mapping(source = "usageNum", target = "usageNum")
    DtoParamsFormula toDtoParamsFormula(DtoExportParamsFormula exportParamsFormula);

    /**
     * DtoExportParamsFormula 实例集合转换成DtoParamsFormula 实例集合
     *
     * @param exportParamsFormulaList 分析项目导入导出实例集合
     * @return DtoParamsFormula 实例集合
     */
    @InheritConfiguration(name = "toDtoParamsFormula")
    List<DtoParamsFormula> toDtoParamsFormulaList(List<DtoExportParamsFormula> exportParamsFormulaList);

}
