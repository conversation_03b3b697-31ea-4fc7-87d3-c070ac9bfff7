package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;


/**
 * reportrecover2report实体
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="reportrecover2report")
 @Data
 public  class ReportRecover2Report implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 回收id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("回收id")
	private String recoverId;
    
    /**
    * 报告id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("报告id")
	private String reportId;
    
 }