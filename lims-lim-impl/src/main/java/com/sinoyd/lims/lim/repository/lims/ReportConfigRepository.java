package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.base.repository.base.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;


/**
 * ReportConfig数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/13
 * @since V100R001
 */
public interface ReportConfigRepository extends IBaseJpaRepository<DtoReportConfig, String>, LimsRepository<DtoReportConfig, String> {

    /**
     * 根据报表编码查询
     *
     * @param reportCode 报表编码
     * @return 返回数据
     */
    List<DtoReportConfig> findByReportCodeAndIdNot(String reportCode, String id);


    /**
     * 根据报表编码获取数据
     *
     * @param reportCode 报表编码
     * @return 返回数据
     */
    List<DtoReportConfig> findByReportCode(String reportCode);

    /**
     * 根据报表编码获取数据
     *
     * @param reportCodes 报表编码
     * @return 返回数据
     */
    List<DtoReportConfig> findByReportCodeIn(List<String> reportCodes);

    /**
     * 根据id查询（忽略假删条件）
     *
     * @param ids 主键集合
     * @return 报表配置实体集合
     */
    @Query(value = "select * from TB_LIM_ReportConfig where id in :ids ", nativeQuery = true)
    List<DtoReportConfig> findAllWithDeleted(@Param("ids") Collection<String> ids);

    /**
     * 查询全部记录（忽略假删条件）
     *
     * @return 报表配置实体集合
     */
    @Query(value = "select * from TB_LIM_ReportConfig ", nativeQuery = true)
    List<DtoReportConfig> findAllWithDeleted();

}