package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.customer.DtoRecordConfigTest;

import java.util.List;


/**
 * 原始记录单配置
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
public interface RecordConfigService extends IBaseJpaService<DtoRecordConfig, String> {

    /**
     * 根据测试项目获取相关的记录单
     *
     * @param testIds 测试项目ids
     * @return 返回数据
     */
    List<DtoRecordConfigTest> findRecordConfigByTestIds(List<String> testIds);

    /**
     * 根据记录单配置id分页获取检测类型参数数据
     *
     * @param pageBean 分页数据
     * @param paramsCriteria 查询条件
     * @return 查询结果
     */
    void findParamsConfigByRecordId(PageBean<DtoParamsConfig> pageBean, BaseCriteria paramsCriteria);

    /**
     *  复制分析记录单配置
     *
     * @param recordConfig 数据载体
     */
    void copyRecordConfig(DtoRecordConfig recordConfig);
}