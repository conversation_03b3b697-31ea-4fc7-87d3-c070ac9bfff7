package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.OrderContract;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.math.BigDecimal;


/**
 * DtoOrderContract实体
 * <AUTHOR>
 * @version V1.0.0 2022/12/30
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_PRO_OrderContract")
 @Data
 @DynamicInsert
 public  class DtoOrderContract extends OrderContract {
   private static final long serialVersionUID = 1L;

   @Transient
   private String signPersonName;

    /**
     * 已收金额
     */
    @Transient
    private BigDecimal recAmount;

    /**
     * 未收金额
     */
    @Transient
    private BigDecimal notRecAmount;

    /**
     * 收款进度
     */
    @Transient
    private Double receiveProgress;
 }