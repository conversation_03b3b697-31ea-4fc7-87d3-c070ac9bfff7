package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;

/**
 * ocr对象检索
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OcrConfigRecordCriteria extends BaseCriteria {
    /**
     * 时间范围开始
     */
    private String dateBeginStr;

    /**
     * 时间范围结束
     */
    private String dateEndStr;

    /**
     * 操作人（id）
     */
    private String creatorId;

    /**
     * 关键字（样品编号)
     */
    private String key;

    /**
     * 分组名称)
     */
    private String groupName;

    /**
     * 对象标识
     */
    private String configId;

    /**
     * 对象名称
     */
    private String configName;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(configId)) {
            condition.append(" and configId = :configId");
            values.put("configId", configId);
        }
        if (StringUtils.isNotNullAndEmpty(configName)) {
            condition.append(" and exists (select 1 from DtoOcrConfig t1 where a.configId = t1.id and t1.configName like :configName  and t1.isDeleted = 0)");
            values.put("configName", "%" + configName + "%");
        }
        // 识别时间区间
        if (StringUtils.isNotNullAndEmpty(dateBeginStr)) {
            condition.append(" and createDate >= :dtBegin");
            values.put("dtBegin", DateUtil.stringToDate(this.dateBeginStr, DateUtil.YEAR));
        }
        if (StringUtils.isNotNullAndEmpty(dateEndStr)) {
            Date to = DateUtil.stringToDate(this.dateEndStr, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_YEAR, 1);
            condition.append(" and createDate < :dtEnd");
            values.put("dtEnd", c.getTime());
        }
        //操作人（id）
        if (StringUtils.isNotNullAndEmpty(creatorId)) {
            condition.append(" and creator = :creator");
            values.put("creator", creatorId);
        }
        //关键字（样品编号)
        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and (sampleCode like :key) ");
            values.put("key", "%" + key + "%");
        }
        // 分组名称
        if (StringUtils.isNotNullAndEmpty(groupName)) {
            condition.append(" and (groupName like :groupName) ");
            values.put("groupName", "%" + groupName + "%");
        }
        return condition.toString();
    }
}