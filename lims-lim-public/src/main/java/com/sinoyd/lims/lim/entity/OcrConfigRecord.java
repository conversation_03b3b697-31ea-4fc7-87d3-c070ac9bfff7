package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * OcrConfigRecord实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/04/28
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "OcrConfigRecord")
@Data
@EntityListeners(AuditingEntityListener.class)
public class OcrConfigRecord implements BaseEntity, Serializable {


    private static final long serialVersionUID = 1L;

    public OcrConfigRecord() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     *OCR对象标识
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String configId;

    /**
     *样品编号
     */
    @Column(length = 50,nullable = false)
    @Length(message = "样品编号{validation.message.length}", max = 50)
    private String sampleCode;

    /**
     *分组标识，多个用;隔开
     */
    @Column(length = 50,nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    private String groupId;

    /**
     *分组名称 多个用;隔开
     */
    @Column
    private String groupName;

    /**
     *  文件路径
     */
    @Column(length = 500,nullable=false)
    @ColumnDefault("''")
    @Length(message = "文件路径{validation.message.length}", max = 500)
    private String filePath;

    /**
     * ocr原始数据
     */
    @Column(columnDefinition="TEXT")
    private String originData;

    /**
     * ai回答结果
     */
    @Column(columnDefinition="TEXT")
    private String aiAnswer;

    /**
     * 是否删除
     */
    @Column(nullable=false)
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;


    /**
     * 组织机构id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 所属实验室
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 创建人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
