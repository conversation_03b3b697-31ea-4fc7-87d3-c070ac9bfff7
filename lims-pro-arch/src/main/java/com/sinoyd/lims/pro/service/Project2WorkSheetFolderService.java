package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoProject2WorkSheetFolder;

import java.util.List;

/**
 * Project2WorkSheetFolder操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface Project2WorkSheetFolderService extends IBaseJpaService<DtoProject2WorkSheetFolder, String> {
    /**
     * 核查后删除项目及检测单id
     *
     * @param projectId            项目id
     * @param workSheetFolderIds   检测单id集合
     * @param exceptSampleIds      排除的样品id集合
     * @param exceptAnalyseDataIds 排除的数据id集合
     */
    void removeByProjectIdAndWorkSheetFolderId(String projectId,
                                               List<String> workSheetFolderIds,
                                               List<String> exceptSampleIds,
                                               List<String> exceptAnalyseDataIds);

//    /**
//     * 核查检测单id
//     *
//     * @param workSheetFolderId 检测单id
//     */
//    void checkByWorkSheetFolderId(String workSheetFolderId);
}
