package com.sinoyd.lims.pro.repository;

import com.sinoyd.lims.pro.dto.DtoCostInfo;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * 费用数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface CostInfoRepository extends IBaseJpaRepository<DtoCostInfo, String> {
    /**
     * 判断是否存在同项目id的费用
     *
     * @param projectId 项目id
     * @return 同项目id的费用个数
     */
    Integer countByProjectId(String projectId);

    /**
     * 获取对应项目的费用
     *
     * @param projectId 项目id
     * @return 费用
     */
    DtoCostInfo findByProjectId(String projectId);

    /**
     * 修改费用方案变更状态
     *
     * @param id                 样品的ids
     * @param schemeChangeStatus 方案变更状态
     * @return 返回相应的修改信息
     */
    @Transactional
    @Modifying
    @Query("update DtoCostInfo c set c.schemeChangeStatus = :schemeChangeStatus where c.id = :id")
    Integer updateSchemeChangeStatus(@Param("id") String id,
                                     @Param("schemeChangeStatus") Integer schemeChangeStatus);

    /**
     * @param ids        样品ids
     * @param status     状态
     * @param modifier   修改人
     * @param modifyDate 修改时间
     * @return 返回更新信息
     */
    @Transactional
    @Modifying
    @Query("update DtoCostInfo c set c.status = :status,c.modifyDate = :modifyDate,c.modifier = :modifier  where c.id in :ids")
    Integer updateCostStatus(@Param("ids") List<String> ids, @Param("status") String status,
                               @Param("modifier") String modifier,
                               @Param("modifyDate") Date modifyDate);
}