package com.sinoyd.lims.monitor.repository.rcc;

import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPointExpend;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * FixedPointExpend数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedPointExpendRepository extends IBaseJpaPhysicalDeleteRepository<DtoFixedPointExpend, String> {

    /**
     * 通过点位id查询
     *
     * @param fixedPointId 点位id
     * @return 实体
     */
    DtoFixedPointExpend findByFixedPointId(String fixedPointId);

    /**
     * 根据点位id列表查询
     *
     * @param fixedPointIdList 点位id列表
     * @return 实体列表
     */
    List<DtoFixedPointExpend> findByFixedPointIdIn(List<String> fixedPointIdList);


}