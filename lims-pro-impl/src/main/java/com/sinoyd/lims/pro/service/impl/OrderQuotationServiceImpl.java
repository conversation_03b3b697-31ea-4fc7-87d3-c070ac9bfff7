package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.lims.pro.dto.DtoOrderQuotation;
import com.sinoyd.lims.pro.repository.OrderQuotationRepository;
import com.sinoyd.lims.pro.service.OrderFormService;
import com.sinoyd.lims.pro.service.OrderQuotationService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * OrderQuotation操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @Service
public class OrderQuotationServiceImpl extends BaseJpaServiceImpl<DtoOrderQuotation,String,OrderQuotationRepository> implements OrderQuotationService {

    @Autowired
    private OrderFormService orderFormService;

    @Override
    public void findByPage(PageBean<DtoOrderQuotation> pb, BaseCriteria orderQuotationCriteria) {
        pb.setEntityName("DtoOrderQuotation a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, orderQuotationCriteria);
    }

    @Transactional
    @Override
    public DtoOrderQuotation update(DtoOrderQuotation orderQuotation) {
        //修改费用详情
        super.update(orderQuotation);
        comRepository.flush();
        return orderFormService.updateQuotation(orderQuotation.getOrderId(), false, false);
    }


    @Override
    public List<DtoOrderQuotation> findByOrderIdIn(List<String> orderIds) {
        return repository.findByOrderIdIn(orderIds);
    }

    @Override
    public DtoOrderQuotation findByOrderId(String orderId) {
        return repository.findByOrderId(orderId);
    }
}