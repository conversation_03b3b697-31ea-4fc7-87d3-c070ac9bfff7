package com.sinoyd.lims.monitor.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.monitor.dto.rcc.DtoPointExtendData;

import java.util.Collection;
import java.util.List;

/**
 * 例行点位拓展字段数据访问操作接口
 *
 * <AUTHOR>
 * @version V5.2.0 2023/06/12
 * @since V100R001
 */
public interface PointExtendDataRepository extends IBaseJpaPhysicalDeleteRepository<DtoPointExtendData,String> {

    /**
     * 根据点位Id集合查询到所有的点位拓展数据
     *
     * @param fixedPointIds 点位Id集合
     * @return 点位拓展数据
     */
    List<DtoPointExtendData> findByFixedPointIdIn(Collection<String> fixedPointIds);

    /**
     * 根据点位Id查询到所有的点位拓展数据
     *
     * @param fixedPointId 点位Id
     * @return 点位拓展数据
     */
    List<DtoPointExtendData> findByFixedPointId(String fixedPointId);

    /**
     * 根据点位Id集合删除所有拓展数据
     *
     * @param fixedPointIds 点位Id集合
     * @return 删除的条数
     */
    Integer deleteByFixedPointIdIn(Collection<String> fixedPointIds);
}
