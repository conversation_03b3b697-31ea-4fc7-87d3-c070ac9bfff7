package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 质控评价信息
 *
 * <AUTHOR>
 * @version V1.0.0 2020/2/5
 * @since V100R001
 */
@Data
public class DtoQMEvaluation {
    /**
     * 数据id
     */
    private String id;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 盲样类型
     */
    private Integer blindType;

    /**
     * 样品状态
     */
    private String sampleStatus;

    /**
     * 父样id
     */
    private String parentSampleId = UUIDHelper.GUID_EMPTY;

    /**
     * 父样编号
     */
    private String parentSampleCode;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * 测试项目
     */
    private String redAnalyzeItemName;

    /**
     * 方法名称
     */
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    private String redCountryStandard;

    /**
     * 数据状态
     */
    private String status;

    /**
     * 分析人
     */
    private String analystName;

    /**
     * 复核人
     */
    private String checkerName = "";

    /**
     * 审核人
     */
    private String auditorName = "";

    /**
     * 检测结果修约
     */
    private BigDecimal testValueD;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 量纲
     */
    private String dimension;

    /**
     * 评价结果
     */
    private String analyseRemark = "";

    /**
     * 检测单编号
     */
    private String workSheetCode = "";

    /**
     * 仪器id
     */
    private String instrumentId = UUIDHelper.GUID_EMPTY;

    /**
     * 仪器名称
     */
    private String instrumentName = "";

    /**
     * 仪器编号
     */
    private String instrumentsCode = "";

    /**
     * 规格型号
     */
    private String model = "";

    /**
     * 质控类型
     */
    private Integer qmType;

    /**
     * 标准编号
     */
    private String qmCode = "";

    /**
     * 标准浓度
     */
    private String qmValue = "";

    /**
     * 范围
     */
    private String qmRange = "";

    /**
     * 加标体积
     */
    private String qmVolume = "";

    /**
     * 样值
     */
    private String stTestValue = "";

    /**
     * 加标回收率
     */
    private String qcRecoverRate = "";

    /**
     * 计量单位id
     */
    private String unitId;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 不确定度类型
     */
    private Integer uncertainType;

    /**
     * 标样区间范围低点
     */
    private String rangeLow;

    /**
     * 标样区间范围高点
     */
    private String rangeHigh;


    public DtoQMEvaluation() {

    }

    /**
     * 该构造函数主要用到 QualityManageServiceImpl 下面 findQMEvaluationListNotInFolder 方法，如果修改，对应的应用地方也要调整
     */
    public DtoQMEvaluation(String id, String testId, String sampleId, String sampleCode, Integer blindType, String sampleStatus, String parentSampleId, String redFolderName,
                           String redAnalyzeItemName, String redAnalyzeMethodName, String redCountryStandard, String dataStatus, String analystName, BigDecimal testValueD,
                           String testValue, String dimension, Integer qmType, String qmCode, String qmValue, String qmRange, String qmVolume, String stTestValue,
                           String itemName, String instrumentId, String unitId, String unit, Integer uncertainType) {
        this.id = id;
        this.testId = testId;
        this.sampleId = sampleId;
        this.sampleCode = sampleCode;
        this.blindType = blindType;
        this.sampleStatus = sampleStatus;
        this.parentSampleId = parentSampleId;
        this.redFolderName = redFolderName;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.status = dataStatus;
        this.analystName = analystName;
        this.testValueD = testValueD;
        this.testValue = testValue;
        this.dimension = dimension;
        this.qmType = qmType;
        this.qmCode = qmCode;
        if (StringUtils.isNotNullAndEmpty(itemName)) {
            this.qmCode = qmCode + "(" + itemName + ")";
        }
        this.qmValue = qmValue;
        this.qmRange = qmRange;
        this.instrumentId = instrumentId;
        this.qmVolume = qmVolume;
        this.stTestValue = stTestValue;
        this.unitId = unitId;
        this.unit = unit;
        this.uncertainType = uncertainType;
    }

    /**
     * 该构造函数主要用到 QualityManageServiceImpl 下面 findQMEvaluationListNotInFolder 方法，如果修改，对应的应用地方也要调整
     */
    public DtoQMEvaluation(String id, String testId, String sampleId, String sampleCode, Integer blindType, String sampleStatus, String parentSampleId, String redFolderName,
                           String redAnalyzeItemName, String redAnalyzeMethodName, String redCountryStandard, String dataStatus, String analystName, BigDecimal testValueD,
                           String testValue, String dimension, Integer qmType, String qmCode, String qmValue, String qmRange, String qmVolume, String stTestValue,
                           String itemName, String instrumentId, String unitId, String unit, Integer uncertainType,String rangeLow,String rangeHigh ) {
        this.id = id;
        this.testId = testId;
        this.sampleId = sampleId;
        this.sampleCode = sampleCode;
        this.blindType = blindType;
        this.sampleStatus = sampleStatus;
        this.parentSampleId = parentSampleId;
        this.redFolderName = redFolderName;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.status = dataStatus;
        this.analystName = analystName;
        this.testValueD = testValueD;
        this.testValue = testValue;
        this.dimension = dimension;
        this.qmType = qmType;
        this.qmCode = qmCode;
        if (StringUtils.isNotNullAndEmpty(itemName)) {
            this.qmCode = qmCode + "(" + itemName + ")";
        }
        this.qmValue = qmValue;
        this.qmRange = qmRange;
        this.instrumentId = instrumentId;
        this.qmVolume = qmVolume;
        this.stTestValue = stTestValue;
        this.unitId = unitId;
        this.unit = unit;
        this.uncertainType = uncertainType;
        this.rangeLow = rangeLow;
        this.rangeHigh = rangeHigh;
    }

    /**
     * 该构造函数主要用到 QualityManageServiceImpl 下面 findQMEvaluationListInFolder 方法，如果修改，对应的应用地方也要调整
     */
    public DtoQMEvaluation(String id, String testId, String sampleId, String sampleCode, Integer blindType, String sampleStatus, String parentSampleId, String redFolderName,
                           String redAnalyzeItemName, String redAnalyzeMethodName, String redCountryStandard, String dataStatus, String analystName, BigDecimal testValueD,
                           String testValue, String dimension, Integer qmType, String qmCode, String qmValue, String qmRange, String itemName, String instrumentId, String workSheetCode,
                           String checkerName, String auditorName, String qmVolume, String stTestValue, String unitId, String unit, Integer uncertainType) {
        this.id = id;
        this.testId = testId;
        this.sampleId = sampleId;
        this.sampleCode = sampleCode;
        this.blindType = blindType;
        this.sampleStatus = sampleStatus;
        this.parentSampleId = parentSampleId;
        this.redFolderName = redFolderName;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.status = dataStatus;
        this.analystName = analystName;
        this.testValueD = testValueD;
        this.testValue = testValue;
        this.dimension = dimension;
        this.qmType = qmType;
        this.qmCode = qmCode;
        if (StringUtils.isNotNullAndEmpty(itemName)) {
            this.qmCode = qmCode + "(" + itemName + ")";
        }
        this.qmValue = qmValue;
        this.qmRange = qmRange;
        this.instrumentId = instrumentId;
        this.workSheetCode = workSheetCode;
        this.checkerName = checkerName;
        this.auditorName = auditorName;
        this.qmVolume = qmVolume;
        this.stTestValue = stTestValue;
        this.unitId = unitId;
        this.unit = unit;
        this.uncertainType = uncertainType;
    }

    /**
     * 该构造函数主要用到 QualityManageServiceImpl 下面 findQMEvaluationListInFolder 方法，如果修改，对应的应用地方也要调整
     */
    public DtoQMEvaluation(String id, String testId, String sampleId, String sampleCode, Integer blindType, String sampleStatus, String parentSampleId, String redFolderName,
                           String redAnalyzeItemName, String redAnalyzeMethodName, String redCountryStandard, String dataStatus, String analystName, BigDecimal testValueD,
                           String testValue, String dimension, Integer qmType, String qmCode, String qmValue, String qmRange, String itemName, String instrumentId, String workSheetCode,
                           String checkerName, String auditorName, String qmVolume, String stTestValue, String unitId, String unit, Integer uncertainType,String rangeLow,String rangeHigh) {
        this.id = id;
        this.testId = testId;
        this.sampleId = sampleId;
        this.sampleCode = sampleCode;
        this.blindType = blindType;
        this.sampleStatus = sampleStatus;
        this.parentSampleId = parentSampleId;
        this.redFolderName = redFolderName;
        this.redAnalyzeItemName = redAnalyzeItemName;
        this.redAnalyzeMethodName = redAnalyzeMethodName;
        this.redCountryStandard = redCountryStandard;
        this.status = dataStatus;
        this.analystName = analystName;
        this.testValueD = testValueD;
        this.testValue = testValue;
        this.dimension = dimension;
        this.qmType = qmType;
        this.qmCode = qmCode;
        if (StringUtils.isNotNullAndEmpty(itemName)) {
            this.qmCode = qmCode + "(" + itemName + ")";
        }
        this.qmValue = qmValue;
        this.qmRange = qmRange;
        this.instrumentId = instrumentId;
        this.workSheetCode = workSheetCode;
        this.checkerName = checkerName;
        this.auditorName = auditorName;
        this.qmVolume = qmVolume;
        this.stTestValue = stTestValue;
        this.unitId = unitId;
        this.unit = unit;
        this.uncertainType = uncertainType;
        this.rangeLow = rangeLow;
        this.rangeHigh = rangeHigh;
    }
}
