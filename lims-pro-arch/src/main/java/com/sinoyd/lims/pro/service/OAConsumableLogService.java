package com.sinoyd.lims.pro.service;

import java.util.List;

import com.sinoyd.base.dto.lims.DtoConsumableDetail;
import com.sinoyd.base.dto.lims.DtoConsumableLog;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;

/**
 * 领料申请操作接口
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-02
 * @since V100R001
 */
public interface OAConsumableLogService {
    /**
     * 添加审批并启动流程
     * 
     * @param taskDto 参数实体
     * @return 流程实例ID
     */
    String startProcess(DtoOATaskCreate<List<DtoConsumableLog>> taskDto);

    /**
     * 查询任务详细信息
     * 
     * @param taskId 任务ID
     * @return
     */
    DtoOATaskDetail<List<DtoConsumableLog>, List<DtoConsumableDetail>> findOATaskDetail(String taskId);
}