package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.RecordConfig2ParamsConfigCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2ParamsConfig;
import com.sinoyd.lims.lim.service.RecordConfig2ParamsConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * RecordConfig2ParamsConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2022/6/27
 * @since V100R001
 */
@Api(tags = "示例: RecordConfig服务")
@RestController
@RequestMapping("api/lim/RecordConfig2ParamsConfig")
@Validated
public class RecordConfig2ParamsConfigController extends
        BaseJpaController<DtoRecordConfig2ParamsConfig,String, RecordConfig2ParamsConfigService> {

    /**
     * 根据采样单配置id获取已配置的参数数据
     *
     * @param criteria 查询条件
     * @return 结果
     */
    @ApiModelProperty(name = "根据采样单配置id获取已配置的参数数据",notes = "根据采样单配置id获取已配置的参数数据")
    @GetMapping
    public RestResponse<List<DtoRecordConfig2ParamsConfig>> findByRecordId(RecordConfig2ParamsConfigCriteria criteria){
        RestResponse<List<DtoRecordConfig2ParamsConfig>> response = new RestResponse<>();
        PageBean<DtoRecordConfig2ParamsConfig> pageBean = super.getPageBean();
        service.findByPage(pageBean,criteria);
        response.setData(pageBean.getData());
        response.setCount(pageBean.getRowsCount());
        response.setMsg(StringUtil.isNotEmpty(pageBean.getData()) ? "查询成功" : "未查询到数据");
        return response;
    }

    /**
     * 保存数据
     *
     * @param recordConfig 需要保存的数据
     * @return 保存的数据
     */
    @ApiModelProperty(name = "保存数据",notes = "保存数据")
    @PostMapping
    public RestResponse<List<DtoRecordConfig2ParamsConfig>> save(@Validated @RequestBody DtoRecordConfig recordConfig){
        RestResponse<List<DtoRecordConfig2ParamsConfig>> response = new RestResponse<>();
        response.setData(service.save(recordConfig));
        response.setMsg("保存成功");
        return response;
    }

    /**
     * 根据参数配置Id删除关联数据
     *
     * @param ids 参数配置id集合
     * @return 结果
     */
    @ApiModelProperty(name = "根据参数配置Id删除关联数据",notes = "根据参数配置Id删除关联数据")
    @DeleteMapping
    public RestResponse<Integer> deleteByParamsConfigIds(@RequestBody List<String> ids){
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.logicDeleteById(ids));
        response.setMsg("删除成功");
        return response;
    }
}
