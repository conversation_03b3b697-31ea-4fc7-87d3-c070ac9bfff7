package com.sinoyd.lims.pro.dto.customer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinoyd.lims.pro.dto.DtoQuotationDetail;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 方案结构
 * <AUTHOR>
 * @version V1.0.0 2019/11/23
 * @since V100R001
 */
@Data
public class DtoSampleFolderTemp {
    /**
     * 主键id
     */
    private String id;

    /**
     * 点位id
     */
    private String sampleFolderId;

    /**
     * 层级
     */
    private Integer depth;

    /**
     * 检测大类id
     */
    private String bigSampleTypeId;

    /**
     * 检测小类id
     */
    private String sampleTypeId;

    /**
     * 检测小类名称
     */
    private String sampleTypeName;

    /**
     * 标题
     */
    private String label;

    /**
     * 点位名称
     */
    private String watchSpot;

    /**
     * 周期
     */
    private Integer periodCount;

    /**
     * 次数
     */
    private Integer timePerPeriod;

    /**
     * 样品数
     */
    private Integer samplePeriod;

    /**
     * 样品个数
     */
    private Integer sampleNum;

    /**
     * 排序值
     */
    private int orderNum;

    /**
     * 例行任务点位id
     */
    private String fixedPointId;

    /**
     * 分包的指标id集合
     */
    private List<String> outsourcingItemIds = new ArrayList<>();

    /**
     * 分包的指标id集合
     */
    private List<String> samplingOutItemIds = new ArrayList<>();

    /**
     * 指标id集合
     */
    private List<String> itemIds = new ArrayList<>();

    /**
     * 指标map
     */
    @JsonIgnore
    private Map<String, DtoProjectTest> item = new HashMap<>();

    /**
     * 项目map
     */
    private Map<String, Map<String, Object>> scheme = new HashMap<>();

    /**
     * 子集
     */
    private List<DtoSampleFolderTemp> children = new ArrayList<>();

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    private String customerId;

    private String customerName;

    private List<DtoQuotationDetail> detailList;
}
