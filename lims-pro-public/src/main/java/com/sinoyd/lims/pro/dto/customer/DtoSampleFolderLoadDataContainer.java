package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import lombok.Data;

import java.util.List;

/**
 * 按样品录入（新） 数据加载容器
 *
 * <AUTHOR>
 * @version ：v1.0.0
 * @date ：20240301
 */
@Data
public class DtoSampleFolderLoadDataContainer {
    /**
     *  公共参数
     */
    private List<DtoParamsConfig> commonParamList;

    /**
     * 点位集合
     */
    private List<DtoSampleFolder> sampleFolderList;
}
