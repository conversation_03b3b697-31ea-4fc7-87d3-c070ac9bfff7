package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoProject2Report;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * 项目和报告关联关系操作接口
 * <AUTHOR>
 * @version V1.0.0 2023/04/25
 * @since V100R001
 */
public interface Project2ReportService extends IBaseJpaService<DtoProject2Report, String> {

    /**
     * 根据项目id查询project2Report
     *
     * @param projectId 项目id
     */
    List<DtoProject2Report> findForProject(String projectId);

    /**
     * 根据报告id删除project2Report
     *
     * @param reportId 报告id
     */
    void deleteByReportId(String reportId);

    /**
     * 上传电子报告
     *
     * @param request 请求体
     */
    DtoProject2Report uploadElectronicReport(HttpServletRequest request);

    /**
     * 下载电子报告
     *
     * @param projectReportId 项目和报告关系id
     * @param response 响应体
     */
    String downloadElectronicReport(String projectReportId, HttpServletResponse response) throws IOException;


    /**
     * 清除电子报告
     *
     * @param projectReportId 项目和报告关系id
     */
    void clearElectronicReport(String projectReportId);
}