package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelation;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelationParams;
import com.sinoyd.lims.lim.service.AnalyzeItemRelationParamsService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分析项目关系参数
 * <AUTHOR> 修改：xuxb
 * @version V1.0.0 2019/1/31
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/analyzeItemRelationParams")
@Validated
public class AnalyzeItemRelationParamsController
        extends BaseJpaController<DtoAnalyzeItemRelationParams, String, AnalyzeItemRelationParamsService> {

    /**
     * 新增分析项目关系参数
     *
     * @param entity   分析项目关系
     * @return
     */
    @ApiOperation(value = "新增分析项目关系参数", notes = "新增分析项目关系参数")
    @PostMapping
    public RestResponse<String> create(@Validated @RequestBody DtoAnalyzeItemRelation entity) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.create(entity.getId(), entity.getAnalyzeItemIds());
        restResp.setCount(count);

        return restResp;
    }


    /**
     * 删除分析项目关系参数
     *
     * @param entity   分析项目关系
     * @return
     */
    @ApiOperation(value = "删除分析项目关系参数", notes = "删除分析项目关系参数")
    @PostMapping("/deleteParams")
    public RestResponse<String> deleteParams(@RequestBody DtoAnalyzeItemRelation entity) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.deleteParams(entity.getId(), entity.getAnalyzeItemIds());
        restResp.setCount(count);

        return restResp;
    }

//    /**
//     * 根据id删除分析项目关系参数
//     *
//     * @param id
//     * @return
//     */
//    @ApiOperation(value = "根据id删除分析项目关系参数", notes = "根据id删除分析项目关系参数")
//    @DeleteMapping("/{id}")
//    public RestResponse<String> delete(@PathVariable(name = "id") String id) {
//
//        RestResponse<String> restResp = new RestResponse<>();
//        restResp.setRestStatus(ERestStatus.SUCCESS);
//
//        Integer count = service.logicDeleteById(id);
//        restResp.setCount(count);
//
//        return restResp;
//    }
//
//    /**
//     * 批量删除分析项目关系参数
//     *
//     * @param ids
//     * @return
//     */
//    @ApiOperation(value = "批量删除分析项目关系参数", notes = "批量删除分析项目关系参数")
//    @DeleteMapping
//    public RestResponse<String> delete(@RequestBody List<String> ids) {
//
//        RestResponse<String> restResp = new RestResponse<>();
//        restResp.setRestStatus(ERestStatus.SUCCESS);
//
//        Integer count = service.logicDeleteById(ids);
//        restResp.setCount(count);
//
//        return restResp;
//    }

    /**
     * 通过关系id获取关系参数
     * 
     * @param relationId
     * @param sort
     * @return
     */
    @GetMapping
    public RestResponse<List<DtoAnalyzeItemRelationParams>> getList(String relationId, String sort) {

        RestResponse<List<DtoAnalyzeItemRelationParams>> restResp = new RestResponse<>();

        List<DtoAnalyzeItemRelationParams> params = service.getList(relationId, sort);

        restResp.setRestStatus(StringUtil.isEmpty(params) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(params);
        restResp.setCount(params.size());

        return restResp;
    }
}
