package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoNewSearchPlan;

import java.util.Map;

/**
 * 查新计划接口
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
public interface NewSearchPlanService extends IBaseJpaService<DtoNewSearchPlan, String> {

    /**
     * 提交查新计划
     *
     * @param id 查新计划主键id
     * @return 组装job实体
     */
    Map<String, Object> submit(String id);

    String deactivate(String id);
}
