package com.sinoyd.lims.lim.ocr;

import com.sinoyd.lims.lim.service.ocr.RecognizeStrategy;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NumberAfterParamInOneLine implements RecognizeStrategy {
    @Override
    public String recognize(List<String> data, String paramName) {
        String textWithParam = data.stream().filter(d->d.indexOf(paramName)!=-1).findFirst().orElse(null);
        if(textWithParam!=null){
            textWithParam = textWithParam.substring(textWithParam.indexOf(paramName)).trim();
            Pattern pattern = Pattern.compile("[0-9]+(\\.[0-9]+)?");
            Matcher matcher = pattern.matcher(textWithParam);
            if (matcher.find()) {
                return matcher.group();
            }
        }
        return "";
    }
}
