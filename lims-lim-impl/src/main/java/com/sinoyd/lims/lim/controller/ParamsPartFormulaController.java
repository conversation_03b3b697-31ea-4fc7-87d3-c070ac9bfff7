package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ParamsPartFormulaCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.service.ParamsPartFormulaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数部分公式管理控制器
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "参数部分公式管理: 参数部分公式管理服务")
@RestController
@RequestMapping("/api/lim/paramsPartFormula")
@Validated
public class ParamsPartFormulaController extends BaseJpaController<DtoParamsPartFormula, String, ParamsPartFormulaService> {

    /**
     * 分页查询参数部分公式
     *
     * @param paramsPartFormulaCriteria
     * @return 参数部分公式List
     */
    @ApiOperation(value = "分页动态条件获取参数部分公式", notes = "分页动态条件获取参数部分公式")
    @GetMapping("")
    public RestResponse<List<DtoParamsPartFormula>> findByPage(ParamsPartFormulaCriteria paramsPartFormulaCriteria)
    {
        RestResponse<List<DtoParamsPartFormula>> restResponse = new RestResponse<>();
        PageBean<DtoParamsPartFormula> pageBean = super.getPageBean();
        service.findByPage(pageBean, paramsPartFormulaCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());

        return restResponse;
    }

    /**
     * 根据id获取参数部分公式
     * 
     * @param id 参数部分公式id
     * @return 参数部分公式实体
     */
    @ApiOperation(value = "按主键获取参数部分公式", notes = "按主键获取参数部分公式")
    @GetMapping("/{id}")
    public RestResponse<DtoParamsPartFormula> getById(@PathVariable String id)
    {
        RestResponse<DtoParamsPartFormula> restResponse = new RestResponse<>();
        DtoParamsPartFormula params = service.findOne(id);
        restResponse.setData(params);
        restResponse.setRestStatus(StringUtil.isNull(params) ?
        ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        
        return restResponse;
    }

    /**
     * 新增参数部分公式
     * 
     * @param params 参数部分公式实体
     * @return 新增的参数部分公式实体
     */
    @ApiOperation(value = "新增参数部分公式", notes = "新增参数部分公式")
    @PostMapping("")
    public RestResponse<DtoParamsPartFormula> create(@Validated @RequestBody DtoParamsPartFormula params)
    {
        RestResponse<DtoParamsPartFormula> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParamsPartFormula data = service.save(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新参数部分公式
     * 
     * @param params 参数部分公式实体
     * @return 更新后的参数部分公式实体
     */
    @ApiOperation(value = "更新参数部分公式", notes = "更新参数部分公式")
    @PutMapping("")
    public RestResponse<DtoParamsPartFormula> update(@Validated @RequestBody DtoParamsPartFormula params)
    {
        RestResponse<DtoParamsPartFormula> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParamsPartFormula data = service.update(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     * 
     * @param id 参数部分公式id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数部分公式", notes = "根据id批量删除参数部分公式")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id)
    {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     * 
     * @param ids 参数部分公式ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数部分公式", notes = "根据id批量删除参数部分公式")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids)
    {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }
}