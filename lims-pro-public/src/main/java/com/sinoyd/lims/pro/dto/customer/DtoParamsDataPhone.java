package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import java.util.List;

@Data
public class DtoParamsDataPhone {

    /**
     * 参数配置id
     */
    private String paramsConfigId;

    /**
     * 参数名称
     */
    private String paramsName;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 参数数据
     */
    private String paramsValue;

    /**
     * 默认控件（枚举EnumDefaultControl:1.文本控件 2.日期控件 3.数字控件 4.下拉框控件 5.RadioGroup控件 6.CheckBoxGroup控件 7.日期时间控件 8.文本区域控件 9.时间控件）
     */
    private Integer defaultControl;

    /**
     * 数据源
     */
    private List<DtoDataSourcePhone> dataSource;

    /**
     * 量纲
     */
    private String dimension;

    /**
     * 分组id
     */
    private String groupId = UUIDHelper.GUID_EMPTY;
}
