package com.sinoyd.lims.webbackend.configuration;

import com.sinoyd.lims.pro.service.AnalyseDataCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @version V1.0.0 2020/4/14
 * @since V100R001
 */
@Component
@Configurable
@EnableScheduling
@EnableAsync
public class CronJobConfig {

    @Autowired
    private AnalyseDataCacheService analyseDataCacheService;

    /**
     * 每月1号凌晨刷新实验室分析缓存
     */
    @Async
    @Scheduled(cron = "0 30 0 1 * ?")
    public void refreshAnalyseCache() {
        analyseDataCacheService.refreshAnalyseCache();
    }
}
