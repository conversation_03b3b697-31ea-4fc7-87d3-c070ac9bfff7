package com.sinoyd.lims.pro.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collection;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * 快递报告关联查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年11月5日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExpressageInfo2ReportCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 快递id
    */
    private Collection expressageInfoIds;

    public void setExpressageInfoIds(Collection ids){
        this.expressageInfoIds = ids;
    }
    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and e2r.reportId = r.id");
        if (StringUtil.isNotEmpty(this.expressageInfoIds)) {
            condition.append(" and e2r.expressageInfoId in :expressageInfoIds");
            values.put("expressageInfoIds", this.expressageInfoIds);
        }
        return condition.toString();
    }
}