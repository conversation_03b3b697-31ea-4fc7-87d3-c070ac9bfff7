package com.sinoyd.lims.foreign;


import lombok.Data;

import java.util.Date;

/**
* 样品分组信息
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataSampleGroupVO {

    /**
     * id
     */
    private String id;

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 样品id
     */
    private String sampleId;

    /**
     * 样品分组名称
     */
    private String sampleTypeGroupName;

    /**
     * 分析项目名称
     */
    private String analyseItemNames;

    /**
     * 固定剂
     */
    private String fixer;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 前处理方式
     */
    private String pretreatmentMethod;

    /**
     * 采样体积
     */
    private String sampleVolume;

    /**
     * 保存条件
     */
    private String saveCondition;

    /**
     * 危险性描述
     */
    private String riskDescription;

    /**
     * 运输条件
     */
    private String transportationCondition;

    /**
     * 分组标识 1:按分组 2:全因子 3:单因子
     */
    private Integer isGroup;

    /**
     * 采样容器状态 EnumContainerStatus 1.完好无损 2.破损
     */
    private Integer containerStatus;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;
}
