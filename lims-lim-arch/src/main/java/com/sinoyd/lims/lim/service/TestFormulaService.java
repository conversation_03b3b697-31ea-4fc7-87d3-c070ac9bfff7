package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormula;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaTheSame;

import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 测试项目公式接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/12/09
 * @since V100R001
 */
public interface TestFormulaService {

    /**
     * 查询相应的测试项目公式数据
     *
     * @param pageBean     分页条件
     * @param baseCriteria 查询条件
     */
    void findByPage(PageBean<DtoTestFormula> pageBean, BaseCriteria baseCriteria);


    /**
     * 保存公式相关数据
     *
     * @param dtoTestFormula 公式数据
     * @return 保存相关数据
     */
    DtoTestFormula saveTestFormula(DtoTestFormula dtoTestFormula);

    /**
     * 修改公式数据
     *
     * @param dtoTestFormula 公式数据
     * @return 修改相关数据
     */
    DtoTestFormula updateTestFormula(DtoTestFormula dtoTestFormula);

    /**
     * 保存部分参数公式
     *
     * @param dtoTestFormula 公式数据
     * @param id             公式id
     */
    void saveParamsPartFormula(DtoTestFormula dtoTestFormula, String id);

    /**
     * 找到相应的公式数据
     *
     * @param id 公式id
     * @return 返回相应的数据
     */
    DtoTestFormula findOne(String id);


    /**
     * 删除相应公式
     *
     * @param ids 主键ids
     * @return 删除相应的数据
     */
    Integer delete(List<String> ids);


    /**
     * 是否是相同的测试项目公式
     *
     * @param dtoTestFormulaTheSame 相同公式的判断dto
     * @return 返回 true 或者 false
     */
    Boolean isSameTestFormula(DtoTestFormulaTheSame dtoTestFormulaTheSame);

    /**
     * 保存参数公式
     *
     * @param paramsPartFormula 部分参数公式数据
     * @return 保存参数公式数据
     */
    DtoParamsPartFormula saveParamsPartFormula(DtoParamsPartFormula paramsPartFormula);

    /**
     * 赋值公式数据
     *
     * @param formulaId    公式id
     * @param testIds      测试项目ids
     * @param sampleTypeId 检测类型id
     */
    void copyTestFormula(String formulaId, String sampleTypeId, List<String> testIds);

    /**
     * 设置分页查询实体名称（解决南通一体化多数据源冲突问题）
     *
     * @return 实体名称
     */
    String getFindByPageEntityName();

    /**
     * 获取EntityManager
     *
     * @return 实体管理器
     */
    EntityManager selectEntityManager();

    /**
     * 加载测试项目公式关联的测试项目信息
     *
     * @param formula 测试项目公式
     */
    void loadTestInfo4TestFormula(DtoTestFormula formula);

    /**
     * 公式导出
     *
     * @param pageBean            分页条件
     * @param testFormulaCriteria 筛选条件
     * @param response            响应体
     */
    void export(PageBean<DtoTestFormula> pageBean, BaseCriteria testFormulaCriteria, HttpServletResponse response);

    /**
     * 根据公式类型清空
     *
     * @param id          测试项目公式id
     * @param formulaType 公式类型
     */
    void removeFormulaByType(String id, Integer formulaType);
}
