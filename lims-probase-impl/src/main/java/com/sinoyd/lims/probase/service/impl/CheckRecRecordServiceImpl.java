package com.sinoyd.lims.probase.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.entity.ReceiveSubSampleRecord;
import com.sinoyd.lims.pro.entity.Sample;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.probase.repository.*;
import com.sinoyd.lims.probase.service.CheckRecRecordService;
import com.sinoyd.lims.probase.service.NewLogBaseService;
import com.sinoyd.lims.probase.service.StatusForRecordBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CheckRecRecordServiceImpl implements CheckRecRecordService {

    @Autowired
    private SampleBaseRepository sampleBaseRepository;

    @Autowired
    private AnalyseDataBaseRepository analyseDataBaseRepository;

    @Autowired
    private ReceiveSampleRecordBaseRepository receiveSampleRecordBaseRepository;

    @Autowired
    private ReceiveSubSampleRecordBaseRepository receiveSubSampleRecordBaseRepository;

    @Autowired
    private ReceiveSubSampleRecord2SampleBaseRepository receiveSubSampleRecord2SampleBaseRepository;

    @Autowired
    private CommonRepository commonRepository;

    @Autowired
    private NewLogBaseService newLogBaseService;

    @Autowired
    private StatusForRecordBaseService statusForRecordBaseService;

    @Transactional
    @Override
    public void checkReceiveSampleRecord(String sentId) {
        DtoReceiveSampleRecord receiveSampleRecord = receiveSampleRecordBaseRepository.findOne(sentId);
        checkReceiveSampleRecord(receiveSampleRecord);
    }

    @Transactional
    @Override
    public void checkReceiveSampleRecord(DtoReceiveSampleRecord objrsr) {
        try {
            String uid = "11111111-1111-1111-1111-111111111111";
            String cname = "系统";
            try {
                uid = PrincipalContextUser.getPrincipal().getUserId();
                cname = PrincipalContextUser.getPrincipal().getUserName();
            } catch (Exception ex) {

            }
            String sentId = objrsr.getId();

            //获取送样单相关样品
            List<DtoSample> samList = sampleBaseRepository.findByReceiveId(sentId);


            List<DtoReceiveSubSampleRecord> subList = receiveSubSampleRecordBaseRepository.findByReceiveId(sentId);

            List<String> recSubIds = subList.stream().map(ReceiveSubSampleRecord::getId).collect(Collectors.toList());

            //获取所有领样单相关样品
            List<DtoReceiveSubSampleRecord2Sample> record2SamList = receiveSubSampleRecord2SampleBaseRepository.findByReceiveSubSampleRecordIdIn(recSubIds);

            List<String> samIds = record2SamList.stream().map(DtoReceiveSubSampleRecord2Sample::getSampleId).distinct().collect(Collectors.toList());
            List<String> zksIds = sampleBaseRepository.findAll(samIds).stream().filter(p -> UUIDHelper.GUID_EMPTY.equals(p.getReceiveId())
                    && !p.getIsDeleted()).map(DtoSample::getId).distinct().collect(Collectors.toList());

            //获取送样单相关样品id
            List<String> allsids = samList.stream().map(Sample::getId).collect(Collectors.toList());
            allsids.addAll(zksIds);
            //获取送样单相关样品数据，除外包假删数据
            List<DtoAnalyseData> anaList = new ArrayList<>();
            if (allsids.size() > 0) {
                anaList = analyseDataBaseRepository.findBySampleIdInAndIsOutsourcingAndIsSamplingOut(allsids, false, false);
            }
            //所有现场样品
            List<String> lsids = anaList.stream().filter(DtoAnalyseData::getIsCompleteField).map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());

            //所有实验室样品
            List<String> asids = anaList.stream().filter(s -> !s.getIsCompleteField()).map(DtoAnalyseData::getSampleId).distinct().collect(Collectors.toList());

            //TODO:开关考虑
            Boolean isWorkSheetAudit = false;//是否有检测单审核步骤

            Boolean isFirstReceiveSubSampleRecord = true;//是否先拆分领样单
            //如果送样单下无样品
            if (samList.size() == 0) {
                //无样品，内部送样和外部送样单删除处理
                if (objrsr.getReceiveType().equals(EnumPRO.EnumReceiveType.内部送样.getValue())
                        || objrsr.getReceiveType().equals(EnumPRO.EnumReceiveType.外部送样.getValue())) {
                    subList = receiveSubSampleRecordBaseRepository.findByReceiveId(sentId);

                    if (subList != null) {
                        for (DtoReceiveSubSampleRecord objsub : subList) {
                            //删除领样单和样品的管理
                            receiveSubSampleRecord2SampleBaseRepository.deleteByReceiveSubSampleRecordId(objsub.getId(),PrincipalContextUser.getPrincipal().getUserId(), new Date());

                            //日志
                            String comment = String.format("由于送样单下无样品,删除领样单%s。", objsub.getCode());
                            newLogBaseService.createLog(objrsr.getId(),
                                    comment,
                                    "",
                                    EnumPRO.EnumLogType.送样单信息.getValue(),
                                    EnumPRO.EnumLogObjectType.送样单.getValue(),
                                    EnumPRO.EnumLogOperateType.删除领样单.toString(),
                                    uid,
                                    cname,
                                    "",
                                    "");

                            //删除领样单
                            receiveSubSampleRecordBaseRepository.delete(objsub);
                        }
                    }
                    //日志
                    String comment = String.format("由于送样单下无样品,删除送样单%s。", objrsr.getRecordCode());
                    newLogBaseService.createLog(objrsr.getId(),
                            comment,
                            "",
                            EnumPRO.EnumLogType.送样单信息.getValue(),
                            EnumPRO.EnumLogObjectType.送样单.getValue(),
                            EnumPRO.EnumLogOperateType.删除送样单.toString(),
                            uid,
                            cname,
                            "",
                            "");
                    newLogBaseService.createLog(objrsr.getProjectId(),
                            comment,
                            "",
                            EnumPRO.EnumLogType.项目送样单.getValue(),
                            EnumPRO.EnumLogObjectType.项目.getValue(),
                            EnumPRO.EnumLogOperateType.删除送样单.toString(),
                            uid,
                            cname,
                            "",
                            "");

                    //删除送样单
                    receiveSampleRecordBaseRepository.delete(objrsr);
                }
            } else {
                //#region 增删领样单
                //送样单相关领样单
                subList = receiveSubSampleRecordBaseRepository.findByReceiveId(sentId);
                Boolean anasubFlag = subList.stream().anyMatch(r -> (r.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0);
                //如果送样单不在新建状态或者流程是否先拆分领样单
                if (!objrsr.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue()) || isFirstReceiveSubSampleRecord) {
                    List<DtoReceiveSubSampleRecord> localsub = subList.stream().filter(r -> (r.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) != 0).collect(Collectors.toList());

                    if (lsids.size() == 0) {//实际没有现场数据，删除相关领样单
                        if (localsub != null && localsub.size() > 0) {
                            for (DtoReceiveSubSampleRecord objsub : localsub) {
                                receiveSubSampleRecord2SampleBaseRepository.deleteByReceiveSubSampleRecordId(objsub.getId(),PrincipalContextUser.getPrincipal().getUserId(), new Date());
                                subList.remove(objsub);

                                //日志
                                String comment = String.format("删除现场领样单%s。", objsub.getCode());
                                newLogBaseService.createLog(objrsr.getId(),
                                        comment,
                                        "",
                                        EnumPRO.EnumLogType.送样单信息.getValue(),
                                        EnumPRO.EnumLogObjectType.送样单.getValue(),
                                        EnumPRO.EnumLogOperateType.删除现场领样单.toString(),
                                        uid,
                                        cname,
                                        "",
                                        "");

                                receiveSubSampleRecordBaseRepository.delete(objsub);
                            }
                        }
                    } else {//有现场数据
                        //无现场领样单
                        if (subList.stream().noneMatch(r -> (r.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) != 0)) {
                            DtoReceiveSubSampleRecord objsub = createReceiveSubSampleRecord(objrsr, lsids, "XC");
                            subList.add(objsub);

                            if (isFirstReceiveSubSampleRecord) {
                                List<DtoSample> lsamples = samList.stream().filter(s -> lsids.contains(s.getId()) && s.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不能分析.getValue())).collect(Collectors.toList());
                                for (DtoSample objsam : lsamples) {
                                    objsam.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());

                                    //#region Biz修改
                                    objsam.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                                    objsam.setModifyDate(new Date());
                                    //#endregion
                                    commonRepository.merge(objsam);
                                }
//                                List<String> lSampleIds = samList.stream().filter(s -> lsids.contains(s.getId())
//                                        && s.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不能分析.getValue())).map(DtoSample::getId).collect(Collectors.toList());
//                                if (lSampleIds.size() > 0) {
//                                    sampleBaseRepository.updateAnalyzeStatus(lSampleIds,
//                                            EnumPRO.EnumAnalyzeStatus.可以分析.getValue(),
//                                            PrincipalContextUser.getPrincipal().getUserId(), new Date());
//                                }
                            }

                            //日志
                            String comment = String.format("创建现场领样单%s。", objsub.getCode());
                            newLogBaseService.createLog(objsub.getId(),
                                    comment,
                                    "",
                                    EnumPRO.EnumLogType.现场领样单信息.getValue(),
                                    EnumPRO.EnumLogObjectType.现场领样单.getValue(),
                                    EnumPRO.EnumLogOperateType.创建现场领样单.toString(),
                                    uid,
                                    cname,
                                    "",
                                    "");
                        }
                    }
                    List<DtoReceiveSubSampleRecord> anasub = subList.stream().filter(s -> (s.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) != 0).collect(Collectors.toList());

                    if (asids.size() == 0) {//实际没有实验室数据，删除相关领样单
                        if (anasub != null && anasub.size() > 0) {
                            for (DtoReceiveSubSampleRecord objsub : anasub) {
                                receiveSubSampleRecord2SampleBaseRepository.deleteByReceiveSubSampleRecordId(objsub.getId(),PrincipalContextUser.getPrincipal().getUserId(), new Date());
                                subList.remove(objsub);

                                //日志
                                String comment = String.format("删除实验室领样单%s。", objsub.getCode());
                                newLogBaseService.createLog(objrsr.getId(),
                                        comment,
                                        "",
                                        EnumPRO.EnumLogType.送样单信息.getValue(),
                                        EnumPRO.EnumLogObjectType.送样单.getValue(),
                                        EnumPRO.EnumLogOperateType.删除实验室领样单.toString(),
                                        uid,
                                        cname,
                                        "",
                                        "");

                                receiveSubSampleRecordBaseRepository.delete(objsub);
                            }
                        }
                    } else {//有实验室数据
                        //无实验室领样单
                        if (subList.stream().noneMatch(r -> (r.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) != 0)) {

                            DtoReceiveSubSampleRecord objsub = createReceiveSubSampleRecord(objrsr, asids, "FX");
                            subList.add(objsub);

                            if (isFirstReceiveSubSampleRecord) {
                                List<DtoSample> asamples = samList.stream().filter(s -> asids.contains(s.getId()) && s.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不能分析.getValue())).collect(Collectors.toList());

                                for (DtoSample objsam : asamples) {
                                    objsam.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                                    //#region Biz修改
                                    objsam.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                                    objsam.setModifyDate(new Date());
                                    //#endregion
                                    commonRepository.merge(objsam);
                                }
//                                List<String> aSampleIds = samList.stream().filter(s -> asids.contains(s.getId())
//                                        && s.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不能分析.getValue())).map(DtoSample::getId).collect(Collectors.toList());
//                                if (aSampleIds.size() > 0) {
//                                    sampleBaseRepository.updateAnalyzeStatus(aSampleIds,
//                                            EnumPRO.EnumAnalyzeStatus.可以分析.getValue(),
//                                            PrincipalContextUser.getPrincipal().getUserId(), new Date());
//                                }
                            }

                            //日志
                            String comment = String.format("创建实验室领样单%s。", objsub.getCode());
                            newLogBaseService.createLog(objsub.getId(),
                                    comment,
                                    "",
                                    EnumPRO.EnumLogType.实验室领样单信息.getValue(),
                                    EnumPRO.EnumLogObjectType.实验室领样单.getValue(),
                                    EnumPRO.EnumLogOperateType.创建实验室领样单.toString(),
                                    uid,
                                    cname,
                                    "",
                                    "");
                        }
                    }
                }
                //#endregion

                List<String> subids = subList.stream().map(ReceiveSubSampleRecord::getId).collect(Collectors.toList());

                //获取所有领样单相关样品
                List<DtoReceiveSubSampleRecord2Sample> r2sList = receiveSubSampleRecord2SampleBaseRepository.findByReceiveSubSampleRecordIdIn(subids);

                //#region 核对领样单关联及状态
                for (DtoReceiveSubSampleRecord objsub : subList) {
                    //获取领样单相关样品
                    List<String> sids = r2sList.stream().filter(r -> r.getReceiveSubSampleRecordId().equals(objsub.getId())).map(DtoReceiveSubSampleRecord2Sample::getSampleId).collect(Collectors.toList());

                    //#region 确认领样单样品关联
                    //核对送样单中样品是都加入现场领样单
                    if ((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) != 0) {

                        //不应该在现场领样单下的样品处理
                        List<String> noSubSampleIds = new ArrayList<>();
                        noSubSampleIds.addAll(sids.stream().filter(s -> !lsids.contains(s)).collect(Collectors.toList()));
                        if (noSubSampleIds.size() > 0) {
                            receiveSubSampleRecord2SampleBaseRepository.deleteByReceiveSubSampleRecordIdAndSampleIds(objsub.getId(), noSubSampleIds,PrincipalContextUser.getPrincipal().getUserId(), new Date());
                        }
                        //不存在现场领样单下的样品处理
                        for (String id : lsids.stream().filter(s -> !sids.contains(s)).collect(Collectors.toList())) {

                            DtoReceiveSubSampleRecord2Sample objr2s = new DtoReceiveSubSampleRecord2Sample();
                            objr2s.setReceiveSubSampleRecordId(objsub.getId());
                            objr2s.setSampleId(id);
                            receiveSubSampleRecord2SampleBaseRepository.save(objr2s);

                            Optional<DtoSample> opt = samList.stream().filter(s -> s.getId().equals(id)).findFirst();
                            if (opt.isPresent()) {
                                DtoSample objsam = opt.get();
                                objsam.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                                if (objsam.getStatus().equals(EnumPRO.EnumSampleStatus.样品未采样.toString())) {
                                    objsam.setStatus(EnumPRO.EnumSampleStatus.样品未领样.toString());
                                }
                                if (objsam.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue())) {
                                    objsam.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue());
                                }
                                if (objsam.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不需要分析.getValue())
                                        || objsam.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不能分析.getValue())) {
                                    objsam.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                                }

                                //#region Biz修改
                                objsam.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                                objsam.setModifyDate(new Date());
                                //#endregion
                                commonRepository.merge(objsam);
//                                sampleBaseRepository.updateSampleStatus(objsam.getId(), objsam.getSamplingStatus(), objsam.getStatus(),
//                                        objsam.getInnerReceiveStatus(), objsam.getAnanlyzeStatus(), PrincipalContextUser.getPrincipal().getUserId(), new Date());
                            }
                        }
                    } //核对送样单中样品是都加入实验室领样单
                    else if ((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) != 0) {

                        //不应该在实验室领样单下的样品
                        List<String> noSubSampleIds = new ArrayList<>();
                        noSubSampleIds.addAll(sids.stream().filter(s -> !asids.contains(s)).collect(Collectors.toList()));
                        if (noSubSampleIds.size() > 0) {
                            receiveSubSampleRecord2SampleBaseRepository.deleteByReceiveSubSampleRecordIdAndSampleIds(objsub.getId(), noSubSampleIds,PrincipalContextUser.getPrincipal().getUserId(), new Date());
                        }

                        //不存在实验室领样单下的样品
                        for (String id : asids.stream().filter(s -> !sids.contains(s)).collect(Collectors.toList())) {

                            DtoReceiveSubSampleRecord2Sample objr2s = new DtoReceiveSubSampleRecord2Sample();
                            objr2s.setReceiveSubSampleRecordId(objsub.getId());
                            objr2s.setSampleId(id);
                            receiveSubSampleRecord2SampleBaseRepository.save(objr2s);

                            Optional<DtoSample> opt = samList.stream().filter(s -> s.getId().equals(id)).findFirst();
                            if (opt.isPresent()) {
                                DtoSample objsam = opt.get();
                                objsam.setSamplingStatus(EnumPRO.EnumSamplingStatus.已经完成取样.getValue());
                                if (objsam.getStatus().equals(EnumPRO.EnumSampleStatus.样品未采样.toString())) {
                                    objsam.setStatus(EnumPRO.EnumSampleStatus.样品未领样.toString());
                                }
                                if (objsam.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.不能领取.getValue()) ||
                                        (objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue()) == 0) {
                                    objsam.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue());
                                }
                                if (objsam.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不需要分析.getValue())
                                        || objsam.getAnanlyzeStatus().equals(EnumPRO.EnumAnalyzeStatus.不能分析.getValue())) {
                                    objsam.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                                }

                                //#region Biz修改
                                objsam.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                                objsam.setModifyDate(new Date());
                                //#endregion
                                commonRepository.merge(objsam);
                            }
                        }
                    }
                    //#endregion

                    //#region 现场数据领样单
                    if ((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) != 0) {
                        Boolean isChangeLocStatus = false;
                        List<DtoAnalyseData> ablslist = anaList.stream().filter(a -> lsids.contains(a.getSampleId()) && a.getIsCompleteField()).collect(Collectors.toList());

                        //如果领样单不是现场录入状态并且领样单中有现场录入状态
                        if ((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue()) != 0
                                && ablslist.stream().filter(a -> !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已测.getValue())
                                && !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())).count() > 0) {

                            objsub.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue());
                            objsub.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.测试中.toString());
                            commonRepository.merge(objsub);

                            isChangeLocStatus = true;

                        }//如果领样单不在数据待复核状态并且领样单中数据都是数据待复核状态
                        else if (((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue()) != 0
                                || (objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue()) == 0)
                                && ablslist.stream().filter(a -> !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())).count() > 0
                                && ablslist.stream().filter(a -> !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已测.getValue())
                                && !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())).count() == 0) {

                            objsub.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue());
                            objsub.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.复核中.toString());
                            commonRepository.merge(objsub);

                            isChangeLocStatus = true;
                        }//如果领样单不在待数据确认状态并且领样单中数据都是待数据确认状态
                        else if ((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue()) == 0
                                && ablslist.stream().filter(a -> !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())).count() == 0
                                && ablslist.stream().filter(a -> !a.getIsDataEnabled()).count() > 0) {
                            objsub.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue() | EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue());
                            objsub.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.待数据确认.toString());
                            commonRepository.merge(objsub);

                            isChangeLocStatus = true;
                        }//如果领样单不在已数据确认状态并且领样单中数据都是已数据确认状态
                        else if ((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue()) == 0
                                && ablslist.stream().filter(a -> !a.getIsDataEnabled()).count() == 0) {
                            objsub.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue() | EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue());
                            objsub.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.已经确认.toString());
                            commonRepository.merge(objsub);

                            isChangeLocStatus = true;
                        }

                        if (isChangeLocStatus) {
                            //日志
                            String comment = String.format("更新现场领样单%s状态为%s。", objsub.getCode(), objsub.getStatus());
                            newLogBaseService.createLog(objsub.getId(),
                                    comment,
                                    "",
                                    EnumPRO.EnumLogType.现场领样单流程.getValue(),
                                    EnumPRO.EnumLogObjectType.现场领样单.getValue(),
                                    EnumPRO.EnumLogOperateType.更新现场领样单状态.toString(),
                                    uid,
                                    cname,
                                    "",
                                    "");
                        }
                    }
                    //#endregion

                    //#region 实验室数据领样单
                    else if ((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) != 0) {
                        Boolean isChangeAnaStatus = false;
                        List<DtoAnalyseData> abaslist = anaList.stream().filter(a -> asids.contains(a.getSampleId()) && !a.getIsCompleteField()).collect(Collectors.toList());

                        List<DtoSample> slist = samList.stream().filter(s -> sids.contains(s.getId())).collect(Collectors.toList());

                        //如果领样单不是待领取状态并且领样单中样品都是待领取状态
                        if ((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue()) != 0
                                && slist.stream().filter(s -> s.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue())).count() == 0
                                && abaslist.stream().filter(a -> !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.未测.getValue())).count() == 0) {
                            List<String> sIds = slist.stream().map(DtoSample::getId).collect(Collectors.toList());
                            for (DtoSample objsam : slist) {
                                if (isFirstReceiveSubSampleRecord) {
                                    objsam.setAnanlyzeStatus(EnumPRO.EnumAnalyzeStatus.可以分析.getValue());
                                }
                                objsam.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.可以领取.getValue());

                                //#region Biz修改
                                objsam.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                                objsam.setModifyDate(new Date());
                                //#endregion
                                commonRepository.merge(objsam);

                            }
//                            if (isFirstReceiveSubSampleRecord) {
//                                sampleBaseRepository.updateInnerReceiveStatusAndAnalyzeStatus(sids,
//                                        EnumPRO.EnumAnalyzeStatus.可以分析.getValue(),
//                                        EnumPRO.EnumInnerReceiveStatus.可以领取.getValue(),
//                                        PrincipalContextUser.getPrincipal().getUserId(), new Date());
//                            } else {
//                                sampleBaseRepository.updateInnerReceiveStatus(sids,
//                                        EnumPRO.EnumInnerReceiveStatus.可以领取.getValue(),
//                                        PrincipalContextUser.getPrincipal().getUserId(), new Date());
//                            }
                            objsub.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue());
                            objsub.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.未领取.toString());
                            commonRepository.merge(objsub);

                            isChangeAnaStatus = true;
                        }//如果领样单不是测试状态并且领样单中样品都是测试状态
                        else if (((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue()) != 0
                                || (objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue()) == 0)
                                && slist.stream().filter(s -> s.getInnerReceiveStatus().equals(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue())).count() > 0
                                && abaslist.stream().filter(a -> !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue())
                                && !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue())).count() > 0
                        ) {
                            for (DtoSample objsam : slist) {
                                objsam.setInnerReceiveStatus(EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());

                                //#region Biz修改
                                objsam.setModifier(PrincipalContextUser.getPrincipal().getUserId());
                                objsam.setModifyDate(new Date());
                                //#endregion
                                commonRepository.merge(objsam);
                            }
//                            List<String> sIds=slist.stream().map(DtoSample::getId).collect(Collectors.toList());
//                            if (sids.size()>0) {
//                                sampleBaseRepository.updateInnerReceiveStatus(sIds, EnumPRO.EnumInnerReceiveStatus.已经领取.getValue(),
//                                        PrincipalContextUser.getPrincipal().getUserId(), new Date());
//                            }
                            objsub.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue());
                            objsub.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.测试中.toString());
                            commonRepository.merge(objsub);

                            isChangeAnaStatus = true;
                        }//如果领样单不是待数据确认状态并且领样单中样品都是数据确认状态
                        else if (((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue()) != 0
                                || (objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue()) == 0)
                                && abaslist.stream().filter(a -> (!isWorkSheetAudit && !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.复核通过.getValue()))
                                || (isWorkSheetAudit && !a.getDataStatus().equals(EnumPRO.EnumAnalyseDataStatus.已确认.getValue()))).count() == 0
                                && abaslist.stream().filter(a -> !a.getIsDataEnabled()).count() > 0) {
                            objsub.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue() | EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue());
                            objsub.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.待数据确认.toString());
                            commonRepository.merge(objsub);

                            isChangeAnaStatus = true;
                        }//如果领样单不在已数据确认状态并且领样单中数据都是已数据确认状态
                        else if ((objsub.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue()) == 0
                                && abaslist.stream().filter(a -> !a.getIsDataEnabled()).count() == 0) {
                            objsub.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已领取.getValue() | EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue());
                            objsub.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.已经确认.toString());
                            commonRepository.merge(objsub);

                            isChangeAnaStatus = true;
                        }

                        if (isChangeAnaStatus) {
                            //日志
                            String comment = String.format("更新实验室领样单%s状态为%s。", objsub.getCode(), objsub.getStatus());
                            newLogBaseService.createLog(objsub.getId(),
                                    comment,
                                    "",
                                    EnumPRO.EnumLogType.实验室领样单流程.getValue(),
                                    EnumPRO.EnumLogObjectType.实验室领样单.getValue(),
                                    EnumPRO.EnumLogOperateType.更新实验室领样单状态.toString(),
                                    uid,
                                    cname,
                                    "",
                                    "");
                        }
                    }
                    //#endregion
                }
                //#endregion

                //#region 核对送样单
                Boolean isChange = false;
                String rsrStatus = objrsr.getStatus();
                Integer infoStatus = objrsr.getInfoStatus();

                if (!objrsr.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.已经送样.getValue())
                        && !objrsr.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue())) {
                    //所有领样单存在不是可确认或已确认状态
                    if (subList.stream().filter(s -> (s.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue()) == 0
                            && (s.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue()) == 0).count() > 0
                            && subList.size() > 0) {
                        objrsr.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.已经送样.getValue());
                        objrsr.setUploadStatus(EnumPRO.EnumReceiveUploadStatus.已数据同步.getValue());
                        objrsr.setStatus(EnumLIM.EnumReceiveRecordStatus.已经送样.toString());
                        isChange = true;
                    }
                }
                if (!objrsr.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.待数据确认.getValue())) {
                    //所有领样单都是待确认状态
                    if (subList.stream().filter(s -> (s.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue()) == 0).count() == 0
                            && subList.stream().filter(s -> (s.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue()) != 0).count() != subList.size()
                            && subList.size() > 0) {
                        objrsr.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.待数据确认.getValue());
                        objrsr.setStatus(EnumLIM.EnumReceiveRecordStatus.待数据确认.toString());
                        isChange = true;
                    }
                }
                if (!objrsr.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.已数据确认.getValue())) {
                    if (subList.stream().filter(s -> (s.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue()) == 0).count() == 0
                            && subList.size() > 0) {
                        objrsr.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.已数据确认.getValue());
                        objrsr.setStatus(EnumLIM.EnumReceiveRecordStatus.已数据确认.toString());
                        isChange = true;
                    } else if (subList.size() == 0
                            && samList.stream().filter(s -> s.getStatus().equals(EnumPRO.EnumSampleStatus.样品检毕.toString())).count() == samList.size()
                            && ((!objrsr.getReceiveStatus().equals(EnumLIM.EnumReceiveRecordStatus.新建.getValue())
                            && !objrsr.getReceiveType().equals(EnumPRO.EnumReceiveType.内部送样.getValue()))
                            || objrsr.getReceiveType().equals(EnumPRO.EnumReceiveType.内部送样.getValue()))//内部送样送样单在任何状态都变状态
                            && (!isFirstReceiveSubSampleRecord
                            || (isFirstReceiveSubSampleRecord && objrsr.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.已确认)))) {
                        objrsr.setReceiveStatus(EnumLIM.EnumReceiveRecordStatus.已数据确认.getValue());
                        objrsr.setStatus(EnumLIM.EnumReceiveRecordStatus.已数据确认.toString());
                        isChange = true;
                    }
                }

                //纠正信息状态
                List<DtoReceiveSubSampleRecord> localSub = subList.stream().filter(r -> (r.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue()) != 0).collect(Collectors.toList());

                if (localSub != null && localSub.size() > 0 && objrsr.getInfoStatus() > EnumPRO.EnumReceiveInfoStatus.新建.getValue()) {
                    //有现场领样单未提交，信息状态应该为信息登记中
                    if (!objrsr.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.信息登记中.getValue())
                            && localSub.stream().filter(r -> (r.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue()) == 0).count() > 0) {
                        objrsr.setInfoStatus(EnumPRO.EnumReceiveInfoStatus.信息登记中.getValue());
                        objrsr.setUploadStatus(EnumPRO.EnumReceiveUploadStatus.未提交.getValue());
                        objrsr.setCheckerId(UUIDHelper.GUID_EMPTY);
                        objrsr.setAuditorId(UUIDHelper.GUID_EMPTY);
                        isChange = true;
                    }
                    //现场领样单都在复核中，信息状态应该为复核中
                    if (!objrsr.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.信息复核中.getValue())
                            && localSub.stream().filter(r -> r.getSubStatus().equals(EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue())).count() == localSub.size()) {
                        objrsr.setInfoStatus(EnumPRO.EnumReceiveInfoStatus.信息复核中.getValue());
                        objrsr.setUploadStatus(EnumPRO.EnumReceiveUploadStatus.已数据同步.getValue());
                        isChange = true;
                    }
                    //现场领样单都在审核中，信息状态应该为审核中
                    if (!objrsr.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.信息审核中.getValue())
                            && localSub.stream().filter(r -> r.getSubStatus().equals(EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue() | EnumLIM.EnumReceiveSubRecordStatus.已提交.getValue() | EnumLIM.EnumReceiveSubRecordStatus.可确认.getValue())).count() == localSub.size()) {
                        objrsr.setInfoStatus(EnumPRO.EnumReceiveInfoStatus.信息审核中.getValue());
                        objrsr.setUploadStatus(EnumPRO.EnumReceiveUploadStatus.已数据同步.getValue());
                        isChange = true;
                    }
                    //现场领样单都已确认，信息状态应该为已确认
                    if (!objrsr.getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.已确认.getValue())
                            && localSub.stream().filter(r -> (r.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.已确认.getValue()) != 0).count() > 0) {
                        objrsr.setInfoStatus(EnumPRO.EnumReceiveInfoStatus.已确认.getValue());
                        objrsr.setUploadStatus(EnumPRO.EnumReceiveUploadStatus.已数据同步.getValue());
                        isChange = true;
                    }
                }

                if (isChange || (anasubFlag.equals(subList.stream().noneMatch(r -> (r.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0)))) {
                    statusForRecordBaseService.checkInfoStatus(objrsr,
                            subList.stream().filter(r -> (r.getSubStatus() & EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue()) > 0).findFirst().orElse(null));
                }
                if (isChange) {
                    commonRepository.merge(objrsr);
                    //日志
                    if (!objrsr.getStatus().equals(rsrStatus) || (!objrsr.getInfoStatus().equals(infoStatus) && isFirstReceiveSubSampleRecord)) {
                        String comment = "";
                        if (!objrsr.getStatus().equals(rsrStatus)) {

                            comment = String.format("送样单%s状态更新为%s", objrsr.getRecordCode(), objrsr.getStatus());
                        }

                        if (!objrsr.getInfoStatus().equals(infoStatus) && isFirstReceiveSubSampleRecord) {
                            if (comment != "") {
                                comment += String.format(",信息状态更新为%s。", EnumPRO.EnumReceiveInfoStatus.getByValue(objrsr.getInfoStatus()).toString());
                            } else {
                                comment += String.format("送样单%s信息状态更新为%s。", objrsr.getRecordCode(), EnumPRO.EnumReceiveInfoStatus.getByValue(objrsr.getInfoStatus()).toString());
                            }
                        }

                        newLogBaseService.createLog(objrsr.getId(),
                                comment,
                                "",
                                EnumPRO.EnumLogType.送样单流程.getValue(),
                                EnumPRO.EnumLogObjectType.送样单.getValue(),
                                EnumPRO.EnumLogOperateType.更新送样单状态.toString(),
                                uid,
                                cname,
                                "",
                                "");

                    }
                }
                //#endregion
            }
        } catch (Exception ex) {
            throw new BaseException(ex);
        }
    }

    /**
     * 创建领样单
     *
     * @param objrsr 送样单
     * @param samIds 相关样品id列表
     * @param type   领样单类型
     * @return
     */
    @Transactional
    @Override
    public DtoReceiveSubSampleRecord createReceiveSubSampleRecord(DtoReceiveSampleRecord
                                                                          objrsr, List<String> samIds, String type) {
        DtoReceiveSubSampleRecord subRecord = new DtoReceiveSubSampleRecord();
        subRecord.setReceiveId(objrsr.getId());
        subRecord.setProjectId(objrsr.getProjectId());
        subRecord.setCode(objrsr.getRecordCode() + "-" + type);
        if (type.equals("FX")) {
            subRecord.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.未领取.toString());// "待领取";
            subRecord.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有实验室数据.getValue());
        } else {
            subRecord.setStatus(EnumPRO.EnumReceiveSubRecordStatusName.测试中.toString()); //"测试中";
            subRecord.setSubStatus(EnumLIM.EnumReceiveSubRecordStatus.有现场数据.getValue());
        }
        subRecord.setReceivePersonId(UUIDHelper.GUID_EMPTY);
        subRecord.setReceiveName("");
        subRecord.setCheckerId(UUIDHelper.GUID_EMPTY);
        subRecord.setCheckerName("");
        subRecord.setAuditorId(UUIDHelper.GUID_EMPTY);
        subRecord.setAuditorName("");
        subRecord.setBackOpinion("");
        subRecord.setDomainId(UUIDHelper.GUID_EMPTY);
        subRecord.setCreator(UUIDHelper.GUID_EMPTY);
        subRecord.setCreateDate(new Date());
        subRecord.setModifier(UUIDHelper.GUID_EMPTY);
        subRecord.setModifyDate(new Date());
        subRecord.setAuditTime(new Date());
        subRecord.setCheckTime(new Date());
        subRecord.setReceiveTime(new Date());
        subRecord.setCreator(UUIDHelper.GUID_EMPTY);
        subRecord.setCreateDate(new Date());
        subRecord.setModifier(UUIDHelper.GUID_EMPTY);
        subRecord.setModifyDate(new Date());
        receiveSubSampleRecordBaseRepository.save(subRecord);
        for (String id : samIds) {
            DtoReceiveSubSampleRecord2Sample objr2s = new DtoReceiveSubSampleRecord2Sample();
            objr2s.setReceiveSubSampleRecordId(subRecord.getId());
            objr2s.setSampleId(id);
            receiveSubSampleRecord2SampleBaseRepository.save(objr2s);
        }
        return subRecord;
    }
}
