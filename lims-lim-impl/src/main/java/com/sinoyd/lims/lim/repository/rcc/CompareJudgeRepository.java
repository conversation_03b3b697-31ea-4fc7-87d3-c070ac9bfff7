package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoCompareJudge;

import java.util.Collection;
import java.util.List;

/**
 * CompareJudgeRepository
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/06/15
 */
public interface CompareJudgeRepository extends IBaseJpaPhysicalDeleteRepository<DtoCompareJudge, String> {

    /**
     * 根据分析项目id和检测类型查询条数
     *
     * @param analyzeItemId 分析项目id
     * @param checkType 检测类型
     * @return 返回条数
     */
    Integer countByAnalyzeItemIdAndCheckTypeAndIdNot(String analyzeItemId, Integer checkType, String id);

    /**
     * 根据分析项目id查询数据
     *
     * @param analyzeItemIds 分析项目id
     * @return List<DtoCompareJudge>
     */
    List<DtoCompareJudge> findByAnalyzeItemIdIn(Collection<String> analyzeItemIds);

}
