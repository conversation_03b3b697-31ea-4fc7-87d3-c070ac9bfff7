package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2ParamsConfig;

import java.util.List;

/**
 * 采样单配置关联检测类型参数配置
 *
 * <AUTHOR>
 * @version V1.0.0 2022/6/27
 * @since V100R001
 */
public interface RecordConfig2ParamsConfigService extends IBaseJpaService<DtoRecordConfig2ParamsConfig, String> {

    /**
     * 保存数据
     *
     * @param recordConfig 采样单配置信息
     * @return 已保存的数据
     */
    List<DtoRecordConfig2ParamsConfig> save(DtoRecordConfig recordConfig);
}
