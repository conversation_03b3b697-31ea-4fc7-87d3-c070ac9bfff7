package com.sinoyd.lims.pro.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.service.WorkSheetService;
import com.sinoyd.lims.pro.criteria.WorkSheetCriteria;
import com.sinoyd.lims.pro.dto.DtoWorkSheet;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;

import java.util.List;


/**
 * WorkSheet服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Api(tags = "示例: WorkSheet服务")
 @RestController
 @RequestMapping("api/pro/workSheet")
 public class WorkSheetController extends BaseJpaController<DtoWorkSheet, String,WorkSheetService> {


    /**
     * 分页动态条件查询WorkSheet
     *
     * @param workSheetCriteria 条件参数
     * @return RestResponse<List < WorkSheet>>
     */
    @ApiOperation(value = "分页动态条件查询WorkSheet", notes = "分页动态条件查询WorkSheet")
    @GetMapping
    public RestResponse<List<DtoWorkSheet>> findByPage(WorkSheetCriteria workSheetCriteria) {
        PageBean<DtoWorkSheet> pageBean = super.getPageBean();
        RestResponse<List<DtoWorkSheet>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, workSheetCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询WorkSheet
     *
     * @param id 主键id
     * @return RestResponse<DtoWorkSheet>
     */
    @ApiOperation(value = "按主键查询WorkSheet", notes = "按主键查询WorkSheet")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoWorkSheet> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoWorkSheet> restResponse = new RestResponse<>();
        DtoWorkSheet workSheet = service.findOne(id);
        restResponse.setData(workSheet);
        restResponse.setRestStatus(StringUtil.isNull(workSheet) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增WorkSheet
     *
     * @param workSheet 实体列表
     * @return RestResponse<DtoWorkSheet>
     */
    @ApiOperation(value = "新增WorkSheet", notes = "新增WorkSheet")
    @PostMapping
    public RestResponse<DtoWorkSheet> create(@RequestBody @Validated DtoWorkSheet workSheet) {
        RestResponse<DtoWorkSheet> restResponse = new RestResponse<>();
        restResponse.setData(service.save(workSheet));
        return restResponse;
    }

    /**
     * 新增WorkSheet
     *
     * @param workSheet 实体列表
     * @return RestResponse<DtoWorkSheet>
     */
    @ApiOperation(value = "修改WorkSheet", notes = "修改WorkSheet")
    @PutMapping
    public RestResponse<DtoWorkSheet> update(@RequestBody @Validated DtoWorkSheet workSheet) {
        RestResponse<DtoWorkSheet> restResponse = new RestResponse<>();
        restResponse.setData(service.update(workSheet));
        return restResponse;
    }

    /**
     * "根据id批量删除WorkSheet
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除WorkSheet", notes = "根据id批量删除WorkSheet")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * "根据父检测单id获取子检测单
     *
     * @param parentId 检测单id
     * @return RestResponse<List < DtoWorkSheet>>
     */
    @ApiOperation(value = "根据父检测单id获取子检测单", notes = "根据父检测单id获取子检测单")
    @GetMapping("/tests")
    public RestResponse<List<DtoWorkSheet>> findTest(@RequestParam(name = "parentId") String parentId) {
        RestResponse<List<DtoWorkSheet>> restResp = new RestResponse<>();
        List<DtoWorkSheet> list = service.findByParentId(parentId);
        restResp.setData(list);
        restResp.setCount(list.size());
        return restResp;
    }
}