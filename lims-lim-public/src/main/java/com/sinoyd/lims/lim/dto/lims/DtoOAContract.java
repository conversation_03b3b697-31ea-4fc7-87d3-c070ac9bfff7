package com.sinoyd.lims.lim.dto.lims;

import javax.persistence.*;
import com.sinoyd.lims.lim.entity.OAContract;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;


/**
 * DtoOAContract实体
 * <AUTHOR>
 * @version V1.0.0 2020/9/28
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_OAContract") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoOAContract extends OAContract {
   private static final long serialVersionUID = 1L;
 }