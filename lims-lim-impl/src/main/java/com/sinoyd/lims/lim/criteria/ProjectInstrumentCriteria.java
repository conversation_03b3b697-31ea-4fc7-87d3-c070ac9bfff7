package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;


/**
 * ProjectInstrument查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectInstrumentCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
    private String startTime;

    /**
     * 结束日期
     */
    private String endTime;

    /**
     * 项目名称/编号
     */
    private String projectNameCode;

    /**
     * 仪器名称/型号
     */
    private String instrumentNameCode;

    /**
     * 仪器id
     */
    private String instrumentId;

    /**
     * 项目id(根据名称或编码查询)
     */
    private List<String> projectIds;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 出入库状态 0：所有  1：待入库  2：部分入库  3：已入库
     */
    private Integer storageStatus;

    /**
     * 仪器出入库记录id
     */
    private String projectInstrumentId;

    /**
     * 仪器名称编号，出厂编号，规格型号
     */
    private String instrumentKey;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
//        condition.append(" and a.projectId = b.id ");
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date startDate = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.useDate >= :startDate");
            values.put("startDate", startDate);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date endDate = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(endDate);
            c.add(Calendar.DAY_OF_MONTH, 1);
            endDate = c.getTime();
            condition.append(" and a.useDate <= :endDate");
            values.put("endDate", endDate);
        }
        if (StringUtil.isNotEmpty(this.projectNameCode)) {
            if (StringUtil.isNotEmpty(this.projectIds)) {
                condition.append(" and (a.projectId in :projectIds or a.projectName like :projectNameCode )");
                values.put("projectIds", projectIds);
            } else {
                condition.append(" and a.projectName like :projectNameCode");
            }
            values.put("projectNameCode", "%" + projectNameCode + "%");
        }
        if (StringUtil.isNotEmpty(this.instrumentNameCode)) {
            condition.append(" and exists (select 1 from DtoProjectInstrumentDetails c, DtoInstrument d " +
                    " where c.instrumentId = d.id and d.isDeleted = 0 and (d.instrumentName like :instrumentNameCode " +
                    " or d.instrumentsCode like :instrumentNameCode) and c.projectInstrumentId = a.id) ");
            values.put("instrumentNameCode", instrumentNameCode);
        }
        if (StringUtil.isNotEmpty(this.instrumentKey)) {
            condition.append(" and exists (select 1 from DtoProjectInstrumentDetails c, DtoInstrument d " +
                    " where c.instrumentId = d.id and d.isDeleted = 0 and (d.instrumentName like :instrumentKey " +
                    " or d.instrumentsCode like :instrumentKey or d.model like :instrumentKey or d.serialNo like :instrumentKey) and c.projectInstrumentId = a.id) ");
            values.put("instrumentKey", this.instrumentKey);
        }
        if (StringUtil.isNotEmpty(this.instrumentId)) {
            condition.append(" and exists (select 1 from DtoProjectInstrumentDetails cc, DtoInstrument dd " +
                    " where cc.instrumentId = dd.id and dd.isDeleted = 0 and dd.id = :instrumentId and cc.projectInstrumentId = a.id) ");
            values.put("instrumentId", instrumentId);
        }
        condition.append(" and a.isDeleted = 0 order by a.useDate desc ");
        return condition.toString();
    }
}