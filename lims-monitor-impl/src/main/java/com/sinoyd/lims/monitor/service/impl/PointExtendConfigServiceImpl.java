package com.sinoyd.lims.monitor.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.monitor.dto.rcc.DtoPointExtendConfig;
import com.sinoyd.lims.monitor.repository.rcc.PointExtendConfigRepository;
import com.sinoyd.lims.monitor.service.PointExtendConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 例行点位拓展字段配置接口实现
 *
 * <AUTHOR>
 * @version V5.2.0 2023/06/12
 * @since V100R001
 */
@Service
public class PointExtendConfigServiceImpl extends BaseJpaServiceImpl<DtoPointExtendConfig,String, PointExtendConfigRepository> implements PointExtendConfigService {

    private CodeService codeService;

    @Override
    public void findByPage(PageBean<DtoPointExtendConfig> page, BaseCriteria criteria) {
        page.setEntityName("DtoPointExtendConfig p");
        page.setSelect("select p");
        super.findByPage(page, criteria);
        List<DtoPointExtendConfig> data = page.getData();
        List<DtoCode> pointTypes = codeService.findCodes("LIM_EnvQualityPointType");
        if (StringUtil.isNotEmpty(pointTypes)){
            Map<String, String> pointNameMap = pointTypes.stream().collect(Collectors.toMap(DtoCode::getDictCode, DtoCode::getDictName));
            for (DtoPointExtendConfig extendConfig : data) {
                if (EnumLIM.EnumControlDataSourceType.常量数据.getValue().equals(extendConfig.getDataSourceType()) && StringUtil.isNotEmpty(extendConfig.getCodeDataSource())){
                    extendConfig.setCodeDataSourceList(codeService.findCodes(extendConfig.getCodeDataSource()));
                }
                if (StringUtil.isNotEmpty(pointNameMap)){
                    extendConfig.setPointTypeName(pointNameMap.getOrDefault(extendConfig.getPointType(),""));
                }
            }
        }
    }


    @Transactional
    @Override
    public DtoPointExtendConfig save(DtoPointExtendConfig entity) {
        if (StringUtil.isNull(entity.getDataSourceType())){
            entity.setDataSourceType(EnumLIM.EnumControlDataSourceType.无.getValue());
        }
        return super.save(entity);
    }

    @Transactional
    @Override
    public DtoPointExtendConfig update(DtoPointExtendConfig entity) {
        if (StringUtil.isNull(entity.getDataSourceType())){
            entity.setDataSourceType(EnumLIM.EnumControlDataSourceType.无.getValue());
        }
        return super.update(entity);
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }
}
