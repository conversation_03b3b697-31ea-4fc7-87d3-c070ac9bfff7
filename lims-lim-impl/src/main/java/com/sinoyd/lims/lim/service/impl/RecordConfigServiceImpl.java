package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.RecordConfigCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoRecordConfigTest;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2ParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2Test;
import com.sinoyd.lims.lim.entity.ParamsConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.RecordConfig2ParamsConfigRepository;
import com.sinoyd.lims.lim.repository.rcc.RecordConfig2TestRepository;
import com.sinoyd.lims.lim.repository.rcc.RecordConfigRepository;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import com.sinoyd.lims.lim.service.RecordConfigService;
import com.sinoyd.lims.lim.service.ReportConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 原始记录单操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Service
public class RecordConfigServiceImpl extends BaseJpaServiceImpl<DtoRecordConfig, String, RecordConfigRepository> implements RecordConfigService {

    private ParamsConfigService paramsConfigService;

    private SampleTypeService sampleTypeService;

    private ReportConfigService reportConfigService;

    private RecordConfig2ParamsConfigRepository recordConfig2ParamsConfigRepository;

    private RecordConfig2TestRepository recordConfig2TestRepository;

    private DocumentService documentService;

    @Override
    public void findByPage(PageBean<DtoRecordConfig> pb, BaseCriteria recordConfigCriteria) {
        RecordConfigCriteria criteria = (RecordConfigCriteria) recordConfigCriteria;
//        int pageNum = pb.getPageNo();
//        int rowsPerPage = pb.getRowsPerPage();
        pb.setEntityName("DtoRecordConfig a");
        pb.setSelect("select a");
//        Boolean sortFlag = Boolean.FALSE;
//        if (EnumLIM.EnumRecordType.采样记录单.getValue().equals(criteria.getRecordType())) {
//            pb.setPageNo(1);
//            pb.setRowsPerPage(Integer.MAX_VALUE);
//            sortFlag = Boolean.TRUE;
//        }
        comRepository.findByPage(pb, recordConfigCriteria);
//        pb.setPageNo(pageNum);
//        pb.setRowsPerPage(rowsPerPage);
        List<DtoRecordConfig> data = pb.getData();
        List<String> sampleTypeIds = data.stream().map(DtoRecordConfig::getSampleTypeId).collect(Collectors.toList());
        List<String> recordIds = data.stream().map(DtoRecordConfig::getReportConfigId).collect(Collectors.toList());
        List<DtoReportConfig> configList = reportConfigService.findAll(recordIds);
        List<DtoSampleType> sampleTypes = sampleTypeService.findRedisByIds(sampleTypeIds);


        List<String> reportConfigIds = configList.stream().map(DtoReportConfig::getId).collect(Collectors.toList());
        Map<String, List<DtoDocument>> templateDocMap = loadRecordTemplateInfo(reportConfigIds);
        for (DtoRecordConfig recordConfig : data) {
            Optional<DtoSampleType> sampleType = sampleTypes.stream().filter(p -> recordConfig.getSampleTypeId().equals(p.getId())).findFirst();
            sampleType.ifPresent(p -> {
                recordConfig.setSampleTypeName(p.getTypeName());
                if (!UUIDHelper.GUID_EMPTY.equals(p.getParentId())) {
                    recordConfig.setBigSampleTypeId(p.getParentId());
                }
            });
            Optional<DtoReportConfig> configOptional = configList.stream().filter(p -> recordConfig.getReportConfigId().equals(p.getId())).findFirst();
            configOptional.ifPresent(p -> {
                String template = p.getTemplate();
                if (StringUtil.isNotEmpty(template) && !template.startsWith("/")) {
                    template = String.format("/%s", template);
                }
                recordConfig.setWorkPath(template);
                recordConfig.setWorkName(p.getTemplateName());

                List<DtoDocument> templateList = templateDocMap.get(template);
                if (StringUtil.isNotEmpty(templateList)) {
                    templateList.sort(Comparator.comparing(DtoDocument::getCreateDate, Comparator.reverseOrder()));
                    recordConfig.setTemplateDocId(templateList.get(0).getId());
                }
            });
        }
        pb.setData(data);
        // 24-10-18 实施说先只按照排序值排序
//        if (sortFlag) {
//            List<DtoSampleType> bigSampleTypes = sampleTypeService.findAllBigSampleType().stream().sorted(Comparator.comparing(DtoSampleType::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
//            List<DtoSampleType> allSampleTypes = sampleTypeService.findAll().stream().sorted(Comparator.comparing(DtoSampleType::getOrderNum).reversed()).collect(Collectors.toList());
//            List<DtoRecordConfig> configs = new ArrayList<>();
//            Comparator<DtoRecordConfig> comparator1 = Comparator.comparing(d -> {
//                Optional<DtoSampleType> sampleTypeOptional = allSampleTypes.stream().filter(s -> s.getId().equals(d.getSampleTypeId())).findFirst();
//                if (sampleTypeOptional.isPresent()) {
//                    return sampleTypeOptional.get().getOrderNum();
//                }
//                return -1;
//            }, Comparator.reverseOrder());
//            Comparator<DtoRecordConfig> comparator2 = Comparator.comparing(DtoRecordConfig::getRecordName);
//            data.sort(comparator1.thenComparing(comparator2));
//            for (DtoSampleType bigSampleType : bigSampleTypes) {
//                List<DtoRecordConfig> configs2Type = data.stream().filter(d -> bigSampleType.getId().equals(d.getSampleTypeId())).collect(Collectors.toList());
//                configs.addAll(configs2Type);
//                List<DtoRecordConfig> configsOfBigType = data.stream().filter(d -> bigSampleType.getId().equals(d.getBigSampleTypeId())).collect(Collectors.toList());
//                configs.addAll(configsOfBigType);
//            }
//            data.removeAll(configs);
//            configs.addAll(data);
//            configs = configs.stream().skip((long) (pb.getPageNo() - 1) * pb.getRowsPerPage()).limit(pb.getRowsPerPage()).collect(Collectors.toList());
//            pb.setData(configs);
//        }
    }

    /**
     * 获取表单模版信息
     *
     * @param reportConfigIds 报表配置id集合
     * @return 表单模版信息
     */
    protected Map<String, List<DtoDocument>> loadRecordTemplateInfo(List<String> reportConfigIds) {
        return documentService.findByObjectIds(reportConfigIds).stream().collect(Collectors.groupingBy(DtoDocument::getFolderId));
    }

    @Override
    public List<DtoRecordConfigTest> findRecordConfigByTestIds(List<String> testIds) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.lim.dto.customer.DtoRecordConfigTest(");
        stringBuilder.append("a.id,b.testId,a.reportConfigId,a.recordName,c.reportCode) ");
        stringBuilder.append("from DtoRecordConfig as a,");
        stringBuilder.append("DtoReportConfig as c,");
        stringBuilder.append("DtoRecordConfig2Test as b where 1=1 ");
        stringBuilder.append(" and a.id=b.recordConfigId");
        stringBuilder.append(" and a.reportConfigId=c.id");
        stringBuilder.append(" and b.testId in :testIds");
//        stringBuilder.append(" and a.orgId = :orgId");
        stringBuilder.append(" and a.recordType= :recordType");
        Map<String, Object> values = new HashMap<>();
        values.put("testIds", testIds);
//        values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        values.put("recordType", EnumLIM.EnumRecordType.原始记录单.getValue());
        return comRepository.find(stringBuilder.toString(), values);
    }

    /**
     * 根据记录单配置id分页获取检测类型参数数据
     *
     * @param pageBean       分页数据
     * @param paramsCriteria 查询条件
     * @return 查询结果
     */
    @Override
    public void findParamsConfigByRecordId(PageBean<DtoParamsConfig> pageBean, BaseCriteria paramsCriteria) {
        paramsConfigService.findByPage(pageBean, paramsCriteria);

        List<DtoParamsConfig> paramsConfigs = pageBean.getData();
        //设置参数类型名称
        paramsConfigs.forEach(p -> p.setParamTypeName(getParamsTypeName(p.getParamsType())));
        //移除已经配置的参数
        paramsConfigs.sort(Comparator.comparing(ParamsConfig::getOrderNum).reversed());
        //赋值数据
        pageBean.setData(paramsConfigs);
    }

    @Override
    @Transactional
    public DtoRecordConfig save(DtoRecordConfig entity) {
        if (StringUtil.isNotNull(entity)) {
            DtoRecordConfig dupNameRecord = repository.findByRecordNameAndRecordType(entity.getRecordName(), entity.getRecordType());
            if (StringUtil.isNotNull(dupNameRecord)) {
                throw new BaseException("配置名称已存在!");
            }
            DtoRecordConfig dupRecord;
            if (entity.getRecordType().equals(EnumLIM.EnumRecordType.采样记录单.getValue())) {
                dupRecord = repository.findByReportConfigIdAndRecordTypeAndSampleTypeId(entity.getReportConfigId(), entity.getRecordType(), entity.getSampleTypeId());
            } else {
                dupRecord = repository.findByReportConfigIdAndRecordType(entity.getReportConfigId(), entity.getRecordType());
            }
            if (StringUtil.isNotNull(dupRecord)) {
                throw new BaseException("模板配置已存在!");
            }
            return super.save(entity);
        }
        return null;
    }

    @Override
    @Transactional
    public DtoRecordConfig update(DtoRecordConfig entity) {
        DtoRecordConfig recordConfig = repository.findOne(entity.getId());
        if (StringUtil.isNull(recordConfig)) {
            throw new BaseException("配置不存在或已被删除!");
        }
        if (!entity.getRecordName().equals(recordConfig.getRecordName())) {
            //修改了配置名称需要判断名称是否重复
            DtoRecordConfig dupRecord = repository.findByRecordNameAndRecordType(entity.getRecordName(), entity.getRecordType());
            if (StringUtil.isNotNull(dupRecord)) {
                throw new BaseException("配置名称已存在!");
            }
        }
        if (!entity.getReportConfigId().equals(recordConfig.getReportConfigId())) {
            //修改了模板名称需要判断模板配置是否重复
            DtoRecordConfig dupRecord;
            if (entity.getRecordType().equals(EnumLIM.EnumRecordType.采样记录单.getValue())) {
                dupRecord = repository.findByReportConfigIdAndRecordTypeAndSampleTypeId(entity.getReportConfigId(), entity.getRecordType(), entity.getSampleTypeId());
            } else {
                dupRecord = repository.findByReportConfigIdAndRecordType(entity.getReportConfigId(), entity.getRecordType());
            }
            if (StringUtil.isNotNull(dupRecord)) {
                throw new BaseException("模板配置已存在!");
            }
        }
        return super.update(entity);
    }

    @Override
    @Transactional
    public void copyRecordConfig(DtoRecordConfig recordConfig) {
        save(recordConfig);
        if (EnumLIM.EnumRecordType.采样记录单.getValue().equals(recordConfig.getRecordType())) {
            List<DtoRecordConfig2Test> recordConfigTests = new ArrayList<>();
            List<DtoRecordConfig2ParamsConfig> recordConfig2ParamsConfigs = new ArrayList<>();
            // 获取复制源配置的测试项目和参数
            List<DtoRecordConfig2ParamsConfig> sourceParamsConfigs = recordConfig2ParamsConfigRepository.findByRecordConfigId(recordConfig.getSourceRecordConfigId());
            List<DtoRecordConfig2Test> sourceTests = recordConfig2TestRepository.findByRecordConfigId(recordConfig.getSourceRecordConfigId());
            for (DtoRecordConfig2ParamsConfig sourceParamsConfig : sourceParamsConfigs) {
                DtoRecordConfig2ParamsConfig save = new DtoRecordConfig2ParamsConfig();
                BeanUtils.copyProperties(sourceParamsConfig, save, "id");
                save.setRecordConfigId(recordConfig.getId());
                recordConfig2ParamsConfigs.add(save);
            }
            for (DtoRecordConfig2Test sourceTest : sourceTests) {
                DtoRecordConfig2Test save = new DtoRecordConfig2Test();
                BeanUtils.copyProperties(sourceTest, save, "id");
                save.setRecordConfigId(recordConfig.getId());
                recordConfigTests.add(save);
            }
            if (StringUtil.isNotEmpty(recordConfig2ParamsConfigs)) {
                recordConfig2ParamsConfigRepository.save(recordConfig2ParamsConfigs);
            }
            if (StringUtil.isNotEmpty(recordConfigTests)) {
                recordConfig2TestRepository.save(recordConfigTests);
            }
        } else if (EnumLIM.EnumRecordType.原始记录单.getValue().equals(recordConfig.getRecordType())) {
            List<DtoParamsConfig> sourceParamsConfigs = new ArrayList<>();
            Stream.of(6, 9).forEach(type -> sourceParamsConfigs.addAll(paramsConfigService.findByObjIdInAndType(Collections.singletonList(recordConfig.getSourceRecordConfigId()), type)));
            List<DtoParamsConfig> saveParamsConfigs = new ArrayList<>();
            for (DtoParamsConfig sourceParamsConfig : sourceParamsConfigs) {
                DtoParamsConfig save = new DtoParamsConfig();
                BeanUtils.copyProperties(sourceParamsConfig, save, "id");
                save.setObjId(recordConfig.getId());
                saveParamsConfigs.add(save);
            }
            if (StringUtil.isNotEmpty(saveParamsConfigs)) {
                paramsConfigService.save(saveParamsConfigs);
            }
        }
    }

    /**
     * 获取参数类型名称
     *
     * @param paramsType 参数类型
     * @return 参数类型名称
     */
    private String getParamsTypeName(Integer paramsType) {
        String paramsTypeName;
        Integer publicParams = EnumLIM.EnumParamsType.公共参数.getValue();
        Integer sampleParams = EnumLIM.EnumParamsType.样品参数.getValue();
        Integer analyseItemParams = EnumLIM.EnumParamsType.分析项目参数.getValue();
        Integer pointParams = EnumLIM.EnumParamsType.点位参数.getValue();
        if (publicParams.equals(paramsType)) {
            paramsTypeName = EnumLIM.EnumParamsType.公共参数.name();
        } else if (sampleParams.equals(paramsType)) {
            paramsTypeName = EnumLIM.EnumParamsType.样品参数.name();
        } else if (analyseItemParams.equals(paramsType)) {
            paramsTypeName = EnumLIM.EnumParamsType.分析项目参数.name();
        } else if (pointParams.equals(paramsType)) {
            paramsTypeName = EnumLIM.EnumParamsType.点位参数.name();
        } else {
            paramsTypeName = "";
        }
        return paramsTypeName;
    }

    @Autowired
    public void setParamsConfigService(ParamsConfigService paramsConfigService) {
        this.paramsConfigService = paramsConfigService;
    }


    @Autowired
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    @Lazy
    public void setReportConfigService(ReportConfigService reportConfigService) {
        this.reportConfigService = reportConfigService;
    }

    @Autowired
    @Lazy
    public void setRecordConfig2ParamsConfigRepository(RecordConfig2ParamsConfigRepository recordConfig2ParamsConfigRepository) {
        this.recordConfig2ParamsConfigRepository = recordConfig2ParamsConfigRepository;
    }

    @Autowired
    @Lazy
    public void setRecordConfig2TestRepository(RecordConfig2TestRepository recordConfig2TestRepository) {
        this.recordConfig2TestRepository = recordConfig2TestRepository;
    }

    @Autowired
    @Lazy
    public void setDocumentService(DocumentService documentService) {
        this.documentService = documentService;
    }
}