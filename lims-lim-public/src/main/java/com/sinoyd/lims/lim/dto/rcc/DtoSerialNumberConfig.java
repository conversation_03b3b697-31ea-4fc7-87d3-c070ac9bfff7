package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.SerialNumberConfig;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoSerialNumberConfig实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_SerialNumberConfig")
 @Data
 @DynamicInsert
 public  class DtoSerialNumberConfig extends SerialNumberConfig {
    private static final long serialVersionUID = 1L;

}