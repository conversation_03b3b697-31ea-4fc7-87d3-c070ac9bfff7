package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.base.criteria.InstrumentCriteria;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.lims.InstrumentRepository;
import com.sinoyd.base.service.InstrumentService;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoDepartment;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.DepartmentService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoImportInstrumentExpend;
import com.sinoyd.lims.lim.dto.customer.expimp.DtoExpImpInstrument;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.lim.service.ExpImpInstrumentService;
import com.sinoyd.lims.lim.utils.ImportUtils;
import com.sinoyd.lims.lim.verify.expimp.ImpModifyInstrumentVerify;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 仪器导入导出接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/13
 * @since V100R001
 */
@Service
public class ExpImpInstrumentServiceImpl extends BaseJpaServiceImpl<DtoInstrument, String, InstrumentRepository> implements ExpImpInstrumentService {

    private InstrumentService instrumentService;

    private ImportUtils importUtils;

    private DepartmentService departmentService;

    private CodeService codeService;

    private PersonRepository personRepository;

    /**
     * 仪器状态
     */
    private static final List<String> insStatus = new ArrayList<>();

    /**
     * 计量类型
     */
    private static final List<String> meteringType = new ArrayList<>();

    static {
        insStatus.add(EnumBase.EnumInstrumentStatus.正常.name());
        insStatus.add(EnumBase.EnumInstrumentStatus.报废.name());
        insStatus.add(EnumBase.EnumInstrumentStatus.停用.name());
        meteringType.add(EnumBase.EnumOriginType.校准.name());
        meteringType.add(EnumBase.EnumOriginType.检定.name());
        meteringType.add(EnumBase.EnumOriginType.自校.name());
    }


    /**
     * 导出
     *
     * @param baseCriteria 请求参数
     * @param response     响应体
     * @param sheetNames   sheet页名称
     * @param fileName     附件名称
     */
    @Override
    public void export(BaseCriteria baseCriteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName) {
        PageBean<DtoInstrument> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        InstrumentCriteria criteria = (InstrumentCriteria) baseCriteria;
        instrumentService.findByPage(page, criteria);
        List<DtoInstrument> instrumentList = page.getData();
        Date date1753 = DateUtil.stringToDate("1753-01-01", DateUtil.YEAR);
        List<DtoDepartment> deptList = departmentService.findAll();
        List<DtoExpImpInstrument> expImpInstrumentList = new ArrayList<>();
        for (DtoInstrument instrument : instrumentList) {
            DtoExpImpInstrument expImpInstrument = new DtoExpImpInstrument();
            BeanUtils.copyProperties(instrument, expImpInstrument);
            deptList.stream().filter(p -> p.getId().equals(instrument.getBelongDeptId())).findFirst().ifPresent(dept -> {
                expImpInstrument.setDeptName(dept.getDeptName());
            });

            // 购置日期
            String purchaseDate = "";
            if (StringUtil.isNotNull(instrument.getPurchaseDate()) && instrument.getPurchaseDate().compareTo(date1753) != 0) {
                purchaseDate = DateUtil.dateToString(instrument.getPurchaseDate(), DateUtil.YEAR);
            }
            expImpInstrument.setPurchaseDate(purchaseDate);
            expImpInstrument.setPrice(instrument.getPrice().toPlainString());
            expImpInstrument.setState(EnumBase.EnumInstrumentStatus.EnumInstrumentStatus(instrument.getState()));
            // 溯源周期
            expImpInstrument.setOriginCyc(instrument.getOriginCyc().toPlainString());

            // 溯源日期
            String originDate = "";
            if (StringUtil.isNotNull(instrument.getOriginDate()) && instrument.getOriginDate().compareTo(date1753) != 0) {
                originDate = DateUtil.dateToString(instrument.getOriginDate(), DateUtil.YEAR);
            }
            expImpInstrument.setOriginDate(originDate);
            // 溯源方式
            expImpInstrument.setOriginType(EnumBase.EnumOriginType.getEnumOriginType(instrument.getOriginType()).name());
            // 溯源结果
            expImpInstrument.setOriginResult(instrument.getOriginResult() == 1 ? "合格" : "不合格");
            // 核查结果
            expImpInstrument.setInspectResult(instrument.getInspectResult() == 1 ? "合格" : "不合格");
            // 核查周期
            expImpInstrument.setInspectPeriod(instrument.getInspectPeriod().toPlainString());
            // 核查日期
            String inspectDate = "";
            if (StringUtil.isNotNull(instrument.getInspectDate()) && instrument.getInspectDate().compareTo(date1753) != 0) {
                inspectDate = DateUtil.dateToString(instrument.getInspectDate(), DateUtil.YEAR);
            }
            expImpInstrument.setInspectDate(inspectDate);

            // 维护周期
            expImpInstrument.setMaintenanceCyc(instrument.getMaintenanceCyc().toPlainString());
            // 维护日期
            String maintenanceDate = "";
            if (StringUtil.isNotNull(instrument.getMaintenanceDate()) && instrument.getMaintenanceDate().compareTo(date1753) != 0) {
                maintenanceDate = DateUtil.dateToString(instrument.getMaintenanceDate(), DateUtil.YEAR);
            }
            expImpInstrument.setMaintenanceDate(maintenanceDate);

            // 启用日期
            String recentOpenDate = "";
            if (StringUtil.isNotNull(instrument.getRecentOpenDate()) && instrument.getRecentOpenDate().compareTo(date1753) != 0) {
                recentOpenDate = DateUtil.dateToString(instrument.getRecentOpenDate(), DateUtil.YEAR);
            }
            expImpInstrument.setRecentOpenDate(recentOpenDate);
            expImpInstrumentList.add(expImpInstrument);
        }
        // 获取所有的仪器类型
        List<String> insTypeNames = codeService.findCodes("LIM_InstrumentType").stream().map(DtoCode::getDictName).collect(Collectors.toList());
        // 获取所有的部门
        List<String> deptNames = deptList.stream().map(DtoDepartment::getDeptName).collect(Collectors.toList());
        //endregion

        //region 获取关联数据集合
        // 获取关联数据
        List<DtoImportInstrumentExpend> insExtendList = getExtendData(insTypeNames, deptNames);

        Workbook workBook = importUtils.getWorkBook(sheetNames, DtoExpImpInstrument.class, DtoImportInstrumentExpend.class, expImpInstrumentList, insExtendList);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }


    /**
     * 处理导入表格
     *
     * @param file      传入的文件
     * @param objectMap
     * @param response
     * @return List<T>
     */
    @Override
    public List<DtoInstrument> importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception {
        PoiExcelUtils.verifyFileType(file);

        //是否导入仪器类型
        Boolean isImportInsType = (Boolean) objectMap.get(0);
        //获取导入校验器
        ImpModifyInstrumentVerify verify = getVerify(file, isImportInsType);
        //获取导入结果
        ExcelImportResult<DtoExpImpInstrument> result = getExcelData(verify, file, response);
        //校验后需要导入的仪器数据
        List<DtoExpImpInstrument> importInstruments = result.getList();
        //删除空行
        importInstruments.removeIf(p -> StringUtil.isEmpty(p.getInstrumentsCode()));
        //判断文件中是否存在数据
        if (StringUtil.isEmpty(importInstruments)) {
            throw new BaseException("文件中无数据，请检查后导入");
        }
        List<DtoInstrument> instrumentsAll = repository.findAll();
        Map<String, DtoInstrument> instrumentMap = instrumentsAll.stream().collect(Collectors.toMap(DtoInstrument::getId, p -> p));
        //获取数据库所有部门
        List<DtoDepartment> dbDepartment = departmentService.findAll();
        //获取数据库中所有的仪器类型
        List<DtoCode> dbInsType = codeService.findCodes("LIM_InstrumentType");

        List<DtoPerson> personList = personRepository.findAll();
        //处理是否导入仪器类型
        if (isImportInsType) {
            //导入不包含的仪器类型
            importInsType(dbInsType, file);
        }
        //导入数据库
        List<DtoInstrument> instruments = new ArrayList<>();
        for (DtoExpImpInstrument importInstrument : importInstruments) {
            DtoInstrument instrument = new DtoInstrument();
            if (instrumentMap.containsKey(importInstrument.getId())) {
                instrument = instrumentMap.get(importInstrument.getId());
            } else {
                importInstrument.setId(UUIDHelper.NewID());
            }
            BeanUtils.copyProperties(importInstrument, instrument);
            AtomicReference<String> personId = new AtomicReference<>(UUIDHelper.GUID_EMPTY);
            personList.stream().filter(p -> p.getCName().equals(importInstrument.getManagerName())).findFirst().ifPresent(person -> personId.set(person.getId()));
            List<String> deptIds = dbDepartment.stream().filter(p -> importInstrument.getDeptName().equals(p.getDeptName())).map(DtoDepartment::getId).collect(Collectors.toList());
            instrument.setManager(StringUtil.isNotEmpty(personId.get()) ? personId.get() : "");
            instrument.setBelongDeptId(StringUtil.isEmpty(deptIds) ? UUIDHelper.GUID_EMPTY : deptIds.get(0));
            importToEntity(instrument, importInstrument);
            instruments.add(instrument);
        }
        addData(instruments);
        return repository.findAll();
    }

    /**
     * 导入到数据库
     *
     * @param data 需要导入的数据
     */
    @Override
    public void addData(List<DtoInstrument> data) {
        repository.save(data);
    }

    /**
     * 获取文件需要导入的数据
     *
     * @param verifyHandler 校验器
     * @param file          传入的文件
     * @param response      响应体
     * @return 导入结果
     */
    @Override
    public ExcelImportResult<DtoExpImpInstrument> getExcelData(IExcelVerifyHandler<DtoExpImpInstrument> verifyHandler, MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        params.setVerifyHandler(verifyHandler);
        ExcelImportResult<DtoExpImpInstrument> result = ExcelImportUtil.importExcelMore(
                file.getInputStream(),
                DtoExpImpInstrument.class, params);

        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "仪器导入错误信息");
            failWorkbook.removeSheetAt(1);
            PoiExcelUtils.downLoadExcel("仪器导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        return result;
    }


    /**
     * 获取文件需要导入的数据
     *
     * @param file     传入的文件
     * @param response
     * @return List
     */
    @Override
    public ExcelImportResult<DtoExpImpInstrument> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception {
        return null;
    }

    /**
     * 获取仪器导入校验器
     *
     * @param file            导入文件
     * @param isImportInsType 是否同步导入仪器类型
     * @return 导入校验器
     */
    private ImpModifyInstrumentVerify getVerify(MultipartFile file, Boolean isImportInsType) {
        Map<String, Boolean> relationMap = new HashMap<>();
        relationMap.put("isImportInsType", isImportInsType);
        //需要导入的部门
        List<DtoImportInstrumentExpend> importDeptNames = importUtils.getImportNames(file, DtoImportInstrumentExpend.class);
        return new ImpModifyInstrumentVerify(
                relationMap, departmentService.findAll(),
                codeService.findCodes("LIM_InstrumentType"),
                instrumentService.findAll(), importDeptNames);
    }


    /**
     * 设置导入实体值
     *
     * @param importInstrument 导入的数据
     */
    public void importToEntity(DtoInstrument instrument, DtoExpImpInstrument importInstrument) {
        instrument.setPrice(importInstrument.getPrice() == null ? BigDecimal.ZERO : new BigDecimal(importInstrument.getPrice()));
        instrument.setState(importInstrument.getState() == null ? 1 : Integer.parseInt(importInstrument.getState()));
        instrument.setOriginCyc(importInstrument.getOriginCyc() == null ? BigDecimal.ZERO : new BigDecimal(importInstrument.getOriginCyc()));
        instrument.setOriginType(importInstrument.getOriginType() == null ? -1 : Integer.parseInt(importInstrument.getOriginType()));
        instrument.setInspectPeriod(importInstrument.getInspectPeriod() == null ? BigDecimal.ZERO : new BigDecimal(importInstrument.getInspectPeriod()));
        instrument.setMaintenanceCyc(importInstrument.getMaintenanceCyc() == null ? BigDecimal.ZERO : new BigDecimal(importInstrument.getMaintenanceCyc()));
        instrument.setOriginResult(importInstrument.getOriginResult() == null ? 1 : Integer.parseInt(importInstrument.getOriginResult()));
        instrument.setInspectResult(importInstrument.getInspectResult() == null ? 1 : Integer.parseInt(importInstrument.getInspectResult()));
        instrument.setPurchaseDate(StringUtils.isNotNullAndEmpty(importInstrument.getPurchaseDate()) ? importUtils.stringToDateAllFormat(importInstrument.getPurchaseDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
        instrument.setOriginDate(StringUtils.isNotNullAndEmpty(importInstrument.getOriginDate()) ? importUtils.stringToDateAllFormat(importInstrument.getOriginDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
        instrument.setInspectDate(StringUtils.isNotNullAndEmpty(importInstrument.getInspectDate()) ? importUtils.stringToDateAllFormat(importInstrument.getInspectDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
        instrument.setMaintenanceDate(StringUtils.isNotNullAndEmpty(importInstrument.getMaintenanceDate()) ? importUtils.stringToDateAllFormat(importInstrument.getMaintenanceDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
        instrument.setRecentOpenDate(StringUtils.isNotNullAndEmpty(importInstrument.getRecentOpenDate()) ? importUtils.stringToDateAllFormat(importInstrument.getRecentOpenDate()) : importUtils.stringToDateAllFormat("1753-01-01"));
        Date originEndDate = getOriginEndDate(instrument.getOriginDate(), instrument.getOriginCyc());
        if (StringUtil.isNotNull(originEndDate)) {
            instrument.setOriginEndDate(originEndDate);
        }
    }


    /**
     * 导入仪器类型
     *
     * @param dbInsType 数据库的所有仪器类型
     * @param file      传入的文件
     */
    private void importInsType(List<DtoCode> dbInsType, MultipartFile file) {
        //需要导入的部门
        List<DtoImportInstrumentExpend> importInsNames = importUtils.getImportNames(file, DtoImportInstrumentExpend.class);
        //数据库中所有的部门Id
        List<String> InsTypeNames = dbInsType.stream().map(DtoCode::getDictName).collect(Collectors.toList());
        //不包含的部门(需要导入的部门)
        List<String> isImportInsTypes = importInsNames.stream().map(p -> StringUtil.isEmpty(p.getInstrumentTypeName()) ? null : p.getInstrumentTypeName()).collect(Collectors.toList());
        importUtils.createCodes(isImportInsTypes, "LIM_InstrumentType", InsTypeNames);
    }

    /**
     * 获取相应的有效期
     *
     * @param originDate 开始时间
     * @param originCyc  周期
     * @return 返回想要的数据
     */
    private Date getOriginEndDate(Date originDate, BigDecimal originCyc) {
        if (StringUtil.isNotNull(originDate) && StringUtil.isNotNull(originCyc)) {
            // 溯源日期为"1753-01-01" 不计算有效期
            if (importUtils.stringToDateAllFormat("1753-01-01").compareTo(originDate) == 0) {
                return null;
            }
            Calendar c = Calendar.getInstance();
            c.setTime(originDate);
            c.set(Calendar.HOUR_OF_DAY, 23);
            c.set(Calendar.MINUTE, 59);
            c.set(Calendar.SECOND, 59);
            c.add(Calendar.MONTH, originCyc.intValue());
            //有效期 = 溯源日期+溯源周期-1
            c.add(Calendar.DAY_OF_YEAR, -1);
            return c.getTime();
        }
        return null;
    }

    /**
     * 获取仪器导入关联数据
     *
     * @param insTypeNames 仪器类型数据
     * @param deptNames    部门数据
     * @return 关联集合
     */
    private List<DtoImportInstrumentExpend> getExtendData(List<String> insTypeNames, List<String> deptNames) {
        //返回的数据集合
        List<DtoImportInstrumentExpend> insExtendList = new ArrayList<>();

        // 获取最长
        List<Integer> size = new ArrayList<>();
        size.add(insTypeNames.size());
        size.add(deptNames.size());
        size.add(insStatus.size());
        size.add(meteringType.size());
        Integer max = Collections.max(size);
        // 循环赋值第二种Sheet内容
        for (int i = 0; i < max; i++) {
            DtoImportInstrumentExpend insExtend = new DtoImportInstrumentExpend();
            insExtend.setDeptName(deptNames.size() < i + 1 ? null : deptNames.get(i));
            insExtend.setInstrumentTypeName(insTypeNames.size() < i + 1 ? null : insTypeNames.get(i));
            insExtend.setStatus(insStatus.size() < i + 1 ? null : insStatus.get(i));
            insExtend.setMeteringType(meteringType.size() < i + 1 ? null : meteringType.get(i));
            insExtendList.add(insExtend);
        }
        return insExtendList;
    }


    @Autowired
    public void setInstrumentService(InstrumentService instrumentService) {
        this.instrumentService = instrumentService;
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }

    @Autowired
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }
}
