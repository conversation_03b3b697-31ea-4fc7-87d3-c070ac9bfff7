package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.CertHistoryFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoCertHistoryFile实体
 * <AUTHOR>
 * @version V1.0.0
 * @since  2024/11/26
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_CertHistoryFile")
@Data
@DynamicInsert
public class DtoCertHistoryFile extends CertHistoryFile {
}
