package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * 监管平台采样人员配置
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2022/11/25
 */
@MappedSuperclass
@ApiModel(description = "SHSamplingPerson")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SHSamplingPersonNew implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 任务id
     */
    @Column(length = 25, nullable = false)
    @ApiModelProperty("任务id")
    private String taskId;

    /**
     * 人员id
     */
    @Column(length = 25, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("人员id")
    private String personId;
}
