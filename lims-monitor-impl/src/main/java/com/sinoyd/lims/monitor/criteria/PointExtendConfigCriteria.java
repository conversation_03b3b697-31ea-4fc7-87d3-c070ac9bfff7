package com.sinoyd.lims.monitor.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 点位拓展字段配置查询条件
 *
 * <AUTHOR>
 * @version V5.2.0 2023/06/12
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointExtendConfigCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 点位类型
     */
    private String pointType;

    /**
     * 字段名称
     */
    private String filedName;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.pointType)) {
            if (pointType.replace("，",",").contains(",")){
                List<String> pointTypes = Arrays.stream(pointType.replace("，", ",").split(",")).collect(Collectors.toList());
                condition.append(" and pointType in :pointType");
                values.put("pointType", pointTypes);
            }else{
                condition.append(" and pointType = :pointType");
                values.put("pointType", this.pointType);
            }
        }
        if (StringUtil.isNotEmpty(this.filedName)) {
            condition.append(" and filedName like :filedName");
            values.put("filedName", "%" + this.filedName + "%");
        }
        return condition.toString();
    }
}
