package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.CarConsumerRecord;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoCarConsumerRecord实体
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_CarConsumerRecord")
 @Data
 @DynamicInsert
 public  class DtoCarConsumerRecord extends CarConsumerRecord {

    /**
     * 类型名称
     */
    @Transient
    private String typeName;


    /**
     * 车牌号码
     */
    @Transient
    private String carCode;


    /**
     * 格式化日期，用于附件机制归档目录
     */
    @Transient
    private String formatDate;
}