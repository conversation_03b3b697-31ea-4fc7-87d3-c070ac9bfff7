package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.ReportDeprive;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoReportDeprive实体
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_ReportDeprive")
@Data
@DynamicInsert
public class DtoReportDeprive extends ReportDeprive {
}
