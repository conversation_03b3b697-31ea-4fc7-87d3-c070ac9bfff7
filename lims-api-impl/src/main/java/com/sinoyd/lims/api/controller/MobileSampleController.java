package com.sinoyd.lims.api.controller;

import com.sinoyd.base.dto.customer.DtoComplexQuery;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.api.dto.customer.DtoSampleParamsApiPhone;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.service.ProService;
import com.sinoyd.lims.pro.service.SampleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 移动端样品接口Sample服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@Api(tags = "示例: ergencyMonitoring服务")
@RestController
@RequestMapping("api/mobile/sample")
public class MobileSampleController extends BaseJpaController<DtoSample, String, SampleService> {



    @Autowired
    private ProService proService;

    /**
     * 作废样品
     *
     * @param dto 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "作废样品", notes = "作废样品")
    @PostMapping(path = "/invalidSamples")
    public RestResponse<Boolean> invalidSamples(@RequestBody DtoComplexQuery dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.invalidSamples(dto.getIds(), dto.getOpinion());
        List<String> receiveIds = service.clearCodeBackRecordIds(dto.getIds());
        if (receiveIds.size() > 0) {
            proService.checkReceiveSampleRecord(receiveIds);
        }
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 取消作废样品
     *
     * @param dto 传输实体
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "取消作废样品", notes = "取消作废样品")
    @PostMapping(path = "/invalidCancelSamples")
    public RestResponse<Boolean> invalidCancelSamples(@RequestBody DtoComplexQuery dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.invalidCancelSamples(dto.getIds(), dto.getOpinion());
        restResponse.setData(true);
        return restResponse;
    }

    /**
     * 批量公式计算现场数据出证结果并保存
     *
     * @param dtoSampleParamsApiPhone 参数容器
     * @return RestResponse<Boolean>
     */
    @ApiOperation(value = "批量公式计算现场数据出证结果并保存", notes = "批量公式计算现场数据出证结果并保存")
    @PostMapping(path = "/batchCalculateFormula")
    public RestResponse<Map<String,Object>> batchCalculateFormula(@RequestBody DtoSampleParamsApiPhone dtoSampleParamsApiPhone) {
        RestResponse<Map<String,Object>> restResponse = new RestResponse<>();
        restResponse.setData(service.batchCalculateFormula(dtoSampleParamsApiPhone.getAnalyseDataPhoneList()));
        return restResponse;
    }

    /**
     * 获取ocr样品选择数据源
     *
     *
     * @return RestResponse<List<DtoSample>>
     */
    @ApiOperation(value = "获取ocr样品选择数据源", notes = "获取ocr样品选择数据源")
    @GetMapping(path = "/ocr")
    public RestResponse<List<Map<String,Object>>> getOcrSelectList() {
        RestResponse<List<Map<String,Object>>> restResponse = new RestResponse<>();
        restResponse.setData(service.getOcrSelectList());
        return restResponse;
    }

    /**
     * 获取ocr样品选择数据源
     *
     * @param sampleId 样品编号
     * @return RestResponse<List<DtoSample>>
     */
    @ApiOperation(value = "获取ocr样品选择数据源", notes = "获取ocr样品选择数据源")
    @GetMapping(path = "/group/{sampleId}")
    public RestResponse<List<Map<String,Object>>> getSampleGroupSelectList(@PathVariable String sampleId) {
        RestResponse<List<Map<String,Object>>> restResponse = new RestResponse<>();
        restResponse.setData(service.getSampleGroupSelectList(sampleId));
        return restResponse;
    }
}
