package com.sinoyd.lims.order.service.impl;

import com.sinoyd.base.utils.CalendarUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.pro.enums.EnumPRO;

import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import com.sinoyd.lims.pro.vo.OrderCustomerNumAnlVo;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service(CustomerOrderAnaService.CUSTOMER_ORDER_NUM_ANL)
public class CustomerOrderNumAnlImpl extends AbsCustomerOrder {

    /**
     * 客户数量分析
     *
     * @param year 年份
     * @return OrderCustomerAnlVo
     */
    @Override
    public BaseOrderCustomerAnalyzeVO statistics(Integer year) {
        String currentYearBegin = CalendarUtil.getCurrentYearBegin(year);
        String currentYearEnd = CalendarUtil.getCurrentYearEnd(year);
        String threeYearBegin = CalendarUtil.getThreeYearBegin(year);
        // 查找所有的客户信息
        StringBuilder totalSb = new StringBuilder();
        totalSb.append("SELECT * FROM TB_BASE_Enterprise where isDeleted = 0 and createDate <= ?");
        Object[] params = new Object[]{currentYearEnd};
        List<Map<String, Object>> totalList = super.getCustomerOrderData(totalSb.toString(),params);
        int customerNumTotal = 0;
        if (totalList != null) {
            customerNumTotal = totalList.size();
        }


        //筛选出三年前到至今的客户信息
        StringBuilder totalThreeSb = new StringBuilder();
        totalThreeSb.append("SELECT id,date_add(createDate,interval 2 year) createDate FROM TB_BASE_Enterprise where createDate between ");
        totalThreeSb.append(" ? ");
        totalThreeSb.append(" and ");
        totalThreeSb.append(" ? ");
        totalThreeSb.append(" and isDeleted = 0 ");
        Object[] totalThreeParams = new Object[]{threeYearBegin, currentYearEnd};

        //查找出满了三年的客户id
        String ids = "";
        int threeNumTotal = 0;
        List<Map<String, Object>> threeTotalList = super.getCustomerOrderData(totalThreeSb.toString(), totalThreeParams);
        if (threeTotalList != null) {
            for (Map<String, Object> map : threeTotalList) {
                String createDate = map.get("createDate").toString();
                String id = map.get("id").toString();
                Boolean isPass = compareDate(createDate,year);
                if (isPass) {
                    if (threeNumTotal == 0) {
                        ids = "'" + id + "'";
                    } else {
                        ids = String.join(",", ids, "'" + id + "'");
                    }
                    threeNumTotal++;
                }
            }
        }


        //查找新增的客户信息
        StringBuilder newSb = new StringBuilder();
        newSb.append("SELECT * FROM TB_BASE_Enterprise WHERE createDate BETWEEN ");
        newSb.append(" ? ");
        newSb.append(" and ");
        newSb.append(" ? ");
        newSb.append(" and isDeleted = 0 ");
        Object[] newParams = new Object[]{currentYearBegin, currentYearEnd};
        List<Map<String, Object>> newList = super.getCustomerOrderData(newSb.toString(), newParams);
        int customerNumNew = 0;
        if (newList != null) {
            customerNumNew = newList.size();
        }

        //查找没有流失的客户信息
        int customerNumLose = 0;
        if (StringUtil.isNotEmpty(ids)) {
            StringBuilder unLoseSb = new StringBuilder();
            unLoseSb.append("SELECT enterpriseId FROM TB_PRO_OrderForm  WHERE  ");
            unLoseSb.append(" enterpriseId in ( " + ids + " ) AND ");
            unLoseSb.append("  grantStatus = " + EnumPRO.EnumOrderSignStatus.已签订.getValue() + " ");
            unLoseSb.append(" and isDeleted = 0 ");
            unLoseSb.append(" and signDate between ");
            unLoseSb.append(" ? and");
            unLoseSb.append(" ? ");
            List<Map<String, Object>> unLoseList = super.getCustomerOrderData(unLoseSb.toString(), totalThreeParams);
            if (unLoseList != null) {
                int unLoseNum = unLoseList.stream().map(item -> item.get("enterpriseId").toString()).distinct().collect(Collectors.toList()).size();
                customerNumLose = threeNumTotal - unLoseNum;
            }else {
                customerNumLose = threeNumTotal;
            }
        }

        return new OrderCustomerNumAnlVo(customerNumTotal, customerNumNew, customerNumLose);
    }

    private boolean compareDate(String date,Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR,year);
        Date nowTime = calendar.getTime();
        Date threeYearDate = DateUtil.stringToDate(date, DateUtil.YEAR);
        boolean isBefore = nowTime.after(threeYearDate);
        return isBefore;
    }

}
