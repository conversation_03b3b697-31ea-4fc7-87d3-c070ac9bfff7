package com.sinoyd.lims.lim.dto.customer;


import lombok.Data;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Transient;
import java.math.BigDecimal;


/**
 *	费用总金额传输类
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
 @Data
 @DynamicInsert
 public  class DtoRecAndPayRecordTotal  {
	/**
	 * 总收入
	 */
	@Transient  
	private BigDecimal totalCollections;
	/**
	 * 	总支出(元)
	 */
	@Transient  
	private BigDecimal totalPayments;
	/**
	 * 总坏账(元)
	 */
	@Transient  
	private BigDecimal totalBadAmounts;
	/**
	 * 合计(元)
	 */
	@Transient  
	private BigDecimal totalMoney;
 }