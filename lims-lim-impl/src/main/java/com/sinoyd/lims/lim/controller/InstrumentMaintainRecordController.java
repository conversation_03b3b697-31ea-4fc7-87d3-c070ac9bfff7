package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentMaintainRecordCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentMaintainRecord;
import com.sinoyd.lims.lim.service.InstrumentMaintainRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 仪器维护记录接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
@Api(tags = "仪器维护记录")
@Validated
@RestController
@RequestMapping("/api/lim/instrumentMaintainRecord")
public class InstrumentMaintainRecordController extends BaseJpaController<DtoInstrumentMaintainRecord, String, InstrumentMaintainRecordService>{

    /**
     * 根据id获取仪器维护记录
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id获取仪器维护记录", notes = "根据id获取仪器维护记录")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrumentMaintainRecord> find(@PathVariable(name = "id") String id){

        RestResponse<DtoInstrumentMaintainRecord> restResp = new RestResponse<>();
        DtoInstrumentMaintainRecord record = service.findOne(id);
        restResp.setData(record);

        restResp.setRestStatus(StringUtil.isNull(record) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }

    /**
     * 分页动态条件查询维护记录
     * @param criteria
     * @return
     */
    @ApiOperation(value = "分页动态条件查询维护记录", notes = "分页动态条件查询维护记录")
    @GetMapping
    public RestResponse<List<DtoInstrumentMaintainRecord>> findByPage(InstrumentMaintainRecordCriteria criteria){

        RestResponse<List<DtoInstrumentMaintainRecord>> restResp = new RestResponse<>();

        PageBean<DtoInstrumentMaintainRecord> page = super.getPageBean();
        service.findByPage(page, criteria);

        restResp.setRestStatus(StringUtil.isEmpty(page.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 新增仪器维护记录
     * @param entity
     * @return
     */
    @ApiOperation(value = "新增仪器维护记录", notes = "新增仪器维护记录")
    @PostMapping
    public RestResponse<DtoInstrumentMaintainRecord> save(@Validated @RequestBody DtoInstrumentMaintainRecord entity) {

        RestResponse<DtoInstrumentMaintainRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentMaintainRecord data = service.save(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 修改仪器维护记录
     * @param entity
     * @return
     */
    @ApiOperation(value = "修改仪器维护记录", notes = "修改仪器维护记录")
    @PutMapping
    public RestResponse<DtoInstrumentMaintainRecord> update(@Validated @RequestBody DtoInstrumentMaintainRecord entity) {

        RestResponse<DtoInstrumentMaintainRecord> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoInstrumentMaintainRecord data = service.update(entity);
        restResp.setData(data);
        restResp.setCount(1);

        return restResp;
    }

    /**
     * 根据id批量删除仪器维护记录
     * @param ids
     * @return
     */
    @ApiOperation(value = "根据id批量删除仪器维护记录", notes = "根据id批量删除仪器维护记录")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);

        return restResp;
    }

    /***
     * 获取维护单位
     * @return
     */
    @ApiOperation(value = "获取维护单位", notes = "获取维护单位")
    @GetMapping("/getMaintainDeptList")
    public RestResponse<List<DtoInstrumentMaintainRecord>> getMaintainDeptList() {
        RestResponse<List<DtoInstrumentMaintainRecord>> restResp = new RestResponse<>();
        List<DtoInstrumentMaintainRecord> list = service.getMaintainDeptList();

        restResp.setRestStatus(StringUtil.isEmpty(list) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(list);
        restResp.setCount(list.size());

        return restResp;
    }
}