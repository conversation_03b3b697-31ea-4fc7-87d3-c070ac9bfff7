package com.sinoyd.lims.od.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.od.dto.DtoLabSeq;

import java.util.List;
import java.util.Map;

/**
 * 实验次序操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
public interface LabSeqService extends IBaseJpaService<DtoLabSeq, String> {

    /**
     * 初始化实验组数据
     *
     * @param labSeq 实验组传输实体
     * @return 初始化实验组数据
     */
    DtoLabSeq initLab(DtoLabSeq labSeq);

    /**
     * 根据样品id 获取最大流水号
     *
     * @param sampleId 样品id
     * @return 流水号Map
     */
    Map<String, Object> findMaxSn(String sampleId);

    /**
     * 开始实验
     *
     * @param labSeq 实验次序实体
     */
    DtoLabSeq startLab(DtoLabSeq labSeq);

    /**
     * 刷新解答
     *
     * @param labSeqId 实验次序id
     * @return 实验次序
     */
    DtoLabSeq refreshAnswer(String labSeqId);

    /**
     * 根据实验组ids 查询
     *
     * @param groupIds 实验组ids
     * @return 实验次序集合
     */
    List<DtoLabSeq> findByGroupIdIn(List<String> groupIds);


    /**
     * 检查固定源任务，同一组中是否存在相同的稀释倍数的实验
     *
     * @param labSeq 实验次序实体
     * @return 相同稀释倍数的次序
     */
    String getExistsDilutionRat(DtoLabSeq labSeq);
}
