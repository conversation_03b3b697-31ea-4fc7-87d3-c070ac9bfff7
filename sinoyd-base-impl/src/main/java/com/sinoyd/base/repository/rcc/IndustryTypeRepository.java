package com.sinoyd.base.repository.rcc;

import java.util.List;

import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 行业类型仓储
 * <AUTHOR>
 * @version V1.0.0 2019/1/7
 * @since V100R001
 */
public interface IndustryTypeRepository extends IBaseJpaRepository<DtoIndustryType, String> {
    /**
     * 根据parentId获取行业类型
     *
     * @param parentId 行业类型parentId
     * @return 行业类型实体
     */
    List<DtoIndustryType> findByParentId(String parentId);

    /**
     * 根据名称与id获取重复的条数
     *
     * @param id
     * @param industryName
     * @return
     */
    @Query("select count(p) from DtoIndustryType p where p.id <> :id and p.industryName = :industryName and p.isDeleted = 0")
    Integer getCountByName(@Param("id") String id, @Param("industryName") String industryName);


    /**
     * 获取所有假删的行业信息
     *
     * @return 返回排除假删的数据
     */
    @Override
    @Query("select p from DtoIndustryType p where p.isDeleted = 0")
    List<DtoIndustryType> findAll();

    /**
     * 根据id数组获取信息
     *
     * @param ids 主键ids
     * @return 返回信息
     */
    @Query("select d from DtoIndustryType d where d.isDeleted = 0 and d.id in :ids")
    @Override
    List<DtoIndustryType> findAll(@Param("ids") Iterable<String> ids);


    /**
     * 返回所有的带假删的信息
     *
     * @return 返回带删除的信息
     */
    @Query("select p from DtoIndustryType p")
    List<DtoIndustryType> findAllDeleted();


    /**
     * 返回所有的带假删的信息
     *
     * @param ids 主键的ids
     * @return 返回带删除的信息
     */
    @Query("select p from DtoIndustryType p where p.id in :ids")
    List<DtoIndustryType> findAllDeleted(@Param("ids") List<String> ids);

    /**
     * 倒序排序所有的数据
     *
     * @return 返回想要的数据
     */
    @Query("select d from DtoIndustryType d where d.isDeleted = 0  order by  d.orderNum desc ")
    List<DtoIndustryType> findAllOrderByOrderNumDesc();

}