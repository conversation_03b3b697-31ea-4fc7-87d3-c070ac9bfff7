package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.FileGrantRecovery;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.Date;


/**
 * DtoFileGrantRecovery实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_FileGrantRecovery")
@Data
@DynamicInsert
public class DtoFileGrantRecovery extends FileGrantRecovery {


    @Transient
    private String materialPersonName;

    @Transient
    private String recoveryPerosnName;

    @Transient
    private String fileId;

    @Transient
    private String fileName;

    @Transient
    private String fileCode;

    @Transient
    private String controlCode;

    @Transient
    private String version;

    @Transient
    private String fileType;

    @Transient
    private String fileTypeName;

    @Transient
    private String maker;

    @Transient
    private String makerName;

    @Transient
    private Date compileTime;

    @Transient
    private Integer status;
}