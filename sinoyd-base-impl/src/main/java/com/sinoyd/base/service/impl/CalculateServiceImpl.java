package com.sinoyd.base.service.impl;

import com.sinoyd.base.service.CalculateService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.formula.FormulaReplacer;
import com.sinoyd.common.revise.ReviseDataFactory;
import com.sinoyd.frame.configuration.HttpServiceUrl;
import com.sinoyd.frame.service.impl.CalculationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 计算相关实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/6/30
 */
@Service
@Slf4j
@Primary
public class CalculateServiceImpl extends CalculationServiceImpl implements CalculateService {

    private HttpServiceUrl httpServiceUrl;
    private RestTemplate restTemplate;

    @Override
    public Object calculationExpression(List<String> formulas, Map<String, Object> params) {
        List<String> formulaList = new ArrayList<>();
        formulas.forEach(p -> {
            formulaList.add(FormulaReplacer.getInstance().replace(p, params));
        });
        //如果值是"/" 则把参数排除
        Map<String, Object> formulaParams = new HashMap<>();
        for (String key : params.keySet()) {
            Object value = params.get(key);
            if (!value.equals("/")) {
                formulaParams.put(key, params.get(key));
            }
        }
        return this.calculationExpression(formulaList, formulaParams, null);
    }

    @Override
    public Object calculationExpression(List<String> formulas, Map<String, Object> params, String token) {
        log.info("...... 开始远程调用进行公式计算 ......... ");
        log.info("...... 计算公式: {} ,参数: {} .......", formulas, params);
        try {
            String url = httpServiceUrl.getGateUrlPrefix() + "/tool/calculate";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            Map<String, Object> map = new HashMap<>();
            map.put("exp", String.join(",", formulas));
            map.put("params", params);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(map, headers);
            String json = restTemplate.postForEntity(url, request, String.class).getBody();
            if (StringUtil.isNotEmpty(json)) {
                if (json.startsWith("\"")) {
                    json = json.replaceFirst("\"", "");
                }
                if (json.endsWith("\"")) {
                    json = json.replaceFirst("\"", "");
                }
            }
            return json;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return "";
        }
    }

    /**
     * 修约
     *
     * @param significantDigits 有效位数
     * @param decimalDigits     小数位数
     * @param value             值
     * @return 修约后的值
     */
    @Override
    public String revise(int significantDigits, int decimalDigits, String value) {
        return ReviseDataFactory.revise(value, significantDigits, decimalDigits, Boolean.FALSE);
    }

    /**
     * 修约
     *
     * @param significantDigits 有效位数
     * @param decimalDigits     小数位数
     * @param value             值
     * @param isSci             是否强制修约
     * @return 修约后的值
     */
    @Override
    public String revise(int significantDigits, int decimalDigits, String value, Boolean isSci) {
        return ReviseDataFactory.revise(value, significantDigits, decimalDigits, isSci);
    }

    @Autowired
    public void setHttpServiceUrl(HttpServiceUrl httpServiceUrl) {
        this.httpServiceUrl = httpServiceUrl;
    }

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }
}