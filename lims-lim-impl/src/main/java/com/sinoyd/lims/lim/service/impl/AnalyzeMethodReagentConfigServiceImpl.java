package com.sinoyd.lims.lim.service.impl;

import java.io.Serializable;
import java.util.*;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.AuthorizeService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeMethodReagentConfig;
import com.sinoyd.lims.lim.repository.lims.AnalyzeMethodReagentConfigRepository;
import com.sinoyd.lims.lim.service.AnalyzeMethodReagentConfigService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 试剂配制记录接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-14
 * @since V100R001
 */
@Service
public class AnalyzeMethodReagentConfigServiceImpl
        extends BaseJpaPhysicalDeleteServiceImpl<DtoAnalyzeMethodReagentConfig, String, AnalyzeMethodReagentConfigRepository>
        implements AnalyzeMethodReagentConfigService {

    @Autowired
    private AuthorizeService authorizeService;

    /**
     * 根据analyzeMethodId获取试剂配制记录(备选) 留作备用,可能会需要根据analyzeMethodId获取
     * 若需要去接口添加方法,然后取消注释@Override即可(实现)
     *
     * @param analyzeMethodId
     * @return 试剂配制记录集合
     */
    // @Override
    public List<DtoAnalyzeMethodReagentConfig> getByAnalyzeMethodId(String analyzeMethodId) {
        return repository.getListByAnalyzeMethodId(analyzeMethodId);
    }

    /***
     * 分页获取试剂配制记录
     *
     * @param page
     * @param criteria
     */
    @Override
    public void findByPage(PageBean<DtoAnalyzeMethodReagentConfig> page, BaseCriteria criteria) {

        // 多表关联查询返回自定义字段
        page.setEntityName("DtoAnalyzeMethodReagentConfig analyzeMethodReagentConfig, DtoAnalyzeMethod analyzeMethod");
        page.setSelect("select analyzeMethodReagentConfig, analyzeMethod.methodName as redAnalyzeMethodName, analyzeMethod.countryStandard as redCountryStandard");

        super.findByPage(page, criteria);

        page.setSort("expiryDate-configDate-");

        List<DtoAnalyzeMethodReagentConfig> datas = page.getData();
        List<DtoAnalyzeMethodReagentConfig> newDatas = new ArrayList<>();

        Iterator<DtoAnalyzeMethodReagentConfig> item = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性
        Date now = new Date();
        while (item.hasNext()) {
            Object obj = item.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoAnalyzeMethodReagentConfig dto = (DtoAnalyzeMethodReagentConfig) objs[0];
            dto.setRedAnalyzeMethodName((String) objs[1]);
            dto.setRedCountryStandard((String) objs[2]);
            if (StringUtil.isNotNull(dto.getExpiryDate())) {
                if (now.before(dto.getExpiryDate())) {
                    dto.setStatus("否");
                } else {
                    dto.setStatus("是");
                }
            }
            newDatas.add(dto);
        }

        page.setData(newDatas);
    }

    @Transactional
    @Override
    public DtoAnalyzeMethodReagentConfig update(DtoAnalyzeMethodReagentConfig entity) {
        DtoAnalyzeMethodReagentConfig cfg = super.findOne(entity.getId());
        String userId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "";
        if (StringUtils.isNotNullAndEmpty(userId) && (cfg.getCreator().equals(userId) || authorizeService.haveActionPermission(userId, LimCodeHelper.REAGENT_CONFIG_ALL))) {
            return super.update(entity);
        } else {
            throw new BaseException("你没有权限修改其他人的试剂配置记录！");
        }
    }

    /**
     * 删除试剂配置
     */
    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        DtoAnalyzeMethodReagentConfig cfg = super.findOne(idStr);
        String userId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "";
        if (StringUtils.isNotNullAndEmpty(userId) && (cfg.getCreator().equals(userId) || authorizeService.haveActionPermission(userId, LimCodeHelper.REAGENT_CONFIG_ALL))) {
            return super.logicDeleteById(id);
        } else {
            throw new BaseException("你没有权限删除其他人的试剂配置记录！");
        }
    }

    /**
     * 批量删除试剂配置
     */
    @Transactional
    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        List<String> configIds = new ArrayList<>();
        for (Object id : ids) {
            configIds.add(String.valueOf(id));
        }
        List<DtoAnalyzeMethodReagentConfig> configList = super.findAll(configIds);
        String userId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getUserId() : "";
        if (StringUtils.isNotNullAndEmpty(userId) &&
                (configList.stream().allMatch(p -> p.getCreator().equals(userId)) ||
                        authorizeService.haveActionPermission(userId, LimCodeHelper.REAGENT_CONFIG_ALL))) {
            return this.repository.logicDeleteById(ids);
        } else {
            throw new BaseException("存在其他人的试剂配置，请核查！");
        }
    }
}