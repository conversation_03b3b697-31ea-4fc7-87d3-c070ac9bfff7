package com.sinoyd.lims.pro.service;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoOutSourceData;
import com.sinoyd.lims.pro.dto.customer.DtoImportOutSourceData;
import com.sinoyd.lims.pro.vo.OutSourceDataVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 分包数据接口
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/2/8
 **/
public interface OutSourceDataService extends IBaseJpaService<DtoOutSourceData, String>{


    /**
     * 查询列表中所有分析方法，并根据名称去重
     *
     * @return 分析方法map
     */
    Map<String, String> findAllAnalyzeMethod();

    /**
     * 批量操作中的批量修改
     *
     * @param vo 分包数据传输类
     * @return 分包数据list
     */
    List<DtoOutSourceData> updateBatch(OutSourceDataVO vo);

    /**
     * 查询所有量纲名称，并根据名称去重
     *
     * @return 量纲数据
     */
    Map<String, String> findAllDimensionName();

    /**
     * 导出excel
     *
     * @param criteria 分页条件
     * @param response 响应流
     */
    void exportExcel(BaseCriteria criteria, HttpServletResponse response);

    /**
     * 单个修改以及批量确认
     *
     * @param entitys 分包数据list
     * @return 分包数据list
     */
    List<DtoOutSourceData> update(List<DtoOutSourceData> entitys);

    /**
     * 处理导入表格
     * @param file 传入的文件
     */
    void importExcel(MultipartFile file, Map<Integer, Object> objectMap, HttpServletResponse response) throws Exception;

    /**
     * 获取文件需要导入的数据
     * @param file 传入的文件
     * @return List
     */
    ExcelImportResult<DtoImportOutSourceData> getExcelData(MultipartFile file, HttpServletResponse response) throws Exception;

}
