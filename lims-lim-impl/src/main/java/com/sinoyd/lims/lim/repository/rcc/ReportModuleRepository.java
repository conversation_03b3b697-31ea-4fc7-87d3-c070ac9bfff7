package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule;

import java.util.List;


/**
 * 报告组件
 *
 * <AUTHOR>
 * @version V1.0.0 2022/12/09
 * @since V100R001
 */
public interface ReportModuleRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportModule, String> {


    /**
     * 根据组件编码查询
     *
     * @param codeList 组件编码列表
     * @return 报告组件列表
     */
    List<DtoReportModule> findByModuleCodeIn(List<String> codeList);
}