package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.rcc.AnalyzeItemRepository;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.entity.ParamsConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.enums.EnumLIM.EnumParamsConfigType;
import com.sinoyd.lims.lim.repository.rcc.*;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 参数配置管理接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Service
public class ParamsConfigServiceImpl extends BaseJpaServiceImpl<DtoParamsConfig, String, ParamsConfigRepository> implements ParamsConfigService {


    @Autowired
    private ParamsRepository paramsRepository;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    private ParamsFormulaRepository paramsFormulaRepository;

    @Autowired
    private AnalyzeItemRepository analyzeItemRepository;

    @Autowired
    @Lazy
    private AnalyzeItemService analyzeItemService;

    @Autowired
    private RecordConfig2TestRepository recordConfig2TestRepository;

    @Autowired
    private RecordConfig2ParamsConfigRepository recordConfig2ParamsConfigRepository;

    @Autowired
    private RecordConfigRepository recordConfigRepository;

    @Autowired
    private Params2ParamsFormulaRepository params2ParamsFormulaRepository;

    @Autowired
    private ParamsTestFormulaRepository paramsTestFormulaRepository;

    @Autowired
    private ParamsPartFormulaRepository paramsPartFormulaRepository;

    /**
     * 分页获取参数配置信息
     *
     * @param pageBean       封装分页类
     * @param paramsCriteria 查询条件
     */
    @Override
    //@SuppressWarnings("rawtypes")//对泛型的引用应当进行参数配置化,可以使用此注解抑制编译器产生警告信息
    public void findByPage(PageBean<DtoParamsConfig> pageBean, BaseCriteria paramsCriteria) {
        pageBean.setEntityName("DtoParamsConfig p");
        pageBean.setSelect("select p");
        super.findByPage(pageBean, paramsCriteria);

        List<DtoParamsConfig> list = pageBean.getData();

        List<String> paramsIds = list.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getParamsId())
                        && !UUIDHelper.GUID_EMPTY.equals(p.getParamsId()))
                .map(ParamsConfig::getParamsId).collect(Collectors.toList());
        List<DtoParams> paramsList = new ArrayList<>();
        if (paramsIds.size() > 0) {
            paramsList = paramsRepository.getListByIds(paramsIds);
        }

        List<String> formulaIds = list.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getFormulaId())
                        && !UUIDHelper.GUID_EMPTY.equals(p.getFormulaId()))
                .map(ParamsConfig::getFormulaId).collect(Collectors.toList());
        List<DtoParamsFormula> formulaList = new ArrayList<>();
        if (formulaIds.size() > 0) {
            formulaList = paramsFormulaRepository.findAll(formulaIds);
        }

        List<String> analyzeItemIds = list.stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getAnalyzeItemId())
                        && !UUIDHelper.GUID_EMPTY.equals(p.getAnalyzeItemId()))
                .map(ParamsConfig::getAnalyzeItemId).collect(Collectors.toList());
        List<DtoAnalyzeItem> analyzeItemList = new ArrayList<>();
        if (analyzeItemIds.size() > 0) {
            analyzeItemList = analyzeItemRepository.findAll(analyzeItemIds);
        }

        for (DtoParamsConfig paramConfig : list) {

            if (StringUtils.isNotNullAndEmpty(paramConfig.getParamsId())
                    && !UUIDHelper.GUID_EMPTY.equals(paramConfig.getParamsId())) {
                Optional<DtoParams> ap = paramsList.stream().filter(p -> paramConfig.getParamsId().contains(p.getId())).findFirst();
                if (ap.isPresent()) {
                    DtoParams params = ap.get();
                    paramConfig.setParamName(params.getParamName());
                }
            }

            if (StringUtils.isNotNullAndEmpty(paramConfig.getFormulaId())
                    && !UUIDHelper.GUID_EMPTY.equals(paramConfig.getFormulaId())) {
                Optional<DtoParamsFormula> ap = formulaList.stream().filter(p -> paramConfig.getFormulaId().contains(p.getId())).findFirst();
                if (ap.isPresent()) {
                    DtoParamsFormula formula = ap.get();
                    paramConfig.setParamFormula(formula.getFormula());
                }
            }

            if (StringUtils.isNotNullAndEmpty(paramConfig.getAnalyzeItemId())
                    && !UUIDHelper.GUID_EMPTY.equals(paramConfig.getAnalyzeItemId())) {
                Optional<DtoAnalyzeItem> ap = analyzeItemList.stream().filter(p -> paramConfig.getAnalyzeItemId().contains(p.getId())).findFirst();
                if (ap.isPresent()) {
                    DtoAnalyzeItem analyzeItem = ap.get();
                    paramConfig.setAnalyzeItemName(analyzeItem.getAnalyzeItemName());
                }
            }
        }

        pageBean.setData(list);// 重新封装到pageBean返回到上层
    }


    @Override
    public List<DtoParamsConfig> findSampleTypeParams(String objId, String parentObjectId, Integer type) {
        List<DtoParamsConfig> paramsConfigs = repository.findByObjIdAndType(objId, type, UUIDHelper.GUID_EMPTY);
        List<DtoParamsConfig> paramsConfigsAll = new ArrayList<>(paramsConfigs);
        //参数id 一样的过滤掉
        List<String> paramIds = paramsConfigs.stream().map(DtoParamsConfig::getParamsId).distinct().collect(Collectors.toList());

        if (StringUtils.isNotNullAndEmpty(parentObjectId) && !parentObjectId.equals(UUIDHelper.GUID_EMPTY)) {
            if (paramIds.size() > 0) {
                paramsConfigsAll.addAll(repository.findByObjIdAndTypeAndParamsIdNotIn(parentObjectId, type, UUIDHelper.GUID_EMPTY, paramIds));
            } else {
                paramsConfigsAll.addAll(repository.findByObjIdAndType(parentObjectId, type, UUIDHelper.GUID_EMPTY));
            }
        }
        List<String> paramsIds = paramsConfigsAll.stream().map(DtoParamsConfig::getParamsId).distinct().collect(Collectors.toList());

        List<DtoParams> paramsList = new ArrayList<>();
        if (paramsIds.size() > 0) {
            paramsList = paramsRepository.getListByIds(paramsIds);
        }
        for (DtoParamsConfig paramConfig : paramsConfigsAll) {
            Optional<DtoParams> ap = paramsList.stream().filter(p -> paramConfig.getParamsId().contains(p.getId())).findFirst();
            if (ap.isPresent()) {
                DtoParams params = ap.get();
                paramConfig.setParamName(params.getParamName());
            }
        }
        //倒序排序
        paramsConfigsAll = paramsConfigsAll.stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());
        return paramsConfigsAll;
    }

    /**
     * 针对检测类型的参数特殊处理
     *
     * @param objIds          当前数据id
     * @param parentObjectIds 父级的id
     * @param type            类型
     * @return 返回相应的参数数据
     */
    @Override
    public List<DtoParamsConfig> findSampleTypeParams(List<String> objIds, List<String> parentObjectIds, Integer type) {
        //获取所有的检测类型小类
        List<DtoSampleType> sonSampleTypes = sampleTypeService.findAll(objIds);
        List<DtoSampleType> parentSampleTypes = sampleTypeService.findAll(parentObjectIds);
        List<DtoParamsConfig> paramsConfigs = repository.findByObjIdInAndType(objIds, type, UUIDHelper.GUID_EMPTY);
        List<DtoParamsConfig> paramsConfigsAll = new ArrayList<>();
        //参数id 一样的过滤掉
        List<String> paramIds = paramsConfigs.stream().map(DtoParamsConfig::getParamsId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(parentObjectIds)) {
            parentObjectIds = parentObjectIds.stream().distinct().collect(Collectors.toList());
            if (paramIds.size() > 0) {
                paramsConfigs.addAll(repository.findByObjIdInAndTypeAndParamsIdNotIn(parentObjectIds, type, UUIDHelper.GUID_EMPTY, paramIds));
            } else {
                paramsConfigs.addAll(repository.findByObjIdInAndType(parentObjectIds, type, UUIDHelper.GUID_EMPTY));
            }
        }
        List<String> paramsIds = paramsConfigs.stream().map(DtoParamsConfig::getParamsId).distinct().collect(Collectors.toList());

        List<DtoParams> paramsList = new ArrayList<>();
        if (paramsIds.size() > 0) {
            paramsList = paramsRepository.getListByIds(paramsIds);
        }
        for (DtoParamsConfig paramConfig : paramsConfigs) {
            Optional<DtoParams> ap = paramsList.stream().filter(p -> paramConfig.getParamsId().contains(p.getId())).findFirst();
            if (ap.isPresent()) {
                DtoParams params = ap.get();
                paramConfig.setParamName(params.getParamName());
            }
        }
        //处理小类名称
        fillSampleTypes(paramsConfigs, sonSampleTypes, paramsConfigsAll);
        //处理大类名称
        fillSampleTypes(paramsConfigs, parentSampleTypes, paramsConfigsAll);
        //倒序排序
        paramsConfigsAll = paramsConfigsAll.stream().sorted(Comparator.comparing(DtoParamsConfig::getSampleTypeOrder).thenComparing(DtoParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());
        return paramsConfigsAll;
    }

    @Override
    @Transactional
    public void formulaSync(String recordConfigId,List<String> paramsConfigIdList) {
        //待配置的数据参数列表
        List<DtoParamsConfig> paramsConfigList = repository.findAll(paramsConfigIdList);
        // 待更新配置列表
        List<DtoParams2ParamsFormula> waitSaveList = new ArrayList<>();
        // 所有记录单与测试项目关系
        List<DtoRecordConfig2Test> recordConfig2TestList = recordConfig2TestRepository.findByRecordConfigId(recordConfigId);
        // 所有测试项目
        List<String> testIds = recordConfig2TestList.stream().map(DtoRecordConfig2Test::getTestId).distinct().collect(Collectors.toList());
        // 所有测试项目公式
        List<DtoParamsFormula> paramsFormulaList = (!testIds.isEmpty())?paramsFormulaRepository.findByObjectIds(testIds):new ArrayList<>();
        paramsFormulaList = paramsFormulaList.stream().filter(p->EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue().equals(p.getObjectType())).collect(Collectors.toList());
        List<String> testFormulaIdList = paramsFormulaList.stream().map(DtoParamsFormula::getId).collect(Collectors.toList());
        //所有测试项目公式 参数
        List<DtoParamsTestFormula> paramsTestFormulaList =(!testFormulaIdList.isEmpty())? paramsTestFormulaRepository.findByObjIdIn(testFormulaIdList):new ArrayList<>();
        //所有记录单参数对应测试项目公式的个性化配置
        List<DtoParams2ParamsFormula> params2ParamsFormulaList = params2ParamsFormulaRepository.findByRecordId(recordConfigId);
        for (DtoParamsConfig paramsConfig:paramsConfigList ) {
            collectWaitSaveList(recordConfigId,waitSaveList,paramsConfig,paramsFormulaList,paramsTestFormulaList,params2ParamsFormulaList);
        }
        //配置更新
        params2ParamsFormulaRepository.save(waitSaveList);
    }

    /**
     * 个性化公式同步测试项目参数公式
     * @param recordConfigId              记录单标识
     * @param waitSaveList                待同步列表
     * @param paramsConfig                数据参数
     * @param paramsFormulaList           测试项目公式
     * @param paramsTestFormulaList       测试项目公式参数列表
     * @param params2ParamsFormulaList    个性化公式列表
     */
    private void collectWaitSaveList( String recordConfigId,List<DtoParams2ParamsFormula> waitSaveList,DtoParamsConfig paramsConfig,
                                      List<DtoParamsFormula> paramsFormulaList,List<DtoParamsTestFormula> paramsTestFormulaList,
                                      List<DtoParams2ParamsFormula> params2ParamsFormulaList){
        for (DtoParamsFormula dtoParamsFormula :paramsFormulaList) {
            //本测试项目公式的所有参数
            List<DtoParamsTestFormula> dtoParamsTestFormulaList = paramsTestFormulaList.stream().filter(t->dtoParamsFormula.getId().equals(t.getObjId())).collect(Collectors.toList());
            DtoParamsTestFormula existParamsTestFormula = dtoParamsTestFormulaList.stream().filter(t->paramsConfig.getAlias().equals(t.getParamsName())).findFirst().orElse(null);
            // 包含同名参数采同步配置
            if (existParamsTestFormula!=null){
                DtoParams2ParamsFormula dtoParams2ParamsFormula = params2ParamsFormulaList.stream().filter(p->paramsConfig.getId().equals(p.getParamsConfigId())
                        &&dtoParamsFormula.getId().equals(p.getObjectId()))
                        .findFirst().orElse(null);
                if(dtoParams2ParamsFormula==null){
                    dtoParams2ParamsFormula = new DtoParams2ParamsFormula();
                    dtoParams2ParamsFormula.setRecordId(recordConfigId);
                    dtoParams2ParamsFormula.setParamsConfigId(paramsConfig.getId());
                    dtoParams2ParamsFormula.setObjectId(dtoParamsFormula.getId());
                    dtoParams2ParamsFormula.setIsEnabled(true);
                }
                dtoParams2ParamsFormula.setFormula("["+existParamsTestFormula.getParamsName()+"]");
                waitSaveList.add(dtoParams2ParamsFormula);
            }
        }
    }

    /**
     * 处理参数中的检测类型数据
     *
     * @param paramsConfigs   参数集合
     * @param sampleTypes     检测类型数据集合
     * @param paramsConfigAll 处理后参数集合
     */
    private void fillSampleTypes(List<DtoParamsConfig> paramsConfigs, List<DtoSampleType> sampleTypes, List<DtoParamsConfig> paramsConfigAll) {
        for (DtoSampleType sonSampleType : sampleTypes) {
            List<DtoParamsConfig> paramsConfigsOfType = paramsConfigs.stream().filter(p -> sonSampleType.getId().equals(p.getObjId())).collect(Collectors.toList());
            paramsConfigsOfType.forEach(p -> {
                p.setSampleTypeOrder(sonSampleType.getOrderNum());
                p.setSampleTypeName(sonSampleType.getTypeName());
            });
            paramsConfigAll.addAll(paramsConfigsOfType);
        }
    }

    /**
     * 新增参数配置信息
     *
     * @param params
     * @return
     */
    @Transactional
    @Override
    public DtoParamsConfig save(DtoParamsConfig params) {
        if (StringUtils.isNotNullAndEmpty(params.getAnalyzeItemId()) && !UUIDHelper.GUID_EMPTY.equals(params.getAnalyzeItemId())) {
            // 判断别名是否重复
            if (repository.getCountByAnalyzeItemIdAndId(params.getId(), params.getObjId(), params.getAnalyzeItemId()) > 0) {
                throw new BaseException("已存在相同的分析项目！");
            }
        } else {
            // 判断别名是否重复
            if (repository.getCountByName(params.getAlias(), params.getObjId()) > 0) {
                throw new BaseException("已存在相同别名的参数！");
            }
        }
        if (StringUtil.isNotNull(params.getParamsType())) {
            if (EnumLIM.EnumParamsType.公共参数.getValue().equals(params.getParamsType()) ||
                    EnumLIM.EnumParamsType.点位参数.getValue().equals(params.getParamsType())) {
                if (params.getIsFormula()) {
                    throw new BaseException(params.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())
                            ? "公共参数无法配置公式！" : "点位参数无法配置公式！");
                }
                if (params.getMostSignificance() != -1 || params.getMostDecimal() != -1) {
                    throw new BaseException(params.getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue()) ? "公共参数无法配置有效位数和小数位数！" : "点位参数无法配置有效位数和小数位数！");
                }
            }
        }
        if (StringUtils.isNotNullAndEmpty(params.getParentId()) && !UUIDHelper.GUID_EMPTY.equals(params.getParentId())) {
            DtoParamsConfig parentParamsConfig = repository.findOne(params.getParentId());
            params.setParamsId(parentParamsConfig.getParamsId());
            params.setAlias(parentParamsConfig.getAlias());
            params.setParamsType(parentParamsConfig.getParamsType());
            params.setDefaultControl(parentParamsConfig.getDefaultControl());
            params.setType(parentParamsConfig.getType());
            params.setDimensionId(parentParamsConfig.getDimensionId());
            params.setDimension(parentParamsConfig.getDimension());
            params.setDataSource(parentParamsConfig.getDataSource());
        }
        DtoParamsConfig config = super.save(params);
        if (StringUtil.isNotEmpty(params.getAnaParamsConfigList())) {

            for (DtoParamsConfig item : params.getAnaParamsConfigList()) {
                DtoParamsConfig newItem = new DtoParamsConfig();
                newItem.setObjId(config.getObjId());
                newItem.setParamsId(config.getParamsId());
                newItem.setAlias(config.getAlias());
                newItem.setParamsType(config.getParamsType());
                newItem.setDefaultControl(config.getDefaultControl());
                newItem.setType(config.getType());
                newItem.setDimensionId(config.getDimensionId());
                newItem.setDimension(config.getDimension());
                newItem.setDataSource(config.getDataSource());
                newItem.setDefaultValue(item.getDefaultValue());
                newItem.setAnalyzeItemId(item.getAnalyzeItemId());
                newItem.setMostSignificance(item.getMostSignificance());
                newItem.setMostDecimal(item.getMostDecimal());
                newItem.setOrderNum(item.getOrderNum());
                newItem.setIsShow(item.getIsShow());
                newItem.setIsRequired(item.getIsRequired());
                newItem.setIsFormula(item.getIsFormula());
                newItem.setParentId(config.getId());
                super.save(newItem);
            }
        }
        return config;
    }

    /**
     * 更新参数配置信息
     *
     * @param params
     * @return
     */
    @Transactional
    @Override
    public DtoParamsConfig updateConfig(DtoParamsConfig params) {
        // 判断别名是否重复
        if (repository.getCountByNameAndId(params.getAlias(), params.getId(), params.getObjId()) > 0) {
            throw new BaseException("已存在相同别名的参数！");
        }

        List<DtoParamsConfig> paramsConfigs = repository.findByParentId(params.getId());
        List<String> deleteIds = paramsConfigs.stream().map(DtoParamsConfig::getId).distinct().collect(Collectors.toList());
        if (EnumLIM.EnumParamsType.公共参数.getValue().equals(params.getParamsType()) ||
                EnumLIM.EnumParamsType.点位参数.getValue().equals(params.getParamsType())) {
            if (params.getIsFormula()) {
                throw new BaseException(EnumLIM.EnumParamsType.公共参数.getValue().equals(params.getParamsType()) ? "公共参数无法配置公式！" : "点位参数无法配置公式！");
            }
            if (params.getMostSignificance() != -1 || params.getMostDecimal() != -1) {
                throw new BaseException(EnumLIM.EnumParamsType.公共参数.getValue().equals(params.getParamsType()) ? "公共参数无法配置有效位数和小数位数！" : "点位参数无法配置有效位数和小数位数！");
            }
        }
        if (StringUtil.isNotEmpty(params.getAnaParamsConfigList())) {
            List<DtoParamsConfig> list = new ArrayList<>();
            List<String> ids = new ArrayList<>();
            for (DtoParamsConfig item : params.getAnaParamsConfigList()) {
                //老的数据
                DtoParamsConfig oldParamsConfig = paramsConfigs.stream().filter(p -> p.getId().equals(item.getId())).findFirst().orElse(null);
                if (StringUtil.isNotNull(oldParamsConfig)) { //这是做修改数据
                    Boolean isChange = false;
                    if (!params.getObjId().equals(oldParamsConfig.getObjId())) {
                        isChange = true;
                    }
                    if (!params.getParamsId().equals(oldParamsConfig.getParamsId())) {
                        isChange = true;
                    }
                    if (!params.getAlias().equals(oldParamsConfig.getAlias())) {
                        isChange = true;
                    }
                    if (!params.getParamsType().equals(oldParamsConfig.getParamsType())) {
                        isChange = true;
                    }
                    if (!params.getDefaultControl().equals(oldParamsConfig.getDefaultControl())) {
                        isChange = true;
                    }
                    if (!params.getDimensionId().equals(oldParamsConfig.getDimensionId())) {
                        isChange = true;
                    }
                    if (StringUtil.isEmpty(params.getDimension()) && StringUtil.isNotEmpty(oldParamsConfig.getDimension())) {
                        isChange = true;
                    } else if (StringUtil.isNotEmpty(params.getDimension()) && StringUtil.isEmpty(oldParamsConfig.getDimension())) {
                        isChange = true;
                    } else if (StringUtil.isEmpty(params.getDimension()) && StringUtil.isEmpty(oldParamsConfig.getDimension())) {
                        isChange = false;
                    } else if (!params.getDimension().equals(oldParamsConfig.getDimension())) {
                        isChange = true;
                    }

                    if (StringUtil.isEmpty(params.getDataSource()) && StringUtil.isNotEmpty(oldParamsConfig.getDataSource())) {
                        isChange = true;
                    } else if (StringUtil.isNotEmpty(params.getDataSource()) && StringUtil.isEmpty(oldParamsConfig.getDataSource())) {
                        isChange = true;
                    } else if (StringUtil.isEmpty(params.getDataSource()) && StringUtil.isEmpty(oldParamsConfig.getDataSource())) {
                        isChange = false;
                    } else if (!params.getDataSource().equals(oldParamsConfig.getDataSource())) {
                        isChange = true;
                    }

                    if (StringUtil.isEmpty(params.getDefaultValue()) && StringUtil.isNotEmpty(oldParamsConfig.getDefaultValue())) {
                        isChange = true;
                    } else if (StringUtil.isNotEmpty(params.getDefaultValue()) && StringUtil.isEmpty(oldParamsConfig.getDefaultValue())) {
                        isChange = true;
                    } else if (StringUtil.isEmpty(params.getDefaultValue()) && StringUtil.isEmpty(oldParamsConfig.getDefaultValue())) {
                        isChange = false;
                    } else if (!params.getDefaultValue().equals(oldParamsConfig.getDefaultValue())) {
                        isChange = true;
                    }

                    if (!params.getAnalyzeItemId().equals(oldParamsConfig.getAnalyzeItemId())) {
                        isChange = true;
                    }
                    if (!params.getMostSignificance().equals(oldParamsConfig.getMostSignificance())) {
                        isChange = true;
                    }
                    if (!params.getMostDecimal().equals(oldParamsConfig.getMostDecimal())) {
                        isChange = true;
                    }
                    if (!params.getOrderNum().equals(oldParamsConfig.getOrderNum())) {
                        isChange = true;
                    }
                    if (!params.getIsShow().equals(oldParamsConfig.getIsShow())) {
                        isChange = true;
                    }
                    if (!params.getIsRequired().equals(oldParamsConfig.getIsRequired())) {
                        isChange = true;
                    }
                    if (!params.getIsFormula().equals(oldParamsConfig.getIsFormula())) {
                        isChange = true;
                    }
                    if (!params.getIsFormula().equals(oldParamsConfig.getIsFormula())) {
                        isChange = true;
                    }
                    if (isChange) {
                        comRepository.merge(item);
                    }
                    ids.add(item.getId());
                } else {
                    DtoParamsConfig newItem = new DtoParamsConfig();
                    newItem.setObjId(params.getObjId());
                    newItem.setParamsId(params.getParamsId());
                    newItem.setAlias(params.getAlias());
                    newItem.setParamsType(params.getParamsType());
                    newItem.setDefaultControl(params.getDefaultControl());
                    newItem.setType(params.getType());
                    newItem.setDimensionId(params.getDimensionId());
                    newItem.setDimension(params.getDimension());
                    newItem.setDataSource(params.getDataSource());
                    newItem.setDefaultValue(item.getDefaultValue());
                    newItem.setAnalyzeItemId(item.getAnalyzeItemId());
                    newItem.setMostSignificance(item.getMostSignificance());
                    newItem.setMostDecimal(item.getMostDecimal());
                    newItem.setOrderNum(item.getOrderNum());
                    newItem.setIsShow(item.getIsShow());
                    newItem.setIsRequired(item.getIsRequired());
                    newItem.setIsFormula(item.getIsFormula());
                    newItem.setParentId(params.getId());
                    list.add(newItem);
                }
            }
            if (list.size() > 0) {
                super.save(list);
            }
            deleteIds = deleteIds.stream().filter(p -> !ids.contains(p)).collect(Collectors.toList());
        }
        if (deleteIds.size() > 0) {
            super.logicDeleteById(deleteIds);
        }
        return super.update(params);
    }

    /**
     * 更新参数配置信息
     *
     * @param params 更新参数
     * @return 返回更新参数信息
     */
    @Transactional
    @Override
    public DtoParamsConfig update(DtoParamsConfig params) {
        if (StringUtils.isNotNullAndEmpty(params.getAnalyzeItemId())
                && !params.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY)) {
            // 判断别名是否重复
            if (repository.getCountByAnalyzeItemIdAndId(params.getId(), params.getObjId(), params.getAnalyzeItemId()) > 0) {
                throw new BaseException("已存在相同的分析项目！");
            }
        } else {
            // 判断别名是否重复
            if (repository.getCountByNameAndId(params.getAlias(), params.getId(), params.getObjId()) > 0) {
                throw new BaseException("已存在相同别名的参数！");
            }
        }
        return super.update(params);
    }

    /**
     * 根据检测小类id获取参数配置信息
     *
     * @param sampleTypeId 检测小类id
     * @return 对应检测小类id下的参数配置信息
     */
    @Override
    public List<DtoParamsConfig> findBySampleTypeId(String sampleTypeId) {
        DtoSampleType samType = sampleTypeService.findOne(sampleTypeId);
        List<String> sampleTypeIds = new ArrayList<>();
        sampleTypeIds.add(sampleTypeId);
        if (StringUtil.isNotNull(samType) && StringUtils.isNotNullAndEmpty(samType.getParentId())) {
            sampleTypeIds.add(samType.getParentId());
        }

        List<DtoParamsConfig> paramsConfigs = this.findByObjIdInAndType(sampleTypeIds, EnumParamsConfigType.样品参数.getValue());

        List<String> typeIds = sampleTypeIds.stream().map(p -> String.format("%s%s", p, p)).collect(Collectors.toList());
        Comparator<DtoParamsConfig> comparator = (a, b) -> {
            return typeIds.contains(String.format("%s%s", a.getObjId(), b.getObjId())) ? b.getOrderNum().compareTo(a.getOrderNum()) : (a.getObjId().equals(sampleTypeId) ? -1 : 1);
        };
        return paramsConfigs.stream().sorted(comparator).
                collect(Collectors.groupingBy(DtoParamsConfig::getAlias, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values()
                .stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum).reversed()).filter(DtoParamsConfig::getIsShow).collect(Collectors.toList());
    }

    /**
     * 根据检测小类id及分析项目id集合获取参数配置信息
     *
     * @param sampleTypeId   检测小类id
     * @param analyzeItemIds 分析项目id集合
     * @return 对应检测小类id和分析项目id集合下的参数配置信息
     */
    @Override
    public List<DtoParamsConfig> findBySampleTypeId(String sampleTypeId, Collection<String> analyzeItemIds) {
        List<DtoParamsConfig> paramsConfigs = this.findBySampleTypeId(sampleTypeId);
        List<String> paramsConfigIds = paramsConfigs.stream().filter(p -> p.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue())).map(DtoParamsConfig::getId).collect(Collectors.toList());

        if (paramsConfigIds.size() > 0) {
            List<DtoParamsConfig> analyzeItemCfgs = this.findByParentIdInAndAnalyzeItemIdInAndIsShowTrue(paramsConfigIds, analyzeItemIds);
            if (StringUtil.isNotNull(analyzeItemCfgs)) {//有分析项目参数才进行插入
                if (StringUtil.isEmpty(analyzeItemIds)) {//若没有传分析项目id集合，则返回所有的
                    analyzeItemIds = analyzeItemCfgs.stream().map(DtoParamsConfig::getAnalyzeItemId).distinct().collect(Collectors.toList());
                }
                Map<String, String> analyzeNameMap = new HashMap<>();
                for (String analyzeItemId : analyzeItemIds) {
                    DtoAnalyzeItem item = analyzeItemService.findOne(analyzeItemId);
                    if (StringUtil.isNotNull(item)) {
                        analyzeNameMap.put(analyzeItemId, item.getAnalyzeItemName());
                    }
                }
                Map<String, List<DtoParamsConfig>> analyzeItemCfgMap = analyzeItemCfgs.stream().collect(Collectors.groupingBy(DtoParamsConfig::getParentId));
                List<DtoParamsConfig> list = new ArrayList<>();
                for (DtoParamsConfig paramsConfig : paramsConfigs) {
                    list.add(paramsConfig);
                    if (analyzeItemCfgMap.containsKey(paramsConfig.getId())) {//若有子级分析项目参数，插到后面
                        List<DtoParamsConfig> configs = analyzeItemCfgMap.get(paramsConfig.getId());
                        for (DtoParamsConfig cfg : configs) {
                            if (analyzeNameMap.containsKey(cfg.getAnalyzeItemId())) {//若没有分析因子，说明该分析因子已被假删，不显示
                                DtoParamsConfig newCfg = new DtoParamsConfig();
                                BeanUtils.copyProperties(cfg, newCfg);
                                newCfg.setParamsName(analyzeNameMap.get(cfg.getAnalyzeItemId())
                                        + (StringUtils.isNotNullAndEmpty(cfg.getParamsName()) ? cfg.getParamsName() : ""));
                                newCfg.setAlias(analyzeNameMap.get(cfg.getAnalyzeItemId()) + cfg.getAlias());
                                newCfg.setDimensionId(paramsConfig.getDimensionId());
                                newCfg.setDimension(paramsConfig.getDimension());
                                list.add(newCfg);
                            }
                        }
                    }
                }

                return list;
            }
        }

        return paramsConfigs;
    }

    /**
     * 根据样品类型和测试项目获取参数
     *
     * @param sampleTypeId 检测小类id
     * @param testIds      测试项目id
     * @return 参数信息
     */
    @Override
    public List<DtoParamsConfig> findByTypeIdAndTestIds(String sampleTypeId, List<String> testIds) {
        //通过小类获取大类的样品类型
        DtoSampleType samType = sampleTypeService.findOne(sampleTypeId);
        List<String> sampleTypeIds = new ArrayList<>();
        sampleTypeIds.add(sampleTypeId);
        if (StringUtil.isNotNull(samType) && StringUtils.isNotNullAndEmpty(samType.getParentId())) {
            sampleTypeIds.add(samType.getParentId());
        }

        List<DtoParamsConfig> paramsConfigList = new ArrayList<>();
        List<String> paramsIds = new ArrayList<>();
        //通过样品类型和测试项目获取报表配置
        List<String> recordIds = recordConfig2TestRepository.findByTestIdIn(testIds).stream()
                .map(DtoRecordConfig2Test::getRecordConfigId).distinct().collect(Collectors.toList());
        if (recordIds.size() > 0) {
            List<DtoRecordConfig> recordConfigList = recordConfigRepository.findBySampleTypeIdInAndIdIn(sampleTypeIds, recordIds);
            List<String> recIds = recordConfigList.stream().map(DtoRecordConfig::getId).collect(Collectors.toList());
            //通过报表配置获取配置的参数
            if (recIds.size() > 0) {
                paramsIds = recordConfig2ParamsConfigRepository.findByRecordConfigIdIn(recIds).stream()
                        .map(DtoRecordConfig2ParamsConfig::getParamsConfigId).distinct().collect(Collectors.toList());
            }
        }
        //如果配置的参数为空的，那么获取样品类型配置的参数信息
        if (paramsIds.size() > 0) {
            paramsConfigList = repository.findAll(paramsIds);
            List<String> pIds = paramsConfigList.stream().map(DtoParamsConfig::getParamsId).distinct().collect(Collectors.toList());
            List<DtoParams> paramsList = paramsRepository.findAll(pIds);
            paramsConfigList.forEach(config -> {
                Optional<DtoParams> params = paramsList.stream().filter(p -> p.getId().equals(config.getParamsId())).findFirst();
                if (params.isPresent()) {
                    config.setParamsName(params.get().getParamName());
                } else {
                    config.setParamsName(config.getAlias());
                }
            });
        } else {
            paramsConfigList = this.findBySampleTypeId(sampleTypeId);
        }
        Set<String> typeIds = paramsConfigList.stream().map(DtoParamsConfig::getObjId).collect(Collectors.toSet());
        //给参数进行排序
        List<String> samTypeIds = typeIds.stream().map(p -> String.format("%s%s", p, p)).collect(Collectors.toList());
        Comparator<DtoParamsConfig> comparator = (a, b) -> {
            return samTypeIds.contains(String.format("%s%s", a.getObjId(), b.getObjId())) ?
                    b.getOrderNum().compareTo(a.getOrderNum()) : (a.getObjId().equals(sampleTypeId) ? -1 : 1);
        };
        return paramsConfigList.stream().sorted(comparator).
                collect(Collectors.groupingBy(DtoParamsConfig::getAlias, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))).values()
                .stream().sorted(Comparator.comparing(DtoParamsConfig::getOrderNum).reversed()).filter(DtoParamsConfig::getIsShow).collect(Collectors.toList());
    }

    /**
     * 根据父级id集合、分析项目id集合获取配置信息
     *
     * @param parentIds      父级id集合
     * @param analyzeItemIds 分析项目id集合
     * @return 对应父级id集合、分析项目id集合获取配置信息
     */
    @Override
    public List<DtoParamsConfig> findByParentIdInAndAnalyzeItemIdInAndIsShowTrue(Collection<String> parentIds, Collection<String> analyzeItemIds) {
        if (analyzeItemIds.size() > 0) {
            return repository.findByParentIdInAndAnalyzeItemIdInAndIsShowTrue(parentIds, analyzeItemIds);
        } else {
            return repository.findByParentIdInAndIsShowTrue(parentIds);
        }
    }

    /**
     * 根据关联id集合获取参数配置信息
     *
     * @param objIds 关联id集合
     * @param type   关联类型
     * @return 对应关联id集合下的参数配置信息
     */
    @Override
    public List<DtoParamsConfig> findByObjIdInAndType(List<String> objIds, Integer type) {
        List<DtoParamsConfig> configList = repository.findByObjIdInAndTypeAndParentId(objIds, type, UUIDHelper.GUID_EMPTY);
        if (StringUtil.isNotEmpty(configList)) {
            List<String> paramIds = configList.stream().map(DtoParamsConfig::getParamsId).distinct().collect(Collectors.toList());
            List<DtoParams> paramsList = paramsRepository.findAll(paramIds);
            for (DtoParamsConfig config : configList) {
                Optional<DtoParams> paramsOptional = paramsList.stream().filter(p -> p.getId().equals(config.getParamsId()))
                        .findFirst();
                paramsOptional.ifPresent(p -> config.setParamsName(p.getParamName()));
            }
        }
        return configList;
    }

    @Override
    public List<DtoParamsConfig> findAllDeleted(List<String> list) {
        return null;
    }

    @Override
    public List<DtoParamsConfig> findAllDeleted() {
        return null;
    }


    @Transactional
    @Override
    public void copyParamsConfig(String copyTestId, List<String> testIds) {
        if (StringUtil.isNull(testIds) || testIds.size() <= 0) {
            throw new BaseException("请选择需要复制的数据！");
        }
        // 源测试项目参数数据
        List<DtoParamsConfig> paramsConfigs = repository.findByObjIdAndType(copyTestId, EnumParamsConfigType.分析项目参数.getValue());

        // 待复制的数据
        List<DtoParamsConfig> paramsConfigAll = repository.findByObjIdInAndType(testIds, EnumParamsConfigType.分析项目参数.getValue());
        // 新增的参数配置
        List<DtoParamsConfig> saveParamsConfigs = new ArrayList<>();
        // 进行修改的参数配置
        List<DtoParamsConfig> updateParamsConfigs = new ArrayList<>();
        // 进行复制
        for (String testId : testIds) {
            //已存在的参数数据
            List<DtoParamsConfig> existParamsConfigList = paramsConfigAll.stream().filter(p -> p.getObjId().equals(testId)).collect(Collectors.toList());
            //获取已经存在参数的名称集合
            List<String> aliasList = existParamsConfigList.stream().map(DtoParamsConfig::getAlias).collect(Collectors.toList());

            //目标测试项目参数配置,其他的参数数据(需要新增的参数数据)
            List<DtoParamsConfig> othersParamsConfigList = paramsConfigs.stream().filter(p -> !aliasList.contains(p.getAlias())).collect(Collectors.toList());
            // 需要进行修改的参数数据
            List<DtoParamsConfig> updateParamsConfigList = paramsConfigs.stream().filter(p -> aliasList.contains(p.getAlias())).collect(Collectors.toList());
            // 不存在的参数进行新增操作
            for (DtoParamsConfig paramsConfig : othersParamsConfigList) {
                DtoParamsConfig dtoParamsConfig = new DtoParamsConfig();
                dtoParamsConfig.setParamsId(paramsConfig.getParamsId());
                dtoParamsConfig.setAlias(paramsConfig.getAlias());
                dtoParamsConfig.setDefaultControl(paramsConfig.getDefaultControl());
                dtoParamsConfig.setDimensionId(paramsConfig.getDimensionId());
                dtoParamsConfig.setDimension(paramsConfig.getDimension());
                dtoParamsConfig.setOrderNum(paramsConfig.getOrderNum());
                dtoParamsConfig.setType(paramsConfig.getType());
                dtoParamsConfig.setDefaultValue(paramsConfig.getDefaultValue());
                dtoParamsConfig.setDataSource(paramsConfig.getDataSource());
                dtoParamsConfig.setIsRequired(paramsConfig.getIsRequired());
                dtoParamsConfig.setMostSignificance(paramsConfig.getMostSignificance());
                dtoParamsConfig.setMostDecimal(paramsConfig.getMostDecimal());
                dtoParamsConfig.setAnalyzeItemId(paramsConfig.getAnalyzeItemId());
                dtoParamsConfig.setParentId(paramsConfig.getParentId());
                dtoParamsConfig.setIsShow(paramsConfig.getIsShow());
                dtoParamsConfig.setIsFormula(paramsConfig.getIsFormula());
                dtoParamsConfig.setFormulaId(paramsConfig.getFormulaId());
                dtoParamsConfig.setIsAllConfig(paramsConfig.getIsAllConfig());
                dtoParamsConfig.setObjId(testId);
                saveParamsConfigs.add(dtoParamsConfig);
            }
            // 已经存在的数据进行数据的修改
            for (DtoParamsConfig params : existParamsConfigList) {
                for (DtoParamsConfig paramsConfig : updateParamsConfigList) {
                    if (paramsConfig.getAlias().equals(params.getAlias())) {
                        params.setParamsId(paramsConfig.getParamsId());
                        params.setDefaultControl(paramsConfig.getDefaultControl());
                        params.setAlias(paramsConfig.getAlias());
                        params.setDimensionId(paramsConfig.getDimensionId());
                        params.setDimension(paramsConfig.getDimension());
                        params.setOrderNum(paramsConfig.getOrderNum());
                        params.setType(paramsConfig.getType());
                        params.setDefaultValue(paramsConfig.getDefaultValue());
                        params.setDataSource(paramsConfig.getDataSource());
                        params.setIsRequired(paramsConfig.getIsRequired());
                        params.setMostSignificance(paramsConfig.getMostSignificance());
                        params.setMostDecimal(paramsConfig.getMostDecimal());
                        params.setAnalyzeItemId(paramsConfig.getAnalyzeItemId());
                        params.setParentId(paramsConfig.getParentId());
                        params.setIsShow(paramsConfig.getIsShow());
                        params.setIsFormula(paramsConfig.getIsFormula());
                        params.setFormulaId(paramsConfig.getFormulaId());
                        params.setIsAllConfig(paramsConfig.getIsAllConfig());
                        params.setObjId(testId);
                        updateParamsConfigs.add(params);
                    }
                }
            }
        }
        if (saveParamsConfigs.size() > 0) {
            repository.save(saveParamsConfigs);
        }
        if (StringUtil.isNotEmpty(updateParamsConfigs)) {
            super.update(updateParamsConfigs);
        }
    }

    @Override
    @Transactional
    public List<DtoParamsConfig> copyParamsConfigList(String objectId, List<DtoParamsConfig> list) {
        //复制容器
        List<DtoParamsConfig> waitSaveParamsConfigList = new ArrayList<>();
        List<DtoParamsFormula> waitSaveParamsFormulaList = new ArrayList<>();
        List<DtoParamsTestFormula> waitSaveTestFormulaList = new ArrayList<>();
        List<DtoParamsPartFormula> waitSavePartFormulaList = new ArrayList<>();
        //相关数据
        List<String> ids = list.stream().map(DtoParamsConfig::getId).collect(Collectors.toList());
        List<DtoParamsConfig> analyzeItemParamsList = repository.findByParentIdIn(ids);
        List<String> paramsFormulaIds = list.stream().filter(d->StringUtil.isNotEmpty(d.getFormulaId())).map(DtoParamsConfig::getFormulaId).collect(Collectors.toList());
        List<DtoParamsFormula> allParamsFormulaList = paramsFormulaIds.isEmpty()?new ArrayList<>():paramsFormulaRepository.findAll(paramsFormulaIds);
        List<DtoParamsTestFormula> allParamsTestFormulaList = paramsFormulaIds.isEmpty()?new ArrayList<>():paramsTestFormulaRepository.findByObjIdIn(paramsFormulaIds);
        List<DtoParamsPartFormula> allParamsPartFormulaList = paramsFormulaIds.isEmpty()?new ArrayList<>():paramsPartFormulaRepository.findByFormulaIdIn(paramsFormulaIds);
        for (DtoParamsConfig paramsConfig:list ) {
            //参数主体复制
            DtoParamsConfig copyParamsConfig = new DtoParamsConfig();
            BeanUtils.copyProperties(paramsConfig,copyParamsConfig,"id");
            copyParamsConfig.setObjId(objectId);
            //分析项目参数 分析项目配置复制
            if(EnumLIM.EnumParamsType.分析项目参数.getValue().equals(paramsConfig.getParamsType())){
                List<DtoParamsConfig> paramsConfigAnalyzeItemList = analyzeItemParamsList.stream().filter(a->paramsConfig.getId().equals(a.getParentId())).collect(Collectors.toList());
                for (DtoParamsConfig analyzeItem:paramsConfigAnalyzeItemList) {
                    DtoParamsConfig analyzeItemTarget = new DtoParamsConfig();
                    BeanUtils.copyProperties(analyzeItem,analyzeItemTarget,"id");
                    analyzeItemTarget.setParentId(copyParamsConfig.getId());
                    waitSaveParamsConfigList.add(analyzeItemTarget);
                }
            }
            //公式复制
            if(StringUtil.isNotEmpty(paramsConfig.getFormulaId())){
                //参数公式复制
                DtoParamsFormula paramsFormula = allParamsFormulaList.stream().filter(f->paramsConfig.getFormulaId().equals(f.getId())).findFirst().orElse(null);
                if(paramsFormula!=null){
                    DtoParamsFormula copyParamsFormula = new DtoParamsFormula();
                    BeanUtils.copyProperties(paramsFormula,copyParamsFormula,"id");
                    copyParamsConfig.setFormulaId(copyParamsFormula.getId());
                    waitSaveParamsFormulaList.add(copyParamsFormula);
                    //参数公式参数复制
                    List<DtoParamsTestFormula> paramsTestFormulaList = allParamsTestFormulaList.stream().filter(t->paramsFormula.getId().equals(t.getObjId())).collect(Collectors.toList());
                    for (DtoParamsTestFormula paramsTestFormula:paramsTestFormulaList) {
                        DtoParamsTestFormula copyParamsTestFormula = new DtoParamsTestFormula();
                        BeanUtils.copyProperties(paramsTestFormula,copyParamsTestFormula,"id");
                        copyParamsTestFormula.setObjId(copyParamsFormula.getId());
                        waitSaveTestFormulaList.add(copyParamsTestFormula);
                    }
                }
                //部分修约公式
                List<DtoParamsPartFormula> ParamsPartFormulaList = allParamsPartFormulaList.stream().filter(f->paramsConfig.getFormulaId().equals(f.getId())).collect(Collectors.toList());
                for (DtoParamsPartFormula paramsPartFormula:ParamsPartFormulaList) {
                    DtoParamsPartFormula copyparamsPartFormula = new DtoParamsPartFormula();
                    BeanUtils.copyProperties(paramsPartFormula,copyparamsPartFormula,"id");
                    copyparamsPartFormula.setFormulaId(copyParamsConfig.getFormulaId());
                    waitSavePartFormulaList.add(copyparamsPartFormula);
                }
            }
            waitSaveParamsConfigList.add(copyParamsConfig);
        }
        paramsFormulaRepository.save(waitSaveParamsFormulaList);
        paramsTestFormulaRepository.save(waitSaveTestFormulaList);
        paramsPartFormulaRepository.save(waitSavePartFormulaList);
        return save(waitSaveParamsConfigList);
    }
}
