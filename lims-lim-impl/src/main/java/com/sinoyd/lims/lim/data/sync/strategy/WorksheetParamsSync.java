package com.sinoyd.lims.lim.data.sync.strategy;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.strategy.base.AbsDataSync;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsConfigRepository;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 原始记录单参数
 *配置同步
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/5/25
 */
@Component
@DependsOn({"springContextAware"})
@Order(23)
@Slf4j
public class WorksheetParamsSync extends AbsDataSync<DtoParamsConfig> {

    @Autowired
    private ParamsConfigService service;

    @Autowired
    private ParamsConfigRepository repository;

    /**
     * 比较数据
     *
     * @param recordIds 原始记录单配置Id
     * @return 比较结果
     */
    @Override
    public List<DtoDataCompareResult<DtoParamsConfig>> compareData(List<String> recordIds) {
        List<DtoParamsConfig> projectData = service.findAll();
        List<DtoParamsConfig> standardData = queryStandardData();
        if (StringUtil.isNotEmpty(recordIds)&&StringUtil.isNotEmpty(standardData)){
           standardData = standardData.stream().filter(p->recordIds.contains(p.getObjId())).collect(Collectors.toList());
        }
        return compareData(standardData,projectData);
    }

    /**
     * 同步数据
     *
     * @param recordId 原始记录单配置Id
     * @param webSocketServer websockets服务端
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(List<String> recordId, WebSocketServer webSocketServer) {
        List<DtoDataCompareResult<DtoParamsConfig>> compareResult = compareData(recordId);
        Optional<DtoDataCompareResult<DtoParamsConfig>> resultOptional = compareResult.parallelStream().filter(p -> COMPARE_CATEGORY[0].equals(p.getCategory())
                && p.getCount() > 0).findFirst();
        resultOptional.ifPresent(r -> {
            //已同步记录数
            int i = 0;
            DtoParamsConfig errorDto = null;
            try {
                for (DtoParamsConfig dtoParamsConfig : r.getAddDataList()) {
                    errorDto = dtoParamsConfig;
                    if (repository.findOne(dtoParamsConfig.getId()) != null) {
                        service.update(dtoParamsConfig);
                    } else {
                        service.save(dtoParamsConfig);
                    }
                    webSocketServer.sendMessage(String.format("同步" + getItemName() + "(%d/%d)", ++i, r.getCount()));
                }
            } catch (Exception e) {
                if (errorDto != null) {
                    webSocketServer.sendMessage("同步" + getItemName() + "发生错误, ID = " + errorDto.getId() + ", 错误信息: "
                            + e.getMessage());
                }
                log.error(e.getMessage(), e);
                throw new BaseException("同步" + getItemName() + "发生错误");
            }
        });
    }

    /**
     * 是否必须同步
     *
     * @return 返回结果
     */
    @Override
    public boolean mustSync() {
        return true;
    }

    /**
     * 获取显示名称
     *
     * @return 返回结果
     */
    @Override
    public String getItemName() {
        return EnumLIM.EnumDataSyncType.原始记录单参数配置.name();
    }

    /**
     * 获取排序值
     *
     * @return 返回结果
     */
    @Override
    public Integer getOrderNum() {
        return EnumLIM.EnumDataSyncType.原始记录单参数配置.getValue();
    }

    /**
     * 获取请求路径
     *
     * @return 返回结果
     */
    @Override
    public String getStandardDataQueryUrl() {
        return "/api/sinoyd-lims/lim/paramsConfig";
    }

    /**
     * 获取同步类型
     *
     * @return 返回结果
     */
    @Override
    public Integer getSyncDataType() {
        return EnumLIM.EnumDataSyncType.原始记录单参数配置.getValue();
    }
}
