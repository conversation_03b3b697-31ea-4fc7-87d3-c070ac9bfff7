package com.sinoyd.lims.lim.controller;

import com.sinoyd.base.dto.lims.DtoConsumable;
import com.sinoyd.base.dto.lims.DtoConsumableOfMixed;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.lims.DtoInstrument;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.lims.*;
import com.sinoyd.lims.lim.service.*;
import com.sinoyd.lims.lim.service.transform.ExpImpPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.lims.lim.dto.customer.DtoDataSyncParams;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据导入
 *
 * <AUTHOR>
 * @version V1.0.0 2022/3/23
 * @since V100R001
 */
@RestController
@RequestMapping("/api/lim/import")
public class ImportController extends ExceptionHandlerController<ImportInstrumentService> {

    @Autowired
    private ImportConsumableService importConsumableService;

    @Autowired
    private ImportPersonService importPersonService;

    @Autowired
    private ImportTestService importTestService;

    @Autowired
    private ImportConsumableOfMixedService ofMixedService;

    @Autowired
    private ImportInsCheckRecordService importInsCheckRecordService;

    @Autowired
    private ImportEnterpriseService importEnterpriseService;

    @Autowired
    private ImportEvaluationCriteriaService importEvaluationCriteriaService;

    @Autowired
    private ImportTestFormulaService importTestFormulaService;

    @Autowired
    private ImportTestTransformerService importTestTransformerService;

    @Autowired
    private ImportTestFormulaUpdateService importTestFormulaUpdateService;

    @Autowired
    private ImportRecordConfig2TestService importRecordConfig2TestService;

    @Autowired
    private ImportFixedAssetsService importFixedAssetsService;

    @Autowired
    private ImportParamsConfigService importParamsConfigService;

    @Autowired
    private ExpImpFixedPropertyService expImpFixedPropertyService;

    @Autowired
    private ExpImpEnterpriseService expImpEnterpriseService;

    @Autowired
    private ExpImpStandardService expImpStandardService;

    @Autowired
    private ExpImpConsumableService expImpConsumableService;

    @Autowired
    private ExpImpInstrumentCheckRecordService expImpInstrumentCheckRecordService;

    @Autowired
    private ExpImpTestService expImpTestService;

    @Autowired
    private ExpImpPersonService expImpPersonService;

    @Autowired
    private ExpImpPersonAbilityService expImpPersonAbilityService;

    @Autowired
    private ExpImpEvaluationCriteriaService expImpEvaluationCriteriaService;

    @Autowired
    private ImportQualityControlLimitService importQualityControlLimitService;

    @Autowired
    private ExpImpInstrumentService expImpInstrumentService;

    @Autowired
    private ImportPersonCertService importPersonCertService;

    @Autowired
    private ImportStandardMethodService importStandardMethodService;

    /**
     * 仪器导入
     *
     * @param file 导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/importInstrument")
    public RestResponse<List<DtoInstrument>> importInstrument(MultipartFile file, Boolean isImportInsType, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, isImportInsType);
        RestResponse<List<DtoInstrument>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 消耗品导入
     *
     * @param file                   文件流
     * @param isImportConsumableType 是否导入消耗品类型
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importConsumable")
    public RestResponse<List<DtoConsumable>> importConsumable(MultipartFile file, Boolean isImportConsumableType, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, isImportConsumableType);
        RestResponse<List<DtoConsumable>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importConsumableService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 人员导入
     *
     * @param file              文件流
     * @param isImportPost      是否导入职务
     * @param isImportTitle     是否导入职称
     * @param isImportEducation 是否导入学历
     * @param isCreateAccount   是否创建账号
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importPerson")
    public RestResponse<List<DtoPerson>> importPerson(MultipartFile file, Boolean isImportPost, Boolean isImportTitle, Boolean isImportEducation, Boolean isCreateAccount, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, isImportPost);
        objectMap.put(1, isImportTitle);
        objectMap.put(2, isImportEducation);
        objectMap.put(3, isCreateAccount);
        RestResponse<List<DtoPerson>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importPersonService.importExcel(file, objectMap, response));
        return restResponse;
    }


    /**
     * 测试项目导入
     *
     * @param file               文件流
     * @param isImportAnaItem    是否导入分析项目
     * @param isImportAnaMethod  是否导入分析方法
     * @param isImportDimension  是否导入量纲
     * @param isImportFormula    是否导入计算公式
     * @param isImportTestPerson 是否导入测试人员
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importTest")
    public RestResponse<List<DtoTest>> importTest(MultipartFile file, Boolean isImportAnaItem, Boolean isImportAnaMethod, Boolean isImportDimension, Boolean isImportFormula, Boolean isImportTestPerson, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, isImportAnaItem);
        objectMap.put(1, isImportAnaMethod);
        objectMap.put(2, isImportDimension);
        objectMap.put(3, isImportFormula);
        objectMap.put(4, isImportTestPerson);
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importTestService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 标准样品混标导入
     *
     * @param file         文件流
     * @param consumableId 选择的标准样品的id
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importConsumableOfMixed")
    public RestResponse<List<DtoConsumableOfMixed>> importConsumableOfMixed(MultipartFile file, String consumableId, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, consumableId);
        RestResponse<List<DtoConsumableOfMixed>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(ofMixedService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 仪器检定校准导入
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importCheckRecord")
    public RestResponse<List<DtoInstrumentCheckRecord>> importCheckRecord(MultipartFile file, String instrumentId, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, instrumentId);
        RestResponse<List<DtoInstrumentCheckRecord>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importInsCheckRecordService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 企业导入
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importEnterprise")
    public RestResponse<List<DtoEnterprise>> importEnterprise(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoEnterprise>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importEnterpriseService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 评价标准导入
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importEvaluationCriteria")
    public RestResponse<List<DtoEvaluationCriteria>> importEvaluationCriteria(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoEvaluationCriteria>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importEvaluationCriteriaService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 测试项目导入（新增）
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importTestTransformer")
    public RestResponse<Void> importTestTransformer(MultipartFile file, HttpServletResponse response) throws Exception {
        RestResponse<Void> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        importTestTransformerService.importExcel(file, response);
        return restResponse;
    }

    /**
     * 测试项目数据迁移
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importTestFormulaAdd")
    public RestResponse<Void> importTestFormulaAdd(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<Void> restResponse = new RestResponse<>();
        importTestFormulaService.importExcel(file, objectMap, response);
        return restResponse;
    }

    /**
     * 测试项目导入（修改）
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importTestFormulaUpdate")
    public RestResponse<Void> importTestFormulaUpdate(MultipartFile file, HttpServletResponse response) throws Exception {
        RestResponse<Void> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        importTestFormulaUpdateService.importExcel(file, response);
        return restResponse;
    }

    /**
     * 原始记录单与测试项目关系导入
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importRecordConfig2Test")
    public RestResponse<Void> importRecordConfig2Test(MultipartFile file, HttpServletResponse response) throws Exception {
        RestResponse<Void> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        importRecordConfig2TestService.importExcel(file, response);
        return restResponse;
    }

    /**
     * 固定资产导入
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importFixedAssets")
    public RestResponse<List<DtoFixedProperty>> importFixedAssets(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoFixedProperty>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importFixedAssetsService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 原始记录单数据参数导入
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importDataParamsConfig")
    public RestResponse<Void> importDataParamsConfig(MultipartFile file, String recordConfigId, HttpServletResponse response) throws Exception {
        RestResponse<Void> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("recordConfigId", recordConfigId);
        importParamsConfigService.importExcel(file, objectMap, response);
        return restResponse;
    }


    /**
     * 固定资产导入(修改)
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/fixedProperty")
    public RestResponse<List<DtoFixedProperty>> importFixedProperty(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoFixedProperty>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpFixedPropertyService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 企业导入（修改）
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/enterprise")
    public RestResponse<List<DtoEnterprise>> updateEnterprise(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoEnterprise>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpEnterpriseService.importExcel(file, objectMap, response));
        return restResponse;
    }


    /**
     * 标准物质导入（修改）
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/standard")
    public RestResponse<List<DtoConsumable>> importStandard(MultipartFile file, Boolean isImportConsumableType, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, isImportConsumableType);
        RestResponse<List<DtoConsumable>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpStandardService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 消耗品导入（修改）
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/consumable")
    public RestResponse<List<DtoConsumable>> importUpdateConsumable(MultipartFile file, Boolean isImportConsumableType, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, isImportConsumableType);
        RestResponse<List<DtoConsumable>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpConsumableService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 仪器检定浇导入（修改）
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/instrumentCheckRecord")
    public RestResponse<List<DtoInstrumentCheckRecord>> importInstrumentCheckRecord(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoInstrumentCheckRecord>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpInstrumentCheckRecordService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 测试项目导入（修改）
     *
     * @param file               文件流
     * @param isImportAnaItem    是否导入分析项目
     * @param isImportAnaMethod  是否导入分析方法
     * @param isImportDimension  是否导入量纲
     * @param isImportFormula    是否导入计算公式
     * @param isImportTestPerson 是否导入测试人员
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/test")
    public RestResponse<List<DtoTest>> importUpdateTest(MultipartFile file, Boolean isImportAnaItem, Boolean isImportAnaMethod, Boolean isImportDimension, Boolean isImportFormula, Boolean isImportTestPerson, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, isImportAnaItem);
        objectMap.put(1, isImportAnaMethod);
        objectMap.put(2, isImportDimension);
        objectMap.put(3, isImportFormula);
        objectMap.put(4, isImportTestPerson);
        RestResponse<List<DtoTest>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpTestService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 人员导入(修改)
     *
     * @param file              文件流
     * @param isImportPost      是否导入职务
     * @param isImportTitle     是否导入职称
     * @param isImportEducation 是否导入学历
     * @param isCreateAccount   是否创建账号
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/person")
    public RestResponse<List<DtoPerson>> importUpdatePerson(MultipartFile file, Boolean isImportPost, Boolean isImportTitle, Boolean isImportEducation, Boolean isCreateAccount, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, isImportPost);
        objectMap.put(1, isImportTitle);
        objectMap.put(2, isImportEducation);
        objectMap.put(3, isCreateAccount);
        RestResponse<List<DtoPerson>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpPersonService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 人员检测能力导入（修改）
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/personAbility")
    public RestResponse<List<DtoPersonAbility>> importPersonAbility(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoPersonAbility>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpPersonAbilityService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 评价标准导入（修改）
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/evaluationCriteria")
    public RestResponse<List<DtoEvaluationCriteria>> importUpdateEvaluationCriteria(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoEvaluationCriteria>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpEvaluationCriteriaService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 测试项目迁移导入（查询基础数据）
     *
     * @param file 文件
     * @return 返回待绑定基础数据
     */
    @PostMapping("/baseData")
    public RestResponse<DtoDataSyncParams> getBaseData(MultipartFile file, HttpServletResponse response) {
        RestResponse<DtoDataSyncParams> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importTestTransformerService.getBaseData(file, response));
        return restResponse;
    }

    /**
     * 测试项目迁移导入（导入前检查)
     *
     * @param dataSyncBindCheck 检查绑定实体
     * @return
     */
    @PostMapping("/checkData")
    public RestResponse<DtoDataSyncParams> checkData(@RequestBody DtoDataSyncParams dataSyncBindCheck, HttpServletResponse response) {
        RestResponse<DtoDataSyncParams> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importTestTransformerService.getCheckData(dataSyncBindCheck, response));
        return restResponse;
    }

    /**
     * 获取导入数据表名
     *
     * @return 响应结果
     */
    @GetMapping("/dataTable")
    public RestResponse<List<Map<String, String>>> getDataTable() {
        RestResponse<List<Map<String, String>>> restResp = new RestResponse<>();
        restResp.setData(importTestTransformerService.getDataTable());
        return restResp;
    }

    /**
     * 测试项目迁移导入（数据确认导入)
     *
     * @param dtoDataSyncParams 数据同步参数实体
     * @return 响应结果
     */
    @PostMapping("/importData")
    public RestResponse<String> importData(@RequestBody DtoDataSyncParams dtoDataSyncParams) {
        RestResponse<String> restResp = new RestResponse<>();
        importTestTransformerService.importData(dtoDataSyncParams);
        return restResp;
    }

    /**
     * 质控限值导入
     *
     * @param file 文件流
     * @return RestResponse<T>
     * @throws Exception 错误信息
     */
    @PostMapping("/importQcLimit")
    public RestResponse<List<DtoQualityControlLimit>> importQcLimit(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoQualityControlLimit>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importQualityControlLimitService.importExcel(file, objectMap, response));
        return restResponse;
    }


    /**
     * 仪器更新导入
     *
     * @param file 导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/instrument")
    public RestResponse<List<DtoInstrument>> importUpdateInstrument(MultipartFile file, Boolean isImportInsType, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        objectMap.put(0, isImportInsType);
        RestResponse<List<DtoInstrument>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(expImpInstrumentService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 仪器更新导入
     *
     * @param file 导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/personCert")
    public RestResponse<List<DtoPersonCert>> importPersonCert(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoPersonCert>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importPersonCertService.importExcel(file, objectMap, response));
        return restResponse;
    }

    /**
     * 仪器更新导入
     *
     * @param file 导入的excel文件
     * @return RestResponse<T>
     */
    @PostMapping("/standardMethod")
    public RestResponse<List<DtoStandardMethod>> importStandardMethod(MultipartFile file, HttpServletResponse response) throws Exception {
        Map<Integer, Object> objectMap = new HashMap<>();
        RestResponse<List<DtoStandardMethod>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(importStandardMethodService.importExcel(file, objectMap, response));
        return restResponse;
    }
}
