package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.repository.rcc.DimensionRepository;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataProjectVO;
import com.sinoyd.lims.foreign.DataQualityControlVO;
import com.sinoyd.lims.foreign.DataReportFolderSortInfoVO;
import com.sinoyd.lims.foreign.service.DataQualityControlService;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 质控信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataQualityControlServiceImpl extends AbsQueryDataServiceImpl<DataQualityControlVO> implements DataQualityControlService {

    private DimensionRepository dimensionRepository;

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select id,associateSampleId,qcGrade,qcType,qcValue, ");
        sql.append("qcVolume,qaId,qcTime,qcTestValue,realSampleTestValue, ");
        sql.append("qcCode,qcOriginValue,qcValidDate,qcStandardDate, ");
        sql.append("qcConcentration,orgId,domainId,qcVolumeDimensionId,qcValueDimensionId, ");
        sql.append("qcTestValueDimensionId,realSampleTestValueDimensionId,qcConcentrationDimensionId ");
        sql.append(" from TB_PRO_QualityControl ");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND modifyDate > :syncTime ", "syncTime");
        super.appendCondition(sql, map, "AND associateSampleId in (:associateSampleIds) ", "associateSampleIds");
        super.appendCondition(sql, map, "AND id in (:qcIds) ", "qcIds");


    }

    @Override
    protected RowMapper<DataQualityControlVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataQualityControlVO qualityControlVO = new DataQualityControlVO();
            qualityControlVO.setId(UUIDHelper.NewID());
            qualityControlVO.setQcId(rs.getString("id"));
            qualityControlVO.setAssociateSampleId(rs.getString("associateSampleId"));
            qualityControlVO.setQcGrade(rs.getInt("qcGrade"));
            qualityControlVO.setQcType(rs.getInt("qcType"));
            qualityControlVO.setQcValue(rs.getString("qcValue"));
            qualityControlVO.setQcVolume(rs.getString("qcVolume"));
            qualityControlVO.setQaId(rs.getString("qaId"));
            qualityControlVO.setQcTime(rs.getDate("qcTime"));
            qualityControlVO.setQcTestValue(rs.getString("qcTestValue"));
            qualityControlVO.setRealSampleTestValue(rs.getString("realSampleTestValue"));
            qualityControlVO.setQcCode(rs.getString("qcCode"));
            qualityControlVO.setQcOriginValue(rs.getString("qcOriginValue"));
            qualityControlVO.setQcValidDate(rs.getDate("qcValidDate"));
            qualityControlVO.setQcStandardDate(rs.getDate("qcStandardDate"));
            qualityControlVO.setQcConcentration(rs.getString("qcConcentration"));
            qualityControlVO.setQcVolumeDimension(rs.getString("qcVolumeDimensionId"));
            qualityControlVO.setQcValueDimension(rs.getString("qcValueDimensionId"));
            qualityControlVO.setQcTestValueDimension(rs.getString("qcTestValueDimensionId"));
            qualityControlVO.setRealSampleTestValueDimension(rs.getString("realSampleTestValueDimensionId"));
            qualityControlVO.setQcConcentrationDimension(rs.getString("qcConcentrationDimensionId"));
            qualityControlVO.setOrgId(rs.getString("orgId"));
            qualityControlVO.setDomainId(rs.getString("domainId"));
            qualityControlVO.setSyncTime(new Date());
            return qualityControlVO;
        };
    }

    @Override
    protected void individuationData(Map<String, Object> map, List<DataQualityControlVO> qualityControlVOList) {
        List<String> dimensionIds = qualityControlVOList.stream().flatMap(p -> Stream.of(p.getQcVolumeDimension(),
                p.getQcValueDimension(), p.getQcTestValueDimension(),
                p.getRealSampleTestValueDimension(), p.getQcConcentrationDimension()))
                .filter(vo -> !UUIDHelper.GUID_EMPTY.equals(vo) && !vo.isEmpty()).distinct().collect(Collectors.toList());
        List<DtoDimension> dtoDimensionList = StringUtil.isNotEmpty(dimensionIds) ?
                dimensionRepository.findAllDeleted(dimensionIds) : new ArrayList<>();
        Map<String, String> dimensionMap = dtoDimensionList.stream().collect(Collectors.toMap(DtoDimension::getId, DtoDimension::getDimensionName));
        for (DataQualityControlVO qualityControlVO : qualityControlVOList) {
            qualityControlVO.setQcVolumeDimension(dimensionMap.getOrDefault(qualityControlVO.getQcVolumeDimension(), ""));
            qualityControlVO.setQcTestValueDimension(dimensionMap.getOrDefault(qualityControlVO.getQcTestValueDimension(), ""));
            qualityControlVO.setQcValueDimension(dimensionMap.getOrDefault(qualityControlVO.getQcValueDimension(), ""));
            qualityControlVO.setRealSampleTestValueDimension(dimensionMap.getOrDefault(qualityControlVO.getRealSampleTestValueDimension(), ""));
            qualityControlVO.setQcConcentrationDimension(dimensionMap.getOrDefault(qualityControlVO.getQcConcentrationDimension(), ""));
        }
    }

    @Autowired
    public void setDimensionRepository(DimensionRepository dimensionRepository) {
        this.dimensionRepository = dimensionRepository;
    }
}
