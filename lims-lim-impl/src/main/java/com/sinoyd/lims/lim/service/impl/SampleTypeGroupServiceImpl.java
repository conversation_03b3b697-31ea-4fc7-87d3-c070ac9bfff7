package com.sinoyd.lims.lim.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.sinoyd.base.dto.rcc.DtoIndustryType;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.customer.DtoSampleTypeDefaultGroup;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.IndustryTypeService;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.criteria.SampleTypeGroupCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup2Test;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroup2TestRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroupRepository;
import com.sinoyd.lims.lim.service.SampleTypeGroup2TestService;
import com.sinoyd.lims.lim.service.SampleTypeGroupService;
import com.sinoyd.lims.lim.service.TestService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 样品分组接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
public class SampleTypeGroupServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleTypeGroup, String, SampleTypeGroupRepository> implements SampleTypeGroupService {

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private IndustryTypeService industryTypeService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypesService;

    @Autowired
    @Lazy
    private SampleTypeGroup2TestService sampleTypeGroup2TestService;

    @Autowired
    private SampleTypeGroup2TestRepository sampleTypeGroup2TestRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    /**
     * 样品分组/分组规则新增
     */
    @Override
    @Transactional
    public DtoSampleTypeGroup save(DtoSampleTypeGroup sampleTypeGroup) {

        validate(sampleTypeGroup);
        if (StringUtil.isNull(sampleTypeGroup.getOrderNum())) {
            //排序值默认0
            sampleTypeGroup.setOrderNum(0);
        }


        return super.save(sampleTypeGroup);
    }

    /**
     * 样品分组分页列表
     */
    @Override
    public void findByPage(PageBean<DtoSampleTypeGroup> pb, BaseCriteria baseCriteria) {
        pb.setEntityName("DtoSampleTypeGroup s");
        pb.setSelect("select s");
        super.findByPage(pb, baseCriteria);
        List<DtoSampleTypeGroup> dataList = pb.getData();
        dataList.forEach(p -> p.setDefaultLabelGroup(false));
        //根据检测类型小类id，获取小类上配置的默认标签分组
        String sonSampleTypeId = ((SampleTypeGroupCriteria) baseCriteria).getSonSampleTypeId();
        if (StringUtil.isNotEmpty(sonSampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(sonSampleTypeId)) {
            DtoSampleType type = sampleTypesService.findOne(sonSampleTypeId);
            if (StringUtil.isNotNull(type)) {
                String defaultLabelGroupId = type.getDefaultLabelGroupId();
                if (StringUtil.isNotEmpty(defaultLabelGroupId) && !UUIDHelper.GUID_EMPTY.equals(defaultLabelGroupId)) {
                    for (DtoSampleTypeGroup group : dataList) {
                        if (defaultLabelGroupId.equals(group.getId())) {
                            group.setDefaultLabelGroup(true);
                        }
                    }
                    pb.setData(dataList);
                }
            }
        }
    }

    /**
     * 样品分组/分组规则更新
     */
    @Transactional
    @Override
    public DtoSampleTypeGroup update(DtoSampleTypeGroup sampleTypeGroup) {
        validate(sampleTypeGroup);
        if (StringUtil.isNull(sampleTypeGroup.getOrderNum())) {
            //排序值默认0
            sampleTypeGroup.setOrderNum(0);
        }
        return super.save(sampleTypeGroup);
    }

    /**
     * 通用校验
     *
     * @param sampleTypeGroup 分组
     */
    private void validate(DtoSampleTypeGroup sampleTypeGroup) {
        if (StringUtil.isEmpty(sampleTypeGroup.getGroupName())) {
            throw new BaseException("名称不能为空！");
        }

        if (repository.getCount(sampleTypeGroup.getId(), sampleTypeGroup.getGroupName(), sampleTypeGroup.getGroupType(), sampleTypeGroup.getSampleTypeId(), sampleTypeGroup.getParentId()) > 0) {
            if (sampleTypeGroup.getGroupType().equals(EnumLIM.EnumGroupType.分组规则.getValue())) {
                throw new BaseException("已存在相同名称的分组规则！");
            } else {
                throw new BaseException("已存在相同名称的分组！");
            }
        }
        if (StringUtil.isNotEmpty(sampleTypeGroup.getSampleTypeId())) {
            DtoSampleType type = sampleTypesService.findOne(sampleTypeGroup.getSampleTypeId());
            if (Boolean.TRUE.equals(type.getIsOpenGroupTag()) && StringUtil.isEmpty(sampleTypeGroup.getSampleCodeTag())) {
                throw new BaseException("对应检测类型启用样品编号标识后,分组的样品编号标识必填!");
            }
        }
        if (sampleTypeGroup.getGroupType().equals(EnumLIM.EnumGroupType.分组.getValue()) && StringUtil.isNotEmpty(sampleTypeGroup.getSampleCodeTag())) {
            List<DtoSampleTypeGroup> sampleTypeGroupList = repository.getSampleCodeTagList(sampleTypeGroup.getId(), sampleTypeGroup.getSampleCodeTag(), EnumLIM.EnumGroupType.分组.getValue(), sampleTypeGroup.getSampleTypeId(), sampleTypeGroup.getParentId());
            if (StringUtil.isNotEmpty(sampleTypeGroupList) && sampleTypeGroupList.stream().anyMatch(p-> p.getSampleCodeTag().equals(sampleTypeGroup.getSampleCodeTag()))) {
                // 因为数据库查询不区分大小写，所以传输号标识与已存在的标识对比，大小写不敏感判断
                throw new BaseException("已存在相同样品编号标识的分组！");
            }
        }
    }

    /**
     * 分组规则树
     */
    @Override
    public List<TreeNode> getNode() {

        List<TreeNode> topList = new ArrayList<>();
        //行业类型
        List<DtoIndustryType> industryTypes = industryTypeService.findAll();
        industryTypes.sort(Comparator.comparing(DtoIndustryType::getOrderNum).reversed());
        //样品大类
        List<DtoSampleType> sampleTypeLists = sampleTypesService.findAll();
        List<DtoSampleType> sampleTypes = sampleTypeLists.stream().filter(p -> p.getCategory().equals(EnumBase.EnumSampleTypeCategory.检测类型大类.getValue())).sorted(Comparator.comparing(DtoSampleType::getOrderNum).reversed()).collect(Collectors.toList());
        List<DtoSampleTypeGroup> groupList = repository.getListByGroupType(EnumLIM.EnumGroupType.分组规则.getValue()).stream().sorted(Comparator.comparing(DtoSampleTypeGroup::getOrderNum).reversed()).collect(Collectors.toList());
        //遍历行业类型
        for (DtoIndustryType i : industryTypes) {
            TreeNode tNode = new TreeNode();
            tNode.setId(i.getId());

            //遍历行业类型相关检测类型大类
            List<DtoSampleType> sampleTypesByIndustryType = sampleTypes.stream().filter(p -> p.getIndustryTypeId().equals(i.getId())).sorted(Comparator.comparing(DtoSampleType::getOrderNum).reversed()).collect(Collectors.toList());
            if (sampleTypesByIndustryType.size() > 0) {
                if (StringUtil.isNull(tNode.getChildren())) {
                    tNode.setChildren(new ArrayList<>());
                }
                tNode.setIsLeaf(false);
            } else {
                tNode.setIsLeaf(true);
            }

            tNode.setLabel(i.getIndustryName());
            tNode.setOrderNum(i.getOrderNum());
            tNode.setParentId(UUIDHelper.GUID_EMPTY);//默认为空id

            if (sampleTypesByIndustryType.size() > 0) {
                for (DtoSampleType s : sampleTypesByIndustryType) {
                    TreeNode sNode = new TreeNode();
                    sNode.setId(s.getId());

                    //遍历检测类型大类相关样品分组规则
                    List<DtoSampleTypeGroup> groupListBySampleType = groupList.stream().filter(p -> p.getSampleTypeId().equals(s.getId())).sorted(Comparator.comparing(DtoSampleTypeGroup::getOrderNum).reversed()).collect(Collectors.toList());
                    if (groupListBySampleType.size() > 0) {
                        if (StringUtil.isNull(sNode.getChildren())) {
                            sNode.setChildren(new ArrayList<>());
                        }
                        sNode.setIsLeaf(false);
                    } else {
                        sNode.setIsLeaf(true);
                    }

                    sNode.setLabel(s.getTypeName());
                    sNode.setOrderNum(s.getOrderNum());
                    sNode.setParentId(s.getIndustryTypeId());


                    if (groupListBySampleType.size() > 0) {
                        for (DtoSampleTypeGroup g : groupListBySampleType) {
                            TreeNode gNode = new TreeNode();
                            gNode.setId(g.getId());
                            gNode.setIsLeaf(true);
                            gNode.setLabel(g.getGroupName());
                            gNode.setOrderNum(g.getOrderNum());
                            gNode.setParentId(g.getSampleTypeId());
//                            topList.add(gNode);

                            sNode.getChildren().add(gNode);
                        }
                    }
//                    topList.add(sNode);

                    tNode.getChildren().add(sNode);
                }
            }
            topList.add(tNode);
        }

        List<TreeNode> topTreeList = topList.stream().collect(Collectors.toList());
        return topTreeList;
    }

    /**
     * 分组规则下获取分组列表
     */
    @Override
    public List<DtoSampleTypeGroup> getSampleTypeGroupList(String parentId) {

        return repository.getListByParentId(parentId);

    }

    /**
     * 分组规则下获取测试项目
     */
    @Override
    public List<DtoTest> getTestList(String sampleTypeId, String sampleTypeGroupId, Integer isGroup, String analyzeItem,
                                     String analyzeMethodStd, Integer cert) {
        StringBuilder sb = new StringBuilder();
        sb.append("select new com.sinoyd.lims.lim.dto.lims.DtoTest(a.id,a.analyzeMethodId,a.redAnalyzeMethodName,a.redAnalyzeItemName," +
                "a.redCountryStandard,a.analyzeItemId,a.cert,a.sampleTypeId) " +
                "from DtoTest a where a.sampleTypeId = :sampleTypeId and a.isDeleted = 0 and a.isCompleteField = false ");
        Map<String, Object> value = new HashMap<>();
        if (StringUtil.isNotEmpty(analyzeItem)) {
            sb.append(" and a.redAnalyzeItemName like :analyzeItem ");
            value.put("analyzeItem", "%" + analyzeItem + "%");
        }
        if (StringUtil.isNotEmpty(analyzeMethodStd)) {
            sb.append(" and (a.redAnalyzeMethodName like :analyzeMethodStd or a.redCountryStandard like :analyzeMethodStd)");
            value.put("analyzeMethodStd", "%" + analyzeMethodStd + "%");
        }
        if (StringUtil.isNotNull(cert) && cert != -1) {
            sb.append(" and a.cert = :cert");
            value.put("cert", cert);
        }
        sb.append(" order by a.redAnalyzeMethodName, a.redAnalyzeItemName ");
        value.put("sampleTypeId", sampleTypeId);

        List<DtoTest> testList = comRepository.find(sb.toString(), value);

        //获取分组规则相关分组列表
        List<DtoSampleTypeGroup> groupList = repository.getListByParentId(sampleTypeGroupId);
        //要过滤掉的测试项目id列表
        List<String> rmvTstIdList = new ArrayList<>();
        if (groupList.size() > 0) {
            List<String> groupIds = groupList.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList());
            List<DtoSampleTypeGroup2Test> sam2TestList = sampleTypeGroup2TestService.findBySampleTypeGroupIds(groupIds);
            if (sam2TestList.size() > 0) {
                for (DtoTest test : testList) {
                    boolean flag = true;
                    List<DtoSampleTypeGroup2Test> list = sam2TestList.stream().filter(p -> test.getId().equals(p.getTestId())).collect(Collectors.toList());
                    if (StringUtil.isNotNull(isGroup) && !isGroup.equals(-1)) {
                        //按是否分组条件进行过滤 1：是  2：否  -1：所有 (为空默认所有)
                        if ((isGroup.equals(1) && StringUtil.isEmpty(list)) || (isGroup.equals(2) && StringUtil.isNotEmpty(list))) {
                            //按分组查询条件过滤掉对应测试项目
                            rmvTstIdList.add(test.getId());
                            flag = false;
                        }
                    }
                    if (flag && list.size() > 0) {
                        String groupId = list.get(0).getSampleTypeGroupId();
                        DtoSampleTypeGroup group = groupList.stream().filter(p -> groupId.equals(p.getId())).collect(Collectors.toList()).get(0);
                        test.setSampleTypeGroup(group);
                    }
                }
            }
        }
        //移除不需要的测试项目
        if (StringUtil.isNotEmpty(rmvTstIdList)) {
            testList = testList.stream().filter(p -> !rmvTstIdList.contains(p.getId())).collect(Collectors.toList());
        }
        return testList;
    }

    @Override
    public List<DtoTest> findTestByGroupId(String sampleTypeId, String sampleTypeGroupId, String anaNameMtdStd, Integer cert) {
        //获取分组规则相关分组列表
        List<DtoSampleTypeGroup2Test> groupList = sampleTypeGroup2TestService.findBySampleTypeGroupId(sampleTypeGroupId);
        List<String> testIds = groupList.stream().map(DtoSampleTypeGroup2Test::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = new ArrayList<>();
        if (StringUtil.isNotEmpty(testIds)) {
            testList = testService.findAll(testIds);
        }
        testList = testList.stream().filter(p -> sampleTypeId.equals(p.getSampleTypeId())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(anaNameMtdStd)) {
            //按分析项目名称，分析方法名称，标准编号关键字过滤
            testList = testList.stream().filter(p -> p.getRedAnalyzeItemName().contains(anaNameMtdStd)
                    || p.getRedAnalyzeMethodName().contains(anaNameMtdStd) || p.getRedCountryStandard().contains(anaNameMtdStd)).collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(cert) && cert != -1) {
            testList = testList.stream().filter(t -> cert.equals(t.getCert())).collect(Collectors.toList());
        }
        DtoSampleTypeGroup group = repository.findOne(sampleTypeGroupId);
        for (DtoTest test : testList) {
            test.setSampleTypeGroup(group);
        }
        return testList;
    }

    /**
     * 设置分组
     */
    @Transactional
    @Override
    public void setSampleTypeGroup(String sampleTypeGroupId, Collection<String> testIds) {

        DtoSampleTypeGroup sampleTypeGroup = repository.getOne(sampleTypeGroupId);
        cancelSampleTypeGroup(sampleTypeGroup.getParentId(), testIds);

        List<DtoSampleTypeGroup2Test> newS2Ts = new ArrayList<>();
        for (String id : testIds) {
            DtoSampleTypeGroup2Test st = new DtoSampleTypeGroup2Test();
            st.setSampleTypeGroupId(sampleTypeGroupId);
            st.setTestId(id);
            newS2Ts.add(st);
        }
        sampleTypeGroup2TestService.save(newS2Ts);
    }

    /**
     * 取消分组
     */
    @Transactional
    @Override
    public void cancelSampleTypeGroup(String sampleTypeGroupId, Collection<String> testIds) {
        List<String> groupIds = repository.getListByParentId(sampleTypeGroupId).stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList());

        List<DtoSampleTypeGroup2Test> oldSam2TestList = sampleTypeGroup2TestRepository.findBySampleTypeGroupIdAndTestIds(groupIds, testIds);

        if (oldSam2TestList.size() > 0) {
            for (DtoSampleTypeGroup2Test obj : oldSam2TestList) {
                sampleTypeGroup2TestService.delete(obj);
            }
        }
    }

    /**
     * 删除分组规则/分组及相关信息
     */
    @Transactional
    @Override
    public void deleteById(String id) {
        // 根据父节点获取子分组
        List<DtoSampleTypeGroup> groupList = repository.getListByParentId(id);
        if (groupList.size() > 0) {
            for (DtoSampleTypeGroup entity : groupList) {
                deleteById(entity.getId());
            }
        }

        //删除相关测试项目
        List<DtoSampleTypeGroup2Test> sam2TestList = sampleTypeGroup2TestService.findBySampleTypeGroupId(id);
        if (sam2TestList.size() > 0) {
            for (DtoSampleTypeGroup2Test obj : sam2TestList) {
                sampleTypeGroup2TestService.delete(obj);
            }
        }

        super.delete(id);
    }

    @Override
    public List<DtoSampleTypeDefaultGroup> findDefaultSampleGroup(List<String> bigSampleTypeIdList) {
        List<DtoSampleTypeDefaultGroup> sampleTypeDefaultGroupList = new ArrayList<>();
        Map<String, String> bigTypeId2DefaultGroupIdMap = new HashMap<>();
        Map<String, String> bigTypeId2DefaultXcGroupIdMap = new HashMap<>();
        if (StringUtil.isNotEmpty(bigSampleTypeIdList)) {
            List<DtoSampleType> bigSampleTypeList = sampleTypeRepository.findAll(bigSampleTypeIdList);
            //放置所有默认标签分组id
            List<String> defaultGroupIdList = new ArrayList<>();
            //放置所有默认现场任务分组id7
            List<String> defaultXcGroupIdList = new ArrayList<>();
            //遍历所有样品大类获取默认标签分组
            for (DtoSampleType bigSampleType : bigSampleTypeList) {
                if (StringUtil.isNotEmpty(bigSampleType.getDefaultLabelGroupId()) && !UUIDHelper.GUID_EMPTY.equals(bigSampleType.getDefaultLabelGroupId())) {
                    defaultGroupIdList.add(bigSampleType.getDefaultLabelGroupId());
                    bigTypeId2DefaultGroupIdMap.put(bigSampleType.getId(), bigSampleType.getDefaultLabelGroupId());
                }
                if (StringUtil.isNotEmpty(bigSampleType.getFieldTaskGroupId()) && !UUIDHelper.GUID_EMPTY.equals(bigSampleType.getFieldTaskGroupId())) {
                    defaultXcGroupIdList.add(bigSampleType.getFieldTaskGroupId());
                    bigTypeId2DefaultXcGroupIdMap.put(bigSampleType.getId(), bigSampleType.getFieldTaskGroupId());
                }
            }
            List<DtoSampleTypeGroup> sampleTypeGroupList = new ArrayList<>();
            if (StringUtil.isNotEmpty(defaultGroupIdList)) {
                sampleTypeGroupList = repository.findAll(defaultGroupIdList);
            }
            List<DtoSampleTypeGroup> xcSampleTypeGroupList = new ArrayList<>();
            if (StringUtil.isNotEmpty(defaultXcGroupIdList)) {
                xcSampleTypeGroupList = repository.findAll(defaultXcGroupIdList);
            }
            Map<String, DtoSampleTypeGroup> groupId2GroupMap = sampleTypeGroupList.stream().collect(Collectors.toMap(DtoSampleTypeGroup::getId, dto -> dto));
            Map<String, DtoSampleTypeGroup> xcGroupId2GroupMap = xcSampleTypeGroupList.stream().collect(Collectors.toMap(DtoSampleTypeGroup::getId, dto -> dto));

            for (DtoSampleType bigSampleType : bigSampleTypeList) {
                String bigTypeId = bigSampleType.getId();
                DtoSampleTypeDefaultGroup sampleTypeDefaultGroup = new DtoSampleTypeDefaultGroup();
                sampleTypeDefaultGroup.setBigSampleTypeId(bigTypeId);
                sampleTypeDefaultGroup.setDefaultLabelGroupId("");
                sampleTypeDefaultGroup.setDefaultLabelGroupName("");
                sampleTypeDefaultGroup.setLocalLabelGroupId("");
                sampleTypeDefaultGroup.setLocalLabelGroupName("");
                if (bigTypeId2DefaultGroupIdMap.containsKey(bigTypeId) || bigTypeId2DefaultXcGroupIdMap.containsKey(bigTypeId)) {
                    String dftGroupId = bigTypeId2DefaultGroupIdMap.get(bigTypeId);
                    DtoSampleTypeGroup defaultLabelGroup = StringUtil.isNotNull(dftGroupId) ? groupId2GroupMap.get(dftGroupId) : null;
                    String dftXcGroupId = bigTypeId2DefaultXcGroupIdMap.get(bigTypeId);
                    DtoSampleTypeGroup defaultXcGroup = StringUtil.isNotNull(dftXcGroupId) ? xcGroupId2GroupMap.get(dftXcGroupId) : null;
                    if (StringUtil.isNotNull(defaultLabelGroup) || StringUtil.isNotNull(defaultXcGroup)) {
                        sampleTypeDefaultGroup.setDefaultLabelGroupId(StringUtil.isNotNull(defaultLabelGroup) ? defaultLabelGroup.getId() : "");
                        sampleTypeDefaultGroup.setDefaultLabelGroupName(StringUtil.isNotNull(defaultLabelGroup) ? defaultLabelGroup.getGroupName() : "");
                        sampleTypeDefaultGroup.setLocalLabelGroupId(StringUtil.isNotNull(defaultXcGroup) ? defaultXcGroup.getId() : "");
                        sampleTypeDefaultGroup.setLocalLabelGroupName(StringUtil.isNotNull(defaultXcGroup) ? defaultXcGroup.getGroupName() : "");
                    }
                }
                sampleTypeDefaultGroupList.add(sampleTypeDefaultGroup);
            }
        }
        return sampleTypeDefaultGroupList;
    }

    @Override
    @Transactional
    public void copySampleTypeGroup(String id) {
        DtoSampleTypeGroup sampleTypeGroup = repository.findOne(id);
        List<DtoSampleTypeGroup> childSampleTypeGroups = repository.findByParentIdInAndGroupType(Collections.singletonList(id), EnumLIM.EnumGroupType.分组.getValue());
        List<String> childSampleTypeGroupIds = childSampleTypeGroups.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList());
        List<DtoSampleTypeGroup2Test> sampleTypeGroup2Tests = StringUtil.isNotEmpty(childSampleTypeGroupIds) ? sampleTypeGroup2TestRepository.findBySampleTypeGroupIds(childSampleTypeGroupIds) : new ArrayList<>();
        List<DtoSampleTypeGroup> saveList = new ArrayList<>();
        List<DtoSampleTypeGroup2Test> saveTestList = new ArrayList<>();
        DtoSampleTypeGroup sampleTypeGroupNew = new DtoSampleTypeGroup();
        BeanUtils.copyProperties(sampleTypeGroup, sampleTypeGroupNew, "id", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate");
        sampleTypeGroupNew.setGroupName(sampleTypeGroup.getGroupName() + "【复制】");
        saveList.add(sampleTypeGroupNew);
        for (DtoSampleTypeGroup childSampleTypeGroup : childSampleTypeGroups) {
            DtoSampleTypeGroup sampleTypeGroupSave = new DtoSampleTypeGroup();
            BeanUtils.copyProperties(childSampleTypeGroup, sampleTypeGroupSave, "id", "orgId", "creator", "createDate", "domainId", "modifier", "modifyDate");
            sampleTypeGroupSave.setParentId(sampleTypeGroupNew.getId());
            saveList.add(sampleTypeGroupSave);
            List<String> testIds = sampleTypeGroup2Tests.stream().filter(t -> t.getSampleTypeGroupId().equals(childSampleTypeGroup.getId())).map(DtoSampleTypeGroup2Test::getTestId).collect(Collectors.toList());
            for (String testId : testIds) {
                DtoSampleTypeGroup2Test testSave = new DtoSampleTypeGroup2Test();
                testSave.setTestId(testId);
                testSave.setSampleTypeGroupId(sampleTypeGroupSave.getId());
                saveTestList.add(testSave);
            }
        }
        if (StringUtil.isNotEmpty(saveList)) {
            repository.save(saveList);
        }
        if (StringUtil.isNotEmpty(saveTestList)) {
            sampleTypeGroup2TestRepository.save(saveTestList);
        }
    }
}