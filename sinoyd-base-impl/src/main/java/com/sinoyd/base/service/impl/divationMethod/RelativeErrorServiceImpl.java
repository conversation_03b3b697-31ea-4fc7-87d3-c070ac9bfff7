package com.sinoyd.base.service.impl.divationMethod;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.factory.quality.CurveCheck;
import com.sinoyd.base.factory.quality.QualityCorrectionFactor;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.CalculationService;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 相对误差
 *
 * <AUTHOR>
 * @version V1.0.0 2019/1/30
 * @since V100R001
 */
public class RelativeErrorServiceImpl implements QualityDivationService {

    @Override
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        String retStr = "";
        CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
        if (valueList.size() > 2) {
            //原样结果
            String samValue = valueList.get(0);
            //质控结果
            String qcValue = valueList.get(1);
            //均值结果
            String avgValue = valueList.get(2);
            if (DivationUtils.isNumber(samValue) && DivationUtils.isNumber(qcValue) && DivationUtils.isNumber(avgValue)) {
                //判断数据偏差是否使用绝对偏差 -- 检查项范围
                String qcRangeLimit = controlLimit.getRangeConfigData();//controlLimit.getRangeConfig();
                Boolean flag = true;
                if (StringUtil.isNotEmpty(qcRangeLimit) && !new CurveCheck().qcTypeValue().equals(controlLimit.getQcType())) {
                    flag = DivationUtils.calculationResult(qcRangeLimit, new BigDecimal(avgValue), calculationService);
                } else if (StringUtil.isNotEmpty(qcRangeLimit) && new CurveCheck().qcTypeValue().equals(controlLimit.getQcType())) {
                    //曲线校核用加入量做判断条件
                    flag = DivationUtils.calculationResult(qcRangeLimit, new BigDecimal(samValue), calculationService);
                }
                if (flag) {
                    String deviationFormula = map.getOrDefault("deviationFormula", "").toString();
                    if (StringUtil.isNotEmpty(deviationFormula)) {
                        retStr = getRetStrByFormula(calculationService, samValue, qcValue, deviationFormula);
                    } else {
                        //相对误差算法 |A-B|/A*100%，在原有的基础上A-B的值取绝对值
//                    subtract = subtract.abs();
                        //曲线校核和校正系数公式为 (|x-y|)/y*100
                        if (new CurveCheck().qcTypeValue().equals(controlLimit.getQcType()) ||
                                new QualityCorrectionFactor().qcTypeValue().equals(controlLimit.getQcType())) {
                            //曲线校核样和校正系数检验样，原样结果传的是理论值和校正点浓度,需要调换samValue和qcValue的值
                            String tmp;
                            tmp = samValue;
                            samValue = qcValue;
                            qcValue = tmp;
                            BigDecimal subtract = new BigDecimal(samValue).subtract(new BigDecimal(qcValue));
                            if (new BigDecimal(samValue).compareTo(BigDecimal.ZERO) != 0 && subtract.compareTo(BigDecimal.ZERO) != 0) {
                                if (new BigDecimal(qcValue).compareTo(BigDecimal.ZERO) != 0) {
//                            if (new BigDecimal(samValue).compareTo(BigDecimal.ZERO) != 0 && subtract.compareTo(BigDecimal.ZERO) != 0) {
                                    retStr = (subtract.divide(new BigDecimal(qcValue), 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100))).toString();
//                            } else {
//                                retStr = BigDecimal.ZERO.toString();
//                            }
                                } else {
                                    retStr = "无法计算";
                                }
                            } else {
                                retStr = BigDecimal.ZERO.toString();
                            }
                        } else {
                            BigDecimal subtract = new BigDecimal(qcValue).subtract(new BigDecimal(samValue));
                            if (subtract.compareTo(BigDecimal.ZERO) != 0) {
                                if (new BigDecimal(samValue).compareTo(BigDecimal.ZERO) != 0) {
//                                if (subtract.compareTo(BigDecimal.ZERO) != 0) {
                                    //(y-x)/x*100
                                    retStr = (subtract.divide(new BigDecimal(samValue), 10, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100))).toString();
//                                } else {
//                                    retStr = BigDecimal.ZERO.toString();
//                                }
                                } else {
                                    retStr = "无法计算";
                                }
                            } else {
                                retStr = BigDecimal.ZERO.toString();
                            }

                        }
                    }
                }
            }
        }
        map.put("qcRate", retStr);
    }

    /**
     * 根据公式配置获取误差
     *
     * @param calculationService
     * @param samValue
     * @param qcValue
     * @param deviationFormula
     * @return
     */
    private String getRetStrByFormula(CalculationService calculationService, String samValue, String qcValue, String deviationFormula) {
        String retStr = "";
        BigDecimal a = new BigDecimal(samValue), b = new BigDecimal(qcValue), c = a.subtract(b).abs();
        //判断分母是否为0，如果分母为0则无法计算
        if (c.compareTo(BigDecimal.ZERO) != 0) {
            if (deviationFormula.contains("a+b")) {
                BigDecimal sumValue = a.add(b);
                if (sumValue.compareTo(BigDecimal.ZERO) == 0) {
                    retStr = "无法计算，除数为0";
                }
            } else {
                if (a.compareTo(BigDecimal.ZERO) == 0) {
                    retStr = "无法计算，除数为0";
                }
            }
            if (!retStr.contains("无法计算")) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("a", a);
                paramMap.put("b", b);
                retStr = calculationService.calculationExpression(deviationFormula, paramMap).toString();
            }
        } else {
            retStr = BigDecimal.ZERO.toString();
        }
        if (DivationUtils.isNumber(retStr)) {
            retStr = new BigDecimal(retStr).multiply(new BigDecimal("100")).round(new MathContext(10, RoundingMode.HALF_EVEN)).toString();
        }
        return retStr;
    }
}
