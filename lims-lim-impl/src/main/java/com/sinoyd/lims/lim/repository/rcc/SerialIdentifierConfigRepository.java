package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;

import java.util.List;


/**
 * SerialIdentifierConfig数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface SerialIdentifierConfigRepository extends IBaseJpaRepository<DtoSerialIdentifierConfig, String> {

    /**
     * 修改配置编号编号时候编号是否重复
     *
     * @param configCode 配置编号
     * @param id         主键id
     * @return 返回是否存在其他相同的编号
     */
    Integer countByConfigCodeAndIdNot(String configCode, String id);

    /**
     * 新增配置编号的似乎还编号是否有重复
     *
     * @param configCode 配置编号
     * @return 返回是否存在其他相同的编号
     */
    Integer countByConfigCode(String configCode);

    /**
     * 根据配置类型获取相应的数据
     *
     * @param configType 配置类型
     * @return 返回想要的配置数据
     */
    List<DtoSerialIdentifierConfig> findByConfigType(Integer configType);

    /**
     * 根据配置类型，质控等级，质控类型获取相应的数据
     *
     * @param configType 配置类型
     * @param qcType     质控类型
     * @param qcGrade    质控等级
     * @return  List<DtoSerialIdentifierConfig>
     */
    List<DtoSerialIdentifierConfig> findByConfigTypeAndQcTypeAndQcGrade(Integer configType, Integer qcType, Integer qcGrade);
}
