package com.sinoyd.base.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class DtoConsumableStandard {

    @Excel(name = "唯一性编号(必填)",orderNum = "10",width = 15)
    private String consumableCode;

    @Excel(name = "批号",orderNum = "15",width = 22)
    private String productionCode;

    @Excel(name = "证书编号",orderNum = "20",width = 17)
    private String codeInStation;

    @Excel(name = "标准样品/消耗品名称(必填)",orderNum = "30",width = 22)
    private String consumableName;

    @Excel(name = "标准样品/消耗品分类(必填)",orderNum = "40",width = 22)
    private String categoryId;

    @Excel(name = "规格",orderNum = "50",width = 13)
    private String specification;

    @Excel(name = "等级",orderNum = "60",width = 13)
    private String grade;

    @Excel(name = "库存数量(必填)",orderNum = "70",width = 17)
    private BigDecimal inventory;

    @Excel(name = "单位",orderNum = "80",width = 12)
    private String unit;

    @Excel(name = "库存警告数量",orderNum = "90",width = 14)
    private BigDecimal warningNum;

    @Excel(name = "单价",orderNum = "100",width = 12)
    private BigDecimal unitPrice;

    @Excel(name = "化合物名称",orderNum = "105",width = 12)
    private String compoundName;

    @Excel(name = "浓度",orderNum = "110",width = 10)
    private String standard;

    @Excel(name = "不确定度",orderNum = "120",width = 10)
    private String uncertainty;

    @Excel(name = "范围低点",orderNum = "130",width = 10)
    private String rangeLow;

    @Excel(name = "范围高点",orderNum = "140",width = 10)
    private String rangeHigh;

    @Excel(name = "浓度量纲",orderNum = "150",width = 10)
    private String dimension;

    @Excel(name = "不确定度类型",orderNum = "160",width = 10)
    private String uncertainTypeName;

    @Excel(name = "定值日期",format = "yyyy-MM-dd",orderNum = "170",width = 20)
    private Date orderTime;

    @Excel(name = "入库日期(必填)",format = "yyyy-MM-dd",orderNum = "180",width = 20)
    private Date storageDate;

    @Excel(name = "有效日期(必填)",format = "yyyy-MM-dd",orderNum = "190",width = 20)
    private Date expiryDate;

    @Excel(name = "是否实验室加密(必填)",orderNum = "200",width = 20)
    private String isLabEncryption;

    @Excel(name = "管理员(必填)",orderNum = "210",width = 20)
    private String checkerId;

    @Excel(name = "存放位置",orderNum = "220",width = 11)
    private String keepPlace;

    @Excel(name = "备注",orderNum = "230",width = 11)
    private String reMark;

    @Excel(name = "稀释液",orderNum = "240",width = 17)
    private String dilutedSolution;

    @Excel(name = "稀释方法",orderNum = "250",width = 17)
    private String dilutionMethod;

    @Excel(name = "保存条件",orderNum = "260",width = 22)
    private String keepCondition;

    @Excel(name = "安全须知",orderNum = "270",width = 22)
    private String safetyInstruction;

    @Excel(name = "生产厂商",orderNum = "280",width = 22)
    private String manufacturerName;

    @Excel(name = "供应厂商",orderNum = "290",width = 22)
    private String supplierName;

    private Boolean isStandard = true;
}
