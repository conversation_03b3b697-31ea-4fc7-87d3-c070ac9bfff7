package com.sinoyd.lims.lim.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.lim.dto.customer.DtoExportAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.service.transform.ImportCommonCheckImpl;
import com.sinoyd.lims.lim.utils.ImportUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;

import java.util.Collections;
import java.util.List;

/**
 * 分析方法导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2022/9/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TransformAnalyzeMethodVerifyHandler extends ImportCommonCheckImpl implements IExcelVerifyHandler<DtoExportAnalyzeMethod> {
    /**
     * 工具类
     */
    private final ImportUtils importUtils = new ImportUtils();

    private List<DtoAnalyzeMethod> repoDataList;

    private List<DtoExportAnalyzeMethod> sheetExistDataList;

    @SneakyThrows
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoExportAnalyzeMethod exportAnalyzeMethod) {
        //导入数据处理,跳过空行,数据去除前后空格
        try {
            if (importUtils.checkObjectIsNull(exportAnalyzeMethod)) {
                return new ExcelVerifyHandlerResult(true);
            }
            importUtils.strToTrim(exportAnalyzeMethod);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }

        //校验结果
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //总校验错误信息
        StringBuilder failStr = new StringBuilder("第" + (exportAnalyzeMethod.getRowNum()+1) + "行数据校验有误");

        //本sheet内数据重复
        checkSeetDataRepeat(result,failStr,sheetExistDataList,exportAnalyzeMethod, Collections.singletonList("methodName"));
        //与数据库数据重复
        checkRepoDataRepeat(result,failStr,repoDataList,exportAnalyzeMethod, Collections.singletonList("methodName"));

        //更新重复校验容器
        sheetExistDataList.add(exportAnalyzeMethod);

        //构建异常信息
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }
}
