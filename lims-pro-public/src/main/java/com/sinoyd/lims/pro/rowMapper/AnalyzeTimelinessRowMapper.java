package com.sinoyd.lims.pro.rowMapper;

import com.sinoyd.lims.pro.vo.AnalyzeTimelinessMapVO;
import com.sinoyd.lims.pro.vo.AnalyzeTimelinessVO;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 分析及时率统计结果集映射
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/1/31
 **/
public class AnalyzeTimelinessRowMapper implements RowMapper<AnalyzeTimelinessMapVO> {
    @Override
    public AnalyzeTimelinessMapVO mapRow(ResultSet resultSet, int i) throws SQLException {
        AnalyzeTimelinessMapVO vo = new AnalyzeTimelinessMapVO();
        vo.setAnalystId(resultSet.getString("analystId"))
                .setAnalystName(resultSet.getString("analystName"))
                .setSamplingTime(resultSet.getDate("samplingTimeBegin"))
                .setAnalyseDayLen(resultSet.getInt("analyseDayLen"))
                .setAnalyzeTime(resultSet.getDate("analyzeTime"))
                .setDataStatus(resultSet.getInt("dataStatus"));
        return vo;
    }
}
