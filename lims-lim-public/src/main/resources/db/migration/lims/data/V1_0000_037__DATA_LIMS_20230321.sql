-- ----------------------------------------------
-- -------- 归属于LIMS部分的表相关初始化数据脚本 -------
-- -------- TB_LIM_ReportConfig -----------------
-- ----------------------------------------------

INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('02198c5c-b8c9-4e78-9950-4088f2fa388e', 1, 'WorkSheetZLFQ', '（317）重量法分析原始记录（气）_模板.xlsx', 'WorkSheet/（317）重量法分析原始记录（气）_模板.xls', 'output/WorkSheet/（317）重量法分析原始记录（气）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFQWorkSheetDataSourceImpl" }', 370, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-09 22:48:22', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:20:29', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetZLFQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('04fcd27b-4c96-42e6-8cf9-4ff611468a55', 1, 'WorkSheetZDXSYD', '（308）总氮、硝酸盐氮分析原始记录单_模板.xlsx', 'WorkSheet/（308）总氮、硝酸盐氮分析原始记录单_模板.xls', 'output/WorkSheet/（308）总氮、硝酸盐氮分析原始记录单.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZDXSYDWorkSheetDataSourceImpl" }', 440, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-08 22:04:45', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 11:23:30', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetZDXSYD', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('0C4E3A66-60E2-4AC5-89F1-8ACC8B12BF2C', 1, 'SampleLabel', '样品标签.xlsx', 'Sample/样品标签.xls', 'output/Sample/样品标签.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.SampleLabelServiceImpl', 'isBlank,isGroup,groupIds,isSample,sampleIds,receiveId', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-26 17:28:03', null, 'Sample', '-', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('0df57004-fbec-44c6-966e-da81d184bc42', 1, 'AtmosphereSamplingRecord', '大气环境采样和交接记录单.xlsx', 'SamplingRecords/大气环境采样和交接记录单.xls', 'output/SamplingRecords/大气环境采样和交接记录单.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.AtmosphereSamplingRecordService', '{"sort":"orderNum-"}', null, 660, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-27 16:08:30', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:47:34', 'sampleIds', 'Sampling', 'AtmosphereSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('104401a3-c446-4bf8-9e2f-127558479b9b', 1, 'Person', '人员一览表.xlsx', '/LIMReportForms/人员一览表.xls', 'output/LIMReportForms/人员一览表.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.PersonExportService', '{"sort":"orderNum-"}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-11-21 17:50:31', 'com.sinoyd.lims.lim.criteria.PersonCriteria', 'LIMReportForms', 'export/Person', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('115b7033-8fd6-4d62-89ef-d3a899ee99bc', 1, 'FlueGasSamplingRecord', '烟气分析仪测量记录表.xlsx', 'SamplingRecords/烟气分析仪测量记录表.xls', 'output/SamplingRecords/烟气分析仪测量记录表.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.FlueGasSamplingRecordService', '{"sort":"orderNum-"}', null, 680, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-05 14:54:16', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:47:09', 'sampleIds', 'Sampling', 'FlueGasSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('11e986ca-8eca-45e9-b173-692069145836', 1, 'SampleReceive', '交接单.xlsx', 'LIMReportForms/交接单.xls', 'output/LIMReportForms/交接单.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.proReport.DeliveryReceiptReportService', '', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 15:09:19', 'com.sinoyd.lims.lim.criteria.InstrumentUseRecordCriteria', 'LIMReportForms', 'SampleReceive', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('1325a1d0-9df3-49a7-b1a2-fdda3981f344', 1, 'WTWasteWater', '委托监测（废水）监测报告_模板.doc', 'Report/委托监测（废水）监测报告_模板.doc', 'output/Report/委托监测（废水）监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.WTWasteWaterReportService', '{"sort":"orderNum-"}', null, 1950, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-09 16:34:48', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:26:45', 'reportId,sortId', 'Report', 'WTWasteWater', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('13ca8480-6871-436e-88c1-002b8cdc0390', 1, 'SurfaceWaterAugustSecondSamplingRecord', 'pH、电导率、溶解氧、水温、浊度、透明度测试原始记录（8月）.xlsx', 'SamplingRecords/pH、电导率、溶解氧、水温、浊度、透明度测试原始记录（8月）.xls', 'output/SamplingRecords/pH、电导率、溶解氧、水温、浊度、透明度测试原始记录（8月）.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.SurfaceWaterAugustSecondSamplingRecordService', '{"sort":"orderNum-"}', '', 0, 3, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 11:42:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 11:42:00', 'sampleIds', 'Sampling', 'SurfaceWaterAugustSecondSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('15fab25f-60a3-4f83-b698-abb3e3ab419a', 1, 'AtmosphericPrecipitationWaterSamplingRecord', '大气降水采样和交接记录.xlsx', 'SamplingRecords/大气降水采样和交接记录.xls', 'output/SamplingRecords/大气降水采样和交接记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.AtmosphericPrecipitationWaterSamplingRecordService', '{"sort":"orderNum-"}', null, 860, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-14 14:16:18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-20 11:30:17', 'sampleIds', 'Sampling', 'AtmosphericPrecipitationWaterSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('17B39793-F9B8-4EC1-AB71-D116D302359C', 1, 'DetailDataProject', '项目分析数据.xlsx', 'Statistic/项目分析数据.xls', 'output/Statistic/项目分析数据.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.DetailDataExportService', '{"sort":"orderNum-"}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-09-27 17:25:28', 'com.sinoyd.lims.pro.criteria.DetailDataProjectCriteria', 'Statistic', 'export/DetailDataProject', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('17cafc42-dd44-4f57-b189-4c4eff5b2acd', 1, 'Smoke', '烟气监测报告_模板.doc', 'Report/烟气监测报告_模板.doc', 'output/Report/烟气监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.SmokeReportService', '{"sort":"orderNum-"}', null, 1870, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-21 13:44:44', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:27:43', 'reportId,sortId', 'Report', 'Smoke', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('1b665218-f21b-4935-9c60-e0811c1a4e91', 1, 'AreaNoise', '区域噪声监测报告_模板.doc', 'Report/区域噪声监测报告_模板.doc', 'output/Report/区域噪声监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.AreaNoiseReportService', '{"sort":"orderNum-"}', null, 1770, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-24 09:50:13', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:28:29', 'reportId,sortId', 'Report', 'AreaNoise', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('1b87da15-8ae4-4e64-9f29-62a5d313fdcb', 1, 'ZFZDWasteWater', '执法监测（重点源废水）监测报告_模板.doc', 'Report/执法监测（重点源废水）监测报告_模板.doc', 'output/Report/执法监测（重点源废水）监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.ZFZDWasteWaterReportService', '{"sort":"orderNum-"}', null, 1980, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-12 21:31:56', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:26:52', 'reportId,sortId', 'Report', 'ZFZDWasteWater', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('1b88786d-e7bc-494f-9680-0e4cf24ec358', 1, 'SurfaceWaterAugustSamplingRecord', '地表水采样和交接记录（8月）.xlsx', 'SamplingRecords/地表水采样和交接记录（8月）.xls', 'output/SamplingRecords/地表水采样和交接记录（8月）.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.SurfaceWaterAugustSamplingRecordService', '{"sort":"orderNum-"}', '', 890, 3, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 11:24:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-20 11:30:05', 'sampleIds', 'Sampling', 'SurfaceWaterAugustSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('1c9ce2dc-6851-45e8-8223-7353e1574344', 1, 'EnvironmentStatistics', '环境质量数据统计.xlsx', 'Statistic/环境质量数据统计.xls', 'output/Statistic/环境质量数据统计.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.EnvironmentStatisticsExportService', '{"sort":"orderNum-"}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-08-18 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-13 17:34:33', 'com.sinoyd.lims.pro.criteria.EnvironmentStatisticsExportCriteria', 'Statistic', 'export/EnvironmentStatistics', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('1cb6a072-03f2-4c93-ab0a-5c19fc278fce', 1, 'WorkSheetPHDDL', '（319）pH、电导率测试原始记录_模板.xlsx', 'WorkSheet/（319）pH、电导率测试原始记录_模板.xls', 'output/WorkSheet/（319）pH、电导率测试原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "PHDDLWorkSheetDataSourceImpl" }', 350, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-10 14:02:37', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:21:44', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetPHDDL', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('1e42ed56-8181-4284-89eb-f60e25606a3b', 1, 'QualityControl', '质控分析表.xlsx', 'LIMReportForms/质控分析表.xls', 'output/LIMReportForms/质控分析表.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.statisticReport.QualityAnalyzeReportService', '{"sort":"orderNum-"}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 16:18:07', 'com.sinoyd.lims.pro.criteria.AnalysisQualityStatisticsCriteria', 'LIMReportForms', 'export/QualityControl', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('1fd82f64-5e64-43ce-ac20-d29d1c609c77', 1, 'SmokeSampelRecord', '自动烟尘(气、油烟)采样记录记录表.xlsx', 'SamplingRecords/自动烟尘(气、油烟)采样记录记录表.xls', 'output/SamplingRecords/自动烟尘(气、油烟)采样记录记录表.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.SmokeSamplingRecordService', '{"sort":"orderNum-"}', null, 670, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-29 11:15:57', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:47:17', 'sampleIds', 'Sampling', 'SmokeSampelRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('22b0aca6-df06-45a0-b456-41864979f2c5', 1, 'WorkSheetRLFGMS', '（310）容量法分析原始记录（高锰酸盐指数）_模板.xlsx', 'WorkSheet/（310）容量法分析原始记录（高锰酸盐指数）_模板.xls', 'output/WorkSheet/（310）容量法分析原始记录（高锰酸盐指数）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "RLFWorkSheetDataSourceImpl" }', 430, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-08 23:17:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 11:24:11', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetRLFGMS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('23928ece-1569-4447-a92b-66a4b00958ce', 1, 'SoilSamplingRecord', '土壤采样和交接记录.xlsx', 'SamplingRecords/土壤采样和交接记录.xls', 'output/SamplingRecords/土壤采样和交接记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.SoilSamplingRecordService', '{"sort":"orderNum-"}', null, 500, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-18 15:55:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:48:39', 'sampleIds', 'Sampling', 'SoilSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('29837dae-c309-4614-9d9b-ed923d89a420', 1, 'WorkSheetSDXSBSF', '（323）色度（稀释倍数法） 分析原始记录单_模板.xlsx', 'WorkSheet/（323）色度（稀释倍数法） 分析原始记录单_模板.xls', 'output/WorkSheet/（323）色度（稀释倍数法） 分析原始记录单.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "SDXSBSFWorkSheetDataSourceImpl" }', 330, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-10 15:24:41', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:23:13', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSDXSBSF', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('2a3106df-09b2-4503-b5cc-6858a892c625', 1, 'WorkSheetZYJT', '（357）总有机碳(TOC)分析原始记录_模板.xlsx', 'WorkSheet/（357）总有机碳(TOC)分析原始记录_模板.xls', 'output/WorkSheet/（357）总有机碳(TOC)分析原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZYJTWorkSheetDataSourceImpl" }', 70, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-20 13:46:42', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:14:58', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetZYJT', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('2c1b684f-15fe-49d5-ba8e-71f93abc43b9', 1, 'InstrumentUseRecord', '仪器设备使用记录表.xlsxx', 'LIMReportForms/仪器设备使用记录表.xls', 'output/LIMReportForms/仪器设备使用记录表.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.InstrumentUseRecordExportService', '{"sort":"startTime-"}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-28 10:12:57', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 16:04:00', 'com.sinoyd.lims.lim.criteria.InstrumentUseRecordCriteria', 'LIMReportForms', 'export/InstrumentUseRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('2cd060b9-bdc5-4264-91b9-89f4a4d04485', 1, 'WorkSheetLYZXSST', '（344）冷原子吸收法分析原始记录（水、土）_模板.xlsx', 'WorkSheet/（344）冷原子吸收法分析原始记录（水、土）_模板.xls', 'output/WorkSheet/（344）冷原子吸收法分析原始记录（水、土）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZYGFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 170, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-18 11:18:17', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:03:52', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLYZXSST', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('2d971659-fd73-4998-b4a1-d3ca495fa4b5', 1, 'WorkSheetICPMSKLW', '（346）ICP-MS分析原始记录（颗粒物）_模板.xlsx', 'WorkSheet/（346）ICP-MS分析原始记录（颗粒物）_模板.xls', 'output/WorkSheet/（346）ICP-MS分析原始记录（颗粒物）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "ICPMSKLWWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 150, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-19 15:34:08', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-11-24 14:13:18', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetICPMSKLW', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('308B0522-DDB1-428B-9B6E-A30CEDF60FF8', 1, 'ConsumableLog', '消耗品领用记录.xlsx', 'LIMReportForms/消耗品领用记录.xlsx', 'output/LIMReportForms/消耗品领用记录.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.ConsumableLogExportService', '{"sort":"consumableName+specification"}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-21 09:22:06', 'com.sinoyd.base.criteria.ConsumableCriteria', 'LIMReportForms', 'export/ConsumableLog', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('335a4134-96c6-4746-8ff8-afc97ddd92b1', 1, 'QuotationSheet', '检测报价单.xls', '', 'output/LIMReportForms/检测报价单.xls', 'application/excel', 'com.sinoyd.lims.report.service.limReportForms.QuotationSheetService', '', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-19 14:40:36', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-19 14:40:36', '', 'LIMReportForms', 'QuotationSheet', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('33c70d5a-feb6-4faa-8468-f56cf870f94e', 1, 'CoalSamplingRecord', '煤样采集和交接记录.xlsx', 'SamplingRecords/煤样采集和交接记录.xls', 'output/SamplingRecords/煤样采集和交接记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.CoalSamplingRecordService', '{"sort":"orderNum-"}', null, 490, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-17 15:03:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:48:55', 'sampleIds', 'Sampling', 'CoalSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('34983ed3-2cb1-4fc9-a4b2-1fe19b7be730', 1, 'InstrumentVerificationCalibration', '计量仪器检定校准情况记录.xlsx', 'LIMReportForms/计量仪器检定校准情况记录.xls', 'output/LIMReportForms/计量仪器检定校准情况记录.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.instrument.InstrumentVerificationCalibrationService', null, null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-09 14:47:26', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-06-11 09:02:02', null, 'LIMReportForms', null, false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('34afebc4-6f06-4976-88f2-68fdaaff009e', 1, 'WorkSheetJJGWJG', '（354)甲基汞、烷基汞分析原始记录_模板.xlsx', 'WorkSheet/（354)甲基汞、烷基汞分析原始记录_模板.xls', 'output/WorkSheet/（354)甲基汞、烷基汞分析原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "JJGWJGWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 90, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-20 08:21:27', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:12:20', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetJJGWJG', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('36242497-8d85-443c-b9c1-f8ddf68b61eb', 1, 'WorkSheetSPSZLJS', '（327）色谱、色质联机分析原始记录(水)_模板.xlsx', 'WorkSheet/（327）色谱、色质联机分析原始记录(水)_模板.xls', 'output/WorkSheet/（327）色谱、色质联机分析原始记录(水).xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJSWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 310, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 10:23:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:25:36', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSPSZLJS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('366c1331-f7e2-452c-a633-ee28e9aff959', 1, 'FoodBill', '夜餐费补助发放表.xlsx', 'LIMReportForms/夜餐费补助发放表.xlsx', 'output/LIMReportForms/夜餐费补助发放表.xlsx', 'application/excel', 'com.sinoyd.lims.oa.service.FoodBillReportService', '', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-05 19:25:45', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-05 19:25:45', '', 'LIMReportForms', '-', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('383cf141-7199-4c2d-a576-7e1992763a08', 1, 'WorkSheetLYZXSFQ', '（343）冷原子吸收法分析原始记录（气）_模板.xlsx', 'WorkSheet/（343）冷原子吸收法分析原始记录（气）_模板.xls', 'output/WorkSheet/（343）冷原子吸收法分析原始记录（气）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZXSFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 180, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-18 10:56:58', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:03:16', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLYZXSFQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('3847f64f-e4a5-4da5-a3e9-63566e17482f', 1, 'ConsumableYZD', '易制毒材料出入库登记表.xlsx', 'LIMReportForms/易制毒材料出入库登记表.xlsx', 'output/LIMReportForms/易制毒材料出入库登记表.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.limReportForms.jx.ConsumableYZDService', '', '', 6, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-05 19:24:27', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-19 14:42:19', '', 'LIMReportForms', 'ConsumableYZD', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('3b6975bc-79d2-46c3-8f5c-b0c8a6808d6a', 1, 'AttendanceTotal', '考勤统计表.xlsx', 'LIMReportForms/考勤统计表.xlsx', 'output/LIMReportForms/考勤统计表.xlsx', 'application/excel', 'com.sinoyd.lims.oa.service.AttendanceTotalReportService', '', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-05 19:25:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-05 19:25:01', '', 'LIMReportForms', '-', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('3d58569c-5314-4d09-af71-c81b762d232d', 1, 'WorkSheetFGGDQ', '（302）分光光度法原始记录（气）_模板.xlsx', 'WorkSheet/（302）分光光度法原始记录（气）_模板.xls', 'output/WorkSheet/（302）分光光度法原始记录（气）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "FGGDQWorkSheetDataSourceImpl" }', 500, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-05 17:34:59', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 11:19:30', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFGGDQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('3daed502-3166-4cbc-9cf8-cbd23caf1d36', 1, 'SolidWasteSamplingRecord', '工业固体废物采样和交接记录.xlsx', 'SamplingRecords/工业固体废物采样和交接记录.xls', 'output/SamplingRecords/工业固体废物采样和交接记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.SolidWasteSamplingRecordService', '{"sort":"orderNum-"}', null, 460, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-18 15:34:11', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:49:10', 'sampleIds', 'Sampling', 'SolidWasteSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('3e842677-05fb-4af0-ad53-8fcc69fe017c', 1, 'Precipitation', '降雨常规监测报告_模板.doc', 'Report/降雨常规监测报告_模板.doc', 'output/Report/降雨常规监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.PrecipitationReportService', '', null, 1920, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-08 15:37:11', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:29:04', 'reportId,sortId', 'Report', 'Precipitation', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('40e81b2c-e0b7-4a13-8c00-6dcac5fdb006', 1, 'RegionalEnvironmentNoiseSamplingRecord', '区域环境噪声监测原始记录.xlsx', 'SamplingRecords/区域环境噪声监测原始记录.xls', 'output/SamplingRecords/区域环境噪声监测原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.RegionalEnvironmentalNoiseSamplingRecordService', '{"sort":"orderNum-"}', null, 610, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-22 14:17:58', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:48:01', 'sampleIds', 'Sampling', 'RegionalEnvironmentNoiseSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('43bc4a72-5fbe-4ede-bc85-24caab90cc29', 1, 'PollutionWasteWaterSamplingRecord', '污染源废水采样和交接记录.xlsx', 'SamplingRecords/污染源废水采样和交接记录.xls', 'output/SamplingRecords/污染源废水采样和交接记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.PollutionWasteWaterSamplingRecordService', '{"sort":"orderNum-"}', null, 880, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-13 13:57:11', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-24 14:43:42', 'sampleIds', 'Sampling', 'PollutionWasteWaterSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('444e0e74-53c8-4679-90fc-8adb774efe77', 1, 'WorkSheetGTFWFSX', '（321）固体废物腐蚀性原始记录_模板.xlsx', 'WorkSheet/（321）固体废物腐蚀性原始记录_模板.xls', 'output/WorkSheet/（321）固体废物腐蚀性原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "GTFWFSXWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "1","mainDataExcludeTypeList" : [{"sampleCategory" : "1", "qcGrade" : "2", "qcType" : "1"}], "relDataTypeList" : [{"sampleCategory" : "1", "qcGrade" : "2", "qcType" : "1"}]}}', 300, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 10:32:29', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:26:16', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetGTFWFSX', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('476240ad-6f2b-42f2-a4ec-4b0a83a329b7', 1, 'Instrument', '仪器一览表.xlsx', 'LIMReportForms/仪器一览表.xls', 'output/LIMReportForms/仪器一览表.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.InstrumentExportService', '{"sort":"instrumentsCode"}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 16:01:44', 'com.sinoyd.base.criteria.InstrumentCriteria', 'LIMReportForms', 'export/Instrument', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('486f9342-7c83-4afe-a98c-f7d36ecdefcf', 1, 'DetectionTask', '任务单.xlsx', 'LIMReportForms/任务单_模板.xls', 'output/LIMReportForms/任务单_模板.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.proReport.DetectionTaskReportService', '', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-03-01 17:04:35', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:43:12', 'projectId', 'LIMReportForms', 'DetectionTask', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('48c167b4-c125-4674-bcbc-e56de0355d29', 1, 'AttendanceDetail', '考勤汇总表_模板.xlsx', 'LIMReportForms/考勤汇总表_模板.xls', 'output/LIMReportForms/考勤汇总表_模板.xlsx', 'application/excel', 'com.sinoyd.lims.oa.service.AttendanceReportService', '', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-27 15:06:49', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-05 20:29:48', '', 'LIMReportForms', ' ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('4be62f6f-49cb-4a61-8a73-b0e50cb58632', 1, 'WorkSheetRLFS', '（312）容量法分析原始记录（水）_模板.xlsx', 'WorkSheet/（312）容量法分析原始记录（水）_模板.xls', 'output/WorkSheet/（312）容量法分析原始记录（水）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "RLFWorkSheetDataSourceImpl" }', 410, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-09 15:16:20', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:06:07', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetRLFS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('4da29a8a-a8e4-49d8-962e-e4b13ce71041', 1, 'WorkSheetLZXZDJF', '离子选择电极法分析原始记录_模板.xls', 'WorkSheet/离子选择电极法分析原始记录_模板.xls', 'output/WorkSheet/离子选择电极法分析原始记录.xls', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "LZXZDJFWorkSheetDataSourceImpl"}', 325, 2, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 10:05:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-21 10:07:31', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLZXZDJF', false, '', '', '', '', null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('4e416b15-e6b9-4bda-b3d7-6a7e87182c38', 1, 'ZFUnOrgEnv', '执法监测（无组织、环境空气）报告_模板.doc', 'Report/执法监测（无组织、环境空气）报告_模板.doc', 'output/Report/执法监测（无组织、环境空气）报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.ZFUnOrgEnvReportService', '{"sort":"orderNum-"}', null, 1890, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-21 13:46:44', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:27:31', 'reportId,sortId', 'Report', 'ZFUnOrgEnv', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('4f29dab4-f73a-4d71-88d0-e6b693c0ae4c', 1, 'SensitiveEnvironmentNoiseSamplingRecord', '敏感点（区域）环境噪声监测原始记录.xlsx', 'SamplingRecords/敏感点（区域）环境噪声监测原始记录.xls', 'output/SamplingRecords/敏感点（区域）环境噪声监测原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.SensitiveEnvironmentNoiseSamplingRecordService', '{"sort":"orderNum-"}', null, 580, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-21 15:51:33', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:48:23', 'sampleIds', 'Sampling', 'SensitiveEnvironmentNoiseSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('50d92e04-c761-4d93-b99d-152a190dd9c4', 1, 'WorkSheetFGGDHYS', '（306）分光光度法原始记录（丁基黄原酸）_模板.xlsx', 'WorkSheet/（306）分光光度法原始记录（丁基黄原酸）_模板.xls', 'output/WorkSheet/（306）分光光度法原始记录（丁基黄原酸）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 470, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-08 11:29:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-13 15:34:17', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFGGDHYS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('519e62ea-a900-4635-b6fc-fe106fca73ac', 1, 'WorkSheetYLSA', '（309）叶绿素a测得原始记录_模板.xlsx', 'WorkSheet/（309）叶绿素a测得原始记录_模板.xls', 'output/WorkSheet/（309）叶绿素a测得原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 450, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-08 21:52:20', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 11:22:53', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetYLSA', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('55fc5d14-cc9a-4396-be34-8b2e4fceb009', 1, 'Evection', '出差申请单_模板.xlsx', 'LIMReportForms/出差申请单_模板.xls', 'output/LIMReportForms/出差申请单.xlsx', 'application/excel', 'com.sinoyd.lims.oa.service.EvectionReportService', 'ids', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-28 08:57:18', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-04 13:56:59', null, 'LIMReportForms', null, false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('5b54620b-0197-43c9-acf5-ffb431812c78', 1, 'WorkSheetFLZXZDJFWZZ', '（325）氟离子选择电极法分析原始记录（气-无组织）_模板.xlsx', 'WorkSheet/（325）氟离子选择电极法分析原始记录（气-无组织）_模板.xls', 'output/WorkSheet/氟离子选择电极法分析原始记录（气-无组织）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "FLZXZDJFWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}', 0, 2, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:07:07', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:07:07', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFLZXZDJF', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('5d5c9d4e-20bf-4478-8dd4-84eb4ee92620', 1, 'WorkSheetZLFJC', '（318）重量法分析原始记录（降尘）_模板.xlsx', 'WorkSheet/（318）重量法分析原始记录（降尘）_模板.xls', 'output/WorkSheet/（318）重量法分析原始记录（降尘）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFWorkSheetDataSourceImpl" }', 360, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-10 09:05:51', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:21:02', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetZLFJC', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('5dba6b7f-fcdb-492d-84eb-7e93f3e87894', 1, 'GaseousPollutantsSamplingRecord', '有组织气态污染物采样和交接记录表(不等速采样).xlsx', 'SamplingRecords/有组织气态污染物采样和交接记录表(不等速采样).xls', 'output/SamplingRecords/有组织气态污染物采样和交接记录表(不等速采样).xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.GaseousPollutantsSamplingRecordService', '{"sort":"orderNum-"}', null, 700, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-31 15:31:24', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:46:48', 'sampleIds', 'Sampling', 'GaseousPollutantsSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('5df5a558-4add-4a4c-a94b-a60e1d62b359', 1, 'Consumable', '消耗品清单.xlsx', 'LIMReportForms/消耗品清单.xlsx', 'output/LIMReportForms/消耗品清单.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.ConsumableExportService', '{"sort":"consumableName+specification"}', null, 10, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-06 09:30:59', 'com.sinoyd.base.criteria.ConsumableCriteria', 'LIMReportForms', 'export/Consumable', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('5f3df1da-8425-43a5-99d6-3ff37a19ac14', 1, 'WorkSheetKXFYJLS', '（355）可吸附有机卤素分析原始记录_模板.xlsx', 'WorkSheet/（355）可吸附有机卤素分析原始记录_模板.xls', 'output/WorkSheet/（355）可吸附有机卤素分析原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "KXFYJLSWorkSheetDataSourceImpl" }', 80, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-20 10:31:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:12:48', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetKXFYJLS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('6758fbb9-5e51-4657-a019-2b0581116db5', 1, 'LieuHoliday', '补休审批表_模板.xlsx', 'LIMReportForms/补休审批表_模板.xls', 'output/LIMReportForms/补休审批表.xlsx', 'application/excel', 'com.sinoyd.lims.oa.service.LieuHolidayReportService', 'id', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-28 16:21:28', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-15 20:15:41', '', 'LIMReportForms', ' ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('67963bc5-eac5-47ff-a926-857b7d1b7181', 1, 'ProjectInstrument', '仪器设备出入库情况记录_模板.xlsx', 'LIMReportForms/仪器设备出入库情况记录_模板.xls', 'output/LIMReportForms/仪器设备出入库情况记录.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.ProjectInstrumentQueryReportService', '', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-28 13:10:11', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 16:06:42', 'com.sinoyd.lims.lim.criteria.ProjectInstrumentCriteria', 'LIMReportForms', 'ProjectInstrument', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('6a7692b8-fcd6-4741-bf9a-8ab20e57cbe5', 1, 'SmokeBlack', '烟气黑度监测报告_模板.doc', 'Report/烟气黑度监测报告_模板.doc', 'output/Report/烟气黑度监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.SmokeBlackReportService', '{"sort":"orderNum-"}', null, 1880, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-21 13:42:31', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:27:38', 'reportId,sortId', 'Report', 'SmokeBlack', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('6b985723-75cb-4e65-95fc-e9daee6275d1', 1, 'WorkSheetLZSPFQX', '（351）离子色谱法分析原始记录（带曲线）_模板.xls', 'WorkSheet/（351）离子色谱法分析原始记录（带曲线）_模板.xls', 'output/WorkSheet/（351）离子色谱法分析原始记录（带曲线）.xls', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "LZSPFQXWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 111, 2, '', false, '5f7bcf90feb545968424b0a872863876', 'a70e76eb-0638-48d5-9660-6090170bc163', '2023-02-26 21:31:44', '5f7bcf90feb545968424b0a872863876', 'a70e76eb-0638-48d5-9660-6090170bc163', '2023-02-26 21:33:52', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLZSPFQX', false, '', '', '', '', null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('711be950-f96f-463a-aa59-1f66355ba1f2', 1, 'StandardMaterial', '标准物质领用记录.xlsx', 'LIMReportForms/标准物质领用记录.xlsx', 'output/LIMReportForms/标准物质领用记录.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.limReportForms.StandardMaterialService', '', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-19 14:20:27', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-19 14:20:27', 'StandardMaterial', 'LIMReportForms', 'StandardMaterial', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('7146c6a6-6e6c-4494-b16c-e587dba931a4', 1, 'TestExport', '测试项目导出_模板.xls', 'LIMReportForms/测试项目导出_模板.xls', 'output/LIMReportForms/测试项目导出.xls', 'application/excel', 'com.sinoyd.lims.report.service.exports.TestExportService', '{"sort":"sampleTypeId+,orderNum-"}', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-05 10:17:38', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-05 17:00:40', 'com.sinoyd.lims.lim.criteria.TestCriteria', 'LIMReportForms', 'export/TestExport', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('7157741c-7783-42e3-8dff-6e2f19d21e18', 1, 'WorkSheetZLFQWZZ', '（317）重量法分析原始记录（气-无组织）_模板.xlsx', 'WorkSheet/（317）重量法分析原始记录（气-无组织）_模板.xls', 'output/WorkSheet/重量法分析原始记录（气-无组织）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFQWorkSheetDataSourceImpl" }', 0, 2, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:03:27', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:03:27', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetZLFQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('76d8d28c-f387-405f-8020-a2bbc48e86f3', 1, 'WorkSheetZLFS', '（315）重量法分析原始记录（水）_模板.xlsx', 'WorkSheet/（315）重量法分析原始记录（水）_模板.xls', 'output/WorkSheet/（315）重量法分析原始记录（水）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFWorkSheetDataSourceImpl" }', 390, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-09 16:06:23', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:11:43', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetZLFS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('76ee46dc-6f2c-4e4b-9e03-3a74a6746303', 1, 'WorkSheetLZSPF', '（351）离子色谱法分析原始记录_模板.xlsx', 'WorkSheet/（351）离子色谱法分析原始记录_模板.xls', 'output/WorkSheet/（351）离子色谱法分析原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 110, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-19 16:43:20', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:11:27', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLZSPF', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('76fceb26-1a14-44fc-9f2f-54328d8639fc', 1, 'WorkSheetSPSZLJT', '（328）色谱、色质联机分析原始记录(土)_模板.xlsx', 'WorkSheet/（328）色谱、色质联机分析原始记录(土)_模板.xls', 'output/WorkSheet/（328）色谱、色质联机分析原始记录(土).xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 290, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 11:20:43', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:26:49', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSPSZLJT', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('7a8d0a5d-1ff7-4236-b28d-2d24afe568a3', 1, 'UnderGroundWaterSamplingRecord', '地下水采样和交接记录.xlsx', 'SamplingRecords/地下水采样和交接记录.xls', 'output/SamplingRecords/地下水采样和交接记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.UnderGroundWaterSamplingRecordService', '{"sort":"orderNum-"}', null, 870, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-08 17:03:55', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-20 11:30:14', 'sampleIds', 'Sampling', 'UnderGroundWaterSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('7c9bcdc5-efa2-4e3e-832c-1ab19aa56487', 1, 'ZFWasteWater', '执法监测（废水）监测报告_模板.doc', 'Report/执法监测（废水）监测报告_模板.doc', 'output/Report/执法监测（废水）监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.ZFWasteWaterReportService', '{"sort":"orderNum-"}', null, 1970, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-12 21:30:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:26:39', 'reportId,sortId', 'Report', 'ZFWasteWater', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('80298b64-3579-48fe-aba4-54f966924ed6', 1, 'EvaluationCriteria', '评价标准信息_模板.xlsx', 'LIMReportForms/评价标准信息_模板.xlsx', 'output/LIMReportForms/评价标准信息.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.ExportEvaluationCriteriaService', '{"sort":"name"}', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-27 18:38:43', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-27 18:43:29', 'com.sinoyd.lims.lim.criteria.EnvironmentalCriteria', 'LIMReportForms', 'export/EvaluationCriteria', false, '', '', '', '', null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('8068f37d-accb-40cb-9e49-9c4892f97d48', 1, 'JcNoise', '监测报告格式使用版（噪声）_模板.doc', 'Report/监测报告格式使用版（噪声）_模板.doc', 'output/Report/监测报告格式使用版（噪声）报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.JcNoiseReportService', '{"sort":"orderNum-"}', null, 1790, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-24 09:47:26', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:28:19', 'reportId,sortId', 'Report', 'JcNoise', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('80850661-2665-4c44-9813-3475f58d19c6', 1, 'InstrumentLabel', '仪器标签.xlsx', 'LIMReportForms/仪器标签.xlsx', 'output/LIMReportForms/仪器标签.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.limReportForms.InstrumentLabelService', '', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-29 10:09:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-01-29 13:33:01', '', 'LIMReportForms', 'InstrumentLabel', false, '', '', '', '', null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('8259d792-6390-40e9-b31c-90e8b30ad138', 1, 'RailwayNoiseSamplingRecord', '铁路边界噪声监测原始记录.xlsx', 'SamplingRecords/铁路边界噪声监测原始记录.xls', 'output/SamplingRecords/铁路边界噪声监测原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.RailwayNoiseSamplingRecordService', '{"sort":"orderNum-"}', null, 570, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-23 11:27:01', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:48:27', 'sampleIds', 'Sampling', 'RailwayNoiseSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('83af429e-3ebe-4e37-b1d4-3ba6f140b81f', 1, 'WorkSheetLYZYGS', '（334）（冷）原子荧光法分析原始记录(水)_模板.xlsx', 'WorkSheet/（334）（冷）原子荧光法分析原始记录(水)_模板.xls', 'output/WorkSheet/（334）（冷）原子荧光法分析原始记录(水).xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 250, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-16 22:01:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:31:13', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLYZYGS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('847c4f73-e898-45e7-8e83-2f4e001016da', 1, 'OtSummary', '加班汇总审批单.xlsx', 'LIMReportForms/加班汇总审批单.xlsx', 'output/LIMReportForms/加班汇总审批单.xlsx', 'application/excel', 'com.sinoyd.lims.oa.service.WorkOverTimeTotalReportService', '', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-13 08:27:56', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-13 08:27:56', '', 'LIMReportForms', 'OtSummary', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('8592c3d4-6656-4223-8282-00791fd324b0', 1, 'WorkSheetJYL', '（359）降雨量分析原始记录_模板.xlsx', 'WorkSheet/（359）降雨量分析原始记录_模板.xls', 'output/WorkSheet/（359）降雨量分析原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 40, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-20 14:14:34', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:16:02', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetJYL', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('87a9ace0-d4b9-4e97-86ac-c07239e8f9f6', 1, 'FuncNoise', 'Report', 'Report/功能区噪声监测报告_模板.doc', 'output/Report/功能区噪声监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.FuncNoiseReportService', '{"sort":"orderNum-"}', null, 0, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-24 09:45:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-17 10:05:21', 'reportId,sortId', 'Report', 'FuncNoise', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('8ac8f239-048b-4e7e-b26a-b8a6df89179b', 1, 'WorkSheetYW', '（338）油雾分析原始记录_模板.xlsx', 'WorkSheet/（338）油雾分析原始记录_模板.xls', 'output/WorkSheet/（338）油雾分析原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "YWWorkSheetDataSourceImpl" }', 220, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-17 10:14:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:01:20', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetYW', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('8b1c3b43-6ecf-470e-b968-0d4c4b798ef1', 1, 'WaterReport', '水监测报告_模板.doc', 'Report/水监测报告_模板.doc', 'output/Report/水监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.wordReport.WaterReportService', '{"sort":"orderNum-"}', null, 1940, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-16 16:17:37', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:27:04', 'reportId,sortId', 'Report', 'WaterReport', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('944766ed-960b-4c13-a667-e58feb129eca', 1, 'WorkSheetRLFHXXYL', '（311）容量法分析原始记录（化学需氧量）_模板.xlsx', 'WorkSheet/（311）容量法分析原始记录（化学需氧量）_模板.xls', 'output/WorkSheet/（311）容量法分析原始记录（化学需氧量）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "RLFWorkSheetDataSourceImpl" }', 420, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-09 14:39:37', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 11:25:11', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetRLFHXXYL', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('94a483db-4841-4c22-95e5-2b892eb3ca0b', 1, 'BaseQuery', '自定义查询结果_模板.xlsx', 'LIMReportForms/自定义查询结果_模板.xls', 'output/LIMReportForms/自定义查询结果.xlsx', 'application/excel', 'com.sinoyd.lims.query.service.ItemExportService', 'com.sinoyd.lims.query.criteria.SelectBaseItemCriteria', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-08-10 17:02:00', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-08-13 08:49:00', null, 'LIMReportForms', null, false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('975F6767-EBD6-4AF7-B0C6-8A9D9730C822', 1, 'InstrumentUseRecord', '仪器设备使用记录表.xlsxx', 'LIMReportForms/仪器设备使用记录表.xls', 'output/LIMReportForms/仪器设备使用记录表.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.InstrumentUseRecordExportService', 'com.sinoyd.lims.lim.criteria.InstrumentUseRecordCriteria', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-10 20:58:37', null, 'LIMReportForms', null, false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('9ad6013c-2e67-44c8-8547-824d0c412131', 1, 'FactoryEnvironmentNoiseSamplingRecord', '工业企业厂界环境噪声测量记录.xlsx', 'SamplingRecords/工业企业厂界环境噪声测量记录.xls', 'output/SamplingRecords/工业企业厂界环境噪声测量记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.FactoryEnvironmentNoiseSamplingRecordService', '{"sort":"orderNum-"}', null, 630, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-15 17:11:41', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:47:48', 'sampleIds', 'Sampling', 'FactoryEnvironmentNoiseSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('9c2a604b-1be9-40d6-b747-662690e98952', 1, 'SurfaceWaterSamplingRecord', '地表水采样和交接记录.xlsx', 'SamplingRecords/地表水采样和交接记录.xls', 'output/SamplingRecords/地表水采样和交接记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.SurfaceWaterSamplingRecordService', '{"sort":"orderNum-"}', null, 900, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-06 10:57:53', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-20 11:29:24', 'sampleIds', 'Sampling', 'SurfaceWaterSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('9d16e7c2-341d-4fb5-a116-3be3564003f2', 1, 'ConsumableYZB', '易制爆材料出入库登记表.xlsx', 'LIMReportForms/易制爆材料出入库登记表.xlsx', 'output/LIMReportForms/易制爆材料出入库登记表.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.limReportForms.jx.ConsumableYZBService', '', '', 4, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-05 19:23:49', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-19 14:42:25', '', 'LIMReportForms', 'ConsumableYZB', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('9dc1061d-ab81-4abe-bec2-a6d02d09a39b', 1, 'NormalWaterTestHorizontal', '常规水检测报告（横版）_模板.doc', 'Report/常规水检测报告（横版）_模板.doc', 'output/Report/常规水检测报告（横版）.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.NormalWaterTestReportHorizontalService', '{"sort":"orderNum-"}', '', 1985, 4, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-24 17:27:22', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-08-24 17:27:22', 'reportId,sortId', 'Report', 'NormalWaterTestHorizontal', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('9e4e25f7-aae8-475d-a6ab-2b2325a6da70', 1, 'WorkSheetFDCJQ', '（363）粪大肠菌群（酶底物法）分析原始记录_模板.xlsx', 'WorkSheet/（363）粪大肠菌群（酶底物法）分析原始记录_模板.xls', 'output/WorkSheet/（363）粪大肠菌群（酶底物法）分析原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "FDCJQWorkSheetDataSourceImpl" }', 20, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-20 14:59:34', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:17:18', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFDCJQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('9edbedfd-e8fb-427b-9fbd-97c082282d07', 1, 'WorkSheetFLZXZDJF', '（325）氟离子选择电极法分析原始记录（气）_模板.xlsx', 'WorkSheet/（325）氟离子选择电极法分析原始记录（气）_模板.xls', 'output/WorkSheet/（325）氟离子选择电极法分析原始记录（气）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "FLZXZDJFWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}', 320, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-11 23:00:44', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:23:59', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFLZXZDJF', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('a67f7243-6754-4a40-9e82-216241d1c9c8', 1, 'WorkSheetICPMSGTFW', '（345）ICP-MS分析原始记录（固体废物浸出液）_模板.xlsx', 'WorkSheet/（345）ICP-MS分析原始记录（固体废物浸出液）_模板.xls', 'output/WorkSheet/（345）ICP-MS分析原始记录（固体废物浸出液）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "ICPMSGTFWWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 160, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-18 13:12:29', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:05:40', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetICPMSGTFW', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('a7bfa9ec-e38d-4c57-8285-43bd55659c5b', 1, 'OT', '加班审批表_模板.xlsx', 'LIMReportForms/加班审批表_模板.xls', 'output/LIMReportForms/加班审批表.xlsx', 'application/excel', 'com.sinoyd.lims.oa.service.OtReportService', 'id', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-28 15:12:25', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-15 20:10:34', '', 'LIMReportForms', ' ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('a80e9479-5173-4862-bac9-10bf6baf3276', 1, 'WorkSheetJWZT', '（329）总烃、甲烷、非甲烷总烃分析原始记录.xlsx', 'WorkSheet/（329）总烃、甲烷、非甲烷总烃分析原始记录_模板.xls', 'output/WorkSheet/（329）总烃、甲烷、非甲烷总烃分析原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "JWZTWorkSheetDataSourceImpl" }', 287, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-25 14:37:36', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:27:22', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetJWZT', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('a818dd51-085a-4897-ad22-7f9066d185a0', 1, 'WorkSheetLYZYGQ', '（333）（冷）原子荧光法分析原始记录(气)_模板.xlsx', 'WorkSheet/（333）（冷）原子荧光法分析原始记录(气)_模板.xls', 'output/WorkSheet/（333）（冷）原子荧光法分析原始记录(气).xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZYGFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 260, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-16 21:37:36', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:30:30', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLYZYGQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('aa774fca-48a4-4240-be99-779dc1d59067', 1, 'DustSamplingRecord', '降尘采样和交接记录.xlsx', 'SamplingRecords/降尘采样和交接记录.xls', 'output/SamplingRecords/降尘采样和交接记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.DustSamplingRecordService', '{"sort":"orderNum-"}', null, 865, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-24 16:28:24', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-20 11:30:22', 'sampleIds', 'Sampling', 'DustSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('abee292e-6b62-40ae-ac21-4f78462a6d8c', 1, 'WorkSheetFGGDS', '（301）分光光度法原始记录（水）_模板.xlsx', 'WorkSheet/（301）分光光度法原始记录（水）_模板.xls', 'output/WorkSheet/（301）分光光度法原始记录（水）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 510, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-05 15:21:36', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 11:17:34', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFGGDS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('ac44d3ee-03dc-48b5-9f82-071a29ec8d81', 1, 'SedimentSamplingRecord', '底质（底泥、沉积物）采样和交接记录.xlsx', 'SamplingRecords/底质（底泥、沉积物）采样和交接记录.xls', 'output/SamplingRecords/底质（底泥、沉积物）采样和交接记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.SedimentSamplingRecordService', '{"sort":"orderNum-"}', null, 480, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-17 17:50:21', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:49:01', 'sampleIds', 'Sampling', 'SedimentSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('ad110547-6935-440d-b1f9-be0066e03fd8', 1, 'FunctionalAreasNoiseSamplingRecord', '功能区环境噪声监测原始记录.xlsx', 'SamplingRecords/功能区环境噪声监测原始记录.xls', 'output/SamplingRecords/功能区环境噪声监测原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.FunctionalAreasNoiseSamplingRecordService', '{"sort":"orderNum-"}', null, 620, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-16 16:05:12', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:47:55', 'sampleIds', 'Sampling', 'FunctionalAreasNoiseSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('b208f3a9-ce4f-4291-91db-604a873b72b1', 1, 'TrafficNoise', '交通噪声监测报告_模板.doc', 'Report/交通噪声监测报告_模板.doc', 'output/Report/交通噪声监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.TrafficNoiseReportService', '{"sort":"orderNum-"}', null, 1780, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-24 09:48:50', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:28:23', 'reportId,sortId', 'Report', 'TrafficNoise', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('B233BB98-412E-46D5-9A3A-ADF7EB264F23', 1, 'DetailParamsDataProject', '项目参数数据.xlsx', 'Statistic/项目参数数据.xls', 'output/Statistic/项目参数数据.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.DetailDataExportService', '{"sort":"orderNum-"}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-09-27 17:25:32', 'com.sinoyd.lims.pro.criteria.DetailDataProjectCriteria', 'Statistic', 'export/DetailParamsDataProject', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('b27e8645-6319-42e0-ab5b-07bb2135689e', 1, 'TrafficNoiseSamplingRecord', '交通噪声监测原始记录.xlsx', 'SamplingRecords/交通噪声监测原始记录.xls', 'output/SamplingRecords/交通噪声监测原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.TrafficNoiseSamplingRecordService', '{"sort":"orderNum-"}', null, 590, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-22 16:18:35', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:48:15', 'sampleIds', 'Sampling', 'TrafficNoiseSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('b4862f99-ab84-49f6-aad3-804b45cbd7f1', 1, 'SampleLabelGC', '样品标签（国产化）.xls', 'Sample/样品标签（国产化）.xls', 'output/Sample/样品标签（国产化）.xls', 'application/excel', 'com.sinoyd.lims.sampling.service.samplingReport.SampleLabelServiceImpl', 'isBlank,isGroup,groupIds,isSample,sampleIds,receiveId', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-29 13:47:07', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-29 13:47:35', '', 'LIMReportForms', '-', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('b4f459ee-f234-4fd5-82a3-fade13de06aa', 1, 'WorkSheetLDZSF', '（361）流动注射法分析记录_模板.xlsx', 'WorkSheet/（361）流动注射法分析记录_模板.xls', 'output/WorkSheet/（361）流动注射法分析记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 30, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-20 14:25:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:16:30', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLDZSF', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('b5ac1c6d-c841-436d-b38f-00e4cb28323f', 1, 'WorkSheetFGGDZZS', '（307）分光光度法原始记录（浊度-色度补偿）_模板.xlsx', 'WorkSheet/（307）分光光度法原始记录（浊度-色度补偿）_模板.xls', 'output/WorkSheet/（307）分光光度法原始记录（浊度-色度补偿）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 460, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-08 15:16:54', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 11:22:18', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFGGDZZS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('b73a4a93-400c-4401-a046-0df2dc61cb70', 1, 'ConsumableCRK', '消耗性材料出入库登记表.xlsx', 'LIMReportForms/消耗性材料出入库登记表.xlsx', 'output/LIMReportForms/消耗性材料出入库登记表.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.limReportForms.jx.ConsumableCRKService', '', '', 8, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-05 19:23:07', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-19 14:16:47', '', 'LIMReportForms', 'ConsumableCRK', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('baba271d-afde-48da-a285-2e22e9fbd808', 1, 'WasteWater', '废水采样单.xlsx', 'Sample/废水采样单.xls', 'output/Sample/废水采样单.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.samplingReport.FSSamplingRecordService', 'sampleIds', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-02-05 08:40:00', null, 'SamplingRecords', null, false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('bee92f18-7ac8-41bf-8905-1a296b979f3d', 1, 'WorkSheetRLFGLCOD', '（314）容量法分析原始记录（高氯COD）_模板.xlsx', 'WorkSheet/（314）容量法分析原始记录（高氯COD）_模板.xls', 'output/WorkSheet/（314）容量法分析原始记录（高氯COD）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "RLFWorkSheetDataSourceImpl" }', 400, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-09 15:42:49', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:10:41', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetRLFGLCOD', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('beeffdf7-6ca1-4b26-980e-91060191ef4b', 1, 'FallenDust', '降尘监测报告_模板.doc', 'Report/降尘监测报告_模板.doc', 'output/Report/降尘监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.FallenDustReportService', '{"sort":"orderNum-"}', null, 1760, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-21 13:36:36', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:29:27', 'reportId,sortId', 'Report', 'FallenDust', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('bfe0fe80-6cb7-420d-9bb6-045e7bfd252f', 1, 'WorkSheetSPSZLJ', '（326）色谱、色质联机分析原始记录（曲线计算）_模板.xlsx', 'WorkSheet/（326）色谱、色质联机分析原始记录（曲线计算）_模板.xls', 'output/WorkSheet/（326）色谱、色质联机分析原始记录（曲线计算）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 0, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-05 11:00:15', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-19 15:41:05', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSPSZLJ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('C065A75C-68D0-4DB8-A358-4996F54884AB', 1, 'DetailData', '数据查询.xlsx', 'Statistic/数据查询.xls', 'output/Statistic/数据查询.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.statisticReport.DetailDataReportService', '{"sort":""}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-09 14:13:58', 'com.sinoyd.lims.pro.criteria.DetailDataCriteria', 'LIMReportForms', 'export/DetailDataProject', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('c074b990-316a-4ec0-802c-002edee5bf61', 1, 'WorkSheetICPAESS', '（349）ICP-AES分析原始记录（水）_模板.xlsx', 'WorkSheet/（349）ICP-AES分析原始记录（水）_模板.xls', 'output/WorkSheet/（349）ICP-AES分析原始记录（水）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 120, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-19 16:18:25', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:10:43', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetICPAESS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('c077c43e-6316-4753-9955-e74412159c2f', 1, 'SurfaceWater', '地表水监测报告_模板.doc', '/Report/地表水监测报告_模板.doc', 'output/Report/地表水监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.SurfaceWaterReportService', '{"sort":"orderNum-"}', null, 19850, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-07 13:48:58', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-11-21 17:48:41', 'reportId,sortId', 'Report', 'SurfaceWater', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('c14e2f38-e18b-4151-9654-621feeee9e04', 1, 'StandardConsumable', '标样清单.xlsx', 'LIMReportForms/标样清单.xlsx', 'output/LIMReportForms/标样清单.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.baseReport.StandardConsumableReportService', '{"sort":"consumableName"}', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2020-12-05 13:19:02', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-20 11:31:20', 'com.sinoyd.base.criteria.ConsumableCriteria', 'LIMReportForms', 'export/StandardConsumable', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('c3129316-2a7b-4870-ac1f-2604c4dff612', 1, 'WorkSheetSDBGBSF', '（322）色度(铂钴比色法)原始记录_模板.xlsx', 'WorkSheet/（322）色度(铂钴比色法)原始记录_模板.xls', 'output/WorkSheet/（322）色度(铂钴比色法)原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 340, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-10 14:18:31', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:22:33', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSDBGBSF', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('c3498b50-f2cf-4d7f-b001-0b862d2c3311', 1, 'WorkSheetQXFZXS', '（358）气相分子吸收光谱法原始记录_模板.xlsx', 'WorkSheet/（358）气相分子吸收光谱法原始记录_模板.xls', 'output/WorkSheet/（358）气相分子吸收光谱法原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 50, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-20 13:59:14', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:15:32', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetQXFZXS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('c7e4a25a-2683-4934-90b0-c52e7c9c60a1', 1, 'GasSampleAnalysisResults', '气体样品分析结果计算表_模板.xlsx', 'SamplingRecords/气体样品分析结果计算表_模板.xlsx', 'output/SamplingRecords/气体样品分析结果计算表.xlsx', 'application/excel', 'com.sinoyd.lims.wordreport.service.jx.wordReport.GasSampleAnalysisResultsService', '', '', 0, 3, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-08 09:20:14', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-09 15:18:53', '', 'Sampling', 'GasSampleAnalysisResults', false, '', '', '', '', null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('c891cb86-3891-428a-aaab-d4fa88c244e2', 1, ' WorkSheetLZSPFQWZZ', '（352）离子色谱法分析原始记录（气-无组织）_模板.xlsx', 'WorkSheet/（352）离子色谱法分析原始记录（气-无组织）_模板.xls', 'output/WorkSheet/离子色谱法分析原始记录（气-无组织）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTestGroup", "workSheetDataSourceType" : "LZSPFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}', 0, 2, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:05:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:05:05', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLZSPFQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('caee46e5-ef7b-45f0-bcfc-06d7a3ef764c', 1, 'WorkSheetSPSZLJQ', '（331）色谱、色质联机分析原始记录(气)_模板.xlsx', 'WorkSheet/（331）色谱、色质联机分析原始记录(气)_模板.xls', 'output/WorkSheet/（331）色谱、色质联机分析原始记录(气).xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 280, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 14:07:33', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:28:27', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSPSZLJQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('cb62e1f3-4ff5-4caa-b483-81f9c5d01d8b', 1, 'WorkSheetWRSHXYL', '（356）五日生化需氧量分析记录_模板.xlsx', 'WorkSheet/（356）五日生化需氧量分析记录_模板.xls', 'output/WorkSheet/（356）五日生化需氧量分析记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "WRSHXYLWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "1","mainDataExcludeTypeList" : [{"sampleCategory" : "3", "qcGrade" : "2", "qcType" : "16"}], "relDataTypeList" : [{"sampleCategory" : "3", "qcGrade" : "2", "qcType" : "16"}]}}', 75, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-20 11:12:59', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:13:27', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetWRSHXYL', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('cc6a7036-1320-4ea0-97a5-2820633ad0a8', 1, 'WorkSheetHWFGGDF', '（336）红外(非分散)分光光度法分析原始记录（水）_模板.xlsx', 'WorkSheet/（336）红外(非分散)分光光度法分析原始记录（水）_模板.xls', 'output/WorkSheet/（336）红外(非分散)分光光度法分析原始记录（水）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySample",  "workSheetDataSourceType" : "HWFGGDWorkSheetDataSourceImpl",  "qCGroupByTest" : "1", "qCAnaLmtCnt" : "1" }', 230, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-17 09:21:39', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-08 10:28:40', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetHWFGGDF', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('cdff15b0-a443-492f-842d-86a347f38e8f', 1, 'ceshiSamplingRecord', '有组织气态污染物采样和交接记录表(不等速采样)_测试.xlsx', 'SamplingRecords/有组织气态污染物采样和交接记录表(不等速采样)_测试.xls', 'output/SamplingRecords/有组织气态污染物采样和交接记录表(不等速采样)_测试.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.GaseousPollutantsSamplingRecordServiceTwo', '{"sort":"orderNum-"}', null, 690, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-14 09:38:25', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:46:57', 'sampleIds', 'Sampling', 'ceshiSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('ce909369-2370-4f7b-a8b7-f49e93f348ef', 1, 'WorkSheetFGGDSY', '（305）分光光度法原始记录（石油类）_模板.xlsx', 'WorkSheet/（305）分光光度法原始记录（石油类）_模板.xls', 'output/WorkSheet/（305）分光光度法原始记录（石油类）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 480, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-08 10:57:58', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 11:20:54', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFGGDSY', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('cfd5467a-d55b-4323-af33-086c6630f375', 1, 'WorkSheetYZXSFGGDFS', '（341）原子吸收分光光度法分析原始记录（水）_模板.xlsx', 'WorkSheet/（341）原子吸收分光光度法分析原始记录（水）_模板.xls', 'output/WorkSheet/（341）原子吸收分光光度法分析原始记录（水）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "YZXSFGGDSWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 200, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-18 10:24:42', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:02:13', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetYZXSFGGDFS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('cfe61a30-992f-4a31-915d-68051dc8dcfc', 1, 'WorkSheetFGGDQHW', '（304）分光光度法原始记录（浓度评价）_模板.xlsx', 'WorkSheet/（304）分光光度法原始记录（浓度评价）_模板.xls', 'output/WorkSheet/（304）分光光度法原始记录（浓度评价）_模板.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "commonWorkSheetDataSourceImpl" }', 490, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-07 13:14:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-28 16:50:36', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFGGDQHW', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('d1db1218-0695-4d52-a85c-7495e624afdf', 1, 'SampleDispose', '样品留样及处理登记表_模板.xlsx', 'LIMReportForms/样品留样及处理登记表_模板.xls', 'output/LIMReportForms/样品留样及处理登记表.xlsx', 'application/excel', 'com.sinoyd.lims.report.service.exports.SampleDisposeReportService', '', null, 0, 1, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-28 22:38:08', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 16:13:48', 'com.sinoyd.lims.pro.criteria.SampleDisposeCriteria', 'LIMReportForms', 'SampleDispose', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('d4524da2-3414-4a4c-99a4-816965107af2', 1, 'WorkSheetZLFGHS', '（316）重量法分析原始记录（干物质、含水率、水分）_模板.xlsx', 'WorkSheet/（316）重量法分析原始记录（干物质、含水率、水分）_模板.xls', 'output/WorkSheet/（316）重量法分析原始记录（干物质、含水率、水分）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "ZLFGHSWorkSheetDataSourceImpl" }', 380, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-09 22:03:55', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:12:11', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetZLFGHS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('d5842be5-3a2a-4af9-9be0-40befb046af7', 1, 'WorkSheetLYZYGQWZZ', '（333）（冷）原子荧光法分析原始记录(气-无组织)_模板.xlsx', 'WorkSheet/（333）（冷）原子荧光法分析原始记录(气-无组织)_模板.xls', 'output/WorkSheet/（冷）原子荧光法分析原始记录(气-无组织).xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZYGFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 0, 2, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 13:56:37', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 13:56:37', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLYZYGQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('d5b83be1-be36-41a2-a654-3e6114643d6a', 1, 'WorkSheetICPMSS', '（347）ICP-MS分析原始记录（水）_模板.xlsx', 'WorkSheet/（347）ICP-MS分析原始记录（水）_模板.xls', 'output/WorkSheet/（347）ICP-MS分析原始记录（水）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 130, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-19 15:53:29', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:06:40', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetICPMSS', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('d5fdc9a7-e980-419e-b0e1-cbbb9ceea6a9', 1, 'WorkSheetLZSPFQ', '（352）离子色谱法分析原始记录（气）_模板.xlsx', 'WorkSheet/（352）离子色谱法分析原始记录（气）_模板.xls', 'output/WorkSheet/（352）离子色谱法分析原始记录（气）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTestGroup", "workSheetDataSourceType" : "LZSPFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}', 100, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-19 17:29:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:11:55', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLZSPFQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('d724cc9a-0f65-4652-aac5-5b837125f875', 1, 'SocialEnvironmentNoiseSamplingRecord', '社会生活源边界环境噪声测量记录.xlsx', 'SamplingRecords/社会生活源边界环境噪声测量记录.xls', 'output/SamplingRecords/社会生活源边界环境噪声测量记录.xlsx', 'application/excel', 'com.sinoyd.lims.sampling.service.jx.samplingReport.SocialEnvironmentNoiseSamplingRecordService', '{"sort":"orderNum-"}', null, 600, 3, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-22 10:26:30', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-10 14:48:07', 'sampleIds', 'Sampling', 'SocialEnvironmentNoiseSamplingRecord', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('d7d94fa2-263b-4607-8daf-08885cd0e641', 1, 'WorkSheetLYZXSFQWZZ', '（343）冷原子吸收法分析原始记录（气-无组织）_模板.xlsx', 'WorkSheet/（343）冷原子吸收法分析原始记录（气-无组织）_模板.xls', 'output/WorkSheet/冷原子吸收法分析原始记录（气-无组织）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "LYZXSFQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 0, 2, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 13:58:47', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 13:58:47', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLYZXSFQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('d95a33e3-80cd-45ce-82d0-a4b26b275f1a', 1, 'WorkSheetLYZYGT', '（335）（冷）原子荧光法分析原始记录(土)_模板.xlsx', 'WorkSheet/（335）（冷）原子荧光法分析原始记录(土)_模板.xls', 'output/WorkSheet/（335）（冷）原子荧光法分析原始记录(土).xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySample", "workSheetDataSourceType" : "SPSZLJWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 240, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-16 22:22:35', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:31:47', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetLYZYGT', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('dc87dc82-c2e5-4163-bc20-fa4f1f033186', 1, 'WorkSheetSPSZLJQCLWZZ', '（332）色谱、色质联机分析原始记录（气-串联-无组织)_模板.xlsx', 'WorkSheet/（332）色谱、色质联机分析原始记录（气-串联-无组织)_模板.xls', 'output/WorkSheet/色谱、色质联机分析原始记录（气-串联-无组织).xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTestGroup", "workSheetDataSourceType" : "SPSZLJQCLWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}', 0, 2, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:02:12', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:02:12', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSPSZLJQCL', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('e54f395b-eeca-460b-b730-a912c86b7950', 1, 'NormalWater', '常规水监测报告_模板.doc', 'Report/常规水监测报告_模板.doc', 'output/Report/常规水监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.NormalWaterReportService', '{"sort":"orderNum-"}', null, 2000, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-07 15:53:37', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:25:52', 'reportId,sortId', 'Report', 'NormalWater', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('e5c1ef02-9170-408c-a17a-5aea859735ea', 1, 'WasteGas', '废气监测报告_模板.doc', 'Report/废气监测报告_模板.doc', 'output/Report/废气监测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.WasteGasReportService', '{"sort":"orderNum-"}', null, 1900, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-21 13:36:07', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:27:26', 'reportId,sortId', 'Report', 'WasteGas', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('e656a41e-734a-4171-b45f-5678e95b42c6', 1, 'Radiation', '辐射报告_模板.doc', 'Report/辐射报告_模板.doc', 'output/Report/辐射报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.RadiationReportService', '{"sort":"orderNum-"}', null, 1930, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-24 09:51:38', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:29:42', 'reportId,sortId', 'Report', 'Radiation', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('e71ae6aa-1182-4088-a9ec-66225d0a7948', 1, 'ConsumablesPurchase', '消耗性材料采购申请表.xls', 'LIMReportForms/消耗性材料采购申请表.xls', 'output/LIMReportForms/消耗性材料采购申请表.xls', 'application/excel', 'com.sinoyd.lims.oa.service.ConsumablesPurchaseRequisitionReportService', 'id', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-10 16:56:09', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-07-10 16:56:09', '', 'LIMReportForms', 'ConsumablesPurchase', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('e71eb218-886f-4d46-83fe-fa2560affadb', 1, 'WorkSheetSPSZLJQWZZ', '（331）色谱、色质联机分析原始记录(气-无组织)_模板.xlsx', 'WorkSheet/（331）色谱、色质联机分析原始记录(气-无组织)_模板.xls', 'output/WorkSheet/色谱、色质联机分析原始记录(气-无组织).xlsx', 'application/excel', ' com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "SPSZLJQWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 0, 2, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:00:24', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:00:24', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSPSZLJQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('e89254bc-3044-4ba8-bb41-02838f689ff9', 1, 'OrderForm', '订单明细_模板.xlsx', 'LIMReportForms/订单明细_模板.xlsx', 'output/LIMReportForms/订单明细.xlsx', 'application', 'com.sinoyd.lims.report.service.exports.OrderFormExportService', '{"sort":"orderDate-"}', '', 0, 1, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-22 13:30:55', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-02-22 13:31:20', 'com.sinoyd.lims.pro.criteria.OrderFormCriteria', 'LIMReportForms', 'export/OrderForm', false, '', '', '', '', null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('e964d612-50c3-4854-bec7-46c7f729ca54', 1, 'WorkSheetYY', '（339）油烟分析原始记录_模板.xlsx', 'WorkSheet/（339）油烟分析原始记录_模板.xls', 'output/WorkSheet/（339）油烟分析原始记录.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleGroup",  "workSheetDataSourceType" : "YYWorkSheetDataSourceImpl", "groupInfo" : {"groupType" : "2", "mainDataExcludeTypeList" : [], "relDataTypeList" : []}}', 210, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-18 10:01:05', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:01:46', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetYY', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('eab54930-afa0-4dc5-9b40-41fde23dfba9', 1, 'WorkSheetYZXSFGGDFT', '（342）原子吸收分光光度法分析原始记录（土）_模板.xlsx', 'WorkSheet/（342）原子吸收分光光度法分析原始记录（土）_模板.xls', 'output/WorkSheet/（342）原子吸收分光光度法分析原始记录（土）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTest", "workSheetDataSourceType" : "YZXSFGGDSWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4"}', 190, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-18 10:39:27', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 14:02:40', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetYZXSFGGDFT', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('edac2fd2-200e-4b4c-9158-20aab569c91c', 1, 'WorkSheetSPSZLJDZF', '（330）色谱、色质联机分析原始记录（多组分）_模板.xlsx', 'WorkSheet/（330）色谱、色质联机分析原始记录（多组分）_模板.xls', 'output/WorkSheet/（330）色谱、色质联机分析原始记录（多组分）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySingleSampleTestSPSZDZF", "workSheetDataSourceType" : "SPSZLJDZFWorkSheetDataSourceImpl"}', 285, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-19 15:40:36', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-07 13:27:53', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSPSZLJDZF', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('f780d70b-05aa-4f4d-a302-d56fcaeb8bc2', 1, 'NormalWaterTest', '常规水检测报告_模板.doc', 'Report/常规水检测报告_模板.doc', 'output/Report/常规水检测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.NormalWaterTestReportService', '{"sort":"orderNum-"}', null, 1990, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-07 17:12:39', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:26:09', 'reportId,sortId', 'Report', 'NormalWaterTest', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('fb681ec3-5f64-49bf-a9c3-6bfb242c1b2c', 1, 'WorkSheetFGGDQWZZ', '（302）分光光度法原始记录（气-无组织）_模板.xlsx', 'WorkSheet/（302）分光光度法原始记录（气-无组织）_模板.xls', 'output/WorkSheet/分光光度法原始记录（气-无组织）.xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleOnly",  "workSheetDataSourceType" : "FGGDQWorkSheetDataSourceImpl" }', 0, 2, '', false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:06:08', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-12-30 14:06:08', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetFGGDQ', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('fc2386c6-d75f-434c-a787-1ce184089d8a', 1, 'WorkSheetSPSZLJQCL', '（332）色谱、色质联机分析原始记录（气-串联)_模板.xlsx', 'WorkSheet/（332）色谱、色质联机分析原始记录（气-串联)_模板.xls', 'output/WorkSheet/（332）色谱、色质联机分析原始记录（气-串联).xlsx', 'application/excel', 'com.sinoyd.lims.worksheet.jx.service.JxCommonWorkSheetService', '', '{"originalRecordType" : "lineExtendBySampleTestGroup", "workSheetDataSourceType" : "SPSZLJQCLWorkSheetDataSourceImpl", "qCGroupByTest" : "1", "qCAnaLmtCnt" : "4", "groupInfo" : {"groupType" : "1", "mainDataExcludeTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}], "relDataTypeList" : [{"sampleCategory" : "2", "qcGrade" : "-1", "qcType" : "-1"}]}}', 270, 2, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-01-15 15:44:23', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-05-19 15:43:12', 'workSheetFolderId,type', 'WorkSheet', 'WorkSheetSPSZLJQCL', false, '', '', null, null, null);
INSERT INTO TB_LIM_ReportConfig (id, type, reportCode, templateName, template, outputName, returnType, method, params, pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId, modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName, beanName, versionNum, controlNum, reportName) VALUES ('fffaa09b-3e4d-484c-9341-739b13c27c1d', 1, 'FallenDustTest', '降尘检测报告_模板.doc', 'Report/降尘检测报告_模板.doc', 'output/Report/降尘检测报告.doc', 'application/word', 'com.sinoyd.lims.wordreport.service.jx.wordReport.FallenDustReportService', '{"sort":"orderNum-"}', null, 1750, 4, null, false, '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2021-12-21 13:36:59', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2022-06-17 13:29:33', 'reportId,sortId', 'Report', 'FallenDustTest', false, '', '', null, null, null);
