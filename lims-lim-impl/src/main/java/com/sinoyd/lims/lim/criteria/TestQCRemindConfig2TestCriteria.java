package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * TestQCRemindConfig2Test查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestQCRemindConfig2TestCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    private String configId;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;

    /**
     * 分析方法，方法标准
     */
    private String analyzeMethod;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        if (StringUtils.isNotNullAndEmpty(this.configId)
                && !UUIDHelper.GUID_EMPTY.equals(this.configId)) {
            condition.append(" and configId = :configId ");
            values.put("configId", this.configId);
        }
        if (StringUtil.isNotEmpty(this.analyzeItemName)) {
            condition.append(" and exists (select 1 from DtoTest t where t.id = p.testId and " +
                    "(t.redAnalyzeItemName like :analyzeItemName or t.fullPinYin like :analyzeItemName or t.pinYin like :analyzeItemName))");
            values.put("analyzeItemName", "%" + this.analyzeItemName + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeMethod)) {
            condition.append(" and exists (select 1 from DtoTest t where t.id = p.testId and " +
                    "(t.redAnalyzeMethodName like :analyzeMethod or t.redCountryStandard like :analyzeMethod))");
            values.put("analyzeMethod", "%" + this.analyzeMethod + "%");
        }
        return condition.toString();
    }
}