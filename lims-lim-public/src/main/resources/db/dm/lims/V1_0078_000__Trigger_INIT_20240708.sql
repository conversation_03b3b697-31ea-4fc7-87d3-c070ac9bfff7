CREATE  TRIGGER "AFTER_INSERT_RECORDC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
    AFTER  INSERT
    ON "TB_LIM_RECORDCONFIG2<PERSON>RAMSCONFIG"
    referencing OLD ROW AS "OLD" NEW ROW AS "NEW"

 for each row

BEGIN

INSERT INTO tb_lim_testoperatelog (id, tableName, tableId, operatorId, operatorDate, operateType, operateField,
                                   oldValue, newValue, orgId, domainId)
VALUES (NEWID(), 'tb_lim_recordconfig2paramsconfig', new.id, '00000000-0000-0000-0000-000000000000', now(), 1, null, null, null,
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000');
end;
