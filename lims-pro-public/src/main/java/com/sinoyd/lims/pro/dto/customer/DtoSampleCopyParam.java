package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 样品复制传参
 * <AUTHOR>
 * @version V1.0.0 2020/2/5
 * @since V100R001
 */
@Data
public class DtoSampleCopyParam {
    /**
     * 样品id
     */
    private List<String> ids = new ArrayList<>();

    /**
     * 送样单id
     */
    private String receiveId;

    /**
     * 复制次数
     */
    private Integer times;

    /**
     * 采样日期
     */
    private Date samplingTimeBegin;
}
