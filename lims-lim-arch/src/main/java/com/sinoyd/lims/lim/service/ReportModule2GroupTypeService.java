package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoModuleGroupTypeTemp;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule2GroupType;

import java.util.List;


/**
 * 报告组件配置
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
public interface ReportModule2GroupTypeService extends IBaseJpaService<DtoReportModule2GroupType, String> {

    /**
     * 新增报告和组件关联关系的分页配置扩展信息
     *
     * @param moduleGroupTypeTemp 请求参数entity
     * @return 查询结果
     */
    List<DtoReportModule2GroupType> saveGroupType(DtoModuleGroupTypeTemp moduleGroupTypeTemp);

    /**
     * 修改报告和组件关联关系的分页配置扩展信息
     *
     * @param moduleGroupTypeTemp 请求参数entity
     * @return 查询结果
     */
    List<DtoReportModule2GroupType> updateGroupType(DtoModuleGroupTypeTemp moduleGroupTypeTemp);
}