package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.SerialIdentifierConfigService;
import com.sinoyd.lims.pro.dto.DtoAnalyseAchievementDetails;
import com.sinoyd.lims.pro.dto.DtoReportAchievementDetails;
import com.sinoyd.lims.pro.repository.ReportAchievementDetailsRepository;
import com.sinoyd.lims.pro.service.ReportAchievementDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ReportAchievementDetails操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Service
public class ReportAchievementDetailsServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportAchievementDetails, String, ReportAchievementDetailsRepository> implements ReportAchievementDetailsService {

    private SerialIdentifierConfigService serialIdentifierConfigService;

    private PersonService personService;

    @Override
    public void findByPage(PageBean<DtoReportAchievementDetails> page, BaseCriteria criteria) {
        page.setEntityName("DtoReportAchievementDetails a");
        page.setSelect("select a");
        page.setSort("a.reportTime-, a.reportCode-");
        super.findByPage(page, criteria);
        fillingTransientFields(page.getData());
    }

    @Override
    public void export(HttpServletResponse response, BaseCriteria criteria) {
        PageBean<DtoReportAchievementDetails> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        pb.setSort("a.reportTime-, a.reportCode-");
        findByPage(pb, criteria);
        PoiExcelUtils.exportExcel(pb.getData(), null, "报告绩效明细表格", DtoReportAchievementDetails.class, "报告绩效明细表格" + "_" + DateUtil.nowTime("yyyyMMddHHmmss"), response);
    }

    private void fillingTransientFields(List<DtoReportAchievementDetails> dataList) {
        List<DtoSerialIdentifierConfig> list = serialIdentifierConfigService.findListByConfigType(EnumLIM.EnumIdentifierConfig.报告编号.getValue());
        Map<String, String> reportTypeMap = list.stream().collect(Collectors.toMap(DtoSerialIdentifierConfig::getId, DtoSerialIdentifierConfig::getConfigName));
        List<String> personIds = dataList.stream().map(DtoReportAchievementDetails::getReportPersonId).distinct().collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personService.findAll(personIds) : new ArrayList<>();
        dataList.forEach(d -> {
            d.setReportType(reportTypeMap.get(d.getReportTypeId()));
            Optional<DtoPerson> person = personList.stream().filter(p -> p.getId().equals(d.getReportPersonId())).findFirst();
            person.ifPresent(p -> d.setReportPersonName(p.getCName()));
        });
    }

    @Autowired
    @Lazy
    public void setSerialIdentifierConfigService(SerialIdentifierConfigService serialIdentifierConfigService) {
        this.serialIdentifierConfigService = serialIdentifierConfigService;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }


}
