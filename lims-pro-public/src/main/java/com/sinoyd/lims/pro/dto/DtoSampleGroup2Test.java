package com.sinoyd.lims.pro.dto;


import com.sinoyd.lims.pro.entity.SampleGroup2Test;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoSampleGroup2Test
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/28
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SampleGroup2Test")
@Data
@DynamicInsert
public class DtoSampleGroup2Test extends SampleGroup2Test {
}
