-- 年度计划
CREATE TABLE TB_QA_YearlyPlan
(
    id             varchar(50)             primary key COMMENT '标识',
    planName       varchar(50)             NOT NULL DEFAULT '' COMMENT '计划名称',
    planType       int(10)                 NOT NULL  COMMENT '计划类型，关联枚举EnumYearlyPlanType',
    planYear       int(10)                 NOT NULL  COMMENT '计划年份',
    compilePeople  varchar(500)            COMMENT '编制人员',
    remark         varchar(1000)           COMMENT '备注',
    status         int(10)                 NOT NULL DEFAULT 0 COMMENT '状态,0未提交/1已提交',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '年度计划';
-- 质量监督计划
CREATE TABLE TB_QA_YearlyQualitySupervisionPlan
(
    id             varchar(50)             primary key COMMENT '标识',
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '年度计划标识',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '计划标识',
    content        varchar(500)            COMMENT '监督内容',
    target         varchar(50)             COMMENT '监督对象',
    timeRequire    varchar(50)             COMMENT '时间要求/完成时间',
    deptId         varchar(50)             COMMENT '责任部门标识',
    personId       varchar(50)             COMMENT '监督员标识',
    completion     varchar(500)            COMMENT '完成情况',
    status         int(10)                 NOT NULL DEFAULT 0 COMMENT '执行状态,0执行中/1完成',
    planType       int(10)                 NOT NULL DEFAULT 0 COMMENT '计划状态,0计划内/1计划外',
    executeType    int(10)                 NOT NULL DEFAULT 0 COMMENT '计划类型,0计划/1执行',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '质量监督计划';
-- 质量控制计划
CREATE TABLE TB_QA_YearlyQualityControlPlan
(
    id             varchar(50)             primary key COMMENT '标识',
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '年度计划标识',
    detailId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '计划标识',
    content        varchar(500)            COMMENT '质控内容',
    analyzeItem    varchar(50)             COMMENT '分析项目',
    target         varchar(50)             COMMENT '质控对象',
    timeRequire    varchar(50)             COMMENT '时间要求/完成时间',
    method         varchar(50)             COMMENT '质控方法',
    point          varchar(50)             COMMENT '评价指标',
    personId       varchar(50)             COMMENT '负责人标识/责任人标识',
    completion     varchar(500)            COMMENT '完成情况',
    status         int(10)                 NOT NULL DEFAULT 0 COMMENT '执行状态,0执行中/1完成',
    planType       int(10)                 NOT NULL DEFAULT 0 COMMENT '计划状态,0计划内/1计划外',
    executeType    int(10)                 NOT NULL DEFAULT 0 COMMENT '计划类型,0计划/1执行',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '质量控制计划';
-- 内审计划
CREATE TABLE TB_QA_YearlyInnerAuditPlan
(
    id             varchar(50)             primary key COMMENT '标识',
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '年度计划标识',
    chargePersonId varchar(50)             COMMENT '负责人标识',
    auditTime      datetime(0)             COMMENT '评审时间',
    basis          varchar(500)            COMMENT '评审依据',
    purpose        varchar(500)            COMMENT '评审目的',
    form           varchar(500)            COMMENT '评审形式',
    content        varchar(500)            COMMENT '评审内容',
    people         varchar(500)            COMMENT '参与评审人员',
    progress       varchar(500)            COMMENT '会议议程',
    completion     varchar(500)            COMMENT '完成情况',
    status         int(10)                 NOT NULL DEFAULT 0 COMMENT '执行状态,0执行中/1完成',
    executeType    int(10)                 NOT NULL DEFAULT 0 COMMENT '计划类型,0计划/1执行',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '内审计划';
-- 管理评审计划
CREATE TABLE TB_QA_YearlyManagementReviewPlan
(
    id             varchar(50)             primary key COMMENT '标识',
    planId         varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '年度计划标识',
    chargePersonId varchar(50)             COMMENT '负责人标识/责任人标识',
    auditTime      datetime(0)             COMMENT '评审时间/完成时间',
    basis          varchar(500)            COMMENT '评审依据',
    purpose        varchar(500)            COMMENT '评审目的',
    form           varchar(500)            COMMENT '评审形式',
    content        varchar(500)            COMMENT '评审内容',
    people         varchar(500)            COMMENT '参与评审人员',
    progress       varchar(500)            COMMENT '会议议程',
    completion     varchar(500)            COMMENT '完成情况',
    status         int(10)                 NOT NULL DEFAULT 0 COMMENT '执行状态,0执行中/1完成',
    executeType    int(10)                 NOT NULL DEFAULT 0 COMMENT '计划类型,0计划/1执行',
    isDeleted      bit(1)                  NOT NULL DEFAULT 0 COMMENT '是否删除',
    orgId          varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator        varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    domainId       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier       varchar(50)             NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate     datetime(0)             NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '修改时间'
) COMMENT '管理评审计划';
