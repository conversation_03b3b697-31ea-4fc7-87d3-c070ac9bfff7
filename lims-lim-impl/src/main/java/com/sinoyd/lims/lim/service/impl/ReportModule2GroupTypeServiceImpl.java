package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.customer.DtoModuleGroupTypeTemp;
import com.sinoyd.lims.lim.dto.rcc.DtoReportConfig2Module;
import com.sinoyd.lims.lim.dto.rcc.DtoReportModule2GroupType;
import com.sinoyd.lims.lim.repository.rcc.ReportConfig2ModuleRepository;
import com.sinoyd.lims.lim.repository.rcc.ReportModule2GroupTypeRepository;
import com.sinoyd.lims.lim.service.ReportModule2GroupTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 报告组件关联关系对应的分页方式操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Service
public class ReportModule2GroupTypeServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportModule2GroupType, String, ReportModule2GroupTypeRepository> implements ReportModule2GroupTypeService {

    private ReportConfig2ModuleRepository reportConfig2ModuleRepository;


    @Override
    public List<DtoReportModule2GroupType> saveGroupType(DtoModuleGroupTypeTemp moduleGroupTypeTemp) {
        DtoReportConfig2Module existConfig2Module = reportConfig2ModuleRepository.findOne(moduleGroupTypeTemp.getReportConfigModuleId());
        if (StringUtil.isNull(existConfig2Module)) {
            throw new BaseException("报告和组件的关联关系不存在！");
        }
        List<DtoReportModule2GroupType> module2GroupTypeList = moduleGroupTypeTemp.getGroupTypeList();
        module2GroupTypeList.forEach(p -> p.setReportConfigModuleId(moduleGroupTypeTemp.getReportConfigModuleId()));
        if (StringUtil.isNotEmpty(module2GroupTypeList)) {
            repository.save(module2GroupTypeList);
        }
        return module2GroupTypeList;
    }

    @Override
    public List<DtoReportModule2GroupType> updateGroupType(DtoModuleGroupTypeTemp moduleGroupTypeTemp) {
        DtoReportConfig2Module existConfig2Module = reportConfig2ModuleRepository.findOne(moduleGroupTypeTemp.getReportConfigModuleId());
        if (StringUtil.isNull(existConfig2Module)) {
            throw new BaseException("报告和组件的关联关系不存在！");
        }
        //先删除原有的
        List<DtoReportModule2GroupType> oldGroupTypeList = repository.findByReportConfigModuleId(moduleGroupTypeTemp.getReportConfigModuleId());
        if (StringUtil.isNotEmpty(oldGroupTypeList)) {
            repository.delete(oldGroupTypeList);
        }
        //加入现有的
        List<DtoReportModule2GroupType> newGroupTypeList = moduleGroupTypeTemp.getGroupTypeList();
        newGroupTypeList.forEach(p -> p.setReportConfigModuleId(moduleGroupTypeTemp.getReportConfigModuleId()));
        return repository.save(newGroupTypeList);
    }

    @Autowired
    public void setReportConfig2ModuleRepository(ReportConfig2ModuleRepository reportConfig2ModuleRepository) {
        this.reportConfig2ModuleRepository = reportConfig2ModuleRepository;
    }
}