package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * ReportAchievementDetailsCriteria查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2023/03/14
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportAchievementDetailsCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 绩效id
     */
    private String achievementId;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 报告编号
     */
    private String reportCode;


    private String projectKey;

    @Override
    public String getCondition() {
        values.clear();
        Calendar calendar = new GregorianCalendar();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.personId)) {
            condition.append(" and a.reportPersonId = :personId");
            values.put("personId", this.personId);
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.reportTime >= :startTime");
            values.put("startTime", date);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.reportTime < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(this.reportCode)) {
            condition.append(" and a.reportCode like :reportCode");
            values.put("reportCode", "%" + this.reportCode + "%");
        }
        if (StringUtil.isNotEmpty(this.achievementId)) {
            condition.append(" and a.achievementId = :achievementId");
            values.put("achievementId", this.achievementId);
        }
        if (StringUtil.isNotEmpty(this.projectKey)) {
            condition.append(" and (a.projectCode = :key or a.projectName like :key or a.entName like :key)");
            values.put("key", "%" + this.projectKey + "%");
        }
        return condition.toString();
    }
}
