package com.sinoyd.lims.lim.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.utils.poi.ExcelStyle;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.criteria.MpnConfigDetailsCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoMpnConfig;
import com.sinoyd.lims.lim.dto.lims.DtoMpnConfigDetails;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.repository.lims.MpnConfigDetailsRepository;
import com.sinoyd.lims.lim.repository.lims.MpnConfigRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsRepository;
import com.sinoyd.lims.lim.service.MpnConfigDetailsService;
import com.sinoyd.lims.lim.verify.MpnConfigVerifyHandle;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Mnp配置接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@Service
public class MpnConfigDetailsServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoMpnConfigDetails, String, MpnConfigDetailsRepository> implements MpnConfigDetailsService {

    private MpnConfigRepository mpnConfigRepository;
    private static final String CONFIDENCE_UP = "95%置信上限";
    private static final String CONFIDENCE_LOW = "95%置信下限";
    private MpnConfigVerifyHandle mpnConfigVerifyHandle;
    private ParamsRepository paramsRepository;


    @Override
    public void findByPage(PageBean<DtoMpnConfigDetails> page, BaseCriteria criteria) {
        page.setEntityName("DtoMpnConfigDetails m");
        page.setSelect("select m");
        super.findByPage(page, criteria);
    }

    @Override
    public Map<String, Object> findDetailsByPage(PageBean<DtoMpnConfigDetails> pageBean, BaseCriteria baseCriteria) {
        this.findByPage(pageBean, baseCriteria);
        List<DtoMpnConfigDetails> mpnConfigDetails = pageBean.getData();
        MpnConfigDetailsCriteria configDetailsCriteria = (MpnConfigDetailsCriteria) baseCriteria;
        return getDetails(configDetailsCriteria.getMpnConfigId(), mpnConfigDetails);
    }

    @Override
    public void exportTemplate(String mpnConfigId, HttpServletResponse response) {
        DtoMpnConfig mpnConfig = getMpnConfig(mpnConfigId);
        List<String> list = buildColumns(mpnConfig);
        List<ExcelExportEntity> entityList = new ArrayList<>();
        for (String value : list) {
            entityList.add(new ExcelExportEntity(value, value));
        }
        ExportParams params = new ExportParams();
        params.setStyle(ExcelStyle.class);
        Workbook workbook = ExcelExportUtil.exportExcel(params, entityList, new ArrayList<>());
        PoiExcelUtils.downLoadExcel("MNP导入模版", response, workbook);
    }

    @Override
    public List<DtoMpnConfigDetails> findByTestIdIn(List<String> testIds) {
        List<DtoMpnConfig> mpnConfigList = mpnConfigRepository.findByTestIdIn(testIds);
        List<DtoParams> paramsList = paramsRepository.findAll();
        Map<String, String> paramsMap = paramsList.stream().collect(Collectors.toMap(DtoParams::getId, DtoParams::getParamName));
        List<String> mpnConfigIds = mpnConfigList.stream().map(DtoMpnConfig::getId).collect(Collectors.toList());
        List<DtoMpnConfigDetails> mpnConfigDetailsList = StringUtil.isNotEmpty(mpnConfigIds) ? repository.findByMpnConfigIdIn(mpnConfigIds) : new ArrayList<>();
        for (DtoMpnConfigDetails details : mpnConfigDetailsList) {
            mpnConfigList.stream().filter(p -> p.getId().equals(details.getMpnConfigId())).findFirst().ifPresent(mnp -> {
                details.setParam1Name(paramsMap.getOrDefault(mnp.getParam1Id(), ""));
                details.setParam2Name(paramsMap.getOrDefault(mnp.getParam2Id(), ""));
                details.setParam3Name(paramsMap.getOrDefault(mnp.getParam3Id(), ""));
                details.setResultParamName(paramsMap.getOrDefault(mnp.getResultParamId(), ""));
            });
        }
        return mpnConfigDetailsList;
    }

    @Override
    @Transactional
    public void importData(MultipartFile file, String mpnConfigId, HttpServletResponse response) {
        ImportParams params = new ImportParams();
        //设置表头区域
        params.setTitleRows(0);
        //设置表头开始行
        params.setHeadRows(1);
        //设置开始工作簿
        params.setStartSheetIndex(0);
        //设置是否校验
        params.setNeedVerify(true);
        mpnConfigVerifyHandle.getLineNoTl().set(null);
        params.setVerifyHandler(mpnConfigVerifyHandle);
        ExcelImportResult<Map<String, Object>> result;
        try {
            result = ExcelImportUtil.importExcelMore(
                    file.getInputStream(),
                    Map.class, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException("获取导入数据失败" + e.getMessage());
        }
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(0, "MNP导入数据错误");
            PoiExcelUtils.downLoadExcel("MNP导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        List<Map<String, Object>> mapList = result.getList();
        DtoMpnConfig mpnConfig = getMpnConfig(mpnConfigId);
        List<DtoMpnConfigDetails> mpnConfigDetails = new ArrayList<>();
        int orderNum = 0;
        for (Map<String, Object> map : mapList) {
            DtoMpnConfigDetails details = new DtoMpnConfigDetails();
            details.setMpnConfigId(mpnConfigId);
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                String value = null != entry.getValue() ? entry.getValue().toString() : "";
                if (mpnConfig.getParam1().equals(key)) {
                    details.setParam1Value(value);
                } else if (StringUtil.isNotNull(mpnConfig.getParam2()) && mpnConfig.getParam2().equals(key)) {
                    details.setParam2Value(value);
                } else if (StringUtil.isNotNull(mpnConfig.getParam3()) && mpnConfig.getParam3().equals(key)) {
                    details.setParam3Value(value);
                } else if (mpnConfig.getResultParam().equals(key)) {
                    details.setResultParamValue(value);
                } else if (CONFIDENCE_LOW.equals(key)) {
                    details.setConfidenceLow(value);
                } else if (CONFIDENCE_UP.equals(key)) {
                    details.setConfidenceUp(value);
                }
                details.setOrderNum(++orderNum);
                mpnConfigDetails.add(details);
            }
        }
        if (StringUtil.isNotEmpty(mpnConfigDetails)) {
            repository.save(mpnConfigDetails);
        }
    }

    @Override
    public void export(BaseCriteria mpnConfigCriteria, HttpServletResponse response) {
        PageBean<DtoMpnConfigDetails> page = new PageBean<>();
        page.setPageNo(1);
        page.setRowsPerPage(Integer.MAX_VALUE);
        page.setSort("orderNum");
        Map<String, Object> detailsByPage = this.findDetailsByPage(page, mpnConfigCriteria);
        List<String> columns = (List<String>) detailsByPage.get("columns");
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) detailsByPage.get("data");
        List<ExcelExportEntity> entityList = new ArrayList<>();
        // 创建动态表头
        for (String column : columns) {
            entityList.add(new ExcelExportEntity(column, column));
        }
        ExportParams params = new ExportParams();
        params.setStyle(ExcelStyle.class);
        params.setSheetName("MNP配置数据");
        Workbook workbook = ExcelExportUtil.exportExcel(params, entityList, dataList);
        PoiExcelUtils.downLoadExcel("MNP配置数据", response, workbook);
    }

    // 获取动态表数据
    private Map<String, Object> getDetails(String mpnConfigId, List<DtoMpnConfigDetails> mpnConfigDetails) {
        // 主配置
        DtoMpnConfig mngConfig = getMpnConfig(mpnConfigId);
        // 构建动态列头
        List<String> columns = buildColumns(mngConfig);

        List<Map<String, Object>> dataList = new ArrayList<>();
        for (DtoMpnConfigDetails detail : mpnConfigDetails) {
            Map<String, Object> dataMap = new LinkedHashMap<>();
            dataMap.put("id", detail.getId());
            dataMap.put(mngConfig.getParam1(), detail.getParam1Value());
            if (StringUtil.isNotEmpty(mngConfig.getParam2())) {
                dataMap.put(mngConfig.getParam2(), detail.getParam2Value());
            }
            if (StringUtil.isNotEmpty(mngConfig.getParam3())) {
                dataMap.put(mngConfig.getParam3(), detail.getParam3Value());
            }
            dataMap.put(mngConfig.getResultParam(), detail.getResultParamValue());
            dataMap.put(CONFIDENCE_LOW, detail.getConfidenceLow());
            dataMap.put(CONFIDENCE_UP, detail.getConfidenceUp());
            dataList.add(dataMap);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("columns", columns);
        result.put("data", dataList);
        return result;
    }

    /**
     * 查询配置参数
     *
     * @param mpnConfigId 配置id
     * @return 配置参数Map
     */
    private DtoMpnConfig getMpnConfig(String mpnConfigId) {
        DtoMpnConfig mpnConfig = mpnConfigRepository.findOne(mpnConfigId);
        List<DtoParams> paramsList = paramsRepository.findAll();
        Map<String, String> paramsMap = paramsList.stream().collect(Collectors.toMap(DtoParams::getId, DtoParams::getParamName));
        mpnConfig.setParam1(paramsMap.getOrDefault(mpnConfig.getParam1Id(), ""));
        if (StringUtil.isNotEmpty(mpnConfig.getParam2Id())) {
            mpnConfig.setParam2(paramsMap.getOrDefault(mpnConfig.getParam2Id(), ""));
        }
        if (StringUtil.isNotEmpty(mpnConfig.getParam3Id())) {
            mpnConfig.setParam3(paramsMap.getOrDefault(mpnConfig.getParam3Id(), ""));
        }
        mpnConfig.setResultParam(paramsMap.getOrDefault(mpnConfig.getResultParamId(), ""));
        return mpnConfig;
    }

    /**
     * 构建动态列头
     *
     * @param mpnConfig 配置参数
     * @return 动态表头
     */
    private List<String> buildColumns(DtoMpnConfig mpnConfig) {
        List<String> columns = new ArrayList<>();
        if (StringUtil.isNotEmpty(mpnConfig.getParam1())) {
            columns.add(mpnConfig.getParam1());
        }
        if (StringUtil.isNotEmpty(mpnConfig.getParam2())) {
            columns.add(mpnConfig.getParam2());
        }
        if (StringUtil.isNotEmpty(mpnConfig.getParam3())) {
            columns.add(mpnConfig.getParam3());
        }
        columns.add(mpnConfig.getResultParam());
        columns.add(CONFIDENCE_LOW);
        columns.add(CONFIDENCE_UP);
        return columns;
    }

    public void setParamsName(List<DtoMpnConfig> mpnConfigList, Map<String, String> paramsMap) {

    }


    @Override
    @Transactional
    public void deleteByMpnConfigIdIn(List<String> mpnConfigIds) {
        List<DtoMpnConfigDetails> mpnConfigDetails = repository.findByMpnConfigIdIn(mpnConfigIds);
        if (StringUtil.isNotEmpty(mpnConfigDetails)) {
            repository.delete(mpnConfigDetails);
        }
    }


    @Autowired
    public void setMpnConfigRepository(MpnConfigRepository mpnConfigRepository) {
        this.mpnConfigRepository = mpnConfigRepository;
    }

    @Autowired
    public void setMpnConfigVerifyHandle(MpnConfigVerifyHandle mpnConfigVerifyHandle) {
        this.mpnConfigVerifyHandle = mpnConfigVerifyHandle;
    }

    @Autowired
    public void setParamsRepository(ParamsRepository paramsRepository) {
        this.paramsRepository = paramsRepository;
    }
}
