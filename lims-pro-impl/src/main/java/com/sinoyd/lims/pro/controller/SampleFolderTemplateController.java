package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.customer.DtoComplexQuery;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoProjectScheme;
import com.sinoyd.lims.pro.service.SampleFolderTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * SampleFolderTemplate服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/04
 * @since V100R001
 */
@Api(tags = "示例: SampleFolderTemplate服务")
@RestController
@RequestMapping("api/pro/sampleFolderTemplate")
public class SampleFolderTemplateController extends BaseJpaController<DtoSampleFolderTemplate, String, SampleFolderTemplateService> {

    @ApiOperation(value = "选择点位", notes = "选择点位")
    @PostMapping("/selectSampleFolder")
    public RestResponse<Void> selectSampleFolder(@RequestBody DtoSampleFolderTemplate sampleFolderTemplate) {
        RestResponse<Void> response = new RestResponse<>();
        service.selectSampleFolder(sampleFolderTemplate.getFolderIds(), sampleFolderTemplate.getApproveId(), false);
        return response;
    }

    @ApiOperation(value = "新增点位", notes = "新增点位")
    @PostMapping
    public RestResponse<Void> addFolder(@RequestBody DtoSampleFolder sampleFolder) {
        RestResponse<Void> response = new RestResponse<>();
        service.addFolder(sampleFolder);
        return response;
    }


    @ApiOperation(value = "新增点位周期频次", notes = "新增点位周期频次")
    @PostMapping("/periodTime")
    public RestResponse<Void> addPeriodTimes(@RequestBody DtoSamplingFrequency dto) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.addSchemePeriodTimes(dto.getSampleFolderId(), dto.getPeriodCount(), dto.getTimePerPeriod(), dto.getSamplePerTime());
        return restResponse;
    }

    @ApiOperation(value = "更新点位", notes = "更新点位")
    @PutMapping
    public RestResponse<DtoSampleFolderTemplate> update(@RequestBody  @Validated DtoSampleFolderTemplate dto) {
        RestResponse<DtoSampleFolderTemplate> restResponse = new RestResponse<>();
        restResponse.setData(service.update(dto));
        return restResponse;
    }

    @ApiOperation(value = "复制点位", notes = "复制点位")
    @PostMapping("/copy")
    public RestResponse<Void> copy(@RequestBody DtoComplexQuery dto) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.copySampleFolderTemplate(dto.getIds(), dto.getTimes());
        return restResponse;
    }

    @ApiOperation(value = "标记删除点位", notes = "标记删除点位")
    @PostMapping("/markDeleteFolder")
    public RestResponse<Void> markDeleteFolder(@RequestBody DtoSampleFolderTemplate sampleFolderTemplate) {
        RestResponse<Void> response = new RestResponse<>();
        service.markDeleteFolderTemp(sampleFolderTemplate.getFolderIds(), sampleFolderTemplate.getApproveId());
        return response;
    }


    @ApiOperation(value = "新增次数", notes = "新增次数")
    @PostMapping("/time")
    public RestResponse<Void> addTimes(@RequestBody DtoSamplingFrequency dto) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.addTimes(dto.getSampleFolderId(), dto.getPeriodCount(), dto.getTimePerPeriod());
        return restResponse;
    }

    @ApiOperation(value = "复制周期", notes = "复制周期")
    @PostMapping("/copyPeriod/{copyTimes}")
    public RestResponse<Void> copyPeriod(@RequestBody DtoSamplingFrequency dto, @PathVariable(name = "copyTimes") Integer copyTimes) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.copyPeriod(dto.getSampleFolderId(), dto.getPeriodCount(), copyTimes);
        return restResponse;
    }

    @ApiOperation(value = "删除周期", notes = "删除周期")
    @PostMapping(path = "/period/{sampleFolderId}/{periodCount}")
    public RestResponse<Boolean> deletePeriod(@PathVariable(name = "sampleFolderId") String sampleFolderId,
                                              @PathVariable(name = "periodCount") Integer periodCount) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.deletePeriod(sampleFolderId, periodCount);
        restResponse.setData(true);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "刪除周期頻次", notes = "刪除周期頻次")
    @PostMapping("/time/{samplingFrequencyTempId}")
    public RestResponse<Void> deleteTimes(@PathVariable(name = "samplingFrequencyTempId") String samplingFrequencyTempId) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.markDeleteFrequencyTemp(samplingFrequencyTempId);
        return restResponse;
    }

    @ApiOperation(value = "批量刪除周期頻次", notes = "批量刪除周期頻次")
    @PostMapping("/time/batchDeleteFrequency")
    public RestResponse<Void> batchDeleteFrequency(@RequestBody List<String> samplingFrequencyTempIds) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.batchMarkDeleteFrequencyTemp(samplingFrequencyTempIds);
        return restResponse;
    }

    @ApiOperation(value = "复制次数", notes = "复制次数")
    @PostMapping("/copyTime/{copyTimes}")
    public RestResponse<Void> copyTimes(@RequestBody DtoSamplingFrequencyTest dto,
                                        @PathVariable(name = "copyTimes") Integer copyTimes) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.copyTimes(dto.getSampleFolderId(), dto.getSamplingFrequencyId(), copyTimes);
        return restResponse;
    }

    @ApiOperation(value = "取消點位", notes = "取消點位")
    @PostMapping("/cancelFolderTemplate")
    public RestResponse<Void> cancelFolderTemplate(@RequestBody DtoSampleFolderTemplate sampleFolderTemplate) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.cancelFolderTemplate(sampleFolderTemplate.getFolderIds());
        return restResponse;
    }

    @ApiOperation(value = "查詢方案", notes = "查詢方案")
    @GetMapping("/scheme")
    public RestResponse<DtoProjectScheme> findScheme(@RequestParam(name = "approveId") String approveId) {
        RestResponse<DtoProjectScheme> response = new RestResponse<>();
        response.setData(service.findScheme(approveId));
        return response;
    }

    @ApiOperation(value = "获取点位列表", notes = "获取点位列表")
    @GetMapping("/getFolderList")
    public RestResponse<List<DtoSampleFolder>> getFolderList(@RequestParam(name = "approveId") String approveId,
                                                             @RequestParam(name = "sampleTypeId") String sampleTypeId,
                                                             @RequestParam(name = "watchSpot") String watchSpot) {
        RestResponse<List<DtoSampleFolder>> response = new RestResponse<>();
        response.setData(service.getFolderList(approveId, sampleTypeId, watchSpot));
        return response;
    }

    @ApiOperation(value = "设置分包", notes = "设置分包")
    @PutMapping(path = "/sub")
    public RestResponse<Boolean> sub(@RequestBody DtoProjectScheme dto) {
        RestResponse<Boolean> restResponse = new RestResponse<>();
        service.sub(dto.getSamplingFrequencyIds(), dto.getTest());
        restResponse.setData(true);
        return restResponse;
    }

    @ApiOperation(value = "取消删除点位", notes = "取消删除点位")
    @PostMapping("/cancelDeleteFolders")
    public RestResponse<Void> cancelDeleteFolders(@RequestBody DtoSampleFolderTemplate sampleFolderTemplate) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.cancelDeleteFolders(sampleFolderTemplate.getFolderIds());
        return restResponse;
    }

    @ApiOperation(value = "取消删除点位周期频次", notes = "取消删除点位周期频次")
    @PostMapping("/cancelDeleteFrequency")
    public RestResponse<Void> cancelDeleteFrequency(@RequestBody DtoSampleFolderTemplate sampleFolderTemplate) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.cancelDeleteFrequency(sampleFolderTemplate.getFolderIds());
        return restResponse;
    }

    @ApiOperation(value = "恢复点位", notes = "恢复点位")
    @PostMapping("/restoreFolders")
    public RestResponse<Void> restoreFolders(@RequestBody DtoSampleFolderTemplate sampleFolderTemplate) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.restoreFolders(sampleFolderTemplate.getFolderIds());
        return restResponse;
    }

    @ApiOperation(value = "恢复点位周期频次", notes = "恢复点位周期频次")
    @PostMapping("/restoreFrequency")
    public RestResponse<Void> restoreFrequency(@RequestBody DtoSampleFolderTemplate sampleFolderTemplate) {
        RestResponse<Void> restResponse = new RestResponse<>();
        service.restoreFrequency(sampleFolderTemplate.getFolderIds());
        return restResponse;
    }
}
