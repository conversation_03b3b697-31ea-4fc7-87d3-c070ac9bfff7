package com.sinoyd.lims.monitor.dto.rcc;

import com.sinoyd.lims.monitor.entity.Station;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoStation实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_MONITOR_Station") 
 @Where(clause = "isDeleted = 0")
 @Data
 @DynamicInsert
 public  class DtoStation extends Station {
   private static final long serialVersionUID = 1L;


 /**
  * 测站位置名字
  */
 @Transient // 此注解标识为非数据库表字段
 private String staddressName;

 /**
  * 所属单位名称
  */
 @Transient // 此注解标识为非数据库表字段
 private String endName;



}