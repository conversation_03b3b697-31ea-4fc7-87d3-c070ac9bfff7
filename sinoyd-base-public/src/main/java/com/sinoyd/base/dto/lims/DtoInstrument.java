package com.sinoyd.base.dto.lims;

import com.sinoyd.base.dto.customer.DtoImportInstrument;
import com.sinoyd.base.entity.Instrument;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * DtoInstrument实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_BASE_Instrument")
@Data
@DynamicInsert
public class DtoInstrument extends Instrument {
    /**
     * 图片转化后的base64的字符串
     */
    @Transient
    private String base64Content;

    /**
     * 温度
     */
    @Transient
    private String temperature;

    /**
     * 湿度
     */
    @Transient
    private String moisture;

    /**
     * 大气压
     */
    @Transient
    private String pressure;

    /**
     * 开始使用时间
     */
    @Transient
    private Date startTime;

    /**
     * 结束使用时间
     */
    @Transient
    private Date endTime;

    /**
     * 仪器类型名称
     */
    @Transient
    private String instrumentTypeName;

    /**
     * 所属科室名称
     */
    @Transient
    private String belongDeptName;

    /**
     * 状态名称
     */
    @Transient
    private String stateName;

    /**
     * 溯源检定方式
     */
    @Transient
    private String originTypeName;

    /**
     * 过期预警时间（天数）
     */
    @Transient
    private String expireAlertDays;

    /**
     * 出入库记录的使用人名称（多个用，隔开）
     */
    @Transient
    private String user;

    /**
     * 出入库记录的使用人id（多个用,隔开）
     */
    @Transient
    private String userId;

    /**
     * 是否出库
     */
    @Transient
    private String outBound;

    /**
     * 过期天数
     */
    @Transient
    private Integer expirationDays;

    /**
     * 仪器出入库绑定的项目id
     */
    @Transient
    private String projectIds;

    /**
     * 仪器出库时间
     */
    @Transient
    private String outDateStr;

    /**
     * 仪器多检测器溯源记录
     */
    @Transient
    private List<DtoInstrument2Detector> instrument2DetectorList;

    /**
     * 测试项目ids
     */
    @Transient
    private List<String> testIds;

    /**
     * 设置导入实体值
     *
     * @param importInstrument 导入的数据
     */
    public void importToEntity(DtoImportInstrument importInstrument) {
        setInstrumentsCode(importInstrument.getInstrumentsCode());
        setSerialNo(importInstrument.getSerialNo());
        setFixedAssetsCode(importInstrument.getFixedAssetsCode());
        setInstrumentName(importInstrument.getInstrumentName());
        setModel(importInstrument.getModel());
        setInstrumentTypeId(importInstrument.getInstrumentTypeId());
        setInsRange(importInstrument.getInsRange());
        setNicetyRate(importInstrument.getNicetyRate());
        setPrice(importInstrument.getPrice() == null ? BigDecimal.ZERO : new BigDecimal(importInstrument.getPrice()));
        setFactoryName(importInstrument.getFactoryName());
        setSaleName(importInstrument.getSaleName());
        setState(importInstrument.getState() == null ? 1 : Integer.valueOf(importInstrument.getState()));
        setDeptName(importInstrument.getDeptName());
        setManagerName(importInstrument.getManagerName());
        setPlace(importInstrument.getPlace());
        setOriginCyc(importInstrument.getOriginCyc() == null ? BigDecimal.ZERO : new BigDecimal(importInstrument.getOriginCyc()));
        setOriginType(importInstrument.getOriginType() == null ? -1 : Integer.valueOf(importInstrument.getOriginType()));
        setOriginUnit(importInstrument.getOriginUnit());
        setOriginRemark(importInstrument.getOriginRemark());
        setInspectPeriod(importInstrument.getInspectPeriod() == null ? BigDecimal.ZERO : new BigDecimal(importInstrument.getInspectPeriod()));
        setInspectMethod(importInstrument.getInspectMethod());
        setMaintenanceCyc(importInstrument.getMaintenanceCyc() == null ? BigDecimal.ZERO : new BigDecimal(importInstrument.getMaintenanceCyc()));
        setMaintenanceContent(importInstrument.getMaintenanceContent());
        setControlMeasures(importInstrument.getControlMeasures());
        setUseConditions(importInstrument.getUseConditions());
        setUseMethod(importInstrument.getUseMethod());
        setRemark(importInstrument.getRemark());
        setOriginResult(importInstrument.getOriginResult() == null ? 1 : Integer.valueOf(importInstrument.getOriginResult()));
        setInspectResult(importInstrument.getInspectResult() == null ? 1 : Integer.valueOf(importInstrument.getInspectResult()));
    }
}