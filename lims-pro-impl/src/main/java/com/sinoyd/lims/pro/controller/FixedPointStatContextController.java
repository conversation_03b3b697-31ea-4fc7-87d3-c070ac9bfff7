package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.pro.criteria.FixedPointStatCriteria;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.vo.FixedPointStatTestVO;
import com.sinoyd.lims.strategy.context.FixedPointStatContext;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 监测点位统计上下文管理器 Controller
 *
 * <AUTHOR>
 * @version V5.2.0 2025/4/15
 * @since V100R001
 */
@RestController
@RequestMapping("api/pro/fixedPointStatContext")
public class FixedPointStatContextController extends ExceptionHandlerController<FixedPointStatContext> {

    /**
     * 统计点位详情数据
     *
     * @param code     统计类型编码
     * @param criteria 查询条件
     * @return 统计数据
     */
    @PostMapping("/detail/{code}")
    public RestResponse<List<FixedPointStatTestVO>> stat(@PathVariable("code") String code,
                                                         @RequestBody FixedPointStatCriteria criteria) {
        RestResponse<List<FixedPointStatTestVO>> response = new RestResponse<>();
        PageBean<DtoAnalyseData> pb = super.getPageBean();
        response.setData(service.stat(criteria, code, pb));
        response.setCount(pb.getRowsCount());
        return response;
    }
}
