package com.sinoyd.lims.monitor.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * FixedPointExpend实体
 * <AUTHOR>
 * @version V1.0.0 2021/4/16
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
@ApiModel(description="FixedPointExpend")
@Data
public class FixedPointExpend extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public  FixedPointExpend() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 点位标识
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("点位标识")
    private String fixedPointId;

    /**
     * 水体、河流id
     */
    @Column(length=50,nullable=false)
    @ApiModelProperty("水体、河流id")
    private String waterId;

    /**
     * 水域功能代码
     */
    @ApiModelProperty("水域功能代码")
    @Length(message = "水域功能代码{validation.message.length}", max = 255)
    private String functionZoneCode;

    /**
     * 网管断面级别
     */
    @Column(length=50)
    @ApiModelProperty("网管断面级别")
    @Length(message = "网管断面级别{validation.message.length}", max = 50)
    private String watersl;

    /**
     * 域管断面级别
     */
    @Column(length=50)
    @ApiModelProperty("域管断面级别")
    @Length(message = "域管断面级别{validation.message.length}", max = 50)
    private String areasl;

    /**
     * 交接断面管辖级别
     */
    @Column(length=50)
    @ApiModelProperty("交接断面管辖级别")
    @Length(message = "交接断面管辖级别{validation.message.length}", max = 50)
    private String rchal;

    /**
     * 月取水量
     */
    @ApiModelProperty("月取水量")
    @Length(message = "月取水量{validation.message.length}", max = 255)
    private String ygwq;

    /**
     * 功能区噪声_昼间起始时
     */
    @Column(nullable=false)
    @ApiModelProperty("功能区噪声_昼间起始时")
    private Integer dayhorbegin;

    /**
     * 功能区噪声_昼间结束时
     */
    @Column(nullable=false)
    @ApiModelProperty("功能区噪声_昼间结束时")
    private Integer dayhorend;

    /**
     * 功能区噪声_夜间起始时
     */
    @Column(nullable=false)
    @ApiModelProperty("功能区噪声_夜间起始时")
    private Integer nighthorbegin;

    /**
     * 功能区噪声_夜间结束时
     */
    @Column(nullable=false)
    @ApiModelProperty("功能区噪声_夜间结束时")
    private Integer nighthorend;

    /**
     * 测点参照物
     */
    @Column(length=50)
    @ApiModelProperty("测点参照物")
    @Length(message = "测点参照物{validation.message.length}", max = 50)
    private String refer;

    /**
     * 区域噪声_网格边长
     */
    @ApiModelProperty("区域噪声_网格边长")
    @Length(message = "区域噪声_网格边长{validation.message.length}", max = 255)
    private String gridLength;

    /**
     * 区域噪声_网格边宽
     */
    @ApiModelProperty("区域噪声_网格边宽")
    @Length(message = "区域噪声_网格边宽{validation.message.length}", max = 255)
    private String gridWidth;

    /**
     * 区域噪声_噪声声源代码
     */
    @ApiModelProperty("区域噪声_噪声声源代码")
    @Length(message = "区域噪声_噪声声源代码{validation.message.length}", max = 255)
    private String noiseSourceCode;

    /**
     * 区域噪声_噪声功能区代码
     */
    @ApiModelProperty("区域噪声_噪声功能区代码")
    @Length(message = "区域噪声_噪声功能区代码{validation.message.length}", max = 255)
    private String noiseFunZoneCode;

    /**
     * 区域噪声_网格覆盖人口
     */
    @ApiModelProperty("区域噪声_网格覆盖人口")
    @Length(message = "区域噪声_网格覆盖人口{validation.message.length}", max = 255)
    private String gridCoverPeoples;

    /**
     * 道路噪声_路段名称
     */
    @ApiModelProperty("道路噪声_路段名称")
    @Length(message = "道路噪声_路段名称{validation.message.length}", max = 255)
    private String rdsecName;

    /**
     * 道路噪声_路段起始点
     */
    @ApiModelProperty("道路噪声_路段起始点")
    @Length(message = "道路噪声_路段起始点{validation.message.length}", max = 255)
    private String rdsecfromto;

    /**
     * 道路噪声_路段长度
     */
    @ApiModelProperty("道路噪声_路段长度")
    @Length(message = "道路噪声_路段长度{validation.message.length}", max = 255)
    private String railwayLength;

    /**
     * 道路噪声_路幅宽度
     */
    @ApiModelProperty("道路噪声_路幅宽度")
    @Length(message = "道路噪声_路幅长度{validation.message.length}", max = 255)
    private String railwayWidth;

    /**
     * 道路噪声_道路等级
     */
    @Column(length=50)
    @ApiModelProperty("道路噪声_道路等级")
    @Length(message = "道路噪声_道路等级{validation.message.length}", max = 50)
    private String rdLevel;

    /**
     * 大气_测点空气质量报告级别
     */
    @Column(length=50)
    @ApiModelProperty("大气_测点空气质量报告级别")
    @Length(message = "大气_测点空气质量报告级别{validation.message.length}", max = 50)
    private String weekCalcu;

    /**
     * 大气_二氧化硫区管测点级别
     */
    @Column(length=50)
    @ApiModelProperty("大气_二氧化硫区管测点级别")
    @Length(message = "大气_二氧化硫区管测点级别{validation.message.length}", max = 50)
    private String so2pl;

    /**
     * 大气_酸雨区管测点级别
     */
    @Column(length=50)
    @ApiModelProperty("大气_酸雨区管测点级别")
    @Length(message = "大气_酸雨区管测点级别{validation.message.length}", max = 50)
    private String acidpl;

    /**
     * 大气_大气网管测点级别
     */
    @Column(length=50)
    @ApiModelProperty("大气_大气网管测点级别")
    @Length(message = "大气_大气网管测点级别{validation.message.length}", max = 50)
    private String airpl;

    /**
     * 大气_降水网管测点级别
     */
    @Column(length=50)
    @ApiModelProperty("大气_降水网管测点级别")
    @Length(message = "大气_降水网管测点级别{validation.message.length}", max = 50)
    private String acidp;

    /**
     * 地下水_地下水类型代码
     */
    @ApiModelProperty("地下水_地下水类型代码")
    @Length(message = "地下水_地下水类型代码{validation.message.length}", max = 255)
    private String underWaterTypeCode;

    /**
     * 地下水_地下水类型
     */
    @ApiModelProperty("地下水_地下水类型")
    private String underWaterType;

    /**
     * 排气管高度
     */
    @ApiModelProperty("排气管高度")
    @Length(message = "排气管高度{validation.message.length}", max = 255)
    private String exhaustPipeHeight;

    /**
     * 工艺设备名称
     */
    @ApiModelProperty("工艺设备名称")
    @Length(message = "工艺设备名称{validation.message.length}", max = 255)
    private String craftFacilityName;

    /**
     * 净化设备名称
     */
    @ApiModelProperty("净化设备名称")
    @Length(message = "净化设备名称{validation.message.length}", max = 255)
    private String purificateFacilityName;

    /**
     * 污染源种类
     */
    @ApiModelProperty("污染源种类")
    @Length(message = "污染源种类{validation.message.length}", max = 255)
    private String pollutionType;

    /**
     * 工艺设备/启用时间
     */
    @Column(nullable=false)
    @ApiModelProperty("工艺设备/启用时间")
    @JsonFormat(pattern = "yyyy-mm-dd")
    private Date craftFacilityUseDate;

    /**
     * 锅炉制造单位
     */
    @ApiModelProperty("锅炉制造单位")
    @Length(message = "锅炉制造单位{validation.message.length}", max = 255)
    private String boilerMakeUnit;

    /**
     * 名称(型号)
     */
    @ApiModelProperty("名称(型号)")
    @Length(message = "名称(型号){validation.message.length}", max = 255)
    private String equipmentTypeName;

    /**
     * 锅炉投运日期
     */
    @Column(nullable=false)
    @ApiModelProperty("锅炉投运日期")
    @JsonFormat(pattern = "yyyy-mm-dd")
    private Date boilerUseDate;

    /**
     * 烟囱高度
     */
    @ApiModelProperty("烟囱高度")
    @Length(message = "烟囱高度{validation.message.length}", max = 255)
    private String chimneyHeight;

    /**
     * 净化设备制造单位
     */
    @ApiModelProperty("净化设备制造单位")
    @Length(message = "净化设备制造单位{validation.message.length}", max = 255)
    private String purificateFacilityUnit;

    /**
     * 净化设备型号
     */
    @ApiModelProperty("净化设备型号")
    @Length(message = "净化设备型号{validation.message.length}", max = 255)
    private String purificateFacilityType;

    /**
     * 净化设备投运日期
     */
    @Column(nullable=false)
    @ApiModelProperty("净化设备投运日期")
    @JsonFormat(pattern = "yyyy-mm-dd")
    private Date purificateFacilityUseDate;

    /**
     * 燃料类型
     */
    @ApiModelProperty("燃料类型")
    @Length(message = "燃料类型{validation.message.length}", max = 255)
    private String fuelType;

    /**
     * 炉窖设备型号
     */
    @ApiModelProperty("炉窖设备型号")
    @Length(message = "炉窖设备型号{validation.message.length}", max = 255)
    private String stoveFacilityType;

    /**
     * 炉窖设备编号
     */
    @ApiModelProperty("炉窖设备编号")
    @Length(message = "炉窖设备编号{validation.message.length}", max = 255)
    private String stoveFacilityCode;

    /**
     * 排放去向
     */
    @ApiModelProperty("排放去向")
    @Length(message = "排放去向{validation.message.length}", max = 255)
    private String emissionFate;

    /**
     * 进出口
     */
    @ApiModelProperty("进出口")
    @Length(message = "进出口{validation.message.length}", max = 255)
    private String importAndExport;

    /**
     * 所属水厂
     */
    @ApiModelProperty("所属水厂")
    @Length(message = "饮用水-所属水厂{validation.message.length}", max = 200)
    private String waterworks;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}