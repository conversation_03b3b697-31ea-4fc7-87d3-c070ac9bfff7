package com.sinoyd.lims.pro.strategy.strategy.documentFileName;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.service.ReportService;
import com.sinoyd.lims.strategy.IFileNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 报告附件名称生成
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2022/7/28
 */
@Component(IFileNameConstant.FileNameStrategyKey.REPORT_FILENAME)
public class ReportFileNameStrategy extends AbsDocumentFileNameStrategy{

    private ReportService reportService;

    @Override
    public Map<String, String> generateDocumentName(Map<String,Object> map) {
        Map<String, String> reportMap = new HashMap<>();
        if (map.containsKey(EnumPRO.EnumDocumnetName.报告报表.getValue())) {
            String reportId = map.get(EnumPRO.EnumDocumnetName.报告报表.getValue()).toString();
            if (StringUtil.isNotEmpty(reportId)) {
                DtoReport report = reportService.findOne(reportId);
                if (StringUtil.isNotNull(report)) {
                    reportMap.put("reportCode", report.getCode());
                }
            }
        }
        return reportMap;
    }

    @Autowired
    @Lazy
    public void setReportService(ReportService reportService) {
        this.reportService = reportService;
    }
}
