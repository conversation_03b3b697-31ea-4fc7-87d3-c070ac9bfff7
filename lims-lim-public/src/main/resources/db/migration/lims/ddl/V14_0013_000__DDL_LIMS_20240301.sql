CREATE TABLE TB_PRO_BusinessSerialNumber
(
    id               VARCHAR(50) NOT NULL COMMENT '主键',
    businessType     VARCHAR(50) NOT NULL COMMENT '业务类型，枚举管理，参考枚举 EnumLogObjectType',
    businessId       VARCHAR(50) NOT NULL COMMENT '业务id',
    businessNumber   VARCHAR(50) NOT NULL COMMENT '业务编号',
    serialNumberType VARCHAR(50) NOT NULL COMMENT '流水号类型',
    para0            VARCHAR(50) COMMENT '流水号参数0',
    para1            VARCHAR(50) COMMENT '流水号参数1',
    para2            VARCHAR(50) COMMENT '流水号参数2',
    para3            VARCHAR(50) COMMENT '流水号参数3',
    isDeleted        BIT(1)      NOT NULL DEFAULT b'0' COMMENT '假删 标识',
    orgId            VARCHAR(50) NOT NULL COMMENT '所属机构ID',
    domainId         VARCHAR(50) NOT NULL COMMENT '所属实验室ID',
    creator          VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier         VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '业务流水号表';