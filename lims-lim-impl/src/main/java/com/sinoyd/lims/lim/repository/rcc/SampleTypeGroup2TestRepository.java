package com.sinoyd.lims.lim.repository.rcc;

import java.util.Collection;
import java.util.List;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup2Test;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 样品模板分组
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/4
 * @since V100R001
 */
public interface SampleTypeGroup2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoSampleTypeGroup2Test, String> {

    /**
     * 根据sampleTypeGroupId查询关联数据
     *
     * @param sampleTypeGroupId 样品分组id
     * @return 返回关联数据集合
     */
    List<DtoSampleTypeGroup2Test> findBySampleTypeGroupId(String sampleTypeGroupId);

    /**
     * 根据testId查询关联数据集合
     *
     * @param testId 测试id
     * @return 查询结果集
     */
    List<DtoSampleTypeGroup2Test> findByTestId(String testId);

    /**
     * 根据testId集合查询关联数据集合
     *
     * @param testIds 测试id集合
     * @return 查询结果集
     */
    List<DtoSampleTypeGroup2Test> findByTestIdIn(List<String> testIds);

    /**
     * 根据sampleTypeGroupIds数组查询关联数据
     *
     * @param sampleTypeGroupIds 样品分组id数组
     * @return 返回关联数据集合
     */
    @Query("select p from DtoSampleTypeGroup2Test p where p.sampleTypeGroupId in :sampleTypeGroupIds")
    List<DtoSampleTypeGroup2Test> findBySampleTypeGroupIds(@Param("sampleTypeGroupIds") Collection<String> sampleTypeGroupIds);

    /**
     * 根据sampleTypeGroupIds和testIds数组查询关联数据
     *
     * @param sampleTypeGroupIds 样品分组id数组
     * @param testIds            测试id数组
     * @return 返回关联数据集合
     */
    @Query("select p from DtoSampleTypeGroup2Test p where p.sampleTypeGroupId in :sampleTypeGroupIds and p.testId in :testIds")
    List<DtoSampleTypeGroup2Test> findBySampleTypeGroupIdAndTestIds(@Param("sampleTypeGroupIds") Collection<String> sampleTypeGroupIds, @Param("testIds") Collection<String> testIds);


    /**
     * 查询关联数据
     *
     * @param sampleTypeGroupId 样品分组id
     * @param testIds           测试项目id集合
     * @return 返回关联数据集合
     */
    @Query("select p from DtoSampleTypeGroup2Test p where p.sampleTypeGroupId = :sampleTypeGroupId and p.testId in :testIds")
    List<DtoSampleTypeGroup2Test> getList(@Param("sampleTypeGroupId") String sampleTypeGroupId, @Param("testIds") Collection<String> testIds);

    /**
     * 删除关联数据
     *
     * @param sampleTypeGroupId 样品分组id
     * @param testIds           测试项目id集合
     * @return 返回删除数量
     */
    @Modifying
    @Query("delete from DtoSampleTypeGroup2Test p where p.sampleTypeGroupId = :sampleTypeGroupId and p.testId in :testIds")
    Integer delete(@Param("sampleTypeGroupId") String sampleTypeGroupId, @Param("testIds") Collection<String> testIds);

}