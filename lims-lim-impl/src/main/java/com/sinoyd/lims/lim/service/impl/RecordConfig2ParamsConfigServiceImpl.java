package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig2ParamsConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.ParamsConfigRepository;
import com.sinoyd.lims.lim.repository.rcc.ParamsRepository;
import com.sinoyd.lims.lim.repository.rcc.RecordConfig2ParamsConfigRepository;
import com.sinoyd.lims.lim.service.RecordConfig2ParamsConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 采样单配置关联检测类型参数配置操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2022/6/27
 * @since V100R001
 */
@Service
public class RecordConfig2ParamsConfigServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoRecordConfig2ParamsConfig, String, RecordConfig2ParamsConfigRepository> implements RecordConfig2ParamsConfigService {

    private ParamsConfigRepository paramsConfigRepository;

    private ParamsRepository paramsRepository;

    /**
     * 分页查询
     *
     * @param page 分页数据
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoRecordConfig2ParamsConfig> page, BaseCriteria criteria) {
        page.setEntityName("DtoRecordConfig2ParamsConfig r,DtoParamsConfig pc");
        page.setSelect("select r");
        page.setSort("pc.orderNum-");
        super.findByPage(page, criteria);
        //分页的数据
        List<DtoRecordConfig2ParamsConfig> paramsConfigList = page.getData();
        //此采样单关联的所有参数配置id
        List<String> paramsConfigIds = paramsConfigList.stream().map(DtoRecordConfig2ParamsConfig::getParamsConfigId).collect(Collectors.toList());
        //查询此采样单下所有关联的参数配置数据
        List<DtoParamsConfig> paramsConfigs = paramsConfigRepository.findByIdIn(paramsConfigIds);

        //赋值参数配置数据
        for (DtoRecordConfig2ParamsConfig dto : paramsConfigList) {
            Optional<DtoParamsConfig> paramsConfig = paramsConfigs.stream().filter(p -> dto.getParamsConfigId().equals(p.getId())).findFirst();
            if (paramsConfig.isPresent()){
                String paramsId = paramsConfig.get().getParamsId();
                DtoParams params = paramsRepository.findOne(paramsId);
                if (StringUtil.isNotNull(params)){
                    dto.setParamsName(params.getParamName());
                }
                dto.setAlias(paramsConfig.get().getAlias());
                dto.setDefaultValue(paramsConfig.get().getDefaultValue());
                dto.setIsRequired(paramsConfig.get().getIsRequired());
                dto.setOrderNum(paramsConfig.get().getOrderNum());
                dto.setParamsType(paramsConfig.get().getParamsType());
                dto.setParamsTypeName(getParamsTypeName(paramsConfig.get().getParamsType()));

            }
        }
        //按照分析项目倒序排序
        paramsConfigList.sort(Comparator.comparing(DtoRecordConfig2ParamsConfig::getOrderNum).reversed());
        //设置数据
        page.setData(paramsConfigList);
    }

    /**
     * 保存关联数据
     *
     * @param recordConfig 采样单配置信息
     * @return 已保存的数据
     */
    @Override
    @Transactional
    public List<DtoRecordConfig2ParamsConfig> save(DtoRecordConfig recordConfig) {
        //需要保存的数据
        List<DtoRecordConfig2ParamsConfig> saveList = new ArrayList<>();
        if (StringUtil.isNotNull(recordConfig)){
            //需要保存的采样单配置id
            String recordId = recordConfig.getId();
            //需要保存的参数配置id
            List<String> paramsConfigIds = recordConfig.getParamsConfigIds();
            //保存数据集合赋值
            paramsConfigIds.forEach(p->{
                DtoRecordConfig2ParamsConfig dto = new DtoRecordConfig2ParamsConfig();
                dto.setRecordConfigId(recordId);
                dto.setParamsConfigId(p);
                saveList.add(dto);
            });
        }
        //保存数据
        return super.save(saveList);
    }

    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        return super.logicDeleteById(ids);
    }

    /**
     * 获取参数类型名称
     *
     * @param paramsType 参数类型
     * @return 参数类型名称
     */
    private String getParamsTypeName(Integer paramsType){
        String paramsTypeName;
        Integer publicParams = EnumLIM.EnumParamsType.公共参数.getValue();
        Integer sampleParams = EnumLIM.EnumParamsType.样品参数.getValue();
        Integer analyseItemParams = EnumLIM.EnumParamsType.分析项目参数.getValue();
        Integer pointParams = EnumLIM.EnumParamsType.点位参数.getValue();
        if (publicParams.equals(paramsType)){
            paramsTypeName = EnumLIM.EnumParamsType.公共参数.name();
        }else if (sampleParams.equals(paramsType)){
            paramsTypeName = EnumLIM.EnumParamsType.样品参数.name();
        }else if (analyseItemParams.equals(paramsType)){
            paramsTypeName = EnumLIM.EnumParamsType.分析项目参数.name();
        }else if (pointParams.equals(paramsType)){
            paramsTypeName = EnumLIM.EnumParamsType.点位参数.name();
        }else{
            paramsTypeName = "";
        }
        return paramsTypeName;
    }

    @Autowired
    public void setParamsConfigRepository(ParamsConfigRepository paramsConfigRepository) {
        this.paramsConfigRepository = paramsConfigRepository;
    }

    @Autowired
    public void setParamsRepository(ParamsRepository paramsRepository) {
        this.paramsRepository = paramsRepository;
    }
}
