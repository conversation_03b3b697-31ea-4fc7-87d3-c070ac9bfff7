package com.sinoyd.lims.foreign.service.impl;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.foreign.DataReportVO;
import com.sinoyd.lims.foreign.service.DataReportService;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.pro.enums.EnumPRO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报告信息接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/7
 * @since V100R001
 */
@Service
public class DataReportServiceImpl extends AbsQueryDataServiceImpl<DataReportVO> implements DataReportService {

    private PersonRepository personRepository;

    private final Map<String, String> reportStatusMap = new HashMap<>();

    @Override
    public StringBuilder getSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select t1.id, t1.projectId,t1.code,t1.createDate,t1.modifyDate,t1.modifier,t2.configName,t1.reportYear,");
        sql.append("t1.analyseItemSortId,t1.folderSortId,t1.orgId,t1.domainId,t1.status");
        sql.append(" from TB_PRO_Report t1 INNER JOIN TB_LIM_SerialIdentifierConfig t2 on t1.reportTypeId = t2.id");
        sql.append(" WHERE 1 = 1 ");
        return sql;
    }

    @Override
    protected void appendCondition(StringBuilder sql, Map<String, Object> map, String condition, String paramName) {
        super.appendCondition(sql, map, "AND t1.modifyDate > :syncTime", "syncTime");
        super.appendCondition(sql, map, "AND t1.projectId in (:projectIds)", "projectIds");
    }

    @Override
    protected RowMapper<DataReportVO> getEntityRowMapper() {
        return (rs, i) -> {
            DataReportVO reportVO = new DataReportVO();
//            String reportId = UUIDHelper.NewID();
            String reportId = rs.getString("id");
            reportVO.setId(reportId);
            reportVO.setProjectId(rs.getString("projectId"));
            reportVO.setCode(rs.getString("code"));
            reportVO.setReportDate(rs.getDate("createDate"));
            reportVO.setSignDate(rs.getDate("modifyDate"));
            reportVO.setSignPerson(rs.getString("modifier"));
            reportVO.setReportType(rs.getString("configName"));
            reportVO.setReportYear(rs.getInt("reportYear"));
            reportVO.setAnalyseItemSortId(rs.getString("analyseItemSortId"));
            reportVO.setFolderSortId(rs.getString("folderSortId"));
            reportVO.setOrgId(rs.getString("orgId"));
            reportVO.setDomainId(rs.getString("domainId"));
            reportVO.setSyncTime(new Date());
            reportStatusMap.put(reportId, rs.getString("status"));
            return reportVO;
        };
    }

    @Override
    protected void individuationData(Map<String, Object> map, List<DataReportVO> list) {
        List<DtoPerson> personList = personRepository.findAllDeleted();
        Map<String, String> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, DtoPerson::getCName));
        list.forEach(p -> {
            if (EnumPRO.EnumReportState.已签发.toString().equals(reportStatusMap.get(p.getId()))){
                p.setSignPerson(personMap.getOrDefault(p.getSignPerson(), ""));
            }else{
                p.setSignDate(null);
                p.setSignPerson(null);
            }
        });
    }

    @Autowired
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }
}
