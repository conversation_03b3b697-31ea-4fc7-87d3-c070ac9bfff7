package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * ParamsFormula实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ParamsFormula")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class ParamsFormula implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  ParamsFormula() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
       this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 参数Id（测试项目、检测类型参数）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("参数Id（测试项目、检测类型参数）")
	private String objectId;
    
    /**
    * 公式
    */
    @Column(length=1000)
    @ApiModelProperty("公式")
    @Length(message = "公式{validation.message.length}", max = 1000)
    private String formula;
    
    /**
    * 配置日期
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("配置日期")
    private Date configDate;
    
    /**
    * 原始公式
    */
    @ApiModelProperty("原始公式")
	private String orignFormula;
    
    /**
    * 原始公式类型（枚举EnumOrignFormulatType：0:手写html,1:图片）
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("原始公式类型（枚举EnumOrignFormulatType：0:手写html,1:图片）")
    private Integer orignFormulatType;
    
    /**
    * 假删
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
	private Boolean isDeleted=false;
    
    /**
    * 类型（枚举EnumParamsFormulaObjectType：0.测试公式 1.检测类型参数公式）
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
    @ApiModelProperty("类型（枚举EnumParamsFormulaObjectType：0.测试公式 1.检测类型参数公式）")
    private Integer objectType;
    
    /**
    * 检测类型id（仅用于测试项目）
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("检测类型id（仅用于测试项目）")
    private String sampleTypeId;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;

    /**
     * 验证状态 0未验证 1已验证
     */
    @ColumnDefault("0")
    @ApiModelProperty("验证状态 0未验证 1已验证")
    private Integer validate;

    /**
     * 使用次数
     */
    @ApiModelProperty("使用次数")
    private Integer usageNum;
 }