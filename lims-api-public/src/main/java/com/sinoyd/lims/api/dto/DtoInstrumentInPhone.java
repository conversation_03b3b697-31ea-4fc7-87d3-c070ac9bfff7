package com.sinoyd.lims.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 仪器入库扫码实体
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/2/2
 */
@Data
@Accessors(chain = true)
public class DtoInstrumentInPhone {

    /**
     * 仪器id
     */
    private String id;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器状态
     */
    private Integer state;

    /**
     * 仪器编号
     */
    private String instrumentsCode;


    /**
     * 规格型号
     */
    private String model;


    /**
     * 出库人id
     */
    private String outPersonId;


    /**
     * 出库人名称
     */
    private String outPersonName;

    /**
     * 出库日期
     */
    private Date outDate;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 使用人名称
     */
    private String userNames;

    /**
     * 仪器出入库记录id
     */
    private String projectInstrumentId;

    /**
     * 有效期
     */
    private Date originEndDate;
}
