package com.sinoyd.lims.pro.dto;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 审批任务详情传输实体
 * <AUTHOR>
 * @version V1.0.0 2019-03-27
 * @since V100R001
 */
@Data
public class DtoOATaskDetail<D, E> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 审批任务信息
     */
    private DtoOATask task;

    /**
     * 附件列表
     */
    private List<DtoOATaskAttach> attach;

    /**
     * 详细信息
     */
    private D detail; 

    /**
     * 扩展信息
     */
    private E extend;
}