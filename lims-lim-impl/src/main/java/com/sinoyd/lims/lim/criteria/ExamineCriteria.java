package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 考核管理查询条件
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExamineCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  登记开始日期
     */
    private String registeStartDate;

    /**
     *  登记结束日期
     */
    private String registeEndDate;

    /**
     * 考核部门
     */
    private String deptId;

    /**
     *  责任人
     */
    private String inspectedPersonId;


    /**
     *  所有受考核人员名称
     */
    private String fullChargePeopleName;


    /**
     *  处理状态
     */
    private Integer status;


    /**
     * 关键字
     */
    private String keyWord;

    /**
     *  用以区分在不同模块的显示
     */
    private Integer stepValue;

    /**
     *  登记人
     */
    private String  addPersonId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        //状态区分显示模块
        if(EnumLIM.EnumExamineStep.考核下达.getValue().equals(stepValue)){
            if(EnumLIM.EnumExamineHandleStatus.待处理.getValue().equals(status)){
                condition.append(" and e.status = :status");
                values.put("status", EnumLIM.EnumExamineStatus.编辑中.getValue());
            }
            if(EnumLIM.EnumExamineHandleStatus.已处理.getValue().equals(status)){
                condition.append(" and e.status >= :status");
                values.put("status", EnumLIM.EnumExamineStatus.已下达.getValue());
            }
        }
        else if(EnumLIM.EnumExamineStep. 考核分配.getValue().equals(stepValue)){
            condition.append(" and e.status >= :status");
            values.put("status", EnumLIM.EnumExamineStatus.已下达.getValue());
            if(EnumLIM.EnumExamineHandleStatus.待处理.getValue().equals(status)){
                condition.append(" and e.status = :status");
                values.put("status", EnumLIM.EnumExamineStatus.已下达.getValue());
            }
            if(EnumLIM.EnumExamineHandleStatus.已处理.getValue().equals(status)){
                condition.append(" and e.status >= :status");
                values.put("status", EnumLIM.EnumExamineStatus.考核中.getValue());
            }
        }
        else if(EnumLIM.EnumExamineStep. 考核填报.getValue().equals(stepValue)){
            condition.append(" and e.status >= :status");
            values.put("status", EnumLIM.EnumExamineStatus.考核中.getValue());
            if(EnumLIM.EnumExamineHandleStatus.待处理.getValue().equals(status)){
                condition.append(" and (e.status = :status1 or e.status = :status2) ");
                values.put("status1", EnumLIM.EnumExamineStatus.考核中.getValue());
                values.put("status2", EnumLIM.EnumExamineStatus.审核不通过.getValue());
            }
            if(EnumLIM.EnumExamineHandleStatus.已处理.getValue().equals(status)){
                condition.append(" and (e.status = :status1 or e.status = :status2) ");
                values.put("status1", EnumLIM.EnumExamineStatus.待审核.getValue());
                values.put("status2", EnumLIM.EnumExamineStatus.审核通过.getValue());
            }
        }
        else if(EnumLIM.EnumExamineStep.考核审核.getValue().equals(stepValue)){
            condition.append(" and e.status >= :status");
            values.put("status", EnumLIM.EnumExamineStatus.待审核.getValue());
            if(EnumLIM.EnumExamineHandleStatus.待处理.getValue().equals(status)){
                condition.append(" and e.status = :status");
                values.put("status", EnumLIM.EnumExamineStatus.待审核.getValue());
            }
            if(EnumLIM.EnumExamineHandleStatus.已处理.getValue().equals(status)){
                condition.append(" and e.status = :status");
                values.put("status", EnumLIM.EnumExamineStatus.审核通过.getValue());
            }
        }

        //登记时间区间
        if(StringUtil.isNotEmpty(registeStartDate)&&StringUtil.isNotEmpty(registeEndDate)){
            condition.append(" and (e.addDate >= :registeStartDate and e.addDate <= :registeEndDate) ");
            values.put("registeStartDate", DateUtil.stringToDate(registeStartDate, DateUtil.YEAR));
            values.put("registeEndDate", DateUtil.stringToDate(registeEndDate, DateUtil.YEAR));
        }

        //考核部门
        if(StringUtil.isNotEmpty(deptId)){
            condition.append(" and e.deptId = :deptId ");
            values.put("deptId", deptId);
        }

        //责任人
        if(StringUtil.isNotEmpty(inspectedPersonId)){
            condition.append(" and e.inspectedPersonId = :inspectedPersonId ");
            values.put("inspectedPersonId", inspectedPersonId);
        }

        //受考核人员
        if(StringUtil.isNotEmpty(fullChargePeopleName)){
            condition.append(" and (exists (select 1 from  DtoExamineType t where e.id = t.examineId and t.inspectedPersonId = :inspectedPersonId2) or e.inspectedPersonId = :inspectedPersonId3 )");
            values.put("inspectedPersonId2",  fullChargePeopleName);
            values.put("inspectedPersonId3",  fullChargePeopleName);
        }

        //关键字
        if(StringUtil.isNotEmpty(keyWord)){
            condition.append(" and e.title like :title ");
            values.put("title", "%" + keyWord + "%");
        }
        //登记人员
        if(StringUtil.isNotEmpty(addPersonId)){
            condition.append(" and e.addPersonId = :addPersonId ");
            values.put("addPersonId", addPersonId);
        }
        return condition.toString();
    }
}
