package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoAppConfig;

import java.util.List;

/**
 * app应用配置仓储
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/12
 */
public interface AppConfigRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoAppConfig, String> {

    /**
     * 根据应用编码和id查询数量
     *
     * @param code 应用编码
     * @param id   主键
     * @return app应用配置Dto
     */
    Integer countByCodeAndIdNot(String code, String id);

    /**
     * 查询应用配置
     * @return 配置内容
     */
    List<DtoAppConfig> findByStatusTrue();
}