package com.sinoyd.lims.lim.service;

import com.sinoyd.base.criteria.SampleTypeCriteria;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleType2Test;
import com.sinoyd.lims.lim.dto.customer.DtoSampleTypeTemplate;

import java.util.Collection;
import java.util.List;

/**
 * 样品类型2测试项目
 *
 * <AUTHOR>
 * @version V1.0.0 2019/2/25
 * @since V100R001
 */
public interface SampleType2TestService extends IBaseJpaService<DtoSampleType2Test, String> {
    /**
     * 根据id进行查询
     *
     * @param id
     * @return DtoSampleTypeTemplate
     */
    DtoSampleTypeTemplate getSampleTypeById(String id);

    /**
     * 新增检测模板配置
     *
     * @param sampleTypeTemplate 检测模板信息
     */
    DtoSampleTypeTemplate createTemplate(DtoSampleTypeTemplate sampleTypeTemplate);

    /**
     * 新增检测模板配置
     *
     * @param sampleTypeTemplate 检测模板信息
     */
    DtoSampleTypeTemplate updateTemplate(DtoSampleTypeTemplate sampleTypeTemplate);

    /**
     * 获取检测类型集合下的模板明细（有测试项目的模板才会筛选出来）
     *
     * @param sampleTypeIds 检测类型id集合
     * @return 模板
     */
    List<DtoSampleTypeTemplate> findTemplateBySampleTypeIdIn(Collection<String> sampleTypeIds);

    /**
     * 修改样品模板测试项目频次及样品数
     *
     * @param testList 模板测试项目
     */
    void updateTestMsg(List<DtoSampleType2Test> testList);

    /**
     * 根据模板id和测试项目id删除
     *
     * @param sampleTypeId 模板id
     * @param testIds      测试项目id
     */
    void deleteTest(String sampleTypeId, List<String> testIds);


    /**
     * 复制检测模版
     *
     * @param id 源id
     * @return 复制后检测模版
     */
    DtoSampleType copyTemplate(String id);

    /**
     * 分页获取检测类型
     *
     * @param page         分页
     * @param baseCriteria 查询条件
     */
    void findSampleTypeByPage(PageBean<DtoSampleType> page, BaseCriteria baseCriteria);

    /**
     * 修改样品模板测试项目频次及样品数 取测试项目上的配置
     *
     * @param sampleTypeId 模板标识
     */
    DtoSampleTypeTemplate updateFrequencyByConfig(String sampleTypeId);
}