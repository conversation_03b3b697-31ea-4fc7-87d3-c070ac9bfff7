package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeItemRelation;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 分析项目关系仓储
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-13
 * @since V100R001
 */
public interface AnalyzeItemRelationRepository
        extends IBaseJpaPhysicalDeleteRepository<DtoAnalyzeItemRelation, String> {

    /**
     * 通过analyzeItemId查询,用来去重
     *
     * @param analyzeItemId 分析项目id
     * @return 查询到的数量
     */
    @Query("select count(p.analyzeItemId) from DtoAnalyzeItemRelation p where p.analyzeItemId = :analyzeItemId")
    Integer getByAnalyzeItemId(@Param("analyzeItemId") String analyzeItemId);

    /**
     * 通过analyzeItemId查询,用来去重
     *
     * @param analyzeItemId 分析项目id
     * @param id            排除当前数据id
     * @return 查询到的数量
     */
    @Query("select count(p.analyzeItemId) from DtoAnalyzeItemRelation p where p.analyzeItemId = :analyzeItemId and p.id <> :id")
    Integer getByAnalyzeItemIdAndNotId(@Param("analyzeItemId") String analyzeItemId, @Param("id") String id);

    /**
     * 通过analyzeItemId、formula查询,用来去重
     *
     * @param analyzeItemId 分析项目id
     * @param formula       分析项目关系公式
     * @return 查询到的数量
     */
    @Query("select count(p.analyzeItemId) from DtoAnalyzeItemRelation p where p.analyzeItemId = :analyzeItemId and p.formula = :formula")
    Integer getByAnaIdAndFormula(@Param("analyzeItemId") String analyzeItemId, @Param("formula") String formula);

    /**
     * 根据分析项目id及类型获取相关的数据
     *
     * @param analyzeItemId 分析项目id
     * @param type          类型数据
     * @return 返回相关的提醒配置
     */
    List<DtoAnalyzeItemRelation> findByAnalyzeItemIdAndType(String analyzeItemId, Integer type);
}