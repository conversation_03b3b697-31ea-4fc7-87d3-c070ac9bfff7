package com.sinoyd.base.utils;

import com.sinoyd.frame.base.util.DateUtil;

import java.util.Calendar;
import java.util.Date;

public class CalendarUtil {


    /**
     * 获取上一年的开始时间
     *
     * @param year 年份
     * @return 上一年的开始时间
     */
    public static String getLastYearBegin(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.add(Calendar.YEAR, -1);
        return DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
    }


    /**
     * 获取上一年的结束日期
     *
     * @param year 年份
     * @return 上一年的结束日期
     */
    public static String getLastYearEnd(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.add(Calendar.YEAR, -1);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);


        return DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
    }

    /**
     * 获取当前年份的开始时间
     *
     * @param year 年份
     * @return 当前年份的开始时间
     */
    public static String getCurrentYearBegin(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        return DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
    }

    /**
     * 获取当前年份的结束时间
     *
     * @param year 年份
     * @return 当前年份的结束时间
     */
    public static String getCurrentYearEnd(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
    }

    /**
     * 获取当前月份的开始时间
     *
     * @param year 年份
     * @return 当前月份的开始时间
     */
    public static String getCurrentMonthBegin(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
    }

    /**
     * 获取当前月份的结束日期
     *
     * @param year 年份
     * @return 当前月份的结束日期
     */
    public static String getCurrentMonthEnd(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 0);

        return DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
    }

    /**
     * 获取上个月的开始日期
     *
     * @param year 年份
     * @return 上个月的开始日期
     */
    public static String getLastMonthBegin(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);//设置为1号

        return DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
    }

    /**
     * 获取上个月的结束日期
     *
     * @param year 年份
     * @return 上个月的结束日期
     */
    public static String getLastMonthEnd(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        return DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
    }

    /**
     * 根据传入日期获取月数
     *
     * @param time
     * @return
     */
    public static Integer getMonth(Date time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取三年前的开始时间
     */

    public static String getThreeYearBegin(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.add(Calendar.YEAR, -2);

        return DateUtil.dateToString(calendar.getTime(), DateUtil.YEAR);
    }

    /**
     * 获取当前日期所在年份
     * @param date 日期
     * @return Integer
     */
    public static Integer getYear(Date date) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        return ca.get(Calendar.YEAR);
    }

    /**
     * 计算日期间隔
     * @param begin  开始时间
     * @param end    结束时间
     * @return       过期天数
     */
    public static int getDaysBetween(Date begin,Date end){
        long time = Math.abs(end.getTime()-begin.getTime());
        return (int)(time/1000/60/60/24);
    }

    /**
     * 星期几显示
     * @param date  日期
     * @return      格式化星期几
     */
    public static  String dateToWeek(Date date) {
        String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        return weekDays[w];
    }
}
