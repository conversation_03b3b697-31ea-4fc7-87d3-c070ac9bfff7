package com.sinoyd.lims.lim.repository.rcc;

import com.sinoyd.base.dto.rcc.DtoQualityLimitDisposition;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.Collection;
import java.util.List;


/**
 * QualityLimitDisposition数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/5/27
 * @since V100R001
 */
public interface QualityLimitDispositionRepository extends IBaseJpaPhysicalDeleteRepository<DtoQualityLimitDisposition, String> {

    /**
     * 根据质控、评判方式、公式 获取配置数据
     *
     * @param qcGrade 质控类型
     * @param qcType  质控种类
     * @param method  评判方式
     * @param formula 公式
     * @return 配置数据
     */
    DtoQualityLimitDisposition findByQcGradeAndQcTypeAndJudgingMethodAndFormula(Integer qcGrade, Integer qcType, Integer method, String formula);


    /**
     * 根据质控、评判方式、是否默认 获取配置数据
     *
     * @param qcGrade     质控类型
     * @param qcType      质控种类
     * @param method      评判方式
     * @param isAcquiesce 是否默认
     * @return 配置数据集合
     */
    List<DtoQualityLimitDisposition> findByQcGradeAndQcTypeAndJudgingMethodAndIsAcquiesce(Integer qcGrade, Integer qcType, Integer method, Boolean isAcquiesce);

    /**
     * 根据质控、评判方式、是否默认 获取配置数据
     *
     * @param qcGrade     质控类型
     * @param qcType      质控种类
     * @param method      评判方式
     * @param isAcquiesce 是否默认
     * @return 配置数据集合
     */
    List<DtoQualityLimitDisposition> findByQcGradeInAndQcTypeInAndJudgingMethodInAndIsAcquiesce(Collection<Integer> qcGrade, Collection<Integer> qcType, Collection<Integer> method, Boolean isAcquiesce);
}