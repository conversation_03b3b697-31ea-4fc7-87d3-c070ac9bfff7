package com.sinoyd.lims.monitor.service;


import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.lims.lim.service.ImportExcelService;
import com.sinoyd.lims.monitor.dto.customer.DtoExpImpFixedPointWR;
import com.sinoyd.lims.monitor.dto.customer.DtoImportFixedPointRS;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 污染源点位导入导出接口
 *
 * @version V1.0.0 2023/12/27
 * @author: hukq
 * @since V100R001
 */
public interface ExportFixedPointWRService extends ImportExcelService<DtoExpImpFixedPointWR, DtoFixedpoint> {

    /**
     * 导出
     *
     * @param criteria   请求参数
     * @param response   响应体
     * @param sheetNames sheet页名称
     * @param fileName   附件名称
     */
    void export(BaseCriteria criteria, HttpServletResponse response, Map<String, String> sheetNames, String fileName);


}
