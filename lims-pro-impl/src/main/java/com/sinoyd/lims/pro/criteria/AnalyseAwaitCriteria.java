package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * AnalyseAwaitCriteria待检样品查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AnalyseAwaitCriteria extends BaseCriteria implements Serializable {

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 项目关键字
     */
    private String projectKey;

    /**
     * 样品关键字
     */
    private String sampleKey;

    /**
     * 检测类型的id
     */
    private String sampleTypeId;

    /**
     * 测试岗位id
     */
    private List<String> testPostIds = new ArrayList<>();

    /**
     * 是否按岗位分配,前端不传递此参数，后端根据系统开关是否启用进行赋值
     */
    private Boolean isAllocateByPost;

    /**
     * 是否隐藏受检单位信息
     */
    private Boolean isHideInspectedEnt;

    /**
     * 采样开始时间
     */
    private String samplingTimeBegin;

    /**
     * 采样结束时间
     */
    private String samplingTimeEnd;

    /**
     * 分析项目，分析方法
     */
    private String analyzeItemKey;

    /**
     * 状态
     */
    private String status;

    /**
     * orgId
     */
    private String orgId;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.sampleId = b.id");
        condition.append(" and b.receiveId = c.id");
        condition.append(" and c.projectId = d.id");
        condition.append(" and a.isDeleted = 0");
        condition.append(" and b.isDeleted = 0");
        if (StringUtils.isNotNullAndEmpty(this.testId) && !this.testId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and a.testId = :testId");
            values.put("testId", this.testId);
        }
        //按岗位分配开关关闭时，才根据分析人员id进行条件过滤
        if ((StringUtils.isNull(isAllocateByPost) || !isAllocateByPost) && StringUtils.isNotNullAndEmpty(this.personId)
                && !this.personId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and a.analystId = :personId");
            values.put("personId", this.personId);
        }

        if (StringUtils.isNotNullAndEmpty(projectKey)) {
            condition.append(" and (d.projectCode like :projectKey or d.projectName like :projectKey or c.recordCode like :projectKey)");
            values.put("projectKey", "%" + this.projectKey + "%");
        }
        if (StringUtils.isNotNullAndEmpty(sampleKey)) {
            StringBuilder builder = new StringBuilder(" and (b.code like :sampleKey or b.inspectedEnt like :sampleKey)");
            if (StringUtils.isNotNullAndEmpty(isHideInspectedEnt)) {
                if (isHideInspectedEnt) {
                    builder = new StringBuilder(" and b.code like :sampleKey");
                }
            }
            condition.append(builder);
            values.put("sampleKey", "%" + this.sampleKey + "%");
        }
        if (StringUtil.isNotEmpty(this.analyzeItemKey)) {
            condition.append(" and (a.redAnalyzeItemName like :analyzeItemKey or a.redAnalyzeMethodName like :analyzeItemKey)");
            values.put("analyzeItemKey", "%" + this.analyzeItemKey + "%");
        }
        if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and b.sampleTypeId =:sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }

        if (StringUtil.isNotEmpty(this.samplingTimeBegin)) {
            Date from = DateUtil.stringToDate(this.samplingTimeBegin, DateUtil.YEAR);
            condition.append(" and b.samplingTimeBegin >= :samplingTimeBegin");
            values.put("samplingTimeBegin", from);
        }
        if (StringUtil.isNotEmpty(this.samplingTimeEnd)) {
            Date to = DateUtil.stringToDate(this.samplingTimeEnd, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            condition.append(" and b.samplingTimeBegin < :samplingTimeEnd");
            values.put("samplingTimeEnd", c.getTime());
        }

        condition.append(" and a.workSheetId = :workSheetId");
        values.put("workSheetId", UUIDHelper.GUID_EMPTY);
        condition.append(" and a.workSheetFolderId = :workSheetFolderId");
        values.put("workSheetFolderId", UUIDHelper.GUID_EMPTY);

        //过滤分包
        condition.append(" and a.isOutsourcing = 0 and a.isSamplingOut = 0");
        //过滤现场
        condition.append(" and a.isCompleteField = 0 ");
        //数据状态
        condition.append(" and a.dataStatus = :dataStatus ");
        values.put("dataStatus", EnumPRO.EnumAnalyseDataStatus.未测.getValue());
        //过滤样品领样状态
        condition.append(" and b.innerReceiveStatus = :innerReceiveStatus ");
        values.put("innerReceiveStatus", EnumPRO.EnumInnerReceiveStatus.已经领取.getValue());
        condition.append(" and b.status <> '样品检毕'");
        if (StringUtil.isNotEmpty(this.status)) {
            values.put("status", this.status);
        }
        if (StringUtil.isNotEmpty(this.orgId)) {
            values.put("orgId", this.orgId);
        }
        return condition.toString();
    }
}
