package com.sinoyd.lims.lim.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;


/**
 * FeeConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "FeeConfig")
@Data
@EntityListeners(AuditingEntityListener.class)
public class FeeConfig extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public FeeConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 费用类型名称
     */
    @Column(length = 50)
    @ApiModelProperty("费用类型名称")
    @Length(message = "费用类型名称{validation.message.length}", max = 50)
    private String typeName;

    /**
     * 收费标准
     */
    @Column(nullable = false)
    @ColumnDefault("0.00")
    @ApiModelProperty("收费标准")
    private BigDecimal standard;

    /**
     * 单位
     */
    @Column(length = 50)
    @ApiModelProperty("单位")
    @Length(message = "单位{validation.message.length}", max = 50)
    private String unit;

    /**
     * 天数
     */
    @Column(nullable = false)
    @ColumnDefault("0.00")
    @ApiModelProperty("天数")
    private BigDecimal days;

    /**
     * 数量
     */
    @Column(nullable = false)
    @ColumnDefault("0.00")
    @ApiModelProperty("数量")
    private BigDecimal count;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 255)
    private String remark;

    /**
     * 是否删除
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 关联系统产品编号
     */
    @Column(length = 50)
    @ApiModelProperty("关联系统产品编号")
    @Length(message = "关联系统产品编号{validation.message.length}", max = 50)
    private String externalId;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 计算公式
     */
    @Length(message = "计算公式{validation.message.length}", max = 1000)
    private String formula;

}