package com.sinoyd.base.repository.lims;


import com.sinoyd.base.dto.lims.DtoLogForDocument;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;

/**
 * DocumentRepository数据接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
public interface LogForDocumentRepository extends IBaseJpaPhysicalDeleteRepository<DtoLogForDocument, String> {

    /**
     * 根据objectId获取日志
     *
     * @param objectId 附件id
     * @return 日志集合
     */
    List<DtoLogForDocument> findByObjectId(String objectId);
}