package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.enums.EnumBase.EnumSampleTypeCategory;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleType2Test;
import com.sinoyd.lims.lim.dto.customer.DtoSampleTypeTemplate;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestExpandRepository;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleType2TestRepository;
import com.sinoyd.lims.lim.service.SampleType2TestService;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 检测模板-测试项目
 *
 * <AUTHOR>
 * @version V1.0.0 2019/3/12
 * @since V100R001
 */
@Service
public class SampleType2TestServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleType2Test, String, SampleType2TestRepository> implements SampleType2TestService {

    @Autowired
    @Lazy
    private SampleType2TestService sampleType2TestService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    @Lazy
    private TestService testService;

    private TestRepository testRepository;

    private AnalyzeMethodRepository analyzeMethodRepository;

    private TestExpandRepository testExpandRepository;

    /**
     * 获取单个检测模板
     */
    @Override
    public DtoSampleTypeTemplate getSampleTypeById(String id) {
        DtoSampleType sampleType = sampleTypeService.findOne(id);
        String bigSampleTypeName = "";
        DtoSampleType samType = sampleTypeService.findOne(sampleType.getParentId());
        bigSampleTypeName = samType.getTypeName();
        if (sampleType.getCategory().equals(EnumSampleTypeCategory.检测类型小类.getValue())) {
            DtoSampleType bigSampleType = sampleTypeService.findOne(samType.getParentId());
            bigSampleTypeName = bigSampleType.getTypeName();
        }

        DtoSampleTypeTemplate sampleTypeTemplate = new DtoSampleTypeTemplate();
        sampleTypeTemplate.setId(sampleType.getId());
        sampleTypeTemplate.setIndustryTypeId(sampleType.getIndustryTypeId());
        sampleTypeTemplate.setParentId(sampleType.getParentId());
        sampleTypeTemplate.setTypeCode(sampleType.getTypeCode());
        sampleTypeTemplate.setTypeName(sampleType.getTypeName());
        sampleTypeTemplate.setRemark(sampleType.getRemark());
        sampleTypeTemplate.setLaboratoryId(sampleType.getLaboratoryId());
        sampleTypeTemplate.setShortName(sampleType.getShortName());
        sampleTypeTemplate.setIsDeleted(sampleType.getIsDeleted());
        sampleTypeTemplate.setKeepLongTime(sampleType.getKeepLongTime());
        sampleTypeTemplate.setReportingCycle(sampleType.getReportingCycle());
        sampleTypeTemplate.setCategory(sampleType.getCategory());
        sampleTypeTemplate.setOrderNum(sampleType.getOrderNum());
        sampleTypeTemplate.setIcon(sampleType.getIcon());
        sampleTypeTemplate.setSystemType(sampleType.getSystemType());
        sampleTypeTemplate.setOrgId(sampleType.getOrgId());
        List<DtoSampleType2Test> sampleType2TestList = repository.findBySampleTypeId(id);
        if (sampleType2TestList.size() > 0) {
            List<String> tids = sampleType2TestList.stream().map(DtoSampleType2Test::getTestId)
                    .distinct().collect(Collectors.toList());
            List<DtoTest> dtoTestList = testService.findAllDeleted(tids);

            List<String> analyzeMethodIds = dtoTestList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
            List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(analyzeMethodIds) ? analyzeMethodRepository.findAllDeleted(analyzeMethodIds) : new ArrayList<>();
            Map<String, DtoAnalyzeMethod> analyzeMethodMap = analyzeMethodList.stream().collect(Collectors.toMap(DtoAnalyzeMethod::getId, p -> p));
            List<DtoTest> tList = new ArrayList<>();
            for (DtoTest test : dtoTestList) {
                DtoTest dtoTest = new DtoTest();
                BeanUtils.copyProperties(test, dtoTest);
                Optional<DtoSampleType2Test> testOptional = sampleType2TestList.stream().filter(p -> test.getId().equals(p.getTestId())).findFirst();
                if (testOptional.isPresent()) {
                    dtoTest.setTimesOrder(testOptional.get().getTimesOrder());
                    dtoTest.setSamplePeriod(testOptional.get().getSamplePeriod());
                    dtoTest.setType2TestId(testOptional.get().getId());
                }
                dtoTest.setSampleTypeName(bigSampleTypeName);
                // 测试项目状态赋值
                DtoAnalyzeMethod analyzeMethod = analyzeMethodMap.get(test.getAnalyzeMethodId());
                String testStatus = "";
                if (analyzeMethod!=null&&((!analyzeMethod.getIsDeleted() && !test.getIsDeleted())
                        || (EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(analyzeMethod.getStatus()) && test.getIsDeleted()))) {
                    testStatus = EnumLIM.EnumAnalyzeMethodStatus.getByValue(analyzeMethod.getStatus());
                } else {
                    testStatus = "删除";
                }
                dtoTest.setTestStatus(testStatus);
                tList.add(dtoTest);
            }
            List<DtoTest> testList = new ArrayList<>();
            Map<String, DtoSampleType2Test> stMap = sampleType2TestList.stream().collect(Collectors.toMap(DtoSampleType2Test::getId, dto -> dto));
            sampleType2TestList.stream().collect(Collectors.groupingBy(DtoSampleType2Test::getParentId)).forEach((parentId, childTests) -> {
                List<String> childTestIds = childTests.stream().map(DtoSampleType2Test::getTestId).collect(Collectors.toList());
                List<DtoTest> childTestList = tList.stream().filter(t -> childTestIds.contains(t.getId())).collect(Collectors.toList());
                if (StringUtils.isNotNullAndEmpty(parentId) && !UUIDHelper.GUID_EMPTY.equals(parentId)) {
                    DtoSampleType2Test parent = stMap.get(parentId);
                    if (StringUtil.isNotNull(parent) && tList.stream().anyMatch(p -> parent.getTestId().equals(p.getId()))) {
                        tList.stream().filter(t -> parent.getTestId().equals(t.getId())).findFirst().ifPresent(p -> {
                            p.setChildTest(childTestList);
                        });
                    }
                } else {
                    testList.addAll(childTestList);
                }
            });
            //测试项目列表按照分析方法、分析项目名称顺序排序
            if (StringUtil.isNotEmpty(testList)) {
                testList.sort(Comparator.comparing(DtoTest::getRedAnalyzeMethodName).thenComparing(DtoTest::getRedAnalyzeItemName));
            }
            sampleTypeTemplate.setTestList(testList);
        }
        return sampleTypeTemplate;
    }

    /**
     * 检测模板新增
     */
    @Transactional
    @Override
    public DtoSampleTypeTemplate createTemplate(DtoSampleTypeTemplate sampleTypeTemplate) {

        DtoSampleType sampleType = new DtoSampleType();
        String sampleTypeId = UUIDHelper.NewID();
        sampleTypeTemplate.setId(sampleTypeId);
        sampleType.setId(sampleTypeId);
        sampleType.setIndustryTypeId(sampleTypeTemplate.getIndustryTypeId());
        sampleType.setParentId(sampleTypeTemplate.getParentId());
        sampleType.setTypeCode(sampleTypeTemplate.getTypeCode());
        sampleType.setTypeName(sampleTypeTemplate.getTypeName());
        sampleType.setRemark(sampleTypeTemplate.getRemark());
        sampleType.setLaboratoryId(sampleTypeTemplate.getLaboratoryId());
        sampleType.setShortName(sampleTypeTemplate.getShortName());
        sampleType.setIsDeleted(sampleTypeTemplate.getIsDeleted());
        sampleType.setKeepLongTime(sampleTypeTemplate.getKeepLongTime());
        sampleType.setReportingCycle(sampleTypeTemplate.getReportingCycle());
        sampleType.setCategory(sampleTypeTemplate.getCategory());
        sampleType.setOrderNum(sampleTypeTemplate.getOrderNum());
        sampleType.setIcon(sampleTypeTemplate.getIcon());
        sampleType.setSystemType(sampleTypeTemplate.getSystemType());
        sampleType.setOrgId(sampleTypeTemplate.getOrgId());

        List<DtoSampleType2Test> sampleType2TestList = new ArrayList<>();
        List<String> testIds = sampleTypeTemplate.getTestList().stream().map(DtoTest::getId).collect(Collectors.toList());
        List<DtoTestExpand> testExpandList = testExpandRepository.findByTestIdIn(testIds);
        for (DtoTest test : sampleTypeTemplate.getTestList()) {
            DtoSampleType2Test s2t = new DtoSampleType2Test();
            String id = test.getId();
            s2t.setSampleTypeId(sampleTypeId);
            s2t.setTestId(id);
            Optional<DtoTestExpand> expandOptional = testExpandList.stream().filter(p -> test.getId().equals(p.getTestId())
                    && p.getSampleTypeId().equals(sampleTypeTemplate.getParentId())).findFirst();
            if (expandOptional.isPresent()) {
                s2t.setTimesOrder(expandOptional.get().getTimesOrder());
                s2t.setSamplePeriod(expandOptional.get().getSamplePeriod());
            } else {
                s2t.setTimesOrder(test.getTimesOrder());
                s2t.setSamplePeriod(test.getSamplePeriod());
            }
            sampleType2TestList.add(s2t);
        }
        sampleType2TestService.save(sampleType2TestList);
        sampleTypeService.save(sampleType);

        return sampleTypeTemplate;
    }

    @Override
    @Transactional
    public void deleteTest(String sampleTypeId, List<String> testIds) {
        repository.deleteBySampleTypeIdAndTestIdIn(sampleTypeId, testIds);
    }

    /**
     * 检测模板更新
     */
    @Transactional
    @Override
    public DtoSampleTypeTemplate updateTemplate(DtoSampleTypeTemplate sampleTypeTemplate) {

        String sampleTypeId = sampleTypeTemplate.getId();

        DtoSampleType sampleType = sampleTypeService.findOne(sampleTypeId);
        sampleType.setTypeName(sampleTypeTemplate.getTypeName());
        sampleType.setRemark(sampleTypeTemplate.getRemark());
        sampleType.setOrderNum(sampleTypeTemplate.getOrderNum());

        List<DtoSampleType2Test> sampleType2TestList = repository.findBySampleTypeId(sampleTypeId);
        List<String> existTestIds = sampleType2TestList.stream().map(DtoSampleType2Test::getTestId).collect(Collectors.toList());
        if(sampleTypeTemplate.getTestIds()==null){
            sampleTypeTemplate.setTestIds(new ArrayList<>());
        }
        sampleTypeTemplate.getTestIds().removeAll(existTestIds);

        if (StringUtil.isNotEmpty(sampleTypeTemplate.getTestIds())) {
            List<DtoTest> addTests = testService.findAll(sampleTypeTemplate.getTestIds());
            List<DtoSampleType2Test> saveTestList = new ArrayList<>();
            addTests.sort(Comparator.comparing(DtoTest::getIsTotalTest, Comparator.reverseOrder()));
            List<DtoTestExpand> testExpandList = testExpandRepository.findByTestIdIn(sampleTypeTemplate.getTestIds());
            for (DtoTest add : addTests) {
                DtoSampleType2Test save = new DtoSampleType2Test();
                save.setSampleTypeId(sampleTypeId);
                save.setTestId(add.getId());
                Optional<DtoTestExpand> expandOptional = testExpandList.stream().filter(p -> add.getId().equals(p.getTestId())
                        && p.getSampleTypeId().equals(sampleTypeTemplate.getParentId())).findFirst();
                if (expandOptional.isPresent()) {
                    save.setTimesOrder(expandOptional.get().getTimesOrder());
                    save.setSamplePeriod(expandOptional.get().getSamplePeriod());
                } else {
                    save.setTimesOrder(add.getTimesOrder());
                    save.setSamplePeriod(add.getSamplePeriod());
                }
                if (add.getIsTotalTest()) {
                    List<DtoTest> childTests = testRepository.findByParentIdIn(Collections.singletonList(add.getId()));
                    childTests.removeIf(c -> existTestIds.contains(c.getId()));
                    for (DtoTest childTest : childTests) {
                        Optional<DtoSampleType2Test> s2tOp = sampleType2TestList.stream().filter(t -> t.getTestId().equals(childTest.getId())).findFirst();
                        if (s2tOp.isPresent()) {
                            DtoSampleType2Test s2t = s2tOp.get();
                            s2t.setParentId(save.getId());
                            s2t.setTimesOrder(childTest.getTimesOrder());
                            s2t.setSamplePeriod(childTest.getSamplePeriod());
                            saveTestList.add(s2t);
                        } else {
                            DtoSampleType2Test saveChild = new DtoSampleType2Test();
                            saveChild.setSampleTypeId(sampleTypeId);
                            saveChild.setTestId(childTest.getId());
                            saveChild.setParentId(save.getId());
                            saveChild.setTimesOrder(childTest.getTimesOrder());
                            saveChild.setSamplePeriod(childTest.getSamplePeriod());
                            saveTestList.add(saveChild);
                            existTestIds.add(childTest.getId());
                        }
                    }
                }
                if (StringUtils.isNotNullAndEmpty(add.getParentId()) && !UUIDHelper.GUID_EMPTY.equals(add.getParentId())) {
                    DtoSampleType2Test s2tParent = sampleType2TestList.stream().filter(s -> s.getTestId().equals(add.getParentId())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(s2tParent)) {
                        save.setParentId(s2tParent.getId());
                    } else {
                        s2tParent = saveTestList.stream().filter(s -> s.getTestId().equals(add.getParentId())).findFirst().orElse(null);
                        if (StringUtil.isNotNull(s2tParent)) {
                            save.setParentId(s2tParent.getId());
                        }
                    }
                }
                saveTestList.add(save);
            }
            if (StringUtil.isNotEmpty(saveTestList)) {
                repository.save(saveTestList);
            }
        }
        sampleTypeService.update(sampleType);
        return sampleTypeTemplate;
    }

    /**
     * 修改样品模板测试项目频次及样品数
     *
     * @param testList 模板测试项目
     */
    @Transactional
    @Override
    public void updateTestMsg(List<DtoSampleType2Test> testList) {
        List<String> testIds = testList.stream().map(DtoSampleType2Test::getId).collect(Collectors.toList());
        List<DtoSampleType2Test> type2TestList = sampleType2TestService.findAll(testIds);
        for (DtoSampleType2Test type2Test : type2TestList) {
            Optional<DtoSampleType2Test> test = testList.stream().filter(p -> p.getId().equals(type2Test.getId())).findFirst();
            test.ifPresent(p -> {
                type2Test.setTimesOrder(p.getTimesOrder());
                type2Test.setSamplePeriod(p.getSamplePeriod());
            });
        }
        sampleType2TestService.save(type2TestList);
    }

    /**
     * 对测试项目进行校验，找出同分析项目，不同分析方法的分析方法名称
     *
     * @param testList 测试项目列表
     * @return 同分析项目，不同分析方法的分析方法名称集合
     */
    private Set<String> getDupItemNames(List<DtoTest> testList) {
        Map<String, List<DtoTest>> testMap = testList.stream().collect(Collectors.groupingBy(DtoTest::getAnalyzeItemId));
        Set<String> dupTestNameSet = new HashSet<>();
        for (Map.Entry<String, List<DtoTest>> entry : testMap.entrySet()) {
            if (entry.getValue().stream().map(DtoTest::getAnalyzeMethodId).distinct().count() > 1) {
                dupTestNameSet.add(entry.getValue().get(0).getRedAnalyzeItemName());
            }
        }
        return dupTestNameSet;
    }

    /**
     * 获取检测类型集合下的模板明细（有测试项目的模板才会筛选出来）
     *
     * @param sampleTypeIds 检测类型id集合
     * @return 模板
     */
    @Override
    public List<DtoSampleTypeTemplate> findTemplateBySampleTypeIdIn(Collection<String> sampleTypeIds) {

        StringBuilder sql = new StringBuilder("select s2t,st from DtoSampleType2Test s2t,DtoSampleType st");
        sql.append(" where s2t.sampleTypeId = st.id");
        sql.append(" and st.category = :category");
        sql.append(" and st.parentId in :parentIds");
        Map<String, Object> value = new HashMap<>();
        value.put("category", EnumBase.EnumSampleTypeCategory.模板.getValue());
        value.put("parentIds", sampleTypeIds);
        List list = comRepository.find(sql.toString(), value);
        Set<DtoSampleType> stSet = new HashSet<>();
        List<DtoSampleType2Test> newDatas = new ArrayList<>();
        Iterator iterator = list.iterator();
        while (iterator.hasNext()) {
            Object obj = iterator.next();
            Object[] objs = (Object[]) obj;
            DtoSampleType2Test s2t = (DtoSampleType2Test) objs[0];
            newDatas.add(s2t);

            DtoSampleType st = (DtoSampleType) objs[1];
            stSet.add(st);
        }

        List<String> testIds = newDatas.stream().map(DtoSampleType2Test::getTestId).collect(Collectors.toList());
        List<DtoTest> testList = testIds.size() > 0 ? testService.findRedisByIds(testIds) : new ArrayList<>();

        List<DtoSampleTypeTemplate> templateList = new ArrayList<>();
        for (DtoSampleType st : stSet) {
            DtoSampleTypeTemplate template = new DtoSampleTypeTemplate();
            template.setId(st.getId());
            template.setTypeName(st.getTypeName());
            List<String> thisTestIds = newDatas.stream().filter(p -> p.getSampleTypeId().equals(st.getId())).map(DtoSampleType2Test::getTestId).collect(Collectors.toList());

            template.setTestList(testList.stream().filter(p -> thisTestIds.contains(p.getId())).collect(Collectors.toList()));
            templateList.add(template);
        }
        return templateList;
    }

    @Override
    public DtoSampleType copyTemplate(String id) {
        DtoSampleType copySampleType = new DtoSampleType();
        DtoSampleType sampleType = sampleTypeService.findOne(id);
        BeanUtils.copyProperties(sampleType, copySampleType);
        copySampleType.setTypeName(sampleType.getTypeName() + "【复制】");
        copySampleType.setId(UUIDHelper.NewID());
        List<DtoSampleType2Test> sampleType2TestList = repository.findBySampleTypeId(sampleType.getId());
        List<DtoSampleType2Test> copySampleType2TestList = new ArrayList<>();
        // 用于存储 parentId 不为空的数据
        Map<String, List<String>> map = new HashMap<>();
        for (DtoSampleType2Test sampleType2Test : sampleType2TestList) {
            DtoSampleType2Test copy = new DtoSampleType2Test();
            BeanUtils.copyProperties(sampleType2Test, copy);
            copy.setId(UUIDHelper.NewID());
            List<String> parentTestIds = sampleType2TestList.stream().filter(p -> sampleType2Test.getId().equals(p.getParentId())).map(DtoSampleType2Test::getTestId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(parentTestIds)) {
                map.put(copy.getId(), parentTestIds);
            }
            copy.setSampleTypeId(copySampleType.getId());
            copySampleType2TestList.add(copy);
        }
        if (StringUtil.isNotEmpty(map.keySet())) {
            for (String key : map.keySet()) {
                List<String> parentTestIds = map.get(key);
                for (DtoSampleType2Test dtoSampleType2Test : copySampleType2TestList) {
                    if (parentTestIds.contains(dtoSampleType2Test.getTestId())) {
                        dtoSampleType2Test.setParentId(key);
                    }
                }
            }
        }
        if (StringUtil.isNotNull(copySampleType)) {
            sampleTypeService.save(copySampleType);
        }
        if (StringUtil.isNotEmpty(copySampleType2TestList)) {
            sampleType2TestService.save(copySampleType2TestList);
        }
        return copySampleType;
    }

    @Override
    public void findSampleTypeByPage(PageBean<DtoSampleType> page, BaseCriteria baseCriteria) {
        sampleTypeService.findByPage(page, baseCriteria);
        List<DtoSampleType> sampleTypeList = page.getData();
        List<String> sampleTypeIds = sampleTypeList.stream().map(DtoSampleType::getId).collect(Collectors.toList());
        List<DtoSampleType2Test> sampleType2Tests = repository.findBySampleTypeIdIn(sampleTypeIds);
        Map<String, List<DtoSampleType2Test>> sampleType2TestGroupMap = sampleType2Tests.stream().collect(Collectors.groupingBy(DtoSampleType2Test::getSampleTypeId));
        List<String> testIds = sampleType2Tests.stream().filter(p -> p.getParentId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType2Test::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testService.findAllDeleted(testIds) : new ArrayList<>();
        List<String> analyzeMethodIds = testList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
        List<DtoAnalyzeMethod> analyzeMethodList = StringUtil.isNotEmpty(analyzeMethodIds) ?
                analyzeMethodRepository.findAllDeleted(analyzeMethodIds) : new ArrayList<>();

        for (DtoSampleType sampleType : sampleTypeList) {
            // 筛选计划内数据，判定是否需要提示
            List<DtoSampleType2Test> sampleType2TestList = sampleType2TestGroupMap.get(sampleType.getId());
            if (StringUtil.isNotEmpty(sampleType2TestList)) {
                List<String> thisTestIds = sampleType2TestList.stream().filter(p -> UUIDHelper.GUID_EMPTY.equals(p.getParentId())).map(DtoSampleType2Test::getTestId).collect(Collectors.toList());
                List<DtoTest> thisTestList = testList.parallelStream().filter(t -> thisTestIds.contains(t.getId())).collect(Collectors.toList());
                List<String> methodIds = thisTestList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
                List<DtoAnalyzeMethod> analyzeMethods = analyzeMethodList.stream().filter(p -> methodIds.contains(p.getId())).collect(Collectors.toList());
                // 当点位关联测试项目状态，存在“停用”、“作废”、“删除”的测试项目时，需要测试项目提示
                if (analyzeMethods.stream().anyMatch(p -> EnumLIM.EnumAnalyzeMethodStatus.停用.getValue().equals(p.getStatus())
                        || EnumLIM.EnumAnalyzeMethodStatus.废止.getValue().equals(p.getStatus()) || p.getIsDeleted()) ||
                        thisTestList.stream().anyMatch(DtoTest::getIsDeleted)) {
                    sampleType.setIsTestTip(true);
                }
                sampleType.setAnalyzeItemNames(thisTestList.stream().map(DtoTest::getRedAnalyzeItemName).collect(Collectors.joining("、")));
            }
        }
    }

    @Override
    @Transactional
    public DtoSampleTypeTemplate updateFrequencyByConfig(String sampleTypeId) {
        //模板
        DtoSampleType template = sampleTypeService.findOne(sampleTypeId);
        //模板对应检测类型
        DtoSampleType templateSampleType = sampleTypeService.findOne(template.getParentId());
        boolean isSmallType = StringUtil.isNotEmpty(templateSampleType.getParentId()) && !UUIDHelper.GUID_EMPTY.equals(templateSampleType.getParentId());
        //模板内的测试项目
        List<DtoSampleType2Test> sampleType2Tests = repository.findBySampleTypeId(sampleTypeId);
        List<String> testIds = sampleType2Tests.stream().filter(p -> p.getParentId().equals(UUIDHelper.GUID_EMPTY)).map(DtoSampleType2Test::getTestId).distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(testIds)) {
            List<DtoSampleType2Test> waitSaveList = new ArrayList<>();
            List<DtoTest> testList = testService.findAll(testIds);
            List<DtoTestExpand> testExpandList = testExpandRepository.findByTestIdIn(testIds);
            for (DtoSampleType2Test type2Test : sampleType2Tests) {
                //只有在创建模板时选择了小类才去匹配小类中设置的批次样次，如果小类没有配则匹配默认配置
                if (isSmallType) {
                    DtoTestExpand testExpand = testExpandList.stream().filter(v -> type2Test.getTestId().equals(v.getTestId()) && templateSampleType.getId().equals(v.getSampleTypeId()))
                            .findFirst().orElse(null);
                    if (testExpand != null) {
                        type2Test.setSamplePeriod(testExpand.getSamplePeriod());
                        type2Test.setTimesOrder(testExpand.getTimesOrder());
                        waitSaveList.add(type2Test);
                        continue;
                    }
                }
                //如果创建模板选的是大类就直接匹配默认配置
                DtoTest test = testList.stream().filter(v -> type2Test.getTestId().equals(v.getId())).findFirst().orElse(null);
                if (test != null) {
                    type2Test.setSamplePeriod(test.getSamplePeriod());
                    type2Test.setTimesOrder(test.getTimesOrder());
                    waitSaveList.add(type2Test);
                }
            }
            repository.save(waitSaveList);
        }
        return getSampleTypeById(sampleTypeId);
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }

    @Autowired
    public void setTestExpandRepository(TestExpandRepository testExpandRepository) {
        this.testExpandRepository = testExpandRepository;
    }
}