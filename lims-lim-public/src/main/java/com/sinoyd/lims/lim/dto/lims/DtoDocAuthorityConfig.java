package com.sinoyd.lims.lim.dto.lims;


import com.sinoyd.lims.lim.entity.DocAuthorityConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * DtoDocAuthorityConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/3
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_DocAuthorityConfig")
@Data
@DynamicInsert
public class DtoDocAuthorityConfig extends DocAuthorityConfig {

    /**
     * 人数
     */
    @Transient
    private Integer userNums;
}
