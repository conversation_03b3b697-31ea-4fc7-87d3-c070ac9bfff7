package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoArrange2Method;

import java.util.List;

/**
 * 采样方法仓储
 *
 * <AUTHOR>
 * @version V1.0.0 2024/7/25
 * @since V100R001
 */
public interface Arrange2MethodRepository extends IBaseJpaPhysicalDeleteRepository<DtoArrange2Method, String> {

    /**
     * 根据采样计划ID查询
     *
     * @param samplingPlanId 采样计划ID
     * @return  DtoArrange2Method
     */
    List<DtoArrange2Method> findBySamplingPlanId(String samplingPlanId);

}
