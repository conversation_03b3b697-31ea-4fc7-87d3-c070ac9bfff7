package com.sinoyd.lims.lim.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;


/**
 * InstrumentUseRecord2Sample实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="InstrumentUseRecord2Sample")
 @Data
 public  class InstrumentUseRecord2Sample implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 仪器使用信息id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("仪器使用信息id")
	private String instrumentUseRecordId;
    
    /**
    * 样品id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("样品id")
	private String sampleId;
    
 }