package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.pro.dto.DtoSampleDispose;
import com.sinoyd.lims.pro.dto.DtoSampleReserve;

import java.util.List;

/**
 * SampleReserveRepository数据访问操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/20
 * @since V100R001
 */
public interface SampleReserveGroupRepository extends IBaseJpaRepository<DtoSampleReserve, String> {

    /**
     * 根据样品Id和分组id查询数据
     *
     * @param sampleGroupIds 样品分组主键id
     * @return 查询结果
     */
    List<DtoSampleReserve> findBySampleGroupIdIn(List<String> sampleGroupIds);

    /**
     * 根据样品Id和分组id查询数据
     *
     * @param sampleGroupId 样品分组主键id
     * @return 查询结果
     */
    List<DtoSampleReserve> findBySampleGroupId(String sampleGroupId);
}
