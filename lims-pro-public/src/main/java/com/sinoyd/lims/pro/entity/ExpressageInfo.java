package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;


/**
 * ExpressageInfo实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="ExpressageInfo")
 @Data
 public  class ExpressageInfo implements BaseEntity,Serializable {

    public  ExpressageInfo() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("项目id")
	private String projectId;
    
    /**
    * 报告id（冗余，可能会精确到报告）
    */
    @Column(length=50)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("报告id（冗余，可能会精确到报告）")
	private String reportId;
    
    /**
    * 收件单位
    */
    @Column(length=100)
    @ApiModelProperty("收件单位")
	private String addressee;
    
    /**
    * 收件人
    */
    @Column(length=50)
    @ApiModelProperty("收件人")
	private String recipients;
    
    /**
    * 收件人电话
    */
    @Column(length=50)
    @ApiModelProperty("收件人电话")
	private String recipientsPhone;
    
    /**
    * 收件地址
    */
    @ApiModelProperty("收件地址")
	private String consigneeAddress;
    
    /**
    * 快递公司
    */
    @Column(length=100)
    @ApiModelProperty("快递公司")
	private String expressCompany;
    
    /**
    * 快递单号
    */
    @Column(length=50)
    @ApiModelProperty("快递单号")
	private String expressNumber;
    
    /**
    * 寄件人
    */
    @Column(length=50)
    @ApiModelProperty("寄件人")
	private String sender;
    
    /**
    * 寄件日期
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @ApiModelProperty("寄件日期")
	private Date sendDate;
    
    /**
    * 备注
    */
    @Column(length=200)
    @ApiModelProperty("备注")
	private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }