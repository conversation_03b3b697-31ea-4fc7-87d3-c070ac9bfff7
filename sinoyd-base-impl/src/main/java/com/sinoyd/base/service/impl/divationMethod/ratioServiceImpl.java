package com.sinoyd.base.service.impl.divationMethod;

import com.sinoyd.SpringContextAware;
import com.sinoyd.base.dto.rcc.DtoQualityControlLimit;
import com.sinoyd.base.service.QualityDivationService;
import com.sinoyd.base.utils.base.DivationUtils;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.CalculationService;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 比值
 * <AUTHOR>
 * @version V1.0.0 2025/4/25
 * @since V100R001
 */
public class ratioServiceImpl implements QualityDivationService {
    @Override
    public void deviationValue(DtoQualityControlLimit controlLimit, List<String> valueList, Map<String, Object> map) {
        String retStr = "";
        if (valueList.size() > 1&&map.get("deviationFormula")!=null) {
            //样值
            String standardValue = valueList.get(0);
            //检查项值
            String sampleValue = valueList.get(1);
            //计算公式
            String deviationFormula = (String) map.get("deviationFormula");
            if (DivationUtils.isNumber(standardValue) && DivationUtils.isNumber(sampleValue)
                    && StringUtil.isNotEmpty(deviationFormula)&&!BigDecimal.ZERO.equals(new BigDecimal(sampleValue))) {
                CalculationService calculationService = SpringContextAware.getBean(CalculationService.class);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("a", new BigDecimal(standardValue));
                paramMap.put("b", new BigDecimal(sampleValue).multiply(new BigDecimal("100")));
                retStr = calculationService.calculationExpression(deviationFormula, paramMap).toString();
            }
        }
        map.put("qcRate", retStr);
    }
}
