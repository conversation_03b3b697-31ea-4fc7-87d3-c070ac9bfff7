package com.sinoyd.lims.od.dto;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.od.entity.OdPersonAnswer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * OdPersonAnswer实体
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Ods_OdPersonAnswer")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoOdPersonAnswer extends OdPersonAnswer {

    /**
     * 嗅辨次序流水
     */
    @Transient
    private String personSn;

    /**
     * 嗅辨员名称
     */
    @Transient
    private String personName;

    /**
     * 任务类型
     */
    @Transient
    private Integer taskType;

    /**
     * 任务id
     */
    @Transient
    private String taskId;


    /**
     * 样品编号
     */
    @Transient
    private String sampleCode;

    /**
     * 嗅辨日期
     */
    @Transient
    private Date odData;

    /**
     * 配气员名称
     */
    @Transient
    private String gasMixerName;


    /**
     * 实验组
     */
    @Transient
    private Integer groupSn;

    /**
     * 实验组字符换
     */
    @Transient
    private String groupSnStr;

    /**
     * 实验次序
     */
    @Transient
    private Integer seqSn;

    /**
     * 实验次序字符串
     */
    @Transient
    private String seqSnStr;

    /**
     * 稀释倍数
     */
    @Transient
    private Integer dilutionRate;


    public DtoOdPersonAnswer() {
    }

    public DtoOdPersonAnswer(String id, String personId, String labSeqId, String sampleCode,
                             String taskId, Integer groupSn, Integer seqSn, Boolean isCompleted,
                             String personAnswer, Integer confidenceLvl) {
        setId(id);
        setPersonId(personId);
        setLabSeqId(labSeqId);
        setTaskId(taskId);
        setSampleCode(sampleCode);
        setGroupSn(groupSn);
        setSeqSn(seqSn);
        setIsCompleted(isCompleted);
        setPersonAnswer(personAnswer);
        setConfidenceLvl(confidenceLvl);
        setGroupSnStr(String.format("实验%s", groupSn));
        setSeqSnStr(String.format("第%s轮", seqSn));
    }
}
