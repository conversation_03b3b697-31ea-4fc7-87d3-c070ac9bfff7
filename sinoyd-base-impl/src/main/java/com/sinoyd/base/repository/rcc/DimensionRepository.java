package com.sinoyd.base.repository.rcc;

import java.util.Collection;
import java.util.List;

import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.frame.repository.IBaseJpaRepository;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * 量纲仓储
 * <AUTHOR>
 * @version V1.0.0 2019/5/13
 * @since V100R001
 */
public interface DimensionRepository extends IBaseJpaRepository<DtoDimension, String> {

    /**
     * 重写获取所有的方法
     *
     * @return 返回未删除量纲信息
     */
    @Override
    @Query("select p from DtoDimension p where p.isDeleted = 0")
    List<DtoDimension> findAll();

    /**
     * 根据id数组获取量纲信息
     *
     * @param ids 分析项目ids
     * @return 返回量纲信息
     */
    @Query("select d from DtoDimension d where d.isDeleted = 0 and d.id in :ids")
    @Override
    List<DtoDimension> findAll(@Param("ids") Iterable<String> ids);

    /**
     * 根据名称与id获取重复的条数
     * @param id
     * @param dimensionName
     * @return
     */
    @Query("select count(d) from DtoDimension d where d.id <> :id and d.dimensionName = :dimensionName and d.isDeleted = 0")
    Integer getCountByName(@Param("id") String id,@Param("dimensionName") String dimensionName);

    /**
     * 根据id和名称查询记录数
     *
     * @param id 主键
     * @param dimensionName 量纲名称
     * @return 记录数
     */
    Integer countByIdNotAndDimensionNameAndIsDeletedFalse(String id, String dimensionName);

    /**
     * 根据量纲名称获取量纲
     * 
     * @param dimensionName
     * @return 量纲实体
     */
    @Query("select d from DtoDimension d where d.dimensionName = :dimensionName and d.isDeleted = 0")
    DtoDimension findByDimensionName(@Param("dimensionName") String dimensionName);

    /**
     * 根据量纲名称获取量纲
     *
     * @param dimensionName
     * @return 量纲实体
     */
    @Query("select d from DtoDimension d where d.dimensionName in :dimensionName and d.isDeleted = 0")
    List<DtoDimension> findByDimensionNameIn(@Param("dimensionName") List<String> dimensionName);

    /**
     * 返回所有的带假删的信息
     *
     * @return 返回带删除的信息
     */
    @Query("select p from DtoDimension p")
    List<DtoDimension> findAllDeleted();


    /**
     * 返回所有的带假删的量纲信息
     *
     * @param ids 主键的ids
     * @return 返回带删除的量纲信息
     */
    @Query("select p from DtoDimension p where p.id in :ids")
    List<DtoDimension> findAllDeleted(@Param("ids") List<String> ids);

    /**
     * 排序自增
     *
     * @param idList 量纲id
     * @return 返回相应的更改条数
     */
    @Modifying
    @Transactional
    @Query("update DtoDimension d set d.orderNum = d.orderNum + 1 where d.id in :idList")
    Integer incrementOrderNum(@Param("idList") Collection<String> idList);
}