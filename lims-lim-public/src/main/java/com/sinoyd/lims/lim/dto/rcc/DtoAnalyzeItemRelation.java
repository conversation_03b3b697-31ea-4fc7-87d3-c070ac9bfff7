package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.lims.lim.entity.AnalyzeItemRelation;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import java.util.List;


/**
 * DtoAnalyzeItemRelation实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/5/9
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_AnalyzeItemRelation")
@Data
@DynamicInsert
public class DtoAnalyzeItemRelation extends AnalyzeItemRelation {

    @Transient
    private List<String> analyzeItemIds;

    @Transient
    private List<DtoAnalyzeItem> analyzeItemList;
}