package com.sinoyd.lims.instrument.parse.repository;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.instrument.parse.dto.DtoTcpData;

import java.util.List;

/**
 * WWTCP数据DB访问接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/26
 */
public interface TcpDataRepository extends IBaseJpaRepository<DtoTcpData, String> {

    /**
     * 根据任务编号，采样单号获取tcp数据
     *
     * @param taskId   任务编号
     * @param sampleId 采样单号
     * @return tcp数据
     */
    List<DtoTcpData> findByWwTaskIdAndWwSampleId(String taskId, String sampleId);
}
