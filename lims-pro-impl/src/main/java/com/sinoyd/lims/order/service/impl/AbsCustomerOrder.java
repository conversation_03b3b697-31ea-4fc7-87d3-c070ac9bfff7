package com.sinoyd.lims.order.service.impl;

import com.sinoyd.lims.order.service.CustomerOrderAnaService;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.vo.BaseOrderCustomerAnalyzeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

/**
 * 客户订单统计查询抽象类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/26
 */
public abstract class AbsCustomerOrder implements CustomerOrderAnaService {

    private JdbcTemplate jdbcTemplate;

    /**
     * 父类的策略方法返回单个对象
     * @param year 年份
     * @return BaseOrderCustomerAnalyzeVO
     */
    @Override
    public BaseOrderCustomerAnalyzeVO statistics(Integer year) {
        return null;
    }

    /**
     * 父类的策略方法返回list对象
     * @param year 年份
     * @return List<BaseOrderCustomerAnalyzeVO>
     */
    @Override
    public List<BaseOrderCustomerAnalyzeVO> statisticsList(Integer year) {
        return null;
    }


    /**
     * sql语句JDBC查询
     * @param sql sql语句
     * @param params 参数数组
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getCustomerOrderData(String sql,Object[] params) {

        List<Map<String, Object>> mapList = jdbcTemplate.queryForList(sql,params);
        if (mapList.isEmpty()) {
            return null;
        } else {
            return mapList;
        }
    }

    /**
     * sql语句JDBC查询
     * @param sql sql语句
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getCustomerOrderData(String sql) {

        List<Map<String, Object>> mapList = jdbcTemplate.queryForList(sql);
        if (mapList.isEmpty()) {
            return null;
        } else {
            return mapList;
        }
    }
    /**
     * 根据订单id查询订单数据
     * @param salesPersonIds 销售人员ids
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param isSignDate 是否签单数据
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getFinalQuotationSql(String salesPersonIds, String beginTime, String endTime, boolean isSignDate) {
        Object[] params;
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT t1.finalQuotation,t2.salesPersonId FROM TB_PRO_OrderQuotation t1,(");
        sb.append("SELECT id, salesPersonId FROM TB_PRO_OrderForm WHERE isDeleted = 0 and salesPersonId IN (");
        sb.append(salesPersonIds);
        if (isSignDate) {
            sb.append(") AND signDate BETWEEN ");
        } else {
            sb.append(") AND orderDate BETWEEN ");
        }

        sb.append(" ? ");
        sb.append(" and ");
        sb.append(" ? ");
        sb.append(" and ");
        if (isSignDate) {
            sb.append("grantStatus = ? ");
            params = new Object[] {beginTime,endTime,EnumPRO.EnumOrderSignStatus.已签订.getValue()};
        } else {
            sb.append("orderStatus = ? ");
            params = new Object[] {beginTime,endTime,EnumPRO.EnumOrderStatus.审核通过.getValue()};
        }
        sb.append(") t2 WHERE t1.orderId = t2.id and t1.isDeleted = 0");
        return  getCustomerOrderData(sb.toString(),params);
    }

    /**
     * 获取订单金额数据
     * @param isOrderData 是否是订单数据
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getOrderQuotationAnlSql(boolean isOrderData, String beginTime, String endTime) {
        Object[] params;
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT finalQuotation FROM TB_PRO_OrderQuotation WHERE isDeleted = 0 and orderId IN (SELECT id FROM TB_PRO_OrderForm WHERE isDeleted = 0 and");
        if (isOrderData) {
            sb.append(" orderDate BETWEEN ");
        } else {
            sb.append(" signDate BETWEEN ");
        }

        sb.append(" ? ");
        sb.append(" and ");
        sb.append(" ? ");
        sb.append(" and ");
        if (isOrderData) {
            sb.append("orderStatus = ? ");
            params = new Object[] {beginTime,endTime,EnumPRO.EnumOrderStatus.审核通过.getValue()};
        } else {
            sb.append("grantStatus = ? ");
            params = new Object[] {beginTime,endTime,EnumPRO.EnumOrderSignStatus.已签订.getValue()};
        }
        sb.append(")");
        return getCustomerOrderData(sb.toString(),params);
    }

    /**
     * 获取订单或签单的数量
     * @param isOrderData 是否是订单数据
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 订单或签单的数量
     */
    public List<Map<String, Object>> getOrderNum(boolean isOrderData, String beginTime, String endTime){
        Object[] params;
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT id FROM TB_PRO_OrderForm WHERE isDeleted = 0 and ");
        if (isOrderData) {
            sb.append(" orderDate BETWEEN ");
        } else {
            sb.append(" signDate BETWEEN ");
        }

        sb.append(" ? ");
        sb.append(" and ");
        sb.append(" ? ");
        sb.append(" and ");
        if (isOrderData) {
            sb.append("orderStatus = ? ");
            params = new Object[] {beginTime,endTime,EnumPRO.EnumOrderStatus.审核通过.getValue()};
        } else {
            sb.append("grantStatus = ? ");
            params = new Object[] {beginTime,endTime,EnumPRO.EnumOrderSignStatus.已签订.getValue()};
        }
        return getCustomerOrderData(sb.toString(),params);
    }

    @Autowired
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
}