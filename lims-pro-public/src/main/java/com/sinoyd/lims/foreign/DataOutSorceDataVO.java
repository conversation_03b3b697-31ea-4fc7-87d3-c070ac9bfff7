package com.sinoyd.lims.foreign;

import lombok.Data;

import java.util.Date;

/**
*分包信息
* <AUTHOR>
* @version V1.0.0 2023/11/7
* @since V100R001
*/
@Data
public class DataOutSorceDataVO {

    /**
     * id
     */
    private String id;

    /**
     * 数据id
     */
    private String analyseDataId;

    /**
     * 分析名称
     */
    private String analyzeMethodName;

    /**
     * 出证结果
     */
    private String testValue;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 分析日期
     */
    private Date analyzeTime;

    /**
     * 分析结束日期
     */
    private Date analyzeEndTime;

    /**
     * 分包商
     */
    private String subcontractor;

    /**
     * CMA编号
     */
    private String cmaCode;

    /**
     * 分包报告编号
     */
    private String outSourceReportCode;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 同步时间
     */
    private Date syncTime;

}