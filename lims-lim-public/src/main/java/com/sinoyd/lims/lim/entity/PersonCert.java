package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * PersonCert实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="PersonCert")
 @Data
 public  class PersonCert implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  PersonCert() { 
       this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 人员Id（Guid）
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("人员Id（Guid）")
	private String personId;
    
    /**
    * 证书名称
    */
    @Column(length=100,nullable=false)
    @ApiModelProperty("证书名称")
    @Length(message = "证书名称{validation.message.length}", max = 100)
	private String certName;
    
    /**
    * 证书编号
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("证书编号")
    @Length(message = "证书编号{validation.message.length}", max = 50)
	private String certCode;
    
    /**
    * 发证日期
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("发证日期")
    private Date issueCertTime;
    
    /**
    * 有效期至
    */
    @Column(nullable=false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("有效期至")
    private Date certEffectiveTime;
    
    /**
    * 证书类型（枚举EnumCertType：1.水 2.气 3.声 4.土 5.其他    多选用","隔开）
    */
    @Column(length=100,nullable=false)
    @ColumnDefault("'-1'")
    @ApiModelProperty("证书类型（枚举EnumCertType：1.水 2.气 3.声 4.土 5.其他    多选用 ,隔开）")
    @Length(message = "证书类型{validation.message.length}", max = 100)
	private String certType;
    
    /**
    * 图片存放位置
    */
    @Column(length=500)
    @ApiModelProperty("图片存放位置")
    @Length(message = "图片存放位置{validation.message.length}", max = 500)
    private String phoneUrl;
    
    /**
    * 发证机关（预留，3.2）
    */
    @Column(length=100)
    @ApiModelProperty("发证机关（预留，3.2）")
    @Length(message = "发证机关{validation.message.length}", max = 100)
    private String issuingAuthority;
    
    /**
    * 备注（预留，3.2）
    */
    @Column(length=1000)
    @ApiModelProperty("备注（预留，3.2）")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
 }