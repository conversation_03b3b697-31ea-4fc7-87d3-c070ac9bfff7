package com.sinoyd.lims.pro.service;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.vo.AnalyzeDetailDataVO;

import java.util.List;

/**
 * 分析及时率列表数据查询业务接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/1/31
 */
public interface AnalyzeTimelinessDataService {

    /**
     * 及时率列表查询
     *
     * @param criteria 查询条件
     * @return 及时率结果
     */
    List<Object> queryTimeliness(BaseCriteria criteria);

    /**
     * 数据详情列表查询
     *
     * @param pb       分页条件
     * @param criteria 查询条件
     */
    void queryDetailData(PageBean<AnalyzeDetailDataVO> pb, BaseCriteria criteria, RestResponse response);
}