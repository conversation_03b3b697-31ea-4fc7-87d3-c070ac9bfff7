package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.InstrumentGatherParams;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * InstrumentGatherParams实体
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_InstrumentGatherParams")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoInstrumentGatherParams extends InstrumentGatherParams {

    public DtoInstrumentGatherParams() {
    }


    /**
     * 构造参数构造器
     *
     * @param paramName 参数名称
     * @param orderNum  排序值
     */
    public DtoInstrumentGatherParams(String paramName, Integer orderNum) {
        this.setParamName(paramName);
        this.setOrderNum(orderNum);
    }
}