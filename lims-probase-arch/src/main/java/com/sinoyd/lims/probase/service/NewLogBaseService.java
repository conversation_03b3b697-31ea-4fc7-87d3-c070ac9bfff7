package com.sinoyd.lims.probase.service;

import com.sinoyd.lims.pro.dto.customer.DtoLog;

import java.util.List;

public interface NewLogBaseService {

    /**
     * 新增日志
     * @param dtoLog
     */
    void createLog(DtoLog dtoLog);

    /**
     * 批量创建同一种类型的日志
     * @param logs 日志
     * @param logType 日志类型
     */
    void  createLog(List<DtoLog> logs, Integer logType);

    /**
     * 新增日志
     * @param objectId 对象id
     * @param comment 说明
     * @param opinion 意见（评审意见等）
     * @param logType 日志类型（如项目的方案、合同，样品的信息、检测项目）
     * @param objectType 对象类型（检测单、项目、数据等）
     * @param operateInfo 操作类型（新建、保存、修改等）
     * @param operatorId 操作人Id
     * @param operatorName 操作人
     * @param nextOperatorId 下一步操作人Id
     * @param nextOperatorName 下一步操作人
     */
    void createLog(String objectId, String comment, String opinion, int logType, int objectType, String operateInfo, String operatorId, String operatorName, String nextOperatorId, String nextOperatorName);
}
