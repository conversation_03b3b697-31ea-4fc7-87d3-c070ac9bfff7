package com.sinoyd.base.service.impl;

import com.google.common.collect.Maps;
import com.jsoniter.JsonIterator;
import com.sinoyd.base.criteria.WorkflowCriteria;
import com.sinoyd.base.dto.customer.DtoWorkflow;
import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.base.service.WorkflowService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.boot.workflow.activiti.dto.ActPageDTO;
import com.sinoyd.boot.workflow.activiti.dto.ActTaskDTO;
import com.sinoyd.boot.workflow.activiti.service.IActProcessService;
import com.sinoyd.boot.workflow.activiti.service.IActTaskService;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * WorkflowServiceImpl 工作流的接口的实现
 * <AUTHOR>
 * @version V1.0.0 2019/11/01
 * @since V100R001
 */
@Slf4j
@Service
public class WorkflowServiceImpl implements WorkflowService {

    @Autowired
    private IActProcessService actProcessService;

    @Autowired
    private IActTaskService actTaskService;

    @Autowired
    private CommonRepository commonRepository;


    @Override
    public void findByPage(PageBean<DtoWorkflow> pageBean, BaseCriteria baseCriteria) {
        WorkflowCriteria workflowCriteria = (WorkflowCriteria) baseCriteria;
        ActPageDTO actPageDTO = new ActPageDTO(pageBean.getPageNo(), pageBean.getRowsPerPage());
        List<ProcessDefinition> processDefinitionList = actProcessService.processList(actPageDTO, workflowCriteria.getActProcessDTO());
        List<DtoWorkflow> workflowList = processDefinitionList.stream().map(DtoWorkflow::new).collect(Collectors.toList());
        pageBean.setData(workflowList);
        pageBean.setRowsCount(actPageDTO.getTotal());
    }


    @Transactional
    @Override
    public String submitSign(DtoWorkflowSign dtoWorkflowSign) throws Exception {
        String status = "";
        List<String> objectIds = dtoWorkflowSign.getObjectIds();
        for (String objectId : objectIds) {
            ProcessInstance processInstance = actProcessService.getProcessInstance(objectId);
            if (StringUtil.isNotNull(processInstance)) {
                status = submit(processInstance, objectId, dtoWorkflowSign.getSignal(), dtoWorkflowSign.getOption(), dtoWorkflowSign.getNextOperatorId(),dtoWorkflowSign.getIsAutoStatus());
            } else if (StringUtil.isNotNull(dtoWorkflowSign.getIsActivate())
                    && dtoWorkflowSign.getIsActivate()) { //如果是自动激活的
                ProcessInstance procInst = activateInstance(objectId);
                status = submit(procInst, objectId, dtoWorkflowSign.getSignal(), dtoWorkflowSign.getOption(), dtoWorkflowSign.getNextOperatorId(),dtoWorkflowSign.getIsAutoStatus());
            }
        }
        return status;
    }

    @Override
    @Transactional
    public void createInstance(String workflowCode, String objectId) {
        // 启动业务流程
        ProcessInstance procInst = actProcessService.startProcess(workflowCode, objectId);
        // 提交业务流程到新建状态
        actTaskService.completeCurrentTask(procInst.getActivityId());
    }

    @Override
    public Map<String,Object> getCurrentTask(String procInstId) {
        TaskEntity task = actTaskService.getCurrentTaskEntity(procInstId);
        Map<String, Object> map = new HashMap<>();
        String taskId = "";
        Map<String, Object> configMap = new HashMap<>();
        String processInstanceId = "";
        if (StringUtil.isNotNull(task)) {
            taskId = task.getId();
            String config = task.getDescription();
            processInstanceId = task.getProcessInstanceId();
            if (StringUtils.isNotNullAndEmpty(config)) {
                try {
                    configMap = JsonIterator.deserialize(config, Map.class);
                } catch (Exception ex) {
                    System.out.print(ex.getMessage());
                }
            }
        }
        //任务id
        map.put("taskId", taskId);
        //将配置信息维护到描述里面
        map.put("config", configMap);
        //流程实例id
        map.put("processInstanceId", processInstanceId);
        return map;
    }

    @Override
    public Map<String,Object> getProcessDefinition(String proDefKey) {
        ProcessDefinition processDefinition = actProcessService.getProcessDefinitionByProcDefKey(proDefKey);
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> configMap = new HashMap<>();
        if (StringUtil.isNotNull(processDefinition)) {
            String config = processDefinition.getDescription();
            if (StringUtils.isNotNullAndEmpty(config)) {
                try {
                    configMap = JsonIterator.deserialize(config, Map.class);
                } catch (Exception ex) {
                    System.out.print(ex.getMessage());
                }
            }
        }
        //将配置信息维护到描述里面
        /*"config": {
            "nextIsSponsor": "下一步是否发起人员操作，如果为true，无须选择人，直接提交的时候将发起人sponsorId 传入即可",
                    "authority": "权限编码，如果nextIsSponsor为fale,启用权限编码，并且判断是否为空，如果不为空将编码传入人员权限的接口中，否则默认所有人",
                    "step": "步骤，first 标识第一步，next标识中间步骤,end 标识最后一步，如果是最后一步不需要选择人员直接提交",
                    "taskSign": "instrumentRepair 表示这一步需要填写仪器维修记录,consumableStorage 消耗品入库，instrumentStorage 仪器入库"
        },*/
        map.put("config", configMap);
        return map;
    }

    @Override
    @Transactional
    public  void  endInstance(String objectId,String option) {
        ProcessInstance processInstance = actProcessService.getProcessInstance(objectId);
        if (StringUtil.isNotNull(processInstance)) {
            actProcessService.deleteProcIns(processInstance.getId(), option);
        }
    }

    /**
     * 重新激活实例
     *
     * @param objectId 对象id
     * @return 返回激活的实例
     */
    private ProcessInstance activateInstance(String objectId) {
        Map<String, Object> vars = new HashMap<>();
        ProcessInstance procInst = actProcessService.restartHistoricProcessDeleteHistoric(objectId, vars);
        // 提交业务流程到新建状态
        actTaskService.completeCurrentTask(procInst.getActivityId());
        return procInst;
    }


    /**
     * 提交的通用方法
     *
     * @param processInstance 流程实例
     * @param objectId        对象id
     * @param sign            信号值
     * @param option          已经
     * @param nextOperatorId  下一步操作人
     * @return 返回想要的状态数据
     * @throws Exception
     */
    private String submit(ProcessInstance processInstance,
                          String objectId, String sign, String option, String nextOperatorId,Boolean isAutoStatus) throws Exception {
        String status = "";
        ActTaskDTO actTask = new ActTaskDTO();
        Task task = actTaskService.getCurrentTask(processInstance.getId());
        actTask.setTaskId(task.getId());
        actTask.setComment(option);
        actTask.setProcInsId(processInstance.getId());
        Map<String, Object> vars = StringUtil.isNull(actTask.getVars())
                ? Maps.newHashMap()
                : actTask.getVars();

        //指定信号值
        if (StringUtils.isNotNullAndEmpty(sign)) {
            vars.put("signal", sign);
        }
        //指定下一步操作人的变量
        if (StringUtils.isNotNullAndEmpty(nextOperatorId)) {
            vars.put("nextOperatorId", nextOperatorId);
        }
        actTask.setVars(vars);
        actTaskService.complete(actTask, false);
        Task currentTask = actTaskService.getCurrentTask(processInstance.getId());
        log.info("objectId：" + objectId);
        if (StringUtil.isNotNull(currentTask)) {
            status = currentTask.getName();
            log.info("status：" + status);
            if (StringUtil.isNotNull(isAutoStatus) && isAutoStatus) {
                ProcessDefinition processDefinition = actProcessService.getProcessDefinition(processInstance.getProcessDefinitionId());
                if (StringUtils.isNotNullAndEmpty(processDefinition.getDescription())) {
                    log.info("开始修改业务表的状态" + objectId);
                    //如何修改业务表里面的状态
                    Class aClass = Class.forName(processDefinition.getDescription());//用描述作为包的命名
                    Field[] fields = aClass.getSuperclass().getDeclaredFields();
                    Object object = aClass.newInstance();
                    for (Field field : fields) {
                        // 设置些属性是可以访问的
                        field.setAccessible(true);
                        if (field.getName().equals("status")) {
                            log.info("设置status属性值：" + currentTask.getName());
                            field.set(object, currentTask.getName());
                        }
                        if (field.getName().equals("id")) {
                            log.info("设置id属性值：" + objectId);
                            field.set(object, objectId);
                        }
                    }
                    BaseEntity baseEntity = (BaseEntity) object;
                    commonRepository.merge(baseEntity);
                }
            }
        }
        return status;
    }
}
