package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.criteria.RecordConfigParamsConfigCriteria;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.service.RecordConfigParamsConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 记录单配置相关的参数
 * <AUTHOR>
 * @version V1.0.0 2020/10/14
 * @since V100R001
 */
@Api(tags = "示例: RecordConfig服务")
@RestController
@RequestMapping("api/lim/recordConfigParamsConfig")
@Validated
public class RecordConfigParamsConfigController extends ExceptionHandlerController<RecordConfigParamsConfigService> {


    @ApiOperation(value = "动态查询参数数据", notes = "动态查询参数数据")
    @GetMapping
    public RestResponse<List<DtoParamsConfig>> findRecordConfigParamsConfigList(RecordConfigParamsConfigCriteria criteria) {
        RestResponse<List<DtoParamsConfig>> restResponse = new RestResponse<>();
        List<DtoParamsConfig> paramsConfigList = service.findRecordConfigParamsConfigList(criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(paramsConfigList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(paramsConfigList);
        restResponse.setCount(paramsConfigList.size());
        return restResponse;
    }

    @ApiOperation(value = "新增记录单参数", notes = "新增记录单参数")
    @PostMapping("/{paramsConfigType}")
    public RestResponse<DtoParamsConfig> create(@Validated @RequestBody DtoParamsConfig paramsConfig,
                                                @PathVariable Integer paramsConfigType) {
        RestResponse<DtoParamsConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.saveRecordConfigParams(paramsConfig, paramsConfigType));
        return restResponse;
    }

    @ApiOperation(value = "修改记录单参数", notes = "修改记录单参数")
    @PutMapping("/{paramsConfigType}")
    public RestResponse<DtoParamsConfig> update(@Validated @RequestBody DtoParamsConfig paramsConfig,
                                                @PathVariable Integer paramsConfigType) {
        RestResponse<DtoParamsConfig> restResponse = new RestResponse<>();
        restResponse.setData(service.updateRecordConfigParams(paramsConfig, paramsConfigType));
        return restResponse;
    }


    @ApiOperation(value = "删除记录单参数", notes = "删除记录单参数")
    @DeleteMapping
    public RestResponse<Integer> update(@RequestBody List<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.deleteRecordConfigParams(ids));
        return restResponse;
    }
}
