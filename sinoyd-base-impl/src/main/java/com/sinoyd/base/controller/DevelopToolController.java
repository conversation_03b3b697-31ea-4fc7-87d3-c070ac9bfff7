package com.sinoyd.base.controller;

import com.sinoyd.base.service.DevelopToolService;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 开发工具服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/11
 */
@RestController
@RequestMapping("/api/base/develop/tool")
public class DevelopToolController extends ExceptionHandlerController<DevelopToolService> {

    /**
     * 产生模型
     *
     * @param params 参数
     * @return 结果
     */
    @PostMapping("/model")
    public String generateModel(@RequestBody Map<String, String> params) {
        return service.generateApifoxObjectModel(params);
    }

    /**
     * 容器健康检查
     *
     * @return 结果
     */
    @GetMapping("/container/health")
    public RestResponse<Boolean> containerHealthCheck() {
        RestResponse<Boolean> response = new RestResponse<>();
        response.setData(true);
        return response;
    }
}