package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.customer.TreeNode;
import com.sinoyd.base.dto.lims.DtoEnterprise;
import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.repository.lims.EnterpriseRepository;
import com.sinoyd.base.repository.rcc.SampleTypeRepository;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.common.utils.SortUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.*;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.repository.rcc.CompareJudgeRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroup2TestRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroupRepository;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.monitor.dto.rcc.DtoFixedpoint;
import com.sinoyd.lims.monitor.repository.rcc.FixedpointRepository;
import com.sinoyd.lims.pro.criteria.SampleFolderCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.*;
import com.sinoyd.lims.pro.dto.customer.DtoMethodStandardSample;
import com.sinoyd.lims.pro.entity.SamplingFrequencyTest;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumLogObjectType;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumLogOperateType;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumLogType;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumProAction;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.io.Serializable;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * 点位操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/20
 * @since V100R001
 */
@Service
@Slf4j
public class SampleFolderServiceImpl extends BaseJpaServiceImpl<DtoSampleFolder, String, SampleFolderRepository> implements SampleFolderService {

    //#region 注入
    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;

    @Autowired
    private SamplingFrequencyRepository samplingFrequencyRepository;

    @Autowired
    @Lazy
    private SamplingFrequencyService samplingFrequencyService;

    @Autowired
    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    @Autowired
    @Lazy
    private SamplingFrequencyTestService samplingFrequencyTestService;

    @Autowired
    private ReceiveSampleRecordRepository receiveSampleRecordRepository;

    @Autowired
    @Lazy
    private TestService testService;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    @Lazy
    private AnalyseDataService analyseDataService;

    @Autowired
    @Lazy
    private ProjectTestService projectTestService;

    @Autowired
    @Lazy
    private SchemeService schemeService;

    @Autowired
    private FixedpointRepository fixedpointRepository;

    @Autowired
    AnalyzeMethodRepository analyzeMethodRepository;

    @Autowired
    private SampleJudgeDataRepository sampleJudgeDataRepository;

    private TestRepository testRepository;

    private SampleFolderRepository sampleFolderRepository;

    private AnalyseDataRepository analyseDataRepository;

    private EvaluationRecordRepository evaluationRecordRepository;

    private EnterpriseRepository enterpriseRepository;

    private CompareJudgeRepository compareJudgeRepository;

    private SampleJudgeDataService sampleJudgeDataService;

    private SampleGroupService sampleGroupService;

    @Autowired
    private FlowCalibration2FrequencyRepository flowCalibration2FrequencyRepository;

    @Autowired
    @Lazy
    private FlowCalibration2FrequencyService flowCalibration2FrequencyService;

    private SampleGroupRepository sampleGroupRepository;

    @Autowired
    private SampleTypeRepository sampleTypeRepository;

    @Autowired
    private SampleTypeGroupRepository sampleTypeGroupRepository;

    @Autowired
    private SampleTypeGroup2TestRepository sampleTypeGroup2TestRepository;

    @Autowired
    @Lazy
    private ParamsConfigService paramsConfigService;
    //#endregion

    @Override
    public void findByPage(PageBean<DtoSampleFolder> pb, BaseCriteria sampleFolderCriteria) {
        pb.setEntityName("DtoSampleFolder s");
        pb.setSelect("select s");
        comRepository.findByPage(pb, sampleFolderCriteria);
    }

    //#region 方案

    //region 刘庄卓添加（2022/4/13）

    /**
     * 根据测试项目返回样品列表
     *
     * @param testId       选中的测试项目编号
     * @param projectId    项目编号
     * @param sampleTypeId 样品类型
     * @param isLoad       是否缓存
     * @return 样品列表
     */
    @Override
    public List<DtoMethodStandardSample> findSample(String testId, String projectId, String sampleTypeId, Boolean isLoad) {
        List<DtoSample> samples = new ArrayList<>();
        List<DtoMethodStandardSample> sampleList = new ArrayList<>();

        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select s");
        stringBuilder.append(" from DtoSampleFolder s where 1=1 and s.isDeleted = 0");
        stringBuilder.append(" and s.projectId = :projectId ");
        values.put("projectId", projectId);
        List<DtoSampleFolder> folderList = comRepository.find(stringBuilder.toString(), values);
        List<DtoSamplingFrequencyTest> frequencyTestList = new ArrayList<>();
        if (StringUtil.isNotEmpty(folderList)) {
            values.clear();
            stringBuilder = new StringBuilder("select s");
            stringBuilder.append(" from DtoSamplingFrequencyTest s where 1=1");
            stringBuilder.append(" and s.sampleFolderId in :sampleFolderIds ");
            stringBuilder.append(" and s.testId = :testId ");

            values.put("sampleFolderIds", folderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList()));
            values.put("testId", testId);
            frequencyTestList = comRepository.find(stringBuilder.toString(), values);
        }
        if (StringUtil.isNotEmpty(frequencyTestList)) {
            values.clear();
            stringBuilder = new StringBuilder("select s");
            stringBuilder.append(" from DtoSample s where 1=1");
            stringBuilder.append(" and s.samplingFrequencyId in :samplingFrequencyIds ");
            stringBuilder.append(" and s.sampleTypeId in :sampleTypeId ");

            values.put("samplingFrequencyIds", frequencyTestList.stream().map(DtoSamplingFrequencyTest::getSamplingFrequencyId).collect(Collectors.toList()));
            values.put("sampleTypeId", sampleTypeId);
            samples = comRepository.find(stringBuilder.toString(), values);
        }
        if (StringUtil.isNotEmpty(samples)) {
            for (DtoSample sample : samples) {
                DtoMethodStandardSample standardSample = new DtoMethodStandardSample();
                standardSample.setSampleId(sample.getId());
                standardSample.setSampleCode(sample.getCode());
                standardSample.setFolderName(sample.getRedFolderName());
                standardSample.setCycleNum(sample.getCycleOrder());
                standardSample.setTimesOrder(sample.getTimesOrder());
                standardSample.setSamplePeriod(sample.getSampleOrder());
                sampleList.add(standardSample);
            }
        }
        List<String> sampleCodes = sampleList.stream().map(DtoMethodStandardSample::getSampleCode).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sampleCodes)) {
            sampleList = sampleList.stream().sorted(Comparator.comparing(DtoMethodStandardSample::getSampleCode)).collect(Collectors.toList());
        } else {
            sampleList = sampleList.stream().sorted(Comparator.comparing(DtoMethodStandardSample::getFolderName)).collect(Collectors.toList());
        }
        return sampleList;
    }


    /**
     * 根据样品修改测试项目方法
     *
     * @param sampleIds          样品集合
     * @param testId             当前选中的测试项目Id
     * @param newAnalyzeMethodId 修改后的测试项目方法
     * @return 修改后的方法标准列表
     */
    @Override
    @Transactional
    public List<DtoProjectTest> changeAnalyzeMethodBySample(List<String> sampleIds, String testId, String newAnalyzeMethodId, Boolean isChangeAll) {
        //获取需要修改方法的样品集合
        List<DtoSample> samples = sampleRepository.findByIdInAndIsDeletedFalse(sampleIds);
        //获取样品对应的点位Id
        List<String> sampleFolderIds = samples.stream().map(DtoSample::getSampleFolderId).collect(Collectors.toList());
        //获取样品对应的频率id
        List<String> SamplingFrequencyIds = samples.stream().map(DtoSample::getSamplingFrequencyId).collect(Collectors.toList());

        //查询样品关联的频次记录
        List<DtoSamplingFrequencyTest> samplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdInAndSamplingFrequencyIdIn(sampleFolderIds, SamplingFrequencyIds);
        List<String> tIds = new ArrayList<>();
        //修改方法
        if (StringUtil.isNotEmpty(newAnalyzeMethodId)) {
            //获取当前测试项目下的频次记录
            List<DtoSamplingFrequencyTest> testsByFrequency = samplingFrequencyTests.stream().filter(p -> testId.contains(p.getTestId())).collect(Collectors.toList());
            if (isChangeAll) {
                //获取选中的分析方法Id
                List<String> analyzeMethodId = testsByFrequency.stream().map(DtoSamplingFrequencyTest::getAnalyzeMethodId).collect(Collectors.toList());
                //获取选中样品总所有分析方法相同的频次记录
                List<DtoSamplingFrequencyTest> changeAllCollection = samplingFrequencyTests.stream().filter(p -> analyzeMethodId.contains(p.getAnalyzeMethodId())).collect(Collectors.toList());
                //获取所有需要修改的样品
                List<String> frequencyIds = changeAllCollection.stream().map(DtoSamplingFrequencyTest::getSamplingFrequencyId).collect(Collectors.toList());
                List<String> foldId = changeAllCollection.stream().map(DtoSamplingFrequencyTest::getSampleFolderId).collect(Collectors.toList());
                List<DtoSample> allSamples = sampleRepository.findBySampleFolderIdInAndSamplingFrequencyIdIn(foldId, frequencyIds);
                samples.addAll(allSamples);
                tIds = changeAllCollection.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
                //循环修改方法
                freTestValue(changeAllCollection, newAnalyzeMethodId);
            } else {
                tIds = testsByFrequency.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
                //循环修改方法
                freTestValue(testsByFrequency, newAnalyzeMethodId);
            }
            //保存修改
            if (StringUtil.isNotEmpty(samplingFrequencyTests)) {
                samplingFrequencyTestRepository.save(samplingFrequencyTests);
            }
        }
        List<DtoProjectTest> data = new ArrayList<>();
        if (StringUtil.isNotEmpty(samples)) {
            String projectId = samples.get(0).getProjectId();
            String sampleTypeId = samples.get(0).getSampleTypeId();
            data = findMethodStandard(projectId, sampleTypeId, null, null, false);
        }
        //region 更改AnalyzeData的样品数据
        //获取当前测试项目
        DtoTest oldTest = testService.findOne(testId);
        //获取修改后的测试项目
        List<DtoTest> testsOfNewMethod = Collections.singletonList(testRepository.findByAnalyzeItemIdAndAnalyzeMethodId(oldTest.getAnalyzeItemId(), newAnalyzeMethodId));
        //统一修改查找含有新分析方法的所有测试项目
        if (isChangeAll) {
            testsOfNewMethod = testRepository.findByAnalyzeMethodIdAndIsDeletedFalse(newAnalyzeMethodId);
            List<String> newTestIds = samplingFrequencyTests.parallelStream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toList());
            testsOfNewMethod = testsOfNewMethod.parallelStream().filter(t -> newTestIds.contains(t.getId())).collect(Collectors.toList());
        }
        //获取样品Id
        List<String> sampleTypeIds = samples.stream().map(DtoSample::getSampleTypeId).distinct().collect(Collectors.toList());
        String sampleTypeId = StringUtil.isNotEmpty(sampleIds) ? sampleTypeIds.get(0) : UUIDHelper.GUID_EMPTY;
        //样品去重
        samples = samples.stream().distinct().collect(Collectors.toList());
        //修改分析数据
        analyseDataService.changeAnalyseMethod(samples, testsOfNewMethod, sampleTypeId);
        //比对数据修改
        sampleJudgeDataService.updateJudgeDataByTest(sampleIds, tIds, testsOfNewMethod.stream()
                .map(DtoTest::getId).distinct().collect(Collectors.toList()));
        //endregion
        return data;
    }


    /**
     * 获取方法标准页面数据
     *
     * @param projectId            项目编号
     * @param sampleTypeId         样品类型编号
     * @param redAnalyzeItemName   分析项目名称
     * @param redAnalyzeMethodName 分析方法和编号
     * @param isLoad               是否缓存
     * @return 方法标准数据
     */
    @Override
    public List<DtoProjectTest> findMethodStandard(String projectId, String sampleTypeId, String redAnalyzeItemName, String redAnalyzeMethodName, Boolean isLoad) {
        DtoProjectScheme projectScheme = findScheme(projectId, sampleTypeId, isLoad, redAnalyzeItemName, redAnalyzeMethodName, true);
        return projectScheme.getTest();
    }

    /**
     * 修改方法
     *
     * @param tests              相关测试项目Id
     * @param newAnalyzeMethodId 需要修改为的方法id
     */
    private void freTestValue(List<DtoSamplingFrequencyTest> tests, String newAnalyzeMethodId) {
        for (DtoSamplingFrequencyTest frequencyTest : tests) {
            DtoTest newTests = testRepository.findByAnalyzeItemIdAndAnalyzeMethodId(frequencyTest.getAnalyseItemId(), newAnalyzeMethodId);
            if (StringUtil.isNotNull(newTests)) {
                frequencyTest.setTestId(newTests.getId());
                frequencyTest.setAnalyzeMethodId(newTests.getAnalyzeMethodId());
                frequencyTest.setRedAnalyzeMethodName(newTests.getRedAnalyzeMethodName());
            }
        }
    }
    //endregion

    /**
     * 点位明细
     *
     * @param id 点位id
     * @return 点位明细
     */
    @Override
    public DtoSampleFolderTemp findDetail(String id) {
        DtoSampleFolder folder = repository.findOne(id);
        List<DtoSamplingFrequency> frequencyList = samplingFrequencyRepository.findBySampleFolderId(id);
        List<DtoSamplingFrequencyTest> frequencyTestList = samplingFrequencyTestRepository.findBySampleFolderId(id);
        List<String> testIds = frequencyTestList.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testIds.size() > 0 ? testService.findRedisByIds(testIds) : new ArrayList<>();

        Map<String, DtoSampleType> samTypeMap = new HashMap<>();
        DtoSampleType samType = sampleTypeService.findOne(folder.getSampleTypeId());
        samTypeMap.put(folder.getSampleTypeId(), samType);

        List<DtoSampleFolderTemp> temps = this.getFolderSchemes(folder.getProjectId(), Collections.singletonList(folder), frequencyList, frequencyTestList, testList, samTypeMap);

        return temps.get(0);
    }


    /**
     * 查询项目对应检测类型下的方案
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测大类id
     * @param isLoad       是否加载到redis
     * @return 方案
     */
    @Override
    public DtoProjectScheme findScheme(String projectId, String sampleTypeId, Boolean isLoad, String redAnalyzeItemName, String redAnalyzeMethodName, Boolean isShowTest) {
        DtoProjectScheme scheme = new DtoProjectScheme();

        Map<String, Object> values = new HashMap<>();
        StringBuilder stringBuilder = new StringBuilder("select s");
        stringBuilder.append(" from DtoSampleFolder s where 1=1");
        if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
            stringBuilder.append(" and s.orgId = :orgId");
            values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        }
        stringBuilder.append(" and s.projectId = :projectId ");
        if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(sampleTypeId)) {
            stringBuilder.append(" and s.sampleTypeId = :sampleTypeId ");
            values.put("sampleTypeId", sampleTypeId);
        }

        values.put("projectId", projectId);
        List<DtoSampleFolder> folderList = comRepository.find(stringBuilder.toString(), values);

        List<DtoSamplingFrequency> frequencyList = new ArrayList<>();
        List<DtoSamplingFrequencyTest> frequencyTestList = new ArrayList<>();
        List<DtoTest> testList = new ArrayList<>();
        Set<DtoProjectTest> projectTestSet = new HashSet<>();
        if (folderList.size() > 0) {
            values.clear();

            stringBuilder = new StringBuilder("select s");
            stringBuilder.append(" from DtoSamplingFrequency s where 1=1");
            if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
                stringBuilder.append(" and s.orgId = :orgId");
                values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
            }
            stringBuilder.append(" and s.sampleFolderId in :sampleFolderIds ");

            values.put("sampleFolderIds", folderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList()));
            frequencyList = comRepository.find(stringBuilder.toString(), values);

            values.clear();

            stringBuilder = new StringBuilder("select s ");
            stringBuilder.append(" from DtoSamplingFrequencyTest s where 1=1");
            if (StringUtil.isNotNull(PrincipalContextUser.getPrincipal())) {
                stringBuilder.append(" and s.orgId = :orgId");
                values.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
            }
            stringBuilder.append(" and s.sampleFolderId in :sampleFolderIds ");
            if (StringUtil.isNotEmpty(redAnalyzeItemName)) {
                stringBuilder.append(" and s.redAnalyzeItemName like :redAnalyzeItemName ");
                values.put("redAnalyzeItemName", "%" + redAnalyzeItemName + "%");
            }
            if (StringUtil.isNotEmpty(redAnalyzeMethodName)) {
                stringBuilder.append(" and (s.redAnalyzeMethodName like :redAnalyzeMethodName or s.redCountryStandard like :redCountryStandard )");
                values.put("redAnalyzeMethodName", "%" + redAnalyzeMethodName + "%");
                values.put("redCountryStandard", "%" + redAnalyzeMethodName + "%");
            }
            values.put("sampleFolderIds", folderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList()));

            List<DtoSamplingFrequencyTest> frequencyTests = comRepository.find(stringBuilder.toString(), values);
            List<String> testIds = frequencyTests.stream().map(SamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
            if (testIds.size() > 0) {
                List<String> frequencyIdList = frequencyTests.stream().map(DtoSamplingFrequencyTest::getSamplingFrequencyId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
                testList = testService.findRedisByIds(testIds);
                List<String> samplingMethodIds = frequencyTests.stream().map(DtoSamplingFrequencyTest::getSamplingMethodId).distinct().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p) && StringUtil.isNotEmpty(p)).collect(Collectors.toList());
                if (StringUtil.isEmpty(samplingMethodIds)) {
                    samplingMethodIds = testList.stream().map(DtoTest::getSamplingMethodId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p) && StringUtil.isNotEmpty(p)).distinct().collect(Collectors.toList());
                }
                List<DtoAnalyzeMethod> samplingMethodList = StringUtil.isNotEmpty(samplingMethodIds) ? analyzeMethodRepository.findAll(samplingMethodIds) : new ArrayList<>();
                Map<String, DtoAnalyzeMethod> samplingMethodMap = samplingMethodList.stream().collect(Collectors.toMap(DtoAnalyzeMethod::getId, p -> p));
                Map<String, DtoTest> testMap = testList.stream().collect(Collectors.toMap(DtoTest::getId, test -> test));
                //获取所有点位上的样品类型id列表
                List<String> sampleTypeIdsForFolder = folderList.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
                Map<String, List<DtoSamplingFrequencyTest>> tstId2FrequencyTestListMap = frequencyTests.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getTestId));
                //查询所有样品
                List<DtoSample> sampleList = StringUtil.isNotEmpty(frequencyIdList) ? sampleRepository.findBySamplingFrequencyIdIn(frequencyIdList) : new ArrayList<>();
                sampleList = sampleList.stream().filter(p -> sampleTypeIdsForFolder.contains(p.getSampleTypeId())).collect(Collectors.toList());
                for (DtoSamplingFrequencyTest frequencyTest : frequencyTests) {
                    DtoSamplingFrequencyTest sft = new DtoSamplingFrequencyTest();
                    sft.setSampleFolderId(frequencyTest.getSampleFolderId());
                    sft.setSamplingFrequencyId(frequencyTest.getSamplingFrequencyId());
                    sft.setTestId(frequencyTest.getTestId());
                    sft.setIsOutsourcing(frequencyTest.getIsOutsourcing());
                    sft.setIsSamplingOut(frequencyTest.getIsSamplingOut());
                    if (testMap.containsKey(sft.getTestId())) {
                        DtoTest test = testMap.get(sft.getTestId());
                        sft.setAnalyseItemId(test.getAnalyzeItemId());
                        sft.setAnalyzeMethodId(test.getAnalyzeMethodId());
                        sft.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                        sft.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                        sft.setRedCountryStandard(test.getRedCountryStandard());
                        sft.setIsCompleteField(test.getIsCompleteField());
                        frequencyTestList.add(sft);
                        DtoProjectTest pt = new DtoProjectTest(test);
                        pt.setIsOutsourcing(test.getIsOutsourcing());
                        pt.setIsSamplingOut(test.getIsSamplingOut());
                        pt.setProjectId(projectId);
                        // 采样方法名称
                        DtoAnalyzeMethod samplingMethodMapOrDefault = samplingMethodMap.getOrDefault(test.getSamplingMethodId(), null);
                        pt.setSamplingMethodName(null != samplingMethodMapOrDefault ? samplingMethodMapOrDefault.getMethodName() : "");
                        pt.setSampleTypeId(folderList.stream().filter(p -> p.getId().equals(sft.getSampleFolderId())).map(DtoSampleFolder::getSampleTypeId).findFirst().orElse(""));
                        if (isShowTest) {
                            String sampleTypeIdForFolder = folderList.stream().filter(p -> p.getId().equals(sft.getSampleFolderId()))
                                    .map(DtoSampleFolder::getSampleTypeId).findFirst().orElse("");
                            List<DtoSamplingFrequencyTest> frequencyTestListForTest = tstId2FrequencyTestListMap.getOrDefault(test.getId(), new ArrayList<>());
                            List<String> frequencyIdListForTest = frequencyTestListForTest.stream().map(DtoSamplingFrequencyTest::getSamplingFrequencyId).distinct().collect(Collectors.toList());
                            //按照频次id列表和样品类型id对样品进行过滤
                            List<DtoSample> loopSampleList = sampleList.stream().filter(p -> frequencyIdListForTest.contains(p.getSamplingFrequencyId())
                                    && sampleTypeIdForFolder.equals(p.getSampleTypeId())).collect(Collectors.toList());
                            pt.setSampleCount(loopSampleList.size());
                        }
                        projectTestSet.add(pt);
                    }
                }
            }
        }
        //#region 处理项目指标
        List<DtoProjectTest> projectTestList = new ArrayList<>(projectTestSet);

        //读取缓存中对应的检测类型数据和测试项目数据
        List<String> sampleTypeIds = folderList.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        Map<String, DtoSampleType> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, samType -> samType));

        for (DtoProjectTest pt : projectTestList) {
            if (samTypeMap.containsKey(pt.getSampleTypeId())) {
                DtoSampleType samType = samTypeMap.get(pt.getSampleTypeId());
                pt.setBigSampleTypeId(samType.getParentId());
                pt.setSampleTypeName(samType.getTypeName());
            }
        }
        projectTestList.sort(Comparator.comparing(DtoProjectTest::getBigSampleTypeId).thenComparing(DtoProjectTest::getSampleTypeName)
                .thenComparing(DtoProjectTest::getRedAnalyzeMethodName).thenComparing(DtoProjectTest::getRedAnalyzeItemName));

        scheme.setTest(projectTestList);
        //#endregion
        if (!isShowTest) {
            //获取点位信息
            List<DtoSampleFolderTemp> sampleFolders = new ArrayList<>();
            if (StringUtil.isNotEmpty(folderList)) {
                sampleFolders = this.getFolderSchemes(projectId, folderList, frequencyList, frequencyTestList, testList, samTypeMap);
            }
            scheme.setSampleFolder(sampleFolders);

            if (isLoad) {
                projectTestService.saveProjectTest(projectId, scheme.getTest());
            }
        }
        return scheme;
    }

    /**
     * 查询项目下的指标
     *
     * @param projectId 项目id
     * @return 方案
     */
    @Override
    public List<DtoProjectTest> findProjectTest(String projectId) {
        List<DtoSampleFolder> sampleFolders = repository.findByProjectId(projectId);
        List<String> sampleFolderIds = sampleFolders.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTest> sftList = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
        Map<String, List<DtoSampleFolder>> sampleFolderMap = sampleFolders.stream().collect(Collectors.groupingBy(DtoSampleFolder::getSampleTypeId));

        List<DtoProjectTest> ptList = new ArrayList<>();
        for (String sampleType : sampleFolderMap.keySet()) {
            List<String> thisSamTypeFolderIds = sampleFolderMap.get(sampleType).stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
            List<DtoSamplingFrequencyTest> thisSamTypeSfts = sftList.stream().filter(p -> thisSamTypeFolderIds.contains(p.getSampleFolderId())).collect(Collectors.toList());

            List<String> thisTestIds = thisSamTypeSfts.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> thisTests = testService.findRedisByIds(thisTestIds);
            DtoSampleType samType = sampleTypeService.findOne(sampleType);
            for (DtoTest test : thisTests) {
                DtoProjectTest pt = new DtoProjectTest(test);
                pt.setProjectId(projectId);
                pt.setSampleTypeId(sampleType);
                pt.setBigSampleTypeId(samType.getParentId());
                pt.setSampleTypeName(samType.getTypeName());
                ptList.add(pt);
            }
        }

        return ptList;
    }

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位
     * @return 返回该点位的方案
     */
    @Transactional
    @Override
    public DtoSampleFolderTemp addFolder(DtoSampleFolder dtoSampleFolder) {
        List<DtoSampleFolderItemVo> itemVoList = dtoSampleFolder.getItemVos();
        List<String> testIds = itemVoList.stream().map(DtoSampleFolderItemVo::getTestId).collect(Collectors.toList());
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        testList = testList.stream().filter(p -> !p.getIsTotalTest()).collect(Collectors.toList());
        Integer timeOrderMax = Collections.max(itemVoList.stream().map(DtoSampleFolderItemVo::getTimesOrder).collect(Collectors.toList()));
        Integer samplePeriodMax = Collections.max(itemVoList.stream().map(DtoSampleFolderItemVo::getSamplePeriod).collect(Collectors.toList()));
        dtoSampleFolder.setTimePerPeriod(timeOrderMax);
        dtoSampleFolder.setSampleOrder(samplePeriodMax);
        testList.forEach(p -> {
            Optional<DtoSampleFolderItemVo> itemVo = itemVoList.stream()
                    .filter(item -> item.getTestId().equals(p.getId())).findFirst();
            if (itemVo.isPresent()) {
                p.setTimePerPeriod(itemVo.get().getTimesOrder());
                p.setSampleOrder(itemVo.get().getSamplePeriod());
            }
        });
        return this.addFolder(dtoSampleFolder, testList);
    }

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位
     * @return 返回该点位的方案
     */
    @Transactional
    @Override
    public DtoSampleFolderTemp addFolderTemp(DtoSampleFolder dtoSampleFolder) {
        List<DtoTest> testList = new ArrayList<>();
        List<String> anaItemIds = dtoSampleFolder.getAnalyseItemIds();
        if (dtoSampleFolder.getTestIds().size() > 0) {
            testList.addAll(testService.findRedisByIds(dtoSampleFolder.getTestIds()));
            List<String> removeItemIds = testList.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
            anaItemIds.removeAll(removeItemIds);
            // 通过模版选择的测试项目，校验方法状态
            List<String> methodIds = testList.stream().map(DtoTest::getAnalyzeMethodId).distinct().collect(Collectors.toList());
            List<DtoAnalyzeMethod> analyzeMethods = StringUtil.isNotEmpty(methodIds) ? analyzeMethodRepository.findAll(methodIds) : new ArrayList<>();
            if (analyzeMethods.stream().anyMatch(p -> p.getStatus().equals(EnumLIM.EnumAnalyzeMethodStatus.停用.getValue()))) {
                throw new BaseException("存在方法已停用的测试项目，请剔除后重试！");
            }
        }

        testList.addAll(this.getDefaultTest(dtoSampleFolder.getProjectId(), dtoSampleFolder.getSampleTypeId(), anaItemIds));
        testList.forEach(p -> {
            p.setTimePerPeriod(dtoSampleFolder.getTimePerPeriod());
            p.setSampleOrder(dtoSampleFolder.getSampleOrder());
        });
        return this.addFolder(dtoSampleFolder, testList);
    }

    /**
     * 新增点位
     *
     * @param dtoSampleFolder 点位
     * @param testList        测试项目
     * @return 返回该点位的方案
     */
    @Transactional
    @Override
    public DtoSampleFolderTemp addFolder(DtoSampleFolder dtoSampleFolder, List<DtoTest> testList) {
        //进行点位添加并返回
        List<DtoTest> addtestList;
        if (StringUtil.isNotEmpty(testList) && testList.size() > 0) {
            addtestList = new ArrayList<>(testList);
        } else {
            addtestList = this.getDefaultTest(dtoSampleFolder.getProjectId(), dtoSampleFolder.getSampleTypeId(), dtoSampleFolder.getAnalyseItemIds());
        }
        DtoLoadScheme target = schemeService.addSchemeFolder(dtoSampleFolder, addtestList);

        DtoSampleType samType = sampleTypeService.findOne(dtoSampleFolder.getSampleTypeId());
        Map<String, DtoSampleType> samTypeMap = new HashMap<>();
        if (StringUtil.isNotNull(samType)) {
            samTypeMap.put(dtoSampleFolder.getSampleTypeId(), samType);
        }

        List<DtoSampleFolderTemp> temps = this.getFolderSchemes(dtoSampleFolder.getProjectId(), target.getSampleFolder(),
                target.getSamplingFrequency(), target.getSamplingFrequencyTest(), addtestList, samTypeMap);

        //更新项目缓存的指标及处理自增
        projectTestService.modifyProjectTest(dtoSampleFolder.getProjectId(), new ArrayList<>(temps.get(0).getItem().values()));

        return temps.get(0);
    }

    @Transactional
    @Override
    public DtoSampleFolder update(DtoSampleFolder dtoSampleFolder) {
        DtoSampleFolder sampleFolder = repository.findOne(dtoSampleFolder.getId());
        sampleFolder.setLat(dtoSampleFolder.getLat());
        sampleFolder.setLon(dtoSampleFolder.getLon());
        if (!sampleFolder.getWatchSpot().equals(dtoSampleFolder.getWatchSpot())) {
            //修改样品点位名称
            sampleService.updateWatchSpot(dtoSampleFolder.getId(), dtoSampleFolder.getWatchSpot(), dtoSampleFolder.getFolderCode());
            //修改点位名称
            String comment = String.format("修改点位名称,由%s改为%s。", sampleFolder.getWatchSpot(), dtoSampleFolder.getWatchSpot());
            newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumLogType.方案点位信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.修改点位.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
            sampleFolder.setWatchSpot(dtoSampleFolder.getWatchSpot());
            return super.update(sampleFolder);
        }
        sampleService.updateSampleLonAndLat(dtoSampleFolder);
        return sampleFolder;
    }

    @Transactional
    @Override
    public <K extends Serializable> Integer logicDeleteById(K id) {
        String idStr = String.valueOf(id);
        DtoSampleFolder folder = repository.findOne(idStr);
        DtoSampleType sampleType = sampleTypeService.findOne(folder.getSampleTypeId());
        String comment = String.format("删除了点位%s(%s)。", folder.getWatchSpot(), sampleType.getTypeName());

        newLogService.createLog(folder.getProjectId(), comment, "", EnumLogType.方案点位信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.删除点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

        //删除对应点位下的频次及指标
        List<String> oldTestIds = samplingFrequencyTestRepository.findBySampleFolderId(idStr).stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
        if (oldTestIds.size() > 0) {
            testService.decrementOrderNum(oldTestIds);
        }
        samplingFrequencyService.deleteBySampleFolderId(idStr);
        samplingFrequencyTestRepository.deleteBySampleFolderId(idStr);

        //删除点位样品相关信息
        schemeService.deleteSampleFolder(folder.getProjectId(), idStr);

        //当前点位的样品
        List<String> samIds = sampleRepository.findBySampleFolderId(idStr).stream().map(DtoSample::getId).collect(Collectors.toList());
        sampleJudgeDataService.deleteJudgeDataByTest(samIds, new ArrayList<>(), Boolean.TRUE);

        //删除仪器流量校准关联的点位(如果有)
        deleteFlowCalibration2Frequency(Collections.singletonList((String) id), null, null);

        Integer count = super.logicDeleteById(id);

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.方案删除点位, folder.getProjectId());
                    }
                }
        );

        return count;
    }

    /**
     * 复制点位
     *
     * @param ids       被复制的点位id集合
     * @param copyTimes 复制次数
     * @return 复制出来的点位方案
     */
    @Transactional
    @Override
    public List<DtoSampleFolderTemp> copyFolders(List<String> ids, Integer copyTimes, Boolean isApi) {
        //进行点位复制并返回复制后的数据
        DtoLoadScheme target = schemeService.copySchemeSampleFolder(ids, copyTimes, isApi);


        //读取测试项目信息
        List<String> testIds = target.getSamplingFrequencyTest().stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = testService.findRedisByIds(testIds);

        //读取点位检测类型信息
        List<String> sampleTypes = target.getSampleFolder().stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypes);
        Map<String, DtoSampleType> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, samType -> samType));

        //返回预设实体下的增量方案信息的展现
        return this.getFolderSchemes(target.getSampleFolder().get(0).getProjectId(), target.getSampleFolder(), target.getSamplingFrequency(),
                target.getSamplingFrequencyTest(), tests, samTypeMap);
    }

    /**
     * 添加点位周期频次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @return 点位周期频次
     */
    @Transactional
    @Override
    public List<DtoSampleFolderTemp> addPeriodTimes(String sampleFolderId, Integer periodCount, Integer timePerPeriod,
                                                    Integer samplePeriod) {
        //进行点位添加并返回的数据
        DtoLoadScheme target = schemeService.addSchemePeriodTimes(sampleFolderId, periodCount, timePerPeriod, samplePeriod);

        //读取点位检测类型信息
        List<String> sampleTypes = target.getSampleFolder().stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypes);
        Map<String, DtoSampleType> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, samType -> samType));

        List<DtoSampleFolderTemp> temps = this.getFolderSchemes(target.getSampleFolder().get(0).getProjectId(), target.getSampleFolder(),
                target.getSamplingFrequency(), new ArrayList<>(), new ArrayList<>(), samTypeMap);
        return temps.get(0).getChildren();
    }

    /**
     * 添加次数
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @param samplePeriod   样品数
     * @return 次数
     */
    @Transactional
    @Override
    public List<DtoSampleFolderTemp> addTimes(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod) {
        //进行点位添加并返回的数据
        DtoLoadScheme target = schemeService.addSchemeTimes(sampleFolderId, periodCount, timePerPeriod, samplePeriod);

        //读取点位检测类型信息
        List<String> sampleTypes = target.getSampleFolder().stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypes);
        Map<String, DtoSampleType> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, samType -> samType));

        List<DtoSampleFolderTemp> temps = this.getFolderSchemes(target.getSampleFolder().get(0).getProjectId(), target.getSampleFolder(),
                target.getSamplingFrequency(), new ArrayList<>(), new ArrayList<>(), samTypeMap);

        return temps.get(0).getChildren().get(0).getChildren();
    }

    /**
     * 添加样品数
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePerPeriod  次数
     * @param samplePeriod   样品数
     * @return 次数
     */
    @Transactional
    @Override
    public List<DtoSampleFolderTemp> addSamplePeriods(String sampleFolderId, Integer periodCount, Integer timePerPeriod, Integer samplePeriod) {
        //进行点位添加并返回的数据
        DtoLoadScheme target = schemeService.addSchemeSamplePeriods(sampleFolderId, periodCount, timePerPeriod, samplePeriod);
        //读取点位检测类型信息
        List<String> sampleTypes = target.getSampleFolder().stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypes);
        Map<String, DtoSampleType> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, samType -> samType));
        List<DtoSampleFolderTemp> temps = this.getFolderSchemes(target.getSampleFolder().get(0).getProjectId(), target.getSampleFolder(),
                target.getSamplingFrequency(), new ArrayList<>(), new ArrayList<>(), samTypeMap);
        return temps.get(0).getChildren().get(0).getChildren().get(0).getChildren();
    }

    /**
     * 复制周期
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param copyTimes      复制次数
     * @return 周期
     */
    @Transactional
    @Override
    public List<DtoSampleFolderTemp> copyPeriod(String sampleFolderId, Integer periodCount, Integer copyTimes) {
        //读取点位
        DtoSampleFolder sampleFolder = repository.findOne(sampleFolderId);
        //进行点位复制并返回复制后的数据
        DtoLoadScheme target = schemeService.copySchemePeriod(sampleFolderId, periodCount, copyTimes);

        //读取测试项目信息
        List<String> testIds = target.getSamplingFrequencyTest().stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = testIds.size() > 0 ? testService.findRedisByIds(testIds) : new ArrayList<>();

        //读取点位检测类型信息
        DtoSampleType samType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        Map<String, DtoSampleType> samTypeMap = new HashMap<>();
        samTypeMap.put(samType.getId(), samType);

        //返回预设实体下的增量方案信息的展现
        return this.getFolderSchemes(sampleFolder.getProjectId(), target.getSampleFolder(), target.getSamplingFrequency(),
                target.getSamplingFrequencyTest(), tests, samTypeMap).get(0).getChildren();
    }

    /**
     * 复制批次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePeriod     批次
     * @param copyTimes      复制次数
     * @return 周期
     */
    @Transactional
    @Override
    public List<DtoSampleFolderTemp> copyTimePeriod(String sampleFolderId, Integer periodCount, Integer timePeriod, Integer copyTimes) {
        //读取点位
        DtoSampleFolder sampleFolder = repository.findOne(sampleFolderId);
        //进行点位复制并返回复制后的数据
        DtoLoadScheme target = schemeService.copySchemeTimePeriod(sampleFolderId, periodCount, timePeriod, copyTimes);

        //读取测试项目信息
        List<String> testIds = target.getSamplingFrequencyTest().stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = testIds.size() > 0 ? testService.findRedisByIds(testIds) : new ArrayList<>();

        //读取点位检测类型信息
        DtoSampleType samType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        Map<String, DtoSampleType> samTypeMap = new HashMap<>();
        samTypeMap.put(samType.getId(), samType);

        //返回预设实体下的增量方案信息的展现
        return this.getFolderSchemes(sampleFolder.getProjectId(), target.getSampleFolder(), target.getSamplingFrequency(),
                target.getSamplingFrequencyTest(), tests, samTypeMap).get(0).getChildren().get(0).getChildren();
    }

    /**
     * 删除周期
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     */
    @Transactional
    @Override
    public void deletePeriod(String sampleFolderId, Integer periodCount) {
        DtoSampleFolder folder = repository.findOne(sampleFolderId);
        DtoSampleType samType = sampleTypeService.findOne(folder.getSampleTypeId());

        //删除对应点位周期下的频次及指标
        List<DtoSamplingFrequency> frequencyList = samplingFrequencyRepository.findBySampleFolderIdAndPeriodCount(sampleFolderId, periodCount);

        deleteFrequencyDetail(folder.getProjectId(), frequencyList);

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.方案删除周期, folder.getProjectId());
                    }
                }
        );

        String comment = String.format("删除了点位%s(%s)第%d周期信息。", folder.getWatchSpot(), samType.getTypeName(), periodCount);
        newLogService.createLog(folder.getProjectId(), comment, "", EnumLogType.方案点位信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.删除周期.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    /**
     * 删除批次
     *
     * @param sampleFolderId 点位id
     * @param periodCount    周期
     * @param timePeriod     批次
     */
    @Transactional
    @Override
    public void deleteTimePeriod(String sampleFolderId, Integer periodCount, Integer timePeriod) {
        DtoSampleFolder folder = repository.findOne(sampleFolderId);
        DtoSampleType samType = sampleTypeService.findOne(folder.getSampleTypeId());

        //删除对应点位周期下的频次及指标
        List<DtoSamplingFrequency> frequencyList = samplingFrequencyRepository
                .findBySampleFolderIdAndPeriodCountAndTimePerPeriod(sampleFolderId, periodCount, timePeriod);

        deleteFrequencyDetail(folder.getProjectId(), frequencyList);

        deleteFlowCalibration2Frequency(Collections.singletonList(sampleFolderId), Collections.singletonList(periodCount), null);

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.方案删除批次, folder.getProjectId());
                    }
                }
        );

        String comment = String.format("删除了点位%s(%s)第%d周期第%d次信息。", folder.getWatchSpot(), samType.getTypeName(),
                periodCount, timePeriod);
        newLogService.createLog(folder.getProjectId(), comment, "", EnumLogType.方案点位信息.getValue(), EnumLogObjectType.方案.getValue(),
                EnumLogOperateType.删除批次.toString(), PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    /**
     * 复制样品数
     *
     * @param sampleFolderId      点位id
     * @param samplingFrequencyId 频次id
     * @param copyTimes           复制次数
     * @return 样品数
     */
    @Transactional
    @Override
    public List<DtoSampleFolderTemp> copyTimes(String sampleFolderId, String samplingFrequencyId, Integer copyTimes) {

        //读取点位
        DtoSampleFolder sampleFolder = repository.findOne(sampleFolderId);
        //进行点位复制并返回复制后的数据
        DtoLoadScheme target = schemeService.copySchemeSamplingFrequency(sampleFolderId, samplingFrequencyId, copyTimes);

        //读取测试项目信息
        List<String> testIds = target.getSamplingFrequencyTest().stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> tests = testIds.size() > 0 ? testService.findRedisByIds(testIds) : new ArrayList<>();

        //读取点位检测类型信息
        DtoSampleType samType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
        Map<String, DtoSampleType> samTypeMap = new HashMap<>();
        samTypeMap.put(samType.getId(), samType);

        //返回预设实体下的增量方案信息的展现
        return this.getFolderSchemes(sampleFolder.getProjectId(), target.getSampleFolder(), target.getSamplingFrequency(),
                target.getSamplingFrequencyTest(), tests, samTypeMap).get(0).getChildren().get(0).getChildren().get(0).getChildren();
    }

    /**
     * 删除次数
     *
     * @param samplingFrequencyId 频次id
     */
    @Transactional
    @Override
    public void deleteTimes(String samplingFrequencyId) {
        //删除对应点位周期下的频次及指标
        DtoSamplingFrequency frequency = samplingFrequencyService.findOne(samplingFrequencyId);
        //注意不要用logicDeleteById，因为若点位下所有周期均删除的话该点位也会被删除，会影响用户操作
        samplingFrequencyService.delete(frequency);
        samplingFrequencyTestRepository.deleteBySamplingFrequencyIdIn(Collections.singletonList(samplingFrequencyId));

        List<String> sampleIds = sampleRepository.findBySamplingFrequencyId(samplingFrequencyId).stream()
                .map(DtoSample::getId).collect(Collectors.toList());
        //删除比对数据
        sampleJudgeDataService.deleteJudgeDataByTest(sampleIds, new ArrayList<>(), Boolean.TRUE);

        DtoSampleFolder folder = super.findOne(frequency.getSampleFolderId());

        //删除点位样品相关信息
        schemeService.deleteSamplingFrequency(folder.getProjectId(), Collections.singletonList(samplingFrequencyId));

        //清除项目指标缓存
        projectTestService.removeProjectTest(folder.getProjectId());

        deleteFlowCalibration2Frequency(Collections.singletonList(frequency.getSampleFolderId()),
                Collections.singletonList(frequency.getPeriodCount()), null);

        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        proService.sendProMessage(EnumProAction.方案删除次数, folder.getProjectId());
                    }
                }
        );

        DtoSampleType samType = sampleTypeService.findOne(folder.getSampleTypeId());

        String comment = String.format("删除了点位%s(%s)第%d周期第%d次信息。", folder.getWatchSpot(), samType.getTypeName(), frequency.getPeriodCount(), frequency.getTimePerPeriod());
        newLogService.createLog(folder.getProjectId(), comment, "", EnumLogType.方案点位信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.删除次数.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    /**
     * 删除点位周期次数
     *
     * @param samplingFrequencyIds 频次id集合
     */
    @Transactional
    @Override
    public void deleteTimes(List<String> samplingFrequencyIds) {
        if (StringUtil.isNotNull(samplingFrequencyIds) && samplingFrequencyIds.size() > 0) {
            //删除对应点位周期下的频次及指标
            List<DtoSamplingFrequency> frequencyList = samplingFrequencyService.findAll(samplingFrequencyIds);

            //按照分析项目id删除对应的评价记录
            List<DtoSample> allSampleList = sampleRepository.findBySamplingFrequencyIdIn(samplingFrequencyIds);
            List<String> allSampleIdList = allSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(allSampleIdList)) {
                List<DtoAnalyseData> allAnaDataList = analyseDataRepository.findBySampleIdIn(allSampleIdList);
                List<String> allAnaDataIdList = allAnaDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
                if (StringUtil.isNotEmpty(allAnaDataIdList)) {
                    List<DtoEvaluationRecord> rmvAnaEvaRcdList = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(allAnaDataIdList,
                            EnumPRO.EnumEvaluationType.分析数据.getValue(), EnumPRO.EnumEvaluationPlan.分析数据.getValue());
                    if (StringUtil.isNotEmpty(rmvAnaEvaRcdList)) {
                        evaluationRecordRepository.logicDeleteById(rmvAnaEvaRcdList.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
                    }
                }
                sampleJudgeDataService.deleteJudgeDataByTest(allSampleIdList, new ArrayList<>(), Boolean.TRUE);
            }
            //按照点位id删除对应的评价记录
            List<String> folderIdList = frequencyList.stream().map(DtoSamplingFrequency::getSampleFolderId).distinct().collect(Collectors.toList());
            //找到所有点位下的频次测试项目信息,并按照点位分组
            List<DtoSamplingFrequencyTest> allSamplingFrequencyTestList = samplingFrequencyTestRepository.findBySampleFolderIdIn(folderIdList);
            Map<String, List<DtoSamplingFrequencyTest>> allFldId2FreqTestListMap = allSamplingFrequencyTestList.stream()
                    .collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSampleFolderId));
            Map<String, List<String>> oriFldId2TstIdListMap = new HashMap<>();
            for (Map.Entry<String, List<DtoSamplingFrequencyTest>> entry : allFldId2FreqTestListMap.entrySet()) {
                List<String> loopTstIdList = entry.getValue().stream().map(DtoSamplingFrequencyTest::getTestId).distinct()
                        .collect(Collectors.toList());
                //收集点位下原有的测试项目id
                oriFldId2TstIdListMap.put(entry.getKey(), loopTstIdList);
            }
            //找到所有点位下的频次信息
            List<DtoSamplingFrequency> allSamplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdIn(folderIdList);
            //过滤掉要删除的frequencyId
            List<DtoSamplingFrequency> remainSamplingFrequencyList = allSamplingFrequencyList.stream().filter(p -> !samplingFrequencyIds.contains(p.getId())).collect(Collectors.toList());
            List<String> remainFreqIdList = remainSamplingFrequencyList.stream().map(DtoSamplingFrequency::getId).distinct().collect(Collectors.toList());
            List<DtoSamplingFrequencyTest> remainFreqTestList = StringUtil.isNotEmpty(remainFreqIdList) ?
                    samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(remainFreqIdList) : new ArrayList<>();
            Map<String, Set<String>> remainFldId2TestIdSetMap = remainFreqTestList.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSampleFolderId,
                    Collectors.mapping(DtoSamplingFrequencyTest::getTestId, Collectors.toSet())));
            //遍历点位下原有的测试项目，比对删除后剩余的测试项目，过滤出要删除的测试项目
            Map<String, List<String>> toRmvFldId2TstIdListMap = new HashMap<>();
            for (String fldId : folderIdList) {
                if (oriFldId2TstIdListMap.containsKey(fldId)) {
                    //原有的测试项目
                    List<String> oriTstIdList = oriFldId2TstIdListMap.get(fldId);
                    //剩余的测试项目
                    Set<String> remainTstIdSet = remainFldId2TestIdSetMap.get(fldId);
                    if (StringUtil.isNotEmpty(remainTstIdSet)) {
                        List<String> toRmvTstIdList = oriTstIdList.stream().filter(p -> !remainTstIdSet.contains(p)).collect(Collectors.toList());
                        toRmvFldId2TstIdListMap.put(fldId, toRmvTstIdList);
                    } else {
                        toRmvFldId2TstIdListMap.put(fldId, oriTstIdList);
                    }
                }
            }
            //找到原有的评价记录,按照点位id分组
            Map<String, List<DtoEvaluationRecord>> fld2ExistEvaRcdList = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(folderIdList,
                    EnumPRO.EnumEvaluationType.点位.getValue(), EnumPRO.EnumEvaluationPlan.实际.getValue()).stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getObjectId));
            //放置要删除的评价记录
            List<DtoEvaluationRecord> toRmvRcdList = new ArrayList<>();
            for (Map.Entry<String, List<DtoEvaluationRecord>> entry : fld2ExistEvaRcdList.entrySet()) {
                if (toRmvFldId2TstIdListMap.containsKey(entry.getKey())) {
                    List<String> toRmvTstIdList = toRmvFldId2TstIdListMap.get(entry.getKey());
                    toRmvRcdList.addAll(entry.getValue().stream().filter(p -> toRmvTstIdList.contains(p.getTestId())).collect(Collectors.toList()));
                }
            }
            if (StringUtil.isNotEmpty(toRmvRcdList)) {
                evaluationRecordRepository.logicDeleteById(toRmvRcdList.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }

            if (frequencyList.size() > 0) {
                DtoSampleFolder folder = repository.findOne(frequencyList.get(0).getSampleFolderId());

                deleteFrequency(frequencyList, Collections.singletonList(folder), folder.getProjectId(), samplingFrequencyIds);
            }
            List<String> frequencyIds = frequencyList.stream().map(DtoSamplingFrequency::getId).collect(Collectors.toList());
            List<String> folderIds = frequencyList.stream().map(DtoSamplingFrequency::getSampleFolderId).distinct().collect(Collectors.toList());
            //移除频次id，以及移除频次对应的点位id，剩下的是没有下面没有频次的点位id，需要额外删除
            samplingFrequencyIds.removeAll(frequencyIds);
            samplingFrequencyIds.removeAll(folderIds);
            if (samplingFrequencyIds.size() > 0) {
                repository.logicDeleteById(samplingFrequencyIds, new Date());
            }

            deleteFlowCalibration2Frequency(null, null, frequencyList);
        }
    }

    @Override
    public void deleteFrequency(List<DtoSamplingFrequency> frequencyList,
                                List<DtoSampleFolder> folderList,
                                String projectId,
                                List<String> samplingFrequencyIds) {
        if (frequencyList.size() > 0) {
            //调用频次类的删除，会检查是否需要删除对应点位数据
            samplingFrequencyService.logicDeleteById(samplingFrequencyIds);

            //删除点位样品相关信息
            schemeService.deleteSamplingFrequency(projectId, samplingFrequencyIds);

            //清除项目指标缓存
            projectTestService.removeProjectTest(projectId);

            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            proService.sendProMessage(EnumProAction.方案删除次数, projectId);
                        }
                    }
            );
            Set<String> typeIds = folderList.stream().map(DtoSampleFolder::getSampleTypeId).collect(Collectors.toSet());
            List<DtoSampleType> samTypeList = sampleTypeService.findAll(typeIds);

            //#region 操作日志
            List<DtoLog> logList = new ArrayList<>();
            for (DtoSamplingFrequency frequency : frequencyList) {
                Optional<DtoSampleFolder> folder = folderList.stream().filter(p -> p.getId().equals(frequency.getSampleFolderId())).findFirst();
                AtomicReference<String> watchSpot = new AtomicReference<>("");
                AtomicReference<String> typeName = new AtomicReference<>("");
                folder.ifPresent(f -> {
                    watchSpot.set(f.getWatchSpot());
                    Optional<DtoSampleType> samType = samTypeList.stream().filter(p -> p.getId().equals(f.getSampleTypeId())).findFirst();
                    samType.ifPresent(s -> {
                        typeName.set(s.getTypeName());
                    });
                });

                String comment = String.format("删除了点位%s(%s)第%d周期第%d次信息。", watchSpot.get(), typeName.get(), frequency.getPeriodCount(), frequency.getTimePerPeriod());
                DtoLog log = new DtoLog();
                log.setId(UUIDHelper.NewID());
                log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                log.setOperateTime(new Date());
                log.setOperateInfo(EnumLogOperateType.删除次数.toString());
                log.setLogType(EnumLogType.方案点位信息.getValue());
                log.setObjectId(projectId);
                log.setObjectType(EnumLogObjectType.方案.getValue());
                log.setComment(comment);
                log.setOpinion("");
                log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                log.setRemark("");
                logList.add(log);
            }
            newLogService.createLog(logList, EnumLogType.方案点位信息.getValue());
            //#endregion
        }
    }

    /**
     * 根据周期频次id获取工作单id
     *
     * @param samplingFrequencyIds 周期频次id
     * @return 工作单id
     */
    @Override
    public List<String> getWorkSheetIdsBySamplingFrequencyId(List<String> samplingFrequencyIds) {
        List<String> sampleIds = sampleRepository.findBySamplingFrequencyIdIn(samplingFrequencyIds).stream()
                .map(DtoSample::getId).collect(Collectors.toList());
        List<String> worksheetfolderIds = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds).stream()
                .map(DtoAnalyseData::getWorkSheetFolderId).distinct().collect(Collectors.toList());
        return worksheetfolderIds;
    }

    /**
     * 修改频次指标
     *
     * @param samplingFrequencyId 频次id
     * @param analyseItemIds      分析项目id集合
     * @return 修改后的指标
     */
    @Transactional
    @Override
    public DtoSampleFolderTemp modifySamplingFrequencyTest(String samplingFrequencyId, List<String> analyseItemIds, List<String> testIds) {
        //获取点位频次
        DtoSamplingFrequency frequency = samplingFrequencyService.findOne(samplingFrequencyId);
        DtoSampleFolder folder = repository.findOne(frequency.getSampleFolderId());
        DtoSampleType samType = sampleTypeService.findOne(folder.getSampleTypeId());
        //获取所有的测试项目，用于填充每个样品的测试项目
//        List<DtoAnalyzeItem> analyzeItems = analyzeItemRepository.findAll(analyseItemIds);
        List<DtoTest> testListOfIds = new ArrayList<>();
        List<String> itemIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(testIds)) {
            testListOfIds = testService.findRedisByIds(testIds);
            itemIds = testListOfIds.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
            analyseItemIds.addAll(itemIds);
            analyseItemIds = analyseItemIds.stream().distinct().collect(Collectors.toList());
        }
        //查询出对应频次下的所有指标信息
        List<DtoSamplingFrequencyTest> sftList = samplingFrequencyTestRepository.findBySamplingFrequencyId(samplingFrequencyId);
        //将不包含在修改后指标的次数筛选出来，予以移除
        List<String> finalAnalyseItemIds = analyseItemIds;
        List<DtoSamplingFrequencyTest> removeList = sftList.stream().filter(p -> !finalAnalyseItemIds.contains(p.getAnalyseItemId())).collect(Collectors.toList());
        //筛选出原先存在的指标集合
        List<String> existAnalyseItemIds = sftList.stream().map(DtoSamplingFrequencyTest::getAnalyseItemId).distinct().collect(Collectors.toList());
        //修改后的指标移除原先的指标，保留下来的为需新增的指标
        analyseItemIds.removeAll(existAnalyseItemIds);

        //获取原来指标中的测试项目id集合，并获取测试项目信息
        List<String> testIdsOfFrequency = sftList.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findRedisByIds(testIdsOfFrequency);
        List<DtoSample> samples = sampleRepository.findBySamplingFrequencyId(samplingFrequencyId);

        List<String> sampleIdList = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        if (removeList.size() > 0) {
            //删除频次指标
            samplingFrequencyTestService.delete(removeList);

            //根据待删除的频次指标筛选出待删除的测试项目，塞到对应频次上
            List<String> removeTestIds = removeList.stream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toList());
            //移除比对数据
            sampleJudgeDataService.deleteJudgeDataByTest(sampleIdList, removeTestIds, Boolean.TRUE);
            //获取当前频次下所有的分析数据对象
            List<DtoAnalyseData> frequencyAnaDataList = analyseDataRepository.findBySampleIdIn(sampleIdList);
            //过滤出当前频次下要删除的测试项目对应的所有样品的分析数据
            List<DtoAnalyseData> removeAnaDataList = frequencyAnaDataList.stream().filter(p -> removeTestIds.contains(p.getTestId())).collect(Collectors.toList());
            List<String> removeAnaDataIdList = removeAnaDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            //按照objectId为分析数据id，删除要删除的测试项目对应的评价标准记录
            if (StringUtil.isNotEmpty(removeAnaDataIdList)) {
                List<DtoEvaluationRecord> removeAnaIdEvaRcdList = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(removeAnaDataIdList,
                        EnumPRO.EnumEvaluationType.分析数据.getValue(), EnumPRO.EnumEvaluationPlan.分析数据.getValue());
                if (StringUtil.isNotEmpty(removeAnaIdEvaRcdList)) {
                    evaluationRecordRepository.logicDeleteById(removeAnaIdEvaRcdList.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
                }
            }
            //获取当前点位下所有的频次指标对象
            List<DtoSamplingFrequencyTest> samplingFrequencyTestList = samplingFrequencyTestRepository.findBySampleFolderId(folder.getId());
            //找到删除 removeList 后所有剩余的测试项目id列表
            List<String> remainTestIdList = samplingFrequencyTestList.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
            //遍历 removeTestIds 判断其中的每一个测试项目是否任然包含在当前点位对应的测试项目中(一个点位下的多个频次可能会配置同一个测试项目,
            // 删除一个频次中对应的测试项目并不能保证其它频次中不会配置相同的测试项目),过滤出不包含在当前点位中的测试项目
            List<String> actRmvTestIdList = removeTestIds.stream().filter(p -> !remainTestIdList.contains(p)).collect(Collectors.toList());
            //按照objectId为点位id，以及对应的测试项目id删除对应的评价标准记录
            List<DtoEvaluationRecord> removeFldIdEvaRcdList = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(Collections.singletonList(folder.getId()),
                    EnumPRO.EnumEvaluationType.点位.getValue(), EnumPRO.EnumEvaluationPlan.实际.getValue());
            removeFldIdEvaRcdList = removeFldIdEvaRcdList.stream().filter(p -> actRmvTestIdList.contains(p.getTestId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(removeFldIdEvaRcdList)) {
                evaluationRecordRepository.logicDeleteById(removeFldIdEvaRcdList.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }

            DtoAnalyseDataDelete dtoAnalyseDataDelete = new DtoAnalyseDataDelete(folder.getProjectId(), samples, removeTestIds, new ArrayList<>(), false);
            List<String> reportSampleIds = proService.deleteAnalyseDataBySample(dtoAnalyseDataDelete);

            List<DtoLog> logList = new ArrayList<>();
            for (DtoSample sam : samples) {
                if (reportSampleIds.contains(sam.getId())) {
                    DtoLog log = new DtoLog();
                    log.setId(UUIDHelper.NewID());
                    log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                    log.setOperateTime(new Date());
                    log.setOperateInfo(EnumLogOperateType.删除检测项目.toString());
                    log.setLogType(EnumLogType.样品数据.getValue());
                    log.setObjectId(sam.getId());
                    log.setObjectType(EnumLogObjectType.样品.getValue());
                    log.setComment(String.format("删除了样品%s检测项目:%s", sampleService.getSampleName(sam, ""),
                            removeList.stream().map(DtoSamplingFrequencyTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining("、"))));
                    log.setOpinion("");
                    log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                    log.setRemark("");
                    logList.add(log);
                }
            }
            newLogService.createLog(logList, EnumLogType.样品数据.getValue());

            //频次指标中移除对应删除的指标
            sftList.removeAll(removeList);

            //获取原来指标中保留下的测试项目id集合，并筛选对应的测试项目信息
            List<String> filterTestIds = sftList.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
            testList = testList.stream().filter(p -> filterTestIds.contains(p.getId())).collect(Collectors.toList());
            String comment = String.format("删除了指标：点位%s(%s)第%d周期第%d次删除了指标%s。", folder.getWatchSpot(), samType.getTypeName(), frequency.getPeriodCount(), frequency.getTimePerPeriod(),
                    String.join("、", removeList.stream().map(DtoSamplingFrequencyTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList())));
            newLogService.createLog(folder.getProjectId(), comment, "", EnumLogType.方案点位数据信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.删除检测项目.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }

        if (analyseItemIds.size() > 0) {
            //存在新增指标则需进行添加
            DtoSampleFolder sampleFolder = repository.findOne(frequency.getSampleFolderId());
            List<DtoTest> tests;
            if (StringUtil.isNotEmpty(testListOfIds)) {
                tests = testListOfIds;
                analyseItemIds.removeIf(itemIds::contains);
                tests.addAll(this.getDefaultTest(sampleFolder.getProjectId(), sampleFolder.getSampleTypeId(), analyseItemIds));
            } else {
                tests = this.getDefaultTest(sampleFolder.getProjectId(), sampleFolder.getSampleTypeId(), analyseItemIds);
            }
            //处理比对数据
            if (StringUtil.isNotNull(samType.getCheckType())) {
                Set<String> receiveIds = samples.stream().map(DtoSample::getReceiveId)
                        .filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toSet());
                if (receiveIds.size() > 0) {
                    List<DtoSample> allSampleList = sampleRepository.findByReceiveIdIn(receiveIds);
                    List<String> allSamIds = allSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
                    List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(allSamIds);
                    //新增比对数据
                    createJudgeData(folder.getProjectId(), analyseItemIds, receiveIds, tests, samples, allSampleList, analyseDataList);
                }
            }
            List<DtoSamplingFrequency> frequencyList = new ArrayList<>();
            frequencyList.add(frequency);
            List<DtoSamplingFrequencyTest> newSfts = this.addSamplingFrequencyTest(frequencyList, sftList, tests, false);
            sftList.addAll(newSfts);
            //将两块测试项目合并在一起，排除原有testId
            testList.addAll(tests.stream().filter(p -> !testIdsOfFrequency.contains(p.getId())).collect(Collectors.toList()));
            String comment = String.format("增加了指标：点位%s(%s)第%d周期第%d次增加了指标%s。", folder.getWatchSpot(), samType.getTypeName(), frequency.getPeriodCount(), frequency.getTimePerPeriod(),
                    tests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining("、")));
            newLogService.createLog(folder.getProjectId(), comment, "", EnumLogType.方案点位数据信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.增加检测项目.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }

        List<DtoSampleFolder> folders = Collections.singletonList(folder);
        List<DtoSamplingFrequency> frequencyList = Collections.singletonList(frequency);
        Map<String, DtoSampleType> samTypeMap = new HashMap<>();
        samTypeMap.put(samType.getId(), samType);

        DtoSampleFolderTemp temp = this.getFolderSchemes(folder.getProjectId(), folders, frequencyList, sftList, testList, samTypeMap).get(0).getChildren().get(0);

        List<DtoSample> sampleList = sampleRepository.findByIsDeletedFalseAndSamplingFrequencyId(samplingFrequencyId);

//        List<String> analyzeItemNamesList;
//        if (StringUtil.isNotEmpty(analyzeItems)) {
//            analyzeItemNamesList = analyzeItems.stream()
//                    .map(DtoAnalyzeItem::getAnalyzeItemName).collect(Collectors.toList());
//            String analyzeItemNames = String.join(",",analyzeItemNamesList);
//            for (DtoSample sample : sampleList) {
//                sample.setRedAnalyzeItems(analyzeItemNames);
//            }
//        }
//        sampleService.update(sampleList);
        DtoSample sample = StringUtil.isEmpty(sampleList) ? null : sampleList.get(0);
        DtoProject project = projectRepository.findOne(folder.getProjectId());
        //排除原样，纠正质控样的状态
        List<String> assSampleIds = sampleIdList.stream().filter(p -> !sample.getId().equals(p)).collect(Collectors.toList());
        if (assSampleIds.size() > 0) {
            proService.checkSample(assSampleIds);
        }
        proService.checkSample(Collections.singletonList(sample), project);
//
        //保证事务提交之后才执行
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        //清除项目指标缓存
                        projectTestService.removeProjectTest(folder.getProjectId());
                        proService.sendProMessage(EnumProAction.方案修改指标, folder.getProjectId());
                    }
                }
        );
        return temp;
    }

    /**
     * 批量添加测试项目
     *
     * @param samplingFrequencyIds 频次id集合
     * @param analyseItemIds       分析项目id集合
     * @return 添加的频次指标
     */
    @Transactional
    @Override
    public List<DtoProjectTest> addFrequencyAnalyseItems(List<String> samplingFrequencyIds, List<String> analyseItemIds, List<String> testIds) {
        List<DtoProjectTest> ptList = new ArrayList<>();
        List<DtoTest> testList = new ArrayList<>();
        List<String> itemIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(testIds)) {
            testList = testService.findRedisByIds(testIds);
            itemIds = testList.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
            analyseItemIds.addAll(itemIds);
            analyseItemIds = analyseItemIds.stream().distinct().collect(Collectors.toList());
        }
        List<DtoSample> sampleList = sampleRepository.findBySamplingFrequencyIdIn(samplingFrequencyIds);
        Set<String> receiveIds = sampleList.stream().map(DtoSample::getReceiveId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toSet());
        List<DtoSample> allSampleList = new ArrayList<>();
        List<DtoAnalyseData> analyseDataList = new ArrayList<>();
        if (receiveIds.size() > 0) {
            allSampleList = sampleRepository.findByReceiveIdIn(receiveIds);
            List<String> allSamIds = allSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(allSamIds);
        }
        if (StringUtil.isNotNull(samplingFrequencyIds) && samplingFrequencyIds.size() > 0 &&
                StringUtil.isNotNull(analyseItemIds) && analyseItemIds.size() > 0) {
            List<String> redFolderNames = new ArrayList<>();
            List<DtoSamplingFrequency> frequencyList = samplingFrequencyService.findAll(samplingFrequencyIds);
            frequencyList.sort(Comparator.comparing(DtoSamplingFrequency::getSampleFolderId).thenComparing(DtoSamplingFrequency::getPeriodCount).thenComparing(DtoSamplingFrequency::getTimePerPeriod));
            List<DtoSampleFolder> sampleFolderList = repository.findAll(frequencyList.stream().map(DtoSamplingFrequency::getSampleFolderId).distinct().collect(Collectors.toList()));
            DtoSampleType samType = sampleTypeService.findOne(sampleFolderList.get(0).getSampleTypeId());
            for (DtoSamplingFrequency frequency : frequencyList) {
                redFolderNames.add(String.format("%s(%s)第%d周期第%d次", sampleFolderList.stream().filter(p -> p.getId().equals(frequency.getSampleFolderId())).map(DtoSampleFolder::getWatchSpot).findFirst().orElse(""),
                        samType.getTypeName(), frequency.getPeriodCount(), frequency.getTimePerPeriod()));
            }
            List<DtoTest> tests = new ArrayList<>();
            if (StringUtil.isNotEmpty(testList)) {
                tests = testList;
                analyseItemIds.removeIf(itemIds::contains);
                tests.addAll(this.getDefaultTest(sampleFolderList.get(0).getProjectId(), sampleFolderList.get(0).getSampleTypeId(), analyseItemIds));
            } else {
                tests = this.getDefaultTest(sampleFolderList.get(0).getProjectId(), sampleFolderList.get(0).getSampleTypeId(), analyseItemIds);
            }

            List<DtoSamplingFrequencyTest> frequencyTests = samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(samplingFrequencyIds);
            this.addSamplingFrequencyTest(frequencyList, frequencyTests, tests, true);

            List<String> totalTestIds = tests.stream().map(DtoTest::getParentId).filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).distinct().collect(Collectors.toList());
            if (totalTestIds.size() > 0) {//若存在总称的测试项目id集合，进行添加返回到前端
                List<DtoTest> totalTests = testService.findRedisByIds(totalTestIds).stream().filter((p -> p.getIsTotalTest() && p.getIsShowTotalTest())).collect(Collectors.toList());
                if (totalTests.size() > 0) {
                    tests.addAll(totalTests);
                }
            }

            for (DtoTest test : tests) {
                DtoProjectTest pt = new DtoProjectTest(test);
                pt.setProjectId(sampleFolderList.get(0).getProjectId());
                pt.setSampleTypeId(sampleFolderList.get(0).getSampleTypeId());
                pt.setBigSampleTypeId(samType.getParentId());
                pt.setSampleTypeName(samType.getTypeName());
                ptList.add(pt);
            }

            String comment = String.format("增加了指标：点位%s增加了指标%s。", String.join(",", redFolderNames),
                    tests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining("、")));
            newLogService.createLog(sampleFolderList.get(0).getProjectId(), comment, "", EnumLogType.方案点位数据信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.增加检测项目.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

            //更新项目指标缓存
            projectTestService.modifyProjectTest(sampleFolderList.get(0).getProjectId(), ptList);

            //新增指标 比对数据
            if (StringUtil.isNotNull(samType.getCheckType())) {
                createJudgeData(sampleFolderList.get(0).getProjectId(), analyseItemIds, receiveIds, tests, sampleList, allSampleList, analyseDataList);
            }
            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            proService.sendProMessage(EnumProAction.方案新增指标, sampleFolderList.get(0).getProjectId());
                        }
                    }
            );
        }
        return ptList;
    }

    @Override
    public void checkSampleCode() {
        List<DtoSample> sampleList = sampleRepository.findAll();
        sampleList = sampleList.stream().filter(p -> !p.getIsDeleted()).collect(Collectors.toList());
        sampleList.forEach(p -> {
            String oneFolder = String.format("(%d-%d)", p.getCycleOrder(), p.getTimesOrder());
            String twoFolder = String.format("(%d-%d-%d)", p.getCycleOrder(), p.getTimesOrder(), p.getSampleOrder());
            String folderName = p.getRedFolderName().replace(oneFolder, twoFolder);
            p.setRedFolderName(folderName);
        });
        sampleService.save(sampleList);
    }

    private void deleteFrequencyDetail(String projectId, List<DtoSamplingFrequency> frequencyList) {
        List<String> frequencyIds = frequencyList.stream().map(DtoSamplingFrequency::getId).collect(Collectors.toList());
        samplingFrequencyRepository.logicDeleteById(frequencyIds);
        samplingFrequencyTestRepository.deleteBySamplingFrequencyIdIn(frequencyIds);

        //删除点位样品相关信息
        schemeService.deleteSamplingFrequency(projectId, frequencyIds);

        //清除项目指标缓存
        projectTestService.removeProjectTest(projectId);

        List<String> sampleIds = sampleRepository.findBySamplingFrequencyIdIn(frequencyIds).stream()
                .map(DtoSample::getId).collect(Collectors.toList());
        //删除比对数据
        sampleJudgeDataService.deleteJudgeDataByTest(sampleIds, new ArrayList<>(), Boolean.TRUE);
    }

    private void createJudgeData(String projectId, List<String> analyseItemIds, Collection<String> receiveIds,
                                 List<DtoTest> tests, List<DtoSample> sampleList, List<DtoSample> allSampleList,
                                 List<DtoAnalyseData> analyseDataList) {
        if (receiveIds.size() > 0) {
            DtoProject project = projectRepository.findOne(projectId);
            //测试项目
            List<DtoCompareJudge> compareJudges = compareJudgeRepository.findByAnalyzeItemIdIn(analyseItemIds);
            Set<String> aItemIds = compareJudges.stream().map(DtoCompareJudge::getAnalyzeItemId).collect(Collectors.toSet());
            List<DtoReceiveSampleRecord> recordList = receiveSampleRecordRepository.findAll(receiveIds);
            recordList.forEach(p -> {
                List<String> folderIds = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
                folderIds.forEach(f -> {
                    List<DtoSample> samList = sampleList.stream().filter(sam -> sam.getReceiveId().equals(p.getId())
                            && f.equals(sam.getSampleFolderId())).collect(Collectors.toList());
                    List<DtoSample> allSamList = allSampleList.stream().filter(sam -> sam.getReceiveId().equals(p.getId())
                            && f.equals(sam.getSampleFolderId())).collect(Collectors.toList());
                    List<String> samIds = allSamList.stream().map(DtoSample::getId).collect(Collectors.toList());
                    //送样单中存在的指标
                    List<String> allTestIds = analyseDataList.stream().filter(a -> samIds.contains(a.getSampleId()))
                            .map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
                    List<String> tIds = tests.stream().filter(t -> aItemIds.contains(t.getAnalyzeItemId()))
                            .map(DtoTest::getId).collect(Collectors.toList());
                    sampleJudgeDataService.createJudgeDataBySampleTest(samList, tIds, allTestIds, project);
                });
            });
        }
    }

    /**
     * 批量添加测试项目
     *
     * @param frequencyList 频次集合(带频次下的测试项目集合)
     * @param isCheck       是否状态纠正
     * @return 添加的频次指标
     */
    private List<DtoSamplingFrequencyTest> addSamplingFrequencyTest(List<DtoSamplingFrequency> frequencyList,
                                                                    List<DtoSamplingFrequencyTest> frequencyTestList,
                                                                    List<DtoTest> tests,
                                                                    Boolean isCheck) {
        List<DtoSamplingFrequencyTest> newSftList = new ArrayList<>();
        for (DtoSamplingFrequency frequency : frequencyList) {
            for (DtoTest test : tests) {
                if (frequencyTestList.stream().noneMatch(p -> p.getAnalyseItemId().equals(test.getAnalyzeItemId()) && p.getSamplingFrequencyId().equals(frequency.getId()))) {
                    DtoSamplingFrequencyTest newFrequencyTest = new DtoSamplingFrequencyTest(test);
                    newFrequencyTest.setSampleFolderId(frequency.getSampleFolderId());
                    newFrequencyTest.setSamplingFrequencyId(frequency.getId());
                    newSftList.add(newFrequencyTest);
                }
            }
        }

        samplingFrequencyTestRepository.save(newSftList);
        if (StringUtil.isNotEmpty(tests)) {
            testService.incrementOrderNum(tests.stream().map(DtoTest::getId).collect(Collectors.toList()));
        }

        String sampleFolderId = frequencyList.get(0).getSampleFolderId();
        DtoSampleFolder folder = repository.findOne(sampleFolderId);
        DtoProject project = projectRepository.findOne(folder.getProjectId());

        List<DtoSample> samples = sampleRepository.findBySamplingFrequencyIdIn(frequencyList.stream().map(DtoSamplingFrequency::getId).collect(Collectors.toList()));
        Set<String> receiveIds = samples.stream().map(DtoSample::getReceiveId).filter(receiveId -> !receiveId.equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toSet());
        List<DtoReceiveSampleRecord> records = receiveIds.size() > 0 ? receiveSampleRecordRepository.findAll(receiveIds) : new ArrayList<>();

        DtoAnalyseDataAdd addDto = new DtoAnalyseDataAdd(project, samples, records, tests, isCheck);
        List<String> reportSampleIds = analyseDataService.addAnalyseData(addDto);
        List<DtoLog> logList = new ArrayList<>();
        for (DtoSample sam : samples) {
            if (reportSampleIds.contains(sam.getId())) {
                DtoLog log = new DtoLog();
                log.setId(UUIDHelper.NewID());
                log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                log.setOperateTime(new Date());
                log.setOperateInfo(EnumLogOperateType.增加检测项目.toString());
                log.setLogType(EnumLogType.样品数据.getValue());
                log.setObjectId(sam.getId());
                log.setObjectType(EnumLogObjectType.样品.getValue());
                log.setComment(String.format("增加了样品%s检测项目:%s", sampleService.getSampleName(sam, ""),
                        tests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining("、"))));
                log.setOpinion("");
                log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                log.setRemark("");
                logList.add(log);
            }
        }
        if (logList.size() > 0) {
            newLogService.createLog(logList, EnumLogType.样品数据.getValue());
        }
        return newSftList;
    }

    /**
     * 批量删除测试项目
     *
     * @param frequencyIds   频次id集合
     * @param analyseItemIds 分析项目id集合
     */
    @Transactional
    @Override
    public void deleteFrequencyAnalyseItems(List<String> frequencyIds, List<String> analyseItemIds) {
        if (StringUtil.isNotNull(frequencyIds) && frequencyIds.size() > 0 &&
                StringUtil.isNotNull(analyseItemIds) && analyseItemIds.size() > 0) {
            List<DtoSamplingFrequencyTest> sftList = samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(frequencyIds);
            List<String> testIds = sftList.stream().filter(p -> analyseItemIds.contains(p.getAnalyseItemId())).map(DtoSamplingFrequencyTest::getTestId)
                    .collect(Collectors.toList());
            deleteFrequencyTests(frequencyIds, testIds);
        }
    }

    @Override
    @Transactional
    public void deleteSampleTests(List<String> sampleIds, List<String> testIds) {
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findAll(sampleIds) : new ArrayList<>();
        List<DtoSample> yySampleList = sampleList.stream().filter(p -> p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())).collect(Collectors.toList());
        List<DtoSample> qcSampleList = sampleList.stream().filter(p -> !p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.原样.getValue())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(yySampleList)) {
            List<String> samplingFrequencyIds = yySampleList.stream().map(DtoSample::getSamplingFrequencyId).collect(Collectors.toList());
            deleteFrequencyTests(samplingFrequencyIds, testIds);
        }
        if (StringUtil.isNotEmpty(qcSampleList)) {
            qcSampleList.forEach(p -> proService.deleteAnalyseData(p.getId(), testIds));
        }
    }

    private void deleteFrequencyTests(List<String> frequencyIds, List<String> delTestIds) {
        if (StringUtil.isNotNull(frequencyIds) && frequencyIds.size() > 0 &&
                StringUtil.isNotNull(delTestIds) && delTestIds.size() > 0) {
            //移除点位频次Id为空Id的
            if (frequencyIds.contains(UUIDHelper.GUID_EMPTY)) {
                frequencyIds.removeIf(UUIDHelper.GUID_EMPTY::equals);
            }
            //查询出对应频次下的所有指标信息
            List<DtoSamplingFrequencyTest> sftList = samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(frequencyIds);
            sftList = sftList.stream().filter(p -> delTestIds.contains(p.getTestId())).collect(Collectors.toList());
            //删除频次指标
            samplingFrequencyTestService.delete(sftList);

            List<String> testIds = sftList.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
            List<DtoTest> tests = testService.findRedisByIds(testIds);
            List<DtoSamplingFrequency> frequencyList = samplingFrequencyService.findAll(frequencyIds);
            frequencyList.sort(Comparator.comparing(DtoSamplingFrequency::getSampleFolderId).thenComparing(DtoSamplingFrequency::getPeriodCount).thenComparing(DtoSamplingFrequency::getTimePerPeriod));
            List<DtoSampleFolder> sampleFolderList = repository.findAll(frequencyList.stream().map(DtoSamplingFrequency::getSampleFolderId).distinct().collect(Collectors.toList()));

            List<String> sampleFolderIdList = sampleFolderList.stream().map(DtoSampleFolder::getId).collect(Collectors.toList());
            List<DtoSample> allSampleList = sampleRepository.findBySampleFolderIdIn(sampleFolderIdList);
            //过滤出要删除的频次id下的所有样品
            List<DtoSample> freqSampleList = allSampleList.stream().filter(p -> frequencyIds.contains(p.getSamplingFrequencyId())).collect(Collectors.toList());
            List<String> freqSampleIdList = freqSampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            //过滤出要删除的频次id下的所有样品对应的分析数据
            List<DtoAnalyseData> freqAnaDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(freqSampleIdList);
            //过滤出分析数据中测试项目id包含在 testIds 内的分析数据
            List<DtoAnalyseData> removeAnaDataList = freqAnaDataList.stream().filter(p -> testIds.contains(p.getTestId())).collect(Collectors.toList());
            List<String> removeAnaDataIdList = removeAnaDataList.stream().map(DtoAnalyseData::getId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(removeAnaDataList)) {
                List<DtoEvaluationRecord> rmvAnaEvaRcdList = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(removeAnaDataIdList,
                        EnumPRO.EnumEvaluationType.分析数据.getValue(), EnumPRO.EnumEvaluationPlan.分析数据.getValue());
                if (StringUtil.isNotEmpty(rmvAnaEvaRcdList)) {
                    evaluationRecordRepository.logicDeleteById(rmvAnaEvaRcdList.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
                }
            }
            List<DtoSamplingFrequencyTest> allSmpFreqTstList = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIdList);
            //按照点位id分组
            Map<String, List<DtoSamplingFrequencyTest>> fldId2FreqTestListMap = allSmpFreqTstList.stream()
                    .collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSampleFolderId));
            //放置要按照单位id删除的评价记录列表
            List<DtoEvaluationRecord> rmvFldEvaRcdList = new ArrayList<>();
            //点位id和要删除的测试项目id列表的映射
            Map<String, List<String>> fldId2RmvTstIdListMap = new HashMap<>();
            //遍历每个点位，筛选出要删除的测试项目，以及对应的评价标准记录
            for (String folderId : sampleFolderIdList) {
                List<DtoSamplingFrequencyTest> freqTestList = fldId2FreqTestListMap.get(folderId);
                if (StringUtil.isNotEmpty(freqTestList)) {
                    List<String> remainTestIdList = freqTestList.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
                    List<String> loopActRmvTstIdList = testIds.stream().filter(p -> !remainTestIdList.contains(p)).collect(Collectors.toList());
                    if (StringUtil.isNotEmpty(loopActRmvTstIdList)) {
                        fldId2RmvTstIdListMap.put(folderId, loopActRmvTstIdList);
                    }
                } else {
                    fldId2RmvTstIdListMap.put(folderId, testIds);
                }
            }
            //按点位id为objectId，找到所有评价记录
            List<DtoEvaluationRecord> allEvaRcdList = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(fldId2RmvTstIdListMap.keySet(),
                    EnumPRO.EnumEvaluationType.点位.getValue(), EnumPRO.EnumEvaluationPlan.实际.getValue());
            //按照点位id分组
            Map<String, List<DtoEvaluationRecord>> fldId2EvaRcdListMap = allEvaRcdList.stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getObjectId));
            for (Map.Entry<String, List<DtoEvaluationRecord>> entry : fldId2EvaRcdListMap.entrySet()) {
                String fldId = entry.getKey();
                if (fldId2RmvTstIdListMap.containsKey(fldId)) {
                    List<String> loopRmvTstIdList = fldId2RmvTstIdListMap.get(fldId);
                    //过滤出当前点位下要删除的评价记录列表
                    rmvFldEvaRcdList.addAll(entry.getValue().stream().filter(p -> loopRmvTstIdList.contains(p.getTestId())).collect(Collectors.toList()));
                }
            }
            if (StringUtil.isNotEmpty(rmvFldEvaRcdList)) {
                evaluationRecordRepository.logicDeleteById(rmvFldEvaRcdList.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }

            List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleFolderList.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList()));
            List<String> redFolderNames = new ArrayList<>();
            for (DtoSamplingFrequency frequency : frequencyList) {
                DtoSampleFolder thisFolder = sampleFolderList.stream().filter(p -> p.getId().equals(frequency.getSampleFolderId())).findFirst().orElse(null);
                redFolderNames.add(String.format("%s(%s)第%d周期第%d次", StringUtil.isNotNull(thisFolder) ? thisFolder.getWatchSpot() : "",
                        StringUtil.isNotNull(thisFolder) ? samTypes.stream().filter(p -> p.getId().equals(thisFolder.getSampleTypeId())).map(DtoSampleType::getTypeName).findFirst().orElse("") : "",
                        frequency.getPeriodCount(), frequency.getTimePerPeriod()));
            }

            String projectId = sampleFolderList.get(0).getProjectId();

            String comment = String.format("删除了指标：点位%s删除了指标%s。", String.join(",", redFolderNames),
                    String.join("、", tests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList())));
            newLogService.createLog(sampleFolderList.get(0).getProjectId(), comment, "", EnumLogType.方案点位数据信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.删除检测项目.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");

            //清除项目指标缓存
            projectTestService.removeProjectTest(projectId);

            List<DtoSample> samples = sampleRepository.findBySamplingFrequencyIdIn(frequencyIds);
            //删除比对数据
            sampleJudgeDataService.deleteJudgeDataByTest(samples.stream().map(DtoSample::getId).collect(Collectors.toList()), testIds, Boolean.TRUE);
            DtoAnalyseDataDelete dtoAnalyseDataDelete = new DtoAnalyseDataDelete(projectId, samples, delTestIds, new ArrayList<>(), true);
            //调用方法进行删除分析数据
            List<String> reportSampleIds = proService.deleteAnalyseDataBySample(dtoAnalyseDataDelete);
            List<DtoLog> logList = new ArrayList<>();
            for (DtoSample sam : samples) {
                if (reportSampleIds.contains(sam.getId())) {
                    DtoLog log = new DtoLog();
                    log.setId(UUIDHelper.NewID());
                    log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                    log.setOperateTime(new Date());
                    log.setOperateInfo(EnumLogOperateType.删除检测项目.toString());
                    log.setLogType(EnumLogType.样品数据.getValue());
                    log.setObjectId(sam.getId());
                    log.setObjectType(EnumLogObjectType.样品.getValue());
                    log.setComment(String.format("删除了样品%s检测项目:%s", sampleService.getSampleName(sam, ""),
                            tests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining("、"))));
                    log.setOpinion("");
                    log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                    log.setRemark("");
                    logList.add(log);
                }
            }
            newLogService.createLog(logList, EnumLogType.样品数据.getValue());

            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            proService.sendProMessage(EnumProAction.方案删除指标, projectId);
                        }
                    }
            );
        }
    }

    /**
     * 批量设置分包
     *
     * @param samplingFrequencyIds 频次id集合
     * @param projectTests         项目指标集合
     */
    @Transactional
    @Override
    public void sub(List<String> samplingFrequencyIds, List<DtoProjectTest> projectTests) {
        if (StringUtil.isNotNull(samplingFrequencyIds) && samplingFrequencyIds.size() > 0 &&
                StringUtil.isNotNull(projectTests) && projectTests.size() > 0) {
            List<DtoSamplingFrequency> frequencyList = samplingFrequencyRepository.findAll(samplingFrequencyIds);
            frequencyList.sort(Comparator.comparing(DtoSamplingFrequency::getSampleFolderId).thenComparing(DtoSamplingFrequency::getPeriodCount).thenComparing(DtoSamplingFrequency::getTimePerPeriod));
            List<DtoSampleFolder> sampleFolderList = repository.findAll(frequencyList.stream().map(DtoSamplingFrequency::getSampleFolderId).distinct().collect(Collectors.toList()));
            List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleFolderList.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList()));

            List<String> subAnalyseItemIds = projectTests.stream().filter(DtoProjectTest::getIsOutsourcing).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList());
            List<String> subNotAnalyseItemIds = projectTests.stream().filter(p -> !p.getIsOutsourcing()
                    && !p.getIsSamplingOut()).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList());
            List<String> subSamplingAnalyseItemIds = projectTests.stream().filter(DtoProjectTest::getIsSamplingOut)
                    .map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList());
            List<DtoSamplingFrequencyTest> sftList = samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(samplingFrequencyIds);
            List<DtoSamplingFrequencyTest> changeSftList = new ArrayList<>();
            List<String> allNotSubAnalyseItemIds = new ArrayList<>(subNotAnalyseItemIds);
            allNotSubAnalyseItemIds.addAll(subSamplingAnalyseItemIds);
            List<String> allNotSamlingAnalyseItemIds = new ArrayList<>(subNotAnalyseItemIds);
            allNotSamlingAnalyseItemIds.addAll(subAnalyseItemIds);
            for (DtoSamplingFrequencyTest sft : sftList) {
                if ((sft.getIsOutsourcing() && allNotSubAnalyseItemIds.contains(sft.getAnalyseItemId()))
                        || ((!sft.getIsOutsourcing() && !allNotSubAnalyseItemIds.contains(sft.getAnalyseItemId())))) {
                    sft.setIsOutsourcing(!sft.getIsOutsourcing());
                    changeSftList.add(sft);
                }
                if ((sft.getIsSamplingOut() && allNotSamlingAnalyseItemIds.contains(sft.getAnalyseItemId()))
                        || ((!sft.getIsSamplingOut() && !allNotSamlingAnalyseItemIds.contains(sft.getAnalyseItemId())))) {
                    sft.setIsSamplingOut(!sft.getIsSamplingOut());
                    changeSftList.remove(sft);
                    changeSftList.add(sft);
                }
            }
            if (changeSftList.size() > 0) {
                List<DtoSample> samples = sampleRepository.findBySamplingFrequencyIdIn(samplingFrequencyIds);
                analyseDataService.subAnalyseData(samples,
                        projectTests.stream().filter(DtoProjectTest::getIsOutsourcing).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList()),
                        projectTests.stream().filter(DtoProjectTest::getIsSamplingOut).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList()),
                        projectTests.stream().filter(p -> !p.getIsOutsourcing() && !p.getIsSamplingOut()).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList()));
                List<String> changeFrequencyIds = changeSftList.stream().map(DtoSamplingFrequencyTest::getSamplingFrequencyId).distinct().collect(Collectors.toList());
                List<String> changeTestIds = changeSftList.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
                List<DtoSample> changeSamples = StringUtil.isNotEmpty(changeFrequencyIds) ? sampleRepository.findBySamplingFrequencyIdIn(changeFrequencyIds) : new ArrayList<>();
                List<String> changeSampleIds = changeSamples.stream().map(DtoSample::getId).collect(Collectors.toList());
                List<DtoAnalyseData> changeAnaDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(changeSampleIds);
                if (changeAnaDataList.stream().anyMatch(a -> changeTestIds.contains(a.getTestId()) && !UUIDHelper.GUID_EMPTY.equals(a.getWorkSheetFolderId()))) {
                    throw new BaseException("样品已经创建检测单，禁止修改！");
                }
                List<DtoSample> associatedSamples = StringUtil.isNotEmpty(changeSampleIds) ? sampleService.getLocalAssociateSample(changeSampleIds) : new ArrayList<>();
                final List<Integer> filterQcTypes = Arrays.asList(EnumLIM.EnumQCType.仪器空白.getValue(), EnumLIM.EnumQCType.现场空白.getValue());
                associatedSamples.removeIf(s -> filterQcTypes.contains(s.getQcType()));
                if (StringUtil.isNotEmpty(associatedSamples)) {
                    analyseDataService.subAnalyseData(associatedSamples,
                            projectTests.stream().filter(DtoProjectTest::getIsOutsourcing).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList()),
                            projectTests.stream().filter(DtoProjectTest::getIsSamplingOut).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList()),
                            projectTests.stream().filter(p -> !p.getIsOutsourcing() && !p.getIsSamplingOut()).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList()));
                }
                samplingFrequencyTestService.update(changeSftList);
                if (changeSftList.stream().anyMatch(DtoSamplingFrequencyTest::getIsOutsourcing)) {
                    subToLog(frequencyList, changeSftList, sampleFolderList, samTypes, "采测分包");
                }
                if (changeSftList.stream().anyMatch(DtoSamplingFrequencyTest::getIsSamplingOut)) {
                    subToLog(frequencyList, changeSftList, sampleFolderList, samTypes, "分析分包");
                }
                if (changeSftList.stream().anyMatch(p -> !p.getIsOutsourcing())
                        && changeSftList.stream().anyMatch(p -> !p.getIsSamplingOut())) {
                    subToLog(frequencyList, changeSftList, sampleFolderList, samTypes, "取消分包");
                }
                //保证事务提交之后才执行
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                if (samples.size() > 0) {
                                    proService.sendProMessage(EnumProAction.方案设置分包, samples.get(0).getProjectId());
                                }
                            }
                        }
                );
            }
        }
    }

    /**
     * 更换指标方法
     *
     * @param dto 方法修改传输对象
     */
    @Transactional
    @Override
    public List<DtoProjectTest> changeAnalyzeMethod(DtoSchemeMethodChange dto) {
        //定义返回结果集
        List<DtoProjectTest> ptList = new ArrayList<>();
        List<DtoSampleFolder> folderList = repository.findByProjectIdAndSampleTypeId(dto.getProjectId(), dto.getSampleTypeId());
        if (folderList.size() > 0) {
            List<DtoSample> samples = sampleRepository.findByProjectId(dto.getProjectId());
            //获取点位
            List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findByProjectId(dto.getProjectId());
            //根据检测类型过滤点位
            sampleFolders = sampleFolders.stream().filter(p -> dto.getSampleTypeId().equals(p.getSampleTypeId())).collect(Collectors.toList());
            List<String> sampleFolderIds;
            //根据点位获取未修改的周期测试项目绑定数据
            List<DtoSamplingFrequencyTest> samplingFrequencyTests;
            //获取所有使用新的分析方法的分析项目id
            List<DtoTest> testsOfNewMethod = testRepository.findByAnalyzeMethodIdAndIsDeletedFalse(dto.getAnalyzeMethodId());
            //判断是修改当前项目下所有使用此分析方法的测试项目还是只修改选中数据
            if (dto.getIsChangeAll()) {
                //获取所有所有使用新分析方法的分析项目
                List<String> analyzeItemIdsOfNewMethod = testsOfNewMethod.parallelStream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());
                sampleFolderIds = sampleFolders.parallelStream().map(DtoSampleFolder::getId).collect(Collectors.toList());
                samplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
                //获取所有相同分析方法的数据
                samplingFrequencyTests = samplingFrequencyTests.parallelStream()
                        .filter(s -> analyzeItemIdsOfNewMethod.contains(s.getAnalyseItemId()) && dto.getOldAnalyzeMethodId().equals(s.getAnalyzeMethodId())).collect(Collectors.toList());

            } else {
                sampleFolderIds = folderList.parallelStream().map(DtoSampleFolder::getId).collect(Collectors.toList());
                samplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
                //根据分析方法和分析项目获取选中的数据
                samplingFrequencyTests = samplingFrequencyTests.parallelStream()
                        .filter(t -> t.getAnalyzeMethodId().equals(dto.getOldAnalyzeMethodId())
                                && t.getAnalyseItemId().equals(dto.getAnalyseItemId())).collect(Collectors.toList());
            }
            //获取到需要修改方法的频次id
            List<String> updateFrequencyIds = samplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getSamplingFrequencyId).distinct().collect(Collectors.toList());
            //根据检测类型，频次id过滤样品
            samples = samples.stream().filter(p -> dto.getSampleTypeId().equals(p.getSampleTypeId())
                    && updateFrequencyIds.contains(p.getSamplingFrequencyId())).collect(Collectors.toList());
            //修改完成筛选的数据
            //修改方法的时候，需要把已配置的评价删除 -- 2022-01-21
            List<String> oldTestIds = samplingFrequencyTests.stream().map(DtoSamplingFrequencyTest::getTestId).distinct().collect(Collectors.toList());
            testService.decrementOrderNum(oldTestIds);
            //找到需要配置的评价
            List<DtoEvaluationRecord> evaluationRecordList = evaluationRecordRepository.findByObjectIdInAndTestIdIn(sampleFolderIds, oldTestIds);
            if (evaluationRecordList.size() > 0) {
                //删除已配置的评价
                evaluationRecordRepository.logicDeleteById(evaluationRecordList.stream().map(DtoEvaluationRecord::getId).collect(Collectors.toList()), new Date());
            }

            this.changeSamplingFrequencyTests(dto.getAnalyzeMethodId(), samplingFrequencyTests, testsOfNewMethod);
            samplingFrequencyTestService.update(samplingFrequencyTests);
            //获取修改过后的测试项目
            List<String> newTestIds = samplingFrequencyTests.parallelStream().map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toList());
            testsOfNewMethod = testsOfNewMethod.parallelStream().filter(t -> newTestIds.contains(t.getId())).collect(Collectors.toList());
            //修改分析数据
            analyseDataService.changeAnalyseMethod(samples, testsOfNewMethod, dto.getSampleTypeId());
            List<String> totalTestIds = testsOfNewMethod.stream().map(DtoTest::getParentId)
                    .filter(parentId -> !UUIDHelper.GUID_EMPTY.equals(parentId)).distinct().collect(Collectors.toList());
            if (totalTestIds.size() > 0) {
                List<DtoTest> totalTests = testService.findRedisByIds(totalTestIds).stream().filter(DtoTest::getIsShowTotalTest).collect(Collectors.toList());
                if (totalTests.size() > 0) {
                    testsOfNewMethod.addAll(totalTests);
                }
            }
            DtoSampleType samType = sampleTypeService.findOne(dto.getSampleTypeId());
            for (DtoTest test : testsOfNewMethod) {
                DtoProjectTest pt = new DtoProjectTest(test);
                pt.setProjectId(dto.getProjectId());
                pt.setSampleTypeId(dto.getSampleTypeId());
                pt.setBigSampleTypeId(samType.getParentId());
                pt.setSampleTypeName(samType.getTypeName());
                ptList.add(pt);
            }

            //清除项目指标缓存
            projectTestService.removeProjectTest(dto.getProjectId());
            testService.incrementOrderNum(testsOfNewMethod.stream().map(DtoTest::getId).collect(Collectors.toList()));

            List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
            //比对数据修改
            sampleJudgeDataService.updateJudgeDataByTest(sampleIds, oldTestIds, testsOfNewMethod.stream()
                    .map(DtoTest::getId).distinct().collect(Collectors.toList()));

            String comment = String.format("更换了检测类型%s下的指标%s的方法，原方法为%s，修改为%s", samType.getTypeName(),
                    testsOfNewMethod.stream().map(DtoTest::getRedAnalyzeItemName).collect(Collectors.joining("、")),
                    samplingFrequencyTests.get(0).getRedAnalyzeMethodName(),
                    testsOfNewMethod.get(0).getRedAnalyzeMethodName());

            newLogService.createLog(dto.getProjectId(), comment, "", EnumLogType.方案检测方法信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.修改检测方法.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
            //保证事务提交之后才执行
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            proService.sendProMessage(EnumProAction.方案修改方法, dto.getProjectId());
                        }
                    }
            );
        }
        return ptList;
    }

    /**
     * 修改周期分析项目绑定表
     *
     * @param newMethodId            新的分析方法
     * @param samplingFrequencyTests 需要修改的数据集合
     * @param testsOfNewMethod       新的测试项目
     */
    private void changeSamplingFrequencyTests(String newMethodId, List<DtoSamplingFrequencyTest> samplingFrequencyTests, List<DtoTest> testsOfNewMethod) {
        for (DtoSamplingFrequencyTest samplingFrequencyTest : samplingFrequencyTests) {
            samplingFrequencyTest.setAnalyzeMethodId(newMethodId);
            Optional<DtoTest> testOfNewMethod = testsOfNewMethod.parallelStream()
                    .filter(t -> t.getAnalyzeItemId().equals(samplingFrequencyTest.getAnalyseItemId())).findFirst();
            testOfNewMethod.ifPresent(t -> {
                samplingFrequencyTest.setRedAnalyzeMethodName(t.getRedAnalyzeMethodName());
                samplingFrequencyTest.setIsCompleteField(t.getIsCompleteField());
                samplingFrequencyTest.setTestId(t.getId());
                samplingFrequencyTest.setRedCountryStandard(t.getRedCountryStandard());
                samplingFrequencyTest.setSamplingMethodId(t.getSamplingMethodId());
                samplingFrequencyTest.setIsOutsourcing(t.getIsOutsourcing());
                samplingFrequencyTest.setIsSamplingOut(t.getIsSamplingOut());
            });
        }
    }

    /**
     * 核对方案
     *
     * @param projectId 项目id
     */
    @Transactional
    @Override
    public void checkScheme(String projectId) {
        Map<String, Object> values = new HashMap<>();
        PageBean<DtoSamplingFrequencyTest> pbsft = new PageBean<>();
        // 多表关联查询返回自定义字段
        pbsft.setEntityName("DtoSamplingFrequencyTest sft, DtoSamplingFrequency sf, DtoSampleFolder s");
        pbsft.setSelect("select sft,s.sampleTypeId");
        pbsft.setRowsPerPage(Integer.MAX_VALUE);
        pbsft.addCondition(" and sft.samplingFrequencyId = sf.id");
        pbsft.addCondition(" and sf.sampleFolderId = s.id");
        pbsft.addCondition(" and s.projectId = :projectId");
        values.put("projectId", projectId);

        comRepository.findByPage(pbsft, values);

        List<DtoSamplingFrequencyTest> datas = pbsft.getData();
        List<DtoSamplingFrequencyTest> newDatas = new ArrayList<>();

        Iterator<DtoSamplingFrequencyTest> ite = datas.iterator();
        // 循环迭代获取JPQL中查询返回的属性

        while (ite.hasNext()) {
            Object obj = ite.next();
            Object[] objs = (Object[]) obj;
            // 按查询顺序下标一一获取
            DtoSamplingFrequencyTest sft = (DtoSamplingFrequencyTest) objs[0];
            String sampleTypeId = (String) objs[1];
            sft.setSampleTypeId(sampleTypeId);
            newDatas.add(sft);
        }

        if (newDatas.size() > 0) {
            List<DtoAnalyseData> analyseDataList = analyseDataService.findByProjectId(projectId);
            analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleTypeId, Collectors.toList())).forEach((sampleTypeId, dataList) -> {
                dataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getAnalyseItemId, Collectors.toList())).forEach((analyseItemId, itemDataList) -> {
                    List<String> methodIds = itemDataList.stream().map(DtoAnalyseData::getAnalyzeMethodId).distinct().collect(Collectors.toList());
                    if (methodIds.size() == 1 && newDatas.stream().noneMatch(p -> p.getSampleTypeId().equals(sampleTypeId)
                            && p.getAnalyseItemId().equals(analyseItemId) && p.getAnalyseItemId().equals(methodIds.get(0)))) {
                        List<DtoSamplingFrequencyTest> sftList = newDatas.stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId) && p.getAnalyseItemId().equals(analyseItemId)).collect(Collectors.toList());
                        for (DtoSamplingFrequencyTest sft : sftList) {
                            sft.setTestId(itemDataList.get(0).getTestId());
                            sft.setRedCountryStandard(itemDataList.get(0).getRedCountryStandard());
                            sft.setAnalyzeMethodId(itemDataList.get(0).getAnalyzeMethodId());
                            sft.setRedAnalyzeMethodName(itemDataList.get(0).getRedAnalyzeMethodName());
                            samplingFrequencyTestService.update(sft);
                        }
                    }
                });
            });
        }
    }

    //#region 方案私有方法
    @Override
    public List<DtoSampleFolderTemp> getFolderSchemes(String projectId,
                                                      List<DtoSampleFolder> folderList,
                                                      List<DtoSamplingFrequency> frequencyList,
                                                      List<DtoSamplingFrequencyTest> sftList,
                                                      List<DtoTest> tests,
                                                      Map<String, DtoSampleType> samTypeMap) {
        Map<String, List<DtoSamplingFrequency>> frequencyMap = frequencyList.stream().collect(Collectors.groupingBy(DtoSamplingFrequency::getSampleFolderId));
        Map<String, DtoTest> testMap = tests.stream().collect(Collectors.toMap(DtoTest::getId, test -> test));

        Map<String, List<DtoSamplingFrequencyTest>> sftMap = sftList.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSamplingFrequencyId));
        List<String> fixedPointIds = folderList.stream().map(DtoSampleFolder::getFixedPointId)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<DtoFixedpoint> fixedPoints = new ArrayList<>();
        if (StringUtil.isNotEmpty(fixedPointIds)) {
            fixedPoints = fixedpointRepository.findAll(fixedPointIds);
        }
        //获取点位中的样品
        List<String> sampleFolderIds = folderList.parallelStream().map(DtoSampleFolder::getId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = sampleRepository.findByIsDeletedFalseAndSampleFolderIdInOrderById(sampleFolderIds);
        //遍历点位获取点位方案信息
        List<DtoSampleFolderTemp> sampleFolders = new ArrayList<>();
        List<String> fixedPointIdList = folderList.stream().map(DtoSampleFolder::getFixedPointId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<DtoFixedpoint> fixedPointList = StringUtil.isNotEmpty(fixedPointIdList) ? fixedpointRepository.findAll(fixedPointIdList) : new ArrayList<>();
        Map<String, DtoFixedpoint> fixedPointMap = fixedPointList.stream().collect(Collectors.toMap(DtoFixedpoint::getId, dto -> dto));
        List<String> enterpriseIdList = fixedPointList.stream().filter(p -> StringUtil.isNotEmpty(p.getEnterpriseId())
                && !UUIDHelper.GUID_EMPTY.equals(p.getEnterpriseId())).map(DtoFixedpoint::getEnterpriseId)
                .distinct().collect(Collectors.toList());
        List<DtoEnterprise> enterpriseList = StringUtil.isNotEmpty(enterpriseIdList) ? enterpriseRepository.findAll(enterpriseIdList) : new ArrayList<>();
        Map<String, DtoEnterprise> enterpriseMap = enterpriseList.stream().collect(Collectors.toMap(DtoEnterprise::getId, dto -> dto));

        for (DtoSampleFolder folder : folderList) {
            DtoSampleFolderTemp temp = this.getFolderScheme(projectId, folder, samTypeMap, frequencyMap, sftMap, testMap, samples, sftList);
            String customerId = UUIDHelper.GUID_EMPTY;
            String customerName = "";
            if (fixedPointMap.containsKey(temp.getFixedPointId())) {
                DtoFixedpoint fixedPoint = fixedPointMap.get(temp.getFixedPointId());
                DtoEnterprise enterprise = enterpriseMap.get(fixedPoint.getEnterpriseId());
                if (StringUtil.isNotNull(enterprise)) {
                    customerName = enterprise.getName();
                    customerId = enterprise.getId();
                }
            }
            temp.setCustomerId(customerId);
            temp.setCustomerName(customerName);
            //获取例行监测配置点位的排序值
            if (UUIDHelper.GUID_EMPTY.equals(temp.getFixedPointId())) {
                temp.setOrderNum(0);
            } else {
                Optional<DtoFixedpoint> fixedpointOptional = fixedPoints.stream().filter(f -> f.getId().equals(folder.getFixedPointId())).findFirst();
                fixedpointOptional.ifPresent(o -> temp.setOrderNum(o.getOrderNum()));
            }
            Optional<DtoSample> sampleOptional = samples.parallelStream().filter(s -> s.getSampleFolderId().equals(folder.getId())).findFirst();
            sampleOptional.ifPresent(s -> {
                temp.setLon(s.getLon());
                temp.setLat(s.getLat());
            });
            //关于例行任务如果没有经纬度话先去环境质量点位上取
            if (StringUtil.isEmpty(temp.getLon()) || StringUtil.isEmpty(temp.getLat())) {
                Optional<DtoFixedpoint> fixedpointOptional = fixedPoints.parallelStream().filter(f -> f.getId().equals(temp.getFixedPointId())).findFirst();
                fixedpointOptional.ifPresent(f -> {
                    temp.setLon(f.getLon());
                    temp.setLat(f.getLat());
                });
            }
            sampleFolders.add(temp);
        }
        //获取中文排序工具
        Collator collator = Collator.getInstance();
        Comparator<DtoSampleFolderTemp> watchSpot = getComparator(sampleFolders);
        //先按照检测大类排序，再按照检测小类名称排序，再按照排序值倒序，再按照点位名称
        sampleFolders.sort(Comparator.comparing(DtoSampleFolderTemp::getCustomerId)
                .thenComparing(DtoSampleFolderTemp::getBigSampleTypeId)
                .thenComparing(DtoSampleFolderTemp::getSampleTypeName, collator)
                .thenComparing(DtoSampleFolderTemp::getOrderNum, Comparator.reverseOrder())
                .thenComparing(watchSpot));
        return sampleFolders;
    }

    /**
     * 定义比较器
     *
     * @param folderTemp 排序的Map
     * @return 比较器
     */
    private Comparator<DtoSampleFolderTemp> getComparator(List<DtoSampleFolderTemp> folderTemp) {
        return (a, b) -> SortUtil.compareString(a.getWatchSpot(), b.getWatchSpot());
    }

    /**
     * 获取点位方案
     *
     * @param projectId    项目id
     * @param folder       点位
     * @param samTypeMap   检测类型map
     * @param frequencyMap 频次map
     * @param sftMap       频次指标map
     * @param testMap      测试项目map
     * @param sftList      所有的频次指标
     * @return 返回点位方案
     */
    private DtoSampleFolderTemp getFolderScheme(String projectId, DtoSampleFolder folder,
                                                Map<String, DtoSampleType> samTypeMap,
                                                Map<String, List<DtoSamplingFrequency>> frequencyMap,
                                                Map<String, List<DtoSamplingFrequencyTest>> sftMap,
                                                Map<String, DtoTest> testMap, List<DtoSample> allSamples,
                                                List<DtoSamplingFrequencyTest> sftList) {
        DtoSampleFolderTemp temp = new DtoSampleFolderTemp();
        temp.setId(folder.getId());
        temp.setSampleFolderId(folder.getId());
        temp.setDepth(1);
        temp.setSampleTypeId(folder.getSampleTypeId());
        temp.setFixedPointId(folder.getFixedPointId());
        if (samTypeMap.containsKey(temp.getSampleTypeId())) {
            DtoSampleType samType = samTypeMap.get(temp.getSampleTypeId());
            temp.setBigSampleTypeId(samType.getParentId());
            temp.setSampleTypeName(samType.getTypeName());
            temp.setWatchSpot(folder.getWatchSpot());
            List<DtoSampleFolderTemp> children = this.getFolderChildren(projectId, temp, frequencyMap, sftMap, testMap, allSamples);
            temp.setChildren(children);
            //写入点位的周期、频次及样品数
            this.setFolderNumInfo(temp, children);
            temp.setLabel(temp.getWatchSpot());
            //写入点位的指标信息
            this.setFolderTest(temp, sftList, children);
        }
        return temp;
    }

    /**
     * 获取点位的周期次数方案
     *
     * @param projectId    项目id
     * @param folderTemp   点位方案
     * @param frequencyMap 频次map
     * @param sftMap       频次指标map
     * @param testMap      测试项目map
     * @return 返回周期次数方案
     */
    private List<DtoSampleFolderTemp> getFolderChildren(String projectId, DtoSampleFolderTemp folderTemp,
                                                        Map<String, List<DtoSamplingFrequency>> frequencyMap,
                                                        Map<String, List<DtoSamplingFrequencyTest>> sftMap,
                                                        Map<String, DtoTest> testMap, List<DtoSample> allSamples) {
        List<DtoSampleFolderTemp> tempList = new ArrayList<>();
        if (frequencyMap.containsKey(folderTemp.getId())) {
            List<DtoSamplingFrequency> frequencys = frequencyMap.get(folderTemp.getId());
            Map<Integer, List<DtoSamplingFrequency>> periodMap = frequencys.stream()
                    .collect(Collectors.groupingBy(DtoSamplingFrequency::getPeriodCount));
            for (Integer period : periodMap.keySet()) {
                DtoSampleFolderTemp temp = new DtoSampleFolderTemp();
                temp.setId(String.format("%s_%d", folderTemp.getId(), period));
                temp.setSampleFolderId(folderTemp.getSampleFolderId());
                temp.setDepth(2);
                temp.setPeriodCount(period);
                temp.setSampleTypeId(folderTemp.getSampleTypeId());
                temp.setBigSampleTypeId(folderTemp.getBigSampleTypeId());
                temp.setSampleTypeName(folderTemp.getSampleTypeName());
                temp.setWatchSpot(folderTemp.getWatchSpot());
                temp.setLabel(String.format("第%d周期", period));
                List<DtoSamplingFrequency> times = periodMap.get(period).stream()
                        .sorted(Comparator.comparing(DtoSamplingFrequency::getTimePerPeriod))
                        .collect(Collectors.toList());
                List<DtoSampleFolderTemp> childTemps = new ArrayList<>();
                Map<Integer, List<DtoSamplingFrequency>> timePeriodMap = times.stream()
                        .collect(Collectors.groupingBy(DtoSamplingFrequency::getTimePerPeriod));
                for (Integer timePeriod : timePeriodMap.keySet()) {
                    DtoSampleFolderTemp timeTemp = new DtoSampleFolderTemp();
                    timeTemp.setId(String.format("%s_%d_%d", folderTemp.getId(), period, timePeriod));
                    timeTemp.setSampleFolderId(folderTemp.getSampleFolderId());
                    timeTemp.setDepth(3);
                    timeTemp.setPeriodCount(period);
                    timeTemp.setTimePerPeriod(timePeriod);
                    timeTemp.setLabel(String.format("第%d批次", timePeriod));
                    timeTemp.setSampleTypeId(folderTemp.getSampleTypeId());
                    timeTemp.setBigSampleTypeId(folderTemp.getBigSampleTypeId());
                    timeTemp.setSampleTypeName(folderTemp.getSampleTypeName());
                    timeTemp.setWatchSpot(folderTemp.getWatchSpot());
                    timeTemp.setChildren(getTimesChild(projectId, folderTemp, timePeriod, period, frequencyMap, sftMap,
                            testMap, allSamples));
                    childTemps.add(timeTemp);
                }
                temp.setChildren(childTemps);
                tempList.add(temp);
            }
            tempList.sort(Comparator.comparing(DtoSampleFolderTemp::getPeriodCount));
        }
        return tempList;
    }

    private List<DtoSampleFolderTemp> getTimesChild(String projectId, DtoSampleFolderTemp folderTemp, Integer time,
                                                    Integer period, Map<String, List<DtoSamplingFrequency>> frequencyMap,
                                                    Map<String, List<DtoSamplingFrequencyTest>> sftMap,
                                                    Map<String, DtoTest> testMap, List<DtoSample> allSamples) {
        List<DtoSampleFolderTemp> tempList = new ArrayList<>();
        if (frequencyMap.containsKey(folderTemp.getId())) {
            List<DtoSamplingFrequency> frequencys = frequencyMap.get(folderTemp.getId()).stream().filter(p -> p.getPeriodCount().equals(period)
                    && p.getTimePerPeriod().equals(time)).collect(Collectors.toList());
            Map<Integer, List<DtoSamplingFrequency>> timePeriodMap = frequencys.stream().collect(Collectors.groupingBy(DtoSamplingFrequency::getSamplePerTime));
            List<DtoSample> sampleList = allSamples.stream().filter(p -> p.getSampleFolderId().equals(folderTemp.getSampleFolderId()) &&
                    p.getCycleOrder().equals(period) && p.getTimesOrder().equals(time) &&
                    (!p.getIsDeleted() || EnumPRO.EnumSampleStatus.样品作废.name().equals(p.getStatus()))).collect(Collectors.toList());
            for (Integer timePeriod : timePeriodMap.keySet()) {
                DtoSampleFolderTemp temp = new DtoSampleFolderTemp();
                String frequencyId = timePeriodMap.get(timePeriod).get(0).getId();
                temp.setId(frequencyId);
                temp.setSampleFolderId(folderTemp.getSampleFolderId());
                temp.setDepth(4);
                temp.setPeriodCount(period);
                temp.setTimePerPeriod(time);
                temp.setSamplePeriod(timePeriod);
                temp.setSampleTypeId(folderTemp.getSampleTypeId());
                temp.setBigSampleTypeId(folderTemp.getBigSampleTypeId());
                temp.setSampleTypeName(folderTemp.getSampleTypeName());
                temp.setWatchSpot(folderTemp.getWatchSpot());
                DtoSample sample = sampleList.stream().filter(p -> time.equals(p.getTimesOrder())
                        && period.equals(p.getCycleOrder()) && timePeriod.equals(p.getSampleOrder())
                        && !p.getSampleCategory().equals(EnumPRO.EnumSampleCategory.比对评价样.getValue()))
                        .findFirst().orElse(null);
                temp.setLabel(String.format("第%d个样", timePeriod));
                if (StringUtil.isNotNull(sample)) {
                    if (StringUtil.isNotEmpty(sample.getCode())) {
                        temp.setLabel(String.format("第%d个样（%s）", timePeriod, sample.getCode()));
                    }
                    if (EnumPRO.EnumSampleStatus.样品作废.name().equals(sample.getStatus())) {
                        temp.setLabel(String.format("%s%s", temp.getLabel(), "【作废】"));
                    }
                }

                Map<String, DtoProjectTest> projectTestMap = new HashMap<>();
                List<String> outsourcingItemIds = new ArrayList<>();
                List<String> samplingOutItemIds = new ArrayList<>();
                if (timePeriodMap.containsKey(timePeriod)) {
                    if (sftMap.containsKey(frequencyId)) {
                        for (DtoSamplingFrequencyTest sft : sftMap.get(frequencyId)) {
                            DtoProjectTest pt = new DtoProjectTest(sft);
                            pt.setProjectId(projectId);
                            pt.setSampleTypeId(folderTemp.getSampleTypeId());
                            pt.setBigSampleTypeId(folderTemp.getBigSampleTypeId());
                            pt.setSampleTypeName(folderTemp.getSampleTypeName());
                            if (testMap.containsKey(sft.getTestId())) {
                                DtoTest test = testMap.get(sft.getTestId());
                                pt.setOrderNum(test.getOrderNum());
                            }
                            projectTestMap.put(sft.getAnalyseItemId(), pt);

                            temp.getItemIds().add(sft.getAnalyseItemId());
                            if (sft.getIsOutsourcing()) {
                                outsourcingItemIds.add(sft.getAnalyseItemId());
                            }
                            if (sft.getIsSamplingOut()) {
                                samplingOutItemIds.add(sft.getAnalyseItemId());
                            }
                        }
                    }
                }
                temp.setItem(projectTestMap);
                temp.setOutsourcingItemIds(outsourcingItemIds);
                temp.setSamplingOutItemIds(samplingOutItemIds);
                tempList.add(temp);
            }
            tempList.sort(Comparator.comparing(DtoSampleFolderTemp::getSamplePeriod));
        }
        return tempList;
    }

    /**
     * 写入第一层点位的频次、周期、样品数
     *
     * @param temp     第一层的点位信息
     * @param children 子方案信息列表
     */
    private void setFolderNumInfo(DtoSampleFolderTemp temp, List<DtoSampleFolderTemp> children) {
        if (children.size() > 0) {
            Integer maxPeriod = children.get(children.size() - 1).getPeriodCount();
            temp.setPeriodCount(maxPeriod);
            List<Integer> timeList = new ArrayList<>();
            children.forEach(p -> {
                if (p.getChildren().size() > 0) {
                    timeList.add(p.getChildren().get(p.getChildren().size() - 1).getTimePerPeriod());
                }
            });
            List<DtoSampleFolderTemp> timeChildren = new ArrayList<>();
            children.forEach(p -> {
                timeChildren.addAll(p.getChildren());
            });
            List<Integer> sampleList = new ArrayList<>();
            timeChildren.forEach(p -> {
                if (p.getChildren().size() > 0) {
                    sampleList.add(p.getChildren().get(p.getChildren().size() - 1).getSamplePeriod());
                }
            });
            Integer sampleNum = sampleList.stream().reduce(0, (sum, item) -> sum + item);
            temp.setSampleNum(sampleNum);
            Integer maxTime = Collections.max(timeList);
            temp.setTimePerPeriod(maxTime);
            Integer maxSample = Collections.max(sampleList);
            temp.setSamplePeriod(maxSample);
        } else {
            temp.setPeriodCount(0);
            temp.setTimePerPeriod(0);
            temp.setSamplePeriod(0);
            temp.setSampleNum(0);
        }
    }

    /**
     * 写入第一层点位的指标信息
     *
     * @param temp     第一层的点位信息
     * @param sftList  频次指标信息
     * @param children 子方案信息列表
     */
    private void setFolderTest(DtoSampleFolderTemp temp, List<DtoSamplingFrequencyTest> sftList, List<DtoSampleFolderTemp> children) {
        Map<String, DtoProjectTest> projectTestMap = new HashMap<>();
        Integer folderTestCount = sftList.stream().filter(p -> p.getSampleFolderId().equals(temp.getSampleFolderId())).map(DtoSamplingFrequencyTest::getTestId).collect(Collectors.toSet()).size();
        List<String> outsourcingItemIds = new ArrayList<>();
        List<String> samplingOutItemIds = new ArrayList<>();
        for (DtoSampleFolderTemp periodTemp : children) {
            for (DtoSampleFolderTemp timeTemp : periodTemp.getChildren()) {
                for (DtoSampleFolderTemp sampleTemp : timeTemp.getChildren()) {
                    for (String analyseItemId : sampleTemp.getItem().keySet()) {
                        if (!projectTestMap.containsKey(analyseItemId)) {
                            projectTestMap.put(analyseItemId, sampleTemp.getItem().get(analyseItemId));
                            if (sampleTemp.getItem().get(analyseItemId).getIsOutsourcing()) {
                                outsourcingItemIds.add(analyseItemId);
                            } else if (sampleTemp.getItem().get(analyseItemId).getIsSamplingOut()) {
                                samplingOutItemIds.add(analyseItemId);
                            }
                            temp.getScheme().put(analyseItemId, sampleTemp.getItem().get(analyseItemId).getMapInfo());
                        }
                    }
                    if (folderTestCount.equals(projectTestMap.size())) {
                        break;
                    }
                }
            }
            if (folderTestCount.equals(projectTestMap.size())) {
                break;
            }
        }
        temp.setItem(projectTestMap);
        temp.setOutsourcingItemIds(outsourcingItemIds);
        temp.setSamplingOutItemIds(samplingOutItemIds);
    }

    /**
     * 获取默认的测试项目
     *
     * @param projectId      项目id
     * @param sampleTypeId   检测类型id
     * @param analyseItemIds 分析项目id集合
     * @return 测试项目集合
     */
    private List<DtoTest> getDefaultTest(String projectId, String sampleTypeId, List<String> analyseItemIds) {
        List<DtoTest> projectTests = projectTestService.getProjectTest(projectId, sampleTypeId, analyseItemIds);
        if (projectTests.size() < analyseItemIds.size()) {
            List<String> existItemIds = projectTests.stream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());
            analyseItemIds.removeAll(existItemIds);
            DtoSampleType samType = sampleTypeService.findOne(sampleTypeId);
            projectTests.addAll(testService.findCommonTestBySampleTypeIdAndAnalyzeItemIdIn(samType.getParentId(), analyseItemIds));
        }
        return projectTests;
    }

    //#endregion

    //#endregion

    /**
     * 添加点位(外部送样)
     *
     * @param dto 样品实体
     * @return 返回对应点位频次
     */
    @Transactional
    @Override
    public DtoSamplingFrequency addOutsideFolder(String fixedPointId, DtoSample dto, List<DtoTest> testList) {
        //外部送样没有点位编号，可以直接进行替换
        String watchSpot = dto.getRedFolderName().replace(String.format("(%d-%d-%d)", dto.getCycleOrder(), dto.getTimesOrder(), dto.getSampleOrder()), "");
        List<DtoSampleFolder> sampleFolderList = repository.findByProjectIdAndSampleTypeIdAndWatchSpot(dto.getProjectId(), dto.getSampleTypeId(), watchSpot);
        DtoSampleFolder sampleFolder = StringUtil.isNotEmpty(sampleFolderList) ? sampleFolderList.get(0) : null;
        if (StringUtil.isNull(sampleFolder)) {
            sampleFolder = new DtoSampleFolder();
            sampleFolder.setProjectId(dto.getProjectId());
            sampleFolder.setFixedPointId(fixedPointId);
            sampleFolder.setWatchSpot(watchSpot);
            sampleFolder.setSampleTypeId(dto.getSampleTypeId());
        }
        // 经度纬度
        sampleFolder.setLat(dto.getLat());
        sampleFolder.setLon(dto.getLon());
        repository.save(sampleFolder);

        DtoSamplingFrequency frequency = new DtoSamplingFrequency();
        frequency.setSampleFolderId(sampleFolder.getId());
        frequency.setPeriodCount(dto.getCycleOrder());
        frequency.setTimePerPeriod(dto.getTimesOrder());
        frequency.setSamplePerTime(dto.getSampleOrder());
        samplingFrequencyRepository.save(frequency);

        List<DtoSamplingFrequencyTest> sftList = new ArrayList<>();
        for (DtoTest test : testList) {
            DtoSamplingFrequencyTest sft = new DtoSamplingFrequencyTest(test);
            sft.setSamplingFrequencyId(frequency.getId());
            sft.setSampleFolderId(sampleFolder.getId());
            sftList.add(sft);
        }
        if (sftList.size() > 0) {
            samplingFrequencyTestRepository.save(sftList);
        }
        frequency.setWatchSpot(sampleFolder.getWatchSpot());
        frequency.setRedFolderName(this.getFolderName(frequency, sampleFolder.getWatchSpot(), sampleFolder.getFolderCode()));
        return frequency;
    }

    /**
     * 添加点位(质控)
     *
     * @param dto 样品实体
     * @return 返回对应点位频次
     */
    @Transactional
    @Override
    public DtoSamplingFrequency addQMFolder(DtoSample dto) {
        //外部送样没有点位编号，可以直接进行替换
        String watchSpot = dto.getRedFolderName().replace(String.format("(%d-%d)", dto.getCycleOrder(), dto.getTimesOrder()), "");
        DtoSampleFolder sampleFolder = new DtoSampleFolder();
        sampleFolder.setProjectId(dto.getProjectId());
        sampleFolder.setWatchSpot(watchSpot);
        sampleFolder.setSampleTypeId(dto.getSampleTypeId());
        repository.save(sampleFolder);

        DtoSamplingFrequency frequency = new DtoSamplingFrequency();
        frequency.setSampleFolderId(sampleFolder.getId());
        frequency.setPeriodCount(dto.getCycleOrder());
        frequency.setTimePerPeriod(dto.getTimesOrder());
        samplingFrequencyRepository.save(frequency);

        frequency.setWatchSpot(sampleFolder.getWatchSpot());
        frequency.setRedFolderName(this.getFolderName(frequency, sampleFolder.getWatchSpot(), sampleFolder.getFolderCode()));
        return frequency;
    }

    /**
     * 根据项目id返回点位树
     *
     * @param projectId    项目id
     * @param sampleTypeId 检测类型id
     */
    @Override
    public List<TreeNode> getSampleFolderTree(String projectId, String sampleTypeId) {
        List<DtoSampleFolder> folderList;
        if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(sampleTypeId)) {
            folderList = repository.findByProjectIdAndSampleTypeId(projectId, sampleTypeId);
        } else {
            folderList = repository.findByProjectId(projectId);
        }
        //过滤掉没有对应样品的点位
        List<String> folderIdList = folderList.stream().map(DtoSampleFolder::getId).distinct().collect(Collectors.toList());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(folderIdList) ? sampleRepository.findBySampleFolderIdIn(folderIdList) : new ArrayList<>();
        List<String> samplefolderIdList = sampleList.stream().map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<String> noSampleFolderIdList = folderIdList.stream().filter(p -> !samplefolderIdList.contains(p)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(noSampleFolderIdList)) {
            folderList = folderList.stream().filter(p -> !noSampleFolderIdList.contains(p.getId())).collect(Collectors.toList());
        }
        folderList.sort(Comparator.comparing(DtoSampleFolder::getWatchSpot));

        List<String> sampleTypeIds = folderList.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypeList = sampleTypeService.findRedisByIds(sampleTypeIds);
        List<String> bigSampleTypeIds = samTypeList.stream().map(DtoSampleType::getParentId).distinct().collect(Collectors.toList());
        samTypeList.addAll(sampleTypeService.findRedisByIds(bigSampleTypeIds));
        //将list转为map
        Map<String, DtoSampleType> samTypeMap = samTypeList.stream().collect(Collectors.toMap(DtoSampleType::getId, st -> st));
        //所有点位已配置的评价标准
        List<DtoEvaluationRecord> records = new ArrayList<>();
        if (StringUtil.isNotEmpty(folderIdList)) {
            records = evaluationRecordRepository.findByObjectIdInAndObjectTypeAndFolderPlan(folderIdList, EnumPRO.EnumEvaluationType.点位.getValue(), EnumPRO.EnumEvaluationPlan.实际.getValue());
        }
        //按照点位id分组
        Map<String, List<DtoEvaluationRecord>> fldId2EvaRcdListMap = records.stream().collect(Collectors.groupingBy(DtoEvaluationRecord::getObjectId));

        List<TreeNode> list = new ArrayList<>();
        folderList.stream().collect(Collectors.groupingBy(DtoSampleFolder::getSampleTypeId, Collectors.toList())).forEach((samTypeId, childList) -> {
            TreeNode node = new TreeNode();
            node.setId(samTypeId);
            node.setParentId(UUIDHelper.GUID_EMPTY);
            DtoSampleType samType = samTypeMap.get(samTypeId);
            node.setLabel(samType.getTypeName());//检测类型名称
            node.setParentId(samType.getParentId());//检测大类id
            DtoSampleType bigSamType = samTypeMap.get(samType.getParentId());
            node.setType(bigSamType.getTypeName());//检测大类名称
            node.setIsLeaf(false);
            node.setChildren(new ArrayList<>());
            for (DtoSampleFolder child : childList) {
                TreeNode childNode = new TreeNode();
                childNode.setId(child.getId());
                childNode.setParentId(samTypeId);
                childNode.setLabel(child.getWatchSpot());
                childNode.setIsLeaf(true);
                childNode.setExtent1("0");
                //如果当前遍历的点位上已经配置了评价标准则打上标记
                if (fldId2EvaRcdListMap.containsKey(child.getId()) && !checkEvaluationRecordListEmpty(fldId2EvaRcdListMap.get(child.getId()))) {
                    childNode.setExtent1("1");
                }
                node.getChildren().add(childNode);
            }
            list.add(node);
        });
        //根据检测大类排序
        list.sort(Comparator.comparing(TreeNode::getType));
        return list;
    }

    @Override
    public List<DtoSampleFolder> getByReceiveId(String receiveId) {
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId);
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoSampleJudgeData> judgeDataList = sampleJudgeDataRepository.findBySampleIdIn(sampleIds);
        List<String> folderIds = sampleList.stream().filter(p -> !UUIDHelper.GUID_EMPTY.equals(p.getSampleFolderId())).map(DtoSample::getSampleFolderId).distinct().collect(Collectors.toList());
        List<String> folderIdList = new ArrayList<>();
        for (String folderId : folderIds) {
            List<DtoSample> samples2Folder = sampleList.stream().filter(s -> s.getSampleFolderId().equals(folderId)).collect(Collectors.toList());
            List<String> sampleIds2Folder = samples2Folder.stream().map(DtoSample::getId).collect(Collectors.toList());
            List<DtoSampleJudgeData> judgeData2Folder = judgeDataList.stream().filter(j -> sampleIds2Folder.contains(j.getSampleId())).collect(Collectors.toList());
            if (judgeData2Folder.size() > 0) {
                folderIdList.add(folderId);
            }
        }
        return StringUtil.isNotEmpty(folderIdList) ? repository.findAll(folderIdList).stream().sorted(Comparator.comparing(DtoSampleFolder::getWatchSpot)).collect(Collectors.toList()) : new ArrayList<>();
    }

    /**
     * 检查评价标准列表是否为空(所有评价标准为空，则返回true)
     *
     * @param recordList 评价标准
     * @return 评价标准是否为空
     */
    private boolean checkEvaluationRecordListEmpty(List<DtoEvaluationRecord> recordList) {
        for (DtoEvaluationRecord record : recordList) {
            if (!checkEvaluationRecordEmpty(record)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查评价标准是否为空(评价标准，登记，上下限值，上下限符号都为空时则判定为空)
     *
     * @param record 评价标准
     * @return 评价标准是否为空
     */
    private boolean checkEvaluationRecordEmpty(DtoEvaluationRecord record) {
        if ((StringUtil.isEmpty(record.getEvaluationId()) || UUIDHelper.GUID_EMPTY.equals(record.getEvaluationId()))
                && ((StringUtil.isEmpty(record.getEvaluationLevelId()) || UUIDHelper.GUID_EMPTY.equals(record.getEvaluationLevelId())))
                && (StringUtil.isEmpty(record.getLowerLimitValue())) && (StringUtil.isEmpty(record.getLowerLimitSymble()))
                && (StringUtil.isEmpty(record.getUpperLimitValue())) && (StringUtil.isEmpty(record.getUpperLimitSymble()))
                && (StringUtil.isEmpty(record.getDimensionId()) || UUIDHelper.GUID_EMPTY.equals(record.getDimensionId()))) {
            return true;
        }
        return false;
    }

    //#region 私有方法

    /**
     * 获取样品点位名称
     *
     * @param frequency  频次
     * @param watchSpot  点位名称
     * @param folderCode 点位编号
     * @return 返回样品点位名称
     */
    private String getFolderName(DtoSamplingFrequency frequency, String watchSpot, String folderCode) {
        String folderName = String.format("%s%s", StringUtils.isNotNullAndEmpty(watchSpot) ? watchSpot : "", StringUtils.isNotNullAndEmpty(folderCode) ? "_" + folderCode : "");
        if (StringUtils.isNotNullAndEmpty(folderName)) {
            folderName = String.format("%s(%d-%d-%d)", folderName, frequency.getPeriodCount(), frequency.getTimePerPeriod(), frequency.getSamplePerTime());
        }
        return folderName;
    }

    //#endregion

    /**
     * 记录分包信息
     *
     * @param frequencyList    方案信息
     * @param changeSftList    修改信息
     * @param sampleFolderList 点位
     * @param samTypes         样品类型
     * @param msgStr           消息
     */
    private void subToLog(List<DtoSamplingFrequency> frequencyList, List<DtoSamplingFrequencyTest> changeSftList,
                          List<DtoSampleFolder> sampleFolderList, List<DtoSampleType> samTypes, String msgStr) {
        List<DtoSamplingFrequency> thisFrequencyList = frequencyList.stream().filter(p ->
                changeSftList.stream().filter(q -> q.getIsOutsourcing() || q.getIsSamplingOut())
                        .map(DtoSamplingFrequencyTest::getSamplingFrequencyId)
                        .collect(Collectors.toList()).contains(p.getId())).collect(Collectors.toList());
        List<String> redFolderNames = new ArrayList<>();
        for (DtoSamplingFrequency frequency : thisFrequencyList) {
            DtoSampleFolder thisFolder = sampleFolderList.stream().filter(p -> p.getId().equals(frequency.getSampleFolderId())).findFirst().orElse(null);
            redFolderNames.add(String.format("%s(%s)第%d周期第%d次", StringUtil.isNotNull(thisFolder) ? thisFolder.getWatchSpot() : "",
                    StringUtil.isNotNull(thisFolder) ? samTypes.stream().filter(p -> p.getId().equals(thisFolder.getSampleTypeId())).map(DtoSampleType::getTypeName).findFirst().orElse("") : "",
                    frequency.getPeriodCount(), frequency.getTimePerPeriod()));
        }
        String comment = String.format("将点位%s的部分指标改为%s：%s。", String.join(",", redFolderNames), msgStr,
                String.join("、", changeSftList.stream().filter(p -> p.getIsOutsourcing() || p.getIsSamplingOut()).map(DtoSamplingFrequencyTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList())));
        newLogService.createLog(sampleFolderList.get(0).getProjectId(), comment, "", EnumLogType.方案点位数据信息.getValue(), EnumLogObjectType.方案.getValue(), EnumLogOperateType.分包项目.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public DtoSampleFolderLoadDataContainer loadDataBySampleFolder(PageBean<DtoSampleFolder> pageBean, BaseCriteria baseCriteria) {
        DtoSampleFolderLoadDataContainer container = new DtoSampleFolderLoadDataContainer();
        // 获取所有点
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        findByPage(pageBean, baseCriteria);
        List<DtoSampleFolder> sampleFolderList = pageBean.getData();
        //BUG2024042801083 【重要】【2024-4-30】【马川江】【现场任务】现场任务-按点位录入，按点位名称顺序排列吧，至少1234这种能正常识别；
        sampleFolderList.sort(Comparator.comparing(DtoSampleFolder::getWatchSpot));
        // 获取参数
        SampleFolderCriteria sampleFolderCriteria = (SampleFolderCriteria) baseCriteria;
        String receiveId = sampleFolderCriteria.getReceiveId();
        String sampleTypeId = sampleFolderCriteria.getSampleTypeId();
        //是否项目登记
        boolean isProject = sampleFolderCriteria.getIsProject();
        // 获取数据
        DtoSampleInfo sampleInfo = sampleService.findDetails(receiveId, sampleTypeId, UUIDHelper.GUID_EMPTY);
        List<DtoParamsConfig> paramsConfigList = sampleInfo.getParamsConfig();
        List<DtoParamsConfig> commonParamList = paramsConfigList.stream().filter(p -> EnumLIM.EnumParamsType.公共参数.getValue().equals(p.getParamsType()))
                .collect(Collectors.toList());
        container.setCommonParamList(commonParamList);
        List<DtoSample> sampleList = sampleRepository.findByReceiveId(receiveId).stream().filter(p -> p.getSampleTypeId().equals(sampleTypeId))
                .collect(Collectors.toList());
        List<String> sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
        List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds);
        List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testRepository.findAll(testIds) : new ArrayList<>();
        List<DtoParamsConfig> paramsConfigs = paramsConfigService.findByTypeIdAndTestIds(sampleTypeId, testIds).stream()
                .sorted(Comparator.comparing(DtoParamsConfig::getOrderNum).reversed()).collect(Collectors.toList());
        List<DtoParamsConfig> folderParamList = paramsConfigs.stream().filter(p -> !EnumLIM.EnumParamsType.公共参数.getValue().equals(p.getParamsType()))
                .collect(Collectors.toList());
        if (isProject) {
            folderParamList = folderParamList.stream().filter(p -> !p.getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())).collect(Collectors.toList());
        }
        List<DtoSampleGroup> groupList = sampleGroupService.findBySampleIds(sampleIds);
        //数据构建
        List<Map<String, Object>> rowMapList = sampleInfo.getSample();
        Map<String, List<DtoSampleTypeGroup>> sampleGroupConfigMap = splitSampleGroup(sampleTypeId, sampleIds, testList, analyseDataList, groupList);
        sampleList = sampleService.sortSampleByReceiveDataAndCode(sampleList);
        collectFolderGroupData(sampleList, rowMapList, sampleFolderList, folderParamList, sampleGroupConfigMap, sampleFolderCriteria);
        container.setSampleFolderList(sampleFolderList);
        return container;
    }

    @Override
    public List<DtoSampleFolderTestCount> querySampleFolderTestCount(Collection<String> sampleFolderIds) {
        List<DtoSamplingFrequencyTest> frequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
        List<DtoSampleFolderTestCount> list = new ArrayList<>();
        Map<String, List<DtoSamplingFrequencyTest>> sampleFolderFrequencyMap = frequencyTests.stream()
                .collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getSampleFolderId));
        sampleFolderFrequencyMap.forEach((sampleFolderId, sampleFolderFrequencyList) -> {
            Map<String, List<DtoSamplingFrequencyTest>> testFrequencyMap = sampleFolderFrequencyList.stream()
                    .collect(Collectors.groupingBy(DtoSamplingFrequencyTest::getTestId));
            testFrequencyMap.forEach((testId, frequencyList) -> {
                DtoSampleFolderTestCount sampleFolderTestCount = new DtoSampleFolderTestCount();
                sampleFolderTestCount.setSampleFolderId(sampleFolderId);
                sampleFolderTestCount.setTestId(testId);
                sampleFolderTestCount.setSampleCount(frequencyList.size());
                list.add(sampleFolderTestCount);
            });
        });
        return list;
    }

    /**
     * 删除与流量校准关联的点位周期
     *
     * @param sampleFolderIds 点位标识
     * @param periodCount     周期数据
     * @param frequencyList   周期批次标识
     */
    private void deleteFlowCalibration2Frequency(Collection<String> sampleFolderIds, List<Integer> periodCount, List<DtoSamplingFrequency> frequencyList) {
        //根据点位标识从删除
        if (StringUtil.isNotEmpty(sampleFolderIds)) {
            List<DtoFlowCalibration2Frequency> flowCalibration2FrequencyList = flowCalibration2FrequencyRepository
                    .findBySampleFolderIdIn(sampleFolderIds);
            if (sampleFolderIds.size() == 1 && StringUtil.isNotEmpty(periodCount)) {
                flowCalibration2FrequencyList = flowCalibration2FrequencyList.stream().filter(v -> periodCount.contains(v.getPeriodCount())).collect(Collectors.toList());
            }
            if (StringUtil.isNotEmpty(flowCalibration2FrequencyList)) {
                flowCalibration2FrequencyService.logicDeleteById(flowCalibration2FrequencyList.stream().map(DtoFlowCalibration2Frequency::getId).collect(Collectors.toList()));
            }
        }
        //根据频次标识删除
        if (StringUtil.isNotEmpty(frequencyList)) {
            List<String> frequencySampleFolderIds = frequencyList.stream().map(DtoSamplingFrequency::getSampleFolderId).collect(Collectors.toList());
            List<DtoFlowCalibration2Frequency> flowCalibration2FrequencyList = flowCalibration2FrequencyRepository
                    .findBySampleFolderIdIn(frequencySampleFolderIds);
            Set<String> keys = frequencyList.stream().map(v -> v.getSampleFolderId() + "_" + v.getPeriodCount()).collect(Collectors.toSet());
            flowCalibration2FrequencyList = flowCalibration2FrequencyList.stream().filter(v -> keys.contains(v.getSampleFolderId() + "_" + v.getPeriodCount()))
                    .collect(Collectors.toList());
            if (StringUtil.isNotEmpty(flowCalibration2FrequencyList)) {
                flowCalibration2FrequencyService.logicDeleteById(flowCalibration2FrequencyList.stream().map(DtoFlowCalibration2Frequency::getId).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 获取样品分组信息
     *
     * @param sampleList 样品列表
     * @param rowMapList 样品数据
     * @param sampleFolderList 点位数据
     * @param folderParamList 点位/样品/分组参数
     */
    private void collectFolderGroupData(List<DtoSample> sampleList,List<Map<String, Object>> rowMapList,List<DtoSampleFolder> sampleFolderList,
                                        List<DtoParamsConfig> folderParamList,Map<String,List<DtoSampleTypeGroup>> sampleGroupConfigMap,
                                        SampleFolderCriteria sampleFolderCriteria) {
        if(StringUtil.isNotEmpty(sampleList)){
            List<Map<String, Object>> finalRowDataList = new ArrayList<>();
            for (DtoSample sample:sampleList) {
                Map<String, Object> sampleData = rowMapList.stream().filter(v->sample.getId().equals(v.get("id"))).findFirst().orElse(null);
                if(sampleData!=null){
                    //处理质控样点位标识的问题，质控样跟着原样的点位
                    if(UUIDHelper.GUID_EMPTY.equals(sampleData.get("sampleFolderId"))&&sampleData.containsKey("associateSampleId")){
                        DtoSample yy = sampleList.stream().filter(v->v.getId().equals(sampleData.get("associateSampleId"))).findFirst().orElse(null);
                        if(yy!=null){
                            sampleData.put("sampleFolderId",yy.getSampleFolderId());
                        }
                    }
                    //取检测类型的配置进行分组
                    List<DtoSampleTypeGroup> sampleTypeGroupList = sampleGroupConfigMap.containsKey(sample.getId())?sampleGroupConfigMap.get(sample.getId()):new ArrayList<>();
                    for (DtoSampleTypeGroup sampleTypeGroup:sampleTypeGroupList) {
                        Map<String, Object> rowMap = new HashMap<>();
                        copyMapProperty(sampleData,rowMap,"code","cycleOrder","inspectedEnt","inspectedEntId","qcGrade","qcType","redFolderName","sampleCategory",
                                "sampleFolderId","sampleTypeId","sampleTypeName","samplingTimeBegin","timesOrder");
                        rowMap.put("sampleId",sample.getId());
                        rowMap.put("sampleTypeGroupId",sampleTypeGroup.getId());
                        rowMap.put("sampleTypeGroupName",sampleTypeGroup.getGroupName());
                        rowMap.put("redAnalyzeItems",sampleTypeGroup.getSampleItemNames());
                        //参数数据
                        for (DtoParamsConfig param:folderParamList) {
                            if(EnumLIM.EnumParamsType.分析项目参数.getValue().equals(param.getParamsType())){
                                rowMap.put(param.getId(), sampleData.getOrDefault(param.getId() + ";" + sampleTypeGroup.getId(), ""));
                            }else{
                                rowMap.put(param.getId(), sampleData.getOrDefault(param.getId() + ";" + UUIDHelper.GUID_EMPTY, ""));
                            }
                        }
                        finalRowDataList.add(rowMap);
                    }
                }
            }
            //按点位拆分并赋值点位参数数据
            for (DtoSampleFolder sampleFolder:sampleFolderList) {
                List<Map<String, Object>> folderRowDataList = finalRowDataList.stream().filter(v->sampleFolder.getId().equals(v.get("sampleFolderId")))
                        .collect(Collectors.toList());
                if(Boolean.TRUE.equals(sampleFolderCriteria.getSortByItem())){
                    //按项目排序
                    folderRowDataList.sort(
                            Comparator.comparing(
                                    (Map<String, Object> v) -> (String) v.getOrDefault("redAnalyzeItems",""),
                                    Comparator.nullsFirst(Comparator.naturalOrder())
                            ).thenComparing(
                                    v -> (String) v.getOrDefault("code",""),
                                    Comparator.nullsFirst(Comparator.naturalOrder())
                            )
                    );
                }
                sampleFolder.setRowMapList(folderRowDataList);
                if(StringUtil.isNotEmpty(folderRowDataList)){
                    Map<String, Object> data = folderRowDataList.get(0);
                    List<DtoParamsConfig> copyFolderParamList = new ArrayList<>();
                    for (DtoParamsConfig folderParam:folderParamList) {
                        DtoParamsConfig paramsConfig = new DtoParamsConfig();
                        BeanUtils.copyProperties(folderParam,paramsConfig);
                        if(EnumLIM.EnumParamsType.点位参数.getValue().equals(folderParam.getParamsType())){
                            paramsConfig.setParamsValue((String)data.getOrDefault(folderParam.getId(), ""));
                        }
                        copyFolderParamList.add(paramsConfig);
                    }
                    sampleFolder.setFolderParamList(copyFolderParamList);
                }
            }
        }
    }

    /**
     * 属性复制
     * @param source 源
     * @param target 目标
     * @param params 属性
     */
    private void copyMapProperty(Map<String, Object> source,Map<String, Object> target,String... params){
        for (String param:params ) {
            target.put(param,source.get(param));
        }
    }

    /**
     * 按照检测类型配置对样品进行分组
     *
     * @param sampleTypeId    检测类型标识
     * @param sampleIdList    样品标识集合
     * @param testList        测试项目集合
     * @param analyseDataList 数据集合
     * @return 分组配置
     */
    private Map<String, List<DtoSampleTypeGroup>> splitSampleGroup(String sampleTypeId, List<String> sampleIdList, List<DtoTest> testList,
                                                                   List<DtoAnalyseData> analyseDataList, List<DtoSampleGroup> sampleGroupList) {
        Map<String, List<DtoSampleTypeGroup>> map = new HashMap<>();
        if (StringUtil.isNotEmpty(sampleIdList)) {
            DtoSampleType sampleType = sampleTypeRepository.findOne(sampleTypeId);
            if (StringUtil.isNotEmpty(sampleType.getFieldTaskGroupId()) && !UUIDHelper.GUID_EMPTY.equals(sampleType.getFieldTaskGroupId())) {
                List<DtoSampleTypeGroup> allSampleTypeGroupList = sampleTypeGroupRepository
                        .findByParentIdInAndGroupType(Collections.singletonList(sampleType.getFieldTaskGroupId()), EnumLIM.EnumGroupType.分组.getValue());
                List<DtoSampleTypeGroup2Test> allGroup2TestList = new ArrayList<>();
                if (StringUtil.isNotEmpty(allSampleTypeGroupList)) {
                    allGroup2TestList = sampleTypeGroup2TestRepository.
                            findBySampleTypeGroupIds(allSampleTypeGroupList.stream().map(DtoSampleTypeGroup::getId).collect(Collectors.toList()));
                }
                for (String sampleId : sampleIdList) {
                    map.put(sampleId, new ArrayList<>());
                    List<DtoAnalyseData> dataList = analyseDataList.stream().filter(p -> p.getSampleId().equals(sampleId)).collect(Collectors.toList());
                    List<String> allTestIds = dataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
                    for (DtoSampleTypeGroup group : allSampleTypeGroupList) {
                        List<String> groupTestIds = allGroup2TestList.stream().filter(p -> p.getSampleTypeGroupId().equals(group.getId()))
                                .map(DtoSampleTypeGroup2Test::getTestId).collect(Collectors.toList());
                        Optional<DtoSampleGroup> groupOptional = sampleGroupList.stream().filter(p -> sampleId.equals(p.getSampleId())
                                && group.getId().equals(p.getSampleTypeGroupId())).findFirst();
                        //已有分组的测试项目去掉
                        allTestIds = allTestIds.stream().filter(p -> !groupTestIds.contains(p)).collect(Collectors.toList());
                        if (groupOptional.isPresent()) {
                            DtoSampleTypeGroup sampleTypeGroup = new DtoSampleTypeGroup();
                            BeanUtils.copyProperties(group, sampleTypeGroup);
                            sampleTypeGroup.setSampleItemNames(groupOptional.get().getAnalyseItemNames());
                            map.get(sampleId).add(sampleTypeGroup);
                        } else {
                            List<String> anaName = dataList.stream().filter(p -> groupTestIds.contains(p.getTestId()))
                                    .map(DtoAnalyseData::getRedAnalyzeItemName).sorted(Comparator.comparing(p -> p))
                                    .distinct().collect(Collectors.toList());
                            if (anaName.size() > 0) {
                                DtoSampleTypeGroup sampleTypeGroup = new DtoSampleTypeGroup();
                                BeanUtils.copyProperties(group, sampleTypeGroup);
                                sampleTypeGroup.setSampleItemNames(String.join(",", anaName));
                                map.get(sampleId).add(sampleTypeGroup);
                            }
                        }
                    }
                    if (allTestIds.size() > 0) {
                        DtoSampleTypeGroup sampleTypeGroup = new DtoSampleTypeGroup();
                        sampleTypeGroup.setId(UUIDHelper.GUID_EMPTY);
                        sampleTypeGroup.setGroupName("/");
                        List<String> finalAllTestIds = allTestIds;
                        String anaName = testList.stream().filter(v -> finalAllTestIds.contains(v.getId())).map(DtoTest::getRedAnalyzeItemName).sorted().collect(Collectors.joining(","));
                        sampleTypeGroup.setSampleItemNames(String.join(",", anaName));
                        map.get(sampleId).add(sampleTypeGroup);
                    }
                }
            } else {
                //没有配置现场分组
                for (String sampleId : sampleIdList) {
                    DtoSampleTypeGroup sampleTypeGroup = new DtoSampleTypeGroup();
                    sampleTypeGroup.setId(UUIDHelper.GUID_EMPTY);
                    sampleTypeGroup.setGroupName("/");
                    String anaName = testList.stream().map(DtoTest::getRedAnalyzeItemName).sorted().collect(Collectors.joining(","));
                    sampleTypeGroup.setSampleItemNames(String.join(",", anaName));
                    map.put(sampleId, Collections.singletonList(sampleTypeGroup));
                }
            }
        }
        return map;
    }


    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    @Lazy
    public void setEvaluationRecordRepository(EvaluationRecordRepository evaluationRecordRepository) {
        this.evaluationRecordRepository = evaluationRecordRepository;
    }

    @Autowired
    public void setEnterpriseRepository(EnterpriseRepository enterpriseRepository) {
        this.enterpriseRepository = enterpriseRepository;
    }

    @Autowired
    public void setCompareJudgeRepository(CompareJudgeRepository compareJudgeRepository) {
        this.compareJudgeRepository = compareJudgeRepository;
    }

    @Autowired
    @Lazy
    public void setSampleJudgeDataService(SampleJudgeDataService sampleJudgeDataService) {
        this.sampleJudgeDataService = sampleJudgeDataService;
    }

    @Autowired
    public void setSampleGroupRepository(SampleGroupRepository sampleGroupRepository) {
        this.sampleGroupRepository = sampleGroupRepository;
    }

    @Autowired
    @Lazy
    public void setSampleGroupService(SampleGroupService sampleGroupService) {
        this.sampleGroupService = sampleGroupService;
    }
}