package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalLog;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord;

/**
 * 环境日志
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface EnvironmentalLogService extends IBaseJpaService<DtoEnvironmentalLog, String> {

    /**
     * 保存环境记录
     * @param dto 环境记录
     * @return 环境日志集合
     */
    DtoEnvironmentalLog saveEnvironmentalLog(DtoEnvironmentalRecord dto);


    /**
     * 保存环境记录
     * @param dto 环境记录
     * @return 环境日志集合
     */
    DtoEnvironmentalLog saveOrUpdateEnvironmentalLog(DtoEnvironmentalRecord dto,String ObjectId);
}