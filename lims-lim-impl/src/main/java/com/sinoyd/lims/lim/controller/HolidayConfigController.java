package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.rcc.DtoHolidayConfig;
import com.sinoyd.lims.lim.service.HolidayConfigService;
import com.sinoyd.lims.lim.vo.WorkHolidayConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 节假日管理配置接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023-01-18
 */
@Api(tags = "节假日管理配置接口服务")
@RestController
@RequestMapping("/api/lim/holidayConfig")
@Validated
public class HolidayConfigController extends BaseJpaController<DtoHolidayConfig, String, HolidayConfigService> {

    /**
     * 查询节假日管理配置
     *
     * @param year 年份
     * @return 节假日管理配置List
     */
    @ApiOperation(value = "查询节假日管理配置", notes = "查询节假日管理配置")
    @GetMapping("/year/{year}")
    public RestResponse<List<DtoHolidayConfig>> findByYear(@PathVariable("year") Integer year) {
        RestResponse<List<DtoHolidayConfig>> restResp = new RestResponse<>();
        restResp.setData(service.findByYear(year));
        return restResp;
    }


    /**
     * 保存节假日管理配置
     *
     * @param vo 传入节假日工作日管理配置dto
     * @return 保存完以后的dto
     */
    @ApiOperation(value = "节假日管理配置", notes = "节假日管理配置")
    @PostMapping
    public RestResponse<List<DtoHolidayConfig>> save(@Validated @RequestBody WorkHolidayConfigVO vo) {
        RestResponse<List<DtoHolidayConfig>> restResp = new RestResponse<>();
        restResp.setData(service.save(vo));
        return restResp;
    }

    /**
     * 单个删除信息
     *
     * @param id 主键
     * @return 删除响应
     */
    @ApiOperation(value = "单个删除信息", notes = "单个删除信息")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable("id") String id) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(id);
        restResp.setCount(count);
        return restResp;
    }

}



  