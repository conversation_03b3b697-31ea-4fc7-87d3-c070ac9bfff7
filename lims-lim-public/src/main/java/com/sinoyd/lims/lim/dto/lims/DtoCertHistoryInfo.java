package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.CertHistoryInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * DtoCertHistoryFile实体
 * <AUTHOR>
 * @version V1.0.0
 * @since  2024/11/26
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_CertHistoryInfo")
@Data
@DynamicInsert
public class DtoCertHistoryInfo extends CertHistoryInfo {
}
