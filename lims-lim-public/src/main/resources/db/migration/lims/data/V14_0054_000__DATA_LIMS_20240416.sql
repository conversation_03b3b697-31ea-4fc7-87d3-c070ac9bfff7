-- pH现场分析原始记录单模板配置
INSERT INTO TB_LIM_ReportConfig(id, type, reportCode, templateName, template, outputName, returnType, method, params,
                                pageConfig, orderNum, bizType, remark, isDeleted, orgId, creator, createDate, domainId,
                                modifier, modifyDate, dataMethod, typeCode, strUrl, isDefineFileName, defineFileName,
                                beanName, versionNum, controlNum, reportName, validate, usageNum)
VALUES ('7d121986-3317-44cd-bb0b-f9a81261694b', 1, 'PhFieldAnalyzeSamplingRecord', 'pH现场分析原始记录单.xlsx',
        'Sampling/pH现场分析原始记录单.xlsx', 'output/Sampling/pH现场分析原始记录单.xlsx', 'application/excel',
        'com.sinoyd.lims.sampling.service.jx.samplingReport.PhFieldAnalyzeSamplingRecordService',
        '{\"sort\":\"orderNum-\"}', '', 0, 3, '', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-04-15 15:06:26', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2024-04-15 15:06:26', 'sampleIds', 'Sampling',
        'PhFieldAnalyzeSamplingRecord', b'0', '', NULL, '', '', '', 0, NULL);