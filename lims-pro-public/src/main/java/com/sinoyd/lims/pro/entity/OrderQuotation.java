package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import java.math.BigDecimal;


/**
 * OrderQuotation实体
 * <AUTHOR>
 * @version V1.0.0 2022/5/18
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="OrderQuotation")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class OrderQuotation implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  OrderQuotation() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
       this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 订单id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("订单id")
	private String orderId;
    
    /**
    * 检测费小计
    */
    @Column(nullable=false)
    @ColumnDefault("0.00")
    @ApiModelProperty("检测费小计")
	private BigDecimal testPrice;
    
    /**
    * 检测折扣率
    */
    @Column(nullable=false)
    @ColumnDefault("0.00")
    @ApiModelProperty("检测折扣率")
	private BigDecimal testDiscount;
    
    /**
    * 折后检测费
    */
    @Column(nullable=false)
    @ColumnDefault("0.00")
    @ApiModelProperty("折后检测费")
	private BigDecimal discountPrice;
    
    /**
    * 其他费用小计
    */
    @Column(nullable=false)
    @ColumnDefault("0.00")
    @ApiModelProperty("其他费用小计")
	private BigDecimal otherPrice;
    
    /**
    * 税前报价
    */
    @Column(nullable=false)
    @ColumnDefault("0.00")
    @ApiModelProperty("税前报价")
	private BigDecimal preTax;
    
    /**
    * 税率
    */
    @Column(nullable=false)
    @ColumnDefault("0.00")
    @ApiModelProperty("税率")
	private BigDecimal taxRate;
    
    /**
    * 总价折扣率
    */
    @Column(nullable=false)
    @ColumnDefault("0.00")
    @ApiModelProperty("总价折扣率")
	private BigDecimal totalDiscount;
    
    /**
    * 总价
    */
    @Column(nullable=false)
    @ColumnDefault("0.00")
    @ApiModelProperty("总价")
	private BigDecimal totalPrice;
    
    /**
    * 最后报价
    */
    @Column(nullable=false)
    @ColumnDefault("0.00")
    @ApiModelProperty("最后报价")
	private BigDecimal finalQuotation;
    
    /**
    * 是否删除
    */
    @Column(nullable=false)
    @ColumnDefault("0")
    @ApiModelProperty("是否删除")
	private Boolean isDeleted=false;
    
    /**
    * 组织机构id
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
	private String orgId;
    
    /**
    * 创建人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
	private String creator;
    
    /**
    * 创建时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
	private Date createDate;
    
    /**
    * 所属实验室
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
	private String domainId;
    
    /**
    * 修改人
    */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
	private String modifier;
    
    /**
    * 修改时间
    */
    @Column(nullable=false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
	private Date modifyDate;
    
 }