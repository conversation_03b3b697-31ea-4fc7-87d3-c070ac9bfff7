package com.sinoyd.lims.pro.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * SampleGroup实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "SampleGroup")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SampleGroup extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public SampleGroup() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 送样单id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("送样单id")
    private String receiveId;

    /**
     * 样品id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("样品id")
    private String sampleId;

    /**
     * 样品分组id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("样品分组id")
    private String sampleTypeGroupId;

    /**
     * 样品分组名称
     */
    @Column(length = 50)
    @ApiModelProperty("样品分组名称")
    @Length(message = "样品分组名称{validation.message.length}", max = 255)
    private String sampleTypeGroupName;

    /**
     * 固定剂
     */
    @Column(length = 1000)
    @ApiModelProperty("固定剂")
    @Length(message = "固定剂{validation.message.length}", max = 1000)
    private String fixer;

    /**
     * 容器名称
     */
    @ApiModelProperty("容器名称")
    @Length(message = "容器名称{validation.message.length}", max = 255)
    private String containerName;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 1000)
    private String remark;

    /**
     * 分析项目名称
     */
    @Column(length = 2000)
    @ApiModelProperty("分析项目名称")
    @Length(message = "分析项目名称{validation.message.length}", max = 2000)
    private String analyseItemNames;

    /**
     * 是否已扫码
     */
    @Column(nullable = false)
    @ApiModelProperty("是否已扫码")
    private Boolean hasScanned;

    /**
     * 分组标识 1:按分组  2:全因子  3:单因子
     */
    @Column(length = 10)
    @ApiModelProperty("分组标识 1:按分组  2:全因子  3:单因子")
    private Integer isGroup;

    /**
     * 扫码时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("扫码时间")
    private Date scannedTime;

    /**
     * 扫码人id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("扫码人id")
    @Length(message = "扫码人id{validation.message.length}", max = 50)
    private String scanner;

    /**
     * 保存条件
     */
    @Column(length = 1000)
    @ApiModelProperty("保存条件")
    @Length(message = "保存条件{validation.message.length}", max = 1000)
    private String saveCondition;

    /**
     * 样品状态
     */
    @Column(length = 1000)
    @ApiModelProperty("样品状态")
    @Length(message = "样品状态{validation.message.length}", max = 1000)
    private String sampleStatus;

    /**
     * 危险性描述
     */
    @Column(length = 1000)
    @ApiModelProperty("危险性描述")
    @Length(message = "危险性描述{validation.message.length}", max = 1000)
    private String riskDescription;

    /**
     * 运输条件
     */
    @Column(length = 1000)
    @ApiModelProperty("运输条件")
    @Length(message = "运输条件{validation.message.length}", max = 1000)
    private String transportationCondition;

    /**
     * 采样开始时间
     */
    @Column(length = 100)
    @ApiModelProperty("采样开始时间")
    @Length(message = "采样开始时间{validation.message.length}", max = 1000)
    private String samplingBegin;

    /**
     * 采样结束时间
     */
    @Column(length = 100)
    @ApiModelProperty("采样结束时间")
    @Length(message = "采样结束时间{validation.message.length}", max = 1000)
    private String samplingEnd;

    /**
     * 领取数量
     */
    @Column(length = 50)
    @ApiModelProperty("领取数量")
    private Integer reserveNums;

    /**
     * 样品数
     */
    @Column
    @ColumnDefault("1")
    @ApiModelProperty("样品数")
    private Integer sampleNum;

    /**
     * 分析项目数量
     */
    @Column(length = 50)
    @ApiModelProperty("分析项目总数量")
    private Integer analyseNums;

    @Column
    @ApiModelProperty("前处理方式")
    @Length(message = "前处理方式{validation.message.length}", max = 200)
    private String pretreatmentMethod;

    @Column
    @ApiModelProperty("采样体积")
    @Length(message = "采样体积{validation.message.length}", max = 200)
    private String sampleVolume;

    /**
     * 采样容器状态（采样容器状态 EnumContainerStatus 1.完好无损 2.破损）
     */
    @Column
    @ColumnDefault("0")
    @ApiModelProperty("采样容器状态 EnumContainerStatus 1.完好无损 2.破损")
    private Integer containerStatus;

    /**
     * 接样人名称
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("接样人名称")
    private String recipientName;

    /**
     * 接样日期
     */
    @Column(nullable = false)
    @ApiModelProperty("接样日期")
    private Date receiveSampleDate;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

    /**
     * 假删
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删")
    private Boolean isDeleted = false;
}