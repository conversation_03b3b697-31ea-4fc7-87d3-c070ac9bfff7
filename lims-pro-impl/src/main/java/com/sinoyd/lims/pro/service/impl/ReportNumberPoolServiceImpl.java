package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.core.LimCodeHelper;
import com.sinoyd.lims.lim.dto.customer.DtoGenerateSN;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSort;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialIdentifierConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoSerialNumberConfig;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.AnalyzeItemSortService;
import com.sinoyd.lims.lim.service.GenerateSerialNumberService;
import com.sinoyd.lims.lim.service.SerialIdentifierConfigService;
import com.sinoyd.lims.lim.service.SerialNumberConfigService;
import com.sinoyd.lims.monitor.dto.lims.DtoFixedPointSort;
import com.sinoyd.lims.monitor.service.FixedPointSortService;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoReportNumberPool;
import com.sinoyd.lims.pro.repository.ReportNumberPoolRepository;
import com.sinoyd.lims.pro.service.ProjectService;
import com.sinoyd.lims.pro.service.ReportNumberPoolService;
import com.sinoyd.lims.pro.service.ReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ReportNumberPool数据访问操作接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/8/1
 * @since V100R001
 */
@Service
@Slf4j
public class ReportNumberPoolServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoReportNumberPool, String, ReportNumberPoolRepository> implements ReportNumberPoolService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SerialIdentifierConfigService serialIdentifierConfigService;

    @Autowired
    @Lazy
    private SerialNumberConfigService serialNumberConfigService;

    @Autowired
    private ReportService reportService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private AnalyzeItemSortService analyzeItemSortService;

    @Autowired
    private FixedPointSortService fixedPointSortService;

    @Autowired
    private GenerateSerialNumberService generateSerialNumberService;

    //通用正则
    protected static final Pattern PATTERN = Pattern.compile("(?<=\\[).*?(?=\\])");

    //日期正则
    private static final Pattern PATTERN_DATE = Pattern.compile("(?<=\\().*?(?=\\))");

    @Override
    public void findByPage(PageBean<DtoReportNumberPool> page, BaseCriteria criteria) {
        page.setEntityName("DtoReportNumberPool a");
        page.setSelect("select a");
        super.findByPage(page, criteria);
        List<DtoReportNumberPool> dtoReportNumberPools = page.getData();
        if (StringUtil.isNotEmpty(dtoReportNumberPools)) {
            List<DtoSerialIdentifierConfig> serviceAll = serialIdentifierConfigService.findAll();
            Map<String, String> configToMop = serviceAll.stream().collect(Collectors.toMap(DtoSerialIdentifierConfig::getId, DtoSerialIdentifierConfig::getConfigName));
            dtoReportNumberPools.forEach(p -> p.setReportName(configToMop.get(p.getReportTypeId())));
        }
        page.setData(dtoReportNumberPools);
    }

    @Override
    public DtoReport queryReport(String code) {
        DtoReport report = reportService.findByCode(code);
        // 处理基本信息内的各种名称
        if (StringUtil.isNotNull(report)) {
            DtoProject dtoProject = projectService.findOne(report.getProjectId());
            DtoAnalyzeItemSort analyzeItemSort = analyzeItemSortService.findOne(report.getAnalyseItemSortId());
            if (StringUtil.isNotNull(report.getFolderSortId()) && !UUIDHelper.GUID_EMPTY.equals(report.getFolderSortId())) {
                DtoFixedPointSort fixedPointSort = fixedPointSortService.findOne(report.getFolderSortId());
                report.setFolderSortName(StringUtil.isNotNull(fixedPointSort) ? fixedPointSort.getSortName() : "");
            }
            report.setProjectName(StringUtil.isNotNull(dtoProject) ? dtoProject.getProjectName() : "");
            report.setAnalyseItemSortName(StringUtil.isNotNull(analyzeItemSort) ? analyzeItemSort.getSortName() : "");
        }
        return report;
    }

    @Override
    public DtoReportNumberPool save(DtoReportNumberPool reportNumberPool) {
        // 组装条件语句
        String reportTypeId = reportNumberPool.getReportTypeId();
        DtoSerialIdentifierConfig serialIdentifierConfig = serialIdentifierConfigService.findOne(reportTypeId);
        Integer year = reportNumberPool.getYear();
        String projectCode = reportNumberPool.getProjectCode();
        Integer code = Math.abs(Integer.parseInt(reportNumberPool.getCode()));
        String format = "";
        // 获取报告编号生成配置
        if (StringUtil.isNotNull(serialIdentifierConfig)) {
            format = serialIdentifierConfig.getConfigRule();
            reportNumberPool.setReportTypeId(serialIdentifierConfig.getId());
        }
        Map<String, Object> dataMap = new HashMap<>();
        String serialType = processParam(dataMap, serialIdentifierConfig.getConfigName(), year, format, projectCode);
        // 更新当前流水号
        dataMap.put("serialNumber", code);
        DtoGenerateSN generateSN = generateSerialNumberService.generateCurrentSN(format, dataMap);
        String reportCode = generateSN.getCode();
        Integer count = repository.countByCode(reportCode);
//        if (count > 0) {
//            throw new BaseException("当前编号已存在！");
//        }
        while (count > 0) {
            dataMap.put("serialNumber", code++);
            generateSN = generateSerialNumberService.generateCurrentSN(format, dataMap);
            reportCode = generateSN.getCode();
            count = repository.countByCode(reportCode);
        }
        updateSerialNumber(serialType, code.toString());
        reportNumberPool.setCode(reportCode);
        reportNumberPool.setNumber(code);
        // 流水号类型
        reportNumberPool.setSerialType(serialType);
        return super.save(reportNumberPool);
    }


    @Override
    public List<DtoReportNumberPool> batchCreate(DtoReportNumberPool reportNumberPool) {
        Integer serialNumber = reportNumberPool.getSerialNumber();
        String reportTypeId = reportNumberPool.getReportTypeId();
        String projectCode = reportNumberPool.getProjectCode();
        DtoSerialIdentifierConfig serialIdentifierConfig = serialIdentifierConfigService.findOne(reportTypeId);
        Integer year = reportNumberPool.getYear();
        int code = Math.abs(Integer.parseInt(reportNumberPool.getCode()));
        Map<String, Object> dataMap = new HashMap<>();
        String format = "";
        // 获取报告编号生成配置
        if (StringUtil.isNotNull(serialIdentifierConfig)) {
            format = serialIdentifierConfig.getConfigRule();
            reportNumberPool.setReportTypeId(serialIdentifierConfig.getId());
        }
        // 获取报告流水号类型
        String serialType = processParam(dataMap, serialIdentifierConfig.getConfigName(), year, format, projectCode);
        List<DtoReportNumberPool> reportNumberPools = repository.findByYear(year);
        List<DtoReportNumberPool> dtoReportNumberPools = new ArrayList<>();
        for (int i = 0; i < serialNumber; i++) {
            // 更新当前流水号
            dataMap.put("serialNumber", code);
            //取出当前编号信息
            DtoGenerateSN generateSN = generateSerialNumberService.generateCurrentSN(format, dataMap);
            String reportCode = generateSN.getCode();
            DtoReportNumberPool numberPool = reportNumberPools.stream().filter(p -> p.getCode().equals(reportCode)).findFirst().orElse(null);
            int number = code;
            code++;
            if (StringUtil.isNotNull(numberPool)) {
                continue;
            }

            List<String> codeList = dtoReportNumberPools.stream().map(DtoReportNumberPool::getCode).collect(Collectors.toList());
            if (!codeList.contains(reportCode)) {
                DtoReportNumberPool dtoReportNumberPool = new DtoReportNumberPool();
                dtoReportNumberPool.setReportTypeId(reportNumberPool.getReportTypeId());
                dtoReportNumberPool.setYear(year);
                dtoReportNumberPool.setCode(reportCode);
                dtoReportNumberPool.setNumber(number);
                // 流水号类型
                dtoReportNumberPool.setSerialType(serialType);
                dtoReportNumberPools.add(dtoReportNumberPool);
            }
        }
        // 更新流水号
        updateSerialNumber(serialType, String.valueOf(code - 1));
        return this.save(dtoReportNumberPools);
    }

    @Override
    public Map<String,Object> queryMaxNumber(DtoReportNumberPool reportNumberPool) {
        String reportTypeId = reportNumberPool.getReportTypeId();
        DtoSerialIdentifierConfig serialIdentifierConfig = serialIdentifierConfigService.findOne(reportTypeId);
        Integer year = reportNumberPool.getYear();
        String projectCode = reportNumberPool.getProjectCode();
        // 获取当前流水号类型
        Map<String, Object> dataMap = new HashMap<>();
        String format = "";
        // 获取报告编号生成配置
        if (StringUtil.isNotNull(serialIdentifierConfig)) {
            format = serialIdentifierConfig.getConfigRule();
            reportNumberPool.setReportTypeId(serialIdentifierConfig.getId());
        }

        String serialType = processParam(dataMap, serialIdentifierConfig.getConfigName(), year, format, projectCode);
        DtoSerialNumberConfig dtoSerialNumberConfig = serialNumberConfigService.findBySerialNumberType(serialType);
        String para1 = "";
        if (StringUtil.isNotNull(dtoSerialNumberConfig) && StringUtil.isNotEmpty(dtoSerialNumberConfig.getPara1())) {
            para1 = dtoSerialNumberConfig.getPara1();
        } else {
            para1 = "0";
        }
        Map<String,Object> map = new HashMap<>();
        Integer result = null;
        // 获取当前最大流水号 + 1
        if (StringUtil.isNotEmpty(para1)) {
            result = Integer.parseInt(para1) + 1;
        }
        map.put("number",result);
        map.put("serialType",serialType);
        return map;
    }

    @Override
    public int cancel(List<String> ids) {
        List<DtoReportNumberPool> dtoReportNumberPools = repository.findAll(ids);
        List<DtoReportNumberPool> cancelList = dtoReportNumberPools.stream().filter(p -> p.getStatus().equals(2)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(cancelList)) {
            throw new BaseException("存在已作废的报告，请确认后再操作!");
        }
        List<DtoReportNumberPool> useList = dtoReportNumberPools.stream().filter(p -> p.getStatus().equals(1)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(useList)) {
            throw new BaseException("已使用的编号，不能作废！");
        }
        List<DtoReportNumberPool> updateReportNumberPoolList = dtoReportNumberPools.stream().peek(p -> p.setStatus(2)).collect(Collectors.toList());
        this.save(updateReportNumberPoolList);
        return updateReportNumberPoolList.size();
    }

    @Override
    public int cancelVoid(List<String> ids) {

        List<DtoReportNumberPool> dtoReportNumberPools = repository.findAll(ids);
        List<DtoReportNumberPool> useList = dtoReportNumberPools.stream()
                .filter(p -> p.getStatus().equals(1) || p.getStatus().equals(0)).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(useList)) {
            throw new BaseException("所选报告编号状态正常，无法取消作废！");
        }
        List<DtoReportNumberPool> updateReportNumberPoolList = dtoReportNumberPools.stream().peek(p -> p.setStatus(0)).collect(Collectors.toList());
        this.save(updateReportNumberPoolList);
        return updateReportNumberPoolList.size();

    }

    /**
     * 修改流水号
     *
     * @param serialType 流水号类型
     * @param code       流水号码
     */
    private void updateSerialNumber(String serialType, String code) {
        DtoSerialNumberConfig dtoSerialNumberConfig = serialNumberConfigService.findBySerialNumberType(serialType);
        if (StringUtil.isNotNull(dtoSerialNumberConfig)) {
            if (!code.equals(dtoSerialNumberConfig.getPara1())) {
                dtoSerialNumberConfig.setPara1(code);
                serialNumberConfigService.save(dtoSerialNumberConfig);
            }
        } else {
            dtoSerialNumberConfig = new DtoSerialNumberConfig();
            dtoSerialNumberConfig.setSerialNumberType(serialType);
            dtoSerialNumberConfig.setPara1(String.valueOf(code));
            dtoSerialNumberConfig.setPara2(PrincipalContextUser.getPrincipal().getUserId());
            dtoSerialNumberConfig.setLastUpdateTime(new Date());
            dtoSerialNumberConfig.setCreateDate(new Date());
            dtoSerialNumberConfig.setCreator(PrincipalContextUser.getPrincipal().getUserId());
            dtoSerialNumberConfig.setModifier(PrincipalContextUser.getPrincipal().getUserId());
            dtoSerialNumberConfig.setModifyDate(new Date());
            serialNumberConfigService.save(dtoSerialNumberConfig);
        }
        // 删除缓存
        deleteSerialNumberConfig(serialType);
    }


    /**
     * 获取报告流水号类型
     *
     * @param reportName 报告类型名称
     * @param year       年份
     */
    private String processParam(Map<String, Object> dataMap, String reportName, Integer year, String format, String projectCode) {
        // 组装参数
        Map<String, Object> report = new HashMap<>();
        report.put("name", reportName);
        report.put("year", year);

        Map<String, Object> project = new HashMap<>();
        project.put("projectCode", projectCode);
        //当前时间
        dataMap.put("time", new Date());
        dataMap.put("reportType", report);
        dataMap.put("project", project);
        return getSerialNumberType(format, dataMap);
    }

    /**
     * 获取SerialNumberType
     *
     * @param format 配置文件
     * @param map    参数
     * @return SerialNumberType
     */
    private String getSerialNumberType(String format, Map<String, Object> map) {
        String serialType = "";
        String code = format.trim();//编号去除存储的空格
        Matcher matcher = PATTERN.matcher(format);
        while (matcher.find()) {
            //如何得到key
            String groupValue = matcher.group();
            if (groupValue.contains(LimCodeHelper.SN)) { //说明包含流水号
                try {
                    groupValue = groupValue.substring(groupValue.indexOf("[") + 1, groupValue.length());
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                }
            }
            Map<String, Object> dateMap = getDateValue(groupValue, map);

            //如果有日期数据匹配
            if (dateMap.containsKey(groupValue)) {
                String dateValue = StringUtil.isNotNull(dateMap.get(groupValue)) ?
                        String.valueOf(dateMap.get(groupValue)) : "";
                code = code.replace("[" + groupValue + "]", dateValue);
                continue;
            }

            //带有实体的数据源（不带日期的），日期的上面统一考虑处理
            Map<String, Object> entityMap = getEntityValue(groupValue, map);
            if (entityMap.containsKey(groupValue)) {
                String entityValue = StringUtil.isNotNull(entityMap.get(groupValue)) ? String.valueOf(entityMap.get(groupValue)) : "";
                code = code.replace("[" + groupValue + "]", entityValue);
                continue;
            }

            //两种情况都不包含
            String value = "";
            if (StringUtil.isNotNull(map.get(groupValue))) {
                value = String.valueOf(map.get(groupValue));
            }
            code = code.replace("[" + groupValue + "]", value);

        }
        serialType = getSerialType(code);
        return serialType;
    }

    /**
     * 获取流水号的序列化的类型
     *
     * @param format 数据格式
     * @return 返回相应的格式化编号
     */
    protected String getSerialType(String format) {
        StringBuilder value = new StringBuilder();
        Matcher matcher = PATTERN.matcher(format);
        while (matcher.find()) {
            //如何得到key
            String groupValue = matcher.group();
            if (groupValue.contains(LimCodeHelper.SN)) {
                String[] groupValues = groupValue.split("-");
                for (String group : groupValues) {
                    if (!group.contains(LimCodeHelper.SN)) {
                        value.append(group);
                    }
                }
            }
        }
        return value.toString();
    }

    /**
     * 主要是用来匹配日期的数据 如time(yy) 或者 sample.sampleTime(yy)
     *
     * @param groupValue 匹配的正则
     * @param map        数据源
     * @return 返回匹配的数据
     */
    private Map<String, Object> getDateValue(String groupValue, Map<String, Object> map) {
        Matcher matcher = PATTERN_DATE.matcher(groupValue);
        Map<String, Object> matchMap = new HashMap<>();
        while (matcher.find()) {
            //如何得到value值
            String keyValue = matcher.group();
            String value = "";
            String key = groupValue.replace("(" + keyValue + ")", "");
            if (key.contains(".")) {
                map = getEntityValue(key, map);
            }
            if (Arrays.asList(LimCodeHelper.DATE_FORMAT).contains(keyValue)) {
                Object objectValue = map.get(key);
                if (StringUtil.isNotNull(objectValue)) {
                    if (objectValue instanceof Date) {
                        value = getDate((Date) objectValue, keyValue);
                    } else {
                        value = getDate(DateUtil.stringToDate(String.valueOf(objectValue), DateUtil.FULL), keyValue);
                    }
                }
            }
            matchMap.put(groupValue, value);
        }
        return matchMap;
    }

    /**
     * 获取带实体的数据源 如sample.code
     *
     * @param groupValue 匹配的正则
     * @param map        数据源
     * @return 返回匹配的数据
     */
    private Map<String, Object> getEntityValue(String groupValue, Map<String, Object> map) {
        Map<String, Object> matchMap = new HashMap<>();
        if (groupValue.contains(".")) {
            String[] groups = groupValue.split("\\.");
            Object value = null;
            for (String group : groups) {
                Object object = map.get(group);
                if (object instanceof Map) {
                    map = (Map) object;
                } else {
                    value = object;
                }
            }
            matchMap.put(groupValue, value);
        }
        return matchMap;
    }

    /**
     * 获取相应的指定日期格式数据
     *
     * @param date   日期
     * @param format 日期格式
     * @return 返回想要的数据源
     */
    private String getDate(Date date, String format) {
        return DateUtil.dateToString(date, format);
    }

    /**
     * 删除缓存
     *
     * @param serialType 流水号类型
     */
    private void deleteSerialNumberConfig(String serialType) {
        redisTemplate.delete(serialType + PrincipalContextUser.getPrincipal().getOrgId());
    }

}
