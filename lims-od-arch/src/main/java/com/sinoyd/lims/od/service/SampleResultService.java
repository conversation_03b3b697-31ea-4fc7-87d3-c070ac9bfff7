package com.sinoyd.lims.od.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.od.dto.DtoLabGroup;
import com.sinoyd.lims.od.dto.DtoSampleResult;
import com.sinoyd.lims.od.dto.customer.DtoGroupSummaryTemp;
import com.sinoyd.lims.od.dto.customer.DtoSampleResultTemp;

import java.util.List;

/**
 * 样品结果操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/3
 * @since V100R001
 */
public interface SampleResultService extends IBaseJpaService<DtoSampleResult, String> {

    /**
     * 根据样品id取最终结果实验组
     *
     * @param sampleId 样品id
     * @return 最终结果实验组
     */
    List<DtoGroupSummaryTemp> findDefaultGroupBySampleId(String sampleId);


    /**
     * 计算样品结果
     *
     * @param sampleResultTemp 样品结果汇总传输实体
     * @return 样品结果
     */
    DtoSampleResult calculate(DtoSampleResultTemp sampleResultTemp);

    /**
     * 获取实验组汇总信息
     *
     * @param groupSummaryTemps 实验数据汇总临时传输实体
     * @return 实验组数据汇总
     */
    List<DtoLabGroup> findGroupSummary(List<DtoGroupSummaryTemp> groupSummaryTemps);

    /**
     * 根据样品id查询样品结果
     *
     * @param sampleId 样品id
     * @return 样品结果
     */
    DtoSampleResult findBySampleId(String sampleId);

    /**
     * 完成实验
     *
     * @param sampleId 样品id
     * @return 样品结果
     */
    DtoSampleResult complete(String sampleId);
}
