package com.sinoyd.lims.lim.dto.customer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.dto.customer.PoiBaseEntity;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;
import javax.validation.constraints.Pattern;

/**
 * 测试项目导出实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/9/6
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Table(name = "TB_LIM_Test")
@Data
public class DtoExportTest extends PoiBaseEntity {

    /**
     * 主键id
     */
    @Excel(name = "主键id", orderNum = "10")
    private String id;

    /**
     * 父级Id
     */
    @Excel(name = "父级Id", orderNum = "20")
    private String parentId;

    /**
     * 分析方法Id（Guid）
     */
    @Excel(name = "分析方法Id", orderNum = "30")
    private String analyzeMethodId;

    /**
     * 分析方法名称
     */
    @Excel(name = "分析方法名称", orderNum = "40")
    private String redAnalyzeMethodName;

    /**
     * 国家标准
     */
    @Excel(name = "国家标准", orderNum = "50")
    private String redCountryStandard;

    /**
     * 分析项目Id（Guid）
     */
    @Excel(name = "分析项目Id", orderNum = "60")
    private String analyzeItemId;

    /**
     * 分析项目名称
     */
    @Excel(name = "分析项目名称", orderNum = "70")
    private String redAnalyzeItemName;

    /**
     * 分析项目全拼
     */
    @Excel(name = "分析项目全拼", orderNum = "80")
    private String fullPinYin;

    /**
     * 分析项目拼音缩写
     */
    @Excel(name = "分析项目拼音缩写", orderNum = "90")
    private String pinYin;

    /**
     * 样品类型（Guid）
     */
    @Excel(name = "样品类型", orderNum = "100")
    private String sampleTypeId;

    /**
     * 测试编码
     */
    @Excel(name = "测试编码", orderNum = "110")
    private String testCode;

    /**
     * 计量单位（Guid）
     */
    @Excel(name = "计量单位", orderNum = "120")
    private String dimensionId;

    /**
     * 检出限
     */
    @Excel(name = "检出限", orderNum = "130")
    private String examLimitValue;

    /**
     * 样品有效期（h）
     */
    @Excel(name = "样品有效期", orderNum = "140")
    @Pattern(regexp = "^[+-]?(\\d+\\.?\\d*|\\.\\d+)$", message = "样品有效期格式不符合要求")
    private String validTime;

    /**
     * 排序值
     */
    @Excel(name = "排序值", orderNum = "150")
    @Pattern(regexp = "^-?\\d+$", message = "排序值必须为整数")
    private String orderNum;

    /**
     * 有效位数
     */
    @Excel(name = "有效位数", orderNum = "160")
    @Pattern(regexp = "^-?\\d+$", message = "有效位数必须为整数")
    private String mostSignificance;

    /**
     * 小数位数
     */
    @Excel(name = "小数位数", orderNum = "170")
    @Pattern(regexp = "^-?\\d+$", message = "小数位数必须为整数")
    private String mostDecimal;

    /**
     * 测试资质(枚举EnumCert：0非认可认证、1认证、2认可、4认证认可）
     */
    @Excel(name = "测试资质", orderNum = "180")
    @Pattern(regexp = "^-?\\d+$", message = "测试资质必须为整数")
    private String cert;

    /**
     * 测试名称
     */
    @Excel(name = "测试名称", orderNum = "190")
    private String testName;

    /**
     * 是否采测分包
     */
    @Excel(name = "是否采测分包", orderNum = "200")
    @Pattern(regexp = "^(true|false)$", message = "是否采测分包必须为布尔类型")
    private String isOutsourcing;

    /**
     * 是否分析分包
     */
    @Excel(name = "是否分析分包", orderNum = "205")
    @Pattern(regexp = "^(true|false)$", message = "是否分析分包必须为布尔类型")
    private String isSamplingOut;


    /**
     * 是否现场数据
     */
    @Excel(name = "是否现场数据", orderNum = "210")
    @Pattern(regexp = "^(true|false)$", message = "是否现场数据必须为布尔类型")
    private String isCompleteField;

    /**
     * 是否删除
     */
    @Excel(name = "是否删除", orderNum = "220")
    @Pattern(regexp = "^(true|false)$", message = "是否删除必须为布尔类型")
    private String isDeleted;

    /**
     * 是否做质控平行
     */
    @Excel(name = "是否做质控平行", orderNum = "230")
    @Pattern(regexp = "^(true|false)$", message = "是否做质控平行必须为布尔类型")
    private String isQCP;

    /**
     * 是否做质控空白
     */
    @Excel(name = "是否做质控空白", orderNum = "240")
    @Pattern(regexp = "^(true|false)$", message = "是否做质控空白必须为布尔类型")
    private String isQCB;

    /**
     * 是否做串联样
     */
    @Excel(name = "是否做串联样", orderNum = "250")
    @Pattern(regexp = "^(true|false)$", message = "是否做串联样必须为布尔类型")
    private String isSeries;

    /**
     * 是否启用公式
     */
    @Excel(name = "是否启用公式", orderNum = "260")
    @Pattern(regexp = "^(true|false)$", message = "是否启用公式必须为布尔类型")
    private String isUseFormula;

    /**
     * 小于检出限出证结果
     */
    @Excel(name = "小于检出限出证结果", orderNum = "270")
    private String examLimitValueLess;

    /**
     * 周期数
     */
    @Excel(name = "周期数", orderNum = "90")
    @Pattern(regexp = "^-?\\d+$", message = "周期数必须为整数")
    private String timesOrder;

    /**
     * 样品数量
     */
    @Excel(name = "样品数量", orderNum = "90")
    @Pattern(regexp = "^-?\\d+$", message = "样品数量必须为整数")
    private String samplePeriod;

    /**
     * 测定下限
     */
    @Excel(name = "测定下限", orderNum = "290")
    private String lowerLimit;

    /**
     * 总称（冗余分析项目名称）
     */
    @Excel(name = "总称", orderNum = "300")
    private String totalTestName;

    /**
     * 是否显示总称
     */
    @Excel(name = "是否显示总称", orderNum = "310")
    @Pattern(regexp = "^(true|false)$", message = "是否显示总称必须为布尔类型")
    private String isShowTotalTest;

    /**
     * 是否总称
     */
    @Excel(name = "是否总称", orderNum = "320")
    @Pattern(regexp = "^(true|false)$", message = "是否总称必须为布尔类型")
    private String isTotalTest;

    /**
     * 是否启用嵌套公式(检测结果计算出证结果)默认false
     */
    @Excel(name = "是否启用嵌套公式", orderNum = "330")
    @Pattern(regexp = "^(true|false)$", message = "是否启用嵌套公式必须为布尔类型")
    private String isUseQTFormula;

    /**
     * 斜率有效位数
     */
    @Excel(name = "斜率有效位数", orderNum = "340")
    @Pattern(regexp = "^-?\\d+$", message = "斜率有效位数必须为整数")
    private String valueFormatK;

    /**
     * 斜率小数位数
     */
    @Excel(name = "斜率小数位数", orderNum = "350")
    @Pattern(regexp = "^-?\\d+$", message = "斜率小数位数必须为整数")
    private String decimalFormatK;

    /**
     * 截距有效位数
     */
    @Excel(name = "截距有效位数", orderNum = "360")
    @Pattern(regexp = "^-?\\d+$", message = "截距有效位数必须为整数")
    private String valueFormatB;

    /**
     * 截距小数位数
     */
    @Excel(name = "截距小数位数", orderNum = "370")
    @Pattern(regexp = "^-?\\d+$", message = "截距小数位数必须为整数")
    private String decimalFormatB;

    /**
     * 实数小数位数
     */
    @Excel(name = "实数小数位数", orderNum = "380")
    @Pattern(regexp = "^-?\\d+$", message = "实数小数位数必须为整数")
    private String valueFormatC;

    /**
     * 实数小数位数
     */
    @Excel(name = "实数小数位数", orderNum = "390")
    @Pattern(regexp = "^-?\\d+$", message = "实数小数位数必须为整数")
    private String decimalFormatC;

    /**
     * 采样费金额
     */
    @Excel(name = "采样费金额", orderNum = "400")
    @Pattern(regexp = "^[+-]?(\\d+\\.?\\d*|\\.\\d+)$", message = "采样费金额格式不符合要求")
    private String samplingCharge;

    /**
     * 检测费金额
     */
    @Excel(name = "检测费金额", orderNum = "410")
    @Pattern(regexp = "^[+-]?(\\d+\\.?\\d*|\\.\\d+)$", message = "检测费金额格式不符合要求")
    private String testingCharge;

    /**
     * 报告计量单位（Guid）
     */
    @Excel(name = "报告计量单位", orderNum = "420")
    private String reportDimensionId;

    /**
     * 报告有效位数
     */
    @Excel(name = "报告有效位数", orderNum = "430")
    @Pattern(regexp = "^-?\\d+$", message = "报告有效位数必须为整数")
    private String reportMostSignificance;

    /**
     * 报告小数位数
     */
    @Excel(name = "报告小数位数", orderNum = "440")
    @Pattern(regexp = "^-?\\d+$", message = "报告小数位数必须为整数")
    private String reportMostDecimal;

    /**
     * 备注（预留）
     */
    @Excel(name = "备注", orderNum = "450")
    private String remark;

    /**
     * 是否填写仪器使用记录（预留）
     */
    @Excel(name = "是否填写仪器使用记录", orderNum = "460")
    @Pattern(regexp = "^(true|false)$", message = "是否填写仪器使用记录必须为布尔类型")
    private String isInsUseRecord;

    /**
     * 是否是检测机构传输过来的测试项目（预留）
     */
    @Excel(name = "是否是检测机构传输过来的测试项目", orderNum = "470")
    @Pattern(regexp = "^(true|false)$", message = "是否是检测机构传输过来的测试项目必须为布尔类型")
    private String isSubSync;

    /**
     * 录入方式（预留）（常量Int，常量名称Lim_TestInputMode）（0：默认，1：生物多样性）
     */
    @Excel(name = "录入方式", orderNum = "480")
    @Pattern(regexp = "^-?\\d+$", message = "录入方式必须为整数")
    private String inputMode;

    /**
     * 均值计算方式（0：算术均值，1：原样值，2：几何均值）
     */
    @Excel(name = "均值计算方式", orderNum = "490")
    @Pattern(regexp = "^-?\\d+$", message = "均值计算方式必须为整数")
    private String averageCompute;

    /**
     * 实验室编号（预留）
     */
    @Excel(name = "实验室编号", orderNum = "500")
    private String domainCode;

    /**
     * 年份（预留）
     */
    @Excel(name = "年份", orderNum = "510")
    private String redYearSn;

    /**
     * 分配时长（预留）
     */
    @Excel(name = "分配时长", orderNum = "520")
    @Pattern(regexp = "^-?\\d+$", message = "分配时长必须为整数")
    private String testTimelen;

    /**
     * 基础工作量（预留）
     */
    @Excel(name = "基础工作量", orderNum = "530")
    @Pattern(regexp = "^[+-]?(\\d+\\.?\\d*|\\.\\d+)$", message = "基础工作量格式不符合要求")
    private String basicWorkload;

    /**
     * 单位工作量（预留）
     */
    @Excel(name = "单位工作量", orderNum = "540")
    @Pattern(regexp = "^[+-]?(\\d+\\.?\\d*|\\.\\d+)$", message = "单位工作量格式不符合要求")
    private String unitWorkload;

    /**
     * 关联系统测试项目编号
     */
    @Excel(name = "关联系统测试项目编号", orderNum = "550")
    private String externalId;

    /**
     * 修约方式（参考枚举 EnumReviseType）（1：先修约再比较，2：先比较再修约）
     */
    @Excel(name = "修约方式", orderNum = "620")
    @Pattern(regexp = "^-?\\d+$", message = "修约方式必须为整数")
    private String reviseType;

    /**
     * 计算方式, 参考枚举 EnumCalculateWay（0： 先修约后计算，1：先计算后修约）
     */
    @Excel(name = "计算方式", orderNum = "630")
    @Pattern(regexp = "^-?\\d+$", message = "计算方式必须为整数")
    private String calculateWay;

    /**
     * 合并基数
     */
    @Excel(name = "合并基数", orderNum = "640")
    @Pattern(regexp = "^-?\\d+$", message = "合并基数必须为整数")
    private String mergeBase;

    /**
     * 分析时长（天数）
     */
    @Excel(name = "分析时长", orderNum = "650")
    @Pattern(regexp = "^-?\\d+$", message = "分析时长必须为整数")
    private String analyseDayLen;

    /**
     * 验证状态 0未验证 1已验证
     */
    @Excel(name = "验证状态", orderNum = "660")
    @Pattern(regexp = "^-?\\d+$", message = "验证状态必须为整数")
    private String validate;

    /**
     * 使用次数
     */
    @Excel(name = "使用次数", orderNum = "670")
    @Pattern(regexp = "^-?\\d+$", message = "使用次数必须为整数")
    private String usageNum;

    /**
     * 特殊首字母字段DtoTest实体转入DtoExportTest
     *
     * @param test DtoTest实体
     */
    public void processSpecial(DtoTest test) {
        this.decimalFormatC = test.getCDecimalFormat().toString();
        this.valueFormatK = test.getKValueFormat().toString();
        this.decimalFormatK = test.getKDecimalFormat().toString();
        this.valueFormatB = test.getBValueFormat().toString();
        this.decimalFormatB = test.getBDecimalFormat().toString();
        this.valueFormatC = test.getCValueFormat().toString();
    }

    /**
     * 特殊首字母字段DtoExportTest实体转入DtoTest
     *
     * @param test       DtoTest
     * @param exportTest DtoExportTest
     */
    public void processSpecial(DtoTest test, DtoExportTest exportTest) {
        test.setCDecimalFormat(StringUtil.isNotEmpty(exportTest.getDecimalFormatC()) ? Integer.parseInt(exportTest.getDecimalFormatC()) : -1);
        test.setKValueFormat(StringUtil.isNotEmpty(exportTest.getValueFormatK()) ? Integer.parseInt(exportTest.getValueFormatK()) : -1);
        test.setKDecimalFormat(StringUtil.isNotEmpty(exportTest.getDecimalFormatK()) ? Integer.parseInt(exportTest.getDecimalFormatK()) : -1);
        test.setBValueFormat(StringUtil.isNotEmpty(exportTest.getValueFormatB()) ? Integer.parseInt(exportTest.getValueFormatB()) : -1);
        test.setBDecimalFormat(StringUtil.isNotEmpty(exportTest.getDecimalFormatB()) ? Integer.parseInt(exportTest.getDecimalFormatB()) : -1);
        test.setCValueFormat(StringUtil.isNotEmpty(exportTest.getValueFormatC()) ? Integer.parseInt(exportTest.getValueFormatC()) : -1);
    }
}
