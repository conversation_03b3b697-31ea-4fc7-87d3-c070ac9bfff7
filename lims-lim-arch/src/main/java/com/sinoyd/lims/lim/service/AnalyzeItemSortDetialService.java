package com.sinoyd.lims.lim.service;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;

import java.util.Collection;
import java.util.List;


/**
 * 分析项目排序
 * <AUTHOR>
 * @version V1.0.0 2019/1/14
 * @since V100R001
 */
public interface AnalyzeItemSortDetialService extends IBaseJpaService<DtoAnalyzeItemSortDetail, String> {

    /**
     * 保存分析项目排序详情
     * @param sortId 排序id
     * @param analyzeItems 分析项目列表（前台排好序的）
     * @return 保存的记录条数
     */
   Integer saveSortDetails(String sortId, Collection<DtoAnalyzeItem> analyzeItems);

   /**
    * 获取分析项目排序详情
    * @param sortId 排序id
    * @return 分析项目排序详情列表
    */
   List<DtoAnalyzeItemSortDetail> getSortDetailList(String sortId);
   

}