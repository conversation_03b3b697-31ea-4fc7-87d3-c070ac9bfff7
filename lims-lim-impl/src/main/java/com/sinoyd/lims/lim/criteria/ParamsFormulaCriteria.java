package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 参数公式查询条件
 * <AUTHOR>
 * @version V1.0.0 2022/04/23
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParamsFormulaCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    */
    private String id;

    @Override
    public String getCondition() {
        /**
         * 清除条件数据
         */
        values.clear();
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and a.id = :id");
            values.put("id", this.id);
        }

        condition.append(" and a.isDeleted = 0 ");
        return condition.toString();
    }
}