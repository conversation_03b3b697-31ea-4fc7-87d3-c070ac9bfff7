package com.sinoyd.lims.lim.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * InstrumentMaintainRecord实体
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
@MappedSuperclass
@ApiModel(description = "InstrumentMaintainRecord")
@Data
@EntityListeners(AuditingEntityListener.class)
public class InstrumentMaintainRecord implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public InstrumentMaintainRecord() {
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 设备Id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("设备Id")
    private String instrumentId;

    /**
     * 维护单位
     */
    @Column(length = 100)
    @ApiModelProperty("维护单位")
    @Length(max = 100, message = "维护单位长度不能超过100")
    private String maintainDeptName;

    /**
     * 开始维护时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("开始维护时间")
    private Date startTime;

    /**
     * 结束维护时间
     */
    @Column(nullable = false)
    @ColumnDefault("'1753-1-1'")
    @ApiModelProperty("结束维护时间")
    private Date endTime;

    /**
     * 维护人员id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("维护人员id")
    private String mainTainPersonId;

    /**
     * 维护人员
     */
    @Column(length = 50)
    @ApiModelProperty("维护人员")
    @Length(max = 50, message = "维护人员长度不能超过50")
    private String maintainPerson;

    /**
     * 维护内容
     */
    @Column(length = 1000)
    @ApiModelProperty("维护内容")
    @Length(max = 1000, message = "维护内容长度不能超过1000")
    private String maintainContent;

    /**
     * 维护规则
     */
    @Column(length = 1000)
    @ApiModelProperty("维护规则")
    @Length(max = 1000, message = "维护规则长度不能超过1000")
    private String maintainRule;

    /**
     * 备注
     */
    @Column(length = 1000)
    @ApiModelProperty("备注")
    @Length(max = 1000, message = "备注长度不能超过1000")
    private String maintainremark;

    /**
     * 费用
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @Digits(integer = 18, fraction = 2, message = "费用整数位精度18小数位精度2")
    @ApiModelProperty("费用")
    private BigDecimal cost;

    /**
     * 维护类型（3.2保留,及时未启用）
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("维护类型（3.2保留,及时未启用）")
    private String maintainTypeId;

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;


    /**
     * 温度
     */
    @Column(length = 50)
    @ApiModelProperty("温度")
    @Length(message = "温度{validation.message.length}", max = 50)
    private String temperature;

    /**
     * 湿度
     */
    @Column(length = 50)
    @ApiModelProperty("湿度")
    @Length(message = "湿度{validation.message.length}", max = 50)
    private String humidity;

}