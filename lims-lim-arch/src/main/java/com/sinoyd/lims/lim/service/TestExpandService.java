package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;

import java.util.List;

/**
 * 测试扩展接口
 * <AUTHOR>
 * @version V1.0.0 2019/1/16
 * @since V100R001
 */
public interface TestExpandService extends IBaseJpaService<DtoTestExpand, String> {

    /**
     * 获取测试扩展
     *
     * @param sampleTypeId 样品类型小类id
     * @param testId       测试项目id
     * @return DtoTestExpand
     */
    DtoTestExpand get(String sampleTypeId, String testId);

    /**
     * 清空测试扩展
     *
     * @param sampleTypeId 样品类型小类id
     * @param testId       测试项目id
     * @return 是否清空成功
     */
    Boolean delete(String sampleTypeId, String testId);


    /**
     * 根据测试项目ids获取相关的测试扩展信息
     *
     * @param testIds 测试项目ids
     * @return 返回测试扩展信息
     */
    List<DtoTestExpand> findRedisByTestIds(List<String> testIds);

    /**
     * 根据测试项目id获取相关的测试扩展信息
     *
     * @param testId 测试项目ids
     * @return 返回测试扩展信息
     */
    List<DtoTestExpand> findRedisByTestId(String testId);

    /**
     * 获取去重的检测类型id
     *
     * @param testId 测试项目id
     * @return 返回去重的检测类型id
     */
    List<String> findSampleTypeIdByTestId(String testId);

    /**
     * 根据测试项目ids获取相关的测试扩展信息
     *
     * @param testIds 测试项目ids
     * @return 返回测试扩展信息
     */
    List<DtoTestExpand> findByTestIds(List<String> testIds);
}