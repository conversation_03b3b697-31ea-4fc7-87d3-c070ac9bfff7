package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ELogType;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.dto.lims.DtoOAFileControl;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.service.OAProcFileControlService;

import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件受控服务接口定义
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-08
 * @since V100R001
 */
@Api(tags = "工作流: 文件受控服务")
@RestController
@RequestMapping("/api/pro/oaFileControls")
public class OAProcFileControlController extends ExceptionHandlerController<OAProcFileControlService> {
    /**
     * 添加文件受控申请
     * 
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加文件受控申请", notes = "添加文件受控申请启动流程")
    @PostMapping
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> startProcess(@RequestBody DtoOATaskCreate<DtoOAFileControl> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        String procInstId = service.startProcess(taskDto);
        restResp.setData(procInstId);

        return restResp;
    }

    /**
     * 保存为草稿
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "保存为草稿", notes = "保存为草稿")
    @PostMapping("/saveAsDraft")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> saveAsDraft(@RequestBody DtoOATaskCreate<DtoOAFileControl> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.saveAsDraft(taskDto));
        return restResp;
    }

    /**
     * 草稿保存
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "草稿保存", notes = "草稿保存")
    @PostMapping("/draftSave")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<OATask> draftSave(@RequestBody DtoOATaskCreate<DtoOAFileControl> taskDto) {
        RestResponse<OATask> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSave(taskDto));
        return restResp;
    }

    /**
     * 草稿提交
     *
     * @param taskDto 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加审批", notes = "添加审批启动流程")
    @PostMapping("/draftSubmit")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> draftSubmit(@RequestBody DtoOATaskCreate<DtoOAFileControl> taskDto) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(service.draftSubmit(taskDto));
        return restResp;
    }

    /**
     * 查询文件受控信息
     * 
     * @param taskId 参数对象
     * @return RestResponse<String>
     */
    @ApiOperation(value = "查询文件受控信息", notes = "查询文件受控信息")
    @GetMapping(path = "/task/{taskId}")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<DtoOATaskDetail<DtoOAFileControl, DtoFileControlApplyDetail>> findDetailByTaskId(@PathVariable(name = "taskId") String taskId) {
        RestResponse<DtoOATaskDetail<DtoOAFileControl, DtoFileControlApplyDetail>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        DtoOATaskDetail<DtoOAFileControl, DtoFileControlApplyDetail> detail = service.findOATaskDetail(taskId);
        restResp.setData(detail);

        return restResp;
    }

    /**
     * 文件上传
     * @param procInstId 工作流id
     * @param files 文件集合
     * @param ids 受控申请文件明细id集合
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加附件", notes = "添加附件")
    @PostMapping(path = "/uploadFilesControl")
    public RestResponse<String> uploadFilesControl(@RequestParam(name = "procInstId") String procInstId,
                                                   @RequestParam(name = "ids") List<String> ids,
                                                   List<MultipartFile> files){
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        service.uploadFilesControl(procInstId, ids, files);
        return restResp;
    }
}
