package com.sinoyd.lims.lim.service.transform;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.config.WebSocketServer;
import com.sinoyd.base.dto.customer.DtoImportTest;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.utils.poi.PoiExcelUtils;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.lim.data.mapper.TestMapper;
import com.sinoyd.lims.lim.dto.customer.*;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.lims.DtoTestExpand;
import com.sinoyd.lims.lim.dto.rcc.DtoAnalyzeMethod;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.AnalyzeMethodRepository;
import com.sinoyd.lims.lim.verify.TransformTestVerifyHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试项目数据迁移测试项目导入策略类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @since 2023/9/25
 */
@Component
@Order(60)
public class TestStrategy implements TransformImportStrategy {
    /**
     * sheet页序号
     */
    private static final int sheetIndex = 0;

    private TestRepository testRepository;

    private TransformTestVerifyHandler transformTestVerifyHandler;

    private TestMapper testMapper;

    private AnalyzeMethodRepository analyzeMethodRepository;

    @Autowired
    private RedisTemplate redisTemplate;

    private static final String[] IMPORT_TEST_TYPE = {"已存在（ID相同）", "已存在（ID不同）", "新增"};

    @Override
    @Transactional
    public void importSheetData(InputStream inputStream, HttpServletResponse response, ImportParams params) throws Exception {
        //参数初始化
        handleInit();
        params.setStartSheetIndex(sheetIndex);
        params.setVerifyHandler(transformTestVerifyHandler);
        //获取校验结果集
        ExcelImportResult<DtoExportTest> result = ExcelImportUtil.importExcelMore(inputStream, DtoExportTest.class, params);
        // 检验失败抛出异常并返回错误文档
        if (result.isVerfiyFail()) {
            Workbook failWorkbook = result.getFailWorkbook();
            failWorkbook.setSheetName(sheetIndex, "导入错误信息");
            PoiExcelUtils.downLoadExcel("测试项目导入失败信息", response, failWorkbook);
            throw new BaseException("导入信息中有数据不正确，确认后导入");
        }
        //构建可保存数据
        List<DtoTest> waitSaveList = new ArrayList<>();
        buildRightData(result, waitSaveList);
        //数据保存
        testRepository.save(waitSaveList);
    }

    @Override
    public void getAddData(Map<String, DtoBaseData> substituteMap, Map<String, DtoBaseData> sampleTypeBindMap, DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData, DtoImportTestTemp importTestTemp, DtoTestDependentData exportData) {
        List<DtoExportTest> testList = testDependentData.getTestList();
        if (StringUtil.isNotEmpty(testList)) {
            // 检查后的测试项目，筛选已存在id相同，和新增的数据
            List<DtoImportCheck> importChecks = dtoDataSyncParams.getImportChecks();
            List<DtoDataCheck> dataChecks = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.测试项目表.getCheckItem()))
                    .flatMap(p -> p.getDataChecks().stream()).collect(Collectors.toList());

            List<String> importTestIds = dataChecks.stream().filter(p -> !IMPORT_TEST_TYPE[1].equals(p.getType())).map(DtoDataCheck::getId).collect(Collectors.toList());
            List<DtoExportTest> importTestList = testList.stream().filter(p -> importTestIds.contains(p.getId())).collect(Collectors.toList());

            // 导入前的检查数据
            List<DtoDataCheck> analyzeItemCheck = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.分析项目表.getCheckItem()))
                    .flatMap(p -> p.getDataChecks().stream()).collect(Collectors.toList());
            Map<String, DtoDataCheck> itemCheckMap = analyzeItemCheck.stream().collect(Collectors.toMap(DtoDataCheck::getName, p -> p));
            List<DtoDataCheck> analyzeMethodCheck = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.分析方法表.getCheckItem()))
                    .flatMap(p -> p.getDataChecks().stream()).collect(Collectors.toList());
            Map<String, DtoDataCheck> methodCheckMap = analyzeMethodCheck.stream().collect(Collectors.toMap(DtoDataCheck::getName, p -> p));
            List<String> analyzeMethodIds = analyzeMethodCheck.stream().map(DtoDataCheck::getId).collect(Collectors.toList());
            List<DtoDataCheck> dimensionCheck = importChecks.stream().filter(p -> p.getCheckItem().equals(EnumLIM.EnumImportTestType.量纲表.getCheckItem()))
                    .flatMap(p -> p.getDataChecks().stream()).collect(Collectors.toList());
            Map<String, DtoDataCheck> dimensionCheckMap = dimensionCheck.stream().collect(Collectors.toMap(DtoDataCheck::getName, p -> p));
            // 获取公共库所有测试项目
            List<DtoTest> updateOrInsertTests = new ArrayList<>();
            List<DtoExportTest> exportTests = new ArrayList<>();
            for (DtoExportTest exportTest : importTestList) {
                DtoDataCheck itemCheck = itemCheckMap.getOrDefault(exportTest.getRedAnalyzeItemName(), new DtoDataCheck());
                // 分析方法由于是方法名和标准号组合，所以进行模糊匹配
                DtoDataCheck methodCheck = methodCheckMap.entrySet().stream().filter(p -> p.getKey().contains(exportTest.getRedAnalyzeMethodName())).map(Map.Entry::getValue).findFirst().orElse(new DtoDataCheck());
                DtoDataCheck dimCheckMap = dimensionCheckMap.getOrDefault(exportTest.getDimensionId(), new DtoDataCheck());
                DtoBaseData dtoBaseData = sampleTypeBindMap.getOrDefault(exportTest.getSampleTypeId(), new DtoBaseData());

                // 已存在，ID相同，直接更新不插入，ID不同，不插入，导出未导入的测试项目
                updateOrInsertTests.add(createDtoTest(exportTest, itemCheck, methodCheck, dimCheckMap, dtoBaseData));
            }
            // 测试项目待更新和新增数据
            importTestTemp.setTestTemps(updateOrInsertTests);
            List<String> noImportTestIds = dataChecks.stream().filter(p -> IMPORT_TEST_TYPE[1].equals(p.getType())).map(DtoDataCheck::getId).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(noImportTestIds)) {
                exportTests = testList.stream().filter(p -> noImportTestIds.contains(p.getId())).collect(Collectors.toList());
                exportData.setTestList(exportTests);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(DtoImportTestTemp importTestTemp, WebSocketServer webSocketServer) {
        List<DtoTest> testTemps = importTestTemp.getTestTemps();
        if (StringUtil.isNotEmpty(testTemps)) {
            //已同步记录数
            int i = 0;
            String key = EnumLIM.EnumLIMRedis.getRedisKey(EnumLIM.EnumLIMRedis.LIM_OrgId_Test.getValue());
            for (DtoTest test : testTemps) {
                DtoTest item = testRepository.save(test);
                // 更新redis 数据
                redisTemplate.opsForHash().put(key, item.getId(), JsonStream.serialize(item));
                webSocketServer.sendMessage(getMessage(testTemps.size(), ++i));
            }
        } else {
            webSocketServer.sendMessage(getMessage(testTemps.size(), 0));
        }
    }

    @Override
    public int getAddDataOrderNum() {
        return EnumLIM.EnumImportTestType.测试项目表.getValue();
    }

    @Override
    public String getTableName() {
        return EnumLIM.EnumImportTestType.测试项目表.getSource();
    }

    @Override
    public String getTableRemark() {
        return EnumLIM.EnumImportTestType.测试项目表.name();
    }

    @Override
    public List<DtoImportCheck> check(DtoDataSyncParams dtoDataSyncParams, DtoTestDependentData testDependentData) {
        List<DtoExportTest> testList = testDependentData.getTestList();
        // 获取依赖数据检测结果，分析项目分析方法和检测类型
        List<DtoBaseData> sampleTypeBinds = dtoDataSyncParams.getSampleTypeBinds();
        Map<String, DtoBaseData> sampleTypeBindMap = sampleTypeBinds.stream().collect(Collectors.toMap(DtoBaseData::getSourceName, p -> p));

        List<DtoImportCheck> importChecks = new ArrayList<>();
        List<DtoDataCheck> testCheckList = new ArrayList<>();
        if (StringUtil.isNotEmpty(testList)) {
            List<DtoTest> allTestList = testRepository.findAll();
            for (DtoExportTest exportTest : testList) {
                DtoBaseData dtoBaseData = sampleTypeBindMap.getOrDefault(exportTest.getSampleTypeId(), new DtoBaseData());
                // 根据分析项目名称、分析方法、检测类型判断该测试项目在系统内是否已存在：
                Optional<DtoTest> testOptional = allTestList.stream()
                        .filter(p -> exportTest.getRedAnalyzeItemName().equals(p.getRedAnalyzeItemName()) &&
                                exportTest.getRedAnalyzeMethodName().equals(p.getRedAnalyzeMethodName()) &&
                                p.getSampleTypeId().equals(dtoBaseData.getTargetId())).findFirst();
                DtoDataCheck dtoDataCheck = new DtoDataCheck();
                dtoDataCheck.setId(exportTest.getId());
                Map<String, Object> otherField = new HashMap<>();
                otherField.put("redAnalyzeItemName", exportTest.getRedAnalyzeItemName());
                otherField.put("redAnalyzeMethodName", exportTest.getRedAnalyzeMethodName());
                otherField.put("redCountryStandard", exportTest.getRedCountryStandard());
                otherField.put("sampleType", exportTest.getSampleTypeId());
                dtoDataCheck.setOtherField(otherField);
                String importTestType = "";
                if (testOptional.isPresent()) {
                    if (testOptional.get().getId().equals(exportTest.getId())) {
                        importTestType = IMPORT_TEST_TYPE[0];
                    } else {
                        importTestType = IMPORT_TEST_TYPE[1];
                    }
                } else {
                    importTestType = IMPORT_TEST_TYPE[2];
                }
                dtoDataCheck.setType(importTestType);
                testCheckList.add(dtoDataCheck);
            }
        }

        List<DtoDataCheck> exists = testCheckList.stream().filter(p -> IMPORT_TEST_TYPE[0].equals(p.getType())).collect(Collectors.toList());
        List<DtoDataCheck> existsNotId = testCheckList.stream().filter(p -> IMPORT_TEST_TYPE[1].equals(p.getType())).collect(Collectors.toList());
        List<DtoDataCheck> noExists = testCheckList.stream().filter(p -> IMPORT_TEST_TYPE[2].equals(p.getType())).collect(Collectors.toList());
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.测试项目表.getCheckItem(),
                IMPORT_TEST_TYPE[0], exists.size(), "执行更新操作，更新现有数据。", exists));
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.测试项目表.getCheckItem(),
                IMPORT_TEST_TYPE[1], existsNotId.size(), "忽略该测试项目，及其关联数据，均不进行导入。", existsNotId));
        importChecks.add(new DtoImportCheck(EnumLIM.EnumImportTestType.测试项目表.getCheckItem(),
                IMPORT_TEST_TYPE[2], noExists.size(), "新增测试项目，导入时将进行插入。", noExists));
        return importChecks;
    }

    /**
     * 创建测试项目
     *
     * @param exportTest  到入的测试项目
     * @param itemCheck   分析项目检查数据
     * @param methodCheck 分析方法检查数据
     * @param dimCheckMap 量纲检查数据
     * @param dtoBaseData 基础数据绑定数据
     * @return DtoTest
     */
    private DtoTest createDtoTest(DtoExportTest exportTest, DtoDataCheck itemCheck, DtoDataCheck methodCheck, DtoDataCheck dimCheckMap, DtoBaseData dtoBaseData) {
        DtoTest dtoTest = testMapper.toDtoTest(exportTest);
        exportTest.processSpecial(dtoTest, exportTest);
        dtoTest.setSampleTypeId(dtoBaseData.getTargetId());
        dtoTest.setAnalyzeItemId(itemCheck.getId());
        dtoTest.setRedAnalyzeItemName(itemCheck.getName());
        dtoTest.setRedAnalyzeMethodName(methodCheck.getOtherField().get("methodName").toString());
        dtoTest.setAnalyzeMethodId(methodCheck.getId());
        dtoTest.setDimensionId(dimCheckMap.getId());
        return dtoTest;
    }


    /**
     * 校验容器初始化
     */
    private void handleInit() {
        transformTestVerifyHandler = new TransformTestVerifyHandler();
        transformTestVerifyHandler.setRepoDataList(testRepository.findAll());
        transformTestVerifyHandler.setSheetExistDataList(new ArrayList<>());
    }

    /**
     * 构建可保存数据
     *
     * @param result       导入结果集
     * @param waitSaveList 待保存结果容器
     */
    private void buildRightData(ExcelImportResult<DtoExportTest> result, List<DtoTest> waitSaveList) {
        List<DtoExportTest> importList = result.getList();
        //跳过空行
        importList.removeIf(p -> StringUtil.isEmpty(p.getId()));
        for (DtoExportTest exportTest : importList) {
            DtoTest dtoTest = new DtoTest();
            BeanUtils.copyProperties(exportTest, dtoTest);
            waitSaveList.add(dtoTest);
        }
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    @Lazy
    public void setTestMapper(TestMapper testMapper) {
        this.testMapper = testMapper;
    }

    @Autowired
    @Lazy
    public void setAnalyzeMethodRepository(AnalyzeMethodRepository analyzeMethodRepository) {
        this.analyzeMethodRepository = analyzeMethodRepository;
    }
}
