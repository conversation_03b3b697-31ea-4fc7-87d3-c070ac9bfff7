package com.sinoyd.lims.lim.service.statistics;

import com.sinoyd.lims.lim.constants.LimConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 耗材（消耗品+标样）低库存统计业务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/22
 */
@Service
@Slf4j
public class ConsumableLowInventoryStatisticsServiceImpl extends AbsResourceStatisticsServiceImpl {

    @Override
    public String getStatisticsSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("select count(1) as num ")
                .append("from TB_BASE_Consumable c, ")
                .append("(select sum(d.storage) as totalStorage, d.parentId from TB_BASE_ConsumableDetail d group by d.parentId) temp ")
                .append("where c.id = temp.parentId ")
                .append("and c.warningNum >= temp.totalStorage ")
                .append("and temp.totalStorage > 0 ");
        return sql.toString();
    }

    @Override
    public String getStatisticsItemName() {
        return LimConstants.StatisticsItemName.CONSUMABLE_INVENTORY_WARN;
    }

    @Override
    public String getUnit() {
        return "件";
    }
}