package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentMaintainRecord;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/**
 * 仪器维护记录操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019-04-30
 * @since V100R001
 */
public interface InstrumentMaintainRecordRepository extends IBaseJpaPhysicalDeleteRepository<DtoInstrumentMaintainRecord, String> {


    /***
     * 获取维护单位
     * @return
     */
    @Query("select distinct d.maintainDeptName from DtoInstrumentMaintainRecord d where d.maintainDeptName <> null and d.maintainDeptName <> '' order by d.maintainDeptName")
    List<String> findMaintainDeptName();

    /**
     * 根据仪器id获取维护记录
     *
     * @param instrumentIds 仪器id
     */
    List<DtoInstrumentMaintainRecord> findByInstrumentIdIn(Collection<String> instrumentIds);
}