package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 采样计划安排查询条件
 *
 * <AUTHOR>
 * @version v1.0.0 2023/9/25
 * @since v100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamplingArrangeCriteria extends BaseCriteria {

    /**
     * 开始时间 预览部分查询用
     */
    private String samplingStartDate;

    /**
     * 结束时间 预览部分查询用
     */
    private String samplingEndDate;

    /**
     * 检测类型
     */
    private String sampleTypeId;

    /**
     * 计划安排状态  0/1
     */
    private Boolean isArrange;

    /**
     * 关键字 项目名称/项目编号/点位名称
     */
    private String keyWord;

    /**
     * 采样负责人
     */
    private String chargePersonId;

    /**
     * 项目类型
     */
    private List<String> projectRegisterPageList;

    /**
     * 模块
     */
    private String module;

    /**
     * 状态
     */
    private String status;


    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append("  and a.projectId = p.id");
        condition.append("  and a.sampleFolderId = d.id");
        condition.append("  and a.sampleTypeId = s.id");
        //仅显示任务状态为‘开展中’及之后状态的全部任务中的方案信息
        condition.append("  and  p.status in :status");
        List<String> statusList = new ArrayList<>();
        statusList.add(EnumPRO.EnumProjectStatus.开展中.name());
        statusList.add(EnumPRO.EnumProjectStatus.数据汇总中.name());
        statusList.add(EnumPRO.EnumProjectStatus.已办结.name());
        values.put("status", statusList);
        //过滤项目 不编制方案的项目
        condition.append(" and exists (select 1  from DtoProjectPlan c where p.id = c.projectId and c.isMakePlan = '1') ");
        //过滤项目 只保留projectRegisterPage为QLC QC WT的任务
        if (projectRegisterPageList != null && !projectRegisterPageList.isEmpty()) {
            condition.append(" and p.projectTypeId in (select id from DtoProjectType where")
                    .append(" JSON_VALID(config) = 1 and ( ");
            int i = 1;
            for (String projectRegisterPage : projectRegisterPageList) {
                String temp = "";
                if (1 == i) {
                    temp = " JSON_EXTRACT(config,'$.projectRegisterPage') = '" + projectRegisterPage + "'";
                } else {
                    temp = " or  JSON_EXTRACT(config,'$.projectRegisterPage') = '" + projectRegisterPage + "'";
                }
                condition.append(temp);
                i++;
            }
            condition.append(" ))");
        }
        //检测类型
        if (StringUtil.isNotEmpty(sampleTypeId)) {
            condition.append("  and a.sampleTypeId = :sampleTypeId");
            values.put("sampleTypeId", this.sampleTypeId);
        }
        //关键字 项目名称/项目编号/点位名称 模糊查询
        if (StringUtil.isNotEmpty(keyWord)) {
            condition.append(" and (p.projectCode like :key or p.projectName like :key or d.watchSpot like :key)");
            values.put("key", "%" + this.keyWord + "%");
        }
        // 时间区间
        if (StringUtil.isNotEmpty(samplingStartDate) && StringUtil.isNotEmpty(samplingEndDate)) {
            condition.append("  and a.planSamplingTime >= :samplingStartDate and a.planSamplingTime <= :samplingEndDate");
            values.put("samplingStartDate", DateUtil.stringToDate(this.samplingStartDate, DateUtil.FULL));
            values.put("samplingEndDate", DateUtil.stringToDate(this.samplingEndDate, DateUtil.FULL));
        }
        //采样负责人
        if (StringUtil.isNotEmpty(chargePersonId)) {
            condition.append(" and a.chargePersonId = :chargePersonId");
            values.put("chargePersonId", this.chargePersonId);
        }
        //计划安排状态
        if (StringUtil.isNotNull(isArrange)) {
            condition.append(" and a.isArrange = :isArrange");
            values.put("isArrange", this.isArrange);
        }
        if (StringUtil.isNotEmpty(module)) {
            List<String> statuses = new ArrayList<>();
            switch (module) {
                case "采样方案编制":
                    statuses = Arrays.asList(EnumLIM.EnumSamplingArrangeStatus.编辑中.name(), EnumLIM.EnumSamplingArrangeStatus.审核中.name(), EnumLIM.EnumSamplingArrangeStatus.审核退回.name(), EnumLIM.EnumSamplingArrangeStatus.审核通过.name());
                    if (EnumPRO.EnumStatus.已处理.name().equals(this.status)) {
                        statuses = Arrays.asList(EnumLIM.EnumSamplingArrangeStatus.审核中.name(), EnumLIM.EnumSamplingArrangeStatus.审核通过.name());
                    } else if (EnumPRO.EnumStatus.待处理.name().equals(this.status)) {
                        statuses = Arrays.asList(EnumLIM.EnumSamplingArrangeStatus.编辑中.name(), EnumLIM.EnumSamplingArrangeStatus.审核退回.name());
                    }
                    break;
                case "采样方案确认":
                    statuses = Arrays.asList(EnumLIM.EnumSamplingArrangeStatus.审核中.name(), EnumLIM.EnumSamplingArrangeStatus.审核退回.name(), EnumLIM.EnumSamplingArrangeStatus.审核通过.name());
                    if (EnumPRO.EnumStatus.已处理.name().equals(this.status)) {
                        statuses = Arrays.asList(EnumLIM.EnumSamplingArrangeStatus.审核退回.name(), EnumLIM.EnumSamplingArrangeStatus.审核通过.name());
                    } else if (EnumPRO.EnumStatus.待处理.name().equals(this.status)) {
                        statuses = Arrays.asList(EnumLIM.EnumSamplingArrangeStatus.审核中.name());
                    }
                    break;
                default:
            }
            if (StringUtil.isNotEmpty(statuses)) {
                condition.append(" and a.status in :statuses");
                values.put("statuses", statuses);
            }
        }
        return condition.toString();
    }
}