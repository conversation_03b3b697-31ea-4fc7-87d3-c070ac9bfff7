package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoEntSupplierService;

import java.util.List;

/**
 * 商品管理仓储
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-05-14
 * @since V100R001
 */
public interface EntSupplierServiceRepository extends IBaseJpaPhysicalDeleteRepository<DtoEntSupplierService, String> {

    /**
     * 根据企业id查询商品
     *
     * @param entId
     * @return
     */
    List<DtoEntSupplierService> findByEntId(String entId);
}