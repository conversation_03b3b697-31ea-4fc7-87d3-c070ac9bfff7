package com.sinoyd.lims.pro.util;

import com.sinoyd.boot.common.exception.BaseException;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  xml字符串与实体转换工具类
 *
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2022/11/15
 */
public class XmlToObjectUtil {

    public static List<Map<String, Object>> multilayerXmlToMap(String xml) {
        Document doc = null;
        try {
            doc = DocumentHelper.parseText(xml);
        } catch (DocumentException e) {
            throw new BaseException(e.getMessage());
        }
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> data = new ArrayList<>();
        if (null == doc) {
            return data;
        }
        // 获取根元素
        Element rootElement = doc.getRootElement();
        recursionXmlToMap(rootElement.element("Items"), map ,data);
        return data;
    }

    private static void recursionXmlToMap(Element element, Map<String, Object> outmap, List<Map<String, Object>> data) {
        // 得到根元素下的子元素列表
        List<Element> list = element.elements();
        int size = list.size();
        if (size == 0) {
            // 如果没有子元素,则将其存储进map中
            outmap.put(element.getName(), element.getTextTrim());
        } else {
            // innermap用于存储子元素的属性名和属性值
            Map<String, Object> innermap = new HashMap<>();
            // 遍历子元素
            list.forEach(childElement -> recursionXmlToMap(childElement, innermap , data));
            if(!innermap.isEmpty()){
                data.add(innermap);
            }
        }
    }

}
