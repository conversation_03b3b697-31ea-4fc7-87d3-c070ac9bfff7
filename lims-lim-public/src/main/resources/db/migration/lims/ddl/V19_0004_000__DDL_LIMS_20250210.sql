ALTER TABLE tb_pro_qualitycontrol ADD COLUMN rangeType int(10) NOT NULL DEFAULT 10 COMMENT '范围类型,关联枚举EnumConsumableRangeType';
ALTER TABLE tb_pro_qualitycontrol ADD COLUMN rangeLow varchar(50) COMMENT '范围低点';
ALTER TABLE tb_pro_qualitycontrol ADD COLUMN rangeHigh varchar(50) COMMENT '范围高点';
ALTER TABLE tb_pro_qualitymanage ADD COLUMN rangeType int(10) NOT NULL DEFAULT 10 COMMENT '范围类型,关联枚举EnumConsumableRangeType';
ALTER TABLE tb_pro_qualitymanage ADD COLUMN rangeLow varchar(50) COMMENT '范围低点';
ALTER TABLE tb_pro_qualitymanage ADD COLUMN rangeHigh varchar(50) COMMENT '范围高点';