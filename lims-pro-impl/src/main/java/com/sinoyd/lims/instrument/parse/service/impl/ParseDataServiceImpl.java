package com.sinoyd.lims.instrument.parse.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.instrument.parse.dto.DtoParseData;
import com.sinoyd.lims.instrument.parse.dto.DtoParseLog;
import com.sinoyd.lims.instrument.parse.repository.ParseDataRepository;
import com.sinoyd.lims.instrument.parse.repository.ParseLogRepository;
import com.sinoyd.lims.instrument.parse.service.ParseDataService;
import com.sinoyd.lims.pro.dto.DtoWorkSheetFolder;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.WorkSheetFolderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * 仪器解析业务接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2021/7/27
 */
@Service
@Slf4j
public class ParseDataServiceImpl extends BaseJpaServiceImpl<DtoParseData, String, ParseDataRepository> implements ParseDataService {

    private WorkSheetFolderRepository workSheetFolderRepository;
    private ParseLogRepository parseLogRepository;

    @Override
    public List<DtoParseData> findParseData(List<String> sampleCodeList) {
        return repository.findBySampleCodeIn(sampleCodeList);
    }

    @Override
    public List<DtoParseData> findParseData(String logId, String gatherCode, String analyzeItemName, String paramName) {
        List<DtoParseData> dataList = new ArrayList<>();
        DtoParseLog parseLog = parseLogRepository.findOne(logId);
        if (StringUtil.isNotNull(parseLog) && !parseLog.getParseStatus().equals("2")) {
            //解析不成功，则直接返回空
            return dataList;
        }
        dataList = repository.findByParselogId(logId);
        if (StringUtil.isNotEmpty(gatherCode)) {
            dataList = dataList.stream().filter(p -> p.getSampleCode().toLowerCase().contains(gatherCode.toLowerCase())).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(analyzeItemName)) {
            dataList = dataList.stream().filter(p -> p.getAnalyzeItemName().toLowerCase().contains(analyzeItemName.toLowerCase())).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(paramName)) {
            dataList = dataList.stream().filter(p -> p.getParamName().toLowerCase().contains(paramName.toLowerCase())).collect(Collectors.toList());
        }
        return dataList;
    }

    @Override
    public Boolean checkDuplicateSync(String worksheetFolderId) {
        DtoWorkSheetFolder workSheetFolder = workSheetFolderRepository.findOne(worksheetFolderId);
        if(EnumPRO.EnumWorkSheetStatus.新建.name().equals(workSheetFolder.getStatus())){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Autowired
    @Lazy
    public void setWorkSheetFolderRepository(WorkSheetFolderRepository workSheetFolderRepository) {
        this.workSheetFolderRepository = workSheetFolderRepository;
    }

    @Autowired
    public void setParseLogRepository(ParseLogRepository parseLogRepository) {
        this.parseLogRepository = parseLogRepository;
    }
}