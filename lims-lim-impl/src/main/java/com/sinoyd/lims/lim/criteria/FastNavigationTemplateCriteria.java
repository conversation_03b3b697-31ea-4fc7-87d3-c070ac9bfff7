package com.sinoyd.lims.lim.criteria;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;



/**
 * FastNavigationTemplate查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FastNavigationTemplateCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and userId=:userId");
        values.put("userId", PrincipalContextUser.getPrincipal().getUserId());
        return condition.toString();
    }
}