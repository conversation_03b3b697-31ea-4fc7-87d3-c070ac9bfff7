package com.sinoyd.base.service.impl;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/7/1
 */
@Data
public class ReviseDataVO {

    /**
     * 行号
     */
    private int rowNo;

    /**
     * 计算值
     */
    private String calculateValue;

    /**
     * 原始值
     */
    @Excel(name = "原始值")
    private String value;

    /**
     * 有效位
     */
    @Excel(name = "有效位")
    private int significantDigits;

    /**
     * 小数位
     */
    @Excel(name = "小数位")
    private int decimalDigits;

    /**
     * 期盼值
     */
    @Excel(name = "期盼值")
    private String expectedValue;

    @Override
    public String toString() {
        return "行号: " + this.rowNo + "; 原始值: " + this.value + "; 有效位: " + significantDigits + "; 小数位: " + decimalDigits
                + "; 期盼值: " + this.expectedValue + "; 计算值: " + this.calculateValue;
    }
}