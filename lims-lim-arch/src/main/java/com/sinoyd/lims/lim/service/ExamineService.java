package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoExamine;


import java.util.List;


/**
 *  考核管理service
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/09/14
 */
public interface ExamineService extends IBaseJpaService<DtoExamine, String> {

    /**
     * 提交后更新考核实体的状态
     * @param id     考核实体标识
     * @param status 状态值
     * @param opinion 意见
     * @return  更新后考核实体
     */
    DtoExamine updateExamineStatus(String id, Integer status,String opinion);

    /**
     * 考核复制，级联复制下属大小项
     * @param id 考核实体标识
     * @return 复制后端考核实体
     */
    DtoExamine copyExamine(String id);


    /**
     * 附件路径
     * @param id 考核实体标识
     * @return 返回相应的路径信息
     */
    DtoExamine findAttachPath(String id);


    /**
     * 校验考核项目进度是否全为100
     * @param id 考核实体标识
     * @return 检测信息
     */
    Boolean auditCheck(String id);

    /**
     * 批量删除考核
     * @param examineIdList   考核标识列表
     * @return                删除数量
     */
    int batchDelExamineById(List<String> examineIdList);
}
