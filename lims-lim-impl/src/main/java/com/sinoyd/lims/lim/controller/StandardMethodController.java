package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.StandardMethodCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoStandardMethod;
import com.sinoyd.lims.lim.service.StandardMethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * StandardMethod控制器
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Api(tags = "标准方法管理")
@RestController
@RequestMapping("/api/lim/standardMethod")
@Validated
public class StandardMethodController extends BaseJpaController<DtoStandardMethod, String, StandardMethodService> {

    /**
     * 分页动态条件查询StandardMethod
     *
     * @param criteria 条件参数
     * @return RestResponse<List < StandardMethod>>
     */
    @ApiOperation(value = "分页动态条件查询StandardMethod", notes = "分页动态条件查询StandardMethod")
    @GetMapping
    public RestResponse<List<DtoStandardMethod>> findByPage(StandardMethodCriteria criteria) {
        PageBean<DtoStandardMethod> pageBean = super.getPageBean();
        RestResponse<List<DtoStandardMethod>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, criteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询StandardMethod
     *
     * @param id 主键id
     * @return RestResponse<StandardMethod>
     */
    @ApiOperation(value = "按主键查询StandardMethod", notes = "按主键查询StandardMethod")
    @GetMapping(path = "/{id}")
    public RestResponse<DtoStandardMethod> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoStandardMethod> restResponse = new RestResponse<>();
        DtoStandardMethod standardMethod = service.findOne(id);
        restResponse.setData(standardMethod);
        restResponse.setRestStatus(StringUtil.isNull(standardMethod) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增StandardMethod实体
     *
     * @param standardMethod 实体
     * @return RestResponse<StandardMethod>
     */
    @ApiOperation(value = "新增StandardMethod", notes = "新增StandardMethod")
    @PostMapping
    public RestResponse<DtoStandardMethod> create(@Validated @RequestBody DtoStandardMethod standardMethod) {
        RestResponse<DtoStandardMethod> restResponse = new RestResponse<>();
        restResponse.setData(service.save(standardMethod));
        return restResponse;
    }

    /**
     * 修改StandardMethod实体
     *
     * @param standardMethod 实体
     * @return RestResponse<StandardMethod>
     */
    @ApiOperation(value = "修改StandardMethod", notes = "修改StandardMethod")
    @PutMapping
    public RestResponse<DtoStandardMethod> update(@Validated @RequestBody DtoStandardMethod standardMethod) {
        RestResponse<DtoStandardMethod> restResponse = new RestResponse<>();
        restResponse.setData(service.update(standardMethod));
        return restResponse;
    }

    /**
     * 根据id批量删除StandardMethod
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "根据id批量删除StandardMethod", notes = "根据id批量删除StandardMethod")
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 自动匹配
     *
     * @return RestResponse<Void>
     */
    @ApiOperation(value = "自动匹配", notes = "自动匹配")
    @PostMapping("/autoMatch")
    public RestResponse<Void> autoMatch() {
        RestResponse<Void> restResp = new RestResponse<>();
        service.autoMatch();
        return restResp;
    }

} 