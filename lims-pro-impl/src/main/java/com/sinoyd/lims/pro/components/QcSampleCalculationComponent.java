package com.sinoyd.lims.pro.components;

import com.sinoyd.base.service.CalculateService;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 质控样计算相关组件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/8
 */
@Component
public class QcSampleCalculationComponent {

    private TestService testService;

    private CalculateService calculationService;

    /**
     * 计算标准差
     *
     * @param testId            测试项目id
     * @param significantDigits 有效位
     * @param decimalDigits     小数位
     * @param values            数据列表
     * @return 标准差
     */
    public String standardDiviation(String testId, int significantDigits, int decimalDigits, List<String> values) {
        DtoTest dtoTest = testService.findOne(testId);
        List<String> bdValues = new ArrayList<>();
        if (EnumLIM.EnumCalculateWay.先修约后计算.getValue().equals(dtoTest.getCalculateWay())) {
            for (String val : values) {
                bdValues.add(calculationService.revise(significantDigits, decimalDigits, val));
            }
        }
        //需要判断是否多平行样，如果values个数大于2，则表示平行样超过一个，则计算标准差，否则计算相对偏差
        String result;
        if (values.size() > 2) {
            result = standardDiviation(bdValues);
        } else {
            result = relativeDiviation(bdValues);
        }
        //对结果进行修约
        return calculationService.revise(significantDigits, decimalDigits, result) + "%";
    }

    /**
     * 计算相对偏差
     *
     * @param strValues 数据列表
     * @return 相对偏差
     */
    private String relativeDiviation(List<String> strValues) {
        List<BigDecimal> values = strValues.parallelStream().map(BigDecimal::new).collect(Collectors.toList());
        BigDecimal bdResult = values.get(0).subtract(values.get(1)).abs().divide(values.get(0).add(values.get(1)), 20, RoundingMode.DOWN)
                .multiply(new BigDecimal(100));
        return bdResult.toPlainString();
    }

    /**
     * 计算标准差
     *
     * @param strValues 数据列表
     * @return 标准差
     */
    private String standardDiviation(List<String> strValues) {
        List<BigDecimal> values = strValues.parallelStream().map(BigDecimal::new).collect(Collectors.toList());
        int size = values.size();
        BigDecimal sum = values.parallelStream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //求平均值
        double avg = sum.doubleValue() / size;
        double var = 0;
        //求方差
        for (BigDecimal value : values) {
            var += (value.doubleValue() - avg) * (value.doubleValue() - avg);
        }
        double result = Math.sqrt(var / size) * 100;
        return String.valueOf(result);
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    @Lazy
    public void setCalculationService(CalculateService calculationService) {
        this.calculationService = calculationService;
    }
}