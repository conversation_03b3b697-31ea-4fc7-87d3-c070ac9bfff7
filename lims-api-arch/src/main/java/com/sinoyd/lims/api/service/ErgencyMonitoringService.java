package com.sinoyd.lims.api.service;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.api.dto.DtoPersonPhone;
import com.sinoyd.lims.api.dto.DtoReceiveSampleRecordPhone;
import com.sinoyd.lims.api.dto.customer.DtoMobilePointPic;
import com.sinoyd.lims.api.dto.customer.DtoMobileReceive;
import com.sinoyd.lims.lim.dto.rcc.DtoVersionInfo;
import com.sinoyd.lims.pro.dto.DtoProject;
import com.sinoyd.lims.pro.dto.DtoSampleFolder;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface ErgencyMonitoringService extends IBaseJpaService<DtoProject, String> {

    /**
     * 创建应急项目
     *
     * @param receiveSampleRecordPhone 任务信息
     * @return 创建的应急项目
     */
    DtoReceiveSampleRecordPhone saveOutsideProject(DtoReceiveSampleRecordPhone receiveSampleRecordPhone);

    /**
     * 修改应急项目
     *
     * @param receiveSampleRecordPhone 任务信息
     * @return 修改应急项目
     */
    DtoReceiveSampleRecordPhone updateOutsideProject(DtoReceiveSampleRecordPhone receiveSampleRecordPhone);

    /**
     * 移动端版本
     *
     * @param type 类型
     * @return 移动端版本
     */
    DtoVersionInfo getVersionInfo(String type);

    /**
     * 定位服务判定
     *
     * @return 返回结果
     */
    Map<String, String> isOrientation();

    /**
     * 获取当前人信息
     *
     * @param personId 人员id
     * @return 人员信息
     */
    DtoPersonPhone getPersonById(String personId);

    /**
     * 创建点位
     *
     * @param dtoSampleFolder 点位信息
     */
    String saveSampleList(DtoSampleFolder dtoSampleFolder);

    /**
     * 纠正样品状态
     *
     * @param folderId 点位信息
     */
    String checkSample(String folderId, String projectId);

    /**
     * 保存人员信息
     *
     * @param personId   人员id
     * @param faseBase64 脸部信息
     */
    void savePersonFace(String personId, String faseBase64);

    /**
     * 比对人员信息
     *
     * @param personId 人员id
     * @return 是否正确
     * @throws Exception 报错信息
     */
    String personFaceCompare(String personId) throws Exception;

    /**
     * 送样类复制项目
     *
     * @param receiveSampleRecordPhone 复制项目信息
     * @return 复制的项目
     */
    DtoReceiveSampleRecordPhone copyOutsideProject(DtoReceiveSampleRecordPhone receiveSampleRecordPhone);

    /**
     * 修改点位名称
     *
     * @param folderId   点位id
     * @param folderName 修改的点位名称
     */
    void updateSampleFolderName(String folderId, String folderName);

    /**
     * 复制点位
     *
     * @param ids   点位id
     * @param times 复制次数
     */
    List<DtoSampleFolderTemp> copyFolders(List<String> ids, Integer times);

    /**
     * 修改样品编号
     *
     * @param sampleId   样品id
     * @param sampleCode 样品编号
     */
    void updateSampleCode(String sampleId, String sampleCode);

    /**
     * 移动端测点示意图
     *
     * @param pageBean 查询条件
     * @param criteria 查询条件
     */
    List<DtoMobilePointPic> getMobilePointPic(PageBean<DtoMobilePointPic> pageBean, BaseCriteria criteria);

    /**
     * 根据示意图id批量删除
     *
     * @param ids 点位示意图ids
     * @return 数量
     */
    Integer deletePointPic(List<String> ids);

    /**
     * 送样单列表
     *
     * @param pageBean 查询条件
     * @param criteria 查询条件
     * @return
     */
    List<DtoMobileReceive> getReceiveList(PageBean<DtoMobileReceive> pageBean, BaseCriteria criteria);

    /**
     * 示意图更换
     *
     * @param id        点位示意图id
     * @param receiveId 更换的送样单
     */
    String replacePointPic(String id, String receiveId);

    /**
     * 移动端电子签名
     *
     * @param request 请求体
     * @return 附件
     */
    DtoDocument sign(HttpServletRequest request);

    /**
     * 移动端分页查询电子签名
     *
     * @param pageBean 分页条件
     * @param criteria 查询条件
     */
    void findSignPage(PageBean<DtoDocument> pageBean, BaseCriteria criteria);

    /**
     * 移动端采样单签名
     *
     * @param docId 附件id
     */
    void signReceive(String docId);


    /**
     * 根据送样单id 查询采样单
     *
     * @param folderId 送样单Id
     * @return
     */
    List<DtoDocument> getSamplingDocuments(String folderId);

    /**
     * 采样单清除签名
     *
     * @param docId 附件id
     */
    void removeSignature(String docId);
}
