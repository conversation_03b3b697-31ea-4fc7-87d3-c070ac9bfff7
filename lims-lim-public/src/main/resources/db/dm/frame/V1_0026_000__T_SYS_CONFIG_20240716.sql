delete
from t_sys_config
where configKey = 'sys_pro_workSheet_multiGenerate';

INSERT INTO t_sys_config(id, configName, configKey, configValue, enabled, sortNum, note,
                         createDate, createUserGuid, createUserName, updateDate, updateUserGuid,
                         updateUserName, orgGuid, deptGuid, deleted)
VALUES ('487cdb8f5c504637a3550b865ae7f961', '原始记录单是否按照项目拆分生成多分报表', 'sys_pro_workSheet_multiGenerate', 'true', 1, 0,
        '实验室分析检测单生成原始记录单报表时，是否按照检测单样品所在项目对报表进行拆分，生成多分报表。配置为true时：每个项目单独生成一份报表（不自动下载，仅在原始记录标签页展示多份文件，文件名以项目编号作为前缀），配置为false时：仅生成一份报表，不按项目拆分',
        '2024-07-15 09:36:18', '59141356591b48e18e139aa54d9dd351', '超级管理员', '2024-07-15 14:28:16',
        '59141356591b48e18e139aa54d9dd351', '超级管理员', '5f7bcf90feb545968424b0a872863876', NULL, 0);