package com.sinoyd.lims.pro.service;


import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoAnalyseData;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleGroup;
import com.sinoyd.lims.pro.dto.DtoSampleReserve;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataUpdate;

import java.util.List;
import java.util.Map;

/**
 * 样品处理操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/7/20
 * @since V100R001
 */
public interface SampleReserveGroupService extends IBaseJpaService<DtoSampleReserve, String> {

    /**
     * 分页查询数据
     *
     * @param pb       分页数据
     * @param criteria 查询条件
     */
    void findSamplesGroupByPage(PageBean<DtoSampleGroup> pb, BaseCriteria criteria);


    /**
     * 根据样品id查询样品详细信息
     *
     * @param sampleGroupId 样品分组主键id
     * @return 查询结果
     */
    DtoSample findSampleDetail( String sampleGroupId);


    /**
     * 根据样品id列表查询样品详细信息
     *
     * @param  sampleGroupIds 样品分组主键id
     * @return 查询结果
     */
    Map<String, Object> findSampleDetails(List<String> sampleGroupIds);


    /**
     * 根据样品Id获取测试项目
     *
     * @param sampleGroupIds 样品分组传参vo
     * @return 测试项目集合
     */
    List<DtoAnalyzeItem> findAnalyzeItems(List<String>  sampleGroupIds);

    /**
     * 判断样品是否已经领取
     *
     * @param sampleReserve 保存的数据
     * @return 已存在的数据
     */
    List<DtoSampleReserve> judgeSaveData(DtoSampleReserve sampleReserve);

    /**
     * 保存领取信息
     *
     * @param sampleReserve 保存的数据
     * @return 保存的数据
     */
    List<DtoSampleReserve> saveReserve(DtoSampleReserve sampleReserve);


    /**
     * 取消处置
     *
     * @param sampleGroupIds 需要取消处置的Id
     * @return 取消的样品个数
     */
    Integer cancelDispose(List<String> sampleGroupIds);

    /**
     * 更新样品领取数据
     *
     * @param analyseDataList      分析数据集合
     * @param dtoAnalyseDataUpdate 分析数据更新实体
     */
    void updateReserve(List<DtoAnalyseData> analyseDataList, DtoAnalyseDataUpdate dtoAnalyseDataUpdate);
}
