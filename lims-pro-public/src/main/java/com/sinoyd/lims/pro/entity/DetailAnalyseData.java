package com.sinoyd.lims.pro.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import java.math.BigDecimal;


/**
 * DetailAnalyseData实体
 * <AUTHOR>
 * @version V1.0.0 2020/2/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="DetailAnalyseData")
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class DetailAnalyseData implements BaseEntity,Serializable {

    private static final long serialVersionUID = 1L;

    public DetailAnalyseData() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 详细数据id
     */
    @Column(length = 50)
    @ApiModelProperty("详细数据id")
    private String detailDataId;

    /**
     * 测试id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("测试id")
    private String testId;

    /**
     * 测试id
     */
    @Column(length = 50, nullable = false)
    @ApiModelProperty("测试id")
    private String analyseItemId;

    /**
     * 检出限
     */
    @Column(length = 50)
    @ApiModelProperty("检出限")
    private String examLimitValue;

    /**
     * 单位Id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("单位Id")
    private String dimensionId;

    /**
     * 单位（字符串）
     */
    @Column(length = 50)
    @ApiModelProperty("单位（字符串）")
    private String dimension;

    /**
     * 出证结果
     */
    @Column(length = 100)
    @ApiModelProperty("出证结果")
    private String testValue;

    /**
     * 检测结果（未修约）
     */
    @Column(length = 100)
    @ApiModelProperty("检测结果（未修约）")
    private String testOrignValue;

    /**
     * 参与运算的值（检测结果的数值）（已修约）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("参与运算的值（检测结果的数值）（已修约）")
    private BigDecimal testValueD = new BigDecimal(0);

    /**
     * 检测结果（已修约）
     */
    @Column(length = 100)
    @ApiModelProperty("检测结果（已修约）")
    private String testValueDstr;

    /**
     * 组织机构id
     */
    @Column(length = 50)
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 数据状态（字符串,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）
     */
    @Column(length = 50)
    @ApiModelProperty("数据状态（字符串,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）")
    private String status;

    /**
     * 数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）
     */
    @Column(nullable = false)
    @ColumnDefault("1")
    @ApiModelProperty("数据状态（int,枚举EnumAnalyseDataStatus：1.未测 2.在测 4.已测 8.拒绝 16.已确认 32.复核通过 64.作废）")
    private Integer dataStatus = 1;

    /**
     * 有效性（数据确认，是否出具在报告上）
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("有效性（数据确认，是否出具在报告上）")
    private Boolean isDataEnabled = false;

    /**
     * 假删字段
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;

}