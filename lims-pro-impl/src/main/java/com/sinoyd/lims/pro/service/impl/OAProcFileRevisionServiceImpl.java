package com.sinoyd.lims.pro.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.base.service.DocumentService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoFileControlApplyDetail;
import com.sinoyd.lims.lim.dto.lims.DtoOAFileRevision;
import com.sinoyd.lims.lim.enums.EnumLIM.EnumFileControlStatus;
import com.sinoyd.lims.lim.service.FileControlApplyDetailService;
import com.sinoyd.lims.lim.service.OAFileRevisionService;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskCreate;
import com.sinoyd.lims.pro.dto.DtoOATaskDetail;
import com.sinoyd.lims.pro.dto.DtoOATaskRelation;
import com.sinoyd.lims.pro.entity.OATask;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumOATaskType;
import com.sinoyd.lims.pro.repository.OATaskRepository;
import com.sinoyd.lims.pro.service.OAProcFileRevisionService;
import com.sinoyd.lims.pro.service.OATaskRelationService;
import com.sinoyd.lims.pro.service.OATaskService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件修订业务操作接口实现
 * 
 * <AUTHOR>
 * @version V1.0.0 2019-04-08
 * @since V100R001
 */
@Service
@Slf4j
public class OAProcFileRevisionServiceImpl implements OAProcFileRevisionService {
    /**
     * 审批任务服务
     */
    @Autowired
    @Lazy
    private OATaskService oaTaskService;

    /**
     * 审批任务关联服务
     */
    @Autowired
    @Lazy
    private OATaskRelationService oaTaskRelationService;

    /**
     * 文件修订服务
     */
    @Autowired
    @Lazy
    private OAFileRevisionService oaFileRevisionService;
    /**
     * 文件服务
     */
    @Autowired
    private DocumentService documentService;

    /**
     * 审批任务接口
     */
    @Autowired
    private OATaskRepository oaTaskRepository;

    @Autowired
    private DocumentRepository documentRepository;
    /**
     * 文件控制管理明细服务
     */
    @Autowired
    @Lazy
    private FileControlApplyDetailService fileControlApplyDetailService;
    /**
     * 编码服务
     */
    @Autowired
    private CodeService codeService;
    /**
     * 文件路径
     */
    @Autowired
    private FilePathConfig filePathConfig;

    @Override
    public String startProcess(DtoOATaskCreate<DtoOAFileRevision> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.startProcess(EnumOATaskType.文件修订, taskDto, vars);

        DtoOAFileRevision data = taskDto.getData();

        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(data.getId());

        // 添加修订记录
        oaFileRevisionService.save(data);

        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelation);

        // 更新文件状态
        fileControlApplyDetailService.updateFileStatus(data.getFileId(), EnumFileControlStatus.修订中);

        return oaTask.getId();
    }

    @Override
    public DtoOATaskDetail<DtoOAFileRevision, DtoFileControlApplyDetail> findOATaskDetail(String taskId) {
        DtoOATask task = oaTaskService.findByTaskId(taskId);

        DtoOATaskDetail<DtoOAFileRevision, DtoFileControlApplyDetail> taskDetail = new DtoOATaskDetail<>();
        taskDetail.setTask(task);
        taskDetail.setAttach(oaTaskService.getOATaskAttachDTO(task.getProcInstId()));

        // 查找关系
        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(taskId);

        DtoOAFileRevision detail = oaFileRevisionService.findOne(relation.getObjectId());
        // 设置详细信息
        taskDetail.setDetail(detail);

        // 设fileControlApplyDetailService.findOne(detail.getFileId()置扩展信息
        taskDetail.setExtend(fileControlApplyDetailService.findOne(detail.getFileId()));
        
        return taskDetail;
    }

    @Override
    @Transactional
    public void normalCloseTaskNotify(DtoOATask oaTask) {
        // 查找关系
        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(oaTask.getId());
        DtoOAFileRevision detail = oaFileRevisionService.findOne(relation.getObjectId());
        String fileId = detail.getFileId();

        if (StringUtil.isNotEmpty(fileId) && !UUIDHelper.GUID_EMPTY.equals(fileId)) {
            // 更新文件状态
            fileControlApplyDetailService.updateFileStatus(fileId, detail.getControlCode(), detail.getVersion(), EnumFileControlStatus.已受控);

            // 需更新主表的修订信息
        }
        // 将原先的文件删除
        documentService.deleteByFolderId(fileId);
        // 新的文件受控信息
        DtoFileControlApplyDetail newFileControl = fileControlApplyDetailService.getFileControlPath(fileId);
        // 将文件受控申请下的附件拷贝到质控文件明细下
        documentService.syncFileControlApplyDetail(fileId,detail.getId(),newFileControl.getPath());
    }

    /**
     * 文件上传
     * @param procInstId 工作流id
     * @param files 文件集合
     * @param ids 受控文件明细id
     * @return
     */
    @Transactional
    @Override
    public void uploadFilesControl(String procInstId, List<String> ids, List<MultipartFile> files) {
        if (!StringUtils.isNotNullAndEmpty(files)) {
            log.info("Add attachment file is empty");
        }
        //创建文件目录
        String filePath = filePathConfig.getFilePath();
        // 日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        // 文件对象集合
        List<DtoDocument> dtoDocuments = new ArrayList<>();
        // 获取当前用户
        CurrentPrincipalUser user = PrincipalContextUser.getPrincipal();
        // 获取工作流实例相关任务
        List<DtoOATask> oaTask = oaTaskRepository.findByProcInstId(procInstId);
        DtoOATaskRelation relation = oaTaskRelationService.findByTaskId(oaTask.get(0).getId());
        // 获取文件受控申请明细
        DtoOAFileRevision detail = oaFileRevisionService.findOne(relation.getObjectId());
        // 查询受控申请文件明细下的附件
        List<DtoDocument> documents = documentRepository.findByIdIn(ids);
        // 原本的文件
        for (DtoDocument document : documents) {
            File file = new File(filePathConfig.getFilePath() + document.getPath());
            String filename = document.getFilename();
            if (!file.exists()) {
                throw new BaseException("文件" + filename + "不存在,请确认");
            }
            // 新路径
            String newPath = "";
            DtoCode dtoCode = codeService.findByCode(detail.getFileTypeId());
            while (StringUtil.isNotNull(dtoCode)) {
                newPath = dtoCode.getDictName() + "/" + newPath;
                dtoCode = codeService.findById(dtoCode.getParentId());
            }
            // 物理文件名称(时间戳+文件名称)
            String physicalName = dateFormat.format(new Date()) + "_" + document.getFilename();
            FileInputStream fileInputStream = null;
            FileOutputStream fileOutputStream = null;
            try {
                // 读取文件
                fileInputStream = new FileInputStream(file);
                // 写入文件
                fileOutputStream = new FileOutputStream(new File(filePath + "/" + newPath + physicalName));
                // 一次读取1M
                byte[] temp = new byte[1024 * 1024];
                int i = fileInputStream.read(temp);
                while (i != -1) {
                    fileOutputStream.write(temp, 0, temp.length);
                    fileOutputStream.flush();
                    i = fileInputStream.read(temp);
                }
                DtoDocument dtoDocument = new DtoDocument();
                dtoDocument.setFolderId(detail.getId());
                dtoDocument.setFilename(document.getFilename());
                dtoDocument.setPhysicalName(physicalName);
                dtoDocument.setPath("/" + newPath + physicalName);
                dtoDocument.setIsTranscript(false);
                dtoDocument.setDocTypeId(UUIDHelper.GUID_EMPTY);
                dtoDocument.setDocSize(document.getDocSize());
                dtoDocument.setDocSuffix(document.getDocSuffix());
                dtoDocument.setDownloadTimes(0);
                dtoDocument.setOrderNum(0);
                dtoDocument.setRemark("");
                dtoDocument.setUploadPerson(user.getUserName());
                dtoDocument.setUploadPersonId(user.getUserId());
                dtoDocument.setIsStick(false);
                dtoDocument.setOrgId(user.getOrgId());
                dtoDocuments.add(dtoDocument);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                // 关闭I/O流
                if (fileInputStream != null) {
                    try {
                        fileInputStream.close();
                    } catch (IOException e) {
                        log.error(e.getMessage(), e);
                    }
                }
                if (fileOutputStream != null) {
                    try {
                        fileOutputStream.close();
                    } catch (IOException e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
        //  下面是新的附件进行上传
        String subPath = "";
        if (StringUtil.isNotNull(detail)){
            DtoCode dtoCode = codeService.findByCode(detail.getFileTypeId());
            while (StringUtil.isNotNull(dtoCode)) {
                subPath = dtoCode.getDictName() + "/" + subPath;
                dtoCode = codeService.findById(dtoCode.getParentId());
            }
        }
        // 文件路径
        String checkPath = filePath + "/" + subPath;
        File fileStream = new File(checkPath);
        if (!fileStream.exists()) {
            fileStream.mkdirs();
        }
        for (MultipartFile multipartFile : files) {
            // 获取文件名
            String fileName = multipartFile.getOriginalFilename();
            // 创建物理名称
            String physicalName = dateFormat.format(new Date()) + "_" + fileName;
            // 文件路径,当中有"/" 所以不需要拼接了
            String pathName = checkPath + physicalName;
            // 获取文件类型
            String fileType = fileName.substring(fileName.lastIndexOf("."));
            DtoDocument dtoDocument = new DtoDocument();
            dtoDocument.setFolderId(detail.getId());
            dtoDocument.setFilename(fileName);
            dtoDocument.setPhysicalName(physicalName);
            dtoDocument.setPath("/" + subPath + physicalName);
            dtoDocument.setIsTranscript(false);
            dtoDocument.setDocTypeId(UUIDHelper.GUID_EMPTY);
            dtoDocument.setDocSize(Integer.parseInt(String.valueOf(multipartFile.getSize())));
            dtoDocument.setDocSuffix(fileType);
            dtoDocument.setDownloadTimes(0);
            dtoDocument.setOrderNum(0);
            dtoDocument.setRemark("");
            dtoDocument.setUploadPerson(user.getUserName());
            dtoDocument.setUploadPersonId(user.getUserId());
            dtoDocument.setIsStick(false);
            dtoDocument.setOrgId(user.getOrgId());
            File file = new File(pathName);
            try {
                multipartFile.transferTo(file);
                dtoDocuments.add(dtoDocument);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        if (StringUtil.isNotEmpty(dtoDocuments)) {
            documentService.save(dtoDocuments);
        }
    }


    @Override
    @Transactional
    public OATask saveAsDraft(DtoOATaskCreate<DtoOAFileRevision> taskDto) {
        Map<String, Object> vars = new HashMap<>();
        DtoOATask oaTask = oaTaskService.saveAsDraft(EnumOATaskType.文件修订, taskDto, vars);
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    @Override
    @Transactional
    public OATask draftSave(DtoOATaskCreate<DtoOAFileRevision> taskDto) {
        //保存审批信息
        DtoOATask oaTask = oaTaskService.DraftSave(taskDto);
        //移除原关联关系
        List<DtoOATaskRelation> relationList = oaTaskRelationService.findListByTaskId(oaTask.getId());
        oaTaskRelationService.delete(relationList);
        //保存新关联关系
        saveTaskRelation(taskDto,oaTask);
        return oaTask;
    }

    private void saveTaskRelation(DtoOATaskCreate<DtoOAFileRevision> taskDto,DtoOATask oaTask){
        DtoOAFileRevision data = taskDto.getData();
        DtoOATaskRelation taskRelation = new DtoOATaskRelation();
        taskRelation.setTaskId(oaTask.getId());
        taskRelation.setObjectId(data.getId());
        // 添加修订记录
        if (fileControlApplyDetailService.isExistControlCode(data.getControlCode(), data.getFileId())) {
            throw new BaseException("已存在相同的受控编号！");
        }
        oaFileRevisionService.save(data);
        // 添加审批任务关联信息
        oaTaskRelationService.save(taskRelation);
    }

    @Override
    @Transactional
    public String draftSubmit(DtoOATaskCreate<DtoOAFileRevision> taskDto) {
        // 更新文件状态
        DtoOAFileRevision data = taskDto.getData();
        fileControlApplyDetailService.updateFileStatus(data.getFileId(), EnumFileControlStatus.修订中);
        return oaTaskService.draftSubmit(taskDto, new HashMap<>());
    }
}