package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.factory.quality.QualityBlank;
import com.sinoyd.base.factory.quality.QualityInstrumentBlank;
import com.sinoyd.base.factory.quality.QualityLocalBlank;
import com.sinoyd.base.factory.quality.QualityTransportBlank;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.CalculationService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoParamsFormulaDetail;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroupRepository;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import com.sinoyd.lims.lim.service.ParamsFormulaService;
import com.sinoyd.lims.pro.criteria.ParamsDataCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.dto.customer.DtoSampleInfo;
import com.sinoyd.lims.pro.dto.customer.DtoSampleItemParams;
import com.sinoyd.lims.pro.dto.customer.DtoSampleParamsTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumParamsDataType;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.*;
import com.sinoyd.lims.pro.util.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 参数数据操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/21
 * @since V100R001
 */
@Service
@Slf4j
public class ParamsDataServiceImpl extends BaseJpaServiceImpl<DtoParamsData, String, ParamsDataRepository> implements ParamsDataService {

    private static final Pattern PATTERN = Pattern.compile("(\\[[^\\]]*\\])");

    @Autowired
    @Lazy
    private ProService proService;

    @Autowired
    @Lazy
    private CalculationService calculationService;

    @Autowired
    @Lazy
    private NewLogService newLogService;

    @Autowired
    @Lazy
    private SampleService sampleService;

    @Autowired
    private SampleRepository sampleRepository;

    @Autowired
    private AnalyseDataRepository analyseDataRepository;

    @Autowired
    private SampleFolderRepository sampleFolderRepository;

    @Autowired
    @Lazy
    private ParamsConfigService paramsConfigService;

    @Autowired
    @Lazy
    private ParamsFormulaService paramsFormulaService;

    @Autowired
    @Lazy
    private AnalyzeItemService analyzeItemService;

    @Autowired
    private QualityControlRepository qualityControlRepository;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Autowired
    private ParamsDataFutureService paramsDataFutureService;

    @Autowired
    @Lazy
    private SampleTypeGroupRepository sampleTypeGroupRepository;

    @Override
    public void findByPage(PageBean<DtoParamsData> pb, BaseCriteria paramsDataCriteria) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.DtoParamsData(");
        stringBuilder.append("a.id,a.objectId,a.objectType,a.paramsConfigId,a.paramsName,a.paramsValue,a.dimension,");
        stringBuilder.append("a.dimensionId,a.orderNum,b.analyzeItemId,b.alias,a.groupId)");
        pb.setEntityName("DtoParamsData a,DtoParamsConfig b");
        pb.setSelect(stringBuilder.toString());
        comRepository.findByPage(pb, paramsDataCriteria);
    }

    @Override
    @Transactional
    @Async
    public void saveAsync(Collection<DtoParamsData> entitys) {
        this.save(entitys);
    }

    @Override
    public List<DtoParamsData> findObjectIdsAndObjectType(List<String> objectIds, Integer objectType) {
        PageBean<DtoParamsData> pageBean = new PageBean<>();
        pageBean.setRowsPerPage(Integer.MAX_VALUE);
        ParamsDataCriteria paramsDataCriteria = new ParamsDataCriteria();
        paramsDataCriteria.setObjectIds(objectIds);
        paramsDataCriteria.setObjectType(objectType);
        findByPage(pageBean, paramsDataCriteria);
        return pageBean.getData();
    }

    /**
     * 保存样品分析项目参数
     *
     * @param temp 传输结构
     */
    @Transactional
    @Override
    public void saveAnalyzeItemParamsData(DtoSampleItemParams temp) {
        Map<String, DtoParamsConfig> cfgMap = temp.getParamsConfig().stream().filter(p -> StringUtils.isNotNullAndEmpty(p.getParamsValue())).
                collect(Collectors.toMap(DtoParamsConfig::getId, cfg -> cfg));
        if (cfgMap.keySet().size() == 0 || temp.getSampleIds().size() == 0 || temp.getAnalyseItemIds().size() == 0) {
            throw new BaseException("请核对是否未填写参数数据或未勾选指标！");
        }
        List<DtoParamsConfig> paramsConfigs = paramsConfigService.findByParentIdInAndAnalyzeItemIdInAndIsShowTrue(cfgMap.keySet(), temp.getAnalyseItemIds());
        List<String> paramsConfigIds = paramsConfigs.stream().map(DtoParamsConfig::getId).collect(Collectors.toList());

        if (paramsConfigIds.size() > 0) {
            //查询所有的样品及分析数据
            List<DtoSample> sampleList = sampleRepository.findAll(temp.getSampleIds());
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(temp.getSampleIds());
            Map<String, List<DtoAnalyseData>> sampleDataMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
            //查询数据库中已经存在的对应参数数据
            List<DtoParamsData> paramsDataList = repository.findByObjectTypeAndObjectIdInAndParamsConfigIdIn(EnumParamsDataType.样品.getValue(), temp.getSampleIds(), paramsConfigIds);
            //样品及配置下的主键映射
            Map<String, String> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(),
                    p.getGroupId()), DtoParamsData::getId, (p1, p2) -> p1));
            List<DtoLog> logList = new ArrayList<>();
            for (DtoSample sample : sampleList) {
                if (sampleDataMap.containsKey(sample.getId())) {//该样品下有指标才继续
                    Map<String, String> itemMap = sampleDataMap.get(sample.getId()).stream().
                            collect(Collectors.groupingBy(DtoAnalyseData::getAnalyseItemId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getRedAnalyzeItemName())));
                    for (DtoParamsConfig paramsConfig : paramsConfigs) {
                        if (itemMap.containsKey(paramsConfig.getAnalyzeItemId())) {//该样品下包含该指标参数才进行更新
                            String key = String.format("%s;%s;s%", sample.getId(), paramsConfig.getId(), UUIDHelper.GUID_EMPTY);
                            if (allParamsDataMap.containsKey(key)) {
                                DtoParamsData paramsData = new DtoParamsData();
                                paramsData.setId(allParamsDataMap.get(key));
                                paramsData.setObjectId(sample.getId());
                                paramsData.setObjectType(EnumParamsDataType.样品.getValue());
                                paramsData.setParamsConfigId(paramsConfig.getId());
                                paramsData.setParamsName(paramsConfig.getAlias());
                                paramsData.setParamsValue(cfgMap.get(paramsConfig.getParentId()).getParamsValue());
                                paramsData.setDimension(paramsConfig.getDimension());
                                paramsData.setDimensionId(paramsConfig.getDimensionId());
                                paramsData.setOrderNum(paramsConfig.getOrderNum());
                                paramsData.setGroupId(UUIDHelper.GUID_EMPTY);
                                comRepository.merge(paramsData);
                                String str = String.format("录入样品%s的参数%s为%s。", String.valueOf(sample.getCode()), paramsConfig.getAlias(), paramsData.getParamsValue());
                                DtoLog log = new DtoLog();
                                log.setId(UUIDHelper.NewID());
                                log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                                log.setOperateTime(new Date());
                                log.setOperateInfo(EnumPRO.EnumLogOperateType.修改样品.toString());
                                log.setLogType(EnumPRO.EnumLogType.样品信息.getValue());
                                log.setObjectId(sample.getId());
                                log.setObjectType(EnumPRO.EnumLogObjectType.样品.getValue());
                                log.setComment(str);
                                log.setOpinion("");
                                log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                                log.setRemark("");
                                logList.add(log);
                            }
                        }
                    }
                }
            }
            if (logList.size() > 0) {
                newLogService.createLog(logList, EnumPRO.EnumLogType.样品信息.getValue());
            }
        }
    }

    /**
     * 保存样品参数
     *
     * @param temp 传输结构
     */
    @Transactional
    @Override
    public void saveSampleParamsData(DtoSampleParamsTemp temp) {
        List<String> sampleIds = temp.getIsPublic() ? new ArrayList<>() : temp.getSampleIds();
        List<DtoSample> sampleList = new ArrayList<>();
        DtoSampleFolder folder = StringUtils.isNotNullAndEmpty(temp.getSampleFolderId()) && !UUIDHelper.GUID_EMPTY.equals(temp.getSampleFolderId()) ?
                sampleFolderRepository.findOne(temp.getSampleFolderId()) : new DtoSampleFolder();
        if (temp.getIsPublic()) {
            sampleList = sampleService.findSampleByReceiveIdAndSampleTypeIdAndSampleFolderId(temp.getReceiveId(), temp.getSampleTypeId(),
                    temp.getParamsConfig().getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue()) ? UUIDHelper.GUID_EMPTY : temp.getSampleFolderId(), false);
        } else if (sampleIds.size() > 0) {
            sampleList = sampleRepository.findAll(sampleIds);
        }
        if (sampleList.size() > 0) {
            if (temp.getIsPublic() && EnumLIM.EnumParamsType.点位参数.getValue().equals(temp.getParamsConfig().getParamsType())) {
                List<String> qcIds = sampleList.stream().map(DtoSample::getQcId).filter(q -> !UUIDHelper.GUID_EMPTY.equals(q) && StringUtils.isNotNullAndEmpty(q)).distinct().collect(Collectors.toList());
                List<DtoQualityControl> qualityControls = StringUtil.isNotEmpty(qcIds) ? qualityControlRepository.findAll(qcIds) : new ArrayList<>();
                List<String> filterQcIds = qualityControls.stream().filter(q -> EnumLIM.EnumQCGrade.外部质控.getValue().equals(q.getQcGrade()) &&
                        (new QualityLocalBlank().qcTypeValue().equals(q.getQcType()) || new QualityBlank().qcTypeValue().equals(q.getQcType())
                                || new QualityInstrumentBlank().qcTypeValue().equals(q.getQcType()) || new QualityTransportBlank().qcTypeValue().equals(q.getQcType())))
                        .map(DtoQualityControl::getId).collect(Collectors.toList());
                sampleList = sampleList.stream().filter(s -> !filterQcIds.contains(s.getQcId())).collect(Collectors.toList());
            }
            sampleIds = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            String configId = temp.getParamsConfig().getId();
            if (configId.contains(";")) {
                configId = configId.split(";")[0];
            }
            List<DtoParamsData> paramsDataList = repository.findByObjectTypeAndObjectIdInAndParamsConfigId(EnumParamsDataType.样品.getValue(), sampleIds, configId);
            //样品及配置下的主键映射
            Map<String, DtoParamsData> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(), p.getGroupId()), pd -> pd, (p1, p2) -> p1));
            DtoSampleTypeGroup sampleTypeGroup = sampleTypeGroupRepository.findOne(temp.getGroupId());
            List<DtoLog> logs = new ArrayList<>();
            for (DtoSample sample : sampleList) {
                DtoParamsData paramsData = new DtoParamsData();
                paramsData.setObjectId(sample.getId());
                paramsData.setObjectType(EnumParamsDataType.样品.getValue());
                paramsData.setParamsConfigId(configId);
                String alias = temp.getParamsConfig().getAlias();
                if (!UUIDHelper.GUID_EMPTY.equals(temp.getGroupId()) && StringUtil.isNotNull(sampleTypeGroup) && !alias.contains(sampleTypeGroup.getGroupName())){
                    alias = String.format("%s-%s", sampleTypeGroup.getGroupName(), alias);
                }
                paramsData.setParamsName(alias);
                paramsData.setParamsValue(temp.getParamsValue());
                paramsData.setDimension(temp.getParamsConfig().getDimension());
                paramsData.setDimensionId(temp.getParamsConfig().getDimensionId());
                paramsData.setOrderNum(temp.getParamsConfig().getOrderNum());
                paramsData.setGroupId(temp.getGroupId());
                String key = String.format("%s;%s;%s", sample.getId(), configId, temp.getGroupId());
                if (allParamsDataMap.containsKey(key)) {
                    logs.add(this.getParamsLog(temp, sample, folder.getWatchSpot(), allParamsDataMap.get(key).getParamsValue(), temp.getParamsValue()));
                    paramsData.setId(allParamsDataMap.get(key).getId());
                    comRepository.merge(paramsData);
                } else {
                    logs.add(this.getParamsLog(temp, sample, folder.getWatchSpot(), null, temp.getParamsValue()));
                    repository.save(paramsData);
                }
            }

            if (logs.size() > 0) {
                if (temp.getIsPublic()) {
                    DtoLog log = logs.get(0);
                    log.setObjectId(temp.getReceiveId());
                    newLogService.createLog(log);
                } else {
                    newLogService.createLog(logs, EnumPRO.EnumLogType.样品信息.getValue());
                }
            }
        }
    }

    /**
     * 计算样品参数
     *
     * @param info 样品信息
     */
    @Transactional
    @Override
    public List<Map<String, Object>> calculateParamsData(DtoSampleInfo info) {
        //筛选需要公式计算或修约的配置
        List<DtoParamsConfig> paramsConfigs = new ArrayList<>();
        for (DtoParamsConfig pc : info.getParamsConfig()) {
            if ((pc.getParamsType().equals(EnumLIM.EnumParamsType.样品参数.getValue()) || pc.getParamsType().equals(EnumLIM.EnumParamsType.分析项目参数.getValue()))
                    && (pc.getIsFormula() || !(pc.getMostSignificance().equals(-1) && pc.getMostDecimal().equals(-1)))) {
                paramsConfigs.add(pc);
            }
        }
        paramsConfigs.sort(Comparator.comparing(DtoParamsConfig::getIsFormula));
        if (paramsConfigs.size() == 0) {
            return info.getSample();
        }
        //主配置id（通过公式id不为空来过滤掉子的配置）与公式id的键值对
//        Map<String, String> formulaMap = new HashMap<>();
//        if (paramsConfigs.stream().anyMatch(p -> p.getIsFormula() && !UUIDHelper.GUID_EMPTY.equals(p.getFormulaId()))) {
//            formulaMap = paramsConfigs.stream().filter(p -> p.getIsFormula() && !UUIDHelper.GUID_EMPTY.equals(p.getFormulaId()))
//                    .collect(Collectors.toMap(DtoParamsConfig::getId, DtoParamsConfig::getFormulaId));
//        }
        List<String> formulaIds = paramsConfigs.stream().map(DtoParamsConfig::getFormulaId).distinct().collect(Collectors.toList());
        //根据公式id集合获取公式及部分公式
        List<DtoParamsFormulaDetail> formulaDetailList = formulaIds.size() > 0 ? paramsFormulaService.findParamsFormulaDetails(formulaIds) : new ArrayList<>();
        //按照公式id分组获取键值对
        Map<String, DtoParamsFormulaDetail> formulaDetailMap = formulaDetailList.stream().collect(Collectors.toMap(DtoParamsFormulaDetail::getId, formula -> formula));

        //获取样品参数
        List<String> sampleIds = info.getSample().stream().map(p -> String.valueOf(p.getOrDefault("id", ""))).collect(Collectors.toList());
        List<DtoParamsData> paramsDataList = repository.findByObjectIdInAndObjectType(sampleIds, EnumParamsDataType.样品.getValue());
        Map<String, DtoParamsData> allParamsDataMap = paramsDataList.stream().collect(Collectors.toMap(p -> String.format("%s;%s;%s", p.getObjectId(), p.getParamsConfigId(),
                p.getGroupId()), pd -> pd, (p1, p2) -> p1));

        List<DtoLog> logList = new ArrayList<>();
        for (DtoParamsConfig paramsConfig : paramsConfigs) {
            for (Map<String, Object> sample : info.getSample()) {
                String oldValue = String.valueOf(sample.get(paramsConfig.getId()));
                if (info.getIsFolder()) {
                    oldValue = String.valueOf(sample.get("paramsValue"));
                }
                String newValue = "";
                if (paramsConfig.getIsFormula()) {
                    String formulaId = paramsConfig.getFormulaId();
                    Optional<DtoParamsConfig> optional = paramsConfigs.stream().filter(p -> paramsConfig.getParentId().equals(p.getId())).findFirst();
                    if (optional.isPresent()) {
                        formulaId = optional.get().getFormulaId();
                    }
                    if (formulaDetailMap.containsKey(formulaId)) {//需要进行计算
                        if (info.getIsFolder()) {
                            if (sample.get("paramsId").equals(paramsConfig.getId())) {
                                newValue = this.calculateParamsData(sample, paramsConfig, info.getParamsConfig(),
                                        formulaDetailMap.get(formulaId), info.getIsFolder(), info.getSample());
                            }
                        } else {
                            if (sample.containsKey(paramsConfig.getId()) && StringUtil.isNotNull(sample.get(paramsConfig.getId()))) {
                                newValue = this.calculateParamsData(sample, paramsConfig, info.getParamsConfig(),
                                        formulaDetailMap.get(formulaId), info.getIsFolder(), info.getSample());
                            }
                        }
                    } else if (StringUtil.isNotNull(sample.get(paramsConfig.getId())) && MathUtil.isNumeral(sample.get(paramsConfig.getId()))) {
                        newValue = proService.getDecimal(paramsConfig.getMostSignificance(), paramsConfig.getMostDecimal(), oldValue);
                    }
                } else {
                    if (StringUtil.isNotNull(sample.get(paramsConfig.getId())) && MathUtil.isNumeral(sample.get(paramsConfig.getId()))) {
                        newValue = proService.getDecimal(paramsConfig.getMostSignificance(), paramsConfig.getMostDecimal(), oldValue);
                    }
                }
                String groupId = paramsConfig.getId().contains(";") ? paramsConfig.getId().split(";")[1] : UUIDHelper.GUID_EMPTY;
                if (info.getIsFolder()) {
                    groupId = (String) sample.get("groupId");
                }
                if (StringUtils.isNotNullAndEmpty(newValue) && !oldValue.equals(newValue)) {
                    if (info.getIsFolder()) {
                        sample.put("paramsValue", newValue);
                    } else {
                        sample.put(paramsConfig.getId(), newValue);
                    }
                    DtoParamsData paramsData = new DtoParamsData();
                    paramsData.setObjectId(String.valueOf(sample.get("id")));
                    paramsData.setObjectType(EnumParamsDataType.样品.getValue());
                    paramsData.setParamsConfigId(paramsConfig.getId().split(";")[0]);
                    paramsData.setParamsName(paramsConfig.getAlias());
                    paramsData.setParamsValue(newValue);
                    paramsData.setDimension(paramsConfig.getDimension());
                    paramsData.setDimensionId(paramsConfig.getDimensionId());
                    paramsData.setOrderNum(paramsConfig.getOrderNum());
                    paramsData.setGroupId(groupId);
                    String key = String.format("%s;%s;%s", String.valueOf(sample.get("id")), paramsConfig.getId().split(";")[0], groupId);
                    if (allParamsDataMap.containsKey(key)) {
                        paramsData.setId(allParamsDataMap.get(key).getId());
                        comRepository.merge(paramsData);
                    } else {
                        repository.save(paramsData);
                    }
                }
                if (StringUtils.isNotNullAndEmpty(newValue)) {
                    String str = String.format("计算样品%s的参数%s为%s。", String.valueOf(sample.get("code")), paramsConfig.getAlias(), newValue);
                    DtoLog log = new DtoLog();
                    log.setId(UUIDHelper.NewID());
                    log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
                    log.setOperateTime(new Date());
                    log.setOperateInfo(EnumPRO.EnumLogOperateType.修改样品.toString());
                    log.setLogType(EnumPRO.EnumLogType.样品信息.getValue());
                    log.setObjectId(String.valueOf(sample.get("id")));
                    log.setObjectType(EnumPRO.EnumLogObjectType.样品.getValue());
                    log.setComment(str);
                    log.setOpinion("");
                    log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
                    log.setRemark("");
                    logList.add(log);
                }
            }
        }
        if (logList.size() > 0) {
            newLogService.createLog(logList, EnumPRO.EnumLogType.样品信息.getValue());
        }

        return info.getSample();
    }

    /**
     * 根据样品获取样品参数
     *
     * @param sampleIds 样品id集合
     */
    @Override
    public List<DtoParamsData> findBySampleIds(List<String> sampleIds) {
        List<DtoParamsData> dataList = repository.findByObjectIdInAndObjectType(sampleIds, EnumParamsDataType.样品.getValue());
        if (StringUtil.isNotEmpty(dataList)) {
            Set<String> configIds = dataList.stream().map(DtoParamsData::getParamsConfigId).collect(Collectors.toSet());
            List<DtoParamsConfig> configList = paramsConfigService.findAll(configIds);
            for (DtoParamsData paramsData : dataList) {
                Optional<DtoParamsConfig> configOptional = configList.stream().filter(p -> p.getId().equals(paramsData.getParamsConfigId()))
                        .findFirst();
                configOptional.ifPresent(p -> paramsData.setAnalyzeItemId(p.getAnalyzeItemId()));
                if (!StringUtil.isNotEmpty(paramsData.getAnalyzeItemId())) {
                    paramsData.setAnalyzeItemId(UUIDHelper.GUID_EMPTY);
                }
            }
        }
        return dataList;
    }

    /**
     * 异常数据处理
     */
    @Override
    @Transactional
    public void exceptionalData() {
        // 获取异常数据
        StringBuilder sql = new StringBuilder();
        sql.append("select objectId,objectType,paramsConfigId,groupId,isDeleted,count(*) from TB_PRO_ParamsData")
                .append(" where isDeleted = 0")
                .append(" group by objectId,objectType,paramsConfigId,groupId,isDeleted")
                .append(" HAVING count(1) > 1");
        List<Map<String, Object>> mapList = namedParameterJdbcTemplate.queryForList(sql.toString(), new HashMap<String, Object>());
        if (StringUtil.isNotEmpty(mapList)) {
            List<String> objectIds = mapList.stream().map(p -> p.get("objectId").toString()).distinct().collect(Collectors.toList());
            // 根据异常数据信息多线程获取所有数据
            List<DtoParamsData> paramsDataList = new ArrayList<>();
            List<Future<List<DtoParamsData>>> paramsDataResultList = new ArrayList<>();
            List<String> list = null;
            final int batchSize = 200;
            for (String sampleId : objectIds) {
                if (list == null) {
                    list = new ArrayList<>();
                }
                if (list.size() < batchSize) {
                    list.add(sampleId);
                } else if (list.size() == batchSize) {
                    //多线程处理排序
                    paramsDataResultList.add(paramsDataFutureService.getListByObjectIdIn(list));
                    list = new ArrayList<>();
                    list.add(sampleId);
                }
            }
            //如果存在最后一批样，需要单独去排序处理
            if (StringUtil.isNotEmpty(list)) {
                paramsDataResultList.add(paramsDataFutureService.getListByObjectIdIn(list));
            }
            //处理多线程处理的结果
            try {
                for (Future<List<DtoParamsData>> paramsDataResult : paramsDataResultList) {
                    while (true) {
                        if (paramsDataResult.isDone() && !paramsDataResult.isCancelled()) {
                            paramsDataList.addAll(paramsDataResult.get());
                            break;
                        } else {
                            //防止CPU高速轮询被耗空
                            Thread.sleep(1);
                        }
                    }
                }
            } catch (Exception e) {
                log.info(e.getMessage());
                throw new BaseException("......多线程获取参数数据出错......");
            }
            // 多线程获取删除数据
            List<DtoParamsData> deleteList = new ArrayList<>();
            List<Future<List<DtoParamsData>>> delParamsDataResultList = new ArrayList<>();
            List<Map<String, Object>> delList = null;
            final int delBatchSize = 1000;
            for (Map<String, Object> map : mapList) {
                if (delList == null) {
                    delList = new ArrayList<>();
                }
                if (delList.size() < delBatchSize) {
                    delList.add(map);
                } else if (delList.size() == delBatchSize) {
                    //多线程处理排序
                    delParamsDataResultList.add(paramsDataFutureService.getDelParamsDataList(delList, paramsDataList));
                    delList = new ArrayList<>();
                    delList.add(map);
                }
            }
            //如果存在最后一批样，需要单独去排序处理
            if (StringUtil.isNotEmpty(delList)) {
                delParamsDataResultList.add(paramsDataFutureService.getDelParamsDataList(delList, paramsDataList));
            }
            //处理多线程处理的结果
            try {
                for (Future<List<DtoParamsData>> paramsDataResult : delParamsDataResultList) {
                    while (true) {
                        if (paramsDataResult.isDone() && !paramsDataResult.isCancelled()) {
                            deleteList.addAll(paramsDataResult.get());
                            break;
                        } else {
                            //防止CPU高速轮询被耗空
                            Thread.sleep(1);
                        }
                    }
                }
            } catch (Exception e) {
                log.info(e.getMessage());
                throw new BaseException("......多线程处理参数数据出错......");
            }

            // 删除异常数据
            if (StringUtil.isNotEmpty(deleteList)) {
                List<Future<Void>> deleteResultList = new ArrayList<>();
                List<DtoParamsData> deletes = null;
                final int delSize = 200;
                for (DtoParamsData del : deleteList) {
                    if (deletes == null) {
                        deletes = new ArrayList<>();
                    }
                    if (deletes.size() < delSize) {
                        deletes.add(del);
                    } else if (deletes.size() == delSize) {
                        //多线程处理排序
                        deleteResultList.add(paramsDataFutureService.delete(deletes));
                        deletes = new ArrayList<>();
                        deletes.add(del);
                    }
                }
                //如果存在最后一批样，需要单独去排序处理
                if (StringUtil.isNotEmpty(deletes)) {
                    deleteResultList.add(paramsDataFutureService.delete(deletes));
                }
                //处理多线程处理的结果
                try {
                    for (Future<Void> paramsDataResult : deleteResultList) {
                        while (true) {
                            if (paramsDataResult.isDone() && !paramsDataResult.isCancelled()) {
                                break;
                            } else {
                                //防止CPU高速轮询被耗空
                                Thread.sleep(1);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.info(e.getMessage());
                    throw new BaseException("......多线程处理删除数据出错......");
                }
            }
        }
    }


    @Override
    @Transactional
    public void syncGroupParams(String groupId, List<String> paramsConfigIds,List<Map<String,Object>> sampleList) {
        if(!paramsConfigIds.isEmpty()&&!sampleList.isEmpty()){
            List<String> sampleIds = sampleList.stream().map(s->(String)s.getOrDefault("id","")).filter(StringUtil::isNotEmpty).collect(Collectors.toList());
            List<DtoParamsData> paramsDataList = repository.findByObjectTypeAndObjectIdInAndParamsConfigIdIn(EnumParamsDataType.样品.getValue(), sampleIds,paramsConfigIds)
                    .stream().filter(p->StringUtil.isNotEmpty(p.getGroupId())&&!UUIDHelper.GUID_EMPTY.equals(p.getGroupId())).collect(Collectors.toList());
            List<DtoParamsData> waitSaveList = new ArrayList<>();
            for (String paramsConfigId:paramsConfigIds) {
                for (Map<String,Object> dataMap:sampleList) {
                    if(dataMap.containsKey(paramsConfigId+";"+groupId)){
                        String value = (String)dataMap.get(paramsConfigId+";"+groupId);
                        String sampleId = (String)dataMap.get("id");
                        List<DtoParamsData> sampleParamsDataList = paramsDataList.stream().filter(d->sampleId.equals(d.getObjectId())&&paramsConfigId.equals(d.getParamsConfigId()))
                                .collect(Collectors.toList());
                        sampleParamsDataList.forEach(d->d.setParamsValue(value));
                        waitSaveList.addAll(sampleParamsDataList);
                    }
                }
            }
            repository.save(waitSaveList);
        }
    }

    /**
     * 计算单个样品参数
     *
     * @param sample          单个样品信息
     * @param paramsConfig    待计算的参数配置
     * @param allParamsConfig 所有参数配置
     * @param formulaDetail   参数公式明细
     */
    private String calculateParamsData(Map<String, Object> sample, DtoParamsConfig paramsConfig,
                                       List<DtoParamsConfig> allParamsConfig, DtoParamsFormulaDetail formulaDetail,
                                       Boolean isFolder, List<Map<String, Object>> samples) {
        DtoAnalyzeItem item = StringUtils.isNotNullAndEmpty(paramsConfig.getAnalyzeItemId()) && !paramsConfig.getAnalyzeItemId().equals(UUIDHelper.GUID_EMPTY) ?
                analyzeItemService.findOne(paramsConfig.getAnalyzeItemId()) : null;

        Map<String, String> partResultMap = new HashMap<>();

        //主公式
        String calculateFormula = formulaDetail.getFormula();
        //主公式包含的部分公式才进行纳入，防止脏数据的存在导致无意义的计算
        List<DtoParamsPartFormula> paramsPartFormulaList = formulaDetail.getParamsPartFormula().stream().filter(p -> formulaDetail.getFormula().contains(p.getFormula())).collect(Collectors.toList());
        //按照公式的长度来进行排序，涉及包含与被包含关系的一定是子公式先进行计算修约
        paramsPartFormulaList.sort(Comparator.comparing((DtoParamsPartFormula p) -> p.getFormula().length()));

        Integer index = 0;
        while (true) {
            if (paramsPartFormulaList.size() == 0) {
                break;
            }
            DtoParamsPartFormula partFormula = paramsPartFormulaList.get(0);
            String formula = partFormula.getFormula();
            String result = this.calculateFormula(sample, allParamsConfig, item, partResultMap,
                    partFormula.getFormula(), partFormula.getMostSignificance(), partFormula.getMostDecimal(), isFolder, samples, paramsConfig.getAlias());
            if (StringUtils.isNotNullAndEmpty(result)) {
                paramsPartFormulaList.remove(partFormula);
                partResultMap.put(String.format("P%d", index), result);
                calculateFormula = calculateFormula.replace(formula, String.format("[P%d]", index));
                for (DtoParamsPartFormula paramsPartFormula : paramsPartFormulaList) {
                    paramsPartFormula.setFormula(paramsPartFormula.getFormula().replace(formula, String.format("[P%d]", index)));
                }
            } else {//计算失败，则所有包含该部分公式的全部剔除
                paramsPartFormulaList = paramsPartFormulaList.stream().filter(p -> !p.getFormula().contains(formula)).collect(Collectors.toList());
            }
            index++;
        }
        return this.calculateFormula(sample, allParamsConfig, item, partResultMap,
                calculateFormula, paramsConfig.getMostSignificance(), paramsConfig.getMostDecimal(), isFolder, samples, paramsConfig.getAlias());
    }

    /**
     * 根据公式计算
     *
     * @param sample           单个样品信息
     * @param allParamsConfig  所有参数配置
     * @param item             分析项目
     * @param partResultMap    部分公式结果
     * @param formula          公式
     * @param mostSignificance 有效位
     * @param mostDecimal      小数位
     */
    private String calculateFormula(Map<String, Object> sample, List<DtoParamsConfig> allParamsConfig,
                                    DtoAnalyzeItem item, Map<String, String> partResultMap,
                                    String formula, Integer mostSignificance, Integer mostDecimal,
                                    Boolean isFolder, List<Map<String, Object>> samples, String anaParam) {
        List<String> aliasList = this.getParamsAlias(formula);
        String analyseName = getAnaNameOfParams(anaParam);
        Map<String, Object> params = new HashMap<>();
        for (String alias : aliasList) {
            if (partResultMap.containsKey(alias)) {//为部分修约计算的值则直接插入
                params.put(alias, MathUtil.getBigDecimal(partResultMap.get(alias)));
            } else {//找寻样品参数的值，或公共参数、点位参数的值
                Optional<DtoParamsConfig> pc;
                if (!analyseName.equals(alias)) {
                    pc = allParamsConfig.stream().filter(p -> p.getAlias().equals(analyseName + "-" + alias)).findFirst();
                    if (!pc.isPresent()) {
                        pc = allParamsConfig.stream().filter(p -> p.getAlias().equals(alias)).findFirst();
                    }
                } else {
                    pc = allParamsConfig.stream().filter(p -> p.getAlias().equals(alias)).findFirst();
                }
                if (pc.isPresent()) {
                    if (isFolder) {
                        Optional<DtoParamsConfig> finalPc = pc;
                        Optional<Map<String, Object>> sam = samples.stream().filter(p -> p.get("paramsId").equals(finalPc.get().getId())).findFirst();
                        if (sam.isPresent() && StringUtil.isNotEmpty((String) sam.get().get("paramsValue"))) {
                            params.put(alias, MathUtil.getBigDecimal(sam.get().get("paramsValue")));
                        }
                    } else {
                        if (sample.containsKey(pc.get().getId()) && MathUtil.isNumeral(sample.get(pc.get().getId()))) {
                            params.put(alias, MathUtil.getBigDecimal(sample.get(pc.get().getId())));
                        }
                    }
                    if ((pc.get().getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue()) ||
                            pc.get().getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue()))
                            && MathUtil.isNumeral(pc.get().getParamsValue())) {
                        params.put(alias, MathUtil.getBigDecimal(pc.get().getParamsValue()));
                    }
                }
            }
        }

        if (params.size() == aliasList.size()) {//涉及到的参数与满足条件的参数个数一致才进行计算
            Object result = calculationService.calculationExpression(formula, params);
            if (StringUtil.isNotNull(result) && MathUtil.isNumeral(result)) {
                if (!(mostSignificance.equals(-1) && mostDecimal.equals(-1))) {
                    return proService.getDecimal(mostSignificance, mostDecimal, String.valueOf(result));
                }
                return String.valueOf(result);
            }
        }
        return null;
    }

    /**
     * 根据分析参数名称获取分析项目名称
     *
     * @param paramsName 分析项目参数
     * @return 分析项目名称
     */
    private String getAnaNameOfParams(String paramsName) {
        String resultStr = "";
        if (StringUtil.isEmpty(paramsName)) {
            return resultStr;
        }
        List<String> strList = Stream.of(paramsName.split("-")).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(strList)) {
            Optional<String> firstStr = strList.stream().findFirst();
            if (firstStr.isPresent()) {
                resultStr = firstStr.get();
            }
        }
        return resultStr;
    }


    /**
     * 提取公式中的参数别名
     *
     * @param formula 公式
     * @return 参数别名
     */
    private List<String> getParamsAlias(String formula) {
        List<String> list = new ArrayList<>();
        Matcher matcher = PATTERN.matcher(formula);
        while (matcher.find()) {
            list.add(matcher.group().substring(1, matcher.group().length() - 1));
        }
        return list;
    }

    /**
     * 获取参数保存日志
     *
     * @param temp      传输结构
     * @param sample    样品
     * @param watchSpot 点位名称
     * @param oldValue  修改前
     * @param newValue  修改后
     */
    private DtoLog getParamsLog(DtoSampleParamsTemp temp, DtoSample sample, String watchSpot, String oldValue, String newValue) {
        String comment = "";
        DtoLog log = new DtoLog();
        log.setId(UUIDHelper.NewID());
        log.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
        log.setOperateTime(new Date());
        log.setOpinion("");
        log.setNextOperatorId(UUIDHelper.GUID_EMPTY);
        log.setRemark("");

        if (temp.getIsPublic()) {
            if (temp.getParamsConfig().getParamsType().equals(EnumLIM.EnumParamsType.公共参数.getValue())) {
                log.setObjectId(sample.getReceiveId());
                log.setOperateInfo(EnumPRO.EnumLogOperateType.修改送样单.toString());
                log.setLogType(EnumPRO.EnumLogType.送样单样品信息.getValue());
                log.setObjectType(EnumPRO.EnumLogObjectType.送样单.getValue());
                comment = StringUtils.isNotNullAndEmpty(oldValue) ? String.format("修改送样单的公共参数%s为%s,原参数结果为%s", temp.getParamsConfig().getAlias(), StringUtil.isNotEmpty(newValue) ? newValue : "' '", oldValue) :
                        String.format("保存送样单的公共参数%s为%s", temp.getParamsConfig().getAlias(), newValue);
            } else if (temp.getParamsConfig().getParamsType().equals(EnumLIM.EnumParamsType.点位参数.getValue())) {
                log.setObjectId(sample.getReceiveId());
                log.setOperateInfo(EnumPRO.EnumLogOperateType.修改点位.toString());
                log.setLogType(EnumPRO.EnumLogType.送样单样品信息.getValue());
                log.setObjectType(EnumPRO.EnumLogObjectType.送样单.getValue());
                comment = StringUtils.isNotNullAndEmpty(oldValue) ? String.format("修改点位%s的参数%s为%s,原参数结果为%s", watchSpot, temp.getParamsConfig().getAlias(), StringUtil.isNotEmpty(newValue) ? newValue : "' '", oldValue) :
                        String.format("保存点位%s的参数%s为%s", watchSpot, temp.getParamsConfig().getAlias(), newValue);
            }
        } else {
            log.setObjectId(sample.getId());
            log.setOperateInfo(EnumPRO.EnumLogOperateType.修改样品.toString());
            log.setLogType(EnumPRO.EnumLogType.样品信息.getValue());
            log.setObjectType(EnumPRO.EnumLogObjectType.样品.getValue());
            comment = StringUtils.isNotNullAndEmpty(oldValue) ? String.format("修改样品%s的参数%s为%s,原参数结果为%s", sample.getCode(), temp.getParamsConfig().getAlias(), StringUtil.isNotEmpty(newValue) ? newValue : "' '", oldValue) :
                    String.format("保存样品%s的参数%s为%s", sample.getCode(), temp.getParamsConfig().getAlias(), newValue);
        }

        log.setComment(comment);
        return log;
    }
}