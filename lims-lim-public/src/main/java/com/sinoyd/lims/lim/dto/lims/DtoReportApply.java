package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.ReportApply;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoReportApply实体
 * <AUTHOR>
 * @version V1.0.0 2022/4/21
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_ReportApply")
 @Data
 @DynamicInsert
 public  class DtoReportApply extends ReportApply {
   private static final long serialVersionUID = 1L;

   @Transient
   private Integer orderNum;
 }