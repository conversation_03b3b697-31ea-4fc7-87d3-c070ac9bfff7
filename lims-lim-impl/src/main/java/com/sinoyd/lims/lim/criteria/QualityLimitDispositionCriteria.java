package com.sinoyd.lims.lim.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;



/**
 * QualityLimitDisposition查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QualityLimitDispositionCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    private Integer qcGrade;

    private Integer qcType;

    private Integer method;

    private Boolean isAcquiesce;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtil.isNotEmpty(this.id)) {
            condition.append(" and id = :id");
            values.put("id", this.id);
        }
        if (StringUtil.isNotNull(this.qcGrade)) {
            condition.append(" and qcGrade = :qcGrade");
            values.put("qcGrade", this.qcGrade);
        }
        if (StringUtil.isNotNull(this.qcType)) {
            condition.append(" and qcType = :qcType");
            values.put("qcType", this.qcType);
        }
        if (StringUtil.isNotNull(this.method)) {
            condition.append(" and judgingMethod = :method");
            values.put("method", this.method);
        }
        if (StringUtil.isNotNull(this.isAcquiesce)) {
            condition.append(" and isAcquiesce = :isAcquiesce");
            values.put("isAcquiesce", this.isAcquiesce);
        }
        return condition.toString();
    }
}