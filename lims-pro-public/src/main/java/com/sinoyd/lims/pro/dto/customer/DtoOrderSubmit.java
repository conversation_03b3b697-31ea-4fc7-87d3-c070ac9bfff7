package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.pro.enums.EnumPRO;
import lombok.Data;

@Data
public class DtoOrderSubmit {

    /**
     * 是否审核
     */
    private Boolean isAudit = true;

    /**
     * 审核流程
     */
    private Integer flowType = EnumPRO.EnumOrderFlowType.一审.getValue();

    /**
     * 一审人员
     */
    private String auditPersonId = UUIDHelper.GUID_EMPTY;

    /**
     * 二审人员
     */
    private String reviewPersonId = UUIDHelper.GUID_EMPTY;

    /**
     * 三审人员
     */
    private String affirmPersonId = UUIDHelper.GUID_EMPTY;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 意见
     */
    private String opinion;

    /**
     * 信号值
     */
    private String sign;

    /**
     * 是否通过
     */
    private Boolean isPass = true;
}
