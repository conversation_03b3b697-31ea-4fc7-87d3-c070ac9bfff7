package com.sinoyd.lims.lim.data.sync.service;

import com.sinoyd.base.criteria.EvaluationCriteriaCriteria;
import com.sinoyd.base.dto.rcc.DtoEvaluationCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.criteria.RecordConfigCriteria;
import com.sinoyd.lims.lim.criteria.ReportConfigCriteria;
import com.sinoyd.lims.lim.criteria.TestCriteria;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataCompareResult;
import com.sinoyd.lims.lim.data.sync.dto.DtoDataSyncParam;
import com.sinoyd.lims.lim.dto.rcc.DtoRecordConfig;
import com.sinoyd.lims.lim.dto.lims.DtoReportConfig;
import com.sinoyd.lims.lim.dto.lims.DtoTest;

import java.util.List;
import java.util.Map;

/**
 * 数据同步接口
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/21
 */
public interface DataSyncService {

    /**
     * 数据比较
     *
     * @param dtoDataSyncParam 比较参数
     * @return 比较结果
     */
    Map<String, List<DtoDataCompareResult<?>>> compare(DtoDataSyncParam dtoDataSyncParam);

    /**
     * 分页远程查询共同库测试项目
     *
     * @param page     分页对象
     * @param criteria 条件对象
     */
    void findStandardTestByPage(PageBean<DtoTest> page, TestCriteria criteria);

    /**
     * 分页远程查询共同库评价标准配置
     *
     * @param page     分页对象
     * @param criteria 条件对象
     */
    void findStandardEvaluationCriteriaByPage(PageBean<DtoEvaluationCriteria> page, EvaluationCriteriaCriteria criteria);

    /**
     * 数据同步
     *
     * @param dtoDataSyncParam 同步参数
     */
    void syncData(DtoDataSyncParam dtoDataSyncParam);

    /**
     * 分页远程查询共同库报表模板配置
     * @param page     分页对象
     * @param criteria 条件对象
     */
    void findStandardReportConfigByPage(PageBean<DtoReportConfig> page, ReportConfigCriteria criteria);

    /**
     * 分页远程查询共同库采样单配置
     * @param page     分页对象
     * @param criteria 条件对象
     */
    void findStandardRecordConfigByPage(PageBean<DtoRecordConfig> page, RecordConfigCriteria criteria);

    /**
     * 分页远程查询共同库原始记录单配置
     * @param page     分页对象
     * @param criteria 条件对象
     */
    void findStandardWorksheetRecordConfigByPage(PageBean<DtoRecordConfig> page, RecordConfigCriteria criteria);
}
