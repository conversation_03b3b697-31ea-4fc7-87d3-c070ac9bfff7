package com.sinoyd.lims.lim.dto.customer;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 资源统计传输类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/9/22
 */
@Data
@Accessors(chain = true)
public class DtoResourceStatistics {
    /**
     * 统计类目
     */
    private String itemName;

    /**
     * 统计结果
     */
    private Integer num;

    /**
     * 单位
     */
    private String unit;
}