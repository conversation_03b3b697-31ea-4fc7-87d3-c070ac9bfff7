package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.pro.dto.DtoSubmitRecord;
import com.sinoyd.lims.pro.repository.SubmitRecordRepository;
import com.sinoyd.lims.pro.service.SubmitRecordService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * SubmitRecord操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @Service
public class SubmitRecordServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSubmitRecord,String,SubmitRecordRepository> implements SubmitRecordService {

    @Override
    public void findByPage(PageBean<DtoSubmitRecord> pb, BaseCriteria submitRecordCriteria) {
        pb.setEntityName("DtoSubmitRecord a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, submitRecordCriteria);
    }

    @Transactional
    @Override
    public void createSubmitRecord(String objectId,
                                   Integer objectType,
                                   Integer submitType,
                                   String nextPerson,
                                   String submitRemark,
                                   String from,
                                   String to) {
        DtoSubmitRecord submitRecord = new DtoSubmitRecord();
        submitRecord.setObjectId(objectId);
        submitRecord.setObjectType(objectType);
        submitRecord.setSubmitType(submitType);
        submitRecord.setSubmitPersonId(PrincipalContextUser.getPrincipal().getUserId());
        submitRecord.setSubmitPersonName(PrincipalContextUser.getPrincipal().getUserName());
        submitRecord.setSubmitTime(new Date());
        submitRecord.setNextPerson(nextPerson);
        submitRecord.setSubmitRemark(submitRemark);
        submitRecord.setStateFrom(from);
        submitRecord.setStateTo(to);
        repository.save(submitRecord);
    }

    @Transactional
    @Override
    public void createSubmitRecords(List<DtoSubmitRecord> submitRecords,
                                    String userId,
                                    String userName,
                                    String orgId) {
        for (DtoSubmitRecord submitRecord : submitRecords) {
            submitRecord.setSubmitPersonId(userId);
            submitRecord.setSubmitPersonName(userName);
            submitRecord.setOrgId(orgId);
            submitRecord.setCreator(userId);
            submitRecord.setModifier(userId);
            submitRecord.setSubmitTime(new Date());
        }
        super.insertBatch(submitRecords);
    }
}