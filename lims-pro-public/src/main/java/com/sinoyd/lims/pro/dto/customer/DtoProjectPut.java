package com.sinoyd.lims.pro.dto.customer;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  推送相关实体
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2022/11/15
 */
@Data
public class DtoProjectPut {

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 任务名称
     */
    private String projectName;

    /**
     * 委托方名字
     */
    private String customerName;

    /**
     * 项目状态
     */
    private String status;

    /**
     * 主键id
     */
    private String id;

    /**
     * 合同id
     */
    private String contractId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 任务金额
     */
    private BigDecimal taskPrice;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 是否采样
     */
    private String isSample;

    /**
     * 任务来源
     */
    private String taskSource;

    /**
     * 任务所在地
     */
    private String taskLocation;

    /**
     * 采样联系人
     */
    private String sampleContact;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 任务地址
     */
    private String taskAddress;

    /**
     * 附件说明
     */
    private String fileExplain;

    /**
     * 任务概述
     */
    private String taskContent;

    /**
     * 是否推送（0：否，1：是）
     */
    private Integer isPush;

    /**
     * 是否已经推送（0：否，1：是）
     */
    private Integer hasPush;

    /**
     * 是否处理（0：否，1：是）
     */
    private Integer isHandle;

    /**
     * 方案是否推送
     */
    private Integer schemeHasPush;

    /**
     * 采样计划是否推送
     */
    private Integer planHasPush;

    /**
     * 报告是否推送
     */
    private Integer reportHasPush;

    /**
     * 分析项目id集合
     */
    private List<String> analyzeMethodIds;

    /**
     * 测试项目集合
     */
    private List<String> testIds;

    /**
     * 监管平台项目id
     */
    private String pId;

    /**
     * 用于接收采样计划id和采样时间
     */
    private List<Map<String,String>> samplingPlan;

    /**
     * 文件id
     */
    private String documentId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 登记时间
     */
    private Date inceptTime;

    public DtoProjectPut(String projectTypeId, String projectCode, String projectName, String customerName, Date inceptTime, String status, String id, String contractId,
                         String projectId, String contractName, BigDecimal taskPrice, String taskType,
                         String isSample, String taskSource, String taskLocation, String sampleContact,
                         String contactPhone, String taskAddress, String fileExplain, String taskContent,
                         Integer isPush, Integer hasPush, Integer isHandle, String pId, Integer schemeHasPush, Integer planHasPush, Integer reportHasPush) {
        this.projectTypeId = projectTypeId;
        this.projectCode = projectCode;
        this.projectName = projectName;
        this.customerName = customerName;
        this.inceptTime = inceptTime;
        this.status = status;
        this.id = id;
        this.contractId = contractId;
        this.projectId = projectId;
        this.contractName = contractName;
        this.taskPrice = taskPrice;
        this.taskType = taskType;
        this.isSample = isSample;
        this.taskSource = taskSource;
        this.taskLocation = taskLocation;
        this.sampleContact = sampleContact;
        this.contactPhone = contactPhone;
        this.taskAddress = taskAddress;
        this.fileExplain = fileExplain;
        this.taskContent = taskContent;
        this.isPush = isPush;
        this.hasPush = hasPush;
        this.isHandle = isHandle;
        this.pId = pId;
        this.schemeHasPush = schemeHasPush;
        this.planHasPush = planHasPush;
        this.reportHasPush = reportHasPush;
    }
}
