package com.sinoyd.lims.lim.dto.rcc;

import com.sinoyd.lims.lim.entity.HolidayConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 节假日管理配置实体传输类
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023/1/18
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_HolidayConfig")
@Data
public class DtoHolidayConfig extends HolidayConfig {

}
