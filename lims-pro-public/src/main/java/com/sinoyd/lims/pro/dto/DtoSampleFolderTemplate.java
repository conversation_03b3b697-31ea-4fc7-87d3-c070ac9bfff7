package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.SampleFolderTemplate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * DtoSampleFolderTemplate实体
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_SampleFolderTemplate")
@Data
@DynamicInsert
public class DtoSampleFolderTemplate extends SampleFolderTemplate {

    /**
     * 点位id集合
     */
    @Transient
    private List<String> folderIds;

    /**
     * 次数
     */
    @Transient
    private Integer times;

}
