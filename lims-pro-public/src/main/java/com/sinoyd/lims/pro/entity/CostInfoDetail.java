package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;
import java.util.Date;


/**
 * CostInfoDetail实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="CostInfoDetail")
 @Data
 public  class CostInfoDetail implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

   public  CostInfoDetail() {
      this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
      this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 费用id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("费用id")
	private String costInfoId = "";
    
    /**
    * 测试项目id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("测试项目id")
	private String testId;
    
    /**
    * 分析项目名称
    */
    @Column(length=50)
    @ApiModelProperty("分析项目名称")
    @Length(message = "分析项目名称{validation.message.length}", max = 50)
	private String redAnalyzeItemName;
    
    /**
    * 分析方法名称
    */
    @ApiModelProperty("分析方法名称")
    @Length(message = "分析方法名称{validation.message.length}", max = 255)
	private String redAnalyzeMethodName;
    
    /**
    * 标准编号
    */
    @Column(length=50)
    @ApiModelProperty("标准编号")
    @Length(message = "标准编号{validation.message.length}", max = 100)
	private String redCountryStandard;
    
    /**
    * 检测类型id
    */
    @Column(length=50,nullable=false)
    @ApiModelProperty("检测类型id")
	private String sampleTypeId;
    
    /**
    * 样品个数
    */
    @Column(nullable=false)
    @ApiModelProperty("样品个数")
	private Integer sampleNum=1;
    
    /**
    * 配置上的采样费（当时的配置）
    */
    @ApiModelProperty("配置上的采样费（当时的配置）")
	private BigDecimal samplingConfigCost= BigDecimal.valueOf(0);
    
    /**
    * 采样费
    */
    @Column(nullable=false)
    @ApiModelProperty("采样费")
	private BigDecimal samplingCost= BigDecimal.valueOf(0);
    
    /**
    * 配置上的分析费（当时的配置）
    */
    @ApiModelProperty("配置上的分析费（当时的配置）")
	private BigDecimal analyzeConfigCost= BigDecimal.valueOf(0);
    
    /**
    * 分析费
    */
    @Column(nullable=false)
    @ApiModelProperty("分析费")
	private BigDecimal analyzeCost= BigDecimal.valueOf(0);
    
    /**
    * 折扣率
    */
    @Column(nullable=false)
    @ApiModelProperty("折扣率")
	private BigDecimal testRate= BigDecimal.valueOf(0);
    
    /**
    * 合计
    */
    @Column(nullable=false)
    @ApiModelProperty("合计")
	private BigDecimal totalCost= BigDecimal.valueOf(0);

    /**
     * 组织机构id
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    /**
     * 创建人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 所属实验室
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    /**
     * 修改人
     */
    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
 }