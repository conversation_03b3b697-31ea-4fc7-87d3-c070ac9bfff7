package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.lims.pro.dto.DtoReport;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目进度数据结构
 * <AUTHOR>
 * @version V1.0.0 2020/2/7
 * @since V100R001
 */
@Data
public class DtoProjectInquiry {

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目状态
     */
    private List<Map<String, Object>> projectStatus;

    /**
     * 采样情况
     */
    private List<DtoProjectInquirySamplingInfo> samplingInfo;
    ;

    /**
     * 现场任务
     */
    private List<DtoProjectInquiryRecord> localTask;

    /**
     * 样品分配
     */
    private List<DtoProjectInquiryRecord> sampleAssign;

    /**
     * 检测情况
     */
    private Map<String, List<DtoProjectInquiryAnalyse>> analyzeInfo;

    /**
     * 报告
     */
    private List<DtoReport> report;
}
