package com.sinoyd.base.configuration;

import com.sinoyd.base.dto.vo.DeviationFormulaSrcVO;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * 质控样偏差公式数据源Xml映射类
 *
 * <AUTHOR>
 * @version 5.2.0
 * @since 2023/05/24
 */
@XmlRootElement(name = "rules")
@XmlAccessorType(XmlAccessType.FIELD)
public class QcDeviationFormulaConfig {

    @XmlElement(name = "config")
    private List<DeviationFormulaSrcVO> deviationFormulaSrcVOList;

    @XmlTransient
    public List<DeviationFormulaSrcVO> getDeviationFormulaSrcVOList() {
        return deviationFormulaSrcVOList;
    }

    public void setDeviationFormulaSrcVOList(List<DeviationFormulaSrcVO> deviationFormulaSrcVOList) {
        this.deviationFormulaSrcVOList = deviationFormulaSrcVOList;
    }
}
