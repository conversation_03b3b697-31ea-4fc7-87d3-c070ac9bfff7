package com.sinoyd.lims.od.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.od.criteria.OdSampleCriteria;
import com.sinoyd.lims.od.dto.DtoOdSample;
import com.sinoyd.lims.od.service.OdSampleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * odSample服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Api(tags = "示例: odSample服务")
@RestController
@RequestMapping("api/ods/odSample")
@Validated
public class OdSampleController extends BaseJpaController<DtoOdSample, String, OdSampleService> {

    /**
     * 分页动态条件查询OdSample
     *
     * @param odSampleCriteria 条件参数
     * @return RestResponse<List < OdSample>>
     */
    @ApiOperation(value = "分页动态条件查询", notes = "分页动态条件查询")
    @GetMapping
    public RestResponse<List<DtoOdSample>> findByPage(OdSampleCriteria odSampleCriteria) {
        PageBean<DtoOdSample> pageBean = super.getPageBean();
        RestResponse<List<DtoOdSample>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, odSampleCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据id查询odSample
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询odSample", notes = "根据id查询odSample")
    @GetMapping("/{id}")
    public RestResponse<DtoOdSample> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoOdSample> restResp = new RestResponse<>();
        DtoOdSample entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增odSample
     *
     * @param odSample odSample实体
     * @return 新增的odSample实体
     */
    @ApiOperation(value = "新增odSample", notes = "新增odSample")
    @PostMapping("")
    public RestResponse<DtoOdSample> create(@Validated @RequestBody DtoOdSample odSample) {
        RestResponse<DtoOdSample> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoOdSample data = service.save(odSample);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新odSample
     *
     * @param odSample odSample实体
     * @return 更新后的odSample实体
     */
    @ApiOperation(value = "更新odSample", notes = "更新odSample")
    @PutMapping("")
    public RestResponse<DtoOdSample> update(@Validated @RequestBody DtoOdSample odSample) {
        RestResponse<DtoOdSample> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoOdSample data = service.update(odSample);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数", notes = "根据id批量删除参数")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
