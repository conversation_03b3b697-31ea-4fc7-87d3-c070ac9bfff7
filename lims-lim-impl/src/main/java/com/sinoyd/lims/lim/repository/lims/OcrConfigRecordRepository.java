package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigRecord;

import java.util.List;

/**
 * ocr参数数据历史
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
public interface OcrConfigRecordRepository extends IBaseJpaRepository<DtoOcrConfigRecord, String> {
    /**
     * 根据config查询
     * @param config ocr对象标识
     * @return 识别记录
     */
    List<DtoOcrConfigRecord> findAllByConfigIdOrderByCreateDateDesc(String config);

    /**
     * 根据样品编号查询
     * @param sampleCodes 样品编号集合
     * @return 识别记录
     */
    List<DtoOcrConfigRecord> findAllBySampleCodeInOrderByCreateDateDesc(List<String> sampleCodes);
}