-- --------------------------------------------------
-- 初始化污染源点位拓展字段配置用到的参数数据
-- --------------------------------------------------
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('3e9cb1a5-a5e2-4d37-968d-3ebe707306dc', '', '燃料类型', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:02',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:02');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('a631e94d-e43b-4db2-8c28-d4e782b9603e', '', '烟囱高度', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:34',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:34');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('f14d4f78-aee4-447d-b8f0-283353c41e98', '', '烟囱高度(米)', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:44', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:44');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('0072e220-f1bc-404f-9613-856e60b6d4c2', '', '锅炉投运日期', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:44', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:44');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('17309f29-2dd6-4338-ad62-124be709d5fa', '', '炉窑设备编号', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:21:05', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:21:05');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('c100aff8-2481-42cf-b433-57e654209bc7', '', '炉窑设备型号', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:36', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:36');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('1a31c8f8-c81c-45ed-826c-c7812ddbc0f9', '', '净化设备名称', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:15:16', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:15:16');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('72dcf71d-52e7-480f-aa9e-365d1fda4831', '', '工艺设备名称', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:56', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:56');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('e4148bbc-cb74-45f8-a1dd-9e32ba131f70', '', '净化设备投运日期', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:45', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:45');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('3d404054-997a-42f5-8624-0effa1ef7ad3', '', '进出口', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:13:39',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:13:39');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('ca2ace02-1cdb-4bac-bc95-e38c1c622a8c', '', '锅炉制造单位', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:10', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:10');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('ea0ce619-f29d-4417-a7d3-b7d3f8c3b0e1', '', '排污去向', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:06',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:06');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('c9ef84e0-b466-48b7-a4a4-af6e60484895', '', '净化设备型号', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:23:50', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:23:50');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('4e286bad-4595-43fb-a748-be7b8fbc5d3f', '', '净化设备制造单位', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:03', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:03');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('5146948c-fca9-4d82-b92e-5749217885ad', '', '排气管高度', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:32',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:32');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('594941c7-694f-48b3-aadc-bfb30df30d9d', '', '污染源种类', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:15:51',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:15:51');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('75ceb21a-0797-4805-bb8a-8e0a2d881e79', '', '启用时间', '', '', '', 'null', 'f28615b9-c5a0-440b-930e-1f114a2f2f97',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:16:12',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:16:12');
INSERT INTO TB_LIM_Params(id, paramCode, paramName, remark, variableName, regex, dimension, dimensionId, isDeleted,
                          orgId, creator, createDate, domainId, modifier, modifyDate)
VALUES ('d71d867e-98c8-408e-994c-7c9e18af6ca2', '', '锅炉名称(型号)', '', '', '', 'null',
        'f28615b9-c5a0-440b-930e-1f114a2f2f97', b'0', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:23', '5f7bcf90feb545968424b0a872863876',
        '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:23');


-- --------------------------------------------------
-- 初始化污染源点位拓展字段配置
-- --------------------------------------------------
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('156f72a8-572f-409c-bbb9-c53396aec481', 'LIM_PointType_YQKLW', 'a631e94d-e43b-4db2-8c28-d4e782b9603e', '烟囱高度',
        'chimneyHeight', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:55',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:55');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('e2a39304-a3a9-4bd2-b89d-99536b496c3b', 'LIM_PointType_YQKLW', '1a31c8f8-c81c-45ed-826c-c7812ddbc0f9', '净化设备名称',
        'purificateFacilityName', '', 200, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:08',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:22:08');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('32ae10d4-37a3-4e9a-8256-ab38a0ea68f3', 'LIM_PointType_YQKLW', '17309f29-2dd6-4338-ad62-124be709d5fa', '炉窑设备编号',
        'stoveFacilityCode', '', 400, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:21:18',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:21:18');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('5849a582-d202-4716-8cee-e48ce1adebae', 'LIM_PointType_YQKLW', 'c100aff8-2481-42cf-b433-57e654209bc7', '炉窑设备型号',
        'stoveFacilityType', '', 300, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:59',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:21:27');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('10bffe1b-3671-4eba-9819-40bdd2dff01a', 'LIM_PointType_Boiler', '3e9cb1a5-a5e2-4d37-968d-3ebe707306dc', '燃料类型',
        'fuelType', '', 300, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:10',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:20:10');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('b779031e-8b15-4898-96a9-1149ee001576', 'LIM_PointType_Boiler', 'e4148bbc-cb74-45f8-a1dd-9e32ba131f70',
        '净化设备投运日期', 'purificateFacilityUseDate', '', 400, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 2,
        b'0', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:56',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:56');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('d34af9bd-69c6-4d81-96fc-8b3fe0bdd020', 'LIM_PointType_Boiler', 'c9ef84e0-b466-48b7-a4a4-af6e60484895',
        '净化设备型号', 'purificateFacilityType', '', 500, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:39',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:23:52');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('e8a91b65-3685-4fb4-a701-bc81834d5c0b', 'LIM_PointType_Boiler', '4e286bad-4595-43fb-a748-be7b8fbc5d3f',
        '净化设备制造单位', 'purificateFacilityUnit', '', 600, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1,
        b'0', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:14',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:19:14');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('1afded77-897a-4bce-8d98-d2195f62270f', 'LIM_PointType_Boiler', 'f14d4f78-aee4-447d-b8f0-283353c41e98',
        '烟囱高度(米)', 'chimneyHeight', '', 700, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:56',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:56');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('1be7926e-9c0a-43a0-8cf3-fd3f173570bb', 'LIM_PointType_Boiler', '0072e220-f1bc-404f-9613-856e60b6d4c2',
        '锅炉投运日期', 'boilerUseDate', '', 800, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 2, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:03',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:37');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('fc3515f1-25cd-4a7d-b92c-5d5a75fcaab2', 'LIM_PointType_Boiler', 'd71d867e-98c8-408e-994c-7c9e18af6ca2',
        '锅炉名称(型号)', 'equipmentTypeName', '', 900, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:35',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:32');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('cae83575-1b02-48ff-a504-7f0fab16a9e6', 'LIM_PointType_Boiler', 'ca2ace02-1cdb-4bac-bc95-e38c1c622a8c',
        '锅炉制造单位', 'boilerMakeUnit', '', 1000, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:17:17',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:18:25');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('f9f1d79f-1c49-4c4c-95b2-4b9242cd1b7f', 'LIM_PointType_WasteGas', '75ceb21a-0797-4805-bb8a-8e0a2d881e79',
        '启用时间', 'craftFacilityUseDate', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 2, b'0',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:16:53',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:25:00');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('f71e108f-b559-4765-9d2f-baeb40c69e5b', 'LIM_PointType_WasteGas', '594941c7-694f-48b3-aadc-bfb30df30d9d',
        '污染源种类', 'pollutionType', '', 200, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:16:03',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:55');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('6857567c-e198-4324-a7ac-187f672cd3ab', 'LIM_PointType_WasteGas', '1a31c8f8-c81c-45ed-826c-c7812ddbc0f9',
        '净化设备名称', 'purificateFacilityName', '', 300, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0',
        b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:15:44',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:44');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('a1c84ec2-5458-46d2-af76-8be3cc2ffe71', 'LIM_PointType_WasteGas', '72dcf71d-52e7-480f-aa9e-365d1fda4831',
        '工艺设备名称', 'craftFacilityName', '', 400, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:15:04',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:15');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('eeb270b7-c866-4a1c-ab14-a2ffc96df8f2', 'LIM_PointType_WasteGas', '5146948c-fca9-4d82-b92e-5749217885ad',
        '排气管高度', 'exhaustPipeHeight', '', 500, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:49',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:31');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('cd1e96b3-dbcc-4b38-9e61-5efdcfe7759d', 'LIM_PointType_Effluents', 'ea0ce619-f29d-4417-a7d3-b7d3f8c3b0e1',
        '排污去向', 'emissionFate', '', 100, '[{\"key\":\"\",\"value\":\"\"}]', '', '', '', '', '', 0, 1, b'0', b'0',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:14:15',
        '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351', '2023-07-14 13:24:03');
INSERT INTO TB_MONITOR_PointExtendConfig(id, pointType, paramsId, filedName, filedAlias, defaultValue, orderNum,
                                         dataSource, codeDataSource, dataSourceUrl, urlReturnKey, urlReturnValue,
                                         treeChildFiled, dataSourceType, defaultControl, requiredInd, isDeleted, orgId,
                                         creator, createDate, domainId, modifier, modifyDate)
VALUES ('c406f470-3d6a-47e8-b568-4d5286ae0954', 'LIM_PointType_Effluents', '3d404054-997a-42f5-8624-0effa1ef7ad3',
        '进出口', 'importAndExport', '', 200, '[{\"key\":\"进口\",\"value\":\"进口\"},{\"key\":\"出口\",\"value\":\"出口\"}]', '',
        '', '', '', '', 3, 4, b'0', b'0', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-07-14 13:13:57', '5f7bcf90feb545968424b0a872863876', '59141356591b48e18e139aa54d9dd351',
        '2023-07-14 13:24:00');
