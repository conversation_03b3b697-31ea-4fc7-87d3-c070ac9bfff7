package com.sinoyd.lims.pro.entity;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

/**
 * SampleReserve实体
 *
 * <AUTHOR>
 * @version V5.2.0 2022/6/20
 */
@MappedSuperclass
@ApiModel(description = "SampleReserve")
@Data
@EntityListeners(AuditingEntityListener.class)
public class SampleReserve implements BaseEntity, Serializable {
    private static final long serialVersionUID = 1L;

    public SampleReserve(){
        this.orgId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtil.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    @Column(length = 50, nullable = false)
    @ApiModelProperty("样品标识")
    private String sampleId;

    @Column(nullable = false)
    @ColumnDefault("getdate")
    @ApiModelProperty("操作日期")
    private Date reserveDate;

    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("领取人Id")
    private String reservePersonId;

    @ColumnDefault("0")
    @ApiModelProperty("假删字段")
    private Boolean isDeleted = false;

    @Column(nullable = false)
    @ColumnDefault("-1")
    @ApiModelProperty("类型")
    private Integer reserveType;

    @Column(length = 100)
    @ApiModelProperty("处置方式")
    @Length(message = "处置方式（1.领取，2.处置）{validation.message.length}", max = 100)
    private String disposeMethod;

    @Column(length = 100)
    @ApiModelProperty("备注")
    @Length(message = "备注{validation.message.length}", max = 100)
    private String remark;

    @Column(length = 50, nullable = false)
    @ApiModelProperty("样品分组主键id")
    private String sampleGroupId;

    /**
     * 组织机构id
     */
    @Column(length=50,nullable=false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("组织机构id")
    private String orgId;

    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    @Column(nullable = false)
    @ColumnDefault("getdate")
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("所属实验室")
    private String domainId;

    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @LastModifiedBy
    @ApiModelProperty("修改人")
    private String modifier;

    @Column(nullable = false)
    @ColumnDefault("getdate")
    @LastModifiedDate
    @ApiModelProperty("修改时间")
    private Date modifyDate;
}
