package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * StandardMethodDetail查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/13
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "StandardMethodDetail查询条件")
public class StandardMethodDetailCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 方法id
     */
    private String methodId;


    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();

        if (StringUtil.isNotEmpty(methodId)) {
            condition.append(" and a.methodId = :methodId");
            values.put("methodId", this.methodId);
        }

        return condition.toString();
    }
} 