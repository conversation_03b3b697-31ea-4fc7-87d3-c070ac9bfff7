package com.sinoyd.lims.lim.utils;

import com.sinoyd.lims.lim.vo.CalendarDayVO;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 日历工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/1/19
 */
@Slf4j
public class CalendarUtil {

    /**
     * 按年产生自然日对象列表(一年的每一天)，并判断出周末，周末作为休息日，其余作为工作日
     *
     * @param year 年份
     * @return 结果
     */
    public static List<CalendarDayVO> loadCalendarDayByYear(Integer year) {
        List<CalendarDayVO> voList = new ArrayList<>();
        Date startDate = getFirstDateOfYear(year);
        Date endDate = getLastDateOfYear(year);
        Calendar calendar = Calendar.getInstance();
        while (startDate.compareTo(endDate) <= 0) {
            calendar.setTime(startDate);
            int week = getChineseWeek(calendar);
            CalendarDayVO vo = new CalendarDayVO()
                    .setDay(startDate)
                    .setWeekDay(week)
                    .setType(week < 6 ? 0 : 1);
            voList.add(vo);
            calendar.add(Calendar.DAY_OF_YEAR, 1);
            startDate = calendar.getTime();
        }
        return voList;
    }

    /**
     * 获取中国的星期，1: 星期一， 2: 星期二 .... 7: 星期日
     *
     * @param calendar 日历对象
     * @return 结果
     */
    public static int getChineseWeek(Calendar calendar) {
        int week = calendar.get(Calendar.DAY_OF_WEEK);
        if (week > 1) {
            return --week;
        } else {
            return 7;
        }
    }

    /**
     * 获取一年的第一天日期
     *
     * @param year 年份
     * @return 结果
     */
    public static Date getFirstDateOfYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(year, Calendar.JANUARY, 1);
        return calendar.getTime();
    }

    /**
     * 获取一年的最后一天日期
     *
     * @param year 年份
     * @return 结果
     */
    public static Date getLastDateOfYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(year, Calendar.DECEMBER, 31);
        return calendar.getTime();
    }
}