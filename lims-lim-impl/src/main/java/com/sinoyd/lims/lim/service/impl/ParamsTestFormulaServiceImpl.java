package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsTestFormula;
import com.sinoyd.lims.lim.repository.rcc.ParamsTestFormulaRepository;
import com.sinoyd.lims.lim.service.ParamsTestFormulaService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ParamsTestFormula操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/11/4
 * @since V100R001
 */
@Service
public class ParamsTestFormulaServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoParamsTestFormula, String, ParamsTestFormulaRepository> implements ParamsTestFormulaService {

    @Override
    public void findByPage(PageBean<DtoParamsTestFormula> pb, BaseCriteria paramsTestFormulaCriteria) {
        pb.setEntityName("DtoParamsTestFormula a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, paramsTestFormulaCriteria);
    }

    /**
     * 删除参数公式相关参数
     *
     * @param objId 参数公式id
     * @return 返回删除行数
     */
    @Transactional
    @Override
    public Integer deleteByObjId(String objId) {
        return repository.deleteByObjId(objId);
    }

    @Override
    public List<DtoParamsTestFormula> findByObjIds(List<String> formulaIds) {
        if (StringUtil.isNotNull(formulaIds) && formulaIds.size() > 0) {
            return repository.findByObjIdIn(formulaIds);
        }
        return new ArrayList<>();
    }

    @Override
    public List<DtoParamsTestFormula> findByObjectId(String formulaId) {
        return  repository.findByObjId(formulaId);
    }
}