package com.sinoyd.lims.monitor.service;

import com.sinoyd.lims.monitor.dto.customer.DtoTestPointVo;
import com.sinoyd.lims.monitor.dto.lims.DtoProperty2Point;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;
import java.util.Map;


/**
 * Property2Point操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface Property2PointService extends IBaseJpaService<DtoProperty2Point, String> {

    /**
     * 获取监测计划的关联点位列表
     *
     * @param propertyId 监测计划id
     * @return 关联实体列表
     */
    List<DtoProperty2Point> loadProperty2PointList(String propertyId);

    /**
     * 批量新增测试项目
     *
     * @param paramters 参数
     */
    void batchAddTest(Map<String, Object> paramters);

    /**
     * 批量删除测试项目
     *
     * @param paramters 参数
     */
    void batchDeleteTest(Map<String, List<String>> paramters);

    /**
     * 批量修改批次及样品数
     * @param pointVo 集合
     */
    void batchTimesOrSampleOrder(DtoTestPointVo pointVo);
}