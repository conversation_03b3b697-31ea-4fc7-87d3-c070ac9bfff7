package com.sinoyd.lims.od.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.repository.lims.PersonRepository;
import com.sinoyd.lims.od.dto.DtoLabSeq;
import com.sinoyd.lims.od.dto.DtoOdPerson;
import com.sinoyd.lims.od.dto.DtoOdPersonAnswer;
import com.sinoyd.lims.od.dto.DtoTask;
import com.sinoyd.lims.od.enums.EnumOds;
import com.sinoyd.lims.od.repository.*;
import com.sinoyd.lims.od.service.OdPersonAnswerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Service
@Slf4j
public class OdPersonAnswerServiceImpl extends BaseJpaServiceImpl<DtoOdPersonAnswer, String, OdPersonAnswerRepository> implements OdPersonAnswerService {


    private LabSeqRepository labSeqRepository;

    private TaskRepository taskRepository;

    private PersonRepository personRepository;

    private OdPersonRepository odPersonRepository;

    @Override
    public void findByPage(PageBean<DtoOdPersonAnswer> pageBean, BaseCriteria criteria) {
        pageBean.setEntityName("DtoOdPersonAnswer p, DtoLabSeq a, DtoLabGroup g, DtoOdSample s, DtoTask t");
        pageBean.setSelect("select new com.sinoyd.lims.od.dto.DtoOdPersonAnswer(p.id," +
                "p.personId, p.labSeqId, s.sampleCode, s.taskId, g.sn, a.sn, p.isCompleted, p.personAnswer, p.confidenceLvl)");
        super.findByPage(pageBean, criteria);
        loadTransientFields(pageBean.getData());
    }

    @Override
    public DtoOdPersonAnswer findOne(String key) {
        DtoOdPersonAnswer one = super.findOne(key);
        // 标准答案置空
        one.setStandardAnswer("");
        return one;
    }

    @Override
    @Transactional
    public DtoOdPersonAnswer update(DtoOdPersonAnswer entity) {
        DtoOdPersonAnswer personAnswer = super.findOne(entity.getId());
        personAnswer.setPersonAnswer(entity.getPersonAnswer());
        if (StringUtil.isNotNull(entity.getConfidenceLvl())){
            personAnswer.setConfidenceLvl(entity.getConfidenceLvl());
        }
        super.update(personAnswer);
        return entity;
    }

    @Override
    public List<DtoOdPersonAnswer> findByLabSeqId(String labSeqId) {
        List<DtoOdPersonAnswer> personAnswerList = repository.findByLabSeqId(labSeqId);
        // 嗅辨员集合
        List<String> odPersonIds = personAnswerList.stream().map(DtoOdPersonAnswer::getPersonId).distinct().collect(Collectors.toList());
        List<DtoOdPerson> odPersonList = StringUtil.isNotEmpty(odPersonIds) ? odPersonRepository.findAll(odPersonIds) : new ArrayList<>();
        // 人员集合
        List<String> personIds = odPersonList.stream().map(DtoOdPerson::getPersonId).collect(Collectors.toList());
        List<DtoPerson> personList = StringUtil.isNotEmpty(personIds) ? personRepository.findAll(personIds) : new ArrayList<>();
        personAnswerList.forEach(p -> {
            odPersonList.stream().filter(per -> per.getId().equals(p.getPersonId())).findFirst().ifPresent(odPerson -> {
                p.setPersonSn(odPerson.getPersonSn());
                personList.stream().filter(t -> t.getId().equals(odPerson.getPersonId())).findFirst().ifPresent(per -> {
                    p.setPersonName(per.getCName());
                });
            });
        });
        // 嗅辨流水排序
        personAnswerList.sort(Comparator.comparing(DtoOdPersonAnswer::getPersonSn));
        return personAnswerList;
    }

    @Override
    public List<DtoOdPersonAnswer> findByGroupIdIn(List<String> groupIds) {
        List<DtoLabSeq> labSeqList = StringUtil.isNotEmpty(groupIds) ? labSeqRepository.findByGroupIdIn(groupIds) : new ArrayList<>();
        List<String> labSeqIds = labSeqList.stream().map(DtoLabSeq::getId).collect(Collectors.toList());
        return StringUtil.isNotEmpty(labSeqIds) ? repository.findByLabSeqIdIn(labSeqIds) : new ArrayList<>();
    }

    @Override
    @Transactional
    public DtoOdPersonAnswer submit(DtoOdPersonAnswer odPersonAnswer) {
        DtoOdPersonAnswer oldAnswer = repository.findOne(odPersonAnswer.getId());
        // 答案错误
        if (!odPersonAnswer.getPersonAnswer().equals(oldAnswer.getStandardAnswer())) {
            oldAnswer.setEvaluation(EnumOds.EnumEvaluation.错误.getValue());
        } else {
            // 环境空气需要根据自信度判断是否不明
            if (odPersonAnswer.getTaskType().equals(EnumOds.EnumTaskType.环境空气.getValue())
                    && odPersonAnswer.getConfidenceLvl().equals(EnumOds.EnumConfidenceLvl.猜测.getValue())) {
                oldAnswer.setEvaluation(EnumOds.EnumEvaluation.不明.getValue());
            } else {
                oldAnswer.setEvaluation(EnumOds.EnumEvaluation.正确.getValue());
            }
        }
        if (StringUtil.isNotNull(odPersonAnswer.getConfidenceLvl())){
            oldAnswer.setConfidenceLvl(odPersonAnswer.getConfidenceLvl());
        }
        oldAnswer.setPersonAnswer(odPersonAnswer.getPersonAnswer());
        oldAnswer.setIsCompleted(true);
        return super.update(oldAnswer);
    }

    private void loadTransientFields(List<DtoOdPersonAnswer> data) {
        // 嗅辨任务
        List<String> taskIds = data.stream().map(DtoOdPersonAnswer::getTaskId).collect(Collectors.toList());
        List<String> odPersonIds = data.stream().map(DtoOdPersonAnswer::getPersonId).collect(Collectors.toList());
        List<DtoOdPerson> odPersonList = StringUtil.isNotEmpty(odPersonIds) ? odPersonRepository.findAll(odPersonIds) : new ArrayList<>();
        List<DtoTask> taskList = StringUtil.isNotEmpty(taskIds) ? taskRepository.findAll(taskIds) : new ArrayList<>();
        List<DtoPerson> personList = personRepository.findAll();
        Map<String, DtoPerson> personMap = personList.stream().collect(Collectors.toMap(DtoPerson::getId, p -> p));

        for (DtoOdPersonAnswer answer : data) {
            taskList.stream().filter(p -> p.getId().equals(answer.getTaskId())).findFirst().ifPresent(task -> {
                DtoPerson person = personMap.get(task.getGasMixerId());
                // 配气员
                answer.setGasMixerName(StringUtil.isNotNull(person) ? person.getCName() : "");
                answer.setOdData(task.getOdDate());
                answer.setTaskType(task.getTaskType());
            });
            // 嗅辨员
            odPersonList.stream().filter(p-> p.getId().equals(answer.getPersonId())).findFirst().ifPresent(odp->{
                DtoPerson person = personMap.get(odp.getPersonId());
                answer.setPersonName(StringUtil.isNotNull(person) ? person.getCName() : "");
            });
            // 标准答案置空
            answer.setStandardAnswer("");
        }
    }

    @Autowired
    @Lazy
    public void setLabSeqRepository(LabSeqRepository labSeqRepository) {
        this.labSeqRepository = labSeqRepository;
    }

    @Autowired
    @Lazy
    public void setTaskRepository(TaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }

    @Autowired
    @Lazy
    public void setPersonRepository(PersonRepository personRepository) {
        this.personRepository = personRepository;
    }

    @Autowired
    @Lazy
    public void setOdPersonRepository(OdPersonRepository odPersonRepository) {
        this.odPersonRepository = odPersonRepository;
    }
}
