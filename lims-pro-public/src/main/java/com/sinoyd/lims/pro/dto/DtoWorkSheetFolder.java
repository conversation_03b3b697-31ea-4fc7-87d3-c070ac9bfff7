package com.sinoyd.lims.pro.dto;

import com.sinoyd.lims.pro.entity.WorkSheetFolder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * DtoWorkSheetFolder实体
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PRO_WorkSheetFolder")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public  class DtoWorkSheetFolder extends WorkSheetFolder {
    private static final long serialVersionUID = 1L;

    /**
     * 检测单id
     */
    @Transient
    private String workSheetId;

    /**
     * 等级
     */
    @Transient
    private Integer grade;

    /**
     * 是否按样品显示
     */
    @Transient
    private Boolean isSample;

    /**
     * 分析项目名称
     */
    @Transient
    private String redAnalyzeItemName;

    /**
     * 分析方法名称
     */
    @Transient
    private String redAnalyzeMethodName;

    /**
     * 标准编号
     */
    @Transient
    private String redCountryStandard;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;
}