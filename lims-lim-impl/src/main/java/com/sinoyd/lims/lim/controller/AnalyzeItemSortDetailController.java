
package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSort;
import com.sinoyd.lims.lim.dto.lims.DtoAnalyzeItemSortDetail;
import com.sinoyd.lims.lim.service.AnalyzeItemSortDetialService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 使用仪器
 * <AUTHOR> 修改：xuxb
 * @version V1.0.0 2019/1/17
 * @since V100R001ss
 */
@RestController
@RequestMapping("/api/lim/analyzeItemSortDetail")
@Validated
public class AnalyzeItemSortDetailController
        extends BaseJpaController<DtoAnalyzeItemSortDetail, String, AnalyzeItemSortDetialService> {

    /**
     * 获取分析项目排序详情
     * 
     * @param sortId
     * @return
     */
    @GetMapping("")
    public RestResponse<List<DtoAnalyzeItemSortDetail>> getList(@RequestParam("sortId") String sortId) {

        RestResponse<List<DtoAnalyzeItemSortDetail>> restResp = new RestResponse<>();

        List<DtoAnalyzeItemSortDetail> details = service.getSortDetailList(sortId);

        restResp.setRestStatus(StringUtil.isEmpty(details) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResp.setData(details);
        restResp.setCount(details.size());

        return restResp;

    }

    /**
     * 新增分析项目排序详情
     * 
     * @param analyzeItemSort 分析项目排序详情
     */
    @PostMapping
    public RestResponse<String> saveSortDetails(@Validated @RequestBody DtoAnalyzeItemSort analyzeItemSort) {

        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        Integer count = service.saveSortDetails(analyzeItemSort.getId(), analyzeItemSort.getAnalyzeItemList());
        restResp.setCount(count);

        return restResp;
    }
}
