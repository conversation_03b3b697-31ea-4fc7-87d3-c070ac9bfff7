package com.sinoyd.lims.pro.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.controller.ExceptionHandlerController;
import com.sinoyd.lims.lim.dto.customer.DtoSchemaVersionTemp;
import com.sinoyd.lims.lim.dto.lims.DtoFastNavigationTemplate;
import com.sinoyd.lims.pro.dto.DtoHomePendingNo;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.service.HomeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * 首页的操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/03/12
 * @since V100R001
 */
@Api(tags = "示例: 首页")
@RestController
@RequestMapping("api/pro/home")
public class HomeController  extends ExceptionHandlerController<HomeService> {
    @ApiOperation(value = "缓存首页业务数据", notes = "缓存首页业务数据")
    @GetMapping("/project")
    public RestResponse<Integer> cacheProjectTask(@RequestParam("userId") String userId,
                                                  @RequestParam("moduleCode") String moduleCode) throws ExecutionException, InterruptedException {
        RestResponse<Integer> restResponse = new RestResponse<>();
        Future<Integer> future = service.cacheProjectTask(userId, PrincipalContextUser.getPrincipal().getOrgId(), moduleCode);
        restResponse.setData(future.get());
        return restResponse;
    }

    @ApiOperation(value = "查询重点项目", notes = "查询重点项目")
    @GetMapping("/keyProject")
    public RestResponse<List<Map<String, Object>>> findCacheKeyProjects() {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        PageBean<Map<String, Object>> pageBean = super.getPageBean();
        service.findCacheKeyProjects(pageBean);
        restResponse.setData(pageBean.getData());
        restResponse.setRestStatus(StringUtil.isNull(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "查询公告", notes = "查询公告")
    @GetMapping("/notice")
    public RestResponse<List<Map<String, Object>>> findCacheNotices() {
        RestResponse<List<Map<String, Object>>> restResponse = new RestResponse<>();
        PageBean<Map<String, Object>> pageBean = super.getPageBean();
        service.findCacheNotices(pageBean);
        restResponse.setData(pageBean.getData());
        restResponse.setRestStatus(StringUtil.isNull(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "查询待审批任务", notes = "查询待审批任务")
    @GetMapping("/oaTask")
    public RestResponse<List<DtoOATask>> findOATaskCache(@RequestParam("moduleCode") String moduleCode) {
        RestResponse<List<DtoOATask>> restResponse = new RestResponse<>();
        PageBean<DtoOATask> pageBean = super.getPageBean();
        service.findOATaskCache(pageBean, moduleCode);
        restResponse.setData(pageBean.getData());
        restResponse.setRestStatus(StringUtil.isNull(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "查询快速导航", notes = "查询快速导航")
    @GetMapping("/fastNavigation")
    public RestResponse<List<DtoFastNavigationTemplate>> findFastNavigationCache() {
        RestResponse<List<DtoFastNavigationTemplate>> restResponse = new RestResponse<>();
        PageBean<DtoFastNavigationTemplate> pageBean = super.getPageBean();
        service.findFastNavigationCache(pageBean);
        restResponse.setData(pageBean.getData());
        restResponse.setRestStatus(StringUtil.isNull(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "查询首页统计", notes = "查询首页统计")
    @GetMapping("/stat")
    public RestResponse<List<?>> findStatCache(@RequestParam("code") String code) {
        RestResponse<List<?>> restResponse = new RestResponse<>();
        List statList = service.findStatCache(code);
        restResponse.setData(statList);
        restResponse.setRestStatus(StringUtil.isNull(statList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    @ApiOperation(value = "缓存消息数据", notes = "缓存消息数据")
    @GetMapping("/message")
    public RestResponse<Integer> findCacheMessageTotal(@RequestParam("dtBegin") String dtBegin,
                                                       @RequestParam("dtEnd") String dtEnd) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.findCacheMessageTotal(dtBegin, dtEnd));
        return restResponse;
    }

    @ApiOperation(value = "当前月份的样品数", notes = "当前月份的样品数")
    @GetMapping("/countByMonth")
    public RestResponse<Map<String, Integer>> findSampleCountByMonth(@RequestParam("year") int year,
                                                       @RequestParam("month") int month) {
        RestResponse<Map<String, Integer>> restResponse = new RestResponse<>();
        restResponse.setData(service.findSampleCountByMonth(year, month));
        return restResponse;
    }

    @ApiOperation(value = "首页待办数据", notes = "首页待办数据")
    @GetMapping("/homePendingNo")
    public RestResponse<List<DtoHomePendingNo>> findHomePendingNo(@RequestParam("orgId") String orgId,
                                                                       @RequestParam("userId") String userId) {
        RestResponse<List<DtoHomePendingNo>> restResponse = new RestResponse<>();
        restResponse.setData(service.findHomePendingNo(orgId, userId));
        return restResponse;
    }

    @ApiOperation(value = "首页flyway脚本数据", notes = "首页flyway脚本数据")
    @GetMapping("/flyway")
    public RestResponse<List<DtoSchemaVersionTemp>> findFlyway(DtoSchemaVersionTemp schemaVersionTemp) {
        PageBean<DtoSchemaVersionTemp> pageBean = super.getPageBean();
        RestResponse<List<DtoSchemaVersionTemp>> restResponse = new RestResponse<>();
        service.findFlyway(pageBean,schemaVersionTemp);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }


}
