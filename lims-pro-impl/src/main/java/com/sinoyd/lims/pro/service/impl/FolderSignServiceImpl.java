package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.lims.pro.dto.DtoFolderSign;
import com.sinoyd.lims.pro.repository.FolderSignRepository;
import com.sinoyd.lims.pro.service.FolderSignService;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.stereotype.Service;


/**
 * FolderSign操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/7/7
 * @since V100R001
 */
 @Service
public class FolderSignServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoFolderSign,String,FolderSignRepository> implements FolderSignService {

    @Override
    public void findByPage(PageBean<DtoFolderSign> pb, BaseCriteria folderSignCriteria) {
        pb.setEntityName("DtoFolderSign a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, folderSignCriteria);
    }
}