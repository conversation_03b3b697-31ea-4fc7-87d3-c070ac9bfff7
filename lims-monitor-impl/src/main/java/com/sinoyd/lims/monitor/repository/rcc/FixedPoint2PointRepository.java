package com.sinoyd.lims.monitor.repository.rcc;

import com.sinoyd.lims.monitor.dto.rcc.DtoFixedPoint2Point;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * FixedPoint2Point数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface FixedPoint2PointRepository extends IBaseJpaPhysicalDeleteRepository<DtoFixedPoint2Point, String> {

    /**
     * 根据点位id查询关联点位信息
     *
     * @param fixedPointId 点位id
     * @return 关联点位列表
     */
    List<DtoFixedPoint2Point> findByFixedPointId(String fixedPointId);

    /**
     * 根据点位id列表查询关联点位信息
     *
     * @param fixedPointIds 点位id列表
     * @return 关联点位列表
     */
    List<DtoFixedPoint2Point> findByFixedPointIdIn(List<String> fixedPointIds);
}