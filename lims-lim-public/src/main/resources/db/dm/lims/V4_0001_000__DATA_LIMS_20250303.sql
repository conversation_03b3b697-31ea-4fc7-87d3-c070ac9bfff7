CREATE TABLE TB_Ods_Task
(
    id         VARCHAR(50)  NOT NULL,
    taskType   INT          NOT NULL DEFAULT -1,
    taskName   VARCHAR(100) NOT NULL,
    gasMixerId VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    odDate     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    taskState  INT          NOT NULL DEFAULT -1,
    isDeleted  BIT          NOT NULL DEFAULT 0,
    domainId   VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId      VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator    <PERSON><PERSON><PERSON><PERSON>(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    modifier   VARCHAR(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_Task IS '嗅辨任务表';
COMMENT ON COLUMN TB_Ods_Task.id IS '主键';
COMMENT ON COLUMN TB_Ods_Task.taskType IS '嗅辨类型，枚举管理';
COMMENT ON COLUMN TB_Ods_Task.taskName IS '任务名称';
COMMENT ON COLUMN TB_Ods_Task.gasMixerId IS '配气员id';
COMMENT ON COLUMN TB_Ods_Task.odDate IS '嗅辨日期';
COMMENT ON COLUMN TB_Ods_Task.taskState IS '任务状态，枚举管理';
COMMENT ON COLUMN TB_Ods_Task.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_Task.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_Task.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_Task.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_Task.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_Task.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_Task.modifyDate IS '更新时间';

CREATE TABLE TB_Ods_Sample
(
    id           VARCHAR(50) NOT NULL,
    taskId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    sampleCode   VARCHAR(50) NOT NULL,
    samplingDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    sampleState  INT         NOT NULL DEFAULT -1,
    isDeleted    BIT         NOT NULL DEFAULT 0,
    domainId     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    modifier     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_Sample IS '嗅辨样品表';
COMMENT ON COLUMN TB_Ods_Sample.id IS '主键';
COMMENT ON COLUMN TB_Ods_Sample.taskId IS '嗅辨任务id';
COMMENT ON COLUMN TB_Ods_Sample.sampleCode IS '样品编号';
COMMENT ON COLUMN TB_Ods_Sample.samplingDate IS '采样日期';
COMMENT ON COLUMN TB_Ods_Sample.sampleState IS '样品状态， 枚举管理';
COMMENT ON COLUMN TB_Ods_Sample.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_Sample.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_Sample.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_Sample.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_Sample.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_Sample.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_Sample.modifyDate IS '更新时间';

CREATE TABLE TB_Ods_SampleResult
(
    id               VARCHAR(50) NOT NULL,
    sampleId         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    rValue           VARCHAR(20),
    tValue           VARCHAR(20),
    xValue           VARCHAR(20),
    aValue           VARCHAR(20),
    bValue           VARCHAR(20),
    odourConsistence VARCHAR(20),
    isPassed         BIT NOT NULL DEFAULT  0,
    isDeleted        BIT NOT NULL DEFAULT  0,
    domainId         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId            VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    modifier         VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_SampleResult IS '样品结果表';
COMMENT ON COLUMN TB_Ods_SampleResult.id IS '主键';
COMMENT ON COLUMN TB_Ods_SampleResult.sampleId IS '样品id';
COMMENT ON COLUMN TB_Ods_SampleResult.rValue IS 'r相关系数(固定源)';
COMMENT ON COLUMN TB_Ods_SampleResult.tValue IS 'T检验结果(固定源)';
COMMENT ON COLUMN TB_Ods_SampleResult.xValue IS 'X平均阈值(固定源)';
COMMENT ON COLUMN TB_Ods_SampleResult.aValue IS 'α幂参数(环境空气)';
COMMENT ON COLUMN TB_Ods_SampleResult.bValue IS 'β幂参数(环境空气)';
COMMENT ON COLUMN TB_Ods_SampleResult.odourConsistence IS '臭气浓度';
COMMENT ON COLUMN TB_Ods_SampleResult.isPassed IS '是否通过';
COMMENT ON COLUMN TB_Ods_SampleResult.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_SampleResult.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_SampleResult.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_SampleResult.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_SampleResult.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_SampleResult.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_SampleResult.modifyDate IS '更新时间';

CREATE TABLE TB_Ods_OdPerson
(
    id         VARCHAR(50) NOT NULL,
    taskId     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    personId   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    personSn   VARCHAR(10) NOT NULL,
    isDeleted  BIT         NOT NULL DEFAULT 0,
    domainId   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    modifier   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_OdPerson IS '嗅辨员表';
COMMENT ON COLUMN TB_Ods_OdPerson.id IS '主键';
COMMENT ON COLUMN TB_Ods_OdPerson.taskId IS '嗅辨任务id';
COMMENT ON COLUMN TB_Ods_OdPerson.personId IS '嗅辨员id';
COMMENT ON COLUMN TB_Ods_OdPerson.personSn IS '嗅辨员次序，A、B、C、D之类流水';
COMMENT ON COLUMN TB_Ods_OdPerson.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_OdPerson.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_OdPerson.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_OdPerson.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_OdPerson.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_OdPerson.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_OdPerson.modifyDate IS '更新时间';

CREATE TABLE TB_Ods_LabGroup
(
    id          VARCHAR(50) NOT NULL,
    sampleId    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    sn          INT         NOT NULL DEFAULT -1,
    isCalculate BIT         NOT NULL DEFAULT 0,
    calculateSn INT         NOT NULL DEFAULT -1,
    isDeleted   BIT         NOT NULL DEFAULT 0,
    domainId    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modifier    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_LabGroup IS '实验组表';
COMMENT ON COLUMN TB_Ods_LabGroup.id IS '主键';
COMMENT ON COLUMN TB_Ods_LabGroup.sampleId IS '样品id';
COMMENT ON COLUMN TB_Ods_LabGroup.sn IS '序号(第几组实验)';
COMMENT ON COLUMN TB_Ods_LabGroup.isCalculate IS '是否作为最终分析结果';
COMMENT ON COLUMN TB_Ods_LabGroup.calculateSn IS '最终分析结果显示顺序';
COMMENT ON COLUMN TB_Ods_LabGroup.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_LabGroup.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_LabGroup.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_LabGroup.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_LabGroup.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_LabGroup.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_LabGroup.modifyDate IS '更新时间';

CREATE TABLE TB_Ods_LabSeq
(
    id           VARCHAR(50) NOT NULL,
    groupId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    sn           INT         NOT NULL DEFAULT -1,
    dilutionRate INT         NOT NULL DEFAULT -1,
    labState     INT         NOT NULL DEFAULT -1,
    isDeleted    BIT         NOT NULL DEFAULT 0,
    domainId     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    modifier     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_LabSeq IS '实验次序表';
COMMENT ON COLUMN TB_Ods_LabSeq.id IS '主键';
COMMENT ON COLUMN TB_Ods_LabSeq.groupId IS '实验组id';
COMMENT ON COLUMN TB_Ods_LabSeq.sn IS '实验次序号(第几次实验)';
COMMENT ON COLUMN TB_Ods_LabSeq.dilutionRate IS '稀释倍数';
COMMENT ON COLUMN TB_Ods_LabSeq.labState IS '实验状态，枚举管理';
COMMENT ON COLUMN TB_Ods_LabSeq.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_LabSeq.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_LabSeq.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_LabSeq.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_LabSeq.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_LabSeq.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_LabSeq.modifyDate IS '更新时间';

CREATE TABLE TB_Ods_StationarySourceResult
(
    id           VARCHAR(50) NOT NULL,
    groupId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    avgThreshold VARCHAR(20),
    rsd          VARCHAR(20),
    isDeleted    BIT         NOT NULL DEFAULT 0,
    domainId     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    modifier     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_StationarySourceResult IS '固定源结果表';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.id IS '主键';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.groupId IS '实验组id';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.avgThreshold IS '平均阈值';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.rsd IS '标准偏差';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_StationarySourceResult.modifyDate IS '更新时间';

CREATE TABLE TB_Ods_StationarySourceResultDetail
(
    id         VARCHAR(50) NOT NULL,
    resultId   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    personId   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    threshold  VARCHAR(20) NOT NULL,
    isDeleted  BIT         NOT NULL DEFAULT 0,
    domainId   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator    VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    modifier   VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_StationarySourceResultDetail IS '固定源结果详情表表';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.id IS '主键';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.resultId IS '结果表id';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.personId IS '嗅辨员id';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.threshold IS '个人阈值';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_StationarySourceResultDetail.modifyDate IS '更新时间';

CREATE TABLE TB_Ods_EnvGasResult
(
    id           VARCHAR(50) NOT NULL,
    groupId      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    dilutionRate INT         NOT NULL DEFAULT -1,
    aCount       INT         NOT NULL DEFAULT -1,
    bCount       INT         NOT NULL DEFAULT -1,
    cCount       INT         NOT NULL DEFAULT -1,
    mValue       VARCHAR(20) NOT NULL DEFAULT -1,
    isPassed     BIT         NOT NULL DEFAULT 0,
    isDeleted    BIT         NOT NULL DEFAULT 0,
    domainId     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator      VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    modifier     VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_EnvGasResult IS '环境空气结果表';
COMMENT ON COLUMN TB_Ods_EnvGasResult.id IS '主键';
COMMENT ON COLUMN TB_Ods_EnvGasResult.groupId IS '实验组id';
COMMENT ON COLUMN TB_Ods_EnvGasResult.dilutionRate IS '稀释倍数';
COMMENT ON COLUMN TB_Ods_EnvGasResult.aCount IS '答对的次数';
COMMENT ON COLUMN TB_Ods_EnvGasResult.bCount IS '不明的次数';
COMMENT ON COLUMN TB_Ods_EnvGasResult.cCount IS '答错的次数';
COMMENT ON COLUMN TB_Ods_EnvGasResult.mValue IS 'M值';
COMMENT ON COLUMN TB_Ods_EnvGasResult.isPassed IS '是否通过';
COMMENT ON COLUMN TB_Ods_EnvGasResult.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_EnvGasResult.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_EnvGasResult.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_EnvGasResult.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_EnvGasResult.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_EnvGasResult.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_EnvGasResult.modifyDate IS '更新时间';

CREATE TABLE TB_Ods_OdPersonAnswer
(
    id             VARCHAR(50) NOT NULL,
    labSeqId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    personId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    personAnswer   VARCHAR(10),
    standardAnswer VARCHAR(10) NOT NULL,
    evaluation     INT                  DEFAULT -1,
    confidenceLvl  INT                  DEFAULT -1,
    isCompleted    BIT         NOT NULL DEFAULT 0,
    isDeleted      BIT         NOT NULL DEFAULT 0,
    domainId       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    orgId          VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    creator        VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    createDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    modifier       VARCHAR(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    modifyDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    PRIMARY KEY (id)
);
COMMENT ON TABLE TB_Ods_OdPersonAnswer IS '嗅辨员答案表';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.id IS '主键';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.labSeqId IS '实验次序id';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.personId IS '嗅辨员id';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.personAnswer IS '嗅辨员测试结果';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.standardAnswer IS '标准答案';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.evaluation IS '评价，枚举管理';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.confidenceLvl IS '自信度，枚举管理';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.isCompleted IS '是否嗅辨完成';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.isDeleted IS '假删标识';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.domainId IS '所属实验室id';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.orgId IS '组织机构id';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.creator IS '创建人';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.createDate IS '创建时间';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.modifier IS '更新人';
COMMENT ON COLUMN TB_Ods_OdPersonAnswer.modifyDate IS '更新时间';
