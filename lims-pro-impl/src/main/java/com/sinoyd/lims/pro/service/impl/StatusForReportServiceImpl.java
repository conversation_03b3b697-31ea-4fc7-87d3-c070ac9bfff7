package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.pro.dto.DtoReport;
import com.sinoyd.lims.pro.dto.DtoStatusForReport;
import com.sinoyd.lims.lim.enums.EnumLIM.EnumReportModule;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumReportState;
import com.sinoyd.lims.pro.enums.EnumPRO.EnumStatus;
import com.sinoyd.lims.pro.repository.StatusForReportRepository;
import com.sinoyd.lims.pro.service.StatusForReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 报告状态操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/8
 * @since V100R001
 */
 @Service
public class StatusForReportServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoStatusForReport,String,StatusForReportRepository> implements StatusForReportService {

    @Autowired
    private StatusForReportRepository statusForReportRepository;

    /**
     * 创建状态
     *
     * @param reportId 报告id
     */
    @Transactional
    @Override
    public void createStatus(String reportId){
        DtoStatusForReport status = new DtoStatusForReport();
        status.setModule(EnumLIM.EnumReportModule.报告编制.getCode());
        status.setReportId(reportId);
        status.setStatus(EnumStatus.待处理.getValue());
        repository.save(status);
    }

    /**
     * 修改状态数据
     *
     * @param from   状态起
     * @param to     状态止
     * @param sign   工作流信号对象
     * @param report 报告实体
     */
    @Transactional
    @Override
    public void modifyStatus(String from, String to, DtoWorkflowSign sign, DtoReport report, Map<String, DtoPerson> personMap) {
        if (from.equals(to)) {
            return;
        }
        //读取该报告的所有状态数据
        List<DtoStatusForReport> statusList = statusForReportRepository.findByReportId(report.getId());
        //将list转为map      
        Map<String, DtoStatusForReport> statusMap = statusList.stream().collect(Collectors.toMap(DtoStatusForReport::getModule, status -> status));

        EnumReportState fromStatus = EnumReportState.getByName(from);
        String fromModule = StringUtil.isNull(fromStatus) ? "" : this.getModule(fromStatus);
        EnumReportState toStatus = EnumReportState.getByName(to);
        String toModule = StringUtil.isNull(toStatus) ? "" : this.getModule(toStatus);
        //若状态起在状态止之后，则不需要修改状态起的状态表数据，直接删除即可，否则需将状态起的状态表数据改为已处理
        if (StringUtils.isNotNullAndEmpty(fromModule) && fromStatus.getValue() > toStatus.getValue()) {
            fromModule = "";
        }

        if (StringUtil.isNotEmpty(fromModule)) {
            //默认存在当前状态对应的状态数据
            DtoStatusForReport status = statusMap.get(fromModule);
            status.setStatus(EnumStatus.已处理.getValue());
            status.setNextPersonId(sign.getNextOperatorId());
            status.setNextPersonName(sign.getNextOperator());
            status.setLastNewOpinion("");
            this.save(status);
        } else {//若来源模块为空，说明为退回，需移除状态
            this.removeStatus(toModule, statusMap);
        }

        if (StringUtil.isNotEmpty(toModule)) {
            //若存在状态止的待处理模块，需修改对应状态数据
            if (statusMap.containsKey(toModule)) {
                //若数据库中存在该条数据
                DtoStatusForReport status = statusMap.get(toModule);
                status.setStatus(EnumStatus.待处理.getValue());
                status.setNextPersonId(UUIDHelper.GUID_EMPTY);
                status.setNextPersonName("");
                status.setLastNewOpinion(sign.getOption());
                this.save(status);
            } else {
                DtoStatusForReport status = new DtoStatusForReport();
                status.setReportId(report.getId());
                status.setModule(toModule);
                status.setStatus(EnumStatus.待处理.getValue());
                status.setNextPersonId(UUIDHelper.GUID_EMPTY);
                status.setNextPersonName("");
                status.setLastNewOpinion(sign.getOption());
                status.setCurrentPersonId(sign.getNextOperatorId());
                status.setCurrentPersonName(sign.getNextOperator());
                if ("reviewPass".equals(sign.getSignal())) {
                    //报告复核提交通过时获取报告的二审人作为下一步操作人
                    status.setCurrentPersonId(report.getSecondInstanceId());
                    DtoPerson person = personMap.get(report.getSecondInstanceId());
                    status.setCurrentPersonName(StringUtil.isNotNull(person) ? person.getCName() : "");
                }
                status.setLastNewOpinion(sign.getOption());
                this.save(status);
            }
        }
    }

    /**
     * 根据到达的模块状态删除该状态之后的数据
     *
     * @param toModule  到达的状态模块
     * @param statusMap 状态数据map
     */
    private void removeStatus(String toModule, Map<String, DtoStatusForReport> statusMap) {
        Map<String, Integer> enumMap = new HashMap<>();
        for (EnumReportModule m : EnumLIM.EnumReportModule.values()) {
            enumMap.put(m.getCode(), m.getValue());
        }
        Integer toModuleVal = enumMap.getOrDefault(toModule,-1);
        for (String key : enumMap.keySet()) {
            if (statusMap.containsKey(key) && enumMap.get(key) > toModuleVal) {
                this.logicDeleteById(statusMap.get(key).getId());
            }
        }
    }

    /**
     * 根据报告状态枚举获取对应所处模块
     *
     * @param status 报告状态枚举值
     */
    private String getModule(EnumReportState status) {
        switch (status) {
            case 编制报告中:
            case 报告未通过:
                return EnumLIM.EnumReportModule.报告编制.getCode();

            case 报告审核中:
                return EnumLIM.EnumReportModule.报告审核.getCode();

            case 报告复核中:
                return EnumLIM.EnumReportModule.报告复核.getCode();

            case 报告签发中:
                return EnumLIM.EnumReportModule.报告签发.getCode();

            case 已签发:
                return "";

            default:
                return "";
        }
    }
}