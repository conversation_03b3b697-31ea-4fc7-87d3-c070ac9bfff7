package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.OAInstrumentRepairApply;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;


/**
 * DtoOAInstrumentRepairApply实体
 * <AUTHOR>
 * @version V1.0.0 2020/4/3
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "TB_LIM_OAInstrumentRepairApply")
 @Data
 @DynamicInsert
 public  class DtoOAInstrumentRepairApply extends OAInstrumentRepairApply {
   private static final long serialVersionUID = 1L;
 }