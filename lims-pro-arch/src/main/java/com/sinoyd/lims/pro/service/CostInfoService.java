package com.sinoyd.lims.pro.service;

import com.sinoyd.base.dto.customer.DtoWorkflowSign;
import com.sinoyd.lims.pro.dto.DtoCostInfo;
import com.sinoyd.frame.service.IBaseJpaService;


/**
 * 费用操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface CostInfoService extends IBaseJpaService<DtoCostInfo, String> {
    /**
     * 生成项目的费用
     *
     * @param projectId 项目id
     */
    void generate(String projectId);

    /**
     * 刷新费用信息并返回
     *
     * @param id 费用id
     */
    DtoCostInfo reload(String id);

    /**
     * 工作流操作
     *
     * @param dtoWorkflowSign 实体
     */
    void costInfoSignal(DtoWorkflowSign dtoWorkflowSign);
}