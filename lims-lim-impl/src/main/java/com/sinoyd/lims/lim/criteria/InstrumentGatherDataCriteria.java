package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * 仪器接入数据查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentGatherDataCriteria extends BaseCriteria {


    /**
     * 上传开始时间
     */
    private String startTime;

    /**
     * 上传结束时间
     */
    private String endTime;

    /**
     * 仪器接入id
     */
    private String instrumentGatherId;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 查询关键字，采样编号
     */
    private String sampleCode;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();

        Calendar calendar = new GregorianCalendar();
        //开始时间查询
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and x.uploadTime >= :startTime");
            values.put("startTime", date);
        }
        //结束时间查询
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and x.uploadTime < :endTime");
            values.put("endTime", date);
        }

        if (StringUtil.isNotEmpty(instrumentGatherId)) {
            condition.append(" and x.instrumentGatherId = :instrumentGatherId");
            values.put("instrumentGatherId", instrumentGatherId);
        }
        if (StringUtil.isNotEmpty(dataType)) {
            condition.append(" and x.dataType = :dataType");
            values.put("dataType", dataType);
        }

        if (StringUtil.isNotEmpty(sampleCode)) {
            condition.append(" and exists (select 1 from DtoInstrumentGatherDataDetails d where x.id = d.instrumentGatherDataId and d.params = '采样编号' and d.paramValue like :sampleCode)");
            values.put("sampleCode", "%" + sampleCode + "%");
        }

        return condition.toString();
    }
}