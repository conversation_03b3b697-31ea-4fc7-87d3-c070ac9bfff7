package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.pro.dto.customer.DtoQualityControlDetail;

import java.util.List;

/**
 * 分析质量统计 接口
 * <AUTHOR>
 * @version V1.0.0 2020/02/17
 * @since V100R001
 */
public interface AnalysisQualityStatisticsService {

    /**
     * 分析质量统计
     *
     * @param baseCriteria 查询条件
     */
    void findQualityControlDetailGroupByAnalyseDate(PageBean<DtoQualityControlDetail> pb, BaseCriteria baseCriteria);
}
