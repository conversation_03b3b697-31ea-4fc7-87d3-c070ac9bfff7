package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TestQCRangeCopy查询条件
 * <AUTHOR>
 * @version V1.0.0 2022年6月14日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QualityControlLimitCriteria extends BaseCriteria implements Serializable {
    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 质控类型（所有的）
     */
    private Integer qcType;

    /**
     * 质控等级
     */
    private Integer qcGrade;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotNullAndEmpty(this.testId) && !UUIDHelper.GUID_EMPTY.equals(this.testId)) {
            condition.append(" and testId = :testId ");
            values.put("testId", this.testId);
        }
        if (StringUtil.isNotNull(qcType) && qcType!= -1) {
            condition.append(" and qcType = :qcType");
            values.put("qcType", this.qcType);
        }
        if (StringUtil.isNotNull(qcGrade) && qcGrade!= -1) {
            condition.append(" and qcGrade = :qcGrade");
            values.put("qcGrade", this.qcGrade);
        }
        return condition.toString();
    }
}
