package com.sinoyd.lims.pro.service;

import com.sinoyd.lims.pro.dto.DtoSubmitRecord;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;


/**
 * SubmitRecord操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
public interface SubmitRecordService extends IBaseJpaService<DtoSubmitRecord, String> {
    /**
     * 创建提交记录
     *
     * @param objectId
     * @param objectType
     * @param submitType
     * @param nextPerson
     * @param submitRemark
     * @param from
     * @param to
     */
    void createSubmitRecord(String objectId,
                            Integer objectType,
                            Integer submitType,
                            String nextPerson,
                            String submitRemark,
                            String from,
                            String to);

    /**
     * 批量创建提交记录
     * @param submitRecords 送样单记录
     * @param userId 用户id
     * @param userName 用户名称
     * @param orgId 组织机构id
     */
    void createSubmitRecords(List<DtoSubmitRecord> submitRecords,
                             String userId,
                             String userName,
                             String orgId);
}