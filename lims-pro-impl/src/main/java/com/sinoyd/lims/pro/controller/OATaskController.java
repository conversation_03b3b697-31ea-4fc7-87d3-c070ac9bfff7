package com.sinoyd.lims.pro.controller;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.sinoyd.boot.common.annotation.BusinessLog;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ELogType;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.dto.DtoOATask;
import com.sinoyd.lims.pro.dto.DtoOATaskHandle;
import com.sinoyd.lims.pro.dto.DtoOATaskQuery;
import com.sinoyd.lims.pro.service.OATaskService;

import org.activiti.engine.task.Attachment;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 审批任务信息服务接口定义 
 * <AUTHOR>
 * @version V1.0.0 2019-03-26
 * @since V100R001
 */
@RestController
@RequestMapping("/api/pro/oaTasks")
@Slf4j
@Api(tags = "工作流: 审批任务服务")
public class OATaskController extends BaseJpaController<DtoOATask, String, OATaskService> 
{
    /**
     * 查询待我审批任务
     * @param param 参数对象
     * @return
     */
    @ApiOperation(value = "查询待我审批任务", notes = "查询待我审批任务")
    @GetMapping(path = "/todos")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<List<DtoOATask>> findTodo(DtoOATaskQuery param) {
        PageBean<DtoOATask> page = super.getPageBean();
        page.setPageNo(param.getPageNo());
        page.setRowsPerPage(param.getPageSize());

        if (StringUtil.isEmpty(page.getSort())) {
            page.setSort("submitTime-");
        }

        service.findTodo(page, param);

        return buildPageResponse(page);
    }
    
    /**
     * 查询我已审批任务
     * @param param 参数对象
     * @return
     */
    @ApiOperation(value = "查询我已审批任务", notes = "查询我已审批任务")
    @GetMapping(path = "/finishs")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<List<DtoOATask>> findFinish(DtoOATaskQuery param) {
        PageBean<DtoOATask> page = super.getPageBean();
        page.setPageNo(param.getPageNo());
        page.setRowsPerPage(param.getPageSize());

        if (StringUtil.isEmpty(page.getSort())) {
            page.setSort("submitTime-");
        }

        service.findFinish(page, param);

        return buildPageResponse(page);
    }

    /**
     * 查询我已发起任务
     * @param param 参数对象
     * @return
     */
    @ApiOperation(value = "查询我已发起任务", notes = "查询我已发起任务")
    @GetMapping(path = "/sponsors")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<List<DtoOATask>> findSponsor(DtoOATaskQuery param) {
        PageBean<DtoOATask> page = super.getPageBean();
        page.setPageNo(param.getPageNo());
        page.setRowsPerPage(param.getPageSize());

        if (StringUtil.isEmpty(page.getSort())) {
            page.setSort("submitTime-");
        }

        service.findSponsor(page, param);

        return buildPageResponse(page);
    }
    
    /**
     * 查询所有审批任务
     * @param param 参数对象
     * @return
     */
    @ApiOperation(value = "查询所有审批任务", notes = "查询所有审批任务")
    @GetMapping(path = "/alls")
    @BusinessLog(logType = ELogType.ACCESS)
    public RestResponse<List<DtoOATask>> findAll(DtoOATaskQuery param) {
        PageBean<DtoOATask> page = super.getPageBean();
        page.setPageNo(param.getPageNo());
        page.setRowsPerPage(param.getPageSize());

        if (StringUtil.isEmpty(page.getSort())) {
            page.setSort("submitTime-");
        }

        service.findAll(page, param);

        return buildPageResponse(page);
    }

    /**
     * 构建分页返回对象
     * @param page 分页对象
     * @return
     */
    private RestResponse<List<DtoOATask>> buildPageResponse(PageBean<DtoOATask> page) {
        RestResponse<List<DtoOATask>> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);
        restResp.setData(page.getData());
        restResp.setCount(page.getRowsCount());

        return restResp;
    }

    /**
     * 添加附件
     * @param procInstId 流程实例ID
     * @param actTaskId  工作流任务ID
     * @param files      文件列表
     * @return RestResponse<String>
     */
    @ApiOperation(value = "添加附件", notes = "添加附件")
    @PostMapping(path = "/attachments")
    public RestResponse<String> uploadAttachment(@RequestParam(name = "procInstId") String procInstId, 
                                                 @RequestParam(name = "actTaskId", required = false) String actTaskId,
                                                 List<MultipartFile> files) 
    {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        service.addAttachment(procInstId, actTaskId, files);

        return restResp;
    }

    /**
     * 下载附件
     * @throws IOException 
     */
    @ApiOperation(value = "下载附件", notes = "下载附件")
    @GetMapping(path = "/attachments")
    public void downAttachment(@RequestParam(name = "attachmentId") String attachmentId,
                               HttpServletResponse response)
        throws IOException
    {
        OutputStream outputStream = null;
        InputStream inputStream = null;

        try {
            inputStream = service.getAttachmentContent(attachmentId);
        
            byte[] resBytes = new byte[1024];
            int len = -1;
            
            Attachment attach = service.getAttachment(attachmentId);
            
            response.setHeader("Content-Type", attach.getType());
            response.setHeader("Content-Disposition", "attachment; filename="
                + attach.getName());
            
            outputStream = response.getOutputStream();
            while ( (len = inputStream.read(resBytes, 0, 1024)) != -1) {
                outputStream.write(resBytes, 0, len);
            }
            
            response.flushBuffer();

        } catch (IOException e) {
            log.error("get attachment error: ", e);
        }
        finally {
            if (StringUtil.isNotNull(outputStream)) {
                outputStream.close();
            }
            
            if (StringUtil.isNotNull(inputStream)) {
                inputStream.close();
            }
        }
    }

    /**
     * 办理审批任务
     * @param param 办理参数对象
     * @return
     */
    @ApiOperation(value = "办理审批任务", notes = "办理审批任务")
    @PostMapping(path = "/complete")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> completeTask(@RequestBody DtoOATaskHandle param) 
    {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        service.completeTask(param);

        return restResp;
    }

    /**
     * 撤销审批任务
     * 
     * @param taskId 审批任务标识
     * @param reason 撤销原因
     */
    @ApiOperation(value = "撤销审批任务", notes = "撤销审批任务")
    @PostMapping(path = "/cancel")
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<String> cancelTask(@RequestParam(name = "taskId") String taskId,
                                           @RequestParam(name = "reason") String reason) {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setRestStatus(ERestStatus.SUCCESS);

        service.cancelTask(taskId, reason);

        return restResp;
    }

    /**
     * 删除草稿任务
     *
     * @param ids 任务标识
     */
    @ApiOperation(value = "删除草稿任务", notes = "删除草稿任务")
    @DeleteMapping
    @BusinessLog(logType = ELogType.OPERATE)
    public RestResponse<Integer> deleteDraft(@RequestBody List<String> ids){
        RestResponse<Integer> restResp = new RestResponse<>();
        restResp.setData(service.logicDeleteById(ids));
        restResp.setRestStatus(ERestStatus.SUCCESS);
        return restResp;
    }
}
