package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoReportAchievement2Person;

import java.util.List;


/**
 *  报告绩效repository
 *  <AUTHOR>
 *  @version V1.0.0
 *  @since 2023/03/12
 */
public interface ReportAchievement2PersonRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportAchievement2Person, String> {

    /**
     * 根据人员id查询数据
     * @param personIds 人员id
     * @return List<DtoAnalyseAchievement2Person>
     */
    List<DtoReportAchievement2Person> findByPersonIdIn(List<String> personIds);

}
