ALTER TABLE TB_BASE_SystemConfig ADD COLUMN linkMan varchar(50)  COMMENT '联系人';
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN legalRepresentative varchar(50)  COMMENT '法人代表';
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN establishTime datetime  COMMENT '成立时间';
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN labArea varchar(50)  COMMENT '实验室面积';
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN staffCount int(10)  COMMENT '在职人数';
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN registeredCapital varchar(50)  COMMENT '注册资金';
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN certificate varchar(50)  COMMENT '资质证书';
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN cmaScope  text COMMENT 'CNAS能力范围';
ALTER TABLE TB_BASE_SystemConfig ADD COLUMN cnasScope text COMMENT 'CNAS能力范围';
