package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.customer.DtoTestFormulaParamsConfig;
import com.sinoyd.lims.lim.dto.customer.DtoTestQCRemindTemp;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import com.sinoyd.lims.pro.dto.DtoQualityControl;
import com.sinoyd.lims.pro.dto.DtoQualityControlEvaluate;
import com.sinoyd.lims.pro.dto.customer.DtoAnalyseDataTemp;
import com.sinoyd.lims.pro.dto.customer.DtoQualityControlDetail;
import com.sinoyd.lims.pro.dto.customer.DtoQualityControlTemp;

import java.util.List;
import java.util.Map;


/**
 * QualityControl操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2019/10/25
 * @since V100R001
 */
public interface QualityControlService extends IBaseJpaService<DtoQualityControl, String> {

    /**
     * 计算加标回收率，样值，测定值
     *
     * @param qcId                     质控id
     * @param anaId                    数据id
     * @param formulaId                公式id
     * @param isCalculate              是否重新计算
     * @param analyseDataAllMap        数据集合（主要有加标数据及原样数据）
     * @param testFormulaParamsConfigs 公式参数
     * @return 返回相应的数据
     */
    DtoQualityControl calculateJBValue(String qcId, String anaId, String formulaId,
                                       Boolean isCalculate,
                                       List<Map<String, Object>> analyseDataAllMap,
                                       List<DtoTestFormulaParamsConfig> testFormulaParamsConfigs);

    /**
     * 主要计算加标回收率及增值
     *
     * @param realSampleTestValue 样值
     * @param qcTestValue         质控测定值
     * @param testValue           原样值
     * @param qcValue             质控值
     * @param testId              测试项目id
     * @param qcType              质控类型
     * @return 返回加标回收率
     */
    Map<String, Object> calculateJBValue(String realSampleTestValue, String qcTestValue, String testValue,
                                         String qcValue, String testId, Integer qcType);

    /**
     * 批量计算加标回收率及增值
     *
     * @param qualityControlTempList 样值
     * @return 返回加标回收率
     */
    List<DtoQualityControl> calculateJBValueBatch(List<DtoQualityControlTemp> qualityControlTempList);

    /**
     * 批量修改加标量纲
     *
     * @param qualityControlTempList         样值
     * @param qcVolumeDimensionId            加标体积量纲
     * @param qcValueDimensionId             加入标准量量纲
     * @param qcTestValueDimensionId         测定值量纲
     * @param realSampleTestValueDimensionId 样值量纲
     */
    void updateJBDimensionBatch(List<DtoQualityControlTemp> qualityControlTempList, String qcVolumeDimensionId,
                                String qcValueDimensionId, String qcTestValueDimensionId, String realSampleTestValueDimensionId,
                                String ssConcentrationDimensionId, String constantVolumeDimensionId);


    /**
     * 主要计算加标回收率及增值
     *
     * @param realSampleTestValue 样值
     * @param qcTestValue         质控测定值
     * @param qcValue             质控值
     * @param qcAddedValue        增值
     * @param formulaId           公式id
     * @return 返回加标回收率
     */
    Map<String, Object> calculateJBValue(String realSampleTestValue, String qcTestValue, String qcValue, String qcAddedValue, String formulaId);

    /**
     * 获取替代物加入量，化合物名称，CAS号
     *
     * @param qualityControlTemp
     * @return 返回相应的数据
     */
    DtoQualityControlTemp getReplaceValue(DtoQualityControlTemp qualityControlTemp);

    /**
     * 计算替代样回收率
     *
     * @param testValue 替代样出证结果
     * @param addition  替代样加入量
     * @param formulaId 公式id
     * @return 返回替代回收率
     */
    Map<String, Object> calculateReplaceValue(String testValue, String addition, String formulaId);


    /**
     * 计算校正系数检验样偏差
     *
     * @param testValue 校正系数检验样出证结果
     * @param qcvalue   校正系数检验样校正浓度
     * @param formulaId 公式id
     * @return 返回校正系数检验样偏差
     */
    Map<String, Object> calculateCorrectionFactorValue(String testValue, String qcvalue, String formulaId);

    /**
     * 修改标样信息
     *
     * @param dtoAnalyseDataTemp 待修改的数据
     */
    DtoAnalyseDataTemp updateStandardSampleInfo(DtoAnalyseDataTemp dtoAnalyseDataTemp) throws IllegalAccessException;

    /**
     * 修改标样信息
     *
     * @param map 待修改的数据
     */
    Map<String, Object> updateStandardSampleInfoFromMap(Map<String, Object> map) throws IllegalAccessException;

    /**
     * 修改替代样信息
     *
     * @param dtoQualityControl 待修改的数据
     */
    DtoQualityControl updateReplaceSampleInfo(DtoQualityControl dtoQualityControl);

    /**
     * 修改校正系数检验样信息
     *
     * @param dtoQualityControl 待修改的数据
     */
    DtoQualityControl updateCorrectionFactorSampleInfo(DtoQualityControl dtoQualityControl);

    /**
     * 根据项目id返回质控信息
     *
     * @param projectId 项目id
     * @return 样品数据进度
     */
    List<DtoQualityControlDetail> findQualityControlDetailByProjectId(String projectId);

    List<DtoQualityControlDetail> findQualityControlDetailByProjectIdAndSampleTypeIdList(String projectId, List<String> sampleTypeIds);

    /**
     * 计算分析质控的逻辑
     *
     * @param detail         详细数据
     * @param thisQcDataList 当前的质控数据
     * @param remindMap      质控范围的提醒对象
     * @param testId         测试项目id
     */
    void calculateAnalysisQuality(DtoQualityControlDetail detail,
                                  List<DtoAnalyseDataTemp> thisQcDataList,
                                  Map<String, DtoTestQCRemindTemp> remindMap,
                                  String testId, List<DtoQualityControlEvaluate> qualityControlEvaluateList);

    /**
     * 计算分析质控的逻辑
     *
     * @param detail         详细数据
     * @param thisQcDataList 当前的质控数据
     * @param remindMap      质控范围的提醒对象
     * @param testId         测试项目id
     */
    void calculateAnalysisQuality(DtoQualityControlDetail detail,
                                  List<DtoAnalyseDataTemp> thisQcDataList,
                                  Map<String, DtoTestQCRemindTemp> remindMap,
                                  String testId, List<DtoQualityControlEvaluate> qualityControlEvaluateList, List<String> sampleTypeIds);

    /**
     * 根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是测试项目上的检出限对加标测定值及样值进行比较处理
     *
     * @param oriValue          原始值
     * @param paramsPartFormula 加标测得量公式
     * @param testId            测试项目id
     * @param sampleTypeId      样品类型id
     * @return 判断检出限处理后的数据
     */
    String checkLimitForJb(String oriValue, DtoParamsPartFormula paramsPartFormula, String testId, String sampleTypeId);


    /**
     * 根据加标测得量公式配置的是否使用测试项目的检出限来判断使用公式配置的检出限还是检测单分析数据中配置的检出限对加标测定值及样值进行比较处理
     *
     * @param oriValue          原始值
     * @param paramsPartFormula 加标测得量公式
     * @param examLimitValue     检出限
     * @return 判断检出限处理后的数据
     */
    String checkLimitForJb(String oriValue, DtoParamsPartFormula paramsPartFormula, String examLimitValue);
}