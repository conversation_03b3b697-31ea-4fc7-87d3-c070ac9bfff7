CREATE TABLE TB_QA_PlanInternalAudit
(
    id           varchar(50)   NOT NULL COMMENT '主键',
    annualPlanId varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '年度计划id',
    auditTime    datetime      NOT NULL COMMENT '审核时间',
    attendee     varchar(2000) NOT NULL COMMENT '审核参与人员',
    auditPurp    varchar(2000) NOT NULL COMMENT '审核目的',
    auditScope   varchar(2000) NOT NULL COMMENT '审核范围',
    auditContent varchar(2000) NOT NULL COMMENT '审核内容',
    auditGist    varchar(2000) NOT NULL COMMENT '审核依据',
    status       varchar(50)            DEFAULT NULL COMMENT '状态',
    isDeleted    bit(1)        NOT NULL DEFAULT b'0' COMMENT '是否删除',
    orgId        varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId     varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='内审计划信息';


CREATE TABLE tb_qa_internalauditdividethework
(
    id           varchar(50)   NOT NULL,
    auditTime    datetime      NOT NULL COMMENT '审核时间',
    auditedDept  varchar(100)  NOT NULL DEFAULT '' COMMENT '审核部门',
    auditElement varchar(2000)          DEFAULT '' COMMENT '审核要素',
    auditLeader  varchar(50)   NOT NULL DEFAULT '' COMMENT '内审组长',
    auditor      varchar(2000) NOT NULL DEFAULT '' COMMENT '内审员',
    isDeleted    bit(1)        NOT NULL DEFAULT b'0' COMMENT '是否删除',
    status       varchar(50)            DEFAULT '',
    orgId        varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    creator      varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '创建人',
    createDate   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    domainId     varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    modifier     varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '修改人',
    modifyDate   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    auditPlanId  varchar(50)   NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
    PRIMARY KEY (id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='内审分工';

ALTER TABLE tb_qa_notconformitem
    add column auditDivideTheWorkId varchar(50)  NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '内审分工Id';


