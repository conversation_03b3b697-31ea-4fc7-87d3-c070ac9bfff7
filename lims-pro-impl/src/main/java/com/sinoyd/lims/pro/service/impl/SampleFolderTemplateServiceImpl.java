package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoProjectScheme;
import com.sinoyd.lims.pro.dto.customer.DtoProjectTest;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderItemVo;
import com.sinoyd.lims.pro.dto.customer.DtoSampleFolderTemp;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.NewLogService;
import com.sinoyd.lims.pro.service.ProjectTestService;
import com.sinoyd.lims.pro.service.SampleFolderTemplateService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Collator;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * SampleFolderTemplate操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2023/08/02
 * @since V100R001
 */
@Service
public class SampleFolderTemplateServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoSampleFolderTemplate, String, SampleFolderTemplateRepository> implements SampleFolderTemplateService {

    private SampleFolderRepository sampleFolderRepository;

    private SamplingFrequencyRepository samplingFrequencyRepository;

    private SamplingFrequencyTestRepository samplingFrequencyTestRepository;

    private SamplingFrequencyTempRepository samplingFrequencyTempRepository;

    private SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository;

    private TestService testService;

    private SampleTypeService sampleTypeService;

    private ProjectTestService projectTestService;

    private ProjectApprovalRepository projectApprovalRepository;

    private SampleRepository sampleRepository;

    private NewLogService newLogService;

    private static final Pattern PATTERN = Pattern.compile("\\d+$");


    @Override
    @Transactional
    public void addFolder(DtoSampleFolder dtoSampleFolder) {
        DtoProjectApproval projectApproval = projectApprovalRepository.findOne(dtoSampleFolder.getProjectId());
        List<String> testIds = dtoSampleFolder.getItemVos().stream().map(DtoSampleFolderItemVo::getTestId).collect(Collectors.toList());
        Integer maxTimeCount = dtoSampleFolder.getItemVos().stream().max(Comparator.comparing(DtoSampleFolderItemVo::getTimesOrder)).get().getTimesOrder();
        Integer maxSampleCount = dtoSampleFolder.getItemVos().stream().max(Comparator.comparing(DtoSampleFolderItemVo::getSamplePeriod)).get().getSamplePeriod();
        dtoSampleFolder.setTimePerPeriod(maxTimeCount);
        dtoSampleFolder.setSampleOrder(maxSampleCount);
        List<DtoTest> testList = testService.findRedisByIds(testIds);
        this.addFolder(dtoSampleFolder, testList);
    }

    @Override
    @Transactional
    public void selectSampleFolder(List<String> folderIds, String approveId, Boolean isMarkDelete) {
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findAll(folderIds);
        List<DtoSamplingFrequency> samplingFrequencies = samplingFrequencyRepository.findBySampleFolderIdIn(folderIds);
        List<DtoSamplingFrequencyTest> samplingFrequencyTests = samplingFrequencyTestRepository.findBySampleFolderIdIn(folderIds);
        List<DtoSampleFolderTemplate> sampleFolderTemps = new ArrayList<>();
        List<DtoSamplingFrequencyTemp> frequencyTemps = new ArrayList<>();
        List<DtoSamplingFrequencyTestTemp> frequencyTestTemps = new ArrayList<>();
        for (DtoSampleFolder sampleFolder : sampleFolders) {
            //添加点位temp
            DtoSampleFolderTemplate sampleFolderTemplate = new DtoSampleFolderTemplate();
            BeanUtils.copyProperties(sampleFolder, sampleFolderTemplate, "id");
            sampleFolderTemplate.setSampleFolderId(sampleFolder.getId());
            sampleFolderTemplate.setApproveId(approveId);
            if (isMarkDelete) {
                sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.删除.getValue());
            }
            sampleFolderTemps.add(sampleFolderTemplate);
            List<DtoSamplingFrequency> samplingFrequency2Folder = samplingFrequencies.stream().filter(s -> s.getSampleFolderId().equals(sampleFolder.getId())).collect(Collectors.toList());
            for (DtoSamplingFrequency samplingFrequency : samplingFrequency2Folder) {
                //添加点位周期频次temp
                DtoSamplingFrequencyTemp samplingFrequencyTemp = new DtoSamplingFrequencyTemp();
                BeanUtils.copyProperties(samplingFrequency, samplingFrequencyTemp, "id");
                samplingFrequencyTemp.setSamplingFrequencyId(samplingFrequency.getId());
                samplingFrequencyTemp.setSampleFolderTempId(sampleFolderTemplate.getId());
                if (isMarkDelete) {
                    samplingFrequencyTemp.setOperateType(EnumLIM.EnumOperateType.删除.getValue());
                }
                frequencyTemps.add(samplingFrequencyTemp);
                List<DtoSamplingFrequencyTest> samplingFrequencyTests2Folder = samplingFrequencyTests.stream().filter(s -> s.getSampleFolderId().equals(sampleFolder.getId()) && s.getSamplingFrequencyId().equals(samplingFrequency.getId())).collect(Collectors.toList());
                for (DtoSamplingFrequencyTest dtoSamplingFrequencyTest : samplingFrequencyTests2Folder) {
                    //添加点位周期频次与测试项目关联表temp
                    DtoSamplingFrequencyTestTemp samplingFrequencyTestTemp = new DtoSamplingFrequencyTestTemp();
                    BeanUtils.copyProperties(dtoSamplingFrequencyTest, samplingFrequencyTestTemp, "id");
                    samplingFrequencyTestTemp.setSamplingFrequencyTempId(samplingFrequencyTemp.getId());
                    samplingFrequencyTestTemp.setSamplingFrequencyTestId(dtoSamplingFrequencyTest.getId());
                    if (isMarkDelete) {
                        samplingFrequencyTestTemp.setOperateType(EnumLIM.EnumOperateType.删除.getValue());
                    }
                    frequencyTestTemps.add(samplingFrequencyTestTemp);
                }
            }
        }

        if (StringUtil.isNotEmpty(sampleFolderTemps)) {
            super.save(sampleFolderTemps);
        }
        if (StringUtil.isNotEmpty(frequencyTemps)) {
            samplingFrequencyTempRepository.save(frequencyTemps);
        }
        if (StringUtil.isNotEmpty(frequencyTestTemps)) {
            samplingFrequencyTestTempRepository.save(frequencyTestTemps);
        }
        String comment = "";
        if (!isMarkDelete) {
            comment = String.format("选择了点位%s", sampleFolders.stream().map(DtoSampleFolder::getWatchSpot).collect(Collectors.joining(",")));
        } else {
            comment = String.format("标记删除了点位%s", sampleFolders.stream().map(DtoSampleFolder::getWatchSpot).collect(Collectors.joining(",")));
        }
        newLogService.createLog(approveId, comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public void addSchemePeriodTimes(String sampleFolderTempId, Integer periodCount, Integer timePerPeriod, Integer sampleOrder) {
        DtoSampleFolderTemplate sampleFolderTemplate = repository.findOne(sampleFolderTempId);
        if (!EnumLIM.EnumOperateType.新增.getValue().equals(sampleFolderTemplate.getOperateType())) {
            sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
            repository.save(sampleFolderTemplate);
        }
        List<DtoSamplingFrequencyTemp> samplingFrequencyTempList = samplingFrequencyTempRepository.findBySampleFolderTempIdOrderByPeriodCountDesc(sampleFolderTempId);
        DtoSamplingFrequencyTemp frequencyTemp = StringUtil.isEmpty(samplingFrequencyTempList) ? null : samplingFrequencyTempList.get(0);
        Integer period = StringUtil.isNotNull(frequencyTemp) ? frequencyTemp.getPeriodCount() + 1 : 1;
        List<DtoSamplingFrequencyTemp> frequencyTemps = new ArrayList<>();
        for (Integer i = period; i < period + periodCount; i++) {
            for (Integer j = 1; j <= timePerPeriod; j++) {
                for (Integer k = 1; k <= sampleOrder; k++) {
                    //新增周期频次
                    DtoSamplingFrequencyTemp samplingFrequencyTemp = new DtoSamplingFrequencyTemp();
                    samplingFrequencyTemp.setSampleFolderTempId(sampleFolderTempId);
                    samplingFrequencyTemp.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                    samplingFrequencyTemp.setPeriodCount(i);
                    samplingFrequencyTemp.setTimePerPeriod(j);
                    samplingFrequencyTemp.setSamplePerTime(k);
                    frequencyTemps.add(samplingFrequencyTemp);
                }
            }
        }
        if (StringUtil.isNotEmpty(frequencyTemps)) {
            samplingFrequencyTempRepository.save(frequencyTemps);
        }
        String comment = String.format("增加点位%s周期频次，周期%s，频次%s，样品数%s", sampleFolderTemplate.getWatchSpot(),
                periodCount, timePerPeriod, sampleOrder);
        newLogService.createLog(sampleFolderTemplate.getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(),
                EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(), PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public DtoSampleFolderTemplate update(DtoSampleFolderTemplate entity) {
        DtoSampleFolderTemplate sampleFolderTemplate = repository.findOne(entity.getId());
        StringBuilder log = new StringBuilder();
        if (StringUtil.isNotEmpty(entity.getWatchSpot()) && !entity.getWatchSpot().equals(sampleFolderTemplate.getWatchSpot())) {
            log.append("，点位名称修改为" + entity.getWatchSpot());
        }
        if (StringUtil.isNotEmpty(entity.getLon()) && !entity.getLon().equals(sampleFolderTemplate.getLon())) {
            log.append("，经度修改为" + entity.getLon());
        }
        if (StringUtil.isNotEmpty(entity.getLat()) && !entity.getLat().equals(sampleFolderTemplate.getLat())) {
            log.append("，纬度修改为" + entity.getLat());
        }
        String watchSpot = sampleFolderTemplate.getWatchSpot();
        sampleFolderTemplate.setLon(entity.getLon());
        sampleFolderTemplate.setLat(entity.getLat());
        sampleFolderTemplate.setWatchSpot(entity.getWatchSpot());
        if (!EnumLIM.EnumOperateType.新增.getValue().equals(sampleFolderTemplate.getOperateType())) {
            sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
        }
        if (StringUtil.isNotEmpty(log.toString())) {
            String comment = String.format("更新点位%s信息", watchSpot) + log.toString();
            newLogService.createLog(sampleFolderTemplate.getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
        return super.update(entity);
    }

    @Override
    @Transactional
    public void copySampleFolderTemplate(List<String> ids, Integer copyTimes) {
        List<DtoSampleFolderTemplate> sampleFolderTemplates = repository.findAll(ids);
        List<DtoSamplingFrequencyTemp> samplingFrequencyTemps = samplingFrequencyTempRepository.findBySampleFolderTempIdIn(ids).stream().filter(s -> !EnumLIM.EnumOperateType.删除.getValue().equals(s.getOperateType())).collect(Collectors.toList());
        Map<String, List<DtoSamplingFrequencyTemp>> folderTempId2SamplingFrequencyTemps = samplingFrequencyTemps.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTemp::getSampleFolderTempId));
        List<String> samplingFrequencyTempIds = samplingFrequencyTemps.stream().map(DtoSamplingFrequencyTemp::getId).distinct().collect(Collectors.toList());
        Map<String, List<DtoSamplingFrequencyTestTemp>> frequencyTempId2Tests = StringUtil.isNotEmpty(samplingFrequencyTempIds) ? samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(samplingFrequencyTempIds).stream().filter(s -> !EnumLIM.EnumOperateType.删除.getValue().equals(s.getOperateType())).collect(Collectors.groupingBy(DtoSamplingFrequencyTestTemp::getSamplingFrequencyTempId)) : new HashMap<>();
        List<DtoSampleFolderTemplate> newSampleFoldeTemplates = new ArrayList<>();
        List<DtoSamplingFrequencyTemp> newSamplingFrequencyTemps = new ArrayList<>();
        List<DtoSamplingFrequencyTestTemp> newSamplingFrequencyTestTemps = new ArrayList<>();
        for (Integer i = 1; i <= copyTimes; i++) {
            for (DtoSampleFolderTemplate sampleFolderTemplate : sampleFolderTemplates) {
                DtoSampleFolderTemplate newSampleFolderTemplate = new DtoSampleFolderTemplate();
                BeanUtils.copyProperties(sampleFolderTemplate, newSampleFolderTemplate, "id");
                Object[] watchSpotArr = this.getStrSplit(sampleFolderTemplate.getWatchSpot());
                if (watchSpotArr.length == 2) {
                    newSampleFolderTemplate.setWatchSpot(String.format("%s%d", String.valueOf(watchSpotArr[0]), Integer.valueOf(String.valueOf(watchSpotArr[1])) + i));
                }
                newSampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                newSampleFolderTemplate.setApproveId(sampleFolderTemplate.getApproveId());
                newSampleFoldeTemplates.add(newSampleFolderTemplate);
                if (StringUtil.isNotNull(folderTempId2SamplingFrequencyTemps.get(sampleFolderTemplate.getId()))) {
                    for (DtoSamplingFrequencyTemp samplingFrequencyTemp : folderTempId2SamplingFrequencyTemps.get(sampleFolderTemplate.getId())) {
                        DtoSamplingFrequencyTemp newSamplingFrequencyTemp = new DtoSamplingFrequencyTemp();
                        BeanUtils.copyProperties(samplingFrequencyTemp, newSamplingFrequencyTemp, "id");
                        newSamplingFrequencyTemp.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                        newSamplingFrequencyTemp.setSampleFolderTempId(newSampleFolderTemplate.getId());
                        newSamplingFrequencyTemps.add(newSamplingFrequencyTemp);
                        if (StringUtil.isNotNull(frequencyTempId2Tests.get(samplingFrequencyTemp.getId()))) {
                            for (DtoSamplingFrequencyTestTemp samplingFrequencyTestTemp : frequencyTempId2Tests.get(samplingFrequencyTemp.getId())) {
                                DtoSamplingFrequencyTestTemp newSamplingFrequencyTestTemp = new DtoSamplingFrequencyTestTemp();
                                BeanUtils.copyProperties(samplingFrequencyTestTemp, newSamplingFrequencyTestTemp, "id");
                                newSamplingFrequencyTestTemp.setSamplingFrequencyTempId(newSamplingFrequencyTemp.getId());
                                newSamplingFrequencyTestTemp.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                                newSamplingFrequencyTestTemps.add(newSamplingFrequencyTestTemp);
                            }
                        }
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(newSampleFoldeTemplates)) {
            repository.save(newSampleFoldeTemplates);
        }
        if (StringUtil.isNotEmpty(newSamplingFrequencyTemps)) {
            samplingFrequencyTempRepository.save(newSamplingFrequencyTemps);
        }
        if (StringUtil.isNotEmpty(newSamplingFrequencyTestTemps)) {
            samplingFrequencyTestTempRepository.save(newSamplingFrequencyTestTemps);
        }
        String comment = String.format("复制点位%s，复制次数%s", sampleFolderTemplates.stream().map(DtoSampleFolderTemplate::getWatchSpot).collect(Collectors.joining(",")), copyTimes);
        newLogService.createLog(sampleFolderTemplates.get(0).getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public void markDeleteFolderTemp(List<String> folderIds, String approveId) {
        List<DtoSampleFolderTemplate> sampleFolderTemplates = repository.findAll(folderIds);
        List<String> frequencyIds = samplingFrequencyTempRepository.findBySampleFolderTempIdIn(folderIds).stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        frequencyIds.forEach(f -> markDeleteFrequencyTemp(f));
        List<String> delIds = new ArrayList<>();
        List<DtoSampleFolderTemplate> saveList = new ArrayList<>();
        for (DtoSampleFolderTemplate sampleFolderTemplate : sampleFolderTemplates) {
            if (EnumLIM.EnumOperateType.新增.getValue().equals(sampleFolderTemplate.getOperateType())) {
                delIds.add(sampleFolderTemplate.getId());
            } else {
                sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.删除.getValue());
                saveList.add(sampleFolderTemplate);
            }
        }
        if (StringUtil.isNotEmpty(delIds)) {
            repository.logicDeleteById(delIds);
        }
        if (StringUtil.isNotEmpty(saveList)) {
            repository.save(saveList);
        }
        String comment = String.format("标记删除了点位%s", sampleFolderTemplates.stream().map(DtoSampleFolderTemplate::getWatchSpot).collect(Collectors.joining(",")));
        newLogService.createLog(approveId, comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
//        selectSampleFolder(folderIds, approveId, true);
    }

    @Override
    @Transactional
    public void addTimes(String sampleFolderTempId, Integer periodCount, Integer timePerPeriod) {
        DtoSampleFolderTemplate sampleFolderTemplate = repository.findOne(sampleFolderTempId);
        if (!EnumLIM.EnumOperateType.新增.getValue().equals(sampleFolderTemplate.getOperateType())) {
            sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
            repository.save(sampleFolderTemplate);
        }
        List<DtoSamplingFrequencyTemp> samplingFrequencyTempList = samplingFrequencyTempRepository.findBySampleFolderTempIdAndPeriodCountOrderByTimePerPeriodDesc(sampleFolderTempId, periodCount);
        DtoSamplingFrequencyTemp frequencyTemp = StringUtil.isEmpty(samplingFrequencyTempList) ? null : samplingFrequencyTempList.get(0);
        Integer times = StringUtil.isNotNull(frequencyTemp) ? frequencyTemp.getTimePerPeriod() + 1 : 1;
        Integer sampleCount = StringUtil.isNotNull(frequencyTemp) ? frequencyTemp.getSamplePerTime() : 1;
        List<DtoSamplingFrequencyTemp> frequencyTemps = new ArrayList<>();
        for (Integer i = times; i < times + timePerPeriod; i++) {
            for (Integer j = 1; j <= sampleCount; j++) {
                //新增周期频次
                DtoSamplingFrequencyTemp samplingFrequencyTemp = new DtoSamplingFrequencyTemp();
                samplingFrequencyTemp.setSampleFolderTempId(sampleFolderTempId);
                samplingFrequencyTemp.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                samplingFrequencyTemp.setPeriodCount(periodCount);
                samplingFrequencyTemp.setTimePerPeriod(i);
                samplingFrequencyTemp.setSamplePerTime(j);
                frequencyTemps.add(samplingFrequencyTemp);
            }
        }
        if (StringUtil.isNotEmpty(frequencyTemps)) {
            samplingFrequencyTempRepository.save(frequencyTemps);
        }
        String comment = String.format("新增点位%s第%s周期，次数%s", sampleFolderTemplate.getWatchSpot(), periodCount, timePerPeriod);
        newLogService.createLog(sampleFolderTemplate.getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(),
                EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(), PrincipalContextUser.getPrincipal().getUserId(),
                PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public void copyPeriod(String sampleFolderTempId, Integer periodCount, Integer copyTimes) {
        DtoSampleFolderTemplate sampleFolderTemplate = repository.findOne(sampleFolderTempId);
        if (!EnumLIM.EnumOperateType.新增.getValue().equals(sampleFolderTemplate.getOperateType())) {
            sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
            repository.save(sampleFolderTemplate);
        }
        List<DtoSamplingFrequencyTemp> samplingFrequencyTemps = samplingFrequencyTempRepository
                .findBySampleFolderTempIdAndPeriodCount(sampleFolderTempId, periodCount).stream()
                .filter(s -> !EnumLIM.EnumOperateType.删除.getValue().equals(s.getOperateType())).collect(Collectors.toList());
        List<String> samplingFrequencyTempIds = samplingFrequencyTemps.stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTestTemp> samplingFrequencyTestTemps = StringUtil.isNotEmpty(samplingFrequencyTempIds) ?
                samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(samplingFrequencyTempIds).stream()
                        .filter(s -> !EnumLIM.EnumOperateType.删除.getValue().equals(s.getOperateType())).collect(Collectors.toList()) : new ArrayList<>();
        List<DtoSamplingFrequencyTemp> samplingFrequencyList = samplingFrequencyTempRepository.findBySampleFolderTempIdOrderByPeriodCountDesc(sampleFolderTempId);
        DtoSamplingFrequencyTemp maxPeriodFrequency = StringUtil.isEmpty(samplingFrequencyList) ? null : samplingFrequencyList.get(0);
        List<DtoSamplingFrequencyTemp> newFrequencyTemps = new ArrayList<>();
        List<DtoSamplingFrequencyTestTemp> newFrequencyTestTemps = new ArrayList<>();
        for (Integer i = 1; i <= copyTimes; i++) {
            for (DtoSamplingFrequencyTemp sf : samplingFrequencyTemps) {
                DtoSamplingFrequencyTemp targetSamplingFrequency = new DtoSamplingFrequencyTemp();
                BeanUtils.copyProperties(sf, targetSamplingFrequency, "id");
                targetSamplingFrequency.setSampleFolderTempId(sampleFolderTempId);
                targetSamplingFrequency.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                targetSamplingFrequency.setPeriodCount(maxPeriodFrequency.getPeriodCount() + i);
                targetSamplingFrequency.setTimePerPeriod(sf.getTimePerPeriod());
                targetSamplingFrequency.setSamplePerTime(sf.getSamplePerTime());
                newFrequencyTemps.add(targetSamplingFrequency);
                List<DtoSamplingFrequencyTestTemp> sfts = samplingFrequencyTestTemps.stream().filter(s -> s.getSamplingFrequencyTempId().equals(sf.getId())).collect(Collectors.toList());
                for (DtoSamplingFrequencyTestTemp sft : sfts) {
                    DtoSamplingFrequencyTestTemp newSft = new DtoSamplingFrequencyTestTemp();
                    BeanUtils.copyProperties(sft, newSft, "id");
                    newSft.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                    newSft.setSamplingFrequencyTempId(targetSamplingFrequency.getId());
                    newSft.setSamplingFrequencyTestId(UUIDHelper.GUID_EMPTY);
                    newFrequencyTestTemps.add(newSft);
                }
            }
        }
        if (StringUtil.isNotEmpty(newFrequencyTemps)) {
            samplingFrequencyTempRepository.save(newFrequencyTemps);
        }
        if (StringUtil.isNotEmpty(newFrequencyTestTemps)) {
            samplingFrequencyTestTempRepository.save(newFrequencyTestTemps);
        }
        String comment = String.format("复制点位%s第%s周期，复制次数%s", sampleFolderTemplate.getWatchSpot(), periodCount, copyTimes);
        newLogService.createLog(sampleFolderTemplate.getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public void deletePeriod(String sampleFolderId, Integer periodCount) {
        List<DtoSamplingFrequencyTemp> frequencyList = samplingFrequencyTempRepository.findBySampleFolderTempIdAndPeriodCount(sampleFolderId, periodCount);
        List<String> frequencyIds = frequencyList.stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTestTemp> frequencyTestTemps = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(frequencyIds);
        DtoSampleFolderTemplate sampleFolderTemplate = repository.findOne(sampleFolderId);
        if (!EnumLIM.EnumOperateType.新增.getValue().equals(sampleFolderTemplate.getOperateType())) {
            sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
            repository.save(sampleFolderTemplate);
        }
        List<DtoSamplingFrequencyTemp> frequencyTemps = new ArrayList<>();
        List<String> delIds = new ArrayList<>();
        for (DtoSamplingFrequencyTemp frequency : frequencyList) {
            if (!EnumLIM.EnumOperateType.新增.getValue().equals(frequency.getOperateType())) {
                frequency.setOperateType(EnumLIM.EnumOperateType.删除.getValue());
                frequencyTemps.add(frequency);
            } else {
                delIds.add(frequency.getId());
            }
        }
        if (StringUtil.isNotEmpty(delIds)) {
            samplingFrequencyTempRepository.logicDeleteById(delIds);
        }
        if (StringUtil.isNotEmpty(frequencyTemps)) {
            samplingFrequencyTempRepository.save(frequencyTemps);
        }
        doSaveTest(frequencyTestTemps);
        String comment = String.format("删除点位%s第%s周期", sampleFolderTemplate.getWatchSpot(), periodCount);
        newLogService.createLog(sampleFolderTemplate.getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public void copyTimes(String sampleFolderId, String samplingFrequencyId, Integer copyTimes) {
        DtoSampleFolderTemplate sampleFolderTemplate = repository.findOne(sampleFolderId);
        if (!EnumLIM.EnumOperateType.新增.getValue().equals(sampleFolderTemplate.getOperateType())) {
            sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
            repository.save(sampleFolderTemplate);
        }
        List<DtoSamplingFrequencyTemp> newFrequencyTemps = new ArrayList<>();
        List<DtoSamplingFrequencyTestTemp> newFrequencyTestTemps = new ArrayList<>();
        DtoSamplingFrequencyTemp frequencyTemp = samplingFrequencyTempRepository.findOne(samplingFrequencyId);
        List<DtoSamplingFrequencyTestTemp> sfts = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(Arrays.asList(samplingFrequencyId)).stream().filter(s -> !EnumLIM.EnumOperateType.删除.getValue().equals(s.getOperateType())).collect(Collectors.toList());
        List<DtoSamplingFrequencyTemp> samplingFrequencyList = samplingFrequencyTempRepository.findBySampleFolderTempIdAndPeriodCountOrderByTimePerPeriodDesc(
                sampleFolderId,
                frequencyTemp.getPeriodCount());
        DtoSamplingFrequencyTemp maxFrequency = StringUtil.isEmpty(samplingFrequencyList) ? null : samplingFrequencyList.get(0);
        for (Integer i = 1; i <= copyTimes; i++) {
            DtoSamplingFrequencyTemp targetSamplingFrequency = new DtoSamplingFrequencyTemp();
            BeanUtils.copyProperties(frequencyTemp, targetSamplingFrequency, "id");
            targetSamplingFrequency.setSampleFolderTempId(sampleFolderId);
            targetSamplingFrequency.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
            targetSamplingFrequency.setPeriodCount(frequencyTemp.getPeriodCount());
            targetSamplingFrequency.setTimePerPeriod(maxFrequency.getTimePerPeriod() + i);
            newFrequencyTemps.add(targetSamplingFrequency);
            for (DtoSamplingFrequencyTestTemp sft : sfts) {
                DtoSamplingFrequencyTestTemp newSft = new DtoSamplingFrequencyTestTemp();
                BeanUtils.copyProperties(sft, newSft, "id");
                newSft.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                newSft.setSamplingFrequencyTempId(targetSamplingFrequency.getId());
                newSft.setSamplingFrequencyTestId(UUIDHelper.GUID_EMPTY);
                newFrequencyTestTemps.add(newSft);
            }
        }
        if (StringUtil.isNotEmpty(newFrequencyTemps)) {
            samplingFrequencyTempRepository.save(newFrequencyTemps);
        }
        if (StringUtil.isNotEmpty(newFrequencyTestTemps)) {
            samplingFrequencyTestTempRepository.save(newFrequencyTestTemps);
        }
        String comment = String.format("复制点位%s第%s周期第%s次，复制次数%s", sampleFolderTemplate.getWatchSpot(), frequencyTemp.getPeriodCount(), frequencyTemp.getTimePerPeriod(), copyTimes);
        newLogService.createLog(sampleFolderTemplate.getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public void batchMarkDeleteFrequencyTemp(List<String> frequencyTempIds) {
        frequencyTempIds.forEach(i -> markDeleteFrequencyTemp(i));
    }

    @Override
    @Transactional
    public void markDeleteFrequencyTemp(String frequencyTempId) {
        DtoSamplingFrequencyTemp frequencyTemp = samplingFrequencyTempRepository.findOne(frequencyTempId);
        DtoSampleFolderTemplate sampleFolderTemplate = repository.findOne(frequencyTemp.getSampleFolderTempId());
        if (!EnumLIM.EnumOperateType.新增.getValue().equals(sampleFolderTemplate.getOperateType())) {
            sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
            repository.save(sampleFolderTemplate);
        }
        if (EnumLIM.EnumOperateType.新增.getValue().equals(frequencyTemp.getOperateType())) {
            samplingFrequencyTempRepository.delete(frequencyTempId);
        } else {
            frequencyTemp.setOperateType(EnumLIM.EnumOperateType.删除.getValue());
            samplingFrequencyTempRepository.save(frequencyTemp);
            List<DtoSamplingFrequencyTestTemp> frequencyTestTemps = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(Arrays.asList(frequencyTempId));
            List<String> deleteFrequencyTestTempIds = new ArrayList<>();
            List<DtoSamplingFrequencyTestTemp> updateFrequencyTestTemps = new ArrayList<>();
            for (DtoSamplingFrequencyTestTemp frequencyTestTemp : frequencyTestTemps) {
                if (EnumLIM.EnumOperateType.新增.getValue().equals(frequencyTemp.getOperateType())) {
                    deleteFrequencyTestTempIds.add(frequencyTestTemp.getId());
                } else {
                    frequencyTestTemp.setOperateType(EnumLIM.EnumOperateType.删除.getValue());
                    updateFrequencyTestTemps.add(frequencyTestTemp);
                }
            }
            if (StringUtil.isNotEmpty(deleteFrequencyTestTempIds)) {
                samplingFrequencyTestTempRepository.logicDeleteById(deleteFrequencyTestTempIds);
            }
            if (StringUtil.isNotEmpty(updateFrequencyTestTemps)) {
                samplingFrequencyTestTempRepository.save(updateFrequencyTestTemps);
            }
        }
        String comment = String.format("删除点位%s第%s周期第%s次", sampleFolderTemplate.getWatchSpot(), frequencyTemp.getPeriodCount(), frequencyTemp.getTimePerPeriod());
        newLogService.createLog(sampleFolderTemplate.getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public void cancelFolderTemplate(List<String> folderTempIds) {
        if (StringUtil.isNotEmpty(folderTempIds)) {
            List<DtoSampleFolderTemplate> sampleFolderTemplates = repository.findAll(folderTempIds);
            List<DtoSamplingFrequencyTemp> frequencyTemps = samplingFrequencyTempRepository.findBySampleFolderTempIdIn(folderTempIds);
            List<String> frequencyTempIds = frequencyTemps.stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
            repository.logicDeleteById(folderTempIds);
            samplingFrequencyTempRepository.deleteBySampleFolderTempIdIn(folderTempIds);
            samplingFrequencyTestTempRepository.deleteBySamplingFrequencyTempIdIn(frequencyTempIds);
            String comment = String.format("取消点位%s", sampleFolderTemplates.stream().map(DtoSampleFolderTemplate::getWatchSpot).collect(Collectors.joining(",")));
            newLogService.createLog(sampleFolderTemplates.get(0).getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.修改点位.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
    }

    @Override
    public DtoProjectScheme findScheme(String approveId) {
        DtoProjectScheme scheme = new DtoProjectScheme();
        List<DtoSampleFolderTemplate> sampleFolderTemplates = repository.findByApproveId(approveId);
        List<String> sampleFolderTemplateIds = sampleFolderTemplates.stream().map(DtoSampleFolderTemplate::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTemp> frequencyTemps = StringUtil.isNotEmpty(sampleFolderTemplateIds) ?
                samplingFrequencyTempRepository.findBySampleFolderTempIdIn(sampleFolderTemplateIds) : new ArrayList<>();
        List<String> delFrequencyTempIds = frequencyTemps.stream().filter(s -> EnumLIM.EnumOperateType.删除.getValue().equals(s.getOperateType()))
                .map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        List<String> frequencyTempIds = frequencyTemps.stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTestTemp> frequencyTestTemps = StringUtil.isNotEmpty(frequencyTempIds) ? samplingFrequencyTestTempRepository
                .findBySamplingFrequencyTempIdIn(frequencyTempIds).stream().filter(s -> !EnumLIM.EnumOperateType.删除.getValue().equals(s.getOperateType())
                        || delFrequencyTempIds.contains(s.getSamplingFrequencyTempId())).collect(Collectors.toList()) : new ArrayList<>();
        List<String> testIds = frequencyTestTemps.stream().map(DtoSamplingFrequencyTestTemp::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = StringUtil.isNotEmpty(testIds) ? testService.findRedisByIds(testIds) : new ArrayList<>();
        List<String> sampleTypeIds = sampleFolderTemplates.stream().map(DtoSampleFolderTemplate::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleTypeIds);
        Map<String, DtoSampleType> samTypeMap = samTypes.stream().collect(Collectors.toMap(DtoSampleType::getId, samType -> samType));
        List<DtoSampleFolderTemp> sampleFolders = new ArrayList<>();
        if (StringUtil.isNotEmpty(sampleFolderTemplates)) {
            sampleFolders = this.getFolderSchemes(approveId, sampleFolderTemplates, frequencyTemps, frequencyTestTemps, testList, samTypeMap);
        }
        scheme.setSampleFolder(sampleFolders);
        return scheme;
    }

    public List<DtoSampleFolderTemp> getFolderSchemes(String projectId,
                                                      List<DtoSampleFolderTemplate> folderList,
                                                      List<DtoSamplingFrequencyTemp> frequencyList,
                                                      List<DtoSamplingFrequencyTestTemp> sftList,
                                                      List<DtoTest> tests,
                                                      Map<String, DtoSampleType> samTypeMap) {
        Map<String, List<DtoSamplingFrequencyTemp>> frequencyMap = frequencyList.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTemp::getSampleFolderTempId));
        Map<String, DtoTest> testMap = tests.stream().collect(Collectors.toMap(DtoTest::getId, test -> test));

        Map<String, List<DtoSamplingFrequencyTestTemp>> sftMap = sftList.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTestTemp::getSamplingFrequencyTempId));

        //获取点位中的样品
        List<String> sampleFolderIds = folderList.parallelStream().map(DtoSampleFolderTemplate::getSampleFolderId).distinct().collect(Collectors.toList());
        List<DtoSample> samples = sampleRepository.findBySampleFolderIdInOrderById(sampleFolderIds);
        //遍历点位获取点位方案信息
        List<DtoSampleFolderTemp> sampleFolders = new ArrayList<>();

        for (DtoSampleFolderTemplate folder : folderList) {
            DtoSampleFolderTemp temp = this.getFolderScheme(projectId, folder, samTypeMap, frequencyMap, sftMap, testMap, samples, sftList);
            String customerId = UUIDHelper.GUID_EMPTY;

            temp.setCustomerId(customerId);
            sampleFolders.add(temp);
        }
        //获取中文排序工具
        Collator collator = Collator.getInstance();
        //先按照检测大类排序，再按照检测小类名称排序，再按照排序值倒序，再按照点位名称
        sampleFolders.sort(Comparator.comparing(DtoSampleFolderTemp::getCustomerId)
                .thenComparing(DtoSampleFolderTemp::getBigSampleTypeId)
                .thenComparing(DtoSampleFolderTemp::getSampleTypeName, collator)
                .thenComparing(DtoSampleFolderTemp::getOrderNum, Comparator.reverseOrder())
                .thenComparing(DtoSampleFolderTemp::getWatchSpot, collator));
        return sampleFolders;
    }

    @Override
    public List<DtoSampleFolder> getFolderList(String approveId, String sampleTypeId, String watchSpot) {
        List<DtoSampleFolderTemplate> sampleFolderTemplates = repository.findByApproveId(approveId);
        List<String> existsFolderIds = sampleFolderTemplates.stream().map(DtoSampleFolderTemplate::getSampleFolderId).collect(Collectors.toList());
        DtoProjectApproval projectApproval = projectApprovalRepository.findOne(approveId);
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findByProjectId(projectApproval.getProjectId()).stream().filter(s -> !existsFolderIds.contains(s.getId())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sampleTypeId) && !UUIDHelper.GUID_EMPTY.equals(sampleTypeId)) {
            sampleFolders.removeIf(s -> !sampleTypeId.equals(s.getSampleTypeId()));
        }
        if (StringUtil.isNotEmpty(watchSpot)) {
            sampleFolders.removeIf(s -> !s.getWatchSpot().contains(watchSpot));
        }
        Collator collator = Collator.getInstance();
        List<String> sampleTypeIds = sampleFolders.stream().map(DtoSampleFolder::getSampleTypeId).distinct().collect(Collectors.toList());
        List<DtoSampleType> sampleTypes = StringUtil.isNotEmpty(sampleTypeIds) ? sampleTypeService.findAll(sampleTypeIds) : new ArrayList<>();
        for (DtoSampleFolder folder : sampleFolders) {
            sampleTypes.stream().filter(s -> s.getId().equals(folder.getSampleTypeId())).findFirst().ifPresent(s -> folder.setSampleTypeName(s.getTypeName()));
        }
        sampleFolders.sort(Comparator.comparing(DtoSampleFolder::getSampleTypeName, collator).thenComparing(DtoSampleFolder::getWatchSpot, collator));
        return sampleFolders;
    }

    /**
     * 新增点位
     *
     * @param sampleFolder 点位
     * @param testList     测试项目
     * @return 返回该点位的方案
     */
    @Transactional
    public void addFolder(DtoSampleFolder sampleFolder, List<DtoTest> testList) {
        List<DtoTest> addtestList = new ArrayList<>();
        if (StringUtil.isNotEmpty(testList) && testList.size() > 0) {
            addtestList = testList;
        } else {
            addtestList = this.getDefaultTest(sampleFolder.getProjectId(), sampleFolder.getSampleTypeId(), sampleFolder.getAnalyseItemIds());
        }
        DtoSampleFolderTemplate sampleFolderTemplate = new DtoSampleFolderTemplate();
        BeanUtils.copyProperties(sampleFolder, sampleFolderTemplate, "id");
        sampleFolderTemplate.setApproveId(sampleFolder.getProjectId());
        sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
        repository.save(sampleFolderTemplate);
        List<DtoSamplingFrequencyTemp> frequencyTemps = new ArrayList<>();
        List<DtoSamplingFrequencyTestTemp> frequencyTestTemps = new ArrayList<>();
        //新增样品周期频次
        for (Integer i = 1; i <= sampleFolder.getPeriodCount(); i++) {
            for (Integer j = 1; j <= sampleFolder.getTimePerPeriod(); j++) {
                DtoSamplingFrequencyTemp frequency = new DtoSamplingFrequencyTemp();
                frequency.setPeriodCount(i);
                frequency.setTimePerPeriod(j);
                frequency.setSampleFolderTempId(sampleFolderTemplate.getId());
                frequency.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                frequencyTemps.add(frequency);
                for (DtoTest test : addtestList) {
                    DtoSamplingFrequencyTestTemp sft = new DtoSamplingFrequencyTestTemp();
                    sft.setTestId(test.getId());
                    sft.setAnalyseItemId(test.getAnalyzeItemId());
                    sft.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                    sft.setAnalyzeMethodId(test.getAnalyzeMethodId());
                    sft.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                    sft.setRedCountryStandard(test.getRedCountryStandard());
                    sft.setIsCompleteField(test.getIsCompleteField());
                    sft.setIsOutsourcing(test.getIsOutsourcing());
                    sft.setIsSamplingOut(Boolean.FALSE);
                    sft.setSamplingFrequencyTempId(frequency.getId());
                    sft.setIsSamplingOut(false);
                    sft.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                    frequencyTestTemps.add(sft);
                }
            }
        }
        if (StringUtil.isNotEmpty(frequencyTemps)) {
            samplingFrequencyTempRepository.save(frequencyTemps);
        }
        if (StringUtil.isNotEmpty(frequencyTestTemps)) {
            samplingFrequencyTestTempRepository.save(frequencyTestTemps);
        }
        String comment = String.format("增加了点位%s", sampleFolder.getWatchSpot());
        newLogService.createLog(sampleFolder.getProjectId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加点位.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Override
    @Transactional
    public void sub(List<String> samplingFrequencyIds, List<DtoProjectTest> projectTests) {
        if (StringUtil.isNotNull(samplingFrequencyIds) && samplingFrequencyIds.size() > 0 &&
                StringUtil.isNotNull(projectTests) && projectTests.size() > 0) {
            List<DtoSamplingFrequencyTemp> frequencyList = samplingFrequencyTempRepository.findAll(samplingFrequencyIds);
            List<DtoSampleFolderTemplate> sampleFolderList = repository.findAll(frequencyList.stream().map(DtoSamplingFrequencyTemp::getSampleFolderTempId).distinct().collect(Collectors.toList()));
            List<DtoSampleType> samTypes = sampleTypeService.findRedisByIds(sampleFolderList.stream().map(DtoSampleFolderTemplate::getSampleTypeId).distinct().collect(Collectors.toList()));
            doSaveSampleFolderTemplate(frequencyList);
            frequencyList.sort(Comparator.comparing(DtoSamplingFrequencyTemp::getSampleFolderTempId).thenComparing(DtoSamplingFrequencyTemp::getPeriodCount).thenComparing(DtoSamplingFrequencyTemp::getTimePerPeriod));
            List<String> subAnalyseItemIds = projectTests.stream().filter(DtoProjectTest::getIsOutsourcing).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList());
            List<String> subNotAnalyseItemIds = projectTests.stream().filter(p -> !p.getIsOutsourcing()
                    && !p.getIsSamplingOut()).map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList());
            List<String> subSamplingAnalyseItemIds = projectTests.stream().filter(DtoProjectTest::getIsSamplingOut)
                    .map(DtoProjectTest::getAnalyseItemId).collect(Collectors.toList());
            List<DtoSamplingFrequencyTestTemp> sftList = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(samplingFrequencyIds);
            List<DtoSamplingFrequencyTestTemp> changeSftList = new ArrayList<>();
            List<String> allNotSubAnalyseItemIds = new ArrayList<>(subNotAnalyseItemIds);
            allNotSubAnalyseItemIds.addAll(subSamplingAnalyseItemIds);
            List<String> allNotSamlingAnalyseItemIds = new ArrayList<>(subNotAnalyseItemIds);
            allNotSamlingAnalyseItemIds.addAll(subAnalyseItemIds);
            for (DtoSamplingFrequencyTestTemp sft : sftList) {
                if ((sft.getIsOutsourcing() && allNotSubAnalyseItemIds.contains(sft.getAnalyseItemId()))
                        || ((!sft.getIsOutsourcing() && !allNotSubAnalyseItemIds.contains(sft.getAnalyseItemId())))) {
                    sft.setIsOutsourcing(!sft.getIsOutsourcing());
                    if (!EnumLIM.EnumOperateType.新增.getValue().equals(sft.getOperateType())) {
                        sft.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
                    }
                    changeSftList.add(sft);
                } else if ((sft.getIsSamplingOut() && allNotSamlingAnalyseItemIds.contains(sft.getAnalyseItemId()))
                        || ((!sft.getIsSamplingOut() && !allNotSamlingAnalyseItemIds.contains(sft.getAnalyseItemId())))) {
                    sft.setIsSamplingOut(!sft.getIsSamplingOut());
                    if (!EnumLIM.EnumOperateType.新增.getValue().equals(sft.getOperateType())) {
                        sft.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
                    }
                    changeSftList.add(sft);
                }
            }
            if (StringUtil.isNotEmpty(changeSftList)) {
                samplingFrequencyTestTempRepository.save(changeSftList);
                if (changeSftList.stream().anyMatch(DtoSamplingFrequencyTestTemp::getIsOutsourcing)) {
                    subToLog(frequencyList, changeSftList, sampleFolderList, samTypes, "采测分包");
                }
                if (changeSftList.stream().anyMatch(DtoSamplingFrequencyTestTemp::getIsSamplingOut)) {
                    subToLog(frequencyList, changeSftList, sampleFolderList, samTypes, "分析分包");
                }
                if (changeSftList.stream().anyMatch(p -> !p.getIsOutsourcing())
                        && changeSftList.stream().anyMatch(p -> !p.getIsSamplingOut())) {
                    subToLog(frequencyList, changeSftList, sampleFolderList, samTypes, "取消分包");
                }
            }
        }
    }

    @Override
    @Transactional
    public void addFrequencyAnalyseItems(List<String> samplingFrequencyIds, List<String> analyseItemIds, List<String> testIds) {
        List<DtoTest> testList = new ArrayList<>();
        List<String> itemIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(testIds)) {
            testList = testService.findRedisByIds(testIds);
            itemIds = testList.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
            analyseItemIds.addAll(itemIds);
            analyseItemIds = analyseItemIds.stream().distinct().collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(samplingFrequencyIds) && samplingFrequencyIds.size() > 0 &&
                StringUtil.isNotNull(analyseItemIds) && analyseItemIds.size() > 0) {
            List<String> redFolderNames = new ArrayList<>();
            List<DtoSamplingFrequencyTemp> frequencyList = samplingFrequencyTempRepository.findAll(samplingFrequencyIds);
            frequencyList.sort(Comparator.comparing(DtoSamplingFrequencyTemp::getSampleFolderTempId).thenComparing(DtoSamplingFrequencyTemp::getPeriodCount).thenComparing(DtoSamplingFrequencyTemp::getTimePerPeriod));
            doSaveSampleFolderTemplate(frequencyList);
            List<DtoSampleFolderTemplate> sampleFolderList = repository.findAll(frequencyList.stream().map(DtoSamplingFrequencyTemp::getSampleFolderTempId).distinct().collect(Collectors.toList()));
            DtoSampleType samType = sampleTypeService.findOne(sampleFolderList.get(0).getSampleTypeId());
            for (DtoSamplingFrequencyTemp frequency : frequencyList) {
                redFolderNames.add(String.format("%s(%s)第%d周期第%d次", sampleFolderList.stream().filter(p -> p.getId().equals(frequency.getSampleFolderTempId())).map(DtoSampleFolderTemplate::getWatchSpot).findFirst().orElse(""),
                        samType.getTypeName(), frequency.getPeriodCount(), frequency.getTimePerPeriod()));
            }
            DtoProjectApproval projectApproval = projectApprovalRepository.findOne(sampleFolderList.get(0).getApproveId());
            List<DtoTest> tests = new ArrayList<>();
            if (StringUtil.isNotEmpty(testList)) {
                tests = testList;
                analyseItemIds.removeIf(itemIds::contains);
                tests.addAll(this.getDefaultTest(projectApproval.getProjectId(), sampleFolderList.get(0).getSampleTypeId(), analyseItemIds));
            } else {
                tests = this.getDefaultTest(projectApproval.getProjectId(), sampleFolderList.get(0).getSampleTypeId(), analyseItemIds);
            }

            List<DtoSamplingFrequencyTestTemp> frequencyTests = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(samplingFrequencyIds);
            List<DtoSamplingFrequencyTestTemp> newSftList = new ArrayList<>();
            for (DtoSamplingFrequencyTemp frequency : frequencyList) {
                for (DtoTest test : tests) {
                    if (frequencyTests.stream().noneMatch(p -> p.getAnalyseItemId().equals(test.getAnalyzeItemId()) && p.getSamplingFrequencyTempId().equals(frequency.getId()))) {
                        DtoSamplingFrequencyTestTemp newFrequencyTest = new DtoSamplingFrequencyTestTemp();
                        newFrequencyTest.setTestId(test.getId());
                        newFrequencyTest.setAnalyseItemId(test.getAnalyzeItemId());
                        newFrequencyTest.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                        newFrequencyTest.setAnalyzeMethodId(test.getAnalyzeMethodId());
                        newFrequencyTest.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                        newFrequencyTest.setRedCountryStandard(test.getRedCountryStandard());
                        newFrequencyTest.setIsCompleteField(test.getIsCompleteField());
                        newFrequencyTest.setIsOutsourcing(test.getIsOutsourcing());
                        //默认分析分包为false -- 后续如果要改到配置再说
                        newFrequencyTest.setIsSamplingOut(Boolean.FALSE);
                        newFrequencyTest.setSamplingFrequencyTempId(frequency.getId());
                        newFrequencyTest.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                        newSftList.add(newFrequencyTest);
                    }
                }
            }
            samplingFrequencyTestTempRepository.save(newSftList);
            String comment = String.format("增加了指标：点位%s增加了指标%s。", String.join(",", redFolderNames),
                    String.join("、", tests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList())));
            newLogService.createLog(sampleFolderList.get(0).getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加检测项目.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
    }

    @Override
    @Transactional
    public void deleteFrequencyAnalyseItems(List<String> frequencyIds, List<String> analyseItemIds) {
        if (StringUtil.isNotNull(frequencyIds) && frequencyIds.size() > 0 &&
                StringUtil.isNotNull(analyseItemIds) && analyseItemIds.size() > 0) {
            frequencyIds.removeIf(UUIDHelper.GUID_EMPTY::equals);
            List<DtoSamplingFrequencyTemp> frequencyTemps = samplingFrequencyTempRepository.findAll(frequencyIds);
            doSaveSampleFolderTemplate(frequencyTemps);
            List<DtoSamplingFrequencyTestTemp> sftList = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(frequencyIds);
            sftList = sftList.stream().filter(p -> analyseItemIds.contains(p.getAnalyseItemId())).collect(Collectors.toList());
            List<DtoTest> tests = testService.findRedisByIds(sftList.stream().map(DtoSamplingFrequencyTestTemp::getTestId).distinct().collect(Collectors.toList()));
            doSaveTest(sftList);
            List<DtoSampleFolderTemplate> sampleFolderTemplates = repository.findAll(frequencyTemps.stream().map(DtoSamplingFrequencyTemp::getSampleFolderTempId).distinct().collect(Collectors.toList()));
            String redFolderNames = sampleFolderTemplates.stream().map(DtoSampleFolderTemplate::getWatchSpot).collect(Collectors.joining(","));
            String comment = String.format("删除了指标：点位%s删除了指标%s。", String.join(",", redFolderNames),
                    String.join("、", tests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.toList())));
            newLogService.createLog(sampleFolderTemplates.get(0).getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.删除检测项目.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
        }
    }

    @Override
    @Transactional
    public void modifySamplingFrequencyTest(String samplingFrequencyId, List<String> analyseItemIds, List<String> testIds) {
        //获取点位频次
        DtoSamplingFrequencyTemp frequency = samplingFrequencyTempRepository.findOne(samplingFrequencyId);
        doSaveSampleFolderTemplate(Collections.singletonList(frequency));
        //获取所有的测试项目，用于填充每个样品的测试项目
        List<DtoTest> testListOfIds = new ArrayList<>();
        List<String> itemIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(testIds)) {
            testListOfIds = testService.findRedisByIds(testIds);
            itemIds = testListOfIds.stream().map(DtoTest::getAnalyzeItemId).distinct().collect(Collectors.toList());
            analyseItemIds.addAll(itemIds);
            analyseItemIds = analyseItemIds.stream().distinct().collect(Collectors.toList());
        }
        //查询出对应频次下的所有指标信息
        List<DtoSamplingFrequencyTestTemp> sftList = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(Collections.singletonList(samplingFrequencyId))
                .stream().filter(s -> !EnumLIM.EnumOperateType.删除.getValue().equals(s.getOperateType())).collect(Collectors.toList());
        //将不包含在修改后指标的次数筛选出来，予以移除
        List<String> finalAnalyseItemIds = analyseItemIds;
        List<DtoSamplingFrequencyTestTemp> removeList = sftList.stream().filter(p -> !finalAnalyseItemIds.contains(p.getAnalyseItemId())).collect(Collectors.toList());
        //筛选出原先存在的指标集合
        List<String> existAnalyseItemIds = sftList.stream().map(DtoSamplingFrequencyTestTemp::getAnalyseItemId).distinct().collect(Collectors.toList());
        //修改后的指标移除原先的指标，保留下来的为需新增的指标
        analyseItemIds.removeAll(existAnalyseItemIds);

        //获取原来指标中的测试项目id集合，并获取测试项目信息
        List<String> testIdsOfFrequency = sftList.stream().map(DtoSamplingFrequencyTestTemp::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = testService.findRedisByIds(testIdsOfFrequency);

        if (removeList.size() > 0) {
            //删除频次指标
            doSaveTest(removeList);
        }
        if (analyseItemIds.size() > 0) {
            //存在新增指标则需进行添加
            DtoSampleFolderTemplate sampleFolder = repository.findOne(frequency.getSampleFolderTempId());
            DtoSampleType sampleType = sampleTypeService.findOne(sampleFolder.getSampleTypeId());
            DtoProjectApproval projectApproval = projectApprovalRepository.findOne(sampleFolder.getApproveId());
            List<DtoTest> tests;
            if (StringUtil.isNotEmpty(testListOfIds)) {
                tests = testListOfIds;
                analyseItemIds.removeIf(itemIds::contains);
                tests.addAll(this.getDefaultTest(projectApproval.getProjectId(), sampleFolder.getSampleTypeId(), analyseItemIds));
            } else {
                tests = this.getDefaultTest(projectApproval.getProjectId(), sampleFolder.getSampleTypeId(), analyseItemIds);
            }
            List<DtoSamplingFrequencyTestTemp> frequencyTestList = new ArrayList<>();
            for (DtoTest test : tests) {
                if (sftList.stream().noneMatch(p -> p.getAnalyseItemId().equals(test.getAnalyzeItemId()) && p.getSamplingFrequencyTempId().equals(frequency.getId()))) {
                    DtoSamplingFrequencyTestTemp newFrequencyTest = new DtoSamplingFrequencyTestTemp();
                    newFrequencyTest.setTestId(test.getId());
                    newFrequencyTest.setAnalyseItemId(test.getAnalyzeItemId());
                    newFrequencyTest.setRedAnalyzeItemName(test.getRedAnalyzeItemName());
                    newFrequencyTest.setAnalyzeMethodId(test.getAnalyzeMethodId());
                    newFrequencyTest.setRedAnalyzeMethodName(test.getRedAnalyzeMethodName());
                    newFrequencyTest.setRedCountryStandard(test.getRedCountryStandard());
                    newFrequencyTest.setIsCompleteField(test.getIsCompleteField());
                    newFrequencyTest.setIsOutsourcing(test.getIsOutsourcing());
                    newFrequencyTest.setIsSamplingOut(Boolean.FALSE);
                    newFrequencyTest.setSamplingFrequencyTempId(frequency.getId());
                    newFrequencyTest.setOperateType(EnumLIM.EnumOperateType.新增.getValue());
                    frequencyTestList.add(newFrequencyTest);
                }
            }
            if (StringUtil.isNotEmpty(frequencyTestList)) {
                samplingFrequencyTestTempRepository.save(frequencyTestList);
            }
            String comment = String.format("增加了指标：点位%s(%s)第%d周期第%d次第%d个样增加了指标%s。", sampleFolder.getWatchSpot(),
                    sampleType.getTypeName(), frequency.getPeriodCount(), frequency.getTimePerPeriod(), frequency.getSamplePerTime(),
                    tests.stream().map(DtoTest::getRedAnalyzeItemName).distinct().collect(Collectors.joining("、")));
            newLogService.createLog(sampleFolder.getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(),
                    EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.增加检测项目.toString(),
                    PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(),
                    UUIDHelper.GUID_EMPTY, "");
        }
    }

    @Transactional
    public void doSaveSampleFolderTemplate(List<DtoSamplingFrequencyTemp> frequencyList) {
        List<String> sampleFolderTempIds = frequencyList.stream().map(DtoSamplingFrequencyTemp::getSampleFolderTempId).collect(Collectors.toList());
        List<DtoSampleFolderTemplate> sampleFolderTemplates = repository.findAll(sampleFolderTempIds);
        List<DtoSampleFolderTemplate> saveList = new ArrayList<>();
        for (DtoSampleFolderTemplate sampleFolderTemplate : sampleFolderTemplates) {
            if (!EnumLIM.EnumOperateType.新增.getValue().equals(sampleFolderTemplate.getOperateType())) {
                sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
                saveList.add(sampleFolderTemplate);
            }
        }
        List<DtoSamplingFrequencyTemp> frequencyTemps = new ArrayList<>();
        for (DtoSamplingFrequencyTemp frequency : frequencyList) {
            if (!EnumLIM.EnumOperateType.新增.getValue().equals(frequency.getOperateType())) {
                frequency.setOperateType(EnumLIM.EnumOperateType.修改.getValue());
                frequencyTemps.add(frequency);
            }
        }
        if (StringUtil.isNotEmpty(saveList)) {
            repository.save(saveList);
        }
        if (StringUtil.isNotEmpty(frequencyTemps)) {
            samplingFrequencyTempRepository.save(frequencyTemps);
        }
    }

    @Transactional
    public void doSaveTest(List<DtoSamplingFrequencyTestTemp> sftList) {
        List<String> deletedIds = new ArrayList<>();
        List<DtoSamplingFrequencyTestTemp> saveList = new ArrayList<>();
        for (DtoSamplingFrequencyTestTemp sft : sftList) {
            if (EnumLIM.EnumOperateType.新增.getValue().equals(sft.getOperateType())) {
                deletedIds.add(sft.getId());
            } else {
                sft.setOperateType(EnumLIM.EnumOperateType.删除.getValue());
                saveList.add(sft);
            }
        }
        if (StringUtil.isNotEmpty(deletedIds)) {
            samplingFrequencyTestTempRepository.logicDeleteById(deletedIds);
        }
        if (StringUtil.isNotEmpty(saveList)) {
            samplingFrequencyTestTempRepository.save(saveList);
        }
    }

    @Override
    @Transactional
    public void cancelDeleteFolders(List<String> folderIds) {
        List<DtoSampleFolderTemplate> sampleFolderTemplates = repository.findAll(folderIds);
        List<DtoSamplingFrequencyTemp> samplingFrequencyTemps = samplingFrequencyTempRepository.findBySampleFolderTempIdIn(folderIds);
        List<String> frequencyTempIds = samplingFrequencyTemps.stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTestTemp> samplingFrequencyTestTemps = StringUtil.isNotEmpty(frequencyTempIds) ? samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(frequencyTempIds) : new ArrayList<>();
        sampleFolderTemplates.forEach(s -> s.setOperateType(EnumLIM.EnumOperateType.无修改.getValue()));
        samplingFrequencyTemps.forEach(s -> s.setOperateType(EnumLIM.EnumOperateType.无修改.getValue()));
        samplingFrequencyTestTemps.forEach(s -> s.setOperateType(EnumLIM.EnumOperateType.无修改.getValue()));
        repository.save(sampleFolderTemplates);
        samplingFrequencyTempRepository.save(samplingFrequencyTemps);
        samplingFrequencyTestTempRepository.save(samplingFrequencyTestTemps);
    }

    @Override
    @Transactional
    public void cancelDeleteFrequency(List<String> frequencyIds) {
        List<DtoSamplingFrequencyTemp> samplingFrequencyTemps = samplingFrequencyTempRepository.findAll(frequencyIds);
        List<DtoSamplingFrequencyTestTemp> samplingFrequencyTestTemps = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(frequencyIds);
        samplingFrequencyTemps.forEach(s -> s.setOperateType(EnumLIM.EnumOperateType.无修改.getValue()));
        samplingFrequencyTestTemps.forEach(s -> s.setOperateType(EnumLIM.EnumOperateType.无修改.getValue()));
        samplingFrequencyTempRepository.save(samplingFrequencyTemps);
        samplingFrequencyTestTempRepository.save(samplingFrequencyTestTemps);
    }


    @Override
    @Transactional
    public void restoreFolders(List<String> folderIds) {
        List<DtoSampleFolderTemplate> sampleFolderTemplates = repository.findAll(folderIds);
        List<DtoSamplingFrequencyTemp> samplingFrequencyTemps = samplingFrequencyTempRepository.findBySampleFolderTempIdIn(folderIds);
        List<String> frequencyTempIds = samplingFrequencyTemps.stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        List<DtoSamplingFrequencyTestTemp> samplingFrequencyTestTemps = StringUtil.isNotEmpty(frequencyTempIds) ? samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(frequencyTempIds) : new ArrayList<>();
        List<String> sampleFolderIds = sampleFolderTemplates.stream().map(DtoSampleFolderTemplate::getSampleFolderId).collect(Collectors.toList());
        List<DtoSampleFolder> sampleFolders = sampleFolderRepository.findAll(sampleFolderIds);
        List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository.findBySampleFolderIdIn(sampleFolderIds);
        List<DtoSamplingFrequencyTest> samplingFrequencyTestList = samplingFrequencyTestRepository.findBySampleFolderIdIn(sampleFolderIds);
        List<DtoSampleFolderTemplate> saveList = new ArrayList<>();
        List<String> delFrequencyTempIds = new ArrayList<>();
        List<DtoSamplingFrequencyTemp> saveFrequencyTempList = new ArrayList<>();
        List<String> delFrequencyTestTempIds = new ArrayList<>();
        List<DtoSamplingFrequencyTestTemp> saveFrequencyTestTempList = new ArrayList<>();
        for (DtoSampleFolderTemplate sampleFolderTemplate : sampleFolderTemplates) {
            DtoSampleFolder sampleFolder = sampleFolders.stream().filter(s -> s.getId().equals(sampleFolderTemplate.getSampleFolderId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(sampleFolder)) {
                BeanUtils.copyProperties(sampleFolder, sampleFolderTemplate, "id", "sampleFolderId", "approveId", "operateType");
                sampleFolderTemplate.setOperateType(EnumLIM.EnumOperateType.无修改.getValue());
                saveList.add(sampleFolderTemplate);
            }
        }
        for (DtoSamplingFrequencyTemp samplingFrequencyTemp : samplingFrequencyTemps) {
            DtoSamplingFrequency samplingFrequency = samplingFrequencyList.stream().filter(s -> s.getId().equals(samplingFrequencyTemp.getSamplingFrequencyId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(samplingFrequency)) {
                samplingFrequencyTemp.setOperateType(EnumLIM.EnumOperateType.无修改.getValue());
                saveFrequencyTempList.add(samplingFrequencyTemp);
            } else {
                delFrequencyTempIds.add(samplingFrequencyTemp.getId());
            }
        }
        for (DtoSamplingFrequencyTestTemp samplingFrequencyTestTemp : samplingFrequencyTestTemps) {
            DtoSamplingFrequencyTest samplingFrequencyTest = samplingFrequencyTestList.stream().filter(s -> s.getId().equals(samplingFrequencyTestTemp.getSamplingFrequencyTestId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(samplingFrequencyTest)) {
                BeanUtils.copyProperties(samplingFrequencyTest, samplingFrequencyTestTemp, "id", "samplingFrequencyTempId", "samplingFrequencyTestId", "operateType");
                samplingFrequencyTestTemp.setOperateType(EnumLIM.EnumOperateType.无修改.getValue());
                saveFrequencyTestTempList.add(samplingFrequencyTestTemp);
            } else {
                delFrequencyTestTempIds.add(samplingFrequencyTestTemp.getId());
            }
        }
        if (StringUtil.isNotEmpty(saveList)) {
            repository.save(saveList);
        }
        if (StringUtil.isNotEmpty(saveFrequencyTempList)) {
            samplingFrequencyTempRepository.save(saveFrequencyTempList);
        }
        if (StringUtil.isNotEmpty(saveFrequencyTestTempList)) {
            samplingFrequencyTestTempRepository.save(saveFrequencyTestTempList);
        }
        if (StringUtil.isNotEmpty(delFrequencyTempIds)) {
            samplingFrequencyTempRepository.logicDeleteById(delFrequencyTempIds);
        }
        if (StringUtil.isNotEmpty(delFrequencyTestTempIds)) {
            samplingFrequencyTestTempRepository.logicDeleteById(delFrequencyTestTempIds);
        }
    }

    @Override
    @Transactional
    public void restoreFrequency(List<String> frequencyIds) {
        List<DtoSamplingFrequencyTemp> samplingFrequencyTemps = samplingFrequencyTempRepository.findAll(frequencyIds);
        List<DtoSamplingFrequencyTestTemp> samplingFrequencyTestTemps = samplingFrequencyTestTempRepository.findBySamplingFrequencyTempIdIn(frequencyIds);
        List<String> samplingFrequencyIds = samplingFrequencyTemps.stream().map(DtoSamplingFrequencyTemp::getSamplingFrequencyId).collect(Collectors.toList());
        List<DtoSamplingFrequency> samplingFrequencyList = samplingFrequencyRepository.findAll(samplingFrequencyIds);
        List<DtoSamplingFrequencyTest> samplingFrequencyTestList = samplingFrequencyTestRepository.findBySamplingFrequencyIdIn(samplingFrequencyIds);
        List<String> delFrequencyTempIds = new ArrayList<>();
        List<DtoSamplingFrequencyTemp> saveFrequencyTempList = new ArrayList<>();
        List<String> delFrequencyTestTempIds = new ArrayList<>();
        List<DtoSamplingFrequencyTestTemp> saveFrequencyTestTempList = new ArrayList<>();
        for (DtoSamplingFrequencyTemp samplingFrequencyTemp : samplingFrequencyTemps) {
            DtoSamplingFrequency samplingFrequency = samplingFrequencyList.stream().filter(s -> s.getId().equals(samplingFrequencyTemp.getSamplingFrequencyId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(samplingFrequency)) {
                samplingFrequencyTemp.setOperateType(EnumLIM.EnumOperateType.无修改.getValue());
                saveFrequencyTempList.add(samplingFrequencyTemp);
            } else {
                delFrequencyTempIds.add(samplingFrequencyTemp.getId());
            }
        }
        for (DtoSamplingFrequencyTestTemp samplingFrequencyTestTemp : samplingFrequencyTestTemps) {
            DtoSamplingFrequencyTest samplingFrequencyTest = samplingFrequencyTestList.stream().filter(s -> s.getId().equals(samplingFrequencyTestTemp.getSamplingFrequencyTestId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(samplingFrequencyTest)) {
                BeanUtils.copyProperties(samplingFrequencyTest, samplingFrequencyTestTemp, "id", "samplingFrequencyTempId", "samplingFrequencyTestId", "operateType");
                samplingFrequencyTestTemp.setOperateType(EnumLIM.EnumOperateType.无修改.getValue());
                saveFrequencyTestTempList.add(samplingFrequencyTestTemp);
            } else {
                delFrequencyTestTempIds.add(samplingFrequencyTestTemp.getId());
            }
        }
        if (StringUtil.isNotEmpty(saveFrequencyTempList)) {
            samplingFrequencyTempRepository.save(saveFrequencyTempList);
        }
        if (StringUtil.isNotEmpty(saveFrequencyTestTempList)) {
            samplingFrequencyTestTempRepository.save(saveFrequencyTestTempList);
        }
        if (StringUtil.isNotEmpty(delFrequencyTempIds)) {
            samplingFrequencyTempRepository.logicDeleteById(delFrequencyTempIds);
        }
        if (StringUtil.isNotEmpty(delFrequencyTestTempIds)) {
            samplingFrequencyTestTempRepository.logicDeleteById(delFrequencyTestTempIds);
        }
    }

    /**
     * 获取点位方案
     *
     * @param projectId    项目id
     * @param folder       点位
     * @param samTypeMap   检测类型map
     * @param frequencyMap 频次map
     * @param sftMap       频次指标map
     * @param testMap      测试项目map
     * @param sftList      所有的频次指标
     * @return 返回点位方案
     */
    private DtoSampleFolderTemp getFolderScheme(String projectId, DtoSampleFolderTemplate folder,
                                                Map<String, DtoSampleType> samTypeMap,
                                                Map<String, List<DtoSamplingFrequencyTemp>> frequencyMap,
                                                Map<String, List<DtoSamplingFrequencyTestTemp>> sftMap,
                                                Map<String, DtoTest> testMap, List<DtoSample> allSamples,
                                                List<DtoSamplingFrequencyTestTemp> sftList) {
        DtoSampleFolderTemp temp = new DtoSampleFolderTemp();
        temp.setId(folder.getId());
        temp.setSampleFolderId(folder.getSampleFolderId());
        temp.setDepth(1);
        temp.setSampleTypeId(folder.getSampleTypeId());
        temp.setCustomerName(EnumLIM.EnumOperateType.getName(folder.getOperateType()));
        temp.setLat(folder.getLat());
        temp.setLon(folder.getLon());
//        temp.setFixedPointId(folder.getFixedPointId());
        if (samTypeMap.containsKey(temp.getSampleTypeId())) {
            DtoSampleType samType = samTypeMap.get(temp.getSampleTypeId());
            temp.setBigSampleTypeId(samType.getParentId());
            temp.setSampleTypeName(samType.getTypeName());
            temp.setWatchSpot(folder.getWatchSpot());
            List<DtoSampleFolderTemp> children = this.getFolderChildren(projectId, temp, frequencyMap, sftMap, testMap, allSamples);
            temp.setChildren(children);
            //写入点位的周期、频次及样品数
            this.setFolderNumInfo(temp, children);
            temp.setLabel(temp.getWatchSpot());
            //写入点位的指标信息
            this.setFolderTest(temp, sftList, children);
        }
        return temp;
    }

    /**
     * 获取点位的周期次数方案
     *
     * @param projectId    项目id
     * @param folderTemp   点位方案
     * @param frequencyMap 频次map
     * @param sftMap       频次指标map
     * @param testMap      测试项目map
     * @return 返回周期次数方案
     */
    private List<DtoSampleFolderTemp> getFolderChildren(String projectId, DtoSampleFolderTemp folderTemp,
                                                        Map<String, List<DtoSamplingFrequencyTemp>> frequencyMap,
                                                        Map<String, List<DtoSamplingFrequencyTestTemp>> sftMap,
                                                        Map<String, DtoTest> testMap, List<DtoSample> allSamples) {
        List<DtoSampleFolderTemp> tempList = new ArrayList<>();
        if (frequencyMap.containsKey(folderTemp.getId())) {
            List<DtoSamplingFrequencyTemp> frequencys = frequencyMap.get(folderTemp.getId());
            Map<Integer, List<DtoSamplingFrequencyTemp>> periodMap = frequencys.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTemp::getPeriodCount));
            List<DtoSample> sampleList = allSamples.stream().filter(p -> p.getSampleFolderId().equals(folderTemp.getId()) &&
                    (!p.getIsDeleted() || EnumPRO.EnumSampleStatus.样品作废.name().equals(p.getStatus()))).collect(Collectors.toList());
            for (Integer period : periodMap.keySet()) {
                DtoSampleFolderTemp temp = new DtoSampleFolderTemp();
                temp.setId(String.format("%s_%d", folderTemp.getId(), period));
                temp.setSampleFolderId(folderTemp.getId());
                temp.setDepth(2);
                temp.setPeriodCount(period);
                temp.setSampleTypeId(folderTemp.getSampleTypeId());
                temp.setBigSampleTypeId(folderTemp.getBigSampleTypeId());
                temp.setSampleTypeName(folderTemp.getSampleTypeName());
                temp.setWatchSpot(folderTemp.getWatchSpot());
                temp.setLabel(String.format("第%d周期", period));
                temp.setCustomerId(folderTemp.getId());
                List<DtoSamplingFrequencyTemp> times = periodMap.get(period).stream()
                        .sorted(Comparator.comparing(DtoSamplingFrequencyTemp::getTimePerPeriod)).collect(Collectors.toList());
                Map<Integer,List<DtoSamplingFrequencyTemp>> timePeriodMap = times.stream().collect(Collectors.groupingBy(DtoSamplingFrequencyTemp::getTimePerPeriod));
                List<DtoSampleFolderTemp> childTemps = new ArrayList<>();
                for (Integer timePeriod : timePeriodMap.keySet()) {
                    DtoSampleFolderTemp timeTemp = new DtoSampleFolderTemp();
                    timeTemp.setId(String.format("%s_%d_%d", folderTemp.getId(), period,timePeriod));
                    timeTemp.setSampleFolderId(folderTemp.getId());
                    timeTemp.setDepth(3);
                    timeTemp.setPeriodCount(period);
                    timeTemp.setTimePerPeriod(timePeriod);
                    timeTemp.setSampleTypeId(folderTemp.getSampleTypeId());
                    timeTemp.setBigSampleTypeId(folderTemp.getBigSampleTypeId());
                    timeTemp.setSampleTypeName(folderTemp.getSampleTypeName());
                    timeTemp.setWatchSpot(folderTemp.getWatchSpot());
                    timeTemp.setLabel(String.format("第%d批次", timePeriod));
                    List<DtoSampleFolderTemp> childSamplePeriodTemps = new ArrayList<>();
                    for(DtoSamplingFrequencyTemp sampleTemp : timePeriodMap.get(timePeriod)) {
                        DtoSampleFolderTemp samplePeriodTemp = new DtoSampleFolderTemp();
                        samplePeriodTemp.setId(sampleTemp.getId());
                        samplePeriodTemp.setSampleFolderId(folderTemp.getId());
                        samplePeriodTemp.setDepth(4);
                        samplePeriodTemp.setPeriodCount(period);
                        samplePeriodTemp.setTimePerPeriod(timePeriod);
                        samplePeriodTemp.setSamplePeriod(sampleTemp.getSamplePerTime());
                        samplePeriodTemp.setSampleTypeId(folderTemp.getSampleTypeId());
                        samplePeriodTemp.setBigSampleTypeId(folderTemp.getBigSampleTypeId());
                        samplePeriodTemp.setSampleTypeName(folderTemp.getSampleTypeName());
                        samplePeriodTemp.setWatchSpot(folderTemp.getWatchSpot());
                        samplePeriodTemp.setCustomerName(EnumLIM.EnumOperateType.getName(sampleTemp.getOperateType()));
                        DtoSample sample = sampleList.stream().filter(p -> sampleTemp.getSamplePerTime().equals(p.getSampleOrder())
                                && period.equals(p.getCycleOrder()) && timePeriod.equals(p.getTimesOrder())).findFirst().orElse(null);
                        samplePeriodTemp.setLabel(String.format("第%d个样", sampleTemp.getSamplePerTime()));
                        if (StringUtil.isNotNull(sample)) {
                            if (StringUtil.isNotEmpty(sample.getCode())) {
                                samplePeriodTemp.setLabel(String.format("第%d个样（%s）", sampleTemp.getSamplePerTime(), sample.getCode()));
                            }
                            if (EnumPRO.EnumSampleStatus.样品作废.name().equals(sample.getStatus())) {
                                samplePeriodTemp.setLabel(String.format("%s%s", samplePeriodTemp.getLabel(), "【作废】"));
                            }
                        }
                        Map<String, DtoProjectTest> projectTestMap = new HashMap<>();
                        List<String> outsourcingItemIds = new ArrayList<>();
                        List<String> samplingOutItemIds = new ArrayList<>();
                        if (sftMap.containsKey(sampleTemp.getId())) {
                            for (DtoSamplingFrequencyTestTemp sft : sftMap.get(sampleTemp.getId())) {
                                DtoProjectTest pt = new DtoProjectTest();
                                pt.setTestId(sft.getTestId());
                                pt.setAnalyseItemId(sft.getAnalyseItemId());
                                pt.setAnalyzeMethodId(sft.getAnalyzeMethodId());
                                pt.setRedAnalyzeItemName(sft.getRedAnalyzeItemName());
                                pt.setRedAnalyzeMethodName(sft.getRedAnalyzeMethodName());
                                pt.setRedCountryStandard(sft.getRedCountryStandard());
                                pt.setIsOutsourcing(sft.getIsOutsourcing());
                                pt.setIsSamplingOut(sft.getIsSamplingOut());
                                pt.setIsCompleteField(sft.getIsCompleteField());
                                pt.setProjectId(projectId);
                                pt.setSampleTypeId(folderTemp.getSampleTypeId());
                                pt.setBigSampleTypeId(folderTemp.getBigSampleTypeId());
                                pt.setSampleTypeName(folderTemp.getSampleTypeName());
                                if (testMap.containsKey(sft.getTestId())) {
                                    DtoTest test = testMap.get(sft.getTestId());
                                    pt.setOrderNum(test.getOrderNum());
                                }
                                projectTestMap.put(sft.getAnalyseItemId(), pt);
                                samplePeriodTemp.getItemIds().add(sft.getAnalyseItemId());
                                if (sft.getIsOutsourcing()) {
                                    outsourcingItemIds.add(sft.getAnalyseItemId());
                                }
                                if (sft.getIsSamplingOut()) {
                                    samplingOutItemIds.add(sft.getAnalyseItemId());
                                }
                            }
                        }
                        samplePeriodTemp.setItem(projectTestMap);
                        samplePeriodTemp.setOutsourcingItemIds(outsourcingItemIds);
                        samplePeriodTemp.setSamplingOutItemIds(samplingOutItemIds);
                        childSamplePeriodTemps.add(samplePeriodTemp);
                    }
                    timeTemp.setChildren(childSamplePeriodTemps);
                    childTemps.add(timeTemp);
                }
                temp.setChildren(childTemps);
                tempList.add(temp);
            }
            tempList.sort(Comparator.comparing(DtoSampleFolderTemp::getPeriodCount));
        }
        return tempList;
    }

    /**
     * 写入第一层点位的频次、周期、样品数
     *
     * @param temp     第一层的点位信息
     * @param children 子方案信息列表
     */
    private void setFolderNumInfo(DtoSampleFolderTemp temp, List<DtoSampleFolderTemp> children) {
        if (children.size() > 0) {
            Integer maxPeriod = children.get(children.size() - 1).getPeriodCount();
            temp.setPeriodCount(maxPeriod);
            List<Integer> timeList = new ArrayList<>();
            children.forEach(p -> {
                if (p.getChildren().size() > 0) {
                    timeList.add(p.getChildren().get(p.getChildren().size() - 1).getTimePerPeriod());
                }
            });
            Integer sampleNum = timeList.stream().reduce(0, (sum, item) -> sum + item);
            temp.setSampleNum(sampleNum);
            Integer maxTime = Collections.max(timeList);
            temp.setTimePerPeriod(maxTime);
        } else {
            temp.setPeriodCount(0);
            temp.setTimePerPeriod(0);
            temp.setSampleNum(0);
        }
    }

    /**
     * 写入第一层点位的指标信息
     *
     * @param temp     第一层的点位信息
     * @param sftList  频次指标信息
     * @param children 子方案信息列表
     */
    private void setFolderTest(DtoSampleFolderTemp temp, List<DtoSamplingFrequencyTestTemp> sftList, List<DtoSampleFolderTemp> children) {
        Map<String, DtoProjectTest> projectTestMap = new HashMap<>();
        List<String> frequencyTempIds = samplingFrequencyTempRepository.findBySampleFolderTempIdIn(Arrays.asList(temp.getId())).stream().map(DtoSamplingFrequencyTemp::getId).collect(Collectors.toList());
        Integer folderTestCount = sftList.stream().filter(p -> frequencyTempIds.contains(p.getSamplingFrequencyTempId())).map(DtoSamplingFrequencyTestTemp::getTestId).collect(Collectors.toSet()).size();
        List<String> outsourcingItemIds = new ArrayList<>();
        List<String> samplingOutItemIds = new ArrayList<>();
        for (DtoSampleFolderTemp periodTemp : children) {
            for (DtoSampleFolderTemp timeTemp : periodTemp.getChildren()) {
                for (String analyseItemId : timeTemp.getItem().keySet()) {
                    if (!projectTestMap.containsKey(analyseItemId)) {
                        projectTestMap.put(analyseItemId, timeTemp.getItem().get(analyseItemId));
                        if (timeTemp.getItem().get(analyseItemId).getIsOutsourcing()) {
                            outsourcingItemIds.add(analyseItemId);
                        } else if (timeTemp.getItem().get(analyseItemId).getIsSamplingOut()) {
                            samplingOutItemIds.add(analyseItemId);
                        }
                        temp.getScheme().put(analyseItemId, timeTemp.getItem().get(analyseItemId).getMapInfo());
                    }
                }
                if (folderTestCount.equals(projectTestMap.size())) {
                    break;
                }
            }
            if (folderTestCount.equals(projectTestMap.size())) {
                break;
            }
        }
        temp.setItem(projectTestMap);
        temp.setOutsourcingItemIds(outsourcingItemIds);
        temp.setSamplingOutItemIds(samplingOutItemIds);
    }

    /**
     * 按照末尾数字切割字符串
     *
     * @param str 字符串
     * @return 切割后的数组
     */
    private Object[] getStrSplit(String str) {
        Matcher matcher = PATTERN.matcher(str);
        try {
            if (matcher.find()) {
                return new Object[]{str.replace(matcher.group(), ""), Integer.valueOf(matcher.group())};
            } else {
                return new Object[]{str};
            }
        } catch (Exception e) {
            return new Object[]{str};
        }
    }

    /**
     * 获取默认的测试项目
     *
     * @param projectId      项目id
     * @param sampleTypeId   检测类型id
     * @param analyseItemIds 分析项目id集合
     * @return 测试项目集合
     */
    private List<DtoTest> getDefaultTest(String projectId, String sampleTypeId, List<String> analyseItemIds) {
        List<DtoTest> projectTests = projectTestService.getProjectTest(projectId, sampleTypeId, analyseItemIds);
        if (projectTests.size() < analyseItemIds.size()) {
            List<String> existItemIds = projectTests.stream().map(DtoTest::getAnalyzeItemId).collect(Collectors.toList());
            analyseItemIds.removeAll(existItemIds);
            DtoSampleType samType = sampleTypeService.findOne(sampleTypeId);
            projectTests.addAll(testService.findCommonTestBySampleTypeIdAndAnalyzeItemIdIn(samType.getParentId(), analyseItemIds));
        }
        return projectTests;
    }

    /**
     * 记录分包信息
     *
     * @param frequencyList    方案信息
     * @param changeSftList    修改信息
     * @param sampleFolderList 点位
     * @param samTypes         样品类型
     * @param msgStr           消息
     */
    private void subToLog(List<DtoSamplingFrequencyTemp> frequencyList, List<DtoSamplingFrequencyTestTemp> changeSftList,
                          List<DtoSampleFolderTemplate> sampleFolderList, List<DtoSampleType> samTypes, String msgStr) {
        List<DtoSamplingFrequencyTemp> thisFrequencyList = frequencyList.stream().filter(p ->
                changeSftList.stream().filter(q -> q.getIsOutsourcing() || q.getIsSamplingOut())
                        .map(DtoSamplingFrequencyTestTemp::getSamplingFrequencyTempId)
                        .collect(Collectors.toList()).contains(p.getId())).collect(Collectors.toList());
        List<String> redFolderNames = new ArrayList<>();
        for (DtoSamplingFrequencyTemp frequency : thisFrequencyList) {
            DtoSampleFolderTemplate thisFolder = sampleFolderList.stream().filter(p -> p.getId().equals(frequency.getSampleFolderTempId())).findFirst().orElse(null);
            redFolderNames.add(String.format("%s(%s)第%d周期第%d次", StringUtil.isNotNull(thisFolder) ? thisFolder.getWatchSpot() : "",
                    StringUtil.isNotNull(thisFolder) ? samTypes.stream().filter(p -> p.getId().equals(thisFolder.getSampleTypeId())).map(DtoSampleType::getTypeName).findFirst().orElse("") : "",
                    frequency.getPeriodCount(), frequency.getTimePerPeriod()));
        }
        String comment = String.format("将点位%s的部分指标改为%s：%s。", String.join(",", redFolderNames), msgStr,
                String.join("、", changeSftList.stream().filter(p -> p.getIsOutsourcing() || p.getIsSamplingOut()).map(DtoSamplingFrequencyTestTemp::getRedAnalyzeItemName).distinct().collect(Collectors.toList())));
        newLogService.createLog(sampleFolderList.get(0).getApproveId(), comment, "", EnumPRO.EnumLogType.方案点位信息.getValue(), EnumPRO.EnumLogObjectType.方案.getValue(), EnumPRO.EnumLogOperateType.分包项目.toString(),
                PrincipalContextUser.getPrincipal().getUserId(), PrincipalContextUser.getPrincipal().getUserName(), UUIDHelper.GUID_EMPTY, "");
    }

    @Autowired
    public void setSampleFolderRepository(SampleFolderRepository sampleFolderRepository) {
        this.sampleFolderRepository = sampleFolderRepository;
    }

    @Autowired
    public void setSamplingFrequencyRepository(SamplingFrequencyRepository samplingFrequencyRepository) {
        this.samplingFrequencyRepository = samplingFrequencyRepository;
    }

    @Autowired
    public void setSamplingFrequencyTestRepository(SamplingFrequencyTestRepository samplingFrequencyTestRepository) {
        this.samplingFrequencyTestRepository = samplingFrequencyTestRepository;
    }

    @Autowired
    public void setSamplingFrequencyTempRepository(SamplingFrequencyTempRepository samplingFrequencyTempRepository) {
        this.samplingFrequencyTempRepository = samplingFrequencyTempRepository;
    }

    @Autowired
    public void setSamplingFrequencyTestTempRepository(SamplingFrequencyTestTempRepository samplingFrequencyTestTempRepository) {
        this.samplingFrequencyTestTempRepository = samplingFrequencyTestTempRepository;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    @Lazy
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    @Lazy
    public void setProjectTestService(ProjectTestService projectTestService) {
        this.projectTestService = projectTestService;
    }

    @Autowired
    public void setProjectApprovalRepository(ProjectApprovalRepository projectApprovalRepository) {
        this.projectApprovalRepository = projectApprovalRepository;
    }

    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    @Lazy
    public void setNewLogService(NewLogService newLogService) {
        this.newLogService = newLogService;
    }
}
