package com.sinoyd.lims.pro.dto.customer;

import com.sinoyd.frame.base.util.StringUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 方案方法更换
 * <AUTHOR>
 * @version V1.0.0 2019/12/26
 * @since V100R001
 */
@Data
public class DtoSchemeMethodChange {
    /**
     * 项目id
     */
    private String projectId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 分析项目Id
     */
    private String analyseItemId;

    /**
     * 分析项目Id集合
     */
    private List<String> analyseItemIds = new ArrayList<>();

    /**
     * 方法id
     */
    private String analyzeMethodId;

    /**
     * 修改前的分析方法id
     */
    private String oldAnalyzeMethodId;

    /**
     * 是否批量修改当前项目下的分析方法
     */
    private Boolean isChangeAll;

    /**
     * 将单个的数据对象放入到集合里面
     *
     * @param analyseItemIds 数据对象ids
     */
    public void setAnalyseItemIds(List<String> analyseItemIds) {
        if (StringUtils.isNotNullAndEmpty(this.getAnalyseItemId())) {
            if (!this.analyseItemIds.contains(this.getAnalyseItemId())) {
                analyseItemIds.add(this.getAnalyseItemId());
            }
        }
        this.analyseItemIds = analyseItemIds;
    }

    /**
     * 将单个的数据对象放入到集合里面
     */
    public List<String> getAnalyseItemIds() {
        if (StringUtils.isNotNullAndEmpty(this.getAnalyseItemId())) {
            if (!this.analyseItemIds.contains(this.getAnalyseItemId())) {
                analyseItemIds.add(this.getAnalyseItemId());
            }
        }
        return analyseItemIds;
    }
}
