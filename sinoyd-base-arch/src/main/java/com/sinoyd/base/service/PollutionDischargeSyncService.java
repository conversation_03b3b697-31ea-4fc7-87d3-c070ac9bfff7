package com.sinoyd.base.service;

import com.sinoyd.base.dto.lims.DtoPollutionDischargeSync;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.Collection;
import java.util.List;

/**
 * 企业排污许可证服务接口
 *
 * <AUTHOR>
 * @version V5.2.0 2025/05/06
 * @since V100R001
 */
public interface PollutionDischargeSyncService extends IBaseJpaService<DtoPollutionDischargeSync, String> {

    /**
     * 根据企业id集合查询相关数据
     *
     * @param enterpriseIds 企业id集合
     * @return 企业排污许可证同步数据集合
     */
    List<DtoPollutionDischargeSync> findByEnterpriseIdIn(Collection<String> enterpriseIds);

    /**
     * 获取同步未成功的数据（且同步发起时间大于30分钟的数据）
     * <p/>
     * 用于定时获取等待抓取的数据进行存储
     *
     * @return 同步未成功数据
     */
    List<DtoPollutionDischargeSync> findUnSuccessList();
}
