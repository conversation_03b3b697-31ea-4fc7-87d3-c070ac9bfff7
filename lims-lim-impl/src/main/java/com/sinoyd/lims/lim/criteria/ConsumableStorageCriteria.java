package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * 消耗领用记录查询条件
 * <AUTHOR> 修改： guqx
 * @version V1.0.0 2019/3/11
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsumableStorageCriteria extends BaseCriteria {
    /**
     * 消耗品Id
     */
    private String consumableId;

    /**
     * 开始入库日期
     */
    private String startStorageDate;

    /**
     * 结束入库时间
     */
    private String endStorageDate;

    @Override
    public String getCondition() {
        /**
         * 清除条件数据
         */
        values.clear();
        StringBuilder condition = new StringBuilder();

        if (StringUtils.isNotNullAndEmpty(this.consumableId) && !UUIDHelper.GUID_EMPTY.equals(this.consumableId)) {
            condition.append(" and consumableId = :consumableId");
            values.put("consumableId", this.consumableId);
        }

        if (StringUtil.isNotEmpty(this.startStorageDate)){
            Date date = DateUtil.stringToDate(this.startStorageDate, DateUtil.YEAR);
            condition.append(" and p.storageTime >= :startStorageDate ");
            values.put("startStorageDate", date);
        }
        Calendar calendar = new GregorianCalendar();
        if (StringUtil.isNotEmpty(this.endStorageDate)) {
            Date date = DateUtil.stringToDate(this.endStorageDate, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and p.storageTime < :endStorageDate ");
            values.put("endStorageDate", date);
        }

        condition.append(" and isDeleted = 0 ");
        return condition.toString();
    }
}