package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.lims.DtoConsumableOfMixed;
import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.repository.lims.ConsumableOfMixedRepository;
import com.sinoyd.base.service.AnalyzeItemService;
import com.sinoyd.base.service.ConsumableOfMixedService;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 混标信息
 * <AUTHOR>
 * @version V1.0.0 2019/5/5
 * @since V100R001
 */
@Service
public class ConsumableOfMixedServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoConsumableOfMixed, String, ConsumableOfMixedRepository> implements ConsumableOfMixedService {
    @Autowired
    @Lazy
    private AnalyzeItemService analyzeItemService;

    @Autowired
    @Lazy
    private DimensionService dimensionService;

    //分页查询混标信息
    @Override
    public void findByPage(PageBean<DtoConsumableOfMixed> pageBean, BaseCriteria criteria) {
        // 设置查询的实体类名及别名
        pageBean.setEntityName("DtoConsumableOfMixed p");
        // 设置查询返回的字段、实体别名表示所有字段
        pageBean.setSelect("select p");
        super.findByPage(pageBean, criteria);

        List<DtoConsumableOfMixed> list = pageBean.getData();

        //获取相关量纲
        List<String> dimensionIds = list.stream()
                .filter(p -> StringUtil.isNotNull(p.getDimensionId()))
                .map(DtoConsumableOfMixed::getDimensionId).collect(Collectors.toList());
        List<DtoDimension> dimensionList = new ArrayList<>();
        if (dimensionIds.size() > 0) {
            dimensionList = dimensionService.findByIds(dimensionIds);
        }

        //获取相关分析项目
        List<String> analyzeItemIds = list.stream()
                .filter(p -> StringUtil.isNotNull(p.getAnalyzeItemId()))
                .map(DtoConsumableOfMixed::getAnalyzeItemId).collect(Collectors.toList());

        List<DtoAnalyzeItem> analyzeItemList = new ArrayList<>();
        if (analyzeItemIds.size() > 0) {
            analyzeItemList = analyzeItemService.findByIds(analyzeItemIds);
        }

        for (DtoConsumableOfMixed item : list) {

            //获取分析项目名称
            Optional<DtoAnalyzeItem> apai = analyzeItemList.stream().filter(p -> item.getAnalyzeItemId().contains(p.getId())).findFirst();
            if (apai.isPresent()) {
                DtoAnalyzeItem analyzeItem = apai.get();
                item.setAnalyzeItemName(analyzeItem.getAnalyzeItemName());
            }

            //获取量纲名称
            Optional<DtoDimension> apde = dimensionList.stream().filter(p -> item.getDimensionId().contains(p.getId())).findFirst();
            if (apde.isPresent()) {
                DtoDimension dimension = apde.get();
                item.setDimensionName(dimension.getDimensionName());
            }
        }

        pageBean.setData(list);// 重新封装到pageBean返回到上层
    }

    //新增混标信息
    @Override
    @Transactional
    public DtoConsumableOfMixed save(DtoConsumableOfMixed entity) {
        verify(entity);
        return super.save(entity);
    }

    //修改混标信息
    @Transactional
    @Override
    public DtoConsumableOfMixed update(DtoConsumableOfMixed entity) {
        verify(entity);
        return super.update(entity);
    }

    /**
     * 校验信息
     *
     * @param entity 数据载体
     */
    private void verify(DtoConsumableOfMixed entity) {
        if (repository.countByConsumableIdAndAnalyzeItemIdAndIdNot(entity.getConsumableId(), entity.getAnalyzeItemId(), entity.getId()) > 0) {
            throw new BaseException("分析项目重复，不允许保存！");
        }
    }

}