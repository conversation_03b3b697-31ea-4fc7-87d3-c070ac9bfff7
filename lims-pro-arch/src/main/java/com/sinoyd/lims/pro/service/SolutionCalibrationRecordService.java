package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.pro.dto.DtoSolutionCalibrationRecord;

import java.util.List;

/**
 * 溶液标定记录操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/04/19
 * @since V100R001
 */
public interface SolutionCalibrationRecordService  extends IBaseJpaService<DtoSolutionCalibrationRecord, String> {
    /**
     * 根据solutionCalibrationId 查询
     *
     * @param solutionCalibrationId 标定标识
     * @return 标定记录
     */
    List<DtoSolutionCalibrationRecord> findBySolutionCalibrationId(String solutionCalibrationId);
}
