package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoReportRecover2Report;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * reportrecover2report数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2022/4/14
 * @since V100R001
 */
public interface ReportRecover2ReportRepository extends IBaseJpaPhysicalDeleteRepository<DtoReportRecover2Report, String> {
        /**
     * 根据报告Id和回收Id查询
     *
     * @param recoverId 回收Id
     * @param reportIds 报告Id
     * @return 查询结果
     */
    List<DtoReportRecover2Report> findByRecoverIdAndReportIdIn(String recoverId,List<String> reportIds);

    /**
     * 根据回收Id删除
     *
     * @param recoverIds 回收id
     * @return 删除的条数
     */
    @Transactional
    Integer deleteByRecoverIdIn(List<String> recoverIds);

    /**
     * 根据回收Id查询
     * @param recoverIds 回收Id
     * @return 关联信息
     */
    List<DtoReportRecover2Report> findByRecoverIdIn(List<String> recoverIds);
}