ALTER TABLE TB_LIM_ReportConfig
    ADD isDefineFileName bit(1) NOT NULL DEFAULT b'0' COMMENT '是否定义生成报表名称';


ALTER TABLE TB_LIM_ReportConfig
    ADD defineFileName VARCHAR(500) NULL DEFAULT '' COMMENT '配置报表名称';
ALTER TABLE TB_LIM_ReportConfig
    ADD beanName VARCHAR(500) NULL DEFAULT '' COMMENT '配置方法名称';


DROP TABLE IF EXISTS TB_BASE_LogForLuckySheet;
-- lucksheet操作记录表 --
CREATE TABLE TB_BASE_LogForLuckySheet
(
    id           varchar(50) NOT NULL COMMENT 'id',
    operatorId   varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '操作者id',
    operatorName varchar(50) COMMENT '操作者名字',
    operateTime  datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '操作时间',
    operateInfo  varchar(500) COMMENT '操作类型（新建、保存、修改等）',
    objectId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '对象id',
    remark       mediumtext NULL COMMENT '备注',
    comment      varchar(1000) COMMENT '说明',
    orgId        varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '组织机构id',
    domainId     varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '所属实验室',
    PRIMARY KEY (id)
) comment 'lucksheet操作记录表';


-- 替代物表中添加量纲名称字段
alter table TB_BASE_SubStitute
    add COLUMN dimensionName varchar(100) NULL COMMENT '量纲名称';

ALTER TABLE TB_LIM_ProjectInstrumentDetails
    ADD COLUMN inPerson varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '入库人';


ALTER TABLE TB_LIM_ProjectInstrumentDetails
    ADD COLUMN outPerson varchar(50) NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000' COMMENT '出库人';



alter table TB_LIM_ProjectInstrumentDetails
    add COLUMN isConfirm bit(1) NOT NULL DEFAULT 0 COMMENT '是否出库确认';



DROP TABLE IF EXISTS TB_LIM_PublishSystemVersion;
-- 版本发布管理表 --
create table TB_LIM_PublishSystemVersion
(
    id             varchar(50)      not null comment '主键',
    title          varchar(200)     not null comment '标题',
    versionNum     varchar(100)     not null comment '版本号',
    publishPerson  varchar(50)      not null comment '发布人',
    publishDate    datetime         not null comment '发布日期',
    flaywayVersion varchar(50)      not null comment 'flayway版本',
    isProduct      bit default b'1' not null comment '是否产品 0否 1是',
    isPublish      bit default b'1' not null comment '是否产品 0否 1是',
    isTemplate     bit default b'0' not null comment '是否产品 0否 1是',
    isConfig       bit default b'0' not null comment '是否产品 0否 1是',
    updateContent  text             not null comment '更新内容',
    deployContent  text null comment '部署注意事项',
    primary key (id)
) comment '版本发布管理表';

