package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.lims.lim.entity.EnvironmentalRecord2Sample;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * DtoEnvironmentalRecord2Sample实体
 * <AUTHOR>
 * @version V1.0.0 2019/12/3
 * @since V100R001
 */
 @EqualsAndHashCode(callSuper = true)
 @Entity
 @Table(name = "tb_lim_environmentalrecord2sample")
 @Data
 @DynamicInsert
 public  class DtoEnvironmentalRecord2Sample extends EnvironmentalRecord2Sample {
}