package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.base.enums.EnumBase;
import com.sinoyd.base.service.SampleTypeService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.customer.DtoSampleItemGroupTag;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.lims.DtoTest;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup2Test;
import com.sinoyd.lims.lim.repository.lims.TestRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroup2TestRepository;
import com.sinoyd.lims.lim.repository.rcc.SampleTypeGroupRepository;
import com.sinoyd.lims.lim.service.PersonService;
import com.sinoyd.lims.lim.service.TestService;
import com.sinoyd.lims.pro.criteria.SampleGroupCriteria;
import com.sinoyd.lims.pro.criteria.SampleGroupRecordCriteria;
import com.sinoyd.lims.pro.dto.*;
import com.sinoyd.lims.pro.dto.customer.DtoLog;
import com.sinoyd.lims.pro.dto.customer.DtoSampleLabelData;
import com.sinoyd.lims.pro.entity.SampleGroup;
import com.sinoyd.lims.pro.enums.EnumPRO;
import com.sinoyd.lims.pro.repository.*;
import com.sinoyd.lims.pro.service.NewLogService;
import com.sinoyd.lims.pro.service.ReceiveSampleRecordService;
import com.sinoyd.lims.pro.service.SampleGroup2TestService;
import com.sinoyd.lims.pro.service.SampleGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.Collator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * SampleGroup操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/6/2
 * @since V100R001
 */
@Slf4j
@Service
public class SampleGroupServiceImpl extends BaseJpaServiceImpl<DtoSampleGroup, String, SampleGroupRepository> implements SampleGroupService {

    @Autowired
    @Lazy
    private SampleTypeService sampleTypeService;
    private ReceiveSampleRecordService receiveSampleRecordService;
    private SampleRepository sampleRepository;
    private NewLogService newLogService;
    private ProjectRepository projectRepository;
    private TestRepository testRepository;
    private CommonRepository commonRepository;
    private SampleTypeGroup2TestRepository sampleTypeGroup2TestRepository;
    private SampleGroup2TestRepository sampleGroup2TestRepository;
    private AnalyseDataRepository analyseDataRepository;
    private SampleGroup2TestService sampleGroup2TestService;
    private SampleTypeGroupRepository sampleTypeGroupRepository;
    private PersonService personService;
    private TestService testService;

    @Override
    public void findByPage(PageBean<DtoSampleGroup> pb, BaseCriteria sampleGroupCriteria) {
        pb.setEntityName("DtoSampleGroup a,DtoSample s");
        pb.setSelect("select new com.sinoyd.lims.pro.dto.DtoSampleGroup (s.code,s.redFolderName,s.samplingTimeBegin," +
                "s.sampleTypeId,a.id,a.receiveId,a.sampleId,a.sampleTypeGroupId,a.sampleTypeGroupName,a.analyseItemNames," +
                "a.hasScanned,a.scannedTime,a.scanner,a.orgId,a.creator,a.createDate,a.domainId,a.modifier,a.modifyDate," +
                "s.sampleCategory,s.associateSampleId) ");
        comRepository.findByPage(pb, sampleGroupCriteria);
        List<DtoSampleGroup> newDatas = pb.getData();
        if (newDatas.size() > 0) {
            List<String> sampleTypeIds = newDatas.stream().map(DtoSampleGroup::getSampleTypeId).distinct().collect(Collectors.toList());
            List<String> sampleTypeGroupIds = newDatas.stream().map(DtoSampleGroup::getSampleTypeGroupId).filter(StringUtil::isNotEmpty)
                    .distinct().collect(Collectors.toList());
            List<DtoSampleType> sampleTypeList = sampleTypeService.findAll(sampleTypeIds);
            List<DtoSampleTypeGroup> sampleTypeGroupList = sampleTypeGroupIds.isEmpty() ? new ArrayList<>() : sampleTypeGroupRepository.findByIdIn(sampleTypeGroupIds);
            fillingTransientFields(newDatas, sampleTypeList, sampleTypeGroupList, null, true);
        }
    }

    @Override
    public List<DtoSampleGroup> findForReceiveSampleRecord(BaseCriteria criteria) {
        //根据领样单查样品，再根据样品查分组
        SampleGroupRecordCriteria recordCriteria = (SampleGroupRecordCriteria) criteria;
        List<String> sampleIdList = sampleRepository.findByReceiveId(recordCriteria.getReceiveId()).stream().map(DtoSample::getId).collect(Collectors.toList());
        recordCriteria.setSampleIds(sampleIdList);
        return findBySamples(recordCriteria);
    }

    @Override
    public List<DtoSampleGroup> findBySamples(BaseCriteria criteria) {
        SampleGroupRecordCriteria recordCriteria = (SampleGroupRecordCriteria) criteria;
        if (recordCriteria.getSampleIds() != null && !recordCriteria.getSampleIds().isEmpty()) {
            List<String> sampleIdList = recordCriteria.getSampleIds();
            List<DtoSampleGroup> sampleGroupList = repository.findBySampleIdIn(sampleIdList);
            List<DtoSample> sampleList = sampleRepository.findByIdInAndIsDeletedFalse(sampleIdList).stream()
                    .filter(s -> !EnumPRO.EnumSampleCategory.比对评价样.getValue().equals(s.getSampleCategory())).collect(Collectors.toList());
            Map<String, DtoSample> sampleMap = sampleList.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
            List<DtoSampleType> sampleTypeList = sampleTypeService.findAll();
            List<String> sampleTypeGroupIds = sampleGroupList.stream().map(DtoSampleGroup::getSampleTypeGroupId).distinct().collect(Collectors.toList());
            List<DtoSampleTypeGroup> sampleTypeGroupList = sampleTypeGroupIds.isEmpty() ? new ArrayList<>() : sampleTypeGroupRepository.findByIdIn(sampleTypeGroupIds);
            fillingTransientFields(sampleGroupList, sampleTypeList, sampleTypeGroupList, sampleMap, false);
            //按条件进行过滤及排序
            return filterAndSortSampleGroups(recordCriteria, sampleGroupList);
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public void updateForRecord(DtoSampleGroup sampleGroup) {
        DtoSampleGroup oldSampleGroup = repository.findOne(sampleGroup.getId());
        if (StringUtil.isNull(oldSampleGroup)) {
            throw new BaseException("样品分组信息不存在！");
        }
        //获取修改的参数名称及原来的参数值，新的值
        List<String> updateParamInfo = getUpdateParamInfo(oldSampleGroup, sampleGroup);
        if (StringUtil.isNotEmpty(updateParamInfo)) {
            repository.saveWithNull(sampleGroup);
            //插入修改日志
            addUpdateParamLog(updateParamInfo, sampleGroup);
            // 更新样品有效期
            updateRequireDeadLine(Collections.singletonList(sampleGroup), sampleGroup.getReceiveSampleDate());

        }
    }

    @Override
    @Transactional
    public void updateGroupInfo(BaseCriteria criteria) {
        SampleGroupRecordCriteria recordCriteria = (SampleGroupRecordCriteria) criteria;
        List<DtoSample> samples = sampleRepository.findByReceiveId(recordCriteria.getReceiveId());
        List<String> sampleIds = samples.stream().map(DtoSample::getId).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(sampleIds)) {
            if (recordCriteria.getIsGroup() == null) {
                //修改分组配置后的更新操作，反查出 isgroup groupId(父分组标识）
                List<DtoSampleGroup> sampleGroupList = repository.findBySampleIdIn(sampleIds);
                if (!sampleGroupList.isEmpty()) {
                    recordCriteria.setIsGroup(sampleGroupList.get(0).getIsGroup());
                    recordCriteria.setGroupIds(new ArrayList<>());
                    if (sampleGroupList.get(0).getIsGroup() == 1) {
                        List<String> groupIds = sampleGroupList.stream().map(DtoSampleGroup::getSampleTypeGroupId).distinct()
                                .filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).collect(Collectors.toList());
//                        String sampleTypeGroupId = sampleGroupList.get(0).getSampleTypeGroupId();
                        List<DtoSampleTypeGroup> sampleTypeGroups = StringUtil.isNotEmpty(groupIds) ? sampleTypeGroupRepository.findAll(groupIds) : new ArrayList<>();
                        recordCriteria.setGroupIds(sampleTypeGroups.stream().map(DtoSampleTypeGroup::getParentId).distinct().collect(Collectors.toList()));
                    }
                }
            }
            if (EnumBase.EnumSampleGroupType.全因子.getValue().equals(recordCriteria.getIsGroup())
                    || EnumBase.EnumSampleGroupType.单因子.getValue().equals(recordCriteria.getIsGroup())
                    || (EnumBase.EnumSampleGroupType.按分组.getValue().equals(recordCriteria.getIsGroup()) && StringUtil.isNotEmpty(recordCriteria.getGroupIds()))) {
                List<DtoSampleLabelData> sampleLabelDataList = findSampleLabelData(sampleIds, recordCriteria.getIsGroup(), recordCriteria.getGroupIds());
                updateSampleGroup(sampleLabelDataList, recordCriteria.getIsGroup());
            }
        }
    }

    @Override
    public List<DtoSampleGroup> findBySampleIds(List<String> sampleIds) {
        return repository.findBySampleIdIn(sampleIds);
    }

    /**
     * 赋值冗余字段分组编号拆分标识
     *
     * @param newDatas            样品分组数据源
     * @param sampleTypeList      样品类型集合
     * @param sampleTypeGroupList 样品分组类型集合
     * @param sampleMap           样品Map
     * @param isDisCl             是否处理串联样
     */
    protected void fillingTransientFields(List<DtoSampleGroup> newDatas, List<DtoSampleType> sampleTypeList, List<DtoSampleTypeGroup> sampleTypeGroupList,
                                          Map<String, DtoSample> sampleMap, Boolean isDisCl) {
        for (DtoSampleGroup sampleGroup : newDatas) {
            if (StringUtil.isNotNull(sampleMap)) {
                DtoSample sample = sampleMap.get(sampleGroup.getSampleId());
                sampleGroup.setSampleTypeId(sample.getSampleTypeId());
                sampleGroup.setSampleCode(StringUtil.isNotNull(sample) ? sample.getCode() : "");
                sampleGroup.setRedFolderName(StringUtil.isNotNull(sample) ? sample.getRedFolderName() : "");
                if (StringUtil.isEmpty(sampleGroup.getSampleTypeGroupName())) {
                    sampleGroup.setSampleTypeGroupName("/");
                }
            }
            sampleGroup.setSampleCodeWithTag(sampleGroup.getSampleCode());
            DtoSampleType sampleType = sampleTypeList.stream().filter(p -> p.getId().equals(sampleGroup.getSampleTypeId())).findFirst().orElse(null);
            if (sampleType != null) {
                sampleGroup.setSampleTypeName(sampleType.getTypeName());
                if (Boolean.TRUE.equals(sampleType.getIsOpenGroupTag())) {
                    DtoSampleTypeGroup sampleTypeGroup = sampleTypeGroupList.stream().filter(t -> t.getId().equals(sampleGroup.getSampleTypeGroupId()))
                            .findFirst().orElse(null);
                    if (sampleTypeGroup != null && StringUtil.isNotEmpty(sampleTypeGroup.getSampleCodeTag())) {
                        sampleGroup.setSampleCodeTag(sampleTypeGroup.getSampleCodeTag());
                        if (isDisCl) {
                            if (!sampleGroup.getSampleCategory().equals(EnumPRO.EnumSampleCategory.串联样.getValue())) {
                                sampleGroup.setSampleCodeWithTag(sampleGroup.getSampleCode() + "-" + sampleTypeGroup.getSampleCodeTag());
                            } else {
                                String yySampleId = sampleGroup.getAssociateSampleId();
                                Optional<DtoSampleGroup> yySampleGroup = newDatas.stream().filter(p -> yySampleId.equals(p.getSampleId())).findFirst();
                                if (yySampleGroup.isPresent()) {
                                    String clCode = sampleGroup.getSampleCode().replace(yySampleGroup.get().getSampleCode(), "");
                                    sampleGroup.setSampleCodeWithTag(yySampleGroup.get().getSampleCode() + "-" + sampleTypeGroup.getSampleCodeTag() + clCode);
                                }
                            }
                        } else {
                            if (StringUtil.isNotEmpty(sampleGroup.getSampleCode()) && StringUtil.isNotEmpty(sampleGroup.getSampleCodeTag())) {
                                sampleGroup.setSampleCodeWithTag(sampleGroup.getSampleCode() + "-" + sampleGroup.getSampleCodeTag());
                            }
                        }
                    }
                }
            }
        }
    }


    private void updateSampleGroup(List<DtoSampleLabelData> sampleLabelDataList, int groupInfo) {
        //测试项目ids
        List<String> testAllIds = sampleLabelDataList.stream().map(DtoSampleLabelData::getTestId).distinct().collect(Collectors.toList());
        List<DtoTest> testList = StringUtil.isNotEmpty(testAllIds) ? testRepository.findAll(testAllIds) : new ArrayList<>();
        List<String> parentIdList = testList.stream().map(DtoTest::getParentId).distinct().collect(Collectors.toList());
        parentIdList.remove(UUIDHelper.GUID_EMPTY);
        List<DtoTest> parentTestList = StringUtil.isNotEmpty(parentIdList) ? testRepository.findAll(parentIdList) : new ArrayList<>();
        //样品信息
        List<String> sampleIds = sampleLabelDataList.stream().map(DtoSampleLabelData::getSampleId).distinct().collect(Collectors.toList());
        List<DtoSample> sampleList = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(sampleIds) : new ArrayList<>();
        List<String> receiveIds = sampleList.stream().map(DtoSample::getReceiveId).distinct().collect(Collectors.toList());
        List<DtoReceiveSampleRecord> recordList = receiveSampleRecordService.findAll(receiveIds);
        // 单因子打印时处理根据总称查询原始数据
        List<DtoTest> parentDtoTests = new ArrayList<>();
        Map<String, List<DtoAnalyseData>> dtoAnalyseDataToMap = new HashMap<>();
        Map<String, List<DtoTest>> testToMap = new HashMap<>();
        if (groupInfo == 3) {
            // 筛选样品id
            List<String> sampleIdList = sampleList.stream().map(DtoSample::getId).collect(Collectors.toList());
            // 根据样品id查询所有分析数据的测试项目id
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIdList);
            dtoAnalyseDataToMap = analyseDataList.stream().collect(Collectors.groupingBy(DtoAnalyseData::getSampleId));
            List<String> testIdList = analyseDataList.stream().map(DtoAnalyseData::getTestId).collect(Collectors.toList());
            // 获取对应的所有测试项目
            List<DtoTest> allTests = testRepository.findAll(testIdList);
            // 筛选父级id不为空的数据
            List<String> parentIds = allTests.stream().filter(p -> !p.getParentId().equals(UUIDHelper.GUID_EMPTY)).map(DtoTest::getParentId).collect(Collectors.toList());
            parentDtoTests = StringUtil.isNotEmpty(parentIds) ? testRepository.findAll(parentIds) : new ArrayList<>();
            List<DtoTest> allSunDtoTests = StringUtil.isNotEmpty(parentIds) ? testRepository.findByParentIdIn(parentIds) : new ArrayList<>();
            testToMap = allSunDtoTests.stream().collect(Collectors.groupingBy(DtoTest::getParentId));
        }

        //已经存在的分组信息
        List<DtoSampleGroup> oldSampleGroupList = StringUtil.isNotEmpty(sampleIds) ? repository.findBySampleIdIn(sampleIds) : new ArrayList<>();
        List<DtoSampleGroup> remainOldSampleGroupList = new ArrayList<>(oldSampleGroupList);
        if (groupInfo != 3) {
            boolean groupFlag = false;
            if (groupInfo == 1) {
                groupFlag = true;
            }
            final boolean isGroup = groupFlag;
            List<String> marks = sampleLabelDataList.stream().map(isGroup ? DtoSampleLabelData::getGroupMark : DtoSampleLabelData::getMark).distinct()
                    .collect(Collectors.toList());
            //需要新增的分组信息列表
            List<DtoSampleGroup> newSampleGroupList = new ArrayList<>();
            //需要删除的分组信息列表
            List<DtoSampleGroup> needDeleteSampleGroupList = new ArrayList<>();
            for (String mark : marks) {
                //分组后的信息
                List<DtoSampleLabelData> dataList = sampleLabelDataList.stream().filter(p -> isGroup ? mark.equals(p.getGroupMark()) :
                        mark.equals(p.getMark())).collect(Collectors.toList());
                List<String> testIds = dataList.stream().map(DtoSampleLabelData::getTestId).distinct().collect(Collectors.toList());
                if (dataList.size() > 0) {
                    DtoSampleLabelData sampleLabelData = dataList.get(0);
                    //获取样品信息
                    DtoSample dtoSample = sampleList.stream().filter(p -> p.getCode().equals(sampleLabelData.getSampleCode()))
                            .findFirst().orElse(null);
                    String analyseItemNames = String.join("，", getTestTotalName(testList, testIds, parentTestList));
//                    //送样单已通过则不更新
//                    boolean isBack = Boolean.TRUE;
//                    if (dtoSample != null) {
//                        Optional<DtoReceiveSampleRecord> recordOptional = recordList.stream()
//                                .filter(p -> dtoSample.getReceiveId().equals(p.getId())).findFirst();
//                        if (recordOptional.isPresent()) {
//                            if (recordOptional.get().getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.已确认.getValue())) {
//                                oldSampleGroupList.removeIf(p -> dtoSample.getId().equals(p.getSampleId()));
//                                remainOldSampleGroupList.removeIf(p -> dtoSample.getId().equals(p.getSampleId()));
//                            } else {
//                                isBack = Boolean.FALSE;
//                            }
//                        }
//                    }
//                    if (isBack) {
//                        continue;
//                    }
                    //样品已检毕，分组信息不做任何更新
//                    if (dtoSample == null || EnumPRO.EnumSampleStatus.样品检毕.name().equals(dtoSample.getStatus())) {
//                        if (dtoSample != null) {
//                            oldSampleGroupList.removeIf(p -> dtoSample.getId().equals(p.getSampleId()));
//                            remainOldSampleGroupList.removeIf(p -> dtoSample.getId().equals(p.getSampleId()));
//                        }
//                        continue;
//                    }
                    //设置新增分组数据并获取是否删除旧分组数据
                    setGroupMsg(dtoSample, oldSampleGroupList, remainOldSampleGroupList, sampleLabelData, analyseItemNames, marks, needDeleteSampleGroupList,
                            newSampleGroupList, groupInfo, dataList);
                }
            }
            //对应样品下的原有的分组信息，排除掉分组id和当前新的分组id相同的记录，剩余的进行删除，避免更换分组方式进行更新以后，原有的分组信息遗留在数据库中
            if (StringUtil.isNotEmpty(remainOldSampleGroupList)) {
                needDeleteSampleGroupList.addAll(remainOldSampleGroupList);
            }
            //删除旧的分组信息
            if (StringUtil.isNotEmpty(needDeleteSampleGroupList)) {
                this.delete(needDeleteSampleGroupList);
            }
            //存储新的分组信息
            if (StringUtil.isNotEmpty(newSampleGroupList)) {
                this.save(newSampleGroupList);
            }
        } else {
            List<String> marks = sampleLabelDataList.stream().map(DtoSampleLabelData::getMark).distinct().collect(Collectors.toList());
            //需要新增的分组信息列表
            List<DtoSampleGroup> newSampleGroupList = new ArrayList<>();
            //需要删除的分组信息列表
            List<DtoSampleGroup> needDeleteSampleGroupList = new ArrayList<>();
            for (String mark : marks) {
                //分组后的信息
                List<DtoSampleLabelData> dataList = sampleLabelDataList.stream().filter(p -> mark.equals(p.getMark())).collect(Collectors.toList());
                List<String> testIds = dataList.stream().map(DtoSampleLabelData::getTestId).distinct().collect(Collectors.toList());
                if (dataList.size() > 0) {
                    DtoSampleLabelData sampleLabelData = dataList.get(0);
                    //获取样品信息
                    DtoSample dtoSample = sampleList.stream().filter(p -> p.getCode().equals(sampleLabelData.getSampleCode()))
                            .findFirst().orElse(null);
                    // 根据样品信息重新处理单因子，存在总称的数据
                    List<DtoSampleLabelData> sampleLabelDatas = new ArrayList<>();
                    List<DtoAnalyseData> dtoAnalyseDatas = dtoAnalyseDataToMap.get(dtoSample.getId());
                    DtoTest dtoTest = parentDtoTests.stream().filter(p -> p.getRedAnalyzeItemName().equals(sampleLabelData.getRedAnalyzeItemName())).findFirst().orElse(null);
                    if (StringUtil.isNotNull(dtoTest)) {
                        List<String> ids = testToMap.get(dtoTest.getId()).stream().map(DtoTest::getId).collect(Collectors.toList());
                        List<DtoAnalyseData> dtoAnalyseData = dtoAnalyseDatas.stream().filter(p -> ids.contains(p.getTestId())).collect(Collectors.toList());
                        for (DtoAnalyseData dtoAnalyseDatum : dtoAnalyseData) {
                            DtoSampleLabelData dtoSampleLabelData = new DtoSampleLabelData();
                            dtoSampleLabelData.setAnaId(dtoAnalyseDatum.getId());
                            dtoSampleLabelData.setTestId(dtoAnalyseDatum.getTestId());
                            sampleLabelDatas.add(dtoSampleLabelData);
                        }
                    } else {
                        DtoAnalyseData analyseData = dtoAnalyseDatas.stream().filter(p -> p.getRedAnalyzeItemName().equals(sampleLabelData.getRedAnalyzeItemName())).findFirst().orElse(null);
                        DtoSampleLabelData dtoSampleLabelData = new DtoSampleLabelData();
                        dtoSampleLabelData.setAnaId(analyseData.getId());
                        dtoSampleLabelData.setTestId(analyseData.getTestId());
                        sampleLabelDatas.add(dtoSampleLabelData);
                    }

                    //送样单已通过则不更新
                    boolean isBack = Boolean.TRUE;
                    if (dtoSample != null) {
                        Optional<DtoReceiveSampleRecord> recordOptional = recordList.stream()
                                .filter(p -> dtoSample.getReceiveId().equals(p.getId())).findFirst();
                        if (recordOptional.isPresent()) {
                            if (recordOptional.get().getInfoStatus().equals(EnumPRO.EnumReceiveInfoStatus.已确认.getValue())) {
                                oldSampleGroupList.removeIf(p -> dtoSample.getId().equals(p.getSampleId()));
                                remainOldSampleGroupList.removeIf(p -> dtoSample.getId().equals(p.getSampleId()));
                            } else {
                                isBack = Boolean.FALSE;
                            }
                        }
                    }
                    if (isBack) {
                        continue;
                    }
                    //设置新增分组数据并获取是否删除旧分组数据
                    setGroupMsg(dtoSample, oldSampleGroupList, remainOldSampleGroupList, sampleLabelData, sampleLabelData.getRedAnalyzeItemName(), marks,
                            needDeleteSampleGroupList, newSampleGroupList, groupInfo, sampleLabelDatas);
                }
            }
            //对应样品下的原有的分组信息，排除掉分组id和当前新的分组id相同的记录，剩余的进行删除，避免更换分组方式进行更新以后，原有的分组信息遗留在数据库中
            if (StringUtil.isNotEmpty(remainOldSampleGroupList)) {
                needDeleteSampleGroupList.addAll(remainOldSampleGroupList);
            }
            //删除旧的分组信息
            if (StringUtil.isNotEmpty(needDeleteSampleGroupList)) {
                this.delete(needDeleteSampleGroupList);
            }
            //存储新的分组信息
            if (StringUtil.isNotEmpty(newSampleGroupList)) {
                this.save(newSampleGroupList);
            }
        }
    }

    @Override
    public List<DtoSampleLabelData> findSampleLabelData(List<String> sampleIds, int groupInfo, List<String> groupIds) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.pro.dto.customer.DtoSampleLabelData(");
        stringBuilder.append("s.id, s.code,s.redFolderName,s.samplingTimeBegin,a.testId,a.id,s.sampleTypeId,st.typeName,");
        stringBuilder.append("st.parentId,a.redAnalyzeItemName,a.isOutsourcing,s.receiveId,s.inspectedEnt,s.storageConditions,s.status,s.sampleFolderId,a.isSamplingOut)");
        stringBuilder.append(" from DtoSample s,DtoAnalyseData a,DtoSampleType st");
        stringBuilder.append(" where 1=1");
        stringBuilder.append("  and s.id = a.sampleId");
        stringBuilder.append("  and s.sampleTypeId = st.id");
//        stringBuilder.append("  and s.projectId = p.id");
        stringBuilder.append(" and s.isDeleted=0 and a.isDeleted=0 and a.isCompleteField = 0 and a.dataStatus <> :dataStatus");
        stringBuilder.append(" and s.isDeleted=0 and a.isDeleted=0 and a.isOutsourcing = 0");
        stringBuilder.append(" and s.id in :sampleIds");
        stringBuilder.append(" order by s.code asc");
        Map<String, Object> values = new HashMap<>();
        values.put("sampleIds", sampleIds);
        values.put("dataStatus", EnumPRO.EnumAnalyseDataStatus.作废.getValue());
        //样品对应项目编号Map
        Map<String, String> sampleToProjectCodeMap = findProjectCodeMap(sampleIds);
        List<DtoSampleLabelData> sampleLabelDataList = commonRepository.find(stringBuilder.toString(), values);
        for (DtoSampleLabelData loopData : sampleLabelDataList) {
            String projectCode = "";
            if (StringUtil.isNotEmpty(sampleToProjectCodeMap) && sampleToProjectCodeMap.containsKey(loopData.getSampleId())) {
                projectCode = sampleToProjectCodeMap.getOrDefault(loopData.getSampleId(), "");
            }
            loopData.setProjectCode(projectCode);
        }
        if (groupInfo != 3) {
            boolean isGroup = false;
            if (groupInfo == 1) {
                isGroup = true;
            }
            //分组相关的信息
            List<DtoSampleTypeGroup> sampleTypeGroupList = findSampleTypeGroup(isGroup, groupIds);
            for (DtoSampleLabelData sampleLabelData : sampleLabelDataList) {
                //是否进行分包
                Boolean isOutsourcing = sampleLabelData.getIsOutsourcing();
                if (isOutsourcing) {
                    sampleLabelData.setMark(sampleLabelData.getSampleCode() + sampleLabelData.getRedAnalyzeItemName());
                    sampleLabelData.setGroupMark(sampleLabelData.getSampleCode() + sampleLabelData.getRedAnalyzeItemName());
                } else {
                    sampleLabelData.setMark(sampleLabelData.getSampleCode());
                    Optional<DtoSampleTypeGroup> sampleTypeGroupOptional = sampleTypeGroupList.stream().filter(p -> p.getTestIds().contains(sampleLabelData.getTestId())
                            && p.getSampleTypeId().equals(sampleLabelData.getBigSampleTypeId())).findFirst();
                    sampleLabelData.setGroupId(UUIDHelper.GUID_EMPTY);

                    sampleTypeGroupOptional.ifPresent(dto -> {
                        sampleLabelData.setFixer(dto.getFixer());
                        sampleLabelData.setGroupId(dto.getId());
                        sampleLabelData.setFixer(dto.getFixer());
                        sampleLabelData.setSaveCondition(dto.getSaveCondition());
                        sampleLabelData.setGroupName(dto.getGroupName());
                        sampleLabelData.setContainerName(dto.getContainerName());
                        sampleLabelData.setPretreatmentMethod(dto.getPretreatmentMethod());
                        sampleLabelData.setSampleVolume(dto.getSampleVolume());
                        sampleLabelData.setContainerStatus(dto.getContainerStatus());
                        sampleLabelData.setRemark(dto.getRemark());
                    });
                    //分组信息判空，为后续判断避免空指针
                    if (StringUtil.isNull(sampleLabelData.getContainerName())) {
                        sampleLabelData.setContainerName("");
                    }
                    if (StringUtil.isNull(sampleLabelData.getPretreatmentMethod())) {
                        sampleLabelData.setPretreatmentMethod("");
                    }
                    if (StringUtil.isNull(sampleLabelData.getSampleVolume())) {
                        sampleLabelData.setSampleVolume("");
                    }
                    if (StringUtil.isNull(sampleLabelData.getContainerStatus())) {
                        sampleLabelData.setContainerStatus(0);
                    }
                    if (StringUtil.isNull(sampleLabelData.getRemark())) {
                        sampleLabelData.setRemark("");
                    }
                    if (StringUtil.isNull(sampleLabelData.getFixer())) {
                        sampleLabelData.setFixer("");
                    }
                    if (StringUtil.isNull(sampleLabelData.getSaveCondition())) {
                        sampleLabelData.setSaveCondition("");
                    }
                    if (StringUtil.isNull(sampleLabelData.getGroupName())) {
                        sampleLabelData.setGroupName("");
                    }
                    sampleLabelData.setGroupMark(sampleLabelData.getSampleCode() + "," + sampleLabelData.getGroupId() + "," + (sampleLabelData.getIsSamplingOut() ? "是" : "否"));
                }
            }
        } else {
            //groupInfo 为3时，按照单因子生成标签
            //测试项目ids
            List<String> testAllIds = sampleLabelDataList.stream().map(DtoSampleLabelData::getTestId).distinct().collect(Collectors.toList());
            //相关的测试项目数据
            List<DtoTest> testList = StringUtil.isNotEmpty(testAllIds) ? testRepository.findAll(testAllIds) : new ArrayList<>();
            List<String> parentIdList = testList.stream().map(DtoTest::getParentId).distinct().collect(Collectors.toList());
            List<DtoTest> parentTestList = StringUtil.isNotEmpty(parentIdList) ? testRepository.findAll(parentIdList) : new ArrayList<>();
            //按照样品id分组
            Map<String, List<DtoSampleLabelData>> smpId2SampleLabelDataListMap = sampleLabelDataList.stream().collect(Collectors.groupingBy(DtoSampleLabelData::getSampleId));
            //合并总称后的 sampleLabelData 列表
            List<DtoSampleLabelData> mergeSampleLabelDataList = new ArrayList<>();
            for (Map.Entry<String, List<DtoSampleLabelData>> entry : smpId2SampleLabelDataListMap.entrySet()) {
                List<DtoSampleLabelData> loopLabelDataList = entry.getValue();
                DtoSampleLabelData fstLabelData = loopLabelDataList.get(0);
                List<String> loopTstIdList = loopLabelDataList.stream().map(DtoSampleLabelData::getTestId).distinct().collect(Collectors.toList());
                //获取该样品id对应的样品下所有分析项目合并后的分析项目名称列表
                List<String> generalTestNameList = getTestTotalName(testList, loopTstIdList, parentTestList);
                //每一个分析项目名称对应一个 DtoSampleLabelData 对象
                for (String testName : generalTestNameList) {
                    DtoSampleLabelData mergeSampleLabelData = new DtoSampleLabelData(fstLabelData.getSampleId(), fstLabelData.getSampleCode(), fstLabelData.getRedFolderName(),
                            fstLabelData.getSamplingTimeBegin(), UUIDHelper.GUID_EMPTY, UUIDHelper.GUID_EMPTY, fstLabelData.getSampleTypeId(), fstLabelData.getSampleTypeName(),
                            fstLabelData.getBigSampleTypeId(), testName, fstLabelData.getIsOutsourcing(), fstLabelData.getReceiveId(), fstLabelData.getInspectedEnt(), fstLabelData.getStorageConditions(), fstLabelData.getStatus(), fstLabelData.getSampleFolderId(), fstLabelData.getIsSamplingOut());
                    mergeSampleLabelData.setMark(mergeSampleLabelData.getSampleId() + mergeSampleLabelData.getRedAnalyzeItemName());
                    mergeSampleLabelData.setGroupId(UUIDHelper.GUID_EMPTY);
                    mergeSampleLabelData.setProjectCode(fstLabelData.getProjectCode());
                    //分组信息全置空，为后续判断避免空指针
                    mergeSampleLabelData.setContainerName("");
                    mergeSampleLabelData.setPretreatmentMethod("");
                    mergeSampleLabelData.setSampleVolume("");
                    mergeSampleLabelData.setContainerStatus(0);
                    mergeSampleLabelData.setRemark("");
                    mergeSampleLabelData.setFixer("");
                    mergeSampleLabelData.setSaveCondition("");
                    mergeSampleLabelData.setGroupName("");
                    mergeSampleLabelData.setGroupMark(mergeSampleLabelData.getSampleCode() + "," + mergeSampleLabelData.getGroupId());
                    mergeSampleLabelDataList.add(mergeSampleLabelData);
                }
            }
            sampleLabelDataList = mergeSampleLabelDataList;
        }
        return sampleLabelDataList;
    }

    @Override
    public List<DtoSampleItemGroupTag> findSampleItemGroupTag(BaseCriteria criteria) {
        StringBuilder stringBuilder = new StringBuilder("select new com.sinoyd.lims.lim.dto.customer.DtoSampleItemGroupTag(");
        stringBuilder.append("g.sampleId,g2t.testId,tg.id as groupId,tg.groupName,tg.sampleCodeTag,s.associateSampleId,s.sampleCategory)");
        stringBuilder.append(" from DtoSampleGroup g,DtoSample s,DtoSampleTypeGroup tg,DtoSampleType t,DtoSampleGroup2Test g2t");
        stringBuilder.append(" where g.sampleId = s.id and g.sampleTypeGroupId = tg.id and s.sampleTypeId = t.id and g.id = g2t.sampleGroupId ");
        stringBuilder.append(" and t.isOpenGroupTag = 1 ");
        SampleGroupCriteria sampleGroupCriteria = (SampleGroupCriteria) criteria;
        Map<String, Object> values = new HashMap<>();
        if (StringUtil.isNotEmpty(sampleGroupCriteria.getSampleIds())) {
            stringBuilder.append(" and g.sampleId in :sampleIds");
            values.put("sampleIds", sampleGroupCriteria.getSampleIds());
            if (StringUtil.isNotEmpty(sampleGroupCriteria.getTestIds())) {
                stringBuilder.append(" and g2t.testId in :testIds");
                values.put("testIds", sampleGroupCriteria.getTestIds());
            }
            return commonRepository.find(stringBuilder.toString(), values);
        }
        return new ArrayList<>();
    }

    /**
     * 取出分组信息
     *
     * @param isGroup  是否分组显示
     * @param groupIds 选择的分组id
     * @return 返回分组信息
     */
    @Override
    public List<DtoSampleTypeGroup> findSampleTypeGroup(Boolean isGroup, List<String> groupIds) {
        List<DtoSampleTypeGroup> sampleTypeGroups = new ArrayList<>();
        if (isGroup && groupIds.size() > 0) {
            StringBuilder select = new StringBuilder("select a from DtoSampleTypeGroup as a");
            select.append(" where 1=1 ");
            select.append(" and a.parentId in :groupIds");
            Map<String, Object> values = new HashMap<>();
            values.put("groupIds", groupIds);
            sampleTypeGroups = commonRepository.find(select.toString(), values);
            List<String> gIds = sampleTypeGroups.stream().map(DtoSampleTypeGroup::getId).distinct().collect(Collectors.toList());
            //存在分组为配置具体分组的情况
            if (gIds.size() > 0) {
                //获取相关的测试项目分组信息
                List<DtoSampleTypeGroup2Test> group2Tests =
                        sampleTypeGroup2TestRepository.findBySampleTypeGroupIds(gIds);
                for (DtoSampleTypeGroup sampleTypeGroup : sampleTypeGroups) {
                    List<String> testIds =
                            group2Tests.stream().filter(p -> p.getSampleTypeGroupId().equals(sampleTypeGroup.getId())).map(DtoSampleTypeGroup2Test::getTestId).distinct().collect(Collectors.toList());
                    sampleTypeGroup.setTestIds(testIds);
                }
            }
        }
        return sampleTypeGroups;
    }

    @Transactional
    @Override
    public void delete(Collection<DtoSampleGroup> entities) {
        if (StringUtil.isNotEmpty(entities)) {
            super.logicDeleteById(entities.stream().map(DtoSampleGroup::getId).collect(Collectors.toList()));
            List<String> sampleGroupIds = entities.stream().map(DtoSampleGroup::getId).collect(Collectors.toList());
            sampleGroup2TestRepository.deleteBySampleGroupIdIn(sampleGroupIds);
        }
    }

    @Transactional
    @Override
    public List<DtoSampleGroup> save(Collection<DtoSampleGroup> entities) {
        List<DtoSampleGroup> save = super.save(entities);
        batchSaveSampleGroup2Test(save);
        return save;
    }

    @Override
    @Transactional
    public void batchUpdate(DtoSampleGroup sampleGroup) {
        if (StringUtil.isNotEmpty(sampleGroup.getRecipientName()) || StringUtil.isNotNull(sampleGroup.getReceiveSampleDate())) {
            List<DtoSampleGroup> sampleGroups = repository.findAll(sampleGroup.getIds());
            for (DtoSampleGroup group : sampleGroups) {
                if (StringUtil.isNotEmpty(sampleGroup.getRecipientName())) {
                    group.setRecipientName(sampleGroup.getRecipientName());
                }
                if (StringUtil.isNotNull(sampleGroup.getReceiveSampleDate())) {
                    group.setReceiveSampleDate(sampleGroup.getReceiveSampleDate());
                }
            }
            repository.save(sampleGroups);
            // 更新样品有效期
            updateRequireDeadLine(sampleGroups, sampleGroup.getReceiveSampleDate());
        }
    }

    /**
     * 根据选中分组和接样日期更新样品有效期
     *
     * @param sampleGroups      样品分组数据
     * @param receiveSampleDate 接样日期
     */
    private void updateRequireDeadLine(List<DtoSampleGroup> sampleGroups, Date receiveSampleDate) {
        if (StringUtil.isNotNull(receiveSampleDate)) {
            List<String> sampleGroupIds = sampleGroups.stream().map(DtoSampleGroup::getId).collect(Collectors.toList());
            List<String> sampleIds = sampleGroups.stream().map(DtoSampleGroup::getSampleId).collect(Collectors.toList());
            // 样品分组关联测试项目
            List<DtoSampleGroup2Test> sampleGroup2Tests = StringUtil.isNotEmpty(sampleGroupIds) ?
                    sampleGroup2TestRepository.findBySampleGroupIdIn(sampleGroupIds) : new ArrayList<>();
            List<String> sample2TestIds = sampleGroup2Tests.stream().map(DtoSampleGroup2Test::getTestId).distinct().collect(Collectors.toList());

            //更新样品有效期，根据分组中包含的测试项目过滤分析数据
            List<DtoAnalyseData> analyseDataList = StringUtil.isNotEmpty(sampleIds) ? analyseDataRepository.findBySampleIdInAndIsDeletedFalse(sampleIds) : new ArrayList<>();
            analyseDataList = analyseDataList.stream().filter(p -> sample2TestIds.contains(p.getTestId())).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(analyseDataList)) {
                List<String> testIds = analyseDataList.stream().map(DtoAnalyseData::getTestId).distinct().collect(Collectors.toList());
                List<DtoTest> allTests = StringUtil.isNotEmpty(testIds) ? testService.findAll(testIds) : new ArrayList<>();
                Map<String, DtoTest> testMap = allTests.stream().collect(Collectors.toMap(DtoTest::getId, t -> t));
                Calendar cal = Calendar.getInstance();
                analyseDataList.forEach(a -> {
                    DtoTest test = testMap.getOrDefault(a.getTestId(), null);
                    if (StringUtil.isNotNull(test) && StringUtil.isNotNull(test.getValidTime()) && test.getValidTime().compareTo(BigDecimal.valueOf(0)) > 0) {
                        cal.clear();
                        cal.setTime(receiveSampleDate);
                        cal.add(Calendar.DATE, (int) Math.ceil(test.getValidTime().doubleValue() / 24));
                        a.setRequireDeadLine(cal.getTime());
                    }
                });
                analyseDataRepository.save(analyseDataList);
            }
        }
    }


    @Override
    @Transactional
    public void updateByReceiveInfo(DtoReceiveSampleRecord receiveSampleRecord) {
        // 分批接样未否是，需要将送样单重的接样人、接样时间同步到交接单信息
        if (!receiveSampleRecord.getIsBatchReceive()) {
            // 查询接样人姓名
            String recipientId = receiveSampleRecord.getRecipientId();
            DtoPerson person = new DtoPerson();
            if (StringUtil.isNotEmpty(recipientId) && !UUIDHelper.GUID_EMPTY.equals(recipientId)) {
                person = personService.findOne(recipientId);
            }
            List<DtoSampleGroup> sampleGroupList = repository.findByReceiveId(receiveSampleRecord.getId());
            for (DtoSampleGroup p : sampleGroupList) {
                p.setRecipientName(person.getCName());
                p.setReceiveSampleDate(receiveSampleRecord.getReceiveSampleDate());
            }
            repository.save(sampleGroupList);
        }
    }

    /**
     * @param newSampleGroupList
     */
    public void batchSaveSampleGroup2Test(List<DtoSampleGroup> newSampleGroupList) {
        List<DtoSampleGroup2Test> sampleGroup2Tests = new ArrayList<>();
        List<Map<String, String>> collect = newSampleGroupList.stream().map(DtoSampleGroup::getAnalDataMap).collect(Collectors.toList());
        List<String> keyList = new ArrayList<>();
        if (StringUtil.isNotEmpty(collect.get(0))) {
            collect.forEach(p -> keyList.addAll(p.keySet()));
            List<DtoAnalyseData> analyseDataList = analyseDataRepository.findAll(keyList);
            analyseDataList.removeIf(DtoAnalyseData::getIsSamplingOut);
            Map<String, DtoAnalyseData> analyseDataMap = analyseDataList.stream().collect(Collectors.toMap(DtoAnalyseData::getId, p -> p));
            for (DtoSampleGroup sampleGroup : newSampleGroupList) {
                Map<String, String> analDataMap = sampleGroup.getAnalDataMap();
                for (String key : analDataMap.keySet()) {
                    DtoSampleGroup2Test sampleGroup2Test = new DtoSampleGroup2Test();
                    sampleGroup2Test.setSampleGroupId(sampleGroup.getId());
                    sampleGroup2Test.setTestId(analDataMap.get(key));
                    DtoAnalyseData dtoAnalyseData = analyseDataMap.get(key);
                    if (StringUtil.isNotNull(dtoAnalyseData)) {
                        sampleGroup2Test.setAnalyzeItemId(dtoAnalyseData.getAnalyseItemId());
                        sampleGroup2Test.setRedAnalyzeMethodName(dtoAnalyseData.getRedAnalyzeMethodName());
                        sampleGroup2Test.setRedCountryStandard(dtoAnalyseData.getRedCountryStandard());
                    }
                    sampleGroup2Tests.add(sampleGroup2Test);
                }
            }
            sampleGroup2TestService.save(sampleGroup2Tests);
        }
    }

    /**
     * 获取样品对应项目编号字典数据
     *
     * @param sampleIds 样品编号集合
     * @return 项目编号字典数据
     */
    protected Map<String, String> findProjectCodeMap(List<String> sampleIds) {
        //样品对应项目编号字典数据
        Map<String, String> sampleToProjectCodeMap = new HashMap<>();
        //获取所有样品数据
        List<DtoSample> samples = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(sampleIds) : new ArrayList<>();
        //样品数据字典
        Map<String, DtoSample> sampleMap = samples.stream().collect(Collectors.toMap(DtoSample::getId, dto -> dto));
        //获取所有样品数据包括关联原样数据
        List<String> associateSampleIds = samples.stream().map(DtoSample::getAssociateSampleId).distinct().collect(Collectors.toList());
        //所有的关联原样数据
        List<DtoSample> associateSamples = StringUtil.isNotEmpty(sampleIds) ? sampleRepository.findByIds(associateSampleIds) : new ArrayList<>();
        for (DtoSample associateSample : associateSamples) {
            if (!sampleMap.containsKey(associateSample.getId())) {
                sampleMap.put(associateSample.getId(), associateSample);
            }
        }
        if (StringUtil.isNotEmpty(samples)) {
            //获取所有的项目编号
            List<String> projectIds = samples.stream().map(DtoSample::getProjectId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList());
            if (StringUtil.isNotEmpty(associateSamples)) {
                projectIds.addAll(associateSamples.stream().map(DtoSample::getProjectId).filter(p -> !UUIDHelper.GUID_EMPTY.equals(p)).distinct().collect(Collectors.toList()));
            }
            List<DtoProject> projectList = StringUtil.isNotEmpty(projectIds) ? projectRepository.findAll(projectIds) : new ArrayList<>();
            Map<String, String> projectCodeMap = projectList.stream().collect(Collectors.toMap(DtoProject::getId, DtoProject::getProjectCode));
            //处理样品对应项目编号
            for (DtoSample sample : samples) {
                String projectId = sample.getProjectId();
                if (!EnumPRO.EnumSampleCategory.原样.getValue().equals(sample.getSampleCategory())) {
                    DtoSample associateSample = StringUtil.isNotEmpty(sampleMap) ? sampleMap.getOrDefault(sample.getAssociateSampleId(), null) : null;
                    projectId = StringUtil.isNotNull(associateSample) ? associateSample.getProjectId() : projectId;
                }
                String projectCode = "";
                if (StringUtil.isNotEmpty(projectCodeMap) && projectCodeMap.containsKey(projectId)) {
                    projectCode = projectCodeMap.getOrDefault(projectId, "");
                }
                sampleToProjectCodeMap.put(sample.getId(), projectCode);
            }
        }
        return sampleToProjectCodeMap;
    }

    /**
     * 获取合并的测试项目名称
     *
     * @param testList       测试项目
     * @param testIds        测试项目id
     * @param parentTestList 父测试项目
     * @return 返回数据
     */
    public static List<String> getTestTotalName(List<DtoTest> testList, List<String> testIds, List<DtoTest> parentTestList) {
        //当前遍历的测试项目集合
        List<DtoTest> curTestList = testList.stream().filter(p -> testIds.contains(p.getId())).collect(Collectors.toList());
        //按照父id分组
        Map<String, List<DtoTest>> parentId2TestListMap = curTestList.stream().collect(Collectors.groupingBy(DtoTest::getParentId));
        List<String> mergedTestNameList = new ArrayList<>();
        for (Map.Entry<String, List<DtoTest>> entry : parentId2TestListMap.entrySet()) {
            String parentId = entry.getKey();
            List<DtoTest> loopTestList = entry.getValue();
            List<String> loopAnalyzeItemNameList = new ArrayList<>();
            //获取父测试项目
            DtoTest parentTest = parentTestList.stream().filter(p -> parentId.equals(p.getId())).findFirst().orElse(null);
            if (StringUtil.isNotNull(parentTest)) {
                if (parentTest.getIsTotalTest() && loopTestList.size() > parentTest.getMergeBase()) {
                    //合并显示
                    mergedTestNameList.add(parentTest.getRedAnalyzeItemName());
                } else {
                    //存在父测试项目，是否总称开关关闭时，多个因子放在一起展示
                    for (DtoTest test : loopTestList) {
                        if (StringUtil.isNotEmpty(test.getRedAnalyzeItemName()) && !loopAnalyzeItemNameList.contains(test.getRedAnalyzeItemName())) {
                            loopAnalyzeItemNameList.add(test.getRedAnalyzeItemName());
                        }
                    }
                    mergedTestNameList.add(String.join("，", loopAnalyzeItemNameList));
                }
            } else {
                for (DtoTest test : loopTestList) {
                    if (StringUtil.isNotEmpty(test.getRedAnalyzeItemName()) && !loopAnalyzeItemNameList.contains(test.getRedAnalyzeItemName())) {
                        loopAnalyzeItemNameList.add(test.getRedAnalyzeItemName());
                    }
                }
                mergedTestNameList.addAll(loopAnalyzeItemNameList);
            }
        }
        Collator comparator = Collator.getInstance(Locale.CHINA);
        mergedTestNameList.sort(Comparator.comparing(t -> t, comparator));
        return mergedTestNameList;
    }

    /**
     * 插入修改日志
     *
     * @param updateParamInfo 修改的参数属性信息（参数名称，原来的值，新的值）
     * @param sampleGroup     修改后的样品分组信息
     */
    private void addUpdateParamLog(List<String> updateParamInfo, DtoSampleGroup sampleGroup) {
        DtoLog dtoLog = new DtoLog();
        dtoLog.setId(UUIDHelper.NewID());
        dtoLog.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
        dtoLog.setOperateTime(new Date());
        dtoLog.setNextOperatorId(UUIDHelper.GUID_EMPTY);
        dtoLog.setOpinion("");
        dtoLog.setRemark("");
        dtoLog.setObjectId(sampleGroup.getSampleId());
        dtoLog.setOperateInfo(EnumPRO.EnumLogOperateType.修改样品.toString());
        dtoLog.setLogType(EnumPRO.EnumLogType.样品信息.getValue());
        dtoLog.setObjectType(EnumPRO.EnumLogObjectType.样品.getValue());
        String comment = String.format("修改样品%s的分组参数%s为%s,原参数结果为%s", sampleGroup.getSampleCode(), updateParamInfo.get(0),
                updateParamInfo.get(2), updateParamInfo.get(1));
        log.info("comment：" + comment);
        dtoLog.setComment(comment);
        newLogService.createLog(dtoLog);
    }

    /**
     * 获取修改的属性参数名称及原来的参数值，新的值
     *
     * @param oldGroup 原有的分组信息
     * @param newGroup 新的分组信息
     * @return 修改的属性信息（参数名称，原来的值，新的值）
     */
    private List<String> getUpdateParamInfo(DtoSampleGroup oldGroup, DtoSampleGroup newGroup) {
        List<String> resList = new ArrayList<>();
        List<String[]> paramInfoList = Arrays.asList(new String[]{"fixer", "固定剂"}, new String[]{"containerName", "容器名称"}, new String[]{"saveCondition", "保存条件"},
                new String[]{"sampleStatus", "样品状态"}, new String[]{"riskDescription", "危险性描述"}, new String[]{"transportationCondition", "运输条件"},
                new String[]{"samplingBegin", "采样开始时间"}, new String[]{"samplingEnd", "采样结束时间"}, new String[]{"remark", "备注"},
                new String[]{"pretreatmentMethod", "前处理方式"}, new String[]{"sampleVolume", "采样体积"}, new String[]{"containerStatus", "采样容器状态"},
                new String[]{"sampleNum", "样品数"}, new String[]{"recipientName", "接样人名称"}, new String[]{"receiveSampleDate", "接样日期"});
        for (String[] paramInfo : paramInfoList) {
            Field field = getFieldByName(paramInfo[0]);
            try {
                String oldVal = String.valueOf(field.get(oldGroup));
                oldVal = StringUtil.isNotEmpty(oldVal) ? oldVal : "";
                String newVal = String.valueOf(field.get(newGroup));
                newVal = StringUtil.isNotEmpty(newVal) ? newVal : "";
                if (!newVal.equals(oldVal)) {
                    //新的值和原来的值不一样，则认定当前操作修改了该属性的值（默认每修改一个数据保存一次）
                    resList = Arrays.asList(paramInfo[1], oldVal, newVal);
                    break;
                }
            } catch (IllegalAccessException e) {
                throw new BaseException("异常错误!");
            }
        }
        return resList;
    }

    /**
     * 根据属性名称获取属性对象
     *
     * @param propertyName 属性名称
     * @return 属性对象
     */
    private Field getFieldByName(String propertyName) {
        try {
            Field field = SampleGroup.class.getDeclaredField(propertyName);
            field.setAccessible(true);
            return field;
        } catch (Exception e) {
            throw new BaseException("异常错误!");
        }
    }

    /**
     * 按条件对样品分组信息进行过滤
     *
     * @param recordCriteria  查询条件
     * @param sampleGroupList 样品分组列表
     * @return 样品分组列表
     */
    private List<DtoSampleGroup> filterAndSortSampleGroups(SampleGroupRecordCriteria recordCriteria, List<DtoSampleGroup> sampleGroupList) {
        if (StringUtil.isNotEmpty(recordCriteria.getSampleCodeFolderName())) {
            sampleGroupList = sampleGroupList.stream().filter(p -> p.getSampleCode().contains(recordCriteria.getSampleCodeFolderName())
                    || p.getRedFolderName().contains(recordCriteria.getSampleCodeFolderName())).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(recordCriteria.getSampleCode())) {
            sampleGroupList = sampleGroupList.stream().filter(p -> p.getSampleCode().contains(recordCriteria.getSampleCode())).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(recordCriteria.getGroupName())) {
            sampleGroupList = sampleGroupList.stream().filter(p -> p.getSampleTypeGroupName().contains(recordCriteria.getGroupName())).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(recordCriteria.getAnalyzeItemName())) {
            sampleGroupList = sampleGroupList.stream().filter(p -> p.getAnalyseItemNames().contains(recordCriteria.getAnalyzeItemName())).collect(Collectors.toList());
        }
        if (StringUtil.isNotEmpty(recordCriteria.getMobileKey())) {
            sampleGroupList = sampleGroupList.stream().filter(p -> p.getSampleCode().contains(recordCriteria.getMobileKey())
                    || p.getAnalyseItemNames().contains(recordCriteria.getMobileKey())).collect(Collectors.toList());
        }
        if (StringUtil.isEmpty(recordCriteria.getSortType())) {
            sampleGroupList.sort(Comparator.comparing(DtoSampleGroup::getSampleCode).thenComparing(DtoSampleGroup::getSampleTypeGroupName));
        } else if ("1".equals(recordCriteria.getSortType())) {
            //按照样品分组排序
            sampleGroupList.sort(Comparator.comparing(DtoSampleGroup::getSampleTypeGroupName).thenComparing(DtoSampleGroup::getAnalyseItemNames)
                    .thenComparing(DtoSampleGroup::getSampleCode));
        } else {
            //按样品编号排序
            sampleGroupList.sort(Comparator.comparing(DtoSampleGroup::getSampleCode).thenComparing(DtoSampleGroup::getSampleTypeGroupName)
                    .thenComparing(DtoSampleGroup::getAnalyseItemNames));
        }
        return sampleGroupList;
    }

    /**
     * 设置新增分组数据
     *
     * @param dtoSample                 样品信息
     * @param oldSampleGroupList        已经存在的分组信息
     * @param sampleLabelData           样品标签数据
     * @param analyseItemNames          测试项目名称
     * @param marks                     标记
     * @param needDeleteSampleGroupList 需要删除的分组信息列表
     * @param newSampleGroupList        需要新增的分组信息列表
     */
    @Override
    public void setGroupMsg(DtoSample dtoSample, List<DtoSampleGroup> oldSampleGroupList, List<DtoSampleGroup> remainOldSampleGroupList,
                            DtoSampleLabelData sampleLabelData, String analyseItemNames,
                            List<String> marks, List<DtoSampleGroup> needDeleteSampleGroupList,
                            List<DtoSampleGroup> newSampleGroupList, Integer isGroup, List<DtoSampleLabelData> dataList) {
        //获取已存在的分组信息
        List<DtoSampleGroup> oldGroupInfoListForSample = oldSampleGroupList.parallelStream()
                .filter(p -> p.getSampleId().equals(sampleLabelData.getSampleId())).collect(Collectors.toList());
        Map<String, String> analDataMap = dataList.stream().collect(Collectors.toMap(DtoSampleLabelData::getAnaId, DtoSampleLabelData::getTestId));
        Optional<DtoSampleGroup> oldSampleGroupOptional = oldGroupInfoListForSample.parallelStream()
                .filter(p -> p.getSampleTypeGroupId().equals(sampleLabelData.getGroupId())
                        && p.getAnalyseItemNames().equals(analyseItemNames)).findFirst();
        oldSampleGroupOptional.ifPresent(remainOldSampleGroupList::remove);

        //是否需要更新标记，默认需要
        Boolean needUpdate = Boolean.TRUE;
        //存在旧的分组信息
        if (oldSampleGroupOptional.isPresent()) {
            DtoSampleGroup oldSampleGroup = oldSampleGroupOptional.get();
            //比较分组信息有没有更改，无更改则 needUpdate标记成false
            if (oldSampleGroup.getSampleId().equals(sampleLabelData.getSampleId())
                    && oldSampleGroup.getReceiveId().equals(sampleLabelData.getReceiveId())
                    && oldSampleGroup.getAnalyseItemNames().equals(analyseItemNames)
                    && oldSampleGroup.getSampleTypeGroupId().equals(sampleLabelData.getGroupId())
                    && oldSampleGroup.getSampleTypeGroupName().equals(sampleLabelData.getGroupName())
                    && sampleLabelData.getContainerName().equals(oldSampleGroup.getContainerName())
                    && sampleLabelData.getFixer().equals(oldSampleGroup.getFixer())
                    && sampleLabelData.getRemark().equals(oldSampleGroup.getRemark())
                    && sampleLabelData.getSaveCondition().equals(oldSampleGroup.getSaveCondition())
                    && sampleLabelData.getContainerStatus().equals(oldSampleGroup.getContainerStatus())
                    && sampleLabelData.getPretreatmentMethod().equals(oldSampleGroup.getPretreatmentMethod())
                    && sampleLabelData.getSampleVolume().equals(oldSampleGroup.getSampleVolume())
                    && oldSampleGroupList.size() == marks.size()) {
                needUpdate = Boolean.FALSE;
            } else {
                //分组信息有更改，需要删除旧的分组信息
                needDeleteSampleGroupList.add(oldSampleGroup);
            }
        }
        if (needUpdate) {
            DtoSampleGroup dtoSampleGroup = new DtoSampleGroup();
            dtoSampleGroup.setSampleId(dtoSample.getId());
            dtoSampleGroup.setReceiveId(dtoSample.getReceiveId());
            dtoSampleGroup.setSampleTypeGroupId(sampleLabelData.getGroupId());
            dtoSampleGroup.setSampleTypeGroupName(sampleLabelData.getGroupName());
            dtoSampleGroup.setRemark(sampleLabelData.getRemark());
            dtoSampleGroup.setFixer(sampleLabelData.getFixer());
            dtoSampleGroup.setContainerName(sampleLabelData.getContainerName());
            dtoSampleGroup.setPretreatmentMethod(sampleLabelData.getPretreatmentMethod());
            dtoSampleGroup.setSampleVolume(sampleLabelData.getSampleVolume());
            dtoSampleGroup.setContainerStatus(sampleLabelData.getContainerStatus());
            dtoSampleGroup.setSaveCondition(sampleLabelData.getSaveCondition());
            dtoSampleGroup.setHasScanned(Boolean.FALSE);
            dtoSampleGroup.setAnalyseItemNames(analyseItemNames);
            // 设置分组标识
            dtoSampleGroup.setIsGroup(isGroup);
            // 冗余分析项目数量
            dtoSampleGroup.setAnalyseNums(dataList.size());
            dtoSampleGroup.setAnalDataMap(analDataMap);
            // 之前填写的分组信息重新赋值
            if (oldSampleGroupOptional.isPresent() && (sampleLabelData.getGroupName().equals(oldSampleGroupOptional.get().getSampleTypeGroupName()) ||
                    "/".equals(oldSampleGroupOptional.get().getSampleTypeGroupName()))) {
                DtoSampleGroup sampleGroup = oldSampleGroupOptional.get();
                dtoSampleGroup.setSampleStatus(sampleGroup.getSampleStatus());
                dtoSampleGroup.setRiskDescription(sampleGroup.getRiskDescription());
                dtoSampleGroup.setTransportationCondition(sampleGroup.getTransportationCondition());
                dtoSampleGroup.setSampleNum(sampleGroup.getSampleNum());
                dtoSampleGroup.setSampleVolume(sampleGroup.getSampleVolume());
                dtoSampleGroup.setRecipientName(sampleGroup.getRecipientName());
                dtoSampleGroup.setReceiveSampleDate(sampleGroup.getReceiveSampleDate());
            }
            newSampleGroupList.add(dtoSampleGroup);
        }
    }

    @Override
    public Boolean checkExist(String receiveId) {
        List<DtoSampleGroup> sampleGroupList = repository.findByReceiveId(receiveId);
        return StringUtil.isNotEmpty(sampleGroupList);
    }


    /**
     * 根据样本信息和关联样本的查找逻辑，生成并设置带标签的样本编码。
     *
     * @param setSampleCodeWithTag       一个函数 (Consumer)，用于设置最终生成的带标签的编码。例如：s -> evaluate.setSampleCodeWithTag(s)
     * @param currentSampleCode          当前样本的编码。
     * @param sampleCodeTag              要应用的样本标签。
     * @param sampleCategory             当前样本的类别。
     * @param associateSampleId          关联样本的ID，仅在类别为“串联样”时使用。
     * @param associatedSampleCodeFinder 一个函数 (Function)，用于根据关联ID查找并返回关联样本的编码。
     */
    @Override
    public void generateAndSetTaggedSampleCode(
            java.util.function.Consumer<String> setSampleCodeWithTag,
            String currentSampleCode,
            String sampleCodeTag,
            Integer sampleCategory,
            String associateSampleId,
            Function<String, Optional<String>> associatedSampleCodeFinder) {
        // 假设 StringUtil.isNotEmpty 的实现是检查字符串是否为 null 或空
        if (sampleCodeTag != null && !sampleCodeTag.isEmpty()) {
            // 假设 EnumPRO.EnumSampleCategory.串联样.getValue() 返回一个字符串值
            if (!sampleCategory.equals(EnumPRO.EnumSampleCategory.串联样.getValue())) {
                // 非串联样：直接拼接
                setSampleCodeWithTag.accept(currentSampleCode + "-" + sampleCodeTag);
            } else {
                // 串联样：通过传入的 finder 函数查找关联样本并构建新编码
                Optional<String> associatedSampleCodeOpt = associatedSampleCodeFinder.apply(associateSampleId);
                if (associatedSampleCodeOpt.isPresent()) {
                    String associatedSampleCode = associatedSampleCodeOpt.get();
                    // 计算出剩余部分
                    String remainderCode = currentSampleCode.replace(associatedSampleCode, "");
                    // 构建新编码
                    setSampleCodeWithTag.accept(associatedSampleCode + "-" + sampleCodeTag + remainderCode);
                }
            }
        }
    }


    @Autowired
    public void setSampleRepository(SampleRepository sampleRepository) {
        this.sampleRepository = sampleRepository;
    }

    @Autowired
    public void setNewLogService(NewLogService newLogService) {
        this.newLogService = newLogService;
    }

    @Autowired
    public void setProjectRepository(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Autowired
    public void setTestRepository(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    @Autowired
    public void setCommonRepository(CommonRepository commonRepository) {
        this.commonRepository = commonRepository;
    }

    @Autowired
    public void setSampleTypeGroup2TestRepository(SampleTypeGroup2TestRepository sampleTypeGroup2TestRepository) {
        this.sampleTypeGroup2TestRepository = sampleTypeGroup2TestRepository;
    }

    @Autowired
    public void setAnalyseDataRepository(AnalyseDataRepository analyseDataRepository) {
        this.analyseDataRepository = analyseDataRepository;
    }

    @Autowired
    public void setSampleGroup2TestRepository(SampleGroup2TestRepository sampleGroup2TestRepository) {
        this.sampleGroup2TestRepository = sampleGroup2TestRepository;
    }

    @Autowired
    public void setSampleGroup2TestService(SampleGroup2TestService sampleGroup2TestService) {
        this.sampleGroup2TestService = sampleGroup2TestService;
    }

    @Autowired
    @Lazy
    public void setReceiveSampleRecordService(ReceiveSampleRecordService receiveSampleRecordService) {
        this.receiveSampleRecordService = receiveSampleRecordService;
    }

    @Autowired
    public void setSampleTypeGroupRepository(SampleTypeGroupRepository sampleTypeGroupRepository) {
        this.sampleTypeGroupRepository = sampleTypeGroupRepository;
    }

    @Autowired
    @Lazy
    public void setPersonService(PersonService personService) {
        this.personService = personService;
    }

    @Autowired
    @Lazy
    public void setTestService(TestService testService) {
        this.testService = testService;
    }
}