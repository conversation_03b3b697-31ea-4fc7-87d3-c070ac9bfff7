package com.sinoyd.lims.pro.service.impl;

import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.core.BaseCodeHelper;
import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.repository.lims.DocumentRepository;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.lims.pro.dto.customer.DtoSigDocument;
import com.sinoyd.lims.pro.service.LuckySheetService;
import com.sinoyd.lims.pro.service.SignatureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * luckysheet操作服务
 * <AUTHOR>
 * @version V1.0.0 2022/07/29
 * @since V100R001
 */
@Service
@Slf4j
public class LuckySheetServiceImpl implements LuckySheetService {

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    private SignatureService signatureService;

    @Override
    public String insertSig2Excel(String documentId, List<DtoSigDocument> sigDocumentList) {
        DtoDocument document = documentRepository.findOne(documentId);
        if (StringUtil.isNull(document)) {
            Optional<DtoDocument> documentOptional = documentRepository.findByFolderId(documentId).stream()
                    .filter(p -> p.getDocTypeId().equals(BaseCodeHelper.DOCUMENT_EXTEND_ORIGINAL_RECORD))
                    .max(Comparator.comparing(DtoDocument::getCreateDate));
            if (documentOptional.isPresent()) {
                document = documentOptional.get();
            }
        }
        String path = filePathConfig.getFilePath() + document.getPath();
        for(DtoSigDocument sigDocument : sigDocumentList){
            sigDocument.setIsSplitDate(false);
            sigDocument.setSigIndex(0);
        }
        try {
            signatureService.sigByDocument(path,sigDocumentList);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        return null;
    }
}
