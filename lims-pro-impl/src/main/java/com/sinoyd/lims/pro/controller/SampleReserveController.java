package com.sinoyd.lims.pro.controller;

import com.sinoyd.base.dto.rcc.DtoAnalyzeItem;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.pro.criteria.SampleReserveCriteria;
import com.sinoyd.lims.pro.dto.DtoSample;
import com.sinoyd.lims.pro.dto.DtoSampleReserve;
import com.sinoyd.lims.pro.service.SampleReserveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * SampleReserve服务接口定义
 * <AUTHOR>
 * @version V5.2.0 2022/6/21
 */
@Api(tags = "示例: SampleReserve服务")
@RestController
@RequestMapping("api/pro/sampleReserve")
public class SampleReserveController extends BaseJpaController<DtoSampleReserve, String, SampleReserveService> {

    /**
     * 查询样品交接提交后的所有样品
     *
     * @return 查询结果
     */
    @ApiModelProperty(name = "查询样品交接提交后的所有样品",notes = "查询样品交接提交后的所有样品")
    @GetMapping("/samples")
    public RestResponse<List<DtoSample>> findSamples(SampleReserveCriteria criteria){
        PageBean<DtoSample> pageBean = super.getPageBean();
        RestResponse<List<DtoSample>> response = new RestResponse<>();
        service.findSamplesByPage(pageBean, criteria);
        response.setData(pageBean.getData());
        response.setCount(pageBean.getRowsCount());
        response.setMsg("查询成功");
        return response;
    }

    /**
     * 查询样品交接提交后的所有样品
     *
     * @return 查询结果
     */
    @ApiModelProperty(name = "查询样品交接提交后的所有样品",notes = "查询样品交接提交后的所有样品")
    @GetMapping("/detail/{sampleId}")
    public RestResponse<DtoSample> findSampleDetail(@PathVariable(name = "sampleId") String sampleId){
        RestResponse<DtoSample> response = new RestResponse<>();
        response.setData(service.findSampleDetail(sampleId));
        response.setMsg("查询成功");
        return response;
    }

    /**
     * 查询所选择的样品列表的留样信息
     *
     * @return 查询结果
     */
    @ApiModelProperty(name = "查询所选择的样品列表的留样信息",notes = "查询所选择的样品列表的留样信息")
    @PostMapping("/details")
    public RestResponse<Map<String, Object>> findSampleDetails(@RequestBody List<String> sampleIds){
        RestResponse<Map<String, Object>> response = new RestResponse<>();
        response.setData(service.findSampleDetails(sampleIds));
        response.setMsg("查询成功");
        return response;
    }

    /**
     * 保存数据（领取/处置）
     *
     * @param reserve 需要保存的数据
     * @return 保存成功的数据
     */
    @ApiModelProperty(name = "保存数据",notes = "保存数据")
    @PostMapping
    public RestResponse<List<DtoSampleReserve>> save(@RequestBody DtoSampleReserve reserve){
        RestResponse<List<DtoSampleReserve>> response = new RestResponse<>();
        response.setData(service.saveReserve(reserve));
        response.setMsg("保存成功");
        return response;
    }


    /**
     * 判断数据是否存在（领取/处置）
     *
     * @param reserve 需要保存的数据
     * @return 保存成功的数据
     */
    @ApiModelProperty(name = "判断数据是否存在",notes = "判断数据是否存在")
    @PostMapping("/judge")
    public RestResponse<List<DtoSampleReserve>> judgeSaveData(@RequestBody DtoSampleReserve reserve){
        RestResponse<List<DtoSampleReserve>> response = new RestResponse<>();
        List<DtoSampleReserve> dtoSampleReserves = service.judgeSaveData(reserve);
        response.setData(dtoSampleReserves);
        response.setCount(dtoSampleReserves.size());
        response.setMsg(StringUtil.isNotEmpty(dtoSampleReserves)?"查询成功":"不存在已保存的数据");
        return response;
    }

    /**
     * 根据样品Id获取对于的测试项目
     *
     * @param sampleIds 样品Id
     * @return 结果
     */
    @ApiModelProperty(name = "根据样品Id获取对于的测试项目",notes = "根据样品Id获取对于的测试项目")
    @PostMapping("/analyzeItems")
    public RestResponse<List<DtoAnalyzeItem>> findTestBySampleIds(@RequestBody List<String> sampleIds){
        RestResponse<List<DtoAnalyzeItem>> response = new RestResponse<>();
        List<DtoAnalyzeItem> tests = service.findAnalyzeItemBySampleIds(sampleIds);
        response.setData(tests);
        response.setCount(tests.size());
        response.setMsg("查询成功");
        return response;
    }

    /**
     * 取消样品处置
     *
     * @param sampleIds 样品Id
     * @return 取消的数量
     */
    @ApiModelProperty(name = "取消样品处置",notes = "取消样品处置")
    @PutMapping
    public RestResponse<Integer> cancelDispose(@RequestBody List<String> sampleIds){
        RestResponse<Integer> response = new RestResponse<>();
        response.setData(service.cancelDispose(sampleIds));
        response.setMsg("操作成功");
        return response;
    }
}
