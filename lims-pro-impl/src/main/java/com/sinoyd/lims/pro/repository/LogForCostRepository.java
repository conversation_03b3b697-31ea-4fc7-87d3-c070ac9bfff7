package com.sinoyd.lims.pro.repository;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.pro.dto.DtoLogForCost;

import java.util.List;


/**
 * LogForProject数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2019/12/12
 * @since V100R001
 */
public interface LogForCostRepository extends IBaseJpaPhysicalDeleteRepository<DtoLogForCost, String> {

    List<DtoLogForCost> findByObjectId(String objectId);

    List<DtoLogForCost> findByObjectIdIn(List<String> objectIds);
}