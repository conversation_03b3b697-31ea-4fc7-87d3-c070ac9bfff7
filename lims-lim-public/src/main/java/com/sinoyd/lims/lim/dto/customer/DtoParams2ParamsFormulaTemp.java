package com.sinoyd.lims.lim.dto.customer;
import com.sinoyd.lims.lim.dto.rcc.DtoParams2ParamsFormula;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsPartFormula;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *  个性化的参数
 * <AUTHOR>
 * @version V1.0.0 2020/10/15
 * @since V100R001
 */
@Data
public class DtoParams2ParamsFormulaTemp {

    /**
     * 公式
     */
    private DtoParams2ParamsFormula params2ParamsFormula;


    /**
     * 修约公式
     */
    private  List<DtoParamsPartFormula> paramsPartFormulas=new ArrayList<>();


    /**
     * 参数
     */
    private List<DtoParamsConfig> paramsConfigs = new ArrayList<>();
}
