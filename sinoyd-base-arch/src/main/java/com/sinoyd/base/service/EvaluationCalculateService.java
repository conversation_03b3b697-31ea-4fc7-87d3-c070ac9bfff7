package com.sinoyd.base.service;

import com.sinoyd.base.dto.customer.EvaluationIntervalVO;

import java.util.List;

/**
 * 评价标准计算服务
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2024/03/13
 */
public interface EvaluationCalculateService {

    /**
     * 判断数值是否满足区间限值
     *
     * @param value       需要判断的值
     * @param evaInterval 限值数据
     * @return 是否满足
     */
    Boolean judgeIsPass(String value, EvaluationIntervalVO evaInterval);

    /**
     * 获取评价限制下满足值区间的评价条件ID
     *
     * @param analyzeItemId 当前判断的分析项目id
     * @param value         当前判断的数据值
     * @param evaValues     评价限值集合
     * @return 满足值区间的评价条件ID
     */
    String findIntervalEvaLevelId(String analyzeItemId, String value, List<EvaluationIntervalVO> evaValues);
}
