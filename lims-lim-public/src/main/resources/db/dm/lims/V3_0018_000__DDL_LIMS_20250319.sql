CREATE TABLE "TB_BASE_LOGFORDOCUMENT"
(
    "id"           VARCHAR(50) NOT NULL,
    "operatorId"   VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "operatorName" VARCHAR(50) NULL,
    "operateTime"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
                               NOT NULL,
    "operateInfo"  VARCHAR(500) NULL,
    "objectId"     VARCHAR(50)  DEFAULT ''
                               NOT NULL,
    "orgId"        VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL,
    "domainId"     VARCHAR(50)  DEFAULT '00000000-0000-0000-0000-000000000000'
                               NOT NULL
);
ALTER TABLE "TB_BASE_LOGFORDOCUMENT" ADD CONSTRAINT PRIMARY KEY ("id");
COMMENT
ON TABLE "TB_BASE_LOGFORDOCUMENT" IS '附件下载日志';
COMMENT
ON COLUMN "TB_BASE_LOGFORDOCUMENT"."id" IS 'id';
COMMENT
ON COLUMN "TB_BASE_LOGFORDOCUMENT"."operatorId" IS '操作者Id';
COMMENT
ON COLUMN "TB_BASE_LOGFORDOCUMENT"."operatorName" IS '操作者名字';
COMMENT
ON COLUMN "TB_BASE_LOGFORDOCUMENT"."operateTime" IS '操作时间';
COMMENT
ON COLUMN "TB_BASE_LOGFORDOCUMENT"."operateInfo" IS '操作信息';
COMMENT
ON COLUMN "TB_BASE_LOGFORDOCUMENT"."objectId" IS '附件id';
COMMENT
ON COLUMN "TB_BASE_LOGFORDOCUMENT"."orgId" IS '组织机构id';
COMMENT
ON COLUMN "TB_BASE_LOGFORDOCUMENT"."domainId" IS '所属实验室id';