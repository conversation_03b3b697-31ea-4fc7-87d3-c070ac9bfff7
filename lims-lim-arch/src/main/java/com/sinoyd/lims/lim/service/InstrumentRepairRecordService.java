package com.sinoyd.lims.lim.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentRepairRecord;

import java.util.List;

/**
 * 仪器维修配置
 * <AUTHOR>
 * @version V1.0.0 2019/3/5
 * @since V100R001
 */
public interface InstrumentRepairRecordService extends IBaseJpaService<DtoInstrumentRepairRecord, String> {
    /**
     *  根据申请单id查询仪器维修记录
     * @param purchaseApplyID 申请单id
     * @return 仪器维修集合
     */
    List<DtoInstrumentRepairRecord> findByPurchaseApplyID(String purchaseApplyID);
}