package com.sinoyd.lims.lim.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 测试公式查询条件
 *
 * <AUTHOR>
 * @version v1.0.0 2019/5/6
 * @since v100R001
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TestFormulaCriteria extends BaseCriteria implements Serializable {

    /**
     * 检测类型
     */
    private String sampleTypeId;

    /**
     * 关键字（分析方法，标准编号）
     */
    private String key;

    /**
     * 分析项目
     */
    private String redAnalyzeItemName;

    /**
     * 公式
     */
    private String formula;

    /**
     * 是否只做下来框数据绑定
     */
    private Boolean isCombo = false;

    /**
     * 测试项目ids
     */
    private List<String> testIds;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 公式ids
     */
    private List<String> ids;

    /**
     * 是否导出参数扩展
     */
    private Boolean isExportExtend;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.objectId = b.id");
        condition.append(" and a.sampleTypeId = c.id");

        if (StringUtils.isNotNullAndEmpty(key)) {
            condition.append(" and ( b.redAnalyzeMethodName like :key or b.redCountryStandard like :key )");
            values.put("key", "%" + this.key + "%");
        }
        if (StringUtils.isNotNullAndEmpty(redAnalyzeItemName)) {
            condition.append(" and ( b.redAnalyzeItemName like :redAnalyzeItemName )");
            values.put("redAnalyzeItemName", "%" + this.redAnalyzeItemName + "%");
        }
        if (StringUtils.isNotNullAndEmpty(formula)) {
            condition.append(" and ( a.formula like :formula )");
            values.put("formula", "%" + this.formula + "%");
        }

        if (StringUtils.isNotNullAndEmpty(sampleTypeId) && !sampleTypeId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and a.sampleTypeId = :sampleTypeId ");
            values.put("sampleTypeId", this.sampleTypeId);
        }

        if (StringUtil.isNotNull(testIds) && testIds.size() > 0) {
            condition.append(" and a.objectId in :testIds ");
            values.put("testIds", this.testIds);
        }
        if (StringUtils.isNotNullAndEmpty(testId) && !testId.equals(UUIDHelper.GUID_EMPTY)) {
            condition.append(" and a.objectId = :testId ");
            values.put("testId", this.testId);
        }
        if (StringUtils.isNotNullAndEmpty(ids)) {
            condition.append(" and a.id in :ids ");
            values.put("ids", this.ids);
        }

        condition.append(" and a.objectType = :objectType");
        values.put("objectType", EnumLIM.EnumParamsFormulaObjectType.测试公式.getValue());
        condition.append(" and a.isDeleted = 0");
        condition.append(" and b.isDeleted = 0");
        return condition.toString();
    }
}
