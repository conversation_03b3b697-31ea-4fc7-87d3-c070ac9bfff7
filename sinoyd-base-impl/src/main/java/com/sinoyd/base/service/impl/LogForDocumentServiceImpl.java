package com.sinoyd.base.service.impl;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.base.dto.lims.DtoLogForDocument;
import com.sinoyd.base.repository.lims.LogForDocumentRepository;
import com.sinoyd.base.service.LogForDocumentService;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class LogForDocumentServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoLogForDocument, String, LogForDocumentRepository> implements LogForDocumentService {

    @Override
    @Transactional
    public void saveLog(List<DtoDocument> documentList, String type) {
        List<DtoLogForDocument> dtoLogForDocumentList = new ArrayList<>();
        documentList.forEach(document -> {
            DtoLogForDocument logForDocument = new DtoLogForDocument();
            logForDocument.setObjectId(document.getId());
            logForDocument.setOperateTime(new Date());
            logForDocument.setOperatorId(PrincipalContextUser.getPrincipal().getUserId());
            logForDocument.setOperatorName(PrincipalContextUser.getPrincipal().getUserName());
            logForDocument.setOperateInfo(String.format("%s下载了%s文件", PrincipalContextUser.getPrincipal().getUserName(), type));
            dtoLogForDocumentList.add(logForDocument);
        });
        repository.save(dtoLogForDocumentList);
    }

    /**
     * 根据objectId获取日志
     *
     * @param documentId 附件id
     * @return 日志集合
     */
    @Override
    public List<DtoLogForDocument> findByDocumentId(String documentId) {
        return repository.findByObjectId(documentId);
    }
}
