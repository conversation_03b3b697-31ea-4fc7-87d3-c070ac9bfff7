package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.ParamsConfigCriteria;
import com.sinoyd.lims.lim.dto.customer.DtoDetailDataSelect;
import com.sinoyd.lims.lim.dto.rcc.DtoParamsConfig;
import com.sinoyd.lims.lim.service.ParamsConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数配置管理控制器
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Api(tags = "参数配置管理: 参数配置管理服务")
@RestController
@RequestMapping("/api/lim/paramsConfig")
@Validated
public class ParamsConfigController extends BaseJpaController<DtoParamsConfig, String, ParamsConfigService> {

    /**
     * 根据id获取参数配置
     *
     * @param id 参数配置id
     * @return 参数配置实体
     */
    @ApiOperation(value = "按主键获取参数配置", notes = "按主键获取参数配置")
    @GetMapping("/{id}")
    public RestResponse<DtoParamsConfig> getById(@PathVariable String id) {
        RestResponse<DtoParamsConfig> restResponse = new RestResponse<>();
        DtoParamsConfig params = service.findOne(id);
        restResponse.setData(params);
        restResponse.setRestStatus(StringUtil.isNull(params) ?
                ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResponse;
    }

    /**
     * 分页查询参数配置
     *
     * @param paramsCriteria 参数配置列表:关键字(参数配置名称、参数配置编号、变量名称),排序,分页
     * @return 参数配置List
     */
    @ApiOperation(value = "分页动态条件获取参数配置", notes = "分页动态条件获取参数配置")
    @GetMapping("")
    public RestResponse<List<DtoParamsConfig>> findByPage(ParamsConfigCriteria paramsCriteria) {
        RestResponse<List<DtoParamsConfig>> restResponse = new RestResponse<>();
        PageBean<DtoParamsConfig> pageBean = super.getPageBean();
        service.findByPage(pageBean, paramsCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());

        return restResponse;
    }

    @ApiOperation(value = "查询检测类型参数", notes = "查询检测类型参数")
    @GetMapping("/sampleType")
    public RestResponse<List<DtoParamsConfig>> find(DtoDetailDataSelect params) {
        RestResponse<List<DtoParamsConfig>> restResponse = new RestResponse<>();
        List<DtoParamsConfig> paramsConfigs = service.findSampleTypeParams(params.getObjIds(), params.getParentObjIds(), params.getType());
        restResponse.setRestStatus(StringUtil.isEmpty(paramsConfigs) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(paramsConfigs);
        restResponse.setCount(paramsConfigs.size());
        return restResponse;
    }

    /**
     * 新增参数配置
     *
     * @param params 参数配置实体
     * @return 新增的参数配置实体
     */
    @ApiOperation(value = "新增参数配置", notes = "新增参数配置")
    @PostMapping("")
    public RestResponse<DtoParamsConfig> create(@Validated @RequestBody DtoParamsConfig params) {
        RestResponse<DtoParamsConfig> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParamsConfig data = service.save(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新参数配置（包含相关分析项目配置的更新）
     *
     * @param params 参数配置实体
     * @return 更新后的参数配置实体
     */
    @ApiOperation(value = "更新参数配置", notes = "更新参数配置")
    @PostMapping("/updateConfig")
    public RestResponse<DtoParamsConfig> updateConfig(@Validated @RequestBody DtoParamsConfig params) {
        RestResponse<DtoParamsConfig> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParamsConfig data = service.updateConfig(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新参数配置(基础更新)
     *
     * @param params 参数配置实体
     * @return 更新后的参数配置实体
     */
    @ApiOperation(value = "更新参数配置", notes = "更新参数配置")
    @PutMapping("")
    public RestResponse<DtoParamsConfig> update(@Validated @RequestBody DtoParamsConfig params) {
        RestResponse<DtoParamsConfig> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoParamsConfig data = service.update(params);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id 参数配置id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除参数配置", notes = "根据id批量删除参数配置")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(service.logicDeleteById(id));
        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids 参数配置ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除参数配置", notes = "根据id批量删除参数配置")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setCount(service.logicDeleteById(ids));
        return restResponse;
    }

    @ApiOperation(value = "根据testId批量复制参数配置", notes = "根据testId批量复制参数配置")
    @PostMapping("/copy/{testId}")
    public RestResponse<String> copy(@PathVariable String testId, @RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.copyParamsConfig(testId, ids);
        return restResponse;
    }

    /**
     * 原始记录单数据参数一键配置功能
     * 功能描述：将同名的测试项目公式参数的公式内容同步到个性化公式中
     * @param paramsConfigIdList    数据参数标识列表
     * @return                      响应
     */
    @ApiOperation(value = "原始记录单数据参数一键配置功能", notes = "原始记录单数据参数一键配置功能")
    @PostMapping("/formulaSync/{recordConfigId}")
    public RestResponse<Void> formulaSync(@PathVariable String recordConfigId,@RequestBody List<String> paramsConfigIdList){
        RestResponse<Void> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.formulaSync(recordConfigId,paramsConfigIdList);
        return restResponse;
    }

    /**
     * 复制检测类型参数
     *
     * @param objectId 检测类型标识
     * @param list 参数列表
     * @return 参数列表
     */
    @ApiOperation(value = "复制检测类型参数", notes = "复制检测类型参数")
    @PostMapping("/copyInType/{objectId}")
    public RestResponse<List<DtoParamsConfig>> copyParamsConfigList(@PathVariable String objectId, @Validated @RequestBody List<DtoParamsConfig> list) {
        RestResponse<List<DtoParamsConfig>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.copyParamsConfigList(objectId,list));
        return restResponse;
    }
}