-- 删除采样方法表
DROP TABLE IF EXISTS TB_LIM_SamplingMethod;

-- 分析方法新增字段
ALTER TABLE TB_LIM_AnalyzeMethod
    ADD COLUMN isSamplingMethod bit DEFAULT 0 NOT NULL;
COMMENT
ON COLUMN TB_LIM_AnalyzeMethod.isSamplingMethod is '是否采样方法';

ALTER TABLE TB_LIM_AnalyzeMethod
    ADD COLUMN sampleTypeId varchar(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NUll;
COMMENT
ON COLUMN  TB_LIM_AnalyzeMethod.sampleTypeId is '检测类型id';

ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN sampleTypeId varchar(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL;
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN samplingMethodId varchar(50) DEFAULT '00000000-0000-0000-0000-000000000000' NOT NULL;
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN abilityType varchar(100) DEFAULT '' NOT NULL;
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN redAnalyzeItemName varchar(255) NULL;
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN redAnalyzeMethodName varchar(255) NULL;
ALTER TABLE TB_LIM_PersonAbility
    ADD COLUMN redCountryStandard varchar(200) NULL;



COMMENT
ON COLUMN   TB_LIM_PersonAbility.sampleTypeId is '检测类型id';
COMMENT
ON COLUMN   TB_LIM_PersonAbility.samplingMethodId is '采样方法id';
COMMENT
ON COLUMN  TB_LIM_PersonAbility.redAnalyzeItemName is '项目名称';
COMMENT
ON COLUMN  TB_LIM_PersonAbility.redAnalyzeMethodName is '方法标准名称';
COMMENT
ON COLUMN  TB_LIM_PersonAbility.redCountryStandard is '标准编号';
COMMENT
ON COLUMN  TB_LIM_PersonAbility.abilityType is '能力类型';


-- 刷新人员能力类型
UPDATE TB_LIM_PersonAbility
set abilityType = '6';
