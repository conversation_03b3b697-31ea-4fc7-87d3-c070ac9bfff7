package com.sinoyd.lims.pro.enums;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.lim.enums.EnumLIM;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class EnumPRO {

    //#region 项目

    /**
     * 项目状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectStatus {
        项目登记中(100, EnumLIM.EnumHomeTaskModule.项目登记.getValue()),

        项目退回中(101, EnumLIM.EnumHomeTaskModule.方案编制.getValue()),

        审核未通过(200, EnumLIM.EnumHomeTaskModule.项目登记.getValue()),

        技术审核中(300, EnumLIM.EnumHomeTaskModule.项目审核.getValue()),

        项目下达中(400, EnumLIM.EnumHomeTaskModule.技术下达.getValue()),

        方案编制中(401, EnumLIM.EnumHomeTaskModule.方案编制.getValue()),

        方案未通过(402, EnumLIM.EnumHomeTaskModule.方案编制.getValue()),

        方案审核中(403, EnumLIM.EnumHomeTaskModule.方案审核.getValue()),

        方案确认中(404, EnumLIM.EnumHomeTaskModule.方案确认.getValue()),

        开展中(500, ""),

        数据汇总中(600, EnumLIM.EnumHomeTaskModule.数据汇总.getValue()),

        结果评价中(700, EnumLIM.EnumHomeTaskModule.评价结果.getValue()),

        已下达(750, ""),

        已办结(800, EnumLIM.EnumHomeTaskModule.任务办结.getValue());

        private Integer value;

        /**
         * 模块编码（通过状态找到对应的首页模块编码）
         */
        private String moduleCode;

        /**
         * 根据名称返回枚举值
         *
         * @param name 名称
         * @return 返回枚举值
         */
        public static EnumProjectStatus getByName(String name) {
            for (EnumProjectStatus c : EnumProjectStatus.values()) {
                if (c.name().equals(name)) {
                    return c;
                }
            }
            return null;
        }

        /**
         * 通过状态找到对应的首页模块信息
         *
         * @param name 状态值
         * @return 返回首页模块值
         */
        public static String getModuleCode(String name) {
            for (EnumProjectStatus c : EnumProjectStatus.values()) {
                if (c.name().equals(name)) {
                    return c.getModuleCode();
                }
            }
            return "";
        }

        /**
         * 通过状态找到对应的首页模块信息（主要处理开展中对应不同模块的场景）
         *
         * @param name 状态值
         * @return 返回首页模块值
         */
        public static List<String> getModuleCodes(String name) {
            List<String> moduleCodes = new ArrayList<>();
            if (name.equals(EnumProjectStatus.开展中.name())) {
                moduleCodes.add(EnumLIM.EnumHomeTaskModule.采样准备.getValue());
                moduleCodes.add(EnumLIM.EnumHomeTaskModule.现场委托送样.getValue());
                moduleCodes.add(EnumLIM.EnumHomeTaskModule.报告编制.getValue());
                moduleCodes.add(EnumLIM.EnumHomeTaskModule.任务办结.getValue());
            } else {
                moduleCodes.add(getModuleCode(name));
            }
            return moduleCodes;
        }
    }

    /**
     * 项目等级
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectGrade {

        所有(-1),

        一般(0),

        紧急(1),

        特急(2);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumProjectGrade c : EnumProjectGrade.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 分包状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOutSourcing {
        不分包(0),

        全部分包(1),

        部分分包(2);

        private Integer value;
    }

    /**
     * 报表名称
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumDocumnetName {

        订单("orderId"),

        项目("projectId"),

        工作单("workSheetFolderId"),

        报告报表("reportId"),

        送样单("receiveSampleRecordId");

        private String value;
    }

    /**
     * 采毕状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSampledStatus {
        未采毕(1),

        已采毕(2);

        private Integer value;
    }

    /**
     * 现场任务参数显示排序
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSampleParamsSortType {
        按照样品编号排序(1),

        按照参数排序(2),

        按照分组排序(3);

        private Integer value;
    }
    //endregion

    //region 数据

    /**
     * 数据状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumAnalyseDataStatus {
        未测(1),

        在测(2),

        已测(4),

        拒绝(8),

        已确认(16),

        复核通过(32),

        作废(64);

        private Integer value;
    }

    /**
     * 数据状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumStatisticDataStatus {
        未领(1),

        待检(2),

        检测中(4),

        复核中(8),

        已确认(16);

        private Integer value;
    }

//endregion

    //#region 样品

    /**
     * 样品类别
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSampleCategory {
        原样(0, -1, -1),

        质控样(1, -1, -1),

        串联样(2, EnumLIM.EnumQCGrade.外部质控.getValue(), 32),

        原样加原样(3, EnumLIM.EnumQCGrade.内部质控.getValue(), 16),

        比对样(4, -1, -1),

        洗涤剂(5, 1, 128),

        比对评价样(6, -1, -1);

        /*
         * 枚举值
         */
        private Integer value;

        /*
         * 对应的质控等级
         */
        private Integer qcGrade;

        /*
         * 对应的质控类型
         */
        private Integer qcType;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumSampleCategory getByValue(Integer value) {
            for (EnumSampleCategory c : EnumSampleCategory.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 盲样类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSampleBlindType {
        非盲样(0),

        实际水样(1),

        标样(2),

        留样复测(3),

        加标样(4),

        密码平行(5),

        密码加标(6);

        /*
         * 枚举值
         */
        private Integer value;
    }

    /**
     * 样品数据变更状态（针对已经编制报告的数据修改状态-样品数据增删改）
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSampleChangeStatus {
        未变更(0),

        已变更(1);

        private Integer value;
    }

    /**
     * 项目变更状态（针对已经编制报告的数据修改状态-样品数据增删改）
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectChangeStatus {
        未变更(0),

        已变更(1);

        private Integer value;
    }

    /**
     * 样品状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSampleStatus {
        样品未采样,

        样品未领样,

        样品待检,

        样品在检,

        样品检毕,

        样品作废;
    }

    /**
     * 采样分配状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSamplingConfig {
        未分配(0),

        已分配(1);

        private Integer value;
    }

    /**
     * 领样状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumInnerReceiveStatus {
        不能领取(1),

        可以领取(2),

        已经领取(6),

        已确认领取(12);

        private Integer value;
    }

    /**
     * 采样状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSamplingStatus {
        不需要取样(1),

        需要取样还未取样(2),

        采样中(4),

        已经完成取样(8);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumSamplingStatus getByValue(Integer value) {
            for (EnumSamplingStatus c : EnumSamplingStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 分析状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumAnalyzeStatus {
        不需要分析(1),

        不能分析(2),

        可以分析(4),

        正在分析(8),

        分析完成(16);

        private Integer value;
    }

    /**
     * 存储状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumStoreStatus {
        不能存储(1),

        可以存储(2),

        已经存储(4),

        可以销毁(8),

        已经销毁(16),

        已经被提取(32);

        private Integer value;
    }

    /**
     * 订单状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOrderStatus {
        登记中(1),

        审核不通过(2),

        一审中(4),

        二审中(8),

        审核通过(16);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumOrderStatus getByValue(Integer value) {
            for (EnumOrderStatus c : EnumOrderStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }

        /**
         * 根据名称返回枚举值
         *
         * @param name 名称
         * @return 返回枚举值
         */
        public static EnumOrderStatus getByName(String name) {
            for (EnumOrderStatus c : EnumOrderStatus.values()) {
                if (c.name().equals(name)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 推送状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPushStatus {

        未推送(0),

        已推送(1);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumPushStatus getByValue(Integer value) {
            for (EnumPushStatus c : EnumPushStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 订单审核流程
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOrderFlowType {

        不审核(0),

        一审(1),

        二审(2),

        三审(3);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumOrderFlowType getByValue(Integer value) {
            for (EnumOrderFlowType c : EnumOrderFlowType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 制样状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumMakeStatus {
        不需要制样(1),

        需要制样还未制样(2),

        已经完成制样(6);

        private Integer value;
    }

    /**
     * 打印状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPrintStatus {
        未打印(0),

        已打印(1);

        private Integer value;
    }

    /**
     * 样品
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSampleTestType {
        无指标(0),

        实验室指标(1),

        现场指标(2),

        全指标(3);

        private Integer value;
    }
    //#endregion

    //#region 送样单

    /**
     * 接样类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReceiveType {
        内部送样(1),

        外部送样(2),

        现场送样(3),

        委托现场送样(4);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumReceiveType getByValue(Integer value) {
            for (EnumReceiveType c : EnumReceiveType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 信息状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReceiveInfoStatus {
        新建(0),

        信息登记中(1),

        信息复核中(2),

        信息审核中(3),

        已确认(4);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumReceiveInfoStatus getByValue(Integer value) {
            for (EnumReceiveInfoStatus c : EnumReceiveInfoStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 移动端状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReceiveUploadStatus {
        未提交(0),

        数据录入中(1),

        已数据同步(2);
        private Integer value;
    }

    //#endregion

    //#region 领样单

    /**
     * 领样单类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSubRecordType {
        现场("XC"),

        分析("FX");

        private String value;
    }

    /**
     * 领样单状态名称
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReceiveSubRecordStatusName {
        未领取,
        未确认领样,
        测试中,
        待数据确认,
        已经确认,
        复核中//现场数据复核
    }

    //#endregion

    //#region 质控

    //#endregion

    /**
     * 流程类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOATaskType {
        合同审批("contract"),

        项目支出("projectExpend"),

        部门支出("departmentExpend"),

        仪器采购("instrumentPurchase"),

        仪器维修("instrumentRepair"),

        仪器报废("instrumentScrap"),

        领料("consumable"),

        消耗品采购("consumablePurchase"),

        文件受控("fileControl"),

        文件修订("fileRevision"),

        文件废止("fileAbolish"),

        加班申请("overtime"),

        调休申请("lieuHoliday"),

        培训审批("training");

        private String code;

        /**
         * 根据编码返回枚举值
         *
         * @param code 编码
         * @return 返回枚举值
         */
        public static EnumOATaskType getByCode(String code) {
            for (EnumOATaskType c : EnumOATaskType.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 报告编号状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReportCodeStatus {
        未使用(0),

        已使用(1),

        已作废(2);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumReportCodeStatus c : EnumReportCodeStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 流程审核状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOATaskStatus {
        新建(0),

        审批通过(1),

        审批中(2),

        审批拒绝(3),

        已撤销(4);

        private Integer value;
    }

    /**
     * 审核查询类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOATaskQueryType {
        待我审批(1),

        我已审批(2),

        我已发起(3),

        所有审批(4);

        private Integer value;
    }

    /**
     * 项目模块枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOrderModule {
        订单录入("orderForm", 0),

        订单一审("orderFirst", 1),

        订单二审("orderSecond", 2),

        订单三审("orderThree", 3);

        private String code;

        private Integer value;

        /**
         * 根据名称返回枚举值
         *
         * @param name 名称
         * @return 返回枚举值
         */
        public static EnumOrderModule getByName(String name) {
            for (EnumOrderModule c : EnumOrderModule.values()) {
                if (c.name().equals(name)) {
                    return c;
                }
            }
            return null;
        }

        /**
         * 根据编码返回枚举值
         *
         * @param code 编码
         * @return 返回枚举值
         */
        public static EnumOrderModule getByCode(String code) {
            for (EnumOrderModule c : EnumOrderModule.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 提交关联类型 1.项目 2.检测单 3.送样单 4.现场领样单 5.分析领样单
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSubmitObjectType {
        项目(1),

        检测单(2),

        送样单(3),

        现场领样单(4),

        分析领样单(5),

        方案(6);

        private Integer value;
    }

    /**
     * 提交类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSubmitType {
        无(0),

        委托现场提交(1),

        数据录入提交(2),

        样品交接提交(3),

        样品分配提交(4),

        质控项目提交(5),

        方案提交(6);

        private Integer value;
    }

    /**
     * 费用模块枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCostInfoModule {
        费用核算("check", 1),

        费用审核("audit", 2),

        费用审批("approve", 3);

        private String code;

        private Integer value;
    }

    /**
     * 费用模块状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCostInfoStatus {
        新建(1, EnumLIM.EnumHomeTaskModule.费用管理.getValue()),

        审核不通过(2, EnumLIM.EnumHomeTaskModule.费用管理.getValue()),

        审批不通过(3, EnumLIM.EnumHomeTaskModule.费用管理.getValue()),

        审核中(4, EnumLIM.EnumHomeTaskModule.费用审核.getValue()),

        审批中(5, EnumLIM.EnumHomeTaskModule.费用审批.getValue()),

        已完成(6, EnumLIM.EnumHomeTaskModule.费用审批.getValue());

        private Integer value;

        /**
         * 模块编码（通过状态找到对应的首页模块编码）
         */
        private String moduleCode;

        /**
         * 根据名称返回枚举值
         *
         * @param name 名称
         * @return 返回枚举值
         */
        public static EnumCostInfoStatus getByName(String name) {
            for (EnumCostInfoStatus c : EnumCostInfoStatus.values()) {
                if (c.name().equals(name)) {
                    return c;
                }
            }
            return null;
        }

        /**
         * 通过状态找到对应的首页模块信息
         *
         * @param name 状态值
         * @return 返回首页模块值
         */
        public static String getModuleCode(String name) {
            for (EnumCostInfoStatus c : EnumCostInfoStatus.values()) {
                if (c.name().equals(name)) {
                    return c.getModuleCode();
                }
            }
            return "";
        }
    }

    /**
     * 方案变更状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSchemeChangeStatus {
        未变更(0),

        已变更(1);

        private Integer value;
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSigType {
        maker(1, "maker"),
        auditor(2, "auditor"),
        signer(3, "signer"),
        certificateMaker(4, "certificateMaker"),
        分析者(5, "分析者"),
        上岗证人员(6, "上岗证人员"),
        复核者(7, "复核者"),
        审核者(8, "审核者"),
        测试人员(9, "测试人员"),
        陪同人(10, "陪同人"),
        校核人员_日期(11, "校核人员/日期"),
        审核人员_日期(12, "审核人员/日期"),
        送样者(15, "送样者"),
        接样者(14, "接样者"),
        申请人_日期(60, "申请人："),
        科室负责人_日期(61, "科室审核人："),
        分管主任_日期(62, "分管主任："),
        采购经办人_日期(63, "采购经办人："),
        办公室负责人_日期(64, "办公室负责人："),
        执法人员(65, "执法人员"),
        科室负责人意见_日期(50, "科室负责人意见"),
        分析日期(71, "分析日期"),
        校核日期(72, "校核日期"),
        复核日期(73, "复核日期"),
        采样人(74, "采样人"),
        分析人(75, "分析人"),
        采样日期(76, "采样日期"),
        校核人(77, "校核人"),
        审核人(78, "审核人"),
        检测人员(79, "检测人员"),
        监测人员(80, "监测人员"),
        检测日期(81, "检测日期"),
        监测日期(82, "监测日期"),
        登记人(83, "登记人"),
        下达人(85, "下达人"),
        送样人(86, "送样人"),
        接样人(87, "接样人"),
        接样时间(88, "接样时间"),
        上报报表编制人(90, "编制："),
        上报报表校核人(91, "校核："),
        上报报表复核人(92, "复核："),
        上报报表审核人(93, "审核："),
        上报报表签发人(94, "签发：");

        private Integer value;

        private String name;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumSigType getByValue(Integer value) {
            for (EnumSigType c : EnumSigType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSigDate {
        makersmallDate(1),
        auditorsmallDate(2),
        signersmallDate(3),
        zhiwu(4),
        qcReviewsmallDate(5);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumSigDate getByValue(Integer value) {
            for (EnumSigDate c : EnumSigDate.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSigRemark {
        审核意见(1),
        审定意见(2),
        签发意见(3);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumSigRemark getByValue(Integer value) {
            for (EnumSigRemark c : EnumSigRemark.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 通用状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumStatus {
        所有(-1),

        待处理(1),

        已处理(2);

        private Integer value;
    }

    /**
     * 报告模块状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReportState {
        报告未通过(1, ""),

        编制报告中(2, ""),

        报告审核中(4, EnumLIM.EnumHomeTaskModule.报告审核.getValue()),

        报告复核中(3, EnumLIM.EnumHomeTaskModule.报告复核.getValue()),

        报告签发中(5, EnumLIM.EnumHomeTaskModule.报告签发.getValue()),

        已签发(6, "");

        private Integer value;

        /**
         * 模块编码（通过状态找到对应的首页模块编码）
         */
        private String moduleCode;

        /**
         * 根据名称返回枚举值
         *
         * @param name 名称
         * @return 返回枚举值
         */
        public static EnumReportState getByName(String name) {
            for (EnumReportState c : EnumReportState.values()) {
                if (c.name().equals(name)) {
                    return c;
                }
            }
            return null;
        }

        /**
         * 通过状态找到对应的首页模块信息
         *
         * @param name 状态值
         * @return 返回首页模块值
         */
        public static String getModuleCode(String name) {
            for (EnumReportState c : EnumReportState.values()) {
                if (c.name().equals(name)) {
                    return c.getModuleCode();
                }
            }
            return "";
        }
    }

    /**
     * 项目报告状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReportStatus {
        未完成(0),

        已完成(1);

        private Integer value;
    }

    /**
     * 报告数据变更状态（针对已经编制报告的数据修改状态-样品数据增删改）
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReportChangeStatus {
        未变更(1),

        已变更(2);

        private Integer value;
    }


    /**
     * 报告关联类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReportDetailType {
        样品(1),

        断面属性(2);

        private Integer value;
    }

    /**
     * 样品检毕状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumMonitorType {

        未检毕(1),

        已检毕(2);

        private Integer value;
    }

    /**
     * 样品出证状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCertificateType {
        未出证(1),

        已出证(2);

        private Integer value;
    }

    /**
     * 评价类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumEvaluationType {
        项目(1),

        点位(2),

        样品(3),

        分析数据(4);

        private Integer value;
    }

    /**
     * 点位计划类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumEvaluationPlan {
        实际(1),

        计划(2),

        分析数据(3);

        private Integer value;
    }

    /**
     * 报告评价类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReportEvaluationType {
        平均值("avg"),

        最小值("min"),

        最大值("max");
        private String value;
    }

    /**
     * 评论对象类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCommentObjectType {
        项目(1),

        我的审批(2),

        检测单(3),

        报告(4);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumCommentObjectType getByValue(Integer value) {
            for (EnumCommentObjectType c : EnumCommentObjectType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 评论类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCommentType {
        留言(1),

        日志(2);

        private Integer value;
    }

    /**
     * 分析数据变更状态（针对已经编制报告的数据修改状态-样品数据增删改）
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumDataChangeStatus {
        未变更(0),

        新增(1),

        修改(2),

        删除(3);

        private Integer value;
    }


    /**
     * 项目场景条件枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectConditionType {
        登记提交("projectRegister"),

        报告完成("reportCompleted"),

        任务办结("projectEnd");

        private String value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumProjectConditionType getByValue(String value) {
            for (EnumProjectConditionType c : EnumProjectConditionType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 项目登记类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectType {
        委托类("WT"),

        送样类("SY"),

        现场类("XC"),

        质控类("QC"),

        例行类("LX"),

        全流程("QLC");

        private String value;
    }

    /**
     * 工作流编码枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumWorkflowCode {
        质控项目("PRO_QCProject"),

        质控项目无方案("PRO_QCProject_D2"),

        报告("PRO_Report"),

        费用("PRO_CostInfo"),

        订单("PRO_Order"),

        检测单("PRO_WorkSheet");

        private String value;
    }

    /**
     * 送样单提交核查枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReceiveSubmitType {
        项目登记提交("projectRegister", 2),

        委托现场提交("localSendSample", 3),

        数据录入提交("localDataInput", 30),

        样品交接提交("sampleReceive", 2),

        质控项目提交("qcRegister", 34);

        private String code;

        private Integer value;

        /**
         * 根据名称返回枚举值
         *
         * @param code 名称
         * @return 返回枚举值
         */
        public static Integer getValueByCode(String code) {
            for (EnumReceiveSubmitType c : EnumReceiveSubmitType.values()) {
                if (c.getCode().equals(code)) {
                    return c.getValue();
                }
            }
            return 0;
        }
    }

    /**
     * 送样单提交核查枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReceiveCheckType {
        核查送样人员(1),

        核查样品数据(2),

        核查环境记录(4),

        核查参数数据(8),

        核查现场数据(16),

        核查分析人员(32);

        private Integer value;
    }

    /**
     * 采样人员关联类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSamplingType {
        任务(0),

        送样单(1);

        private Integer value;
    }

    /**
     * 车辆关联类型枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSamplingCarType {
        任务(0),

        送样单(1);

        private Integer value;
    }

    /**
     * 参数数据对象类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumParamsDataType {
        样品(1),

        检测单(2),

        企业(3),

        采样单(4);

        private Integer value;
    }

    /**
     * 质量任务等级
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPorjectQCGrade {
        内部质控(1),

        外部质控(2),

        分包质控(3),

        现场质控(4),

        期间核查(5);

        private Integer value;
    }

    /**
     * 期间核查类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumInspectType {
        仪器期间核查(1),

        标样期间核查(2);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumInspectType getByValue(Integer value) {
            for (EnumInspectType c : EnumInspectType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 质量任务内部质控
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumInnerQCType {
        人员比对(1),

        方法比对(2),

        仪器比对(3),

        标样考核(4),

        盲样考核(5),

        理论考核(6),

        现场操作(7),

        实样检测(8),

        留样复测(9),

        瓶检(10),

        加标样考核(11);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumInnerQCType getByValue(Integer value) {
            for (EnumInnerQCType c : EnumInnerQCType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 质量任务外部质控
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOuterQCType {
        实验室比对(1),

        能力验证(2),

        测量审核(3),

        其他(4);

        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回枚举值
         */
        public static EnumOuterQCType getByValue(Integer value) {
            for (EnumOuterQCType c : EnumOuterQCType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 质量任务分包质控
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumSubQCType {
        社会实验室比对(1),

        社会实验室标样考核(2);

        private Integer value;
    }

    /**
     * 样品分配状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumAssignStatus {
        所有(-1),

        未分配(1),

        已分配(2);

        private Integer value;
    }


    /**
     * PRO相关的redis key定义
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumPRORedis {

        /**
         * 分析日历的redis key,定义hash 根据分析人员id 获取当前月份该分析人的分析日历情况  json 数组
         */
        PRO_OrgId_AnalyseCalendar("PRO:{0}:AnalyseCalendar"),

        /**
         * 检测计划的redis key,定义hash 根据分析人员id 获取分析人的待检测计划情况 json 数组
         */
        PRO_OrgId_AnalysePlan("PRO:{0}:AnalysePlan"),

        /**
         * 分析项目统计图的redis key,定义hash 根据分析人员id 获取分析人的分析项目统计情况 json 数组
         */
        PRO_OrgId_AnalyzeStatistics("PRO:{0}:AnalyzeStatistics"),

        /**
         * 分析状态图的redis key,定义hash 根据分析人员id 获取分析人的检测分析状态情况 json
         */
        PRO_OrgId_AnalyzeStatus("PRO:{0}:AnalyzeStatus"),

        /**
         * 审核中检测单的redis key,定义hash 根据分析人员id 获取分析人的审核中检测单详情
         */
        PRO_OrgId_AuditWorkSheetDetail("PRO:{0}:AuditWorkSheetDetail"),

        /**
         * 待检测样品的redis key,定义hash 根据分析人员id 获取分析人的待检测样品详情
         */
        PRO_OrgId_AwaitWorkSheetDetail("PRO:{0}:AwaitWorkSheetDetail"),

        /**
         * 检测中检测单的redis key,定义hash 根据分析人员id 获取分析人的检测中检测单详情
         */
        PRO_OrgId_AwaitSampleDetail("PRO:{0}:AwaitSampleDetail"),

        /**
         * 已完成检测单的redis key,定义hash 根据分析人员id 获取分析人的已完成检测单详情
         */
        PRO_OrgId_FinishWorkSheetDetail("PRO:{0}:FinishWorkSheetDetail"),

        /**
         * 项目指标的redis key,定义hash 对应项目下根据检测类型id获取下面的项目指标列表
         */
        PRO_OrgId_ProjectTest("PRO:{0}:ProjectTest"),

        /**
         * 项目方案变动的redis key,定义string 对应产生方案变动的项目id
         */
        PRO_OrgId_SchemeChangeProject("PRO:{0}:SchemeChangeProject"),

        /**
         * 分析数据缓存的策略redis key,定义string 对应目前调用该分析人数据缓存方法的并发数
         */
        PRO_OrgId_AnalyseDataTemp("PRO:{0}:AnalyseDataTemp"),

        /**
         * 分析数据缓存的策略redis key,定义string 对应目前调用该复审人数据缓存方法的并发数
         */
        PRO_OrgId_AnalyseDataCheckTemp("PRO:{0}:AnalyseDataCheckTemp"),

        /**
         * 项目json字段冗余的策略redis key,定义string 对应目前调用该项目json字段冗余方法的并发数
         */
        PRO_OrgId_ProjectSampleJsonTemp("PRO:{0}:ProjectSampleJsonTemp"),

        /**
         * 送样单json字段冗余的策略redis key,定义string 对应目前调用该送样单json字段冗余方法的并发数
         */
        PRO_OrgId_ReceiveJsonTemp("PRO:{0}:ReceiveJsonTemp"),

        /**
         * 首页委托统计的redis key，string
         */
        PRO_OrgId_CustomerStat("PRO:{0}:CustomerStat"),

        /**
         * 首页样品统计的redis key，string
         */
        PRO_OrgId_SampleStat("PRO:{0}:SampleStat"),

        /**
         * 首页检测状态统计的redis key，string
         */
        PRO_OrgId_AnalyseStatusStat("PRO:{0}:AnalyseStatusStat"),

        /**
         * 首页检测及时率的redis key，string
         */
        PRO_OrgId_AnalysePromptness("PRO:{0}:AnalysePromptness"),

        /**
         * 首页业务量趋势的redis key，string
         */
        PRO_OrgId_BusinessTrend("PRO:{0}:BusinessTrend"),

        /**
         * 首页样品数趋势的redis key，string
         */
        PRO_OrgId_SampleNumTrend("PRO:{0}:SampleNumTrend");

        private String value;

        /**
         * 将组织机构id替换成真实key
         *
         * @param value 枚举传入的key
         * @return 返回相应的key值
         */
        public static String getRedisKey(String value) {
            String orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
            return value.replace("{0}", orgId);
        }

        /**
         * 将组织机构id替换成真实key
         *
         * @param value 枚举传入的key
         * @return 返回相应的key值
         */
        public static String getRedisKey(String value, String orgId) {
            return value.replace("{0}", orgId);
        }
    }

    /**
     * 首页统计枚举
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumHomeStat {
        委托统计("customer", EnumPRORedis.PRO_OrgId_CustomerStat),
        样品统计("sample", EnumPRORedis.PRO_OrgId_SampleStat),
        检测状态统计("analyseStatus", EnumPRORedis.PRO_OrgId_AnalyseStatusStat),
        检测及时率("analysePromptness", EnumPRORedis.PRO_OrgId_AnalysePromptness),
        业务量趋势("business", EnumPRORedis.PRO_OrgId_BusinessTrend),
        样品数趋势("sampleNum", EnumPRORedis.PRO_OrgId_SampleNumTrend);

        /**
         * 编码
         */
        private String code;

        /**
         * redis key值
         */
        private EnumPRORedis value;

        /**
         * 根据编码返回对象
         *
         * @param code 编码
         * @return 返回对象
         */
        public static EnumHomeStat getValueByCode(String code) {
            for (EnumHomeStat c : EnumHomeStat.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 检测单枚举
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumWorkSheetStatus {
        新建(1),
        已经保存(2),
        检测单拒绝(6),
        确认检测单(16),
        已经提交(8),
        复核通过(24),
        审核通过(32);
        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回name
         */
        public static String getName(Integer value) {
            for (EnumWorkSheetStatus c : EnumWorkSheetStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 流程触发动作
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumProAction {

        复制项目("COPY_PROJECT"),

        方案新增点位("ADD_FOLDER"),
        方案复制点位("COPY_FOLDER"),
        方案删除点位("DELETE_FOLDER"),
        方案新增指标("ADD_ANALYZE_ITEM"),
        方案删除指标("DELETE_ANALYZE_ITEM"),
        方案设置分包("SET_SUB"),
        方案新增周期次数("ADD_PERIOD_TIME"),
        方案复制周期("COPY_PERIOD"),
        方案删除周期("DELETE_PERIOD"),
        方案复制批次("COPY_TIME_PERIOD"),
        方案删除批次("DELETE_TIME_PERIOD"),
        方案新增次数("ADD_TIME"),
        方案复制次数("COPY_TIME"),
        方案删除次数("DELETE_TIME"),
        方案修改指标("MODIFY_ANALYZE_ITEM"),
        方案修改方法("MODIFY_METHOD"),

        添加外部样品("ADD_SAMPLE"),
        复制外部样品("COPY_SAMPLE"),
        删除样品("DELETE_SAMPLE"),
        作废样品("INVALID_SAMPLE"),
        取消作废("CANCEL_INVALID"),
        添加现场质控样("ADD_LOCAL_QC"),
        添加测试项目("ADD_TEST"),
        删除测试项目("DELETE_TEST"),
        确认分包("AFFIRM_SUB"),
        取消分包("CANCEL_SUB"),

        建立样品编号("CREATE_CODE"),
        清除样品编号("CLEAR_CODE"),

        委托现场送样提交("LOCAL_SEND_SUBMIT"),
        委托现场送样单删除("LOCAL_SEND_RECORD_DELETE"),
        现场数据录入提交("LOCAL_DATA_SUBMIT"),
        现场数据复核通过("LOCAL_DATA_CHECK_PASS"),
        现场数据审核通过("LOCAL_DATA_AUDIT_PASS"),
        现场数据退回("LOCAL_DATA_BACK"),
        新建内部送样单("CREATE_INNER_RECORD"),
        删除送样单("DELETE_RECORD"),
        送样单选择样品("CHOOSE_SAMPLE"),
        送样单剔除样品("REMOVE_SAMPLE"),
        样品交接提交("SAMPLE_RECEIVE_SUBMIT"),

        样品分配("SAMPLE_ASSIGN"),
        更换人员("CHANGE_PERSON"),
        更换方法("CHANGE_METHOD"),

        创建检测单("CREATE_WORK_SHEET"),
        加入检测单("JOIN_WORK_SHEET"),
        修改检测单("UPDATE_WORK_SHEET"),
        删除检测单("DELETE_WORK_SHEET"),
        剔除检测单数据("REMOVE_SHEET_DATA"),
        添加室内质控样("ADD_INNER_QC"),
        提交检测单("SUBMIT_WORK_SHEET"),
        确认检测单("WORK_CONFIRM_SHEET"),
        检测单复核通过("WORK_SHEET_CHECK_PASS"),
        检测单审核通过("WORK_SHEET_AUDIT_PASS"),
        检测单退回("WORK_SHEET_BACK"),
        分析数据退回("ANALYSE_DATA_BACK"),

        新增报告("CREATE_REPORT"),
        删除报告("DELETE_REPORT"),
        报告签发通过("REPORT_SIGN"),
        报告签发退回("REPORT_SIGN_BACK"),
        报告关联样品("REPORT_RELATE_SAMPLE"),

        状态纠正("STATUS_CHECK");
        private String value;
    }

    /**
     * 质控任务的质控类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumQMType {
        标样(1),
        加标样(2),
        其他(3),
        空白样(4);
        private Integer value;
    }

    //#region 日志

    /**
     * 对象类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumLogObjectType {
        项目(1),//存项目Id
        方案(2),//存项目Id
        送样单(3),//存送样单Id
        现场领样单(4),//存领样单Id
        实验室领样单(5),//存领样单Id
        样品(6),//存样品Id
        数据(7),//存数据Id
        检测单(8),//存检测单Id
        领样单(9),//存领样单Id
        报告(10),//存报告Id
        费用管理(11),//费用管理id
        订单(12);//订单id
        private Integer value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回name
         */
        public static String getName(Integer value) {
            for (EnumLogObjectType c : EnumLogObjectType.values()) {
                if (c.getValue().equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回name
         */
        public static EnumLogObjectType getByValue(Integer value) {
            for (EnumLogObjectType c : EnumLogObjectType.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 日志类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumLogType {
        项目信息(1),//增删改办结
        项目流程(2),//提交、退回
        项目分包(3),//分包同步
        项目方案(4),//项目方案审核
        项目合同(5),//项目合同审核
        项目报告(6),//项目报告审核
        项目报告转交(7),//项目报告转交
        项目送样单(8),//项目送样单增删
        项目检测单(9),//项目检测单增删
        方案点位信息(10),//方案点位、频次增删改复制
        方案点位数据信息(11),//具体数据增删、分包
        新增方案(12),//新增方案
        复制方案(13),//复制方案
        清空方案(14),//清空方案
        方案检测方法信息(15),//方案检测方法批量修改
        送样单信息(20),//增删改
        送样单流程(21),//送样单流程
        送样单样品信息(22),//样品加入剔除送样单
        送样单采样使用记录(23),//送样单采样使用记录
        现场领样单信息(24),//增删改
        现场领样单数据(29),//现场领样单数据
        现场领样单流程(25),//现场领样单流程
        现场领样单使用记录(26),//现场领样单使用记录
        实验室领样单信息(27),//增删改
        实验室领样单流程(28),//实验室领样单流程
        实验室领样单分配(29),//实验室领样单人员方法调整

        领样单信息(70),//增删改
        领样单数据(71),//领样单数据
        领样单流程(72),//领样单流程

        样品增删作废(30),//增删改作废，包括质控样
        样品信息(31),//样品基本信息、参数
        样品数据(32),//相关数据增删
        样品流程(33),//相关样品流转包括生成样品编号、清空、送样、交接样、数据审核等
        样品质控(34),//样品增加质控样

        数据增删(40),//增删
        数据保存(41),//数据修改保存
        数据退回(42),//数据退回
        数据检测单(43),//数据在检测单中增加、剔除
        数据配置(44),//数据有效位数、小数位数、单位、更换人员、更换方法
        数据分包(45),//是否分包
        数据审核(46),//检测单通过审核时

        检测单增删(50),//增删
        检测单基本信息(51),//检测单基本信息
        检测单数据保存(52),//检测单中数据保存
        检测单增删样品(53),//检测单中增删质控样、原样
        检测单原始记录(54),//检测单原始记录单保存日志
        检测单使用记录(55),//检测单仪器使用记录
        检测单配置(56),//检测单修改公式、标准曲线
        检测单更新参数(57),//检测单更新参数
        检测单同步仪器(58),//检测单同步仪器
        检测单试剂配置(59),//检测单试剂配制增删改
        检测单流程(60),//检测单试剂配制增删改

        订单流程(90),

        费用流程(80);//费用管理的流程日志

        private int value;

        public static EnumLogType getByValue(int value) {
            for (EnumLogType c : EnumLogType.values()) {
                if (c.getValue() == value) {
                    return c;
                }
            }
            return null;
        }

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回name
         */
        public static String getName(Integer value) {
            for (EnumLogType c : EnumLogType.values()) {
                if (c.getValue() == value) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 操作类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumLogOperateType {

        增加项目,
        修改项目,
        删除项目,
        生成详细方案,
        复制方案,
        清空方案,
        增加点位,
        修改点位,
        删除点位,
        复制点位,
        增加频次,
        复制周期,
        删除周期,
        删除批次,
        增加次数,
        复制次数,
        删除次数,
        样品作废,
        取消作废,
        增加检测项目,
        删除检测项目,
        修改检测方法,
        修改检测人员,
        修改检测仪器,
        分包项目,
        取消分包项目,
        增加样品,
        修改样品,
        增加质控样,
        删除样品,
        复制样品,
        样品采样,
        样品送样,
        样品领取,
        样品数据确认,
        样品状态更新,
        创建送样单,
        修改送样单,
        删除送样单,
        提交送样单,
        更新送样单状态,
        创建实验室领样单,
        删除实验室领样单,
        更新实验室领样单状态,
        实验室领样单领样,
        创建现场领样单,
        删除现场领样单,
        修改现场领样单,
        更新现场领样单状态,
        提交现场领样单,
        复核现场领样单,
        退回现场领样单,

        创建领样单,
        修改领样单,
        更新领样单状态,
        提交领样单,
        复核领样单,
        退回领样单,

        领样单数据确认,
        删除领样单,
        保存数据,
        审核数据,
        退回数据,
        更新主项目状态,
        更新项目状态,
        创建检测单,
        删除检测单,
        修改检测单,
        提交检测单,
        确认检测单,
        复核检测单,
        审核检测单,
        退回检测单,
        更新检测单公式,
        更新检测单标准曲线,
        保存原始记录,
        新增试剂配置,
        修改试剂配置,
        删除试剂配置,
        新增使用记录,
        修改使用记录,
        删除使用记录,

        增加报告,
        修改报告,
        删除报告,
        转交报告,
        更新报告状态,
        退回报告,
        提交订单,
        审核订单,
        退回订单
    }

    /**
     * 项目进度模块
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumProjectInquiryModule {
        项目状态("projectStatus"),

        采样情况("samplingInfo"),

        现场任务("localTask"),

        样品分配("sampleAssign"),

        检测情况("analyzeInfo"),

        报告("report");
        private String value;
    }

    /**
     * 项目进度状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumProjectInquiryStatus {
        项目登记("register"),

        审核下达("auditIssue"),

        开展中("launch"),

        已办结("finish");
        private String value;

        /**
         * 根据值返回枚举值
         *
         * @param value 名称
         * @return 返回name
         */
        public static EnumProjectInquiryStatus getByValue(String value) {
            for (EnumProjectInquiryStatus c : EnumProjectInquiryStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }
    //#endregion

    //#region 统计
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumStatisticsTimeType {
        登记时间(0),
        采样时间(1);
        private Integer value;
    }
    //#endregion

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumProjectField {
        项目名称("projectName"),
        项目等级("grade"),
        登记日期("inceptTime"),
        登记人员("inceptPersonId"),
        项目负责人("leaderId"),
        编制报告人("reportMakerId"),
        要求完成日期("deadLine"),
        要求报告日期("reportDate"),
        要求报告数("reportNum"),
        是否着重关注("isStress"),

        委托方("customerName"),
        委托方联系人("linkMan"),
        委托方联系方式("linkPhone"),
        委托方传真("linkFax"),
        委托方邮箱("linkEmail"),
        委托方邮政编码("zipCode"),
        委托方地址("customerAddress"),
        送样方("customerName"),
        送样方联系人("linkMan"),
        送样方联系方式("linkPhone"),
        送样方传真("linkFax"),
        送样方邮箱("linkEmail"),
        送样方邮政编码("zipCode"),
        送样方地址("customerAddress"),
        受检方("inspectedEnt"),
        受检方联系人("inspectedLinkMan"),
        受检方联系方式("inspectedLinkPhone"),
        受检方地址("inspectedAddress"),

        监测目的("monitorPurp"),
        是否提醒("isWarning"),
        提前提醒天数("warningDay"),
        监测方式("monitorMethods"),
        保存条件("saveCondition"),
        交付方式("postMethod"),
        监测要求及说明("customerRequired"),
        其他说明("remark"),
        监测类型("sampleType"),
        报告出具方式("reportMethod"),
        出具报告日期("reportDate"),
        要求完成时间("deadLine"),
        样品描述("sampleDescription"),
        样品数量("sampleQuantity"),
        联系人("linkMan"),
        联系电话("linkPhone"),
        地址("customerAddress"),
        质量来源("qcSource"),
        判断依据("judgment"),
        责任人("responsePerson");

        private String value;
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumSampleField {
        样品编号("code"),
        点位名称("redFolderName"),
        备注("remark");

        private String value;
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumReceiveSampleRecordField {
        送样人员("senderName"),
        送样日期("sendTime"),
        采样日期("samplingTime"),
        备注("remark");

        private String value;
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumWorkSheetReagentField {
        试剂名称("reagentName"),
        试剂规格("reagentSpecification"),
        配置溶液("configurationSolution"),
        配置过程("reagent"),
        配置日期("configDate"),
        有效期("expiryDate");

        private String value;
    }

    /**
     * 错误码
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumErrorCode {
        项目未编方案("P0001", "项目", "项目未编制方案，无法提交"),
        项目无指标("P0002", "项目", "存在没有测试项目的样品，无法提交"),
        项目报告不足("P0003", "项目", "签发报告数小于要求报告数"),
        项目报告未签("P0004", "项目", "存在未签发的报告"),
        项目未检毕("P0005", "项目", "所选项目存在样品未检毕，办结后状态将无法还原");

        private String code;

        private String domain;

        private String description;

        /**
         * 根据编号返回枚举值
         *
         * @param code 编号
         * @return 返回枚举值
         */
        public static EnumErrorCode getByCode(String code) {
            for (EnumErrorCode c : EnumErrorCode.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 订单签订状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum EnumOrderSignStatus {

        未签订(0),
        已签订(1);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumOrderSignStatus c : EnumOrderSignStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 数据分包确认状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumOutSourceDataStatus {
        未完成(0),

        已确认(1);

        private Integer value;

        /**
         * 根据值返回枚举名称
         *
         * @param value 值
         * @return 返回枚举名称
         */
        public static String getByValue(Integer value) {
            for (EnumOutSourceDataStatus c : EnumOutSourceDataStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 样品制备状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumPreParedStatus {
        已制备(1),
        未制备(0);
        private Integer value;
    }

    /**
     * 收款状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumContractCollectionStatus {
        未收款(0),
        部分收款(1),
        已收款(2);
        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumContractCollectionStatus c : EnumContractCollectionStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 收款项
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumCollectItem {
        预付款(0),
        验收款(1);
        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumCollectItem c : EnumCollectItem.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 收款项
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumRestrictItem {
        检测费用明细("检测费用明细", "订单管理"),
        监测方案("监测方案", "项目登记"),
        登记参数必填验证("送样参数必填验证", "项目登记"),
        样品登记("样品登记", "现场送样"),
        参数必填验证("参数必填验证", "现场任务"),
        现场出证必填验证("出证必填验证", "现场任务"),
        现场仪器使用记录("仪器使用记录", "现场任务"),
        现场仪器判定冲突("仪器判定冲突", "现场任务"),
        核查送样人("核查送样人", "现场任务"),
        现场质控比例判定("质控比例判定", "现场任务"),
        现场质控比例配置("质控比例配置", "现场任务"),
        现场质控限制配置("质控限值配置", "现场任务"),
        现场质控评价("质控限值评价", "现场任务"),
        采样单("采样单", "现场任务"),
        现场电子签名("电子签名", "现场任务"),
        是否添加样品("是否添加样品", "现场任务"),
        是否空样品编号("是否空样品编号", "现场任务"),
        采样上岗证检测("上岗证检测", "现场任务"),
        送样日期验证("送样日期验证", "现场任务"),
        交接是否空编号("是否空样品编号", "样品交接"),
        采样单电子签名("电子签名", "样品交接"),
        交接单接样数据验证("接样数据", "样品交接"),
        人员检测("人员检测", "样品分配"),
        分析项目关系("分析项目关系", "实验室分析"),
        实验室质控比例判定("质控比例判定", "实验室分析"),
        实验室质控比例配置("质控比例配置", "实验室分析"),
        实验室仪器使用记录("仪器使用记录", "实验室分析"),
        原始记录单("原始记录单", "实验室分析"),
        实验室电子签名("电子签名", "实验室分析"),
        实验室出证必填验证("出证必填验证", "实验室分析"),
        超标数据("超标数据", "实验室分析"),
        实验室质控限制配置("质控限值配置", "实验室分析"),
        实验室质控评价("质控限值评价", "实验室分析"),
        上岗证检测("上岗证检测", "实验室分析"),
        实验室参数必填验证("参数必填验证", "实验室分析"),
        历史极值("历史极值验证", "实验室分析"),
        分析日期验证("分析日期验证", "实验室分析"),
        报告编号("报告编号", "编制报告"),
        报告完成("报告完成", "编制报告"),
        质控信息("质控信息", "编制报告"),
        关联样品("关联样品", "编制报告"),
        样品检毕("样品检毕", "编制报告"),
        报告电子签名("电子签名", "编制报告"),
        报告验证("报告验证", "编制报告"),
        提交状态验证("状态验证", "状态验证");

        private String checkItem;

        private String moduleName;
    }

    /**
     * 报告盖章枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectReportStamp {

        监测专用章(1),
        CMA章(2),
        CNAS章(3);

        private final Integer value;

        public static String getNameByValue(Integer value) {
            for (EnumProjectReportStamp c : EnumProjectReportStamp.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 报告监测方法
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectMonitorMethod {

        由服务方决定(1),
        由委托方决定(2);

        private final Integer value;
    }

    /**
     * 质控评价评价类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumQualityControlEvaluateType {

        系统验证(1),
        人工评价(2);

        private final Integer value;

        public static String getNameByValue(Integer value) {
            for (EnumQualityControlEvaluateType c : EnumQualityControlEvaluateType.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }

    /**
     * 报告发放状态
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumReportGrantStatus {
        未发放(1),
        已发放(2),
        已回收(3);

        private Integer value;

        public static String getByValue(Integer value) {
            for (EnumReportGrantStatus c : EnumReportGrantStatus.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }
    }


    /**
     * 保存方式
     *
     * <AUTHOR>
     * @version V1.0.0
     * @since 2024/08/27
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectSampleSaveMethod {

        符合规范(1),
        不符合规范(2),
        不确定(3);

        private final Integer value;

        public static String getByValue(Integer value) {
            for (EnumProjectSampleSaveMethod c : EnumProjectSampleSaveMethod.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }


    /**
     * 样品确认枚举
     *
     * <AUTHOR>
     * @version V1.0.0
     * @since 2024/08/27
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectSampleRight {

        正确(1),
        不正确(2),
        不确定(3);

        private final Integer value;

        public static String getByValue(Integer value) {
            for (EnumProjectSampleRight c : EnumProjectSampleRight.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 样品有效期枚举
     *
     * <AUTHOR>
     * @version V1.0.0
     * @since 2024/08/27
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumProjectSampleValidityPeriod {
        在有效期内(1),
        不在有效期内(2),
        不确定(3);

        private final Integer value;

        public static String getByValue(Integer value) {
            for (EnumProjectSampleValidityPeriod c : EnumProjectSampleValidityPeriod.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }

    /**
     * 监测间隔
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumDisassembleType {
        年度(1, "1次/年"),
        半年度(2, "1次/半年"),
        季度(3, "1次/季"),
        双月(4, ""),
        单月(5, ""),
        月度(12, "1次/月");

        /**
         * 枚举值
         */
        private final Integer value;

        /**
         * 排污许可证自行监测间隔
         */
        private final String PollutantDischargePermitType;

        public static String getByValue(Integer value) {
            for (EnumDisassembleType c : EnumDisassembleType.values()) {
                if (c.getValue().equals(value)) {
                    return c.toString();
                }
            }
            return "";
        }

        /**
         * 根据排污许可证自行监测间隔获取枚举项
         *
         * @param type 排污许可证自行监测间隔
         * @return 枚举项
         */
        public static EnumDisassembleType getByPollutantDischargePermitType(String type) {
            for (EnumDisassembleType c : EnumDisassembleType.values()) {
                if (c.getPollutantDischargePermitType().equals(type)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 监测点位统计类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumFixedPointStatType {

        环境质量("environmentQuality", "HJFixedPointStat"),
        污染源("pollutionSource", "WRFixedPointStat");

        /**
         * 枚举编码
         */
        private final String code;

        /**
         * 策略类名
         */
        private final String beanName;

        /**
         * 根据编码获取枚举项
         *
         * @param code 枚举编码
         * @return 枚举项
         */
        public static EnumFixedPointStatType getByCode(String code) {
            for (EnumFixedPointStatType c : EnumFixedPointStatType.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 文件申请类型
     *
     * <AUTHOR>
     * @version V1.0.0
     * @since 2025/05/19
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumFileAuditApplyType {
        新增(1),
        修订(2),
        作废(3);

        private final Integer value;

        public static String getNameByValue(Integer value) {
            for (EnumFileAuditApplyType c : EnumFileAuditApplyType.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

        public static EnumFileAuditApplyType getByValue(Integer value) {
            for (EnumFileAuditApplyType c : EnumFileAuditApplyType.values()) {
                if (c.value.equals(value)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 文件申请步骤状态
     *
     * <AUTHOR>
     * @version V1.0.0
     * @since 2025/05/19
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumFileAuditStep {
        文件申请(1,"apply",-1),
        审核会签(2,"batchAudit",2),
        负责人审核(3,"leaderAudit",3),
        文件批准(4,"approve",4),
        接收会签(5,"batchReceive",5),
        备案(5,"register",6);

        private final Integer step;

        private final String code;

        private final Integer standardStatus;

        public static String getNameByValue(Integer value) {
            for (EnumFileAuditStep c : EnumFileAuditStep.values()) {
                if (c.step.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }

        public static EnumFileAuditStep getByCode(String stepCode) {
            for (EnumFileAuditStep c : EnumFileAuditStep.values()) {
                if (c.code.equals(stepCode)) {
                    return c;
                }
            }
            return null;
        }
    }

    /**
     * 文件申请状态
     *
     * <AUTHOR>
     * @version V1.0.0
     * @since 2025/05/19
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum EnumFileAuditStatus {
        新建(-1),
        退回(0),
        文件申请(1),
        审核会签(2),
        负责人审核(3),
        文件批准(4),
        接收会签(5),
        备案(6),
        审批通过(7);

        private final Integer value;

        public static String getNameByValue(Integer value) {
            for (EnumFileAuditStatus c : EnumFileAuditStatus.values()) {
                if (c.value.equals(value)) {
                    return c.name();
                }
            }
            return "";
        }
    }
}