package com.sinoyd.lims.od.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * ask查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2025/3/4
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskCriteria extends BaseCriteria {


    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 类型
     */
    private Integer taskType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 配气员Id
     */
    private String gasMixerId;

    /**
     * 嗅辨员Id
     */
    private String personId;


    @Override
    public String getCondition() {
        values.clear(); // 清除条件数据
        StringBuilder condition = new StringBuilder();

        Calendar calendar = new GregorianCalendar();
        //开始时间查询
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date date = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and a.odDate >= :startTime");
            values.put("startTime", date);
        }
        //结束时间查询
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date date = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
            condition.append(" and a.odDate < :endTime");
            values.put("endTime", date);
        }
        if (StringUtil.isNotEmpty(taskName)) {
            condition.append(" and a.taskName like :taskName");
            values.put("taskName", "%" + taskName + "%");
        }
        if (StringUtil.isNotNull(taskType)) {
            condition.append(" and a.taskType = :taskType");
            values.put("taskType", taskType);
        }

        if (StringUtil.isNotEmpty(gasMixerId)) {
            condition.append(" and a.gasMixerId = :gasMixerId");
            values.put("gasMixerId", gasMixerId);
        }
        if (StringUtil.isNotEmpty(personId)) {
            condition.append(" and exists (select 1 from DtoOdPerson p where a.id = p.taskId and p.personId = :personId)");
            values.put("personId", personId);
        }
        return condition.toString();
    }
}
