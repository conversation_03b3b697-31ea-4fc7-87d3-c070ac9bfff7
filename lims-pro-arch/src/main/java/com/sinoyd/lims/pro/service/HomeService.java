package com.sinoyd.lims.pro.service;

import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.lim.dto.customer.DtoSchemaVersionTemp;
import com.sinoyd.lims.lim.dto.lims.DtoFastNavigationTemplate;
import com.sinoyd.lims.pro.dto.DtoHomePendingNo;
import com.sinoyd.lims.pro.dto.DtoOATask;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * 首页的操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/03/10
 * @since V100R001
 */
public interface HomeService {

    /**
     * 缓存当前人员的项目任务
     *
     * @param userId     用户id
     * @param orgId      组织机构id
     * @param moduleCode 模块编码
     */
    Future<Integer> cacheProjectTask(String userId, String orgId, String moduleCode) throws ExecutionException, InterruptedException;


    /**
     * 清除当然人员的缓存数据
     *
     * @param userId         用户id
     * @param orgId          组织机构id
     * @param moduleCode     模块编码
     * @param nextModuleCode 下一步的模块编码
     */
    void clearProjectTask(String userId, String orgId, String moduleCode, String nextModuleCode);

    /**
     * 批量清除当前人员的缓存数据
     *
     * @param userId          用户id
     * @param orgId           组织机构id
     * @param moduleCode      模块编码
     * @param nextModuleCodes 下一步的模块编码集合
     */
    void clearProjectTask(String userId, String orgId, String moduleCode, List<String> nextModuleCodes);

    /**
     * 进行实验室分析的任务数量缓存
     *
     * @param userId  用户id
     * @param orgId   组织机构id
     * @param module  模块
     * @param taskNum 任务数
     */
    void cacheAnalysTask(String userId, String orgId, String module, Integer taskNum);

    /**
     * 返回重点项目
     *
     * @param pageBean 分析条件
     */
    void findCacheKeyProjects(PageBean<Map<String, Object>> pageBean);


    /**
     * 返回公告信息
     *
     * @param pageBean 分析条件
     */
    void findCacheNotices(PageBean<Map<String, Object>> pageBean);

    /**
     * 清除公告的缓存
     *
     * @param userId 用户ID
     * @param orgId  组织机构ID
     */
    void clearCacheNotices(String userId, String orgId);

    /**
     * 缓存我的审批任务
     *
     * @param pageBean   分页条件
     * @param moduleCode 模块名称
     */
    void findOATaskCache(PageBean<DtoOATask> pageBean, String moduleCode);


    /**
     * 清除我的审批的缓存
     *
     * @param userId     用户ID
     * @param orgId      组织机构ID
     * @param moduleCode 模块编码
     */
    void clearOATaskCache(String userId, String orgId, String moduleCode);


    /**
     * 快速导航
     *
     * @param pageBean 分页条件
     */
    void findFastNavigationCache(PageBean<DtoFastNavigationTemplate> pageBean);

    /**
     * 当前月份的样品数
     *
     * @param year  年份
     * @param month 月份
     * @return 每天对应的样品个数
     */
    Map<String, Integer> findSampleCountByMonth(int year, int month);


    /**
     * 清除快速导航
     *
     * @param userId 用户ID
     * @param orgId  组织机构ID
     */
    void clearFastNavigationCache(String userId, String orgId);

    /**
     * @param code 统计编码
     */
    List findStatCache(String code);


    /**
     * 缓存消息总数
     *
     * @param dtBegin 开始时间
     * @param dtEnd   结束时间
     * @return 返回总数
     */
    Integer findCacheMessageTotal(String dtBegin, String dtEnd);

    /**
     * 首页待办数量
     *
     * @param orgId  组织机构id
     * @param userId 用户id
     * @return 首页待办
     */
    List<DtoHomePendingNo> findHomePendingNo(String orgId, String userId);

    /**
     * 查询flyway脚本
     *
     * @param pageBean          分页条件
     * @param schemaVersionTemp 查询条件
     */
    void findFlyway(PageBean<DtoSchemaVersionTemp> pageBean, DtoSchemaVersionTemp schemaVersionTemp);
}
