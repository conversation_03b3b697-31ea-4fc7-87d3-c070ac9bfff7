package com.sinoyd.lims.lim.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.lims.lim.criteria.InstrumentGatherCriteria;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGather;
import com.sinoyd.lims.lim.dto.lims.DtoInstrumentGather;
import com.sinoyd.lims.lim.service.InstrumentGatherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 仪器接入接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2024/10/17
 * @since V100R001
 */
@Api(tags = "示例: 仪器接入接口定义")
@RestController
@RequestMapping("api/lim/instrumentGather")
@Validated
public class InstrumentGatherController extends BaseJpaController<DtoInstrumentGather, String, InstrumentGatherService> {

    /**
     * 分页动态条件查询InstrumentGather
     *
     * @param InstrumentGatherCriteria 条件
     * @return RestResponse<List < InstrumentGather>>
     */
    @ApiOperation(value = "分页动态条件查询InstrumentGather", notes = "分页动态条件查询InstrumentGather")
    @GetMapping
    public RestResponse<List<DtoInstrumentGather>> findByPage(InstrumentGatherCriteria InstrumentGatherCriteria) {
        PageBean<DtoInstrumentGather> pageBean = super.getPageBean();
        RestResponse<List<DtoInstrumentGather>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, InstrumentGatherCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 仪器在线状态
     *
     * @return 新增的实体
     */
    @ApiOperation(value = "仪器在线状态", notes = "仪器在线状态")
    @GetMapping("/onlineStatus")
    public RestResponse<Map<String, Object>> onlineStatus() {
        RestResponse<Map<String, Object>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.onlineStatus());
        return restResponse;
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询", notes = "根据id查询")
    @GetMapping("/{id}")
    public RestResponse<DtoInstrumentGather> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoInstrumentGather> restResp = new RestResponse<>();
        DtoInstrumentGather entity = service.findOne(id);
        restResp.setData(entity);
        restResp.setRestStatus(StringUtil.isNull(entity) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);

        return restResp;
    }


    /**
     * 新增
     *
     * @param InstrumentGather 实体
     * @return 新增的实体
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("")
    public RestResponse<DtoInstrumentGather> create(@Validated @RequestBody DtoInstrumentGather InstrumentGather) {
        RestResponse<DtoInstrumentGather> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoInstrumentGather data = service.save(InstrumentGather);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 更新
     *
     * @param InstrumentGather 实体
     * @return 更新后的实体
     */
    @ApiOperation(value = "更新", notes = "更新")
    @PutMapping("")
    public RestResponse<DtoInstrumentGather> update(@Validated @RequestBody DtoInstrumentGather InstrumentGather) {
        RestResponse<DtoInstrumentGather> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        DtoInstrumentGather data = service.update(InstrumentGather);
        restResponse.setData(data);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 单个假删
     *
     * @param id id
     * @return 是否删除成功
     */
    @ApiOperation(value = "根据id删除", notes = "根据id批量删除")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        service.logicDeleteById(id);
        restResponse.setCount(1);

        return restResponse;
    }

    /**
     * 批量假删
     *
     * @param ids ids
     * @return 删除的记录数
     */
    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @DeleteMapping("")
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        Integer count = service.logicDeleteById(ids);
        restResponse.setCount(count);

        return restResponse;
    }


}
