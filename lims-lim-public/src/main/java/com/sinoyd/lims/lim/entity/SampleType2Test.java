package com.sinoyd.lims.lim.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;


/**
 * SampleType2Test实体
 * <AUTHOR>
 * @version V1.0.0 2019/11/6
 * @since V100R001
 */
 @MappedSuperclass
 @ApiModel(description="SampleType2Test")
 @Data
 public  class SampleType2Test implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

 

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();
    
    /**
    * 样品类型id
    */
    @Column(length=25,nullable=false)
    @ApiModelProperty("")
	private String sampleTypeId;
    
    /**
    * 测试项目id
    */
    @Column(length=25,nullable=false)
    @ApiModelProperty("")
    private String testId;

    /**
     * 总称测试项目id
     */
    @Column(length=25,nullable=false)
    @ApiModelProperty("总称测试项目id")
    private String parentId;

    /**
     * 次数
     */
    @Column(nullable=false)
    @ApiModelProperty("次数")
    private Integer timesOrder;

    /**
     * 样品数
     */
    @Column(nullable=false)
    @ApiModelProperty("样品数")
    private Integer samplePeriod;
 }