package com.sinoyd.lims.monitor.repository.lims;

import com.sinoyd.lims.monitor.dto.lims.DtoProperty2Point;
import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;

import java.util.List;


/**
 * Property2Point数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2021/4/12
 * @since V100R001
 */
public interface Property2PointRepository extends IBaseJpaPhysicalDeleteRepository<DtoProperty2Point, String> {


    /**
     * 通过计划id 获取计划与点位的集合
     * @param propertyIds 计划ids
     * @return 计划与点位的集合
     */
    List<DtoProperty2Point> findByPropertyIdIn(List<String> propertyIds);

    /**
     * 通过点位和计划获取计划与点位的集合
     * @param propertyIds 计划ids
     * @param pointIds 点位ids
     * @return 计划与点位的集合
     */
    List<DtoProperty2Point> findByPropertyIdInAndFixedPointIdIn(List<String> propertyIds,List<String> pointIds);

    /**
     * 根据监测计划id查询
     *
     * @param propertyId 监测计划id
     * @return 实体列表
     */
    List<DtoProperty2Point> findByPropertyId(String propertyId);

    /**
     * 根据点位id列表查询
     *
     * @param pointIds 点位id列表
     * @return 实体列表
     */
    List<DtoProperty2Point> findByFixedPointIdIn(List<String> pointIds);

    /**
     * 根据点位id查询
     *
     * @param pointId 点位id
     * @return 实体列表
     */
    List<DtoProperty2Point> findByFixedPointId(String pointId);
}