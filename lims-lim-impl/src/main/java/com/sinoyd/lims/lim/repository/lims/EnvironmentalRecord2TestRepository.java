package com.sinoyd.lims.lim.repository.lims;

import com.sinoyd.frame.repository.IBaseJpaPhysicalDeleteRepository;
import com.sinoyd.lims.lim.dto.lims.DtoEnvironmentalRecord2Test;

import java.util.List;


/**
 * EnvironmentalRecord2TestRepository数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2024/03/05
 * @since V100R001
 */
public interface EnvironmentalRecord2TestRepository extends IBaseJpaPhysicalDeleteRepository<DtoEnvironmentalRecord2Test, String> {

    /**
     * 根据EnvironmentalRecordId查询
     * @param originIds EnvironmentalRecordId
     * @return 集合
     */
    List<DtoEnvironmentalRecord2Test> findAllByEnvironmentalRecordIdIn(List<String> originIds);
}