package com.sinoyd.lims.pro.criteria;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;


/**
 * FileAudit查询条件
 *
 * <AUTHOR>
 * @version V1.0.0 2025/05/19
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileAuditCriteria extends BaseCriteria implements Serializable {

    /**
     * 发起时间开始日期
     */
    private String startTime;

    /**
     * 发起时间结束日期
     */
    private String endTime;

    /**
     * 发起人
     */
    private String creator;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 是否处理
     */
    private Boolean isHandle;

    /**
     * 必传 步骤编码
     */
    private String stepCode;

    /**
     * 当前步骤操作人
     */
    private String currentPersonId;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        condition.append(" and p.id = s.fileAuditId ");
        if (StringUtil.isNotEmpty(this.stepCode)) {
            condition.append(" and s.stepCode = :stepCode");
            values.put("stepCode", stepCode);
        }
        if (StringUtil.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and p.createDate >= :startTime");
            values.put("startTime", from);
        }
        if (StringUtil.isNotEmpty(this.endTime)) {
            Date to = DateUtil.stringToDate(this.endTime, DateUtil.YEAR);
            Calendar c = Calendar.getInstance();
            c.setTime(to);
            c.add(Calendar.DAY_OF_MONTH, 1);
            to = c.getTime();
            condition.append(" and p.createDate < :endTime");
            values.put("endTime", to);
        }
        if(StringUtil.isNotEmpty(creator)){
            condition.append(" and p.creator = :creator");
            values.put("creator", creator);
        }
        if(StringUtil.isNotEmpty(fileName)){
            condition.append(" and p.fileName like :fileName");
            values.put("fileName", "%"+fileName+"%");
        }
        if(isHandle!=null){
            condition.append(" and s.isHandle = :isHandle");
            values.put("isHandle", isHandle);
        }
        if(StringUtil.isNotEmpty(currentPersonId)){
            condition.append(" and s.currentPersonId = :currentPersonId");
            values.put("currentPersonId", currentPersonId);
        }
        return condition.toString();
    }
}