package com.sinoyd.lims.lim.vo;

import com.sinoyd.frame.base.util.DateUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 自然日VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/1/19
 */
@Data
@Accessors(chain = true)
public class CalendarDayVO {

    /**
     * 自然日日期
     */
    private Date day;

    /**
     * 类型（0：工作日，1：休息日）
     */
    private Integer type;

    /**
     * 星期几，星期一: 1， 星期二: 2 ... 星期日: 7
     */
    private Integer weekDay;

    public String toString(){
        return "当前日期是: " + DateUtil.dateToString(this.day, DateUtil.YEAR) + "; 是" + (this.type == 0 ? "工作日" : "休日日");
    }
}