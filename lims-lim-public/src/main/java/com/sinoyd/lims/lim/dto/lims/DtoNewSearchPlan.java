package com.sinoyd.lims.lim.dto.lims;

import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.lim.entity.NewSearchPlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 查新计划
 *
 * <AUTHOR>
 * @version V1.0.0 2024/11/2
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_LIM_NewSearchPlan")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoNewSearchPlan extends NewSearchPlan {

    /**
     * 执行人名称
     */
    @Transient
    private String executorName;

}