package com.sinoyd.lims.pro.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.util.UUIDHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

@MappedSuperclass
@ApiModel(description = "Project2Customer")
@Data
public class Project2Customer implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("项目id")
    private String projectId;

    @Column(length = 50, nullable = false)
    @ColumnDefault("'00000000-0000-0000-0000-000000000000'")
    @ApiModelProperty("企业id")
    private String customerId;

    @Column(length = 100)
    @ApiModelProperty("企业名称")
    private String customerName;


}
