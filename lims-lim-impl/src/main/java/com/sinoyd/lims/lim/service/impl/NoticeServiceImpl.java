package com.sinoyd.lims.lim.service.impl;

import com.jsoniter.output.JsonStream;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.base.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.dto.DtoUser;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.service.UserService;
import com.sinoyd.frame.service.impl.BaseJpaPhysicalDeleteServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.lims.DtoNotice;
import com.sinoyd.lims.lim.dto.lims.DtoNoticeMsg;
import com.sinoyd.lims.lim.entity.Notice;
import com.sinoyd.lims.lim.enums.EnumLIM;
import com.sinoyd.lims.lim.repository.lims.NoticeMsgRepository;
import com.sinoyd.lims.lim.repository.lims.NoticeRepository;
import com.sinoyd.lims.lim.service.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公告管理
 * <AUTHOR>
 * @version V1.0.0 2019/3/19
 * @since V100R001
 */
@Service
public class NoticeServiceImpl extends BaseJpaPhysicalDeleteServiceImpl<DtoNotice, String, NoticeRepository> implements NoticeService {

    @Autowired
    private UserService userService;

    @Autowired
    private NoticeMsgRepository noticeMsgRepository;

    @Autowired
    private CodeService codeService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 取消置顶
     */
    @Transactional
    @Override
    public DtoNotice cancelTop(String id) {
        repository.cancelTop(id);
        return super.findOne(id);
    }

    /**
     * 创建公告
     */
    @Transactional
    @Override
    public DtoNotice save(DtoNotice notice) {

        if (StringUtil.isNotEmpty(notice.getLabelArray())) {
            notice.setLabel(String.join(",",
                    notice.getLabelArray().stream().filter(p -> !p.equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList())));
//            String lable = "";
//            for (String lab : notice.getLabelArray()) {
//                if (!lab.equals(BaseCodeHelper.GUID_EMPTY.toString())) {
//                    lable += lab + ",";
//                }
//            }
//            notice.setLabel(lable.substring(0, lable.length() - 1));
        } else {
            notice.setLabel("");
        }
        if (StringUtils.isNotNullAndEmpty(notice.getReleaseId()) && !UUIDHelper.GUID_EMPTY.equals(notice.getReleaseId())) {
            DtoUser user = userService.findByUserId(notice.getReleaseId());
            if (StringUtil.isNotNull(user)) {
                notice.setReleaseMan(user.getUserName());
            }
        }
        notice.setReleaseTime(new Date());
        if (notice.getIsTop()) {
            notice.setOrderNum(100);
        } else {
            notice.setOrderNum(0);
        }
        notice.setClickNumber(0);
        DtoNotice item = super.save(notice);
        //利用通知的方式，要清除公告缓存
        Map<String, Object> map = new HashMap<>();
        map.put("userId", PrincipalContextUser.getPrincipal().getUserId());
        map.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        redisTemplate.convertAndSend(EnumLIM.EnumLIMRedisChannel.LIM_Notice_Cache.name(), JsonStream.serialize(map));
        return item;
    }

    /**
     * 公告置顶
     */
    @Transactional
    @Override
    public DtoNotice makeTop(String id) {
        repository.makeTop(id);
        return super.findOne(id);
    }

    /**
     * 更新公告
     */
    @Transactional
    @Override
    public DtoNotice update(DtoNotice notice) {
        if (StringUtil.isNotEmpty(notice.getLabelArray())) {
            notice.setLabel(String.join(",",
                    notice.getLabelArray().stream().filter(p -> !p.equals(UUIDHelper.GUID_EMPTY)).collect(Collectors.toList())));
//            String lable = "";
//            for (String lab : notice.getLabelArray()) {
//                if (!lab.equals(BaseCodeHelper.GUID_EMPTY.toString())) {
//                    lable += lab + ",";
//                }
//            }
//            notice.setLabel(lable.substring(0, lable.length() - 1));
        } else {
            notice.setLabel("");
        }
        if (StringUtils.isNotNullAndEmpty(notice.getReleaseId()) && !UUIDHelper.GUID_EMPTY.equals(notice.getReleaseId())) {
            DtoUser user = userService.findByUserId(notice.getReleaseId());
            if (StringUtil.isNotNull(user)) {
                notice.setReleaseMan(user.getUserName());
            }
        }
        if (notice.getIsTop()) {
            notice.setOrderNum(100);
        } else {
            notice.setOrderNum(0);
        }
        DtoNotice item = super.update(notice);
        //利用通知的方式，要清除公告缓存
        Map<String, Object> map = new HashMap<>();
        map.put("userId", PrincipalContextUser.getPrincipal().getUserId());
        map.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        redisTemplate.convertAndSend(EnumLIM.EnumLIMRedisChannel.LIM_Notice_Cache.name(), JsonStream.serialize(map));
        return item;
    }


    /**
     * 分页获取
     */
    @Override
    public void findByPage(PageBean<DtoNotice> pageBean, BaseCriteria noticeCriteria) {
        pageBean.setEntityName("DtoNotice p");
        pageBean.setSelect("select p");
        super.findByPage(pageBean, noticeCriteria);
        List<DtoNotice> list = pageBean.getData();

        if (StringUtil.isNotEmpty(list)) {
            List<String> noticeIds = list.stream().map(Notice::getId).distinct().collect(Collectors.toList());

            List<DtoNoticeMsg> msgs = noticeMsgRepository.getByNoticeIds(noticeIds);
            for (DtoNotice notice : list) {
                if (StringUtils.isNotNullAndEmpty(notice.getLabel())) {
                    String str[] = notice.getLabel().split(",");
                    notice.setLabelArray(Arrays.asList(str));
                }

                Integer count = (int) msgs.stream().filter(p -> notice.getId().equals(p.getNoticeId())).count();
                notice.setMsgCount(count);
            }
        }
        pageBean.setData(list);// 重新封装到pageBean返回到上层
    }

    @Override
    public DtoNotice getNoticePath(String id) {
        DtoNotice dtoNotice = super.findOne(id);
        String categoryName = "";
        if (StringUtil.isNotNull(dtoNotice)) {
            DtoCode dtoCode = codeService.findByCode(dtoNotice.getCategory());
            dtoNotice.setReleaseDateStr(DateUtil.dateToString(dtoNotice.getReleaseTime(), DateUtil.YEAR));
            if (StringUtil.isNotNull(dtoCode)) {
                categoryName = dtoCode.getDictName();
            }
            dtoNotice.setCategoryName(categoryName);
        }
        return dtoNotice;
    }

    @Override
    public Integer logicDeleteById(Collection<?> ids) {
        //利用通知的方式，要清除公告缓存
        Map<String, Object> map = new HashMap<>();
        map.put("userId", PrincipalContextUser.getPrincipal().getUserId());
        map.put("orgId", PrincipalContextUser.getPrincipal().getOrgId());
        redisTemplate.convertAndSend(EnumLIM.EnumLIMRedisChannel.LIM_Notice_Cache.name(), JsonStream.serialize(map));
        return super.logicDeleteById(ids);
    }
}