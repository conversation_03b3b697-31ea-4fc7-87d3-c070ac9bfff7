package com.sinoyd.lims.lim.service.impl;

import com.sinoyd.base.dto.rcc.DtoDimension;
import com.sinoyd.base.service.DimensionService;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.lim.dto.rcc.DtoParams;
import com.sinoyd.lims.lim.repository.rcc.ParamsRepository;
import com.sinoyd.lims.lim.service.ParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 参数管理接口实现
 * <AUTHOR>
 * @version V1.0.0 2019/5/14
 * @since V100R001
 */
@Service
public class ParamsServiceImpl extends BaseJpaServiceImpl<DtoParams, String, ParamsRepository> implements ParamsService {

    @Autowired
    @Lazy
    private DimensionService dimensionService;

    /**
     * 分页获取参数信息
     *
     * @param pageBean       封装分页类
     * @param paramsCriteria 查询条件
     */
    @Override
    //@SuppressWarnings("rawtypes")//对泛型的引用应当进行参数化,可以使用此注解抑制编译器产生警告信息
    public void findByPage(PageBean<DtoParams> pageBean, BaseCriteria paramsCriteria) {
        pageBean.setEntityName("DtoParams p");
        pageBean.setSelect("select p");
        super.findByPage(pageBean, paramsCriteria);

        List<DtoParams> list = pageBean.getData();
//        //先取id
//        List<String> dimensionIds = list.stream()
//                .filter(p -> p.getDimensionId() != null)
//                .map(p -> p.getDimensionId()).collect(Collectors.toList());
//
//        List<DtoDimension> dimensionList = new ArrayList<>();
//        if (dimensionIds.size() > 0) {
//            dimensionList = dimensionService.findAll();
//        }
//
//        //对应赋值(判断)
//        // if(dimensionList != null && !dimensionList.isEmpty()){
//        if (list != null && !list.isEmpty()) {
//            if (dimensionList != null && !dimensionList.isEmpty()) {
//                for (DtoParams param : list) {
//                    String dimensionName = dimensionList.stream().filter(p -> p.getId().equals(param.getDimensionId()))
//                            .map(p -> p.getDimensionName()).findFirst().orElseGet(() -> "");
//                    param.setDimension(dimensionName);
//                }
//            }
//        }
        pageBean.setData(list);// 重新封装到pageBean返回到上层
    }

    /**
     * 新增参数信息
     *
     * @param params
     * @return
     */
    @Override
    @Transactional
    public DtoParams save(DtoParams params) {
        if (StringUtil.isEmpty(params.getParamName())) {
            throw new BaseException("参数名称不能为空！");
        }
        // 判断参数名称是否重复
        else if (repository.getCountByName(UUIDHelper.NewID(), params.getParamName()) > 0) {
            throw new BaseException("已存在相同名称的参数！");
        } else {
            DtoDimension dimension = null;
            String did = UUIDHelper.GUID_EMPTY;
            if (StringUtil.isNotEmpty(params.getDimension())) {
//                    throw new BaseException("量纲名称不能为空！");
                dimension = dimensionService.findByDimensionName(params.getDimension());

                if ( StringUtil.isNull(dimension)) {
                    dimension = new DtoDimension();
                    dimension.setDimensionName(params.getDimension());
                    dimension.setIsDeleted(false);
                    dimension.setBaseValue(new BigDecimal(0));
                    dimension.setDimensionTypeId(UUIDHelper.GUID_EMPTY);
                    did = dimensionService.save(dimension).getId();
                } else {
                    did = dimension.getId();
                }
            }
            params.setDimensionId(did);
        }
        if(StringUtil.isNull(params.getParamCode())) {
            params.setParamCode("");
        }
        return super.save(params);
    }

    /**
     * 更新参数信息
     *
     * @param params
     * @return
     */
    @Transactional
    @Override
    public DtoParams update(DtoParams params) {
        if ( StringUtil.isEmpty(params.getParamName())) {
            throw new BaseException("参数名称不能为空！");
        }
        // 判断参数名称是否重复
        else if (repository.getCountByName(params.getId(), params.getParamName()) > 0) {
            throw new BaseException("已存在相同名称的参数！");
        } else {
            DtoDimension dimension = null;
            if ( StringUtil.isNotEmpty(params.getDimension())) {
//                    throw new BaseException("量纲名称不能为空！");
                dimension = dimensionService.findByDimensionName(params.getDimension());
                String did ="";
                if ( StringUtil.isNull(dimension)) {
                    dimension = new DtoDimension();
                    dimension.setDimensionName(params.getDimension());
                    dimension.setIsDeleted(false);
                    dimension.setBaseValue(new BigDecimal(0));
                    dimension.setDimensionTypeId(UUIDHelper.GUID_EMPTY);
                    did = dimensionService.save(dimension).getId();
                } else {
                    did = dimension.getId();
                }
                params.setDimensionId(did);
            }
            else
            {
                params.setDimensionId(UUIDHelper.GUID_EMPTY);
            }
        }
        return super.update(params);
    }
}
